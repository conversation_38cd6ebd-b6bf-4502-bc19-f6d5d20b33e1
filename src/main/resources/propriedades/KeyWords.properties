#FuncionalidadesSistemaEnum

es_ES_CAIXA_EM_ABERTO = Recibir, recibido, cuotas, cheque, tarjeta, caja, cuotas sin pago, Atrasos, Financieras
pt_BR_CAIXA_EM_ABERTO = Receber, recebimento, parcela,cheque,cartï¿½o,caixa,parcela em aberto,Pendencias,Financeiras

pt_BR_VENDA_AVULSA = Avaliaï¿½ï¿½o fï¿½sica, bioimpedï¿½ncia, camiseta, agua, squeeze, venda, blusa
es_ES_VENDA_AVULSA = Evaluacion Fï¿½sica, bioimpedancia, camiseta, agua, squeeze, venta,blusa

pt_BR_DIARIA = <PERSON><PERSON>¿½ria, aula, avulsa
es_ES_DIARIA = Diaria, aula, general

pt_BR_FREE_PASS = Passe livre, aula gratis, aula experimental, FreePass
es_ES_FREE_PASS = Passe libre, aula gratis, aula experimental, FreePass

pt_BR_ORCAMENTO = Realizar orcamento, consultar orcamento, vendas
es_ES_ORCAMENTO = Realizar presupuesto, consultar presupuesto, ventas

pt_BR_CONSULTA_DE_TURMAS = Vagas, turma, disponibilidade de turma, limite de vagas
es_ES_CONSULTA_DE_TURMAS = Vacante, grupo, disponibilidad de grupo, limite de vacantes

pt_BR_MAPA_TURMAS = MT, JORNAL
es_ES_MAPA_TURMAS = MT, PERIODICO

pt_BR_GESTAO_PERSONAL = Taxa, Personal, plano personal,gestï¿½o de personal
es_ES_GESTAO_PERSONAL = Tasa, Personal, plan personal, gestion de personal

pt_BR_RELATORIO_DE_PERSONAL = Gestï¿½o de personal, personal, relatorio
es_ES_RELATORIO_DE_PERSONAL = Gestion de personal, personal, informe

pt_BR_GESTAO_DE_TRANSACOES = GT,gestao,transacoes,Transaï¿½ï¿½es,Gestï¿½o
es_ES_GESTAO_DE_TRANSACOES = GT,gesion,transacciones,Transacciones,Gestion

pt_BR_GESTAO_DE_REMESSAS = Getnet, dcc, recorrï¿½ncia, tivit, cartï¿½o de credito,gestao, remessas,Gestï¿½o,Remessas
es_ES_GESTAO_DE_REMESSAS = Getnet, dcc, recurrencia, tivit, tarjeta de credito,gestion,remesas,Gestion,Remesas

pt_BR_GESTAO_DE_COMISSAO = Comissï¿½o, relatï¿½rio de comissï¿½o, comissï¿½o para professores
es_ES_GESTAO_DE_COMISSAO = Comision, informe de comision, comision para profesores

pt_BR_COMISSAO_VARIAVEL = Comissï¿½o, Comissï¿½o para consultores, meta
es_ES_COMISSAO_VARIAVEL = Comision, Comision para consultores, meta

pt_BR_GESTAO_DE_ARMARIOS = Chaves, devoluï¿½ï¿½o
es_ES_GESTAO_DE_ARMARIOS = Llaves, devolucion

pt_BR_CADASTROS_AUXILIARES = Cadastros
es_ES_CADASTROS_AUXILIARES = Registros

pt_BR_CADASTROS_PRODUTOS_TURMAS_PLANOS = Cadastros
es_ES_CADASTROS_PRODUTOS_TURMAS_PLANOS = Registros

pt_BR_CADASTROS_CONFIG_FINANCEIRA = Cadastros
es_ES_CADASTROS_CONFIG_FINANCEIRA = Registros

pt_BR_CADASTROS_ACESSO_SISTEMA = Cadastros
es_ES_CADASTROS_ACESSO_SISTEMA = Registros

pt_BR_CADASTROS_CONFIG_CONTRATO = Cadastros
es_ES_CADASTROS_CONFIG_CONTRATO = Registros

pt_BR_COLABORADOR = Funcionï¿½rio, permuta, terceirizado, consultor, usuï¿½rio, colaborador
es_ES_COLABORADOR = Empleado, trueque, externalizado, consultor, usuï¿½rio, colaborador

pt_BR_CLIENTE = Aluno, pessoa, cadastrar cliente, cadastrar aluno
es_ES_CLIENTE = Alumno,persona, registrar cliente, registrar aluno

pt_BR_CONFIGURACAO_NOTAFISCAL = Configuraï¿½ï¿½o Nota Fiscal
es_ES_CONFIGURACAO_NOTAFISCAL = Configuracion Factura

pt_BR_CLASSIFICACAO = ""
es_ES_CLASSIFICACAO = ""

pt_BR_QUESTIONARIO = BV, boletim de visita, perfil de cliente
es_ES_QUESTIONARIO = BV, boletin de visita, perfil de cliente

pt_BR_PESQUISA = Pesquisa, satisfaï¿½ï¿½o
es_ES_PESQUISA = Bï¿½squeda, satisfacciï¿½n

pt_BR_CATEGORIA_CLIENTES = CTC
es_ES_CATEGORIA_CLIENTES = CTC

pt_BR_PROFISSAO = PFS
es_ES_PROFISSAO = PFS

pt_BR_GRAU_DE_INSTRUCAO = GRIN
es_ES_GRAU_DE_INSTRUCAO = GRIN

pt_BR_GRUPO_DESCONTO = GRP
es_ES_GRUPO_DESCONTO = GRP

pt_BR_PARENTESCO = PRENT
es_ES_PARENTESCO = PRENT

pt_BR_PAIS = PAIS
es_ES_PAIS = PAIS

pt_BR_CIDADE = CDD
es_ES_CIDADE = CDD

pt_BR_PERGUNTA = PGT
es_ES_PERGUNTA = PGT

pt_BR_CATEGORIA_PRODUTO = CTP
es_ES_CATEGORIA_PRODUTO = CTP

pt_BR_PRODUTO = PRD
es_ES_PRODUTO = PRD

pt_BR_BRINDE = brinde
es_ES_BRINDE = brinde

pt_BR_LANCAMENTO_PRODUTO_COLETIVO = LPC
es_ES_LANCAMENTO_PRODUTO_COLETIVO = LPC

pt_BR_CONTROLE_ESTOQUE = CTRE
es_ES_CONTROLE_ESTOQUE = CTRE

pt_BR_TAMANHO_ARMARIO = TAME
es_ES_TAMANHO_ARMARIO = TAME

pt_BR_PLANO = LPC
es_ES_PLANO = LPC

pt_BR_MODALIDADE = MDLD
es_ES_MODALIDADE = MDLD

pt_BR_TIPO_MODALIDADE = TPMDLD
es_ES_TIPO_MODALIDADE = TPMDLD

pt_BR_DESCONTO = DSC
es_ES_DESCONTO = DSC

pt_BR_PACOTE = PCT
es_ES_PACOTE = PCT

pt_BR_CONDICAO_DE_PAGAMENTO = Forma de pagamento
es_ES_CONDICAO_DE_PAGAMENTO = Forma de pago

pt_BR_CONVENIO_DE_DESCONTO = CDSC
es_ES_CONVENIO_DE_DESCONTO = CDSC

pt_BR_TIPO_PLANO = Tipo Plano
es_ES_TIPO_PLANO = Tipo Plano

pt_BR_AMBIENTE = ABT
es_ES_AMBIENTE = ABT

pt_BR_NIVEL_TURMA = NT
es_ES_NIVEL_TURMA = NT

pt_BR_TURMA = TR
es_ES_TURMA = TR

pt_BR_HORARIO = HR
es_ES_HORARIO = HR

pt_BR_BALANCO = BLC
es_ES_BALANCO = BLC

pt_BR_CARDEX = CDX
es_ES_CARDEX = CDX

pt_BR_COMPRA = CMP
es_ES_COMPRA = CMP

pt_BR_CONFIGURAR_PRODUTO_ESTOQUE = CPE
es_ES_CONFIGURAR_PRODUTO_ESTOQUE = CPE

pt_BR_POSICAO_DO_ESTOQUE = PSE, Controle de Estoque
es_ES_POSICAO_DO_ESTOQUE = PSE, Control de Iventario

pt_BR_CONVITE_AULA_EXPERIMENTAL = CAE
es_ES_CONVITE_AULA_EXPERIMENTAL = CAE

pt_BR_CAMPANHA_CUPOM_DESCONTO = CCD, Desconto, Cupon
es_ES_CAMPANHA_CUPOM_DESCONTO = CCD, Descuento, Cupï¿½n

pt_BR_INDICE_FINANCEIRO_REAJUSTE_PRECO = IFRP, reajuste
es_ES_INDICE_FINANCEIRO_REAJUSTE_PRECO = IFRP, reajustar

pt_BR_DEPARTAMENTO = Departamento
es_ES_DEPARTAMENTO = Departamento

pt_BR_DADOS_USUSARIO = Usuario, Dados
es_ES_DADOS_USUSARIO = Usuario, Datos

pt_BR_CONSULTA_DE_RECIBOS = recibos, CR
es_ES_CONSULTA_DE_RECIBOS = recibos, CR

pt_BR_CONSULTA_DE_CUPONS_FISCAIS = fiscais, cupons, CCF
es_ES_CONSULTA_DE_CUPONS_FISCAIS = fiscal, cupons, CCF

pt_BR_GESTAO_NOTAS = GN, notas
es_ES_GESTAO_NOTAS = GN, notas

pt_BR_VENDA_CONSUMIDOR = VC, venda, consumidor
es_ES_VENDA_CONSUMIDOR = VC, venta, consumidor

pt_BR_MOVIMENTO_CC_CLIENTE = MVC
es_ES_MOVIMENTO_CC_CLIENTE = MVC

pt_BR_BANCO = CC
es_ES_BANCO = CC

pt_BR_CONTA_CORRENTE = CC
es_ES_CONTA_CORRENTE = CC

pt_BR_CONVENIO_COBRANCA = CC
es_ES_CONVENIO_COBRANCA = CC

pt_BR_TIPO_RETORNO = TRT
es_ES_TIPO_RETORNO = TRT

pt_BR_TIPO_REMESSA = TRE, Getnet, dcc, recorrï¿½ncia
es_ES_TIPO_REMESSA = TRE, Getnet, dcc, recurrencia

pt_BR_OPERADORA_CARTAO = OP, Visa, mastercard, dinners, american express
es_ES_OPERADORA_CARTAO = OP, Visa, Mastercard, Dinners, American Express

pt_BR_METAS_FINANCEIRO = OC
es_ES_METAS_FINANCEIRO = OC

pt_BR_TAXA_COMISSAO = TC, taxa
es_ES_TAXA_COMISSAO = TC, tasa

pt_BR_EMPRESA = EMP, UNIDADE
es_ES_EMPRESA = EMP, UNIDADE

pt_BR_FORMA_PAGAMENTO = EMP, UNIDADE
es_ES_FORMA_PAGAMENTO = EMP, UNIDADE

pt_BR_PERFIL_ACESSO = PFA, Permissï¿½o, permissao, Permissao
es_ES_PERFIL_ACESSO = PFA, Permision, permision, Permision

pt_BR_USUARIO = USR, usuario
es_ES_USUARIO = USR, usuario

pt_BR_LOCAL_ACESSO = LA, Catraca, digital
es_ES_LOCAL_ACESSO = LA, Torniquete, digital


pt_BR_SERVIDOR_FACIAL = SF, Servidor, facial
es_ES_SERVIDOR_FACIAL = SF, Servidor, facial

pt_BR_CONTROLE_LOG = CLG, Auditoria
es_ES_CONTROLE_LOG = CLG, Auditoria

pt_BR_AUTORIZACAO_ACESSO = ATA, acesso, autorizaï¿½ï¿½o
es_ES_AUTORIZACAO_ACESSO = ATA, autorizaciï¿½n

pt_BR_ATUALIZACOES_BD = ATA
es_ES_ATUALIZACOES_BD = ATA


pt_BR_INTEGRACAO_ACESSO = ITA
es_ES_INTEGRACAO_ACESSO = ITA


pt_BR_GERADOR_CONSULTAS = GRC
es_ES_GERADOR_CONSULTAS = GRC


pt_BR_DESCUBRA_FATOR_ZW = Fator ZW, Calcule
es_ES_DESCUBRA_FATOR_ZW = Factor ZW, Calcule

pt_BR_SORTEIO = Silvio Santos, Roda Viva, sorteio
es_ES_SORTEIO = loteria

pt_BR_MODELO_CONTRATO = MDLC
es_ES_MODELO_CONTRATO = MDLC

pt_BR_MOVIMENTO_PRODUTO = MOVP
es_ES_MOVIMENTO_PRODUTO = MOVP

pt_BR_JUSTIFICATIVA_OPERACAO = JSO
es_ES_JUSTIFICATIVA_OPERACAO = JSO

pt_BR_IMPRIME_RECIBO_BANCO = IRB
es_ES_IMPRIME_RECIBO_BANCO = IRB

pt_BR_GERAL_CLIENTES = RC, Alunos
es_ES_GERAL_CLIENTES = RC, Alunos

pt_BR_RELATORIO_VISITANTES = ""
es_ES_RELATORIO_VISITANTES = ""

pt_BR_RELATORIO_CLIENTES_CANCELADOS = ""
es_ES_RELATORIO_CLIENTES_CANCELADOS = ""

pt_BR_RELATORIO_CLIENTES_TRANCADOS = ""
es_ES_RELATORIO_CLIENTES_TRANCADOS = ""

pt_BR_RELATORIO_BONUS = ""
es_ES_RELATORIO_BONUS = ""

pt_BR_RELATORIO_ATESTADO = ""
es_ES_RELATORIO_ATESTADO = ""

pt_BR_CONTRATOS_DURACAO = ""
es_ES_CONTRATOS_DURACAO = ""

pt_BR_ANIVERSARIANTES = aniversario
es_ES_ANIVERSARIANTES =

pt_BR_LISTA_ACESSOS = frequï¿½ncia, acesso, acessos 
es_ES_LISTA_ACESSOS = frecuencia, acesso, acessos

pt_BR_INDICADOR_ACESSOS = acesso, acessos 
es_ES_INDICADOR_ACESSOS = acesso, acessos 

pt_BR_TOTALIZADOR_TICKETS = acesso, acessos
es_ES_TOTALIZADOR_TICKETS= acesso, acessos

pt_BR_FECHAMENTO_ACESSOS = acesso, acessos
es_ES_FECHAMENTO_ACESSOS= acesso, acessos

pt_BR_LISTA_CLIENTES_SIMPLIFICADA = ""
es_ES_LISTA_CLIENTES_SIMPLIFICADA = ""

pt_BR_SALDO_CREDITO = Creditos, Crï¿½ditos, Crï¿½ditos
es_ES_SALDO_CREDITO = Creditos, Crï¿½ditos, Crï¿½ditos

pt_BR_RELATORIO_CLIENTES_ORCAMENTOS = Orï¿½amento, Orï¿½amentos, Orï¿½amento cliente
es_ES_RELATORIO_CLIENTES_ORCAMENTOS = resupuesto, Presupuesto, Presupuesto cliente

pt_BR_LISTA_CHAMADA = ""
es_ES_LISTA_CHAMADA = ""

pt_BR_FREQUENCIA_OCUPACAO_TURMAS = ""
es_ES_FREQUENCIA_OCUPACAO_TURMAS = ""

pt_BR_FECHAMENTO_CAIXA_OPERADOR = ""
es_ES_FECHAMENTO_CAIXA_OPERADOR = ""

pt_BR_COMPETENCIA_MENSAL = ""
es_ES_COMPETENCIA_MENSAL = ""

pt_BR_FATURAMENTO_PERIODO = ""
es_ES_FATURAMENTO_PERIODO = ""

pt_BR_FATURAMENTO_RECEBIDO_PERIODO = Recebï¿½veis
es_ES_FATURAMENTO_RECEBIDO_PERIODO = Recibidos

pt_BR_RECEITA_PERIODO = Dinheiro
es_ES_RECEITA_PERIODO = Dinero

pt_BR_PARCELAS = ""
es_ES_PARCELAS = ""

pt_BR_RELATORIO_PRODUTOS = ""
es_ES_RELATORIO_PRODUTOS = ""

pt_BR_SALDO_CONTA_CORRENTE = CC, debito, credito
es_ES_SALDO_CONTA_CORRENTE = CC, debito, credito

pt_BR_PREVISAO_RENOVACAO_CONTRATO = ""
es_ES_PREVISAO_RENOVACAO_CONTRATO = ""

pt_BR_RELATORIO_CLIENTES = ""
es_ES_RELATORIO_CLIENTES = ""

pt_BR_RELATORIO_BVS = Botelin
es_ES_RELATORIO_BVS = Boletï¿½n

pt_BR_RELATORIO_PESQUISA = pesquisas, satisfaï¿½ï¿½o
es_ES_RELATORIO_PESQUISA = Bï¿½squedas, satisfacciï¿½n

pt_BR_RELATORIO_REPASSE = ""
es_ES_RELATORIO_REPASSE = ""

pt_BR_RELATORIO_GERAL_ARMARIOS = Armario, Armï¿½rios
es_ES_RELATORIO_GERAL_ARMARIOS = Armario, Armï¿½rios

pt_BR_HISTORICO_PONTOS_ALUNO = Pontos, Pontos Aluno, Historico, historico, pontos, relatorio,Historico Pontos, historico pontos
es_ES_HISTORICO_PONTOS_ALUNO = Puntos, Puntos del Alumno, Historial, historial, puntos, informe,Historial Puntos, Historial puntos

pt_BR_DESCONTO_OCUPACAO_TURMAS = ""
es_ES_DESCONTO_OCUPACAO_TURMAS = ""

pt_BR_CARTEIRAS = carteira, carteiras, organizar
es_ES_CARTEIRAS = cartera, carteras, organizar

pt_BR_CRM_META_EXTA = Meta extra, CRM
es_ES_CRM_META_EXTA = Meta extra, CRM

pt_BR_GRUPO_COLABORADOR = ""
es_ES_GRUPO_COLABORADOR = ""

pt_BR_EVENTO = ""
es_ES_EVENTO = ""

pt_BR_OBJECAO = ""
es_ES_OBJECAO = ""

pt_BR_MODELO_MENSAGEM = ""
es_ES_MODELO_MENSAGEM = ""

pt_BR_TEXTO_PADRAO = texto padrï¿½o, texto, padrï¿½o, script
es_ES_TEXTO_PADRAO = texto padrï¿½n, texto, padrï¿½n, script

pt_BR_FERIADO = ""
es_ES_FERIADO = ""

pt_BR_MARCAR_COMPARECIMENTO = ""
es_ES_MARCAR_COMPARECIMENTO = ""

pt_BR_CONSULTA_HISTORICO_CONTATO = ""
es_ES_CONSULTA_HISTORICO_CONTATO = ""

pt_BR_TOTALIZADOR_METAS = ""
es_ES_TOTALIZADOR_METAS = ""

pt_BR_PASSIVO = ""
es_ES_PASSIVO = ""

pt_BR_INDICACAO = ""
es_ES_INDICACAO = ""

pt_BR_CONTATO_APP = ""
es_ES_CONTATO_APP = ""

pt_BR_AGENDAMENTOS_CRM = ""
es_ES_AGENDAMENTOS_CRM = ""

pt_BR_LANCAMENTOS = Cobrar
es_ES_LANCAMENTOS = cobrar

pt_BR_LANCAMENTO_CONTA_RAPIDO = ""
es_ES_LANCAMENTO_CONTA_RAPIDO = ""

pt_BR_CONTAS_PAGAR = ""
es_ES_CONTAS_PAGAR = ""

pt_BR_NOVA_CONTA_PAGAR = ""
es_ES_NOVA_CONTA_PAGAR = ""

pt_BR_CONTAS_RECEBER = ""
es_ES_CONTAS_RECEBER = ""

pt_BR_NOVA_CONTAS_RECEBER = ""
es_ES_NOVA_CONTAS_RECEBER = ""

pt_BR_ULTIMOS_LANCAMENTOS = ""
es_ES_ULTIMOS_LANCAMENTOS = ""

pt_BR_RECEBIVEIS = ""
es_ES_RECEBIVEIS = ""

pt_BR_LOTES = ""
es_ES_LOTES = ""

pt_BR_FORNCEDOR = ""
es_ES_FORNCEDOR = ""

pt_BR_PESSOA = ""
es_ES_PESSOA = ""

pt_BR_CAIXA_ADIMISTRATIVO = ""
es_ES_CAIXA_ADIMISTRATIVO = ""

pt_BR_CONSULTAR_CAIXA = ""
es_ES_CONSULTAR_CAIXA = ""

pt_BR_ABRIR_CAIXA = ""
es_ES_ABRIR_CAIXA = ""

pt_BR_FECHAR_CAIXA = ""
es_ES_FECHAR_CAIXA = ""

pt_BR_RESUMO_CONTAS = ""
es_ES_RESUMO_CONTAS = ""

pt_BR_FIN_CADASTROS_AUXILIARES = ""
es_ES_FIN_CADASTROS_AUXILIARES = ""

pt_BR_FIN_CONFIG_FINANCEIRAS = ""
es_ES_FIN_CONFIG_FINANCEIRAS = ""

pt_BR_FINAN_CONTA = ""
es_ES_FINAN_CONTA = ""

pt_BR_DEMONSTRATIVO_FINAN = ""
es_ES_DEMONSTRATIVO_FINAN = ""

pt_BR_DRE = ""
es_ES_DRE = ""

pt_BR_FLUXO_CAIXA_FINAN = ""
es_ES_FLUXO_CAIXA_FINAN = ""

pt_BR_MOVIMENTACOES_FINAN = ""
es_ES_MOVIMENTACOES_FINAN = ""

pt_BR_RELATORIO_DEVOLUCAO_CHEQUE = ""
es_ES_RELATORIO_DEVOLUCAO_CHEQUE = ""

pt_BR_RATEIO_INTEGRACAO = "" 
es_ES_RATEIO_INTEGRACAO = ""

pt_BR_PLANO_CONTAS = ""
es_ES_PLANO_CONTAS = ""

pt_BR_CHEQUES_CARTOES_AVULSOS = ""
es_ES_CHEQUES_CARTOES_AVULSOS = ""

pt_BR_ESTORNO_PAGAMENTO = ""
es_ES_ESTORNO_PAGAMENTO = ""

pt_BR_CENTRO_CUSTOS = ""
es_ES_CENTRO_CUSTOS = ""

pt_BR_TIPO_CONTA = TC
es_ES_TIPO_CONTA = TC

pt_BR_CONTA_CONTABIL = TD
es_ES_CONTA_CONTABIL = TD

pt_BR_SIMULAR_ORCAMENTO = ""
es_ES_SIMULAR_ORCAMENTO = ""

pt_BR_LISTA_PROSPECTS = ""
es_ES_LISTA_PROSPECTS = ""

pt_BR_AGENDA_VISISTA = ""
es_ES_AGENDA_VISISTA = ""

pt_BR_CONVERSAS = ""
es_ES_CONVERSAS = ""

pt_BR_CAIXA_EM_ABERTO_CE = ""
es_ES_CAIXA_EM_ABERTO_CE = ""

pt_BR_PESQUISA_GERAL = ""
es_ES_PESQUISA_GERAL = ""

pt_BR_GESTAO_CREDITO = ""
es_ES_GESTAO_CREDITO = ""

pt_BR_TIPO_AMBIENTE = ""
es_ES_TIPO_AMBIENTE = ""

pt_BR_PERFIL_EVENTO = ""
es_ES_PERFIL_EVENTO = ""

pt_BR_SERVICOS = ""
es_ES_SERVICOS = ""

pt_BR_CADASTRO_INICIAL_CE = ""
es_ES_CADASTRO_INICIAL_CE = ""

pt_BR_AGENDA_EVENTOS = ""
es_ES_AGENDA_EVENTOS = ""

pt_BR_ANIVERSARIANTES_CE = ""
es_ES_ANIVERSARIANTES_CE = ""

pt_BR_FECHAMENTO_CAIXA_CE = ""
es_ES_FECHAMENTO_CAIXA_CE = ""

pt_BR_RECEITA_PERIODO_CE = ""
es_ES_RECEITA_PERIODO_CE = ""

pt_BR_PARCELA_ABERTO = ""
es_ES_PARCELA_ABERTO = ""

pt_BR_SALDO_CONTA_CORRENT = ""
es_ES_SALDO_CONTA_CORRENT = ""

pt_BR_CATEGORIA_CLIENTE_CE = ""
es_ES_CATEGORIA_CLIENTE_CE = ""

pt_BR_CLIENTE_CE = Aluno, pessoa, cadastrar cliente, cadastrar aluno
es_ES_CLIENTE_CE = Alumno, persona, registrar cliente, registrar aluno

pt_BR_COLABORADOR_CE = Funcionï¿½rio, permuta, terceirizado, consultor, usuï¿½rio,colaborador
es_ES_COLABORADOR_CE = Empleado, intercambio, externalizado, consultor, usuario,colaborador

pt_BR_GRAU_DE_INSTRUCAO_CE = GRIN
es_ES_GRAU_DE_INSTRUCAO_CE = GRIN

pt_BR_PROFISSAO_CE = PFS
es_ES_PROFISSAO_CE = PFS

pt_BR_PAIS_CE = PAIS
es_ES_PAIS_CE = PAIS

pt_BR_CIDADE_CE = CDD
es_ES_CIDADE_CE = CDD

pt_BR_PRODUTO_LOCACAO = CDD
es_ES_PRODUTO_LOCACAO = CDD

pt_BR_EMPRESA_CE = ""
es_ES_EMPRESA_CE = ""

pt_BR_PERFIL_ACESSO_CE = ""
es_ES_PERFIL_ACESSO_CE = ""

pt_BR_USUARIO_CE = ""
es_ES_USUARIO_CE = ""

pt_BR_CONTROLE_LOG_CE = ""
es_ES_CONTROLE_LOG_CE = ""

pt_BR_FORNECEDOR_CE = ""
es_ES_FORNECEDOR_CE = ""

pt_BR_AMBIENTE_CE = ABT
es_ES_AMBIENTE_CE = ABT

pt_BR_ESTORNO_RECIBO_CE = CR
es_ES_ESTORNO_RECIBO_CE = CR

pt_BR_MOVIMENTO_CC_CLIENTE_CE = MVC
es_ES_MOVIMENTO_CC_CLIENTE_CE = MVC

pt_BR_FORMA_PAGAMENTO_CE = FP
es_ES_FORMA_PAGAMENTO_CE = FP

pt_BR_OPERADORA_CARTAO_CE = OP, Visa, mastercard, dinners, american express
es_ES_OPERADORA_CARTAO_CE = OP, Visa, Mastercard, Dinners, American Express

pt_BR_COMISSAO_EST = ""
es_ES_COMISSAO_EST = ""

pt_BR_DIARIO = ""
es_ES_DIARIO = ""

pt_BR_AGENDAMENTOS = ""
es_ES_AGENDAMENTOS = ""

pt_BR_CLIENTES_SEM_SESSAO = ""
es_ES_CLIENTES_SEM_SESSAO = ""

pt_BR_CONFIG_EST = ""
es_ES_CONFIG_EST = ""

pt_BR_DISPONIBILIDADE_EST = ""
es_ES_DISPONIBILIDADE_EST = ""

pt_BR_VENDA_AVULSA_EST = ""
es_ES_VENDA_AVULSA_EST = ""

pt_BR_CAIXA_ABERTO_EST = ""
es_ES_CAIXA_ABERTO_EST = ""

pt_BR_PACOTE_EST = ""
es_ES_PACOTE_EST = ""

pt_BR_AGENDA_MENSAL_EST = ""
es_ES_AGENDA_MENSAL_EST = ""

pt_BR_AMBIENTE_EST = ""
es_ES_AMBIENTE_EST = ""

pt_BR_PROFISSIONAL_EST = ""
es_ES_PROFISSIONAL_EST = ""

pt_BR_INDIVIDUAL_EST = ""
es_ES_INDIVIDUAL_EST = ""

pt_BR_CONFIG_ZW = ""
es_ES_CONFIG_ZW = ""

pt_BR_GOOG_CALENDAR = ""
es_ES_GOOG_CALENDAR = ""

pt_BR_VELOCIMETRO = ""
es_ES_VELOCIMETRO = ""

pt_BR_DOC_VELOCIDADE = ""
es_ES_DOC_VELOCIDADE = ""

pt_BR_ALTERAR_SENHA = ""
es_ES_ALTERAR_SENHA = ""

pt_BR_UCP = ""
es_ES_UCP = ""

pt_BR_WIKI_PACTO = ""
es_ES_WIKI_PACTO = ""

pt_BR_CONFIG_CRM = ""
es_ES_CONFIG_CRM = ""

pt_BR_CONFIG_FIN = ""
es_ES_CONFIG_FIN = ""

pt_BR_SUPORTE = ""
es_ES_SUPORTE = ""

pt_BR_BOLETO = ""
es_ES_BOLETO = ""

pt_BR_CONFIG_ESTUDIO = ""
es_ES_CONFIG_ESTUDIO = ""

pt_BR_CONVITES_AULAS = ""
es_ES_CONVITES_AULAS = ""

pt_BR_BLOQUEIO_CAIXA = ""
es_ES_BLOQUEIO_CAIXA = ""

pt_BR_GYM_PASS_RELATORIO = Gym, gym, gympass
es_ES_GYM_PASS_RELATORIO = Gym, gym, gympass

pt_BR_GOGOOD_RELATORIO = Go, Gogood, gogood, GoGood
es_ES_GOGOOD_RELATORIO = Go, Gogood, gogood, GoGood

pt_BR_REGISTRAR_ACESSO_AVULSO = registrar, acesso, manual, acessos
es_ES_REGISTRAR_ACESSO_AVULSO = registrar, acceso, manual, acessos

pt_BR_ADQUIRENTE = ADQUIRENTE
es_ES_ADQUIRENTE = ADQUISITIVO

pt_BR_MODELO_ORCAMENTO = MDLO
es_ES_MODELO_ORCAMENTO = MDLO

pt_BR_CONTAS_A_PAGAR = Contas a pagar
es_ES_CONTAS_A_PAGAR = Cuentas a paga

pt_BR_CONTAS_A_RECEBER = Contas a receber
es_ES_CONTAS_A_RECEBER = Cuentas a recibir

pt_BR_IMPORTADOR_FINANCEIRO = ""
es_ES_IMPORTADOR_FINANCEIRO = ""

pt_BR_MAPA_ESTATISTICO = ""
es_ES_MAPA_ESTATISTICO = ""

pt_BR_HISTORICO_PONTOS_DOTZ = Pontos, Pontos Parceiro, Historico, historico, pontos, relatorio,Historico Pontos, historico pontos, dotz
es_ES_HISTORICO_PONTOS_DOTZ = Puntos, Puntos Socios,Historico, historico, punto, informe,Historico Puntos, historico puntos, dotz

pt_BR_GESTAO_DE_TURMA = Turmas, horarios, professores
es_ES_GESTAO_DE_TURMA = Grupos, horarios, profesores

pt_BR_GESTAO_FAMILIA = ""
es_ES_GESTAO_FAMILIA = ""

pt_BR_GYMPASS = Gym, gym, gympass
es_ES_GYMPASS = Gym, gym, gympass

pt_BR_CLUBE_VANTAGENS_CONFIGURACOES = Clube de vantagens, Indicadores, Pontuaï¿½ï¿½o, Pontos, 1berto
es_ES_CLUBE_VANTAGENS_CONFIGURACOES = Club de ventages,Indicadores,Pontuaciï¿½n, Puntos, 1berto

pt_BR_CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE = Clube de vantagens,Indicadores,Pontuaï¿½ï¿½o, Pontos, Vantagens, 1berto
es_ES_CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE = Club de ventages,Indicadores,Pontuaciï¿½n, Puntos, 1berto

pt_BR_CLUBE_VANTAGENS_CAMPANHA = Clube de vantagens, Campanha, Pontuaï¿½ï¿½o, Pontos, 1berto
es_ES_CLUBE_VANTAGENS_CAMPANHA = Club de ventage,Campaï¿½a,Pontuaciï¿½n, Puntos, 1berto

pt_BR_RELATORIO_CONVIDADOS = Convidados,convites,relatorio de convidados
es_ES_RELATORIO_CONVIDADOS = Invitados,invitaciones,informe de invitados

pt_BR_VENDAS_ONLINE = Vendas, Online, Vendas Online, Site
es_ES_VENDAS_ONLINE = Ventas, Online, Ventas Online, Sitio

pt_BR_TOTALIZADOR_ACESSOS = totalizador de acessos,acessos
es_ES_TOTALIZADOR_ACESSOS = totalizador de acceso, acceso

pt_BR_GESTAO_NFCE = gestao nota fiscal produto,gestao notas fiscais produtos, nota fiscal produto
es_ES_GESTAO_NFCE = gestiï¿½n de facturas de productos, gestiï¿½n de facturas de productos, facturas de productos

pt_BR_CONTATO_PESSOAL = contato pessoal,mailing,sms,email,ligacao,enviar
es_ES_CONTATO_PESSOAL = contato pessoal,mailing,sms,email,ligacao,enviar

pt_BR_MAILING = contato grupo,mailing,sms,email,ligacao,enviar,grupo
es_ES_MAILING = contato grupo,mailing,sms,email,ligacao,enviar,grupo

pt_BR_TIPO_DOCUMENTO = tipo de documento,tipos de documentos, documentos
es_ES_TIPO_DOCUMENTO = tipo de documento, tipos de documentos, documentos

pt_BR_CANAL_CLIENTE_PACTO_STORE = módulo, modulo, recurso, marketplace, comprar, sms, store, pacto
es_ES_CANAL_CLIENTE_PACTO_STORE = módulo, modulo, recurso, marketplace, comprar, sms, store, pacto

pt_BR_SMD_RELATORIO = SMD, smd, Smd
es_ES_SMD_RELATORIO = SMD, smd, Smd




































