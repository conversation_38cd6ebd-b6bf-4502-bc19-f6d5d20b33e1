<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReciboRel" pageWidth="680" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="625" leftMargin="20" rightMargin="35" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.771561000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="qtdCD" class="java.lang.String"/>
	<parameter name="valorCD" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="codRecibo" class="java.lang.String"/>
	<parameter name="empresaVO.cnpj" class="java.lang.String"/>
	<parameter name="empresaVO.endereco" class="java.lang.String"/>
	<parameter name="empresaVO.site" class="java.lang.String"/>
	<parameter name="empresaVO.fone" class="java.lang.String"/>
	<parameter name="mostrarCnpj" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="totalContrato" class="java.lang.String"/>
	<parameter name="mostrarModalidade" class="java.lang.Boolean"/>
	<parameter name="detalharPeriodoProduto" class="java.lang.Boolean"/>
	<parameter name="detalharParcelas" class="java.lang.Boolean"/>
	<parameter name="detalharPagamentos" class="java.lang.Boolean"/>
	<parameter name="detalharDescontos" class="java.lang.Boolean"/>
	<parameter name="apresentarAssinaturas" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="apresentarObservacao" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="observacaoRecibo" class="java.lang.String"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<parameter name="identificador" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="emitirNomeResponsavel" class="java.lang.Boolean" isForPrompting="false"/>
	<field name="listaMovProduto" class="java.lang.Object"/>
	<field name="listaMovParcela" class="java.lang.Object"/>
	<field name="listaMovPagamento" class="java.lang.Object"/>
	<field name="listaDescontosRecibo" class="java.lang.Object"/>
	<field name="reciboPagamentoVO.codigo" class="java.lang.Integer"/>
	<field name="reciboPagamentoVO.data" class="java.util.Date"/>
	<field name="reciboPagamentoVO.valorTotal" class="java.lang.Double"/>
	<field name="reciboPagamentoVO.nomePessoaPagador" class="java.lang.String"/>
	<field name="reciboPagamentoVO.responsavelLancamento.nome" class="java.lang.String"/>
	<field name="reciboPagamentoVO.valorTotalPorExtenso" class="java.lang.String"/>
	<field name="numeroContrato" class="java.lang.String"/>
	<field name="nomeOperador" class="java.lang.String"/>
	<field name="matricula" class="java.lang.String"/>
	<field name="mostrarNumeroContrato" class="java.lang.Boolean"/>
	<field name="consultorResponsavel" class="java.lang.String"/>
	<field name="centralEventos" class="java.lang.Boolean"/>
	<field name="descricaoDevolucao" class="java.lang.String"/>
	<field name="reciboPagamentoVO.pessoaPagador.cfp" class="java.lang.String"/>
	<field name="valorParcelasAberto" class="java.lang.Double"/>
	<field name="contratoVO.valorContrato" class="java.lang.Double"/>
	<field name="movProduto.precoUnitario" class="java.lang.Double"/>
	<field name="modalidades" class="java.lang.String"/>
	<field name="descConfiguracaoVO.porcentagemDescontoApresentar" class="java.lang.String"/>
	<field name="movProduto.produto.tipoProduto" class="java.lang.String"/>
	<field name="convenioDescontoVO.descricao" class="java.lang.String"/>
	<field name="reciboPagamentoVO.contrato.codigo" class="java.lang.Integer"/>
	<field name="movProduto.descricao" class="java.lang.String"/>
	<field name="apresentarDescontos" class="java.lang.Boolean"/>
	<field name="sequencial" class="java.lang.Integer"/>
	<field name="reciboPagamentoVO.nomeResponsaveis" class="java.lang.String"/>
	<field name="reciboPagamentoVO.pessoaPagador.cnpj" class="java.lang.String"/>
	<field name="reciboPagamentoVO.nomeResponsavelLegal" class="java.lang.String"/>
	<field name="reciboPagamentoVO.cpfResponsavelLegal" class="java.lang.String"/>
	<variable name="reciboPagamentoVO.codigo_SUM" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{reciboPagamentoVO.codigo}]]></variableExpression>
	</variable>
	<group name="caixaPoOperador" minHeightToStartNewPage="75">
		<groupExpression><![CDATA[$F{reciboPagamentoVO.codigo}]]></groupExpression>
	</group>
	<group name="movProduto">
		<groupExpression><![CDATA[]]></groupExpression>
	</group>
	<group name="movParcela"/>
	<group name="movPagamento">
		<groupExpression><![CDATA[]]></groupExpression>
	</group>
	<group name="sequencial">
		<groupHeader>
			<band splitType="Immediate"/>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<detail>
		<band height="84">
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="3" y="42" width="89" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DT Ent Caixa]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-121" positionType="Float" mode="Opaque" x="93" y="42" width="50" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Recibo]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="143" y="42" width="80" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-88" positionType="Float" mode="Opaque" x="223" y="42" width="205" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Nome ]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-131" mode="Opaque" x="542" y="42" width="28" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Desc.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-132" mode="Opaque" x="524" y="42" width="18" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Qtd]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-130" mode="Opaque" x="465" y="42" width="59" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Unitário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-133" mode="Opaque" x="570" y="42" width="50" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Valor Pago]]></text>
			</staticText>
			<image scaleImage="FillFrame" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" stretchType="RelativeToBandHeight" x="4" y="0" width="95" height="39" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-228" mode="Opaque" x="4" y="58" width="90" height="12">
					<printWhenExpression><![CDATA[$F{reciboPagamentoVO.codigo}.intValue() > 0]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{reciboPagamentoVO.data}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="224" y="58" width="280" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{reciboPagamentoVO.nomePessoaPagador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-230" mode="Opaque" x="95" y="58" width="50" height="12">
					<printWhenExpression><![CDATA[$F{reciboPagamentoVO.codigo}.intValue() > 0]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{reciboPagamentoVO.codigo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-231" mode="Opaque" x="146" y="58" width="78" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="56" width="622" height="1"/>
			</line>
			<rectangle radius="10">
				<reportElement key="retDadosEmpresa1" x="104" y="0" width="367" height="39" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="109" y="2" width="255" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="109" y="14" width="357" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.endereco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="109" y="26" width="255" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.site}.toLowerCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="365" y="2" width="101" height="13">
					<printWhenExpression><![CDATA[$P{mostrarCnpj}.equals(true)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.cnpj}]]></textFieldExpression>
			</textField>
			<rectangle radius="10">
				<reportElement key="retDadosRecibo1" x="481" y="0" width="136" height="39" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="nomeRecibo1" x="489" y="2" width="119" height="19">
					<printWhenExpression><![CDATA[$F{reciboPagamentoVO.codigo}.intValue() > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio} + " Nº " + $F{reciboPagamentoVO.codigo}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="valorRecibo1" x="524" y="19" width="84" height="19">
					<printWhenExpression><![CDATA[$F{reciboPagamentoVO.valorTotal}.intValue() > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{reciboPagamentoVO.valorTotal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="365" y="26" width="101" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.fone}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-130" mode="Opaque" x="428" y="42" width="43" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Pacote]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="224" y="70" width="280" height="12">
					<printWhenExpression><![CDATA[$F{reciboPagamentoVO.nomeResponsaveis} != null && !$F{reciboPagamentoVO.nomeResponsaveis}.equals("")]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{reciboPagamentoVO.nomeResponsaveis}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="489" y="21" width="35" height="20"/>
				<textElement>
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
		<band height="40" splitType="Immediate">
			<subreport isUsingCache="false" runToBottom="false">
				<reportElement key="subreport-1" positionType="Float" stretchType="RelativeToTallestObject" x="12" y="22" width="610" height="11" isPrintWhenDetailOverflows="true"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="listaMovProduto">
					<subreportParameterExpression><![CDATA[$F{listaMovProduto}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharPeriodoProduto">
					<subreportParameterExpression><![CDATA[$P{detalharPeriodoProduto}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaMovProduto}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR1} + "MovProduto.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement key="staticText-125" mode="Opaque" x="0" y="10" width="102" height="12">
					<printWhenExpression><![CDATA[!$F{centralEventos}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Produtos do Recibo]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-228" mode="Opaque" x="1" y="0" width="342" height="12">
					<printWhenExpression><![CDATA[$F{centralEventos}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoDevolucao}]]></textFieldExpression>
			</textField>
		</band>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="145" y="2" width="329" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{mostrarModalidade}.equals(true)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{modalidades}]]></textFieldExpression>
			</textField>
		</band>
		<band height="30">
			<printWhenExpression><![CDATA[$P{detalharDescontos} && $F{movProduto.produto.tipoProduto}.equals("PM")]]></printWhenExpression>
			<subreport isUsingCache="false">
				<reportElement x="12" y="12" width="610" height="11" isRemoveLineWhenBlank="true"/>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaDescontosRecibo}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "Descontos.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement key="staticText-126" mode="Opaque" x="0" y="0" width="131" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{apresentarDescontos}.equals( true )]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Descontos]]></text>
			</staticText>
		</band>
		<band height="15">
			<printWhenExpression><![CDATA[$P{detalharParcelas}]]></printWhenExpression>
			<subreport isUsingCache="false">
				<reportElement key="subreport-2" stretchType="RelativeToBandHeight" x="12" y="2" width="610" height="11" isRemoveLineWhenBlank="true"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="listaMovProduto">
					<subreportParameterExpression><![CDATA[$F{listaMovProduto}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaMovParcela}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR2} + "MovParcela.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="30">
			<staticText>
				<reportElement key="staticText-126" mode="Opaque" x="0" y="0" width="131" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Pagamentos do Recibo]]></text>
			</staticText>
			<subreport isUsingCache="false">
				<reportElement key="subreport-3" stretchType="RelativeToBandHeight" x="12" y="16" width="610" height="11"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharPagamentos">
					<subreportParameterExpression><![CDATA[$P{detalharPagamentos}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="listaMovPagamento">
					<subreportParameterExpression><![CDATA[$F{listaMovPagamento}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaMovPagamento}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovPagamento.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="130">
			<textField isStretchWithOverflow="true">
				<reportElement x="12" y="0" width="612" height="29" isPrintWhenDetailOverflows="true" backcolor="#000000"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["* Recebemos de " +
($P{emitirNomeResponsavel} == true ?
    ($F{reciboPagamentoVO.nomeResponsavelLegal}.isEmpty() ?
        "                                              " :
        $F{reciboPagamentoVO.nomeResponsavelLegal} ) :
($F{reciboPagamentoVO.nomePessoaPagador}.isEmpty()? "                                              " : $F{reciboPagamentoVO.nomePessoaPagador})
)+
", a quantia de ''" +
($F{reciboPagamentoVO.valorTotalPorExtenso}.isEmpty()? "                                                              " : $F{reciboPagamentoVO.valorTotalPorExtenso})+
"'', proveniente dos itens supracitados." +($F{valorParcelasAberto}.doubleValue() > 0 ? " **Resta ainda uma quantia de R$ "+$F{valorParcelasAberto} + " para ser quitada." : "")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="FixRelativeToBottom" x="0" y="128" width="624" height="1" isRemoveLineWhenBlank="true"/>
				<graphicElement>
					<pen lineWidth="1.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="12" y="119" width="49" height="8" isRemoveLineWhenBlank="true"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Data impressão:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement key="dataImpressao1" positionType="FixRelativeToBottom" mode="Transparent" x="61" y="119" width="90" height="8" isRemoveLineWhenBlank="true"/>
				<textElement verticalAlignment="Top">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="500" y="119" width="49" height="8" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Data pagamento:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy HH:mm:ss">
				<reportElement key="dataPagamento1" positionType="FixRelativeToBottom" x="549" y="119" width="70" height="8" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{reciboPagamentoVO.codigo}.intValue() > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{reciboPagamentoVO.data}]]></textFieldExpression>
			</textField>
			<elementGroup>
				<line>
					<reportElement positionType="FixRelativeToBottom" x="11" y="91" width="262" height="1" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
				</line>
				<textField>
					<reportElement positionType="FixRelativeToBottom" x="11" y="93" width="262" height="12" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["Resp. Recebimento: " + $F{reciboPagamentoVO.responsavelLancamento.nome}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="FixRelativeToBottom" x="12" y="103" width="261" height="12" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["Cons. Resp.: " + $F{consultorResponsavel}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="FixRelativeToBottom" x="343" y="91" width="276" height="1" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
				</line>
				<textField>
					<reportElement positionType="FixRelativeToBottom" x="343" y="93" width="276" height="12" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["Resp. Pagamento: " + ($P{emitirNomeResponsavel} ?
    $F{reciboPagamentoVO.nomeResponsavelLegal} :
    $F{reciboPagamentoVO.nomePessoaPagador})]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="FixRelativeToBottom" x="343" y="103" width="276" height="12" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($P{emitirNomeResponsavel} == true ?
    $F{reciboPagamentoVO.cpfResponsavelLegal} :
    ""+($F{reciboPagamentoVO.pessoaPagador.cfp} == null || $F{reciboPagamentoVO.pessoaPagador.cfp}.equals("") ?
            ($F{reciboPagamentoVO.pessoaPagador.cnpj} == null || $F{reciboPagamentoVO.pessoaPagador.cnpj}.equals("")  ?
                " " :
                "CNPJ: "+$F{reciboPagamentoVO.pessoaPagador.cnpj}) :
        $P{identificador} + " " + $F{reciboPagamentoVO.pessoaPagador.cfp})
)]]></textFieldExpression>
				</textField>
			</elementGroup>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" x="12" y="35" width="612" height="33" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$P{apresentarObservacao}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{observacaoRecibo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
