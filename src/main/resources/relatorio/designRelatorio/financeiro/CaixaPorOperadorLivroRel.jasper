¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî            q           J  ¨    #    pppur ([Lnet.sf.jasperreports.engine.JRDataset;L6Í¬D  xp   sr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressiont *Lnet/sf/jasperreports/engine/JRExpression;[ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî  pppt Testeur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ L propertiesListt Ljava/util/List;L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~ #ppt 
JASPER_REPORTpsq ~ &pppt (net.sf.jasperreports.engine.JasperReportpsq ~ #ppt REPORT_CONNECTIONpsq ~ &pppt java.sql.Connectionpsq ~ #ppt REPORT_MAX_COUNTpsq ~ &pppt java.lang.Integerpsq ~ #ppt REPORT_DATA_SOURCEpsq ~ &pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ #ppt REPORT_SCRIPTLETpsq ~ &pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ #ppt 
REPORT_LOCALEpsq ~ &pppt java.util.Localepsq ~ #ppt REPORT_RESOURCE_BUNDLEpsq ~ &pppt java.util.ResourceBundlepsq ~ #ppt REPORT_TIME_ZONEpsq ~ &pppt java.util.TimeZonepsq ~ #ppt REPORT_FORMAT_FACTORYpsq ~ &pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ #ppt REPORT_CLASS_LOADERpsq ~ &pppt java.lang.ClassLoaderpsq ~ #ppt REPORT_URL_HANDLER_FACTORYpsq ~ &pppt  java.net.URLStreamHandlerFactorypsq ~ #ppt REPORT_FILE_RESOLVERpsq ~ &pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ #ppt REPORT_TEMPLATESpsq ~ &pppt java.util.Collectionpsq ~ &ppppppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupt %Lnet/sf/jasperreports/engine/JRGroup;L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ dL resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xr java.lang.Enum          xpt SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ it NONEppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp    ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt new java.lang.Integer(1)q ~ 6pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ it REPORTq ~ 6psq ~ b  wî   q ~ jppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6pt 
COLUMN_NUMBERp~q ~ xt PAGEq ~ 6psq ~ b  wî   ~q ~ ht COUNTsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt REPORT_COUNTpq ~ yq ~ 6psq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt 
PAGE_COUNTpq ~ q ~ 6psq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt COLUMN_COUNTp~q ~ xt COLUMNq ~ 6p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ it NULLpppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressionq ~ L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrenq ~ 'L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   sr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiust Ljava/lang/Integer;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ ®L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ ®xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ ±L 	forecolorq ~ »L keyq ~ L modeq ~ ®L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ dL 
propertiesMapq ~ [ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî          o   ÿÿÿÿpq ~ q ~ ²pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ it FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ it 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ »L 	lineStyleq ~ ®L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîppppq ~ Áppsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ dL evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ ®L borderColorq ~ »L bottomBorderq ~ ®L bottomBorderColorq ~ »L 
bottomPaddingq ~ ¶L fontNameq ~ L fontSizeq ~ ¶L horizontalAlignmentq ~ ®L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ ÐL isItalicq ~ ÐL 
isPdfEmbeddedq ~ ÐL isStrikeThroughq ~ ÐL isStyledTextq ~ ÐL isUnderlineq ~ ÐL 
leftBorderq ~ ®L leftBorderColorq ~ »L leftPaddingq ~ ¶L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ ®L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ ¶L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ ®L rightBorderColorq ~ »L rightPaddingq ~ ¶L rotationq ~ ®L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ ®L topBorderColorq ~ »L 
topPaddingq ~ ¶L verticalAlignmentq ~ ®L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ º  wî           L  "    pq ~ q ~ ²pt 
staticText-85p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ it OPAQUEpp~q ~ Ât FLOATppppq ~ Æ  wîpppppt Microsoft Sans Serifsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   
ppsr java.lang.BooleanÍ rÕúî Z valuexpsq ~ â pq ~ äpq ~ äpppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ ¶L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ ¶L leftPenq ~ æL paddingq ~ ¶L penq ~ æL rightPaddingq ~ ¶L rightPenq ~ æL 
topPaddingq ~ ¶L topPenq ~ æxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ Óxq ~ È  wîppppq ~ èq ~ èq ~ ×psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ ê  wîppppq ~ èq ~ èpsq ~ ê  wîppppq ~ èq ~ èpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ ê  wîppppq ~ èq ~ èpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ ê  wîppppq ~ èq ~ èppt noneppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ it MIDDLE  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ it NOWsq ~ o   +uq ~ r   sq ~ tt nomeUsuarioOperacaot java.lang.Stringppppppppppsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xq ~ ·  wî             ÿÿÿÿpq ~ q ~ ²ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ it TOP_DOWNsq ~ Í  wî           L  Ð    pq ~ q ~ ²sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~
xp    ÿÿ  pppt 
staticText-85pq ~ Úppq ~ Üppppq ~ Æ  wîpppppt Microsoft Sans Serifq ~ áp~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ it RIGHTq ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîppppq ~q ~q ~psq ~ ì  wîppppq ~q ~psq ~ ê  wîppppq ~q ~psq ~ ï  wîppppq ~q ~psq ~ ñ  wîppppq ~q ~ppt noneppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   ,uq ~ r   sq ~ tt valorTotalSaidat java.lang.Doublepppppppppt Â¤ #,##0.00sq ~   wî             Íÿÿÿÿpq ~ q ~ ²ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~p  wî q ~sq ~ Í  wî           N  }    pq ~ q ~ ²sq ~    ÿ3 pppt 
staticText-85pq ~ Úppq ~ Üppppq ~ Æ  wîpppppt Microsoft Sans Serifq ~ ápq ~q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîppppq ~%q ~%q ~!psq ~ ì  wîppppq ~%q ~%psq ~ ê  wîppppq ~%q ~%psq ~ ï  wîppppq ~%q ~%psq ~ ñ  wîppppq ~%q ~%ppt noneppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   -uq ~ r   sq ~ tt valorTotalPagamentot java.lang.Doublepppppppppt Â¤ #,##0.00sq ~   wî             |ÿÿÿÿpq ~ q ~ ²ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~3p  wî q ~sq ~ Í  wî              ò    pq ~ q ~ ²pt 
staticText-85pq ~ Úppq ~ Üppppq ~ Æ  wîpppppt Microsoft Sans Serifq ~ áppq ~ äq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîppppq ~8q ~8q ~5psq ~ ì  wîppppq ~8q ~8psq ~ ê  wîppppq ~8q ~8psq ~ ï  wîppppq ~8q ~8psq ~ ñ  wîppppq ~8q ~8ppt noneppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   .uq ~ r   sq ~ tt formaPagamentoDescricaot java.lang.Stringppppppppppsq ~   wî              ïÿÿÿÿpq ~ q ~ ²ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~Ep  wî q ~sq ~ Í  wî           ¸   6    pq ~ q ~ ²pt 
staticText-85pq ~ Úppq ~ Üppppq ~ Æ  wîpppppt Microsoft Sans Serifq ~ áppq ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîppppq ~Jq ~Jq ~Gpsq ~ ì  wîppppq ~Jq ~Jpsq ~ ê  wîppppq ~Jq ~Jpsq ~ ï  wîppppq ~Jq ~Jpsq ~ ñ  wîppppq ~Jq ~Jppt noneppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   /uq ~ r   sq ~ tt nomePagadort java.lang.Stringppppppppppsq ~   wî              3ÿÿÿÿpq ~ q ~ ²ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~Wp  wî q ~sq ~ Í  wî           -       pq ~ q ~ ²pt 
staticText-85pq ~ Úppq ~ Üppppq ~ Æ  wîpppppt Microsoft Sans Serifq ~ áp~q ~t CENTERq ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîppppq ~^q ~^q ~Ypsq ~ ì  wîppppq ~^q ~^psq ~ ê  wîppppq ~^q ~^psq ~ ï  wîppppq ~^q ~^psq ~ ñ  wîppppq ~^q ~^ppt noneppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   0uq ~ r   sq ~ tt 	matriculat java.lang.Stringppppppppppxp  wî   pppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsq ~   wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ L valueClassNameq ~ L valueClassRealNameq ~ xppt 	matriculasq ~ &pppt java.lang.Stringpsq ~tpt nomePagadorsq ~ &pppt java.lang.Stringpsq ~tpt formaPagamentoDescricaosq ~ &pppt java.lang.Stringpsq ~tpt valorTotalPagamentosq ~ &pppt java.lang.Doublepsq ~tpt nomeUsuarioOperacaosq ~ &pppt java.lang.Stringpsq ~tpt valorTotalSaidasq ~ &pppt java.lang.Doublepsq ~tt  t dataLancamentosq ~ &pppt java.util.Dateppur &[Lnet.sf.jasperreports.engine.JRGroup;@£_zLýxê  xp   sr ,net.sf.jasperreports.engine.base.JRBaseGroup      'Ø I PSEUDO_SERIAL_VERSION_UIDB footerPositionZ isReprintHeaderOnEachPageZ isResetPageNumberZ isStartNewColumnZ isStartNewPageZ keepTogetherI minHeightToStartNewPageL 
countVariablet (Lnet/sf/jasperreports/engine/JRVariable;L 
expressionq ~ L footerPositionValuet 5Lnet/sf/jasperreports/engine/type/FooterPositionEnum;L groupFooterq ~ L groupFooterSectionq ~ L groupHeaderq ~ L groupHeaderSectionq ~ L nameq ~ xp  wî         sq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt dataLancamento_COUNTq ~~q ~ xt GROUPq ~ 6psq ~ o   uq ~ r   sq ~ tt dataLancamentot java.lang.Objectp~r 3net.sf.jasperreports.engine.type.FooterPositionEnum          xq ~ it NORMALpsq ~ ¨uq ~ «   sq ~ ­sq ~ ³   w   sq ~ µ  wî          o   ÿÿÿÿpq ~ q ~®ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~°ppsq ~ µ  wî          0   ï   pq ~ q ~®ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~²ppsq ~   wî             ÿÿÿÿpq ~ q ~®ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~´p  wî q ~sq ~   wî             |ÿÿÿÿpq ~ q ~®ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~¶p  wî q ~sq ~   wî             Íÿÿÿÿpq ~ q ~®ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~¸p  wî q ~sq ~   wî              ïÿÿÿÿpq ~ q ~®ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~ºp  wî q ~sq ~   wî              3ÿÿÿÿpq ~ q ~®ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~¼p  wî q ~sq ~   wî             |   pq ~ q ~®ppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~¾p  wî q ~sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ Ñ  wî              ò   pq ~ q ~®pt 
staticText-85pq ~ Úppq ~ Üsq ~ o   #uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())t java.lang.Booleanppppq ~ Æ  wîpppppt Arialsq ~ ß   ppq ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ it SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ à    q ~Îq ~Îq ~Ápsq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~Îq ~Îpsq ~ ê  wîppppq ~Îq ~Îpsq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~Îq ~Îpsq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~Îq ~Îpppppt 	Helveticappppppppppq ~ öt 	RESULTADOsq ~À  wî              ò    pq ~ q ~®pt 
staticText-85pq ~ Úppq ~ Üsq ~ o   $uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())q ~Ëppppq ~ Æ  wîpppppt Arialq ~Íppq ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~íq ~íq ~âpsq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~íq ~ípsq ~ ê  wîppppq ~íq ~ípsq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~íq ~ípsq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~íq ~ípppppt 	Helveticappppppppppq ~ öt TOTALsq ~ Í  wî           J     pq ~ q ~®sq ~    ÿ3 pppt 
staticText-85pq ~ Úppq ~ Üppppq ~ Æ  wîpppppt Microsoft Sans Serifq ~ ápq ~q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîppppq ~q ~q ~ýpsq ~ ì  wîppppq ~q ~psq ~ ê  wîppppq ~q ~psq ~ ï  wîppppq ~q ~psq ~ ñ  wîppppq ~q ~ppt noneppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   %uq ~ r   sq ~ tt totalEntradat java.lang.Doublepppppppppt Â¤ #,##0.00sq ~ Í  wî           L  Ð   pq ~ q ~®sq ~    ÿÿ  pppt 
staticText-85pq ~ Úppq ~ Üppppq ~ Æ  wîpppppt Microsoft Sans Serifq ~ ápq ~q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîppppq ~q ~q ~psq ~ ì  wîppppq ~q ~psq ~ ê  wîppppq ~q ~psq ~ ï  wîppppq ~q ~psq ~ ñ  wîppppq ~q ~ppt noneppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   &uq ~ r   sq ~ tt 
totalSaidat java.lang.Doublepppppppppt Â¤ #,##0.00sq ~ Í  wî                pq ~ q ~®sq ~    ÿÿ  pppt 
staticText-85pq ~ Úppq ~ Üsq ~ o   'uq ~ r   sq ~ tt (sq ~ tt totalEntradasq ~ tt  - sq ~ tt 
totalSaidasq ~ tt ) < 0.0q ~Ëppppq ~ Æ  wîpppppt Microsoft Sans Serifq ~ ápq ~q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîppppq ~1q ~1q ~!psq ~ ì  wîppppq ~1q ~1psq ~ ê  wîppppq ~1q ~1psq ~ ï  wîppppq ~1q ~1psq ~ ñ  wîppppq ~1q ~1ppt noneppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   (uq ~ r   sq ~ tt totalEntradasq ~ tt  - sq ~ tt 
totalSaidat java.lang.Doublepppppppppt Â¤ #,##0.00sq ~ Í  wî                pq ~ q ~®sq ~    ÿ 3ÿpppt 
staticText-85pq ~ Úppq ~ Üsq ~ o   )uq ~ r   sq ~ tt (sq ~ tt totalEntradasq ~ tt  - sq ~ tt 
totalSaidasq ~ tt ) > 0.0q ~Ëppppq ~ Æ  wîpppppt Microsoft Sans Serifq ~ ápq ~q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîppppq ~Sq ~Sq ~Cpsq ~ ì  wîppppq ~Sq ~Spsq ~ ê  wîppppq ~Sq ~Spsq ~ ï  wîppppq ~Sq ~Spsq ~ ñ  wîppppq ~Sq ~Sppt noneppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   *uq ~ r   sq ~ tt totalEntradasq ~ tt  - sq ~ tt 
totalSaidat java.lang.Doublepppppppppt Â¤ #,##0.00xp  wî   *ppppsq ~ ¨uq ~ «   sq ~ ­sq ~ ³   w   sq ~À  wî             1   'pq ~ q ~gpt 
staticText-82pq ~ Úppq ~ Ãppppq ~ Æ  wîpppppt Arialq ~Ípq ~\q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~lq ~lq ~ipsq ~ ì  wîsq ~    ÿfffpppppsq ~Ô    q ~lq ~lpsq ~ ê  wîsq ~    ÿfffpppppsq ~Ô    q ~lq ~lpsq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~lq ~lpsq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~lq ~lpppppt Helvetica-Boldppppppppppq ~ öt asq ~ Í  wî           i     sq ~    ÿÿÿÿpppq ~ q ~gpt 	dataRel-1p~q ~ Ùt TRANSPARENTppq ~ Ãppppq ~ Æ  wîpppppt Verdanasq ~ ß   pq ~\pq ~ äpppppppsq ~ åsq ~ ß    sq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~q ~q ~~psq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~q ~psq ~ ê  wîppppq ~q ~psq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~q ~psq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~q ~pppppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   uq ~ r   sq ~ tt 
new Date()t java.util.Dateppppppq ~ äppt dd/MM/yyyy HH.mm.sssq ~ Í  wî          µ   R   pq ~ q ~gpt textField-2ppppq ~ Ãppppq ~ Æ  wîpppppt Arialq ~Ípq ~\q ~ ãppppppppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~q ~q ~psq ~ ì  wîppq ~Òsq ~Ô    q ~q ~psq ~ ê  wîppq ~Òsq ~Ô?   q ~q ~psq ~ ï  wîppq ~Òsq ~Ô    q ~q ~psq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~q ~pppppt Helvetica-Boldppppppppppq ~ ö  wî        ppq ~ ùsq ~ o   uq ~ r   sq ~ tt tituloRelatoriot java.lang.Stringppppppq ~ äpppsq ~ Í  wî           ß   R   'pq ~ q ~gpt 
textField-215ppppq ~ Ãppppq ~ Æ  wîpppppt Arialq ~Ípq ~q ~ ãq ~ ãpppppppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~´q ~´q ~±psq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~´q ~´psq ~ ê  wîppppq ~´q ~´psq ~ ï  wîsq ~    ÿfffppppq ~Òpq ~´q ~´psq ~ ñ  wîppppq ~´q ~´pppppt Helvetica-BoldObliqueppppppppppq ~ ö  wî        ppq ~ ùsq ~ o   uq ~ r   sq ~ tt dataInit java.lang.Stringppppppq ~ äpppsq ~ Í  wî           ¼  K   'pq ~ q ~gpt 
textField-216ppppq ~ Ãppppq ~ Æ  wîpppppt Arialq ~Íp~q ~t LEFTq ~ ãq ~ ãpppppppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~Êq ~Êq ~Åpsq ~ ì  wîppppq ~Êq ~Êpsq ~ ê  wîppppq ~Êq ~Êpsq ~ ï  wîsq ~    ÿfffpppppsq ~Ô?   q ~Êq ~Êpsq ~ ñ  wîppppq ~Êq ~Êpppppt Helvetica-BoldObliqueppppppppppq ~ ö  wî        ppq ~ ùsq ~ o   uq ~ r   sq ~ tt dataFimt java.lang.Stringppppppq ~ äpppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ ®L borderColorq ~ »L bottomBorderq ~ ®L bottomBorderColorq ~ »L 
bottomPaddingq ~ ¶L evaluationGroupq ~ dL evaluationTimeValueq ~ ÎL 
expressionq ~ L horizontalAlignmentq ~ ®L horizontalAlignmentValueq ~ ÒL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ ÏL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ ÐL 
leftBorderq ~ ®L leftBorderColorq ~ »L leftPaddingq ~ ¶L lineBoxq ~ ÓL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ ¶L rightBorderq ~ ®L rightBorderColorq ~ »L rightPaddingq ~ ¶L 
scaleImageq ~ ®L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ ®L topBorderColorq ~ »L 
topPaddingq ~ ¶L verticalAlignmentq ~ ®L verticalAlignmentValueq ~ Öxq ~ ·  wî   &       R       pq ~ q ~gpt image-1ppppq ~ Üppppq ~ Æ  wîppsq ~ È  wîppppq ~Ýp  wî         ppppppp~q ~ øt PAGEsq ~ o   uq ~ r   sq ~ tt logoPadraoRelatoriot java.io.InputStreamppppppppq ~ ãpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~çq ~çq ~Ýpsq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~çq ~çpsq ~ ê  wîppppq ~çq ~çpsq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~çq ~çpsq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~çq ~çpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ it BLANKpppppppppp~q ~ õt TOPsq ~ Í  wî           i     
pq ~ q ~gpppq ~ppq ~ Ãppppq ~ Æ  wîpppppt Verdanaq ~pq ~\pppppppppsq ~ åpsq ~ é  wîpp~q ~Ñt DOTTEDsq ~Ô?   q ~üq ~üq ~úpsq ~ ì  wîppq ~þsq ~Ô?   q ~üq ~üpsq ~ ê  wîppppq ~üq ~üpsq ~ ï  wîppq ~þsq ~Ô?   q ~üq ~üpsq ~ ñ  wîppppq ~üq ~üppppppppppppppppq ~ ö  wî        ppq ~ ùsq ~ o   uq ~ r   sq ~ tt 
"UsuÃ¡rio:"+ sq ~ tt usuariot java.lang.Stringpppppppppt  sq ~ Í  wî           F     pq ~ q ~gppppppq ~ Ãppppq ~ Æ  wîpppppt Verdanaq ~pq ~pppppppppsq ~ åpsq ~ é  wîppq ~þsq ~Ô?   q ~q ~q ~psq ~ ì  wîppq ~þsq ~Ô?   q ~q ~psq ~ ê  wîppppq ~q ~psq ~ ï  wîppppq ~q ~psq ~ ñ  wîppppq ~q ~ppppppppppppppppq ~ ö  wî        ppq ~ ùsq ~ o   uq ~ r   sq ~ tt "PÃ¡gina "+sq ~ tt PAGE_NUMBERsq ~ tt +" de"t java.lang.Stringppppppppppsq ~ Í  wî           #  M   pq ~ q ~gppppppq ~ Ãppppq ~ Æ  wîpppppt Verdanaq ~pppppppppppsq ~ åpsq ~ é  wîppq ~þsq ~Ô?   q ~$q ~$q ~"sq ~ ß   sq ~ ì  wîppppq ~$q ~$psq ~ ê  wîppppq ~$q ~$psq ~ ï  wîppq ~þsq ~Ô?   q ~$q ~$psq ~ ñ  wîppppq ~$q ~$ppppppppppppppppq ~ ö  wî        pp~q ~ øt REPORTsq ~ o   uq ~ r   sq ~ tt PAGE_NUMBERt java.lang.Integerppppppppppsq ~ Í  wî          µ   R   8pq ~ q ~gppppppq ~ Ãppppq ~ Æ  wîpppppt Arialsq ~ ß   pq ~\pppppppppsq ~ åpsq ~ é  wîppppq ~7q ~7q ~4psq ~ ì  wîppppq ~7q ~7psq ~ ê  wîppppq ~7q ~7psq ~ ï  wîppppq ~7q ~7psq ~ ñ  wîppppq ~7q ~7ppppppppppppppppq ~ ö  wî        ppq ~ ùsq ~ o   uq ~ r   sq ~ tt filtrost java.lang.Stringppppppppppsq ~ Í  wî          µ   R   pq ~ q ~gpt textField-2ppppq ~ Ãppppq ~ Æ  wîpppppt Arialq ~Ípq ~\q ~ ãppppppppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~Eq ~Eq ~Bpsq ~ ì  wîppq ~Òsq ~Ô    q ~Eq ~Epsq ~ ê  wîppq ~Òsq ~Ô?   q ~Eq ~Epsq ~ ï  wîppq ~Òsq ~Ô    q ~Eq ~Epsq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~Eq ~Epppppt Helvetica-Boldppppppppppq ~ ö  wî        ppq ~ ùsq ~ o   uq ~ r   sq ~ tt nomeEmpresat java.lang.Stringppppppq ~ äpppsq ~ Í  wî           i      Xsq ~    ÿÿÿÿpppq ~ q ~gpt 	dataRel-1pq ~ppq ~ Ãppppq ~ Æ  wîpppppt Verdanaq ~pq ~\pq ~ äpppppppsq ~ åq ~sq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~\q ~\q ~Xpsq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~\q ~\psq ~ ê  wîppppq ~\q ~\psq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~\q ~\psq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô?   q ~\q ~\pppppt 	Helveticappppppppppq ~ ö  wî        ppq ~ ùsq ~ o   uq ~ r   sq ~ tt dataLancamentot java.util.Dateppppppq ~ äppt 
dd/MM/yyyysq ~ µ  wî          o      dpq ~ q ~gppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~qppsq ~À  wî           1      fpq ~ q ~gpt 
staticText-85pq ~ Úppq ~ Üsq ~ o   uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())q ~Ëppppq ~ Æ  wîpppppt Arialq ~Ípq ~\q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~~q ~~q ~spsq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~~q ~~psq ~ ê  wîppppq ~~q ~~psq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~~q ~~psq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~~q ~~pppppt 	Helveticappppppppppq ~ öt MATsq ~   wî              3   dpq ~ q ~gppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~p  wî q ~sq ~À  wî           ¹   4   fpq ~ q ~gpt 
staticText-85pq ~ Úppq ~ Üsq ~ o   uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())q ~Ëppppq ~ Æ  wîpppppt Arialq ~Ípq ~\q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~q ~psq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~psq ~ ê  wîppppq ~q ~psq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~psq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~pppppt 	Helveticappppppppppq ~ öt NOMEsq ~   wî              ï   dpq ~ q ~gppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~«p  wî q ~sq ~À  wî              ò   fpq ~ q ~gpt 
staticText-85pq ~ Úppq ~ Üsq ~ o   uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())q ~Ëppppq ~ Æ  wîpppppt Arialq ~Ípq ~\q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~¸q ~¸q ~­psq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~¸q ~¸psq ~ ê  wîppppq ~¸q ~¸psq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~¸q ~¸psq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~¸q ~¸pppppt 	Helveticappppppppppq ~ öt 
FORMA DE PGTOsq ~   wî             |   dpq ~ q ~gppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~Èp  wî q ~sq ~À  wî           P  }   fpq ~ q ~gpt 
staticText-85pq ~ Úppq ~ Üsq ~ o    uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())q ~Ëppppq ~ Æ  wîpppppt Arialq ~Ípq ~\q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~Õq ~Õq ~Êpsq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~Õq ~Õpsq ~ ê  wîppppq ~Õq ~Õpsq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~Õq ~Õpsq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~Õq ~Õpppppt 	Helveticappppppppppq ~ öt ENTRADAsq ~   wî             Í   dpq ~ q ~gppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~åp  wî q ~sq ~À  wî           O  Î   fpq ~ q ~gpt 
staticText-85pq ~ Úppq ~ Üsq ~ o   !uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())q ~Ëppppq ~ Æ  wîpppppt Arialq ~Ípq ~\q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~òq ~òq ~çpsq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~òq ~òpsq ~ ê  wîppppq ~òq ~òpsq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~òq ~òpsq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~òq ~òpppppt 	Helveticappppppppppq ~ öt SAÃDAsq ~   wî                dpq ~ q ~gppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~p  wî q ~sq ~À  wî           N      fpq ~ q ~gpt 
staticText-85pq ~ Úppq ~ Üsq ~ o   "uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())q ~Ëppppq ~ Æ  wîpppppt Arialq ~Ípq ~\q ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~q ~psq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~psq ~ ê  wîppppq ~q ~psq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~psq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~pppppt 	Helveticappppppppppq ~ öt USUÃRIOxp  wî   xpppt dataLancamentot CaixaPorOperadorLivroReluq ~ !   sq ~ #ppq ~ %psq ~ &pppq ~ *psq ~ #ppq ~ ,psq ~ &pppq ~ .psq ~ #ppq ~ 0psq ~ &pppq ~ 2psq ~ #ppq ~ 4psq ~ &pppq ~ 6psq ~ #ppq ~ 8psq ~ &pppq ~ :psq ~ #ppq ~ <psq ~ &pppq ~ >psq ~ #ppq ~ @psq ~ &pppq ~ Bpsq ~ #ppq ~ Dpsq ~ &pppq ~ Fpsq ~ #ppq ~ Hpsq ~ &pppq ~ Jpsq ~ #ppq ~ Lpsq ~ &pppq ~ Npsq ~ #ppq ~ Ppsq ~ &pppq ~ Rpsq ~ #ppq ~ Tpsq ~ &pppq ~ Vpsq ~ #ppq ~ Xpsq ~ &pppq ~ Zpsq ~ #ppq ~ \psq ~ &pppq ~ ^psq ~ #ppt REPORT_VIRTUALIZERpsq ~ &pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ #ppt IS_IGNORE_PAGINATIONpsq ~ &pppq ~Ëpsq ~ #  ppt tituloRelatoriopsq ~ &pppt java.lang.Stringpsq ~ #  ppt nomeEmpresapsq ~ &pppt java.lang.Stringpsq ~ #  ppt versaoSoftwarepsq ~ &pppt java.lang.Stringpsq ~ #  ppt usuariopsq ~ &pppt java.lang.Stringpsq ~ #  ppt filtrospsq ~ &pppt java.lang.Stringpsq ~ # sq ~ o    uq ~ r   sq ~ tt p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~ &pppq ~^psq ~ # sq ~ o   uq ~ r   sq ~ tt p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR1psq ~ &pppq ~fpsq ~ #  ppt dataInipsq ~ &pppt java.lang.Stringpsq ~ #  ppt dataFimpsq ~ &pppt java.lang.Stringpsq ~ #  ppt logoPadraoRelatoriopsq ~ &pppt java.io.InputStreampsq ~ # ppt 
valorGeralpsq ~ &pppt java.lang.Doublepsq ~ # sq ~ o   uq ~ r   sq ~ tt p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR2psq ~ &pppq ~~psq ~ # ppt 
totalizadorespsq ~ &pppt java.lang.Objectpsq ~ # ppt somenteSinteticopsq ~ &pppt java.lang.Booleanpsq ~ # ppt detalheSaldopsq ~ &pppt java.lang.Objectpsq ~ &psq ~ ³   w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~t 1.650000000000014q ~t 
ISO-8859-1q ~t 41q ~t 0q ~t 0xpppppuq ~ `   sq ~ b  wî   q ~ jppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6pq ~ wpq ~ yq ~ 6psq ~ b  wî   q ~ jppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6pq ~ pq ~ q ~ 6psq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pq ~ pq ~ yq ~ 6psq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pq ~ pq ~ q ~ 6psq ~ b  wî   q ~ sq ~ o   	uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   
uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pq ~ ¢pq ~ £q ~ 6pq ~sq ~ b  wî    ~q ~ ht SUMsq ~ o   
uq ~ r   sq ~ tt valorTotalPagamentot java.lang.Doubleppq ~ mppsq ~ o   uq ~ r   sq ~ tt 0.0q ~Èpt totalEntradaq ~q ~¢q ~Èpsq ~ b  wî    q ~Âsq ~ o   uq ~ r   sq ~ tt valorTotalSaidat java.lang.Doubleppq ~ mppsq ~ o   uq ~ r   sq ~ tt 0.0q ~Ópt 
totalSaidaq ~q ~¢q ~Óp~q ~ ¥t EMPTYq ~ p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ it PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ it VERTICALpsq ~ ­sq ~ ³   w   sq ~ µ  wî   ¼        ï     pq ~ q ~áppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~ãppsq ~À  wî           á     pq ~ q ~ápt 
staticText-85pq ~ Úppq ~ Üsq ~ o   1uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())q ~Ëppppq ~ Æ  wîpppppt Arialq ~Íppq ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~ðq ~ðq ~åpsq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~ðq ~ðpsq ~ ê  wîppppq ~ðq ~ðpsq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~ðq ~ðpsq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~ðq ~ðpppppt 	Helveticappppppppppq ~ öt 
OBSERVAÃÃO:sq ~À  wî           á     «pq ~ q ~ápt 
staticText-85pq ~ Úppq ~ Üsq ~ o   2uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())q ~Ëppppq ~ Æ  wîpppppt Arialq ~ áppq ~ ãq ~ äpq ~ äpq ~ äpppsq ~ åpsq ~ é  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~q ~ psq ~ ì  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~psq ~ ê  wîppppq ~q ~psq ~ ï  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~psq ~ ñ  wîsq ~    ÿfffppppq ~Òsq ~Ô    q ~q ~pppppt 	Helveticappppppppppq ~ öt ASSINATURA:sq ~   wî           î     ©pq ~ q ~áppppppq ~ Ãppppq ~ Æ  wîppsq ~ È  wîppppq ~p  wî q ~sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ Ð[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ Ðxq ~ º  wî   º       {      
pq ~ q ~áppppppq ~ Ãpq ~ppq ~ Æpsq ~ o   3uq ~ r   sq ~ tt detalheSaldoq ~ :psq ~ o   4uq ~ r   sq ~ tt 
SUBREPORT_DIRsq ~ tt / + "CaixaPorOperadorLivroRel_subreport1.jasper"t java.lang.Stringppppppxp  wî   Ëppppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ it ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ (L datasetCompileDataq ~ (L mainDatasetCompileDataq ~ xpsq ~?@     w       xsq ~?@     w      q ~  ur [B¬óøTà  xp  Êþº¾   .  3CaixaPorOperadorLivroRel_Teste_1749577262352_756387  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	    	  !  	  # 	 	  % 
 	  '  	  )  	  + 
 	  -  	  /  	  1  	  3  	  5  	  7  	  9  	  ;  	  =  	  ?  	  A  	  C LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V H I
  J 
initFields L I
  M initVars O I
  P 
REPORT_LOCALE R 
java/util/Map T get &(Ljava/lang/Object;)Ljava/lang/Object; V W U X 0net/sf/jasperreports/engine/fill/JRFillParameter Z 
JASPER_REPORT \ REPORT_TIME_ZONE ^ REPORT_FILE_RESOLVER ` REPORT_SCRIPTLET b REPORT_PARAMETERS_MAP d REPORT_CONNECTION f REPORT_CLASS_LOADER h REPORT_DATA_SOURCE j REPORT_URL_HANDLER_FACTORY l REPORT_FORMAT_FACTORY n REPORT_MAX_COUNT p REPORT_TEMPLATES r REPORT_RESOURCE_BUNDLE t PAGE_NUMBER v /net/sf/jasperreports/engine/fill/JRFillVariable x 
COLUMN_NUMBER z REPORT_COUNT | 
PAGE_COUNT ~ COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                   Ì     d*· *µ  *µ "*µ $*µ &*µ (*µ **µ ,*µ .*µ 0*µ 2*µ 4*µ 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D±    E   V       	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c   F G     4     *+· K*,· N*-· Q±    E       :  ; 
 <  =  H I    M     ý*+S¹ Y À [À [µ  *+]¹ Y À [À [µ "*+_¹ Y À [À [µ $*+a¹ Y À [À [µ &*+c¹ Y À [À [µ (*+e¹ Y À [À [µ **+g¹ Y À [À [µ ,*+i¹ Y À [À [µ .*+k¹ Y À [À [µ 0*+m¹ Y À [À [µ 2*+o¹ Y À [À [µ 4*+q¹ Y À [À [µ 6*+s¹ Y À [À [µ 8*+u¹ Y À [À [µ :±    E   >    E  F $ G 6 H H I Z J l K ~ L  M ¢ N ´ O Æ P Ø Q ê R ü S  L I           ±    E       [  O I          [*+w¹ Y À yÀ yµ <*+{¹ Y À yÀ yµ >*+}¹ Y À yÀ yµ @*+¹ Y À yÀ yµ B*+¹ Y À yÀ yµ D±    E       c  d $ e 6 f H g Z h              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    p  r 0 v 9 w < { E | H  Q  T  ]  `  i  l  u  x       ¡              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ª  ¬ 0 ° 9 ± < µ E ¶ H º Q » T ¿ ] À ` Ä i Å l É u Ê x Î  Ï  Ó  Û              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ä  æ 0 ê 9 ë < ï E ð H ô Q õ T ù ] ú ` þ i ÿ l u x 	 
       xuq ~3  -Êþº¾   ._ -CaixaPorOperadorLivroRel_1749577262352_756387  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_JASPER_REPORT 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_TIME_ZONE parameter_usuario parameter_REPORT_FILE_RESOLVER parameter_REPORT_PARAMETERS_MAP parameter_SUBREPORT_DIR1 parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR2 parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_valorGeral parameter_somenteSintetico parameter_REPORT_LOCALE parameter_dataIni parameter_REPORT_VIRTUALIZER parameter_logoPadraoRelatorio parameter_detalheSaldo parameter_REPORT_SCRIPTLET parameter_totalizadores parameter_REPORT_CONNECTION parameter_SUBREPORT_DIR parameter_dataFim parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_nomeEmpresa  parameter_REPORT_RESOURCE_BUNDLE parameter_versaoSoftware parameter_filtros field_formaPagamentoDescricao .Lnet/sf/jasperreports/engine/fill/JRFillField; field_nomePagador field_dataLancamento field_valorTotalSaida field_nomeUsuarioOperacao field_valorTotalPagamento field_matricula variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_dataLancamento_COUNT variable_totalEntrada variable_totalSaida <init> ()V Code 6 7
  9  	  ;  	  =  	  ? 	 	  A 
 	  C  	  E  	  G 
 	  I  	  K  	  M  	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g  	  i  	  k  	  m   	  o ! 	  q " 	  s # 	  u $ 	  w % &	  y ' &	  { ( &	  } ) &	   * &	   + &	   , &	   - .	   / .	   0 .	   1 .	   2 .	   3 .	   4 .	   5 .	   LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V  
   
initFields  
   initVars ¡ 
  ¢ 
JASPER_REPORT ¤ 
java/util/Map ¦ get &(Ljava/lang/Object;)Ljava/lang/Object; ¨ © § ª 0net/sf/jasperreports/engine/fill/JRFillParameter ¬ REPORT_TIME_ZONE ® usuario ° REPORT_FILE_RESOLVER ² REPORT_PARAMETERS_MAP ´ SUBREPORT_DIR1 ¶ REPORT_CLASS_LOADER ¸ REPORT_URL_HANDLER_FACTORY º REPORT_DATA_SOURCE ¼ IS_IGNORE_PAGINATION ¾ SUBREPORT_DIR2 À REPORT_MAX_COUNT Â REPORT_TEMPLATES Ä 
valorGeral Æ somenteSintetico È 
REPORT_LOCALE Ê dataIni Ì REPORT_VIRTUALIZER Î logoPadraoRelatorio Ð detalheSaldo Ò REPORT_SCRIPTLET Ô 
totalizadores Ö REPORT_CONNECTION Ø 
SUBREPORT_DIR Ú dataFim Ü REPORT_FORMAT_FACTORY Þ tituloRelatorio à nomeEmpresa â REPORT_RESOURCE_BUNDLE ä versaoSoftware æ filtros è formaPagamentoDescricao ê ,net/sf/jasperreports/engine/fill/JRFillField ì nomePagador î dataLancamento ð valorTotalSaida ò nomeUsuarioOperacao ô valorTotalPagamento ö 	matricula ø PAGE_NUMBER ú /net/sf/jasperreports/engine/fill/JRFillVariable ü 
COLUMN_NUMBER þ REPORT_COUNT  
PAGE_COUNT COLUMN_COUNT dataLancamento_COUNT totalEntrada 
totalSaida
 evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable eE:\desenvolvimentosv\Projeto Pacto Solucoes\ZillyonWeb\src\java\relatorio\designRelatorio\financeiro\ java/lang/Integer (I)V 6
 getValue ()Ljava/lang/Object;
 í java/lang/Double valueOf (D)Ljava/lang/Double;
  java/util/Date"
# 9
 ­ java/lang/String& java/io/InputStream( java/lang/StringBuffer* 	UsuÃ¡rio:, (Ljava/lang/String;)V 6.
+/ append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;12
+3 toString ()Ljava/lang/String;56
+7 PÃ¡gina 9
 ý ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;1<
+=  de? java/lang/BooleanA booleanValue ()ZCD
BE (Z)Ljava/lang/Boolean;G
BH doubleValue ()DJK
L (net/sf/jasperreports/engine/JRDataSourceN &(Ljava/lang/Object;)Ljava/lang/String;P
'Q *CaixaPorOperadorLivroRel_subreport1.jasperS evaluateOld getOldValueV
 íW
 ýW evaluateEstimated getEstimatedValue[
 ý\ 
SourceFile !     .                 	     
               
                                                                                                     !     "     #     $     % &    ' &    ( &    ) &    * &    + &    , &    - .    / .    0 .    1 .    2 .    3 .    4 .    5 .     6 7  8  ¿     ë*· :*µ <*µ >*µ @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ±       Â 0      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê      8   4     *+· *,·  *-· £±           U  V 
 W  X     8  Ã    /*+¥¹ « À ­À ­µ <*+¯¹ « À ­À ­µ >*+±¹ « À ­À ­µ @*+³¹ « À ­À ­µ B*+µ¹ « À ­À ­µ D*+·¹ « À ­À ­µ F*+¹¹ « À ­À ­µ H*+»¹ « À ­À ­µ J*+½¹ « À ­À ­µ L*+¿¹ « À ­À ­µ N*+Á¹ « À ­À ­µ P*+Ã¹ « À ­À ­µ R*+Å¹ « À ­À ­µ T*+Ç¹ « À ­À ­µ V*+É¹ « À ­À ­µ X*+Ë¹ « À ­À ­µ Z*+Í¹ « À ­À ­µ \*+Ï¹ « À ­À ­µ ^*+Ñ¹ « À ­À ­µ `*+Ó¹ « À ­À ­µ b*+Õ¹ « À ­À ­µ d*+×¹ « À ­À ­µ f*+Ù¹ « À ­À ­µ h*+Û¹ « À ­À ­µ j*+Ý¹ « À ­À ­µ l*+ß¹ « À ­À ­µ n*+á¹ « À ­À ­µ p*+ã¹ « À ­À ­µ r*+å¹ « À ­À ­µ t*+ç¹ « À ­À ­µ v*+é¹ « À ­À ­µ x±            `  a $ b 6 c H d Z e l f ~ g  h ¢ i ´ j Æ k Ø l ê m ü n o  p2 qD rV sh tz u v w° xÂ yÔ zæ {ø |
 } ~.      8   ³     *+ë¹ « À íÀ íµ z*+ï¹ « À íÀ íµ |*+ñ¹ « À íÀ íµ ~*+ó¹ « À íÀ íµ *+õ¹ « À íÀ íµ *+÷¹ « À íÀ íµ *+ù¹ « À íÀ íµ ±       "       $  6  H  Z  l  ~   ¡   8   Ï     *+û¹ « À ýÀ ýµ *+ÿ¹ « À ýÀ ýµ *+¹ « À ýÀ ýµ *+¹ « À ýÀ ýµ *+¹ « À ýÀ ýµ *+¹ « À ýÀ ýµ *+	¹ « À ýÀ ýµ *+¹ « À ýÀ ýµ ±       & 	      $  7  J  ]  p      
      8  ~    ºMª  µ       4   á   è   ï   ö        &  2  >  J  V  b  n  |        ¨  ³  Á  Ï  Ý  ë  	  -  ;  I  W  e      ¹  Õ  ñ  
  )  E  S  a    ¯  Û  ý      '  5  C  Q  m    M§ÐM§ÉM§Â»Y·M§¶»Y·M§ª»Y·M§»Y·M§»Y·M§»Y·M§z»Y·M§n»Y·M§b»Y·M§V»Y·M§J*´ ¶ÀM§<¸!M§4*´ ¶ÀM§&¸!M§*´ ~¶À#M§»#Y·$M§*´ p¶%À'M§÷*´ \¶%À'M§é*´ l¶%À'M§Û*´ `¶%À)M§Í»+Y-·0*´ @¶%À'¶4¶8M§¯»+Y:·0*´ ¶;À¶>@¶4¶8M§*´ ¶;ÀM§}*´ x¶%À'M§o*´ r¶%À'M§a*´ ~¶À#M§S*´ X¶%ÀB¶F § ¸IM§7*´ X¶%ÀB¶F § ¸IM§*´ X¶%ÀB¶F § ¸IM§ÿ*´ X¶%ÀB¶F § ¸IM§ã*´ X¶%ÀB¶F § ¸IM§Ç*´ X¶%ÀB¶F § ¸IM§«*´ X¶%ÀB¶F § ¸IM§*´ X¶%ÀB¶F § ¸IM§s*´ ¶;ÀM§e*´ ¶;ÀM§W*´ ¶;À¶M*´ ¶;À¶Mg § ¸IM§+*´ ¶;À¶M*´ ¶;À¶Mg¸!M§	*´ ¶;À¶M*´ ¶;À¶Mg § ¸IM§ Ý*´ ¶;À¶M*´ ¶;À¶Mg¸!M§ »*´ ¶À'M§ ­*´ ¶ÀM§ *´ ¶ÀM§ *´ z¶À'M§ *´ |¶À'M§ u*´ ¶À'M§ g*´ X¶%ÀB¶F § ¸IM§ K*´ X¶%ÀB¶F § ¸IM§ /*´ b¶%ÀOM§ !»+Y*´ j¶%À'¸R·0T¶4¶8M,°      ² l   ¦  ¨ ä ¬ è ­ ë ± ï ² ò ¶ ö · ù » ¼ À Á Å Æ Ê& Ë) Ï2 Ð5 Ô> ÕA ÙJ ÚM ÞV ßY ãb äe èn éq í| î ò ó ÷ ø ü ý¨«³¶ÁÄÏÒÝàëî	 $-%0);*>.I/L3W4Z8e9h=>BC G¹H¼LÕMØQñRôV
W[)\,`EaHeSfVjakdopt¯u²yÛzÞ~ý '*58CFQT¡m¢p¦§«¬°¸¸ U
      8  ~    ºMª  µ       4   á   è   ï   ö        &  2  >  J  V  b  n  |        ¨  ³  Á  Ï  Ý  ë  	  -  ;  I  W  e      ¹  Õ  ñ  
  )  E  S  a    ¯  Û  ý      '  5  C  Q  m    M§ÐM§ÉM§Â»Y·M§¶»Y·M§ª»Y·M§»Y·M§»Y·M§»Y·M§z»Y·M§n»Y·M§b»Y·M§V»Y·M§J*´ ¶XÀM§<¸!M§4*´ ¶XÀM§&¸!M§*´ ~¶XÀ#M§»#Y·$M§*´ p¶%À'M§÷*´ \¶%À'M§é*´ l¶%À'M§Û*´ `¶%À)M§Í»+Y-·0*´ @¶%À'¶4¶8M§¯»+Y:·0*´ ¶YÀ¶>@¶4¶8M§*´ ¶YÀM§}*´ x¶%À'M§o*´ r¶%À'M§a*´ ~¶XÀ#M§S*´ X¶%ÀB¶F § ¸IM§7*´ X¶%ÀB¶F § ¸IM§*´ X¶%ÀB¶F § ¸IM§ÿ*´ X¶%ÀB¶F § ¸IM§ã*´ X¶%ÀB¶F § ¸IM§Ç*´ X¶%ÀB¶F § ¸IM§«*´ X¶%ÀB¶F § ¸IM§*´ X¶%ÀB¶F § ¸IM§s*´ ¶YÀM§e*´ ¶YÀM§W*´ ¶YÀ¶M*´ ¶YÀ¶Mg § ¸IM§+*´ ¶YÀ¶M*´ ¶YÀ¶Mg¸!M§	*´ ¶YÀ¶M*´ ¶YÀ¶Mg § ¸IM§ Ý*´ ¶YÀ¶M*´ ¶YÀ¶Mg¸!M§ »*´ ¶XÀ'M§ ­*´ ¶XÀM§ *´ ¶XÀM§ *´ z¶XÀ'M§ *´ |¶XÀ'M§ u*´ ¶XÀ'M§ g*´ X¶%ÀB¶F § ¸IM§ K*´ X¶%ÀB¶F § ¸IM§ /*´ b¶%ÀOM§ !»+Y*´ j¶%À'¸R·0T¶4¶8M,°      ² l  Á Ã äÇ èÈ ëÌ ïÍ òÑ öÒ ùÖ×ÛÜàáå&æ)ê2ë5ï>ðAôJõMùVúYþbÿenq|	
¨«!³"¶&Á'Ä+Ï,Ò0Ý1à5ë6î:	;?-@0D;E>IIJLNWOZSeThXY]^ b¹c¼gÕhØlñmôq
rv)w,{E|HSVad¯²ÛÞý £¤¨'©*­5®8²C³F·Q¸T¼m½pÁÂÆÇË¸Ó Z
      8  ~    ºMª  µ       4   á   è   ï   ö        &  2  >  J  V  b  n  |        ¨  ³  Á  Ï  Ý  ë  	  -  ;  I  W  e      ¹  Õ  ñ  
  )  E  S  a    ¯  Û  ý      '  5  C  Q  m    M§ÐM§ÉM§Â»Y·M§¶»Y·M§ª»Y·M§»Y·M§»Y·M§»Y·M§z»Y·M§n»Y·M§b»Y·M§V»Y·M§J*´ ¶ÀM§<¸!M§4*´ ¶ÀM§&¸!M§*´ ~¶À#M§»#Y·$M§*´ p¶%À'M§÷*´ \¶%À'M§é*´ l¶%À'M§Û*´ `¶%À)M§Í»+Y-·0*´ @¶%À'¶4¶8M§¯»+Y:·0*´ ¶]À¶>@¶4¶8M§*´ ¶]ÀM§}*´ x¶%À'M§o*´ r¶%À'M§a*´ ~¶À#M§S*´ X¶%ÀB¶F § ¸IM§7*´ X¶%ÀB¶F § ¸IM§*´ X¶%ÀB¶F § ¸IM§ÿ*´ X¶%ÀB¶F § ¸IM§ã*´ X¶%ÀB¶F § ¸IM§Ç*´ X¶%ÀB¶F § ¸IM§«*´ X¶%ÀB¶F § ¸IM§*´ X¶%ÀB¶F § ¸IM§s*´ ¶]ÀM§e*´ ¶]ÀM§W*´ ¶]À¶M*´ ¶]À¶Mg § ¸IM§+*´ ¶]À¶M*´ ¶]À¶Mg¸!M§	*´ ¶]À¶M*´ ¶]À¶Mg § ¸IM§ Ý*´ ¶]À¶M*´ ¶]À¶Mg¸!M§ »*´ ¶À'M§ ­*´ ¶ÀM§ *´ ¶ÀM§ *´ z¶À'M§ *´ |¶À'M§ u*´ ¶À'M§ g*´ X¶%ÀB¶F § ¸IM§ K*´ X¶%ÀB¶F § ¸IM§ /*´ b¶%ÀOM§ !»+Y*´ j¶%À'¸R·0T¶4¶8M,°      ² l  Ü Þ äâ èã ëç ïè òì öí ùñòö÷ûü &)25
>AJMVYbenq#|$()-.237¨8«<³=¶AÁBÄFÏGÒKÝLàPëQîU	VZ-[0_;`>dIeLiWjZneohstxy }¹~¼ÕØñô
),EHSV a¡d¥¦ª¯«²¯Û°Þ´ýµ ¹º¾¿Ã'Ä*È5É8ÍCÎFÒQÓT×mØpÜÝáâæ¸î ^    t _1749577262352_756387t 2net.sf.jasperreports.engine.design.JRJavacCompiler