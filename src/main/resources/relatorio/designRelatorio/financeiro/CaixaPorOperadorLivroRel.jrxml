<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CaixaPorOperadorLivroRel" pageWidth="680" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="625" leftMargin="20" rightMargin="35" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.650000000000014"/>
	<property name="ireport.x" value="41"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="valorGeral" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="totalizadores" class="java.lang.Object"/>
	<parameter name="somenteSintetico" class="java.lang.Boolean"/>
	<parameter name="detalheSaldo" class="java.lang.Object"/>
	<field name="matricula" class="java.lang.String"/>
	<field name="nomePagador" class="java.lang.String"/>
	<field name="formaPagamentoDescricao" class="java.lang.String"/>
	<field name="valorTotalPagamento" class="java.lang.Double"/>
	<field name="nomeUsuarioOperacao" class="java.lang.String"/>
	<field name="valorTotalSaida" class="java.lang.Double"/>
	<field name="dataLancamento" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<variable name="totalEntrada" class="java.lang.Double" resetType="Group" resetGroup="dataLancamento" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalPagamento}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<variable name="totalSaida" class="java.lang.Double" resetType="Group" resetGroup="dataLancamento" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalSaida}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<group name="dataLancamento" isStartNewPage="true">
		<groupExpression><![CDATA[$F{dataLancamento}]]></groupExpression>
		<groupHeader>
			<band height="120">
				<staticText>
					<reportElement key="staticText-82" mode="Opaque" x="305" y="39" width="26" height="17"/>
					<box>
						<pen lineWidth="0.0" lineColor="#666666"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineColor="#666666"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[a]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
					<reportElement key="dataRel-1" mode="Transparent" x="519" y="1" width="105" height="12" backcolor="#FFFFFF"/>
					<box bottomPadding="0">
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-2" x="82" y="20" width="437" height="19"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-215" x="82" y="39" width="223" height="17"/>
					<box>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{dataIni}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-216" x="331" y="39" width="188" height="17"/>
					<box>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.5" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{dataFim}]]></textFieldExpression>
				</textField>
				<image vAlign="Top" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
					<reportElement key="image-1" positionType="Float" x="0" y="1" width="82" height="38" isPrintWhenDetailOverflows="true"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
				</image>
				<textField pattern="">
					<reportElement mode="Transparent" x="519" y="13" width="105" height="12"/>
					<box>
						<leftPen lineWidth="0.5" lineStyle="Dotted"/>
						<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
						<rightPen lineWidth="0.5" lineStyle="Dotted"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Verdana" size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["Usuário:"+ $P{usuario}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="519" y="25" width="70" height="14"/>
					<box>
						<leftPen lineWidth="0.5" lineStyle="Dotted"/>
						<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["Página "+$V{PAGE_NUMBER}+" de"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="589" y="25" width="35" height="14"/>
					<box leftPadding="4">
						<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
						<rightPen lineWidth="0.5" lineStyle="Dotted"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Verdana" size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="82" y="56" width="437" height="15"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="12"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-2" x="82" y="1" width="437" height="19"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="false">
					<reportElement key="dataRel-1" mode="Transparent" x="1" y="88" width="105" height="12" backcolor="#FFFFFF"/>
					<box bottomPadding="0">
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[$F{dataLancamento}]]></textFieldExpression>
				</textField>
				<rectangle>
					<reportElement x="1" y="100" width="623" height="20"/>
				</rectangle>
				<staticText>
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="2" y="102" width="49" height="17">
						<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[MAT]]></text>
				</staticText>
				<line>
					<reportElement x="51" y="100" width="1" height="20"/>
				</line>
				<staticText>
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="52" y="102" width="185" height="17">
						<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[NOME]]></text>
				</staticText>
				<line>
					<reportElement x="239" y="100" width="1" height="20"/>
				</line>
				<staticText>
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="242" y="102" width="138" height="17">
						<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[FORMA DE PGTO]]></text>
				</staticText>
				<line>
					<reportElement x="380" y="100" width="1" height="20"/>
				</line>
				<staticText>
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="381" y="102" width="80" height="17">
						<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[ENTRADA]]></text>
				</staticText>
				<line>
					<reportElement x="461" y="100" width="1" height="19"/>
				</line>
				<staticText>
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="462" y="102" width="79" height="17">
						<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[SAÍDA]]></text>
				</staticText>
				<line>
					<reportElement x="542" y="100" width="1" height="20"/>
				</line>
				<staticText>
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="544" y="102" width="78" height="17">
						<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[USUÁRIO]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="42">
				<rectangle>
					<reportElement x="1" y="-1" width="623" height="20"/>
				</rectangle>
				<rectangle>
					<reportElement x="239" y="19" width="304" height="20"/>
				</rectangle>
				<line>
					<reportElement x="542" y="-1" width="1" height="20"/>
				</line>
				<line>
					<reportElement x="380" y="-1" width="1" height="20"/>
				</line>
				<line>
					<reportElement x="461" y="-1" width="1" height="20"/>
				</line>
				<line>
					<reportElement x="239" y="-1" width="1" height="20"/>
				</line>
				<line>
					<reportElement x="51" y="-1" width="1" height="20"/>
				</line>
				<line>
					<reportElement x="380" y="19" width="1" height="20"/>
				</line>
				<staticText>
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="242" y="20" width="135" height="18">
						<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[RESULTADO]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="242" y="0" width="135" height="18">
						<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[TOTAL]]></text>
				</staticText>
				<textField pattern="¤ #,##0.00">
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="385" y="2" width="74" height="14" forecolor="#339900"/>
					<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
						<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{totalEntrada}]]></textFieldExpression>
				</textField>
				<textField pattern="¤ #,##0.00">
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="464" y="2" width="76" height="14" forecolor="#FF0000"/>
					<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
						<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{totalSaida}]]></textFieldExpression>
				</textField>
				<textField pattern="¤ #,##0.00">
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="385" y="20" width="155" height="18" forecolor="#FF0000">
						<printWhenExpression><![CDATA[($V{totalEntrada} - $V{totalSaida}) < 0.0]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
						<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{totalEntrada} - $V{totalSaida}]]></textFieldExpression>
				</textField>
				<textField pattern="¤ #,##0.00">
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="385" y="20" width="155" height="18" forecolor="#0033FF">
						<printWhenExpression><![CDATA[($V{totalEntrada} - $V{totalSaida}) > 0.0]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
						<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{totalEntrada} - $V{totalSaida}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<detail>
		<band height="20">
			<rectangle>
				<reportElement x="1" y="-1" width="623" height="20"/>
			</rectangle>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="546" y="0" width="76" height="18"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeUsuarioOperacao}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="542" y="-1" width="1" height="20"/>
			</line>
			<textField pattern="¤ #,##0.00">
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="464" y="0" width="76" height="18" forecolor="#FF0000"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalSaida}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="461" y="-1" width="1" height="20"/>
			</line>
			<textField pattern="¤ #,##0.00">
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="381" y="0" width="78" height="18" forecolor="#339900"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalPagamento}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="380" y="-1" width="1" height="20"/>
			</line>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="242" y="0" width="135" height="18"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{formaPagamentoDescricao}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="239" y="-1" width="1" height="20"/>
			</line>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="54" y="0" width="184" height="18"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePagador}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="51" y="-1" width="1" height="20"/>
			</line>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="3" y="0" width="45" height="18"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="203">
			<rectangle>
				<reportElement x="387" y="11" width="239" height="188"/>
			</rectangle>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="390" y="12" width="225" height="22">
					<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[OBSERVAÇÃO:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="390" y="171" width="225" height="22">
					<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ASSINATURA:]]></text>
			</staticText>
			<line>
				<reportElement x="387" y="169" width="238" height="1"/>
			</line>
			<subreport>
				<reportElement x="1" y="10" width="379" height="186" printWhenGroupChanges="dataLancamento"/>
				<dataSourceExpression><![CDATA[$P{detalheSaldo}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "CaixaPorOperadorLivroRel_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</summary>
</jasperReport>
