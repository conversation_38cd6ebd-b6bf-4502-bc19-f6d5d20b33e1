<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReciboRel" pageWidth="200" pageHeight="500" whenNoDataType="AllSectionsNoDetail" columnWidth="200" leftMargin="0" rightMargin="0" topMargin="5" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="4.2871776200000165"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="654"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorGeral" class="java.lang.Double"/>
	<parameter name="totalizadores" class="java.lang.Object"/>
	<parameter name="somenteSintetico" class="java.lang.Boolean"/>
	<parameter name="detalheSaldo" class="java.lang.Object"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<parameter name="identificador" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<field name="matricula" class="java.lang.String"/>
	<field name="nomePagador" class="java.lang.String"/>
	<field name="nomeUsuarioOperacao" class="java.lang.String"/>
	<field name="formaPagamentoDescricao" class="java.lang.String"/>
	<field name="valorTotalPagamento" class="java.lang.Double"/>
	<field name="valorTotalSaida" class="java.lang.Double"/>
	<field name="dataLancamento" class="java.util.Date"/>
	<variable name="totalEntrada" class="java.lang.Double" resetType="Group" resetGroup="nomeUsuarioOperacao" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalPagamento}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<variable name="totalSaida" class="java.lang.Double" resetType="Group" resetGroup="nomeUsuarioOperacao" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalSaida}]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<group name="nomeUsuarioOperacao">
		<groupExpression><![CDATA[$F{nomeUsuarioOperacao}]]></groupExpression>
		<groupHeader>
			<band height="40">
				<textField>
					<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="158" y="30" width="39" height="10"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
						<font fontName="SansSerif" size="5" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["SAÍDA"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="119" y="30" width="39" height="10"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
						<font fontName="SansSerif" size="5" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["ENTRADA"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="80" y="30" width="39" height="10"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font fontName="SansSerif" size="5" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["FM. PGTO"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="30" y="30" width="50" height="10"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font fontName="SansSerif" size="5" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["NOME"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="3" y="30" width="27" height="10"/>
					<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font fontName="SansSerif" size="5" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["MAT"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="3" y="7" width="194" height="23"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["Consultor: " + $F{nomeUsuarioOperacao}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="15">
				<textField>
					<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="3" y="0" width="116" height="14"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
						<font fontName="SansSerif" size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["TOTAL"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-231" mode="Opaque" x="119" y="0" width="39" height="14"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="5" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[java.text.NumberFormat.getCurrencyInstance(new java.util.Locale("pt", "BR")).format($V{totalEntrada})]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-231" mode="Opaque" x="158" y="0" width="39" height="14"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="5" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[java.text.NumberFormat.getCurrencyInstance(new java.util.Locale("pt", "BR")).format($V{totalSaida})]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<pageHeader>
		<band height="54">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
			<textField>
				<reportElement x="48" y="0" width="149" height="38" forecolor="#000000"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="3" y="38" width="116" height="12"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Unidade: " + $P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="119" y="38" width="78" height="12"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataIni} + " a " + $P{dataFim}]]></textFieldExpression>
			</textField>
			<image vAlign="Top" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" x="3" y="0" width="45" height="38" isPrintWhenDetailOverflows="true"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
		</band>
	</pageHeader>
	<detail>
		<band height="20">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-231" mode="Opaque" x="158" y="0" width="39" height="20"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="5" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalSaida} == null || $F{valorTotalSaida} == 0
                        ? "R$ 0,00"
                        : java.text.NumberFormat.getCurrencyInstance(new java.util.Locale("pt", "BR")).format($F{valorTotalSaida})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-231" mode="Opaque" x="119" y="0" width="39" height="20"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="5" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalPagamento} == null || $F{valorTotalPagamento} == 0
                        ? "R$ 0,00"
                        : java.text.NumberFormat.getCurrencyInstance(new java.util.Locale("pt", "BR")).format($F{valorTotalPagamento})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-231" mode="Opaque" x="80" y="0" width="39" height="20"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="5" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{formaPagamentoDescricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-231" mode="Opaque" x="30" y="0" width="50" height="20"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="5" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePagador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-228" mode="Opaque" x="3" y="0" width="27" height="20"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="5" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="131">
			<textField>
				<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="34" y="63" width="140" height="10"/>
				<box topPadding="1" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Ass. Responsável"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="34" y="95" width="140" height="10"/>
				<box topPadding="1" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Ass. Consultor"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="3" y="4" width="194" height="30"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" markup="none">
					<font fontName="SansSerif" size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Observação: "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="53" y="116" width="105" height="12" backcolor="#FFFFFF"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
