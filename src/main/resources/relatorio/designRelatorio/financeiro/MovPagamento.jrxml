<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MovPagamento" pageWidth="615" pageHeight="802" whenNoDataType="AllSectionsNoDetail" columnWidth="615" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.5026296018031564"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="detalharPagamentos" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="dataPagamento" class="java.util.Date"/>
	<field name="valor" class="java.lang.Double"/>
	<field name="formaPagamento.tipoFormaPagamento" class="java.lang.String"/>
	<field name="listaCheque" class="java.lang.Object"/>
	<field name="nrParcelaCartaoCredito" class="java.lang.Integer"/>
	<field name="formaPagamento.descricao" class="java.lang.String"/>
	<field name="operadoraCartaoVO.descricao" class="java.lang.String"/>
	<field name="dataAlteracaoManual_Apresentar" class="java.lang.String"/>
	<field name="observacao" class="java.lang.String"/>
	<field name="creditoApresentar" class="java.lang.String"/>
	<field name="valorTotal" class="java.lang.Double"/>
	<field name="autorizacaoCartao" class="java.lang.String"/>
	<field name="nsu" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="38" splitType="Prevent">
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-5" mode="Transparent" x="62" y="0" width="58" height="14"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotal}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-6" mode="Transparent" x="120" y="0" width="178" height="14"/>
				<box leftPadding="5">
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{formaPagamento.tipoFormaPagamento} + " - " + $F{formaPagamento.descricao}]]></textFieldExpression>
			</textField>
			<subreport isUsingCache="true">
				<reportElement key="subreport-1" x="22" y="26" width="620" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPagamentos} && ( $F{formaPagamento.tipoFormaPagamento}.equals("CH")? new Boolean(true) : new Boolean(false) )]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ListaCheque">
					<subreportParameterExpression><![CDATA[$F{listaCheque}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaCheque}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovPagamento_cheques.jasper"]]></subreportExpression>
			</subreport>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-7" positionType="Float" mode="Transparent" x="304" y="0" width="45" height="14">
					<printWhenExpression><![CDATA[( $F{formaPagamento.tipoFormaPagamento}.equals("CA")? new Boolean(true) : new Boolean(false) )]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nrParcelaCartaoCredito}+" Vezes"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-7" positionType="Float" mode="Transparent" x="353" y="0" width="128" height="26">
					<printWhenExpression><![CDATA[( !$F{operadoraCartaoVO.descricao}.trim().isEmpty())]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{operadoraCartaoVO.descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="14" width="60" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{dataAlteracaoManual_Apresentar}.equals("")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Dt_alteracao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="62" y="14" width="47" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataAlteracaoManual_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-8" x="3" y="0" width="26" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{creditoApresentar}.trim()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-7" positionType="Float" mode="Transparent" x="481" y="0" width="136" height="14">
					<printWhenExpression><![CDATA[( !$F{autorizacaoCartao}.trim().isEmpty())]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Autorizacao} + " " +
$F{autorizacaoCartao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-7" positionType="Float" mode="Transparent" x="497" y="14" width="134" height="12">
					<printWhenExpression><![CDATA[( !$F{nsu}.trim().isEmpty())]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["NSU: "+$F{nsu}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="30" y="0" width="32" height="14" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="12" splitType="Stretch">
			<printWhenExpression><![CDATA[!$F{observacao}.equals("")]]></printWhenExpression>
			<textField>
				<reportElement x="0" y="0" width="22" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{observacao}.equals("")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Obs} + ".:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement key="textField-6" mode="Opaque" x="22" y="1" width="513" height="11">
					<printWhenExpression><![CDATA[!$F{observacao}.trim().equals("")]]></printWhenExpression>
				</reportElement>
				<box leftPadding="5">
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{observacao}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
