<%@page contentType="text/html;charset=UTF-8" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
</head>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="rick" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
    .linkscriptemail{
        width: 100%;
        position: relative;
        padding: 10px;
        border: 1px solid #ddd!important;
        background-color: #fff!important;
        border-radius: 3px;
        color: #b4b4b4!important;
        margin-bottom: 20px;
    }
</style>
<script>


    function mascarahora(field, sMask, evtKeyPress) {
        var i, nCount, sValue, fldLen, mskLen, bolMask, sCod, nTecla;

        var nTecla = (evtKeyPress.charCode || evtKeyPress.keyCode || evtKeyPress.which);
        if (evtKeyPress.keyCode != 0
            && ((nTecla == 8) || (nTecla == 9) || (nTecla == 18) || (nTecla == 27) || (nTecla == 33) || (nTecla == 34) || (nTecla == 35) || (nTecla == 36)
            || (nTecla == 37) || (nTecla == 38) || (nTecla == 39) || (nTecla == 40) || (nTecla == 45) || (nTecla == 46))) {
            return true;
        }

        sValue = field.value;

        // Limpa todos os caracteres de formatação que já estiverem no campo.
        sValue = sValue.toString().replace(/[_\W]/g, "" );
        fldLen = sValue.length;
        mskLen = sMask.length;


        i = 0;
        nCount = 0;
        sCod = "";
        mskLen = fldLen;

        while (i <= mskLen) {
            bolMask = sMask.charAt(i).search(/[_\W]/) >= 0;

            if (bolMask) {
                sCod += sMask.charAt(i);
                mskLen++;
            } else {
                sCod += sValue.charAt(nCount);
                nCount++;
            }

            i++;
        }
        if (sMask.length == sCod.length) {
            return false;
        }

        field.value = sCod;

        if (sMask.charAt(i-1) == "9") { // apenas números...
            return ((nTecla > 47) && (nTecla < 58));
        } // números de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    }
</script>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>${msg_aplic.prt_ConfiguracaoSistemaCRM_tituloForm}</title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConfiguracaoSistemaCRM_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}configuracoes-da-engrenagem-do-modulo-crm/"/>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_crm.jsp"/>
        </f:facet>
    </h:panelGroup>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <h:commandLink action="#{ConfiguracaoSistemaCRMControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" width="100%">

                <rich:tabPanel width="100%" activeTabClass="true" >
                    <rich:tab id="dadosBasico" label="Dados Básicos" switchType="Client">
                        <h:panelGrid columns="1" width="100%">
                            <h:panelGroup>
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_abertoSabado}" />
                                <rich:spacer width="10px"/>
                                <h:selectBooleanCheckbox id="abertoSabado" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.abertoSabado}"/>
                                <rich:spacer width="50px"/>
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_abertoDomingo}" />
                                <rich:spacer width="10px"/>
                                <h:selectBooleanCheckbox id="abertoDomingo" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.abertoDomingo}"/>
                            </h:panelGroup>

                            <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="Geral"/>
                                </f:facet>
                            </h:panelGrid>
                            <h:panelGrid columns="2" width="100%"
                                         columnClasses="colunaEsquerdaConfiguracoes, colunaDireitaConfiguracoes">
                                <h:outputText styleClass="tituloCampos" value="Toda ação irá contabilizar meta"/>
                                <h:selectBooleanCheckbox id="baterMetaTodasAcoes" styleClass="campos"
                                                         value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes}"/>

                                <h:outputText styleClass="tituloCampos" value="Filtro de colaboradores por tipo de colaborador"/>
                                <h:panelGroup layout="block">
                                    <h:selectBooleanCheckbox id="apresentarColaboradoresPorTipoColaborador" styleClass="campos"
                                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.apresentarColaboradoresPorTipoColaborador}">
                                        <a4j:support event="onclick" reRender="form"/>
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="Exibir usuários inativos caso a configuração \"Filtro de colaboradores por tipo de colaborador\" esteja habilitada"
                                              rendered="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.apresentarColaboradoresPorTipoColaborador}"/>
                                <h:panelGroup layout="block"
                                              rendered="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.apresentarColaboradoresPorTipoColaborador}">
                                    <h:selectBooleanCheckbox id="apresentarColaboradoresInativos" styleClass="campos"
                                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.apresentarColaboradoresInativos}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Agendamentos devem aparecer na meta do consultor vinculado ao aluno"/>
                                <h:panelGroup layout="block">
                                    <h:selectBooleanCheckbox id="agendamentoParaMetaConsultor" styleClass="campos"
                                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.agendamentoParaMetaConsultor}"/>

                                    <rich:toolTip direction="bottom-left"
                                                  for="agendamentoParaMetaConsultor"
                                                  value="Ao desmarcar essa configuração os agendamentos avulsos irão aparecer na meta do usuário que realizou a operação"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="Contrato autorrenovável entra na fase renovação"/>
                                <h:panelGroup layout="block">
                                    <h:selectBooleanCheckbox id="autoRenovavelRenovacao" styleClass="campos"
                                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.autorrenovavelEntraRenovacao}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="Gerar indicação para cadastros feitos por meio do link de convite no Vendas Online"/>
                                <h:panelGroup layout="block">
                                    <h:selectBooleanCheckbox id="gerarIndicacaoParaCadastroConvidadosVendasOnline" styleClass="campos"
                                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.gerarIndicacaoParaCadastroConvidadosVendasOnline}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="Direcionar agendamento de aula experimental para a agenda"/>
                                <h:panelGroup layout="block">
                                    <h:selectBooleanCheckbox id="direcionaragendamentosexperimentaisagenda" styleClass="campos"
                                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="Quantidade de dias para ser uma conversão"/>
                                <h:inputText id="nrDiasContarResultado" size="10" maxlength="10" styleClass="campos"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrDiasContarResultado}"/>

                                <h:outputText  styleClass="tituloCampos" value="Número de dias para que o sistema identifique clientes que tenham contratos previstos para renovar. \"Contrato mensal\":" />
                                <h:inputText  id="nrrenovar" size="10" maxlength="10" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrDiasParaClientePreveRenovacao}" />

                                <h:outputText  styleClass="tituloCampos" value="Número de dias para que o sistema identifique clientes que tenham contratos previstos para renovar. \"Contrato maior que um mês\":" />
                                <h:inputText  id="nrrenovarmaior" size="10" maxlength="10" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrDiasParaClientePreveRenovacaoMaiorUmMes}" />

                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_nrDiasParaClientePrevePerda}" />
                                <h:inputText  id="nrPerda" size="10" maxlength="10" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrDiasParaClientePrevePerda}" />

                                <h:outputText styleClass="tituloCampos"
                                              value="Quantidade de créditos para que o aluno entre na fase renovação"/>
                                <h:inputText id="nrCreditosTreinoRenovar" size="10" maxlength="10" styleClass="campos"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrCreditosTreinoRenovar}"/>
                                <rich:toolTip  direction="bottom-left"
                                               for="nrCreditosTreinoRenovar"
                                               style="white-space: pre-line;"
                                               value="Informe a quantidade de créditos para que o aluno entre na fase de renovação:
                                               &#13;Ex: se definido '7' neste campo, quando o aluno tiver exatamente 7 créditos ou menos, então ele entrará na fase de renovação.
                                               &#13;Obs: Este aluno será mostrado na fase renovação juntamente com os alunos que possuem contrato normal (não crédito),
                                               &#13;porém será diferenciado dos demais com uma flag 'Contrato de crédito'"/>
                            </h:panelGrid>

                            <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                         columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_tituloNumeroDeFaltas}"/>
                                </f:facet>
                            </h:panelGrid>
                            <h:panelGrid columns="2" width="100%"  columnClasses="colunaEsquerdaConfiguracoes, colunaDireitaConfiguracoes">
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_nrFaltaPlanoMensal}" />
                                <h:inputText  id="nrFaltaPlanoMensal" size="10" maxlength="10" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrFaltaPlanoMensal}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_nrFaltaPlanoTrimestral}" />
                                <h:inputText  id="nrFaltaPlanoTrimestral" size="10" maxlength="10" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrFaltaPlanoTrimestral}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_nrFaltaPlanoAcimaSemestral}" />
                                <h:inputText  id="nrFaltaPlanoAcimaSemestral" size="10" maxlength="10" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrFaltaPlanoAcimaSemestral}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_nrRisco}" />
                                <h:inputText  id="nrRisco" size="10" maxlength="10" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrRisco}" />

                            </h:panelGrid>

                            <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_tituloContratosEspontaneos}"/>
                                </f:facet>
                            </h:panelGrid>
                            <h:panelGrid columns="2" width="100%"
                                         columnClasses="colunaEsquerdaConfiguracoes, colunaDireitaConfiguracoes">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_antecipacaoAgendado}"/>
                                <h:inputText id="nrDiasAnterioresAgendamento" size="10" maxlength="10"
                                             styleClass="campos"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrDiasAnterioresAgendamento}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_vencidoAgendado}"/>
                                <h:inputText id="nrDiasPosterioresAgendamento" size="10" maxlength="10"
                                             styleClass="campos"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrDiasPosterioresAgendamento}"/>
                            </h:panelGrid>

                            <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_tituloAgendamentos}"/>
                                </f:facet>
                            </h:panelGrid>
                            <h:panelGrid columns="2" width="100%"
                                         columnClasses="colunaEsquerdaConfiguracoes, colunaDireitaConfiguracoes">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_tituloDataLimiteAgendamento}"/>
                                <h:inputText id="nrDiasLimiteAgendamentoFuturo" size="10" maxlength="10" styleClass="campos"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.nrDiasLimiteAgendamentoFuturo}"/>
                            </h:panelGrid>

                            <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_tituloMetas}"/>
                                </f:facet>
                            </h:panelGrid>
                            <h:panelGrid columns="2" width="100%"
                                         columnClasses="colunaEsquerdaConfiguracoes, colunaDireitaConfiguracoes">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_tituloMetasIndicacao}"/>
                                <h:inputText id="qtdIndicacoesMes" size="10" maxlength="10" styleClass="campos"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.indicacoesMes}"/>
                            </h:panelGrid>

                            <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="Carteiras"/>
                                </f:facet>
                            </h:panelGrid>
                            <h:panelGrid columns="2" width="100%"
                                         columnClasses="colunaEsquerdaConfiguracoes, colunaDireitaConfiguracoes">
                                    <h:outputText styleClass="tituloCampos"
                                                  value="Em \"Clientes ativos sem Professor\" desconsiderar clientes com vínculo com Professor do TreinoWeb"/>
                                    <h:selectBooleanCheckbox id="professorTW" styleClass="campos"
                                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.considerarProfessorTreinoWeb}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="configuracaoEmail" label="E-mail">

                        <h:panelGrid columns="2" width="100%" columnClasses="w40,w60">
                            <h:outputText styleClass="tituloCampos" value="Enviar e-mails via Wagi" />
                            <h:selectBooleanCheckbox
                                    styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.integracaoPacto}">
                                <a4j:support event="onclick" reRender="form"/>
                            </h:selectBooleanCheckbox>

                            <h:outputText styleClass="tituloCampos" value="Limite diário de e-mails"/>
                            <h:inputText size="20" maxlength="200" styleClass="campos"
                                         value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.limiteDiarioEmails}" />

                        </h:panelGrid>

                        <h:panelGrid columns="2" width="100%" columnClasses="w40,w60" rendered="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.integracaoPacto}">
                            <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_ConfiguracaoSistemaCRM_remetentePadrao}" />

                            <h:panelGroup id="panelMensagem">

                                <h:inputText id="remetentePadraoSendy" size="50" maxlength="50" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.remetentePadrao}" />
                                <rich:suggestionbox id="suggestionRemetentePadraoMailingsendy" height="200" width="300"
                                                    for="remetentePadraoSendy" status="statusInComponent" immediate="true"
                                                    suggestionAction="#{ConfiguracaoSistemaCRMControle.autoCompleteRemetentePadraoMailing}"
                                                    nothingLabel="A busca não obteve resultados!" var="result">

                                    <a4j:support event="onselect"
                                                 action="#{ConfiguracaoSistemaCRMControle.selecionarRemetentePadrao}"
                                                 reRender="panelMensagem"/>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Nome" styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText styleClass="textverysmall" value="#{result.nome}"/>
                                    </h:column>
                                </rich:suggestionbox>
                                <a4j:commandButton id="removerRemetentesendy" alt="Remove Remetente"
                                                   action="#{ConfiguracaoSistemaCRMControle.removerRemetentePadraoMailing}" reRender="panelMensagem"
                                                   image="./imagens/limpar.gif" />
                            </h:panelGroup>


                            <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_ConfiguracaoSistemaCRM_emailPadrao}" />
                            <h:inputText id="emailPadraoSendy" size="50" maxlength="80" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.emailPadrao}" />

                            <h:outputText styleClass="tituloCampos" value="* E-mail para teste" />
                            <h:inputText id="emailTesteSendy" size="50" maxlength="50" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.emailTeste}"/>

                            <div style="text-align: center; margin-top: 20px">
                                <a4j:commandButton id="configurarEmailSendy" action="#{ConfiguracaoSistemaCRMControle.configurarEmail}"
                                                   reRender="form" styleClass="botoes nvoBt btSec"
                                                   oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}"
                                                   value="Configurar E-mail" accesskey="5" />
                                <rich:toolTip for="configurarEmailSendy">
                                    <span>Para configurar o email é necessário preencher um email para teste.</span>
                                </rich:toolTip>
                            </div>
                        </h:panelGrid>

                        <rich:simpleTogglePanel id="painelHealthCheck" switchType="client" label="Healt-Check Serviço Wagi"
                                                width="100%" opened="false"
                                                rendered="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.integracaoPacto and ConfiguracaoSistemaCRMControle.permiteHealthCheck}">
                            <h:panelGrid columns="2" width="100%" columnClasses="w40,w60">
                                <rick:spacer />
                                <a4j:commandButton id="healtsCheckSendy"
                                                   action="#{ConfiguracaoSistemaCRMControle.healtsCheckSendy}"
                                                   reRender="healthCheck" styleClass="botoes nvoBt"
                                                   value="Processar Health-Check"/>
                                <rick:spacer />
                                <h:inputTextarea readonly="true" cols="65" rows="12" styleClass="" id="healthCheck"
                                                 style="border: 0px"
                                                 value="#{ConfiguracaoSistemaCRMControle.jsonHealthCheckSendy}"/>
                            </h:panelGrid>
                        </rich:simpleTogglePanel>

                        <h:panelGrid columns="2"  width="100%"
                                     columnClasses="w40,w60"
                                     rendered="#{!ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.integracaoPacto}">
                            <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_ConfiguracaoSistemaCRM_login}" />
                            <h:inputText id="login" size="50" maxlength="80" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.login}" />
                            <h:panelGroup id="senha-token" styleClass="alinhamentoLabels">
                                <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_ConfiguracaoSistemaCRM_senha}" />
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                <rich:toolTip for="senha-token">
                                    <span>Caso utilize autenticação de dois fatores, será preciso gerar uma 'senha de aplicação' e inseri-la aqui.</span><br/>
                                    <span>Entre em contato com o suporte para instruções mais detalhadas.</span>
                                </rich:toolTip>
                            </h:panelGroup>
                            <h:inputSecret id="senha" size="50" maxlength="80" styleClass="campos"
                                           redisplay="true"
                                           value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.senha}" />

                            <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_ConfiguracaoSistemaCRM_remetentePadrao}" />

                            <h:panelGroup id="panelResponsavelGrupo">
                                <h:inputText id="textColaboradorResponsavel" size="50"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="campos"

                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.remetentePadrao}"/>
                                <rich:suggestionbox height="100" width="400"
                                                    for="textColaboradorResponsavel"
                                                    suggestionAction="#{ConfiguracaoSistemaCRMControle.executarAutocompleteRemetente}"
                                                    minChars="1" rowClasses="20"
                                                    status="statusInComponent"
                                                    nothingLabel="#{msg_aplic.prt_New_Mala_Direta_Form_reponsavel}"
                                                    var="result" fetchValue="#{result.nome}" id="suggestionResponsavel">
                                    <h:column>
                                        <h:outputText value="#{result.nome}"/>
                                    </h:column>
                                    <a4j:support event="onselect" action="#{ConfiguracaoSistemaCRMControle.setarRemetente}"/>

                                </rich:suggestionbox>
                                <a4j:commandButton id="removeRemetnete" alt="Remove Remetente"
                                                   action="#{ConfiguracaoSistemaCRMControle.removerRemetentePadraoMailing}" reRender="panelResponsavelGrupo"
                                                   image="./imagens/limpar.gif" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_ConfiguracaoSistemaCRM_emailPadrao}" />
                            <h:inputText id="emailPadrao" size="50" maxlength="80" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.emailPadrao}" />

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_usarRemetentePadrao}" />
                            <h:panelGroup id="usarRemetentePadrao">
                                <h:selectBooleanCheckbox  id="usarRemetentePadraoGeral" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.usarRemetentePadraoGeral}">
                                    <a4j:support event="onclick" action="#{ConfiguracaoSistemaCRMControle.setarRemetentePadrao}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                                <rich:toolTip for="usarRemetentePadrao">
                                    <span>Ao marcar esta configuração, ao enviar o e-mail o remetente será o remetente padrão</span><br/>
                                    <span>Quando desmarcada: ao enviar o e-mail o remetente será o colaborador logado</span>
                                </rich:toolTip>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_enviarEmailIndividualmente}" />
                            <h:panelGroup id="emailIndividual">
                                <h:selectBooleanCheckbox id="enviarEmailIndividualmente" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.enviarEmailIndividualmente}"/>
                            <rich:toolTip for="emailIndividual">
                            <span>Ao marcar esta configuração, o sistema irá enviar um e-mail para cada destinatário.</span><br/>
                            <span>Quando desmarcada: o sistema envia um e-mail para cada 100 destinatários em cópia oculta.</span>
                            </rich:toolTip>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="* E-mail para teste" />
                            <h:inputText id="emailTeste" size="50" maxlength="50" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.emailTeste}"/>


                        </h:panelGrid>

                        <c:if test="${!ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.integracaoPacto}">
                            <div style="text-align: center; margin-top: 20px">
                                <a4j:commandButton id="configurarEmail" action="#{ConfiguracaoSistemaCRMControle.configurarEmail}"
                                                   reRender="form" styleClass="botoes nvoBt btSec"
                                                   oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}"
                                                   value="Configurar E-mail" accesskey="5" />
                                <rich:toolTip for="configurarEmail">
                                    <span>Para configurar o email é necessário preencher um email para teste.</span>
                                </rich:toolTip>
                            </div>
                        </c:if>
                        <c:if test="${!ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.integracaoPacto}">
                            <br/>
                            <rich:simpleTogglePanel id="configAvancadas" switchType="client" label="Configurações Avançadas" width="100%" opened="false">
                                <h:panelGrid  columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                    <h:panelGroup>
                                        <table  width="100%">
                                            <tr>
                                                <td width="30%">
                                                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_mailServer}" />
                                                </td>
                                                <td width="70%">
                                                        <h:inputText id="mailServer" size="50" maxlength="50" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.mailServer}" />
                                                <td>
                                            </tr>

                                            <tr>
                                                <td width="30%">
                                                    <h:outputText styleClass="tituloCampos" value="Porta Server" />
                                                </td>
                                                <td width="70%">
                                                        <h:inputText id="portaServer" size="10" maxlength="10" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.portaServer}" />
                                                <td>
                                            </tr>

                                            <tr>
                                                <td width="30%">
                                                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_conexaoSegura}" />
                                                </td>
                                                <td width="70%">
                                                        <h:selectBooleanCheckbox id="conexaoSegura" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.conexaoSegura}"/>
                                                <td>
                                            </tr>

                                            <tr>
                                                <td width="30%">
                                                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_iniciarTLS}" />
                                                </td>
                                                <td width="70%">
                                                        <h:selectBooleanCheckbox id="iniciarTLS" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.iniciarTLS}"/>
                                                <td>
                                            </tr>

                                            <tr>
                                                <td width="30%">
                                                    <h:outputText styleClass="tituloCampos" value="Usa protocolo SMTPS" />
                                                </td>
                                                <td width="70%">
                                                        <h:selectBooleanCheckbox id="protocoloSMTPS" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.usaSMTPS}"/>
                                                    <rich:toolTip for="protocoloSMTPS">
                                                    <span>Está opção requer configuração manual.</span>
                                                    </rich:toolTip>
                                                <td>
                                            </tr>

                                            <tr>
                                                <td width="30%">
                                                    <h:outputText styleClass="tituloCampos" value="Forçar configuração manual" />
                                                </td>
                                                <td width="70%">
                                                        <h:selectBooleanCheckbox id="configuracaoAvancadaManual" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.usaConfiguracaoEmailManual}"/>
                                                    <rich:toolTip for="configuracaoAvancadaManual">
                                                    <span>Não valida SMTP automatico</span>
                                                    </rich:toolTip>
                                                <td>
                                            </tr>

                                        </table>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </rich:simpleTogglePanel>
                        </c:if>

                        </br>

                        <rich:simpleTogglePanel id="emailsFechamentoMeta" switchType="client" label="E-mails para notificação de fechamento de meta" width="100%" opened="false">
                            <h:panelGrid  columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">


                                <h:panelGroup  id="dadosEmail" >
                                    <table width="100%">
                                        <tr>
                                            <td width="30%">
                                                &nbsp;<h:outputText styleClass="tituloCampos" value="E-mail "/>
                                            </td>
                                            <td width="70%">
                                                <h:inputText id="emailFechamento" size="50" maxlength="80"
                                                             value="#{ConfiguracaoSistemaCRMControle.emailFechamento.email}"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width="30%">
                                                &nbsp;<h:outputText styleClass="tituloCampos" value="Empresa "/>
                                            </td>
                                            <td width="70%">
                                                <h:selectOneMenu id="empresaEmailFechamento"
                                                                 value="#{ConfiguracaoSistemaCRMControle.emailFechamento.empresa.codigo}">
                                                    <f:selectItems value="#{ConfiguracaoSistemaCRMControle.listaEmpresas}"/>
                                                </h:selectOneMenu>
                                            </td>
                                        </tr>
                                    </table>
                                    <center>
                                        <a4j:commandButton id="adicionarEmail"
                                                           action="#{ConfiguracaoSistemaCRMControle.adicionarEmail}"
                                                           value="#{msg_bt.btn_adicionar}"
                                                           image= "./imagensCRM/botaoAdicionar.png"
                                                           reRender="emails, dadosEmail,mensagens"
                                                           accesskey="5" styleClass="botoes"/>
                                    </center>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="emails" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                             value="#{ConfiguracaoSistemaCRMControle.emailsFechamento}" var="email">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="E-mail"/>
                                        </f:facet>
                                        <h:outputText value="#{email.email}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Empresa"/>
                                        </f:facet>
                                        <h:outputText value="#{email.empresa.nome}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <h:commandButton id="removerItemFaixa"
                                                             action="#{ConfiguracaoSistemaCRMControle.removerEmail}"
                                                             value="#{msg_bt.btn_excluir}"
                                                             image="./imagensCRM/botaoRemover.png" accesskey="7"
                                                             styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </rich:simpleTogglePanel>

                        <br/>

                        <rich:simpleTogglePanel switchType="client" label="Termos fiscalizados pelos programas anti-spam " width="100%" opened="false">
                            <h:panelGrid  columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">


                                <h:panelGroup  id="termosSpam" >
                                    <table width="100%">
                                        <tr>
                                            <td width="30%">&nbsp;<h:outputText  styleClass="tituloCampos" id="blockTermos" value="Bloquear Termos? " />
                                                <rich:toolTip for="blockTermos" followMouse="true" direction="top-right" style="width:300px; height:70px; " showDelay="200">
                                                    <h:outputText styleClass="tituloCampos"
                                                                  value="#{msg.msg_tip_bloquearTermos}" />
                                                </rich:toolTip>

                                            </td>
                                            <td width="70%">
                                                <h:selectBooleanCheckbox id="checkBlock" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.bloquearTermoSpam}"></h:selectBooleanCheckbox>
                                                <rich:toolTip for="checkBlock" followMouse="true" direction="top-right" style="width:300px; height:70px; " showDelay="200">
                                                    <h:outputText styleClass="tituloCampos"
                                                                  value="#{msg.msg_tip_bloquearTermos}" />
                                                </rich:toolTip>
                                            </td>
                                        </tr><tr>
                                            <td width="30%">&nbsp;<h:outputText  styleClass="tituloCampos" value="Termo " /></td>
                                            <td width="70%">

                                                <h:inputText  id="termoIn" size="50"
                                                              maxlength="80" value="#{ConfiguracaoSistemaCRMControle.termoSpam}"/></td>
                                                <rich:hotKey selector="#termoIn" key="return"
                                                             handler="#{rich:element('addTermo')}.onclick();return false;"/>
                                        </tr>
                                    </table>
                                    <center>
                                        <a4j:commandButton action="#{ConfiguracaoSistemaCRMControle.adicionarTermoSpam}"
                                                           value="#{msg_bt.btn_adicionar}" id="addTermo"
                                                           image= "./imagensCRM/botaoAdicionar.png"
                                                           reRender="termosSpam, termos, mensagens"
                                                           oncomplete="#{rich:element('termoIn')}.focus();"
                                                           accesskey="5" styleClass="botoes"/>
                                    </center>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="termos" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.termosSpam}"
                                             var="termo">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="Termo" />
                                        </f:facet>
                                        <h:outputText  value="#{termo}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="removerTermo" reRender="termos"
                                                               action="#{ConfiguracaoSistemaCRMControle.removerTermo}"
                                                               value="#{msg_bt.btn_excluir}" image="./imagensCRM/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </rich:simpleTogglePanel>

                        <c:if test="${!ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.integracaoPacto}">
                            <br/>
                            <rich:simpleTogglePanel switchType="client" label="Mailing Automático" width="100%" opened="false">

                                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoSistemaCRM_remetentePadraoMailing}" />

                                    <h:panelGroup id="painelMessageRemetente">
                                        <h:inputText id="remetentePadraoMailing" size="50" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.remetentePadraoMailing.nome}"/>

                                        <rich:suggestionbox id="suggestionRemetentePadraoMailing" height="200" width="600"
                                                            for="remetentePadraoMailing" status="statusInComponent" immediate="true"
                                                            suggestionAction="#{ConfiguracaoSistemaCRMControle.autoCompleteRemetentePadraoMailing}"
                                                            nothingLabel="A busca não obteve resultados!" var="result">

                                            <a4j:support event="onselect"
                                                         action="#{ConfiguracaoSistemaCRMControle.selecionarRemetentePadrao}"
                                                         reRender="panelMensagem"/>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Nome" styleClass="textverysmall"/>
                                                </f:facet>
                                                <h:outputText styleClass="textverysmall" value="#{result.nome}"/>
                                            </h:column>
                                        </rich:suggestionbox>
                                        <a4j:commandButton id="removerRemetenteborracha" alt="Remove Remetente" rendered="true"
                                                           action="#{ConfiguracaoSistemaCRMControle.removerRemetentePadraoMailing}" reRender="painelMessageRemetente"
                                                           image="./imagens/limpar.gif" />
                                    </h:panelGroup>
                                </h:panelGrid>
                            </rich:simpleTogglePanel>
                        </c:if>

                        <br/>
                        <rich:simpleTogglePanel switchType="client" label="Integrações" width="100%" opened="false">

                            <rich:simpleTogglePanel switchType="client" label="Bitly" width="100%" opened="false">
                                <h:panelGrid  columns="1" width="100%" headerClass="subordinado">

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCampos" value="Token Bitly:"/>
                                        <rich:spacer width="15px"/>
                                        <h:inputText size="50" styleClass="campos"
                                                     value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.tokenBitly}"/>
                                    </h:panelGroup>

                                </h:panelGrid>
                            </rich:simpleTogglePanel>

                            <rich:simpleTogglePanel switchType="client" label="All In" width="100%" opened="false">
                                <h:panelGrid  columns="1" width="100%" headerClass="subordinado">
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCampos" value="Servidor FTP:"/>
                                        <rich:spacer width="15px"/>
                                        <h:inputText size="50" styleClass="campos"
                                                     value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.mailingFtpServer}"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCampos" value="Porta:"/>
                                        <rich:spacer width="15px"/>
                                        <h:inputText size="50" styleClass="campos"
                                                     value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.mailingFtpPort}"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCampos" value="Protocolo:"/>
                                        <rich:spacer width="15px"/>
                                        <h:selectOneMenu
                                                value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.mailingFtpType}">
                                            <f:selectItem itemLabel="SFTP" itemValue="sftp"/>
                                            <f:selectItem itemLabel="FTP" itemValue="ftp"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCampos" value="Pasta:"/>
                                        <rich:spacer width="15px"/>
                                        <h:inputText size="50" styleClass="campos"
                                                     value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.mailingFtpFolder}"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCampos" value="Usuário FTP:"/>
                                        <rich:spacer width="15px"/>
                                        <h:inputText size="50" styleClass="campos"
                                                     value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.mailingFtpUser}"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCampos" value="Senha FTP:"/>
                                        <rich:spacer width="15px"/>
                                        <h:inputText size="50" styleClass="campos"
                                                     value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.mailingFtpPass}"/>
                                    </h:panelGroup>

                                </h:panelGrid>
                            </rich:simpleTogglePanel>
                            <br/>
                        </rich:simpleTogglePanel>
                    </rich:tab>

                    <rich:tab id="configuracaoDiasPosVendas" label="Pós Vendas">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Configurações Básicas"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%"  footerClass="colunaCentralizada">
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_renovacao}" />
                                <h:selectBooleanCheckbox id="incluirContratosRenovados" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.incluirContratosRenovados}" />
                            </h:panelGrid>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada" id="pnlCfgDiasPosVenda">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%"  footerClass="colunaCentralizada">
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_descricao}" />
                                <h:inputText id="configuracaoDiasPosVenda_descricao" size="70" maxlength="50" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasPosVendaVO.descricao}"/>
                                <h:outputText  styleClass="tituloCampos" value="Responsável pelo contato" />
                                <h:selectOneMenu value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasPosVendaVO.siglaResponsavelPeloContato}" id="tipoColaboradorPosVenda">
                                    <f:selectItems value="#{ConfiguracaoSistemaCRMControle.montarSelectTipoColaboradorPosVenda}" />
                                </h:selectOneMenu>
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_nrDia}" />
                                <h:inputText  id="configuracaoDiasPosVenda_nrDia" size="10" maxlength="10" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasPosVendaVO.nrDia}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_ativo}" />
                                <h:selectBooleanCheckbox id="configuracaoDiasPosVenda_ativo" value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasPosVendaVO.ativo}" />
                            </h:panelGrid>
                            <a4j:commandButton
                                    id="posVendasBtnAdicionar"
                                    action="#{ConfiguracaoSistemaCRMControle.adicionarConfiguracaoDiasPosVenda}" value="#{msg_bt.btn_adicionar}" image= "./imagensCRM/botaoAdicionar.png" accesskey="5" styleClass="botoes" oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}" reRender="gridDiasPosVenda,pnlCfgDiasPosVenda" />
                        </h:panelGrid>
                        <h:panelGrid id="gridDiasPosVenda" columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="configuracaoDiasPosVendaVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                         value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.configuracaoDiasPosVendaVOs}" var="configuracaoDiasPosVenda">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_descricao}" />
                                    </f:facet>
                                    <h:outputText  value="#{configuracaoDiasPosVenda.descricao}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_nrDia}" />
                                    </f:facet>
                                    <h:outputText  value="#{configuracaoDiasPosVenda.nrDia}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Responsável pelo contato" />
                                    </f:facet>
                                    <h:outputText  value="#{configuracaoDiasPosVenda.montarResponsavel}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Ativo" />
                                    </f:facet>
                                    <h:outputText value="Ativo" rendered="#{configuracaoDiasPosVenda.ativo}"/>
                                    <h:outputText value="Inativo" rendered="#{!configuracaoDiasPosVenda.ativo}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <a4j:commandLink id="linkEditarPosVenda"
                                                         styleClass="botoes"
                                                         oncomplete="#{ConfiguracaoSistemaCRMControle.mensagemNotificar}"
                                                         action="#{ConfiguracaoSistemaCRMControle.editarConfiguracaoDiasPosVenda}"
                                                         reRender="gridDiasPosVenda,pnlCfgDiasPosVenda">
                                            <h:graphicImage id="editarPosVenda" value="./imagensCRM/botaoEditar.png"/>
                                        </a4j:commandLink>
                                        <rich:spacer width="5px;" />
                                        <a4j:commandLink id="linkRemoverPosVenda"
                                                         styleClass="botoes"
                                                         oncomplete="#{ConfiguracaoSistemaCRMControle.mensagemNotificar}"
                                                         action="#{ConfiguracaoSistemaCRMControle.removerConfiguracaoDiasPosVenda}"
                                                         reRender="gridDiasPosVenda,pnlCfgDiasPosVenda">
                                            <h:graphicImage id="removerPosVenda" value="./imagensCRM/botaoRemover.png"/>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="cfgDiasExAlunos" label="Ex-Alunos">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" id="pnlCfgDiasExAlunos"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_ConfiguracaoExAlunos_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%"  footerClass="colunaCentralizada">
                                <h:outputText styleClass="tituloCampos" value="* Motivo para entrar em contato com o cliente:"/>
                                <h:inputText id="cfgExAlunos_desc" size="70" maxlength="50" styleClass="campos"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasMetas.descricao}"/>

                                <h:outputText styleClass="tituloCampos" value="* Número de dias:"/>
                                <h:inputText id="cfgExAlunos_nrDia" size="5"
                                             onkeypress="return mascara(this.form, this.id, '9999', event);"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasMetas.nrDia}"/>
                            </h:panelGrid>
                            <a4j:commandButton action="#{ConfiguracaoSistemaCRMControle.adicionarConfiguracaoExAlunos}"
                                               id="exAlunosBtnAdicionar"
                                               value="#{msg_bt.btn_adicionar}" image="./imagensCRM/botaoAdicionar.png"
                                               styleClass="botoes" reRender="pnlCfgDiasExAlunos, cfgExAlunos_tbl"
                                               oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}"/>
                        </h:panelGrid>
                        <h:panelGrid id="cfgExAlunos_tbl" columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         id="exAlunos_tabFormSubordinada"
                                         rowClasses="linhaImparSubordinado, linhaParSubordinado"
                                         columnClasses="centralizado, centralizado, centralizado"
                                         var="configuracaoDiasMetas"
                                         value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.configuracaoDiasMetasExAlunos}">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Descrição"/>
                                    </f:facet>
                                    <h:outputText value="#{configuracaoDiasMetas.descricao}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Número de dias"/>
                                    </f:facet>
                                    <h:outputText value="#{configuracaoDiasMetas.nrDia}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="removerItemMeta"
                                                         action="#{ConfiguracaoSistemaCRMControle.removerConfiguracaoDiasExAlunos}"
                                                         value="#{msg_bt.btn_excluir}" styleClass="botoes"
                                                         image="./imagensCRM/botaoRemover.png"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>




                    <rich:tab id="cfgDiasGympass" label="Último Acesso Wellhub">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" id="pnlCfgDiasGympas"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_ConfiguracaoUltimoaAcessoGym_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%"  footerClass="colunaCentralizada">
                                <h:outputText styleClass="tituloCampos" value="* Motivo para entrar em contato com o cliente:"/>
                                <h:inputText id="cfgDiasGympas_desc" size="70" maxlength="50" styleClass="campos"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasMetas.descricao}"/>

                                <h:outputText styleClass="tituloCampos" value="* Número de dias:"/>
                                <h:inputText id="cfgDiasGympas_nrDia" size="5"
                                             onkeypress="return mascara(this.form, this.id, '9999', event);"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasMetas.nrDia}"/>
                            </h:panelGrid>
                            <a4j:commandButton action="#{ConfiguracaoSistemaCRMControle.adicionarConfiguracaoUltimoAcssoGym}"
                                               id="diasGympasBtnAdicionar"
                                               value="#{msg_bt.btn_adicionar}" image="./imagensCRM/botaoAdicionar.png"
                                               styleClass="botoes" reRender="pnlCfgDiasGympas, cfgDiasGympas_tbl"
                                               oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}"/>
                        </h:panelGrid>
                        <h:panelGrid id="cfgDiasGympas_tbl" columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         id="diasGympas_tabFormSubordinada"
                                         rowClasses="linhaImparSubordinado, linhaParSubordinado"
                                         columnClasses="centralizado, centralizado, centralizado"
                                         var="configuracaoDiasMetas"
                                         value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.configuracaoDiasMetasUltimoAcessoGymp}">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Descrição"/>
                                    </f:facet>
                                    <h:outputText value="#{configuracaoDiasMetas.descricao}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Número de dias"/>
                                    </f:facet>
                                    <h:outputText value="#{configuracaoDiasMetas.nrDia}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="removerItemMetaGym"
                                                         action="#{ConfiguracaoSistemaCRMControle.removerConfiguracaoDiasUltimoAcessoGymp}"
                                                         value="#{msg_bt.btn_excluir}" styleClass="botoes"
                                                         image="./imagensCRM/botaoRemover.png"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>




                    <rich:tab id="cfgDiasVisitantesAntigos" label="Visitantes Antigos">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" id="pnlCfgDiasVisitantesAntigos"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Visitantes Antigos"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%"  footerClass="colunaCentralizada">
                                <h:outputText styleClass="tituloCampos" value="* Motivo para entrar em contato com o cliente:"/>
                                <h:inputText id="cfgVisitantesAntigos_desc" size="70" maxlength="50" styleClass="campos"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasMetas.descricao}"/>

                                <h:outputText styleClass="tituloCampos" value="* Número de dias:"/>
                                <h:inputText id="cfgVisitantesAntigos_nrDia" size="5"
                                             onkeypress="return mascara(this.form, this.id, '9999', event);"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasMetas.nrDia}"/>
                            </h:panelGrid>
                            <a4j:commandButton action="#{ConfiguracaoSistemaCRMControle.adicionarConfiguracaoVisitantesAntigos}"
                                               id="visitantesAntigosBtnAdicionar"
                                               value="#{msg_bt.btn_adicionar}" image="./imagensCRM/botaoAdicionar.png"
                                               styleClass="botoes" reRender="pnlCfgDiasVisitantesAntigos, cfgVisitantesAntigos_tbl"
                                               oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}"/>
                        </h:panelGrid>
                        <h:panelGrid id="cfgVisitantesAntigos_tbl" columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         id="visitantesAntigos_tabFormSubordinada"
                                         rowClasses="linhaImparSubordinado, linhaParSubordinado"
                                         columnClasses="centralizado, centralizado, centralizado"
                                         var="configuracaoDiasMetas"
                                         value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.configuracaoDiasMetasVisitantesAntigos}">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Descrição"/>
                                    </f:facet>
                                    <h:outputText value="#{configuracaoDiasMetas.descricao}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Número de dias"/>
                                    </f:facet>
                                    <h:outputText value="#{configuracaoDiasMetas.nrDia}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="removerItemMeta"
                                                         action="#{ConfiguracaoSistemaCRMControle.removerConfiguracaoDiasVisitantesAntigos}"
                                                         value="#{msg_bt.btn_excluir}" styleClass="botoes"
                                                         image="./imagensCRM/botaoRemover.png"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="configuracaoFaixasHorarios" label="Faixas de Horário de Acesso">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <h:panelGrid style="margin-right:15px;background-color:#B5B5B5;text-align:center;" width="100%">
                                <h:panelGroup>
                                    <h:outputText style="font-size:12px;font-weight:bold;margin-right:5px;" value="Configuração das Faixas de Horário de Acesso dos Clientes"/>
                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-configurar-as-faixas-de-horario-dos-acessos-dos-clientes/"
                                                  title="Clique e saiba mais: Configurações CRM Web" target="_blank">
                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                    </h:outputLink>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid id="dados" columns="2" width="100%"  footerClass="colunaCentralizada">
                                <h:outputText  styleClass="tituloCampos" value="Nome do Período" />
                                <h:inputText id="nomeFaixa" size="50" maxlength="50" styleClass="campos" value="#{ConfiguracaoSistemaCRMControle.faixaAcesso.nomePeriodo}"/>

                                <h:outputText  styleClass="tituloCampos" value="Horário Inicial" />
                                <h:inputText  id="horaInicio" size="7"
                                              maxlength="7" value="#{ConfiguracaoSistemaCRMControle.faixaAcesso.horaInicial}"
                                              onkeypress="return mascarahora(this, '99:99', event);"/>

                                <h:outputText  styleClass="tituloCampos" value="Horário Final" />
                                <h:inputText id="horaFim" value="#{ConfiguracaoSistemaCRMControle.faixaAcesso.horaFinal}"
                                             onkeypress="return mascarahora(this, '99:99', event);"
                                             size="7" styleClass="form" onfocus="focusinput(this);" onblur="blurinput(this);"
                                             maxlength="7">
                                </h:inputText>
                            </h:panelGrid>

                            <center>
                                <h:panelGroup>
                                    <a4j:commandButton action="#{ConfiguracaoSistemaCRMControle.adicionarFaixa}"
                                                       value="#{msg_bt.btn_adicionar}"
                                                       image= "./imagensCRM/botaoAdicionar.png"
                                                       reRender="faixas, dados,mensagens"
                                                       id="botaoAdicionar"
                                                       accesskey="5" styleClass="botoes"
                                                       oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}"/>
                                    <a4j:commandButton action="#{ConfiguracaoSistemaCRMControle.iniciarFaixa}"
                                                       value="#{msg_bt.btn_adicionar}"
                                                       image= "./imagensCRM/botaoCancelar.png"
                                                       reRender="faixas, dados,mensagens"
                                                       styleClass="botoes"/>
                                </h:panelGroup>
                            </center>

                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="faixas" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                         value="#{ConfiguracaoSistemaCRMControle.faixasPeriodoAcesso}" var="faixa">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Nome do Período" />
                                    </f:facet>
                                    <h:outputText  value="#{faixa.nomePeriodo}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Horário Inicial" />
                                    </f:facet>
                                    <h:outputText  value="#{faixa.horaInicial}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Horário Final" />
                                    </f:facet>
                                    <h:outputText  value="#{faixa.horaFinal}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="editarItemFaixa"
                                                         action="#{ConfiguracaoSistemaCRMControle.editarFaixa}"
                                                         value="#{msg_bt.btn_editar}"
                                                         image="./imagensCRM/botaoEditar.png"
                                                         accesskey="6" styleClass="botoes"/>

                                        <rich:spacer width="5px;" />
                                        <h:commandButton id="removerItemFaixa" action="#{ConfiguracaoSistemaCRMControle.removerFaixas}" value="#{msg_bt.btn_excluir}" image="./imagensCRM/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>
                    <!--  ---------CONFIGURACAO DE TIPOS DE VINCULOS NAS FASE ------------------- -->

                    <rich:tab id="configuracaoFasesVinculos" label="Responsáveis pelas Fases">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <h:panelGrid style="margin-right:15px;background-color:#B5B5B5;text-align:center;" width="100%">
                                <h:panelGroup>
                                    <h:outputText style="font-size:12px;font-weight:bold;margin-right:5px;" value="Associar tipos de colaboradores a fases do CRM"/>
                                </h:panelGroup>
                            </h:panelGrid>

                            <h:panelGrid id="dadosFases" columns="2" width="100%"
                                          footerClass="colunaCentralizada">
                                <h:panelGroup>
                                    <br />
                                    <h:selectBooleanCheckbox value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.dividirFase}" id="habilitarResponsaveisFase" >
                                        <a4j:support action="#{ConfiguracaoSistemaCRMControle.montarTabelaFases}"
                                                     event="onclick" reRender="form"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="tituloCampos" value="Usar a associação de tipos de colaboradores a fases do CRM"/>
                                    <br/>
                                </h:panelGroup>
                            </h:panelGrid>

                            <center>

                                <h:panelGroup id="grupoBtnPadrao" rendered="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.dividirFase}">
                                    <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                       oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}" action="#{ConfiguracaoSistemaCRMControle.confirmarExcluir}"
                                                       image="imagensCRM/botaoPadrao.png" title="Utilizar a sugestão do Método de Gestão" />
                                </h:panelGroup>

                                <%--<a4j:commandButton rendered="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.dividirFase}"--%>
                                                   <%--image="imagensCRM/botaoPadrao.png"--%>
                                                   <%--action="#{ConfiguracaoSistemaCRMControle.sugerirDivisao}" reRender="fases" onclick="if(!confirm('Deseja usar a sugestão do Método de Gestão?')){return false};">--%>
                                    <%--<rich:toolTip followMouse="true" direction="top-right" style="width:150px; height:70px; " showDelay="200">--%>
                                        <%--<h:outputText styleClass="tituloCampos"--%>
                                                      <%--value="Utilizar a sugestão do Método de Gestão" />--%>
                                    <%--</rich:toolTip>--%>
                                <%--</a4j:commandButton>--%>
                            </center>

                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <rich:dataTable rendered="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.dividirFase}"
                                            id="fases" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                            rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                            value="#{ConfiguracaoSistemaCRMControle.tiposVinculosFase}" var="fase">

                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Fase" />
                                    </f:facet>
                                    <h:graphicImage width="25px" height="25px" url="./imagensCRM/#{fase.fase.imagem}" title="#{fase.fase.descricao}" />
                                    &nbsp;
                                    <h:outputText value="#{fase.fase.descricao}" />
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Tipos de Colaboradores" />
                                    </f:facet>

                                    <center>
                                        <table cellpadding="0" cellspacing="0">
                                            <tr>
                                                <td valign="top">
                                                    <h:outputText style="color: #474747;font-family: Arial,Verdana,sans-serif;font-size: 11px;" value="Tipo de Colaborador:" />&nbsp;
                                                    <h:selectOneMenu value="#{fase.tipoSelecionado}" id="tipoColaborador">
                                                        <f:selectItem itemLabel=" -- Selecione --" itemValue="" />
                                                        <f:selectItems value="#{fase.opcoesTipoColaborador}" />
                                                    </h:selectOneMenu></td>
                                                <td>&nbsp;
                                                    <a4j:commandButton value="Adicionar"
                                                                       id="btnAdicionar"
                                                                       action="#{ConfiguracaoSistemaCRMControle.adicionarTipoAFase}"
                                                                       reRender="fases" image="imagens/botaoAdicionar.png">
                                                    </a4j:commandButton></td>
                                            </tr>
                                        </table>
                                    </center>
                                    <br/>
                                    <rich:dataTable id="tabelaRemover" rendered="#{not empty fase.tiposColaborador}" width="100%" value="#{fase.tiposColaborador}" var="tipo" styleClass="tabFormSubordinada"
                                                    rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento">
                                        <rich:column width="50%">
                                            <f:facet name="header">
                                                <h:outputText  value="Tipos Associados" />
                                            </f:facet>
                                            <h:outputText  value="#{tipo.descricao}" />
                                        </rich:column>
                                        <rich:column width="50%">
                                            <f:facet name="header">
                                                <h:outputText  value="Opções" />
                                            </f:facet>
                                            <a4j:commandButton id="btnRemover" value="Excluir" action="#{ConfiguracaoSistemaCRMControle.removerTipoAFase}"
                                                               reRender="fases" image= "./imagensCRM/botaoRemover.png"/>
                                        </rich:column>
                                    </rich:dataTable>
                                </rich:column>
                            </rich:dataTable>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="abaOrdenacaoMetas" label="Ordenação Metas">
                        <h:panelGrid columns="2" width="100%" headerClass="subordinado" columnClasses="colunaEsquerda">
                            <f:facet name="header">
                                <h:outputText value="Ordenação Metas"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" >
                                <h:selectBooleanCheckbox id="obrigSeguirMetas" value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.obrigatorioSeguirOrdemMetas}"/>
                                <h:outputText styleClass="tituloCampos" value="Obrigatório seguir ordenação das metas"/>
                                <br/>
                                <h:outputText styleClass="tituloCampos" value="** Essas metas não entram na obrigatoriedade da ordenação, visto que podem acontecer a qualquer momento."/>
                            </h:panelGrid>
                            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                <a4j:commandButton id="restuararOrdenacaoMetas" action="#{ConfiguracaoSistemaCRMControle.restaurarPadraoOrdenacaoMetas}" reRender="tabelaOrdenacaoMetasCRM"
                                                   value="Restaurar Padrão" accesskey="2" styleClass="botoes nvoBt" style="font-size: x-small"/>
                            </h:panelGrid>
                        </h:panelGrid>

                        <h:panelGrid id="tabelaOrdenacaoMetas" columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="tabelaOrdenacaoMetasCRM" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="centralizado, centralizado, centralizado, centralizado"
                                         var="ordenacaoMetas"
                                         value="#{ConfiguracaoSistemaCRMControle.listaOrdenacaoMetas}">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Sigla"/>
                                    </f:facet>
                                    <h:outputText value="#{ordenacaoMetas.sigla}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Tipo Meta"/>
                                    </f:facet>
                                    <h:outputText value="#{ordenacaoMetas.tipoFase.descricaoCurta}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Meta"/>
                                    </f:facet>
                                    <h:outputText value="#{ordenacaoMetas.descricao}"/>
                                    <h:outputText rendered="#{ordenacaoMetas.sigla == 'AG' || ordenacaoMetas.sigla == 'IN' || ordenacaoMetas.sigla == 'AL'}" style="margin-left: 5px" value="**"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Ordenação"/>
                                    </f:facet>
                                    <h:panelGroup>
                                        <a4j:commandButton id="subir"
                                                         action="#{ConfiguracaoSistemaCRMControle.moverMetaParaCima}"
                                                         reRender="tabelaOrdenacaoMetasCRM"
                                                         styleClass="botoes"
                                                         image="./imagens/setaCima.png"/>

                                        <a4j:commandButton id="descer"
                                                         action="#{ConfiguracaoSistemaCRMControle.moverMetaParaBaixo}"
                                                         reRender="tabelaOrdenacaoMetasCRM"
                                                         styleClass="botoes"
                                                         image="./imagens/setaBaixo.png"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>

 					<rich:tab id="configuracaoDiasMetasEstudio" label="Metas Estúdio" rendered="#{LoginControle.apresentarLinkEstudio}">
                        <h:panelGrid style="margin-right:15px;background-color:#B5B5B5;text-align:center;" width="100%">
                            <h:panelGroup>
                                <h:outputText style="font-size:12px;font-weight:bold;margin-right:5px;" value="Metas Estúdio"/>
                                <h:outputLink value="#{SuperControle.urlWikiCRM}MetasStudio"
                                              title="Clique e saiba mais: Metas Estúdio" target="_blank">
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" id="dadosMetaEstudio" columnClasses="colunaCentralizada">
                            <h:panelGrid columns="2" width="100%" 
                                         columnClasses="colunaDireita,colunaEsquerda">
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_fase}:"/>
                                <h:selectOneMenu id="oneMenuFase" value="#{ConfiguracaoSistemaCRMControle.faseEstudioCodigo}">
                                    <f:selectItems value="#{ConfiguracaoSistemaCRMControle.faseEstudioItens}"/>
                                    <a4j:support event="onchange" reRender="listaMetaEstudio, dadosMetaEstudio"/>
                                </h:selectOneMenu>

                                <h:outputText styleClass="tituloCampos" value="*Descrição:"/>
                                <h:inputText id="configuracaoDiasMetas_descricao" size="70" maxlength="50" styleClass="campos"
                                             value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasMetas.descricao}"/>

                                <h:outputText rendered="#{ConfiguracaoSistemaCRMControle.semAgendamento}" styleClass="tituloCampos"
                                              value="*Produto:"/>

                                <h:panelGroup rendered="#{ConfiguracaoSistemaCRMControle.semAgendamento}">
                                    <h:inputText  id="produtoSemAgendamento" onblur="blurinput(this);"
                                                  onfocus="focusinput(this);" styleClass="form"
                                                  value="#{ConfiguracaoSistemaCRMControle.produto.descricao}" />

                                    <rich:suggestionbox height="200" width="200" for="produtoSemAgendamento"
                                                        fetchValue="#{result.descricao}"
                                                        suggestionAction="#{ConfiguracaoSistemaCRMControle.executarAutocompletePesqProduto}"
                                                        minChars="1" rowClasses="20" status="statusHora" id="suggestionProduto"
                                                        nothingLabel="Nenhum Produto encontrado!" var="result">
                                        <a4j:support event="onselect" action="#{ConfiguracaoSistemaCRMControle.selecionarProduto}"/>
                                        <h:column>
                                            <h:outputText value="#{result.descricao}" />
                                        </h:column>
                                    </rich:suggestionbox>
                                    <rich:spacer width="5px"/>
                                </h:panelGroup>

                                <h:outputText rendered="#{ConfiguracaoSistemaCRMControle.semAgendamento}"
                                              styleClass="tituloCampos"
                                              value="*#{msg_aplic.prt_ConfiguracaoDiasMetas_semagendamento}:"/>

                                <h:outputText rendered="#{ConfiguracaoSistemaCRMControle.sessoesFinais}"
                                              styleClass="tituloCampos"
                                              value="*#{msg_aplic.prt_ConfiguracaoDiasMetas_sessoesfinais}:"/>
                                <h:panelGroup>
                                    <h:inputText id="configuracaoDiasMetas_nrDia" size="5"
                                                 onkeypress="return mascara(this.form, this.id, '9999', event);"
                                                 value="#{ConfiguracaoSistemaCRMControle.configuracaoDiasMetas.nrDia}"/>
                                </h:panelGroup>

                            </h:panelGrid>
                            <a4j:commandButton rendered="#{ConfiguracaoSistemaCRMControle.sessoesFinais}"
                                             action="#{ConfiguracaoSistemaCRMControle.adicionarConfiguracaoSessoesFinais}" value="#{msg_bt.btn_adicionar}"
                            				 image= "./imagensCRM/botaoAdicionar.png" accesskey="5" styleClass="botoes"
                            				 reRender="listaMetaEstudio, dadosMetaEstudio"
                            				 oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}"/>

                            <a4j:commandButton rendered="#{ConfiguracaoSistemaCRMControle.semAgendamento}"
                                             action="#{ConfiguracaoSistemaCRMControle.adicionarConfiguracaoSemAgendamento}" value="#{msg_bt.btn_adicionar}"
                            				 image= "./imagensCRM/botaoAdicionar.png" accesskey="5" styleClass="botoes"
                            				 reRender="listaMetaEstudio, dadosMetaEstudio"
                            				 oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}"/>

                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada" id="listaMetaEstudio">
                            <h:dataTable id="configuracaoDiasMetasSF" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="centralizado, centralizado, centralizado"
                                         rendered="#{ConfiguracaoSistemaCRMControle.sessoesFinais}"
                                         value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.configuracaoDiasMetasSessoesFinais}" var="configuracaoDiasMetas">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_descricao}" />
                                    </f:facet>
                                    <h:outputText  value="#{configuracaoDiasMetas.descricao}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_ConfiguracaoDiasMetas_sessoesfinais}" />
                                    </f:facet>
                                    <h:outputText value="#{configuracaoDiasMetas.nrDia}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="removerItemMeta" action="#{ConfiguracaoSistemaCRMControle.removerConfiguracaoDiasSessoesFinais}"
                                        value="#{msg_bt.btn_excluir}" image="./imagensCRM/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>

                            <h:dataTable id="configuracaoDiasMetasSA" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="centralizado, centralizado, centralizado"
                                         value="#{ConfiguracaoSistemaCRMControle.configuracaoSistemaCRMVO.configuracaoDiasMetasSemAgendamento}"
                                         rendered="#{ConfiguracaoSistemaCRMControle.semAgendamento}"
                                         var="configuracaoDiasMetas">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_ConfiguracaoDiasPosVenda_descricao}" />
                                    </f:facet>
                                    <h:outputText  value="#{configuracaoDiasMetas.descricao}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_ConfiguracaoDiasMetas_semagendamento}" />
                                    </f:facet>
                                    <h:outputText  value="#{configuracaoDiasMetas.nrDia}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Produto" />
                                    </f:facet>
                                    <h:outputText value="#{configuracaoDiasMetas.produto.descricao}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="removerItemMeta" action="#{ConfiguracaoSistemaCRMControle.removerConfiguracaoDiasSemAgendamento}"
                                        value="#{msg_bt.btn_excluir}" image="./imagensCRM/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>
                </rich:tabPanel>

                <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGroup layout="block" style="text-align: center">
                        <a4j:commandButton reRender="form" id="salvar"
                                           action="#{ConfiguracaoSistemaCRMControle.gravar}"
                                           oncomplete="#{ConfiguracaoSistemaCRMControle.msgAlert}"
                                           value="#{msg_bt.btn_gravar}"
                                           styleClass="botoes nvoBt"
                                           title="#{msg.msg_gravar_dados}" accesskey="2">
                        </a4j:commandButton>

                        <a4j:commandLink id="visualizarLog"
                                         action="#{ConfiguracaoSistemaCRMControle.realizarConsultaLogCRM}"
                                         style="display: inline-block; padding: 8px 15px;"
                                         oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                         title="Visualizar Log"
                                         styleClass="botoes nvoBt btSec">
                            <i class="fa-icon-list"/>
                        </a4j:commandLink>

                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script type="text/javascript">
    document.getElementById("form:abertoSabado").focus();
</script>
