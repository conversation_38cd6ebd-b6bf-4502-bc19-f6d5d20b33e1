<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@include file="/includes/verificaModulo.jsp" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>

    .situacaoBallon {
        border-radius: 100px;
        color: white;
        padding: 4px 18px 4px 16px;
        text-align: center;
        display: inline-block;
        width: 65px;
        height: 15px;
        line-height: normal;
    }

    .rich-table-subheader {
        line-height: normal;
    }
</style>
<h:panelGrid id="listagemParcelas" rendered="#{not empty ParcelaEmAbertoControleRel.listaParcelas}" columns="1"
             width="100%" cellpadding="0" cellspacing="0">
    <h:panelGrid columns="1" width="100%">
        <h:panelGrid width="100%" style="text-align: right;">
            <h:panelGroup layout="block">
                <c:if test="${modulo eq 'zillyonWeb'}">

                    <a4j:commandLink id="btnExcel"
                                     rendered="#{ParcelaEmAbertoControleRel.relatorioRetrato}"
                                     styleClass="exportadores linkPadrao"
                                     actionListener="#{ParcelaEmAbertoControleRel.exportar}"
                                     oncomplete="#{ParcelaEmAbertoControleRel.abrirRelatorio}"
                                     accesskey="3">
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos"
                                     value="#{ParcelaEmAbertoControleRel.atributos}"/>
                        <f:attribute name="prefixo" value="ParcelaEmAberto"/>
                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>

                    <a4j:commandLink styleClass="exportadores margin-h-10 linkPadrao"
                                     action="#{ParcelaEmAbertoControleRel.imprimirHorizontal}"
                                     rendered="#{ParcelaEmAbertoControleRel.relatorioRetrato}"
                                     oncomplete="#{ParcelaEmAbertoControleRel.abrirRelatorio}"
                                     reRender="form">
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                    </a4j:commandLink>

                    <a4j:commandLink id="imprimirPDF-Horizontal"
                                     action="#{ParcelaEmAbertoControleRel.imprimirVertical}"
                                     oncomplete="#{ParcelaEmAbertoControleRel.abrirRelatorio}"
                                     value="Gerar Relatório (Paisagem)"
                                     rendered="#{ParcelaEmAbertoControleRel.relatorioPaisagem}"
                                     accesskey="2" styleClass="botoes nvoBt" reRender="form"/>
                </c:if>
                <c:if test="${modulo eq 'centralEventos'}">
                    <a4j:commandButton id="imprimirPDF" action="#{ParcelaEmAbertoControleRel.imprimirPDF}"
                                       oncomplete="#{ParcelaEmAbertoControleRel.nomeRefRelatorioGeradoAgora}"
                                       value="Gerar Relatório (PDF)"
                                       accesskey="2" styleClass="botoes" reRender="form"
                                       image="/imagens/imprimir.png"/>
                </c:if>

            </h:panelGroup>
        </h:panelGrid>

        <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes" style="line-height: 45px; white-space: nowrap;"
                        value="#{ParcelaEmAbertoControleRel.listaParcelas}" rows="50" var="resumoPessoa"
                        rowKeyVar="status">
            <%--<%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>--%>
            <rich:column sortBy="#{resumoPessoa.matricula}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Matrícula"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.matricula}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.nome}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Nome"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              title="#{resumoPessoa.nome}"
                              value="#{resumoPessoa.nomeAbreviado}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.primeiroTelefone}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Celular"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              title="#{resumoPessoa.telefones}"
                              value="#{resumoPessoa.primeiroTelefone}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.contrato}" styleClass="col-text-align-center"
                         headerClass="col-text-align-center
">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Contrato"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.contrato}">
                </h:outputText>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.descricaoParcela}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Parcela"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              title="#{resumoPessoa.descricaoParcela}"
                              value="#{resumoPessoa.descricaoParcelaAbreviado}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.situacao_Apresentar}" styleClass="col-text-align-left tooltipster"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="SITUAÇÃO"/>
                </f:facet>
                <h:outputText
                        styleClass="tooltipster texto-size-14-real texto-cor-cinza texto-font situacaoBallon"
                        style="#{resumoPessoa.situacao eq 'EA' ? 'background: #DB2C3D;' : resumoPessoa.situacao eq 'PG' ? 'background: #48D567;' : 'background: #80858C;'}"
                        title="#{resumoPessoa.situacao eq 'EA' ? 'Aguardando Pagamento' : resumoPessoa.situacao eq 'CA' ? resumoPessoa.dataCancelamento_Hint : resumoPessoa.dataPagamento_Hint}"
                        value="#{resumoPessoa.situacao_Apresentar}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.valorMultasJuros}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Valor"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              title="Valor: #{ParcelaEmAbertoControleRel.empresaLogado.moeda} #{resumoPessoa.valor_Apresentar} + (Multas e Juros)"
                              value="#{ParcelaEmAbertoControleRel.empresaLogado.moeda} #{resumoPessoa.valorMultasJuros_Apresentar}"/>
            </rich:column>
            <c:if test="${ParcelaEmAbertoControleRel.gerarMultasJuros}">
                <rich:column sortBy="#{resumoPessoa.multas}" styleClass="col-text-align-left"
                             headerClass="col-text-align-left">
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Multa"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{ParcelaEmAbertoControleRel.empresaLogado.moeda} #{resumoPessoa.multas}"/>
                </rich:column>
                <rich:column sortBy="#{resumoPessoa.juros}" styleClass="col-text-align-left"
                             headerClass="col-text-align-left">
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Juros"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{ParcelaEmAbertoControleRel.empresaLogado.moeda} #{resumoPessoa.juros}"/>
                </rich:column>
            </c:if>
            <rich:column sortBy="#{resumoPessoa.recorrencia_Apresentar}" styleClass="col-text-align-center"
                         headerClass="col-text-align-center">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Recorrência"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.recorrencia_Apresentar}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.nrTentativas}" styleClass="col-text-align-center" rendered="#{ParcelaEmAbertoControleRel.mostrarNrTentativasMovitoRetorno}"
                         headerClass="col-text-align-center">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Nr. Tentativas"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.nrTentativas}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.motivoRetorno}" styleClass="col-text-align-left" rendered="#{ParcelaEmAbertoControleRel.mostrarNrTentativasMovitoRetorno}"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Motivo Retorno"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              title="#{resumoPessoa.motivoRetorno}"
                              value="#{resumoPessoa.motivoRetornoAbreviado}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.dataFatura}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Faturamento"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.dataFaturamento_Apresentar}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.dateVencimento}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Vencimento"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.dateVencimento_Apresentar}"/>
            </rich:column>
            <rich:column rendered="#{ParcelaEmAbertoControleRel.apresentarFormaPagamento}" sortBy="#{resumoPessoa.dataPagamento}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Pagamento"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.dataPagamento_Apresentar}"/>
            </rich:column>
            <rich:column rendered="#{ParcelaEmAbertoControleRel.apresentarFormaPagamento}"  sortBy="#{resumoPessoa.formasPagamento}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Forma"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.formasPagamento}"/>
            </rich:column>
            <rich:column  sortBy="#{resumoPessoa.modalidades}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Modalidades"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.modalidades}"/>
            </rich:column>
            <rich:column  sortBy="#{resumoPessoa.nomeRespPgto}" styleClass="col-text-align-left"
                          headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Resp. Pgto"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.nomeRespPgto}"/>
            </rich:column>
            <rich:column  sortBy="#{resumoPessoa.turma}" styleClass="col-text-align-left"
                          headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Turma"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.turma}"/>
            </rich:column>
            <rich:column  sortBy="#{resumoPessoa.nome_plano}" styleClass="col-text-align-left"
                          headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Plano"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.nome_plano}"/>
            </rich:column>
            <rich:column rendered="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.apresentarDadosSensiveis}"  sortBy="#{resumoPessoa.cpf}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="CPF"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.cpf}"/>
            </rich:column>
            <rich:column rendered="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.apresentarDadosSensiveis}"  sortBy="#{resumoPessoa.endereco}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Endereço"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.endereco}"/>
            </rich:column>
            <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                <a4j:commandLink rendered="#{resumoPessoa.apresentarBotaoCliente}" styleClass="linkPadrao texto-size-16-real texto-cor-azul"
                                 action="#{ParcelaEmAbertoControleRel.irParaTelaCliente}"
                                 oncomplete="#{ParcelaEmAbertoControleRel.msgAlert};#{ParcelaEmAbertoControleRel.mensagemNotificar}">
                    <f:param name="state" value="AC"/>
                    <i class="fa-icon-search"></i>
                </a4j:commandLink>
            </rich:column>
            <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                <a4j:commandLink  action="#{ModalRenegociadasControle.abrirModalRenegociacoesRelParcelas}" reRender="mensgens, formModalRenegociadas"
                                  oncomplete="#{ModalRenegociadasControle.msgAlert};#{ModalRenegociadasControle.mensagemNotificar}"
                                  title="Histórico de Renegociações"
                                  styleClass="linkPadrao texto-cor-azul texto-size-16-real">
                    <i class="fa-icon-refresh"></i>
                    <f:param name="movparcela" value="#{resumoPessoa.parcela}"/>
                </a4j:commandLink>
            </rich:column>
        </rich:dataTable>
        <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center"
                           for="v20_form:tabelaRes" maxPages="10" id="sctabelaRes"/>
    </h:panelGrid>
</h:panelGrid>

