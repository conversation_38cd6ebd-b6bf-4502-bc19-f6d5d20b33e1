<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="../includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" language="javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="../bootstrap/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="../script/tooltipster/jquery.tooltipster.min.js"></script>
<title>Relatório de Parcelas</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<style type="text/css">
    .notificacaoAtividades {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        -webkit-font-smoothing: subpixel-antialiased;
        background-clip: padding-box;
        background: #819aa5 -webkit-linear-gradient(top, #96acb6,
        #5d7b89);
        box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        color: rgb(255, 255, 255) !important;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        height: 13px !important;
        line-height: normal;
        list-style-type: none;
        padding: 1px 3px !important;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0 -1px 0;
        zoom: 1;
        border-radius: 40%;
    }

    .iconCalendar {
        width: 17px;
        height: 17px;
        padding-top: 6px;
        padding-left: 5px;
    }

    input.inputs, select.inputs {
        padding: 5px;
        background-image: none !important;
        font-size: 12px !important;
        border-radius: 4px;
        color: #A1A5AA;
        border: 1px solid #DCDDDF;
        width: 80%;
    }

    .separador-horizontal {
        margin: 10px 0px 10px 0px;
        width: 100%;
        height: 1px;
        border-bottom: 1px solid #E5E5E5;
    }

    .tituloCampos {
        color: #51555A !important;
        text-align: right;
    }

    .iconeDentro {
        margin-left: -23px;
    }

    .width100 {
        width: 100% !important;
    }

    .col35 {
        width: 35%;
        padding-right: 10px !important;
    }

    .col50 {
        width: 50%;
    }

    .btn-expand50 {
        width: 50%;
        display: inline-block;
        text-align: center;
        /*padding: 8px;*/
        padding-right: 10px !important;
    }

    .btn-expand60 {
        width: 60%;
        display: inline-block;
        text-align: center;
        /*padding: 8px;*/
    }
</style>

<f:view>
    <%@include file="../include_modal_renegociadas.jsp"%>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ParcelaEmAberto_tituloRel}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-de-parcelas/"/>

    <rich:modalPanel id="v20_panelResponsavel" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500"
                     height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Responsável"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="v20_hidelink1"/>
                <rich:componentControl for="v20_panelResponsavel" attachTo="v20_hidelink1" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="v20_formResponsavel" ajaxSubmit="true" styleClass="font-size-Em-max">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza "
                                  value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="v20_consultaresponsavel"
                                         value="#{ParcelaEmAbertoControleRel.campoConsultarResponsavel}">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.tipoConsultaComboResponsavel}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="v20_valorConsultaResponsavel" size="10"
                                 value="#{ParcelaEmAbertoControleRel.valorConsultarResponsavel}"/>

                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandLink id="v20_btnConsultarResponsavel" reRender="v20_formResponsavel"
                                         action="#{ParcelaEmAbertoControleRel.consultarResponsavel}"
                                         styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                         value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandLink id="v20_btnConsultarResponsavel" reRender="v20_formResponsavel"
                                         action="#{ParcelaEmAbertoControleRel.consultarResponsavel}"
                                         value="#{msg_bt.btn_consultar}"
                                         styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                         title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                </h:panelGrid>

                <rich:dataTable id="v20_resultadoConsultaResponsavel" width="100%" styleClass="tabelaSimplesCustom"
                                rendered="#{not empty ParcelaEmAbertoControleRel.listaConsultarResponsavel}"
                                value="#{ParcelaEmAbertoControleRel.listaConsultarResponsavel}" rows="5"
                                var="responsavel">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText
                                    styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                    value="#{msg_aplic.prt_Usuario_colaborador}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                             action="#{ParcelaEmAbertoControleRel.selecionarResponsavel}"
                                             focus="responsavel" reRender="v20_form"
                                             oncomplete="Richfaces.hideModalPanel('v20_panelResponsavel')"
                                             value="#{responsavel.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <a4j:commandLink action="#{ParcelaEmAbertoControleRel.selecionarResponsavel}"
                                         styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                         focus="responsavel"
                                         reRender="v20_form" oncomplete="Richfaces.hideModalPanel('v20_panelResponsavel')"
                                         title="#{msg.msg_selecionar_dados}">
                            <span>Selecionar </span>
                            <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="v20_formResponsavel:v20_resultadoConsultaResponsavel"
                                   maxPages="10" renderIfSinglePage="false"
                                   styleClass="scrollPureCustom"
                                   id="v20_scResultadoResponsavel"/>
                <h:panelGrid id="v20_mensagemConsultaResponsavel" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{ParcelaEmAbertoControleRel.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{ParcelaEmAbertoControleRel.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="v20_panelCliente" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500"
                     height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Cliente"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="v20_hidelink2"/>
                <rich:componentControl for="v20_panelCliente" attachTo="v20_hidelink2" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="v20_formCliente" ajaxSubmit="true" styleClass="font-size-Em-max">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza "
                                  value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="v20_consultacliente"
                                         value="#{ParcelaEmAbertoControleRel.campoConsultarCliente}">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.tipoConsultaComboCliente}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="v20_valorConsultaCliente" size="10"
                                 value="#{ParcelaEmAbertoControleRel.valorConsultarCliente}"/>

                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandLink id="v20_btnConsultarCliente" reRender="v20_formCliente"
                                         action="#{ParcelaEmAbertoControleRel.consultarCliente}"
                                         styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                         value="#{msg_bt.btn_consultar}"
                                         title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandLink id="v20_btnConsultarCliente" reRender="v20_formCliente"
                                         action="#{ParcelaEmAbertoControleRel.consultarCliente}"
                                         styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                         value="#{msg_bt.btn_consultar}"
                                         title="#{msg.msg_consultar_dados}"/>
                    </c:if>

                </h:panelGrid>

                <rich:dataTable id="v20_resultadoConsultaCliente" width="100%" styleClass="tabelaSimplesCustom"
                                rendered="#{not empty ParcelaEmAbertoControleRel.listaConsultarCliente}"
                                value="#{ParcelaEmAbertoControleRel.listaConsultarCliente}" rows="5" var="cliente">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText
                                    styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                    value="#{msg_aplic.prt_Usuario_nomePessoa}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                             action="#{ParcelaEmAbertoControleRel.selecionarCliente}" focus="cliente"
                                             reRender="v20_form" oncomplete="Richfaces.hideModalPanel('v20_panelCliente')"
                                             value="#{cliente.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                         action="#{ParcelaEmAbertoControleRel.selecionarCliente}"
                                         focus="cliente" reRender="v20_form"
                                         oncomplete="Richfaces.hideModalPanel('v20_panelCliente')"
                                         title="#{msg.msg_selecionar_dados}">
                            <span>Selecionar </span>
                            <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="v20_formCliente:v20_resultadoConsultaCliente" maxPages="10"
                                   styleClass="scrollPureCustom"
                                   id="v20_scResultadoCliente" renderIfSinglePage="false"/>
                <h:panelGrid id="v20_mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{ParcelaEmAbertoControleRel.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{ParcelaEmAbertoControleRel.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid id="panelFiltros" columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="../topoReduzido_material.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="../pages/ce/includes/topoReduzido.jsp"/>
            </c:if>

        </f:facet>

        <h:form id="v20_form" target="_blank">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:commandLink action="#{ParcelaEmAbertoControleRel.liberarBackingBeanMemoria}"
                           id="v20_idLiberarBackingBeanMemoria" style="display: none"/>
            <h:commandLink action="#{ParcelaEmAbertoControleRel.imprimirRelatorio}" id="v20_imprimirRelatorio"
                           style="display: none"/>
            <h:inputHidden id="v20_relatorio" value="#{ParcelaEmAbertoControleRel.relatorio}"/>

            <h:panelGroup styleClass="separador-horizontal" layout="block"/>
            <h:panelGrid columns="1"
                         style="margin: 10px; display: inline-flex">
                <h:panelGrid columns="1">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_Empresa}"/>
                    <h:panelGroup layout="block">
                        <h:selectOneMenu id="v20_empresas"
                                         style="width: 200px"
                                         disabled="#{!ParcelaEmAbertoControleRel.permissaoConsultaTodasEmpresas}"
                                         styleClass="inputs" value="#{ParcelaEmAbertoControleRel.filtroEmpresa}">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.listaEmpresas}"/>
                            <a4j:support action="#{ParcelaEmAbertoControleRel.carregarPlanoseConvenios}" reRender="v20_form"
                                         event="onchange"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="v20_groupForm" columns="6" style="display: inline-flex; margin: 10px;">

                <h:panelGrid columns="1" cellspacing="0" cellpadding="0">
                    <h:panelGrid columns="1">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_periodoVencimento}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="2">
                        <h:panelGroup style="font-size: 11px !important;" layout="block">
                            <rich:calendar id="v20_dataInicioVencimento"
                                           value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.dataInicioVencimento}"
                                           buttonClass="iconeDentro"
                                           inputClass="form inputs width100"
                                           buttonIcon="/imagens_flat/icon-calendar-check.png"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="v20_dataInicioVencimento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:panelGroup style="font-size: 11px !important;" layout="block">
                            <rich:calendar id="v20_dataTerminoVencimento"
                                           value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.dataTerminoVencimento}"
                                           buttonClass="iconeDentro"
                                           inputClass="form inputs width100"
                                           buttonIcon="/imagens_flat/icon-calendar-check.png"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="v20_dataTerminoVencimento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="1" cellspacing="0" cellpadding="0">
                    <h:panelGrid columns="1">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_periodoFaturamento}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="2">
                        <h:panelGroup style="font-size: 11px !important;" layout="block">
                            <rich:calendar id="v20_dataInicioFaturamento"
                                           value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.dataInicioFaturamento}"
                                           buttonClass="iconeDentro"
                                           inputClass="form inputs width100"
                                           buttonIcon="/imagens_flat/icon-calendar-check.png"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="v20_dataInicioFaturamento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:panelGroup style="font-size: 11px !important;" layout="block">
                            <rich:calendar id="v20_dataTerminoFaturamento"
                                           value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.dataTerminoFaturamento}"
                                           buttonClass="iconeDentro"
                                           inputClass="form inputs width100"
                                           buttonIcon="/imagens_flat/icon-calendar-check.png"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="v20_dataTerminoFaturamento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="1" cellspacing="0" cellpadding="0">
                    <h:panelGrid columns="1">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_periodoPagamento}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="2">
                        <h:panelGroup style="font-size: 11px !important;" layout="block">
                            <rich:calendar id="v20_dataInicioPagamento"
                                           value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.dataInicioPagamento}"
                                           buttonClass="iconeDentro"
                                           inputClass="form inputs width100"
                                           buttonIcon="/imagens_flat/icon-calendar-check.png"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="v20_dataInicioPagamento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:panelGroup style="font-size: 11px !important;" layout="block">
                            <rich:calendar id="v20_dataTerminoPagamento"
                                           value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.dataTerminoPagamento}"
                                           buttonClass="iconeDentro"
                                           inputClass="form inputs width100"
                                           buttonIcon="/imagens_flat/icon-calendar-check.png"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="v20_dataTerminoPagamento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" cellspacing="0" cellpadding="0">
                    <h:panelGrid columns="1">
                        <h:outputText styleClass="tituloCampos" value="Pesquisa por data de cancelamento"/>
                    </h:panelGrid>
                    <h:panelGrid columns="2">
                        <h:panelGroup style="font-size: 11px !important;" layout="block">
                            <rich:calendar id="v20_dataInicioCancelamento"
                                           value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.dataInicioCancelamento}"
                                           buttonClass="iconeDentro"
                                           inputClass="form inputs width100"
                                           buttonIcon="/imagens_flat/icon-calendar-check.png"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="v20_dataInicioCancelamento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:panelGroup style="font-size: 11px !important;" layout="block">
                            <rich:calendar id="v20_dataTerminoCancelamento"
                                           value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.dataTerminoCancelamento}"
                                           buttonClass="iconeDentro"
                                           inputClass="form inputs width100"
                                           buttonIcon="/imagens_flat/icon-calendar-check.png"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="v20_dataInicioCancelamento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid id="v20_grpSituacoes" columns="2" cellpadding="0">
                    <h:panelGrid columns="1">
                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_ParcelaEmAberto_Situacao}"/>
                        <h:panelGroup layout="block">
                            <h:selectOneMenu id="v20_situacao" styleClass="inputs"
                                             value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.situacao}"
                                             style="float: left; font-size: 12px !important; width: 100%;">
                                <f:selectItems
                                        value="#{ParcelaEmAbertoControleRel.listaSelectItemTipoSituacao}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="3" style="padding-top: 15px;">
                        <a4j:commandButton id="v20_adicionarSituacaoSelecionado"
                                           action="#{ParcelaEmAbertoControleRel.adicionarSituacaoNaLista}"
                                           reRender="v20_groupForm, v20_situacao, v20_mensagemConsultaResponsavel, v20_grpSituacoes"
                                           image="../imagens/botaoAdicionar.png" styleClass="botoes"/>

                        <h:panelGrid id="v20_qtdSituacoes" cellpadding="0" cellspacing="0" columns="2">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="notificacaoAtividades">
                                    <h:outputText
                                            value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.qtdSituacoesSelecionadas}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                            <rich:toolTip
                                    value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.situacoesSelecionadasApresentar}"
                                    for="v20_qtdSituacoes" followMouse="true"/>
                        </h:panelGrid>

                        <a4j:commandButton id="v20_limparSituacoesSelecionadas" image="../imagens/limpar.gif"
                                           reRender="v20_groupForm, v20_situacao, v20_mensagemConsultaResponsavel, v20_grpSituacoes"
                                           styleClass="botoes"
                                           action="#{ParcelaEmAbertoControleRel.limparSituacoes}"/>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="2" style="padding-top: 20px; width: 100%;" columnClasses="col35,col50">
                    <a4j:commandLink id="filtros"
                                     ajaxSingle="false"
                                     styleClass="botaoPrimario texto-size-14 btn-expand50"
                                     reRender="v20_form"
                                     action="#{ParcelaEmAbertoControleRel.mostrarMaisFiltros}">
                        <i title="Mais Filtros"
                           style="line-height: normal !important; padding-right: 5px;"
                           class="tooltipster fa-icon-filter bi-link"></i>

                        <h:panelGroup layout="block" rendered="#{!ParcelaEmAbertoControleRel.mostrarMenuFiltro}">
                            <i title="Mais Filtros"
                               style="line-height: normal !important; font-size: 12px;"
                               class="tooltipster fa-icon-caret-down bi-link"></i>
                        </h:panelGroup>
                        <h:panelGroup layout="block" rendered="#{ParcelaEmAbertoControleRel.mostrarMenuFiltro}">
                            <i title="Mais Filtros"
                               style="line-height: normal !important; font-size: 12px;"
                               class="tooltipster fa-icon-caret-up bi-link"></i>
                        </h:panelGroup>
                    </a4j:commandLink>
                    <a4j:commandLink id="consultar"
                                     ajaxSingle="false"
                                     action="#{ParcelaEmAbertoControleRel.consultarParcelas}"
                                     styleClass="botaoPrimario texto-size-14 btn-expand60"
                                     reRender="v20_form, v20_GridListagemParcelas"
                                     oncomplete="#{ParcelaEmAbertoControleRel.mensagemNotificar}"
                                     value="Consultar">
                    </a4j:commandLink>
                </h:panelGrid>

            </h:panelGrid>

            <h:panelGrid columns="5" rendered="#{ParcelaEmAbertoControleRel.mostrarMenuFiltro}"
                         style="margin: 10px; display: inline-flex">

                <h:panelGrid columns="1" style="width: 100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_nomeOperador}"/>
                    <h:panelGroup>
                        <h:inputText id="v20_nomeOperador" size="40" maxlength="40" styleClass="inputs"
                                     value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.colaboradorVO.pessoa.nome}"/>
                        <a4j:commandButton id="v20_consultarOperador"
                                           oncomplete="Richfaces.showModalPanel('v20_panelResponsavel'), setFocus(v20_formResponsavel,'v20_formResponsavel:v20_valorConsultarResponsavel')"
                                           alt="Consultar Operador" image="../imagens/informacao.gif"/>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton id="v20_LimparOperador" image="../imagens/limpar.gif"
                                           reRender="v20_nomeOperador"
                                           action="#{ParcelaEmAbertoControleRel.limparCampoOperador}"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_ParcelaEmAberto_parcelasCanceladas}"/>
                    <h:panelGroup layout="block">
                        <h:selectOneMenu id="v20_parcelasCanceladas" styleClass="inputs"
                                         style="font-size: 12px !important; width: 100%;"
                                         value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.parcelaCancelada}">
                            <f:selectItems
                                    value="#{ParcelaEmAbertoControleRel.listaSelectItemParcelaCancelada}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1" style="width: 100%">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_ParcelaEmAberto_recorrenciasomente}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="v20_recorrencia" styleClass="inputs"
                                         value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.parcelasRecorrencia}">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.listaSelectItemRecorrencia}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1" style="width: 100%">
                    <h:outputText style="vertical-align:middle;" styleClass="tituloCampos"
                                  value="Forma de Pagamento:"/>
                    <h:selectOneMenu style="vertical-align:middle;" id="v20_comboFormas" styleClass="inputs"
                                     value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.formaPagamentoSelecionado}">
                        <f:selectItems value="#{ParcelaEmAbertoControleRel.selectItemsFormaDePagamento}"/>
                        <a4j:support reRender="v20_form" event="onchange"/>
                    </h:selectOneMenu>
                </h:panelGrid>
                <h:panelGrid columns="1" style="width: 100%">
                    <h:outputText style="vertical-align:middle;" styleClass="tituloCampos"
                                  value="Alunos com Autorização de Cobrança"/>
                    <h:selectOneMenu style="vertical-align:middle;" id="v20_comboConvenios" styleClass="inputs"
                                     value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.convenio.codigo}">
                        <f:selectItems value="#{ParcelaEmAbertoControleRel.convenios}"/>
                        <a4j:support reRender="v20_form" event="onchange"/>
                    </h:selectOneMenu>
                </h:panelGrid>
                <h:panelGrid columns="1" style="width: 100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_Clientenome}"/>
                    <h:panelGroup>
                        <h:inputText id="v20_nomeCliente" size="40" maxlength="40" styleClass="inputs"
                                     value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.clienteVO.pessoa.nome}"/>
                        <a4j:commandButton id="v20_consultarCliente"
                                           oncomplete="Richfaces.showModalPanel('v20_panelCliente'), setFocus(formCliente,'v20_formCliente:v20_valorConsultarCliente')"
                                           alt="Consultar Operador" image="../imagens/informacao.gif"/>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton id="v20_LimparCliente" image="../imagens/limpar.gif"
                                           reRender="v20_form:v20_nomeCliente"
                                           action="#{ParcelaEmAbertoControleRel.limparCampoCliente}"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1" style="width: 100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_situacaoCliente}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="v20_situacaoCliente" styleClass="inputs"
                                         value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.situacaoCliente}">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.listaSelectItemSituacaoCliente}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1" style="width: 100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_plano}"/>
                    <h:panelGroup id="v20_grpPlano">
                        <h:selectOneMenu id="v20_plano" styleClass="inputs"
                                         value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.codigoPlano}"
                                         style="float: left">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.listaSelectItemPlano}"/>
                        </h:selectOneMenu>

                        <a4j:commandButton id="v20_adicionarPlanoSelecionado"
                                           action="#{ParcelaEmAbertoControleRel.adicionarPlanoNaLista}"
                                           style="float: left"
                                           reRender="v20_groupForm, v20_plano, v20_mensagemConsultaResponsavel, v20_grpPlano"
                                           image="../imagens/botaoAdicionar.png" styleClass="botoes"/>

                        <h:panelGrid cellpadding="0" cellspacing="0" columns="2" id="v20_qtd"
                                     style="float: left; margin-top: 3px; margin-left: 5px">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="notificacaoAtividades">
                                    <h:outputText
                                            value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.qtdPlanosSelecionados}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>
                        <rich:toolTip
                                value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.planosSelecionadosApresentar}"
                                for="v20_qtd"
                                followMouse="true"/>

                        <a4j:commandButton id="v20_limparPlanosSelecionados" image="../imagens/limpar.gif"
                                           reRender="v20_groupForm, v20_plano, v20_mensagemConsultaResponsavel, v20_grpPlano"
                                           styleClass="botoes"
                                           style="vertical-align: top; margin-top: 6px; margin-left: 6px; float: left"
                                           action="#{ParcelaEmAbertoControleRel.limparPlanos}"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1" style="width: 100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_horario}"/>
                    <h:panelGroup id="v20_grpHorarios">
                        <h:selectOneMenu id="v20_horario"
                                         styleClass="inputs"
                                         value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.codigoHorario}"
                                         style="float: left">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.listaSelectItemHorariosPlano}"/>
                        </h:selectOneMenu>

                        <a4j:commandButton id="v20_adicionarHorarioSelecionado"
                                           action="#{ParcelaEmAbertoControleRel.adicionarHorarioNaLista}"
                                           style="float: left"
                                           reRender="v20_groupForm, v20_horario, v20_mensagemConsultaResponsavel, v20_grpHorarios"
                                           image="../imagens/botaoAdicionar.png" styleClass="botoes"/>

                        <h:panelGrid id="v20_qtdHorarios" cellpadding="0" cellspacing="0" columns="2"
                                     style="float: left; margin-top: 3px; margin-left: 5px">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="notificacaoAtividades">
                                    <h:outputText
                                            value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.qtdHorariosSelecionados}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>
                        <rich:toolTip
                                value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.horariosSelecionadosApresentar}"
                                for="v20_qtdHorarios" followMouse="true"/>

                        <a4j:commandButton id="v20_limparHorariosSelecionados" image="../imagens/limpar.gif"
                                           reRender="v20_groupForm, v20_horario, v20_mensagemConsultaResponsavel, v20_grpHorarios"
                                           styleClass="botoes"
                                           style="vertical-align: top; margin-top: 6px; margin-left: 6px; float: left"
                                           action="#{ParcelaEmAbertoControleRel.limparHorarios}"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" style="width: 100%">
                    <h:outputText styleClass="tituloCampos" value="Modalidade"/>
                    <h:panelGroup id="v20_grpModalidades">
                        <h:selectOneMenu id="v20_modalidade"
                                         styleClass="inputs"
                                         value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.codigoModalidade}"
                                         style="float: left">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.listaSelectItemModalidade}"/>
                        </h:selectOneMenu>

                        <a4j:commandButton id="v20_adicionarModalidadeSelecionada"
                                           action="#{ParcelaEmAbertoControleRel.adicionarModalidadeNaLista}"
                                           style="float: left"
                                           reRender="v20_groupForm, v20_modalidade, v20_mensagemConsultaResponsavel, v20_grpModalidades"
                                           image="../imagens/botaoAdicionar.png" styleClass="botoes"/>

                        <h:panelGrid id="v20_qtdModalidades" cellpadding="0" cellspacing="0" columns="2"
                                     style="float: left; margin-top: 3px; margin-left: 5px">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="notificacaoAtividades">
                                    <h:outputText
                                            value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.qtdModalidadesSelecionadas}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>
                        <rich:toolTip
                                value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.modalidadeSelecionadasApresentar}"
                                for="v20_qtdModalidades" followMouse="true"/>

                        <a4j:commandButton id="v20_limparModalidadesSelecionadas" image="../imagens/limpar.gif"
                                           reRender="v20_groupForm, v20_modalidade, v20_mensagemConsultaResponsavel, v20_grpModalidades"
                                           styleClass="botoes"
                                           style="vertical-align: top; margin-top: 6px; margin-left: 6px; float: left"
                                           action="#{ParcelaEmAbertoControleRel.limparModalidades()}"/>
                    </h:panelGroup>
                </h:panelGrid>
<%--                TURMA--%>
                <h:panelGrid columns="1" style="width: 100%">
                    <h:outputText styleClass="tituloCampos" value="Turma"/>
                    <h:panelGroup id="v20_grpTurmas">
                        <h:selectOneMenu id="v20_turma"
                                         styleClass="inputs"
                                         value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.codigoTurma}"
                                         style="float: left">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.listaSelectItemTurma}"/>
                        </h:selectOneMenu>

                        <a4j:commandButton id="v20_adicionarTurmaSelecionada"
                                           action="#{ParcelaEmAbertoControleRel.adicionarTurmaNaLista()}"
                                           style="float: left"
                                           reRender="v20_groupForm, v20_turma, v20_mensagemConsultaResponsavel, v20_grpTurmas"
                                           image="../imagens/botaoAdicionar.png" styleClass="botoes"/>

                        <h:panelGrid id="v20_qtdTurmas" cellpadding="0" cellspacing="0" columns="2"
                                     style="float: left; margin-top: 3px; margin-left: 5px">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="notificacaoAtividades">
                                    <h:outputText
                                            value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.qtdTurmasSelecionadas}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>
                        <rich:toolTip
                                value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.turmaSelecionadasApresentar}"
                                for="v20_qtdTurmas" followMouse="true"/>

                        <a4j:commandButton id="v20_limparTurmasSelecionadas" image="../imagens/limpar.gif"
                                           reRender="v20_groupForm, v20_turma, v20_mensagemConsultaResponsavel, v20_grpTurmas"
                                           styleClass="botoes"
                                           style="vertical-align: top; margin-top: 6px; margin-left: 6px; float: left"
                                           action="#{ParcelaEmAbertoControleRel.limparTurmas()}"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="2">
                    <h:selectBooleanCheckbox
                            disabled="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.convenio.codigo == null ||
                                    ParcelaEmAbertoControleRel.parcelaEmAbertoRel.convenio.codigo <= 0}"
                            value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.ignorarParcelasSemRetorno}"/>
                    <h:outputText style="vertical-align:middle;" styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_ignorarremessasemretorno}"/>
                </h:panelGrid>
                <h:panelGrid columns="2">
                    <h:selectBooleanCheckbox
                            value="#{ParcelaEmAbertoControleRel.mostrarNrTentativasMovitoRetorno}">
                        <a4j:support reRender="v20_GridListagemParcelas" event="onchange"/>
                    </h:selectBooleanCheckbox>
                    <h:outputText style="vertical-align:middle;" styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_mostrarNrtentativasMotivoRetorno}"/>
                </h:panelGrid>
                <h:panelGrid columns="2">
                    <h:panelGroup id="v20_multasJuros">
                        <h:selectBooleanCheckbox id="v20_checkMultasJuros"
                                                 disabled="#{!ParcelaEmAbertoControleRel.empresaLogado.cobrarAutomaticamenteMultaJuros}"
                                                 value="#{ParcelaEmAbertoControleRel.gerarMultasJuros}"
                                                 style="float: left"/>
                        <a4j:support event="onchange" reRender="btnExcel"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  value="Gerar Multas e Juros"
                                  title="Este campo fica disponível quando a cobrança automática de multas e juros está habilitada."/>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid columns="4" cellpadding="0" cellspacing="0">
                <h:panelGrid columns="2"
                             style="font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 10px; width: 250px; box-shadow: 0px 2px 4px #E4E5E6;"
                             cellspacing="0" cellpadding="0">

                    <h:outputText styleClass="tituloCampos" value="TOTAL DE ALUNOS"
                                  style="color: black; float: left"></h:outputText>
                    <h:outputText styleClass="tituloCampos" value="#{ParcelaEmAbertoControleRel.totalAlunos}"
                                  style="float: right"></h:outputText>

                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>
                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>

                    <h:outputText styleClass="tituloCampos" value="TOTAL DE PARCELAS"
                                  style="color: black; float: left"></h:outputText>
                    <h:outputText styleClass="tituloCampos" value="#{ParcelaEmAbertoControleRel.qtdTotalParcelas}"
                                  style="color: black; float: right"></h:outputText>

                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>
                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>

                    <h:outputText styleClass="tituloCampos" value="TOTAL"
                                  style="color: black; float: left; font-weight: bold"></h:outputText>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{EmpresaControle.empresaLogado.moeda} #{ParcelaEmAbertoControleRel.valorTotalParcelas_Apresentar}"
                                  style="color: black; float: right; font-weight: bold"></h:outputText>

                </h:panelGrid>
                <h:panelGrid columns="2"
                             style="display: inline-flex;
    float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #DB2C3D; box-sizing: border-box;"
                             cellspacing="0" cellpadding="0">
                    <h:graphicImage url="/images/pct-repeat.jpg"
                                    style="padding-left: 40px; padding-right: 35px;"></h:graphicImage>
                    <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                        <h:outputText value="Aberto" style="font-size: 16px; color: #BDC3C7; "></h:outputText>
                        <h:outputText value="#{ParcelaEmAbertoControleRel.qtdeParcelasEmAberto}"
                                      style="font-weight: bold; font-size: 32px; color: #51555A;"></h:outputText>
                        <h:outputText
                                value="#{EmpresaControle.empresaLogado.moeda} #{ParcelaEmAbertoControleRel.valorTotalEmAberto_Apresentar}"
                                style="background-color: #EB5757; font-weight: bold; font-size: 12px; color: #FFFFFF; padding: 1px 7px 1px 7px;"></h:outputText>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="2"
                             style="display: inline-flex;
    float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #81D742; box-sizing: border-box;"
                             cellspacing="0" cellpadding="0">
                    <h:graphicImage url="/images/pct-check-circle.jpg"
                                    style="padding-left: 40px; padding-right: 35px;"></h:graphicImage>
                    <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                        <h:outputText value="Pago" style="font-size: 16px; color: #BDC3C7; "></h:outputText>
                        <h:outputText value="#{ParcelaEmAbertoControleRel.qtdeParcelasPago}"
                                      style="font-weight: bold; font-size: 32px; color: #51555A;"></h:outputText>
                        <h:outputText
                                value="#{EmpresaControle.empresaLogado.moeda} #{ParcelaEmAbertoControleRel.valorTotalPago_Apresentar}"
                                style="background-color: #81D742; font-weight: bold; font-size: 12px; color: #FFFFFF; padding: 1px 7px 1px 7px;"></h:outputText>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="2"
                             style="display: inline-flex;
    float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #80858C; box-sizing: border-box;"
                             cellspacing="0" cellpadding="0">
                    <h:graphicImage url="/images/pct-flag.jpg"
                                    style="padding-left: 40px; padding-right: 35px;"></h:graphicImage>
                    <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                        <h:outputText value="Cancelado" style="font-size: 16px; color: #BDC3C7; "></h:outputText>
                        <h:outputText value="#{ParcelaEmAbertoControleRel.qtdeParcelasCancelado}"
                                      style="font-weight: bold; font-size: 32px; color: #51555A;"></h:outputText>
                        <h:outputText
                                value="#{EmpresaControle.empresaLogado.moeda} #{ParcelaEmAbertoControleRel.valorTotalCancelado_Apresentar}"
                                style="background-color: #80858C; font-weight: bold; font-size: 12px; color: #FFFFFF; padding: 1px 7px 1px 7px;"></h:outputText>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGroup id="v20_GridListagemParcelas" >
                <jsp:include page="/relatorio/include_Listagem_Parcelas.jsp"/>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>
</f:view>

<script>
    window.onload = iniciar();
    document.getElementById("v20_form:v20_dataInicioVencimento").focus();
</script>
