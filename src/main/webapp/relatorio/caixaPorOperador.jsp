<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<title>Relatório de Fechamento de Caixa por Operador</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="aj4" uri="http://richfaces.org/a4j" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<c:set var="iconeWikiEquivalentes" scope="request" value="true"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_CaixaPorOperador_tituloRel}"/>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlBaseConhecimento}como-verificar-o-relatorio-de-fechamento-de-caixa-por-operador/"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <rich:modalPanel id="panelColaborador" autosized="true" shadowOpacity="true" width="300" height="250">
<%--        <f:facet name="header">--%>
<%--            <jsp:include page="../topoReduzido_material_semUCP.jsp"/>--%>
<%--        </f:facet>--%>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Operador"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink1"/>
                <rich:componentControl for="panelColaborador" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formColaborador" ajaxSubmit="true">
            <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                         width="100%">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu id="consultacolaborador"
                                     value="#{CaixaPorOperadorRelControleRel.campoConsultarOperador}">
                        <f:selectItems value="#{CaixaPorOperadorRelControleRel.tipoConsultaComboOperador}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultaColaborador" size="10"
                                 value="#{CaixaPorOperadorRelControleRel.valorConsultarOperador}"/>

                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandButton id="btnConsultarColaborador" reRender="formColaborador:mensagemConsultaColaborador,
                                            formColaborador:resultadoConsultaColaborador, formColaborador:scResultadoColaborador"
                                           action="#{CaixaPorOperadorRelControleRel.consultarOperador}"
                                           styleClass="botoes"
                                           value="#{msg_bt.btn_consultar}" image="../imagens/botaoConsultar.png"
                                           alt="#{msg.msg_consultar_dados}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandButton id="btnConsultarColaborador"
                                           reRender="formColaborador:mensagemConsultaColaborador, formColaborador:resultadoConsultaColaborador, formColaborador:scResultadoColaborador"
                                           action="#{CaixaPorOperadorRelControleRel.consultarOperador}"
                                           styleClass="botoes"
                                           value="#{msg_bt.btn_consultar}" image="../imagens/botoesCE/buscar.png"
                                           alt="#{msg.msg_consultar_dados}"/>
                    </c:if>

                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaColaborador" width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{CaixaPorOperadorRelControleRel.listaConsultarOperador}" rows="5"
                                var="colaborador">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Usuario_colaborador}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{CaixaPorOperadorRelControleRel.selecionarOperador}"
                                             focus="colaborador" reRender="form"
                                             oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                             value="#{colaborador.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{CaixaPorOperadorRelControleRel.selecionarOperador}"
                                           focus="colaborador" reRender="form"
                                           oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                           value="#{msg_bt.btn_selecionar}" image="../imagens/botaoEditar.png"
                                           alt="#{msg.msg_selecionar_dados}" styleClass="botoes"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formColaborador:resultadoConsultaColaborador" maxPages="10"
                                   id="scResultadoColaborador"/>
                <h:panelGrid id="mensagemConsultaColaborador" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{CaixaPorOperadorRelControleRel.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{CaixaPorOperadorRelControleRel.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel width="350" id="panelStatusRealizandoConsulta" autosized="true"
                     styleClass="modal-carregando-ripple"
                     showWhenRendered="#{CaixaPorOperadorRelControleRel.emProcessamento}">
        <div class="loader-inner ball-scale-ripple-multiple">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
        <h:panelGroup layout="block" styleClass="textoImcompleto" style="width: 100%;">
            <h:outputText id="mensagemBICarregando"
                          style="color:#094771;font-style: italic;line-height: 6;padding: 10px;background-color: #fff;border:1px solid #094771;border-radius: 3px;"
                          styleClass="texto-font texto-size-20 texto-cor-cinza mensagemBICarregando"
                          value="    Gerando dados para o Relatório... isso pode levar alguns segundos..."/>
        </h:panelGroup>
    </rich:modalPanel>
    <a4j:status id="statusRealizandoConsulta" forceId="true"
                onstart="Richfaces.showModalPanel('panelStatusRealizandoConsulta');"
                onstop="#{rich:component('panelStatusRealizandoConsulta')}.hide();"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <a4j:poll id="pollConsulta" interval="5000" status="none"
                      enabled="#{CaixaPorOperadorRelControleRel.pollEnabled}"
                      reRender="pollConsulta,panelStatusRealizandoConsulta"
                      oncomplete="#{CaixaPorOperadorRelControleRel.nomeRefRelatorioGeradoAgora}"/>

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:commandLink action="#{CaixaPorOperadorRelControleRel.liberarBackingBeanMemoria}"
                           id="idLiberarBackingBeanMemoria" style="display: none"/>
            <h:commandLink action="#{CaixaPorOperadorRelControleRel.imprimirRelatorio}" id="imprimirRelatorio"
                           style="display: none"/>
            <h:inputHidden id="relatorio" value="#{CaixaPorOperadorRelControleRel.relatorio}"/>
            <h:panelGrid columns="1" width="100%">
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{CaixaPorOperadorRelControleRel.permissaoConsultaTodasEmpresas}"
                                  value="#{msg_aplic.prt_CompetenciaSintetico_empresa}"/>
                    <h:panelGroup layout="block"
                                  rendered="#{CaixaPorOperadorRelControleRel.permissaoConsultaTodasEmpresas}">
                        <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{CaixaPorOperadorRelControleRel.filtroEmpresa}">
                            <f:selectItems value="#{CaixaPorOperadorRelControleRel.listaEmpresas}"/>
                            <a4j:support event="onchange" reRender="form"
                                         action="#{CaixaPorOperadorRelControleRel.montarListaSelectItemConvenio}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CaixaPorOperador_periodoPesquisa}"/>
                    <h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataInicio"
                                           value="#{CaixaPorOperadorRelControleRel.caixaPorOperadorRel.dataInicio}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           disabled="#{!CaixaPorOperadorRelControleRel.relatorioTodosDias}"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <rich:spacer width="10px"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CaixaPorOperador_ate}"/>
                        <rich:spacer width="10px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{CaixaPorOperadorRelControleRel.caixaPorOperadorRel.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           disabled="#{!CaixaPorOperadorRelControleRel.relatorioTodosDias}"
                                           zindex="2"
                                           showWeeksBar="true"/>
                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                         query="mask('99/99/9999')"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" title="Busca produtos por data de lançamento"
                                  value="#{msg_aplic.prt_CaixaPorOperador_periodoFaturamentoProduto}"/>
                    <h:panelGroup id="panelFaturamentoPeriodo">
                        <h:panelGroup>
                            <rich:calendar id="dataInicioFaturamento"
                                           value="#{CaixaPorOperadorRelControleRel.caixaPorOperadorRel.dataInicioFaturamento}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           disabled="#{!CaixaPorOperadorRelControleRel.relatorioTodosDias}"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="dataInicioFaturamento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <rich:spacer width="10px"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CaixaPorOperador_ate}"/>
                        <rich:spacer width="10px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTerminoFaturamento"
                                           value="#{CaixaPorOperadorRelControleRel.caixaPorOperadorRel.dataTerminoFaturamento}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           disabled="#{!CaixaPorOperadorRelControleRel.relatorioTodosDias}"
                                           zindex="2"
                                           showWeeksBar="true"/>
                            <h:message for="dataTerminoFaturamento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton id="LimparFaturamentoPrdito" image="../imagens/limpar.gif"
                                           reRender="form:panelFaturamentoPeriodo"
                                           action="#{CaixaPorOperadorRelControleRel.limparCampoFaturamentoPeriodo}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CaixaPorOperador_faixaHorario}"/>
                    <h:panelGroup layout="block">
                        <h:inputText id="horaInicio" size="5" maxlength="5"
                                     onkeypress="return mascaraTodos(this.form, 'form:horaInicio', '00:00', event);"
                                     style="position:relative; top:0px; left:0px;" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{CaixaPorOperadorRelControleRel.caixaPorOperadorRel.horaInicio}"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CaixaPorOperador_ate}"
                                      style="position:relative; top:0px; left:10px;"/>
                        <h:inputText id="horaTermino" size="5" maxlength="5"
                                     onkeypress="return mascaraTodos(this.form, 'form:horaTermino', '00:00', event);"
                                     style="position:relative; top:0px; left:20px;" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{CaixaPorOperadorRelControleRel.caixaPorOperadorRel.horaTermino}"/>
                    </h:panelGroup>


                    <h:outputText
                            styleClass="tituloCampos"
                            value="#{msg_aplic.prt_CaixaPorOperador_nomeOperador}"/>
                    <h:panelGroup id="panelOperador">
                        <h:inputText id="nomeOperador" size="40" maxlength="40" styleClass="form"
                                     value="#{CaixaPorOperadorRelControleRel.usuarioFiltro.nome}"/>
                        <rich:suggestionbox height="200" width="270"
                                            for="nomeOperador"
                                            frequency="0"
                                            popupStyle="margin-left: 12px;background-color: #000066;"
                                            fetchValue="#{CaixaPorOperadorRelControleRel.usuarioFiltro.nome}"
                                            suggestionAction="#{CaixaPorOperadorRelControleRel.executarAutocompleteFuncionalidade}"
                                            minChars="1" rowClasses="20"
                                            status="true"
                                            nothingLabel="Nenhuma funcionalidade Encontrada!"
                                            var="colaborador" id="sugestion">
                            <a4j:support event="onselect"
                                         action="#{CaixaPorOperadorRelControleRel.selecionarOperador}"
                                         reRender="form,nomeOperador,sugestion"></a4j:support>
                            <h:column>
                                <h:outputText value="#{colaborador.nome}"/>
                            </h:column>
                        </rich:suggestionbox>
                        <%--<a4j:commandButton id="consultarOperador" oncomplete="Richfaces.showModalPanel('panelColaborador'), setFocus(formColaborador,'formColaborador:valorConsultarOperador')" alt="Consultar Operador" image="../imagens/informacao.gif" />--%>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton id="LimparOperador" image="../imagens/limpar.gif"
                                           reRender="form,form:panelOperador"
                                           action="#{CaixaPorOperadorRelControleRel.limparCampoOperador}"/>
                    </h:panelGroup>


                    <c:if test="${CaixaPorOperadorRelControleRel.usuarioFiltro.codigo == 0}">
                        <h:outputText
                                styleClass="tituloCampos"
                                value="Considerar usuário ADMIN:"
                                title="Esse usuário é utilizado para registrar as vendas e recebimentos feitos pelo vendas online e em alguns outros processos internos do sistema"/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox
                                    value="#{CaixaPorOperadorRelControleRel.considerarUsuarioAdmin}"
                                    id="considerarUsuarioAdmin"
                                    title="Esse usuário é utilizado para registrar as vendas e recebimentos feitos pelo vendas online e em alguns outros processos internos do sistema">
                                <a4j:support event="onchange" reRender="form"/>
                            </h:selectBooleanCheckbox>
                        </h:panelGroup>

                        <h:outputText
                                styleClass="tituloCampos"
                                value="Considerar usuário RECORRENCIA:"
                                title="Esse usuário é utilizado pelo sistema para fazer as cobranças automáticas"/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox
                                    value="#{CaixaPorOperadorRelControleRel.considerarUsuarioRecorrencia}"
                                    id="considerarUsuarioRecorrencia"
                                    title="Esse usuário é utilizado pelo sistema para fazer as cobranças automáticas">
                                <a4j:support event="onchange" reRender="form"/>
                            </h:selectBooleanCheckbox>
                        </h:panelGroup>
                    </c:if>


                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CaixaPorOperador_tipoComprador}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="tipoComprador" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{CaixaPorOperadorRelControleRel.caixaPorOperadorRel.tipoComprador}">
                            <f:selectItems value="#{CaixaPorOperadorRelControleRel.listaSelectItemTipoComprador}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText rendered="#{LoginControle.apresentarLinkCE}" styleClass="tituloCampos"
                                  value="Fonte de dados"/>
                    <h:panelGroup rendered="#{LoginControle.apresentarLinkCE}">
                        <h:selectOneMenu id="fonteDados" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{CaixaPorOperadorRelControleRel.caixaPorOperadorRel.fonteDados}">
                            <f:selectItems value="#{CaixaPorOperadorRelControleRel.listaFonteDados}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <c:if test="${!CaixaPorOperadorRelControleRel.somenteSintetico}">
                        <h:outputText styleClass="tituloCampos" value="Layout Livro Caixa:"/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox value="#{CaixaPorOperadorRelControleRel.layoutLivroCaixa}"
                                                     id="layoutLivroCaixaFechamentocaixa">
                                <a4j:support event="onchange" reRender="form"/>
                            </h:selectBooleanCheckbox>
                        </h:panelGroup>
                    </c:if>

                    <c:if test="${!CaixaPorOperadorRelControleRel.somenteSintetico and !CaixaPorOperadorRelControleRel.layoutImpressaoTermicaResumido}">
                        <h:outputText styleClass="tituloCampos" value="Layout Impressão Térmica:"/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox value="#{CaixaPorOperadorRelControleRel.layoutImpressaoTermica}"
                                                     id="layoutImpressaoTermica">
                                <a4j:support event="onchange" reRender="form"/>
                            </h:selectBooleanCheckbox>
                        </h:panelGroup>
                    </c:if>

                    <c:if test="${!CaixaPorOperadorRelControleRel.somenteSintetico and !CaixaPorOperadorRelControleRel.layoutImpressaoTermica}">
                        <h:outputText styleClass="tituloCampos" value="Layout Impressão Térmica Resumido:"/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox value="#{CaixaPorOperadorRelControleRel.layoutImpressaoTermicaResumido}"
                                                     id="layoutImpressaoTermicaResumido">
                                <a4j:support event="onchange" reRender="form"/>
                            </h:selectBooleanCheckbox>
                        </h:panelGroup>
                    </c:if>

                    <c:if test="${!CaixaPorOperadorRelControleRel.layoutLivroCaixa and !CaixaPorOperadorRelControleRel.layoutImpressaoTermica and !CaixaPorOperadorRelControleRel.layoutImpressaoTermicaResumido}">
                        <h:outputText styleClass="tituloCampos" value="Somente totalizadores:"/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox value="#{CaixaPorOperadorRelControleRel.somenteSintetico}"
                                                     id="somenteTotalizadoresFechamentoCaixa">
                                <a4j:support event="onchange" reRender="form"/>
                            </h:selectBooleanCheckbox>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CaixaPorOperador_ordenacao}"/>
                        <h:panelGroup>
                            <h:selectOneRadio id="ordenacao" styleClass="tituloCampos"
                                              value="#{CaixaPorOperadorRelControleRel.caixaPorOperadorRel.ordenacao}">
                                <f:selectItems value="#{CaixaPorOperadorRelControleRel.listaSelectItemOrdenacao}"/>
                            </h:selectOneRadio>
                        </h:panelGroup>
                    </c:if>

                    <aj4:region>
                        <h:outputText styleClass="tituloCampos" value="Convênios de Cobrança:"
                                      rendered="#{CaixaPorOperadorRelControleRel.apresentarConvenioCobranca}"/>
                    </aj4:region>
                    <h:panelGroup>
                        <h:selectManyCheckbox styleClass="tituloCampos" layout="pageDirection" style="text-align: left;"
                                              value="#{CaixaPorOperadorRelControleRel.conveniosSelecionados}"
                                              rendered="#{CaixaPorOperadorRelControleRel.apresentarConvenioCobranca}">
                            <f:selectItems value="#{CaixaPorOperadorRelControleRel.listaConvenioCobranca}"/>
                        </h:selectManyCheckbox>
                    </h:panelGroup>
                    <aj4:region>
                    </aj4:region>

                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem" value="#{CaixaPorOperadorRelControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{CaixaPorOperadorRelControleRel.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid id="botoes" columns="1" width="100%" columnClasses="colunaCentralizada">
                        <rich:spacer width="20px"/>
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <a4j:commandLink id="imprimirPDF"
                                             styleClass="botoes nvoBt"
                                             action="#{CaixaPorOperadorRelControleRel.imprimirPDF}"
                                             oncomplete="#{CaixaPorOperadorRelControleRel.nomeRefRelatorioGeradoAgora}"
                                             accesskey="2"
                                             status="statusRealizandoConsulta"
                                             reRender="mensagemConsultaColaborador, mensagem">
                                <i class="fa-icon-print"></i> &nbsp Gerar Relatório
                            </a4j:commandLink>
                        </c:if>
                        <c:if test="${modulo eq 'centralEventos'}">
                            <a4j:commandLink id="imprimirPDF"
                                             styleClass="botoes nvoBt"
                                             action="#{CaixaPorOperadorRelControleRel.imprimirPDF}"
                                             oncomplete="#{CaixaPorOperadorRelControleRel.nomeRefRelatorioGeradoAgora}"
                                             accesskey="2"
                                             reRender="mensagemConsultaColaborador, mensagem">
                                <i class="fa-icon-print"></i> &nbsp Gerar Relatório
                            </a4j:commandLink>
                        </c:if>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>
