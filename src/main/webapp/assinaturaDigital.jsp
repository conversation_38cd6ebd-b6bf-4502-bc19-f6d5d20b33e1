<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@200;300&display=swap" rel="stylesheet">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link href="./qrcode.css" rel="stylesheet" type="text/css">
    <%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
    <%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
    <%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
    <%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
    <%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
    <%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
    <c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
    <title>Assinatura Contrato</title>

    <link href="./css/pacto_flat.css" rel="stylesheet" type="text/css">
    <link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
    <script src="script/packJQueryPlugins.min.js" type="text/javascript" ></script>
</head>
<script>
    function contratoOuTokenNaoExistente() {
        alert("Contrato expirado ou n�o existente! Realize o envio do contrato novamente para assin�-lo!");
    }

    function erroEnvioEmailConfirmacao() {
        alert("Seu contrato foi assinado com sucesso, entretanto n�o foi poss�vel enviar o email de confirma��o de assinatura! Por favor, entre em contato com a academia!");
    }


    function nomeOuEmailIncorreto() {
        alert("CPF ou Email incorreto! Verifique os dados e tente novamente!");
    }

    function contratoValidado() {
        alert("Contrato Assinado com sucesso! Em breve, voc� receber� no email a confirma��o da assinatura!");
    }

    function getPlaceHolder() {
        document.getElementById('form:cpf').setAttribute("placeholder", "Informe seu CPF");
        document.getElementById('form:email').setAttribute("placeholder", "Informe seu e-mail");
    }

    function submitIp() {
        jQuery.ajax({
            type: "GET",
            url: 'https://app.pactosolucoes.com.br/ip/v2.php',
            dataType: 'text',
            success: function (valor) {
                console.log("IP: " + valor);
                enviarIp(valor);
            }
        });
    }
</script>
<style>
    .tabelaProduto tbody tr td:nth-child(6) {
        text-align: right;
    }
</style>
<body>
<f:view>
    <c:if test="${not AssinaturaDigitalControle.tudoCerto}">
        <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    </c:if>
    <h:form id="form">
        <a4j:keepAlive beanName="AssinaturaDigitalControle"/>
    <div id="container-principal"
         style="position: fixed;top: 0;left: 0;width: 100%;height: 30%;background-color: #026ABC;">
        <c:if test="${not AssinaturaDigitalControle.tudoCerto}">
            <div style="text-align: center;padding: 8%;font-family: 'Nunito Sans'">

                <div id="formNadaCerto" styleClass="pure-form pure-u-1"
                        style="background-color: white;box-shadow: 0 6px 12px #e4e5e6;border-radius: 12px;padding-bottom: 14px;width: 400px;display: inline-table">
                    <div style="padding: 30px;font-size: 20px;font-weight: 700;">
                        <h:graphicImage styleClass="iconePadrao aberto" style="height: 40px; margin-top: 18px"
                                        url="/imagens_flat/2022/pct-icone-pacto-completa.svg"/>
                    </div>

                    <div style="padding: 0 30px;font-size: 20px;font-weight: 700;text-align: center">
                        <h:graphicImage styleClass="iconePadrao aberto" style="height: 230px;"
                                        url="/imagens_flat/assinatura_digital/sorryContrato.svg"/>
                    </div>

                    <div style="font-size: 16px;font-family: 'Nunito Sans';color: #43474B;line-height: 120%;font-weight: 400;padding: 0 30px;">
                        <p>Desculpe! Esta url est� incompleta ou esse contrato j� foi assinado.</p>
                        <p>Por favor, entre em contato com sua academia para enviar o email de assinatura de contrato
                            novamente.</p>
                    </div>

                </div>


                <div style="font-size: 14px;font-weight: 300;font-style: normal;font-family: 'Nunito Sans';line-height: 150%;margin-top: 94px">
                    A assinatura deste contrato ser� registrada em um log audit�vel e o seu documento e dados ser�o
                    protegidos por <br>tranca eletr�nica e criptografia de ponta a ponta.
                </div>

                <div style="font-size: 14px;font-weight: 300;font-style: normal;font-family: 'Nunito Sans';line-height: 150%;margin-top: 64px">
                    1999-2022 &copy; | Pacto Solu��es Tecnol�gicas LTDA <br>Tech. People. Wellness. <span
                        style="color: red">&hearts;</span></div>

            </div>
        </c:if>

        <c:if test="${AssinaturaDigitalControle.tudoCerto and not AssinaturaDigitalControle.contratoAssinadoComSucesso}">
        <div>
            <h:panelGroup layout="block" style="text-align: center;padding: 8%;font-family: 'Nunito Sans'"
                          styleClass="pure-g-r">
                <div styleClass="pure-form pure-u-1"
                        style="background-color: white;box-shadow: 0 6px 12px #e4e5e6;border-radius: 12px;padding-bottom: 14px;width: 400px;display: inline-table">
                    <div style="padding: 30px;font-size: 20px;font-weight: 700;">
                        <h:graphicImage styleClass="iconePadrao aberto" style="height: 40px; margin-top: 18px"
                                        url="/imagens_flat/2022/pct-icone-pacto-completa.svg"/>
                    </div>

                    <h:panelGroup
                            style="font-family: 'Nunito Sans', sans-serif;font-size: 28px;font-style: normal;font-weight: 700;line-height: 100%;color:black;padding: 0 30px">
                        <h:outputText value="Ol�, #{AssinaturaDigitalControle.nomePessoaContrato}."/>
                    </h:panelGroup>

                    <br><br>
                    <div style="font-size: 24px;font-family: 'Nunito Sans';color: #43474B;font-weight: 400;font-size: 20px;line-height: 120%;font-weight: bolder;">
                        Para seguir com a assinatura, <br>
                        Informe os dados abaixo.
                    </div>
                    <br>

                    <h:panelGroup id="pnlCpf" style="padding: 0 30px;" layout="block">
                        <h:panelGroup id="txtCpf" style="width: 100%; text-align: left" layout="block">
                            <h:outputLabel for="cpf">CPF</h:outputLabel>
                        </h:panelGroup>
                        <h:panelGroup id="inputCpf" style="width: 100%; text-align: left" layout="block">
                            <h:inputText id="cpf"
                                         style="font-family: 'Nunito Sans';
                                     font-weight: 600;
                                     line-height: 16px;
                                     font-size: 14px;
                                     color: #51555A;
                                     border-radius: 4px;
                                     width: 100%;
                                     max-width: 320px;
                                     height: 42px;
                                     border-color: #D3D5D7;
                                     padding: 0 12px;"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="pnlEmail" style="padding: 0 30px;margin-top: 16px">
                        <h:panelGroup id="txtEmail" style="width: 100%; text-align: left" layout="block">
                            <h:outputLabel for="email">E-mail</h:outputLabel>
                        </h:panelGroup>
                        <h:panelGroup id="inputEmail" style="width: 100%; text-align: left" layout="block">
                            <h:inputText id="email"
                                         style="font-family: 'Nunito Sans';
                                     font-weight: 600;
                                     line-height: 16px;
                                     font-size: 14px;
                                     color: #51555A;
                                     border-radius: 4px;
                                     width: 100%;
                                     max-width: 320px;
                                     height: 42px;
                                     padding: 0 12px;">
                            </h:inputText>
                        </h:panelGroup>
                    </h:panelGroup>

                    <a4j:commandButton id="submit-button"
                                       style="cursor: pointer;
                                       font-family: 'Nunito Sans';
                                       font-style: normal;
                                       background: #0380E3;
                                       color: #FFFFFF;
                                       border-radius: 4px;
                                       font-weight: 700;
                                       width: 330px;
                                       font-size: 16px;
                                       line-height: 24px;
                                       height: 40px;
                                       margin: 24px 0"
                                       value="Assinar"
                                       action="#{AssinaturaDigitalControle.validarDadosAssinaturaCliente}"
                                       oncomplete="#{AssinaturaDigitalControle.msgAlert}"
                                       reRender="form">
                    </a4j:commandButton>
                </div>

                <div style="font-size: 14px;font-weight: 300;font-style: normal;font-family: 'Nunito Sans';line-height: 150%;margin-top: 94px">
                    A assinatura deste contrato ser� registrada em um log audit�vel e o seu documento e dados ser�o
                    protegidos por <br>tranca eletr�nica e criptografia de ponta a ponta.
                </div>

                <div style="font-size: 14px;font-weight: 300;font-style: normal;font-family: 'Nunito Sans';line-height: 150%;margin-top: 64px">
                    1999-2022 &copy; | Pacto Solu��es Tecnol�gicas LTDA <br>Tech. People. Wellness. <span
                        style="color: red">&hearts;</span></div>


            </h:panelGroup>
            </c:if>

            <c:if test="${AssinaturaDigitalControle.tudoCerto and AssinaturaDigitalControle.contratoAssinadoComSucesso}">
                <div style="text-align: center;padding: 8%;font-family: 'Nunito Sans'">

                    <div id="formFinalizadoComSucesso" styleClass="pure-form pure-u-1"
                            style="background-color: white;box-shadow: 0 6px 12px #e4e5e6;border-radius: 12px;padding-bottom: 14px;width: 400px;display: inline-table">
                        <div style="padding: 30px;font-size: 20px;font-weight: 700;">
                            <h:graphicImage styleClass="iconePadrao aberto" style="height: 40px; margin-top: 18px"
                                            url="/imagens_flat/2022/pct-icone-pacto-completa.svg"/>
                        </div>

                        <div style="padding: 0 30px;font-size: 20px;font-weight: 700;text-align: center">
                            <h:graphicImage styleClass="iconePadrao aberto" style="height: 230px;"
                                            url="/imagens_flat/assinatura_digital/assinadoContrato.svg"/>
                        </div>

                        <div style="font-size: 16px;font-family: 'Nunito Sans';color: #43474B;line-height: 120%;font-weight: 400;padding: 0 30px;">
                            <p>Contrato assinado com sucesso! Em breve, voc� receber� no email a confirma��o da
                                assinatura!</p>
                        </div>

                    </div>


                    <div style="font-size: 14px;font-weight: 300;font-style: normal;font-family: 'Nunito Sans';line-height: 150%;margin-top: 94px">
                        A assinatura deste contrato ser� registrada em um log audit�vel e o seu documento e dados ser�o
                        protegidos por <br>tranca eletr�nica e criptografia de ponta a ponta.
                    </div>

                    <div style="font-size: 14px;font-weight: 300;font-style: normal;font-family: 'Nunito Sans';line-height: 150%;margin-top: 64px">
                        1999-2022 &copy; | Pacto Solu��es Tecnol�gicas LTDA <br>Tech. People. Wellness. <span
                            style="color: red">&hearts;</span></div>

                </div>
            </c:if>

        </div>
    </div>
        <a4j:jsFunction name="enviarIp">
            <a4j:actionparam name="tmpIP" assignTo="#{AssinaturaDigitalControle.ipAddress}"/>
        </a4j:jsFunction>
    </h:form>
</f:view>
</body>
<script>
    getPlaceHolder();
    submitIp();
</script>
