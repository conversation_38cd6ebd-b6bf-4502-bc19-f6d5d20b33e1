<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>   
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <h:outputText value="Compra"/>
    </title>
    <c:set var="titulo" scope="session" value="Compra"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-as-compras-de-produtos-de-estoque/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <script type="text/javascript" src="script/demonstrativoFinan.js"></script>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form" style="overflow: visible !important;">
            <hr style=",border-color: #e6e6e6;"/>
            <h:commandLink action="#{CompraControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <rich:tabPanel width="100%" >
                    <rich:tab id="dadosCompra" label="Dados Compra">
                        <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText rendered="#{CompraControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="Empresa" />
                            <h:panelGroup rendered="#{CompraControle.usuarioLogado.administrador}">
                                <h:selectOneMenu  id="Compra_empresa" onblur="blurinput(this);"
                                                  disabled="#{CompraControle.compraVO.cancelada}"
                                                  onfocus="focusinput(this);"
                                                  styleClass="form"
                                                  value="#{CompraControle.compraVO.empresa.codigo}" >
                                    <f:selectItems  value="#{CompraControle.listaSelectItemEmpresa}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_Compra_empresa" action="#{CompraControle.montarListaSelectItemEmpresa}"
                                                   image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:Compra_empresa"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{CompraControle.compraVO.solicitacaoCompra != null && CompraControle.compraVO.solicitacaoCompra > 0}"
                                          value="Solicitação de Compra" />
                            <h:inputText  id="codigoSolicitacaoCompra" rendered="#{CompraControle.compraVO.solicitacaoCompra != null && CompraControle.compraVO.solicitacaoCompra > 0}"
                                          disabled="true"
                                          size="20" onblur="blurinput(this);"
                                          onfocus="focusinput(this);" styleClass="form"
                                          value="#{CompraControle.compraVO.solicitacaoCompra}" />

                            <h:outputText style="padding-right:5px; vertical-align: middle;" styleClass="tituloCampos" value="Fornecedor" />
                            <h:panelGroup>
                                <h:selectOneMenu id="fornecedor" styleClass="form"
                                                 disabled="#{CompraControle.compraVO.cancelada}"
                                                 style = "vertical-align: middle;"
                                                 value="#{CompraControle.compraVO.fornecedor.codigo}" >
                                    <f:selectItems value="#{CompraControle.listaSelectItemFornedor}" />
                                </h:selectOneMenu>

                                <a4j:commandButton id="atualizar_fornecedor" action="#{CompraControle.montarListaSelectItemFornecedor}"
                                                   image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                   reRender="form:fornecedor" title="Atualizar Fornecedor"/>
                                <a4j:commandButton id="consultaDadosFornecedor" action="#{FornecedorControle.abrirFornecedorCompra}"
                                                   oncomplete="#{FornecedorControle.msgAlert}"  alt="Cadastrar Fornecedor"
                                                   image="./images/icon_add.gif" title="Cadastrar Fornecedor"/>

                                <h:outputText style="padding-left:10px;"  id="inpCancelada" rendered="#{CompraControle.compraVO.cancelada}" value="Cancelada"  />
                                <h:selectBooleanCheckbox id="chkCancelada" rendered="#{CompraControle.compraVO.cancelada}" disabled="true" styleClass="campos" value="#{CompraControle.compraVO.cancelada}"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{CompraControle.contaPagarCompraEstoque}" value="Descrição da Conta a Pagar" />
                            <h:inputText  id="descricaoContaPagar" rendered="#{CompraControle.contaPagarCompraEstoque}" size="50" maxlength="100" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{CompraControle.compraVO.descricaoFinanceiro}" />

                            <h:outputText   value="Número Nota Fiscal" />
                            <h:panelGroup>
                                <h:inputText  readonly="#{CompraControle.compraVO.cancelada}" id="numeroNF"  size="15" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{CompraControle.compraVO.numeroNF}" />
                                <h:message for="numeroNF" styleClass="mensagemDetalhada"/>
                                <h:outputText style="padding-left:10px" styleClass="form" value="Valor Total: " />
                                <h:outputText styleClass="form" value="#{CompraControle.compraVO.valorTotal}" >
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>

                            </h:panelGroup>

                            <h:outputText value="Contato" />
                            <h:inputText  id="contato" readonly="#{CompraControle.compraVO.cancelada}"  size="50" maxlength="100" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{CompraControle.compraVO.contato}" />

                            <h:outputText value="Telefone Contato" />
                            <h:inputText  id="TelContato" readonly="#{CompraControle.compraVO.cancelada}"  size="50" maxlength="100" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{CompraControle.compraVO.telefoneContato}" />

                            <h:outputText styleClass="tituloCampos" style="padding-right:5px;padding-left:5px" value="Data Emissão"/>
                            <rich:calendar id="dataEmissao" readonly="#{CompraControle.compraVO.cancelada}" value="#{CompraControle.compraVO.dataEmissao}"
                                           oninputchange="return validar_Data(this.id);"
                                            showInput="true" datePattern="dd/MM/yyyy" zindex="2" showWeeksBar="false">
                            </rich:calendar>
                            <h:outputText styleClass="tituloCampos" style="padding-right:5px;padding-left:5px" value="Data Compra"/>
                            <h:panelGroup>
                            <h:inputText id="dataCompra"
                                         styleClass="form"
                                         onchange="validar_Data_horas_minutos(this.id);"
                                         value="#{CompraControle.compraVO.dataCadastro_Apresentar}"></h:inputText>
                                         
                            <rich:calendar id="dataQuitacaoCalendar"
                                           value="#{CompraControle.dataCompraCalendario}"
                                           direction="bottom-left"
                                           inputSize="10"
                                           styleClass="tituloboxcentro2"
                                           showInput="false"
                                           inputClass="form"
                                           datePattern="dd/MM/yyyy"
                                           zindex="2"
                                           showWeeksBar="false">
                                <a4j:support event="onchanged" reRender="dataCompra"
                                             action="#{CompraControle.calendarioDataCadastro}" />
                            </rich:calendar>   
                            </h:panelGroup>
                            <h:outputText rendered="#{CompraControle.contaPagarCompraEstoque}" value="Número de parcelas" />
                            <rich:inputNumberSpinner id="nrParcelas" styleClass="form" minValue="0" disabled="#{CompraControle.compraVO.codigo > 0}"
                                                     maxValue="99999"
                                                     rendered="#{CompraControle.contaPagarCompraEstoque}"
                                                     value="#{CompraControle.compraVO.nrParcelasContaPagar}" />
                            <h:panelGroup rendered="#{!CompraControle.existeDocumento and !CompraControle.compraVO.importacaoXMLNFE}">
                                <h:outputText styleClass="tituloCampos tooltipster" title="O tamanho do arquivo deve ser menor ou igual a 1MB"
                                              id="anexarDocumento"
                                              value="Anexar Documento: "></h:outputText>
                            </h:panelGroup>
                            <h:panelGroup rendered="#{!CompraControle.existeDocumento and !CompraControle.compraVO.importacaoXMLNFE}">
                                <rich:fileUpload id="uploadComprovante"
                                                 listHeight="50"
                                                 listWidth="350"
                                                 noDuplicate="false"
                                                 fileUploadListener="#{CompraControle.uploadDocumento}"
                                                 maxFilesQuantity="24"
                                                 allowFlash="false"
                                                 immediateUpload="false"
                                                 acceptedTypes="jpg, jpeg, gif, png, bmp, pdf, JPG, JPEG, GIF, PNG, BMP, PDF"
                                                 addControlLabel="Adicionar"
                                                 cancelEntryControlLabel="Cancelar"
                                                 doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido. 512KB"
                                                 progressLabel="Enviando"
                                                 clearControlLabel="Limpar"
                                                 clearAllControlLabel="Limpar todos"
                                                 stopControlLabel="Parar"
                                                 uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão"
                                                 stopEntryControlLabel="Parar">
                                    <a4j:support event="onadd" reRender="panelMensagemErro"/>
                                    <a4j:support event="onerror" oncomplete="#{CompraControle.mensagemNotificar}" reRender="uploadComprovante, panelMensagemErro"/>
                                    <a4j:support event="onupload" reRender="panelMensagemErro"/>
                                    <a4j:support event="onuploadcomplete" reRender="panelMensagemErro"/>
                                    <a4j:support event="onclear" action="#{CompraControle.limparArquivo}" oncomplete="#{CompraControle.mensagemNotificar}" reRender="uploadComprovante, panelMensagemErro"/>
                                </rich:fileUpload>
                            </h:panelGroup>
                            <h:panelGroup rendered="#{CompraControle.existeDocumento}">
                                <h:outputText value="Anexo Documento: "
                                              id="anexarContaFinanText"
                                              rendered="#{!CompraControle.compraVO.importacaoXMLNFE}"
                                              title="O tamanho do arquivo deve ser menor ou igual a 1MB"
                                              styleClass="tooltipster tituloCampos"/>
                                <h:outputText value="XML NFe:"
                                              id="anexarContaFinanTextXML"
                                              rendered="#{CompraControle.compraVO.importacaoXMLNFE}"
                                              styleClass="tooltipster tituloCampos"/>
                            </h:panelGroup>

                            <h:panelGroup rendered="#{CompraControle.existeDocumento}"
                                          id="existeArquivoContaFinan">
                                <h:commandLink id="arqMovConta" style="margin-left: 12px"
                                               actionListener="#{CompraControle.downloadDocumentoListener}"
                                               value="DOWNLOAD"/>
                                <h:commandLink id="excluirArqConta" style="margin-left: 12px"
                                               actionListener="#{CompraControle.excluirArqDocumentoListener}"
                                               value="EXCLUIR"/>
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="Observações" />
                            <h:inputTextarea readonly="#{CompraControle.compraVO.cancelada}"  style="height: 63px; resize: none;" id="observacaoRichEditor"
                                             value="#{CompraControle.compraVO.observacoes}" rows="3" cols="60"/>


                        </h:panelGrid>
                    </rich:tab>

                    <a4j:jsFunction name="prepararProdutosNfe" action="#{CompraControle.prepararTelaProdutosNaoEncontrados}"
                                    reRender="compraFormNfeProdutosNaoEncontradosModal, selectProdutoEstoque, selectCategoriaNovosProdutosNfe"/>

                    <rich:tab id="compraItens" label="Produtos da Compra">
                        <h:panelGroup rendered="#{not empty CompraControle.produtosNaoEncontradosNaNfe}">
                            <div class="alert alert-danger" id="avisoProdutosNfe" style="cursor: pointer; padding: 10px; border-radius: 5px;"
                                 onclick="prepararProdutosNfe(); Richfaces.showModalPanel('compraFormNfeProdutosNaoEncontradosModal')">
                                <span style="font-weight: bold;">&#9888; Não encontramos esses produtos no seu estoque.</span>
                                Você pode vinculá-los manualmente a um produto já existente ou criar um novo item.
                                Para concluir a compra, é necessário ajustar essas pendências.
                            </div>
                        </h:panelGroup>
                        <h:panelGrid id="gridItens" columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">
                            <f:facet name="header">
                                <h:outputText styleClass="tituloCampos" value="Adicionar Produtos da Compra"/>
                            </f:facet>

                            <h:outputText   value="Produto" />
                            <h:panelGroup>
                                <h:inputText  id="nomeProdutoSelecionado"
                                              size="50"
                                              maxlength="50"
                                              title="Para pesqusar, informe o código ou nome do produto."
                                              onblur="blurinput(this);"
                                              disabled="#{!CompraControle.compraVO.permiteAlterarItens}"
                                              onfocus="focusinput(this);"
                                              styleClass="form"
                                              value="#{CompraControle.produtoSelecionado}" />

                                <rich:suggestionbox   height="200" width="400"
                                                      for="nomeProdutoSelecionado"
                                                      status="statusInComponent"
                                                      immediate="true"
                                                      suggestionAction="#{CompraControle.executarAutocompletePesqProduto}"
                                                      minChars="1"
                                                      reRender="panelMensagemErro"
                                                      rowClasses="linhaImpar, linhaPar"
                                                      var="result"  id="suggestionResponsavel">
                                    <a4j:support event="onselect"
                                                 reRender="form, panelMensagemErro"
                                                 action="#{CompraControle.selecionarProduto}">
                                    </a4j:support>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Nome"  styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText styleClass="textverysmall" value="#{result.descricao}" />
                                    </h:column>
                                    <h:column >
                                        <f:facet name="header">
                                            <h:outputText value="Categoria" styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText  styleClass="textverysmall" value="#{result.categoriaProduto.descricao}" />
                                    </h:column>
                                </rich:suggestionbox>
                            </h:panelGroup>

                            <h:outputText   value="Quantidade" />
                            <h:inputText  id="quantidade"
                                          disabled="#{!CompraControle.compraVO.permiteAlterarItens}"
                                          size="50"
                                          maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{CompraControle.compraItensVO.quantidade}" />

                            <h:outputText   value="Valor Unitário" />
                            <h:inputText  id="valorUnitario" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                                          disabled="#{!CompraControle.compraVO.permiteAlterarItens}"
                                          onkeypress="return(currencyFormat(this,'.',',',event));"
                                          styleClass="form"  value="#{CompraControle.compraItensVO.valorUnitario}" >
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>

                            <h:outputText value="Desconto Total" />
                            <h:inputText id="desconto" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                                         disabled="#{!CompraControle.compraVO.permiteAlterarItens}"
                                         onkeypress="return(currencyFormat(this,'.',',',event));"
                                         styleClass="form"  value="#{CompraControle.compraItensVO.desconto}" >
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" styleClass="centralizado">
                            <a4j:commandButton id="btnAdicionarProduto" rendered="#{CompraControle.compraVO.permiteAlterarItens}"
                                               reRender="form"
                                               action="#{CompraControle.adicionarItensCompra}" value="#{msg_bt.btn_adicionar}"
                                               image= "./imagens/botaoAdicionar.png" accesskey="6" styleClass="botoes"/>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="tbCompraItens" width="100%" headerClass="subordinado"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="esquerda, centralizado, direita,direita,direita,centralizado"
                                         value="#{CompraControle.compraVO.itensList}" var="compraItem">

                                <h:column >
                                    <f:facet name="header">
                                        <h:outputText  value="Produto" />
                                    </f:facet>
                                    <h:outputText  value="#{compraItem.produto.descricao}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Quantidade" />
                                    </f:facet>
                                    <h:outputText rendered="#{(((CompraControle.ordemCompraEstoque && compraItem.quantidade == 0) || (compraItem.quantidadeAutorizar != null && compraItem.quantidadeAutorizar > 0)) && CompraControle.compraVO.autorizada == null) || (CompraControle.compraVO.autorizada != null && !CompraControle.compraVO.autorizada)}" value="#{compraItem.quantidadeAutorizar}" />
                                    <h:outputText rendered="#{((!CompraControle.ordemCompraEstoque || compraItem.quantidade > 0) && (compraItem.quantidadeAutorizar == null || compraItem.quantidadeAutorizar == 0)) || (CompraControle.compraVO.autorizada != null && CompraControle.compraVO.autorizada)}" value="#{compraItem.quantidade}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText   value="Valor Unitário" />
                                    </f:facet>
                                    <h:outputText  value="#{compraItem.valorUnitario_Apresentar}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Desconto" />
                                    </f:facet>
                                    <h:outputText   value="#{compraItem.desconto_Apresentar}" />
                                </h:column>

                                <h:column >
                                    <f:facet name="header">
                                        <h:outputText  value="Total" />
                                    </f:facet>
                                    <h:outputText   value="#{compraItem.total_Apresentar}" />
                                </h:column>

                                <h:column >
                                    <f:facet name="header">
                                        <h:outputText  value="Pontos" />
                                    </f:facet>
                                    <h:outputText   value="#{compraItem.pontos}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>

                                    <h:panelGroup rendered="#{CompraControle.compraVO.permiteAlterarItens}" >
                                        <h:outputText value="    "/>
                                        <a4j:commandButton  reRender="tbCompraItens,gridItens, panelMensagemErro"  id="removerItem" immediate="true" action="#{CompraControle.removerItensCompra}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                        <a4j:commandButton reRender="tbCompraItens,gridItens, panelMensagemErro" id="linkEditarCompraItem" action="#{CompraControle.editarCompraItem}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_editar_dados}" styleClass="botoes"/>
                                    </h:panelGroup>
                                </h:column>

                            </h:dataTable>
                        </h:panelGrid>

                    </rich:tab>
                    <rich:tab id="financeiroCompra" label="Financeiro" rendered="#{CompraControle.contaPagarCompraEstoque}">
                        <h:panelGrid id="panelContaGeradaCompraFinanceiro" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada" rendered="#{(not (CompraControle.compraVO.codigo eq null)) and CompraControle.compraVO.codigo > 0}">
                            <f:facet name="header">
                                <h:outputText
                                        value="Situação da(s) conta(s) gerada(s) nesta compra" />
                            </f:facet>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <rich:dataTable id="contaGeradaFinanceiroCompra" width="100%" headerClass="subordinado"
                                                styleClass="tabFormSubordinada" rowClasses="linhaImpar, linhaPar"
                                                columnClasses="colunaAlinhamento"
                                                value="#{CompraControle.contasDestaCompra}"
                                                var="contaGeradaFinanceiroCompra">
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Código da conta" />
                                        </f:facet>
                                        <h:outputText value="#{contaGeradaFinanceiroCompra.codigo}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Descrição" />
                                        </f:facet>
                                        <h:outputText value="#{contaGeradaFinanceiroCompra.descricao}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Data de Vencimento" />
                                        </f:facet>
                                        <h:outputText value="#{contaGeradaFinanceiroCompra.dataVencimento_Apresentar}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Data de Quitação" />
                                        </f:facet>
                                        <h:outputText value="#{contaGeradaFinanceiroCompra.dataQuitacao eq null ? '-' : contaGeradaFinanceiroCompra.dataQuitacao_Apresentar}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Código de Barras" />
                                        </f:facet>
                                        <h:outputText value="#{((contaGeradaFinanceiroCompra.codigoBarras eq null) or (empty contaGeradaFinanceiroCompra.codigoBarras)) ? '-' : contaGeradaFinanceiroCompra.codigoBarras}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Situação" />
                                        </f:facet>
                                        <h:outputText value="#{contaGeradaFinanceiroCompra.dataQuitacao eq null ? 'Em Aberto' : 'Pago'}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Valor"/>
                                        </f:facet>
                                        <h:outputText value="#{contaGeradaFinanceiroCompra.valor}">
                                            <f:convertNumber maxFractionDigits="2" groupingUsed="true"
                                                             maxIntegerDigits="14" type="currency"
                                                             currencySymbol="#{contaGeradaFinanceiroCompra.empresa.moeda} "/>
                                        </h:outputText>
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>

                        <h:panelGrid id="panelContaCompraFinanceiro" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada" rendered="#{CompraControle.compraVO.codigo eq null or CompraControle.compraVO.codigo eq 0}">
                            <f:facet name="header">
                                <h:outputText
                                        value="Personalizar conta(s) gerar no financeiro ao clicar em gravar" />
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita">
                                <h:outputText value="Parcela" />
                                <rich:inputNumberSpinner id="parcela" styleClass="form" minValue="0"
                                                         maxValue="99999"
                                                         value="#{CompraControle.nrParcelaContaFinanceiro}" />

                                <h:outputText value="Data de Vencimento" />
                                <h:panelGroup>
                                <rich:calendar id="dataVencimentoContaCompra"
                                               value="#{CompraControle.dataVencimentoContaCompra}"
                                               oninputchange="return validar_Data(this.id);"
                                               showInput="true" datePattern="dd/MM/yyyy" zindex="2" showWeeksBar="false">
                                </rich:calendar>
                                </h:panelGroup>

                                <h:outputText value="Valor" />
                                <h:inputText id="valorDeVenda" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                                             onkeypress="return(currencyFormat(this,'.',',',event));"
                                             styleClass="form" value="#{CompraControle.valorParcelaContaFinanceiro}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:inputText>

                                <h:outputText value="Código de Barras" />
                                <h:inputText id="codigoBarra" style="width: 150px;" onfocus="focusinput(this);"
                                             styleClass="form" value="#{CompraControle.codigoBarrasParcelaContaFinanceiro}"/>
                            </h:panelGrid>
                            <a4j:commandButton action="#{CompraControle.adicionarParcelaContaFinanceiroCompra}"
                                               reRender="panelContaCompraFinanceiro, panelMensagemErro"
                                               value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes" />

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <rich:dataTable id="contaFinanceiroCompra" width="100%" headerClass="subordinado"
                                                styleClass="tabFormSubordinada" rowClasses="linhaImpar, linhaPar"
                                                columnClasses="colunaAlinhamento"
                                                value="#{CompraControle.compraVO.compraContasLancarFinanceiro}"
                                                var="contaFinanceiroCompra">
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Parcela" />
                                        </f:facet>
                                        <h:outputText value="#{contaFinanceiroCompra.nrParcela}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Data de Vencimento" />
                                        </f:facet>
                                        <h:outputText value="#{contaFinanceiroCompra.dataVencimento_Apresentar}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Valor" />
                                        </f:facet>
                                        <h:outputText value="#{contaFinanceiroCompra.valorParcela_Apresentar}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Código de Barras" />
                                        </f:facet>
                                        <h:outputText value="#{contaFinanceiroCompra.codigoBarras}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                        <a4j:commandButton id="editarItem" reRender="form" ajaxSingle="true"
                                                           immediate="true"
                                                           action="#{CompraControle.editarParcelaContaFinanceiroCompra}"
                                                           value="#{msg_bt.btn_editar}"
                                                           image="./imagens/botaoEditar.png" accesskey="6"
                                                           styleClass="botoes"/>

                                        <h:outputText value="    "/>
                                        <h:commandButton id="removerItemParcelaContaFinanceiroCompra" immediate="true"
                                                         action="#{CompraControle.removerParcelaContaFinanceiroCompra}"
                                                         value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png"
                                                         accesskey="7" styleClass="botoes" />

                                        </h:panelGroup>
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="importarXML" label="Importar XML de NFE">
                        <h:panelGrid id="gridItensXml" columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">
                            <div></div>
                            <rich:fileUpload id="uploadXmlNfe2"
                                             listHeight="50"
                                             listWidth="350"
                                             noDuplicate="false"
                                             fileUploadListener="#{CompraControle.uploadNfe}"
                                             maxFilesQuantity="1"
                                             allowFlash="false"
                                             immediateUpload="true"
                                             acceptedTypes="xml, XML"
                                             addControlLabel="Adicionar XML NFe"
                                             cancelEntryControlLabel="Cancelar"
                                             doneLabel="Pronto"
                                             sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido. 512KB"
                                             progressLabel="Enviando"
                                             clearControlLabel="Limpar"
                                             clearAllControlLabel="Limpar todos"
                                             stopControlLabel="Parar"
                                             uploadControlLabel="Enviar"
                                             transferErrorLabel="Falha de Transmissão"
                                             stopEntryControlLabel="Parar">
                                <a4j:support event="onadd" reRender="tbCompraItens"/>
                                <a4j:support event="onerror" oncomplete="#{CompraControle.mensagemNotificar}"/>
                                <a4j:support event="onuploadcomplete" oncomplete="#{CompraControle.msgAlert}" reRender="tbItensNaoEncontradosNfe, compraFormNfeProdutosNaoEncontradosModal"/>
                                <a4j:support event="onclear" action="#{CompraControle.limparArquivo}" oncomplete="#{CompraControle.mensagemNotificar}" reRender="uploadXmlNfe2"/>
                                <a4j:support event="onclearAll" action="#{CompraControle.limparArquivo}" oncomplete="#{CompraControle.mensagemNotificar}" reRender="uploadXmlNfe2"/>
                                <a4j:support event="oncancel" action="#{CompraControle.limparArquivo}" oncomplete="#{CompraControle.mensagemNotificar}" reRender="uploadXmlNfe2"/>
                            </rich:fileUpload>
                        </h:panelGrid>
                    </rich:tab>
                </rich:tabPanel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{CompraControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{CompraControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgCompra" styleClass="mensagem"  value="#{CompraControle.mensagem}"/>
                            <h:outputText id="msgCompraDet" styleClass="mensagemDetalhada" value="#{CompraControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" id="gridOperacoes">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{CompraControle.novo}"
                                             value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                             styleClass="botoes nvoBt btSec"/>

                            <a4j:commandButton id="salvar"
                                               rendered="#{!CompraControle.compraVO.cancelada && !CompraControle.processandoOperacao}"
                                               action="#{CompraControle.gravar}"
                                               oncomplete="#{CompraControle.mensagemNotificar}"
                                               value="#{msg_bt.btn_gravar}"
                                               title="#{msg.msg_gravar_dados}"
                                               reRender="gridOperacoes"
                                               accesskey="2" styleClass="botoes nvoBt"/>

                            <h:panelGroup id="grupoClonarCompra" rendered="#{CompraControle.compraVO.codigo > 0}">
                                <a4j:commandButton id="clonar"
                                                   action="#{CompraControle.confirmarExcluir}"
                                                   oncomplete="#{CompraControle.msgAlert}"
                                                   reRender="mdlMensagemGenerica"
                                                   value="#{msg_bt.btn_Clonar}"
                                                   alt="#{msg.msg_clonar_dados}" accesskey="4"
                                                   styleClass="botoes nvoBt btSec">
                                    <f:param name="metodochamar" value="clonar"/>
                                </a4j:commandButton>
                            </h:panelGroup>

                            <h:panelGroup id="grupoAutorizarCompra" rendered="#{CompraControle.ordemCompraEstoque && CompraControle.compraVO.codigo > 0 &&
                             CompraControle.compraVO.autorizada == null}">
                                <a4j:commandButton id="autorizar"
                                                   action="#{CompraControle.autorizar}"
                                                   oncomplete="#{CompraControle.msgAlert}"
                                                   reRender="mdlMensagemGenerica"
                                                   value="Autorizar"
                                                   accesskey="4" styleClass="botoes nvoBt btSec">
                                </a4j:commandButton>
                                <a4j:commandButton id="negar"
                                                   action="#{CompraControle.negar}"
                                                   oncomplete="#{CompraControle.msgAlert}"
                                                   reRender="mdlMensagemGenerica"
                                                   value="Negar"
                                                   accesskey="4" styleClass="botoes nvoBt btSec">
                                </a4j:commandButton>
                            </h:panelGroup>

                            <h:panelGroup id="grupoClonarCancelar" rendered="#{CompraControle.compraVO.permiteCancelarCompra}">
                                <a4j:commandButton id="cancelar"
                                                   action="#{CompraControle.confirmarExcluir}"
                                                   oncomplete="#{CompraControle.msgAlert}"
                                                   reRender="mdlMensagemGenerica"
                                                   value="#{msg_bt.btn_cancelar}"
                                                   alt="Cancelar Compra" accesskey="4" styleClass="botoes nvoBt btSec">
                                    <f:param name="metodochamar" value="cancelarCompra"/>
                                </a4j:commandButton>
                            </h:panelGroup>

                            <a4j:commandButton id="consultar" immediate="true" action="#{CompraControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <a4j:commandLink rendered="#{CompraControle.compraVO.codigo != 0}"
                                             id="imprimirComp" styleClass="botoes nvoBt btSec fa-icon-print"
                                             style="font-size: 16px;;display: inline-block"
                                             action="#{RelatorioCompraControle.imprimirRelatorioCompraPDF}"
                                             oncomplete="abrirPopupPDFImpressao('relatorio/#{RelatorioCompraControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                            </a4j:commandLink>
                            <h:outputText value="    "/>
                            <a4j:commandLink id="visualizarLog"
                                             immediate="true"
                                             action="#{CompraControle.realizarConsultaLogObjetoSelecionadoCompra}"
                                             style="display: inline-block; padding: 8px 15px;"
                                             accesskey="5"
                                             styleClass="botoes nvoBt btSec"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />

        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="compraFormNfeProdutosNaoEncontradosModal"
                     style="overflow: auto; max-height: 600px;"
                     shadowOpacity="true" width="700" height="600"
                     onshow="chamarMontarListasProdutosCategoria()">

    <a4j:jsFunction name="chamarMontarListasProdutosCategoria"
                        action="#{CompraControle.montarListaSelectItemProduto}"
                        oncomplete="chamarMontarListaCategoria()"
                        ajaxSingle="true" reRender="selectProdutoEstoque" />

        <a4j:jsFunction name="chamarMontarListaCategoria"
                        action="#{CompraControle.montarListaSelectItemCategoriaProduto}"
                        ajaxSingle="true" reRender="selectCategoriaNovosProdutosNfe" />

        <f:facet name="header">
            <h:outputText value="Ajuste de produtos"
                          styleClass="centralizar-titulo-header" />
        </f:facet>


        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                              id="hidemodalcompraFormNfeProdutosNaoEncontradosModal"/>
                <rich:componentControl for="compraFormNfeProdutosNaoEncontradosModal"
                                       attachTo="hidemodalcompraFormNfeProdutosNaoEncontradosModal"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModaxCompraXML" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                <h:panelGroup style="overflow: auto; max-height: 300px; width: 100%;" layout="block">
                <h:dataTable id="tbItensNaoEncontradosNfe" width="100%" headerClass="subordinado"
                             rowClasses="linhaImpar, linhaPar" columnClasses="esquerda, centralizado, direita,direita,direita,centralizado"
                             value="#{CompraControle.produtosNaoEncontradosNaNfe}" var="item">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Produto não identificado na compra" />
                        </f:facet>
                        <h:outputText value="#{item.produto.descricao}" />
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Vincular ao estoque" />
                        </f:facet>
                        <h:selectOneMenu id="selectProdutoEstoque" styleClass="form"
                                         style="vertical-align: middle;"
                                         value="#{item.produtoParaVincularNoEstoque}">
                            <f:selectItems value="#{CompraControle.listaSelectItemProduto}" />
                        </h:selectOneMenu>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Cadastrar como novo" />
                        </f:facet>
                        <h:selectBooleanCheckbox value="#{item.cadastrarNovo}" immediate="true">
                            <a4j:support event="change" reRender="compraFormNfeProdutosNaoEncontradosModal" />
                        </h:selectBooleanCheckbox>

                        <a4j:commandButton id="updateButton"
                                           actionListener="#{CompraControle.atualizarCheckbox}"
                                           styleClass="hidden"
                                           reRender="compraFormNfeProdutosNaoEncontradosModal"/>

                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Valor de venda" />
                        </f:facet>
                        <h:inputText id="valorDeVenda" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                                     disabled="#{!CompraControle.compraVO.permiteAlterarItens}"
                                     onkeypress="return(currencyFormat(this,'.',',',event));"
                                     styleClass="form" value="#{item.valorFinal}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:inputText>
                    </h:column>

                </h:dataTable>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="selectCategoriaProduto" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Categoria"/>
                </f:facet>

                <h:outputText value="Categoria:"/>
                <h:panelGroup layout="block">
                    <h:selectOneMenu id="categoria" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form"
                                     value="#{CompraControle.categoriaParaNovosProdutosNfe}">
                        <f:selectItems value="#{CompraControle.listaSelectItemCategoriaProduto}"/>
                    </h:selectOneMenu>

                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="enviarEstoqueMinimo" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Estoque mínimo"/>
                </f:facet>

                <h:outputText value="Estoque mínimo:"/>
                <h:panelGroup layout="block">
                    <h:inputText id="estoqueMinimo" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 size="10" maxlength="50"
                                 value="#{CompraControle.estoqueMinimoParaNovosProdutosNfe}"/>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada"
                         id="gridOperacoesProdutosNaoEncontradosNaNfe" style="padding-bottom: 20px;">
                <h:panelGroup>
                    <h:commandButton id="botaoCancelarProdutosNaoEncontradosNaNfe" immediate="true"
                                     onclick="#{rich:component('compraFormNfeProdutosNaoEncontradosModal')}.hide();"
                                     value="Cancelar" alt="Cancelar"
                                     styleClass="botoes nvoBt btSec"/>

                    <a4j:commandButton id="botaoSalvarProdutosNaoEncontradosNaNfe"
                                       action="#{CompraControle.salvarProdutosNaoEncontradosNaNfe}"
                                       oncomplete="#{CompraControle.mensagemNotificar}; #{CompraControle.produtosNaoEncontradosNaNfe.isEmpty()} && Richfaces.hideModalPanel('compraFormNfeProdutosNaoEncontradosModal');"
                                       value="Salvar produtos"
                                       title="Salvar produtos"
                                       reRender="tbItensNaoEncontradosNfe, tbCompraItens, panelMensagemErro"
                                       styleClass="botoes nvoBt"/>
                </h:panelGroup>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>

    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
