<?xml version="1.0" encoding="ISO-8859-1"?>
<faces-config version="1.2" xmlns="http://java.sun.com/xml/ns/javaee"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_1_2.xsd">

    <application>
        <view-handler>
            controle.arquitetura.view.CustomViewHandler
        </view-handler>
    </application>

    <lifecycle>
        <phase-listener>
            controle.arquitetura.view.ViewPhaseListener
        </phase-listener>
    </lifecycle>

    <!--
    <application>
        <view-handler>org.ajax4jsf.application.AjaxViewHandler</view-handler>
    </application>
    -->
    <application>
        <message-bundle>propriedades.Botoes</message-bundle>
        <locale-config>
            <default-locale>pt</default-locale>
            <supported-locale>en</supported-locale>
        </locale-config>
    </application>
    <application>
        <message-bundle>propriedades.Aplicacao</message-bundle>
        <resource-bundle>
            <base-name>propriedades.Aplicacao</base-name>
            <var>msg_aplic</var>
        </resource-bundle>
        <locale-config>
            <default-locale>pt</default-locale>
            <supported-locale>en</supported-locale>
        </locale-config>
    </application>
    <application>
        <message-bundle>propriedades.Menu</message-bundle>
        <locale-config>
            <default-locale>pt</default-locale>
            <supported-locale>en</supported-locale>
        </locale-config>
    </application>
    <application>
        <message-bundle>propriedades.KeyWords</message-bundle>
        <locale-config>
            <default-locale>pt</default-locale>
            <supported-locale>en</supported-locale>
            <supported-locale>es</supported-locale>
        </locale-config>
    </application>
    <application>
        <message-bundle>propriedades.Mensagens</message-bundle>
        <locale-config>
            <default-locale>pt</default-locale>
            <supported-locale>en</supported-locale>
        </locale-config>
    </application>
    <converter>
        <converter-id>FormatadorNumerico</converter-id>
        <converter-class>negocio.comuns.utilitarias.FormatadorNumerico</converter-class>
    </converter>
    <converter>
        <converter-id>FormatadorNumerico3Casa</converter-id>
        <converter-class>negocio.comuns.utilitarias.FormatadorNumerico3Casa</converter-class>
    </converter>
    <converter>
        <converter-id>FormatadorNumerico7Casa</converter-id>
        <converter-class>negocio.comuns.utilitarias.FormatadorNumerico7Casa</converter-class>
    </converter>
    <converter>
        <converter-id>FormatarPercentual</converter-id>
        <converter-class>negocio.comuns.utilitarias.FormatarPercentual</converter-class>
    </converter>
    <converter>
        <converter-id>simpleIndexConverter</converter-id>
        <converter-class>negocio.comuns.utilitarias.SimpleIndexConverter</converter-class>
    </converter>

    <managed-bean>
        <managed-bean-name>ThreadRoboControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.threads.ThreadRoboControle</managed-bean-class>

        <managed-bean-scope>application</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SuperControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.SuperControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LoginControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.security.LoginControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>QRCodeControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.security.QRCodeControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RecuperacaoSenhaControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.security.RecuperacaoSenhaControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MenuAvisosControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.MenuAvisosControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SuporteControle</managed-bean-name>
        <managed-bean-class>controle.basico.SuporteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RespostaPerguntaClienteControle</managed-bean-name>
        <managed-bean-class>controle.basico.RespostaPerguntaClienteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AmbienteControle</managed-bean-name>
        <managed-bean-class>controle.plano.AmbienteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MatriculaAlunoHorarioTurmaRelControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.contrato.MatriculaAlunoHorarioTurmaRelControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ListaAcessoControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.ListaAcessoControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FechamentoAcessosControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.FechamentoAcessosControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ParcelaEmAbertoControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.ParcelaEmAbertoControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ParcelaEmAbertoSPCControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.ParcelaEmAbertoSPCControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SaldoContaCorrenteControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.SaldoContaCorrenteControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ResultadoConvenioCobrancaControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.ResultadoConvenioCobrancaControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TotalizadorFrequenciaControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.TotalizadorFrequenciaControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TotalizadorTicketsControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.TotalizadorTicketsControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ClienteRelControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.ClienteRelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LocalAcessoControle</managed-bean-name>
        <managed-bean-class>controle.acesso.LocalAcessoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>DemonstrativoFinanceiroControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.DemonstrativoFinanceiroControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FechamentoCaixaPlanoContaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.FechamentoCaixaPlanoContaControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>


    <managed-bean>
        <managed-bean-name>CaixaPorOperadorRelControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.CaixaPorOperadorRelControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ReceitaPorPeriodoSinteticoRelControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.ReceitaPorPeriodoSinteticoRelControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CompetenciaSinteticoControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.CompetenciaSinteticoControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FaturamentoSinteticoControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.FaturamentoSinteticoControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ArmarioControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.ArmarioControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ClientePorDuracaoRelControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.ClientePorDuracaoRelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AniversarioControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.ClientePorAniversarioRelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>HistoricoPontosParceiroFidelidadeControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.HistoricoPontosParceiroFidelidadeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SaldoCreditoRelControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.SaldoCreditoRelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.ContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ReciboControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.ReciboControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GrupoControle</managed-bean-name>
        <managed-bean-class>controle.basico.GrupoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PaisControle</managed-bean-name>
        <managed-bean-class>controle.basico.PaisControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AdquirenteControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.AdquirenteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LogProtheusControle</managed-bean-name>
        <managed-bean-class>controle.integracao.LogProtheusControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ProdutoControle</managed-bean-name>
        <managed-bean-class>controle.plano.ProdutoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>QuestionarioClienteControle</managed-bean-name>
        <managed-bean-class>controle.basico.QuestionarioClienteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>QuestionarioClienteCRMControle</managed-bean-name>
        <managed-bean-class>controle.basico.QuestionarioClienteCRMControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CarteirasControle</managed-bean-name>
        <managed-bean-class>controle.crm.CarteirasControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PlanoControle</managed-bean-name>
        <managed-bean-class>controle.plano.PlanoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PlanoModalidadeVezesSemanaControle</managed-bean-name>
        <managed-bean-class>controle.plano.PlanoModalidadeVezesSemanaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CategoriaProdutoControle</managed-bean-name>
        <managed-bean-class>controle.plano.CategoriaProdutoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MovimentoContaCorrenteClienteControle</managed-bean-name>
        <managed-bean-class>controle.basico.MovimentoContaCorrenteClienteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MovParcelaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.MovParcelaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ProfissaoControle</managed-bean-name>
        <managed-bean-class>controle.basico.ProfissaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GrauInstrucaoControle</managed-bean-name>
        <managed-bean-class>controle.basico.GrauInstrucaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ConvenioControle</managed-bean-name>
        <managed-bean-class>controle.contrato.ConvenioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PessoaControle</managed-bean-name>
        <managed-bean-class>controle.basico.PessoaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AtualizarDadosControle</managed-bean-name>
        <managed-bean-class>controle.basico.AtualizarDadosControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AtualizarDadosFinanceiroControle</managed-bean-name>
        <managed-bean-class>controle.basico.AtualizarDadosFinanceiroControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MovPagamentoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.MovPagamentoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>EmpresaControle</managed-bean-name>
        <managed-bean-class>controle.basico.EmpresaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ProdutoSugeridoControle</managed-bean-name>
        <managed-bean-class>controle.plano.ProdutoSugeridoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>QuestionarioControle</managed-bean-name>
        <managed-bean-class>controle.basico.QuestionarioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>NivelTurmaControle</managed-bean-name>
        <managed-bean-class>controle.plano.NivelTurmaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CategoriaControle</managed-bean-name>
        <managed-bean-class>controle.basico.CategoriaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>DepartamentoControle</managed-bean-name>
        <managed-bean-class>controle.basico.DepartamentoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>HorarioControle</managed-bean-name>
        <managed-bean-class>controle.plano.HorarioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ClassificacaoControle</managed-bean-name>
        <managed-bean-class>controle.basico.ClassificacaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>VezesSemanaControle</managed-bean-name>
        <managed-bean-class>controle.plano.VezesSemanaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ModalidadeControle</managed-bean-name>
        <managed-bean-class>controle.plano.ModalidadeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TipoModalidadeControle</managed-bean-name>
        <managed-bean-class>controle.plano.TipoModalidadeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TipoConviteAulaExperimentalControle</managed-bean-name>
        <managed-bean-class>controle.basico.TipoConviteAulaExperimentalControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CampanhaCupomDescontoControle</managed-bean-name>
        <managed-bean-class>controle.oamd.CampanhaCupomDescontoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CanalPactoControle</managed-bean-name>
        <managed-bean-class>controle.basico.CanalPactoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ContaContabilControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.ContaContabilControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>


    <managed-bean>
        <managed-bean-name>ProdutoEstoqueControle</managed-bean-name>
        <managed-bean-class>controle.estoque.ProdutoEstoqueControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>


    <managed-bean>
        <managed-bean-name>CompraControle</managed-bean-name>
        <managed-bean-class>controle.estoque.CompraControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioCompraControle</managed-bean-name>
        <managed-bean-class>controle.estoque.RelatorioCompraControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BalancoControle</managed-bean-name>
        <managed-bean-class>controle.estoque.BalancoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioBalancoControle</managed-bean-name>
        <managed-bean-class>controle.estoque.RelatorioBalancoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioEstoqueProdutoControle</managed-bean-name>
        <managed-bean-class>controle.estoque.RelatorioEstoqueProdutoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CardexControle</managed-bean-name>
        <managed-bean-class>controle.estoque.CardexControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioCardexControle</managed-bean-name>
        <managed-bean-class>controle.estoque.RelatorioCardexControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>


    <managed-bean>
        <managed-bean-name>PlanoTextoPadraoControle</managed-bean-name>
        <managed-bean-class>controle.plano.PlanoTextoPadraoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ModeloOrcamentoControle</managed-bean-name>
        <managed-bean-class>controle.plano.ModeloOrcamentoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>OrcamentoControleRel</managed-bean-name>
        <managed-bean-class>controle.plano.OrcamentoControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ColaboradorControle</managed-bean-name>
        <managed-bean-class>controle.basico.ColaboradorControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ConfiguracaoNotaFiscalControle</managed-bean-name>
        <managed-bean-class>controle.basico.ConfiguracaoNotaFiscalControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PerguntaControle</managed-bean-name>
        <managed-bean-class>controle.basico.PerguntaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>

        <managed-bean-name>ComposicaoControle</managed-bean-name>
        <managed-bean-class>controle.plano.ComposicaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PerfilAcessoControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.security.PerfilAcessoControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>UsuarioControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.security.UsuarioControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ParentescoControle</managed-bean-name>
        <managed-bean-class>controle.basico.ParentescoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>DuracaoControle</managed-bean-name>
        <managed-bean-class>controle.plano.DuracaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FormaPagamentoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.FormaPagamentoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CondicaoPagamentoControle</managed-bean-name>
        <managed-bean-class>controle.plano.CondicaoPagamentoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ContratoModalidadeTurmaControle</managed-bean-name>
        <managed-bean-class>controle.contrato.ContratoModalidadeTurmaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PerguntaClienteControle</managed-bean-name>
        <managed-bean-class>controle.basico.PerguntaClienteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CidadeControle</managed-bean-name>
        <managed-bean-class>controle.basico.CidadeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>IndiceFinanceiroReajustePrecoControle</managed-bean-name>
        <managed-bean-class>controle.basico.IndiceFinanceiroReajustePrecoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>


    <managed-bean>
        <managed-bean-name>ConfiguracaoSistemaControle</managed-bean-name>
        <managed-bean-class>controle.basico.ConfiguracaoSistemaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ImportacaoControle</managed-bean-name>
        <managed-bean-class>controle.basico.ImportacaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RespostaPerguntaControle</managed-bean-name>
        <managed-bean-class>controle.basico.RespostaPerguntaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>HorarioTurmaControle</managed-bean-name>
        <managed-bean-class>controle.plano.HorarioTurmaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TurmaControle</managed-bean-name>
        <managed-bean-class>controle.plano.TurmaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ClienteControle</managed-bean-name>
        <managed-bean-class>controle.basico.ClienteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ConsultaClienteControle</managed-bean-name>
        <managed-bean-class>controle.basico.ConsultaClienteControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BancoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.BancoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ContaCorrenteControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.ContaCorrenteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TipoRetornoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.TipoRetornoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TipoRemessaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.TipoRemessaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ConvenioCobrancaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.ConvenioCobrancaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PinPadControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.PinPadControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ConvenioDescontoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.ConvenioDescontoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>DescontoControle</managed-bean-name>
        <managed-bean-class>controle.plano.DescontoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MovProdutoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.MovProdutoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>JustificativaOperacaoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.JustificativaOperacaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ConsultarTurmaControle</managed-bean-name>
        <managed-bean-class>controle.plano.ConsultarTurmaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MapaEstatisticoControle</managed-bean-name>
        <managed-bean-class>controle.relatorios.MapaEstatisticoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FrequenciaOcupacaoControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.FrequenciaOcupacaoControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>DescontoOcupacaoControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.DescontoOcupacaoControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ContratoOperacaoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.ContratoOperacaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>EstornoContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.EstornoContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TransferenciaContratoEVOControle</managed-bean-name>
        <managed-bean-class>controle.contrato.TransferenciaContratoEVOControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TransferirDireitoUsoContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.TransferirDireitoUsoContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CancelamentoContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.CancelamentoContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GraficosGerenciaisControle</managed-bean-name>
        <managed-bean-class>relatorio.grafico.GraficosGerenciaisControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LogControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.security.LogControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>VendaAvulsaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.VendaAvulsaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CalculadoraControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.CalculadoraControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TrancamentoContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.TrancamentoContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RetornoTrancamentoContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.RetornoTrancamentoContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AulaAvulsaDiariaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.AulaAvulsaDiariaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FreePassControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.FreePassControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>OrcamentoControle</managed-bean-name>
        <managed-bean-class>controle.plano.OrcamentoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoTurmaControle</managed-bean-name>
        <managed-bean-class>controle.plano.GestaoTurmaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoPersonalControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.GestaoPersonalControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RoboControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.RoboControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SituacaoContratoSinteticoDWControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.SituacaoContratoSinteticoDWControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>Showcase</managed-bean-name>
        <managed-bean-class>controle.arquitetura.ShowCaseControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SituacaoClienteSinteticoDWControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.SituacaoClienteSinteticoDWControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SituacaoRenovacaoSinteticoDWControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.SituacaoRenovacaoSinteticoDWControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SituacaoContratoAnaliticoDWControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.SituacaoContratoAnaliticoDWControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SituacaoRenovacaoAnaliticoDWControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.SituacaoRenovacaoAnaliticoDWControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RotatividadeAnaliticoDWControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.RotatividadeAnaliticoDWControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MetaCrescimentoControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.MetaCrescimentoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BonusContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.BonusContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AtestadoContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.AtestadoContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RetornoAtestadoContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.RetornoAtestadoContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ClienteMensagemControle</managed-bean-name>
        <managed-bean-class>controle.basico.ClienteMensagemControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RiscoControle</managed-bean-name>
        <managed-bean-class>controle.basico.RiscoControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LtvControle</managed-bean-name>
        <managed-bean-class>controle.basico.LtvControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AlterarHorarioContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.AlterarHorarioContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ManutencaoModalidadeControle</managed-bean-name>
        <managed-bean-class>controle.contrato.ManutencaoModalidadeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RenovacaoSinteticoControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.RenovacaoSinteticoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>IndiceConversaoVendaRelControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.IndiceConversaoVendaRelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ICVSessaoRelControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.IndiceConversaoVendaSessaoRelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CarenciaContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.CarenciaContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AfastamentoContratoDependenteControle</managed-bean-name>
        <managed-bean-class>controle.contrato.AfastamentoContratoDependenteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RetornoCarenciaContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.RetornoCarenciaContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>EstornoMovProdutoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.EstornoMovProdutoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PendenciaControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.PendenciaControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AcessoGymPassControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.AcessoGymPassControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>EstornoReciboControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.EstornoReciboControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>CupomFiscalControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.CupomFiscalControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>VendaConsumidorControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.VendaConsumidorControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GrupoColaboradorControle</managed-bean-name>
        <managed-bean-class>controle.crm.GrupoColaboradorControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ModeloMensagemControle</managed-bean-name>
        <managed-bean-class>controle.crm.ModeloMensagemControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MensagemBuilderControle</managed-bean-name>
        <managed-bean-class>controle.crm.MensagemBuilderControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MalaDiretaControle</managed-bean-name>
        <managed-bean-class>controle.crm.MalaDiretaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ConfiguracaoSistemaCRMControle</managed-bean-name>
        <managed-bean-class>controle.crm.ConfiguracaoSistemaCRMControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PassivoControle</managed-bean-name>
        <managed-bean-class>controle.crm.PassivoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>IndicacaoControle</managed-bean-name>
        <managed-bean-class>controle.crm.IndicacaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FeriadoControle</managed-bean-name>
        <managed-bean-class>controle.crm.FeriadoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TextoPadraoControle</managed-bean-name>
        <managed-bean-class>controle.crm.TextoPadraoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AgendaControle</managed-bean-name>
        <managed-bean-class>controle.crm.AgendaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AberturaMetaControle</managed-bean-name>
        <managed-bean-class>controle.crm.AberturaMetaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MetaCRMControle</managed-bean-name>
        <managed-bean-class>controle.crm.MetaCRMControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TotalizadorMetaControle</managed-bean-name>
        <managed-bean-class>controle.crm.TotalizadorMetaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BusinessIntelligenceCRMControle</managed-bean-name>
        <managed-bean-class>controle.crm.BusinessIntelligenceCRMControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>HistoricoContatoControle</managed-bean-name>
        <managed-bean-class>controle.crm.HistoricoContatoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>EventoControle</managed-bean-name>
        <managed-bean-class>controle.crm.EventoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>HistoricoVinculoControle</managed-bean-name>
        <managed-bean-class>controle.basico.HistoricoVinculoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ObjecaoControle</managed-bean-name>
        <managed-bean-class>controle.crm.ObjecaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>OperadoraCartaoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.OperadoraCartaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GrupoTelaControle</managed-bean-name>
        <managed-bean-class>controle.basico.GrupoTelaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioClientesControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.RelatorioClientesControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AfastamentoContratoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.AfastamentoContratoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PendenciasCRMControle</managed-bean-name>
        <managed-bean-class>controle.crm.PendenciasCRMControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>treeStateAdvisor</managed-bean-name>
        <managed-bean-class>controle.arquitetura.TreeStateAdvisorImpl</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>planoContasControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.PlanoContasControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ModalRenegociadasControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.ModalRenegociadasControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelControleOperacoesControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.RelControleOperacoesControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>RelControleProbEvas</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.RelControleProbEvas</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>PagamentoCartaoCreditoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.PagamentoCartaoCreditoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>PagamentoCartaoDebitoOnlineControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.PagamentoCartaoDebitoOnlineControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>RelContratosRecorrenciaControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.RelContratosRecorrenciaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>EdicaoPagamentoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.EdicaoPagamentoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoTransacoesControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.GestaoTransacoesControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TrocarCartaoRecorrenciaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.TrocarCartaoRecorrenciaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AlterarVencimentoParcelasControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.AlterarVencimentoParcelasControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CaptchaControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.security.CaptchaControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoRemessasControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.GestaoRemessasControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoBoletosOnlineControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.GestaoBoletosOnlineControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <!--MANAGER BEAN DE CONTROLE DE SKIN DO RICH-FACES-->
    <managed-bean>
        <managed-bean-name>SkinControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.SkinControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
        <managed-property>
            <property-name>skin</property-name>
            <value>glassX</value>
        </managed-property>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MenuControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.MenuControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CapturaFotoControle</managed-bean-name>
        <managed-bean-class>controle.basico.CapturaFotoControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>DataScrollerControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.view.DataScrollerControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ComboBoxEmpresaControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.view.ComboBoxEmpresaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TreeViewControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.view.TreeViewControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TreeViewColaboradorControle</managed-bean-name>
        <managed-bean-class>controle.crm.TreeViewColaboradorControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LogoutControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.security.LogoutControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SmartBoxControle</managed-bean-name>
        <managed-bean-class>controle.modulos.SmartBoxControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ListaClientesControle</managed-bean-name>
        <managed-bean-class>controle.basico.ListaClientesControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>RelatorioBVsControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.RelatorioBVsControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>NotificadorServiceControle</managed-bean-name>
        <managed-bean-class>servicos.notificador.NotificadorServiceControle</managed-bean-class>
        <managed-bean-scope>application</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SessionViewControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.session.SessionViewControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoComissaoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.GestaoComissaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SocialMailingControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.socialmailing.controle.SocialMailingControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AutorizacaoCobrancaControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.autorizacaocobranca.controle.AutorizacaoCobrancaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>NotaFiscalDeServicoControle</managed-bean-name>
        <managed-bean-class>controle.nfe.NotaFiscalDeServicoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ItemNFSeControle</managed-bean-name>
        <managed-bean-class>controle.nfe.ItemNFSeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>UsuarioNFeControle</managed-bean-name>
        <managed-bean-class>controle.nfe.UsuarioNFeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>EmpresaNFeControle</managed-bean-name>
        <managed-bean-class>controle.nfe.EmpresaNFeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PerfilNFeControle</managed-bean-name>
        <managed-bean-class>controle.nfe.PerfilNFeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioRepasseControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.RelatorioRepasseControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ReposicaoControle</managed-bean-name>
        <managed-bean-class>controle.plano.ReposicaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AutorizacaoFuncionalidadeControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.security.AutorizacaoFuncionalidadeControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BoletoBancarioControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.BoletoBancarioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MensagemGenericaControle</managed-bean-name>
        <managed-bean-class>controle.basico.clube.MensagemGenericaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MensagemEntregabilidadeControle</managed-bean-name>
        <managed-bean-class>controle.basico.MensagemEntregabilidadeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>VinculoAgendaControle</managed-bean-name>
        <managed-bean-class>controle.basico.VinculoAgendaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>EnvioEmailContratoReciboControle</managed-bean-name>
        <managed-bean-class>controle.basico.EnvioEmailContratoReciboControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>UCPControle</managed-bean-name>
        <managed-bean-class>controle.basico.UCPControle</managed-bean-class>
        <managed-bean-scope>application</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>UCPUsuarioControle</managed-bean-name>
        <managed-bean-class>controle.basico.UCPUsuarioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ComunicacaoControle</managed-bean-name>
        <managed-bean-class>controle.basico.ComunicacaoControle</managed-bean-class>
        <managed-bean-scope>application</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PushUsuarioControle</managed-bean-name>
        <managed-bean-class>controle.basico.PushUsuarioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>NotificacaoControle</managed-bean-name>
        <managed-bean-class>controle.basico.NotificacaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <!--MANAGER BEAN DE CONTROLE DO ATUALIZADOR DE BANCO DE DADOS-->
    <managed-bean>
        <managed-bean-name>AtualizadorBDControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.atualizadb.controle.AtualizadorBDControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ListasERelatoriosControle</managed-bean-name>
        <managed-bean-class>controle.basico.ListasERelatoriosControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LRVisitantesControle</managed-bean-name>
        <managed-bean-class>controle.basico.LRVisitantesControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LRAlunosCanceladosControle</managed-bean-name>
        <managed-bean-class>controle.basico.LRAlunosCanceladosControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LRAlunosTrancadosControle</managed-bean-name>
        <managed-bean-class>controle.basico.LRAlunosTrancadosControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LRAlunosBonusControle</managed-bean-name>
        <managed-bean-class>controle.basico.LRAlunosBonusControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LRAlunosAtestadoControle</managed-bean-name>
        <managed-bean-class>controle.basico.LRAlunosAtestadoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ExportadorListaControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>UsuarioMovelControle</managed-bean-name>
        <managed-bean-class>controle.modulos.integracao.UsuarioMovelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MovideskControle</managed-bean-name>
        <managed-bean-class>controle.modulos.integracao.MovideskControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>IntegracaoAcessoGrupoEmpresarialControle</managed-bean-name>
        <managed-bean-class>controle.acesso.IntegracaoAcessoGrupoEmpresarialControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AutorizacaoAcessoGrupoEmpresarialControle</managed-bean-name>
        <managed-bean-class>controle.acesso.AutorizacaoAcessoGrupoEmpresarialControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>DicasControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.DicasControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RetiradaAutomaticaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.RetiradaAutomaticaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GeradorConsultasControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.GeradorConsultasControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ComissaoGeralConfiguracaoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.ComissaoGeralConfiguracaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ComissaoControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.ComissaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AjusteSaldoContaCorrenteControle</managed-bean-name>
        <managed-bean-class>controle.basico.AjusteSaldoContaCorrenteControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RenovarProdutoControle</managed-bean-name>
        <managed-bean-class>controle.basico.RenovarProdutoControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ProdutoRelControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.financeiro.ProdutoRelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoNotasControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.GestaoNotasControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoNFCeControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.GestaoNFCeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LancamentoProdutoColetivoControle</managed-bean-name>
        <managed-bean-class>controle.plano.LancamentoProdutoColetivoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>ExcluirFinanceiroControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.ExcluirFinanceiroControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AtestadoControle</managed-bean-name>
        <managed-bean-class>controle.contrato.AtestadoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BITicketMedioControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.BITicketMedioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>PreCadastroClienteControle</managed-bean-name>
        <managed-bean-class>controle.basico.PreCadastroClienteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>FeedGestaoControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.FeedGestaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RenegociacaoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.RenegociacaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FinanceiroPactoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.FinanceiroPactoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>RelatorioAgendamentosControle</managed-bean-name>
        <managed-bean-class>controle.crm.RelatorioAgendamentosControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>GestaoArmarioControle</managed-bean-name>
        <managed-bean-class>controle.armario.GestaoArmarioControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TamanhoArmarioControle</managed-bean-name>
        <managed-bean-class>controle.armario.TamanhoArmarioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FuncionalidadeControle</managed-bean-name>
        <managed-bean-class>controle.basico.FuncionalidadeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>    
    
    <managed-bean>
        <managed-bean-name>RelatorioContatoAppControle</managed-bean-name>
        <managed-bean-class>controle.crm.RelatorioContatoAppControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SorteioControle</managed-bean-name>
        <managed-bean-class>controle.basico.SorteioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>OperacaoColetivaControle</managed-bean-name>
        <managed-bean-class>controle.basico.OperacaoColetivaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BIControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.BIControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ClientesMarcadosControle</managed-bean-name>
        <managed-bean-class>controle.basico.ClientesMarcadosControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TelaClienteControle</managed-bean-name>
        <managed-bean-class>controle.basico.TelaClienteControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RedirectNovoFrontControle</managed-bean-name>
        <managed-bean-class>controle.basico.RedirectNovoFrontControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RedirectClientesNovoFrontControle</managed-bean-name>
        <managed-bean-class>controle.basico.RedirectClientesNovoFrontControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PesquisaVisualizacaoControle</managed-bean-name>
        <managed-bean-class>controle.basico.PesquisaVisualizacaoControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>NotaFiscalControle</managed-bean-name>
        <managed-bean-class>controle.notaFiscal.NotaFiscalControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ConvidadoControle</managed-bean-name>
        <managed-bean-class>controle.basico.ConvidadoControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FamiliaresControle</managed-bean-name>
        <managed-bean-class>controle.basico.FamiliaresControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>NegociacaoFamiliaresControle</managed-bean-name>
        <managed-bean-class>controle.contrato.NegociacaoFamiliaresControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoVendasOnlineControle</managed-bean-name>
        <managed-bean-class>controle.vendasonline.GestaoVendasOnlineControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AdquiraVendasOnlineControle</managed-bean-name>
        <managed-bean-class>controle.vendasonline.AdquiraVendasOnlineControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AtivarClubeVantagensControle</managed-bean-name>
        <managed-bean-class>controle.clubevantagens.AtivarClubeVantagensControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AdquiraSMSControle</managed-bean-name>
        <managed-bean-class>controle.crm.AdquiraSMSControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>QuarentenaControle</managed-bean-name>
        <managed-bean-class>controle.crm.QuarentenaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BIFamiliaControle</managed-bean-name>
        <managed-bean-class>controle.basico.BIFamiliaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LinhaTempoContratoControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.linhatempocontrato.controle.LinhaTempoContratoControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BlogControle</managed-bean-name>
        <managed-bean-class>controle.basico.BlogControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BIConviteAulaExperimentalControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.sad.BIConviteAulaExperimentalControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ShowCaseControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.ShowCaseControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ClientesVerificadosRelControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.ClientesVerificadosRelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>MensagemExceptionControle</managed-bean-name>
        <managed-bean-class>controle.arquitetura.MensagemExceptionControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FixBoletoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.FixBoletoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>ListaConvidadosRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.ListaConvidadosRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ListaGymPassRelControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.ListaGymPassRelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioPessoasBloqueioCobrancaControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.RelatorioPessoasBloqueioCobrancaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MovimentacaoAutomaticaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.MovimentacaoAutomaticaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RegistrarAcessoAvulsoControle</managed-bean-name>
        <managed-bean-class>controle.basico.RegistrarAcessoAvulsoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GympassControle</managed-bean-name>
        <managed-bean-class>controle.basico.GympassControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BrindeControle</managed-bean-name>
        <managed-bean-class>controle.basico.BrindeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>HistoricoPontosControle</managed-bean-name>
        <managed-bean-class>controle.basico.HistoricoPontosControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>LancarBrindeClienteControle</managed-bean-name>
        <managed-bean-class>controle.basico.LancarBrindeClienteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ServidorFacialControle</managed-bean-name>
        <managed-bean-class>controle.acesso.ServidorFacialControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>NotificacaoHorarioAgendadoControle</managed-bean-name>
        <managed-bean-class>controle.basico.NotificacaoHorarioAgendadoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>GestaoAcessoRelControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.GestaoAcessoRelControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>BIInadimplenciaControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.BIInadimplenciaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>IndicadorAcessoControleRel</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.IndicadorAcessoControleRel</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ClubeVantagensControle</managed-bean-name>
        <managed-bean-class>controle.clubevantagens.ClubeVantagensControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>ItemCampanhaControle</managed-bean-name>
        <managed-bean-class>controle.clubevantagens.ItemCampanhaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>InclusaoVendaRapidaControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.InclusaoVendaRapidaControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PlanoTipoControle</managed-bean-name>
        <managed-bean-class>controle.basico.PlanoTipoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MenuAcessoFacilControle</managed-bean-name>
        <managed-bean-class>controle.basico.MenuAcessoFacilControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SGPModalidadeComTurmaControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.SGPModalidadeComTurmaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SGPTurmaControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.SGPTurmaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SGPModalidadeSemTurmaControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.SGPModalidadeSemTurmaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>SGPAvalicaoFisicaControle</managed-bean-name>
        <managed-bean-class>relatorio.controle.basico.SGPAvalicaoFisicaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

<!--    <managed-bean>-->
<!--        <managed-bean-name>PrintScreenControle</managed-bean-name>-->
<!--        <managed-bean-class>controle.arquitetura.PrintScreenControle</managed-bean-class>-->
<!--        <managed-bean-scope>request</managed-bean-scope>-->
<!--    </managed-bean>-->

    <managed-bean>
        <managed-bean-name>PixController</managed-bean-name>
        <managed-bean-class>controle.pix.PixController</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>AssinaturaDigitalControle</managed-bean-name>
        <managed-bean-class>controle.contrato.AssinaturaDigitalControle</managed-bean-class>
        <managed-bean-scope>request</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PixRelatorioController</managed-bean-name>
        <managed-bean-class>controle.pix.PixRelatorioController</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GetCardRelatorioController</managed-bean-name>
        <managed-bean-class>controle.getcard.GetCardRelatorioController</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PinpadPedidoRelatorioControle</managed-bean-name>
        <managed-bean-class>controle.basico.PinpadPedidoRelatorioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>PactoPayComunicacaoControle</managed-bean-name>
        <managed-bean-class>controle.basico.PactoPayComunicacaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioTokensOperacoesControle</managed-bean-name>
        <managed-bean-class>controle.basico.RelatorioTokensOperacoesControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>TokenOperacaoControle</managed-bean-name>
        <managed-bean-class>controle.basico.TokenOperacaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>HealthControle</managed-bean-name>
        <managed-bean-class>controle.basico.HealthControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>LocalImpressaoControle</managed-bean-name>
        <managed-bean-class>controle.basico.pactoprint.LocalImpressaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
</faces-config>
