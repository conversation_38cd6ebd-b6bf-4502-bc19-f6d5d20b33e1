<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_EstornoRecibo_tituloForm}"/>
    </title>

    <rich:modalPanel id="panelConfirmacaoEstornar" autosized="true"
                     shadowOpacity="true" width="450" height="250"
                     styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação Data Estorno Recibo"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink"/>
                <rich:componentControl for="panelConfirmacaoEstornar"
                                       attachTo="hidelink" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacaoEstornar" styleClass="font-size-Em-max">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid id="panelConfirmacao" columns="2" width="100%" cellpadding="10"
                             columnClasses="colunaEsquerda w30, colunaEsquerda w70" styleClass="font-size-Em-max">

                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Data do estorno:"
                                  rendered="#{EstornoReciboControle.dataRetroativa}"/>
                    <h:panelGroup styleClass="dateTimeCustom" rendered="#{EstornoReciboControle.dataRetroativa}">
                        <rich:calendar id="dataEstorno"
                                       value="#{EstornoReciboControle.dataEstorno}"
                                       inputSize="10"
                                       inputClass="form"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>
                    <h:inputText style="opacity:0;height: 0px;" size="5" maxlength="7"/>
                    <h:inputSecret size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                </h:panelGrid>
                <h:panelGrid id="msg" columns="2" width="100%">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagemDetalhada"
                                      rendered="#{EstornoReciboControle.estornoReciboVO.mostrarMsgExcluirNFse}"
                                      value="Já existe nota fiscal emitida para recibo(s) deste contrato, que não são canceladas automaticamente. Deseja estornar mesmo assim?"/>
                    </h:panelGrid>
                </h:panelGrid>
                <rich:spacer height="10px"></rich:spacer>
                <h:panelGroup>
                    <a4j:commandButton id="login" value="#{msg_bt.btn_confirmar}"
                                       styleClass="botoes nvoBt"
                                       rendered="#{!EstornoReciboControle.estornoReciboVO.mostrarMsgExcluirNFse}"
                                       action="#{EstornoReciboControle.abrirConfirmacaoEstornar}"
                                       oncomplete="#{EstornoReciboControle.mensagemNotificar}"
                                       reRender="form,panelConfirmacao,msg,panelMensagem, formConfirmacaoEstornar, panelAutorizacaoFuncionalidade, mdlMensagemGenerica"/>

                    <a4j:commandButton value="Estornar mesmo assim"
                                       rendered="#{EstornoReciboControle.estornoReciboVO.mostrarMsgExcluirNFse}"
                                       action="#{EstornoReciboControle.confirmarEstornoReciboComNFSe}"
                                       oncomplete="#{EstornoReciboControle.msgAlert}#{EstornoReciboControle.mensagemNotificar}"
                                       reRender="form,panelConfirmacao,msg,panelMensagem, mdlMensagemGenerica, panelAutorizacaoFuncionalidade"
                                       styleClass="botoes nvoBt btSec"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConfirmacaoEmitir" autosized="true"
                     shadowOpacity="true" width="450" height="250"
                     onshow="document.getElementById('formConfirmacaoEmitir:senha2').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkConfirmacaoEmitir"/>
                <rich:componentControl for="panelConfirmacaoEmitir"
                                       attachTo="hidelinkConfirmacaoEmitir" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacaoEmitir">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario"
                                  value="Confirmação do Emissão de CF para o Recibo"/>
                </h:panelGrid>
                <h:panelGrid id="panelEmissao" columns="1" width="100%"
                             columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText style="opacity:0;height: 0px;" size="5" maxlength="7"/>
                        <h:inputSecret size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Código:"/>
                        <h:inputText id="codigoUsuario2" size="5" maxlength="7"
                                     style="margin-left:5px"
                                     value="#{EstornoReciboControle.estornoReciboVO.responsavelEstornoRecivo.codigo}">
                            <a4j:support event="onchange"
                                         focus="formConfirmacaoEstornar:senha"
                                         action="#{EstornoReciboControle.consultarResponsavelEstornoRecibo}"
                                         reRender="panelConfirmacao, msg"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden2" size="3" maxlength="10" style="margin-left:6px;opacity:0;"
                                     value="#{EstornoReciboControle.estornoReciboVO.responsavelEstornoRecivo.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Usuário:"/>
                        <h:outputText styleClass="text" style="margin-left:6px"
                                      value="#{EstornoReciboControle.estornoReciboVO.responsavelEstornoRecivo.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha2" size="14" maxlength="64"
                                       style="margin-left:8px"
                                       value="#{EstornoReciboControle.estornoReciboVO.responsavelEstornoRecivo.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('login')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="msg" columns="2" width="100%">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{EstornoReciboControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <a4j:commandButton id="login2" value="#{msg_bt.btn_confirmar}"
                                   image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}"
                                   action="#{EstornoReciboControle.emitirCupom}"
                                   oncomplete="#{EstornoReciboControle.fecharRichModalPanelEmissao}"
                                   reRender="form,panelEmissao,msg,panelMensagem"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConfirmacaoEmitirPagamento" autosized="true"
                     shadowOpacity="true" width="450" height="250"
                     onshow="document.getElementById('formConfirmacaoEmitirPagamento:senha2').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkConfirmacaoEmitirPagamento"/>
                <rich:componentControl for="panelConfirmacaoEmitirPagamento"
                                       attachTo="hidelinkConfirmacaoEmitirPagamento" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacaoEmitirPagamento">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText rendered="#{EstornoReciboControle.emissaoMovPagamento}" styleClass="tituloFormulario"
                                  value="Confirmação do Emissão de CF para o Pagamento"/>
                    <h:outputText rendered="#{EstornoReciboControle.emissaoCheque}" styleClass="tituloFormulario"
                                  value="Confirmação do Emissão de CF para o Cheque"/>
                    <h:outputText rendered="#{EstornoReciboControle.emissaoCartao}" styleClass="tituloFormulario"
                                  value="Confirmação do Emissão de CF para Parcela de Cartão"/>

                </h:panelGrid>
                <h:panelGrid id="panelEmissao" columns="1" width="100%"
                             columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText style="opacity:0;height: 0px;" size="5" maxlength="7"/>
                        <h:inputSecret size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Código:"/>
                        <h:inputText id="codigoUsuario2" size="5" maxlength="7"
                                     style="margin-left:5px"
                                     value="#{EstornoReciboControle.estornoReciboVO.responsavelEstornoRecivo.codigo}">
                            <a4j:support event="onchange"
                                         focus="formConfirmacaoEstornar:senha"
                                         action="#{EstornoReciboControle.consultarResponsavelEstornoRecibo}"
                                         reRender="panelConfirmacao, msg"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden2" size="3" maxlength="10" style="margin-left:6px;opacity:0;"
                                     value="#{EstornoReciboControle.estornoReciboVO.responsavelEstornoRecivo.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Usuário:"/>
                        <h:outputText styleClass="text" style="margin-left:6px"
                                      value="#{EstornoReciboControle.estornoReciboVO.responsavelEstornoRecivo.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha2" size="14" maxlength="64"
                                       style="margin-left:8px"
                                       value="#{EstornoReciboControle.estornoReciboVO.responsavelEstornoRecivo.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('login')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="msg" columns="2" width="100%">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{EstornoReciboControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <a4j:commandButton rendered="#{EstornoReciboControle.emissaoMovPagamento}" id="loginPagamento"
                                   value="#{msg_bt.btn_confirmar}"
                                   image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}"
                                   action="#{EstornoReciboControle.emitirCupomPagamento}"
                                   oncomplete="#{EstornoReciboControle.fecharRichModalPanelEmissaoPagamento}"
                                   reRender="form,panelEmissao,msg,panelMensagem"/>
                <a4j:commandButton rendered="#{EstornoReciboControle.emissaoCheque}" id="loginCheque"
                                   value="#{msg_bt.btn_confirmar}"
                                   image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}"
                                   action="#{EstornoReciboControle.emitirCupomCheque}"
                                   oncomplete="#{EstornoReciboControle.fecharRichModalPanelEmissaoPagamento}"
                                   reRender="form,panelEmissao,msg,panelMensagem"/>
                <a4j:commandButton rendered="#{EstornoReciboControle.emissaoCartao}" id="loginCartao"
                                   value="#{msg_bt.btn_confirmar}"
                                   image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}"
                                   action="#{EstornoReciboControle.emitirCupomCartao}"
                                   oncomplete="#{EstornoReciboControle.fecharRichModalPanelEmissaoPagamento}"
                                   reRender="form,panelEmissao,msg,panelMensagem"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConfirmacaoNFSE" autosized="true"
                     shadowOpacity="true" width="450" height="250"
                     onshow="document.getElementById('formConfirmacaoNFSE:senha2').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação do Envio de Nota Fiscal de Serviço Eletrônica"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkConfirmacaoNFSE"/>
                <rich:componentControl for="panelConfirmacaoNFSE"
                                       attachTo="hidelinkConfirmacaoNFSE" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacaoNFSE">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario"
                                  value="Envio de Nota Fiscal de Serviço Eletrônica"/>
                </h:panelGrid>
                <h:panelGrid id="panelEmissao" columns="1" width="100%"
                             columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText style="opacity:0;height: 0px;" size="5" maxlength="7"/>
                        <h:inputSecret size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Código:"/>
                        <h:inputText id="codigoUsuario2" size="5" maxlength="7"
                                     style="margin-left:5px"
                                     value="#{EstornoReciboControle.usuarioEnvioNFSe.codigo}">
                            <a4j:support event="onchange"
                                         focus="formConfirmacaoNFSE:senha"
                                         action="#{EstornoReciboControle.consultarResponsavelNFSE}"
                                         reRender="formConfirmacaoNFSE, msg"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden2" size="3" maxlength="10" style="margin-left:6px;opacity:0;"
                                     value="#{EstornoReciboControle.usuarioEnvioNFSe.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Usuário:"/>
                        <h:outputText styleClass="text" style="margin-left:6px"
                                      value="#{EstornoReciboControle.usuarioEnvioNFSe.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha2" size="14" maxlength="64"
                                       style="margin-left:8px"
                                       value="#{EstornoReciboControle.usuarioEnvioNFSe.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('login')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="msg" columns="2" width="100%">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{EstornoReciboControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <a4j:commandButton id="login2" value="#{msg_bt.btn_confirmar}"
                                   image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}"
                                   action="#{EstornoReciboControle.emitirNFSe}"
                                   oncomplete="#{EstornoReciboControle.msgAlert}"
                                   reRender="form,panelEmissao,msg,panelMensagem"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConfirmacaoNFCE" autosized="true"
                     shadowOpacity="true" width="450" height="250"
                     onshow="document.getElementById('formConfirmacaoNFCE:senha2').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação do Envio de NFC-e"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkConfirmacaoNFCE"/>
                <rich:componentControl for="panelConfirmacaoNFCE"
                                       attachTo="hidelinkConfirmacaoNFCE" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacaoNFCE">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario"
                                  value="Envio de NFC-e"/>
                </h:panelGrid>
                <h:panelGrid id="panelEmissao" columns="1" width="100%"
                             columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText style="opacity:0;height: 0px;" size="5" maxlength="7"/>
                        <h:inputSecret size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Código:"/>
                        <h:inputText id="codigoUsuario2" size="5" maxlength="7"
                                     style="margin-left:5px"
                                     value="#{EstornoReciboControle.usuarioEnvioNFCe.codigo}">
                            <a4j:support event="onchange"
                                         focus="formConfirmacaoNFCE:senha"
                                         action="#{EstornoReciboControle.consultarResponsavelNFCe}"
                                         reRender="formConfirmacaoNFCE, msg"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden2" size="3" maxlength="10" style="margin-left:6px;opacity:0;"
                                     value="#{EstornoReciboControle.usuarioEnvioNFCe.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Usuário:"/>
                        <h:outputText styleClass="text" style="margin-left:6px"
                                      value="#{EstornoReciboControle.usuarioEnvioNFCe.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha2" size="14" maxlength="64"
                                       style="margin-left:8px"
                                       value="#{EstornoReciboControle.usuarioEnvioNFCe.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('login')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="msg" columns="2" width="100%">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{EstornoReciboControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <a4j:commandButton id="login2" value="#{msg_bt.btn_confirmar}"
                                   image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}"
                                   action="#{EstornoReciboControle.emitirNFCe}"
                                   oncomplete="#{EstornoReciboControle.mensagemNotificar};#{EstornoReciboControle.msgAlert}"
                                   reRender="form,panelEmissao,msg,panelMensagem"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalEstornoGetcard"  styleClass="novaModal noMargin" shadowOpacity="true"
                     width="500" height="375">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Estorno GetCard (Scope)"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hideModalEstornoGetcard"/>
                <rich:componentControl for="modalEstornoGetcard"
                                       attachTo="hideModalEstornoGetcard" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup layout="block" style="display: flex; justify-content: center; align-items: center; flex-direction: column;">
            <a4j:form id="formModalEstornoGetcard" style="height: 100%; text-align: center">
                <h:panelGroup id="panelGeralModalPinpad" layout="block"
                              style="text-align: center;">
                    <div style="padding-top: 20px; text-align: center;">
                        <h:graphicImage url="images/logo-getcard.svg"
                                        style="height: 40px;vertical-align: middle;"/>
                    </div>

                    <h:outputText style="display: block; text-transform: uppercase; margin: 10px 0; text-align: center;"
                                  value="#{msg_aplic.prt_estorno_cappta_senha_nr_controle}"
                                  styleClass="texto-bold texto-font texto-cor-cinza"/>
                    <h:inputText id="nrControleGetcard"
                                 disabled="#{not empty EstornoReciboControle.pinpad.nrControle}"
                                 value="#{EstornoReciboControle.pinpad.nrControle}"
                                 style="width: 100px; text-align: center;"/>

                    <h:panelGroup layout="block"
                                  id="panelMensagemPinPad"
                                  style="color: #51555a; margin-bottom: 20px; font-family: 'Nunito Sans', sans-serif; font-size: 24px;font-weight: 400; padding-top: 20px; text-align: center">
                        <h:outputText id="mensagemPinPad"
                                      style="color: #51555a; font-family: 'Nunito Sans', sans-serif; font-size: 24px;font-weight: 400;"
                                      value=""/>
                    </h:panelGroup>

                    <div style="padding-top: 20px; text-align: center;" id="divBotoesPinpad">
                        <a4j:commandLink style="display: inline-block; background: #da2128"
                                         id="solicitarCancelamentoGetCard"
                                         reRender="formModalEstornoGetcard:solicitarCancelamentoGetCard, formModalEstornoGetcard:desfazerSolicitarCancelamentoGetCard"
                                         rendered="#{!EstornoReciboControle.exibirBotaoAbortarEstorno}"
                                         onclick="estornoGetcard()"
                                         styleClass="botoes nvoBt">
                            <h:outputText value="Solicitar cancelamento"/>
                        </a4j:commandLink>
                        <a4j:commandLink style="display: inline-block;"
                                         id="desfazerSolicitarCancelamentoGetCard"
                                         reRender="formModalEstornoGetcard:solicitarCancelamentoGetCard, formModalEstornoGetcard:desfazerSolicitarCancelamentoGetCard"
                                         rendered="#{EstornoReciboControle.exibirBotaoAbortarEstorno}"
                                         oncomplete="abortarOperacaoGetcard()"
                                         title="Clique para abortar a operação caso não queira prosseguir com o cancelamento da transação"
                                         styleClass="botoes nvoBt tooltipster">
                            <h:outputText value="Abortar operação"/>
                        </a4j:commandLink>
                    </div>
                </h:panelGroup>
            </a4j:form>
        </h:panelGroup>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <c:set var="titulo" scope="session" value="${msg_aplic.prt_EstornoRecibo_tituloForm}"/>
        <c:set var="urlWiki" scope="session"
               value="${SuperControle.urlBaseConhecimento}como-estornar-o-recibo-de-pagamento-de-um-colaborador/"/>

        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>

        </f:facet>
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                             width="100%">
                    <h:panelGrid columns="1" columnClasses="colunaEsquerda"
                                 width="100%">
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_EstornoRecibo_reciboNumero}"/>
                            <rich:spacer width="5px"/>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{EstornoReciboControle.estornoReciboVO.reciboPagamentoVO.codigo}"/>
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_EstornoRecibo_nome}"/>
                            <rich:spacer width="5px"/>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{EstornoReciboControle.estornoReciboVO.reciboPagamentoVO.nomePessoaPagador}"/>
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCamposVerdeGrande"
                                          value="#{msg_aplic.prt_EstornoRecibo_valorTotal}"/>
                            <rich:spacer width="5px"/>
                            <h:outputText styleClass="tituloCamposVerdeGrande" value="R$"/>
                            <rich:spacer width="5px"/>
                            <h:outputText styleClass="tituloCamposVerdeGrande"
                                          value="#{EstornoReciboControle.estornoReciboVO.reciboPagamentoVO.valorTotal}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" columnClasses="colunaEsquerda"
                                 width="100%">
                        <rich:dataTable id="movPagamento" width="100%" border="0"
                                        cellspacing="0" cellpadding="0" styleClass="textsmall"
                                        columnClasses="centralizado, centralizado, centralizado"
                                        value="#{EstornoReciboControle.estornoReciboVO.listaMovPagamento}"
                                        var="historicoPagamentos">
                            <f:facet name="header">
                                <h:outputText styleClass="titulocampos"
                                              value="Pagamentos do Recibo"/>
                            </f:facet>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_codigo}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoPagamentos.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_nomePagador}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoPagamentos.nomePagador}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_formaPagamento}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoPagamentos.formaPagamento.descricao}"/>
                                <h:outputText rendered="#{historicoPagamentos.credito}"
                                              title="Crédito Conta Corrente do Aluno" style="font-weight: bold"
                                              styleClass="red"
                                              value="#{historicoPagamentos.creditoApresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_tipo_formaPagamento}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento_Apresentar}"/>
                                <h:outputText rendered="#{historicoPagamentos.credito}"
                                              title="Crédito Conta Corrente do Aluno" style="font-weight: bold"
                                              styleClass="red"
                                              value="#{historicoPagamentos.creditoApresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_valor}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoPagamentos.valorTotal}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoPagamentos.dataLancamento}">
                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                </h:outputText>
                            </rich:column>
                            <rich:column
                                    rendered="#{EstornoReciboControle.usaEcf && EstornoReciboControle.usaEcfPagamento}">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_Opcoes}"/>
                                </f:facet>
                                <rich:spacer width="5px"/>

                                <a4j:commandButton id="emitirCupomCheque"
                                                   rendered="#{!historicoPagamentos.cupomEmitido && historicoPagamentos.formaPagamento.tipoFormaPagamento != 'CC'}"
                                                   actionListener="#{EstornoReciboControle.prepararCupomPagamento}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao,panelConfirmacaoEmitirPagamento"
                                                   image="./images/btn_emitir_cupomParcela.png"
                                                   title="Emitir cupom deste pagamento."
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoEmitirPagamento');">
                                    <f:attribute name="pagamento" value="#{historicoPagamentos}"/>
                                </a4j:commandButton>

                                <a4j:commandButton id="informacaoCupomCheque"
                                                   title="Este pagamento já teve cupom emitido."
                                                   rendered="#{historicoPagamentos.cupomEmitido}"
                                                   image="./images/btn_cupomEmitido.png"/>
                                <a4j:commandButton id="informacaoContaCorrente"
                                                   title="Este pagamento é de conta corrente, não pode gerar cupom."
                                                   rendered="#{historicoPagamentos.formaPagamento.tipoFormaPagamento == 'CC'}"
                                                   image="./images/btn_cupomEmitido.png"/>

                                <rich:spacer width="5px"/>
                            </rich:column>

                            <rich:column rendered="#{EstornoReciboControle.apresentarBotoaoNFSePagamento}">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="NFSe"/>
                                </f:facet>
                                <rich:spacer width="5px"/>

                                <a4j:commandButton id="emitirNFSePagamento"
                                                   rendered="#{!historicoPagamentos.nfseEmitido}"
                                                   actionListener="#{EstornoReciboControle.prepararNFSePagamento}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFSE"
                                                   image="./images/btn_emitir_cupomParcela.png"
                                                   title="Emitir NFSe deste pagamento."
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFSE');">
                                    <f:attribute name="pagamento" value="#{historicoPagamentos}"/>
                                </a4j:commandButton>

                                <a4j:commandButton id="informacaoNFsePagamento"
                                                   title="Este pagamento já teve NFSe emitida."
                                                   rendered="#{historicoPagamentos.nfseEmitido}"
                                                   image="./images/btn_cupomEmitido.png"
                                                   actionListener="#{EstornoReciboControle.prepararNFSePagamento}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFSE"
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFSE');"/>
                                <rich:spacer width="5px"/>
                            </rich:column>

                            <rich:column rendered="#{EstornoReciboControle.apresentarBotoaoNFCePagamento}">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="NFC-e"/>
                                </f:facet>
                                <rich:spacer width="5px"/>

                                <a4j:commandButton id="emitirNFCePagamento"
                                                   rendered="#{!historicoPagamentos.nfceEmitido}"
                                                   actionListener="#{EstornoReciboControle.prepararNFCePagamento}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFCE"
                                                   image="./images/btn_emitir_cupomParcela.png"
                                                   title="Emitir NFCe deste pagamento."
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFCE');">
                                    <f:attribute name="pagamento" value="#{historicoPagamentos}"/>
                                </a4j:commandButton>

                                <a4j:commandButton id="informacaoNFCePagamento"
                                                   title="Este pagamento já teve NFCe emitida."
                                                   rendered="#{historicoPagamentos.nfceEmitido}"
                                                   image="./images/btn_cupomEmitido.png"
                                                   actionListener="#{EstornoReciboControle.prepararNFCePagamento}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFCE"
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFCE');"/>
                                <rich:spacer width="5px"/>
                            </rich:column>

                        </rich:dataTable>
                    </h:panelGrid>

                    <rich:spacer rendered="#{!empty EstornoReciboControle.listaCheques}" height="20px"/>
                    <h:panelGrid rendered="#{!empty EstornoReciboControle.listaCheques}" columns="1"
                                 columnClasses="colunaEsquerda"
                                 width="100%">
                        <rich:dataTable id="listaHistoricoCheques" width="100%" border="0"
                                        cellspacing="0" cellpadding="0" styleClass="textsmall"
                                        columnClasses="centralizado, centralizado, centralizado"
                                        value="#{EstornoReciboControle.listaCheques}" var="historicoCheques">
                            <f:facet name="header">
                                <h:outputText styleClass="titulocampos"
                                              value="Lista de Cheques"/>
                            </f:facet>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="#{msg_aplic.prt_Cheque_codigo}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCheques.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="#{msg_aplic.prt_Cheque_banco}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCheques.banco.nome}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="#{msg_aplic.prt_Cheque_agencia}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCheques.agencia}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="#{msg_aplic.prt_Cheque_conta}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCheques.conta}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_numeroCheque}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCheques.numero}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="#{msg_aplic.prt_Cheque_data}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCheques.dataOriginalApresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_nomeNoCheque}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCheques.nomeNoCheque}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="#{msg_aplic.prt_Cheque_valor}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCheques.valor}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>
                            <rich:column
                                    rendered="#{EstornoReciboControle.usaEcf && EstornoReciboControle.usaEcfPagamento}">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_Opcoes}"/>
                                </f:facet>
                                <rich:spacer width="5px"/>

                                <a4j:commandButton id="emitirCupomCheque"
                                                   rendered="#{!historicoCheques.cupomEmitido && historicoCheques.situacao != 'CA'}"
                                                   actionListener="#{EstornoReciboControle.prepararCupomCheque}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, panelConfirmacaoEmitirPagamento"
                                                   image="./images/btn_emitir_cupomParcela.png"
                                                   title="Emitir cupom deste cheque."
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoEmitirPagamento');">
                                    <f:attribute name="cheque" value="#{historicoCheques}"/>
                                </a4j:commandButton>

                                <a4j:commandButton id="informacaoCupomCheque"
                                                   title="Este cheque já teve cupom emitido."
                                                   rendered="#{historicoCheques.cupomEmitido && historicoCheques.situacao != 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"/>
                                <a4j:commandButton id="informacaoCupomChequeCan"
                                                   title="Este cheque está cancelado"
                                                   rendered="#{historicoCheques.situacao == 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"/>


                                <rich:spacer width="5px"/>
                            </rich:column>

                            <rich:column rendered="#{EstornoReciboControle.apresentarBotoaoNFSePagamento}">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="NFSe"/>
                                </f:facet>
                                <rich:spacer width="5px"/>

                                <a4j:commandButton id="emitirNFSeCheque"
                                                   rendered="#{!historicoCheques.nfseEmitido && historicoCheques.situacao != 'CA'}"
                                                   actionListener="#{EstornoReciboControle.prepararNFSeCheque}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFSE"
                                                   image="./images/btn_emitir_cupomParcela.png"
                                                   title="Emitir NFSe deste cheque."
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFSE');">
                                    <f:attribute name="cheque" value="#{historicoCheques}"/>
                                </a4j:commandButton>

                                <a4j:commandButton id="informacaoNFSeCheque"
                                                   title="Este cheque já teve NFSe emitida."
                                                   rendered="#{historicoCheques.nfseEmitido && historicoCheques.situacao != 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"
                                                   actionListener="#{EstornoReciboControle.prepararNFSeCheque}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFSE"
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFSE');"/>
                                <a4j:commandButton id="informacaoNFSeChequeCan"
                                                   title="Este cheque está cancelado"
                                                   rendered="#{historicoCheques.situacao == 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"/>
                                <rich:spacer width="5px"/>
                            </rich:column>


                            <rich:column rendered="#{EstornoReciboControle.apresentarBotoaoNFCePagamento}">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="NFC-e"/>
                                </f:facet>
                                <rich:spacer width="5px"/>

                                <a4j:commandButton id="emitirNFCeCheque"
                                                   rendered="#{!historicoCheques.nfceEmitido && historicoCheques.situacao != 'CA'}"
                                                   actionListener="#{EstornoReciboControle.prepararNFCeCheque}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFCE"
                                                   image="./images/btn_emitir_cupomParcela.png"
                                                   title="Emitir NFSe deste cheque."
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFCE');">
                                    <f:attribute name="cheque" value="#{historicoCheques}"/>
                                </a4j:commandButton>

                                <a4j:commandButton id="informacaoNFCeCheque"
                                                   title="Este cheque já teve NFCe emitida."
                                                   rendered="#{historicoCheques.nfceEmitido && historicoCheques.situacao != 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"
                                                   actionListener="#{EstornoReciboControle.prepararNFCeCheque}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFCE"
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFCE');"/>
                                <a4j:commandButton id="informacaoNFCeChequeCan"
                                                   title="Este cheque está cancelado"
                                                   rendered="#{historicoCheques.situacao == 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"/>
                                <rich:spacer width="5px"/>
                            </rich:column>

                        </rich:dataTable>
                    </h:panelGrid>
                    <rich:spacer rendered="#{!empty EstornoReciboControle.listaCartoes}" height="20px"/>
                    <h:panelGrid rendered="#{!empty EstornoReciboControle.listaCartoes}" columns="1"
                                 columnClasses="colunaEsquerda"
                                 width="100%">
                        <rich:dataTable id="listaHistoricoCartoes" width="100%" border="0"
                                        cellspacing="0" cellpadding="0" styleClass="textsmall"
                                        columnClasses="centralizado, centralizado, centralizado"
                                        value="#{EstornoReciboControle.listaCartoes}" var="historicoCartao">
                            <f:facet name="header">
                                <h:outputText styleClass="titulocampos"
                                              value="Lista de Parcelas do Cartão de Crédito"/>
                            </f:facet>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="#{msg_aplic.prt_Cheque_codigo}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCartao.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_OperadoraCartao_tituloForm}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCartao.operadora.descricao}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="#{msg_aplic.prt_Cheque_data}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCartao.dataCompensacao}">
                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="#{msg_aplic.prt_Cheque_valor}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoCartao.valor}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>

                            <rich:column
                                    rendered="#{EstornoReciboControle.usaEcf && EstornoReciboControle.usaEcfPagamento}">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_Opcoes}"/>
                                </f:facet>
                                <rich:spacer width="5px"/>

                                <a4j:commandButton id="emitirCupomCartao"
                                                   rendered="#{!historicoCartao.cupomEmitido && historicoCartao.situacao != 'CA'}"
                                                   actionListener="#{EstornoReciboControle.prepararCupomCartao}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao,panelConfirmacaoEmitirPagamento"
                                                   image="./images/btn_emitir_cupomParcela.png"
                                                   title="Emitir cupom desta parcela do Cartão."
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoEmitirPagamento');">
                                    <f:attribute name="cartao" value="#{historicoCartao}"/>
                                </a4j:commandButton>

                                <a4j:commandButton id="informacaoCupomCartao"
                                                   title="Este parcela do cartão já teve cupom emitido."
                                                   rendered="#{historicoCartao.cupomEmitido && historicoCartao.situacao != 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"/>
                                <a4j:commandButton id="informacaoCupomCartaoCan"
                                                   title="Este parcela do cartão está cancelada."
                                                   rendered="#{historicoCartao.situacao == 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"/>
                                <rich:spacer width="5px"/>
                            </rich:column>

                            <rich:column rendered="#{EstornoReciboControle.apresentarBotoaoNFSePagamento}">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="NFSe"/>
                                </f:facet>
                                <rich:spacer width="5px"/>

                                <a4j:commandButton id="emitirNFSeCartao"
                                                   rendered="#{!historicoCartao.nfseEmitido && historicoCartao.situacao != 'CA'}"
                                                   actionListener="#{EstornoReciboControle.prepararNFSeCartao}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFSE"
                                                   image="./images/btn_emitir_cupomParcela.png"
                                                   title="Emitir NFSe desta parcela do Cartão."
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFSE');">
                                    <f:attribute name="cartao" value="#{historicoCartao}"/>
                                </a4j:commandButton>

                                <a4j:commandButton id="informacaoNFSeCartao"
                                                   title="Este parcela do cartão já teve cupom emitido."
                                                   rendered="#{historicoCartao.nfseEmitido && historicoCartao.situacao != 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"
                                                   actionListener="#{EstornoReciboControle.prepararNFSeCartao}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFSE"
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFSE');"/>
                                <a4j:commandButton id="informacaoNFSeCartaoCan"
                                                   title="Este parcela do cartão está cancelada."
                                                   rendered="#{historicoCartao.situacao == 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"/>
                                <rich:spacer width="5px"/>
                            </rich:column>

                            <rich:column rendered="#{EstornoReciboControle.apresentarBotoaoNFCePagamento}">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="NFC-e"/>
                                </f:facet>
                                <rich:spacer width="5px"/>

                                <a4j:commandButton id="emitirNFCeCartao"
                                                   rendered="#{!historicoCartao.nfceEmitido && historicoCartao.situacao != 'CA'}"
                                                   actionListener="#{EstornoReciboControle.prepararNFCeCartao}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFCE"
                                                   image="./images/btn_emitir_cupomParcela.png"
                                                   title="Emitir NFC-e desta parcela do Cartão."
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFCE');">
                                    <f:attribute name="cartao" value="#{historicoCartao}"/>
                                </a4j:commandButton>

                                <a4j:commandButton id="informacaoNFCeCartao"
                                                   title="Este parcela do cartão já teve cupom emitido."
                                                   rendered="#{historicoCartao.nfceEmitido && historicoCartao.situacao != 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"
                                                   actionListener="#{EstornoReciboControle.prepararNFCeCartao}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, formConfirmacaoNFCE"
                                                   oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFCE');"/>
                                <a4j:commandButton id="informacaoNFCeCartaoCan"
                                                   title="Este parcela do cartão está cancelada."
                                                   rendered="#{historicoCartao.situacao == 'CA'}"
                                                   image="./images/btn_cupomEmitido.png"/>
                                <rich:spacer width="5px"/>
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>

                    <rich:spacer height="20px"/>
                    <h:panelGrid columns="1" columnClasses="colunaEsquerda"
                                 width="100%">

                        <rich:dataTable id="movParcela" width="100%" border="0"
                                        cellspacing="0" cellpadding="0" styleClass="textsmall"
                                        columnClasses="centralizado, centralizado, centralizado"
                                        value="#{EstornoReciboControle.estornoReciboVO.listaMovParcela}"
                                        var="historicoParcela">
                            <f:facet name="header">
                                <h:outputText styleClass="titulocampos"
                                              value="Parcelas Pagas pelo Recibo"/>
                            </f:facet>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_codigo}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoParcela.contrato.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Código"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoParcela.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_nomeAluno}"/>
                                </f:facet>
                                <h:panelGroup
                                        rendered="#{EstornoReciboControle.desenharColunaNomeContrato}">
                                    <h:outputText style="font-weight: bold" styleClass="red"
                                                  value="#{historicoParcela.contrato.pessoa.nome}"/>
                                </h:panelGroup>
                                <h:panelGroup
                                        rendered="#{EstornoReciboControle.desenharColunaNomeVendaAvulsa}">
                                    <h:outputText style="font-weight: bold" styleClass="red"
                                                  value="#{historicoParcela.vendaAvulsaVO.nomeComprador}"/>
                                </h:panelGroup>
                                <h:panelGroup
                                        rendered="#{EstornoReciboControle.desenharColunaNomeAulaAvusa}">
                                    <h:outputText style="font-weight: bold" styleClass="red"
                                                  value="#{historicoParcela.aulaAvulsaDiariaVO.nomeComprador}"/>
                                </h:panelGroup>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoParcela.descricao}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_modalidade}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoParcela.modalidade}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoParcela.dataVencimento_Apresentar}">
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoParcela.valorParcela}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="red"
                                              value="#{historicoParcela.situacao_Apresentar}"/>
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%"
                                 rendered="#{!empty EstornoReciboControle.estornoReciboVO.listaTransacoes}"
                                 styleClass="tabFormSubordinada,tituloCamposNegritoMaior"
                                 columnClasses="colunaCentralizada">
                        <%@include file="includes/transacoes/include_table_transacoes.jsp" %>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" id="panelSelectEstornarTransacoes"
                                 rendered="#{!empty EstornoReciboControle.estornoReciboVO.listaTransacoes}">
                        <h:panelGroup layout="block" style="padding: 10px 0;display: flex;">
                            <h:outputText style="padding-top: 15px; padding-right: 5px;"
                                          styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"
                                          value="CANCELAR TRANSAÇÕES"/>
                            <h:panelGroup layout="block" styleClass="cb-container pl20">
                                <h:selectOneMenu id="estornarTransacoes" styleClass="form" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 value="#{EstornoReciboControle.estornarTransacoes}">
                                    <f:selectItems
                                            value="#{EstornoReciboControle.listaSelectItemEstornarTransacoes}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%"
                                 rendered="#{!empty GestaoRemessasControle.itensRemessaEstorno}"
                                 styleClass="tabFormSubordinada,tituloCamposNegritoMaior"
                                 columnClasses="colunaCentralizada">
                        <%@include file="includes/remessas/include_itens_remessaestorno.jsp" %>
                    </h:panelGrid>

                    <div style="text-align: center;">
                        <h:panelGrid id="panelMensagem" columns="3" width="100%" columnClasses="colunaEsquerda"
                                     styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText value=" "/>
                            </h:panelGrid>
                            <h:commandButton rendered="#{EstornoReciboControle.sucesso}" image="./imagens/sucesso.png"/>
                            <h:commandButton rendered="#{EstornoReciboControle.erro}" image="./imagens/erro.png"/>
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem" value="#{EstornoReciboControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada"
                                              value="#{EstornoReciboControle.mensagemDetalhada}"/>
                            </h:panelGrid>

                        </h:panelGrid>

                        <h:panelGrid columns="3" width="100%">
                            <rich:spacer width="40px"/>
                            <h:panelGroup>
                                <h:outputText styleClass="mensagemDetalhada"
                                              rendered="#{EstornoReciboControle.temChequeComLote}"
                                              value="#{msg.msg_recibo_nao_pode_estornar}">
                                </h:outputText>
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}como-retirar-recebimentos-dos-lotes-no-financeiro-para-conseguir-estornar-o-recibo/"
                                              rendered="#{EstornoReciboControle.temChequeComLote}"
                                              title="Clique e saiba mais: RecebivelLote"
                                              target="_blank">
                                    <i class="fa-icon-question-sign" style="font-size: 18px; color: #0090FF"></i>
                                </h:outputLink>

                            </h:panelGroup>
                        </h:panelGrid>


                        <pre id="resposta" style="right: 1.5em; position: absolute; margin-right: 20px;"></pre>
                        <h:panelGrid columns="5">
                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <h:commandLink value="#{msg_bt.btn_voltar_lista}"
                                               action="#{EstornoReciboControle.consultar}"
                                               styleClass="botoes nvoBt btSec"/>
                                <a4j:commandLink
                                        rendered="#{EstornoReciboControle.apresentarBotaoEstorno && !EstornoReciboControle.temChequeComLote && !EstornoReciboControle.estornoReciboVO.mostrarMsgExcluirNFse}"
                                        oncomplete="#{EstornoReciboControle.msgAlert}#{EstornoReciboControle.mensagemNotificar}"
                                        action="#{EstornoReciboControle.validarCaixaAbrirModal}"
                                        id="btnEstorno"
                                        reRender="panelAutorizacaoFuncionalidade,formConfirmacaoEstornar,form,modalAbrirCaixa,mdlMensagemGenerica"
                                        styleClass="botoes nvoBt" value="Estornar"/>

                                <a4j:commandButton value="Estornar mesmo assim"
                                       rendered="#{EstornoReciboControle.estornoReciboVO.mostrarMsgExcluirNFse}"
                                       action="#{EstornoReciboControle.confirmarEstornoReciboComNFSe}"
                                       oncomplete="#{EstornoReciboControle.msgAlert}#{EstornoReciboControle.mensagemNotificar}"
                                       reRender="form,panelConfirmacao,msg,panelMensagem, mdlMensagemGenerica, panelAutorizacaoFuncionalidade"
                                       styleClass="botoes nvoBt"/>

                                <a4j:commandLink
                                        action="#{EstornoReciboControle.validarCaixaAbrirModalCappta}"
                                        rendered="#{EstornoReciboControle.apresentarBotaoEstorno and EstornoReciboControle.estornoCappta}"
                                        reRender="form, dadosEstornoCappta"
                                        id="btnEstornoCappta"
                                        oncomplete="#{EstornoReciboControle.msgAlert}#{EstornoReciboControle.mensagemNotificar}"
                                        styleClass="botoes nvoBt">
                                    <h:outputText value="#{msg_aplic.prt_estorno_cappta}"/>
                                </a4j:commandLink>

                                <a4j:commandLink
                                        id="btnEstornoGetcard"
                                        action="#{EstornoReciboControle.validarCaixaAbrirModalGetcard}"
                                        rendered="#{EstornoReciboControle.apresentarBotaoEstorno and EstornoReciboControle.estornoGetcard}"
                                        reRender="form, formModalEstornoGetcard, form:panelGeralEstornoGetcard"
                                        oncomplete="#{EstornoReciboControle.msgAlert}#{EstornoReciboControle.mensagemNotificar}"
                                        styleClass="botoes nvoBt">
                                    <h:outputText value="Estorno GetCard (Scope)"/>
                                </a4j:commandLink>

                                <a4j:commandButton
                                        rendered="#{EstornoReciboControle.apresentarBotaoEmissao}"
                                        image="./images/btn_emitir_cupom.png"
                                        oncomplete="Richfaces.showModalPanel('panelConfirmacaoEmitir')"
                                        value="Emitir Cupom"/>

                                <a4j:commandButton
                                        rendered="#{EstornoReciboControle.estornoReciboVO.reciboPagamentoVO.empresa.apresentarBotoesFaturamentoNFSe}"
                                        oncomplete="Richfaces.showModalPanel('panelConfirmacaoNFSE')"
                                        value="Enviar NFSe" reRender="formConfirmacaoNFSE"
                                        image="./images/btn_emitir_cupom-01.png"
                                        onclick="#{EstornoReciboControle.onclickNFSe}"
                                        action="#{EstornoReciboControle.abrirModalNFSe}"/>


                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <h:commandButton value="#{msg_bt.btn_voltar_lista}"
                                                 action="#{EstornoReciboControle.consultar}"
                                                 image="./imagens/botoesCE/voltar_sem_fundo.png"/>

                                <a4j:commandButton
                                        rendered="#{EstornoReciboControle.apresentarBotaoEstorno && !EstornoReciboControle.temChequeComLote}"
                                        oncomplete="#{EstornoReciboControle.msgAlert}"
                                        action="#{EstornoReciboControle.validarCaixaAbrirModal}"
                                        reRender="modalAbrirCaixa"
                                        image="./imagens/botoesCE/estorno.png"/>
                            </c:if>
                        </h:panelGrid>

                        <a4j:jsFunction name="concluirPinpad"
                                        action="#{EstornoReciboControle.gravar}"
                                        oncomplete="#{EstornoReciboControle.fecharRichModalPanelConfirmacao}"
                                        reRender="form,panelConfirmacao,msg,panelMensagem, formConfirmacaoEstornar"/>

                        <a4j:jsFunction name="erroPagamentoPinpad"
                                        action="#{EstornoReciboControle.erroPagamentoPinpad}">
                            <a4j:actionparam name="param1"
                                             assignTo="#{EstornoReciboControle.pinpad.respostaRequisicao}"/>
                        </a4j:jsFunction>

                        <a4j:jsFunction name="gravarLogGetCard"
                                        status="false"
                                        action="#{EstornoReciboControle.gravarLogGetCard}">
                            <a4j:actionparam name="param1" assignTo="#{EstornoReciboControle.pinPadPedidoVO.retornoPinpadOperacao}"/>
                            <a4j:actionparam name="param2" assignTo="#{EstornoReciboControle.pinPadPedidoVO.retornoPinpad}"/>
                        </a4j:jsFunction>

                        <a4j:jsFunction name="erroGetCard"
                                        action="#{EstornoReciboControle.erroGetCard}"
                                        oncomplete="#{EstornoReciboControle.onCompletePinpad}"
                                        reRender="formConfirmacaoEstornar">
                            <a4j:actionparam name="param1" assignTo="#{EstornoReciboControle.pinPadPedidoVO.paramsRespCancel}"/>
                        </a4j:jsFunction>

                        <a4j:jsFunction name="salvarIdExternoGetCard"
                                        action="#{EstornoReciboControle.salvarIdExternoGetCard}"
                                        reRender="form:panelGeralEstornoGetcard">
                            <a4j:actionparam name="param1" assignTo="#{EstornoReciboControle.pinPadPedidoVO.idExternoCancel}"/>
                        </a4j:jsFunction>

                        <a4j:jsFunction name="exibirBotaoAbortarEstornoFalse"
                                        action="#{EstornoReciboControle.exibirBotaoAbortarEstornoFalse}"
                                        reRender="formModalEstornoGetcard">
                        </a4j:jsFunction>

                        <a4j:jsFunction name="exibirBotaoAbortarEstornoTrue"
                                        action="#{EstornoReciboControle.exibirBotaoAbortarEstornoTrue}"
                                        reRender="formModalEstornoGetcard">
                        </a4j:jsFunction>

                        <a4j:jsFunction name="concluirEstornoPinpadGetCard"
                                        action="#{EstornoReciboControle.gravarPinpadGetcard}"
                                        oncomplete="Richfaces.hideModalPanel('modalEstornoGetcard');Richfaces.hideModalPanel('panelConfirmacaoEstornar');fireElementFromParent('form:btnAtualizaCliente');Notifier.cleanAll();"
                                        reRender="form,panelConfirmacao,msg,panelMensagem, formConfirmacaoEstornar">
                            <a4j:actionparam name="param1" assignTo="#{EstornoReciboControle.pinPadPedidoVO.paramsRespCancel}"/>
                        </a4j:jsFunction>

                        <c:if test="${EstornoReciboControle.checkoutPinPad}">

                            <style>
                                #cappta-checkout-iframe {
                                    z-index: 999999;
                                    display: none;
                                }

                                .BrandCappta {
                                    display: none !important;
                                }

                            </style>

                            <!-- https://linxpaykitapi.linx.com.br/LinxPaykitApi/paykit-checkout.js -->
                            <!-- A lib abaixo é referente ao endereço acima e foi adiciona local no dia 09/12/2024. -->
                            <!-- Tendo uma nova versão, precisa criar ou trocar o arquilo local. -->
                            <script type="text/javascript" src="script/linx-checkout.js"></script>
                            <script>
                                var authenticationRequest = {
                                    authenticationKey: '${EstornoReciboControle.authenticationKeyCappta}'
                                };
                                var onAuthenticationSuccess = function (response) {
                                    document.getElementById('resposta').innerHTML = 'Pinpad Autenticado com sucesso' + '<br>' + 'Checkout GUID: ' + response.merchantCheckoutGuid;
                                };
                                var onAuthenticationError = function (error) {
                                    document.getElementById('resposta').innerHTML = 'Código: ' + error.reasonCode + '<br>' + error.reason;
                                };
                                var onPendingPayments = function (response) {
                                    console.log(response);
                                };
                                var checkout;

                                function fazerCheckout() {
                                    checkout = CapptaCheckout.authenticate(authenticationRequest, onAuthenticationSuccess, onAuthenticationError, onPendingPayments);
                                }

                                function paymentReversal() {
                                    jQuery('#cappta-checkout-iframe').show();
                                    var paymentReversalRequest = {
                                        administrativePassword: document.getElementById('form:senhaAdm').value,
                                        administrativeCode: document.getElementById('form:nrControle').value
                                    };

                                    CapptaCheckout.paymentReversal(paymentReversalRequest, onPaymentSuccess, onPaymentError);
                                }

                                var onPaymentSuccess = function (response) {
                                    console.log(response);
                                    jQuery('#cappta-checkout-iframe').hide();
                                    concluirPinpad();
                                };


                                var onPaymentError = function (error) {
                                    document.getElementById('resposta').innerHTML = 'Código: ' + error.reasonCode + '<br>' + error.reason;
                                    jQuery('#cappta-checkout-iframe').hide();
                                };

                                function matarConexaoWs() {
                                    if (document.getElementById('cappta-checkout-iframe')) {
                                        document.getElementById('cappta-checkout-iframe').remove();
                                    }
                                    document.getElementById('resposta').innerHTML = 'Código: 9' + '<br>' + 'Conexão iniciada.' + '<br>' + 'Escolha um PDV!';
                                    if (ws) {
                                        ws.close();
                                    }
                                }
                            </script>

                            <rich:modalPanel id="painelEstornoCappta" autosized="true"
                                             shadowOpacity="true" width="450" height="250"
                                             styleClass="novaModal"
                                             onshow="document.getElementById('form:senhaAdm').focus()">
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText value="#{msg_aplic.prt_estorno_cappta}"/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="controls">
                                    <h:panelGroup>
                                        <h:outputText
                                                styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                                                id="hidelinkcappta"/>
                                        <rich:componentControl for="painelEstornoCappta"
                                                               attachTo="hidelinkcappta" operation="hide"
                                                               event="onclick"/>
                                    </h:panelGroup>
                                </f:facet>
                                <h:panelGroup id="dadosEstornoCappta" style="font-size: 14px; margin: 10px;">
                                    <h:outputText style="display: block; text-transform: uppercase; margin: 10px 0;"
                                                  value="#{msg_aplic.prt_estorno_cappta_senha_administrativa}:"
                                                  styleClass="texto-bold texto-font texto-cor-cinza"/>
                                    <h:inputSecret id="senhaAdm" style="display: block; width: 75%;"
                                                   styleClass="inputTextClean"/>

                                    <h:outputText style="display: block; text-transform: uppercase; margin: 10px 0;"
                                                  value="#{msg_aplic.prt_estorno_cappta_senha_nr_controle}:"
                                                  styleClass="texto-bold texto-font texto-cor-cinza"/>
                                    <h:inputText id="nrControle"
                                                 value="#{EstornoReciboControle.pinpad.nrControle}"
                                                 style="display: block; width: 75%;"/>


                                    <div style="text-align: center; margin: 10px 0;">
                                        <a4j:commandLink onclick="paymentReversal()" style="display: inline-block;"
                                                         styleClass="botoes nvoBt">
                                            <h:outputText value="#{msg_aplic.prt_estorno_cappta_executar_operacao}"/>
                                        </a4j:commandLink>
                                    </div>


                                </h:panelGroup>


                            </rich:modalPanel>

                        </c:if>

                        <c:if test="${EstornoReciboControle.estornoGetcard}">
                            <h:panelGroup layout="block" id="panelGeralEstornoGetcard">
                                <a4j:poll status="false"
                                          enabled="#{EstornoReciboControle.estornoGetcardConfirmar}"
                                          interval="4000"
                                          oncomplete="consultarGetcard()"/>
                                <%@include file="includes/include_estorno_pinpad.jsp" %>
                            </h:panelGroup>
                        </c:if>
                    </div>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <%@include file="includes/transacoes/include_paramspanel_transacao.jsp" %>
    <%@include file="/pages/finan/includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    window.addEventListener("load", function (event) {
        executePostMessage({loaded: true, message: 'window load'})
    });
    document.getElementById("form:valorConsulta").focus();
</script>
