<jsp:useBean id="LoginControle" scope="session" type="controle.arquitetura.security.LoginControle"/>
<jsp:useBean id="SuperControle" scope="session" type="controle.arquitetura.SuperControle"/>
<script>
    var chatMovOpened = false;
    document.addEventListener('click', (e) => {
        if (chatMovOpened && e.target.parentNode && e.target.parentNode.id !== 'chat-movidesk') {
            movideskChatWidgetChangeWindowState('minimized');
            chatMovOpened = false;
        }
    });



    function abrirChat(){
        fecharPonto();
        if (!chatMovOpened) {
            movideskChatWidgetChangeWindowState('maximized');
            chatMovOpened = true;
        }
    }
    var pontoInterrogacaoMs = '${SuperControle.pontoInterrogacaoMs}';
    let conteudoModal = {id: 99};
    const idConhecimentoBusca = '${SuperControle.idConhecimentoBusca}';
    const cfgPonto = {
        nomeUnidade: '${LoginControle.empresaLogado.nome}',
        empresa: '${LoginControle.empresaLogado.codigo}',
        codEmpresaFinanceiro: '${LoginControle.empresaLogado.codEmpresaFinanceiro}',
        codigoUsuarioZw: '${LoginControle.usuarioLogado.codigo}',
        colaboradorNomeSimples: '${LoginControle.usuarioLogado.nome}',
        userName: '${LoginControle.usuarioLogado.username}',
        key: '${LoginControle.key}',
        zona: '${SuperControle.zona}',
    };

    var sim = false;
    var nao = false;
    var conhecimento;
    var alertas;
    var dicas;
    var filtros;
    var versoes;
    var abaSelecionada = 'aba-alertas';

    function utilizacao(tipo, video, link) {
        try {
            const url = pontoInterrogacaoMs + '/acao';
            let data = {
                username : cfgPonto.colaboradorNomeSimples,
                chave: cfgPonto.key,
                codigoUsuario: cfgPonto.codigoUsuarioZw,
                nomeEmpresa: cfgPonto.nomeUnidade,
                empresa: cfgPonto.empresa,
                conhecimento: conhecimento.cod,
                link: link ? link : null,
                video: video ? video : null,
                tipo
            };
            fetch(url, {
                method: "POST",
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(data)
            }).then(res => {
                console.log("question ok");
            });
        } catch (e) {
            console.log(e);
        }
    }
    function ajuda() {
        utilizacao('CENTRAL_AJUDA', null, null);
        window.open('https://pactosolucoes.com.br/ajuda/', "_blank");
    }

    function verMais() {
        utilizacao('VER_MAIS', null, null);
        if(conhecimento.verMaisLink.startsWith('http')){
            window.open(conhecimento.verMaisLink, "_blank");
        } else {
            window.open('https://pactosolucoes.com.br/ajuda/search/' + conhecimento.verMaisLink, "_blank");
        }

    }

    function chat() {
        if ('${LoginControle.utilizaOctadesk}' === 'true') {
            apresentarOctadeskChat();
        }

        if ('${LoginControle.utilizaChatMovidesk}' === 'true') {
            var elemento = document.getElementById("md-chat-iframe");
            if (elemento) {
                elemento.style.height = "100%";
            }
            utilizacao('CHAT', null, null);
            abrirChat();
            apresentarMovChat();
        }

        if ('${LoginControle.utilizaGymbot}' === 'true') {
            window.open('https://wa.me/556234140314?text=${LoginControle.mensagemSuporte}', "_blank");
        }

    }

    function apresentarOctadeskChat(){
        if (document.querySelector('.octa-widget-v2')) {
            waitForElm('.octa-widget-v2').then(
                element => {
                    element.style.visibility = 'visible';
                    octadesk.chat.showApp();
                }
            )
        }
    }

    function apresentarMovChat(){
        if (document.querySelector('.md-chat-widget-btn-wrapper')) {
            debugger
            waitForElm('.md-chat-widget-btn-wrapper').then(
                element => {
                    element.style.visibility = 'visible';
                }
            )
        }
    }

    async function suporte() {
        utilizacao('SUPORTE', null, null);
        window.open('https://pactosolucoes.com.br/ajuda/', "_blank");

        //C�digo comentado para caso for usar essa funcionalidade novamente
        /* let data = await obterUrlAcessoExterno();
        //if (data && data.urlRedirect) {
        //  utilizacao('SUPORTE', null, null);
        // window.open(data.urlRedirect, "_blank");
        //} else
        utilizacao('SUPORTE', null, null);
        window.open('https://pactosolucoes.movidesk.com/Account/Login?ReturnUrl=%2f', "_blank");
        }*/
    }

    async function obterUrlAcessoExterno() {
        var ehPacto = cfgPonto.userName.toUpperCase() === 'PACTOBR';
        var url =
            'https://app.pactosolucoes.com.br/apoio/prest/api/loginMovidesk?' +
            'username=' + cfgPonto.userName +
            '&idFavorecido=' + cfgPonto.codEmpresaFinanceiro +
            '&ehPacto=' + ehPacto;
        const response =  fetch(url, {
            method: "GET",
            headers: {'Content-Type': 'application/json'}
        });
        const data = (await response).json();
        return data;
    }

    function selecionarLink(link, url) {
        utilizacao('LINK', null, link);
        window.open(url, "_blank");
    }

    function selecionarVideo(video, url) {
        utilizacao('VIDEO', video, null);
        let iframe = '';
        if (url.includes('vimeo')) {
            url = 'https://player.vimeo.com/video/' + getVimeoID(url);
            iframe = '<iframe  onclick="event.stopPropagation()" src="'+url+'" frameborder="0" width="640" height="480" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>';
        } else {
            url = 'https://www.youtube.com/embed/' + getYoutubeID(url);
            iframe = '<iframe  onclick="event.stopPropagation()" width="640" height="480" frameborder="0" src="'+url
                +'" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
        }
        jQuery('#video-selecionado').html(iframe);
        jQuery('#id-baloon-ponto-interrogacao').hide();
    }

    async function chamarConhecimento() {
        const url = pontoInterrogacaoMs + '/pagina?id=' + idConhecimentoBusca
            + '&usuario=${LoginControle.usuarioLogado.username}'
            + '&chave=${LoginControle.key}'
            + '&zona=${SuperControle.zona}';
        const response =  fetch(url, {
            method: "GET",
            headers: {'Content-Type': 'application/json'}
        });
        const data = (await response).json();
        return data;
    }

    async function tips() {
        const url = pontoInterrogacaoMs + '/tooltips-legado';
        try {
            const response =  fetch(url, {
                method: "GET",
                headers: {'Content-Type': 'application/json'}
            });
            const data = (await response).json();

            return data;
        } catch (e) {
            console.error(e);
        }
    }

    async function chamarTotalNotificacao() {
        const url = pontoInterrogacaoMs + '/notificacao/v2/total'
            + '?usuario=${LoginControle.usuarioLogado.codigo}'
            + '&username=${LoginControle.usuarioLogado.username}'
            + '&chave=${LoginControle.key}'
            + '&empresa=${LoginControle.empresaLogado.codigo}'
            + '&zona=${SuperControle.zona}'
            + '&origem=ZW';
        const response =  fetch(url, {
            method: "GET",
            headers: {'Content-Type': 'application/json'}
        });
        return (await response).json();
    }

    async function chamarNotificacao(categoria, tipoNotificacao) {
        const url = pontoInterrogacaoMs + '/notificacao/v2/obter/' + tipoNotificacao
            + '?chave=${LoginControle.key}'
            + '&empresa=${LoginControle.empresaLogado.codigo}'
            + '&usuario=${LoginControle.usuarioLogado.codigo}'
            + '&username=${LoginControle.usuarioLogado.username}'
            + '&zona=${SuperControle.zona}'
            + '&origem=ZW'
            + '&categoria=' + (categoria ? categoria : '');
        const response =  fetch(url, {
            method: "GET",
            headers: {'Content-Type': 'application/json'}
        });
        return (await response).json();
    }

    async function obterHTML(id, url, tipoNotificacao) {
        try {
            iniciarCarregandoNotificacao();
            const urlEnd = pontoInterrogacaoMs + '/notificacao/v2/html'
                + '?chave=${LoginControle.key}'
                + '&empresa=${LoginControle.empresaLogado.codigo}'
                + '&usuario=${LoginControle.usuarioLogado.codigo}'
                + '&username=${LoginControle.usuarioLogado.username}'
                + '&zona=${SuperControle.zona}'
                + '&origem=ZW'
                + '&id=' + id
                + '&url=' + url
                + '&tipo=' + tipoNotificacao;
            const response = await fetch(urlEnd);
            const data = await response.text();
            const dataJson = JSON.parse(data);
            jQuery('#saiba-mais-titulo-html').html(dataJson.content);
            fimCarregandoNotificacao();
        } catch (error) {
            console.error(error);
            jQuery('#saiba-mais-titulo-html').html('');
        }
    }

    async function chamarFiltros(aba) {
        let tipoNotificacao = '';
        if (aba === 'aba-alertas') {
            tipoNotificacao = 'alertas'
        } else if (aba === 'aba-dicas') {
            tipoNotificacao = 'dicas'
        } else if (aba === 'aba-versoes') {
            tipoNotificacao = 'versao'
        }
        const response =  fetch((pontoInterrogacaoMs + '/notificacao/v2/categorias/' + tipoNotificacao), {
            method: "GET",
            headers: {'Content-Type': 'application/json'}
        });
        return (await response).json();
    }

    function registrarAcaoNotificacao(nameAcao, idReferencia) {
        try {
            const url = pontoInterrogacaoMs + '/notificacao/v2/acao';
            let data = {
                usuario : ${LoginControle.usuarioLogado.codigo},
                username : "${LoginControle.usuarioLogado.username}",
                chave: "${LoginControle.key}",
                zona:  "${LoginControle.zona}",
                empresa : ${LoginControle.empresaLogado.codigo},
                acao: ''+ nameAcao + '',
                idRef: ''+ idReferencia + '',
                origem: 'ZW'
            };
            fetch(url, {
                method: "POST",
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(data)
            }).then(res => {
                console.log(res);
            });
        } catch (error) {
            console.error(error);
        }
    }

    function clearConhecimento() {
        jQuery('.artigos').hide();
        jQuery('#videos-conhecimento').empty();
        jQuery('#links-conhecimento').empty();
        jQuery('.vermais').hide();
        jQuery('#video-selecionado').empty();
        document.getElementById("comentario-conhecimento").value = '';
        sim = false;
        nao = false;
        jQuery('.sim_nao').removeClass('desmarcado');
    }

    function selecionarAba(aba) {
        abaSelecionada = aba;
        jQuery('.item-menu-modal-notificacao-versao').removeClass('selecionado');
        jQuery('.conteudo-modal-notificacao-versao-aba').removeClass('aba-open');
        jQuery('.' + aba).addClass('selecionado');
        jQuery('.conteudo-modal-notificacao-versao-' + aba).addClass('aba-open');
        sairSaibaMais();
        initFiltros(aba);
        if (aba === 'aba-alertas') {
            registrarAcaoNotificacao('CLICOU_MENU_ALERTAS', '');
            initAlertas('');
        } else if (aba === 'aba-dicas') {
            registrarAcaoNotificacao('CLICOU_MENU_DICAS', '');
            initDicas('');
        } else if (aba === 'aba-versoes') {
            registrarAcaoNotificacao('CLICOU_MENU_VERSAO', '');
            initVersoes('');
        }
        initTotalNotificacoes(false);
    }

    function selecionarFiltro(id, idFiltro, aba) {
        jQuery('.item-notificacao-filtro').removeClass('selecionado');
        jQuery('#' + id).addClass('selecionado');
        if (aba === 'aba-alertas') {
            initAlertas(idFiltro);
        } else if (aba === 'aba-dicas') {
            initDicas(idFiltro);
        } else if (aba === 'aba-versoes') {
            initVersoes(idFiltro);
        }
        initTotalNotificacoes(false);
    }

    function fecharNotificacoes() {
        jQuery('.modal-notificacoes-versoes').addClass('hidden');
        jQuery('.modal-notificacoes-versoes').removeClass('modal-open');
        jQuery('.topbar-menu-item').removeClass('topbar-item-selected');
    }

    function validarTemModalNaTela(){
        try {
            var conteudosMkt = localStorage.getItem('conteudos-mkt');
            if (conteudosMkt) {
                let conteudos = JSON.parse(conteudosMkt);
                var conteudo = conteudos[idConhecimentoBusca];
                if (conteudo) {
                    jQuery('#nome-modal-reabrir').html(conteudo.tituloModal);
                    jQuery('#verModalDenovo').show();
                } else {
                    jQuery('#nome-modal-reabrir').html('');
                    jQuery('#verModalDenovo').hide();
                }
            }
        } catch (e) {
            console.error(e);
        }
    }

    async function initConhecimento() {
        jQuery('.gaveta-acessos').hide();
        document.getElementsByClassName('modal-mkt')[0].style.setProperty('display', 'none', 'important');
        conhecimento = null;
        validarTemModalNaTela();
        jQuery('#id-baloon-ponto-interrogacao').show();
        let data = await chamarConhecimento();
        if(data.content && data.content.cod){
            jQuery('.artigos').show();
            conhecimento = data.content;
            if(conhecimento.verMaisLink){
                jQuery('.vermais').show();
            }
            validarNovidade();
            populateVideos(conhecimento.videos, 'V�deos');
            populatelinks(conhecimento.linksArtigos, '#title-links-artigos', '#links-conhecimento', 'Artigos relacionados');
            populatelinks(conhecimento.linksCursos, '#title-links-cursos', '#links-conhecimento-cursos', 'Certifica��es & Cursos');
            jQuery('#videos-conhecimento').show();
        }
        reloadTips();
    }

    async function initTotalNotificacoes(ocultar) {
        try {
            if (ocultar) {
                jQuery('#item-superior-qtd-total-notificacoes').hide();
            }
            let data = await chamarTotalNotificacao();
            if (data.content && data.content) {
                jQuery('#item-menu-qtd-itens-alertas').html('(' + data.content.alertas + ')');
                jQuery('#item-menu-qtd-itens-dicas').html('(' +data.content.dicas + ')');
                jQuery('#item-menu-qtd-itens-versao').html('(' +data.content.versao + ')');
                jQuery('#item-superior-qtd-total-notificacoes').html(data.content.total);
                if (data.content.total > 0) {
                    jQuery('#item-superior-qtd-total-notificacoes').show();
                } else {
                    jQuery('#item-superior-qtd-total-notificacoes').hide();
                }
            }
        } catch (e) {
            console.log(e);
        }
    }

    async function initAlertas(categoria) {
        try {
            alertas = null;
            jQuery('#id-notificacoes-versoes').show();
            const idCaixa = '#itens-alertas';
            jQuery(idCaixa).hide();
            iniciarCarregandoNotificacao();
            let data = await chamarNotificacao(categoria, 'alertas');
            if (data.content && data.content) {
                alertas = data.content;
                populateAlertas(alertas, idCaixa);
            }
            fimCarregandoNotificacao();
        } catch (e) {
            console.log(e);
        }
    }

    async function initDicas(categoria) {
        try {
            dicas = null;
            jQuery('#id-notificacoes-versoes').show();
            const idCaixa = '#itens-dicas';
            jQuery(idCaixa).hide();
            iniciarCarregandoNotificacao();
            let data = await chamarNotificacao(categoria, 'dicas');
            if (data.content && data.content) {
                dicas = data.content;
                populateDicas(dicas, idCaixa);
            }
            fimCarregandoNotificacao();
        } catch (e) {
            console.log(e);
        }
    }

    async function initVersoes(modulo) {
        try {
            versoes = null;
            jQuery('#id-notificacoes-versoes').show();
            const idCaixa = '#itens-versoes';
            jQuery(idCaixa).hide();
            iniciarCarregandoNotificacao();
            let data = await chamarNotificacao(modulo, 'versao');
            if (data.content && data.content) {
                versoes = data.content;
                populateVersoes(versoes, idCaixa);
            }
            fimCarregandoNotificacao();
        } catch (e) {
            console.log(e);
        }
    }

    function iniciarCarregandoNotificacao() {
        semItensHide();
        jQuery('.div-notificacao-carregando-geral').show();
    }

    function fimCarregandoNotificacao() {
        jQuery('.div-notificacao-carregando-geral').hide();
    }

    function semItensShow() {
        jQuery('.div-notificacao-itens-empty').show();
    }

    function semItensHide() {
        jQuery('.div-notificacao-itens-empty').hide();
    }

    async function initFiltros(aba) {
        try {
            filtros = null;
            jQuery('#itens-filtro-alertas').hide();
            jQuery('#itens-filtro-dicas').hide();
            jQuery('#itens-filtro-versoes').hide();
            let data = await chamarFiltros(aba);
            if (data.content && data.content) {
                filtros = data.content;
                if (aba === 'aba-alertas') {
                    populateFiltros(filtros, '#itens-filtro-alertas', aba);
                } else if (aba === 'aba-dicas') {
                    populateFiltros(filtros, '#itens-filtro-dicas', aba);
                } else if (aba === 'aba-versoes') {
                    populateFiltros(filtros, '#itens-filtro-versoes', aba);
                }
            }
        } catch (e) {
            console.log(e);
        }
    }

    function validarNovidade() {
        for (var video of conhecimento.videos) {
            video.showGifNovidade = validarShowGifNovidade(video.videoNovidade, video.lancamentoNovidade);
        }
        for (var link of conhecimento.linksArtigos) {
            link.showGifNovidade = validarShowGifNovidade(link.linkNovidade, link.lancamentoNovidade);
        }
        for (var link of conhecimento.linksCursos) {
            link.showGifNovidade = validarShowGifNovidade(link.linkNovidade, link.lancamentoNovidade);
        }
    }

    function validarShowGifNovidade(isNovidade, dataLancamento) {
        if (!isNovidade || dataLancamento === null || dataLancamento === undefined) {
            return false;
        }
        const dataAtual = new Date();
        const dataLancamentoTratada = new Date(dataLancamento);
        const diffEmMillisegundos = dataAtual.getTime() - dataLancamentoTratada.getTime();
        const diffEmDias = diffEmMillisegundos / (1000 * 60 * 60 * 24);
        return diffEmDias <= 15
    }

    function
    populateVideos(videos, title){
        try {
            if(!videos || videos.length === 0){
                return;
            }
            var html = '<div class="container-videos"><span class="videos-conhecimento-title">'+title+'</span>';
            videos.forEach(function(video, i) {
                html += '<div class="video '+ (video.interno ? 'interno' : '') +'" onclick="selecionarVideo(' + video.cod +',\'' + video.url +'\')">';
                html += '<div><img class="thumb-yt" src="'+getThumbnail(video)+'"></div>';
                html += '<div class="sem-margin-lateral">';
                html += '<div><span class="title">'+video.titulo+'</span><span class="subtitle">'+video.subtitulo+'</span></div>';
                if (video.showGifNovidade) {
                    html += '<div><img class="imgGifVideoNovoPontoInterrogacao" alt="GIF"/></div>';
                }
                html += '</div>';
                if(video.interno){
                    html += '<span class="texto-interno">[Interno]</span>';
                }
                html += '</div>';

            });
            html += '</div>';
            jQuery('#videos-conhecimento').html(html);
            var diretorio1 = "../../images/gif_video_novo_sistema.gif";
            var diretorio2 = "images/gif_video_novo_sistema.gif";
            var elementosImagem = document.getElementsByClassName("imgGifVideoNovoPontoInterrogacao");
            verificarImagem(diretorio1, function(existe) {
                if (existe) {
                    definirSrcParaElementos(elementosImagem, diretorio1);
                } else {
                    verificarImagem(diretorio2, function(existe) {
                        if (existe) {
                            definirSrcParaElementos(elementosImagem, diretorio2);
                        }
                    });
                }
            });
        }catch (e){
            console.log(e);
        }
    }

    function definirSrcParaElementos(elementosImagem, src) {
        for (var i = 0; i < elementosImagem.length; i++) {
            elementosImagem[i].src = src;
        }
    }

    function populatelinks(links, idTitle, idCaixa, title){
        try {
            if(!links || links.length === 0){
                return;
            }
            jQuery(idTitle).html('<span>'+title+'</span>');
            var html = '';
            links.forEach(function(link, i) {
                html += '<div style="display: flex;">';
                if (link.showGifNovidade) {
                    html += '<div style="margin-right: 8px;"><img class="imgGifLinkNovoPontoInterrogacao" alt="GIF"/></div>';
                }
                html += '<div class="link-ui '+ (link.interno ? 'interno' : '')
                    +'">'
                    +'<a onclick="selecionarLink('
                    + link.cod +',\''+link.url+'\')" target="_blank">'+link.titulo
                    +'</a>'
                    + (link.interno ? '<span class="texto-interno">[Interno]</span>' : '')
                    +'</div>';
                html += '</div>';
            });

            jQuery(idCaixa).html(html);
            var diretorio1 = "../../images/gif_novo_sistema.gif";
            var diretorio2 = "images/gif_novo_sistema.gif";
            var elementosImagem = document.getElementsByClassName("imgGifLinkNovoPontoInterrogacao");
            verificarImagem(diretorio1, function(existe) {
                if (existe) {
                    definirSrcParaElementos(elementosImagem, diretorio1);
                } else {
                    verificarImagem(diretorio2, function(existe) {
                        if (existe) {
                            definirSrcParaElementos(elementosImagem, diretorio2);
                        }
                    });
                }
            });
        }catch (e){
            console.log(e);
        }
    }

    function populateAlertas(links, idCaixa) {
        try {
            jQuery(idCaixa).hide();
            semItensHide();
            if(!links || links.length === 0){
                semItensShow();
                return;
            }
            var html = '';
            links.forEach(function (link, i) {
                html += ('<div class="item-geral-versao">' +
                    '   <div class="item-geral-versao-icon">' +
                    '         <div class="div-item-novidade" ' + (link.lido ? 'style="background: #ffffff;"' : 'style="background: #1E60FA;"') + '></div>' +
                    '         <img src="imagens_flat/pct-icone-alerta-geral.svg" width="32"/>' +
                    '         </div>' +
                    '     <div class="item-geral-versao-info">' +
                    '         <div class="item-geral-versao-info-titulo">' +
                    '              '+link.titulo+' ' +
                    '         </div>' +
                    '         <div class="item-geral-versao-info-texto">' +
                    '             '+link.texto+' ' +
                    '         </div>' +
                    '         <div class="item-geral-versao-info-data">' +
                    '             <div class="item-geral-versao-info-data-hora">' +
                    '                 '+link.dataCriacao+' ' +
                    '             </div>' +
                    '             <div>' +
                    '             <div class="item-geral-versao-info-data-saiba" ' + (link.link ? '' : 'style="display: none" '  )  +
                    'onclick="clickSaibaMais(\'ALERTAS\',\''+link.titulo+'\',\''+link.id+'\',\''+link.link+'\')">' +
                    '                 Saiba mais ' +
                    '             </div>' +
                    '             </div>' +
                    '         </div>' +
                    '     </div>' +
                    ' </div>');
            });

            jQuery(idCaixa).html(html);
            jQuery(idCaixa).show();
        }catch (e){
            console.log(e);
        }
    }

    function populateDicas(links, idCaixa){
        try {
            jQuery(idCaixa).hide();
            semItensHide();
            if(!links || links.length === 0){
                semItensShow();
                return;
            }
            var html = '';
            links.forEach(function (link, i) {
                html += ('<div class="item-dica"' +
                'onclick="clickSaibaMais(\'DICAS\',\''+link.titulo+'\',\''+link.id+'\',\''+link.link+'\')" >' +
                    (link.lido ? '' : '<div class="item-dica-novidade">Novidade</div>') +
                 '   <div class="item-dica-image"' +
                'style="background-repeat: round; background-image:url(\''+link.image+'\')">' +
                    '<div class="item-dica-image-sombra"></div>' +
                    '</div>' +
                '<div class="item-dica-data">'+link.dataPublicacao+'</div>' +
                '<div class="item-dica-titulo">'+link.titulo+'</div>'+
                '</div>');
            });

            jQuery(idCaixa).html(html);
            jQuery(idCaixa).show();
        }catch (e){
            console.log(e);
        }
    }

    function populateVersoes(links, idCaixa) {
        try {
            jQuery(idCaixa).hide();
            semItensHide();
            if(!links || links.length === 0){
                semItensShow();
                return;
            }
            var html = '';
            links.forEach(function (link, i) {
                html += ('<div class="item-geral-versao">' +
                    '   <div class="item-geral-versao-icon">' +
                    '         <div class="div-item-novidade" ' + (link.lido ? 'style="background: #ffffff;"' : 'style="background: #1E60FA;"') + '></div>' +
                    '         <img src="imagens_flat/pct-icone-versao-'+link.modulo+'.svg" width="32"/>' +
                    '         </div>' +
                    '     <div class="item-geral-versao-info">' +
                    '         <div class="item-geral-versao-info-titulo">' +
                    '              '+link.titulo+' ' +
                    '         </div>' +
                    '         <div class="item-geral-versao-info-texto">' +
                    '             '+link.texto+' ' +
                    '         </div>' +
                    '         <div class="item-geral-versao-info-data">' +
                    '             <div class="item-geral-versao-info-data-hora">' +
                    '                 '+link.dataPublicacao+' ' +
                    '             </div>' +
                    '             <div>' +
                    '             <div class="item-geral-versao-info-data-saiba" ' + (link.link ? '' : 'style="display: none" '  )  +
                    'onclick="clickSaibaMais(\'VERSAO\',\''+link.titulo+'\',\''+link.id+'\',\''+link.link+'\')">' +
                    '                 Saiba mais ' +
                    '             </div>' +
                    '             </div>' +
                    '         </div>' +
                    '     </div>' +
                    ' </div>');
            });

            jQuery(idCaixa).html(html);
            jQuery(idCaixa).show();
        }catch (e){
            console.log(e);
        }
    }

    function populateFiltros(links, idCaixa, aba) {
        try {
            jQuery(idCaixa).hide();

            if(!links || links.length === 0){
                return;
            }

            var html = '<div class="div-notificacao-filtro-geral">';
            html += '<div class="div-notificacao-filtro-icon-left" onclick="scrollLeftFiltro()"><i class="pct pct-chevron-left"></i></div>';
            html += '<div class="div-notificacao-filtro" id="div-notificacao-filtro-'+aba+'">';
            links.forEach(function (link, i) {
                html += ('<div class="item-notificacao-filtro item-notificacao-filtro-'+aba+'"' +
                                'id="item-notificacao-filtro-' + aba + '-' +link.id+ '"' +
                                'onclick="selecionarFiltro(this.id, \''+link.id+'\',\''+ aba + '\')">'
                                    + link.titulo +
                            '</div>');
            });
            html += '</div>';
            html += '<div class="div-notificacao-filtro-icon-right" onclick="scrollRightFiltro()"><i class="pct pct-chevron-right"></i></div>';
            html += '</div>';

            jQuery(idCaixa).html(html);
            jQuery(idCaixa).show();
            document.getElementsByClassName('item-notificacao-filtro-' + aba)[0].addClassName('selecionado')
        }catch (e){
            console.log(e);
        }
    }

    function sairSaibaMais() {
        try {
            jQuery('.div-notificacao-filtro-geral').show();
            document.getElementById("saiba-mais-notificacoes").style.display = 'none';
            document.getElementById("itens-alertas").style.display = 'block';
            document.getElementById("itens-dicas").style.display = 'block';
            document.getElementById("itens-versoes").style.display = 'block';
            initTotalNotificacoes(false);
        } catch (e) {
            console.log(e);
        }
    }

    function scrollLeftFiltro() {
        document.getElementById("div-notificacao-filtro-" + abaSelecionada).scrollLeft -= 200;
    }

    function scrollRightFiltro() {
        document.getElementById("div-notificacao-filtro-" + abaSelecionada).scrollLeft += 200;
    }

    function clickSaibaMais(tipoNotificacao, titulo, id, url) {
        try {
            jQuery('#saiba-mais-titulo-html').html('');
            jQuery('.div-notificacao-filtro-geral').hide();
            document.getElementById("itens-alertas").style.display = 'none';
            document.getElementById("itens-dicas").style.display = 'none';
            document.getElementById("itens-versoes").style.display = 'none';
            document.getElementById("saiba-mais-notificacoes").style.display = 'block';
            jQuery('#saiba-mais-titulo').html(titulo);
            obterHTML(id, url, tipoNotificacao);
        } catch (e) {
            console.log(e);
        }
    }

    function avaliacao() {
        try {
            console.log(cfgPonto);
            if (cfgPonto.key) {
                const url = pontoInterrogacaoMs + '/avaliacao';
                let data = {
                    username : cfgPonto.colaboradorNomeSimples,
                    chave: cfgPonto.key,
                    codigoUsuario: cfgPonto.codigoUsuarioZw,
                    nomeEmpresa: cfgPonto.nomeUnidade,
                    empresa: cfgPonto.empresa,
                    conhecimento: conhecimento ? conhecimento.cod : null,
                    sim: sim,
                    nao: nao,
                    tela: idConhecimentoBusca,
                    comentario: document.getElementById("comentario-conhecimento").value
                };
                fetch(url, {
                    method: "POST",
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(data)
                }).then(res => {
                    try{ Notifier.success(decodeURIComponent('Obrigado pela sua avalia%C3%A7%C3%A3o!'));} catch(e){}
                    desabilitarBotaoEnviar();
                    fecharPonto();
                });
            }
        } catch (e) {
            console.log(e);
        }
    }

    function getThumbnail(video) {
        if (video.url.includes('vimeo')) {
            return video.thumbnail;
        } else {
            return 'https://i.ytimg.com/vi/' + getYoutubeID(video.url) + '/hqdefault.jpg';
        }
    }
    function getYoutubeID(url) {
        try {
            const YoutubeRegexObject_v1 = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{11})/i;
            const YoutubeVideoID = url.match(YoutubeRegexObject_v1);
            return YoutubeVideoID[1];
        } catch (e) {
            return url;
        }
    }
    function getVimeoID(url) {
        try {
            if (url.includes('manage')) {
                const split = url.split('videos/');
                console.log(split);
                let id = split[1];
                id = id.replace('/', '?h=');
                console.log(id);
                return id;
            } else {
                const match = /vimeo.*\/(\d+)/i.exec(url);
                return match[1];
            }
        } catch (e) {
            return url;
        }
    }

    function util() {
        sim = true;
        nao = false;
        jQuery('.sim_nao').removeClass('desmarcado');
        jQuery('#nao-util-conhecimento').addClass('desmarcado');
        habilitarBotaoEnviar();
    }

    function inutil() {
        sim = false;
        nao = true;
        jQuery('.sim_nao').removeClass('desmarcado');
        jQuery('#sim-util-conhecimento').addClass('desmarcado');
        habilitarBotaoEnviar();
    }

    function habilitarBotaoEnviar() {
        document.getElementById('enviar-util').style.pointerEvents = 'initial';
        document.getElementById('enviar-util').style.opacity = '1';
    }

    function desabilitarBotaoEnviar() {
        document.getElementById('enviar-util').style.pointerEvents = 'none';
        document.getElementById('enviar-util').style.opacity = '0.5';
    }

    function hintPi(component){

        if(component.id){
            try {
                const item = JSON.parse(localStorage.getItem('tooltips'));
                const identificador = idConhecimentoBusca + '_' + component.id;
                const tip = item ? item[identificador] : null;
                if(tip){
                    jQuery(component.id).click(function() {
                        verMaisInterrogacao(tipJson.verMais);
                    });
                    hintPiTip(component, tip);
                } else {
                    let tipsContent = tips().then((resultado) => {
                        localStorage.setItem('tooltips', JSON.stringify(resultado.content));
                        hintPiTip(component, resultado.content[identificador]);
                    });
                }

            } catch (e) {
                console.log(e);
            }
        }
    }

    function hintPiTip(component, tipJson){
        var content = '<div class="tooltip-pi tip-direita">' +
            '<span>'+
            (tipJson ? tipJson.conteudo : ('#' + component.id))+
            '</span>' +
            '<div class="verMais">' +
            '<span>Ver mais</span>' +
            '<i class="pct pct-arrow-right"></i>' +
            '</div><div class="tailShadow"></div><div class="tail1"></div><div class="tail2"></div></div>';
        component.innerHTML = content;
    }

    function verMaisInterrogacao(link) {
        alert(link);
        if ( link === '?') {
            document.getElementById('topbar-central-ajuda').querySelector('i').click();
        } else if ( link.startsWith('?')) {
            localStorage.setItem('conhecimento-especifico', link.replace('?', ''));
            document.getElementById('topbar-central-ajuda').querySelector('i').click();
        } else if ( link.startsWith('http')) {
            window.open(link, "_blank");
        } else {
            window.open('https://pactosolucoes.com.br/ajuda/search/' + link, "_blank");
        }
    }

    function closeModalInfoChat() {
        var notification = document.getElementById("notification");
        notification.classList.add("hidden");
    }

    async function reloadTips() {
        tips().then((resultado) => {
            localStorage.setItem('tooltips', JSON.stringify(resultado.content));
        });
    }

    document.addEventListener("DOMContentLoaded", function() {
        document.addEventListener("click", function(event) {
            if (event.target) {
                if (event.target.classList.contains('md-chat-widget-btn-wrapper') ||
                    event.target.classList.contains('md-chat-widget-btn-title') ||
                    event.target.classList.contains('md-chat-widget-btn-icon')) {

                    movideskChatWidgetChangeWindowState('minimized');
                    chatMovOpened = false;

                    var notification = document.getElementById("notification");
                    notification.classList.remove("hidden");
                    notificarClickChat();
                }
            }
        });
        verificarModalMkt();
        initTotalNotificacoes(true);
    });

    function verificarImagem(src, callback) {
        var img = new Image();
        img.onload = function() {
            callback(true);
        };
        img.onerror = function() {
            callback(false);
        };
        img.src = src;
    }

    document.addEventListener('DOMContentLoaded', function() {
        var diretorio1 = "../../images/maxGptOpenAiIcon.png";
        var diretorio2 = "images/maxGptOpenAiIcon.png";
        var elementoImagem = document.getElementById("imagemMaxGptIcon");

        verificarImagem(diretorio1, function(existe) {
            if (existe) {
                elementoImagem.src = diretorio1;
            } else {
                verificarImagem(diretorio2, function(existe) {
                    if (existe) {
                        elementoImagem.src = diretorio2;
                    }
                });
            }
        });
    });

    let isIframeVisible = false;

    function showIframe() {
        const iframeContainer = document.getElementById('iframe-container');
        if (isIframeVisible) {
            iframeContainer.style.display = 'none';
        } else {
            iframeContainer.style.display = 'block';
            fecharPonto();
            utilizacao('MAXGPT', null, null);
        }
        isIframeVisible = !isIframeVisible;
    }

    window.addEventListener('message', event => {
        if (event.data === 'closeIframe') {
            showIframe();
        }
    });


    function fecharModalMkt() {
        const identificador = 'fechar-mkt_' + cfgPonto.userName + '_' + conteudoModal.id;
        document.getElementsByClassName('modal-mkt')[0].style.setProperty('display', 'none', 'important');
        fecharPonto();
        localStorage.setItem(
         	identificador,
        	new Date().getTime().toString());
        acaoModalMkt('FECHAR');
    }

    function acaoVerModalDeNovo() {
        var conteudosMkt = localStorage.getItem('conteudos-mkt');
        if (conteudosMkt) {
            let conteudos = JSON.parse(conteudosMkt);
            var conteudo = conteudos[idConhecimentoBusca];
            if (conteudo) {
                acaoModalMkt('VER_DE_NOVO');
                conteudoModal = conteudo;
                abrirModalMkt();
            }
        }
    }

    function lembrarDepoisModalMkt() {
        document.getElementsByClassName('modal-mkt')[0].style.setProperty('display', 'none', 'important');
        const identificador = 'depois-mkt_' + cfgPonto.userName  + '_' + conteudoModal.id;
        localStorage.setItem(identificador, getTodayDateString());
        fecharPonto();
        acaoModalMkt('LEMBRE_DEPOIS');
    }

    function naoMostreMaisModalMkt() {
        const identificador = 'nao-mostre-mkt_' + cfgPonto.userName + '_' + conteudoModal.id;
        localStorage.setItem(identificador, 'true');
        acaoModalMkt('NAO_VER_MAIS');
        document.getElementsByClassName('modal-mkt')[0].style.setProperty('display', 'none', 'important');
        fecharPonto();
    }

    function getTodayDateString() {
        return getDateString(new Date());
    }

    function getDateString(today) {
        const day = today.getDate();
        const month = today.getMonth() + 1;
        const year = today.getFullYear();
        return year + '-' + month.toString().padStart(2, '0') +'-' + day.toString().padStart(2, '0');
    }

    function acaoModalMkt(acao) {
        try {
            var url = pontoInterrogacaoMs + '/modal-mkt/usuario/' + acao +
                '?username='+cfgPonto.userName+'&chave='+cfgPonto.key+'&idModal=' + conteudoModal.id;
            fetch(url, {
                method: "POST",
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({})
            }).then(res => {
            });
        } catch (e) {
            console.log(e);
        }
    }

    async function modaisMkt() {
        var url =  pontoInterrogacaoMs + '/modal-mkt/modais-user?username='+cfgPonto.userName+
            '&chave=' + cfgPonto.key + '&zona=' + cfgPonto.zona;
        try {
            var response =  fetch(url, {
                method: "GET",
                headers: {'Content-Type': 'application/json'}
            });
            var data = (await response).json();
            return data;
        } catch (e) {
            console.error(e);
        }
    }

    function clicouFecharMenosXHoras(id) {
        var identificador = 'fechar-mkt_' + cfgPonto.userName + '_' + id;
        var dataClickClose = parseInt(localStorage.getItem(identificador), 10);
        if (dataClickClose) {
            var currentDate = new Date().getTime();
            var diffInMilliseconds = currentDate - dataClickClose;
            var diffInHours = diffInMilliseconds / (1000 * 60 * 60);
            return diffInHours < 3;
        }
        return false;
    }

    function clicouLembreDepoisHoje(id) {
        var identificador = 'depois-mkt_' + cfgPonto.userName + '_' + id;
        var dia = localStorage.getItem(identificador);
        if (dia) {
            var today = this.getTodayDateString();
            return dia === today;
        }
        return false;
    }

    function clicouNaoMostreMais(id) {
        var identificador = 'nao-mostre-mkt_' + cfgPonto.userName + '_' + id;
        var naoMostre = localStorage.getItem(identificador);
        if (naoMostre) {
            return 'true' === naoMostre;
        }
        return false;
    }

    function validarConteudoModal(conteudos) {
        try {
            if (conteudos && conteudos[idConhecimentoBusca]) {
                var conteudo = conteudos[idConhecimentoBusca];
                if (clicouFecharMenosXHoras(conteudo.id) || clicouLembreDepoisHoje(conteudo.id)
                    || clicouNaoMostreMais(conteudo.id)) {
                    return;
                }
                if(conteudo.id && conteudo.id > 0){
                    conteudoModal = conteudo;
                    abrirModalMkt();
                }
            }
        } catch (e) {
            console.error(e);
        }
    }

    function verificarModalMkt() {
        try {
            var conteudosMkt = localStorage.getItem('conteudos-mkt');
            if (conteudosMkt && idConhecimentoBusca !== 'tela1.jsp') {
                validarConteudoModal(JSON.parse(conteudosMkt));
            } else {
                modaisMkt().then((resultado) => {
                    localStorage.setItem('conteudos-mkt', JSON.stringify(resultado.content));
                    var conteudos = resultado.content;
                    if(conteudos && conteudos[idConhecimentoBusca]){
                        verificarAcoesUsuario(conteudos[idConhecimentoBusca]);
                    }
                    validarConteudoModal(conteudos);
                });
            }
        } catch (e) {
            console.error(e);
        }
    }

    function verificarAcoesUsuario(conteudoElegivel) {
        if (conteudoElegivel && conteudoElegivel.acoesUsuario) {
            conteudoElegivel.acoesUsuario.forEach(function(acaoUser, i) {
                if (acaoUser.acao === 'FECHAR') {
                    const identificador = 'fechar-mkt_' + cfgPonto.userName + '_' + conteudoElegivel.id;
                    localStorage.setItem(identificador, acaoUser.lancamento.toString());
                } else if (acaoUser.acao === 'LEMBRE_DEPOIS') {
                    const identificador = 'depois-mkt_' + cfgPonto.userName + '_' + conteudoElegivel.id;
                    localStorage.setItem(identificador, getDateString(new Date(acaoUser.lancamento)));
                }
            });
        }
    }

    function substituirNumeros(str) {
        return str.replace(/\/\d+/g, '/##');
    }

    function linkCta() {
        acaoModalMkt('CTA');
        window.open(conteudoModal.linkCTA, "_blank");
    }

    function abrirGavetaAcessos() {
        jQuery('#id-baloon-ponto-interrogacao').hide();
        jQuery('.modal-ponto-interrogacao').addClass('modal-open');
        jQuery('.gaveta-acessos').show();
        listaRapidaAcessos();
    }


    function abrirModalMkt() {
        jQuery('.gaveta-acessos').hide();
        var titleDiv = document.getElementById("titulo-modal-mkt");
        titleDiv.innerText = conteudoModal.tituloModal;
        var textoDiv = document.getElementById("texto-modal-mkt");
        textoDiv.innerText = conteudoModal.textoAuxiliar;
        if (conteudoModal.naoMostreMais === 'false') {
            document.getElementsByClassName('nao_mostre_mais')[0].style.setProperty('display', 'none', 'important');
        }
        if (conteudoModal.linkCTA) {
            var btnCta = document.getElementById("btn-cta");
            btnCta.innerText = conteudoModal.nomeCTA;
            document.getElementsByClassName('btnCta')[0].style.setProperty('display', 'block', 'important');
        } else {
            var spanParaRemover = document.getElementById("linkCTA");
            if (spanParaRemover) {
                spanParaRemover.parentNode.removeChild(spanParaRemover);
            }
        }

        jQuery('.modal-mkt').removeClass('somenteTexto');
        if (conteudoModal.urlYoutube) {
            var url = 'https://www.youtube.com/embed/' + conteudoModal.urlYoutube;
            var iframe = '<iframe  onclick="event.stopPropagation()" width="640" height="480" frameborder="0" src="'+url
                +'" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
            jQuery('#conteudo-modal-mkt').html(iframe);
        } else if (conteudoModal.urlImg) {
            var img = '<img src="'+conteudoModal.urlImg+'" alt="Imagem"/>';
            jQuery('#conteudo-modal-mkt').html(img);
        } else {
            jQuery('#conteudo-modal-mkt').hide();
            jQuery('.modal-mkt').addClass('somenteTexto');
        }

        jQuery('#id-baloon-ponto-interrogacao').hide();
        document.getElementsByClassName('modal-mkt')[0].style.setProperty('display', 'grid', 'important');
        jQuery('.modal-ponto-interrogacao').addClass('modal-open');
    }
</script>


<style>
    .modal-conteudo.ponto-interrogacao-modal{
        width: 100%;
        height: 232px;
        left: 542px;
    }

    .hidden {
        display: none;
    }

    .div-fundo-gif-chat {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background-color: #000;
        opacity: 0.7;
        z-index: 9;
    }

    .modal-content-gif-chat {
        width: 35%;
        position: absolute;
        top: 10%;
        padding: 25px;
        z-index: 10;
        background-color: #fff;
        left: 50%;
        transform: translateX(-50%);
    }

    .modal-content-gif-chat  img {
        width: 90%;
    }

    .div-content-btn-gif-chat {
        display: flex;
        justify-content: center;
        margin-top: 30px;
    }

    .div-btn-gif-chat {
        background: #0380e3;
        border: 1px solid #0380e3;
        padding: 7px 12px;
        cursor: pointer;
        border-radius: 0.25rem;
    }

    .div-btn-gif-chat svg {
        vertical-align: middle;
    }

    .close-modal-chat-interrogacao {
        font-family: 'Nunito Sans',sans-serif;
        color: #fff;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        vertical-align: middle;
    }

    .div-gif-chat {
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .notification .body {
        font-size: 16px;
        margin-top: 20px;
    }

    .chat-max-gpt {
        margin-top: 24px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }

    .image-area-max-gpt {
        width: 40px;
        margin-right: 8px;
    }

    .image-area-max-gpt img {
        width: inherit;
        height: 40px;
    }

    .chat-max-gpt-content {
        width: calc(100% - 40px - 8px);
        display: flex;
        flex-direction: column;
    }

    .chat-max-gpt-content .title {
        font-family: Nunito Sans, sans-serif;
        font-size: 16px;
        font-weight: 600;
        line-height: 18px;
        letter-spacing: 0.25px;
        text-align: start;
        color: #55585E;
        margin-bottom: 4px;
    }

    .chat-max-gpt-content .text span {
        font-family: Nunito Sans, sans-serif;
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0px;
        text-align: start;
        color: #80858C;
    }

    .container-videos {
        background-color: #eff1f7;
        padding: 12px 0 0.1px 0;
    }

    #videos-conhecimento .video {
        margin-top: 14px;
    }

    .fundo-padrao-card {
        background-color: #eff1f7;
        padding: 12px!important;
        margin-top: 24px;
        border-bottom: 0!important;
    }

    .sem-margin-lateral {
        margin-left: 0!important;
    }

    .videos-conhecimento-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 16px;
        margin-left: 12px;
    }

    #title-links-cursos {
        margin-top: 20px;
    }

    .background-modal-mkt {
    }

    .background-modal-mkt .modal-mkt {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: none;
        grid-template-rows: repeat(5, auto);
        gap: 5px;
        padding: 16px;
        background-color: #ffffff;
        background-clip: padding-box;
        border-radius: 8px;
        outline: 0;
        height: 538px;
        width: 968px;
        max-width: 90vw;
        overflow: hidden;
    }

    .background-modal-mkt .modal-mkt.somenteTexto {
        grid-template-rows: auto auto 430px auto;
        align-items: start;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-texto {
        max-height: 437px;
    }

    .background-modal-mkt .modal-mkt div {
        overflow: hidden;
        height: 28px;
        text-align: center;
    }

    .background-modal-mkt .modal-mkt div iframe {
        height: 100%;
        width: 56%;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-titulo {
        color: #55585E;
        font-size: 22px;
        font-style: normal;
        font-weight: 600;
        line-height: 125%;
        letter-spacing: 0.25px;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-texto {
        height: auto;
        color: #797D86;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-midia {
        height: auto;
        min-height: 299px;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-midia img {
        width: 100%;
        height: auto;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-footer {
        height: auto;
        display: flex;
        align-items: end;
        justify-content: space-between;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-footer  .column-footer a.button-zw-ui{
        background-color: #0380e3;
        border: 1px solid #1998fc;
        line-height: 40px;
        padding: 0 10px;
        margin-top: 0;
        font-size: 16px;
        font-weight: 600;
        text-decoration: none;
        color: #FFFFFF;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-footer .column-footer {
        flex: 1;
        text-align: left;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-footer .column-footer a {
        margin-top: 10px;
        display: inline-block;
        text-decoration: none;
        padding: 10px 8px;
        justify-content: center;
        color: #FA1E1E;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: 100%;
        letter-spacing: 0.25px;
        cursor: pointer;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-footer .column-footer a.lembreme {
        color: #1E60FA;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-footer .column-footer:first-child {
        text-align: right;
        order: 2;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-header {
        width: 100%;
        text-align: right;
    }

    .background-modal-mkt .modal-mkt .modal-mkt-header .close-wrapper {
        font-size: 20px;
        color: #51555a;
        cursor: pointer;
    }

    .container-videos.ver-modal-de-novo{
        cursor: pointer;
        color: #1e60fa;
        font-size: 14px;
        font-weight: 600;
        line-height: 16px;
        padding: 8px 12px;
        margin-bottom: 10px;
    }

    .modal-notificacao-versao-modal{
        width: 420px;
        left: 542px;
        font-family: Poppins, sans-serif;
    }

    .header-modal-notificacao-versao {
        font-family: Poppins, sans-serif;
        width: calc(100% - 30px);
        height: 30px;
        display: grid;
        align-items: center;
        grid-template-columns: 3fr 1fr;
        background: #FAFAFA;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        padding: 15px 15px 0 15px;
        color: #55585E;
        font-size: 14px;
        font-weight: 600;
    }

    .container-modal-notificacao-versao {
        background: #FFFFFF;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    .container-modal-notificacao-versao-menu {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1.5fr;
        justify-content: center;
        text-align: center;
        border-bottom: 1px solid #c9cbcf;

        .selecionado {
            border-bottom: 2px solid #1E60FA;
            color: #55585E;
        }

    }

    .container-modal-notificacao-versao-menu-conteudo{
        /*overflow-x: auto;*/
        /*max-height: 500px;*/

        .conteudo-modal-notificacao-versao-aba {
            display: none;
        }

        .aba-open {
            display: block;
        }
    }

    .div-itens-notificacoes {
        padding: 0 15px 0 15px;
        overflow-x: auto;
        max-height: 500px;
    }

    .item-geral-versao {
        display: grid;
        grid-template-columns: 1fr 6fr;
        align-items: center;
        padding-bottom: 20px;

        .item-geral-versao-icon {
            text-align: center;
            padding-right: 15px;
            display: flex;
            align-items: center;
        }
    }

    .item-geral-versao-info{
        display: grid;
        grid-template-columns: 1fr;

        .item-geral-versao-info-titulo{
            font-size: 14px;
            font-weight: 600;
            color: #55585E
        }

        .item-geral-versao-info-texto{
            font-size: 14px;
            font-weight: 400;
            color: #494B50;
            padding-top: 10px;
            font-family: "Nunito Sans", sans-serif;
        }

        .item-geral-versao-info-data {
            display: grid;
            grid-template-columns: 1fr 1fr;
            padding-top: 10px;
            align-items: center;

            .item-geral-versao-info-data-hora {
                color: #AFB1B6;
                font-size: 14px;
                font-weight: 400;
            }

            .item-geral-versao-info-data-saiba {
                font-family: Poppins, sans-serif;
                color: #1E60FA;
                font-size: 12px;
                font-weight: 600;
                cursor: pointer;
                width: 70px;
                padding: 5px;
                border-radius: 5px;
                float: right;
                text-align: center;
            }

            .item-geral-versao-info-data-saiba:hover {
                background: #B4CAFD;
            }
        }
    }

    .div-item-novidade {
        width: 8px;
        height: 8px;
        border-radius: 100%;
        margin-right: 10px;
    }

    .item-menu-modal-notificacao-versao {
        font-family: Poppins, sans-serif;
        font-size: 12px;
        font-weight: 600;
        padding: 5px 10px 5px 10px;
        cursor: pointer;
        color: #797D86;
        display: flex;
        gap: 3px;
    }

    .item-menu-modal-notificacao-versao:hover {
        border-bottom: 2px solid #B4CAFD;
    }

    .div-saiba-mais{
        display: grid;
        padding: 0 15px 0 15px;
    }

    .div-notificacao-itens-empty{
        display: block;
        text-align: center;
        grid-template-columns: 1fr;
        padding: 45px;
    }

    .div-notificacao-itens-empty-texto {
        font-size: 12px;
        color: #797D86;
        font-weight: 600;
    }

    .div-notificacao-carregando-geral{
        display: block;
        text-align: center;
        grid-template-columns: 1fr;
        padding: 20px;
    }

    .div-notificacao-carregando-icon {
    }

    .div-notificacao-carregando-texto {
        font-size: 12px;
        color: #797D86;
        font-weight: 600;
    }

    .div-saiba-mais-titulo{
        display: flex;
        align-items: center;
        padding-bottom: 10px;

        .saiba-mais-icon {
            cursor: pointer;
            font-size: 20px;
            padding: 5px;
            color: #1E60FA;
            border-radius: 5px;
            margin-right: 10px;
        }

        .saiba-mais-icon:hover {
            background: #B4CAFD;
        }

        .saiba-mais-titulo{
            font-size: 14px;
            font-weight: 600;
            color: #55585E;
        }
    }

    .saiba-mais-titulo-html {
        padding: 0 15px 15px 15px;
        max-height: 500px;
        overflow: auto;
    }

    .item-dica-novidade {
        position: absolute;
        top: 10px;
        background: #B4CAFD;
        z-index: 3;
        border-radius: 10px;
        padding: 2px 10px;
        left: 10px;
        color: #03277D;
        font-size: 12px;
        font-weight: 400;
    }

    .item-dica-image{
        background-repeat: round;
        height: 185px;
        border-radius: 8px;
        position: relative;
        z-index: 1;
    }

    .item-dica-image-sombra {
        background: rgba(0, 0, 0, 0.7);
        z-index: 2;
        height: 184px;
        border-radius: 8px;
        position: relative;
    }

    .item-dica {
        height: 185px;
        border-radius: 8px;
        cursor: pointer;
        margin-bottom: 20px;
        position: relative;
    }

    .item-dica-data {
        padding-left: 10px;
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        text-align: left;
        color: #FFFFFF;
        top: 75%;
        position: absolute;
        z-index: 3;
    }

    .item-dica-titulo{
        padding-left: 10px;
        font-size: 14px;
        font-weight: 600;
        text-align: left;
        color: #FFFFFF;
        position: absolute;
        top: 85%;
        width: 360px;
        text-overflow: ellipsis;
        overflow: hidden;
        height: 20px;
        white-space: nowrap;
        z-index: 3;
    }

    .div-notificacao-filtro {
        display: flex;
        font-size: 12px;
        font-weight: 600;
        align-items: center;
        width: 385px;
        overflow: hidden;

        .selecionado {
            background: #B4CAFD;
        }
    }

    .div-notificacao-filtro-geral{
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        background: #FAFAFA;
        font-family: Poppins, sans-serif;
    }

    .div-notificacao-filtro-icon-left{
        cursor: pointer;
        color: #A1A5AA;
        height: 45px;
        align-items: center;
        display: flex;
        font-size: 20px;
        padding: 0 10px;
        color: #1E60FA;
    }

    .div-notificacao-filtro-icon-right{
        cursor: pointer;
        color: #A1A5AA;
        height: 45px;
        align-items: center;
        display: flex;
        font-size: 20px;
        padding: 0 10px;
        color: #1E60FA;
    }

    .item-notificacao-filtro {
        background: #FAFAFA;
        color: #1E60FA;
        padding: 5px;
        border-radius: 5px;
        text-align: center;
        cursor: pointer;
        margin-right: 10px;
        white-space: nowrap;
    }

    .item-notificacao-filtro:hover {
        background: #B4CAFD;
    }

</style>
<div  class="modal-ponto-interrogacao background-modal-mkt  balloon-modal"  onclick="fecharPonto()">
    <div id="video-selecionado" onclick="event.stopPropagation()"></div>
    <div id="id-baloon-ponto-interrogacao" class="modal-dialog" >
        <div  class="modal-content-usuario-zw-ui"  onclick="event.stopPropagation()">
            <div>
                <div class="pacto-modal-wrapper">
                    <div class="modal-conteudo ponto-interrogacao-modal"><!---->
                        <div>

                            <div class="blue-container">
                                <span>Ajuda</span>
                                <i class="pct pct-x" onclick="fecharPonto()"></i>
                            </div>
                            <div class="container-zw-ui ponto-interrogacao">
                                <textarea class="fake-input"></textarea>
                                <!---->


                                <div id="verModalDenovo" onclick="acaoVerModalDeNovo()" class="container-videos ver-modal-de-novo">
                                    Visualizar "<span id="nome-modal-reabrir"></span>"
                                </div>

                                <div id="videos-conhecimento" style="display: none">

                                </div>

                                <div class="aprendizado artigos fundo-padrao-card">
                                    <div class="title" id="title-links-artigos"></div>
                                    <div id="links-conhecimento"></div>

                                    <div class="title" id="title-links-cursos"></div>
                                    <div id="links-conhecimento-cursos"></div>

                                    <div onclick="verMais()" class="link-ui vermais" style="display: none"><span class="ver-mais">Ver mais</span></div>
                                </div>
                                <div class="aprendizado fundo-padrao-card precisa-ajuda">
                                    <div class="title"><span>Ainda precisa de ajuda?</span></div>
                                    <div onclick="ajuda()" class="link-ajuda icone-pi"><i class="pct pct-book"></i><a>Encontre respostas na Central de Ajuda</a></div>
                                    <div onclick="chat()" class="link-ajuda icone-pi" id="chat-movidesk"><i class="pct pct-message-square"></i><a>Fale com um atendente via Chat</a></div>
                                    <div onclick="suporte()" class="link-ajuda icone-pi"><i class="pct pct-telemarketing"></i><a>Abra um ticket de suporte</a></div>
                                </div>
                                <!----><!---->
                                <div class="chat-max-gpt fundo-padrao-card">
                                    <div class="image-area-max-gpt">
                                        <img id="imagemMaxGptIcon" alt="">
                                    </div>
                                    <div class="chat-max-gpt-content">
                                        <div class="title">Precisa de ajuda?</div>
                                        <div class="text">
                                            <span>
                                                Sou o Max GPT um assistente virtual alimentado pela nossa central de conhecimento.
                                                Meu objetivo � fornecer informa��es �teis, responder a perguntas e ajud�-lo(a) no que for necess�rio.
                                            </span>
                                        </div>
                                    </div>
                                    <div class="div-content-btn-gif-chat" style="width: 100%; margin-top: 14px;">
                                        <div class="div-btn-gif-chat" style="width: 100%; text-align: center;" onclick="showIframe()">
                                            <svg class="icon-openai" xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
                                                <g clip-path="url(#clip0_394_839)">
                                                    <path d="M15.3164 6.55655C15.6766 5.47601 15.5529 4.29333 14.9777 3.30955C14.1122 1.8043 12.3704 1.03017 10.6717 1.39036C10.2968 0.970274 9.83696 0.634653 9.32259 0.405738C8.80822 0.176822 8.25107 0.0598408 7.68806 0.0625459C5.95169 0.0625459 4.40881 1.1807 3.87122 2.83108C2.75306 3.06223 1.79078 3.76108 1.22631 4.75026C0.355437 6.25548 0.554343 8.14777 1.72087 9.43798C1.36072 10.5185 1.48434 11.7012 2.05956 12.6796C2.92506 14.1903 4.66684 14.9644 6.371 14.6042C7.12362 15.4535 8.20953 15.9428 9.34922 15.9374C11.0856 15.9374 12.6285 14.8192 13.1661 13.1688C14.2843 12.9377 15.2465 12.2388 15.8056 11.2497C16.6818 9.74442 16.483 7.85211 15.3164 6.56192V6.55655ZM14.0799 3.83098C14.424 4.43308 14.553 5.13733 14.4347 5.82005C14.4132 5.80392 14.3702 5.78242 14.3434 5.7663L11.1824 3.93848C11.1028 3.89355 11.013 3.86994 10.9216 3.86994C10.8303 3.86994 10.7405 3.89355 10.6609 3.93848L6.95697 6.07811V4.50836L10.0158 2.7397C11.4404 1.9172 13.2574 2.40639 14.0799 3.83098ZM6.95697 7.09411L8.51597 6.19098L10.0749 7.09411V8.89505L8.51597 9.79817L6.95697 8.89505V7.09411ZM7.68269 1.10005C8.38156 1.10005 9.05353 1.34198 9.59112 1.78817C9.56962 1.79892 9.52662 1.8258 9.49434 1.84192L6.33337 3.66436C6.17209 3.75576 6.07531 3.9278 6.07531 4.11592V8.39511L4.71522 7.61023V4.07289C4.71522 2.42789 6.04306 1.10008 7.68806 1.0947L7.68269 1.10005ZM2.12944 5.27176C2.47887 4.66964 3.02184 4.20733 3.67769 3.96542V7.72311C3.67769 7.91123 3.77447 8.07792 3.93575 8.17467L7.63431 10.3089L6.26884 11.0991L3.21537 9.33586C1.79616 8.51336 1.30694 6.69633 2.12944 5.27173V5.27176ZM2.96272 12.169C2.61328 11.5722 2.48962 10.8626 2.60791 10.1799C2.62941 10.196 2.67241 10.2175 2.69928 10.2336L5.86028 12.0615C5.93983 12.1064 6.02965 12.13 6.12102 12.13C6.21238 12.13 6.3022 12.1064 6.38175 12.0615L10.0803 9.92186V11.4916L7.02147 13.2549C5.59687 14.072 3.77984 13.5882 2.95734 12.169H2.96272ZM9.35459 14.8999C8.66109 14.8999 7.98375 14.658 7.45153 14.2118C7.48429 14.1948 7.51657 14.1768 7.54831 14.158L10.7093 12.3356C10.8706 12.2442 10.9727 12.0722 10.9673 11.884V7.6102L12.3274 8.39508V11.927C12.3274 13.572 10.9942 14.9052 9.35459 14.9052V14.8999V14.8999ZM14.9132 10.7282C14.5637 11.3303 14.0154 11.7926 13.3649 12.0291V8.27145C13.3649 8.0833 13.2682 7.91126 13.1069 7.81989L9.40297 5.6803L10.7631 4.89542L13.8219 6.65867C15.2465 7.48117 15.7303 9.29823 14.9078 10.7228L14.9132 10.7282H14.9132Z" fill="#EFF1F7"/>
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_394_839">
                                                        <rect width="16" height="16" fill="white" transform="translate(0.5)"/>
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            <span class="close-modal-chat-interrogacao">
                                                Iniciar conversa
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="aprendizado util">
                                    <div class="title"><span>Isso foi �til pra voc�?</span></div>

                                    <div class="title">
                                        <span id="sim-util-conhecimento" class="sim_nao" onclick="util()">
                                            <span>Sim</span>
                                            <i class="pct pct-thumbs-up"></i>
                                        </span>
                                        <span id="nao-util-conhecimento" class="sim_nao" onclick="inutil()">
                                            <span>N�o</span>
                                            <i class="pct pct-thumbs-down"></i>
                                        </span>
                                    </div>
                                    <textarea class="form-control form-control-sm ng-untouched ng-pristine ng-valid"
                                              id="comentario-conhecimento"
                                              name="name" placeholder="Deixe seu feedback sobre o conte�do deste Ponto de Interroga��o (voc� n�o est� abrindo suporte ou falando com um atendente aqui, apenas deixando feedback sobre o conte�do deste recurso/tela)" rows="5"></textarea>
                                    <div class="acao">
                                            <div onclick="avaliacao()"
                                                 class="button-zw-ui"
                                                 id="enviar-util"
                                                 style="pointer-events: none; opacity: 0.5">
                                                    <span>Enviar</span>
                                            </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-mkt"   onclick="event.stopPropagation()">
        <div  class="modal-mkt-header"><span onclick="fecharModalMkt()" class="close-wrapper"><i class="pct pct-x"></i></span></div>
        <div  class="modal-mkt-titulo" id="titulo-modal-mkt"></div>
        <div  class="modal-mkt-texto" id="texto-modal-mkt"></div>
        <div  class="modal-mkt-midia ng-star-inserted" id="conteudo-modal-mkt">

        </div>
        <div  class="modal-mkt-footer">
            <span id="linkCTA" class="column-footer btnCta">
                <a onclick="linkCta()" id="btn-cta" class="button-zw-ui"></a>
			</span>
            <span  class="column-footer">
                <a onclick="naoMostreMaisModalMkt()" id="nao_mostre_mais" class="nao_mostre_mais">N�o me mostrar mais</a>
                <a onclick="lembrarDepoisModalMkt()" class="lembreme">Lembre-me depois</a>
            </span>
        </div>
    </div>

    <style>

        .gaveta-acessos .header-modal-notificacao-versao{
            padding: 15px;
            font-family: Poppins;
        }
        .gaveta-acessos {
            width: 570px;
            height: 100vh;
            top: 0;
            right: 0;
            position: fixed;
            border-radius: 8px 0px 0px 0px;
            border: 2px solid #C9CBCF;
            background-color: #FFFFFF;
            display: none;
        }
        .container-gaveta{
            padding-left: 8px;
            padding-right: 8px;
            height: calc(100% - 120px);
            overflow: auto;
        }
        .linha-gaveta{
            display: flex;
            padding-bottom: 16px;
            padding-top: 16px;
            font-size: 14px;
            align-items: center;
        }
        .linha-gaveta .horario-acesso-cliente{
            text-align: center;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
            padding-right: 4px;
            color: #797D86;
            width: 40px;
        }
        .linha-gaveta .card-acesso-cliente{
            border-radius: 4px;
            border: 1px solid #C9CBCF;
            background-color: #FAFAFA;
            padding: 8px;
            width: 100%;
            min-height: 54px;
            display: grid;
            align-items: center;
            grid-template-columns: 14px 1fr;
        }
        .linha-gaveta .barra-cor-acesso{
            width: 6px;
            height: 100%;
            border-radius: 4px;
            background-color: #494B50;
        }
        .linha-gaveta .barra-cor-acesso.vermelho-acesso{
            background-color: #AF0404;
        }
        .linha-gaveta .barra-cor-acesso.amarelo-acesso{
            background-color: #AFAF04;
        }
        .linha-gaveta .barra-cor-acesso.azul-acesso{
            background-color: #B4CAFD;
        }
        .linha-gaveta .info-cliente-acesso{
            display: grid;
            gap: 4px;
        }
        .linha-gaveta .foto-nome-situacao-plano{
            display: grid;
            text-transform: capitalize;
            align-items: center;
            grid-template-columns: 42px 1fr;
            color: #494B50;
            font-size: 12px;
            font-style: normal;
            line-height: 125%;
        }
        .linha-gaveta .nome-plano-parq{
            display: grid;
            gap: 4px;
        }
        .linha-gaveta .foto-nome-situacao-plano img{
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }
        .linha-gaveta.divisor-hora{
            border-top: 1px solid #C9CBCF;
        }
        .linha-gaveta .acesso-gaveta-nome{
            color: #1E60FA;
            text-transform: capitalize;
            font-family: Poppins, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 14px */
            display: grid;
            align-items: center;
            justify-content: space-between;
            grid-template-columns: 1fr auto;
        }
        .linha-gaveta .acesso-mais-info{
            padding-left: 8px;
            color: #1E60FA;
            font-family: Poppins, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 14px */
            letter-spacing: 0.25px;
        }
        .linha-gaveta .acesso-situacoes{
            display: grid;
            grid-template-columns: repeat(4, auto);
            text-align: right;
            gap: 3px;
            margin-lef: 1px;
        }
        .linha-gaveta .acesso-situacoes .sit-acesso{
            width: 16px;
            height: 16px;
            padding: 5px 16px;
            justify-content: center;
            align-items: center;
            gap: 4px;
            border-radius: 50px;
            text-align: center;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
            margin-right: 5px;
            text-transform: uppercase;
        }
        .sit-vi.sit-acesso {
             color: #163E9C;
             background-color: #BCCDF5;
         }
        .sit-at.sit-acesso {
             color: #037D03;
             background-color: #B4FDB4;
         }
        .sit-in.sit-acesso {
             color: #7D0303;
             background-color: #FDB4B4;
         }
        .sit-tr.sit-acesso {
             color: #797D86;
             background-color: #E4E5E7;
         }
        .sit-no.sit-acesso {
             color: #0A4326;
             background-color: #8FEFBF;
         }
        .sit-di.sit-acesso {
             color: #0A4326;
             background-color: #63E9A6;
         }
        .sit-aa.sit-acesso {
            color: #0A4326;
            background-color: #63E9A6;
        }
        .sit-pe.sit-acesso {
             color: #0A4326;
             background-color: #1DC973;
         }
        .sit-av.sit-acesso {
             color: #705810;
             background-color: #EFD78F;
         }
        .sit-ve.sit-acesso {
             color: #705810;
             background-color: #E9C763;
         }
        .sit-tv.sit-acesso {
             color: #705810;
             background-color: #E2B736;
         }
        .sit-ca.sit-acesso {
             color: #701028;
             background-color: #F5BCCA;
         }
        .sit-de.sit-acesso {
             color: #701028;
             background-color: #EF8FA7;
         }
        .sit-in.sit-acesso {
             color: #701028;
             background-color: #E96384;
         }
        .sit-ae.sit-acesso {
             color: #105870;
             background-color: #63C7E9;
         }
        .sit-cr.sit-acesso {
             color: #105870;
             background-color: #8FD7EF;
         }
        .sit-gympass.sit-acesso {
             color: #9C5316;
             background-color: #F5D6BC;
         }
        .sit-totalPass.sit-acesso {
             color: #9C5316;
             background-color: #EFBA8F;
         }
        .sit-freepass.sit-acesso {
            color: #0A4326;
            background-color: #1DC973;
        }
        .filtros-gaveta{
            padding: 16px 16px 8px 43px;
            display: grid;
            align-items: center;
            justify-content: space-between;
            grid-template-columns: 1fr 1fr;
        }
        .filtros-gaveta i.pct-settings, .filtros-gaveta i.pct-arrow-left{
            font-size: 20px;
            color: #1E60FA;
            cursor: pointer;
            text-align: right;
        }
        .configuracoes .filtros-gaveta.configuracoes i.pct-arrow-left{
            text-align: left;
        }
        .configuracoes .filtros-gaveta.configuracoes .info-cfg-gaveta{
            font-family: Nunito Sans;
            font-size: 14px;
            font-weight: 400;
            line-height: 17.5px;
            text-align: center;
            margin-top: 16px;
        }
        .configuracoes .filtros-gaveta.configuracoes {
            grid-template-columns: auto 1fr;
            text-align: center;
            display: grid;
            font-family: Poppins;
            font-size: 14px;
            font-weight: 600;
            line-height: 17.5px;
            letter-spacing: 0.25px;

        }
        .filtros-gaveta .cb-container select, .filtros-gaveta .cb-container select:hover{
            color: #494B50;
        }
        .filtros-gaveta .cb-container select.disabled-select{
            opacity: 0.5;
            cursor: not-allowed;
        }
        .alerta-aniversario-cliente{
            font-weight: 900;
        }

        .gaveta-acessos.configuracoes .filtros-gaveta.configuracoes,
        .gaveta-acessos.configuracoes .container-gaveta.configuracoes{
            display: grid;
        }

        .gaveta-acessos.configuracoes .filtros-gaveta.listagem,
        .gaveta-acessos.configuracoes .container-gaveta.listagem,
        .gaveta-acessos .filtros-gaveta.configuracoes,
        .gaveta-acessos .container-gaveta.configuracoes{
            display: none;
        }

        @font-face {
            font-family: 'Poppins';
            src: url('beta/font/Poppins/Poppins-Regular.ttf') format('truetype');
            font-weight: 400;
            font-style: normal;
        }

        @font-face {
            font-family: 'Poppins';
            src: url('beta/font/Poppins/Poppins-Bold.ttf') format('truetype');
            font-weight: 700;
            font-style: normal;
        }
        .item-cfg-gaveta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 5px;
            font-size: 14px;
            font-weight: 400;
            line-height: 17.5px;
        }
        .configuracoes .container-gaveta.configuracoes{
            height: auto;
        }
        /* The switch - the container */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        /* Hide default HTML checkbox */
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        /* The slider */
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        /* Before the slider - the round knob */
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        /* Checked state */
        input:checked + .slider {
            background-color: #2196F3;
        }

        /* Checked state for the slider knob */
        input:checked + .slider:before {
            transform: translateX(26px);
        }

    </style>

    <h:panelGroup id="painel-token-nt">
        <script>
            const tokenNT = '${LoginControle.tokenNT}';
        </script>
    </h:panelGroup>

    <script>

        function abrirAlunoListaRapida(matricula){
            window.location.href = "${pageContext.request.contextPath}/faces/clienteNav.jsp?page=cliente&matricula="+matricula;
        }

        async function listaRapidaAcessos() {
            var tipo = 1;
            const selectEmpresa = document.getElementById('select-empresa');
            if(selectEmpresa && selectEmpresa.value){
                tipo = selectEmpresa.value;
            }
            const urlTreino = '${LoginControle.urlTreino}';
            const empresa = '${LoginControle.empresaLogado.codigo}';
            const endpoint = urlTreino + '/prest/psec/alunos/lista-rapida-acessos?tipo=' + tipo;
            try {
                const response = await fetch(endpoint, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + tokenNT,
                        'empresaId': empresa,
                        'Content-Type': 'application/json'
                    }
                });
                const buffer = await response.arrayBuffer();
                const decoder = new TextDecoder('UTF-8');
                const text = decoder.decode(buffer);
                const data = JSON.parse(text);
                const contentArray = data.content.lista;
                const verTodos = data.content.verTodos;
                Object.keys(data.content.mapConfigs).forEach(key => {
                    checkConfigACessos(key, data.content.mapConfigs[key]);
                });

                if(verTodos === false){
                    selectEmpresa.value = 2;
                    selectEmpresa.disabled = true;
                    selectEmpresa.classList.add('disabled-select');
                }

                const containerGaveta = document.getElementById('container-gaveta');
                containerGaveta.innerHTML = ''; // Limpa o container antes de preenche-lo

                contentArray.forEach(function(item) {
                    var horarioAcesso = item.hora || 'Hor�rio n�o dispon�vel';
                    var nomeCliente = item.nome || 'Nome n�o dispon�vel';
                    var planoCliente = item.plano || 'Plano n�o dispon�vel';
                    var parqCliente = item.parqAssinado ? item.parqAssinado.descricao : 'Par-q n�o dispon�vel';
                    var situacaoCliente = item.situacao;
                    var situacaoContrato = item.situacaoContrato;
                    var gympass = item.gympass;
                    var totalpass = item.totalpass;
                    var freepass = item.freepass;
                    var diaria = item.diaria;
                    var aulaAvulsa = item.aulaAvulsa;
                    var avatarCliente = item.foto || 'imagens_flat/ds3_avatar.png';
                    var alertaAcesso = item.aviso ? item.aviso.descricao : null;
                    var alertasHtml = '';
                    var alertas = null;
                    if(item.avisos && item.avisos.length > 0){
                        for(var i = 0; i < item.avisos.length; i++){
                            if(alertas == null){
                                alertas = item.avisos[i].descricao;
                            }else{
                                alertas += '<br/>' + item.avisos[i].descricao;
                            }
                        }
                        alertasHtml = '<span class="acesso-mais-info tooltipster" title="'+alertas+'">+'+item.avisos.length+'</span>';
                    }
                    var alertaAniversario = item.aniversariante;

                    var htmlContent =
                        '<div class="linha-gaveta '+ (item.divisor ? 'divisor-hora' : '')+'">' +
                        '<span class="horario-acesso-cliente">' + horarioAcesso + '</span>' +
                        '<span class="card-acesso-cliente">' +
                        '<span class="barra-cor-acesso ' + item.cor +'"></span>' +
                        '<span class="info-cliente-acesso">' +
                        '<span class="foto-nome-situacao-plano">' +
                        '<img src="' + avatarCliente + '"/>' +
                        '<span class="nome-plano-parq">' +
                        '<span class="acesso-gaveta-nome">' +
                        '<span style="cursor: pointer" onclick="abrirAlunoListaRapida('+item.matricula+')">' + nomeCliente + '</span>' +
                        '<span class="acesso-situacoes">' +
                        '<span class="sit-acesso sit-'+situacaoCliente+'">' + situacaoCliente + '</span>' +
                        (situacaoContrato ? ('<span class="sit-acesso sit-'+situacaoContrato+'">') + situacaoContrato + '</span>' : '') +
                        (gympass ? '<span class="sit-acesso sit-gympass">GY</span>' : '') +
                        (totalpass ? '<span class="sit-acesso sit-totalPass">TP</span>' : '') +
                        (freepass ? '<span class="sit-acesso sit-freepass">PL</span>' : '') +
                        (diaria ? '<span class="sit-acesso sit-di">DI</span>' : '') +
                        (aulaAvulsa ? '<span class="sit-acesso sit-aa">AA</span>' : '') +
                        '</span>' +
                        '</span>' +
                        '<span class="acesso-gaveta-plano"><b>Plano:</b> ' + planoCliente + '</span>' +
                        '<span class="acesso-gaveta-parq"><b>Par-q:</b> ' + parqCliente + '</span>' +
                        '</span>' +
                        '</span>' +
                        (alertaAcesso ?
                            ('<span class="alerta-acesso-cliente"><i class="pct pct-info"></i> <span>' + alertaAcesso + '</span>' +
                                alertasHtml) : '') +
                        '</span>' +
                        (alertaAniversario ? ('<span class="alerta-aniversario-cliente"><img src="imagens_flat/pct-cake.png"/> Hoje � anivers�rio do cliente </span>') : '') +
                        '</span>' +
                        '</span>' +
                        '</div>';

                    containerGaveta.insertAdjacentHTML('beforeend', htmlContent);
                });
                carregarTooltipster();
            } catch (error) {
                console.error('Erro ao buscar dados:', error);
            }
        }

        function abrirConfigsAcessoRapido() {
            jQuery('.gaveta-acessos').addClass('configuracoes');
        }

        function fecharConfigsAcessoRapido() {
            jQuery('.gaveta-acessos').removeClass('configuracoes');
        }

        function toggleConfig(config) {
            const checkbox = document.querySelector('.check' + config);
            chamarServico(config, checkbox.checked);
        }

        function checkConfigACessos(config, valor) {
            const checkbox = document.querySelector('.check' + config);
            if(checkbox) {
                checkbox.checked = valor;
            }
        }

        async function chamarServico(config, valor) {
            const urlTreino = '${LoginControle.urlTreino}';
            const empresa = '${LoginControle.empresaLogado.codigo}';
            const url = urlTreino + '/prest/psec/alunos/lista-rapida-acessos-notf/'+
                config + '/' + valor;
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + tokenNT,
                        'empresaId': empresa,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({}),
                    mode: 'cors'
                });

                if (!response.ok) {
                    throw new Error(`Erro: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('Erro ao chamar o servi�o:', error);
            }
        }

    </script>

    <div class="gaveta-acessos" onclick="event.stopPropagation()">
        <div class="header-modal-notificacao-versao">
            <div>
                <span>�ltimas entradas</span>
            </div>
            <div style="text-align: right;">
                <i class="pct pct-x" style="font-size: 18px; cursor: pointer"
                   onclick="fecharPonto()"></i>
            </div>
        </div>

        <div class="filtros-gaveta listagem">
            <div class="cb-container">
                <select id="select-empresa" onchange="listaRapidaAcessos()" >
                    <option value="1">Todos os alunos</option>
                    <option value="2">Alunos vinculados a mim</option>
                </select>
            </div>

            <i style="display: none" onclick="abrirConfigsAcessoRapido()" class="pct pct-settings"></i>
        </div>

        <div class="filtros-gaveta configuracoes">
            <i onclick="fecharConfigsAcessoRapido()" class="pct pct-arrow-left"></i>
            <span>Configurar alerta de entradas</span>
            <span></span>
            <span class="info-cfg-gaveta">Receba alertas das entradas de clientes com situa��es espec�ficas vinculados a voc� atrav�s do sistema Pacto. </span>
        </div>


        <div  class="container-gaveta configuracoes">
            <div class="item-cfg-gaveta">
                <span>Sem programa de treino</span>
                <label class="switch">
                    <input class="checkSEM_PROGRAMA_DE_TREINO" type="checkbox" onclick="toggleConfig('SEM_PROGRAMA_DE_TREINO')">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="item-cfg-gaveta">
                <span>Faltosos</span>
                <label class="switch">
                    <input class="checkFALTOSOS" type="checkbox" onclick="toggleConfig('FALTOSOS')">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="item-cfg-gaveta">
                <span>4 dias ou mais sem executar o treino</span>
                <label class="switch">
                    <input class="checkQUATRO_DIAS_OU_MAIS_SEM_EXECUTAR_O_TREINO" type="checkbox" onclick="toggleConfig('QUATRO_DIAS_OU_MAIS_SEM_EXECUTAR_O_TREINO')">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="item-cfg-gaveta">
                <span>Programa de treino vencido</span>
                <label class="switch">
                    <input class="checkPROGRAMA_DE_TREINO_VENCIDO" type="checkbox" onclick="toggleConfig('PROGRAMA_DE_TREINO_VENCIDO')">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="item-cfg-gaveta">
                <span>Programa de treino a Vencer</span>
                <label class="switch">
                    <input class="checkPROGRAMA_DE_TREINO_A_VENCER" type="checkbox" onclick="toggleConfig('PROGRAMA_DE_TREINO_A_VENCER')">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="item-cfg-gaveta">
                <span>Aluno sem v�nculo de professor treinoweb</span>
                <label class="switch">
                    <input class="checkALUNO_SEM_VINCULO_DE_PROFESSOR_TREINOWEB" type="checkbox" onclick="toggleConfig('ALUNO_SEM_VINCULO_DE_PROFESSOR_TREINOWEB')">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="item-cfg-gaveta">
                <span>Parcelas em atraso</span>
                <label class="switch">
                    <input class="checkPARCELAS_EM_ATRASO" type="checkbox" onclick="toggleConfig('PARCELAS_EM_ATRASO')">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="item-cfg-gaveta">
                <span>Avalia��o f�sica atrasada</span>
                <label class="switch">
                    <input class="checkAVALIACAO_FISICA_ATRASADA" type="checkbox" onclick="toggleConfig('AVALIACAO_FISICA_ATRASADA')">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="item-cfg-gaveta">
                <span>Sem assinatura de contrato</span>
                <label class="switch">
                    <input class="checkSEM_ASSINATURA_DE_CONTRATO" type="checkbox" onclick="toggleConfig('SEM_ASSINATURA_DE_CONTRATO')">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="item-cfg-gaveta">
                <span>Cadastro incompleto</span>
                <label class="switch">
                    <input class="checkCADASTRO_INCOMPLETO" type="checkbox" onclick="toggleConfig('CADASTRO_INCOMPLETO')">
                    <span class="slider"></span>
                </label>
            </div>

        </div>
        <div id="container-gaveta" class="container-gaveta listagem">
            <div class="linha-gaveta">

            </div>
        </div>

    </div>
</div>

<div id="notification" class="notification hidden">
    <div>
        <div class="div-fundo-gif-chat"></div>
        <div class="modal-content-gif-chat">
            <div>
                <div class="div-gif-chat">
                    <img src="https://cdn1.pactorian.net/wiki/Gif-suporte-3-texto.gif" alt="GIF" />
                </div>
                <div class="div-content-btn-gif-chat">
                    <div class="div-btn-gif-chat" onclick="closeModalInfoChat()">
					    <span class="close-modal-chat-interrogacao">
                            Entendi
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-notificacoes-versoes balloon-modal hidden" onclick="fecharNotificacoes()">
    <div id="id-notificacoes-versoes" class="modal-dialog">
        <div class="modal-content-usuario-zw-ui" onclick="event.stopPropagation()"
             style="border-radius: 8px; width: 420px; padding-bottom: 15px; min-height: 290px;">
            <div class="modal-notificacao-versao-modal">
                <div class="header-modal-notificacao-versao">
                    <div>
                        <span>Notifica��es & dicas</span>
                    </div>
                    <div style="text-align: right;">
                        <i class="pct pct-x" style="font-size: 18px; cursor: pointer"
                           onclick="fecharNotificacoes()"></i>
                    </div>
                </div>
                <div class="container-modal-notificacao-versao">
                    <div style="padding: 15px 0">
                        <div class="container-modal-notificacao-versao-menu">
                            <div class="item-menu-modal-notificacao-versao aba-alertas"
                                 onclick="selecionarAba('aba-alertas')">
                                Alertas <div id="item-menu-qtd-itens-alertas"
                                             class="item-menu-qtd-itens-alertas">0</div>
                            </div>
                            <div class="item-menu-modal-notificacao-versao aba-dicas"
                                 onclick="selecionarAba('aba-dicas')">
                                Dicas <div id="item-menu-qtd-itens-dicas"
                                           class="item-menu-qtd-itens-dicas">0</div>
                            </div>
                            <div class="item-menu-modal-notificacao-versao aba-versoes"
                                 onclick="selecionarAba('aba-versoes')">
                                Vers�o <div id="item-menu-qtd-itens-versao"
                                            class="item-menu-qtd-itens-versao">0</div>
                            </div>
                            <div></div>
                        </div>
                    </div>
                    <div class="container-modal-notificacao-versao-menu-conteudo">
                        <div class="conteudo-modal-notificacao-versao-aba conteudo-modal-notificacao-versao-aba-alertas">
                            <div id="itens-filtro-alertas"></div>
                            <div id="itens-alertas" class="div-itens-notificacoes"></div>
                        </div>
                        <div class="conteudo-modal-notificacao-versao-aba conteudo-modal-notificacao-versao-aba-dicas">
                            <div id="itens-filtro-dicas"></div>
                            <div id="itens-dicas" class="div-itens-notificacoes"></div>
                        </div>
                        <div class="conteudo-modal-notificacao-versao-aba conteudo-modal-notificacao-versao-aba-versoes">
                            <div id="itens-filtro-versoes"></div>
                            <div id="itens-versoes" class="div-itens-notificacoes"></div>
                        </div>
                        <div id="saiba-mais-notificacoes" style="display: none">
                            <div class="div-saiba-mais">
                                <div class="div-saiba-mais-titulo">
                                    <div id="saiba-mais-icon"
                                         class="saiba-mais-icon"
                                         onclick="sairSaibaMais()">
                                        <i class="pct pct-arrow-left"></i>
                                    </div>
                                    <div id="saiba-mais-titulo"
                                         class="saiba-mais-titulo">
                                    </div>
                                </div>
                            </div>
                            <div id="saiba-mais-titulo-html"
                                 class="saiba-mais-titulo-html">
                            </div>
                        </div>
                        <div class="div-notificacao-carregando-geral">
                            <img class="div-notificacao-carregando-icon"
                                 src="imagens_flat/loader-small.gif" width="35"/>
                            <div class="div-notificacao-carregando-texto">Carregando...</div>
                        </div>
                        <div class="div-notificacao-itens-empty">
                            <div class="div-notificacao-itens-empty-texto">Nenhum item encontrado</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
