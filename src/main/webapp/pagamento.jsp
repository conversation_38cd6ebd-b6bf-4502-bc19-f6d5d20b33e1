<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="iso-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="css/opcoespagamento_v2.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<c:set var="pagina" scope="request" value="<%=request.getRequestURI()%>"/>
<c:set var="root" value="${pageContext.request.contextPath}" scope="request" />

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="root" value="${pageContext.request.contextPath}" scope="request"/>
<script type="text/javascript" src="script/jquery.maskedinput-1.2.2.js"></script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="Formas de Pagamento"/>
    </title>
    <head>
        <meta http-equiv=Content-Type content="text/html; charset=iso-8859-1">
    </head>

    <%@include file="includes/integracao/include_pagamentodigital.jsp" %>
    <%@include file="include_gerarChequeNovo.jsp" %>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <rich:modalPanel id="modalPinpad"  styleClass="novaModal noMargin" shadowOpacity="true"
                     width="500" height="320">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Pinpad"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup layout="block" style="display: flex; justify-content: center; align-items: center; flex-direction: column;">
            <a4j:form id="formModalPinpad" style="height: 100%">
                <h:panelGroup id="panelGeralModalPinpad" layout="block">
                    <div style="padding-top: 20px; text-align: center;">
                        <h:graphicImage url="images/logo-stone-connect.png"
                                        rendered="#{MovPagamentoControle.pinpadStoneConnect}"
                                        style="height: 40px;vertical-align: middle;"/>
                        <h:graphicImage url="images/logo-getcard.svg"
                                        rendered="#{MovPagamentoControle.pinpadGetCard}"
                                        style="height: 40px;vertical-align: middle;"/>
                    </div>

                    <h:panelGroup layout="block"
                                  rendered="#{!MovPagamentoControle.confirmarPinpad}"
                                  style="padding-top: 20px; display: flex; justify-content: center">

                        <h:panelGroup styleClass="bloco"
                                      style="padding: 15px;"
                                      rendered="#{fn:length(MovPagamentoControle.listaPinpad) > 1}">
                            <span class="cinza negrito upper flex">
                                <h:outputText value="POS:"/>
                            </span>
                            <div class="block cb-container">
                                <h:selectOneMenu id="selectPinpadModal"
                                                 value="#{MovPagamentoControle.pinpadSelecionado}"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 style="font-size: 14px;top:0px;">
                                    <f:selectItems
                                            value="#{MovPagamentoControle.listaSelectItemPinpad}"/>
                                    <a4j:support event="onchange"
                                                 reRender="panelGeralModalPinpad,painelPagamentoPinpad"
                                                 action="#{MovPagamentoControle.selecionarPinPad}"
                                                 oncomplete="#{MovPagamentoControle.onCompletePinpad}"/>
                                </h:selectOneMenu>
                            </div>
                        </h:panelGroup>

                        <h:panelGroup styleClass="bloco"
                                      style="padding: 15px;"
                                      rendered="#{MovPagamentoControle.apresentarParcelasPinpad && MovPagamentoControle.convenioPinPadSucesso}">
                            <span class="cinza negrito upper flex">
                                <h:outputText value="Parcelas:"/>
                            </span>
                            <div class="block cb-container">
                                <h:selectOneMenu id="nrParcelasStoneConnect"
                                                 value="#{MovPagamentoControle.nrParcelasPinpad}"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" style="top:0px;">
                                    <f:selectItems
                                            value="#{MovPagamentoControle.selectItemNrVezesPinpad}"/>
                                    <a4j:support event="onchange"
                                                 reRender="panelGeralModalPinpad,painelPagamentoPinpad"
                                                 action="#{MovPagamentoControle.criarPedidoPinpad}"
                                                 oncomplete="#{MovPagamentoControle.onCompletePinpad}"/>
                                </h:selectOneMenu>
                            </div>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block"
                                  id="panelMensagemPinPad"
                                  rendered="#{MovPagamentoControle.confirmarPinpad}"
                                  style="color: #51555a; margin-bottom: 20px; font-family: 'Nunito Sans', sans-serif; font-size: 24px;font-weight: 400; padding-top: 20px; text-align: center">
                        <h:outputText id="mensagemPinPad"
                                      style="color: #51555a; font-family: 'Nunito Sans', sans-serif; font-size: 24px;font-weight: 400;"
                                      value="Aguardando pagamento..."/>
                    </h:panelGroup>

                    <div style="padding-top: 30px; text-align: center;">
                        <a4j:commandLink id="cancelarPinpad"
                                         value="Cancelar"
                                         styleClass="tooltipster botoes nvoBt btSec"
                                         action="#{MovPagamentoControle.cancelarPinpad}"
                                         oncomplete="#{MovPagamentoControle.onCompletePinpad}"/>
                    </div>

                    <a4j:poll status="false"
                              enabled="#{MovPagamentoControle.confirmarPinpadStone}"
                              interval="2000"
                              action="#{MovPagamentoControle.confirmarPagamentoPinpad}"
                              oncomplete="#{MovPagamentoControle.onCompletePinpad}"
                              reRender="modalPix,msgConfirmacaoPix,formConfirmacaoPagamento,form:geralnovopagamento,geralnovopagamento,
                                  mensagemSup,panelSaldoContaCorrente,dataPagto,panelCobrancaMultaJuros,form:panelBotoesControle,
                                  form:panelEsconderControles,form:panelEsconderControles,form:panelEsconderControles2,
                                  form:panelEsconderControles3, form:tituloFormasPagamento,residuo,qrCodePix,conveniosPix"/>

                    <a4j:poll status="false"
                              enabled="#{MovPagamentoControle.confirmarPinpadGetCard}"
                              interval="5000"
                              oncomplete="consultarGetcard()"/>
                </h:panelGroup>
            </a4j:form>
        </h:panelGroup>
    </rich:modalPanel>

    <rich:modalPanel id="modalPix"  styleClass="novaModal noMargin" shadowOpacity="true"
                     width="600" height="690">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="QRcode para pagamento com pix"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink17"/>
                <rich:componentControl for="modalPix" attachTo="hidelink17" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup layout="block" style="display: flex; justify-content: center; align-items: center; flex-direction: column;">
            <h:panelGroup id="msgConfirmacaoPix" styleClass="pix-qr-row" layout="block">
                <h:outputText style="color: #cc9933" rendered="#{MovPagamentoControle.pixVO.status eq 'ATIVA'}" value="Aguardando pagamento" />
                <h:outputText style="color: #5aac41" rendered="#{MovPagamentoControle.pixVO.status eq 'CONCLUIDA'}" value="Pagamento confirmado" />
                <h:outputText style="color: orangered" rendered="#{MovPagamentoControle.pixVO.status eq 'EXPIRADA'}" value="Pix expirado" />
            </h:panelGroup>
            <h:graphicImage value="#{MovPagamentoControle.pixVO.textoQRCodeSemEncodeSomenteJSP}&w=570&h=570" alt="QRcode Pix" />
        </h:panelGroup>
    </rich:modalPanel>

    <rich:modalPanel id="modalPixTelefone"
                     onshow="document.getElementById('formTelenefoneWhatsPix:pix-input-phone-number').focus();"
                     styleClass="novaModal
                     noMargin" shadowOpacity="true"
                     width="400" height="300">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Verificação de número"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText id="hidelink8Pix" styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
                <rich:componentControl id="closeModalPixTelefone" for="modalPixTelefone" attachTo="hidelink8Pix" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
            <a4j:form id="formTelenefoneWhatsPix" style="height: 100%">
                <h:panelGroup id="blockModalPixBtnSend" layout="block"
                              style="display: flex; justify-content: space-evenly; flex-direction: column; align-items: center; height: 100%;">
                        <span>Confira o número de telefone do aluno antes de enviar</span>
                    <h:panelGrid columns="2" style="display: contents" width="100%">
                        <h:panelGroup
                                style="display: flex; justify-content: center; align-items: center; border: 1px solid #ddd;">
                            <img style="padding-left: 13px;" src="${root}/images/pix/whats-icon-input.svg" alt=""/>
                            <h:inputText value="#{MovPagamentoControle.telefonePix}"
                                         id="pix-input-phone-number"
                                         styleClass="form"
                                         maxlength="15"
                                         onfocus="phoneNumberFormatModalPix('formTelenefoneWhatsPix:pix-input-phone-number');"
                                         onkeypress="if (event.keyCode == 13){enviarWhatsPix();} else {return mascara(this.form, this.id , '(99) 99999-9999', event);}"
                                         onchange="return mascara(this.form, this.id , '(99) 99999-9999', event);"
                                         style="text-align: center; width: 160px; border: none !important; box-shadow: none !important;"></h:inputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="margin-left: 2%; margin-block-start: auto;">
                            <a4j:commandLink action="#{MovPagamentoControle.limparTelefoneWhatsPix}"
                                             oncomplete="document.getElementById('formTelenefoneWhatsPix:pix-input-phone-number').focus()"
                                             style="text-decoration: none"
                                             reRender="formTelenefoneWhatsPix">
                                <i class="fa-icon-eraser texto-size-18 tooltipster" title="Limpar"
                                   style="margin-left: 5px;"></i>
                                <h:outputText styleClass="title"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>

                    <a4j:jsFunction name="enviarWhatsPix"
                                        action="#{MovPagamentoControle.enviarWhatsPix}"
                                        oncomplete="#{MovPagamentoControle.linkSendWhatsPix}" >
                        </a4j:jsFunction>
                        <div onclick="enviarWhatsPix()" class="pix-qr-button">
                            <img class="pix-qr-button-icon" src="${pageContext.request.contextPath}/images/pix/whats.svg" alt="Enviar por WhatsApp"/>
                            <span>Enviar por WhatsApp</span>
                        </div>
                </h:panelGroup>
            </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalPixEmail"  styleClass="novaModal noMargin" shadowOpacity="true"
                     width="400" height="300">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Email do aluno"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText id="hidelink18Pix" styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
                <rich:componentControl id="closeModalPixEmail" for="modalPixEmail" attachTo="hidelink18Pix" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formEmailPix" style="height: 100%">
            <h:panelGroup id="blockModalPixEmailBtnSend" layout="block"
                          style="display: flex; justify-content: space-evenly; flex-direction: column; align-items: center; height: 100%;">
                <span>Confira o email do aluno antes de enviar</span>
                <h:inputText value="#{MovPagamentoControle.emailPix}"
                             onkeypress="if (event.keyCode == 13) { enviarEmailPix(); return false; }"
                             style="text-align: center; width: 200px;"></h:inputText>
                <a4j:jsFunction name="enviarEmailPix"
                                action="#{MovPagamentoControle.enviarEmailPix}"
                                oncomplete="#{MovPagamentoControle.onCompleteJs}" >
                </a4j:jsFunction>
                <div onclick="enviarEmailPix()" class="pix-qr-button email">
                    <img class="pix-qr-button-icon" src="${pageContext.request.contextPath}/images/pix/email.svg" alt="Enviar email"/>
                    <span>Enviar email</span>
                </div>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConfirmacaoPagamento" autosized="true"
                     styleClass="novaModal"
                     shadowOpacity="true" width="450" height="250"
                     onshow="document.getElementById('formConfirmacaoPagamento:senha').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação do Pagamento"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink"/>
                <rich:componentControl for="panelConfirmacaoPagamento"
                                       attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacaoPagamento" styleClass="font-size-Em-max">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%">
                <h:panelGrid id="panelResponsavelPagamento" columns="1" width="100%" cellpadding="10"
                             columnClasses="colunaEsquerda" styleClass="font-size-Em-max">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Código:"/>
                        <h:inputText id="codigoUsuario" size="3" maxlength="100"
                                     style="margin-left:6px"
                                     value="#{MovPagamentoControle.movPagamentoVO.responsavelPagamento.codigo}">
                            <a4j:support event="onchange"
                                         focus="formConfirmacaoPagamento:senha"
                                         action="#{MovPagamentoControle.consultarResponsavelPagamento}"
                                         reRender="panelResponsavelPagamento"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;"
                                     value="#{MovPagamentoControle.movPagamentoVO.responsavelPagamento.username}"/>

                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Usuário:"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" style="margin-left:5px"
                                      value="#{MovPagamentoControle.movPagamentoVO.responsavelPagamento.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64"
                                       styleClass="inputTextClean"
                                       style="margin-left:8px"
                                       value="#{MovPagamentoControle.movPagamentoVO.responsavelPagamento.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('loginConfirmacao')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="mensagem2">
                    <h:outputText styleClass="mensagem"
                                  value="#{MovPagamentoControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{MovPagamentoControle.mensagemDetalhada}"/>
                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandLink id="loginConfirmacao"
                                         value="#{msg_bt.btn_confirmar}"
                                         title="#{msg.msg_gravar_dados}"
                                         action="#{MovPagamentoControle.verificarUsuarioSenhaResponsavelPagamentoCupom}"
                                         styleClass="botaoPrimario texto-size-14-real"
                                         reRender="formConfirmacaoPagamento,geralnovopagamento,
                                       mensagemSup,panelSaldoContaCorrente, dataPagto, panelCobrancaMultaJuros,
                                       form:panelBotoesControle,form:panelEsconderControles,
                                       form:panelEsconderControles,form:panelEsconderControles2,form:panelEsconderControles3, form:tituloFormasPagamento,residuo"
                                         oncomplete="#{MovPagamentoControle.apresentarMensagem}"/>

                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandLink id="loginConfirmacao"
                                         value="#{msg_bt.btn_confirmar}"
                                         title="#{msg.msg_gravar_dados}"
                                         styleClass="botaoPrimario texto-size-16-real"
                                         action="#{MovPagamentoControle.pagamentoParcelaCE}"
                                         actionListener="#{MovPagamentoControle.listenerPagamentoCE}">

                            <!-- funcao.confirmacao do pagamento -->
                            <f:attribute name="funcao" value="117"/>
                            <f:attribute name="codigoEventoInteresse"
                                         value="#{MovParcelaControle.codigoEventoInteresse}"/>
                        </a4j:commandLink>
                    </c:if>
                </h:panelGroup>
            </h:panelGrid>
            <input type="hidden" value="${modulo}" name="modulo"/>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConfirmacaoPagamentoDepositoNaConta" autosized="true" shadowOpacity="true" width="450"
                     height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkConfirmacaoPagamentoDepositoNaConta"/>
                <rich:componentControl
                        for="panelConfirmacaoPagamentoDepositoNaConta"
                        attachTo="hidelinkConfirmacaoPagamentoDepositoNaConta"
                        operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacaoPagamentoDepositoNaConta">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Atenção!"/>
                </h:panelGrid>
                <h:panelGrid id="panelConfirmacaoPagamentoDepositoNaConta"
                             columns="1" width="100%" columnClasses="colunaEsquerda"
                             styleClass="tabForm">
                    <h:panelGroup>
                        <h:outputText styleClass="text"
                                      value="Você está efetuando um pagamento que gerou crédito para o cliente. O que deseja fazer com ele?"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="botoes2" columns="2">
                    <a4j:commandButton id="depositarContar"
                                       image="./imagens/botaoDepositarNaConta.png"
                                       alt="Depositar dinheiro na Conta Academia"
                                       action="#{MovPagamentoControle.depositarNaConta}"
                                       reRender="panelConfirmacao, panelAutorizacaoFuncionalidade, totalLancado, residuo, mensagemSup, mensagemInf,mensagem1, mensagem2, form"
                                       oncomplete="Richfaces.hideModalPanel('panelConfirmacaoPagamentoDepositoNaConta');#{MovPagamentoControle.apresentarMensagem};#{MovPagamentoControle.mensagemNotificar}"
                                       />
                    <%--
<a4j:commandButton id="troco" image="./imagens/botaoTroco.png"
                   alt="Receber Troco" action="#{MovPagamentoControle.devolverTroco}"
                   reRender="formConfirmacaoPagamento"
                   oncomplete="Richfaces.hideModalPanel('panelConfirmacaoPagamentoDepositoNaConta');Richfaces.showModalPanel('panelConfirmacaoPagamento'), setFocus(formConfirmacaoPagamento,'formConfirmacaoPagamento:username');" />
                    --%>
                    <a4j:commandButton id="fechar" image="./imagens/botaoFechar.png"
                                       alt="Fechar"
                                       onclick="Richfaces.hideModalPanel('panelConfirmacaoPagamentoDepositoNaConta');"/>


                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelAutorizacao" autosized="true"
                     styleClass="novaModal"
                     shadowOpacity="true" width="450" height="250"
                     onshow="document.getElementById('formAutorizacao:senha2').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText
                        value="#{MovPagamentoControle.abrirRichModalContaCorrente ? 'Autorização Para utilizar crédito em academia' : 'Autorização Para Utilizar Dinheiro em Academia' }"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <a4j:commandLink styleClass="linkPadrao  texto-size-20-real"
                                 onclick="Richfaces.hideModalPanel('panelAutorizacao');document.getElementById('formAutorizacao:botaoFecharHide').click()">
                    <h:outputText
                            styleClass="texto-cor-branco fa-icon-remove-sign"/>
                </a4j:commandLink>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAutorizacao" styleClass="font-size-Em-max">
            <a4j:commandButton style="display : none;" status="false" id="botaoFecharHide"
                               reRender="panelSaldoContaCorrente, panelCobrancaMultaJuros, mensagem1, escolhaFormaPagamento , escolhaFormaPagamentoCC, mensagemSup,mensagemInf, totalLancado, residuo, panelBotoesControle"
                               action="#{MovPagamentoControle.fecharJanelaResponsavelLiberacaoContaAcademia}"
                               oncomplete="Richfaces.hideModalPanel('panelAutorizacao');montaModulomenu();"/>

            <input type="hidden" value="${modulo}" name="modulo"/>
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid id="panelResponsavelLiberacao" columns="1" width="100%" styleClass="font-size-Em-max"
                             cellpadding="8">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Código:"/>
                        <h:inputText id="codigoUsuario" size="5" maxlength="7"
                                     style="margin-left:5px"
                                     value="#{MovPagamentoControle.movContaCorrenteCliente.responsavelAutorizacao.codigo}">
                            <a4j:support event="onchange" focus="formAutorizacao:senha2"
                                         action="#{MovPagamentoControle.consultarResponsavelLiberacao}"
                                         oncomplete="montaModulomenu();"
                                         reRender="panelResponsavelLiberacao"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden2" size="3" maxlength="10" style="margin-left:6px;opacity:0;"
                                     value="#{MovPagamentoControle.movContaCorrenteCliente.responsavelAutorizacao.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Usuário:"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" style="margin-left:5px"
                                      value="#{MovPagamentoControle.movContaCorrenteCliente.responsavelAutorizacao.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha2" size="14" maxlength="14"
                                       styleClass="inputTextClean"
                                       style="margin-left:8px"
                                       value="#{MovPagamentoControle.movContaCorrenteCliente.responsavelAutorizacao.senha}"
                                       onkeypress="validarEnter(event,'formAutorizacao:login');"/>
                    </h:panelGroup>
                </h:panelGrid>


                <h:outputText styleClass="mensagem"
                              value="#{MovPagamentoControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              value="#{MovPagamentoControle.mensagemDetalhada}"/>


                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="login" value="#{msg_bt.btn_confirmar}"
                                     styleClass="botaoPrimario texto-size-14-real" title="#{msg.msg_gravar_dados}"
                                     action="#{MovPagamentoControle.verificarUsuarioSenhaResponsavelLiberacaoContaAcademia}"
                                     reRender="panelCobrancaMultaJuros, panelSaldoContaCorrente,mensagem1, escolhaFormaPagamento,escolhaFormaPagamentoCC,
                                       mensagemInf, mensagemSup, totalLancado, residuo, panelBotoesControle"
                                     oncomplete="#{MovPagamentoControle.autorizacao};montaModulomenu();"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="panelConfirmarAlteracao" autosized="true" styleClass="novaModal" shadowOpacity="true"
                     width="450" height="200">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmar Alteração da Data de Pagamento"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink7"/>
                <rich:componentControl for="panelConfirmarAlteracao" attachTo="hidelink7" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmarAlteracao" ajaxSubmit="true" styleClass="font-size-Em-max">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" width="100%">
                    <rich:spacer width="10"/>
                    <h:panelGroup styleClass="font-size-Em-max">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="Data de Pagamento:"/>
                        <rich:spacer height="5"/>
                        <h:panelGroup layout="block" styleClass="dateTimeCustom">
                            <rich:calendar id="dataPagto"
                                           value="#{MovPagamentoControle.dataAuxiliar}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="dd/MM/yyyy"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           enableManualInput="true"
                                           styleClass="inputTextClean"
                                           zindex="2"
                                           showWeeksBar="false">
                                <a4j:support event="oninputchange"
                                             reRender="panelConfirmarAlteracao"
                                             oncomplete="Richfaces.showModalPanel('panelConfirmarAlteracao')"
                                             action="#{MovPagamentoControle.validardatabase}" focus="dataPagto"/>
                                <a4j:support event="onchanged" reRender="panelConfirmarAlteracao"
                                             oncomplete="Richfaces.showModalPanel('panelConfirmarAlteracao')"
                                             action="#{MovPagamentoControle.validardatabase}" focus="dataPagto"/>
                            </rich:calendar>
                            <rich:jQuery query="mask('99/99/9999');" selector=".rich-calendar-input"></rich:jQuery>
                        </h:panelGroup>
                        <rich:spacer height="5"/>
                        <h:message for="dataPagto" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <rich:spacer style="display:block" width="10"/>
                    <h:outputText styleClass="text" value="Alterar a data de pagamento alterará todos os relatórios e
                                  estatísticas do sistema referentes a data escolhida. Tem certeza que deseja continuar? "/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{MovPagamentoControle.mensagemDetalhada}"/>
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink id="confirmar" reRender="dataPagto, panelAutorizacaoFuncionalidade"
                                         action="#{MovPagamentoControle.autorizarDataPagto}"
                                         oncomplete="Richfaces.hideModalPanel('panelConfirmarAlteracao');"
                                         value="#{msg_bt.btn_confirmar}" accesskey="2"
                                         styleClass="botaoPrimario texto-size-14-real"/>
                        <rich:spacer width="10"/>
                        <a4j:commandLink id="cancelar" oncomplete="Richfaces.hideModalPanel('panelConfirmarAlteracao');"
                                         value="#{msg_bt.btn_fechar}" accesskey="2"
                                         styleClass="botaoSecundario texto-size-14-real"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelAutorizarDataPagto" styleClass="novaModal" autosized="true" shadowOpacity="true"
                     width="450" height="250" onshow="document.getElementById('formAutorizarDataPagto:senha').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Autorizar Nova Data de Pagamento"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink8"/>
                <rich:componentControl for="panelAutorizarDataPagto" attachTo="hidelink8" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAutorizarDataPagto" styleClass="font-size-Em-max">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid id="panelAutorizarDP" columns="1" width="100%" columnClasses="colunaEsquerda"
                             styleClass="font-size-Em-max" cellpadding="10">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-16-real texto-cor-cinza texto-font" value="Código:"/>
                        <h:inputText id="codigoUsuario" size="3" maxlength="7" style="margin-left:5px"
                                     value="#{MovPagamentoControle.responsavelDataPagto.codigo}">
                            <a4j:support event="onchange" focus="formAutorizarDataPagto:senha"
                                         action="#{MovPagamentoControle.consultarResponsavelDataPagto}"
                                         reRender="panelAutorizarDP, mensagemSup,mensagemInf"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;"
                                     value="#{MovPagamentoControle.responsavelDataPagto.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-16-real  texto-cor-cinza texto-font" value="Usuário:"/>
                        <h:outputText styleClass="texto-size-16-real  texto-cor-cinza texto-font"
                                      style="margin-left:6px"
                                      value="#{MovPagamentoControle.responsavelDataPagto.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-16-real  texto-cor-cinza texto-font" value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha" styleClass="inputTextClean" size="14"
                                       maxlength="64" style="margin-left:8px"
                                       value="#{MovPagamentoControle.responsavelDataPagto.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('autorizarDP')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="autorizarDP" value="#{msg_bt.btn_confirmar}"
                                     reRender="form"
                                     styleClass="botaoPrimario texto-size-16-real "
                                     oncomplete="Richfaces.hideModalPanel('panelAutorizarDataPagto');#{MovPagamentoControle.msgAlert};montaModulomenu();"
                                     title="#{msg.msg_gravar_dados}"
                                     action="#{MovPagamentoControle.autorizarDataPagto}"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%@include file="includes/include_pinpad_cappta.jsp" %>
    <%@include file="includes/include_pagamento_pinpad.jsp" %>

    <h:form id="form">

        <a4j:jsFunction name="concluirPinpadGetCard"
                        action="#{MovPagamentoControle.confirmarPagamentoPinpad}"
                        oncomplete="#{MovPagamentoControle.onCompletePinpad}"
                        reRender="modalPix,msgConfirmacaoPix,formConfirmacaoPagamento,form:geralnovopagamento,geralnovopagamento,
        mensagemSup,panelSaldoContaCorrente,dataPagto,panelCobrancaMultaJuros,form:panelBotoesControle,
        form:panelEsconderControles,form:panelEsconderControles,form:panelEsconderControles2,
        form:panelEsconderControles3, form:tituloFormasPagamento,residuo,qrCodePix,conveniosPix">
            <a4j:actionparam name="param1" assignTo="#{MovPagamentoControle.pinPadPedidoVO.paramsResp}"/>
        </a4j:jsFunction>

        <a4j:jsFunction name="salvarIdExternoGetCard"
                        action="#{MovPagamentoControle.salvarIdExternoGetCard}"
                        reRender="painelPagamentoPinpad,formModalPinpad:panelGeralModalPinpad">
            <a4j:actionparam name="param1" assignTo="#{MovPagamentoControle.pinPadPedidoVO.idExterno}"/>
        </a4j:jsFunction>

        <a4j:jsFunction name="erroGetCard"
                        action="#{MovPagamentoControle.erroGetCard}"
                        oncomplete="#{MovPagamentoControle.onCompletePinpad}"
                        reRender="painelPagamentoPinpad,formModalPinpad:panelGeralModalPinpad">
            <a4j:actionparam name="param1" assignTo="#{MovPagamentoControle.pinPadPedidoVO.paramsResp}"/>
        </a4j:jsFunction>

        <a4j:jsFunction name="gravarLogGetCard"
                        status="false"
                        action="#{MovPagamentoControle.gravarLogGetCard}">
            <a4j:actionparam name="param1" assignTo="#{MovPagamentoControle.pinPadPedidoVO.retornoPinpadOperacao}"/>
            <a4j:actionparam name="param2" assignTo="#{MovPagamentoControle.pinPadPedidoVO.retornoPinpad}"/>
        </a4j:jsFunction>


        <a4j:jsFunction name="concluirPinpad" action="#{MovPagamentoControle.confirmarPagamentoCappta}"
                        reRender="formConfirmacaoPagamento,
                                       mensagemSup,panelSaldoContaCorrente, dataPagto, panelCobrancaMultaJuros,
                                       form:panelBotoesControle,form:panelEsconderControles,
                                       form:panelEsconderControles,form:panelEsconderControles2,form:panelEsconderControles3, form:tituloFormasPagamento,residuo, form:mensagemInf, form:geralnovopagamento">
            <a4j:actionparam name="param1" assignTo="#{MovPagamentoControle.pinpad.retorno}"/>
            <a4j:actionparam name="param2" assignTo="#{MovPagamentoControle.pinpad.numeroUnicoTransacao}"/>
            <a4j:actionparam name="param3" assignTo="#{MovPagamentoControle.pinpad.respostaRequisicao}"/>
        </a4j:jsFunction>

        <a4j:jsFunction name="erroPagamentoPinpad" action="#{MovPagamentoControle.erroPagamentoPinpad}">
            <a4j:actionparam name="param1" assignTo="#{MovPagamentoControle.pinpad.respostaRequisicao}"/>
        </a4j:jsFunction>

        <a4j:jsFunction name="fazerCheckoutCappta" action="true" status="false"
                        reRender="form:geralnovopagamento"
                        oncomplete="#{MovPagamentoControle.onCompleteFazerCheckoutCappta}">
        </a4j:jsFunction>

        <h:inputHidden id="bandeiraCappta"
                       value="#{MovPagamentoControle.pinpad.codBandeiraCappta}"/>

        <input type="hidden" value="${modulo}" name="modulo"/>

        <c:if test="${modulo eq 'zillyonWeb'}">
            <jsp:include page="include_head.jsp" flush="true"/>
        </c:if>
        <c:if test="${modulo eq 'centralEventos'}">
            <%@include file="pages/ce/includes/include_head.jsp" %>
        </c:if>
        <h:panelGroup layout="block" style="display: inline-table; overflow: visible; width: 100%">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central"
                                      style="position:relative;height: 100% !important;">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial">
                                <h:panelGroup layout="block" styleClass="container-box-header">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo"
                                                      value="Formas de Pagamento"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-receber-uma-parcela-do-aluno-no-caixa-em-aberto/"
                                                      title="Clique e saiba mais: Formas Pagamento"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>



                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box" id="geralnovopagamento">
                                    <table width="100%" border="0" cellpadding="0"
                                           cellspacing="0" class="tablepadding2 textsmall"
                                           style="margin-bottom: 25px;">
                                        <tr>
                                            <td align="left" valign="top" style="padding: 0 10px 10px 0;">
                                                <!-- inicio item -->
                                                <h:panelGroup
                                                        rendered="#{!MovPagamentoControle.movPagamentoVO.usarPagamentoAprovaFacil}">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                                           class="tablepadding2">
                                                        <tr>
                                                            <td align="left" valign="top">

                                                                <h:panelGroup id="panelDataPagto"
                                                                              styleClass="detalhesPagamento">
                                                                    <h:panelGroup style="text-align: left"
                                                                                  rendered="#{MovPagamentoControle.dtLancamentoContrato != null}">
                                                                        <%--<tr>--%>
                                                                        <%--<td align="left" valign="top">--%>
                                                                        <div style="" class="text">
                                                                            <h:outputText styleClass="mensagemDetalhada"
                                                                                          value="#{MovPagamentoControle.mensagemAlerta}"/>
                                                                        </div>
                                                                        <%--</td>--%>
                                                                        <%--</tr>--%>
                                                                    </h:panelGroup>

                                                                    <h:panelGroup id="innerPanelDP" layout="block"
                                                                                  styleClass="bloco"
                                                                                  rendered="#{MovPagamentoControle.apresentarAlteracaoDataBasePagtoContrato}">
                                                                        <span class="cinza negrito upper flex">Data:</span>
                                                                        <div class="dados">
                                                                            <h:outputText styleClass="cinza negrito"
                                                                                          id="dtPagamentoPg"
                                                                                          style="font: 12pt"
                                                                                          value="#{MovPagamentoControle.dataPagto_Apresentar}"/>
                                                                            <rich:spacer width="5px"/>
                                                                            <a4j:commandLink
                                                                                    title="Alterar a Data do Pagamento."
                                                                                    reRender="panelConfirmarAlteracao"
                                                                                    oncomplete="Richfaces.showModalPanel('panelConfirmarAlteracao')">
                                                                                <i class="fa-icon-edit"></i>
                                                                            </a4j:commandLink>
                                                                            <rich:spacer width="5px"/>
                                                                            <a4j:commandLink
                                                                                    action="#{MovPagamentoControle.limparDataPagto}"
                                                                                    title="Limpar Data de Pagamento"
                                                                                    reRender="panelDataPagto">
                                                                                <i class="fa-icon-eraser"></i>
                                                                            </a4j:commandLink>
                                                                        </div>

                                                                    </h:panelGroup>

                                                                    <h:panelGroup layout="block"
                                                                                  styleClass="bloco">
                                                                        <span class="cinza negrito upper flex">Pagador:</span>
                                                                        <div class="dados">
                                                                            <a4j:commandLink
                                                                                    rendered="#{MovPagamentoControle.apresentarlinkCliente && LoginControle.permissaoAcessoMenuVO.cliente}"
                                                                                    action="#{ClienteControle.abrirDaTelaPagamento}"
                                                                                    oncomplete="#{ClienteControle.onCompleteTelaPagamento}">
                                                                                <h:outputText
                                                                                        id="nomeResponsavelPagamentolink"
                                                                                        styleClass="cinza negrito"
                                                                                        value="#{MovPagamentoControle.nomePagador}"/>
                                                                            </a4j:commandLink>
                                                                            <h:outputText
                                                                                    rendered="#{!MovPagamentoControle.apresentarlinkCliente || !LoginControle.permissaoAcessoMenuVO.cliente}"
                                                                                    id="nomeResponsavelPagamento"
                                                                                    styleClass="cinza negrito"
                                                                                    value="#{MovPagamentoControle.nomePagador}"/>
                                                                        </div>
                                                                    </h:panelGroup>
                                                                    <h:panelGroup layout="block" style="float: right"
                                                                                  styleClass="bloco">
                                                                        <h:panelGroup id="panelCobrancaMultaJuros"
                                                                                      layout="block"
                                                                                      style="display: inline-block; margin-right: 30px;">

                                                                            <rich:dataTable id="tabMultaJuros"
                                                                                            headerClass="consulta"
                                                                                            rowClasses="linhaImpar, linhaPar"
                                                                                            columnClasses="colunaCentralizada,colunaCentralizada"
                                                                                            rendered="#{MovPagamentoControle.valorMultaJuros != 0}"
                                                                                            value="" rows="10" var="">

                                                                                <rich:column>
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                value="Multa/Juros"/>
                                                                                    </f:facet>

                                                                                    <h:outputText id="valorMultaJuros"
                                                                                                  value="#{MovPagamentoControle.valorMultaJuros}">
                                                                                        <f:converter
                                                                                                converterId="FormatadorNumerico"/>
                                                                                    </h:outputText>
                                                                                </rich:column>

                                                                                <rich:column>
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                value="O que deseja fazer?"/>
                                                                                    </f:facet>

                                                                                <h:selectOneRadio id="opcaoCobrarMulta"
                                                                                                  styleClass="textsmall"
                                                                                                  value="#{MovPagamentoControle.opcaoCobrarMulta}">
                                                                                    <a4j:support event="onchange"
                                                                                                 action="#{MovPagamentoControle.validarAutorizacaoIsentarMulta}"
                                                                                                 oncomplete="#{MovPagamentoControle.mensagemNotificar};#{MovPagamentoControle.msgAlert}"
                                                                                                 reRender="panelAutorizacaoFuncionalidade,panelEsconderControles,panelCobrancaMultaJuros, panelSaldoContaCorrente,
                                                                                                 escolhaFormaPagamento,escolhaFormaPagamentoCC,totalLancado,
                                                                                                 residuo,mensagemInf,mensagemSup,panelBotoesControle,
                                                                                                 formCheque,formAutorizacao,escolhaFormaPagamentoCC,panelAutorizacaoFuncionalidade"/>
                                                                                        <f:selectItems
                                                                                                value="#{MovPagamentoControle.listaSelectItemCobrarNaoCobrarMulta}"/>
                                                                                    </h:selectOneRadio>
                                                                                </rich:column>

                                                                            </rich:dataTable>
                                                                        </h:panelGroup>

                                                                        <h:panelGroup id="panelSaldoContaCorrente"
                                                                                      layout="block"
                                                                                      style="display: inline-block;">

                                                                            <rich:dataTable id="tabContaCorrente"
                                                                                            headerClass="consulta"
                                                                                            rowClasses="linhaImpar, linhaPar"
                                                                                            columnClasses="colunaCentralizada,colunaCentralizada"
                                                                                            rendered="#{MovPagamentoControle.movContaCorrenteCliente.saldoAtual != 0 &&
                                                                                                    !MovPagamentoControle.apresentarBotaoRecibo &&
                                                                                                    MovPagamentoControle.movContaCorrenteCliente.apresentarPanelSaldoContaCorrente}"
                                                                                            value="" rows="10" var="">
                                                                                <rich:column>
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                value="Saldo Conta Corrente"/>
                                                                                    </f:facet>

                                                                                    <h:panelGroup
                                                                                            rendered="#{MovPagamentoControle.tipoOperacaoContaCorrenteEnum.id == 'RE'}">
                                                                                        <h:outputText id="saldoCC1"
                                                                                                      value="#{MovPagamentoControle.movContaCorrenteCliente.saldoAtual }">
                                                                                            <f:converter
                                                                                                    converterId="FormatadorNumerico"/>
                                                                                        </h:outputText>
                                                                                    </h:panelGroup>
                                                                                    <h:panelGroup
                                                                                            rendered="#{MovPagamentoControle.tipoOperacaoContaCorrenteEnum.id == 'DE'}">
                                                                                        <h:outputText id="saldoCC2"
                                                                                                      value="#{MovPagamentoControle.movContaCorrenteCliente.saldoAtual}">
                                                                                            <f:converter
                                                                                                    converterId="FormatadorNumerico"/>
                                                                                        </h:outputText>
                                                                                    </h:panelGroup>
                                                                                    <h:panelGroup
                                                                                            rendered="#{MovPagamentoControle.tipoOperacaoContaCorrenteEnum.id == 'NA'}">
                                                                                        <h:outputText id="saldoCC3"
                                                                                                      value="#{MovPagamentoControle.movContaCorrenteCliente.saldoAtual}">
                                                                                            <f:converter
                                                                                                    converterId="FormatadorNumerico"/>
                                                                                        </h:outputText>
                                                                                    </h:panelGroup>

                                                                                </rich:column>

                                                                                <rich:column>
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                value="O que deseja fazer?"/>
                                                                                    </f:facet>

                                                                                    <h:selectOneRadio id="selecCC"
                                                                                                      styleClass="textsmall"
                                                                                                      value="#{MovPagamentoControle.tipoOperacaoContaCorrenteEnum}">
                                                                                        <a4j:support
                                                                                            action="#{MovPagamentoControle.verificarAutorizacaoUtilizarCredito}"
                                                                                            oncomplete="#{MovPagamentoControle.mensagemNotificar}"
                                                                                                event="onchange"
                                                                                            reRender="panelAutorizacaoFuncionalidade, panelEsconderControles, panelCobrancaMultaJuros, panelSaldoContaCorrente,
                                                                                        escolhaFormaPagamento,escolhaFormaPagamentoCC,totalLancado,
                                                                                        residuo,mensagemInf, mensagemSup,panelBotoesControle,panelEsconderControles3,
                                                                                        formCheque,formAutorizacao"/>
                                                                                        <f:selectItems
                                                                                                value="#{MovPagamentoControle.listaSelectItemReceberOuDevolver}"/>
                                                                                    </h:selectOneRadio>
                                                                                </rich:column>

                                                                                <rich:column width="20px"
                                                                                             rendered="#{MovPagamentoControle.tipoOperacaoContaCorrenteEnum.id != 'NA'}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                value="Valor #{MovPagamentoControle.empresaLogado.moeda}"/>
                                                                                    </f:facet>
                                                                                    <h:inputText
                                                                                            id="valorOperacaoContaCorrente"
                                                                                            value="#{MovPagamentoControle.movPagamentoVO.valorReceberOuDevolverContaCorrente}"
                                                                                            style="text-align:right;"
                                                                                            validator="#{MovPagamentoControle.validarOperacaoContaCorrente}"
                                                                                            onblur="blurinput(this);"
                                                                                            onfocus="focusinput(this);"
                                                                                            styleClass="form">
                                                                                        <a4j:support event="onchange"
                                                                                                     action="#{MovPagamentoControle.calcularEncapsulado}"
                                                                                                     reRender="panelEsconderControles,panelCobrancaMultaJuros, panelSaldoContaCorrente,
                                                                                                 escolhaFormaPagamento,escolhaFormaPagamentoCC,totalLancado,
                                                                                                 residuo,mensagemInf,mensagemSup,panelBotoesControle,
                                                                                                 formCheque,formAutorizacao,escolhaFormaPagamentoCC,tabContaCorrente,valorOperacaoContaCorrente"/>
                                                                                        <f:converter
                                                                                                converterId="FormatadorNumerico"/>
                                                                                    </h:inputText>
                                                                                    <h:message
                                                                                            styleClass="mensagemDetalhada"
                                                                                            id="valorOperacaoContaCorrenteError"
                                                                                            for="valorOperacaoContaCorrente"/>
                                                                                </rich:column>

                                                                            </rich:dataTable>
                                                                        </h:panelGroup>
                                                                    </h:panelGroup>

                                                                </h:panelGroup>
                                                                <pre id="resposta"
                                                                     style="position: static; text-align: right;"></pre>
                                                            </td>
                                                        </tr>


                                                        <tr>
                                                            <td align="left" valign="top"><h:panelGroup
                                                                    layout="block" id="panelEsconderControles"
                                                                    style="margin-right:0px">
                                                                <h:panelGroup layout="block" id="escolhaFormaPagamento"
                                                                              rendered="#{MovPagamentoControle.apresentarFormasPagamento}">
                                                                    <jsp:include page="include_opcoes_pagamento.jsp"
                                                                                 flush="true"/>
                                                                    <rich:jQuery id="mskData"
                                                                                 selector=".rich-calendar-input"
                                                                                 timing="onload"
                                                                                 query="mask('99/99/9999')"/>
                                                                </h:panelGroup>
                                                            </h:panelGroup></td>
                                                        </tr>
                                                    </table>
                                                </h:panelGroup>
                                                <!-- fim item --> <!-- inicio item -->
                                                <!-- Inicio - Tabela que mostra o total e o residuo -->

                                                <h:panelGroup id="containerTotalizadores"
                                                              rendered="#{MovPagamentoControle.apresentarFormasPagamento}"
                                                              styleClass="container-totalizador" layout="block">

                                                    <div class="col-md-3" style="margin-top: 20px;">
                                                        <a class="linkAzul addObservacao"
                                                           onclick="jQuery('.caixaObservacao').show();jQuery('.addObservacao').hide();"
                                                           status="false">
                                                            <span style="font-size: 14px !important;">Adicionar observação</span>
                                                        </a>
                                                    </div>

                                                    <h:panelGroup layout="block" styleClass="col-md-3"
                                                                  style="display: inline-block; float: right"
                                                                  rendered="#{MovPagamentoControle.empresaLogado.usarParceiroFidelidade && MovPagamentoControle.totalPontosParceiroFidelidade != 0.0}">
                                                        <div class="box-totalizador" style="margin-left: 20px;">
                                                            <div class="titulo-totalizador" style="color: orangered;">
                                                                TOTAL PONTOS LANÇADO
                                                            </div>
                                                            <div class="corpo-totalizador">
                                                                <h:outputText id="pontos"
                                                                              value="#{MovPagamentoControle.totalPontosParceiroFidelidade_Apresentar}"/>
                                                                <br/>
                                                                <a4j:repeat
                                                                        value="#{MovPagamentoControle.listaPagamentosParceiroFidelidade}"
                                                                        var="categoria" rowKeyVar="pont">
                                                                    <br/>
                                                                    <h:outputText styleClass="cinza upper"
                                                                                  style="font-size: 12px;"
                                                                                  value="#{categoria.descricaoTotalPontos}"/>
                                                                </a4j:repeat>
                                                            </div>
                                                        </div>
                                                    </h:panelGroup>

                                                    <div class="col-md-3" style="display: inline-block; float: right">
                                                        <div class="box-totalizador" style="margin-right: 2px;">
                                                            <div class="titulo-totalizador" style="color: green;">Total
                                                                lançado
                                                            </div>
                                                            <div class="corpo-totalizador">
                                                                <h:outputText
                                                                        value="#{MovPagamentoControle.empresaLogado.moeda} "/>
                                                                <h:outputText id="totalLancado"
                                                                              value="#{MovPagamentoControle.totalLancado}">
                                                                    <f:converter converterId="FormatadorNumerico"/>
                                                                </h:outputText>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-3" style="display: inline-block; float: right">
                                                        <div class="box-totalizador" style="margin-right: 20px;">
                                                            <div class="titulo-totalizador" style="color: red;">
                                                                Resíduo
                                                            </div>
                                                            <div class="corpo-totalizador">
                                                                <h:outputText
                                                                        value="#{MovPagamentoControle.empresaLogado.moeda} "/>
                                                                <h:outputText id="residuo"
                                                                              value="#{MovPagamentoControle.valorResiduoBaseCalculo}">
                                                                    <f:converter converterId="FormatadorNumerico"/>
                                                                </h:outputText>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </h:panelGroup>


                                                <!-- Fim - Tabela que mostra o total e o residuo -->

                                                <h:panelGroup
                                                        layout="block" id="panelEsconderControles3"
                                                        style="margin-right:0px">

                                                    <br>

                                                    <h:panelGroup layout="block"
                                                                  styleClass="opcaoPagamento caixaObservacao"
                                                                  style="padding: 0px 0px 20px 20px; display: none;">
                                                    <span class="cinza negrito upper flex" style="font-size: 14px;">
                                                        <h:outputText
                                                                rendered="#{MovPagamentoControle.apresentarFormasPagamento}"
                                                                value="Observação:"/>
                                                    </span>
                                                        <h:inputTextarea
                                                                style="width: calc(100% - 20px); height: 100px;"
                                                                rendered="#{MovPagamentoControle.apresentarFormasPagamento}"
                                                                value="#{MovPagamentoControle.movPagamentoVO.observacao}"
                                                                onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                rows="5"
                                                                styleClass="inputTextClean"/>
                                                    </h:panelGroup>
                                                </h:panelGroup>


                                                <h:panelGroup layout="block" styleClass="formNovo"
                                                              rendered="#{MovPagamentoControle.movPagamentoVO.usarPagamentoAprovaFacil}">
                                                    <jsp:include page="include_pagamentocartaocredito.jsp"
                                                                 flush="true"/>
                                                </h:panelGroup>

                                                <h:panelGroup
                                                        rendered="#{MovPagamentoControle.movPagamentoVO.usarPagamentoDebitoOnline}">
                                                    <jsp:include page="include_pagamentoDebitoOnline.jsp" flush="true"/>
                                                </h:panelGroup>


                                                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                                       class="tablepadding2">
                                                    <tr>
                                                        <td align="left" valign="top">
                                                            <h:panelGrid
                                                                    rendered="#{MovPagamentoControle.apresentarFormasPagamento}">
                                                                <div class="sep" style="margin: 2px 0;"><img
                                                                        src="images/shim.gif"></div>
                                                            </h:panelGrid>
                                                        </td>
                                                    </tr>
                                                </table>

                                                    <%-- BOTÕES DE CONTROLE --%>
                                                <%@include file="includes/menus/include_menubotoesfixos_tela9.jsp" %>
                                                <h:panelGrid id="mensagemInf" columns="2" width="100%">
                                                    <c:if
                                                            test="${(not empty MovPagamentoControle.mensagemPagamento)
                                                            || (not empty MovPagamentoControle.mensagemDetalhada)
                                                            || (MovPagamentoControle.sucesso)
                                                            || (MovPagamentoControle.erro)}">
                                                        <h:panelGrid columns="1" width="100%">
                                                            <h:outputText id="msgVerdeConfirmacao1"
                                                                          styleClass="mensagemVerde"
                                                                          style="font-size: 16pt !important;"
                                                                          value="#{MovPagamentoControle.mensagemPagamento}"/>
                                                            <h:outputText id="msgPagDet1"
                                                                          styleClass="mensagemDetalhada"
                                                                          value="#{MovPagamentoControle.mensagemDetalhada}"/>
                                                        </h:panelGrid>
                                                        <c:if test="${MovPagamentoControle.pixConfirmado}">
                                                            <script>
                                                                window.scrollTo(0, 0);
                                                            </script>
                                                        </c:if>
                                                    </c:if>
                                                </h:panelGrid>
                                            </td>
                                        </tr>
                                    </table>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <c:choose>
                                <c:when test="${(not LoginControle.apresentarLinkZW) && LoginControle.apresentarLinkEstudio}">
                                    <jsp:include page="pages/estudio/includes/include_box_menulateral_sc.jsp"
                                                 flush="true"/>
                                </c:when>
                                <c:otherwise>
                                    <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                                </c:otherwise>
                            </c:choose>
                        </c:if>

                        <c:if test="${modulo eq 'centralEventos'}">
                            <%@include file="pages/ce/includes/include_box_menulateral.jsp" %>
                        </c:if>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>


    </h:form>

    <%--<%@include file="includes/cliente/include_modal_emailEnviadoSucesso.jsp" %>--%>

    <rich:modalPanel id="panelUsuarioSenha" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450"
                     height="250" onshow="document.getElementById('formUsuarioSenha:senha').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação do Envio de Dados"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkUsuarioSenha"/>
                <rich:componentControl for="panelUsuarioSenha" attachTo="hidelinkUsuarioSenha" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formUsuarioSenha">

            <input type="hidden" value="${modulo}" name="modulo"/>


            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup layout="block" styleClass="margin-box">
                    <h:panelGrid id="panelConfimSenhaAutorizacao" columns="2" width="75%" cellpadding="8"
                                 styleClass="font-size-Em-max">
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                        <h:outputText styleClass="rotuloCampos" value="CÓDIGO:"/>
                        <h:panelGroup>

                            <h:inputText id="codigoUsuario" size="3" maxlength="100" style="margin-left:5px"
                                         value="#{PagamentoCartaoCreditoControle.responsavelPagamento.codigo}">
                                <a4j:support event="onchange" focus="formUsuarioSenha:senha"
                                             oncomplete="#{PagamentoCartaoCreditoControle.mensagemNotificar};#{PagamentoCartaoCreditoControle.onComplete}"
                                             action="#{PagamentoCartaoCreditoControle.consultarResponsavel}"
                                             reRender="formUsuarioSenha"/>
                            </h:inputText>

                            <h:inputText id="autoCompleteHidden" size="3" maxlength="10"
                                         style="margin-left:6px;opacity:0;"
                                         value="#{PagamentoCartaoCreditoControle.responsavelPagamento.username}"/>
                        </h:panelGroup>

                        <h:outputText styleClass="rotuloCampos" value="USUÁRIO:"/>
                        <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" style="margin-left:6px"
                                      value="#{PagamentoCartaoCreditoControle.responsavelPagamento.username}"/>

                        <h:outputText styleClass="rotuloCampos" value="SENHA:"/>
                        <h:panelGroup>
                            <h:inputSecret autocomplete="off" id="senha" size="14" styleClass="inputTextClean"
                                           maxlength="64" style="margin-left:8px"
                                           value="#{PagamentoCartaoCreditoControle.responsavelPagamento.senha}"
                                           onkeypress="validarEnter(event,'formUsuarioSenha:loginCaixa')"/>
                        </h:panelGroup>

                    </h:panelGrid>
                </h:panelGroup>
                <h:outputText id="mensagemAutorizacaoFuncionalidade" styleClass="mensagemDetalhada"
                              value="#{PagamentoCartaoCreditoControle.mensagemDetalhada}"/>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandButton id="loginCaixa" value="#{msg_bt.btn_confirmar}"
                                       reRender="form,formUsuarioSenha"
                                       styleClass="botaoPrimario texto-size-14-real"
                                       action="#{PagamentoCartaoCreditoControle.pagar}"
                                       oncomplete="#{PagamentoCartaoCreditoControle.mensagemNotificar}#{PagamentoCartaoCreditoControle.onComplete};montaModulomenu();"/>
                </h:panelGroup>

            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>

    <h:panelGroup id="panelIncludeMensagem" layout="block">
        <rich:modalPanel id="panelContratoPrestacaoServico" domElementAttachment="parent"
                         rendered="#{MovPagamentoControle.apresentarPanelContratoPrestacaoServico}"
                         autosized="true" shadowOpacity="false" width="500" height="100">

            <f:facet name="header">
                <h:outputText value="Imprimir Contrato  de Prestação de Serviços" styleClass="titulo2"/>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <a4j:form>
                        <a4j:commandButton image="/imagens/close.png" style="cursor:pointer;"
                                           action="#{MovPagamentoControle.fecharPanelContratoPrestacaoServico}"
                                           oncomplete="#{rich:component('panelContratoPrestacaoServico')}.hide();"
                                           id="hidelink12"/>
                    </a4j:form>
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formContratoPrestacaoServico" ajaxSubmit="false">
                <h:outputText rendered="#{!empty MovPagamentoControle.listaSelectModelosContratoPrestacao}"
                              value="Escolha o modelo de contrato: " style="font-weight:bold;"/>
                <h:selectOneMenu rendered="#{!empty MovPagamentoControle.listaSelectModelosContratoPrestacao}"
                                 id="modeloContratoPrestacao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{MovPagamentoControle.textoContratoPrestacao.codigo}">
                    <f:selectItems value="#{MovPagamentoControle.listaSelectModelosContratoPrestacao}"/>
                </h:selectOneMenu>
                <rich:spacer width="5px"/>
                <a4j:commandButton style="vertical-align:middle" id="btnImprimirContratoPrestacao"
                                   rendered="#{!empty MovPagamentoControle.listaSelectModelosContratoPrestacao}"
                                   value="Imprimir Contrato" image="./imagens/imprimir.png"
                                   action="#{MovPagamentoControle.imprimirContratoServico}"
                                   reRender="form:panelMensagemSuperior,form:panelMensagemInferior"
                                   oncomplete="abrirPopup('faces/VisualizarContrato', 'RelatorioContrato', 730, 545);#{rich:component('panelContratoPrestacaoServico')}.hide();"/>
                <h:panelGrid rendered="#{empty MovPagamentoControle.listaSelectModelosContratoPrestacao}">
                    <br>
                    <h:outputText
                            value="Não existe Modelo de Contrato de Prestação de Serviço ativo. Cadastre um novo modelo ou Ative um existente para realizar essa impressão"
                            style="color:red;"/>

                </h:panelGrid>

            </a4j:form>
        </rich:modalPanel>


        <rich:modalPanel id="panelUsuarioSenhaDebito" styleClass="novaModal" autosized="true" shadowOpacity="true"
                         width="450" height="250"
                         onshow="document.getElementById('formUsuarioSenhaDebito:senhaDebito').focus()">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Confirmação do Envio de Dados"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                            id="hidelinkUsuarioSenhaDebito"/>
                    <rich:componentControl for="panelUsuarioSenhaDebito" attachTo="hidelinkUsuarioSenhaDebito"
                                           operation="hide"
                                           event="onclick"/>
                </h:panelGroup>
            </f:facet>
            <a4j:form id="formUsuarioSenhaDebito">

                <input type="hidden" value="${modulo}" name="modulo"/>


                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup layout="block" styleClass="margin-box" id="panelUsuarioCartaoDebitoOnline">
                        <h:panelGrid id="panelConfimSenhaAutorizacaoDebito" columns="2" width="75%" cellpadding="8"
                                     styleClass="font-size-Em-max">
                            <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                            <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                            <h:outputText styleClass="rotuloCampos" value="CÓDIGO:"/>
                            <h:panelGroup>

                                <h:inputText id="codigoUsuarioDebito" size="3" maxlength="100" style="margin-left:5px"
                                             value="#{PagamentoCartaoDebitoOnlineControle.responsavelPagamento.codigo}">
                                    <a4j:support event="onchange" focus="formUsuarioSenhaDebito:senhaDebito"
                                                 action="#{PagamentoCartaoDebitoOnlineControle.consultarResponsavel}"
                                                 reRender="formUsuarioSenhaDebito"/>
                                </h:inputText>

                                <h:inputText id="autoCompleteHiddenDebito" size="3" maxlength="10"
                                             style="margin-left:6px;opacity:0;"
                                             value="#{PagamentoCartaoDebitoOnlineControle.responsavelPagamento.username}"/>
                            </h:panelGroup>

                            <h:outputText styleClass="rotuloCampos" value="USUÁRIO:"/>
                            <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" style="margin-left:6px"
                                          value="#{PagamentoCartaoDebitoOnlineControle.responsavelPagamento.username}"/>

                            <h:outputText styleClass="rotuloCampos" value="SENHA:"/>
                            <h:panelGroup>
                                <h:inputSecret autocomplete="off" id="senhaDebito" size="14" styleClass="inputTextClean"
                                               maxlength="64" style="margin-left:8px"
                                               value="#{PagamentoCartaoDebitoOnlineControle.responsavelPagamento.senha}"
                                               onkeypress="validarEnter(event,'formUsuarioSenhaDebito:btnAutorizar')"/>
                            </h:panelGroup>

                        </h:panelGrid>
                    </h:panelGroup>
                    <h:outputText id="mensagemAutorizacaoFuncionalidadeDebito" styleClass="mensagemDetalhada"
                                  value="#{PagamentoCartaoDebitoOnlineControle.mensagemDetalhada}"/>
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandButton id="pagarDebitOnline" value="#{msg_bt.btn_confirmar}"
                                           reRender="form,formUsuarioSenhaDebito"
                                           styleClass="botaoPrimario texto-size-14-real"
                                           action="#{PagamentoCartaoDebitoOnlineControle.pagar}"
                                           oncomplete="Richfaces.hideModalPanel('panelUsuarioSenhaDebito');#{PagamentoCartaoDebitoOnlineControle.onComplete};montaModulomenu();"/>
                    </h:panelGroup>

                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>
    </h:panelGroup>
    <%@include file="includes/include_envio_contrato_email.jsp" %>

    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp"/>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>

    <script>

        function params() {
            var params = {};

            if (location.search) {
                var parts = location.search.substring(1).split('&');

                for (var i = 0; i < parts.length; i++) {
                    var nv = parts[i].split('=');
                    if (!nv[0])
                        continue;
                    params[nv[0]] = nv[1] || true;
                }
            }
            return params;

        }

        var parametros = params();
        if (parametros.retornoDebitoOnline) {
            document.getElementById('form:confirmarTransacaoDebitoOnline').click();
        }

        window.onload = function() {
            fazerCheckoutCappta();
        };

        function phoneNumberFormatModalPix(elementId) {
            let element = document.getElementById(elementId);
            let number = element.value.replace(/\D/g, ""); // Remove todos os caracteres não numéricos

            if (number) {
                let formattedNumber = "";
                if (number.length > 10) {
                    formattedNumber = number.replace(/^(\d\d)(\d{5})(\d{4}).*/, "($1) $2-$3");
                } else if (number.length > 5) {
                    formattedNumber = number.replace(/^(\d\d)(\d{4})(\d{0,4}).*/, "($1) $2-$3");
                } else if (number.length > 2) {
                    formattedNumber = number.replace(/^(\d\d)(\d{0,5})/, "($1) $2");
                } else {
                    formattedNumber = number.replace(/^(\d*)/, "($1");
                }

                let cursorPosition = element.selectionStart;
                let oldLength = element.value.length;
                element.value = formattedNumber;

                // Verifica se o cursor está antes de um caractere não numérico e ajusta a posição do cursor
                if (cursorPosition < oldLength && !/\d/.test(element.value[cursorPosition])) {
                    while (cursorPosition > 0 && !/\d/.test(element.value[cursorPosition])) {
                        cursorPosition--;
                    }
                }
            }
        }

        document.onkeydown = function () {
            switch (event.keyCode) {
                case 116 : //F5 button
                    console.log('bloqueia F5!');
                    event.returnValue = false;
                    event.keyCode = 0;
                    return false;
                case 82 : //R button
                    if (event.ctrlKey) {
                        event.returnValue = false;
                        event.keyCode = 0;
                        return false;
                    }
            }
        };
    </script>
</f:view>
