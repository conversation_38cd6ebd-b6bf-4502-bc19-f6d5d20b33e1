<!--
To change this template, choose Tools | Templates
and open the template in the editor.
-->
<!DOCTYPE html>
<html>
<head>
    <title>ZW Assinatura Digital</title>

    <script type="text/javascript" src="jquery.min.js"></script>
    <link href="../css/signature-pad/signature-pad.css" rel="stylesheet" type="text/css">
    <link href="../css/guilhotina/jquery.guillotine.css" rel="stylesheet" type="text/css">
    <link href="../beta/css/font-awesome.css" rel="stylesheet" type="text/css">
    <link href="canvasCrop.css" rel="stylesheet" type="text/css"/>
    <link href="assinaturas_v11.css?" rel="stylesheet" type="text/css">
    <link href="facial2.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <link rel="manifest" href="manifest.json">

    <script src="jquery.canvasCrop.js" type="text/javascript"></script>

    <link rel="icon" sizes="152x152" href="../imagens/assinaturadigital.png"/>

</head>
<body>
<style>
    .fa-icon-crop:before {
        content: "\f125";
    }

    .caixa.btn {
        width: 97vw;
        margin: 0 auto;
        padding: 0;
        margin-bottom: 2vh;
    }

    .preview.foto {
        max-width: 98% !important;
    }
</style>
<div id="painelmenu">
    <div class="caixaMenu" style="overflow-y: auto;">
        <header>
            <a class="voltar" onclick="closeMenu();">
                <i class="fa-icon-chevron-left"></i>
            </a>
            <span class="texto-header">
                        PAINEL DE AÇÕES
                    </span>
        </header>

        <div id="username" class="texto-header">

        </div>

        <div id="empresas" class="texto-header">
        </div>

        <span class="infoAssinatura texto-header"
              style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                VOCÊ PRECISA LOGAR PARA ACESSAR ESSA FUNCIONALIDADE
            </span>


    </div>
</div>

<header id="mainHeader">
    <span id="tituloHeader">
        SUA SESSÃO ESTÁ EXPIRADA
    </span>
</header>


<div id="main">
    <div  class="lblTitulo" style="margin-top: 15vh; text-align: center">
        <a href="https://app.pactosolucoes.com.br/">
            Por favor, clique aqui para fazer login no sistema e ter acesso a este recurso.
        </a>
    </div>
</div>

<div class="caixaConcluir" onclick="concluir();">
    <i class="fa-icon-ok-circle"></i>
    <span id="msgConcluir">
                
            </span>
    <a onclick="concluir();" class="ok">
        <div class="caixa btn" style="margin-left: 1.5vw">
            CONCLUIR
        </div>
    </a>
</div>

<div class="caixaAssinatura">
    <div style="position: absolute;
    top: 37%;
    width: 95%;
    text-align: center;
    font-weight: bold;
    color: #777777;
    font-size: 7vw;
    border-top: #777777 1px solid;
    margin-left: 2.5%;">
        Assine aqui
    </div>

    <div id="signature-pad" class="m-signature-pad">
        <div class="m-signature-pad--body">
            <canvas></canvas>
        </div>
        <div class="m-signature-pad--footer">

            <div class="right" style="display: none;">
                <button type="button" class="button clear" data-action="clear">Limpar assinatura</button>
            </div>
            <div class="right" style="display: none;">
                <button type="button" class="button save" data-action="save-png">Save as PNG</button>
                <button type="button" class="button save" data-action="save-svg">Save as SVG</button>
            </div>
        </div>
    </div>
    <div class="caixa assinaturaResponsavel lblTitulo" style="padding-top: 10px;">
        Assinatura do responsável
    </div>
    <a onclick="signaturePad.clear();" id="idbtnlimparassinatura">
        <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
            LIMPAR ASSINATURA
        </div>
    </a>
    <a onclick="prosseguirFotoPerfil();">
        <div class="caixa btn">
            PROSSEGUIR
        </div>
    </a>
</div>

<div class="caixaDocumentos">
            <span class="infoAssinatura"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                ANEXE OS DOCUMENTOS DE IDENTIFICAÇÃO E ENDEREÇO REFERENTES AO RESPONSÁVEL
            </span>

    <div class="labelPequena" style="margin-top: 5vh;">Documento de identificação <i class="fa-icon-remove"
                                                                                     title="Remover"
                                                                                     style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;"
                                                                                     onclick="limparDocumentos()"></i>
    </div>
    <img id="uploadPreview" class="preview" onclick="document.getElementById('uploadImage').click();"
         style="cursor: pointer;"/>
    <input id="uploadImage" type="file" name="myPhoto"
           onchange="PreviewImage('uploadImage', 'uploadPreview', 'docs', 800);"
           accept="image/*" style="display: none;"/>

    <div class="labelPequena">Comprovante de Endereço <i class="fa-icon-remove" title="Remover"
                                                         style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;"
                                                         onclick="limparEndereco()"></i></div>
    <img id="uploadPreviewEND" class="preview" onclick="document.getElementById('uploadImageEND').click();"
         style="cursor: pointer;"/>
    <input id="uploadImageEND" type="file" name="myPhoto"
           onchange="PreviewImage('uploadImageEND', 'uploadPreviewEND', 'endereco', 800);"
           accept="image/*" style="display: none;"/>

    <div class="labelPequena">Atestado de Aptidão Física <i class="fa-icon-remove" title="Remover"
                                                            style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;"
                                                            onclick="limparAtestado()"></i></div>
    <img id="uploadPreviewATESTADO" class="preview" onclick="document.getElementById('uploadImageATESTADO').click();"
         style="cursor: pointer;"/>
    <input id="uploadImageATESTADO" type="file" name="myPhoto"
           onchange="PreviewImage('uploadImageATESTADO', 'uploadPreviewATESTADO', 'atestado', 800);"
           accept="image/*" style="display: none;"/>

    <div class="labelPequena">Anexo 1 <i class="fa-icon-remove" title="Remover"
                                         style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;"
                                         onclick="limparAnexo1()"></i></div>
    <img id="uploadPreviewAnexo1" class="preview" onclick="document.getElementById('uploadImageAnexo1').click();"
         style="cursor: pointer;"/>
    <input id="uploadImageAnexo1" type="file" name="myPhoto"
           onchange="PreviewImage('uploadImageAnexo1', 'uploadPreviewAnexo1', 'anexo1', 800);"
           accept="image/*" style="display: none;"/>

    <div class="labelPequena">Anexo 2 <i class="fa-icon-remove" title="Remover"
                                         style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;"
                                         onclick="limparAnexo2()"></i></div>
    <img id="uploadPreviewAnexo2" class="preview" onclick="document.getElementById('uploadImageAnexo2').click();"
         style="cursor: pointer;"/>
    <input id="uploadImageAnexo2" type="file" name="myPhoto"
           onchange="PreviewImage('uploadImageAnexo2', 'uploadPreviewAnexo2', 'anexo2', 800);"
           accept="image/*" style="display: none;"/>

    <a onclick="prosseguirValidar();">
        <div class="caixa btn">
            PROSSEGUIR
        </div>
    </a>
</div>

<div class="caixaAtestado">
            <span class="infoAssinatura"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                ANEXE A IMAGEM DO ATESTADO DE APTIDÃO FÍSICA
            </span>

    <div class="labelPequena" id="idnomeparaatestado" style="text-transform: uppercase;"></div>
    <img id="uploadPreviewAtestado1" class="preview" src="../imagens/image_icon.jpg"
         onclick="document.getElementById('uploadImageAtestado1').click();"/>
    <input id="uploadImageAtestado1" type="file" name="myPhoto"
           onchange="PreviewImage('uploadImageAtestado1', 'uploadPreviewAtestado1', true);"
           accept="image/*" style="display: none; cursor: pointer;"/>

    <a onclick="prosseguirValidar();">
        <div class="caixa btn">
            CONCLUIR
        </div>
    </a>
</div>

<div class="caixaFoto">
            <span class="infoAssinatura" id="headerAluno"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                TIRE UMA FOTO PARA O PERFIL DO ALUNO NO ZW
            </span>
    <div class="labelPequena" id="idnomeparafoto" style="text-transform: uppercase;"></div>

    <div id="containerimagebox">
        <div class="imageBox">
            <div class="mask"></div>
            <div class="thumbBox"></div>
            <div class="spinner" style="display: none"></div>
        </div>
        <div style="width: 100%;">
            <div class="tools clearfix">
                <span id="rotateLeft"><i class="fa-icon-undo"></i></span>
                <span id="rotateRight"><i class="fa-icon-repeat"></i></span>
                <span id="zoomIn"><i class="fa-icon-zoom-out"></i></span>
                <span id="zoomOut"><i class="fa-icon-zoom-in"></i></span>
            </div>
        </div>
    </div>

    <div style="border: 2px #094771 solid;
    border-radius: 50%;
    width: 10vh;
    height: 10vh;
    color: #094771;
    line-height: 10vh;
    text-align: center;
    cursor: pointer;
    margin-left: calc(50% - 5vh);
    margin-top: 4vh;
    display: block;
    font-size: 4vh;"
         onclick="document.getElementById('upload-file').click();">
        <i class="fa-icon-camera"></i>
    </div>
    <input type="file" id="upload-file" accept="image/*" style="display: none;"/>


    <script type="text/javascript">
        var CanvasCrop;
        $(function () {
            var rot = 0, ratio = 1;
            CanvasCrop = $.CanvasCrop({
                cropBox: ".imageBox",
                imgSrc: "images/avatar.jpg",
                limitOver: 2
            });


            $('#upload-file').on('change', function () {
                fotoAlterada = true;
                var reader = new FileReader();
                reader.onload = function (e) {
                    CanvasCrop = $.CanvasCrop({
                        cropBox: ".imageBox",
                        imgSrc: e.target.result,
                        limitOver: 2
                    });
                    rot = 0;
                    ratio = 1;
                };

                var arquivo = this.files[0];
                reader.onloadend = function (e) {
                    getOrientation(arquivo, function (orientation) {
                        switch (orientation) {
                            case 6:
                                rot += 90;
                                rot = rot > 360 ? 90 : rot;
                                CanvasCrop.rotate(rot);
                                break;
                            case 8:
                                rot -= 90;
                                rot = rot < 0 ? 270 : rot;
                                CanvasCrop.rotate(rot);
                                break;
                        }
                    });
                };

                reader.readAsDataURL(this.files[0]);
                this.files = null;
            });

            $("#rotateLeft").on("click", function () {
                rot -= 90;
                rot = rot < 0 ? 270 : rot;
                CanvasCrop.rotate(rot);
                fotoAlterada = true;
            });
            $("#rotateRight").on("click", function () {
                rot += 90;
                rot = rot > 360 ? 90 : rot;
                CanvasCrop.rotate(rot);
                fotoAlterada = true;
            });
            $("#zoomIn").on("click", function () {
                ratio = ratio * 0.9;
                CanvasCrop.scale(ratio);
                fotoAlterada = true;
            });
            $("#zoomOut").on("click", function () {
                ratio = ratio * 1.1;
                CanvasCrop.scale(ratio);
                fotoAlterada = true;
            });
        });
    </script>
</div>

<div class="caixaFacial">

    <div style="text-align: right">

    </div>

    <span onclick="sairCapturaFacial();" class="infoAssinatura cabecalhofacial">
                <i class="fa-icon-caret-left"></i>
                VOLTAR
            </span>

    <div class="containervideo">

        <div class="amostra" style="">
            <img id="fotofacial1"/>
            <img id="fotofacial2"/>
        </div>

        <video autoplay class="hidden"></video>
    </div>

    <div class="containerbtsvideo">

        <a onclick="trocarCamera();"
           class="btnfacial virar">
            <i class="fa-icon-refresh"></i>
        </a>


        <a onclick="enviarFotoFacial();"
           class="btnfacial avancar">
            <i class="fa-icon-arrow-right"></i>
        </a>


        <div onclick="takePhoto()"
             class="bck-take">
            <div class="frt-take">
            </div>
        </div>
    </div>

    <!--            <a onclick="limparFotos()" class="limparfotos" style="position: relative; display: none">-->
    <!--                <div class="caixa btn" style="margin-top: 40px; background-color: #ededed; color: #373737;margin-bottom: 20px; ">-->
    <!--                    LIMPAR FOTOS-->
    <!--                </div>-->
    <!--            </a>-->


    <div class="caixaConcluir" id="msgfacial" style="position: fixed" onclick="$('#msgfacial').hide()">
        <i class="fa-icon-warning-sign"></i>
        <span id="msgFacialtxt"></span>

        <a onclick="$('#msgfacial').hide()" class="ok" style="display: block">
            <div class="caixa btn"
                 style="background-color: #ffffff; color: #094771; margin-top: 40px; margin-left: 1vw;">
                OK
            </div>
        </a>
    </div>
</div>

<a onclick="prosseguirDocumentos()" class="enviarFotoAssinando" style="position: relative; ">
    <div class="caixa btn" style="margin-top: 4vh; background-color: #ededed; color: #373737;">
        PULAR
    </div>
</a>
<a onclick="subirFoto();" class="caixaFoto" style="position: relative; width: 100%;  bottom: 2vh;">
    <div class="caixa btn concluir" style="margin-top: 4vh;">
        ENVIAR FOTO
    </div>
</a>

<div class="caixaValidarTermoResponsabilidade" style="text-align-last: center;">
    <table style="width: 100%;">
        <tr valign="top">
            <td class="caixaMenor2" style="text-align: left; margin-left: 15px; margin-right: 15px;">
                <div class="labelPequena">Assinatura</div>

                <div class="caixaPreview">
                    <img id="previewAssinaturaTermoResponsabilidade"
                         class="preview" style="height: auto;"/>
                </div>
            </td>
        </tr>
    </table>

    <a onclick="editar()" class="ok">
        <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
            REENVIAR ASSINATURA
        </div>
    </a>
    <a onclick="voltarInicio()" class="ok">
        <div class="caixa btn">
            OK
        </div>
    </a>
</div>


<div class="caixaValidar">
    <table style="width: 100%;">
        <tr valign="top">
            <td class="caixaMenor2" style="text-align: left; margin-left: 30px; margin-right: 10px;">
                <div class="labelPequena">Documento de identificação</div>
                <div class="caixaPreview">
                    <img id="previewDocs"
                         class="preview" onclick="abrirPreviewGrande(this)" style="cursor: pointer;"/>
                </div>
            </td>

            <td class="caixaMenor2" style="text-align: right; margin-left: 15px; margin-right: 15px;">
                <div class="labelPequena">Comprovante de Endereço</div>
                <div class="caixaPreview">
                    <img id="previewEndereco"
                         class="preview" onclick="abrirPreviewGrande(this)" style="cursor: pointer;"/>
                </div>
            </td>

            <td class="caixaMenor2" style="text-align: right; margin-left: 15px; margin-right: 15px;">
                <div class="labelPequena">Atestado</div>

                <div class="caixaPreview">
                    <img id="previewAtestado"
                         class="preview" onclick="abrirPreviewGrande(this)" style="cursor: pointer;"/>
                </div>
            </td>
        </tr>

        <tr valign="top">
            <td class="caixaMenor2" style="text-align: right; margin-left: 30px; margin-right: 10px;">
                <div class="labelPequena">Anexo 1</div>

                <div class="caixaPreview">
                    <img id="previewAnexo1"
                         class="preview" onclick="abrirPreviewGrande(this)" style="height: auto;"/>
                </div>
            </td>

            <td class="caixaMenor2" style="text-align: right; margin-left: 15px; margin-right: 15px;">
                <div class="labelPequena">Anexo 2</div>

                <div class="caixaPreview">
                    <img id="previewAnexo2"
                         class="preview" onclick="abrirPreviewGrande(this)" style="height: auto;"/>
                </div>
            </td>

            <td class="caixaMenor2" style="text-align: left; margin-left: 15px; margin-right: 15px;">
                <div class="labelPequena">Assinatura</div>

                <div class="caixaPreview">
                    <img id="previewAssinatura"
                         class="preview" style="height: auto;"/>
                    <button type="button" id="abrirModal" onclick="abrirModal()">Remover Assinatura</button>
                </div>
            </td>
        </tr>
    </table>

    <div id="permissaoNegadaRemoverAssinatura" class="modal">
        <div class="modal-content">
            <h3 class="lblTitulo">Você não tem permissão para remover a assinatura.</h3>
            <h3 style="text-align: center">Permissão (9.70)</h3>
            <div class="modal-options" style="justify-content: center">
                <button type="button" style="margin-top: 30px" class="buttonFechar" onclick="fecharModal()">Fechar
                </button>
            </div>
        </div>
    </div>

    <div id="modalAssinatura" class="modal">
        <div class="modal-content">
            <h3 class="lblTitulo">Deseja remover a assinatura?</h3>
            <div class="modal-options">
                <button type="button" class="buttonRemover" onclick="removerAssinatura(this)">Sim</button>
                <button type="button" class="buttonFechar" onclick="fecharModal()">Não</button>
            </div>
        </div>
    </div>
    <a onclick="validar();" id="concluirValidar">
        <div class="caixa btn">
            VALIDAR
        </div>
    </a>
    <a onclick="editar()" class="ok">
        <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
            REENVIAR DOCUMENTOS
        </div>
    </a>
    <a onclick="voltarInicio()" class="ok">
        <div class="caixa btn">
            OK
        </div>
    </a>
</div>
<div class="caixaCartaoVacina">
            <span class="infoAssinatura"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                ANEXE O CARTÃO DE VACINAS
            </span>

    <div class="labelPequena" id="idnomeparacartao" style="text-transform: uppercase;"></div>
    <img style="margin-bottom: 10px;" id="uploadPreviewAnexo1Cartao" class="preview" src="../imagens/image_icon.jpg"
         onclick="document.getElementById('uploadImageAnexo1Cartao').click();"/>
    <input id="uploadImageAnexo1Cartao" type="file" name="myPhoto"
           onchange="PreviewImage('uploadImageAnexo1Cartao', 'uploadPreviewAnexo1Cartao', 'cartaovacina', 800);"
           accept="image/*" style="display: none; cursor: pointer;"/>
    <div class="labelPequena">Cartão de vacina <i class="fa-icon-remove" title="Remover"
                                                  style="font-size: 24px; cursor: pointer; margin: 0px 0px 0px 8px;"
                                                  onclick="limparAnexo1Cartao()"></i></div>
    <div class="labelPequena">
        <label style="margin: 20px;" class="selectPequeno">
            <input style="margin: 4px;border: 1px solid #B4B7BB;" type="checkbox" class="radio" value="1"
                   name="tipoanexo" onclick="selecionarTipoAnexo(this)"/>1ª dose</label>
        <label style="margin: 20px;" class="selectPequeno">
            <input style="margin: 4px;border: 1px solid #B4B7BB;" type="checkbox" class="radio" value="2"
                   name="tipoanexo" onclick="selecionarTipoAnexo(this)"/>2ª dose / Dose única</label>

    </div>

    <a onclick="prosseguirValidar();">
        <div class="caixa btn">
            PROSSEGIR
        </div>
    </a>
    <div class="caixaConcluir" id="msgcartao" style="position: fixed" onclick="$('#msgcartao').hide()">
        <i class="fa-icon-warning-sign"></i>
        <span id="msgcartaotxt"></span>

        <a onclick="$('#msgcartao').hide()" class="ok" style="display: block">
            <div class="caixa btn"
                 style="background-color: #ffffff; color: #094771; margin-top: 40px; margin-left: 1vw;">
                OK
            </div>
        </a>
    </div>

</div>

<div class="caixaCartaoVisualizar">
    <table style="width: 100%;  text-align: center; margin-bottom: 10px;">
        <div class="labelPequena" id="idnomeparacartao1" style="text-transform: uppercase;"></div>
        <tr valign="top">
            <td class="caixaMenor2" style="text-align: left; margin-left: 30px; margin-right: 10px;">
                <div class="labelPequena">Cartão de Vacina</div>
                <div class="caixaPreview">
                    <img id="previewCartao"
                         class="preview" onclick="abrirPreviewGrande(this)" style="cursor: pointer;"/></div>
            </td>
        </tr>
    </table>

    <div class="labelPequena">
        <label style="margin: 20px;" class="selectPequeno">
            <input disabled="true" style="margin: 4px;border: 1px solid #B4B7BB;" type="checkbox" class="radio"
                   value="1" name="tipoanexo1"/>1ª dose</label>
        <label style="margin: 20px;" class="selectPequeno">
            <input disabled="true" style="margin: 4px;border: 1px solid #B4B7BB;" type="checkbox" class="radio"
                   value="2" name="tipoanexo1"/>2ª dose / Dose única</label>

    </div>
    <a onclick="editarCartao()" class="ok">
        <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
            REENVIAR CARTÃO DE VACINA
        </div>
    </a>
    <a onclick="voltarInicio()" class="ok">
        <div class="caixa btn">
            VOLTAR
        </div>
    </a>
</div>

<div class="formularioParQ">
            <span id="titleFormularioParQ" class="infoAssinatura"
                  style="width: calc(100% - 8vh); text-align: center; font-weight: bold; font-style: normal; padding: 4vh; font-size: 2vh;">
                Preencha o formulário Par-Q
            </span>

    <div class="labelPequena" id="idParQ" style="text-transform: uppercase;"></div>

    <div id="perguntasParQ"></div>

    <div class="termoDeAceite">
        <div class="titleTermoAceite">
<!--            Lei Estadual 6.725/2014 - Ticket: TW-470-->
            TERMO DE RESPONSABILIDADE PARA PRÁTICA DE ATIVIDADE FÍSICA
        </div>
        <div class="textoTermoAceite">
            <input type="checkbox" id="checkTermoDeAceite" name="termoDeAceite">
            <label id="texto_obrigatorio_termo" for="checkTermoDeAceite">
                Declaro que estou ciente de que é recomendável conversar com um médico, antes de iniciar ou aumentar o nível
                de atividade física pretendido, assumindo plena responsabilidade pela realização de qualquer atividade física
                sem o atendimento desta recomendação.
            </label>
        </div>
    </div>
</div>

<div class="caixaAssinaturaTermoResponsabilidade">
    <div class="assine-aqui">
        Assine aqui
    </div>

    <div id="signature-pad-termoresponsabilidade" class="m-signature-pad">
        <div class="m-signature-pad--body">
            <canvas></canvas>
        </div>
        <div class="m-signature-pad--footer">

            <div class="right" style="display: none;">
                <button type="button" class="button clear" data-action="clear">Limpar assinatura</button>
            </div>
            <div class="right" style="display: none;">
                <button type="button" class="button save" data-action="save-png">Save as PNG</button>
                <button type="button" class="button save" data-action="save-svg">Save as SVG</button>
            </div>
        </div>
    </div>
    <div class="caixa assinaturaResponsavel lblTitulo" style="padding-top: 10px;">
        Assinatura do responsável
    </div>
    <a onclick="signaturePad.clear();" id="idbtnlimparassinaturatermoresponsabilidade">
        <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
            LIMPAR ASSINATURA
        </div>
    </a>
    <a onclick="prosseguirValidar();">
        <div class="caixa btn">
            PROSSEGUIR
        </div>
    </a>
</div>

<div class="caixaAssinaturaParQ">
    <div class="assine-aqui">
        Assine aqui
    </div>

    <div id="signature-pad-parq" class="m-signature-pad">
        <div class="m-signature-pad--body">
            <canvas></canvas>
        </div>
        <div class="">

            <div class="right" style="display: none;">
                <button type="button" class="button clear" data-action="clear">Limpar assinatura</button>
            </div>
            <div class="right" style="display: none;m-signature-pad--footer">
                <button type="button" class="button save" data-action="save-png">Save as PNG</button>
                <button type="button" class="button save" data-action="save-svg">Save as SVG</button>
            </div>
        </div>
    </div>
    <div class="caixa assinaturaResponsavel lblTitulo" style="padding-top: 10px;">
        Assinatura do responsável
    </div>
    <a onclick="signaturePad.clear();" id="idbtnlimparassinaturaparq">
        <div class="caixa btn" style="margin-bottom: 2vh; background-color: #ededed; color: #373737;">
            LIMPAR ASSINATURA
        </div>
    </a>
    <a onclick="prosseguirValidar();">
        <div class="caixa btn">
            PROSSEGUIR
        </div>
    </a>
</div>

<div class="previewGrande" onclick="fecharPreviewGrande();" style="z-index: 999; left: 0;">
    <img id="idpreviewGrande" class="preview" style="margin-top: 10vh; width: 70%; margin-left: 15%;"/>
</div>

<div class="spinnerCarregando" id="loadingDiv">
    <div class="bounce1"></div>
    <div class="bounce2"></div>
    <div class="bounce3"></div>
</div>
<script type="text/javascript" src="../script/signature-pad/signature_pad.js"></script>
<script type="text/javascript" src="../script/signature-pad/app.js"></script>
<script type="text/javascript" src="../script/guilhotina/jquery.guillotine.min.js"></script>
<script type="text/javascript" src="../script/guilhotina/html2canvas.js"></script>
<script type="text/javascript" charset="ISO-8859-1" src="assinaturas_v56.js"></script>
<script type="text/javascript" src="facial.js"></script>
<script type="text/javascript" src="imageCapture3.js"></script>

<canvas class="pseudocanvas" id="resize_canvas"></canvas>
<canvas class="pseudocanvas" id="crop_canvas"></canvas>
<canvas class="pseudocanvas" id="rotation_canvas"></canvas>
<canvas class="pseudocanvas" id="canvasprofile"></canvas>

<img id="pseudoprofile" crossOrigin="Anonymous" style="display: none;"/>
</body>

</html>
