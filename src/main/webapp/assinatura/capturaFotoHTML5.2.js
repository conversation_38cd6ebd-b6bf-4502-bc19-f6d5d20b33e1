var initialized = false;

function clearElement(elem) {
    while (elem.firstChild) {
        elem.removeChild(elem.firstChild);
    }
}

function takeSnapshot() {
    var video = document.getElementById('webcamVideo');
    var canvas = document.getElementById('canvas_img');
    var ctx = canvas.getContext('2d');

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    ctx.save();
    ctx.translate(canvas.width, 0);
    ctx.scale(-1, 1);
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    ctx.restore();

    var imgSrc = canvas.toDataURL("image/png");

    showImageCanvas(imgSrc);

    // Esconder o modal após a captura
    var modal = document.getElementById('modalCapturaFoto');
    modal.style.display = 'none';
}

function showImageCanvas(imgSrc) {
    hideWebcamTab(); // Oculta a aba da webcam (se necessário)

    var imageDiv = document.getElementById('imageDiv');

    imageDiv.style.display = "block";

    // Configurar a imagem no canvas
    var canvasImg = document.getElementById("canvas_img");
    var ctx = canvasImg.getContext("2d");
    var originalImage = new Image();

    originalImage.onload = function () {
        canvasImg.width = originalImage.width;
        canvasImg.height = originalImage.height;
        ctx.clearRect(0, 0, canvasImg.width, canvasImg.height);
        ctx.drawImage(originalImage, 0, 0, canvasImg.width, canvasImg.height);

        CanvasCrop = $.CanvasCrop({
            cropBox: ".imageBox",
            imgSrc: imgSrc,
            limitOver: 2
        });
    };
    fotoAlterada = true;
    originalImage.src = imgSrc;
}

function hideWebcamTab() {
    var capturaFotoDiv = document.getElementById("capturaFotoDiv");
    var webcamContainer = document.getElementById('video_container');
    var loadingWebcam = document.getElementById("loading_webcam");
    var loadingWebcamLbl = document.getElementById("loading_webcam_lbl");

    // Parar streams de vídeo
    stopAllVideoStreams();

    // Escondendo os elementos
    loadingWebcam.style.display = "none";
    loadingWebcamLbl.style.display = "none";
    webcamContainer.style.display = "none";
    clearElement(webcamContainer);
    capturaFotoDiv.style.display = "none";
}

function showWebcamTab() {
    // hideImageCanvas();
    var capturaFotoDiv = document.getElementById("capturaFotoDiv");
    var webcamContainer = document.getElementById('video_container');
    var modalCaptura = document.getElementById('modalCapturaFoto');
    var snapBtn = document.getElementById('snapshot_btn');

    webcamContainer.style.display = "block";
    capturaFotoDiv.style.display = "block";
    modalCaptura.style.display = 'block';
    snapBtn.style.display = "none"; // Será mostrado quando o vídeo estiver pronto

    var loadingWebcam = document.getElementById("loading_webcam");
    var loadingWebcamLbl = document.getElementById("loading_webcam_lbl");
    loadingWebcam.style.display = "block";
    loadingWebcamLbl.style.display = "block";

    // Centralizando o label de loading
    loadingWebcamLbl.style.left = "calc(50% - " + (loadingWebcamLbl.clientWidth / 2) + "px)";
    loadingWebcamLbl.style.top = "calc(50% + " + ((loadingWebcam.clientHeight * 1.5) / 2 + 35) + "px)";
    loadingWebcam.style.left = "calc(50% - " + (loadingWebcam.clientWidth / 2) + "px)";
    loadingWebcam.style.top = "calc(50% - " + (loadingWebcam.clientHeight / 2) + "px)";

    setupCameraByFacingMode(currentFacingMode);
}

function capturarFoto() {
    const modal = document.getElementById('modalCapturaFoto');
    initializeModalCapFoto();
    modal.style.display = 'block';
}

function initializeModalCapFoto() {
    var snapBtn = document.getElementById('snapshot_btn');

    snapBtn.removeEventListener("click", takeSnapshot);
    snapBtn.addEventListener("click", takeSnapshot);

    showWebcamTab();
}

document.getElementById("closeModalBtn").addEventListener("click", function(e) {
    e.preventDefault();
    document.getElementById("modalCapturaFoto").style.display = "none";
    hideWebcamTab();
});

function stopAllVideoStreams() {
    if (currentStream) {
        currentStream.getTracks().forEach(track => {
            track.stop();
        });
        currentStream = null;
    }
}

let currentStream = null;

async function setupCameraByFacingMode(facingMode = "user") {
    try {
        stopAllVideoStreams();

        const loadingWebcam = document.getElementById("loading_webcam");
        const loadingWebcamLbl = document.getElementById("loading_webcam_lbl");
        loadingWebcam.style.display = "block";
        loadingWebcamLbl.style.display = "block";

        const container = document.getElementById('video_container');
        clearElement(container);

        const video = document.createElement('video');
        video.id = 'webcamVideo';
        video.autoplay = true;
        video.playsInline = true;
        container.appendChild(video);

        try {
            constraints = {
                video: {
                    facingMode: { exact: facingMode },
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            };
        } catch (e) {
            constraints = {
                video: {
                    facingMode: { ideal: facingMode },
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            };
        }

        currentStream = await navigator.mediaDevices.getUserMedia(constraints);
        video.srcObject = currentStream;

        video.onloadedmetadata = () => {
            loadingWebcam.style.display = "none";
            loadingWebcamLbl.style.display = "none";
            document.getElementById('snapshot_btn').style.display = "inline-block";
        };

        // Espelhar apenas a câmera frontal
        video.style.transform = (facingMode === "user") ? "scaleX(-1)" : "scaleX(1)";
    } catch (error) {
        console.error('Erro ao configurar câmera:', error);
        showCameraError(error);
    }
}

let currentFacingMode = "environment";

document.getElementById("toggle_camera_btn").addEventListener("click", function () {
    currentFacingMode = currentFacingMode === "user" ? "environment" : "user";
    setupCameraByFacingMode(currentFacingMode);
});