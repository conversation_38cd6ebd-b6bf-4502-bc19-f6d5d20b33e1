<%@page pageEncoding="ISO-8859-1"%>
<%@include file="includes/imports.jsp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>


<style>
    .alinhar{
        vertical-align: middle !important;
    }

    .btn-experimente {
        box-shadow: 0 2px 2px 0 rgba(169,169,169,.14), 0 3px 1px -2px rgba(169,169,169,.2), 0 1px 5px 0 rgba(169,169,169,.12);
        transition: .2s ease-in;
        background-color: #fff !important;
        color: #67757c !important;
        border-color: #b1b8bb !important;
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        border: 1px solid transparent;
        transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
        background: #fff !important;
        padding: 7px 12px !important;
        /*font-size: 15px !important;*/
        line-height: 1.5;
        border-radius: 5px;
        font-family: "Nunito Sans",sans-serif !important;
    }

    .btn-experimente:hover {
        box-shadow: 0 14px 26px -12px rgba(169,169,169,.42), 0 4px 23px 0 rgba(0,0,0,.12), 0 8px 10px -5px rgba(169,169,169,.2);
        color: #212529 !important;
        background-color: #b3b2b2 !important;
        border-color: #acacac !important;
    }

    .div-geral-experimente {
        display: grid;
        padding: 1.5% 0 0 1.5%;
        width: 96%;
        grid-template-columns: 2fr 0fr;
    }

    .div-experimente2 {
        justify-content: end;
        display: flex;
        padding-left: 15px;
    }

    .div-experimente {
        background-color: #fff !important;
        border-radius: 5px;
        font-family: "Nunito Sans",sans-serif !important;
        padding: 10px;
        align-items: center;
        display: flex;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 30px;
        height: 17px;
    }

    /* Hide default HTML checkbox */
    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    /* The slider */
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: #2196F3;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #2196F3;
    }

    input:checked + .slider:before {
        -webkit-transform: translateX(13px);
        -ms-transform: translateX(13px);
        transform: translateX(13px);
    }

    /* Rounded sliders */
    .slider.round {
        border-radius: 17px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .padrao-cliente {
        display: flex;
        color: #797D86;
        font-size: 16px;
        font-weight: 400;
        line-height: 16px;
        padding: 40px 0px 0 40px;
        width: calc(100% - 80px);
        justify-content: flex-end;
    }

    .padrao-cliente label {
        margin-left: 8px;
    }

    .padrao-cliente .clicavel {
        cursor: pointer;
    }

    .textoAtencaoNovaVersao {
        color: #AF0404;
        font-family: "Nunito Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        padding-right: 5px;
    }
    .textoSaibaMaisNovaVersao {
        color: #1E60FA;
        font-family: "Nunito Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        padding-left: 5px;
    }
    .textoNovaVersao {
        color: #55585E;
        font-family: "Nunito Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
    }
    .labelHoraNovaVersaoData {
        color: #AF0404;
        font-family: "Nunito Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
    }
    .labelNovaVersaoData {
        color: #494B50;
        font-family: "Nunito Sans";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    
    <head>
        <link rel="shortcut icon" href="${root}/favicon.ico" >
        <title><h:outputText value="#{msg_aplic.prt_Cliente_tituloForm}" /></title>
        <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
        <script src="./beta/js/DT_bootstrap.js" type="text/javascript"></script>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    </head>

    <jsp:include page="include_head.jsp" flush="true" />

    <h:form id="form">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item5" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
        <script type="text/javascript">
                setDocumentCookie('popupsImportante', 'close',1);
        </script>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">

                            <h:panelGroup layout="block"
                                          rendered="#{!PreCadastroClienteControle.novaTelaIncluirClientePadraoEmpresa && LoginControle.apresentarModuloNovoTreino}">
                                            <span class="padrao-cliente"
                                                  id="div-switch-nova-versao">
                                                <span class="clicavel" onclick="abrirNovaTelaIncluirCliente()">Usar nova versão</span>
                                                <label class="switch clicavel" onclick="abrirNovaTelaIncluirCliente()">
                                                    <input type="checkbox"
                                                           id="switch-nova-versao">
                                                    <span class="slider round"></span>
                                                </label>
                                            </span>
                            </h:panelGroup>

                            <h:panelGroup layout="block"
                                          id="divContadorNovaVersaoTelaAluno"
                                          style="display: none">
                                <h:panelGroup layout="block"
                                              style="background: #FAFAFA; border-radius: 8px; display: grid; margin: 1.5% 0 0 1.5%; width: 96%;">

                                    <h:panelGroup layout="block" id="divGeralContadorNovaVersao"
                                                  styleClass="divGeralContadorNovaVersao" style="padding: 20px;">

                                        <h:panelGroup layout="block" id="divGeralContadorNovaVersaoSuperior"
                                                      styleClass="divGeralContadorNovaVersaoSuperior"
                                                      style="padding-bottom: 20px; display: flex;align-items: center;">
                                            <h:panelGroup layout="block">
                                                <h:graphicImage value="images/pct-alert-triangle-ds3.svg"
                                                                style="width: 32px; padding-right: 5px;"/>
                                            </h:panelGroup>
                                            <h:panelGroup layout="block" style="display: flex">
                                                <h:outputText styleClass="textoAtencaoNovaVersao" value="Atenção: "/>
                                                <h:outputText styleClass="textoNovaVersao" value="Esta tela será desligada em "/>
                                                <h:outputText id="novaVersaoTelaAlunoData" styleClass="textoNovaVersao" style="padding-left: 5px" value=""/>
                                                <h:outputText styleClass="textoNovaVersao" value=". Recomendamos que utilize a nova tela para obter familiaridade até o desligamento dessa. "/>
                                                <h:outputLink styleClass="textoSaibaMaisNovaVersao"
                                                              target="_blank"
                                                              value="https://pactosolucoes.com.br/ajuda/conhecimento/adm-sua-antiga-tela-do-perfil-do-aluno-sera-desativada/">
                                                    Saiba mais
                                                </h:outputLink>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" id="divGeralContadorNovaVersaoInferior"
                                                      styleClass="divGeralContadorNovaVersaoInferior"
                                                      style="justify-content: center;display: flex;">
                                            <h:panelGroup layout="block"
                                                          style="padding: 15px;display: grid;grid-template-columns: 1fr 1fr 1fr 1fr;text-align: center;width: 45%;border: 1px solid #E23661;border-radius: 8px">
                                                <h:panelGroup layout="block" style="display: grid">
                                                    <h:outputText id="qtdDiasNovaVersao"
                                                                  styleClass="labelHoraNovaVersaoData"
                                                                  value="00"/>
                                                    <h:outputText styleClass="labelNovaVersaoData" value="Dias"/>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block" style="display: grid; border-left: 1px solid #D7D8DB;">
                                                    <h:outputText id="qtdHorasNovaVersao"
                                                                  styleClass="labelHoraNovaVersaoData"
                                                                  value="00"/>
                                                    <h:outputText styleClass="labelNovaVersaoData" value="Horas"/>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block" style="display: grid; border-left: 1px solid #D7D8DB;">
                                                    <h:outputText id="qtdMinutosNovaVersao"
                                                                  styleClass="labelHoraNovaVersaoData"
                                                                  value="00"/>
                                                    <h:outputText styleClass="labelNovaVersaoData" value="Minutos"/>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block" style="display: grid; border-left: 1px solid #D7D8DB;">
                                                    <h:outputText id="qtdSegundosNovaVersao"
                                                                  styleClass="labelHoraNovaVersaoData"
                                                                  value="00"/>
                                                    <h:outputText styleClass="labelNovaVersaoData" value="Segundos"/>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Cadastrar cliente" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-cliente-visitante-aluno/"
                                                      title="Clique e saiba mais: Informações do Cliente"
                                                      target="_blank" >
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">

                                    <div style="clear: both;" class="text">
                                        <p style="margin-bottom: 6px;">
                                            <img src="images/arrow2.gif" width="16" height="16" style="vertical-align: middle; margin-right: 6px;">Consulta cliente
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-cliente-visitante-aluno/"
                                                          title="Clique e saiba mais: Informações do Cliente"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </p>
                                    </div>
                                    <div class="sep" style="margin: 4px 0 10px 0;"><img src="images/shim.gif"></div>

                                    <h:outputText styleClass="alinhar" style="font-size: 12px" value="Consulte por #{ClienteControle.displayIdentificadorFront[0]}, nome ou telefone:"/>
                                    &nbsp
                                    <h:panelGroup styleClass="pure-form">
                                        <h:inputText value="#{PreCadastroClienteControle.termoConsulta}" styleClass="inputTextClean"
                                                     id="termosConsulta"
                                                     size="50" maxlength="80"/>
                                        <rich:hotKey selector="#termosConsulta" key="return"
                                                     handler="#{rich:element('btnConsultarCliente')}.onclick();return false;"/>
                                    </h:panelGroup>
                                    &nbsp
                                    <a4j:commandLink id="btnConsultarCliente"
                                                     styleClass="pure-button pure-button-primary"
                                                     action="#{PreCadastroClienteControle.consultarPreCadastro}"
                                                     accesskey="1"
                                                     reRender="msgCasdas, msgPanel, painelDados,mdlMensagemGenerica, pnlMsgClienteRestricaoPreCadastro"
                                                     oncomplete="document.getElementById('form:termosConsulta').focus();#{PreCadastroClienteControle.msgAlert}">
                                        <i class="fa-icon-search" ></i> &nbsp Consultar
                                    </a4j:commandLink>

                                    <c:if test="${PreCadastroClienteControle.empresaLogado.usarParceiroFidelidade}">
                                        <br/>
                                        <br/>
                                        <h:outputText styleClass="alinhar" style="font-size: 12px" value="Consulte o saldo DOTZ por CPF:"/>
                                        &nbsp
                                        <h:panelGroup styleClass="pure-form">
                                            <h:inputText value="#{PreCadastroClienteControle.cpfParceiro}" styleClass="inputTextClean"
                                                         id="cpfParceiro"
                                                         onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                                         size="15" maxlength="15"/>
                                            <rich:hotKey selector="#cpfParceiro" key="return"
                                                         handler="#{rich:element('consultarSaldoParceiroFidelidade')}.onclick();return false;"/>
                                        </h:panelGroup>
                                        &nbsp
                                        <img src="images/logo-dotz.png" width="40" height="30" style="vertical-align: middle;">
                                        &nbsp
                                        <a4j:commandLink id="consultarSaldoParceiroFidelidade"
                                                         action="#{PreCadastroClienteControle.consultarSaldoParceiroFidelidade}"
                                                         accesskey="1"
                                                         style="font-size: 20px;"
                                                         styleClass="fa-icon-search tooltipster"
                                                         reRender="msgCasdas, msgPanel, painelDados"
                                                         oncomplete="document.getElementById('form:cpfParceiro').focus();"/>
                                    </c:if>

                                    &nbsp
                                    <h:outputText id="msgCasdas"
                                                  styleClass="alinhar" style="font-size: 12px; font-weight: bold;"
                                                  escape="false"
                                                  value="#{PreCadastroClienteControle.mensagemConsulta}" />

                                    <h:panelGroup id="pnlMsgClienteRestricaoPreCadastro">
                                        <h:panelGroup styleClass="col-md-11 msg-cliente-restricao"
                                                      rendered="#{not empty ClienteControle.mensagemClienteRestricao}">
                                            <i class="pct pct-alert-triangle" style="margin: 0px 10px 0px 0px"></i>
                                            <h:outputText value="#{ClienteControle.mensagemClienteRestricao}"/>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <br/>

                                    <h:panelGrid id="msgPanel" columns="3" width="100%">
                                        <h:panelGrid columns="1" width="100%">

                                            <h:outputText id="msgCasdasCliSimples1" styleClass="mensagem" value="#{PreCadastroClienteControle.mensagem}" />
                                            <h:outputText id="msgCasdasCliDet1" styleClass="mensagemDetalhada" value="#{PreCadastroClienteControle.mensagemDetalhada}" />
                                        </h:panelGrid>
                                    </h:panelGrid>
                                    <h:panelGroup id="painelDados">
                                        <rich:dataTable value="#{PreCadastroClienteControle.clientes}"
                                                        var="cliente" width="100%"
                                                        id="tableResults" styleClass="pure-table pure-table-horizontal pure-table-striped pure-table-noCellPadding pure-table-links"
                                                        rendered="#{fn:length(PreCadastroClienteControle.clientes) > 0}"
                                                        columnClasses="centralizado,esquerda,centralizado,centralizado,esquerda,esquerda"
                                                >

                                            <%-- FOTO --%>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText styleClass="fa-icon-picture" value=""/>
                                                </f:facet>

                                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                                style="width:43px;height:43px"
                                                                url="#{cliente.pessoa.urlFoto}">
                                                </h:graphicImage>

                                            </rich:column>

                                            <%-- NOME --%>
                                            <rich:column sortBy="#{cliente.pessoa.nome}">
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_GestaoPersonal_nome}"
                                                                  styleClass="text"
                                                                  style="font-weight: bold;"/>
                                                </f:facet>

                                                <a4j:commandLink actionListener="#{ClienteControle.prepareEditar}"
                                                                 action="#{ClienteControle.editarClienteBasico}"
                                                                 rendered="#{(cliente.empresa.codigo == PreCadastroClienteControle.codigoEmpresa || PreCadastroClienteControle.usuarioLogado.administrador) && !cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.pessoa.nome}" styleClass="text"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelExisteCliente');"
                                                                 action="#{ClienteControle.transferirClienteEmpresa}"
                                                                 reRender="formExisteCliente"
                                                                 rendered="#{(cliente.empresa.codigo != PreCadastroClienteControle.codigoEmpresa && !PreCadastroClienteControle.usuarioLogado.administrador) && !cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.pessoa.nome}" styleClass="text"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink action="#{PreCadastroClienteControle.converterPassivoIndicado}"
                                                                 rendered="#{cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.pessoa.nome}" styleClass="text"/>
                                                </a4j:commandLink>
                                            </rich:column>

                                            <%-- CPF --%>
                                            <rich:column sortBy="#{cliente.pessoa.cfp}">
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_ConfiguracaoSistema_cfpOb}"
                                                                  styleClass="text"
                                                                  style="font-weight: bold;"/>
                                                </f:facet>

                                                <a4j:commandLink actionListener="#{ClienteControle.prepareEditar}"
                                                                 action="#{ClienteControle.editarClienteBasico}"
                                                                 rendered="#{(cliente.empresa.codigo == PreCadastroClienteControle.codigoEmpresa || PreCadastroClienteControle.usuarioLogado.administrador) && !cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.pessoa.cfp}" styleClass="text"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelExisteCliente');"
                                                                 action="#{ClienteControle.transferirClienteEmpresa}"
                                                                 reRender="formExisteCliente"
                                                                 rendered="#{(cliente.empresa.codigo != PreCadastroClienteControle.codigoEmpresa && !PreCadastroClienteControle.usuarioLogado.administrador) && !cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.pessoa.cfp}" styleClass="text"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink action="#{PreCadastroClienteControle.converterPassivoIndicado}"
                                                                 rendered="#{cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.pessoa.cfp}" styleClass="text"/>
                                                </a4j:commandLink>

                                            </rich:column>

                                            <%-- TELEFONES --%>
                                            <rich:column sortBy="#{cliente.pessoa.nrTelefones}">
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_Agenda_telefones}"
                                                                  styleClass="text"
                                                                  style="font-weight: bold;"/>
                                                </f:facet>

                                                <a4j:commandLink actionListener="#{ClienteControle.prepareEditar}"
                                                                 action="#{ClienteControle.editarClienteBasico}"
                                                                 rendered="#{(cliente.empresa.codigo == PreCadastroClienteControle.codigoEmpresa || PreCadastroClienteControle.usuarioLogado.administrador) && !cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.pessoa.nrTelefonesApresentar}" styleClass="text"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelExisteCliente');"
                                                                 action="#{ClienteControle.transferirClienteEmpresa}"
                                                                 reRender="formExisteCliente"
                                                                 rendered="#{(cliente.empresa.codigo != PreCadastroClienteControle.codigoEmpresa && !PreCadastroClienteControle.usuarioLogado.administrador) && !cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.pessoa.nrTelefonesApresentar}" styleClass="text"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink action="#{PreCadastroClienteControle.converterPassivoIndicado}"
                                                                 rendered="#{cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.pessoa.nrTelefonesApresentar}" styleClass="text"/>
                                                </a4j:commandLink>

                                            </rich:column>

                                            <%-- ORIGEM - INDICADO OU PASSIVO OU CLIENTE --%>
                                            <rich:column sortBy="#{cliente.origem}">
                                                <f:facet name="header">
                                                    <h:outputText value="Origem"
                                                                  styleClass="text"
                                                                  style="font-weight: bold;"/>
                                                </f:facet>

                                                <a4j:commandLink actionListener="#{ClienteControle.prepareEditar}"
                                                                 action="#{ClienteControle.editarClienteBasico}"
                                                                 rendered="#{(cliente.empresa.codigo == PreCadastroClienteControle.codigoEmpresa || PreCadastroClienteControle.usuarioLogado.administrador) && !cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.origem}" styleClass="text"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelExisteCliente');"
                                                                 action="#{ClienteControle.transferirClienteEmpresa}"
                                                                 reRender="formExisteCliente"
                                                                 rendered="#{(cliente.empresa.codigo != PreCadastroClienteControle.codigoEmpresa && !PreCadastroClienteControle.usuarioLogado.administrador) && !cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.origem}" styleClass="text"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink action="#{PreCadastroClienteControle.converterPassivoIndicado}"
                                                                 rendered="#{cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.origem}" styleClass="text"/>
                                                </a4j:commandLink>
                                            </rich:column>

                                            <%-- EMPRESA --%>
                                            <rich:column sortBy="#{cliente.empresa.nome}"
                                                         rendered="#{PreCadastroClienteControle.maisDeUmaEmpresa}">
                                                <f:facet name="header">
                                                    <h:outputText value="Empresa"
                                                                  styleClass="text"
                                                                  style="font-weight: bold;"/>
                                                </f:facet>

                                                <a4j:commandLink actionListener="#{ClienteControle.prepareEditar}"
                                                                 action="#{ClienteControle.editarClienteBasico}"
                                                                 rendered="#{(cliente.empresa.codigo == PreCadastroClienteControle.codigoEmpresa || PreCadastroClienteControle.usuarioLogado.administrador) && !cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.empresa.nome}" styleClass="text"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelExisteCliente');"
                                                                 action="#{ClienteControle.transferirClienteEmpresa}"
                                                                 reRender="formExisteCliente"
                                                                 rendered="#{(cliente.empresa.codigo != PreCadastroClienteControle.codigoEmpresa && !PreCadastroClienteControle.usuarioLogado.administrador) && !cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.empresa.nome}" styleClass="text"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink action="#{PreCadastroClienteControle.converterPassivoIndicado}"
                                                                 rendered="#{cliente.passivoIndicado}">
                                                    <h:outputText value="#{cliente.empresa.nome}" styleClass="text"/>
                                                </a4j:commandLink>

                                            </rich:column>

                                            <%-- Status - img dependendo do status do cliente --%>
                                            <rich:column sortBy="#{cliente.situacao}">
                                                <f:facet name="header">
                                                    <h:outputText value="Status"
                                                                  styleClass="text"
                                                                  style="font-weight: bold;"/>
                                                </f:facet>

                                                <h:graphicImage id="alunoAtivo" style="width: 20px; height: 20px;" value="/images/botaoAtivo.png" rendered="#{cliente.situacao == 'AT'}"   />
                                                <h:graphicImage id="alunoInativo" style="width: 20px; height: 20px;" value="/images/botaoInativo.png" rendered="#{cliente.situacao == 'IN'}"  />
                                                <h:graphicImage id="alunoVisitante" style="width: 20px; height: 20px;" value="/images/botaoVisitante.png" rendered="#{cliente.situacao == 'VI'}"   />

                                                <h:graphicImage id="alunoNormal" style="width: 20px; height: 20px;" value="/images/botaoNormal.png" rendered="#{cliente.situacaoContrato == 'NO'}"  />
                                                <h:graphicImage id="alunoAVencer" style="width: 20px; height: 20px;" value="/images/botaoAvencer.png" rendered="#{cliente.situacaoContrato == 'AV'}"  />
                                                <h:graphicImage id="alunoCancelado" style="width: 20px; height: 20px;" value="images/botaoCancelamento.png" rendered="#{cliente.situacaoContrato == 'CA'}"  />
                                                <h:graphicImage id="alunoDesistente" style="width: 20px; height: 20px;" value="/images/botaoDesistente.png" rendered="#{cliente.situacaoContrato == 'DE'}"  />
                                                <h:graphicImage id="alunoCarencia" style="width: 18px; height: 18px;" value="/imagens/botaoCarencia.png" rendered="#{cliente.situacaoContrato == 'CR'}"  />
                                                <h:graphicImage id="alunoVencido" style="width: 20px; height: 20px;" value="/images/botaoVencido.png" rendered="#{cliente.situacaoContrato == 'VE'}"  />
                                                <h:graphicImage id="alunoTrancado" style="width: 20px; height: 20px;" value="/images/botaoTrancamento.png" rendered="#{cliente.situacaoContrato == 'TR'}"  />
                                                <h:graphicImage id="alunoTrancadoVencido" style="width: 20px; height: 20px;" value="/images/botaoTrancadoVencido.png" rendered="#{cliente.situacaoContrato == 'TV'}"  />

                                            </rich:column>
                                        </rich:dataTable>
                                        <br/>
                                        <a4j:commandLink id="btnNovo"
                                                         styleClass="pure-button pure-button-primary"
                                                         action="#{PreCadastroClienteControle.novoCliente}"
                                                         accesskey="1" rendered="#{PreCadastroClienteControle.habilitarBotaoNovo}"
                                                         oncomplete="if (#{PreCadastroClienteControle.apresentarRichModalValidarCpfIguais}) Richfaces.showModalPanel('modalCadastrarMesmoCpf');"
                                                         reRender="form,modalCadastrarMesmoCpf">
                                            <i class="fa-icon-plus" ></i> &nbsp Cadastrar novo
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>

                        <c:if test="${SuperControle.menuZwUi}">
                            <jsp:include page="include_box_menulateral.jsp">
                                <jsp:param name="menu" value="PESSOAS-INICIO" />
                            </jsp:include>
                        </c:if>

                        <c:if test="${not SuperControle.menuZwUi}">
                            <jsp:include page="include_box_menulateral.jsp">
                                <jsp:param name="menu" value="ADM-INICIO" />
                            </jsp:include>
                        </c:if>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

        </h:panelGroup>

        <a4j:jsFunction name="abrirNovaTelaIncluirCliente"
                        oncomplete="#{PreCadastroClienteControle.msgAlert}"
                        action="#{PreCadastroClienteControle.abrirNovaTelaIncluirCliente}">
        </a4j:jsFunction>
        <a4j:jsFunction name="verificarAbrirNovaTelaIncluirCliente" status="false"
                        oncomplete="#{PreCadastroClienteControle.msgAlert}"
                        action="#{PreCadastroClienteControle.verificarAbrirNovaTelaIncluirCliente}">
        </a4j:jsFunction>
    </h:form>

    <rich:modalPanel id="modalCadastrarMesmoCpf" width="450" autosized="true" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                Cadastrar mesmo CPF
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hideModalObservacaoConfirmarcao"/>
                <rich:componentControl for="modalObservacaoConfirmarcao" attachTo="hideModalObservacaoConfirmarcao" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formMdlObservacoesConfirmarcao">
            <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza" style="position: absolute;left: 5%;right: 5%">
                <h:outputText value="CPF já está cadastrado, deseja recadastra-lo para um novo cliente ?"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="container-botoes" style="margin-bottom: 15px;margin-top: 20px;">
                <h:panelGroup styleClass="margin-box">
                    <a4j:commandLink styleClass="botaoSecundario texto-size-16-real"
                                     id="confirmarExclussaoObservacao"
                                     action="#{PreCadastroClienteControle.prepararNovoClienteComMesmoCpf}"
                                     reRender="formMdlObservacoes, ultimaObservacao">
                        Sim
                    </a4j:commandLink>
                    <rich:spacer width="30px;"/>
                    <a4j:commandLink styleClass="botaoPrimario texto-size-16-real"
                                     id="naoExcluirObservacao"
                                     action="#{PreCadastroClienteControle.fecharApresentarRichModalValidarCpfIguais}"
                                     oncomplete="Richfaces.hideModalPanel('modalCadastrarMesmoCpf')"
                                     reRender="modalCadastrarMesmoCpf">
                        Não
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <%@include file="include_modal_transferirCliente.jsp" %>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>

    <script>

        const novaversao_second = 1000;
        const novaversao_minute = novaversao_second * 60;
        const novaversao_hour = novaversao_minute * 60;
        const novaversao_day = novaversao_hour * 24;
        let novaversao_dataDesativar = null;

        let novaversao_count_down = null;
        let novaversao_x = null;

        async function obterDataDesativarNovaTelaIncluirCliente() {
            if (novaversao_dataDesativar == null) {
                var url = '${SuperControle.urloamdrecursomigracao}/prest/migracao-recurso/data-migracao/${LoginControle.key}/INCLUIR_CLIENTE';
                const response =  fetch(url, {
                    method: "GET",
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = (await response).json();
                return data;
            }
        }

        function countDownDesativarNovaTelaIncluirCliente() {
            let now = new Date(Date.now()).getTime();
            let diff = novaversao_count_down - now;
            if (diff < 0) {
                diff = 0;
            }
            document.getElementById('form:qtdDiasNovaVersao').innerText = Math.floor(diff / novaversao_day);
            document.getElementById('form:qtdHorasNovaVersao').innerText = Math.floor(diff % novaversao_day / novaversao_hour);
            document.getElementById('form:qtdMinutosNovaVersao').innerText = Math.floor(diff % novaversao_hour / novaversao_minute);
            document.getElementById('form:qtdSegundosNovaVersao').innerText = Math.floor(diff % novaversao_minute / novaversao_second);
        }

        function iniciarCountDownDesativarNovaTelaIncluirCliente() {
            try {
                if (novaversao_x) {
                    clearInterval(novaversao_x);
                }
                obterDataDesativarNovaTelaIncluirCliente().then((resultado) => {
                    if (resultado.content) {
                        novaversao_dataDesativar = resultado.content;
                        const dataNova = new Date(resultado.content);
                        document.getElementById('form:novaVersaoTelaAlunoData').innerText = dataNova.toLocaleDateString();
                        novaversao_count_down = dataNova;
                        novaversao_x = setInterval(() => countDownDesativarNovaTelaIncluirCliente(), novaversao_second);
                        const divContadorNovaVersaoTelaAluno = document.getElementById('form:divContadorNovaVersaoTelaAluno');
                        if (divContadorNovaVersaoTelaAluno) {
                            divContadorNovaVersaoTelaAluno.style.display = 'block';
                        }
                        const divMsgExperimenteNovaVersao = document.getElementById('form:divMsgExperimenteNovaVersao');
                        if (divMsgExperimenteNovaVersao) {
                            divMsgExperimenteNovaVersao.style.display = 'none';
                        }
                    }
                })
            } catch (e) {
                console.log(e);
            }
        }

        window.onload = function() {
            var campo = document.getElementById('form:termosConsulta');
            campo.focus();
        };

        document.addEventListener('DOMContentLoaded', function () {
            iniciarCountDownDesativarNovaTelaIncluirCliente();
            try {
                verificarAbrirNovaTelaIncluirCliente();
            } catch (e) {
                console.log(e);
            }
        });
    </script>
</f:view>
