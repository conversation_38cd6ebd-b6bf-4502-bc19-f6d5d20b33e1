<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

<style>
    .textoAtencaoNovaVersao {
        color: #AF0404;
        font-family: "Nunito Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        padding-right: 5px;
    }
    .textoSaibaMaisNovaVersao {
        color: #1E60FA;
        font-family: "Nunito Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        padding-left: 5px;
    }
    .textoNovaVersao {
        color: #55585E;
        font-family: "Nunito Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
    }
    .labelHoraNovaVersaoData {
        color: #AF0404;
        font-family: "Nunito Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
    }
    .labelNovaVersaoData {
        color: #494B50;
        font-family: "Nunito Sans";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
    }
    .div-geral-experimente {
        display: grid;
        padding: 1.5% 0 0 1.5%;
        width: 96%;
        grid-template-columns: 2fr 0fr;
    }

    .div-experimente2 {
        justify-content: end;
        display: flex;
        padding-left: 15px;
    }

    .div-experimente {
        background-color: #fff !important;
        border-radius: 5px;
        font-family: "Nunito Sans",sans-serif !important;
        padding: 10px;
        align-items: center;
        display: flex;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 30px;
        height: 17px;
    }

    /* Hide default HTML checkbox */
    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    /* The slider */
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: #2196F3;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #2196F3;
    }

    input:checked + .slider:before {
        -webkit-transform: translateX(13px);
        -ms-transform: translateX(13px);
        transform: translateX(13px);
    }

    /* Rounded sliders */
    .slider.round {
        border-radius: 17px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .padrao-cliente {
        display: flex;
        color: #797D86;
        font-size: 16px;
        font-weight: 400;
        line-height: 16px;
        padding: 30px 0px 0 40px;
        width: calc(100% - 80px);
        justify-content: flex-end;
    }

    .padrao-cliente label {
        margin-left: 8px;
    }

    .padrao-cliente .clicavel {
        cursor: pointer;
    }
</style>

<h:panelGroup layout="block" styleClass="div-geral-experimente"
              rendered="#{!MovParcelaControle.usaPinpad}">

    <h:panelGroup layout="block"
                  id="divMsgExperimenteNovaVersao"
                  styleClass="div-experimente">
        <h:graphicImage value="images/pct-alert-triangle.svg" style="width: 16px; padding-right: 5px; padding-bottom: 2px;"/>
        <h:outputText value="Nos próximos meses essa tela será descontinuada.
                                                              Experimente a nova versão e teste as melhorias.
                                                              Você poderá nos enviar feedbacks e ajudar a construir um sistema melhor para o seu dia a dia."
                      styleClass="texto-size-14"/>
    </h:panelGroup>

    <h:panelGroup layout="block"
                  styleClass="div-experimente2"
                  rendered="#{MovParcelaControle.novaTelaCaixaAbertoPadraoEmpresa}">
        <%--                                                <a4j:commandButton id="abrirNovaTelaClienteExperimente"--%>
        <%--                                                                   action="#{TelaClienteControle.abrirNovaTelaClienteExperimente}"--%>
        <%--                                                                   value="Experimentar nova versão"--%>
        <%--                                                                   alt="Experimentar nova versão"--%>
        <%--                                                                   styleClass="btn-experimente texto-size-14"--%>
        <%--                                                                   oncomplete="#{TelaClienteControle.msgAlert}"/>--%>
    </h:panelGroup>
</h:panelGroup>

<h:panelGroup layout="block"
              rendered="#{!MovParcelaControle.usaPinpad && !MovParcelaControle.novaTelaCaixaAbertoPadraoEmpresa}">
              <span class="padrao-cliente"
                    id="div-switch-nova-versao">
                  <span class="clicavel" onclick="abrirNovaCaixaAbertoPadrao()">Usar nova versão</span>
                  <label class="switch clicavel" onclick="abrirNovaCaixaAbertoPadrao()">
                      <input type="checkbox"
                             id="switch-nova-versao">
                      <span class="slider round"></span>
                  </label>
              </span>
</h:panelGroup>

<h:panelGroup layout="block"
              style="border-radius: 8px;display: grid;margin-top: 20px;margin-bottom: -20px;padding-left: 3%;padding-right: 3%;"
              rendered="#{MovParcelaControle.usaPinpad}">
    <h:panelGroup layout="block"
                  id="empresaUsaPinpad"
                  styleClass="div-experimente">
        <h:graphicImage value="images/pct-alert-triangle.svg" style="width: 16px; padding-right: 5px; padding-bottom: 2px;"/>
        <h:outputText value="Esta empresa utiliza PINPAD, este recurso ainda não está disponível na nova tela de recebimento."
                      styleClass="texto-size-14"/>
    </h:panelGroup>
</h:panelGroup>

<h:panelGroup layout="block"
              id="divContadorNovaVersaoTelaAluno"
              style="display: none">
    <h:panelGroup layout="block"
                  style="background: #FAFAFA; border-radius: 8px; display: grid; margin: 1.5% 0 0 1.5%; width: 96%;">

        <h:panelGroup layout="block" id="divGeralContadorNovaVersao"
                      styleClass="divGeralContadorNovaVersao" style="padding: 20px;">

            <h:panelGroup layout="block" id="divGeralContadorNovaVersaoSuperior"
                          styleClass="divGeralContadorNovaVersaoSuperior"
                          style="padding-bottom: 20px; display: flex;align-items: center;">
                <h:panelGroup layout="block">
                    <h:graphicImage value="images/pct-alert-triangle-ds3.svg"
                                    style="width: 32px; padding-right: 5px;"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="display: flex">
                    <h:outputText styleClass="textoAtencaoNovaVersao" value="Atenção: "/>
                    <h:outputText styleClass="textoNovaVersao" value="Esta tela será desligada em "/>
                    <h:outputText id="novaVersaoTelaAlunoData" styleClass="textoNovaVersao" style="padding-left: 5px" value=""/>
                    <h:outputText styleClass="textoNovaVersao" value=". Recomendamos que utilize a nova tela para obter familiaridade até o desligamento dessa. "/>
                    <h:outputLink styleClass="textoSaibaMaisNovaVersao"
                                  target="_blank"
                                  value="https://pactosolucoes.com.br/ajuda/conhecimento/adm-sua-antiga-tela-do-perfil-do-aluno-sera-desativada/">
                        Saiba mais
                    </h:outputLink>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" id="divGeralContadorNovaVersaoInferior"
                          styleClass="divGeralContadorNovaVersaoInferior"
                          style="justify-content: center;display: flex;">
                <h:panelGroup layout="block"
                              style="padding: 15px;display: grid;grid-template-columns: 1fr 1fr 1fr 1fr;text-align: center;width: 45%;border: 1px solid #E23661;border-radius: 8px">
                    <h:panelGroup layout="block" style="display: grid">
                        <h:outputText id="qtdDiasNovaVersao"
                                      styleClass="labelHoraNovaVersaoData"
                                      value="00"/>
                        <h:outputText styleClass="labelNovaVersaoData" value="Dias"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="display: grid; border-left: 1px solid #D7D8DB;">
                        <h:outputText id="qtdHorasNovaVersao"
                                      styleClass="labelHoraNovaVersaoData"
                                      value="00"/>
                        <h:outputText styleClass="labelNovaVersaoData" value="Horas"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="display: grid; border-left: 1px solid #D7D8DB;">
                        <h:outputText id="qtdMinutosNovaVersao"
                                      styleClass="labelHoraNovaVersaoData"
                                      value="00"/>
                        <h:outputText styleClass="labelNovaVersaoData" value="Minutos"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="display: grid; border-left: 1px solid #D7D8DB;">
                        <h:outputText id="qtdSegundosNovaVersao"
                                      styleClass="labelHoraNovaVersaoData"
                                      value="00"/>
                        <h:outputText styleClass="labelNovaVersaoData" value="Segundos"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>



<h:panelGroup layout="block" styleClass="container-box zw_ui especial">
    <h:panelGroup styleClass="container-box-header" layout="block">
        <h:panelGroup layout="block" styleClass="margin-box">
            <h:outputText value="Caixa em Aberto" styleClass="container-header-titulo"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-receber-uma-parcela-do-aluno-no-caixa-em-aberto/"
                          title="Clique e saiba mais: Caixa em Aberto"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
    </h:panelGroup>


    <h:panelGroup id="panelCaixaAberto"  layout="block" styleClass="margin-box">
        <h:panelGrid width="100%" columns="2" rowClasses="linhaTop">

            <!-- --------------- FILTROS ----------------- -->
            <h:panelGroup>
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td align="left" valign="top">
                            <div style="clear:both;" class="text">
                                <p style="margin-bottom:4px;"><img src="${root}/images/arrow2.gif" width="16"
                                                                   height="16"
                                                                   style="vertical-align:middle;margin-right:6px;">
                                    Filtros de Pesquisa</p>
                            </div>
                                <rich:separator height="2px" lineType="dotted"/>
                    </tr>
                </table>

                <h:panelGrid columns="3" styleClass="tablelistras textsmall" id="panelGroupConsultar">
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="consultaHoje" value="#{MovParcelaControle.consultarHoje}">
                            <a4j:support status="statusHora" event="onclick"
                                         action="#{MovParcelaControle.obterFiltroConsultarHoje}"
                                         reRender="panelGroupConsultar"/>
                        </h:selectBooleanCheckbox>
                        <label><span style="font-weight: bold">Consultar todos de Hoje </span></label>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="consultaSemana" value="#{MovParcelaControle.consultarSemana}">
                            <a4j:support status="statusHora" event="onclick"
                                         action="#{MovParcelaControle.obterFiltroConsultarSemana}"
                                         reRender="panelGroupConsultar"/>
                        </h:selectBooleanCheckbox>
                        <label><span style="font-weight: bold">Consultar todos da semana</span></label>
                    </h:panelGroup>


                    <h:panelGroup>
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <h:selectBooleanCheckbox id="incluirParcelasRecorrencia"
                                                     value="#{MovParcelaControle.incluirParcelasRecorrencia}">
                            </h:selectBooleanCheckbox>
                            <label><span style="font-weight: bold">Incluir parcelas Recorrência</span></label>
                        </c:if>
                    </h:panelGroup>


                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="ordenarLancamento"
                                                 value="#{MovParcelaControle.ordenarPorLancamento}">
                            <a4j:support status="statusHora" event="onclick"
                                         action="#{MovParcelaControle.alternarOrdenacaoLanc}"
                                         reRender="panelGroupConsultar"/>
                        </h:selectBooleanCheckbox>
                        <label><span style="font-weight: bold">Ordenar por data de lançamento</span></label>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="ordenarAlfabetico"
                                                 value="#{MovParcelaControle.ordenarPorAlfabetica}">
                            <a4j:support status="statusHora" event="onclick"
                                         action="#{MovParcelaControle.alternarOrdenacaoAlf}"
                                         reRender="panelGroupConsultar"/>
                        </h:selectBooleanCheckbox>
                        <label><span style="font-weight: bold">Ordem Alfabética</span></label>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="naoApresentarVencimentosDeMesesFuturos"
                                                 value="#{MovParcelaControle.naoApresentarVencimentosDeMesesFuturos}">
                            <a4j:support status="statusHora" event="onclick" reRender="panelGroupConsultar"/>
                        </h:selectBooleanCheckbox>
                        <label><span
                                style="font-weight: bold">Não apresentar vencimentos de meses futuros</span></label>
                    </h:panelGroup>

                </h:panelGrid>
                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelistras textsmall" role="presentation">
                    <c:if test="${MovParcelaControle.apresentarEmpresa}">
                        <tr>
                            <td align="left" valign="middle">
                                <h:outputText value="Empresa:" style="font-weight: bold"/>
                            </td>
                            <td align="left" valign="middle">
                                <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{MovParcelaControle.filtroCodigoEmpresa}">
                                    <f:selectItems value="#{MovParcelaControle.listaSelectItemEmpresa}"/>
                                    <a4j:support event="onchange"
                                                 reRender="tabelaItens, paginacao, paginacao2, mensagem, consultaHoje, consultaSemana, contrato, valorTotalParcela,
                                                                 responsavel,gerarBoleto,dataInicio,dataTermino, responsavelAbaixo,valorAbaixo, valorConsulta, msgMSG, panelMensagemErro,
                                                                 renegociarParcelas"
                                                 action="#{MovParcelaControle.limparBusca}"/>
                                </h:selectOneMenu>
                                <rich:spacer width="5"/>
                                <a4j:commandButton id="atualizar_empresa"
                                                   action="#{MovParcelaControle.montarListaSelectItemEmpresa}"
                                                   style="vertical-align:middle;" image="imagens/atualizar.png"
                                                   immediate="true"
                                                   ajaxSingle="true" reRender="form:empresa"/>
                            </td>
                        </tr>
                    </c:if>
                    <tr>
                        <td align="left" valign="middle" class="par"><label>
                            <span style="font-weight: bold">Matrícula:</span></label></td>
                        <td align="left" valign="middle" class="par"><h:inputText id="matricula"
                                                                                  value="#{MovParcelaControle.matricula}"
                                                                                  disabled="#{SuperControle.key eq '2c0033a8ac79bdcff56cd499dfa7874b'}"
                                                                                  size="10" styleClass="form"
                                                                                  onfocus="focusinput(this);"
                                                                                  onblur="blurinput(this);"/></td>
                    </tr>
                    <tr>
                        <td align="left" valign="middle"><label>
                            <span style="font-weight: bold">Cliente:</span></label></td>
                        <td align="left" valign="middle"><h:inputText id="valorConsulta"
                                                                      value="#{MovParcelaControle.valorConsulta}"
                                                                      disabled="#{SuperControle.key eq '2c0033a8ac79bdcff56cd499dfa7874b'}"
                                                                      size="50" styleClass="form"
                                                                      onfocus="focusinput(this);"
                                                                      onblur="blurinput(this);"/></td>
                    </tr>
                    <tr>
                        <td align="left" valign="middle" class="par"><span style="font-weight: bold">Período de Vencimento de Parcelas:</span>
                        </td>
                        <td align="left" class="par">
                            <rich:calendar id="dataInicio"
                                           value="#{MovParcelaControle.dataInicio}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                                <%--h:inputText id="data" styleClass="form" style="width:100px;" onfocus="focusinput(this);" onblur="blurinput(this);return validar_Data('form:data');" onchange="return mascara(this.form, 'form:data', '99/99/9999', event);" value="#{MovParcelaControle.data}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:inputText--%>
                            <rich:spacer width="7"/>
                            <span style="font-weight: bold"> até </span>
                            <rich:spacer width="7"/>
                            <rich:calendar id="dataTermino"
                                           value="#{MovParcelaControle.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false">
                                <a4j:support event="onchanged"
                                             action="#{MovParcelaControle.desmarcarNaoApresentarVencimentosFuturos}"
                                             reRender="panelGroupConsultar"/>
                            </rich:calendar>
                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                                <%--h:inputText id="data" styleClass="form" style="width:100px;" onfocus="focusinput(this);" onblur="blurinput(this);return validar_Data('form:data');" onchange="return mascara(this.form, 'form:data', '99/99/9999', event);" value="#{MovParcelaControle.data}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                                </h:inputText--%>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="middle"><label>
                            <span style="font-weight: bold">Produto:</span></label></td>
                        <td align="left" valign="middle">
                            <h:inputText id="produto"
                                         value="#{MovParcelaControle.produto}"
                                         disabled="#{SuperControle.key eq '2c0033a8ac79bdcff56cd499dfa7874b'}"
                                         size="51" maxlength="51"
                                         styleClass="form"
                                         onfocus="focusinput(this);"
                                         onblur="blurinput(this);"/>
                            <h:inputText id="produtoaux"
                                         style="border:none; background-image:none; background-color:transparent; -webkit-box-shadow: none; -moz-box-shadow: none; box-shadow: none;max-width: 1px;max-height: 1px;"
                                         onfocus="document.getElementById('form:botaoBuscarParcelas').focus();"/>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="middle">&nbsp;</td>
                        <td align="left" valign="middle">
                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="botaoBuscarParcelas"
                                                 style="font-size: larger"
                                                 styleClass="pure-button pure-button-small pure-button-primary"
                                                 reRender="paginacao,paginacao2,tabelaItens,mensagem , contrato, valorTotalParcela,
                                                                   responsavel,gerarBoleto,dataInicio,dataTermino, responsavelAbaixo,valorAbaixo,msg, panelMensagemErro,
                                                                   renegociarParcelas"
                                                 actionListener="#{MovParcelaControle.caixaAbertoListener}">
                                    <f:attribute name="tipoConsulta" value="detalhada"/>
                                    <f:attribute name="paginaInicial" value="paginaInicial"/>
                                    <i class="fa-icon-search"></i> &nbsp Buscar Parcelas
                                </a4j:commandLink>


                                <rich:hotKey selector="#valorConsulta" key="return"
                                             handler="#{rich:element('botaoBuscarParcelas')}.onclick();return false;"/>
                                <rich:hotKey selector="#dataInicio" key="return"
                                             handler="#{rich:element('botaoBuscarParcelas')}.onclick();return false;"/>
                                <rich:hotKey selector="#dataFim" key="return"
                                             handler="#{rich:element('botaoBuscarParcelas')}.onclick();return false;"/>
                                <rich:spacer width="30px"/>

                                <a4j:commandLink id="botaoLimparFiltro"
                                                 style="font-size: larger"
                                                 styleClass="pure-button pure-button-small"
                                                 reRender="tabelaItens, paginacao, paginacao2, mensagem, consultaHoje, consultaSemana,
                                                                   contrato, valorTotalParcela, responsavel,gerarBoleto,dataInicio,dataTermino,
                                                                   responsavelAbaixo,valorAbaixo, valorConsulta, matricula, produto, renegociarParcelas"
                                                 action="#{MovParcelaControle.limparConsulta}"
                                                 title="Limpar Busca de Resultado de Parcelas">
                                    Limpar Busca
                                </a4j:commandLink>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="botaoBuscarParcelasCE" reRender="tabelaItens, paginacao, paginacao2, mensagem , contrato, valorTotalParcela,
                                                                   responsavel,gerarBoleto,dataInicio,dataTermino, responsavelAbaixo,valorAbaixo,
                                                                   renegociarParcelas" style="vertical-align:middle;"
                                                   action="#{MovParcelaControle.consultaCentralEventos}"
                                                   image="/imagens/botoesCE/buscar.png" alt="Buscar"/>
                                <rich:hotKey selector="#valorConsulta" key="return"
                                             handler="#{rich:element('botaoBuscarParcelasCE')}.onclick();return false;"/>
                                <rich:hotKey selector="#dataInicio" key="return"
                                             handler="#{rich:element('botaoBuscarParcelasCE')}.onclick();return false;"/>
                                <rich:hotKey selector="#dataFim" key="return"
                                             handler="#{rich:element('botaoBuscarParcelasCE')}.onclick();return false;"/>
                                <rich:spacer width="30px"/>
                                <a4j:commandButton id="botaoLimparFiltroCE"
                                                   reRender="tabelaItens, paginacao, paginacao2, mensagem, consultaHoje, consultaSemana,
                                                                   contrato, valorTotalParcela, responsavel, gerarBoleto, dataInicio, dataTermino,
                                                                   responsavelAbaixo,valorAbaixo, valorConsulta, renegociarParcelas"
                                                   style="vertical-align:middle;"
                                                   action="#{MovParcelaControle.limparConsulta}"
                                                   image="/imagens/botoesCE/limpar_busca.png"
                                                   alt="Limpar Busca de Resultado de Parcelas"/>

                            </c:if>
                        </td>


                    </tr>
                </table>

            </h:panelGroup>
            <!-- --------------- BOTOES IE ----------------- -->
            <c:if test="${SuperControle.internetExplorer}">

                <h:panelGroup>
                    <rich:spacer width="30px"/>
                    <table width="100%" height="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td align="left" valign="bottom">

                                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                       class="tablepreviewtotal">
                                    <tr>
                                        <span style="font-weight: bold">Responsável Pagamento:</span></label></td>
                                        <td align="left" valign="middle">
                                            <h:selectOneMenu id="responsavel"
                                                             value="#{MovParcelaControle.numeroContratoResponsavel}"
                                                             styleClass="form" onfocus="focusinput(this);"
                                                             onblur="blurinput(this);">
                                                <a4j:support event="onchange" ajaxSingle="true"
                                                             action="#{MovParcelaControle.selecionarResponsavelPagamento}"
                                                             reRender="responsavel,gerarBoleto,responsavelAbaixo, renegociarParcelas"/>
                                                <f:selectItems value="#{MovParcelaControle.listaResponsavelPagamento}"/>
                                            </h:selectOneMenu>
                                        </td>
                                    </tr>
                                </table>

                                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                       class="tablepreviewtotal">
                                    <tr>
                                        <td align="right" valign="middle">
                                            TOTAL FINAL:
                                                            <span class="verde"><h:outputText value="#{MovParcelaControle.empresaLogado.moeda}"/>
                                                                <h:outputText id="valorTotalParcela"
                                                                              value="#{MovParcelaControle.valorTotalParcela}">
                                                                    <f:converter converterId="FormatadorNumerico"/>
                                                                </h:outputText>
                                                            </span>
                                        </td>
                                    </tr>
                                </table>


                                <!-- fim item -->

                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" align="right">
                                <%@include file="includes/menus/include_menubotoesfixos_tela8.jsp" %>
                            </td>
                        </tr>
                    </table>

                </h:panelGroup>
            </c:if>
            <c:if test="${not SuperControle.internetExplorer}">
                <%@include file="include_parcelasPagar.jsp" %>
            </c:if>
            <!-- --------------- PARCELAS SELECIONADAS ----------------- -->

        </h:panelGrid>
        <c:choose>
            <c:when test="${SuperControle.internetExplorer}">
                <table width="61%">
                    <tr>
                        <td>
                            <%@include file="include_parcelasPagar.jsp" %>
                        </td>
                    </tr>
                </table>

            </c:when>
            <c:otherwise>
                <%@include file="includes/menus/include_menubotoesfixos_tela8.jsp" %>
            </c:otherwise>
        </c:choose>
        <%-- <h:outputText id="msg" styleClass="tituloCamposVerde" value="#{MovParcelaControle.confirmacaoCancelamento}"/>
        <h:outputText id="mensagem" styleClass="mensagemDetalhada" value="#{MovParcelaControle.mensagemDetalhada}"/> --%>

        <h:panelGrid id="pnlMsg" columns="1" width="100%" styleClass="tabMensagens">
            <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                <h:commandButton id="icSucesso" rendered="#{RenegociacaoControle.sucesso}"
                                 image="./imagens/sucesso.png"/>
                <h:commandButton id="icErro" rendered="#{RenegociacaoControle.erro}" image="./imagens/erro.png"/>

                <h:panelGrid columns="1" width="100%">
                    <h:outputText id="msgMSG" styleClass="mensagem" value="#{RenegociacaoControle.mensagem}"/>
                    <h:outputText id="msgDet" styleClass="mensagemDetalhada"
                                  value="#{RenegociacaoControle.mensagemDetalhada}"/>

                    <h:outputText id="msg" styleClass="tituloCamposVerde"
                                  value="#{MovParcelaControle.confirmacaoCancelamento}"/>
                    <h:outputText id="mensagem" styleClass="mensagemDetalhada"
                                  value="#{MovParcelaControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
        <!-- PAGINADOR EM BANCO -->

        <h:panelGrid id="paginacao2" style="padding:0px;" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup id="painelPaginacao2" rendered="#{MovParcelaControle.apresentarPaginacao}">
                <a4j:commandLink id="pagiInicial2" styleClass="tituloCampos9" value="  <<  "
                                 reRender="tabelaItens, paginaAtual, painelPaginacao,painelPaginacao2"
                                 rendered="#{MovParcelaControle.confPaginacao.apresentarPrimeiro}"
                                 actionListener="#{MovParcelaControle.caixaAbertoListener}">
                    <f:attribute name="pagNavegacao" value="pagInicial"/>
                </a4j:commandLink>
                <a4j:commandLink id="pagiAnterior2" styleClass="tituloCampos9" value="  <  "
                                 reRender="tabelaItens, paginaAtual, painelPaginacao,painelPaginacao2"
                                 rendered="#{MovParcelaControle.confPaginacao.apresentarAnterior}"
                                 actionListener="#{MovParcelaControle.caixaAbertoListener}">
                    <f:attribute name="pagNavegacao" value="pagAnterior"/>
                </a4j:commandLink>
                <h:outputText id="paginaAtual2" styleClass="tituloCampos9"
                              value="#{msg_aplic.prt_msg_pagina} #{MovParcelaControle.confPaginacao.paginaAtualDeTodas}"
                              rendered="true"/>
                <a4j:commandLink id="pagiPosterior2" styleClass="tituloCampos9" value="  >  "
                                 reRender="tabelaItens, paginaAtual, painelPaginacao,painelPaginacao2"
                                 rendered="#{MovParcelaControle.confPaginacao.apresentarPosterior}"
                                 actionListener="#{MovParcelaControle.caixaAbertoListener}">
                    <f:attribute name="pagNavegacao" value="pagPosterior"/>
                </a4j:commandLink>
                <a4j:commandLink id="pagiFinal2" styleClass="tituloCampos9" value="  >>  "
                                 reRender="tabelaItens, paginaAtual, painelPaginacao,painelPaginacao2"
                                 rendered="#{MovParcelaControle.confPaginacao.apresentarUltimo}"
                                 actionListener="#{MovParcelaControle.caixaAbertoListener}">
                    <f:attribute name="pagNavegacao" value="pagFinal"/>
                </a4j:commandLink>
                <h:outputText id="totalItens2" styleClass="tituloCampos9"
                              value=" [#{msg_aplic.prt_msg_itens} #{MovParcelaControle.confPaginacao.numeroTotalItens}]"
                              rendered="true"/>
            </h:panelGroup>
        </h:panelGrid>


        <rich:dataTable id="tabelaItens" value="#{MovParcelaControle.itensCaixaAberto}" var="item" width="100%"
                        rowClasses="linhaPar, linhaImparEscura" styleClass="colunaFina"
                        cellpadding="0" cellspacing="0">
            <h:column id="t1">
                <h:panelGrid rowClasses="linhaCentralizadaMeio" columns="3" cellpadding="0" cellspacing="0">
                    <h:panelGroup>
                        <a4j:commandLink id="btnExpandirParcelas" title="#{msg_aplic.prt_ExpandirParcelas}"
                                         action="#{MovParcelaControle.exibirParcelas}"
                                         reRender="tabelaItens"
                                         rendered="#{!item.exibeParcelas}">
                            <i class="fa-icon-plus-sign" style="font-size: large;  color: green"> </i>
                        </a4j:commandLink>

                        <a4j:commandLink id="btnOcultarParcelas" title="#{msg_aplic.prt_OcultarParcelas}"
                                         action="#{MovParcelaControle.exibirParcelas}"
                                         reRender="tabelaItens"
                                         rendered="#{item.exibeParcelas}">
                            <i class="fa-icon-minus-sign" style="font-size: large; color: green"> </i>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:panelGroup layout="block" style="padding-left: 5px">
                        <h:selectBooleanCheckbox id="selecionarTodasParcelas" value="#{item.marcarTodas}">
                            <a4j:support event="onclick" ajaxSingle="true"
                                         action="#{MovParcelaControle.marcaTodasParcelas}"
                                         reRender="selecionadas,tabelaParcelas, cliente,parcela,responsavel,gerarBoleto,
                                                         valorTotalParcela,responsavelAbaixo,valorAbaixo,renegociarParcelas,selecionarTodos,
                                                         tabelaItens"/>
                        </h:selectBooleanCheckbox>

                        <a4j:commandLink title="Clique para exibir as parcelas em aberto"
                                         action="#{MovParcelaControle.exibirParcelas}"
                                         style="padding-left: 5px"
                                         reRender="tabelaItens,tabelaParcelas"
                                         status="statusHora">

                            <h:outputText id="ttResponsavelDescricao"
                                          styleClass="tituloCamposNegritoMenor"
                                          rendered="#{!(item.personal != null and item.personal > 0) and MovParcelaControle.filtroCodigoEmpresa > 0 or MovParcelaControle.filtroCodigoEmpresa == null}"
                                          value="#{item.nomeCliente} - #{item.descricao} - #{CaixaControle.empresaLogado.moeda} #{item.valorTotalMonetario} - #{item.lancamentoFormatada}"/>

                            <h:outputText styleClass="tituloCamposNegritoMenor"
                                          rendered="#{!(item.personal != null and item.personal > 0) and MovParcelaControle.filtroCodigoEmpresa == 0}"
                                          value="#{item.nomeCliente} -  #{item.descricao} #{CaixaControle.empresaLogado.moeda} #{item.valorTotalMonetario} - #{item.lancamentoFormatada} - #{item.empresaNome}" />
                            <h:outputText styleClass="tituloCamposNegritoMenor"
                                          rendered="#{item.personal != null and item.personal > 0 and MovParcelaControle.filtroCodigoEmpresa == 0}"
                                          value="#{item.nomeCliente} - Personal - #{item.valorTotalMonetario} #{CaixaControle.empresaLogado.moeda} #{item.lancamentoFormatada} - #{item.empresaNome}" />
                            <h:outputText styleClass="tituloCamposNegritoMenor"
                                          rendered="#{item.personal != null and item.personal > 0 and MovParcelaControle.filtroCodigoEmpresa > 0}"
                                          value="#{item.nomeCliente} - Personal - #{CaixaControle.empresaLogado.moeda} #{item.valorTotalMonetario} - #{item.lancamentoFormatada}" />
                        </a4j:commandLink>

                    </h:panelGroup>
                </h:panelGrid>


                <rich:dataTable id="tabelaParcelas" width="100%" columnClasses="colunaCentralizada"
                                value="#{item.parcelas}" var="parcelaContrato"
                                rendered="#{item.exibeParcelas && !item.emptyParcelas}">

                    <rich:column width="5%">
                        <f:facet name="header">
                            <h:outputText value="Código"/>
                        </f:facet>
                        <h:outputText value="#{parcelaContrato.codigo}"/>
                    </rich:column>

                    <rich:column width="30%">
                        <f:facet name="header">
                            <h:outputText value="Parcela"/>
                        </f:facet>
                        <rich:spacer width="30px"/>
                        <h:selectBooleanCheckbox id="selecionarParcela" value="#{parcelaContrato.parcelaEscolhida}">
                            <a4j:support event="onclick" ajaxSingle="true"
                                         action="#{MovParcelaControle.selecionarParcela}"
                                         reRender="selecionadas, tabelaItens,valorTotalParcela, selecionarTodasParcela ,
                                                         responsavel,responsavelAbaixo,gerarBoleto,valorAbaixo,renegociarParcelas,selecionarTodos,
                                                         tabelaParcelas"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText id="tabelaParcelaDescricao"
                                      style="padding-left: 5px"
                                      value="#{parcelaContrato.descricao}"/>
                        <h:outputText style="font-weight:bold;" rendered="#{parcelaContrato.contrato.pagarComBoleto}"
                                      value=" - (Boleto Bancário)"/>
                    </rich:column>

                        <rich:column width="25%" id="colunaProduto"  rendered="#{item.apresentarNomeProduto}">
                            <f:facet name="header"  >
                                <h:outputText value="Produto(s)"/>
                            </f:facet>
                            <rich:spacer width="30px"/>
                            <h:outputText id="tabelaProdutoDescricao"
                                          style="padding-left: 5px"
                                          value="#{item.descricaoProduto}"/>
                        </rich:column>

                    <rich:column width="20%">
                        <f:facet name="header">
                            <h:outputText value="Data de Lançamento"/>
                        </f:facet>
                        <h:outputText id="tabelaParcelaDataLancamento" value="#{parcelaContrato.dataRegistro_Apresentar}"/>
                    </rich:column>

                    <rich:column width="20%">
                        <f:facet name="header">
                            <h:outputText value="Vencimento"/>
                        </f:facet>
                        <h:outputText id="tabelaParcelaVencimento" value="#{parcelaContrato.dataVencimento_Apresentar}"/>
                    </rich:column>

                    <rich:column width="15%">
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText id="tabelaParcelaValor" value="#{parcelaContrato.valorParcela}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>

                    <rich:column visible="#{MovParcelaControle.valorMultaJuros != 0}" width="20%">
                        <f:facet name="header">
                            <h:outputText value="Multa/Juros"/>
                        </f:facet>
                        <h:outputText value="#{parcelaContrato.valorMultaJuros}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>


                </rich:dataTable>

                <rich:spacer height="10px" rendered="#{item.exibeParcelas && !item.emptyParcelasPagasCE}"/>
                <center>
                    <h:outputText rendered="#{item.exibeParcelas && !item.emptyParcelasPagasCE}"
                                  value="PAGAMENTOS EFETUADOS" styleClass="tituloCamposNegritoMenor"/>
                </center>
                <rich:spacer height="10px" rendered="#{item.exibeParcelas && !item.emptyParcelasPagasCE}"/>

                <rich:dataTable id="tabelaParcelasPagas" width="100%" columnClasses="colunaCentralizada"
                                value="#{item.pagamentosCE}" var="historicoPagamentos"
                                rendered="#{item.exibeParcelas && !item.emptyParcelasPagasCE}">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="#{msg_aplic.prt_HistoricoComprasCliente_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{historicoPagamentos.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="#{msg_aplic.prt_HistoricoComprasCliente_nrRecibo}"/>
                        </f:facet>
                        <h:outputText value="#{historicoPagamentos.reciboPagamento.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="#{msg_aplic.prt_HistoricoComprasCliente_nomePagador}"/>
                        </f:facet>
                        <h:outputText value="#{historicoPagamentos.nomePagador}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}"/>
                        </f:facet>
                        <h:outputText value="#{historicoPagamentos.dataLancamento}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="#{msg_aplic.prt_HistoricoComprasCliente_formaPagamento}"/>
                        </f:facet>
                        <h:outputText value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Nr. vezes"/>
                        </f:facet>
                        <h:outputText value="#{historicoPagamentos.nrVezes}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="#{msg_aplic.prt_HistoricoComprasCliente_valor}"/>
                        </f:facet>
                        <h:outputText value="#{historicoPagamentos.valorTotal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>


                </rich:dataTable>
            </h:column>

            <h:column id="t2">
                <h:panelGroup layout="block" style="vertical-align:top;text-align:center;">

                    <h:panelGroup rendered="#{MovParcelaControle.apresentarRenegociarParcelas}">
                        <a4j:commandLink action="#{MovParcelaControle.iniciarRenegociacaoParcelas}"
                                         oncomplete="#{MovParcelaControle.mensagemNotificar}; #{MovParcelaControle.msgAlert}"
                                         reRender="mensagem,mdlRenegociar,formBoletoPendente"
                                         id="renegociarParcelas"
                                         title="Renegociar parcelas"
                                         style="font-size: large">
                            <i class="fa-icon-refresh"></i>
                        </a4j:commandLink>
                        <rich:spacer width="10px"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{MovParcelaControle.apresentarImprimirComprovanteCompra && !item.centralEventos && item.exibirBotoesVendaAvulsa}">
                        <a4j:commandLink id="imprimirComprovanteCompra"
                                         style="font-size: large"
                                         oncomplete="#{MovParcelaControle.mensagemNotificar};#{MovParcelaControle.msgAlert}"
                                         action="#{MovParcelaControle.imprimirComprovanteCompra}"
                                         title="Imprimir Comprovante de compra">
                            <h:graphicImage url="imagens/pct-comprovante-compra.svg"
                                            style="height: 19px;vertical-align: middle;"/>
                        </a4j:commandLink>
                        <rich:spacer width="10px"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{item.exibirBotoesContrato && !item.centralEventos}">
                        <a4j:commandLink
                                oncomplete="abrirPopup('VisualizarContrato', 'RelatorioContrato', 730, 545);"
                                action="#{MovParcelaControle.gerarImpressaoContrato}"
                                id="imprimirContrato"
                                style="font-size: large"
                                title="Imprimir o Contrato">
                            <i class="fa-icon-print"></i>
                        </a4j:commandLink>
                        <rich:spacer width="10px"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{item.exibirBotoesCliente && !item.centralEventos}">
                        <a4j:commandLink action="#{ClienteControle.validarTelaParcelasPagamento}"
                                         oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente&jsessionid=#{ClienteControle.session.id}', 'Cliente', 1024, 700);"
                                         id="visualizarCliente"
                                         title="Visualizar o Cliente"
                                         style="font-size: large">
                            <i class="fa-icon-search"></i>
                        </a4j:commandLink>
                        <rich:spacer width="10px"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{MovParcelaControle.permiteAcessarColaborador && item.exibirBotoesColaborador && !item.centralEventos}">
                        <a4j:commandLink action="#{ClienteControle.validarTelaParcelasPagamento}"
                                         oncomplete="abrirPopup('colaboradorForm.jsp', 'Colaborador', 1024, 700);"
                                         id="visualizarColaborador"
                                         title="Visualizar o Colaborador"
                                         style="font-size: large">
                            <i class="fa-icon-search" style="color: #BE2E26;"></i>
                        </a4j:commandLink>
                        <rich:spacer width="10px"/>
                    </h:panelGroup>

                    <a4j:commandLink rendered="#{item.centralEventos}"
                                     action="#{CadastroInicialControle.abrirDetalhamentoSemRedirecionar}"
                                     actionListener="#{CadastroInicialControle.selCadastroInicialListener}"
                                     oncomplete="abrirPopup('pages/ce/eventos/detalhamentoEvento.jsp', 'DetalhamentoEvento', 1024, 700);">
                        <h:graphicImage url="/imagens/botaoVisualizar.png" title="Visualizar o Evento"
                                        height="18px"
                                        width="18px"
                                        style="border:none;">
                        </h:graphicImage>
                        <f:attribute name="codigoEventoInteresse" value="#{item.codigoEvento}"/>
                    </a4j:commandLink>

                </h:panelGroup>
            </h:column>


        </rich:dataTable>
        <!-- PAGINADOR EM BANCO -->
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" id="paginacao">
            <h:panelGroup id="painelPaginacao" rendered="#{MovParcelaControle.apresentarPaginacao}">
                <a4j:commandLink id="pagiInicial" styleClass="tituloCampos9" value="  <<  "
                                 reRender="tabelaItens, paginaAtual, painelPaginacao,painelPaginacao2"
                                 rendered="#{MovParcelaControle.confPaginacao.apresentarPrimeiro}"
                                 actionListener="#{MovParcelaControle.caixaAbertoListener}">
                    <f:attribute name="pagNavegacao" value="pagInicial"/>
                </a4j:commandLink>
                <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos9" value="  <  "
                                 reRender="tabelaItens, paginaAtual, painelPaginacao,painelPaginacao2"
                                 rendered="#{MovParcelaControle.confPaginacao.apresentarAnterior}"
                                 actionListener="#{MovParcelaControle.caixaAbertoListener}">
                    <f:attribute name="pagNavegacao" value="pagAnterior"/>
                </a4j:commandLink>
                <h:outputText id="paginaAtual" styleClass="tituloCampos9"
                              value="#{msg_aplic.prt_msg_pagina} #{MovParcelaControle.confPaginacao.paginaAtualDeTodas}"
                              rendered="true"/>
                <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos9" value="  >  "
                                 reRender="tabelaItens, paginaAtual, painelPaginacao,painelPaginacao2"
                                 rendered="#{MovParcelaControle.confPaginacao.apresentarPosterior}"
                                 actionListener="#{MovParcelaControle.caixaAbertoListener}">
                    <f:attribute name="pagNavegacao" value="pagPosterior"/>
                </a4j:commandLink>
                <a4j:commandLink id="pagiFinal" styleClass="tituloCampos9" value="  >>  "
                                 reRender="tabelaItens, paginaAtual, painelPaginacao,painelPaginacao2"
                                 rendered="#{MovParcelaControle.confPaginacao.apresentarUltimo}"
                                 actionListener="#{MovParcelaControle.caixaAbertoListener}">
                    <f:attribute name="pagNavegacao" value="pagFinal"/>
                </a4j:commandLink>
                <h:outputText id="totalItens" styleClass="tituloCampos9"
                              value=" [#{msg_aplic.prt_msg_itens} #{MovParcelaControle.confPaginacao.numeroTotalItens}]"
                              rendered="true"/>
            </h:panelGroup>
        </h:panelGrid>

        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td>
                    <table width="100%" height="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td align="left" valign="bottom">
                                <c:if test="${SuperControle.internetExplorer}">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                       class="tablepreviewtotal">
                                    <tr>
                                        <td align="right" valign="middle"><label>
                                            <span style="font-weight: bold">Responsável Pagamento:</span></label></td>
                                        <td align="left" valign="middle">
                                            <h:selectOneMenu id="responsavelAbaixo"
                                                             value="#{MovParcelaControle.numeroContratoResponsavel}"
                                                             styleClass="form" onfocus="focusinput(this);"
                                                             onblur="blurinput(this);">
                                                <a4j:support event="onchange"
                                                             action="#{MovParcelaControle.selecionarResponsavelPagamento}"
                                                             reRender="responsavelAbaixo,responsavel,gerarBoleto,renegociarParcelas"/>
                                                <f:selectItems value="#{MovParcelaControle.listaResponsavelPagamento}"/>
                                            </h:selectOneMenu>
                                        </td>
                                    </tr>
                                </table>

                                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                       class="tablepreviewtotal">
                                    <tr>
                                        <td align="right" valign="middle">
                                            TOTAL FINAL:
                                                                <span class="verde"><h:outputText value="#{CaixaControle.empresaLogado.moeda}"/>
                                                                    <h:outputText id="valorAbaixo"
                                                                                  value="#{MovParcelaControle.valorTotalParcela}">
                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                    </h:outputText>
                                                                </span>
                                        </td>
                                    </tr>
                                </table>


                                <!-- fim item -->

                            </td>
                        </tr>
                        <tr>
                            <td align="right" colspan="2">
                                <a4j:commandLink id="btnCancelarParcelasIEInferior" title="Cancelar Parcelas"
                                                 style="height:36px;line-height:36px; "
                                                 action="#{MovParcelaControle.validarCancelarParcelas}"
                                                 oncomplete="#{MovParcelaControle.mensagemNotificar};#{MovParcelaControle.fecharRichModalPanelConfirmacao}"
                                                 reRender="mensagem, formBoletoPendente">
                                    <img src="${root}/images/btn_cancelarParcelaG.png" class="imgalpha"
                                         title="Cancelar Parcelas">
                                </a4j:commandLink>
                                <a4j:commandLink id="btnReceberCaixaAberto"
                                               action="#{MovParcelaControle.validarListaParcelasPagarView}"
                                                 reRender="formBoletoPendente"
                                               oncomplete="#{MovParcelaControle.msgAlert eq '' ? MovParcelaControle.mensagemNotificar : MovParcelaControle.msgAlert}">
                                    <img src="${root}/images/btn_receber.gif" class="imgalpha" title="Receber">
                                </a4j:commandLink>
                            </td>
                        </tr>
                        </c:if>
                    </table>
                </td>
            </tr>

        </table>

    </h:panelGroup>

</h:panelGroup>

<a4j:jsFunction name="abrirNovaCaixaAbertoPadrao"
                oncomplete="#{MovParcelaControle.msgAlert}"
                action="#{MovParcelaControle.abrirNovaTela}">
</a4j:jsFunction>
<a4j:jsFunction name="verificarAbrirNovaTelaCaixa" status="false"
                oncomplete="#{MovParcelaControle.msgAlert}"
                action="#{MovParcelaControle.verificarAbrirNovaTela}">
</a4j:jsFunction>

<script>
    const novaversao_second = 1000;
    const novaversao_minute = novaversao_second * 60;
    const novaversao_hour = novaversao_minute * 60;
    const novaversao_day = novaversao_hour * 24;
    let novaversao_dataDesativar = null;

    let novaversao_count_down = null;
    let novaversao_x = null;

    async function obterDataDesativarNovaTela() {
        if (novaversao_dataDesativar == null) {
            var url = '${SuperControle.urloamdrecursomigracao}/prest/migracao-recurso/data-migracao/${LoginControle.key}/CAIXA_ABERTO';
            const response =  fetch(url, {
                method: "GET",
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = (await response).json();
            return data;
        }
    }

    function countDownDesativarNovaTelaAluno() {
        let now = new Date(Date.now()).getTime();
        let diff = novaversao_count_down - now;
        if (diff < 0) {
            diff = 0;
        }
        document.getElementById('form:qtdDiasNovaVersao').innerText = Math.floor(diff / novaversao_day);
        document.getElementById('form:qtdHorasNovaVersao').innerText = Math.floor(diff % novaversao_day / novaversao_hour);
        document.getElementById('form:qtdMinutosNovaVersao').innerText = Math.floor(diff % novaversao_hour / novaversao_minute);
        document.getElementById('form:qtdSegundosNovaVersao').innerText = Math.floor(diff % novaversao_minute / novaversao_second);
    }

    function iniciarCountDownDesativarNovaTelaCaixa() {
        try {
            if (novaversao_x) {
                clearInterval(novaversao_x);
            }
            obterDataDesativarNovaTela().then((resultado) => {
                if (resultado.content) {
                    novaversao_dataDesativar = resultado.content;
                    const dataNova = new Date(resultado.content);
                    document.getElementById('form:novaVersaoTelaAlunoData').innerText = dataNova.toLocaleDateString();
                    novaversao_count_down = dataNova;
                    novaversao_x = setInterval(() => countDownDesativarNovaTelaAluno(), novaversao_second);
                    const divContadorNovaVersaoTelaAluno = document.getElementById('form:divContadorNovaVersaoTelaAluno');
                    if (divContadorNovaVersaoTelaAluno) {
                        divContadorNovaVersaoTelaAluno.style.display = 'block';
                    }
                    const divMsgExperimenteNovaVersao = document.getElementById('form:divMsgExperimenteNovaVersao');
                    if (divMsgExperimenteNovaVersao) {
                        divMsgExperimenteNovaVersao.style.display = 'none';
                    }
                }
            })
        } catch (e) {
            console.log(e);
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        iniciarCountDownDesativarNovaTelaCaixa();
        try {
            verificarAbrirNovaTelaCaixa();
        } catch (e) {
            console.log(e);
        }
    });
</script>