<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<%@include file="/includes/imports.jsp" %>

<head>
    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
    <style type="text/css">
        .legend {
            font-size: 12px;
            font-weight: bold;
            color: #0F4C6B;
        }

        fieldset {
            margin-top: 5px;
            margin-bottom: 5px;
        }

        ::-webkit-scrollbar{width:8px;height:6px;margin-right:2px;}
        ::-webkit-scrollbar-button:start:decrement,::-webkit-scrollbar-button:end:increment{display:block;height:0px;}
        ::-webkit-scrollbar-button:vertical:end:increment{background:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-top:1px solid #ccc;}
        ::-webkit-scrollbar-button:vertical:increment{background-color:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-bottom:1px solid #ccc;border-top:1px solid #ccc;}
        ::-webkit-scrollbar-track-piece{background:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-top:1px solid #ccc;}
        ::-webkit-scrollbar-thumb:vertical{background-color:#ccc;}
        ::-webkit-scrollbar-thumb:vertical:hover{background-color:#666;}
        ::-webkit-scrollbar-thumb:vertical:active{background-color:#333;}

        .fs {
            min-height: 40px;
        }

        .rich-fileupload-list-decor {
            border: none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-fileupload-toolbar-decor {
            background-color: transparent;
            border: none;
        }

        .rich-fileupload-table-td {
            border: none;
        }

        .rich-panel {
            background-color: transparent;
            border: none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-panel-body {
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-fileupload-ico-add {
            background-image: url(images/drive-upload.png);
        }

        .rich-fileupload-button {
            background-color: transparent;
            background-image: none;
        }

        .rich-fileupload-button-border {
            border: none;
        }

        .rich-fileupload-button-light, .rich-fileupload-button-press {
            background-image: none;
            background-color: transparent;
        }

        /* CUSTOMIZE THE CAROUSEL
            -------------------------------------------------- */

        /* Carousel base class */
        .carousel {
            position: relative;
            line-height: 1;
        }

        .carousel-inner {
            position: relative;
            width: 100%;
            overflow: hidden;
        }

        .carousel-inner > .item {
            position: relative;
            display: none;
            -webkit-transition: 0.6s ease-in-out left;
            -moz-transition: 0.6s ease-in-out left;
            -o-transition: 0.6s ease-in-out left;
            transition: 0.6s ease-in-out left;
        }

        .carousel-inner > .item > img,
        .carousel-inner > .item > a > img {
            display: block;
            line-height: 1;
        }

        .carousel-inner > .active,
        .carousel-inner > .next,
        .carousel-inner > .prev {
            display: block;
        }

        .carousel-inner > .active {
            left: 0;
        }

        .carousel-inner > .next,
        .carousel-inner > .prev {
            position: absolute;
            top: 0;
            width: 100%;
        }

        .carousel-inner > .next {
            left: 100%;
        }

        .carousel-inner > .prev {
            left: -100%;
        }

        .carousel-inner > .next.left,
        .carousel-inner > .prev.right {
            left: 0;
        }

        .carousel-inner > .active.left {
            left: -100%;
        }

        .carousel-inner > .active.right {
            left: 100%;
        }

        .carousel-control {
            position: absolute;
            top: 40%;
            left: 15px;
            width: 40px;
            height: 40px;
            margin-top: -20px;
            font-size: 60px;
            font-weight: 100;
            line-height: 30px;
            color: #ffffff;
            text-align: center;
            background: #222222;
            border: 3px solid #ffffff;
            -webkit-border-radius: 23px;
            -moz-border-radius: 23px;
            border-radius: 23px;
            opacity: 0.5;
            filter: alpha(opacity=50);
        }

        .carousel-control.right {
            right: 15px;
            left: auto;
        }

        .carousel-control:hover,
        .carousel-control:focus {
            color: #ffffff;
            text-decoration: none;
            opacity: 0.9;
            filter: alpha(opacity=90);
        }

        .carousel-indicators {
            position: absolute;
            top: 15px;
            right: 0px;
            z-index: 2;
            margin: 0;
            list-style: none;
            left: 50%;
        }

        .carousel-indicators li {
            display: block;
            float: left;
            width: 10px;
            height: 10px;
            margin-left: 5px;
            text-indent: -999px;
            background-color: #CCBEBE;
            background-color: rgba(204, 190, 190, 0.25);
            border-radius: 5px;
        }

        .carousel-indicators .active {
            background-color: #626469;
        }

        .carousel-caption {
            position: absolute;
            right: 0;
            bottom: 0;
            left: 0;
            padding: 15px;
            background: #444444;
            background: rgba(0, 0, 0, 0.75);
        }

        .carousel-caption h4,
        .carousel-caption p {
            line-height: 20px;
            color: #ffffff;
        }

        .carousel-caption h4 {
            margin: 0 0 5px;
        }

        .carousel-caption p {
            margin-bottom: 0;
        }
    </style>
    <%--<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">--%>
    <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="bootstrap/bootstrap-transition.js"></script>
    <script type="text/javascript" src="bootstrap/bootstrap-carousel.js"></script>
    <script>
        jQuery(document).ready(function ($) {
            $("#myCarousel").carousel({interval: 10000});
        });
    </script>
</head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de Boletos Online"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <h:form id="form" prependId="true" styleClass="pure-form no-ext-css">
        <a4j:keepAlive beanName="GestaoBoletosOnlineControle"/>
        <html>
            <body>
                <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                    <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                        <jsp:include page="include_topo_novo.jsp" flush="true"/>
                        <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial container-conteudo-central">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Gestão de Boletos Online" styleClass="container-header-titulo"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGroup id="panelConteudo">
                                        <h:panelGrid cellpadding="0" cellspacing="0" columns="1" width="100%">
                                            <h:panelGroup id="panelOpcoes">
                                                <fieldset>
                                                    <legend class="legend">Opções</legend>
                                                    <h:panelGroup layout="block" id="panelGeralRemessa"
                                                                  style="display: inline-flex; #{empty SuporteControle.bannersRemessa ? '' : 'width: 100%'}">
                                                        <h:panelGroup layout="block" id="panelSuperior"
                                                                      style="#{empty SuporteControle.bannersRemessa ? 'width: 100%' : 'width: 35%'}; padding-right: 10px;">
                                                        <h:panelGroup/>

                                                        <h:panelGroup>
                                                            <fieldset class="fs" style="vertical-align:top;">
                                                                <a4j:outputPanel rendered="#{GestaoBoletosOnlineControle.usuarioLogado.administrador || GestaoBoletosOnlineControle.consultarInfoTodasEmpresas}">
                                                                    <h:panelGroup>
                                                                        <h:outputText style="vertical-align:middle;" styleClass="tituloCampos" value="Empresa:" />
                                                                        <rich:spacer width="10px" />
                                                                        <h:selectOneMenu  onblur="blurinput(this);"
                                                                                          id="filtroEmpresa"
                                                                                          style="margin:6 6 6 6;"
                                                                                          onfocus="focusinput(this);"
                                                                                          value="#{GestaoBoletosOnlineControle.empresaSelecionada}" >
                                                                            <f:selectItems  value="#{GestaoBoletosOnlineControle.listaSelectItemEmpresa}" />
                                                                            <a4j:support
                                                                                    event="onchange"
                                                                                    action="#{GestaoBoletosOnlineControle.selecionouEmpresaCarregarConvenios}"
                                                                                    reRender="panelConteudo, panelOpcoes"/>
                                                                        </h:selectOneMenu>
                                                                        <rich:spacer width="10px"/>
                                                                    </h:panelGroup>
                                                                </a4j:outputPanel>

                                                                <h:panelGrid columns="2"
                                                                             columnClasses="esquerda, esquerda">

                                                                    <h:outputText style="vertical-align:middle;"
                                                                                  styleClass="tituloCampos"
                                                                                  value="Convênio Cobrança: "/>
                                                                    <h:selectOneMenu
                                                                            style="vertical-align:middle;"
                                                                            id="comboConvenios"
                                                                            value="#{GestaoBoletosOnlineControle.convenioSelecionado}">
                                                                        <f:selectItems
                                                                                value="#{GestaoBoletosOnlineControle.conveniosSelectItens}"/>
                                                                        <a4j:support event="onchange"
                                                                                     action="#{GestaoBoletosOnlineControle.selecionarConvenio}"
                                                                                     reRender="panelConteudo, panelOpcoes"/>
                                                                    </h:selectOneMenu>
                                                                </h:panelGrid>
                                                            </fieldset>
                                                        </h:panelGroup>

                                                        <h:panelGroup>
                                                            <fieldset class="fs" style="vertical-align:top;">
                                                                <legend class="legend">
                                                                    <h:outputText rendered="#{GestaoBoletosOnlineControle.abaSelecionada == 'abaBoletos'}" value="Data Geração dos Boletos"/>
                                                                    <h:outputText rendered="#{GestaoBoletosOnlineControle.abaSelecionada == 'abaParcelas'}" value="Vencimento Parcelas"/>
                                                                </legend>

                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="tituloCampos"
                                                                                  style="vertical-align:middle;"
                                                                                  value="De: "/>
                                                                    <rich:calendar id="dataInicio"
                                                                                   value="#{GestaoBoletosOnlineControle.dataInicio}"
                                                                                   inputSize="10"
                                                                                   inputClass="form calendarIndexRemessas"
                                                                                   oninputblur="blurinput(this);"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputchange="return validar_Data(this.id);"
                                                                                   datePattern="dd/MM/yyyy"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false"/>

                                                                    <rich:spacer width="20px"/>

                                                                    <h:outputText styleClass="tituloCampos"
                                                                                  style="vertical-align:middle;"
                                                                                  value="Até: "/>
                                                                    <rich:calendar id="dataTermino"
                                                                                   value="#{GestaoBoletosOnlineControle.dataFim}"
                                                                                   inputSize="10"
                                                                                   inputClass="form calendarIndexRemessas"
                                                                                   oninputblur="blurinput(this);"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputchange="return validar_Data(this.id);"
                                                                                   datePattern="dd/MM/yyyy"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false"/>
                                                                </h:panelGroup>
                                                            </fieldset>
                                                        </h:panelGroup>

                                                        <h:panelGrid id="mensagem" columns="1" width="100%"
                                                                     style="margin:0 0 0 0;">
                                                            <h:outputText escape="false" styleClass="mensagemVerde"
                                                                          value="#{GestaoBoletosOnlineControle.mensagem}"/>
                                                            <h:outputText escape="false"
                                                                          styleClass="mensagemDetalhada"
                                                                          value="#{GestaoBoletosOnlineControle.mensagemDetalhada}"/>
                                                        </h:panelGrid>

                                                        <h:panelGroup layout="block" id="panelCarousel"
                                                                      style="#{empty SuporteControle.bannersRemessa ? '' : 'width: 65%'}"
                                                                      rendered="#{not empty SuporteControle.bannersRemessa}">
                                                            <div id="myCarousel"
                                                                 class="carousel slide container-conteudo-central">
                                                                <div class="carousel-inner">
                                                                    <c:forEach
                                                                            items="${SuporteControle.bannersRemessa}"
                                                                            var="ban" varStatus="ind">
                                                                        <div class="item ${ind.count == 1 ? 'active' : ''}">
                                                                            <a
                                                                                    <c:if test="${not empty ban.urlLink}">href="${ban.urlLink}"</c:if>
                                                                                    target="_blank">
                                                                                <img border="none"
                                                                                     style="width: 100%;"
                                                                                     src="${ban.urlImagem}">
                                                                            </a>
                                                                        </div>
                                                                    </c:forEach>
                                                                </div>

                                                                <ol class="carousel-indicators">
                                                                    <c:forEach
                                                                            items="${SuporteControle.bannersRemessa}"
                                                                            var="ban"
                                                                            varStatus="ind">
                                                                        <li style="cursor: pointer;"
                                                                            data-target="#myCarousel"
                                                                            data-slide-to="${ind.count -1}"
                                                                            class="${ind.count == 1 ? 'active' : ''}"></li>
                                                                    </c:forEach>
                                                                </ol>
                                                            </div>
                                                        </h:panelGroup>

                                                        <h:panelGroup layout="block">
                                                            <br/>
                                                            <br/>
                                                            <h:panelGroup styleClass="tituloCampos"
                                                                          style="vertical-align:middle;">
                                                                <a4j:commandLink id="btnConsultarGestaoBoletosOnline"
                                                                                 value="Consultar"
                                                                                 styleClass="pure-button pure-button-primary pure-button-small"
                                                                                 action="#{GestaoBoletosOnlineControle.consultarInformacoes}"
                                                                                 reRender="panelConteudo,mensagem,richPanel"/>
                                                            </h:panelGroup>
                                                            <br/>
                                                            <br/>
                                                        </h:panelGroup>
                                                    </h:panelGroup>
                                                </fieldset>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                    <rich:tabPanel id="richPanel"
                                                   tabClass="aba"
                                                   width="100%"
                                                   switchType="ajax"
                                                   immediate="true"
                                                   headerAlignment="left"
                                                   selectedTab="#{GestaoBoletosOnlineControle.abaSelecionada}">

                                        <rich:tab label="Consulta Boletos"
                                                  reRender="panelConteudo"
                                                  id="abaBoletos">
                                            <h:panelGrid columns="1" width="100%">
                                                <h:panelGroup>
                                                    <rich:dataTable rowKeyVar="status" id="tblBoletosGerarBoletoOnline" width="100%" styleClass="pure-table-striped"
                                                                    columnClasses="colunaCentralizada" value="#{GestaoBoletosOnlineControle.listaBoletos}"
                                                                    rows="#{DataScrollerControle.nrRows}" var="boleto">
                                                        <%@include file="/includes/remessas/include_dadosBoletosGestaoBoletosOnline.jsp" %>
                                                    </rich:dataTable>
                                                    <rich:datascroller for="tblBoletosGerarBoletoOnline" status="false" renderIfSinglePage="false"/>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                        </rich:tab>

                                        <rich:tab label="Consulta Parcelas"
                                                  reRender="panelConteudo"
                                                  id="abaParcelas">
                                            <h:panelGrid columns="1" width="100%">
                                                <h:panelGroup>
                                                    <rich:dataTable rowKeyVar="status" id="tblParcelasGerarBoletoOnline" width="100%" styleClass="pure-table-striped"
                                                                    columnClasses="colunaCentralizada" value="#{GestaoBoletosOnlineControle.listaParcelas}"
                                                                    rows="#{DataScrollerControle.nrRows}" var="parc">
                                                        <%@include file="/includes/remessas/include_dadosParcelaBoletoOnline.jsp" %>
                                                    </rich:dataTable>
                                                    <rich:datascroller for="tblParcelasGerarBoletoOnline" status="false" renderIfSinglePage="false"/>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                        </rich:tab>
                                    </rich:tabPanel>

                                    <c:if test="${GestaoBoletosOnlineControle.exibirCamposGerarBoleto()}">

                                        <h:panelGroup layout="block">
                                            <br/>
                                            <br/>
                                            <h:panelGroup styleClass="tituloCampos"
                                                          style="vertical-align:middle;">
                                                <h:outputText styleClass="tituloCampos"
                                                              style="vertical-align:middle;"
                                                              value="Data para Vencimento dos Boletos: "/>

                                                <rich:spacer width="10px" />

                                                <rich:calendar id="dataVencimentoBoletos"
                                                               value="#{GestaoBoletosOnlineControle.dataVencimentoBoletos}"
                                                               inputSize="10"
                                                               inputClass="form calendarIndexRemessas"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false"/>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block">
                                            <br/>
                                            <br/>
                                            <h:panelGroup styleClass="tituloCampos"
                                                          style="vertical-align:middle;">
                                                <h:outputText style="vertical-align:middle;"
                                                              styleClass="tituloCampos"
                                                              value="E-mail para Notificação de Término:"/>
                                                <rich:spacer width="10px" />

                                                <h:inputText id="inputEmailNotificacaoTermino"
                                                             size="50"
                                                             value="#{GestaoBoletosOnlineControle.emailNotificacaoTerminoProcesso}"/>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block">
                                            <br/>
                                            <br/>
                                            <h:panelGroup styleClass="tituloCampos"
                                                          style="vertical-align:middle;">
                                                <a4j:commandLink id="gerarBoletosOnline"
                                                                 value="Gerar Boletos"
                                                                 styleClass="pure-button pure-button-primary pure-button-small"
                                                                 action="#{GestaoBoletosOnlineControle.processoGerarBoletosOnline}"
                                                                 reRender="mensagem"/>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                    </c:if>

                                    <rich:jQuery id="mskData" selector=".calendarIndexRemessas" timing="onload" query="mask('99/99/9999')" />
                                </h:panelGroup>
                            </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <c:if test="${SuperControle.menuZwUi}">
                            <jsp:include page="include_box_menulateral.jsp">
                                <jsp:param name="menu" value="ADM-INICIO" />
                            </jsp:include>
                        </c:if>
                    </h:panelGroup>
                    <jsp:include page="include_rodape_flat.jsp" flush="true" />
                </h:panelGroup>
            </body>
        </html>
    </h:form>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="true"/>
    <jsp:include page="includes/autorizacao/include_autorizacao_funcionalidade.jsp" flush="true"/>
    <jsp:include page="includes/parcelas/include_modal_alterarVencimentoParcelas.jsp" flush="true"/>

</f:view>
