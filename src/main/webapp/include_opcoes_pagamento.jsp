<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>
<link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="script/opcoespagamento.js"></script>
<link href="css/pix/pix.css" rel="stylesheet" type="text/css">

<h:panelGroup id="opcoesFormaPagamento">
    <a4j:repeat id="opcoesFormasPagamento" value="#{MovPagamentoControle.listaSelectItemMovPagamento}"
                var="movPagamento" rowKeyVar="idx">
        <h:panelGroup layout="block"
                      styleClass="opcaoPagamento formapag#{idx} #{movPagamento.movPagamentoEscolhida ? 'selecionado' : ''} #{movPagamento.pagamentoAberto ? 'aberto' : ''}"
                      rendered="#{movPagamento.naoApresentarContaCorrente}">

            <h:panelGroup layout="block" styleClass="opcaoPagamentoHead">
                <div class="chk-fa-container inline " id="divFormaPagamentoEscolhido">
                    <h:selectBooleanCheckbox id="movPagamentoEscolhido"
                                             styleClass="FORMAPAGAMENTO-#{movPagamento.formaPagamento.codigo}"
                                             value="#{movPagamento.movPagamentoEscolhida}"
                                             disabled="#{not empty movPagamento.observacao and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad}">
                        <a4j:support event="onclick" id="testeId"
                                     focus="movPagamentoEscolhido"
                                     oncomplete="#{MovPagamentoControle.msgAlert}#{MovPagamentoControle.autorizacao}"
                                     action="#{MovPagamentoControle.calcularPagamentoSeletcCheckbox}"
                                     reRender="escolhaFormaPagamento, containerTotalizadores, painelCappta, panelPinPad,
                                                                                                         mensagemSup,mensagemInf,formCheque,panelBotoesControle,
                                                                                                         formAutorizacao,
                                                                                                         escolhaFormaPagamentoCC,totalLancado,residuo,panelCobrancaMultaJuros,autorizacaoCC"/>
                    </h:selectBooleanCheckbox>
                    <span/>
                </div>

                <a4j:commandLink action="#{MovPagamentoControle.checkAberto}" status="false"
                                 oncomplete="#{MovPagamentoControle.msgAlert}#{MovPagamentoControle.autorizacao}"
                                 reRender="escolhaFormaPagamento, containerTotalizadores, painelCappta, panelPinPad,
                                                                                                         mensagemSup,mensagemInf,formCheque,panelBotoesControle,
                                                                                                         formAutorizacao,
                                                                                                         escolhaFormaPagamentoCC,totalLancado,residuo,panelCobrancaMultaJuros,autorizacaoCC">
                    <h:outputText styleClass="cinza negrito"
                                  rendered="#{!movPagamento.opcaoPagamentoParceiroFidelidade}"
                                  value="#{movPagamento.formaPagamento.descricao}"></h:outputText>
                    <h:panelGroup layout="block" rendered="#{movPagamento.opcaoPagamentoParceiroFidelidade}">
                        <img src="images/logo-dotz.png" width="50" height="35" style="vertical-align: middle;">
                    </h:panelGroup>
                    <i class="fa-icon-chevron-down"></i>
                    <i class="fa-icon-chevron-up"></i>
                    <h:outputText styleClass="cinza negrito valortotal"
                                  rendered="#{movPagamento.movPagamentoEscolhida and movPagamento.valorTotal > 0.0}"
                                  value="#{MovPagamentoControle.empresaLogado.moeda} #{movPagamento.valorTotal_Apresentar}"></h:outputText>

                </a4j:commandLink>
            </h:panelGroup>


            <!-- ################################## BODY ######################################### -->


            <h:panelGroup styleClass="opcaoPagamentoBody " layout="block">

                <div class="bloco">
                    <span class="cinza negrito upper flex">
                        <h:outputText value="VALOR:"
                                      rendered="#{!movPagamento.opcaoPagamentoParceiroFidelidade}"/>
                        <h:outputText value="VALOR EM REAIS:"
                                      rendered="#{movPagamento.opcaoPagamentoParceiroFidelidade}"/>
                    </span>
                    <h:inputText disabled="#{!movPagamento.movPagamentoEscolhida || movPagamento.opcaoPagamentoParceiroFidelidade ||
                                            (movPagamento.movPagamentoEscolhida and not empty movPagamento.observacao
                                            and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad)}"
                                 id="valorpago"
                                 value="#{movPagamento.valorTotal}"
                                 style="top:0px;"
                                 styleClass="inputTextClean FORMAPAGAMENTO-VALOR-#{movPagamento.formaPagamento.codigo}">
                        <a4j:support event="onblur"
                                     action="#{MovPagamentoControle.calcularEncapsulado}"
                                     reRender="escolhaFormaPagamento, totalLancado, residuo,mensagemSup, mensagemInf,panelBotoesControle,
                                     panelCobrancaMultaJuros,panelSaldoContaCorrente,escolhaFormaPagamentoCC, containerTotalizadores"/>
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:inputText>
                </div>

                <%--PIX--%>
                <h:panelGroup rendered="#{movPagamento.formaPagamento.pix}">
                    <h:panelGroup styleClass="bloco">
                    <span class="cinza negrito upper flex"><h:outputText
                            value="Convênio:"></h:outputText>
                    </span>
                        <h:panelGroup layout="block" styleClass="block cb-container">
                            <h:selectOneMenu
                                    id="conveniosPix"
                                    value="#{MovPagamentoControle.convenioPix}"
                                    onblur="blurinput(this);" onfocus="focusinput(this);"
                                    styleClass="form"
                                    style="top:0px;">
                                <a4j:support event="onchange"
                                             action="#{MovPagamentoControle.acaoSelecionarConvenioPix}"
                                             reRender="form:geralnovopagamento, qrCodePix, modalPix"
                                             oncomplete="#{MovPagamentoControle.msgAlert}"/>
                                <f:selectItems
                                        value="#{MovPagamentoControle.listaSelectItemConvenioCobrancaPix}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGroup>

                    <%-- CPF Pix--%>
                    <h:panelGroup styleClass="bloco" rendered="#{MovPagamentoControle.apresentarCPFPix}">
                        <h:panelGroup layout="block" id="cpfPixLabelTes" style="margin-bottom: -4px;">
                            <h:outputText styleClass="cinza negrito upper flex" value="CPF:"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="block">
                            <h:inputText id="cpfPix"
                                         value="#{MovPagamentoControle.cpfPix}"
                                         onblur="blurinput(this);"
                                         onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                         onfocus="focusinput(this);" size="12" maxlength="14"
                                         styleClass="inputTextClean"/>
                            <a4j:commandLink action="#{MovPagamentoControle.alterarCPFPessoa}"
                                             oncomplete="#{MovPagamentoControle.mensagemNotificar}"
                                             title="Gravar o CPF no cadastro da pessoa"
                                             styleClass="tooltipster"
                                             style="padding-left: 5px"
                                             reRender="form, qrCodePix, modalPix"
                                             id="btnGravarCpfPixConsumidor"
                                             value="Gravar CPF"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block"
                                  style="display: flex; justify-content: center; align-items: center; flex-direction: column;"
                                  rendered="#{!MovPagamentoControle.existeConvenioPix}">
                        <span>Você precisa cadastrar um convênio de cobrança pix</span>
                        <a4j:commandLink action="#{MovPagamentoControle.abrirCadastroConvenio}"
                                         oncomplete="#{MovPagamentoControle.onCompleteJs}"
                                         value="Clique aqui para cadastrar"/>
                    </h:panelGroup>


                    <h:panelGroup layout="block"
                                  style="display: flex; justify-content: center; align-items: center; flex-direction: column;"
                                  rendered="#{MovPagamentoControle.existeConvenioPix}">

                        <h:panelGroup layout="block"
                                      style="display: flex; margin-left: 20px; margin-top: 10px; color: red;">
                            <h:outputText value="#{MovPagamentoControle.mensagemDetalhada}"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block"
                                      style="display: flex; justify-content: center; align-items: center"
                                      rendered="#{MovPagamentoControle.pixGerado}">
                            <div class="pix-qr-container" id="qrCodePix">
                                <a4j:poll status="false"
                                          enabled="#{MovPagamentoControle.confirmarCobrancaPix}"
                                          interval="10000"
                                          oncomplete="#{MovPagamentoControle.onCompleteJs}"
                                          action="#{MovPagamentoControle.consultarCobrancaPix}"
                                          reRender="modalPix,msgConfirmacaoPix,formConfirmacaoPagamento,form:geralnovopagamento,geralnovopagamento,mensagemSup,panelSaldoContaCorrente,dataPagto,panelCobrancaMultaJuros,form:panelBotoesControle,form:panelEsconderControles,form:panelEsconderControles,form:panelEsconderControles2,form:panelEsconderControles3, form:tituloFormasPagamento,residuo,qrCodePix,conveniosPix"/>
                                <h:panelGroup layout="block" rendered="#{MovPagamentoControle.pixVO.statusAtivo}">
                                    <div class="pix-qr-row title">
                                        <span>Use o <strong>QR Code do PIX</strong> para efetuar o pagamento.</span>
                                    </div>
                                    <div class="pix-qr-row">
                                        <div class="box-pix">
                                            <a4j:jsFunction name="openModalPix" reRender="modalPix"
                                                            oncomplete="Richfaces.showModalPanel('modalPix')"/>
                                            <h:graphicImage value="#{MovPagamentoControle.pixVO.textoQRCodeSemEncodeSomenteJSP}&w=180&h=180"
                                                            onclick="openModalPix()"
                                                            alt="QRcode Pix"/>
                                        </div>
                                    </div>
                                </h:panelGroup>
                                <div class="pix-qr-row status">
                                    <span class="status">Status do pagamento:</span>
                                    <h:panelGroup id="msgConfirmacaoPix" styleClass="pix-qr-row" layout="block">
                                        <h:outputText style="color: #5aac41; font-weight: bold;"
                                                      rendered="#{MovPagamentoControle.pixVO.statusConcluido}"
                                                      value="Pagamento confirmado"/>
                                        <h:outputText style="color: orangered; font-weight: bold;"
                                                      rendered="#{MovPagamentoControle.pixVO.statusExpirado}"
                                                      value="Pix expirado"/>
                                        <h:outputText style="font-weight: bold;"
                                                      rendered="#{MovPagamentoControle.pixVO.statusAtivo}"
                                                      value="Aguardando pagamento"/>
                                        <h:outputText style="font-weight: bold;"
                                                      rendered="#{!MovPagamentoControle.pixVO.statusConcluido && !MovPagamentoControle.pixVO.statusExpirado && !MovPagamentoControle.pixVO.statusAtivo}"
                                                      value="#{MovPagamentoControle.pixVO.statusExplicacao}"/>
                                    </h:panelGroup>
                                </div>
                                <c:if test="${MovPagamentoControle.pixVO.statusAtivo}">
                                    <div class="pix-qr-row">
                                        <a4j:commandLink id="btnEnviarWhats"
                                                         styleClass="btn-enviar-whats"
                                                         value=""
                                                         status="false"
                                                         reRender="modalPixTelefone"
                                                         oncomplete="Richfaces.showModalPanel('modalPixTelefone')"></a4j:commandLink>
                                        <div onclick="document.getElementsByClassName('btn-enviar-whats')[0].click()"
                                             class="pix-qr-button">
                                            <img class="pix-qr-button-icon"
                                                 src="${pageContext.request.contextPath}/images/pix/whats.svg"
                                                 alt="Enviar por WhatsApp"/>
                                            <span>Enviar por WhatsApp</span>
                                        </div>
                                    </div>
                                    <div class="pix-qr-row">
                                        <a4j:commandLink id="btnEnviarEmail"
                                                         styleClass="btn-enviar-email"
                                                         value=""
                                                         status="false"
                                                         reRender="modalPixEmail"
                                                         oncomplete="Richfaces.showModalPanel('modalPixEmail')"></a4j:commandLink>
                                        <div onclick="document.getElementsByClassName('btn-enviar-email')[0].click()"
                                             class="pix-qr-button email">
                                            <img class="pix-qr-button-icon"
                                                 src="${pageContext.request.contextPath}/images/pix/email.svg"
                                                 alt="Enviar por Email"/>
                                            <span>Enviar por email</span>
                                        </div>
                                    </div>
                                </c:if>
                            </div>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup styleClass="bloco"
                              id="panelValorParceiroFidelidadeSuperior"
                              rendered="#{movPagamento.opcaoPagamentoParceiroFidelidade}">
                            <span class="cinza negrito upper flex">
                                <h:outputText value="VALOR EM PONTOS DOTZ:"/>
                            </span>
                    <h:inputText disabled="true"
                                 value="#{movPagamento.pontosParceiroFidelidade}"
                                 style="top:0px;"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="inputTextClean">
                    </h:inputText>
                </h:panelGroup>

                <h:panelGroup styleClass="bloco"
                              rendered="#{(movPagamento.opcaoPagamentoCartaoCredito or movPagamento.opcaoPagamentoCartaoDebito) and !movPagamento.formaPagamento.receberSomenteViaPinPad}">
                    <span class="cinza negrito upper flex"><h:outputText
                            value="Adquirente:"></h:outputText>
                    </span>
                    <h:panelGroup layout="block" styleClass="block cb-container">
                        <h:selectOneMenu id="adquirente"
                                         value="#{movPagamento.adquirenteVO.codigo}"
                                         disabled="#{!movPagamento.movPagamentoEscolhida or (movPagamento.movPagamentoEscolhida and not empty movPagamento.observacao
                                                    and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad)}"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         valueChangeListener="#{MovPagamentoControle.atualizarAdquirente}"
                                         styleClass="form"
                                         style="top:0px;">
                            <f:selectItems value="#{MovPagamentoControle.listaSelectItemAdquirente}"/>
                            <a4j:support event="onchange" reRender="form:geralnovopagamento" oncomplete="#{MovPagamentoControle.mensagemNotificar}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup styleClass="bloco"
                              rendered="#{(movPagamento.opcaoPagamentoCartaoCredito or movPagamento.opcaoPagamentoCartaoDebito) and !movPagamento.formaPagamento.receberSomenteViaPinPad}">
                    <span class="cinza negrito upper flex">
                        <h:outputText
                                rendered="#{MovPagamentoControle.empresa.obrigatorioPreencherCamposCartao}"
                                value="* ">
                        </h:outputText>
                        <h:outputText value="Operadora de Cartão:"></h:outputText>
                    </span>
                    <h:panelGroup layout="block" styleClass="block cb-container">
                        <h:selectOneMenu id="operadoraCartao"
                                         rendered="#{movPagamento.opcaoPagamentoCartaoCredito}"
                                         value="#{movPagamento.operadoraCartaoVO.codigo}"
                                         disabled="#{!movPagamento.movPagamentoEscolhida or (movPagamento.movPagamentoEscolhida and not empty movPagamento.observacao
                                                    and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad)}"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         valueChangeListener="#{MovPagamentoControle.atualizarOperadoraCartao}"
                                         styleClass="form"
                                         style="top:0px;">
                            <a4j:support event="onchange"
                                         action="#{MovPagamentoControle.atualizaNrParcelasOperadora}"
                                         reRender="form:geralnovopagamento"/>
                            <f:selectItems
                                    value="#{MovPagamentoControle.listaSelectItemOperadoraCartaoCredito}"/>
                        </h:selectOneMenu>

                        <h:selectOneMenu id="operadoraCartaoDebitoOnline"
                                         rendered="#{movPagamento.opcaoPagamentoCartaoDebito && movPagamento.formaPagamento.cartaoDebitoOnline}"
                                         disabled="#{!movPagamento.movPagamentoEscolhida or (movPagamento.movPagamentoEscolhida and not empty movPagamento.observacao
                                                    and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad)}"
                                         value="#{movPagamento.operadoraCartaoVO.codigo}"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         styleClass="form"
                                         style="top:0px;">
                            <f:selectItems
                                    value="#{MovPagamentoControle.listaSelectItemOperadoraCartaoDebitoOnline}"/>
                            <a4j:support event="onchange"
                                         action="#{MovPagamentoControle.prepararPagamentoOnlineDebito}"
                                         reRender="form"/>
                        </h:selectOneMenu>

                        <h:selectOneMenu id="operadoraCartao1"
                                         rendered="#{movPagamento.opcaoPagamentoCartaoDebito && !movPagamento.formaPagamento.cartaoDebitoOnline}"
                                         disabled="#{!movPagamento.movPagamentoEscolhida or (movPagamento.movPagamentoEscolhida and not empty movPagamento.observacao
                                                    and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad)}"
                                         value="#{movPagamento.operadoraCartaoVO.codigo}"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         style="top:0px;">
                            <f:selectItems
                                    value="#{MovPagamentoControle.listaSelectItemOperadoraCartaoDebito}"/>
                        </h:selectOneMenu>

                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="bloco" rendered="#{movPagamento.opcaoPagamentoCartaoCredito}">
                    <span class="cinza negrito upper flex"><h:outputText value="Parcelas:"></h:outputText>
                        </span>
                    <div class="block cb-container">
                        <h:selectOneMenu id="nrParcelaCartao"
                                         rendered="#{movPagamento.opcaoPagamentoCartaoCredito and not (movPagamento.formaPagamento.pinpad.pinpad >= 1)}"
                                         disabled="#{!movPagamento.movPagamentoEscolhida or (movPagamento.movPagamentoEscolhida and not empty movPagamento.observacao
                                                    and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad)}"
                                         valueChangeListener="#{MovPagamentoControle.atualizarOperadoraCartao}"
                                         value="#{movPagamento.nrParcelaCartaoCredito}"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" style="top:0px;">
                            <f:selectItems
                                    value="#{MovPagamentoControle.listaSelectItemNrParcelaCartao}"/>
                            <a4j:support event="onchange" focus="nrCartao1" reRender="form:geralnovopagamento" oncomplete="#{MovPagamentoControle.mensagemNotificar}"/>
                        </h:selectOneMenu>

                        <h:selectOneMenu id="nrParcelaCartaoPinpad"
                                         rendered="#{movPagamento.opcaoPagamentoCartaoCredito and movPagamento.formaPagamento.pinpad.pinpad >= 1}"
                                         disabled="#{!movPagamento.movPagamentoEscolhida or (movPagamento.movPagamentoEscolhida and not empty movPagamento.observacao
                                                    and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad)}"
                                         value="#{movPagamento.nrParcelaCartaoCredito}"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" style="top:0px;">
                            <f:selectItems
                                    value="#{MovPagamentoControle.listaSelectItemNrParcelaCartao}"/>
                        </h:selectOneMenu>

                    </div>
                </h:panelGroup>

                <h:panelGroup id="panelTipoParcelamentoStoneCaixaAberto"
                              styleClass="bloco"
                              rendered="#{movPagamento.operadoraCartaoVO.codigoIntegracaoStoneOnline != null &&
                              movPagamento.nrParcelaCartaoCredito > 1 && movPagamento.adquirenteVO.nome.contains('STONE')}">
                    <span class="cinza negrito upper flex">
                        <h:outputText value="#{msg_aplic.prt_tipoParcelamento}:" />
                    </span>
                    <h:panelGroup layout="block" styleClass="block cb-container">
                        <h:selectOneMenu id="tipoparcelamentostoneCaixaAberto"
                                         valueChangeListener="#{MovPagamentoControle.atualizarTipoPagamentoStone}"
                                         disabled="#{!movPagamento.movPagamentoEscolhida}"
                                         value="#{movPagamento.tipoParcelamentoStone}"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         styleClass="form"
                                         style="top:0px; min-width: 160px;">
                            <a4j:support event="onchange" focus="tipoparcelamentostoneCaixaAberto" reRender="form"/>
                            <f:selectItem itemLabel="" />
                            <f:selectItem itemLabel="#{msg_aplic.prt_tipo_parcelamento_stone_lojista}" itemValue="MCHT" />
                            <f:selectItem itemLabel="#{msg_aplic.prt_tipo_parcelamento_stone_emissor}" itemValue="ISSR" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="bloco"
                              rendered="#{(movPagamento.opcaoPagamentoCartaoCredito and movPagamento.formaPagamento.pinpad.pinpad != 2 or movPagamento.opcaoPagamentoCartaoDebito
                                and movPagamento.formaPagamento.pinpad.pinpad != 2) and !movPagamento.formaPagamento.receberSomenteViaPinPad}">
                    <span class="cinza negrito upper flex">
                        <h:outputText
                                rendered="#{MovPagamentoControle.empresa.obrigatorioPreencherCamposCartao}"
                                value="* ">
                        </h:outputText>
                        <h:outputText value="Autorização:"></h:outputText>
                    </span>
                    <h:inputText size="40" maxlength="40"
                                 autocomplete="off"
                                 disabled="#{!movPagamento.movPagamentoEscolhida or (movPagamento.movPagamentoEscolhida and not empty movPagamento.observacao
                                                    and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad)}"
                                 value="#{movPagamento.autorizacaoCartao}"
                                 style="top:0px;"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="inputTextClean">
                    </h:inputText>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="bloco" rendered="#{movPagamento.formaPagamento.apresentarNSU}">
                    <span class="cinza negrito upper flex">
                        <h:outputText
                                rendered="#{MovPagamentoControle.empresa.obrigatorioPreencherCamposCartao}"
                                value="* NSU (SOMENTE NÚMEROS):">
                        </h:outputText>
                        <h:outputText
                                rendered="#{!MovPagamentoControle.empresa.obrigatorioPreencherCamposCartao}"
                                value="NSU:">
                        </h:outputText>
                    </span>
                    <%-- Campo abaixo evita que o navegador sugira autocomplete com senhas j salvas anteriormente --%>
                    <input type="password" style="visibility: hidden; width: 1px; margin: 0;"/>
                    <h:inputText id="autorizacaonsu" size="20" maxlength="20"
                                 rendered="#{!MovPagamentoControle.empresa.obrigatorioPreencherCamposCartao}"
                                 disabled="#{!movPagamento.movPagamentoEscolhida or (movPagamento.movPagamentoEscolhida and not empty movPagamento.observacao
                                                    and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad)}"
                                 value="#{movPagamento.nsu}"
                                 style="top:0; left:95px;"
                                 autocomplete="off"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="inputTextClean">
                    </h:inputText>

                    <h:inputText id="autorizacaoNsuOnlyNumber" size="20" maxlength="20"
                                 rendered="#{MovPagamentoControle.empresa.obrigatorioPreencherCamposCartao}"
                                 disabled="#{!movPagamento.movPagamentoEscolhida or (movPagamento.movPagamentoEscolhida and not empty movPagamento.observacao
                                                    and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad)}"
                                 onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                 value="#{movPagamento.nsu}"
                                 style="top:0; left:95px;"
                                 autocomplete="off"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="inputTextClean">
                    </h:inputText>

                </h:panelGroup>

                <h:panelGrid id="panelPinPad" columns="2">
                    <h:panelGroup layout="block" styleClass="bloco"
                                  rendered="#{movPagamento.opcaoPagamentoCartaoCredito and movPagamento.formaPagamento.pinpad.pinpad == 1}">
                    <span class="cinza negrito upper flex">
                        <h:outputText value="#{msg_aplic.prt_tipoParcelamento}:"/>
                    </span>
                        <div class="block cb-container">
                            <h:selectOneMenu id="tipoparcelamento"
                                             disabled="#{!movPagamento.movPagamentoEscolhida}"
                                             value="#{movPagamento.tipoParcelamento}"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form"
                                             style="top:0px;">
                                <f:selectItem itemLabel="#{msg_aplic.prt_administradora}" itemValue="1"/>
                                <f:selectItem itemLabel="#{msg_aplic.prt_loja}" itemValue="2"/>
                            </h:selectOneMenu>
                        </div>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="bloco"
                                  rendered="#{!movPagamento.movPagamentoEscolhida and movPagamento.formaPagamento.pinpad.pinpad >= 1}">
                        <h:graphicImage
                                url="images/icon-pinpad.png"
                                style="opacity: .5; margin-left: 5px; height: 21px;vertical-align: middle;"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="bloco"
                                  rendered="#{movPagamento.movPagamentoEscolhida
                              and (movPagamento.opcaoPagamentoCartaoDebito or movPagamento.opcaoPagamentoCartaoCredito)
                              and (movPagamento.formaPagamento.pinpad.pinpad == 1)}">

                        <a4j:commandLink
                                disabled="#{not empty movPagamento.observacao and not empty movPagamento.numeroUnicoTransacao and not empty movPagamento.respostaRequisicaoPinpad}"
                                action="#{MovPagamentoControle.abrirPinpadCappta}"
                                reRender="painelCapptaPayment" oncomplete="#{MovPagamentoControle.msgAlert}" title="pinpad Linx">
                            <h:graphicImage url="images/icon-pinpad.png"
                                            style="margin-left: 5px; height: 26px;vertical-align: middle;"/>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="bloco"
                                  rendered="#{movPagamento.movPagamentoEscolhida
                              and (movPagamento.opcaoPagamentoCartaoDebito or movPagamento.opcaoPagamentoCartaoCredito)
                              and (movPagamento.formaPagamento.pinpad.pinpad == 2 and empty movPagamento.respostaRequisicaoPinpad)}">

                        <a4j:commandLink id="commandLinkPinpadGeoitd"
                                         action="#{MovPagamentoControle.modalGeoitd}"
                                         oncomplete="#{MovPagamentoControle.msgAlert}"
                                         title="pinpadGeoitd" reRender="mdlPinpadGeoitd">
                            <h:graphicImage url="images/icon-pinpad.png"
                                            style="margin-left: 5px; height: 26px;vertical-align: middle;"/>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:panelGroup
                            style="width:auto !important; padding: 5px; margin-top: 10px; color: #777777 !important; font-size: 12px"
                            id="panelMsgGeoitd" rendered="#{movPagamento.formaPagamento.pinpad.pinpad == 2}">
                        <h:outputText id="msgPinpadGeoitd" value="#{MovPagamentoControle.msgGeoitdPos}"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGroup layout="block" style="display: flex; justify-content: space-between;">
                    <div style="display: flex;">
                        <h:panelGroup layout="block"
                                      style="padding: 15px;"
                                      rendered="#{movPagamento.movPagamentoEscolhida and movPagamento.opcaoPagamentoCartaoCredito and movPagamento.apresentarPagamentoOnline}">
                            <a4j:commandLink id="btnPagamentoOnline"
                                             styleClass="tooltipster botoes nvoBt btSec"
                                             action="#{MovPagamentoControle.pagamentoOnline}"
                                             oncomplete="#{MovPagamentoControle.mensagemNotificar}"
                                             reRender="form:geralnovopagamento, cardCartao">
                                <i class="fa-icon-credit-card"></i> Pagamento Online
                            </a4j:commandLink>
                        </h:panelGroup>

                        <h:panelGroup layout="block"
                                      style="padding: 15px;"
                                      rendered="#{movPagamento.movPagamentoEscolhida
                                      and movPagamento.formaPagamento.getCard
                                      and (movPagamento.opcaoPagamentoCartaoDebito or movPagamento.opcaoPagamentoCartaoCredito)}">
                            <a4j:commandLink id="btnGetCard"
                                             styleClass="tooltipster botoes nvoBt btSec"
                                             title="Abrir #{movPagamento.labelTipoPinPad}"
                                             action="#{MovPagamentoControle.abrirPinpad}"
                                             oncomplete="#{MovPagamentoControle.onCompletePinpad}"
                                             reRender="formModalPinpad,painelPagamentoPinpad">
                                <i class="fa-icon-credit-card"></i>
                                <h:outputText value="#{movPagamento.labelTipoPinPad}"/>
                            </a4j:commandLink>
                        </h:panelGroup>

                        <h:panelGroup layout="block"
                                      style="padding: 16px;"
                                      rendered="#{movPagamento.movPagamentoEscolhida
                                      and movPagamento.formaPagamento.stoneConnect
                                      and (movPagamento.opcaoPagamentoCartaoDebito or movPagamento.opcaoPagamentoCartaoCredito)}">
                            <a4j:commandLink id="btnStoneConnect"
                                             styleClass="tooltipster botoes nvoBt btSec"
                                             style="background-color: #00a868; color: white; display: flex; padding-right: 10px; padding-left: 10px; height: 15px; border-radius: 2px;"
                                             title="Abrir #{movPagamento.labelTipoPinPad}"
                                             action="#{MovPagamentoControle.abrirPinpad}"
                                             oncomplete="#{MovPagamentoControle.onCompletePinpad}"
                                             reRender="formModalPinpad,painelPagamentoPinpad">
                                <h:graphicImage value="/imagens/stone-logo.svg" style="width: 20px; height: 19px; margin-top: -2px;"
                                                id="hidelinkLancamentos" />
                                <h:outputText style="margin-top: -12px;" value="#{movPagamento.labelTipoPinPad}"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </div>

                    <h:panelGroup layout="block"
                                  styleClass="addNovoCartao"
                                  rendered="#{movPagamento.movPagamentoEscolhida and (movPagamento.opcaoPagamentoCartaoDebito or movPagamento.opcaoPagamentoCartaoCredito)
                               and movPagamento.formaPagamento.pinpad.pinpad != 1 and movPagamento.formaPagamento.pinpad.pinpad != 2}">
                        <a4j:commandLink action="#{MovPagamentoControle.addNovoCartao}"
                                         id="addNovoCartao"
                                         styleClass="tooltipster botoes nvoBt btSec"
                                         reRender="tabelaEscolhaFormaPagamento, escolhaFormaPagamento, totalLancado, residuo,mensagemSup, mensagemInf,panelBotoesControle,panelCobrancaMultaJuros,panelSaldoContaCorrente,escolhaFormaPagamentoCC">
                            <i class="fa-icon-plus"></i> Adicionar novo cartão
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup id="panelPagDigital" style="margin-left: 22px;">
                    <h:panelGroup id="panelBooleanPagDigital">
                <h:selectBooleanCheckbox
                        style="margin-top: -3px;"
                        styleClass="form" id="chkUsarPagamentoDigital"
                        value="#{movPagamento.usarPagamentoDigital}"
                        disabled="#{!movPagamento.movPagamentoEscolhida}"
                        valueChangeListener="#{MovPagamentoControle.atualizarOperadoraCartao}"
                        rendered="#{movPagamento.operadoraCartaoVO.codigoIntegracao.id != 0 && movPagamento.formaPagamento.pinpad.pinpad != 1}">
                    <a4j:support event="onchange"
                                 reRender="form"/>
                </h:selectBooleanCheckbox>
                    </h:panelGroup>
                        <h:panelGroup id="panelTextPagDigital">
                <h:outputText
                        rendered="#{movPagamento.operadoraCartaoVO.codigoIntegracao.id != 0 && movPagamento.formaPagamento.pinpad.pinpad != 1}"
                        style="top:0px;padding-left: 6px;padding-right: 6px;"
                        value="Pagamento Digital"/>
                </h:panelGroup>
                    </h:panelGroup>


                <h:outputText
                        rendered="#{movPagamento.opcaoPagamentoBoleto}"
                        value="Data Quitação (dia que o aluno pagou):"
                        style="top:0px;padding-left: 6px;padding-right: 6px;"/>
                <h:panelGroup id="dataBoleto"
                              rendered="#{movPagamento.opcaoPagamentoBoleto}"
                              style="top:0px;">
                    <rich:calendar id="dataQuitacao"
                                   value="#{movPagamento.dataQuitacao}" inputSize="10"
                                   inputClass="form" oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   disabled="#{!movPagamento.movPagamentoEscolhida}"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy" enableManualInput="true"
                                   zindex="2" showWeeksBar="false"/>
                    <h:message for="dataQuitacao"
                               styleClass="mensagemDetalhada"/>
                </h:panelGroup>

                <h:outputText
                        rendered="#{movPagamento.opcaoPagamentoBoleto}"
                        style="top:0px; padding-left: 6px;padding-right: 6px;"
                        value="Convênio:"/>
                <h:panelGroup
                        rendered="#{movPagamento.opcaoPagamentoBoleto}"
                        style="top:0px;">
                    <h:selectOneMenu id="convenio"
                                     disabled="#{!movPagamento.movPagamentoEscolhida}"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form"
                                     value="#{movPagamento.convenio.codigo}">
                        <f:selectItems
                                value="#{MovPagamentoControle.listaSelectItemConvenioCobrancaBoleto}"/>
                        <a4j:support event="onchange"
                                     action="#{MovPagamentoControle.validarConvenioPjBank}"
                                     reRender="form"/>
                    </h:selectOneMenu>
                    <a4j:commandButton id="atualizar_convenio"
                                       action="#{MovPagamentoControle.montarListaTodosConvenios}"
                                       image="imagens/atualizar.png" ajaxSingle="true"
                                       reRender="form"/>
                </h:panelGroup>

                <h:panelGroup
                        rendered="#{movPagamento.exibirDataCreditoBoleto}">
                <h:outputText
                        styleClass="tooltipster"
                        value="Data Crédito (compensação):"
                        title="Informe a previsão de crédito do boleto na PjBank"
                        style="top:0px;padding-left: 6px;padding-right: 6px;"/>
                <h:panelGroup id="dataCreditoBoleto"
                              style="top:0px;">
                    <rich:calendar id="dataCredito"
                                   value="#{movPagamento.dataPagamento}" inputSize="10"
                                   inputClass="form" oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   disabled="#{!movPagamento.movPagamentoEscolhida}"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy" enableManualInput="true"
                                   zindex="2" showWeeksBar="false"/>
                    <h:message for="dataCredito"
                               styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelGeralParceiroFidelidade"
                              style="padding-top: 15px; padding-bottom: 20px"
                              rendered="#{movPagamento.opcaoPagamentoParceiroFidelidade || movPagamento.formaPagamento.gerarPontos}">

                    <h:panelGroup layout="block"
                                  id="panelGerarPontosParceiroFidelidade"
                                  style="display: inline-flex; width: 100%; padding-left: 20px"
                                  rendered="#{movPagamento.formaPagamento.gerarPontos}">
                        <div class="chk-fa-container inline " style="margin-top: 14px; padding-right: 10px;">
                            <h:selectBooleanCheckbox
                                    style="top:0px; padding-left: 6px;padding-right: 6px;"
                                    styleClass="form" id="usarParceiroFidelidade"
                                    value="#{movPagamento.usarParceiroFidelidade}">
                                <a4j:support event="onchange"
                                             reRender="panelGeralParceiroFidelidade"/>
                            </h:selectBooleanCheckbox>
                            <span/>
                        </div>
                        <h:outputText styleClass="cinza negrito upper flex"
                                      value="GERAR PONTOS DOTZ"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="panelInfoGeralParceiroFidelidade"
                                  style="display: inline-flex; width: 100%;"
                                  rendered="#{movPagamento.opcaoPagamentoParceiroFidelidade || movPagamento.usarParceiroFidelidade}">

                        <h:panelGroup styleClass="bloco"
                                      id="panelParceiroFidelidade"
                                      rendered="#{movPagamento.formaPagamento.gerarPontos}">
                            <span class="cinza negrito upper flex">
                                <h:outputText value="Tabela Acumular:"/>
                            </span>
                            <h:panelGroup layout="block" styleClass="block cb-container">
                                <h:selectOneMenu id="testeLuiz"
                                                 value="#{movPagamento.tabelaParceiroFidelidadeVO.codigo}"
                                                 disabled="#{!movPagamento.movPagamentoEscolhida}"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 style="top:0px;">
                                    <a4j:support event="onchange"
                                                 action="#{MovPagamentoControle.calcularEncapsulado}"
                                                 reRender="opcoesFormaPagamento, escolhaFormaPagamento, totalLancado, residuo,mensagemSup, mensagemInf,panelBotoesControle,panelCobrancaMultaJuros,panelSaldoContaCorrente,escolhaFormaPagamentoCC,containerTotalizadores"/>
                                    <f:selectItems
                                            value="#{movPagamento.listaSelectItemTabelaParceiroFidelidade}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup styleClass="bloco"
                                      id="panelValorParceiroFidelidade"
                                      rendered="#{movPagamento.formaPagamento.gerarPontos}">
                            <span class="cinza negrito upper flex">
                                <h:outputText value="PONTOS DOTZ:"/>
                            </span>
                            <h:inputText disabled="true"
                                         value="#{movPagamento.pontosParceiroFidelidade}"
                                         style="top:0px;"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="inputTextClean">
                            </h:inputText>
                        </h:panelGroup>

                        <h:panelGroup styleClass="bloco"
                                      id="panelCPFParceiroFidelidade">
                            <span class="cinza negrito upper flex">
                                <h:outputText value="CPF BENEFICIÁRIO DOTZ:"
                                              rendered="#{movPagamento.formaPagamento.gerarPontos}"/>
                                <h:outputText value="CPF COBRANÇA DOTZ:"
                                              rendered="#{movPagamento.opcaoPagamentoParceiroFidelidade}"/>
                            </span>
                            <h:inputText value="#{movPagamento.cpfParceiroFidelidade}"
                                         disabled="#{!LoginControle.permissaoAcessoMenuVO.permiteAlterarCPFDotz}"
                                         style="top:0px;"
                                         id="cpfParceiroFidelidade"
                                         onblur="blurinput(this);"
                                         onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                         onfocus="focusinput(this);" size="12" maxlength="14"
                                         styleClass="inputTextClean"/>

                            <a4j:commandLink id="consultarSaldoParceiro"
                                             rendered="#{movPagamento.formaPagamento.gerarPontos}"
                                             style="padding-left: 10px; font-size: 18px;"
                                             styleClass="fa-icon-search tooltipster"
                                             action="#{MovPagamentoControle.consultarSaldoParceiroFidelidade}"
                                             title="Consultar saldo parceiro"
                                             reRender="opcoesFormaPagamento"/>

                            <a4j:commandLink id="consultarProdutosParceiro"
                                             rendered="#{movPagamento.opcaoPagamentoParceiroFidelidade}"
                                             style="padding-left: 10px; font-size: 18px;"
                                             styleClass="tooltipster"
                                             oncomplete="#{MovPagamentoControle.mensagemNotificar}"
                                             action="#{MovPagamentoControle.consultarProdutosParceiro}"
                                             title="Consultar produtos disponíveis para troca"
                                             reRender="opcoesFormaPagamento, escolhaFormaPagamento, totalLancado, residuo,mensagemSup, mensagemInf,panelBotoesControle,panelCobrancaMultaJuros,panelSaldoContaCorrente,escolhaFormaPagamentoCC, containerTotalizadores">
                                <h:graphicImage value="imagens_flat/search-dollar-solid.svg" width="28" style="vertical-align: middle;"/>
                            </a4j:commandLink>
                        </h:panelGroup>

                        <h:panelGroup styleClass="bloco"
                                      id="panelProdutosParceiroFidelidade"
                                      rendered="#{movPagamento.opcaoPagamentoParceiroFidelidade}">
                            <span class="cinza negrito upper flex">
                                <h:outputText value="Tabela Resgate:"/>
                            </span>
                            <h:panelGroup layout="block" styleClass="block cb-container">
                                <h:selectOneMenu id="pontosProdutoParceiroFidelidade"
                                                 value="#{movPagamento.codigoExternoProdutoParceiroFidelidade}"
                                                 disabled="#{!movPagamento.movPagamentoEscolhida}"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 style="top:0px;">
                                    <a4j:support event="onchange"
                                                 action="#{MovPagamentoControle.selecionarProdutoParceiro}"
                                                 oncomplete="#{MovPagamentoControle.mensagemNotificar}"
                                                 reRender="opcoesFormaPagamento, escolhaFormaPagamento, totalLancado, residuo,mensagemSup, mensagemInf,panelBotoesControle,panelCobrancaMultaJuros,panelSaldoContaCorrente,escolhaFormaPagamentoCC,containerTotalizadores"/>
                                    <f:selectItems
                                            value="#{movPagamento.selectItemsProdutosParceiroFidelidade}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup styleClass="bloco"
                                      id="panelSenhaParceiroFidelidade"
                                      rendered="#{movPagamento.opcaoPagamentoParceiroFidelidade}">
                            <span class="cinza negrito upper flex">
                                <h:outputText value="SENHA DOTZ:"/>
                            </span>
                            <h:inputSecret value="#{movPagamento.senhaParceiroFidelidade}"
                                           redisplay="true"
                                           style="top:0px;" autocomplete="off"
                                           id="senhaParceiroFidelidade"
                                           onblur="blurinput(this);"
                                           onfocus="focusinput(this);" size="20" maxlength="25"
                                           styleClass="inputTextClean"/>
                        </h:panelGroup>

                    </h:panelGroup>

                    <h:panelGroup id="panelMsgParceiroFidelidade"
                                  style="display: inline-flex; width: 100%; padding-left: 20px"
                                  rendered="#{not empty movPagamento.msgParceiroFidelidade}">
                        <h:outputText value="#{movPagamento.msgParceiroFidelidade}"/>
                    </h:panelGroup>

                </h:panelGroup>




                <div style="padding: 20px;">
                    <rich:dataTable id="MovPagamentoCheque"
                                    rendered="#{movPagamento.opcaoPagamentoCheque}"
                                    rowClasses="tablelistras" width="100%"
                                    style="border:none;" columnClasses="colunaEsquerda"
                                    value="#{movPagamento.chequeVOs}" var="cheque">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Cod. Banco" styleClass="textsmall"/>
                            </f:facet>

                            <h:inputText id="codigoBanco1"
                                         value="#{cheque.banco.codigoBanco}"
                                         onblur="blurinput(this);"
                                         size="10" maxlength="10"
                                         onfocus="focusinput(this);"
                                         styleClass="form">
                                <a4j:support event="onchange"
                                             ajaxSingle="true"
                                             actionListener="#{MovPagamentoControle.atualizarBancoEvent}"
                                             reRender="escolhaFormaPagamento"/>
                            </h:inputText>

                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Banco" styleClass="textsmall"/>
                            </f:facet>
                            <h:selectOneMenu id="bancoPreenchido"
                                             style="width:180px;"
                                             value="#{cheque.banco.codigo}"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form">
                                <f:selectItems
                                        value="#{MovPagamentoControle.listaSelectItemBanco}"/>
                                <a4j:support reRender="escolhaFormaPagamento"
                                             action="#{MovPagamentoControle.atualizarBanco}"
                                             event="onchange"/>
                            </h:selectOneMenu>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Agência" styleClass="textsmall"/>
                            </f:facet>
                            <h:inputText id="agencia" value="#{cheque.agencia}"
                                         size="4" maxlength="6" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Conta" styleClass="textsmall"/>
                            </f:facet>
                            <h:inputText id="conta" value="#{cheque.conta}"
                                         size="9" maxlength="11" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Cheque" styleClass="textsmall"/>
                            </f:facet>
                            <h:inputText id="nDoc" value="#{cheque.numero}"
                                         size="8" maxlength="10" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nomeTerceiro}:"
                                              styleClass="textsmall"/>
                            </f:facet>
                            <h:inputText id="nometerceiro" value="#{cheque.nomeNoCheque}"
                                         size="8" maxlength="120" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"/>
                        </rich:column>

                        <rich:column
                                rendered="#{ChequeControle.apresentarCampoCPF}">
                            <f:facet name="header">
                                <h:outputText value="#{ChequeControle.displayIdentificadorFront[0]}" styleClass="textsmall"/>
                            </f:facet>
                            <c:if test="${!ChequeControle.configuracaoSistemaVO.usarSistemaInternacional}">
                                <h:inputText id="CPF" value="#{cheque.cpf}"
                                             onblur="blurinput(this);"
                                             onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                             onfocus="focusinput(this);" size="12" maxlength="14"
                                             styleClass="form"/>
                            </c:if>
                            <c:if test="${ChequeControle.configuracaoSistemaVO.usarSistemaInternacional}">
                                <h:inputText id="CPFInternacional" value="#{cheque.cpf}"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" size="12" maxlength="14"
                                             styleClass="form"/>
                            </c:if>
                        </rich:column>

                        <rich:column
                                rendered="#{ChequeControle.apresentarCampoCNPJ}">
                            <f:facet name="header">
                                <h:outputText value="#{ChequeControle.displayIdentificadorFront[2]}" styleClass="textsmall"/>
                            </f:facet>
                            <c:if test="${!ChequeControle.configuracaoSistemaVO.usarSistemaInternacional}">
                                <h:inputText id="CNPJ" value="#{cheque.cnpj}"
                                             onblur="blurinput(this);"
                                             onkeypress="return mascara(this.form,this.id,'99.999.999/9999-99', event);"
                                             onfocus="focusinput(this);" size="16" maxlength="18"
                                             styleClass="form"/>
                            </c:if>
                            <c:if test="${ChequeControle.configuracaoSistemaVO.usarSistemaInternacional}">
                                <h:inputText id="CNPJInter" value="#{cheque.cnpj}"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" size="16" maxlength="20"
                                             styleClass="form"/>
                            </c:if>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Compensação"
                                              styleClass="textsmall"/>
                            </f:facet>
                            <h:panelGroup id="dataCheque">
                                <rich:calendar id="dataCompensacao"
                                               value="#{cheque.dataCompensacao}" inputSize="7"
                                               inputClass="form" oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy" enableManualInput="true"
                                               showWeeksBar="false"/>
                                <h:message for="dataCompensacao"
                                           styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Valor" styleClass="textsmall"/>
                            </f:facet>
                            <h:inputText id="valor" value="#{cheque.valor}"
                                         size="5" onblur="blurinput(this);" maxlength="7"
                                         onfocus="focusinput(this);" styleClass="form">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:inputText>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Opções" styleClass="textsmall"/>
                            </f:facet>
                            <a4j:commandButton id="btnAdicionarCheque" value="adicionar"
                                               action="#{MovPagamentoControle.adicionarCheque}"
                                               reRender="escolhaFormaPagamento, totalLancado,residuo,
                                                                                                                   mensagemSup, mensagemInf, panelBotoesControle,escolhaFormaPagamentoCC"
                                               image="./images/icon_mais.gif"
                                               title="Adicionar Cheque"/>

                            <a4j:commandButton id="btnRemoverCheque" value="remover"
                                               rendered="#{MovPagamentoControle.desenharBotao}"
                                               action="#{MovPagamentoControle.removerCheque}"
                                               reRender="escolhaFormaPagamento,totalLancado, residuo,
                                                                                                                   mensagemSup, mensagemInf,panelBotoesControle,escolhaFormaPagamentoCC"
                                               image="./images/icon_delete.gif"
                                               title="Excluir Cheque"/>

                        </rich:column>
                    </rich:dataTable>

                    <a4j:commandButton
                            rendered="#{movPagamento.opcaoPagamentoCheque}"
                            value="Gerar Datas" styleClass="botoes nvoBt botaoPrimarioGrande "
                            title="Programa as Datas dos Cheques de 30 em 30 dias"
                            action="#{MovPagamentoControle.gerarDatas}"
                            reRender="panelEsconderControles"/>

                    <a4j:commandButton id="btAtualizarValoresCheque" styleClass="botoes nvoBt botaoPrimarioGrande "
                                       rendered="#{movPagamento.opcaoPagamentoCheque}"
                                       value="Atualizar"
                                       action="#{MovPagamentoControle.calcularEncapsulado}"
                                       reRender="escolhaFormaPagamento, totalLancado, residuo, mensagemSup, mensagemInf,escolhaFormaPagamentoCC"/>

                </div>


            </h:panelGroup>
        </h:panelGroup>

    </a4j:repeat>

    <h:panelGroup id="panelMovCC" layout="block" styleClass="opcaoPagamento" style="padding: 5px 0px 5px 0px;"
                  rendered="#{MovPagamentoControle.apresentarFormasPagamento && MovPagamentoControle.apresentarFormaPagamentoContaCorrente}">
        <a4j:repeat id="escolhaFormaPagamentoCC"
                    value="#{MovPagamentoControle.listaSelectItemMovPagamento}"
                    var="movPagamento">
            <h:panelGroup layout="block" styleClass="opcaoPagamentoHead"
                          rendered="#{movPagamento.opcaoPagamentoContaCorrenteCliente}">
                <div class="chk-fa-container inline ">
                    <h:selectBooleanCheckbox id="movPagamentoEscolhidoCC"
                                             value="#{movPagamento.movPagamentoEscolhida}"
                                             rendered="#{movPagamento.opcaoPagamentoContaCorrenteCliente && movPagamento.mostraContaCorrenteAcademia}">
                        <a4j:support event="onclick"
                                     focus="movPagamentoEscolhido"
                                     oncomplete="#{MovPagamentoControle.mensagemNotificar}"
                                     action="#{MovPagamentoControle.calcularPagamentoSeletcCheckbox}"
                                     reRender="panelAutorizacaoFuncionalidade, panelCobrancaMultaJuros,panelSaldoContaCorrente,escolhaFormaPagamento,painelCappta, panelPinPad, totalLancado, residuo, mensagemSup, mensagemInf, panelBotoesControle, escolhaFormaPagamentoCC"/>
                    </h:selectBooleanCheckbox>
                    <span/>
                </div>


                <span class="cinza negrito upper" style="font-size: 14px;">
                                                                <h:outputText
                                                                        value="#{movPagamento.formaPagamento.descricao} :"
                                                                        rendered="#{movPagamento.opcaoPagamentoContaCorrenteCliente && movPagamento.mostraContaCorrenteAcademia}"/>
                                                            </span>

                <span class="cinza upper" style="font-size: 14px;">
                                                                <h:outputText id="saldoContaCorrenteCliente"
                                                                              rendered="#{movPagamento.opcaoPagamentoContaCorrenteCliente}"
                                                                              value="#{movPagamento.saldoContaCorrenteCliente}"
                                                                              style="position:relative; top:0px; left:10px;"/>
                                                            </span>


                <h:inputText id="valorContaCorrenteCliente"
                             disabled="#{MovPagamentoControle.tipoOperacaoContaCorrenteEnum.id == 'DE' || !movPagamento.movPagamentoEscolhida}"
                             rendered="#{movPagamento.opcaoPagamentoContaCorrenteCliente}"
                             value="#{movPagamento.valorTotal}"
                             style="position:relative; top:0px; left:20px;"
                             onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             styleClass="inputTextClean">
                    <a4j:support event="onchange"
                                 focus="movPagamentoEscolhido"
                                 action="#{MovPagamentoControle.calcularEncapsulado}"
                                 reRender="escolhaFormaPagamento, escolhaFormaPagamentoCC ,totalLancado, residuo , mensagemSup, mensagemInf,panelCobrancaMultaJuros,panelSaldoContaCorrente,panelBotoesControle"/>
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>
            </h:panelGroup>
        </a4j:repeat>
    </h:panelGroup>
</h:panelGroup>

<%@include file="includes/include_modal_pinpad_geoitd.jsp" %>
<script>
    function copy(elementId, message) {
        var element = document.getElementById(elementId);
        element.focus();
        element.select();
        navigator.clipboard.writeText(element.value).then(function () {
            Notifier.success(message);
        }, function (err) {
            console.error('Async: Could not copy text: ', err);
        });
    }

</script>
