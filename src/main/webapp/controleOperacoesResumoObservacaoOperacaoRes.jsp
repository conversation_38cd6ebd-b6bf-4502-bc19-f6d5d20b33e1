<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="includes/include_import_minifiles.jsp"%>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <%@include file="include_modal_renegociadas.jsp"%>
    <title>
        <h:outputText value="Resumo de Observações"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
        <body onload="fireElement('form:botaoAtualizarPagina')">
        <h:form id="form">
            <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
            <c:set var="titulo" scope="session" value="${RelControleOperacoesControle.nomeTela}:${fn:length(RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs)} "/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-controle-de-operacoes-de-excecoes-adm/"/>
            <h:panelGroup layout="block" styleClass="pure-g-r">
                <f:facet name="header">
                    <jsp:include page="topo_reduzido_popUp.jsp"/>
                </f:facet>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid width="100%" style="text-align: right">
                    <h:panelGroup layout="block">
                        <a4j:commandLink id="exportarExcel"
                                           style="margin-left: 8px;"
                                           actionListener="#{ExportadorListaControle.exportar}"
                                           rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                           accesskey="2" styleClass="botoes">
                            <f:attribute name="lista"
                                         value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                            <f:attribute name="atributos"
                                         value="coluna1_ObservacaoOperacao=Parcela,coluna2_ObservacaoOperacao=Valor,nomePessoaApresentar=Nome,coluna3_ObservacaoOperacao=Data da Operação,coluna4_ObservacaoOperacao=Responsável,coluna5_ObservacaoOperacao=Tipo de Operação,coluna6_ObservacaoOperacao=Justificativa"/>
                            <f:attribute name="prefixo" value="ControleOpExcecao"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>
                        <%--BOTÃO PDF--%>
                        <a4j:commandLink id="exportarPdf"
                                           style="margin-left: 8px;"
                                           actionListener="#{ExportadorListaControle.exportar}"
                                           rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                           accesskey="2" styleClass="botoes">
                            <f:attribute name="lista"
                                         value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                            <f:attribute name="atributos"
                                         value="coluna1_ObservacaoOperacao=Parcela,coluna2_ObservacaoOperacao=Valor,nomePessoaApresentar=Nome,coluna3_ObservacaoOperacao=Data da Operação,coluna4_ObservacaoOperacao=Responsável,coluna5_ObservacaoOperacao=Tipo de Operação,coluna6_ObservacaoOperacao=Justificativa"/>
                            <f:attribute name="prefixo" value="ControleOpExcecao"/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
                <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                rows="50" var="resumoPessoa" rowKeyVar="status">
                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                    <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Parcela"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.observacaoOperacaoVO.movParcela.codigo}"/>
                    </rich:column>
                    <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Valor"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.observacaoOperacaoVO.valorOperacao_Apresentar}"/>
                    </rich:column>
                    <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Nome"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.nomePessoaApresentar}"/>
                    </rich:column>
                    <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Data da Operação"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.observacaoOperacaoVO.dataOperacao_Apresentar}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Responsável"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.observacaoOperacaoVO.usuarioResponsavel}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Tipo de Operação"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{resumoPessoa.observacaoOperacaoVO.tipoCancelamento}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Justificativa"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{resumoPessoa.observacaoOperacaoVO.justificativa}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <a4j:commandLink  action="#{ModalRenegociadasControle.abrirModalRenegociacoesParcelasCanceladas}" reRender="mensagens, formModalRenegociadas"
                                          oncomplete="#{ModalRenegociadasControle.msgAlert};#{ModalRenegociadasControle.mensagemNotificar}"
                                          title="Histórico de Renegociações"
                                          rendered="#{resumoPessoa.observacaoOperacaoVO.movParcela.codigo != null and
                                          not empty resumoPessoa.observacaoOperacaoVO.movParcela.codigo}"
                                          styleClass="linkPadrao texto-cor-azul texto-size-16-real">
                            <i class="fa-icon-refresh"></i>
                            <f:param name="movparcela" value="#{resumoPessoa.observacaoOperacaoVO.movParcela.codigo}"/>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" styleClass="scrollPureCustom" renderIfSinglePage="false" for="form:tabelaRes" maxPages="10" id="sctabelaRes"/>
            </h:panelGrid>
        </h:form>
        </body>
        </html>
    </h:panelGrid>
</f:view>

