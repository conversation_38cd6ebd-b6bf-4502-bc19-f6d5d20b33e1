<%@ page contentType="text/html;charset=UTF-8" pageEncoding="ISO-8859-1" language="java" %>
<%@ include file="includes/imports.jsp" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
</head>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>

<style type="text/css">
    body {
        margin: 0;
        padding: 0;
    }
    .alinhar{
        vertical-align: top !important;
        padding-left: 20px;
        padding-right: 20px;
    }
    td.colunaDireita{
        text-align: right;
    }

</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Agendamentos_CRM_tituloForm}"/>
    </title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Agendamentos_CRM_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-de-agendamentos-do-crm/"/>

    <f:facet name="header">
        <jsp:include page="topoReduzido_material_crm.jsp"/>
    </f:facet>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <head>
        <link rel="shortcut icon" href="./favicon.ico">
        <title><h:outputText value="Relatório de agendamentos" /></title>
        <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    </head>

    <h:form id="form" style="border: 0; margin: 0; overflow-x: visible;">
        <style>
            .filtrosTbl tr{
                vertical-align: middle;
            }
            .filtrosTbl td{
                height: 40px;
            }
            .tituloCampos, .alinhar, table{
                color: #777777 !important;
            }
        </style>
        <h:panelGrid columns="2"
                     columnClasses="alinhar, alinhar" cellpadding="20">

            <h:panelGrid columns="2" columnClasses="colunaDireita,colunaEsquerda" border="0" id="filtrosMailing" styleClass="filtrosTbl">
                <h:outputText styleClass="tituloCampos" value="Período de Agendamento" />
                <h:panelGroup>
                    <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px !important;">
                        <rich:calendar value="#{RelatorioAgendamentosControle.inicio}" id="dataInicialCalen"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false">
                            <a4j:support event="onchanged" oncomplete="#{RelatorioAgendamentosControle.msgAlert}"
                                         action="#{RelatorioAgendamentosControle.validarDataAgendamento}" reRender="filtrosMailing"/>
                        </rich:calendar>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="até"
                                  style="margin-left: 8px; margin-right: 8px;"/>
                    <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px !important;">

                        <rich:calendar value="#{RelatorioAgendamentosControle.fim}" id="dataFinalCalenAgendamento"
                                   inputSize="10"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false">
                        <a4j:support event="onchanged" oncomplete="#{RelatorioAgendamentosControle.msgAlert}"
                                     action="#{RelatorioAgendamentosControle.validarDataAgendamento}" reRender="filtrosMailing"/>
                        </rich:calendar>
                    </h:panelGroup>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    <a4j:commandButton id="limparPeriodoCriacao" action="#{RelatorioAgendamentosControle.limparPeriodo}"
                                       image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}"
                                       reRender="filtrosMailing"
                                       style="margin-left: 8px;"/>
                    </h:panelGroup>


                <h:outputText styleClass="tituloCampos" value="Período de Lancamento" />
                <h:panelGroup>
                    <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px !important;">
                        <rich:calendar value="#{RelatorioAgendamentosControle.iniciolancamento}" id="dataInicialLancamento"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false">
                                <a4j:support event="onchanged" oncomplete="#{RelatorioAgendamentosControle.msgAlert}"
                                        action="#{RelatorioAgendamentosControle.validarDataLancamento}" reRender="filtrosMailing"/>

                    </rich:calendar>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="até"
                                  style="margin-left: 8px; margin-right: 8px;"/>
                    <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px !important;">
                        <rich:calendar value="#{RelatorioAgendamentosControle.fimlancamento}" id="dataFinalCalenLancamento"
                                       inputSize="10"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" >
                            <a4j:support event="onchanged" oncomplete="#{RelatorioAgendamentosControle.msgAlert}"
                                         action="#{RelatorioAgendamentosControle.validarDataLancamento}" reRender="filtrosMailing"/>
                        </rich:calendar>

                    </h:panelGroup>
                    <rich:jQuery id="mskDataLancamento" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')"/>
                    <a4j:commandButton id="limparPeriodoCriacaoLancamento" action="#{RelatorioAgendamentosControle.limparPeriodolancamento}"
                                       image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}"
                                       reRender="filtrosMailing"
                                       style="margin-left: 8px;"/>

                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="Tipo de agendamento:"/>
                <h:panelGroup styleClass="cb-container"
                              layout="block">
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" id="meioDeEnvio"
                                     value="#{RelatorioAgendamentosControle.tipo}" style="font-size: 12px">
                        <f:selectItems value="#{RelatorioAgendamentosControle.tipos}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="Resp. agendamento:" />
                <h:panelGroup>
                    <h:inputText  id="nomeOperador" size="40" 
                                  maxlength="255" onblur="blurinput(this);"
                                  onfocus="focusinput(this);" styleClass="inputTextClean"  style="font-size: 12px !important;"
                                  value="#{RelatorioAgendamentosControle.responsavel.nome}" />

                    <rich:suggestionbox height="200" width="200"
                                        for="nomeOperador"
                                        fetchValue="#{result.nome}"
                                        suggestionAction="#{RelatorioAgendamentosControle.executarAutocompleteConsultaUsuario}"
                                        minChars="1" rowClasses="20"
                                        status="statusHora"
                                        nothingLabel="Nenhum usuário encontrado !"
                                        var="result"  id="suggestionUsuario">
                        <a4j:support event="onselect"
                                     action="#{RelatorioAgendamentosControle.selecionarUsuarioSuggestionBox}"/>
                        <h:column>
                            <h:outputText value="#{result.nome}" />
                        </h:column>
                    </rich:suggestionbox>
                    <rich:spacer width="5px"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="Professor:" />
                <h:panelGroup>
                    <h:inputText  id="nomeColaborador" size="40"
                                  maxlength="255" onblur="blurinput(this);"
                                  onfocus="focusinput(this);" styleClass="inputTextClean"  style="font-size: 12px !important;"
                                  value="#{RelatorioAgendamentosControle.colaborador.pessoa.nome}" />

                    <rich:suggestionbox height="200" width="200"
                                        for="nomeColaborador"
                                        fetchValue="#{colaborador.pessoa.nome}"
                                        suggestionAction="#{RelatorioAgendamentosControle.executarAutocompleteColaboradores}"
                                        minChars="1" rowClasses="20"
                                        status="statusHora"
                                        nothingLabel="Nenhum professor encontrado!"
                                        var="colaborador"
                                        id="caixaSugestaoColaborador">
                        <a4j:support event="onselect" action="#{RelatorioAgendamentosControle.selecionarColaborador}"/>
                        <h:column>
                            <h:outputText value="#{colaborador.pessoa.nome}" />
                        </h:column>
                    </rich:suggestionbox>
                    <rich:spacer width="5px"/>
                </h:panelGroup>


                <h:outputText rendered="#{RelatorioAgendamentosControle.usuarioLogado.administrador}" styleClass="tituloCampos" style="margin-top: 6px" value="Empresa:"/>
                <h:panelGroup styleClass="cb-container"
                              layout="block"
                              rendered="#{RelatorioAgendamentosControle.usuarioLogado.administrador}">
                    <h:selectOneMenu
                                     styleClass="form" id="empresa" value="#{RelatorioAgendamentosControle.codigoEmpresa}">
                        <f:selectItem itemLabel="" itemValue="0"/>
                        <f:selectItems value="#{RelatorioAgendamentosControle.listaSelectItemEmpresa}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="Incluir Alunos Wellhub:"/>
                <h:panelGroup layout="block" styleClass="chk-fa-container inline ">
                    <h:selectBooleanCheckbox value="#{RelatorioAgendamentosControle.gymPass}"></h:selectBooleanCheckbox>
                    <span/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.somente_executadas}:"/>
                <h:panelGroup layout="block" styleClass="chk-fa-container inline ">
                    <h:selectBooleanCheckbox value="#{RelatorioAgendamentosControle.somenteExecutadas}"></h:selectBooleanCheckbox>
                    <span/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="Somente convertidos:"/>
                <h:panelGroup layout="block" styleClass="chk-fa-container inline ">
                    <h:selectBooleanCheckbox value="#{RelatorioAgendamentosControle.somenteConvertidos}"></h:selectBooleanCheckbox>
                    <span/>
                </h:panelGroup>



                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.aulas_aconteceram}:"/>
                <h:panelGroup layout="block" styleClass="chk-fa-container inline ">
                    <h:selectBooleanCheckbox value="#{RelatorioAgendamentosControle.aconteceram}"></h:selectBooleanCheckbox>
                    <span/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.aulas_acontecer}:"/>
                <h:panelGroup layout="block" styleClass="chk-fa-container inline ">
                    <h:selectBooleanCheckbox value="#{RelatorioAgendamentosControle.acontecer}"></h:selectBooleanCheckbox>
                    <span/>
                </h:panelGroup>

            </h:panelGrid>
            <h:panelGrid columns="2" columnClasses="colunaEsquerda" border="0">
                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Total: #{fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista)}"
                              rendered="#{fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) >= 1 and empty RelatorioAgendamentosControle.tipo}"/>

                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Convertidos: #{RelatorioAgendamentosControle.relatorioAgendamentoTO.totalConvertidos}"
                              rendered="#{fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) >= 1 and empty RelatorioAgendamentosControle.tipo}"/>

                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Nenhum agendamento no período."
                              rendered="#{fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) == 0}"/>

                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Aulas experimentais: #{RelatorioAgendamentosControle.relatorioAgendamentoTO.totalAulaExperimental}"
                              rendered="#{RelatorioAgendamentosControle.mostrarAulasExperimentais and fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) >= 1}"/>
                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Convertidos: #{RelatorioAgendamentosControle.relatorioAgendamentoTO.totalConvertidosAulaExperimental}"
                              rendered="#{RelatorioAgendamentosControle.mostrarAulasExperimentais and fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) >= 1}"/>


                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Ligações: #{RelatorioAgendamentosControle.relatorioAgendamentoTO.totalLigacoes}"
                              rendered="#{RelatorioAgendamentosControle.mostrarLigacoes and fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) >= 1}"/>
                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Convertidos: #{RelatorioAgendamentosControle.relatorioAgendamentoTO.totalConvertidosLigacoes}"
                              rendered="#{RelatorioAgendamentosControle.mostrarLigacoes and fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) >= 1}"/>

                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Visitas: #{RelatorioAgendamentosControle.relatorioAgendamentoTO.totalVisita}"
                              rendered="#{RelatorioAgendamentosControle.mostrarVisitas and fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) >= 1}"/>
                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Convertidos: #{RelatorioAgendamentosControle.relatorioAgendamentoTO.totalConvertidosVisita}"
                              rendered="#{RelatorioAgendamentosControle.mostrarVisitas and fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) >= 1}"/>

                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Diárias: #{RelatorioAgendamentosControle.relatorioAgendamentoTO.totalDiarias}"
                              rendered="#{RelatorioAgendamentosControle.mostrarDiarias and fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) >= 1}"/>
                <h:outputText styleClass="alinhar" style="font-weight: bold;"
                              value="Convertidos: #{RelatorioAgendamentosControle.relatorioAgendamentoTO.totalConvertidosDiarias}"
                              rendered="#{RelatorioAgendamentosControle.mostrarDiarias and fn:length(RelatorioAgendamentosControle.relatorioAgendamentoTO.lista) >= 1}"/>

            </h:panelGrid>

        </h:panelGrid>

        <center>
            <a4j:commandLink id="btnConsultarCliente"
                             styleClass="pure-button pure-button-primary" oncomplete="#{RelatorioAgendamentosControle.msgAlert}"
                             action="#{RelatorioAgendamentosControle.consultarAgendamentos}"
                             accesskey="1" 
                             reRender="form">
                <i class="fa-icon-search" ></i> &nbsp Consultar
            </a4j:commandLink>
        </center>
        <br/>

        <h:panelGroup layout="block" id="resultado">
            <h:panelGroup layout="block" style="margin: 20px;">
                <a4j:commandLink id="btnPDF"
                                 styleClass="exportadores margin-h-10 linkPadrao"
                                 actionListener="#{ExportadorListaControle.exportar}"
                                 oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                 accesskey="3">
                    <f:attribute name="lista" value="#{RelatorioAgendamentosControle.relatorioAgendamentoTO.lista}"/>
                    <f:attribute name="filtro" value="#{RelatorioAgendamentosControle.filtros}"/>
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="itemExportacao" value="relAulaExperimental"/>
                    <f:attribute name="atributos" value="matricula=Matricula,nomePessoa=Nome,respCadastro=Resp.Agendamento,telefonesResidencialPessoa=Telefone Residencial,telefonesCelularPessoa=Telefone Celular,telefonesComercialPessoa=Telefone Comercial,tipoAgendamento_Apresentar=Tipo,colaboradorResponsavelNomeAbreviado=Colaborador Responsável,dataAgendamentoComHora=DataAgendamento,dataComparecimento_Apresentar=Comparecimento,presenteNaAula=Presente na aula,nomeProfessor=Professor,contratoCodigo=Contrato,respContrato=Resp.Contrato,consultorContrato=Consultor Contrato,dataLancamento_Apresentar=DataLancamento"/>
                    <f:attribute name="prefixo" value="Agendamentos"/>
                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                </a4j:commandLink>

                <a4j:commandLink id="btnExcel"
                                 styleClass="exportadores linkPadrao"
                                 actionListener="#{ExportadorListaControle.exportar}"
                                 oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                 accesskey="3">
                    <f:attribute name="lista" value="#{RelatorioAgendamentosControle.relatorioAgendamentoTO.lista}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="itemExportacao" value="relAulaExperimental"/>
                    <f:attribute name="atributos" value="matricula=Matricula,nomePessoa=Nome,respCadastro=Resp.Agendamento,telefonesResidencialPessoa=Telefone Residencial,telefonesCelularPessoa=Telefone Celular,telefonesComercialPessoa=Telefone Comercial,tipoAgendamento_Apresentar=Tipo,colaboradorResponsavelNomeAbreviado=Colaborador Responsável,dataAgendamentoComHora=DataAgendamento,dataComparecimento_Apresentar=Comparecimento,presenteNaAula=Presente na aula,nomeProfessor=Professor,contratoCodigo=Contrato,respContrato=Resp.Contrato,consultorContrato=Consultor Contrato,dataLancamento_Apresentar=DataLancamento"/>
                    <f:attribute name="prefixo" value="Agendamentos"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>

            </h:panelGroup>
            <h:dataTable value="#{RelatorioAgendamentosControle.relatorioAgendamentoTO.lista}"
                            var="agendamento" id="tabelaResultados"
                            styleClass="pure-g-r pure-u-11-12 margin-0-auto dataTable tableCliente"
                            style="width: calc(100% - 40px);margin-left: 20px;">

                <h:column>
                    <f:facet name="header">
                        <h:outputText value="Situação" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:graphicImage id="alunoAtivo" value="/images/botaoAtivo.png" rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.ativo}"/>
                    <h:graphicImage id="alunoInativo" value="/images/botaoInativo.png" rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.inativo}"/>
                    <h:graphicImage id="alunoVisitante" value="/images/botaoVisitante.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.visitante}"/>
                    <h:graphicImage id="alunoTrancado" value="/images/botaoTrancamento.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.trancado}"/>
                    <h:graphicImage id="alunoNormal" value="/images/botaoNormal.png" rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.ativoNormal}"/>
                    <h:graphicImage id="alunoTrancadoVencido" value="/images/botaoTrancadoVencido.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.trancadoVencido}"/>
                    <h:graphicImage id="alunoFreePass" value="/images/botaoFreePass.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.visitanteFreePass}"/>
                    <h:graphicImage id="alunoAulaAvulsa" value="/images/botaoAulaAvulsa.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.visitanteAulaAvulsa}"/>
                    <h:graphicImage id="alunoDiaria" value="/images/botaoDiaria.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.visitanteDiaria}"/>
                    <h:graphicImage id="alunoCancelado" value="/images/botaoCancelamento.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.inativoCancelamento}"/>
                    <h:graphicImage id="alunoDesistente" value="/images/botaoDesistente.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.inativoDesistente}"/>
                    <h:graphicImage id="alunoAVencer" value="/images/botaoAvencer.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.ativoAvencer}"/>
                    <h:graphicImage id="alunoVencido" value="/images/botaoVencido.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.inativoVencido}"/>
                    <h:graphicImage id="alunoCarencia" value="/images/botaoCarencia.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.ativoCarencia}"/>
                    <h:graphicImage id="alunoAtestado" value="/images/botaoAtestado.png"
                                    rendered="#{agendamento.cliente.clienteSituacaoVO_Apresentar.ativoAtestado}"/>
                </h:column>

                <rich:column sortBy="#{agendamento.matricula}">
                    <f:facet name="header">
                        <h:outputText value="Matricula" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <a4j:commandLink action="#{RelatorioAgendamentosControle.irParaTelaCliente}"
                                     oncomplete="#{RelatorioAgendamentosControle.msgAlert}"
                                     styleClass="texto-cor-azul linkPadrao">
                        <h:outputText value="#{agendamento.matricula}" />
                    </a4j:commandLink>
                </rich:column>

                <rich:column sortBy="#{agendamento.nomePessoa}">
                    <f:facet name="header">
                        <h:outputText value="Nome" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <a4j:commandLink action="#{RelatorioAgendamentosControle.irParaTelaCliente}"
                                     oncomplete="#{RelatorioAgendamentosControle.msgAlert}"
                                     styleClass="texto-cor-azul linkPadrao">
                        <h:outputText value="#{agendamento.nomePessoa}" styleClass="text"/>    
                    </a4j:commandLink>
                </rich:column>

                <rich:column sortBy="#{agendamento.tipoAgendamento_Apresentar}">
                    <f:facet name="header">
                        <h:outputText value="Tipo" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText value="#{agendamento.tipoAgendamento_Apresentar}" styleClass="text"/>
                </rich:column>

                <rich:column sortBy="#{agendamento.responsavelCadastro.nomeAbreviado}">
                    <f:facet name="header">
                        <h:outputText value="Resp. Agendamento" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText value="#{agendamento.responsavelCadastro.nomeAbreviado}" styleClass="text"/>
                </rich:column>

                <rich:column sortBy="#{agendamento.dataLancamento}">
                    <f:facet name="header">
                        <h:outputText value="Data Lançamento" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText value="#{agendamento.dataLancamento_Apresentar}" styleClass="text"/>
                </rich:column>

                <rich:column sortBy="#{agendamento.dataAgendamentoComHora}">
                    <f:facet name="header">
                        <h:outputText value="Data Agendamento" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText value="#{agendamento.dataAgendamento_Apresentar} #{agendamento.apresentarMinutoEmHora}" styleClass="text"/>
                </rich:column>

                <rich:column sortBy="#{agendamento.dataComparecimento}">
                    <f:facet name="header">
                        <h:outputText value="Comparecimento" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText value="#{agendamento.dataComparecimento_Apresentar}" styleClass="text"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.presente_aula}" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText value="#{msg_aplic.sim}" styleClass="text" rendered="#{agendamento.presenca}"/>

                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Professor:" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText value="#{agendamento.nomeProfessor}" styleClass="text"/>

                </rich:column>

                <rich:column sortBy="#{agendamento.contrato.codigo}"> 
                    <f:facet name="header">
                        <h:outputText value="Contrato" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText id="codigoContratoAgendamento" value="#{agendamento.contrato.codigo}" 
                                  rendered="#{agendamento.contrato.codigo > 0}"
                                  styleClass="text"/>
                </rich:column>

                <rich:column sortBy="#{agendamento.contrato.responsavelContrato.nomeAbreviado}}"> 
                    <f:facet name="header">
                        <h:outputText value="Resp. Contrato" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText value="#{agendamento.contrato.responsavelContrato.nomeAbreviado}" styleClass="text"/>
                </rich:column>

                <rich:column sortBy="#{agendamento.colaboradorResponsavel}">
                    <f:facet name="header">
                        <h:outputText value="Consultor Responsável CRM" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText value="#{agendamento.colaboradorResponsavel}" styleClass="text"/>
                </rich:column>
            </h:dataTable>
        </h:panelGroup>

    </h:form>
</f:view>
