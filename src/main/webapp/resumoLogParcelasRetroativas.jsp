<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="includes/include_import_minifiles.jsp"%>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <jsp:include page="include_log.jsp"/>
    <%@include file="include_modal_renegociadas.jsp"%>
    <title>
        <h:outputText value="Resumo das renegociações"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" style="overflow: hidden" >
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <c:set var="titulo" scope="session" value="Resumo das renegociações de parcelas - Total:${RelControleOperacoesControle.controleOperacoesRelVO.qtdRenegociacaoParcelaRetroativa} "/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-controle-de-operacoes-de-excecoes-adm/"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <h:panelGrid columns="1" width="100%" styleClass="font-size-Em-max" >
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup layout="block">
                            <a4j:commandLink id="exportarExcel"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaLogRenegociacaoParcelasRetroativas}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="lista" value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaLogRenegociacaoParcelasRetroativas}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                                <f:attribute name="atributos"  value="clienteNome=Nome do cliente,responsavelAlteracao=Responsável pela alteração,dataAlteracao_Apresentar=Data Alteração,justificativa=Justificativa"/>
                                <f:attribute name="prefixo" value="ControleOpExcecao"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>
                            <%--BOTÃO PDF--%>
                            <a4j:commandLink id="exportarPdf"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaLogRenegociacaoParcelasRetroativas}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="lista" value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaLogRenegociacaoParcelasRetroativas}"/>
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                                <f:attribute name="atributos" value="clienteNome=Nome do cliente,responsavelAlteracao=Responsável pela alteração,dataAlteracao_Apresentar=Data Alteração,justificativa=Justificativa"/>
                                <f:attribute name="prefixo" value="ControleOpExcecao"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                    <rich:dataTable width="100%" styleClass="tabelaSimplesCustom font-size-Em-max" id="tabelaRes"
                                    value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaLogRenegociacaoParcelasRetroativas}" rows="50" var="log" rowKeyVar="status">
                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                        <rich:column sortBy="#{log.codigo}" styleClass="col-text-align-left" headerClass="col-text-align-left" >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-bold  texto-size-14-real texto-cor-cinza texto-font"  value="Nome do cliente" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{log.clienteNome}"/>
                            <h:outputText value="" />
                        </rich:column>

                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-bold  texto-size-14-real texto-cor-cinza texto-font" value="Data da alteração" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{log.dataAlteracao_Apresentar}"/>
                        </rich:column>

                        <rich:column sortBy="#{log.responsavelAlteracao}"  styleClass="col-text-align-left" headerClass="col-text-align-left"  >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-bold  texto-size-14-real texto-cor-cinza texto-font" value="Responsável pela alteração" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{log.responsavelAlteracao}"/>
                        </rich:column>

                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left"  >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-bold  texto-size-14-real texto-cor-cinza texto-font" value="Justificativa" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{log.justificativa}"/>
                        </rich:column>

                        <rich:column >
                            <a4j:commandLink  action="#{RelControleOperacoesControle.abrirModalLog}" reRender="form, formLog"
                                              oncomplete="Richfaces.showModalPanel('panelLog')"
                                              styleClass="linkPadrao texto-cor-azul texto-size-16-real">
                                <i class="fa-icon-search"></i>
                                <f:param name="log" value="#{log}"/>
                            </a4j:commandLink>
                        </rich:column>
                        <rich:column >
                            <a4j:commandLink  action="#{ModalRenegociadasControle.abrirModalRenegociacoes}" reRender="mensagens, formModalRenegociadas"
                                              oncomplete="#{ModalRenegociadasControle.msgAlert};#{ModalRenegociadasControle.mensagemNotificar}"
                                              title="Histórico de Renegociações"
                                              styleClass="linkPadrao texto-cor-azul texto-size-16-real">
                                <i class="fa-icon-refresh"></i>
                                <f:param name="log" value="#{log}"/>
                            </a4j:commandLink>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:tabelaRes" maxPages="10" id="sctabelaRes" />
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>

