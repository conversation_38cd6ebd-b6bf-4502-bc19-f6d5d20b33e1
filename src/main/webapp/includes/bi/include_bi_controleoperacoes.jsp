<%--
    Document   : include_bi_controleoperacoes
    Created on : 05/07/2011
    Author     : Carla
--%>
<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="biContOp" styleClass="container-bi"
              rendered="#{LoginControle.permissaoAcessoMenuVO.biOperacaoExececoes && !BIControle.configuracaoBI.controleOp.naLixeira}">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.biOperacaoExececoes || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.controleOp.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarControleOp}" reRender="containerControleOp"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                         status="nenhumStatus"
                         styleClass="btn-atualizar-bi"/>
        <h:panelGroup layout="block" id="containerControleOp">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.controleOp.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Controle de Opera��es de Exce��es" styleClass="bi-titulo pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>

                <div class="ghost">
                    <table>
                        <tr>
                            <td style="width: 30%;"><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td style="width: 30%;"><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td style="width: 30%;"><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td style="width: 30%;"><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td style="width: 30%;"><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                    </table>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          rendered="#{!BIControle.configuracaoBI.controleOp.apresentarBoxCarregar || BIControle.biCarregado}">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Controle de Opera��es de Exce��es" styleClass="bi-titulo pull-left"/>

                        <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                      style="float: left;margin-top: 4.4%;margin-left: 1%"
                                      value="#{SuperControle.urlBaseConhecimento}bi-controle-de-operacoes-de-excecoes-adm/"
                                      title="Clique e saiba mais: BI - Controle de Opera��es de Exce��o" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                        </h:outputLink>

                        <a4j:commandLink id="consultarContOp"
                                         reRender="containerControleOp"
                                         oncomplete="montarTips();"
                                         action="#{RelControleOperacoesControle.filtrarPorOperacaoPorEmpresaTela}">
                            <i title="Consultar Dados Controle de Opera��es de Exce��es"
                               class="tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                        </a4j:commandLink>
                        <h:panelGroup layout="block" styleClass="pull-right calendarSemInputBI col-text-align-right">
                            <div title="${RelControleOperacoesControle.dataBase_ApresentarMesDia}"
                                 style="display: inline-flex;margin-top: 1em;vertical-align: top;"
                                 class="dateTimeCustom alignToRight tooltipster">
                                <rich:calendar id="dataInicioCO"
                                               value="#{RelControleOperacoesControle.dataBaseFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="#{RelControleOperacoesControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{RelControleOperacoesControle.filtrarPorOperacaoPorEmpresaTela}"
                                                 oncomplete="montarTips();" reRender="containerControleOp"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      styleClass="pull-right calendarSemInputBI" style="margin-right: 12px;">
                            <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')"
                                             action="#{BIControle.biAtualizarParam}"
                                             reRender="formPanelFiltroCol, tituloFiltro">
                                <i title="Filtrar Por Colaborador"
                                   class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">
                                    <span class="badgeItem2Icon" data-bagde="${BIControle.qtdColContrOperacoes}"></span>
                                </i>
                                <a4j:actionparam name="biAtualizar" value="CONTROLE_OPERACOES"></a4j:actionparam>
                                <a4j:actionparam name="biAtualizarAbrirConsulta"
                                                 value="biAtualizarAbrirConsulta"></a4j:actionparam>
                                <a4j:actionparam name="reRenderBi" value="containerControleOp"></a4j:actionparam>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <style>
                    .colunaindicador {
                        text-align: right;
                        padding-right: 20px;
                    }
                </style>
                <rich:dataTable id="listaControleOperacoes" width="100%"
                                styleClass="tabelaSimplesCustom tabelaContOp semPrimeiraBorda"
                                value="#{RelControleOperacoesControle.listaControleOperacoesRelVOs}"
                                var="pendenciaRelVO">

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdClientesInativosComPeriodoAcesso && (pendenciaRelVO.qtdClientesInativosComPeriodoAcesso > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Apresenta os clientes que possuem contratos inativos (vencidos ou cancelados) que tem per�odo de acesso  vigente, podendo o aluno acessar academia mesmo sem contrato ativo."
                                      value="Clientes com Contrato Inativo com Per�odo de Acesso"/>
                    </rich:column>
                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdClientesInativosComPeriodoAcesso && (pendenciaRelVO.qtdClientesInativosComPeriodoAcesso > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink
                                id="controleOperacoesClientesInativosComPeriodoAcesso"
                                styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                action="#{RelControleOperacoesControle.selecionarClientesInativosComPeriodoAcesso}"
                                oncomplete="abrirPopup('./controleOperacoesResumoClienteRes.jsp', 'ControleOperacoesResumoClienteRes', 780, 595);"
                                value="#{pendenciaRelVO.qtdClientesInativosComPeriodoAcesso} "/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdOperacoesContratoRetroativas && (pendenciaRelVO.qtdOperacoesContratoRetroativas > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI apresenta os clientes que realizaram opera��es de contrato retroativos (passados) em seus contratos.</br> A pesquisa considera o contrato atual, a partir do primeiro dia do m�s pesquisado at� o dia escolhido para a pesquisa. Lembrando que � considerado a data de lan�amento da opera��o para a pesquisa de datas."
                                      value="Opera��es de Contrato Retroativas"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdOperacoesContratoRetroativas && (pendenciaRelVO.qtdOperacoesContratoRetroativas > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesContratoRetroativas"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarOperacoesContratoRetroativas}"
                                         oncomplete="abrirPopup('./controleOperacoesResumoClienteRes.jsp', 'ControleOperacoesResumoClienteRes', 780, 595);"
                                         value="#{pendenciaRelVO.qtdOperacoesContratoRetroativas} "/>
                    </rich:column>


                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdExclusaoVisitantes && (pendenciaRelVO.qtdExclusaoVisitantes > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra os logs de clientes que visitaram a academia e que tamb�m tiveram seus logs exclu�dos.</br> A pesquisa � feita a partir do primeiro dia do m�s pesquisado at� o dia escolhido para a pesquisa."
                                      value="Exclus�o de Visitantes"/>
                    </rich:column>
                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdExclusaoVisitantes && (pendenciaRelVO.qtdExclusaoVisitantes > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesExclusaoVis"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarLogsExclusaoVisitantes}"
                                         oncomplete=" abrirPopup('./controleOperacoesResumoLogRes.jsp', 'ControleOperacoesResumoLogRes', 780, 595);"
                                         value="#{pendenciaRelVO.qtdExclusaoVisitantes} ">
                            <f:setPropertyActionListener value="Exclus�o de Visitantes"
                                                         target="#{RelControleOperacoesControle.nomeTela}"/>
                        </a4j:commandLink>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdAlteracaoConsultorContrato && (pendenciaRelVO.qtdAlteracaoConsultorContrato > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra o n�mero de vezes que houveram altera��es de consultor de um contrato no per�odo consultado.</br> Cada contrato possui um consultor respons�vel e o BI consultores com contratos alterados era somar todas as vezes que, por algum motivo, o consultor de um contrato for modificado."
                                      value="Consultores de Contrato Alterados"/>
                    </rich:column>
                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdAlteracaoConsultorContrato && (pendenciaRelVO.qtdAlteracaoConsultorContrato > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesAlteracaoConsultorContrato"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarLogsAlteracaoConsultorContrato}"
                                         oncomplete="abrirPopup('./controleOperacoesResumoLogRes.jsp', 'ControleOperacoesResumoLogRes', 780, 595);"
                                         value="#{pendenciaRelVO.qtdAlteracaoConsultorContrato} ">
                            <f:setPropertyActionListener value="Consultores de Contrato Alterados"
                                                         target="#{RelControleOperacoesControle.nomeTela}"/>
                        </a4j:commandLink>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdEstornoContratoAdmin  && (pendenciaRelVO.qtdEstornoContratoAdmin > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra logs (hist�rico de altera��o de contratos) com o motivo registrado pelo usu�rio de contratos estornados por administrador, ou seja, ele apresenta um hist�rico dos contratos que foram alterados e quem foi o administrador que fez a altera��o.</br> Para a pesquisa, o BI considera a data da consulta a partir do primeiro dia do m�s pesquisado at� o dia escolhido para a pesquisa."
                                      value="Estornos de Contrato - Administrador"/>
                    </rich:column>
                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdEstornoContratoAdmin  && (pendenciaRelVO.qtdEstornoContratoAdmin > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesEstornoAdmin"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarLogsEstornoContratoAdmin}"
                                         oncomplete="abrirPopup('./controleOperacoesResumoEstornoRes.jsp', 'ControleOperacoesResumoLogRes', 780, 595);"
                                         value="#{pendenciaRelVO.qtdEstornoContratoAdmin} ">
                            <f:setPropertyActionListener value="Estornos de Contrato - Administrador"
                                                         target="#{RelControleOperacoesControle.nomeTela}"/>
                        </a4j:commandLink>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdEstornoContratoRecorrencia  && (pendenciaRelVO.qtdEstornoContratoRecorrencia > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra logs (hist�rico de altera��o de contratos) com o motivo registrado pelo usu�rio de contratos estornados atrav�s dos planos de recorr�ncia.</br> Para a pesquisa, o BI considera a data da consulta a partir do primeiro dia do m�s pesquisado at� o dia escolhido para a pesquisa."
                                      value="Estornos de Contrato - Recorr�ncia"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdEstornoContratoRecorrencia && (pendenciaRelVO.qtdEstornoContratoRecorrencia > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesEstornoRecorrencia"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarLogsEstornoContratoRecorrencia}"
                                         oncomplete="abrirPopup('./controleOperacoesResumoEstornoRes.jsp', 'ControleOperacoesResumoLogRes', 780, 595);"
                                         value="#{pendenciaRelVO.qtdEstornoContratoRecorrencia} ">
                            <f:setPropertyActionListener value="Estornos de Contrato - Recorr�ncia"
                                                         target="#{RelControleOperacoesControle.nomeTela}"/>
                        </a4j:commandLink>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdEstornoContratoUsuarioComum && (pendenciaRelVO.qtdEstornoContratoUsuarioComum > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra logs com o motivo registrado pelo usu�rio de contratos estornados por usu�rio comum, ou seja, ele apresenta um hist�rico dos contratos que foram alterados e quem foi o usu�rio comum que fez a altera��o.</br> Para a pesquisa, o BI considera a data da consulta a partir do primeiro dia do m�s pesquisado at� o dia escolhido para a pesquisa."
                                      value="Estornos de Contrato - Usu�rio Comum"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdEstornoContratoUsuarioComum && (pendenciaRelVO.qtdEstornoContratoUsuarioComum > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesEstornoComum"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarLogsEstornoContratoUsuarioComum}"
                                         oncomplete=" abrirPopup('./controleOperacoesResumoEstornoRes.jsp', 'ControleOperacoesResumoLogRes', 780, 595);"
                                         value="#{pendenciaRelVO.qtdEstornoContratoUsuarioComum} ">
                            <f:setPropertyActionListener value="Estornos de Contrato - Usu�rio Comum"
                                                         target="#{RelControleOperacoesControle.nomeTela}"/>
                        </a4j:commandLink>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdEstornoRecibo && (pendenciaRelVO.qtdEstornoRecibo > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Aqui voc� ter� o controle das opera��es de estorno de recibo. Este BI registra o usu�rio, a data, a hora e o aluno.</br> Ao clicar na lupa � poss�vel visualizar o log, exibindo todas as informa��es do recibo que foi estornado."
                                      value="Estornos de Recibo"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdEstornoRecibo && (pendenciaRelVO.qtdEstornoRecibo > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesEstornoRecibo"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarLogsEstornoRecibo}"
                                         oncomplete="abrirPopup('./controleOperacoesResumoLogRes.jsp', 'ControleOperacoesResumoLogRes', 780, 595);"
                                         value="#{pendenciaRelVO.qtdEstornoRecibo} ">
                            <f:setPropertyActionListener value="Estornos de Recibo"
                                                         target="#{RelControleOperacoesControle.nomeTela}"/>
                        </a4j:commandLink>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdAlteracoesDataBaseContrato && (pendenciaRelVO.qtdAlteracoesDataBaseContrato > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra contratos em que sua data de lan�amento foi modificada por algum motivo.</br> Ele considera a data da consulta a partir do primeiro dia do m�s pesquisado at� o dia escolhido para a pesquisa."
                                      value="Contratos com DataBase Alterada"/>
                    </rich:column>


                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdAlteracoesDataBaseContrato  && (pendenciaRelVO.qtdAlteracoesDataBaseContrato > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesDataBaseContrato"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarAlteracoesDataBaseContrato}"
                                         oncomplete="#{RelControleOperacoesControle.abriRichModalContratosAlterados}"
                                         value="#{pendenciaRelVO.qtdAlteracoesDataBaseContrato} "/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdAlteracoesDataBasePagamento  && (pendenciaRelVO.qtdAlteracoesDataBasePagamento > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra os contratos que tiveram sua data de pagamento modificada por algum motivo.</br> Ele considera a data da consulta a partir do primeiro dia do m�s pesquisado at� o dia escolhido para a pesquisa."
                                      value="Pagamentos com DataBase Alterada"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdAlteracoesDataBasePagamento && (pendenciaRelVO.qtdAlteracoesDataBasePagamento > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesDataBasePagamento"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarAlteracoesDataBasePagamento}"
                                         oncomplete="abrirPopup('faces/controleOperacoesResumoPagamentoRes.jsp', 'ControleOperacoesResumo', 980, 650);"
                                         value="#{pendenciaRelVO.qtdAlteracoesDataBasePagamento} "/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdRenegociacaoParcelaRetroativa  && (pendenciaRelVO.qtdRenegociacaoParcelaRetroativa > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra as parcelas que foram renegociadas.</br> Ele considera a data da consulta a partir do primeiro dia do m�s pesquisado at� o dia escolhido para a pesquisa."
                                      value="Renegocia��es de parcelas"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdRenegociacaoParcelaRetroativa && (pendenciaRelVO.qtdRenegociacaoParcelaRetroativa > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesParcelaRetroativas"
                                         action="#{RelControleOperacoesControle.selecionarRenegociacaoParcelaRetroativa}"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         oncomplete="abrirPopup('faces/resumoLogParcelasRetroativas.jsp', 'ControleOperacoesResumo', 980, 650);"
                                         value="#{pendenciaRelVO.qtdRenegociacaoParcelaRetroativa} "/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdAlteracoesRecibo && (pendenciaRelVO.qtdAlteracoesRecibo > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra logs de edi��es de pagamentos j� realizadas.</br> Na pesquisa, ele considera a data da consulta a partir do primeiro dia do m�s pesquisado at� o dia escolhido para a pesquisa.
                              </br> Ao clicar na quantidade de edi��es de pagamento voc� ver� uma lista de logs agrupados por chave prim�ria referentes a altera��es de pagamento.
                              </br> Veja o exemplo ao lado. Ao clicar na lupa Botao Lupa.jpg ver� mais detalhes desse log, incluindo os valores anteriores a edi��o e os valores atuais ap�s a edi��o."
                                      value="Edi��es de Pagamento"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdAlteracoesRecibo && (pendenciaRelVO.qtdAlteracoesRecibo > 0 || RelControleOperacoesControle.exibirTodos)}">

                        <a4j:commandLink id="controleOperacoesRecibo"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarAlteracoesRecibo}"
                                         oncomplete="abrirPopup('./resumoAlteracoesPagamentoRes.jsp', 'ControleOperacoesResumoLogPagamento', 980, 650);"
                                         value="#{pendenciaRelVO.qtdAlteracoesRecibo} "/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdContratosCancelamento && (pendenciaRelVO.qtdContratosCancelamento > 0 || RelControleOperacoesControle.exibirTodos)}">

                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra os contratos que foram cancelados.</br>
Ele considera a data da consulta a partir do primeiro dia do m�s pesquisado at� o dia escolhido para a pesquisa.</br>
Para usu�rios com permiss�o, o sistema permitir� pesquisar por colaboradores de outros grupos ou do pr�prio grupo. Para isso � s� clicar no colaborador do grupo escolhido, em V�nculos de Carteira."
                                      value="Contratos Cancelados"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdContratosCancelamento && (pendenciaRelVO.qtdContratosCancelamento > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesCancelados"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         oncomplete="abrirPopup('./includes/bi/lista_contratos_cancelados.jsp', 'ContratosCancelados', 800, 650);"
                                         value="#{pendenciaRelVO.qtdContratosCancelamento} "/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdContratosTransferidosCancelados && (pendenciaRelVO.qtdContratosTransferidosCancelados > 0 || RelControleOperacoesControle.exibirTodos)}">

                        <h:outputText styleClass="bi-table-text tooltipster"
                                      value="Transferidos com Contrato Cancelados"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdContratosTransferidosCancelados && (pendenciaRelVO.qtdContratosTransferidosCancelados > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesTransferidosCancelados"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarContratosTranferidosCancelados}"
                                         oncomplete="abrirPopup('./includes/bi/lista_clientes_tranferidos_contrato_cancelado.jsp', 'ClientesTransferidosContratoCancelado', 800, 650);"
                                         value="#{pendenciaRelVO.qtdContratosTransferidosCancelados} "/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdParcelasCanceladas && (pendenciaRelVO.qtdParcelasCanceladas > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra o controle das parcelas que foram canceladas (logs com o registro de usu�rio data, hora e o aluno e sua parcela que foi cancelada)."
                                      value="Parcelas Canceladas"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdParcelasCanceladas && (pendenciaRelVO.qtdParcelasCanceladas > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesParcelasCancelados"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarCancelamentoParcelas}"
                                         oncomplete="abrirPopup('./controleOperacoesResumoObservacaoOperacaoRes.jsp', 'ControleOperacoesResumoClienteRes', 820, 595);"
                                         value="#{pendenciaRelVO.qtdParcelasCanceladas} ">
                            <f:setPropertyActionListener value="Parcelas Canceladas"
                                                         target="#{RelControleOperacoesControle.nomeTela}"/>
                        </a4j:commandLink>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdClientesComBonus && (pendenciaRelVO.qtdClientesComBonus > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra clientes que possuem algum tipo de b�nus, na empresa, sendo que, ele considera o dia da consulta que esteja entre a data de in�cio e data final de lan�amento do b�nus ou considera os b�nus que v�o iniciar no futuro (b�nus iniciando na data da consulta do BI ou ap�s a data), se for o m�s atual. Se n�o for o m�s atual, ser� apresentado somente os clientes com b�nus em que a data da consulta do BI esteja entre o seu in�cio e t�rmino.</br>
Obs.: O relat�rio vai trazer alunos com status inativo caso o este possua b�nus dentro do per�odo de pesquisa.</br>
O relat�rio vai exibir tamb�m as pessoas que n�o tem nenhum v�nculo com respons�vel, nas op��es do b�nus.</br>
A op��o de pesquisa por v�nculos de colaboradores n�o ir� apresentar os colaboradores sem grupo (CRM) para pesquisa, mas, se a pesquisa dos alunos for feita sem filtro de pesquisa, ent�o, ser�o apresentados todos os alunos independente dos v�nculos existentes com os colaboradores.</br>
Se o cliente tiver mais de um b�nus lan�ado, o sistema s� vai apresentar o primeiro b�nus."
                                      value="Clientes com B�nus"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdClientesComBonus && (pendenciaRelVO.qtdClientesComBonus > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesClientesComBonus"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarClientesComBonus}"
                                         oncomplete="abrirPopup('./controleOperacoesResumoClientesBonus.jsp', 'ControleOperacoesResumoClienteBonus', 780, 595);"
                                         value="#{pendenciaRelVO.qtdClientesComBonus} "/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdClientesComFreePass && (pendenciaRelVO.qtdClientesComFreePass > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI apresentar� um relat�rio de alunos que est�o acessando academia porque possuem um Free-Pass (uma esp�cie de passe livre que permite que os visitantes tenham entradas gratuitas na empresa)."
                                      value="Clientes com FreePass"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdClientesComFreePass && (pendenciaRelVO.qtdClientesComFreePass > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesClientesComFreePass"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarClientesComFreePass}"
                                         oncomplete="abrirPopup('./controleOperacoesResumoClienteRes.jsp', 'ControleOperacoesResumoClienteFreePass', 780, 595);"
                                         value="#{pendenciaRelVO.qtdClientesComFreePass} "/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdClientesComGymPass && (pendenciaRelVO.qtdClientesComGymPass > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI apresentar� um relat�rio de alunos que est�o acessando academia porque possuem um acesso liberado GymPass."
                                      value="Clientes com Wellhub"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdClientesComGymPass && (pendenciaRelVO.qtdClientesComGymPass > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesClientesComGymPass"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         action="#{RelControleOperacoesControle.selecionarClientesComGymPass}"
                                         oncomplete="abrirPopup('./controleOperacoesResumoClienteRes.jsp', 'ControleOperacoesResumoClienteGymPass', 780, 595);"
                                         value="#{pendenciaRelVO.qtdClientesComGymPass} "/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdContratosTipoBolsa && (pendenciaRelVO.qtdContratosTipoBolsa > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este BI mostra o n�mero de alunos com contrato ativo do tipo bolsa."
                                      value="Alunos Bolsa"/>
                    </rich:column>
                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdContratosTipoBolsa && (pendenciaRelVO.qtdContratosTipoBolsa > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="controleOperacoesContratosBolsa"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         oncomplete="abrirPopup('./controleOperacoesResumoClienteRes.jsp', 'ControleOperacoesResumoAlunoBolsa', 780, 595);"
                                         action="#{RelControleOperacoesControle.selecionarAlunoBolsa}"
                                         value="#{pendenciaRelVO.qtdContratosTipoBolsa}"/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdClientesAutorizacaoNaoRenovavel && (pendenciaRelVO.qtdClientesAutorizacaoNaoRenovavel > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este indicador mostra a quantidade de alunos que tem autoriza��o de cobran�a,<br/>seu contrato vence no m�s filtrado e n�o � renov�vel automaticamente. Este indicador n�o se aplica � datas.<br/>Ex: dia 1 at� 10, o resultado apresentado ser� sempre o m�s todo."
                                      value="Clientes com autoriza��o sem renova��o autom�tica"/>
                    </rich:column>
                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdClientesAutorizacaoNaoRenovavel && (pendenciaRelVO.qtdClientesAutorizacaoNaoRenovavel > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="qtdClientesAutorizacaoNaoRenovavel"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         oncomplete="abrirPopup('./controleOperacoesResumoClientesAutorizacaoContrato.jsp', 'ClientesAutorizacaoContrato', 800, 600);"
                                         action="#{RelControleOperacoesControle.selecionarClientesAutorizacaoNaoRenovavel}"
                                         value="#{pendenciaRelVO.qtdClientesAutorizacaoNaoRenovavel}"/>
                    </rich:column>


                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdClientesAutorizacaoRenovavel && (pendenciaRelVO.qtdClientesAutorizacaoRenovavel > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este indicador mostra a quantidade de alunos que tem autoriza��o de cobran�a,<br/>seu contrato vence no m�s filtrado e � renov�vel automaticamente. Este indicador n�o se aplica � datas.<br/>Ex: dia 1 at� 10, o resultado apresentado ser� sempre o m�s todo."
                                      value="Clientes com autoriza��o e renova��o autom�tica"/>
                    </rich:column>
                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdClientesAutorizacaoRenovavel && (pendenciaRelVO.qtdClientesAutorizacaoRenovavel > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="qtdClientesAutorizacaoRenovavel"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         oncomplete="abrirPopup('./controleOperacoesResumoClientesAutorizacaoContrato.jsp', 'ClientesAutorizacaoContrato', 800, 600);"
                                         action="#{RelControleOperacoesControle.selecionarClientesAutorizacaoRenovavel}"
                                         value="#{pendenciaRelVO.qtdClientesAutorizacaoRenovavel}"/>
                    </rich:column>


                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarValorDescontos && (pendenciaRelVO.valorDescontos > 0.0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="#{msg_aplic.prt_valor_em_descontos_tip}"
                                      value="#{msg_aplic.prt_valor_em_descontos}"/>
                    </rich:column>

                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarValorDescontos && (pendenciaRelVO.valorDescontos > 0.0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="valordescontos"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         oncomplete="abrirPopup('./controleOperacoesResumoDescontos.jsp', 'Descontos', 800, 700);"
                                         action="#{RelControleOperacoesControle.selecionarDescontos}"
                                         value="#{MovPagamentoControle.empresaLogado.moeda} #{pendenciaRelVO.valorDescontosApresentar}"/>
                    </rich:column>

                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdClientesExcluidosTreinoWeb && (pendenciaRelVO.qtdClientesExcluidosTreinoWeb > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este indicador mostra os alunos que possu�am v�nculos no treinoweb e l� foram exclu�dos."
                                      value="Alunos exclu�dos do treinoweb que possu�am v�nculos"/>
                    </rich:column>
                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdClientesExcluidosTreinoWeb && (pendenciaRelVO.qtdClientesExcluidosTreinoWeb > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="qtdClienteExcluidoTreinoWeb"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         oncomplete="abrirPopup('./controleOperacoesResumoLogRes.jsp', 'ControleOperacoesResumoLogRes', 780, 595);"
                                         action="#{RelControleOperacoesControle.selecionarLogsExclusaoClientesVinculadoTreinoWeb}"
                                         value="#{pendenciaRelVO.qtdClientesExcluidosTreinoWeb}">
                            <f:setPropertyActionListener value="Alunos exclu�dos do treinoweb que possu�am v�nculos"
                                                         target="#{RelControleOperacoesControle.nomeTela}"/>
                        </a4j:commandLink>
                    </rich:column>


                    <rich:column
                            rendered="#{pendenciaRelVO.apresentarQtdClientesExcluidos && (pendenciaRelVO.qtdClientesExcluidos > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <h:outputText styleClass="bi-table-text tooltipster"
                                      title="Este indicador mostra os alunos que foram excluidos da base de dados "
                                      value="Alunos exclu�dos da base de dados"/>
                    </rich:column>
                    <rich:column styleClass="colunaindicador"
                                 rendered="#{pendenciaRelVO.apresentarQtdClientesExcluidos && (pendenciaRelVO.qtdClientesExcluidos > 0 || RelControleOperacoesControle.exibirTodos)}">
                        <a4j:commandLink id="qtdClienteExcluido"
                                         styleClass="texto-size-16 bi-font-family bi-cor-azul"
                                         oncomplete="abrirPopup('./controleOperacoesResumoLogRes.jsp', 'ControleOperacoesResumoLogRes', 780, 595);"
                                         action="#{RelControleOperacoesControle.selecionarLogsExclusaoClientes}"
                                         value="#{pendenciaRelVO.qtdClientesExcluidos}">
                            <f:setPropertyActionListener value="Alunos exclu�dos da base de dados"
                                                         target="#{RelControleOperacoesControle.nomeTela}"/>
                        </a4j:commandLink>
                    </rich:column>


                </rich:dataTable>
                <h:panelGroup layout="block" styleClass="bi-panel" style="text-align: right">
                    <a4j:commandLink value="#{!RelControleOperacoesControle.exibirTodos ? 'Ver mais' : 'Ver menos'}"
                                     oncomplete="montarTips();"
                                     styleClass="linkPadrao texto-font texto-size-16 texto-cor-azul tooltipster"
                                     title="Mostrar/Esconder indicadores sem resultado."
                                     reRender="containerControleOp">
                        <h:outputText style="margin-left: 5px;vertical-align: middle;"
                                      styleClass="texto-font texto-cor-azul texto-size-16 fa-icon-minus-sign"
                                      rendered="#{RelControleOperacoesControle.exibirTodos}"/>
                        <h:outputText style="margin-left: 5px;vertical-align: middle;"
                                      styleClass="texto-font texto-cor-azul texto-size-16 fa-icon-plus-sign"
                                      rendered="#{!RelControleOperacoesControle.exibirTodos}"/>
                        <f:setPropertyActionListener value="#{!RelControleOperacoesControle.exibirTodos}"
                                                     target="#{RelControleOperacoesControle.exibirTodos}"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>
