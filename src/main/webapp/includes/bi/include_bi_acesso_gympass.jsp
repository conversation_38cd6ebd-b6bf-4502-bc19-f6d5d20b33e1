<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="bi-gympass" styleClass="container-bi" rendered="#{LoginControle.permissaoAcessoMenuVO.biGymPass}">
    <a4j:commandLink reRender="containerGymPass"
                     styleClass="btn-atualizar-bi" status="nenhumStatus" onclick="abrirCarregandoPoBI(this)"
                     oncomplete="hideCarregandoPorBI(this)"/>
    <h:panelGroup layout="block" id="containerGymPass" style="">
        <h:panelGroup layout="block"
                      styleClass="bi-unloaded">
            <h:panelGroup styleClass="bi-header" layout="block">
                <h:panelGroup layout="block" styleClass="bi-panel">
                    <h:outputText value="Wellhub" styleClass="bi-titulo pull-left"
                                  id="tituloBIGymPass"/>
                    <h:panelGroup layout="block" styleClass="pull-left">
                        <div
                                style="margin-left:0.8em">
                            <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                          value="#{SuperControle.urlBaseConhecimento}bi-gympass-adm/"
                                          title="Clique e saiba mais: Wellhub" target="_blank">
                                <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important;"></i>
                            </h:outputLink>
                        </div>
                    </h:panelGroup>
                    <a4j:commandLink id="consultarAcessosGymPass"
                                     reRender="containerGymPass"
                                     styleClass="font-size-Em tooltipster"
                                     action="#{AcessoGymPassControle.prepararDadosGymPass}">
                        <i title="Consultar Acessos de clientes Wellhub"
                           class="tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                    </a4j:commandLink>
                    <a4j:commandLink
                                     oncomplete="Richfaces.showModalPanel('panelFiltroAcessoGympass')">
                        <i title="Filtro"
                           class="tooltipster fa-icon-filter bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                    </a4j:commandLink>
                    <rich:modalPanel id="panelFiltroAcessoGympass" width="480" minHeight="220" styleClass="novaModal" autosized="true" onhide="processModalClose()">
                        <a4j:jsFunction name="processModalClose" action="#{AcessoGymPassControle.prepararDadosGymPass}" reRender="containerGymPass" />
                        <f:facet name="header">
                            <h:panelGroup>
                                <h:outputText value="Filtro Wellhub"/>
                            </h:panelGroup>
                        </f:facet>
                        <f:facet name="controls">
                            <h:panelGroup>
                                 <span class="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                                    id="hidelinkFiltroConvenioCobranca"
                                    onclick="Richfaces.hideModalPanel('panelFiltroAcessoGympass');">
                                 </span>
                            </h:panelGroup>
                        </f:facet>
                        <div style="padding: 2rem; display: inline-flex;margin-top: 0em;vertical-align: top;"
                             class="tooltipster dateTimeCustom alignToRight">
                            <h:selectBooleanCheckbox id="ConsiderarAcessoPeloCheckin"
                                                     style="margin-right: 5px" styleClass="campos"
                                                     value="#{AcessoGymPassControle.acessoCheckinGympass}">
                            </h:selectBooleanCheckbox>
                            <h:outputText styleClass="texto-size-14 bi-font-family bi-cor-cinza tooltipster" style="margin-right: 25px"
                                          title="Caso este checkbox esteja marcado, check-ins tamb�m ser�o considerados na quantidade de acessos."
                                          value="Considerar acesso pelo Check-In"/>
                        </div>
                    </rich:modalPanel>

                    <h:panelGroup layout="block" styleClass="pull-right calendarSemInputBI col-text-align-right">
                        <div title="${AcessoGymPassControle.dataBase_ApresentarMesAno}"
                             id="divDataInicioGymPass"
                             style="display: inline-flex;margin-top: 1em;vertical-align: top;"
                             class="tooltipster dateTimeCustom alignToRight">
                            <rich:calendar id="dataInicioAcessoGymPass"
                                           value="#{AcessoGymPassControle.dataInicio}"
                                           inputSize="8"
                                           showInput="false"
                                           inputClass="forcarSemBorda"
                                           buttonIcon="#{AcessoGymPassControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="false"
                                           todayControlMode="hidden"
                                           showFooter="false"
                                           styleClass="special"
                                           zindex="2">
                                <a4j:support event="oncurrentdateselected"
                                             action="#{AcessoGymPassControle.acaoMudarMes}"
                                             reRender="containerGymPass"/>
                            </rich:calendar>
                        </div>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="bi-panel" style="text-align: right">
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block" style="margin-top: 20px;">
                <h:panelGroup id="bi-header-gympass" styleClass="" layout="block" style="padding-bottom: 10px;">
                    <h:panelGroup layout="block" styleClass="gr-container-totalizador" style="padding-bottom: 30px;">
                        <h:panelGroup layout="block" styleClass="gr-totalizador"
                                      style="width: calc(150% / 3 - 1px);margin-bottom: 48px;">
                            <a4j:commandLink
                                    oncomplete="abrirPopup('./listaBIAlunosAcessoGympass.jsp', 'AcessoAlunosBIGymPass', 780, 595);"
                                    actionListener="#{AcessoGymPassControle.selecionarAlunosGympass}"
                                    style="display: block;font-family: 'Arial';font-style: normal;font-weight: 700;font-size: 40px;line-height: 46px;text-align: center;color: #0380E3;"
                                    styleClass="bi-font-family bi-table-text tooltipster"
                                    value="#{AcessoGymPassControle.qtdClientesGymPass}">
                                <f:attribute name="acessoGymPassAlunos"
                                             value="#{AcessoGymPassControle.qtdClientesGymPass}"/>
                            </a4j:commandLink>
                            <div style="height: 30px;">
                                <h:outputText
                                        style="margin-bottom: 36px;display: block;font-family: 'Arial';font-style: normal;font-weight: 700;font-size: 20px;line-height: 23px;text-align: center;color: #828282;"
                                        styleClass="bi-font-family bi-table-text"
                                        value="Alunos Wellhub"/>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="gr-totalizador"
                                      style="width: calc(150% / 3 - 1px);margin-bottom: 48px;">
                            <a4j:commandLink
                                    oncomplete="abrirPopup('./listaBIAcessoGympass.jsp', 'AcessoAlunosBIGymPass', 780, 595);"
                                    actionListener="#{AcessoGymPassControle.selecionarAlunosAcessoGympass}"
                                    style="display: block;font-family: 'Arial';font-style: normal;font-weight: 700;font-size: 40px;line-height: 46px;text-align: center;color: #0380E3;"
                                    styleClass="bi-font-family bi-table-text tooltipster"
                                    value="#{AcessoGymPassControle.qtdAcessosTotais}">
                                <f:attribute name="acessoGymPassAlunos"
                                             value="#{AcessoGymPassControle.acessosPorCliente}"/>
                            </a4j:commandLink>
                            <div style="height: 30px;">
                                <h:outputText
                                        style="display: block;font-family: 'Arial';font-style: normal;font-weight: 700;font-size: 20px;line-height: 23px;text-align: center;color: #828282;"
                                        styleClass="bi-font-family bi-table-text"
                                        value="Acessos com Wellhub"/>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="container-row" style="padding-bottom: 20px">
                            <rich:dataTable width="100%" value="#{AcessoGymPassControle.totalizador}"
                                            id="tabelaGymPass"
                                            var="totalizador"
                                            headerClass="font-size-Em"
                                            columnClasses="col-text-align-left,col-text-align-left,col-text-align-center,col-text-align-right"
                                            styleClass="bi-totalizador-table">
                                <rich:column styleClass="bi-table-text tooltipster" style="border-top: solid 1px #E5E5E5 !important;margin-bottom: 1em !important;">

                                    <f:facet name="header">
                                            <h:outputText
                                                    styleClass="tooltipster texto-size-14 bi-font-bold bi-font-family bi-cor-cinza"
                                                    style="font-family: 'Arial';font-style: normal;font-weight: 700;font-size: 16px;line-height: 18px;color: #363636;"
                                                    value="Qtd. alunos"/>
                                    </f:facet>
                                    <h:outputText styleClass="bi-table-text texto-size-16" style=" color: #0380E3;font-family: 'Arial';font-style: normal;font-weight: 700;font-size: 16px;line-height: 18px;"
                                                  value="#{totalizador.quantidadeAcesso}">
                                        <f:attribute name="totalizador" value="#{totalizador}"/>
                                    </h:outputText>
                                </rich:column>

                                <rich:column styleClass="bi-table-text col-text-align-right tooltipster" style="border-top: solid 1px #E5E5E5 !important;margin-bottom: 1em !important;"
                                             headerClass="col-text-align-right">

                                    <f:facet name="header">
                                        <h:outputText
                                                styleClass="pull-right tooltipster texto-size-14 bi-font-bold bi-font-family bi-cor-cinza"
                                                style="font-family: 'Arial';font-style: normal;font-weight: 700;font-size: 16px;line-height: 18px;color: #363636;padding-right: 28%;"
                                                value="Qtd. de acessos"/>
                                    </f:facet>

                                    <h:outputText styleClass="texto-size-16 tooltipster" id="label"
                                                  style="padding-left: 50%;"
                                                  value="#{totalizador.label}" title="Para alterar a faixa de acesso, � necess�rio ir em Configura��es->B�sico->Acesso Academia."/>

                                </rich:column>
                            </rich:dataTable>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:panelGroup layout="block" id="infoGraficoGymPass" styleClass="bi-totalizador-header">
                        <h:panelGroup layout="block" style="margin-left: 4.5%;display: inline-block;"
                                      styleClass="col-text-align-center">
                            <h:outputText styleClass="icv-periodo-text bi-left-align tooltipster"
                                          value="Acessos / Dia"
                                          title="Quantidade de clientes Wellhub que acessam por dia."/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="container-row tooltipster" style="padding-bottom: 20px">
                        <script type="text/javascript">
                            function carregarGraficoGymPass() {
                                chartGymPass = AmCharts.makeChart("chargympassdiv", {
                                    "type": "serial",
                                    "theme": "light",
                                    "dataProvider": ${AcessoGymPassControle.dadosAcessosJSON},
                                    "valueAxes": [{
                                        "id": "acessoGymPassAxis",
                                        "position": "left",
                                        "maximum": ${AcessoGymPassControle.maiorQtdDeAcessoEmUmDia},
                                        "title": "Quantidade de acessos"
                                    }, ],
                                    "startDuration": 0,
                                    "legend": {
                                        "autoMargins": true,
                                        "maxColumns": 1,
                                        "align": "center",
                                        "equalWidths": true,
                                        "useGraphSettings": false,
                                        "valueAlign": "center",
                                        "valueWidth": 0
                                    },
                                    "numberFormatter": {
                                        "precision": 0,
                                        "thousandsSeparator": "."
                                    },
                                    "graphs": [{
                                        "balloonText": "Acessos: [[qtdAcessos]] \nDia do m�s: [[dia]]",
                                        "lineAlpha": 1,
                                        "lineThickness": 2,
                                        "bullet": "round",
                                        "dashLengthField": "dashLength",
                                        "type": "line",
                                        "lineColor": "#DB2C3D",
                                        "valueField": "qtdAcessos",
                                        "title": "Dias do m�s - ${AcessoGymPassControle.dataBase_ApresentarMesAno}",
                                        "valueAxis": "acessoGymPassAxis"
                                    }],
                                    "categoryField": "dia",
                                    "categoryAxis": {
                                        "range": 10,
                                        "gridAlpha": 0,
                                        "axisAlpha": 0,
                                        "tickLength": 0,
                                        "labelRotation": 0
                                    },
                                    "export": {
                                        "enabled": true
                                    }

                                });
                            }
                        </script>

                        <div id="chargympassdiv" style="height: 300px; margin-top: 15px"></div>
                        <script>
                            carregarGraficoGymPass();
                        </script>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
