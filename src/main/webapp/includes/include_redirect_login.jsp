<%-- 
    Document   : include_getip
    Created on : 26/05/2011, 11:18:43
    Author     : Waller
--%>

<%@page import="controle.arquitetura.SuperControle"%>
<%@page import="java.net.URL"%>
<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="imports.jsp" %>
<%
    if (SuperControle.getRedirectLoginServidorLocal()) {
        StringBuffer url = request.getRequestURL();
        URL u = new URL(url.toString());
        String urlLogin = u.getProtocol() + "://" + u.getHost() + ":" + u.getPort() + "/login";
        if (u.getPort() < 0) {
            urlLogin = u.getProtocol() + "://" + u.getHost() + "/login";
        }
        String chave = request.getParameter("key");
        if (chave != null && !chave.equals("") && !SuperControle.getEmpresasPermitidasAlterarDataBase().contains(chave)) {
            response.sendRedirect(urlLogin + "/" + (chave == null ? "" : chave));
        }
    } else if (SuperControle.getUrlLoginFront() != null && !SuperControle.getUrlLoginFront().isEmpty() &&
            !SuperControle.getUrlLoginFront().contains("@")
            && (request.getRequestURL().toString().contains("pactosolucoes") || request.getRequestURL().toString().contains("ZillyonWeb"))) {
        response.sendRedirect(SuperControle.getUrlLoginFront());
    } else if (SuperControle.getUrlLoginExterno() != null && !SuperControle.getUrlLoginExterno().isEmpty() &&
            !SuperControle.getUrlLoginExterno().contains("@")
            && (request.getRequestURL().toString().contains("pactosolucoes") || request.getRequestURL().toString().contains("ZillyonWeb"))) {
        String chave = request.getParameter("key");
        if (chave != null && !chave.equals("") && !SuperControle.getEmpresasPermitidasAlterarDataBase().contains(chave)) {
            String urlLoginExterno = SuperControle.getUrlLoginExterno();
            if(urlLoginExterno.contains("auth")){
                response.sendRedirect(urlLoginExterno);
            } else {
                response.sendRedirect(urlLoginExterno + "/" + (chave == null ? "" : chave));
            }

        }
    }
%>
