<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<html>
<head>
    <title>Comprovante de Cancelamento GetCard</title>
    <style>

        body {
            font-family: Arial, sans-serif;
        }

        .comprovante {
            border: 1px solid #ccc;
            padding: 20px;
            width: 400px;
            margin: auto;
        }

        .btn-imprimir {
            background-color: #4CAF50; /* Verde */
            border: none;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            transition-duration: 0.4s;
            border-radius: 12px; /* Borda arredondada */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Sombra */
        }

        .btn-imprimir:hover {
            background-color: white;
            color: black;
            border: 2px solid #4CAF50; /* Adiciona uma borda verde ao passar o mouse */
        }

        .center {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
<div class='comprovante'><h2 style="text-align: center">Comprovante de Cancelamento GetCard</h2>
    <pre style="margin-left: 65px">${GetCardRelatorioController.htmlComprovante}</pre>
    <div class="center">
        <button class="btn-imprimir" onclick='window.print()'>Imprimir Comprovante</button>
    </div>

</div>
</body>
</html>