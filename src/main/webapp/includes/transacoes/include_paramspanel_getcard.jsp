<%@include file="/includes/imports.jsp" %>

<a4j:outputPanel>


    <%-- PAR�METROS UTILIZADOS NO GETCARDX--%>
    <rich:modalPanel  id="panelDadosParametrosGetCard" showWhenRendered="#{GetCardRelatorioController.exibirModalParametros}" width="550"
                      autosized="true" styleClass="novaModal"
                      shadowOpacity="true">

        <h:panelGrid columns="2" width="100%" rendered="#{not empty GetCardRelatorioController.mensagemDetalhada}" title="#{GetCardRelatorioController.mensagemDetalhada}">
            <h:graphicImage value="imagens/icon-error.png" width="45"/>
            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagemDetalhada"
                              value="#{GetCardRelatorioController.mensagem}"/>
            </h:panelGrid>
        </h:panelGrid>

        <f:facet name="header">
            <h:panelGroup rendered="#{!empty GetCardRelatorioController.cancelTOSelecionado.idTransacao}">
                <h:outputText value="Id: #{GetCardRelatorioController.cancelTOSelecionado.idTransacao}" style="margin-left: 30px"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <a4j:form id="formGetCard">
                    <a4j:commandButton id="btnFecharEnvioPix" image="imagens/close.png" style="cursor:pointer"
                                       action="#{GetCardRelatorioController.fecharPanelDadosParametros}"
                                       oncomplete="#{rich:component('panelDadosParametrosGetCard')}.hide();"/>
                </a4j:form>
            </h:panelGroup>
        </f:facet>

        <h:panelGroup>
            <c:if test="${empty GetCardRelatorioController.listaParametrosSelecionado}">
                <h:panelGrid columns="2">
                    <h:graphicImage value="images/warning.png"/>
                    <h:outputText styleClass="mensagemDetalhada" value="N�o h� par�metros para esta transa��o."/>
                </h:panelGrid>
            </c:if>
            <h:panelGroup layout="block" style="height: 355px; overflow-y: scroll; overflow-x: hidden;">
                <rich:dataTable  width="100%"  value="#{GetCardRelatorioController.listaParametrosSelecionado}" var="obj">
                    <f:facet name="header">
                        <c:if test="${!empty GetCardRelatorioController.listaParametrosSelecionado}">
                            <h:outputText value="Par�metros utilizados"/>
                        </c:if>
                    </f:facet>
                    <rich:column >
                        <f:facet name="header">
                            <h:outputText value="Atributo"/>
                        </f:facet>
                        <h:outputText value="#{obj.atributo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText style="font-weight:bold;" value="#{obj.valor}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>

        </h:panelGroup>
    </rich:modalPanel>
</a4j:outputPanel>
