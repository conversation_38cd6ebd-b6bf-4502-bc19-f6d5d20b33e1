
function imprimirDireto() {
    if (navigator.appName == "Netscape") {
        window.print()
        alert('2')
    } else {
        var WebBrowser = '<OBJECT ID="WebBrowser1" WIDTH=0 HEIGHT=0 ' + 
            'CLASSID="CLSID:8856F961-340A-11D0-A96B-00C04FD705A2"></OBJECT>'; 
							 
        document.body.insertAdjacentHTML('beforeEnd', WebBrowser);
        WebBrowser1.ExecWB(6,11);
    }
}


function setFocus(form, campo) {
    form[campo].focus();
}

function imprimirRelatorio(form) {
    if (window.document.getElementById("form:relatorio").value == "sim") {   
        var hiddenCommandLink = window.document.getElementById("form:imprimirRelatorio");
        if (hiddenCommandLink) {
        
            //hiddenCommandLink.fireEvent("onclick");
            hiddenCommandLink.onclick();
        } 
    } else {         
        form.submit();
    }
}

function selecionarRichCalendarColaborador_dataNasc(form) {
    var hiddenCommandLink = window.document.getElementById("form:dataNascCommandLink");
    if (hiddenCommandLink) {
        //hiddenCommandLink.fireEvent("onclick");
        hiddenCommandLink.onclick();
    }
}

function selecionarRichCalendarCliente_dataNasc(form) {
    var hiddenCommandLink = window.document.getElementById("form:dataNascCommandLink");
    if (hiddenCommandLink) {
        //hiddenCommandLink.fireEvent("onclick");
        hiddenCommandLink.onclick();
    }
}

function testarCodigofecharJanela(form) {
    var codigo = form["form:codigo"].value;
    if (codigo != 0) {
        window.close();
    } 
}

function abrirPopup(URL, nomeJanela, comprimento, altura) {
    var posTopo = 0;//((screen.height / 2) - (altura / 2));
    var posEsquerda = 0;//((screen.width / 2) -(comprimento / 2));
    var comprimento = screen.width;
    var altura = screen.height;
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ' dependent=yes, menubar=no, toolbar=no, resizable=yes , scrollbars=yes' ;
    var parameterCaracter = URL.include("?") ? "&" : "?";
    var urlPopup = URL+parameterCaracter+"from=popup";
    window.open(urlPopup,nomeJanela,atributos);
    return false;
}

function abrirPopupDownload(URL, nomeJanela, comprimento, altura) {
    var posTopo = 0;//((screen.height / 2) - (altura / 2));
    var posEsquerda = 0;//((screen.width / 2) -(comprimento / 2));
    var comprimento = screen.width;
    var altura = screen.height;
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ' menubar=no, toolbar=no, resizable=yes , scrollbars=yes' ;
    var parameterCaracter = URL.include("?") ? "&" : "?";
    var urlPopup = URL+parameterCaracter+"from=popup";
    newwin = window.open(urlPopup, nomeJanela, atributos);
    return false;
}

function fecharJanela() {
    window.close();
}

function Tecla(e) {
    TeclasAtalho(e);
    if (document.all) // Internet Explorer
        var tecla = event.keyCode;
    else if(document.layers) // Nestcape
        var tecla = e.which;
    if ((tecla > 47 && tecla < 58) || (tecla == 44)|| (tecla == 46)) // numeros de 0 a 9 ponto (.) e virgula(,)
        return true;
    else {
        if (tecla != 8) { // backspace
            event.keyCode = 0;
        } else {
            return true;
        }    
    }
}				
    
function TeclasAtalho(e) {
    if (e.shiftKey) {
        if (e.keyCode == 43) {
            event.keyCode = 0;
            document.forms[0].incluir.click();
        } else if (e.keyCode == 45) {
            event.keyCode = 0;
            document.forms[0].excluir.click();
        } else if (e.keyCode == 46) {
            event.keyCode = 0;
            document.forms[0].gravar.click();
        } else if (e.keyCode == 42) {
            event.keyCode = 0;
            document.forms[0].consultar.click();
        }
    }
}

function getSelText() {
    var txt = '';
    if (window.getSelection) {
        txt = window.getSelection();
    } else if (document.getSelection) {
        txt = document.getSelection();
    }  else if (document.selection) {
        txt = document.selection.createRange().text;
    }  else {
        return;
    }
    return txt;
}

function mascaraMatricula(objForm, strField, evtKeyPress) {
    var nTecla;
    var textoSel = getSelText();
    if (textoSel != '') {
        return true;
    }    
    TeclasAtalho(evtKeyPress);
    if(document.all) { // Internet Explorer
        nTecla = evtKeyPress.keyCode;       
    } else if(document.layers) { // Nestcape
        nTecla = evtKeyPress.which;
    } else {
        nTecla = evtKeyPress.which;
        if (nTecla == 120) {
            return true;
        }
    }
    if ((nTecla == 8)){
        return true;
    }
    if ((nTecla == 9)){
        return true;
    }
    if ((nTecla != 120)){        
        return false;                    
    }
}
function mascara(objForm, strField, sMask, evtKeyPress) {
    //alert(objForm[strField].value);
    var i, nCount, sValue, fldLen, mskLen,bolMask, sCod;
    var textoSel = getSelText();
    if (textoSel != '') {
        return true;
    }
    //aqui obtem o nr da tecla digitada
    var nTecla = (evtKeyPress.charCode || evtKeyPress.keyCode || evtKeyPress.which);

    if (evtKeyPress.keyCode != 0 && ((nTecla == 8) || (nTecla == 9))){
        return true;
    }

    TeclasAtalho(evtKeyPress);

    sValue = objForm[strField].value;

    // Limpa todos os caracteres de formata��o que
    // j� estiverem no campo.
    sValue = sValue.toString().replace( "-", "" );
    sValue = sValue.toString().replace( "-", "" );
    sValue = sValue.toString().replace( ".", "" );
    sValue = sValue.toString().replace( ".", "" );
    sValue = sValue.toString().replace( "/", "" );
    sValue = sValue.toString().replace( "/", "" );
    sValue = sValue.toString().replace( "(", "" );
    sValue = sValue.toString().replace( "(", "" );
    sValue = sValue.toString().replace( ")", "" );
    sValue = sValue.toString().replace( ")", "" );
    sValue = sValue.toString().replace( " ", "" );
    sValue = sValue.toString().replace( " ", "" );
    fldLen = sValue.length;
    mskLen = sMask.length;


    i = 0;
    nCount = 0;
    sCod = "";
    mskLen = fldLen;

    while (i <= mskLen) {
        bolMask = ((sMask.charAt(i) == "-") || (sMask.charAt(i) == ":") || (sMask.charAt(i) == ".") || (sMask.charAt(i) == "/"));
        bolMask = bolMask || ((sMask.charAt(i) == "(") || (sMask.charAt(i) == ")") || (sMask.charAt(i) == " "));

        if (bolMask) {
            sCod += sMask.charAt(i);
            mskLen++;
        } else {
            sCod += sValue.charAt(nCount);
            nCount++;
        }

        i++;
    }
    if (sMask.length == sCod.length) {
        //event.keyCode = 0;
        //Joao Alcides : esta modificacao impedira que a funcao permita que o usuario digite mais
        //numeros do que o definido na mascara.
        return false;
    }

    objForm[strField].value = sCod;
    if (nTecla != 8) { // backspace
        if (sMask.charAt(i-1) == "9") { // apenas n�meros...
            return ((nTecla > 47) && (nTecla < 58));
        } // n�meros de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    } else {
        return true;
    }
}
//onkeypress="return mascara(this, '99999-999', event);">
function mascara(objForm, strField, sMask, evtKeyPress) {
    //alert(objForm[strField].value);
    var i, nCount, sValue, fldLen, mskLen,bolMask, sCod;
    var textoSel = getSelText();
    if (textoSel != '') {
        return true;
    }
    //aqui obtem o nr da tecla digitada
    var nTecla = (evtKeyPress.charCode || evtKeyPress.keyCode || evtKeyPress.which);

    if (evtKeyPress.keyCode != 0 && ((nTecla == 8) || (nTecla == 9))){
        return true;
    }

    TeclasAtalho(evtKeyPress);

    sValue = objForm[strField].value;

    // Limpa todos os caracteres de formata��o que
    // j� estiverem no campo.
    sValue = sValue.toString().replace( "-", "" );
    sValue = sValue.toString().replace( "-", "" );
    sValue = sValue.toString().replace( ".", "" );
    sValue = sValue.toString().replace( ".", "" );
    sValue = sValue.toString().replace( "/", "" );
    sValue = sValue.toString().replace( "/", "" );
    sValue = sValue.toString().replace( "(", "" );
    sValue = sValue.toString().replace( "(", "" );
    sValue = sValue.toString().replace( ")", "" );
    sValue = sValue.toString().replace( ")", "" );
    sValue = sValue.toString().replace( " ", "" );
    sValue = sValue.toString().replace( " ", "" );
    fldLen = sValue.length;
    mskLen = sMask.length;


    i = 0;
    nCount = 0;
    sCod = "";
    mskLen = fldLen;

    while (i <= mskLen) {
        bolMask = ((sMask.charAt(i) == "-") || (sMask.charAt(i) == ":") || (sMask.charAt(i) == ".") || (sMask.charAt(i) == "/"));
        bolMask = bolMask || ((sMask.charAt(i) == "(") || (sMask.charAt(i) == ")") || (sMask.charAt(i) == " "));

        if (bolMask) {
            sCod += sMask.charAt(i);
            mskLen++;
        } else {
            sCod += sValue.charAt(nCount);
            nCount++;
        }

        i++;
    }
    if (sMask.length == sCod.length) {
        //event.keyCode = 0;
        //Joao Alcides : esta modificacao impedira que a funcao permita que o usuario digite mais
        //numeros do que o definido na mascara.
        return false;
    }

    objForm[strField].value = sCod;
    if (nTecla != 8) { // backspace
        if (sMask.charAt(i-1) == "9") { // apenas n�meros...
            return ((nTecla > 47) && (nTecla < 58));
        } // n�meros de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    } else {
        return true;
    }
}
function permiteSomenteLetra(objForm, strField, evtKeyPress) {
    var nTecla;

    if(document.all) { // Internet Explorer
        nTecla = evtKeyPress.keyCode;
    } else if(document.layers) { // Nestcape
        nTecla = evtKeyPress.which;

    } else {
        nTecla = evtKeyPress.which;
        if (nTecla == 8) {
            return true;
        }
    }
    if (nTecla != 8) { // backspace
        if ((nTecla > 47) && (nTecla < 58)) { // apenas n�meros...
            return false;
        } // n�meros de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    } else {
        return true;
    }
}

function mascaraTodos(objForm, strField, sMask, evtKeyPress) {
    var i, nCount, sValue, fldLen, mskLen,bolMask, sCod, nTecla;
    nTecla = (evtKeyPress.which) ? evtKeyPress.which : evtKeyPress.keyCode;
    sValue = objForm[strField].value;
    // Limpa todos os caracteres de formata��o que
    // j� estiverem no campo.
    expressao = /[\.\/\-\(\)\,\;\: ]/gi;
    sValue = sValue.toString().replace(expressao, '');
    fldLen = sValue.length;
    mskLen = sMask.length;

    i = 0;
    nCount = 0;
    sCod = "";
    mskLen = fldLen;

    while (i <= mskLen) {
        bolMask = ((sMask.charAt(i) == "-") || (sMask.charAt(i) == ".") || (sMask.charAt(i) == "/") || (sMask.charAt(i) == ",") || (sMask.charAt(i) == ";") || (sMask.charAt(i) == ":"))
        bolMask = bolMask || ((sMask.charAt(i) == "(") || (sMask.charAt(i) == ")") || (sMask.charAt(i) == " "))

        if (bolMask) {
            sCod += sMask.charAt(i);
            mskLen++; }
        else {
            sCod += sValue.charAt(nCount);
            nCount++;
        }

        i++;
    }

    objForm[strField].value = sCod;

    if (nTecla != 8 && nTecla != 13)
    { // backspace enter
        if (sMask.charAt(i-1) == "9") 
        { // apenas n�meros...
            return ((nTecla > 47) && (nTecla < 58)); 
        } // n�meros de 0 a 9
        else 
        { 
            if (sMask.charAt(i-1) == "x") 
            { // apenas letras... Sem espaco
                return ((nTecla > 64) && (nTecla < 123)); 
            } // maiusculas e minusculas de A a z sem acentos
            else 
            { // qualquer caracter...
                return true;
            } 
        } 
    }
    else 
    {
        return true;
    }
}


function flash(file, width, height){
    document.write("<object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' codebase='http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0' width='" + width + "' height='" + height + "'>");
    document.write("<param name='movie' value='" + file + "'>");
    document.write("<param name='quality' value='high'>");
    document.write("<embed src='" + file + "' quality='high' pluginspage='http://www.macromedia.com/go/getflashplayer' type='application/x-shockwave-flash' width='" + width + "' height='" + height + "'></embed>");
    document.write("</object>");
}

function renderData(Ncampo){
    if ((navigator.userAgent.indexOf("Firefox")!=-1) ||
        (navigator.userAgent.indexOf("Chrome")!=-1)) {
        document.getElementById(Ncampo).style.color="#5A480C";
        document.getElementById(Ncampo).style.backgroundColor="#FEFFDF";
    }
    return true;
}

function validar_Data(Ncampo){
    b = document.getElementById(Ncampo).value;  
    if (b != "" && b != "__/__/____") {
        if (b.length == 10){
            var dia = b.substring(0,2);
            var mes = b.substring(3,5);
            var ano = b.substring(6,10);          

            if ((ano < 1900) || (ano > 2099)) {
                alert("O ano especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if ((mes <= 0) || (mes > 12)) {
                alert("O m�s especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if (dia <= 0) {
                alert("Dia especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if ((mes==1 || mes==3 || mes==5 || mes==7 || mes==8 || mes==10 || mes==12) && (dia > 31)) {
                alert("Data incorreta! O m�s especificado cont�m no m�ximo 31 dias");
                document.getElementById(Ncampo).focus();
                return false;
            } else if ((mes==4 || mes==6 || mes==9 || mes==11) && (dia > 30)) {
                alert("Data incorreta! O m�s especificado cont�m no m�ximo 30 dias");
                document.getElementById(Ncampo).focus();
                return false;
            } else {
                if ((ano%4!=0) && (mes==2) && (dia>28)) {
                    alert("Data incorreta! O m�s especificado cont�m no m�ximo 28 dias.");
                    document.getElementById(Ncampo).focus();
                    return false;
                } else{
                    if ((ano%4==0) && (mes==2) && (dia>29)) {
                        alert("Data incorreta! O m�s especificado cont�m no m�ximo 29 dias.");
                        document.getElementById(Ncampo).focus();
                        return false;
                    } else{
                        return true;
                    }
                }
            }
        } else {
            alert("Formato ou data inv�lida " + b + " (Exemplo de data: dd/mm/aaaa).");
            document.getElementById(Ncampo).focus();
            return false;
        }
    }else {
        alert("Formato ou data inv�lida " + b + " (Exemplo de data: dd/mm/aaaa).");
        document.getElementById(Ncampo).focus();
        return false;
    }
}

function validar_Data_Nula(Ncampo){
    b = document.getElementById(Ncampo).value;
    if(b == "") {
        return true;
    }
    if (b != "__/__/____") {
        if (b.length == 10){
            var dia = b.substring(0,2);
            var mes = b.substring(3,5);
            var ano = b.substring(6,10);
            if ((ano < 1900) || (ano > 2099)) {
                alert("O ano especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if ((mes <= 0) || (mes > 12)) {
                alert("O m�s especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if (dia <= 0) {
                alert("Dia especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if ((mes==1 || mes==3 || mes==5 || mes==7 || mes==8 || mes==10 || mes==12) && (dia > 31)) {
                alert("Data incorreta! O m�s especificado cont�m no m�ximo 31 dias");
                document.getElementById(Ncampo).focus();
                return false;
            } else if ((mes==4 || mes==6 || mes==9 || mes==11) && (dia > 30)) {
                alert("Data incorreta! O m�s especificado cont�m no m�ximo 30 dias");
                document.getElementById(Ncampo).focus();
                return false;
            } else {
                if ((ano%4!=0) && (mes==2) && (dia>28)) {
                    alert("Data incorreta! O m�s especificado cont�m no m�ximo 28 dias.");
                    document.getElementById(Ncampo).focus();
                    return false;
                } else{
                    if ((ano%4==0) && (mes==2) && (dia>29)) {
                        alert("Data incorreta! O m�s especificado cont�m no m�ximo 29 dias.");
                        document.getElementById(Ncampo).focus();
                        return false;
                    } else{
                        return true;
                    }
                }
            }
        } else {
            alert("Formato ou data inv�lida " + b + " (Exemplo de data: dd/mm/aaaa).");
            document.getElementById(Ncampo).focus();
            return false;
        }
    } else {
        alert("Formato ou data inv�lida " + b + " (Exemplo de data: dd/mm/aaaa).");
        document.getElementById(Ncampo).focus();
        return false;
    }
}

function focoTabNavigator(NCampo){
    document.getElementById("form:questionarioCliente:" + numeroLinha + ":data").focus();
}


function validar_Telefone(Ncampo){
    b = document.getElementById(Ncampo).value;
    if (b != "") {
        if (b.length < 10) {
            document.getElementById(Ncampo).focus();
            alert("Formato de telefone inv�lido!");
            return false;
        }
    }
}

function mascaraDentroDataTable(form, campo) {
    // form:escolhaFormaPagamento:4:MovPagamentoCheque:
    // 012345678901234567890123456789012345678901234567
    var nomePadrao = 'form:escolhaFormaPagamento:4:MovPagamentoCheque:';
    var nrLinha = campo.substring(48);
    var posFim = nrLinha.indexOf(':',0);
    nrLinha = nrLinha.substring(0, posFim);
    
    var dataCompensacao_nomeCampo = nomePadrao + nrLinha + ':dataCompensacao';
    
    //alert(form[salario_nomeCampo].value);
    //alert(form[aliquotaServidor_nomeCampo].value);
    var contribuicaoServidor = 0.0;
    var percCalcServidor = parseFloat(form[aliquotaServidor_nomeCampo].value);
    var salario = parseFloat(form[salario_nomeCampo].value);
    //alert(percCalcServidor);
    //alert(form[salario_nomeCampo].value);
    if (percCalcServidor != 0) {
        contribuicaoServidor = (salario * (percCalcServidor / 100));
    }
    form[contribuicaoServidor_nomeCampo].value = contribuicaoServidor.toLocaleString(2);
    var contribuicaoPatronal = 0.0;
    var percCalcPatronal = parseFloat(form[aliquotaPatronal_nomeCampo].value);
    //alert(percCalcPatronal);
    //alert(form[salario_nomeCampo].value);
    if (percCalcPatronal != 0) {
        contribuicaoPatronal = (salario * (percCalcPatronal / 100));
    }
    form[contribuicaoPatronal_nomeCampo].value = contribuicaoPatronal.toLocaleString(2);
    var totalFinal = contribuicaoServidor + contribuicaoPatronal;
    form[totalContribuicao_nomeCampo].value = totalFinal.toLocaleString(2);
}

function validarEnter(event){   
    if(event == null){      
        return true;        
    }
    if(event.keyCode == 13){       
        fireElement('form:botaInisivel');
        return true;
    }else{       
        return true;
    }
    
}
function validarEnterTela3(event){
    if(event == null){
        return true;
    }
    if(event.keyCode == 13){      
        fireElement('form:botaInisivelTela3');      
        return true;
    }else{
        return true;
    }

}
function validarEnterTela71(event){
    if(event == null){
        return true;
    }
    if(event.keyCode == 13){
        fireElement('form:botaInisivelTela71');
        return true;
    }else{
        return true;
    }
}
function validarEnterTela72(event){
    if(event == null){
        return true;
    }
    if(event.keyCode == 13){
        fireElement('form:botaInisivelTela72');
        return true;
    }else{
        return true;
    }
}
function validarEnterSenha(e, idBotao){
    if ((window.event ? e.keyCode : e.which) == 13) {
        document.getElementById( idBotao ).click();
        return false;
    } else return true;
}

function fireElement(id) {    
    var target = document.getElementById(id);   
    if (document.dispatchEvent) { // W3C      
        var oEvent = document.createEvent( "MouseEvents" );
        oEvent.initMouseEvent("click", true, true,window, 1, 1, 1, 1, 1, false, false, false, false, 0, target);
        target.dispatchEvent( oEvent );
    } else {
        if(document.fireEvent) { // IE               
            target.fireEvent("onclick");          
        }
    }
}
function Tecla2(objForm, strField, e) {
    //    TeclasAtalho(e);
    if(document.all) { // Internet Explorer
        var tecla = e.keyCode;
    } else if(document.layers) { // Nestcape
        var tecla = e.which;
    } else {
        var tecla = e.which;
    }
    if (tecla > 47 && tecla < 58) // numeros de 0 a 9
        return true;
    else {
        if (tecla == 0) { // backspace
            return true;
        } else if (tecla != 8) {
            return false;
        } else {
            return true;
        }
    }
}
function FormataValor(campo,tammax,teclapres) {

    var tecla = teclapres.keyCode;
    var vr = campo.value;    
    vr = vr.replace( ",", "" );
    vr = vr.replace( ".", "" );    
    tam = vr.length;

    if (tam < tammax && tecla != 8){
        tam = vr.length + 1 ;
    }

    if (tecla == 8 ){
        tam = tam - 1 ;
    }

    if ( tecla == 8 || (tecla >= 48 && tecla <= 57) || (tecla >= 96 && tecla <= 105) ){
        if ( tam <= 2 ){
            campo.value = vr ;
        }
        tam = tam - 1;
        if ( (tam > 2) && (tam <= 5) ){
            campo.value = vr.substr( 0, tam - 2 ) + '.' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 6) && (tam <= 8) ){
            campo.value = vr.substr( 0, tam - 5 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 9) && (tam <= 11) ){
            campo.value = vr.substr( 0, tam - 8 ) + '.' + vr.substr( tam - 8, 3 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 12) && (tam <= 14) ){
            campo.value = vr.substr( 0, tam - 11 ) + '.' + vr.substr( tam - 11, 3 ) + '.' + vr.substr( tam - 8, 3 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 15) && (tam <= 17) ){
            campo.value = vr.substr( 0, tam - 14 ) + '.' + vr.substr( tam - 14, 3 ) + '.' + vr.substr( tam - 11, 3 ) + '.' + vr.substr( tam - 8, 3 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
    }
}


function MascaraMoeda(e){
    var objTextBox = e.element();
    var SeparadorMilesimo = '.';
    var SeparadorDecimal = ',';
    var sep = 0;
    var key = '';
    var i = j = 0;
    var len = len2 = 0;
    var strCheck = '0123456789';
    var aux = aux2 = '';
    var whichCode = (window.Event) ? e.which : e.keyCode;
	
    objTextBox=objTextBox.replace(/\D/g,"");
    if (whichCode == 13) return true;
    key = String.fromCharCode(whichCode); // Valor para o c�digo da Chave
    if (strCheck.indexOf(key) == -1) return false; // Chave inv�lida
    len = objTextBox.value.length;
    for(i = 0; i < len; i++)
	if ((objTextBox.value.charAt(i) != '0') && (objTextBox.value.charAt(i) != SeparadorDecimal)) break;
    aux = '';
    for(; i < len; i++)
	if (strCheck.indexOf(objTextBox.value.charAt(i))!=-1) aux += objTextBox.value.charAt(i);
    aux += key;
    len = aux.length;
    if (len == 0) objTextBox.value = '';
    if (len == 1) objTextBox.value = '0'+ SeparadorDecimal + '0' + aux;
    if (len == 2) objTextBox.value = '0'+ SeparadorDecimal + aux;
    if (len > 2) {
	aux2 = '';
	for (j = 0, i = len - 3; i >= 0; i--) {
            if (j == 3) {
                aux2 += SeparadorMilesimo;
                j = 0;
            }
            aux2 += aux.charAt(i);
            j++;
	}
	objTextBox.value = '';
	len2 = aux2.length;
	for (i = len2 - 1; i >= 0; i--)
            objTextBox.value += aux2.charAt(i);
	objTextBox.value += SeparadorDecimal + aux.substr(len - 2, len);
    }
    return false;
} 
	
function formatar_moeda(campo, separador_milhar, separador_decimal, tecla) {
    var sep = 0;
    var key = '';
    var i = j = 0;
    var len = len2 = 0;
    var strCheck = '0123456789';
    var aux = aux2 = '';
    var whichCode = (window.Event) ?  tecla.which : tecla.keyCode;
	
	
    if(navigator.appName.indexOf("Netscape") == -1){
        whichCode= event.keyCode;
    }
	
    if (whichCode == 13) return true; // Tecla Enter
    if (whichCode == 8) return true; // Tecla Delete
    if(whichCode == 0) return true; //Tecla TAB
	
    key = String.fromCharCode(whichCode); // Pegando o valor digitado
    if (strCheck.indexOf(key) == -1) return false; // Valor inv�lido (n�o inteiro)
	
    len = campo.value.length;
    for(i = 0; i < len; i++)
        if ((campo.value.charAt(i) != '0') && (campo.value.charAt(i) != separador_decimal)) break;
    aux = '';
	
    for(; i < len; i++)
        if (strCheck.indexOf(campo.value.charAt(i))!=-1) aux += campo.value.charAt(i);
	
    aux += key;
    len = aux.length;
    if (len == 0) campo.value = '';
    if (len == 1) campo.value = '0'+ separador_decimal + '0' + aux;
    if (len == 2) campo.value = '0'+ separador_decimal + aux;

    if (len > 2) {
        aux2 = '';

        for (j = 0, i = len - 3; i >= 0; i--) {
            if (j == 3) {
                aux2 += separador_milhar;
                j = 0;
            }
            aux2 += aux.charAt(i);
            j++;
        }

        campo.value = '';
        len2 = aux2.length;
        for (i = len2 - 1; i >= 0; i--)
            campo.value += aux2.charAt(i);
        campo.value += separador_decimal + aux. substr(len - 2, len);
    }

    return false;
}
	
function popup(URL, nomeJanela, comprimento, altura) {
    var posTopo = ((screen.height / 2) - (altura / 2));
    var posEsquerda = ((screen.width / 2) -(comprimento / 2));
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ', dependent=yes, menubar=no, toolbar=no, resizable=yes ,, scrollbars=yes' ;
    newwin = window.open(URL, nomeJanela, atributos);
    if(newwin.focus()){
        newwin.close();
        newwin = window.open(URL, nomeJanela, atributos);
    }
    newwin.focus();
    newwin.submit();
    return true;
}

function focusinput(elemento) {
    elemento.style.border = "1px solid #8eb3c3";
    elemento.style.backgroundColor = "#fff2c2";
}
function blurinput(elemento) {
    elemento.style.border = "1px solid #8eb3c3";
    elemento.style.backgroundColor = "#fff";
}


function autoTab(input)
{
    alert();
    var tecla = event.keyCode;
    var ind = 0;
    var isNN = (navigator.appName.indexOf("Netscape")!=-1);
    var keyCode = (isNN) ? tecla.which : tecla.keyCode;
    var nKeyCode = tecla.keyCode;
    
    if(keyCode == 13){
        if (!isNN) {window.event.keyCode = 0;} // evitar o beep
        ind = getIndex(input);
        if (input.form[ind].type == 'textarea') {
            return;
        }
        ind++;
    
        input.form[ind].focus();
        if (input.form[ind].type == 'text') {
            input.form[ind].select();
        }
    }
}   

function getIndex(input)
{
    var index = -1, i = 0, found = false;
    while (i < input.form.length && index == -1)
        if (input.form[i] == input) {
            index = i;
            if (i < (input.form.length -1)) {
                if (input.form[i+1].type == 'hidden') {
                    index++;
                }
                if (input.form[i+1].type == 'button' && input.form[i+1].id == 'tabstopfalse') {
                    index++;
                }
        }
    }
    else
        i++;
    return index;
}
function currencyFormat(fld, milSep, decSep, e) {
  var sep = 0;
  var key = '';
  var i = j = 0;
  var len = len2 = 0;
  var strCheck = '0123456789';
  var aux = aux2 = '';
  
  var whichCode = (e.charCode || e.keyCode || e.which);
  
  // Teclas de navega��o
  if(e.keyCode != 0
  		&& ((whichCode == 9) || (whichCode == 18) || (whichCode == 27) || (whichCode == 33) || (whichCode == 34) || (whichCode == 35) || (whichCode == 36)
			|| (whichCode == 37) || (whichCode == 38) || (whichCode == 39) || (whichCode == 40) || (whichCode == 45))) {
	  return true;
  }
  
  var del = (e.keyCode != 0 && ((whichCode == 8) || (whichCode == 46)));
  
  len = fld.value.length;
  if (len >= fld.maxLength && !del) {
  	return false;
  }
  
  key = String.fromCharCode(whichCode);  // Get key value from key code
  
  if (!del && strCheck.indexOf(key) == -1)
	  return false;  // Not a valid key
  
  for(i = 0; i < len; i++)
	  if ((fld.value.charAt(i) != '0') && (fld.value.charAt(i) != decSep))
		  break;
  
  aux = '';
  
  for(; i < len; i++)
	  if (strCheck.indexOf(fld.value.charAt(i))!=-1)
		  aux += fld.value.charAt(i);
  
  if (!del) {
	  aux += key;
  }
  len = aux.length;
  
  if (len == 0) fld.value = '';
  if (len == 1) fld.value = '0'+ decSep + '0' + aux;
  if (len == 2) fld.value = '0'+ decSep + aux;
  
  var nrDec = whichCode == 8 ? 3 : 2;
  
  if (len > 2) {
    aux2 = '';
    for (j = 0, i = len - (nrDec+1); i >= 0; i--) {
      if (j == 3) {
        aux2 += milSep;
        j = 0;
      }
      aux2 += aux.charAt(i);
      j++;
    }
    fld.value = '';
    len2 = aux2.length;
    for (i = len2 - 1; i >= 0; i--)
    fld.value += aux2.charAt(i);
    fld.value += decSep + aux.substr(len - nrDec, len);
  }
  return del;
}

function validarValorPercentual(campo) {
	var perc = /^100(\,(0){1,2})?$|^([1-9]?[0-9])(\,(\d{0,2}))?$/;
	if (campo.value.search(perc) < 0 || campo.value.length > 6) {
		campo.value = "";
		alert("Valor percentual inv�lido!");
	}
}

function preencherHiddenChamarBotao(idBotao, idHidden, valorHidden){
	var hidden = document.getElementById(idHidden);
	var botao = document.getElementById(idBotao);
	hidden.value = valorHidden;
	botao.click();
}

function limparMensagem(id) {
	if (typeof id == "string") {
		var divObgMensagem = document.getElementById('divObg-' + id);
		if (navigator.appName.indexOf('Microsoft') != -1){
			divObgMensagem.style.visibility="hidden";
			divObgMensagem.style.display="none";
		} else {
			divObgMensagem.innerHTML = "";
		}
	} else {
		for (var i = 0; i < id.length; i++) {
			var divObgMensagem = document.getElementById('divObg-' + id[i]);
			if (navigator.appName.indexOf('Microsoft') != -1){
				divObgMensagem.style.visibility="hidden";
				divObgMensagem.style.display="none"; 
			} else {
				divObgMensagem.innerHTML = "";
			}
		}
	}
}

function exibirMensagem(mensagem, id) {
	if (typeof id == "string") {
		var divObgMensagem = document.getElementById('divObg-' + id);
		if (navigator.appName.indexOf('Microsoft') != -1){
			divObgMensagem.style.visibility="visible";
			divObgMensagem.style.display="block"; 
			divObgMensagem.innerHTML = mensagem;
		} else {
			divObgMensagem.innerHTML = mensagem;
		}
	} else {
		for (var i = 0; i < id.length; i++) {
			var divObgMensagem = document.getElementById('divObg-' + id[i]);
			if (navigator.appName.indexOf('Microsoft') != -1){
				divObgMensagem.style.visibility="visible";
				divObgMensagem.style.display="block";
				divObgMensagem.innerHTML = mensagem;
			} else {
				divObgMensagem.innerHTML = mensagem;
			}
		}
	}
}

function montarMsgObrigatoriedade(nomeCampo) {
	return "O campo '" + nomeCampo + "' deve ser informado.";
}

function montarMsgObrigatoriedadeSelecao(nomeCampo) {
	return "Algum item de '" + nomeCampo + "' deve ser selecionado.";
}

function limparMsgObrig(idCampo, idDivMsg) {
	var campo = document.getElementById(idCampo);
	if (campo != null && campo.value != null && campo.value != "") {
		limparMensagem(idDivMsg);
	}
}
function openWindow(URL, Width, Height) {
    var parameterCaracter = URL.include("?") ? "&" : "?";
    var urlPopup = URL+parameterCaracter+"from=popup";
	if(navigator.appName.indexOf('Internet Explorer')>0){
		Attributes = 'dialogWidth:' + Width + 'px;dialogHeight:' + Height + 'px;';
		return(window.open(urlPopup, '_blank', Attributes));
		}else{
		abrirPopup(urlPopup,"",Width, Height);
		        }
	}
function iniciarFoco(form) {
		  if (!isNaN(form)) {
		   form = document.getElementsByTagName("form")[form];
		  } else if (!form) {
		   form = document.getElementsByTagName("form")[0];
		  }
		  
		  if (form) {
		   for(var i = 0; i < form.elements.length; i++){
		    var elemento = form.elements[i];
		    
		    if (this.isCampoEditavel(elemento) && elemento.name!="form:buscaTopo") {
		    	
		    	if(elemento)
		     try {
		      elemento.focus();
		     } catch (e) {}
		     break;
		    }
		   } 
		  }
		 }
	 function isCampoEditavel(elemento) {
		  return ((!elemento.disabled) && ((elemento.type == "text") || (elemento.type == "textarea") 
		    || (elemento.type == "password") || (elemento.type == "select-one") 
		    || (elemento.type == "checkbox") || (elemento.type == "radio") 
		    || (elemento.type == "submit") || (elemento.type == "buttom")));
		 }

	 function formataHora(pStr, pFmt) {
	 			var reTime2 = /^([0-1]\d|2[0-3]):[0-5]\d$/;
	 			eval("reTime = reTime" + pFmt);
	 			if (reTime.test(pStr)) {
	 				return true;
	 			} else if (pStr != null && pStr != "") {
	 				return false;
	 			}
	 		}
	//envia email para o valor do campo passado como parametro
	function email(email){
		document.location.href = "mailto:" + document.getElementById(email).value;
	}
	
	//Funcao que converte o valor digitado em um inputText que estiver em 
	//caixa baixa para caixa alta.
	//Para usar colocar no inputText a seguinte chamada:
	//onkeyup="return convertParaUpperCase(event, this);"
	function convertParaUpperCase(event, obj){
		try {
			obj.value = obj.value.toUpperCase();
		} catch (e){
		}
	}

    //Funcao que permite apenas valor numerico
    //Para usar colocar no inputText a seguinte chamada:
    //onkeyup="return isNumero(event);"
    function isNumero(event) {
        event = (event) ? event : window.event;
        var charCode = (event.which) ? event.which : event.keyCode;
        if (charCode > 31 && (charCode < 48 || charCode > 57)) {
            return false;
        }
        return true;
    }
