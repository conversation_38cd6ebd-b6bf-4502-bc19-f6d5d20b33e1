/* global Richfaces */

var BrowserDetect = {
        init: function() {
		this.browser = this.searchString(this.dataBrowser) || "An unknown browser";
		this.version = this.searchVersion(navigator.userAgent) || this.searchVersion(navigator.appVersion) || "an unknown version";
		this.OS = this.searchString(this.dataOS) || "an unknown OS";
	},
	searchString: function(data) {
		for (var i = 0; i < data.length; i++) {
			var dataString = data[i].string;
			var dataProp = data[i].prop;
			this.versionSearchString = data[i].versionSearch || data[i].identity;
			if (dataString) {
				if (dataString.indexOf(data[i].subString) != -1) return data[i].identity;
			} else if (dataProp) return data[i].identity;
		}
	},
	searchVersion: function(dataString) {
		var index = dataString.indexOf(this.versionSearchString);
		if (index == -1) return;
		return parseFloat(dataString.substring(index + this.versionSearchString.length + 1));
	},
	dataBrowser: [{
		string: navigator.userAgent,
		subString: "Chrome",
		identity: "Chrome"
	}, {
		string: navigator.userAgent,
		subString: "OmniWeb",
		versionSearch: "OmniWeb/",
		identity: "OmniWeb"
	}, {
		string: navigator.vendor,
		subString: "Apple",
		identity: "Safari",
		versionSearch: "Version"
	}, {
		prop: window.opera,
		identity: "Opera",
		versionSearch: "Version"
	}, {
		string: navigator.vendor,
		subString: "iCab",
		identity: "iCab"
	}, {
		string: navigator.vendor,
		subString: "KDE",
		identity: "Konqueror"
	}, {
		string: navigator.userAgent,
		subString: "Firefox",
		identity: "Firefox"
	}, {
		string: navigator.vendor,
		subString: "Camino",
		identity: "Camino"
	}, { // for newer Netscapes (6+)
		string: navigator.userAgent,
		subString: "Netscape",
		identity: "Netscape"
	}, {
		string: navigator.userAgent,
		subString: "MSIE",
		identity: "Explorer",
		versionSearch: "MSIE"
	}, {
		string: navigator.userAgent,
		subString: "Gecko",
		identity: "Mozilla",
		versionSearch: "rv"
	}, { // for older Netscapes (4-)
		string: navigator.userAgent,
		subString: "Mozilla",
		identity: "Netscape",
		versionSearch: "Mozilla"
	}],
	dataOS: [{
		string: navigator.platform,
		subString: "Win",
		identity: "Windows"
	}, {
		string: navigator.platform,
		subString: "Mac",
		identity: "Mac"
	}, {
		string: navigator.userAgent,
		subString: "iPhone",
		identity: "iPhone/iPod"
	}, {
		string: navigator.platform,
		subString: "Linux",
		identity: "Linux"
	}]

};
BrowserDetect.init();

///// mobile
var isMobile = {
    Android: function() {
        return navigator.userAgent.match(/Android/i);
    },
    BlackBerry: function() {
        return navigator.userAgent.match(/BlackBerry/i);
    },
    iOS: function() {
        return navigator.userAgent.match(/iPhone|iPad|iPod/i);
    },
    Opera: function() {
        return navigator.userAgent.match(/Opera Mini/i);
    },
    Windows: function() {
        return navigator.userAgent.match(/IEMobile/i);
    },
    any: function() {
        return (isMobile.Android() || isMobile.BlackBerry() || isMobile.iOS() || isMobile.Opera() || isMobile.Windows());
    }
};

var Notifier;
function  carregarNotifier() {
    Notifier = (function() {
        var MENS_ERRO = 0, MENS_INFO = 1, MENS_SUCESS = 3, MENS_WARN = 4;
        function tipoNotificacao(e) {
            if(e == MENS_ERRO){
                return 'mens-erro';
            }else if(e == MENS_INFO){
                return 'mens-info';
            }else if(e == MENS_SUCESS){
                return 'mens-sucess';
            }else if(e == MENS_WARN){
                return 'mens-warn';
            }
            return '';
        }
        function removerElemento(el) {
            el.className += ' hideNotificationMessage';
            setTimeout(function(){
                if(el != null) {
                    el.parentNode.removeChild(el);
                }
            },1000);
        }
        var fade_out = function(element) {
            if (element.style.opacity && element.style.opacity > 0.05) {
                element.style.opacity = element.style.opacity - 0.05;
            } else if (element.style.opacity && element.style.opacity <= 0.1) {
                if (element.parentNode) {
                    removerElemento(element);
                }
            } else {
                element.style.opacity = 0.9;
            }
            setTimeout(function() {
                fade_out.apply(this, [element]);
            }, 1000 / 30);
        };
        container_box = document.createElement('div');
        container_box.className = 'growl-container';
        var config = { /* How long the notification stays visible */
            default_timeout: 5000,
            /* container for the notifications */
            container: container_box
        };
        document.body.appendChild(container_box);
        function removerMesmoTipo(tipo) {
            for(var e = 0 ; e < container_box.childNodes.length ; e++){
                var node =  container_box.childNodes[e];
                if(node.className == tipoNotificacao(tipo)){
                    container_box.removeChild(node);
                }
            }
        }
        return {
            MENS_ERRO : 0,
            MENS_INFO : 1,
            MENS_SUCESS : 3,
            MENS_WARN : 4,
            notify: function(message, title,type,autoRemove) {
                if (message == null || message.length === 0) {
                    return;
                }
                var notification = document.createElement('div');
                notification.className = tipoNotificacao(type);
                removerMesmoTipo(type);
                var notification_fundo = document.createElement('div');
                notification_fundo.className = 'growl-notification-fundo';

                notification.appendChild(notification_fundo);

                notification.onclick = function() {
                    removerElemento(notification);
                };

                var container_icon = document.createElement('div');
                container_icon.className = 'growl-box-icon';

                var icon = document.createElement('span');
                container_icon.appendChild(icon);

                notification.appendChild(container_icon);

                var text = document.createElement('div');
                text.className = 'growl-box-text';
                notification.appendChild(text);

                if (title) {
                    var title_text = document.createElement('div');
                    title_text.id = "titleInfo";
                    title_text.className = "growl-box-title";
                    title_text.appendChild(document.createTextNode(title));
                    text.appendChild(title_text);
                }

                if (message) {
                    var message_text = document.createElement('div');
                    message_text.id = "messageInfo";
                    message_text.className =  "growl-box-message";
                    message_text.appendChild(document.createTextNode(message));
                    text.appendChild(message_text);
                }

                config.container.insertBefore(notification, config.container.firstChild);
                if(autoRemove) {
                    setTimeout(function () {
                        fade_out(notification);
                    }, config.default_timeout);
                }

            },
            info: function(message, title) {
                this.notify(message, title,MENS_INFO,true);
            },
            warning: function(message, title) {
                this.notify(message, title,MENS_WARN,true);
            },
            success: function(message, title) {
                this.notify(message, title,MENS_SUCESS,true);
            },
            error: function(message, title) {
                this.notify(message, title ,MENS_ERRO,false);
            },
            errorRemove: function(message, title) {
                this.notify(message, title ,MENS_ERRO,true);
            },
            custom: function(message, title,type,autoRemove) {
                this.notify(message, title ,type,autoRemove);
            },
            cleanAll : function() {
                for(var e = 0 ; e < container_box.childNodes.length ; e++){
                    var node =  container_box.childNodes[e];
                    container_box.removeChild(node);
                }
            },
            cleanAllOnType : function(tipo) {
                for(var e = 0 ; e < container_box.childNodes.length ; e++){
                    var node =  container_box.childNodes[e];
                    if(node.className == tipoNotificacao(tipo)) {
                        container_box.removeChild(node);
                    }
                }
            }
        };
    }());
}
document.addEventListener('DOMContentLoaded', carregarNotifier, false);


function imprimirDireto() {
    if (navigator.appName == "Netscape") {
        window.print();
    } else {
        var WebBrowser = '<OBJECT ID="WebBrowser1" WIDTH=0 HEIGHT=0 ' +
            'CLASSID="CLSID:8856F961-340A-11D0-A96B-00C04FD705A2"></OBJECT>';

        document.body.insertAdjacentHTML('beforeEnd', WebBrowser);
        WebBrowser1.ExecWB(6,11);
    }
}
function carregarMaskInput(){
    jQuery('.rich-calendar-input').unmask();
    jQuery('.rich-calendar-input').mask('99/99/9999');
}
function validarEnterSenha(e, idBotao){
    if ((window.event ? e.keyCode : e.which) == 13) {
        document.getElementById( idBotao ).click();
        return false;
    } else return true;
}

function setFocus(form, campo) {
    form[campo].focus();
}



function imprimirRelatorio(form) {
    if (window.document.getElementById("form:relatorio").value == "sim") {
        var hiddenCommandLink = window.document.getElementById("form:imprimirRelatorio");
        if (hiddenCommandLink) {

            //hiddenCommandLink.fireEvent("onclick");
            hiddenCommandLink.onclick();
        }
    } else {
        form.submit();
    }
}
function obterBandeiraCartaoPorNumero(number){
    var Bandeiras = {};
    Bandeiras["elo"] = {
        regexpBin: /^401178|^401179|^431274|^438935|^451416|^457393|^457631|^457632|^504175|^627780|^636297|^636368|^(506699|5067[0-6]\d|50677[0-8])|^(50900\d|5090[1-9]\d|509[1-9]\d{2})|^65003[1-3]|^(65003[5-9]|65004\d|65005[0-1])|^(65040[5-9]|6504[1-3]\d)|^(65048[5-9]|65049\d|6505[0-2]\d|65053     [0-8])|^(65054[1-9]|6505[5-8]\d|65059[0-8])|^(65070\d|65071[0-8])|^65072[0-7]|^(65090[1-9]|65091\d|650920)|^(65165[2-9]|6516[6-7]\d)|^(65500\d|65501\d)|^(65502[1-9]|6550[3-4]\d|65505[0-8])/,
        regexpFull: /^(401178|401179|431274|438935|451416|457393|457631|457632|504175|627780|636297|636368|(506699|5067[0-6]\d|50677[0-8])|(50900\d|5090[1-9]\d|509[1-9]\d{2})|65003[1-3]|(65003[5-9]|65004\d|65005[0-1])|(65040[5-9]|6504[1-3]\d)|(65048[5-9]|65049\d|6505[0-2]\d|65053[0-8])|(65054[1-9]| 6505[5-8]\d|65059[0-8])|(65070\d|65071[0-8])|65072[0-7]|(65090[1-9]|65091\d|650920)|(65165[2-9]|6516[6-7]\d)|(65500\d|65501\d)|(65502[1-9]|6550[3-4]\d|65505[0-8]))[0-9]{10,12}/,
        regexpCvv: /^\d{3}$/,
    };
    Bandeiras["dinners"] = {
        regexpBin: /^3(?:0[0-5]|[68][0-9])/,
        regexpFull: /^3(?:0[0-5]|[68][0-9])[0-9]{11}$/,
        regexpCvv: /^\d{3}$/
    };
    Bandeiras["discover"] = {
        regexpBin: /^6(?:011|5[0-9]{2})/,
        regexpFull: /^6(?:011|5[0-9]{2})[0-9]{12}$/,
        regexpCvv: /^\d{3}$/
    };
    Bandeiras["hipercard"] = {
        regexpBin: /^3841[046]0|^60/,
        regexpFull: /^(38[0-9]{17}|60[0-9]{14})$/,
        regexpCvv: /^\d{3}$/
    };
    Bandeiras["amex"] = {
        regexpBin: /^3[47]/,
        regexpFull: /^3[47][0-9]{13}$/,
        regexpCvv: /^\d{3,4}$/
    };
    Bandeiras["aura"] = {
        regexpBin: /^50[0-9]/,
        regexpFull: /^50[0-9]{14,17}$/,
        regexpCvv: /^\d{3}$/
    };
    Bandeiras["mastercard"] = {
        regexpBin: /^5[1-5][0-9][0-9]/,
        regexpFull: /^5[1-5][0-9]{14}$/,
        regexpCvv: /^\d{3}$/
    };
    Bandeiras["visa"] = {
        regexpBin: /^4/,
        regexpFull: /^4[0-9]{12}(?:[0-9]{3})?$/,
        regexpCvv: /^\d{3}$/
    };
    Bandeiras["jcb"] = {
        regexpBin: /^35/,
        regexpFull: /^(?:2131|1800|35\d{3})\d{11}$/,
        regexpCvv: /^\d{3}$/
    };
    //VISA(1, "Visa", "visa", new Integer[]{4}),
    //MASTERCARD(2, "Mastercard", "mastercard", new Integer[]{5}),
    //DINERS(3, "Diners", "diners", new Integer[]{301,305,36,38}),
    //AMEX(4, "Amex", "amex", new Integer[]{34, 37}),
    //HIPERCARD(5, "Hipercard", "hipercard", new Integer[]{38,60}),
    //JCB(6, "JCB", "jcb", new Integer[]{35}),
    //SOROCRED(7, "Sorocred", "sorocred", new Integer[]{0}),
    //AURA(8, "Aura", "aura", new Integer[]{50}),
    //ELO(9, "Elo", "elo", new Integer[]{636368, 636369, 438935, 504175, 451416, 636297,5067,4576,4011,506699});
    if(Bandeiras["visa"].regexpFull.test(Number(number))){
        return "visa";
    }
    if(Bandeiras["elo"].regexpFull.test(Number(number))){
        return "elo";
    }
    if(Bandeiras["dinners"].regexpFull.test(Number(number))){
        return "dinners";
    }
    if(Bandeiras["amex"].regexpFull.test(Number(number))){
        return "amex";
    }
    if(Bandeiras["hipercard"].regexpFull.test(Number(number))){
        return "hipercard";
    }
    if(Bandeiras["jcb"].regexpFull.test(Number(number))){
        return "jcb";
    }
    if(Bandeiras["aura"].regexpFull.test(Number(number))){
        return "aura";
    }
    if(Bandeiras["mastercard"].regexpFull.test(Number(number))){
        return "mastercard";
    }
    return false;

}
function selecionarRichCalendarColaborador_dataNasc(form) {
    var hiddenCommandLink = window.document.getElementById("form:dataNascCommandLink");
    if (hiddenCommandLink) {
        //hiddenCommandLink.fireEvent("onclick");
        hiddenCommandLink.onclick();
    }
}

function selecionarRichCalendarCliente_dataNasc(form) {
    var hiddenCommandLink = window.document.getElementById("form:dataNascCommandLink");
    if (hiddenCommandLink) {
        //hiddenCommandLink.fireEvent("onclick");
        hiddenCommandLink.onclick();
    }
}

function testarCodigofecharJanela(form) {
    var codigo = form["form:codigo"].value;
    if (codigo != 0) {
        window.close();
    }
}

function verificaPopUpAbriu(popUpWindow){
    try{
        popUpWindow.focus();
    }catch (exception) {

        if(BrowserDetect.browser === 'Chrome') {
            document.getElementById("imagem_divInterno_modalPopUpBloqueado").innerHTML = '<img src="images/PopUpBloqueado_Chrome.png" style="width: 80vw;">';
        } else if (BrowserDetect.browser === 'Edge') {
            document.getElementById("imagem_divInterno_modalPopUpBloqueado").innerHTML = '<img src="images/PopUpBloqueado_Edge.png" style="width: 80vw;">';
        } else if (BrowserDetect.browser === 'Firefox') {
            document.getElementById("imagem_divInterno_modalPopUpBloqueado").innerHTML = '<img src="images/PopUpBloqueado_Firefox.png" style="width: 80vw;">';
        } else if (BrowserDetect.browser === 'Safari') {
            if(BrowserDetect.version < 10.14){
                document.getElementById("imagem_divInterno_modalPopUpBloqueado").innerHTML = '<img src="images/PopUpBloqueado_Safari.png" style="width: 80vw;">';
            } else {
                document.getElementById("imagem_divInterno_modalPopUpBloqueado").innerHTML = '<img src="images/PopUpBloqueado_Safari_1014.png" style="width: 80vw;">';
            } 
        }else{
            return;
        }
        Richfaces.showModalPanel('modalPopUpBloqueado');
    }
}

function abrirPopupMaximizada(URL, nomeJanela) {
    var comprimento = screen.width - 20;
    var altura = screen.height -100;
    var atributos = 'width=' + comprimento + ", height=" + altura + ', dependent=yes, menubar=no, toolbar=no, resizable=yes , scrollbars=yes' ;
    var parameterCaracter = URL.include("?") ? "&" : "?";
    var urlPopup = URL+parameterCaracter+"from=popup";
    var popUpWindow = window.open(urlPopup,nomeJanela,atributos);
    verificaPopUpAbriu(popUpWindow);
    return false;
}

function replaceAll(texto, valor, novoValor) {
    while (texto.indexOf(valor) > -1) {
        texto = texto.replace(valor, novoValor);
    }
    return texto;
}

function pegarUrlCompleta(URL) {
    var Urlrecebida = window.location.href;
    var novoCaminho = Urlrecebida.substring(Urlrecebida.indexOf('/faces/'), Urlrecebida.lastIndexOf('/') + 1)
    Urlrecebida = Urlrecebida.replace(novoCaminho, '/');
    var urlCompleta = replaceAll(Urlrecebida, '/faces/', '/');
    if (URL.indexOf("../") > -1) {
        URL = replaceAll(URL,"../","");
    }
    urlCompleta = urlCompleta.substring(0, urlCompleta.lastIndexOf('/') + 1) + URL;
    var link = document.createElement("a");
    link.href = urlCompleta;

    var marcadorNomeArquivo = "file=";
    var nomeArquivoIndex = URL.indexOf(marcadorNomeArquivo);
    if (nomeArquivoIndex > -1) {
        var nomeArquivo = URL.substring(nomeArquivoIndex + marcadorNomeArquivo.length);
        if (nomeArquivo.indexOf("&") > -1) {
            nomeArquivo = nomeArquivo.substring(0, nomeArquivo.indexOf("&"));
        }
        link.download = nomeArquivo;
    }
    return link;
}
function abrirPopup(URL, nomeJanela, comprimento, altura) {
    var parameterCaracter = URL.include("?") ? "&" : "?";
    var urlPopup = URL+parameterCaracter+"from=popup";
    if(URL.indexOf("UpdateServlet?op=downloadfile") > -1) {
        link = pegarUrlCompleta(urlPopup);

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        delete link;
    } else {
        var posTopo = ((screen.height / 2) - (altura / 2));
        var posEsquerda = ((screen.width / 2) - (comprimento / 2));
        var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ' dependent=yes, menubar=no, toolbar=no, resizable=yes , scrollbars=yes';

        var popUpWindow = window.open(urlPopup, nomeJanela, atributos);
        verificaPopUpAbriu(popUpWindow);
        return false;
    }
}

function popupCenter(url, title, largura, altura) {
    // Fixes dual-screen position                         Most browsers      Firefox
    var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : window.screenX;
    var dualScreenTop = window.screenTop != undefined ? window.screenTop : window.screenY;

    var width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
    var height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

    var systemZoom = width / window.screen.availWidth;
    var left = (width - largura) / 2 / systemZoom + dualScreenLeft;
    var top = (height - altura) / 2 / systemZoom + dualScreenTop;
    var newWindow = window.open(url, title, 'scrollbars=yes, width=' + largura / systemZoom + ', height=' + altura / systemZoom + ', top=' + top + ', left=' + left);

    // Puts focus on the newWindow
    if (window.focus) newWindow.focus();
}

function abrirPopupTopoPagina(URL, nomeJanela, comprimento, altura) {
    var posTopo = 5;
    var posEsquerda = ((screen.width / 2) -(comprimento / 2));
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ' dependent=yes, menubar=no, toolbar=no, resizable=yes , scrollbars=yes' ;
    var parameterCaracter = URL.include("?") ? "&" : "?";
    var urlPopup = URL+parameterCaracter+"from=popup";
    var popUpWindow = window.open(urlPopup,nomeJanela,atributos);
    verificaPopUpAbriu(popUpWindow);
    return false;
}
function abrirPopupDownload(URL, nomeJanela, comprimento, altura) {
    var posTopo = ((screen.height / 2) - (altura / 2));
    var posEsquerda = ((screen.width / 2) -(comprimento / 2));
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ' menubar=no, toolbar=no, resizable=yes , scrollbars=yes' ;
    var popUpWindow = window.open(URL,nomeJanela,atributos);
    verificaPopUpAbriu(popUpWindow);
    return false;
}

function abrirPopupPDFImpressao(caminhoRelativoPDF, nomeJanela, comprimento, altura) {
    var urlCompleta = replaceAll(window.location.href, '/faces/', '/');
    urlCompleta = urlCompleta.substring(0, urlCompleta.lastIndexOf('/') + 1) + caminhoRelativoPDF;

    var posTopo = ((screen.height / 2) - (altura / 2));
    var posEsquerda = ((screen.width / 2) -(comprimento / 2));
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ' dependent=yes, menubar=no, toolbar=no, resizable=yes , scrollbars=yes' ;
    if (caminhoRelativoPDF !== ''){

        var popUpWindow = window.open(urlCompleta,nomeJanela,atributos);
        verificaPopUpAbriu(popUpWindow);
    }
    return true;
}

function fecharJanela() {
    window.close();
}

function Tecla(e) {
    TeclasAtalho(e);
    if (document.all) // Internet Explorer
        var tecla = event.keyCode;
    else if(document.layers) // Nestcape
        var tecla = e.which;
    if ((tecla > 47 && tecla < 58) || (tecla == 44)|| (tecla == 46)) // numeros de 0 a 9 ponto (.) e virgula(,)
        return true;
    else {
        if (tecla != 8) { // backspace
            event.keyCode = 0;
        } else {
            return true;
        }
    }
}

function TeclasAtalho(e) {
    if (e.shiftKey) {
        if (e.keyCode == 43) {
            event.keyCode = 0;
            document.forms[0].incluir.click();
        } else if (e.keyCode == 45) {
            event.keyCode = 0;
            document.forms[0].excluir.click();
        } else if (e.keyCode == 46) {
            event.keyCode = 0;
            document.forms[0].gravar.click();
        } else if (e.keyCode == 42) {
            event.keyCode = 0;
            document.forms[0].consultar.click();
        }
    }
}

function getSelText() {
    var txt = '';
    if (window.getSelection) {
        txt = window.getSelection();
    } else if (document.getSelection) {
        txt = document.getSelection();
    }  else if (document.selection) {
        txt = document.selection.createRange().text;
    }  else {
        return;
    }
    return txt;
}

function mascaraMatricula(objForm, strField, evtKeyPress) {
    var nTecla;
    var textoSel = getSelText();
    if (textoSel != '') {
        return true;
    }
    TeclasAtalho(evtKeyPress);
    if(document.all) { // Internet Explorer
        nTecla = evtKeyPress.keyCode;
    } else if(document.layers) { // Nestcape
        nTecla = evtKeyPress.which;
    } else {
        nTecla = evtKeyPress.which;
        if (nTecla == 120) {
            return true;
        }
    }
    if ((nTecla == 8)){
        return true;
    }
    if ((nTecla == 9)){
        return true;
    }

    if ((nTecla != 120)){
        return false;
    }


}

//onkeypress="return mascara(document.rcfDownload, 'str_cep', '99999-999', event);">
function mascara(objForm, strField, sMask, evtKeyPress) {
    //alert(objForm[strField].value);
    var i, nCount, sValue, fldLen, mskLen,bolMask, sCod;
    var textoSel = getSelText();
    if (textoSel != '') {
        return true;
    }
    //aqui obtem o nr da tecla digitada
    var nTecla = (evtKeyPress.charCode || evtKeyPress.keyCode || evtKeyPress.which);

    if (evtKeyPress.keyCode != 0 && ((nTecla == 8) || (nTecla == 9))){
    	return true;
    }

    TeclasAtalho(evtKeyPress);
    sValue = objForm[strField].value;

    // Limpa todos os caracteres de formata��o que
    // j� estiverem no campo.
    sValue = sValue.toString().replace( "-", "" );
    sValue = sValue.toString().replace( "-", "" );
    sValue = sValue.toString().replace( ".", "" );
    sValue = sValue.toString().replace( ".", "" );
    sValue = sValue.toString().replace( "/", "" );
    sValue = sValue.toString().replace( "/", "" );
    sValue = sValue.toString().replace( "(", "" );
    sValue = sValue.toString().replace( "(", "" );
    sValue = sValue.toString().replace( ")", "" );
    sValue = sValue.toString().replace( ")", "" );
    sValue = sValue.toString().replace( "+", "" );
    sValue = sValue.toString().replace( "+", "" );
    sValue = sValue.toString().replace( " ", "" );
    sValue = sValue.toString().replace( " ", "" );
    fldLen = sValue.length;
    mskLen = sMask.length;


    i = 0;
    nCount = 0;
    sCod = "";
    mskLen = fldLen;

    while (i <= mskLen) {
        bolMask = ((sMask.charAt(i) == "-") || (sMask.charAt(i) == ":") || (sMask.charAt(i) == ".") || (sMask.charAt(i) == "/"))
        bolMask = bolMask || ((sMask.charAt(i) == "(") || (sMask.charAt(i) == ")") || (sMask.charAt(i) == " ") || (sMask.charAt(i) == "+"))

        if (bolMask) {
            sCod += sMask.charAt(i);
            mskLen++;
        } else {
            sCod += sValue.charAt(nCount);
            nCount++;
        }

        i++;
    }
    if (sMask.length == sCod.length) {
    	//event.keyCode = 0;
    	//Joao Alcides : esta modificacao impedira que a funcao permita que o usuario digite mais
    	//numeros do que o definido na mascara.
    	return false;
    }

    objForm[strField].value = sCod;
    if (nTecla != 8) { // backspace
        if (sMask.charAt(i-1) == "9") { // apenas numeros...
            return ((nTecla > 47) && (nTecla < 58));
        } // n�meros de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    } else {
        return true;
    }
}
function permiteSomenteLetra(objForm, strField, evtKeyPress) {
    var nTecla;

    if(document.all) { // Internet Explorer
        nTecla = evtKeyPress.keyCode;
    } else if(document.layers) { // Nestcape
        nTecla = evtKeyPress.which;

    } else {
        nTecla = evtKeyPress.which;
        if (nTecla == 8) {
            return true;
        }
    }
    if (nTecla != 8) { // backspace
        if ((nTecla > 47) && (nTecla < 58)) { // apenas n�meros...
            return false;
        } // n�meros de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    } else {
        return true;
    }
}

function mascaraTodos(objForm, strField, sMask, evtKeyPress) {
    var i, nCount, sValue, fldLen, mskLen,bolMask, sCod, nTecla;
    nTecla = (evtKeyPress.which) ? evtKeyPress.which : evtKeyPress.keyCode;
    sValue = objForm[strField].value;
    // Limpa todos os caracteres de formata��o que
    // j� estiverem no campo.
    expressao = /[\.\/\-\(\)\,\;\: ]/gi;
    sValue = sValue.toString().replace(expressao, '');
    fldLen = sValue.length;
    mskLen = sMask.length;

    i = 0;
    nCount = 0;
    sCod = "";
    mskLen = fldLen;

    while (i <= mskLen) {
        bolMask = ((sMask.charAt(i) == "-") || (sMask.charAt(i) == ".") || (sMask.charAt(i) == "/") || (sMask.charAt(i) == ",") || (sMask.charAt(i) == ";") || (sMask.charAt(i) == ":"))
        bolMask = bolMask || ((sMask.charAt(i) == "(") || (sMask.charAt(i) == ")") || (sMask.charAt(i) == " "))

        if (bolMask) {
            sCod += sMask.charAt(i);
            mskLen++; }
        else {
            sCod += sValue.charAt(nCount);
            nCount++;
        }

        i++;
    }

    objForm[strField].value = sCod;

    if (nTecla != 8 && nTecla != 13)
    { // backspace enter
        if (sMask.charAt(i-1) == "9")
        { // apenas n�meros...
            return ((nTecla > 47) && (nTecla < 58));
        } // n�meros de 0 a 9
        else
        {
            if (sMask.charAt(i-1) == "x")
            { // apenas letras... Sem espaco
                return ((nTecla > 64) && (nTecla < 123));
            } // maiusculas e minusculas de A a z sem acentos
            else
            { // qualquer caracter...
                return true;
            }
        }
    }
    else
    {
        return true;
    }
}


function flash(file, width, height){
    document.write("<object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' codebase='http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cabversion=7,0,19,0' width='" + width + "' height='" + height + "'>");
    document.write("<param name='movie' value='" + file + "'>");
    document.write("<param name='quality' value='high'>");
    document.write("<embed src='" + file + "' quality='high' pluginspage='http://www.macromedia.com/go/getflashplayer' type='application/x-shockwave-flash' width='" + width + "' height='" + height + "'></embed>");
    document.write("</object>");
}

function renderData(Ncampo){
    if ((navigator.userAgent.indexOf("Firefox")!=-1) ||
        (navigator.userAgent.indexOf("Chrome")!=-1)) {
        document.getElementById(Ncampo).style.color="5A480C";
        document.getElementById(Ncampo).style.backgroundColor="FEFFDF";
    }
    return true;
}

function validar_Data(Ncampo){
    b = document.getElementById(Ncampo).value;
    if (b != "" && b != "__/__/____") {
        if (b.length == 10){
            var dia = b.substring(0,2);
            var mes = b.substring(3,5);
            var ano = b.substring(6,10);
            if ((ano < 1900) || (ano > 2099)) {
                showMessageGrowOrAlert("O ano especificado n\u00e3o \u00e9 valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if ((mes <= 0) || (mes > 12)) {
                showMessageGrowOrAlert("O m\u00eas especificado n\u00e3o \u00e9 valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if (dia <= 0) {
                showMessageGrowOrAlert("Dia especificado n\u00e3o \u00e9 valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if ((mes==1 || mes==3 || mes==5 || mes==7 || mes==8 || mes==10 || mes==12) && (dia > 31)) {
                showMessageGrowOrAlert("Data incorreta! O m\u00eas especificado cont\u00e9m no m\u00e1ximo 31 dias");
                document.getElementById(Ncampo).focus();
                return false;
            } else if ((mes==4 || mes==6 || mes==9 || mes==11) && (dia > 30)) {
                showMessageGrowOrAlert("Data incorreta! O m\u00eas especificado cont\u00e9m no m\u00e1ximo 30 dias");
                document.getElementById(Ncampo).focus();
                return false;
            } else {
                if ((ano%4!=0) && (mes==2) && (dia>28)) {
                    showMessageGrowOrAlert("Data incorreta! O m\u00eas especificado cont\u00e9m no m\u00e1ximo 28 dias.");
                    document.getElementById(Ncampo).focus();
                    return false;
                } else{
                    if ((ano%4==0) && (mes==2) && (dia>29)) {
                        showMessageGrowOrAlert("Data incorreta! O m\u00eas especificado cont\u00e9m no m\u00e1ximo 29 dias.");
                        document.getElementById(Ncampo).focus();
                        return false;
                    } else{
                        return true;
                    }
                }
            }
        } else {
            showMessageGrowOrAlert("Formato ou data inv\u00e1lida " + b + " (Exemplo de data: dd/mm/aaaa).");
            document.getElementById(Ncampo).focus();
            return false;
        }
    }else {
        //showMessageGrowOrAlert("Formato ou data invalida " + b + " (Exemplo de data: dd/mm/aaaa).");
        //document.getElementById(Ncampo).focus();
        //return false;
    }
}

function showMessageGrowOrAlert(msg){
     try{
        Notifier.errorRemove("", msg)
    }catch (e){}
}

function validar_DataFatura(Ncampo){
    b = document.getElementById(Ncampo).value;
    //alert("script:"+b);
    if (b != "" && b != "__/__/____") {
        var data = b.split("/");
        var dia = data[0];
        var mes = data[1];
        var ano = data[2];
        if ((ano <= 1999) || (ano > 2090)) {
            showMessageGrowOrAlert("O ano especificado n�o � valido.");
            document.getElementById(Ncampo).value = "";
            document.getElementById(Ncampo).focus();
            return false;
        }
        if ((mes <= 0) || (mes > 12)) {
            showMessageGrowOrAlert("O m�s especificado n�o � valido.");
            document.getElementById(Ncampo).value = "";
            document.getElementById(Ncampo).focus();
            return false;
        }
        if (dia <= 0) {
            showMessageGrowOrAlert("Dia especificado n�o � valido.");
            document.getElementById(Ncampo).value = "";
            document.getElementById(Ncampo).focus();
            return false;
        }
        if ((mes==1 || mes==3 || mes==5 || mes==7 || mes==8 || mes==10 || mes==12) && (dia > 31)) {
            showMessageGrowOrAlert("Data incorreta! O m�s especificado cont�m no m�ximo 31 dias");
            document.getElementById(Ncampo).focus();
            document.getElementById(Ncampo).value = "";
            return false;
        } else if ((mes==4 || mes==6 || mes==9 || mes==11) && (dia > 30)) {
            showMessageGrowOrAlert("Data incorreta! O m�s especificado cont�m no m�ximo 30 dias");
            document.getElementById(Ncampo).value = "";
            document.getElementById(Ncampo).focus();
            return false;
        } else {
            if ((ano%4!=0) && (mes==2) && (dia>28)) {
                showMessageGrowOrAlert("Data incorreta! O m�s especificado cont�m no m�ximo 28 dias.");
                document.getElementById(Ncampo).value = "";
                document.getElementById(Ncampo).focus();
            } else{
                if ((ano%4==0) && (mes==2) && (dia>29)) {
                    showMessageGrowOrAlert("Data incorreta! O m�s especificado cont�m no m�ximo 29 dias.");
                    document.getElementById(Ncampo).value = "";
                    document.getElementById(Ncampo).focus();
                } else{
                    return true;
                }
            }
        }
    }else{
        document.getElementById(Ncampo).value = "";
        return false;
    }
    return true;
}
function validar_Data_Nula(Ncampo){
    b = document.getElementById(Ncampo).value;
    if(b == "") {
        return true;
    }
    if (b != "__/__/____") {
        if (b.length == 10){
            var dia = b.substring(0,2);
            var mes = b.substring(3,5);
            var ano = b.substring(6,10);
            if ((ano < 1900) || (ano > 2099)) {
                alert("O ano especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if ((mes <= 0) || (mes > 12)) {
                alert("O m�s especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if (dia <= 0) {
                alert("Dia especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if ((mes==1 || mes==3 || mes==5 || mes==7 || mes==8 || mes==10 || mes==12) && (dia > 31)) {
                alert("Data incorreta! O m�s especificado cont�m no m�ximo 31 dias");
                document.getElementById(Ncampo).focus();
                return false;
            } else if ((mes==4 || mes==6 || mes==9 || mes==11) && (dia > 30)) {
                alert("Data incorreta! O m�s especificado cont�m no m�ximo 30 dias");
                document.getElementById(Ncampo).focus();
                return false;
            } else {
                if ((ano%4!=0) && (mes==2) && (dia>28)) {
                    alert("Data incorreta! O m�s especificado cont�m no m�ximo 28 dias.");
                    document.getElementById(Ncampo).focus();
                    return false;
                } else{
                    if ((ano%4==0) && (mes==2) && (dia>29)) {
                        alert("Data incorreta! O m�s especificado cont�m no m�ximo 29 dias.");
                        document.getElementById(Ncampo).focus();
                        return false;
                    } else{
                        return true;
                    }
                }
            }
        } else {
            alert("Formato ou data inv�lida " + b + " (Exemplo de data: dd/mm/aaaa).");
            document.getElementById(Ncampo).focus();
            return false;
        }
    } else {
        alert("Formato ou data inv�lida " + b + " (Exemplo de data: dd/mm/aaaa).");
        document.getElementById(Ncampo).focus();
        return false;
    }
}

function focoTabNavigator(NCampo){
    document.getElementById("form:questionarioCliente:" + numeroLinha + ":data").focus();
}


function validar_Telefone(Ncampo){
    b = document.getElementById(Ncampo).value;
    if (b != "") {
        if (b.length < 12) {
            document.getElementById(Ncampo).focus();
            alert("Formato de telefone inv�lido!");
            return false;
        }
    }
}

function mascaraDentroDataTable(form, campo) {
    // form:escolhaFormaPagamento:4:MovPagamentoCheque:
    // 012345678901234567890123456789012345678901234567
    var nomePadrao = 'form:escolhaFormaPagamento:4:MovPagamentoCheque:';
    var nrLinha = campo.substring(48);
    var posFim = nrLinha.indexOf(':',0);
    nrLinha = nrLinha.substring(0, posFim);

    var dataCompensacao_nomeCampo = nomePadrao + nrLinha + ':dataCompensacao';

    //alert(form[salario_nomeCampo].value);
    //alert(form[aliquotaServidor_nomeCampo].value);
    var contribuicaoServidor = 0.0;
    var percCalcServidor = parseFloat(form[aliquotaServidor_nomeCampo].value);
    var salario = parseFloat(form[salario_nomeCampo].value);
    //alert(percCalcServidor);
    //alert(form[salario_nomeCampo].value);
    if (percCalcServidor != 0) {
        contribuicaoServidor = (salario * (percCalcServidor / 100));
    }
    form[contribuicaoServidor_nomeCampo].value = contribuicaoServidor.toLocaleString(2);
    var contribuicaoPatronal = 0.0;
    var percCalcPatronal = parseFloat(form[aliquotaPatronal_nomeCampo].value);
    //alert(percCalcPatronal);
    //alert(form[salario_nomeCampo].value);
    if (percCalcPatronal != 0) {
        contribuicaoPatronal = (salario * (percCalcPatronal / 100));
    }
    form[contribuicaoPatronal_nomeCampo].value = contribuicaoPatronal.toLocaleString(2);
    var totalFinal = contribuicaoServidor + contribuicaoPatronal;
    form[totalContribuicao_nomeCampo].value = totalFinal.toLocaleString(2);
}

function validarEnter(e, idBotao){

    if(e == null){
        return true;
    }
    var whichCode = (window.Event) ? e.which : e.keyCode;

    if(whichCode == 13){
        fireElement(idBotao);
        return true;
    }else{
        return true;
    }
}
function desValidarEnter(e, idBotao){

    if(e == null){
        return false;
    }
    var whichCode = (window.Event) ? e.which : e.keyCode;

    if(whichCode == 13){
        return false;
    }
}
/*
 *
 **/
function fireElement(id) {
    var target = document.getElementById(id);
    if (document.dispatchEvent) { // W3C
        var oEvent = document.createEvent( "MouseEvents" );
        oEvent.initMouseEvent("click", true, true,window, 1, 1, 1, 1, 1, false, false, false, false, 0, target);
        target.dispatchEvent( oEvent );
    } else {
        if(document.fireEvent) { // IE
            target.fireEvent("onclick");
        }
    }
}

function fireElementFromParent(id) {
    var target = window.opener.document.getElementById(id);
    if (target) {
        if (window.opener.document.dispatchEvent) { // W3C
            var oEvent = window.opener.document.createEvent("MouseEvents");
            oEvent.initMouseEvent("click", true, true, window, 1, 1, 1, 1, 1, false, false, false, false, 0, target);
            target.dispatchEvent(oEvent);
        } else {
            if (window.opener.document.fireEvent) { // IE
                target.fireEvent("onclick");
            }
        }
    }
}

function dispararEvento(id, event){
    var element = document.getElementById(id);
    var evt;
    if (document.createEventObject){
        // IE
        evt = document.createEventObject();
        return element.fireEvent('on'+event,evt);
    }
    else{
        // firefox e outros
        evt = document.createEvent("HTMLEvents");
        evt.initEvent(event, true, true ); // event type,bubbling,cancelable
        return !element.dispatchEvent(evt);
    }
}

function Tecla2(objForm, strField, e) {
    //    TeclasAtalho(e);
    if(document.all) { // Internet Explorer
        var tecla = e.keyCode;
    } else if(document.layers) { // Nestcape
        var tecla = e.which;
    } else {
        var tecla = e.which;
    }
    if (tecla > 47 && tecla < 58) // numeros de 0 a 9
        return true;
    else {
        if (tecla == 0) { // backspace
            return true;
        } else if (tecla != 8) {
            return false;
        } else {
            return true;
        }
    }
}
function FormataValor(campo,tammax,teclapres) {

    var tecla = teclapres.keyCode;
    var vr = campo.value;
    vr = vr.replace( ",", "" );
    vr = vr.replace( ".", "" );
    tam = vr.length;

    if (tam < tammax && tecla != 8){
        tam = vr.length + 1 ;
    }

    if (tecla == 8 ){
        tam = tam - 1 ;
    }

    if ( tecla == 8 || (tecla >= 48 && tecla <= 57) || (tecla >= 96 && tecla <= 105) ){
        if ( tam <= 2 ){
            campo.value = vr ;
        }
        tam = tam - 1;
        if ( (tam > 2) && (tam <= 5) ){
            campo.value = vr.substr( 0, tam - 2 ) + '.' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 6) && (tam <= 8) ){
            campo.value = vr.substr( 0, tam - 5 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 9) && (tam <= 11) ){
            campo.value = vr.substr( 0, tam - 8 ) + '.' + vr.substr( tam - 8, 3 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 12) && (tam <= 14) ){
            campo.value = vr.substr( 0, tam - 11 ) + '.' + vr.substr( tam - 11, 3 ) + '.' + vr.substr( tam - 8, 3 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 15) && (tam <= 17) ){
            campo.value = vr.substr( 0, tam - 14 ) + '.' + vr.substr( tam - 14, 3 ) + '.' + vr.substr( tam - 11, 3 ) + '.' + vr.substr( tam - 8, 3 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
    }
}


function MascaraMoeda(e){
    var objTextBox = e.element();
    var SeparadorMilesimo = '.';
    var SeparadorDecimal = ',';
    var sep = 0;
    var key = '';
    var i = j = 0;
    var len = len2 = 0;
    var strCheck = '0123456789';
    var aux = aux2 = '';
    var whichCode = (window.Event) ? e.which : e.keyCode;

    objTextBox=objTextBox.replace(/\D/g,"");
    if (whichCode == 13) return true;
    key = String.fromCharCode(whichCode); // Valor para o c�digo da Chave
    if (strCheck.indexOf(key) == -1) return false; // Chave inv�lida
    len = objTextBox.value.length;
    for(i = 0; i < len; i++)
	if ((objTextBox.value.charAt(i) != '0') && (objTextBox.value.charAt(i) != SeparadorDecimal)) break;
    aux = '';
    for(; i < len; i++)
	if (strCheck.indexOf(objTextBox.value.charAt(i))!=-1) aux += objTextBox.value.charAt(i);
    aux += key;
    len = aux.length;
    if (len == 0) objTextBox.value = '';
    if (len == 1) objTextBox.value = '0'+ SeparadorDecimal + '0' + aux;
    if (len == 2) objTextBox.value = '0'+ SeparadorDecimal + aux;
    if (len > 2) {
	aux2 = '';
	for (j = 0, i = len - 3; i >= 0; i--) {
            if (j == 3) {
                aux2 += SeparadorMilesimo;
                j = 0;
            }
            aux2 += aux.charAt(i);
            j++;
	}
	objTextBox.value = '';
	len2 = aux2.length;
	for (i = len2 - 1; i >= 0; i--)
            objTextBox.value += aux2.charAt(i);
	objTextBox.value += SeparadorDecimal + aux.substr(len - 2, len);
    }
    return false;
}

function moeda(z){
    v = z.value;
    v=v.replace(/\D/g,"");  //permite digitar apenas n�meros
    v=v.replace(/[0-9]{12}/,"inv?lido");   //limita pra m�ximo 999.999.999,99
    v=v.replace(/(\d{1})(\d{8})$/,"$1.$2")  //coloca ponto antes dos �ltimos 8 digitos
    v=v.replace(/(\d{1})(\d{5})$/,"$1.$2");  //coloca ponto antes dos �ltimos 5 digitos
    v=v.replace(/(\d{1})(\d{1,2})$/,"$1,$2");	//coloca virgula antes dos �ltimos 2 digitos
    z.value = v;
}

function porcentagem(z){
    v = z.value;
    v=v.replace(/\D/g,"");  //permite digitar apenas n�meros
    v=v.replace(/[0-9]{6}/,"inv?lido");   //limita pra m�ximo 999,99
    v=v.replace(/(\d{1})(\d{1,2})$/,"$1.$2");	//coloca virgula antes dos �ltimos 2 digitos
    z.value = v;
}

function moedaComTipo(z){
    v = z.value;
    v=v.replace(/\D/g,"");  //permite digitar apenas n�meros
    v=v.replace(/[0-9]{12}/,"inv?lido");   //limita pra m�ximo 999.999.999,99
    v=v.replace(/(\d{1})(\d{8})$/,"$1.$2");  //coloca ponto antes dos �ltimos 8 digitos
    v=v.replace(/(\d{1})(\d{5})$/,"$1.$2");  //coloca ponto antes dos �ltimos 5 digitos
    v=v.replace(/(\d{1})(\d{1,2})$/,"$1,$2");	//coloca virgula antes dos �ltimos 2 digitos
    if(v != '' || v === 'R$ ' ) {
        v = "R$ " + v;
    }
    z.value = v;
}

function formatNumber(number) {
    number = number.toFixed(2) + '';
    x = number.split('.');
    x1 = x[0];
    x2 = x.length > 1 ? ',' + x[1] : '';
    var rgx = /(\d+)(\d{3})/;
    while (rgx.test(x1)) {
        x1 = x1.replace(rgx, '$1' + '.' + '$2');
    }
    return x1 + x2;
}

function parseStringReaisParaFloat(valorString) {
    return parseFloat(valorString.replace('.','').replace(',','.'));
}

function converterPorcentagemParaReais(idPainel, idCampoPorcentagem, idCampoReais, idCampoValorTotal) {
    var valorTotal = parseStringReaisParaFloat(document.getElementById(idPainel + ':' + idCampoValorTotal).innerHTML);

    if (!document.getElementById(idPainel + ':' + idCampoPorcentagem).value) {
        var valorEmReais = parseStringReaisParaFloat(document.getElementById(idPainel + ':' + idCampoReais).value);
        document.getElementById(idPainel + ':' + idCampoPorcentagem).value =  formatNumber((valorEmReais / valorTotal) * 100);
        alert("O campo multa em porcentagem n�o pode ser vazio!");
    }

    var valorPorcentagem = parseStringReaisParaFloat(document.getElementById(idPainel + ':' + idCampoPorcentagem).value);
    document.getElementById(idPainel + ':' + idCampoReais).value = formatNumber((valorTotal * valorPorcentagem) / 100);
}

function converterReaisParaPorcentagem(idPainel, idCampoPorcentagem, idCampoReais, idCampoValorTotal) {
    var valorTotal = parseStringReaisParaFloat(document.getElementById(idPainel + ':' + idCampoValorTotal).innerHTML);

    if (!document.getElementById(idPainel + ':' + idCampoReais).value) {
        var valorPorcentagem = parseStringReaisParaFloat(document.getElementById(idPainel + ':' + idCampoPorcentagem).value);
        document.getElementById(idPainel + ':' + idCampoReais).value = formatNumber((valorTotal * valorPorcentagem) / 100);
        alert("O campo multa em reais n�o pode ser vazio!");
    }

    var valorEmReais = parseStringReaisParaFloat(document.getElementById(idPainel + ':' + idCampoReais).value);
    document.getElementById(idPainel + ':' + idCampoPorcentagem).value = formatNumber((valorEmReais / valorTotal) * 100);
}

function formatar_moeda(campo, separador_milhar, separador_decimal, tecla) {
    var sep = 0;
    var key = '';
    var i = j = 0;
    var len = len2 = 0;
    var strCheck = '0123456789';
    var aux = aux2 = '';
    var whichCode = (window.Event) ?  tecla.which : tecla.keyCode;

    if(navigator.appName.indexOf("Netscape") == -1){
        whichCode= event.keyCode;
    }

    if (whichCode == 13) return true; // Tecla Enter
    if (whichCode == 8) return true; // Tecla Delete
    if(whichCode == 0) return true; //Tecla TAB

    key = String.fromCharCode(whichCode); // Pegando o valor digitado
    if (strCheck.indexOf(key) == -1) return false; // Valor inv�lido (n�o inteiro)

    len = campo.value.length;
    for(i = 0; i < len; i++)
        if ((campo.value.charAt(i) != '0') && (campo.value.charAt(i) != separador_decimal)) break;
    aux = '';

    for(; i < len; i++)
        if (strCheck.indexOf(campo.value.charAt(i))!=-1) aux += campo.value.charAt(i);

    aux += key;
    len = aux.length;
    if (len == 0) campo.value = '';
    if (len == 1) campo.value = '0'+ separador_decimal + '0' + aux;
    if (len == 2) campo.value = '0'+ separador_decimal + aux;

    if (len > 2) {
        aux2 = '';

        for (j = 0, i = len - 3; i >= 0; i--) {
            if (j == 3) {
                aux2 += separador_milhar;
                j = 0;
            }
            aux2 += aux.charAt(i);
            j++;
        }

        campo.value = '';
        len2 = aux2.length;
        for (i = len2 - 1; i >= 0; i--)
            campo.value += aux2.charAt(i);
        campo.value += separador_decimal + aux. substr(len - 2, len);
    }

    return false;
}

function popup(URL, nomeJanela, comprimento, altura) {
    var posTopo = ((screen.height / 2) - (altura / 2));
    var posEsquerda = ((screen.width / 2) -(comprimento / 2));
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ', dependent=yes, menubar=no, toolbar=no, resizable=yes, scrollbars=yes' ;
    var popUpWindow = window.open(URL, nomeJanela, atributos);
    verificaPopUpAbriu(popUpWindow);
    popUpWindow.focus();
    popUpWindow.submit();
    return true;
}

function autoTab(input)
{
    alert();
    var tecla = event.keyCode;
    var ind = 0;
    var isNN = (navigator.appName.indexOf("Netscape")!=-1);
    var keyCode = (isNN) ? tecla.which : tecla.keyCode;
    var nKeyCode = tecla.keyCode;

    if(keyCode == 13){
        if (!isNN) {window.event.keyCode = 0;} // evitar o beep
        ind = getIndex(input);
        if (input.form[ind].type == 'textarea') {
            return;
        }
        ind++;

        input.form[ind].focus();
        if (input.form[ind].type == 'text') {
            input.form[ind].select();
        }
    }
}

function getIndex(input)
{
    var index = -1, i = 0, found = false;
    while (i < input.form.length && index == -1)
        if (input.form[i] == input) {
            index = i;
            if (i < (input.form.length -1)) {
                if (input.form[i+1].type == 'hidden') {
                    index++;
                }
                if (input.form[i+1].type == 'button' && input.form[i+1].id == 'tabstopfalse') {
                    index++;
                }
        }
    }
    else
        i++;
    return index;
}
function abrirPopupSemRedimensionar(URL, nomeJanela, comprimento, altura) {
    var posTopo = ((screen.height / 2) - (altura / 2));
    var posEsquerda = ((screen.width / 2) -(comprimento / 2));
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ' dependent=yes, menubar=no, toolbar=no, resizable=no , scrollbars=no' ;
    var parameterCaracter = URL.include("?") ? "&" : "?";
    var urlPopup = URL+parameterCaracter+"from=popup";
    var popUpWindow = window.open(urlPopup, nomeJanela, atributos);
    verificaPopUpAbriu(popUpWindow);
    return false;
}


function mascara_com_sinal_negativo(obj){
  valida_num(obj)
  if (obj.value.match("-")){
    mod = "-";
  }else{
    mod = "";
  }
  valor = obj.value.replace("-","");
  valor = valor.replace(",","");
  if (valor.length >= 3){
    valor = poe_ponto_num(valor.substring(0,valor.length-2))+","+valor.substring(valor.length-2, valor.length);
  }
  obj.value = mod+valor;
}
function poe_ponto_num(valor){
  valor = valor.replace(/\./g,"");
  if (valor.length > 3){
    valores = "";
    while (valor.length > 3){
      valores = "."+valor.substring(valor.length-3,valor.length)+""+valores;
      valor = valor.substring(0,valor.length-3);
    }
    return valor+""+valores;
  }else{
    return valor;
  }
}
function valida_num(obj){
  numeros = new RegExp("[0-9]");
  while (!obj.value.charAt(obj.value.length-1).match(numeros)){
    if(obj.value.length == 1 && obj.value == "-"){
      return true;
    }
    if(obj.value.length >= 1){
      obj.value = obj.value.substring(0,obj.value.length-1)
    }else{
      return false;
    }
  }
}


function fireElementFromAnyParent(id) {
    try{
        var windowParent = window.opener;
        var target;
        if(windowParent){
            target = windowParent.document.getElementById(id);
        }

        while (windowParent != null && target == null) {
            windowParent = windowParent.window.opener;
            target = windowParent != null ? windowParent.document.getElementById(id) : null;
        }
        if (windowParent != null) {
            if (windowParent.document.dispatchEvent) { // W3C
                var oEvent = windowParent.document.createEvent("MouseEvents");
                oEvent.initMouseEvent("click", true, true, window, 1, 1, 1, 1, 1, false, false, false, false, 0, target);
                target.dispatchEvent(oEvent);
            } else {
                if (windowParent.document.fireEvent) { // IE
                    target.fireEvent("onclick");
                }
            }
        }
    }catch (e){
        console.log('Erro ao disparar evento (fireElementFromAnyParent)', e);
    }
}

function recarregarMetas(){
    fireElementFromAnyParent('form:indicador1:atualizarBIVenda');
    fireElementFromAnyParent('form:indicador2:atualizarBIRetencao');
    fireElementFromAnyParent('form:atualizarBIEstudio');
    fireElementFromAnyParent('form:btnAtualizar');
}

var curDt = new Date();
function desabilitarDataRetroativa(day){
    if (curDt==undefined){
        curDt = day.date.getDate;
    }
    if (curDt.getTime() - day.date.getTime() < 0) return true;
    else return false;
}

function bloquearCtrlJ() {   // Verifica��o das Teclas
    var tecla = window.event.keyCode;   //Para controle da tecla pressionada
    var ctrl = window.event.ctrlKey;    //Para controle da Tecla CTRL

    if (ctrl && tecla == 74) {    //Evita teclar ctrl + j
        event.keyCode = 0;
        event.returnValue = false;
    }
}

function limitTextArea(element, limit) {
    if (element.value.length > limit) {
        element.value = element.value.substring(0, limit);
    }
}


function adicionarPlaceHolderCRM() {
    try{
        if (document.getElementById("form:eventoIndicado") != null) {
            document.getElementById("form:eventoIndicado").setAttribute("placeholder", "Evento");
        }
        if (document.getElementById("form:nomeIndicado") != null) {
            document.getElementById("form:nomeIndicado").setAttribute("placeholder", "Nome do Indicado");
        }
        if (document.getElementById("form:telIndicado01") != null) {
            document.getElementById("form:telIndicado01").setAttribute("placeholder", "Celular");
        }
        if (document.getElementById("form:telIndicado02") != null) {
            document.getElementById("form:telIndicado02").setAttribute("placeholder", "Tel Residencial");
        }
        if (document.getElementById("form:emailIndicado") != null) {
            document.getElementById("form:emailIndicado").setAttribute("placeholder", "Email");
        }
        if (document.getElementById("form:obsIndicado") != null) {
            document.getElementById("form:obsIndicado").setAttribute("placeholder", "Observa\u00e7\u00e3o");
        }
        if (document.getElementById("form:horaMinutoAgendamento") != null) {
            document.getElementById("form:horaMinutoAgendamento").setAttribute("placeholder", "Hora : Minuto");
        }
        if (document.getElementById("form:remetenteEmail") != null) {
            document.getElementById("form:remetenteEmail").setAttribute("placeholder", "De :");
        }
        if (document.getElementById("form:tituloEmail") != null) {
            document.getElementById("form:tituloEmail").setAttribute("placeholder", "T\u00edtulo");
        }
        if (document.getElementById("form:destinatarioEmail") != null) {
            document.getElementById("form:destinatarioEmail").setAttribute("placeholder", "Para :");
        }
        if (document.getElementById("form:tituloMensagemAPP") != null) {
            document.getElementById("form:tituloMensagemAPP").setAttribute("placeholder", "T\u00edtulo");
        }
        if (document.getElementById("form:mensagemAPP") != null) {
            document.getElementById("form:mensagemAPP").setAttribute("placeholder", "Mensagem");
        }
        if (document.getElementById("form:mensagemSMS") != null) {
            document.getElementById("form:mensagemSMS").setAttribute("placeholder", "Mensagem");
        }
        if (document.getElementById("form:eventoIndicadoMeta") != null) {
            document.getElementById("form:eventoIndicadoMeta").setAttribute("placeholder", "Evento");
        }
        if (document.getElementById("form:clienteOUColaboradorIndicou") != null) {
            document.getElementById("form:clienteOUColaboradorIndicou").setAttribute("placeholder", "Cliente ou Colaborador que indicou");
        }
        if (document.getElementById("form:nomeIndicadoMeta") != null) {
            document.getElementById("form:nomeIndicadoMeta").setAttribute("placeholder", "Nome do Indicado");
        }
        if (document.getElementById("form:telIndicado01Meta") != null) {
            document.getElementById("form:telIndicado01Meta").setAttribute("placeholder", "Celular");
        }
        if (document.getElementById("form:telIndicado02Meta") != null) {
            document.getElementById("form:telIndicado02Meta").setAttribute("placeholder", "Tel Residencial");
        }
        if (document.getElementById("form:emailIndicadoMeta") != null) {
            document.getElementById("form:emailIndicadoMeta").setAttribute("placeholder", "Email");
        }
        if (document.getElementById("form:obsIndicadoMeta") != null) {
            document.getElementById("form:obsIndicadoMeta").setAttribute("placeholder", "Observa\u00e7\u00e3o");
        }
        if (document.getElementById("form:textModeloMensagem") != null) {
            document.getElementById("form:textModeloMensagem").setAttribute("placeholder", "Modelo de Mensagem");
        }
        if (document.getElementById("form:textModeloMensagemSMS") != null) {
            document.getElementById("form:textModeloMensagemSMS").setAttribute("placeholder", "Modelo de Mensagem");
        }
        if (document.getElementById("form:textModeloMensagemAPP") != null) {
            document.getElementById("form:textModeloMensagemAPP").setAttribute("placeholder", "Modelo de Mensagem");
        }
        if (document.getElementById("form:horaMinutoReagendamento") != null) {
            document.getElementById("form:horaMinutoReagendamento").setAttribute("placeholder", "Hora : Minuto");
        }
        if (document.getElementById("form:nomePassivo") != null) {
            document.getElementById("form:nomePassivo").setAttribute("placeholder", "Nome");
        }
        if (document.getElementById("form:telResidencialPassivo") != null) {
            document.getElementById("form:telResidencialPassivo").setAttribute("placeholder", "Tel Residencial");
        }
        if (document.getElementById("form:telCelularPassivo") != null) {
            document.getElementById("form:telCelularPassivo").setAttribute("placeholder", "Celular");
        }
        if (document.getElementById("form:telTrabalhoPassivo") != null) {
            document.getElementById("form:telTrabalhoPassivo").setAttribute("placeholder", "Tel Trabalho");
        }
        if (document.getElementById("form:emailPassivo") != null) {
            document.getElementById("form:emailPassivo").setAttribute("placeholder", "Email");
        }
        if (document.getElementById("form:eventoPassivo") != null) {
            document.getElementById("form:eventoPassivo").setAttribute("placeholder", "Evento");
        }
        if (document.getElementById("form:obsPassivo") != null) {
            document.getElementById("form:obsPassivo").setAttribute("placeholder", "Coment\u00e1rio");
        }
        if (document.getElementById("form:horaMinutoAgendamentoIndicacao") != null) {
            document.getElementById("form:horaMinutoAgendamentoIndicacao").setAttribute("placeholder", "Hora : Minuto");
        }
        if (document.getElementById("form:horaMinutoAgendamentoPassivo") != null) {
            document.getElementById("form:horaMinutoAgendamentoPassivo").setAttribute("placeholder", "Hora : Minuto");
        }
        if (document.getElementById("form:observacaoReagendamento") != null) {
            document.getElementById("form:observacaoReagendamento").setAttribute("placeholder", "Coment\u00e1rio");
        }
        if (document.getElementById("form:observacaoHistorico") != null) {
            document.getElementById("form:observacaoHistorico").setAttribute("placeholder", "Coment\u00e1rio");
        }
        if (document.getElementById("form:searchInput") != null) {
            document.getElementById("form:searchInput").setAttribute("placeholder", "Nome ou matr\u00edcula");
        }
        if (document.getElementById("form:searchInputPassivo") != null) {
            document.getElementById("form:searchInputPassivo").setAttribute("placeholder", "Buscar por nome");
        }
        if (document.getElementById("form:cpfindicado") != null) {
            document.getElementById("form:cpfindicado").setAttribute("placeholder", "CPF do Indicado");
        }
        if (document.getElementById("form:cpfindicadoMeta") != null) {
            document.getElementById("form:cpfindicadoMeta").setAttribute("placeholder", "CPF do Indicado");
        }
    }catch (e) {
        console.log("ERRO adicionarPlaceHolderCRM: "+e);
    }
}

function dois_pontos(tempo) {
    if (event.keyCode < 48 || event.keyCode > 57) {
        event.returnValue = false;
    }
    if (tempo.value.length == 2) {
        tempo.value += ":";
    }
}

function bloquearEnter() {
    var tecla = window.event.keyCode;
    if (tecla == 13) {
        event.keyCode = 0;
        event.returnValue = false;
    }
}


function carregarTooltipster(){
    try {
        jQuery('.tooltipster').tooltipster({
            theme: 'tooltipster-light',
            position: 'top',
            animation: 'grow',
            delay: 100,
            contentAsHTML: true
        });
        jQuery('.tooltipsterbottom').tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            delay: 100,
            contentAsHTML: true
        });
        jQuery('.tooltipsterright').tooltipster({
            theme: 'tooltipster-light',
            hideOnClick: true,
            position: 'right',
            animation: 'grow',
            delay: 100,
            contentAsHTML: true
        });
        jQuery('.tooltipclientesMarcados').tooltipster({
            theme: 'tooltipster-light',
            hideOnClick: true,
            position: 'bottom',
            delay: 0,
            contentAsHTML: true
        });
    }catch (ex){
        console.log('Erro [carregarTooltipster] '+ex)
    }
}

function converteFloatMoeda(valor){
      var inteiro = null, decimal = null, c = null, j = null;
      var aux = new Array();
      valor = ""+valor;
      c = valor.indexOf(".",0);
      //encontrou o ponto na string
      if(c > 0){
         //separa as partes em inteiro e decimal
         inteiro = valor.substring(0,c);
         decimal = valor.substring(c+1,valor.length);
      }else{
         inteiro = valor;
      }

      //pega a parte inteiro de 3 em 3 partes
      for (j = inteiro.length, c = 0; j > 0; j-=3, c++){
         aux[c]=inteiro.substring(j-3,j);
      }

      //percorre a string acrescentando os pontos
      inteiro = "";
      for(c = aux.length-1; c >= 0; c--){
         inteiro += aux[c]+'.';
      }
      //retirando o ultimo ponto e finalizando a parte inteiro

      inteiro = inteiro.substring(0,inteiro.length-1);

      if (isNaN(parseInt(decimal))) {
         decimal = "00";
      } else if (decimal.length == 1) {
         decimal = decimal + "0";
      } else if (decimal.length > 2) {
          decimal = decimal.substring(0,2);
      }

      valor = inteiro+","+decimal;


      return valor;

   }

function validarTamanhoBanner(validarHeight) {
    var semCarousel = true;
    var tamanho = jQuery('.carousel.slide img').length;
    jQuery('.carousel.slide img').each(function (i) {
        var obj = jQuery(this);
        var image = new Image();
        image.src = obj.attr("src");
        image.onload = function(){
            if(image.width <= 1706 && (image.height == 740 || !validarHeight)){
                obj.addClass('imagemBanner');
                semCarousel = false;
            } else {
                var  item = jQuery('.carousel ol li:nth-child('+(i+1)+'),.carousel-inner .item:nth-child('+(i+1)+')');
                item.remove();
                tamanho = jQuery('.carousel.slide img').length;
                console.log('IMAGEM NAO EXIBIDA EM BANNER '+obj.attr("src"));
            }
            if(tamanho >= (i+1)){
                esconderCarousel(semCarousel);
                jQuery('.container-imagem img').css('max-width',(window.innerWidth - 206 - 8));
                jQuery('.carousel-inner .item:first-child').addClass('active');
            }
        };
    });
}
function esconderCarousel(semCarousel){
    if(semCarousel){
        jQuery('.carousel').remove();
        jQuery('.bannerPadrao').show();
    }else{
        jQuery("#myCarousel").carousel({interval: 10000});
        jQuery('.carousel').show();
        jQuery('.bannerPadrao').addClass('hidden');
    }
}

function setDocumentCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + (exdays*24*60*60*1000));
    var expires = "expires="+ d.toUTCString();
    document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
}

function getDocumentCookie(cname) {
    if (cname === 'popupsImportante' && angular()) {
        setDocumentCookie('popupsImportante', '', 1);
        return '';
    }
    var name = cname + "=";
    var decodedCookie = decodeURIComponent(document.cookie);
    var ca = decodedCookie.split(';');
    for(var i = 0; i <ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') {
            c = c.substring(1);
        }
        if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
        }
    }
    return "";
}

function angular() {
    try {
        const queryString = window.location.search;
        const urlParams = new URLSearchParams(queryString);
        const angular = urlParams.get('angular')
        return (angular === 't');
    } catch (e) {
        console.log(e);
        return false;
    }
}

function somenteNumerosInteiros(num) {
    var er = /[^0-9]/;
    er.lastIndex = 0;
    if (er.test(num.value)) {
        num.value = "0";
    }
}

/*
* M�todo respons�vel por enviar mensagens de dentro de uma janela para o pai.
*
* Usado atualmente no projeto em Angular para algumas a��es como fechar ou ap�s a conclus�o de algum processamento.
* */
function executePostMessage(body) {
    try {
        var win = window.parent;
        if (win) {
            var finalBody = body;
            if (typeof body === 'object') {
                finalBody = JSON.stringify(body);
            }
            win.postMessage(finalBody, '*');
        }
    } catch(e) {
        console.error(e);
    }
}
