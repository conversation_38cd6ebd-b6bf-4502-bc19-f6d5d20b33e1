   function imprimirDireto() {
    if (navigator.appName == "Netscape") {
        window.print(); 
    } else {
        var WebBrowser = '<OBJECT ID="WebBrowser1" WIDTH=0 HEIGHT=0 ' + 
            'CLASSID="CLSID:8856F961-340A-11D0-A96B-00C04FD705A2"></OBJECT>'; 
							 
        document.body.insertAdjacentHTML('beforeEnd', WebBrowser);
        WebBrowser1.ExecWB(6,11);
    }
}


function setFocus(form, campo) {
    form[campo].focus();
}

function imprimirRelatorio(form) {
    if (window.document.getElementById("form:relatorio").value == "sim") {   
        var hiddenCommandLink = window.document.getElementById("form:imprimirRelatorio");
        if (hiddenCommandLink) {
        
            //hiddenCommandLink.fireEvent("onclick");
            hiddenCommandLink.onclick();
        } 
    } else {         
        form.submit();
    }
}

function selecionarRichCalendarColaborador_dataNasc(form) {
    var hiddenCommandLink = window.document.getElementById("form:dataNascCommandLink");
    if (hiddenCommandLink) {
        //hiddenCommandLink.fireEvent("onclick");
        hiddenCommandLink.onclick();
    }
}

function selecionarRichCalendarCliente_dataNasc(form) {
    var hiddenCommandLink = window.document.getElementById("form:dataNascCommandLink");
    if (hiddenCommandLink) {
        //hiddenCommandLink.fireEvent("onclick");
        hiddenCommandLink.onclick();
    }
}

function testarCodigofecharJanela(form) {
    var codigo = form["form:codigo"].value;
    if (codigo != 0) {
        window.close();
    } 
}

function abrirPopupMaximizada(URL, nomeJanela) {
    var comprimento = screen.width;
    var altura = screen.height;
    var atributos = 'width=' + comprimento + ", height=" + altura + ', dependent=yes, menubar=no, toolbar=no, resizable=yes , scrollbars=yes' ;
    var parameterCaracter = URL.include("?") ? "&" : "?";
    var urlPopup = URL+parameterCaracter+"from=popup";
    window.open(urlPopup,nomeJanela,atributos);
    return false;
}

function replaceAll(texto, valor, novoValor) {
    while (texto.indexOf(valor) > -1) {
        texto = texto.replace(valor, novoValor);
    }
    return texto;
}

function pegarUrlCompleta(URL) {
    var Urlrecebida = window.location.href;
    var novoCaminho = Urlrecebida.substring(Urlrecebida.indexOf('/faces/'), Urlrecebida.lastIndexOf('/') + 1)
    Urlrecebida = Urlrecebida.replace(novoCaminho, '/');
    var urlCompleta = replaceAll(Urlrecebida, '/faces/', '/');
    if (URL.indexOf("../") > -1) {
        URL = replaceAll(URL,"../","");
    }
    urlCompleta = urlCompleta.substring(0, urlCompleta.lastIndexOf('/') + 1) + URL;
    var link = document.createElement("a");
    link.href = urlCompleta;

    var marcadorNomeArquivo = "file=";
    var nomeArquivoIndex = URL.indexOf(marcadorNomeArquivo);
    if (nomeArquivoIndex > -1) {
        var nomeArquivo = URL.substring(nomeArquivoIndex + marcadorNomeArquivo.length);
        if (nomeArquivo.indexOf("&") > -1) {
            nomeArquivo = nomeArquivo.substring(0, nomeArquivo.indexOf("&"));
        }
        link.download = nomeArquivo;
    }
    return link;
}

function abrirPopup(URL, nomeJanela, comprimento, altura) {
    var parameterCaracter = URL.include("?") ? "&" : "?";
    var urlPopup = URL+parameterCaracter+"from=popup";
    if(URL.indexOf("UpdateServlet?op=downloadfile") > -1) {

        link = pegarUrlCompleta(urlPopup);
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        delete link;
    } else {
        var posTopo = 0;//((screen.height / 2) - (altura / 2));
        var posEsquerda = 0;//((screen.width / 2) - (comprimento / 2));
        var comprimento = screen.width;
        var altura = screen.height;
        var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ' dependent=yes, menubar=no, toolbar=no, resizable=yes , scrollbars=yes';

        var popUpWindow = window.open(urlPopup, nomeJanela, atributos);
        verificaPopUpAbriu(popUpWindow);
        return false;
    }
}

function abrirPopupDownload(URL, nomeJanela, comprimento, altura) {   
    var posTopo = 0;//((screen.height / 2) - (altura / 2));
    var posEsquerda = 0;//((screen.width / 2) -(comprimento / 2));
    var comprimento = screen.width;
    var altura = screen.height;
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ' menubar=no, toolbar=no, resizable=yes , scrollbars=yes' ;
    newwin = window.open(URL, nomeJanela, atributos);
    return false;
}

function fecharJanela() {	
    window.close();
}

function Tecla(e) {
    TeclasAtalho(e);
    if (document.all) // Internet Explorer
        var tecla = event.keyCode;
    else if(document.layers) // Nestcape
        var tecla = e.which;
    if ((tecla > 47 && tecla < 58) || (tecla == 44)|| (tecla == 46)) // numeros de 0 a 9 ponto (.) e virgula(,)
        return true;
    else {
        if (tecla != 8) { // backspace
            event.keyCode = 0;
        } else {
            return true;
        }    
    }
}				
    
function TeclasAtalho(e) {
    if (e.shiftKey) {
        if (e.keyCode == 43) {
            event.keyCode = 0;
            document.forms[0].incluir.click();
        } else if (e.keyCode == 45) {
            event.keyCode = 0;
            document.forms[0].excluir.click();
        } else if (e.keyCode == 46) {
            event.keyCode = 0;
            document.forms[0].gravar.click();
        } else if (e.keyCode == 42) {
            event.keyCode = 0;
            document.forms[0].consultar.click();
        }
    }
}

function getSelText() {
    var txt = '';
    if (window.getSelection) {
        txt = window.getSelection();
    } else if (document.getSelection) {
        txt = document.getSelection();
    }  else if (document.selection) {
        txt = document.selection.createRange().text;
    }  else {
        return;
    }
    return txt;
}

function mascaraMatricula(objForm, strField, evtKeyPress) {
    var nTecla;
    var textoSel = getSelText();
    if (textoSel != '') {
        return true;
    }    
    TeclasAtalho(evtKeyPress);
    if(document.all) { // Internet Explorer
        nTecla = evtKeyPress.keyCode;       
    } else if(document.layers) { // Nestcape
        nTecla = evtKeyPress.which;
    } else {
        nTecla = evtKeyPress.which;
        if (nTecla == 120) {
            return true;
        }
    }
    if ((nTecla == 8)){
        return true;
    }
    if ((nTecla == 9)){
        return true;
    }
        
    if ((nTecla != 120)){        
        return false;                    
    }


}

//onkeypress="return mascara(document.rcfDownload, 'str_cep', '99999-999', event);">
   function mascara(objForm, strField, sMask, evtKeyPress) {
       //alert(objForm[strField].value);
       var i, nCount, sValue, fldLen, mskLen,bolMask, sCod;
       var textoSel = getSelText();
       if (textoSel != '') {
           return true;
       }
       //aqui obtem o nr da tecla digitada
       var nTecla = (evtKeyPress.charCode || evtKeyPress.keyCode || evtKeyPress.which);

       if (evtKeyPress.keyCode != 0 && ((nTecla == 8) || (nTecla == 9))){
           return true;
       }

       TeclasAtalho(evtKeyPress);

       sValue = objForm[strField].value;

       // Limpa todos os caracteres de formata��o que
       // j� estiverem no campo.
       sValue = sValue.toString().replace( "-", "" );
       sValue = sValue.toString().replace( "-", "" );
       sValue = sValue.toString().replace( ".", "" );
       sValue = sValue.toString().replace( ".", "" );
       sValue = sValue.toString().replace( "/", "" );
       sValue = sValue.toString().replace( "/", "" );
       sValue = sValue.toString().replace( "(", "" );
       sValue = sValue.toString().replace( "(", "" );
       sValue = sValue.toString().replace( ")", "" );
       sValue = sValue.toString().replace( ")", "" );
       sValue = sValue.toString().replace( " ", "" );
       sValue = sValue.toString().replace( " ", "" );
       fldLen = sValue.length;
       mskLen = sMask.length;


       i = 0;
       nCount = 0;
       sCod = "";
       mskLen = fldLen;

       while (i <= mskLen) {
           bolMask = ((sMask.charAt(i) == "-") || (sMask.charAt(i) == ":") || (sMask.charAt(i) == ".") || (sMask.charAt(i) == "/"))
           bolMask = bolMask || ((sMask.charAt(i) == "(") || (sMask.charAt(i) == ")") || (sMask.charAt(i) == " "))

           if (bolMask) {
               sCod += sMask.charAt(i);
               mskLen++;
           } else {
               sCod += sValue.charAt(nCount);
               nCount++;
           }

           i++;
       }
       if (sMask.length == sCod.length) {
           //event.keyCode = 0;
           //Joao Alcides : esta modificacao impedira que a funcao permita que o usuario digite mais
           //numeros do que o definido na mascara.
           return false;
       }

       objForm[strField].value = sCod;
       if (nTecla != 8) { // backspace
           if (sMask.charAt(i-1) == "9") { // apenas n�meros...
               return ((nTecla > 47) && (nTecla < 58));
           } // n�meros de 0 a 9
           else { // qualquer caracter...
               return true;
           }
       } else {
           return true;
       }
   }
function permiteSomenteLetra(objForm, strField, evtKeyPress) {
    var nTecla;

    if(document.all) { // Internet Explorer
        nTecla = evtKeyPress.keyCode;
    } else if(document.layers) { // Nestcape
        nTecla = evtKeyPress.which;

    } else {
        nTecla = evtKeyPress.which;
        if (nTecla == 8) {
            return true;
        }
    }
    if (nTecla != 8) { // backspace
        if ((nTecla > 47) && (nTecla < 58)) { // apenas n�meros...
            return false;
        } // n�meros de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    } else {
        return true;
    }
}

function mascaraTodos(objForm, strField, sMask, evtKeyPress) {
    var i, nCount, sValue, fldLen, mskLen,bolMask, sCod, nTecla;
    nTecla = (evtKeyPress.which) ? evtKeyPress.which : evtKeyPress.keyCode;
    sValue = objForm[strField].value;
    // Limpa todos os caracteres de formata��o que
    // j� estiverem no campo.
    expressao = /[\.\/\-\(\)\,\;\: ]/gi;
    sValue = sValue.toString().replace(expressao, '');
    fldLen = sValue.length;
    mskLen = sMask.length;

    i = 0;
    nCount = 0;
    sCod = "";
    mskLen = fldLen;

    while (i <= mskLen) {
        bolMask = ((sMask.charAt(i) == "-") || (sMask.charAt(i) == ".") || (sMask.charAt(i) == "/") || (sMask.charAt(i) == ",") || (sMask.charAt(i) == ";") || (sMask.charAt(i) == ":"))
        bolMask = bolMask || ((sMask.charAt(i) == "(") || (sMask.charAt(i) == ")") || (sMask.charAt(i) == " "))

        if (bolMask) {
            sCod += sMask.charAt(i);
            mskLen++; }
        else {
            sCod += sValue.charAt(nCount);
            nCount++;
        }

        i++;
    }

    objForm[strField].value = sCod;

    if (nTecla != 8 && nTecla != 13)
    { // backspace enter
        if (sMask.charAt(i-1) == "9") 
        { // apenas n�meros...
            return ((nTecla > 47) && (nTecla < 58)); 
        } // n�meros de 0 a 9
        else 
        { 
            if (sMask.charAt(i-1) == "x") 
            { // apenas letras... Sem espaco
                return ((nTecla > 64) && (nTecla < 123)); 
            } // maiusculas e minusculas de A a z sem acentos
            else 
            { // qualquer caracter...
                return true;
            } 
        } 
    }
    else 
    {
        return true;
    }
}


function flash(file, width, height){
    document.write("<object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' codebase='http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cabversion=7,0,19,0' width='" + width + "' height='" + height + "'>");
    document.write("<param name='movie' value='" + file + "'>");
    document.write("<param name='quality' value='high'>");
    document.write("<embed src='" + file + "' quality='high' pluginspage='http://www.macromedia.com/go/getflashplayer' type='application/x-shockwave-flash' width='" + width + "' height='" + height + "'></embed>");
    document.write("</object>");
}

function renderData(Ncampo){
    if ((navigator.userAgent.indexOf("Firefox")!=-1) ||
        (navigator.userAgent.indexOf("Chrome")!=-1)) {
        document.getElementById(Ncampo).style.color="5A480C";
        document.getElementById(Ncampo).style.backgroundColor="FEFFDF";
    }
    return true;
}

function validar_Data(Ncampo){
    var er = /^(([0-2]\d|[3][0-1])\/([0]\d|[1][0-2])\/[1-2][0-9]\d{2})$/;
    //bSemMascara = document.getElementById(Ncampo).value;
    //b = aplicarMascaraValor(bSemMascara, '99/99/9999');
    //document.getElementById(Ncampo).value = b;
    b = document.getElementById(Ncampo).value;  
    if (b != "" && b != "__/__/____") {       
        //if (er.test(b)){
        if (b.length == 10){
            var dia = b.substring(0,2);
            var mes = b.substring(3,5);
            var ano = b.substring(6,10);          

            if ((ano < 1900) || (ano > 2099)) {
                document.getElementById(Ncampo).focus();
                alert("O ano especificado n�o � valido.");
                //  fecharJanela();
                return false;
            }

            if ((mes=="04" || mes=="06" || mes=="09" || mes=="11") && (dia > 30)) {
                document.getElementById(Ncampo).focus();              
                alert("Data incorreta! O m�s especificado cont�m no m�ximo 30 dias");
                // fecharJanela();
              
                return false;
            } else {
                if ((ano%4!=0) && (mes=="02") && (dia>28)) {
                    document.getElementById(Ncampo).focus();
                    alert("Data incorreta! O m�s especificado cont�m no m�ximo 28 dias.");
                    // fecharJanela();
                    return false;
                } else{
                    if ((ano%4==0) && (mes=="02") && (dia>29)) {
                        document.getElementById(Ncampo).focus();
                        alert("Data incorreta! O m�s especificado cont�m no m�ximo 29 dias.");
                        // fecharJanela();                       
                        return false;
                    } else{
                        return true;
                    }
                }
            }
        } else {
            document.getElementById(Ncampo).focus();
            alert("Formato ou data inv�lida " + b + " (Exemplo de data: dd/mm/aaaa).");
            // fecharJanela();
            return false;
        }
    }else {
        document.getElementById(Ncampo).focus();
        alert("Formato ou data inv�lida " + b + " (Exemplo de data: dd/mm/aaaa).");
        // fecharJanela();
        return false;
    }

}

function focoTabNavigator(NCampo){
    document.getElementById("form:questionarioCliente:" + numeroLinha + ":data").focus();
}


function validar_Telefone(Ncampo){
    b = document.getElementById(Ncampo).value;
    if (b != "") {
        if (b.length != 13) {
            document.getElementById(Ncampo).focus();
            alert("Formato de telefone inv�lido!" );
            return false;
        }
    }
}

function mascaraDentroDataTable(form, campo) {
    // form:escolhaFormaPagamento:4:MovPagamentoCheque:
    // 012345678901234567890123456789012345678901234567
    var nomePadrao = 'form:escolhaFormaPagamento:4:MovPagamentoCheque:';
    var nrLinha = campo.substring(48);
    var posFim = nrLinha.indexOf(':',0);
    nrLinha = nrLinha.substring(0, posFim);
    
    var dataCompensacao_nomeCampo = nomePadrao + nrLinha + ':dataCompensacao';
    
    //alert(form[salario_nomeCampo].value);
    //alert(form[aliquotaServidor_nomeCampo].value);
    var contribuicaoServidor = 0.0;
    var percCalcServidor = parseFloat(form[aliquotaServidor_nomeCampo].value);
    var salario = parseFloat(form[salario_nomeCampo].value);
    //alert(percCalcServidor);
    //alert(form[salario_nomeCampo].value);
    if (percCalcServidor != 0) {
        contribuicaoServidor = (salario * (percCalcServidor / 100));
    }
    form[contribuicaoServidor_nomeCampo].value = contribuicaoServidor.toLocaleString(2);
    var contribuicaoPatronal = 0.0;
    var percCalcPatronal = parseFloat(form[aliquotaPatronal_nomeCampo].value);
    //alert(percCalcPatronal);
    //alert(form[salario_nomeCampo].value);
    if (percCalcPatronal != 0) {
        contribuicaoPatronal = (salario * (percCalcPatronal / 100));
    }
    form[contribuicaoPatronal_nomeCampo].value = contribuicaoPatronal.toLocaleString(2);
    var totalFinal = contribuicaoServidor + contribuicaoPatronal;
    form[totalContribuicao_nomeCampo].value = totalFinal.toLocaleString(2);
}

function validarEnter(e, idBotao){    
    if(e == null){
        return true;
    }
    var whichCode = (window.Event) ? e.which : e.keyCode;
    
    if(whichCode == 13){
        fireElement(idBotao);
        return true;
    }else{
        return true;
    }
}
/*
 *
 **/
function fireElement(id) {    
    var target = document.getElementById(id);   
    if (document.dispatchEvent) { // W3C      
        var oEvent = document.createEvent( "MouseEvents" );
        oEvent.initMouseEvent("click", true, true,window, 1, 1, 1, 1, 1, false, false, false, false, 0, target);
        target.dispatchEvent( oEvent );
    } else {
        if(document.fireEvent) { // IE               
            target.fireEvent("onclick");          
        }
    }
}
function Tecla2(objForm, strField, e) {
    //    TeclasAtalho(e);
    if(document.all) { // Internet Explorer
        var tecla = e.keyCode;
    } else if(document.layers) { // Nestcape
        var tecla = e.which;
    } else {
        var tecla = e.which;
    }
    if (tecla > 47 && tecla < 58) // numeros de 0 a 9
        return true;
    else {
        if (tecla == 0) { // backspace
            return true;
        } else if (tecla != 8) {
            return false;
        } else {
            return true;
        }
    }
}
function FormataValor(campo,tammax,teclapres) {

    var tecla = teclapres.keyCode;
    var vr = campo.value;    
    vr = vr.replace( ",", "" );
    vr = vr.replace( ".", "" );    
    tam = vr.length;

    if (tam < tammax && tecla != 8){
        tam = vr.length + 1 ;
    }

    if (tecla == 8 ){
        tam = tam - 1 ;
    }

    if ( tecla == 8 || (tecla >= 48 && tecla <= 57) || (tecla >= 96 && tecla <= 105) ){
        if ( tam <= 2 ){
            campo.value = vr ;
        }
        tam = tam - 1;
        if ( (tam > 2) && (tam <= 5) ){
            campo.value = vr.substr( 0, tam - 2 ) + '.' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 6) && (tam <= 8) ){
            campo.value = vr.substr( 0, tam - 5 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 9) && (tam <= 11) ){
            campo.value = vr.substr( 0, tam - 8 ) + '.' + vr.substr( tam - 8, 3 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 12) && (tam <= 14) ){
            campo.value = vr.substr( 0, tam - 11 ) + '.' + vr.substr( tam - 11, 3 ) + '.' + vr.substr( tam - 8, 3 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
        if ( (tam >= 15) && (tam <= 17) ){
            campo.value = vr.substr( 0, tam - 14 ) + '.' + vr.substr( tam - 14, 3 ) + '.' + vr.substr( tam - 11, 3 ) + '.' + vr.substr( tam - 8, 3 ) + '.' + vr.substr( tam - 5, 3 ) + ',' + vr.substr( tam - 2, tam ) ;
        }
    }
}


function MascaraMoeda(e){
    var objTextBox = e.element();
    var SeparadorMilesimo = '.';
    var SeparadorDecimal = ',';
    var sep = 0;
    var key = '';
    var i = j = 0;
    var len = len2 = 0;
    var strCheck = '0123456789';
    var aux = aux2 = '';
    var whichCode = (window.Event) ? e.which : e.keyCode;
	
    objTextBox=objTextBox.replace(/\D/g,"");
    if (whichCode == 13) return true;
    key = String.fromCharCode(whichCode); // Valor para o c�digo da Chave
    if (strCheck.indexOf(key) == -1) return false; // Chave inv�lida
    len = objTextBox.value.length;
    for(i = 0; i < len; i++)
	if ((objTextBox.value.charAt(i) != '0') && (objTextBox.value.charAt(i) != SeparadorDecimal)) break;
    aux = '';
    for(; i < len; i++)
	if (strCheck.indexOf(objTextBox.value.charAt(i))!=-1) aux += objTextBox.value.charAt(i);
    aux += key;
    len = aux.length;
    if (len == 0) objTextBox.value = '';
    if (len == 1) objTextBox.value = '0'+ SeparadorDecimal + '0' + aux;
    if (len == 2) objTextBox.value = '0'+ SeparadorDecimal + aux;
    if (len > 2) {
	aux2 = '';
	for (j = 0, i = len - 3; i >= 0; i--) {
            if (j == 3) {
                aux2 += SeparadorMilesimo;
                j = 0;
            }
            aux2 += aux.charAt(i);
            j++;
	}
	objTextBox.value = '';
	len2 = aux2.length;
	for (i = len2 - 1; i >= 0; i--)
            objTextBox.value += aux2.charAt(i);
	objTextBox.value += SeparadorDecimal + aux.substr(len - 2, len);
    }
    return false;
} 
	
function formatar_moeda(campo, separador_milhar, separador_decimal, tecla) {
    var sep = 0;
    var key = '';
    var i = j = 0;
    var len = len2 = 0;
    var strCheck = '0123456789';
    var aux = aux2 = '';
    var whichCode = (window.Event) ?  tecla.which : tecla.keyCode;
	
	
    if(navigator.appName.indexOf("Netscape") == -1){
        whichCode= event.keyCode;
    }
	
    if (whichCode == 13) return true; // Tecla Enter
    if (whichCode == 8) return true; // Tecla Delete
    if(whichCode == 0) return true; //Tecla TAB
	
    key = String.fromCharCode(whichCode); // Pegando o valor digitado
    if (strCheck.indexOf(key) == -1) return false; // Valor inv�lido (n�o inteiro)
	
    len = campo.value.length;
    for(i = 0; i < len; i++)
        if ((campo.value.charAt(i) != '0') && (campo.value.charAt(i) != separador_decimal)) break;
    aux = '';
	
    for(; i < len; i++)
        if (strCheck.indexOf(campo.value.charAt(i))!=-1) aux += campo.value.charAt(i);
	
    aux += key;
    len = aux.length;
    if (len == 0) campo.value = '';
    if (len == 1) campo.value = '0'+ separador_decimal + '0' + aux;
    if (len == 2) campo.value = '0'+ separador_decimal + aux;

    if (len > 2) {
        aux2 = '';

        for (j = 0, i = len - 3; i >= 0; i--) {
            if (j == 3) {
                aux2 += separador_milhar;
                j = 0;
            }
            aux2 += aux.charAt(i);
            j++;
        }

        campo.value = '';
        len2 = aux2.length;
        for (i = len2 - 1; i >= 0; i--)
            campo.value += aux2.charAt(i);
        campo.value += separador_decimal + aux. substr(len - 2, len);
    }

    return false;
}
	
function popup(URL, nomeJanela, comprimento, altura) {
    var posTopo = ((screen.height / 2) - (altura / 2));
    var posEsquerda = ((screen.width / 2) -(comprimento / 2));
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ', dependent=yes, menubar=no, toolbar=no, resizable=yes, scrollbars=yes' ;
    newwin = window.open(URL, nomeJanela, atributos);
    if(newwin.focus()){
        newwin.close();
        newwin = window.open(URL, nomeJanela, atributos);
    }
    newwin.focus();
    newwin.submit();
    return true;
}

function autoTab(input)
{
    alert();
    var tecla = event.keyCode;
    var ind = 0;
    var isNN = (navigator.appName.indexOf("Netscape")!=-1);
    var keyCode = (isNN) ? tecla.which : tecla.keyCode;
    var nKeyCode = tecla.keyCode;
    
    if(keyCode == 13){
        if (!isNN) {window.event.keyCode = 0;} // evitar o beep
        ind = getIndex(input);
        if (input.form[ind].type == 'textarea') {
            return;
        }
        ind++;
    
        input.form[ind].focus();
        if (input.form[ind].type == 'text') {
            input.form[ind].select();
        }
    }
}   

function getIndex(input)
{
    var index = -1, i = 0, found = false;
    while (i < input.form.length && index == -1)
        if (input.form[i] == input) {
            index = i;
            if (i < (input.form.length -1)) {
                if (input.form[i+1].type == 'hidden') {
                    index++;
                }
                if (input.form[i+1].type == 'button' && input.form[i+1].id == 'tabstopfalse') {
                    index++;
                }
        }
    }
    else
        i++;
    return index;
}