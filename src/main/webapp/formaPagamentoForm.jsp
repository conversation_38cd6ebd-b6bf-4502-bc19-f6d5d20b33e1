<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>

    <link href="./css/otimize.css" rel="stylesheet" type="text/css">
    <link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
    <link href="../css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
    <link href="../css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<style type="text/css">
    .notificacaoAtividades {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        -webkit-font-smoothing: subpixel-antialiased;
        background-clip: padding-box;
        background: #819aa5
        -webkit-linear-gradient(top, #96acb6,
                #5d7b89);
        box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        color: rgb(255, 255, 255) !important;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        height: 13px !important;
        line-height: normal;
        list-style-type: none;
        padding: 1px 3px !important;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0 -1px 0;
        zoom: 1;
        border-radius: 40%;
    }
    .colunaOpcoes {
        width: 120px;

    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_FormaPagamento_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_FormaPagamento_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-uma-forma-de-pagamento/"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">

            <style>
                .chk-fa-container input[type="checkbox"]+span{
                    margin-top: 4px;
                }
                .contratoSelecionado tbody tr{
                    background-color: #ffffff;
                }
                .texto-size-14 {
                    font-size: 14px !important;
                }
                .caixainfo .texto-size-14{
                    font-size: 11px !important;
                }
            </style>

            <hr style="border-color: #e6e6e6;"/>
            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%" styleClass="novaModal">

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FormaPagamento_codigo}"/>
                    <h:panelGroup>
                        <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                     value="#{FormaPagamentoControle.formaPagamentoVO.codigo}"/>
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FormaPagamento_descricao}"/>
                    <h:panelGroup>
                        <h:inputText id="descricao" size="45" maxlength="45" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{FormaPagamentoControle.formaPagamentoVO.descricao}"/>
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FormaPagamento_tipoFormaPagamento}"/>
                    <h:panelGroup>
                        <h:panelGroup rendered="#{FormaPagamentoControle.permiteAlterarTipoForma}" layout="block" styleClass="block cb-container">
                            <h:selectOneMenu disabled="#{FormaPagamentoControle.editarSomenteDescricao}"
                                             id="tipoFormaPagamento" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento}">
                                <f:selectItems
                                        value="#{FormaPagamentoControle.listaSelectItemTipoFormaPagamentoFormaPagamento}"/>
                                <a4j:support event="onchange" focus="tipoFormaPagamento"
                                             action="#{FormaPagamentoControle.redesenhaTela}" reRender="form"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <h:outputText  rendered="#{!FormaPagamentoControle.permiteAlterarTipoForma}" title="Tipo não pode ser alterado pois já existe pagamento lançado com essa forma de pagamento" styleClass="tituloCampos" value="#{FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento_Apresentar}"/>
                        <h:message for="tipoFormaPagamento" styleClass="mensagemDetalhada"/>

                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FormaPagamento_convenio}"
                                  rendered="#{FormaPagamentoControle.exibirConvenio}"/>
                    <h:panelGroup rendered="#{FormaPagamentoControle.exibirConvenio}">
                        <h:panelGroup layout="block" styleClass="block cb-container">
                            <h:selectOneMenu disabled="#{FormaPagamentoControle.editarSomenteDescricao}" id="convenio"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.convenioCobrancaVO.codigo}">
                                <f:selectItems value="#{FormaPagamentoControle.listaSelectItemConvenioCobrancaApresentar}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <a4j:commandLink id="atualizar_convenio" style="margin-left:5px" action="#{FormaPagamentoControle.montarListaTodosConvenios}"
                                           ajaxSingle="true" reRender="form:convenio" styleClass="pure-button pure-button-primary">
                            <i class="fa-icon-refresh"></i>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Tipo Convênio Cobrança (Extrato do aluno):"
                                  rendered="#{FormaPagamentoControle.integraProtheus}"/>
                    <h:panelGroup layout="block" styleClass="block cb-container"
                                  rendered="#{FormaPagamentoControle.integraProtheus}">
                        <h:selectOneMenu id="tipoConvenioCobranca" styleClass="form" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         value="#{FormaPagamentoControle.formaPagamentoVO.tipoConvenioCobranca}">
                            <f:selectItems value="#{FormaPagamentoControle.listaSelectItemTipoConvenioCobranca}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_formaPagamento_gerarPontos}"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarGerarPontos}"/>
                    <h:panelGroup layout="block" styleClass="chk-fa-container"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarGerarPontos}">
                        <h:selectBooleanCheckbox id="gerarPontos" styleClass="form"
                                                 value="#{FormaPagamentoControle.formaPagamentoVO.gerarPontos}">
                            <a4j:support reRender="form" event="onclick" status="false"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_formaPagamento_tipoParceiro}"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarTipoParceiro}"/>
                    <h:panelGroup layout="block" styleClass="block cb-container"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarTipoParceiro}">
                        <h:selectOneMenu id="tipoParceiro" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{FormaPagamentoControle.formaPagamentoVO.tipoParceiro.id}">
                            <f:selectItems
                                    value="#{FormaPagamentoControle.listaSelectItemTipoParceiro}"/>
                            <a4j:support event="onchange" focus="@this"
                                         action="#{FormaPagamentoControle.redesenhaTela}" reRender="form"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarDefaultRecorrencia}"
                                  value="#{msg_aplic.prt_FormaPagamento_defaultRecorrencia}"/>
                    <h:panelGroup rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarDefaultRecorrencia}">
                        <h:panelGroup layout="block" styleClass="chk-fa-container">
                            <h:selectBooleanCheckbox disabled="#{FormaPagamentoControle.editarSomenteDescricao}"
                                                     id="defaultRecorrencia" styleClass="form"

                                                     value="#{FormaPagamentoControle.formaPagamentoVO.defaultRecorrencia}">
                                <a4j:support reRender="form" event="onclick" status="false"/>
                            </h:selectBooleanCheckbox>
                            <span/>
                        </h:panelGroup>

                </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCampoNSU}"
                                  value="Cartão Débito Online:"/>

                    <h:panelGroup layout="block" styleClass="chk-fa-container" rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCampoNSU}">
                        <h:selectBooleanCheckbox id="cartaoDebitoOnline" styleClass="form"
                                                 value="#{FormaPagamentoControle.formaPagamentoVO.cartaoDebitoOnline}">
                            <a4j:support reRender="form" event="onclick" status="false"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </h:panelGroup>


                    <h:outputText styleClass="tituloCampos" value="Tipo Cartão Débito:"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.cartaoDebitoOnline}"/>
                    <h:panelGroup id="dadosTipoDebitoOnline"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.cartaoDebitoOnline}">
                        <h:panelGroup layout="block" styleClass="block cb-container">
                            <h:selectOneMenu id="tipoDebitoOnline"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.tipoDebitoOnline}">
                                <f:selectItems value="#{FormaPagamentoControle.listaTipoDebitoOnlineEnum}"/>
                                <a4j:support reRender="form" event="onchange" status="false"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value=""
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCamposMerchant}"/>
                    <h:panelGroup rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCamposMerchant}">
                        <h:panelGrid columns="2" rowClasses="linhaImpar" columnClasses="classEsquerda, classDireita"
                                     width="100%">
                            <h:outputText styleClass="tituloCampos" value="MerchantId"/>
                            <h:inputText id="merchantid" size="45" styleClass="form"
                                         value="#{FormaPagamentoControle.formaPagamentoVO.merchantid}"/>
                            <h:outputText styleClass="tituloCampos" value="MerchantKey"/>
                            <h:inputText id="merchantkey" size="45" styleClass="form"
                                         value="#{FormaPagamentoControle.formaPagamentoVO.merchantkey}"/>
                        </h:panelGrid>

                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCodigoAutorizacao}"
                                  value="#{msg_aplic.prt_FormaPagamento_exigeCodAutorizacao}"/>
                    <h:panelGroup rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCodigoAutorizacao}">
                        <h:panelGroup layout="block" styleClass="chk-fa-container">
                            <h:selectBooleanCheckbox disabled="#{FormaPagamentoControle.editarSomenteDescricao}"
                                                     id="exigeCodAutorizacao" styleClass="form"

                                                     value="#{FormaPagamentoControle.formaPagamentoVO.exigeCodAutorizacao}">
                                <a4j:support reRender="form" event="onclick" status="false"/>
                            </h:selectBooleanCheckbox>
                            <span/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <c:if test="${FormaPagamentoControle.exibirDefaultDco}">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FormaPagamento_defaultDco}"
                                      rendered="#{!FormaPagamentoControle.formaPagamentoVO.parceiroFidelidade && FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento != 'CC'}"/>
                        <h:panelGroup rendered="#{!FormaPagamentoControle.formaPagamentoVO.parceiroFidelidade && FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento != 'CC'}">
                            <h:panelGroup layout="block" styleClass="chk-fa-container">
                                <h:selectBooleanCheckbox disabled="#{FormaPagamentoControle.editarSomenteDescricao}"
                                                         id="defaultdco" styleClass="form"
                                                         value="#{FormaPagamentoControle.formaPagamentoVO.defaultDCO}"/>
                                <span/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </c:if>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FormaPagamento_ativo}" rendered="#{!(FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento == 'CC')}"/>

                    <h:panelGroup  rendered="#{!(FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento == 'CC')}" layout="block" styleClass="chk-fa-container">
                        <h:selectBooleanCheckbox  disabled="#{FormaPagamentoControle.editarSomenteDescricao}" id="ativo"
                                                 styleClass="form"
                                                 value="#{FormaPagamentoControle.formaPagamentoVO.ativo}"/>
                        <span/>
                    </h:panelGroup>

                    <h:outputText rendered="#{FormaPagamentoControle.apresentarSomenteFinanceiro}"
                                  styleClass="tituloCampos" value="#{msg_aplic.prt_FormaPagamento_somenteFinanceiro}"/>

                    <h:panelGroup rendered="#{FormaPagamentoControle.apresentarSomenteFinanceiro}">
                    <h:panelGroup layout="block" styleClass="chk-fa-container">
                        <h:selectBooleanCheckbox id="somenteFinanceiro" styleClass="form"
                                                 value="#{FormaPagamentoControle.formaPagamentoVO.somenteFinanceiro}"
                                                 disabled="#{FormaPagamentoControle.tipoLote || FormaPagamentoControle.editarSomenteDescricao}"/>
                        <span/>
                    </h:panelGroup>
                    </h:panelGroup>

                    <h:outputText rendered="#{EmpresaControle.usuarioLogado.administrador}" styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_FormaPagamento_diasCompensacaoCartaoCredito}"/>
                    <h:panelGroup rendered="#{EmpresaControle.usuarioLogado.administrador}">
                        <h:inputText id="diasCompensacaoCartaoCredito" size="1" maxlength="3" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{FormaPagamentoControle.formaPagamentoVO.diasCompensacaoCartaoCredito}"/>
                        <h:message for="diasCompensacaoCartaoCredito" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCampoCompensacao}"
                                  value="#{msg_aplic.prt_FormaPagamento_compensacaoDiasUteis}"/>
                    <h:panelGroup layout="block" styleClass="chk-fa-container" rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCampoCompensacao}">
                        <h:selectBooleanCheckbox id="compensacaoDiasUteis"
                                                 styleClass="form"
                                                 value="#{FormaPagamentoControle.formaPagamentoVO.compensacaoDiasUteis}"/>
                        <span/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCampoNSU}"
                                  value="#{msg_aplic.prt_FormaPagamento_apresentarNSU}"/>

                    <h:panelGroup layout="block" styleClass="chk-fa-container" rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCampoNSU}">
                        <h:selectBooleanCheckbox id="apresentarnsu" styleClass="form"
                                                 value="#{FormaPagamentoControle.formaPagamentoVO.apresentarNSU}"/>
                        <span/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Cor:"/>
                    <rich:colorPicker colorMode="hex" value="#{FormaPagamentoControle.formaPagamentoVO.cor}">
                    </rich:colorPicker>

                    <h:outputText id="titulopix" styleClass="tituloCampos tooltipster "
                                  rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCampoTaxaPix}"
                                  value="Taxa Pix(#{FormaPagamentoControle.formaPagamentoVO.labelSimboloTaxaPix})"
                                  title="O valor configurado nesse campo será utilizado (deduzido) quando Movimentar no Recebíveis do Financeiro um valor recebido de pix para uma conta do Tipo Banco. </br>
                                  Você pode configurar o valor por porcentagem ou por valor absoluto."/>
                    <h:panelGroup layout="block" styleClass="chk-fa-container" rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCampoTaxaPix}">
                        <h:inputText id="taxapix" size="6" maxlength="6"
                                     style="float: left; margin-top: 3px; margin-left: 5px"
                                     onkeyup="return porcentagem(this);"
                                     onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                     value="#{FormaPagamentoControle.formaPagamentoVO.taxaPix}">
                        </h:inputText>
                        <h:outputText rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCampoTaxaPix}"
                                      styleClass="tituloCampos"
                                      style="display: inline-block; margin-left: 15px; margin-top: 8px;"
                                      value="Valor absoluto: "/>

                        <h:panelGroup rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarCampoTaxaPix}"
                                      layout="block" styleClass="chk-fa-container" style="display: inline">
                            <h:selectBooleanCheckbox  styleClass="form"
                                                      value="#{FormaPagamentoControle.formaPagamentoVO.taxaPixValorAbsoluto}">
                            <a4j:support reRender="titulopix" event="onclick" status="false"/>
                            </h:selectBooleanCheckbox>
                            <span style="display: inline-flex; margin-left: 2px; margin-top: 5px"></span/>
                        </h:panelGroup>
                    </h:panelGroup>

                </h:panelGrid>

                <%-- PINPAD--%>
                <h:panelGroup layout="block" styleClass="painelDadosAluno" style="width: calc(98.5% - 10px); height: auto; font-family: Arial,Verdana,sans-serif;"
                              rendered="#{FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento eq 'CA' or FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento eq 'CD'}"
                              id="pinPadFormaPgto" >
                    <div class="tituloPainelAluno">
                        <h:outputText styleClass="negrito cinzaEscuro pl20"
                                      value="Configurações de pinpad"
                                      style="line-height: 40px;font-size: 14px !important;"/>

                    </div>

                    <div class="conteudoDadosCliente" style="min-height: 50px; text-align: center">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" id="pinpadelecionadafpgto"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     styleClass="novaModal">

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FormaPagamento_pinpad}:"/>
                            <h:panelGroup layout="block" styleClass="block cb-container">
                                <h:selectOneMenu id="pinpad" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{FormaPagamentoControle.formaPagamentoVO.pinpad.pinpad}">
                                    <f:selectItems
                                            value="#{FormaPagamentoControle.pinpads}"/>
                                    <a4j:support reRender="form:pinPadFormaPgto" event="onchange"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <c:if test="${FormaPagamentoControle.formaPagamentoVO.pinpad.CAPPTA}">
                                <h:outputText styleClass="tituloCampos" value="Descrição:"/>
                                <h:inputText id="descricaoPinpadCappta" size="20" maxlength="20" styleClass="form"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.pinpad.descricao}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="PDV:"/>
                                <h:inputText id="serialNumberCappta" size="20" maxlength="50" styleClass="form"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.pinpad.pdvPinpad}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="CNPJ:"/>
                                <h:inputText id="cnpjCappta" size="20" maxlength="50" styleClass="form"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.pinpad.cnpjPinpad}"/>

                                <h:outputText styleClass="tituloCampos" value="Receber somente via Pinpad:"/>
                                <h:selectBooleanCheckbox
                                        value="#{FormaPagamentoControle.formaPagamentoVO.receberSomenteViaPinPad}"/>

                                <h:outputText styleClass="tituloCampos" value="Número máximo de parcelas via Pinpad:"/>
                                <h:inputText onkeypress="return mascara(this.form, this.id, '99', event);"
                                             onkeyup="somenteNumeros(this);"
                                             id="nrMaxParcelasPinpad" size="2" maxlength="2"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.nrMaxParcelasPinpad}"/>

                                <h:outputText styleClass="tituloCampos" value="Empresa:"/>
                                <h:panelGroup layout="block" styleClass="block cb-container">
                                    <h:selectOneMenu
                                            value="#{FormaPagamentoControle.formaPagamentoVO.pinpad.empresa.codigo}"
                                            styleClass="form">
                                        <f:selectItems value="#{FormaPagamentoControle.empresasPinpadCappta}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                            </c:if>

                            <c:if test="${FormaPagamentoControle.formaPagamentoVO.pinpad.GEOIT}">
                                <h:outputText styleClass="tituloCampos" value="PosID:"/>
                                <h:inputText id="posIDGeoitd" size="10" maxlength="10" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             onkeypress="return mascara(this.form, this.id, '99999999', event);"
                                             onkeyup="somenteNumeros(this);"
                                             styleClass="form"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.posIDGeoitd}"/>

                                <h:outputText styleClass="tituloCampos" value="SystemID:"/>
                                <h:inputText id="systemIdGeoitd" maxlength="200" size="50" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.systemIdGeoitd}"/>
                            </c:if>

                            <c:if test="${FormaPagamentoControle.formaPagamentoVO.pinpad.stoneConnect || FormaPagamentoControle.formaPagamentoVO.pinpad.getCard}">
                                <h:outputText styleClass="tituloCampos" value="Descrição:"/>
                                <h:inputText id="descricaoPinpad" size="20" styleClass="form"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.pinpad.descricao}"/>

                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{FormaPagamentoControle.formaPagamentoVO.pinpad.stoneConnect}"
                                              value="Serial Number:"/>
                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{FormaPagamentoControle.formaPagamentoVO.pinpad.getCard}"
                                              value="PDV:"/>
                                <h:inputText id="serialNumberStone" size="20" styleClass="form"
                                             value="#{FormaPagamentoControle.formaPagamentoVO.pinpad.pdvPinpad}"/>

                                <c:if test="${FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento eq 'CA'}">
                                    <h:outputText styleClass="tituloCampos" value="Número máximo de parcelas:"/>
                                    <h:inputText onkeypress="return mascara(this.form, this.id, '99', event);"
                                                 onkeyup="somenteNumeros(this);"
                                                 id="nrMaxParcelasStoneConnect" size="2" maxlength="2"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{FormaPagamentoControle.formaPagamentoVO.pinpad.nrMaxParcelas}"/>
                                </c:if>

                                <h:outputText styleClass="tituloCampos" value="Convênio de Cobrança:"/>
                                <h:panelGroup layout="block" styleClass="block cb-container">
                                    <h:selectOneMenu
                                            value="#{FormaPagamentoControle.formaPagamentoVO.pinpad.convenioCobranca.codigo}"
                                            styleClass="form">
                                        <f:selectItems value="#{FormaPagamentoControle.listaSelectItemConvenioCobrancaPinPad}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Empresa:"/>
                                <h:panelGroup layout="block" styleClass="block cb-container">
                                    <h:selectOneMenu
                                            value="#{FormaPagamentoControle.formaPagamentoVO.pinpad.empresa.codigo}"
                                            styleClass="form">
                                        <f:selectItems value="#{FormaPagamentoControle.empresasPinpad}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </c:if>
                        </h:panelGrid>

                        <a4j:commandButton id="adicionarPinpad"
                                           rendered="#{FormaPagamentoControle.formaPagamentoVO.apresentarAdicionarPinPad}"
                                           action="#{FormaPagamentoControle.adicionarPinpad}"
                                           oncomplete="#{FormaPagamentoControle.mensagemNotificar}"
                                           reRender="form"
                                           styleClass="botoes nvoBt botaoPrimarioGrande "
                                           accesskey="9"
                                           value="Adicionar"/>
                    </div>

                    <h:dataTable width="100%"
                                 styleClass="tblHeaderLeft contratoSelecionado"
                                 columnClasses="colunaAlinhamento, colunaAlinhamento"
                                 rendered="#{not empty FormaPagamentoControle.formaPagamentoVO.listaPinPad}"
                                 value="#{FormaPagamentoControle.formaPagamentoVO.listaPinPad}" var="pinpe">

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="DESCRIÇÃO" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:outputText value="#{pinpe.descricao}" styleClass="texto-size-14 cinza upper"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="PINPAD" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:outputText value="#{pinpe.opcoesPinpadEnum.nome}" styleClass="texto-size-14 cinza upper"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="PDV/Serial Number" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:outputText value="#{pinpe.pdvPinpad}" styleClass="texto-size-14 cinza"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="CNPJ" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:outputText value="#{pinpe.cnpjPinpad}" styleClass="texto-size-14 cinza"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="AUTOATENDIMENTO" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:outputText value="#{(pinpe.autoAtendimento) ? 'SIM' : 'NÃO'}" styleClass="texto-size-14 cinza"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="CONVÊNIO" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:outputText value="#{pinpe.convenioCobranca.descricao}" styleClass="texto-size-14 cinza"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="EMPRESA" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:outputText value="#{pinpe.empresa.nome}" styleClass="texto-size-14 cinza"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>


                            <a4j:commandButton id="removerPinPad"
                                               action="#{FormaPagamentoControle.removerPinPad}"
                                               value="Remover"
                                               oncomplete="Richfaces.showModalPanel('mdlMensagemGenerica');"
                                               style="vertical-align: middle;"
                                               reRender="mdlMensagemGenerica"
                                               styleClass="botoes nvoBt btSec bt"/>
                        </h:column>
                    </h:dataTable>


                </h:panelGroup>

                <%-- EMPRESAS--%>
                <h:panelGroup layout="block" styleClass="painelDadosAluno"
                              style="width: calc(98.5% - 10px); height: auto;
                               font-family: Arial,Verdana,sans-serif;"
                               id="empresasFormaPgto"
                              rendered="#{FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento != 'CC'}">
                    <div class="tituloPainelAluno">
                        <h:outputText styleClass="negrito cinzaEscuro pl20"
                                      value="Configurações de empresas"
                                      style="line-height: 40px;font-size: 14px !important;"/>

                    </div>

                    <div class="conteudoDadosCliente" style="min-height: 50px; text-align: center">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" id="empresaselecionadafpgto"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     styleClass="novaModal">

                            <h:outputText styleClass="tituloCampos" value="Empresa:"/>
                            <h:panelGroup layout="block" styleClass="block cb-container">
                                <h:selectOneMenu value="#{FormaPagamentoControle.empresaSelecionada}"
                                                 styleClass="form">
                                    <f:selectItems value="#{FormaPagamentoControle.empresas}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="Conta de destino"/>
                            <h:panelGroup>
                                <h:panelGroup layout="block" styleClass="block cb-container">
                                    <h:selectOneMenu id="contadestino"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                     value="#{FormaPagamentoControle.contaSelecionada}">
                                        <f:selectItems value="#{FormaPagamentoControle.contas}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <a4j:commandLink id="atualizar_contas" style="margin-left:5px"
                                                   action="#{FormaPagamentoControle.montarListaSelectItemContas}"
                                                   ajaxSingle="true" reRender="form:contadestino" styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-refresh"></i>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGrid>

                        <a4j:commandButton id="adicionarCfgEmpresa"
                                           action="#{FormaPagamentoControle.adicionarEmpresa}"
                                           reRender="form"
                                           styleClass="botoes nvoBt botaoPrimarioGrande "
                                           accesskey="9"
                                           value="Adicionar nova empresa">

                        </a4j:commandButton>


                        <h:dataTable width="100%"
                                     styleClass="tblHeaderLeft contratoSelecionado"
                                     columnClasses="colunaAlinhamento, colunaAlinhamento"
                                     rendered="#{not empty FormaPagamentoControle.formasEmpresas}"
                                     value="#{FormaPagamentoControle.formasEmpresas}" var="empForma">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="EMPRESA" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:outputText value="#{empForma.empresa.nome}" styleClass="texto-size-14 cinza"/>
                            </h:column>


                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="CONTA DE DESTINO" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:outputText value="#{empForma.contaDestino.descricaoBBApresentar}" styleClass="texto-size-14 cinza"/>
                            </h:column>


                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>

                                <a4j:commandButton id="selecionarTaxaEmpresa"
                                                   action="#{FormaPagamentoControle.lancarTaxasEmpresa}"
                                                   value="Taxas"
                                                   rendered="#{FormaPagamentoControle.apresentarTaxaCartaoDebito or FormaPagamentoControle.apresentarTaxaCartaoCredito}"
                                                   oncomplete="#{FormaPagamentoControle.msgAlert}"
                                                   style="vertical-align: middle"
                                                   reRender="mdllancartaxas"
                                                   styleClass="botoes nvoBt botaoPrimarioGrande "
                                                   accesskey="9"/>

                                <a4j:commandButton id="selecionarTaxaEmpresaBoleto"
                                                   action="#{FormaPagamentoControle.lancarTaxasEmpresa}"
                                                   value="Taxa Boleto"
                                                   rendered="#{FormaPagamentoControle.apresentarTaxaBoleto}"
                                                   oncomplete="#{FormaPagamentoControle.msgAlert}"
                                                   style="vertical-align: middle"
                                                   reRender="mdllancartaxasboleto"
                                                   styleClass="botoes nvoBt botaoPrimarioGrande "
                                                   accesskey="9"/>

                                <a4j:commandButton id="removerEmpresa"
                                                   action="#{FormaPagamentoControle.removerEmpresa}"
                                                   value="Remover "
                                                   oncomplete="Richfaces.showModalPanel('mdlMensagemGenerica');"
                                                   style="vertical-align: middle;"
                                                   reRender="mdlMensagemGenerica"
                                                   styleClass="botoes nvoBt btSec bt"/>
                            </h:column>
                        </h:dataTable>
                    </div>
                </h:panelGroup>


                <%-- TAXAS DO CARTÃO DE DÉBITO--%>
                <h:panelGroup layout="block" styleClass="painelDadosAluno novaModal" style="width: calc(98.5% - 10px); height: auto; font-family: Arial,Verdana,sans-serif;"
                              rendered="#{FormaPagamentoControle.apresentarTaxaCartaoDebito}"
                                id="panelTaxaCartaoVigenciaDebito">
                    <div class="tituloPainelAluno">
                        <h:outputText styleClass="negrito cinzaEscuro pl20"
                                      value="Taxa Cartão de Débito"
                                      style="line-height: 40px;font-size: 14px !important;"/>
                    </div>
                    <div class="conteudoDadosCliente" style="min-height: 50px; text-align: center">
                        <h:outputText style="color: red; display: block; margin: 20px;"
                                      value="* Ao modificar a vigência da taxa, as operações já realizadas no Financeiro não serão modificadas."/>
                        <a4j:commandButton id="adicionarTaxaVigenciaDebito"
                                           action="#{FormaPagamentoControle.adicionarNovaTaxaDebito}"
                                           reRender="panelTaxaCartaoVigenciaDebito" value="Adicionar nova taxa"
                                           styleClass="botoes nvoBt botaoPrimarioGrande " accesskey="5"/>

                        <h:dataTable id="dtTaxasVigenciaDebito" width="100%"  styleClass="tblHeaderLeft contratoSelecionado"
                                     rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                                     value="#{FormaPagamentoControle.taxaCartaoDebito}"
                                     rendered="#{not empty FormaPagamentoControle.taxaCartaoDebito}" var="taxaCartaoDebito">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Taxa" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:inputText size="6" maxlength="6" id="taxaVigencia"
                                             onkeyup="return porcentagem(this);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{taxaCartaoDebito.taxa}">
                                </h:inputText>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Adquirente" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:panelGroup layout="block" styleClass="block cb-container">
                                    <h:selectOneMenu value="#{taxaCartaoDebito.adquirenteVO.codigo}"
                                                     styleClass="form">
                                        <f:selectItems value="#{FormaPagamentoControle.adquirentes}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Bandeira" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:panelGroup layout="block" styleClass="block cb-container">
                                    <h:selectOneMenu value="#{taxaCartaoDebito.bandeira.codigo}"
                                                     styleClass="form">
                                        <f:selectItems value="#{FormaPagamentoControle.operadoras}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </h:column>


                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Vigência Inicial" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:panelGroup styleClass="dateTimeCustom" >
                                    <rich:calendar datePattern="dd/MM/yyyy"
                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                   inputSize="8"
                                                   showWeekDaysBar="false"
                                                   showWeeksBar="false"
                                                   enableManualInput="true"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   styleClass="form"
                                                   direction="top-left"
                                                   value="#{taxaCartaoDebito.vigenciaInicial}"/>
                                </h:panelGroup>
                            </h:column>


                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Vigência Final" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:panelGroup styleClass="dateTimeCustom" >
                                    <rich:calendar datePattern="dd/MM/yyyy"
                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                   inputSize="8"
                                                   showWeekDaysBar="false"
                                                   showWeeksBar="false"
                                                   enableManualInput="true"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   styleClass="form"
                                                   direction="top-left"
                                                   value="#{taxaCartaoDebito.vigenciaFinal}"/>
                                </h:panelGroup>
                            </h:column>

                            <c:if test="${FormaPagamentoControle.configuracaoSistema.utilizarServicoSesiSC}">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  styleClass="tituloCampos" value="Produto Venda Sesi"/>
                                    </f:facet>
                                    <h:inputText  size="30" maxlength="100" id="produtoVendaSesi"
                                                 value="#{taxaCartaoDebito.produtoVendaSesi}">
                                    </h:inputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  styleClass="tituloCampos" value="Tipo Documento Sesi"/>
                                    </f:facet>
                                    <h:inputText  size="30" maxlength="100" id="tipodocumentoSesi"
                                                  value="#{taxaCartaoDebito.tipodocumentoSesi}">
                                    </h:inputText>
                                </h:column>
                            </c:if>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>

                                <a4j:commandLink action="#{FormaPagamentoControle.removerTaxaDebito}"
                                                 reRender="panelTaxaCartaoVigenciaDebito"
                                                 style="vertical-align: middle; display: initial; "
                                                 styleClass="botoes nvoBt btSec " accesskey="7">
                                    <i class="fa-icon-trash"></i>
                                </a4j:commandLink>


                            </h:column>
                        </h:dataTable>

                    </div>

                </h:panelGroup>


                <%-- CARTÃO DE CRÉDITO--%>

                <h:panelGroup layout="block" styleClass="painelDadosAluno"
                              rendered="#{FormaPagamentoControle.apresentarTaxaCartaoCredito}" id="pnlTaxas"
                              style="width: calc(98.5% - 10px); height: auto; font-family: Arial,Verdana,sans-serif;">
                    <div class="tituloPainelAluno">
                        <h:outputText styleClass="negrito cinzaEscuro pl20"
                                      value="Taxa Cartão de Crédito - Geral"
                                      style="line-height: 40px;font-size: 14px !important;"/>
                    </div>
                    <div class="conteudoDadosCliente novaModal" style="min-height: 50px; text-align: center">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     rendered="#{empty FormaPagamentoControle.taxaCartaoVigencia}"
                                     columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText styleClass="tituloCampos"
                                          value="Antecipação Automática:"/>
                            <h:panelGroup layout="block" styleClass="chk-fa-container">
                            <h:selectBooleanCheckbox id="compensacaoPorTaxa"
                                                     styleClass="form tooltipster"
                                                     title="Ao ativar essa configuração, todas as parcelas geradas com essa taxa irão compensar na mesma data, no prazo que estiver configurado no campo \"Dias Compensação\"."
                                                     value="#{FormaPagamentoControle.compensacaoPorTaxa}">
                                <a4j:support reRender="pnlTaxas" event="onclick"/>
                            </h:selectBooleanCheckbox>
                                <span style="float: left; margin-top: 3px; margin-left: 5px"/>
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="* Nr. Parcelas:"/>
                            <h:panelGroup id="grupoNumeroParcelas" layout="block">
                                <h:inputText id="nrMeses" size="5" maxlength="3" onblur="blurinput(this);"
                                             style="float: left; margin-top: 3px; margin-left: 5px"
                                             onfocus="focusinput(this);" styleClass="form"
                                             onkeypress="return mascara(this.form, this.id , '99', event);"
                                             value="#{FormaPagamentoControle.taxaCartaoVO.nrmeses}">
                                </h:inputText>
                                <h:panelGroup>
                                    <a4j:commandLink id="adicionarNumeroParcelasNaListaSelecionado" action="#{FormaPagamentoControle.adicionarNumeroParcelaNaLista}"
                                                       reRender="pnlTaxas, nrMeses, nrParcelaNaLista"
                                                       style="float: left; margin-left: 5px; margin-top: 9px"
                                                        styleClass="fa-icon-plus-sign texto-size-20"/>

                                    <h:panelGrid id="quantidadeNumeroParcelas" style="float: left; margin-top: 5px; margin-left: 5px">
                                        <h:panelGroup layout="block">
                                            <h:outputText styleClass="tooltipster" style="color: #0090FF;" title="#{FormaPagamentoControle.quantidadeNumeroParcelaNaListaApresentar}" value="#{FormaPagamentoControle.tamanhoListaQuantidadeNumeroParcela}">
                                                    </h:outputText>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                    <a4j:commandLink id="limparNumeroParcelasSelecionadas"
                                                       styleClass="fa-icon-trash texto-size-20"
                                                       style="float: left; margin-top: 9px; margin-left: 5px"
                                                       reRender="pnlTaxas, nrMeses, grupoNumeroParcelas"
                                                       action="#{FormaPagamentoControle.limparQuantidadeNumeroParcelaNaLista}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_FormaPagamento_taxaCartao}"/>
                            <h:inputText id="taxa" size="6" maxlength="6"
                                         style="float: left; margin-top: 3px; margin-left: 5px"
                                         onkeyup="return porcentagem(this);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{FormaPagamentoControle.taxaCartaoVO.taxa}">
                            </h:inputText>
                            <c:if test="${FormaPagamentoControle.compensacaoPorTaxa}">
                                <h:outputText styleClass="tituloCampos" value="* Dias Compensação:"/>
                                <h:inputText id="nrDiasCompensacaoTaxa" size="3" maxlength="3"
                                             style="float: left; margin-top: 3px; margin-left: 5px"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{FormaPagamentoControle.nrDiasCompensacaoPorTaxa}">
                                </h:inputText>
                            </c:if>
                            <h:outputText styleClass="tituloCampos"
                                          value="* #{msg_aplic.prt_FormaPagamento_adquirente}:"/>
                            <h:panelGroup id="grupoAdquirentes">
                                <h:panelGroup layout="block" styleClass="block cb-container"
                                              style="float: left; margin-top: auto; margin-left: 5px">
                                    <h:selectOneMenu id="adquirente"
                                                     value="#{FormaPagamentoControle.taxaCartaoVO.adquirenteVO.codigo}"
                                                     styleClass="form">
                                        <f:selectItems value="#{FormaPagamentoControle.adquirentes}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <h:panelGroup id="quantidadeAdquirente">
                                    <a4j:commandLink id="adicionarAdquirenteNaListaSelecionado"
                                                       style="float: left; margin-left: 5px; margin-top: 9px"
                                                       styleClass="fa-icon-plus-sign texto-size-20"
                                                       action="#{FormaPagamentoControle.adicionarAdquirenteNaLista}"
                                                       reRender="pnlTaxas, adquirente, adquirenteNaLista"
                                                       />

                                    <h:panelGrid id="quantidadeAdquitente" style="float: left; margin-top: 5px; margin-left: 5px">
                                        <h:panelGroup layout="block">
                                            <h:outputText styleClass="tooltipster" style="color: #0090FF;" title="#{FormaPagamentoControle.adquirenteSelecionadasApresentar}" value="#{FormaPagamentoControle.tamanhoListaAdquirentes}">
                                            </h:outputText>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                    <h:outputText
                                                  rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                                  value="Permitir que a renovação automática renove o plano com desconto"
                                                  title="Ao selecionar esta opção, a renovação automática vai renovar os planos com os respectivos descontos (caso tenha desconto) que foram dados no ato da compra do plano"/>

                                    <a4j:commandLink id="limparAdquirenteSelecionadas"
                                                       styleClass="fa-icon-trash texto-size-20"
                                                       style="float: left; margin-top: 9px; margin-left: 5px"
                                                       reRender="pnlTaxas, adquirente, grupoAdquirente"
                                                       action="#{FormaPagamentoControle.limparAdquirentesNaLista}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_FormaPagamento_bandeira}:"/>
                            <h:panelGroup id="grupoOperadoras">
                                <h:panelGroup layout="block" styleClass="block cb-container"
                                              style="float: left; margin-top: auto; margin-left: 5px">
                                    <h:selectOneMenu id="operadoras"
                                                     value="#{FormaPagamentoControle.taxaCartaoVO.bandeira.codigo}"
                                                     styleClass="form">
                                        <f:selectItems value="#{FormaPagamentoControle.operadoras}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <a4j:commandLink id="adicionarbandeiraNaListaSelecionado"
                                                       action="#{FormaPagamentoControle.adicionarBanceiraNaLista}"
                                                       reRender="pnlTaxas, operadoras, operadoraNaLista"
                                                       style="float: left; margin-left: 5px; margin-top: 9px"
                                                       styleClass="fa-icon-plus-sign texto-size-20" />

                                    <h:panelGrid id="quantidadeBandeira" style="float: left; margin-top: 5px; margin-left: 5px">
                                        <h:panelGroup layout="block">
                                            <h:outputText styleClass="tooltipster" style="color: #0090FF;" title="#{FormaPagamentoControle.bandeiraSelecionadasApresentar}" value="#{FormaPagamentoControle.tamanhoListaBandeira}">
                                            </h:outputText>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                    <a4j:commandLink id="limparBandeirasSelecionadas"
                                                       reRender="pnlTaxas, operadoras, grupoOperadoras"
                                                       styleClass="fa-icon-trash texto-size-20"
                                                       style="float: left; margin-top: 9px; margin-left: 5px"
                                                       action="#{FormaPagamentoControle.limparBandeiraNaLista}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos"
                                          value="* #{msg_aplic.prt_FormaPagamento_vigenciaInicial}"/>
                            <h:panelGroup styleClass="dateTimeCustom" style="float: left; margin-top: 3px; margin-left: 5px">
                                <rich:calendar datePattern="dd/MM/yyyy" id="vigenciaInicialTaxa"
                                               direction="top-right"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               inputSize="8"
                                               showWeekDaysBar="false"
                                               showWeeksBar="false"
                                               enableManualInput="true"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               styleClass="form"
                                               value="#{FormaPagamentoControle.taxaCartaoVO.vigenciaInicial}"/>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos"
                                          value="* #{msg_aplic.prt_FormaPagamento_vigenciaFinal}"/>
                            <h:panelGroup styleClass="dateTimeCustom" style="float: left; margin-top: 3px; margin-left: 5px">
                                <rich:calendar datePattern="dd/MM/yyyy" id="vigenciaFinalTaxa"
                                               direction="top-right"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               inputSize="8"
                                               showWeekDaysBar="false"
                                               showWeeksBar="false"
                                               enableManualInput="true"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               styleClass="form"
                                               value="#{FormaPagamentoControle.taxaCartaoVO.vigenciaFinal}"/>
                            </h:panelGroup>
                            <c:if test="${FormaPagamentoControle.configuracaoSistema.utilizarServicoSesiSC}">
                                <h:outputText  styleClass="tituloCampos" value="Produto Venda Sesi:"/>
                                <h:inputText  size="30" maxlength="100" id="produtoVendaSesi"
                                                  value="#{FormaPagamentoControle.taxaCartaoVO.produtoVendaSesi}"/>

                                <h:outputText  styleClass="tituloCampos" value="Tipo Documento Sesi:"/>
                                <h:inputText  size="30" maxlength="100" id="tipodocumentoSesi"
                                                  value="#{FormaPagamentoControle.taxaCartaoVO.tipodocumentoSesi}"/>
                            </c:if>
                        </h:panelGrid>

                        <a4j:commandButton action="#{FormaPagamentoControle.adicionarTaxa}"
                                           rendered="#{empty FormaPagamentoControle.taxaCartaoVigencia}"
                                           reRender="pnlTaxas,pnlMsg" value="#{msg_bt.btn_adicionar}"
                                           styleClass="botoes nvoBt botaoPrimarioGrande " accesskey="5"/>


                        <h:dataTable id="dtTaxas" width="100%"
                                     rowClasses="linhaImpar, linhaPar" styleClass="tblHeaderLeft contratoSelecionado"
                                     rendered="#{empty FormaPagamentoControle.taxaCartaoVigencia and not empty FormaPagamentoControle.taxaCartaoAtual}"
                                     columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaOpcoes"
                                     value="#{FormaPagamentoControle.taxaCartaoAtual}" var="taxaCartao">


                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Nr. Parcelas" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:outputText value="#{taxaCartao.nrmeses}" styleClass="texto-size-14 cinza"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Antecipação Automática" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:outputText rendered="#{taxaCartao.compensacaoPorTaxa && taxaCartao.nrDiasCompensacaoPorTaxa >= 0}"
                                        styleClass="texto-size-14 cinza"
                                        value="Sim, em #{taxaCartao.nrDiasCompensacaoPorTaxa} dias"/>

                                <h:outputText rendered="#{!taxaCartao.compensacaoPorTaxa && taxaCartao.nrDiasCompensacaoPorTaxa == 0}"
                                        styleClass="texto-size-14 cinza"
                                        value="Não"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Taxa Atual" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:outputText value="#{taxaCartao.taxa}" styleClass="texto-size-14 cinza">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Adquirente" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:outputText value="#{taxaCartao.adquirenteVO.nome}" styleClass="texto-size-14 cinza"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Bandeira" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:outputText value="#{taxaCartao.bandeira.descricao}" styleClass="texto-size-14 cinza"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Vigente Desde" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:outputText value="#{taxaCartao.vigenciaInicial_Apresentar}" styleClass="texto-size-14 cinza"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Vigente Até" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <h:outputText value="#{taxaCartao.vigenciaFinal_Apresentar}" styleClass="texto-size-14 cinza"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="texto-size-14 cinza negrito upper"/>
                                </f:facet>
                                <%--<a4j:commandButton id="editarUsuarioPerfilAcesso"--%>
                                <%--action="#{FormaPagamentoControle.editarTaxa}"--%>
                                <%--styleClass="botoes"--%>
                                <%--style="vertical-align: middle"--%>
                                <%--reRender="pnlTaxas" value="#{msg_bt.btn_editar}"--%>
                                <%--image="./imagens/botaoEditar.png"/>--%>
                                <a4j:commandLink id="removerTipoColaborador"
                                                 action="#{FormaPagamentoControle.removerTaxa}"
                                                 reRender="pnlTaxas"
                                                 style="vertical-align: middle; display: initial; "
                                                 styleClass="botoes nvoBt btSec" accesskey="7">
                                    <i class="fa-icon-trash"></i>
                                </a4j:commandLink>
                                <a4j:commandLink id="selecionarTaxa"
                                                 action="#{FormaPagamentoControle.selecionarTaxa}"
                                                 reRender="form"
                                                 style="vertical-align: middle; display: initial"
                                                 styleClass="botoes nvoBt btSec"
                                                 accesskey="9">
                                    <i class="fa-icon-edit"></i>
                                </a4j:commandLink>
                            </h:column>
                        </h:dataTable>

                            <%--TAXA CARTÃO VIGENCIA--%>
                        <h:panelGrid id="panelTaxaCartaoVigencia"
                                     rendered="#{not empty FormaPagamentoControle.taxaCartaoVigencia}" columns="1"
                                     width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">


                            <h:outputText styleClass="titulo" style="color: red; display: block; "
                                          value="* Ao modificar a vigência da taxa, as operações já realizadas no Financeiro não serão modificadas."/>

                            <a4j:commandButton id="adicionarTaxaVigencia"
                                               action="#{FormaPagamentoControle.adicionarNovaTaxaVigencia}"
                                               reRender="form" value="Adicionar nova taxa"
                                               styleClass="botoes nvoBt bt" accesskey="5" />

                            <h:dataTable id="dtTaxasVigencia" width="100%"  styleClass="tblHeaderLeft contratoSelecionado"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                                         value="#{FormaPagamentoControle.taxaCartaoVigencia}" var="taxaCartaoVigencia">


                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Nr. Parcelas" styleClass="texto-size-14 cinza negrito upper"/>
                                    </f:facet>
                                    <h:outputText value="#{taxaCartaoVigencia.nrmeses}" styleClass="texto-size-14 cinza"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Antecipação Automática" styleClass="texto-size-14 cinza negrito upper"/>
                                    </f:facet>
                                    <h:panelGroup layout="block" styleClass="chk-fa-container">
                                    <h:selectBooleanCheckbox
                                                             styleClass="form tooltipster"
                                                             title="Ao ativar a configuração para esta taxa, todas as parcelas adicionadas na mesma
                                                     terão a data de compensação de acordo com o configurado no campo \"Dias Compensação\"."
                                                             value="#{taxaCartaoVigencia.compensacaoPorTaxa}">
                                        <a4j:support reRender="pnlTaxas" actionListener="#{FormaPagamentoControle.validarLimparCompensacaoPorTaxa}" event="onclick">
                                            <f:attribute name="taxaCartaoCompensacao" value="#{taxaCartaoVigencia}"/>
                                        </a4j:support>
                                    </h:selectBooleanCheckbox>
                                    <span />
                                    </h:panelGroup>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Nr. Dias Compensação" styleClass="texto-size-14 cinza negrito upper"/>
                                    </f:facet>
                                    <h:inputText size="3" maxlength="3"
                                                 disabled="#{!taxaCartaoVigencia.compensacaoPorTaxa}"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{taxaCartaoVigencia.nrDiasCompensacaoPorTaxa}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Taxa" styleClass="texto-size-14 cinza negrito upper"/>
                                    </f:facet>
                                    <h:inputText size="6" maxlength="6" id="taxaVigencia"
                                                 onkeyup="return porcentagem(this);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{taxaCartaoVigencia.taxa}">
                                    </h:inputText>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Adquirente" styleClass="texto-size-14 cinza negrito upper"/>
                                    </f:facet>
                                    <h:panelGroup layout="block" styleClass="block cb-container">
                                        <h:selectOneMenu value="#{taxaCartaoVigencia.adquirenteVO.codigo}"
                                                         styleClass="form">
                                            <f:selectItems value="#{FormaPagamentoControle.adquirentes}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Bandeira" styleClass="texto-size-14 cinza negrito upper"/>
                                    </f:facet>
                                    <h:panelGroup layout="block" styleClass="block cb-container">
                                        <h:selectOneMenu value="#{taxaCartaoVigencia.bandeira.codigo}"
                                                         styleClass="form">
                                            <f:selectItems value="#{FormaPagamentoControle.operadoras}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Vigência Inicial" styleClass="texto-size-14 cinza negrito upper"/>
                                    </f:facet>
                                    <h:panelGroup styleClass="dateTimeCustom" >
                                        <rich:calendar datePattern="dd/MM/yyyy"
                                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                                       inputSize="8"
                                                       showWeekDaysBar="false"
                                                       showWeeksBar="false"
                                                       enableManualInput="true"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputchange="return validar_Data(this.id);"
                                                       styleClass="form"
                                                       direction="top-left"
                                                       value="#{taxaCartaoVigencia.vigenciaInicial}"/>
                                    </h:panelGroup>
                                </h:column>


                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Vigência Final" styleClass="texto-size-14 cinza negrito upper"/>
                                    </f:facet>
                                    <h:panelGroup styleClass="dateTimeCustom" >
                                        <rich:calendar datePattern="dd/MM/yyyy"
                                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                                       inputSize="8"
                                                       showWeekDaysBar="false"
                                                       showWeeksBar="false"
                                                       enableManualInput="true"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputchange="return validar_Data(this.id);"
                                                       styleClass="form"
                                                       direction="top-left"
                                                       value="#{taxaCartaoVigencia.vigenciaFinal}"/>
                                    </h:panelGroup>
                                </h:column>
                                <c:if test="${FormaPagamentoControle.configuracaoSistema.utilizarServicoSesiSC}">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  styleClass="tituloCampos" value="Produto Venda Sesi"/>
                                        </f:facet>
                                        <h:inputText  size="20" maxlength="100" id="produtoVendaSesiEdit"
                                                      value="#{taxaCartaoVigencia.produtoVendaSesi}">
                                        </h:inputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  styleClass="tituloCampos" value="Tipo Documento Sesi"/>
                                        </f:facet>
                                        <h:inputText  size="30" maxlength="100" id="tipodocumentoSesiEdit"
                                                      value="#{taxaCartaoVigencia.tipodocumentoSesi}">
                                        </h:inputText>
                                    </h:column>
                                </c:if>


                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="texto-size-14 cinza negrito upper"/>
                                    </f:facet>
                                    <a4j:commandLink action="#{FormaPagamentoControle.removerTaxaVigencia}"
                                                       reRender="pnlTaxas" style="display: initial"
                                                       accesskey="7"
                                                     styleClass="botoes nvoBt btSec " >
                                        <i class="fa-icon-trash"></i>
                                    </a4j:commandLink>
                                </h:column>
                            </h:dataTable>


                            <a4j:commandButton id="gravarTaxaVigencia"
                                               action="#{FormaPagamentoControle.gravarTaxaVigenciaTela}"
                                               value="Gravar Vigência da Taxa"
                                               style="vertical-align: middle"
                                               reRender="form"
                                               styleClass="botoes nvoBt btSec"/>
                        </h:panelGrid>

                    </div>

                </h:panelGroup>

                <%-- PERFIL DE ACESSO --%>
                <h:panelGroup layout="block" styleClass="painelDadosAluno"
                              style="width: calc(98.5% - 10px); height: auto; font-family: Arial,Verdana,sans-serif;"
                              id="perfilAcessoFormaPgto">
                    <div class="tituloPainelAluno">
                        <h:outputText styleClass="negrito cinzaEscuro pl20" value="Configurações de Perfis"
                                      style="line-height: 40px;font-size: 14px !important;"/>
                    </div>

                    <div class="conteudoDadosCliente" style="min-height: 50px; text-align: center">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" id="perfilAcessoSelecionadaFormaPagamento"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     styleClass="novaModal">

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FormaPagamento_perfilAcesso}:"/>
                            <h:panelGroup layout="block" styleClass="block cb-container">
                                <h:selectOneMenu id="perfilAcesso" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{FormaPagamentoControle.formaPagamentoPerfilAcesso.perfilAcessoVO.codigo}">
                                    <f:selectItems value="#{FormaPagamentoControle.perfisAcesso}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>

                        <a4j:commandButton id="adicionarFormaPagamentoPerfilAcesso"
                                           action="#{FormaPagamentoControle.adicionarFormaPagamentoPerfilAcesso}"
                                           reRender="form"
                                           styleClass="botoes nvoBt botaoPrimarioGrande"
                                           accesskey="9"
                                           value="Adicionar"/>
                    </div>

                    <h:dataTable width="100%"
                                 styleClass="tblHeaderLeft contratoSelecionado"
                                 columnClasses="colunaAlinhamento, colunaAlinhamento"
                                 rendered="#{not empty FormaPagamentoControle.formaPagamentoVO.formasPerfilAcesso}"
                                 value="#{FormaPagamentoControle.formaPagamentoVO.formasPerfilAcesso}" var="perfilAcesso">

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="PERFIL ACESSO" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:outputText value="#{perfilAcesso.perfilAcessoVO.nome}" styleClass="texto-size-14 cinza upper"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>

                            <a4j:commandButton id="removerPerfilAcesso"
                                               action="#{FormaPagamentoControle.removerFormaPagamentoPerfilAcesso}"
                                               value="Remover"
                                               oncomplete="Richfaces.showModalPanel('mdlMensagemGenerica');"
                                               style="vertical-align: middle;"
                                               reRender="mdlMensagemGenerica"
                                               styleClass="botoes nvoBt btSec bt"/>
                        </h:column>
                    </h:dataTable>


                </h:panelGroup>


                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="pnlMsg" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton rendered="#{FormaPagamentoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{FormaPagamentoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{FormaPagamentoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{FormaPagamentoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" action="#{FormaPagamentoControle.novo}"
                                                   value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                                   styleClass="botoes nvoBt btSec"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="salvar" action="#{FormaPagamentoControle.gravar}"
                                                   value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}"
                                                   oncomplete="#{FormaPagamentoControle.mensagemNotificar}"
                                                   reRender="form"
                                                   accesskey="2" styleClass="botoes nvoBt"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="excluir"
                                                   action="#{FormaPagamentoControle.confirmarExcluir}"
                                                   oncomplete="#{FormaPagamentoControle.msgAlert}"
                                                   reRender="mdlMensagemGenerica"
                                                   value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}"
                                                   accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="consultar"
                                                   action="#{FormaPagamentoControle.inicializarConsultar}"
                                                   value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                                   accesskey="4" styleClass="botoes nvoBt btSec"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandLink action="#{FormaPagamentoControle.realizarConsultaLogObjetoSelecionado}"
                                                 reRender="form"
                                                 oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                 title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                                 style="display: inline-block; padding: 8px 15px;">
                                    <i class="fa-icon-list"></i>
                                </a4j:commandLink>

                            </h:panelGroup>


                        </c:if>
                        <c:if test="${modulo eq 'centralEventos'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" action="#{FormaPagamentoControle.novo}"
                                                   value="#{msg_bt.btn_novo}"
                                                   alt="#{msg.msg_novo_dados}" accesskey="1"
                                                   styleClass="botoes nvoBt btSec"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="salvar" action="#{FormaPagamentoControle.gravarCE}"
                                                   value="#{msg_bt.btn_gravar}"
                                                   oncomplete="#{FormaPagamentoControle.mensagemNotificar}"
                                                   reRender="form"
                                                   alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                                                   actionListener="#{FormaPagamentoControle.autorizacao}">
                                    <!-- entidade.formas pagamento -->
                                    <f:attribute name="entidade" value="123"/>
                                    <!-- operacao.gravar -->
                                    <f:attribute name="operacao" value="G"/>
                                </a4j:commandButton>

                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                   action="#{FormaPagamentoControle.confirmarExcluir}"
                                                   oncomplete="#{FormaPagamentoControle.msgAlert}"
                                                   value="#{msg_bt.btn_excluir}"
                                                   alt="#{msg.msg_excluir_dados}"
                                                   accesskey="3" styleClass="botoes nvoBt btSec btPerigo"
                                                   actionListener="#{FormaPagamentoControle.autorizacao}">
                                    <!-- entidade.formas pagamento -->
                                    <f:attribute name="entidade" value="123"/>
                                    <!-- operacao.excluir -->
                                    <f:attribute name="operacao" value="E"/>
                                </a4j:commandButton>


                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="consultar"
                                                   action="#{FormaPagamentoControle.inicializarConsultar}"
                                                   value="#{msg_bt.btn_consultar}"
                                                   alt="#{msg.msg_consultar_dados}" accesskey="4"
                                                   styleClass="botoes nvoBt btSec"
                                                   actionListener="#{FormaPagamentoControle.autorizacao}">
                                    <!-- entidade.formas pagamento -->
                                    <f:attribute name="entidade" value="123"/>
                                    <!-- operacao.consultar -->
                                    <f:attribute name="operacao" value="C"/>
                                </a4j:commandButton>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandLink action="#{FormaPagamentoControle.realizarConsultaLogObjetoSelecionado}"
                                                 reRender="form"
                                                 oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                 title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                                 style="display: inline-block; padding: 8px 15px;">
                                    <i class="fa-icon-list"></i>
                                </a4j:commandLink>
                            </h:panelGroup>


                        </c:if>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')"/>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/include_modal_taxa_cartao_empresa.jsp" %>
    <%@include file="includes/include_modal_taxa_boleto.jsp" %>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript">
        jQuery.noConflict();
    </script>


</f:view>
<script>
    document.getElementById("form:descricao").focus();

    function somenteNumeros(num) {
        var er = /[^0-9.]/;
        er.lastIndex = 0;
        var campo = num;
        if (er.test(campo.value)) {
            campo.value = "";
        }
    }

</script>
