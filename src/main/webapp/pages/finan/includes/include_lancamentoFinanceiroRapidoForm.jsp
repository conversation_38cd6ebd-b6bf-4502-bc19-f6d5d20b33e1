<%@page pageEncoding="ISO-8859-1" %>
<%--
    Document   : include_lancamentoFinanceiroRapido
    Created on : 16/10/2017, 10:00:24
    Author     : Ulis<PERSON>
--%>
<style type="text/css">
    .tituloBold {
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        text-decoration: none;
        line-height: normal;
        text-transform: none;
        font-weight: bold;
        color: #000000;
    }

    .tituloDemonstrativo {
        font-family:"Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
        font-size: 13px;
        text-decoration: none;
        line-height: normal;
        text-transform: none;
        color: #000000;
    }
    .pure-button {
        font-size: 100%;
        padding: .5em 1.5em;
    }
</style>

<script>
    function aplicarMascaraData(){
        jQuery('.rich-calendar-input').unmask().mask('99/99/9999');
    }
    function retirarTabIndexButtonCalendar(){
        var btnCalVenc = document.getElementById('form:dataVencimentoPopupButton');
        var btnCalComp = document.getElementById('form:dataCompetenciaPopupButton');
        btnCalVenc.setAttribute('tabindex', '-1');
        btnCalComp.setAttribute('tabindex', '-1');
    }
    function setarDatasAoMudarLancamento(valor){
        document.getElementById('form:dataCompetenciaInputDate').value = valor;
        document.getElementById('form:dataVencimentoInputDate').value = valor;
    }

    function tabulacaoOnBlurDataLancamento(e){
       if ((e.shiftKey) && (e.keyCode == 9 || e.which == 9)) {
           povoarDataLancFocoValor();
       }else if (e.keyCode == 9 || e.which == 9){
           povoarDataLancFocoDtVenc();
       }
    }

    function tabulacaoOnBlurDataQuitacao(e){
        if ((e.shiftKey) && (e.keyCode == 9 || e.which == 9)) {
            povoarDtQuitacaoFocObs();
        }else if (e.keyCode == 9 || e.which == 9){
            povoarDtQuitacaoFocForma();
        }
    }


</script>

<a4j:jsFunction status="statusHora" reRender="panelPessoaContaRapido" name="pesquisarPessoaAssinc" action="#{MovContaControle.pesquisarPessoaAssincrono}">
    <a4j:actionparam name="tmpPessAss" value="document.getElementById('form:pessoa').value" noEscape="true" assignTo="#{MovContaControle.codigoPessoaAssincrono}"/>
</a4j:jsFunction>
<a4j:jsFunction status="statusHora" reRender="pgPlanoContas" name="pesquisarPlanoContaAssinc" action="#{MovContaControle.pesquisarPlanoContasAssincrono}">
    <a4j:actionparam name="tmpPlanoAssinc" value="document.getElementById('form:nomePlanoSelecionado').value" noEscape="true" assignTo="#{MovContaControle.codigoPlanoContaAssincrono}"/>
</a4j:jsFunction>

<a4j:jsFunction status="statusHora" reRender="pgCentroCustos" name="pesquisarCentroCustoAssinc" action="#{MovContaControle.pesquisarCentroCustoAssincrono}">
    <a4j:actionparam name="tmpCentroAssinc" value="document.getElementById('form:nomeCentroSelecionado').value" noEscape="true" assignTo="#{MovContaControle.codigoPlanoCentroCustoAssincrono}"/>
</a4j:jsFunction>
<a4j:jsFunction status="statusHora"  name="povoarDataLancFocoDtVenc" oncomplete="document.getElementById('form:dataVencimentoInputDate').focus();" >
    <a4j:actionparam name="tmpPovoarDtLanc" value="document.getElementById('form:dtLancamento').value" noEscape="true" assignTo="#{MovContaControle.dataLancamentoRapido}"/>
</a4j:jsFunction>
<a4j:jsFunction status="statusHora"  name="povoarDataLancFocoValor" oncomplete="document.getElementById('form:valor').focus();" >
    <a4j:actionparam name="tmpPovoarDtLancFocValor" value="document.getElementById('form:dtLancamento').value" noEscape="true" assignTo="#{MovContaControle.dataLancamentoRapido}"/>
</a4j:jsFunction>

<a4j:jsFunction status="statusHora"  name="povoarDtQuitacaoFocForma" oncomplete="document.getElementById('form:formaPagamento').focus();" reRender="pgDtQuit,panelMensagem, conta,formaPagamento" >
    <a4j:actionparam name="tmpPovoarDtQuitacaoFocForma" value="document.getElementById('form:dataQuitacao').value" noEscape="true" assignTo="#{MovContaControle.dataQuitacaoRapido}"/>
</a4j:jsFunction>

<a4j:jsFunction status="statusHora"  name="povoarDtQuitacaoFocObs" oncomplete="document.getElementById('form:observacaoRichEditor').focus();" reRender="pgDtQuit,panelMensagem, conta,formaPagamento">
    <a4j:actionparam name="tmpPovoarDtQuitacaoFocForma" value="document.getElementById('form:dataQuitacao').value" noEscape="true" assignTo="#{MovContaControle.dataQuitacaoRapido}"/>
</a4j:jsFunction>



    <h:panelGroup style="width :100%; vertical-align: top;">

        <table width="100%">
            <tr>
                <td align="left">
                    <img src="${root}/images/arrow2.gif" width="16" height="16"
                         style="vertical-align: middle; margin-right: 6px;">
                    <h:outputText styleClass="tituloBold" value="#{msg_aplic.prt_Finan_Lancamentos_dados}"/>
                </td>
            </tr>
        </table>

        <div class="sep" style="margin:4px 0 5px 0;"><img src="${root}/images/shim.gif"></div>

        <h:panelGrid id="panelTipoLancamentoRapido" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="alinhamentoTop, classDireita"
                     width="100%">
            <!-- tipo lançamento -->
            <h:outputText  styleClass="tituloCampos"
                           value="Tipo Lançamento:"/>
            <h:selectOneMenu id="comboTpLancamento" onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             style="color:black;"
                             tabindex="1"
                             styleClass="form"
                             value="#{MovContaControle.tipoLancamentoRapido}">
                <f:selectItems value="#{MovContaControle.listaTipoLancamentoRapido}"/>
                <a4j:support event="onchange" action="#{MovContaControle.onChangeTipoLancamentoRapido}"
                             oncomplete="setarFocusCampoPessoaOuEmpresa();"
                             reRender="dadosLancamentoRapido, formLanc:panelMensagem"/>
            </h:selectOneMenu>

       </h:panelGrid>

       <h:panelGroup layout="block" id="dadosLancamentoRapido">
           <h:panelGrid id="panelCampos"
                       columns="2"
                       rowClasses="linhaImpar, linhaPar"
                       columnClasses="alinhamentoTop, classDireita"
                       width="100%">

         <%--  <%@include file="/pages/finan/includes/include_dadosLancamento.jsp" %> --%>

            <!-- empresa -->
            <h:outputText rendered="#{MovContaControle.mostrarCampoEmpresa && MovContaControle.existeMaisDeUmaEmpresa}" styleClass="tituloCampos"
                          value="#{msg_aplic.prt_Finan_Lancamento_empresa}"/>
            <h:selectOneMenu rendered="#{MovContaControle.mostrarCampoEmpresa && MovContaControle.existeMaisDeUmaEmpresa}"
                             id="empresa" onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             style="color:black;"
                             tabindex="2"
                             styleClass="form"
                             value="#{MovContaControle.movContaVO.empresaVO.codigo}">
                <f:selectItems value="#{MovContaControle.listaSelectItemEmpresa}"/>
                <a4j:support event="onchange" action="#{MovContaControle.verificarPreenchimentoCampoEmpresa}"
                             focus="pessoa"
                             reRender="pessoa, suggestionPessoa, nomePessoaLabel, conta"/>
            </h:selectOneMenu>



            <!-- pessoa -->
            <h:outputText styleClass="tituloCampos" id="labelPessoaContaRapido" value="#{MovContaControle.nomeCampoPessoa}"/>
            <h:panelGroup id="panelPessoaContaRapido">

                <h:panelGroup>
                    <h:inputText id="pessoa" size="50" maxlength="80"
                                 tabindex="3"
                                 onblur="pesquisarPessoaAssinc();"
                                 onkeypress="if (event.keyCode == 13) { document.getElementById('form:descricao').focus();return false;};"
                                 onfocus="focusinput(this);" styleClass="form" value="#{MovContaControle.movContaVO.pessoaVO.nomeComCodigo}">
                    </h:inputText>
                    <h:panelGroup id="pgNovaPessoa">
                        <a4j:commandLink action="#{MovContaControle.cadastrarNovaPessoaLancamentoRapido}"
                                         rendered="#{MovContaControle.mostrarBotaoCadastrarPessoa}"
                                         style="margin-left: 10px; font-size: 1.5em; vertical-align: middle"
                                         title="Cadastrar nova pessoa"
                                         styleClass="fa fa-icon-plus"
                                         reRender="modalPanelCadastrarPessoaSimplificada"
                                         oncomplete="#{MovContaControle.msgAlert}">

                        </a4j:commandLink>
                    </h:panelGroup>

                </h:panelGroup>

                <h:graphicImage id="imageLoading" style="visibility: hidden;vertical-align: middle;" value="/images/loading.gif"/>

                <rich:suggestionbox height="200" width="650"
                                    for="pessoa"
                                    fetchValue="#{result}"
                                    suggestionAction="#{MovContaControle.executarAutocompleteConsultaPessoa}"
                                    minChars="1" rowClasses="20"
                                    status="true"
                                    reRender="panelMensagem, pgNovaPessoa"
                                    nothingLabel="Nenhuma pessoa encontrada!"
                                    var="result" id="suggestionPessoa">
                    <a4j:support event="onselect" ignoreDupResponses="true" action="#{MovContaControle.selecionarPessoaSuggestionBox}" focus="descricao" reRender="form"/>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText id="nomeSugestionLanctitle" styleClass="textverysmall" value="Nome"/>
                        </f:facet>
                        <h:outputText id="nomeSugestionLanc" value="#{result.nomeComCodigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="textverysmall" value="Tipo"/>
                        </f:facet>
                        <h:outputText value="#{result.tipoPessoa}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="textverysmall" value="CPF/CNPJ"/>
                        </f:facet>
                        <h:outputText value="#{result.cfp}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="textverysmall" value="Empresa"/>
                        </f:facet>
                        <h:outputText value="#{result.nomeEmpresa}"/>
                    </h:column>
                </rich:suggestionbox>
                <rich:spacer width="15px"/>
            </h:panelGroup>

            <!-- descricao -->
            <h:panelGroup >
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_descricao}"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText id="descricao" size="50" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);"
                             tabindex="4"
                             styleClass="form" value="#{MovContaControle.movContaVO.descricao}"/>
            </h:panelGroup>

            <h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_valor}"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText id="valor" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                             tabindex="5"
                             onkeyup="return moeda(this);"
                             styleClass="form" value="#{MovContaControle.movContaVO.valor}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>

                <script>
                    VMasker(document.getElementById("form:valor")).maskMoney({
                        precision: 2,
                        separator: ',',
                        delimiter: '.',
                        zeroCents: false
                    });
                </script>
            </h:panelGroup>


            <h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_dataLancamento}"/>
            </h:panelGroup>

            <h:panelGroup>
                <!-- data lancamento -->
                <h:panelGroup>
                    <rich:jQuery id="mskDataLanc" selector="#dtLancamento" timing="onload" query="mask('99/99/9999')" />
                    <h:inputText id="dtLancamento"
                                 styleClass="form"
                                 size="8"
                                 tabindex="6"
                                 onkeydown="tabulacaoOnBlurDataLancamento(event)"
                                 onchange="if(validar_DataFatura(this.id)) {setarDatasAoMudarLancamento(this.value)} else { return false };"
                                 value="#{MovContaControle.dataLancamentoRapido}">

                        <a4j:support event="onblur"
                                     reRender="dtLancamentoCalendar"
                                     action="#{MovContaControle.povoarDataLancamento}" />
                    </h:inputText>

                    <rich:calendar id="dtLancamentoCalendar"
                                   value="#{MovContaControle.movContaVO.dataLancamento}"
                                   inputSize="8"
                                   inputClass="form"
                                   oninputchange="return validar_DataFatura(this.id);"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   showInput="false"
                                   showWeeksBar="false">

                        <a4j:support event="onchanged" reRender="dtLancamento,pgDataVencimento,pgCompetencia, panelMensagem"
                                     oncomplete="aplicarMascaraData();document.getElementById('form:dataVencimento').focus();retirarTabIndexButtonCalendar();"
                                     action="#{MovContaControle.povoarDataLancamentoVencimentoECompetencia2}" />

                    </rich:calendar>
                </h:panelGroup>
                <!-- data vencimento -->
                <h:panelGroup style="margin-left: 20px">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_dataVencimento}"/>
                </h:panelGroup>
                <h:panelGroup id="pgDataVencimento">

                    <rich:calendar id="dataVencimento"
                                   tabindex="7"
                                   value="#{MovContaControle.movContaVO.dataVencimento}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"

                                   onchanged="document.getElementById('form:dataCompetenciaInputDate').value=document.getElementById('form:dataVencimentoInputDate').value;"
                                   oninputchange="if(validar_DataFatura(this.id)){document.getElementById('form:dataCompetenciaInputDate').value=document.getElementById('form:dataVencimentoInputDate').value;}"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   showWeeksBar="false">
                    </rich:calendar>

                </h:panelGroup>
                <!-- data competencia -->
                <h:panelGroup style="margin-left: 20px">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_dataCompetencia}"/>
                </h:panelGroup>

                <h:panelGroup id="pgCompetencia">

                    <rich:calendar id="dataCompetencia"
                                   value="#{MovContaControle.movContaVO.dataCompetencia}"
                                   styleClass="dataCompetencia"
                                   inputSize="8"
                                   tabindex="8"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_DataFatura(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:outputText   styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_observacoes}"/>

            <h:inputTextarea style="height: 63px; resize: none;"
                             tabindex="9"
                             id="observacaoRichEditor"
                             value="#{MovContaControle.movContaVO.observacoes}" rows="3" cols="31"/>



            <!-- data quitacao -->
            <h:outputText styleClass="tituloCampos"  value="#{msg_aplic.prt_Finan_Lancamento_dataQuitacao}"/>
               <h:panelGroup id="pgDtQuit">
                   <rich:jQuery id="mskDataQuitacao" selector="#dataQuitacao" timing="onload" query="mask('99/99/9999 - 99:99:99')" />
                   <h:inputText id="dataQuitacao"
                                styleClass="form"
                                tabindex="10"
                                onkeydown="tabulacaoOnBlurDataQuitacao(event)"
                                value="#{MovContaControle.dataQuitacaoRapido}">

                       <a4j:support event="onblur" reRender="pgDtQuit,panelMensagem, conta,formaPagamento"
                                    status="false"
                                    action="#{MovContaControle.povoarDataQuitacaoConta}" />
                   </h:inputText>

                   <rich:calendar id="dataQuitacaoCalendar"
                                  value="#{MovContaControle.dataQuitacaoConta}"
                                  direction="bottom-left"
                                  inputSize="12"
                                  styleClass="tituloboxcentro2"
                                  showInput="false"
                                  inputClass="form"
                                  datePattern="dd/MM/yyyy"
                                  zindex="2"
                                  showWeeksBar="false">
                       <a4j:support event="onchanged" reRender="dataQuitacao, dataQuitacaoCalendar,panelMensagem"
                                    action="#{MovContaControle.povoarDataQuitacaoConta2}" />
                   </rich:calendar>

               </h:panelGroup>

               <!-- forma de pagamento -->
               <h:outputText styleClass="tituloCampos"
                             value="#{msg_aplic.prt_Finan_Lancamento_formaPagamento}"/>
               <h:selectOneMenu
                       id="formaPagamento" onblur="blurinput(this);"
                       tabindex="11"
                       onfocus="focusinput(this);"
                       styleClass="form"
                       value="#{MovContaControle.formaPagamentoRapido}">
                   <f:selectItems value="#{MovContaControle.listaFormaPagamentoRapido}"/>
               </h:selectOneMenu>

               <h:outputText styleClass="tituloCampos" value="Conta:" ></h:outputText>
               <h:panelGroup >
                   <h:selectOneMenu value="#{MovContaControle.contaQuitacaoRapido}"
                                    id="conta"
                                    tabindex="12"
                                    onblur="blurinput(this);"
                                    onfocus="focusinput(this);"
                                    style="color:black;"
                                    styleClass="form">

                       <f:selectItems value="#{MovContaControle.listaComboContaQuitacao}"></f:selectItems>
                   </h:selectOneMenu>
               </h:panelGroup>


            <!-- plano de contas -->
            <h:outputText  styleClass="tituloCampos"
                          value="#{msg_aplic.prt_Finan_Lancamento_planoContas}"/>
            <h:panelGroup id="pgPlanoContas">
                <table cellpadding="0" cellspacing="0">
                    <tr valign="top">
                        <td>
                            <h:inputText id="nomePlanoSelecionado"
                                         size="50"
                                         maxlength="50"
                                         tabindex="#{PlanoContasControle.tabIndexSuggestionBoxPlanoContas}"
                                         onfocus="focusinput(this);"
                                         onblur="pesquisarPlanoContaAssinc();"
                                         styleClass="form"
                                         value="#{PlanoContasControle.planoNome}"  >
                            </h:inputText>

                            <rich:suggestionbox   height="200" width="400"
                                                  for="nomePlanoSelecionado"
                                                  status="statusInComponent"
                                                  immediate="true"
                                                  suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                                                  minChars="1"
                                                  nothingLabel="Nenhum Plano de Contas encontrado"
                                                  rowClasses="linhaImpar, linhaPar"
                                                  var="result"  id="suggestionResponsavel" >
                                <a4j:support event="onselect"
                                             reRender="formTelaLancRapido:form"
                                             focus="nomeCentroSelecionado"
                                             action="#{PlanoContasControle.selecionarPlanoContas}">
                                </a4j:support>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Nome"  styleClass="textverysmall"/>
                                    </f:facet>
                                    <h:outputText styleClass="textverysmall" value="#{result.descricaoCurtaComCodigo}" />
                                </h:column>
                                <h:column >
                                    <f:facet name="header">
                                        <h:outputText value="Tipo" styleClass="textverysmall"/>
                                    </f:facet>
                                    <h:outputText styleClass="textverysmall" value="#{result.tipoPadrao.descricao}" />
                                </h:column>
                            </rich:suggestionbox>

                        </td>
                        <td> &nbsp;<a4j:commandLink action="#{PlanoContasControle.verificarConsultaLancamento}"
                                                    reRender="modalPlanos"
                                                    id="btAddPlano" value="Consultar"
                                                    oncomplete="Richfaces.showModalPanel('modalPlanos')"/></td>
                    </tr>
                </table>
            </h:panelGroup>

            <!-- centro de custos -->
            <h:outputText styleClass="tituloCampos"
                          value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}"/>
            <h:panelGroup id="pgCentroCustos">
                <table cellpadding="0" cellspacing="0">
                    <tr valign="top">
                        <td>
                            <h:inputText  id="nomeCentroSelecionado"
                                          size="50"
                                          maxlength="50"
                                          tabindex="#{CentroCustosControle.tabIndexSuggestionBoxCentroCustos}"
                                          onblur="blurinput(this);pesquisarCentroCustoAssinc();"
                                          onfocus="focusinput(this);"
                                          styleClass="form"
                                          value="#{CentroCustosControle.centroNome}" >
                            </h:inputText>

                            <rich:suggestionbox   height="200" width="400"
                                                  for="nomeCentroSelecionado"
                                                  status="statusInComponent"
                                                  immediate="true"
                                                  suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                                                  minChars="1"
                                                  nothingLabel="Nenhum Centro de Custos encontrado"
                                                  rowClasses="linhaImpar, linhaPar"
                                                  var="result"  id="suggestionCentroCusto">
                                <a4j:support event="onselect"
                                             focus="ok"
                                             reRender="formTelaLancRapido:form"
                                             action="#{CentroCustosControle.selecionarCentroCusto}">
                                </a4j:support>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Nome"  styleClass="textverysmall"/>
                                    </f:facet>
                                    <h:outputText styleClass="textverysmall" value="#{result.descricaoCurtaComCodigo}" />
                                </h:column>
                            </rich:suggestionbox>

                        </td>
                        <td> &nbsp;<a4j:commandLink reRender="modalCentros"
                                                    id="btAddCentro" value="Consultar"
                                                    oncomplete="Richfaces.showModalPanel('modalCentros')"/></td>
                    </tr>
                </table>
            </h:panelGroup>


        </h:panelGrid>
       </h:panelGroup>


    </h:panelGroup>





<h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
        <h:panelGrid columns="1" width="100%">

            <h:outputText value=" "/>

        </h:panelGrid>
        <h:commandButton rendered="#{MovContaControle.sucesso}" image="/imagens/sucesso.png"/>
        <h:commandButton rendered="#{MovContaControle.erro}" image="/imagens/erro.png"/>
        <h:panelGrid columns="1" width="100%">
            <h:outputText id="msgContaPagar" styleClass="mensagem" value="#{MovContaControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" value="#{MovContaControle.mensagemDetalhada}"/>
        </h:panelGrid>
    </h:panelGrid>
</h:panelGrid>

<h:panelGrid width="100%" id="gridBotoesLancamentoRapido" columnClasses="colunaCentralizada" columns="1">
    <h:panelGroup>
        <a4j:commandButton id="ok"
                           reRender="form"
                           oncomplete="#{MovContaControle.mensagemNotificar} document.getElementById('form:pessoa').focus();"
                           tabindex="14"
                           value="Gravar"
                           focus="pessoa"
                           action="#{MovContaControle.gravarLancamentoRapido}"
                         styleClass="botoes nvoBt btSec btPerigo">
        </a4j:commandButton>

    </h:panelGroup>
</h:panelGrid>
<script>
    retirarTabIndexButtonCalendar();
</script>


