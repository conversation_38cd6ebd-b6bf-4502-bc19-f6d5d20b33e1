<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/include_imports.jsp" %>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>
<script type="text/javascript" src="../../script/script.js"></script>
<script type="text/javascript" language="javascript" src="../../script/gobackblock.js"></script>
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>

<head>
    <%@include file="includes/include_head_finan.jsp" %>
    <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
    <script type="text/javascript" language="javascript" src="${contextoFinan}/script/vanilla-masker.min.js"></script>
    <script type="text/javascript" language="javascript" src="../../script/Notifier.js"></script>
    <link href="${contexto}/beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
</head>

<script>
    function setarFocusCampoPessoaOuEmpresa(){
        var empresa = document.getElementById("formTelaLancRapido:empresa");
        if (!empresa){
            document.getElementById("formTelaLancRapido:pessoa").focus();
        } else {
            empresa.focus();
        }
    }
</script>

<c:set var="moduloSession" value="1" scope="session"/>
<c:set var="titulo" scope="session" value="FINANCEIRO"/>
<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <%@include file="includes/include_modalSelecaoPlanoConta.jsp" %>
    <%@include file="includes/include_modalSelecaoCentroCusto.jsp" %>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:form id="formTelaLancRapido">
        <html>
        <body>
        <h:panelGroup layout="block" styleClass="fundoCinza" style="width: 100%; display: inline-table;">

            <!-- Topo e Menu -->
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
            </h:panelGroup>

            <!-- Corpo com conteúdo -->
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%; width: 100%;">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial">

                                <!-- Cabeçalho -->
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="LANÇAMENTO DE CONTA RÁPIDO"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-fazer-um-lancamento-rapido-de-conta-no-financeiro/"
                                                      title="Clique e saiba mais: Lançamentos Financeiros"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <!-- Abas internas -->
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <rich:tabPanel switchType="client" styleClass="tabsFinan" style="margin-top: 10px;">
                                        <rich:tab id="abaDadosLancamento" label="Dados do Lançamento" styleClass="titulo3SemDecoracao">
                                            <%@ include file="includes/include_lancamentoFinanceiroRapidoForm.jsp" %>
                                        </rich:tab>
                                        <rich:tab id="abaImpostacaoContas" label="Importar Contas (Planilha)"
                                                    styleClass="titulo3SemDecoracao">
                                            <%@ include file="includes/include_lancamentoFinanceiroImportacaoForm.jsp" %>
                                        </rich:tab>
                                    </rich:tabPanel>
                                </h:panelGroup>

                            </h:panelGroup>
                        </h:panelGroup>

                        <!-- Menu lateral -->
                        <jsp:include page="includes/include_menu_relatorios.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <!-- Rodapé -->
            <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="immediate" query="mask('99/99/9999')" />
        </h:panelGroup>
        </body>
        </html>
    </h:form>

    <!-- Scripts finais e includes -->
    <script>
        setarFocusCampoPessoaOuEmpresa();
    </script>

    <%@include file="pessoaSimplificado.jsp" %>
    <%@include file="includes/include_modal_consultarCaixa.jsp" %>
    <%@include file="includes/include_box_fecharCaixas.jsp" %>
    <%@include file="includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="include_modalConfigGestaoRecebiveis.jsp" %>
    <%@include file="includes/include_modal_excessoValorRateio.jsp" %>
    <%@include file="includes/include_modal_confirmaExclusaoRateio.jsp" %>
    <%@include file="includes/include_modal_estornoPagamento.jsp" %>
    <%@include file="cidade.jsp" %>
    <jsp:include page="../../includes/include_panelMensagem_goBackBlock.jsp"/>
</f:view>
