<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/include_imports.jsp" %>
<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">
<head><script type="text/javascript" language="javascript" src="../../script/script.js"></script></head>

<script type="text/javascript" src="../../script/jquery.js"></script>
<script type="text/javascript" src="../../script/jquery.treeTable.js"></script>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>


<c:set var="moduloSession" value="1" scope="session" />
<script type="text/javascript">
    $.noConflict();
</script>

<script type="text/javascript">
            function naoMostrarBotaoEditar(){
                var mostrarBotaoEditar = true;
                return mostrarBotaoEditar;
            }
</script>
<style>

    .equivalencia{
        font-family: Arial, Helvetica, sans-serif;
        font-size: 11px;
        text-decoration: none;
        line-height: normal;
        font-weight: bold;
        text-transform: none;
        color: gray;
    }

    .codigoPlano{
        font-family: Arial, Helvetica, sans-serif;
        font-size: 11px;
        text-decoration: none;
        line-height: normal;
        font-weight: bold;
        text-transform: none;
        color: gray;
    }


    </style>

<f:view>

    <html>
        <!-- Inclui o elemento HEAD da página -->
        <head>
            <%@include file="includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
        </head>
        <body onload="naoMostrarBotaoEditar();">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:form id="formTopo" style="overflow-x: visible !important;">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                </h:panelGroup>
            </h:form>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">

                            <h:panelGroup id="caixaPlano" layout="block" styleClass="container-box zw_ui especial ">

                                <h:panelGroup id="panelGruoupAbasPlanoContaReplicacao" style="min-width: 980px;" styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box"
                                                  style="display:flex; justify-content: space-between; align-items: center">
                                        <h:panelGroup layout="block" styleClass="text" style="margin-top: 6px;">
                                            <h:form id="formBotoesTopo">
                                            <a4j:commandLink
                                                    action="#{PlanoContasControle.voltarPlanoContas}" id="linkGestaoRecebiveis"
                                                    reRender="caixaPlano"
                                                    oncomplete="reRenderMenuLateral()"
                                                    styleClass="tudo"
                                                    style="padding: 15px; display: inline-block; #{PlanoContasControle.visaoReplicarEmpresa ? 'opacity: 0.3;' : 'border-bottom: solid #094771;'}">
                                                <h:outputText styleClass="container-header-titulo"
                                                              value="Plano de Contas "/>
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-plano-de-contas/"
                                                              title="Clique e saiba mais: Plano de Contas"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                </h:outputLink>

                                            </a4j:commandLink>

                                            <a4j:commandLink rendered="#{PlanoContasControle.exibirReplicarRedeEmpresa}"
                                                             action="#{PlanoContasControle.abrirReplicarEmpresa}" id="link_Conciliacao_Recebiveis"
                                                             reRender="caixaPlano"
                                                             styleClass="tudo step6"
                                                             oncomplete="reRenderMenuLateral()"
                                                             style="padding: 15px; display: inline-block; #{PlanoContasControle.visaoReplicarEmpresa ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}">
                                                <h:outputText styleClass="container-header-titulo" value="Replicar Empresa"/>
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlBaseConhecimento}/"
                                                              title="Clique e saiba mais: Replicar Empresa"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                </h:outputLink>

                                            </a4j:commandLink>

                                            </h:form>
                                        </h:panelGroup>

                                    </h:panelGroup>
                                </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="margin-box">

                                        <c:if test="${not PlanoContasControle.visaoReplicarEmpresa}">
                                            <h:form id="formBotoes">
                                                <a4j:commandButton id="adicionar"
                                                                   rendered="#{!PlanoContasControle.bloquearCriacaoPlanoConta}"
                                                                   reRender="painelEdicaoPlanoContas"
                                                                   action="#{PlanoContasControle.incluir}"
                                                                   value="#{msg_bt.btn_adicionar}"
                                                                   oncomplete="Richfaces.showModalPanel('painelEdicaoPlanoContas')"
                                                                   image="/imagens/botaoNovo.png" styleClass="botoes"/>
                                                <rich:spacer width="17px;"></rich:spacer>
                                                <a4j:commandButton
                                                        oncomplete="abrirPopup('visualizarImpressaoPlanoContas.jsp', 'RelatórioPlanoContas', 780, 595);"
                                                        image="../../imagens/btn_VisualizarImpressao.png"
                                                        value="Visualizar Impressão"
                                                >
                                                </a4j:commandButton>
                                            </h:form>
                                            <%@include file="include_planoContasCons.jsp" %>
                                        </c:if>
                                        <%@include file="include_ReplicarEmpresa.jsp" %>
                                    </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:form id="formMenu" style="display: flex;align-items: stretch;flex-direction: row-reverse;">
                            <jsp:include page="includes/include_menu_relatorios.jsp" flush="true"/>
                        </h:form>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
    </html>

    <%@include file="/pages/finan/includes/include_modalRateios.jsp" %>
    <%@include file="/pages/finan/includes/include_modalSelecaoCentroCusto.jsp" %>
    <%@include file="/pages/finan/includes/include_modalSelecaoPlanoConta.jsp" %>

    <!-- ---------------------------------------------- INICIO MODAL DE EDIÇÃO / ADIÇÃO ------------------------------------------------- -->
    <rich:modalPanel id="painelEdicaoPlanoContas" autosized="true"
                     shadowOpacity="true" width="500" height="350">
        <f:facet name="header">
            
                <jsp:include page="cadastros/includes/topoReduzido.jsp" />
            
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Plano de Contas"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelink1" />
                <rich:componentControl for="painelEdicaoPlanoContas"
                                       attachTo="hidelink1" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formEdicaoPlanoContas" ajaxSubmit="true">
            <a4j:commandButton action="#{PlanoContasControle.montarArvorePlano}"
                               id="fechaModal"
                               reRender="form:dados, form:panelMensagem"
                               style="visibility: hidden;"
                               oncomplete="atualizarTreeViewPlanoContas('#{PlanoContasControle.codigoPlanoPaiSelecionado}'); Richfaces.hideModalPanel('painelEdicaoPlanoContas');">
            </a4j:commandButton>
            <h:panelGrid id="formPlano" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">
                <h:outputText rendered="#{PlanoContasControle.plano.codigo gt 0}" styleClass="tituloCampos" value="Código Interno:" />
                <h:outputText rendered="#{PlanoContasControle.plano.codigo gt 0}" styleClass="tituloCampos" value="#{PlanoContasControle.plano.codigo}" />

                <h:outputText rendered="#{PlanoContasControle.plano.codigo gt 0}" styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroPlanoConta_codigoPlano}" />
                <h:panelGroup rendered="#{PlanoContasControle.plano.codigo gt 0}" >
                    <h:inputText disabled="#{!PlanoContasControle.alterarCodigo}" id="alterarCodigoPlano"
                                 size="40" maxlength="255" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form" value="#{PlanoContasControle.plano.codigoPlano}" />
                    <h:panelGroup >
                        <h:selectBooleanCheckbox title="Só é possível alterar o cód. de um plano de contas sem filhos"
                                                 value="#{PlanoContasControle.alterarCodigo}"
                                                 disabled="#{!PlanoContasControle.btAlterarCodPlano}">
                            <a4j:support event="onclick" reRender="alterarCodigoPlano"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroPlanoConta_alterarCodigo}" />
                    </h:panelGroup>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroPlanoConta_planoPai}" />
                <h:panelGroup id="planoconta">
                    <h:inputText disabled="#{PlanoContasControle.disabledCampoPlanoConta}" id="nomePlanoSelecionado"
                                 size="50"
                                 maxlength="50"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{PlanoContasControle.planoNome}" >
                        <a4j:support event="onchange" action="#{PlanoContasControle.setarPlanoPaiVazio}" reRender="codigoPlano, panelMensagem, form"/>
                    </h:inputText>

                    <rich:suggestionbox   height="200" width="400"
                                          for="nomePlanoSelecionado"
                                          status="statusInComponent"
                                          immediate="true"
                                          suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                                          minChars="1"
                                          rowClasses="linhaImpar, linhaPar"
                                          var="result"  id="suggestionResponsavel" >
                        <a4j:support event="onselect"
                                     action="#{PlanoContasControle.selecionarPlanoContas}"
                                     reRender="codigoPlano, panelMensagem, nomePlanoSelecionado, formEdicaoPlanoContas"
                                     oncomplete="#{rich:element('descricao')}.focus();" focus="nomePlanoSelecionado"  >
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome"  styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall" value="#{result.descricaoDetalhada}" />
                        </h:column>
                        <h:column >
                            <f:facet name="header">
                                <h:outputText value="Tipo" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall" value="#{result.tipoPadrao.descricao}" />
                        </h:column>
                    </rich:suggestionbox>
                    <h:panelGroup id="panelTrocarPai">

                        <a4j:commandButton rendered="#{!PlanoContasControle.disabledCampoPlanoConta}"
                                           action="#{PlanoContasControle.verificarConsultaCorreta}"
                                           reRender="panelMensagem, modalPlanos"
                                           id="btAddPlano" value="Consultar"
                                           oncomplete="Richfaces.showModalPanel('modalPlanos')" />
                    </h:panelGroup>
                    <c:if test="${PlanoContasControle.btTrocarPlanoPai}">
                        <h:selectBooleanCheckbox value="#{PlanoContasControle.btTrocarPlanoPaiMarcado}">
                            <a4j:support event="onclick" action="#{PlanoContasControle.habilitarCampoPlanoPai}"
                                         reRender="nomePlanoSelecionado,panelTrocarPai,suggestionResponsavel"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroPlanoConta_trocarPlanoPai}" />
                    </c:if>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroPlanoConta_descricao}" />
                <h:panelGroup>
                    <h:inputText  id="descricao"  size="40" maxlength="255" onblur="blurinput(this);"
                                  onfocus="focusinput(this);" styleClass="form" value="#{PlanoContasControle.plano.descricao}" />
                </h:panelGroup>

                <h:outputText rendered="#{PlanoContasControle.mostrarComboTipoPadrao 
                                          ||PlanoContasControle.mostrarInputTipoPadrao}" styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_Finan_CadastroPlanoConta_tipoPadrao}" />
                <h:inputText rendered="#{PlanoContasControle.mostrarInputTipoPadrao}" id="tipoPadraoReadOnly"
                             readonly="#{PlanoContasControle.mostrarInputTipoPadrao}" size="10" maxlength="40" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form" value="#{PlanoContasControle.plano.tipoPadrao.descricao}" />

                <h:panelGroup rendered="#{PlanoContasControle.mostrarComboTipoPadrao}">
                    <h:selectOneMenu id="tipoPadrao"  onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     style="color:black;"
                                     styleClass="form"
                                     value="#{PlanoContasControle.codigoTipoPadrao}" >
                        <f:selectItems  value="#{PlanoContasControle.listaSelectItemTipoPadrao}" />
                        <a4j:support reRender="formEdicaoPlanoContas" event="onchange"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="Percentual pretendido:" rendered="#{PlanoContasControle.despesa}"/>
                <h:inputText onblur="blurinput(this); " rendered="#{PlanoContasControle.despesa}"
                                     onfocus="focusinput(this);"
                                     onkeypress="return(currencyFormat(this,'.',',',event));"
                                     styleClass="form" maxlength="14"
                                     value="#{PlanoContasControle.plano.percGastoPretendidoDesc}" />

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroPlanoConta_equivalenciaDRE}" />
                <h:panelGroup>
                    <h:selectOneMenu id="equivalenciaDRE"  onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     style="color:black;"
                                     styleClass="form"
                                     value="#{PlanoContasControle.codigoEquivalenciaDRE}" >
                        <f:selectItems  value="#{PlanoContasControle.listaSelectItemTipoEquivalenciaDRE}" />
                        <h:message styleClass="mensagem" for="equivalenciaDRE" />
                    </h:selectOneMenu>
                </h:panelGroup>
				<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroPlanoConta_metaDRE}:" />
				<h:inputText onblur="blurinput(this); "
                                     onfocus="focusinput(this);"
                                     onkeypress="return(currencyFormat(this,'.',',',event));"
                                     styleClass="form" maxlength="14"
                                     value="#{PlanoContasControle.plano.metaDesc}" />

                <c:if test="${PlanoContasControle.configuracaoLumi}">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroPlanoConta_codigoLumi}:" />
                    <h:inputText onblur="blurinput(this); "
                                 onfocus="focusinput(this);"
                                 onkeypress="return isNumero(event);"
                                 styleClass="form" maxlength="8"
                                 value="#{PlanoContasControle.plano.codigoLumi}" />
                </c:if>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_LTV}" title="Marque esta opção para os valores desse plano de contas insidir no cálculo do BI CAC (Custo de Aquisição do Cliente)" />
                <h:selectBooleanCheckbox  value="#{PlanoContasControle.plano.insidirLTV}" id="insidirLTV" title="Marque esta opção para os valores desse plano de contas insidir no cálculo do BI CAC (Custo de Aquisição do Cliente)" />
            </h:panelGrid>
            <br/>
            <center>
                <a4j:commandButton id="novo" reRender="formEdicaoPlanoContas"
                                   rendered="#{!PlanoContasControle.bloquearCriacaoPlanoConta}"
                                   action="#{PlanoContasControle.incluir}" value="#{msg_bt.btn_adicionar}"
                                   image="/imagens/botaoNovo.png" styleClass="botoes"/>
                <rich:spacer width="10"/>
                <a4j:commandButton id="btnGravarModalPlanoContas"
                                   action="#{PlanoContasControle.salvar}" reRender="formEdicaoPlanoContas,form:dados"
                                   oncomplete="atualizarTreeViewPlanoContas('#{PlanoContasControle.codigoPlanoPaiSelecionado}');"
                                   image="/imagens/botaoGravar.png"  styleClass="botoes"/>
                <rich:spacer width="10"/>
                <a4j:commandButton id="rateioCentroCusto"
                                   rendered="#{PlanoContasControle.plano.codigo gt 0}"
                                   action="#{PlanoContasControle.ratearCentroCustos}"
                                   reRender="modalIncluirRateio, dadosRateio"
                                   image="/imagens/botaoRatearCentrodeCustos.png"  styleClass="botoes" />
                <a4j:commandButton rendered="#{PlanoContasControle.btExcluir}" 
                                   reRender="formEdicaoPlanoContas,panelMensagem,form:dados, formTrocarPlano"
                                   image="/imagens/botaoExcluir.png"
                                   oncomplete="#{PlanoContasControle.fecharModalExclusao}; atualizarTreeViewPlanoContas('#{PlanoContasControle.codigoPlanoPaiSelecionado}');"
                                   onclick="if(!confirm('Deseja realmente excluir este plano de contas?')) {return false;}"
                                   action="#{PlanoContasControle.excluir}"
                                   />
                <rich:spacer width="10"/>
                <a4j:commandButton id="fechar"
                                   oncomplete="Richfaces.hideModalPanel('painelEdicaoPlanoContas')"
                                   image="/imagens/botaoFechar.png" styleClass="botoes"/>
            </center>
            <br/>
            <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        
                            <h:outputText value=" "/>
                        
                    </h:panelGrid>
                    <h:commandButton rendered="#{PlanoContasControle.sucesso}" image="/imagens/sucesso.png"/>
                    <h:commandButton rendered="#{PlanoContasControle.erro}" image="/imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoContasControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoContasControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <br/>


        </a4j:form>
    </rich:modalPanel>

    <!-- ---------------------------------------------- FIM MODAL DE EDIÇÃO / ADIÇÃO ------------------------------------------------- -->


<a4j:outputPanel> 
<!-- Modal de Exclusão -->
    <rich:modalPanel id="modalTrocarPlano" autosized="true"
                     shadowOpacity="true" width="500" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Trocar Plano para Exclusão"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinktrocar" />
                <rich:componentControl for="modalTrocarPlano"
                                       attachTo="hidelinktrocar" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>


        <a4j:form id="formTrocarPlano">
            <center>
                <h:outputText styleClass="tituloBold" value="Existem registros no sistema vinculados à este Plano de contas. Para continuar, informe para qual Plano de Contas irão os lançamentos antigos do plano a ser excluído"/>
            </center>
            <br/>
            <h:panelGrid columns="2" width="100%">
	            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_planoContas}"/>
				<h:panelGroup>
				    <%@include file="includes/include_SuggestionPlanoConta.jsp" %>
				</h:panelGroup>
            
            </h:panelGrid>
            <br/>
            <center>
            	<a4j:commandButton value="Confirmar" image="/images/finan/confirmar.png" action="#{PlanoContasControle.excluirTrocando}"
            					   reRender="formEdicaoPlanoContas,panelMensagem,form:dados"
            					   oncomplete="atualizarTreeViewPlanoContas('#{PlanoContasControle.codigoPlanoPaiSelecionado}'); #{PlanoContasControle.fecharModalExclusao}; #{PlanoContasControle.msgAlert}">
            	
            	</a4j:commandButton>
            	&nbsp;
            	<a4j:commandButton value="Cancelar" image="/imagens/Cancelar_Modal.png"
            					   oncomplete="Richfaces.hideModalPanel('modalTrocarPlano');"
            					   status="false">
            	</a4j:commandButton>
            
            </center>

        </a4j:form>

    </rich:modalPanel>
</a4j:outputPanel> 
<!-- FIM: Modal de Exclusão -->

    
    
    <%@include file="includes/include_carregando.jsp" %>
</f:view>

