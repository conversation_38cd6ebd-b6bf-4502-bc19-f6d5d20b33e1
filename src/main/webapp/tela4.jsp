<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="script/negociacaoContrato_1.0.min.js"></script>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
    setDocumentCookie('popupsImportante', 'close',1);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_QuestionarioCliente_tituloForm}"/>
    </title>
    <style>
        .fa-icon-check-empty:before{
            margin-right: 3px;
        }
    </style>
    <rich:modalPanel id="panelExisteFreePass" styleClass="novaModal" autosized="true" shadowOpacity="true" showWhenRendered="#{ClienteControle.existeFreePass}" width="300" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText id="ttAtencao" value="Atenção!"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formExisteFreePass">
            <h:panelGroup id="gridFreePass" layout="block" styleClass="col-text-align-center">
                <h:panelGrid id="gridAvisoFreePass" columns="1" footerClass="colunaCentralizada" width="100%">
                    <h:outputText id="msgAvisoFreePass" styleClass="tituloCampos" value="#{ClienteControle.msgFreePass}"/>
                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="botaoGravarCliente" action="#{ClienteControle.abrirModalExisteFreePass}"
                                     reRender="voltar, panelMesangem,gravar,cancelar,imprimir,questionarioCliente,panelMesangem1,gravar1,cancelar1,imprimir1,foto,consultor,data,textarea,panelFreePass,panelVinculo,panelAutorizacaoFuncionalidade"
                                     oncomplete="#{ClienteControle.mensagemNotificar}"
                                     value="#{msg_bt.btn_sim}"
                                     accesskey="5" styleClass="botaoPrimario texto-size-16"/>
                    <a4j:commandLink  action="#{ClienteControle.fecharPanelExisteFreePass}"
                                      id="btnNaoFreePass"
                                      reRender="voltar, panelMesangem,gravar,cancelar,imprimir,questionarioCliente,panelMesangem1,gravar1,cancelar1,imprimir1,foto,consultor,data,textarea,panelFreePass,panelVinculo"
                                      onclick="Richfaces.hideModalPanel('panelExisteFreePass')"
                                      value="#{msg_bt.btn_nao}" style="margin-left: 5px;"  accesskey="6" styleClass="botaoSecundario texto-size-16"/>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelExisteFreePassResponderDepois" styleClass="novaModal" autosized="true" shadowOpacity="true"
                     showWhenRendered="#{ClienteControle.existeFreePassResponderDepois}" width="300" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText id="ttAtencaoRes" value="Atenção!"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formExisteFreePassResponderDepois" styleClass="font-size-Em">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                    <h:outputText id="msgFressPassResDep" styleClass="tituloCampos" value="#{ClienteControle.msgFreePass}"/>
                </h:panelGrid>
                <h:panelGroup styleClass="container-botoes" layout="block">
                        <a4j:commandLink id="botaoGravarCliente" action="#{ClienteControle.abrirModalExisteFreePassResponderDepois}"
                                         oncomplete="#{ClienteControle.mensagemNotificar}"
                                         reRender="voltar, panelMesangem,gravar,cancelar,imprimir,questionarioCliente,panelMesangem1,gravar1,cancelar1,imprimir1,foto,consultor,data,textarea,panelFreePass,panelVinculo,panelAutorizacaoFuncionalidade"
                                         value="#{msg_bt.btn_sim}"  accesskey="5" styleClass="botaoPrimario texto-size-16"/>
                        <a4j:commandLink id="btnNaoRes"  style="margin-left: 5px;"
                                         action="#{ClienteControle.fecharPanelExisteFreePassResponderDepois}"
                                         reRender="voltar, panelMesangem,gravar,cancelar,imprimir,questionarioCliente,panelMesangem1,gravar1,cancelar1,imprimir1,foto,consultor,data,textarea,panelFreePass,panelVinculo"
                                         onclick="Richfaces.hideModalPanel('panelExisteFreePassResponderDepois')"
                                         value="#{msg_bt.btn_nao}"
                                         accesskey="6" styleClass="botaoSecundario texto-size-16"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalConfirmacaoGravarBV" autosized="true" styleClass="novaModal" shadowOpacity="true" width="450" height="130">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalConfirmacaoGravarBV"/>
                <rich:componentControl for="modalConfirmacaoGravarBV" attachTo="hidelinkModalConfirmacaoGravarBV" operation="hide"  event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formGravarBV">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup id="textoInformativo" style="margin: 10px 0px 10px 0px;" styleClass="font-size-Em texto-size-20 texto-cor-cinza-3 texto-font container-botoes">
                    <h:outputText styleClass="mensagem" style="line-height: 50px;" value="As respostas estão incompletas, deseja continuar assim mesmo?"/>
                </h:panelGroup>
                <h:panelGrid id="panelResponsavelPagamentoResponderDepois" columns="2" width="100%" columnClasses="colunaEsquerda" styleClass="font-size-Em ">
                    <a4j:commandLink id="btnSimRealizarNegociacao" rendered="#{ClienteControle.btnRealizarNegociacao}"
                                       style="float: right" value="Sim"
                                       styleClass="botaoPrimario texto-size-14"
                                       reRender="panelExisteFreePassResponderDepois"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmacaoGravarBV');#{ClienteControle.mostrarPanelResponderDepois}#{ClienteControle.msgAlert}"
                                       action="#{ClienteControle.validarResponderQuestionarioDepoisRedirect}"/>

                    <a4j:commandLink id="btnSimCadastrarVisitante" rendered="#{ClienteControle.btnCadastrarVisitante}"
                                       style="float: right" value="Sim"
                                       styleClass="botaoPrimario texto-size-14"
                                       reRender="panelExisteFreePassResponderDepois"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmacaoGravarBV');#{ClienteControle.mostrarPanelResponderDepois}#{ClienteControle.msgAlert}"
                                       action="#{ClienteControle.cadastrarVisitanteSemResponderBV}"/>
                    <a4j:commandLink id="btnSimFinalizarVenda" rendered="#{ClienteControle.btnFinalizarVenda}"
                                       style="float: right" value="Sim"
                                       styleClass="botaoPrimario texto-size-14"
                                       reRender="panelExisteFreePass"
                                       action="#{ClienteControle.gravarFinalizandoVenda}"/>

                    <a4j:commandLink id="btnNão" value="Não" action="#{ClienteControle.validarQuestionarioRespondido}"
                                       styleClass="botaoSecundario texto-size-14"
                                       reRender="mensagem, mensagemDetalhada, form:msgBvDet, form:msgBvDet1"
                                       onclick="Richfaces.hideModalPanel('modalConfirmacaoGravarBV')"/>
                </h:panelGrid>

                <h:panelGrid id="mensagem2">
                    <%--<h:outputText styleClass="mensagem" value="#{ClienteControle.mensagem}"/>--%>
                    <%--<h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>--%>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:form id="form">
        <jsp:include page="include_head.jsp" flush="true" />
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item4" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central" style="position:relative;">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="Questionário"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup id="panelbotoes" layout="block" styleClass="container-box-header bg-cinza">
                                    <h:panelGroup layout="block" styleClass="margin-container col-text-align-right" style="width: 96%;margin-right: 2%;float: right;">
                                        <a4j:commandLink id="voltar" action="#{ClienteControle.voltar}"
                                                           rendered="#{!ClienteControle.finalizarVenda && !ClienteControle.processandoOperacao}"
                                                           accesskey="2" styleClass="linkPadrao texto-cor-azul texto-size-16 pull-left">
                                            <h:outputText styleClass="fa-icon-arrow-left "/>
                                            <h:outputText  value=" Voltar" />
                                        </a4j:commandLink>
                                        <a4j:commandLink id="cadastrarVisitante" value="#{ClienteControle.textoBotaoVisitante}"
                                                         action="#{ClienteControle.cadastrarVisitante}"
                                                         reRender="formGravarBV, form:panelMesangem, form:panelMesangem1,panelExisteFreePass,formExisteFreePass"
                                                         oncomplete="#{ClienteControle.mostrarPanelResponderDepois}"
                                                         rendered="#{!ClienteControle.finalizarVenda && !ClienteControle.processandoOperacao && ClienteControle.retornoBV == ''}"
                                                         style="margin-left: 1em;"
                                                             title="Salvar BV e voltar para tela do cliente" accesskey="2" styleClass="linkPadrao texto-cor-azul texto-size-16"/>

                                        <a4j:commandLink id="realizarNegociacao" value="Realizar Negociação"
                                                               action="#{ContratoControle.novoContratoConcomitanteViaTela4Jsf}"
                                                               reRender="formGravarBV, form:panelMesangem, form:panelMesangem1,panelExisteFreePass,formExisteFreePass"
                                                               rendered="#{!ClienteControle.finalizarVenda && !ClienteControle.clienteSessao && !ContratoControle.novaNegociacaoPadrao && !ClienteControle.processandoOperacao && ClienteControle.retornoBV == ''}"
                                                               oncomplete="#{ClienteControle.mostrarPanelResponderDepois}"
                                                               style="margin-left: 1em;"
                                                               accesskey="2" styleClass="botaoPrimario texto-size-16"/>

                                        <a4j:commandLink id="realizarNegociacaoNova" value="Realizar Negociação"
                                                               action="#{ContratoControle.abrirNovNegociacaoBV}"
                                                               reRender="formGravarBV, form:panelMesangem, form:panelMesangem1,panelExisteFreePass,formExisteFreePass"
                                                               rendered="#{!ClienteControle.finalizarVenda && !ClienteControle.clienteSessao && ContratoControle.novaNegociacaoPadrao && !ClienteControle.processandoOperacao && ClienteControle.retornoBV == ''}"
                                                               oncomplete="#{ContratoControle.msgAlert}#{ClienteControle.mostrarPanelResponderDepois}"
                                                               style="margin-left: 1em;"
                                                               accesskey="2" styleClass="botaoPrimario texto-size-16"/>

                                        <a4j:commandLink id="finalizarCompra" value="Finalizar Venda"
                                                           action="#{ClienteControle.finalizarVenda}"
                                                           reRender="formGravarBV, form:panelMesangem, form:panelMesangem1,panelExisteFreePass,formExisteFreePass"
                                                           oncomplete="#{ClienteControle.mostrarPanelResponderDepois}"
                                                           title="#{msg.msg_finalizar_venda}"
                                                           style="margin-left: 1em;"
                                                           rendered="#{LoginControle.apresentarLinkEstudio && ClienteControle.finalizarVenda && !ClienteControle.processandoOperacao && ClienteControle.retornoBV == ''}"
                                                           accesskey="2" styleClass="botaoPrimario texto-size-16"/>
                                       <h:outputText  rendered="#{ClienteControle.processandoOperacao || ClienteControle.retornoBV != ''}" id="msgProcessando" styleClass="mensagem"  value="Operação já está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                                       <rich:spacer height="7"/>
                                       <a4j:commandLink id="AtualizarOP" value="Atualizar"
                                                           action="#{ClienteControle.atualizarTelaBv}"
                                                           reRender="panelbotoes,panelbotoes2,formGravarBV, form:panelMesangem, form:panelMesangem1,panelExisteFreePass,formExisteFreePass"
                                                           oncomplete="window.location.reload();"
                                                           title="#{msg.msg_finalizar_venda}"
                                                           style="margin-left: 1em;"
                                                           rendered="#{ClienteControle.processandoOperacao || ClienteControle.retornoBV != ''}"
                                                           accesskey="2" styleClass="botaoPrimario texto-size-16"/> 
                                    </h:panelGroup>
                                </h:panelGroup>
                                
                                <a4j:jsFunction name="updateFoto" action="#{ClienteControle.recarregarFotoBV}" reRender="menuBVFoto"/>
                                
                                <h:panelGroup layout="block"  style="margin: 20px 0px 0px 30px;width: auto;">
                                    <h:panelGroup id="menuBVFoto" layout="block" style="margin: 0;width: 57%;" styleClass="caixaEsquerda">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font-size-Em">
                                        <tr>
                                            <td align="left" valign="top" bgcolor="#ffffff">
                                                <table width="100%" border="0" cellpadding="0" cellspacing="0" bgcolor="#ffffff" class="font-size-Em">
                                                    <tr>
                                                        <td align="center" valign="top" style="padding-bottom:15px;">
                                                                <a4j:commandLink actionListener="#{CapturaFotoControle.selecionarPessoa}"
                                                                    action="#{CapturaFotoControle.vazio}"                                           
                                                                    id="btnAlterarFoto" 
                                                                    oncomplete="
                                                                        setAttributesModalCapFoto('#{ClienteControle.key}',
                                                                            '#{ClienteControle.clienteVO.pessoa.codigo}', 
                                                                            '#{ClienteControle.contextPath}',
                                                                            '#{ClienteControle.usuarioLogado.codigo}');
                                                                        Richfaces.showModalPanel('modalCapFotoHTML5');"
                                                                    title="Capturar Foto" 
                                                                    styleClass="botoes tooltipsterright"
                                                                    style="display: flow-root;margin-right: 105px;">
                                                                    <f:attribute name="pessoa" value="#{ClienteControle.clienteVO.pessoa.codigo}"/>
                                                                    <i class="fa-icon-camera texto-size-18" style="text-decoration: none;"></i>
                                                                </a4j:commandLink>
                                                                <a4j:mediaOutput element="img" id="imagemFoto"
                                                                             rendered="#{!SuperControle.fotosNaNuvem}"
                                                                             style="border-radius: 50%;width: 150px; height: 150px;"
                                                                             cacheable="false"
                                                                             createContent="#{ClienteControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                                                                     <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                                                 </a4j:mediaOutput>
                                                                 <a4j:mediaOutput element="img" id="imagemFotoBv"
                                                                                  rendered="#{SuperControle.fotosNaNuvem and SuperControle.fotoTempBVExiste}"
                                                                             style="border-radius: 50%;width: 150px; height: 150px;"
                                                                             cacheable="false"
                                                                             createContent="#{ClienteControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                                                                     <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                                                 </a4j:mediaOutput>
                                                                 <h:graphicImage rendered="#{SuperControle.fotosNaNuvem and !SuperControle.fotoTempBVExiste}"
                                                                            style="border-radius: 50%;width: 150px; height: 150px;"
                                                                            url="#{ClienteControle.paintFotoDaNuvem}"/>
                                                            <h:outputText id="nomeCliente" style="display: block" styleClass="texto-size-16 texto-bold texto-font texto-cor-cinza padding-11" value="#{ClienteControle.pessoaVO.nome}" />
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td align="left" valign="top">
                                                            <table width="100%" border="0" cellspacing="0" cellpadding="2" class="font-size-Em">


                                                                <tr>
                                                                    <td align="left" valign="middle" >
                                                                        <h:panelGroup layout="block" style="width: 60%;margin-left: 20%" styleClass="pull-left">
                                                                            <h:outputText styleClass="rotuloCampos margenVertical"
                                                                                    style="display: block;"
                                                                                    value="#{msg_aplic.prt_QuestionarioCliente_consultor_maiusculo}"/>
                                                                            <h:panelGroup id="pnlConsultor" layout="block" styleClass="cb-container margenVertical" style="width: 100%;">
                                                                                <h:selectOneMenu id="consultor"
                                                                                                 onblur="blurinput(this);"
                                                                                                 onfocus="focusinput(this);"
                                                                                                 styleClass="form"
                                                                                                 disabled="#{not ClienteControle.permitirAlterarConsultor}"
                                                                                                 value="#{ClienteControle.questionarioClienteVO.consultor.codigo}">
                                                                                    <f:selectItems
                                                                                            value="#{ClienteControle.listaSelectConsultor}"/>
                                                                                    <a4j:support event="onchange"
                                                                                                 actionListener="#{ClienteControle.selecionouConsultor}" reRender="mdlMensagemGenerica" oncomplete="#{ClienteControle.modalMensagemGenerica}"/>
                                                                                </h:selectOneMenu>
                                                                            </h:panelGroup>

                                                                            <h:panelGroup layout="block" style="text-align: right">
                                                                            <a4j:commandLink id="atualizar_consultor"
                                                                                             styleClass="linkPadrao col-text-align-right margenVertical"
                                                                                               rendered="#{ClienteControle.permitirAlterarConsultor}"
                                                                                               action="#{ClienteControle.montarListaSelectItemConsultor}"
                                                                                               immediate="true"
                                                                                               ajaxSingle="true"
                                                                                               reRender="form:consultor">
                                                                                <h:outputText styleClass="fa-icon-refresh texto-cor-azul texto-size-16"/>
                                                                                <h:outputText style="margin-left: 5px" styleClass="texto-font texto-cor-azul texto-size-16" value="Atualizar"/>
                                                                            </a4j:commandLink>

                                                                            <a4j:commandLink id="cadastro_consultor"
                                                                                             style="margin-left: 12px"
                                                                                               styleClass="linkPadrao col-text-align-right margenVertical"
                                                                                               rendered="#{ClienteControle.permitirAlterarConsultor}"
                                                                                               action="#{ColaboradorControle.novoConsultor}"
                                                                                               oncomplete="abrirPopup('colaboradorForm.jsp', 'Colaborador', 903,620);">
                                                                                <h:outputText styleClass="fa-icon-plus-sign texto-cor-azul texto-size-16"/>
                                                                                <h:outputText style="margin-left: 5px" styleClass="texto-font texto-cor-azul texto-size-16" value="Novo consultor"/>
                                                                            </a4j:commandLink>
                                                                            </h:panelGroup>
                                                                        </h:panelGroup>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td align="left" valign="middle">
                                                                    <h:panelGroup layout="block" style="width: 60%;margin-left: 20%" styleClass="pull-left">
                                                                        <h:outputText style="display: block" styleClass="rotuloCampos margenVertical" value="#{msg_aplic.prt_QuestionarioCliente_evento_maiusculo}" />
                                                                        <h:panelGroup layout="block"
                                                                                      styleClass="cb-container margenVertical" style="width: 100%">
                                                                            <h:selectOneMenu id="evento"
                                                                                             onblur="blurinput(this);"
                                                                                             onfocus="focusinput(this);"
                                                                                             styleClass="form"
                                                                                             value="#{ClienteControle.questionarioClienteVO.eventoVO.codigo}">
                                                                                <f:selectItems
                                                                                        value="#{ClienteControle.listaSelectEvento}"/>
                                                                            </h:selectOneMenu>
                                                                        </h:panelGroup>
                                                                        <%--<a4j:commandButton id="atualizar_evento" action="#{ClienteControle.getListaSelectEvento}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:evento"/>--%>
                                                                            <a4j:commandLink id="cadastro_evento" action="#{EventoControle.novoEvento}"
                                                                                             rendered="#{LoginControle.permissaoAcessoMenuVO.evento}"
                                                                                             styleClass="linkPadrao col-text-align-right margenVertical"
                                                                                             style="display: block"
                                                                                             oncomplete="abrirPopup('eventoForm.jsp', 'Evento', 903,620);" >
                                                                                <h:outputText styleClass="fa-icon-plus-sign texto-cor-azul texto-size-16"/>
                                                                                <h:outputText style="margin-left: 5px" styleClass="texto-font texto-cor-azul texto-size-16" value="Novo evento"/>
                                                                            </a4j:commandLink>
                                                                    </h:panelGroup>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td align="left" valign="middle">
                                                                    <h:panelGroup layout="block" style="width: 60%;margin-left: 20%" styleClass="pull-left" id="painelDataBoletim">
                                                                        <h:outputText style="display: block" styleClass="rotuloCampos margenVertical" value="#{msg_aplic.prt_QuestionarioCliente_data_maiusculo}" />
                                                                        <h:panelGroup layout="block" styleClass="dateTimeCustom" style="width: 100%;">
                                                                            <rich:calendar id="data"
                                                                                           value="#{ClienteControle.questionarioClienteVO.data}"
                                                                                           inputSize="10"
                                                                                           inputClass="form"
                                                                                           oninputblur="blurinput(this);"
                                                                                           oninputfocus="focusinput(this);"
                                                                                           oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                                                                           datePattern="dd/MM/yyyy"
                                                                                           enableManualInput="true"
                                                                                           zindex="2"
                                                                                           showWeeksBar="false">
                                                                                    <a4j:support event="oninputchange"
                                                                                                 action="#{ClienteControle.autorizarDataBoletim}"
                                                                                         reRender="painelDataBoletim, panelAutorizacaoFuncionalidade"></a4j:support>
                                                                                    <a4j:support event="onchanged"
                                                                                                 action="#{ClienteControle.autorizarDataBoletim}"
                                                                                                 reRender="painelDataBoletim, panelAutorizacaoFuncionalidade"></a4j:support>
                                                                            </rich:calendar>
                                                                            <h:message for="data" styleClass="mensagemDetalhada"/>
                                                                        </h:panelGroup>
                                                                    </h:panelGroup>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td align="left" valign="middle">
                                                                        <h:panelGroup layout="block" style="width: 60%;margin-left: 20%" styleClass="pull-left" rendered="#{LoginControle.permissaoAcessoMenuVO.permissaoFreePass}">
                                                                            <h:outputText  style="display: block" styleClass="rotuloCampos margenVertical" value="#{msg_aplic.prt_Cliente_freePass_maiusculo}" />
                                                                            <h:panelGroup layout="block" styleClass="cb-container">
                                                                                <h:selectOneMenu  id="freePass" styleClass="camposObrigatorios" value="#{ClienteControle.clienteVO.freePass.codigo}" >
                                                                                    <f:selectItems  value="#{ClienteControle.listaProdutoFreePass}" />
                                                                                </h:selectOneMenu>
                                                                                <%--<a4j:commandButton id="atualizar_freePass" action="#{ClienteControle.montarListaSelectItemFreePass}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:freePass"/>--%>
                                                                            </h:panelGroup>
                                                                        </h:panelGroup>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td colspan="2" align="left" valign="middle">
                                                                        <h:panelGroup layout="block" style="width: 80%;margin:20px 0px 20px 10%;" styleClass="pull-left">
                                                                            <h:panelGroup id="containerObservacao" style="border-top: 1px solid #DDE7E7;border-bottom: 1px solid #DDE7E7;line-height: 3em;" layout="block">
                                                                                <a4j:commandLink rendered="#{!ClienteControle.exibirObservacaoQuestionario}" styleClass="linkPadrao" reRender="containerObservacao">
                                                                                    <h:outputText style="margin-left: 60px" styleClass="texto-font texto-cor-azul texto-size-16" value="Adicionar observação"/>
                                                                                    <f:setPropertyActionListener value="#{!ClienteControle.exibirObservacaoQuestionario}" target="#{ClienteControle.exibirObservacaoQuestionario}"/>
                                                                                </a4j:commandLink>
                                                                                <h:inputTextarea rendered="#{ClienteControle.exibirObservacaoQuestionario}" styleClass="inputTextClean col-text-align-left" onblur="blurinput(this);"  onfocus="focusinput(this);"   id="textarea" cols="3" rows="50" style="width:73%;height:90px;margin: 20px 0px 20px 0px" value="#{ClienteControle.questionarioClienteVO.observacao}"/>
                                                                            </h:panelGroup>
                                                                        </h:panelGroup>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <h:panelGrid id="panelVinculo" rendered="#{ClienteControle.permitirAlterarConsultor}"  columns="1" width="100%" styleClass="font-size-Em">

                                                    <h:panelGroup layout="block" style="width: 60%;margin-left: 20%" styleClass="pull-left col-text-align-left">
                                                        <h:outputText  style="display: block" styleClass="rotuloCampos margenVertical"
                                                                       value="#{msg_aplic.prt_Vinculo_NaoObrigatorio_tipoVinculo_maiusculo}"  />
                                                        <h:panelGroup layout="block" styleClass="cb-container" style="width: 100%">
                                                            <h:selectOneMenu disabled="#{!ClienteControle.permissaoAlterarVinculosClienteColaborador}"  id="tipoVinculo"  styleClass="camposObrigatorios" value="#{ClienteControle.vinculoVO.tipoVinculo}" >
                                                                <a4j:support event="onchange" action="#{ClienteControle.montarTipoVinculoColaboradorSemLimparQuestionario}" reRender="panelVinculo"/>
                                                                <f:selectItems  value="#{ClienteControle.listaSelectItemTipoVinculoSemConsultor}" />
                                                            </h:selectOneMenu>
                                                        </h:panelGroup>
                                                    </h:panelGroup>

                                                    <h:panelGroup layout="block" style="width: 60%;margin-left: 20%" styleClass="pull-left col-text-align-left font-size-Em">
                                                        <h:outputText  style="display: block" styleClass="rotuloCampos margenVertical"
                                                                       value="#{msg_aplic.prt_Vinculo_NaoObrigatorio_colaborador_maiusculo}"  />
                                                        <h:panelGroup layout="block" styleClass="cb-container" style="width: 100%">
                                                            <h:selectOneMenu  id="colaboradorVinculo" styleClass="camposObrigatorios" disabled="#{!ClienteControle.permissaoAlterarVinculosClienteColaborador}" value="#{ClienteControle.vinculoVO.colaborador.codigo}" >
                                                                <f:selectItems  value="#{ClienteControle.listaSelectVinculoColaborador}" />
                                                            </h:selectOneMenu>
                                                            <%--<a4j:commandButton id="atualizar_colaborador" action="#{ClienteControle.montarTipoVinculoColaborador}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:colaboradorVinculo"/>--%>
                                                        </h:panelGroup>
                                                    </h:panelGroup>

                                                    <h:panelGroup layout="block" styleClass="container-botoes font-size-Em">
                                                        <a4j:commandLink id="adicionarVinculor" action="#{ClienteControle.adicionarVinculo}"
                                                                     focus="tipoVinculo"
                                                                     styleClass="linkPadrao texto-size-16"
                                                                     style="margin-left: 206px;"
                                                                     reRender="panelVinculo,panelExistePessoa"
                                                                     oncomplete="#{ClienteControle.mensagemNotificar}">
                                                            <h:outputText styleClass="fa-icon-plus-sign texto-cor-azul texto-size-16"/>
                                                            <h:outputText style="margin-left: 5px;" styleClass="texto-font texto-cor-azul texto-size-16" value="Adicionar Vínculo"/>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>

                                                    <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                                        <rich:dataTable  id="clienteVinculoVO" width="100%" styleClass="tabelaSimplesCustom font-size-Em"
                                                                      rendered="#{not empty ClienteControle.listaVinculoSemConsultor}"
                                                                      value="#{ClienteControle.listaVinculoSemConsultor}" var="vinculo"  >
                                                            <rich:column rendered="#{vinculo.tipoVinculo != 'CO'}"  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                <f:facet name="header">
                                                                    <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Vinculo_NaoObrigatorio_tipoVinculo_maiusculo}" />
                                                                </f:facet>
                                                                <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16" rendered="#{vinculo.tipoVinculo != 'CO'}"  value="#{vinculo.tipoVinculo_Apresentar}" />
                                                            </rich:column>
                                                            <rich:column  rendered="#{vinculo.tipoVinculo != 'CO'}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                <f:facet name="header">
                                                                    <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Vinculo_NaoObrigatorio_colaborador_maiusculo}" />
                                                                </f:facet>
                                                                <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16" rendered="#{vinculo.tipoVinculo != 'CO'}" value="#{vinculo.colaborador.pessoa.nome}" />
                                                            </rich:column>
                                                            <rich:column  rendered="#{vinculo.tipoVinculo != 'CO'}" styleClass="col-text-align-right" headerClass="col-text-align-right">
                                                                <h:panelGroup rendered="#{vinculo.tipoVinculo != 'CO'}">
                                                                    <a4j:commandLink  rendered="#{vinculo.tipoVinculo != 'CO'}" id="removerItemVenda"
                                                                                      action="#{ClienteControle.removerVinculoBV}"                                                                                       reRender="panelVinculo, panelMesangem1,consultor" accesskey="7" styleClass="linkPadrao">
                                                                        <h:outputText styleClass="fa-icon-minus-sign texto-cor-vermelho texto-size-16"/>
                                                                    </a4j:commandLink>
                                                                </h:panelGroup>
                                                            </rich:column>
                                                        </rich:dataTable>
                                                    </h:panelGrid>
                                                </h:panelGrid>

                                                <%--Gympass a partir daqui--%>

                                                <h:panelGroup rendered="#{ClienteControle.mostrarAbaGympass}" layout="block" style="text-align: center; margin-top: 20px; margin-bottom: 10px;">
                                                    <h:outputText value="GYMPASS" styleClass="tituloGympass" />
                                                </h:panelGroup>

                                                <h:panelGrid id="panelGympass" rendered="#{ClienteControle.mostrarAbaGympass}"  columns="1" width="100%" styleClass="font-size-Em">

                                                    <h:panelGroup layout="block" style="width: 60%;margin-left: 20%" styleClass="pull-left col-text-align-left font-size-Em">
                                                        <h:outputText  style="display: block" styleClass="rotuloCampos margenVertical"
                                                                       value="#{msg_aplic.prt_Tipo_Acesso_Maiusculo}"  />
                                                        <h:panelGroup layout="block" styleClass="cb-container" style="width: 100%">
                                                            <h:selectOneMenu id="tipoGympass" styleClass="camposObrigatorios" value="#{ClienteControle.clienteVO.gympassTypeNumber}">
                                                                <f:selectItem itemLabel=" " itemValue="#{null}"/>
                                                                <f:selectItems value="#{ClienteControle.listaTipoGymPass}"/>
                                                            </h:selectOneMenu>
                                                        </h:panelGroup>
                                                    </h:panelGroup>

                                                    <h:panelGroup layout="block" style="width: 60%;margin-left: 20%" styleClass="pull-left col-text-align-left">
                                                        <h:outputText  style="display: block" styleClass="rotuloCampos margenVertical" value="TOKEN GYMPASS: " />

                                                        <h:inputText id="tokenGympass"  size="55" maxlength="100" onblur="blurinput(this);"
                                                                     onfocus="focusinput(this);" styleClass="form tamanhoInputGrande"
                                                                     value="#{ClienteControle.clienteVO.gympasUniqueToken}" />

                                                    </h:panelGroup>

                                                    <h:panelGroup rendered="#{ClienteControle.mostrarProdutoGympass}" layout="block" style="width: 60%;margin-left: 20%" styleClass="pull-left col-text-align-left font-size-Em">
                                                        <h:outputText  style="display: block" styleClass="rotuloCampos margenVertical"
                                                                       value="PRODUTO GYMPASS"  />
                                                        <h:panelGroup layout="block" styleClass="cb-container" style="width: 100%">
                                                            <h:selectOneMenu id="produtoGympass" styleClass="camposObrigatorios" value="#{ClienteControle.produtoGympassVO.codigo}">
                                                                <f:selectItems value="#{ClienteControle.listaSelectItemProdutoGympass}"/>
                                                            </h:selectOneMenu>
                                                        </h:panelGroup>
                                                    </h:panelGroup>

                                                </h:panelGrid>

                                            </td>
                                        </tr>
                                    </table>
                                </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="caixaDireita fundoCinza" >
                                        <h:dataTable id="questionarioCliente"  styleClass="tabelaSimplesCustom"  value="#{ClienteControle.questionarioClienteVO.questionarioPerguntaClienteVOs}" var="pergunta"  width="80%">
                                            <h:column >
                                                <h:panelGroup layout="block" styleClass="margenVertical" style="border-bottom: 1px solid #9E9E9E;width: 94%;margin: 5px 3%;">
                                                    <h:outputText styleClass="rotuloCampos textoImcompleto tooltipster"
                                                                  style="display:inline-block;max-width: 94%;white-space: pre-wrap; padding: 3px"
                                                                  title="#{pergunta.perguntaCliente.descricao}"
                                                                  value="#{pergunta.perguntaCliente.descricao}" />

                                                    <h:outputText styleClass="rotuloCampos textoImcompleto tooltipster"
                                                                  style="float: right"
                                                                  rendered="#{pergunta.perguntaCliente.obrigatoria}"
                                                                  value="*Obrigatória" />
                                                </h:panelGroup>

                                                <h:dataTable id="tabela01" style="margin-left: 5%;cursor: pointer"  value="#{pergunta.perguntaCliente.respostaPergClienteVOs}" var="repostaPergCliente"  width="90%"
                                                             rendered="#{pergunta.perguntaCliente.tipoPergunta ne 'NS'}">
                                                    <h:column>
                                                        <h:panelGroup layout="block"   rendered="#{pergunta.perguntaCliente.multipla}" style="display: inline-block;" styleClass="chk-fa-container">
                                                            <%--<a4j:commandLink  styleClass="linkPadrao" id="idPerguntaClienteMultipla" reRender="questionarioCliente">--%>
                                                                <%--<f:setPropertyActionListener  value="#{!repostaPergCliente.respostaOpcao}" target="#{repostaPergCliente.respostaOpcao}"/>--%>
                                                                <%--<h:outputText style="font-family: Arial" styleClass="#{repostaPergCliente.respostaOpcao ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-azul texto-font texto-size-16" />--%>
                                                                <%--<h:outputText styleClass="texto-size-16-real texto-cor-azul texto-font"  style="display: inline-block;padding-left: 6px;cursor: pointer;margin-left: 0px;"--%>
                                                                                  <%--value="#{repostaPergCliente.descricaoRespota}"/>--%>
                                                            <%--</a4j:commandLink>--%>
                                                            <div onclick="marcarCheckBox(this,event);" class="font-size-em">
                                                                <h:outputText id="idPerguntaClienteMultipla" style="font-family: Arial" styleClass="linkPadrao  texto-cor-azul texto-font texto-size-16 #{repostaPergCliente.respostaOpcao ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{repostaPergCliente.descricaoRespota}"></h:outputText>
                                                                <h:selectBooleanCheckbox value="#{repostaPergCliente.respostaOpcao}" style="display: none;"/>
                                                            </div>
                                                        </h:panelGroup>

                                                        <h:inputTextarea id="idPerguntaClienteTextual" styleClass="inputTextClean noTransitions"
                                                                         value="#{repostaPergCliente.descricaoRespota}"
                                                                         rendered="#{pergunta.perguntaCliente.textual}"
                                                                         rows="3" cols="43"
                                                                         style="width:100%"
                                                                         onblur="blurinput(this);"
                                                                         onfocus="focusinput(this);"/>

                                                        <h:panelGroup layout="block" rendered="#{pergunta.perguntaCliente.simples}" style="display: inline-block" styleClass="chk-fa-container">
                                                            <%--<a4j:commandLink  styleClass="linkPadrao" id="idPerguntaClienteSimples"  action="#{ClienteControle.escolhaSimples}"--%>
                                                                              <%--reRender="questionarioCliente,questionario,consultor,data,panelMesangem">--%>
                                                                <%--<f:setPropertyActionListener  value="#{!repostaPergCliente.respostaOpcao}" target="#{repostaPergCliente.respostaOpcao}"/>--%>
                                                                <%--<h:outputText style="font-family: Arial" styleClass="#{repostaPergCliente.respostaOpcao ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-azul texto-font texto-size-16" />--%>
                                                              <%--<h:outputText  styleClass="texto-size-16-real texto-cor-azul texto-font"--%>
                                                                             <%--value="#{repostaPergCliente.descricaoRespota}"></h:outputText>--%>
                                                            <%--</a4j:commandLink>--%>
                                                            <div onclick="marcarRadio(this, event);" class="font-size-em">
                                                                <h:outputText id="idPerguntaClienteSimples" style="font-family: Arial" styleClass="linkPadrao  texto-cor-azul texto-font texto-size-16 #{repostaPergCliente.respostaOpcao ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{repostaPergCliente.descricaoRespota}"></h:outputText>
                                                                <h:selectBooleanCheckbox value="#{repostaPergCliente.respostaOpcao}" style="display: none;"/>
                                                            </div>
                                                        </h:panelGroup>

                                                    </h:column>
                                                </h:dataTable>

                                                <h:panelGroup id="tabela02" style="margin-left: 5%;cursor: pointer" rendered="#{pergunta.perguntaCliente.tipoPergunta eq 'NS'}">
                                                    <h:panelGroup layout="block" style="display: table-caption; height: 33px; width: 80px;" styleClass="chk-fa-container">
                                                        <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-bold" value="Não indica"/>
                                                    </h:panelGroup>
                                                    <a4j:repeat id="opcoesFormasPagamento" value="#{pergunta.perguntaCliente.respostaPergClienteVOs}" var="repostaPergCliente" rowKeyVar="idx">
                                                        <h:panelGroup layout="block" style="display: inline-block;" styleClass="chk-fa-container">
                                                            <div onclick="marcarRadio(this, event);" class="font-size-em" style="display: table-caption;">
                                                                <h:outputText id="idPerguntaClienteSimples" style="font-family: Arial" styleClass="linkPadrao  texto-cor-azul texto-font texto-size-16 #{repostaPergCliente.respostaOpcao ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{repostaPergCliente.descricaoRespota}"></h:outputText>
                                                                <h:selectBooleanCheckbox value="#{repostaPergCliente.respostaOpcao}" style="display: none;"/>
                                                            </div>
                                                        </h:panelGroup>
                                                    </a4j:repeat>
                                                    <h:panelGroup layout="block" style="display: table-caption; height: 33px; width: 80px;" styleClass="chk-fa-container">
                                                        <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-bold" value="Indica"/>
                                                    </h:panelGroup>
                                                </h:panelGroup>
                                            </h:column>
                                        </h:dataTable>
                                    <h:panelGroup id="panelbotoes2" layout="block" styleClass="container-botoes" rendered="#{not empty ClienteControle.questionarioClienteVO.questionarioPerguntaClienteVOs}" >
                                        <a4j:commandLink id="cadastrarVisitante2" value="#{ClienteControle.textoBotaoVisitante}"
                                                         action="#{ClienteControle.cadastrarVisitante}"
                                                         reRender="formGravarBV, form:panelMesangem, form:panelMesangem1,panelExisteFreePass,formExisteFreePass"
                                                         oncomplete="#{ClienteControle.mostrarPanelResponderDepois}"
                                                         rendered="#{!ClienteControle.finalizarVenda && !ClienteControle.processandoOperacao && ClienteControle.retornoBV == ''}"
                                                         title="Salvar BV e voltar para tela do cliente" accesskey="2" styleClass="linkPadrao texto-cor-azul texto-size-16"/>

                                        <a4j:commandLink value="Realizar Negociação"
                                                         id="realizarNegociacao1"
                                                         action="#{ContratoControle.novoContratoConcomitanteViaTela4Jsf}"
                                                         reRender="formGravarBV, form:panelMesangem, form:panelMesangem1,panelExisteFreePass,formExisteFreePass"
                                                         rendered="#{!ClienteControle.finalizarVenda && !ClienteControle.clienteSessao && !ContratoControle.novaNegociacaoPadrao && !ClienteControle.processandoOperacao && ClienteControle.retornoBV == ''}"
                                                         oncomplete="#{ClienteControle.mostrarPanelResponderDepois}"
                                                         style="margin-left: 1em;"
                                                         accesskey="2" styleClass="botaoPrimario texto-size-16"/>

                                        <a4j:commandLink id="realizarNegociacaoNova1" value="Realizar Negociação"
                                                         action="#{ContratoControle.abrirNovNegociacaoBV}"
                                                         reRender="formGravarBV, form:panelMesangem, form:panelMesangem1,panelExisteFreePass,formExisteFreePass"
                                                         rendered="#{!ClienteControle.finalizarVenda && !ClienteControle.clienteSessao && ContratoControle.novaNegociacaoPadrao && !ClienteControle.processandoOperacao && ClienteControle.retornoBV == ''}"
                                                         oncomplete="#{ContratoControle.msgAlert}#{ClienteControle.mostrarPanelResponderDepois}"
                                                         style="margin-left: 1em;"
                                                         accesskey="2" styleClass="botaoPrimario texto-size-16"/>

                                        <a4j:commandLink value="Finalizar Venda"
                                                         action="#{ClienteControle.finalizarVenda}"
                                                         reRender="formGravarBV, form:panelMesangem, form:panelMesangem1,panelExisteFreePass,formExisteFreePass"
                                                         oncomplete="#{ClienteControle.mostrarPanelResponderDepois}"
                                                         title="#{msg.msg_finalizar_venda}"
                                                         style="margin-left: 1em;"
                                                         rendered="#{LoginControle.apresentarLinkEstudio && ClienteControle.finalizarVenda && !ClienteControle.processandoOperacao && ClienteControle.retornoBV == ''}"
                                                         accesskey="2" styleClass="botaoPrimario texto-size-16"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                                </h:panelGroup>
                                <script>
                                    jQuery('.caixaDireita').height(jQuery('.caixaDireita').parent().height());
                                </script>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
    </h:form>
    <jsp:include page="includes/autorizacao/include_autorizacao_funcionalidade.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="true"/>
    <jsp:include page="includes/cliente/include_modal_capfoto_html5.jsp" flush="true"/>
</f:view>

