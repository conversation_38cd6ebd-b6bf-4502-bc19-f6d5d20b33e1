<%--
    Document   : gestaoRemessas
    Created on : 02/08/2011, 18:38:25
    Author     : Waller
--%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<%@include file="/includes/imports.jsp" %>

<head>
    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
    <style type="text/css">
        .legend {
            font-size: 12px;
            font-weight: bold;
            color: #0F4C6B;
        }

        fieldset {
            margin-top: 5px;
            margin-bottom: 5px;
        }

        ::-webkit-scrollbar{width:8px;height:6px;margin-right:2px;}
        ::-webkit-scrollbar-button:start:decrement,::-webkit-scrollbar-button:end:increment{display:block;height:0px;}
        ::-webkit-scrollbar-button:vertical:end:increment{background:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-top:1px solid #ccc;}
        ::-webkit-scrollbar-button:vertical:increment{background-color:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-bottom:1px solid #ccc;border-top:1px solid #ccc;}
        ::-webkit-scrollbar-track-piece{background:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-top:1px solid #ccc;}
        ::-webkit-scrollbar-thumb:vertical{background-color:#ccc;}
        ::-webkit-scrollbar-thumb:vertical:hover{background-color:#666;}
        ::-webkit-scrollbar-thumb:vertical:active{background-color:#333;}

        .fs {
            min-height: 40px;
        }

        .rich-fileupload-list-decor {
            border: none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-fileupload-toolbar-decor {
            background-color: transparent;
            border: none;
        }

        .rich-fileupload-table-td {
            border: none;
        }

        .rich-panel {
            background-color: transparent;
            border: none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-panel-body {
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-fileupload-ico-add {
            background-image: url(images/drive-upload.png);
        }

        .rich-fileupload-button {
            background-color: transparent;
            background-image: none;
        }

        .rich-fileupload-button-border {
            border: none;
        }

        .rich-fileupload-button-light, .rich-fileupload-button-press {
            background-image: none;
            background-color: transparent;
        }

        /* CUSTOMIZE THE CAROUSEL
            -------------------------------------------------- */

        /* Carousel base class */
        .carousel {
            position: relative;
            line-height: 1;
        }

        .carousel-inner {
            position: relative;
            width: 100%;
            overflow: hidden;
        }

        .carousel-inner > .item {
            position: relative;
            display: none;
            -webkit-transition: 0.6s ease-in-out left;
            -moz-transition: 0.6s ease-in-out left;
            -o-transition: 0.6s ease-in-out left;
            transition: 0.6s ease-in-out left;
        }

        .carousel-inner > .item > img,
        .carousel-inner > .item > a > img {
            display: block;
            line-height: 1;
        }

        .carousel-inner > .active,
        .carousel-inner > .next,
        .carousel-inner > .prev {
            display: block;
        }

        .carousel-inner > .active {
            left: 0;
        }

        .carousel-inner > .next,
        .carousel-inner > .prev {
            position: absolute;
            top: 0;
            width: 100%;
        }

        .carousel-inner > .next {
            left: 100%;
        }

        .carousel-inner > .prev {
            left: -100%;
        }

        .carousel-inner > .next.left,
        .carousel-inner > .prev.right {
            left: 0;
        }

        .carousel-inner > .active.left {
            left: -100%;
        }

        .carousel-inner > .active.right {
            left: 100%;
        }

        .carousel-control {
            position: absolute;
            top: 40%;
            left: 15px;
            width: 40px;
            height: 40px;
            margin-top: -20px;
            font-size: 60px;
            font-weight: 100;
            line-height: 30px;
            color: #ffffff;
            text-align: center;
            background: #222222;
            border: 3px solid #ffffff;
            -webkit-border-radius: 23px;
            -moz-border-radius: 23px;
            border-radius: 23px;
            opacity: 0.5;
            filter: alpha(opacity=50);
        }

        .carousel-control.right {
            right: 15px;
            left: auto;
        }

        .carousel-control:hover,
        .carousel-control:focus {
            color: #ffffff;
            text-decoration: none;
            opacity: 0.9;
            filter: alpha(opacity=90);
        }

        .carousel-indicators {
            position: absolute;
            top: 15px;
            right: 0px;
            z-index: 2;
            margin: 0;
            list-style: none;
            left: 50%;
        }

        .carousel-indicators li {
            display: block;
            float: left;
            width: 10px;
            height: 10px;
            margin-left: 5px;
            text-indent: -999px;
            background-color: #CCBEBE;
            background-color: rgba(204, 190, 190, 0.25);
            border-radius: 5px;
        }

        .carousel-indicators .active {
            background-color: #626469;
        }

        .carousel-caption {
            position: absolute;
            right: 0;
            bottom: 0;
            left: 0;
            padding: 15px;
            background: #444444;
            background: rgba(0, 0, 0, 0.75);
        }

        .carousel-caption h4,
        .carousel-caption p {
            line-height: 20px;
            color: #ffffff;
        }

        .carousel-caption h4 {
            margin: 0 0 5px;
        }

        .carousel-caption p {
            margin-bottom: 0;
        }
    </style>
    <%--<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">--%>
    <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="bootstrap/bootstrap-transition.js"></script>
    <script type="text/javascript" src="bootstrap/bootstrap-carousel.js"></script>
    <script>
        jQuery(document).ready(function ($) {
            $("#myCarousel").carousel({interval: 10000});
        });
    </script>
</head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de Remessas"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <h:form id="form" prependId="true" styleClass="no-ext-css">
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <a4j:jsFunction status="statusHora" name="enviarBrowserGr" action="#{SuperControle.enviarBrowser}">
            <a4j:actionparam name="tmpBrowser" assignTo="#{SuperControle.browser}"/>
            <a4j:actionparam name="tmpW" assignTo="#{SuperControle.widthScreenClient}"/>
            <a4j:actionparam name="tmpH" assignTo="#{SuperControle.heightScreenClient}"/>
            <a4j:actionparam name="protocol" assignTo="#{SuperControle.protocol}"/>
        </a4j:jsFunction>
        <html>
            <body>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">

                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial container-conteudo-central">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText value="Gestão de Remessas" styleClass="container-header-titulo"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}como-acompanho-as-cobrancas-em-cartao-de-credito-enviadas-por-remessa-dos-meus-alunos-dcc/"
                                                          title="Clique e saiba mais: Gestão de Remessas" target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>

                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box  pure-form">
                                        <h:panelGroup id="panelConteudo">
                                            <h:panelGrid cellpadding="0" cellspacing="0" columns="1" width="100%">
                                                <h:panelGroup id="panelOpcoes">
                                                    <c:if test="${GestaoRemessasControle.tipoCobrancaPacto != null && (GestaoRemessasControle.tipoCobrancaPacto.codigo == 0 || GestaoRemessasControle.tipoCobrancaPacto.codigo == 4)}">
                                                        <fieldset>
                                                            <legend class="legend">Saldo de Transações</legend>
                                                            <h:panelGrid columns="2" style="vertical-align:top;"
                                                                         rowClasses="remessasOpcoesFix">
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="tituloCampos"
                                                                                  style="#{GestaoRemessasControle.styleSituacaoCreditoDCC}"
                                                                                  value="#{GestaoRemessasControle.situacaoCreditoDCC}"/>
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                            <h:panelGrid columns="2" style="vertical-align:top;"
                                                                         rowClasses="remessasOpcoesFix">
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="tituloCampos"
                                                                                  style="#{GestaoRemessasControle.styleSituacaoDataCreditoDCC}"
                                                                                  value="#{GestaoRemessasControle.situacaoDataCreditoDCC}"/>
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                        </fieldset>
                                                    </c:if>
                                                    <fieldset>
                                                        <legend class="legend">Opções</legend>
                                                        <h:panelGroup layout="block" id="panelGeralRemessa"
                                                                      style="display: inline-flex; #{empty SuporteControle.bannersRemessa ? '' : 'width: 100%'}">
                                                            <h:panelGroup layout="block" id="panelSuperior"
                                                                          style="#{empty SuporteControle.bannersRemessa ? 'width: 100%' : 'width: 35%'}; padding-right: 10px;">
                                                                <h:selectOneRadio id="remessaRetorno"
                                                                                  style="font-weight:bold;"
                                                                                  styleClass="text"
                                                                                  value="#{GestaoRemessasControle.escolhaGerarRemessaOuProcessarRetorno}">
                                                                    <f:selectItem itemLabel="Consultar / Gerar Remessa"
                                                                                  itemValue="0"/>
                                                                    <f:selectItem itemLabel="Processar Retorno"
                                                                                  itemValue="1"/>
                                                                    <a4j:support event="onchange"
                                                                                 reRender="panelConteudo"/>
                                                                </h:selectOneRadio>
                                                                <h:panelGroup/>
                                                                <h:panelGroup>
                                                                    <fieldset class="fs" style="vertical-align:top;">
                                                                        <legend class="legend">
                                                                            <c:if test="${GestaoRemessasControle.abaSelecionada == 'abaParcelas' && GestaoRemessasControle.gerarRemessa}">
                                                                                Empresa/Convênio Cobrança/Plano
                                                                            </c:if>
                                                                            <c:if test="${GestaoRemessasControle.abaSelecionada != 'abaParcelas' || !GestaoRemessasControle.gerarRemessa}">
                                                                                Empresa/Convênio Cobrança
                                                                            </c:if>
                                                                        </legend>
                                                                        <a4j:outputPanel
                                                                                rendered="#{GestaoRemessasControle.usuarioLogado.administrador || GestaoRemessasControle.consultarInfoTodasEmpresas}">
                                                                            <h:panelGroup>
                                                                                <h:outputText style="vertical-align:middle;" styleClass="tituloCampos" value="Empresa:" />
                                                                                <rich:spacer width="10px" />
                                                                                <h:selectOneMenu  onblur="blurinput(this);"
                                                                                                  id="filtroEmpresa"
                                                                                                  style="margin:6 6 6 6;"
                                                                                                  onfocus="focusinput(this);"
                                                                                                  value="#{GestaoRemessasControle.empresaSelecionada}" >
                                                                                    <f:selectItems  value="#{SuperControle.listaEmpresas}" />
                                                                                    <a4j:support
                                                                                            event="onchange"
                                                                                            action="#{GestaoRemessasControle.selecionouEmpresaCarregarConvenios}"
                                                                                            reRender="panelConteudo, panelOpcoes"/>
                                                                                </h:selectOneMenu>
                                                                                <rich:spacer width="10px"/>
                                                                            </h:panelGroup>
                                                                        </a4j:outputPanel>

                                                                        <h:panelGrid columns="2"
                                                                                     rendered="#{GestaoRemessasControle.abaRemessa}"
                                                                                     columnClasses="esquerda, esquerda">
                                                                            <h:selectBooleanCheckbox
                                                                                    value="#{GestaoRemessasControle.apresentarConvenioInativo}">
                                                                                <a4j:support
                                                                                        event="onchange"
                                                                                        oncomplete="#{GestaoRemessasControle.mensagemNotificar}"
                                                                                        action="#{GestaoRemessasControle.carregarConveniosTela}"
                                                                                        reRender="panelConteudo, panelOpcoes"/>
                                                                            </h:selectBooleanCheckbox>
                                                                            <h:outputText style="vertical-align:middle;"
                                                                                          styleClass="tituloCampos"
                                                                                          value="Apresentar convênio de cobrança inativo"/>
                                                                        </h:panelGrid>

                                                                        <h:panelGrid columns="2"
                                                                                     columnClasses="esquerda, esquerda">

                                                                            <h:outputText style="vertical-align:middle;"
                                                                                          styleClass="tituloCampos"
                                                                                          value="Convênio Cobrança: "/>
                                                                            <h:selectOneMenu
                                                                                    style="vertical-align:middle;"
                                                                                    id="comboConvenios"
                                                                                    value="#{GestaoRemessasControle.codigoGenericoConvenio}">
                                                                                <f:selectItems
                                                                                        value="#{GestaoRemessasControle.convenios}"/>
                                                                                <a4j:support event="onchange"
                                                                                             action="#{GestaoRemessasControle.selecionarConvenio}"
                                                                                             reRender="panelConteudo, panelOpcoes"/>
                                                                            </h:selectOneMenu>

                                                                            <h:outputText
                                                                                    style="vertical-align:middle; color: red"
                                                                                    rendered="#{GestaoRemessasControle.convenio.gerarArquivoUnico}"
                                                                                    styleClass="tituloCampos"
                                                                                    value=""/>

                                                                            <h:outputText style="vertical-align:middle; color: red"
                                                                                          rendered="#{GestaoRemessasControle.convenio.gerarArquivoUnico}"
                                                                                          styleClass="tituloCampos"
                                                                                          value="*Convênio de Cobrança com geração de arquivo único!"/>


                                                                            <h:outputText value="Plano:"
                                                                                          styleClass="tituloCampos"
                                                                                          rendered="#{GestaoRemessasControle.abaParcelaNaoBoleto}"/>
                                                                            <h:panelGroup
                                                                                    rendered="#{GestaoRemessasControle.abaParcelaNaoBoleto}">
                                                                                <h:inputText id="plano" size="30"
                                                                                             maxlength="30"
                                                                                             onfocus="focusinput(this);"
                                                                                             styleClass="form"
                                                                                             value="#{GestaoRemessasControle.planoFiltro.descricao}"/>
                                                                                <a4j:commandButton title="Limpar Plano"
                                                                                                   id="limparPlanoFiltroRemessa"
                                                                                                   action="#{GestaoRemessasControle.limparPlano}"
                                                                                                   image="images/limpar.gif"
                                                                                                   alt="Limpar Plano"
                                                                                                   reRender="form"
                                                                                                   style="padding-left: 10px;"/>
                                                                                <rich:suggestionbox height="200"
                                                                                                    width="400"
                                                                                                    for="plano"
                                                                                                    fetchValue="#{result}"
                                                                                                    suggestionAction="#{GestaoRemessasControle.executarAutocompletePlano}"
                                                                                                    minChars="1"
                                                                                                    rowClasses="20"
                                                                                                    status="true"
                                                                                                    nothingLabel="Nenhum produto encontrado!"
                                                                                                    var="result"
                                                                                                    id="suggestionPlano">
                                                                                    <a4j:support event="onselect"
                                                                                                 ignoreDupResponses="true"
                                                                                                 action="#{GestaoRemessasControle.selecionarPlanoSuggestionBox}"
                                                                                                 focus="plano"
                                                                                                 reRender="form, painelMensagens"/>

                                                                                    <h:column>
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    styleClass="textverysmall"
                                                                                                    value="Descrição"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                value="#{result.descricao}"/>
                                                                                    </h:column>
                                                                                </rich:suggestionbox>
                                                                            </h:panelGroup>
                                                                        </h:panelGrid>

                                                                    </fieldset>
                                                                </h:panelGroup>

                                                                <h:panelGroup>
                                                                    <h:panelGroup
                                                                            rendered="#{GestaoRemessasControle.abaParcelaNaoBoleto}">
                                                                        <fieldset class="fs">
                                                                            <legend class="legend">Tipos de Parcelas
                                                                            </legend>
                                                                            <h:panelGroup styleClass="tituloCampos"
                                                                                          style="vertical-align:middle;"
                                                                                          rendered="#{GestaoRemessasControle.abaParcela}">
                                                                                <h:selectManyCheckbox id="tpParcelas"
                                                                                                      style="vertical-align:middle;font-size:12px;font-weight:bold;"
                                                                                                      value="#{GestaoRemessasControle.tiposConsultaEscolhido}">
                                                                                    <f:selectItems
                                                                                            value="#{GestaoRemessasControle.tiposConsulta}"/>
                                                                                </h:selectManyCheckbox>
                                                                            </h:panelGroup>
                                                                        </fieldset>
                                                                    </h:panelGroup>
                                                                </h:panelGroup>

                                                                <h:panelGroup id="panelRetorno"
                                                                              rendered="#{GestaoRemessasControle.apresentarPainelRetornoRemessa}"
                                                                              layout="block"
                                                                              style="vertical-align:top;padding:0 0 0 0;">
                                                                    <fieldset class="fs"
                                                                              style="vertical-align:top;height: 70px;">
                                                                        <legend class="legend">
                                                                            Processamento Arquivo de Retorno
                                                                        </legend>
                                                                        <h:panelGroup layout="block">
                                                                            <rich:fileUpload id="selArquivo"
                                                                                             style="border: none;vertical-align:middle;"
                                                                                             acceptedTypes="txt,cmp,ret,sai,crt,gpg"
                                                                                             immediateUpload="true"
                                                                                             transferErrorLabel="Erro na transferência"
                                                                                             addControlLabel="Escolha o Arquivo..."
                                                                                             doneLabel="Concluído"
                                                                                             progressLabel="Progresso..."
                                                                                             listHeight="0"
                                                                                             listWidth="0"
                                                                                             sizeErrorLabel="Tamanho inválido"
                                                                                             stopControlLabel="Parar"
                                                                                             autoclear="true"
                                                                                             maxFilesQuantity="1"
                                                                                             fileUploadListener="#{GestaoRemessasControle.uploadListener}">
                                                                                <f:facet name="label">
                                                                                    <h:outputText
                                                                                            value="{_KB}KB de {KB}KB carregados --- {mm}:{ss}"/>
                                                                                </f:facet>
                                                                                <a4j:support event="onuploadcomplete"
                                                                                             reRender="panelRetorno,mensagem,form:mensagem"/>
                                                                                <a4j:support
                                                                                        action="#{GestaoRemessasControle.limparInformacoesPanelRetorno}"
                                                                                        status="none"
                                                                                        event="ontyperejected"
                                                                                        reRender="panelRetorno,mensagem,form:mensagem"/>
                                                                            </rich:fileUpload>
                                                                        </h:panelGroup>
                                                                    </fieldset>

                                                                    <h:panelGrid columnClasses="text,text" columns="1">

                                                                        <h:panelGroup
                                                                                rendered="#{GestaoRemessasControle.itauNovo || (GestaoRemessasControle.itau && GestaoRemessasControle.convenioTipoBoleto)}">
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Arquivo de retorno:</b> #{GestaoRemessasControle.nomeArquivo}"/><br/><br/>

                                                                            <h:outputText escape="false"
                                                                                          value="<b>Data Prevista Crédito:</b> #{GestaoRemessasControle.dataPrevistaCredito_Apresentar}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Quantidade de itens:</b> #{GestaoRemessasControle.quantidadeDeItens}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Valor Total do Arquivo:</b> #{GestaoRemessasControle.valorTotalDoArquivo_Apresentar}"/><br/><br/>

                                                                            <h:outputText escape="false"
                                                                                          value="<b>Total A Baixar em Duplicidade:</b> #{GestaoRemessasControle.quantidadeItensBaixaEmDuplicidade}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Total A Baixar com Valor Menor:</b> #{GestaoRemessasControle.quantidadeItensBaixaValorMenor}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Total A Baixar com Valor Maior:</b> #{GestaoRemessasControle.quantidadeItensBaixaValorMaior}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Total A Baixar de forma Normal:</b> #{GestaoRemessasControle.quantidadeItensBaixaNormal}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Total Já Baixado de forma Manual:</b> #{GestaoRemessasControle.quantidadeItensBaixaManual}"/><br/><br/>

                                                                            <h:outputText escape="false"
                                                                                          value="<b>Total de Registros Aceitos:</b> #{GestaoRemessasControle.quantidadeItensRegistrados}"/><br/><br/>

                                                                            <h:outputText escape="false"
                                                                                          value="<b>Total de itens reconhecidos:</b> #{GestaoRemessasControle.quantidadeItensReconhecidos}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Valor Total a ser baixado:</b> #{GestaoRemessasControle.valorTotalASerBaixado_Apresentar}"/><br/><br/>

                                                                            <h:outputText escape="false"
                                                                                          value="<b>Total de itens não reconhecidos:</b> #{GestaoRemessasControle.quantidadeItensNaoReconhecido}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Valor Total não reconhecido:</b> #{GestaoRemessasControle.valorTotalNaoReconhecido_Apresentar}"/><br/><br/>
                                                                            <h:panelGroup
                                                                                    rendered="#{not empty GestaoRemessasControle.itensOutrasEmpresa}"
                                                                                    id="pnlItensOutrasEmpresas">
                                                                                <rich:dataTable
                                                                                        style="margin-top: 12px"
                                                                                        rowClasses="linhaImpar,linhaPar"
                                                                                        width="100%"
                                                                                        id="tblOutrasEmpresas" rows="4"
                                                                                        rowKeyVar="status"
                                                                                        value="#{GestaoRemessasControle.itensOutrasEmpresa}"
                                                                                        var="item">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                value="Itens de outras unidades"/>
                                                                                    </f:facet>

                                                                                    <rich:column
                                                                                            sortBy="#{item.codigoString}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Empresa"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                title="Nome Fantasia"
                                                                                                value="#{item.codigoString}"/>
                                                                                    </rich:column>
                                                                                    <rich:column sortBy="#{item.valor}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Qt. Itens"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                title="Quantidade de Itens"
                                                                                                value="#{item.valor}"/>
                                                                                    </rich:column>
                                                                                    <rich:column sortBy="#{item.label}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Resultado do processamento"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                rendered="#{item.selecionado}"
                                                                                                title="resultado"
                                                                                                value="#{item.label}"/>
                                                                                        <h:outputText
                                                                                                rendered="#{!item.selecionado}"
                                                                                                title="resultado"
                                                                                                styleClass="mensagemDetalhada"
                                                                                                value="#{item.label}"/>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </h:panelGroup>
                                                                            <h:panelGroup id="pnlItensRetornoCobranca">
                                                                                <h:panelGroup
                                                                                        rendered="#{GestaoRemessasControle.apresentarTabelaBoleto}">
                                                                                    <h:selectBooleanCheckbox
                                                                                            value="#{GestaoRemessasControle.apresentarItensRetornoBoleto}">
                                                                                        <a4j:support event="onchange"
                                                                                                     reRender="pnlItensRetornoCobranca"/>
                                                                                    </h:selectBooleanCheckbox>
                                                                                    <h:outputText
                                                                                            styleClass="tituloCampos"
                                                                                            value="Apresentar Itens do Retorno"/>
                                                                                </h:panelGroup>


                                                                                <rich:dataTable
                                                                                        rendered="#{GestaoRemessasControle.apresentarTabelaBoleto && GestaoRemessasControle.apresentarItensRetornoBoleto}"
                                                                                        style="margin-top: 12px"
                                                                                        rowClasses="linhaImpar,linhaPar"
                                                                                        width="100%"
                                                                                        id="tblItensRemessaBoleto"
                                                                                        rows="10"
                                                                                        rowKeyVar="status"
                                                                                        value="#{GestaoRemessasControle.itensProcessar}"
                                                                                        var="item">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                value="Itens do retorno da cobrança"/>
                                                                                    </f:facet>

                                                                                    <rich:column
                                                                                            sortBy="#{item.codigo}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText value="Cod."/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                title="Código Item Remessa"
                                                                                                value="#{item.codigo}"/>
                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            rendered="#{fn:contains(pagina, 'estorno') || fn:contains(pagina, 'tela6')}"
                                                                                            sortBy="#{item.remessa.identificador}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Remessa"/>
                                                                                        </f:facet>
                                                                                        <h:outputText title="Remessa"
                                                                                                      value="#{item.remessa.identificador}"/>
                                                                                    </rich:column>


                                                                                    <rich:column
                                                                                            rendered="#{!(fn:contains(pagina, 'estorno') || fn:contains(pagina, 'tela6'))}"
                                                                                            sortBy="#{item.pessoa.nome}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Pessoa"/>
                                                                                        </f:facet>
                                                                                        <div title="Clique para abrir o Cadastro de Cliente"
                                                                                             style="overflow:hidden;width:250px">
                                                                                            <% if (request.getRequestURI().contains("impressao")) {%>
                                                                                            <a4j:commandLink
                                                                                                    value="#{item.pessoa.nome}"
                                                                                                    oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'ClienteGestaoRemessa', 1024, 700);"
                                                                                                    actionListener="#{ClienteControle.atualizarCliente}"
                                                                                                    action="#{ClienteControle.acaoAjax}">
                                                                                                <f:attribute
                                                                                                        name="pessoa"
                                                                                                        value="#{item.pessoa}"/>
                                                                                            </a4j:commandLink>
                                                                                            <%} else {%>
                                                                                            <a4j:commandLink
                                                                                                    value="#{item.pessoa.nome}"
                                                                                                    oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'ClienteGestaoRemessa', 1024, 700);"
                                                                                                    actionListener="#{ClienteControle.atualizarCliente}"
                                                                                                    action="#{ClienteControle.acaoAjax}">
                                                                                                <f:attribute
                                                                                                        name="pessoa"
                                                                                                        value="#{item.pessoa}"/>
                                                                                            </a4j:commandLink>
                                                                                            <%}%>

                                                                                        </div>
                                                                                    </rich:column>

                                                                                    <rich:column width="30"
                                                                                                 sortBy="#{item.codigoStatus}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Status"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                style="cursor:pointer;"
                                                                                                value="#{item.codigoStatus}"
                                                                                                title="\"#{item.descricaoStatus}\""/>
                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            sortBy="#{item.movParcela.dataVencimento}"
                                                                                            rendered="#{!GestaoRemessasControle.convenioTipoBoleto}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Venc."/>
                                                                                        </f:facet>

                                                                                        <h:outputText
                                                                                                title="Data de Vencimento"
                                                                                                value="#{item.movParcela.dataVencimento}">
                                                                                            <f:convertDateTime
                                                                                                    pattern="dd/MM/yyyy"/>
                                                                                        </h:outputText>
                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            sortBy="#{item.dataVencimentoBoleto}"
                                                                                            rendered="#{GestaoRemessasControle.convenioTipoBoleto}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Venc."/>
                                                                                        </f:facet>

                                                                                        <h:outputText
                                                                                                title="Data de Vencimento"
                                                                                                value="#{item.dataVencimentoBoleto}">
                                                                                            <f:convertDateTime
                                                                                                    pattern="dd/MM/yyyy"/>
                                                                                        </h:outputText>
                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            sortBy="#{item.movPagamento.codigo}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    title="MovPagamento"
                                                                                                    value="MPg"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                style="color:blue;font-weight:bold;"
                                                                                                title="Código do Movimento de Pagamento"
                                                                                                rendered="#{item.movPagamento.codigo != 0}"
                                                                                                value="Pgt. #{item.movPagamento.codigo}"/>

                                                                                        <h:outputText
                                                                                                style="color:red;font-weight:bold;"
                                                                                                title="Outra Remessa ou Outra Forma de Pagamento pagou a Parcela"
                                                                                                rendered="#{(item.movPagamento.codigo == 0) && (item.todasParcelasPagas)}"
                                                                                                value="Pgt. OUTRO"/>

                                                                                        <h:outputText
                                                                                                style="color:green;font-weight:bold;"
                                                                                                title="Outra Remessa ou Outra Forma de Pagamento pagou a Parcela"
                                                                                                rendered="#{(item.movPagamento.codigo == 0) && (!item.todasParcelasPagas && item.temParcelasPagas)}"
                                                                                                value="Pgt. PARCIAL"/>

                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            sortBy="#{item.movPagamento.codigo}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    title="Valor do Título"
                                                                                                    value="V. Tít(R$)"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                value="#{item.valorBoletoApresentar}"/>
                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            sortBy="#{item.movParcela.codigo}">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    title="MovParcela"
                                                                                                    value="Parcelas do Boleto"/>
                                                                                        </f:facet>

                                                                                        <rich:dataTable
                                                                                                rowClasses="linhaImpar,linhaPar"
                                                                                                width="100%"
                                                                                                id="tblParcelasBoleto"
                                                                                                rowKeyVar="statusParc"
                                                                                                value="#{item.movParcelas}"
                                                                                                var="parcela">

                                                                                            <rich:column
                                                                                                    rendered="#{SuperControle.widthScreenClient > 1024}"
                                                                                                    sortBy="#{parcela.movParcelaVO.contrato.codigo}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText
                                                                                                            title="Cod. Parcela"
                                                                                                            value="Parc."/>
                                                                                                </f:facet>
                                                                                                <h:outputText
                                                                                                        title="Contrato #{parcela.movParcelaVO.codigo}"
                                                                                                        value="#{parcela.movParcelaVO.codigo}"/>
                                                                                            </rich:column>

                                                                                            <rich:column
                                                                                                    sortBy="#{parcela.movParcelaVO.descricao}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText
                                                                                                            value="Descrição"/>
                                                                                                </f:facet>
                                                                                                <h:outputText
                                                                                                        rendered="#{!empty parcela.movParcelaVO.descricao}"
                                                                                                        style="vertical-align:middle;"
                                                                                                        title="#{parcela.movParcelaVO.descricao}"
                                                                                                        value="#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : '***Estornada***'}"/>
                                                                                                <a4j:commandLink
                                                                                                        rendered="#{empty parcela.movParcelaVO.descricao}"
                                                                                                        value="Log Estorno"
                                                                                                        onclick="alert('#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : item.logEstorno}');"/>
                                                                                            </rich:column>

                                                                                            <rich:column
                                                                                                    sortBy="#{parcela.movParcelaVO.situacao_Apresentar}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText
                                                                                                            title="Situação Atual da Parcela"
                                                                                                            value="Sit."/>
                                                                                                </f:facet>
                                                                                                <h:outputText
                                                                                                        title="#{item.duplicidade ? 'Significa que foi cobrado do Cartão de Crédito e aprovada, porém a parcela já se encontrava quitada.' : ''}"
                                                                                                        value="#{item.duplicidade ? '***Duplicidade***' : parcela.movParcelaVO.situacao_Apresentar}"/>

                                                                                            </rich:column>


                                                                                            <rich:column
                                                                                                    sortBy="#{parcela.movParcelaVO.valorParcela}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText
                                                                                                            value="Valor"/>
                                                                                                </f:facet>
                                                                                                <h:outputText
                                                                                                        title="Valor da Parcela Cobrada"
                                                                                                        value="R$ #{parcela.movParcelaVO.valorParcelaNumerico}"/>
                                                                                            </rich:column>
                                                                                        </rich:dataTable>

                                                                                    </rich:column>

                                                                                </rich:dataTable>
                                                                                <rich:datascroller
                                                                                        rendered="#{GestaoRemessasControle.apresentarTabelaBoleto && GestaoRemessasControle.apresentarItensRetornoBoleto}"
                                                                                        id="scItensRemessaBoleto"
                                                                                        reRender="tblItensRemessaBoleto"
                                                                                        maxPages="30"
                                                                                        for="tblItensRemessaBoleto"/>

                                                                            </h:panelGroup>
                                                                        </h:panelGroup>

                                                                        <h:panelGroup
                                                                                rendered="#{!GestaoRemessasControle.itauNovo && (GestaoRemessasControle.itau && !GestaoRemessasControle.convenioTipoBoleto)}">
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Arquivo de retorno:</b> #{GestaoRemessasControle.nomeArquivo}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Total de itens:</b> #{fn:length(GestaoRemessasControle.itensProcessar)}"/>
                                                                            <br/><br/>
                                                                            <rich:dataTable
                                                                                    rowClasses="linhaImpar,linhaPar"
                                                                                    width="100%"
                                                                                    id="tblItensRemessa"
                                                                                    rows="20"
                                                                                    rowKeyVar="status"
                                                                                    value="#{GestaoRemessasControle.itensProcessar}"
                                                                                    var="itemretorno">
                                                                                <f:facet name="header">
                                                                                    <h:outputText
                                                                                            value="Itens do retorno da cobrança"/>
                                                                                </f:facet>

                                                                                <rich:column
                                                                                        sortBy="#{itemretorno.codigo}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="Cod."/>
                                                                                    </f:facet>
                                                                                    <h:outputText
                                                                                            title="Código Item Remessa"
                                                                                            value="#{itemretorno.codigo}"/>
                                                                                </rich:column>

                                                                                <rich:column>
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="Remessa"/>
                                                                                    </f:facet>
                                                                                    <h:outputText title="Remessa"
                                                                                                  value="#{itemretorno.remessa.codigo}"/>
                                                                                </rich:column>


                                                                                <rich:column
                                                                                        sortBy="#{itemretorno.pessoa.nome}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="Pessoa"/>
                                                                                    </f:facet>
                                                                                    <div title="Clique para abrir o Cadastro de Cliente"
                                                                                         style="overflow:hidden;width:250px">
                                                                                        <a4j:commandLink
                                                                                                value="#{itemretorno.pessoa.nome}"
                                                                                                oncomplete="abrirPopup('./clienteNav.jsp?page=viewCliente', 'ClienteGestaoRemessa', 1024, 700);"
                                                                                                actionListener="#{ClienteControle.atualizarCliente}"
                                                                                                action="#{ClienteControle.acaoAjax}">
                                                                                            <f:attribute name="pessoa"
                                                                                                         value="#{itemretorno.pessoa}"/>
                                                                                        </a4j:commandLink>
                                                                                    </div>
                                                                                </rich:column>

                                                                                <rich:column width="30"
                                                                                             sortBy="#{itemretorno.valorCartaoMascaradoOuAgenciaConta}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                value="Cartão/Ag/CC"/>
                                                                                    </f:facet>
                                                                                    <h:outputText
                                                                                            style="cursor:pointer;"
                                                                                            value="#{itemretorno.valorCartaoMascaradoOuAgenciaConta}"/>
                                                                                </rich:column>

                                                                                <rich:column width="30"
                                                                                             sortBy="#{itemretorno.codigoStatus}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="Status"/>
                                                                                    </f:facet>
                                                                                    <h:outputText
                                                                                            style="cursor:pointer;"
                                                                                            value="#{itemretorno.codigoStatus}"
                                                                                            title="\"#{itemretorno.descricaoStatus}\""/>
                                                                                </rich:column>


                                                                                <rich:column
                                                                                        sortBy="#{itemretorno.identificadorClienteEmpresa}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                title="Identificador de débito automático no banco"
                                                                                                value="Id.Cob."/>
                                                                                    </f:facet>
                                                                                    <h:outputText
                                                                                            value="#{itemretorno.identificadorClienteEmpresa}"/>
                                                                                </rich:column>

                                                                                <rich:column
                                                                                        sortBy="#{itemretorno.movParcela.codigo}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText title="MovParcela"
                                                                                                      value="MPc"/>
                                                                                    </f:facet>
                                                                                    <h:outputText
                                                                                            title="Parcela: #{itemretorno.movParcela.codigo}"
                                                                                            value="#{itemretorno.movParcela.codigo}"/>
                                                                                </rich:column>

                                                                                <rich:column
                                                                                        rendered="#{SuperControle.widthScreenClient > 1024}"
                                                                                        sortBy="#{itemretorno.movParcela.contrato.codigo}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText title="Contrato"
                                                                                                      value="Cont."/>
                                                                                    </f:facet>
                                                                                    <h:outputText
                                                                                            title="Contrato #{itemretorno.movParcela.contrato.codigo}"
                                                                                            value="#{itemretorno.movParcela.contrato.codigo}"/>
                                                                                </rich:column>

                                                                                <rich:column
                                                                                        sortBy="#{itemretorno.movParcela.descricao}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                value="Descrição"/>
                                                                                    </f:facet>
                                                                                    <h:outputText
                                                                                            rendered="#{!empty itemretorno.movParcela.descricao}"
                                                                                            style="vertical-align:middle;"
                                                                                            title="#{itemretorno.movParcela.descricao}"
                                                                                            value="#{!empty itemretorno.movParcela.descricao ? itemretorno.movParcela.descricao : '***Estornada***'}"/>
                                                                                    <a4j:commandLink
                                                                                            rendered="#{empty itemretorno.movParcela.descricao}"
                                                                                            value="Log Estorno"
                                                                                            onclick="alert('#{!empty itemretorno.movParcela.descricao ? itemretorno.movParcela.descricao : itemretorno.logEstorno}');"/>
                                                                                </rich:column>

                                                                                <rich:column
                                                                                        sortBy="#{itemretorno.movParcela.dataVencimento}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="Venc."/>
                                                                                    </f:facet>

                                                                                    <h:outputText
                                                                                            rendered="#{!empty itemretorno.movParcela.descricao}"
                                                                                            title="Data de Vencimento"
                                                                                            id="dataVenc"
                                                                                            value="#{itemretorno.movParcela.dataVencimento}">
                                                                                        <f:convertDateTime
                                                                                                pattern="dd/MM/yyyy"/>
                                                                                    </h:outputText>

                                                                                </rich:column>


                                                                                <rich:column
                                                                                        sortBy="#{itemretorno.movPagamento.codigo}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                title="MovPagamento"
                                                                                                value="MPg"/>
                                                                                    </f:facet>
                                                                                    <h:outputText
                                                                                            style="color:blue;font-weight:bold;"
                                                                                            title="Código do Movimento de Pagamento"
                                                                                            rendered="#{itemretorno.movPagamento.codigo != 0}"
                                                                                            value="Pgt. #{itemretorno.movPagamento.codigo}"/>

                                                                                    <h:outputText
                                                                                            style="color:red;font-weight:bold;"
                                                                                            title="Outra Remessa ou Outra Forma de Pagamento pagou a Parcela"
                                                                                            rendered="#{(itemretorno.movPagamento.codigo == 0) && (itemretorno.movParcela.situacao == 'PG')}"
                                                                                            value="Pgt. OUTRO"/>

                                                                                </rich:column>

                                                                                <rich:column
                                                                                        sortBy="#{itemretorno.movParcela.situacao_Apresentar}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                title="Situação Atual da Parcela"
                                                                                                value="Sit."/>
                                                                                    </f:facet>
                                                                                    <h:outputText
                                                                                            title="#{itemretorno.duplicidade ? 'Significa que foi cobrado do Cartão de Crédito e aprovada, porém a parcela já se encontrava quitada.' : ''}"
                                                                                            value="#{itemretorno.duplicidade ? '***Duplicidade***' : itemretorno.movParcela.situacao_Apresentar}"/>

                                                                                </rich:column>

                                                                                <rich:column
                                                                                        sortBy="#{itemretorno.movParcela.valorParcela}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="Valor"/>
                                                                                    </f:facet>
                                                                                    <h:outputText
                                                                                            title="Valor da Parcela Cobrada"
                                                                                            value="R$ #{itemretorno.movParcela.valorParcelaNumerico}"/>
                                                                                </rich:column>

                                                                            </rich:dataTable>
                                                                            <rich:datascroller id="scItensRemessa"
                                                                                               reRender="tblItensRemessa"
                                                                                               maxPages="30"
                                                                                               for="tblItensRemessa"/>
                                                                        </h:panelGroup>

                                                                        <h:panelGroup rendered="#{!empty GestaoRemessasControle.remessaVO
                                                                                                              && GestaoRemessasControle.remessaVO.codigo != 0
                                                                                                              && !GestaoRemessasControle.itau}">
                                                                            <h:panelGrid columnClasses="text,text"
                                                                                         columns="2">
                                                                                <h:outputText style="font-weight:bold;"
                                                                                              value="Cabeçalho Retorno:"/>
                                                                                <h:outputText
                                                                                        value=" #{GestaoRemessasControle.headRetornoString}"/>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Código Remessa"
                                                                                              value="Código:"/>
                                                                                <h:outputText
                                                                                        value="#{GestaoRemessasControle.remessaVO.codigo}"/>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Código Empresa"
                                                                                              value="Empresa:"/>
                                                                                <h:outputText
                                                                                        value="#{GestaoRemessasControle.remessaVO.empresa}"/>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Identificador Remessa"
                                                                                              value="Id:"/>
                                                                                <h:outputText
                                                                                        value="#{GestaoRemessasControle.remessaVO.identificador}"/>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              value="Data Geração:"/>
                                                                                <h:outputText
                                                                                        title="Data Geração Remessa"
                                                                                        value="#{GestaoRemessasControle.remessaVO.dataRegistro}">
                                                                                    <f:convertDateTime
                                                                                            timeStyle="medium"
                                                                                            pattern="dd/MM/yyyy HH:mm:ss"/>
                                                                                </h:outputText>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Status Remessa"
                                                                                              value="Situação:"/>
                                                                                <h:panelGrid
                                                                                        columnClasses="colunaEsquerda"
                                                                                        width="100%">
                                                                                    <h:column>
                                                                                        <rich:spacer style="cursor:pointer;vertical-align:middle;
                                                                                                                 width: 8px; height: 8px;
                                                                                                                 background-color:#{GestaoRemessasControle.remessaVO.situacaoRemessa.cor}"
                                                                                                     width="8"
                                                                                                     height="8"
                                                                                                     title="#{GestaoRemessasControle.remessaVO.situacaoRemessa.hint}">
                                                                                        </rich:spacer>
                                                                                    </h:column>
                                                                                </h:panelGrid>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Número de Itens da Remessa"
                                                                                              value="Quantidade:"/>
                                                                                <h:outputText
                                                                                        value="#{GestaoRemessasControle.remessaVO.qtdRegistros}"/>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Valor Bruto da Remessa"
                                                                                              value="R$ Bruto:"/>
                                                                                <h:outputText
                                                                                        value="#{GestaoRemessasControle.remessaVO.valorBruto_Apresentar}"/>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Valor Aceito (Aprovado)"
                                                                                              value="R$ Aceito:"/>
                                                                                <h:outputText
                                                                                        style="color:#{GestaoRemessasControle.remessaVO.valorAceito < remessaVO.valorBruto && remessaVO.situacaoremessaVO.id == 2 ? 'red' : ''};"
                                                                                        value="#{GestaoRemessasControle.remessaVO.valorAceito_Apresentar}"/>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Valor Líquido"
                                                                                              value="R$ Líquido:"/>
                                                                                <h:outputText
                                                                                        value="#{GestaoRemessasControle.remessaVO.valorLiquido_Apresentar}"/>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Data Prevista para Crédito no Banco"
                                                                                              value="Dt.Crédito:"/>
                                                                                <h:outputText
                                                                                        value="#{GestaoRemessasControle.remessaVO.dataPrevistaCredito}">
                                                                                    <f:convertDateTime
                                                                                            timeStyle="medium"
                                                                                            pattern="dd/MM/yyyy"/>
                                                                                </h:outputText>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Usuário criou Remessa"
                                                                                              value="U. Remessa:"/>
                                                                                <h:outputText
                                                                                        value="#{GestaoRemessasControle.remessaVO.usuario.nomeAbreviado}"/>

                                                                                <h:outputText style="font-weight:bold;"
                                                                                              title="Usuário processou o Retorno"
                                                                                              value="U.Retorno:"/>
                                                                                <h:outputText
                                                                                        value="#{GestaoRemessasControle.remessaVO.usuarioRetorno}"/>
                                                                            </h:panelGrid>
                                                                        </h:panelGroup>

                                                                        <h:panelGroup
                                                                                rendered="#{GestaoRemessasControle.convenio.layoutFebrabanDCO}">

                                                                            <h:outputText escape="false"
                                                                                          value="<b>Arquivo de retorno:</b> #{GestaoRemessasControle.nomeArquivo}"/><br/><br/>

                                                                            <h:outputText escape="false"
                                                                                          value="<b>Quantidade de autorizações:</b> #{GestaoRemessasControle.quantidadeAutorizacoes}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Quantidade de confirmações:</b> #{GestaoRemessasControle.quantidadeConfirmacoes}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Quantidade de itens:</b> #{GestaoRemessasControle.quantidadeItensReconhecidos}"/><br/>
                                                                            <h:outputText escape="false"
                                                                                          value="<b>Total de Registros:</b> #{GestaoRemessasControle.quantidadeDeItens}"/><br/>

                                                                        </h:panelGroup>
                                                                    </h:panelGrid>

                                                                    <h:panelGrid columns="2"
                                                                                 columnClasses="colunaEsquerda">
                                                                        <a4j:commandButton id="processarRet"
                                                                                           rendered="#{!empty GestaoRemessasControle.remessaVO && GestaoRemessasControle.remessaVO.codigo != 0 && !GestaoRemessasControle.itau}"
                                                                                           value="Processar Retorno"
                                                                                           action="#{GestaoRemessasControle.confirmarProcessarRetorno}"
                                                                                           reRender="panelRetorno,mensagem"/>
                                                                        <a4j:commandButton id="processarRetItau"
                                                                                           rendered="#{!GestaoRemessasControle.itauNovo && GestaoRemessasControle.itau && !GestaoRemessasControle.convenioTipoBoleto}"
                                                                                           value="Processar Retorno"
                                                                                           action="#{GestaoRemessasControle.confirmarProcessarItau}"
                                                                                           reRender="panelRetorno,mensagem"/>
                                                                        <a4j:commandButton id="processarRetBoleto"
                                                                                           rendered="#{!GestaoRemessasControle.boletoOnline && (GestaoRemessasControle.itauNovo || (GestaoRemessasControle.itau && GestaoRemessasControle.convenioTipoBoleto && GestaoRemessasControle.apresentarBotaoProcessar))}"
                                                                                           value="Processar Retorno"
                                                                                           action="#{GestaoRemessasControle.confirmarProcessarBoleto}"
                                                                                           reRender="panelRetorno,mensagem"/>
                                                                        <a4j:commandButton id="processarRetBoletoOnline"
                                                                                           rendered="#{GestaoRemessasControle.boletoOnline && GestaoRemessasControle.convenioTipoBoleto && GestaoRemessasControle.apresentarBotaoProcessar}"
                                                                                           value="Processar Retorno"
                                                                                           action="#{GestaoRemessasControle.confirmarProcessarBoletoOnline}"
                                                                                           oncomplete="#{GestaoRemessasControle.onCompleteBoleto}#{GestaoRemessasControle.mensagemNotificar}"
                                                                                           reRender="panelRetorno,mensagem"/>
                                                                        <a4j:commandButton id="processarRetFebraban"
                                                                                           rendered="#{GestaoRemessasControle.apresentarBotaoProcessarRetorno}"
                                                                                           value="Processar Retorno"
                                                                                           action="#{GestaoRemessasControle.confirmarProcessarFebraban}"
                                                                                           reRender="panelRetorno,mensagem"/>

                                                                        <a4j:commandButton id="exibeResultado"
                                                                                           rendered="#{!empty GestaoRemessasControle.remessaVO && GestaoRemessasControle.remessaVO.codigo != 0 && (!empty GestaoRemessasControle.remessaVO.retorno)}"
                                                                                           value="Exibir Resultado"
                                                                                           action="#{GestaoRemessasControle.exibirItensRemessa}"
                                                                                           reRender="panelDadosRemessa"/>
                                                                    </h:panelGrid>
                                                                    <c:if test="${not empty GestaoRemessasControle.registrosNaoEncontrados}">
                                                                        <h:panelGrid columnClasses="text,text"
                                                                                     columns="1"
                                                                                     style="margin-top: 8px">
                                                                            <h:panelGroup
                                                                                    rendered="#{GestaoRemessasControle.itau}">
                                                                                <rich:dataTable
                                                                                        rowClasses="linhaImpar,linhaPar"
                                                                                        width="100%"
                                                                                        id="tblItensNaoProcessados"
                                                                                        rows="20" rowKeyVar="status"
                                                                                        value="#{GestaoRemessasControle.registrosNaoEncontrados}"
                                                                                        var="registro">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                                value="Itens Não Encontrados"/>
                                                                                    </f:facet>

                                                                                    <rich:column>
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Cod. Ocorrência"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                title="Cógigo da Ocorrencia"
                                                                                                value="#{registro.statusVenda}"/>
                                                                                    </rich:column>

                                                                                    <rich:column>
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Nosso Número"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                title="Nósso Número"
                                                                                                value="#{registro.nossoNumero}"/>
                                                                                    </rich:column>

                                                                                    <rich:column>
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Núm. Documento"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                title="Número do Documento"
                                                                                                value="#{registro.numDocumento}"/>
                                                                                    </rich:column>

                                                                                    <rich:column>
                                                                                        <f:facet name="header">
                                                                                            <h:outputText value="Nome"/>
                                                                                        </f:facet>
                                                                                        <h:outputText title="Nome"
                                                                                                      value="#{registro.nomeCliente}"/>
                                                                                    </rich:column>

                                                                                    <rich:column>
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Valor"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                title="Valor da Parcela"
                                                                                                value="#{registro.valorRetorno}"/>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                                <rich:datascroller
                                                                                        id="scItensNaoEncontrados"
                                                                                        style="margin-bottom: 8px"
                                                                                        reRender="tblItensNaoProcessados"
                                                                                        maxPages="30"
                                                                                        for="tblItensNaoProcessados"/>

                                                                                <h:panelGroup style="float: right">
                                                                                    <a4j:commandLink id="btnExcel"
                                                                                                     styleClass="pure-button pure-button-small"
                                                                                                     actionListener="#{GestaoRemessasControle.exportarItensNaoEncontrados}"
                                                                                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                                                     accesskey="3">
                                                                                        <f:attribute name="tipo"
                                                                                                     value="xls"/>
                                                                                        <f:attribute name="atributos"
                                                                                                     value="codOcorrencia=Código Ocorrência,nossoNumero=Nosso Número,numDocumento=Número do Documento,nomeCliente=Nome,valorRetorno=Valor"/>
                                                                                        <f:attribute name="prefixo"
                                                                                                     value="ItensNaoEncontrados"/>
                                                                                        <i class="fa-icon-excel"></i> &nbsp Excel
                                                                                    </a4j:commandLink>

                                                                                    <a4j:commandLink id="btnPDF"
                                                                                                     styleClass="pure-button pure-button-small margin-h-10"
                                                                                                     actionListener="#{GestaoRemessasControle.exportarItensNaoEncontrados}"
                                                                                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                                                     accesskey="4">
                                                                                        <f:attribute name="tipo"
                                                                                                     value="pdf"/>
                                                                                        <f:attribute name="atributos"
                                                                                                     value="codOcorrencia=Código Ocorrência,nossoNumero=Nosso Número,numDocumento=Número do Documento,nomeCliente=Nome,valorRetorno=Valor"/>
                                                                                        <f:attribute name="prefixo"
                                                                                                     value="ItensNaoEncontrados"/>
                                                                                        <i class="fa-icon-pdf"></i> &nbsp PDF
                                                                                    </a4j:commandLink>
                                                                                </h:panelGroup>
                                                                            </h:panelGroup>
                                                                        </h:panelGrid>
                                                                    </c:if>
                                                                </h:panelGroup>

                                                                <h:panelGroup
                                                                        rendered="#{GestaoRemessasControle.gerarRemessa}">
                                                                    <fieldset class="fs" style="vertical-align:top;">
                                                                        <legend class="legend">
                                                                            <h:outputText
                                                                                    value="#{GestaoRemessasControle.textoFieldsetDataConsulta}"/>
                                                                        </legend>

                                                                        <h:panelGroup>
                                                                            <h:outputText styleClass="tituloCampos"
                                                                                          style="vertical-align:middle;"
                                                                                          value="De: "/>
                                                                            <rich:calendar id="dataInicio"
                                                                                           value="#{GestaoRemessasControle.dataInicio}"
                                                                                           inputSize="10"
                                                                                           inputClass="form calendarIndexRemessas"
                                                                                           oninputblur="blurinput(this);"
                                                                                           oninputfocus="focusinput(this);"
                                                                                           oninputchange="return validar_Data(this.id);"
                                                                                           datePattern="dd/MM/yyyy"
                                                                                           enableManualInput="true"
                                                                                           zindex="2"
                                                                                           showWeeksBar="false"/>

                                                                            <rich:spacer width="20px"/>

                                                                            <h:outputText styleClass="tituloCampos"
                                                                                          style="vertical-align:middle;"
                                                                                          value="Até: "/>
                                                                            <rich:calendar id="dataTermino"
                                                                                           value="#{GestaoRemessasControle.dataFim}"
                                                                                           inputSize="10"
                                                                                           inputClass="form calendarIndexRemessas"
                                                                                           oninputblur="blurinput(this);"
                                                                                           oninputfocus="focusinput(this);"
                                                                                           oninputchange="return validar_Data(this.id);"
                                                                                           datePattern="dd/MM/yyyy"
                                                                                           enableManualInput="true"
                                                                                           zindex="2"
                                                                                           showWeeksBar="false"/>
                                                                        </h:panelGroup>
                                                                    </fieldset>

                                                                    <c:if test="${GestaoRemessasControle.convenio.boleto and GestaoRemessasControle.abaParcela}">
                                                                        <fieldset class="fs"
                                                                                  style="vertical-align:top;">
                                                                            <legend class="legend">
                                                                                <h:outputText value="Modalidade"/>
                                                                            </legend>

                                                                            <h:panelGroup>
                                                                                <h:selectOneMenu
                                                                                        style="vertical-align:middle;"
                                                                                        id="comboModalidades"
                                                                                        value="#{GestaoRemessasControle.modalidade.codigo}">
                                                                                    <f:selectItems
                                                                                            value="#{GestaoRemessasControle.listaModalidades}"/>
                                                                                </h:selectOneMenu>
                                                                            </h:panelGroup>
                                                                        </fieldset>
                                                                    </c:if>

                                                                </h:panelGroup>

                                                                <h:panelGroup styleClass="tituloCampos"
                                                                              style="vertical-align:middle;"
                                                                              rendered="#{GestaoRemessasControle.gerarRemessa && GestaoRemessasControle.abaSelecionada == 'abaRemessa'}">
                                                                    <fieldset class="fs" style="vertical-align:top; text-align: left">
                                                                        <legend class="legend">
                                                                            <h:outputText value="Situação da Remessa"/>
                                                                        </legend>

                                                                        <h:selectManyCheckbox id="tpStatusSituacoesRemessa"
                                                                                style="vertical-align:middle;font-size:12px;"
                                                                                value="#{GestaoRemessasControle.tiposConsultaStatusEscolhido}">
                                                                            <f:selectItems value="#{GestaoRemessasControle.tiposConsultaStatus}"/>
                                                                        </h:selectManyCheckbox>

                                                                        <c:if test="${GestaoRemessasControle.apresentarAbaCancelamento}">
                                                                            <div style="padding-left: 3px;">
                                                                                <h:selectBooleanCheckbox
                                                                                        value="#{GestaoRemessasControle.filtroRemessasCancelamento}"/>
                                                                                <h:outputText style="font-size:12px;"
                                                                                              value="Somente remessa de cancelamento"/>
                                                                            </div>
                                                                        </c:if>
                                                                    </fieldset>
                                                                </h:panelGroup>

                                                                <h:panelGroup
                                                                        rendered="#{GestaoRemessasControle.abaParcelaNaoBoleto}">
                                                                    <fieldset class="fs" style="vertical-align:top;">
                                                                        <legend class="legend">
                                                                            <h:outputText
                                                                                    value="Data de Cobrança das Parcelas"/>
                                                                        </legend>

                                                                        <h:panelGroup>
                                                                            <h:outputText styleClass="tituloCampos"
                                                                                          style="vertical-align:middle;"
                                                                                          value="De: "/>
                                                                            <rich:calendar id="dataInicioCobranca"
                                                                                           value="#{GestaoRemessasControle.dataInicioCobranca}"
                                                                                           inputSize="10"
                                                                                           inputClass="form calendarIndexRemessas"
                                                                                           oninputblur="blurinput(this);"
                                                                                           oninputfocus="focusinput(this);"
                                                                                           oninputchange="return validar_Data(this.id);"
                                                                                           datePattern="dd/MM/yyyy"
                                                                                           enableManualInput="true"
                                                                                           zindex="2"
                                                                                           showWeeksBar="false"/>

                                                                            <rich:spacer width="20px"/>

                                                                            <h:outputText styleClass="tituloCampos"
                                                                                          style="vertical-align:middle;"
                                                                                          value="Até: "/>
                                                                            <rich:calendar id="dataTerminoCobranca"
                                                                                           value="#{GestaoRemessasControle.dataFimCobranca}"
                                                                                           inputSize="10"
                                                                                           inputClass="form calendarIndexRemessas"
                                                                                           oninputblur="blurinput(this);"
                                                                                           oninputfocus="focusinput(this);"
                                                                                           oninputchange="return validar_Data(this.id);"
                                                                                           datePattern="dd/MM/yyyy"
                                                                                           enableManualInput="true"
                                                                                           zindex="2"
                                                                                           showWeeksBar="false"/>
                                                                        </h:panelGroup>
                                                                    </fieldset>
                                                                </h:panelGroup>


                                                                <h:panelGroup rendered="#{GestaoRemessasControle.abaParcelaNaoBoleto && GestaoRemessasControle.apresentarFiltroStatus}">
                                                                    <fieldset class="fs" style="vertical-align:top;">
                                                                        <legend class="legend">
                                                                            <h:outputText
                                                                                    value="Status"/>
                                                                        </legend>

                                                                        <h:panelGroup>
                                                                            <a4j:repeat
                                                                                    value="#{GestaoRemessasControle.listaStatus}"
                                                                                    var="status">
                                                                                <h:selectBooleanCheckbox
                                                                                        value="#{status.selecionado}">
                                                                                    <a4j:support event="onclick"
                                                                                                 action="#{GestaoRemessasControle.processarFiltroStatus}"
                                                                                                 reRender="panelConteudo"/>
                                                                                </h:selectBooleanCheckbox>
                                                                                <h:outputText
                                                                                        styleClass="tituloCampos"
                                                                                        title="#{status.hint}"
                                                                                        value="#{status.descricao}"/>
                                                                            </a4j:repeat>
                                                                        </h:panelGroup>
                                                                    </fieldset>
                                                                </h:panelGroup>

                                                                <c:if test="${GestaoRemessasControle.abaSelecionada == 'abaRemessa' && GestaoRemessasControle.gerarRemessa}">
                                                                    <a4j:commandLink id="consultarRemessas"
                                                                                     value="Consultar"
                                                                                     styleClass="pure-button pure-button-primary pure-button-xsmall"
                                                                                     action="#{GestaoRemessasControle.consultarInformacoes}"
                                                                                     reRender="panelConteudo"/>
                                                                </c:if>

                                                                <c:if test="${GestaoRemessasControle.abaSelecionada == 'abaCancelamento' && GestaoRemessasControle.gerarRemessa}">
                                                                    <a4j:commandLink id="consultarItensPagos"
                                                                                     value="Consultar"
                                                                                     styleClass="pure-button pure-button-primary pure-button-xsmall"
                                                                                     action="#{GestaoRemessasControle.consultarInformacoes}"
                                                                                     reRender="panelConteudo"/>
                                                                </c:if>

                                                                <h:panelGrid id="mensagem" columns="1" width="100%"
                                                                             style="margin:0 0 0 0;">
                                                                    <h:outputText escape="false" styleClass="mensagem"
                                                                                  value="#{GestaoRemessasControle.mensagem}"/>
                                                                    <h:outputText escape="false"
                                                                                  styleClass="mensagemDetalhada"
                                                                                  value="#{GestaoRemessasControle.mensagemDetalhada}"/>
                                                                </h:panelGrid>

                                                                <h:panelGroup layout="block"
                                                                              style="padding-top: 10px; padding-bottom: 10px"
                                                                              rendered="#{not empty GestaoRemessasControle.errosGeracaoApresentar}">
                                                                    <h:panelGrid id="errosGeracaoRemessaBoleto" columns="1" width="100%"
                                                                                 style="margin:0 0 0 0;">
                                                                        <h:outputText escape="false"
                                                                                      style="font-weight: bold;"
                                                                                      styleClass="mensagemDetalhada"
                                                                                      value="Erros na geração:"/>
                                                                        <h:outputText escape="false"
                                                                                      styleClass="mensagemDetalhada"
                                                                                      value="#{GestaoRemessasControle.errosGeracaoApresentar}"/>
                                                                    </h:panelGrid>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                            <h:panelGroup layout="block" id="panelCarousel"
                                                                          style="#{empty SuporteControle.bannersRemessa ? '' : 'width: 65%'}"
                                                                          rendered="#{not empty SuporteControle.bannersRemessa}">
                                                                <div id="myCarousel"
                                                                     class="carousel slide container-conteudo-central">
                                                                    <div class="carousel-inner">
                                                                        <c:forEach
                                                                                items="${SuporteControle.bannersRemessa}"
                                                                                var="ban" varStatus="ind">
                                                                            <div class="item ${ind.count == 1 ? 'active' : ''}">
                                                                                <a
                                                                                        <c:if test="${not empty ban.urlLink}">href="${ban.urlLink}"</c:if>
                                                                                        target="_blank">
                                                                                    <img border="none"
                                                                                         style="width: 100%;"
                                                                                         src="${ban.urlImagem}">
                                                                                </a>
                                                                            </div>
                                                                        </c:forEach>
                                                                    </div>

                                                                    <ol class="carousel-indicators">
                                                                        <c:forEach
                                                                                items="${SuporteControle.bannersRemessa}"
                                                                                var="ban"
                                                                                varStatus="ind">
                                                                            <li style="cursor: pointer;"
                                                                                data-target="#myCarousel"
                                                                                data-slide-to="${ind.count -1}"
                                                                                class="${ind.count == 1 ? 'active' : ''}"></li>
                                                                        </c:forEach>
                                                                    </ol>
                                                                </div>
                                                            </h:panelGroup>
                                                        </h:panelGroup>

                                                        <c:if test="${GestaoRemessasControle.gerarRemessa}">
                                                            <h:panelGroup layout="block">
                                                                <h:panelGrid
                                                                        rendered="#{GestaoRemessasControle.abaParcela}"
                                                                        columns="2" style="vertical-align:top;"
                                                                        columnClasses="alinhar, alinhar">
                                                                    <h:panelGroup
                                                                            rendered="#{GestaoRemessasControle.abaParcelaBoleto}">

                                                                        <h:panelGroup>
                                                                            <h:selectBooleanCheckbox
                                                                                    title="Gera boleto manual para parcelas com boleto que não foram gerados manualmente por esse recurso. Desde que o boleto gerado por outro recurso tenha a data de vencimento anterior a hoje."
                                                                                    value="#{GestaoRemessasControle.mostrarParcelasComBoleto}"/>
                                                                            <h:outputText
                                                                                    title="Gera boleto manual para parcelas com boleto que não foram gerados manualmente por esse recurso. Desde que o boleto gerado por outro recurso tenha a data de vencimento anterior a hoje."
                                                                                    value="#{msg_aplic['mostrarParcelasComBoleto']}"
                                                                                    styleClass="tituloCampos"/>
                                                                        </h:panelGroup>
                                                                        <br/>
                                                                        <br/>

                                                                        <h:panelGroup styleClass="tituloCampos"
                                                                                      style="vertical-align:middle;">
                                                                            <a4j:commandLink id="validarCriarRemessa"
                                                                                             value="Criar Remessa"
                                                                                             styleClass="pure-button pure-button-primary pure-button-small"
                                                                                             action="#{GestaoRemessasControle.confirmarGerarNovaRemessa}"
                                                                                             oncomplete="#{GestaoRemessasControle.msgAlert}"
                                                                                             reRender="mdlMensagemGenerica, mensagem, panelConteudo"/>
                                                                        </h:panelGroup>
                                                                    </h:panelGroup>

                                                                    <h:panelGroup
                                                                            rendered="#{GestaoRemessasControle.abaParcelaNaoBoleto}">
                                                                        <h:panelGroup>
                                                                            <h:selectBooleanCheckbox
                                                                                    value="#{GestaoRemessasControle.agrupar}"
                                                                                    id="agruparAcoes">
                                                                                <a4j:support reRender="panelConteudo"
                                                                                             action="#{GestaoRemessasControle.agruparParcelas}"
                                                                                             event="onclick"/>
                                                                            </h:selectBooleanCheckbox>
                                                                            <h:outputText
                                                                                    value="#{msg_aplic['agrupar']}"
                                                                                    styleClass="tituloCampos"/>
                                                                            <rich:spacer width="5px"/>
                                                                            <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-configurar-retentativa-de-cobranca-para-retornos-especificos-de-remessas/" title="Clique e saiba mais: Gestão de Remessas" target="_blank">
                                                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                            </h:outputLink>
                                                                        </h:panelGroup>
                                                                        <br/>
                                                                        <br/>
                                                                        <h:panelGroup>
                                                                            <h:selectBooleanCheckbox
                                                                                    value="#{GestaoRemessasControle.mostrarParcelasComBoleto}"/>
                                                                            <h:outputText
                                                                                    value="#{msg_aplic['mostrarParcelasComBoleto']}"
                                                                                    styleClass="tituloCampos"/>
                                                                        </h:panelGroup>
                                                                        <br/>
                                                                        <br/>
                                                                        <h:panelGroup>
                                                                            <h:selectBooleanCheckbox styleClass="tooltipster"
                                                                                                     title="Com esta configuração <b>MARCADA</b>, o sistema irá exibir todas as parcelas, da mesma forma como era antes, <b>independente</b> se o aluno possui ou não bloqueio de cobrança automática configurado em seu perfil.
                                                                                                </br>Com esta configuração <b>DESMARCADA</b>, o sistema irá exibir somente as parcelas dos alunos cujo a cobrança automática esteja <b>LIBERADA</b>. Também poderá mostrar parcela de um aluno que tenha configurado
                                                                                                </br>bloqueio parcial dependendo da data de vencimento configurada. Desta forma você não corre o risco de criar uma cobrança manual aqui para um aluno que esteja com a cobrança bloqueada."
                                                                                                     value="#{GestaoRemessasControle.apresentarAlunosBloqueioCobranca}"/>
                                                                            <h:outputText title="Com esta configuração <b>MARCADA</b>, o sistema irá exibir todas as parcelas, da mesma forma como era antes, <b>independente</b> se o aluno possui ou não bloqueio de cobrança automática configurado em seu perfil.
                                                                                                </br>Com esta configuração <b>DESMARCADA</b>, o sistema irá exibir somente as parcelas dos alunos cujo a cobrança automática esteja <b>LIBERADA</b>. Também poderá mostrar parcela de um aluno que tenha configurado
                                                                                                </br>bloqueio parcial dependendo da data de vencimento configurada. Desta forma você não corre o risco de criar uma cobrança manual aqui para um aluno que esteja com a cobrança bloqueada."
                                                                                          value="Mostrar parcelas de clientes com bloqueio de cobrança automática"
                                                                                          styleClass="tituloCampos tooltipster"/>
                                                                        </h:panelGroup>
                                                                        <br/>
                                                                        <br/>
                                                                        <h:panelGroup styleClass="tituloCampos"
                                                                                      style="vertical-align:middle;">
                                                                            <a4j:commandLink id="consultarParcelas"
                                                                                             value="Consultar"
                                                                                             styleClass="pure-button pure-button-primary pure-button-small"
                                                                                             action="#{GestaoRemessasControle.consultarInformacoes}"
                                                                                             reRender="panelConteudo, mensagem"/>
                                                                            <a4j:commandLink id="configurarRemessa"
                                                                                             styleClass="pure-button pure-button-small margin-h-10"
                                                                                             value="#{msg_aplic.prt_acoes_remessas_config_btn}"
                                                                                             action="#{GestaoRemessasControle.abrirConfigAcao}"
                                                                                             reRender="panelConfigAcoes"
                                                                                             oncomplete="Richfaces.showModalPanel('panelConfigAcoes');"/>
                                                                        </h:panelGroup>
                                                                    </h:panelGroup>
                                                                </h:panelGrid>
                                                            </h:panelGroup>
                                                        </c:if>


                                                    </fieldset>
                                                </h:panelGroup>

                                            </h:panelGrid>

                                            <rich:tabPanel id="richPanel" tabClass="aba" width="100%"
                                                           switchType="ajax" immediate="true" headerAlignment="left"
                                                           rendered="#{GestaoRemessasControle.gerarRemessa}"
                                                           selectedTab="#{GestaoRemessasControle.abaSelecionada}">
                                                <rich:tab label="Remessas"
                                                          reRender="panelConteudo" id="abaRemessa">
                                                    <h:panelGrid columns="1" width="100%">
                                                        <h:panelGroup>
                                                            <%@include
                                                                    file="includes/remessas/include_table_remessa.jsp" %>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                </rich:tab>

                                                <rich:tab label="Consulta Parcelas"
                                                          reRender="panelConteudo"
                                                          id="abaParcelas">
                                                    <h:panelGrid columns="1" width="100%">
                                                        <h:panelGroup>
                                                            <%@include
                                                                    file="includes/remessas/include_parcelas_remessa.jsp" %>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                </rich:tab>

                                                <rich:tab id="abaCancelamento" label="Remessa de Cancelamento"
                                                          reRender="panelConteudo"
                                                          rendered="#{GestaoRemessasControle.apresentarAbaCancelamento}">
                                                    <h:panelGrid columns="1" width="100%">
                                                        <h:panelGroup>
                                                            <%@include
                                                                    file="includes/remessas/include_parcelas_remessa_cancelamento.jsp" %>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                </rich:tab>
                                            </rich:tabPanel>
                                            <rich:jQuery id="mskData" selector=".calendarIndexRemessas" timing="onload"
                                                         query="mask('99/99/9999')" />
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>

                    </h:panelGroup>

                    <c:if test="${SuperControle.menuZwUi}">
                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-INICIO" />
                        </jsp:include>
                    </c:if>

                </h:panelGroup>
                <jsp:include page="include_rodape_flat.jsp" flush="true" />
            </h:panelGroup>
            </body>
        </html>
    </h:form>
    <jsp:include page="includes/remessas/include_paramspanel_remessa.jsp" flush="true"/>
    <jsp:include page="includes/remessas/include_configurar_acoes.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="true"/>
    <jsp:include page="includes/autorizacao/include_autorizacao_funcionalidade.jsp" flush="true"/>
    <jsp:include page="includes/parcelas/include_modal_alterarVencimentoParcelas.jsp" flush="true"/>


    <rich:modalPanel id="modalConfirmarDownload" width="480" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Autorização para Baixar arquivos da Remessa"/>
        </h:panelGroup>
    </f:facet>


    <a4j:form id="formmodalConfirmarDownload">
        <h:panelGroup id="panelConteudoConfirmar">
            <rich:panel>
                <h:outputText styleClass="text" value="O download de arquivos do Gestão de Remessas exije uma senha especial. Informe-a:"/>
                <center><h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" style="margin-left:8px"
                                       value="#{GestaoRemessasControle.senhaDownloadRemessa}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('autorizar')}.onclick();return false;"/></center>
                        
                        <h:outputText id="mensagem" styleClass="mensagemDetalhada" value="#{GestaoRemessasControle.mensagemDetalhada}"/>
            </rich:panel>
           
            <h:panelGroup id="botoes">
                <h:panelGroup style="float: center;">
                     <a4j:commandButton id="autorizar" value="#{msg_bt.btn_confirmar}" reRender="modalConfirmarDownload"
                                         alt="#{msg.msg_gravar_dados}" action="#{GestaoRemessasControle.validarSenhaDownload}" oncomplete="#{GestaoRemessasControle.msgAlertAuxiliar}"/>
                     <rich:spacer height="5"/>
                     <a4j:commandButton action="#{GestaoRemessasControle.cancelarDownload}" value="Cancelar" oncomplete="#{rich:component('modalConfirmarDownload')}.hide();"
                                       reRender="modalConfirmarDownload"/>
                </h:panelGroup>
                
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

    <rich:modalPanel id="mdlGeracaoRemessa" width="450" autosized="true" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>Atenção</h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText id="hidelinkmdlGeracaoRemessa"
                              styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
                <rich:componentControl for="mdlGeracaoRemessa" attachTo="hidelinkmdlGeracaoRemessa" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formmdlGeracaoRemessa">
            <h:panelGrid width="100%">
                <h:outputText styleClass="tituloCampos" value="Confirma a geração de Remessa com #{GestaoRemessasControle.qtdTotalSelecao} #{GestaoRemessasControle.qtdTotalSelecao > 1 ? 'itens' : 'item'}, no valor de #{GestaoRemessasControle.somaTotalSelecao}?"/>
            </h:panelGrid>

            <h:panelGrid width="100%" columnClasses="colunaCentralizada" columns="1">
                <a4j:commandButton styleClass="botaoPrimario texto-size-16"
                                   value="Criar" action="#{GestaoRemessasControle.criarRemessa}"
                                   oncomplete="#{GestaoRemessasControle.msgAlert}"
                                   reRender="panelConteudo,modalRemesaComErro">
                </a4j:commandButton>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="mdlCriarRemessaCielo" width="450" autosized="true" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>Atenção</h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText id="hidelinkmdlCriarRemessaCielo"
                              styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
                <rich:componentControl for="mdlCriarRemessaCielo" attachTo="hidelinkmdlCriarRemessaCielo"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formmdlCriarRemessaCielo">
            <h:panelGrid width="100%">
                <h:outputText styleClass="tituloCampos" value="Confirma a geração de Remessa com #{GestaoRemessasControle.agrupamentoParcelas.qtdContatoCieloSelecionadas} #{GestaoRemessasControle.agrupamentoParcelas.qtdContatoCieloSelecionadas > 1 ? 'itens' : 'item'}, no valor de #{GestaoRemessasControle.agrupamentoParcelas.valorContatoCieloSelecionadasFormatado}?"/>
            </h:panelGrid>

            <h:panelGrid width="100%" columnClasses="colunaCentralizada" columns="1">
                <a4j:commandButton styleClass="botaoPrimario texto-size-16"
                                   value="Criar" action="#{GestaoRemessasControle.criarRemessaContatoCielo}"
                                   oncomplete="#{GestaoRemessasControle.msgAlert}"
                                   reRender="panelConteudo,modalRemesaComErro">
                </a4j:commandButton>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="mdlCriarRemessaReenvio" width="450" autosized="true" shadowOpacity="true"
                     styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>Atenção</h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText id="hidelinkmdlCriarRemessaReenvio"
                              styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
                <rich:componentControl for="mdlCriarRemessaReenvio" attachTo="hidelinkmdlCriarRemessaReenvio"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formmdlCriarRemessaReenvio">
            <h:panelGrid width="100%">
                <h:outputText styleClass="tituloCampos" value="Confirma a geração de Remessa com #{GestaoRemessasControle.agrupamentoParcelas.qtdReenvioSelecionadas} #{GestaoRemessasControle.agrupamentoParcelas.qtdReenvioSelecionadas > 1 ? 'itens' : 'item'}, no valor de #{GestaoRemessasControle.agrupamentoParcelas.valorReenvioSelecionadasFormatado}?"/>
            </h:panelGrid>

            <h:panelGrid width="100%" columnClasses="colunaCentralizada" columns="1">
                <a4j:commandButton styleClass="botaoPrimario texto-size-16"
                                   value="Criar" action="#{GestaoRemessasControle.criarRemessaReenvio}"
                                   oncomplete="#{GestaoRemessasControle.msgAlert}"
                                   reRender="panelConteudo,modalRemesaComErro">
                </a4j:commandButton>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="modalRemesaComErro" width="510" height="450" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Autorização de cobrança incompatível"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink"/>
                <rich:componentControl for="modalRemesaComErro"
                                       attachTo="hidelink" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
            <h:panelGroup layout="block" styleClass="controles" rendered="#{fn:length(GestaoRemessasControle.listaRemessaComErro) > 0}">
                    <a4j:commandLink id="btnPDFAlunoErro"
                                     styleClass="exportadores"
                                     actionListener="#{ExportadorListaControle.exportar}"
                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                     accesskey="3">
                        <f:attribute name="lista" value="#{GestaoRemessasControle.listaRemessaComErro}"/>
                        <f:attribute name="filtro" value=""/>
                        <f:attribute name="tipo" value="pdf"/>
                        <f:attribute name="atributos" value="pessoa_Apresentar=Aluno(s)"/>
                        <f:attribute name="prefixo" value="Alunos com autorização incompatível"/>
                        <h:outputText title="PDF" styleClass="btn-print-2 pdf"/>
                    </a4j:commandLink>

                    <a4j:commandLink id="btnExcelAlunoErro"
                                     styleClass="exportadores"
                                     actionListener="#{ExportadorListaControle.exportar}"
                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                     accesskey="3">
                        <f:attribute name="lista" value="#{GestaoRemessasControle.listaRemessaComErro}"/>
                        <f:attribute name="filtro" value=""/>
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos" value="pessoa_Apresentar=Aluno(s)"/>
                        <f:attribute name="prefixo" value="Alunos com autorização incompatível"/>
                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>

            </h:panelGroup>
        </h:panelGroup>
        <a4j:form id="formRemessaErro">
            <rich:dataTable id="tabelaRemessaErro" value="#{GestaoRemessasControle.listaRemessaComErro}" var="remessa"
                            rows="10" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                            columnClasses="colunaAlinhamento">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Aluno(s)"/>
                    </f:facet>
                    <h:outputText value="#{remessa.pessoa.nome}"/> 
                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center" for="formRemessaErro:tabelaRemessaErro" maxPages="10"
                               style="margin-top: 11px;" id="scResultadoRemessaErro"/>
        </a4j:form>
        <h:outputText styleClass="mensagem" value="Total: #{fn:length(GestaoRemessasControle.listaRemessaComErro)}"
                      style="color:#777;"/>
    </rich:modalPanel>
</f:view>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        try {
            enviarBrowserGr(navigator.userAgent, screen.availWidth,
                screen.availHeight, document.location.protocol, document.location.origin);
        } catch (e) {
            console.log(e);
        }
    });
</script>
