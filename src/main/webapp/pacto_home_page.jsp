<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<link href="${root}/css/home_page_v1.1.css" rel="stylesheet" type="text/css"/>
<script>

    function abrirAlunoHomePage(matricula){
        window.location.href = "${pageContext.request.contextPath}/faces/clienteNav.jsp?page=cliente&matricula="+matricula;
    }

    var tokenHP = '${LoginControle.tokenNT}';

    async function listaRapidaHomePage() {
        const urlTreino = '${LoginControle.urlTreino}';
        const empresa = '${LoginControle.empresaLogado.codigo}';
        const endpoint = urlTreino + '/prest/psec/alunos/lista-rapida-acessos?tipo=2&limite=3';
        try {
            const response = await fetch(endpoint, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + tokenHP,
                    'empresaId': empresa,
                    'Content-Type': 'application/json'
                }
            });
            const buffer = await response.arrayBuffer();
            const decoder = new TextDecoder('UTF-8');
            const text = decoder.decode(buffer);
            const data = JSON.parse(text);
            const contentArray = data.content.lista;

            const containerGaveta = document.getElementById('container-home-acessos');
            containerGaveta.innerHTML = '';

            contentArray.forEach(function(item, i) {
                if(i > 2){
                    carregarTooltipster();
                    return;
                }
                var horarioAcesso = item.hora || 'Horário não disponível';
                var nomeCliente = item.nome || 'Nome não disponível';
                var planoCliente = item.plano || 'Plano não disponível';
                var parqCliente = item.parqAssinado ? item.parqAssinado.descricao : 'Par-q não disponível';
                var situacaoCliente = item.situacao;
                var situacaoContrato = item.situacaoContrato;
                var gympass = item.gympass;
                var totalpass = item.totalpass;
                var freepass = item.freepass;
                var diaria = item.diaria;
                var aulaAvulsa = item.aulaAvulsa;
                var avatarCliente = item.foto || 'imagens_flat/ds3_avatar.png';
                var alertaAcesso = item.aviso ? item.aviso.descricao : null;
                var alertasHtml = '';
                var alertas = null;
                if(item.avisos && item.avisos.length > 0){
                    for(var i = 0; i < item.avisos.length; i++){
                        if(alertas == null){
                            alertas = item.avisos[i].descricao;
                        }else{
                            alertas += '<br/>' + item.avisos[i].descricao;
                        }
                    }
                    alertasHtml = '<span class="acesso-mais-info tooltipster" title="'+alertas+'">+'+item.avisos.length+'</span>';
                }
                var alertaAniversario = item.aniversariante;

                var htmlContent =
                    '<div class="linha-gaveta '+ (item.divisor ? 'divisor-hora' : '')+'">' +
                    '<span class="horario-acesso-cliente">' + horarioAcesso + '</span>' +
                    '<span class="card-acesso-cliente">' +
                    '<span class="barra-cor-acesso ' + item.cor +'"></span>' +
                    '<span class="info-cliente-acesso">' +
                    '<span class="foto-nome-situacao-plano">' +
                    '<img src="' + avatarCliente + '"/>' +
                    '<span class="nome-plano-parq">' +
                    '<span class="acesso-gaveta-nome">' +
                    '<span style="cursor: pointer" onclick="abrirAlunoHomePage('+item.matricula+')">' + nomeCliente + '</span>' +
                    '<span class="acesso-situacoes">' +
                    '<span class="sit-acesso sit-'+situacaoCliente+'">' + situacaoCliente + '</span>' +
                    (situacaoContrato ? ('<span class="sit-acesso sit-'+situacaoContrato+'">') + situacaoContrato + '</span>' : '') +
                    (gympass ? '<span class="sit-acesso sit-gympass">GY</span>' : '') +
                    (totalpass ? '<span class="sit-acesso sit-totalPass">TP</span>' : '') +
                    (freepass ? '<span class="sit-acesso sit-freepass">PL</span>' : '') +
                    (diaria ? '<span class="sit-acesso sit-di">DI</span>' : '') +
                    (aulaAvulsa ? '<span class="sit-acesso sit-aa">AA</span>' : '') +
                    '</span>' +
                    '</span>' +
                    '<span class="acesso-gaveta-plano"><b>Plano:</b> ' + planoCliente + '</span>' +
                    '<span class="acesso-gaveta-parq"><b>Par-q:</b> ' + parqCliente + '</span>' +
                    '</span>' +
                    '</span>' +
                    (alertaAcesso ?
                        ('<span class="alerta-acesso-cliente"><i class="pct pct-info"></i> <span>' + alertaAcesso + '</span>' +
                            alertasHtml) : '') +
                    '</span>' +
                    (alertaAniversario ? ('<span class="alerta-aniversario-cliente"><img src="imagens_flat/pct-cake.png"/> Hoje é aniversário do cliente </span>') : '') +
                    '</span>' +
                    '</span>' +
                    '</div>';

                containerGaveta.insertAdjacentHTML('beforeend', htmlContent);
            });
            carregarTooltipster();
        } catch (error) {
            console.error('Erro ao buscar dados:', error);
        }
    }

    async function chamarFiltrosHP() {
        const pontoInterrogacaoMs = '${SuperControle.pontoInterrogacaoMs}';
        let url = '/notificacao/versoes/modulos'
        const response =  fetch((pontoInterrogacaoMs + url), {
            method: "GET",
            headers: {'Content-Type': 'application/json'}
        });
        return (await response).json();
    }
    async function chamarVersoesHP() {
        const pontoInterrogacaoMs = '${SuperControle.pontoInterrogacaoMs}';
        const url = pontoInterrogacaoMs + '/notificacao/versoes?usuario=${LoginControle.usuarioLogado.username}'
            + '&modulo='
            + '&chave=${LoginControle.key}'
            + '&zona=${SuperControle.zona}';
        const response =  fetch(url, {
            method: "GET",
            headers: {'Content-Type': 'application/json'}
        });
        return (await response).json();
    }

    async function initVersoesHome() {
        try {
            const idCaixa = '#itens-filtro-versoes-homepage';
            let data = await chamarVersoesHP();
            if (data.content && data.content) {
                versoes = data.content;
                populateVersoesHP(versoes, idCaixa);
            }
        } catch (e) {
            console.log(e);
        }
    }


    function populateVersoesHP(links, idCaixa) {
        try {
            var html = '';
            links.forEach(function (link, i) {
                if(i > 2){
                    return;
                }
                html += ('<div class="item-geral-versao">' +
                    '   <div class="item-geral-versao-icon">' +
                    '         <img src="imagens_flat/pct-icone-versao-'+link.modulo+'.svg" width="32"/>' +
                    '         </div>' +
                    '     <div class="item-geral-versao-info">' +
                    '         <div class="item-geral-versao-info-titulo">' +
                    '              '+link.titulo+' ' +
                    '         </div>' +
                    '         <div class="item-geral-versao-info-texto">' +
                    '             '+link.texto+' ' +
                    '         </div>' +
                    '         <div class="item-geral-versao-info-data">' +
                    '             <div class="item-geral-versao-info-data-hora">' +
                    '                 '+link.dataPublicacao+' ' +
                    '             </div>' +
                    '             <div>' +
                    '             <div class="home-link item-geral-versao-info-data-saiba" ' + (link.link ? '' : 'style="display: none" '  )  +
                    'onclick="clickSaibaMaisHP(\''+link.titulo+'\',\''+link.id+'\',\''+link.link+'\')">' +
                    '                 Saiba mais ' +
                    '             </div>' +
                    '             </div>' +
                    '         </div>' +
                    '     </div>' +
                    ' </div>');
            });

            jQuery(idCaixa).html(html);
            jQuery(idCaixa).show();
        }catch (e){
            console.log(e);
        }
    }

    async function  clickSaibaMaisHP(titulo, id, url) {
        try {
            await abrirNotificacoes('aba-versoes');
            try {
                const response = await fetch(pontoInterrogacaoMs + '/notificacao/html?id=' + id + '&url=' + url);
                const data = await response.text();
                const dataJson = JSON.parse(data);
                jQuery('#saiba-mais-titulo-html').html('');
                document.getElementById("saiba-mais-notificacoes").style.display = 'block';
                jQuery('#saiba-mais-titulo').html(titulo);
                jQuery('#saiba-mais-titulo-html').html(dataJson.content);
                setTimeout(function () {
                    jQuery('.div-notificacao-filtro-geral').hide();
                    document.getElementById("itens-alertas").style.display = 'none';
                    document.getElementById("itens-dicas").style.display = 'none';
                    document.getElementById("itens-versoes").style.display = 'none';
                }, 100);
            } catch (error) {
                console.error(error);
                jQuery('#saiba-mais-titulo-html').html('');
            }

        } catch (e) {
            console.log(e);
        }
    }
    var pontoInterrogacaoMs = '${SuperControle.pontoInterrogacaoMs}';
    var podeLancarAviso = '${LoginControle.podeLancarAviso}';
    function abrirNotificacoesHP() {
        try {
            let left = getOffset(document.getElementById('idNotificacoesVersoes')).left;
            document.getElementById("id-notificacoes-versoes").style.left = (left - 410) + 'px';
            document.getElementById('idNotificacoesVersoes').classList.add('topbar-item-selected');
        } catch (e) {
            console.log(e);
        }
        abaSelecionada = 'aba-versoes';
        jQuery('.item-menu-modal-notificacao-versao').removeClass('selecionado');
        jQuery('.conteudo-modal-notificacao-versao-aba').removeClass('aba-open');
        jQuery('.' + 'aba-versoes').addClass('selecionado');
        jQuery('.conteudo-modal-notificacao-versao-' + 'aba-versoes').addClass('aba-open');
        jQuery('.modal-notificacoes-versoes').removeClass('hidden');
        jQuery('.modal-notificacoes-versoes').addClass('modal-open');
        versoes = null;
        jQuery('#id-notificacoes-versoes').show();
        const idCaixa = '#itens-versoes';
        jQuery(idCaixa).hide();
        iniciarCarregandoNotificacao();
        let data = chamarVersoes('');
        if (data.content && data.content) {
            versoes = data.content;
            populateVersoes(versoes, idCaixa);
        }
        fimCarregandoNotificacao();
    }

    function abrirModalAvisos() {
        document.getElementById("modal-avisos-home-page").style.display = "block";
    }
</script>



<div class="home-page">
    <div class="full-width">
        <div class="ola-empresa">
            <div>Olá, ${SuperControle.usuarioLogado.nomePrimeiraLetraMaiuscula}</div>
            <div class="tooltipster"
                     title="${SuperControle.nomeEmpresaLogadaMinusculo}">${SuperControle.nomeEmpresaLogadaMinusculo}
            </div>
        </div>
        <div class="troca-empresa diviser">
            <div> Esta é a sua página inicial. Fixe algumas funcionalidades de sua preferência e fique por dentro das novidades do sistema e do seu negócio. Aproveite! </div>
            <div>
                <c:if test="${InicioControle.apresentarTrocaDeUnidades}">
                    <a4j:commandLink
                            styleClass="zw_ui_trocar_unidade"
                            reRender="form:panelModalTrocaUnidade,formTopo:panelModalTrocaUnidade"
                            id="trocarUnidadeUsuarioLink-home"
                            oncomplete="#{InicioControle.onComplete}"
                            action="#{InicioControle.abrirTrocaEmpresa}">
                        <div> Trocar Unidade <i _ngcontent-mhy-c6="" class="pct pct-repeat"></i></div>
                    </a4j:commandLink>
                </c:if>


                <c:if test="${!InicioControle.apresentarTrocaDeUnidades}">
                    <a href="">
                        <div class="zw_ui_trocar_unidade"><i _ngcontent-mhy-c6=""
                                                             class=""></i></div>
                    </a>
                </c:if>
            </div>
        </div>

        <div class="ola-empresa">
            <div class="fixados">Fixados</div>
            <div>
                <a4j:commandLink
                        styleClass="zw_ui_trocar_unidade"
                        id="fixar-home"
                        oncomplete="toggleModulos()">
                    <div><i class="pct pct-pin"></i> Fixar uma funcionalidade na home</div>
                </a4j:commandLink>
            </div>
        </div>

        <h:panelGroup layout="block" styleClass="fixados-itens" id="fixados-itens">
            <a4j:repeat value="#{MenuAcessoFacilControle.funcionalidades.favorito}" var="funcionalidade">
                <h:panelGroup id="menuFuncionalidade${funcionalidade}" rendered="#{funcionalidade.tipo == 'menulink' and funcionalidade.renderizar}"
                              layout="block" styleClass="fixado-item">
                    <h:panelGroup layout="block"
                                  styleClass="grupoMenuItemNomeContainer"
                                  style="max-width: 156px;">
                        <a4j:commandLink value="#{funcionalidade.descricaoMenulateral}"
                                         styleClass="link-fixado"
                                         style="margin-left: 2px;margin-right: 2px;"
                                         actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                         action="#{FuncionalidadeControle.abrirComCasoNavegacao}">
                            <f:attribute name="funcionalidade" value="#{funcionalidade.name}" />
                        </a4j:commandLink>
                        <h:panelGroup layout="block" styleClass="divNovidadeFav"
                                      rendered="#{funcionalidade.novidade}">
                            <h:outputText styleClass="new"
                                          value="NOVIDADE"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <a4j:commandLink  styleClass="menuItemLinkFixed" style="visibility: visible; margin: 0px;"
                                          actionListener="#{MenuAcessoFacilControle.removerFavorito}"
                                          oncomplete="#{MenuAcessoFacilControle.resultFavorito}"
                                          title="Remover" reRender="panelMenuAcessoRapido, panelMsg, fixados-itens" id="idRemoverFavorito-fixo">
                            <i class="pct pct-unpin"></i>
                            <f:attribute name="funcionalidade" value="#{funcionalidade}" />
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </a4j:repeat>
        </h:panelGroup>

    </div>

    <div style="display: flex;
    flex-wrap: nowrap;
    flex-direction: row;
    width: 100%;
    gap: 16px;
    padding-top: 16px;">
        <div class="home-half-width">
            <div class="home-column">
                <div class="ola-empresa diviser">
                    <div class="fixados">Últimos acessos de meus alunos</div>
                </div>
                <div id="container-home-acessos"></div>

                <div class="item-geral-versao-info-data">
                    <div class="home-link item-geral-versao-info-data-saiba" onclick="abrirGavetaAcessos()">
                        Ver todos os acessos
                    </div>
                </div>

            </div>
            <script>
                listaRapidaHomePage();
            </script>

            <h:panelGroup styleClass="home-column">
                <div class="ola-empresa diviser" style="margin-bottom: 0">
                    <div class="fixados">Mural de avisos internos</div>
                </div>

                <h:panelGroup id="avisos-painel" styleClass="lista-avisos">

                    <a4j:repeat id="avisos-items" value="#{LoginControle.avisosInternos}" var="aviso" rowKeyVar="index">
                        <div class="avisos-item ng-star-inserted">
                            <div class="avisos-body" style="width: 100%">
                                <div class="avisos-content div-aviso-utf8">
                                    <p><h:outputText value="#{aviso.aviso}"/></p>
                                </div>
                                <div class="avisos-footer">
                                    <h3><h:outputText value="#{aviso.autor}"/></h3>
                                    <p><h:outputText value="#{aviso.dataPublicacao}"/></p>
                                </div>
                            </div>
                            <div class="menu-container">
                                <a4j:commandLink styleClass="menu-button" onclick="toggleMenuAviso(#{index}); return false;">
                                    <i class="pct pct-more-vertical"></i>
                                </a4j:commandLink>
                                <h:panelGroup styleClass="dropdown-menu idx#{index}">
                                    <a4j:commandLink styleClass="dropdown-item"
                                                     reRender="modalGravarAviso"
                                                     id="botaoEditAviso"
                                                     oncomplete="Richfaces.showModalPanel('modalGravarAviso')"
                                                     action="#{MenuAvisosControle.editarAviso}"
                                    >
                                        <span style="border-bottom: 1px solid #f0f0f0;">Editar aviso</span>
                                        <f:param name="codigo" value="#{aviso.codigo}" />
                                    </a4j:commandLink>
                                    <a4j:commandLink style="border-bottom: 1px solid #f0f0f0;"
                                                     styleClass="dropdown-item delete"
                                                     reRender="avisos-painel"
                                                     id="botaoDeleteAviso"
                                                     oncomplete="#{MenuAvisosControle.msgAlert}"
                                                     action="#{MenuAvisosControle.deletarAviso}"
                                    >
                                        <span style="border-bottom: 1px solid #f0f0f0;">Excluir aviso</span>
                                        <f:param name="codigo" value="#{aviso.codigo}" />
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </div>
                        </div>
                    </a4j:repeat>

                    <script>
                        function toggleMenuAviso(idx){
                            document.querySelector('.idx'+idx).classList.toggle('show');
                        }

                        function editAviso(idx, codigo){
                            document.querySelector('.idx'+idx).classList.remove('show');
                        }

                        function deleteAviso(idx, codigo){
                            document.querySelector('.idx'+idx).classList.remove('show');
                        }
                    </script>

                </h:panelGroup>

                <h:panelGroup styleClass="item-geral-versao-info-data" rendered="#{LoginControle.podeLancarAviso}">
                    <a4j:commandLink reRender="modalGravarAviso"
                                     action="#{MenuAvisosControle.novoAviso}"
                                     oncomplete="Richfaces.showModalPanel('modalGravarAviso')">
                        <h:panelGroup layout="block" style="margin-top: 16px" styleClass="home-link item-geral-versao-info-data-saiba">
                            Adicionar novo aviso
                        </h:panelGroup>
                    </a4j:commandLink>
                </h:panelGroup>

            </h:panelGroup>

            <h:inputHidden id="codigoSelecao" value="#{MenuAvisosControle.codigo}" />

        </div>

        <div class="home-half-width">
            <div class="home-column container-conteudo-central home-banner">
                <c:if test="${SuporteControle.exibirClubeDeBeneficios}">
                    <iframe id="xOutput" width="100%" height="100%" scrolling="auto"
                            src="${SuporteControle.clubeDeBeneficios.link}" frameborder="0"></iframe>
                </c:if>
                <c:if test="${SuperControle.bannerEmergencia}">
                    <a href="#" class="bannerPadrao" target="_blank">
                        <img border="none" class="img-responsive imagemApresentacao"
                             src="${LoginControle.urlBannerEmergencia}"/>
                    </a>
                </c:if>

                <c:if test="${not SuperControle.bannerEmergencia && SuperControle.bannerRetro && not SuporteControle.exibirClubeDeBeneficios}">
                    <a href="https://game.pactosolucoes.com.br/core/retrospectiva/2024/myyear.html?chave=${SuperControle.key}&empresa=${SuperControle.empresaLogado.codigo}"
                       class="bannerPadrao" target="_blank">
                        <img border="none" class="img-responsive imagemApresentacao"
                             src="${SuporteControle.urlImagemRetrospectiva}"/>
                    </a>
                </c:if>
                <c:if test="${not SuperControle.bannerEmergencia && not SuperControle.bannerRetro && not SuporteControle.exibirClubeDeBeneficios}">
                    <a href="#" class="bannerPadrao">
                        <img border="none" class="img-responsive imagem-blur-bottom imagemApresentacao"
                             src="${SuporteControle.urlImagemBannerRespiracao}"/>
                    </a>
                    <div id="myCarousel" class="carousel slide container-conteudo-central">
                        <div class="carousel-inner">
                            <c:forEach items="${SuporteControle.banners}" var="ban" varStatus="ind">
                                <div class="item ${ind.count == 1 ? 'active' : ''}">

                                    <c:if test="${not empty ban.funcionalidade}">
                                        <a onclick="abrirFuncionalidade('${ban.funcionalidade}')"
                                           style="cursor: pointer">
                                            <img border="none" style="max-width:100%"
                                                 src="${ban.urlImagem}">
                                        </a>
                                    </c:if>

                                    <c:if test="${empty ban.funcionalidade}">
                                        <c:if test="${not empty ban.urlLink}">
                                            <a onclick="clickNotificar()"
                                               href="${ban.urlLink}"
                                               target="_blank">
                                                <img border="none" style="max-width:100%"
                                                     src="${ban.urlImagem}">
                                            </a>
                                        </c:if>
                                        <c:if test="${empty ban.urlLink}">
                                            <img border="none" style="width:100%"
                                                 src="${ban.urlImagem}">
                                        </c:if>
                                    </c:if>
                                </div>
                            </c:forEach>
                        </div>

                        <ol class="carousel-indicators">
                            <c:forEach items="${SuporteControle.banners}" var="ban"
                                       varStatus="ind">
                                <li style="cursor: pointer;" data-target="#myCarousel"
                                    data-slide-to="${ind.count -1}"
                                    class="${ind.count == 1 ? 'active' : ''}"></li>
                            </c:forEach>
                        </ol>
                    </div>
                    <script>
                        validarTamanhoBanner(true);
                    </script>

                    <a4j:commandLink
                            status="false"
                            id="notificarClickBanner"
                            style="display: none"
                            action="#{SuporteControle.notificarClick}">
                    </a4j:commandLink>

                    <a4j:commandLink id="funcionalidadeAbrirClick"
                                     style="display: none"
                                     action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                     actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                     oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                        <f:attribute name="funcionalidade" value="#{SuporteControle.funcionalidadeAbrir}"/>
                    </a4j:commandLink>

                    <h:inputHidden id="funcionalidadeAbrir"
                                   value="#{SuporteControle.funcionalidadeAbrir}"/>
                </c:if>

                <h:panelGroup layout="block" styleClass="container-cards"
                              rendered="#{not empty BlogControle.itemsCampanhaBlog}">
                    <h:outputText styleClass="headerBlog" value="Novidades do Blog"/>
                    <a4j:repeat value="#{BlogControle.itemsCampanhaBlog}" var="itemCampanha">

                        <h:panelGroup layout="block" styleClass="card-blog">
                            <h:outputLink onclick="window.open('#{itemCampanha.url}','_blank')">
                                <h:graphicImage value="#{itemCampanha.urlImagem}"/>
                                <h:panelGroup layout="block" styleClass="conteudoArtigo">
                                    <h:outputText styleClass="titulo-artigo" value="#{itemCampanha.titulo}"/>
                                    <h:outputText styleClass="texto-artigo" value="#{itemCampanha.texto}"/>
                                </h:panelGroup>
                            </h:outputLink>
                        </h:panelGroup>

                    </a4j:repeat>
                </h:panelGroup>
                <a4j:commandButton style="display: none;" reRender="panelExpiracaoSenha"
                                   id="btnAtualizaPagina">
                </a4j:commandButton>
            </div>


            <div class="home-column">
                <div class="ola-empresa diviser">
                    <div class="fixados">Fique por dentro das últimas mudanças</div>
                </div>

                <div id="itens-filtro-versoes-homepage"></div>

                <div class="item-geral-versao-info-data">
                    <div class="home-link item-geral-versao-info-data-saiba" onclick="abrirNotificacoes('aba-versoes')">
                        Ver todas as novidades
                    </div>
                </div>
            </div>
            <script>
                initVersoesHome();
            </script>
        </div>
    </div>
</div>













