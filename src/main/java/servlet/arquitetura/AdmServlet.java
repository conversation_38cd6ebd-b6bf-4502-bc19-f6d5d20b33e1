package servlet.arquitetura;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioPerfilAcesso;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import org.json.JSONObject;
import servicos.integracao.adm.beans.EmpresaWS;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class AdmServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
            response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("obterEmpresasUsuario")) {
                envelopeRespostaDTO = obterEmpresasUsuario(request);
            } else if (operacao.equalsIgnoreCase("obterEmpresasUsuarioCodigos")) {
                envelopeRespostaDTO = obterEmpresasUsuarioCodigos(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String chave = request.getParameter("chave");
        if (UteisValidacao.emptyString(chave)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(chave.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private EnvelopeRespostaDTO obterEmpresasUsuarioCodigos(ServletRequest request) throws Exception {
        List<EmpresaWS> empresas = obterEmpresasUsuarioBase(request);
        String empresascod = "";
        for (EmpresaWS empresa : empresas) {
            empresascod += "," + empresa.getCodigo();
        }
        return EnvelopeRespostaDTO.of(empresascod.replaceFirst(",", ""));
    }

    private EnvelopeRespostaDTO obterEmpresasUsuario(ServletRequest request) throws Exception {
        List<EmpresaWS> empresas = obterEmpresasUsuarioBase(request);
        return EnvelopeRespostaDTO.of(empresas);
    }

    private List<EmpresaWS> obterEmpresasUsuarioBase(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        UsuarioPerfilAcesso usuarioPerfilAcessoDAO;
        Empresa empresaDAO;
        Colaborador colaboradorDAO;
        try (Connection con = obterConexao(request)) {
            usuarioDAO = new Usuario(con);
            usuarioPerfilAcessoDAO = new UsuarioPerfilAcesso(con);
            empresaDAO = new Empresa(con);
            colaboradorDAO = new Colaborador(con);

            Integer usuario = UteisValidacao.converterInteiro(request.getParameter("usuario"));

            if (!usuarioDAO.consultarUsuarioAtivo(usuario)) {
                throw new Exception("Usuário inativo");
            }

            List<UsuarioPerfilAcessoVO> listaPerfisUsuario = usuarioPerfilAcessoDAO.consultarUsuarioPerfilAcesso(usuario, Uteis.NIVELMONTARDADOS_MINIMOS);
            List<EmpresaWS> empresas = new ArrayList<>();
            for (UsuarioPerfilAcessoVO perfil : listaPerfisUsuario) {
                EmpresaVO emp = empresaDAO.consultarPorChavePrimaria(perfil.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ColaboradorVO colaboradorVO = colaboradorDAO.consultarPorUsuarioEmpresaComBasePessoa(usuario, emp.getCodigo());
                EmpresaWS empWS = new EmpresaWS(emp);
                if (colaboradorVO != null) {
                    empWS.setCodigoColaborador(colaboradorVO.getCodigo());
                }
                empWS.setDescricaoPerfil(perfil.getPerfilAcesso().getNome());
                empresas.add(empWS);
            }
            return empresas;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            usuarioPerfilAcessoDAO = null;
            empresaDAO = null;
            colaboradorDAO = null;
        }
    }
}
