package servlet.arquitetura;

import br.com.pactosolucoes.integracao.pactopay.front.AlunoBuscaDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 05/07/2021
 */
public class ConsultaClienteServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.addHeader("Access-Control-Allow-Headers", "Authorization");
            response.setContentType("application/json");

            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(consultarClientes(request)).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private EnvelopeRespostaDTO consultarClientes(ServletRequest request) throws Exception {
        Connection con = null;
        try {
            con = obterConexao(request);

            String parametro = request.getParameter("p");
            Integer empresa = obterEmpresa(request, false);

//            boolean comFoto = false;
//            try {
//                comFoto = (request.getParameter("f").equalsIgnoreCase("t") || request.getParameter("f").equalsIgnoreCase("true"));
//            } catch (Exception ignored) {
//            }

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;


//            boolean somenteNumeros = UteisValidacao.somenteNumeros(parametro);
            String letras = Uteis.tirarCaracteres(parametro, false);
            String numeros = Uteis.tirarCaracteres(parametro, true);
            boolean buscarCPF = numeros != null && numeros.length() >= 11;

            StringBuilder sql = new StringBuilder();
            sql.append("select * from (\n");
            sql.append("select \n");
            sql.append("3 as peso, \n");
            sql.append("cl.matricula, \n");
            sql.append("p.nome, \n");
            sql.append("p.codigo as pessoa, \n");
            sql.append("cl.codigo as cliente, \n");
            sql.append("e.nome as empresa, \n");
            sql.append("p.fotokey \n");
            sql.append("from cliente cl \n");
            sql.append("inner join empresa e on e.codigo = cl.empresa \n");
            sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
            sql.append("inner join situacaoclientesinteticodw sw on sw.codigopessoa = p.codigo \n");
            if (UteisValidacao.emptyString(letras)) {
                sql.append("where false \n");
            } else {
                sql.append("where sw.nomeconsulta LIKE remove_acento_upper(?) \n");
            }

            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append("and cl.empresa = ? \n");
            }

            if (!UteisValidacao.emptyString(numeros)) {
                sql.append("union \n");
                sql.append("select \n");
                sql.append("2 as peso, \n");
                sql.append("cl.matricula, \n");
                sql.append("p.nome, \n");
                sql.append("p.codigo as pessoa, \n");
                sql.append("cl.codigo as cliente, \n");
                sql.append("e.nome as empresa, \n");
                sql.append("p.fotokey \n");
                sql.append("from cliente cl \n");
                sql.append("inner join empresa e on e.codigo = cl.empresa \n");
                sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
                sql.append("where (cl.matricula = ? or cl.codigomatricula = ?) \n");
                if (!UteisValidacao.emptyNumber(empresa)) {
                    sql.append("and cl.empresa = ? \n");
                }
            }

            if (buscarCPF) {
                sql.append("union \n");
                sql.append("select \n");
                sql.append("1 as peso, \n");
                sql.append("cl.matricula, \n");
                sql.append("p.nome, \n");
                sql.append("p.codigo as pessoa, \n");
                sql.append("cl.codigo as cliente, \n");
                sql.append("e.nome as empresa, \n");
                sql.append("p.fotokey \n");
                sql.append("from cliente cl \n");
                sql.append("inner join empresa e on e.codigo = cl.empresa \n");
                sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
                sql.append("where (p.cfp = ? or p.cfp = ?) \n");
                if (!UteisValidacao.emptyNumber(empresa)) {
                    sql.append("and cl.empresa = ? \n");
                }
            }

            sql.append(") as sql \n");
            sql.append("order by peso,nome \n");

            // Preparar parâmetros para PreparedStatement
            List<Object> parametros = new ArrayList<>();

            // Parâmetros para busca por letras
            if (!UteisValidacao.emptyString(letras)) {
                parametros.add("%" + letras.toUpperCase() + "%");
            }
            // Parâmetro empresa para busca por letras (se empresa estiver preenchida)
            if (!UteisValidacao.emptyNumber(empresa)) {
                parametros.add(empresa);
            }

            // Parâmetros para busca por números
            if (!UteisValidacao.emptyString(numeros)) {
                parametros.add(numeros);
                parametros.add(Integer.parseInt(numeros));
                // Parâmetro empresa para busca por números (se empresa estiver preenchida)
                if (!UteisValidacao.emptyNumber(empresa)) {
                    parametros.add(empresa);
                }
            }

            // Parâmetros para busca por CPF
            if (buscarCPF) {
                parametros.add(Uteis.formatarCpfCnpj(numeros, true));
                parametros.add(Uteis.formatarCpfCnpj(numeros, false));
                // Parâmetro empresa para busca por CPF (se empresa estiver preenchida)
                if (!UteisValidacao.emptyNumber(empresa)) {
                    parametros.add(empresa);
                }
            }

            // Contar total de registros
            String sqlCount = "SELECT COUNT(*) FROM (" + sql.toString() + ") as qtd";
            Integer total;
            try (PreparedStatement pstCount = con.prepareStatement(sqlCount)) {
                for (int i = 0; i < parametros.size(); i++) {
                    pstCount.setObject(i + 1, parametros.get(i));
                }
                try (ResultSet rsCount = pstCount.executeQuery()) {
                    rsCount.next();
                    total = rsCount.getInt(1);
                }
            }

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            sql.append("LIMIT ? OFFSET ? \n");
            parametros.add(maxResults);
            parametros.add(indiceInicial);

            List<AlunoBuscaDTO> lista = new ArrayList<>();
            try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
                for (int i = 0; i < parametros.size(); i++) {
                    pst.setObject(i + 1, parametros.get(i));
                }
                try (ResultSet rs = pst.executeQuery()) {
                    while (rs.next()) {
                        try {
                            AlunoBuscaDTO dto = new AlunoBuscaDTO();
                            dto.setMatricula(rs.getString("matricula"));
                            dto.setNome(rs.getString("nome"));
                            dto.setCliente(rs.getInt("cliente"));
                            dto.setPessoa(rs.getInt("pessoa"));
                            dto.setEmpresa(rs.getString("empresa"));
                            dto.setFoto(Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                            lista.add(dto);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            }
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private Integer obterEmpresa(ServletRequest request, boolean validar) throws Exception {
        Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
        if (UteisValidacao.emptyNumber(empresa)) {
            empresa = UteisValidacao.converterInteiro(request.getParameter("e"));
        }
        if (validar && UteisValidacao.emptyNumber(empresa)) {
            throw new Exception("Empresa não informado");
        }
        return empresa;
    }
}
