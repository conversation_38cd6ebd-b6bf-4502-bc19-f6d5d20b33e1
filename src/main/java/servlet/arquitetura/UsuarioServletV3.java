package servlet.arquitetura;

import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.exceptions.ParametroInvalidoException;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.arquitetura.response.TokenResponseTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.OpcaoPerfilAcesso;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.PerfilAcesso;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioPerfilAcesso;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class UsuarioServletV3 extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");

        String chave = request.getParameter("chave");
        Integer codUsuario = Integer.parseInt(request.getParameter("usuario"));
        Integer empresaId = Integer.parseInt(request.getParameter("empresaId"));

        UsuarioInterfaceFacade usuarioService = null;
        UsuarioPerfilAcesso usuarioPerfilAcessoService = null;
        PerfilAcessoInterfaceFacade perfilAcessoService = null;
        EmpresaInterfaceFacade empresaService = null;
        try (Connection c = new DAO().obterConexaoEspecifica(chave)) {
            validarParametros(codUsuario, empresaId);
            usuarioService = new Usuario(c);
            usuarioPerfilAcessoService = new UsuarioPerfilAcesso(c);
            perfilAcessoService = new PerfilAcesso(c);
            empresaService = new Empresa(c);

            UsuarioVO usuarioVO = usuarioService.consultarPorCodigo(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(usuarioVO == null){
                throw new Exception("Usuário não encontrado");
            }
            usuarioVO.setUsuarioPerfilAcessoVOs(usuarioPerfilAcessoService.consultarUsuarioPerfilAcessoEmpresaAtiva(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            for (UsuarioPerfilAcessoVO usuarioPerfilAcessoVO : usuarioVO.getUsuarioPerfilAcessoVOs()) {
                if (usuarioPerfilAcessoVO.getEmpresa().getCodigo().equals(empresaId)) {
                    PerfilAcessoVO perfilAcessoVO = perfilAcessoService.consultarPorChavePrimaria(usuarioPerfilAcessoVO.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                    usuarioPerfilAcessoVO.setPerfilAcesso(perfilAcessoVO);
                    break;
                }
            }

            List<EmpresaVO> empresas = empresaService.consultarEmpresas(Uteis.NIVELMONTARDADOS_MINIMOS);

            List<EmpresaVO> empresasPermitidas = new ArrayList<>();
            for (UsuarioPerfilAcessoVO upaVO : usuarioVO.getUsuarioPerfilAcessoVOs()) {
                for (EmpresaVO empresaVO : empresas) {
                    if (upaVO.getEmpresa().getCodigo().equals(empresaVO.getCodigo())) {
                        empresasPermitidas.add(empresaVO);
                    }
                }
            }

            //para o momento de liberação de nova tela de negociacao, faz se necessario incluir uma permissao fake
            //para que o usuario possa acessar a tela
            boolean temPlanoPacote = empresaService.temPlanoPacote(empresaId);
            boolean novaNegociacaoHabilitada = usuarioService.recursoHabilitado(TipoInfoMigracaoEnum.NEGOCIACAO, usuarioVO.getCodigo(), empresaId);
            boolean temPermissao340Habilitada = false;
            for (PermissaoVO permissaoVO : usuarioVO.getUsuarioPerfilAcessoVOs().get(0).getPerfilAcesso().getPermissaoVOs()) {
                if (permissaoVO.getNomeEntidade().equals("VendaRapidaTelaPadraoLancarContrato")) {
                    temPermissao340Habilitada = true;
                    break;
                }
            }
            if((!temPlanoPacote &&
                    novaNegociacaoHabilitada &&
                    !temPermissao340Habilitada) || Uteis.isAmbienteDesenvolvimentoTeste()){
                PermissaoVO permissao = new PermissaoVO();
                permissao.setNomeEntidade("sem_plano_pacote");
                permissao.setPermissoes("(0)(1)(2)(3)(9)(12)");
                permissao.setTipoPermissao(OpcaoPerfilAcesso.TP_FUNCIONALIDADE);
                permissao.setTituloApresentacao("9999.9 - Sem Plano Pacote");
                adicionarPermissao(usuarioVO, empresaId, permissao);
            }

            //Novo - Caixa em aberto
            boolean empresaUsaPinpad = empresaService.empresaUsaPinpad(empresaId);
            boolean novoCaixaEmAbertoHabilitado = usuarioService.recursoHabilitado(TipoInfoMigracaoEnum.CAIXA_ABERTO, usuarioVO.getCodigo(), empresaId);
            if((!empresaUsaPinpad &&
                    novoCaixaEmAbertoHabilitado) || Uteis.isAmbienteDesenvolvimentoTeste()){
                PermissaoVO permissao = new PermissaoVO();
                permissao.setNomeEntidade("novo_caixa_aberto");
                permissao.setPermissoes("(0)(1)(2)(3)(9)(12)");
                permissao.setTipoPermissao(OpcaoPerfilAcesso.TP_FUNCIONALIDADE);
                permissao.setTituloApresentacao("9999.8 - Novo caixa em aberto");
                adicionarPermissao(usuarioVO, empresaId, permissao);
            }

            //Novo - Cadastro de cliente
            boolean novoIncluirClienteHabilitado = usuarioService.recursoHabilitado(TipoInfoMigracaoEnum.INCLUIR_CLIENTE, usuarioVO.getCodigo(), empresaId);
            if(novoIncluirClienteHabilitado || Uteis.isAmbienteDesenvolvimentoTeste()){
                PermissaoVO permissao = new PermissaoVO();
                permissao.setNomeEntidade("novo_incluir_cliente");
                permissao.setPermissoes("(0)(1)(2)(3)(9)(12)");
                permissao.setTipoPermissao(OpcaoPerfilAcesso.TP_FUNCIONALIDADE);
                permissao.setTituloApresentacao("9999.7 - Novo Incluir Cliente");
                adicionarPermissao(usuarioVO, empresaId, permissao);
            }

            usuarioService.registrarUltimoLoginAcessoAgora(usuarioVO);

            JSONObject resposta = new JSONObject();
            resposta.put("content", new JSONObject(new TokenResponseTO(usuarioVO, empresasPermitidas, empresaId).toJSON()));
            response.getWriter().append(resposta.toString());
        } catch (Exception e) {
            processarErro(e, request, response, null);
        } finally {
            usuarioService = null;
            usuarioPerfilAcessoService = null;
            perfilAcessoService = null;
            empresaService = null;
        }
    }

    private void adicionarPermissao(UsuarioVO usuarioVO, Integer empresaId, PermissaoVO permissao) {
        if (UteisValidacao.emptyNumber(empresaId)) {
            usuarioVO.getUsuarioPerfilAcessoVOs().get(0).getPerfilAcesso().getPermissaoVOs().add(permissao);
        } else {
            for (UsuarioPerfilAcessoVO usuarioPerfilAcessoVO : usuarioVO.getUsuarioPerfilAcessoVOs()) {
                if (usuarioPerfilAcessoVO.getEmpresa().getCodigo().equals(empresaId)) {
                    usuarioPerfilAcessoVO.getPerfilAcesso().getPermissaoVOs().add(permissao);
                }
            }
        }
    }

    private void validarParametros(Integer codUsuario, Integer empresaId) throws ParametroInvalidoException {
        String msgErro = "";
        if(codUsuario == null) {
            msgErro = "O código do usuário não foi informado";
        }
        if(codUsuario.equals(0)) {
            msgErro += "O código do usuário não pode ser 0";
        }

        if(empresaId == null) {
            msgErro += "O código da empresa não foi informado";
        }

        if(empresaId.equals(0)) {
            msgErro += "O código da empresa não pode ser 0";
        }

        if(!msgErro.isEmpty())
            throw new ParametroInvalidoException(msgErro);
    }
}
