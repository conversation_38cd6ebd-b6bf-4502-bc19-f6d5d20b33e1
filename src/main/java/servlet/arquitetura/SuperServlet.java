package servlet.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import com.google.api.client.json.Json;
import com.google.common.net.HttpHeaders;
import controle.arquitetura.exceptions.ParametroInvalidoException;
import controle.arquitetura.exceptions.ParametroObrigatorioException;
import controle.arquitetura.exceptions.ServiceException;
import controle.arquitetura.exceptions.TokenInvalidoException;
import negocio.comuns.arquitetura.UsuarioVO;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryPoolMXBean;
import java.sql.SQLException;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.text.ParseException;
import java.util.Date;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeFactory;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.facade.jdbc.utilitarias.ConnectionSerializable;
import org.json.JSONObject;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class SuperServlet extends HttpServlet {

    private String chave = "";
    private int parametroPagina;
    private int parametroLimit;
    private int parametroOffset;
    private int parametroEmpresa;

    public FacadeFactory ff(HttpServletRequest request) {
        FacadeFactory ff = (FacadeFactory) request.getSession().getAttribute(FacadeFactory.class.getSimpleName());
        final String k = (String) getAttribute(request, JSFUtilities.KEY);
        final ConnectionSerializable c = (ConnectionSerializable) getAttribute(request, JSFUtilities.CON);
        if (k != null && c != null) {
            try {
                Conexao.guardarConexaoForServlet(k, c.getCon());
            } catch (SQLException ex) {
                Logger.getLogger(SuperServlet.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return ff;
    }

    protected JSONObject getJSONBody(HttpServletRequest request) throws IOException {
        request.setCharacterEncoding("UTF-8");
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = request.getReader();
        try {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line).append('\n');
            }
        } finally {
            reader.close();
        }

        return new JSONObject(sb.toString());
    }

    protected void processResponseContenteType(HttpServletResponse response) {
        response.setContentType("application/json");
        response.setCharacterEncoding("ISO-8859-1");
    }

    protected Object getAttribute(HttpServletRequest r, final String name) {
        return r.getSession().getAttribute(name);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        MemoryPoolMXBean iter1 = ManagementFactory.getMemoryPoolMXBeans().get(1);
        System.out.println("Usage: " + iter1.getUsage());

        processResponseContenteType(response);

        UsuarioVO usuarioLogado = (UsuarioVO) getAttribute(request, JSFUtilities.LOGGED);
        if (usuarioLogado == null || usuarioLogado.getCodigo() <= 0) {
            String key = request.getParameter("chave");
            if (key != null && key.length() > 0) {
                setChave(key);
            } else {
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Usuário não autorizado!");
                setChave((String) getAttribute(request, JSFUtilities.KEY));
            }
        } else {
            setChave((String) getAttribute(request, JSFUtilities.KEY));
        }
    }

    void processRequestAPI(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MemoryPoolMXBean iter1 = ManagementFactory.getMemoryPoolMXBeans().get(1);
        System.out.println("Usage: " + iter1.getUsage());

        processResponseContenteType(response);

        UsuarioVO usuarioLogado = JSFUtilities.getUsuarioLogado(request);

        if (usuarioLogado == null || usuarioLogado.getCodigo() <= 0) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Usuario nao utorizado!");
        }
    }

    protected void processarParametros(HttpServletRequest request) {
        setParametroEmpresa(request.getParameter("empresa") == null ? 0 : Integer.parseInt(request.getParameter("empresa")));
        processarParametrosPaginacao(request);
    }

    private void processarParametrosPaginacao(HttpServletRequest request) {
        setParametroLimit(request.getParameter("limitePorPagina") == null ? 0 : Integer.parseInt(request.getParameter("limitePorPagina")));
        int pagina = request.getParameter("pagina") == null ? 0 : Integer.parseInt(request.getParameter("pagina"));
        setParametroPagina(pagina);
        setParametroOffset(pagina == 0 ? 0 : (pagina - 1) * getParametroLimit() + 1);
    }

    protected Integer obterParametro(Object parametro) {
        return RecuperadorParametrosServlet.obterParametro(parametro);
    }

    protected Integer obterParametroTratado(Object parametro) {
        try {
            return RecuperadorParametrosServlet.obterParametro(parametro);
        } catch (Exception ex) {
            Uteis.logar(ex, SuperServlet.class);
            return null;
        }
    }

    protected String obterParametroString(Object parametro) {
        return RecuperadorParametrosServlet.obterParametroString(parametro);
    }

    protected Date obterParametroDate(Object parametro) throws ParseException {
        return RecuperadorParametrosServlet.obterParametroDate(parametro);
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void validarParametrosObrigatorios(HttpServletRequest request, String[] parametrosObrigatorios) throws Exception {
        String parametrosInvalidos = "";

        for (String nomeParametro : parametrosObrigatorios) {
            String valorParametro = request.getParameter(nomeParametro);
            if(valorParametro == null || valorParametro.trim().isEmpty()){
                parametrosInvalidos += nomeParametro+" ";
            }
        }

        if(!parametrosInvalidos.isEmpty()){
            throw new ParametroObrigatorioException(parametrosInvalidos);
        }

    }

    protected void validarAuthorizationComChave(HttpServletRequest request) throws TokenInvalidoException {
        String chave = request.getHeader(HttpHeaders.AUTHORIZATION);

        if(chave == null || chave.trim().isEmpty()){
            throw new TokenInvalidoException();
        }
    }

    protected void processarErro(Exception exc, HttpServletRequest req, HttpServletResponse res, JSONObject formatoPayload) throws IOException {
        String mensagem = "";

        if(exc != null && exc.getMessage() != null){
            System.out.println("Erro na api rest "+exc.getMessage());

            if (exc.getMessage().contains("encontrado empresa com essa chave")) {
                res.setStatus(400);
                mensagem = "Parametro chave invalida";
            }

            if(exc instanceof ServiceException){
                res.setStatus(400);
                mensagem = exc.getMessage();
            }

            if(exc.getMessage().toLowerCase().contains("informe") || exc.getMessage().toLowerCase().contains("O campo")){
                res.setStatus(400);
                mensagem = exc.getMessage();
            }

            if (exc.getClass().getName().equals("org.json.JSONException") || exc.getMessage().contains("JSONException") || exc.getMessage().contains("JSONObject")
                    || exc.getMessage().contains("JSONArray")) {
                mensagem = "O formato dos dados são inválidos.";
                if (formatoPayload != null) {
                    mensagem += "O formato correto é:" + formatoPayload;
                }
            }

            if(exc instanceof TokenInvalidoException){
                res.setStatus(401);
                mensagem = exc.getMessage();
            }

            if(exc instanceof ParametroObrigatorioException || exc instanceof ParametroInvalidoException){
                res.setStatus(400);
                mensagem = exc.getMessage();
            }
        }

        if(exc != null){
            exc.printStackTrace();
        }

        if (mensagem.length() == 0) {
            res.setStatus(500);
            mensagem = "Ocorreu algo inesperado ao processar a requisição. Por favor, entre em contato com nosso suporte;";
            if (exc != null && isNotBlank(exc.getMessage())) {
                mensagem += " Mensagem tecnica: " + exc.getMessage();
            }
        }

        res.getWriter().append("{" + "\"sucesso\": \"false\"," + "\"mensagem\": \"").append(mensagem).append("\"").append("}");
    }

    protected void processarErro(String mensagem, HttpServletResponse res) throws IOException {
        res.getWriter().append("{" + "\"sucesso\": \"false\"," + "\"mensagem\": \"").append(mensagem).append("\"").append("}");
    }

    /**
     * Obtem o nome do grupo de recursos se a url estiver no padrão /{modulo}/{recurso}
     * @return nome do modulo
     */
    public String getModulo(HttpServletRequest request){
        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        return pathParts[2];
    }

    /**
     * Obtem o nome do recurso se a url estiver no padrão /{modulo}/{recurso}
     * @return nome do recurso
     */
    public String getRecurso(HttpServletRequest request){
        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        return pathParts[3];
    }

    /**
     * Obtem o nome do recurso se a url estiver no padrão /{modulo}/{recurso}/{codigo}
     * @return nome do recurso
     */
    public String getCodigoRecurso(HttpServletRequest request){
        String path = request.getPathInfo();
        if(path != null){
            String[] pathParts = path.split("/");

            if(pathParts.length > 0){
                return pathParts[1];
            }
        }

        return null;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public int getParametroLimit() {
        return parametroLimit;
    }

    public void setParametroLimit(int parametroLimit) {
        this.parametroLimit = parametroLimit;
    }

    public int getParametroOffset() {
        return parametroOffset;
    }

    public void setParametroOffset(int parametroOffset) {
        this.parametroOffset = parametroOffset;
    }

    public int getParametroEmpresa() {
        return parametroEmpresa;
    }

    public void setParametroEmpresa(int parametroEmpresa) {
        this.parametroEmpresa = parametroEmpresa;
    }

    public int getParametroPagina() {
        return parametroPagina;
    }

    public void setParametroPagina(int parametroPagina) {
        this.parametroPagina = parametroPagina;
    }
}
