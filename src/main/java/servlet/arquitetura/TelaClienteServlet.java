package servlet.arquitetura;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.DateUtilities;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.contrato.servico.impl.ContratoAssinaturaDigitalServiceImpl;
import br.com.pactosolucoes.contrato.servico.intf.ContratoAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;
import br.com.pactosolucoes.enumeradores.TipoContatoCRM;
import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoContratoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import br.com.pactosolucoes.integracao.conciliadora.LogConciliadoraVO;
import br.com.pactosolucoes.integracao.telaCliente.*;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import controle.basico.EnvioEmailContratoReciboControle;
import controle.basico.LtvControle;
import controle.basico.TelaClienteControle;
import negocio.armario.AluguelArmarioVO;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.DadosAcessoOfflineVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TimeZoneEnum;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.AfastamentoContratoDependenteVO;
import negocio.comuns.contrato.AtestadoContratoVO;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.MalaDiretaEnviadaVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.comuns.crm.TipoPerguntaEnum;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoBloqueioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.plano.HistoricoTurmasContratoTO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.acesso.Coletor;
import negocio.facade.jdbc.acesso.LocalAcesso;
import negocio.facade.jdbc.armario.Armario;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Permissao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioPerfilAcesso;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Brinde;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.facade.jdbc.basico.Convite;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Familiar;
import negocio.facade.jdbc.basico.HistoricoPontos;
import negocio.facade.jdbc.basico.Parentesco;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.Reposicao;
import negocio.facade.jdbc.basico.TokenBoleto;
import negocio.facade.jdbc.contrato.*;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.contrato.AfastamentoContratoDependente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoAssinaturaDigital;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.contrato.HistoricoContrato;
import negocio.facade.jdbc.contrato.JustificativaOperacao;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.crm.AberturaMeta;
import negocio.facade.jdbc.crm.Agenda;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.crm.MalaDireta;
import negocio.facade.jdbc.crm.Objecao;
import negocio.facade.jdbc.crm.optin.Optin;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.PlanoTextoPadrao;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.plano.Turma;
import negocio.facade.jdbc.telaCliente.TelaClienteBoleto;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.JenkinsService;
import servicos.boleto.BoletoService;
import servicos.boleto.TokenBoletoVO;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.cliente.VendaRapidaRecorrenteServiceImpl;
import servicos.impl.gestaoaula.GestaoAulaService;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.foguete.enums.ClienteFogueteStatusEnum;
import servicos.integracao.impl.conciliadora.ConciliadoraServiceImpl;
import servicos.integracao.mgb.impl.MgbServiceImpl;
import servicos.integracao.mgb.intf.MgbService;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import servicos.vendasonline.VendasOnlineService;
import br.com.pactosolucoes.atualizadb.processo.ProcessoSincronizarAutorizacoesAcessoClientePlanoVIP;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.time.Period;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.TimeZone;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

import static controle.arquitetura.SuperControle.*;
import static negocio.comuns.utilitarias.Uteis.getUrl;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import static org.apache.commons.lang3.StringUtils.isBlank;

/**
 * #########################################################################################
 * ######################################## ATENÇÃO ########################################
 * <p>
 * ESSA CLASSE FOI CRIADA COM O OBJETIVO DE CRIAR UM SERVLET GENERICO PARA USO DE ALGUMAS OPERACOES EXPECÍFICAS DA TELA DO CLIENTE, ENTAO ESSAS
 * OPERAÇÕES SÃO MIGRADAS PARA MICROSERVIÇO.
 * <p>
 * SÓ É ACEITO REQUISAÇÃO POST !
 * <p>
 * Deve-se criar a OPERAÇÃO que será enviada na requisição (parametro "op") e criar um método para essa operação onde será realizado a operação.
 * <p>
 * #########################################################################################
 * #########################################################################################
 */
public class TelaClienteServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");
            request.setAttribute("servlet-relatorio", "true");
            if (request.getParameter("key") != null &&
                    !UteisValidacao.emptyString(request.getParameter("key"))) {
                request.setAttribute("servlet-chave", request.getParameter("key"));
            }

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Operação não informada");
            }

            con = obterConexao(request, null);

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("emitirNFSe")) {
                envelopeRespostaDTO = emitirNFSe(request, con);
            } else if (operacao.equalsIgnoreCase("emitirNFCe")) {
                envelopeRespostaDTO = emitirNFCe(request, con);
            } else if (operacao.equalsIgnoreCase("imprimirRecibo")) {
                envelopeRespostaDTO = imprimirRecibo(request, con);
            } else if (operacao.equalsIgnoreCase("enviarReciboEmail")) {
                envelopeRespostaDTO = enviarReciboEmail(request, con);
            } else if (operacao.equalsIgnoreCase("imprimirContrato")) {
                envelopeRespostaDTO = imprimirContrato(request, con);
            } else if (operacao.equalsIgnoreCase("enviarContratoEmail")) {
                envelopeRespostaDTO = enviarContratoEmail(request, con);
            } else if (operacao.equalsIgnoreCase("enviarContratoProdutoEmail")) {
                envelopeRespostaDTO = enviarContratoProdutoEmail(request, con);
            } else if (operacao.equalsIgnoreCase("alterarVencimentoParcelasObter")) {
                envelopeRespostaDTO = obterParcelasAlterarVencimento(request, con);
            } else if (operacao.equalsIgnoreCase("alterarVencimentoParcelas")) {
                envelopeRespostaDTO = alterarVencimentoParcelas(request, con);
            } else if (operacao.equalsIgnoreCase("alterarVigenciaFinalMovProduto")) {
                envelopeRespostaDTO = alterarVigenciaFinalMovProduto(request, con);
            } else if (operacao.equalsIgnoreCase("renovarMovProdutoObter")) {
                envelopeRespostaDTO = obterRenovarMovProduto(request, con);
            } else if (operacao.equalsIgnoreCase("renovarMovProduto")) {
                envelopeRespostaDTO = renovarMovProduto(request, con);
            } else if (operacao.equalsIgnoreCase("getListaContratoRematricularERenovar")) {
                envelopeRespostaDTO = getListaContratoRematricularERenovar(request, con);
            } else if (operacao.equalsIgnoreCase("gympass")) {
                envelopeRespostaDTO = gymPass(request, con);
            } else if (operacao.equalsIgnoreCase("gogood")) {
                envelopeRespostaDTO = goGood(request, con);
            } else if (operacao.equalsIgnoreCase("totalpass")) {
                envelopeRespostaDTO = totalPass(request, con);
            } else if (operacao.equalsIgnoreCase("linkCartao")) {
                envelopeRespostaDTO = linkCartao(request, con);
            } else if (operacao.equalsIgnoreCase("definirSenhaDeAcesso")) {
                envelopeRespostaDTO = definirSenhaDeAcesso(request, con);
            } else if (operacao.equalsIgnoreCase("bloqueioAcessoCatraca")) {
                envelopeRespostaDTO = bloqueioAcessoCatraca(request, con);
            } else if (operacao.equalsIgnoreCase("registrarAcessoManual")) {
                envelopeRespostaDTO = registrarAcessoManual(request, con);
            } else if (operacao.equalsIgnoreCase("obterDocumentosContrato")) {
                envelopeRespostaDTO = obterDocumentosContrato(request, con);
            } else if (operacao.equalsIgnoreCase("imprimirContratoOperacao")) {
                envelopeRespostaDTO = imprimirContratoOperacao(request, con);
            } else if (operacao.equalsIgnoreCase("estornarContratoOperacao")) {
                envelopeRespostaDTO = estornarContratoOperacao(request, con);
            } else if (operacao.equalsIgnoreCase("desmarcarHorario")) {
                envelopeRespostaDTO = desmarcarHorario(request, con);
            } else if (operacao.equalsIgnoreCase("salvarImagens")) {
                envelopeRespostaDTO = salvarImagens(request, con);
            } else if (operacao.equalsIgnoreCase("enviarEmailCancelamentoContrato")) {
                envelopeRespostaDTO = enviarEmailCancelamentoContrato(request, con);
            } else if (operacao.equalsIgnoreCase("desfazerCancelamento")) {
                envelopeRespostaDTO = desfazerCancelamento(request, con);
            } else if (operacao.equalsIgnoreCase("cancelarOperacao")) {
                envelopeRespostaDTO = cancelarOperacao(request, con);
            } else if (operacao.equalsIgnoreCase("aulasContrato")) {
                envelopeRespostaDTO = aulasContrato(request, con);
            } else if (operacao.equalsIgnoreCase("alterarDadosContrato")) {
                envelopeRespostaDTO = alterarDadosContrato(request, con);
            } else if (operacao.equalsIgnoreCase("operacaoAula")) {
                envelopeRespostaDTO = operacaoAula(request, con);
            } else if (operacao.equalsIgnoreCase("opcoesContratoCredito")) {
                envelopeRespostaDTO = opcoesContratoCredito(request, con);
            } else if (operacao.equalsIgnoreCase("opcoesContrato")) {
                envelopeRespostaDTO = opcoesContrato(request, con);
            } else if (operacao.equalsIgnoreCase("configuracoesSistema")) {
                envelopeRespostaDTO = obterTabela("configuracaosistema", con, request);
            } else if (operacao.equalsIgnoreCase("configuracoesEmpresa")) {
                envelopeRespostaDTO = obterConfiguracaoEmpresa(con, request);
            } else if (operacao.equalsIgnoreCase("transferirDireitoContrato")) {
                envelopeRespostaDTO = transferirDireitoContrato(con, request);
            } else if (operacao.equalsIgnoreCase("operacoesContrato")) {
                envelopeRespostaDTO = operacoesContrato(con, request);
            } else if (operacao.equalsIgnoreCase("consultarSaldoCredito")) {
                envelopeRespostaDTO = consultarSaldoCredito(con, request);
            } else if (operacao.equalsIgnoreCase("consultarAvisos")) {
                envelopeRespostaDTO = consultarAvisos(con, request);
            } else if (operacao.equalsIgnoreCase("dadosPagamentoCliente")) {
                envelopeRespostaDTO = dadosPagamentoCliente(con, request);
            } else if (operacao.equalsIgnoreCase("liberarVagaTurma")) {
                envelopeRespostaDTO = liberarVagaTurma(con, request);
            } else if (operacao.equalsIgnoreCase("faltasTurmasRemovidas")) {
                envelopeRespostaDTO = faltasTurmasRemovidas(con, request);
            } else if (operacao.equalsIgnoreCase("buscarHorarioTurmaByCodigo")) {
                envelopeRespostaDTO = buscarHorarioTurmaByCodigo(con, request);
            } else if (operacao.equalsIgnoreCase("alterarMatricula")) {
                envelopeRespostaDTO = alterarMatricula(con, request);
            } else if (operacao.equalsIgnoreCase("incluirControleCreditoTreino")) {
                envelopeRespostaDTO = incluirControleCreditoTreino(con, request);
            } else if (operacao.equalsIgnoreCase("consultarAlunosTransferenciaDeCredito")) {
                envelopeRespostaDTO = consultarAlunosTransferenciaDeCredito(con, request);
            } else if (operacao.equalsIgnoreCase("consultarAlunoTransferenciaDeCreditoPorCpfOuEmail")) {
                envelopeRespostaDTO = consultarAlunoTransferenciaDeCreditoPorCpfOuEmail(con, request);
            } else if (operacao.equalsIgnoreCase("consultarQuantosCreditosOAlunoPodeTransferir")) {
                envelopeRespostaDTO = consultarQuantosCreditosOAlunoPodeTransferir(con, request);
            }  else if (operacao.equalsIgnoreCase("obterStatusConciliadora")) {
                envelopeRespostaDTO = obterStatusConciliadora(con, request);
            } else if (operacao.equalsIgnoreCase("obterCheques")) {
                envelopeRespostaDTO = obterCheques(con, request);
            } else if (operacao.equalsIgnoreCase("pontos")) {
                envelopeRespostaDTO = pontos(con, request);
            } else if (operacao.equalsIgnoreCase("brinde")) {
                envelopeRespostaDTO = brinde(con, request);
            } else if (operacao.equalsIgnoreCase("convidado")) {
                envelopeRespostaDTO = convidado(con, request);
            } else if (operacao.equalsIgnoreCase("imprimirContratoPrestacaoServico")) {
                envelopeRespostaDTO = imprimirContratoPrestacaoServico(con, request);
            } else if (operacao.equalsIgnoreCase("imprimirReciboDevolucaoMovProduto")) {
                envelopeRespostaDTO = imprimirReciboDevolucaoMovProduto(request, con);
            } else if (operacao.equalsIgnoreCase("alterarFotoAluno")) {
                envelopeRespostaDTO = alterarFotoAluno(request, con);
            } else if (operacao.equalsIgnoreCase("excluirFotoAluno")) {
                envelopeRespostaDTO = excluirFotoAluno(request, con);
            } else if (operacao.equalsIgnoreCase("configuracaoRecibo")) {
                envelopeRespostaDTO = configuracaoRecibo(request, con);
            } else if (operacao.equalsIgnoreCase("atualizarSintetico")) {
                envelopeRespostaDTO = atualizarSintetico(request, con);
            } else if (operacao.equalsIgnoreCase("atualizarAvisosPendencias")) {
                envelopeRespostaDTO = atualizarAvisosPendencias(request, con);
            } else if (operacao.equalsIgnoreCase("validarParcelaAbertaComBoletoPendente")) {
                envelopeRespostaDTO = validarParcelaAbertaComBoletoPendente(request, con);
            } else if (operacao.equalsIgnoreCase("excluirAnexoZW")) {
                envelopeRespostaDTO = excluirAnexoZW(request, con);
            } else if (operacao.equalsIgnoreCase("excluirClienteMensagemProdutoVencido")) {
                envelopeRespostaDTO = excluirClienteMensagemProdutoVencido(request, con);
            } else if (operacao.equalsIgnoreCase("desbloquearMsgProdutoVencido")) {
                envelopeRespostaDTO = desbloquearMsgProdutoVencido(request, con);
            } else if (operacao.equalsIgnoreCase("alterarObjecaoCliente")) {
                envelopeRespostaDTO = alterarObjecaoCliente(con, request);
            } else if (operacao.equalsIgnoreCase("boleto")) {
                envelopeRespostaDTO = boleto(con, request);
            } else if (operacao.equalsIgnoreCase("boletoToken")) {
                envelopeRespostaDTO = boletoToken(con, request);
            } else if (operacao.equalsIgnoreCase("notafiscal")) {
                envelopeRespostaDTO = notaFiscal(con, request);
            } else if (operacao.equalsIgnoreCase("pactopay")) {
                envelopeRespostaDTO = pactoPay(con, request);
            } else if (operacao.equalsIgnoreCase("importacao")) {
                envelopeRespostaDTO = importacao(con, request);
            } else if (operacao.equalsIgnoreCase("ltv")) {
                envelopeRespostaDTO = ltv(con, request);
            } else if (operacao.equalsIgnoreCase("contratoDependente")) {
                envelopeRespostaDTO = contratoDependente(con, request);
            } else if (operacao.equalsIgnoreCase("familiares")) {
                envelopeRespostaDTO = familiares(con, request);
            } else if (operacao.equalsIgnoreCase("armario")) {
                envelopeRespostaDTO = armario(con, request);
            } else if (operacao.equalsIgnoreCase("movproduto")) {
                envelopeRespostaDTO = movproduto(con, request);
            } else if (operacao.equalsIgnoreCase("contrato")) {
                envelopeRespostaDTO = contrato(con, request);
            } else if (operacao.equalsIgnoreCase("crm")) {
                envelopeRespostaDTO = crm(con, request);
            } else if (operacao.equalsIgnoreCase("aplicativo")) {
                envelopeRespostaDTO = aplicativo(con, request);
            } else if (operacao.equalsIgnoreCase("sesi")) {
                envelopeRespostaDTO = sesi(con, request);
            } else if (operacao.equalsIgnoreCase("enviarEmailOptin")) {
                envelopeRespostaDTO = enviarEmailOptin(request, con);
            } else if (operacao.equalsIgnoreCase("enviarEmailReciboCancelamentoCobrancaGetcard")) {
                envelopeRespostaDTO = enviarEmailReciboCancelamentoCobrancaGetcard(request, con);
            } else if (operacao.equalsIgnoreCase("boleto_redirect")) {
                String urlRedirect = boletoRedirect(con, request);
                response.sendRedirect(urlRedirect);
                return;
            } else if (operacao.equalsIgnoreCase("sincronizarAlunoPlanoVIP")) {
                envelopeRespostaDTO = sincronizarAlunoVIPRedeEmpresa(request, con);
            } else if (operacao.equalsIgnoreCase("acesso_convidado")) {
                envelopeRespostaDTO = acessoConvidado(request, con);
            } else if (operacao.equalsIgnoreCase("notificarIntegracaoFoguete")) {
                envelopeRespostaDTO = notificarIntegracaoFoguete(con, request);
            } else if (operacao.equalsIgnoreCase("validarLinkPagamento")){
                envelopeRespostaDTO = validarLinkPagamento(request, con);
            } else {
                throw new Exception("Nenhuma operação executada");
            }

            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO sincronizarAlunoVIPRedeEmpresa(HttpServletRequest request, Connection con) throws Exception {
        String key = request.getParameter("key");
        Integer codigoCliente = Integer.parseInt(request.getParameter("codigoCliente"));

        ProcessoSincronizarAutorizacoesAcessoClientePlanoVIP processo = new ProcessoSincronizarAutorizacoesAcessoClientePlanoVIP(con, key);
        processo.sincronizarAlunoVIPRedeEmpresa(codigoCliente);
        processo = null;

        return EnvelopeRespostaDTO.of("Sincronizado com sucesso!");
    }

    private EnvelopeRespostaDTO getListaContratoRematricularERenovar(HttpServletRequest request, Connection con) throws Exception {
        Contrato contratoDAO = null;
        Cliente clienteDAO = null;
        try {
            contratoDAO = new Contrato(con);
            clienteDAO = new Cliente(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codCliente = jsonObject.getInt("codCliente");
            Integer codEmpresaLogada = jsonObject.getInt("codEmpresaLogada");

            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            List<ContratoDTO> contratos = new ArrayList<>();
            if (clienteVO.getEmpresa().getPermiteContratosConcomintante()) {
                List<ContratoVO> contratosRenovarRematricular = contratoDAO.consultarPorCodigoPessoaOrdenadoPelaDataVencimentoContrato(
                        clienteVO.getPessoa().getCodigo(), false,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS, null);
                verificarQualBotaoReferenteASituacaoContratoSeraApresentado(con, clienteVO, contratosRenovarRematricular);
                contratosRenovarRematricular.forEach(
                        c -> {
                            if (c.getContratoInativo()
                                    && c.getDataRenovarRealizada() != null) {
                                c.setSituacao("RN");
                                c.getContratoInativo();
                            } else if (c.getContratoInativo()) {
                                c.setApresentarBotaoRenovarContrato(false);
                                c.setSituacao("IN");
                                c.getContratoInativo();
                            }
                            if (c.getRematricularContrato()
                                    || c.getRenovarContrato()) {
                                // apresentar botão renovar c apenas para usuário logado
                                // na empresa do respectivo c
                                if (!codEmpresaLogada.equals(c.getEmpresa().getCodigo())) {
                                    c.setRenovarContrato(false);
                                    c.setRematricularContrato(false);
                                }
                                ContratoDTO contrato = new ContratoDTO();
                                contrato.setCodigo(c.getCodigo());
                                contrato.setSituacao(c.getSituacao());
                                contrato.setPlanoDescricao(c.getPlano().getDescricao());
                                contrato.setDataInicio(c.getVigenciaDe().getTime());
                                contrato.setDataTermino(c.getVigenciaAte().getTime());
                                contrato.setRenovarContrato(c.getRenovarContrato());
                                contrato.setRematricularContrato(c.getRematricularContrato());
                                contratos.add(contrato);
                            }
                        }
                );
            }
            return EnvelopeRespostaDTO.of(contratos);
        } finally {
            contratoDAO = null;
            clienteDAO = null;
        }
    }

    private void verificarQualBotaoReferenteASituacaoContratoSeraApresentado(Connection con, ClienteVO clienteVO, List<ContratoVO> listaContrato) throws Exception {
        ConfiguracaoSistema configuracaoSistema = null;
        ContratoOperacao contratoOperacao = null;
        ZillyonWebFacade zillyonWebFacade = null;
        try {
            contratoOperacao = new ContratoOperacao(con);
            configuracaoSistema = new ConfiguracaoSistema(con);
            zillyonWebFacade = new ZillyonWebFacade(con);
            Integer nrDiasVencido = 0;
            if (clienteVO.getEmpresa().getCarenciaRenovacao() != 0) {
                nrDiasVencido = clienteVO.getEmpresa().getCarenciaRenovacao();
            } else {
                ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
                if (configuracaoSistemaVO == null) {
                    throw new Exception("Não foi possível inicializar as configurações do sistema");
                }
                nrDiasVencido = configuracaoSistemaVO.getCarenciaRenovacao();
            }
            if (clienteVO.getSituacao().equals("AT")) {
                for (ContratoVO contrato : listaContrato) {
                    Date dataAtual = Calendario.hoje();
                    Date dataVigenciaAjustada = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), 1);
                    Date dataVigenciaRenovacao = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), (nrDiasVencido + 1));
                    if (contrato.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores()) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    } else if ((contrato.getContratoResponsavelRenovacaoMatricula() != 0) || (contrato.getContratoResponsavelRematriculaMatricula() != 0)) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    } else if (contratoOperacao.existeOperacaoParaEsteContrato(contrato.getCodigo(), "CA")) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(true);
                    } else if (validarSeExisteTrancamentoSemRetornoBoolean(con, contrato)) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    } else if (dataAtual.before(dataVigenciaAjustada)) {
                        contrato.setRenovarContrato(true);
                        contrato.setRematricularContrato(false);
                    } else if (dataAtual.before(dataVigenciaRenovacao)) {
                        contrato.setRenovarContrato(true);
                        contrato.setRematricularContrato(false);
                    } else if (contrato.getSituacao().equals("IN")) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(true);
                    } else {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    }
                }
            } else if (clienteVO.getSituacao().equals("IN")) {
                for (Object aListaContrato : listaContrato) {
                    ContratoVO contrato = (ContratoVO) aListaContrato;
                    String sit = zillyonWebFacade.obterSituacaoClienteInativo(contrato, clienteVO);
                    // Date dataAtual = negocio.comuns.utilitarias.Calendario.hoje();
                    // Date dataVigenciaRenovacao = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), (nrDiasVencido + 1));
                    if (contrato.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores()) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    } else if ((contrato.getContratoResponsavelRenovacaoMatricula() != 0) || (contrato.getContratoResponsavelRematriculaMatricula() != 0)) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    } else if (contrato.getSituacao().equals("CA")) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(true);
                    } else if (sit.equals("VE")
                            || (contrato.getSituacao().equals("AT") && Calendario.maiorOuIgual(contrato.getVigenciaAteAjustada(), Calendario.hoje()))) { // contratos rematriculados futuramente
                        contrato.setRenovarContrato(true);
                        contrato.setRematricularContrato(false);
                    } else if (sit.equals("DE")) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(true);
                    } else {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    }
                }
            } else {
                for (Object aListaContrato : listaContrato) {
                    ContratoVO contrato = (ContratoVO) aListaContrato;
                    if (contrato.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores() || (contrato.getContratoResponsavelRenovacaoMatricula() != 0) || (contrato.getContratoResponsavelRematriculaMatricula() != 0)) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    } else if (contrato.getSituacao().equals("AT") && Calendario.maiorOuIgual(contrato.getVigenciaAteAjustada(), Calendario.hoje())) {
                        contrato.setRenovarContrato(true);
                        contrato.setRematricularContrato(false);
                    }
                }
            }
        } finally {
            contratoOperacao = null;
            configuracaoSistema = null;
            zillyonWebFacade = null;
        }
    }

    private boolean validarSeExisteTrancamentoSemRetornoBoolean(Connection con, ContratoVO contrato) throws Exception {
        try {
            validarSeExisteTrancamentoSemRetorno(con, contrato);
        } catch (ConsistirException ce) {
            return true;
        } catch (Exception e) {
            throw e;
        }
        return false;
    }

    private void validarSeExisteTrancamentoSemRetorno(Connection con, ContratoVO contrato) throws Exception {
        HistoricoContrato historicoContrato = null;
        try {
            historicoContrato = new HistoricoContrato(con);
            //verifica se o contrato está trancado
            HistoricoContratoVO operacao =
                    historicoContrato.
                            obterUltimoHistoricoContratoPorContratoTipoHistorico(
                                    contrato.getCodigo().intValue(),
                                    "TR",
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            //verifica se possui retorno de trancamento
            HistoricoContratoVO retornoPrevisto =
                    historicoContrato.
                            obterUltimoHistoricoContratoPorContratoTipoHistorico(
                                    contrato.getCodigo().intValue(),
                                    "RT",
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //se possui trancamento
            if (operacao != null) {
                //se o retorno existente não é referente ao trancamento ou não existe retorno de trancamento
                if ((retornoPrevisto != null && Calendario.menor(retornoPrevisto.getDataFinalSituacao(), operacao.getDataInicioSituacao())
                ) || (retornoPrevisto == null)) {
                    throw new ConsistirException("Não pode lançar outra operação de contrato antes do aluno retornar do trancamento");
                }
            }
        } finally {
            historicoContrato = null;
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private Connection obterConexao(ServletRequest request, String chave) throws Exception {
        if (!UteisValidacao.emptyString(chave)) {
            return new DAO().obterConexaoEspecifica(chave.trim());
        }
        return new DAO().obterConexaoEspecifica(obterChave(request));
    }

    private String obterChave(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return key.trim();
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private EnvelopeRespostaDTO emitirNFSe(ServletRequest request, Connection con) throws Exception {
        NotaFiscal notaFiscalDAO;
        try {
            notaFiscalDAO = new NotaFiscal(con);
            String chave = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer recibo = json.getInt("recibo");
            Integer usuario = json.getInt("usuario");

            String retorno = notaFiscalDAO.emitirNFSeRecibo(recibo, usuario, chave);
            return EnvelopeRespostaDTO.of(retorno);
        } finally {
            notaFiscalDAO = null;
        }
    }

    private EnvelopeRespostaDTO emitirNFCe(ServletRequest request, Connection con) throws Exception {
        NotaFiscal notaFiscalDAO;
        try {
            notaFiscalDAO = new NotaFiscal(con);
            String chave = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer recibo = json.getInt("recibo");
            Integer usuario = json.getInt("usuario");

            String retorno = notaFiscalDAO.emitirNFCeRecibo(recibo, usuario, chave, "CLIENTE");
            return EnvelopeRespostaDTO.of(retorno);
        } finally {
            notaFiscalDAO = null;
        }
    }

    private EnvelopeRespostaDTO imprimirRecibo(HttpServletRequest request, Connection con) throws Exception {
        String chave = obterChave(request);
        JSONObject json = new JSONObject(obterBody(request));
        Integer recibo = json.getInt("recibo");
        Integer usuario = json.optInt("usuario");
        String url = imprimirReciboGeral(recibo, usuario, chave, request, con);
        return EnvelopeRespostaDTO.of(url);
    }

    private EnvelopeRespostaDTO enviarReciboEmail(HttpServletRequest request, Connection con) throws Exception {
        ReciboPagamento reciboPagamentoDAO;
        Usuario usuarioDAO;
        Pessoa pessoaDAO;
        try {
            reciboPagamentoDAO = new ReciboPagamento(con);
            usuarioDAO = new Usuario(con);
            pessoaDAO = new Pessoa(con);

            String chave = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer recibo = json.getInt("recibo");
            Integer usuario = json.getInt("usuario");
            JSONArray jsonEmails = json.getJSONArray("emails");
            List<String> emailsEnviarRecibo = new ArrayList<>();
            for (int i = 0; i < jsonEmails.length(); i++) {
                String em = jsonEmails.getString(i);
                if (UteisValidacao.validaEmail(em)) {
                    emailsEnviarRecibo.add(em);
                }
            }

            if (UteisValidacao.emptyList(emailsEnviarRecibo)) {
                throw new Exception("Nenhum email informado");
            }

            String[] emails = new String[emailsEnviarRecibo.size()];
            int i = 0;
            for (String emailVO : emailsEnviarRecibo) {
                emails[i] = emailVO;
                i++;
            }

            String url = imprimirReciboGeral(recibo, usuario, chave, request, con);

            ReciboPagamentoVO reciboVO = reciboPagamentoDAO.consultarPorChavePrimaria(recibo, Uteis.NIVELMONTARDADOS_TODOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(reciboVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            File arquivo = new File(new File(request.getSession().getServletContext().getRealPath("/")).getAbsolutePath() + "/servlet-relatorio/" + request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString());
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo("RECIBO", configuracaoSistemaCRMVO);
            uteisEmail.setRemetente(usuarioVO);
            uteisEmail = uteisEmail.addAnexo(request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString(), arquivo);

            String textoEmail = ("Pagador: " + pessoaVO.getNome() + "<br/>" + "Data do recibo: " + Uteis.getData(reciboVO.getData()));

            uteisEmail.enviarEmailN(emails, textoEmail, "RECIBO", reciboVO.getEmpresa_Apresentar());

            return EnvelopeRespostaDTO.of("E-mail enviado");
        } finally {
            reciboPagamentoDAO = null;
            usuarioDAO = null;
            pessoaDAO = null;
        }
    }

    private String imprimirReciboGeral(Integer recibo, Integer usuario, String chave, HttpServletRequest request, Connection con) throws Exception {
        ReciboPagamento reciboPagamentoDAO;
        Usuario usuarioDAO;
        try {
            reciboPagamentoDAO = new ReciboPagamento(con);
            usuarioDAO = new Usuario(con);

            ReciboPagamentoVO reciboVO = reciboPagamentoDAO.consultarPorChavePrimaria(recibo, Uteis.NIVELMONTARDADOS_TODOS);
            UsuarioVO usuarioVO;
            if (UteisValidacao.emptyNumber(usuario)) {
                usuarioVO = usuarioDAO.getUsuarioRecorrencia();
            } else {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }

            Conexao.guardarConexaoForJ2SE(chave, con);

            request.getSession().setAttribute("key", chave);

            String urlArquivo = reciboPagamentoDAO.imprimirReciboPDF(Boolean.FALSE,
                    reciboVO.getEmpresa().isReciboParaImpressoraTermica(), reciboVO.getEmpresa(),
                    reciboVO.getEmpresa(), usuarioVO, reciboVO, null, "PDF", request);
            String[] urlSplit = urlArquivo.split("/");
            String nomeArquivo = (urlSplit[urlSplit.length - 1]);
            return (getUrlAplicacaoPastaRelatorio(chave, request) + nomeArquivo);
        } finally {
            reciboPagamentoDAO = null;
            usuarioDAO = null;
        }
    }

    private EnvelopeRespostaDTO imprimirContrato(ServletRequest request, Connection con) throws Exception {
        ContratoTextoPadrao contratoTextoPadraoDAO;
        try {
            contratoTextoPadraoDAO = new ContratoTextoPadrao(con);
            String chave = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer contrato = json.getInt("contrato");
            Conexao.guardarConexaoForJ2SE(chave, con);
            return EnvelopeRespostaDTO.of(contratoTextoPadraoDAO.consultarHtmlContratoTelaNova(contrato));
        } finally {
            contratoTextoPadraoDAO = null;
        }
    }

    Integer buscarParametroAditivo(JSONObject json) {
        try {
            return obterParametroTratado(json.getInt("aditivo"));
        } catch (Exception ex) {
            return null;
        }
    }

    private EnvelopeRespostaDTO enviarContratoEmail(HttpServletRequest request, Connection con) throws Exception {
        final String chave = obterChave(request);
        final JSONObject json = new JSONObject(obterBody(request));
        final Integer contrato = json.getInt("contrato");
        final Integer usuario = json.getInt("usuario");
        final Integer aditivo = buscarParametroAditivo(json);

        final JSONArray jsonEmails = json.getJSONArray("emails");
        final List<EmailVO> emailsContrato = new ArrayList<>();
        for (int i = 0; i < jsonEmails.length(); i++) {
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(jsonEmails.getString(i));
            emailsContrato.add(emailVO);
        }

        return EnvelopeRespostaDTO.of(enviarContratoPorEmail(chave, usuario, contrato, aditivo, emailsContrato, false, request, con));
    }

    private EnvelopeRespostaDTO enviarContratoProdutoEmail(HttpServletRequest request, Connection con) throws Exception {
        String chave = obterChave(request);
        JSONObject json = new JSONObject(obterBody(request));
        Integer vendaAvulsa = json.has("vendaAvulsa") ? json.getInt("vendaAvulsa") : null;
        Integer aulaAvulsaDiaria = json.has("aulaAvulsaDiaria") ? json.getInt("aulaAvulsaDiaria") : null;
        Integer produto = json.getInt("produto");
        Integer usuario = json.getInt("usuario");
        JSONArray jsonEmails = json.getJSONArray("emails");
        List<EmailVO> emailsContrato = new ArrayList<>();
        for (int i = 0; i < jsonEmails.length(); i++) {
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(jsonEmails.getString(i));
            emailsContrato.add(emailVO);
        }
        String retorno = enviarContratoProdutoPorEmail(chave, usuario, vendaAvulsa, aulaAvulsaDiaria, produto, emailsContrato, request, con);
        return EnvelopeRespostaDTO.of(retorno);
    }

    private String enviarContratoPorEmail(String chave, Integer codUsuario, Integer codContrato, Integer aditivo,
                                          List<EmailVO> emailsEnviar, boolean assinar,
                                          HttpServletRequest request, Connection con) throws Exception {
        Contrato contratoDAO;
        ConfiguracaoSistema configuracaoSistemaDAO;
        Usuario usuarioDAO;
        try {
            contratoDAO = new Contrato(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            usuarioDAO = new Usuario(con);

            boolean existeEmailValido = false;
            for (EmailVO email : emailsEnviar) {
                if (UteisValidacao.validaEmail(email.getEmail())) {
                    existeEmailValido = true;
                    break;
                }
            }
            if (!existeEmailValido) {
                throw new Exception("Não foi possível enviar o contrato pois nenhum e-mail informado é válido.");
            }

            Conexao.guardarConexaoForJ2SE(chave, con);

            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
            EnvioEmailContratoReciboControle envioEmailContratoReciboControle = new EnvioEmailContratoReciboControle();
            envioEmailContratoReciboControle.setContratoVO(contratoVO);
            envioEmailContratoReciboControle.prepararContratoEPagamento(contratoVO);
            envioEmailContratoReciboControle.setEnviarContratoParaAssinatura(configuracaoSistemaVO.isAssinaturaContratoViaEmail());
            envioEmailContratoReciboControle.setListaEmailsEnviar(emailsEnviar);
            envioEmailContratoReciboControle.setEnvioDeContrato(true);
            envioEmailContratoReciboControle.setReciboColaborador(false);
            envioEmailContratoReciboControle.setReciboPagamento(false);

            return envioEmailContratoReciboControle.enviarContrato(assinar, true, request, usuarioVO, aditivo);
        } finally {
            contratoDAO = null;
            configuracaoSistemaDAO = null;
            usuarioDAO = null;
        }
    }

    private String enviarContratoProdutoPorEmail(String chave, Integer codUsuario, Integer codVendaAvulsa, Integer codAulaAvulsaDiaria, Integer produto,
                                          List<EmailVO> emailsEnviar,
                                          HttpServletRequest request, Connection con) throws Exception {
        VendaAvulsa vendaAvulsaDAO;
        AulaAvulsaDiaria aulaAvulsaDiariaDAO;
        ConfiguracaoSistema configuracaoSistemaDAO;
        Usuario usuarioDAO;
        try {
            vendaAvulsaDAO = new VendaAvulsa(con);
            aulaAvulsaDiariaDAO = new AulaAvulsaDiaria(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            usuarioDAO = new Usuario(con);

            boolean existeEmailValido = false;
            for (EmailVO email : emailsEnviar) {
                if (UteisValidacao.validaEmail(email.getEmail())) {
                    existeEmailValido = true;
                    break;
                }
            }
            if (!existeEmailValido) {
                throw new Exception("Não foi possível enviar o contrato pois nenhum e-mail informado é válido.");
            }

            Conexao.guardarConexaoForJ2SE(chave, con);

            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            EnvioEmailContratoReciboControle envioEmailContratoReciboControle = new EnvioEmailContratoReciboControle();
            if (!UteisValidacao.emptyNumber(codVendaAvulsa)) {
                VendaAvulsaVO vendaAvulsaVO = vendaAvulsaDAO.consultarPorChavePrimaria(codVendaAvulsa, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                envioEmailContratoReciboControle.setVendaAvulsaVO(vendaAvulsaVO);
                envioEmailContratoReciboControle.prepararContratoProdutoEPagamento(chave, produto, con, request);
            } else if (!UteisValidacao.emptyNumber(codAulaAvulsaDiaria)) {
                AulaAvulsaDiariaVO aulaAvulsaDiariaVO = aulaAvulsaDiariaDAO.consultarPorChavePrimaria(codAulaAvulsaDiaria, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                envioEmailContratoReciboControle.setAulaAvulsaDiariaVO(aulaAvulsaDiariaVO);
                envioEmailContratoReciboControle.prepararContratoProdutoEPagamentoDiaria(chave, produto, con, request);
            } else {
                throw new IllegalArgumentException("Código de venda avulsa ou aula avulsa diária deve ser fornecido");
            }
            envioEmailContratoReciboControle.setEnviarContratoParaAssinatura(configuracaoSistemaVO.isAssinaturaContratoViaEmail());
            envioEmailContratoReciboControle.setListaEmailsEnviar(emailsEnviar);
            envioEmailContratoReciboControle.setEnvioDeContratoProduto(true);
            envioEmailContratoReciboControle.setEnvioDeContrato(false);
            envioEmailContratoReciboControle.setReciboColaborador(false);
            envioEmailContratoReciboControle.setReciboPagamento(false);
            return envioEmailContratoReciboControle.enviarContratoProduto(true, request, usuarioVO);
        } finally {
            vendaAvulsaDAO = null;
            aulaAvulsaDiariaDAO = null;
            configuracaoSistemaDAO = null;
            usuarioDAO = null;
        }
    }

    private EnvelopeRespostaDTO obterParcelasAlterarVencimento(HttpServletRequest request, Connection con) throws Exception {
        JSONObject json = new JSONObject(obterBody(request));
        Integer codContrato = json.optInt("contrato");
        Integer codCliente = json.optInt("cliente");
        Integer codUsuario = json.getInt("usuario");

        List<ParcelaDTO> listaParcelas = new ArrayList<>();
        List<MovParcelaVO> listaParcelaVO = obterParcelasAlterarVencimentoGeral(codContrato, codCliente, codUsuario, con);
        for (MovParcelaVO movParcelaVO : listaParcelaVO) {
            listaParcelas.add(new ParcelaDTO(movParcelaVO));
        }

        PaginadorDTO paginadorDTO = new PaginadorDTO(request);
        if (paginadorDTO != null
                && paginadorDTO.getPage() != null
                && paginadorDTO.getSize() != null) {
            int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
            int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
            if (ultimoRegistro > listaParcelas.size()) {
                listaParcelas = listaParcelas.subList(primeiroPaginacao, listaParcelas.size());
            } else {
                listaParcelas = listaParcelas.subList(primeiroPaginacao, ultimoRegistro);
            }
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos((long) listaParcelaVO.size());
        }

        return EnvelopeRespostaDTO.of(listaParcelas, paginadorDTO);
    }

    private List<MovParcelaVO> obterParcelasAlterarVencimentoGeral(Integer codContrato, Integer codCliente,
                                                                   Integer codUsuario, Connection con) throws Exception {
        Contrato contratoDAO;
        Usuario usuarioDAO;
        ContratoRecorrencia contratoRecorrenciaDAO;
        MovParcela movParcelaDAO;
        RemessaItem remessaItemDAO;
        Cliente clienteDAO;
        try {
            contratoDAO = new Contrato(con);
            usuarioDAO = new Usuario(con);
            contratoRecorrenciaDAO = new ContratoRecorrencia(con);
            movParcelaDAO = new MovParcela(con);
            remessaItemDAO = new RemessaItem(con);
            clienteDAO = new Cliente(con);

            ClienteVO cliente = null;
            ContratoVO contrato = null;
            ContratoRecorrenciaVO contratoRecorrencia = null;
            EmpresaVO empresaVO = null;
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (!UteisValidacao.emptyNumber(codCliente)) {
                cliente = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                empresaVO = cliente.getEmpresa();
            }

            if (!UteisValidacao.emptyNumber(codContrato)) {
                contrato = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                Boolean ehRecorrencia = (contrato.getRegimeRecorrencia());
                if (ehRecorrencia != null && ehRecorrencia) {
                    contratoRecorrencia = contratoRecorrenciaDAO.consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    cliente = null;
                }
                empresaVO = contrato.getEmpresa();
            }

            List<MovParcelaVO> listaParcelas = new ArrayList<>();
            if ((contrato != null && contrato.getCodigo() != 0 && contratoRecorrencia != null && contratoRecorrencia.getCodigo() != 0)
                    || (cliente != null && cliente.getCodigo() != 0)) {

                validarPermissaoAlterarDataVencimento(empresaVO, usuarioVO, con);

                List<MovParcelaVO> listaTemp;
                if (contrato != null) {
                    listaTemp = movParcelaDAO.consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    listaTemp = movParcelaDAO.consultarPorCodigoPessoa(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                }
                for (MovParcelaVO movParcelaVO : listaTemp) {
                    if (!remessaItemDAO.existeParcelaEmRemessaGeradaouAguardando(movParcelaVO.getCodigo())) {
                        if (movParcelaVO.getSituacao().equals("EA")) {
                            if (cliente != null && !movParcelaVO.getRegimeRecorrencia()) {
                                listaParcelas.add(movParcelaVO);
                            }

                            if (cliente == null && movParcelaVO.getRegimeRecorrencia()) {
                                listaParcelas.add(movParcelaVO);
                            }
                        }
                    }
                }
                Ordenacao.ordenarLista(listaParcelas, "codigo");
            }

            if (listaParcelas.isEmpty()) {
                throw new Exception("Não há parcelas que não sejam de recorrência para serem alteradas!");
            }

            return listaParcelas;
        } finally {
            contratoDAO = null;
            usuarioDAO = null;
            contratoRecorrenciaDAO = null;
            movParcelaDAO = null;
            remessaItemDAO = null;
            clienteDAO = null;
        }
    }

    private EnvelopeRespostaDTO alterarVencimentoParcelas(HttpServletRequest request, Connection con) throws Exception {
        Contrato contratoDAO;
        Usuario usuarioDAO;
        ContratoRecorrencia contratoRecorrenciaDAO;
        MovParcela movParcelaDAO;
        Cliente clienteDAO;
        try {
            con.setAutoCommit(false);

            contratoDAO = new Contrato(con);
            usuarioDAO = new Usuario(con);
            contratoRecorrenciaDAO = new ContratoRecorrencia(con);
            movParcelaDAO = new MovParcela(con);
            clienteDAO = new Cliente(con);

            JSONObject json = new JSONObject(obterBody(request));
            Integer codContrato = json.optInt("contrato");
            Integer codCliente = json.optInt("cliente");
            Integer codUsuario = json.optInt("usuario");
            Integer diaVencimento = json.optInt("diaVencimento");

            JSONArray jsonParcelas = json.getJSONArray("parcelas");
            List<ParcelaDTO> parcelaDTOList = UteisJSON.jsonToListObject(ParcelaDTO.class, jsonParcelas);

            List<MovParcelaVO> listaParcelasAlteradas = new ArrayList<>();
            List<MovParcelaVO> listaParcelasOriginais = new ArrayList<>();
            for (ParcelaDTO parcelaDTO : parcelaDTOList) {
                MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorChavePrimaria(parcelaDTO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                listaParcelasOriginais.add((MovParcelaVO) movParcelaVO.getClone(true));
                movParcelaVO.setDataVencimento(Calendario.getDate("yyyy-MM-dd", parcelaDTO.getVencimento()));
                listaParcelasAlteradas.add(movParcelaVO);
            }

            ClienteVO cliente = null;
            ContratoVO contrato = null;
            ContratoRecorrenciaVO contratoRecorrencia = null;
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (!UteisValidacao.emptyNumber(codCliente)) {
                cliente = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (!UteisValidacao.emptyNumber(codContrato)) {
                contrato = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                Boolean ehRecorrencia = (contrato.getRegimeRecorrencia());
                if (ehRecorrencia != null && ehRecorrencia) {
                    contratoRecorrencia = contratoRecorrenciaDAO.consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    cliente = null;
                }
            }

            for (MovParcelaVO movParcelaVO : listaParcelasAlteradas) {
                if (!movParcelaVO.getSituacao().equalsIgnoreCase("EA")) {
                    throw new Exception("Para alterar o vencimento das parcelas todas precisam estar em aberto.");
                }
                if (Calendario.menor(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
                    throw new Exception("A data de vencimento da(s) parcela(s) não poder ser menor que " + Uteis.getData(Calendario.hoje()) + ".");
                }
                if (existeBoletoAguardandoPagamento(movParcelaVO)) {
                    throw new Exception("A(s) parcela(s) não podem ser alteradas por ter boletos aguardando pagamento. Cancele os boletos antes de editar o vencimento das parcelas.");
                }
            }

            if (cliente == null && contratoRecorrencia != null) {
                if (diaVencimento <= 0 || diaVencimento >= 31) {
                    throw new Exception("O dia para vencimento deve estar entre 1 e 30.");
                }
                contratoRecorrencia.setDiaVencimentoCartao(diaVencimento);
                contratoRecorrenciaDAO.alterar(contratoRecorrencia);
            }

            movParcelaDAO.alterarVencimentoListaParcelas(listaParcelasAlteradas, listaParcelasOriginais, false,
                    usuarioVO.getNome(), "TelaClienteServlet", true, true);

            con.commit();
            return EnvelopeRespostaDTO.of("Alteração realizada com sucesso!");
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
            contratoDAO = null;
            usuarioDAO = null;
            contratoRecorrenciaDAO = null;
            movParcelaDAO = null;
            clienteDAO = null;
        }
    }

    private boolean existeBoletoAguardandoPagamento(MovParcelaVO movParcelaVO) {
        boolean existeBoletoAguardandoPagamento = false;
        try {
            existeBoletoAguardandoPagamento = getFacade().getBoleto().existeBoletoPendentePorMovParcela(movParcelaVO.getCodigo(), true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return existeBoletoAguardandoPagamento;
    }

    public void validarPermissaoAlterarDataVencimento(EmpresaVO empresaVO, UsuarioVO usuarioVO, Connection con) throws Exception {
        Permissao permissaoDAO;
        ControleAcesso controleAcessoDAO;
        UsuarioPerfilAcesso usuarioPerfilAcessoDAO;
        try {
            permissaoDAO = new Permissao(con);
            controleAcessoDAO = new ControleAcesso(con);
            usuarioPerfilAcessoDAO = new UsuarioPerfilAcesso(con);

            usuarioVO.setUsuarioPerfilAcessoVOs(usuarioPerfilAcessoDAO.consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            Iterator i = usuarioVO.getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (empresaVO.getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(permissaoDAO.consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    controleAcessoDAO.verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            usuarioVO, "AlterarDataVencimentoParcela", "2.41 - Permissão para alterar a data de vencimento de parcelas");
                }
            }
        } finally {
            permissaoDAO = null;
            controleAcessoDAO = null;
            usuarioPerfilAcessoDAO = null;
        }
    }

    private EnvelopeRespostaDTO alterarVigenciaFinalMovProduto(HttpServletRequest request, Connection con) throws Exception {
        Usuario usuarioDAO;
        MovProduto movProdutoDAO;
        try {
            usuarioDAO = new Usuario(con);
            movProdutoDAO = new MovProduto(con);

            JSONObject json = new JSONObject(obterBody(request));
            Integer codMovProduto = json.optInt("movproduto");
            Integer codUsuario = json.optInt("usuario");
            String vigenciaFinal = json.optString("final");

            Date vigenciaFinalDate = Calendario.getDate("yyyy-MM-dd", vigenciaFinal);

            MovProdutoVO movProdutoVO = movProdutoDAO.consultarPorChavePrimaria(codMovProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            movProdutoVO.setDataFinalVigencia(vigenciaFinalDate);
            MovProdutoVO.validarDataFinalVigencia(movProdutoVO);
            movProdutoDAO.alterar(movProdutoVO);

            return EnvelopeRespostaDTO.of("Alteração realizada com sucesso!");
        } finally {
            usuarioDAO = null;
            movProdutoDAO = null;
        }
    }

    private EnvelopeRespostaDTO obterRenovarMovProduto(HttpServletRequest request, Connection con) throws Exception {
        Produto produtoDAO;
        MovProduto movProdutoDAO;
        try {
            produtoDAO = new Produto(con);
            movProdutoDAO = new MovProduto(con);

            JSONObject json = new JSONObject(obterBody(request));
            Integer codMovProduto = json.optInt("movproduto");
            Integer codUsuario = json.optInt("usuario");

            MovProdutoVO movProdutoVO = movProdutoDAO.consultarPorChavePrimaria(codMovProduto, Uteis.NIVELMONTARDADOS_VENDA);

            List<ProdutoVO> produtos = produtoDAO.consultarProdutosPorTipoProdutoTipoVigencia(movProdutoVO.getProduto().getTipoProduto(), "ID", false, Uteis.NIVELMONTARDADOS_MINIMOS);
            List<ProdutoDTO> produtoDTOS = new ArrayList<>();
            for (ProdutoVO produtoVO : produtos) {
                produtoDTOS.add(new ProdutoDTO(produtoVO));
            }
            return EnvelopeRespostaDTO.of(produtoDTOS);
        } finally {
            produtoDAO = null;
        }
    }

    private EnvelopeRespostaDTO renovarMovProduto(HttpServletRequest request, Connection con) throws Exception {
        Usuario usuarioDAO;
        MovProduto movProdutoDAO;
        Produto produtoDAO;
        Empresa empresaDAO;
        Cliente clienteDAO;
        ClienteMensagem clienteMensagemDAO;
        try {
            usuarioDAO = new Usuario(con);
            movProdutoDAO = new MovProduto(con);
            produtoDAO = new Produto(con);
            empresaDAO = new Empresa(con);
            clienteDAO = new Cliente(con);
            clienteMensagemDAO = new ClienteMensagem(con);

            JSONObject json = new JSONObject(obterBody(request));
            Integer codProduto = json.optInt("produto");
            Integer codMovProduto = json.optInt("movproduto");
            Integer codUsuario = json.optInt("usuario");
            Integer codEmpresa = json.optInt("empresa");

            MovProdutoVO movProdutoVO = movProdutoDAO.consultarPorChavePrimaria(codMovProduto, Uteis.NIVELMONTARDADOS_VENDA);
            ProdutoVO produtoVO = produtoDAO.consultarPorChavePrimaria(codProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(movProdutoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            // Ao renovar um produto que tem prazo de vigência, deve ser setado na data de compra do novo produto uma data posterior a data final de vigênvia do produto anterior.
            Date dataCompraNovoProduto = Uteis.obterDataFutura2(movProdutoVO.getDataFinalVigencia(), 1);
            gerarProduto(clienteVO, produtoVO, produtoVO.getValorFinal(), usuarioVO, dataCompraNovoProduto, empresaVO, con);

            clienteMensagemDAO.excluirClienteMensagemProdutoVencido(clienteVO.getCodigo(), produtoVO.getCodigo());

            return EnvelopeRespostaDTO.of("Produto renovado com sucesso!");
        } finally {
            usuarioDAO = null;
            movProdutoDAO = null;
            produtoDAO = null;
            empresaDAO = null;
            clienteDAO = null;
            clienteMensagemDAO = null;
        }
    }

    public VendaAvulsaVO gerarProduto(ClienteVO clienteVO, ProdutoVO produto, Double valor, UsuarioVO responsavel,
                                      Date data, EmpresaVO empresaVO, Connection con) throws Exception {
        VendaAvulsa vendaAvulsaDAO;
        try {
            vendaAvulsaDAO = new VendaAvulsa(con);

            VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
            vendaAvulsaVO.setTipoComprador("CI");
            vendaAvulsaVO.setCliente(clienteVO);
            vendaAvulsaVO.setDataRegistro(data);
            vendaAvulsaVO.setEmpresa(empresaVO);

            ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
            item.setDataVenda(data);
            item.setQuantidade(1);
            item.setUsuarioVO(responsavel);
            item.setProduto(produto);
            item.setValorParcial(valor);
            item.getProduto().setValorFinal(valor);
            vendaAvulsaVO.setValorTotal(valor);
            vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
            vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
            vendaAvulsaVO.setResponsavel(item.getUsuarioVO());
            vendaAvulsaVO.setDescricaoAdicional("Venda Avulsa");

            vendaAvulsaDAO.incluirSemCommit(vendaAvulsaVO, false, data);
            return vendaAvulsaVO;
        } finally {
            vendaAvulsaDAO = null;
        }
    }

    private EnvelopeRespostaDTO gymPass(HttpServletRequest request, Connection con) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        Empresa empresaDAO;
        PeriodoAcessoCliente periodoAcessoClienteDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            empresaDAO = new Empresa(con);
            periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codPessoa = jsonObject.optInt("pessoa");
            Integer codUsuario = jsonObject.optInt("usuario");
            Integer codEmpresa = jsonObject.optInt("empresa");
            String operacao = jsonObject.optString("operacao");
            String chave = obterChave(request);

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            UsuarioVO usuarioVO = null;
            if (!UteisValidacao.emptyNumber(codUsuario)) {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            EmpresaVO empresaVO = null;
            if (!UteisValidacao.emptyNumber(codEmpresa)) {
                empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            switch (operacao) {
                case "obter":
                    return EnvelopeRespostaDTO.of(new AlunoInfoGymPassDTO(clienteVO));
                case "cadastro":

                    if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                        throw new Exception("Usuário não informado");
                    }
                    if (empresaVO == null || UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
                        throw new Exception("Empresa não informado");
                    }

                    Integer tipo = jsonObject.optInt("tipo");
                    String tokenGymPass = jsonObject.optString("token");

                    clienteVO.setGympasUniqueToken(tokenGymPass.trim());
                    clienteVO.setGympassTypeNumber(tipo.toString());
                    clienteDAO.validarInformacoesTokenGymPass(clienteVO, empresaVO);
                    String tokem = clienteVO.getGympasUniqueToken();

                    String matricula = clienteDAO.nomeMatricuaClienteGymPassUniqueToken(clienteVO.getGympasUniqueToken());
                    if (!matricula.isEmpty() && !matricula.trim().contains(clienteVO.getMatricula())) {
                        throw new Exception("O Token (" + tokem + ") já está sendo utilizado por " + matricula);
                    }

                    clienteDAO.alterarGymPassUniqueToken(clienteVO, usuarioVO);

                    try {
                        clienteDAO.validarTokenGymPass(chave, clienteVO, usuarioVO, empresaVO, false);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                    return EnvelopeRespostaDTO.of("Token salvo com sucesso!");

                case "excluir":

                    if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                        throw new Exception("Usuário não informado");
                    }
                    if (empresaVO == null || UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
                        throw new Exception("Empresa não informado");
                    }

                    validarPermissao(empresaVO, "ExcluirTokemGympass", "9.63 - Excluir Tokem GymPass", usuarioVO, con);
                    clienteDAO.excluirTokenGymPassCliente(clienteVO, usuarioVO);

                    return EnvelopeRespostaDTO.of("Token excluído com sucesso!");

                case "autorizar":

                    if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                        throw new Exception("Usuário não informado");
                    }
                    if (empresaVO == null || UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
                        throw new Exception("Empresa não informado");
                    }

                    clienteDAO.validarTokenGymPass(chave, clienteVO, usuarioVO, empresaVO, false);
                    return EnvelopeRespostaDTO.of("GymPass lançado com sucesso!");

                case "historico":

                    List<PeriodoAcessoClienteVO> lista = periodoAcessoClienteDAO.consultarPorPessoaGymPass(clienteVO.getPessoa().getCodigo());
                    List<HistoricoGymPassDTO> listaDTO = new ArrayList<>();
                    for (PeriodoAcessoClienteVO obj : lista) {
                        listaDTO.add(new HistoricoGymPassDTO(obj));
                    }

                    PaginadorDTO paginadorDTO = new PaginadorDTO(request);
                    if (paginadorDTO != null
                            && paginadorDTO.getPage() != null
                            && paginadorDTO.getSize() != null) {
                        int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                        int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                        if (ultimoRegistro > listaDTO.size()) {
                            listaDTO = listaDTO.subList(primeiroPaginacao, listaDTO.size());
                        } else {
                            listaDTO = listaDTO.subList(primeiroPaginacao, ultimoRegistro);
                        }
                    }

                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos((long) lista.size());
                    }

                    return EnvelopeRespostaDTO.of(listaDTO, paginadorDTO);

                default:
                    throw new Exception("Nenhuma operacao executada");
            }
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO goGood(HttpServletRequest request, Connection con) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        Empresa empresaDAO;
        PeriodoAcessoCliente periodoAcessoClienteDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            empresaDAO = new Empresa(con);
            periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codPessoa = jsonObject.optInt("pessoa");
            Integer codUsuario = jsonObject.optInt("usuario");
            Integer codEmpresa = jsonObject.optInt("empresa");
            String operacao = jsonObject.optString("operacao");
            String tokenAcademyGoGood = jsonObject.optString("tokenAcademy");
            String chave = obterChave(request);

            ClienteVO clienteVO = null;
            if(!UteisValidacao.emptyNumber(codPessoa)) {
                clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            }

            UsuarioVO usuarioVO = null;
            if (!UteisValidacao.emptyNumber(codUsuario)) {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            EmpresaVO empresaVO = null;
            if (!UteisValidacao.emptyNumber(codEmpresa)) {
                empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            switch (operacao) {
                case "obter":
                    return EnvelopeRespostaDTO.of(new AlunoInfoGymPassDTO(clienteVO.getTokenGoGood()));
                case "cadastrarChaveWebhook":
                    empresaVO.setTokenAcademyGoGood(tokenAcademyGoGood);
                    return EnvelopeRespostaDTO.of(clienteDAO.inserirWebhookGogood(chave, empresaVO));
                case "cadastro":

                    if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                        throw new Exception("Usuário não informado");
                    }
                    if (empresaVO == null || UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
                        throw new Exception("Empresa não informado");
                    }

                    String tokenGoGood = jsonObject.optString("tokenUser");

                    empresaVO.setTokenAcademyGoGood(tokenAcademyGoGood);
                    clienteVO.setTokenGoGood(tokenGoGood);
                    clienteDAO.validarInformacoesTokenGoGood(clienteVO, empresaVO);
                    String tokem = clienteVO.getTokenGoGood();

                    String matricula = clienteDAO.nomeMatricuaClienteGoGoodToken(clienteVO.getTokenGoGood());
                    if (!matricula.isEmpty() && !matricula.trim().contains(clienteVO.getMatricula())) {
                        throw new Exception("O Token (" + tokem + ") já está sendo utilizado por " + matricula);
                    }

                    clienteDAO.alterarGoGoodToken(clienteVO, usuarioVO);

                    try {
                        clienteDAO.validarTokenGoGood(chave, clienteVO, usuarioVO, empresaVO, false);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        throw new Exception("Token salvo, porém retornou ERRO: "+ex.getMessage());
                    }

                    return EnvelopeRespostaDTO.of("Token salvo com sucesso!");

                case "excluir":

                    if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                        throw new Exception("Usuário não informado");
                    }
                    if (empresaVO == null || UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
                        throw new Exception("Empresa não informado");
                    }

                    validarPermissao(empresaVO, "ExcluirTokemGoGood", "10.06 - Excluir Tokem GoGood", usuarioVO, con);
                    clienteDAO.excluirTokenGoGoodCliente(clienteVO, usuarioVO);

                    return EnvelopeRespostaDTO.of("Token excluído com sucesso!");

                case "historico":

                    List<PeriodoAcessoClienteVO> lista = periodoAcessoClienteDAO.consultarPorPessoaGoGood(clienteVO.getPessoa().getCodigo());
                    List<HistoricoGymPassDTO> listaDTO = new ArrayList<>();
                    for (PeriodoAcessoClienteVO obj : lista) {
                        listaDTO.add(new HistoricoGymPassDTO(obj, obj.getTokenGoGood()));
                    }

                    PaginadorDTO paginadorDTO = new PaginadorDTO(request);
                    if (paginadorDTO != null
                            && paginadorDTO.getPage() != null
                            && paginadorDTO.getSize() != null) {
                        int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                        int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                        if (ultimoRegistro > listaDTO.size()) {
                            listaDTO = listaDTO.subList(primeiroPaginacao, listaDTO.size());
                        } else {
                            listaDTO = listaDTO.subList(primeiroPaginacao, ultimoRegistro);
                        }
                    }

                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos((long) lista.size());
                    }

                    return EnvelopeRespostaDTO.of(listaDTO, paginadorDTO);

                default:
                    throw new Exception("Nenhuma operacao executada");
            }
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            empresaDAO = null;
        }
    }

    public void validarPermissao(EmpresaVO empresaVO, String permissao, String descricao, UsuarioVO usuarioVO, Connection con) throws Exception {
        Permissao permissaoDAO;
        ControleAcesso controleAcessoDAO;
        UsuarioPerfilAcesso usuarioPerfilAcessoDAO;
        try {
            permissaoDAO = new Permissao(con);
            controleAcessoDAO = new ControleAcesso(con);
            usuarioPerfilAcessoDAO = new UsuarioPerfilAcesso(con);

            usuarioVO.setUsuarioPerfilAcessoVOs(usuarioPerfilAcessoDAO.consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            if (usuarioVO.getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (usuarioVO.getAdministrador()) {
                    return;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = usuarioVO.getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (empresaVO.getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(permissaoDAO.consultarPermissaos(
                            usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    controleAcessoDAO.verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            usuarioVO, permissao, descricao);
                }
            }
        } finally {
            permissaoDAO = null;
            controleAcessoDAO = null;
            usuarioPerfilAcessoDAO = null;
        }
    }

    private EnvelopeRespostaDTO linkCartao(HttpServletRequest request, Connection con) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        Empresa empresaDAO;
        ZillyonWebFacade zillyonWebFacade;
        VendasOnlineService vendasOnlineService;
        Log logDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            empresaDAO = new Empresa(con);
            logDAO = new Log(con);
            zillyonWebFacade = new ZillyonWebFacade(con);
            vendasOnlineService = new VendasOnlineService(null, con);

            String chave = obterChave(request);

            JSONObject jsonObject = getJSONBody(request);
            Integer codCliente = jsonObject.optInt("cliente");
            Integer codUsuario = jsonObject.optInt("usuario");
            Integer codEmpresa = jsonObject.optInt("empresa");
            String operacao = jsonObject.optString("operacao");
            Boolean todasEmAberto = jsonObject.optBoolean("todasEmAberto");
            String parcelasSelecionadas = jsonObject.optString("parcelasSelecionadas");
            String tipoCompartilhamento = jsonObject.optString("tipoCompartilhamento");
            Boolean linkJaGerado = jsonObject.optBoolean("linkGerado");
            Integer numeroVezesParcelamentoOperadora = 1; //Valor padrão de cobrança

            try {
                numeroVezesParcelamentoOperadora = Integer.parseInt(jsonObject.optString("numeroVezesParcelamentoOperadora"));
            } catch (Exception e) {
            }

            if (!operacao.equals("pagamento") &&
                    !operacao.equals("cadastrar")) {
                throw new Exception("Nenhuma operacao executada");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            boolean linkParaPagamento = operacao.equalsIgnoreCase("pagamento");

            if (!linkJaGerado) {
                zillyonWebFacade.notificarRecursoSistema(chave,
                        (linkParaPagamento ? RecursoSistema.LINK_PAGAMENTO_ONLINE : RecursoSistema.LINK_CADASTRO_CARTAO_ONLINE),
                        usuarioVO, empresaVO);
                boolean buscarTodas = ((todasEmAberto != null && todasEmAberto) || !UteisValidacao.emptyString(parcelasSelecionadas));
                clienteDAO.validarCompartilharLink(clienteVO, !linkParaPagamento, empresaVO, usuarioVO, buscarTodas, parcelasSelecionadas);

                String link = vendasOnlineService.obterLinkPagamentoVendasOnline(chave, clienteVO, clienteVO.getEmpresa().getCodigo(), linkParaPagamento,
                        linkParaPagamento ? OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO : OrigemCobrancaEnum.VENDAS_ONLINE_LINK_CADASTRAR, null, usuarioVO,
                        null, todasEmAberto, parcelasSelecionadas, numeroVezesParcelamentoOperadora);
                return EnvelopeRespostaDTO.of(link);
            }

            boolean whatsApp = tipoCompartilhamento.equalsIgnoreCase("whatsApp");
            incluirLogResponsavelGerarLink(clienteVO, usuarioVO, whatsApp, linkParaPagamento, logDAO, con);

            return EnvelopeRespostaDTO.of("");
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            empresaDAO = null;
            zillyonWebFacade = null;
            vendasOnlineService = null;
            logDAO = null;
        }
    }

    public void incluirLogResponsavelGerarLink(ClienteVO cliente, UsuarioVO usuario, boolean whatsapp, boolean linkParaPagamento, Log logDao, Connection con) throws Exception {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(cliente.getPessoa().getCodigo().toString());
            obj.setNomeEntidade("LINK DE COMPARTILHAMENTO");
            obj.setNomeEntidadeDescricao("LINK DE COMPARTILHAMENTO");
            if (!linkParaPagamento) {
                obj.setOperacao("GEROU LINK PARA CADASTRAR CARTÃO");
            } else {
                obj.setOperacao("GEROU LINK PARA PAGAMENTO");
            }
            obj.setResponsavelAlteracao(usuario.getNome());
            obj.setUserOAMD(usuario.getUserOamd());
            obj.setNomeCampo("Compartilhou via");
            obj.setValorCampoAnterior("");
            if (whatsapp) {
                obj.setValorCampoAlterado("botão WhatsApp");
            } else {
                obj.setValorCampoAlterado("botão Copiar");
            }

            obj.setDataAlteracao(Calendario.hoje());
            registrarLogObjetoVO(obj, cliente.getPessoa().getCodigo(), logDao);
        } catch (Exception e) {
            registrarLogErroObjetoVO("GEROU LINK DE COMPARTILHAMENTO", cliente.getPessoa().getCodigo(), "ERRO AO REGISTRAR LOG "
                    + " GEROU LINK DE COMPARTILHAMENTO", usuario.getNome(), usuario.getUserOamd(), con);
            e.printStackTrace();
        }
    }

    private EnvelopeRespostaDTO definirSenhaDeAcesso(HttpServletRequest request, Connection con) throws Exception {
        Pessoa pessoaDao = null;
        Cliente clienteDao = null;
        Empresa empresaDao = null;
        try {
            pessoaDao = new Pessoa(con);
            empresaDao = new Empresa(con);
            clienteDao = new Cliente(con);

            JSONObject jsonObject = getJSONBody(request);
            // Operação: obter ou cadastrar
            String operacao = jsonObject.getString("operacao");
            Integer codigoPessoa = jsonObject.getInt("codPessoa");
            PessoaVO pessoaVO = pessoaDao.consultarPorChavePrimaria(codigoPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (operacao.equals("cadastrar")) {
                boolean liberarSenhaAcesso = true;
                try {
                    liberarSenhaAcesso = jsonObject.getBoolean("liberarSenhaAcesso");
                } catch (Exception e) {
                }
                Integer codigoCliente = jsonObject.getInt("codCliente");
                Integer codigoEmpresa = jsonObject.getInt("codEmpresa");
                pessoaVO.setLiberaSenhaAcesso(liberarSenhaAcesso);
                if (liberarSenhaAcesso) {
                    String plainPsw = jsonObject.getString("senha");
                    String plainConfirmPsw = jsonObject.getString("confirmarSenha");
                    pessoaVO.setSenhaAcesso(plainPsw);
                    pessoaVO.setConfirmarSenhaAcesso(plainConfirmPsw);
                    EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ClienteVO clienteVO = clienteDao.consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    pessoaVO.validarSenhaAcesso(empresaVO.isSenhaAcessoOnzeDigitos(), clienteVO);
                    String senhaEncriptada = Uteis.encriptar(plainPsw);
                    if (pessoaDao.senhaAcessoJaUtilizada(0, pessoaVO.getCodigo(), senhaEncriptada)) {
                        throw new Exception("Senha não permitida. Informe outra senha.");
                    }

                    pessoaDao.liberarRemoverSenhaAcesso(pessoaVO);
                    pessoaDao.alterarSenhaAcesso(pessoaVO.getCodigo(), pessoaVO.getSenhaAcesso());
                    return EnvelopeRespostaDTO.of("Senha alterada com sucesso !");

                } else {
                    pessoaDao.liberarRemoverSenhaAcesso(pessoaVO);
                    return EnvelopeRespostaDTO.of("Senha excluída e desabilitada com sucesso!");
                }
            } else if (operacao.equals("obterLiberarSenhaAcesso")) {
                return EnvelopeRespostaDTO.of(pessoaVO.getLiberaSenhaAcesso());
            } else {
                throw new Exception("Operação inválida");
            }
        } finally {
            pessoaDao = null;
            empresaDao = null;
            clienteDao = null;
        }
    }

    private EnvelopeRespostaDTO bloqueioAcessoCatraca(HttpServletRequest request, Connection con) throws Exception {
        ClienteMensagem clienteMensagemDao = null;
        Log logDao = null;
        Usuario usuarioDao = null;
        Cliente clienteDao = null;
        try {
            clienteMensagemDao = new ClienteMensagem(con);
            logDao = new Log(con);
            usuarioDao = new Usuario(con);
            clienteDao = new Cliente(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codCliente = jsonObject.getInt("codCliente");
            Integer codUsuarioResponsavel = jsonObject.optInt("codUsuarioResponsavel");
            String operacao = jsonObject.getString("operacao");
            String chave = obterChave(request);

            ClienteVO clienteVOMin;
            UsuarioVO usuarioResponsavelAlteracaoVO;

            switch (operacao) {
                case "consultar":
                    ClienteMensagemVO clienteMensagem = clienteMensagemDao.consultarPorCodigoTipoMensagemECliente(codCliente, TiposMensagensEnum.CATRACA.getSigla(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ClienteMensagemDTO clienteMensagemDTO = new ClienteMensagemDTO(clienteMensagem);

                    return EnvelopeRespostaDTO.of(clienteMensagemDTO);
                case "gravar":
                    JSONObject jsonMsgCatraca = jsonObject.getJSONObject("clienteMensagem");
                    String mensagem = jsonMsgCatraca.getString("mensagem");
                    if (mensagem.length() > 214) {
                        throw new Exception("A mensagem deve conter no máximo 214 caracteres!");
                    }

                    clienteVOMin = clienteDao.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_MINIMOS);
                    usuarioResponsavelAlteracaoVO = usuarioDao.consultarPorChavePrimaria(codUsuarioResponsavel, Uteis.NIVELMONTARDADOS_MINIMOS);

                    String dataBloqueio = jsonMsgCatraca.optString("dataBloqueio");
                    if (jsonMsgCatraca.has("codigo") && jsonMsgCatraca.getInt("codigo") != 0) {
                        ClienteMensagemVO clienteMensagemVO = clienteMensagemDao.consultarPorChavePrimaria(jsonMsgCatraca.getInt("codigo"), Uteis.NIVELMONTARDADOS_TODOS);
                        ClienteMensagemVO clienteMensagemVOAntAlt = (ClienteMensagemVO) clienteMensagemVO.getClone(true);
                        clienteMensagemVO.setBloqueio(jsonMsgCatraca.getBoolean("bloqueio"));
                        clienteMensagemVO.setMensagem(jsonMsgCatraca.getString("mensagem"));
                        clienteMensagemVO.setDataBloqueio(UteisValidacao.emptyString(dataBloqueio) ? null : Calendario.getDate("yyyy-MM-dd", dataBloqueio));
                        clienteMensagemDao.alterar(clienteMensagemVO);
                        try {
                            clienteMensagemVO.setObjetoVOAntesAlteracao(clienteMensagemVOAntAlt);
                            SuperControle.registrarLogObjetoVOGeralAlterandoResponsavel(
                                    clienteMensagemVO, clienteMensagemVO.getCodigo(), "MENSAGEMCATRACA", clienteVOMin.getPessoa().getCodigo(), false, usuarioResponsavelAlteracaoVO.getNome(), usuarioResponsavelAlteracaoVO, con
                            );
                        } catch (Exception e) {
                            registrarLogErroObjetoVO("MENSAGEMCATRACA", clienteVOMin.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE MENSAGEM CATRACA", usuarioResponsavelAlteracaoVO.getNome(), usuarioResponsavelAlteracaoVO.getUserOamd(), con);
                        }

                        bloquearAgora(clienteVOMin, clienteMensagemVO, chave);

                        return EnvelopeRespostaDTO.of("Mensagem alterada com sucesso!");
                    } else {
                        ClienteMensagemVO clienteMensagemVO = new ClienteMensagemVO();
                        clienteMensagemVO.setMensagem(mensagem);
                        clienteMensagemVO.setDataBloqueio(UteisValidacao.emptyString(dataBloqueio) ? null : Calendario.getDate("yyyy-MM-dd", dataBloqueio));
                        clienteMensagemVO.setObjetoVOAntesAlteracao(new ClienteMensagemVO());
                        clienteMensagemVO.setBloqueio(jsonMsgCatraca.getBoolean("bloqueio"));
                        clienteMensagemVO.setNovoObj(true);
                        clienteMensagemVO.setTipomensagem(TiposMensagensEnum.CATRACA);
                        ClienteVO clienteVO = new ClienteVO();
                        clienteVO.setCodigo(codCliente);
                        clienteMensagemVO.setCliente(clienteVO);
                        clienteMensagemDao.incluir(clienteMensagemVO);

                        try {
                            clienteMensagemVO.setNovoObj(true);
                            SuperControle.registrarLogObjetoVOGeralAlterandoResponsavel(
                                    clienteMensagemVO, clienteMensagemVO.getCodigo(), "MENSAGEMCATRACA", clienteVOMin.getPessoa().getCodigo(), false, usuarioResponsavelAlteracaoVO.getNome(), usuarioResponsavelAlteracaoVO, con
                            );
                        } catch (Exception e) {
                            registrarLogErroObjetoVO("MENSAGEMCATRACA", clienteVOMin.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE MENSAGEM CATRACA", usuarioResponsavelAlteracaoVO.getNome(), usuarioResponsavelAlteracaoVO.getUserOamd(), con);
                        }

                        bloquearAgora(clienteVOMin, clienteMensagemVO, chave);

                        return EnvelopeRespostaDTO.of("Mensagem salva com sucesso!");
                    }

                case "excluir":
                    Integer codigoMensagemCatraca = jsonObject.getInt("codigo");
                    ClienteMensagemVO clienteMensagemVO = new ClienteMensagemVO();
                    clienteMensagemVO.setCodigo(codigoMensagemCatraca);
                    clienteMensagemDao.excluir(clienteMensagemVO);

                    clienteVOMin = clienteDao.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_MINIMOS);
                    usuarioResponsavelAlteracaoVO = usuarioDao.consultarPorChavePrimaria(codUsuarioResponsavel, Uteis.NIVELMONTARDADOS_MINIMOS);

                    try {
                        registrarLogExclusaoObjetoVO(clienteMensagemVO, clienteMensagemVO.getCodigo(), "MENSAGEMCATRACA", clienteVOMin.getPessoa().getCodigo(), usuarioResponsavelAlteracaoVO, logDao);
                    } catch (Exception e) {
                        e.printStackTrace();
                        registrarLogErroObjetoVO("MENSAGEMCATRACA", clienteVOMin.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE MENSAGEM CATRACA", usuarioResponsavelAlteracaoVO.getNome(), usuarioResponsavelAlteracaoVO.getUserOamd(), con);
                    }

                    clienteMensagemVO.setBloqueio(false);
                    bloquearAgora(clienteVOMin, clienteMensagemVO, chave);

                    return EnvelopeRespostaDTO.of("Mensagem excluída com sucesso!");
                default:
                    throw new Exception("Bloqueio Acesso Catraca: Operação inválida!");
            }
        } finally {
            clienteMensagemDao = null;
            logDao = null;
            usuarioDao = null;
            clienteDao = null;
        }
    }

    private void bloquearAgora(ClienteVO clienteVO, ClienteMensagemVO clienteMensagemVO, String chave) {
        try {
            if (clienteMensagemVO.getBloqueio() || (clienteMensagemVO.getDataBloqueio() != null && Calendario.igual(clienteMensagemVO.getDataBloqueio(), Calendario.hoje()))) {
                SuperControle.notificarOuvintes("bloquear agora("+clienteVO.getCodAcesso()+")", PropsService.getPropertyValue("urlNotificacaoAcesso"), chave);
            } else {
                SuperControle.notificarOuvintes("desbloquear agora("+clienteVO.getCodAcesso()+")", PropsService.getPropertyValue("urlNotificacaoAcesso"), chave);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    private EnvelopeRespostaDTO registrarAcessoManual(HttpServletRequest request, Connection con) throws Exception {
        LocalAcesso localAcessoDao = null;
        Coletor coletorDao = null;
        AcessoCliente acessoClienteDao = null;
        Usuario usuarioDao = null;
        Cliente clienteDao = null;
        ControleCreditoTreino controleCreditoTreinoDao = null;
        SituacaoClienteSinteticoDW situacaoClienteSinteticoDW = null;
        ConfiguracaoSistema configuracaoSistemaDao = null;
        ZillyonWebFacade zwFacadeDao = null;
        Empresa empresaDao = null;
        Log logDao = null;

        try {
            localAcessoDao = new LocalAcesso(con);
            coletorDao = new Coletor(con);
            acessoClienteDao = new AcessoCliente(con);
            usuarioDao = new Usuario(con);
            clienteDao = new Cliente(con);
            controleCreditoTreinoDao = new ControleCreditoTreino(con);
            situacaoClienteSinteticoDW = new SituacaoClienteSinteticoDW(con);
            configuracaoSistemaDao = new ConfiguracaoSistema(con);
            zwFacadeDao = new ZillyonWebFacade(con);
            empresaDao = new Empresa(con);
            logDao = new Log(con);

            JSONObject jsonObject = getJSONBody(request);
            String operacao = jsonObject.getString("operacao");
            Integer codEmpresa = jsonObject.getInt("codEmpresa");
            if (operacao.equals("listaLocalAcesso")) {
                List<LocalAcessoVO> localAcessos = localAcessoDao.consultarPorEmpresa(codEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List<LocalAcessoDTO> localAcessoDTOS = new ArrayList<>();
                localAcessos.forEach(
                        la -> localAcessoDTOS.add(new LocalAcessoDTO(la.getCodigo(), la.getDescricao()))
                );
                return EnvelopeRespostaDTO.of(localAcessoDTOS);
            } else {

                JSONObject jsonRegAcesso = jsonObject.getJSONObject("registrarAcesso");

                if (operacao.equals("listaColetor")) {
                    List<ColetorVO> coletores = coletorDao.consultarColetores(jsonRegAcesso.getInt("codLocalAcesso"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    List<ColetorDTO> coletoresDTO = new ArrayList<>();
                    coletores.forEach(
                            coletor -> coletoresDTO.add(new ColetorDTO(coletor.getCodigo(), coletor.getDescricao()))
                    );
                    return EnvelopeRespostaDTO.of(coletoresDTO);
                } else if (operacao.equals("consultarAcessosDia")) {
                    ClienteVO clienteVO = new ClienteVO();
                    clienteVO.setCodigo(jsonRegAcesso.getInt("codCliente"));
                    validarSalvarRegistroAcessoManual(jsonRegAcesso);
                    List<AcessoClienteVO> listaAcessosDia = acessoClienteDao.consultarUltimoAcessoDia(
                            clienteVO,
                            Calendario.getDate("yyyy-MM-dd", jsonRegAcesso.getString("dataAcesso")),
                            null,
                            Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA
                    );
                    List<AcessoClienteDTO> acessoClienteDTOS = new ArrayList<>();
                    if (!UteisValidacao.emptyList(listaAcessosDia)) {
                        listaAcessosDia.forEach(
                                lad -> acessoClienteDTOS.add(new AcessoClienteDTO(
                                        lad.getDataHoraEntrada(),
                                        lad.getDataHoraSaida(),
                                        lad.getLocalAcesso().getCodigo(),
                                        lad.getColetor().getCodigo(),
                                        lad.getCodigo()
                                ))
                        );
                    }
                    return EnvelopeRespostaDTO.of(acessoClienteDTOS);
                } else if (operacao.equals("gravar")) {
                    if (jsonRegAcesso.has("codigo")) {
                        return alterarAcessoCliente(
                                jsonRegAcesso,
                                acessoClienteDao,
                                clienteDao,
                                logDao,
                                situacaoClienteSinteticoDW,
                                usuarioDao,
                                zwFacadeDao,
                                localAcessoDao,
                                request,
                                con
                        );
                    } else {
                        return gravarAcessoCliente(
                                jsonRegAcesso,
                                clienteDao,
                                usuarioDao,
                                localAcessoDao,
                                coletorDao,
                                acessoClienteDao,
                                controleCreditoTreinoDao,
                                configuracaoSistemaDao,
                                situacaoClienteSinteticoDW,
                                empresaDao,
                                zwFacadeDao,
                                logDao,
                                request,
                                con
                        );
                    }
                } else {
                    throw new Exception("Opção inválida");
                }
            }

        } finally {
            localAcessoDao = null;
            coletorDao = null;
            acessoClienteDao = null;
            usuarioDao = null;
            clienteDao = null;
            controleCreditoTreinoDao = null;
            situacaoClienteSinteticoDW = null;
            configuracaoSistemaDao = null;
            zwFacadeDao = null;
            empresaDao = null;
            logDao = null;
        }
    }

    private EnvelopeRespostaDTO alterarAcessoCliente(
            JSONObject jsonRegAcesso,
            AcessoCliente acessoClienteDao,
            Cliente clienteDao,
            Log logDao,
            SituacaoClienteSinteticoDW situacaoClienteSinteticoDW,
            Usuario usuarioDao,
            ZillyonWebFacade zwFacade,
            LocalAcesso localAcessoDao,
            HttpServletRequest request,
            Connection con
    ) throws Exception {
        validarSalvarRegistroAcessoManual(jsonRegAcesso);
        Date dataAcesso = Calendario.getDate("yyyy-MM-dd", jsonRegAcesso.getString("dataAcesso"));
        String horaEntrada = jsonRegAcesso.getString("horaEntrada");
        boolean registrarSaida = jsonRegAcesso.getBoolean("registrarSaida");
        Integer codColetor = jsonRegAcesso.getInt("codColetor");
        String horaSaida = jsonRegAcesso.getString("horaSaida");
        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setCodigo(jsonRegAcesso.getInt("codCliente"));
        AcessoClienteVO acessoClienteVO = new AcessoClienteVO();
        acessoClienteVO.setCodigo(jsonRegAcesso.getInt("codigo"));
        clienteVO = clienteDao.consultarPorChavePrimaria(jsonRegAcesso.getInt("codCliente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(jsonRegAcesso.getInt("codUsuario"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        LocalAcessoVO localAcessoVO = localAcessoDao.consultarPorCodigo(jsonRegAcesso.getInt("codLocalAcesso"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        String[] entrada = horaEntrada.split(":");
        Calendar calendarEntrada = Calendario.getInstance(dataAcesso);
        calendarEntrada.set(Calendar.HOUR, Integer.parseInt(entrada[0]));
        calendarEntrada.set(Calendar.MINUTE, Integer.parseInt(entrada[1]));
        Date dataGravarEntrada = calendarEntrada.getTime();
        acessoClienteVO.setDataHoraEntrada(dataGravarEntrada);

        if (registrarSaida) {
            String[] saida = horaSaida.split(":");
            Calendar calendarSaida = Calendario.getInstance(dataAcesso);
            calendarSaida.set(Calendar.HOUR, Integer.parseInt(saida[0]));
            calendarSaida.set(Calendar.MINUTE, Integer.parseInt(saida[1]));
            Date dataGravarSaida = calendarSaida.getTime();

            if (Calendario.menorComHora(dataGravarSaida, dataGravarEntrada)) {
                throw new Exception("A hora de saida deve ser maior que a entrada");
            }

            acessoClienteVO.setDataHoraSaida(dataGravarSaida);
            situacaoClienteSinteticoDW.registrarUltimoAcessoSaida(clienteVO.getCodigo(), dataGravarSaida);
        }
        acessoClienteDao.alterarAcessoEntradaSaida(acessoClienteVO);
        AcessoClienteVO ultimoAcesso = acessoClienteDao.consultarUltimoAcesso(clienteVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        if (!UteisValidacao.emptyNumber(ultimoAcesso.getCodigo())) {
            clienteDao.registrarUltimoAcesso(clienteVO.getCodigo(), ultimoAcesso.getCodigo());
        }
        TreinoWSConsumer.atualizarStatusAluno(request.getParameter("key"), clienteVO.getCodigo(), dataGravarEntrada, localAcessoVO.getEmpresa().getCodigo());
        situacaoClienteSinteticoDW.registrarUltimoAcesso(clienteVO.getCodigo(), dataGravarEntrada);
        zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_DADOSACESSO, false);

        //REGISTRAR LOG INCLUSAO
        try {
            LogVO log = new LogVO();
            log.setChavePrimaria(clienteVO.getPessoa().getCodigo().toString());
            log.setNomeEntidade("CLIENTE - ACESSO MANUAL");
            log.setNomeEntidadeDescricao("Cliente - Acesso Manual");
            log.setOperacao("ALTERAR - REGISTRO ACESSO MANUAL");
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setNomeCampo("AcessoManual-Cliente");
            log.setValorCampoAnterior("");
            StringBuilder campoAlterado = new StringBuilder();
            if (acessoClienteVO != null && acessoClienteVO.getCodigo() != null) {
                campoAlterado.append("CÓDIGO ACESSO: " + acessoClienteVO.getCodigo()).append(" \n");
            }
            if (acessoClienteVO != null && acessoClienteVO.getDataHoraEntrada() != null) {
                campoAlterado.append("ENTRADA: " + Uteis.getDataAplicandoFormatacao(acessoClienteVO.getDataHoraEntrada(), "dd/MM HH:mm:ss")).append(" \n");
            }
            if (acessoClienteVO != null && acessoClienteVO.getDataHoraSaida() != null) {
                campoAlterado.append("SAÍDA: " + Uteis.getDataAplicandoFormatacao(acessoClienteVO.getDataHoraSaida(), "dd/MM HH:mm:ss")).append(" \n");
            }
            log.setValorCampoAlterado(campoAlterado.toString());
            log.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            registrarLogObjetoVO(log, clienteVO.getPessoa().getCodigo(), logDao);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CLIENTE - ACESSO MANUAL", clienteVO.getPessoa().getCodigo(), "ERRO AO REGISTRAR LOG ACESSO MANUAL", usuarioVO.getNome(), usuarioVO.getUserOamd(), con);
            e.printStackTrace();
        }
        return EnvelopeRespostaDTO.of("Último acesso alterado para MAT " + clienteVO.getMatricula() + " - " + clienteVO.getNome_Apresentar() + " - " + Uteis.getDataAplicandoFormatacao(acessoClienteVO.getDataHoraEntrada(), "dd/MM/yyyy HH:mm"));
    }

    private EnvelopeRespostaDTO gravarAcessoCliente(
            JSONObject jsonRegAcesso,
            Cliente clienteDao,
            Usuario usuarioDao,
            LocalAcesso localAcessoDao,
            Coletor coletorDao,
            AcessoCliente acessoClienteDao,
            ControleCreditoTreino controleCreditoTreinoDao,
            ConfiguracaoSistema configuracaoSistemaDao,
            SituacaoClienteSinteticoDW situacaoClienteSinteticoDW,
            Empresa empresaDao,
            ZillyonWebFacade zwFacadeDao,
            Log logDao,
            HttpServletRequest request,
            Connection con
    ) throws Exception {
        validarSalvarRegistroAcessoManual(jsonRegAcesso);
        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setCodigo(jsonRegAcesso.getInt("codCliente"));
        Date dataAcesso = Calendario.getDate("yyyy-MM-dd", jsonRegAcesso.getString("dataAcesso"));
        String horaEntrada = jsonRegAcesso.getString("horaEntrada");
        boolean registrarSaida = jsonRegAcesso.getBoolean("registrarSaida");
        Integer codColetor = jsonRegAcesso.getInt("codColetor");

        clienteVO = clienteDao.consultarPorChavePrimaria(jsonRegAcesso.getInt("codCliente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(jsonRegAcesso.getInt("codUsuario"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        LocalAcessoVO localAcessoVO = localAcessoDao.consultarPorCodigo(jsonRegAcesso.getInt("codLocalAcesso"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        ColetorVO coletorVO = coletorDao.consultarPorCodigo(codColetor);

        String[] entrada = horaEntrada.split(":");
        Calendar calendarEntrada = Calendario.getInstance(dataAcesso);
        calendarEntrada.set(Calendar.HOUR, Integer.parseInt(entrada[0]));
        calendarEntrada.set(Calendar.MINUTE, Integer.parseInt(entrada[1]));
        calendarEntrada.set(Calendar.SECOND, 0);
        Date dataGravarEntrada = calendarEntrada.getTime();

        Date dataAtual = Calendario.hoje();
        if (Calendario.maior(dataGravarEntrada, dataAtual)) {
            Uteis.logarDebug("gravarAcessoCliente | dataGravarEntrada: " + dataGravarEntrada.getTime() + " | " + Calendario.getDataAplicandoFormatacao(dataGravarEntrada, "dd/MM/yyyy HH:mm:ss") +
                    " | dataAtual: " + dataAtual.getTime() + " | " + Calendario.getDataAplicandoFormatacao(dataAtual, "dd/MM/yyyy HH:mm:ss") + " | ");
            throw new Exception("Não é possivel registrar acesso futuro");
        }

        Date dataGravarSaida = null;
        if (registrarSaida) {
            String horaSaida = jsonRegAcesso.getString("horaSaida");
            String[] saida = horaSaida.split(":");
            Calendar calendarSaida = Calendario.getInstance(dataAcesso);
            calendarSaida.set(Calendar.HOUR, Integer.parseInt(saida[0]));
            calendarSaida.set(Calendar.MINUTE, Integer.parseInt(saida[1]));
            calendarSaida.set(Calendar.SECOND, 0);
            dataGravarSaida = calendarSaida.getTime();
            if (Calendario.menorComHora(dataGravarSaida, dataGravarEntrada)) {
                throw new Exception("A hora de saida deve ser maior que a entrada");
            }
        }

        AcessoClienteVO acessoEntrada = acessoClienteDao.registrarAcessoCliente(
                dataGravarEntrada, clienteVO, SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO, DirecaoAcessoEnum.DA_ENTRADA, localAcessoVO, coletorVO,
                usuarioVO, MeioIdentificacaoEnum.AVULSO, controleCreditoTreinoDao, request.getParameter("key"));

        ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDao.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (configuracaoSistemaVO.isMarcarPresencaPeloAcesso()) {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(request.getParameter("key"));
            acessoControle.marcarPresenca(clienteVO, dataGravarEntrada, localAcessoVO.getEmpresa().getCodigo(), usuarioVO);
            acessoControle.marcarPresencaAulaCheia(clienteVO, dataGravarEntrada, localAcessoVO.getEmpresa().getCodigo(), usuarioVO.getColaboradorVO().getCodigo());

        }

        AcessoClienteVO acessoSaida = null;
        if (registrarSaida) {
            acessoSaida = acessoClienteDao.registrarAcessoCliente(
                    dataGravarSaida, clienteVO, SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO, DirecaoAcessoEnum.DA_SAIDA, localAcessoVO, coletorVO,
                    usuarioVO, MeioIdentificacaoEnum.AVULSO, controleCreditoTreinoDao, request.getParameter("key"));
        }

        AcessoClienteVO ultimoAcesso = acessoClienteDao.consultarUltimoAcesso(clienteVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        if (!UteisValidacao.emptyNumber(ultimoAcesso.getCodigo())) {
            clienteDao.registrarUltimoAcesso(clienteVO.getCodigo(), ultimoAcesso.getCodigo());
        }

        TreinoWSConsumer.atualizarStatusAluno(request.getParameter("key"), clienteVO.getCodigo(), dataGravarEntrada, localAcessoVO.getEmpresa().getCodigo());
        situacaoClienteSinteticoDW.registrarUltimoAcesso(clienteVO.getCodigo(), dataGravarEntrada);
        if (empresaDao.integracaoMyWellnesHabilitada(localAcessoVO.getEmpresa().getCodigo(), false)) {
            zwFacadeDao.startThreadMyWellness(clienteVO.getCodigo(), null, true, null);
        }
        zwFacadeDao.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_DADOSACESSO, false);

        if (registrarSaida) {
            situacaoClienteSinteticoDW.registrarUltimoAcessoSaida(clienteVO.getCodigo(), dataGravarSaida);
        }


        //REGISTRAR LOG INCLUSAO
        try {
            LogVO log = new LogVO();
            log.setChavePrimaria(clienteVO.getPessoa().getCodigo().toString());
            log.setNomeEntidade("CLIENTE - ACESSO MANUAL");
            log.setNomeEntidadeDescricao("Cliente - Acesso Manual");
            log.setOperacao("INCLUIR - REGISTRO ACESSO MANUAL");
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setNomeCampo("AcessoManual-Cliente");
            log.setValorCampoAnterior("");
            StringBuilder campoAlterado = new StringBuilder();
            if (acessoEntrada != null && acessoEntrada.getCodigo() != null) {
                campoAlterado.append("CÓDIGO ACESSO: " + acessoEntrada.getCodigo()).append(" \n");
            }
            if (acessoEntrada != null && acessoEntrada.getDataHoraEntrada() != null) {
                campoAlterado.append("ENTRADA: " + Uteis.getDataAplicandoFormatacao(acessoEntrada.getDataHoraEntrada(), "dd/MM HH:mm:ss")).append(" \n");
            }
            if (acessoSaida != null && acessoSaida.getDataHoraSaida() != null) {
                campoAlterado.append("SAÍDA: " + Uteis.getDataAplicandoFormatacao(acessoSaida.getDataHoraSaida(), "dd/MM HH:mm:ss")).append(" \n");
            }
            log.setValorCampoAlterado(campoAlterado.toString());
            log.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            registrarLogObjetoVO(log, clienteVO.getPessoa().getCodigo(), logDao);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CLIENTE - ACESSO MANUAL", clienteVO.getPessoa().getCodigo(), "ERRO AO REGISTRAR LOG ACESSO MANUAL", usuarioVO.getNome(), usuarioVO.getUserOamd(), con);
            e.printStackTrace();
        }

        return EnvelopeRespostaDTO.of("Último acesso gravado para MAT " + clienteVO.getMatricula() + " - " + clienteVO.getNome_Apresentar() + " - " + Calendario.getDataAplicandoFormatacao(acessoEntrada.getDataHoraEntrada(), "dd/MM/yyyy HH:mm"));
    }

    private void registrarLogObjetoVO(LogVO logVO, int codPessoa, Log logDao) throws Exception {
        if (logVO != null) {
            logVO.setPessoa(codPessoa);
            logDao.incluirSemCommit(logVO);
        }
    }

    private void registrarLogErroObjetoVO(String nomeEntidade, int codPessoa, String msg, String responsavel, String userOamd, Connection con) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior(msg);
            log.setValorCampoAlterado(msg);
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            logDAO.incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            logDAO = null;
        }
    }

    private void validarSalvarRegistroAcessoManual(JSONObject jsonObject) throws Exception {
        Integer codCliente = jsonObject.getInt("codCliente");
        Date dataAcesso = jsonObject.has("dataAcesso") ? Calendario.getDate("yyyy-MM-dd", jsonObject.getString("dataAcesso")) : null;
        String horaEntrada = jsonObject.getString("horaEntrada");
        boolean registrarSaida = jsonObject.getBoolean("registrarSaida");
        Integer codLocalAcesso = jsonObject.getInt("codLocalAcesso");
        Integer codColetor = jsonObject.getInt("codColetor");
        if (UteisValidacao.emptyNumber(codCliente)) {
            throw new Exception("Nenhum aluno selecionado");
        }

        if (dataAcesso == null) {
            throw new Exception("Informe a data do acesso");
        }

        if (UteisValidacao.emptyString(horaEntrada)) {
            throw new Exception("Informe a hora da entrada");
        }

        if (registrarSaida) {
            String horaSaida = jsonObject.getString("horaSaida");
            if (UteisValidacao.emptyString(horaSaida)) {
                throw new Exception("Informe a hora da saída");
            }

            String[] saida = horaSaida.split(":");
            if (Integer.parseInt(saida[0]) > 24) {
                throw new Exception("Hora da saída inválida");
            }
            if (Integer.parseInt(saida[1]) > 59) {
                throw new Exception("Minutos da saída inválida");
            }
        }

        if (UteisValidacao.emptyNumber(codLocalAcesso)) {
            throw new Exception("Nenhum local de acesso selecionado");
        }

        if (UteisValidacao.emptyNumber(codColetor)) {
            throw new Exception("Nenhum coletor selecionado");
        }

        String[] entrada = horaEntrada.split(":");
        if (Integer.parseInt(entrada[0]) > 24) {
            throw new Exception("Hora da entrada inválida");
        }
        if (Integer.parseInt(entrada[1]) > 59) {
            throw new Exception("Minutos da entrada inválida");
        }

    }

    private EnvelopeRespostaDTO obterDocumentosContrato(ServletRequest request, Connection con) throws Exception {
        ContratoAssinaturaDigital contratoAssinaturaDigitalDAO;
        try {
            contratoAssinaturaDigitalDAO = new ContratoAssinaturaDigital(con);

            Integer codContrato = Uteis.converterInteiro(request.getParameter("contrato"));
            if (UteisValidacao.emptyNumber(codContrato)) {
                throw new Exception("Contrato não informado");
            }
            ContratoAssinaturaDigitalVO docs = contratoAssinaturaDigitalDAO.consultarPorContrato(codContrato);
            return EnvelopeRespostaDTO.of(new ContratoAssinaturaDigitalDTO(docs));
        } finally {
            contratoAssinaturaDigitalDAO = null;
        }
    }

    private EnvelopeRespostaDTO imprimirContratoOperacao(HttpServletRequest request, Connection con) throws Exception {
        Pessoa pessoaDAO;
        ContratoOperacao contratoOperacaoDAO;
        Contrato contratoDAO;
        Empresa empresaDAO;
        try {
            pessoaDAO = new Pessoa(con);
            contratoOperacaoDAO = new ContratoOperacao(con);
            contratoDAO = new Contrato(con);
            empresaDAO = new Empresa(con);

            Integer codContratoOperacao = Uteis.converterInteiro(request.getParameter("contratoOperacao"));
            if (UteisValidacao.emptyNumber(codContratoOperacao)) {
                throw new Exception("Contrato Operação não informado");
            }

            ContratoOperacaoVO contratoOperacaoVO = contratoOperacaoDAO.consultarPorChavePrimaria(codContratoOperacao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(contratoOperacaoVO.getContrato(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            try {
                contratoOperacaoVO.setAssinaturaDigitalBiometria(pessoaDAO.obterAssinaturaBiometriaDigital(contratoVO.getPessoa().getCodigo()));
            } catch (Exception ignored) {
            }
            String chave = obterChave(request);
            Conexao.guardarConexaoForJ2SE(chave, con);
            String nomeArquivo = new SuperControleRelatorio().imprimirComprovanteOperacao(contratoOperacaoVO, empresaVO, request);
            return EnvelopeRespostaDTO.of(getUrlAplicacaoPastaRelatorio(chave, request) + nomeArquivo);
        } finally {
            pessoaDAO = null;
            contratoOperacaoDAO = null;
            contratoDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO estornarContratoOperacao(HttpServletRequest request, Connection con) throws Exception {
        ContratoOperacao contratoOperacaoDAO;
        Contrato contratoDAO;
        Cliente clienteDAO;
        Usuario usuarioDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            contratoOperacaoDAO = new ContratoOperacao(con);
            contratoDAO = new Contrato(con);
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            zillyonWebFacade = new ZillyonWebFacade(con);

            JSONObject json = new JSONObject(obterBody(request));
            Integer codContratoOperacao = json.getInt("contratoOperacao");
            Integer codUsuario = json.getInt("usuario");

            if (UteisValidacao.emptyNumber(codContratoOperacao)) {
                throw new Exception("Contrato Operação não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            ContratoOperacaoVO contratoOperacaoVO = contratoOperacaoDAO.consultarPorChavePrimaria(codContratoOperacao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(contratoOperacaoVO.getContrato(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (contratoVO.getSituacao().equals("AT") || contratoVO.getSituacao().equals("TR")) {
                if (!contratoOperacaoVO.getTipoOperacao().equals("AH") && !contratoOperacaoVO.getTipoOperacao().equals("BC") && !contratoOperacaoVO.getTipoOperacao().equals("TV") && !contratoOperacaoVO.getTipoOperacao().equals("RT")) {
                    if (!(((contratoOperacaoVO.getTipoOperacao().equals("CR")
                            || contratoOperacaoVO.getTipoOperacao().equals("AT")
                            || contratoOperacaoVO.getTipoOperacao().equals("BA")
                            || contratoOperacaoVO.getTipoOperacao().equals("BR")
                            || contratoOperacaoVO.getTipoOperacao().equals("TR")) && contratoVO.getSituacao().equals("AT"))
                            || (contratoOperacaoVO.getTipoOperacao().equals("TR") && contratoVO.getSituacao().equals("TR")))) {
                        throw new Exception("Não é possível estornar operação essa operação.");
                    }
                }
            } else if (contratoVO.getSituacao().equals("IN")) {
                if (!((contratoOperacaoVO.getTipoOperacao().equals("BA") || contratoOperacaoVO.getTipoOperacao().equals("AT")) && Calendario.maior(contratoOperacaoVO.getDataOperacao(), contratoVO.getVigenciaAteAjustada()))) {
                    throw new Exception("Não é possível estornar operação essa operação.");
                }
            }

            if (contratoVO.isVendaCreditoTreino()) {
                throw new Exception("Não é possível estornar operação de planos que são de crédito, pois houveram aulas desmarcadas automaticamente oriundas desta operação e ela não pode ser desfeita.");
            }

            String chave = obterChave(request);
            Conexao.guardarConexaoForJ2SE(chave, con);
            contratoVO = contratoOperacaoDAO.estornarContratoOperacao(contratoOperacaoVO, usuarioVO);
            zillyonWebFacade.atualizarSintetico(clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);

            return EnvelopeRespostaDTO.of("Estorno de " + contratoOperacaoVO.getTipoOperacao() + " realizado com sucesso.");
        } finally {
            contratoOperacaoDAO = null;
            contratoDAO = null;
            clienteDAO = null;
            usuarioDAO = null;
            zillyonWebFacade = null;
        }
    }

    private EnvelopeRespostaDTO desmarcarHorario(HttpServletRequest request, Connection con) throws Exception {
        HorarioTurma horarioTurmaDAO;
        Contrato contratoDAO;
        Cliente clienteDAO;
        Usuario usuarioDAO;
        Turma turmaDAO;
        Empresa empresaDAO;
        try {
            horarioTurmaDAO = new HorarioTurma(con);
            contratoDAO = new Contrato(con);
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            turmaDAO = new Turma(con);
            empresaDAO = new Empresa(con);

            JSONObject json = new JSONObject(obterBody(request));
            Integer codHorarioTurma = json.getInt("horarioTurma");
            Integer codCliente = json.getInt("cliente");
            Integer codContrato = json.getInt("contrato");
            Integer codUsuario = json.getInt("usuario");
            Integer codTurmaDestino = json.optInt("codTurmaDestino");

            if (UteisValidacao.emptyNumber(codHorarioTurma)) {
                throw new Exception("Horário Turma não informado");
            }
            if (UteisValidacao.emptyNumber(codCliente)) {
                throw new Exception("Cliente não informado");
            }
            if (UteisValidacao.emptyNumber(codContrato)) {
                throw new Exception("Contrato não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            String chave = obterChave(request);
            HorarioTurmaVO horarioTurma = horarioTurmaDAO.consultarPorChavePrimaria(codHorarioTurma, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Date dataAulaDesmarcar = json.has("dataDesmarcar") ?
                    Calendario.getDate("yyyy-MM-dd", json.getString("dataDesmarcar")) :
                    Calendario.proximoDiaSemana(horarioTurma.getDiaSemanaNumero(), Calendario.hoje());

            AulaDesmarcadaVO aulaDesmarcadaVO = new AulaDesmarcadaVO();
            aulaDesmarcadaVO.setClienteVO(clienteVO);
            aulaDesmarcadaVO.setContratoVO(contratoVO);
            aulaDesmarcadaVO.setEmpresaVO(clienteVO.getEmpresa());
            aulaDesmarcadaVO.setTurmaVO(turmaDAO.consultarPorChavePrimaria(horarioTurma.getTurma(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            aulaDesmarcadaVO.setHorarioTurmaVO(horarioTurma);
            aulaDesmarcadaVO.setDataOrigem(dataAulaDesmarcar);
            aulaDesmarcadaVO.setUsuarioVO(usuarioVO);
            if (UteisValidacao.emptyNumber(codTurmaDestino)) {
                aulaDesmarcadaVO.setTurmaDestino(codTurmaDestino);
            }

            if (Calendario.menor(aulaDesmarcadaVO.getDataOrigem(), aulaDesmarcadaVO.getContratoVO().getVigenciaDe())) {
                throw new Exception("Contrato não está vigente na data da aula desmarca");
            }

            final GestaoAulaService gestaoAulaService = new GestaoAulaService(con, chave);
            boolean naoPedirPermissao = false;
            String mensagemUsuario = "Você precisa da permissão \"2.64 - Permitir desmarcar aula fora da tolerância da turma\"";
            try {
                gestaoAulaService.validarMinutosAntecedenciaDesmarcarAula(aulaDesmarcadaVO.getTurmaVO(), aulaDesmarcadaVO.getHorarioTurmaVO(), aulaDesmarcadaVO.getDataOrigem());
                naoPedirPermissao = true;
            } catch (ConsistirException toleranciaExc) {
                mensagemUsuario = "Você está tentando desmarcar uma aula fora do período de tolerância, sendo assim, você precisa da permissão \"2.64 - Permitir desmarcar aula fora da tolerância da turma\"";
            } catch (Exception ignored) {
            }

            try {
                validarPermissao(empresaVO, "DesmarcarAulaForaTolerancia", "2.64 - Permitir desmarcar aula fora da tolerância da turma", usuarioVO, con);
            } catch (Exception ex) {
                throw new Exception(mensagemUsuario);
            }

            final boolean pedirPermissao = !naoPedirPermissao;
            gestaoAulaService.desmarcarAula(aulaDesmarcadaVO, pedirPermissao);
            return EnvelopeRespostaDTO.of("Aula desmarcada com sucesso!");
        } finally {
            horarioTurmaDAO = null;
            contratoDAO = null;
            clienteDAO = null;
            usuarioDAO = null;
            turmaDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO salvarImagens(HttpServletRequest request, Connection con) throws Exception {
        Usuario usuarioDAO;
        ContratoAssinaturaDigitalServiceInterface cadServiceContrato;
        try {
            usuarioDAO = new Usuario(con);
            cadServiceContrato = new ContratoAssinaturaDigitalServiceImpl(con);

            JSONObject json = new JSONObject(obterBody(request));

            Integer codUsuario = json.getInt("usuario");
            Integer codContrato = json.getInt("contrato");

            String token = json.optString("token");

            String imgDocs = json.optString("documentos");
            String imgEndereco = json.optString("endereco");
            String imgAtestado = json.optString("atestado");
            String imgAnexo1 = json.optString("anexo1");
            String imgAnexo2 = json.optString("anexo2");

            boolean docUpdate = json.getBoolean("documentosUpdate");
            boolean endUpdate = json.getBoolean("enderecoUpdate");
            boolean ateUpdate = json.getBoolean("atestadoUpdate");
            boolean anex1Update = json.getBoolean("anexo1Update");
            boolean anex2Update = json.getBoolean("anexo2Update");

            imgDocs = imgDocs.contains("image_icon.jpg") ? "" : imgDocs;
            imgEndereco = imgEndereco.contains("image_icon.jpg") ? "" : imgEndereco;
            imgAtestado = imgAtestado.contains("image_icon.jpg") ? "" : imgAtestado;
            imgAnexo1 = imgAnexo1.contains("image_icon.jpg") ? "" : imgAnexo1;
            imgAnexo2 = imgAnexo2.contains("image_icon.jpg") ? "" : imgAnexo2;

            if (UteisValidacao.emptyNumber(codContrato)) {
                throw new Exception("Contrato não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            cadServiceContrato.salvarImagens(token, codContrato, usuarioVO,
                    imgDocs, docUpdate, imgEndereco, endUpdate, imgAtestado, ateUpdate,
                    imgAnexo1, anex1Update, imgAnexo2, anex2Update);
            return EnvelopeRespostaDTO.of("Sucesso");
        } finally {
            usuarioDAO = null;
            cadServiceContrato = null;
        }
    }

    private EnvelopeRespostaDTO enviarEmailCancelamentoContrato(HttpServletRequest request, Connection con) throws Exception {
        ContratoOperacao contratoOperacaoDAO;
        Contrato contratoDAO;
        Empresa empresaDAO;
        Email emailDAO;
        Cliente clienteDAO;
        ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO;
        try {
            contratoOperacaoDAO = new ContratoOperacao(con);
            contratoDAO = new Contrato(con);
            empresaDAO = new Empresa(con);
            emailDAO = new Email(con);
            clienteDAO = new Cliente(con);
            configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(con);

            JSONObject json = new JSONObject(obterBody(request));
            Integer codContratoOperacao = json.getInt("contratoOperacao");
            JSONArray jsonEmails = json.getJSONArray("emails");
            List<EmailVO> emailsEnviar = new ArrayList<>();

            if (jsonEmails != null) {
                for (int i = 0; i < jsonEmails.length(); i++) {
                    String email = jsonEmails.getString(i);
                    if (UteisValidacao.emptyString(email)) {
                        continue;
                    }
                    EmailVO emailVO = new EmailVO();
                    emailVO.setEmail(email);
                    emailsEnviar.add(emailVO);
                }
            }

            if (UteisValidacao.emptyNumber(codContratoOperacao)) {
                throw new Exception("Contrato Operação não informado");
            }

            ContratoOperacaoVO contratoOperacaoVO = contratoOperacaoDAO.consultarPorChavePrimaria(codContratoOperacao, Uteis.NIVELMONTARDADOS_TODOS);
            if (!contratoOperacaoVO.getTipoOperacao().equalsIgnoreCase(TipoOperacaoContratoEnum.CANCELAMENTO.getSigla())) {
                throw new Exception("Contrato operação não é de cancelamento");
            }

            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(contratoOperacaoVO.getContrato(), Uteis.NIVELMONTARDADOS_TIPOPESSOA);
            contratoVO.setEmpresa(empresaDAO.consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
            contratoOperacaoVO.setNome(contratoVO.getNome_Apresentar());

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMEnvio = getConfiguracaoSMTPNoReply();
            if (UteisValidacao.emptyString(configuracaoSistemaCRMEnvio.getMailServer())) {
                throw new Exception("Não foi possível enviar o e-mail. Verifique as configurações de e-mail no CRM!");
            }

            UteisEmail uteisEmail = new UteisEmail();
            String assuntoEmail = "Cancelamento Contrato " + contratoOperacaoVO.getContrato() + " - " + contratoVO.getEmpresa().getNome();
            uteisEmail.novo(assuntoEmail, configuracaoSistemaCRMEnvio);

            //REMETENTE
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_TODOS);
            uteisEmail.setRemetente(configuracaoSistemaCRM.getRemetentePadraoMailing());
            String mensagemEmailAux = clienteDAO.montarEmailCancelamento(contratoOperacaoVO, contratoVO.getEmpresa(), "");

            if (UteisValidacao.emptyList(emailsEnviar)) {
                emailsEnviar = emailDAO.consultarEmails(contratoVO.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (UteisValidacao.emptyList(emailsEnviar)) {
                throw new Exception("Cliente não possui e-mail cadastrado.");
            }

            String[] emails = new String[emailsEnviar.size()];
            int i = 0;
            for (EmailVO emailVO : emailsEnviar) {
                emails[i] = emailVO.getEmail();
                i++;
            }
            uteisEmail.enviarEmailN(emails, mensagemEmailAux, assuntoEmail, contratoVO.getEmpresa().getNome());
            return EnvelopeRespostaDTO.of("E-mail enviado com sucesso.");
        } finally {
            contratoOperacaoDAO = null;
            contratoDAO = null;
            empresaDAO = null;
            emailDAO = null;
            clienteDAO = null;
            configuracaoSistemaCRMDAO = null;
        }
    }

    private EnvelopeRespostaDTO desfazerCancelamento(HttpServletRequest request, Connection con) throws Exception {
        ContratoOperacao contratoOperacaoDAO;
        Contrato contratoDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            contratoOperacaoDAO = new ContratoOperacao(con);
            contratoDAO = new Contrato(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            zillyonWebFacade = new ZillyonWebFacade(con);

            JSONObject json = new JSONObject(obterBody(request));
            Integer codContratoOperacao = json.getInt("contratoOperacao");
            Integer codUsuario = json.getInt("usuario");

            if (UteisValidacao.emptyNumber(codContratoOperacao)) {
                throw new Exception("Contrato Operação não informado");
            }

            ContratoOperacaoVO contratoOperacaoVO = contratoOperacaoDAO.consultarPorChavePrimaria(codContratoOperacao, Uteis.NIVELMONTARDADOS_TODOS);
            if (!(Calendario.menor(Calendario.hoje(), contratoOperacaoVO.getDataInicioEfetivacaoOperacao()))) {
                throw new Exception("Não foi possível desfazer o cancelamento");
            }
            if (UteisValidacao.emptyString(contratoOperacaoVO.getInformacoesDesfazer())) {
                throw new ConsistirException("Este contrato não foi cancelado pelo método de \"Cancelamento verificando próxima parcela em aberto\", portanto, não é possível desfazer essa operação.");
            }

            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(contratoOperacaoVO.getContrato(), Uteis.NIVELMONTARDADOS_TODOS);

            if (!contratoVO.getContratoRecorrenciaVO().isCancelamentoProporcional()) {
                throw new Exception("Não foi possível desfazer o cancelamento");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            contratoVO.setEmpresa(empresaDAO.consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
            contratoOperacaoVO.setNome(contratoVO.getNome_Apresentar());

            zillyonWebFacade.desfazerCancelamentoProporcional(contratoOperacaoVO, usuarioVO);

            return EnvelopeRespostaDTO.of("Cancelamento revertido com sucesso!");
        } finally {
            contratoOperacaoDAO = null;
            contratoDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            zillyonWebFacade = null;
        }
    }

    private EnvelopeRespostaDTO aulasContrato(HttpServletRequest request, Connection con) throws Exception {
        Contrato contratoDAO;
        AulaDesmarcada aulaDesmarcadaDAO;
        Reposicao reposicaoDAO;
        try {
            contratoDAO = new Contrato(con);
            aulaDesmarcadaDAO = new AulaDesmarcada(con);
            reposicaoDAO = new Reposicao(con);

            String chave = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer codContrato = json.getInt("contrato");
            String tipoConsulta = json.getString("tipo");

            if (UteisValidacao.emptyNumber(codContrato)) {
                throw new Exception("Contrato não informado");
            }

            Conexao.guardarConexaoForJ2SE(chave, con);

            List<Object> listaGeral = new ArrayList<>();

            if (tipoConsulta.equalsIgnoreCase("reposicoes")) {
                Integer codTurma = json.getInt("turma");

                List<ReposicaoVO> lista = reposicaoDAO.consultarReposicoes(codContrato.toString(), codTurma.toString(), "", false, null, null, true);
                for (ReposicaoVO obj : lista) {
                    listaGeral.add(new AulaAgendaTO(obj));
                }

            } else if (tipoConsulta.equalsIgnoreCase("faltas")) {
                Integer codContratoModalidadeTurma = json.getInt("contratoModalidadeTurma");

                ContratoVO contratoVO = contratoDAO.consultarContratoDetalhe(codContrato);
                for (ContratoModalidadeVO contratoModalidadeVO : contratoVO.getContratoModalidadeVOs()) {
                    for (Object obj : contratoModalidadeVO.getContratoModalidadeTurmaVOs()) {
                        ContratoModalidadeTurmaVO contratoModalidadeTurmaVO = (ContratoModalidadeTurmaVO) obj;
                        if (contratoModalidadeTurmaVO.getCodigo().equals(codContratoModalidadeTurma)) {
                            listaGeral.addAll(contratoModalidadeTurmaVO.getTurma().getListaDeFaltas());
                        }
                    }
                }
            } else if (tipoConsulta.equalsIgnoreCase("desmarcadas")) {
                Integer codTurma = json.getInt("turma");

                List<AulaDesmarcadaVO> lista = aulaDesmarcadaDAO.consultarListaAulasDesmarcadas(codContrato, 0, codTurma, Uteis.NIVELMONTARDADOS_TODOS);
                for (AulaDesmarcadaVO obj : lista) {
                    listaGeral.add(new AulaAgendaTO(obj));
                }
            } else if (tipoConsulta.equalsIgnoreCase("desmarcadasContratoPassado")) {
                Integer codContratoPassado = json.getInt("codContratoPassado");
                Integer codEmpresaLogada = json.getInt("codEmpresaLogada");

                List<AulaDesmarcadaVO> lista = aulaDesmarcadaDAO.consultarListaAulasDesmarcadasContratoPassado(codContrato, codEmpresaLogada, codContratoPassado, Uteis.NIVELMONTARDADOS_TODOS);
                for (AulaDesmarcadaVO obj : lista) {
                    listaGeral.add(new AulaAgendaTO(obj));
                }
            } else {
                throw new Exception("Nenhuma operação realizada");
            }

            Integer totalItens = listaGeral.size();
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            if (paginadorDTO != null
                    && paginadorDTO.getPage() != null
                    && paginadorDTO.getSize() != null) {
                int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                if (ultimoRegistro > listaGeral.size()) {
                    listaGeral = listaGeral.subList(primeiroPaginacao, listaGeral.size());
                } else {
                    listaGeral = listaGeral.subList(primeiroPaginacao, ultimoRegistro);
                }
            }

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos((long) totalItens);
            }

            return EnvelopeRespostaDTO.of(listaGeral, paginadorDTO);
        } finally {
            contratoDAO = null;
            aulaDesmarcadaDAO = null;
            reposicaoDAO = null;
        }
    }

    private EnvelopeRespostaDTO alterarDadosContrato(HttpServletRequest request, Connection con) throws Exception {
        Contrato contratoDAO;
        Colaborador colaboradorDAO;
        Usuario usuarioDAO;
        Cliente clienteDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            contratoDAO = new Contrato(con);
            usuarioDAO = new Usuario(con);
            clienteDAO = new Cliente(con);
            colaboradorDAO = new Colaborador(con);
            zillyonWebFacade = new ZillyonWebFacade(con);

            String chave = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer codContrato = json.optInt("contrato");
            Integer codUsuario = json.optInt("usuario");
            Integer codConsultor = json.optInt("consultor");
            TipoContratoEnum tipoContratoEnum = TipoContratoEnum.getTipo(json.optInt("tipoContrato"));
            Boolean renovacaoAutomatica = json.has("renovacaoAutomatica") ? json.getBoolean("renovacaoAutomatica") : null;

            if (UteisValidacao.emptyNumber(codContrato)) {
                throw new Exception("Contrato não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            Conexao.guardarConexaoForJ2SE(chave, con);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_TODOS);
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoVO.setObjetoVOAntesAlteracao(contratoVO);
            ColaboradorDTO colaboradorDTO = new ColaboradorDTO();
            ContratoDTO contratoDTO = new ContratoDTO();

            //alterou o consultor
            if (!UteisValidacao.emptyNumber(codConsultor) &&
                    !contratoVO.getConsultor().getCodigo().equals(codConsultor)) {

                ColaboradorVO colaboradorVO = colaboradorDAO.consultarPorChavePrimaria(codConsultor, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoVO.setConsultor(colaboradorVO);

                colaboradorDTO.setCodigo(colaboradorVO.getCodigo());
                PessoaDTO pessoaDTO = new PessoaDTO(colaboradorVO.getPessoa().getCodigo(), colaboradorVO.getPessoa().getNome());
                colaboradorDTO.setPessoa(pessoaDTO);
                contratoDTO.setConsultorResponsavelDTO(colaboradorDTO);

                zillyonWebFacade.notificarRecursoSistema(chave, RecursoSistema.CONSULTOR_RESPONSAVEL_CONTRATO_CLIENTE, usuarioVO, contratoVO.getEmpresa());
                contratoDAO.alterarConsultorContratoGeral(contratoVO, clienteVO, usuarioVO);
            }

            //alterou o tipo do contrato
            if (tipoContratoEnum != null &&
                    !contratoVO.getContratoAgendadoEspontaneo().equals(tipoContratoEnum)) {

                TipoContratoEnum valorAntesAlteracao = contratoVO.getContratoAgendadoEspontaneo();

                contratoVO.setContratoAgendadoEspontaneo(tipoContratoEnum);

                if (contratoVO.getContratoAgendadoEspontaneo().equals(TipoContratoEnum.AGENDADO)) {
                    zillyonWebFacade.notificarRecursoSistema(chave, RecursoSistema.ALTERAR_TIPO_CONTRATO_AGENDADO_CLIENTE, usuarioVO, contratoVO.getEmpresa());
                } else if (contratoVO.getContratoAgendadoEspontaneo().equals(TipoContratoEnum.ESPONTANEO)) {
                    zillyonWebFacade.notificarRecursoSistema(chave, RecursoSistema.ALTERAR_TIPO_CONTRATO_EXPONTANEO_CLIENTE, usuarioVO, contratoVO.getEmpresa());
                }
                contratoDAO.alteraTipoContrato(contratoVO, valorAntesAlteracao, usuarioVO);
                contratoDTO.setTipoContrato(contratoVO.getContratoAgendadoEspontaneo().getCodigo());
            }

            //alterou renovação automática
            if (renovacaoAutomatica != null &&
                    !contratoVO.getPermiteRenovacaoAutomatica().equals(renovacaoAutomatica)) {
                zillyonWebFacade.notificarRecursoSistema(chave, RecursoSistema.RENOVAVEL_AUTOMATICAMENTE_CLIENTE, usuarioVO, contratoVO.getEmpresa());
                contratoDTO.setPermiteRenovacaoAutomatica(renovacaoAutomatica);
                contratoDAO.alterarPermiteRenovacaoAutomatica(contratoVO, renovacaoAutomatica, usuarioVO, clienteVO);
            }


            return EnvelopeRespostaDTO.of(contratoDTO);
        } finally {
            contratoDAO = null;
            colaboradorDAO = null;
            usuarioDAO = null;
            clienteDAO = null;
            zillyonWebFacade = null;
        }
    }

    private EnvelopeRespostaDTO operacaoAula(HttpServletRequest request, Connection con) throws Exception {
        Pessoa pessoaDAO;
        AulaDesmarcada aulaDesmarcadaDAO;
        Reposicao reposicaoDAO;
        Usuario usuarioDAO;
        Cliente clienteDAO;
        GestaoAulaService gestaoAulaService;
        try {
            aulaDesmarcadaDAO = new AulaDesmarcada(con);
            reposicaoDAO = new Reposicao(con);
            usuarioDAO = new Usuario(con);
            pessoaDAO = new Pessoa(con);
            clienteDAO = new Cliente(con);

            String chave = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer codUsuario = json.getInt("usuario");
            String tipoConsulta = json.getString("tipo");

            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            Conexao.guardarConexaoForJ2SE(chave, con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (tipoConsulta.equalsIgnoreCase("excluirAulaDesmarcada")) {

                Integer codAulaDesmarcada = json.getInt("aulaDesmarcada");
                AulaDesmarcadaVO aulaDesmarcadaVO = aulaDesmarcadaDAO.consultarPorCodigo(codAulaDesmarcada, Uteis.NIVELMONTARDADOS_TODOS);
                ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(aulaDesmarcadaVO.getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);

                gestaoAulaService = new GestaoAulaService(con, chave);
                aulaDesmarcadaVO.getClienteVO().setPessoa(pessoaDAO.consultarPorChavePrimaria(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                aulaDesmarcadaVO.setUsuarioVO(usuarioVO);
                gestaoAulaService.excluirAulaDesmarcada(aulaDesmarcadaVO, "");
                return EnvelopeRespostaDTO.of("A aula desmarcada foi excluída com sucesso!");

            } else if (tipoConsulta.equalsIgnoreCase("excluirReposicao")) {
                Integer codReposicao = json.getInt("reposicao");
                ReposicaoVO reposicaoVO = reposicaoDAO.consultarPorChavePrimaria(codReposicao, Uteis.NIVELMONTARDADOS_TODOS);

                gestaoAulaService = new GestaoAulaService(con, chave);
                reposicaoVO.getCliente().setPessoa(pessoaDAO.consultarPorChavePrimaria(reposicaoVO.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                reposicaoVO.setUsuario(usuarioVO);

                boolean ignorarAntecedencia = true;
                try {
                    validarPermissao(reposicaoVO.getCliente().getEmpresa(), "DesmarcarAulaForaTolerancia", " 2.64 - Permitir desmarcar aula fora da tolerância da turma ", usuarioVO, con);
                } catch (Exception e) {
                    ignorarAntecedencia = false;
                }
                gestaoAulaService.desmarcarReposicao(reposicaoVO, ignorarAntecedencia);

                String email = reposicaoDAO.enviarEmail(reposicaoVO, true);
                String sms = reposicaoDAO.enviarSMS(reposicaoVO, true);

                String msg = "";
                if (sms.equalsIgnoreCase("ok") && email.equals("ok")) {
                    msg = "Email e SMS enviados com sucesso!";
                } else if (sms.equalsIgnoreCase("ok")) {
                    msg = "SMS enviado com sucesso!";
                } else if (email.equalsIgnoreCase("ok")) {
                    msg = "Email enviado com sucesso!";
                }
                return EnvelopeRespostaDTO.of("Reposição excluída com sucesso! " + msg);

            } else if (tipoConsulta.equalsIgnoreCase("enviarReposicaoEmailSMS")) {
                Integer codReposicao = json.getInt("reposicao");
                ReposicaoVO reposicaoVO = reposicaoDAO.consultarPorChavePrimaria(codReposicao, Uteis.NIVELMONTARDADOS_TODOS);
                reposicaoVO.getCliente().setPessoa(pessoaDAO.consultarPorChavePrimaria(reposicaoVO.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                String email = reposicaoDAO.enviarEmail(reposicaoVO, false);
                String sms = reposicaoDAO.enviarSMS(reposicaoVO, false);
                if (sms.equalsIgnoreCase("ok") && email.equals("ok")) {
                    return EnvelopeRespostaDTO.of("Email e SMS enviados com sucesso!");
                } else if (sms.equalsIgnoreCase("ok")) {
                    return EnvelopeRespostaDTO.of("SMS enviado com sucesso!");
                } else if (email.equalsIgnoreCase("ok")) {
                    return EnvelopeRespostaDTO.of("Email enviado com sucesso!");
                } else {
                    throw new Exception("Email e/ou SMS não enviados");
                }
            } else {
                throw new Exception("Nenhuma operação realizada");
            }
        } finally {
            pessoaDAO = null;
            aulaDesmarcadaDAO = null;
            reposicaoDAO = null;
            usuarioDAO = null;
            clienteDAO = null;
            gestaoAulaService = null;
        }
    }

    private EnvelopeRespostaDTO opcoesContratoCredito(HttpServletRequest request, Connection con) throws Exception {
        Usuario usuarioDAO;
        Cliente clienteDAO;
        Contrato contratoAO;
        ControleCreditoTreino controleCreditoTreinoDAO;
        SituacaoClienteSinteticoDW situacaoClienteSinteticoDWDAO;
        try {
            usuarioDAO = new Usuario(con);
            clienteDAO = new Cliente(con);
            controleCreditoTreinoDAO = new ControleCreditoTreino(con);
            contratoAO = new Contrato(con);
            situacaoClienteSinteticoDWDAO = new SituacaoClienteSinteticoDW(con);

            String chave = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer codContrato = json.getInt("contrato");
            Integer codUsuario = json.getInt("usuario");
            String tipoConsulta = json.getString("tipo");

            if (UteisValidacao.emptyNumber(codContrato)) {
                throw new Exception("Contrato não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            Conexao.guardarConexaoForJ2SE(chave, con);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoVO contratoVO = contratoAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (!contratoVO.isVendaCreditoTreino()) {
                throw new Exception("Contrato não é de crédito");
            }

            if (tipoConsulta.equalsIgnoreCase("extrato")) {

                contratoVO.setListaControleCreditoTreino(controleCreditoTreinoDAO.consultar(contratoVO.getCodigo()));

                List<ExtratoCreditoTreinoTO> listaGeral = new ArrayList<>();
                for (ControleCreditoTreinoVO obj : contratoVO.getListaControleCreditoTreino()) {
                    listaGeral.add(new ExtratoCreditoTreinoTO(obj));
                }

                Integer totalItens = listaGeral.size();
                PaginadorDTO paginadorDTO = new PaginadorDTO(request);
                if (paginadorDTO != null
                        && paginadorDTO.getPage() != null
                        && paginadorDTO.getSize() != null) {
                    int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                    int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                    if (ultimoRegistro > listaGeral.size()) {
                        listaGeral = listaGeral.subList(primeiroPaginacao, listaGeral.size());
                    } else {
                        listaGeral = listaGeral.subList(primeiroPaginacao, ultimoRegistro);
                    }
                }

                if (paginadorDTO != null) {
                    paginadorDTO.setQuantidadeTotalElementos((long) totalItens);
                }
                return EnvelopeRespostaDTO.of(listaGeral, paginadorDTO);

            } else if (tipoConsulta.equalsIgnoreCase("novo")) {

                ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
                controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL);
                controleCreditoTreinoVO.setUsuarioVO(usuarioVO);
                controleCreditoTreinoVO.setContratoVO(contratoVO);
                controleCreditoTreinoDAO.incluir(controleCreditoTreinoVO, clienteVO.getCodigo(), situacaoClienteSinteticoDWDAO);
                situacaoClienteSinteticoDWDAO.atualizarBaseOffLineZillyonAcesso(chave, clienteVO.getPessoa().getCodigo());

                return EnvelopeRespostaDTO.of("Dados gravados com sucesso!");
            } else {
                throw new Exception("Nenhuma operação realizada");
            }
        } finally {
            usuarioDAO = null;
            clienteDAO = null;
            contratoAO = null;
            controleCreditoTreinoDAO = null;
            situacaoClienteSinteticoDWDAO = null;
        }
    }

    private EnvelopeRespostaDTO opcoesContrato(HttpServletRequest request, Connection con) throws Exception {
        Usuario usuarioDAO;
        Cliente clienteDAO;
        Contrato contratoDAO;
        Empresa empresaDAO;
        try {
            usuarioDAO = new Usuario(con);
            clienteDAO = new Cliente(con);
            contratoDAO = new Contrato(con);
            empresaDAO = new Empresa(con);

            String chave = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer codContrato = json.getInt("contrato");
            Integer codCliente = json.getInt("cliente");
            Integer codEmpresa = json.getInt("empresa");
            Integer codUsuario = json.getInt("usuario");
            String tipoConsulta = json.getString("tipo");

            if (UteisValidacao.emptyNumber(codContrato)) {
                throw new Exception("Contrato não informado");
            }
            if (UteisValidacao.emptyNumber(codCliente)) {
                throw new Exception("Cliente não informado");
            }
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                throw new Exception("Empresa não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            Conexao.guardarConexaoForJ2SE(chave, con);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_TODOS);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (tipoConsulta.equalsIgnoreCase("mapaMostrar")) {
                List<ContratoVO> listaContratosCliente = contratoDAO.consultarListaTela(clienteVO.getPessoa().getCodigo(), 0);
                Map<String, Boolean> mapaMostrar = contratoDAO.apresentarOperacaoNoMenuCliente(contratoVO, clienteVO, empresaVO, usuarioVO, listaContratosCliente);
                JSONObject jsonRet = new JSONObject();
                for (String key : mapaMostrar.keySet()) {
                    StringBuilder name = new StringBuilder();
                    for (String aaa : key.toLowerCase().split("_")) {
                        String output = aaa;
                        if (name.length() > 0) {
                            output = aaa.substring(0, 1).toUpperCase() + aaa.substring(1);
                        }
                        name.append(output);
                    }
                    jsonRet.put(name.toString(), mapaMostrar.get(key));
                }
                jsonRet.put("alterarVencimento", contratoVO.isRegimeRecorrencia());
                jsonRet.put("alterarPlano", contratoVO.isRegimeRecorrencia() && contratoVO.getSituacao().equalsIgnoreCase("AT"));
                return EnvelopeRespostaDTO.of(jsonRet);
            } else {
                throw new Exception("Nenhuma operação realizada");
            }
        } finally {
            usuarioDAO = null;
            clienteDAO = null;
            contratoDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO obterTabela(String tabela, Connection con,
                                            HttpServletRequest request) throws Exception {

        Integer codigo = UteisValidacao.converterInteiro(request.getParameter("codigo"));

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM \n");
        sql.append(tabela).append(" \n");
        if (!UteisValidacao.emptyNumber(codigo)) {
            sql.append("WHERE codigo = ").append(codigo).append(" \n");
        }
        sql.append(" ORDER BY codigo ");
        if (tabela.equals("configuracaosistema")) {
            sql.append(" limit 1 ");
        }

        JSONObject json = new JSONObject();
        JSONArray array = new JSONArray();
        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                final ResultSetMetaData meta = rs.getMetaData();
                final int columnCount = meta.getColumnCount();
                while (rs.next()) {
                    json = new JSONObject();
                    for (int column = 1; column <= columnCount; ++column) {
                        try {
                            final String nomeColuna = meta.getColumnName(column);
                            final Object value = rs.getObject(column);
                            json.put(nomeColuna.toLowerCase(), value);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                    array.put(json);
                }
            }
        }

        if (total > 1) {
            return EnvelopeRespostaDTO.of(array);
        } else {
            return EnvelopeRespostaDTO.of(json);
        }
    }

    private EnvelopeRespostaDTO obterConfiguracaoEmpresa(Connection con, HttpServletRequest request) throws Exception {
        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(con);

            Integer codEmpresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            if (!UteisValidacao.emptyNumber(codEmpresa)) {
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                return EnvelopeRespostaDTO.of(new EmpresaDTO(empresaVO));
            } else {
                List<EmpresaVO> empresaVOList = empresaDAO.consultarTodas(true, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

                List<EmpresaDTO> lista = new ArrayList<>();
                for (EmpresaVO empresaVO : empresaVOList) {
                    lista.add(new EmpresaDTO(empresaVO));
                }
                return EnvelopeRespostaDTO.of(lista);
            }
        } finally {
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO notificarIntegracaoFoguete(Connection con, HttpServletRequest request) throws Exception {

        try {
            Cliente clienteDAO = new Cliente(con);
            Contrato contratoDAO = new Contrato(con);
            ContratoPlanoProdutoSugerido contratoPlanoProdutoSugeridoDAO = new ContratoPlanoProdutoSugerido(con);
            HistoricoContrato historicoContratoDAO = new HistoricoContrato(con);
            Usuario usuarioDAO = new Usuario(con);

            JSONObject jsonObject = getJSONBody(request);
            String tipo = jsonObject.optString("tipo");

            if (tipo.equalsIgnoreCase("contratoAtivo")) {
                final Integer codCliente = jsonObject.optInt("cliente");
                final ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Integer codigoPessoaContrato = clienteVO.getPessoa().getCodigo();

                final Integer codUsuario = jsonObject.optInt("usuario");
                UsuarioVO usuarioVO = null;
                if (!UteisValidacao.emptyNumber(codUsuario)) {
                    usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }

                final boolean isSincronizacaoManual = jsonObject.optBoolean("sincronizacaoManual");

                if (clienteVO.isDependentePlanoCompartilhado()) {
                    final ClienteVO titularVO = clienteDAO.consultarPorChavePrimaria(clienteVO.getTitularPlanoCompartilhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    codigoPessoaContrato = titularVO.getPessoa().getCodigo();
                }

                final ContratoVO contratoVO = contratoDAO.consultarUltimoContratoPorPessoa(codigoPessoaContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyNumber(contratoVO.getCodigo())) {
                    throw new Exception("Nenhum contrato encontrado para o cliente.");
                }

                contratoVO.setContratoPlanoProdutoSugeridoVOs(contratoPlanoProdutoSugeridoDAO.consultarContratoPlanoProdutoSugeridos(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                final HistoricoContratoVO historicoContrato = historicoContratoDAO.obterHistoricoContratoPorStatusCodigoContratoDataInicioDataFim(contratoVO.getCodigo(), new Date(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (contratoVO.getSituacao().equals("AT")) {
                    notificarIntegracaoFogueteContratoCliente(con, ClienteFogueteStatusEnum.ACTIVE, contratoVO, clienteVO, usuarioVO, isSincronizacaoManual);
                } else if (contratoVO.getSituacao().equals("IN")) {
                    notificarIntegracaoFogueteContratoCliente(con, ClienteFogueteStatusEnum.INACTIVE, contratoVO, clienteVO, usuarioVO, isSincronizacaoManual);
                } else if (contratoVO.getSituacao().equals("CA")) {
                    notificarIntegracaoFogueteContratoCliente(con, ClienteFogueteStatusEnum.CANCELED, contratoVO, clienteVO, usuarioVO, isSincronizacaoManual);
                } else if (contratoVO.getSituacao().equals("IN") && historicoContrato != null) {
                    notificarIntegracaoFogueteContratoCliente(con, ClienteFogueteStatusEnum.PAST_DUE, contratoVO, clienteVO, usuarioVO, isSincronizacaoManual);
                }

                return EnvelopeRespostaDTO.of("Notificação integração foguete realizada com sucesso.");
            } else {
                throw new Exception("Nenhuma operação realizada");
            }
        } catch (Exception ex) {
            Uteis.logar(ex, TelaClienteServlet.class);
            throw new Exception("Falha ao sincronizar integração foguete. Erro: " + ex.getMessage());
        }
    }

    private EnvelopeRespostaDTO transferirDireitoContrato(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        Contrato contratoDAO;
        ContratoPlanoProdutoSugerido contratoPlanoProdutoSugeridoDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            contratoDAO = new Contrato(con);
            contratoPlanoProdutoSugeridoDAO = new ContratoPlanoProdutoSugerido(con);
            zillyonWebFacade = new ZillyonWebFacade(con);

            String chave = obterChave(request);
            JSONObject jsonObject = getJSONBody(request);
            Integer codUsuario = jsonObject.optInt("usuario");
            Integer codEmpresa = jsonObject.optInt("empresa");
            String tipo = jsonObject.optString("tipo");

            if (tipo.equalsIgnoreCase("consulta")) {
                String busca = jsonObject.optString("busca");
                List<ClienteVO> clientes = clienteDAO.consultarPorNomePessoaSituacaoDiferente(busca, "AT", codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 50);
                List<ClienteDTO> retorno = new ArrayList<>();
                for (ClienteVO clienteVO : clientes) {
                    retorno.add(new ClienteDTO(clienteVO));
                }

                Integer total = retorno.size();
                PaginadorDTO paginadorDTO = new PaginadorDTO(request);
                if (paginadorDTO != null
                        && paginadorDTO.getPage() != null
                        && paginadorDTO.getSize() != null) {
                    int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                    int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                    if (ultimoRegistro > retorno.size()) {
                        retorno = retorno.subList(primeiroPaginacao, retorno.size());
                    } else {
                        retorno = retorno.subList(primeiroPaginacao, ultimoRegistro);
                    }
                }

                if (paginadorDTO != null) {
                    paginadorDTO.setQuantidadeTotalElementos(Long.valueOf(total));
                }

                return EnvelopeRespostaDTO.of(retorno, paginadorDTO);

            } else if (tipo.equalsIgnoreCase("transferir")) {

                Integer codCliente = jsonObject.optInt("cliente");
                Integer codClienteDestino = jsonObject.optInt("clienteDestino");
                Integer codContrato = jsonObject.optInt("contrato");

                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ClienteVO clienteDestinoVO = clienteDAO.consultarPorChavePrimaria(codClienteDestino, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoVO.setContratoPlanoProdutoSugeridoVOs(contratoPlanoProdutoSugeridoDAO.consultarContratoPlanoProdutoSugeridos(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                zillyonWebFacade.transferirDireitoDeUsoContrato(clienteVO, clienteDestinoVO, contratoVO, usuarioVO, chave);

                notificarIntegracaoFogueteContratoCliente(con, ClienteFogueteStatusEnum.ACTIVE, contratoVO, clienteDestinoVO, usuarioVO, false);
                notificarIntegracaoFogueteContratoCliente(con, ClienteFogueteStatusEnum.INACTIVE, contratoVO, clienteVO, usuarioVO, false);

                return EnvelopeRespostaDTO.of("Transferência realizada com sucesso.");

            } else if (tipo.equalsIgnoreCase("recuperar")) {

                Integer codCliente = jsonObject.optInt("cliente");
                Integer codContrato = jsonObject.optInt("contrato");

                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoVO.setContratoPlanoProdutoSugeridoVOs(contratoPlanoProdutoSugeridoDAO.consultarContratoPlanoProdutoSugeridos(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                ClienteVO clienteDestinoVO = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoaOriginal().getCodigo(), 0, Uteis.NIVELMONTARDADOS_MINIMOS);
                ClienteVO clienteOrigemVO = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), 0, Uteis.NIVELMONTARDADOS_MINIMOS);
                ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                zillyonWebFacade.recuperarDireitoDeUsoContrato(clienteVO, clienteDestinoVO, contratoVO, usuarioVO, chave);

                notificarIntegracaoFogueteContratoCliente(con, ClienteFogueteStatusEnum.ACTIVE, contratoVO, clienteDestinoVO, usuarioVO, false);
                notificarIntegracaoFogueteContratoCliente(con, ClienteFogueteStatusEnum.INACTIVE, contratoVO, clienteOrigemVO, usuarioVO, false);

                return EnvelopeRespostaDTO.of("Recuperação dos direitos de uso realizada com sucesso.");
            } else {
                throw new Exception("Nenhuma operação realizada");
            }
        } finally {
            clienteDAO = null;
            contratoDAO = null;
            usuarioDAO = null;
            zillyonWebFacade = null;
            contratoPlanoProdutoSugeridoDAO = null;
        }
    }

    private EnvelopeRespostaDTO operacoesContrato(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        Contrato contratoDAO;
        ContratoOperacao contratoOperacaoDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            contratoDAO = new Contrato(con);
            contratoOperacaoDAO = new ContratoOperacao(con);

            String chave = obterChave(request);
            JSONObject jsonObject = getJSONBody(request);
            Integer codUsuario = jsonObject.optInt("usuario");
            String tipo = jsonObject.optString("tipo");

            Conexao.guardarConexaoForJ2SE(chave, con);

            if (tipo.equalsIgnoreCase("retornoAtestado")) {

                Integer codContrato = jsonObject.optInt("contrato");
                Integer codCliente = jsonObject.optInt("cliente");

                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                AtestadoContratoVO atestadoContratoVO = new AtestadoContratoVO();
                atestadoContratoVO.setResponsavelOperacao(usuarioVO);
                atestadoContratoVO.setContratoVO(contratoVO);
                atestadoContratoVO.setEmpresa(clienteVO.getEmpresa().getCodigo());

                ContratoOperacaoVO contratoOperacaoVO = contratoOperacaoDAO.consultarPorTipoOperacaoCodigoContrato(contratoVO.getCodigo(), "AT", false, Uteis.NIVELMONTARDADOS_ROBO);
                atestadoContratoVO.setDataInicio(contratoOperacaoVO.getDataInicioEfetivacaoOperacao());
                atestadoContratoVO.setDataTermino(contratoOperacaoVO.getDataFimEfetivacaoOperacao());
                atestadoContratoVO.setTipoJustificativa(contratoOperacaoVO.getTipoJustificativa().getCodigo());

                contratoOperacaoDAO.incluirOperacaoRetornoAtestado(atestadoContratoVO, usuarioVO);

                return EnvelopeRespostaDTO.of("Retorno realizado com sucesso.");
            } else {
                throw new Exception("Nenhuma operação realizada");
            }
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            contratoDAO = null;
            contratoOperacaoDAO = null;
        }
    }

    private EnvelopeRespostaDTO consultarSaldoCredito(Connection con, HttpServletRequest request) throws Exception {
        ControleCreditoTreino controleCreditoTreino;
        try {
            controleCreditoTreino = new ControleCreditoTreino(con);
            Integer codContrato = Integer.parseInt(request.getParameter("codContrato"));
            Integer matricula = Integer.parseInt(request.getParameter("matricula"));
            Integer saldoCreditoTreino = controleCreditoTreino.consultarSaldoCredito(codContrato);
            Integer saldoVirtualALuno = controleCreditoTreino.saldoVirtualAluno(matricula, codContrato);
            return EnvelopeRespostaDTO.of(new SaldoCreditoCliente(saldoCreditoTreino, saldoCreditoTreino - saldoVirtualALuno));
        } finally {
            controleCreditoTreino = null;
        }
    }

    private EnvelopeRespostaDTO consultarAvisos(Connection con, HttpServletRequest request) throws Exception {
        ClienteMensagem clienteMensagemDAO;
        try {
            clienteMensagemDAO = new ClienteMensagem(con);

            Integer codCliente = Integer.parseInt(request.getParameter("cliente"));

            List<ClienteMensagemVO> avisos = clienteMensagemDAO.consultarTelaCliente(codCliente, null);
            List<ClienteMensagemDTO> retorno = new ArrayList<>();
            for (ClienteMensagemVO obj : avisos) {
                retorno.add(new ClienteMensagemDTO(obj));
            }

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            if (paginadorDTO != null
                    && paginadorDTO.getPage() != null
                    && paginadorDTO.getSize() != null) {
                int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                try {
                    if (ultimoRegistro > retorno.size()) {
                        retorno = retorno.subList(primeiroPaginacao, retorno.size());
                    } else {
                        retorno = retorno.subList(primeiroPaginacao, ultimoRegistro);
                    }
                } catch (Exception ex) {
                    retorno = new ArrayList<>();
                }
            }

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos((long) avisos.size());
            }

            return EnvelopeRespostaDTO.of(retorno, paginadorDTO);
        } finally {
            clienteMensagemDAO = null;
        }
    }

    private EnvelopeRespostaDTO dadosPagamentoCliente(Connection con, HttpServletRequest request) throws Exception {
        Pessoa pessoaDAO;
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO;
        try {
            pessoaDAO = new Pessoa(con);
            autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(con);

            Integer codCliente = Integer.parseInt(request.getParameter("cliente"));
            Integer codPessoa = pessoaDAO.obterPessoaCliente(codCliente);
            if (UteisValidacao.emptyNumber(codPessoa)) {
                throw new Exception("Pessoa não encontrada");
            }

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setCodigo(codPessoa);
            pessoaDAO.obterInformacoesDeBloqueioCobrancaAutomatica(pessoaVO);

            List<AutorizacaoCobrancaClienteVO> listaAuto = autorizacaoCobrancaClienteDAO.consultarPorCliente(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            String descricao = "";
            for (AutorizacaoCobrancaVO auto : listaAuto) {
                descricao += auto.getConvenio().getDescricao() + ", ";
            }

            DadosPagamentoClienteDTO objDTO = new DadosPagamentoClienteDTO();
            objDTO.setBloqueioCobrancaAutomaticaData(pessoaVO.getDataBloqueioCobrancaAutomaticaApresentar());
            objDTO.setBloqueioCobrancaAutomaticaTipo(pessoaVO.getTipoBloqueioCobrancaAutomatica() != null ? pessoaVO.getTipoBloqueioCobrancaAutomatica().getCodigo() : null);
            objDTO.setConvenioCobranca(Uteis.removerUltimosCaracteres(descricao, 2));

            String hintExibir = "";
            if (pessoaVO.getDataBloqueioCobrancaAutomatica() == null) {
                hintExibir = "A cobrança automática está liberada.";
            } else {
                if (pessoaVO.getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.TODAS_PARCELAS)) {
                    hintExibir = "TODAS as parcelas estão bloqueadas para cobrança automática.";
                } else if (pessoaVO.getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS)) {
                    hintExibir = "TODAS as parcelas com vencimento superior ao dia " + pessoaVO.getDataBloqueioCobrancaAutomaticaApresentar() + " estão bloqueadas para cobrança automática.";
                }
            }
            objDTO.setHintExibir(hintExibir);
            return EnvelopeRespostaDTO.of(objDTO);
        } finally {
            pessoaDAO = null;
            autorizacaoCobrancaClienteDAO = null;
        }
    }

    public EnvelopeRespostaDTO liberarVagaTurma(Connection con, HttpServletRequest request) throws Exception {
        ContratoOperacao contratoOperacaoDao;
        MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurmaDao;
        Pessoa pessoaDao;
        Log logDao;
        Usuario usuarioDao;
        try {
            contratoOperacaoDao = new ContratoOperacao(con);
            matriculaAlunoHorarioTurmaDao = new MatriculaAlunoHorarioTurma(con);
            pessoaDao = new Pessoa(con);
            logDao = new Log(con);
            usuarioDao = new Usuario(con);
            Integer codContrato = Integer.parseInt(request.getParameter("codContrato"));
            boolean existeOperacaoPendenteDeRetorno = contratoOperacaoDao.existeOperacaoPendenteDeRetorno(codContrato, Calendario.hoje());
            if (!existeOperacaoPendenteDeRetorno) {
                Date vigenciaAteAjustada = Calendario.getDate("yyyy-MM-dd", request.getParameter("vigenciaAteAjustada"));
                Integer codEmpresaContrato = Integer.parseInt(request.getParameter("codEmpresaContrato"));
                Integer codUsuario = Integer.parseInt(request.getParameter("codUsuarioLogado"));
                UsuarioVO usuarioLogado = usuarioDao.consultarPorCodigo(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List<MatriculaAlunoHorarioTurmaVO> matriculas = matriculaAlunoHorarioTurmaDao
                        .consultarMatriculaAtivaPorContrato(codContrato, codEmpresaContrato, Calendario.hoje(),
                                Uteis.NIVELMONTARDADOS_MINIMOS
                        );
                Date dataFimAtualMatricula;
                Integer codigoPessoa = pessoaDao.consultarCodigoPessoaPorCodigoContrato(codContrato);
                for (MatriculaAlunoHorarioTurmaVO matricula : matriculas) {
                    dataFimAtualMatricula = matricula.getDataFim();
                    if (Calendario.maior(Uteis.somarDias(Calendario.hoje(), -1), vigenciaAteAjustada)) {
                        matricula.setDataFim(Uteis.somarDias(Calendario.hoje(), -1));
                    } else {
                        matricula.setDataFim(vigenciaAteAjustada);
                    }
                    if (Calendario.maior(matricula.getDataInicio(), matricula.getDataFim())) {
                        matriculaAlunoHorarioTurmaDao.excluir(matricula);
                    } else {
                        matriculaAlunoHorarioTurmaDao.alterar(matricula);
                    }
                    registraLogs(logDao, codigoPessoa, dataFimAtualMatricula, matricula, usuarioLogado, codContrato);
                }
                registrarContratoOperacaoParaLiberacaoDeVaga(codContrato, usuarioLogado, contratoOperacaoDao);
                return EnvelopeRespostaDTO.of("Aula liberada com sucesso");
            }
            return EnvelopeRespostaDTO.of("Não é possível liberar a aula pois existe operações pendentes para a data de hoje.");
        } finally {
            contratoOperacaoDao = null;
            matriculaAlunoHorarioTurmaDao = null;
        }
    }

    private void registraLogs(Log logDao, Integer codigoPessoa, Date dataFimAtual, MatriculaAlunoHorarioTurmaVO mat, UsuarioVO usuarioLogado, Integer codContrato) throws Exception {
        LogVO log = new LogVO();
        log.setOperacao("ALTERAÇÃO");
        log.setNomeEntidade("MatriculaHorarioTurma");
        log.setChavePrimaria(mat.getCodigo().toString());
        log.setPessoa(codigoPessoa);
        log.setNomeCampo("Liberar Vaga - DataFim");
        log.setValorCampoAnterior(Calendario.getData(dataFimAtual, "dd/MM/yyyy"));
        log.setValorCampoAlterado(Calendario.getData(mat.getDataFim(), "dd/MM/yyyy"));
        log.setDescricao("O contrato " + codContrato
                + " estava inativo e ainda ocupando vagas em turmas do sistema. "
                + " O usuário '" + usuarioLogado.getNome() + "' decidiu então liberar as vagas.");
        log.setResponsavelAlteracao(usuarioLogado.getNome());
        logDao.incluir(log);
    }

    private void registrarContratoOperacaoParaLiberacaoDeVaga(Integer codContrato, UsuarioVO usuarioLogado, ContratoOperacao contratoOperacaoDao) throws Exception {
        ContratoOperacaoVO contratoOperacaoVO = contratoOperacaoDao.novo();
        contratoOperacaoVO.setContrato(codContrato);
        contratoOperacaoVO.setTipoOperacao(TipoOperacaoContratoEnum.LIBERAR_VAGA.getSigla());
        contratoOperacaoVO.setJustificativa("Permitir que vagas de alunos inativos possam ser preenchidas.");
        contratoOperacaoVO.setResponsavel(usuarioLogado);
        contratoOperacaoVO.setObservacao("Usuário liberou vaga de aluno inativo.");
        contratoOperacaoDao.incluir(contratoOperacaoVO);
    }

    private EnvelopeRespostaDTO faltasTurmasRemovidas(Connection con, HttpServletRequest request) throws Exception {
        Contrato contratoDao;
        try {
            contratoDao = new Contrato(con);
            // TODO Verificar erro de conexão fechando após um tempo
            String chave = obterChave(request);
            Conexao.guardarConexaoForJ2SE(chave, con);
            Integer codContrato = Integer.parseInt(request.getParameter("codContrato"));
            Date vigenciaAteAjustada = Calendario.getDate("yyyy-MM-dd", request.getParameter("vigenciaAteAjustada"));
            Integer codCliente = Integer.parseInt(request.getParameter("codCliente"));
            String matricula = request.getParameter("matricula");
            HistoricoTurmasContratoTO historicoTurmasContratoTO = contratoDao.preencherInfoAualsRemovidas(codContrato, vigenciaAteAjustada, matricula, codCliente);
            return EnvelopeRespostaDTO.of(historicoTurmasContratoTO);
        } finally {
            contratoDao = null;
        }
    }

    private EnvelopeRespostaDTO buscarHorarioTurmaByCodigo(Connection con, HttpServletRequest request) throws Exception {
        HorarioTurma horarioTurmaDao;
        try {
            horarioTurmaDao = new HorarioTurma(con);
            Integer codigoHorarioTurma = Integer.parseInt(request.getParameter("codHorarioTurma"));
            HorarioTurmaVO horarioTurma = horarioTurmaDao.consultarPorChavePrimaria(codigoHorarioTurma, Uteis.NIVELMONTARDADOS_TODOS);
            HorarioTurmaDTO horarioTurmaDTO = new HorarioTurmaDTO(
                    horarioTurma.getCodigo(),
                    horarioTurma.getIdentificadorTurma(),
                    horarioTurma.getDiaSemana(),
                    horarioTurma.getDiaSemanaNumero(),
                    horarioTurma.getHoraInicial(),
                    horarioTurma.getHoraFinal(),
                    new ColaboradorDTO(new PessoaDTO(horarioTurma.getProfessor().getPessoa().getCodigo(), horarioTurma.getProfessor().getPessoa().getNome()))
            );
            return EnvelopeRespostaDTO.of(horarioTurmaDTO);
        } finally {
            horarioTurmaDao = null;
        }
    }

    private EnvelopeRespostaDTO alterarMatricula(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        ConfiguracaoSistema configuracaoSistemaDAO;
        Log logDAO;
        try {
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            logDAO = new Log(con);

            String chave = obterChave(request);
            JSONObject jsonObject = getJSONBody(request);
            Integer codCliente = jsonObject.optInt("cliente");
            Integer codUsuario = jsonObject.optInt("usuario");
            Integer codEmpresa = jsonObject.optInt("empresa");
            Integer novaMatricula = jsonObject.optInt("novaMatricula");
            if (UteisValidacao.emptyNumber(codCliente)) {
                throw new Exception("Cliente não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                throw new Exception("Empresa não informada");
            }
            if (UteisValidacao.emptyNumber(novaMatricula) ||
                    novaMatricula.equals(0)) {
                throw new Exception("Matrícula inválida.");
            }

            Conexao.guardarConexaoForJ2SE(chave, con);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_EMPRESA_BASICO);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_TODOS);

            String antigaMatricula = clienteVO.getMatricula();
            Integer antigoCodMatricula = clienteVO.getCodigoMatricula();

            ClienteVO clienteExistenteVO = clienteDAO.consultarPorCodigoMatricula(novaMatricula, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (clienteExistenteVO.getCodigo() != 0) {
                throw new Exception("Já existe um cliente com a matrícula " + clienteExistenteVO.getCodigoMatricula().toString() + ". Por favor informe outra matrícula.");
            }

            String matriculaMascara = Uteis.getMontarMatricula(novaMatricula.toString(), empresaVO.getMascaraMatricula().length());

            clienteVO.setMatricula(matriculaMascara);
            clienteVO.setCodigoMatricula(novaMatricula);
            clienteDAO.alterar(clienteVO, configuracaoSistemaVO);

            Integer numeroMatriculaAtual = clienteDAO.obterMatriculaAluno();
            if (novaMatricula > numeroMatriculaAtual) {
                clienteDAO.atualizarMatriculaAluno(novaMatricula);
            }

            //REGISTRAR LOG DA ALTERAÇAO DE MATRÍCULA
            try {
                LogVO obj = new LogVO();
                obj.setChavePrimaria(clienteVO.getPessoa().getCodigo().toString());
                obj.setNomeEntidade("CLIENTE - MATRICULA");
                obj.setNomeEntidadeDescricao("Cliente - Matrícula");
                obj.setOperacao("ALTERACAO DE MATRICULA");
                obj.setResponsavelAlteracao(usuarioVO.getNome());
                obj.setUserOAMD(usuarioVO.getUserOamd());
                obj.setNomeCampo("Matrícula-Cliente");
                obj.setValorCampoAnterior("MATRÍCULA ANTERIOR: " + antigaMatricula);
                obj.setValorCampoAlterado("NOVA MATRÍCULA: " + matriculaMascara);
                obj.setDataAlteracao(Calendario.hoje());
                registrarLogObjetoVO(obj, clienteVO.getPessoa().getCodigo(), logDAO);
            } catch (Exception e) {
                registrarLogErroObjetoVO("CLIENTE - MATRICULA", clienteVO.getPessoa().getCodigo(), "ERRO AO REGISTRAR LOG ALTERAÇÃO DE MATRICULA",
                        usuarioVO.getNome(), usuarioVO.getUserOamd(), con);
                e.printStackTrace();
            }

            return EnvelopeRespostaDTO.of("Matrícula alterada com sucesso");
        } finally {
            clienteDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            configuracaoSistemaDAO = null;
            logDAO = null;
        }
    }

    private EnvelopeRespostaDTO incluirControleCreditoTreino(Connection con, HttpServletRequest request) throws Exception {
        ControleCreditoTreino controleCreditoTreinoDao = null;
        SituacaoClienteSinteticoDW situacaoClienteSinteticoDW = null;
        Cliente clienteDAO = null;
        Log logDAO = null;
        Usuario usuarioDAO = null;
        try {
            controleCreditoTreinoDao = new ControleCreditoTreino(con);
            situacaoClienteSinteticoDW = new SituacaoClienteSinteticoDW(con);
            clienteDAO = new Cliente(con);
            logDAO = new Log(con);
            usuarioDAO = new Usuario(con);

            JSONObject jsonObject = new JSONObject(obterBody(request));
            Integer codCliente = jsonObject.getInt("codCliente");

            ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
            controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.getTipo(jsonObject.getInt("tipoOperacaoCreditoTreino")));
            controleCreditoTreinoVO.setCodigoTipoAjusteManualCreditoTreino(jsonObject.getInt("codigoTipoAjusteManualCreditoTreino"));
            controleCreditoTreinoVO.setQuantidade(jsonObject.getInt("quantidade"));
            controleCreditoTreinoVO.setObservacao(jsonObject.optString("observacao"));
            ContratoVO contrato = new ContratoVO();
            contrato.setCodigo(jsonObject.getJSONObject("contrato").getInt("codigo"));
            controleCreditoTreinoVO.setContratoVO(contrato);
            JSONObject usuarioJson = jsonObject.optJSONObject("usuario");
            UsuarioVO usuario = new UsuarioVO();
            if (usuarioJson != null) {
                usuario.setCodigo(usuarioJson.getInt("codigo"));
                controleCreditoTreinoVO.setUsuarioVO(usuario);
            } else {
                UsuarioVO admin = usuarioDAO.consultarPorCodigo(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                controleCreditoTreinoVO.setUsuarioVO(admin);
            }

            if (controleCreditoTreinoVO.getCodigoTipoAjusteManualCreditoTreino() == 3){

                Integer codigoClienteRecebendoTransferencia = jsonObject.getJSONObject("clienteRecebendoTransferencia").getInt("codigo");

                ClienteVO clienteRecebedorVO = clienteDAO.consultarPorChavePrimaria(codigoClienteRecebendoTransferencia, Uteis.NIVELMONTARDADOS_TODOS);
                ClienteVO clienteTransferidorVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (clienteTransferidorVO.getSituacao().equals("IN") || clienteTransferidorVO.getSituacaoClienteSinteticoVO().getSituacao().equals("IN")){
                    throw new Exception("Alunos inativos não podem transferir crédito");
                }

                ControleCreditoTreinoVO controleCreditoTreinoVORecebedor = (ControleCreditoTreinoVO) controleCreditoTreinoVO.getClone(false);

                controleCreditoTreinoVO.setObservacao(controleCreditoTreinoVO.getObservacao() + " (Enviado para: " + clienteRecebedorVO.getNome_Apresentar() + ")");

                controleCreditoTreinoVORecebedor.setObservacao(controleCreditoTreinoVORecebedor.getObservacao() + " (Recebido de: " + clienteTransferidorVO.getNome_Apresentar() + ")");

                controleCreditoTreinoDao.realizarTransferenciaDeCredito(controleCreditoTreinoVORecebedor, controleCreditoTreinoVO, situacaoClienteSinteticoDW, codCliente, clienteRecebedorVO);

                incluirLogTransferenciaDeCredito(clienteTransferidorVO, clienteRecebedorVO, usuario, logDAO, controleCreditoTreinoVO);

                return EnvelopeRespostaDTO.of("Transferência realizada com sucesso!");
            }

            controleCreditoTreinoDao.incluir(controleCreditoTreinoVO, codCliente, situacaoClienteSinteticoDW);

            return EnvelopeRespostaDTO.of("Ajuste realizado com sucesso!");
        } finally {
            controleCreditoTreinoDao = null;
            clienteDAO = null;
            situacaoClienteSinteticoDW = null;
        }
    }

    public void incluirLogTransferenciaDeCredito(ClienteVO clienteOrigem, ClienteVO clienteDestino, UsuarioVO usuario, Log logDao, ControleCreditoTreinoVO controleCreditoTreinoVO) throws Exception {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(clienteOrigem.getPessoa().getCodigo().toString());
            obj.setNomeEntidade("CONTROLE CREDITO TREINO");
            obj.setNomeEntidadeDescricao("CONTROLE CREDITO TREINO");
            obj.setOperacao("TRANSFERIU CREDITO ENTRE ALUNOS");
            obj.setResponsavelAlteracao(usuario.getNome());
            obj.setUserOAMD(usuario.getUserOamd());
            obj.setNomeCampo("Campo(s) ");
            obj.setValorCampoAlterado(gerarLogTransferenciaDeCredito(clienteOrigem, clienteDestino, controleCreditoTreinoVO));
            obj.setValorCampoAnterior("");

            obj.setDataAlteracao(Calendario.hoje());
            registrarLogObjetoVO(obj, clienteOrigem.getPessoa().getCodigo(), logDao);
            registrarLogObjetoVO(obj, clienteDestino.getPessoa().getCodigo(), logDao);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String gerarLogTransferenciaDeCredito(ClienteVO clienteOrigem, ClienteVO clienteDestino, ControleCreditoTreinoVO controleCreditoTreinoVO){
        StringBuilder valores = new StringBuilder();
        valores.append("\n");
        valores.append("Aluno origem = ").append(clienteOrigem.getPessoa().getNome()).append("\n");
        valores.append("Aluno destino = ").append(clienteDestino.getPessoa().getNome()).append("\n");
        valores.append("Quantidade de créditos transferidos = ").append((controleCreditoTreinoVO.getQuantidade() * -1)).append("\n");
        valores.append("Data da transferência = ").append(controleCreditoTreinoVO.getDataOperacao()).append("\n");

        return valores.toString();
    }

    private EnvelopeRespostaDTO consultarAlunosTransferenciaDeCredito(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;

        try {
            clienteDAO = new Cliente(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codEmpresa = jsonObject.optInt("empresa");

            String busca = jsonObject.optString("busca");
            List<ClienteVO> clientes = clienteDAO.consultarPorNomePessoa(busca,  codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 50);
            List<ClienteDTO> retorno = new ArrayList<>();
            for (ClienteVO clienteVO : clientes) {
                retorno.add(new ClienteDTO(clienteVO));
            }

            Integer total = retorno.size();
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            if (paginadorDTO != null
                    && paginadorDTO.getPage() != null
                    && paginadorDTO.getSize() != null) {
                int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                if (ultimoRegistro > retorno.size()) {
                    retorno = retorno.subList(primeiroPaginacao, retorno.size());
                } else {
                    retorno = retorno.subList(primeiroPaginacao, ultimoRegistro);
                }
            }

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(Long.valueOf(total));
            }

            return EnvelopeRespostaDTO.of(retorno, paginadorDTO);

        } finally {
            clienteDAO = null;
        }
    }

    private EnvelopeRespostaDTO consultarQuantosCreditosOAlunoPodeTransferir(Connection con, HttpServletRequest request) throws Exception {
        ControleCreditoTreino controleCreditoTreinoDAO = null;

        try {
            controleCreditoTreinoDAO = new ControleCreditoTreino(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codigoContrato = jsonObject.optInt("contrato");

            Integer quantidadeDeCreditosPermitidosATransferir = controleCreditoTreinoDAO.consultarQuantosCreditosOAlunoPodeTransferir(codigoContrato);

            Map<String, Object> retorno = new HashMap<>();
            retorno.put("quantidade", quantidadeDeCreditosPermitidosATransferir);

            return EnvelopeRespostaDTO.of(retorno);

        } finally {
            controleCreditoTreinoDAO = null;
        }
    }

    private EnvelopeRespostaDTO consultarAlunoTransferenciaDeCreditoPorCpfOuEmail(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO = null;

        try {
            clienteDAO = new Cliente(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer empresa = jsonObject.optInt("empresa");
            String busca = jsonObject.optString("busca");

            List<ClienteVO> resultSql = clienteDAO.consultarPorEmailOuCPF(busca, empresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (resultSql == null || resultSql.isEmpty()) {
                throw new Exception("Aluno não encontrado para o CPF ou e-mail informado.");
            }

            ClienteVO clienteVO = resultSql.get(0);

            Map<String, Object> retorno = new HashMap<>();
            retorno.put("codigo", clienteVO.getCodigo());
            retorno.put("nome", clienteVO.getNome_Apresentar());

            return EnvelopeRespostaDTO.of(retorno);

        } finally {
            clienteDAO = null;
        }
    }

    private EnvelopeRespostaDTO cancelarOperacao(HttpServletRequest request, Connection con) throws Exception {
        ZillyonWebFacade zwFacade;
        ContratoOperacao contratoOperacao;
        Usuario usuario;
        try {
            zwFacade = new ZillyonWebFacade(con);
            contratoOperacao = new ContratoOperacao(con);
            usuario = new Usuario(con);
            Integer codContratoOperacao = Integer.parseInt(request.getParameter("codContratoOperacao"));
            Integer codUsuarioLogado = Integer.parseInt(request.getParameter("codUsuarioLogado"));

            UsuarioVO usuarioLogadoVO = usuario.consultarPorChavePrimaria(codUsuarioLogado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoOperacaoVO contratoOperacaoVO = contratoOperacao.consultarPorChavePrimaria(codContratoOperacao, Uteis.NIVELMONTARDADOS_TODOS);
            if (UteisValidacao.emptyString(contratoOperacaoVO.getInformacoesDesfazer())) {
                throw new ConsistirException("Este contrato não foi cancelado pelo método de \"Cancelamento verificando próxima parcela em aberto\", portanto, não é possível desfazer essa operação.");
            }
            zwFacade.desfazerCancelamentoProporcional(contratoOperacaoVO, usuarioLogadoVO);
            return EnvelopeRespostaDTO.of("Cancelamento revertido com sucesso!");
        } finally {
            zwFacade = null;
        }
    }

    private EnvelopeRespostaDTO obterStatusConciliadora(Connection con, HttpServletRequest request) throws Exception {
        ConciliadoraServiceImpl conciliadoraService;
        try {
            conciliadoraService = new ConciliadoraServiceImpl(con);
            Integer codMovPagamento = Integer.parseInt(request.getParameter("codMovPagamento"));
            List<LogConciliadoraVO> logsVos = conciliadoraService.obterLogConciliadora(0, codMovPagamento);
            List<LogConciliadoraDTO> logsDtos = new ArrayList<>();
            logsVos.forEach(
                    log -> {
                        logsDtos.add(new LogConciliadoraDTO(log));
                    }
            );
            return EnvelopeRespostaDTO.of(logsDtos);
        } finally {
            conciliadoraService = null;
        }
    }

    private EnvelopeRespostaDTO obterCheques(Connection con, HttpServletRequest request) throws Exception {
        Cheque chequeDao;
        try {
            chequeDao = new Cheque(con);
            Integer codContrato = Integer.parseInt(request.getParameter("codContrato"));
            List<ChequeVO> cheques = chequeDao.consultarTelaCliente(codContrato);
            List<ChequeDTO> chequesDto = new ArrayList<>();
            for (ChequeVO cheque : cheques) {
                chequesDto.add(new ChequeDTO(cheque));
            }
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            if (paginadorDTO != null
                    && paginadorDTO.getPage() != null
                    && paginadorDTO.getSize() != null) {
                int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                if (ultimoRegistro > chequesDto.size()) {
                    chequesDto = chequesDto.subList(primeiroPaginacao, chequesDto.size());
                } else {
                    chequesDto = chequesDto.subList(primeiroPaginacao, ultimoRegistro);
                }
            }

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos((long) cheques.size());
            }
            return EnvelopeRespostaDTO.of(chequesDto, paginadorDTO);
        } finally {
            chequeDao = null;
        }
    }

    private EnvelopeRespostaDTO pontos(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        HistoricoPontos historicoPontosDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            historicoPontosDAO = new HistoricoPontos(con);

            String chave = obterChave(request);
            JSONObject jsonObject = new JSONObject(obterBody(request));
            Integer codCliente = jsonObject.getInt("cliente");
            Integer codUsuario = jsonObject.getInt("usuario");
            Integer pontos = jsonObject.getInt("pontos");
            String descricao = jsonObject.getString("descricao");
            String tipoOperacao = jsonObject.getString("tipo");

            Conexao.guardarConexaoForJ2SE(chave, con);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

//            permissaoFuncionalidade(usuarioVO, "AjusteManualPontos", "5.69 - Permitir ajuste manual de pontos");

            HistoricoPontosVO historicoPontosVO = new HistoricoPontosVO();
            historicoPontosVO.setCliente(clienteVO);
            historicoPontosVO.setPontos(pontos);
            historicoPontosVO.setDescricao(descricao);

            if (historicoPontosVO.getDescricao().isEmpty()) {
                throw new Exception("Descrição e obrigatório");
            }

            if (tipoOperacao.isEmpty()) {
                throw new Exception("Tipo de Operação obrigatório");
            }
            if (!tipoOperacao.equalsIgnoreCase("entrada") &&
                    !tipoOperacao.equalsIgnoreCase("saida")) {
                throw new Exception("Tipo de Operação inválida");
            }

            if (historicoPontosVO.getPontos().intValue() < 0) {
                throw new Exception("Pontução deve ser informada");
            }

            Integer valor = 0;
            if (tipoOperacao.equals("entrada")) {
                historicoPontosVO.setEntrada(true);
            } else if (tipoOperacao.equals("saida")) {
                historicoPontosVO.setEntrada(false);
                valor = (historicoPontosVO.getPontos() * -1);
                historicoPontosVO.setPontos(valor);
            }

            historicoPontosVO.setTipoPonto(TipoItemCampanhaEnum.AJUSTE_PONTO);
            historicoPontosVO.setDataConfirmacao(Calendario.hoje());
            historicoPontosVO.setDataaula(Calendario.hoje());

            historicoPontosDAO.incluir(historicoPontosVO);

            return EnvelopeRespostaDTO.of("Ajuste de pontuação lançado com Sucesso!");
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            historicoPontosDAO = null;
        }
    }

    private EnvelopeRespostaDTO brinde(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        Brinde brindeDAO;
        HistoricoPontos historicoPontosDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            brindeDAO = new Brinde(con);
            historicoPontosDAO = new HistoricoPontos(con);

            String chave = obterChave(request);
            JSONObject jsonObject = new JSONObject(obterBody(request));
            Integer codCliente = jsonObject.getInt("cliente");
            Integer codUsuario = jsonObject.getInt("usuario");
            Integer codEmpresa = jsonObject.getInt("empresa");
            String tipoOperacao = jsonObject.getString("tipo");

            Conexao.guardarConexaoForJ2SE(chave, con);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (!tipoOperacao.equalsIgnoreCase("obter") &&
                    !tipoOperacao.equalsIgnoreCase("lancar")) {
                throw new Exception("Tipo de Operação inválida");
            }


            if (tipoOperacao.equalsIgnoreCase("obter")) {
                Integer pontosCliente = historicoPontosDAO.obterPontosTotalPorCliente(clienteVO.getCodigo());
                List<BrindeVO> brindes = brindeDAO.consultarBrindeDisponiveisPorPonto(pontosCliente, codEmpresa, "nao");

                List<BrindeDTO> retorno = new ArrayList<>();
                for (BrindeVO obj : brindes) {
                    retorno.add(new BrindeDTO(obj));
                }

                PaginadorDTO paginadorDTO = new PaginadorDTO(request);
                if (paginadorDTO != null
                        && paginadorDTO.getPage() != null
                        && paginadorDTO.getSize() != null) {
                    int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                    int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                    if (ultimoRegistro > retorno.size()) {
                        retorno = retorno.subList(primeiroPaginacao, retorno.size());
                    } else {
                        retorno = retorno.subList(primeiroPaginacao, ultimoRegistro);
                    }
                }

                if (paginadorDTO != null) {
                    paginadorDTO.setQuantidadeTotalElementos((long) brindes.size());
                }

                return EnvelopeRespostaDTO.of(retorno, paginadorDTO);

            } else if (tipoOperacao.equalsIgnoreCase("lancar")) {

                Integer codBrinde = jsonObject.getInt("brinde");
                String descricao = jsonObject.getString("descricao");

                BrindeVO brindeVO = brindeDAO.consultarPorChavePrimaria(codBrinde);
                if (brindeVO == null || UteisValidacao.emptyNumber(brindeVO.getCodigo())) {
                    throw new Exception("Brinde não encontrado com o código " + codBrinde);
                }

                HistoricoPontosVO dadosBrindeLancar = new HistoricoPontosVO();
                dadosBrindeLancar.setBrinde(brindeVO);
                dadosBrindeLancar.setCliente(clienteVO);
                dadosBrindeLancar.setTipoPonto(TipoItemCampanhaEnum.RESGATE_BRINDE);
                dadosBrindeLancar.setDataaula(Calendario.hoje());
                dadosBrindeLancar.setDescricao("Resgate do Brinde " + brindeVO.getNome());
                dadosBrindeLancar.setObservacao(descricao);
                dadosBrindeLancar.setDataConfirmacao(Calendario.hoje());
                dadosBrindeLancar.setEntrada(false);
                dadosBrindeLancar.setPontos(brindeVO.getPontosNegativo());


//                permissaoFuncionalidade(getUsuarioLogado(), "LancamentoBrindeAluno", "5.68 - Permitir lançar brinde para aluno");

                historicoPontosDAO.incluir(dadosBrindeLancar);

//                try {
//                    dadosBrindeLancar.setObjetoVOAntesAlteracao(new HistoricoPontosVO());
//                    dadosBrindeLancar.setNovoObj(true);
//                    registrarLogObjetoVO(dadosBrindeLancar, dadosBrindeLancar.getCodigo(), "HistoricoPontos", clienteVO.getCodigo());
//                } catch (Exception e) {
//                    registrarLogErroObjetoVO("Historico Pontos", dadosBrindeLancar.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DO HISTORICO PONTOS", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
//                    e.printStackTrace();
//                }

                return EnvelopeRespostaDTO.of("Brinde Lançado com Sucesso!");
            } else {
                throw new Exception("Operação não encontrada");
            }
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            historicoPontosDAO = null;
        }
    }

    private EnvelopeRespostaDTO convidado(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        Convite conviteDAO;
        Contrato contratoDAO;
        ContratoDependente contratoDependenteDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            conviteDAO = new Convite(con);
            contratoDAO = new Contrato(con);
            contratoDependenteDAO = new ContratoDependente(con);

            String chave = obterChave(request);
            JSONObject jsonObject = new JSONObject(obterBody(request));
            Integer codCliente = jsonObject.getInt("cliente");
            String tipoOperacao = jsonObject.getString("tipo");

//            Conexao.guardarConexaoForJ2SE(chave, con);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            switch (tipoOperacao) {
                case "informacoes":
                    InfoConvidadoDTO dto = new InfoConvidadoDTO();

                    dto.setUsados(conviteDAO.totalizarMes(clienteVO.getCodigo()));
                    Integer convitesDireito = 0;
                    boolean exibirConvites = false;
                    List<ContratoVO> listaContratosCliente = contratoDAO.consultarListaTela(clienteVO.getPessoa().getCodigo(), 5);
                    List<ContratoDependenteVO> listaContratosClienteDependente = contratoDependenteDAO.findAllByCliente(clienteVO, 5);
                    if (!UteisValidacao.emptyList(listaContratosClienteDependente)) {
                        for (ContratoDependenteVO contratoDep : listaContratosClienteDependente) {
                            listaContratosCliente.add(contratoDep.getContrato());
                        }
                    }
                    if (!UteisValidacao.emptyList(listaContratosCliente)) {
                        for (ContratoVO contratoVO : listaContratosCliente) {
                            convitesDireito += conviteDAO.convitesDireito(contratoVO.getCodigo());
                        }
                        exibirConvites = conviteDAO.jaConvidou(clienteVO.getCodigo());
                    }

                    dto.setDireito(convitesDireito);
                    dto.setDisponivel(convitesDireito - dto.getUsados());
                    dto.setExibirConvite(exibirConvites);
                    return EnvelopeRespostaDTO.of(dto);

                case "consultar_cpf":

//                    Integer codEmpresaCpf = jsonObject.getInt("clienteEmpresa");
//                    if (jsonObject.optBoolean("consultarAlunosCaixaAbertoTodasEmpresas")) {
//                        codEmpresaCpf = 0;
//                    }

                    String cpfBusca = jsonObject.getString("cpfBusca");
                    String cpf = Uteis.formatarCpfCnpj(Uteis.removerMascara(cpfBusca),false);
                    List<ClienteVO> clientesComMesmoCpf = clienteDAO.consultarClientesPorCfp(cpf, 0, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, 5);

                    List<NovoConvidadoClienteDTO> retornoCpf = new ArrayList<>();
                    for (ClienteVO obj : clientesComMesmoCpf) {
                        retornoCpf.add(new NovoConvidadoClienteDTO(obj));
                    }
                    return EnvelopeRespostaDTO.of(retornoCpf);

                case "consultar":

                    Integer codEmpresa = jsonObject.getInt("clienteEmpresa");
                    if (jsonObject.optBoolean("consultarAlunosCaixaAbertoTodasEmpresas")) {
                        codEmpresa = 0;
                    }

                    String pref = jsonObject.getString("busca");
                    List<ClienteVO> clientes = clienteDAO.consultarPorNomePessoaVisitanteEDesistente(pref, codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 20);

                    List<ConvidadoClienteDTO> retorno = new ArrayList<>();
                    for (ClienteVO obj : clientes) {
                        retorno.add(new ConvidadoClienteDTO(obj));
                    }

                    PaginadorDTO paginadorDTO = new PaginadorDTO(request);
                    if (paginadorDTO != null
                            && paginadorDTO.getPage() != null
                            && paginadorDTO.getSize() != null) {
                        int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                        int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                        if (ultimoRegistro > retorno.size()) {
                            retorno = retorno.subList(primeiroPaginacao, retorno.size());
                        } else {
                            retorno = retorno.subList(primeiroPaginacao, ultimoRegistro);
                        }
                    }

                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos((long) clientes.size());
                    }

                    return EnvelopeRespostaDTO.of(retorno, paginadorDTO);

                case "incluir_convidado":

                    String matriculaCliente = incluirConvidadoLancarConvite(chave, clienteVO, jsonObject, con);
                    return EnvelopeRespostaDTO.of(matriculaCliente);

                case "gravar":

                    Integer codUsuario = jsonObject.getInt("usuario");
                    Integer codConvidado = jsonObject.getInt("convidado");

                    ClienteVO clienteConvidadoVO = clienteDAO.consultarPorChavePrimaria(codConvidado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    conviteDAO.lancarConvite(usuarioVO, clienteConvidadoVO, clienteVO);
                    return EnvelopeRespostaDTO.of("Convite lançado com sucesso!");


                case "historico":

                    List<ConviteVO> historico = conviteDAO.historico(clienteVO.getCodigo());

                    List<HistoricoConvidadoDTO> retornoHistotico = new ArrayList<>();
                    for (ConviteVO obj : historico) {
                        retornoHistotico.add(new HistoricoConvidadoDTO(obj));
                    }

                    PaginadorDTO paginadorDTO1 = new PaginadorDTO(request);
                    if (paginadorDTO1 != null
                            && paginadorDTO1.getPage() != null
                            && paginadorDTO1.getSize() != null) {
                        int primeiroPaginacao = (int) (paginadorDTO1.getPage() * paginadorDTO1.getSize());
                        int ultimoRegistro = (int) ((paginadorDTO1.getPage() * paginadorDTO1.getSize()) + paginadorDTO1.getSize());
                        if (ultimoRegistro > retornoHistotico.size()) {
                            retornoHistotico = retornoHistotico.subList(primeiroPaginacao, retornoHistotico.size());
                        } else {
                            retornoHistotico = retornoHistotico.subList(primeiroPaginacao, ultimoRegistro);
                        }
                    }

                    if (paginadorDTO1 != null) {
                        paginadorDTO1.setQuantidadeTotalElementos((long) historico.size());
                    }

                    return EnvelopeRespostaDTO.of(retornoHistotico, paginadorDTO1);
                default:
                    throw new Exception("Operação não encontrada");
            }

        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            conviteDAO = null;
            contratoDAO = null;
            contratoDependenteDAO = null;
        }
    }

    private EnvelopeRespostaDTO imprimirContratoPrestacaoServico(Connection con, HttpServletRequest request) throws Exception {

        MovParcela movParcelaDao;
        VendaAvulsa vendaAvulsaDao;
        Empresa empresaDao;

        try {
            movParcelaDao = new MovParcela(con);
            vendaAvulsaDao = new VendaAvulsa(con);
            empresaDao = new Empresa(con);
            Conexao.guardarConexaoForJ2SE(obterChave(request), con);

            Integer codMovProduto = Integer.parseInt(request.getParameter("codMovProduto"));
            Integer codEmpresaLogada = Integer.parseInt(request.getParameter("codEmpresaLogada"));
            Integer codContratoPrestacaoServico = Integer.parseInt(request.getParameter("codContratoPrestacaoServico"));
            String userOamd = request.getParameter("userOamd");

            EmpresaVO empresaLogada = empresaDao.consultarPorCodigo(codEmpresaLogada, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<MovParcelaVO> parcelas = movParcelaDao.consultarPorMovProduto(codMovProduto, Uteis.NIVELMONTARDADOS_TODOS);
            VendaAvulsaVO vendaAvulsa = vendaAvulsaDao.consultarPorChavePrimaria(parcelas.get(0).getVendaAvulsaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            vendaAvulsa.setParcela(parcelas.get(0));

            List<MovPagamentoVO> pagamentos = prepararVendaEPagamento(con, vendaAvulsa, codContratoPrestacaoServico, userOamd);
            if (vendaAvulsa.getCodigo() != 0) {
                vendaAvulsa.getTextoPadrao().substituirTagsTextoPadraoVendaAvulsa(obterChave(request), Conexao.getFromSession(), vendaAvulsa, null, pagamentos, empresaLogada.getDescMoeda(), request, false, false, null);
                return EnvelopeRespostaDTO.of(request.getSession().getAttribute("textoRelatorio"));
            } else {
                throw new Exception("Não foi possível emitir o contrato. Dados não encontrados!");
            }
        } finally {
            movParcelaDao = null;
            vendaAvulsaDao = null;
            empresaDao = null;
        }
    }

    public List<MovPagamentoVO> prepararVendaEPagamento(Connection con, VendaAvulsaVO vendaAvulsa, Integer codContratoPrestacaoServico, String userOamd) throws Exception {
        PlanoTextoPadrao planoTextoPadraoDao;
        Pessoa pessoaDao;
        MovProduto movProdutoDao;
        Empresa empresaDao;
        Usuario usuarioDao;
        MovPagamento movPagamentoDao;

        try {
            planoTextoPadraoDao = new PlanoTextoPadrao(con);
            pessoaDao = new Pessoa(con);
            movProdutoDao = new MovProduto(con);
            empresaDao = new Empresa(con);
            usuarioDao = new Usuario(con);
            movPagamentoDao = new MovPagamento(con);
            // setar atributos do contrato
            vendaAvulsa.setTextoPadrao(planoTextoPadraoDao.consultarPorChavePrimaria(codContratoPrestacaoServico, Uteis.NIVELMONTARDADOS_TODOS));
            vendaAvulsa.getParcela().setPessoa(pessoaDao.consultarPorChavePrimaria(
                    vendaAvulsa.getParcela().getPessoa().getCodigo(),
                    Uteis.NIVELMONTARDADOS_TODOS));
            vendaAvulsa.setMovProdutoVOs(movProdutoDao.consultarPorCodigoParcela(vendaAvulsa.getParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            vendaAvulsa.setEmpresa(
                    empresaDao.consultarPorChavePrimaria(
                            vendaAvulsa.getEmpresa().getCodigo(),
                            Uteis.NIVELMONTARDADOS_TODOS));
            vendaAvulsa.setResponsavel(
                    usuarioDao.consultarPorChavePrimaria(
                            vendaAvulsa.getResponsavel().getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            vendaAvulsa.getResponsavel().setUserOamd(userOamd);

            return (List<MovPagamentoVO>) movPagamentoDao.consultarPagamentoDeUmaParcela(
                    vendaAvulsa.getParcela().getCodigo(), false,
                    Uteis.NIVELMONTARDADOS_TODOS);
        } finally {
            planoTextoPadraoDao = null;
            pessoaDao = null;
            movProdutoDao = null;
            empresaDao = null;
            usuarioDao = null;
            movPagamentoDao = null;
        }
    }

    private EnvelopeRespostaDTO imprimirReciboDevolucaoMovProduto(HttpServletRequest request, Connection con) throws Exception {
        ReciboDevolucao reciboDevolucaoDao;

        try {
            reciboDevolucaoDao = new ReciboDevolucao(con);

            Integer codReciboDevolucao = Uteis.converterInteiro(request.getParameter("codReciboDevolucao"));
            if (UteisValidacao.emptyNumber(codReciboDevolucao)) {
                throw new Exception("Contrato do recibo não informado");
            }
            ReciboDevolucaoVO reciboDevolucaoVO = new ReciboDevolucaoVO();
            reciboDevolucaoVO.setCodigo(codReciboDevolucao);
            String chave = obterChave(request);
            Conexao.guardarConexaoForJ2SE(chave, con);
            request.getSession().setAttribute("key", chave);
            String nomeArquivo = new SuperControleRelatorio().imprimirReciboDevolucao(reciboDevolucaoVO, request);
            return EnvelopeRespostaDTO.of(getUrlAplicacaoPastaRelatorio(chave, request) + nomeArquivo);
        } finally {
            reciboDevolucaoDao = null;
        }
    }

    private String getUrlAplicacaoHttps(String key) {
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(key);
            return clientDiscoveryDataDTO.getServiceUrls().getZwUrl();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private String getUrlAplicacaoPastaRelatorio(String key, HttpServletRequest request) {
        try {
            String context = request.getContextPath();
            String urlApli = request.getRequestURL().toString().split(context)[0];
            return urlApli + context + "/servlet-relatorio/";
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private EnvelopeRespostaDTO alterarFotoAluno(HttpServletRequest request, Connection con) throws Exception {
        ContratoAssinaturaDigitalServiceInterface cadServiceContrato;
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);
            cadServiceContrato = new ContratoAssinaturaDigitalServiceImpl(con);

            String chave = obterChave(request);

            JSONObject jsonObject = getJSONBody(request);
            Integer codPessoa = jsonObject.getInt("pessoa");
            Integer codUsuario = jsonObject.getInt("usuario");
            String imagem = jsonObject.getString("imagem");

            cadServiceContrato.setServletContext(request.getSession().getServletContext());
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            cadServiceContrato.alterarFotoAluno(chave, null, codPessoa, imagem, usuarioVO.getCodigo(), usuarioVO);
            SuperControle.notificarOuvintes(DadosAcessoOfflineVO.CHAVE_NOTIFICAR_ATUALIZAR_FOTO_PESSOA + "(" + codPessoa + ")", PropsService.getPropertyValue("urlNotificacaoAcesso"), chave);
            return EnvelopeRespostaDTO.of("ok");
        } finally {
            cadServiceContrato = null;
            usuarioDAO = null;
        }
    }

    private EnvelopeRespostaDTO excluirFotoAluno(HttpServletRequest request, Connection con) throws Exception {
        Pessoa pessoaDAO;
        Usuario usuarioDAO;
        try {
            pessoaDAO = new Pessoa(con);
            usuarioDAO = new Usuario(con);

            String chave = obterChave(request);

            JSONObject jsonObject = getJSONBody(request);
            Integer codPessoa = jsonObject.getInt("pessoa");
            Integer codUsuario = jsonObject.getInt("usuario");

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(codPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            pessoaDAO.removerFoto(chave, pessoaVO.getCodigo());
            String fotoKey = pessoaVO.getFotoKey();
            SuperControle.notificarOuvintes("excluir foto=" + pessoaVO.getCodigo(), PropsService.getPropertyValue("urlNotificacaoAcesso"), chave);

            String problemaTrocarFotoTreino = "";
            try {
                String urlTreino = PropsService.getPropertyValue(chave, PropsService.urlTreino);
                Map<String, String> params = new HashMap<String, String>();
                params.put("codigopessoa", String.valueOf(pessoaVO.getCodigo()));
                params.put("removerFoto", "true");
                params.put("fotoKey", fotoKey);
                ExecuteRequestHttpService.executeRequest(urlTreino + "/prest/config/" + chave + "/baixarFotoAlunoPorFotoKey", params);
            } catch (Exception ex) {
                System.out.println("Problema ao enviar solicitação para remover a foto do aluno");
                problemaTrocarFotoTreino = ex.getMessage();
            }

            try {
                StringBuilder sb = new StringBuilder();
                if (UteisValidacao.emptyString(problemaTrocarFotoTreino)) {
                    sb.append("Foto removida com sucesso. ");
                } else {
                    sb.append("Foto removida parcialmente. ");
                }

                if (!UteisValidacao.emptyString(problemaTrocarFotoTreino)) {
                    sb.append("Problemas ao remover a foto no Treino: ").append(problemaTrocarFotoTreino);
                }

                registrarLog(pessoaVO.getCodigo(), usuarioVO,
                        "REMOVER FOTO", "Pessoa", "Remover Foto - Pessoa", "Foto", "", sb.toString(), con);
            } catch (Exception ex) {
                registrarLog(pessoaVO.getCodigo(), usuarioVO,
                        "ERRO AO CRIAR LOG", "Pessoa", "Remover Foto - Pessoa", "Erro", "", "", con);
            }

            return EnvelopeRespostaDTO.of("ok");
        } finally {
            pessoaDAO = null;
            usuarioDAO = null;
        }
    }

    private void registrarLog(Integer codigoPessoa, UsuarioVO usuarioVO,
                              String operacao, String entidade, String entidadeDescricao,
                              String nomeCampo, String valorAnterior, String valorAlterado,
                              Connection con) throws Exception {
        Log lodDAO;
        try {
            lodDAO = new Log(con);

            LogVO log = new LogVO();
            log.setOperacao(operacao);
            if ("Erro".equals(nomeCampo)) {
                log.setChavePrimaria("");
            } else {
                log.setChavePrimaria(codigoPessoa.toString());
            }
            if (usuarioVO == null) {
                log.setResponsavelAlteracao("WEBSERVICE");
            } else {
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            }
            log.setNomeEntidade(entidade);
            log.setNomeEntidadeDescricao(entidadeDescricao);
            log.setDataAlteracao(Calendario.hoje());
            log.setNomeCampo(nomeCampo);
            log.setValorCampoAnterior(valorAnterior);
            log.setValorCampoAlterado(valorAlterado);
            log.setPessoa(codigoPessoa);
            lodDAO.incluirSemCommit(log);
        } finally {
            lodDAO = null;
        }
    }

    private EnvelopeRespostaDTO configuracaoRecibo(ServletRequest request, Connection con) throws Exception {
        Empresa empresaDAO;
        Usuario usuarioDAO;
        try {
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            Integer usuario = UteisValidacao.converterInteiro(request.getParameter("usuario"));

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            boolean permissaoGestaoNFCe = false;
            try {
                if (empresaVO.isUsarNFCe()) {
                    validarPermissao(empresaVO, "GestaoNFCe", "4.38 - Acessar e enviar NFC-e através da gestão de NFC-e", usuarioVO, con);
                    permissaoGestaoNFCe = true;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            JSONObject ret = new JSONObject();
            ret.put("apresentarBotoesNFSe", empresaVO.isApresentarBotoesFaturamentoNFSe());
            ret.put("apresentarBotoesNFCe", empresaVO.isUsarNFCe() && permissaoGestaoNFCe);
            ret.put("apresentarStatusConciliadora", empresaVO.isUsarConciliadora());
            return EnvelopeRespostaDTO.of(ret);
        } finally {
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO atualizarSintetico(ServletRequest request, Connection con) throws Exception {
        Cliente clienteDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            clienteDAO = new Cliente(con);
            zillyonWebFacade = new ZillyonWebFacade(con);

            Integer cliente = UteisValidacao.converterInteiro(request.getParameter("cliente"));

            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);

            zillyonWebFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);

            try {
                MgbService mgbService = new MgbServiceImpl(con);
                mgbService.syncAlunoMgb(null, cliente, null);
            } catch (Exception e) {
                Uteis.logar(e, TelaClienteServlet.class);
            }

            return EnvelopeRespostaDTO.of("ok");
        } finally {
            clienteDAO = null;
            zillyonWebFacade = null;
        }
    }

    private EnvelopeRespostaDTO atualizarAvisosPendencias(ServletRequest request, Connection con) throws Exception {
        Cliente clienteDAO;
        ConfiguracaoSistema configuracaoSistemaDAO;

        try {
            clienteDAO = new Cliente(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            String chave = obterChave(request);

            Conexao.guardarConexaoForJ2SE(chave, con);

            Integer cliente = UteisValidacao.converterInteiro(request.getParameter("cliente"));

            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_TODOS);

            clienteDAO.gerarPendenciaCliente(clienteVO, false, null, configuracaoSistemaVO);
            return EnvelopeRespostaDTO.of("ok");
        } finally {
            clienteDAO = null;
            configuracaoSistemaDAO = null;
        }
    }

    private EnvelopeRespostaDTO validarParcelaAbertaComBoletoPendente(ServletRequest request, Connection con) throws Exception {
        MovParcela movParcelaDAO;

        try {
            movParcelaDAO = new MovParcela(con);
            String chave = obterChave(request);

            Conexao.guardarConexaoForJ2SE(chave, con);

            Integer codContrato = UteisValidacao.converterInteiro(request.getParameter("codContrato"));

            List<MovParcelaVO> listaParcelasContrato = movParcelaDAO.consultarPorContrato(codContrato, Uteis.NIVELMONTARDADOS_MINIMOS);

            for (MovParcelaVO movParcelaVO : listaParcelasContrato) {
                boolean existeBoletoPendenteDeParcela = getFacade().getBoleto().existeBoletoPendentePorMovParcela(movParcelaVO.getCodigo(), true);

                if (existeBoletoPendenteDeParcela) {
                    throw new ConsistirException("Existem um ou mais boletos pendentes para este contrato. Caso queira fazer uma manutenção de modalidade, é necessário cancelar todos os boletos pendentes das parcelas deste contrato antes de prosseguir.");
                }
            }

            return EnvelopeRespostaDTO.of("ok");
        } finally {
            movParcelaDAO = null;
        }
    }


    private EnvelopeRespostaDTO excluirAnexoZW(HttpServletRequest request, Connection con) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codPessoa = jsonObject.getInt("pessoa");
            String usuarioUsername = jsonObject.optString("username");
            Integer codUsuario = jsonObject.optInt("usuario");


            UsuarioVO usuarioVO = null;
            if (!UteisValidacao.emptyString(usuarioUsername)) {
                usuarioVO = usuarioDAO.consultarPorUsername(usuarioUsername, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
            if (!UteisValidacao.emptyNumber(codUsuario) &&
                    (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo()))) {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuario não informado");
            }

            if (UteisValidacao.emptyNumber(codPessoa)) {
                throw new Exception("Pessoa não informado");
            }
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);

            clienteDAO.excluirAnexo(clienteVO, usuarioVO);
            return EnvelopeRespostaDTO.of("ok");
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
        }
    }

    private EnvelopeRespostaDTO excluirClienteMensagemProdutoVencido(HttpServletRequest request, Connection con) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        ClienteMensagem clienteMensagemDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            clienteMensagemDAO = new ClienteMensagem(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codPessoa = jsonObject.getInt("pessoa");
            Integer codClienteMensagem = jsonObject.getInt("clienteMensagem");
            String usuarioUsername = jsonObject.optString("username");
            Integer codUsuario = jsonObject.optInt("usuario");

            if (UteisValidacao.emptyNumber(codClienteMensagem)) {
                throw new Exception("Cliente Mensagem não informado");
            }
            UsuarioVO usuarioVO = null;
            if (!UteisValidacao.emptyString(usuarioUsername)) {
                usuarioVO = usuarioDAO.consultarPorUsername(usuarioUsername, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
            if (!UteisValidacao.emptyNumber(codUsuario) &&
                    (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo()))) {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuario não informado");
            }

            if (UteisValidacao.emptyNumber(codPessoa)) {
                throw new Exception("Pessoa não informado");
            }
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            ClienteMensagemVO clienteMensagemVO = clienteMensagemDAO.consultarPorChavePrimaria(codClienteMensagem, Uteis.NIVELMONTARDADOS_TODOS);
            clienteMensagemDAO.excluirClienteMensagemProdutoVencido(clienteVO, clienteMensagemVO, usuarioVO);
            return EnvelopeRespostaDTO.of("ok");
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            clienteMensagemDAO = null;
        }
    }

    private EnvelopeRespostaDTO desbloquearMsgProdutoVencido(HttpServletRequest request, Connection con) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        ClienteMensagem clienteMensagemDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            clienteMensagemDAO = new ClienteMensagem(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codPessoa = jsonObject.getInt("pessoa");
            Integer codClienteMensagem = jsonObject.getInt("clienteMensagem");
            String usuarioUsername = jsonObject.optString("username");
            Integer codUsuario = jsonObject.optInt("usuario");

            if (UteisValidacao.emptyNumber(codClienteMensagem)) {
                throw new Exception("Cliente Mensagem não informado");
            }
            UsuarioVO usuarioVO = null;
            if (!UteisValidacao.emptyString(usuarioUsername)) {
                usuarioVO = usuarioDAO.consultarPorUsername(usuarioUsername, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
            if (!UteisValidacao.emptyNumber(codUsuario) &&
                    (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo()))) {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuario não informado");
            }

            if (UteisValidacao.emptyNumber(codPessoa)) {
                throw new Exception("Pessoa não informado");
            }
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            ClienteMensagemVO clienteMensagemVO = clienteMensagemDAO.consultarPorChavePrimaria(codClienteMensagem, Uteis.NIVELMONTARDADOS_TODOS);
            clienteMensagemDAO.desbloquearMsgProdutoVencido(clienteVO, clienteMensagemVO, usuarioVO);
            return EnvelopeRespostaDTO.of("ok");
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            clienteMensagemDAO = null;
        }
    }

    private EnvelopeRespostaDTO alterarObjecaoCliente(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codCliente = jsonObject.optInt("cliente");
            Integer codUsuario = jsonObject.optInt("usuario");
            Integer codObjecao = jsonObject.optInt("objecao");
            if (UteisValidacao.emptyNumber(codCliente)) {
                throw new Exception("Cliente não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            clienteVO.getObjecao().setCodigo(codObjecao);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            clienteDAO.alterarObjecaoCliente(clienteVO, usuarioVO, true);
            return EnvelopeRespostaDTO.of("ok");
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
        }
    }

    private EnvelopeRespostaDTO boleto(Connection con, HttpServletRequest request) throws Exception {

        String chave = obterChave(request);
        JSONObject jsonObject = new JSONObject(obterBody(request));
        String tipoOperacao = jsonObject.getString("tipo");
        Integer remessa_item = jsonObject.optInt("remessa_item");
        Integer boleto = jsonObject.optInt("boleto");
        Integer usuarioLogado = jsonObject.optInt("usuario");
        int empresa = jsonObject.optInt("empresa");

        List<BoletoVO> boletosOnline = new ArrayList<>();
        List<RemessaItemVO> boletosRemessa = new ArrayList<>();
        JSONArray boletosJson = jsonObject.optJSONArray("boletos");
        if (boletosJson != null) {
            for (int i = 0; i < boletosJson.length(); i++) {
                JSONObject jsonBol = boletosJson.getJSONObject(i);
                Integer tipoCobranca = jsonBol.optInt("tipo_cobranca");
                Integer codigo = jsonBol.optInt("codigo");
                if (tipoCobranca.equals(TipoCobrancaEnum.BOLETO_ONLINE.getId())) {
                    BoletoVO boletoVO = new BoletoVO();
                    boletoVO.setCodigo(codigo);
                    boletosOnline.add(boletoVO);
                }  else if (tipoCobranca.equals(TipoCobrancaEnum.BOLETO.getId())) {
                    RemessaItemVO itemVO = new RemessaItemVO();
                    itemVO.setCodigo(codigo);
                    boletosRemessa.add(itemVO);
                }
            }
        }

        switch (tipoOperacao) {
            case "detalhe":
                return EnvelopeRespostaDTO.of(new TelaClienteBoleto().detalhes(boleto, usuarioLogado, con));
            case "imprimir":
                String urlImprimir = new TelaClienteBoleto().imprimirRemessaItem(chave, remessa_item, request, con);
                return EnvelopeRespostaDTO.of(urlImprimir);
            case "imprimirBBOnline":
                BoletoService service = new BoletoService(con);
                String urlPDFBoleto = service.imprimirBoletoBancoBrasilNovaTelaCliente(boleto, request);
                return EnvelopeRespostaDTO.of(urlPDFBoleto);
            case "imprimirBoletoOnlineNaoRegistrado":
                String urlImprimirBoleto = new TelaClienteBoleto().imprimirBoletosOnlineNaoRegistrado(boleto, usuarioLogado, con);
                return EnvelopeRespostaDTO.of(urlImprimirBoleto);
            case "imprimirBoletos":
                return EnvelopeRespostaDTO.of(new TelaClienteBoleto().imprimirBoletos(boletosOnline, boletosRemessa, con, request));
            case "emailBBOnline":
                BoletoService serviceB = new BoletoService(con);
                boolean sucesso = serviceB.enviarEmailClienteBoletoBancoBrasil(boleto, request, usuarioLogado, empresa);
                return EnvelopeRespostaDTO.of(sucesso);
            case "cancelar":
                new TelaClienteBoleto().cancelar(boleto, usuarioLogado, con);
                return EnvelopeRespostaDTO.of("ok");
            case "cancelarBoletos":
                return EnvelopeRespostaDTO.of(new TelaClienteBoleto().cancelarBoletos(boletosOnline, boletosRemessa, usuarioLogado, con));
            case "sincronizar":
                return EnvelopeRespostaDTO.of(new TelaClienteBoleto().sincronizar(boleto, usuarioLogado, con));
            case "sincronizarBoletos":
                return EnvelopeRespostaDTO.of(new TelaClienteBoleto().sincronizarBoletos(boletosOnline, boletosRemessa, usuarioLogado, con));
            case "email":
                if (!UteisValidacao.emptyNumber(boleto)) {
                    new TelaClienteBoleto().enviarEmailBoleto(chave, boleto, usuarioLogado, null, request, con);
                } else {
                    new TelaClienteBoleto().enviarEmailRemessaItem(chave, remessa_item, usuarioLogado, null, request, con);
                }
                return EnvelopeRespostaDTO.of("ok");
            case "emailBoletos":
                return EnvelopeRespostaDTO.of(new TelaClienteBoleto().enviarEmailBoletos(boletosOnline, boletosRemessa, usuarioLogado, con, request));
            case "remover_parcela":
                new TelaClienteBoleto().removerParcelaBoleto(chave, remessa_item, usuarioLogado, request, con);
                return EnvelopeRespostaDTO.of("ok");
            default:
                throw new Exception("Operação não encontrada");
        }

    }

    private EnvelopeRespostaDTO notaFiscal(Connection con, HttpServletRequest request) throws Exception {
        NotaFiscal notaFiscalDAO;
        Usuario usuarioDAO;
        Email emailDAO;
        try {
            notaFiscalDAO = new NotaFiscal(con);
            usuarioDAO = new Usuario(con);
            emailDAO = new Email(con);

            JSONObject jsonObject = new JSONObject(obterBody(request));
            String tipoOperacao = jsonObject.optString("tipo");
            Integer codNotaFiscal = jsonObject.optInt("notafiscal");
            Integer codUsuarioLogado = jsonObject.optInt("usuario");

            if (UteisValidacao.emptyNumber(codNotaFiscal)) {
                throw new Exception("Nota Fiscal não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuarioLogado)) {
                throw new Exception("Usuario não informado");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuarioLogado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            switch (tipoOperacao) {
                case "email":
                    String email = jsonObject.optString("email");
                    NotaFiscalVO notaFiscalVO = notaFiscalDAO.consultarPorChavePrimaria(codNotaFiscal, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (UteisValidacao.emptyString(email)) {
                        List<EmailVO> emailVOS = emailDAO.consultarEmails(notaFiscalVO.getPessoaVO().getCodigo(), true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (UteisValidacao.emptyList(emailVOS)) {
                            emailVOS = emailDAO.consultarEmails(notaFiscalVO.getPessoaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        }
                        email = UteisValidacao.emptyList(emailVOS) ? "" : emailVOS.get(0).getEmail();
                        if (UteisValidacao.emptyString(email)) {
                            throw new Exception("Cliente não tem e-mail");
                        }
                    }
                    String retorno = notaFiscalDAO.enviarEmailNotaFiscal(email, notaFiscalVO, usuarioVO, "");
                    return EnvelopeRespostaDTO.of(retorno);
                default:
                    throw new Exception("Operação não encontrada");
            }
        } finally {
            notaFiscalDAO = null;
            usuarioDAO = null;
            emailDAO = null;
        }
    }

    private EnvelopeRespostaDTO pactoPay(Connection con, HttpServletRequest request) throws Exception {
        JSONObject jsonBody = new JSONObject(obterBody(request));
        String tipoOperacao = jsonBody.getString("tipo");

        switch (tipoOperacao) {
            case "retentar":
                return retentarTransacao(jsonBody, con, request);
            case "sincronizar":
                return sincronizarTransacao(jsonBody, con, request);
            case "linkComprovanteCancelamento":
                return linkCancelamentoTransacao(jsonBody, con, request);
            case "emailComprovanteCancelamento":
                return emailCancelamentoTransacao(jsonBody, con, request);
            default:
                throw new Exception("Operação não encontrada");
        }
    }

    private EnvelopeRespostaDTO retentarTransacao(JSONObject jsonBody, Connection con, HttpServletRequest request) throws Exception {
        Transacao transacaoDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        ConvenioCobranca convenioCobrancaDAO;
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO;
        try {
            transacaoDAO = new Transacao(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(con);

            Integer codTransacao = jsonBody.getInt("transacao");
            Integer codUsuario = jsonBody.optInt("usuario");

            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informada");
            }
            if (UteisValidacao.emptyNumber(codTransacao)) {
                throw new Exception("Transação não informada");
            }

            String ipCliente = obterIpCliente(request);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(codTransacao, Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);
            ConvenioCobrancaVO convenioCobrancaDaTransacaoAntiga = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELA_ALUNO);
            //Se o convênio estiver inativo, usar o convênio da autorização atual do aluno
            if (convenioCobrancaDaTransacaoAntiga.getSituacao() != null &&
                    convenioCobrancaDaTransacaoAntiga.getSituacao().equals(SituacaoConvenioCobranca.INATIVO)) {
                if (transacaoVO.getPessoaPagador() != null &&
                        !UteisValidacao.emptyNumber(transacaoVO.getPessoaPagador().getCodigo())) {
                    List<AutorizacaoCobrancaVO> auts = new ArrayList<AutorizacaoCobrancaVO>();
                    if (UteisValidacao.emptyList(auts)) {
                        throw new Exception("Convênio da transação original antiga está inativo e o cliente não possui nenhuma autorização ativa para uma nova tentativa de cobrança");
                    }
                    auts.addAll(autorizacaoCobrancaClienteDAO.consultarPorPessoaTipoAutorizacao(
                            transacaoVO.getPessoaPagador().getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    transacaoVO.setConvenioCobrancaVO(auts.get(0).getConvenio());
                } else {
                    throw new Exception("Convênio da transação original antiga está inativo e não foi possível obter a pessoa para retentar com a autorização vigente do aluno.");
                }
            }
            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            transacaoVO.setEmpresaVO(empresaVO);
            transacaoVO.setListaParcelas(transacaoDAO.obterParcelasDaTransacao(transacaoVO));
            String retorno = transacaoDAO.retentativaTransacao(transacaoVO, usuarioVO, transacaoVO.getConvenioCobrancaVO().getCodigo(), ipCliente,
                    OrigemCobrancaEnum.ZW_MANUAL_RETENTATIVA_TELA_ALUNO_V2);
            return EnvelopeRespostaDTO.of(retorno);
        } finally {
            transacaoDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            convenioCobrancaDAO = null;
            autorizacaoCobrancaClienteDAO = null;
        }
    }

    private EnvelopeRespostaDTO sincronizarTransacao(JSONObject jsonBody, Connection con, HttpServletRequest request) throws Exception {
        Transacao transacaoDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        try {
            transacaoDAO = new Transacao(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);

            Integer codTransacao = jsonBody.getInt("transacao");
            Integer codUsuario = jsonBody.optInt("usuario");

            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informada");
            }
            if (UteisValidacao.emptyNumber(codTransacao)) {
                throw new Exception("Transação não informada");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(codTransacao, Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);
            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            transacaoVO.setEmpresaVO(empresaVO);
            transacaoVO.setListaParcelas(transacaoDAO.obterParcelasDaTransacao(transacaoVO));
            String retorno = transacaoDAO.sincronizarTransacao(transacaoVO, usuarioVO, true);
            return EnvelopeRespostaDTO.of(retorno);
        } finally {
            transacaoDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO linkCancelamentoTransacao(JSONObject jsonBody, Connection con, HttpServletRequest request) throws Exception {
        Transacao transacaoDAO;
        try {
            transacaoDAO = new Transacao(con);

            Integer codTransacao = jsonBody.getInt("transacao");
            if (UteisValidacao.emptyNumber(codTransacao)) {
                throw new Exception("Transação não informada");
            }
            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(codTransacao, Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);
            return EnvelopeRespostaDTO.of(transacaoVO.getUrlComprovanteCancelamento());
        } finally {
            transacaoDAO = null;
        }
    }

    private EnvelopeRespostaDTO emailCancelamentoTransacao(JSONObject jsonBody, Connection con, HttpServletRequest request) throws Exception {
        Transacao transacaoDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        Email emailDAO;
        try {
            transacaoDAO = new Transacao(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            emailDAO = new Email(con);

            Integer codTransacao = jsonBody.getInt("transacao");
            Integer codUsuario = jsonBody.optInt("usuario");

            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informada");
            }
            if (UteisValidacao.emptyNumber(codTransacao)) {
                throw new Exception("Transação não informada");
            }

            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(codTransacao, Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);
            List<EmailVO> emailVOS = emailDAO.consultarEmails(transacaoVO.getPessoaPagador().getCodigo(), true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyList(emailVOS)) {
                emailVOS = emailDAO.consultarEmails(transacaoVO.getPessoaPagador().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (UteisValidacao.emptyList(emailVOS)) {
                throw new Exception("Cliente não tem e-mail");
            }
            String email = emailVOS.get(0).getEmail();
            transacaoDAO.enviarEmailComprovanteCancelamento(email, transacaoVO);
            return EnvelopeRespostaDTO.of("ok");
        } finally {
            transacaoDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            emailDAO = null;
        }
    }

    private String obterIpCliente(HttpServletRequest request) {
        String ipCliente = "";
        try {
            String xForwardedForHeader = request.getHeader("X-Forwarded-For");
            if (xForwardedForHeader == null) {
                ipCliente = request.getRemoteAddr();
            } else {
                // As of https://en.wikipedia.org/wiki/X-Forwarded-For
                // The general format of the field is: X-Forwarded-For: client, proxy1, proxy2 ...
                // we only want the client
                ipCliente = new StringTokenizer(xForwardedForHeader, ",").nextToken().trim();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return ipCliente;
    }

    private String incluirConvidadoLancarConvite(String chave, ClienteVO clienteAnfitriaoVO, JSONObject jsonBody, Connection con) throws Exception {
        Usuario usuarioDAO;
        Convite conviteDAO;
        VendaRapidaRecorrenteServiceImpl vendaServiceDAO;
        ConfiguracaoSistema configuracaoSistemaDAO;
        Empresa empresaDAO;
        try {
            usuarioDAO = new Usuario(con);
            conviteDAO = new Convite(con);
            vendaServiceDAO = new VendaRapidaRecorrenteServiceImpl(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            empresaDAO = new Empresa(con);

            Conexao.guardarConexaoForJ2SE(chave, con);

            Integer codUsuario = jsonBody.getInt("usuario");
            Integer codEmpresa = jsonBody.getInt("empresa");
            JSONObject jsonDados = jsonBody.optJSONObject("dados");

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setCodigoMatricula(0);
            clienteVO.setMatricula("");
            clienteVO.setVinculoVOs(new ArrayList<>());

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setNome(jsonDados.optString("nomeCompleto"));
            pessoaVO.setCfp(jsonDados.optString("cpf"));
            pessoaVO.setRne(jsonDados.optString("rne"));
            pessoaVO.setSexo(jsonDados.optString("sexo"));

            Long dataNascimento = jsonDados.optLong("nascimento");
            pessoaVO.setDataNasc(UteisValidacao.emptyNumber(dataNascimento) ? null : new Date(dataNascimento));
            pessoaVO.setNomeMae(jsonDados.optString("nomeResponsavel"));
            pessoaVO.setCpfMae(jsonDados.optString("cpfResponsavel"));
            pessoaVO.setEmailMae(jsonDados.optString("emailResponsavel"));

            Long nascimentoResponsavel = jsonDados.optLong("nascimentoResponsavel");
            pessoaVO.setDataNascimentoResponsavel(UteisValidacao.emptyNumber(nascimentoResponsavel) ? null : new Date(nascimentoResponsavel));

            TelefoneVO telefoneCelularVO = new TelefoneVO();
            telefoneCelularVO.setNumero(jsonDados.optString("telefoneCelular"));

            TelefoneVO telefoneResidencialVO = new TelefoneVO();
            telefoneResidencialVO.setNumero(jsonDados.optString("telefoneFixo"));

            JSONObject jsonEndereco = jsonDados.optJSONObject("endereco");
            EnderecoVO enderecoResidencialVO = new EnderecoVO();
            if (jsonEndereco != null) {
                enderecoResidencialVO.setEndereco(jsonEndereco.optString("endereco"));
                enderecoResidencialVO.setComplemento(jsonEndereco.optString("complemento"));
                enderecoResidencialVO.setNumero(jsonEndereco.optString("numero"));
                enderecoResidencialVO.setBairro(jsonEndereco.optString("bairro"));
                enderecoResidencialVO.setCep(jsonEndereco.optString("cep"));
            }

            EnderecoVO enderecoComercialVO = new EnderecoVO();
            JSONObject jsonEnderecoComercial = jsonDados.optJSONObject("enderecoComercial");
            if (jsonEnderecoComercial != null) {
                enderecoComercialVO.setEndereco(jsonEnderecoComercial.optString("endereco"));
                enderecoComercialVO.setComplemento(jsonEnderecoComercial.optString("complemento"));
                enderecoComercialVO.setNumero(jsonEnderecoComercial.optString("numero"));
                enderecoComercialVO.setBairro(jsonEnderecoComercial.optString("bairro"));
                enderecoComercialVO.setCep(jsonEnderecoComercial.optString("cep"));
            }

            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(jsonDados.optString("email"));

            boolean estrangeira = jsonDados.getBoolean("alunoEstrangeiro");

            JSONObject jsonConsultor = jsonDados.optJSONObject("consultor");
            Integer codConsultor = jsonConsultor != null ? jsonConsultor.getInt("codigo") : 0;

            JSONObject jsonEvento = jsonDados.optJSONObject("evento");
            Integer codEvento = jsonEvento != null ? jsonEvento.optInt("codigo") : 0;

            //validar
            vendaServiceDAO.gravar(telefoneCelularVO, new TelefoneVO(),
                    telefoneResidencialVO, enderecoResidencialVO, enderecoComercialVO,
                    clienteVO, pessoaVO, emailVO, usuarioVO, empresaVO,
                    configuracaoSistemaVO, estrangeira, true);

            //gravar
            vendaServiceDAO.gravar(telefoneCelularVO, new TelefoneVO(),
                    telefoneResidencialVO, enderecoResidencialVO, enderecoComercialVO,
                    clienteVO, pessoaVO, emailVO, usuarioVO, empresaVO,
                    configuracaoSistemaVO, estrangeira, false);

            ColaboradorVO consultorVo = new ColaboradorVO();
            consultorVo.setCodigo(codConsultor);
            vendaServiceDAO.adicionarConsultor(clienteVO, consultorVo);
            conviteDAO.lancarConvite(usuarioVO, clienteVO, clienteAnfitriaoVO);
            return clienteVO.getMatricula();
        } finally {
            usuarioDAO = null;
            conviteDAO = null;
            vendaServiceDAO = null;
            configuracaoSistemaDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO aplicativo(Connection con, HttpServletRequest request) throws Exception {
        JSONObject jsonBody = new JSONObject(obterBody(request));
        String tipoOperacao = jsonBody.getString("tipo");
        switch (tipoOperacao) {
            case "email":
                return enviarEmailAplicativo(jsonBody, con, request);
            default:
                throw new Exception("Operação não encontrada");
        }
    }

    private EnvelopeRespostaDTO enviarEmailAplicativo(JSONObject jsonBody, Connection con, HttpServletRequest request) throws Exception {
        Usuario usuarioDAO;
        AcessoControle acessoControle;
        UsuarioMovel usuarioMovelDAO;
        Email emailDAO;
        ZillyonWebFacade zillyonWebFacade;
        Colaborador colaboradorDAO;
        Cliente clienteDAO;
        try {
            usuarioDAO = new Usuario(con);
            usuarioMovelDAO = new UsuarioMovel(con);
            emailDAO = new Email(con);
            zillyonWebFacade = new ZillyonWebFacade(con);
            acessoControle = new AcessoControle(con);
            colaboradorDAO = new Colaborador(con);
            clienteDAO = new Cliente(con);

            String chave = obterChave(request);
            String nomeApp = "";
            String senha = "";
            String username = "";
            String urlAppEmail = "";
            Integer codigoCliente = 0;
            boolean novoAPP = false;

            RequestHttpService httpService = new RequestHttpService();
            String urlTreino = PropsService.getPropertyValue(chave, PropsService.urlTreino);
            RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(urlTreino + "/prest/config/" + chave + "/configuracao-aplicativo", null, null, null, MetodoHttpEnum.GET);
            JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
            if (json.has("return")) {
                String returnValue = json.getString("return");
                returnValue = returnValue.replaceAll("\\\\", "");
                JSONObject nestedObj = new JSONObject(returnValue);
                nomeApp = nestedObj.getString("nomeAppParaEmail");
                urlAppEmail = nestedObj.getString("appUrlEmail");
            } else {
                throw new Exception("Erro consultar configurações do Treino");
            }

            UsuarioMovelVO usuarioMovel;
            boolean appTreino = nomeApp.equalsIgnoreCase("APP Treino - Colaborador") || nomeApp.equalsIgnoreCase("APP Treino - Cliente");

            if (nomeApp.equalsIgnoreCase("APP Treino - Colaborador")) {
                usuarioMovel = usuarioMovelDAO.consultarPorColaborador(colaboradorDAO.consultarPorCodigo(codigoCliente, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else if (nomeApp.equalsIgnoreCase("APP Treino - Cliente")) {
                usuarioMovel = usuarioMovelDAO.consultarPorCodigoCliente(codigoCliente, Uteis.NIVELMONTARDADOS_MINIMOS);
                usuarioMovel.setCliente(clienteDAO.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_MINIMOS));
            } else {
                usuarioMovel = usuarioMovelDAO.consultarPorUserName(username);
            }

            if (!UteisValidacao.emptyNumber(codigoCliente) && !appTreino &&
                    (usuarioMovel == null || UteisValidacao.emptyNumber(usuarioMovel.getCodigo()))) {
                usuarioMovel = clienteDAO.gerarUsuarioMovelAluno(chave, codigoCliente, "", username);
            }

            if (isBlank(senha)) {
                String uuid = UUID.randomUUID().toString();
                senha = uuid.substring(0, uuid.indexOf("-"));
                if (isBlank(senha)) {
                    senha = codigoCliente.toString();
                }
            }

            if (usuarioMovel != null && usuarioMovel.getCodigo() > 0) {
                if (!appTreino) {
                    usuarioMovel.setNome(username);
                } else if (usuarioMovel.getColaborador() != null && UteisValidacao.notEmptyNumber(usuarioMovel.getColaborador().getCodigo())) {
                    usuarioDAO.alterarSenhaUsuario(usuarioDAO.consultarPorColaborador(usuarioMovel.getColaborador().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS), true, Uteis.encriptar(senha));
                }

                usuarioMovel.setSenha(senha);
                usuarioMovelDAO.alterar(usuarioMovel);
            } else {
                throw new ConsistirException("Usuário móvel não existe!");
            }
            SituacaoClienteSinteticoDWVO swCliente = null;
            List<EmailVO> emailVOs = new ArrayList<>();
            if (usuarioMovel.getCliente() != null && UteisValidacao.notEmptyNumber(usuarioMovel.getCliente().getCodigo())) {
                swCliente = zillyonWebFacade.atualizarSintetico(usuarioMovel.getCliente(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CLIENTE, true);
                emailVOs = emailDAO.consultarEmails(usuarioMovel.getCliente().getPessoa().getCodigo(), Boolean.FALSE,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (usuarioMovel.getNome() != null && UteisValidacao.validaEmail(usuarioMovel.getNome())) {
                    EmailVO email = new EmailVO();
                    email.setEmail(usuarioMovel.getNome());
                    emailVOs.add(email);
                }
            }

            if (usuarioMovel.getColaborador() != null && UteisValidacao.notEmptyNumber(usuarioMovel.getColaborador().getCodigo())) {
                if (usuarioMovel.getUsuarioEmailVO() != null && !UteisValidacao.emptyString(usuarioMovel.getUsuarioEmailVO().getEmail())) {
                    EmailVO email = new EmailVO();
                    email.setEmail(usuarioMovel.getUsuarioEmailVO().getEmail());
                    emailVOs.add(email);
                } else {
                    emailVOs = emailDAO.consultarEmails(usuarioMovel.getColaborador().getPessoa().getCodigo(), Boolean.FALSE,
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            }

            if (emailVOs.isEmpty()) {
                throw new ConsistirException("Nenhum email foi encontrado para envio da senha.");
            }

            Set<String> emailsSet = new HashSet<>();
            if (UteisValidacao.validaEmail(username)) {
                emailsSet.add(username);
            }
            for (EmailVO email : emailVOs) {
                emailsSet.add(email.getEmail());
            }
            List<String> emails = new ArrayList<>(emailsSet);
            String[] emailArr = new String[emails.size()];

            acessoControle.enviarEmail(
                    emails.toArray(emailArr), "Informações de acesso ao Pacto Treino",
                    usuarioMovelDAO.gerarCorpoEmailSenhaUsuarioMovel(chave, usuarioMovel.getCliente(), usuarioMovel.getColaborador(), usuarioMovel, senha, novoAPP, urlAppEmail));
            if (usuarioMovel.getCliente() != null && UteisValidacao.notEmptyNumber(usuarioMovel.getCliente().getCodigo())) {
                usuarioMovel.getCliente().setSituacaoClienteSinteticoVO(swCliente);
            }

            TreinoWSConsumer.sincronizarUsuario(chave, usuarioMovel.toUsuarioTreino());
            return EnvelopeRespostaDTO.of("Email enviado!");
        } finally {
            usuarioDAO = null;
            usuarioMovelDAO = null;
            emailDAO = null;
            zillyonWebFacade = null;
            colaboradorDAO = null;
            clienteDAO = null;
        }
    }

    private EnvelopeRespostaDTO importacao(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);

            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer codCliente = jsonBody.getInt("cliente");
            Integer codUsuario = jsonBody.optInt("usuario");
            String tipo = jsonBody.optString("tipo");

            if (UteisValidacao.emptyNumber(codCliente)) {
                throw new Exception("Cliente não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }
            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (tipo.equalsIgnoreCase("verificar")) {
                clienteDAO.verificarCliente(clienteVO, usuarioVO);
                return EnvelopeRespostaDTO.of("ok");
            } else if (tipo.equalsIgnoreCase("desverificar")) {
                clienteDAO.desverificarCliente(clienteVO, usuarioVO);
                return EnvelopeRespostaDTO.of("ok");
            } else {
                throw new Exception("Tipo não identificado");
            }
        } finally {
            clienteDAO = null;
            empresaDAO = null;
        }
    }


    private EnvelopeRespostaDTO ltv(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);

            String chave = obterChave(request);
            JSONObject jsonBody = new JSONObject(obterBody(request));
            String tipo = jsonBody.optString("tipo");

            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            if (tipo.equalsIgnoreCase("cac")) {
                Conexao.guardarConexaoForJ2SE(chave, con);
                LtvControle ltvControle = new LtvControle();
                Double valor = ltvControle.getCacClienteIndividualDouble();
                return EnvelopeRespostaDTO.of(valor.isNaN() ? 0.0 : valor);
            } else {
                throw new Exception("Tipo não identificado");
            }
        } finally {
            clienteDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO contratoDependente(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        ContratoDependente contratoDependenteDAO;
        AfastamentoContratoDependente afastamentoContratoDependenteDAO;
        try {
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            contratoDependenteDAO = new ContratoDependente(con);
            afastamentoContratoDependenteDAO = new AfastamentoContratoDependente(con);

            String chave = obterChave(request);
            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer codUsuario = jsonBody.optInt("usuario");
            Integer codEmpresa = jsonBody.optInt("empresa");
            String tipo = jsonBody.optString("tipo");

            if (UteisValidacao.emptyNumber(codEmpresa)) {
                throw new Exception("Empresa não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }
            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            if (tipo.equalsIgnoreCase("excluir")) {

                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                validarPermissao(empresaVO, "EstornoOperacaoContrato_Autorizar", "3.24 - Estorno de Operação de Contrato - Autorizar", usuarioVO, con);

                Integer codAfastamentoContratoDependente = jsonBody.optInt("afastamentoContratoDependente");
                if (UteisValidacao.emptyNumber(codAfastamentoContratoDependente)) {
                    throw new Exception("Afastamento Contrato Dependente não informado");
                }
                AfastamentoContratoDependenteVO afastamentoVO = afastamentoContratoDependenteDAO.consultarPorChavePrimaria(codAfastamentoContratoDependente, Uteis.NIVELMONTARDADOS_TODOS);
                Optional<ContratoDependenteVO> optional = contratoDependenteDAO.findByCodigo(afastamentoVO.getContratoDependenteVO().getCodigo());
                if (!optional.isPresent()) {
                    throw new Exception("Contrato Dependente não encontrado");
                }
                ContratoDependenteVO contratoDependenteVO = optional.get();

                ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(contratoDependenteVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                afastamentoVO.getContratoDependenteVO().setCliente(clienteVO);

                contratoDependenteDAO.excluirAfastamento(afastamentoVO, usuarioVO);
                return EnvelopeRespostaDTO.of("ok");

            } else if (tipo.equalsIgnoreCase("dados_ferias")) {

                Integer codContratoDependente = jsonBody.optInt("contratoDependente");
                if (UteisValidacao.emptyNumber(codContratoDependente)) {
                    throw new Exception("Contrato Dependente não informado");
                }
                Optional<ContratoDependenteVO> optional = contratoDependenteDAO.findByCodigo(codContratoDependente);
                if (!optional.isPresent()) {
                    throw new Exception("Contrato Dependente não encontrado");
                }
                ContratoDependenteVO contratoDependenteVO = optional.get();
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                return EnvelopeRespostaDTO.of(obterInfoAfastamentoContratoDependenteDTO(empresaVO, contratoDependenteVO, con));

            } else if (tipo.equalsIgnoreCase("gravar_afastamento")) {
                Conexao.guardarConexaoForJ2SE(con);
                Integer codContratoDependente = jsonBody.optInt("contratoDependente");
                if (UteisValidacao.emptyNumber(codContratoDependente)) {
                    throw new Exception("Contrato Dependente não informado");
                }
                Optional<ContratoDependenteVO> optional = contratoDependenteDAO.findByCodigo(codContratoDependente);
                if (!optional.isPresent()) {
                    throw new Exception("Contrato Dependente não encontrado");
                }
                ContratoDependenteVO contratoDependenteVO = optional.get();
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                gravarAfastamentoContratoDependenteDTO(empresaVO, usuarioVO, contratoDependenteVO, jsonBody, con);
                return EnvelopeRespostaDTO.of("ok");
            } else if (tipo.equals("validar_regras_afastamento")){
                Integer codContratoDependente = jsonBody.optInt("codigoContratoDependente");
                if (UteisValidacao.emptyNumber(codContratoDependente)) {
                    throw new Exception("Contrato Dependente não informado");
                }
                String tipoAfastamento = jsonBody.optString("tipoAfastamento");
                if (UteisValidacao.emptyString(tipoAfastamento)) {
                    throw new Exception("Tipo Afastamento não informado");
                }
                Optional<ContratoDependenteVO> optional = contratoDependenteDAO.findByCodigo(codContratoDependente);
                if (!optional.isPresent()) {
                    throw new Exception("Contrato Dependente não encontrado");
                }

                AfastamentoContratoDependenteVO afastamentoContratoDependenteVO = new AfastamentoContratoDependenteVO();
                afastamentoContratoDependenteVO.setContratoDependenteVO(optional.get());
                afastamentoContratoDependenteVO.setTipoAfastamento(tipoAfastamento);
                afastamentoContratoDependenteVO.validarRegrasAfastamento();

                List<ContratoDependenteVO> cdVOS = getFacade().getContratoDependente().findAllByCliente(afastamentoContratoDependenteVO.getContratoDependenteVO().getCliente(), 999);
                Ordenacao.ordenarListaReverse(cdVOS, "dataFinalAjustada");
                if (cdVOS.size() > 0 && afastamentoContratoDependenteVO.getCodigo().equals(cdVOS.get(0).getCodigo())) {
                    throw new ConsistirException("Só é possível lançar um afastamento para o contrato mais recente do cliente.");
                }

                return EnvelopeRespostaDTO.of(true);
            } else {
                throw new Exception("Tipo não identificado");
            }
        } finally {
            clienteDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            contratoDependenteDAO = null;
            afastamentoContratoDependenteDAO = null;
        }
    }

    private InfoAfastamentoContratoDependenteDTO obterInfoAfastamentoContratoDependenteDTO(EmpresaVO empresaVO,
                                                                                           ContratoDependenteVO contratoDependenteVO,
                                                                                           Connection con) throws Exception {
        AfastamentoContratoDependente afastamentoContratoDependenteDAO;
        Contrato contratoDAO;
        Empresa empresaDAO;
        ConfiguracaoSistema configuracaoSistemaDAO;
        try {
            afastamentoContratoDependenteDAO = new AfastamentoContratoDependente(con);
            contratoDAO = new Contrato(con);
            empresaDAO = new Empresa(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);

            Integer carencia = null;
            if (empresaVO != null && empresaVO.getCodigo() != 0) {
                carencia = empresaDAO.obterCarenciaEmpresa(empresaVO.getCodigo());
                if (UteisValidacao.emptyNumber(carencia)) {
                    carencia = configuracaoSistemaDAO.obterCarenciaConfiguracao();
                }
            } else {
                carencia = configuracaoSistemaDAO.obterCarenciaConfiguracao();
            }
            if (carencia == null) {
                carencia = 0;
            }

            ContratoVO contratoOriginal = contratoDAO.consultarPorChavePrimaria(contratoDependenteVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            List<AfastamentoContratoDependenteVO> afastamentos = afastamentoContratoDependenteDAO.consultarPorContratoDependente(contratoDependenteVO.getCodigo(), "CR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            int nrDiasUsados = 0;
            for (AfastamentoContratoDependenteVO afastamento : afastamentos) {
                nrDiasUsados += afastamento.getNrDias();
            }

            AfastamentoContratoDependenteVO afastamentoVO = new AfastamentoContratoDependenteVO();
            afastamentoVO.setContratoDependenteVO(contratoDependenteVO);
            afastamentoVO.setContratoVO(contratoOriginal);
            afastamentoVO.setNrDiasFeriasPermitido(contratoOriginal.getContratoDuracao().getCarencia());
            afastamentoVO.setNrDiasUsados(nrDiasUsados);
            afastamentoVO.setNrDiasRestam(afastamentoVO.getNrDiasFeriasPermitido() - afastamentoVO.getNrDiasUsados());

            InfoAfastamentoContratoDependenteDTO dto = new InfoAfastamentoContratoDependenteDTO();
            dto.setDiasPermitidos(contratoOriginal.getContratoDuracao().getCarencia());
            dto.setDiasUtilizados(nrDiasUsados);
            dto.setDiasRestantes(afastamentoVO.getNrDiasFeriasPermitido() - afastamentoVO.getNrDiasUsados());
            dto.setDiasMinimoSolicitar(carencia);
            return dto;
        } finally {
            afastamentoContratoDependenteDAO = null;
            contratoDAO = null;
            empresaDAO = null;
            configuracaoSistemaDAO = null;
        }
    }

    private void gravarAfastamentoContratoDependenteDTO(EmpresaVO empresaVO, UsuarioVO usuarioVO,
                                                        ContratoDependenteVO contratoDependenteVO,
                                                        JSONObject jsonBody, Connection con) throws Exception {
        AfastamentoContratoDependente afastamentoContratoDependenteDAO;
        Contrato contratoDAO;
        Empresa empresaDAO;
        Cliente clienteDAO;
        ConfiguracaoSistema configuracaoSistemaDAO;
        ContratoDependente contratoDependenteDAO;
        JustificativaOperacao justificativaOperacaoDAO;
        try {
            afastamentoContratoDependenteDAO = new AfastamentoContratoDependente(con);
            contratoDAO = new Contrato(con);
            empresaDAO = new Empresa(con);
            clienteDAO = new Cliente(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            contratoDependenteDAO = new ContratoDependente(con);
            justificativaOperacaoDAO = new JustificativaOperacao(con);


            AfastamentoContratoDependenteVO afastamentoVO = new AfastamentoContratoDependenteVO();

            String tipoOperacao = jsonBody.optString("tipoOperacao");
            if (tipoOperacao.equalsIgnoreCase("CR")) { //férias
                afastamentoVO.setTipoAfastamento("CR");
            } else if (tipoOperacao.equalsIgnoreCase("AT")) { //atestado
                afastamentoVO.setTipoAfastamento("AT");
            } else {
                throw new Exception("Tipo Operação não identificado");
            }

            Date dtInicio = Calendario.getDate("yyyyMMdd", jsonBody.optString("dtInicio"));
            Date dtFim = Calendario.getDate("yyyyMMdd", jsonBody.optString("dtFim"));
            afastamentoVO.setDataInicio(dtInicio);
            afastamentoVO.setDataTermino(dtFim);

            if (Calendario.maior(afastamentoVO.getDataInicio(), afastamentoVO.getDataTermino())) {
                throw new Exception("O campo ATÉ não pode ser antes do campo INÍCIO");
            }

            Integer codJustificativa = jsonBody.optInt("justificativa");
            if (UteisValidacao.emptyNumber(codJustificativa)) {
                throw new Exception("Justificativa não informada");
            }
            JustificativaOperacaoVO justificativaOperacaoVO = justificativaOperacaoDAO.consultarPorChavePrimaria(codJustificativa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            afastamentoVO.setJustificativa(justificativaOperacaoVO.getCodigo());
            afastamentoVO.setObservacao(jsonBody.optString("observacao"));

            Integer carencia = null;
            if (empresaVO != null && empresaVO.getCodigo() != 0) {
                carencia = empresaDAO.obterCarenciaEmpresa(empresaVO.getCodigo());
                if (UteisValidacao.emptyNumber(carencia)) {
                    carencia = configuracaoSistemaDAO.obterCarenciaConfiguracao();
                }
            } else {
                carencia = configuracaoSistemaDAO.obterCarenciaConfiguracao();
            }
            if (carencia == null) {
                carencia = 0;
            }

            Long nrDiasSomar = Uteis.nrDiasEntreDatas(dtInicio, dtFim);
            ContratoVO contratoOriginal = contratoDAO.consultarPorChavePrimaria(contratoDependenteVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            List<AfastamentoContratoDependenteVO> afastamentos = afastamentoContratoDependenteDAO.consultarPorContratoDependente(contratoDependenteVO.getCodigo(), "CR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            int nrDiasUsados = 0;
            for (AfastamentoContratoDependenteVO afastamento : afastamentos) {
                nrDiasUsados += afastamento.getNrDias();
            }

            afastamentoVO.setContratoDependenteVO(contratoDependenteVO);
            afastamentoVO.setContratoVO(contratoOriginal);
            afastamentoVO.setNrDiasFeriasPermitido(contratoOriginal.getContratoDuracao().getCarencia());
            afastamentoVO.setNrDiasSomar(nrDiasSomar.intValue() + 1);
            afastamentoVO.setNrDiasUsados(nrDiasUsados);
            afastamentoVO.setNrDiasRestam(afastamentoVO.getNrDiasFeriasPermitido() - afastamentoVO.getNrDiasUsados());
            afastamentoVO.setResponsavelOperacao(usuarioVO);

            gerarPeriodoRetornoCarencia(empresaVO, usuarioVO, afastamentoVO, contratoDependenteVO, carencia, con);

            ClienteVO cliente = clienteDAO.consultarPorChavePrimaria(contratoDependenteVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            afastamentoVO.getContratoDependenteVO().setCliente(cliente);

            List<AfastamentoContratoDependenteVO> afastamentosLancados = afastamentoContratoDependenteDAO.consultarPorContratoDependente(contratoDependenteVO.getCodigo(), "", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            afastamentoVO.validarPeriodoCarencia(afastamentosLancados);

            contratoDependenteDAO.incluirAfastamento(afastamentoVO);
        } finally {
            afastamentoContratoDependenteDAO = null;
            contratoDAO = null;
            empresaDAO = null;
            configuracaoSistemaDAO = null;
        }
    }

    private void gerarPeriodoRetornoCarencia(EmpresaVO empresaVO, UsuarioVO usuarioVO,
                                             AfastamentoContratoDependenteVO afastamentoVO,
                                             ContratoDependenteVO contratoDependenteVO,
                                             Integer carencia, Connection con) throws Exception {
        Cliente clienteDAO;
        AcessoCliente acessoClienteDAO;
        try {
            clienteDAO = new Cliente(con);
            acessoClienteDAO = new AcessoCliente(con);

            if (afastamentoVO.getDataInicio() != null && afastamentoVO.getNrDiasSomar() != 0) {
                Date novaData = Uteis.somarDias(afastamentoVO.getDataInicio(), (afastamentoVO.getNrDiasSomar() - 1)); // -1 porque o primeiro dia dev
                afastamentoVO.setDataTermino(novaData);
            }

            if (afastamentoVO.getDataInicio() != null && afastamentoVO.getDataTermino() != null) {
                afastamentoVO.validarDatasAfastamento();

                Date dataAnteriorOperacao = Uteis.obterDataAnterior(afastamentoVO.getDataInicio(), 1);

                long nrDiasTotal = Uteis.nrDiasEntreDatas(contratoDependenteVO.getDataInicio(), contratoDependenteVO.getDataFinalAjustada());
                long nrDiasUsados = Uteis.nrDiasEntreDatas(contratoDependenteVO.getDataInicio(), dataAnteriorOperacao);
                long nrDiasRestantes = nrDiasTotal - nrDiasUsados;

                afastamentoVO.setNrDiasContrato((int) nrDiasTotal);
                afastamentoVO.setNrDiasUsadoContrato((int) nrDiasUsados);
                afastamentoVO.setNrDiasRestanteContrato((int) nrDiasRestantes);

                if (afastamentoVO.getNrDiasRestanteContrato() <= 0) {
                    throw new ConsistirException(String.format("Não é possível lançar "
                                    + "um afastamento nesse período, pois o número de dias "
                                    + "utilizados (%s) é maior ou igual ao número de dias do contrato (%s).",
                            nrDiasUsados, nrDiasTotal));
                }

                if (afastamentoVO.isFerias()) {
                    long diferenca = Uteis.nrDiasEntreDatas(afastamentoVO.getDataInicio(), afastamentoVO.getDataTermino()) + 1;
                    afastamentoVO.setPeriodoCarencia(diferenca);
                    if (afastamentoVO.getPeriodoCarencia().intValue() < carencia) {
                        throw new ConsistirException("O período do afastamento deve ser maior do que a quantidade mínima de férias");
                    }
                }


                long diasAfastamento = Uteis.nrDiasEntreDatas(afastamentoVO.getDataInicio(), afastamentoVO.getDataTermino()) + 1;

                afastamentoVO.setDataInicioRetorno(Uteis.obterDataFutura2(afastamentoVO.getDataTermino(), 1));

                //diasRestante pode ser negativo, portanto utilizar os dias da Carência e não o restante do contrato
                if (Calendario.maior(afastamentoVO.getDataTermino(), contratoDependenteVO.getDataFinalAjustada())) {
                    afastamentoVO.setQtdDiasCarenciaMaiorQueContrato(true);
                    afastamentoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(afastamentoVO.getDataTermino(), afastamentoVO.getNrDiasRestanteContrato()));
                    afastamentoVO.setNrDias(afastamentoVO.getNrDiasRestanteContrato());
                } else {
                    afastamentoVO.setQtdDiasCarenciaMaiorQueContrato(false);
                    if (afastamentoVO.getNrDiasRestanteContrato() < (int) diasAfastamento) { // contrato em periodo de bonus
                        afastamentoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(afastamentoVO.getContratoVO().getVigenciaAteAjustada(), afastamentoVO.getNrDiasRestanteContrato()));
                        afastamentoVO.setNrDias(afastamentoVO.getNrDiasRestanteContrato());
                    } else {
                        afastamentoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(afastamentoVO.getContratoVO().getVigenciaAteAjustada(), (int) diasAfastamento));
                        afastamentoVO.setNrDias((int) diasAfastamento);
                    }
                }

                ClienteVO cliente = clienteDAO.consultarPorCodigoPessoa(contratoDependenteVO.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                int acessos = acessoClienteDAO.consultarQtdAcessosEntreDatas(cliente, afastamentoVO.getDataInicio(), afastamentoVO.getDataTermino(), false);
                // se cliente teve algum acesso neste periodo
                if (acessos > 0) {
                    validarPermissao(empresaVO, "AtestadoCarencia_Autorizar", "3.18 - Atestado, Trancamento ou Férias com Frequência - Autorizar", usuarioVO, con);
                }
                afastamentoVO.setMensagemErro(false);
            }
        } finally {
            clienteDAO = null;
            acessoClienteDAO = null;
        }
    }

    private EnvelopeRespostaDTO familiares(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        Familiar familiarDAO;
        try {
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            familiarDAO = new Familiar(con);

            String chave = obterChave(request);
            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer codUsuario = jsonBody.optInt("usuario");
            Integer codEmpresa = jsonBody.optInt("empresa");
            String tipo = jsonBody.optString("tipo");

            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            if (tipo.equalsIgnoreCase("info")) {

                Integer codContrato = jsonBody.optInt("contrato");

                Integer codPessoa = jsonBody.optInt("pessoa");
                if (UteisValidacao.emptyNumber(codPessoa)) {
                    throw new Exception("Pessoa não informado");
                }
                InfoFamiliaresDTO dto = preencherDadosPlanoCompartilhado(codPessoa, codContrato, con);
                return EnvelopeRespostaDTO.of(dto);

            } else if (tipo.equalsIgnoreCase("excluir_contrato_dependente")) {

                Conexao.guardarConexaoForJ2SE(chave, con);
                Integer codContratoDependente = jsonBody.optInt("contratoDependente");
                excluirDependenteContrato(codUsuario, codContratoDependente, con);
                return EnvelopeRespostaDTO.of("ok");

            } else if (tipo.equalsIgnoreCase("adicionar_contrato_dependente")) {

                Conexao.guardarConexaoForJ2SE(chave, con);
                Integer codClienteContrato = jsonBody.optInt("clienteContrato");
                Integer codClienteAdicionar = jsonBody.optInt("clienteAdicionar");
                Integer codContrato = jsonBody.optInt("contrato");

                incluirDependenteContrato(codUsuario, codClienteContrato, codClienteAdicionar, codContrato, con);
                return EnvelopeRespostaDTO.of("ok");

            } else if (tipo.equalsIgnoreCase("alterar")) {

                Integer codFamiliar = jsonBody.optInt("familiar");
                if (UteisValidacao.emptyNumber(codFamiliar)) {
                    throw new Exception("Familiar não informado");
                }

                Conexao.guardarConexaoForJ2SE(chave, con);
                FamiliarVO familiarVO = familiarDAO.consultarPorChavePrimaria(codFamiliar, Uteis.NIVELMONTARDADOS_TODOS);
                familiarDAO.alterar(familiarVO);
                return EnvelopeRespostaDTO.of("ok");

            } else {
                throw new Exception("Tipo não identificado");
            }
        } finally {
            clienteDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            familiarDAO = null;
        }
    }

    public InfoFamiliaresDTO preencherDadosPlanoCompartilhado(Integer pessoa, Integer codContrato, Connection con) throws Exception {
        Contrato contratoDAO;
        ContratoDependente contratoDependenteDAO;
        InfoFamiliaresDTO dto = new InfoFamiliaresDTO();
        try {
            contratoDAO = new Contrato(con);
            contratoDependenteDAO = new ContratoDependente(con);

            dto.setQuantidadeCompartilhamentosPlano(0);
            List<ContratoDependenteVO> contratosDependentes = new ArrayList<>();

            List<ContratoVO> contratosAtivos = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(codContrato)) {
                contratosAtivos.add(contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            } else {
                contratosAtivos = contratoDAO.consultarContratosVigentesAtivosPorPessoaOriginal(pessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            for (ContratoVO contrato : contratosAtivos) {
                if (UteisValidacao.notEmptyNumber(contrato.getPlano().getQuantidadeCompartilhamentos())) {
                    contratosDependentes.addAll(contratoDependenteDAO.findAllByContrato(contrato.getCodigo()));
                }
            }
            dto.setQuantidadeCompartilhamentosPlano(contratosDependentes.size());


            int qtdPossivel = contratosDependentes.size();

            int qtdUsada = 0;
            for (ContratoDependenteVO contDep : contratosDependentes) {
                if (contDep.getCliente() != null && contDep.getCliente().getCodigo() > 0) {
                    qtdUsada++;
                }
            }

            boolean permiteCompartilhar = (qtdPossivel - qtdUsada) > 0;
//            dto.setPermiteCompartilharPlano(permiteCompartilhar && situacaoFamiliarPermiteCompartilharPlano);
            dto.setPermiteCompartilharPlano(permiteCompartilhar && true);
            return dto;
        } finally {
            contratoDAO = null;
            contratoDependenteDAO = null;
        }
    }

    public void excluirDependenteContrato(Integer codUsuario, Integer codContratoDependente,
                                          Connection con) throws Exception {
        Cliente clienteDAO;
        ContratoDependente contratoDependenteDAO;
        Familiar familiarDAO;
        Plano planoDAO;
        try {
            clienteDAO = new Cliente(con);
            contratoDependenteDAO = new ContratoDependente(con);
            familiarDAO = new Familiar(con);
            planoDAO = new Plano(con);

            Optional<ContratoDependenteVO> optional = contratoDependenteDAO.findByCodigo(codContratoDependente);
            if (!optional.isPresent()) {
                throw new Exception("Contrato Dependente não encontrado");
            }
            ContratoDependenteVO contratoDependenteVO = optional.get();

            ClienteVO clienteTitularVO = clienteDAO.consultarPorCodigoContrato(contratoDependenteVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            ClienteVO clienteDependenteVO = clienteDAO.consultarPorChavePrimaria(contratoDependenteVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

            FamiliarVO familiarVO = familiarDAO.consultarPorClienteFamiliar(clienteTitularVO.getCodigo(), clienteDependenteVO.getCodigo());
            if (!UteisValidacao.emptyNumber(familiarVO.getCodigo())) {
                familiarVO.setCompartilharPlano(false);
                familiarVO.setContratoCompartilhado(contratoDependenteVO.getContrato().getCodigo());
                familiarVO.setContratoDependente(contratoDependenteVO);
                familiarDAO.alterar(familiarVO);
//                familiarDAO.excluir(familiarVO);

                if (contratoDependenteVO.getContrato().getPlano() != null && !UteisValidacao.emptyNumber(contratoDependenteVO.getContrato().getPlano().getCodigo()) &&
                        planoDAO.enviarDependenteFoguete(contratoDependenteVO.getContrato().getPlano().getCodigo())) {
                    Usuario usuarioDAO = new Usuario(con);
                    UsuarioVO usuarioVO = null;
                    if(!UteisValidacao.emptyNumber(codUsuario)) {
                        usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }
                    usuarioDAO = null;
                    notificarIntegracaoFogueteContratoCliente(con, ClienteFogueteStatusEnum.INACTIVE, contratoDependenteVO.getContrato(), clienteDependenteVO, usuarioVO, false);
                }
            }

            if (!UteisValidacao.emptyNumber(contratoDependenteVO.getCodigo())) {
                contratoDependenteDAO.removerDependenteContrato(contratoDependenteVO.getCodigo());
            }

        } finally {
            clienteDAO = null;
            contratoDependenteDAO = null;
            familiarDAO = null;
            planoDAO = null;
        }
    }

    public void incluirDependenteContrato(Integer codUsuario, Integer codClienteContrato,
                                          Integer codClienteAdicionar, Integer codContrato,
                                          Connection con) throws Exception {
        Contrato contratoDAO;
        ContratoPlanoProdutoSugerido contratoPlanoProdutoSugeridoDAO;
        Cliente clienteDAO;
        ContratoDependente contratoDependenteDAO;
        Familiar familiarDAO;
        Parentesco parentescoDAO;
        Plano planoDAO;
        try {
            contratoDAO = new Contrato(con);
            contratoPlanoProdutoSugeridoDAO = new ContratoPlanoProdutoSugerido(con);
            clienteDAO = new Cliente(con);
            contratoDependenteDAO = new ContratoDependente(con);
            familiarDAO = new Familiar(con);
            parentescoDAO = new Parentesco(con);
            planoDAO = new Plano(con);

            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            InfoFamiliaresDTO infoFamiliaresDTO = preencherDadosPlanoCompartilhado(contratoVO.getPessoa().getCodigo(), contratoVO.getCodigo(), con);
            if (!infoFamiliaresDTO.isPermiteCompartilharPlano()) {
                throw new Exception("Cliente não tem compartilhamento disponível");
            }

            List<ContratoDependenteVO> contratoDependentesCliente = contratoDependenteDAO.findAllByContratoAndCliente(codContrato, codClienteAdicionar);
            if (!UteisValidacao.emptyList(contratoDependentesCliente)) {
                throw new Exception("Cliente já tem um contrato dependente");
            }

            List<ContratoDependenteVO> contratoDependentes = contratoDependenteDAO.findAllByContrato(codContrato);
            ClienteVO clienteTitularVO = clienteDAO.consultarPorChavePrimaria(codClienteContrato, Uteis.NIVELMONTARDADOS_MINIMOS);
            ClienteVO clienteAdicionarVO = clienteDAO.consultarPorChavePrimaria(codClienteAdicionar, Uteis.NIVELMONTARDADOS_MINIMOS);

            FamiliarVO familiarVO = familiarDAO.consultarPorClienteFamiliar(codClienteContrato, codClienteAdicionar);
            familiarVO.setCompartilharPlano(true);
            familiarVO.setCliente(clienteTitularVO.getCodigo());
            familiarVO.setFamiliar(clienteAdicionarVO.getCodigo());
            familiarVO.setCodAcesso(clienteAdicionarVO.getCodAcesso());
            familiarVO.setNome(clienteAdicionarVO.getNome_Apresentar());

            ContratoDependenteVO contratoDependenteVO = contratoDependentes.stream()
                    .filter(it -> it.getCliente().getCodigo().equals(familiarVO.getFamiliar()))
                    .findFirst()
                    .orElse(null);

            if (familiarVO.isCompartilharPlano()) {
                if (contratoDependenteVO == null) {
                    contratoDependenteVO = contratoDependentes.stream()
                            .filter(it -> it.getCliente().getCodigo().equals(0))
                            .findFirst()
                            .orElse(null);
                }

                if (contratoDependenteVO != null) {
                    contratoDependenteVO.getCliente().setCodigo(familiarVO.getFamiliar());
                    familiarVO.setContratoCompartilhado(contratoDependenteVO.getContrato().getCodigo());
                    familiarVO.setContratoDependente(contratoDependenteVO);
                    contratoDependenteVO.getContrato().setContratoPlanoProdutoSugeridoVOs(contratoPlanoProdutoSugeridoDAO.consultarContratoPlanoProdutoSugeridos(contratoDependenteVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                    if (contratoDependenteVO.getContrato().getPlano() != null && !UteisValidacao.emptyNumber(contratoDependenteVO.getContrato().getPlano().getCodigo()) &&
                            planoDAO.enviarDependenteFoguete(contratoDependenteVO.getContrato().getPlano().getCodigo())) {
                        Usuario usuarioDAO = new Usuario(con);
                        UsuarioVO usuarioVO = null;
                        if(!UteisValidacao.emptyNumber(codUsuario)) {
                            usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        }
                        usuarioDAO = null;
                        notificarIntegracaoFogueteContratoCliente(con, ClienteFogueteStatusEnum.ACTIVE, contratoDependenteVO.getContrato(), clienteAdicionarVO, usuarioVO, false);
                    }
                }
            }

            if (UteisValidacao.emptyNumber(familiarVO.getCodigo())) {
                ParentescoVO parentescoVO = parentescoDAO.obterParentescoCriandoSeNaoExiste("DEPENDENTE");
                familiarVO.setParentesco(parentescoVO);
                familiarDAO.incluir(familiarVO, false);
            } else {
                familiarDAO.alterar(familiarVO);
            }

        } finally {
            clienteDAO = null;
            contratoDAO = null;
            familiarDAO = null;
            parentescoDAO = null;
            contratoDependenteDAO = null;
            contratoPlanoProdutoSugeridoDAO = null;
            planoDAO = null;
        }
    }

    private void notificarIntegracaoFogueteContratoCliente(Connection con, ClienteFogueteStatusEnum status, ContratoVO contrato, ClienteVO clienteNotificar, UsuarioVO usuarioVO, boolean isSincronizacaoManual) {
        ZillyonWebFacade zillyonWebFacadeDAO;
        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(con);
            ConfiguracaoIntegracaoFogueteVO confgFoguete = empresaDAO.consultarConfiguracaoIntegracaoFoguete(contrato.getEmpresa().getCodigo());
            if (confgFoguete.isHabilitada()) {
                zillyonWebFacadeDAO = new ZillyonWebFacade(con);
                zillyonWebFacadeDAO.startThreadIntegracaoFoguete(confgFoguete, status, contrato, clienteNotificar, usuarioVO, isSincronizacaoManual);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(ex, ClienteControle.class);
        } finally {
            zillyonWebFacadeDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO armario(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        Familiar familiarDAO;
        Armario armarioDAO;
        ConfiguracaoSistema configuracaoSistemaDAO;
        try {
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            familiarDAO = new Familiar(con);
            armarioDAO = new Armario(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);

            String chave = obterChave(request);
            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer codUsuario = jsonBody.optInt("usuario");
            Integer codEmpresa = jsonBody.optInt("empresa");
            Integer codAluguelArmario = jsonBody.optInt("aluguelArmario");
            String tipo = jsonBody.optString("tipo");

            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            if (tipo.equalsIgnoreCase("alterar_chave_devolvida")) {

                if (UteisValidacao.emptyNumber(codAluguelArmario)) {
                    throw new Exception("Aluguel armário não informado");
                }
                if (!jsonBody.has("devolvida")) {
                    throw new Exception("Devolvida não informada");
                }

                ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                AluguelArmarioVO aluguelArmarioVO = armarioDAO.obterAluguelArmario(codAluguelArmario, configuracaoSistemaVO.getHabilitarGestaoArmarios());
                ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(aluguelArmarioVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                aluguelArmarioVO.setChaveDevolvida(jsonBody.getBoolean("devolvida"));
                clienteDAO.alterarStatusChaveDevolvidaAluguelArmario(aluguelArmarioVO.getCodigo(), aluguelArmarioVO.getChaveDevolvida(),
                        clienteVO.getCodigo(), clienteVO.getEmpresa().getCodigo(), configuracaoSistemaVO.getHabilitarGestaoArmarios());
                return EnvelopeRespostaDTO.of("ok");

            } else if (tipo.equalsIgnoreCase("alterar_contrato_assinado")) {

                if (UteisValidacao.emptyNumber(codAluguelArmario)) {
                    throw new Exception("Aluguel armário não informado");
                }
                if (!jsonBody.has("assinado")) {
                    throw new Exception("Assinado não informada");
                }

                ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                AluguelArmarioVO aluguelArmarioVO = armarioDAO.obterAluguelArmario(codAluguelArmario, configuracaoSistemaVO.getHabilitarGestaoArmarios());

                aluguelArmarioVO.setContratoAssinado(jsonBody.getBoolean("assinado"));
                armarioDAO.gravarContratoAssinadoArmario(aluguelArmarioVO);
                return EnvelopeRespostaDTO.of("ok");

            } else {
                throw new Exception("Tipo não identificado");
            }
        } finally {
            clienteDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            familiarDAO = null;
        }
    }

    private EnvelopeRespostaDTO movproduto(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        MovProduto movProdutoDAO;
        try {
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            movProdutoDAO = new MovProduto(con);

            String chave = obterChave(request);
            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer codUsuario = jsonBody.optInt("usuario");
            Integer codEmpresa = jsonBody.optInt("empresa");
            Integer codMovProduto = jsonBody.optInt("movProduto");
            String tipo = jsonBody.optString("tipo");

            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            if (tipo.equalsIgnoreCase("alterar_renovacao_automatica")) {

                if (UteisValidacao.emptyNumber(codMovProduto)) {
                    throw new Exception("MovProduto não informado");
                }
                if (UteisValidacao.emptyNumber(codUsuario)) {
                    throw new Exception("Usuário não informado");
                }

                MovProdutoVO movProdutoVO = movProdutoDAO.consultarPorChavePrimaria(codMovProduto, Uteis.NIVELMONTARDADOS_VENDA);
                if (!movProdutoVO.getApresentarAlterarRenovavelAutomaticamente()) {
                    throw new Exception("Produto não permite alterar");
                }
                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                movProdutoDAO.alterarPermiteRenovacaoAutomaticaMovProduto(movProdutoVO, !movProdutoVO.getRenovavelAutomaticamente(), usuarioVO);
                movProdutoVO.setRenovavelAutomaticamente(!movProdutoVO.getRenovavelAutomaticamente());
                return EnvelopeRespostaDTO.of("ok");
            } else {
                throw new Exception("Tipo não identificado");
            }
        } finally {
            clienteDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            movProdutoDAO = null;
        }
    }

    private EnvelopeRespostaDTO contrato(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        Contrato contratoDAO;
        try {
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            contratoDAO = new Contrato(con);

            String chave = obterChave(request);
            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer codUsuario = jsonBody.optInt("usuario");
            Integer codEmpresa = jsonBody.optInt("empresa");
            Integer codContrato = jsonBody.optInt("contrato");
            Integer codCliente = jsonBody.optInt("cliente");
            String tipo = jsonBody.optString("tipo");

            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            if (tipo.equalsIgnoreCase("apresentar_menu_contrato")) {

                try {
                    ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_TODOS);
                    List<ContratoVO> listaContratos = contratoDAO.consultarListaTela(clienteVO.getPessoa().getCodigo(), 3);
                    // Regra de necogio um cliente uma vez matriculado com a configuracão da
                    // empresa nao permitir contrato concomitantes nao podera
                    // realizar outro contrato de matricula somente rematricula ou renovacao
                    // dia 05/05/2010 MAX.
                    if (!UtilReflection.objetoNulo(clienteVO, "getSituacaoClienteSinteticoVO().getSituacao()") &&
                            clienteVO.getSituacaoClienteSinteticoVO().getSituacao().equals("VI") &&
                            UteisValidacao.emptyList(listaContratos)) {
                        return EnvelopeRespostaDTO.of(true);
                    }

                    boolean temContratoNaEmpresaAtual = false;
                    // se a empresa atual do cliente é diferente de alguma empresa dos seus
                    // contratos
                    // deve permitir uma nova linha de contratos da empresa do usuário que
                    // está logado.

                    for (ContratoVO ct : listaContratos) {
                        if (ct.getEmpresa().getCodigo().equals(clienteVO.getEmpresa().getCodigo())
                                && ((UtilReflection.objetoMaiorQueZero(ct ,"getPessoaOriginal().getCodigo()")
                                && (!ct.getPessoaOriginal().getCodigo().equals(clienteVO.getPessoa().getCodigo()) && ct.getSituacao().equals("AT") // cliente que recebeu direto de uso e contrato está ativo não pode lançar contrato se empresa não permitir concomitancia
                                || (ct.getPessoaOriginal().getCodigo().equals(clienteVO.getPessoa().getCodigo()) && (ct.getSituacao().equals("IN") ||ct.getSituacao().equals("CA") ))))  // cliente que cedeu direto de uso e contrato não está ativo não pode lançar contrato se empresa não permitir concomitancia, deve renovar/rematricular
                                || (!UtilReflection.objetoMaiorQueZero(ct ,"getPessoaOriginal().getCodigo()") && UteisValidacao.emptyNumber(ct.getContratoResponsavelRenovacaoMatricula()) && UteisValidacao.emptyNumber(ct.getContratoResponsavelRematriculaMatricula())))) { // nesse caso, um contrato anterior pertence a pessoa, mas a renovação/rematircula foi  transferida. Então essa linha de contratos não pode impedir de lançar um contrato para esse cliente
                            temContratoNaEmpresaAtual = true;
                        }
                    }

                    try {
                        if (clienteVO.getEmpresa().getPermiteContratosConcomintante() || !temContratoNaEmpresaAtual) {
                            return EnvelopeRespostaDTO.of(true);
                        } else {
                            return EnvelopeRespostaDTO.of(false);
                        }
                    } catch (NullPointerException e) {
                        e.printStackTrace();
                        return EnvelopeRespostaDTO.of(false);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    return EnvelopeRespostaDTO.of(false);
                }

            } else if (tipo.equalsIgnoreCase("detalhe_contrato_modalidade")) {

                Conexao.guardarConexaoForJ2SE(chave, con);
                ContratoVO contratoVO = contratoDAO.consultarContratoDetalhe(codContrato);
//                ContratoWS contratoWS = contratoVO.toWS(false);

                JSONArray lista = new JSONArray();
                for (ContratoModalidadeVO objCm : contratoVO.getContratoModalidadeVOs()) {
                    JSONObject jsonCm = new JSONObject();
                    jsonCm.put("contrato_modalidade_codigo", objCm.getCodigo());
                    JSONArray listaCMT = new JSONArray();
                    for (Object objCMT1 : objCm.getContratoModalidadeTurmaVOs()) {
                        ContratoModalidadeTurmaVO objCMT = (ContratoModalidadeTurmaVO) objCMT1;
                        JSONObject jsonCMT = new JSONObject();
                        jsonCMT.put("contrato_modalidade_turma_codigo", objCMT.getCodigo());
                        jsonCMT.put("presenca", objCMT.getTurma().getTotalPresencas());
                        jsonCMT.put("reposicao", objCMT.getTurma().getTotalReposicoes());
                        jsonCMT.put("faltas", objCMT.getTurma().getTotalFaltas());
                        jsonCMT.put("desmarcadas", objCMT.getTurma().getTotalAulasDesmarcadas());
                        jsonCMT.put("desmarcadas_contrato_anterior", objCMT.getTurma().getTotalAulasDesmarcadasContratoPassado());
                        jsonCMT.put("aulas_total_atual", objCMT.getTurma().getTotalAulasAteHoje());
                        jsonCMT.put("aulas_total", objCMT.getTurma().getTotalAulas());
                        jsonCMT.put("aulas_restante", objCMT.getTurma().getTotalAulas() - objCMT.getTurma().getTotalAulasAteHoje());
                        listaCMT.put(jsonCMT);
                    }
                    jsonCm.put("contrato_modalidade_turma_lista", listaCMT);
                    lista.put(jsonCm);
                }
                return EnvelopeRespostaDTO.of(lista.toString());

            } else if (tipo.equalsIgnoreCase("enviar_assinar")) {

                if (UteisValidacao.emptyNumber(codContrato)) {
                    throw new Exception("Contrato não informado");
                }
                if (UteisValidacao.emptyNumber(codUsuario)) {
                    throw new Exception("Usuário não informado");
                }

                JSONArray jsonEmails = jsonBody.getJSONArray("emails");
                List<EmailVO> emailsContrato = new ArrayList<>();
                for (int i = 0; i < jsonEmails.length(); i++) {
                    EmailVO emailVO = new EmailVO();
                    emailVO.setEmail(jsonEmails.getString(i));
                    emailsContrato.add(emailVO);
                }
                String retorno = enviarContratoPorEmail(chave, codUsuario, codContrato, null, emailsContrato, true, request, con);
                return EnvelopeRespostaDTO.of(retorno);

            } else if (tipo.equalsIgnoreCase("enviar_assinar_whats")) {

            if (UteisValidacao.emptyNumber(codContrato)) {
                throw new Exception("Contrato não informado");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            String mensagem = jsonBody.optString("msg");
            String telefone = jsonBody.optString("telefone");
            String linkVisualizarContrato = jsonBody.optString("linkVisualizarContrato");
            if (UteisValidacao.emptyString(mensagem)) {
                throw new Exception("Mensagem do WhatsApp não informada");
            }
            if (UteisValidacao.emptyString(telefone)) {
                throw new Exception("Telefone não informado");
            }
            if (UteisValidacao.emptyString(linkVisualizarContrato)) {
                throw new Exception("Erro ao carregar URL");
            }

            String linkFinalEncurtado = encurtarUrl(linkVisualizarContrato);

            String msgEnviar = mensagem + " " + linkFinalEncurtado;

            String telefoneFormatado = formatarNumeroParaWaMe(telefone);

            String mensagemFinal = URLEncoder.encode(msgEnviar, StandardCharsets.UTF_8.toString());
            String linkWhatsApp = "https://wa.me/" + telefoneFormatado + "?text=" + mensagemFinal;

            return EnvelopeRespostaDTO.of(linkWhatsApp);
        } else if (tipo.equalsIgnoreCase("obter_planos_alterar_plano")) {

                JSONObject jsonResp = obterPlanosAlterarPlano(chave, jsonBody, con);
                return EnvelopeRespostaDTO.of(jsonResp.toString());

            } else if (tipo.equalsIgnoreCase("obter_info_alterar_plano")) {

                JSONObject jsonResp = obterInformacoesAlterarPlano(chave, jsonBody, con);
                return EnvelopeRespostaDTO.of(jsonResp.toString());

            } else if (tipo.equalsIgnoreCase("alterar_plano")) {

                alterarPlanoContrato(chave, jsonBody, con);
                return EnvelopeRespostaDTO.of("ok");

            } else if (tipo.equalsIgnoreCase("obter_info_alterar_vencimento_parcelas_contrato")) {

                JSONObject jsonResp = obterInformacoesVencimentoParcelasContrato(chave, jsonBody, con);
                return EnvelopeRespostaDTO.of(jsonResp.toString());

            } else if (tipo.equalsIgnoreCase("calcular_alterar_vencimento_parcelas_contrato")) {

                JSONObject jsonResp = calcularAlterarVencimentoParcelasContrato(chave, jsonBody, con);
                return EnvelopeRespostaDTO.of(jsonResp.toString());

            } else if (tipo.equalsIgnoreCase("alterar_vencimento_parcelas_contrato")) {

                return EnvelopeRespostaDTO.of(alterarVencimentoParcelasContrato(chave, jsonBody, con));

            } else {
                throw new Exception("Tipo não identificado");
            }
        } finally {
            clienteDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            contratoDAO = null;
        }
    }

    private String montarLinkAssinaturaDigitalSemToken() {
        String urlSistema = "";
        try {
            if (JSFUtilities.isJSFContext()) {
                urlSistema = getUrl();
            }
        } catch (Exception ignored) {
        }

        if (UteisValidacao.emptyString(urlSistema)) {
            try {
                String key = DAO.resolveKeyFromConnection(getFacade().getEmpresa().getCon());
                ClientDiscoveryDataDTO dataDTO = DiscoveryMsService.urlsChave(key);
                urlSistema = dataDTO.getServiceUrls().getZwUrl();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        String url = urlSistema + "/faces/assinaturaDigital.jsp?token=";
        return url;
    }

    public static String encurtarUrl(String urlOriginal) throws Exception {
        String urlCodificada = URLEncoder.encode(urlOriginal, StandardCharsets.UTF_8.name());
        URL url = new URL("https://tinyurl.com/api-create.php?url=" + urlCodificada);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        String linkEncurtado = in.readLine();
        in.close();

        return linkEncurtado;
    }

    public String formatarNumeroParaWaMe(String numeroBruto) {
        String apenasNumeros = numeroBruto.replaceAll("[^0-9]", "");

        if (apenasNumeros.length() == 11) {
            return "55" + apenasNumeros;
        } else if (apenasNumeros.length() == 13 && apenasNumeros.startsWith("55")) {
            return apenasNumeros;
        } else {
            throw new IllegalArgumentException("Formato de número inválido: " + numeroBruto);
        }
    }

    private JSONObject obterPlanosAlterarPlano(String chave, JSONObject jsonBody, Connection con) throws Exception {
        Plano planoDAO;
        Empresa empresaDAO;
        ConfiguracaoSistema configuracaoSistemaDAO;
        try {
            planoDAO = new Plano(con);
            empresaDAO = new Empresa(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);

            Integer codEmpresa = jsonBody.getInt("empresa");
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            boolean consultarMultiplaEmpresa = configuracaoSistemaVO.isControleAcessoMultiplasEmpresasPorPlano();
            List<PlanoVO> planosListagem = planoDAO.consultarPlanoParaTransferencia("", consultarMultiplaEmpresa, empresaVO);
            JSONArray planos = new JSONArray();
            for (PlanoVO planoVO : planosListagem) {
                JSONObject jsonPl = new JSONObject();
                jsonPl.put("codigo", planoVO.getCodigo());
                jsonPl.put("descricao", planoVO.getDescricao());
                planos.put(jsonPl);
            }

            JSONObject jsonResp = new JSONObject();
            jsonResp.put("planos", planos);
            return jsonResp;
        } finally {
            planoDAO = null;
            empresaDAO = null;
            configuracaoSistemaDAO = null;
        }
    }

    private JSONObject obterInformacoesAlterarPlano(String chave, JSONObject jsonBody, Connection con) throws Exception {
        Contrato contratoDAO;
        Plano planoDAO;
        Empresa empresaDAO;
        Cliente clienteDAO;
        Usuario usuarioDAO;

        try {

            contratoDAO = new Contrato(con);
            planoDAO = new Plano(con);
            empresaDAO = new Empresa(con);
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);


            Conexao.guardarConexaoForJ2SE(chave, con);

            Integer codEmpresa = jsonBody.getInt("empresa");
            Integer codUsuario = jsonBody.getInt("usuario");
            JSONObject jsonDados = jsonBody.getJSONObject("dados");
            Integer codPlano = jsonDados.getInt("plano");
            Integer codCliente = jsonDados.getInt("cliente");
            Integer codContratoAtual = jsonDados.getInt("contrato");
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContratoAtual, Uteis.NIVELMONTARDADOS_TODOS);
            PlanoVO planoAtualVO = planoDAO.consultarPorChavePrimaria(contratoVO.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            PlanoVO planoNovoVO = planoDAO.consultarPorChavePrimaria(codPlano, Uteis.NIVELMONTARDADOS_TODOS);

            if (Calendario.igual(contratoVO.getVigenciaDe(), Calendario.hoje())) {
                throw new Exception("A operação de mudança de plano não pode ser feita no primeiro dia do contrato.");
            }
            Integer diaVencimentoCartaoTransferenciaDePlano = (planoNovoVO.getPlanoRecorrencia().isGerarParcelasValorDiferente() || planoNovoVO.getPlanoRecorrencia().isGerarParcelasValorDiferenteRenovacao()
                    ? Uteis.getDiaMesData(Calendario.hoje()) : contratoVO.getContratoRecorrenciaVO().getDiaVencimentoCartao());

            ContratoVO novoContratoVO = contratoDAO.gravarContratoSite(
                    chave,
                    planoNovoVO.getCodigo(),
                    codCliente,
                    contratoVO.getNrParcelasAdesao(),
                    contratoVO.getNrVezesParcelarProduto(),
                    "",
                    true,
                    true,
                    false,
                    contratoVO.getNrParcelasPagamento(),
                    true,
                    0,
                    codEmpresa,
                    true,
                    usuarioVO, diaVencimentoCartaoTransferenciaDePlano, null, false, 0, null, null, contratoVO,
                    true, null, null, null, false);


            Integer diaVencimentoCartaoTransferenciaDePlanoAnterior = ((planoAtualVO.getPlanoRecorrencia() != null && planoAtualVO.getPlanoRecorrencia().isGerarParcelasValorDiferente()) || planoAtualVO.getPlanoRecorrencia().isGerarParcelasValorDiferenteRenovacao()
                    ? Uteis.getDiaMesData(Calendario.hoje()) : contratoVO.getContratoRecorrenciaVO().getDiaVencimentoCartao());

            JSONObject jsonResp = new JSONObject();
            jsonResp.put("planoAnterior", planoAtualVO.getDescricao());
            //anterior
            jsonResp.put("manutencaoAnterior", contratoVO.getValorPorAnuidade());
            jsonResp.put("valorPlanoAnterior", contratoVO.getValorContrato());
            jsonResp.put("valorFinalAnterior", contratoVO.getValorFinal());
            jsonResp.put("mensalidadeAnterior", contratoVO.obterValorContratoReferenteMensal());
            jsonResp.put("dataInicioAnterior", contratoVO.getVigenciaDe() != null ? contratoVO.getVigenciaDe().getTime() : null);
            jsonResp.put("dataFimAnterior", contratoVO.getVigenciaAteAjustada() != null ? contratoVO.getVigenciaAteAjustada().getTime() : null);
            jsonResp.put("diaCobrancaParcelasAnterior", diaVencimentoCartaoTransferenciaDePlanoAnterior);
            //novo
            jsonResp.put("planoNovo", planoNovoVO.getDescricao());
            jsonResp.put("manutencao", novoContratoVO.getValorPorAnuidade());
            jsonResp.put("plano", novoContratoVO.getValorContrato());
            jsonResp.put("valorFinal", novoContratoVO.getValorFinal());
            jsonResp.put("mensalidade", novoContratoVO.obterValorContratoReferenteMensal());
            jsonResp.put("dataInicio", novoContratoVO.getVigenciaDe() != null ? novoContratoVO.getVigenciaDe().getTime() : null);
            jsonResp.put("dataFim", novoContratoVO.getVigenciaAteAjustada() != null ?
                    novoContratoVO.getVigenciaAteAjustada().getTime() : null);
            jsonResp.put("diaCobrancaParcelas", diaVencimentoCartaoTransferenciaDePlano);

            if (contratoVO.getVigenciaDe() != null && contratoVO.getVigenciaAteAjustada() != null) {
                Period periodoAnterior = Period.between(
                        DateUtilities.toLocalDate(contratoVO.getVigenciaDe()),
                        DateUtilities.toLocalDate(contratoVO.getVigenciaAteAjustada())
                );
                int mesesDuracaoAnterior = periodoAnterior.getYears() * 12 + periodoAnterior.getMonths();
                String duracaoAnteriorFormatada = mesesDuracaoAnterior + " " + (mesesDuracaoAnterior == 1 ? "ms" : "meses");
                jsonResp.put("duracaoMesesAnterior", duracaoAnteriorFormatada);
            }

            if (novoContratoVO.getVigenciaDe() != null && novoContratoVO.getVigenciaAteAjustada() != null) {
                Period periodoNovo = Period.between(
                        DateUtilities.toLocalDate(novoContratoVO.getVigenciaDe()),
                        DateUtilities.toLocalDate(novoContratoVO.getVigenciaAteAjustada())
                );
                int mesesDuracaoNovo = periodoNovo.getYears() * 12 + periodoNovo.getMonths();
                String duracaoNovoFormatada = mesesDuracaoNovo + " " + (mesesDuracaoNovo == 1 ? "ms" : "meses");
                jsonResp.put("duracaoMesesNovo", duracaoNovoFormatada);
            }


            BigDecimal valorAtual = BigDecimal.valueOf(contratoVO.getValorFinal());
            BigDecimal valorNovo = BigDecimal.valueOf(novoContratoVO.getValorFinal());
            BigDecimal economia = valorAtual.subtract(valorNovo);
            jsonResp.put("tipoEconomia", economia.compareTo(BigDecimal.ZERO) >= 0 ? "ECONOMIA" : "ACRESCIMO");
            jsonResp.put("economia", economia.abs());

            return jsonResp;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            contratoDAO = null;
        }
    }


    private void alterarPlanoContrato(String chave, JSONObject jsonBody, Connection con) throws Exception {
        Contrato contratoDAO;
        Plano planoDAO;
        Empresa empresaDAO;
        Cliente clienteDAO;
        Usuario usuarioDAO;
        try {
            contratoDAO = new Contrato(con);
            planoDAO = new Plano(con);
            empresaDAO = new Empresa(con);
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);

            Conexao.guardarConexaoForJ2SE(chave, con);

            Integer codEmpresa = jsonBody.getInt("empresa");
            Integer codUsuario = jsonBody.getInt("usuario");
            JSONObject jsonDados = jsonBody.getJSONObject("dados");
            Integer codPlano = jsonDados.getInt("plano");
            Integer codCliente = jsonDados.getInt("cliente");
            Integer codContratoAtual = jsonDados.getInt("contrato");
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_TODOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_TODOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_TODOS);
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContratoAtual, Uteis.NIVELMONTARDADOS_TODOS);
            PlanoVO planoAtualVO = planoDAO.consultarPorChavePrimaria(contratoVO.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            PlanoVO planoNovoVO = planoDAO.consultarPorChavePrimaria(codPlano, Uteis.NIVELMONTARDADOS_TODOS);


            ClienteControle clienteControle = new ClienteControle();
            clienteControle.setClienteVO(clienteVO);
            clienteControle.setEmpresa(empresaVO);
            clienteControle.setUsuario(usuarioVO);
            clienteControle.setContratoVO(contratoVO);

            TelaClienteControle telaClienteControle = new TelaClienteControle();
            telaClienteControle.setEmpresa(empresaVO);
            telaClienteControle.setUsuario(usuarioVO);
            telaClienteControle.setCliente(clienteVO);
            telaClienteControle.selecionarContratoParaTransferenciaDePlanoGeral(codContratoAtual);
            telaClienteControle.selecionarPlanoParaTransferenciaGeral(planoNovoVO, chave);
            telaClienteControle.transferirPlanoDoClienteGeral(true, clienteControle, chave);

        } finally {
            contratoDAO = null;
        }
    }

    private JSONObject obterInformacoesVencimentoParcelasContrato(String chave, JSONObject jsonBody, Connection con) throws Exception {
        Contrato contratoDAO;
        try {
            contratoDAO = new Contrato(con);

            JSONObject jsonDados = jsonBody.optJSONObject("dados");
            Integer codContrato = jsonDados.getInt("contrato");
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_TODOS);

            JSONArray diasVencimento = new JSONArray();
            for (String dia : contratoVO.getPlano().getListaDiasVencimento()) {
                if (!dia.isEmpty()) {
                    JSONObject jDia = new JSONObject();
                    jDia.put("id", dia);
                    jDia.put("label", dia);
                    diasVencimento.put(jDia);
                }
            }
//            notificarRecursoEmpresa(RecursoSistema.MUDANCA_DE_VENCIMENTO_PERFIL_ALUNO);
            JSONObject jsonResp = new JSONObject();
            jsonResp.put("diasVencimento", diasVencimento);
            return jsonResp;

        } finally {
            contratoDAO = null;
        }
    }

    private JSONObject calcularAlterarVencimentoParcelasContrato(String chave, JSONObject jsonBody, Connection con) throws Exception {
        Contrato contratoDAO;
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        try {
            contratoDAO = new Contrato(con);
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);

            Integer codUsuario = jsonBody.getInt("usuario");
            Integer codEmpresa = jsonBody.getInt("empresa");
            JSONObject jsonDados = jsonBody.optJSONObject("dados");

            Integer codContrato = jsonDados.getInt("contrato");
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_TODOS);
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Integer diaVencimentoParcelasAlterado = jsonDados.getInt("diaVencimento");

            return contratoDAO.calcularProRataDeAlteracaoVencimentoParcelas(clienteVO, contratoVO, diaVencimentoParcelasAlterado, empresaVO, usuarioVO);
        } finally {
            contratoDAO = null;
            clienteDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
        }
    }

    private String alterarVencimentoParcelasContrato(String chave, JSONObject jsonBody, Connection con) throws Exception {
        Contrato contratoDAO;
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        ContratoRecorrencia contratoRecorrenciaDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            contratoDAO = new Contrato(con);
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            contratoRecorrenciaDAO = new ContratoRecorrencia(con);
            zillyonWebFacade = new ZillyonWebFacade(con);

            Integer codUsuario = jsonBody.getInt("usuario");
            Integer codEmpresa = jsonBody.getInt("empresa");
            JSONObject jsonDados = jsonBody.optJSONObject("dados");

            Uteis.logarDebug(jsonDados.toString());

            Integer codContrato = jsonDados.getInt("contrato");
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_TODOS);
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Integer diaVencimentoParcelasAlterado = jsonDados.getInt("diaVencimento");
            boolean liberarValorProRata = jsonDados.getBoolean("liberarValorProRata");

            JSONObject jsonCalc = contratoDAO.calcularProRataDeAlteracaoVencimentoParcelas(clienteVO, contratoVO, diaVencimentoParcelasAlterado, empresaVO, usuarioVO);

            boolean alterarMesProximaParcelaEmAberto = jsonCalc.getBoolean("alterarMesProximaParcelaEmAberto");
            boolean permiteLiberarCobrancaDataVencimentoParcelas = jsonCalc.getBoolean("permiteLiberarCobrancaDataVencimentoParcelas");
            Double valorProRataAlteracaoDataVencimentoParcelas = jsonCalc.getDouble("valorProRataAlteracaoDataVencimentoParcelas");
            Integer diferencaDiasNovoVencimento = jsonCalc.getInt("diferencaDiasNovoVencimento");

            List<MovParcelaVO> parcelasAlteradas = contratoRecorrenciaDAO.alterarDataVencimentoParcelasMensalidade(contratoVO, diaVencimentoParcelasAlterado,
                    usuarioVO, valorProRataAlteracaoDataVencimentoParcelas, diferencaDiasNovoVencimento, liberarValorProRata, alterarMesProximaParcelaEmAberto);

            contratoDAO.acrescentarDiasContrato(contratoVO, diferencaDiasNovoVencimento);
            zillyonWebFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            return "Os vencimentos das " + parcelasAlteradas.size() + " parcelas em aberto foram alterados";
//                    notificarRecursoEmpresa(RecursoSistema.MUDANCA_DE_VENCIMENTO_PERFIL_ALUNO_SUCESSO);
        } finally {
            contratoDAO = null;
            clienteDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
        }
    }

    private EnvelopeRespostaDTO crm(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        Modalidade modalidadeDAO;
        Colaborador colaboradorDAO;
        try {
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            modalidadeDAO = new Modalidade(con);
            colaboradorDAO = new Colaborador(con);

            String chave = obterChave(request);
            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer codUsuario = jsonBody.optInt("usuario");
            Integer codEmpresa = jsonBody.optInt("empresa");
            Integer codCliente = jsonBody.optInt("cliente");
            String tipo = jsonBody.optString("tipo");
            JSONObject contato = jsonBody.optJSONObject("contato");

            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            if (tipo.equalsIgnoreCase("salvar")) {

                if (UteisValidacao.emptyNumber(codCliente)) {
                    throw new Exception("Cliente não informado");
                }

                Conexao.guardarConexaoForJ2SE(chave, con);

                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
                historicoContatoVO.setDia(new Date(contato.getLong("dia")));
                historicoContatoVO.setFase("");
                historicoContatoVO.setContatoAvulso(true);
                historicoContatoVO.setClienteVO(clienteVO);
                historicoContatoVO.setResponsavelCadastro(usuarioVO);
                historicoContatoVO.setTipoContato(contato.getString("tipocontato"));
                historicoContatoVO.setObservacao(contato.getString("observacao"));

                if (historicoContatoVO.getTipoContato().equalsIgnoreCase(TipoContatoCRM.CONTATO_TELEFONE.getSigla()) ||
                        historicoContatoVO.getTipoContato().equalsIgnoreCase(TipoContatoCRM.CONTATO_PESSOAL.getSigla()) ||
                        historicoContatoVO.getTipoContato().equalsIgnoreCase(TipoContatoCRM.CONTATO_WHATSAPP.getSigla())) {

                    String tipoAgendamento = contato.optString("tipoAgendamento");
                    Integer codObjecao = contato.optInt("objecao");

                    if (!UteisValidacao.emptyString(tipoAgendamento)) {

                        AgendaVO agendaVO = new AgendaVO();
                        agendaVO.setDataLancamento(historicoContatoVO.getDia());
                        agendaVO.setDataAgendamento(new Date(contato.getLong("dataAgendamento")));
                        agendaVO.setHoraMinuto(contato.getString("horaMinutoAgendamento"));
                        agendaVO.setTipoAgendamento(tipoAgendamento);
                        agendaVO.setEmpresa(empresaVO.getCodigo());

                        UsuarioVO usuarioColaboradorResponsavel = usuarioDAO.consultarPorCodigoColaborador(contato.getInt("colaborador"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        boolean selecionouColaboradorResponsavel = usuarioColaboradorResponsavel.getCodigo() != usuarioVO.getCodigo();
                        if (selecionouColaboradorResponsavel &&
                                (agendaVO.getTipoAgendamento().equalsIgnoreCase("LI") ||
                                        agendaVO.getTipoAgendamento().equalsIgnoreCase("VI"))) {
                            agendaVO.setUsuarioVO(usuarioColaboradorResponsavel);
                        } else {
                            agendaVO.setUsuarioVO(usuarioVO);
                        }

                        Integer codModalidade = contato.optInt("modalidade");
                        if (!UteisValidacao.emptyNumber(codModalidade)) {
                            ModalidadeVO modalidadeVO = modalidadeDAO.consultarPorChavePrimaria(codModalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            agendaVO.setModalidade(modalidadeVO);
                        }

                        Integer codColaborador = contato.optInt("colaborador");
                        if (!UteisValidacao.emptyNumber(codColaborador)) {
                            ColaboradorVO colaboradorVO = colaboradorDAO.consultarPorChavePrimaria(codColaborador, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            UsuarioVO usuarioVOResponsavel = usuarioDAO.consultarPorCodigoColaborador(colaboradorVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            if (usuarioVOResponsavel == null ||
                                    UteisValidacao.emptyNumber(usuarioVOResponsavel.getCodigo())) {
                                usuarioVOResponsavel = usuarioDAO.consultarPorCodigoPessoa(colaboradorVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            }
                            agendaVO.setCodigoProfessor(colaboradorVO.getCodigo());
                            agendaVO.setColaboradorResponsavel(usuarioVOResponsavel);
                        } else {
                            agendaVO.setColaboradorResponsavel(usuarioVO);
                        }

                        gravarAgendamento(historicoContatoVO, agendaVO, empresaVO, usuarioVO, con);
                        return EnvelopeRespostaDTO.of("ok");

                    } else if (!UteisValidacao.emptyNumber(codObjecao)) {

                        historicoContatoVO.setObjecaoVO(new ObjecaoVO());
                        historicoContatoVO.getObjecaoVO().setCodigo(codObjecao);
                        gravarObjecao(historicoContatoVO, empresaVO, usuarioVO, con);
                        return EnvelopeRespostaDTO.of("ok");
                    } else {
                        gravarSimplesRegistro(historicoContatoVO, empresaVO, usuarioVO, con);
                        return EnvelopeRespostaDTO.of("ok");
                    }

                } else if (historicoContatoVO.getTipoContato().equalsIgnoreCase(TipoContatoCRM.CONTATO_APP.getSigla())) {

                    MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
                    malaDiretaVO.setTipoPergunta(contato.getInt("tipoMensagemApp"));
                    malaDiretaVO.setTitulo(contato.getString("titulo"));

                    CamposGenericosTO campos = new CamposGenericosTO();
                    campos.setCampo1(contato.optString("resposta1"));
                    campos.setCampo2(contato.optString("resposta2"));
                    campos.setCampo3(contato.optString("resposta3"));

                    gravarEnviandoContatoAPP(historicoContatoVO, null, malaDiretaVO, campos, empresaVO, usuarioVO, chave, con);
                    return EnvelopeRespostaDTO.of("ok");

                } else if (historicoContatoVO.getTipoContato().equalsIgnoreCase(TipoContatoCRM.CONTATO_SMS.getSigla())) {

                    MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
                    String numero = contato.getString("telefoneCelular");
                    gravarEnviandoSMS(numero, historicoContatoVO, null, malaDiretaVO, empresaVO, usuarioVO, chave, con);
                    return EnvelopeRespostaDTO.of("ok");

                } else if (historicoContatoVO.getTipoContato().equalsIgnoreCase(TipoContatoCRM.CONTATO_EMAIL.getSigla())) {

                    MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
                    malaDiretaVO.setTitulo(contato.getString("titulo"));
                    String email = contato.getString("email");
                    gravarEnviandoEmail(email, historicoContatoVO, null, malaDiretaVO, empresaVO, usuarioVO, chave, con);
                    return EnvelopeRespostaDTO.of("ok");
                }

            } else if (tipo.equalsIgnoreCase("link_cadastrar_cartao")) {

                if (UteisValidacao.emptyNumber(codCliente)) {
                    throw new Exception("Cliente não informado");
                }

                Conexao.guardarConexaoForJ2SE(chave, con);

                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                VendasOnlineService vendasOnlineService = new VendasOnlineService(null, con);
                String link = vendasOnlineService.obterLinkPagamentoVendasOnline(chave, clienteVO, empresaVO.getCodigo(),
                        false, OrigemCobrancaEnum.ZW_HISTORICO_CONTATO, null, usuarioVO, null, null, null, 1);
                return EnvelopeRespostaDTO.of(link);
            } else {
                throw new Exception("Tipo não identificado");
            }
            throw new Exception("Nenhuma operação realizada");
        } finally {
            clienteDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            modalidadeDAO = null;
            colaboradorDAO = null;
        }
    }

    private void gravarSimplesRegistro(HistoricoContatoVO historicoContatoVO, EmpresaVO empresaVO,
                                       UsuarioVO usuarioVO, Connection con) throws Exception {
        HistoricoContato historicoContatoDAO;
        try {
            historicoContatoDAO = new HistoricoContato(con);
            validarPermissao(empresaVO, "RealizarContato", "7.23 - Realizar Contato", usuarioVO, con);
            historicoContatoDAO.gravarHistoricoContato("SR", historicoContatoVO, null, null, empresaVO.getCodigo());
        } finally {
            historicoContatoDAO = null;
        }
    }

    private boolean validarMetaNaoAberta(EmpresaVO empresaVO, UsuarioVO usuarioVO, Connection con) throws Exception {
        ConfiguracaoSistema configuracaoSistemaDAO = new ConfiguracaoSistema(con);
        AberturaMeta aberturaMetaDAO = new AberturaMeta(con);
        boolean validarCfgValidacaoContato = configuracaoSistemaDAO.verificarValidacaoContatoMeta();
        return validarCfgValidacaoContato && !aberturaMetaDAO.consultarAberturaPorCodigoUsuario(usuarioVO.getCodigo(), empresaVO.getCodigo(), Calendario.hoje());
    }

    private void gravarAgendamento(HistoricoContatoVO historicoContatoVO, AgendaVO agendaVO,
                                   EmpresaVO empresaVO, UsuarioVO usuarioVO, Connection con) throws Exception {
        ConfiguracaoSistema configuracaoSistemaDAO = null;
        ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO = null;
        Agenda agendaDAO = null;
        HistoricoContato historicoContatoDAO = null;
        try {
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(con);
            agendaDAO = new Agenda(con);
            historicoContatoDAO = new HistoricoContato(con);

//            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (configuracaoSistemaCRMVO.isAgendamentoParaMetaConsultor()){
                usuarioVO = agendaVO.getColaboradorResponsavel();
            }

            if (validarMetaNaoAberta(empresaVO, usuarioVO, con)) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
            validarPermissao(empresaVO, "RealizarContato", "7.23 - Realizar Contato", usuarioVO, con);
            if (agendaVO.getHoraMinuto().isEmpty()) {
                throw new ConsistirException(("Informe o horário."));
            } else if (agendaVO.getHoraMinuto().length() != 5) {
                throw new ConsistirException(("Informe o horário corretamente."));
            }

            String[] horaMinuto = agendaVO.getHoraMinuto().split(":");
            agendaVO.setHora(horaMinuto[0]);
            agendaVO.setMinuto(horaMinuto[1]);
            if (Integer.parseInt(agendaVO.getHora()) > 23) {
                throw new ConsistirException(("A Hora não pode ser superior a 23."));
            }
            if (Integer.parseInt(agendaVO.getMinuto()) > 59) {
                throw new ConsistirException(("Os Minutos não pode ser superior a 59."));
            }

            agendaDAO.verificaExisteAgendamentoDiaHoraMinuto(historicoContatoVO, agendaVO);

            Date dataLimite = configuracaoSistemaCRMDAO.obterDataCalculadaDiasUteis(Calendario.hoje(), false, configuracaoSistemaCRMVO.getNrDiasLimiteAgendamentoFuturo(), empresaVO);

            if (Calendario.maior(agendaVO.getDataAgendamento(), dataLimite) &&
                    (agendaVO.getTipoAgendamento().equals("AE") || agendaVO.getTipoAgendamento().equals("VI"))) {
                throw new ConsistirException("Agendamento com data superior a " + configuracaoSistemaCRMVO.getNrDiasLimiteAgendamentoFuturoDescricao()
                        + " só pode ser do tipo Ligação. Veja campo 'Número de dias limite para agendamento futuro' nas configurações do CRM.");
            }

            if (!agendaVO.getTipoAgendamento().equals("AE")) {
                agendaVO.setModalidade(new ModalidadeVO());
            }

//        if (!getTipoProfessor().isEmpty() && !getCodigoProfessor().equals(0)) {
//            agendaVO.setTipoProfessor(getTipoProfessor());
//            agendaVO.setCodigoProfessor(getCodigoProfessor());
//        }

            String fase = FasesCRMEnum.AGENDAMENTO.getSigla();
            List<AgendaVO> list = agendaDAO.consultarUltimoPorCodigo(historicoContatoVO.getClienteVO().getCodigo(), 1);
            if (!list.isEmpty()) {
                if (list.get(0).getDataComparecimento() == null) {
                    agendaVO.setCodigo(list.get(0).getCodigo());
                }
            }
            if (Calendario.igual(agendaVO.getDataAgendamento(), Calendario.hoje()) && agendaVO.getTipoAgendamento().equals("LI") && historicoContatoVO.getFase().equals("AL")) {
                throw new ConsistirException("Agendamento de ligações só é possível para datas futuras, se preferir crie um simples registro ou um agendamento de visita! ");
            }

            historicoContatoDAO.gravarHistoricoContatoComColaborador(fase, historicoContatoVO, agendaVO, null, empresaVO.getCodigo(), usuarioVO);

        } finally {
            configuracaoSistemaDAO = null;
            configuracaoSistemaCRMDAO = null;
            agendaDAO = null;
            historicoContatoDAO = null;
        }
    }

    private void gravarEnviandoContatoAPP(HistoricoContatoVO historicoContatoVO, AgendaVO agendaVO, MalaDiretaVO malaDiretaVO,
                                          CamposGenericosTO campos, EmpresaVO empresaVO, UsuarioVO usuarioVO,
                                          String chave, Connection con) throws Exception {
        MalaDireta malaDiretaDAO;
        HistoricoContato historicoContatoDAO;
        ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO;
        try {
            malaDiretaDAO = new MalaDireta(con);
            historicoContatoDAO = new HistoricoContato(con);
            configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(con);

//            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (validarMetaNaoAberta(empresaVO, usuarioVO, con)) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
            validarPermissao(empresaVO, "RealizarContato", "7.23 - Realizar Contato", usuarioVO, con);

            if (malaDiretaVO.getTipoPergunta().equals(TipoPerguntaEnum.OBJETIVA.getCodigo())) {
                malaDiretaVO.setOpcoes(campos.toString());
            } else if (malaDiretaVO.getTipoPergunta().equals(TipoPerguntaEnum.DISSERTATIVA.getCodigo())) {
                malaDiretaVO.setOpcoes("Confirmar;TEXTO");
            } else {
                malaDiretaVO.setOpcoes("");
            }

            MalaDiretaEnviadaVO enviado = new MalaDiretaEnviadaVO();
            enviado.setClienteVO(historicoContatoVO.getClienteVO());
            malaDiretaVO.setmalaDiretaEnviadaVOs(new ArrayList<>());
            malaDiretaVO.getMalaDiretaEnviadaVOs().add(enviado);
            historicoContatoVO.setObservacao(Uteis.retiraTags(historicoContatoVO.getObservacao(), false));
            malaDiretaVO.setMensagem(historicoContatoVO.getObservacao());
            malaDiretaVO.setUsuarioVO(usuarioVO);
            malaDiretaVO.setRemetente(usuarioVO);
            malaDiretaVO.setEmpresa(empresaVO);
            malaDiretaVO.setContatoAvulso(historicoContatoVO.getContatoAvulso());
            malaDiretaVO.setTitulo(Uteis.retirarPontosGraficos(malaDiretaVO.getTitulo()));
            malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.APP);
            malaDiretaVO.setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
            malaDiretaDAO.incluir(malaDiretaVO);

            String retornoGerarNotificacoes = TreinoWSConsumer.gerarNotificacoes(chave,
                    malaDiretaVO, historicoContatoVO.getClienteVO().getCodigo().toString(), malaDiretaVO.getMensagem());

            if (UteisValidacao.emptyString(retornoGerarNotificacoes) || retornoGerarNotificacoes.toUpperCase().contains("ERRO")) {
                throw new Exception("Não foi possível enviar notificação. Tente novamente. " + retornoGerarNotificacoes);
            }
            String[] itens = retornoGerarNotificacoes.split(";");
            for (String item : itens) {
                String[] dados = item.split(",");
                historicoContatoVO.setCodigoNotificacao(Integer.valueOf(dados[1]));
                historicoContatoVO.setTipoContato("AP");
                historicoContatoDAO.gravarHistoricoContato("SR", historicoContatoVO, agendaVO, malaDiretaVO, empresaVO.getCodigo());
            }
        } finally {
            malaDiretaDAO = null;
            historicoContatoDAO = null;
            configuracaoSistemaCRMDAO = null;
        }
    }


    private void gravarEnviandoEmail(String email, HistoricoContatoVO historicoContatoVO, AgendaVO agendaVO, MalaDiretaVO malaDiretaVO,
                                     EmpresaVO empresaVO, UsuarioVO usuarioVO, String chave, Connection con) throws Exception {
        HistoricoContato historicoContatoDAO;
        ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO;
        try {
            historicoContatoDAO = new HistoricoContato(con);
            configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(con);

            if (validarMetaNaoAberta(empresaVO, usuarioVO, con)) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
            validarPermissao(empresaVO, "RealizarContato", "7.23 - Realizar Contato", usuarioVO, con);

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            historicoContatoVO.getClienteVO().getPessoa().setEmail(email);
            malaDiretaVO.setUsuarioVO(usuarioVO);
            malaDiretaVO.setRemetente(usuarioVO);
            malaDiretaVO.setContatoAvulso(historicoContatoVO.getContatoAvulso());
            malaDiretaVO.setTitulo(Uteis.retirarPontosGraficos(malaDiretaVO.getTitulo()));
            malaDiretaVO.setMensagem(historicoContatoVO.getObservacao());
            if (configuracaoSistemaCRMVO.getBloquearTermoSpam()) {
                String verificarTermosSpamNoTitulo = malaDiretaVO.verificarTermosSpam(configuracaoSistemaCRMVO);
                if (!verificarTermosSpamNoTitulo.isEmpty()) {
                    String msg = "Os seguintes termos não podem estar na mensagem ou no título: " + verificarTermosSpamNoTitulo + ".";
                    throw new Exception(msg);
                }
            }

            boolean configuracaoEmailValido = (!configuracaoSistemaCRMVO.getIntegracaoPacto() &&
                    !UteisValidacao.emptyString(configuracaoSistemaCRMVO.getLogin()) &&
                    !UteisValidacao.emptyString(configuracaoSistemaCRMVO.getSenha()) &&
                    !UteisValidacao.emptyString(configuracaoSistemaCRMVO.getEmailPadrao()) &&
                    !UteisValidacao.emptyString(configuracaoSistemaCRMVO.getRemetentePadrao()) &&
                    !UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer()))
                    ||
                    (configuracaoSistemaCRMVO.getIntegracaoPacto() &&
                            !UteisValidacao.emptyString(configuracaoSistemaCRMVO.getEmailPadrao()) &&
                            !UteisValidacao.emptyString(configuracaoSistemaCRMVO.getRemetentePadrao()));

            if (configuracaoEmailValido) {
                historicoContatoDAO.gravarHistoricoContato("", historicoContatoVO, agendaVO, malaDiretaVO, empresaVO.getCodigo());
//                MailingService mailingService = new MailingService(chave);
//                mailingService.enviarMalaDireta(malaDiretaVO.getCodigo());
                updateJenkinsService(chave, malaDiretaVO, configuracaoSistemaCRMVO);
            } else {
                throw new Exception("Configurações para envio de e-mail inválidas, verificar nas configurações do CRM.");
            }
        } finally {
            historicoContatoDAO = null;
            configuracaoSistemaCRMDAO = null;
        }
    }

    private void updateJenkinsService(String key, MalaDiretaVO malaDireta, ConfiguracaoSistemaCRMVO confCrm) throws Exception {
        final String chave = String.format("%s_%s_%s", key,
                malaDireta.getMeioDeEnvioEnum().getDescricao(),
                malaDireta.getTipoAgendamento().name());

        if (malaDireta.getTipoAgendamento().equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO)) {
            JenkinsService.createTaskMailing(chave, malaDireta.getCodigo().toString(), null, confCrm.getUrlJenkins(), confCrm.getUrlMailing(), malaDireta.getChaveAntiga());
            JenkinsService.buildTask(chave, malaDireta.getCodigo().toString(), confCrm.getUrlJenkins());
        } else if (malaDireta.getTipoAgendamento().equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO)) {
            ajustaHorarioCron(malaDireta);
            JenkinsService.createTaskMailing(chave, malaDireta.getCodigo().toString(),
                    malaDireta.getAgendamento().getCron(),
                    confCrm.getUrlJenkins(), confCrm.getUrlMailing(), malaDireta.getChaveAntiga());
        }
    }

    private void ajustaHorarioCron(MalaDiretaVO malaDireta) throws ConsistirException {
        TimeZone tzEmpresa = TimeZone.getTimeZone(malaDireta.getEmpresa().getTimeZoneDefault());
        TimeZone tzBrasilia = TimeZone.getTimeZone(TimeZoneEnum.Brazil_East.getId());

        Calendar dEmpresa = Calendario.hojeCalendar(tzEmpresa);
        Calendar dBrasilia = Calendario.hojeCalendar(tzBrasilia);
        Integer diferencaHoras = Calendario.diferencaEmHoras(dEmpresa.getTime(), dBrasilia.getTime()).intValue();

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, malaDireta.getAgendamento().getHoraInicio());
        calendar.add(Calendar.HOUR_OF_DAY, diferencaHoras);
        malaDireta.getAgendamento().setHoraInicio(calendar.get(Calendar.HOUR_OF_DAY));

        calendar.set(Calendar.HOUR_OF_DAY, malaDireta.getAgendamento().getHoraFim());
        calendar.add(Calendar.HOUR_OF_DAY, diferencaHoras);
        malaDireta.getAgendamento().setHoraFim(calendar.get(Calendar.HOUR_OF_DAY));

        malaDireta.getAgendamento().montarCron();
    }

    private void gravarEnviandoSMS(String numeroCelular, HistoricoContatoVO historicoContatoVO, AgendaVO agendaVO, MalaDiretaVO malaDiretaVO,
                                   EmpresaVO empresaVO, UsuarioVO usuarioVO, String chave, Connection con) throws Exception {
        Empresa empresaDAO = null;
        ConfiguracaoSistema configuracaoSistemaDAO = null;
        ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO = null;
        HistoricoContato historicoContatoDAO = null;
        try {
            empresaDAO = new Empresa(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(con);
            historicoContatoDAO = new HistoricoContato(con);

//            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (!historicoContatoVO.getContatoAvulso() && validarMetaNaoAberta(empresaVO, usuarioVO, con)) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }

            malaDiretaVO.setTitulo(Uteis.retirarPontosGraficos(malaDiretaVO.getTitulo()));
            if (configuracaoSistemaCRMVO.getBloquearTermoSpam()) {
                String mensagem = Uteis.retirarAcentuacaoRegex(historicoContatoVO.getObservacao()).toUpperCase();
                String verificarTermosSpamNoTitulo = MalaDiretaVO.verificar(configuracaoSistemaCRMVO, mensagem);
                if (!verificarTermosSpamNoTitulo.isEmpty()) {
                    String msg = "Os seguintes termos não podem estar na mensagem ou no título: " + verificarTermosSpamNoTitulo + ".";
                    throw new Exception(msg);
                }
            }

            String retorno = "SMS não foi enviado, nenhum celular selecionado";
            String obs = historicoContatoVO.getObservacao().replaceAll(Pattern.quote("<") + "p" + Pattern.quote(">"), "").replaceAll(Pattern.quote("</") + "p" + Pattern.quote(">"), "");

            if (obs.trim().isEmpty()) {
                throw new Exception("Para o envio de SMS, por favor preencha o campo Observação e tente novamente.");
            }

            if (obs.length() > Uteis.TAMANHO_MSG_SMS) {
                throw new Exception("A mensagem não pode ter mais que 140 caracteres");
            }

            // aqui não é necessário fazer a verificação do número pois já foi feita acima
            String tokenSMSEmpresa = empresaDAO.obterTokenSMS(empresaVO.getCodigo());
            String tokenSMSEmpresaShortCode = empresaDAO.obterTokenSMSShortCode(empresaVO.getCodigo());

            SmsController smsController = new SmsController((tokenSMSEmpresa.isEmpty() ? tokenSMSEmpresaShortCode : tokenSMSEmpresa), chave, TimeZone.getTimeZone(malaDiretaVO.getEmpresa().getTimeZoneDefault()));

            if (!UteisValidacao.emptyNumber(historicoContatoVO.getClienteVO().getCodigo())) {
                retorno = smsController.sendMessage(null, new Message().setNumero(numeroCelular).setMsg(ModeloMensagemVO.personalizarTagNomePessoa(obs, historicoContatoVO.getClienteVO().getPessoa().getNome())));
            } else if (!UteisValidacao.emptyNumber(historicoContatoVO.getIndicadoVO().getCodigo())) {
                retorno = smsController.sendMessage(null, new Message().setNumero(numeroCelular).setMsg(ModeloMensagemVO.personalizarTagNomePessoa(obs, historicoContatoVO.getIndicadoVO().getNomeIndicado())));
            } else {
                retorno = smsController.sendMessage(null, new Message().setNumero(numeroCelular).setMsg(ModeloMensagemVO.personalizarTagNomePessoa(obs, historicoContatoVO.getPassivoVO().getNome())));
            }

            if (retorno.toUpperCase().contains("STATUS: OK")) {
                malaDiretaVO.setUsuarioVO(usuarioVO);
                malaDiretaVO.setRemetente(usuarioVO);
                historicoContatoDAO.gravarHistoricoContato("SR", historicoContatoVO, agendaVO, malaDiretaVO, empresaVO.getCodigo());
            } else {
                throw new Exception(retorno);
            }
        } finally {
            empresaDAO = null;
            configuracaoSistemaDAO = null;
            configuracaoSistemaCRMDAO = null;
            historicoContatoDAO = null;
        }
    }

    private void gravarObjecao(HistoricoContatoVO historicoContatoVO, EmpresaVO empresaVO,
                               UsuarioVO usuarioVO, Connection con) throws Exception {
        Objecao objecaoDAO;
        HistoricoContato historicoContatoDAO;
        try {
            historicoContatoDAO = new HistoricoContato(con);
            objecaoDAO = new Objecao(con);

            if (validarMetaNaoAberta(empresaVO, usuarioVO, con)) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
            validarPermissao(empresaVO, "RealizarContato", "7.23 - Realizar Contato", usuarioVO, con);

            if (historicoContatoVO.getObjecaoVO().getCodigo() == 0) {
                throw new ConsistirException("Selecione a objeção.");
            }

            ObjecaoVO objecaoVO = objecaoDAO.consultarPorChavePrimaria(historicoContatoVO.getObjecaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            historicoContatoVO.setObjecaoVO(objecaoVO);
            historicoContatoDAO.gravarHistoricoContato("OB", historicoContatoVO, null, null, empresaVO.getCodigo());
        } finally {
            objecaoDAO = null;
            historicoContatoDAO = null;
        }
    }

    private EnvelopeRespostaDTO sesi(Connection con, HttpServletRequest request) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            zillyonWebFacade = new ZillyonWebFacade(con);

            String chave = obterChave(request);
            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer codUsuario = jsonBody.optInt("usuario");
            Integer codEmpresa = jsonBody.optInt("empresa");
            Integer codCliente = jsonBody.optInt("cliente");
            String tipo = jsonBody.optString("tipo");

            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            if (tipo.equalsIgnoreCase("imprimir_nota_fiscal")) {

                Integer codNotaFiscal = jsonBody.optInt("notafiscal");
                if (UteisValidacao.emptyNumber(codNotaFiscal)) {
                    throw new Exception("Nota fiscal não informado");
                }

//                Conexao.guardarConexaoForJ2SE(chave, con);
//                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//                ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                String pathRelatorio = this.getServletContext().getRealPath("servlet-relatorio");
                String nomeArquivo = String.format("%s-%s-%s.pdf", new Object[]{
                        "notaSesi",
                        chave,
                        new Date().getTime()
                });

                String notaBase64 = zillyonWebFacade.startThreadIntegracaoFiescImprimirNota(0, codNotaFiscal);
                File file = new File(pathRelatorio + File.separator + nomeArquivo);
                try (FileOutputStream fos = new FileOutputStream(file);) {
                    // To be short I use a corrupted PDF string, so make sure to use a valid one if you want to preview the PDF file
                    byte[] decoder = Base64.getDecoder().decode(notaBase64);
                    fos.write(decoder);
                }
                String link = (getUrlAplicacaoPastaRelatorio(chave, request) + nomeArquivo);
                return EnvelopeRespostaDTO.of(link);
            }
            throw new Exception("Operação não encontrada");
        } finally {
            clienteDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            zillyonWebFacade = null;
        }
    }

    private EnvelopeRespostaDTO totalPass(HttpServletRequest request, Connection con) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        Empresa empresaDAO;
        try {
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            empresaDAO = new Empresa(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codPessoa = jsonObject.optInt("pessoa");
            Integer codUsuario = jsonObject.optInt("usuario");
            Integer codEmpresa = jsonObject.optInt("empresa");
            String operacao = jsonObject.optString("operacao");
            String chave = obterChave(request);

            if (UteisValidacao.emptyNumber(codPessoa)) {
                throw new Exception("Pessoa não informada");
            }
            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                throw new Exception("Empresa não informado");
            }

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_TODOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            switch (operacao) {
                case "validar":
                    Conexao.guardarConexaoForJ2SE(chave, con);
                    clienteDAO.validarTotalPass(clienteVO, empresaVO, usuarioVO);
                    return EnvelopeRespostaDTO.of("ok");
                case "desvincular":
                    Conexao.guardarConexaoForJ2SE(chave, con);
                    String nomeAluno = clienteDAO.desvincularAlunoTotalpass(codPessoa.longValue(), usuarioVO);
                    return EnvelopeRespostaDTO.of("TotalPass desvinculado com sucesso para o aluno: " + nomeAluno);
                default:
                    throw new Exception("Nenhuma operacao executada");
            }
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO enviarEmailOptin(HttpServletRequest request, Connection con) throws Exception {
        Optin optinDAO;
        Empresa empresaDAO;
        Cliente clienteDAO;
        ConfiguracaoSistema configuracaoSistemaDAO;
        try {
            optinDAO = new Optin(con);
            empresaDAO = new Empresa(con);
            clienteDAO = new Cliente(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);

            String chaveEmpresa = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer codigoEmpresa = json.optInt("empresa");
            Integer codigoCliente = json.optInt("cliente");

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            boolean configuracaoSesc = false;
            if (configuracaoSistemaVO != null) {
                configuracaoSesc = configuracaoSistemaVO.getSesc();
            }

            optinDAO.enviarEmailOptin(empresaVO, clienteVO, chaveEmpresa, configuracaoSesc);

            return EnvelopeRespostaDTO.of("E-mail enviado");
        } finally {
            optinDAO = null;
            empresaDAO = null;
            clienteDAO = null;
            configuracaoSistemaDAO = null;
        }
    }

    private String boletoRedirect(Connection con, HttpServletRequest request) throws Exception {

        String chave = obterChave(request);
        String tipoOperacao = request.getParameter("tipo");
        Integer remessa_item = UteisValidacao.converterInteiro(request.getParameter("remessa_item"));
        Integer boleto = UteisValidacao.converterInteiro(request.getParameter("boleto"));

        switch (tipoOperacao) {
            case "imprimir":
                String urlImprimir = new TelaClienteBoleto().imprimirRemessaItem(chave, remessa_item, request, con);
                return urlImprimir;
            case "imprimirBBOnline":
                BoletoService service = new BoletoService(con);
                String urlPDFBoleto = service.imprimirBoletoBancoBrasilNovaTelaCliente(boleto, request);
                return urlPDFBoleto;
            default:
                throw new Exception("Operação não encontrada");
        }

    }

    public static void registrarLogExclusaoObjetoVO(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa, UsuarioVO usuarioVO, Log logDao) throws Exception {
        List lista = ObjetoVO.gerarLogExclusaoObjetoVO(usuarioVO);
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            LogVO log = (LogVO) i.next();
            log.setChavePrimaria(codigoCliente.toString());
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            logDao.incluirSemCommit(log);
        }
    }

    private EnvelopeRespostaDTO enviarEmailReciboCancelamentoCobrancaGetcard(HttpServletRequest request, Connection con) throws Exception {
        JSONObject json = new JSONObject(obterBody(request).toString());
        String retorno = "";

        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(con);

            String email = json.optString("email");
            Integer codEmpresa = json.optInt("empresa");
            String mensagem = json.optString("mensagem");
            String idTransacao = json.optString("recibo");

            if (!UteisValidacao.validaEmail(email)) {
                throw new Exception("Não foi possível enviar o contrato pois nenhum e-mail informado é válido.");
            }

            String[] emails = new String[1];
            emails[0] = email;

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo("Recibo Cancelamento Cobrança Getcard", configuracaoSistemaCRMVO);
            uteisEmail.setRemetente(configuracaoSistemaCRMVO.getRemetentePadraoMailing());

            String textoEmail = ("ID Getcard da Transação: " + idTransacao + "<br/><br/>");
            textoEmail = textoEmail + "<pre>" + mensagem + "</pre>";

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            uteisEmail.enviarEmailN(emails, textoEmail, "Recibo Cancelamento Cobrança Getcard", empresaVO.getNome());

            retorno = "E-mail enviado";
        } catch (Exception e) {
            throw new Exception("Erro ao enviar e-mail de recibo de cancelamento de cobrança Getcard: " + e.getMessage());
        } finally {
            empresaDAO = null;
        }

        return EnvelopeRespostaDTO.of(retorno);
    }

    private EnvelopeRespostaDTO acessoConvidado(HttpServletRequest request, Connection con) throws Exception {
        Usuario usuarioDAO;
        Cliente clienteDAO;
        Produto produtoDAO;
        Convite conviteDAO;
        try {
            usuarioDAO = new Usuario(con);
            clienteDAO = new Cliente(con);
            produtoDAO = new Produto(con);
            conviteDAO = new Convite(con);

            String chave = obterChave(request);
            JSONObject json = new JSONObject(obterBody(request));
            Integer usuario = json.getInt("usuario");
            Integer cliente = json.getInt("cliente");
            String operacao = json.getString("operacao");

            if (UteisValidacao.emptyNumber(usuario)) {
                throw new Exception("Usuario não informado");
            }
            if (UteisValidacao.emptyNumber(cliente)) {
                throw new Exception("Cliente não informado");
            }
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Operação não informado");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_TODOS);

            if (operacao.equalsIgnoreCase("ativar")) {
                clienteVO.setResponsavelFreePass(usuarioVO.getCodigo());
                clienteVO.setFreePass(produtoDAO.criarOuConsultarProdutoPorTipoNrDiasVigencia(TipoProduto.FREEPASS.getCodigo(), 1, "CONVITE", Uteis.NIVELMONTARDADOS_MINIMOS));
                clienteDAO.incluirFreePass(clienteVO, Calendario.hoje(), null, null, null);
                conviteDAO.atualizarFaltaAcessoConvidado(clienteVO);
                return EnvelopeRespostaDTO.of("ok");
            } else if (operacao.equalsIgnoreCase("apresentar")) {
                boolean apresentarAtivar = clienteVO.getSituacao().equals("VI") && conviteDAO.faltaLancarAcessoConvidado(clienteVO);
                return EnvelopeRespostaDTO.of(apresentarAtivar);
            } else {
                throw new Exception("Operação não implementada");
            }
        } finally {
            usuarioDAO = null;
            clienteDAO = null;
            produtoDAO = null;
            conviteDAO = null;
        }
    }

    private EnvelopeRespostaDTO validarLinkPagamento(HttpServletRequest request, Connection con) throws Exception {
        Cliente clienteDAO;
        Empresa empresaDAO;

        try {
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);

            JSONObject jsonObject = getJSONBody(request);
            Integer codCliente = jsonObject.optInt("cliente");

            if (UteisValidacao.emptyNumber(codCliente)) {
                String mensagem = "Cliente não informado.";

                return EnvelopeRespostaDTO.erro("Falta de convênio", mensagem);
            }

            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            boolean existeConvenioPixConfigurado = !UteisValidacao.emptyNumber(empresaDAO.obterConvenioCobrancaPix(clienteVO.getEmpresa().getCodigo()));
            boolean existeConvenioCartaoConfigurado = !UteisValidacao.emptyNumber(empresaDAO.obterConvenioCobrancaCartao(clienteVO.getEmpresa().getCodigo()));
            boolean existeConvenioBoletoConfigurado = !UteisValidacao.emptyNumber(empresaDAO.obterConvenioCobrancaBoleto(clienteVO.getEmpresa().getCodigo()));

            if (!existeConvenioPixConfigurado && !existeConvenioCartaoConfigurado && !existeConvenioBoletoConfigurado) {
                String mensagem = "Não encontrei nenhum convênio de cobrança para o link de pagamento online (pix ou cartão ou boleto) (Configurações da empresa na aba Cobrança).";

                return EnvelopeRespostaDTO.erro("Falta de convênio", mensagem);
            }

            return EnvelopeRespostaDTO.of("");
        } finally {
            clienteDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO boletoToken(Connection con, HttpServletRequest request) throws Exception {

        String chave = obterChave(request);
        JSONObject jsonBody = new JSONObject(obterBody(request));
        String operacao = jsonBody.optString("operacao");
        String token = jsonBody.optString("token");
        Integer usuario = jsonBody.optInt("usuario");

        switch (operacao) {
            case "email":
                TokenBoleto tokenBoletoDAO = new TokenBoleto(con);
                TokenBoletoVO tokenBoletoVO = tokenBoletoDAO.consultarPorToken(token);
                if (tokenBoletoVO == null) {
                    throw new Exception("Boleto não encontrado");
                }

                String email = jsonBody.getString("email");

                JSONObject bodyDados = new JSONObject(tokenBoletoVO.getDados());
                if (!bodyDados.has("boletos")) {
                    throw new Exception("Dados do token inválidos: boletos não encontrados");
                }

                JSONArray boletos = bodyDados.getJSONArray("boletos");
                if (boletos.length() == 0) {
                    throw new Exception("Nenhum boleto encontrado no token");
                }

                for (int i = 0; i < boletos .length(); i++) {
                    JSONObject bol = boletos.getJSONObject(i);
                    if (!bol.has("tipoCobranca") || !bol.has("codigo")) {
                        throw new Exception("Dados do boleto incompletos no índice " + i);
                    }

                    Integer tipoCobranca = bol.getInt("tipoCobranca");
                    Integer codigo = bol.getInt("codigo");
                    if (tipoCobranca.equals(TipoCobrancaEnum.BOLETO_ONLINE.getId())) {
                        new TelaClienteBoleto().enviarEmailBoleto(chave, codigo, usuario, email, request, con);
                    } else if (tipoCobranca.equals(TipoCobrancaEnum.BOLETO.getId())) {
                        new TelaClienteBoleto().enviarEmailRemessaItem(chave, codigo, usuario, email, request, con);
                    } else {
                        throw new Exception("Tipo cobrança não identificada");
                    }
                }
                return EnvelopeRespostaDTO.of("ok");
            default:
                throw new Exception("Operação não encontrada");
        }

    }
}
