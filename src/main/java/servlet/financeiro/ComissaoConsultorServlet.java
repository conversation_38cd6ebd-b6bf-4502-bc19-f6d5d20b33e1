package servlet.financeiro;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ComissaoRelTO;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.ComissaoGeralConfiguracao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.basico.ComissaoControle;
import relatorio.negocio.jdbc.contrato.ComissaoRel;
import servlet.arquitetura.SuperServlet;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.sql.Connection;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ComissaoConsultorServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try{
            Uteis.logarDebug("Iniciando consulta de comissão de consultor");
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String method = request.getMethod();
            if (!method.equalsIgnoreCase("GET")) {
                throw new Exception("Método não suportado.");
            }

            String chave = request.getParameter("chave");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada.");
            }

            String dataInicioLancamento = request.getParameter("dataInicioLancamento");
            if (Objects.isNull(dataInicioLancamento)) {
                throw new Exception("Informe a data de inicio.");
            }

            String dataFimLancamento = request.getParameter("dataFimLancamento");
            if (Objects.isNull(dataFimLancamento)) {
                throw new Exception("Informe a data final da conciliação.");
            }

            Date dataReceb = null;
            String dataRecebimento = request.getParameter("dataRecebimento");
            if(!Objects.isNull(dataRecebimento)){
                dataReceb = Calendario.getDate("dd/MM/yyyy", dataRecebimento);
            }

            Date dataComp;
            String dataCompetencia = request.getParameter("dataCompetencia");
            if(!Objects.isNull(dataCompetencia)){
                dataComp = Calendario.getDate("dd/MM/yyyy", dataCompetencia);
            } else {
                dataComp = null;
            }

            Date dataLancadosApartir = null;
            String dataLancadosApartirString = request.getParameter("dataLancadosApartir");
            if(!Objects.isNull(dataLancadosApartirString)){
                dataLancadosApartir = Calendario.getDate("dd/MM/yyyy", dataLancadosApartirString);
            }

            String codigoEmpresa = request.getParameter("codigoEmpresa");
            if(Objects.isNull(codigoEmpresa)){
                throw new Exception("Informe o codigo da empresa.");
            }

            Date dataInicioLan = Calendario.getDate("dd/MM/yyyy", dataInicioLancamento);
            Date dataFimLan = Calendario.getDate("dd/MM/yyyy", dataFimLancamento);

            long diffInMillies = Math.abs(dataInicioLan.getTime() - dataFimLan.getTime());
            long diffInDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);

            if (diffInDays > 31) {
                throw new Exception("O intervalo de datas não pode ser superior a 31 dias.");
            }


            //  RECEITA /FATURAMENTO/ FATURAMENTO_DE_CAIXA/ COMPETENCIA
            String tipoRelatorioParam = request.getParameter("tipoRelatorio");
            TipoRelatorioDF tipoRelatorioDF = Arrays.stream(TipoRelatorioDF.values())
                    .filter(a -> a.name().equals(tipoRelatorioParam))
                    .findFirst()
                    .orElse(TipoRelatorioDF.FATURAMENTO_DE_CAIXA);

            //PORC / FIXO
            String tipoValorComissoes = request.getParameter("tipoValorComissoes") != null?
                    request.getParameter("tipoValorComissoes") :"PORC";

            //RL / CO / CA / SO
            String impressaoPor = request.getParameter("impressaoPor") != null ?
                    request.getParameter("impressaoPor"): "RL";

            if(tipoRelatorioDF.equals(TipoRelatorioDF.COMPETENCIA) && dataCompetencia == null){
                throw new Exception("Data competencia não pode ser nula para esse relatorio");
            }

            Connection con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForServlet(chave, con);

            EmpresaVO empresa =  obterEmpresa(con ,Integer.parseInt(codigoEmpresa));
            boolean retirarRecebiveisComPendencia = false;

            List<ComissaoRel> listaRegistro = getService(con).processarDados(
                    tipoRelatorioDF,
                    empresa.getCodigo(),
                    dataInicioLan,
                    dataFimLan,
                    dataReceb,
                    dataLancadosApartir,
                    dataComp,
                    impressaoPor,
                    tipoValorComissoes,
                    empresa.isComissaoMatriculaRematricula(),
                    empresa.isPagarComissaoManutencaoModalidade(),
                    empresa.isPagarComissaoProdutos(),
                    empresa.isRetirarEdicaoPagamento(),
                    retirarRecebiveisComPendencia,
                    false
            );

            ComissaoControle comissaoControle = new ComissaoControle();
            comissaoControle.setEmpresa(empresa);
            comissaoControle.filtrarEOrdenarListaDeRegistrosServlet(listaRegistro);

            List<ComissaoRelTO> dadosconsulta = listaRegistro.stream().map(ComissaoRel::getTO).collect(Collectors.toList());
            if(TipoRelatorioDF.COMPETENCIA.equals(tipoRelatorioDF)){
                dadosconsulta.forEach(d -> {
                    d.setDataCompetencia(dataComp);
                });
            }
            comissaoControle.preencherDataLancamentoContrato(dadosconsulta);

            JSONObject json = new JSONObject();
            json.put("data", dadosconsulta);

            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().append(this.toJSON(true, json).toString());
            Uteis.logarDebug("Dados comissão de consultar enviados com sucesso");
            con.close();
            con = null;
        }
        catch (SocketTimeoutException | TimeoutException e) {
            response.setStatus(HttpServletResponse.SC_REQUEST_TIMEOUT); // 408
            response.getWriter().write("{\"erro\":\"Tempo de processamento excedido. Tente novamente com menos dados.\"}");
        }
        catch (Exception e){
            e.printStackTrace();
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, e.getCause().getMessage()).toString());
        }
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("data", dados);
        return json;
    }

    private ComissaoGeralConfiguracao getService(Connection con) throws Exception {
        return new ComissaoGeralConfiguracao(con);
    }

    private EmpresaVO obterEmpresa(Connection con, Integer codigoEmpresa) throws Exception {
        return new Empresa(con).consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_LANCAMENTOS_FINANCEIROS);
    }
}
