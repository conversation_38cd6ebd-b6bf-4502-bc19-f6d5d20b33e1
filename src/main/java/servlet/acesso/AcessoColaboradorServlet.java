/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servlet.acesso;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

/**
 *
 * <AUTHOR>
 */
public class AcessoColaboradorServlet extends SuperServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */

    private static String ERROR = "error";
    private static String RETURN = "return";
    private static String RETURN_SUCESSO = "sucesso";
    private static String SUCESSO = "sucesso";

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        JSONObject jsonResponse = new JSONObject();

        try {
            String key = request.getParameter("k");
            String op = request.getParameter("op");
            String codUsuario = request.getParameter("usu");

            switch (op){
                case "registrarAcesso":
                    Integer usuario = Integer.parseInt(codUsuario);
                    registrarLoginTreinoWeb(key, usuario, jsonResponse);
                    break;
                case "aaaa":
                    break;
            }

        } catch (Exception ex) {
            Uteis.logar(null, "ERRO - REQUISIÇÃO AcessoColaboradorServlet: " + ex.getMessage());
            ex.printStackTrace();
            jsonResponse.put(SUCESSO, false);
            jsonResponse.put(ERROR, ex.getMessage().toUpperCase());
        }
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        response.getOutputStream().print(jsonResponse.toString());
    }

    private void registrarLoginTreinoWeb(String key, Integer usuario, JSONObject jsonResponse) {
        try (Connection con = new DAO().obterConexaoEspecifica(key)) {
            UsuarioVO usuarioVO = new UsuarioVO();
            usuarioVO.setCodigo(usuario);

            Usuario usuarioDAO = new Usuario(con);
            usuarioDAO.registrarUltimoLoginAcessoAgora(usuarioVO);
            usuarioDAO = null;
            jsonResponse.put(SUCESSO, true);
            jsonResponse.put(RETURN, RETURN_SUCESSO);
        } catch (Exception ex) {
            jsonResponse.put(SUCESSO, false);
            jsonResponse.put(ERROR, ex.getMessage());
        }
    }

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
