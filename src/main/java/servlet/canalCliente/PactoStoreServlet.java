package servlet.canalCliente;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.MenuControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.session.SessionTO;
import controle.arquitetura.session.listener.SessionState;
import controle.basico.FuncionalidadeControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioPerfilAcesso;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Estado;
import negocio.facade.jdbc.basico.Pais;
import negocio.facade.jdbc.utilitarias.Cep;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.adm.beans.EmpresaWS;
import servicos.vendasonline.VendasOnlineService;
import servicos.vendasonline.dto.RetornoVendaTO;
import servicos.vendasonline.dto.VendaDTO;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 13/06/2020
 */
public class PactoStoreServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try {
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Método não suportado.");
            }

            String key = request.getParameter("chave");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }

            OperacaoPactoStoreDTO operacaoDTO = null;

            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                StringBuffer body = new StringBuffer();
                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }

                operacaoDTO = new OperacaoPactoStoreDTO(new JSONObject(body.toString()));
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            con = new DAO().obterConexaoEspecifica(key);

            Object objRetorno = null;
            if (operacaoDTO.getOperacao().equalsIgnoreCase("consultarInfoEmpresa")) {
                InfoEmpresaDTO infoEmpresaDTO = consultarInfoEmpresa(operacaoDTO, con);
                objRetorno = new JSONObject(infoEmpresaDTO);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("consultarInfoUsuario")) {
                InfoUsuarioDTO infoUsuarioDTO = consultarInfoUsuario(operacaoDTO, con);
                objRetorno = new JSONObject(infoUsuarioDTO);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("atualizarTokenSMSTransacional")) {
                objRetorno = atualizarTokenSMSTransacional(operacaoDTO, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("atualizarTokenSMSMarketing")) {
                objRetorno = atualizarTokenSMSMarketing(operacaoDTO, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("apresentarPactoStore")) {
                objRetorno = apresentarPactoStore(operacaoDTO, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("vendaPactoStore")) {
                objRetorno = vendaPactoStore(operacaoDTO, key, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("atualizarModulos")) {
                objRetorno = atualizarModulos(operacaoDTO, key, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("consultarEmpresas")) {
                objRetorno = consultarEmpresas(operacaoDTO, key, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("criarEmpresa")) {
                objRetorno = criarEmpresa(operacaoDTO, key, con);
            }

            if (objRetorno == null) {
                throw new Exception("Nenhuma operação executada");
            }

            response.getWriter().append(this.toJSON(true, objRetorno).toString());
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                }
            }
        }
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }

    private InfoEmpresaDTO consultarInfoEmpresa(OperacaoPactoStoreDTO operacaoDTO, Connection con) throws Exception {
        try {

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("emp.codigo, \n");
            sql.append("emp.nome, \n");
            sql.append("emp.cnpj, \n");
            sql.append("emp.razaosocial, \n");
            sql.append("emp.endereco, \n");
            sql.append("emp.numero, \n");
            sql.append("emp.complemento, \n");
            sql.append("emp.setor, \n");
            sql.append("emp.cep, \n");
            sql.append("emp.telcomercial1 as tel1, \n");
            sql.append("emp.telcomercial2 as tel2, \n");
            sql.append("emp.telcomercial3 as tel3, \n");
            sql.append("emp.email, \n");
            sql.append("emp.cod_empresafinanceiro as codigoEmpresaFinanceiro,  \n");
            sql.append("emp.tokensms, \n");
            sql.append("emp.tokensmsshortcode, \n");
            sql.append("to_char(emp.dataexpiracao, 'DD/MM/YYYY') as dataexpiracao,  \n");
            sql.append("emp.longitude,  \n");
            sql.append("emp.latitude,  \n");
            sql.append("ci.nome as cidade,  \n");
            sql.append("ci.nomesemacento as cidadeNomeSemAcendo,  \n");
            sql.append("es.descricao as estado,  \n");
            sql.append("es.sigla as estadoUF,  \n");
            sql.append("pa.nome as pais, \n");
            sql.append("emp.tipocobrancapacto,  \n");
            sql.append("emp.creditodcc as creditoatual,  \n");

            //cartão de credito
            sql.append("exists(select cc.codigo from conveniocobranca cc inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo ");
            sql.append("where cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" and cc.tipoconvenio in (").append(getListaCodigoTipoConvenioCobranca(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)).append(") and cce.empresa = emp.codigo) as cartao,  \n");
            //boleto
            sql.append("exists(select cc.codigo from conveniocobranca cc inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo ");
            sql.append("where cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" and cc.tipoconvenio in (").append(getListaCodigoTipoConvenioCobranca(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)).append(") and cce.empresa = emp.codigo) as boleto,  \n");
            //debito em conta
            sql.append("exists(select cc.codigo from conveniocobranca cc inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo ");
            sql.append("where cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" and cc.tipoconvenio in (").append(getListaCodigoTipoConvenioCobranca(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)).append(") and cce.empresa = emp.codigo) as debitoConta,  \n");

            sql.append("exists(select codigo from servidorfacial where empresa = emp.codigo) as facial,  \n");
            sql.append("exists(select codigo from configuracaoempresatotem where empresa = emp.codigo) as totem,  \n");
            sql.append("exists(select codigo from configuracaonotafiscal where empresa = emp.codigo) as notafiscal,  \n");
            sql.append("true as appgestor \n");
            sql.append("from empresa emp  \n");
            sql.append("left join cidade ci on ci.codigo = emp.cidade  \n");
            sql.append("left join estado es on es.codigo = emp.estado  \n");
            sql.append("left join pais pa on pa.codigo = emp.pais  \n");
            sql.append("where 1 = 1 \n");

            if (!UteisValidacao.emptyNumber(operacaoDTO.getEmpresa())) {
                sql.append("and emp.codigo = ").append(operacaoDTO.getEmpresa()).append(" \n");
            } else if (!UteisValidacao.emptyNumber(operacaoDTO.getEmpresaFinanceiro())) {
                sql.append("and emp.cod_empresafinanceiro = ").append(operacaoDTO.getEmpresaFinanceiro()).append(" \n");
            } else {
                throw new Exception("Necessário informar codigo empresa financeiro ou codigo da empresa");
            }

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            InfoEmpresaDTO dto = new InfoEmpresaDTO();
            if (rs.next()) {
                dto.setCodigo(rs.getInt("codigo"));
                dto.setNome(rs.getString("nome"));
                dto.setCnpj(rs.getString("cnpj"));
                dto.setRazaoSocial(rs.getString("razaosocial"));
                dto.setEndereco(rs.getString("endereco"));
                dto.setNumero(rs.getString("numero"));
                dto.setComplemento(rs.getString("complemento"));
                dto.setSetor(rs.getString("setor"));
                dto.setCep(rs.getString("cep"));
                dto.setTelcomercial1(rs.getString("tel1"));
                dto.setTelcomercial2(rs.getString("tel2"));
                dto.setTelcomercial3(rs.getString("tel3"));
                dto.setEmail(rs.getString("email"));
                dto.setCodigoEmpresaFinanceiro(rs.getInt("codigoEmpresaFinanceiro"));
                dto.setTokenSMSTransacional(rs.getString("tokensms"));
                dto.setTokenSMSMarketing(rs.getString("tokensmsshortcode"));
                dto.setDataExpiracao(rs.getString("dataexpiracao"));
                dto.setLongitude(rs.getString("longitude"));
                dto.setLatitude(rs.getString("latitude"));
                dto.setCidade(rs.getString("cidade"));
                dto.setCidadeNomeSemAcendo(rs.getString("cidadeNomeSemAcendo"));
                dto.setEstado(rs.getString("estado"));
                dto.setEstadoUF(rs.getString("estadoUF"));
                dto.setPais(rs.getString("pais"));
                dto.setCreditoDCCAtual(rs.getInt("creditoatual"));
                dto.setTipoCobrancaPacto(rs.getInt("tipocobrancapacto"));
                dto.setTipoCobrancaPactoDescricao(TipoCobrancaPactoEnum.getConsultarDescricaoPorCodigo(dto.getTipoCobrancaPacto()));
                dto.setAppGestor(rs.getBoolean("appgestor"));
                dto.setNotaFiscal(rs.getBoolean("notafiscal"));
                dto.setConvenioCartaoCredito(rs.getBoolean("cartao"));
                dto.setConvenioBoleto(rs.getBoolean("boleto"));
                dto.setConvenioDebitoConta(rs.getBoolean("debitoConta"));
                dto.setReconhecimentoFacial(rs.getBoolean("facial"));
                dto.setTotem(rs.getBoolean("totem"));
                return dto;
            } else {
                throw new Exception("Empresa não encontrada.");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private InfoUsuarioDTO consultarInfoUsuario(OperacaoPactoStoreDTO operacaoDTO, Connection con) throws Exception {
        Email emailDAO = null;
        try {

            if (UteisValidacao.emptyNumber(operacaoDTO.getUsuario())) {
                throw new Exception("Usuário não informado");
            }

            emailDAO = new Email(con);

            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("u.codigo as usuario, \n");
            sql.append("u.username, \n");
            sql.append("p.nome, \n");
            sql.append("p.codigo as pessoa, \n");
            sql.append("p.cfp as cpf, \n");
            sql.append("to_char(p.datanasc, 'DD/MM/YYYY') as dataNascimento, \n");
            sql.append("(select numero from telefone where tipotelefone = 'CE' and pessoa = p.codigo order by codigo desc limit 1) as celular, \n");
            sql.append("(select numero from telefone where tipotelefone not in ('CE') and pessoa = p.codigo order by codigo desc limit 1) as telefone, \n");
            sql.append("(select email from email where pessoa = p.codigo order by codigo desc limit 1) as email, \n");
            sql.append("ue.email as usuarioemail \n");
            sql.append("from usuario u  \n");
            sql.append("inner join colaborador c on c.codigo = u.colaborador \n");
            sql.append("inner join pessoa p on p.codigo = c.pessoa \n");
            sql.append("left join usuarioemail ue on ue.usuario = u.codigo \n");
            sql.append("where u.codigo = ").append(operacaoDTO.getUsuario());

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            InfoUsuarioDTO dto = new InfoUsuarioDTO();
            if (rs.next()) {
                dto.setCodigo(rs.getInt("usuario"));
                dto.setUsername(rs.getString("username"));
                dto.setNome(rs.getString("nome"));
                dto.setCpf(rs.getString("cpf"));
                dto.setDataNascimento(rs.getString("dataNascimento"));
                dto.setCelular(rs.getString("celular"));
                dto.setTelefone(rs.getString("telefone"));

                dto.setEmail(new ArrayList<>());

                Integer pessoa = rs.getInt("pessoa");
                List<EmailVO> emails = emailDAO.consultarEmails(pessoa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                for (EmailVO emailVO : emails) {
                    dto.getEmail().add(emailVO.getEmail());
                }

                String usuarioEmail = rs.getString("usuarioemail");
                if (!UteisValidacao.emptyString(usuarioEmail)) {
                    boolean existe = false;
                    for (String email : dto.getEmail()) {
                        if (email.equalsIgnoreCase(usuarioEmail)) {
                            existe = true;
                        }
                    }

                    if (!existe) {
                        dto.getEmail().add(usuarioEmail);
                    }
                }
                return dto;
            } else {
                throw new Exception("Usuário não encontrado.");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            emailDAO = null;
        }
    }

    private String getListaCodigoTipoConvenioCobranca(TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum) {
        String tipos = "";
        for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
            if (tipo.getTipoAutorizacao() != null && tipo.getTipoAutorizacao().equals(tipoAutorizacaoCobrancaEnum)) {
                tipos += "," + tipo.getCodigo();
            }
        }
        return tipos.replaceFirst(",", "");
    }

    private Object atualizarTokenSMSTransacional(OperacaoPactoStoreDTO operacaoDTO, Connection con) throws Exception {
        try {
            con.setAutoCommit(false);

            InfoEmpresaDTO infoEmpresaAnterior = consultarInfoEmpresa(operacaoDTO, con);

            StringBuilder update = new StringBuilder();
            update.append("update empresa set tokensms = ? where ");
            if (!UteisValidacao.emptyNumber(operacaoDTO.getEmpresa())) {
                update.append(" codigo = ").append(operacaoDTO.getEmpresa());
            } else if (!UteisValidacao.emptyNumber(operacaoDTO.getEmpresaFinanceiro())) {
                update.append(" cod_empresafinanceiro = ").append(operacaoDTO.getEmpresaFinanceiro());
            } else {
                throw new Exception("Necessário informar codigo empresa financeiro ou codigo da empresa");
            }

            try (PreparedStatement pst = con.prepareStatement(update.toString())) {
                int i = 0;
                pst.setString(++i, operacaoDTO.getTokenSMSTransacional());
                pst.execute();
            }

            gerarLogAlterarTokenSMSTransacional(infoEmpresaAnterior, operacaoDTO, con);

            con.commit();
            return "ok";
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            con.setAutoCommit(true);
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void gerarLogAlterarTokenSMSTransacional(InfoEmpresaDTO infoEmpresaAnterior, OperacaoPactoStoreDTO operacaoDTO, Connection con) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("EMPRESA");
            log.setNomeEntidadeDescricao("EMPRESA");
            log.setDescricao("EMPRESA-TOKEN-SMS");
            log.setChavePrimaria(infoEmpresaAnterior.getCodigo().toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setResponsavelAlteracao("PACTO STORE+");
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("EMPRESA-TOKEN-SMS");
            log.setValorCampoAnterior(infoEmpresaAnterior.getTokenSMSTransacional());
            log.setValorCampoAlterado(operacaoDTO.getTokenSMSTransacional());
            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private Object atualizarTokenSMSMarketing(OperacaoPactoStoreDTO operacaoDTO, Connection con) throws Exception {
        try {
            con.setAutoCommit(false);

            InfoEmpresaDTO infoEmpresaAnterior = consultarInfoEmpresa(operacaoDTO, con);

            StringBuilder update = new StringBuilder();
            update.append("update empresa set tokensmsshortcode = ? where ");
            if (!UteisValidacao.emptyNumber(operacaoDTO.getEmpresa())) {
                update.append(" codigo = ").append(operacaoDTO.getEmpresa());
            } else if (!UteisValidacao.emptyNumber(operacaoDTO.getEmpresaFinanceiro())) {
                update.append(" cod_empresafinanceiro = ").append(operacaoDTO.getEmpresaFinanceiro());
            } else {
                throw new Exception("Necessário informar codigo empresa financeiro ou codigo da empresa");
            }

            try (PreparedStatement pst = con.prepareStatement(update.toString())) {
                int i = 0;
                pst.setString(++i, operacaoDTO.getTokenSMSMarketing());
                pst.execute();
            }

            gerarLogAlterarTokenSMSMarketing(infoEmpresaAnterior, operacaoDTO, con);

            con.commit();
            return "ok";
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            con.setAutoCommit(true);
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void gerarLogAlterarTokenSMSMarketing(InfoEmpresaDTO infoEmpresaAnterior, OperacaoPactoStoreDTO operacaoDTO, Connection con) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("EMPRESA");
            log.setNomeEntidadeDescricao("EMPRESA");
            log.setDescricao("EMPRESA-TOKEN-SMS-SHORTCODE");
            log.setChavePrimaria(infoEmpresaAnterior.getCodigo().toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setResponsavelAlteracao("PACTO STORE+");
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("EMPRESA-TOKEN-SMS-SHORTCODE");
            log.setValorCampoAnterior(infoEmpresaAnterior.getTokenSMSMarketing());
            log.setValorCampoAlterado(operacaoDTO.getTokenSMSMarketing());
            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private Object apresentarPactoStore(OperacaoPactoStoreDTO operacaoDTO, Connection con) {
        try {

            if (UteisValidacao.emptyNumber(operacaoDTO.getEmpresa())) {
                throw new Exception("Empresa não informada");
            }

            if (UteisValidacao.emptyNumber(operacaoDTO.getUsuario())) {
                throw new Exception("Usuário não informado");
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select exists( \n");
            sql.append("select  \n");
            sql.append("u.codigo \n");
            sql.append("from usuario u \n");
            sql.append("inner join usuarioperfilacesso up on up.usuario = u.codigo \n");
            sql.append("inner join perfilacesso pa on pa.codigo = up.perfilacesso \n");
            sql.append("where pa.tipo = ").append(PerfilUsuarioEnum.ADMINISTRADOR.ordinal()).append(" \n");
            sql.append("and u.codigo = ").append(operacaoDTO.getUsuario()).append(" \n");
            sql.append("and up.empresa = ").append(operacaoDTO.getEmpresa()).append(" \n");
            sql.append("and (select apresentarmarketplace from configuracaosistema) \n");
            sql.append(") as apresentar \n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            if (rs.next()) {
                return rs.getBoolean("apresentar");
            }
            return false;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    private Object vendaPactoStore(OperacaoPactoStoreDTO operacaoDTO, String chave, Connection con) throws Exception {
        VendasOnlineService service = null;
        try {
            List<VendaDTO> listaVendaDTO = new ArrayList<>();
            JSONArray array = new JSONArray(operacaoDTO.getListaVenda());
            for (int e = 0; e < array.length(); e++) {
                JSONObject obj = array.getJSONObject(e);
                VendaDTO vendaDTO = new VendaDTO(obj.toString());
                listaVendaDTO.add(vendaDTO);
            }

            if (UteisValidacao.emptyList(listaVendaDTO)) {
                throw new Exception("Nenhuma venda informada");
            }

            service = new VendasOnlineService(chave, con);
            List<RetornoVendaTO> listaRetorno = service.incluirAlunoVenda(chave, listaVendaDTO, true, operacaoDTO.getCodigoFinanceiroPagador(), false);

            JSONArray arrayRetorno = new JSONArray();
            for (RetornoVendaTO retornoVendaTO : listaRetorno) {
                arrayRetorno.put(retornoVendaTO.toJSON());
            }
            return arrayRetorno;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            service = null;
        }
    }

    private Object atualizarModulos(OperacaoPactoStoreDTO operacaoDTO, String chave, Connection con) throws Exception {
        Connection conOAMD = null;
        try {
            conOAMD = Conexao.obterConexaoBancoEmpresas();

            String modulosHabilitados = new DAO().obterModulos(chave, conOAMD);

            List<SessionTO> sessions = SessionState.updateList();
            for (SessionTO sessionTO : sessions) {
                if (sessionTO.getChave() != null && sessionTO.getChave().equalsIgnoreCase(chave)) {
                    try {
                        sessionTO.getSession().setAttribute("modulosHabilitados", modulosHabilitados);
                    } catch (Exception ex) {
                    }
                    try {
                        LoginControle loginControle = (LoginControle) sessionTO.getSession().getAttribute("LoginControle");
                        if (loginControle != null) {
                            loginControle.getSession().setAttribute("modulosHabilitados", modulosHabilitados);
                            loginControle.montarModulosHabilitados(modulosHabilitados);
                        }
                    } catch (Exception ignored) {
                    }
                    try {
                        MenuControle menuControle = (MenuControle) sessionTO.getSession().getAttribute("MenuControle");
                        if (menuControle != null) {
                            menuControle.setReprocessar(true);
                        }
                    } catch (Exception ignored) {
                    }
                    try {
                        FuncionalidadeControle funcionalidadeControle = (FuncionalidadeControle) sessionTO.getSession().getAttribute("FuncionalidadeControle");
                        if (funcionalidadeControle != null) {
                            funcionalidadeControle.setFuncHabilitadas(new HashMap<>());
                        }
                    } catch (Exception ignored) {
                    }
                }
            }
            return "ok";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            if (conOAMD != null) {
                try {
                    conOAMD.close();
                } catch (Exception ex) {
                }
            }
        }
    }

    private Object consultarEmpresas(OperacaoPactoStoreDTO operacaoDTO, String chave, Connection con) throws Exception {
        Empresa empresaDAO = null;
        try {
            empresaDAO = new Empresa(con);

            List<EmpresaVO> listaEmpresas;
            if (operacaoDTO.isSomenteAtiva()) {
                listaEmpresas = empresaDAO.consultarTodas(true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                listaEmpresas = empresaDAO.consultarTodas(null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            Ordenacao.ordenarLista(listaEmpresas, "nome");

            JSONArray array = new JSONArray();
            for (EmpresaVO obj : listaEmpresas) {
                JSONObject json = new JSONObject(new EmpresaWS(obj));
                json.put("ativa", obj.isAtiva());
                json.put("tipoCobrancaPacto", obj.getTipoCobrancaPacto());
                array.put(json);
            }
            return array;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            empresaDAO = null;
        }
    }

    private Object criarEmpresa(OperacaoPactoStoreDTO operacaoDTO, String chave, Connection con) throws Exception {
        Cep cepDAO = null;
        Cidade cidadeDAO = null;
        Estado estadoDAO = null;
        Pais paisDAO = null;
        Usuario usuarioDAO = null;
        Empresa empresaDAO = null;
        UsuarioPerfilAcesso usuarioPerfilAcessoDAO = null;
        try {
            cepDAO = new Cep();
            cidadeDAO = new Cidade(con);
            estadoDAO = new Estado(con);
            paisDAO = new Pais(con);
            usuarioDAO = new Usuario(con);
            empresaDAO = new Empresa(con);
            usuarioPerfilAcessoDAO = new UsuarioPerfilAcesso(con);

            EmpresaVO novaVO = new EmpresaVO();

            try {
                CepVO obj = cepDAO.consultarPorNumeroCep(Uteis.removerMascara(operacaoDTO.getCep()), Uteis.NIVELMONTARDADOS_TODOS);
                novaVO.setSetor(obj.getBairroDescricao().trim());
                novaVO.setEndereco(obj.getEnderecoLogradouro().trim());
                novaVO.setComplemento(obj.getEnderecoCompleto().trim());
                CidadeVO objCidade = cidadeDAO.consultarPorNome(Uteis.retirarAcentuacao(obj.getCidadeDescricao().trim()), Uteis.NIVELMONTARDADOS_TODOS);
                if (objCidade != null) {
                    novaVO.setPais(objCidade.getPais());
                    novaVO.setEstado(objCidade.getEstado());
                    novaVO.setCidade(objCidade);
                } else {
                    objCidade = new CidadeVO();
                    PaisVO objPais = paisDAO.consultarPorNome("Brasil", Uteis.NIVELMONTARDADOS_TODOS);
                    if (objPais == null) {
                        throw new Exception("O pais de nome Brasil deve ser cadastrado.");
                    }
                    EstadoVO objEstado = estadoDAO.consultarPorSiglaDescricaoEPais(obj.getUfSigla().trim(), obj.getUfDescricao().trim(), objPais.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
                    if (objEstado == null) {
                        objEstado = new EstadoVO();
                        objEstado.setDescricao(obj.getUfDescricao());
                        objEstado.setSigla(obj.getUfSigla());
                        objEstado.setPais(objPais.getCodigo());
                        estadoDAO.incluir(objEstado);
                    }
                    objCidade.setPais(objPais);
                    objCidade.setEstado(objEstado);
                    objCidade.setNome(obj.getCidadeDescricao().trim());
                    cidadeDAO.incluir(objCidade);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            novaVO.setCarenciaRenovacao(15);
            novaVO.setNrDiasAvencer(15);
            novaVO.setMascaraMatricula("xxxxxx");
            novaVO.setTempoSaidaAcademia(60);
            novaVO.setTempoSaidaAcademiaFormatada("01:00");
            novaVO.setNome(operacaoDTO.getNomeFantasia());
            novaVO.setRazaoSocial(operacaoDTO.getRazaoSocial());
            novaVO.setCNPJ(Uteis.formatarCpfCnpj(operacaoDTO.getCnpj(), false));
            novaVO.setCEP(operacaoDTO.getCep());
            if (UteisValidacao.emptyString(novaVO.getEndereco())) {
                novaVO.setEndereco(operacaoDTO.getEndereco());
            } else {
                novaVO.setComplemento(operacaoDTO.getEndereco());
            }

            empresaDAO.incluir(novaVO);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(operacaoDTO.getUsuario(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //adicionar permissão para o usuário que comprou
            List<UsuarioPerfilAcessoVO> listaPerfil = usuarioPerfilAcessoDAO.consultarUsuarioPerfilAcesso(operacaoDTO.getUsuario(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PerfilAcessoVO perfilAcessoVO = listaPerfil.get(0).getPerfilAcesso();
            for (UsuarioPerfilAcessoVO perf : listaPerfil) {
                if (perf.getEmpresa().getCodigo().equals(operacaoDTO.getEmpresa())) {
                    perfilAcessoVO = perf.getPerfilAcesso();
                    break;
                }
            }

            UsuarioPerfilAcessoVO novoPerfil = new UsuarioPerfilAcessoVO();
            novoPerfil.setUsuario(usuarioVO.getCodigo());
            novoPerfil.setUsuarioVO(usuarioVO);
            novoPerfil.setEmpresa(novaVO);
            novoPerfil.setPerfilAcesso(perfilAcessoVO);
            usuarioPerfilAcessoDAO.incluir(novoPerfil);
            return "ok";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            cepDAO = null;
            cidadeDAO = null;
            estadoDAO = null;
            paisDAO = null;
            usuarioDAO = null;
            empresaDAO = null;
            usuarioPerfilAcessoDAO = null;
        }
    }

}
