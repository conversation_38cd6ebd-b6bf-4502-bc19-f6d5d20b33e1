package servlet.aulacheia;

import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.agendatotal.json.TurmaAulaCheiaJSON;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.dto.FilaDeEsperaTO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.AutorizacaoAcessoGrupoEmpresarial;
import negocio.facade.jdbc.basico.Reposicao;
import negocio.facade.jdbc.plano.PlanoModalidade;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.vendas.*;
import negocio.interfaces.acesso.AutorizacaoAcessoGrupoEmpresarialInterfaceFacade;
import negocio.interfaces.basico.ReposicaoInterfaceFacade;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.oamd.RedeEmpresaService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class AulaCheiaServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }


    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];
        String mapaAulasExcluidas = request.getParameter("mapaAulasExcluidas") == null ? null :  request.getParameter("mapaAulasExcluidas");
        String mapaProfessoresSubstituidos = request.getParameter("mapaProfessoresSubstituidos") == null ? null :  request.getParameter("mapaProfessoresSubstituidos");
        String chave = request.getParameter("chave");
        Integer empresa = request.getParameter("empresa") == null ? null : Integer.valueOf(request.getParameter("empresa"));
        Integer mes = request.getParameter("mes") == null ? null : Integer.valueOf(request.getParameter("mes"));
        Integer ano = request.getParameter("ano") == null ? null : Integer.valueOf(request.getParameter("ano"));
        Long inicio = request.getParameter("inicio") == null ? null : Long.valueOf(request.getParameter("inicio"));
        Long fim = request.getParameter("fim") == null ? null : Long.valueOf(request.getParameter("fim"));
        Integer professor = request.getParameter("professor") == null ? null :  Integer.valueOf(request.getParameter("professor"));
        Integer diaDaSemana = request.getParameter("diaDaSemana") == null ? null :  Integer.valueOf(request.getParameter("diaDaSemana"));
        String horaInicial = request.getParameter("horaInicio") == null ? null :  request.getParameter("horaInicio");
        String horaFinal = request.getParameter("horaFim") == null ? null :  request.getParameter("horaFim");
        Integer max = request.getParameter("max") == null ? null : Integer.valueOf(request.getParameter("max"));
        Integer index = request.getParameter("index") == null ? null : Integer.valueOf(request.getParameter("index"));
        Boolean count = request.getParameter("count") == null ? null : Boolean.valueOf(request.getParameter("count"));
        Integer codigoRelatorio = request.getParameter("codigoRelatorio") == null ? null : Integer.valueOf(request.getParameter("codigoRelatorio"));
        String quicksearchValue = request.getParameter("quicksearchValue") == null ? null : request.getParameter("quicksearchValue");
        String matricula = null;
        Long dia = null;
        Integer origemSistema = (request.getParameter("origemSistema") != null ? Integer.valueOf(request.getParameter("origemSistema")) : null);
        Integer codigoUsuario = (request.getParameter("codigoUsuario") != null ? Integer.valueOf(request.getParameter("codigoUsuario")) : null);
        RedeEmpresaVO redeEmpresaVO = new RedeEmpresaVO();
        String chaveOrigem;
        Integer matriculaAutorizado;
        try (TurmasServiceInterface service = getService(chave)) {
            switch (recurso){
                case "aulas":
                    ListaPaginadaTO paginacao = new ListaPaginadaTO(request.getParameter("size") == null ? 10000 : Integer.valueOf(request.getParameter("size")));
                    String sort = request.getParameter("sort");
                    if(!UteisValidacao.emptyString(sort)){
                        String[] split = sort.split(",");
                        paginacao.setOrderBy(split[0]);
                        paginacao.setOrderByDesc(split[1].equals("DESC"));
                    }
                    Integer codAulaEdit = request.getParameter("codigo") == null ? null : Integer.valueOf(request.getParameter("codigo"));
                    paginacao.setOffset((request.getParameter("page") == null ? 0 : Integer.valueOf(request.getParameter("page"))) * paginacao.getLimit());
                    List<TurmaAulaCheiaJSON> aulasColetivas = service.obterAulasColetivas(empresa, paginacao,
                            codAulaEdit == null ?
                                    (request.getParameter("filtros") == null ? null : new JSONObject(request.getParameter("filtros"))) :
                                    new JSONObject().put("codigo", codAulaEdit));
                    JSONObject content = new JSONObject().put("content", new JSONArray(aulasColetivas.isEmpty() ? new ArrayList<TurmaAulaCheiaJSON>() : aulasColetivas).toString());
                    content.put("total", paginacao.getCount());
                    response.getWriter().append(content.toString());
                    break;
                case "professor-alunos-crossfit":
                    JSONArray alunosCrossfitMes = service.alunosCrossfitMes(mes, ano, empresa, professor, diaDaSemana, horaInicial, horaFinal, max, index, count);
                    response.getWriter().append(alunosCrossfitMes.toString());
                    break;
                case "dados-booking":
                    matricula = request.getParameter("matricula");
                    String classid = request.getParameter("classid");
                    JSONObject dados = service.dadosBooking(classid, matricula);
                    response.getWriter().append(dados.toString());
                    break;
                case "aluno-em-aula":
                    matricula = request.getParameter("matricula");
                    dia = request.getParameter("dia") == null ? null : Long.valueOf(request.getParameter("dia"));
                    Integer idHorarioTurma = Integer.valueOf(request.getParameter("idHorarioTurma"));
                    JSONObject dadoAlunoEmAula = new JSONObject();
                    dadoAlunoEmAula.put("content", service.checarAlunoEmAula(matricula, idHorarioTurma, new Date(dia)));
                    response.getWriter().append(dadoAlunoEmAula.toString());
                    break;
                case "aluno-gestao-rede-em-aula":
                    matriculaAutorizado = Integer.valueOf(request.getParameter("matricula"));
                    chaveOrigem = request.getParameter("chaveOrigem");
                    dia = request.getParameter("dia") == null ? null : Long.valueOf(request.getParameter("dia"));
                    Integer horarioTurma = Integer.valueOf(request.getParameter("idHorarioTurma"));
                    redeEmpresaVO = RedeEmpresaService.obterRedePorChave(chave);
                    if (redeEmpresaVO != null && redeEmpresaVO.getGestaoRedes()) {
                        if (!UteisValidacao.emptyString(chaveOrigem)) {
                            AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO = AcessoSistemaMSService.findByMatriculaAndChave(String.valueOf(matriculaAutorizado), chaveOrigem, redeEmpresaVO);
                            if (UteisValidacao.emptyNumber(autorizacaoAcessoGrupoEmpresarialVO.getCodigo())) {
                                throw new Exception("Cadastro do autorizado não encontrado ");
                            }
                            JSONObject dadosAlunoEmAula = new JSONObject();
                            dadosAlunoEmAula.put("content", service.checarAlunoGestaoRedeEmAula(matriculaAutorizado, autorizacaoAcessoGrupoEmpresarialVO.getCodAcesso(), horarioTurma, new Date(dia)));
                            response.getWriter().append(dadosAlunoEmAula.toString());
                        } else {
                            throw new Exception("Cadastro do autorizado não encontrado ");
                        }
                    } else {
                        JSONObject dadosAlunoEmAula = new JSONObject();
                        dadosAlunoEmAula.put("content", service.checarAlunoEmAula(String.valueOf(matriculaAutorizado), horarioTurma, new Date(dia)));
                        response.getWriter().append(dadosAlunoEmAula.toString());
                    }
                    break;
                case "dados-booked":
                    dia = request.getParameter("dia") == null ? null : Long.valueOf(request.getParameter("dia"));
                    Integer idHorarioTurmaBooked = Integer.valueOf(request.getParameter("idHorarioTurmaBooked"));
                    JSONObject dadosBooked = service.dadosBooked(idHorarioTurmaBooked, new Date(dia));
                    response.getWriter().append(dadosBooked.toString());
                    break;
                case "bi-aulas":
                    JSONObject biDados = service.totalizarOcupacoes(inicio, fim, empresa, professor);
                    response.getWriter().append(biDados.toString());
                    break;
                case "reset":
                    Integer codAula = Integer.valueOf(request.getParameter("codAula"));
                    service.reset(codAula);
                    JSONObject objss = new JSONObject().put("content", "sucesso");
                    response.getWriter().append(objss.toString());
                    break;
                case "listagem-aulas":
                    Integer size = Integer.valueOf(request.getParameter("size"));
                    Integer page = Integer.valueOf(request.getParameter("page"));
                    JSONArray aulas = service.aulasBi(inicio, fim, empresa, professor, codigoRelatorio, quicksearchValue);
                    JSONObject obj = new JSONObject().put("content", aulas);
                    response.getWriter().append(obj.toString());
                    break;
                case "listagem-aulas-bonus":
                    JSONArray aulasBonus = service.aulasBonus(inicio, fim, empresa, professor, codigoRelatorio, quicksearchValue);
                    response.getWriter().append(new JSONObject().put("content", aulasBonus).toString());
                    break;
                case "listagem-modalidades":
                    JSONArray modalidades = service.obterModalidades(inicio, fim, empresa, professor, codigoRelatorio, quicksearchValue);
                    JSONObject objModalidades = new JSONObject().put("content", modalidades);
                    response.getWriter().append(objModalidades.toString());
                    break;
                case "listagem-professores":
                    JSONArray professores = service.obterProfessoresBiDashAgenda(inicio, fim, empresa, professor, codigoRelatorio, quicksearchValue, mapaAulasExcluidas, mapaProfessoresSubstituidos);
                    JSONObject objProfessores = new JSONObject().put("content", professores);
                    response.getWriter().append(objProfessores.toString());
                    break;
                case "listagem-frequencia-alunos":
                    JSONArray frequenciaAlunos = service.obterFrequenciaAlunosBiDashAgenda(inicio, fim, empresa, professor, codigoRelatorio, quicksearchValue);
                    JSONObject objFrequenciaAlunos = new JSONObject().put("content", frequenciaAlunos);
                    response.getWriter().append(objFrequenciaAlunos.toString());
                    break;
                case "listagem-presenca":
                    JSONArray lista = service.processarFrequenciaAlunos(inicio, fim, empresa, professor, codigoRelatorio, quicksearchValue, false);
                    response.getWriter().append(new JSONObject().put("content", lista).toString());
                    break;
                case "desmarcacoes-aluno":
                    Integer aluno = Integer.valueOf(request.getParameter("cliente"));
                    JSONArray desmarcacoes = service.desmarcacoes(inicio, fim, aluno);
                    response.getWriter().append(new JSONObject().put("content", desmarcacoes).toString());
                    break;
                case "alunos-confirmados":
                    boolean buscarProfessorAcompanhou = false;
                    if (request.getParameter("buscarProfessorAcompanhou") != null) {
                        buscarProfessorAcompanhou = Boolean.parseBoolean(request.getParameter("buscarProfessorAcompanhou"));
                    }
                    Date inicioDt = Uteis.getDate(request.getParameter("inicioc"), "yyyy-MM-dd");
                    Date fimDt = Uteis.getDate(request.getParameter("fimc"), "yyyy-MM-dd");
                    JSONArray listaConfirmados = service.confirmadosPeriodo(inicioDt, fimDt, empresa, buscarProfessorAcompanhou);
                    response.getWriter().append(new JSONObject().put("content", listaConfirmados).toString());
                    break;
                case "modalidades-titular":
                    response.getWriter().append(new JSONObject().put("content", service.modalidadesTitular(request.getParameter("matricula"))).toString());
                    break;
                case "agendamentos-dia":
                    String diaAgenda = request.getParameter("dia");
                    String plano = request.getParameter("plano");
                    String produto = request.getParameter("produto");
                    agendamentosDia(chave,
                            diaAgenda,
                            empresa,
                            plano == null ? null : Integer.valueOf(plano),
                            produto == null ? null : Integer.valueOf(produto),
                            response, false, false);
                    break;
                case "agendamentos-dia-aulas-coletivas":
                    String diaAgendaAulaColetiva = request.getParameter("dia");
                    String planoAulaColetiva = request.getParameter("plano");
                    String produtoAulaColetiva = request.getParameter("produto");
                    Boolean linkVisitante = Boolean.valueOf(request.getParameter("linkVisitante"));
                    agendamentosDia(chave,
                            diaAgendaAulaColetiva,
                            empresa,
                            planoAulaColetiva == null ? null : Integer.valueOf(planoAulaColetiva),
                            produtoAulaColetiva == null ? null : Integer.valueOf(produtoAulaColetiva),
                            response, linkVisitante, true);
                    break;
                case "log-aula":
                    ListaPaginadaTO paginacaoLog = new ListaPaginadaTO(request.getParameter("size") == null ? 10000 : Integer.valueOf(request.getParameter("size")));
                    String sortLog = request.getParameter("sort");
                    if(!UteisValidacao.emptyString(sortLog)){
                        String[] split = sortLog.split(",");
                        paginacaoLog.setOrderBy(split[0]);
                        paginacaoLog.setOrderByDesc(split[1].equals("DESC"));
                    }
                    paginacaoLog.setOffset((request.getParameter("page") == null ? 0 : Integer.valueOf(request.getParameter("page"))) * paginacaoLog.getLimit());
                    JSONArray logAula = service.logAula(empresa, paginacaoLog, request.getParameter("filtros") == null ? null : new JSONObject(request.getParameter("filtros")));
                    response.getWriter().append(new JSONObject().put("content", logAula).toString());
                    break;
                case "log-agenda-aulas":
                    ListaPaginadaTO paginacaoLogAulas = new ListaPaginadaTO(request.getParameter("size") == null ? 10000 : Integer.valueOf(request.getParameter("size")));
                    String sortLogAulas = request.getParameter("sort");
                    if(!UteisValidacao.emptyString(sortLogAulas)){
                        String[] split = sortLogAulas.split(",");
                        paginacaoLogAulas.setOrderBy(split[0]);
                        paginacaoLogAulas.setOrderByDesc(split[1].equals("DESC"));
                    }
                    paginacaoLogAulas.setOffset((request.getParameter("page") == null ? 0 : Integer.valueOf(request.getParameter("page"))) * paginacaoLogAulas.getLimit());
                    JSONArray logAulaColetivas = service.logAgendaAulas(empresa, paginacaoLogAulas, request.getParameter("filtros") == null ? null : new JSONObject(request.getParameter("filtros")));
                    JSONObject retornoLogAulas = new JSONObject();
                    retornoLogAulas.put("content", logAulaColetivas);
                    retornoLogAulas.put("total", paginacaoLogAulas.getCount());
                    response.getWriter().append(retornoLogAulas.toString());
                    break;
                case "verificar-aulas-crossfit-aluno":
                    Boolean result = service.verificarSeAulasAlunoECrossfit(request.getParameter("filtros") == null ? null : new JSONObject(request.getParameter("filtros")));
                    JSONObject retorno = new JSONObject().put("content", result);
                    response.getWriter().append(retorno.toString());
                    break;
                case "linha-tempo-agendado-booking":
                    Integer codCliente = Integer.valueOf(request.getParameter("cliente"));
                    JSONArray agendamentos = service.consultarAgendamentosBooking(inicio, fim, empresa, codCliente);
                    response.getWriter().append(new JSONObject().put("content", agendamentos).toString());
                    break;
                case "autorizados":
                    String paramAutorizado = request.getParameter("paramAutorizado");
                    autorizados(chave,
                            paramAutorizado,
                            empresa,
                            response);
                    break;
                case "consultar-autorizado-criando":
                    String chaveAluno = request.getParameter("chaveAluno");
                    matriculaAutorizado = Integer.valueOf(request.getParameter("matricula"));
                    consultarCriando(chave,
                            chaveAluno,
                            matriculaAutorizado,
                            response);
                    break;
                case "inserir-fila-espera":
                    FilaDeEsperaTO paramFilaDeEsperaInserir = new FilaDeEsperaTO();
                    paramFilaDeEsperaInserir.setCodigoHorarioTurma(Integer.valueOf(request.getParameter("codigoHorarioTurma")));
                    paramFilaDeEsperaInserir.setCodigoAluno(Integer.valueOf(request.getParameter("codigoAluno")));
                    paramFilaDeEsperaInserir.setMatricula(request.getParameter("matricula"));
                    paramFilaDeEsperaInserir.setDia(request.getParameter("dia"));
                    paramFilaDeEsperaInserir.setVinculoComAula("espera");
                    paramFilaDeEsperaInserir.setOrigemSistema(origemSistema);
                    paramFilaDeEsperaInserir.setCodigoUsuario(codigoUsuario);
                    String retornoInserirFila = inserirNaFilaDeEspera(chave, paramFilaDeEsperaInserir);
                    response.getWriter().append(new JSONObject().put("content", retornoInserirFila).toString());
                    break;
                case "remover-fila-espera":
                    FilaDeEsperaTO paramFilaDeEsperaRemover = new FilaDeEsperaTO();
                    paramFilaDeEsperaRemover.setCodigoHorarioTurma(Integer.valueOf(request.getParameter("codigoHorarioTurma")));
                    paramFilaDeEsperaRemover.setCodigoAluno(Integer.valueOf(request.getParameter("codigoAluno")));
                    paramFilaDeEsperaRemover.setMatricula(request.getParameter("matricula"));
                    paramFilaDeEsperaRemover.setDia(request.getParameter("dia"));
                    paramFilaDeEsperaRemover.setCodigoUsuario(codigoUsuario);
                    paramFilaDeEsperaRemover.setOrigemSistema(origemSistema);
                    String retornoRemoverFila = removerDaFilaDeEspera(chave, paramFilaDeEsperaRemover);
                    response.getWriter().append(new JSONObject().put("content", retornoRemoverFila).toString());
                    break;
                case "consultar-fila-espera":
                    FilaDeEsperaTO paramFilaDeEsperaConsultar = new FilaDeEsperaTO();
                    paramFilaDeEsperaConsultar.setCodigoHorarioTurma(Integer.valueOf(request.getParameter("codigoHorarioTurma")));
                    paramFilaDeEsperaConsultar.setMatricula(request.getParameter("matricula"));
                    paramFilaDeEsperaConsultar.setDia(request.getParameter("dia"));
                    JSONArray retornoConsultarFila = consultarFila(chave, paramFilaDeEsperaConsultar);
                    response.getWriter().append(new JSONObject().put("content", retornoConsultarFila).toString());
                    break;
                case "validar-modalidade":
                    String pessoa = request.getParameter("pessoa");
                    String modalidade = request.getParameter("modalidade");
                    String tipo = request.getParameter("tipo");
                    boolean modalidadeOutraUnidade = service.validarModalidadeOutraUnidade(Integer.valueOf(pessoa),
                            modalidade,
                            tipo);
                    response.getWriter().append(new JSONObject().put("content", modalidadeOutraUnidade).toString());
                    break;
                case "consultar-autorizado-gestao-rede":
                    String codAcessoAutorizadoGestaoRede = request.getParameter("codAcessoAutorizado");
                    String matriculaAutorizadoGestaoRede = request.getParameter("matriculaAutorizado");
                    chaveOrigem = request.getParameter("chaveOrigem");
                    redeEmpresaVO = RedeEmpresaService.obterRedePorChave(chave);
                    if (redeEmpresaVO != null && redeEmpresaVO.getGestaoRedes()) {
                        if (!UteisValidacao.emptyString(codAcessoAutorizadoGestaoRede)) {
                            AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO = AcessoSistemaMSService.findByMatriculaAndCodAcesso(matriculaAutorizadoGestaoRede, codAcessoAutorizadoGestaoRede, redeEmpresaVO);
                            if (UteisValidacao.emptyNumber(autorizacaoAcessoGrupoEmpresarialVO.getCodigo())) {
                                throw new Exception("Cadastro do autorizado não encontrado ");
                            }
                            response.getWriter().append(new JSONObject().put("content", autorizacaoAcessoGrupoEmpresarialVO.toJSON()).toString());
                        } else if (!UteisValidacao.emptyString(chaveOrigem)) {
                            AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO = AcessoSistemaMSService.findByMatriculaAndChave(matriculaAutorizadoGestaoRede, chaveOrigem, redeEmpresaVO);
                            if (UteisValidacao.emptyNumber(autorizacaoAcessoGrupoEmpresarialVO.getCodigo())) {
                                throw new Exception("Cadastro do autorizado não encontrado ");
                            }
                            response.getWriter().append(new JSONObject().put("content", autorizacaoAcessoGrupoEmpresarialVO.toJSON()).toString());
                        } else {
                            throw new Exception("Cadastro do autorizado não encontrado ");
                        }
                    } else {
                        throw new Exception("Cadastro do autorizado não encontrado ");
                    }
                    break;
                case "gravar-log-outra-unidade":
                    String pessoaLog = request.getParameter("pessoa");
                    String hora = request.getParameter("hora");
                    String fimAula = request.getParameter("fimAula");
                    String aulaNome = request.getParameter("aula");
                    String unidade = request.getParameter("unidade");
                    Boolean desmarcacao = Boolean.valueOf(request.getParameter("desmarcacao"));
                    service.logAlunoAulaOutraUnidade(desmarcacao, Integer.valueOf(pessoaLog), unidade, aulaNome, hora, fimAula);
                    response.getWriter().append(new JSONObject().put("content", "sucesso").toString());
                    break;
                case "log-outra-unidade":
                    String pessoaLogCons = request.getParameter("cliente");
                    JSONArray logs = service.logAlunoAulaOutraUnidade(inicio, fim, Integer.valueOf(pessoaLogCons));
                    response.getWriter().append(new JSONObject().put("content", logs).toString());
                    break;
                case "ambientes-agendados":
                    JSONArray ambientesAgendados = service.ambientesAgendados(inicio, fim, empresa);
                    response.getWriter().append(new JSONObject().put("content", ambientesAgendados).toString());
                    break;
                case "marcar-desmarcar-aulas-app":
                    String retornoMarcarDesmarcar = marcarDesmarcarAulasApp(chave, new JSONObject(request.getParameter("dados")));
                    response.getWriter().append(new JSONObject().put("content", retornoMarcarDesmarcar).toString());
                    break;
                case "processo-desmarcar-aulas-parcelas-vencidas":
                    String processoDesmarcarAulasParcelasVencidas = processoDesmarcarAulasParcelasVencidas(chave);
                    response.getWriter().append(new JSONObject().put("processado", processoDesmarcarAulasParcelasVencidas).toString());
                    break;
                case "consultar-saldo-aluno-app":
                    String consultarSaldoAluno = consultarSaldoAlunoApp(chave, new JSONObject(request.getParameter("dados")));
                    response.getWriter().append(new JSONObject().put("content", consultarSaldoAluno).toString());
                    break;
                case "consultar-saldo-aluno-repor-e-marcar-app":
                    String consultarSaldoAlunoReporEMacar = consultarSaldoAlunoReporEMarcarApp(chave, new JSONObject(request.getParameter("dados")));
                    response.getWriter().append(new JSONObject().put("content", consultarSaldoAlunoReporEMacar).toString());
                    break;
                case "modalidades-contrato":
                    JSONArray modalidadesContrato = consultarModalidadesContrato(chave, new JSONObject(request.getParameter("dados")));
                    response.getWriter().append(new JSONObject().put("content", modalidadesContrato).toString());
                    break;
                case "remover-autorizado-aula":
                    JSONObject dadosRemoverAutorizado = new JSONObject(request.getParameter("dados"));

                    redeEmpresaVO = RedeEmpresaService.obterRedePorChave(chave);
                    if (redeEmpresaVO != null && redeEmpresaVO.getGestaoRedes()) {
                        AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO = AcessoSistemaMSService.findByMatriculaAndChave(String.valueOf(dadosRemoverAutorizado.get("matricula")), dadosRemoverAutorizado.getString("chaveAluno"), redeEmpresaVO);
                        if (UteisValidacao.emptyNumber(autorizacaoAcessoGrupoEmpresarialVO.getCodigo())) {
                            throw new Exception("Cadastro do autorizado não encontrado ");
                        }
                        AutorizacaoAcessoGrupoEmpresarialInterfaceFacade daoAutorizado = new AutorizacaoAcessoGrupoEmpresarial(new DAO().obterConexaoEspecifica(chave));
                        daoAutorizado.excluirAulaAutorizadoGestaoRede(autorizacaoAcessoGrupoEmpresarialVO.getCodigoMatricula(),
                                autorizacaoAcessoGrupoEmpresarialVO.getCodAcesso(),
                                new Date(dadosRemoverAutorizado.getLong("dia")),
                                dadosRemoverAutorizado.getInt("codigoHorarioTurma"));
                        response.getWriter().append(new JSONObject().put("content", "sucesso").toString());
                    } else {
                        AutorizacaoAcessoGrupoEmpresarialInterfaceFacade daoAutorizado = new AutorizacaoAcessoGrupoEmpresarial(new DAO().obterConexaoEspecifica(chave));
                        daoAutorizado.excluirAulaAutorizado(dadosRemoverAutorizado.getInt("matricula"),
                                dadosRemoverAutorizado.getString("chaveAluno"),
                                new Date(dadosRemoverAutorizado.getLong("dia")),
                                dadosRemoverAutorizado.getInt("codigoHorarioTurma"));
                        response.getWriter().append(new JSONObject().put("content", "sucesso").toString());
                    }
                    break;
                case "autorizado-aulas":
                    JSONObject dadosAulaAutorizado = new JSONObject(request.getParameter("dados"));
                    AutorizacaoAcessoGrupoEmpresarialInterfaceFacade daoAutorizadoAula = new AutorizacaoAcessoGrupoEmpresarial(new DAO().obterConexaoEspecifica(chave));
                    JSONArray aulasAutorizado = daoAutorizadoAula.aulasAutorizado(dadosAulaAutorizado.getInt("matricula"),
                            dadosAulaAutorizado.getString("chaveAluno"),
                            new Date(dadosAulaAutorizado.getLong("dia")));
                    response.getWriter().append(new JSONObject().put("content", aulasAutorizado).toString());
                    break;
                case "listar-ids-horario-turma":
                    String listaIdsHorarioTurma = service.listarIdsHorarioTurma();
                    response.getWriter().append(new JSONObject().put("content", listaIdsHorarioTurma).toString());
                    break;
                case "is-aluno-contrato-concomitante":
                    // Validar se aluno possui contrato concomitante, e se possuir obter dados do contrato correto pela modalidade
                    JSONObject dadosVerificarContratoAluno = new JSONObject(request.getParameter("dados"));
                    JSONObject dadosContratoAluno = service.isAlunoContratoConcomitante(
                            empresa,
                            dadosVerificarContratoAluno.getInt("matricula"),
                            dadosVerificarContratoAluno.getInt("modalidade"),
                            dadosVerificarContratoAluno.getInt("horarioTurma"),
                            new Date(dadosVerificarContratoAluno.getLong("dia"))
                    );
                    response.getWriter().append(new JSONObject().put("content", dadosContratoAluno).toString());
                    break;
                case "validar-creditos":
                    Boolean debitar = request.getParameter("d") != null && Boolean.valueOf(request.getParameter("d"));
                    JSONArray jsonArray = service.validarCreditos(debitar);
                    response.getWriter().append(new JSONObject().put("content", jsonArray).toString());
                    break;
                case "marcar-cancelar-comparecimento-meta-presenciais-crm":
                    Integer fecharMetaDetalhado = Integer.valueOf(request.getParameter("fecharMetaDetalhado"));
                    Integer codUsuario = (request.getParameter("codigoUsuario") != null ? Integer.valueOf(request.getParameter("codigoUsuario")) : 0);
                    Boolean marcar = Boolean.valueOf(request.getParameter("marcar"));
                    String ret = service.marcarCancelarComparecimentoMetaAgendamentoPresencialCRM(fecharMetaDetalhado, codUsuario, marcar);
                    response.getWriter().append(new JSONObject().put("content", ret).toString());
                    break;

            }
        }catch (Exception e ){
            Uteis.logarDebug(String.format("Erro na api rest %s. Message: %s",
                    recurso, e.getMessage()));
            processarErro(e, request, response, null);
        }

    }

    private TurmasServiceInterface getService(String chave) throws Exception {
        return new TurmasServiceImpl(new DAO().obterConexaoEspecifica(chave));
    }

    private void agendamentosDia(String key,
                                 String dia,
                                 Integer empresa,
                                 Integer plano,
                                 Integer produto,
                                 HttpServletResponse response, Boolean linkVisitante, boolean aulasColetivas)  throws Exception {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            Date inicio = Calendario.getDataComHoraZerada(Uteis.getDate(dia));
            Date fim = Calendario.getDataComHora(Uteis.getDate(dia), "23:59:59");
            final List<AgendaTotalJSON> agendamentos = turmasService.consultarParaAgenda(
                    inicio,
                    fim,
                    null,
                    montarListaModalidades(key, plano, produto),
                    empresa,
                    aulasColetivas);
            verificarBloqueados(key,
                    inicio.getTime(),
                    fim.getTime(),
                    empresa,
                    agendamentos);
            final List<AgendaTotalJSON> agendamentosPermitidos = verificarPermitidosLinkVisitanteVendasOnline(empresa, c, agendamentos, linkVisitante);
            response.getWriter().append(new JSONObject().put("content", new JSONArray(agendamentosPermitidos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentosPermitidos)).toString());
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            throw e;
        }
    }

    private List<AgendaTotalJSON> verificarPermitidosLinkVisitanteVendasOnline(Integer empresa, Connection c, List<AgendaTotalJSON> agendamentos, Boolean linkVisitante) throws Exception {
        if (linkVisitante != null && linkVisitante) {
            VendasConfig vendasConfigDAO = new VendasConfig(c);
            try {
                VendasConfigVO vendasConfigs = vendasConfigDAO.config(empresa);
                if (vendasConfigs.isHabilitarAgendamentoAulaExperimentalLinkVisitante() &&
                        !AgendaVendasOnlineVO.TODAS_AULAS.equals(vendasConfigDAO.obterTipoAulasLinkVisitante(vendasConfigs.getCodigo()))) {
                    List<AgendaTotalJSON> agendamentosPermitidos = new ArrayList<>();
                    List<AulasVendasOnline> aulasLink = vendasConfigDAO.consultarAulasVendasOnlineLinkVisitantePorEmpresa(empresa, vendasConfigs.getCodigo());
                    for (AulasVendasOnline aulaPermitida : aulasLink) {
                        for (AgendaTotalJSON agendamento : agendamentos) {
                            if (agendamento.getCodigoTurma().equals(aulaPermitida.getCodigoTurma())) {
                                agendamentosPermitidos.add(agendamento);
                            }
                        }
                    }
                    return agendamentosPermitidos;
                }
                return agendamentos;
            } catch (Exception ex) {
                ex.printStackTrace();
                return agendamentos;
            } finally {
                vendasConfigDAO = null;
            }
        }
        return agendamentos;
    }

    private void verificarBloqueados(String ctx,
                                     Long inicio,
                                     Long fim,
                                     Integer empresaId,
                                     List<AgendaTotalJSON> agendamentos){
        try {
            String urlTreino = PropsService.getPropertyValue(ctx, PropsService.urlTreino);
            Map<String, String> headers = new HashMap<String, String>();
            String dados = ExecuteRequestHttpService.executeHttpRequest(urlTreino + "/prest/config/" + ctx + "/getconfig/bloqueio-mesmo-ambiente",
                    null, headers,
                    "POST",
                    "UTF-8", false);
            Boolean block = new JSONObject(dados).getBoolean("return");

            if(!block){
                return;
            }

            JSONArray content = getService(ctx).ambientesAgendados(inicio, fim, empresaId);
            if(content == null || content.length() == 0){
                return;
            }
            Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendados = new HashMap(){{
                for(int i = 0; i < content.length(); i++){
                    AmbienteAgendadoTO ambienteAgendadoTO = new AmbienteAgendadoTO(content.getJSONObject(i));
                    List<AmbienteAgendadoTO> agendados = (ArrayList) get(ambienteAgendadoTO.getAmbiente());
                    if(agendados == null){
                        agendados = new ArrayList<>();
                        put(ambienteAgendadoTO.getAmbiente(), agendados);
                    }
                    agendados.add(ambienteAgendadoTO);
                }
            }};
            for (AgendaTotalJSON agenda : new ArrayList<>(agendamentos)) {
                List<AmbienteAgendadoTO> ambienteAgendadoTOS = ambientesAgendados.get(agenda.getCodigoLocal());
                if (UteisValidacao.emptyList(ambienteAgendadoTOS)) {
                    continue;
                }
                Date horarioInicial = new SimpleDateFormat("dd/MM/yyyy HH:mm").parse(agenda.getInicio());
                Date horarioFim = new SimpleDateFormat("dd/MM/yyyy HH:mm").parse(agenda.getFim());
                for(AmbienteAgendadoTO aa : ambienteAgendadoTOS){
                    if(!Integer.valueOf(agenda.getId()).equals(aa.getHorarioTurma())
                            && ((horarioInicial.getTime() == aa.getInicio().getTime() || horarioFim.getTime() == aa.getFim().getTime())
                            || Calendario.entre(horarioInicial, aa.getInicio(), aa.getFim())
                            || Calendario.entre(horarioFim, aa.getInicio(), aa.getFim())
                            || Calendario.entre( aa.getInicio(), horarioInicial, horarioFim))){
                        agendamentos.remove(agenda);
                    }
                }
            }
        }catch (Exception e){
            Uteis.logar(e, AulaCheiaServlet.class);
        }
    }

    private void autorizados(String key,
                             String parametro,
                             Integer empresa,
                             HttpServletResponse response)  throws Exception {
        try (Connection c = new DAO().obterConexaoEspecifica(key)){
            AutorizacaoAcessoGrupoEmpresarialInterfaceFacade daoAutorizado = new AutorizacaoAcessoGrupoEmpresarial(c);
            JSONArray jsonArray = daoAutorizado.consultarParaAula(empresa, parametro);

            try {
                RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(key);
                if (redeEmpresa != null && redeEmpresa.getGestaoRedes()) {
                    List<AutorizacaoAcessoGrupoEmpresarialVO> autorizacoes = AcessoSistemaMSService.findAllByNameOrCPF(parametro, redeEmpresa);
                    for (AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO : autorizacoes) {
                        JSONObject json = new JSONObject();
                        json.put("id", autorizacaoAcessoGrupoEmpresarialVO.getCodigo());
                        json.put("nome", autorizacaoAcessoGrupoEmpresarialVO.getNomePessoa());
                        json.put("codAcesso", autorizacaoAcessoGrupoEmpresarialVO.getCodAcesso());
                        jsonArray.put(json);
                    }
                }
            } catch (Exception e) {
                Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### ERRO AO LOCALIZAR AUTORIZAÇÃO - GESTÃO REDES");
                Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### CLASSE: AulaCheiaServlet.java");
                Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### MÉTODO: autorizados");
                Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### EXCEPTION: " + e.getMessage());
                e.printStackTrace();
            }

            response.getWriter().append(new JSONObject().put("content", jsonArray).toString());

        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            throw e;
        }
    }

    private void consultarCriando(String key,
                                 String chaveAluno,
                                 Integer matricula,
                                 HttpServletResponse response)  throws Exception {
        try (Connection c = new DAO().obterConexaoEspecifica(key)){
            AutorizacaoAcessoGrupoEmpresarialInterfaceFacade daoAutorizado = new AutorizacaoAcessoGrupoEmpresarial(c);
            response.getWriter().append(new JSONObject().put("content", daoAutorizado.consultarCriando(matricula, chaveAluno)).toString());
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            throw e;
        }
    }

    private List<Integer> montarListaModalidades(String chave, Integer plano, Integer produto) throws Exception{
        if(plano == null){
            return new ArrayList<>();
        }
        Connection c = new DAO().obterConexaoEspecifica(chave);
        if(UteisValidacao.emptyNumber(produto)){
            PlanoModalidade planoModalidadeDao = new PlanoModalidade(c);
            return planoModalidadeDao.modalidadesPlano(plano);
        } else {
            return new ArrayList(){{
                Produto produtoDao = new Produto(c);
                Integer modalidadeProduto = produtoDao.modalidadeProduto(produto);
                if(!UteisValidacao.emptyNumber(modalidadeProduto)){
                    add(modalidadeProduto);
                }
            }};
        }

    }

    private String marcarDesmarcarAulasApp (String chave, JSONObject dados) throws Exception {
        Integer matriculaAluno = dados.getInt("matriculaAluno");
        Integer horarioturma = dados.getInt("horarioturma");
        String data = dados.getString("data");
        String marcar = dados.getString("marcar");
        String bloquearParcelaVencida = dados.getString("bloquearParcelaVencida");
        String proibirMarcarAulaAntesPagamentoPrimeiraParcela = dados.getString("proibirMarcarAulaAntesPagamentoPrimeiraParcela");
        Integer contrato = null;
        try {
            contrato = dados.getInt("contrato");
        } catch (Exception e) {}

        try (Connection c = new DAO().obterConexaoEspecifica(chave); TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.marcarDesmarcarAlunoApp(chave, horarioturma,
                    Uteis.getDate(data, "dd/MM/yyyy"), matriculaAluno,
                    Boolean.valueOf(marcar), Boolean.valueOf(bloquearParcelaVencida),
                    Boolean.valueOf(proibirMarcarAulaAntesPagamentoPrimeiraParcela), contrato);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    private String processoDesmarcarAulasParcelasVencidas (String chave) {
        try (Connection c = new DAO().obterConexaoEspecifica(chave)) {
            ReposicaoInterfaceFacade repoDao = new Reposicao(c);
            Boolean processoSucesso = repoDao.removerMarcacoesFuturasParcelasVencidas(chave);
            return processoSucesso.toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    private String consultarSaldoAlunoReporEMarcarApp(String chave, JSONObject dados) throws Exception {
        Integer matricula = dados.getInt("matriculaAluno");
        Integer contrato = dados.getInt("contrato");

        try (Connection c = new DAO().obterConexaoEspecifica(chave); TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {

            String saldoAlunoRepor = turmasService.consultarSaldoAlunoRepor(matricula, false, contrato != null ? contrato : 0);
            String saldoAlunoMarcar = turmasService.consultarSaldoAlunoMarcar(matricula, false, contrato != null ? contrato : 0);
            return saldoAlunoRepor + ";" + saldoAlunoMarcar;
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    private String consultarSaldoAlunoApp (String chave, JSONObject dados) throws Exception {
        Integer matricula = dados.getInt("matriculaAluno");
        Integer contrato = null;
        try {
            contrato = dados.getInt("contrato");
        } catch (Exception e) { }

        try (Connection c = new DAO().obterConexaoEspecifica(chave); TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.consultarSaldoAluno(matricula, false, contrato);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    private JSONArray consultarModalidadesContrato (String chave, JSONObject obj) throws Exception {
        try (Connection c = new DAO().obterConexaoEspecifica(chave); TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.modalidadesContrato(obj.getInt("contrato"), obj.getInt("matricula"));
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return new JSONArray();
        }
    }

    private JSONObject getJSONfromRequest(HttpServletRequest request) throws Exception{
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = request.getReader();
        try {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line).append('\n');
            }
        } finally {
            reader.close();
        }
        return new JSONObject(sb.toString());
    }

    public String inserirNaFilaDeEspera(String key, FilaDeEsperaTO filaDeEspera) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            FilaDeEsperaTO paramFilaDeEspera = new FilaDeEsperaTO();
            paramFilaDeEspera.setMatricula(filaDeEspera.getMatricula());
            paramFilaDeEspera.setCodigoAluno(filaDeEspera.getCodigoAluno());
            paramFilaDeEspera.setCodigoHorarioTurma(filaDeEspera.getCodigoHorarioTurma());
            paramFilaDeEspera.setDataEntrada(Calendario.hoje());
            paramFilaDeEspera.setDia(filaDeEspera.getDia());
            paramFilaDeEspera.setVinculoComAula("espera");
            paramFilaDeEspera.setCodigoUsuario(filaDeEspera.getCodigoUsuario());
            if(filaDeEspera.getOrigemSistema() != null && OrigemSistemaEnum.getOrigemSistema(filaDeEspera.getOrigemSistema()).getDescricao().equals("Agenda Web")){
                paramFilaDeEspera.setOrigemSistema(filaDeEspera.getOrigemSistema());
            }else{
                paramFilaDeEspera.setOrigemSistema(4);
            }
            return turmasService.inserirNaFilaDeEspera(paramFilaDeEspera, true);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    public String removerDaFilaDeEspera(String key, FilaDeEsperaTO obj) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            FilaDeEsperaTO paramFilaDeEspera = new FilaDeEsperaTO();
            paramFilaDeEspera.setMatricula(obj.getMatricula());
            paramFilaDeEspera.setCodigoAluno(obj.getCodigoAluno());
            paramFilaDeEspera.setCodigoHorarioTurma(obj.getCodigoHorarioTurma());
            paramFilaDeEspera.setDia(obj.getDia());
            paramFilaDeEspera.setCodigoUsuario(obj.getCodigoUsuario());
            paramFilaDeEspera.setOrigemSistema(obj.getOrigemSistema());
            turmasService.removerDaFilaDeEspera(paramFilaDeEspera);

            return "Ok";
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    public JSONArray consultarFila(String key,
                                   FilaDeEsperaTO filaDeEsper) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            FilaDeEsperaTO paramFilaDeEspera = new FilaDeEsperaTO();
            paramFilaDeEspera.setMatricula(filaDeEsper.getMatricula());
            paramFilaDeEspera.setCodigoAluno(filaDeEsper.getCodigoAluno());
            paramFilaDeEspera.setCodigoHorarioTurma(filaDeEsper.getCodigoHorarioTurma());
            paramFilaDeEspera.setDia(filaDeEsper.getDia());
            List<FilaDeEsperaTO> filaDeEsperaTOES = turmasService.consultarFilaJson(paramFilaDeEspera, false, false);
            return new JSONArray(filaDeEsperaTOES);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return new JSONArray("ERRO: " + e.getMessage());
        }
    }

}
