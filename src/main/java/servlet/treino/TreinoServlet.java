package servlet.treino;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import importador.colaborador.ImportarColaborador;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.treino.TreinoService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.Date;

public class TreinoServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET,POST");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        Integer empresa = Integer.valueOf(request.getParameter("empresa"));

        try (TreinoService treinoService = getService(chave)) {
            switch (recurso){
                case "ativos":
                    response.getWriter().append(treinoService.ativosEmpresa(empresa).toString());
                    break;
                case "matriculas-ativos":
                    response.getWriter().append(treinoService.matriculasAtivosEmpresa(empresa).toString());
                    break;
                case "matriculas-externas":
                    response.getWriter().append(treinoService.matriculasExternas().toString());
                    break;
                case "matriculas-externas-prospects":
                    response.getWriter().append(treinoService.matriculasExternasProspects(empresa).toString());
                    break;
                case "matriculas-nao-importadas":
                    response.getWriter().append(treinoService.matriculasNaoImportadas(empresa).toString());
                    break;
                case "matriculas-ativos-professor":
                    Integer professor = Integer.valueOf(request.getParameter("professor"));
                    response.getWriter().append(treinoService.matriculasAtivosEmpresaProfessor(empresa, professor, true).toString());
                    break;
                case "matriculas-professor":
                    Integer professorTodos = Integer.valueOf(request.getParameter("professor"));
                    response.getWriter().append(treinoService.matriculasAtivosEmpresaProfessor(empresa, professorTodos, false).toString());
                    break;
                case "ativos-professor":
                    response.getWriter().append(treinoService.ativosEmpresaProfessores(empresa, true).toString());
                    break;
                case "autorizados":
                    response.getWriter().append(treinoService.consultarAutorizados(request.getParameter("param")).toString());
                    break;
                case "nr-professor":
                    response.getWriter().append(treinoService.ativosEmpresaProfessores(empresa, false).toString());
                    break;
                case "ativos-profesor-data":
                    Long data = Long.valueOf(request.getParameter("data"));
                    Integer professorTreino = Integer.valueOf(request.getParameter("professor"));
                    response.getWriter().append(treinoService.ativosProfessorEmData(professorTreino, empresa, Calendario.getDataComUltimaHora(new Date(data))).toString());
                    break;
                case "prescricao-colaboradores":
                    String search = request.getParameter("search");
                    String tiposPrescricao = request.getParameter("tipos");
                    Integer page = Integer.valueOf(request.getParameter("page"));
                    Integer size = Integer.valueOf(request.getParameter("size"));
                    String limitAvencer = request.getParameter("limitAvencer");
                    response.getWriter().append(treinoService.colaboradoresPrescricao(empresa, search, tiposPrescricao, page, size, Uteis.getDate(limitAvencer)).toString());
                    break;
                case "prescricao-clientes":
                    response.getWriter().append(treinoService.clientesPrescricao(request.getParameter("codigosClientes")).toString());
                    break;
                case "prescricao-colaborador-data":
                    String fimPrograma = request.getParameter("fimPrograma");
                    Integer colaboradorTreino = Integer.valueOf(request.getParameter("colaborador"));
                    treinoService.colaboradorProgramaData(colaboradorTreino, Uteis.getDate(fimPrograma));
                    break;
                case "codigos-professores":
                    String situacao = request.getParameter("situacaoProfessor");
                    String tipos = request.getParameter("tipos");
                    String cargas = request.getParameter("carga");
                    String modalidades = request.getParameter("modalidades");
                    response.getWriter().append(treinoService.codigosColaboradores(empresa, situacao, tipos, cargas, modalidades).toString());
                    break;
                case "aluno-parcela-vencida":
                    Integer pessoa = Integer.valueOf(request.getParameter("pessoa"));
                    response.getWriter().append(treinoService.existeParcelaVencida(empresa, pessoa).toString());
                    break;
                case "aluno-contrato-assinatura-digital":
                    String matricula = request.getParameter("matricula");
                    boolean validarContratoAssinado = false;
                    try {
                        validarContratoAssinado = Boolean.parseBoolean(request.getParameter("validar"));
                    } catch (Exception ignore) {}
                    response.getWriter().append(treinoService.consultarContratosAssinaturaDigital(request, chave, matricula, empresa, validarContratoAssinado).toString());
                    break;
                case "aluno-contrato-assinatura-digital-by-contrato": {
                    Integer contrato = Integer.parseInt(request.getParameter("contrato"));
                    response.getWriter().append(treinoService.consultarContratosAssinaturaDigitalByContrato(request, chave, contrato, empresa).toString());
                }
                break;
                case "aluno-contrato-assinatura-digital-incluir":
                    final String assinatura = request.getParameter("assinatura");
                    final Integer contrato = Integer.parseInt(request.getParameter("contrato"));
                    final Integer aditivo = obterParametroTratado(request.getParameter("aditivo"));
                    String documentos = request.getParameter("documentos");
                    String ip = request.getParameter("ip");
                    String tipoAutenticacao = request.getParameter("tipoAutenticacao");
                    String dadosAutenticacao = request.getParameter("dadosAutenticacao");
                    response.getWriter().append(treinoService.incluirAssinaturaContrato(chave, contrato, assinatura, aditivo, documentos, ip, tipoAutenticacao, dadosAutenticacao));
                    break;
                case "alunos-aviso-medico":
                    boolean contar = Boolean.parseBoolean(request.getParameter("contar"));
                    String colaborador = (request.getParameter("colaborador"));
                    response.getWriter().append(treinoService.alunosAvisoMedico(empresa, contar, colaborador).toString());
                    break;
                case "cliente-mensagem":
                    Integer codCliente = Integer.valueOf(request.getParameter("alunoID"));
                    String tipoMensagem = request.getParameter("tipoMensagem");
                    response.getWriter().append(treinoService.clienteMensagem(codCliente, tipoMensagem));
                    break;
                case "usuario-importador-treino":
                    try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                        Conexao.guardarConexaoForJ2SE(chave, con);
                        ImportarColaborador importarColaborador = new ImportarColaborador(con);
                        UsuarioVO usuarioVO = importarColaborador.consultarOuCriarUsuarioImportadorTreino(chave, empresa);
                        JSONObject retorno = new JSONObject();
                        retorno.put("usuariozw", usuarioVO.getCodigo());
                        response.getWriter().append(retorno.toString());
                    }
                    break;
            }
        }catch (Exception e ){
            System.out.println("Erro na api rest "+recurso+". Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }
    }

    private TreinoService getService(String chave) throws Exception {
        return new TreinoService(new DAO().obterConexaoEspecifica(chave));
    }

}
