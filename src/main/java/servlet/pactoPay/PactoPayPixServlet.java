package servlet.pactoPay;

import br.com.pactosolucoes.integracao.pactopay.front.FiltroPactoPayDTO;
import br.com.pactosolucoes.integracao.pactopay.front.pix.CobrancaPixDetalheDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Pix;
import org.json.JSONObject;
import servicos.pix.PixPagamentoService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 24/07/2022
 */
public class PactoPayPixServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.addHeader("Access-Control-Allow-Headers", "Authorization,empresaId");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("consultar")) {
                envelopeRespostaDTO = consultar(request);
            } else if (operacao.equalsIgnoreCase("totalizador")) {
                envelopeRespostaDTO = totalizador(request);
            } else if (operacao.equalsIgnoreCase("totalizador_tipo")) {
                envelopeRespostaDTO = totalizadorPorTipo(request);
            } else if (operacao.equalsIgnoreCase("totalizador_parcela")) {
                envelopeRespostaDTO = totalizadorPorParcela(request);
            } else if (operacao.equalsIgnoreCase("totalizador_parcela_lista")) {
                envelopeRespostaDTO = totalizadorPorParcelaLista(request);
            } else if (operacao.equalsIgnoreCase("forcar_pix_pago")) {
                envelopeRespostaDTO = forcarPixPago(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private EnvelopeRespostaDTO consultar(ServletRequest request) throws Exception {
        Connection con = null;
        ConvenioCobranca convenioCobrancaDAO;
        Pix pixDAO;
        try {
            con = obterConexao(request);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            pixDAO = new Pix(con);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);

            Map<Integer, ConvenioCobrancaVO> mapaConvenio = new HashMap<>();

            List<CobrancaPixDetalheDTO> listaDTO = new ArrayList<>();
            List<PixVO> lista = pixDAO.consultarPactoPay(filtroDTO, paginadorDTO);
            for (PixVO obj : lista) {
                if (!UteisValidacao.emptyNumber(obj.getConveniocobranca().getCodigo())) {
                    ConvenioCobrancaVO convenioCobrancaVO = mapaConvenio.get(obj.getConveniocobranca().getCodigo());
                    if (convenioCobrancaVO == null) {
                        convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(obj.getConveniocobranca().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        mapaConvenio.put(convenioCobrancaVO.getCodigo(), convenioCobrancaVO);
                    }
                    obj.setConveniocobranca(convenioCobrancaVO);;
                }

                listaDTO.add(new CobrancaPixDetalheDTO(obj));
            }
            return EnvelopeRespostaDTO.of(listaDTO, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixDAO = null;
            convenioCobrancaDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO totalizador(ServletRequest request) throws Exception {
        Connection con = null;
        Pix pixDAO;
        try {
            con = obterConexao(request);
            pixDAO = new Pix(con);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            return EnvelopeRespostaDTO.of(pixDAO.consultarPactoPayTotalizador(filtroDTO, paginadorDTO), paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO totalizadorPorTipo(ServletRequest request) throws Exception {
        Connection con = null;
        Pix pixDAO;
        try {
            con = obterConexao(request);
            pixDAO = new Pix(con);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            return EnvelopeRespostaDTO.of(pixDAO.consultarPactoPayTotalizadorPorTipo(filtroDTO, paginadorDTO), paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO totalizadorPorParcela(ServletRequest request) throws Exception {
        Connection con = null;
        Pix pixDAO;
        try {
            con = obterConexao(request);
            pixDAO = new Pix(con);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            return EnvelopeRespostaDTO.of(pixDAO.consultarPactoPayTotalizadorPorParcela(filtroDTO, paginadorDTO), paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO totalizadorPorParcelaLista(ServletRequest request) throws Exception {
        Connection con = null;
        Pix pixDAO;
        try {
            con = obterConexao(request);
            pixDAO = new Pix(con);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            return EnvelopeRespostaDTO.of(pixDAO.consultarPactoPayTotalizadorPorParcelaLista(filtroDTO, request.getParameter("situacao"), paginadorDTO), paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixDAO = null;
            finalizarConexao(con);
        }
    }

    public EnvelopeRespostaDTO forcarPixPago(ServletRequest request) throws Exception {
        Connection con = null;
        Pix pixDAO;
        PixPagamentoService pixPagamentoService;
        try {
            con = obterConexao(request);
            pixDAO = new Pix(con);
            pixPagamentoService = new PixPagamentoService(con);


            Integer codigoPix = UteisValidacao.converterInteiro(request.getParameter("pix"));
            if (UteisValidacao.emptyNumber(codigoPix)) {
                throw new Exception("Codigo pix não informado");
            }
            PixVO pixVO = pixDAO.consultarPorCodigo(codigoPix, true);
            pixVO.setAmbiente(AmbienteEnum.HOMOLOGACAO.name());
            pixPagamentoService.processarPixControlandoTransacao(pixVO);
            pixVO = pixDAO.consultarPorCodigo(codigoPix, true);
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixDAO = null;
            finalizarConexao(con);
        }
    }
}
