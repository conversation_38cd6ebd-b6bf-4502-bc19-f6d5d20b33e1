package br.com.pactosolucoes.notificacao;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import com.sun.xml.fastinfoset.stax.events.Util;
import controle.arquitetura.threads.ThreadPushNotificacaoApp;
import negocio.comuns.NotificacaoAppGestor;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoCrmIAVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.crm.ConfiguracaoCrmIA;
import negocio.facade.jdbc.plano.Plano;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.util.ExecuteRequestHttpService;
import servicos.util.RequestException;
import servicos.vendasonline.dto.RetornoVendaTO;
import servicos.vendasonline.dto.VendaProdutoDTO;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.text.NumberFormat;
import java.util.*;

public class ServicoNotificacaoPush {

    private static String URL_NOTIFICACAO_APP = "https://appgestor-3f25c.web.app/comunicacao/mandarNotificacao";
    private static String TITULO_VENDA_CONTRATO = "Venda de contrato!";
    private static String CONTENT_VENDA_CONTRATO = "Uma nova venda de contrato foi feita. Acompanhe os seus resultados.";
    private static String TITULO_CANCELAMENTO_CONTRATO = "Cancelamento de contrato";
    private static String CONTENT_CANCELAMENTO_CONTRATO = "Entre em contato com seu aluno para entender os motivos e reverter essa situação.";
    public static String TITULO_TOKEN_GERADO = "Novo código disponível!";
    public static String CONTENT_TOKEN_GERADO = "Toque aqui para ver o código de autenticação gerado";
    public static String URL_NOTIFICACAO_APP_GESTOR = "https://us-central1-appgestor-3f25c.cloudfunctions.net/comunicacao/mandarNotificacaoParaUsuario";


    public static void enviaNotificacaoPush(NotificacaoAppGestor notificacaoAppGestor) throws IOException, RequestException {
        System.out.println(new JSONObject(notificacaoAppGestor).toString());
        List<NotificacaoAppGestor> notificacaoAppGestorList = new ArrayList<>();
        notificacaoAppGestorList.add(notificacaoAppGestor);
        String result = ExecuteRequestHttpService.patch(URL_NOTIFICACAO_APP, new JSONArray(notificacaoAppGestorList).toString(), new HashMap<>(), "UTF-8");
        System.out.println("Resultado envio de notificação: " + result);
    }

    private static void enviaNotificacaoPushUsuarioAppGestor(NotificacaoAppGestor notificacaoAppGestor) {
        try {
            String result = ExecuteRequestHttpService.patch(URL_NOTIFICACAO_APP_GESTOR, new JSONObject(notificacaoAppGestor).toString(), new HashMap<>(), "UTF-8");
            System.out.println("Resultado envio de notificação: " + result);
        } catch (Exception e) {
            System.out.println("Erro ao enviar notificação: " + e.getMessage());
        }
    }

    public static void enviaNotificacaoContrato(String key, Integer codEmpresa, Boolean geral, String origemSistema, Double valorContrato, String descricaoPlano, Integer duracaoMeses, String nomeEmpresa, Integer codigoCliente, Connection con, String nomeConsultor) {
        enviaNotificacaoVendaContratoAppGestor(key, codEmpresa, geral, origemSistema, valorContrato, descricaoPlano, duracaoMeses, nomeEmpresa, nomeConsultor);

        enviaNotificacaoVendaContratoAppDoAluno(key, codigoCliente, con);
    }

    public static String obterLinkApp(String chave, String email) {
        HttpURLConnection httpUrlConnection = null;
        try {

            JSONObject body = new JSONObject();
            body.put("chave", chave);
            body.put("userName", email);
            body.put("codigoValidacao", "");

            String url = "https://app-do-aluno-unificado.web.app/usuario/solicitarLinkDeLoginV2";
            httpUrlConnection = (HttpURLConnection) new URL(url).openConnection();
            httpUrlConnection.setRequestMethod("PATCH");
            httpUrlConnection.setDoOutput(true);
            httpUrlConnection.setRequestProperty("Content-Type", "application/json");
            httpUrlConnection.setRequestProperty("Accept", "application/json");

            try (OutputStreamWriter wr = new OutputStreamWriter(httpUrlConnection.getOutputStream(), StandardCharsets.UTF_8)) {
                wr.write(body.toString());
                wr.flush();
            }

            int responseCode = httpUrlConnection.getResponseCode();
            if (responseCode >= 200 && responseCode < 300) {
                try (InputStreamReader inputStreamReader = new InputStreamReader(httpUrlConnection.getInputStream(), StandardCharsets.UTF_8);
                     BufferedReader reader = new BufferedReader(inputStreamReader)) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    JSONObject jsonResponse = new JSONObject(response.toString());
                    return jsonResponse.optString("sucesso", "");
                }
            } else {
                System.err.println("Request failed with status: " + responseCode);
                return "";
            }
        } catch (Exception e) {
            System.err.println("Failed to obtain linkApp: " + e.getMessage());
            return "";
        } finally {
            if (httpUrlConnection != null) {
                httpUrlConnection.disconnect();
            }
        }
    }

    public static void enviarMensagemConversasAi(String chave, Integer codigoCliente, Integer codigoEmpresa, Connection con) {
        try {
            ConfiguracaoCrmIA configuracaoCrmIA = new ConfiguracaoCrmIA(con);
            ConfiguracaoCrmIAVO configuracaoCrmIAVO = configuracaoCrmIA.consultarPorEmpresa(codigoEmpresa);

            if (configuracaoCrmIAVO != null && configuracaoCrmIAVO.getHabilitarConfigIA()) {
                Pessoa pessoaDAO = new Pessoa(con);
                PessoaVO pessoaVO = pessoaDAO.consultarPessoaPorCliente(codigoCliente);

                if (pessoaVO != null && pessoaVO.getCfp() != null) {
                    String cpf = pessoaVO.getCfp();
                    ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(chave);
                    String CRM_URL = clientDiscoveryDataDTO.getServiceUrls().getContatoMsUrl();
                    String token = AutenticacaoMsService.personaToken(chave);
                    String linkAppTreino = obterLinkApp(chave, pessoaVO.getEmail());

                    String url = String.format("%s/v1/ia/conversa/pos-venda?cpf=%s&chave=%s&linkAppTreino=%s",
                            CRM_URL, cpf, chave, linkAppTreino);

                    HttpURLConnection httpUrlConnection = (HttpURLConnection) new URL(url).openConnection();
                    httpUrlConnection.setRequestMethod("POST");
                    httpUrlConnection.setDoOutput(true);
                    httpUrlConnection.setRequestProperty("Content-Type", "application/json");
                    httpUrlConnection.setRequestProperty("Accept", "application/json");
                    httpUrlConnection.setRequestProperty("Authorization", token);

                    int responseCode = httpUrlConnection.getResponseCode();
                    System.out.println("Response code: " + responseCode);
                    if (responseCode == 200 || responseCode == 201) {
                        System.err.println("Mensagem Conversas.ai enviada para o cliente de codigo " + codigoCliente);
                    }

                    try (InputStreamReader inputStreamReader = new InputStreamReader(
                            responseCode >= 400 ? httpUrlConnection.getErrorStream() : httpUrlConnection.getInputStream(),
                            StandardCharsets.UTF_8);
                         BufferedReader reader = new BufferedReader(inputStreamReader)) {
                        String responseLine = reader.readLine();
                        System.out.println(responseLine != null ? responseLine : "No response body");
                    }

                    httpUrlConnection.disconnect();
                } else {
                    System.err.println("No person found or CPF is null for cliente: " + codigoCliente);
                }
            } else {
                System.err.println("AI configuration not enabled or not found for empresa: " + codigoEmpresa);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to send message to Conversas AI: " + e.getMessage(), e);
        }
    }

    private static String getStringValueOrEmpty(Object value) {
        return value != null ? value.toString() : "";
    }

    private static String getDoubleValueOrEmpty(double value) {
        return value > 0.0 ? String.valueOf(value) : "";
    }


    public static void enviarNotificacaoBiConversas(String chave, Integer codigoCliente, Integer codigoEmpresa, Connection con, RetornoVendaTO retornoVendaTO) {
        try {
            ConfiguracaoCrmIA configuracaoCrmIA = new ConfiguracaoCrmIA(con);
            ConfiguracaoCrmIAVO configuracaoCrmIAVO = configuracaoCrmIA.consultarPorEmpresa(codigoEmpresa);

            if (configuracaoCrmIAVO != null && configuracaoCrmIAVO.getHabilitarConfigIA()
                    && verificarSeEUsuarioConversas(configuracaoCrmIAVO, retornoVendaTO)) {

                ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(chave);
                String CRM_URL = clientDiscoveryDataDTO.getServiceUrls().getContatoMsUrl();
                String token = AutenticacaoMsService.personaToken(chave);
                String url = String.format("%s/v1/ia/bi/indicadores/venda/plano",
                        CRM_URL);

                HttpURLConnection httpUrlConnection = (HttpURLConnection) new URL(url).openConnection();
                httpUrlConnection.setRequestMethod("POST");
                httpUrlConnection.setDoOutput(true);
                httpUrlConnection.setRequestProperty("Content-Type", "application/json");
                httpUrlConnection.setRequestProperty("Accept", "application/json");
                httpUrlConnection.setRequestProperty("Authorization", token);

                if( retornoVendaTO.getVendaDTO() == null || retornoVendaTO.getVendaDTO().getPlano() == null) {
                    throw new RuntimeException("VendaDTO ou Plano não encontrado no RetornoVendaTO");
                }

                JSONObject json = new JSONObject();
                String nome = retornoVendaTO.getClienteVO().getNome_Apresentar();
                String identificador = "plano_vendido";

                json.put("nome", nome);
                json.put("empresa", codigoEmpresa);
                json.put("chave", chave);
                json.put("identificador", identificador);
                json.put("indicador", 1);
                json.put("telefone", retornoVendaTO.getClienteVO().getPessoa().getTelefones());


                JSONObject meta = new JSONObject();
                meta.put("descricaoPlano", Optional.ofNullable(retornoVendaTO.getContratoVO())
                        .map(ContratoVO::getPlano)
                        .map(PlanoVO::getDescricao)
                        .map(ServicoNotificacaoPush::getStringValueOrEmpty)
                        .orElse(""));
                meta.put("codigoPlano", Optional.ofNullable(retornoVendaTO.getContratoVO())
                        .map(ContratoVO::getPlano)
                        .map(PlanoVO::getCodigo)
                        .map(ServicoNotificacaoPush::getStringValueOrEmpty)
                        .orElse(""));
                meta.put("codigoContrato", Optional.ofNullable(retornoVendaTO.getContratoVO())
                        .map(ContratoVO::getCodigo)
                        .map(ServicoNotificacaoPush::getStringValueOrEmpty)
                        .orElse(""));
                meta.put("codigoRecibo", Optional.ofNullable(retornoVendaTO.getTransacaoVO())
                        .map(TransacaoVO::getReciboPagamento)
                        .map(ServicoNotificacaoPush::getStringValueOrEmpty)
                        .orElse(""));
                meta.put("retornoPagamento", getStringValueOrEmpty(retornoVendaTO.getMsgRetorno()));
                meta.put("valorFinalContrato", Optional.ofNullable(retornoVendaTO.getContratoVO())
                        .map(ContratoVO::getValorFinal)
                        .map(ServicoNotificacaoPush::getDoubleValueOrEmpty)
                        .orElse(""));
                meta.put("valorPrimeiraParcelaContrato", Optional.ofNullable(retornoVendaTO.getContratoVO())
                        .map(ContratoVO::getValorPrimeiraParcela)
                        .map(ServicoNotificacaoPush::getDoubleValueOrEmpty)
                        .orElse(""));
                meta.put("matricula", Optional.ofNullable(retornoVendaTO.getClienteVO())
                        .map(ClienteVO::getMatricula)
                        .map(ServicoNotificacaoPush::getStringValueOrEmpty)
                        .orElse(""));
                meta.put("cpf", Optional.ofNullable(retornoVendaTO.getClienteVO())
                        .map(ClienteVO::getPessoa)
                        .map(PessoaVO::getCfp)
                        .map(ServicoNotificacaoPush::getStringValueOrEmpty)
                        .orElse(""));
                json.put("meta", meta);
                try (OutputStreamWriter wr = new OutputStreamWriter(httpUrlConnection.getOutputStream(), StandardCharsets.UTF_8)) {
                    wr.write(json.toString());
                    wr.flush();
                }

                System.out.println("Response code: " + httpUrlConnection.getResponseCode());

                int responseCode = httpUrlConnection.getResponseCode();
                System.out.println("Response code: " + responseCode);
                if (responseCode == 200 || responseCode == 201) {
                    System.err.println("Mensagem Conversas.ai enviada para o cliente de codigo " + codigoCliente);
                }
                try (InputStreamReader inputStreamReader = new InputStreamReader(
                        responseCode >= 400 ? httpUrlConnection.getErrorStream() : httpUrlConnection.getInputStream(),
                        StandardCharsets.UTF_8);
                     BufferedReader reader = new BufferedReader(inputStreamReader)) {
                    String responseLine = reader.readLine();
                    System.out.println(responseLine != null ? responseLine : "No response body");
                }
                httpUrlConnection.disconnect();
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to send message to Conversas AI: " + e.getMessage(), e);
        }
    }

    private static boolean verificarSeEUsuarioConversas(ConfiguracaoCrmIAVO configuracaoCrmIAVO, RetornoVendaTO retornoVendaTO) {
        if(configuracaoCrmIAVO != null && configuracaoCrmIAVO.getPactoConversasLogin() != null && !configuracaoCrmIAVO.getPactoConversasLogin().trim().isEmpty()){
            Uteis.logar("Verificando usuário Conversas para envio de mensagem: " + configuracaoCrmIAVO.getPactoConversasLogin());

            if(retornoVendaTO != null && retornoVendaTO.getContratoVO() != null &&
                    retornoVendaTO.getContratoVO().getResponsavelContrato() != null &&
                    retornoVendaTO.getContratoVO().getResponsavelContrato().getUsername() != null &&
                    retornoVendaTO.getContratoVO().getResponsavelContrato().getUsername().toLowerCase().trim().equals(configuracaoCrmIAVO.getPactoConversasLogin().toLowerCase().trim())){
                Uteis.logar("É responsável pelo contrato ("+retornoVendaTO.getContratoVO().getResponsavelContrato().getUsername()+") é o conversas...");
                return true;
            }else{
                Uteis.logar("Não é responsável pelo contrato, ignorando envio de mensagem...");
            }
        }else{
            Uteis.logar("Usuário do conversas não configurado, ignorando envio de mensagem...");
        }

        return false;
    }

    private static void enviaNotificacaoVendaContratoAppDoAluno(String key, Integer codigoCliente, Connection con) {
        try {
            UsuarioMovel usuarioMovelDAO = new UsuarioMovel(con);
            String nomeUsuario = usuarioMovelDAO.consultarNomeUsuarioPorCodigoCliente(
                    codigoCliente,
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS
            );
            if (nomeUsuario != null && !UteisValidacao.emptyString(nomeUsuario)) {
                long dataNotificacaoMillis = Calendario.hoje().getTime() + (2 * 60 * 1000);
                Date dataNotificacao = new Date(dataNotificacaoMillis);
                String horario = Uteis.getData(dataNotificacao) + " " + Uteis.gethoraHHMMAjustado(dataNotificacao);

                ThreadPushNotificacaoApp.enfileirarNotificacao(
                        horario,
                        key,
                        "Novo contrato disponível",
                        "Acesse para ver detalhes do contrato.",
                        nomeUsuario,
                        "/geral/pushService");
            }
        } catch (Exception ex) {
            System.out.println("Ocorreu um problema ao enviar o push: " + ex.getMessage());
            Uteis.logar(ex.getMessage());
        }
    }

    private static void enviaNotificacaoVendaContratoAppGestor(String key, Integer codEmpresa, Boolean geral, String origemSistema, Double valorContrato, String descricaoPlano, Integer duracaoMeses, String nomeEmpresa, String nomeConsultor) {
        try {
            NotificacaoAppGestor notificacaoAppGestor = new NotificacaoAppGestor();
            notificacaoAppGestor.setChave(key);
            notificacaoAppGestor.setCodEmpresa(codEmpresa);
            notificacaoAppGestor.setGeral(geral);
            notificacaoAppGestor.setTitulo(TITULO_VENDA_CONTRATO);
            notificacaoAppGestor.setContent(CONTENT_VENDA_CONTRATO);
            if(!Util.isEmptyString(nomeConsultor)) {
                notificacaoAppGestor.setContent(notificacaoAppGestor.getContent() + " \nOrigem: " + nomeConsultor);
            } else if (!Util.isEmptyString(origemSistema)) {
                notificacaoAppGestor.setContent(notificacaoAppGestor.getContent() + " \nOrigem: " + (origemSistema.equals(OrigemSistemaEnum.ZW.getDescricao()) ? "Vendas online" : origemSistema));
            }
            notificacaoAppGestor.setHoraEnvioNotificacao(Uteis.getDataComHora(new Date(System.currentTimeMillis())));
            notificacaoAppGestor.setNomeEmpresa(nomeEmpresa);
            if(valorContrato != null && valorContrato > 0) {
                NumberFormat formatadorReal = NumberFormat.getCurrencyInstance(new Locale("pt", "BR"));
                String valorFormatado = formatadorReal.format(valorContrato);
                notificacaoAppGestor.setContent(notificacaoAppGestor.getContent() + "\nValor: " + valorFormatado);
            }
            if(!Util.isEmptyString(descricaoPlano)) {
                notificacaoAppGestor.setContent(notificacaoAppGestor.getContent() + "\nDescrição: " + descricaoPlano);
            }
            if(duracaoMeses != null && duracaoMeses > 0) {
                String duracao = duracaoMeses == 1 ? "1 mês" : duracaoMeses + " meses";
                notificacaoAppGestor.setContent(notificacaoAppGestor.getContent() + "\nDuração: " + duracao);
            }
            enviaNotificacaoPush(notificacaoAppGestor);
        } catch (Exception ignore) {
            System.out.println(ignore.getMessage());
        }
    }

    public static void enviaNotificacaoCancelamentoContrato(String key, Integer codEmpresa, Boolean geral, Integer codUsuario, String nomeEmpresa) {
        try {
            NotificacaoAppGestor notificacaoAppGestor = new NotificacaoAppGestor();
            notificacaoAppGestor.setChave(key);
            notificacaoAppGestor.setCodEmpresa(codEmpresa);
            notificacaoAppGestor.setGeral(geral);
            notificacaoAppGestor.setTitulo(TITULO_CANCELAMENTO_CONTRATO);
            notificacaoAppGestor.setContent(CONTENT_CANCELAMENTO_CONTRATO);
            notificacaoAppGestor.setHoraEnvioNotificacao(Uteis.getDataComHora(new Date(System.currentTimeMillis())));
            notificacaoAppGestor.setNomeEmpresa(nomeEmpresa);
            if(codUsuario != 0) {
                notificacaoAppGestor.setCodUsuarios(new ArrayList<>());
                notificacaoAppGestor.getCodUsuarios().add(codUsuario);
            }
            enviaNotificacaoPush(notificacaoAppGestor);
        } catch (Exception ignore) {
            System.out.println(ignore.getMessage());
        }
    }

    public static void enviaNotificacaoPushTokenPactoApp(String key, Integer codEmpresa, Integer codUsuario) {
        try {
            NotificacaoAppGestor notificacaoAppGestor = new NotificacaoAppGestor();
            notificacaoAppGestor.setChave(key);
            notificacaoAppGestor.setCodEmpresa(codEmpresa);
            notificacaoAppGestor.setGeral(false);
            notificacaoAppGestor.setTitulo(TITULO_TOKEN_GERADO);
            notificacaoAppGestor.setContent(CONTENT_TOKEN_GERADO);
            notificacaoAppGestor.setHoraEnvioNotificacao(Uteis.getDataComHora(new Date(System.currentTimeMillis())));
            notificacaoAppGestor.setNaoAgendada(true);
            if(!UteisValidacao.emptyNumber(codUsuario)) {
                notificacaoAppGestor.setCodUsuarios(new ArrayList<>());
                notificacaoAppGestor.getCodUsuarios().add(codUsuario);
            }
            enviaNotificacaoPush(notificacaoAppGestor);
        } catch (Exception ignore) {
            System.out.println(ignore.getMessage());
        }
    }

    public static void enviaNotificacaoDesativacaoUsuario(String key, Integer codEmpresa, Integer codUsuario, String nomeEmpresa) {
        try {
            NotificacaoAppGestor notificacaoAppGestor = new NotificacaoAppGestor();
            notificacaoAppGestor.setChave(key);
            notificacaoAppGestor.setCodEmpresa(codEmpresa);
            notificacaoAppGestor.setGeral(false);
            notificacaoAppGestor.setTitulo("Seu usuario foi desativado");
            notificacaoAppGestor.setContent("Seu usuario foi desativado");
            notificacaoAppGestor.setHoraEnvioNotificacao(Uteis.getDataComHora(new Date(System.currentTimeMillis())));
            notificacaoAppGestor.setNomeEmpresa(nomeEmpresa);
            notificacaoAppGestor.setCodUsuario(codUsuario);
            enviaNotificacaoPushUsuarioAppGestor(notificacaoAppGestor);
        } catch (Exception ignore) {
            System.out.println(ignore.getMessage());
        }
    }


    public static void enviaNotificacaoAtivacaoUsuario(String key, Integer codEmpresa, Integer codUsuario, String nome) {
        try {
            NotificacaoAppGestor notificacaoAppGestor = new NotificacaoAppGestor();
            notificacaoAppGestor.setChave(key);
            notificacaoAppGestor.setCodEmpresa(codEmpresa);
            notificacaoAppGestor.setGeral(false);
            notificacaoAppGestor.setTitulo("Seu usuario foi ativado");
            notificacaoAppGestor.setContent("Seu usuario foi ativado");
            notificacaoAppGestor.setHoraEnvioNotificacao(Uteis.getDataComHora(new Date(System.currentTimeMillis())));
            notificacaoAppGestor.setCodUsuario(codUsuario);
            notificacaoAppGestor.setNomeEmpresa(nome);
            enviaNotificacaoPushUsuarioAppGestor(notificacaoAppGestor);
        } catch (Exception ignore) {
            System.out.println(ignore.getMessage());
        }
    }

    public static void enviaNotificacaoVendaProdutoAppDoGestor(String key, VendaProdutoDTO vendaProdutoDTO, ClienteVO clienteVO) {
        try {
            NotificacaoAppGestor notificacaoAppGestor = new NotificacaoAppGestor();
            notificacaoAppGestor.setChave(key);
            notificacaoAppGestor.setCodEmpresa(clienteVO.getEmpresa().getCodigo());
            notificacaoAppGestor.setGeral(false);
            notificacaoAppGestor.setTitulo("Venda de produto!");
            String content = "Nova venda de produto no Pacto Flow!";
            content+= "\nCliente: " + clienteVO.getPessoa().getNome();
            content+= "\nMatrícula: " + clienteVO.getMatricula();
            content += "\nProduto: " + vendaProdutoDTO.getDescricao();
            content += "\nValor: R$ " + Uteis.formatarValorEmReal(vendaProdutoDTO.getValorUnitario() * vendaProdutoDTO.getQtd());

            notificacaoAppGestor.setContent(content);
            notificacaoAppGestor.setHoraEnvioNotificacao(Uteis.getDataComHora(new Date(System.currentTimeMillis())));
            notificacaoAppGestor.setCategoria("pactoFlow");
            enviaNotificacaoPush(notificacaoAppGestor);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    public static void enviaNotificacaoVendaDiariaAppDoGestor(String key, AulaAvulsaDiariaVO aulaAvulsaDiariaVO, ClienteVO clienteVO) {
        try {

            NotificacaoAppGestor notificacaoAppGestor = new NotificacaoAppGestor();
            notificacaoAppGestor.setChave(key);
            notificacaoAppGestor.setCodEmpresa(clienteVO.getEmpresa().getCodigo());
            notificacaoAppGestor.setGeral(false);

            notificacaoAppGestor.setTitulo("Venda de diária!");
            String content = "Nova venda de diária no Pacto Flow!";
            content += "\nCliente: " + clienteVO.getPessoa().getNome();
            content += "\nMatrícula: " + clienteVO.getMatricula();
            content += "\nValor: R$ " + Uteis.formatarValorEmReal(aulaAvulsaDiariaVO.getValor());
            content += "\nValidade: " + Uteis.getData(aulaAvulsaDiariaVO.getDataInicio());

            notificacaoAppGestor.setContent(content);
            notificacaoAppGestor.setHoraEnvioNotificacao(Uteis.getDataComHora(new Date(System.currentTimeMillis())));
            notificacaoAppGestor.setCategoria("pactoFlow");
            enviaNotificacaoPush(notificacaoAppGestor);
        } catch (IOException | RequestException e) {
            e.printStackTrace();
        }
    }
}
