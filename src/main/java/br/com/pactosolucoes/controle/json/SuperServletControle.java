/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.controle.json;

import acesso.webservice.DaoAuxiliar;
import com.fasterxml.jackson.databind.ObjectMapper;
import controle.arquitetura.exceptions.TokenExpiradoException;
import negocio.comuns.arquitetura.LogApiVO;
import negocio.comuns.utilitarias.TokenZwDTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.interfaces.arquitetura.LogApiInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import org.json.JSONObject;
import servicos.propriedades.PropsService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class SuperServletControle extends HttpServlet {
    public static final String STATUS = "status";
    public static final String STATUS_SUCESSO = "sucesso";
    public static final String STATUS_ERRO = "erro";
    public static final String RETURN = "return";
    public static final String MSG_DADOS_GRAVADOS = "Dados gravados com sucesso!";
    public static final String MSG_DADOS_EXCLUIDOS = "Dados excluídos com sucesso!";
    public static final String MSG_DADOS_NAOENCONTRADOS = "Dados não encontrados!";

    protected static boolean validateApiZWToken(HttpServletRequest request) throws Exception {
        if (PropsService.isTrue(PropsService.VALIDAR_TOKEN_API_ZW)) {
            String bearerApiToken = request.getHeader("Authorization");
            boolean existeHeaderToken = !UteisValidacao.emptyString(bearerApiToken);
            if (existeHeaderToken) {
                String apiToken = bearerApiToken.split("Bearer")[1].trim();
                try {
                    String jsonToken = Uteis.desencriptar(apiToken, Uteis.getAuthZwSecret());

                    ObjectMapper objectMapper = new ObjectMapper();
                    TokenZwDTO token = objectMapper.readValue(jsonToken, TokenZwDTO.class);
                    if (!token.isValid()) {
                        throw new TokenExpiradoException();
                    }

                    auditApiUse(token);
                } catch (TokenExpiradoException ex) {
                    throw new TokenExpiradoException(ex);
                }
            }
            return existeHeaderToken;
        }
        return false;
    }

    private static void auditApiUse(final TokenZwDTO token) throws Exception {
        LogApiInterfaceFacade logApiDao = DaoAuxiliar.retornarAcessoControle(token.getTokenKey()).getLogApiDao();

        LogApiVO logApi = new LogApiVO();
        logApi.setIp(token.getIp());
        logApi.setMethod(token.getMethod());
        logApi.setUri(token.getUri());
        logApi.setParams(token.getParams());
        logApi.setDescricaoToken(token.getTokenName());

        logApiDao.incluir(logApi);
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        PrintWriter out = response.getWriter();
        try {
            response.setContentType("application/json");
            response.setCharacterEncoding("ISO-8859-1");
            JSONObject retorno = new JSONObject();
            retorno.put(STATUS_ERRO, "Requisições GET não são autorizadas.");
            out.println(retorno);
        } catch (Exception e) {
            out.println(e.getMessage());
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("ISO-8859-1");
        response.setHeader("Access-Control-Allow-Origin", "*");
    }

    protected Integer obterParametro(HttpServletRequest request, String chave) {
        String parameter = request.getParameter(chave);
        if (parameter != null) {
            return Integer.parseInt(parameter);
        }
        return null;
    }

    protected Integer obterParametroIntegerTratado(HttpServletRequest request, String parametro) {
        try {
            String valor = request.getParameter(parametro);
            if (UteisValidacao.emptyString(valor)) {
                return null;
            }
            return Integer.parseInt(valor.trim());

        } catch (NumberFormatException ex) {
            return null;
        }
    }

    protected String obterParametroStringDecode(HttpServletRequest request, String parametro) {
        try {
            String query = request.getQueryString();
            String queryTratada = URLDecoder.decode(query.split(parametro + "=")[1], "ISO-8859-1");
            if (queryTratada.contains("&")) {
                queryTratada = URLDecoder.decode(queryTratada.split("&")[0], "ISO-8859-1");
            }
            return queryTratada;
        } catch (Exception e) {
            return "";
        }
    }

    protected String obterParametroString(HttpServletRequest request, String chave) {
        return request.getParameter(chave);
    }

    protected String obterParametroRaw(HttpServletRequest request) throws IOException {
        StringBuilder stringBuilder = new StringBuilder();

        try (BufferedReader bufferedReader = request.getReader()) {
            char[] charBuffer = new char[128];
            int bytesRead;
            while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                stringBuilder.append(charBuffer, 0, bytesRead);
            }
        }

        return stringBuilder.toString();
    }

    protected Boolean obterParametroBoolean(HttpServletRequest request, String nome) {
        String parameter = request.getParameter(nome);
        if (parameter != null) {
            return Boolean.valueOf(parameter);
        }
        return null;
    }

    protected Integer obterParametroInt(HttpServletRequest request, String nome) {
        return obterParametroInt(request, nome, null);
    }

    protected Integer obterParametroInt(HttpServletRequest request, String nome, Integer defaultValue) {
        String parameter = request.getParameter(nome);
        if (parameter != null) {
            return Integer.parseInt(parameter);
        }
        return defaultValue;
    }

    protected Date obterParametroDate(HttpServletRequest request, String chave, String formatoData) throws ParseException {
        String parameter = request.getParameter(chave);
        if (parameter != null && (!UteisValidacao.emptyString(parameter))) {
            return new SimpleDateFormat(formatoData).parse(parameter);
        }
        return null;
    }

    protected Date obterParametroDate(HttpServletRequest request, String chave) throws ParseException {
        return obterParametroDate(request, chave, "dd/MM/yyyy HH:mm:ss");
    }

    protected Date obterParametroSimpleDate(HttpServletRequest request, String chave, String formatoData) throws ParseException {
        String parameter = request.getParameter(chave);
        if (parameter != null) {
            String parameterX = parameter.replaceAll("%20", " ");
            if (!UteisValidacao.emptyString(parameterX)) {
                return new SimpleDateFormat(formatoData).parse(parameterX);
            }
        } else {
            return new Date();
        }
        return null;
    }

    protected Date obterParametroSimpleDate(HttpServletRequest request, String chave) throws ParseException {
        return obterParametroSimpleDate(request, chave, "dd/MM/yyyy HH:mm:ss");
    }

}
