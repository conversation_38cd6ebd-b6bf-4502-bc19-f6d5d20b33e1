package br.com.pactosolucoes.controle.json.nuvemshop;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.amazonaws.util.IOUtils;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.LogIntegracoesVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.estoque.CompraItensVO;
import negocio.comuns.estoque.CompraVO;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Fornecedor;
import negocio.facade.jdbc.basico.LogIntegracoes;
import negocio.facade.jdbc.estoque.Compra;
import negocio.facade.jdbc.estoque.ProdutoEstoque;
import negocio.facade.jdbc.plano.CategoriaProduto;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.nuvemshop.IntegracaoNuvemshopServiceImpl;
import servicos.integracao.nuvemshop.json.IntegracaoNuvemshopJSON;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

public class IntegracaoNuvemshopServletControle extends SuperServletControle {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();
        Connection con = null;

        try {
            String key = obterParametroString(request, "chave");
            String codigoEmpresa = obterParametroString(request, "codigoEmpresa");
            String operacao = obterParametroString(request, "operacao");

            OperacoesIntegracaoNuvemshopEnum operacaoEnum = OperacoesIntegracaoNuvemshopEnum.obterOperacao(operacao);

            if (operacaoEnum == null) {
                throw new IllegalArgumentException("Não foi possível identificar qual operação deve ser realizada.");
            }

            con = new DAO().obterConexaoEspecifica(key);
            Conexao.guardarConexaoForJ2SE(con);

            IntegracaoNuvemshopServiceImpl integracaoNuvemshopService = new IntegracaoNuvemshopServiceImpl(con);
            IntegracaoNuvemshopJSON dadosIntegracao = integracaoNuvemshopService.montarDadosIntegracao(Integer.parseInt(codigoEmpresa));

            if (dadosIntegracao == null || !dadosIntegracao.isHabilitada()) {
                throw new IllegalArgumentException("Integração Nuvem Shop não habilitada!");
            }

            realizarOperacaoNuvemshop(operacaoEnum, request, jsonRetorno, codigoEmpresa, dadosIntegracao, integracaoNuvemshopService, con);

        } catch (Exception e) {
            handleException(e, jsonRetorno);
        } finally {
            out.println(jsonRetorno.toString(4));
        }
    }

    private void realizarOperacaoNuvemshop(OperacoesIntegracaoNuvemshopEnum operacaoEnum, HttpServletRequest request, JSONObject jsonRetorno,
                                 String codigoEmpresa, IntegracaoNuvemshopJSON dadosIntegracao, IntegracaoNuvemshopServiceImpl integracaoNuvemshopService,
                                 Connection con) throws Exception {
        switch (operacaoEnum) {
            case SALVAR_DADOS_INTEGRACAO:
                salvarDadosIntegracao(request, jsonRetorno, codigoEmpresa, con);
                break;
            case BUSCAR_PEDIDOS:
                buscarPedidos(request, jsonRetorno, dadosIntegracao, integracaoNuvemshopService, con);
                break;
            case REGISTRAR_LOG:
                registrarLog(request, null, codigoEmpresa, null, false, con);
                break;
            default:
                jsonRetorno.put(STATUS_ERRO, "Operação não identificada");
                break;
        }
    }

    private void salvarDadosIntegracao(HttpServletRequest request, JSONObject jsonRetorno, String codigoEmpresa, Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        String idLoja = obterParametroString(request, "idLoja");
        String tokenAcesso = obterParametroString(request, "tokenAcesso");

        EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(Integer.valueOf(codigoEmpresa), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaVO.setIntegracaoNuvemshopStoreId(idLoja);
        empresaVO.setIntegracaoNuvemshopTokenAcesso(tokenAcesso);
        empresaDAO.alterar(empresaVO);

        jsonRetorno.put("empresaAtualizada", empresaVO);
    }

    private void buscarPedidos(HttpServletRequest request, JSONObject jsonRetorno, IntegracaoNuvemshopJSON dadosIntegracao,
                               IntegracaoNuvemshopServiceImpl integracaoNuvemshopService, Connection con) throws Exception {
        JSONObject jsonPayload = new JSONObject(obterBody(request));
        String orderId = String.valueOf(jsonPayload.optInt("id"));
        String codigoEmpresa = obterParametroString(request, "codigoEmpresa");
        JSONObject retornoConsultaPedidoNuvemshop = integracaoNuvemshopService.consultarPedidoNuvemshop(dadosIntegracao, orderId);

        int responseCode = retornoConsultaPedidoNuvemshop.optInt("code", 0);
        if (responseCode != 0) {
            String erro = "Não foi possível consultar o pedido na Nuvemshop! "
                    + retornoConsultaPedidoNuvemshop.optInt("code") + " - "
                    + retornoConsultaPedidoNuvemshop.optString("message")
                    + ": " + retornoConsultaPedidoNuvemshop.optString("description");
            jsonRetorno.put("erro", erro);
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, erro);
        } else {
            if(validarEmailEmpresaFranqueada(retornoConsultaPedidoNuvemshop.optString("contact_email"), codigoEmpresa, con)) {
                processarPedido(request, orderId, jsonRetorno, retornoConsultaPedidoNuvemshop, con, codigoEmpresa);
            } else {
                jsonRetorno.put("erro", "O e-mail da empresa franqueada e o e-mail do pedido não são os mesmos");
                Logger.getLogger(getClass().getName()).log(Level.SEVERE, "NuvemShop: O e-mail da empresa franqueada e o e-mail do pedido não são os mesmos\n" +
                        retornoConsultaPedidoNuvemshop.optInt("id") + " - " + retornoConsultaPedidoNuvemshop.optString("contact_email") +
                        " = R$ " + retornoConsultaPedidoNuvemshop.optString("total"));
            }

        }
    }
    private boolean validarEmailEmpresaFranqueada(String emailPedido, String codigoEmpresa, Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(Integer.valueOf(codigoEmpresa), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (!UteisValidacao.emptyString(emailPedido) && !UteisValidacao.emptyString(empresaVO.getIntegracaoNuvemshopEmail())) {
            if (emailPedido.equals(empresaVO.getIntegracaoNuvemshopEmail())) {
                return true;
            }
        }

        return false;
    }


    private void processarPedido(HttpServletRequest request, String idPedidoNuvemshop, JSONObject jsonRetorno, JSONObject retornoConsultaPedidoNuvemshop, Connection con, String codigoEmpresa) throws Exception {
        boolean statusPedidoFinalizado = "shipped".equals(retornoConsultaPedidoNuvemshop.optString("shipping_status", ""));
        boolean configsFinanceirasHabilitadas = getFacade().getConfiguracaoFinanceiro().consultar().isOrdemCompraEstoque();

        String mensagemErroProcessamentoPedido = null;

        try {
            if (statusPedidoFinalizado) {
                if (configsFinanceirasHabilitadas) {
                    JSONArray listaProdutos = retornoConsultaPedidoNuvemshop.optJSONArray("products");
                    if (listaProdutos != null) {
                        if (!compraJaFoiProcessada(idPedidoNuvemshop, con)) {
                            criarCompra(idPedidoNuvemshop, jsonRetorno, listaProdutos, con, codigoEmpresa);
                        } else {
                            mensagemErroProcessamentoPedido = "Esse pedido da nuvemshop com ID " + idPedidoNuvemshop + " já foi processado anteriormente.";
                        }

                    } else {
                        mensagemErroProcessamentoPedido = "Nenhum produto encontrado no pedido";
                    }
                } else {
                    mensagemErroProcessamentoPedido = "A configuração financeira 'Habilitar ordem de compra ao lançar compra estoque' precisa estar ativada";
                }
            } else {
                mensagemErroProcessamentoPedido = "Status da entrega do produto na Nuvemshop precisa ser 'shipped'";
            }
        } catch (Exception e) {
            mensagemErroProcessamentoPedido = "Erro ao processar o pedido: " + e.getMessage();
            registrarLog(request, mensagemErroProcessamentoPedido, codigoEmpresa, idPedidoNuvemshop, false, con);
            throw e;
        }

        if (mensagemErroProcessamentoPedido != null) {
            jsonRetorno.put("erro", mensagemErroProcessamentoPedido);
            registrarLog(request, mensagemErroProcessamentoPedido, codigoEmpresa, idPedidoNuvemshop, false, con);
        }
    }

    private boolean compraJaFoiProcessada(String idPedidoNuvemshop, Connection con) throws Exception {
        Compra compraDAO = new Compra(con);
        CompraVO compraEncontrada = compraDAO.consultarPorIdPedidoNuvemshop(idPedidoNuvemshop, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return compraEncontrada != null;
    };

    private void criarCompra(String idPedidoNuvemshop, JSONObject jsonRetorno, JSONArray listaProdutos, Connection con, String codigoEmpresa) throws Exception {
        try {
            Usuario usuarioDAO = new Usuario(con);
            Empresa empresaDAO = new Empresa(con);
            Fornecedor fornecedorDAO = new Fornecedor(con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_MINIMOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(Integer.valueOf(codigoEmpresa), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            CompraVO compraVO = new CompraVO();
            compraVO.setAutorizada(false);
            compraVO.setIdPedidoNuvemshop(idPedidoNuvemshop);
            compraVO.setUsuarioCadastro(usuarioVO);
            compraVO.setEmpresa(empresaVO);
            compraVO.setDataCadastro(Calendario.hoje());
            compraVO.setFornecedor(buscarFornecedorNuvemshop(fornecedorDAO));
            compraVO.setDescricaoFinanceiro("Nuvem Shop ID: " + idPedidoNuvemshop);

            montarCompraItensEAtualizarProdutoEstoque(usuarioVO, compraVO, listaProdutos, con);

            Double valorTotal = compraVO.getItens().stream()
                    .mapToDouble(item -> item.getQuantidade() * item.getValorUnitario())
                    .sum();

            compraVO.setValorTotal(valorTotal);
            compraVO.setPontos(0);

            getFacade().getCompra().incluir(compraVO);

            String msgSucesso = "Compra inserida com sucesso no Sistema Pacto.";
            jsonRetorno.put("return", msgSucesso);
            registrarLog(null, msgSucesso, codigoEmpresa, idPedidoNuvemshop, true, con);

        } catch (Exception e) {
            String errorMsg = "Erro ao criar compra. Detalhes: " + e.getMessage();
            registrarLog(null, errorMsg, codigoEmpresa, idPedidoNuvemshop, false, con);

            throw new Exception(errorMsg, e);

        }
    }

    private void registrarLog(HttpServletRequest request, String msg, String codigoEmpresa, String idPedidoNuvemshop, boolean sucesso, Connection con) throws Exception {
        LogIntegracoes logIntegracoesDAO = new LogIntegracoes(con);
        LogIntegracoesVO logIntegracoesVO = new LogIntegracoesVO();

        JSONObject jsonPayload = new JSONObject();
        jsonPayload.put("codigoEmpresa", codigoEmpresa);

        if(request != null) {
            jsonPayload.put("chave", obterParametroString(request, "chave"));
            jsonPayload.put("body", obterBody(request));
        }
        if(!UteisValidacao.emptyString(msg) && !UteisValidacao.emptyString(idPedidoNuvemshop)) {
            jsonPayload.put("orderIdNuvemshop", idPedidoNuvemshop);
            jsonPayload.put("msg", msg);
        }

        logIntegracoesVO.setDadosRecebidos(jsonPayload.toString());
        logIntegracoesVO.setDataLancamento(Calendario.hoje());
        logIntegracoesVO.setResultado(sucesso ? "SUCESSO" : "ERRO");
        logIntegracoesVO.setServico("INTEGRACAO_NUVEM_SHOP");

        logIntegracoesDAO.incluir(logIntegracoesVO);
    }

    private void handleException(Exception e, JSONObject jsonRetorno) {
        jsonRetorno.put(STATUS_ERRO, e.getMessage());

        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        e.printStackTrace(printWriter);

        jsonRetorno.put(STATUS_ERRO, stringWriter.toString());
    }

    private void montarCompraItensEAtualizarProdutoEstoque(UsuarioVO usuarioVO, CompraVO compraVO, JSONArray products, Connection con) throws Exception {
        Produto produtoDAO = new Produto(con);
        ProdutoEstoque produtoEstoqueDAO = new ProdutoEstoque(con);
        List<ProdutoEstoqueVO> listaProdutoEstoqueVO = new ArrayList<>();

        try {
            for (int i = 0; i < products.length(); i++) {
                JSONObject product = products.getJSONObject(i);
                CompraItensVO compraItem = new CompraItensVO();

                CategoriaProduto categoriaProdutoDao = new CategoriaProduto(con);
                CategoriaProdutoVO categoriaProduto = categoriaProdutoDao.criarCategoriaOuConsultarSeExistePorDescricao("LOJA");

                String sqlProdutoIdExterno = "select codigo from produto where id_externo = '" + product.optInt("id") + "'";
                boolean existeProdutoComIdExterno = SuperFacadeJDBC.existe(sqlProdutoIdExterno, con);

                ProdutoVO produtoVO = null;
                ProdutoEstoqueVO produtoEstoqueVO = null;

                try {
                    if (existeProdutoComIdExterno) {
                        ResultSet rsCodigoProduto = SuperFacadeJDBC.criarConsulta(sqlProdutoIdExterno, con);
                        rsCodigoProduto.next();
                        produtoVO = produtoDAO.consultarPorCodigo(rsCodigoProduto.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    } else {
                        produtoVO = new ProdutoVO();
                        produtoVO.setId_externo(product.optInt("id"));
                        produtoVO.setDescricao(formatarNomeProduto(product.optString("name")));
                        produtoVO.setValorFinal(product.optDouble("price"));
                        produtoVO.setCodigoBarras(product.optString("barcode"));
                        produtoVO.setObservacao("Produto importado da Nuvemshop");
                        produtoVO.setTipoProduto("PE");
                        produtoVO.setCategoriaProduto(categoriaProduto);

                        produtoDAO.incluir(produtoVO);
                    }

                    compraItem.setProduto(produtoVO);
                    compraItem.setQuantidade(0);
                    compraItem.setQuantidadeAutorizar(product.optInt("quantity"));  // Relacionado a ordem de compra que requer autorização
                    compraItem.setValorUnitario(product.optDouble("price"));
                    compraItem.setDesconto(product.optDouble("discount", 0.0));
                    compraItem.setPontos(0);
                    compraVO.getItens().add(compraItem);

                    if (!UteisValidacao.emptyNumber(produtoVO.getCodigo())) {
                        produtoEstoqueVO = produtoEstoqueDAO.consultarPorProduto(produtoVO.getCodigo(), compraVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }

                    if (produtoEstoqueVO != null) {
                        int novaQuantidade = produtoEstoqueVO.getEstoque() + compraItem.getQuantidade();
                        produtoEstoqueVO.setEstoque(novaQuantidade);
                        produtoEstoqueDAO.alterar(produtoEstoqueVO);
                    } else {
                        produtoEstoqueVO = new ProdutoEstoqueVO();
                        produtoEstoqueVO.setProduto(produtoVO);
                        produtoEstoqueVO.setEmpresa(compraVO.getEmpresa());
                        produtoEstoqueVO.setEstoque(compraItem.getQuantidade());
                        produtoEstoqueVO.setEstoqueMinimo(1);
                        produtoEstoqueVO.setSituacao("A");
                        produtoEstoqueVO.setPontos(compraVO.getPontos());

                        listaProdutoEstoqueVO.add(produtoEstoqueVO);
                    }

                } catch (SQLException e) {
                    String errorMsg = "Erro ao incluir/atualizar o produto " + product.optString("name") + " (ID Nuvemshop: " + product.optInt("id") + "). Detalhes: " + e.getMessage();
                    registrarLog(null, errorMsg, compraVO.getEmpresa().getCodigo().toString(), null, false, con);
                    throw new Exception(errorMsg, e);
                }
            }

            if (!listaProdutoEstoqueVO.isEmpty()) {
                produtoEstoqueDAO.incluir(listaProdutoEstoqueVO, usuarioVO);
            }

        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    private String formatarNomeProduto(String nomeProduto) {
        String max50caracteres = nomeProduto.length() > 50 ? nomeProduto.substring(0, 50) : nomeProduto;
        String removeAspas = max50caracteres.replace("\"", "").replace("'", "");
        return removeAspas.toUpperCase();
    }


    private FornecedorVO buscarFornecedorNuvemshop(Fornecedor dao) throws Exception {

        String NOME_FORNECEDOR = "CX STORE";

        List<FornecedorVO> fornecedoresEncontrados = dao.consultarPorDescricao(NOME_FORNECEDOR);

        if (UteisValidacao.emptyList(fornecedoresEncontrados)) {
            try {
                //novo fornecedor
                FornecedorVO novoFornecedorNuvemshop = new FornecedorVO();
                novoFornecedorNuvemshop.setDescricao(NOME_FORNECEDOR);

                //nova pessoa usada no fornecedor
                PessoaVO pessoaVO = new PessoaVO();
                pessoaVO.setNome(novoFornecedorNuvemshop.getDescricao());
                getFacade().getPessoa().incluirSemComit(pessoaVO);

                novoFornecedorNuvemshop.setPessoa(pessoaVO);
                getFacade().getFornecedor().incluir(novoFornecedorNuvemshop);

                // RECONSULTA APOS INCLUSAO PRA GARANTIR QUE HÁ UM FORNECEDOR
                fornecedoresEncontrados = dao.consultarPorDescricao(NOME_FORNECEDOR);
            } catch (Exception e) {
                e.printStackTrace();
                throw new Exception("Erro ao incluir novo fornecedor " + NOME_FORNECEDOR + " (Integração Nuvemshop)", e);
            }
        }

        if (UteisValidacao.emptyList(fornecedoresEncontrados)) {
            throw new Exception("Fornecedor " + NOME_FORNECEDOR + " não encontrado após tentativa de inclusão (Integração Nuvemshop)");
        }

        return fornecedoresEncontrados.get(0);
    }

    private String obterBody(HttpServletRequest request) {
        try {
            return IOUtils.toString(request.getInputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }


}
