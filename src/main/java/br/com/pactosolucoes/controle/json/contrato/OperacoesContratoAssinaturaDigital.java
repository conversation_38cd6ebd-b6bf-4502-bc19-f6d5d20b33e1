/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.controle.json.contrato;

/**
 *
 * <AUTHOR>
 */
public enum OperacoesContratoAssinaturaDigital {
    
    consultarContratos,
    consultarPlanosPersonal,
    incluirAssinatura,
    incluirAssinaturaPlanoPersonal,
    selecionarContrato,
    selecionarPlanoPersonal,
    visualizarAnexos,
    visualizarAnexosPersonal,
    pesquisarParaAtestado,
    pesquisarParaAtestadoPersonal,
    alterarFotoAluno,
    alterarFotoPersonal,
    salvarImagens,
    salvarImagensPersonal,
    consultarAlunoCartaoVacina,
    visualizarCartaoVacina,
    incluirCartaoVacina,
    consultarClientesParQ,
    consultarPerguntasParQ,
    salvarAssinaturaParQ,
    salvarEdicaoAssinaturaParQ,
    visualizarRespostasParQ,
    removerAssinaturaParQ,
    consultarTermoResponsabilidade,
    selecionarClienteTermoResponsabilidade,
    salvarAssinaturaTermoResponsabilidade,
    visualizarAssinaturaTermoResponsabilidade,
    validarUtilizaTermoResponsabilidade,
    removerAssinaturaTermoResponsabilidade,
    removerAssinaturaEletronica,
    removerAssinaturaProduto,
    removerAssinaturaPersonal,
    selecionarContratoProduto,
    consultarContratosProdutos,
    visualizarAnexosProdutos,
    incluirAssinaturaContratoProduto,
    verificaConfigSescEAtualizaMensagemParq;
    

    public static OperacoesContratoAssinaturaDigital obterOperacao(String o){
        if(o == null){
            return null;
        }
        for(OperacoesContratoAssinaturaDigital op : values()){
            if(op.name().toLowerCase().equals(o.toLowerCase())){
                return op;
            }
        }
        return null;
    }
}
