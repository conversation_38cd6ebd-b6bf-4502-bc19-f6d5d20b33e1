package br.com.pactosolucoes.controle.json.negociacao;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import br.com.pactosolucoes.estudio.modelo.PacoteVO;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.DetalhesRequestRecebidaVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.VendasConfigWS;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.DetalhesRequestRecebida;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.facade.jdbc.vendas.VendasConfig;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.impl.integracaoSistema.IntegracaoSistemaService;
import servicos.integracao.mgb.impl.MgbServiceImpl;
import servicos.integracao.mgb.intf.MgbService;

import servicos.negociacao.NegociacaoService;
import servicos.vendasonline.VendasOnlineService;
import servicos.vendasonline.dto.VendaDTO;

import javax.faces.context.FacesContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.Charset;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Level;
import java.util.logging.Logger;

public class NegociacaoServletControle extends SuperServletControle {


    private static final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processGetRequest(request, response);
    }

    private void processGetRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();
        try {

            try {
                String key = obterParametroString(request, "key");
                String operacao = obterParametroString(request, "operacao");
                OperacoesNegociacaoEnum operacaoEnum = OperacoesNegociacaoEnum.obterOperacao(operacao);

                if (operacaoEnum == null) {
                    throw new Exception("Não foi possível identificar qual operação deve ser realizada.");
                }

                switch (operacaoEnum) {
                    case CONSULTAR_PLANOS:
                        Integer codEmpresa = obterParametroInt(request, "empresa", 0);
                        Integer codPlano = obterParametroInt(request, "plano", 0);

                        boolean somentePlanosPactoFlow = false;
                        try {
                            if (!UteisValidacao.emptyString(request.getParameter("somentePlanosPactoFlow"))) {
                                somentePlanosPactoFlow = Boolean.parseBoolean(request.getParameter("somentePlanosPactoFlow"));
                            }
                        } catch (Exception ignore){}
                        Boolean isRematricula = false;
                        Boolean isRenovacao = obterParametroBoolean(request, "renovacao") == null ? false : obterParametroBoolean(request, "renovacao");
                        if (!isRenovacao){
                             isRematricula = obterParametroBoolean(request, "rematricula") == null ? false : obterParametroBoolean(request, "rematricula");
                        }
                        out.println(consultarPlanos(key, codEmpresa, codPlano, somentePlanosPactoFlow, isRenovacao, isRematricula));
                        break;
                }

            } catch (Exception e) {
                Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ENTROU NO MÉTODO NegociacaoServletControle - processGetRequest ####");
                Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ERRO AO REALIZAR REQUISIÇÃO GET: ", e.getMessage());
                jsonRetorno.put(STATUS_ERRO, e.getMessage());
            }

        } catch (Exception e) {
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ENTROU NO MÉTODO NegociacaoServletControle - processGetRequest ####");
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ERRO AO REALIZAR REQUISIÇÃO GET: ", e.getMessage());
            out.println("ERRO:" + e.getMessage());
        }
    }

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();
        FacesContext fc = FacesContext.getCurrentInstance();
        if(fc != null){
            fc.release();
        }
        try {
            try {
                String key = obterParametroString(request, "key");
                String operacao = obterParametroString(request, "operacao");
                OperacoesNegociacaoEnum operacaoEnum = OperacoesNegociacaoEnum.obterOperacao(operacao);
                if (operacaoEnum == null) {
                    throw new Exception("Não foi possível identificar qual operação deve ser realizada.");
                }
                String retorno;
                switch (operacaoEnum) {

                    case cadastrarClienteVendasOnline:
                        VendaDTO vendaDTO;
                        Boolean ignoraCpf = obterParametroBoolean(request, "ignoraCpf");
                        try {
                            InputStream inputStream = request.getInputStream();
                            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, Charset.forName("LATIN1")));

                            StringBuffer body = new StringBuffer();
                            String line = null;

                            while ((line = reader.readLine()) != null) {
                                body.append(line);
                            }

                            vendaDTO = new VendaDTO(new JSONObject(body.toString()).toString());

                            try {
                                DetalhesRequestRecebidaVO obj = new DetalhesRequestRecebidaVO();
                                obj.setDataRegistro(Calendario.hoje());
                                obj.setUrl(vendaDTO.getUrlZw());
                                obj.setBody(body.toString());
                                obj.setUserAgent(vendaDTO.getUserAgent());
                                obj.setIp(vendaDTO.getIp());
                                gravarDetalheRequestRecebida(key, obj);
                            } catch (Exception ex) {
                                Uteis.logarDebug("Erro ao gravar detalhes da requisição recebida no fluxo cadastrarClienteVendasOnline. | Chave: "
                                        + key + " | Erro: " + ex.getMessage());
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            throw new Exception("Erro body: " + ex.getMessage());
                        }
                        retorno = cadastrarClienteVendasOnline(key, vendaDTO, ignoraCpf);
                        jsonRetorno.put(STATUS_SUCESSO, retorno);
                        out.println(jsonRetorno);
                        break;
                    case obterVendasConfig:
                        Integer unidade = obterParametro(request, "unidade");
                        JSONObject retornoObj = obterVendasConfigDto(key, unidade);
                        jsonRetorno.put(STATUS_SUCESSO, retornoObj);
                        out.println(jsonRetorno);
                        break;
                    case simularNegociacao:
                        jsonRetorno.put("result", simularNegociacao(key, request));
                        out.println(jsonRetorno);
                        break;
                    case gravarNegociacao:
                        jsonRetorno.put("result", gravarNegociacao(key, request));
                        out.println(jsonRetorno);
                        break;
                    case WEBHOOK_VENDA_AVULSA:
                        InputStream inputStream = request.getInputStream();
                        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, Charset.forName("LATIN1")));

                        StringBuilder sb = new StringBuilder();
                        String line;

                        while ((line = reader.readLine()) != null) {
                            sb.append(line);
                        }

                        String body = sb.toString();
                        JSONObject root = new JSONObject(body);
                        JSONObject pessoaAvulsaVOJson = root.getJSONObject("vendaAvulsa");

                        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                        IntegracaoSistemaService service = new IntegracaoSistemaService(acessoControle.getCon());

                        VendaAvulsaVO vendaAvulsaVO = montarVendaAvulsaVO(pessoaAvulsaVOJson);
                        UsuarioVO usuarioVO = montarUsuarioVO(root.getJSONObject("usuario"));
                        EmpresaVO empresaVO = montarEmpresaVO(root.getJSONObject("empresa"));
                        ClienteVO clienteVO = montarClienteVO(root.getJSONObject("cliente"));

                        service.notificarVendaAvulsa(clienteVO, vendaAvulsaVO, empresaVO, usuarioVO);
                        jsonRetorno.put(STATUS_SUCESSO, "Webhook Venda Avulsa processado com sucesso");
                        out.println(jsonRetorno);
                        break;
                    case WEBHOOK_CONTRATO:
                        notificarWebhookContrato(request, key);
                        jsonRetorno.put(STATUS_SUCESSO, "Webhook Contrato processado com sucesso");
                        out.println(jsonRetorno);
                        break;
                    case WEBHOOK_BV:
                        notificarWebhookBV(request, key);
                        jsonRetorno.put(STATUS_SUCESSO, "Webhook BV processado com sucesso");
                        out.println(jsonRetorno);
                        break;
                    default:
                        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Método POST não é permitido nesta URL");
                        break;
                }
            } catch (Exception e) {
                Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ENTROU NO MÉTODO NegociacaoServletControle - processRequest ####");
                Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ERRO AO REALIZAR REQUISIÇÃO POST: ", e.getMessage());
                jsonRetorno.put(STATUS_ERRO, e.getMessage());
                out.println(jsonRetorno);
            }
        } catch (Exception e) {
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ENTROU NO MÉTODO NegociacaoServletControle - processRequest ####");
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ERRO AO REALIZAR REQUISIÇÃO POST: ", e.getMessage());
            out.println("ERRO:" + e.getMessage());
        }
    }

    private VendaAvulsaVO montarVendaAvulsaVO(JSONObject vendaJson) {
        VendaAvulsaVO venda = new VendaAvulsaVO();
        venda.setPessoaVO(new PessoaVO());
        venda.setCodigo(vendaJson.getInt("codigo"));
        venda.setNomeComprador(vendaJson.getString("nomeComprador"));
        venda.setTipoComprador(vendaJson.getString("tipoComprador"));
        venda.setValorTotal(vendaJson.getDouble("valor"));

        if (vendaJson.has("primeiraParcela")) {
            long timestamp = vendaJson.getLong("primeiraParcela");
            venda.setVencimentoPrimeiraParcela(new Date(timestamp));
        }

        JSONArray itensJson = vendaJson.getJSONArray("itens");
        List<ItemVendaAvulsaVO> itens = new ArrayList<>();

        for (int i = 0; i < itensJson.length(); i++) {
            JSONObject itemJson = itensJson.getJSONObject(i);
            itens.add(montarItemVenda(itemJson));
        }

        venda.setItemVendaAvulsaVOs(itens);
        return venda;
    }

    private ItemVendaAvulsaVO montarItemVenda(JSONObject itemJson) {
        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        ProdutoVO produto = new ProdutoVO();
        if (itemJson.has("produtoCodigo") && !itemJson.isNull("produtoCodigo")) {
            produto.setCodigo(itemJson.getInt("produtoCodigo"));
            produto.setDescricao(itemJson.getString("produtoDescricao"));
            produto.setValorFinal(itemJson.optDouble("valorFinal", 0.0));
            produto.setTipoProduto(itemJson.getString("produtoTipo"));
            item.setProduto(produto);
        }

        item.setCodigo(itemJson.getInt("codigo"));
        item.setQuantidade(itemJson.optInt("qtd", 1));
        item.setValorParcial(itemJson.getDouble("valorParcial"));
        item.setDescontoManual(itemJson.getBoolean("descontoManual"));
        item.setValorDescontoManual(itemJson.optDouble("valorDescontoManual"));

        if (itemJson.has("pacoteEscolhido") && !itemJson.isNull("pacoteEscolhido")) {
            PacoteVO pacote = new PacoteVO();
            item.setPacoteVO(pacote);
        }

        item.setDataVenda(new Date());
        item.setEdicaoVendaAvulsa(false);
        item.setSomarItemAdicionar(false);

        return item;
    }

    private UsuarioVO montarUsuarioVO(JSONObject usuarioJson) {
        UsuarioVO usuario = new UsuarioVO();
        usuario.setCodigo(usuarioJson.getInt("codigo"));
        usuario.setNome(usuarioJson.getString("nome"));
        usuario.setUsername(usuarioJson.getString("username"));
        return usuario;
    }

    private ClienteVO montarClienteVO(JSONObject clienteJson) {
        ClienteVO cliente = new ClienteVO();
        cliente.setCodigo(clienteJson.getInt("codigoCliente"));
        cliente.setMatricula(clienteJson.getString("matricula"));
        cliente.setSituacao(clienteJson.getString("situacao"));
        cliente.setPessoa(new PessoaVO());
        cliente.getPessoa().setCodigo(clienteJson.getInt("codigoPessoa"));
        cliente.getPessoa().setNome(clienteJson.getString("nome"));
        cliente.getPessoa().setSexo(clienteJson.getString("sexo"));
        cliente.getPessoa().setTelefoneVOs(new ArrayList<TelefoneVO>());

        JSONArray telefonesJson = clienteJson.getJSONArray("telefones");
        List<TelefoneVO> telefones = new ArrayList<>();

        for (int i = 0; i < telefonesJson.length(); i++) {
            JSONObject itemJson = telefonesJson.getJSONObject(i);
            telefones.add(montarTelefoneVO(itemJson));
        }
        cliente.getPessoa().setTelefoneVOs(telefones);

        JSONArray emailsJson = clienteJson.getJSONArray("emails");
        List<EmailVO> emails = new ArrayList<>();
        for (int i = 0; i < emailsJson.length(); i++) {
            JSONObject emailJson = emailsJson.getJSONObject(i);
            emails.add(montarEmailVO(emailJson));
        }
        cliente.getPessoa().setEmailVOs(emails);
        return cliente;
    }


    private EmpresaVO montarEmpresaVO(JSONObject empresaJson) {
        EmpresaVO empresa = new EmpresaVO();
        empresa.setCodigo(empresaJson.getInt("codigo"));
        empresa.setNome(empresaJson.getString("nome"));
        empresa.setCNPJ(empresaJson.getString("cnpj"));
        return empresa;
    }

    private TelefoneVO montarTelefoneVO(JSONObject telefoneJson){
        TelefoneVO telefone = new TelefoneVO();
        telefone.setTipoTelefone(telefoneJson.getString("tipo"));
        telefone.setNumero(telefoneJson.getString("numero"));
        telefone.setCodigo(telefoneJson.getInt("seq"));
        return telefone;
    }

    private EmailVO montarEmailVO(JSONObject emailJson){
        EmailVO email = new EmailVO();
        email.setEmail(emailJson.getString("email"));
        email.setCodigo(emailJson.getInt("seq"));
        return email;
    }

    private String cadastrarClienteVendasOnline(String key, VendaDTO dados, Boolean ignoraCpf) {

        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            VendasOnlineService service = new VendasOnlineService(key, acessoControle.getCon());
            return service.incluirAlunoOnline(key, dados, ignoraCpf);
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    private void gravarDetalheRequestRecebida(String key, DetalhesRequestRecebidaVO obj) throws Exception {
        DetalhesRequestRecebida detalhesRequestRecebidaDao;
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            detalhesRequestRecebidaDao = new DetalhesRequestRecebida(acessoControle.getCon());
            detalhesRequestRecebidaDao.incluir(obj);
        } catch (Exception e) {
            throw e;
        } finally {
            detalhesRequestRecebidaDao = null;
        }
    }

    private JSONObject obterVendasConfigDto(String key, Integer unidade) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            VendasConfig vendasConfig = new VendasConfig(acessoControle.getCon());
            VendasConfigWS vendasConfigWS = vendasConfig.config(unidade).toWS();
            return new JSONObject(vendasConfigWS);
        } catch (Exception e) {
        }

        return null;
    }

    private JSONObject simularNegociacao(String key, HttpServletRequest request) {
        try {
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ENTROU NO MÉTODO NegociacaoServletControle - simularNegociacao ####");
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, Charset.forName("UTF-8")));

            StringBuffer body = new StringBuffer();
            String line = null;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }

            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            NegociacaoService service = new NegociacaoService(key, acessoControle.getCon());
            JSONObject retorno =  service.simular(key, new JSONObject(body.toString()), acessoControle);
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### FINALIZOU SIMULAÇÃO DE NEGOCIAÇÃO NO MÉTODO NegociacaoServletControle - simularNegociacao ####");
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### RESULTADO NegociacaoServletControle - simularNegociacao ####" + retorno.toString());
            return retorno;
        } catch (Exception e) {
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ERRO NegociacaoServletControle - simularNegociacao ####");
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ERRO AO SIMULAR NEGOCIAÇÃO: ", e.getMessage());
            e.printStackTrace();
        } finally {
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### SAIU DO MÉTODO NegociacaoServletControle - simularNegociacao ####");
        }

        return null;
    }

    private JSONObject gravarNegociacao(String key, HttpServletRequest request) {
        String clienteKey = null;
        ReentrantLock lock = null;
        try {
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ENTROU NO MÉTODO NegociacaoServletControle - gravarNegociacao ####");
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, Charset.forName("UTF-8")));

            StringBuffer body = new StringBuffer();
            String line = null;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            JSONObject negociacaoJson = new JSONObject(body.toString());
            clienteKey = negociacaoJson.optString("cliente");
            String lockKey = key + ":" + clienteKey;

            lock = lockMap.computeIfAbsent(lockKey, k -> new ReentrantLock());

            // Adquirir o lock
            lock.lock();


            NegociacaoService service = new NegociacaoService();
            JSONObject retorno = service.gravarContrato(key, negociacaoJson );

            try {
                AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                Integer cliente = negociacaoJson.optInt("cliente");
                MgbService mgbService = new MgbServiceImpl(acessoControle.getCon());
                mgbService.syncAlunoMgb(null, cliente, null);
            } catch (Exception e) {
                Uteis.logar(e, NegociacaoServletControle.class);
            }

            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### FINALIZOU NEGOCIAÇÃO NO MÉTODO NegociacaoServletControle - gravarNegociacao ####");
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### RESULTADO NegociacaoServletControle - gravarNegociacao ####" + retorno.toString());
            return retorno;
        } catch (Exception e) {
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ERRO NegociacaoServletControle - gravarNegociacao ####");
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### ERRO AO GRAVAR NEGOCIAÇÃO: ", e.getMessage());
            e.printStackTrace();
        } finally {
            if (lock != null) {
                lock.unlock();
            }
            if (clienteKey != null) {
                String lockKey = key + ":" + clienteKey;
                lockMap.computeIfPresent(lockKey, (k, v) -> v.hasQueuedThreads() ? v : null);
            }
            Logger.getLogger(NegociacaoServletControle.class.getName()).log(Level.SEVERE, "#### SAIU DO MÉTODO NegociacaoServletControle - gravarNegociacao ####");
        }

        return null;
    }

    private JSONArray consultarPlanos(String key, Integer empresa, Integer codigoPlano, boolean somentePlanosPactoFlow, Boolean isRenovacao, Boolean isRematricula) throws Exception {
        if (UteisValidacao.emptyString(key)) {
            throw new ConsistirException("Parâmetro KEY não informado");
        }

        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

        List<PlanoVO> planos = new ArrayList<>();
        if (codigoPlano == 0 && empresa == 0) {
            planos = acessoControle.getPlanoDao().consultarVigentesTodasEmpresas(Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, true);
        } else if (codigoPlano != 0 && empresa == 0) {
            planos.add(acessoControle.getPlanoDao().consultarPorChavePrimaria(codigoPlano, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, true));
        } else if (somentePlanosPactoFlow) {
            planos = acessoControle.getPlanoDao().consultarVigentesPactoFlow(empresa, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, true, true);
        } else {
            planos = acessoControle.getPlanoDao().consultarVigentes(empresa, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, true, true, false);
        }

        VendasOnlineService service = new VendasOnlineService(null, acessoControle.getCon());
        Boolean cobrarPrimeiraParcelaCompra = service.obterConfigPrimeiraParcela(empresa);

        JSONArray planosJson = new JSONArray();
        for (PlanoVO plano : planos) {
            if (plano != null) {
                try {
                    if (plano.getParcelamentoOperadora() != null && plano.getParcelamentoOperadora() &&
                            plano.isParcelamentoOperadoraDuracao()) {
                        Integer parcelamentoDuracao = 0;
                        if (plano.getRecorrencia() != null && plano.getRecorrencia() && plano.getPlanoRecorrencia() != null) {
                            parcelamentoDuracao = plano.getPlanoRecorrencia().getDuracaoPlano();
                        } else if (!UteisValidacao.emptyList(plano.getPlanoDuracaoVOs())) {
                            Integer max = 0;
                            for (PlanoDuracaoVO planoDuracaoVO : plano.getPlanoDuracaoVOs()) {
                                if (planoDuracaoVO.getNumeroMeses() > max) {
                                    max = planoDuracaoVO.getNumeroMeses();
                                }
                            }
                            parcelamentoDuracao = max;
                        }

                        //limitar no max de vezes parcelar
                        if (parcelamentoDuracao < plano.getMaximoVezesParcelar()) {
                            plano.setMaximoVezesParcelar(parcelamentoDuracao);
                        }
                    }
                    try {
                        if (plano.getSite() && !plano.getRecorrencia() && !plano.getParcelamentoOperadora() //plano normal para vendas online
                                && (!UteisValidacao.emptyList(plano.getPlanoDuracaoVOs()) && plano.getPlanoDuracaoVOs().size() == 1)
                                && !UteisValidacao.emptyList(plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs())) {
                            plano.setMaximoVezesParcelar(plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().get(0).getQtdParcela());
                        }
                    } catch (Exception ex) {}
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                PlanoWS planoWS = obterPlanoWSValidandoEmpresa(empresa, plano, acessoControle.getCon(), isRenovacao, somentePlanosPactoFlow, isRematricula);
                planoWS.setCobrarPrimeiraParcelaCompra(cobrarPrimeiraParcelaNaCompra(cobrarPrimeiraParcelaCompra, plano));

                if (!UteisValidacao.emptyNumber(empresa) && service.obterConfigApresentarValorTotalDoPlano(empresa)) {
                    try {
                        planoWS.setValorTotalDoPlano(getValorPlano(key, empresa, service, plano));
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        continue;
                    }
                }

                planoWS.setQuantidadeCompartilhamentos(plano.getQuantidadeCompartilhamentos());

                JSONObject planoJsonObj = new JSONObject(planoWS);
                planosJson.put(planoJsonObj);
            }
        }
        return planosJson;
    }

    private PlanoWS obterPlanoWSValidandoEmpresa(int empresa, PlanoVO plano, Connection con, Boolean isRenovacao, Boolean somentePlanosPactoFlow, Boolean isRematricula) throws Exception {
        PlanoEmpresaVO planoEmpresaVO = plano.obterPlanoEmpresa(empresa);
        PlanoWS planoWs = plano.toWS(planoEmpresaVO, con, isRenovacao, somentePlanosPactoFlow, isRematricula);
        planoWs.setNrVezesParcelarMatricula(plano.getNrVezesParcelarAdesao());
        return planoWs;
    }

    private boolean cobrarPrimeiraParcelaNaCompra(Boolean cobrarPrimeiraParcelaCompra, PlanoVO plano) {
        boolean retorno = false;
        try {
            /*O Vendas Online não cobra a primeira parcela no ato da venda se for uma parcela futura com a configuração do "Cobrar Primeira Parcela Compra" desmarcada no Gestão do Vendas Online
            Por isso, ele precisa ir nas Condições de Pagamento do plano para descobrir se Entrada está configurado.*/
            if (cobrarPrimeiraParcelaCompra) {
                retorno = true;
            } else if (!cobrarPrimeiraParcelaCompra && !UteisValidacao.emptyList(plano.getPlanoDuracaoVOs())
                    && !UteisValidacao.emptyList(plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs())
                    && plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getEntrada() != null
                    && plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getEntrada()) {
                retorno = true;
            }
        } catch (Exception e) {

        }
        return retorno;
    }

    private void notificarWebhookContrato(HttpServletRequest request, String key) throws Exception{
        Integer contrato = obterParametroInt(request, "contrato");
        Integer usuario = obterParametroInt(request, "usuario");
        Integer cliente = obterParametroInt(request, "cliente");
        Integer empresa = obterParametroInt(request, "empresa");

        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        IntegracaoSistemaService service = new IntegracaoSistemaService(acessoControle.getCon());
        Contrato contratoDao = new Contrato(acessoControle.getCon());
        Usuario usuarioDao = new Usuario(acessoControle.getCon());
        Cliente clienteDao = new Cliente(acessoControle.getCon());
        Empresa empresaDao = new Empresa(acessoControle.getCon());


        ContratoVO contratoVO = contratoDao.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_TODOS);
        UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_TODOS);
        ClienteVO clienteVO = clienteDao.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_TODOS);
        EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_TODOS);
        if (contratoVO == null || usuarioVO == null || clienteVO == null || empresaVO == null) {
               throw new Exception("Dados não encontrados para os parâmetros informados");
        }

        service.notificarContrato(clienteVO, contratoVO, false, empresaVO, usuarioVO);
    }

    private void notificarWebhookBV(HttpServletRequest request, String key) throws Exception{
        Integer questionario = obterParametroInt(request, "questionario");
        Integer usuario = obterParametroInt(request, "usuario");
        Integer cliente = obterParametroInt(request, "cliente");

        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        IntegracaoSistemaService service = new IntegracaoSistemaService(acessoControle.getCon());
        QuestionarioCliente questionarioClienteDao = new QuestionarioCliente(acessoControle.getCon());
        Usuario usuarioDao = new Usuario(acessoControle.getCon());
        Cliente clienteDao = new Cliente(acessoControle.getCon());


        QuestionarioClienteVO questionarioClienteVO = questionarioClienteDao.consultarPorChavePrimaria(questionario, Uteis.NIVELMONTARDADOS_TODOS);
        UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_TODOS);
        ClienteVO clienteVO = clienteDao.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_TODOS);
        if (questionarioClienteVO == null || usuarioVO == null || clienteVO == null) {
            throw new Exception("Dados não encontrados para os parâmetros informados");
        }

        service.notificarBV(clienteVO, questionarioClienteVO, clienteVO.getEmpresa(), usuarioVO);
    }

    private Double getValorPlano(String key, int empresa, VendasOnlineService service, PlanoVO plano) throws ServiceException {
        VendaDTO vendaDTO = new VendaDTO();
        vendaDTO.setUnidade(empresa);
        vendaDTO.setPlano(plano.getCodigo());
        vendaDTO.setNrVezesDividir(plano.getPlanoDuracaoVOs().get(0).getNumeroMeses());
        vendaDTO.setDiaVencimento(Calendario.hoje().getDate());

        ContratoVO contratoVO = service.simular(key, vendaDTO, empresa);
        return contratoVO.getValorFinal();
    }
}
