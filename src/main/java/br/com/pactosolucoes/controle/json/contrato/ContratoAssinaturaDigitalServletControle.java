/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.controle.json.contrato;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.contrato.servico.impl.ContratoAssinaturaDigitalServiceImpl;
import br.com.pactosolucoes.contrato.servico.impl.PlanoPersonalAssinaturaDigitalServiceImpl;
import br.com.pactosolucoes.contrato.servico.impl.ProdutoAssinaturaDigitalServiceImpl;
import br.com.pactosolucoes.contrato.servico.intf.ContratoAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.contrato.servico.intf.PlanoPersonalAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.contrato.servico.intf.ProdutoAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.acesso.DadosAcessoOfflineVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ContratoAssinaturaDigitalServletControle extends SuperServletControle {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();

        //Desativar cache
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);

        String token = obterParametroString(request, "token");

        boolean requestFromNewUI = false;
        String ipFromToken = "";
        String userAgentFromToken = "";
        try {
            String tokenDecript = Uteis.desencriptar(token, "pactotknssntrdgtl");
            JSONObject objToken = new JSONObject(tokenDecript);
            ipFromToken = objToken.optString("tokenIp");
            userAgentFromToken = objToken.optString("tokenUserAgent");
            requestFromNewUI = !UteisValidacao.emptyString(ipFromToken) && !UteisValidacao.emptyString(userAgentFromToken);

        } catch (Exception ignored) {
        }

        if (!validarUsuarioLogado(request) && !requestFromNewUI) {
            proccessRedirectToLoginHtmlPage(request, token, out);
            return;
        }

        if (requestFromNewUI) {
            String userAgentFromRequest = request.getHeader("User-Agent");
            String ipFromRequest = request.getHeader("X-Forwarded-For");
            String remoteIp = obterParametroString(request, "remoteIP");
            if (remoteIp != null && !remoteIp.isEmpty()) {
                ipFromRequest = remoteIp;
            }
            if (ipFromRequest == null || ipFromRequest.isEmpty() || "unknown".equalsIgnoreCase(ipFromRequest)) {
                ipFromRequest = request.getRemoteAddr();
            }

            boolean validRequest;
            if (!UteisValidacao.emptyString(ipFromRequest) && !"127.0.0.1".equals(ipFromRequest)) {
                validRequest = ipFromRequest.equals(ipFromToken) && userAgentFromRequest.equals(userAgentFromToken);
            }else{
                validRequest = userAgentFromRequest.equals(userAgentFromToken);
            }

            if (!validRequest) {
                proccessRedirectToLoginHtmlPage(request, token, out);
                return;
            }

        }

        JSONObject jsonRetorno = new JSONObject();
        try {
            try {
                JSONObject params;
                if(token.startsWith("TelaCliente:")){
                    String chave = token.split(":")[1];
                    String jsonStr = "{\"chave\":\""+chave+"\"}";
                    params = new JSONObject(jsonStr);
                }else{
                    params = new JSONObject(Uteis.desencriptar(token, "pactotknssntrdgtl"));
                }
                String key = params.getString("chave");
                Integer empresa = obterParametro(request, "empresa");
                Connection con = new DAO().obterConexaoEspecifica(key);

                ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);

                boolean isTermoResponsabilidadeExAluno =  zillyonWebFacade.getConfiguracaoSistema().isTermoResponsabilidadeExaluno();
                ContratoAssinaturaDigitalServiceInterface cadServiceContrato = new ContratoAssinaturaDigitalServiceImpl(con);
                cadServiceContrato.setServletContext(request.getSession().getServletContext());
                ProdutoAssinaturaDigitalServiceInterface prodServiceContrato = new ProdutoAssinaturaDigitalServiceImpl(con);
                prodServiceContrato.setServletContext(request.getSession().getServletContext());
                String operacao = obterParametroString(request, "operacao");

                request.getSession().setAttribute(LoginControle.class.getName(), new LoginControle());

                PlanoPersonalAssinaturaDigitalServiceInterface cadServicePlanoPersonal = new PlanoPersonalAssinaturaDigitalServiceImpl(con);
                cadServicePlanoPersonal.setServletContext(request.getSession().getServletContext());
                String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreino);
                Integer usuarioNot = 0;
                try {
                    usuarioNot = params.optInt("usuario");
                } catch (Exception ignored){}
                
                OperacoesContratoAssinaturaDigital operacaoEnum = OperacoesContratoAssinaturaDigital.obterOperacao(operacao);
                if (operacaoEnum == null) {
                    throw new Exception("Não foi possível identificar qual operação deve ser realizada.");
                }
                String descMoedaEmpresa = (obterParametroString(request, "descMoedaEmpresa") == null ? "Real" : obterParametroString(request,"descMoedaEmpresa"));
                switch(operacaoEnum){

                    // Contrato de Cliente
                    case consultarContratos:
                        if(UteisValidacao.emptyNumber(empresa)){
                            JSONObject info = cadServiceContrato.infoAcesso(params.getString("chave"), params.optInt("usuario"), params.optInt("empresaLogada"));
                            jsonRetorno.put("info", info);
                            empresa = info.getInt("empresa");
                        }
                        Boolean contratosCancelados = obterParametroBoolean(request, "contratosCancelados");
                        String filtro = obterParametroStringDecode(request, "filtro");
                        String todos = obterParametroString(request, "todos");

                        final JSONObject assinados = cadServiceContrato.consultarContratos(true, filtro, empresa, true, contratosCancelados);
                        jsonRetorno.put("assinados", assinados.get("contratos"));
                        jsonRetorno.put("nrassinados", assinados.get("nr"));

                        final JSONObject naoassinados = cadServiceContrato.consultarContratos(false, filtro, empresa, todos != null && todos.equals("naoassinados"), contratosCancelados);
                        jsonRetorno.put("naoassinados", naoassinados.get("contratos"));
                        jsonRetorno.put("nrnaoassinados", naoassinados.get("nr"));

                        AcessoControle acesso = DaoAuxiliar.retornarAcessoControle(params.getString("chave"));
                        Integer idEmp = params.getInt("empresaLogada");
                        PerfilAcessoVO perfil = acesso.getPerfilAcessoDao().consultarPorUsuarioEmpresa(usuarioNot, idEmp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        UsuarioVO usua = acesso.getUsuarioDao().consultarPorCodigo(usuarioNot, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        Integer empresaLog = params.getInt("empresaLogada");
                        Boolean permitirRemoverassinatura = validarPermissaoExcluirAssinatura(perfil, usua,empresaLog, "RemoverAssinatura", "9.70 - Permite remover assinatura digital de contratos", acesso);
                        jsonRetorno.put("permissaoRemoverAssinatura", permitirRemoverassinatura);
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_TOTALACESSOS_GERAL, usuarioNot, empresa);
                        break;
                    case consultarContratosProdutos:
                        if(UteisValidacao.emptyNumber(empresa)){
                            JSONObject info = cadServiceContrato.infoAcesso(params.getString("chave"), params.optInt("usuario"), params.optInt("empresaLogada"));
                            jsonRetorno.put("info", info);
                            empresa = info.getInt("empresa");
                        }
                        String filtro2 = obterParametroStringDecode(request, "filtro");
                        String todos2 = obterParametroString(request, "todos");
                        JSONObject assinados2 = prodServiceContrato.consultarContratosProdutos(true, filtro2, empresa, true);
                        jsonRetorno.put("assinados", assinados2.get("contratos"));
                        jsonRetorno.put("nrassinados", assinados2.get("nr"));
                        JSONObject naoassinados2 = prodServiceContrato.consultarContratosProdutos(false, filtro2, empresa, todos2 != null && todos2.equals("naoassinados"));
                        jsonRetorno.put("naoassinados", naoassinados2.get("contratos"));
                        jsonRetorno.put("nrnaoassinados", naoassinados2.get("nr"));

                        AcessoControle acesso2 = DaoAuxiliar.retornarAcessoControle(params.getString("chave"));
                        Integer idEmp2 = params.getInt("empresaLogada");
                        PerfilAcessoVO perfil2 = acesso2.getPerfilAcessoDao().consultarPorUsuarioEmpresa(usuarioNot, idEmp2, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        UsuarioVO usua2 = acesso2.getUsuarioDao().consultarPorCodigo(usuarioNot, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        Integer empresaLog2 = params.getInt("empresaLogada");
                        Boolean permitirRemoverassinatura2 = validarPermissaoExcluirAssinatura(perfil2, usua2,empresaLog2, "RemoverAssinatura", "9.70 - Permite remover assinatura digital de contratos", acesso2);
                        jsonRetorno.put("permissaoRemoverAssinatura", permitirRemoverassinatura2);
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_TOTALACESSOS_GERAL, usuarioNot, empresa);
                        break;
                    case incluirAssinatura:
                        Integer contrato = obterParametro(request, "contrato");
                        Integer aditivo = obterParametroIntegerTratado(request, "aditivo");
                        Integer usuarioResponsavel = params.optInt("usuario");
                        Integer contratoTextoPadrao = obterParametro(request, "contratoTextoPadrao");
                        String ip = obterParametroString(request, "ip");
                        String documentos = obterParametroString(request, "documentos");
                        String endereco = obterParametroString(request, "endereco");
                        String assinatura = obterParametroString(request, "assinatura");
                        String assinatura2 = obterParametroString(request, "assinatura2");
                        String atestado = obterParametroString(request, "atestado");
                        String anexo1 = obterParametroString(request, "anexo1");
                        String anexo2 = obterParametroString(request, "anexo2");
                        Boolean assinaturaCancelamento = obterParametroBoolean(request, "assinaturaCancelamento");
                        Boolean updateAssinatura = Boolean.valueOf(obterParametroString(request, "updateAssinatura"));
                        Boolean updateDocs = Boolean.valueOf(obterParametroString(request, "updateDocs"));
                        Boolean updateEnd = Boolean.valueOf(obterParametroString(request, "updateEnd"));
                        Boolean updateAte = Boolean.valueOf(obterParametroString(request, "updateAte"));
                        Boolean updateAnexo1 = Boolean.valueOf(obterParametroString(request, "updateAnexo1"));
                        Boolean updateAnexo2 = Boolean.valueOf(obterParametroString(request, "updateAnexo2"));
                        cadServiceContrato.incluirAssinatura(params.getString("chave"), token, contrato, ip,
                                usuarioResponsavel, contratoTextoPadrao, documentos, assinatura, endereco, atestado,
                                anexo1, anexo2, updateAssinatura, updateDocs, updateEnd, updateAte, updateAnexo1, updateAnexo2,descMoedaEmpresa, assinaturaCancelamento, obterCaminhoLocal(request), assinatura2, aditivo);
                        jsonRetorno.put(STATUS_SUCESSO, "Assinatura salva com sucesso!");
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_TOTALSUCESSO, usuarioResponsavel, obterEmpresaContrato(con, contrato));
                        break;
                    case incluirAssinaturaContratoProduto:
                        Integer contrato2 = obterParametro(request, "contrato");
                        Integer usuarioResponsavel2 = params.optInt("usuario");
                        Integer vendaAvulsa = obterParametro(request, "vendaAvulsa");
                        String ip2 = obterParametroString(request, "ip");
                        String documentos2 = obterParametroString(request, "documentos");
                        String endereco2 = obterParametroString(request, "endereco");
                        String assinatura22 = obterParametroString(request, "assinatura");
                        String atestado2 = obterParametroString(request, "atestado");
                        String anexo12 = obterParametroString(request, "anexo1");
                        String anexo22 = obterParametroString(request, "anexo2");
                        Boolean updateAssinatura2 = Boolean.valueOf(obterParametroString(request, "updateAssinatura"));
                        Boolean updateDocs2 = Boolean.valueOf(obterParametroString(request, "updateDocs"));
                        Boolean updateEnd2 = Boolean.valueOf(obterParametroString(request, "updateEnd"));
                        Boolean updateAte2 = Boolean.valueOf(obterParametroString(request, "updateAte"));
                        Boolean updateAnexo12 = Boolean.valueOf(obterParametroString(request, "updateAnexo1"));
                        Boolean updateAnexo22 = Boolean.valueOf(obterParametroString(request, "updateAnexo2"));
                        prodServiceContrato.incluirAssinaturaContratoProduto(params.getString("chave"), token, contrato2, ip2,
                                usuarioResponsavel2, vendaAvulsa, documentos2, assinatura22, endereco2, atestado2,
                                anexo12, anexo22, updateAssinatura2, updateDocs2, updateEnd2, updateAte2, updateAnexo12, updateAnexo22,descMoedaEmpresa, obterCaminhoLocal(request));
                        jsonRetorno.put(STATUS_SUCESSO, "Assinatura salva com sucesso!");
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_TOTALSUCESSO, usuarioResponsavel2, obterEmpresaContrato(con, contrato2));
                        break;
                    case selecionarContrato:
                        final Integer codigoContrato = obterParametro(request, "contrato");
                        final Integer codigoAditivo = obterParametroIntegerTratado(request, "aditivo");
                        try {
                            jsonRetorno.put("dados", cadServiceContrato.selecionarContrato(params.getString("chave"), codigoContrato, codigoAditivo));
                        }catch (Exception e){
                            JSONObject json = new JSONObject();
                            json.put("contrato", codigoContrato);
                            json.put("aditivo", codigoAditivo);
                            json.put("texto", "<b>ERRO:" + e.getMessage() + "</b>");
                            json.put("modelo", 0);
                            jsonRetorno.put("dados", json);
                        }
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_CONTRATOSSELECIONADOS, usuarioNot, obterEmpresaContrato(con, codigoContrato));
                        break;
                    case selecionarContratoProduto:
                        Integer codigocontrato2 = obterParametro(request, "contrato");
                        try {
                            jsonRetorno.put("dados", prodServiceContrato.selecionarContratoProduto(params.getString("chave"), codigocontrato2, descMoedaEmpresa, request));
                        }catch (Exception e){
                            JSONObject json = new JSONObject();
                            json.put("contrato", codigocontrato2);
                            json.put("texto", "<b>ERRO:" + e.getMessage() + "</b>");
                            json.put("modelo", 0);
                            jsonRetorno.put("dados", json);
                        }
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_CONTRATOSSELECIONADOS, usuarioNot, obterEmpresaContrato(con, codigocontrato2));
                        break;
                    case visualizarAnexos:
                        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(params.getString("chave"));
                        final Integer idEmpresa = params.getInt("empresaLogada");
                        final Integer codigoContratoVisualizar = obterParametro(request, "contrato");
                        final Integer codigoAditivoVisualizar = obterParametroIntegerTratado(request, "aditivo");
                        final Boolean assiCancelamento = obterParametroBoolean(request, "contratosCancelados");

                        jsonRetorno.put("dados", cadServiceContrato.visualizarContrato(codigoContratoVisualizar, obterCaminhoLocal(request), assiCancelamento, codigoAditivoVisualizar));

                        PerfilAcessoVO perfilAcesso = acessoControle.getPerfilAcessoDao().consultarPorUsuarioEmpresa(usuarioNot, idEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        UsuarioVO usuarioVO = acessoControle.getUsuarioDao().consultarPorCodigo(usuarioNot, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        Integer empresaLogado = params.getInt("empresaLogada");
                        Boolean permitirRemover = validarPermissaoExcluirAssinatura(perfilAcesso, usuarioVO,empresaLogado, "RemoverAssinatura", "9.70 - Permite remover assinatura digital de contratos", acessoControle);

                        jsonRetorno.put("permissaoRemoverAssinatura", permitirRemover);
                        jsonRetorno.put("permitirApresentarAssinatura2", acessoControle.getEmpresaDao().consultarPorChavePrimaria(idEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS).isExigirAssinaturaDigitalResponsavelFinanceiro());
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_VISUALIZACAOANEXOS, usuarioNot, obterEmpresaContrato(con, codigoContratoVisualizar));
                        break;
                    case visualizarAnexosProdutos:
                        AcessoControle acessoControle2 = DaoAuxiliar.retornarAcessoControle(params.getString("chave"));
                        Integer idEmpresa2 = params.getInt("empresaLogada");
                        Integer codigocontratoVisualizar2 = obterParametro(request, "contrato");
                        jsonRetorno.put("dados", prodServiceContrato.visualizarContratoProduto(codigocontratoVisualizar2, obterCaminhoLocal(request)));

                        PerfilAcessoVO perfilAcesso2 = acessoControle2.getPerfilAcessoDao().consultarPorUsuarioEmpresa(usuarioNot, idEmpresa2, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        UsuarioVO usuarioVO2 = acessoControle2.getUsuarioDao().consultarPorCodigo(usuarioNot, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        Integer empresaLogado2 = params.getInt("empresaLogada");
                        Boolean permitirRemover2 = validarPermissaoExcluirAssinatura(perfilAcesso2, usuarioVO2,empresaLogado2, "RemoverAssinatura", "9.70 - Permite remover assinatura digital de contratos", acessoControle2);

                        jsonRetorno.put("permissaoRemoverAssinatura", permitirRemover2);
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_VISUALIZACAOANEXOS, usuarioNot, obterEmpresaContrato(con, codigocontratoVisualizar2));
                        break;
                    case pesquisarParaAtestado:
                        try {
                            if(UteisValidacao.emptyNumber(empresa)){
                                JSONObject info = cadServiceContrato.infoAcesso(params.getString("chave"), params.optInt("usuario"), params.optInt("empresaLogada"));
                                jsonRetorno.put("info", info);
                                empresa = info.getInt("empresa");
                            }
                        }catch (Exception e){

                        }
                        String filtroAtestado = obterParametroStringDecode(request, "filtro");
                        JSONObject resultado = cadServiceContrato.consultarParaAtestado(filtroAtestado, empresa);
                        jsonRetorno.put("alunos", resultado.getJSONArray("alunos"));
                        break;
                    case alterarFotoAluno:
                        Integer contratoAlunoFoto = obterParametro(request, "contrato");
                        Integer codigoAlunoFoto = obterParametro(request, "codigoaluno");
                        String fotoAluno = obterParametroString(request, "fotoAluno");
                        Integer usuarioLogado = obterParametro(request, "usuariologado");

                        UsuarioVO usuarioSessao = null;
                        LoginControle loginControle = (LoginControle) request.getSession().getAttribute("LoginControle");
                        if (loginControle != null) {
                            usuarioSessao = loginControle.getUsuario();
                        }

                        cadServiceContrato.alterarFotoAluno(params.getString("chave"), contratoAlunoFoto, codigoAlunoFoto, fotoAluno, usuarioLogado, usuarioSessao);
                        SuperControle.notificarOuvintes(DadosAcessoOfflineVO.CHAVE_NOTIFICAR_ATUALIZAR_FOTO_PESSOA + "(" + codigoAlunoFoto + ")", loginControle.getPropertyValue("urlNotificacaoAcesso"), key);
                        jsonRetorno.put(STATUS_SUCESSO, "Ok");
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_FOTOSALTERADAS, usuarioNot, obterEmpresaContrato(con, contratoAlunoFoto));
                        break;
                    case salvarImagens:
                        String chave = obterParametroString(request, "token");
                        Integer contratoImagens = obterParametro(request, "contrato");
                        String imgDocs = obterParametroString(request, "documentos");
                        String imgEndereco = obterParametroString(request, "endereco");
                        String imgAtestado = obterParametroString(request, "atestado");
                        String imgAnexo1 = obterParametroString(request, "anexo1");
                        String imgAnexo2 = obterParametroString(request, "anexo2");

                        imgDocs = imgDocs.contains("image_icon.jpg") ? "" : imgDocs;
                        imgEndereco = imgEndereco.contains("image_icon.jpg") ? "" : imgEndereco;
                        imgAtestado = imgAtestado.contains("image_icon.jpg") ? "" : imgAtestado;
                        imgAnexo1 = imgAnexo1.contains("image_icon.jpg") ? "" : imgAnexo1;
                        imgAnexo2 = imgAnexo2.contains("image_icon.jpg") ? "" : imgAnexo2;

                        boolean docUpdate = Boolean.valueOf(obterParametroString(request, "documentosUpdate"));
                        boolean endUpdate = Boolean.valueOf(obterParametroString(request, "enderecoUpdate"));
                        boolean ateUpdate = Boolean.valueOf(obterParametroString(request, "atestadoUpdate"));
                        boolean anex1Update = Boolean.valueOf(obterParametroString(request, "anexo1Update"));
                        boolean anex2Update = Boolean.valueOf(obterParametroString(request, "anexo2Update"));

                        UsuarioVO usuarioLogad = null;
                        LoginControle loginCtrl = (LoginControle) request.getSession().getAttribute("LoginControle");
                        if (loginCtrl != null) {
                            usuarioLogad = loginCtrl.getUsuario();
                        }

                        cadServiceContrato.salvarImagens(chave, contratoImagens, usuarioLogad,
                                imgDocs, docUpdate, imgEndereco, endUpdate, imgAtestado, ateUpdate,
                                imgAnexo1, anex1Update, imgAnexo2, anex2Update);
                        jsonRetorno.put(STATUS_SUCESSO, "sucesso");
                        break;

                    // Plano Personal
                    case consultarPlanosPersonal:
                        if(UteisValidacao.emptyNumber(empresa)){
                            JSONObject info = cadServiceContrato.infoAcesso(params.getString("chave"), params.optInt("usuario"), params.optInt("empresaLogada"));
                            jsonRetorno.put("info", info);
                            empresa = info.getInt("empresa");
                        }
                        String filtroPersonal = obterParametroStringDecode(request, "filtro");
                        String todosPersonal = obterParametroString(request, "todos");
                        JSONObject assinadosPersonal = cadServicePlanoPersonal.consultarPlanosPersonal(true, filtroPersonal, empresa, true);
                        jsonRetorno.put("assinadosPlanoPersonal", assinadosPersonal.get("planosPersonal"));
                        jsonRetorno.put("nrAssinadosPlanoPersonal", assinadosPersonal.get("nrPlanosPersonal"));
                        JSONObject naoassinadosPersonal = cadServicePlanoPersonal.consultarPlanosPersonal(false, filtroPersonal, empresa, todosPersonal != null && todosPersonal.equals("naoassinadosPlanoPersonal"));
                        jsonRetorno.put("naoassinadosPlanoPersonal", naoassinadosPersonal.get("planosPersonal"));
                        jsonRetorno.put("nrnaoassinadosPlanoPersonal", naoassinadosPersonal.get("nrPlanosPersonal"));

                        AcessoControle acessoPersonal = DaoAuxiliar.retornarAcessoControle(params.getString("chave"));
                        Boolean permitirRemoverassinaturaPersonal = validarPermissaoRemoverAssinatura(params, usuarioNot, acessoPersonal);
                        jsonRetorno.put("permissaoRemoverAssinatura", permitirRemoverassinaturaPersonal);

                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_TOTALACESSOS_GERAL, usuarioNot, empresa);
                        break;
                    case selecionarPlanoPersonal:
                        Integer codigoPlanoPersonal = obterParametro(request, "taxaPersonal");
                        try {
                            jsonRetorno.put("dadosPersonal", cadServicePlanoPersonal.selecionarPlanoPersonal(params.getString("chave"), codigoPlanoPersonal, descMoedaEmpresa));
                        }catch (Exception e){
                            JSONObject json = new JSONObject();
                            json.put("taxaPersonal", codigoPlanoPersonal);
                            json.put("textoPersonal", "<b>ERRO: " + e.getMessage() + "</b>");
                            json.put("modeloPersonal", 0);
                            jsonRetorno.put("dadosPersonal", json);
                        }
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_CONTRATOSSELECIONADOS, usuarioNot, obterEmpresaPlanoPersonal(con, codigoPlanoPersonal));
                        break;
                    case incluirAssinaturaPlanoPersonal:
                        Integer planoPersonal = obterParametro(request, "planoPersonal");
                        Integer usuarioResponsavelPersonal = params.optInt("usuario");
                        Integer planoTextoPadraoPersonal = obterParametro(request, "planoPesonalTextoPadrao");
                        String documentosPersonal = obterParametroString(request, "documentosPersonal");
                        String enderecoPersonal = obterParametroString(request, "enderecoPersonal");
                        String assinaturaPersonal = obterParametroString(request, "assinaturaPersonal");
                        String atestadoPersonal = obterParametroString(request, "atestadoPersonal");
                        String anexo1Personal = obterParametroString(request, "anexo1Personal");
                        String anexo2Personal = obterParametroString(request, "anexo2Personal");
                        Boolean updateAssinaturaPersonal = Boolean.valueOf(obterParametroString(request, "updateAssinaturaPersonal"));
                        Boolean updateDocsPersonal = Boolean.valueOf(obterParametroString(request, "updateDocsPersonal"));
                        Boolean updateEndPersonal = Boolean.valueOf(obterParametroString(request, "updateEndPersonal"));
                        Boolean updateAtePersonal = Boolean.valueOf(obterParametroString(request, "updateAtePersonal"));
                        Boolean updateAnexo1Personal = Boolean.valueOf(obterParametroString(request, "updateAnexo1Personal"));
                        Boolean updateAnexo2Personal = Boolean.valueOf(obterParametroString(request, "updateAnexo2Personal"));
                        cadServicePlanoPersonal.incluirAssinatura(params.getString("chave"), token, planoPersonal,
                                usuarioResponsavelPersonal, planoTextoPadraoPersonal, documentosPersonal, assinaturaPersonal, enderecoPersonal, atestadoPersonal,
                                anexo1Personal, anexo2Personal, updateAssinaturaPersonal, updateDocsPersonal, updateEndPersonal, updateAtePersonal, updateAnexo1Personal, updateAnexo2Personal,descMoedaEmpresa);
                        jsonRetorno.put(STATUS_SUCESSO, "Assinatura salva com sucesso!");
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_TOTALSUCESSO, usuarioResponsavelPersonal, obterEmpresaPlanoPersonal(con, planoPersonal));
                        break;
                    case visualizarAnexosPersonal:
                        Integer codigoPlanoVisualizar = obterParametro(request, "taxaPersonal");
                        jsonRetorno.put("dadosPersonal", cadServicePlanoPersonal.visualizarPlanoPersonal(codigoPlanoVisualizar, obterCaminhoLocal(request)));

                        AcessoControle acessoControlePersonalVis = DaoAuxiliar.retornarAcessoControle(params.getString("chave"));
                        Boolean permitirRemoverPersonalVis = validarPermissaoRemoverAssinatura(params, usuarioNot, acessoControlePersonalVis);
                        jsonRetorno.put("permissaoRemoverAssinatura", permitirRemoverPersonalVis);

                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_VISUALIZACAOANEXOS, usuarioNot, obterEmpresaPlanoPersonal(con, codigoPlanoVisualizar));
                        break;
                    case pesquisarParaAtestadoPersonal:
                        try {
                            if(UteisValidacao.emptyNumber(empresa)){
                                JSONObject info = cadServiceContrato.infoAcesso(params.getString("chave"), params.optInt("usuario"), params.optInt("empresaLogada"));
                                jsonRetorno.put("info", info);
                                empresa = info.getInt("empresa");
                            }
                        }catch (Exception e){

                        }

                        String filtroAtestadoPersonal = obterParametroStringDecode(request, "filtro");
                        JSONObject resultadoPersonal = cadServicePlanoPersonal.consultarParaAtestado(filtroAtestadoPersonal, empresa);
                        jsonRetorno.put("alunos", resultadoPersonal.getJSONArray("alunos"));
                        break;
                    case alterarFotoPersonal:
                        Integer planoPerosnalFoto = obterParametro(request, "planoPersonal");
                        Integer codigoPersonalFoto = obterParametro(request, "codigoPersonal");
                        String fotoPersonal = obterParametroString(request, "fotoPersonal");
                        Integer usuarioLogadoPerosnal = obterParametro(request, "usuariologado");

                        UsuarioVO usuarioSessaoPersonal = null;
                        LoginControle loginControlePersonal = (LoginControle) request.getSession().getAttribute("LoginControle");
                        if (loginControlePersonal != null) {
                            usuarioSessao = loginControlePersonal.getUsuario();
                        }

                        cadServicePlanoPersonal.alterarFotoPersonal(params.getString("chave"), planoPerosnalFoto, codigoPersonalFoto, fotoPersonal, usuarioLogadoPerosnal, usuarioSessaoPersonal);
                        jsonRetorno.put(STATUS_SUCESSO, "Ok");
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.ASSINATURA_DIGITAL_FOTOSALTERADAS, usuarioNot, obterEmpresaPlanoPersonal(con, planoPerosnalFoto));
                        break;
                    case salvarImagensPersonal:
                        String chavePersonal = obterParametroString(request, "token");
                        Integer planoPersonalImagens = obterParametro(request, "contrato");
                        String imgDocsPersonal = obterParametroString(request, "documentos");
                        String imgEnderecoPersonal = obterParametroString(request, "endereco");
                        String imgAtestadoPersonal = obterParametroString(request, "atestado");
                        String imgAnexo1Personal = obterParametroString(request, "anexo1");
                        String imgAnexo2Personal = obterParametroString(request, "anexo2");

                        imgDocs = imgDocsPersonal.contains("image_icon.jpg") ? "" : imgDocsPersonal;
                        imgEndereco = imgEnderecoPersonal.contains("image_icon.jpg") ? "" : imgEnderecoPersonal;
                        imgAtestado = imgAtestadoPersonal.contains("image_icon.jpg") ? "" : imgAtestadoPersonal;
                        imgAnexo1 = imgAnexo1Personal.contains("image_icon.jpg") ? "" : imgAnexo1Personal;
                        imgAnexo2 = imgAnexo2Personal.contains("image_icon.jpg") ? "" : imgAnexo2Personal;

                        boolean docUpdatePersonal = Boolean.valueOf(obterParametroString(request, "documentosUpdate"));
                        boolean endUpdatePersonal = Boolean.valueOf(obterParametroString(request, "enderecoUpdate"));
                        boolean ateUpdatePersonal = Boolean.valueOf(obterParametroString(request, "atestadoUpdate"));
                        boolean anex1UpdatePersonal = Boolean.valueOf(obterParametroString(request, "anexo1Update"));
                        boolean anex2UpdatePersonal = Boolean.valueOf(obterParametroString(request, "anexo2Update"));

                        UsuarioVO usuarioLogadPersonal = null;
                        LoginControle loginCtrlPersonal = (LoginControle) request.getSession().getAttribute("LoginControle");
                        if (loginCtrlPersonal != null) {
                            usuarioLogad = loginCtrlPersonal.getUsuario();
                        }

                        cadServicePlanoPersonal.salvarImagens(chavePersonal, planoPersonalImagens, usuarioLogadPersonal,
                                imgDocs, docUpdatePersonal, imgEndereco, endUpdatePersonal, imgAtestado, ateUpdatePersonal,
                                imgAnexo1, anex1UpdatePersonal, imgAnexo2, anex2UpdatePersonal);
                        jsonRetorno.put(STATUS_SUCESSO, "sucesso");
                        break;
                    // Contrato de Cliente
                    case consultarAlunoCartaoVacina:
                        JSONObject info =new JSONObject();
                        if(UteisValidacao.emptyNumber(empresa)){
                            info = cadServiceContrato.infoAcesso(params.getString("chave"), params.optInt("usuario"), params.optInt("empresaLogada"));
                            jsonRetorno.put("info", info);
                            empresa = info.getInt("empresa");
                        }
                        String filtroCartao = obterParametroStringDecode(request, "filtro");
                        String todosCartao = obterParametroString(request, "todos");
                        JSONObject cadastrados = cadServiceContrato.consultarAlunosCartaoVacina(true, filtroCartao, empresa, true, info.optInt("tipoAnexoCartaoVacina",0));
                        jsonRetorno.put("cadastrados", cadastrados.get("pessoas"));
                        jsonRetorno.put("nrcadastrados", cadastrados.get("nr"));
                        JSONObject naocadastrados = cadServiceContrato.consultarAlunosCartaoVacina(false, filtroCartao, empresa, todosCartao != null && todosCartao.equals("naocadastrados"), info.optInt("tipoAnexoCartaoVacina",0));
                        jsonRetorno.put("naocadastrados", naocadastrados.get("pessoas"));
                        jsonRetorno.put("nrnaocadastrados", naocadastrados.get("nr"));
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.CARTAO_DE_VACINA_TOTALACESSOS_GERAL, usuarioNot, empresa);
                        break;
                    case incluirCartaoVacina:
                        Integer pessoa = obterParametro(request, "pessoa");
                        Integer usuarioResponsavelCartaoVacina = params.optInt("usuario");
                        Integer tipoAnexo = obterParametro(request, "tipoanexo");
                        String anexo1CartaoVacina = obterParametroString(request, "anexo1");
                        cadServiceContrato.incluirCartaoVacina(params.getString("chave"), token, pessoa,
                                usuarioResponsavelCartaoVacina, tipoAnexo,
                                anexo1CartaoVacina,   true);
                        jsonRetorno.put(STATUS_SUCESSO, "Cartão de vacina salvo com sucesso!");
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.CARTAO_DE_VACINA_TOTALACESSOS_GERAL, usuarioResponsavelCartaoVacina, params.optInt("empresaLogada"));
                        break;
                    case visualizarCartaoVacina:
                        Integer codigopessoa = obterParametro(request, "pessoa");
                        jsonRetorno.put("dados", cadServiceContrato.visualizarCartaoVacina(codigopessoa, obterCaminhoLocal(request)));
                        notificarRecurso(zillyonWebFacade, key, RecursoSistema.CARTAO_DE_VACINA_VISUALIZACAOANEXOS, usuarioNot, params.optInt("empresaLogada"));
                        break;
                    case consultarClientesParQ:
                        AcessoControle acc = DaoAuxiliar.retornarAcessoControle(params.getString("chave"));
                        if(UteisValidacao.emptyNumber(empresa)){
                            JSONObject infos = cadServiceContrato.infoAcesso(params.getString("chave"), params.optInt("usuario"), params.optInt("empresaLogada"));
                            jsonRetorno.put("info", infos);
                            empresa = infos.getInt("empresa");
                        }
                        EmpresaVO empe = acc.getEmpresaDao().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        String filtroConsulta = request.getParameter("filtro");
                        RequestHttpService httpService = new RequestHttpService();
                        Map mapParametros = new HashMap<String, String>();
                        mapParametros.put("empresaZw", empresa.toString());
                        mapParametros.put("filtro", URLEncoder.encode(filtroConsulta, "UTF-8"));
                        mapParametros.put("diasParaVencimentoParq", empe.getDiasParaVencimentoParq().toString());

                        String todosParam = request.getParameter("todos");               // <- leu o flag
                        boolean todosFlag = "naoassinadosParQ".equals(todosParam);
                        if (todosFlag) {
                            mapParametros.put("todos", todosParam);                    // <- sinaliza ao serviço remoto
                        }

                        RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(urlTreino + "/prest/avaliacao/" + key + "/consultarClientesParQAssinaturaDigital", null, mapParametros, null, MetodoHttpEnum.GET);
                        String retornoConsulta = respostaHttpDTO.getResponse();
                        JSONObject jsonClientes = new JSONObject(new JSONObject(retornoConsulta).optString("return"));
                        if (jsonClientes.get("status").equals("sucesso")) {
//                            JSONArray clientesParQPositivo = cadServiceContrato.verificarClientesParQPositivo(jsonClientes.getJSONArray("parqPositivo"), filtroConsulta, empresa);
                            jsonRetorno.put("assinados", jsonClientes.get("assinados"));
                            jsonRetorno.put("nrassinados", jsonClientes.get("nrAssinados"));
                            jsonRetorno.put("assinadosvencidos", jsonClientes.get("assinadosvencidos"));
                            jsonRetorno.put("nrassinadosvencidos", jsonClientes.get("nrAssinadosVencidos"));
                            jsonRetorno.put("naoassinados", jsonClientes.get("naoAssinados"));
                            jsonRetorno.put("nrnaoassinados", jsonClientes.get("nrNaoAssinados"));
                            jsonRetorno.put("parqpositivo", jsonClientes.getJSONArray("parqPositivo"));
                            jsonRetorno.put("nrparqpositivo", jsonClientes.get("nrPositivos"));
                            jsonRetorno.put("diasParaVencimentoParq", empe.getDiasParaVencimentoParq());
                        }
                        break;
                    case consultarPerguntasParQ:
                        Map<String, String> headers = new HashMap<String, String>();
                        String dados = ExecuteRequestHttpService.executeHttpRequestGETEncode(
                                urlTreino + "/prest/avaliacao/" + key + "/obterPerguntasParQ?codigoRespostaParq=" + obterParametro(request, "codigoRespostaParq"),
                                headers,
                                "UTF-8"
                        );
                        JSONObject json = new JSONObject(dados);
                        jsonRetorno.put("perguntasParQ", json.getJSONArray("perguntasParQ"));
                        jsonRetorno.put("respostasClienteParQ", json.getJSONArray("respostasClienteParQ"));

                        ZillyonWebFacade zwDAO = new ZillyonWebFacade(con);
                        String estadoSigla = zwDAO.getEmpresa().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_TODOS).getEstado().getSigla();
                        if (estadoSigla != null) {
                            String siglaU = estadoSigla.toUpperCase();
                            if (siglaU.equals("RJ")) {
                                jsonRetorno.put("apresentarLeiParqRJ", json.getBoolean("apresentarLeiParq"));
                            } else if (siglaU.equals("GO")) {
                                jsonRetorno.put("apresentarLeiParqGO", json.getBoolean("apresentarLeiParq"));
                            }
                        }

                        break;
                    case salvarAssinaturaParQ:
                        Integer usuario = params.optInt("usuario");
                        Integer matriculaAluno = obterParametro(request, "matriculaAluno");
                        String jsonRespostas = obterParametroString(request, "data");
                        String retorno = ExecuteRequestHttpService.post(urlTreino + "/prest/avaliacao/" + key +
                                "/salvarRespostasParQ?usuarioZw=" + usuario, jsonRespostas, new HashMap<>());
                        JSONObject retornoJson = new JSONObject(new JSONObject(retorno).optString("return"));
                        if (retornoJson.optString("retorno").equals("sucesso")) {
                            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().
                                    consultarPorCodigoMatricula(matriculaAluno, 0, Uteis.NIVELMONTARDADOS_MINIMOS);
                            clienteVO.setParqPositivo(retornoJson.getBoolean("parqpositivo"));
                            DaoAuxiliar.retornarAcessoControle(key).getClienteDao().alterarParqCliente(clienteVO);
                        }
                        jsonRetorno.put(STATUS_SUCESSO, "Assinatura par-q concluída com sucesso!");
                        break;
                    case salvarEdicaoAssinaturaParQ:
                        Integer usuarioZw = params.optInt("usuario");
                        Integer matriculaAluno1 = obterParametro(request, "matriculaAluno");
                        Integer codigoRPAS = obterParametro(request, "codigoRespostaParqAlunoSelecionado");
                        String jsonRespostasEditadas = obterParametroString(request, "data");
                        String retornoEdicao = ExecuteRequestHttpService.post(
                                urlTreino + "/prest/avaliacao/" + key + "/salvarEdicaoRespostasParQ?usuarioZw=" + usuarioZw + "&codigoRespostaParq=" + codigoRPAS,
                                jsonRespostasEditadas,
                                new HashMap<>()
                        );
                        JSONObject retornoJson1 = new JSONObject(new JSONObject(retornoEdicao).optString("return"));
                        if (retornoJson1.optString("retorno").equals("sucesso")) {
                            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigoMatricula(matriculaAluno1, 0, Uteis.NIVELMONTARDADOS_MINIMOS);
                            clienteVO.setParqPositivo(retornoJson1.getBoolean("parqpositivo"));
                            DaoAuxiliar.retornarAcessoControle(key).getClienteDao().alterarParqCliente(clienteVO);
                        }
                        jsonRetorno.put(STATUS_SUCESSO, "Assinatura par-q editada com sucesso!");
                        break; //
                    case visualizarRespostasParQ:
                        AcessoControle acessControle = DaoAuxiliar.retornarAcessoControle(params.getString("chave"));
                        String retornoVizualizarRespostaParQ = ExecuteRequestHttpService.executeHttpRequestGETEncode(
                                urlTreino + "/prest/avaliacao/" + key + "/imprimirParQAssinaturaDigital?matricula=" + obterParametro(request, "matriculaAluno") + "&codigoRespostaParq=" + obterParametro(request, "codigoRespostaParq"),
                                        new HashMap<String, String>(), "UTF-8");
                        JSONObject jsonRespostasParQ = new JSONObject(retornoVizualizarRespostaParQ);

                        jsonRetorno.put("urlPdfRespostas", jsonRespostasParQ.has("return") ? jsonRespostasParQ.get("return") : "");
                        jsonRetorno.put("urlAssinatura", jsonRespostasParQ.has("assinatura") ? jsonRespostasParQ.get("assinatura") : "");

                        Integer emp = params.getInt("empresaLogada");
                        PerfilAcessoVO perf = acessControle.getPerfilAcessoDao().consultarPorUsuarioEmpresa(usuarioNot, emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        UsuarioVO us = acessControle.getUsuarioDao().consultarPorCodigo(usuarioNot, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        Boolean podeRemover = validarPermissaoExcluirAssinatura(perf, us,emp, "RemoverAssinatura", "9.70 - Permite remover assinatura digital de contratos", acessControle);
                        jsonRetorno.put("permiteRemoverAss", podeRemover);
                        break;
                    case removerAssinaturaParQ:
                        AcessoControle aControle = DaoAuxiliar.retornarAcessoControle(params.getString("chave"));
                        UsuarioVO user = aControle.getUsuarioDao().consultarPorCodigo(usuarioNot, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        ClienteVO cli = aControle.getClienteDao().consultarPorMatricula(String.valueOf(obterParametro(request, "matriculaAluno")), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        String a = ExecuteRequestHttpService.executeHttpRequestGETEncode(
                                urlTreino + "/prest/avaliacao/" + key + "/removerAssinaturaParQ?matricula=" + obterParametro(request, "matriculaAluno"),
                                new HashMap<>(), "UTF-8");
                        JSONObject r = new JSONObject(a);
                        if (r.has("return")) {
                            jsonRetorno.put("sucesso", r.get("return").equals("OK"));
                            LogVO log = new LogVO();
                            log.setOperacao("EXCLUSÃO ASSINATURA");
                            log.setChavePrimaria(String.valueOf(obterParametro(request, "matriculaAluno")));
                            log.setResponsavelAlteracao(user.getNome());
                            log.setNomeEntidade("PARQ");
                            log.setNomeEntidadeDescricao("PARQ");
                            log.setDataAlteracao(Calendario.hoje());
                            log.setNomeCampo("ASSINATURA PARQ");
                            log.setPessoa(cli.getPessoa().getCodigo());
                            aControle.getLogDao().incluir(log);
                        }
                        break;
                    case consultarTermoResponsabilidade:
                        if(UteisValidacao.emptyNumber(empresa)){
                            JSONObject infos = cadServiceContrato.infoAcesso(params.getString("chave"), params.optInt("usuario"), params.optInt("empresaLogada"));
                            jsonRetorno.put("info", infos);
                            empresa = infos.getInt("empresa");
                        }
                        String filtroConsultaTermoResponsabilidade = obterParametroStringDecode(request, "filtro");
                        String todosTermoResponsabilidade = obterParametroString(request, "todos");

                        JSONObject termoResponsabilidadeAssinados = cadServiceContrato.consultarContratosTermoResponsabilidade(true, filtroConsultaTermoResponsabilidade, empresa, true, isTermoResponsabilidadeExAluno);
                        jsonRetorno.put("assinados", termoResponsabilidadeAssinados.get("contratos"));
                        jsonRetorno.put("nrassinados", termoResponsabilidadeAssinados.get("nr"));
                        JSONObject termoResponsabilidadeNaoAssinados = cadServiceContrato.consultarContratosTermoResponsabilidade(false, filtroConsultaTermoResponsabilidade, empresa, todosTermoResponsabilidade != null && todosTermoResponsabilidade.equals("naoassinados"), isTermoResponsabilidadeExAluno);
                        jsonRetorno.put("naoassinados", termoResponsabilidadeNaoAssinados.get("contratos"));
                        jsonRetorno.put("nrnaoassinados", termoResponsabilidadeNaoAssinados.get("nr"));
                        break;
                    case selecionarClienteTermoResponsabilidade:
                        JSONObject textoTermoResponsabilidade = cadServiceContrato.selecionarClienteTermoResponsabilidade(key, Integer.parseInt(obterParametroStringDecode(request, "matricula")));
                        jsonRetorno.put("texto", textoTermoResponsabilidade.get("texto"));
                        jsonRetorno.put("matricula", textoTermoResponsabilidade.get("matricula"));
                        break;
                    case salvarAssinaturaTermoResponsabilidade:
                        String assinaturaTermoResponsabilidade = obterParametroString(request, "assinatura");
                        Integer matricula = Integer.valueOf(obterParametroString(request, "matricula"));
                        cadServiceContrato.salvarAssinaturaCliente(matricula, assinaturaTermoResponsabilidade);
                        break;
                    case visualizarAssinaturaTermoResponsabilidade:
                        Integer matriculaAssinatura = Integer.valueOf(obterParametroString(request, "matricula"));
                        JSONObject jsonAssinatura = cadServiceContrato.visualizarAssinaturaTermoResponsabilidade(key, matriculaAssinatura, obterCaminhoLocal(request));
                        jsonRetorno.put("assinatura", jsonAssinatura.get("assinaturadigitaltermoresponsabilidade"));
                        jsonRetorno.put("texto", jsonAssinatura.get("texto"));
                        jsonRetorno.put("matricula", jsonAssinatura.get("matricula"));
                        break;
                    case validarUtilizaTermoResponsabilidade:
                        JSONObject utilizaTermoResponsabilidade = cadServiceContrato.verificaUtilizaTermoResponsabilidade();
                        jsonRetorno.put("utilizaTermoResponsabilidade", utilizaTermoResponsabilidade);
                        break;
                    case removerAssinaturaTermoResponsabilidade:
                        final Integer contratoCliente = obterParametro(request, "contrato");
                        final Integer contratoAditivo = obterParametroIntegerTratado(request, "aditivo");
                        final Integer usuarioRes = params.optInt("usuario");
                        final Boolean assCancelamento = obterParametroBoolean(request, "contratoCancelado");
                        final Boolean isAssinatura2 = obterParametroBoolean(request, "assinatura2");

                        cadServiceContrato.removerAssinaturaCliente(contratoCliente, contratoAditivo, usuarioRes, assCancelamento, isAssinatura2);
                        break;
                    case removerAssinaturaEletronica:
                        Integer contratoClienteAssinatura = Integer.valueOf(obterParametro(request, "contrato"));
                        Boolean assCancel = obterParametroBoolean(request, "contratoCancelado");
                        Integer usuarioResRemover = params.optInt("usuario");
                        cadServiceContrato.removerAssinaturaEletronicaCliente(contratoClienteAssinatura, usuarioResRemover, assCancel);
                        break;
                    case removerAssinaturaProduto:
                        Integer contratoClienteAssinatura2 = Integer.valueOf(obterParametro(request, "contrato"));
                        Integer usuarioResRemover2 = params.optInt("usuario");
                        prodServiceContrato.removerAssinaturaCliente(contratoClienteAssinatura2, usuarioResRemover2);
                        break;
                    case verificaConfigSescEAtualizaMensagemParq:
                        ConfiguracaoSistema configuracaoSistemaDAO = new ConfiguracaoSistema(con);
                        boolean configSesc = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS).getSesc();
                        jsonRetorno.put("verificaConfigSescEAtualizaMensagemParq", configSesc);
                        break;
                    case removerAssinaturaPersonal:
                        Integer planoPersonalRemover = Integer.valueOf(obterParametro(request, "contrato"));
                        Integer usuarioResRemoverPersonal = params.optInt("usuario");
                        cadServicePlanoPersonal.removerAssinaturaPersonal(planoPersonalRemover, usuarioResRemoverPersonal);
                        jsonRetorno.put(STATUS_SUCESSO, "Assinatura removida com sucesso!");
                        break;
                }
                con.close();
                con = null;
            } catch (Exception e) {
                jsonRetorno.put(STATUS_ERRO, "erro" + e.getMessage());
            }
            out.println(jsonRetorno);
        } catch (Exception e) {
            out.println(e.getMessage());
        }
    }

    private static void proccessRedirectToLoginHtmlPage(HttpServletRequest request, String token, PrintWriter out) {
        String urlBase = request.getRequestURL().toString();
        String[] partsUrl = urlBase.split("prest");

        String redirectToMobile = partsUrl[0] + "assinatura/contratos.html?token=" + token;
        request.getSession().setAttribute("urlRedirectToMobile", redirectToMobile);

        String redirectTo = partsUrl[0] + "assinatura/logarContratos.html?token=" + token;
        JSONObject jsonRetorno = new JSONObject();
        jsonRetorno.put("urlRedirect", redirectTo);
        out.println(jsonRetorno);
    }

    private static boolean validarUsuarioLogado(HttpServletRequest request) {
        try {
            LoginControle lControle = ((LoginControle) request.getSession().getAttribute(LoginControle.class.getSimpleName()));
            UsuarioVO usuarioLogado = lControle != null ? lControle.getUsuario() : null;

            return usuarioLogado != null && !UteisValidacao.emptyNumber(usuarioLogado.getCodigo());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return false;
    }

    private String obterCaminhoLocal(HttpServletRequest request) {
        String[] path;
        String url = "../";
        if(StringUtils.isNotBlank(request.getParameter("path"))){
             path = request.getParameter("path").split("assinatura");
             url = path[0] != null ? path[0] : "../";
        }
        return url;
    }

    private void notificarRecurso(ZillyonWebFacade zillyonWebFacade, String chave, RecursoSistema recursoSistema, Integer usuario, Integer empresa) {
        try {
            zillyonWebFacade.notificarRecursoSistema(chave, recursoSistema, usuario, empresa);
        } catch (Exception ignored) {}
    }

    private Integer obterEmpresaContrato(Connection con, Integer contrato) {
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select empresa from contrato where codigo = " + contrato, con);
            if (rs.next()) {
                return rs.getInt("empresa");
            }
            return 0;
        } catch (Exception ex){
            return 0;
        }
    }

    private Integer obterEmpresaPlanoPersonal(Connection con, Integer contrato) {
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select empresa from controletaxapersonal where codigo = " + contrato, con);
            if (rs.next()) {
                return rs.getInt("empresa");
            }
            return 0;
        } catch (Exception ex){
            return 0;
        }
    }

    public boolean validarPermissaoExcluirAssinatura(PerfilAcessoVO perfilAcesso,UsuarioVO usuarioLogado,Integer empresaLogado, String funcionalidade, String textoFuncionalidade, AcessoControle acessoControle) {
        try {
            if (usuarioLogado.getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (usuarioLogado.getAdministrador()) {
                    return true;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = usuarioLogado.getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();

                if (empresaLogado.equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(acessoControle.getPermissaoDao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    acessoControle.getControleAcessoDao().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            usuarioLogado, funcionalidade, textoFuncionalidade);
                }
            }
            return true;
        }catch (Exception ex){
            return false;
        }
    }

    private Boolean validarPermissaoRemoverAssinatura(JSONObject params, Integer usuarioNot, AcessoControle acessoControle) throws Exception {
        Integer idEmpresa = params.getInt("empresaLogada");
        PerfilAcessoVO perfil = acessoControle.getPerfilAcessoDao().consultarPorUsuarioEmpresa(usuarioNot, idEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        UsuarioVO usuario = acessoControle.getUsuarioDao().consultarPorCodigo(usuarioNot, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return validarPermissaoExcluirAssinatura(perfil, usuario, idEmpresa, "RemoverAssinatura", "9.70 - Permite remover assinatura digital de contratos", acessoControle);
    }
}
