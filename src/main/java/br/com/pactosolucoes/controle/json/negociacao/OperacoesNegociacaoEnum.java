package br.com.pactosolucoes.controle.json.negociacao;

public enum OperacoesNegociacaoEnum {
    cadastrarClienteVendasOnline,
    obterVendasConfig,
    simularNegociacao,
    gravarNegociacao,
    CONSULTAR_PLANOS,
    WEBHOOK_VENDA_AVULSA,
    WEBHOOK_CONTRATO,
    WEBHOOK_BV;

    public static OperacoesNegociacaoEnum obterOperacao(String o) {
        if (o == null) {
            return null;
        }
        for (OperacoesNegociacaoEnum op : values()) {
            if (op.name().equalsIgnoreCase(o)) {
                return op;
            }
        }
        return null;
    }
}
