package br.com.pactosolucoes.controle.json.rdstationmarketing;

import br.com.pactosolucoes.controle.json.SuperServletControle;
import br.com.pactosolucoes.controle.json.nuvemshop.OperacoesIntegracaoNuvemshopEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ConfiguracaoEmpresaRDStationVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.Connection;

public class IntegracaoRdStationApiMarketingServletControle extends SuperServletControle {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();

        try {
            String accessToken = obterParametroString(request, "accessToken");
            String refreshToken = obterParametroString(request, "refreshToken");
            String chaveEmpresa = obterParametroString(request, "chaveEmpresa");
            String codigoEmpresa = obterParametroString(request, "codigoEmpresa");
            String operacao = obterParametroString(request, "operacao");

            OperacoesIntegracaoRdStationApiMarketingEnum operacaoEnum = OperacoesIntegracaoRdStationApiMarketingEnum.obterOperacao(operacao);

            if (operacaoEnum == null) {
                throw new IllegalArgumentException("Não foi possível identificar qual operação deve ser realizada.");
            }

            try (Connection con = new DAO().obterConexaoEspecifica(chaveEmpresa)){
                realizarOperacaoRdStationMarketing(accessToken, refreshToken, chaveEmpresa, codigoEmpresa, operacaoEnum, con);
            }

        } catch (Exception e) {
            handleException(e, jsonRetorno);
        } finally {
            out.println(jsonRetorno.toString(4));
        }
    }

    private void realizarOperacaoRdStationMarketing(String accessToken, String refreshToken,
                                                    String chaveEmpresa, String codigoEmpresa,
                                                    OperacoesIntegracaoRdStationApiMarketingEnum operacaoEnum,
                                                    Connection con) throws Exception {
        switch (operacaoEnum) {
            case SALVAR_DADOS_INTEGRACAO:
                Empresa empresaDao = new Empresa(con);
                empresaDao.atualizarTokensRdStationApiMarketing(Integer.parseInt(codigoEmpresa), accessToken, refreshToken);
                break;
            case REGISTRAR_LOG:
                break;
        }
    }

    private void handleException(Exception e, JSONObject jsonRetorno) {
        jsonRetorno.put(STATUS_ERRO, e.getMessage());

        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        e.printStackTrace(printWriter);

        jsonRetorno.put(STATUS_ERRO, stringWriter.toString());
    }
}
