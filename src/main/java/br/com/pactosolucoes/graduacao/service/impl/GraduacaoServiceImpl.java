package br.com.pactosolucoes.graduacao.service.impl;

import br.com.pactosolucoes.graduacao.service.interf.GraduacaoService;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.*;

public class GraduacaoServiceImpl implements GraduacaoService, AutoCloseable {

    private Connection con;

    public GraduacaoServiceImpl(Connection con) throws Exception {
        this.con = con;
    }

    public GraduacaoServiceImpl() {
    }

    @Override
    public Connection getCon() {
        return con;
    }

    @Override
    public void setCon(Connection con) {
        this.con = con;
    }

    private String consulta(Integer empresa, JSONObject filtros, boolean contar, Integer codigo, boolean todos) {
        StringBuilder sql = new StringBuilder();
        if (todos) {
            sql.append("select distinct s.codigocliente \n");
        } else if (contar) {
            sql.append("select count(distinct s.codigo) as total \n");
        } else if (codigo == null){
            sql.append("select distinct s.codigocliente,\n");
            sql.append("        s.nomecliente,\n");
            sql.append("        pes.fotokey,\n");
            sql.append("        s.nomecliente,\n");
            sql.append("        s.matricula \n");
        } else {
            sql.append("select a.descricao as ambiente,\n");
            sql.append("        prof.nome as professor,\n");
            sql.append("        t.descricao as turma,\n");
            sql.append("        n.descricao as nivel\n");
        }
        sql.append("from situacaoclientesinteticodw s\n");
        sql.append("inner join pessoa pes on pes.codigo = s.codigopessoa\n");
        sql.append("left join matriculaalunohorarioturma m on m.pessoa = s.codigopessoa and '");
        sql.append(Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
        sql.append("' between m.datainicio and m.datafim\n");
        sql.append("left join horarioturma h on h.codigo = m.horarioturma\n");
        sql.append("left join turma t on t.codigo = h.turma\n");
        sql.append("left join nivelturma n on n.codigo = h.nivelturma\n");
        sql.append("left join ambiente a on a.codigo = h.ambiente\n");
        sql.append("left join colaborador c on c.codigo = h.professor\n");
        sql.append("left join pessoa prof on c.pessoa = prof.codigo\n");
        sql.append("where 1=1 ");

        String quicksearchValue = "";

        try{
           quicksearchValue  = new JSONObject(filtros.get("filtros").toString()).getString("quicksearchValue");
        } catch (Exception e){
            quicksearchValue = filtros.optString("quicksearchValue");
        }

        if(codigo == null && !UteisValidacao.emptyString(quicksearchValue)){
            // Validar entrada para prevenir SQL injection
            if (!Uteis.isValidStringValue(quicksearchValue)) {
                throw new SecurityException("Valor de busca contém caracteres não permitidos");
            }

            if( Uteis.isNumeroValido(quicksearchValue)) {
                sql.append(" AND s.matricula = ").append(quicksearchValue).append("\n");
            } else {
                // Escapar aspas simples para prevenir SQL injection
                String searchValueEscaped = quicksearchValue.replace("'", "''");
                sql.append(" AND (");
                sql.append(" s.nomeconsulta LIKE remove_acento_upper('%").append(searchValueEscaped.replaceAll(" ", "%")).append("%') \n");
                sql.append(" OR upper(prof.nome) like '%").append(searchValueEscaped.toUpperCase()).append("%' \n");
                sql.append(" OR upper(t.descricao) like '%").append(searchValueEscaped.toUpperCase()).append("%' \n");
                sql.append(" OR upper(n.descricao) like '%").append(searchValueEscaped.toUpperCase()).append("%' \n");
                sql.append(" OR upper(a.descricao) like '%").append(searchValueEscaped.toUpperCase()).append("%' \n");
                sql.append(") ");
            }
        }
        if (empresa != null) {
            sql.append(" AND s.empresacliente = ").append(empresa).append("\n");
        }
        String situacoesEnuns = filtro(filtros, "situacoesEnuns", true);
        if (!situacoesEnuns.isEmpty()) {
            sql.append(" AND s.situacao in (").append(situacoesEnuns).append(") ");
        }

        String ambientesIds = filtro(filtros, "ambientesIds", false);
        if (!ambientesIds.isEmpty()) {
            sql.append(" AND a.codigo in (").append(ambientesIds).append(") ");
        }

        String niveisIds = filtro(filtros, "niveisIds", false);
        if (!niveisIds.isEmpty()) {
            sql.append(" AND n.codigo in (").append(niveisIds).append(") ");
        }

        String professoresIds = filtro(filtros, "professoresIds", false);
        if (!professoresIds.isEmpty()) {
            sql.append(" AND c.codigo in (").append(professoresIds).append(") ");
        }

        String turmasIds = filtro(filtros, "turmasIds", false);
        if (!turmasIds.isEmpty()) {
            // Validar IDs para prevenir SQL injection
            if (!turmasIds.matches("^[0-9,]+$")) {
                throw new SecurityException("IDs de turmas contêm caracteres inválidos");
            }
            sql.append(" AND t.codigo in (").append(turmasIds).append(") ");
        }

        if (filtros.optJSONArray("codigos") != null && filtros.getJSONArray("codigos").length() > 0) {
            JSONArray codigos = filtros.getJSONArray("codigos");
            StringBuilder cods = new StringBuilder();
            for(int i = 0; i < codigos.length(); i++ ){
                if (i > 0) cods.append(",");
                // Validar que é um número inteiro
                int cod = codigos.getJSONObject(i).getInt("cod");
                cods.append(cod);
            }
            if (filtros.getBoolean("paraAvaliacao")){
               sql.append(" and s.codigocliente in (").append(cods.toString()).append(")");
            }else {
                sql.append(" and s.codigocliente not in (").append(cods.toString()).append(")");
            }
        }

        if(codigo != null){
            sql.append(" and s.codigocliente = ").append(codigo);
        }

        if (!contar && !todos && codigo == null) {
            String sort = filtros.getString("sort");
            if(UteisValidacao.emptyString(sort)){
                sql.append(" order by s.nomecliente\n");
            } else {
                String[] split = sort.split(",");
                String campo = split[0];
                String direcao = split.length > 1 ? split[1] : "";
                switch (campo){
                    case "nome":
                        sql.append(" order by s.nomecliente ").append(direcao);
                        break;
                }
            }
            sql.append(" limit ").append(filtros.optInt("limit"));
            sql.append(" offset ").append(filtros.optInt("limit") * filtros.optInt("page"));
        }
        if(todos){
            sql.append(" order by s.codigocliente\n");
        }
        return sql.toString();
    }

    private String filtro(JSONObject filtros, String campo, boolean string) {
        String list = "";
        if (filtros != null && filtros.optJSONObject("filtros") != null
                && filtros.optJSONObject("filtros").optJSONArray(campo) != null) {
            for (int i = 0; i < filtros.optJSONObject("filtros").optJSONArray(campo).length(); i++) {
                list += "," +
                        (string ? ("'" + filtros.optJSONObject("filtros").optJSONArray(campo).getString(i) + "'") :
                                         filtros.optJSONObject("filtros").optJSONArray(campo).getInt(i));
            }
        }
        return list.replaceFirst(",", "");
    }

    public Integer totalAlunosGraduacao(Integer empresa, JSONObject filtros) throws Exception {
        String sql = consulta(empresa, filtros, true, null, false);
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    public JSONObject filtrosAlunosGraduacao(Integer empresa, String modalidades) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select t.descricao as turma, \n");
        sql.append("         t.codigo as tid, \n");
        sql.append(" p.nome as professor, \n");
        sql.append("         c.codigo as profid, \n");
        sql.append(" n.codigo as nivelid, \n");
        sql.append("         n.descricao as nivel, \n");
        sql.append(" a.codigo  as ambid, \n");
        sql.append("         a.descricao as ambiente \n");
        sql.append(" from turma t \n");
        sql.append(" left join horarioturma h on h.turma = t.codigo \n");
        sql.append(" left join nivelturma n on n.codigo = h.nivelturma \n");
        sql.append(" left join ambiente a on a.codigo = h.ambiente \n");
        sql.append(" left join colaborador c on c.codigo = h.professor \n");
        sql.append(" left join pessoa p on p.codigo = c.pessoa \n");
        sql.append(" where 1 = 1 \n");
        if(empresa != null){
            sql.append(" and t.empresa = ").append(empresa).append("\n");
        }

        if (!UteisValidacao.emptyString(modalidades)) {
            sql.append(" and t.modalidade in (").append(modalidades).append(")\n");
        }
        sql.append(" and t.datafinalvigencia::date  >= '");
        sql.append(Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
        sql.append("' order by t.descricao, n.descricao, p.nome, a.descricao ");

        List<String> ambientes = new ArrayList<>();
        List<String> turmas = new ArrayList<>();
        List<String> professores = new ArrayList<>();
        List<String> niveis = new ArrayList<>();
        JSONObject filtros = new JSONObject();

        filtros.put("turmas", new JSONArray());
        filtros.put("professores", new JSONArray());
        filtros.put("ambientes", new JSONArray());
        filtros.put("niveis", new JSONArray());
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                montarArray(turmas, "turmas", rs.getInt("tid"), rs.getString("turma"), filtros);
                montarArray(professores, "professores", rs.getInt("profid"), Uteis.getNomeAbreviado(rs.getString("professor")), filtros);
                montarArray(ambientes, "ambientes", rs.getInt("ambid"), rs.getString("ambiente"), filtros);
                montarArray(niveis, "niveis", rs.getInt("nivelid"), rs.getString("nivel"), filtros);
            }
        }
        return filtros;
    }

    private void montarArray(List<String> adicionados,
                             String filtro,
                             Integer id,
                             String nome,
                             JSONObject filtros) {
        if (nome != null && id != null && !adicionados.contains(nome.concat(id.toString()))) {
            filtros.getJSONArray(filtro).put(new JSONObject() {{
                put("value", id);
                put("label", nome);
            }});
            adicionados.add(nome.concat(id.toString()));
        }
    }

    public JSONArray alunosGraduacao(Integer empresa, JSONObject filtros, boolean todos) throws Exception {
        String sql = consulta(empresa, filtros, false, null, todos);
        JSONArray array = new JSONArray();
        TurmasServiceImpl service = new TurmasServiceImpl(con);
        Map<Integer, JSONObject> dadosNivel = new HashMap<>();
        if (filtros.optBoolean("paraAvaliacao") && filtros.optJSONArray("codigos") != null && filtros.getJSONArray("codigos").length() > 0) {
            JSONArray codigos = filtros.getJSONArray("codigos");
            for(int i = 0; i < codigos.length(); i++ ){
                dadosNivel.put(codigos.getJSONObject(i).getInt("cod"), codigos.getJSONObject(i));
            }
        }

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            while (rs.next()) {
                if(todos){
                    JSONObject json = new JSONObject();
                    json.put("id", rs.getInt("codigocliente"));
                    array.put(json);
                    continue;
                }
                JSONObject json = new JSONObject();
                json.put("id", rs.getInt("codigocliente"));
                json.put("imageUri", Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                json.put("nome", Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(Uteis.getNomeAbreviado(rs.getString("nomecliente"))));
                json.put("matricula", rs.getString("matricula"));

                try (ResultSet rsdados = SuperFacadeJDBC.criarConsulta(consulta(empresa, filtros, false, rs.getInt("codigocliente"), false), con)) {
                    Set<String> ambientes = new HashSet<>();
                    Set<String> turmas = new HashSet<>();
                    Set<String> professores = new HashSet<>();
                    Set<String> niveis = new HashSet<>();
                    while (rsdados.next()) {
                        ambientes.add(rsdados.getString("ambiente") == null ? "-" : rsdados.getString("ambiente"));
                        niveis.add(rsdados.getString("nivel") == null ? "-" : rsdados.getString("nivel"));
                        turmas.add(rsdados.getString("turma") == null ? "-" : rsdados.getString("turma"));
                        professores.add(rsdados.getString("professor") == null ? "-" : Uteis.getNomeAbreviado(rsdados.getString("professor")));
                    }
                    json.put("professor", setToString(professores));
                    json.put("turma", setToString(turmas));
                    json.put("nivel", setToString(niveis));
                    json.put("ambiente", setToString(ambientes));

                    if (filtros.optBoolean("paraAvaliacao")) {
                        try {
                            JSONObject alunoNivel = dadosNivel.get(rs.getInt("codigocliente"));
                            json.put("aulasNecessarias", alunoNivel.optInt("aulasNecessarias"));
                            json.put("aulas", service.aulasAluno(filtros.getString("modalidades"), rs.getInt("matricula"), alunoNivel.getLong("lancamento"), null, null));
                            json.put("nivelId", alunoNivel.optInt("nivelId"));
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                    } else {
                        json.put("aulas", 0);
                    }
                }
                array.put(json);
            }
        }
        return array;
    }

    private String setToString(Set<String> set){
        if(set.isEmpty()){
            return "-";
        }
        String string = "";
        for(String s : set){
            string += ", " + Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(s);
        }
        return string.replaceFirst(", ", "");
    }

    @Override
    public void close() throws Exception {
        if (con != null && !con.isClosed()) {
            con.close();
        }
    }

    public JSONArray consultarInformacoesAluno(Integer matricula) throws Exception {
        JSONArray jsonArray = new JSONArray();
        TurmasServiceImpl service = new TurmasServiceImpl(con);

        for (int mes = 1; mes <= 12; mes++) {
            Calendar calendar = new GregorianCalendar();
            calendar.set(Calendar.MONTH, mes - 1);
            int ultimoDiaDoMes = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            String mesFormatado = Integer.toString(mes).length() == 1 ? "0" + mes : Integer.toString(mes);
            String inicioFormatado = (new Date().getYear() + 1900) + "-" + mesFormatado + "-01";
            String fimFormatado = (new Date().getYear() + 1900) + "-" + mesFormatado + "-" + ultimoDiaDoMes;
            Date inicio = new SimpleDateFormat("yyyy-MM-dd").parse(inicioFormatado);
            Date fim = new SimpleDateFormat("yyyy-MM-dd").parse(fimFormatado);

            Integer qtFrequencia = service.aulasAluno("", matricula, null, inicio, fim);
            Integer qtAulasPrevistas = service.contarAulasPrevistasMes(matricula, mes-1, Calendario.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd"));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("mes", getNomeMes(mes-1));
            jsonObject.put("frequencia", qtFrequencia);
            jsonObject.put("aulasPrevistas", qtAulasPrevistas);
            jsonArray.put(jsonObject);
        }

        return jsonArray;
    }

    public String getNomeMes(int mes){
        String[] meses = {"JANEIRO", "FEVEREIRO", "MARÇO", "ABRIL", "MAIO", "JUNHO", "JULHO", "AGOSTO", "SETEMBRO", "OUTUBRO", "NOVEMBRO", "DEZEMBRO"};
        return meses[mes];
    }

    public JSONObject consultarHorariosEDiasAlunoPorPeriodo(Integer matricula, Long data) throws Exception {
        TurmasServiceImpl service = new TurmasServiceImpl(con);
        JSONObject jsonObject = service.obterDiaSemanaEHorarioAlunoPorPeriodo(
                matricula,
                Calendario.getDataAplicandoFormatacao(new Date(data), "yyyy-MM-dd")
        );
        return jsonObject;
    }
}
