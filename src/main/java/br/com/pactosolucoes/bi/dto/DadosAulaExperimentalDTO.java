package br.com.pactosolucoes.bi.dto;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DadosAulaExperimentalDTO {

    private Integer alunosAgendados = 0;
    private Integer executaram = 0;
    private Integer convertidos = 0;
    private Integer aulasAgendadas = 0;
    private Integer aulasExecutadas = 0;
    private Integer agendamentosAconteceram = 0;
    private Integer agendamentosAcontecer = 0;
    private Double aulasPorAluno = 0.0;
    private Double indicePresenca = 0.0;
    private Double indiceConversaoAulas = 0.0;
    private Double indiceConversaoProfessores = 0.0;

    public DadosAulaExperimentalDTO(JSONObject json) {
        alunosAgendados = json.getInt("alunosAgendados");
        executaram = json.getInt("executaram");
        convertidos = json.getInt("convertidos");
        aulasAgendadas = json.getInt("aulasAgendadas");
        aulasExecutadas = json.getInt("aulasExecutadas");
        agendamentosAconteceram = json.getInt("agendamentosAconteceram");
        agendamentosAcontecer = json.getInt("agendamentosAcontecer");
        aulasPorAluno = json.getDouble("aulasPorAluno");
        indicePresenca = json.getDouble("indicePresenca");
        indiceConversaoAulas = json.getDouble("indiceConversaoAulas");
        indiceConversaoProfessores = json.getDouble("indiceConversaoProfessores");
    }

    public DadosAulaExperimentalDTO() {
    }

    public Integer getAlunosAgendados() {
        return alunosAgendados;
    }

    public void setAlunosAgendados(Integer alunosAgendados) {
        this.alunosAgendados = alunosAgendados;
    }

    public Integer getExecutaram() {
        return executaram;
    }

    public void setExecutaram(Integer executaram) {
        this.executaram = executaram;
    }

    public Integer getConvertidos() {
        return convertidos;
    }

    public void setConvertidos(Integer convertidos) {
        this.convertidos = convertidos;
    }

    public Integer getAulasAgendadas() {
        return aulasAgendadas;
    }

    public void setAulasAgendadas(Integer aulasAgendadas) {
        this.aulasAgendadas = aulasAgendadas;
    }

    public Integer getAulasExecutadas() {
        return aulasExecutadas;
    }

    public void setAulasExecutadas(Integer aulasExecutadas) {
        this.aulasExecutadas = aulasExecutadas;
    }

    public Integer getAgendamentosAconteceram() {
        return agendamentosAconteceram;
    }

    public void setAgendamentosAconteceram(Integer agendamentosAconteceram) {
        this.agendamentosAconteceram = agendamentosAconteceram;
    }

    public Integer getAgendamentosAcontecer() {
        return agendamentosAcontecer;
    }

    public void setAgendamentosAcontecer(Integer agendamentosAcontecer) {
        this.agendamentosAcontecer = agendamentosAcontecer;
    }

    public Double getAulasPorAluno() {
        return aulasPorAluno;
    }

    public void setAulasPorAluno(Double aulasPorAluno) {
        this.aulasPorAluno = aulasPorAluno;
    }

    public Double getIndicePresencaFormatado() {
        return Uteis.arredondarForcando2CasasDecimais(indicePresenca);
    }

    public Double getIndicePresenca() {
        return indicePresenca;
    }

    public void setIndicePresenca(Double indicePresenca) {
        this.indicePresenca = indicePresenca;
    }

    public Double getIndiceConversaoAulasFormatado() {
        return Uteis.arredondarForcando2CasasDecimais(indiceConversaoAulas);
    }

    public Double getIndiceConversaoAulas() {
        return indiceConversaoAulas;
    }

    public void setIndiceConversaoAulas(Double indiceConversaoAulas) {
        this.indiceConversaoAulas = indiceConversaoAulas;
    }

    public String getIndiceConversaoProfessoresFormatado() {
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(indiceConversaoProfessores);
    }
    public Double getIndiceConversaoProfessores() {
        return indiceConversaoProfessores;
    }

    public void setIndiceConversaoProfessores(Double indiceConversaoProfessores) {
        this.indiceConversaoProfessores = indiceConversaoProfessores;
    }
}
