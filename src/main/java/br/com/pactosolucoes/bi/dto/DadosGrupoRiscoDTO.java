package br.com.pactosolucoes.bi.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.HashMap;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DadosGrupoRiscoDTO {

    private HashMap<String, Integer> qtdClientes;
    private List<GrupoRiscoClientesDTO> listaClientes;

    public HashMap<String, Integer> getQtdClientes() {
        return qtdClientes;
    }

    public void setQtdClientes(HashMap<String, Integer> qtdClientes) {
        this.qtdClientes = qtdClientes;
    }

    public List<GrupoRiscoClientesDTO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<GrupoRiscoClientesDTO> listaClientes) {
        this.listaClientes = listaClientes;
    }
}
