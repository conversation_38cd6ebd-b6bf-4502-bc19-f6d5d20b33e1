/**
 * 
 */
package br.com.pactosolucoes.comuns.util;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import negocio.comuns.utilitarias.Calendario;

/**
 * Uteis para manipulação de datas data
 * 
 * <AUTHOR>
 * 
 */
public final class DateUtilities {
	
	private DateUtilities(){
		
	}

	/**
	 * 
	 * @param dataInicial
	 * @param dataFinal
	 * @return inverso considerando o ano bissexto
	 */
	public static long nrDiasEntreDatas(final Date dataInicial, final Date dataFinal) {
		final Calendar datIni = Calendario.getInstance();
		final Calendar datFin = Calendario.getInstance();

		boolean inverso = false;

		// Se a dataInicial for maior que a dataFinal
		// Seta dataInicial e dataFinal
		// se não
		// Seta dataFinal e dataInicial e inverso recebe false
		if (dataInicial.before(dataFinal)) {
			datIni.setTime(dataInicial);
			datFin.setTime(dataFinal);
		} else {
			datIni.setTime(dataFinal);
			datFin.setTime(dataInicial);
			inverso = true;
		}

		int numDias = 0;
		// pega o dia inicial do ano
		final int diaIni = datIni.get(Calendar.DAY_OF_YEAR);
		// pega o ano inicial
		final int anoIni = datIni.get(Calendar.YEAR);
		// pega o dia final do ano
		final int diaFin = datFin.get(Calendar.DAY_OF_YEAR);
		// pega o a ano final
		final int anoFin = datFin.get(Calendar.YEAR);

		// se ano inicial igual a ano fim
		// número de dias recebe dia fim - dia inicial
		// se não
		// iterar sobre os anos entre as datas somando os dias
		// considerando anos bissextos
		if (anoIni == anoFin) {
			numDias = diaFin - diaIni;
		} else {
			numDias += (anoIni % 4 == 0) ? (366 - diaIni) : (365 - diaIni);

			for (int i = 1; i < (anoFin - anoIni); i++) {
				final int ano = anoIni + i;
				numDias += (ano % 4 == 0) ? 366 : 365;

			}

			numDias += diaFin;
		}
		return inverso ? -numDias : numDias;
	}
	public static LocalDate toLocalDate(Date date) {
		if (date instanceof java.sql.Date) {
			return ((java.sql.Date) date).toLocalDate();
		}
		return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
	}

}
