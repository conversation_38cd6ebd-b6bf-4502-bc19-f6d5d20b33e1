package br.com.pactosolucoes.comuns.to;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.apf.APF;
import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;
import servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CartaoCreditoTO extends SuperVO {

    private String numero;
    private String codigoSeguranca;
    private int bandeira;
    private Integer parcelas = 0;
    private int mesValidade;
    private int anoValidade;
    private String validade;
    private String nomeTitular;
    private String cpfCnpjPortador;
    private Date dataNascPortador;
    private String nomeMaePortador;
    private String numeroDocumento = "";
    private String valorDocumento = "";
    private double valor = 0.0;
    private String ip = "";
    private String ipClientePacto = "";
    private String url = "";
    private String transacaoAnterior = "";
    private List<MovParcelaVO> listaParcelas;
    private OperadorasExternasAprovaFacilEnum band;
    private UsuarioVO usuarioResponsavel;
    private String codExternoTransacaoAnterior = "";
    private String tentativasRepescagem;
    private Integer empresa = 0;
    private boolean trocarCartao = false;
    private Integer idPessoaCartao;
    private String chaveAPI;
    private AdquirenteMaxiPagoEnum adquirenteMaxiPago = AdquirenteMaxiPagoEnum.NENHUM;
    private String tokenExterno;
    private InstalmentTypeInstlmtTp tipoParcelamentoStone = InstalmentTypeInstlmtTp.A_VISTA;
    private String tokenAragorn;
    private String tokenVindi;
    private String tokenPagoLivre;
    private TipoTransacaoEnum tipoTransacaoEnum;
    private String idCardMundiPagg;
    private String idCardPagarMe;
    private boolean usarIdVindiCobranca = false;
    private boolean somenteUmEnvioCartaoTentativa = false;
    private boolean transacaoPresencial = false;
    private boolean retentativaManual = false;
    private String gatewayTokenVindi;
    private boolean transacaoVerificarCartao = false;
    private OrigemCobrancaEnum origemCobranca;
    private boolean async = false;
    private boolean editandoAutorizacao = false;
    private Double desconto;
    private boolean aplicarDesconto = false;
    private boolean usarTokenCieloCobranca = false;
    private String tokenCielo = "";
    private boolean isVerificacaoZeroDollar = false;
    private String idCardStoneV5;


    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public static void validarDados(CartaoCreditoTO obj) throws Exception {
        if (obj.getBand() == null) {
            throw new ConsistirException("A BANDEIRA deve ser informada.");
        }

        if (!obj.isUsarIdVindiCobranca() && !obj.isUsarTokenCieloCobranca()) {
            if (UteisValidacao.emptyString(obj.getNomeTitular())){
                throw new ConsistirException("Informe o NOME DO TITULAR do cartão!");
            }

            if (UteisValidacao.emptyString(obj.getNumero())) {
                throw new ConsistirException("O NÚMERO DO CARTÃO deve ser informado.");
            }

            if (UteisValidacao.emptyString(obj.getCodigoSeguranca())) {
                throw new ConsistirException("O CÓDIGO DE SEGURANÇA CVV do cartão deve ser informado.");
            }

            try {
                obj.getMesValidade();
                obj.getAnoValidade();
            } catch (Exception e) {
                throw new ConsistirException("Data de validade inválida. Experimente informar a validade do cartão no formato mm/aaaa.");
            }
            //
            if (UteisValidacao.emptyNumber(obj.getMesValidade()) || UteisValidacao.emptyNumber(obj.getAnoValidade())) {
                throw new ConsistirException("A DATA DE VENCIMENTO deve ser informada.");
            }
            Calendar cal = Calendario.getInstance();
            if (obj.getMesValidade() == 0 || obj.getMesValidade() > 12) {
                throw new ConsistirException("O mês informado na VALIDADE é inválido. Valor deve ser entre 01 e 12.");
            }
            if (obj.getAnoValidade() > (cal.get(Calendar.YEAR) + 15)) {
                throw new ConsistirException("O ano informado na VALIDADE é inválido. Valor deve ser no máximo " + (cal.get(Calendar.YEAR) + 15) + ".");
            }
            UteisValidacao.validarVencimentoCartao(obj.getValidade());
        }

        if (UteisValidacao.emptyNumber(obj.getParcelas())) {
            throw new ConsistirException("O NÚMERO DE PARCELAS deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getEmpresa())) {
            throw new ConsistirException("Uma EMPRESA deve ser informada.");
        }
    }

    public String getCpfCnpjSomenteNumeros() {
        return Uteis.removerMascara(this.getCpfCnpjPortador());
    }

    /**
     * @return the codigoSeguranca
     */
    public String getCodigoSeguranca() {
        return codigoSeguranca;
    }

    /**
     * @param codigoSeguranca the codigoSeguranca to set
     */
    public void setCodigoSeguranca(String codigoSeguranca) {
        this.codigoSeguranca = codigoSeguranca;
    }

    /**
     * @return the bandeira
     */
    public int getBandeira() {
        return bandeira;
    }

    /**
     * @param bandeira the bandeira to set
     */
    public void setBandeira(int bandeira) {
        this.bandeira = bandeira;
    }

    public Integer getParcelas() {
        return parcelas;
    }

    public void setParcelas(Integer parcelas) {
        this.parcelas = parcelas;
    }

    /**
     * @return the mesValidade
     */
    public int getMesValidade() {
        if (!UteisValidacao.emptyString(this.getValidade())) {
            mesValidade = Integer.valueOf(this.getValidade().substring(0, 2));
        }
        return mesValidade;
    }

    public String getMesValidadeApre() {
        if (String.valueOf(mesValidade).length() == 1) {
            return '0' + String.valueOf(mesValidade);
        }
        return String.valueOf(mesValidade);
    }

    public String getValidadeMMYY(boolean comBarra) {
        String barra = comBarra ? "/" : "";
        String v = Formatador.formatarValorNumerico(Double.valueOf(this.getMesValidade()), "00")
                + barra
                + Formatador.formatarValorNumerico(Double.valueOf(this.getAnoValidade()), "00");

        return v;
    }

    public String getMesValidadeMM() {
        int mesValidade = this.getMesValidade();
        // Retorna o mês sem o zero à esquerda se for menor que 10
        if (mesValidade < 10) {
            return String.valueOf(mesValidade);
        }
        // Mantém o formato original para 11 e 12
        return String.format("%02d", mesValidade);
    }

    public String getValidadeMMYYYY(boolean comBarra) {
        String barra = comBarra ? "/" : "";
        String v = Formatador.formatarValorNumerico(Double.valueOf(this.getMesValidade()), "00")
                + barra
                + Formatador.formatarValorNumerico(Double.valueOf(this.getAnoValidade()), "0000");

        return v;
    }

    /**
     * @param mesValidade the mesValidade to set
     */
    public void setMesValidade(int mesValidade) {
        this.mesValidade = mesValidade;
    }

    /**
     * @return the anoValidade
     */
    public int getAnoValidade() {
        if (!UteisValidacao.emptyString(this.getValidade())) {
            if (this.getValidade().length() == 7) {
                anoValidade = Integer.valueOf(this.getValidade().substring(3, 7));
            } else if (this.getValidade().length() == 5) {
                anoValidade = Integer.valueOf("20" + this.getValidade().substring(3, 5));
            } else {
                return Integer.parseInt(this.getValidade().split("/")[1]);
            }
        }
        return anoValidade;
    }

    /**
     * @param anoValidade the anoValidade to set
     */
    public void setAnoValidade(int anoValidade) {
        this.anoValidade = anoValidade;
    }

    /**
     * @return the nomeTitular
     */
    public String getNomeTitular() {
        if (nomeTitular == null) {
            nomeTitular = "";
        }
        return nomeTitular;
    }

    /**
     * @param nomeTitular the nomeTitular to set
     */
    public void setNomeTitular(String nomeTitular) {
        this.nomeTitular = nomeTitular;
    }

    /**
     * @return the cpfCnpjPortador
     */
    public String getCpfCnpjPortador() {
        return cpfCnpjPortador;
    }

    /**
     * @param cpfCnpjPortador the cpfCnpjPortador to set
     */
    public void setCpfCnpjPortador(String cpfCnpjPortador) {
        this.cpfCnpjPortador = cpfCnpjPortador;
    }

    /**
     * @return the dataNascPortador
     */
    public Date getDataNascPortador() {
        return dataNascPortador;
    }

    /**
     * @param dataNascPortador the dataNascPortador to set
     */
    public void setDataNascPortador(Date dataNascPortador) {
        this.dataNascPortador = dataNascPortador;
    }

    /**
     * @return the nomeMaePortador
     */
    public String getNomeMaePortador() {
        return nomeMaePortador;
    }

    /**
     * @param nomeMaePortador the nomeMaePortador to set
     */
    public void setNomeMaePortador(String nomeMaePortador) {
        this.nomeMaePortador = nomeMaePortador;
    }

    /**
     * @param validade the validade to set
     */
    public void setValidade(String validade) {
        this.validade = validade;
    }

    /**
     * @return the validade
     */
    public String getValidade() {
        return validade;
    }

    public OperadorasExternasAprovaFacilEnum getBand() {
        return band;
    }

    public void setBand(OperadorasExternasAprovaFacilEnum band) {
        this.band = band;
    }

    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public String getValorDocumento() {
        return valorDocumento;
    }

    public void setValorDocumento(String valorDocumento) {
        this.valorDocumento = valorDocumento;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public List<MovParcelaVO> getListaParcelas() {
        if (listaParcelas == null) {
            listaParcelas = new ArrayList<MovParcelaVO>();
        }
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public boolean isTrocarCartao() {
        return trocarCartao;
    }

    public void setTrocarCartao(boolean trocarCartao) {
        this.trocarCartao = trocarCartao;
    }

    public Map<String, String> parseToMapPadraoCobreBem() throws Exception {
        HashMap<String, String> mapa = new HashMap();
        mapa.put(APF.ValorDoc, Double.toString(this.getValor()));
        mapa.put(APF.QtdParcelas, String.valueOf(this.getParcelas()));
        mapa.put(APF.Bandeira, this.getBand().getDescricao().toUpperCase());
        mapa.put(APF.IPComprador, this.getIp());
        mapa.put(APF.IPClientePacto, this.getIpClientePacto());
        mapa.put(APF.URLRequest, this.getUrl());
        mapa.put(APF.Moeda, "BRL");

        if (!UteisValidacao.emptyString(this.getTokenAragorn())) {
            mapa.put(APF.TokenAragorn, this.getTokenAragorn());
        }

        if (this.numeroDocumento != null) {
            mapa.put(APF.NumeroDoc, this.getNumeroDocumento());
        }

        if (this.getMesValidade() != 0) {
            mapa.put(APF.MesValidade, String.valueOf(this.getMesValidade()));
            mapa.put(APF.AnoValidade, String.valueOf(this.getAnoValidade()).substring(2));
        }

        if (this.getCodigoSeguranca() != null) {
            mapa.put(APF.CodSeguranca, this.getCodigoSeguranca());
        }

        if (this.getNomeTitular() != null) {
            mapa.put(APF.NomePortador, this.getNomeTitular());
        }

        if (this.getCpfCnpjPortador() != null) {
            mapa.put(APF.CPF, this.getCpfCnpjPortador());
        }

        if (this.getDataNascPortador() != null) {
            mapa.put(APF.DataNasc, Uteis.getDataAplicandoFormatacao(this.getDataNascPortador(), "yyyyMMdd"));
        }

        if (this.getNomeMaePortador() != null) {
            mapa.put(APF.NomeMae, this.getNomeMaePortador());
        }

        if (this.getTransacaoAnterior() != null) {
            mapa.put(APF.TransAnterior, this.getTransacaoAnterior());
        }

        if (this.getCodExternoTransacaoAnterior() != null) {
            mapa.put(APF.CodExternoTransacaoRetransmitida, this.getCodExternoTransacaoAnterior());
        }

        if (this.getTentativasRepescagem() != null) {
            mapa.put(APF.NumTentativasRepescagem, this.getTentativasRepescagem());
        }

        return mapa;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public String getTransacaoAnterior() {
        return transacaoAnterior;
    }

    public void setTransacaoAnterior(String transacaoAnterior) {
        this.transacaoAnterior = transacaoAnterior;
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public String getIpClientePacto() {
        return ipClientePacto;
    }

    public void setIpClientePacto(String ipClientePacto) {
        this.ipClientePacto = ipClientePacto;
    }

    public String getCodExternoTransacaoAnterior() {
        return codExternoTransacaoAnterior;
    }

    public void setCodExternoTransacaoAnterior(String codExternoTransacaoAnterior) {
        this.codExternoTransacaoAnterior = codExternoTransacaoAnterior;
    }

    public String getTentativasRepescagem() {
        return tentativasRepescagem;
    }

    public void setTentativasRepescagem(String tentativasRepescagem) {
        this.tentativasRepescagem = tentativasRepescagem;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getIdPessoaCartao() {
        return idPessoaCartao;
    }

    public void setIdPessoaCartao(Integer idPessoaCartao) {
        this.idPessoaCartao = idPessoaCartao;
    }

    public String getChaveAPI() {
        return chaveAPI;
    }

    public void setChaveAPI(String chaveAPI) {
        this.chaveAPI = chaveAPI;
    }

    public AdquirenteMaxiPagoEnum getAdquirenteMaxiPago() {
        return adquirenteMaxiPago;
    }

    public void setAdquirenteMaxiPago(AdquirenteMaxiPagoEnum adquirenteMaxiPago) {
        this.adquirenteMaxiPago = adquirenteMaxiPago;
    }

    public String getTokenExterno() {
        if (tokenExterno == null) {
            tokenExterno = "";
        }
        return tokenExterno;
    }

    public void setTokenExterno(String tokenExterno) {
        this.tokenExterno = tokenExterno;
    }

    public void preencherValor() {
        Double soma = 0.0;
        for (MovParcelaVO parcela : getListaParcelas()) {
            soma += parcela.getValorParcela();
        }
        if (this.isAplicarDesconto() && !UteisValidacao.emptyNumber(getDesconto())) {
            setValor(soma - Uteis.arredondarForcando2CasasDecimais(getDesconto()));
        } else {
            setValor(soma);
        }
    }

    public InstalmentTypeInstlmtTp getTipoParcelamentoStone() {
        return tipoParcelamentoStone;
    }

    public void setTipoParcelamentoStone(InstalmentTypeInstlmtTp tipoParcelamentoStone) {
        this.tipoParcelamentoStone = tipoParcelamentoStone;
    }

    public String getTokenAragorn() {
        if (tokenAragorn == null) {
            tokenAragorn = "";
        }
        return tokenAragorn;
    }

    public void setTokenAragorn(String tokenAragorn) {
        this.tokenAragorn = tokenAragorn;
    }

    public String getTokenVindi() {
        return tokenVindi;
    }

    public void setTokenVindi(String tokenVindi) {
        this.tokenVindi = tokenVindi;
    }

    public TipoTransacaoEnum getTipoTransacaoEnum() {
        if (tipoTransacaoEnum == null) {
            tipoTransacaoEnum = TipoTransacaoEnum.NENHUMA;
        }
        return tipoTransacaoEnum;
    }

    public void setTipoTransacaoEnum(TipoTransacaoEnum tipoTransacaoEnum) {
        this.tipoTransacaoEnum = tipoTransacaoEnum;
    }

    public String getIdCardMundiPagg() {
        if (idCardMundiPagg == null) {
            idCardMundiPagg = "";
        }
        return idCardMundiPagg;
    }

    public void setIdCardMundiPagg(String idCardMundiPagg) {
        this.idCardMundiPagg = idCardMundiPagg;
    }

    public String getIdCardPagarMe() {
        if (idCardPagarMe == null) {
            idCardPagarMe = "";
        }
        return idCardPagarMe;
    }

    public void setIdCardPagarMe(String idCardPagarMe) {
        this.idCardPagarMe = idCardPagarMe;
    }

    public boolean isUsarIdVindiCobranca() {
        return usarIdVindiCobranca;
    }

    public void setUsarIdVindiCobranca(boolean usarIdVindiCobranca) {
        this.usarIdVindiCobranca = usarIdVindiCobranca;
    }

    public boolean isSomenteUmEnvioCartaoTentativa() {
        return somenteUmEnvioCartaoTentativa;
    }

    public void setSomenteUmEnvioCartaoTentativa(boolean somenteUmEnvioCartaoTentativa) {
        this.somenteUmEnvioCartaoTentativa = somenteUmEnvioCartaoTentativa;
    }

    public boolean isTransacaoPresencial() {
        return transacaoPresencial;
    }

    public void setTransacaoPresencial(boolean transacaoPresencial) {
        this.transacaoPresencial = transacaoPresencial;
    }

    public String getGatewayTokenVindi() {
        if (gatewayTokenVindi == null) {
            gatewayTokenVindi = "";
        }
        return gatewayTokenVindi;
    }

    public void setGatewayTokenVindi(String gatewayTokenVindi) {
        this.gatewayTokenVindi = gatewayTokenVindi;
    }

    public boolean isRetentativaManual() {
        return retentativaManual;
    }

    public void setRetentativaManual(boolean retentativaManual) {
        this.retentativaManual = retentativaManual;
    }

    public boolean isTransacaoVerificarCartao() {
        return transacaoVerificarCartao;
    }

    public void setTransacaoVerificarCartao(boolean transacaoVerificarCartao) {
        this.transacaoVerificarCartao = transacaoVerificarCartao;
    }

    public OrigemCobrancaEnum getOrigemCobranca() {
        if (origemCobranca == null) {
            origemCobranca = OrigemCobrancaEnum.NENHUM;
        }
        return origemCobranca;
    }

    public void setOrigemCobranca(OrigemCobrancaEnum origemCobranca) {
        this.origemCobranca = origemCobranca;
    }

    public boolean isAsync() {
        return async;
    }

    public void setAsync(boolean async) {
        this.async = async;
    }

    public String getAnoValidadeYY() {
        String ano = "";
        if (this.getValidade().length() == 5) {
            ano = this.getValidade().substring(3, 5);
        } else if (this.getValidade().length() == 7) {
            ano = this.getValidade().substring(5, 7);
        } else {
            ano = String.valueOf(this.getAnoValidade()).substring(5, 7);
        }
        return ano;
    }

    public String getAnoValidadeYYYY() {
        String ano = "";
        if (this.getValidade().length() == 5) {
            ano = ("20" + this.getValidade().substring(3, 5));
        } else if (this.getValidade().length() == 7) {
            ano = this.getValidade().substring(3, 7);
        } else {
            ano = String.valueOf(this.getAnoValidade());
        }
        return ano;
    }

    public boolean isEditandoAutorizacao() {
        return editandoAutorizacao;
    }

    public void setEditandoAutorizacao(boolean editandoAutorizacao) {
        this.editandoAutorizacao = editandoAutorizacao;
    }

    public Double getDesconto() {
        if (desconto == null) {
            desconto = 0.0;
        }
        return desconto;
    }

    public void setDesconto(Double desconto) {
        this.desconto = desconto;
    }

    public boolean isAplicarDesconto() {
        return aplicarDesconto;
    }

    public void setAplicarDesconto(boolean aplicarDesconto) {
        this.aplicarDesconto = aplicarDesconto;
    }

    public boolean isUsarTokenCieloCobranca() {
        return usarTokenCieloCobranca;
    }

    public void setUsarTokenCieloCobranca(boolean usarTokenCieloCobranca) {
        this.usarTokenCieloCobranca = usarTokenCieloCobranca;
    }

    public String getTokenCielo() {
        return tokenCielo;
    }

    public void setTokenCielo(String tokenCielo) {
        this.tokenCielo = tokenCielo;
    }

    public boolean isVerificacaoZeroDollar() {
        return isVerificacaoZeroDollar;
    }

    public void setVerificacaoZeroDollar(boolean verificacaoZeroDollar) {
        isVerificacaoZeroDollar = verificacaoZeroDollar;
    }

    public String getTokenPagoLivre() {
        if (tokenPagoLivre == null) {
            tokenPagoLivre = "";
        }
        return tokenPagoLivre;
    }

    public void setTokenPagoLivre(String tokenPagoLivre) {
        this.tokenPagoLivre = tokenPagoLivre;
    }

    public String getIdCardStoneV5() {
        if(idCardStoneV5 == null) {
            return "";
        }
        return idCardStoneV5;
    }

    public void setIdCardStoneV5(String idCardStoneV5) {
        this.idCardStoneV5 = idCardStoneV5;
    }
}
