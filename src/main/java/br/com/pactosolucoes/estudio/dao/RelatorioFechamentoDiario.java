package br.com.pactosolucoes.estudio.dao;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estudio.enumeradores.StatusEnum;
import br.com.pactosolucoes.estudio.interfaces.RelatorioFechamentoDiarioInterfaceFacade;
import br.com.pactosolucoes.estudio.modelo.PacoteVO;
import br.com.pactosolucoes.estudio.modelo.RelatorioFechamentoDiarioHorarioVO;
import br.com.pactosolucoes.estudio.modelo.RelatorioFechamentoDiarioVO;
import br.com.pactosolucoes.estudio.util.Validador;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;

/**
 *
 * <AUTHOR> - GeoInova Soluções
 */
public class RelatorioFechamentoDiario extends SuperEntidade implements RelatorioFechamentoDiarioInterfaceFacade {

    // SESSÕES AGENDADAS
    @Override
    public List<RelatorioFechamentoDiarioVO> listarSessoesAgendadasPorStatus(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, String idStatus, Integer idUsuario, Integer idProfissional) throws Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = "SELECT agenda.id_agenda, agenda.data_aula, agenda.hora_inicio, agenda.hora_termino, pessoaCliente.nome AS desc_cliente, pessoaColab.nome AS desc_colab, "
                + "produto.descricao AS desc_produto, ambiente.descricao AS desc_amb, agenda.status, tipo.descricao AS descTipoHorario, cliente.codigo as cliente, colab.codigo as colaborador "
                + "FROM sch_estudio.agenda agenda "
                + "INNER JOIN sch_estudio.tipo_horario tipo ON tipo.id_tipo_horario = agenda.id_tipo_horario "
                + "INNER JOIN ambiente ambiente ON ambiente.codigo = agenda.id_ambiente "
                + "INNER JOIN produto produto ON produto.codigo = agenda.id_produto "
                + "INNER JOIN colaborador colab ON colab.codigo = agenda.id_colaborador "
                + "INNER JOIN cliente cliente ON cliente.codigo = agenda.id_cliente "
                + "INNER JOIN pessoa pessoaCliente ON pessoaCliente.codigo = cliente.pessoa "
                + "INNER JOIN pessoa pessoaColab ON pessoaColab.codigo = colab.pessoa "
                + "WHERE agenda.id_empresa = ?" + filtrosAgenda(dataInicial, dataFinal, null, null, idStatus, idUsuario, idProfissional, horaInicial, horaFinal)
                + "ORDER BY agenda.data_aula, agenda.hora_inicio, agenda.hora_termino, pessoaCliente.nome, produto.descricao";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_AGENDA);
            listaRelatorio.add(obj);
        }

        return listaRelatorio;
    }

    public String filtrosAgenda(Date dataInicialAula, Date dataFinalAula, Date dataInicialLanc, Date dataFinalLanc, String idStatus, int idUsuario, int idProfissional, Time horaInicial, Time horaFinal) {
        String whereData = "";
        if (Validador.isValidaObject(dataInicialAula) && Validador.isValidaObject(dataFinalAula)) {
            whereData = " AND (agenda.data_aula >=   '" + Formatador.formatarData(dataInicialAula, "yyyy-MM-dd")
                    + "' AND agenda.data_aula<= '" + Formatador.formatarData(dataFinalAula, "yyyy-MM-dd") + "') ";
        }

        String whereDataLancamento = "";
        if (Validador.isValidaObject(dataInicialLanc) && Validador.isValidaObject(dataFinalLanc)) {
            whereDataLancamento = " AND (agenda.data_lancamento >= '"
                    + Formatador.formatarData(dataInicialLanc, "yyyy-MM-dd") + " 00:00:00.000' "
                    + "AND agenda.data_lancamento <='" + Formatador.formatarData(dataFinalLanc, "yyyy-MM-dd") + " 23:59:59.999') ";
        }

        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String whereHora = "";
        if (Validador.isValidaObject(horaInicial) && Validador.isValidaObject(horaFinal)) {
            whereHora = " AND (agenda.hora_inicio BETWEEN '" + format.format(horaInicial) + "' AND '" + format.format(horaFinal) + "') ";
        }

        String whereStatus = "";
        if (Validador.isValidaString(idStatus)) {
            // Validar status para prevenir SQL injection
            if (!Uteis.isValidStringValue(idStatus)) {
                throw new SecurityException("Status contém caracteres inválidos");
            }
            // Escapar aspas simples para prevenir SQL injection
            String statusEscapado = idStatus.replace("'", "''");
            whereStatus = " AND (agenda.status = '" + statusEscapado + "') ";
        }

        String whereUsuario = "";
        if (Validador.isValidaInteger(idUsuario)) {
            whereUsuario = " AND (agenda.id_usuario_lancamento = '" + idUsuario + "') ";
        }

        String whereProfissional = "";
        if (Validador.isValidaInteger(idProfissional)) {
            whereProfissional = " AND (agenda.id_colaborador = '" + idProfissional + "') ";
        }
        return whereData + whereDataLancamento + whereHora + whereStatus + whereUsuario + whereProfissional;
    }

    public String filtrosVenda(Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {
        String whereData = "";
        if (Validador.isValidaObject(dataInicial) && Validador.isValidaObject(dataFinal)) {
            whereData = " AND  (venda.dataregistro  >=  '" + Uteis.getDataHoraJDBC(dataInicial, "00:00:00") + "' AND venda.dataregistro <='" + Uteis.getDataHoraJDBC(dataFinal, "23:59:59") + "')";
        }

        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String whereHora = "";
        if (Validador.isValidaObject(horaInicial) && Validador.isValidaObject(horaFinal)) {
            whereHora = " AND (agenda.hora_inicio BETWEEN '" + format.format(horaInicial) + "' AND '" + format.format(horaFinal) + "') ";
        }

        String whereUsuario = "";
        if (Validador.isValidaInteger(idUsuario)) {
            whereUsuario = " AND (venda.responsavel = " + idUsuario + ") ";
        }
        return whereData + whereHora + whereUsuario;
    }

    @Override
    public List<RelatorioFechamentoDiarioVO> listarSessoesAgendadasTotalizador(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = "SELECT COUNT(status), STATUS, SUM(COUNT(STATUS)) OVER (ORDER BY status) "
                + "FROM sch_estudio.agenda agenda "
                + "WHERE agenda.id_empresa = ?" + filtrosAgenda(dataInicial, dataFinal, null, null, "", idUsuario, idProfissional, horaInicial, horaFinal)
                + "GROUP BY agenda.status";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            listaRelatorio.add(obj);
        }

        return listaRelatorio;
    }

    @Override
    public Integer contadorSessoesCliente(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        String sql = "SELECT COUNT(*) "
                + "FROM ( "
                + "SELECT DISTINCT ON (agenda.id_cliente) agenda.id_cliente "
                + "FROM sch_estudio.agenda agenda "
                + "WHERE agenda.id_empresa = ? " + filtrosAgenda(dataInicial, dataFinal, null, null, "", idUsuario, idProfissional, horaInicial, horaFinal)
                + ") AS SESSOES_CLIENTE";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    // VENDA
    @Override
    public Integer contadorVenda(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {
        String sql = "SELECT COUNT(*) "
                + "FROM ( "
                + "SELECT DISTINCT ON (agvenda.id_vendaavulsa) agvenda.id_vendaavulsa "
                + "FROM sch_estudio.agenda agenda "
                + "LEFT JOIN sch_estudio.agenda_venda agvenda ON agvenda.id_agenda = agenda.id_agenda "
                + "LEFT JOIN vendaavulsa venda ON venda.codigo = agvenda.id_vendaavulsa "
                + "WHERE agenda.id_empresa = ? " + filtrosVenda(dataInicial, dataFinal, horaInicial, horaFinal, idUsuario)
                + ") AS QUANT";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    @Override
    public Integer contadorPacotesVendidos(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {
        String sql = montarSqlComumConsultaPacotes(idEmpresa, dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, true);
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    @Override
    public List<RelatorioFechamentoDiarioVO> consultarPacotesVendidos(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {
        String sql = montarSqlComumConsultaPacotes(idEmpresa, dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, false);
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet resultDados = ps.executeQuery();
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_PACOTE);
            if (listaRelatorio.isEmpty()) {
                listaRelatorio.add(obj);
            } else {
                if (listaRelatorio.contains(obj)) {
                    for (RelatorioFechamentoDiarioVO re : listaRelatorio) {
                        if (re.getPacoteVO().getCodigo().intValue() == obj.getPacoteVO().getCodigo().intValue()) {
                            re.getPacoteVO().getListaRelFechamDiario().addAll(obj.getPacoteVO().getListaRelFechamDiario());
                        }
                    }
                } else {
                    listaRelatorio.add(obj);
                }
            }
        }
        return listaRelatorio;
    }

    public String montarSqlComumConsultaPacotes(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, boolean contar) throws Exception {
        String sql = "select  ";
        sql += contar ? " count(distinct(pac.id_pacote)) " : "  distinct(it.codigo),pac.id_pacote,pac.valor_total, pac.generico, pac.titulo,venda.nomecomprador, venda.dataregistro,produto.descricao, it.quantidade, it.valorparcial, venda.tipocomprador ";
        sql += "from sch_estudio.pacote  pac "
                + "inner join itemvendaavulsa it on it.pacote = pac.id_pacote "
                + "inner join vendaavulsa venda on venda.codigo = it.vendaavulsa "
                + "left JOIN sch_estudio.agenda_venda agendavenda on agendavenda.id_vendaavulsa = venda.codigo "
                + "left join sch_estudio.agenda agenda on agenda.id_agenda = agendavenda.id_agenda "
                + "left join produto on it.produto = produto.codigo "
                + "WHERE venda.empresa = " + idEmpresa + filtrosVenda(dataInicial, dataFinal, horaInicial, horaFinal, idUsuario);

        return sql;
    }

    @Override
    public Integer contadorClienteNovo(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        String sql = "SELECT COUNT(*) "
                + "FROM ( "
                + "SELECT DISTINCT ON (agenda.id_cliente) agenda.id_cliente "
                + "FROM sch_estudio.agenda agenda "
                + "INNER JOIN sch_estudio.agenda_venda agvenda ON agvenda.id_agenda = agenda.id_agenda "
                + "INNER JOIN vendaavulsa venda ON venda.codigo = agvenda.id_vendaavulsa "
                + "INNER JOIN cliente cliente ON cliente.codigo = venda.cliente "
                + "INNER JOIN pessoa pessoa ON pessoa.codigo = cliente.pessoa "
                + "WHERE agenda.id_empresa = ? AND (pessoa.datacadastro BETWEEN ? AND ?) "
                + filtrosAgenda(dataInicial, dataFinal, null, null, "", idUsuario, idProfissional, horaInicial, horaFinal)
                + ") AS CLIENTE";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ps.setDate(2, dataInicial);
        ps.setDate(3, dataFinal);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    @Override
    public List<RelatorioFechamentoDiarioVO> listarVenda(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = "SELECT distinct(movproduto.codigo),venda.codigo as codigoVenda, venda.dataregistro, venda.nomeComprador, "
                + "produto.descricao, movproduto.quantidade, venda.tipocomprador, movproduto.totalfinal as valorTotal, "
                + "produto.codigo as codigoProduto, pacote.descricao as pacoteDescricao "
                + "FROM vendaavulsa venda "
                + "left JOIN movparcela parcela ON parcela.vendaavulsa = venda.codigo "
                + "left JOIN movprodutoparcela produtoparcela ON produtoparcela.movparcela = parcela.codigo "
                + "left JOIN movproduto movproduto ON movproduto.codigo = produtoparcela.movproduto "
                + "left join pessoa pessoa on movproduto.pessoa = pessoa.codigo "
                + "Left join cliente cliente on cliente.pessoa = pessoa.codigo "
                + "left join colaborador col on col.pessoa = pessoa.codigo "
                + "left JOIN produto produto ON produto.codigo = movproduto.produto "
                + "left join itemvendaavulsa it on it.vendaavulsa = venda.codigo "
                + "left JOIN sch_estudio.pacote pacote ON pacote.id_pacote = it.pacote "
                + "left JOIN sch_estudio.agenda_venda agvenda ON agvenda.id_vendaavulsa = venda.codigo "
                + "left join sch_estudio.agenda agenda on agvenda.id_agenda = agenda.id_agenda "
                + "WHERE  produto.tipoproduto = 'SS' AND venda.empresa = " + idEmpresa + filtrosVenda(dataInicial, dataFinal, horaInicial, horaFinal, idUsuario)
                + "ORDER BY venda.dataregistro, venda.nomecomprador ";
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_VENDACOMPACOTE);
            listaRelatorio.add(obj);
        }

        return listaRelatorio;
    }
    
    @Override
    public List<RelatorioFechamentoDiarioVO> listarRecebidos(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = "SELECT distinct(movproduto.codigo),venda.codigo as codigoVenda, venda.dataregistro, venda.nomeComprador, "
            	+ "produto.descricao, movproduto.quantidade, venda.tipocomprador, movproduto.totalfinal as valorTotal, "
            	+ "produto.codigo as codigoProduto, pacote.descricao as pacoteDescricao "
                + "FROM vendaavulsa venda "
                + "left JOIN movparcela parcela ON parcela.vendaavulsa = venda.codigo "
                + "left JOIN movprodutoparcela produtoparcela ON produtoparcela.movparcela = parcela.codigo "
                + "left JOIN movproduto movproduto ON movproduto.codigo = produtoparcela.movproduto "
                + "left join pessoa pessoa on movproduto.pessoa = pessoa.codigo "
                + "Left join cliente cliente on cliente.pessoa = pessoa.codigo "
                + "left join colaborador col on col.pessoa = pessoa.codigo "
                + "left JOIN produto produto ON produto.codigo = movproduto.produto "
                + "left join itemvendaavulsa it on it.vendaavulsa = venda.codigo "
                + "left JOIN sch_estudio.pacote pacote ON pacote.id_pacote = it.pacote "
                + "left JOIN sch_estudio.agenda_venda agvenda ON agvenda.id_vendaavulsa = venda.codigo "
                + "left join sch_estudio.agenda agenda on agvenda.id_agenda = agenda.id_agenda "
                + "WHERE  produto.tipoproduto = 'SS' AND parcela.situacao = 'PG' AND venda.empresa = " + idEmpresa + filtrosVenda(dataInicial, dataFinal, horaInicial, horaFinal, idUsuario)
                + "ORDER BY venda.dataregistro, venda.nomecomprador ";
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_VENDACOMPACOTE);
            listaRelatorio.add(obj);
        }

        return listaRelatorio;
    }
    
    @Override
    public List<RelatorioFechamentoDiarioVO> listarRecebidosDCO(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = "SELECT distinct(movproduto.codigo),venda.codigo as codigoVenda, venda.dataregistro, venda.nomeComprador, "
            	+ "produto.descricao, movproduto.quantidade, venda.tipocomprador, (pagamento.valor*produtoparcela.valorpago/parcela.valorparcela) as valorTotal, "
            	+ "produto.codigo as codigoProduto, pacote.descricao as pacoteDescricao "
                + "FROM vendaavulsa venda "
                + "left JOIN movparcela parcela ON parcela.vendaavulsa = venda.codigo "
                + "left JOIN movprodutoparcela produtoparcela ON produtoparcela.movparcela = parcela.codigo "
                + "left JOIN recibopagamento recibo ON recibo.codigo = produtoparcela.recibopagamento "
                + "left JOIN movpagamento pagamento ON pagamento.recibopagamento = recibo.codigo "
                + "left JOIN formapagamento formapagamento ON pagamento.formapagamento = formapagamento.codigo "
                + "left JOIN movproduto movproduto ON movproduto.codigo = produtoparcela.movproduto "
                + "left join pessoa pessoa on movproduto.pessoa = pessoa.codigo "
                + "Left join cliente cliente on cliente.pessoa = pessoa.codigo "
                + "left join colaborador col on col.pessoa = pessoa.codigo "
                + "left JOIN produto produto ON produto.codigo = movproduto.produto "
                + "left join itemvendaavulsa it on it.vendaavulsa = venda.codigo "
                + "left JOIN sch_estudio.pacote pacote ON pacote.id_pacote = it.pacote "
                + "left JOIN sch_estudio.agenda_venda agvenda ON agvenda.id_vendaavulsa = venda.codigo "
                + "left join sch_estudio.agenda agenda on agvenda.id_agenda = agenda.id_agenda "
                + "WHERE  produto.tipoproduto = 'SS' AND formapagamento.tipoformapagamento = 'CC' AND pagamento.credito = FALSE "
                + "AND venda.empresa = " + idEmpresa + filtrosVenda(dataInicial, dataFinal, horaInicial, horaFinal, idUsuario)
                + "ORDER BY venda.dataregistro, venda.nomecomprador ";
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_VENDACOMPACOTE);
            listaRelatorio.add(obj);
        }

        return listaRelatorio;
    }

    @Override
    public List<RelatorioFechamentoDiarioVO> listarRecebidosCCO(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = "SELECT distinct(movproduto.codigo),venda.codigo as codigoVenda, venda.dataregistro, venda.nomeComprador, "
        	+ "produto.descricao, movproduto.quantidade, venda.tipocomprador, (pagamento.valor*produtoparcela.valorpago/parcela.valorparcela) as valorTotal, "
        	+ "produto.codigo as codigoProduto, pacote.descricao as pacoteDescricao "
            + "FROM vendaavulsa venda "
            + "left JOIN movparcela parcela ON parcela.vendaavulsa = venda.codigo "
            + "left JOIN movprodutoparcela produtoparcela ON produtoparcela.movparcela = parcela.codigo "
            + "left JOIN recibopagamento recibo ON recibo.codigo = produtoparcela.recibopagamento "
            + "left JOIN movpagamento pagamento ON pagamento.recibopagamento = recibo.codigo "
            + "left JOIN formapagamento formapagamento ON pagamento.formapagamento = formapagamento.codigo "
            + "left JOIN movproduto movproduto ON movproduto.codigo = produtoparcela.movproduto "
            + "left join pessoa pessoa on movproduto.pessoa = pessoa.codigo "
            + "Left join cliente cliente on cliente.pessoa = pessoa.codigo "
            + "left join colaborador col on col.pessoa = pessoa.codigo "
            + "left JOIN produto produto ON produto.codigo = movproduto.produto "
            + "left join itemvendaavulsa it on it.vendaavulsa = venda.codigo "
            + "left JOIN sch_estudio.pacote pacote ON pacote.id_pacote = it.pacote "
            + "left JOIN sch_estudio.agenda_venda agvenda ON agvenda.id_vendaavulsa = venda.codigo "
            + "left join sch_estudio.agenda agenda on agvenda.id_agenda = agenda.id_agenda "
            + "WHERE  produto.tipoproduto = 'SS'  AND pagamento.credito = TRUE "
            + "AND venda.empresa = " + idEmpresa + filtrosVenda(dataInicial, dataFinal, horaInicial, horaFinal, idUsuario)
            + "ORDER BY venda.dataregistro, venda.nomecomprador ";
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_VENDACOMPACOTE);
            listaRelatorio.add(obj);
        }

        return listaRelatorio;
    }

    @Override
    public List<RelatorioFechamentoDiarioVO> listarFaturadosNaoRecebidos(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = "SELECT distinct(movproduto.codigo),venda.codigo as codigoVenda, venda.dataregistro, venda.nomeComprador, "
            	+ "produto.descricao, movproduto.quantidade, venda.tipocomprador, movproduto.totalfinal as valorTotal, "
            	+ "produto.codigo as codigoProduto, pacote.descricao as pacoteDescricao "
                + "FROM vendaavulsa venda "
                + "left JOIN movparcela parcela ON parcela.vendaavulsa = venda.codigo "
                + "left JOIN movprodutoparcela produtoparcela ON produtoparcela.movparcela = parcela.codigo "
                + "left JOIN movproduto movproduto ON movproduto.codigo = produtoparcela.movproduto "
                + "left JOIN pessoa pessoa ON movproduto.pessoa = pessoa.codigo "
                + "left JOIN cliente cliente ON cliente.pessoa = pessoa.codigo "
                + "left JOIN colaborador col ON col.pessoa = pessoa.codigo "
                + "left JOIN produto produto ON produto.codigo = movproduto.produto "
                + "left JOIN itemvendaavulsa it ON it.vendaavulsa = venda.codigo "
                + "left JOIN sch_estudio.pacote pacote ON pacote.id_pacote = it.pacote "
                + "left JOIN sch_estudio.agenda_venda agvenda ON agvenda.id_vendaavulsa = venda.codigo "
                + "left JOIN sch_estudio.agenda agenda ON agvenda.id_agenda = agenda.id_agenda "
                + "WHERE  produto.tipoproduto = 'SS' AND parcela.situacao != 'PG' AND venda.empresa = "
                + idEmpresa + filtrosVenda(dataInicial, dataFinal, horaInicial, horaFinal, idUsuario)
                + "ORDER BY venda.dataregistro, venda.nomecomprador ";
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_VENDACOMPACOTE);
            listaRelatorio.add(obj);
        }

        return listaRelatorio;
    }
    
    // AGENDAMENTOS LANÇADOS
    @Override
    public Integer contadorAgendamentos(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        String sql = "SELECT COUNT(agenda.id_agenda) "
                + "FROM sch_estudio.agenda agenda "
                + "WHERE agenda.id_empresa = ? " + filtrosAgenda(null, null, dataInicial, dataFinal, "", idUsuario, idProfissional, horaInicial, horaFinal);
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    @Override
    public Integer contadorAgendamentosCliente(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        String sql = "SELECT COUNT(*) FROM ( "
                + "SELECT DISTINCT ON (agenda.id_cliente) agenda.id_cliente "
                + "FROM sch_estudio.agenda agenda "
                + "WHERE agenda.id_empresa = ?  " + filtrosAgenda(null, null, dataInicial, dataFinal, "", idUsuario, idProfissional, horaInicial, horaFinal)
                + ") AS CLIENTE";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    @Override
    public Integer contadorAgendamentosParaMes(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        String sql = "SELECT COUNT(agenda.id_agenda) "
                + "FROM sch_estudio.agenda agenda "
                + "WHERE agenda.id_empresa = ?  "
                + filtrosAgenda(Uteis.getDataJDBC(Uteis.obterPrimeiroDiaMes(dataInicial)),
                Uteis.getDataJDBC(Uteis.obterUltimoDiaMes(dataInicial)), dataInicial, dataFinal, "", idUsuario, idProfissional, horaInicial, horaFinal);
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    @Override
    public Integer contadorAgendamentosParaPeriodo(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        String sql = "SELECT COUNT(agenda.id_agenda) "
                + "FROM sch_estudio.agenda agenda "
                + "WHERE agenda.id_empresa = ? "
                + filtrosAgenda(dataInicial, dataFinal, dataInicial, dataFinal, "", idUsuario, idProfissional, horaInicial, horaFinal);
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    @Override
    public Integer contadorAgendamentosRetroativo(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {

        String whereUsuario = "";
        if (Validador.isValidaInteger(idUsuario)) {
            whereUsuario = " AND (agenda.id_usuario_lancamento = '" + idUsuario + "') ";
        }

        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String whereHora = "";
        if (Validador.isValidaObject(horaInicial) && Validador.isValidaObject(horaFinal)) {
            whereHora = " AND (agenda.hora_inicio BETWEEN '" + format.format(horaInicial) + "' AND '" + format.format(horaFinal) + "') ";
        }

        String datas;
        datas = " AND (agenda.data_aula < '" + Formatador.formatarData(dataInicial, "yyyy-MM-dd") + "')";

        String sql = "SELECT COUNT(agenda.id_agenda) "
                + "FROM sch_estudio.agenda agenda "
                + "WHERE agenda.id_empresa = ? AND (agenda.data_lancamento::date <= ? AND agenda.data_lancamento::date >= ?) " + whereHora + whereUsuario + datas;
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ps.setDate(2, dataInicial);
        ps.setDate(3, dataFinal);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    @Override
    public Integer contadorAgendamentosParaProximoMes(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws Exception {

        String whereUsuario = "";
        if (Validador.isValidaInteger(idUsuario)) {
            whereUsuario = " AND (agenda.id_usuario_lancamento = '" + idUsuario + "') ";
        }

        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String whereHora = "";
        if (Validador.isValidaObject(horaInicial) && Validador.isValidaObject(horaFinal)) {
            whereHora = " AND (agenda.hora_inicio BETWEEN '" + format.format(horaInicial) + "' AND '" + format.format(horaFinal) + "') ";
        }

        String datas;
        datas = " AND (agenda.data_aula > '" + Formatador.formatarData(Uteis.obterUltimoDiaMes(dataFinal), "yyyy-MM-dd") + "')";

        String sql = "SELECT COUNT(agenda.id_agenda) "
                + "FROM sch_estudio.agenda agenda "
                + "WHERE agenda.id_empresa = ? AND (agenda.data_lancamento::date <= ? AND agenda.data_lancamento::date >= ?) " + whereHora + whereUsuario + datas;
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ps.setDate(2, dataInicial);
        ps.setDate(3, dataFinal);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    @Override
    public List<RelatorioFechamentoDiarioVO> listarAgendamentosParaPeriodo(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = "SELECT DISTINCT(agenda.id_agenda), agenda.data_aula, agenda.data_lancamento, agenda.hora_inicio, "
                + "agenda.hora_termino, pessoaCliente.nome AS desc_cliente, pessoaColab.nome AS desc_colab, "
                + "produto.descricao AS desc_produto, produto.valorfinal AS valortotal, ambiente.descricao AS desc_amb, agenda.status, "
                + "tipo.descricao AS descTipoHorario, usuario.username, cliente.codigo as cliente, colab.codigo as colaborador, pacote.titulo  "
                + "FROM sch_estudio.agenda agenda "
                + "left JOIN sch_estudio.tipo_horario tipo ON tipo.id_tipo_horario = agenda.id_tipo_horario "
                + "INNER JOIN ambiente ambiente ON ambiente.codigo = agenda.id_ambiente "
                + "INNER JOIN produto produto ON produto.codigo = agenda.id_produto "
                + "INNER JOIN usuario usuario ON usuario.codigo = agenda.id_usuario_lancamento "
                + "INNER JOIN colaborador colab ON colab.codigo = agenda.id_colaborador "
                + "INNER JOIN cliente cliente ON cliente.codigo = agenda.id_cliente "
                + "INNER JOIN pessoa pessoaCliente ON pessoaCliente.codigo = cliente.pessoa "
                + "INNER JOIN pessoa pessoaColab ON pessoaColab.codigo = colab.pessoa "
                + "left join  sch_estudio.agenda_venda venda on venda.id_agenda = agenda.id_agenda "
                + "left join itemvendaavulsa item on item.vendaavulsa = venda.id_vendaavulsa "
                + "left join sch_estudio.pacote pacote on pacote.id_pacote = item.pacote "
                + "WHERE agenda.id_empresa = ? " + filtrosAgenda(dataInicial, dataFinal, dataInicial, dataFinal, "", idUsuario, idProfissional, horaInicial, horaFinal)
                + "ORDER BY agenda.data_lancamento";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();

        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_USUARIO);
            listaRelatorio.add(obj);
        }

        return listaRelatorio;
    }

    // OUTRAS PENDENCIAS
    @Override
    public Integer contadorOutrasAFaturar(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        String sql = montarSqlComumConsultaOutrasAFaturar(idEmpresa, dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional, true);
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    public String montarSqlComumConsultaOutrasAFaturar(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional, boolean contar) {
        String sql = " SELECT";

        sql += contar ? " COUNT(agenda.id_agenda)" : " agenda.id_agenda, agenda.data_aula,"
            + " agenda.hora_inicio, agenda.hora_termino, pessoaCliente.nome AS desc_cliente,"
            + " pessoaColab.nome AS desc_colab, produto.descricao AS desc_produto,"
            + " ambiente.descricao AS desc_amb, agenda.status, tipo.descricao AS descTipoHorario,"
            + " usuario.username, agenda.data_lancamento, cliente.codigo AS cliente,"
            + " colab.codigo AS colaborador, pacote.titulo, produto.valorfinal AS valortotal";

        sql += " FROM sch_estudio.agenda agenda"
            + " LEFT JOIN sch_estudio.tipo_horario tipo ON tipo.id_tipo_horario = agenda.id_tipo_horario"
            + " LEFT JOIN ambiente ambiente ON ambiente.codigo = agenda.id_ambiente"
            + " LEFT JOIN produto produto ON produto.codigo = agenda.id_produto"
            + " LEFT JOIN colaborador colab ON colab.codigo = agenda.id_colaborador"
            + " LEFT JOIN cliente ON cliente.codigo = agenda.id_cliente"
            + " LEFT JOIN pessoa ON pessoa.codigo = cliente.pessoa"
            + " LEFT JOIN usuario usuario ON usuario.codigo = agenda.id_usuario_lancamento"
            + " LEFT JOIN pessoa pessoaCliente ON pessoaCliente.codigo = cliente.pessoa"
            + " LEFT JOIN pessoa pessoaColab ON pessoaColab.codigo = colab.pessoa"
            + " INNER JOIN sch_estudio.agenda_faturar faturar ON faturar.id_agenda = agenda.id_agenda"
            + " LEFT JOIN  sch_estudio.agenda_venda venda ON venda.id_agenda = agenda.id_agenda"
            + " LEFT JOIN itemvendaavulsa item ON item.vendaavulsa = venda.id_vendaavulsa"
            + " LEFT JOIN sch_estudio.pacote pacote ON pacote.id_pacote = item.pacote"
            + " WHERE agenda.id_empresa = ? " + filtrosAgenda(null, null, dataInicial, dataFinal, "", idUsuario, idProfissional, horaInicial, horaFinal);
        
        return sql;
    }

    public List<RelatorioFechamentoDiarioVO> consultarOutrasAFaturar(
            Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial,
            Time horaFinal, Integer idUsuario, Integer idProfissional) throws SQLException, Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = montarSqlComumConsultaOutrasAFaturar(idEmpresa, dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional, false);
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_USUARIO);
            listaRelatorio.add(obj);
        }
        return listaRelatorio;
    }

    @Override
    public Integer contadorOutrasAAgendar(Integer idEmpresa, Date dataInicial, Date dataFinal, Integer idUsuario) throws Exception {
        String sql = montarSqlComumConsultaOutrasAAgendar(idEmpresa, dataInicial, dataFinal, idUsuario, true);
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    public String montarSqlComumConsultaOutrasAAgendar(Integer idEmpresa, Date dataInicial, Date dataFinal, Integer idUsuario, boolean contar) throws Exception {
        String retorno = contar ? " COUNT(distinct(agendar.id_vendaavulsa, agendar.id_produto)) " : " distinct(venda.codigo), venda.*, it.*, produto.descricao ";
        String sql = "SELECT " + retorno
                + "FROM vendaavulsa venda "
                + "inner join itemvendaavulsa it on it.vendaavulsa = venda.codigo "
                + "INNER JOIN sch_estudio.agenda_agendar agendar ON agendar.id_vendaavulsa = venda.codigo ";
        sql += "INNER JOIN produto on produto.codigo = it.produto ";


        sql += "WHERE produto.tipoproduto = 'SS' and venda.empresa = ? " + filtrosVenda(dataInicial, dataFinal, null, null, idUsuario);
        sql += contar ? "" : " order by dataregistro ;";
        return sql;
    }

    public List<RelatorioFechamentoDiarioVO> consultarOutrasAAgendar(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario) throws SQLException, Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = montarSqlComumConsultaOutrasAAgendar(idEmpresa, dataInicial, dataFinal, idUsuario, false);
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            Iterator<RelatorioFechamentoDiarioVO> i = listaRelatorio.iterator();
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_VENDA);
            listaRelatorio.add(obj);
        }
        return listaRelatorio;
    }

    @Override
    public Integer contadorOutrasExcecao(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        String sql = montarSqlComumConsultaOutrasExcecao(idEmpresa, dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional, true);
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    public String montarSqlComumConsultaOutrasExcecao(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional, boolean contar) {
        String retorno = contar ? "COUNT(agenda.id_agenda_excecao) " : " agenda.id_agenda_excecao as id_agenda, agenda.data_aula,  "
                + "agenda.data_lancamento, agenda.hora_inicio, agenda.hora_termino, pessoaCliente.nome AS desc_cliente, pessoaColab.nome AS desc_colab,"
                + "produto.descricao AS desc_produto, produto.valorfinal AS valortotal, ambiente.descricao AS desc_amb, agenda.status, "
                + "tipo.descricao AS descTipoHorario, usuario.username, agenda.data_lancamento , cliente.codigo as cliente, colab.codigo as colaborador  ";
        String sql = "SELECT " + retorno
                + "FROM sch_estudio.agenda_excecao agenda ";
        String innerJoins = " INNER JOIN sch_estudio.tipo_horario tipo ON tipo.id_tipo_horario = agenda.id_tipo_horario"
                + " INNER JOIN ambiente ambiente ON ambiente.codigo = agenda.id_ambiente "
                + " INNER JOIN produto produto ON produto.codigo = agenda.id_produto "
                + " LEFT JOIN usuario usuario ON usuario.codigo = agenda.id_usuario_lancamento "
                + " INNER JOIN colaborador colab ON colab.codigo = agenda.id_colaborador "
                + " INNER JOIN cliente cliente ON cliente.codigo = agenda.id_cliente "
                + " INNER JOIN pessoa pessoaCliente ON pessoaCliente.codigo = cliente.pessoa "
                + " INNER JOIN pessoa pessoaColab ON pessoaColab.codigo = colab.pessoa  ";
        sql += contar ? "" : innerJoins;
        sql += " WHERE agenda.id_empresa = ? AND resolvido_como = '0' " + filtrosAgenda(dataInicial, dataFinal, null, null, "", idUsuario, idProfissional, horaInicial, horaFinal);
        return sql;
    }

    public List<RelatorioFechamentoDiarioVO> consultarOutrasExcecao(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws SQLException, Exception {
        List<RelatorioFechamentoDiarioVO> listaRelatorio = new ArrayList<RelatorioFechamentoDiarioVO>();
        String sql = montarSqlComumConsultaOutrasExcecao(idEmpresa, dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional, false);
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioFechamentoDiarioVO obj = new RelatorioFechamentoDiarioVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_USUARIO);
            listaRelatorio.add(obj);
        }
        return listaRelatorio;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>RelatorioComissaoVO</code>.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     * @param rs Objeto no está contido o resultado do BD
     * @return RelatorioComissaoVO
     * @throws Exception
     *
     */
    public RelatorioFechamentoDiarioVO montarDados(RelatorioFechamentoDiarioVO obj, ResultSet rs, int nivelMontarDados) throws Exception {
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_PACOTE) {
            obj.getPacoteVO().setCodigo(rs.getInt("id_pacote"));
            obj.getPacoteVO().setDescricao(rs.getString("titulo"));
            obj.getPacoteVO().setPacoteGenerico(rs.getBoolean("generico"));
            obj.setValorTotal(Uteis.arrendondarForcando2CadasDecimaisComVirgula(rs.getDouble("valor_Total")));
            RelatorioFechamentoDiarioVO rel = new RelatorioFechamentoDiarioVO();
            rel.setQuantidadeItemVenda(rs.getInt("quantidade"));
            rel.setTipoComprador(rs.getString("tipoComprador"));
            rel.setNomeComprador(rs.getString("nomecomprador"));
            rel.setDataRegistro(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dataregistro"), "dd/MM/yyyy HH:mm:ss"));
            rel.setProdutoDescricao(rs.getString("descricao"));
            rel.setValorTotal(rs.getString("valorparcial"));
            obj.getPacoteVO().getListaRelFechamDiario().add(rel);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            obj.setTotalSessoesAgendadas(rs.getInt("sum"));
            obj.setTotalStatus(rs.getInt("count"));
            obj.setDescStatus(rs.getString("status").equals("B") ? "PENDENTE" : StatusEnum.get(rs.getString("status")).getDescricao());
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_VENDA) {
            obj.setIdVenda(rs.getInt("codigo"));
            obj.setDataRegistro(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dataregistro"), "dd/MM/yyyy HH:mm:ss"));
            obj.setNomeComprador(rs.getString("nomecomprador"));
            try {
                obj.setQuantidadeItemVenda(rs.getInt("quantidade"));
            } catch (Exception e) {
                // TODO: handle exception
            }
            obj.setTipoComprador(rs.getString("tipoComprador"));
            obj.setProdutoDescricao(rs.getString("descricao"));
            obj.setValorTotal(Uteis.arrendondarForcando2CadasDecimaisComVirgula(rs.getDouble("valorparcial")));
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_VENDACOMPACOTE) {
            obj.setIdVenda(rs.getInt("codigoVenda"));
            obj.setDataRegistro(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dataregistro"), "dd/MM/yyyy HH:mm:ss"));
            obj.setNomeComprador(rs.getString("nomecomprador"));
            try {
                obj.setQuantidadeItemVenda(rs.getInt("quantidade"));
            } catch (Exception e) {
                // TODO: handle exception
            }
            try {
            	obj.setProdutoDescricao(rs.getString("pacoteDescricao"));
            } catch (Exception e) {
            	// TODO: handle exception caso campo esteja vazio
            }
            obj.setTipoComprador(rs.getString("tipoComprador"));
            obj.setProdutoDescricao(rs.getString("descricao"));
            obj.setValorTotal(Uteis.arrendondarForcando2CadasDecimaisComVirgula(rs.getDouble("valorTotal")));
            obj.setDescPacote(getFacade().getPacote().consultarPorCodigoMovParcela(0, rs.getInt("codigoProduto"), obj.getIdVenda()));
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_SOMENTEVENDA) {
            obj.setQuantidadeItemVenda(rs.getInt("quantidade"));
            obj.setValorUnitarioItemVenda(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("valor_unitario")));
            obj.setValorParcialItemVenda(Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("valortotal")));
            obj.setDescPacote(rs.getString("desc_pacote"));
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_USUARIO) {
            obj.setUsuarioLancamento(rs.getString("username"));
            obj.setDataLancamento(Uteis.getDataComHHMM(rs.getTimestamp("data_lancamento")));
            obj.setValorTotal(Uteis.arrendondarForcando2CadasDecimaisComVirgula(rs.getDouble("valortotal")));
            obj.setPacoteVO(new PacoteVO());
            try {
                obj.getPacoteVO().setTitulo(rs.getString("titulo"));
            } catch (Exception e) {
                //e.printStackTrace();
            }
        }
        obj.setIdAgenda(rs.getInt("id_agenda"));
        obj.setDescProduto(rs.getString("desc_produto"));
        obj.setDescCliente(rs.getString("desc_cliente"));
        obj.setCliente(rs.getInt("cliente"));
        obj.setColaborador(rs.getInt("colaborador"));
        obj.setDataAula(Formatador.formatarData(rs.getDate("data_aula"), "dd/MM/yyyy"));
        obj.setHoraInicio(Formatador.formatarHorario(rs.getTime("hora_inicio")));
        if (!(obj.getHoraInicio().toString().equals(Calendario.gerarHorarioInicial(Formatador.obterTime(obj.getHoraInicio())))
                && rs.getTime("hora_termino").toString().equals(Calendario.gerarHorarioFinal(rs.getTime("hora_termino")) + ":59"))) {
            obj.setHoraTermino(Formatador.formatarHorario(rs.getTime("hora_termino")));
        }
        obj.setDescColaborador(Uteis.obterPrimeiroNomeConcatenadoSobreNome(rs.getString("desc_colab")));
        obj.setDescTipoHorario(rs.getString("descTipoHorario"));
        obj.setDescAmbiente(rs.getString("desc_amb"));
        obj.setDescStatus(rs.getString("status").equals("B") ? "PENDENTE" : StatusEnum.get(rs.getString("status")).getDescricao());
        return obj;
    }

    // HORÁRIOS POSSÍVEIS
    @Override
    public List<RelatorioFechamentoDiarioHorarioVO> listarHorario(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        try {
            List<RelatorioFechamentoDiarioHorarioVO> listaHorario = new ArrayList<RelatorioFechamentoDiarioHorarioVO>();

            String sql = "SELECT * FROM sch_estudio.fc_horarios_possiveis(?, ?, ?, ?, ?)";
            PreparedStatement ps = con.prepareStatement(sql);
            ps.setDate(1, dataInicial);
            ps.setDate(2, dataFinal);
            if (Validador.isValidaObject(horaInicial)) {
                ps.setTime(3, horaInicial);
            } else {
                ps.setNull(3, Types.TIME);
            }
            if (Validador.isValidaObject(horaFinal)) {
                ps.setTime(4, horaFinal);
            } else {
                ps.setNull(4, Types.TIME);
            }
            ps.setInt(5, idEmpresa);
            ResultSet resultDados = ps.executeQuery();

            Integer totalHorarios = 0;
            Integer totalAgendado = 0;
            Integer totalConfirmado = 0;
            Integer totalRealizado = 0;
            Integer totalPendente = 0;
            Integer totalFalta = 0;
            Integer totalJustificada = 0;
            while (resultDados.next()) {
                RelatorioFechamentoDiarioHorarioVO obj = new RelatorioFechamentoDiarioHorarioVO();
                obj = montarDados(obj, resultDados, idEmpresa, dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional);
                totalHorarios = totalHorarios + obj.getHorarios();
                obj.setTotalHorarios(totalHorarios);
                totalAgendado = totalAgendado + obj.getAgendado();
                obj.setTotalAgendado(totalAgendado);
                totalConfirmado = totalConfirmado + obj.getConfirmado();
                obj.setTotalConfirmado(totalConfirmado);
                totalRealizado = totalRealizado + obj.getRealizado();
                obj.setTotalRealizado(totalRealizado);
                totalPendente = totalPendente + obj.getPendente();
                obj.setTotalPendente(totalPendente);
                totalFalta = totalFalta + obj.getFalta();
                obj.setTotalFalta(totalFalta);
                totalJustificada = totalJustificada + obj.getJustificada();
                obj.setTotalJustificada(totalJustificada);
                listaHorario.add(obj);
            }

            return listaHorario;
        } catch (Exception e) {
            throw e;
        }
    }

    public Integer contadorAgendadoAmbiente(Integer idEmpresa, Integer idAmbiente, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional, String status) throws Exception {
        String sql = "SELECT COUNT(agenda.id_agenda) "
                + "FROM sch_estudio.agenda agenda "
                + "WHERE agenda.id_empresa = ? AND agenda.id_ambiente = ?  "
                + filtrosAgenda(dataInicial, dataFinal, null, null, status, idUsuario, idProfissional, horaInicial, horaFinal);
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ps.setInt(2, idAmbiente);
        ResultSet resultDados = ps.executeQuery();

        Integer contador = 0;
        if (resultDados.next()) {
            contador = resultDados.getInt("count");
        }

        return contador;
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> contadorAlunosTurma(Integer idEmpresa, Date dataInicial, Date dataFinal, Integer idProfissional) throws Exception {
        String whereData = "";
        if (Validador.isValidaObject(dataInicial) && Validador.isValidaObject(dataFinal)) {
            whereData = " ((datafim >= '" + Uteis.getDataJDBC(dataInicial) + "' AND datafim <= '" + Uteis.getDataJDBC(dataFinal) + "') OR "
                    + "(datainicio >= '" + Uteis.getDataJDBC(dataInicial) + "' AND datainicio <= '" + Uteis.getDataJDBC(dataFinal) + "') OR "
                    + "(datainicio < '" + Uteis.getDataJDBC(dataInicial) + "' AND datafim > '" + Uteis.getDataJDBC(dataFinal) + "')) ";
        }
        String whereProfissional = "";
        if (Validador.isValidaInteger(idProfissional)) {
            whereProfissional = "and  ht.professor = " + idProfissional;
        }

        String sql = "select mat.* from matriculaalunohorarioturma  as mat "
                + "inner join horarioturma as ht on ht.codigo = mat.horarioturma "
                + "inner join turma on turma.codigo = ht.turma "
                + "where " + whereData + whereProfissional + " and turma.monitorada and mat.empresa  = ? ";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        return MatriculaAlunoHorarioTurma.montarDadosConsulta(resultDados, Uteis.NIVELMONTARDADOS_DADOSBASICOS, getCon());
    }

    public RelatorioFechamentoDiarioHorarioVO montarDados(RelatorioFechamentoDiarioHorarioVO obj,
            ResultSet rs, Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, Integer idUsuario, Integer idProfissional) throws Exception {
        obj.setIdAmbiente(rs.getInt("id_ambiente"));
        obj.setDescAmbiente(rs.getString("desc_ambiente"));
        obj.setHorarios(rs.getInt("horarios"));
        obj.setAgendado(contadorAgendadoAmbiente(idEmpresa, obj.getIdAmbiente(), dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional, null));
        obj.setConfirmado(contadorAgendadoAmbiente(idEmpresa, obj.getIdAmbiente(), dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional, "C"));
        obj.setRealizado(contadorAgendadoAmbiente(idEmpresa, obj.getIdAmbiente(), dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional, "M"));
        obj.setPendente(contadorAgendadoAmbiente(idEmpresa, obj.getIdAmbiente(), dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional, "B"));
        obj.setFalta(contadorAgendadoAmbiente(idEmpresa, obj.getIdAmbiente(), dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional, "F"));
        obj.setJustificada(contadorAgendadoAmbiente(idEmpresa, obj.getIdAmbiente(), dataInicial, dataFinal, horaInicial, horaFinal, idUsuario, idProfissional, "X"));

        return obj;
    }

    public RelatorioFechamentoDiario() throws Exception {
        super();
    }

    public RelatorioFechamentoDiario(Connection con) throws Exception {
        super(con);
    }
}
