package br.com.pactosolucoes.estudio.dao;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoParcelaEnum;
import br.com.pactosolucoes.estudio.enumeradores.StatusEnum;
import br.com.pactosolucoes.estudio.interfaces.RelatorioComissaoInterfaceFacade;
import br.com.pactosolucoes.estudio.modelo.RelatorioComissaoVO;
import br.com.pactosolucoes.estudio.util.Validador;
import java.math.BigDecimal;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

/**
 *
 * <AUTHOR> GeoInova Soluções
 */
public class RelatorioComissao extends SuperEntidade implements RelatorioComissaoInterfaceFacade {

    /**
     * Operação para buscar e calcular a comissão dos colaboradores
     *
     * @param idEmpresa
     * @param dataInicial
     * @param dataFinal
     * @param horaInicial
     * @param horaFinal
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaProduto
     * @param listaStatus
     * @param listaTipoHorario
     *
     * @return List<RelatorioComissaoVO> listarComissao
     * @throws Exception
     */
    @Override
    public List<RelatorioComissaoVO> listarComissao(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal,
            List<Integer> listaColaborador, List<Integer> listaAmbiente, List<Integer> listaProduto, List<String> listaStatus, List<Integer> listaTipoHorario, SituacaoParcelaEnum situacaoParcela) throws Exception {

        List<RelatorioComissaoVO> listaComissao = new ArrayList<RelatorioComissaoVO>();

        String whereData = "";
        if (Validador.isValidaObject(dataInicial) && Validador.isValidaObject(dataFinal)) {
            whereData = " AND (agenda.data_aula BETWEEN '" + Formatador.formatarData(dataInicial, "yyyy-MM-dd") + "' AND '" + Formatador.formatarData(dataFinal, "yyyy-MM-dd") + "')";
        }

        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String whereHora = "";
        if (Validador.isValidaObject(horaInicial) && Validador.isValidaObject(horaFinal)) {
            whereHora = " AND (agenda.hora_inicio BETWEEN '" + format.format(horaInicial) + "' AND '" + format.format(horaFinal) + "')";
        }

        String whereColab = "";
        if (Validador.isValidaList(listaColaborador)) {
            whereColab = " AND (colab.codigo IN " + listaColaborador.toString().replace("[", "(").replace("]", ")") + ")";
        }

        String whereAmb = "";
        if (Validador.isValidaList(listaAmbiente)) {
            whereAmb = " AND (amb.codigo IN " + listaAmbiente.toString().replace("[", "(").replace("]", ")") + ")";
        }

        String whereProd = "";
        if (Validador.isValidaList(listaProduto)) {
            whereProd = " AND (prod.codigo IN " + listaProduto.toString().replace("[", "(").replace("]", ")") + ")";
        }

        String whereStatus = "";
        if (Validador.isValidaList(listaStatus)) {
            whereStatus = " AND (agenda.status IN " + listaStatus.toString().replace("[", "('").replace("]", "')").replace(",", "','").replace(" ", "") + ")";
        }

        String whereTipo = "";
        if (Validador.isValidaList(listaTipoHorario)) {
            whereTipo = " AND (agenda.id_tipo_horario IN " + listaTipoHorario.toString().replace("[", "(").replace("]", ")") + ")";
        }
        

        String sql = "SELECT colab.codigo, pessoaColab.nome AS desc_colab, prod.descricao AS desc_prod, cli.codigomatricula, pessoaCli.nome AS desc_cliente, "
                + "agenda.data_aula, agenda.hora_inicio,agenda.hora_termino, "
                + "amb.descricao AS desc_amb, agenda.status, agenda.id_agenda, "
                + "th.sigla, (valorparcial / item.quantidade) AS valor_unitario, (CASE WHEN relprod.porccomissao ISNULL THEN colab.porccomissao ELSE relprod.porccomissao END) AS porc, "
                + "(CASE WHEN relprod.porccomissao ISNULL THEN ((valorparcial / item.quantidade) * (colab.porccomissao / 100)) ELSE ((valorparcial / item.quantidade) * (relprod.porccomissao / 100)) END) AS comissao "
                + ", (CASE WHEN agenda.status = 'S' THEN 'SF' WHEN af.id_agenda_faturar IS not NULL THEN 'NPF'  WHEN movpro.situacao = 'PG' OR (select sum(valorpago) from movprodutoparcela where movproduto = movpro.codigo and recibopagamento is not null) >= ((select count(age.id_agenda) from sch_estudio.agenda age inner join sch_estudio.agenda_venda  agevenda on age.id_agenda = agevenda.id_agenda where agevenda.id_vendaavulsa = venda.id_vendaavulsa  and age.id_agenda <= agenda.id_agenda and age.status not in ('S','X','R'))* (movpro.totalfinal / movpro.quantidade)) THEN 'PG' ELSE 'NPR' END) AS situacao, "
                + " (CASE WHEN agenda.id_colaborador_indicacao  ISNULL THEN 0 ELSE ((valorparcial / item.quantidade) * (colabindicacao.porccomissaoindicacaoestudio / 100)) END) AS comissaoIndicacao, "
                + " pessoacolabindicacao.nome AS desc_colab_indicacao, "
                + " (CASE WHEN agenda.id_colaborador_indicacao ISNULL THEN 0 ELSE colabindicacao.porccomissaoindicacaoestudio END) AS porcIndicacao "
                + "FROM sch_estudio.agenda agenda "
                + "LEFT JOIN sch_estudio.tipo_horario th ON th.id_tipo_horario = agenda.id_tipo_horario "
                + "LEFT JOIN public.ambiente amb ON amb.codigo = agenda.id_ambiente "
                + "LEFT JOIN public.produto prod ON prod.codigo = agenda.id_produto AND prod.desativado = false AND prod.tipoproduto = 'SS' "
                + "LEFT JOIN public.colaborador colab ON colab.codigo = agenda.id_colaborador AND colab.situacao = 'AT' "
                + "LEFT JOIN public.tipocolaborador tipoColab ON tipoColab.colaborador = colab.codigo AND tipoColab.descricao = 'ES' "
                + "LEFT JOIN sch_estudio.produto_colaborador relprod ON relprod.id_produto = agenda.id_produto AND relprod.id_colaborador = agenda.id_colaborador "
                + "LEFT JOIN public.cliente cli ON cli.codigo = agenda.id_cliente "
                + "LEFT JOIN sch_estudio.agenda_venda venda ON venda.id_agenda = agenda.id_agenda "
                + "LEFT JOIN public.itemvendaavulsa item ON item.vendaavulsa = venda.id_vendaavulsa "
                + "LEFT JOIN public.produto prod1 ON prod1.codigo = item.produto "
                + "LEFT JOIN sch_estudio.pacote pc ON item.pacote = pc.id_pacote "
                + "LEFT JOIN public.pessoa pessoaColab ON pessoaColab.codigo = colab.pessoa "
                + "LEFT JOIN public.pessoa pessoaCli ON pessoaCli.codigo = cli.pessoa "
                + "LEFT JOIN public.colaborador colabIndicacao ON colabindicacao.codigo = agenda.id_colaborador_indicacao "
                + "LEFT JOIN public.pessoa pessoaColabIndicacao ON pessoaColabIndicacao.codigo = colabindicacao.pessoa "
                + "left join sch_estudio.agenda_faturar af on af.id_agenda = agenda.id_agenda "
                + "left join public.movproduto movpro  on movpro.vendaavulsa = venda.id_vendaavulsa and (item.produto = movpro.produto) and movpro.situacao <> 'CA' "
                + "WHERE (((item.produto = agenda.id_produto) OR (agenda.produto_generico ='t' AND pc.generico = 't')) OR venda.id_vendaavulsa is null) AND agenda.id_empresa = ?" + whereData + whereHora + whereStatus + whereProd + whereAmb + whereColab + whereTipo
                + " ORDER BY desc_colab, desc_prod, agenda.data_aula, agenda.hora_inicio";
        if(situacaoParcela!=null){
            // Validar código da situação para prevenir SQL injection
            if (!Uteis.isValidStringValue(situacaoParcela.getCodigo())) {
                throw new SecurityException("Código de situação da parcela contém caracteres inválidos");
            }

            if(situacaoParcela.getCodigo().equals("NP")){
                 sql = "select * from ("+sql+") as foo where situacao = 'NPR' or situacao = 'NPF' ";
            }else {
                 // Como já usa PreparedStatement, não precisa escapar manualmente
                 sql = "select * from ("+sql+") as foo where situacao = '"+situacaoParcela.getCodigo()+"'";
            }
        }
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        BigDecimal somaUnitario = new BigDecimal(0);
        BigDecimal somaComissao = new BigDecimal(0);
        while (resultDados.next()) {
            RelatorioComissaoVO obj = new RelatorioComissaoVO();
            obj = montarDados(obj, resultDados);
            if (obj.getValorUnitario() != null && obj.getValorComissao() != null) {
                somaUnitario = somaUnitario.add(obj.getValorUnitario());
                somaComissao = somaComissao.add(obj.getValorComissao());
            }
            listaComissao.add(obj);
        }//4964180

        if (Validador.isValidaList(listaComissao)) {
            listaComissao.get(0).setSomaUnitario(somaUnitario);
            listaComissao.get(0).setSomaComissao(somaComissao);
        }

        return listaComissao;

    }

    /**
     * Operação para contar o número de clientes distintos
     *
     * @param idEmpresa
     * @param dataInicial
     * @param dataFinal
     * @param horaInicial
     * @param horaFinal
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaProduto
     * @param listaStatus
     * @param listaTipoHorario
     *
     * @return Integer totalClientes
     * @throws Exception
     */
    @Override
    public Integer contagemClientes(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal,
            List<Integer> listaColaborador, List<Integer> listaAmbiente, List<Integer> listaProduto, List<String> listaStatus, List<Integer> listaTipoHorario) throws Exception {

        String whereData = "";
        if (Validador.isValidaObject(dataInicial) && Validador.isValidaObject(dataFinal)) {
            whereData = " AND (agenda.data_aula BETWEEN '" + Formatador.formatarData(dataInicial, "yyyy-MM-dd") + "' AND '" + Formatador.formatarData(dataFinal, "yyyy-MM-dd") + "')";
        }

        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String whereHora = "";
        if (Validador.isValidaObject(horaInicial) && Validador.isValidaObject(horaFinal)) {
            whereHora = " AND (agenda.hora_inicio BETWEEN '" + format.format(horaInicial) + "' AND '" + format.format(horaFinal) + "')";
        }

        String whereColab = "";
        if (Validador.isValidaList(listaColaborador)) {
            whereColab = " AND (colab.codigo IN " + listaColaborador.toString().replace("[", "(").replace("]", ")") + ")";
        }

        String whereAmb = "";
        if (Validador.isValidaList(listaAmbiente)) {
            whereAmb = " AND (amb.codigo IN " + listaAmbiente.toString().replace("[", "(").replace("]", ")") + ")";
        }

        String whereProd = "";
        if (Validador.isValidaList(listaProduto)) {
            whereProd = " AND (prod.codigo IN " + listaProduto.toString().replace("[", "(").replace("]", ")") + ")";
        }

        String whereStatus = "";
        if (Validador.isValidaList(listaStatus)) {
            whereStatus = " AND (agenda.status IN " + listaStatus.toString().replace("[", "('").replace("]", "')").replace(",", "','").replace(" ", "") + ")";
        }

        String whereTipo = "";
        if (Validador.isValidaList(listaTipoHorario)) {
            whereTipo = " AND (agenda.id_tipo_horario IN " + listaTipoHorario.toString().replace("[", "(").replace("]", ")") + ")";
        }

        String sql = "SELECT COUNT(*) FROM ( "
                + "SELECT DISTINCT ON (cli.codigomatricula)  cli.codigomatricula "
                + "FROM sch_estudio.agenda agenda "
                + "INNER JOIN sch_estudio.tipo_horario th ON th.id_tipo_horario = agenda.id_tipo_horario "
                + "INNER JOIN public.ambiente amb ON amb.codigo = agenda.id_ambiente "
                + "INNER JOIN public.produto prod ON prod.codigo = agenda.id_produto AND prod.desativado = false AND prod.tipoproduto = 'SS' "
                + "INNER JOIN public.colaborador colab ON colab.codigo = agenda.id_colaborador AND colab.situacao = 'AT' "
                + "INNER JOIN public.tipocolaborador tipoColab ON tipoColab.colaborador = colab.codigo AND tipoColab.descricao = 'ES' "
                + "LEFT JOIN sch_estudio.produto_colaborador relprod ON relprod.id_produto = agenda.id_produto AND relprod.id_colaborador = agenda.id_colaborador "
                + "INNER JOIN public.cliente cli ON cli.codigo = agenda.id_cliente "
                + "INNER JOIN sch_estudio.agenda_venda venda ON venda.id_agenda = agenda.id_agenda "
                + "INNER JOIN public.itemvendaavulsa item ON item.vendaavulsa = venda.id_vendaavulsa AND item.produto = agenda.id_produto "
                + "INNER JOIN public.pessoa pessoaColab ON pessoaColab.codigo = colab.pessoa "
                + "INNER JOIN public.pessoa pessoaCli ON pessoaCli.codigo = cli.pessoa "
                + "WHERE agenda.id_empresa = ?" + whereData + whereHora + whereStatus + whereProd + whereAmb + whereColab + whereTipo
                + " GROUP BY cli.codigomatricula) as CONTAGEM";

        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        Integer totalClientes = null;
        if (resultDados.next()) {
            totalClientes = resultDados.getInt("count");
        }

        return totalClientes;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>RelatorioComissaoVO</code>.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     * @param rs Objeto no está contido o resultado do BD
     * @return RelatorioComissaoVO
     * @throws Exception
     *
     */
    public RelatorioComissaoVO montarDados(RelatorioComissaoVO obj, ResultSet rs) throws Exception {

        obj.setDescColaborador(Uteis.obterPrimeiroNomeConcatenadoSobreNome(rs.getString("desc_colab")));
        obj.setDescProduto(rs.getString("desc_prod"));
        obj.setDescAmbiente(rs.getString("desc_amb"));
        obj.setDescCliente(rs.getString("desc_cliente"));
        obj.setCodgMatricula(rs.getInt("codigomatricula"));
        obj.setDataAula(rs.getDate("data_aula"));
        obj.setHoraInicio(rs.getTime("hora_inicio"));
        if(!(obj.getHoraInicio().toString().equals(Calendario.gerarHorarioInicial(obj.getHoraInicio()) + ":00")
           && rs.getTime("hora_termino").toString().equals(Calendario.gerarHorarioFinal(rs.getTime("hora_termino")) + ":59"))){
           obj.setHoraTermino(rs.getTime("hora_termino"));
        }
        obj.setDescStatus(StatusEnum.get(rs.getString("status")).getDescricao());
        obj.setSiglaTipoAula(rs.getString("sigla"));
        obj.setValorUnitario(rs.getBigDecimal("valor_unitario"));
        obj.setValorComissao(rs.getBigDecimal("comissao"));
        if (rs.getBigDecimal("valor_unitario") != null) {
            obj.setValorUnitarioString(Formatador.formatarValorMonetarioSemMoeda(rs.getBigDecimal("valor_unitario").doubleValue()));
        } else {
            obj.setValorUnitarioString("");
        }
        if (rs.getBigDecimal("comissao") != null) {
            obj.setValorComissaoString(Formatador.formatarValorMonetarioSemMoeda(rs.getBigDecimal("comissao").doubleValue()));
        } else {
            obj.setValorComissaoString("");
        }
        obj.setCodigoAgenda(rs.getInt("id_agenda"));
        obj.setSituacaoParcela(SituacaoParcelaEnum.getSituacaoParcela(rs.getString("situacao")));

        if (rs.getBigDecimal("porc") != null) {
            obj.setPorcentagem(Formatador.formatarValorPercentual(rs.getBigDecimal("porc").doubleValue()/100));
        } else {
            obj.setPorcentagem("");
        }

        try{
            obj.setDescColaboradorIndicacao(Uteis.obterPrimeiroNomeConcatenadoSobreNome(rs.getString("desc_colab_indicacao")));

            obj.setValorComissaoIndicacao(rs.getBigDecimal("comissaoIndicacao"));
            if (rs.getBigDecimal("comissaoIndicacao") != null) {
                obj.setValorComissaoIndicacaoString(Formatador.formatarValorMonetarioSemMoeda(rs.getBigDecimal("comissaoIndicacao").doubleValue()));
            } else {
                obj.setValorComissaoIndicacaoString("");
            }

            if (rs.getBigDecimal("porcIndicacao") != null) {
                obj.setPorcentagemIndicacao(Formatador.formatarValorPercentual(rs.getBigDecimal("porcIndicacao").doubleValue()/100));
            } else {
                obj.setPorcentagemIndicacao("");
            }
        } catch (Exception ignore){}

        return obj;
    }

    public RelatorioComissao() throws Exception {
        super();
    }

    public RelatorioComissao(Connection con) throws Exception {
        super(con);
    }
}
