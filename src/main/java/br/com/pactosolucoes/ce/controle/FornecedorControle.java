package br.com.pactosolucoes.ce.controle;


import br.com.pactosolucoes.ce.comuns.ex.NegocioException;
import br.com.pactosolucoes.ce.comuns.to.FornecedorServicoTO;
import br.com.pactosolucoes.ce.comuns.to.ServicoTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ReplicarRedeEmpresaEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.ReplicarRedeEmpresaCallable;
import controle.basico.CepControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.financeiro.CentroCustosControle;
import controle.financeiro.PlanoContasControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.estoque.CompraVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.plano.FornecedorRedeEmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import servicos.discovery.RedeDTO;
import servicos.impl.pessoaMs.PessoaMsService;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe responsável por implementar a interação entre os componentes JSF da
 * página fornecedor.jsp e fornecedorForm. Implementação da camada controle
 * (Backing Bean).
 *
 * <AUTHOR>
 */
public class FornecedorControle extends CEControle {

    private FornecedorVO fornecedorVO;
    private Boolean sucesso = false;
    private Boolean erro = false;
    private List<FornecedorVO> listaFornecedor;
    private int codigoFornecedor = 0;
    private Boolean novoFicticio;
    private ServicoTO servico;
    private List<ServicoTO> listaservicos;
    private Integer numeroArquivosServicos;
    private FornecedorServicoTO fornecedorServicoTO;
    private List<Integer> servicosAExcluir;

    // Controle das abas
    private String abaAtual;
    private ConfiguracaoSistemaVO configuracaoSistema;
    private EnderecoVO enderecoVO;
    private TelefoneVO telefoneVO;
    private EmailVO emailVO;
    private CepControle cepControle;

    // Atributos para a aba de compras do fornecedor
    private Date dataIniCompra = Calendario.hoje();
    private Date dataFimCompra = Calendario.hoje();
    private String numeroNF;
    private List<CompraVO> listaCompras;

    protected List listaSelectItemCidade;
    protected List listaSelectItemEstado;
    protected List listaSelectItemPais;
    private String[] displayIdentificadorFront;
    private String msgAlert;
    private File documento;
    private boolean existeDocumento;

    // Replicar empresas
    private List<FornecedorRedeEmpresaVO> listaFornecedorRedeEmpresa;
    private String msgAguardandoReplicacao = "AGUARDANDO REPLICAR FORNECEDOR";
    private String msgPrefixoDataAtualizacaoReplicacao = "REPLICADO EM ";

    public void abrirFornecedor() {
        try {
            validarPermissaoFornecedor();
            setMsgAlert("abrirPopup('"+request().getContextPath()+"/faces/pages/ce/cadastros/fornecedor.jsp?modulo=financeiroWeb','Fornecedor', 780, 595);");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

    public void abrirFornecedorCompra() {
        try {
            validarPermissaoFornecedor();
            setMsgAlert("abrirPopup('pages/ce/cadastros/fornecedor.jsp','Fornecedor', 780, 595);");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }


    /**
     * Valida a permissão do usuário logado para a entidade RateioIntegracao
     * que usa a permissão "Plano de Contas"
     * @throws Exception
     */
    public void validarPermissaoFornecedor() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "Fornecedor", "9.11 - Fornecedor");
            }
        }
    }
    /**
     * @return the servicosAExcluir
     */
    public List<Integer> getServicosAExcluir() {
        if (this.servicosAExcluir == null) {
            this.servicosAExcluir = new ArrayList<Integer>();
        }
        return servicosAExcluir;
    }

    /**
     * @param servicosAExcluir
     *            the servicosAExcluir to set
     */
    public void setServicosAExcluir(List<Integer> servicosAExcluir) {
        this.servicosAExcluir = servicosAExcluir;
    }

    /**
     * @return the fornecedorServicoTO
     */
    public FornecedorServicoTO getFornecedorServicoTO() {
        if (this.fornecedorServicoTO == null) {
            this.fornecedorServicoTO = new FornecedorServicoTO();
        }
        return fornecedorServicoTO;
    }

    /**
     * @param fornecedorServicoTO
     *            the fornecedorServicoTO to set
     */
    public void setFornecedorServicoTO(FornecedorServicoTO fornecedorServicoTO) {
        this.fornecedorServicoTO = fornecedorServicoTO;
    }

    public void iniciarNumeroArquivos() {
        this.setNumeroArquivosServicos(1);
    }

    /**
     * @return the numeroArquivosServicos
     */
    public Integer getNumeroArquivosServicos() {
        if (numeroArquivosServicos == null) {
            this.iniciarNumeroArquivos();
        }
        return numeroArquivosServicos;
    }

    /**
     * @param numeroArquivosServicos
     *            the numeroArquivosServicos to set
     */
    public void setNumeroArquivosServicos(Integer numeroArquivosServicos) {
        this.numeroArquivosServicos = numeroArquivosServicos;
    }

    /**
     * @return the listaservicos
     */
    public List<ServicoTO> getListaservicos() {
        return listaservicos;
    }

    /**
     * @param listaservicos
     *            the listaservicos to set
     */
    public void setListaservicos(List<ServicoTO> listaservicos) {
        this.listaservicos = listaservicos;
    }

    /**
     * @return the servico
     */
    public ServicoTO getServico() {
        return servico;
    }

    /**
     * @param servico
     *            the servico to set
     */
    public void setServico(ServicoTO servico) {
        if (this.servico == null) {
            servico = new ServicoTO();
        }
        this.servico = servico;
    }

    /**
     * @return the fornecedor
     */
    public FornecedorVO getFornecedorVO() {
        if (this.fornecedorVO == null) {
            fornecedorVO = new FornecedorVO();
        }
        return fornecedorVO;
    }

    /**
     * @param fornecedorVO
     *            the fornecedor to set
     */
    public void setFornecedorVO(FornecedorVO fornecedorVO) {
        this.fornecedorVO = fornecedorVO;
    }

    /**
     * @return the listaFornecedor
     */
    public List<FornecedorVO> getListaFornecedor() {
        return listaFornecedor;
    }

    /**
     * @param listaFornecedor
     *            the listaFornecedor to set
     */
    public void setListaFornecedor(List<FornecedorVO> listaFornecedor) {
        this.listaFornecedor = listaFornecedor;
    }

    /**
     * @return the novoFicticio
     */
    public Boolean getNovoFicticio() {
        if (this.novoFicticio == null) {
            this.novoFicticio = false;
        }
        return this.novoFicticio;
    }

    /**
     * @param novoFicticio
     *            O novo valor de orcamentoFicticio.
     */
    public void setNovoFicticio(final Boolean novoFicticio) {
        this.novoFicticio = novoFicticio;
    }

    /**
     * @return the codigoFornecedor
     */
    public int getCodigoFornecedor() {
        return this.codigoFornecedor;
    }

    /**
     * @param codigoFornecedor
     *            the codigoFornecedor to set
     */
    public void setCodigoFornecedor(final int codigoFornecedor) {
        this.codigoFornecedor = codigoFornecedor;
    }

    /**
     * @return the sucesso
     */
    @Override
    public Boolean getSucesso() {
        return this.sucesso;
    }

    /**
     * @param sucesso
     *            the sucesso to set
     */
    @Override
    public void setSucesso(final Boolean sucesso) {
        this.sucesso = sucesso;
    }

    /**
     * @return the erro
     */
    @Override
    public Boolean getErro() {
        return this.erro;
    }

    /**
     * @param erro
     *            the erro to set
     */
    @Override
    public void setErro(final Boolean erro) {
        this.erro = erro;
    }

    public FornecedorControle() throws Exception {
        super();
        inicializarConfiguracaoSistema();
        identificacaoPessoalInternacional();
        this.setControleConsulta(new ControleConsulta());
        this.listaFornecedor = new ArrayList<FornecedorVO>();
        this.fornecedorVO = new FornecedorVO();
        this.servico = new ServicoTO();
        // Set mensagem informar parâmetros
        this.setMensagemID("");

    }

    /**
     * Rotina para limpar a lista, a visiblidade dos links de navegação e o
     * objeto de ProdutoLocacaoTO.
     */
    public void limpaLista() {
        this.definirVisibilidadeLinksNavegacao(0, 0);
        this.setFornecedorVO(new FornecedorVO());
        this.setListaFornecedor(new ArrayList<FornecedorVO>());
        this.setCodigoFornecedor(0);
    }

    @Override
    public boolean getApresentarResultadoConsulta() {
        return !(this.listaFornecedor == null);
    }

    /**
     * metodo responsavel por selecionar o objeto correspondente a linha do
     * datatable no tipoAmbiente.jsp
     *
     * @throws Exception
     */
    public String seleciona() throws Exception {
        // pega o map enviado pelo botão editar da tabela de dados
        final Map<String, Object> contexto = FacesContext.getCurrentInstance().getExternalContext().getRequestMap();
        // faz um cast, colocando o objeto selecionado na tabela dentro do
        this.fornecedorVO = (FornecedorVO) contexto.get("fornecedor");
        return "fornecedorNovo";
    }

    /**
     * limpa o TO local e direciona para a pagina de fornecedorNovo
     *
     * @return string de direcionamento
     */
    public String novo() throws Exception {
        limparMsg();
        this.setSucesso(false);
        this.setErro(false);
        this.fornecedorVO = new FornecedorVO();
        this.fornecedorVO.setCodigo(0);
        montarListaSelectItemPais();
        montarListaSelectItemEstado();
        montarListaSelectItemCidade();
        fornecedorVO.setObjetoVOAntesAlteracao(new FornecedorVO());
        fornecedorVO.setNovoObj(true);
        limparControladores();
        return "fornecedorNovo";
    }
    public void limparControladores(){
        PlanoContasControle controlPlanoContas = (PlanoContasControle) JSFUtilities.getFromSession(PlanoContasControle.class.getSimpleName());
        CentroCustosControle controlCentro = (CentroCustosControle) JSFUtilities.getFromSession(CentroCustosControle.class.getSimpleName());

        if (controlPlanoContas == null) {
            controlPlanoContas = new PlanoContasControle();
        }
        if (controlCentro == null) {
            controlCentro = new CentroCustosControle();
        }

        controlPlanoContas.limpar();
        controlCentro.limpar();
    }
    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getFornecedor().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao,filtro, null);
    }

    /**
     * Combo de Empresas
     *
     * @return List - SelectItem
     */
    public List getEmpresaCombo() {
        List itens = new ArrayList();
        try {
            List consultarTodas = getFacade().getEmpresa().consultarTodas(true,
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (Iterator iterator = consultarTodas.iterator(); iterator.hasNext();) {
                EmpresaVO obj = (EmpresaVO) iterator.next();
                itens.add(new SelectItem(obj.getCodigo(), obj.getRazaoSocial()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return itens;
    }

    /**
     * Método responsável por excluir os dados do registro escolhido na tela de
     * consulta
     *
     * @return fornecedor
     * @exception
     */
    public String excluir() {
        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            String path = loginControle.getModuloAberto().getDescricao();

            //this.verificarAutorizacao();
            // Executa o método do facade reponsável pela exclusão, passando
            // como parametro o objeto desse bean
            limparMsg();
            final FornecedorVO obj = getFacade().getFornecedor().obter(
                    this.getFornecedorVO().getCodigo());
            getFacade().getFornecedor().excluir(obj);
            incluirLogExclusao();
            // após a exclusão, limpa o objeto
            this.setFornecedorVO(new FornecedorVO());
            // seta as mensagens de sucesso
            this.setSucesso(true);
            this.setErro(false);
            this.setMensagemID("msg_dados_excluidos");
            this.limpaLista();
            // retorna para a tela de consulta
            novo();
            montarSucessoGrowl("Dados excluídos com sucesso!");
            if (path.equals("Central de Eventos")){
                redirect("/faces/pages/ce/cadastros/fornecedor.jsp?modulo=centralEventos");
            }else {
                redirect("/faces/pages/ce/cadastros/fornecedor.jsp?modulo=financeiroWeb");
            }
            return "consultar";
            // se houverem erros na operação
        } catch (Exception e) {
            this.setSucesso(false);
            this.setErro(true);
            this.setMensagemID("msg_erro_FornecedorRelacionado");
            return "editar";
        }
    }

    public void salvarFornecedor(FornecedorVO obj, List<Integer> servicosExcluir) throws Exception {
        try {
            obj.validarDados(obj);
            if (obj.getCodigo() == null || obj.getCodigo() == 0) {
                PessoaVO pessoaObj = new PessoaVO();
                pessoaObj.setNome(obj.getDescricao());
                pessoaObj.setPais(obj.getPessoa().getPais());
                pessoaObj.setEstadoVO(obj.getPessoa().getEstadoVO());
                pessoaObj.setCidade(obj.getPessoa().getCidade());
                getFacade().getPessoa().incluirSemComit(pessoaObj);
                obj.setPessoa(pessoaObj);
                getFacade().getFornecedor().incluir(obj);
            } else {
                obj.getPessoa().setNome(obj.getDescricao());
                obj.getPessoa().setEmpresaInternacional(configuracaoSistema.isUsarSistemaInternacional());
                getFacade().getPessoa().alterarSemComit(obj.getPessoa());
                getFacade().getFornecedor().alterar(obj);
                for (Integer serv : servicosExcluir) {
                    getCEFacade().getFornecedorServicos().excluir(serv);
                }
            }
            if (getEmpresaLogado().isHabilitarCadastroEmpresaSesi()) {
                anexarArquivo();
                getFacade().getFornecedor().alterarSomenteChaveArquivo(obj);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método responsável por persistir os dados de tipoAmbienteForm.jsp
     *
     * @return tipoAmbiente
     */
    public String salvar() throws Exception {
        String result;
        try {
            validarCamposIntegracaoSesi();
            if (getEmpresaLogado().isHabilitarCadastroEmpresaSesi() &&
                    !UteisValidacao.emptyString(this.getFornecedorVO().getCnpj()) &&
                    getFacade().getFornecedor().existeCNPJNoBanco(this.getFornecedorVO().getCodigo(),
                            Uteis.removerMascara(this.getFornecedorVO().getCnpj()), getEmpresaLogado().getCodigo())) {
                throw new Exception("CNPJ já cadastrado.");
            }
            PlanoContasControle planoContasControle = (PlanoContasControle) getControlador(PlanoContasControle.class);
            CentroCustosControle centroCustosControle = (CentroCustosControle) getControlador(CentroCustosControle.class);
            this.fornecedorVO.setPlanoConta(planoContasControle.getPlanoEscolhido().getCodigo());
            this.fornecedorVO.setCentroCusto(centroCustosControle.getCentroEscolhido().getCodigo());
            //this.verificarAutorizacao();
            // Valida o e-mail e cnpj se não segue o fluxo normal de salvar
            // manda o objeto para o método no Facade que persiste os dados
            if(!getUsuarioLogado().getAdministrador()){
                this.fornecedorVO.setEmpresaVO(getEmpresaLogado());
            }
            this.fornecedorVO.setEmpresaInternacional(configuracaoSistema.isUsarSistemaInternacional());
            salvarFornecedor(this.getFornecedorVO(), this.getServicosAExcluir());
            getCEFacade().incluirFornecedorServico(
                    this.getServico().getFornecedorServicosTOs(),
                    this.getFornecedorVO().getCodigo());
            // salvar entidades associadas
            salvarEnderecos();
            salvarTelefones();
            salvarEmails();
            if (fornecedorVO.isNovoObj()) {
                getFornecedorVO().setPais(getFornecedorVO().getPessoa().getPais().getCodigo().toString());
                getFornecedorVO().setEstado(getFornecedorVO().getPessoa().getEstadoVO().getCodigo().toString());
                getFornecedorVO().setCidade(getFornecedorVO().getPessoa().getCidade().getCodigo().toString());
                //LOG - INICIO
                inicializarLog();
                //LOG - FIM
            } else {
                getFornecedorVO().setPais(getFornecedorVO().getPessoa().getPais().getCodigo().toString());
                getFornecedorVO().setEstado(getFornecedorVO().getPessoa().getEstadoVO().getCodigo().toString());
                getFornecedorVO().setCidade(getFornecedorVO().getPessoa().getCidade().getCodigo().toString());
                //LOG - INICIO
                incluirLogAlteracao();
                //LOG - FIM

                if (isExibirReplicarRedeEmpresa()) {
                    atualizarFornecedorReplicado();
                }
            }

            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
            }

            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            result = "fornecedorNovo";
        } catch (Exception e) {
            // se houverem erros na gravação
            this.setSucesso(false);
            this.setErro(true);
            this.setMensagemDetalhada("msg_erro", e.getMessage());
            result = "fornecedorNovo";
        }

        return result;
    }

    private void validarCamposIntegracaoSesi() throws Exception {
        if (getEmpresaLogado().isHabilitarCadastroEmpresaSesi()) {
            if (UteisValidacao.emptyString(this.fornecedorVO.getRazaoSocial())) {
                throw new Exception("O campo Razão Social é obrigatório.");
            } else if (UteisValidacao.emptyString(this.fornecedorVO.getCnpj())) {
                throw new Exception("O campo CNPJ é obrigatório.");
            } else if (this.fornecedorVO.getDataCadastro() == null) {
                throw new Exception("O campo Data de Cadastro é obrigatório.");
            } else if (this.fornecedorVO.getDataValidade() == null) {
                throw new Exception("O campo Data de Validade é obrigatório.");
            } else if (UteisValidacao.emptyString(this.fornecedorVO.getCnae())) {
                throw new Exception("O campo CNAE é obrigatório.");
            } else if (UteisValidacao.emptyString(this.fornecedorVO.getCodigoFpas())) {
                throw new Exception("O campo Código FPAS é obrigatório.");
            } else if (UteisValidacao.emptyString(this.fornecedorVO.getSindicato())) {
                throw new Exception("O campo Sindicato é obrigatório.");
            } else if (UteisValidacao.emptyNumber(this.fornecedorVO.getNrTotalFuncionarios())) {
                throw new Exception("O campo Número total de funcionários é obrigatório.");
            } else if (UteisValidacao.emptyNumber(this.fornecedorVO.getPorteEmpresa())) {
                throw new Exception("O campo Porte da Empresa é obrigatório.");
            } else if (this.fornecedorVO.getEnderecoVOs() == null || this.fornecedorVO.getEnderecoVOs().isEmpty()) {
                boolean enderecoVazio = true;
                for (EnderecoVO e : this.fornecedorVO.getEnderecoVOs()) {
                    if (!UteisValidacao.emptyString(e.getEndereco())) {
                        enderecoVazio = false;
                        break;
                    }
                }
                if (enderecoVazio) {
                    throw new Exception("O campo Endereço é obrigatório.");
                }
            }
        }
    }

    /**
     * Remove todos os endereços do fornecedor e os salva novamente
     *
     * @throws Exception
     *             caso ocorram erros
     */
    private void salvarEnderecos() throws Exception {
        // remover todos os endereços
        Integer codigoPessoa = this.getFornecedorVO().getPessoa().getCodigo();
        getCEFacade().excluirFornecedorEndereco(codigoPessoa);
        // incluir endereços
        getCEFacade().incluirFornecedorEndereco(this.getFornecedorVO());
    }

    /**
     * Remove todos os emails do fornecedor e os salva novamente
     *
     * @throws Exception
     *             caso ocorram erros
     */
    private void salvarEmails() throws Exception {
        // remover todos os endereços
        Integer codigoPessoa = this.getFornecedorVO().getPessoa().getCodigo();
        getCEFacade().excluirFornecedorEmail(codigoPessoa);
        // incluir endereços
        getCEFacade().incluirFornecedorEmail(this.getFornecedorVO());
    }

    /**
     * Remove todos os telefones do fornecedor e os salva novamente
     *
     * @throws Exception
     *             caso ocorram erros
     */
    private void salvarTelefones() throws Exception {
        // remover todos os endereços
        Integer codigoPessoa = this.getFornecedorVO().getPessoa().getCodigo();
        getCEFacade().excluirFornecedorTelefone(codigoPessoa);
        // incluir endereços
        this.getFornecedorVO().getPessoa().setEmpresaInternacional(configuracaoSistema.isUsarSistemaInternacional());
        getCEFacade().incluirFornecedorTelefone(this.getFornecedorVO());
    }

    /**
     * consulta dados na tabela tipoAmbiente
     *
     * @throws Exception
     *
     */
    @Override
    @SuppressWarnings("unchecked")
    public String consultar() throws Exception {
        String result;
        super.consultar();
        try {
            //this.verificarAutorizacao();
            List objs = this.listar();
            if (this.getErro()) {
                return "fornecedor";
            }
            setListaFornecedor(objs);
            setListaConsulta(objs);
            this.setMensagemID("operacoes.consulta.sucesso");
            this.setSucesso(true);
            this.setErro(false);
            result = "fornecedor";
            // se houverem erros na operação
        } catch (Exception e) {
            this.setSucesso(false);
            this.setErro(true);
            this.setListaConsulta(new ArrayList());
            this.setMensagemDetalhada("msg_erro", e.getMessage());
            result = "fornecedor";
        }
        return result;
    }



    /**
     * limpa os campos da classe
     */
    public void limpar() {
        this.fornecedorVO.setCnpj(null);
    }

    public void inicializarConfiguracaoSistema() throws Exception {
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método que edita
     *
     * @return tipoAmbienteNovo
     * @throws Exception
     */
    public String editar() throws Exception {
        String result;
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            if(!getUsuarioLogado().getAdministrador()){
                this.fornecedorVO.setEmpresaVO(getEmpresaLogado());
            }
            final FornecedorVO obj = getFacade().getFornecedor().obter(codigoConsulta);

            PlanoContasControle controlPlanoContas = (PlanoContasControle) JSFUtilities.getFromSession(PlanoContasControle.class.getSimpleName());
            CentroCustosControle controlCentro = (CentroCustosControle) JSFUtilities.getFromSession(CentroCustosControle.class.getSimpleName());

            if (controlPlanoContas == null) {
                controlPlanoContas = new PlanoContasControle();
            }
            if (controlCentro == null) {
                controlCentro = new CentroCustosControle();
            }

            controlPlanoContas.limpar();
            controlCentro.limpar();

            if (!UteisValidacao.emptyNumber(obj.getPlanoConta())) {
                PlanoContaTO planoContaTO = getFacade().getPlanoConta().consultarPorChavePrimaria(obj.getPlanoConta());
                controlPlanoContas.setPlanoNome(planoContaTO.getCodigoPlano() + " - " + planoContaTO.getDescricao());
                controlPlanoContas.setPlanoEscolhido(planoContaTO);
                controlPlanoContas.setPlano(planoContaTO);
            }
            if (!UteisValidacao.emptyNumber(obj.getCentroCusto())) {
                CentroCustoTO centroCustoTO = getFacade().getCentroCusto().consultarPorChavePrimaria(obj.getCentroCusto());
                controlCentro.setCentroNome(centroCustoTO.getCodigoCentro() + " - " + centroCustoTO.getDescricao());
                controlCentro.setCentroEscolhido(centroCustoTO);
                controlCentro.setCentro(centroCustoTO);
            }

            this.setFornecedorVO(obj);
            this.setCodigoFornecedor(codigoConsulta);
            // obtem os serviços terceirizados por esse fornecedor
            getServico().setFornecedorServicosTOs(getCEFacade().consultarServicosRelacionados(codigoConsulta));
            // define a marcação da combo, que também é responsável pela
            // exibição a listagem de serviços
            if (this.getServico().getFornecedorServicosTOs() != null
                    && !this.getServico().getFornecedorServicosTOs().isEmpty()) {
                this.getFornecedorVO().setChecado(Boolean.TRUE);
            }
            // consulta os endereços
            Integer codgPessoa = this.getFornecedorVO().getPessoa().getCodigo();
            int dadosBasicos = Uteis.NIVELMONTARDADOS_DADOSBASICOS;
            List<EnderecoVO> consultarEnderecos = getFacade().getEndereco().consultarEnderecos(codgPessoa, false,
                    dadosBasicos);
            this.getFornecedorVO().setEnderecoVOs(consultarEnderecos);
            // consulta os telefones
            List<TelefoneVO> telefones = getFacade().getTelefone().consultarTelefones(codgPessoa, false, dadosBasicos);
            this.getFornecedorVO().setTelefoneVOs(telefones);

            // consulta os emails
            List<EmailVO> emails = getFacade().getEmail().consultarEmails(codgPessoa, false, dadosBasicos);
            this.getFornecedorVO().setEmailVOs(emails);

            PessoaVO pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(codgPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            this.getFornecedorVO().setPessoa(pessoaVO);

            montarListaSelectItemPais();
            montarListaSelectItemEstado();
            montarListaSelectItemCidade();

            getFacade().getFornecedor().alterar(fornecedorVO);


            getFornecedorVO().setPais(getFornecedorVO().getPessoa().getPais().getCodigo().toString());
            getFornecedorVO().setEstado(getFornecedorVO().getPessoa().getEstadoVO().getCodigo().toString());
            getFornecedorVO().setCidade(getFornecedorVO().getPessoa().getCidade().getCodigo().toString());

            fornecedorVO.setNovoObj(false);
            fornecedorVO.registrarObjetoVOAntesDaAlteracao();
            inicializarConfiguracaoSistema();
            setCepControle(new CepControle());
            setEnderecoVO(new EnderecoVO());

            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
            }

            result = "fornecedorNovo";
        } catch (Exception e) {
            this.setSucesso(false);
            this.setErro(true);
            this.setListaConsulta(new ArrayList());
            this.setMensagemDetalhada("msg_erro", e.getMessage());
            result = "fornecedor";
        }
        return result;
    }

    /**
     * Lista os fornecedores
     */
    public List<FornecedorVO> listar() {
        try {
            final List<FornecedorVO> objs = getFacade().getFornecedor().consultarPorDescricao(getFornecedorVO().getDescricao());
            // caso a lista tenha pelo menos um resultado
            if (objs.isEmpty()) {
                // mostrar as mensagens de nenhum resultado
                this.setMensagemID("operacoes.consulta.nenhumResultado");
                this.setSucesso(true);
                this.setErro(false);
                // limpar a lista
                this.setListaFornecedor(new ArrayList<FornecedorVO>());
            } else {
                // setar a lista com o resultado da consulta
                this.setListaFornecedor(objs);
                // mostrar as mensagens de sucesso
                this.setMensagemID("operacoes.consulta.sucesso");
                this.setSucesso(true);
                this.setErro(false);
                // caso não tenham resultados
            }

            // se houverem erros na operação
        } catch (Exception e) {
            // mostrar as mensagens de erro
            Exception exTratada = this.tratarMensagemExcecao(e);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(exTratada.getMessage());
            this.setSucesso(false);
            this.setErro(true);
            this.setListaFornecedor(new ArrayList<FornecedorVO>());
        }
        return listaFornecedor;
    }

    /**
     * Consulta os fornecedores
     */
    public void consultarFornecedor() {
        try {
            this.verificarAutorizacao();
            final List<FornecedorVO> objs = getFacade().getFornecedor().consultarOrdenadoPelaDescricao();
            // caso a lista tenha pelo menos um resultado
            if (objs.isEmpty()) {
                // mostrar as mensagens de nenhum resultado
                this.setMensagemID("operacoes.consulta.nenhumResultado");
                this.setSucesso(true);
                this.setErro(false);
                // limpar a lista
                this.setListaFornecedor(new ArrayList<FornecedorVO>());
            } else {
                // setar a lista com o resultado da consulta
                this.setListaFornecedor(objs);
                // mostrar as mensagens de sucesso
                this.setMensagemID("operacoes.consulta.sucesso");
                this.setSucesso(true);
                this.setErro(false);
                // caso não tenham resultados
            }
            // se houverem erros na operação
        } catch (Exception e) {
            // mostrar as mensagens de erro
            final Exception exTratada = this.tratarMensagemExcecao(e);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(exTratada.getMessage());
            this.setSucesso(false);
            this.setErro(true);
            this.setListaFornecedor(new ArrayList<FornecedorVO>());
        }
    }

    /**
     * Método que retorna uma lista com os fornecedores
     *
     * @return os fornecedores
     * @throws Exception
     */
    public List<SelectItem> getListaServico() throws Exception {
        List<SelectItem> result;
        try {
            List<SelectItem> itens = new ArrayList<SelectItem>();
            List<ServicoTO> servicos = FacadeManager.getFacade().getCentralEventosFacade().consultaSemParametro();
            for (ServicoTO servico : servicos) {
                itens.add(new SelectItem(servico.getCodigo(), servico.getDescricao()));
            }
            result = itens;
        } catch (Exception e) {
            result = null;
        }
        return result;
    }

    /**
     * Método que retorna uma lista com os fornecedores
     *
     * @return os fornecedores
     * @throws Exception
     */
    public List<SelectItem> getListaFornecedorServico() throws Exception {
        List<SelectItem> result;
        try {
            List<SelectItem> itens = new ArrayList<SelectItem>();
            List<FornecedorServicoTO> fornecedorServicos = FacadeManager.getFacade().getCentralEventosFacade().consultaFornecedorServicos();
            for (FornecedorServicoTO fornecedorServico : fornecedorServicos) {
                itens.add(new SelectItem(fornecedorServico.getCodigo(),
                        fornecedorServico.getDescServico()));
            }
            result = itens;
        } catch (Exception e) {
            result = null;
        }
        return result;
    }

    public void adicionaServicos() throws Exception {
        try {
            // Obter fornecedor e servicos selecionado, caso não selecionado serviço validar
            if (getServico().getCodigo().intValue() == 0) {
                throw new NegocioException("operacoes.adicao.erro.servicoObrigatorio");
            }
            ServicoTO serv = CEControle.getCEFacade().consultarServicoPorCodigo(getServico().getCodigo());

            // cria um novo objeto para adicionar na lista
            FornecedorServicoTO ft = new FornecedorServicoTO();
            ft.setDescServico(serv.getDescricao());
            ft.setCodServico(serv.getCodigo());

            // Verificar se o servico já foi adicionado
            for (FornecedorServicoTO fsTOs : this.getServico().getFornecedorServicosTOs()) {
                if (ft.getCodServico().equals(fsTOs.getCodServico())) {
                    throw new NegocioException(
                            "operacoes.adicao.erro.servicoJaRelacionado");
                }
            }

            // Adicionar o fornecedor e servico à lista
            this.getServico().getFornecedorServicosTOs().add(ft);

            // Limpar campos de preenchimento de novo ambiente
            // this.setListaservicos(null);

            // Mensagem sucesso
            this.setMensagemID("operacoes.adicao.sucesso");
            this.setAtencao(true);
            this.setSucesso(false);
            this.setErro(false);
        } catch (Exception e) {
            // Tratar mensagem para obter mensagem correta no bundle de
            // mensagens segundo chave contida na exceção
            Exception exTratada = this.tratarMensagemExcecao(e);
            this.setMensagemID(e.getMessage());
            this.setMensagemDetalhada(exTratada.getMessage());
            this.setSucesso(false);
            this.setAtencao(false);
            this.setErro(true);
        }
    }

    public void uploadDocumento(UploadEvent upload) {
        try {
            limparMsg();
            upload(upload);
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void prepararListaReplicarEmpresa() {
        try {
            getListaFornecedorRedeEmpresa().clear();
            final RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());

            for (RedeDTO redeDTO : redeEmpresaDataDTO.getRedeEmpresas()) {
                if (redeDTO.getChave().toLowerCase().trim().equals(getKey().toLowerCase().trim())) {
                    continue;
                }

                FornecedorRedeEmpresaVO fornecedorRedeEmpresaVO = getFacade().getFornecedorRedeEmpresa().consultarPorChaveFornecedorECodigoEmpresaDestino(redeDTO.getChave(), fornecedorVO.getCodigo(), redeDTO.getEmpresaZw());
                if (fornecedorRedeEmpresaVO == null) {
                    fornecedorRedeEmpresaVO = new FornecedorRedeEmpresaVO();
                    fornecedorRedeEmpresaVO.setMensagemSituacao(msgAguardandoReplicacao);
                }
                fornecedorRedeEmpresaVO.setCodigoEmpresaDestino(redeDTO.getEmpresaZw());
                fornecedorRedeEmpresaVO.setChave(redeDTO.getChave().toLowerCase().trim());
                fornecedorRedeEmpresaVO.setNomeUnidade(redeDTO.getNomeFantasia());
                fornecedorRedeEmpresaVO.setRedeDTO(redeDTO);
                getListaFornecedorRedeEmpresa().add(fornecedorRedeEmpresaVO);
            }

            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            Logger.getLogger(FornecedorControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public boolean isExibirReplicarRedeEmpresa() throws Exception {
        boolean integranteFranqueadoraRedeEmpresa = false;
        boolean permiteReplicarRedeEmpresa = false;
        boolean usuarioAdministrador = false;

        try {
            for (UsuarioPerfilAcessoVO userPerfAcess : getControladorTipado(LoginControle.class).getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                if (userPerfAcess.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR")) {
                    usuarioAdministrador = true;
                }
            }
        } catch (Exception e) {
            usuarioAdministrador = false;
        }
        try {
            RedeEmpresaVO redeEmpresaVO = (RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA);
            integranteFranqueadoraRedeEmpresa = redeEmpresaVO != null;
            permiteReplicarRedeEmpresa = !UteisValidacao.emptyNumber(redeEmpresaVO.getId()) && redeEmpresaVO.getChaveFranqueadora().equals(getKey());
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean usuarioAdminPacto = false;
        try {
            usuarioAdminPacto = getUsuarioLogado().getUsuarioAdminPACTO();
            if (!usuarioAdminPacto) {
                usuarioAdminPacto = getUsuarioLogado().getUsuarioPACTOBR();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        final boolean permiteReplicarFornecedorConfiguracao = getFacade().getConfiguracaoSistema().obterPermiteReplicarFornecedorConfiguracao();

        return integranteFranqueadoraRedeEmpresa
                && !fornecedorVO.isNovoObj()
                && permiteReplicarRedeEmpresa
                && (usuarioAdminPacto || usuarioAdministrador)
                && permiteReplicarFornecedorConfiguracao;
    }

    public Integer getListaFornecedorRedeEmpresaSize() {
        return getListaFornecedorRedeEmpresa().size();
    }

    public Integer getListaFornecedorRedeEmpresaSincronizado() {
        Integer cont = 0;
        for (FornecedorRedeEmpresaVO unid : getListaFornecedorRedeEmpresa()) {
            if (unid.getDataAtualizacao() != null) {
                cont++;
            }
        }
        return cont;
    }

    public void replicarTodas() {
        try {
            int qtdThreads = 0;
            final List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (FornecedorRedeEmpresaVO fornecedorRedeEmpresaVO : getListaFornecedorRedeEmpresa()) {
                if (!fornecedorRedeEmpresaVO.getDataAtualizacaoInformada()) {
                    fornecedorRedeEmpresaVO.setSelecionado(true);
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.FORNECEDOR, null, null, null, null, null, null, fornecedorRedeEmpresaVO, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarSelecionadas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (FornecedorRedeEmpresaVO fornecedorRedeEmpresaVO : getListaFornecedorRedeEmpresa()) {
                if (fornecedorRedeEmpresaVO.isSelecionado()) {
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.FORNECEDOR,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            fornecedorRedeEmpresaVO,
                            null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void limparReplicar() {
        for(FornecedorRedeEmpresaVO obj : getListaFornecedorRedeEmpresa()){
            obj.setSelecionado(false);
        }
    }

    public void replicarFornecedorRedeEmpresaGeral() {
        final FornecedorRedeEmpresaVO obj = (FornecedorRedeEmpresaVO) context().getExternalContext().getRequestMap().get("fornecedorRedeEmpresaReplicacao");
        replicarFornecedorRedeEmpresaUnica(obj);
    }

    public void replicarFornecedorRedeEmpresaUnica(FornecedorRedeEmpresaVO obj) {
        try {
            replicarFornecedorRedeEmpresa(obj);
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            obj.setMensagemSituacao("ERRO: " + ex.getMessage());

            try {
                getFacade().getFornecedorRedeEmpresa().alterarMensagemSituacao(obj.getCodigo(), obj.getChave(), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());
            } catch (Exception ignored) {}

            Logger.getLogger(FornecedorControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void replicarFornecedorRedeEmpresa(FornecedorRedeEmpresaVO obj) throws Exception {
        FornecedorRedeEmpresaVO fornecedorRedeEmpresaVO = getFacade()
                .getFornecedorRedeEmpresa()
                .consultarPorChaveFornecedorECodigoEmpresaDestino(
                        obj.getChave(),
                        fornecedorVO.getCodigo(),
                        obj.getCodigoEmpresaDestino()
                );

        final RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
        final String urlOrigemPessoaMs = redeEmpresaDataDTO.getServiceUrls().getPessoaMsUrl();
        final JSONObject cloneFornecedorOrigem = PessoaMsService.clonarFornecedor(fornecedorVO.getCodigo(), urlOrigemPessoaMs, getKey());
        final boolean jaExisteFornecedorRedeEmpresa = fornecedorRedeEmpresaVO != null;

        if (jaExisteFornecedorRedeEmpresa) {
            salvarOuAtualizarFornecedor(obj, fornecedorRedeEmpresaVO, cloneFornecedorOrigem);
        } else {
            cloneFornecedorOrigem.put("empresa", obj.getCodigoEmpresaDestino());

            fornecedorRedeEmpresaVO = new FornecedorRedeEmpresaVO(fornecedorVO.getCodigo(), obj.getChave(), null, obj.getCodigoEmpresaDestino());
            getFacade().getFornecedorRedeEmpresa().inserir(fornecedorRedeEmpresaVO);

            salvarOuAtualizarFornecedor(obj, fornecedorRedeEmpresaVO, cloneFornecedorOrigem);
            prepararListaReplicarEmpresa();
        }
    }

    private void salvarOuAtualizarFornecedor(FornecedorRedeEmpresaVO obj, FornecedorRedeEmpresaVO fornecedorRedeEmpresaVO, JSONObject cloneFornecedorOrigem) throws Exception {
        final JSONObject novoFornecedor = PessoaMsService.replicarFornecedor(cloneFornecedorOrigem, obj.getRedeDTO().getPessoaMsUrl(), obj.getRedeDTO().getChave(), obj.getCodigoEmpresaDestino());
        fornecedorRedeEmpresaVO.setFornecedorReplicado(novoFornecedor.getInt("codigo"));
        fornecedorRedeEmpresaVO.setDataAtualizacao(new Date());
        fornecedorRedeEmpresaVO.setMensagemSituacao(msgPrefixoDataAtualizacaoReplicacao + Uteis.getDataComHora(fornecedorRedeEmpresaVO.getDataAtualizacao()));

        obj.setFornecedorReplicado(novoFornecedor.getInt("codigo"));
        obj.setDataAtualizacao(new Date());
        obj.setMensagemSituacao(msgPrefixoDataAtualizacaoReplicacao + Uteis.getDataComHora(obj.getDataAtualizacao()));
        getFacade().getFornecedorRedeEmpresa().alterarDataAtualizacao(fornecedorVO.getCodigo(), obj.getChave(), novoFornecedor.getInt("codigo"), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());
    }

    public void retirarVinculoReplicacao() {
        limparMsg();
        FornecedorRedeEmpresaVO obj = (FornecedorRedeEmpresaVO) context().getExternalContext().getRequestMap().get("fornecedorRedeEmpresaReplicacao");
        try {
            obj.setDataAtualizacao(null);
            obj.setMensagemSituacao(msgAguardandoReplicacao);
            getFacade().getFornecedorRedeEmpresa().limparDataAtualizacao(fornecedorVO.getCodigo(), obj.getChave(), obj.getCodigoEmpresaDestino());
            getFacade().getFornecedorRedeEmpresa().alterarMensagemSituacao(fornecedorVO.getCodigo(), obj.getChave(), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    public void atualizarFornecedorReplicado() {
        try {
            for (FornecedorRedeEmpresaVO fornecedorRedeEmpresaVO : listaFornecedorRedeEmpresa) {
                final boolean fornecedoresBatem =  fornecedorRedeEmpresaVO.getFornecedor() != null && fornecedorRedeEmpresaVO.getFornecedor().equals(fornecedorVO.getCodigo());
                final boolean replicacaoJaExiste = fornecedorRedeEmpresaVO.getDataAtualizacaoInformada()
                        && fornecedorRedeEmpresaVO.getMensagemSituacao().contains(msgPrefixoDataAtualizacaoReplicacao);

                if (fornecedoresBatem && replicacaoJaExiste) {
                    try {
                        final RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
                        final String urlOrigemPessoaMs = redeEmpresaDataDTO.getServiceUrls().getPessoaMsUrl();

                        final JSONObject fornecedorAtualizadoJson = PessoaMsService.clonarFornecedor(fornecedorVO.getCodigo(), urlOrigemPessoaMs, getKey());
                        fornecedorAtualizadoJson.put("codigo", fornecedorRedeEmpresaVO.getFornecedorReplicado());

                        PessoaMsService.atualizarFornecedorReplicado(fornecedorAtualizadoJson,
                                fornecedorRedeEmpresaVO.getRedeDTO().getPessoaMsUrl(), fornecedorRedeEmpresaVO.getRedeDTO().getChave(),
                                fornecedorRedeEmpresaVO.getCodigoEmpresaDestino());

                    } catch (Exception e) {
                        final String msgErroAtualizacaoReplicacao = "REPLICAO DE FORNECEDORES: Ocorreu um erro ao atualizar os dados do fornecedor replicado '"
                                + fornecedorVO.getDescricao() + "' (cod.: " + fornecedorRedeEmpresaVO.getFornecedorReplicado()
                                + " na unidade de chave " + fornecedorRedeEmpresaVO.getChave()
                                + "). Fornecedor original: cod. " + fornecedorRedeEmpresaVO.getFornecedor();
                        Uteis.logar(msgErroAtualizacaoReplicacao + "- ERRO: " + e, FornecedorControle.class);
                    }
                }
            }

        } catch (Exception e) {
            Uteis.logar(e, FornecedorControle.class);
        }

    }

    private void upload(UploadEvent upload) throws Exception {
        UploadItem item = upload.getUploadItem();
        if (item.getFile().length() > 512000) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.tamanhoLimiteExcedido", "Arquivo tem tamanho superior a 500KB");
            throw new Exception("Tamanho Superior a 500KB");
        }
        String[] partes = item.getFileName().split("\\.");

        setDocumento(item.getFile());
        getFornecedorVO().setDocumentoExtensao("." + partes[partes.length - 1]);

        setSucesso(true);
        setErro(false);
        setMensagem("Arquivo enviado com sucesso");
        setMensagemDetalhada("", "");
    }

    private void anexarArquivo() throws Exception {
        String chaveArquivo = "";
        if (getDocumento() != null) {
            chaveArquivo = MidiaService.getInstance().uploadObjectWithExtension(getKey(),
                    MidiaEntidadeEnum.ANEXO_DOCUMENTO_FORNECEDOR,
                    getFornecedorVO().getCodigo().toString() + "_DOC",
                    getDocumento(),
                    getFornecedorVO().getDocumentoExtensao());
            setExisteDocumento(true);
            getFornecedorVO().setDocumento(chaveArquivo);
        }
        setDocumento(null);
    }

    public void limparArquivo() {
        try {
            limparMsg();
            setDocumento(null);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void downloadDocumentoListener(ActionEvent actionEvent) {
        try {
            limparMsg();
            downloadArquivoListener(actionEvent);
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void excluirArqDocumentoListener(ActionEvent actionEvent) {
        try {
            limparMsg();
            getFornecedorVO().setDocumento("");
            getFornecedorVO().setDocumentoExtensao("");
            getFacade().getFornecedor().alterarSomenteChaveArquivo(getFornecedorVO());
            setExisteDocumento(false);
            montarSucessoGrowl("Arquivo excluído com sucesso!");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    private void downloadArquivoListener(ActionEvent actionEvent) throws Exception {
        byte[] b = MidiaService.getInstance().downloadObjectWithExtensionAsByteArray(getKey(),
                MidiaEntidadeEnum.ANEXO_DOCUMENTO_FORNECEDOR,
                getFornecedorVO().getCodigo().toString() + "_DOC",
                getFornecedorVO().getDocumentoExtensao(), null);
        if (b == null || b.length == 0) {
            throw new Exception("Não foi possível realizar o download do arquivo" +
                    (UteisValidacao.emptyString(getFornecedorVO().getDocumentoExtensao()) ? ", extensão do arquivo inválida." : ""));
        }
        HttpServletResponse res = (HttpServletResponse) context().getExternalContext().getResponse();
        ServletOutputStream out = res.getOutputStream();

        String nomeArquivo = getFornecedorVO().getCodigo() + ("_COMP" + getFornecedorVO().getDocumentoExtensao());
        res.setHeader("Content-disposition", "attachment;filename=\"" + nomeArquivo + "\"");
        res.setContentLength(b.length);
        res.setContentType("application/octet-stream");

        out.write(b);
        out.flush();
        out.close();

        FacesContext.getCurrentInstance().responseComplete();
    }

    /**
     * Remover os serviços dentre os relacionados ao fornecedor
     *
     * @throws Exception
     */
    public void removerServicos() throws Exception {
        // Obter servico a ser excluído
        FornecedorServicoTO obj = (FornecedorServicoTO) JSFUtilities.getRequestAttribute("servico");

        // Remover servico
        this.getServico().getFornecedorServicosTOs().remove(obj);
        this.getServicosAExcluir().add(obj.getCodigo());

        // Exibir mensagem de sucesso
        this.setMensagemID("operacoes.exclusao.sucesso");
        this.setSucesso(true);
        this.setAtencao(false);
        this.setErro(false);
    }

    public void consultarCEPCadastroCompleto() throws Exception {
        try {
            getCepControle().consultarCEPCadastroCompleto(
                    getEnderecoVO().getCep());
            getEnderecoVO().setBairro(
                    getCepControle().getCepVO().getBairroDescricao().trim());
            getEnderecoVO().setEndereco(
                    getCepControle().getCepVO().getEnderecoLogradouro().trim());
            getEnderecoVO().setComplemento(
                    getCepControle().getCepVO().getEnderecoCompleto().trim());
            setMensagemDetalhada("");
            setMensagemID("");
            setMensagem("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<SelectItem> getListaSelectItemRgUfPessoa() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable estados = (Hashtable) Dominios.getEstado();
        Enumeration keys = estados.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) estados.get(value);
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List<SelectItem>) objs, ordenador);
        return objs;
    }

    public String getAbaAtual() {
        return abaAtual;
    }

    public void setAbaAtual(String abaAtual) {
        this.abaAtual = abaAtual;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public EnderecoVO getEnderecoVO() {
        if (enderecoVO == null) {
            enderecoVO = new EnderecoVO();
        }
        return enderecoVO;
    }

    public void setEnderecoVO(EnderecoVO enderecoVO) {
        this.enderecoVO = enderecoVO;
    }

    public CepControle getCepControle() throws Exception {
        if (cepControle == null) {
            cepControle = new CepControle();
        }
        return cepControle;
    }

    public void setCepControle(CepControle cepControle) {
        this.cepControle = cepControle;
    }

    public void selecionarCep() {
        selecionarCep(getEnderecoVO());
    }

    public List<SelectItem> getListaSelectItemTipoTelefoneTelefone() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));

        for (TipoTelefoneEnum tipoTelefone : TipoTelefoneEnum.values()) {

            String value = tipoTelefone.getCodigo();
            String label = tipoTelefone.getDescricao();
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List<SelectItem>) objs, ordenador);
        return objs;
    }

    public boolean cnpjObrigatorioCadastro() {
        try{
            return getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isCnpjObrigatorioFornecedor();
        }catch(Exception e){
            return false;
        }
    }

    public void adicionarTelefone() throws Exception {
        try {
            if (!getFornecedorVO().getCodigo().equals(new Integer(0))) {
                getTelefoneVO().setPessoa(getFornecedorVO().getCodigo());
            }

            getTelefoneVO().setUsarSistemaInternacional(configuracaoSistema.isUsarSistemaInternacional());
            getFornecedorVO().adicionarObjTelefoneVOs(getTelefoneVO());
            this.setTelefoneVO(new TelefoneVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }

    }

    public void editarTelefone() throws Exception {
        TelefoneVO obj = (TelefoneVO) context().getExternalContext().getRequestMap().get("telefone");
        setTelefoneVO(obj);
        setSucesso(false);
        setErro(false);
    }

    public void removerTelefone() throws Exception {
        TelefoneVO obj = (TelefoneVO) context().getExternalContext().getRequestMap().get("telefone");
        getFornecedorVO().excluirObjTelefoneVOs(obj);
        setSucesso(false);
        setErro(true);
        setMensagemID("msg_dados_excluidos");
    }

    public void selecionarCep(EnderecoVO endereco) {
        try {
            // usa a variável de tela
            CepVO obj = (CepVO) context().getExternalContext().getRequestMap().get("cep");
            // atualiza os campos correspondentes
            endereco.setCep(obj.getEnderecoCep());
            endereco.setEndereco(obj.getEnderecoLogradouro());
            endereco.setBairro(obj.getBairroDescricao());

            // reseta as variaveis do controlador
            getCepControle().novo();
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
            setSucesso(true);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setErro(true);
            setSucesso(false);
        }
    }

    public void editarEndereco() throws Exception {
        EnderecoVO obj = (EnderecoVO) context().getExternalContext().getRequestMap().get("endereco");
        setEnderecoVO(obj);
        setSucesso(false);
        setErro(false);
        // return "editar";
    }

    public void removerEndereco() throws Exception {
        EnderecoVO obj = (EnderecoVO) context().getExternalContext().getRequestMap().get("endereco");
        getFornecedorVO().excluirObjEnderecoVOs(obj);
        setSucesso(false);
        setErro(true);
        setMensagemID("msg_dados_excluidos");
        // return "editar";
    }

    public List<SelectItem> getListaSelectItemTipoEnderecoEndereco() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        for (TipoEnderecoEnum obj : TipoEnderecoEnum.values()) {
            String value = obj.getCodigo();
            String label = obj.getDescricao();
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List<SelectItem>) objs, ordenador);
        return objs;
    }

    public void adicionarEndereco() throws Exception {
        try {
            if (!getFornecedorVO().getCodigo().equals(new Integer(0))) {
                enderecoVO.setPessoa(getFornecedorVO().getCodigo());
            }
            getFornecedorVO().adicionarObjEnderecoVOs(getEnderecoVO());
            this.setEnderecoVO(new EnderecoVO());
            setMensagemID("msg_dados_adicionados");
            setAtencao(true);
            setSucesso(false);
            setErro(false);
            // return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            // return "editar";
        }
    }

    public void removerEmail() throws Exception {
        EmailVO obj = (EmailVO) context().getExternalContext().getRequestMap().get("email");
        getFornecedorVO().excluirObjEmailVOs(obj);
        setSucesso(true);
        setErro(false);
        setMensagemID("msg_dados_excluidos");
        // return "editar";
    }

    public void editarEmail() throws Exception {
        EmailVO obj = (EmailVO) context().getExternalContext().getRequestMap().get("email");
        setEmailVO(obj);
        setSucesso(false);
        setErro(false);
        // return "editar";
    }

    public void adicionarEmail() throws Exception {
        try {
            if (!getFornecedorVO().getCodigo().equals(new Integer(0))) {
                emailVO.setPessoa(getFornecedorVO().getCodigo());
            }

            getFornecedorVO().adicionarObjEmailVOs(getEmailVO());
            this.setEmailVO(new EmailVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
            // return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            // return "editar";
        }

    }

    public TelefoneVO getTelefoneVO() {
        if (telefoneVO == null) {
            telefoneVO = new TelefoneVO();
        }
        return telefoneVO;
    }

    public void setTelefoneVO(TelefoneVO telefoneVO) {
        this.telefoneVO = telefoneVO;
    }

    public EmailVO getEmailVO() {
        if (emailVO == null) {
            emailVO = new EmailVO();
        }
        return emailVO;
    }

    public void setEmailVO(EmailVO emailVO) {
        this.emailVO = emailVO;
    }

    public Date getDataFimCompra() {
        return dataFimCompra;
    }

    public void setDataFimCompra(Date dataFimCompra) {
        this.dataFimCompra = dataFimCompra;
    }

    public Date getDataIniCompra() {
        return dataIniCompra;
    }

    public void setDataIniCompra(Date dataIniCompra) {
        this.dataIniCompra = dataIniCompra;
    }

    public String getNumeroNF() {
        return numeroNF;
    }

    public void setNumeroNF(String numeroNF) {
        this.numeroNF = numeroNF;
    }

    public void consultarCompras(){
        try{
            this.listaCompras = getFacade().getCompra().consultar(getEmpresaLogado().getCodigo(), this.dataIniCompra, this.dataFimCompra, this.fornecedorVO.getCodigo(), this.numeroNF, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            setSucesso(false);
            setErro(false);
            this.setMensagemID("operacoes.consulta.sucesso");
        }catch (Exception e){
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public List<CompraVO> getListaCompras() {
        return listaCompras;
    }

    public void setListaCompras(List<CompraVO> listaCompras) {
        this.listaCompras = listaCompras;
    }

    public List getListaSelectItemCidade() {
        return listaSelectItemCidade;
    }

    public void setListaSelectItemCidade(List listaSelectItemCidade) {
        this.listaSelectItemCidade = listaSelectItemCidade;
    }

    public List getListaSelectItemEstado() {
        return listaSelectItemEstado;
    }

    public void setListaSelectItemEstado(List listaSelectItemEstado) {
        this.listaSelectItemEstado = listaSelectItemEstado;
    }

    public List getListaSelectItemPais() {
        return listaSelectItemPais;
    }

    public void setListaSelectItemPais(List listaSelectItemPais) {
        this.listaSelectItemPais = listaSelectItemPais;
    }

    private CidadeVO cidadeVO;

    public CidadeVO getCidadeVO() {
        if (cidadeVO == null) {
            cidadeVO = new CidadeVO();
        }
        return cidadeVO;
    }

    public void setCidadeVO(CidadeVO cidadeVO) {
        this.cidadeVO = cidadeVO;
    }


    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public List consultarCidadePorCodigoEstado(Integer nomePrm) throws Exception {
        return getFacade().getCidade().consultarPorCodigoEstado(nomePrm, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public List consultarEstadoPorCodigoPais(Integer nomePrm) throws Exception {
        return getFacade().getPais().consultarEstadoPorPais(nomePrm, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public List consultarPaisPorNome(String nomePrm) throws Exception {
        return getFacade().getPais().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public void montarListaSelectItemPais() throws Exception {
        montarListaSelectItemPais("");
    }

    public void montarListaSelectItemEstado() throws Exception {
        montarListaSelectItemEstado("");
    }

    public void montarListaSelectItemCidade() throws Exception {
        montarListaSelectItemCidade("");
    }

    public void montarListaSelectItemPais(String prm) throws Exception {
        List resultadoConsulta = consultarPaisPorNome(prm);
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));

        for (Object aResultadoConsulta : resultadoConsulta) {
            PaisVO obj = (PaisVO) aResultadoConsulta;
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }

        setListaSelectItemPais(objs);
    }

    public List getMontarListaSelectItemPorteEmpresa() {
        List objs = new ArrayList();
        objs.add(new SelectItem(1, "Pequena"));
        objs.add(new SelectItem(2, "Média"));
        objs.add(new SelectItem(3, "Grande"));
        return objs;
    }

    public void montarListaSelectItemEstado(String prm) throws Exception {
        getCidadeVO().setPais(getFornecedorVO().getPessoa().getPais());
        List resultadoConsulta = consultarEstadoPorCodigoPais(getFornecedorVO().getPessoa().getPais().getCodigo());

        List objs = new ArrayList();
        for (Object aResultadoConsulta : resultadoConsulta) {
            EstadoVO obj = (EstadoVO) aResultadoConsulta;
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListaSelectItemEstado(objs);
        if (getFornecedorVO().getPessoa().getPais().getCodigo() == 0) {
            getFornecedorVO().getPessoa().getEstadoVO().setCodigo(new Integer(0));
            montarListaSelectItemCidade(prm);
        }

        // setListaSelectItemCidade(new ArrayList());
    }

    public void montarListaSelectItemCidade(String prm) throws Exception {
        getCidadeVO().setEstado(getFornecedorVO().getPessoa().getEstadoVO());
        List resultadoConsulta = consultarCidadePorCodigoEstado(getFornecedorVO().getPessoa().getEstadoVO().getCodigo());
        List objs = new ArrayList();

        for (Object aResultadoConsulta : resultadoConsulta) {
            CidadeVO obj = (CidadeVO) aResultadoConsulta;
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }

        setListaSelectItemCidade(objs);
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");

            control.init("Exclusão de Fornecedor",
                    "Deseja excluir o Fornecedor?",
                    this, "excluir", "", "", "", "grupoBtnExcluir,mensagens");

    }

    public String[] identificacaoPessoalInternacional(){
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(getEmpresaLogado().getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;
    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "FORNECEDOR";
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                getFornecedorVO().getCodigo(), 0);
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(fornecedorVO, fornecedorVO.getCodigo(), "FORNECEDOR", 0);
            fornecedorVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("FORNECEDOR", fornecedorVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAO DE FORNECEDOR ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        fornecedorVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void inicializarLog() throws Exception{
        try {
            fornecedorVO.setObjetoVOAntesAlteracao(new FornecedorVO());
            fornecedorVO.setNovoObj(true);
            registrarLogObjetoVO(fornecedorVO, fornecedorVO.getCodigo(), "FORNECEDOR", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("FORNECEDOR", fornecedorVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAO DE FORNECEDOR ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        fornecedorVO.setNovoObj(false);
        fornecedorVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            fornecedorVO.setObjetoVOAntesAlteracao(new FornecedorVO());
            fornecedorVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(fornecedorVO, fornecedorVO.getCodigo(), "FORNECEDOR", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("FORNECEDOR", fornecedorVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE FORNECEDOR", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();

        }
    }

    public File getDocumento() {
        return documento;
    }

    public void setDocumento(File documento) {
        this.documento = documento;
    }

    public boolean isExisteDocumento() {
        return existeDocumento;
    }

    public void setExisteDocumento(boolean existeDocumento) {
        this.existeDocumento = existeDocumento;
    }

    public List<FornecedorRedeEmpresaVO> getListaFornecedorRedeEmpresa() {
        if(listaFornecedorRedeEmpresa == null) {
            listaFornecedorRedeEmpresa = new ArrayList<>();
        }
        return listaFornecedorRedeEmpresa;
    }

    public void setListaFornecedorRedeEmpresa(List<FornecedorRedeEmpresaVO> listaFornecedorRedeEmpresa) {
        this.listaFornecedorRedeEmpresa = listaFornecedorRedeEmpresa;
    }
}
