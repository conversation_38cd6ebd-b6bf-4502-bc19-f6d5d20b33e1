package br.com.pactosolucoes.contrato.servico.intf;

import negocio.comuns.arquitetura.UsuarioVO;
import org.json.JSONObject;

import javax.servlet.ServletContext;

/**
 * <AUTHOR>
 */
public interface PlanoPersonalAssinaturaDigitalServiceInterface {

    public Boolean validarToken(String token) throws Exception;

    public JSONObject infoAcesso(String chave, Integer codigoUsuario, Integer empresaLogada) throws Exception;

    public void incluirAssinatura(String chave,String token, Integer contrato, Integer usuarioResponsavel,
                                  Integer contratoTextoPadrao, String documentos, String assinatura, String endereco, String atestado,
                                  String anexo1, String anexo2, boolean updateAssinatura, boolean updateDocs, boolean updateEnd,
                                  boolean updateAte, boolean updateAnexo1, boolean updateAnexo2, String descMoedaEmpresa) throws Exception;

    public JSONObject consultarPlanosPersonal(boolean assinados, String filtro, Integer empresa, boolean todos) throws Exception;

    public JSONObject selecionarPlanoPersonal(final String key, final Integer contrato, String descMoedaEmpresa) throws Exception;

    public void incluirAssinaturaSimples(String chave, Integer contrato, String assinatura) throws Exception;

    public JSONObject visualizarPlanoPersonal(Integer contrato, String url) throws Exception;

    public ServletContext getServletContext();

    public void setServletContext(ServletContext servletContext);

    public String alterarFotoPersonal(String chave,
                                   Integer taxaPersonal, Integer codigoPessoa, String foto, Integer usuarioLogado, UsuarioVO usuarioSessao) throws Exception;

    public JSONObject consultarParaAtestado(String filtro, Integer empresa) throws Exception;

    public void salvarImagens(String chave, Integer contrato, UsuarioVO usuarioSessao, String imagemDocumentos, boolean documentosUpdate,
                              String imagemEndereco, boolean enderecoUpdate, String imagemAtestado, boolean atestadoUpdate,
                              String imagemAnexo1, boolean anexo1Update, String imagemAnexo2, boolean anexo2Update) throws Exception;


    public void removerAssinaturaPersonal(final Integer codigoTaxa, final Integer usuarioResponsavel) throws Exception;

}
