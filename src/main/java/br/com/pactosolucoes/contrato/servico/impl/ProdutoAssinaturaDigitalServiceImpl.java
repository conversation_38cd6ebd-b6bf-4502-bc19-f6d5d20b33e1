package br.com.pactosolucoes.contrato.servico.impl;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.contrato.servico.intf.ProdutoAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.*;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ProdutoTextoPadraoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.*;
import negocio.facade.jdbc.financeiro.AulaAvulsaDiaria;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.plano.PlanoTextoPadrao;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.contrato.*;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.operacoes.midias.zwinternal.MidiaZWInternal;
import servicos.propriedades.PropsService;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

import static controle.arquitetura.SuperControle.getConfiguracaoSMTPNoReply;

public class ProdutoAssinaturaDigitalServiceImpl implements ProdutoAssinaturaDigitalServiceInterface {

    private Connection con;
    private ProdutoAssinaturaDigitalInterfaceFacade padDao;
    private UsuarioInterfaceFacade usuarioDao;
    private ServletContext servletContext;

    public ProdutoAssinaturaDigitalServiceImpl(Connection con) throws Exception {
        this.con = con;
        padDao = new ProdutoAssinaturaDigital(con);
        usuarioDao = new Usuario(con);
    }

    @Override
    public void incluirAssinaturaContratoProduto(String chave, String token, Integer contrato, String ip, Integer usuarioResponsavel,
                                  Integer produtoTextoPadrao, String documentos, String assinatura, String endereco,
                                  String atestado, String anexo1, String anexo2, boolean updateAssinatura, boolean updateDocs,
                                  boolean updateEnd, boolean updateAte, boolean updateAnexo1, boolean updateAnexo2, String descMoedaEmpresa, String urlRequest) throws Exception {
        ProdutoAssinaturaDigitalVO assinaturaAnterior = padDao.consultarPorContratoProduto(contrato);

        ProdutoAssinaturaDigitalVO cad = new ProdutoAssinaturaDigitalVO();

        cad.setProdutoTextoPadrao(new ProdutoTextoPadraoVO());
        cad.getProdutoTextoPadrao().setCodigo(contrato);

        cad.setUsuarioResponsavel(new UsuarioVO());
        cad.getUsuarioResponsavel().setCodigo(usuarioResponsavel);

        cad.setDocumentos(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_DOCUMENTOS_CONTRATO_PRODUTO, assinaturaAnterior.getDocumentos(), documentos, updateDocs));
        cad.setEndereco(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_ENDERECO_CONTRATO_PRODUTO, assinaturaAnterior.getEndereco(), endereco, updateEnd));
        cad.setAssinatura(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO_PRODUTO, assinaturaAnterior.getAssinatura(), assinatura, updateAssinatura));
        cad.setAtestado(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO_PRODUTO, assinaturaAnterior.getAtestado(), atestado, updateAte));
        cad.setAnexo1(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO1_PRODUTO, assinaturaAnterior.getAnexo1(), anexo1, updateAnexo1));
        cad.setAnexo2(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO2_PRODUTO, assinaturaAnterior.getAnexo2(), anexo2, updateAnexo2));

        if(assinaturaAnterior == null
                || UteisValidacao.emptyNumber(assinaturaAnterior.getCodigo())){
            padDao.incluir(cad);
        }else{
            cad.setCodigo(assinaturaAnterior.getCodigo());
            padDao.alterar(cad);
        }
//            try {
//                enviarEmail(chave, contrato, cad.getUsuarioResponsavel().getCodigo(), Uteis.getPaintFotoDaNuvem(cad.getDocumentos()),
//                        Uteis.getPaintFotoDaNuvem(cad.getEndereco()),
//                        Uteis.getPaintFotoDaNuvem(cad.getAtestado()),
//                        Uteis.getPaintFotoDaNuvem(cad.getAnexo1()),
//                        Uteis.getPaintFotoDaNuvem(cad.getAnexo2()),
//                        Uteis.getPaintFotoDaNuvem(cad.getAssinatura()), descMoedaEmpresa);
//            } catch (Exception ignore) {
//                String result = ignore.getMessage();
//            }

        UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(usuarioResponsavel, Uteis.NIVELMONTARDADOS_MINIMOS);

        ProdutoTextoPadrao produtoTextoPadraoDAO = new ProdutoTextoPadrao(con);
        ProdutoTextoPadraoVO produtoTextoPadraoVO = produtoTextoPadraoDAO.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        produtoTextoPadraoDAO = null;

        int codPessoa = 0;
        if (!UteisValidacao.emptyNumber(produtoTextoPadraoVO.getVendaAvulsa())) {
            VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
            VendaAvulsaVO venda = vendaAvulsaDAO.consultarPorChavePrimaria(produtoTextoPadraoVO.getVendaAvulsa(), Uteis.NIVELMONTARDADOS_TODOS);
            codPessoa = venda.getCliente().getPessoa().getCodigo();
            vendaAvulsaDAO = null;
        } else if (!UteisValidacao.emptyNumber(produtoTextoPadraoVO.getAulaavulsadiaria())) {
            AulaAvulsaDiaria aulaAvulsaDiariaDAO = new AulaAvulsaDiaria(con);
            AulaAvulsaDiariaVO vendaDiaria = aulaAvulsaDiariaDAO.consultarPorChavePrimaria(produtoTextoPadraoVO.getAulaavulsadiaria(), Uteis.NIVELMONTARDADOS_TODOS);
            codPessoa = vendaDiaria.getCliente().getPessoa().getCodigo();
            aulaAvulsaDiariaDAO = null;
        } else {
            throw new Exception("Produto texto padrão sem venda avulsa ou aula avulsa diária associada");
        }
        registrarLogProduto(cad, usuarioVO, codPessoa);
        notificarAssinaturaParaAcesso(chave, codPessoa);
    }


    public String uploadImagem(Integer codigoContrato, MidiaEntidadeEnum midia, String dataSource) throws Exception{
        if(UteisValidacao.emptyString(dataSource)){
            return "";
        }
        if (dataSource.startsWith("data:") && dataSource.contains(";base64,")) {
            dataSource = dataSource.split(";base64,")[1];
        }
        dataSource = dataSource.replace(" ","+");
        final String chave = DAO.resolveKeyFromConnection(con);

//        final String identificador = codigoContrato+"_"+midia.name()+"_"+Calendario.hoje().getTime();
        //identificador não pode ser maior q 48 caracteres
        //não precisa ter o tipo de midia name pois no s3 já separa por pasta (que é o tipo de midia)

        final String identificador = codigoContrato + "_MIDIA_" + midia.getCodigo() + "_" + Calendario.hoje().getTime();
        return MidiaService.getInstance().uploadObjectFromByteArray(chave, midia, identificador, Base64.decodeBase64(dataSource));
    }

    public String uploadImagem(Integer codigoContrato, MidiaEntidadeEnum midia, String anterior, String novo, boolean forceUpdate) throws Exception {
        final String chave = DAO.resolveKeyFromConnection(con);
        if (anterior != null && anterior.contains(chave)) {
            anterior = anterior.substring(anterior.indexOf(chave));
        }
        if(forceUpdate) {
            anterior = uploadImagem(codigoContrato, midia, novo);
        }
        return anterior;
    }

    @Override
    public JSONObject consultarContratosProdutos(boolean assinados, String filtro, Integer empresa, boolean todos) throws Exception {
        JSONObject retorno = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        List<ProdutoTextoPadraoVO> contratosProduto = padDao.consultarContratosProdutos(assinados, filtro, empresa, todos);
        for(ProdutoTextoPadraoVO c : contratosProduto){
            JSONObject j = new JSONObject();
            j.put("nome", Uteis.getNomeAbreviado(c.getCliente().getPessoa().getNome().toLowerCase()));
            j.put("matricula", c.getCliente().getCodigoMatricula());
            j.put("contrato", c.getCodigo());
            j.put("urlFoto", c.getCliente().getPessoa().getUrlFoto());
            if(assinados){
                if(c.getIpAssinaturaContrato()!=null){
                    j.put("assinadoem", Uteis.getDataAplicandoFormatacao(c.getDataAssinaturaContrato(), "'Assinado via email em ' dd/MM/yyyy 'as' HH:mm"));
                    j.put("cpf", c.getCliente().getPessoa().getCfp());
                    j.put("ipassinaturacontrato", c.getIpAssinaturaContrato());
                    j.put("emailrecebimento", c.getEmailRecebimento());
                    j.put("dataAssinatura", Uteis.getDataAplicandoFormatacao(c.getDataAssinaturaContrato(), " dd/MM/yyyy 'as' HH:mm"));
                }else{
                    j.put("assinadoem", "");
                    j.put("cpf","");
                    j.put("ipassinaturacontrato", "");
                    j.put("emailrecebimento", "");
                    j.put("assinadoem", Uteis.getDataAplicandoFormatacao(c.getAssinadoEm(), "'Assinado em ' dd/MM/yyyy 'as' HH:mm"));
                }
            }
            jsonArray.put(j);
        }
        Integer count = padDao.countContratosProdutos(assinados, filtro, empresa);
        retorno.put("contratos", jsonArray);
        retorno.put("nr", count);
        retorno.put("excede", count > 20);
        return retorno;
    }

    @Override
    public JSONObject selecionarContratoProduto(final String key, final Integer produtoContrato, String descMoedaEmpresa, HttpServletRequest request) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ctp.codigo as produtotextopadrao, ctp.produto, ctp.vendaavulsa, ctp.aulaavulsadiaria FROM produtotextopadrao ctp \n");
        sql.append(" INNER JOIN planotextopadrao ptp ON ptp.codigo = ctp.planotextopadrao \n");
        sql.append(" WHERE ctp.codigo = ").append(produtoContrato);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if(rs.next()){
            String texto = "";
            JSONObject json = new JSONObject();
            json.put("contrato", produtoContrato);
            if(rs.getInt("vendaavulsa") > 0) {
                VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
                VendaAvulsaVO venda = vendaAvulsaDAO.consultarPorChavePrimaria(rs.getInt("vendaavulsa"), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                venda.setParcela(UteisValidacao.emptyList(venda.getMovParcelaVOs()) ? new MovParcelaVO() : venda.getMovParcelaVOs().get(0));
                ProdutoVO prod = new Produto(con).consultarPorChavePrimaria(rs.getInt("produto"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                venda.setTextoPadrao(new PlanoTextoPadrao(con).consultarPorChavePrimaria(prod.getContratoTextoPadrao(), Uteis.NIVELMONTARDADOS_TODOS));
                venda.getParcela().setPessoa(new Pessoa(con).consultarPorChavePrimaria(
                        venda.getParcela().getPessoa().getCodigo(),
                        Uteis.NIVELMONTARDADOS_TODOS));
                venda.setMovProdutoVOs(new MovProduto(con).consultarPorCodigoParcela(venda.getParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                venda.setEmpresa(
                        new Empresa(con).consultarPorChavePrimaria(
                                venda.getEmpresa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_TODOS));
                venda.setResponsavel(
                        new Usuario(con).consultarPorChavePrimaria(
                                venda.getResponsavel().getCodigo(),
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                List<MovPagamentoVO> pagamentos = new MovPagamento(con).consultarPagamentoDeUmaParcela(
                        venda.getParcela().getCodigo().intValue(), false,
                        Uteis.NIVELMONTARDADOS_TODOS);
                venda.getTextoPadrao().substituirTagsTextoPadraoVendaAvulsa(key, con, venda, null, pagamentos, venda.getEmpresa().getDescMoeda(), request, true, true, rs.getInt("produto"));
                texto = (String) request.getSession().getAttribute("textoRelatorio");
            } else {
                AulaAvulsaDiaria aulaAvulsaDiaria = new AulaAvulsaDiaria(con);
                AulaAvulsaDiariaVO vendaDiaria = aulaAvulsaDiaria.consultarPorChavePrimaria(rs.getInt("aulaavulsadiaria"), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                vendaDiaria.setMovParcelaVOs(new MovParcela(con).consultarPorCodigoAulaAvulsaDiariaLista(vendaDiaria.getCodigo(), null, false, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                vendaDiaria.setParcela(UteisValidacao.emptyList(vendaDiaria.getMovParcelaVOs()) ? new MovParcelaVO() : vendaDiaria.getMovParcelaVOs().get(0));
                ProdutoVO prod = new Produto(con).consultarPorChavePrimaria(rs.getInt("produto"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                vendaDiaria.setTextoPadrao(new PlanoTextoPadrao(con).consultarPorChavePrimaria(prod.getContratoTextoPadrao(), Uteis.NIVELMONTARDADOS_TODOS));
                vendaDiaria.getParcela().setPessoa(new Pessoa(con).consultarPorChavePrimaria(
                        vendaDiaria.getParcela().getPessoa().getCodigo(),
                        Uteis.NIVELMONTARDADOS_TODOS));
                vendaDiaria.setMovProdutoVOs(new MovProduto(con).consultarPorCodigoParcela(vendaDiaria.getParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                vendaDiaria.setEmpresa(
                        new Empresa(con).consultarPorChavePrimaria(
                                vendaDiaria.getEmpresa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_TODOS));
                vendaDiaria.setResponsavel(
                        new Usuario(con).consultarPorChavePrimaria(
                                vendaDiaria.getResponsavel().getCodigo(),
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                List<MovPagamentoVO> pagamentos = new MovPagamento(con).consultarPagamentoDeUmaParcela(
                        vendaDiaria.getParcela().getCodigo().intValue(), false,
                        Uteis.NIVELMONTARDADOS_TODOS);
                vendaDiaria.getTextoPadrao().substituirTagsTextoPadraoVendaAvulsa(key, con, null, vendaDiaria, pagamentos, vendaDiaria.getEmpresa().getDescMoeda(), request, true, true, rs.getInt("produto"));
                texto = (String) request.getSession().getAttribute("textoRelatorio");
            }
            texto = texto.replaceAll("__4ssin4tur4digit4l__", "<b>A assinatura digital ser colocada aqui.</b>");
            json.put("texto", texto);
            json.put("modelo", rs.getInt("produtotextopadrao"));
            return json;
        }
        return null;
    }

    @Override
    public JSONObject visualizarContratoProduto(Integer contrato, String url) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT documentos, endereco, assinatura, atestado, anexo1, anexo2 FROM produtoassinaturadigital where produtotextopadrao = ").append(contrato);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            if (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("documentos", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("documentos"), url)));
                json.put("endereco", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("endereco"), url)));
                json.put("contrato", contrato);
                json.put("assinatura", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("assinatura"), url)));
                json.put("atestado", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("atestado"), url)));
                json.put("anexo1", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("anexo1"), url)));
                json.put("anexo2", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("anexo2"), url)));
                json.put("assinaturaviaemail", "");
                return json;
            } else {
                sql = new StringBuilder();
                sql.append("SELECT ipassinaturacontrato,dataassinaturacontrato,emailrecebimento, p.cfp from produtotextopadrao c  " +
                        "INNER JOIN vendaavulsa va ON va.codigo = c.vendaavulsa " +
                        "INNER JOIN cliente cl ON cl.codigo = va.cliente " +
                        "INNER JOIN pessoa p ON p.codigo = cl.pessoa " +
                        "INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa " +
                        "LEFT JOIN contratoassinaturadigital cad ON cad.contrato = c.codigo " +
                        "WHERE c.codigo  =" + contrato + " " +
                        "AND (cad.assinatura is not null AND cad.assinatura <> '' or c.dataassinaturacontrato is not null) " +
                        " ");
                rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                if (rs.next()) {
                    JSONObject json = new JSONObject();
                    json.put("documentos", "");
                    json.put("endereco", "");
                    json.put("contrato", contrato);
                    json.put("assinatura", "");
                    json.put("atestado", "");
                    json.put("anexo1", "");
                    json.put("anexo2", "");
                    json.put("assinaturaviaemail", "Assinado via email pelo IP: " + rs.getString("ipassinaturacontrato") + " <br />" +
                            Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dataassinaturacontrato"), "'Assinado em ' dd/MM/yyyy 'as' HH:mm") + ". <br />" +
                            "CPF confirmado: " + rs.getString("cfp") + ". <br />" +
                            "Email confirmado: " + rs.getString("emailrecebimento") + " .");
                    return json;
                }
        }
        return null;
    }

    public void enviarContrato(String chave, UsuarioVO usuario, String[] emails, ContratoVO contrato,
                               List<MovPagamentoVO> pagamentos, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO,
                               String urlDocs,
                               String urlComprovanteEndereco,
                               String urlAtestado,
                               String urlAnexo1,
                               String urlAnexo2,
                               String urlAssinatura, String descMoedaEmpresa) throws Exception {
        if (contrato.getCodigo() != 0) {
            String texto = new ContratoTextoPadrao(con).consultarHtmlContrato(contrato.getCodigo(),false);
            UteisEmail email = new UteisEmail();
            texto = arranjarImagens(chave, texto, contrato, false);
            texto = addDocsAssinatura(texto, urlDocs, urlComprovanteEndereco, urlAtestado, urlAnexo1, urlAnexo2, urlAssinatura);

            email.novo(contrato.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", configuracaoSistemaCRMVO);
            email.setRemetente(usuario);

            boolean existeEmailValido = false;
            for (String emailEnviar : emails) {
                if (UteisValidacao.validaEmail(emailEnviar)) {
                    existeEmailValido = true;
                }
            }
            if (existeEmailValido) {
                email.enviarEmailN(emails, texto, contrato.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", "");
            } else {
                throw new Exception("Não foi possível enviar o contrato pois o cliente não possui um email válido.");
            }
        } else {
            throw new Exception("Não foi possÍvel enviar o contrato. Dados não encontrados!");
        }
    }

    public String addDocsAssinatura(String texto, String urlDocs,
                                    String urlComprovanteEndereco,
                                    String urlAtestado,
                                    String urlAnexo1,
                                    String urlAnexo2,
                                    String urlAssinatura){
        StringBuilder email = new StringBuilder(texto.replace("</body>", "").replace("</html>", ""));
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Documentos:</div><img style=\"width: 100%;\" src=\"").append(urlDocs).append("\"/>");
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Comprovante de endereço:</div><img style=\"width: 100%;\" src=\"").append(urlComprovanteEndereco).append("\"/>");
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Atestado:</div><img style=\"width: 100%;\" src=\"").append(urlAtestado).append("\"/>");
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Anexo1:</div><img style=\"width: 100%;\" src=\"").append(urlAnexo1).append("\"/>");
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Anexo2:</div><img style=\"width: 100%;\" src=\"").append(urlAnexo2).append("\"/>");
        email.append("</body>");
        email.append("</html>");
        return email.toString();
    }

    /**
     * <AUTHOR> 06/05/2011
     */
    public String arranjarImagens(String chave, String texto, ContratoVO contrato, Boolean app) throws Exception {
        //criar a imagem temporaria
        String path = "/imagensCRM/email/tmp/";
        if(servletContext == null){
            return "";
        }
        File caminhoBase = new File(servletContext.getRealPath("/"));

        String pathFull = caminhoBase.getAbsolutePath() + path;
        String nomeImagem = Uteis.retirarAcentuacaoRegex(contrato.getEmpresa().getNome().replaceAll(" ", ""));
        if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
            try {
                String fotoKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, contrato.getEmpresa().getCodigo().toString());
                texto = texto.replaceAll("<img[^>]+src\\s*=\\s*['\"]acesso\\?emp*([^'\"]+)['\"][^>]*>", "<img src=\"" + Uteis.getPaintFotoDaNuvem(fotoKey) + "\" />");
            }catch (Exception e){
                //quando no tiver a foto do relatrio para a chave em questo, o midiaservice dispara uma exceo
            }

        }else{
            contrato.getEmpresa().setFotoRelatorio(DaoAuxiliar.retornarAcessoControle(chave).getEmpresaDao().obterFoto(chave,
                    contrato.getEmpresa().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
            UteisEmail.criarImagem(pathFull, contrato.getEmpresa().getFotoRelatorio(), nomeImagem + ".jpg");
            path = (app ? ".." : ".") + path + nomeImagem + ".jpg";
            texto = texto.replaceAll("<img[^>]+src\\s*=\\s*['\"]acesso\\?emp*([^'\"]+)['\"][^>]*>", "<img src=\"" + path + "\" />");
        }
        return texto;
    }

    public ContratoVO contratoPreparado(String chave, Integer codigoContrato) throws Exception{

        ContratoVO c = DaoAuxiliar.retornarAcessoControle(chave).getContratoDao().consultarPorChavePrimaria(codigoContrato, Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
        // setar atributos do contrato
        c.setMovParcelaVOs(DaoAuxiliar.retornarAcessoControle(chave).getMovParcelaDao().consultarPorContratoNaoRenegociadaNegociada(c.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        ContratoTextoPadrao cdtpDao = new ContratoTextoPadrao(con);
        c.setContratoTextoPadrao(cdtpDao.consultarPorCodigoContrato(c.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        c.setPessoa(DaoAuxiliar.retornarAcessoControle(chave).getPessoaDao().consultarPorChavePrimaria(c.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        c.setEmpresa(DaoAuxiliar.retornarAcessoControle(chave).getEmpresaDao().consultarPorChavePrimaria(c.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        ContratoDuracao cdDao = new ContratoDuracao(con);
        c.setContratoDuracao(cdDao.consultarContratoDuracoes(c.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (c.isVendaCreditoTreino()){
            c.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(DaoAuxiliar.retornarAcessoControle(chave).getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(c.getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        c.setContratoHorario(DaoAuxiliar.retornarAcessoControle(chave).getContratoHorarioDao().consultarContratoHorarios(c.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        c.setContratoModalidadeVOs(DaoAuxiliar.retornarAcessoControle(chave).getContratoModalidadeDao().consultarContratoModalidades(c.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
        c.setResponsavelContrato(DaoAuxiliar.retornarAcessoControle(chave).getUsuarioDao().consultarPorChavePrimaria(c.getResponsavelContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        c.getPlano().setPlanoTextoPadrao(c.getContratoTextoPadrao().getPlanoTextoPadrao());
        // pesquisar pagamentos j efetuados para informar no contrato.
        return c;
    }

    public void enviarEmail(String chave, Integer codigoContrato, Integer codigoUsuario,
                            String urlDocs,
                            String urlComprovanteEndereco,
                            String urlAtestado,
                            String urlAnexo1,
                            String urlAnexo2,
                            String urlAssinatura, String descMoedaEmpresa) throws Exception {
        ResultSet rsEmails = SuperFacadeJDBC.criarConsulta("SELECT email FROM email e "
                + "inner join contrato c on e.pessoa = c.pessoa and c.codigo = "+codigoContrato, con);
        List<String> emails = new ArrayList<String>();
        while(rsEmails.next()){
            emails.add(rsEmails.getString("email"));
        }
        if(emails.isEmpty()){
            return;
        }
        ContratoVO c = contratoPreparado(chave, codigoContrato);
        List<MovPagamentoVO> pagamentosDeUmContrato = DaoAuxiliar.retornarAcessoControle(chave).getMovPagamentoDao().consultarPagamentoDeUmContrato(c.getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_TODOS);
        UsuarioVO usuario = DaoAuxiliar.retornarAcessoControle(chave).getUsuarioDao().consultarPorChavePrimaria(codigoUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        enviarContrato(chave, usuario, emails.toArray(new String[emails.size()]), c,
                pagamentosDeUmContrato, getConfiguracaoSMTPNoReply(),
                urlDocs, urlComprovanteEndereco, urlAtestado, urlAnexo1, urlAnexo2, urlAssinatura, descMoedaEmpresa);
    }

    public ServletContext getServletContext() {
        return servletContext;
    }

    public void setServletContext(ServletContext servletContext) {
        this.servletContext = servletContext;
    }

    private String urlApresentar(String url) {
        boolean tratarUrl = isTratarUrl();
        String[] urlApresentarTmp = url.split("\\?time");
        String urlFinal = urlApresentarTmp[0];
        if (tratarUrl) {
            urlFinal = urlFinal.replace("*", "+");
        }
        return (tratarUrl ? "../" : "") + urlFinal;
    }

    private boolean isTratarUrl() {
        boolean tratarUrl = false;
        try {
            tratarUrl = MidiaService.getInstance().getClass().equals(MidiaZWInternal.class);
        } catch (Exception ignored) {

        }
        return tratarUrl;
    }

    public void notificarAssinaturaParaAcesso(String chave, Integer codigoPessoa) {
        try {
            SituacaoClienteSinteticoDW situacaoClienteDAO  = new SituacaoClienteSinteticoDW(con);
            situacaoClienteDAO.atualizarBaseOffLineZillyonAcesso(chave, codigoPessoa);
            situacaoClienteDAO = null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public void registrarLogProduto(ProdutoAssinaturaDigitalVO obj, UsuarioVO usuarioResponsavel, Integer codigoPessoa) throws Exception {
        Log logFacade = new Log(con);
        try {
            if(!UteisValidacao.emptyString(obj.getAnexoCancelamento())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO PRODUTO", "Incluir Assinatura Digital - Contrato Produto", "anexoCancelamento", "", obj.getAnexoCancelamento());
            }
            if(!UteisValidacao.emptyString(obj.getDocumentos())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO PRODUTO", "Incluir Assinatura Digital - Contrato Produto", "documentos", "", obj.getDocumentos());
            }
            if(!UteisValidacao.emptyString(obj.getEndereco())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO PRODUTO", "Incluir Assinatura Digital - Contrato Produto", "endereco", "", obj.getEndereco());
            }
            if(!UteisValidacao.emptyString(obj.getAssinatura())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO PRODUTO", "Incluir Assinatura Digital - Contrato Produto", "assinatura", "", obj.getAssinatura());
            }
            if(!UteisValidacao.emptyString(obj.getAtestado())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO PRODUTO", "Incluir Assinatura Digital - Contrato Produto", "atestado", "", obj.getAtestado());
            }
            if(!UteisValidacao.emptyString(obj.getAnexo1())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO PRODUTO", "Incluir Assinatura Digital - Contrato Produto", "anexo1", "", obj.getAnexo1());
            }
            if(!UteisValidacao.emptyString(obj.getAnexo2())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO PRODUTO", "Incluir Assinatura Digital - Contrato Produto", "anexo2", "", obj.getAnexo2());
            }
        } catch (Exception ex){
            registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                    "ERRO AO CRIAR LOG", "CONTRATO PRODUTO", "Incluir Assinatura Digital - Contrato Produto", "Erro", "", "");
        }
    }


    private void registrarLog(Integer codigoPessoa, UsuarioVO usuarioSessao, Log logDAO,
                              String operacao, String entidade, String entidadeDescricao, String nomeCampo, String valorAnterior, String valorAlterado) throws Exception {
        LogVO log = new LogVO();
        log.setOperacao(operacao);
        if ("Erro".equals(nomeCampo)) {
            log.setChavePrimaria("");
        } else {
            log.setChavePrimaria(codigoPessoa.toString());
        }
        if (usuarioSessao == null) {
            log.setResponsavelAlteracao("WEBSERVICE");
        } else {
            log.setResponsavelAlteracao(usuarioSessao.getNome());
            log.setUserOAMD(usuarioSessao.getUserOamd());
        }
        log.setNomeEntidade(entidade);
        log.setNomeEntidadeDescricao(entidadeDescricao);
        log.setDataAlteracao(Calendario.hoje());
        log.setNomeCampo(nomeCampo);
        log.setValorCampoAnterior(valorAnterior);
        log.setValorCampoAlterado(valorAlterado);
        log.setPessoa(codigoPessoa);
        logDAO.incluirSemCommit(log);
    }

    public void removerAssinaturaCliente(Integer contrato, Integer usuarioResponsavel) throws Exception {

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuarioResponsavel, Uteis.NIVELMONTARDADOS_MINIMOS);

        String problemaRemoverAssinatura = "";
        ProdutoAssinaturaDigitalVO assinaturaAnterior = null;
        CancelamentoAssinaturaDigitalVO assinaturaCancelAnterior = null;

            assinaturaAnterior = padDao.consultarPorContratoProduto(contrato);
            try {
                padDao.excluirAssinaturaProdutoContrato(contrato);
                MidiaService.getInstance().deleteObject(assinaturaAnterior.getAssinatura());
            } catch (Exception e) {
                problemaRemoverAssinatura = e.getMessage();
            }

        ProdutoTextoPadrao produtoTextoPadraoDAO = new ProdutoTextoPadrao(con);
        ProdutoTextoPadraoVO produtoTextoPadraoVO = produtoTextoPadraoDAO.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        produtoTextoPadraoDAO = null;

        int codPessoa = 0;
        if (!UteisValidacao.emptyNumber(produtoTextoPadraoVO.getVendaAvulsa())) {
            VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
            VendaAvulsaVO venda = vendaAvulsaDAO.consultarPorChavePrimaria(produtoTextoPadraoVO.getVendaAvulsa(), Uteis.NIVELMONTARDADOS_TODOS);
            codPessoa = venda.getCliente().getPessoa().getCodigo();
            vendaAvulsaDAO = null;
        } else if (!UteisValidacao.emptyNumber(produtoTextoPadraoVO.getAulaavulsadiaria())) {
            AulaAvulsaDiaria aulaAvulsaDiariaDAO = new AulaAvulsaDiaria(con);
            AulaAvulsaDiariaVO vendaDiaria = aulaAvulsaDiariaDAO.consultarPorChavePrimaria(produtoTextoPadraoVO.getAulaavulsadiaria(), Uteis.NIVELMONTARDADOS_TODOS);
            codPessoa = vendaDiaria.getCliente().getPessoa().getCodigo();
            aulaAvulsaDiariaDAO = null;
        } else {
            throw new Exception("Produto texto padrão sem venda avulsa ou aula avulsa diária associada");
        }

        Log logFacade = new Log(con);

        StringBuilder sb = new StringBuilder();
        if (UteisValidacao.emptyString(problemaRemoverAssinatura)) {
            registrarLog(codPessoa, usuarioVO, logFacade,
                    "REMOVER ASSINATURA DIGITAL", "CONTRATO PRODUTO", "REMOVER ASSINATURA DIGITAL DO CONTRATO DE PRODUTO", "Sucess", assinaturaAnterior.getAssinatura(), sb.toString());

            sb.append("Assinatura excluida com sucesso. ");
        }

    }
}
