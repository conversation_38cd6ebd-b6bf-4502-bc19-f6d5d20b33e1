package br.com.pactosolucoes.contrato.servico.impl;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.contrato.servico.intf.PlanoPersonalAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.PlanoPersonalAssinaturaDigitalVO;
import negocio.comuns.contrato.PlanoPersonalTextoPadraoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.PessoaFotoLocalAcesso;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.PlanoPersonalAssinaturaDigital;
import negocio.facade.jdbc.contrato.PlanoPersonalTextoPadrao;
import negocio.interfaces.contrato.PlanoPersonalAssinaturaDigitalInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.operacoes.midias.zwinternal.MidiaZWInternal;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.servlet.ServletContext;
import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;

public class PlanoPersonalAssinaturaDigitalServiceImpl implements PlanoPersonalAssinaturaDigitalServiceInterface {

    private Connection con;
    private PlanoPersonalAssinaturaDigitalInterfaceFacade cadDao;
    private ServletContext servletContext;

    public PlanoPersonalAssinaturaDigitalServiceImpl(Connection con) throws Exception {
        this.con = con;
        cadDao = new PlanoPersonalAssinaturaDigital(con);
    }

    @Override
    public Boolean validarToken(String token) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public JSONObject infoAcesso(String chave, Integer codigoUsuario, Integer empresaLogada) throws Exception {
//        Utiliza-se o método presente na Classe PlanoPersonalAssinaturaDigitalServiceImpl
        return null;
    }

    @Override
    public void incluirAssinatura(String chave, String token, Integer plano, Integer usuarioResponsavel, Integer planoTextoPadrao, String documentos, String assinatura, String endereco, String atestado, String anexo1, String anexo2, boolean updateAssinatura, boolean updateDocs, boolean updateEnd, boolean updateAte, boolean updateAnexo1, boolean updateAnexo2, String descMoedaEmpresa) throws Exception {
        PlanoPersonalAssinaturaDigitalVO assinaturaAnterior = cadDao.consultarPorPlano(plano);

        PlanoPersonalAssinaturaDigitalVO cad = new PlanoPersonalAssinaturaDigitalVO();

        cad.setTaxaPersonal(new ControleTaxaPersonalVO());
        cad.getTaxaPersonal().setCodigo(plano);

        cad.setUsuarioResponsavel(new UsuarioVO());
        cad.getUsuarioResponsavel().setCodigo(usuarioResponsavel);

        cad.setPlanoTextoPadrao(new PlanoPersonalTextoPadraoVO());
        cad.getPlanoTextoPadrao().setCodigo(planoTextoPadrao);

        cad.setDocumentos(uploadImagem(plano, MidiaEntidadeEnum.ANEXO_DOCUMENTOS_CONTRATO, assinaturaAnterior.getDocumentos(), documentos, updateDocs));
        cad.setEndereco(uploadImagem(plano, MidiaEntidadeEnum.ANEXO_ENDERECO_CONTRATO, assinaturaAnterior.getEndereco(), endereco, updateEnd));
        cad.setAssinatura(uploadImagem(plano, MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO, assinaturaAnterior.getAssinatura(), assinatura, updateAssinatura));
        cad.setAtestado(uploadImagem(plano, MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO, assinaturaAnterior.getAtestado(), atestado, updateAte));
        cad.setAnexo1(uploadImagem(plano, MidiaEntidadeEnum.ANEXO1, assinaturaAnterior.getAnexo1(), anexo1, updateAnexo1));
        cad.setAnexo2(uploadImagem(plano, MidiaEntidadeEnum.ANEXO2, assinaturaAnterior.getAnexo2(), anexo2, updateAnexo2));

        if(assinaturaAnterior == null
                || UteisValidacao.emptyNumber(assinaturaAnterior.getCodigo())){
            cadDao.incluir(cad);
        }else{
            cad.setCodigo(assinaturaAnterior.getCodigo());
            cadDao.alterar(cad);
        }
        enviarEmail(chave, plano, cad.getUsuarioResponsavel().getCodigo(), Uteis.getPaintFotoDaNuvem(cad.getDocumentos()),
                Uteis.getPaintFotoDaNuvem(cad.getEndereco()),
                Uteis.getPaintFotoDaNuvem(cad.getAssinatura()), descMoedaEmpresa);
    }

    public String uploadImagem(Integer codigoPlano, MidiaEntidadeEnum midia, String dataSource) throws Exception{
        if(UteisValidacao.emptyString(dataSource)){
            return "";
        }
        dataSource = dataSource.replace(" ","+");
        final String chave = DAO.resolveKeyFromConnection(con);
        final String identificador = codigoPlano + "_"+midia.name()+"_"+Calendario.hoje().getTime();
        String fotoKey = MidiaService.getInstance().uploadObjectFromByteArray(chave, midia,
                identificador, Base64.decodeBase64(dataSource));
        return fotoKey;
    }

    public String uploadImagem(Integer codigoPlano, MidiaEntidadeEnum midia, String anterior, String novo, boolean forceUpdate) throws Exception {
        final String chave = DAO.resolveKeyFromConnection(con);
        if (anterior != null && anterior.contains(chave)) {
            anterior = anterior.substring(anterior.indexOf(chave));
        }
        if(forceUpdate) {
            anterior = uploadImagem(codigoPlano, midia, novo);
        }
        return anterior;
    }

    public void enviarEmail(String chave, Integer planoCodigo, Integer codigoUsuario,
                            String urlDocs,
                            String urlComprovanteEndereco,
                            String urlAssinatura, String descMoedaEmpresa) throws Exception {
        ResultSet rsEmails = SuperFacadeJDBC.criarConsulta("SELECT email FROM email e, pessoa p, controletaxapersonal c, colaborador c2 " +
                "WHERE c.personal = c2.codigo " +
                "AND c2.pessoa = p.codigo " +
                "AND e.pessoa = p.codigo " +
                "AND e.pessoa = c2.pessoa " +
                "AND c.codigo = " + planoCodigo, con);
        List<String> emails = new ArrayList<String>();
        while(rsEmails.next()){
            emails.add(rsEmails.getString("email"));
        }
        if(emails.isEmpty()){
            return;
        }
        ControleTaxaPersonalVO c = planoPreparado(chave, planoCodigo);
        UsuarioVO usuario = DaoAuxiliar.retornarAcessoControle(chave).getUsuarioDao().consultarPorChavePrimaria(codigoUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        enviarContrato(chave, usuario, emails.toArray(new String[emails.size()]), c,
                SuperControle.getConfiguracaoSMTPNoReply(),
                urlDocs, urlComprovanteEndereco, urlAssinatura, descMoedaEmpresa);
    }

    public void enviarContrato(String chave, UsuarioVO usuario, String[] emails, ControleTaxaPersonalVO plano,
                               ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO,
                               String urlDocs,
                               String urlComprovanteEndereco,
                               String urlAssinatura, String descMoedaEmpresa) throws Exception {
        if (plano.getCodigo() != 0) {
            ColaboradorVO personal = DaoAuxiliar.retornarAcessoControle(chave).getColaboradorDao().consultarPorCodigoPessoa(plano.getPersonal().getPessoa().getCodigo(), plano.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            String texto = plano.getPlano().getPlanoTextoPadrao().substituirTagsTextoEnvioPlanoPersonal(chave, plano, personal, con, descMoedaEmpresa);
            UteisEmail email = new UteisEmail();
            texto = arranjarImagens(chave, texto, plano, false);
            texto = addDocsAssinatura(texto, urlDocs, urlComprovanteEndereco, urlAssinatura);

            email.novo(plano.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", configuracaoSistemaCRMVO);
            email.setRemetente(usuario);

            boolean existeEmailValido = false;
            for (String emailEnviar : emails) {
                if (UteisValidacao.validaEmail(emailEnviar)) {
                    existeEmailValido = true;
                }
            }
            if (existeEmailValido) {
                email.enviarEmailN(emails, texto, plano.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", "");
            } else {
                throw new Exception("Não foi possível enviar o contrato pois o colaborador não possui um email válido.");
            }
        } else {
            throw new Exception("Não foi possível enviar o contrato. Dados não encontrados!");
        }
    }

    public String addDocsAssinatura(String texto, String urlDocs,
                                    String urlComprovanteEndereco,
                                    String urlAssinatura){
        StringBuilder email = new StringBuilder(texto.replace("</body>", "").replace("</html>", ""));
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Documentos:</div><img style=\"width: 100%;\" src=\"").append(urlDocs).append("\"/>");
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Comprovante de endereço:</div><img style=\"width: 100%;\" src=\"").append(urlComprovanteEndereco).append("\"/>");
        email.append("</body>");
        email.append("</html>");
        return email.toString();
    }

    @Override
    public JSONObject consultarPlanosPersonal(boolean assinados, String filtro, Integer empresa, boolean todos) throws Exception {
        JSONObject retorno = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        // Nesse momento o sistema atualiza os planos personal assinar
        //cadDao.atualizaPlanoPersonalParaAssinar();
        List<ControleTaxaPersonalVO> planos = cadDao.consultarPlanos(assinados, filtro, empresa, todos);
        for(ControleTaxaPersonalVO c : planos){
            JSONObject j = new JSONObject();
            j.put("nomePersonal", Uteis.getNomeAbreviado(c.getPersonal().getPessoa().getNome().toLowerCase()));
            j.put("codigoPersonal", c.getPersonal().getCodigo());
            j.put("taxaPersonal", c.getCodigo());
            j.put("urlFotoPersonal", c.getPersonal().getPessoa().getUrlFoto());
            if(assinados){
                j.put("assinadoemPersonal", Uteis.getDataAplicandoFormatacao(c.getAssinadoEm(), "'Assinado em' dd/MM/yyyy 'às' HH:mm"));
            }
            jsonArray.put(j);
        }
        Integer count = cadDao.countPlanos(assinados, filtro, empresa);
        retorno.put("planosPersonal", jsonArray);
        retorno.put("nrPlanosPersonal", count);
        retorno.put("excede", count > 20);
        return retorno;
    }

    @Override
    public JSONObject selecionarPlanoPersonal(String key, Integer codigoPlano, String descMoedaEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ctp.planotextopadrao as planopersonaltextopadrao FROM planopersonaltextopadrao ctp ");
        sql.append(" INNER JOIN planotextopadrao ptp ON ptp.codigo = ctp.planotextopadrao ");
        sql.append(" WHERE ctp.taxapersonal = ").append(codigoPlano);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if(rs.next()){
            JSONObject json = new JSONObject();
            json.put("taxaPersonal", codigoPlano);
            ControleTaxaPersonalVO plano = planoPreparado(key, codigoPlano);
            ColaboradorVO personal = DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().consultarPorCodigoPessoa(plano.getPersonal().getPessoa().getCodigo(), plano.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            String texto = plano.getPlano().getPlanoTextoPadrao().substituirTagsTextoEnvioPlanoPersonal(key, plano, personal, con, descMoedaEmpresa);
            texto = texto.replaceAll("__4ssin4tur4digit4l__", "<b>A assinatura digital será colocada aqui.</b>");
            texto = texto.replaceAll("__4ssin4tur4digit4l2__", "<b>A assinatura digital resp. financeiro será colocada aqui.</b>");
            texto = arranjarImagens(key, texto, plano, true);
            json.put("textoPersonal", texto);
            json.put("modeloPersonal", rs.getInt("planopersonaltextopadrao"));
            return json;
        }
        return null;
    }

    @Override
    public void incluirAssinaturaSimples(String chave, Integer plano, String assinatura) throws Exception {
        PlanoPersonalAssinaturaDigitalVO assinaturaAnterior = cadDao.consultarPorPlano(plano);
        PlanoPersonalAssinaturaDigitalVO cad = new PlanoPersonalAssinaturaDigitalVO();
        cad.setTaxaPersonal(new ControleTaxaPersonalVO());
        cad.getTaxaPersonal().setCodigo(plano);
        cad.setUsuarioResponsavel(new UsuarioVO());
        ResultSet rsresponsavel = criarConsulta("select responsavel from controletaxapersonal where codigo = " + plano,
                con);
        if(rsresponsavel.next()){
            cad.getUsuarioResponsavel().setCodigo(rsresponsavel.getInt("responsavel"));
        }

        ResultSet rstexto = criarConsulta("select codigo from planopersonaltextopadrao where taxapersonal = " + plano,
                con);
        cad.setPlanoTextoPadrao(new PlanoPersonalTextoPadraoVO());
        if(rstexto.next()){
            cad.getPlanoTextoPadrao().setCodigo(rstexto.getInt("planotextopadrao"));
        }

        cad.setAssinatura(uploadImagem(plano, MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO, assinatura));

        if(assinaturaAnterior == null
                || UteisValidacao.emptyNumber(assinaturaAnterior.getCodigo())){
            cadDao.incluir(cad);
        }else{
            cad.setCodigo(assinaturaAnterior.getCodigo());
            cad.setAssinatura(assinaturaAnterior.getAssinatura());
            cadDao.alterar(cad);
        }
    }

    private String urlApresentar(String url) {
        boolean tratarUrl = isTratarUrl();
        String[] urlApresentarTmp = url.split("\\?time");
        String urlFinal = urlApresentarTmp[0];
        if (tratarUrl) {
            urlFinal = urlFinal.replace("*", "+");
        }
        return (tratarUrl ? "../" : "") + urlFinal;
    }

    private boolean isTratarUrl() {
        boolean tratarUrl = false;
        try {
            tratarUrl = MidiaService.getInstance().getClass().equals(MidiaZWInternal.class);
        } catch (Exception ignored) {

        }
        return tratarUrl;
    }

    @Override
    public JSONObject visualizarPlanoPersonal(Integer contrato, String url) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT documentos, endereco, assinatura, atestado, anexo1, anexo2 FROM planopersonalassinaturadigital where taxapersonal = ").append(contrato);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            JSONObject json = new JSONObject();
            json.put("documentosPersonal", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("documentos"), url)));
            json.put("enderecoPersonal", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("endereco"), url)));
            json.put("planoPersonal", contrato);
            json.put("assinaturaPersonal", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("assinatura"), url)));
            json.put("atestadoPersonal", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("atestado"), url)));
            json.put("anexo1Personal", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("anexo1"), url)));
            json.put("anexo2Personal", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("anexo2"), url)));
            return json;
        }
        return null;
    }

    @Override
    public ServletContext getServletContext() {
        return servletContext;
    }

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.servletContext = servletContext;
    }

    @Override
    public String alterarFotoPersonal(String chave, Integer taxaPersonal, Integer codigoPessoa, String foto, Integer usuarioLogado, UsuarioVO usuarioSessao) throws Exception {
        if (UteisValidacao.emptyString(foto)) {
            return "";
        }

        if (taxaPersonal != null) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT col.pessoa AS pessoa " +
                    "FROM controletaxapersonal ctp, colaborador col, pessoa pes " +
                    "WHERE ctp.personal = col.codigo " +
                    "AND pes.codigo = col.pessoa " +
                    "AND ctp.codigo = " + taxaPersonal, con);
            if (rs.next()) {
                codigoPessoa = rs.getInt("pessoa");
            }
        }
        byte[] fotoByte = Base64.decodeBase64(foto);
        String fotoKey;
        if(codigoPessoa > 0){
            fotoKey = MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_PESSOA,
                    codigoPessoa.toString(), fotoByte);

            String sql = "UPDATE Pessoa set fotokey = ? WHERE codigo = ?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, fotoKey + "?time=" + Calendario.hoje().getTime());
            sqlAlterar.setInt(2, codigoPessoa);
            sqlAlterar.execute();

            String problemaRemoverPessoaAcesso = "";
            try {
                PessoaFotoLocalAcesso fotoLocal = new PessoaFotoLocalAcesso(con);
                fotoLocal.excluirFotoPessoaLocalAcesso(codigoPessoa);
                fotoLocal = null;
            } catch (Exception e) {
                System.out.println("Problema ao remover a pessoa " + codigoPessoa + " de PessoaFotoLocalAcesso.");
                problemaRemoverPessoaAcesso = e.getMessage();
            }

            String problemaTrocarFotoTreino = "";
            try {
                String urlTreino = PropsService.getPropertyValue(chave, PropsService.urlTreino);
                Map<String, String> params = new HashMap<String, String>();
                params.put("codigopessoa", String.valueOf(codigoPessoa));
                params.put("fotoKey", fotoKey);
                ExecuteRequestHttpService.executeRequest(urlTreino + "/prest/config/" + chave + "/baixarFotoAlunoPorFotoKey", params);
            } catch (Exception ex) {
                System.out.println("Problema ao enviar solicitação de trocar a foto do aluno");
                problemaTrocarFotoTreino = ex.getMessage();
            }

            Log logFacade = new Log(con);
            try {
                StringBuilder sb = new StringBuilder();
                if (UteisValidacao.emptyString(problemaRemoverPessoaAcesso) && UteisValidacao.emptyString(problemaTrocarFotoTreino)) {
                    sb.append("Foto alterada com sucesso. ");
                } else {
                    sb.append("Foto alterada parcialmente. ");
                }
                if (!UteisValidacao.emptyString(problemaRemoverPessoaAcesso)) {
                    sb.append("Problemas ao alterar foto no Acesso: ").append(problemaRemoverPessoaAcesso);
                }
                if (!UteisValidacao.emptyString(problemaTrocarFotoTreino)) {
                    sb.append("Problemas ao alterar a foto no Treino: ").append(problemaTrocarFotoTreino);
                }

                registrarLog(codigoPessoa, usuarioSessao, logFacade,
                        "ALTERAR FOTO", "PESSOA", "Alterar Foto - Pessoa", "Foto", "", sb.toString());
            } catch (Exception ex) {
                registrarLog(codigoPessoa, usuarioSessao, logFacade,
                        "ERRO AO CRIAR LOG", "PESSOA", "Alterar Foto - Pessoa", "Erro", "", "");
            }
        }else{
            File f = new File(PropsService.getPropertyValue(PropsService.diretorioArquivos)
                    + "/fotos_pessoa_temp");
            f.getParentFile().mkdirs();
            File tmp = new File(String.format(f.getPath()+"/%s@%s@%s.jpg", chave, usuarioLogado, 0));
            FileUtilities.saveToFile(fotoByte, tmp.getPath());
            fotoKey = "";
        }
        return fotoKey;
    }

    @Override
    public JSONObject consultarParaAtestado(String filtro, Integer empresa) throws Exception {
        return null;
    }

    @Override
    public void salvarImagens(String chave, Integer planoPersonal, UsuarioVO usuarioSessao, String imagemDocumentos, boolean documentosUpdate, String imagemEndereco, boolean enderecoUpdate, String imagemAtestado, boolean atestadoUpdate, String imagemAnexo1, boolean anexo1Update, String imagemAnexo2, boolean anexo2Update) throws Exception {
        PlanoPersonalAssinaturaDigitalVO assinaturaAnterior = cadDao.consultarPorPlano(planoPersonal);

        PlanoPersonalAssinaturaDigitalVO cad = new PlanoPersonalAssinaturaDigitalVO();
        cad.setTaxaPersonal(new ControleTaxaPersonalVO());
        cad.getTaxaPersonal().setCodigo(planoPersonal);
        cad.setUsuarioResponsavel(usuarioSessao);
        cad.setDocumentos(uploadImagem(planoPersonal, MidiaEntidadeEnum.ANEXO_DOCUMENTOS_CONTRATO, assinaturaAnterior.getDocumentos(), imagemDocumentos, documentosUpdate));
        cad.setEndereco(uploadImagem(planoPersonal, MidiaEntidadeEnum.ANEXO_ENDERECO_CONTRATO, assinaturaAnterior.getEndereco(), imagemEndereco, enderecoUpdate));
        cad.setAtestado(uploadImagem(planoPersonal, MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO, assinaturaAnterior.getAtestado(), imagemAtestado, atestadoUpdate));
        cad.setAnexo1(uploadImagem(planoPersonal, MidiaEntidadeEnum.ANEXO1, assinaturaAnterior.getAnexo1(), imagemAnexo1, anexo1Update));
        cad.setAnexo2(uploadImagem(planoPersonal, MidiaEntidadeEnum.ANEXO2, assinaturaAnterior.getAnexo2(), imagemAnexo2, anexo2Update));

        if (UteisValidacao.emptyNumber(assinaturaAnterior.getCodigo())) {
            PlanoPersonalTextoPadrao cdtpDao = new PlanoPersonalTextoPadrao(con);
            cad.setPlanoTextoPadrao(cdtpDao.consultarPorCodigoPlanoPersonal(planoPersonal, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            cadDao.incluir(cad);
        } else {
            cad.setCodigo(assinaturaAnterior.getCodigo());
            cad.setAssinatura(assinaturaAnterior.getAssinatura());
            cadDao.alterar(cad);
        }
    }

    public String arranjarImagens(String chave, String texto, ControleTaxaPersonalVO plano, Boolean app) throws Exception {
        //criar a imagem temporaria
        String path = "/imagensCRM/email/tmp/";
        if(servletContext == null){
            return "";
        }
        File caminhoBase = new File(servletContext.getRealPath("/"));

        String pathFull = caminhoBase.getAbsolutePath() + path;
        String nomeImagem = Uteis.retirarAcentuacaoRegex(plano.getEmpresa().getNome().replaceAll(" ", ""));
        if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
            try {
                String fotoKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, plano.getEmpresa().getCodigo().toString());
                texto = texto.replaceAll("<img[^>]+src\\s*=\\s*['\"]acesso\\?emp*([^'\"]+)['\"][^>]*>", "<img src=\"" + Uteis.getPaintFotoDaNuvem(fotoKey) + "\" />");
            }catch (Exception e){
                //quando não tiver a foto do relatório para a chave em questão, o midiaservice dispara uma exceção
            }

        }else{
            plano.getEmpresa().setFotoRelatorio(DaoAuxiliar.retornarAcessoControle(chave).getEmpresaDao().obterFoto(chave,
                    plano.getEmpresa().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
            UteisEmail.criarImagem(pathFull, plano.getEmpresa().getFotoRelatorio(), nomeImagem + ".jpg");
            path = (app ? ".." : ".") + path + nomeImagem + ".jpg";
            texto = texto.replaceAll("<img[^>]+src\\s*=\\s*['\"]acesso\\?emp*([^'\"]+)['\"][^>]*>", "<img src=\"" + path + "\" />");
        }
        return texto;
    }

    public ControleTaxaPersonalVO planoPreparado(String chave, Integer codigoPlano) throws Exception{

        ControleTaxaPersonalVO c = DaoAuxiliar.retornarAcessoControle(chave).getTaxapersonalDao().consultarPorChavePrimaria(codigoPlano, Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);

        PlanoPersonalTextoPadrao cdtpDao = new PlanoPersonalTextoPadrao(con);
        c.getPersonal().setAutorizacoes(DaoAuxiliar.retornarAcessoControle(chave).getAutorizacaoCobrancaColaboradorDao().consultarPorColaborador(c.getPersonal().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        c.setPlanoPersonalTextoPadrao(cdtpDao.consultarPorCodigoPlanoPersonal(c.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        c.setPersonal(DaoAuxiliar.retornarAcessoControle(chave).getColaboradorDao().consultarPorChavePrimaria(c.getPersonal().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        c.setEmpresa(DaoAuxiliar.retornarAcessoControle(chave).getEmpresaDao().consultarPorChavePrimaria(c.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        c.setResponsavel(DaoAuxiliar.retornarAcessoControle(chave).getUsuarioDao().consultarPorChavePrimaria(c.getResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        c.getPlano().setPlanoTextoPadrao(c.getPlanoPersonalTextoPadrao().getPlanoTextoPadrao());
        return c;
    }

    private void registrarLog(Integer codigoPessoa, UsuarioVO usuarioSessao, Log logDAO,
                              String operacao, String entidade, String entidadeDescricao, String nomeCampo, String valorAnterior, String valorAlterado) throws Exception {
        LogVO log = new LogVO();
        log.setOperacao(operacao);
        if ("Erro".equals(nomeCampo)) {
            log.setChavePrimaria("");
        } else {
            log.setChavePrimaria(codigoPessoa.toString());
        }
        if (usuarioSessao == null) {
            log.setResponsavelAlteracao("WEBSERVICE");
        } else {
            log.setResponsavelAlteracao(usuarioSessao.getNome());
            log.setUserOAMD(usuarioSessao.getUserOamd());
        }
        log.setNomeEntidade(entidade);
        log.setNomeEntidadeDescricao(entidadeDescricao);
        log.setDataAlteracao(Calendario.hoje());
        log.setNomeCampo(nomeCampo);
        log.setValorCampoAnterior(valorAnterior);
        log.setValorCampoAlterado(valorAlterado);
        log.setPessoa(codigoPessoa);
        logDAO.incluirSemCommit(log);
    }


    public void removerAssinaturaPersonal(Integer codigoTaxa, Integer usuarioResponsavel) throws Exception {
        if (UteisValidacao.emptyNumber(codigoTaxa)) {
            throw new IllegalArgumentException("Código do Taxa Personal é obrigatório para remoção da assinatura.");
        }
        if (UteisValidacao.emptyNumber(usuarioResponsavel)) {
            throw new IllegalArgumentException("Usuário responsável é obrigatório para remoção da assinatura.");
        }        UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(DAO.resolveKeyFromConnection(con))
                .getUsuarioDao().consultarPorChavePrimaria(usuarioResponsavel, Uteis.NIVELMONTARDADOS_MINIMOS);
        String problemaRemoverAssinatura = "";
        PlanoPersonalAssinaturaDigitalVO assinaturaAnterior = null;

        try {
            assinaturaAnterior = cadDao.consultarPorPlano(codigoTaxa);
            if (assinaturaAnterior != null && !UteisValidacao.emptyString(assinaturaAnterior.getAssinatura())) {
                MidiaService.getInstance().deleteObject(assinaturaAnterior.getAssinatura());
            }
            cadDao.excluirAssinaturaPersonal(codigoTaxa);

        } catch (Exception e) {
            problemaRemoverAssinatura = e.getMessage();
        }
        String chave = DAO.resolveKeyFromConnection(con);
        ControleTaxaPersonalVO controleTaxaPersonal = DaoAuxiliar.retornarAcessoControle(chave)
                .getTaxapersonalDao().consultarPorChavePrimaria(codigoTaxa, Uteis.NIVELMONTARDADOS_TODOS);

        Log logFacade = new Log(con);

        if (UteisValidacao.emptyString(problemaRemoverAssinatura)) {
            registrarLog(controleTaxaPersonal.getPersonal().getPessoa().getCodigo(), usuarioVO, logFacade,
                    "REMOVER ASSINATURA DIGITAL", "PLANO PERSONAL", "REMOVER ASSINATURA DIGITAL DO PLANO PERSONAL",
                    "Success", assinaturaAnterior != null ? assinaturaAnterior.getAssinatura() : "",
                    "Assinatura excluída com sucesso.");
        } else {
            throw new Exception("Erro ao remover assinatura: " + problemaRemoverAssinatura);
        }
    }

}
