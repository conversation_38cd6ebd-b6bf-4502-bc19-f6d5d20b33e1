/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.contrato.servico.impl;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.contrato.servico.intf.ContratoAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.acesso.DadosAcessoOfflineVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaAnexoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.PessoaAnexoEnum;
import negocio.comuns.contrato.AditivoVO;
import negocio.comuns.contrato.CancelamentoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoTextoPadraoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.PessoaFotoLocalAcesso;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.PessoaAnexo;
import negocio.facade.jdbc.contrato.CancelamentoAssinaturaDigital;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoAssinaturaDigital;
import negocio.facade.jdbc.contrato.ContratoDuracao;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.PessoaAnexoInterfaceFacade;
import negocio.interfaces.contrato.CancelamentoAssinaturaDigitalInterfaceFacade;
import negocio.interfaces.contrato.ContratoAssinaturaDigitalInterfaceFacade;
import negocio.interfaces.contrato.ContratoTextoPadraoInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.operacoes.midias.zwinternal.MidiaZWInternal;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.servlet.ServletContext;
import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static controle.arquitetura.SuperControle.getConfiguracaoSMTPNoReply;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;

/**
 *
 * <AUTHOR>
 */
public class ContratoAssinaturaDigitalServiceImpl implements ContratoAssinaturaDigitalServiceInterface{

    private Connection con;
    private ContratoAssinaturaDigitalInterfaceFacade cadDao;
    private CancelamentoAssinaturaDigitalInterfaceFacade cadCancelDao;
    private PessoaAnexoInterfaceFacade pesDao;
    private UsuarioInterfaceFacade usuarioDao;
    private ContratoTextoPadraoInterfaceFacade conTexPadraoDao;
    private ServletContext servletContext;

    public ContratoAssinaturaDigitalServiceImpl(Connection con) throws Exception {
        this.con = con;
        cadDao = new ContratoAssinaturaDigital(con);
        cadCancelDao = new CancelamentoAssinaturaDigital(con);
        pesDao = new PessoaAnexo(con);
        usuarioDao = new Usuario(con);
        conTexPadraoDao = new ContratoTextoPadrao(con);
    }

    @Override
    public void incluirAssinatura(String chave, String token, Integer contrato, String ip, Integer usuarioResponsavel,
                                  Integer contratoTextoPadrao, String documentos, String assinatura, String endereco,
                                  String atestado, String anexo1, String anexo2, boolean updateAssinatura, boolean updateDocs,
                                  boolean updateEnd, boolean updateAte, boolean updateAnexo1, boolean updateAnexo2,
                                  String descMoedaEmpresa, Boolean assinaturaCancelamento, String urlRequest, String assinatura2, Integer aditivo) throws Exception {
        final ContratoAssinaturaDigitalVO assinaturaAnterior = cadDao.consultarPorContratoEAditivo(contrato, aditivo);

        final ContratoAssinaturaDigitalVO cad = new ContratoAssinaturaDigitalVO();

        if (aditivo == null) {
            cad.setContrato(new ContratoVO());
            cad.getContrato().setCodigo(contrato);
        } else {
            cad.setContratoAditivo(new ContratoVO());
            cad.getContratoAditivo().setCodigo(contrato);

            cad.setEhAditivo(true);
            cad.setAditivo(new AditivoVO());
            cad.getAditivo().setCodigo(aditivo);
        }

        cad.setUsuarioResponsavel(new UsuarioVO());
        cad.getUsuarioResponsavel().setCodigo(usuarioResponsavel);

        cad.setContratoTextoPadrao(new ContratoTextoPadraoVO());
        cad.getContratoTextoPadrao().setCodigo(contratoTextoPadrao);

        cad.setDocumentos(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_DOCUMENTOS_CONTRATO, assinaturaAnterior.getDocumentos(), documentos, updateDocs));
        cad.setEndereco(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_ENDERECO_CONTRATO, assinaturaAnterior.getEndereco(), endereco, updateEnd));

        if (assinaturaCancelamento != null && !assinaturaCancelamento) {
            cad.setAssinatura(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO, assinaturaAnterior.getAssinatura(), assinatura, updateAssinatura));
            cad.setAssinatura2(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO2, assinaturaAnterior.getAssinatura2(), assinatura2, updateAssinatura));
        } else if (assinaturaAnterior != null && !UteisValidacao.emptyString(assinaturaAnterior.getAssinatura())) {
            cad.setAssinatura(assinaturaAnterior.getAssinatura());
        } else {
            cad.setAssinatura("");
        }

        cad.setAtestado(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO, assinaturaAnterior.getAtestado(), atestado, updateAte));
        cad.setAnexo1(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO1, assinaturaAnterior.getAnexo1(), anexo1, updateAnexo1));
        cad.setAnexo2(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO2, assinaturaAnterior.getAnexo2(), anexo2, updateAnexo2));

        if(assinaturaAnterior == null || UteisValidacao.emptyNumber(assinaturaAnterior.getCodigo())){
            cadDao.incluir(cad);
        } else {
            cad.setCodigo(assinaturaAnterior.getCodigo());
            cadDao.alterar(cad);
        }

        atualizarImpressaoContrato(contrato, usuarioResponsavel, aditivo);

        if (!assinaturaCancelamento) {
            try {
                enviarEmail(chave, contrato, cad.getUsuarioResponsavel().getCodigo(), Uteis.getPaintFotoDaNuvem(cad.getDocumentos()),
                        Uteis.getPaintFotoDaNuvem(cad.getEndereco()),
                        Uteis.getPaintFotoDaNuvem(cad.getAtestado()),
                        Uteis.getPaintFotoDaNuvem(cad.getAnexo1()),
                        Uteis.getPaintFotoDaNuvem(cad.getAnexo2()),
                        Uteis.getPaintFotoDaNuvem(cad.getAssinatura()), descMoedaEmpresa);
            } catch (Exception ignored) {
            }
        }

        UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(usuarioResponsavel, Uteis.NIVELMONTARDADOS_MINIMOS);

        Contrato contratoDao = new Contrato(con);
        ContratoVO contratoVO = contratoDao.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        contratoDao = null;

        CancelamentoAssinaturaDigitalVO assCancelamento = null;
        if (assinaturaCancelamento) {
            CancelamentoAssinaturaDigitalVO assinaturaCancelamentoAnterior = cadDao.consultarPorContratoCancelamento(contrato);
            assCancelamento = new CancelamentoAssinaturaDigitalVO();
            ContratoOperacao contratoOperacaoDAO = new ContratoOperacao(con);
            assCancelamento.setContratoOperacao(contratoOperacaoDAO.consultarPorTipoOperacaoCodigoContrato(contrato, "CA", false, Uteis.NIVELMONTARDADOS_MINIMOS));
            contratoOperacaoDAO = null;
            Empresa empresaDAO = new Empresa(con);
            contratoVO.setEmpresa(empresaDAO.consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            empresaDAO = null;
            assCancelamento.getContratoOperacao().setNome(contratoVO.getPessoa().getNome());
            contratoVO.setCancelamentoContratoVO(assCancelamento.getContratoOperacao());
            assCancelamento.setContrato(contratoVO);
            assCancelamento.setUsuarioResponsavel(usuarioVO);
            assCancelamento.setAssinatura(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO, assinaturaCancelamentoAnterior.getAssinatura(), assinatura, updateAssinatura));

            if(assinaturaCancelamentoAnterior == null
                    || UteisValidacao.emptyNumber(assinaturaCancelamentoAnterior.getCodigo())){
                cadCancelDao.incluir(assCancelamento);
            }else{
                assCancelamento.setCodigo(assinaturaCancelamentoAnterior.getCodigo());
                cadCancelDao.alterar(assCancelamento);
            }

            enviarEmailCancelamento(contratoVO, urlApresentar(Uteis.getPaintImagemDaNuvem(assCancelamento.getAssinatura(), urlRequest)));
        }

        registrarLog(cad, usuarioVO, contratoVO.getPessoa().getCodigo());
        if (assinaturaCancelamento != null && assinaturaCancelamento) {
            registrarLogCancelamento(assCancelamento, usuarioVO, contratoVO.getPessoa().getCodigo());
        }
        notificarAssinaturaParaAcesso(chave, contratoVO.getPessoa().getCodigo());
    }


    public String uploadImagem(Integer codigoContrato, MidiaEntidadeEnum midia, String dataSource) throws Exception{
        if(UteisValidacao.emptyString(dataSource)){
            return "";
        }
        if (dataSource.startsWith("data:") && dataSource.contains(";base64,")) {
            dataSource = dataSource.split(";base64,")[1];
        }
        dataSource = dataSource.replace(" ","+");
        final String chave = DAO.resolveKeyFromConnection(con);

//        final String identificador = codigoContrato+"_"+midia.name()+"_"+Calendario.hoje().getTime();
        //identificador não pode ser maior q 48 caracteres
        //não precisa ter o tipo de midia name pois no s3 já separa por pasta (que é o tipo de midia)

        final String identificador = codigoContrato + "_MIDIA_" + midia.getCodigo() + "_" + Calendario.hoje().getTime();
        return MidiaService.getInstance().uploadObjectFromByteArray(chave, midia, identificador, Base64.decodeBase64(dataSource));
    }

    public String uploadImagem(Integer codigoContrato, MidiaEntidadeEnum midia, String anterior, String novo, boolean forceUpdate) throws Exception {
        final String chave = DAO.resolveKeyFromConnection(con);
        if (anterior != null && anterior.contains(chave)) {
            anterior = anterior.substring(anterior.indexOf(chave));
        }
        if(forceUpdate) {
            anterior = uploadImagem(codigoContrato, midia, novo);
        }
        return anterior;
    }

    @Override
    public JSONObject consultarContratos(boolean assinados, String filtro, Integer empresa, boolean todos, Boolean contratosCancelados) throws Exception {
        JSONObject retorno = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        List<ContratoVO> contratos = cadDao.consultarContratos(assinados, filtro, empresa, todos, contratosCancelados);

        for(ContratoVO c : contratos){
            final JSONObject j = new JSONObject();
            j.put("nome", Uteis.getNomeAbreviado(c.getPessoa().getNome().toLowerCase()));
            j.put("matricula", c.getCliente().getCodigoMatricula());
            j.put("contrato", c.getCodigo());
            j.put("urlFoto", c.getPessoa().getUrlFoto());
            j.put("ehAditivo", c.isEhAditivo());
            j.put("aditivo", c.getAditivo());

            if(assinados){
                if(c.getIpassinaturacontrato()!=null){
                    j.put("assinadoem", Uteis.getDataAplicandoFormatacao(c.getDataAssinaturaContrato(), "'Assinado via email em ' dd/MM/yyyy 'as' HH:mm"));
                    j.put("cpf", c.getPessoa().getCfp());
                    j.put("ipassinaturacontrato", c.getIpassinaturacontrato());
                    j.put("emailrecebimento", c.getEmailRecebimento());
                    j.put("dataAssinatura", Uteis.getDataAplicandoFormatacao(c.getDataAssinaturaContrato(), " dd/MM/yyyy 'as' HH:mm"));
                }else{
                    j.put("assinadoem", "");
                    j.put("cpf","");
                    j.put("ipassinaturacontrato", "");
                    j.put("emailrecebimento", "");
                    j.put("assinadoem", Uteis.getDataAplicandoFormatacao(c.getAssinadoEm(), (c.isEhAditivo() ? "'Aditivo assinado" : "'Assinado") + " em ' dd/MM/yyyy 'as' HH:mm"));
                }
            }

            if(contratosCancelados != null && contratosCancelados){
                j.put("justificativaCancelamento", c.getCancelamentoContratoVO().getJustificativa());
                j.put("msgCancelamento", !UteisValidacao.emptyString(c.getCancelamentoContratoVO().getDescricaoCalculo()) ?
                        c.getCancelamentoContratoVO().getDescricaoCalculo()
                                .replaceAll("\\.", ".</br>")
                                .replaceAll("\n", "</br>") : "");
            }

            jsonArray.put(j);
        }

        final Integer count = cadDao.countContratos(assinados, filtro, empresa, contratosCancelados);
        retorno.put("contratos", jsonArray);
        retorno.put("nr", count);
        retorno.put("excede", count > 20);
        return retorno;
    }

    @Override
    public JSONObject consultarContratosTermoResponsabilidade(boolean assinados, String filtro, Integer empresa, boolean todos, boolean isTermoResponsabilidadeExAluno) throws Exception {
        JSONObject retorno = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        List<ContratoVO> contratos = cadDao.consultarTermoResponsabilidade(assinados, filtro, empresa, todos, isTermoResponsabilidadeExAluno);
        for(ContratoVO c : contratos){
            JSONObject j = new JSONObject();
            j.put("nome", Uteis.getNomeAbreviado(c.getPessoa().getNome().toLowerCase()));
            j.put("matricula", c.getCliente().getCodigoMatricula());
            j.put("contrato", c.getCodigo());
            j.put("urlFoto", c.getPessoa().getUrlFoto());
            jsonArray.put(j);
        }
        Integer count = cadDao.consultarQtdTermoResponsabilidade(assinados, filtro, empresa, isTermoResponsabilidadeExAluno);
        retorno.put("contratos", jsonArray);
        retorno.put("nr", count);
        retorno.put("excede", count > 20);
        return retorno;
    }

    @Override
    public JSONObject selecionarClienteTermoResponsabilidade(final String key, final Integer matricula) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from planotextopadrao p where tipocontrato = 'TR' AND situacao = 'AT'");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        ClienteVO c =  DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigoMatricula(matricula, 0, Uteis.NIVELMONTARDADOS_TODOS);
        List<EnderecoVO> e = DaoAuxiliar.retornarAcessoControle(key).getEnderecoDao().consultarPorCodigoPessoa(c.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        c.getPessoa().setEnderecoVOs(e);
        Date date = new Date();
        String d = new SimpleDateFormat("dd/MM/yyyy").format(date.getTime());
        if(rs.next()){
            JSONObject json = new JSONObject();
            json.put("matricula", matricula);
            String texto = rs.getString("texto");
            texto = texto.replace("[(80){}Nome_Cliente]", c.getPessoa().getNome())
                    .replace("[(50){}RazaoSocial_Empresa]", c.getEmpresaNome())
                    .replace("[(60){}AssinaturaDigital_Cliente]", c.getPessoa().getAssinaturaBiometriaDigital());

            if(!d.isEmpty()){
                texto = texto.replace("[(10){}DtLancamento_Contrato]", d);
            }
            else{
                texto = texto.replace("[(10){}DtLancamento_Contrato]", "Não informado");
            }

            if(!c.getPessoa().getEmail().isEmpty()){
                texto = texto.replace("[(40){}Email_Cliente]", c.getPessoa().getEmail());
            }
            else{
                texto = texto.replace("[(40){}Email_Cliente]", "Não informado");
            }

            if(!c.getPessoa().getDataNasc().toString().isEmpty()){
                texto = texto.replace("[(20){}DataNasc_Cliente]", Calendario.getData(c.getPessoa().getDataNasc(), "dd/MM/yyyy"));
            }
            else{
                texto = texto.replace("[(20){}DataNasc_Cliente]", "Não informado");
            }

            if(!c.getPessoa().getTelefonesCelular().isEmpty()){
                texto = texto.replace("[(20){}Telefone_Celular_Cliente]", c.getPessoa().getTelefonesCelular());
            }
            else{
                texto = texto.replace("[(20){}Telefone_Celular_Cliente]", "Não informado");
            }

            if(!c.getPessoa().getTelefones().isEmpty()){
                texto = texto.replace("[(20){}Telefone_Cliente]", c.getPessoa().getTelefones());
            }
            else{
                texto = texto.replace("[(20){}Telefone_Cliente]", "Não informado");
            }

            if(c.getPessoaResponsavel() != null && UteisValidacao.notEmptyNumber(c.getPessoaResponsavel().getCodigo())){
                texto = texto.replace("[(40){}Responsavel_Cliente]", UteisValidacao.emptyString(c.getPessoaResponsavel().getNome()) ? "Não informado" : c.getPessoaResponsavel().getNome())
                        .replace("[(14){}Responsavel_Cliente_Cpf]",  UteisValidacao.emptyString(c.getPessoaResponsavel().getCfp()) ? "Não informado" : c.getPessoaResponsavel().getCfp())
                        .replace("[(20){}Responsavel_Cliente_Rg]",  UteisValidacao.emptyString(c.getPessoaResponsavel().getRg()) ? "Não informado" : c.getPessoaResponsavel().getRg());
            } else {
                texto = texto.replace("[(40){}Responsavel_Cliente]", "Não informado")
                        .replace("[(14){}Responsavel_Cliente_Cpf]", "Não informado")
                        .replace("[(20){}Responsavel_Cliente_Rg]",  "Não informado");
            }

            if (!UteisValidacao.emptyString(c.getPessoa().getCpfPai())){
                texto = texto.replace("[(40){}Responsavel_Cliente_Pai]", UteisValidacao.emptyString(c.getPessoa().getNomePai()) ? "Não informado" : c.getPessoa().getNomePai())
                        .replace("[(40){}Responsavel_Cliente_Pai_Cpf]",  UteisValidacao.emptyString(c.getPessoa().getCpfPai()) ? "Não informado" : c.getPessoa().getCpfPai())
                        .replace("[(20){}Responsavel_Cliente_Pai_Rg]",  UteisValidacao.emptyString(c.getPessoa().getRgPai()) ? "Não informado" : c.getPessoa().getRgPai());
            } else {
                texto = texto.replace("[(40){}Responsavel_Cliente_Pai]", "Não informado")
                        .replace("[(40){}Responsavel_Cliente_Pai_Cpf]", "Não informado")
                        .replace("[(20){}Responsavel_Cliente_Pai_Rg]",  "Não informado");
            }

            if (!UteisValidacao.emptyString(c.getPessoa().getCpfMae())){
                texto = texto.replace("[(40){}Responsavel_Cliente_Mae]", UteisValidacao.emptyString(c.getPessoa().getNomeMae()) ? "Não informado" : c.getPessoa().getNomeMae())
                        .replace("[(40){}Responsavel_Cliente_Mae_Cpf]",  UteisValidacao.emptyString(c.getPessoa().getCpfMae()) ? "Não informado" : c.getPessoa().getCpfMae())
                        .replace("[(20){}Responsavel_Cliente_Mae_Rg]",  UteisValidacao.emptyString(c.getPessoa().getRgMae()) ? "Não informado" : c.getPessoa().getRgMae());
            } else {
                texto = texto.replace("[(40){}Responsavel_Cliente_Mae]", "Não informado")
                        .replace("[(40){}Responsavel_Cliente_Mae_Cpf]", "Não informado")
                        .replace("[(20){}Responsavel_Cliente_Mae_Rg]",  "Não informado");
            }

            if (!UteisValidacao.emptyString(c.getPessoa().getCpfRespFinanceiro())){
                texto = texto.replace("[(40){}Responsavel_Financeiro_Nome_Cliente]", UteisValidacao.emptyString(c.getPessoa().getNomeRespFinanceiro()) ? "Não informado" : c.getPessoa().getNomeRespFinanceiro())
                        .replace("[(40){}Responsavel_Financeiro_Cpf_Cliente]",  UteisValidacao.emptyString(c.getPessoa().getCpfRespFinanceiro()) ? "Não informado" : c.getPessoa().getCpfRespFinanceiro())
                        .replace("[(20){}Responsavel_Financeiro_Rg_Cliente]",  UteisValidacao.emptyString(c.getPessoa().getRgRespFinanceiro()) ? "Não informado" : c.getPessoa().getRgRespFinanceiro())
                        .replace("[(40){}Responsavel_Financeiro_Email_Cliente]",  UteisValidacao.emptyString(c.getPessoa().getEmailRespFinanceiro()) ? "Não informado" : c.getPessoa().getEmailRespFinanceiro());
            } else {
                texto = texto.replace("[(40){}Responsavel_Financeiro_Nome_Cliente]", "Não informado")
                        .replace("[(40){}Responsavel_Financeiro_Cpf_Cliente]", "Não informado")
                        .replace("[(20){}Responsavel_Financeiro_Rg_Cliente]",  "Não informado")
                        .replace("[(40){}Responsavel_Financeiro_Email_Cliente]",  "Não informado");
            }

            if(!c.getPessoa().getCfp().isEmpty()){
                texto = texto.replace("[(14){}Cpf_Cliente]", c.getPessoa().getCfp());
            }
            else{
                texto = texto.replace("[(14){}Cpf_Cliente]", "Não informado");
            }

            if(!c.getPessoa().getCidade().getNome().isEmpty()){
                texto = texto.replace("[(20){}Endereco_Cidade_Cliente]", c.getPessoa().getCidade().getNome())
                        .replace("[(20){}Endereco_Estado_Cliente]", c.getPessoa().getCidade().getEstado().getSigla());
            }
            else{
                texto = texto.replace("[(20){}Endereco_Cidade_Cliente]", "Não informado")
                        .replace("[(20){}Endereco_Estado_Cliente]", "Não informado");
            }

            if(!c.getPessoa().getEnderecoVOs().isEmpty()){
                texto = texto.replace("[(40){}Endereco_Cliente]", c.getPessoa().getEnderecoVOs().get(0).getEndereco())
                        .replace("[(10){}CEP_Cliente]", c.getPessoa().getEnderecoVOs().get(0).getCep())
                        .replace("[(50){}BairroEnd_Cliente]", c.getPessoa().getEnderecoVOs().get(0).getBairro())
                        .replace("[(40){}ComplementoEnd_Cliente]", c.getPessoa().getEnderecoVOs().get(0).getComplemento())
                        .replace("[(5){}Endereco_Numero_Cliente]", c.getPessoa().getEnderecoVOs().get(0).getNumero())
                        .replace("[(20){}Endereco_Estado_Cliente]", c.getPessoa().getEstadoVO().getDescricao())
                        .replace("[(20){}Endereco_Cidade_Cliente]", c.getPessoa().getCidade_Apresentar());
            }
            else{
                texto = texto.replace("[(40){}Endereco_Cliente]", "Não informado")
                        .replace("[(10){}CEP_Cliente]", "Não informado")
                        .replace("[(50){}BairroEnd_Cliente]", "Não informado")
                        .replace("[(40){}ComplementoEnd_Cliente]", "Não informado")
                        .replace("[(5){}Endereco_Numero_Cliente]", "Não informado")
                        .replace("[(20){}Endereco_Estado_Cliente]", "Não informado")
                        .replace("[(20){}Endereco_Cidade_Cliente]", "Não informado");
            }

            texto = texto.replace("[(20){}Telefone_Emergencia_Cliente]", UteisValidacao.emptyString(c.getPessoa().getTelefoneEmergencia()) ? "Não informado" : c.getPessoa().getTelefoneEmergencia());

            json.put("texto", texto);
            return json;
        }
        return null;
    }

    public JSONObject visualizarAssinaturaTermoResponsabilidade(final String key, final Integer matricula, final String url) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from planotextopadrao p where tipocontrato = 'TR'");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        ClienteVO c =  DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigoMatricula(matricula, 0, Uteis.NIVELMONTARDADOS_TODOS);
        List<EnderecoVO> e = DaoAuxiliar.retornarAcessoControle(key).getEnderecoDao().consultarPorCodigoPessoa(c.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        c.getPessoa().setEnderecoVOs(e);
        Date date = new Date();
        String d = new SimpleDateFormat("dd/MM/yyyy").format(date.getTime());
        JSONObject json = new JSONObject();
        if(rs.next()){
            json.put("matricula", matricula);
            String texto = rs.getString("texto");
            texto = texto.replace("[(80){}Nome_Cliente]", c.getPessoa().getNome())
                    .replace("[(50){}RazaoSocial_Empresa]", c.getEmpresaNome())
                    .replace("[(60){}AssinaturaDigital_Cliente]", c.getPessoa().getAssinaturaBiometriaDigital());

            if(!d.isEmpty()){
                texto = texto.replace("[(10){}DtLancamento_Contrato]", d);
            }
            else{
                texto = texto.replace("[(10){}DtLancamento_Contrato]", "Não informado");
            }

            if(!c.getPessoa().getEmail().isEmpty()){
                texto = texto.replace("[(40){}Email_Cliente]", c.getPessoa().getEmail());
            }
            else{
                texto = texto.replace("[(40){}Email_Cliente]", "Não informado");
            }

            if(!c.getPessoa().getDataNasc().toString().isEmpty()){
                texto = texto.replace("[(20){}DataNasc_Cliente]", c.getPessoa().getDataNasc().toString());
            }
            else{
                texto = texto.replace("[(20){}DataNasc_Cliente]", "Não informado");
            }

            if(!c.getPessoa().getTelefonesCelular().isEmpty()){
                texto = texto.replace("[(20){}Telefone_Celular_Cliente]", c.getPessoa().getTelefonesCelular());
            }
            else{
                texto = texto.replace("[(20){}Telefone_Celular_Cliente]", "Não informado");
            }

            if(!c.getPessoa().getTelefones().isEmpty()){
                texto = texto.replace("[(20){}Telefone_Cliente]", c.getPessoa().getTelefones());
            }
            else{
                texto = texto.replace("[(20){}Telefone_Cliente]", "Não informado");
            }

            if(!c.getPessoa().getCpfPai().isEmpty()){
                texto = texto.replace("[(14){}Cpf_ResponsavelLegal_Cliente]", c.getPessoa().getCpfPai()).replace("[(80){}Responsavel_Cliente_Cpf_Nome]",  c.getPessoa().getNomePai());
            }
            else if(!c.getPessoa().getCpfMae().isEmpty()){
                texto = texto.replace("[(14){}Cpf_ResponsavelLegal_Cliente]", c.getPessoa().getCpfMae()).replace("[(80){}Responsavel_Cliente_Cpf_Nome]",  c.getPessoa().getNomeMae());;
            }
            else{
                if(!c.getPessoa().getCfp().isEmpty()){
                    texto = texto.replace("[(14){}Cpf_ResponsavelLegal_Cliente]", c.getPessoa().getCfp());
                }
                else{
                    texto = texto.replace("[(14){}Cpf_ResponsavelLegal_Cliente]", "Não informado");
                }
                texto = texto.replace("[(80){}Responsavel_Cliente_Cpf_Nome]", c.getPessoa().getNome());
            }

            if(!c.getPessoa().getCfp().isEmpty()){
                texto = texto.replace("[(14){}Cpf_Cliente]", c.getPessoa().getCfp());
            }
            else{
                texto = texto.replace("[(14){}Cpf_Cliente]", "Não informado");
            }

            if(!c.getPessoa().getCidade().getNome().isEmpty()){
                texto = texto.replace("[(20){}Endereco_Cidade_Cliente]", c.getPessoa().getCidade().getNome())
                        .replace("[(20){}Endereco_Estado_Cliente]", c.getPessoa().getCidade().getEstado().getSigla());
            }
            else{
                texto = texto.replace("[(20){}Endereco_Cidade_Cliente]", "Não informado")
                        .replace("[(20){}Endereco_Estado_Cliente]", "Não informado");
            }

            if(!e.isEmpty()){
                texto = texto.replace("[(40){}Endereco_Cliente]", c.getPessoa().getEnderecoVOs().get(0).getEndereco())
                        .replace("[(10){}CEP_Cliente]", c.getPessoa().getEnderecoVOs().get(0).getCep())
                        .replace("[(50){}BairroEnd_Cliente]", c.getPessoa().getEnderecoVOs().get(0).getBairro());
            }
            else{
                texto = texto.replace("[(40){}Endereco_Cliente]", "Não informado")
                        .replace("[(10){}CEP_Cliente]", "Não informado")
                        .replace("[(50){}BairroEnd_Cliente]", "Não informado");
            }

            json.put("texto", texto);

        }
        String sqlcli ="SELECT assinaturadigitaltermoresponsabilidade FROM cliente WHERE codigomatricula = " + matricula;
        ResultSet rsCli = SuperFacadeJDBC.criarConsulta(sqlcli, con);
        if(rsCli.next()){
            json.put("assinaturadigitaltermoresponsabilidade",  urlApresentar(Uteis.getPaintImagemDaNuvem(rsCli.getString("assinaturadigitaltermoresponsabilidade"), url)));

        }
        return json;

    }

    public JSONObject verificaUtilizaTermoResponsabilidade() throws Exception {
        JSONObject json = new JSONObject();

        String sql ="SELECT termoresponsabilidade FROM configuracaosistema WHERE termoresponsabilidade = true;";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        if(rs.next()){
            json.put("termoresponsabilidade",  "true");
        } else {
            json.put("termoresponsabilidade", "false");
        }

        sql = " select * from planotextopadrao p where tipocontrato = 'TR'";
        rs = SuperFacadeJDBC.criarConsulta(sql, con);
        if (rs.next()){
            json.put("situacao",rs.getString("situacao"));
        } else {
            json.put("situacao","null");
        }

        return json;
    }

    @Override
    public void salvarAssinaturaCliente(final Integer matricula, final String assinatura) throws Exception {
        String sql = "UPDATE cliente SET assinaturadigitaltermoresponsabilidade = ?, termoresponsabilidadeassinado = ? WHERE codigomatricula = ?;";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, uploadImagem(matricula, MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO, assinatura));
        sqlAlterar.setBoolean(2, true);
        sqlAlterar.setInt(3, matricula);
        sqlAlterar.execute();
    }

    @Override
    public Boolean validarToken(String token) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public JSONObject selecionarContrato(final String key, final Integer contrato, Integer aditivo) throws Exception {
        final StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ctp.codigo as contratotextopadrao  FROM contratotextopadrao ctp \n");
        sql.append(" INNER JOIN planotextopadrao ptp ON ptp.codigo = ctp.planotextopadrao \n");
        sql.append(" WHERE ctp.contrato = ").append(contrato);
        if (aditivo == null) {
            sql.append(" AND ctp.aditivo is null ");
        } else {
            sql.append(" AND ctp.aditivo = ").append(aditivo);
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if(rs.next()){
            JSONObject json = new JSONObject();
            json.put("contrato", contrato);
            json.put("aditivo", aditivo);

            final ContratoVO c = contratoPreparado(key, contrato);
            String texto = new ContratoTextoPadrao(con).consultarHtmlContratoAditivo(c.getCodigo(),false, aditivo, false);
            texto = texto.replaceAll("__4ssin4tur4digit4l__", "<b>A assinatura digital ser colocada aqui.</b>");
            texto = texto.replaceAll("__4ssin4tur4digit4l2__", "<b>A assinatura digital resp. financeiro será colocada aqui.</b>");
            texto = arranjarImagens(key, texto, c, true);

            json.put("texto", texto);
            json.put("modelo", rs.getInt("contratotextopadrao"));
            json.put("permitirApresentarAssinatura2", c.getEmpresa().isExigirAssinaturaDigitalResponsavelFinanceiro());
            json.put("responsavelFinanceiroPreenchido", !UteisValidacao.emptyString(c.getPessoa().getNomeRespFinanceiro()));

            return json;
        }
        return null;
    }

    @Override
    public JSONObject visualizarContrato(Integer contrato, String url, Boolean assiCancelamento, Integer aditivo) throws Exception {
        StringBuilder sql = new StringBuilder();
        if (aditivo == null) {
            sql.append(" SELECT documentos, endereco, assinatura, assinatura2, atestado, anexo1, anexo2 FROM contratoassinaturadigital where contrato = ").append(contrato);
        } else {
            sql.append(" SELECT documentos, endereco, assinatura, assinatura2, atestado, anexo1, anexo2 FROM contratoassinaturadigital where contratoaditivo = ").append(contrato);
            sql.append(" AND aditivo = ").append(aditivo);
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (assiCancelamento != null && assiCancelamento) {
            sql = new StringBuilder();
            sql.append("SELECT c.codigo as contrato, cad.assinatura from contrato c  " +
                    "LEFT JOIN cancelamentoassinaturadigital cad ON cad.contrato = c.codigo " +
                    "WHERE c.codigo  =" + contrato + " " +
                    "AND (cad.assinatura is not null AND cad.assinatura <> '' or c.dataassinaturacancelamento is not null) ");
            ResultSet rs2 = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            if (rs2.next()) {
                JSONObject json = new JSONObject();
                json.put("contrato", contrato);
                json.put("assinatura", urlApresentar(Uteis.getPaintImagemDaNuvem(rs2.getString("assinatura"), url)));
                if (rs.next()) {
                    json.put("documentos", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("documentos"), url)));
                    json.put("endereco", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("endereco"), url)));
                    json.put("atestado", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("atestado"), url)));
                    json.put("anexo1", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("anexo1"), url)));
                    json.put("anexo2", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("anexo2"), url)));
                    json.put("assinaturaviaemail", "");
                } else {
                    json.put("documentos", urlApresentar(Uteis.getPaintImagemDaNuvem("", url)));
                    json.put("endereco", urlApresentar(Uteis.getPaintImagemDaNuvem("", url)));
                    json.put("atestado", urlApresentar(Uteis.getPaintImagemDaNuvem("", url)));
                    json.put("anexo1", urlApresentar(Uteis.getPaintImagemDaNuvem("", url)));
                    json.put("anexo2", urlApresentar(Uteis.getPaintImagemDaNuvem("", url)));
                    json.put("assinaturaviaemail", "");
                }
                return json;
            }
        } else {
            if (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("documentos", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("documentos"), url)));
                json.put("endereco", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("endereco"), url)));
                json.put("contrato", contrato);
                json.put("assinatura", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("assinatura"), url)));
                json.put("assinatura2", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("assinatura2"), url)));
                json.put("atestado", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("atestado"), url)));
                json.put("anexo1", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("anexo1"), url)));
                json.put("anexo2", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("anexo2"), url)));
                json.put("assinaturaviaemail", "");
                return json;
            } else {
                sql = new StringBuilder();
                if (aditivo == null) {
                    sql.append("SELECT ipassinaturacontrato,dataassinaturacontrato,emailrecebimento, p.cfp from contrato c  " +
                            "INNER JOIN pessoa p ON p.codigo = COALESCE(c.pessoaoriginal, c.pessoa) " +
                            "INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa " +
                            "LEFT JOIN contratoassinaturadigital cad ON cad.contrato = c.codigo AND cad.ehaditivo is false " +
                            "WHERE c.codigo  =" + contrato + " " +
                            "AND (cad.assinatura is not null AND cad.assinatura <> '' or c.dataassinaturacontrato is not null) " +
                            " ");
                } else {
                    sql.append("SELECT ipassinaturacontrato,dataassinaturacontrato,emailrecebimento, p.cfp from contrato c  " +
                            "INNER JOIN pessoa p ON p.codigo = COALESCE(c.pessoaoriginal, c.pessoa) " +
                            "INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa " +
                            "JOIN plano pl ON pl.codigo = c.plano " +
                            "JOIN planotextopadrao p2 ON p2.codigo = pl.planotextopadrao " +
                            "JOIN aditivo ad ON ad.plano = p2.codigo " +
                            "LEFT JOIN contratoassinaturadigital cad ON cad.contratoaditivo = c.codigo " +
                            "WHERE c.codigo  =" + contrato + " " +
                            "AND cad.ehaditivo is true AND cad.aditivo = ad.codigo  " +
                            "AND (cad.assinatura is not null AND cad.assinatura <> '') " +
                            " ");
                }

                rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                if (rs.next()) {
                    JSONObject json = new JSONObject();
                    json.put("documentos", "");
                    json.put("endereco", "");
                    json.put("contrato", contrato);
                    json.put("assinatura", "");
                    json.put("atestado", "");
                    json.put("anexo1", "");
                    json.put("anexo2", "");
                    json.put("assinaturaviaemail", "Assinado via email pelo IP: " + rs.getString("ipassinaturacontrato") + " <br />" +
                            Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dataassinaturacontrato"), "'Assinado em ' dd/MM/yyyy 'as' HH:mm") + ". <br />" +
                            "CPF confirmado: " + rs.getString("cfp") + ". <br />" +
                            "Email confirmado: " + rs.getString("emailrecebimento") + " .");
                    return json;
                }
            }
        }
        return null;
    }

    public void enviarContrato(String chave, UsuarioVO usuario, String[] emails, ContratoVO contrato,
                               List<MovPagamentoVO> pagamentos, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO,
                               String urlDocs,
                               String urlComprovanteEndereco,
                               String urlAtestado,
                               String urlAnexo1,
                               String urlAnexo2,
                               String urlAssinatura, String descMoedaEmpresa) throws Exception {
        if (contrato.getCodigo() != 0) {
            String texto = new ContratoTextoPadrao(con).consultarHtmlContrato(contrato.getCodigo(),false);
            UteisEmail email = new UteisEmail();
            texto = arranjarImagens(chave, texto, contrato, false);
            texto = addDocsAssinatura(texto, urlDocs, urlComprovanteEndereco, urlAtestado, urlAnexo1, urlAnexo2, urlAssinatura);

            email.novo(contrato.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", configuracaoSistemaCRMVO);
            email.setRemetente(usuario);

            boolean existeEmailValido = false;
            for (String emailEnviar : emails) {
                if (UteisValidacao.validaEmail(emailEnviar)) {
                    existeEmailValido = true;
                }
            }
            if (existeEmailValido) {
                email.enviarEmailN(emails, texto, contrato.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", "");
            } else {
                throw new Exception("Não foi possível enviar o contrato pois o cliente não possui um email válido.");
            }
        } else {
            throw new Exception("Não foi possÍvel enviar o contrato. Dados não encontrados!");
        }
    }

    public String addDocsAssinatura(String texto, String urlDocs,
                                    String urlComprovanteEndereco,
                                    String urlAtestado,
                                    String urlAnexo1,
                                    String urlAnexo2,
                                    String urlAssinatura){
        StringBuilder email = new StringBuilder(texto.replace("</body>", "").replace("</html>", ""));
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Documentos:</div><img style=\"width: 100%;\" src=\"").append(urlDocs).append("\"/>");
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Comprovante de endereço:</div><img style=\"width: 100%;\" src=\"").append(urlComprovanteEndereco).append("\"/>");
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Atestado:</div><img style=\"width: 100%;\" src=\"").append(urlAtestado).append("\"/>");
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Anexo1:</div><img style=\"width: 100%;\" src=\"").append(urlAnexo1).append("\"/>");
        email.append("<div style=\"width: 100%; text-align:center;\"><div>Anexo2:</div><img style=\"width: 100%;\" src=\"").append(urlAnexo2).append("\"/>");
        email.append("</body>");
        email.append("</html>");
        return email.toString();
    }

    /**
     * <AUTHOR> 06/05/2011
     */
    public String arranjarImagens(String chave, String texto, ContratoVO contrato, Boolean app) throws Exception {
        //criar a imagem temporaria
        String path = "/imagensCRM/email/tmp/";
        if(servletContext == null){
            return "";
        }
        File caminhoBase = new File(servletContext.getRealPath("/"));

        String pathFull = caminhoBase.getAbsolutePath() + path;
        String nomeImagem = Uteis.retirarAcentuacaoRegex(contrato.getEmpresa().getNome().replaceAll(" ", ""));
        if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
            try {
                String fotoKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, contrato.getEmpresa().getCodigo().toString());
                texto = texto.replaceAll("<img[^>]+src\\s*=\\s*['\"]acesso\\?emp*([^'\"]+)['\"][^>]*>", "<img src=\"" + Uteis.getPaintFotoDaNuvem(fotoKey) + "\" />");
            }catch (Exception e){
                //quando no tiver a foto do relatrio para a chave em questo, o midiaservice dispara uma exceo
            }

        }else{
            contrato.getEmpresa().setFotoRelatorio(DaoAuxiliar.retornarAcessoControle(chave).getEmpresaDao().obterFoto(chave,
                    contrato.getEmpresa().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
            UteisEmail.criarImagem(pathFull, contrato.getEmpresa().getFotoRelatorio(), nomeImagem + ".jpg");
            path = (app ? ".." : ".") + path + nomeImagem + ".jpg";
            texto = texto.replaceAll("<img[^>]+src\\s*=\\s*['\"]acesso\\?emp*([^'\"]+)['\"][^>]*>", "<img src=\"" + path + "\" />");
        }
        return texto;
    }

    public ContratoVO contratoPreparado(String chave, Integer codigoContrato) throws Exception{

        ContratoVO c = DaoAuxiliar.retornarAcessoControle(chave).getContratoDao().consultarPorChavePrimaria(codigoContrato, Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
        // setar atributos do contrato
        c.setMovParcelaVOs(DaoAuxiliar.retornarAcessoControle(chave).getMovParcelaDao().consultarPorContratoNaoRenegociadaNegociada(c.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        ContratoTextoPadrao cdtpDao = new ContratoTextoPadrao(con);
        c.setContratoTextoPadrao(cdtpDao.consultarPorCodigoContrato(c.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        c.setPessoa(DaoAuxiliar.retornarAcessoControle(chave).getPessoaDao().consultarPorChavePrimaria(c.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        c.setEmpresa(DaoAuxiliar.retornarAcessoControle(chave).getEmpresaDao().consultarPorChavePrimaria(c.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        ContratoDuracao cdDao = new ContratoDuracao(con);
        c.setContratoDuracao(cdDao.consultarContratoDuracoes(c.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (c.isVendaCreditoTreino()){
            c.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(DaoAuxiliar.retornarAcessoControle(chave).getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(c.getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        c.setContratoHorario(DaoAuxiliar.retornarAcessoControle(chave).getContratoHorarioDao().consultarContratoHorarios(c.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        c.setContratoModalidadeVOs(DaoAuxiliar.retornarAcessoControle(chave).getContratoModalidadeDao().consultarContratoModalidades(c.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
        c.setResponsavelContrato(DaoAuxiliar.retornarAcessoControle(chave).getUsuarioDao().consultarPorChavePrimaria(c.getResponsavelContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        c.getPlano().setPlanoTextoPadrao(c.getContratoTextoPadrao().getPlanoTextoPadrao());
        // pesquisar pagamentos j efetuados para informar no contrato.
        return c;
    }

    public void enviarEmailCancelamento(ContratoVO contrato, String urlAssinatura) throws Exception {
        ResultSet rsEmails = SuperFacadeJDBC.criarConsulta("SELECT email FROM email e "
                + "inner join contrato c on e.pessoa = c.pessoa and c.codigo = "+contrato.getCodigo(), con);
        String[] emails = {};
        while(rsEmails.next()){
            emails = new String[]{rsEmails.getString("email")};
        }
        if(emails.length == 0){
            return;
        }

        String assuntoEmail = "Cancelamento Contrato " + contrato.getCodigo() + " - " + contrato.getEmpresa().getNome();
        Cliente clienteDAO = new Cliente(con);
        String mensagemEmailAux = clienteDAO.montarEmailCancelamento(contrato.getCancelamentoContratoVO(), contrato.getEmpresa(), urlAssinatura);
        clienteDAO = null;

        UteisEmail uteisEmail = new UteisEmail();
        ConfiguracaoSistemaCRMVO configuracaoSistemaCRMEnvio = getConfiguracaoSMTPNoReply();
        if (UteisValidacao.emptyString(configuracaoSistemaCRMEnvio.getMailServer())) {
            throw new Exception("Não foi possível enviar o e-mail. Verifique as configurações de e-mail no CRM!");
        }
        uteisEmail.novo(assuntoEmail, configuracaoSistemaCRMEnvio);
        //REMETENTE
        ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(con);
        ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_TODOS);
        configuracaoSistemaCRMDAO = null;
        uteisEmail.setRemetente(configuracaoSistemaCRM.getRemetentePadraoMailing());
        uteisEmail.enviarEmailN(emails, mensagemEmailAux, assuntoEmail, contrato.getEmpresa().getNome());
    }

    public void enviarEmail(String chave, Integer codigoContrato, Integer codigoUsuario,
                            String urlDocs,
                            String urlComprovanteEndereco,
                            String urlAtestado,
                            String urlAnexo1,
                            String urlAnexo2,
                            String urlAssinatura, String descMoedaEmpresa) throws Exception {
        ResultSet rsEmails = SuperFacadeJDBC.criarConsulta("SELECT email FROM email e "
                + "inner join contrato c on e.pessoa = c.pessoa and c.codigo = "+codigoContrato, con);
        List<String> emails = new ArrayList<String>();
        while(rsEmails.next()){
            emails.add(rsEmails.getString("email"));
        }
        if(emails.isEmpty()){
            return;
        }
        ContratoVO c = contratoPreparado(chave, codigoContrato);
        List<MovPagamentoVO> pagamentosDeUmContrato = DaoAuxiliar.retornarAcessoControle(chave).getMovPagamentoDao().consultarPagamentoDeUmContrato(c.getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_TODOS);
        UsuarioVO usuario = DaoAuxiliar.retornarAcessoControle(chave).getUsuarioDao().consultarPorChavePrimaria(codigoUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        enviarContrato(chave, usuario, emails.toArray(new String[emails.size()]), c,
                pagamentosDeUmContrato, getConfiguracaoSMTPNoReply(),
                urlDocs, urlComprovanteEndereco, urlAtestado, urlAnexo1, urlAnexo2, urlAssinatura, descMoedaEmpresa);
    }

    public ServletContext getServletContext() {
        return servletContext;
    }

    public void setServletContext(ServletContext servletContext) {
        this.servletContext = servletContext;
    }

    @Override
    public JSONObject consultarParaAtestado(String filtro, Integer empresa) throws Exception {
        JSONObject retorno = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT nome, codigopessoa, fotokey, codacessocliente from pessoa p");
        sql.append(" INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa ");
        if(!UteisValidacao.emptyString(filtro)){
            sql.append("and (cli.nomeconsulta LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sql.append("  OR cli.matricula::varchar  = '").append(filtro).append("' )");
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" WHERE cli.empresacliente = ").append(empresa);
        }
        sql.append(" ORDER BY p.nome LIMIT 20");
        ResultSet rs = criarConsulta(sql.toString(), con);
        //limitar a 20 enviados
        while(rs.next()){
            JSONObject j = new JSONObject();
            j.put("nome", Uteis.getNomeAbreviado(rs.getString("nome").toLowerCase(), false));
            j.put("codigo", rs.getInt("codigopessoa"));
            j.put("codacesso", rs.getString("codacessocliente"));
            j.put("urlFoto", urlApresentar(Uteis.getPaintFotoDaNuvem(rs.getString("fotokey"))));
            jsonArray.put(j);
        }
        retorno.put("alunos", jsonArray);
        return retorno;
    }

    private String urlApresentar(String url) {
        boolean tratarUrl = isTratarUrl();
        String[] urlApresentarTmp = url.split("\\?time");
        String urlFinal = urlApresentarTmp[0];
        if (tratarUrl) {
            urlFinal = urlFinal.replace("*", "+");
        }
        return (tratarUrl ? "../" : "") + urlFinal;
    }

    private boolean isTratarUrl() {
        boolean tratarUrl = false;
        try {
            tratarUrl = MidiaService.getInstance().getClass().equals(MidiaZWInternal.class);
        } catch (Exception ignored) {

        }
        return tratarUrl;
    }

    @Override
    public JSONObject infoAcesso(String chave, Integer codigoUsuario, Integer empresaLogada) throws Exception {
        JSONObject retorno = new JSONObject();
        ResultSet rs = criarConsulta("select situacao, nome, c.empresa from usuario u\n" +
                "inner join colaborador c on c.codigo = u.colaborador \n" +
                "where u.codigo = " + codigoUsuario, con);
        if (rs.next()) {
            if (!rs.getString("situacao").equals("AT")) {
                throw new Exception("O usurio no est ativo");
            }
            retorno.put("nomeusuario", rs.getString("nome"));
            retorno.put("empresa", empresaLogada);
        } else {
            throw new Exception("No foi possível encontrar o usurio!");
        }

        JSONArray empresas = new JSONArray();
        List<UsuarioPerfilAcessoVO> listaPerfisUsuario = DaoAuxiliar.retornarAcessoControle(chave)
                .getUsuarioPerfilAcessoDao().consultarUsuarioPerfilAcesso(codigoUsuario,
                        Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        for (UsuarioPerfilAcessoVO perfil : listaPerfisUsuario) {
            EmpresaVO emp = DaoAuxiliar.retornarAcessoControle(chave).getEmpresaDao().consultarPorChavePrimaria(
                    perfil.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            JSONObject empresa = new JSONObject();
            empresa.put("nome", emp.getNome());
            empresa.put("codigo", emp.getCodigo());
            empresa.put("tipoAnexoCartaoVacina", emp.getTipoAnexoCartaoVacina().getCodigo());
            empresas.put(empresa);
            if(emp.getCodigo() == empresaLogada){
                retorno.put("tipoAnexoCartaoVacina", emp.getTipoAnexoCartaoVacina());
            }
        }
        retorno.put("empresas", empresas);
        return retorno;
    }

    @Override
    public String alterarFotoAluno(String chave,
                                   Integer contrato, Integer codigoPessoa, String foto, Integer usuarioLogado, UsuarioVO usuarioSessao) throws Exception {

        if (UteisValidacao.emptyString(foto)) {
            return "";
        }

        //Forçar encode da foto base64
        foto = foto.replace(" ", "+");

        if (contrato != null) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT pessoa FROM contrato where codigo = " + contrato, con);
            if (rs.next()) {
                codigoPessoa = rs.getInt("pessoa");
            }
        }
        byte[] fotoByte = Base64.decodeBase64(foto);
        String fotoKey;
        if(codigoPessoa > 0){
            String timeStamp = "?time=" + System.currentTimeMillis();
            fotoKey = MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_PESSOA,
                    codigoPessoa.toString() + timeStamp, fotoByte);

            String sql = "UPDATE Pessoa set fotokey = ? WHERE codigo = ?";
            if(con == null || con.isClosed()) {
                con = DaoAuxiliar.retornarAcessoControle(chave).getCon();
            }
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, fotoKey + "?time=" + Calendario.hoje().getTime());
            sqlAlterar.setInt(2, codigoPessoa);
            sqlAlterar.execute();

            String problemaRemoverPessoaAcesso = "";
            try {
                PessoaFotoLocalAcesso fotoLocal = new PessoaFotoLocalAcesso(con);
                fotoLocal.excluirFotoPessoaLocalAcesso(codigoPessoa);
                fotoLocal = null;
            } catch (Exception e) {
                System.out.println("Problema ao remover a pessoa " + codigoPessoa + " de PessoaFotoLocalAcesso.");
                problemaRemoverPessoaAcesso = e.getMessage();
            }

            Boolean atualizarFotoApp = verificaAtualizacaoFotoTreino(chave);

            String problemaTrocarFotoTreino = "";
            try {

                String urlTreino = PropsService.getPropertyValue(chave, PropsService.urlTreino);
                Map<String, String> params = new HashMap<String, String>();
                params.put("codigopessoa", String.valueOf(codigoPessoa));
                params.put("fotoKey", fotoKey);
                params.put("atualizarFotoApp", atualizarFotoApp ? "true" : "false");
                ExecuteRequestHttpService.executeRequest(urlTreino + "/prest/config/" + chave + "/baixarFotoAlunoPorFotoKey", params);
            } catch (Exception ex) {
                System.out.println("Problema ao enviar solicitao de trocar a foto do aluno");
                problemaTrocarFotoTreino = ex.getMessage();
            }

            Log logFacade = new Log(con);
            try {
                StringBuilder sb = new StringBuilder();
                if (UteisValidacao.emptyString(problemaRemoverPessoaAcesso) && UteisValidacao.emptyString(problemaTrocarFotoTreino)) {
                    sb.append("Foto alterada com sucesso. ");
                } else {
                    sb.append("Foto alterada parcialmente. ");
                }
                if (!UteisValidacao.emptyString(problemaRemoverPessoaAcesso)) {
                    sb.append("Problemas ao alterar foto no Acesso: ").append(problemaRemoverPessoaAcesso);
                }
                if (!UteisValidacao.emptyString(problemaTrocarFotoTreino)) {
                    sb.append("Problemas ao alterar a foto no Treino: ").append(problemaTrocarFotoTreino);
                }

                registrarLog(codigoPessoa, usuarioSessao, logFacade,
                        "ALTERAR FOTO", "PESSOA", "Alterar Foto - Pessoa", "Foto", "", sb.toString());

                SuperControle.notificarOuvintes(DadosAcessoOfflineVO.CHAVE_NOTIFICAR_ATUALIZAR_FOTO_PESSOA + "(" + codigoPessoa + ")", PropsService.getPropertyValue("urlNotificacaoAcesso"), chave);
            } catch (Exception ex) {
                registrarLog(codigoPessoa, usuarioSessao, logFacade,
                        "ERRO AO CRIAR LOG", "PESSOA", "Alterar Foto - Pessoa", "Erro", "", "");
            }
        } else {
            File f = new File(PropsService.getPropertyValue(PropsService.diretorioArquivos)
                    + "/fotos_pessoa_temp");
            f.getParentFile().mkdirs();
            File tmp = new File(String.format(f.getPath() + "/%s@%s@%s.jpg", chave, usuarioLogado, 0));
            FileUtilities.saveToFile(fotoByte, tmp.getPath());
            fotoKey = "";
        }
        return fotoKey;

    }

    private Boolean verificaAtualizacaoFotoTreino(String chave) throws IOException {
        try {
            String url = PropsService.getPropertyValue(PropsService.urlAppDoAlunoUnificado);

            String resposta = ExecuteRequestHttpService.executeRequestGET(url + "/clienteApp/listarClientesApp?queryName=" + chave + "&size=1", new HashMap<>());
            JSONObject respostaJson = new JSONObject(resposta);

            if (respostaJson.has("sucesso") && respostaJson.getJSONArray("sucesso").length() > 0) {
                JSONObject clienteApp = respostaJson.getJSONArray("sucesso").getJSONObject(0);
                String documentKey = clienteApp.getString("documentKey");
                if (!UteisValidacao.emptyString(documentKey)) {
                    resposta = ExecuteRequestHttpService.executeRequestGET(url + "/clienteApp/obterClienteApp?documentKey=" + documentKey, new HashMap<>());
                    JSONObject clienteAppDetalhes = new JSONObject(resposta);

                    if (clienteAppDetalhes.has("sucesso") && clienteAppDetalhes.getJSONObject("sucesso").has("configuracaoModulosApp")) {
                        JSONArray configuracaoModulosApp = clienteAppDetalhes.getJSONObject("sucesso").getJSONArray("configuracaoModulosApp");
                        for (int i = 0; i < configuracaoModulosApp.length(); i++) {
                            JSONObject modulo = configuracaoModulosApp.getJSONObject(i);
                            if (modulo.has("moduloApp") && modulo.getString("moduloApp").equals("MODULO_SALVAR_FOTO_ZW")) {
                                return modulo.getBoolean("habilitado");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("Problema ao verificar atualizacao de foto no treino: " + e.getMessage());
        }

        return false;
    }

    @Override
    public void incluirAssinaturaSimples(String chave, Integer contrato, String assinatura) throws Exception {
        ContratoAssinaturaDigitalVO assinaturaAnterior = cadDao.consultarPorContrato(contrato);
        ContratoAssinaturaDigitalVO cad = new ContratoAssinaturaDigitalVO();
        cad.setContrato(new ContratoVO());
        cad.getContrato().setCodigo(contrato);
        cad.setUsuarioResponsavel(new UsuarioVO());
        ResultSet rsresponsavel = criarConsulta("select responsavelcontrato from contrato where codigo = " + contrato,
                con);
        if(rsresponsavel.next()){
            cad.getUsuarioResponsavel().setCodigo(rsresponsavel.getInt("responsavelcontrato"));
        }

        ResultSet rstexto = criarConsulta("select codigo from contratotextopadrao where contrato = " + contrato,
                con);
        cad.setContratoTextoPadrao(new ContratoTextoPadraoVO());
        if(rstexto.next()){
            cad.getContratoTextoPadrao().setCodigo(rstexto.getInt("codigo"));
        }

        cad.setAssinatura(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO, assinatura));

        if(assinaturaAnterior == null
                || UteisValidacao.emptyNumber(assinaturaAnterior.getCodigo())){
            cadDao.incluir(cad);
        }else{
            cad.setCodigo(assinaturaAnterior.getCodigo());
            cad.setAssinatura(assinaturaAnterior.getAssinatura());
            cadDao.alterarSomenteAssinaturaContrato(cad);
        }


        UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(cad.getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

        Contrato contratoDao = new Contrato(con);
        ContratoVO contratoVO = contratoDao.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_MINIMOS);
        contratoDao = null;

        registrarLog(cad, usuarioVO, contratoVO.getPessoa().getCodigo());
        notificarAssinaturaParaAcesso(chave, contratoVO.getPessoa().getCodigo());
//        enviarEmail(chave, contrato, cad.getUsuarioResponsavel().getCodigo(), Uteis.getPaintFotoDaNuvem(cad.getDocumentos()),
//                Uteis.getPaintFotoDaNuvem(cad.getEndereco()),
//                Uteis.getPaintFotoDaNuvem(cad.getAssinatura()));
    }

    public void salvarImagens(String chave, Integer contrato, UsuarioVO usuarioSessao, String imagemDocumentos, boolean documentosUpdate,
                              String imagemEndereco, boolean enderecoUpdate, String imagemAtestado, boolean atestadoUpdate,
                              String imagemAnexo1, boolean anexo1Update, String imagemAnexo2, boolean anexo2Update) throws Exception {

        ContratoAssinaturaDigitalVO assinaturaAnterior = cadDao.consultarPorContrato(contrato);

        ContratoAssinaturaDigitalVO cad = new ContratoAssinaturaDigitalVO();
        cad.setContrato(new ContratoVO());
        cad.getContrato().setCodigo(contrato);
        cad.setUsuarioResponsavel(usuarioSessao);
        cad.setDocumentos(uploadImagem(contrato, imagemDocumentos.contains("application/pdf") ? MidiaEntidadeEnum.ANEXO_DOCUMENTOS_CONTRATO_PDF : MidiaEntidadeEnum.ANEXO_DOCUMENTOS_CONTRATO, assinaturaAnterior.getDocumentos(), imagemDocumentos, documentosUpdate));
        cad.setEndereco(uploadImagem(contrato, imagemEndereco.contains("application/pdf") ? MidiaEntidadeEnum.ANEXO_ENDERECO_CONTRATO_PDF : MidiaEntidadeEnum.ANEXO_ENDERECO_CONTRATO, assinaturaAnterior.getEndereco(), imagemEndereco, enderecoUpdate));
        cad.setAtestado(uploadImagem(contrato, imagemAtestado.contains("application/pdf") ? MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO_PDF : MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO, assinaturaAnterior.getAtestado(), imagemAtestado, atestadoUpdate));
        cad.setAnexo1(uploadImagem(contrato, imagemAnexo1.contains("application/pdf") ? MidiaEntidadeEnum.ANEXO1_PDF : MidiaEntidadeEnum.ANEXO1, assinaturaAnterior.getAnexo1(), imagemAnexo1, anexo1Update));
        cad.setAnexo2(uploadImagem(contrato, imagemAnexo2.contains("application/pdf") ? MidiaEntidadeEnum.ANEXO2_PDF : MidiaEntidadeEnum.ANEXO2, assinaturaAnterior.getAnexo2(), imagemAnexo2, anexo2Update));

        if (UteisValidacao.emptyNumber(assinaturaAnterior.getCodigo())) {
            ContratoTextoPadrao cdtpDao = new ContratoTextoPadrao(con);
            cad.setContratoTextoPadrao(cdtpDao.consultarPorCodigoContrato(contrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            cadDao.incluir(cad);
        } else {
            cad.setCodigo(assinaturaAnterior.getCodigo());
            cad.setAssinatura(assinaturaAnterior.getAssinatura());
            cadDao.alterar(cad);
        }

        Contrato contratoDao = new Contrato(con);
        ContratoVO contratoVO = contratoDao.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_MINIMOS);
        contratoDao = null;

        registrarLog(cad, usuarioSessao, contratoVO.getPessoa().getCodigo());
        if(!chave.contains("TelaCliente:")) {
            notificarAssinaturaParaAcesso(chave, contratoVO.getPessoa().getCodigo());
        }
    }

    public void notificarAssinaturaParaAcesso(String chave, Integer codigoPessoa) {
        try {
            SituacaoClienteSinteticoDW situacaoClienteDAO  = new SituacaoClienteSinteticoDW(con);
            situacaoClienteDAO.atualizarBaseOffLineZillyonAcesso(chave, codigoPessoa);
            situacaoClienteDAO = null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public void salvarAnexoCancelamento(Integer contrato, UsuarioVO usuarioSessao,
                                        String imagemAnexoCancelamento, boolean anexoCancelamentoUpdate) throws Exception {

        ContratoAssinaturaDigitalVO assinaturaAnterior = cadDao.consultarPorContrato(contrato);

        ContratoAssinaturaDigitalVO cad = new ContratoAssinaturaDigitalVO();
        cad.setContrato(new ContratoVO());
        cad.getContrato().setCodigo(contrato);
        cad.setUsuarioResponsavel(usuarioSessao);
        cad.setAnexoCancelamento(uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_CANCELAMENTO,
                assinaturaAnterior.getAnexoCancelamento(), imagemAnexoCancelamento, anexoCancelamentoUpdate));

        if (UteisValidacao.emptyNumber(assinaturaAnterior.getCodigo())) {
            ContratoTextoPadrao cdtpDao = new ContratoTextoPadrao(con);
            cad.setContratoTextoPadrao(cdtpDao.consultarPorCodigoContrato(contrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            cadDao.incluir(cad);
        } else {
            cad.setCodigo(assinaturaAnterior.getCodigo());
            cad.setAssinatura(assinaturaAnterior.getAssinatura());
            cadDao.alterarSomenteAnexoCancelamento(cad);
        }
    }

    public void registrarLogCancelamento(CancelamentoAssinaturaDigitalVO obj, UsuarioVO usuarioResponsavel, Integer codigoPessoa) throws Exception {
        Log logFacade = new Log(con);
        try {
            if(!UteisValidacao.emptyString(obj.getAssinatura())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO CANCELAMENTO", "Incluir Assinatura Digital - Contrato Cancelamento", "assinatura", "", obj.getAssinatura());
            }
        } catch (Exception ex){
            registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                    "ERRO AO CRIAR LOG", "CONTRATO CANCELAMENTO", "Incluir Assinatura Digital - Contrato Cancelamento", "Erro", "", "");
        }
    }

    public void registrarLog(ContratoAssinaturaDigitalVO obj, UsuarioVO usuarioResponsavel, Integer codigoPessoa) throws Exception {
        Log logFacade = new Log(con);
        try {
            if(!UteisValidacao.emptyString(obj.getAnexoCancelamento())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO", "Incluir Assinatura Digital - Contrato", "anexoCancelamento", "", obj.getAnexoCancelamento());
            }
            if(!UteisValidacao.emptyString(obj.getDocumentos())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO", "Incluir Assinatura Digital - Contrato", "documentos", "", obj.getDocumentos());
            }
            if(!UteisValidacao.emptyString(obj.getEndereco())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO", "Incluir Assinatura Digital - Contrato", "endereco", "", obj.getEndereco());
            }
            if(!UteisValidacao.emptyString(obj.getAssinatura())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO", "Incluir Assinatura Digital - Contrato", "assinatura", "", obj.getAssinatura());
            }
            if(!UteisValidacao.emptyString(obj.getAtestado())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO", "Incluir Assinatura Digital - Contrato", "atestado", "", obj.getAtestado());
            }
            if(!UteisValidacao.emptyString(obj.getAnexo1())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO", "Incluir Assinatura Digital - Contrato", "anexo1", "", obj.getAnexo1());
            }
            if(!UteisValidacao.emptyString(obj.getAnexo2())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "INCLUIR ASSINATURA DIGITAL", "CONTRATO", "Incluir Assinatura Digital - Contrato", "anexo2", "", obj.getAnexo2());
            }
        } catch (Exception ex){
            registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                    "ERRO AO CRIAR LOG", "CONTRATO", "Incluir Assinatura Digital - Contrato", "Erro", "", "");
        }
    }

    public void registrarLogApp(ContratoAssinaturaDigitalVO obj, UsuarioVO usuarioResponsavel, Integer codigoPessoa) throws Exception {
        Log logFacade = new Log(con);
        try {
            if(!UteisValidacao.emptyString(obj.getAnexoCancelamento())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "ASSINATURA DIGITAL INCLUIDA VIA APP", "CONTRATO", "Incluir Assinatura Digital - Contrato", "anexoCancelamento", "", obj.getAnexoCancelamento());
            }
            if(!UteisValidacao.emptyString(obj.getDocumentos())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "ASSINATURA DIGITAL INCLUIDA VIA APP", "CONTRATO", "Incluir Assinatura Digital - Contrato", "documentos", "", obj.getDocumentos());
            }
            if(!UteisValidacao.emptyString(obj.getEndereco())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "ASSINATURA DIGITAL INCLUIDA VIA APP", "CONTRATO", "Incluir Assinatura Digital - Contrato", "endereco", "", obj.getEndereco());
            }
            if(!UteisValidacao.emptyString(obj.getAssinatura())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "ASSINATURA DIGITAL INCLUIDA VIA APP", "CONTRATO", "Incluir Assinatura Digital - Contrato", "assinatura", "", obj.getAssinatura());
            }
            if(!UteisValidacao.emptyString(obj.getAtestado())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "ASSINATURA DIGITAL INCLUIDA VIA APP", "CONTRATO", "Incluir Assinatura Digital - Contrato", "atestado", "", obj.getAtestado());
            }
            if(!UteisValidacao.emptyString(obj.getAnexo1())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "ASSINATURA DIGITAL INCLUIDA VIA APP", "CONTRATO", "Incluir Assinatura Digital - Contrato", "anexo1", "", obj.getAnexo1());
            }
            if(!UteisValidacao.emptyString(obj.getAnexo2())){
                registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                        "ASSINATURA DIGITAL INCLUIDA VIA APP", "CONTRATO", "Incluir Assinatura Digital - Contrato", "anexo2", "", obj.getAnexo2());
            }
        } catch (Exception ex){
            registrarLog(codigoPessoa, usuarioResponsavel, logFacade,
                    "ERRO AO CRIAR LOG", "CONTRATO", "Incluir Assinatura Digital - Contrato", "Erro", "", "");
        }
    }


    private void registrarLog(Integer codigoPessoa, UsuarioVO usuarioSessao, Log logDAO,
                              String operacao, String entidade, String entidadeDescricao, String nomeCampo, String valorAnterior, String valorAlterado) throws Exception {
        LogVO log = new LogVO();
        log.setOperacao(operacao);
        if ("Erro".equals(nomeCampo)) {
            log.setChavePrimaria("");
        } else {
            log.setChavePrimaria(codigoPessoa.toString());
        }
        if (usuarioSessao == null) {
            log.setResponsavelAlteracao("WEBSERVICE");
        } else {
            log.setResponsavelAlteracao(usuarioSessao.getNome());
            log.setUserOAMD(usuarioSessao.getUserOamd());
        }
        log.setNomeEntidade(entidade);
        log.setNomeEntidadeDescricao(entidadeDescricao);
        log.setDataAlteracao(Calendario.hoje());
        log.setNomeCampo(nomeCampo);
        log.setValorCampoAnterior(valorAnterior);
        log.setValorCampoAlterado(valorAlterado);
        log.setPessoa(codigoPessoa);
        logDAO.incluirSemCommit(log);
    }

    @Override
    public JSONObject consultarAlunosCartaoVacina(boolean cadastrados, String filtro, Integer empresa, boolean todos, Integer tipoAnexoCartaoVacina) throws Exception {
        JSONObject retorno = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        String tipoAnexo = tipoAnexoCartaoVacina == 2 ? "2" :   "2,1";
        List<PessoaVO> pessoas = pesDao.consultarAlunosCartaoVacina(cadastrados, filtro, empresa, todos, tipoAnexo);
        for(PessoaVO p : pessoas ){
            JSONObject j = new JSONObject();
            j.put("nome", Uteis.getNomeAbreviado(p.getNome().toLowerCase()));
            j.put("matricula", p.getIdentificadorEntidade());
            j.put("urlFoto", p.getUrlFoto());
            j.put("pessoa", p.getCodigo());
            if(cadastrados){
                j.put("cadastradoem", Uteis.getDataAplicandoFormatacao(p.getLancamentoCartaoVacina(), "'Cadastrado em' dd/MM/yyyy 's' HH:mm"));
            }
            jsonArray.put(j);
        }
        Integer count = pesDao.countAlunosCartaoVacina(cadastrados, filtro, empresa, tipoAnexo);
        retorno.put("pessoas", jsonArray);
        retorno.put("nr", count);
        retorno.put("excede", count > 20);
        return retorno;
    }

    public void incluirCartaoVacina(String chave,String token, Integer pessoa, Integer usuarioResponsavel,
                                    Integer tipoAnexo,
                                    String anexo1,   boolean updateAnexo1) throws Exception {
        String tipoAnexoPesquisar =  PessoaAnexoEnum.CARTAO_VACINACAO_PRIMEIRA_DOSE.getCodigo() +","+PessoaAnexoEnum.CARTAO_VACINACAO_SEGUNDA_UNICA.getCodigo();

        PessoaAnexoVO cartaoAnterior= pesDao.consultarPorPessoa(pessoa, tipoAnexoPesquisar);


        PessoaAnexoVO cad = new PessoaAnexoVO();

        cad.setPessoa( new PessoaVO());
        cad.getPessoa().setCodigo(pessoa);
        cad.setUsuarioResponsavel(new UsuarioVO());
        cad.getUsuarioResponsavel().setCodigo(usuarioResponsavel);

        cad.setTipoAnexo(PessoaAnexoEnum.consultarPorCodigo(tipoAnexo));

        cad.setAnexo1(uploadImagem(pessoa, MidiaEntidadeEnum.PESSOA_ANEXO1, cartaoAnterior.getAnexo1(), anexo1, updateAnexo1));

        if(cartaoAnterior == null
                || UteisValidacao.emptyNumber(cartaoAnterior.getCodigo())){
            pesDao.incluir(cad);
        }else{
            cad.setCodigo(cartaoAnterior.getCodigo());
            pesDao.alterar(cad);
        }
    }

    @Override
    public JSONObject visualizarCartaoVacina(Integer pessoa, String url) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT tipoanexo, anexo1, anexo2 FROM pessoaanexo where pessoa =").append(pessoa);
        sql.append(" and tipoanexo in (").append(PessoaAnexoEnum.CARTAO_VACINACAO_PRIMEIRA_DOSE.getCodigo()).append(",");
        sql.append(PessoaAnexoEnum.CARTAO_VACINACAO_SEGUNDA_UNICA.getCodigo()).append(")");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            JSONObject json = new JSONObject();
            json.put("pessoa", pessoa);
            json.put("anexo1", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("anexo1"), url)));
            json.put("anexo2", urlApresentar(Uteis.getPaintImagemDaNuvem(rs.getString("anexo2"), url)));
            json.put("tipoanexo", rs.getInt("tipoanexo"));
            return json;
        }
        return null;
    }

    public JSONArray verificarClientesParQPositivo(JSONArray clientesParQPositivo, String nomeOuMatricula, Integer empresaZw) throws Exception {
        if (clientesParQPositivo.length() < 10) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT p.nome, c.codigomatricula , p.fotokey \n");
            sql.append("FROM cliente c \n");
            sql.append("INNER JOIN pessoa p ON p.codigo = c.pessoa \n");
            sql.append("WHERE c.parqpositivo = true \n");
            sql.append("AND c.empresa = ").append(empresaZw).append(" \n");
            if (!nomeOuMatricula.equals("") & nomeOuMatricula.matches("[0-9]*")) {
                sql.append("AND c.codigomatricula = ").append(nomeOuMatricula).append(" \n");
            } else if (!nomeOuMatricula.equals("")) {
                sql.append("AND UPPER(p.nome) LIKE UPPER('%").append(nomeOuMatricula).append("%') \n");
            }
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                if (clientesParQPositivo.length() < 10) {
                    if (!verificarSeJaEstaNaListaParQPositivo(clientesParQPositivo, rs.getInt("codigomatricula"))) {
                        JSONObject json = new JSONObject();
                        json.put("nome", rs.getString("nome"));
                        json.put("matricula", rs.getInt("codigomatricula"));
                        json.put("urlFoto", getUrlFotoPessoa(rs.getString("fotokey")));
                        json.put("assinado", false);
                        clientesParQPositivo.put(json);
                    }
                }
            }
        }
        return ordenarJSONArray(clientesParQPositivo, "nome");
    }

    public Boolean verificarSeJaEstaNaListaParQPositivo(JSONArray listaClientes, Integer matricula) {
        for (int i = 0; i < listaClientes.length(); i++) {
            if (Integer.parseInt(listaClientes.getJSONObject(i).get("matricula").toString()) == matricula) {
                return true;
            }
        }
        return false;
    }

    public String getUrlFotoPessoa(String fotoKey) {
        try {
            String uri = Uteis.getPaintFotoDaNuvem(fotoKey);
            if (!UteisValidacao.emptyString(uri) && !uri.contains("fotoPadrao"))
                return uri;
            else return uri;
        } catch (Exception e) {
            return "";
        }
    }

    public JSONArray ordenarJSONArray(JSONArray jsonArray, String key) {
        List<JSONObject> jsonValues = new ArrayList<JSONObject>();
        for (int i = 0; i < jsonArray.length(); i++) {
            jsonValues.add(jsonArray.getJSONObject(i));
        }
        Collections.sort( jsonValues, new Comparator<JSONObject>() {
            private final String KEY_NAME = key;
            @Override
            public int compare(JSONObject a, JSONObject b) {
                String valA = new String();
                String valB = new String();
                try {
                    valA = (String) a.get(KEY_NAME);
                    valB = (String) b.get(KEY_NAME);
                }
                catch (JSONException e) { }
                return valA.compareTo(valB);
            }
        });
        JSONArray sortedJsonArray = new JSONArray();
        for (int i = 0; i < jsonArray.length(); i++) {
            sortedJsonArray.put(jsonValues.get(i));
        }
        return sortedJsonArray;
    }

    public void removerAssinaturaCliente(Integer contrato, Integer aditivo, Integer usuarioResponsavel, Boolean assinaturaCancelamento, Boolean assinatura2) throws Exception {
        UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(usuarioResponsavel, Uteis.NIVELMONTARDADOS_MINIMOS);

        String problemaRemoverAssinatura = "";
        ContratoAssinaturaDigitalVO assinaturaAnterior = null;
        CancelamentoAssinaturaDigitalVO assinaturaCancelAnterior = null;

        if (assinaturaCancelamento != null && assinaturaCancelamento) {
            assinaturaCancelAnterior = cadCancelDao.consultarPorContratoCancelamento(contrato);
            try {
                cadCancelDao.excluirAssinaturaPeloCancelamentoContrato(contrato);
                MidiaService.getInstance().deleteObject(assinaturaCancelAnterior.getAssinatura());
            } catch (Exception e) {
                problemaRemoverAssinatura = e.getMessage();
            }
        }else {
            assinaturaAnterior = UteisValidacao.emptyNumber(aditivo) ? cadDao.consultarPorContrato(contrato) : cadDao.consultarPorContratoEAditivo(contrato, aditivo);
            try {
                if (assinatura2 != null && assinatura2) {
                    if (!UteisValidacao.emptyString(assinaturaAnterior.getAssinatura())) {
                        cadDao.excluirAssinaturaPeloContrato(contrato);
                        MidiaService.getInstance().deleteObject(assinaturaAnterior.getAssinatura());
                    }
                    cadDao.excluirAssinatura2PeloContrato(contrato);
                    MidiaService.getInstance().deleteObject(assinaturaAnterior.getAssinatura2());
                } else {
                    if (UteisValidacao.emptyNumber(aditivo)) {
                        cadDao.excluirAssinaturaPeloContrato(contrato);
                    } else {
                        cadDao.excluirAssinaturaPeloContratoEAditivo(contrato, aditivo);
                    }
                    MidiaService.getInstance().deleteObject(assinaturaAnterior.getAssinatura());
                    if (!UteisValidacao.emptyString(assinaturaAnterior.getAssinatura2())) {
                        cadDao.excluirAssinatura2PeloContrato(contrato);
                        MidiaService.getInstance().deleteObject(assinaturaAnterior.getAssinatura2());
                    }
                }
            } catch (Exception e) {
                problemaRemoverAssinatura = e.getMessage();
            }
            atualizarImpressaoContrato(contrato, usuarioResponsavel, aditivo);
        }

        Contrato contratoDao = new Contrato(con);
        ContratoVO pessoa = contratoDao.consultarPorCodigo(contrato, Uteis.NIVELMONTARDADOS_MINIMOS);

        Log logFacade = new Log(con);

        StringBuilder sb = new StringBuilder();
        if (UteisValidacao.emptyString(problemaRemoverAssinatura) && assinaturaCancelamento != null && !assinaturaCancelamento) {
            registrarLog(pessoa.getPessoa().getCodigo(), usuarioVO, logFacade,
                    "REMOVER ASSINATURA DIGITAL", "CONTRATO", "REMOVER ASSINATURA DIGITAL DO CONTRATO", "Sucess", assinaturaAnterior.getAssinatura(), sb.toString());

            sb.append("Assinatura excluida com sucesso. ");
        }
        if (UteisValidacao.emptyString(problemaRemoverAssinatura) && (assinaturaCancelamento != null && assinaturaCancelamento)) {
            registrarLog(pessoa.getPessoa().getCodigo(), usuarioVO, logFacade,
                    "REMOVER ASSINATURA DIGITAL", "CANCELAMENTO CONTRATO", "REMOVER ASSINATURA DIGITAL DO CANCELAMENTO DO CONTRATO", "Sucess", assinaturaCancelAnterior.getAssinatura(), sb.toString());

            sb.append("Assinatura excluida com sucesso. ");
        }

    }

    public void removerAssinaturaEletronicaCliente(Integer contrato, Integer usuarioResponsavel, Boolean assinaturaCancelamento) throws Exception {

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuarioResponsavel, Uteis.NIVELMONTARDADOS_MINIMOS);

        String problemaRemoverAssinatura = "";
        ContratoAssinaturaDigitalVO assinaturaAnterior = null;
        CancelamentoAssinaturaDigitalVO assinaturaCancelAnterior = null;

        if (assinaturaCancelamento != null && assinaturaCancelamento) {
            assinaturaCancelAnterior = cadCancelDao.consultarPorContratoCancelamento(contrato);
            try {
                cadCancelDao.excluirAssinaturaEletronicaCancelamento(contrato);
                MidiaService.getInstance().deleteObject(assinaturaCancelAnterior.getAssinatura());
            } catch (Exception e) {
                problemaRemoverAssinatura = e.getMessage();
            }
        } else {
            assinaturaAnterior = cadDao.consultarPorContrato(contrato);
            try {
                cadDao.excluirAssinaturaEletronicaContrato(contrato);
                MidiaService.getInstance().deleteObject(assinaturaAnterior.getAssinatura());
            } catch (Exception e) {
                problemaRemoverAssinatura = e.getMessage();
            }
            atualizarImpressaoContrato(contrato, usuarioResponsavel, null);
        }

        Contrato contratoDao = new Contrato(con);
        ContratoVO pessoa = contratoDao.consultarPorCodigo(contrato, Uteis.NIVELMONTARDADOS_MINIMOS);

        Log logFacade = new Log(con);

        StringBuilder sb = new StringBuilder();
        if (UteisValidacao.emptyString(problemaRemoverAssinatura) && assinaturaCancelamento != null && !assinaturaCancelamento) {
            registrarLog(pessoa.getPessoa().getCodigo(), usuarioVO, logFacade,
                    "REMOVER ASSINATURA DIGITAL", "CONTRATO", "REMOVER ASSINATURA DIGITAL DO CONTRATO", "Sucess", assinaturaAnterior.getAssinatura(), sb.toString());

            sb.append("Assinatura excluida com sucesso. ");
        }
        if (UteisValidacao.emptyString(problemaRemoverAssinatura) && (assinaturaCancelamento != null && assinaturaCancelamento)) {
            registrarLog(pessoa.getPessoa().getCodigo(), usuarioVO, logFacade,
                    "REMOVER ASSINATURA DIGITAL", "CANCELAMENTO CONTRATO", "REMOVER ASSINATURA DIGITAL DO CANCELAMENTO DE CONTRATO", "Sucess", assinaturaCancelAnterior.getAssinatura(), sb.toString());

            sb.append("Assinatura excluida com sucesso. ");
        }

    }

    private void atualizarImpressaoContrato(Integer contrato, Integer usuarioResponsavel, Integer aditivo) {
        try {
            UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(usuarioResponsavel, Uteis.NIVELMONTARDADOS_MINIMOS);
            conTexPadraoDao.gravarHtmlContrato(contrato, usuarioVO, aditivo);
        } catch (Exception e) {
            Logger.getLogger(ContratoAssinaturaDigitalServiceImpl.class.getName()).log(Level.SEVERE, e.getMessage());
        }
    }
}
