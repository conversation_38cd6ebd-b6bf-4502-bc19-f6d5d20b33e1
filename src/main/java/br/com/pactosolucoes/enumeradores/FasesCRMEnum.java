package br.com.pactosolucoes.enumeradores;

/**
 * <AUTHOR>
 */
public enum FasesCRMEnum {

    AGENDAMENTO("Agendamentos Presenciais", 1, "agendamentos_hoje.png", "AG", "Meta Agendados", 2, TipoFaseCRM.VENDAS, "Indicador_de_Vendas:Agendamentos", "Para atingir essa meta deve marcar o comparecimento, reagendar, fechar uma negociação com o aluno ou uma objeção.", "Agend.<br>Presenciais", "fases-da-meta-diaria-do-crm/"),
    LIGACAO_AGENDADOS_AMANHA("Agendados de Amanhã", 14, "ligacao_amanha.png", "LA", "Ligação para Agendados de Amanhã", 3, TipoFaseCRM.VENDAS, "Indicador_de_Vendas:Agendados_de_Amanhã", "Para atingir essa meta é necessário efetuar um contato ou reagendar o aluno.", "Agend. de<br>Amanh<PERSON>","fases-da-meta-diaria-do-crm/"),
    VINTE_QUATRO_HORAS("Visitantes 24h", 2, "vinteQuatroHoras.png", "HO", "Meta 24 Horas",1, TipoFaseCRM.VENDAS, "Indicador_de_Vendas:24_Horas", "Para atingir essa meta o resultado do contato deve ser um agendamento ou uma objeção.", "Visitantes<br>24h", "fases-da-meta-diaria-do-crm/"),
    RENOVACAO("Renovação", 3, "renovacoes.png", "RE", "Meta Renovação", 4, TipoFaseCRM.VENDAS, "Indicador_de_Vendas:Renovação", "Para atingir essa meta o resultado do contato deve gerar um agendamento de aula, visita ou ligação.", "Renovação", "fases-da-meta-diaria-do-crm/"),
    DESISTENTES("Desistentes", 11, "perdas.png", "PE", "Meta Desistentes", 5, TipoFaseCRM.VENDAS, "Indicador_de_Retenção:Desistentes", "Para atingir essa meta, o resultado do contato deve ser algum agendamento ou objeção.", "Desistentes", "fases-da-meta-diaria-do-crm/"),
    VISITANTES_ANTIGOS("Visitantes Antigos", 19, "visitantes_antigos.png", "VA", "Meta Visitantes Antigos", 6, TipoFaseCRM.VENDAS, "Indicador_de_Vendas:Visitantes_Antigos", "Para atingir essa meta, basta realizar qualquer agendamento ou uma objeção.", "Visitantes<br>Antigos", "fases-da-meta-diaria-do-crm/"),
    EX_ALUNOS("Ex-Alunos", 17, "agendamentos_hoje.png", "EX", "Meta Ex-Alunos", 7, TipoFaseCRM.VENDAS, "Indicador_de_Vendas:Ex-Alunos", "Para atingir essa meta, o resultado do contato deve ser um agendamento ou uma objeção.", "Ex-Alunos", "fases-da-meta-diaria-do-crm/"),
    INDICACOES("Indicações", 7, "indicacoes.png", "IN", "Meta Indicados", 8, TipoFaseCRM.VENDAS, "Indicador_de_Vendas:Indicações", "Para atingir essa meta é necessário registrar uma indicação e realizar qualquer agendamento ou uma objeção.", "Indicações", "fases-da-meta-diaria-do-crm/"),
    
    CONVERSAO_INDICADOS("Conversão de Indicados", 6, "conversao_agendados.png", "CI", "Meta Conversão de Indicados", 9, TipoFaseCRM.VENDAS, "", "", "Conversão<br>Indicados", "fases-da-meta-diaria-do-crm/"),
    CONVERSAO_AGENDADOS("Conversão de Agendados", 13, "conversao_agendados.png", "CV", "Meta Conversão de Agendados", 10, TipoFaseCRM.VENDAS, "", "", "Conversão<br>Agendados", "fases-da-meta-diaria-do-crm/"),
    CONVERSAO_EX_ALUNOS("Conversão de Ex-Alunos", 18, "conversao_agendados.png", "CE", "Meta Conversão de Ex-Alunos", 11, TipoFaseCRM.VENDAS, "", "", "Conversão<br>Ex-Alunos", "fases-da-meta-diaria-do-crm/"),
    CONVERSAO_VISITANTES_ANTIGOS("Conversão de Visitantes Antigos", 5, "conversao_agendados.png", "CA", "Meta Conversão de Visitantes Antigos", 12, TipoFaseCRM.VENDAS, "", "Para atingir essa meta o resultado do contato deve ser agendamento ou objeção.", "Conversão<br>Visitantes Antigos", "fases-da-meta-diaria-do-crm/"),

    CONVERSAO_DESISTENTES("Conversão de Desistentes", 21, "conversao_agendados.png", "CD", "Meta Conversão de Desistentes", 13, TipoFaseCRM.VENDAS, "", "", "Conversão<br>Desistentes", "fases-da-meta-diaria-do-crm/"),
    CONVERSAO_PASSIVO("Conversão de Receptivo", 20, "conversao_agendados.png", "CT", "Meta Conversão de Receptivo", 26, TipoFaseCRM.VENDAS, "", "", "Conversão<br>Receptivo", "fases-da-meta-diaria-do-crm/"),

    GRUPO_RISCO("Grupo de Risco", 8, "grupo_de_risco.png", "RI", "Meta Grupo de Risco", 8, TipoFaseCRM.RETENCAO, "Indicador_de_Retenção:Grupo_de_Risco", "Para atingir essa meta basta realizar qualquer contato, agendamento ou objeção.", "Grupo Risco", "fases-da-meta-diaria-do-crm/"),
    VENCIDOS("Vencidos", 12, "vencidos.png", "VE", "Meta Vencidos", 9, TipoFaseCRM.RETENCAO, "Indicador_de_Vendas:Vencidos", "Para atingir essa meta deve realizar qualquer agendamento, renovação do contrato ou objeção.", "Vencidos", "fases-da-meta-diaria-do-crm/"),
    POS_VENDA("Pós Venda", 4, "pos_vendas.png", "PV", "Meta Pós Venda", 10, TipoFaseCRM.RETENCAO, "Indicador_de_Retenção:Pós-Venda", "Para atingir essa meta é necessário realizar qualquer contato, agendamento ou objeção.", "Pós Venda", "fases-da-meta-diaria-do-crm/"),
    FALTOSOS("Faltosos", 10, "faltantes.png", "FA", "Meta Faltosos", 11, TipoFaseCRM.RETENCAO, "Indicador_de_Retenção:Faltosos", "Para atingir essa meta basta realizar qualquer contato, agendamento ou objeção.", "Faltosos", "fases-da-meta-diaria-do-crm/"),
    ANIVERSARIANTES("Aniversariantes", 9, "aniversariantes.png", "AN", "Meta Aniversariantes", 12, TipoFaseCRM.RETENCAO, "Indicador_de_Retenção:Aniversariantes", "Para atingir essa meta basta realizar qualquer contato, agendamento ou objeção.", "Aniversariantes", "fases-da-meta-diaria-do-crm/"),

    ULTIMAS_SESSOES("Últimas Sessões", 15, "sessoes.png", "SF", "Últimas Sessões", 14, TipoFaseCRM.ESTUDIO, "AgendaStudio:SessoesFinais", "", "Últimas Sessões", "fases-da-meta-diaria-do-crm/"),
    SEM_AGENDAMENTO("Sessões sem agenda", 16, "sem_agendamento.png", "SA", "Sem agendamento", 15, TipoFaseCRM.ESTUDIO, "AgendaStudio:SemAgendamento", "", "Sessões sem agenda", "fases-da-meta-diaria-do-crm/"),


    //NOVOS INDICADORES

    AGENDAMENTOS_LIGACOES("Agendamentos de Ligações", 23, "sem_agendamento.png", "AL", "Sem agendamento", 23, TipoFaseCRM.VENDAS, "AgendaStudio:SemAgendamento", "Ligações agendadas para o dia.", "Agendamentos de Ligações", "fases-da-meta-diaria-do-crm/"),
    INDICACOES_SEM_CONTATO("Indicações sem Contato", 22, "sem_agendamento.png", "IS", "Sem agendamento", 22, TipoFaseCRM.INDICADORES, "AgendaStudio:SemAgendamento", "Para realizar esse indicador, basta realizar qualquer agendamento ou uma objeção.", "Indicações sem Contato", "fases-da-meta-diaria-do-crm/"),
    PASSIVO("Receptivo", 24, "sem_agendamento.png", "CP", "Sem agendamento", 24, TipoFaseCRM.INDICADORES, "AgendaStudio:SemAgendamento", "Registrar os contatos que a academia recebe para que posteriormente possa ser realizada ações de vendas.", "Receptivo", "fases-da-meta-diaria-do-crm/"),

    ALUNO_GYMPASS("Aluno Wellhub", 26, "conversao_agendados.png", "GY", "Aluno GymPass", 26, TipoFaseCRM.VENDAS, "AlunoGymPass", "Para atingir essa meta é necessário realizar qualquer contato, agendamento ou objeção.", "Aluno Wellhub", "fases-da-meta-diaria-do-crm/"),

    //CRM-EXTRA
    CRM_EXTRA("Meta Extra", 25, "sem_agendamento.png", "CR", "Sem agendamento", 25, TipoFaseCRM.CRMEXTRA, "AgendaStudio:SemAgendamento", "Para atingir essa meta é necessário realizar qualquer contato, agendamento ou objeção.", "Meta Extra","fases-da-meta-diaria-do-crm/"),
    
    //LEAD
    LEADS_HOJE("Leads Hoje", 27, "vinteQuatroHoras.png", "LH", "Meta Leads",27, TipoFaseCRM.LEAD, "Indicador_de_Vendas:24_Horas", "Para atingir essa meta o resultado do contato deve ser um agendamento ou uma objeção.", "Leads Hoje", "fases-da-meta-diaria-do-crm/"),
    LEADS_ACUMULADAS("Leads Acumuladas", 28, "vinteQuatroHoras.png", "LC", "Meta Leads",28, TipoFaseCRM.LEAD, "Indicador_de_Vendas:24_Horas", "Para atingir essa meta o resultado do contato deve ser um agendamento ou uma objeção.", "Leads Acumulado", "fases-da-meta-diaria-do-crm/"),

    VISITA_RECORRENTE("Visitas recorrentes", 29, "visita.png", "VR", "Visitas recorrentes", 29, TipoFaseCRM.VENDAS, "", "Para atingir essa meta é necessário realizar qualquer contato, agendamento ou objeção.", "Visitas recorrentes", "fases-da-meta-diaria-do-crm/"),

    CONVERSAO_LEAD("Conversão de Lead", 30, "conversao_agendados.png", "CL", "Meta Conversão de Lead", 30, TipoFaseCRM.VENDAS, "", "", "Conversão<br>Lead", "fases-da-meta-diaria-do-crm/"),

    ALUNO_ULTIMO_ACESSO_GYMPASS("Últ. Acesso Wellhub", 31, "visitantes_antigos.png", "UG", "Últ. Acesso GymPass", 31, TipoFaseCRM.VENDAS, "Último Acesso GymPass", "Para atingir essa meta é necessário realizar qualquer contato, agendamento ou objeção.", "Último Acesso Wellhub", "fases-da-meta-diaria-do-crm/"),
    FILA_ESPERA_TURMA_CRM("Fila de espera turma", 32, "visitantes_antigos.png", "FT", "Fila de espera turma", 32, TipoFaseCRM.RETENCAO, "Fila de espera turma", "Para atingir essa meta o resultado do contato deve ser um agendamento ou uma objeção.", "Fila de espera turma", "fases-da-meta-diaria-do-crm/"),
    ;
    private String descricao;
    private String sigla;
    private Integer codigo;
    private String imagem;
    private Integer ordemTotalizador;
    private TipoFaseCRM tipoFase;
    private String urlWiki;
    private String objetivo;
    private Integer posicao;
    private String descricaoCurtaBI;

    private String urlLinkBaseConhecimento;

    private FasesCRMEnum(String descricao, Integer codigo, String imagem, String sigla, String identificador,
                         Integer ordemTotalizador, TipoFaseCRM tipoFase, String urlWiki, String objetivo, String descricaoCurtaBI, String urlLinkBaseConhecimento) {
        this.descricao = descricao;
        this.codigo = codigo;
        this.imagem = imagem;
        this.sigla = sigla;
        this.ordemTotalizador = ordemTotalizador;
        this.tipoFase = tipoFase;
        this.urlWiki = urlWiki;
        this.objetivo = objetivo;
        this.descricaoCurtaBI = descricaoCurtaBI;
        this.urlLinkBaseConhecimento = urlLinkBaseConhecimento;
    }

    public static FasesCRMEnum getFase(Integer codigo) {
        FasesCRMEnum fase = null;
        for (FasesCRMEnum faseCRM : FasesCRMEnum.values()) {
            if (faseCRM.getCodigo().equals(codigo)) {
                fase = faseCRM;
            }
        }
        return fase;
    }

    public static FasesCRMEnum getFasePorSigla(String sigla) {
        FasesCRMEnum fase = null;
        for (FasesCRMEnum faseCRM : FasesCRMEnum.values()) {
            if (faseCRM.getSigla().equals(sigla)) {
                fase = faseCRM;
            }
        }
        return fase;
    }

    public static String getIdentificador(String sigla) {
        FasesCRMEnum fase = null;
        for (FasesCRMEnum faseCRM : FasesCRMEnum.values()) {
            if (faseCRM.getSigla().equals(sigla)) {
                fase = faseCRM;
            }
        }
        if (fase == null) {
            return "";
        }
        return fase.getDescricao();
    }

    public static FasesCRMEnum getFasePorDescricaoBI(String descricaoCurtaBI) {
        FasesCRMEnum fase = null;
        for (FasesCRMEnum faseCRM : FasesCRMEnum.values()) {
            if (faseCRM.getDescricaoCurtaBI().equals(descricaoCurtaBI)) {
                fase = faseCRM;
            }
        }
        return fase;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getImagem() {
        if (imagem == null) {
            imagem = "";
        }
        return imagem;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public Integer getOrdemTotalizador() {
        return ordemTotalizador;
    }

    public void setOrdemTotalizador(Integer ordemTotalizador) {
        this.ordemTotalizador = ordemTotalizador;
    }

    public TipoFaseCRM getTipoFase() {
        return tipoFase;
    }

    public void setTipoFase(TipoFaseCRM tipoFase) {
        this.tipoFase = tipoFase;
    }

    public String getUrlWiki() {
        return urlWiki;
    }

    public void setUrlWiki(String urlWiki) {
        this.urlWiki = urlWiki;
    }

    public String getObjetivo() {
        return objetivo;
    }

    public void setObjetivo(String objetivo) {
        this.objetivo = objetivo;
    }

    public Integer getPosicao() {
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    public String getDescricaoCurtaBI() {
        return descricaoCurtaBI;
    }

    public void setDescricaoCurtaBI(String descricaoCurtaBI) {
        this.descricaoCurtaBI = descricaoCurtaBI;
    }

    public String getUrlLinkBaseConhecimento() {
        return urlLinkBaseConhecimento;
    }

    public static String retornarSiglas(){
        StringBuilder siglas = new StringBuilder();
        for (FasesCRMEnum object : FasesCRMEnum.values()) {
            if (siglas.toString().equals("")){
                siglas.append("'").append(object.getSigla()).append("'");
            }else{
                siglas.append(",'").append(object.getSigla()).append("'");
            }
        }
        return siglas.toString();
    }

    public static String getSiglaPorString(String key) {
        try {
            FasesCRMEnum fase = FasesCRMEnum.valueOf(key);
            return fase.getSigla();
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

}
