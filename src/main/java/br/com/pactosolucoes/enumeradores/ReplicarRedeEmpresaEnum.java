package br.com.pactosolucoes.enumeradores;

public enum ReplicarRedeEmpresaEnum {
    USUARIO(1),
    COLABORADOR(2),
    PERFIL_ACESSO(3),
    MALA_DIRETA(4),
    PLANO(5),
    PRODUTO(6),
    FORNECEDOR(7),
    PLANO_CONTA(8);

    Integer codigo;

    ReplicarRedeEmpresaEnum(Integer codigo) {
        this.codigo = codigo;
    }

    public static ReplicarRedeEmpresaEnum getFromCodigo(Integer cod) {
        for (ReplicarRedeEmpresaEnum cfg : ReplicarRedeEmpresaEnum.values()) {
            if (cod != null && cfg.getCodigo().equals(cod)) {
                return cfg;
            }
        }
        return null;

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}
