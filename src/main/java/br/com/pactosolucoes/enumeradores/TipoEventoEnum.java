package br.com.pactosolucoes.enumeradores;

import negocio.comuns.crm.ConfigEventoMailingTO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum TipoEventoEnum {

    EVENTO_ANIVERSARIANTES("Aniversariantes", "Fazem aniversário ", 3,
            "\n WHERE extract( day from sw.datanascimento ) >= extract( day from timestamp '%s') "
                    + "\n and extract( month from sw.datanascimento ) >= extract( month from timestamp '%s' )"
                    + "\n and extract( day from sw.datanascimento ) <= extract( day from timestamp '%s' )"
                    + "\n and extract( month from sw.datanascimento ) <= extract( month from timestamp '%s' )",
            "", true, false, false, false, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE),

    EVENTO_MATRICULADOS("Alunos matriculados", "Matriculados ", 4,
            " WHERE MAT_CONTRATO.datalancamento::date BETWEEN '%s' AND '%s' ",
            " LEFT JOIN contrato MAT_CONTRATO ON MAT_CONTRATO.pessoa = cli.pessoa AND MAT_CONTRATO.situacaoContrato = 'MA' ", true, false, true, false, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.APOS_MATRICULA),

    EVENTO_REMATRICULADOS("Alunos rematriculados", "Rematriculados ", 5,
            " WHERE sw.dataultimarematricula BETWEEN '%s' AND '%s' ",
            "", true, false, true, false, true, OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.APOS_REMATRICULA),

    EVENTO_RENOVADOS("Alunos renovados", "Renovados ", 6,
            " WHERE con.datalancamento::date BETWEEN '%s' AND '%s' AND con.situacaocontrato = 'RN' ",
            "\n INNER JOIN contrato con ON con.pessoa = sw.codigopessoa ", true, false, true, false, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.APOS_RENOVACAO),

    VISITANTES("Visitantes", "Visitantes cadastrados ",
            7,
            " WHERE pes.datacadastro BETWEEN '%s' AND '%s' AND sw.situacao LIKE 'VI'",
            " INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ",
            true,
            false,
            true,
            false,
            true,
            OcorrenciaEnum.INSTANTANEO,
            OcorrenciaEnum.DIARIAMENTE,
            OcorrenciaEnum.INCLUSAO_VISITANTE),

    RENOVAR("Contratos vencendo", "Contratos vencendo ", 8,
            " WHERE con.vigenciaateajustada BETWEEN '%s' AND '%s' AND con.situacao <> 'TR'  AND con.situacao <> 'CA' " +
                    " AND NOT EXISTS (SELECT contratooperacao.codigo FROM contratooperacao contratooperacao WHERE contratooperacao.contrato = con.codigo " +
                    " AND contratooperacao.tipooperacao = 'CA')  AND con.bolsa = false " +
                    " AND con.contratoresponsavelrenovacaomatricula = 0 AND con.contratoresponsavelrematriculamatricula = 0  ",
            "\n INNER JOIN contrato con ON con.pessoa = cli.pessoa ", true, false, false, true, false, OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),

    GRUPO_RISCO("Grupo de risco", "", 9,
            " WHERE sw.pesorisco IN (%s) ",
            "", false, false, false, false, false, OcorrenciaEnum.INSTANTANEO),

    FALTOSOS("Faltosos", "", 10,
            " WHERE sw.situacao = 'AT' and sw.situacaocontrato NOT IN ('AE','CR') and ('%s' :: DATE - ac.dthrentrada :: DATE) %s ",
            "INNER JOIN acessocliente ac ON cli.uacodigo = ac.codigo ", false, false, false, false, true, OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.SEMANALMENTE, OcorrenciaEnum.MENSALMENTE),

    SALDO_PONTOS("Saldo de pontos", "", 36,
            " WHERE hp.pontostotal > %s ",
            "INNER JOIN (SELECT * " +
                    " FROM historicopontos hpFiltrado " +
                    " INNER JOIN (select max(codigo) " +
                    " from historicopontos " +
                    " group by cliente) as lista ON lista.max = hpFiltrado.codigo) hp " +
                    " ON hp.cliente = cli.codigo", false, false, false, false, true, OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.SEMANALMENTE, OcorrenciaEnum.MENSALMENTE),

    DEBITO("Alunos com débito", "", 11,
            " WHERE pes.codigo in (select pessoa from movparcela where movparcela.situacao = 'EA' AND movparcela.datavencimento < '%s' " +
                    " and (timestamp '%s' - movparcela.datavencimento ) >= '%s days' \n "
        + "GROUP BY pessoa \n HAVING sum(valorparcela) BETWEEN '%s' AND '%s')",
            "\n INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ", false, false, false, false, false,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.SEMANALMENTE, OcorrenciaEnum.MENSALMENTE),

    PENDENCIAS("Alunos com pendências de cadastro", "", 12,
            " WHERE sw.codigocliente IN (SELECT cliente FROM clientemensagem "
                    + " WHERE mensagem SIMILAR TO '%s' AND tipomensagem = 'DI') ",
            "", false, false, false, false, true,  OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.SEMANALMENTE, OcorrenciaEnum.MENSALMENTE),

    INDICADOS("Indicados", "", 13,
            " SELECT sw.codigo, sw.nomeindicado as nome, telefoneindicado as telefones, email as emails  FROM indicado sw",
            "", false, false, false, false, true, OcorrenciaEnum.INSTANTANEO),


    COMPRA_PRODUTO("Compra de produtos", "Que compraram ", 15,
            "\n WHERE pes.codigo in (SELECT  mp.pessoa FROM movproduto mp INNER JOIN produto p ON mp.produto = p.codigo " +
                    "\n  WHERE mp.datalancamento::date BETWEEN '%s' AND '%s'  " +
                    "   AND p.tipoproduto in (%s)) ",
            "\n INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ", true, false, true, false, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),

    AGENDAMENTOS_PRODUTO("Agendamentos por produto", "Agendados ", 16,
            ConfigEventoMailingTO.SQL_PADRAO_ESTUDIO,
            "\n INNER JOIN sch_estudio.agenda age ON age.id_cliente = cli.codigo ", true, true, true, true, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),
    AGENDAMENTOS_AMBIENTE("Agendamentos por ambiente", "Agendados ", 17,
            ConfigEventoMailingTO.SQL_PADRAO_ESTUDIO,
            "\n INNER JOIN sch_estudio.agenda age ON age.id_cliente = cli.codigo ", true, true, true, true, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),
    AGENDAMENTOS_PROFISSIONAL("Agendamentos por profissional", "Agendados ", 22,
            ConfigEventoMailingTO.SQL_PADRAO_ESTUDIO,
            "\n INNER JOIN sch_estudio.agenda age ON age.id_cliente = cli.codigo ", true, true, true, true, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),
    AGENDAMENTOS_PERIODO("Agendamentos por período", "Agendados ", 18,
            ConfigEventoMailingTO.SQL_PADRAO_ESTUDIO,
            "\n INNER JOIN sch_estudio.agenda age ON age.id_cliente = cli.codigo ", true, true, true, true, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),
    PRODUTOS_VENDIDOS("Produtos vendidos", "Vendidos ", 19,
            "\n WHERE pes.codigo in (SELECT  mp.pessoa FROM movproduto mp INNER JOIN produto p ON mp.produto = p.codigo " +
                    "\n  WHERE mp.datalancamento::date BETWEEN '%s' AND '%s'  %s) ",
            "\n INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ", true, true, false, false, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),
    A_AGENDAR("Produtos a agendar", "", 20,
            "\n WHERE 1=1 %s ",
            "\n INNER JOIN sch_estudio.agenda_agendar age ON age.id_cliente = cli.codigo ", false, true, false, false, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),
    A_FATURAR("Produtos a faturar", "", 21,
            "\n WHERE 1=1 %s ",
            "\n INNER JOIN sch_estudio.agenda_faturar age ON age.id_cliente = cli.codigo ", false, true, false, false, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),
    VINTE_QUATRO_HORAS("24 horas", "24 horas", 23,
            "\n WHERE (sw.situacao = 'VI' OR sw.situacao = 'IN') AND not exists (select contrato.codigo from contrato  where contrato.pessoa = pes.codigo and contrato.vigenciade >= Cast((SELECT cast('%s' AS DATE) - INTERVAL '1 DAY' AS DATA) AS DATE))" +
            "\n AND Cast(pes.datacadastro AS DATE) = Cast((SELECT cast('%s' AS DATE) - INTERVAL '1 DAY' AS DATA) AS DATE) ",
            "\n INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ", false, false, false, false, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE),


    VENCIMENTO_PRODUTO("Produtos vencendo", "Vencendo ", 14,
            "\n WHERE pes.codigo in (SELECT  mp.pessoa FROM movproduto mp INNER JOIN produto p ON mp.produto = p.codigo " +
                    "\n WHERE mp.datafinalvigencia BETWEEN '%s' AND '%s'  " +
                    "   AND p.codigo in (%s)) ",
            "\n INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ", true, false, false, true, false,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),


    PRODUTOS_VENCIDOS("Produtos vencidos", "Vencidos de ", 24,
                           "\n WHERE pes.codigo in (SELECT  mp.pessoa FROM movproduto mp INNER JOIN produto p ON mp.produto = p.codigo " +
                           "\n WHERE mp.datafinalvigencia BETWEEN '%s' AND '%s'  " +
                           "   AND p.codigo in (%s) and not exists (select codigo from movproduto where pessoa = mp.pessoa and produto = mp.produto and datafinalvigencia > mp.datafinalvigencia)) ",
                           "\n INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ", true, false, true, false, false,
                   OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),
    PARCELA_VENCENDO("Parcelas vencendo", "",25,
            " WHERE 1 = 1 ",
            "\n INNER JOIN pessoa pesp ON pesp.codigo = cli.pessoa",  true, false, false, true, false,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),
    ARMARIO_VENCENDO("Parcelas Armário vencendo", "",26,
            " WHERE pes.codigo in (select pessoa from movparcela where movparcela.situacao = 'EA' AND movparcela.datavencimento  BETWEEN '%s' AND '%s' and movparcela.vendaavulsa = alu.vendaavulsa)",
            "\n INNER JOIN pessoa pes ON pes.codigo = cli.pessoa\nINNER JOIN AluguelArmario alu ON alu.cliente = cli.codigo\n",  true, false, false, true, false,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),

    PARCELAS_VENCIDAS("Parcelas vencidas", "", 27,
            " WHERE pes.codigo in (SELECT pessoa FROM movparcela WHERE 1 = 1 AND (%s) AND situacao = 'EA' AND NOT descricao ~ 'MULTA' GROUP BY pessoa HAVING count(codigo) >= %s) "
            + " AND NOT EXISTS( SELECT 1 FROM historicocontato hist WHERE hist.maladireta = %s AND hist.cliente = cli.codigo AND hist.dataproximoenvio >= '%s' )",
            "\n INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ", false, false, false, false, false,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE),

    PARCELAS_RECORRENCIA("Parcelas recorrência", "Vencidas", 28,
            "\n where 1 = 1 \n" +
                    "and mp.situacao = 'EA'\n" +
                    "and mp.nrtentativas > 0\n" +
                    "and (CURRENT_DATE - mp.datavencimento::date) >= %s \n" +
                    " %s ",
            "\n inner join movparcela mp on mp.pessoa = cli.pessoa\n" +
            "\n left join remessaitem ri on ri.movparcela = mp.codigo\n" +
            "\n left join transacaomovparcela tmp on tmp.movparcela = mp.codigo\n" +
            "\n left join transacao t on t.codigo = tmp.transacao ", false, false, true, false, false,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.SEMANALMENTE, OcorrenciaEnum.MENSALMENTE),

    CONTRATO_CREDITO_TREINO("Contrato Crédito Treino", "", 29,
            " WHERE 1 = 1 AND cdc.quantidadeCreditoDisponivel >= %s AND cdc.quantidadeCreditoDisponivel <= %s",
            " INNER JOIN contratoDuracao cd ON cd.contrato = sw.codigocontrato \n" +
            " INNER JOIN contratoDuracaoCreditoTreino cdc on cdc.contratoDuracao = cd.codigo", false, false, false, false, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.SEMANALMENTE, OcorrenciaEnum.MENSALMENTE),

    CARTOES_VENCENDO("Cartões de Crédito Vencendo",
            "Cartões Vencendo",
            30,
            " WHERE 1 = 1 AND acc.ativa = true AND acc.validadecartao in (LISTA_MESES) AND acc.validadecartao <> '' ",
            " inner join autorizacaocobrancacliente acc on acc.cliente = sw.codigocliente ",
            true,
            false,
            false,
            false,
            false,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.MENSALMENTE),

    //Pos venda tem a regra a parte para olhar se vai entrar ou não os contratos renovados.
    POS_VENDA("Pós Venda",
            "Pós Venda",
            31,
            "%s",
            " inner join contrato ON contrato.codigo = sw.codigocontrato",
            false,
            false,
            false,
            false,
            true,
            OcorrenciaEnum.INSTANTANEO,
            OcorrenciaEnum.DIARIAMENTE,
            OcorrenciaEnum.SEMANALMENTE,
            OcorrenciaEnum.MENSALMENTE,
            OcorrenciaEnum.VENDA_CONTRATO),

    FREEPASS("Freepass", "", 32,
            " WHERE pac.tipoacesso = 'PL' and ('%s' :: DATE - pac.datainicioacesso :: DATE) %s ",
            "INNER JOIN periodoacessocliente pac ON cli.pessoa = pac.pessoa ", false, false, false, false, true, OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.SEMANALMENTE, OcorrenciaEnum.MENSALMENTE),


    ACESSOS_INTERVALO_DIAS("Quantidade de acessos",
            "Quantidade de acessos em um intervalo de dias",
            33,
            " WHERE (CASE WHEN quantidadeAcessos IS NULL THEN 0 ELSE quantidadeAcessos END) BETWEEN {quantidadeMinimaAcessos} AND {quantidadeMaximaAcessos} ",
            "LEFT JOIN (SELECT cliente, COUNT(cliente) quantidadeAcessos " +
            "      FROM (SELECT cliente, TO_CHAR(dthrentrada, 'YYYY-MM-DD'), situacao " +
            "            FROM acessocliente " +
            "            WHERE DATE_PART('day', now() - dthrentrada) <= {intevaloDias} AND situacao IN ({situacoesAcesso})" +
            "            GROUP BY cliente, TO_CHAR(dthrentrada, 'YYYY-MM-DD'), situacao " +
            "            ORDER BY cliente" +
            "           ) acessoClientePorDia " +
            "      GROUP BY cliente" +
            ") quantidadeAcessosPorCliente ON quantidadeAcessosPorCliente.cliente = cli.codigo ",
            false,
            false,
            false,
            false,
            true,
            OcorrenciaEnum.INSTANTANEO,
            OcorrenciaEnum.DIARIAMENTE,
            OcorrenciaEnum.SEMANALMENTE,
            OcorrenciaEnum.MENSALMENTE),

    CANCELADO_INTERVALO_DIAS("Cancelados",
            "Cancelados a uma quantidade de dias",
            34,
            " WHERE DATE_PART('day', now() - sw.datavigenciaateajustada) <= {intevaloDias} AND sw.situacao = 'IN' AND sw.situacaocontrato = 'CA' {tipoCancelamento} ",
            "INNER JOIN contratooperacao co on co.contrato = sw.codigocontrato \n" +
                    "INNER JOIN usuario u on co.responsavel = u.codigo",
            false,
            false,
            false,
            false,
            true,
            OcorrenciaEnum.INSTANTANEO,
            OcorrenciaEnum.DIARIAMENTE,
            OcorrenciaEnum.SEMANALMENTE,
            OcorrenciaEnum.MENSALMENTE,
            OcorrenciaEnum.APOS_CANCELAMENTO),


    ITENS_NAO_APROVADOS_REMESSA("Pgtos. não aprovados de remessas",
            "",
            35,
            "WHERE 1 = 1 \n" +
                    "AND mp.situacao = 'EA'\n" +
                    "AND re.situacaoremessa = " + SituacaoRemessaEnum.RETORNO_PROCESSADO.getId() + " \n" +
                    "AND re.tipo = " + TipoRemessaEnum.EDI_CIELO.getId() + " \n" +
                    "AND re.retorno is not null \n" +
                    "AND split_part(split_part(split_part(re.props, 'DataHoraRetorno=', 2), '}',1), ',', 1) <> ''\n" +
                    "AND to_timestamp(substring(split_part(split_part(split_part(re.props, 'DataHoraRetorno=', 2), '}',1), ',', 1), 0, length(split_part(split_part(split_part(re.props, 'DataHoraRetorno=', 2), '}',1), ',', 1))-2)::double precision)::date = CURRENT_DATE \n" +
                    " %s ",
            "INNER JOIN movparcela mp on mp.pessoa = cli.pessoa\n" +
                    "INNER JOIN remessaitem ri on ri.movparcela = mp.codigo \n" +
                    "INNER JOIN remessa re on ri.remessa = re.codigo\n", false, false, false, false, false,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE),

    FAMILIA("Família", "", 37,
            " WHERE sw.codigocliente IN (select familiar from familiar ) ",
            "", false, false, false, false, true, OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE, OcorrenciaEnum.SEMANALMENTE),

    CARTOES_VENCIDOS("Cartões de Crédito Vencidos",
            "Cartões Vencidos",
            38,
            " WHERE to_date(acc.validadecartao, 'MM/YY') < to_date('" + Calendario.getMesAno(Calendario.hoje()) + "', 'MM/YY') AND acc.ativa IS TRUE AND acc.validadecartao <> ''",
            " INNER JOIN autorizacaocobrancacliente acc ON acc.cliente = sw.codigocliente ",
            false,
            false,
            false,
            false,
            false,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE, OcorrenciaEnum.SEMANALMENTE),

    EVENTO_DESISTENTES("Alunos desistentes", "Desistentes cujo contrato venceu",39,
            "WHERE sw.datavigenciaateajustada BETWEEN '%s' AND '%s' AND sw.situacao = 'IN' ",
            "",  true, false, false, false, true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.DIARIAMENTE, OcorrenciaEnum.MENSALMENTE, OcorrenciaEnum.SEMANALMENTE),

    CONVENIO_COBRANCA("Convênio de cobrança", "", 40,
            " WHERE 1 = 1 AND acc.ativa = true  LISTA_CONVENIO",
            " inner join autorizacaocobrancacliente acc on acc.cliente = sw.codigocliente ",
            true,
            false,
            false,
            false,
            true,
            OcorrenciaEnum.INSTANTANEO, OcorrenciaEnum.MENSALMENTE);

    private String descricao;
    private String label;
    private Integer codigo;
    private String where;
    private List<OcorrenciaEnum> ocorrenciasPossiveis;
    private boolean permiteDatas;
    private String join;
    private boolean estudio;
    private boolean dmais;
    private boolean dmenos;
    private boolean verificarOptIn;

    private TipoEventoEnum(String descricao, String label, Integer codigo, String sql, String join,
                           boolean permiteDatas, boolean estudio, boolean dmais, boolean dmenos, boolean verificarOptIn, OcorrenciaEnum... ocorrencias) {
        this.descricao = descricao;
        this.codigo = codigo;
        this.where = sql;
        this.join = join;
        this.permiteDatas = permiteDatas;
        this.estudio = estudio;
        ocorrenciasPossiveis = new ArrayList<OcorrenciaEnum>();
        ocorrenciasPossiveis.addAll(Arrays.asList(ocorrencias));
        Ordenacao.ordenarLista(ocorrenciasPossiveis, "codigo");
        this.label = label;
        this.dmais = dmais;
        this.dmenos = dmenos;
        this.verificarOptIn = verificarOptIn;
    }

    public static List<SelectItem> getListaCombo(boolean estudio) {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        for (TipoEventoEnum tipo : values()) {
            if (estudio || !tipo.isEstudio())
                lista.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
        }
        Ordenacao.ordenarLista(lista, "label");
        return lista;
    }

    public static List<SelectItem> getListaComboCRMExtra(boolean estudio) {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        for (TipoEventoEnum tipo : values()) {
            if (TipoEventoEnum.INDICADOS.equals(tipo)) {
                continue;
            }
            if (estudio || !tipo.isEstudio())
                lista.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));

        }
        Ordenacao.ordenarLista(lista, "label");
        return lista;
    }

    public static TipoEventoEnum obter(Integer codigo) {
        for (TipoEventoEnum tipo : values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return null;
    }

    public boolean isVerificarOptIn() {
        return verificarOptIn;
    }

    public boolean isDmais() {
        return dmais;
    }

    public void setDmais(boolean dmais) {
        this.dmais = dmais;
    }

    public boolean isDmenos() {
        return dmenos;
    }

    public void setDmenos(boolean dmenos) {
        this.dmenos = dmenos;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public boolean isPermiteDatas() {
        return permiteDatas;
    }

    public void setPermiteDatas(boolean permiteDatas) {
        this.permiteDatas = permiteDatas;
    }

    public boolean isEstudio() {
        return estudio;
    }

    public void setEstudio(boolean estudio) {
        this.estudio = estudio;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getWhere() {
        return where;
    }

    public void setWhere(String sql) {
        this.where = sql;
    }

    public List<OcorrenciaEnum> getOcorrenciasPossiveis() {
        return ocorrenciasPossiveis;
    }

    public void setOcorrenciasPossiveis(List<OcorrenciaEnum> ocorrenciasPossiveis) {
        this.ocorrenciasPossiveis = ocorrenciasPossiveis;
    }

    public List<SelectItem> getListaOcorrencias() {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        for (OcorrenciaEnum ocorrencia : ocorrenciasPossiveis) {
            lista.add(new SelectItem(ocorrencia.getCodigo(), ocorrencia.getDescricao()));
        }
        return lista;
    }

    public String getJoin() {
        return join;
    }

    public void setJoin(String join) {
        this.join = join;
    }
}
