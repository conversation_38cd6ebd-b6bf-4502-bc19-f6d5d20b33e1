package br.com.pactosolucoes.gympass;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import org.json.JSONObject;
import servicos.integracao.enumerador.IntegracoesEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import servlet.integracao.GympassBookingServlet;

import java.sql.ResultSet;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class CheckinGympassService {

    private final String GYMPASS_INCLUSAO = "INCLUSÃO GYMPASS - CHECKIN";
    private final String GYMPASS_CHECKIN_DIARIO_EXISTENTE = "INCLUSÃO GYMPASS AUTOMÁTICO - CHECKIN DIÁRIO JÁ EXISTENTE";
    private final String GYMPASS_WEBHOOK = "INCLUSÃO GYMPASS - WEBHOOK";
    private final String GYMPASS_FALHA = "INCLUSÃO GYMPASS - FALHA";
    private final String GYMPASS_NAO_EXISTE = "INCLUSÃO GYMPASS - ALUNO NÃO ENCONTRADO";
    private final String GYMPASS_JSON_WEBHOOK = "WEBHOOK GYMPASS - JSON";
    private final String GYMPASS_CONTATO = " Por favor entre em contato com a GYMPASS";
    private final String ERRO_GYMPASS_ACESS_CONTROL_VALIDATE = " Validação de acesso da GYMPASS";

    public String checkInGymPass(String key,
                                 String json,
                                 final int empresa) {
        String unique_token = "";
        String retorno = "";
        try {
            JSONObject objJson = new JSONObject(json);
            unique_token = objJson.getJSONObject("event_data").getJSONObject("user").get("unique_token").toString();
            String gympasstypenumber = objJson.getJSONObject("event_data").getJSONObject("gym").getJSONObject("product").get("id").toString();
            String produtoGymPass = objJson.getJSONObject("event_data").getJSONObject("gym").getJSONObject("product").get("id") + ";" +
                    objJson.getJSONObject("event_data").getJSONObject("gym").getJSONObject("product").get("description");
            Date hoje = Calendario.hoje();
            Calendar cal = Calendar.getInstance();
            boolean isHorarioVerao = cal.getTimeZone().inDaylightTime(Calendario.hoje());
            Date dataTimezone = hoje;
            if (isHorarioVerao){
                dataTimezone = new Date(dataTimezone.getTime() - cal.getTimeZone().getDSTSavings());
            }
            boolean existeCliente = DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().existeTokenGymPass(unique_token);
            gravaLogGYMPASS(key, new ClienteVO(), unique_token, GYMPASS_JSON_WEBHOOK, json);
            if (!existeCliente){
                gravaLogGYMPASS(key, new ClienteVO(), unique_token, GYMPASS_NAO_EXISTE, "ERRO: Não existe aluno cadastrado com esse Token.");
                return "ERRO: Não existe aluno cadastrado com esse Token.";
            }
            ClienteVO clienteGympass = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarGymPass(unique_token , empresa ,  Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(empresa,Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Map<String, String> headers = new HashMap<String, String>();
            final String urlMsValidate = String.format(
                    "%s/gymid/validate-user/%s/%s",
                    PropsService.getPropertyValue(PropsService.urlGymPassMs),
                    empresaVO.getCodigoGymPass(),
                    unique_token
            );

            Uteis.logar(null, "GYMPASS MS Requisição | " + urlMsValidate);

            JSONObject jsonObject = new JSONObject();
            JSONObject content = new JSONObject();

            try {
                retorno = ExecuteRequestHttpService.executeHttpRequestGETEncode(urlMsValidate, headers, "UTF-8");
                jsonObject = new JSONObject(retorno);
                content = new JSONObject(jsonObject.get("content").toString());
                gravaLogGYMPASS(key, clienteGympass, unique_token, GYMPASS_WEBHOOK, retorno);
            } catch (Exception e) {
                gravaLogGYMPASS(key, clienteGympass, unique_token, GYMPASS_FALHA, e.getMessage());
                if (e.getMessage().toLowerCase().contains("timed out") || e.getMessage().toLowerCase().contains("timedout")) {
                    throw new Exception("GymPass indisponível no momento. Por favor tente mais tarde.");
                } else {
                    throw e;
                }
            }
            Uteis.logar(null, "GYMPASS Retorno | " + retorno);

            if (content.getInt("code") == 200 || (content.getInt("code") == 400 && content.getString("key").equals("checkin.already.validated"))) {
                DaoAuxiliar.retornarAcessoControle(key).getZwFacade().notificarRecursoSistema(key, RecursoSistema.GYM_PASS_SUCESSO, null, empresaVO);
                //TODO: Para que fique mais correto tem que lançar um responsavel com nome de gympass para que identifique o lançamento melhor coloquei 0 para lembrar.
                clienteGympass.setResponsavelFreePass(0);
                clienteGympass.setFreePass(DaoAuxiliar.retornarAcessoControle(key).getProdutoDao().criarOuConsultarProdutoGymPass());
                Boolean naoTem = true;
                try {
                    Integer freepass = jaTemFreePass(Calendario.hoje(), clienteGympass.getCodigo(), key);
                    naoTem = UteisValidacao.emptyNumber(freepass);
                } catch (Exception e) {
                    Uteis.logar(e, Cliente.class);
                }
                if (naoTem) {
                    PeriodoAcessoClienteVO periodoAcesso = null;
                    if (Calendario.diferencaEmDias(dataTimezone,hoje) > 1 || Calendario.diferencaEmDias(dataTimezone,hoje) < -1){
                        periodoAcesso = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().incluirFreePass(clienteGympass, Calendario.hoje(), unique_token, gympasstypenumber, null);
                    } else {
                        periodoAcesso = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().incluirFreePass(clienteGympass, dataTimezone, unique_token, gympasstypenumber, null);
                    }
                    if (periodoAcesso != null && !UteisValidacao.emptyString(unique_token)) {
                        DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().gravarInfoCheckin(clienteGympass,
                                new EmpresaVO(empresa),
                                unique_token,
                                periodoAcesso,
                                IntegracoesEnum.GYMPASS,
                                produtoGymPass
                        );
                    }
                    DaoAuxiliar.retornarAcessoControle(key).getSituacaoClienteSinteticoDWDao().atualizarBaseOffLineZillyonAcesso(key, clienteGympass.getPessoa().getCodigo());
                    gravaLogGYMPASS(key, clienteGympass, unique_token, GYMPASS_INCLUSAO, retorno);
                }
            } else {
                validarMensagemErroAutorizarTokenGymPass(content);
            }
        } catch (Exception ex) {
            gravaLogGYMPASS(key,new ClienteVO(), unique_token, GYMPASS_INCLUSAO, retorno);
            Uteis.logar(ex, this.getClass());
            if(ex.getMessage().toUpperCase().contains("GYMPASS"))
                return ex.getMessage();
            return "ERRO:" + ex.getMessage();
        }
        return "GymPass lançado com sucesso!";
    }

    public Integer jaTemFreePass(Date dia, Integer cliente, String key) {
        try {
            String sql =
                    "select cli.freepass from periodoacessocliente p\n" +
                            "inner join cliente cli on cli.pessoa = p.pessoa  \n" +
                            "where tipoacesso = 'PL' \n" +
                            "and '" + Uteis.getDataJDBC(dia) + "' BETWEEN datainicioacesso::date AND datafinalacesso::date\n" +
                            "and cli.codigo = " + cliente;
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, DaoAuxiliar.retornarAcessoControle(key).getClienteDao().getCon());
            return rs.next() ? rs.getInt("freepass") : null;
        } catch (Exception e) {
            Uteis.logar(e, Cliente.class);
            return null;
        }
    }

    private String validarMensagemErroAutorizarTokenGymPass(JSONObject content) throws Exception {
        switch (content.getInt("code")) {
            case 404:
                throw new Exception("Não foi encontrado na Gympass o check-in do aluno. Por favor, peça ao aluno para realizar o check-in e tente novamente.");
            case 400:
                switch (content.getString("key")) {
                    case "checkin.validation.cancelled":
                        throw new Exception("O aluno cancelou o checkin e seu sistema tentou validar. Por favor, peça ao aluno para realizar o check-in e tente novamente.");
                    case "checkin.validation.expired":
                        throw new Exception("O check-in do aluno expirou. Por favor, peça ao aluno para realizar o check-in e tente novamente.");
                    case "checkin.already.validated":
                        throw new Exception("O check-in do aluno já foi validado.");
                    default:
                        throw new Exception("Erro não definido!");
                }
            case 403:
                throw new Exception("Problema na integração na Gympass, Por favor, entre em contato com a Gympass e solicite para verificar seus dados e integração!");
            default:
                throw new Exception(content.getString("key"));
        }
    }

    public String checkInGymPassValidarAcesso(String key,
                                 String json,
                                 final int empresa,
                                 boolean remover) {
        String unique_token = "";
        String retorno = "";
        try {
            JSONObject objJson = new JSONObject(json);
            unique_token = objJson.getJSONObject("event_data").getJSONObject("user").get("unique_token").toString();
            String gympasstypenumber = "1"; // Foi inutilizado, sempre setou 1, dado nunca fez sentido.
            String produtoGymPass = objJson.getJSONObject("event_data").getJSONObject("gym").getJSONObject("product").get("id") + ";" +
                    objJson.getJSONObject("event_data").getJSONObject("gym").getJSONObject("product").get("description");
            Date hoje = Calendario.hoje();
            Calendar cal = Calendar.getInstance();
            boolean isHorarioVerao = cal.getTimeZone().inDaylightTime(Calendario.hoje());
            Date dataTimezone = hoje;
            if (isHorarioVerao){
                dataTimezone = new Date(dataTimezone.getTime() - cal.getTimeZone().getDSTSavings());
            }
            boolean existeCliente = DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().existeTokenGymPass(unique_token);
            gravaLogGYMPASS(key, new ClienteVO(), unique_token, GYMPASS_JSON_WEBHOOK, json);
            if (!existeCliente){
                gravaLogGYMPASS(key, new ClienteVO(), unique_token, GYMPASS_NAO_EXISTE, "ERRO: Não existe aluno cadastrado com esse Token.");
                return "ERRO: Não existe aluno cadastrado com esse Token.";
            }
            ClienteVO clienteGympass = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarGymPass(unique_token , empresa ,  Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(remover) {
                removerFreePass(objJson, key, clienteGympass, unique_token);
                return removerPeriodoAcessoCheckinRevogado(key, objJson, unique_token);
            } else {
                liberarFreePass(key, hoje, dataTimezone, unique_token, clienteGympass, gympasstypenumber, empresa, produtoGymPass);
            }

        } catch (Exception ex) {
            gravaLogGYMPASS(key,new ClienteVO(), unique_token, GYMPASS_INCLUSAO, retorno);
            Uteis.logar(ex, this.getClass());
            if(ex.getMessage().toUpperCase().contains("GYMPASS"))
                return ex.getMessage();
            return "ERRO:" + ex.getMessage();
        }
        return "GymPass lançado com sucesso!";
    }

    private String removerPeriodoAcessoCheckinRevogado(String key, JSONObject objJson, String unique_token) {
        try {
            if (objJson.getJSONObject("event_data").has("timestamp")) {
                // as datas em timestamp retornadas pela gympass que vi até esta data estavam no formato de segundos;
                long timestamp = objJson.getJSONObject("event_data").getInt("timestamp");
                Date dataCheckin = new Date(timestamp * 1000L);
                dataCheckin = Calendario.getDataComHoraZerada(dataCheckin);
                DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().excluirSemCommit(
                        String.format(" tokengympass = '%s' and dataInicioAcesso = '%s' and dataFinalAcesso = '%s' ",
                                unique_token, Uteis.getDataFormatoBD(dataCheckin), Uteis.getDataFormatoBD(dataCheckin))
                );
                return "GymPass revogado com sucesso!";
            } else {
                return "Timestamp do checkin não localizado, o GymPass não foi revogado!";
            }
        } catch (Exception e) {
            return "ERRO AO REMOVER PERIODO ACESSO: " + e.getMessage();
        }
    }

    private void buscarOuIncluirAluno(String chave, Integer empresa, JSONObject user, String unique_token) throws Exception{
        try {
            String nome_cliente = user.getString("first_name");
            nome_cliente +=  " " + user.getString("last_name");
            String email_cliente = user.getString("email");
            String phone_number = user.getString("phone_number");

            Integer codigoCliente = consultarSeExisteAluno(chave, user);
            if(codigoCliente == null){
                //Chamar o método de gravar um novo cliente
                GympassBookingServlet bookingServlet = new GympassBookingServlet();
                bookingServlet.gerarAluno(chave, empresa, unique_token, nome_cliente, phone_number, email_cliente);
            } else {
                DaoAuxiliar.retornarAcessoControle(chave).getClienteDao().alterarGymPassUniqueToken(unique_token, codigoCliente);
            }
        }catch (Exception e){
            gravaLogGYMPASS(chave, new ClienteVO(), unique_token, GYMPASS_NAO_EXISTE, "ERRO: Não existe aluno cadastrado com esse Token.");
            throw new Exception("Não existe aluno cadastrado com esse Token.");
        }
    }

    private Integer consultarSeExisteAluno(String chave, JSONObject user) throws Exception{
        String nome_cliente = user.getString("first_name");
        nome_cliente +=  " " + user.getString("last_name");
        String email_cliente = user.getString("email");
        String phone_number = user.getString("phone_number");

        String query = "SELECT cliente.codigo, CONCAT(pessoa.telefoneemergencia, '" + phone_number + "') AS telefone_emergencia " +
                "FROM pessoa " +
                "INNER JOIN cliente ON pessoa.codigo = cliente.pessoa " +
                "WHERE pessoa.nome = '" + nome_cliente + "' AND pessoa.email = '" + email_cliente + "'";


        ResultSet rs = SuperFacadeJDBC.criarConsulta(query, DaoAuxiliar.retornarAcessoControle(chave).getClienteDao().getCon());

        return rs.next() ? rs.getInt("codigo") : null;
    }

    private void removerFreePass(JSONObject objJson, String key, ClienteVO clienteGympass, String unique_token) {
        try {
            String metadata = objJson.getJSONObject("metadata").toString();
            String errors = "";
            if(objJson.has("errors")) {
                errors = objJson.getJSONArray("errors").toString();
            }

            DaoAuxiliar.retornarAcessoControle(key).getClienteDao().removerFreePass(clienteGympass);
            gravaLogGYMPASS(key, clienteGympass, unique_token, ERRO_GYMPASS_ACESS_CONTROL_VALIDATE, metadata+" - "+errors);
        }  catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
        }
    }

    private void liberarFreePass(String key, Date hoje, Date dataTimezone,
                                 String unique_token,
                                 ClienteVO clienteGympass, String gympasstypenumber, Integer empresa, String produtoGymPass) {
        try {
            clienteGympass.setResponsavelFreePass(0);
            clienteGympass.setFreePass(DaoAuxiliar.retornarAcessoControle(key).getProdutoDao().criarOuConsultarProdutoGymPass());
            PeriodoAcessoClienteVO periodoAcessoClienteVO;

            boolean alunoPossuiCheckinGympassHoje = DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().existeCheckinGympassHojePorToken(unique_token);
            if (alunoPossuiCheckinGympassHoje) {
                // Se o aluno já possuir checkin no dia atual não precisa registrar um novo periodoacessocliente, apenas registrar o log
                gravaLogGYMPASS(key, clienteGympass, unique_token, GYMPASS_CHECKIN_DIARIO_EXISTENTE, "O aluno já possui um período de acesso lançado para o dia desta data");
                return;
            }

            if (Calendario.diferencaEmDias(dataTimezone,hoje) > 1 || Calendario.diferencaEmDias(dataTimezone,hoje) < -1){
                periodoAcessoClienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().incluirFreePass(clienteGympass, Calendario.hoje(), unique_token, gympasstypenumber, null);
            }else{
                periodoAcessoClienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().incluirFreePass(clienteGympass, dataTimezone, unique_token, gympasstypenumber, null);
            }
            DaoAuxiliar.retornarAcessoControle(key).getSituacaoClienteSinteticoDWDao().atualizarBaseOffLineZillyonAcesso(key, clienteGympass.getPessoa().getCodigo());
            gravaLogGYMPASS(key, clienteGympass, unique_token, GYMPASS_INCLUSAO, "");

            if(periodoAcessoClienteVO != null){
                DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().gravarInfoCheckin(clienteGympass,
                        new EmpresaVO(empresa),
                        unique_token,
                        periodoAcessoClienteVO,
                        IntegracoesEnum.GYMPASS,
                        produtoGymPass
                );
            }
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
        }
    }

    private void gravaLogGYMPASS(String key, ClienteVO cliente, String unique_token, String operacao, String retorno){
        try {
            LogVO obj = new LogVO();
            if (operacao.equals(GYMPASS_JSON_WEBHOOK)){
                obj.setChavePrimaria("JSON DO WEBHOOK" );
            }else{
                obj.setChavePrimaria(cliente.getPessoa() == null ? "CLIENTE NÃO ENCONTRADO" : cliente.getPessoa().getCodigo().toString());
            }
            obj.setNomeEntidade("GYMPASS");
            obj.setNomeEntidadeDescricao("Cliente - GymPass");
            obj.setOperacao(operacao);
            obj.setNomeCampo("GYMPASS-Cliente");
            obj.setValorCampoAlterado("TOKEN GYMPASS: " + unique_token + " RETORNO: " + retorno);
            obj.setDataAlteracao(Calendario.hoje());
            if (cliente.getPessoa() == null){
                obj.setPessoa(0);
            }else{
                obj.setPessoa(cliente.getPessoa().getCodigo());
            }
            DaoAuxiliar.retornarAcessoControle(key).getLogDao().incluirSemCommit(obj);
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }
}
