package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoDTO extends SuperJSON {

    private String chave;
    private String operacao;
    private IntegracaoEmpresaDTO empresa;
    private IntegracaoUsuarioDTO usuarioResponsavel;
    private IntegracaoClienteDTO cliente;
    private IntegracaoContratoDTO contrato;
    private IntegracaoVendaAvulsaDTO vendaAvulsa;
    private IntegracaoBvDTO integracaoBv;
    private IntegracaoPagamentoDTO pagamento;
    private IntegracaoAcessoDTO acesso;

    public IntegracaoDTO(String chave, String operacao,
                         EmpresaVO empresaVO, UsuarioVO usuarioResponsavelVO) {
        this.chave = chave;
        this.operacao = operacao;
        this.empresa = new IntegracaoEmpresaDTO(empresaVO);
        this.usuarioResponsavel = new IntegracaoUsuarioDTO(usuarioResponsavelVO);
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public IntegracaoEmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(IntegracaoEmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public IntegracaoClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(IntegracaoClienteDTO cliente) {
        this.cliente = cliente;
    }

    public IntegracaoPagamentoDTO getPagamento() {
        return pagamento;
    }

    public void setPagamento(IntegracaoPagamentoDTO pagamento) {
        this.pagamento = pagamento;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public IntegracaoContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(IntegracaoContratoDTO contrato) {
        this.contrato = contrato;
    }

    public IntegracaoUsuarioDTO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(IntegracaoUsuarioDTO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public IntegracaoVendaAvulsaDTO getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(IntegracaoVendaAvulsaDTO vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public IntegracaoBvDTO getIntegracaoBv() {
        return integracaoBv;
    }

    public void setIntegracaoBv(IntegracaoBvDTO integracaoBv) {
        this.integracaoBv = integracaoBv;
    }

    public IntegracaoAcessoDTO getAcesso() {
        return acesso;
    }

    public void setAcesso(IntegracaoAcessoDTO acesso) {
        this.acesso = acesso;
    }
}
