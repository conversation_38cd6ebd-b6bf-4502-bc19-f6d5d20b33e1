package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Athos Feitosa",
        data = "29/04/2025",
        descricao = "Criar tabelas para movimentao dos leads (funil, fasefunil, cardlead, logoperacaocardlead, tagfunillead)",
        motivacao = "GCM-361 - Backend Kanban"
)
public class AtualizacaoTicketGCM361 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE funil (" +
                            "codigo serial4 NOT NULL, " +
                            "empresa int4, " +
                            "nome varchar(100), " +
                            "descricao text, " +
                            "ativo bool DEFAULT TRUE, " +
                            "usuarioResponsavelCriacao int4, " +
                            "usuarioResponsavelLeads int4, " +
                            "tipoVinculoPadrao varchar(10), " +
                            "dataCriacao timestamp DEFAULT CURRENT_TIMESTAMP, " +
                            "dataAtualizacao timestamp, " +
                            "CONSTRAINT funil_pk PRIMARY KEY (codigo)" +
                            ");", c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE fasefunil (" +
                            "codigo serial4 NOT NULL, " +
                            "funil int4 NOT NULL, " +
                            "corHexadecimal varchar(10), " +
                            "nome varchar(100), " +
                            "posicaoOrdem int4, " +
                            "finalizaLead bool DEFAULT FALSE, " +
                            "tipoFase varchar(20), " +
                            "filtrojson text, " +
                            "CONSTRAINT fasefunil_pk PRIMARY KEY (codigo), " +
                            "CONSTRAINT fk_fasefunil_funil FOREIGN KEY (funil) REFERENCES funil(codigo)" +
                            ");", c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE cardlead (" +
                            "codigo serial4 NOT NULL, " +
                            "funil int4 NOT NULL, " +
                            "empresa int4, " +
                            "pessoa int4, " +
                            "responsavel int4, " +
                            "fasefunil int4 NOT NULL, " +
                            "dataInicioFaseAtual timestamp, " +
                            "dataEntrada timestamp, " +
                            "dataSaida timestamp, " +
                            "finalizado bool DEFAULT FALSE, " +
                            "origem varchar(200), " +
                            "CONSTRAINT cardlead_pk PRIMARY KEY (codigo), " +
                            "CONSTRAINT fk_cardlead_funil FOREIGN KEY (funil) REFERENCES funil(codigo), " +
                            "CONSTRAINT fk_cardlead_fasefunil FOREIGN KEY (fasefunil) REFERENCES fasefunil(codigo)" +
                            ");", c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE logoperacaocardlead (" +
                            "codigo serial4 NOT NULL, " +
                            "cardlead serial4 NOT NULL, " +
                            "empresa int4, " +
                            "pessoa int4 NOT NULL, " +
                            "funil int4 NOT NULL, " +
                            "faseanterior int4, " +
                            "faseatual int4, " +
                            "usuarioresponsavel int4 NOT NULL, " +
                            "descricao TEXT, " +
                            "msgcontato TEXT, " +
                            "tagsatuais TEXT, " +
                            "dataoperacao timestamp DEFAULT CURRENT_TIMESTAMP, " +
                            "operacao varchar(255) NOT NULL, " +
                            "CONSTRAINT logoperacaocardlead_pk PRIMARY KEY (codigo)" +
                            ");", c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE tagfunillead (" +
                            "codigo serial4 NOT NULL, " +
                            "nome varchar(100), " +
                            "cor varchar(10), " +
                            "CONSTRAINT tagsfunislead_pk PRIMARY KEY (codigo)" +
                            ");", c
            );
        }
    }
}
