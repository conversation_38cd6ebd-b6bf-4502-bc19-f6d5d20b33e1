package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Vitor Junio",
        data = "29/05/2025",
        descricao = "Adicionar cardId e funilId ao historico de contato, e criar a tabela cardleadtags",
        motivacao = "GCM-6")
public class AtualizacaoTicketGCM430 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE historicoContato ADD COLUMN cardCodigo INTEGER;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE historicoContato ADD COLUMN funilCodigo INTEGER;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE cardleadtags (" +
                            "cardlead INTEGER NOT NULL CONSTRAINT fk_cardleadtags_cardlead " +
                            "REFERENCES cardlead ON DELETE CASCADE, " +
                            "tagfunillead INTEGER NOT NULL CONSTRAINT fk_cardleadtags_tagfunillead " +
                            "REFERENCES tagfunillead ON DELETE CASCADE, " +
                            "codigo SERIAL PRIMARY KEY, " +
                            "CONSTRAINT card_lead_tags_card_lead_tag_funil_lead_key UNIQUE (cardlead, tagfunillead)" +
                            ");", c);
        }
    }
}
