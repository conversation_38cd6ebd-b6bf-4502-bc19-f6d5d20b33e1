package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "04/08/2025",
        descricao = "Alterar idstone para no momento da criação gerar emails fake",
        motivacao = "PAY-1299")
public class PAY1299AjustarIdCustomerStonePessoa implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update pessoa set idstone = null \n" +
                    "where codigo in (\n" +
                    "    select p.codigo \n" +
                    "    from email e \n" +
                    "    inner join pessoa p on p.codigo = e.pessoa\n" +
                    "    where (\n" +
                    "        lower(e.email) like '%naoinformado%' \n" +
                    "        or lower(e.email) like '%naotem%'\n" +
                    "    )\n" +
                    "    and p.idstone is not null\n" +
                    ")", c);
        }
    }
}


