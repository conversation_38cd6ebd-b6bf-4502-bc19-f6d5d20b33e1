package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Athos Feitosa",
        data = "01/06/2025",
        descricao = "Cria tabela de log para automaes de insero de cards no funil de leads",
        motivacao = "Registrar execues de automaes que inserem cards no CRM"
)
public class CriarTabelaLogAutomacaoLead implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE logautomacaocardlead (" +
                            "codigo SERIAL PRIMARY KEY, " +
                            "evento VARCHAR(64), " +
                            "funil INTEGER, " +
                            "fase INTEGER, " +
                            "descricao TEXT, " +
                            "resultado TEXT, " +
                            "data TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                            ");",
                    c
            );

        }
    }
}
