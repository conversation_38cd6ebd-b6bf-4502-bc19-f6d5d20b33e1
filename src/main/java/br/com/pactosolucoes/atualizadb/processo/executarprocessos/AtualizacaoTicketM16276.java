package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import java.sql.Connection;

@ClasseProcesso(
        autor = "Paulo Jesus",
        data = "25/07/2025",
        descricao = "Aumentar o tamanho do campo 'descricao' na tabela 'produto' para varchar(100)",
        motivacao = "Resolver erro de limite de caracteres na importao XML de produtos - Ticket M1-6276"
)
public class AtualizacaoTicketM16276 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE produto ALTER COLUMN descricao TYPE VARCHAR(100);",
                    c
            );
        }
    }
}
