package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.controle.sad.SituacaoClienteSinteticoDWControle;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DeletarEmpresas
{
    public static void main(String... args) {
        try {
            if (args.length == 0){
                args = new String[]{
                        "teste"// chave
                        ,"1,2"//Codigos das empresas que ficaram(não serão apagadas), separados por virgula ex: "1,2"
                        , "3,4"//Codigos das empresas que serão apagadas, separados por virgula ex: "4,3,5"
                         };
            }


            List<Integer> empresasQueFicaram = args.length > 1 ?
                    Arrays.stream(args[1].split(",")).map(Integer::parseInt).collect(Collectors.toList())
                    : Stream.of(1).collect(Collectors.toList());
            List<Integer> empresasSeraoApagadas = args.length > 1 ?
                    Arrays.stream(args[2].split(",")).map(Integer::parseInt).collect(Collectors.toList())
                    : Stream.of(2,3,4,5,6,7,8,9,10).collect(Collectors.toList());

            System.out.println(empresasSeraoApagadas);
            Connection c = new DAO().obterConexaoEspecifica(args[0]);
//            Connection c = DriverManager.getConnection("**************************************************", "zillyonweb", "pactodb");
            Conexao.guardarConexaoForJ2SE(c);



            apagarEmpresas(empresasQueFicaram, empresasSeraoApagadas, c);
        } catch (Exception ex) {
            Logger.getLogger(DeletarEmpresas.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void apagarEmpresas(List<Integer> listaEmpresasQueFicam, List<Integer> listaEmpresasApagar, Connection con) throws Exception {
        String inEmpresasApagar = "";
        for (Integer empresa : listaEmpresasApagar) {
            inEmpresasApagar += ","+ empresa;
        }
        inEmpresasApagar = inEmpresasApagar.replaceFirst(",","");
        String pessoasExcluir   = "select p.codigo from pessoa p left join cliente cl on cl.pessoa = p.codigo left join colaborador co on co.pessoa = p.codigo left join fornecedor f on f.pessoa = p.codigo left join movconta m on m.pessoa = p.codigo left join empresa emp on emp.pessoafinan = p.codigo left join cliente cr on cr.pessoaresponsavel = p.codigo where m.codigo is null and cl.codigo is null and co.codigo is null and f.codigo is null and cr.codigo is null and emp.codigo is null";
        String inEmpresasQueFicam = "";
        for (Integer empresa : listaEmpresasQueFicam) {
            inEmpresasQueFicam += ","+ empresa;
        }
        inEmpresasQueFicam = inEmpresasQueFicam.replaceFirst(",","");
        try {
            con.setAutoCommit(false);
            System.out.println("Excluindo Dados");
            for (Integer empresaApagar : listaEmpresasApagar  ) {
                for (Integer empresaQueFicara : listaEmpresasQueFicam  ) {
                    String sqlColaboradores = " select 'update usuario set colaborador = '||empresa2||' where colaborador = '||empresa1||';' from (\n" +
                            "select pessoa, count(codigo),(select codigo from colaborador where pessoa = c.pessoa and empresa = " + empresaQueFicara + " limit 1) as empresa2 ,(select codigo from colaborador where pessoa = c.pessoa and empresa in (" + empresaApagar + ") limit 1) as empresa1  from colaborador c where empresa in (" + empresaApagar + "," + empresaQueFicara + ") group by 1 having count(codigo) > 1) as foo";
                    ResultSet rsColaboradores = SuperFacadeJDBC.criarConsulta(sqlColaboradores, con);
                    while (rsColaboradores.next()) {
                        if (rsColaboradores.getString(1) != null) {
                            executeSql(rsColaboradores.getString(1), con);
                        }
                    }
                }
            }
            for (Integer empresaQueFicara : listaEmpresasQueFicam  ) {
                executeSql("UPDATE cliente cli SET empresa = " + empresaQueFicara+ " WHERE  cli.empresa  IN ("+inEmpresasApagar+") and (exists(select codigo from contrato con where con.pessoa = cli.pessoa and con.empresa = "+empresaQueFicara+") or exists (select codigo from vendaavulsa v where v.pessoa = cli.pessoa and v.empresa = "+empresaQueFicara+"));", con);
            }
            executeSql("update situacaoclientesinteticodw s set empresacliente = (select empresa from cliente where codigo = s.codigocliente)", con);
            executeSql("delete from textopadrao  where empresa in ("+inEmpresasApagar+")", con);
            executeSql("update colaborador  set empresa = "+listaEmpresasQueFicam.get(0)+" where codigo in (select colaborador from usuario u where u.username ilike 'pactobr' or username ilike 'master');", con);
            executeSql("update bdatualizacao  set usuario = 1;", con);
            executeSql("DELETE FROM configuracaoprodutoempresa  where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from risco where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from acessocliente where localacesso  in (select codigo from localacesso where empresa in ("+inEmpresasApagar+")) or coletor in (select codigo from coletor where localacesso in (select codigo from localacesso where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("update colaborador set uacodigo = null where uacodigo in (select codigo from acessocolaborador where localacesso  in (select codigo from localacesso where empresa in ("+inEmpresasApagar+")))", con);
            executeSql("delete from acessocolaborador  where localacesso  in (select codigo from localacesso where empresa in ("+inEmpresasApagar+"))", con);
            executeSql("delete from liberacaoacesso where coletor in (select codigo from coletor where localacesso in (select codigo from localacesso where empresa in ("+inEmpresasApagar+"))) or localacesso  in (select codigo from localacesso where empresa in ("+inEmpresasApagar+"))", con);
            executeSql("delete from validacaolocalacesso  where coletor in (select codigo from coletor where localacesso in (select codigo from localacesso where empresa in ("+inEmpresasApagar+")))", con);
            executeSql("update empresa  set coletorchamada = null where coletorchamada in (select codigo from coletor where localacesso in (select codigo from localacesso where empresa in ("+inEmpresasApagar+"))); ", con);
            executeSql("update empresa  set localacessochamada  = null where localacessochamada in  (select codigo from localacesso where empresa in ("+inEmpresasApagar+")); ", con);
            executeSql("update configuracaosistema set coletorchamada = null where coletorchamada  in (select codigo from coletor where localacesso in (select codigo from localacesso where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("update configuracaosistema set localacessochamada = null where localacessochamada   in (select codigo from localacesso where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("update  ambiente set coletor = null where coletor in (select codigo from coletor c where c.localacesso in (select codigo from localacesso l where l.empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from coletor where localacesso  in (select codigo from localacesso where empresa in ("+inEmpresasApagar+"))", con);
            executeSql("delete from localacesso where empresa in ("+inEmpresasApagar+")", con);
            executeSql("delete from aberturameta where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from fecharmetadetalhado f where historicocontato in (select codigo from historicocontato h where cliente  in (select codigo from cliente where empresa in("+inEmpresasApagar+")) or  passivo  in (select codigo from passivo where empresa in ("+inEmpresasApagar+") or cliente in (select codigo from cliente where empresa in ("+inEmpresasApagar+"))) or indicado in (select codigo from indicado where cliente in (select codigo from cliente  i where empresa in ("+inEmpresasApagar+")) or indicacao in (select codigo from indicacao  where  clientequeindicou  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")) or colaboradorqueindicou in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"))) or empresa in ("+inEmpresasApagar+")) or agenda  in (select codigo from agenda where empresa in ("+inEmpresasApagar+")) or codigo in (select codigo from historicocontato where maladireta  in (select codigo from maladireta  where empresa in ("+inEmpresasApagar+"))));", con);
            executeSql("delete from historicocontato where cliente  in (select codigo from cliente where empresa in("+inEmpresasApagar+")) or   passivo  in (select codigo from passivo where empresa in ("+inEmpresasApagar+") or cliente in (select codigo from cliente where empresa in ("+inEmpresasApagar+"))) or indicado in (select codigo from indicado where cliente in (select codigo from cliente  i where empresa in ("+inEmpresasApagar+")) or empresa in ("+inEmpresasApagar+") ) or agenda  in (select codigo from agenda where  empresa in ("+inEmpresasApagar+") or passivo in (select codigo FROM passivo      where empresa in  ("+inEmpresasApagar+")) or indicado in (select codigo from indicado where indicacao in(select codigo from indicacao  where  clientequeindicou  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")) or colaboradorqueindicou in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+")) ) ));", con);
            executeSql("delete from agenda   where empresa in ("+inEmpresasApagar+") or passivo in (select codigo FROM passivo      where empresa in  ("+inEmpresasApagar+")) or indicado in (select codigo from indicado where indicacao in(select codigo from indicacao  where  clientequeindicou  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")) or colaboradorqueindicou in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+")) ) )  ;", con);
            executeSql("delete FROM configuracaoempresardstation    where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete FROM aulaavulsadiaria      where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from caixamovconta where caixa  in (select codigo from caixa c where c.empresa in ("+inEmpresasApagar+")) or movconta  in (select codigo from movconta where empresa in  ("+inEmpresasApagar+") or movproduto in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+") or contrato in (SELECT codigo FROM contrato WHERE empresa IN ("+inEmpresasApagar+"))) or lote in (select codigo from lote where empresa in ("+inEmpresasApagar+") or pagamovconta in (select codigo from movconta where empresa in  ("+inEmpresasApagar+") or movproduto in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+"))))); ", con);
            executeSql(" delete from caixaconta  where caixa  in (select codigo from caixa c where c.empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from caixa where empresa in ("+inEmpresasApagar+")", con);
            executeSql("delete from transferenciastone where movconta in (select codigo from movconta where empresa in  ("+inEmpresasApagar+") or movproduto in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+") or contrato in (SELECT codigo FROM contrato WHERE empresa IN ("+inEmpresasApagar+"))))", con);
            executeSql("delete from pagamentostone where movconta in (select codigo from movconta where empresa in  ("+inEmpresasApagar+") or movproduto in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+") or contrato in (SELECT codigo FROM contrato WHERE empresa IN ("+inEmpresasApagar+"))))", con);
            executeSql("delete from movcontacontabil where movconta in (select codigo from movconta where empresa in  ("+inEmpresasApagar+") or movproduto in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+") or contrato in (SELECT codigo FROM contrato WHERE empresa IN ("+inEmpresasApagar+"))))", con);
            executeSql("update movconta set lotepagouconta = null where lotepagouconta in (select codigo from lote where empresa in ("+inEmpresasApagar+"))", con);
            executeSql("delete from nfseemitida where movconta in (select codigo from movconta where empresa in  ("+inEmpresasApagar+") or movproduto in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+")))", con);
            executeSql("delete from lote where empresa in ("+inEmpresasApagar+") or pagamovconta in (select codigo from movconta where empresa in  ("+inEmpresasApagar+") or movproduto in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+")))", con);
            executeSql("delete FROM movconta      where empresa in  ("+inEmpresasApagar+") or movproduto in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+") or contrato in (SELECT codigo FROM contrato WHERE empresa IN ("+inEmpresasApagar+")));", con);

            executeSql("delete from historicoaluguelarmario where aluguelarmario in (SELECT codigo from aluguelarmario  where movproduto  in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+")) or cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete from aluguelarmario  where movproduto  in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+")) or cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from remessaitemmovparcela  where remessaitem in (select codigo from remessaitem  where remessa in (select codigo from remessa where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from remessaitem  where remessa in (select codigo from remessa where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from  remessa where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from transacaomovparcela where transacao in (select codigo from transacao where empresa in ("+inEmpresasApagar+") or recibopagamento in (select codigo FROM recibopagamento      where empresa in  ("+inEmpresasApagar+")))", con);
            executeSql("delete from transacao where empresa in ("+inEmpresasApagar+") or recibopagamento in (select codigo FROM recibopagamento      where empresa in  ("+inEmpresasApagar+"))", con);
            executeSql("delete FROM movparcelacupomdesconto where movparcela in (SELECT codigo FROM movparcela where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM movparcela        where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from nfseemitida where movproduto in (select codigo FROM movproduto      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from nfseemitida where cartaocredito  in (select codigo from cartaocredito where movpagamento in (select codigo from movpagamento where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from nfseemitida where cheque in (select codigo from cheque where movpagamento in (select codigo from movpagamento where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from nfseemitida where movpagamento in (select codigo from movpagamento where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("DELETE FROM nfseemitida WHERE contrato IN (SELECT codigo FROM contrato WHERE empresa IN ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM movproduto        where empresa in  ("+inEmpresasApagar+") OR lancamentocoletivo in (select codigo from lancamentoprodutocoletivo where empresa in ("+inEmpresasApagar+") );", con);
            executeSql("delete from movproduto where vendaavulsa in (select codigo from vendaavulsa where empresa in ("+inEmpresasApagar+") or colaborador in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+")) )", con);
            executeSql("delete FROM public.lancamentoprodutocoletivo  where  empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from sch_estudio.agenda_agendar where id_vendaavulsa in (select codigo from vendaavulsa where empresa in ("+inEmpresasApagar+"))", con);
            executeSql("delete FROM vendaavulsa      where empresa in  ("+inEmpresasApagar+") or colaborador in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM periodoacessocliente       where contrato  in  (select codigo FROM contrato      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM fecharmetadetalhado  where contrato  in  (select codigo FROM contrato      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM fecharmetadetalhado  where cliente  in  (select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM fecharmetadetalhado  where indicado   in  (select codigo from indicado where cliente in (select codigo from cliente  i where empresa in ("+inEmpresasApagar+")) or indicacao in (select codigo from indicacao  where  clientequeindicou  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")) or colaboradorqueindicou in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"))));", con);

            executeSql("DELETE FROM contratoassinaturadigital WHERE contrato IN (SELECT codigo FROM contrato WHERE empresa IN ("+inEmpresasApagar+"));", con);
            executeSql("delete from contratoduracaocreditotreino   where contratoduracao  in (select codigo from contratoduracao  where contrato in (select codigo from contrato where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from controlecreditotreino  where horarioturmafalta in (select codigo from horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"))) or contratoorigem in (select codigo from contrato where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("DELETE FROM reajustecontrato WHERE contrato IN (SELECT codigo FROM contrato WHERE empresa IN ("+inEmpresasApagar+"));", con);
            executeSql("update periodoacessocliente set contratobaseadorenovacao = null where contratobaseadorenovacao  in (select codigo from contrato where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from pixmovparcela where pix in (select codigo from pix where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from pix where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete FROM boleto where  recibopagamento  in (select codigo from recibopagamento    where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM boleto where  empresa  in  ("+inEmpresasApagar+");", con);
            executeSql("delete FROM contrato      where empresa in  ("+inEmpresasApagar+");", con);

            executeSql("update contratooperacao  set clienterecebedias = null where  clienterecebedias  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("update contratooperacao set clientetransferedias = null   where  clientetransferedias  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("update cliente set indicadopor = null where indicadopor in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from nfseemitida where recibopagamento in (select codigo FROM recibopagamento      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from reciboclienteconsultor  where cliente in  ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM recibopagamento      where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete FROM risco   where cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from optin where cliente in  ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from agenda where alunohorarioturma in (select codigo from alunohorarioturma where horarioturma in (select codigo  from horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"))) or cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete from alunohorarioturma where horarioturma in (select codigo  from horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"))) or cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from aulaconfirmada where horario in (select codigo  from horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"))) or cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM  autorizacaoacessocliente  where cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM  carteiraprovisoriacliente  where cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM  clienteacompanhado  where clienteacompanhado in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM  clienteacompanhante  where cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM  clienteagendamentotaxa  where cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM  clientecampeonato  where cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM  clientecargotitulacaohistorico  where cliente in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM  clientecategoriahistorico where clientetitular in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);

            executeSql("delete FROM clientecategoriahistorico  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM clientesituacaoclubehistorico  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM clientesocio  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM clientetitulardependente  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM clientetoken  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM convite  where  convidou  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM convite  where  convidado  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM conviteaulaexperimental  where  clienteconvidou  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM conviteaulaexperimental  where  clienteindicadooupassivo  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM conviteaulaexperimental  where  clienteconvidado  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM convitecliente  where  clienteconvidado  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM convitecliente  where  clienteconvidante  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM demandahorarioturma  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM contratodependente  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM contratodependente  where  titular  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM examemedicocliente  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM filaesperaturma  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM historicopontos  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM conversaolead  where  lead  in ( select codigo FROM lead  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")) or passivo in ( select codigo FROM passivo  where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete FROM lead  where  cliente  in ( select codigo FROM cliente   where empresa in  ("+inEmpresasApagar+")) or passivo in ( select codigo FROM passivo  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM maladiretacrmextracliente  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM malingenviados  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM orcamento  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM reciboclienteconsultor  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM riscoevasao  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM smsenviados  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM sorteio  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM sch_estudio.agenda  where  id_cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM sch_estudio.agenda_agendar  where  id_cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM sch_estudio.agenda_faturar  where  id_cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM agenda  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM aulaavulsadiaria  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM fecharmetadetalhado  where indicado in ( select codigo FROM indicado where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")) or  indicacao in (select codigo from indicacao  where  clientequeindicou  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"))));", con);
            executeSql("delete FROM indicacao  where  clientequeindicou  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")) or colaboradorqueindicou in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM indicado  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM itemtaxapersonal  where  aluno  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM maladiretaenviada  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM agenda  where  passivo  in ( select codigo from passivo  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete FROM passivo  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM clientemensagem  where  questionariocliente in ( select codigo FROM questionariocliente  where cliente in   ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete FROM situacaocontratoanaliticodw  where  cliente  in ( select codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);

            executeSql("delete FROM nowlocationipvendaonline where cliente in (SELECT codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM infocheckin where cliente in (SELECT codigo FROM cliente      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM cliente      where empresa in  ("+inEmpresasApagar+");", con);


            executeSql("delete from situacaoclientesinteticodw  where codigo in (select s.codigo from situacaoclientesinteticodw  s left join cliente c on c.codigo = s.codigocliente where c.codigo is null );", con);
            executeSql("update empresa set pessoafinan = null where codigo in ("+inEmpresasApagar+");", con);
            executeSql("delete from acessocolaborador where liberacaoacesso  in (select codigo FROM liberacaoacesso      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from acessocliente where liberacaoacesso  in (select codigo FROM liberacaoacesso      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM liberacaoacesso      where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from configuracaosistemausuario  where usuario in (select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete from usuarioemail    where usuario in (select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("update historicovinculo  SET usuarioresponsavel  = 1  where usuarioresponsavel in(select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("update acessocliente set usuario  = 1 where usuario  in (select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("update clientemensagem      SET usuario   = 1  where usuario in(select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete from itemtaxapersonal  where controle in (select codigo from controletaxapersonal     where  empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from controletaxapersonal     where  empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from mailingagendamento  where maladireta in (select codigo from maladireta  where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from mailingfiltros    where maladireta in (select codigo from maladireta  where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from mailinghistorico     where maladireta in (select codigo from maladireta  where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from historicocontato where maladireta  in (select codigo from maladireta  where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from configeventomailing where maladireta  in (select codigo from maladireta  where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM maladiretacrmextracliente  where  maladireta in ( select codigo FROM maladireta  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM maladiretacrmextracolaborador  where  maladireta in ( select codigo FROM maladireta  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from maladireta   where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from usuarioperfilacesso where empresa  in ("+inEmpresasApagar+");", con);
            executeSql("delete from usuariodicasesconder   where usuario  in(select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete from notificacaousuario where usuario in(select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete  from favorito f where usuario  in (select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete  from usuariotelefone f where usuario  in (select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete from atualizacaocadastral where usuario in (select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete from grupocolaborador where gerente in  (select codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("Delete from socialmailgrupoparticipante where participanteusuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("Delete from socialmailpartener where usuariodestino IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("Delete from socialmail where usuarioorigem IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("Delete from socialmailgrupo where usuariodono IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("Delete from atualizacaocadastral where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update conversa set atendente = 1 where atendente IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update colaboradordocumentorh set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update aberturameta set responsavelcadastro = 1 where responsavelcadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update aberturameta set responsavelliberacaotrocacolaboradorresponsavel = 1 where responsavelliberacaotrocacolaboradorresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update aberturameta set colaboradorresponsavel = 1 where colaboradorresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update acessocliente set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update acessocliente set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update acessocliente set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update acessocliente set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update agenda set colaboradorresponsavel = 1 where colaboradorresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update agenda set responsavelcadastro = 1 where responsavelcadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update agenda set responsavelcomparecimento = 1 where responsavelcomparecimento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update agendavisita set usuariocadastro = 1 where usuariocadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update aluguelarmario set responsavelcadastro = 1 where responsavelcadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update armario set responsavelcadastro = 1 where responsavelcadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update aulaavulsadiaria set responsavel = 1 where responsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update auladesmarcada set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update autorizacaoacessocliente set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update autorizacaoacessogrupoempresarial set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update balanco set usuariocancelamento = 1 where usuariocancelamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update balanco set usuariocadastro = 1 where usuariocadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update bdatualizacao set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update bloqueiocaixa set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update boleto set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update caixa set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update caixa set responsavelfechamento = 1 where responsavelfechamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update caixapagamento set usuariofechamento = 1 where usuariofechamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update caixapagamento set usuarioabertura = 1 where usuarioabertura IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update caixapagamentoitem set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update carteiraprovisoriacliente set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update checklistevento set usuarioresponsavelabertura = 1 where usuarioresponsavelabertura IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update checklistevento set usuarioresponsavelencerramento = 1 where usuarioresponsavelencerramento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update checklistevento set usuariocadastro = 1 where usuariocadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update cliente set responsavelfreepass = 1 where responsavelfreepass IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update clienteacompanhado set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update clienteacompanhante set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update clientecampeonato set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update clientecargotitulacaohistorico set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update clientecategoriahistorico set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update clientemensagem set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update clienteobservacao set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update clientesituacaoclubehistorico set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update clientesmarcados set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update compra set usuariocancelamento = 1 where usuariocancelamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update compra set usuariocadastro = 1 where usuariocadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update configuracaoempresardstation set responsavelpadrao = 1 where responsavelpadrao IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update configuracaomovimentacaoautomatica set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update contrato set responsaveldatabase = 1 where responsaveldatabase IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update contrato set responsavelcontrato = 1 where responsavelcontrato IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update contratoassinaturadigital set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update contratooperacao set responsavel = 1 where responsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update contratooperacao set responsavelliberacao = 1 where responsavelliberacao IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update contratopadraoce set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update controletaxapersonal set responsavel = 1 where responsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update conveniodesconto set responsavelautorizacao = 1 where responsavelautorizacao IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update conversaolead set responsavel = 1 where responsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update conviteaulaexperimental set usuarioconvidou = 1 where usuarioconvidou IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update convitecliente set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update cupomfiscal set responsavel = 1 where responsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update definirlayout set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update devolucaocreditoevento set responsavel = 1 where responsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update examemedicocliente set usuariomedico = 1 where usuariomedico IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update fotopessoa set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update historicoaluguelarmario set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update historicocontato set responsavelcadastro = 1 where responsavelcadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update historicocontrato set responsavelregistro = 1 where responsavelregistro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update historicovinculo set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update indicacao set responsavelcadastro = 1 where responsavelcadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update indicacao set colaboradorresponsavel = 1 where colaboradorresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update interessado set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update itemvendaavulsa set responsaveldesconto = 1 where responsaveldesconto IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update lancamentoprodutocoletivo set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update liberacaoacesso set usuariojustificou = 1 where usuariojustificou IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update liberacaoacesso set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update lote set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update maladireta set remetente = 1 where remetente IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update maladiretacrmextracolaborador set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update modeloorcamento set responsaveldefinicao = 1 where responsaveldefinicao IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update movconta set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update movimentocontacorrentecliente set responsavelautorizacao = 1 where responsavelautorizacao IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update movpagamento set responsavelpagamento = 1 where responsavelpagamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update movparcela set responsavel = 1 where responsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update movproduto set responsavellancamento = 1 where responsavellancamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update negociacaoevento set usuariocadastro = 1 where usuariocadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update negociacaoeventocontrato set responsavelcontrato = 1 where responsavelcontrato IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update negociacaoeventoencerramento set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update notafiscalconsumidoreletronica set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update passivo set colaboradorresponsavel = 1 where colaboradorresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update passivo set responsavelcadastro = 1 where responsavelcadastro IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update pessoaanexo set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update pix set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update planopersonalassinaturadigital set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update planotextopadrao set responsaveldefinicao = 1 where responsaveldefinicao IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update produtoestoque_alteracaosit set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update quarentena set usuarioiniciou = 1 where usuarioiniciou IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update reajustecontrato set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update recibodevolucao set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update recibopagamento set responsavellancamento = 1 where responsavellancamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update remessa set usuariofechamento = 1 where usuariofechamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update reposicao set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update retornoremessa set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update simulacaosaldo set usuariolancamento = 1 where usuariolancamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update sorteio set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update trancamentocontrato set responsaveloperacao = 1 where responsaveloperacao IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update transacao set usuarioresponsavel = 1 where usuarioresponsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update validacaolocalacesso set usuario = 1 where usuario IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update vendaavulsa set responsavel = 1 where responsavel IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update sch_estudio.agenda set id_usuario_lancamento = 1 where id_usuario_lancamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);
            executeSql("update sch_estudio.historico_status_agenda set id_usuario_lancamento = 1 where id_usuario_lancamento IN (SELECT codigo from usuario WHERE colaborador  in ( select codigo FROM colaborador where empresa in  ("+inEmpresasApagar+")));",con);

            executeSql("delete from usuario WHERE colaborador  in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from matriculaalunohorarioturma    where empresa in  ("+inEmpresasApagar+") or horarioturma in ( select codigo from horarioturma where turma in (select codigo from turma where empresa in ("+inEmpresasApagar+")));", con);
            for (Integer empresaQueFicara : listaEmpresasQueFicam  ) {
                executeSql("UPDATE horarioturma h SET professor = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr')  where exists (select codigo from matriculaalunohorarioturma m where m.horarioturma = h.codigo and contrato in (select codigo from contrato where empresa = "+empresaQueFicara+") and professor in (select codigo from colaborador where empresa in ("+inEmpresasApagar+"))) ",con);
                executeSql("update vinculo   set colaborador    = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr') where colaborador  in(select codigo FROM colaborador      where empresa in  (" + inEmpresasApagar + "));", con);
                executeSql("update reciboclienteconsultor     set consultor     = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr') where consultor  in(select codigo FROM colaborador      where empresa in  (" + inEmpresasApagar + "));", con);
                executeSql("update historicovinculo   set colaborador    = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr') where colaborador  in(select codigo FROM colaborador      where empresa in  (" + inEmpresasApagar + "));", con);
                executeSql("update questionariocliente     set consultor      = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr') where consultor  in (select codigo FROM colaborador      where empresa in  (" + inEmpresasApagar + "));", con);
                executeSql("update contrato       set consultor      = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr') where consultor  in (select codigo FROM colaborador      where empresa in  (" + inEmpresasApagar + "));", con);
                executeSql("update risco       set colaborador        = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr') where colaborador  in (select codigo FROM colaborador      where empresa in  (" + inEmpresasApagar + "));", con);

                executeSql("update aulaconfirmada set colaborador        = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr')  where  colaborador in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
                executeSql("update orcamento set consultor        = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr')   where  consultor in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
                executeSql("update conviteaulaexperimental set colaboradorresponsavelconvite        = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr')   where  colaboradorresponsavelconvite in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
                executeSql("update sch_estudio.agenda set id_colaborador        = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr')   where  id_colaborador in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
                executeSql("update historicocontrato set responsavelliberacaomudancahistorico   = (select coalesce(ce.codigo, u.colaborador) as colaborador  from usuario u left join colaborador cu on cu.codigo = u.colaborador left join colaborador ce on ce.pessoa = cu.pessoa and ce.empresa = "+empresaQueFicara+" where u.username ilike 'pactobr')   where  responsavelliberacaomudancahistorico in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);

            }
            executeSql("delete from auladesmarcada where horarioturma in (select codigo from  horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")) or turma in (select codigo from turma where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from fecharmetadetalhado f where historicocontato in (select codigo from historicocontato where agenda in (select codigo from agenda where reposicao  in (select codigo from reposicao r where turmadestino IN (SELECT codigo FROM turma WHERE empresa IN("+inEmpresasApagar+")) or turmaorigem IN (SELECT codigo FROM turma WHERE empresa IN("+inEmpresasApagar+")))))", con);
            executeSql("delete from  historicocontato where agenda in (select codigo from agenda where reposicao  in (select codigo from reposicao r where turmadestino IN (SELECT codigo FROM turma WHERE empresa IN("+inEmpresasApagar+")) or turmaorigem IN (SELECT codigo FROM turma WHERE empresa IN("+inEmpresasApagar+"))))", con);
            executeSql("DELETE FROM reposicao WHERE turmadestino IN (SELECT codigo FROM turma WHERE empresa IN("+inEmpresasApagar+")) or turmaorigem IN (SELECT codigo FROM turma WHERE empresa IN("+inEmpresasApagar+"));", con);
            executeSql("delete from reposicao where horarioturmaorigem in (select codigo from  horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")) or turma in (select codigo from turma where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from reposicao where horarioturma in (select codigo from  horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")) or turma in (select codigo from turma where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from contratomodalidadehorarioturma where horarioturma in (select codigo from  horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")) or turma in (select codigo from turma where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from  filaesperaturma where horarioturma in (select codigo from horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")) or turma in (select codigo from turma where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from  demandahorarioturma where horarioturma in (select codigo from horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")) or turma in (select codigo from turma where empresa in ("+inEmpresasApagar+")));", con);
            executeSql("delete from horarioturma where professor in ( select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+")) or turma in (select codigo from turma where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("update empresa set  consultorsite = null  where  consultorsite in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("update empresa  set  consultorvendaavulsa = null  where  consultorvendaavulsa in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("DELETE from reciboclienteconsultor  where consultor  in (select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("DELETE from metafinanceiraconsultor where colaborador  in (select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("DELETE from grupocolaboradorparticipante    where colaboradorparticipante    in (select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from historicocontato where passivo  in (select codigo from passivo where empresa in ("+inEmpresasApagar+")) or indicado  in (select codigo from indicado i where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete  from indicado i where empresa in ("+inEmpresasApagar+");;", con);
            executeSql("delete from indicacao i where not exists (select codigo from  indicado where indicacao = i.codigo );", con);
            executeSql("delete FROM passivo      where empresa in  ("+inEmpresasApagar+");;", con);
            executeSql("update empresa set consultorvendaavulsa = 1 where consultorvendaavulsa  in (select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("update empresa set consultorsite = 1 where consultorsite  in (select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from colaboradormodalidade where colaborador  in (select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from colaboradordocumentorh where colaborador  in (select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from colaboradorindisponivelcrm where colaboradorindisponivel  in (select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM colaboradorindisponivelcrm  where  colaboradorsuplente in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from colaboradorinforh where colaborador in (select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM metafinanceiraconsultor  where  colaborador in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM carteirinhacliente  where  colaborador in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM sch_estudio.colaborador_agenda_indisp  where  id_colaborador in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM sch_estudio.configuracao_preferencia  where  id_colaborador in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM sch_estudio.produto_colaborador  where  id_colaborador in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM planopersonalassinaturadigital  where  taxapersonal in ( select codigo from controletaxapersonal  where  personal in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("delete FROM controletaxapersonal  where  personal in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM historicoprofessorturma  where  professor in ( select codigo FROM colaborador  where empresa in  ("+inEmpresasApagar+"));", con);

            executeSql("update acessocolaborador set colaborador = null where colaborador in (select codigo FROM colaborador      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM acessocolaborador where colaborador = null", con);
            executeSql("delete FROM colaborador      where empresa in  ("+inEmpresasApagar+");", con);

            dropTriggresEstoque(con);
            executeSql("DELETE FROM compraitens  where compra  in (select codigo from compra where empresa in ("+inEmpresasApagar+") or fornecedor  in  ( select codigo FROM fornecedor      where empresa in  ("+inEmpresasApagar+")));", con);
            executeSql("DELETE FROM balancoitens   where balanco  in (select codigo from balanco   where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from  balanco  where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from compra  where empresa in ("+inEmpresasApagar+") or fornecedor  in  ( select codigo FROM fornecedor      where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from historicoprodutoestoque  where produtoestoque in (select codigo from produtoestoque     where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from produtoestoque_alteracaosit  where produtoestoque in (select codigo from produtoestoque     where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("DELETE FROM   exclusaomovprodutoestoque   where empresa in ("+inEmpresasApagar+");", con);
            executeSql("DELETE FROM produtoestoque     where empresa in ("+inEmpresasApagar+");", con);
            createTriggresEstoque(con);

            executeSql("delete FROM fornecedor     where empresa in  ("+inEmpresasApagar+");", con);

            executeSql("delete from socialmailgrupoparticipante  where participante  in ("+pessoasExcluir+");", con);
            executeSql("delete from socialmailpartener    where pessoadestino  in ("+pessoasExcluir+");", con);
            executeSql("delete from socialmailpartener s where  s.socialmail  in (select codigo from socialmail where pessoaorigem   in ("+pessoasExcluir+"));", con);
            executeSql("delete from socialmail where pessoaorigem   in ("+pessoasExcluir+");", con);
            executeSql("delete from socialmailgrupoparticipante g where g.socialmailgrupo  in (select codigo from socialmailgrupo s where  s.dono    in ("+pessoasExcluir+"));", con);
            executeSql("delete from socialmailgrupo s where  s.dono    in ("+pessoasExcluir+");", con);
            executeSql("delete from nfseemitida where cartaocredito  in (select codigo from cartaocredito where movpagamento in (select codigo from movpagamento where pessoa in ("+pessoasExcluir+")));", con);
            executeSql("delete from nfseemitida where cheque in (select codigo from cheque where movpagamento in (select codigo from movpagamento where pessoa in ("+pessoasExcluir+")));", con);
            executeSql("delete from nfseemitida where movpagamento in (select codigo from movpagamento where pessoa in ("+pessoasExcluir+"));", con);
            executeSql("delete from movpagamento     where pessoa  in ("+pessoasExcluir+");", con);
            executeSql("delete from recibodevolucao     where pessoa in ("+pessoasExcluir+");", con);
            executeSql("update exclusaomovprodutoestoque set pessoa = 1   where pessoa in ("+pessoasExcluir+");", con);
            executeSql("delete FROM public.atestado  where  arquivo in ( select codigo FROM arquivo  where pessoa in  ("+pessoasExcluir+"));", con);
            executeSql("delete FROM public.arquivo  where  pessoa in ("+pessoasExcluir+");", con);
            executeSql("delete FROM public.notafiscalconsumidoreletronica  where  pessoa in ("+pessoasExcluir+");", con);
            executeSql("delete from nfseemitida where movproduto in (select codigo FROM movproduto      where  pessoa in ("+pessoasExcluir+"));", con);
            executeSql("delete FROM public.movproduto  where  pessoa in ("+pessoasExcluir+");", con);
            executeSql("delete FROM public.boleto  where  pessoa in ("+pessoasExcluir+");", con);
            executeSql("delete from pessoa where codigo in ("+pessoasExcluir+");", con);

            executeSql("delete from planoduracaocreditotreino  where planoduracao in (select codigo from planoduracao  where plano in (select codigo from plano p where (empresa in  ("+inEmpresasApagar+") or exists (select codigo from planoempresa where plano = p.codigo and empresa in  ("+inEmpresasApagar+"))) and not exists (select codigo from contrato where plano = p.codigo)));", con);
            executeSql("delete from planoempresa where empresa in ("+inEmpresasApagar+");", con);

            executeSql("delete from plano p where empresa in ("+inEmpresasApagar+") and not exists (select codigo from contrato where plano = p.codigo) and not exists (select codigo from planoempresa where plano = p.codigo and  empresa  in ("+inEmpresasQueFicam+")) ;", con);
            for (Integer empresaQueFicara : listaEmpresasQueFicam  ) {
                executeSql("update plano p set empresa = " + empresaQueFicara + " where empresa in (" + inEmpresasApagar + ") and (exists (select codigo from contrato where plano = p.codigo and empresa = "+empresaQueFicara+") or exists(select codigo from planoempresa where plano = p.codigo and  empresa  ="+empresaQueFicara+")) ;", con);
            }
            for (Integer empresaQueFicara : listaEmpresasQueFicam  ) {
                executeSql("update composicao c set empresa = " + empresaQueFicara + " where empresa in (" + inEmpresasApagar + ") and exists (select codigo from planocomposicao pc where pc.composicao = c.codigo and (plano in (select codigo from plano where empresa = "+empresaQueFicara+") or plano in (select plano from planoempresa where empresa  = "+empresaQueFicara+")))", con);
            }
            executeSql("delete from composicao  where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from caixaconta where conta in (select codigo from conta where  empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("update movconta set conta = null where conta in  (select codigo from conta where  empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("update movconta set contaorigem = null where contaorigem in  (select codigo from conta where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from conta where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from contacorrenteempresa  where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("update formapagamento  set conveniocobranca = null where conveniocobranca in (select codigo from conveniocobranca con where not exists (select codigo from conveniocobrancaempresa where conveniocobranca = con.codigo));", con);
            executeSql("delete from autorizacaocobrancacliente  a where  a.conveniocobranca  in (select codigo from conveniocobranca con where not exists (select codigo from conveniocobrancaempresa where conveniocobranca = con.codigo));", con);
            executeSql("delete from extratodiarioitem  where conveniocobranca in (select codigo from conveniocobranca con where not exists (select codigo from conveniocobrancaempresa where conveniocobranca = con.codigo));", con);
            executeSql("update movpagamento set conveniocobranca = null where conveniocobranca  in (select codigo from conveniocobranca con where not exists (select codigo from conveniocobrancaempresa where conveniocobranca = con.codigo));", con);
            executeSql("delete from transacao where conveniocobranca   in  (select codigo from conveniocobranca con where not exists (select codigo from conveniocobrancaempresa where conveniocobranca = con.codigo));", con);
            executeSql("delete from movparcelatentativaconvenio   where conveniocobranca  in (select codigo from conveniocobranca   where codigo  in (select codigo from conveniocobranca con where not exists (select codigo from conveniocobrancaempresa where conveniocobranca = con.codigo)));", con);
            executeSql("delete from boleto  where conveniocobranca  in (select codigo from conveniocobranca   where codigo  in (select codigo from conveniocobranca con where not exists (select codigo from conveniocobrancaempresa where conveniocobranca = con.codigo)));", con);
            executeSql("delete from conveniocobranca   where codigo  in (select codigo from conveniocobranca con where not exists (select codigo from conveniocobrancaempresa where conveniocobranca = con.codigo));", con);
            executeSql("update contrato c set conveniodesconto = null where conveniodesconto  in (select codigo from conveniodesconto where empresa in ("+inEmpresasApagar+"))", con);
            executeSql("delete from conveniodesconto    where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from dfsinteticodetalhe   where empresa in  ("+inEmpresasApagar+");", con);
            for (Integer empresaQueFicara : listaEmpresasQueFicam  ) {
                executeSql("update justificativaoperacao set empresa = " + empresaQueFicara + " where  empresa in (" + inEmpresasApagar + ") and  codigo  in (select co.tipojustificativa from contratooperacao co inner join contrato c on c.codigo = co.contrato where c.empresa = "+empresaQueFicara+");", con);
            }
            executeSql("delete from justificativaoperacao   where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from logdcc  where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete FROM public.metafinanceiraconsultor  where  metafinanceiraempresa in ( select codigo FROM metafinanceiraempresa  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from metafinanceiraempresavalores    where metafinanceiraempresa  in  (select codigo from metafinanceiraempresa  where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from metafinanceiraempresa  where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from modalidadeempresa   where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("DELETE FROM auladesmarcada WHERE turma IN (SELECT codigo FROM turma WHERE empresa IN("+inEmpresasApagar+"));", con);
            executeSql("DELETE FROM contratomodalidadeturma WHERE turma IN (SELECT codigo FROM turma WHERE empresa IN("+inEmpresasApagar+"));", con);
            executeSql("DELETE FROM produtoturmavendasonline WHERE codigoturma IN (SELECT codigo FROM turma WHERE empresa IN("+inEmpresasApagar+"));", con);

            executeSql("delete from turma    where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from comissaogeralconfiguracao     where empresa in  ("+inEmpresasApagar+");", con);
            executeSql("delete from armario     where empresa in  ("+inEmpresasApagar+");", con);


            executeSql("delete from validacaolocalacesso where empresa  in ("+inEmpresasApagar+");", con);
            executeSql("delete from comissaoprodutoconfiguracao c where empresa   in ("+inEmpresasApagar+");", con);
            executeSql("delete from camera where servidorfacial in (select codigo from servidorfacial where empresa in  ("+inEmpresasApagar+"));", con);
            executeSql("delete from servidorfacial  where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from formapagamentoempresa  where empresa  in ("+inEmpresasApagar+");", con);
            executeSql("delete from logcobrancapacto where empresa  in ("+inEmpresasApagar+");", con);
            executeSql("delete from pactopayconfig  where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from itemcampanha  where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from optin  where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from descontoempresa where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from nfseemitidahistorico where empresa in ("+inEmpresasApagar+");", con);
            executeSql("update empresa set configuracaonotafiscalnfse = null where configuracaonotafiscalnfse in  (select codigo from configuracaonotafiscal where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("update empresa set configuracaonotafiscalnfce = null where configuracaonotafiscalnfce in (select codigo from configuracaonotafiscal where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("update produto  set configuracaonotafiscalnfse = null where configuracaonotafiscalnfse in (select codigo from configuracaonotafiscal where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from nfseemitida where configuracaonotafiscal in (select codigo from configuracaonotafiscal where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from notafiscalconsumidoreletronica n where n.configuracaonotafiscal  in  (select codigo from configuracaonotafiscal where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("update produto set configuracaonotafiscalnfse = null where configuracaonotafiscalnfse  in  (select codigo from configuracaonotafiscal where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("update produto set configuracaonotafiscalnfce = null where configuracaonotafiscalnfce  in  (select codigo from configuracaonotafiscal where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from configuracaonotafiscal where empresa in ("+inEmpresasApagar+");", con);
            executeSql("update colaborador set departamento = null where departamento in (select codigo from departamento where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from departamento where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from grupocolaborador where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from configuracaoempresatotem where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from logemissaonotafiscal where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from notafiscalhistorico where notafiscal in (select codigo from notafiscal where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from notafiscaloperacao where notafiscal in (select codigo from notafiscal where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from notafiscal where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from dadosgame where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from menuvendasonline where  codvendasonlineconfig in (select codigo from vendasonlineconfig where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from paginainicialvendasonline where  codvendasonlineconfig in (select codigo from vendasonlineconfig where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from contatorodapevendasonline where codvendasonlineconfig in (select codigo from vendasonlineconfig where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from fotofachadavendasonline where codvendasonlineconfig in (select codigo from vendasonlineconfig where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from modalidadecarrouselvendasonline where codvendasonlineconfig in (select codigo from vendasonlineconfig where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from planositevendasonline where codvendasonlineconfig in (select codigo from vendasonlineconfig where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from vendasonlineconfig where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from autorizacaoacessogrupoempresarial where integracaoacessogrupoempresarial in (select codigo from integracaoacessogrupoempresarial where empresalocal in ("+inEmpresasApagar+"));", con);
            executeSql("delete from autorizacaoacessogrupoempresarial where  empresalocal in ("+inEmpresasApagar+");", con);
            executeSql("delete from integracaoacessogrupoempresarial where empresalocal in ("+inEmpresasApagar+");", con);
            executeSql("delete from configuracaoreenviomovparcelaempresa where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete FROM public.quarentena  where  empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete FROM public.configuracaoemailfechamentometa  where  empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete FROM public.conveniocobrancarateioitem  where  conveniocobrancarateio in (select codigo from conveniocobrancarateio where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete FROM public.conveniocobrancarateio  where  empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete FROM public.configuracaosorteio  where  empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from configuracaointegracaobuzzlead where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from configuracaointegracaogenericaleads where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from configuracaointegracaobotconversa where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from configuracaointegracaojoin where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from configuracaointegracaowordpress where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from configuracaointegracaogenericaleadsgymbot where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from configtotalpass where empresa_codigo in ("+inEmpresasApagar+");", con);
            executeSql("delete from logtotalpass where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from infocheckin where empresa in ("+inEmpresasApagar+");", con);
            executeSql("delete from historicopontos where brinde in (select codigo from brinde where empresa in ("+inEmpresasApagar+"));", con);
            executeSql("delete from brinde where empresa in ("+inEmpresasApagar+");", con);

            executeSql("delete from empresa where codigo in ("+inEmpresasApagar+");", con);

            System.out.println(" ==== Dados excludos com sucesso");
        }catch (Exception e){
            con.rollback();
            System.out.println(e.getMessage());
            throw e;
        } finally {
            con.commit();
        }
        rodarProcessos(con);

    }

    private static void dropTriggresEstoque(Connection con) throws Exception {
        executeSql("DROP TRIGGER tg_bd_alterarestoque_balancoitens ON balancoitens;", con);
        executeSql("DROP TRIGGER tg_bd_alterarestoque_balanco ON balanco;", con);
        executeSql("DROP TRIGGER tg_bd_alterarestoque_compra ON compra;", con);
        executeSql("DROP TRIGGER tg_bd_alterarestoque_compraitens ON compraitens;", con);
        executeSql("DROP TRIGGER tg_bd_alterarestoque_produtoestoque ON produtoestoque;", con);

    }
    private static void createTriggresEstoque(Connection con) throws Exception {
        executeSql("CREATE TRIGGER tg_bd_alterarestoque_balancoitens\n" +
                "  BEFORE DELETE\n" +
                "  ON balancoitens\n" +
                "  FOR EACH ROW\n" +
                "  EXECUTE PROCEDURE fn_tg_before_alterarestoque();", con);
        executeSql("CREATE TRIGGER tg_bd_alterarestoque_balanco\n" +
                "  BEFORE DELETE\n" +
                "  ON balanco\n" +
                "  FOR EACH ROW\n" +
                "  EXECUTE PROCEDURE fn_tg_before_alterarestoque();", con);
        executeSql("CREATE TRIGGER tg_bd_alterarestoque_compra\n" +
                "  BEFORE DELETE\n" +
                "  ON compra\n" +
                "  FOR EACH ROW\n" +
                "  EXECUTE PROCEDURE fn_tg_before_alterarestoque();", con);
        executeSql("CREATE TRIGGER tg_bd_alterarestoque_compraitens\n" +
                "  BEFORE DELETE\n" +
                "  ON compraitens\n" +
                "  FOR EACH ROW\n" +
                "  EXECUTE PROCEDURE fn_tg_before_alterarestoque();", con);
        executeSql("CREATE TRIGGER tg_bd_alterarestoque_produtoestoque\n" +
                "  BEFORE DELETE\n" +
                "  ON produtoestoque\n" +
                "  FOR EACH ROW\n" +
                "  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n", con);
    }

    private static void rodarProcessos(Connection con) throws Exception {
        System.out.println("===== Rodando processos");
        SituacaoClienteSinteticoDWControle.executar();
        System.out.println("===== Processos Rodados");
    }

    private static void executeSql(String sql, Connection c) throws Exception {
        try (Statement stm = c.createStatement()) {
            Uteis.logarDebug("Executando: "+sql);
            stm.executeUpdate(sql);
        } catch (Exception e) {
            throw new Exception("Erro ao rodar sql:" + sql +"\nERRO>>:"+e.getMessage());
        }
    }

}

