package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "05/08/2025",
        descricao = "Correção do tamanho da coluna descricao da tabela produto",
        motivacao = "GC-2690")
public class AtualizacaoTicketGC2690 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            if (colunaComTamanhoCorreto(c)) {
                return;
            }

            try {
                c.setAutoCommit(false);

                List<String> viewsNames = Arrays.asList(
                        "vendas_detalhadas_final",
                        "vendas_detalhadas_produtos_repeticoes",
                        "vendas_detalhadas",
                        "dadoscampanhaview"
                );

                Map<String, String> mapViewsDefinitions = montarMapViewsDefinitions(c, viewsNames);

                dropViews(c, viewsNames, mapViewsDefinitions);

                SuperFacadeJDBC.executarUpdate("ALTER TABLE produto ALTER COLUMN descricao TYPE VARCHAR(100) USING descricao::VARCHAR(100);", c, false);

                recreateViews(c, viewsNames, mapViewsDefinitions);

                c.commit();
            } catch (Exception e) {
                c.rollback();
                Uteis.logar("Erro ao executar processo: " + e.getMessage(), AtualizacaoTicketGC2690.class);
            } finally {
                c.setAutoCommit(true);
            }
        }
    }

    private boolean colunaComTamanhoCorreto(Connection c) throws Exception {
        String sql = "SELECT cl.* FROM information_schema.columns cl\n" +
                "WHERE cl.table_name = 'produto' \n" +
                "AND cl.column_name = 'descricao'\n" +
                "AND cl.character_maximum_length = 100";
        return SuperFacadeJDBC.existe(sql, c);
    }

    private void recreateViews(Connection c, List<String> viewsNames, Map<String, String> mapViewsDefinitions) throws SQLException {
        // criar em ordem reversa para considerar dependencias entre views
        for (int i = viewsNames.size() - 1; i >= 0; i--) {
            String viewName = viewsNames.get(i);

            try {
                String definition = mapViewsDefinitions.get(viewName);
                if (UteisValidacao.emptyString(definition)) {
                    continue;
                }
                String sql = String.format("CREATE VIEW %s AS %s", viewName, mapViewsDefinitions.get(viewName));
                SuperFacadeJDBC.executarUpdate(sql, c, false);
            } catch (Exception e) {
                Uteis.logarDebug("Erro ao criar view: " + viewName);
                throw e;
            }
        }
    }

    private void dropViews(Connection c, List<String> viewsNames, Map<String, String> mapViewsDefinitions) throws SQLException {
        for (String viewName : viewsNames) {
            if (!mapViewsDefinitions.containsKey(viewName)) {
                continue;
            }
            try {
                String sql = String.format("DROP VIEW %s;", viewName);
                SuperFacadeJDBC.executarUpdate(sql, c, false);
            } catch (Exception e) {
                Uteis.logarDebug("Erro ao remover view: " + viewName);
                throw e;
            }
        }
    }

    private Map<String, String> montarMapViewsDefinitions(Connection c, List<String> viewsNames) throws SQLException {
        Map<String, String> mapViewsDefinitions = new HashMap<>();
        for (String viewName : viewsNames) {
            String definition = consultarViewDefinition(c, viewName);
            if (!UteisValidacao.emptyString(definition)) {
                mapViewsDefinitions.put(viewName, definition);
            }
        }
        return mapViewsDefinitions;
    }

    private String consultarViewDefinition(Connection con, String viewName) throws SQLException {
        String sql = " SELECT definition FROM pg_views WHERE viewname = ?;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, viewName);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("definition");
                }
            }
        }
        return null;
    }
}
