package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "30/07/2025",
        descricao = "Cria colunas para salvar url do webHook que vai receber os dados do evento",
        motivacao = "M1-6318")
public class CriarColunaEnviarMalaDiretaWebHook implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        String sql = "ALTER TABLE maladireta ADD COLUMN urlWebhook VARCHAR(255) DEFAULT NULL;";

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}
