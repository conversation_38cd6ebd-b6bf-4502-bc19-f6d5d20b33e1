package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;


@ClasseProcesso(autor = "Luis Antônio de Melo Gomes",
        data = "29/07/2025",
        descricao = "Implementar historico de prompts da configuração de IA",
        motivacao = "IA-1400")
public class AtualizacaoTIcketIA1400 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("" +
                    "CREATE TABLE historicoPrompt " +
                    "( " +
                    "    codigo             SERIAL PRIMARY KEY, " +
                    "    codigoempresa      integer                     null, " +
                    "    codigoconfiguracao integer                     null, " +
                    "    origemcampo        text                        null, " +
                    "    usuario            text                        null, " +
                    "    restore            boolean                     NOT NULL, " +
                    "    conteudo           TEXT                        NULL, " +
                    "    criado_em          TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(), " +
                    "    CONSTRAINT fk_configuracao FOREIGN KEY (codigoconfiguracao) " +
                    "        REFERENCES public.configuracaocrmia (codigo) " +
                    ");", c);
        }
    }
}
