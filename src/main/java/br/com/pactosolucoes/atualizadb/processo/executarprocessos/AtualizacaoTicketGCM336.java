package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Thalles Faria",
        data = "13/05/2025",
        descricao = "GCM-336 - Configurao Funil de Vendas",
        motivacao = "GCM-336 - Configurao Funil de Vendas")
public class AtualizacaoTicketGCM336 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistemacrm ADD COLUMN modelocrmfunil boolean default false;", c);
        }
    }
}
