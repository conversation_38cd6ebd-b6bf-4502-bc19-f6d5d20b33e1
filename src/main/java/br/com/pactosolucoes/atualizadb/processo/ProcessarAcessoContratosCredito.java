package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessarAcessoContratosCredito {

    private static ZillyonWebFacade zwFacadeDAO;

    private static AcessoCliente acessoClienteDao;

    private static ControleCreditoTreino controleCreditoTreinoDAO;

    private static Cliente clienteDAO;


    public static void main(String... args) {
        try {
            String chave =args.length > 0 ? args[0] : "bodyhiiit";
            Connection c = new DAO().obterConexaoEspecifica(chave);
//            Connection c = DriverManager.getConnection("*******************************************************", "zillyonweb", "pactodb");
//            String chave =args.length > 0 ? args[0] : "pacto";
           // processarConsumo(c,chave);
            executarProcesso(c);


        } catch (Exception ex) {
            Logger.getLogger(RefazerVinculoMovProdutoParcelaContratos.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void executarProcesso(Connection con) throws Exception {
        String chave = DAO.resolveKeyFromConnection(con);
        processarConsumo(con, chave, false, Calendario.hoje());
    }

    private static void inicializar(Connection con) throws Exception {
        zwFacadeDAO = new ZillyonWebFacade(con);
        acessoClienteDao = new AcessoCliente(con);
        controleCreditoTreinoDAO = new ControleCreditoTreino(con);
        clienteDAO = new Cliente(con);
    }

    public static void processarConsumo(Connection con, String chave, boolean somenteHorarioLivre, Date dataAvaliar) throws Exception {
        inicializar(con);
        atualizaSinteticoAlunosContratoErrado(con);
        if(UteisValidacao.emptyString(chave)){
            chave = DAO.resolveKeyFromConnection(con);
        }

        StringBuffer sbErros = new StringBuffer("Resumo de Erros: \n");

        StringBuilder sqlConsulta = new StringBuilder();
        sqlConsulta.append("SELECT ac.*\n");
        sqlConsulta.append("FROM acessocliente ac\n");
        sqlConsulta.append("INNER JOIN cliente cli ON cli.codigo = ac.cliente\n");
        sqlConsulta.append(" inner join localacesso l on l.codigo = ac.localacesso \n");
        sqlConsulta.append(" inner join empresa emp on emp.codigo = l.empresa  \n");
        sqlConsulta.append("WHERE dthrentrada > '").append(Uteis.getDataJDBC(Uteis.somarDias(dataAvaliar, -3))).append(" 00:00:00.000'\n"); // avaliar até 3 dias atrás
        sqlConsulta.append("AND dthrentrada < '").append(Uteis.getDataJDBC(dataAvaliar)).append(" 23:59:59'\n");
        sqlConsulta.append("AND NOT EXISTS (SELECT codigo\n");
        sqlConsulta.append("\tFROM controlecreditotreino\n");
        sqlConsulta.append("\tWHERE acessocliente = ac.codigo)\n");
        sqlConsulta.append("AND\n");
        sqlConsulta.append("EXISTS (SELECT c.codigo\n");
        sqlConsulta.append("\tFROM contrato c\n");
        sqlConsulta.append("\tINNER JOIN contratoduracao cd ON c.codigo = cd.contrato\n");
        sqlConsulta.append("\tINNER JOIN contratoduracaocreditotreino cdt ON cd.codigo = cdt.contratoduracao\n");
        sqlConsulta.append("\tWHERE pessoa = cli.pessoa\n");
        if (somenteHorarioLivre) {
            sqlConsulta.append("\tAND tipohorario = 1\n");
        }
        sqlConsulta.append("\tAND ac.dthrentrada BETWEEN vigenciade AND vigenciaateajustada\n");
        sqlConsulta.append("\tAND vendacreditotreino)\n");
        sqlConsulta.append("AND ac.situacao ILIKE 'RV_LIBACESSOAUTORIZADO'\n");
        // Verifica se não foi descontado nenhum crédito de marcação de aula
        sqlConsulta.append("AND NOT EXISTS (SELECT cct.codigo FROM controlecreditotreino cct\n");
        sqlConsulta.append("\tINNER JOIN contrato con ON con.codigo = cct.contrato  \n");
        sqlConsulta.append("\tINNER JOIN horarioturma ht ON ht.codigo = cct.horarioturma \n");
        sqlConsulta.append("\tWHERE con.pessoa = cli.pessoa\n");
        sqlConsulta.append("\tAND cct.quantidade < 0\n");
        sqlConsulta.append("\tAND cct.tipooperacaocreditotreino = 4\n");
        sqlConsulta.append("\tAND ac.dthrentrada BETWEEN DATE_TRUNC('minute', cct.diaaula::date + ht.horainicial::time) AND DATE_TRUNC('minute', cct.diaaula::date + ht.horafinal::time))");
        sqlConsulta.append("AND not exists(select codigo from acessocliente  where ac.cliente = cliente and localacesso = ac.localacesso and codigo <> ac.codigo and dthrentrada between  ac.dthrentrada - (emp.minutosaposultimoacessodiminuircredito||' minutes')::interval  and ac.dthrentrada)\n");
        sqlConsulta.append("ORDER BY cliente, ac.codigo;\n");

        List<AcessoClienteVO > acessosSemUtilizacao = zwFacadeDAO.getAcessoCliente().consultar(sqlConsulta.toString(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        UsuarioVO usuarioAdm = new UsuarioVO();
        usuarioAdm.setCodigo(1);
        for (AcessoClienteVO acesso: acessosSemUtilizacao) {
            con.setAutoCommit(false);
            try {
                SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO = controleCreditoTreinoDAO.getSituacaoClienteSinteticoDWDao().consultarCliente(acesso.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (situacaoClienteSinteticoDWVO.isValidarSaldoCreditoTreino() && !UteisValidacao.emptyNumber(situacaoClienteSinteticoDWVO.getCodigoContrato())) {
                    boolean diminuirCredito = true;
                    AcessoClienteVO ultimoAcesso = zwFacadeDAO.getAcessoCliente().consultarUltimoAcessoDiferenteDe(acesso.getCliente(), acesso.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (ultimoAcesso != null && !UteisValidacao.emptyNumber(ultimoAcesso.getCodigo())) {
                        long minutosAposUltimoAcesso = Calendario.diferencaEmMinutos((ultimoAcesso.getDataHoraSaida() != null) ? ultimoAcesso.getDataHoraSaida() : ultimoAcesso.getDataHoraEntrada(), acesso.getDataHoraEntrada());
                        diminuirCredito = (Math.abs(minutosAposUltimoAcesso) >= ultimoAcesso.getCliente().getEmpresa().getMinutosAposUltimoAcessoDiminuirCredito());
                    }
                    if (diminuirCredito) {
                        acesso.getCliente().setUsuarioAux(usuarioAdm);
                        acesso.setUsuario(null);
                        controleCreditoTreinoDAO.diminuirCreditoPorAcesso(chave,situacaoClienteSinteticoDWVO, acesso);
                    }
                }

                con.commit();
            }catch (Exception e) {
                con.rollback();
                Uteis.logar(e, RefazerVinculoMovProdutoParcelaContratos.class);
            }finally {
                con.setAutoCommit(true);
            }
        }
    }

    private static void estornarConsumo(Connection con, final String chave) throws Exception {
        StringBuffer sbErros = new StringBuffer("Resumo de Erros: \n");

        List<AcessoClienteVO > acessosSemUtilizacao = zwFacadeDAO.getAcessoCliente().consultar("SELECT * FROM acessocliente a \n" +
                "WHERE a.codigo IN (\n" +
                "SELECT foo.codigoacesso\n" +
                "FROM acessocliente ac\n" +
                "INNER JOIN controlecreditotreino cc ON cc.acessocliente = ac.codigo\n" +
                "INNER JOIN \n" +
                "(SELECT ac1.codigo AS codigoacesso, ac1.cliente, dthrentrada\n" +
                "\tFROM acessocliente ac1\n" +
                "\tINNER JOIN controlecreditotreino cc1 ON\n" +
                "\tcc1.acessocliente = ac1.codigo\n" +
                "\tWHERE dthrentrada < '2021-08-30'\n" +
                "\tAND dataregistro::date = '2021-08-30') AS foo \n" +
                "ON foo.cliente = ac.cliente\n" +
                "AND foo.codigoacesso <> ac.codigo\n" +
                "AND (foo.dthrentrada - ac.dthrentrada < '02:00:00'\n" +
                "\tAND foo.dthrentrada - ac.dthrentrada > '-02:00:00')\n" +
                ") ", Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        UsuarioVO usuarioAdm = new UsuarioVO();
        usuarioAdm.setCodigo(1);
        for (AcessoClienteVO acesso: acessosSemUtilizacao) {
            con.setAutoCommit(false);
            try {
                acesso.getCliente().setUsuarioAux(usuarioAdm);
                acesso.setUsuario(null);
                controleCreditoTreinoDAO.atualizarUtilizacaoParaFaltaExclusaoAcesso(acesso, "AJUSTE - AD2-3061", true);

                con.commit();
            }catch (Exception e) {
                con.rollback();
                Uteis.logar(e, RefazerVinculoMovProdutoParcelaContratos.class);
            }finally {
                con.setAutoCommit(true);
            }
        }
    }

    private static void atualizaSinteticoAlunosContratoErrado(Connection con) throws Exception{
        StringBuilder sqlConsulta = new StringBuilder();
        sqlConsulta.append("SELECT sdw.codigocliente AS codCliente, *\n");
        sqlConsulta.append("FROM situacaoclientesinteticodw sdw\n");
        sqlConsulta.append("INNER JOIN contrato con ON con.codigo = sdw.codigocontrato\n");
        sqlConsulta.append("WHERE 1=1\n");
        sqlConsulta.append("AND sdw.situacao <> con.situacao\n");
        sqlConsulta.append("AND sdw.situacao = 'AT'\n");

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlConsulta.toString())) {
                while (rs.next()) {
                    ClienteVO clienteVO = clienteDAO.consultarPorCodigo(rs.getInt("codCliente"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    zwFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                }
            }
        }
    }

}
