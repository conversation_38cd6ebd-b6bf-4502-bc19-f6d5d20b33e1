package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "11/08/2025",
        descricao = "Novas informações para transação de cartões",
        motivacao = "PAY-1319")
public class PAY1319GravarParametrosdEnvioCadastroPessoa implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE transacao add column paramsenviopessoa text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE transacao add column paramsRespostaPessoa text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE transacao add column erroIncluirPessoa boolean;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE transacao add column erroAtualizarPessoa boolean;", c);
        }
    }
}


