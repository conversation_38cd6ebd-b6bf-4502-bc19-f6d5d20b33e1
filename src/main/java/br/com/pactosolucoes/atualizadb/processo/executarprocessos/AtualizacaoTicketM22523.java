package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Vinicius de Moraes",
        data = "11/11/2024",
        descricao = "M2-2523 - Excluir/Inativar Configuração da Integração GymBot Pro no CRM",
        motivacao = "M2-2523")
public class AtualizacaoTicketM22523 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE public.configuracaointegracaogymbotpro ADD COLUMN ativo boolean DEFAULT true;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE public.configuracaointegracaobotconversa ADD COLUMN ativo boolean DEFAULT true;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE configuracaointegracaogymbotpro ALTER COLUMN codigo TYPE int;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE maladireta ALTER COLUMN configuracaointegracaogymbotpro TYPE INTEGER;", c);
        }
    }
}


