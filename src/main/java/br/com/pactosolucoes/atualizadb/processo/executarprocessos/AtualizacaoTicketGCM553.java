package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Vinicius Costa Franca",
        data = "26/06/2025",
        descricao = "Criar tabelas para log de operao de leads",
        motivacao = "GCM-287 / GCM-553"
)
public class AtualizacaoTicketGCM553 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            // Tabela logoperacaolead
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE  logoperacaolead (" +
                            "codigo SERIAL PRIMARY KEY, " +
                            "empresa INT NOT NULL, " +
                            "pessoa INT NULL, " +
                            "funil INT, " +
                            "cardlead INT, " +
                            "faseorigem INT, " +
                            "fasedestino INT, " +
                            "usuario INT NOT NULL, " +
                            "datalog TIMESTAMP DEFAULT NOW(), " +
                            "acao VARCHAR(50) NOT NULL, " +
                            "descricao TEXT, " +
                            "observacao TEXT, " +
                            "tagsalteradas TEXT" +
                            ");",
                    c
            );

            // Indexes recomendados para a nova tabela
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE INDEX  idx_logoperacaolead_empresa ON logoperacaolead(empresa);",
                    c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE INDEX  idx_logoperacaolead_cardlead ON logoperacaolead(cardlead);",
                    c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE INDEX  idx_logoperacaolead_datalog ON logoperacaolead(datalog);",
                    c
            );
        }
    }
}
