
package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "30/05/2025",
        descricao = "Remover obrigatoriedade de client_id e client_secret da tabela Pix",
        motivacao = "GC-2229")
public class AtualizacaoTicketGC2229 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pix ALTER COLUMN client_id DROP NOT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pix ALTER COLUMN client_secret DROP NOT NULL;", c);
        }
    }
}
