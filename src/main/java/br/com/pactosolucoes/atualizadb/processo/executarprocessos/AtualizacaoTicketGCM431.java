package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Vinicius Franca",
        data = "25/05/2025",
        descricao = "Adiciona colunas na tabela agenda",
        motivacao = "GCM-431"
)
public class AtualizacaoTicketGCM431 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE agenda ADD COLUMN agendamentopai int;", c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE agenda ADD COLUMN cardleadcod int;", c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE agenda ADD COLUMN reagendado boolean;", c
            );
        }
    }
}
