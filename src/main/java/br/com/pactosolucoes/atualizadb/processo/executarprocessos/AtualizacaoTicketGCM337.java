package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Athos Feitosa",
        data = "19/05/2025",
        descricao = "Altera a tabela tagfunillead para incluir coluna status e cria a tabela logtagsfunil",
        motivacao = "GCM-337 - [Go to Dev][Well] Criar backend do cadastro de Tag"
)
public class AtualizacaoTicketGCM337 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE tagfunillead " +
                            "ALTER COLUMN nome TYPE VARCHAR(32), " +
                            "ADD COLUMN ativo BOOLEAN DEFAULT TRUE;",
                    c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE logtagsfunil (" +
                            "codigo serial PRIMARY KEY, " +
                            "nome_tag VARCHAR(32) NOT NULL, " +
                            "acao VARCHAR(20) NOT NULL, " +
                            "data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                            "codigo_usuario INTEGER NOT NULL, " +
                            "CONSTRAINT fk_logtags_usuario FOREIGN KEY (codigo_usuario) REFERENCES usuario(codigo)" +
                            ");",
                    c
            );

        }
    }
}
