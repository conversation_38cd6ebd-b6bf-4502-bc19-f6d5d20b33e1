package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "07/08/2025",
        descricao = "Corrigir código da permissão",
        motivacao = "GC-2672")
public class AtualizacaoTicketGC2672 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String sql = "UPDATE permissao SET tituloapresentacao = '10.13 - Permite visualizar relatório Gogood por período' \n" +
                    " WHERE nomeentidade = 'PermiteVisualizaGogoodPeriodo';";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}
