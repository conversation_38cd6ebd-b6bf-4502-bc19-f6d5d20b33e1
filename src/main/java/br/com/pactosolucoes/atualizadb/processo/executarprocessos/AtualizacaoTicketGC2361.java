
package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON> Mo<PERSON>",
        data = "18/06/2025",
        descricao = "Abertura de tranca magnetica / Vendas online para categoria de produtos",
        motivacao = "GC-2361")
public class AtualizacaoTicketGC2361 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN tema VARCHAR;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN igopassUrl VARCHAR;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN igopassQrCodeUrl VARCHAR;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN igopassEstabelecimento VARCHAR;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN igopassUsuario VARCHAR;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD COLUMN destravarTranca BOOLEAN DEFAULT false;", c);
        }
    }
}
