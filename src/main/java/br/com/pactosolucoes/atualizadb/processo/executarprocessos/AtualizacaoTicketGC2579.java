package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Vitor Junio",
        data = "22/07/2025",
        descricao = "Adicionar coluna para justificativa da operao da renegociao de parcelas e permisso para todos os perfis.",
        motivacao = "GC-2579")
public class AtualizacaoTicketGC2579 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            // Adicionar coluna justificativaoperacao na tabela movparcela
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movparcela ADD COLUMN justificativaoperacao INTEGER;"
                    , c);

            // Adicionar permisso para justificativa de operao em todos os perfis de acesso
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM perfilacesso", c);

            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '4.49 - Permitir renegociar parcelas sem justificativa','(0)(1)(2)(3)(9)(12)', 'RenegociarParcelasSemJustificativa', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        }
    }
}
