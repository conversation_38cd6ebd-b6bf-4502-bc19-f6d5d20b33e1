package br.com.pactosolucoes.atualizadb.processo.gogood;

import acesso.webservice.DaoAuxiliar;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONObject;
import servicos.integracao.enumerador.IntegracoesEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import servlet.integracao.GympassBookingServlet;

import java.sql.ResultSet;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class CheckinGoGoodService {

    private final String GOGOOD_INCLUSAO = "INCLUSÃO GOGOOD - CHECKIN";
    private final String GOGOOD_WEBHOOK = "INCLUSÃO GOGOOD - WEBHOOK";
    private final String GOGOOD_FALHA = "INCLUSÃO GOGOOD - FALHA";
    private final String GOGOOD_NAO_EXISTE = "INCLUSÃO GOGOOD - ALUNO NÃO ENCONTRADO";
    private final String GOGOOD_JSON_WEBHOOK = "WEBHOOK GOGOOD - JSON";
    private final String GOGOOD_CONTATO = " Por favor entre em contato com a GOGOOD";
    private final String ERRO_GOGOOD_ACESS_CONTROL_VALIDATE = " Validação de acesso da GOGOOD";

    public String checkInGymPass(String key,
                                 String json,
                                 final int empresa) {
        String unique_token = "";
        String retorno = "";
        try {
            JSONObject objJson = new JSONObject(json);
            unique_token = objJson.getJSONObject("event_data").getJSONObject("user").get("unique_token").toString();
            String gympasstypenumber = objJson.getJSONObject("event_data").getJSONObject("gym").getJSONObject("product").get("id").toString();
            String produtoGymPass = objJson.getJSONObject("event_data").getJSONObject("gym").getJSONObject("product").get("id") + ";" +
                    objJson.getJSONObject("event_data").getJSONObject("gym").getJSONObject("product").get("description");
            Date hoje = Calendario.hoje();
            Calendar cal = Calendar.getInstance();
            boolean isHorarioVerao = cal.getTimeZone().inDaylightTime(Calendario.hoje());
            Date dataTimezone = hoje;
            if (isHorarioVerao){
                dataTimezone = new Date(dataTimezone.getTime() - cal.getTimeZone().getDSTSavings());
            }
            boolean existeCliente = DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().existeTokenGymPass(unique_token);
            gravaLogGOGOOD(key, new ClienteVO(), unique_token, GOGOOD_JSON_WEBHOOK, json);
            if (!existeCliente){
                gravaLogGOGOOD(key, new ClienteVO(), unique_token, GOGOOD_NAO_EXISTE, "ERRO: Não existe aluno cadastrado com esse Token.");
                return "ERRO: Não existe aluno cadastrado com esse Token.";
            }
            ClienteVO clienteGympass = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarGymPass(unique_token , empresa ,  Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(empresa,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            JSONObject o = new JSONObject();
            o.put("tipoMock", "gympass");
            o.put("pass_number", unique_token);
            o.put("gym_number", empresaVO.getCodigoGymPass());
            o.put("pass_type_number", gympasstypenumber);
            o.put("auth_token", empresaVO.getTokenApiGymPass());
            Map<String, String> headers = new HashMap<String, String>();
            final String urlConsultar = String.format("%s?mockcontent=%s",
                    PropsService.getPropertyValue(PropsService.urlMockZWServer),
                    Uteis.encriptar(o.toString(), "m0oOCk"));
            Uteis.logar(null, "GYMPASS (MOCK) Requisição | " + urlConsultar);

            try {
                retorno = ExecuteRequestHttpService.executeHttpRequestGETEncode(urlConsultar, headers, "UTF-8");
                gravaLogGOGOOD(key, clienteGympass, unique_token, GOGOOD_WEBHOOK, retorno);
            } catch (Exception e) {
                gravaLogGOGOOD(key, clienteGympass, unique_token, GOGOOD_FALHA, e.getMessage());
                if (e.getMessage().toLowerCase().contains("timed out") || e.getMessage().toLowerCase().contains("timedout")) {
                    throw new Exception("GymPass indisponível no momento. Por favor tente mais tarde.");
                } else {
                    throw e;
                }
            }
            Uteis.logar(null, "GYMPASS Retorno | " + retorno);
            JSONObject jsonObject = new JSONObject(retorno);
            JSONObject status = jsonObject.getJSONObject("status");
            int codigoRetorno = status.getInt("code");
            String retornoGymPass = status.getString("description");
            if (codigoRetorno == 0) {
                //TODO: Para que fique mais correto tem que lançar um responsavel com nome de gympass para que identifique o lançamento melhor coloquei 0 para lembrar.
                clienteGympass.setResponsavelFreePass(0);
                clienteGympass.setFreePass(DaoAuxiliar.retornarAcessoControle(key).getProdutoDao().criarOuConsultarProdutoGymPass());
                PeriodoAcessoClienteVO periodoAcesso = null;
                if (Calendario.diferencaEmDias(dataTimezone,hoje) > 1 || Calendario.diferencaEmDias(dataTimezone,hoje) < -1){
                    periodoAcesso = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().incluirFreePass(clienteGympass, Calendario.hoje(), unique_token, gympasstypenumber, null);
                }else{
                    periodoAcesso = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().incluirFreePass(clienteGympass, dataTimezone, unique_token, gympasstypenumber, null);
                }
                if (periodoAcesso != null && !UteisValidacao.emptyString(unique_token)) {
                    DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().gravarInfoCheckin(clienteGympass,
                            new EmpresaVO(empresa),
                            unique_token,
                            periodoAcesso,
                            IntegracoesEnum.GYMPASS,
                            produtoGymPass
                    );
                }
                DaoAuxiliar.retornarAcessoControle(key).getSituacaoClienteSinteticoDWDao().atualizarBaseOffLineZillyonAcesso(key, clienteGympass.getPessoa().getCodigo());
                gravaLogGOGOOD(key, clienteGympass, unique_token, GOGOOD_INCLUSAO, retorno);
            } else {
                throw new Exception("Validar Token GymPass: " + retornoGymPass + GOGOOD_CONTATO);
            }
        } catch (Exception ex) {
            gravaLogGOGOOD(key,new ClienteVO(), unique_token, GOGOOD_INCLUSAO, retorno);
            Uteis.logar(ex, this.getClass());
            if(ex.getMessage().toUpperCase().contains("GYMPASS"))
                return ex.getMessage();
            return "ERRO:" + ex.getMessage();
        }
        return "GymPass lançado com sucesso!";
    }

    public String checkInGymPassValidarAcesso(String key,
                                 String json,
                                 final int empresa) {
        String userToken = "";
        String tokenAcademy = "";
        String retorno = "";
        try {
            JSONObject objJson = new JSONObject(json);
            userToken = objJson.optString("user_token");
            tokenAcademy = objJson.optString("partner_token");
            Date hoje = Calendario.hoje();
            Calendar cal = Calendar.getInstance();
            boolean isHorarioVerao = cal.getTimeZone().inDaylightTime(Calendario.hoje());
            Date dataTimezone = hoje;
            if (isHorarioVerao){
                dataTimezone = new Date(dataTimezone.getTime() - cal.getTimeZone().getDSTSavings());
            }
            boolean existeCliente = DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().existeTokenGoGood(userToken, tokenAcademy);
            gravaLogGOGOOD(key, new ClienteVO(), userToken, GOGOOD_JSON_WEBHOOK, json);
            if (!existeCliente){
                gravaLogGOGOOD(key, new ClienteVO(), userToken, GOGOOD_NAO_EXISTE, "ERRO: Não existe aluno cadastrado com esse Token.");
                return "ERRO: Não existe aluno cadastrado com esse Token.";
            }
            ClienteVO cliente = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarGoGood(userToken, tokenAcademy, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (cliente != null && !UteisValidacao.emptyNumber(cliente.getCodigo())) {
                liberarFreePass(key, hoje, dataTimezone, userToken, cliente, empresa, tokenAcademy);
            } else {
                gravaLogGOGOOD(key, new ClienteVO(), userToken, GOGOOD_NAO_EXISTE, "ERRO: Não consegui montar o aluno cadastrado com esse Token.");
                return "ERRO: Não consegui montar o aluno cadastrado com esse Token.";
            }

        } catch (Exception ex) {
            gravaLogGOGOOD(key,new ClienteVO(), userToken, GOGOOD_INCLUSAO, retorno);
            Uteis.logar(ex, this.getClass());
            if(ex.getMessage().toUpperCase().contains("GOGOOD"))
                return ex.getMessage();
            return "ERRO:" + ex.getMessage();
        }
        return "GoGood lançado com sucesso!";
    }

    private String removerPeriodoAcessoCheckinRevogado(String key, JSONObject objJson, String unique_token) {
        try {
            if (objJson.getJSONObject("event_data").has("timestamp")) {
                // as datas em timestamp retornadas pela gympass que vi até esta data estavam no formato de segundos;
                long timestamp = objJson.getJSONObject("event_data").getInt("timestamp");
                Date dataCheckin = new Date(timestamp * 1000L);
                dataCheckin = Calendario.getDataComHoraZerada(dataCheckin);
                DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().excluirSemCommit(
                        String.format(" tokengympass = '%s' and dataInicioAcesso = '%s' and dataFinalAcesso = '%s' ",
                                unique_token, Uteis.getDataFormatoBD(dataCheckin), Uteis.getDataFormatoBD(dataCheckin))
                );
                return "GymPass revogado com sucesso!";
            } else {
                return "Timestamp do checkin não localizado, o GymPass não foi revogado!";
            }
        } catch (Exception e) {
            return "ERRO AO REMOVER PERIODO ACESSO: " + e.getMessage();
        }
    }

    private void buscarOuIncluirAluno(String chave, Integer empresa, JSONObject user, String unique_token) throws Exception{
        try {
            String nome_cliente = user.getString("first_name");
            nome_cliente +=  " " + user.getString("last_name");
            String email_cliente = user.getString("email");
            String phone_number = user.getString("phone_number");

            Integer codigoCliente = consultarSeExisteAluno(chave, user);
            if(codigoCliente == null){
                //Chamar o método de gravar um novo cliente
                GympassBookingServlet bookingServlet = new GympassBookingServlet();
                bookingServlet.gerarAluno(chave, empresa, unique_token, nome_cliente, phone_number, email_cliente);
            } else {
                DaoAuxiliar.retornarAcessoControle(chave).getClienteDao().alterarGymPassUniqueToken(unique_token, codigoCliente);
            }
        }catch (Exception e){
            gravaLogGOGOOD(chave, new ClienteVO(), unique_token, GOGOOD_NAO_EXISTE, "ERRO: Não existe aluno cadastrado com esse Token.");
            throw new Exception("Não existe aluno cadastrado com esse Token.");
        }
    }

    private Integer consultarSeExisteAluno(String chave, JSONObject user) throws Exception{
        String nome_cliente = user.getString("first_name");
        nome_cliente +=  " " + user.getString("last_name");
        String email_cliente = user.getString("email");
        String phone_number = user.getString("phone_number");

        String query = "SELECT cliente.codigo, CONCAT(pessoa.telefoneemergencia, '" + phone_number + "') AS telefone_emergencia " +
                "FROM pessoa " +
                "INNER JOIN cliente ON pessoa.codigo = cliente.pessoa " +
                "WHERE pessoa.nome = '" + nome_cliente + "' AND pessoa.email = '" + email_cliente + "'";


        ResultSet rs = SuperFacadeJDBC.criarConsulta(query, DaoAuxiliar.retornarAcessoControle(chave).getClienteDao().getCon());

        return rs.next() ? rs.getInt("codigo") : null;
    }

    private void removerFreePass(JSONObject objJson, String key, ClienteVO clienteGympass, String unique_token) {
        try {
            String metadata = objJson.getJSONObject("metadata").toString();
            String errors = "";
            if(objJson.has("errors")) {
                errors = objJson.getJSONArray("errors").toString();
            }

            DaoAuxiliar.retornarAcessoControle(key).getClienteDao().removerFreePass(clienteGympass);
            gravaLogGOGOOD(key, clienteGympass, unique_token, ERRO_GOGOOD_ACESS_CONTROL_VALIDATE, metadata+" - "+errors);
        }  catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
        }
    }

    private void liberarFreePass(String key, Date hoje, Date dataTimezone,
                                 String userToken,
                                 ClienteVO cliente, Integer empresa, String objJson) {
        try {
            cliente.setResponsavelFreePass(0);
            cliente.setFreePass(DaoAuxiliar.retornarAcessoControle(key).getProdutoDao().criarOuConsultarProdutoGoGood());
            PeriodoAcessoClienteVO periodoAcessoClienteVO;
            if (Calendario.diferencaEmDias(dataTimezone,hoje) > 1 || Calendario.diferencaEmDias(dataTimezone,hoje) < -1){
                periodoAcessoClienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().incluirFreePass(cliente, Calendario.hoje(), null, null, userToken);
            }else{
                periodoAcessoClienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().incluirFreePass(cliente, dataTimezone, null, null, userToken);
            }
            DaoAuxiliar.retornarAcessoControle(key).getSituacaoClienteSinteticoDWDao().atualizarBaseOffLineZillyonAcesso(key, cliente.getPessoa().getCodigo());
            gravaLogGOGOOD(key, cliente, userToken, GOGOOD_INCLUSAO, "");

            if(periodoAcessoClienteVO != null){
                DaoAuxiliar.retornarAcessoControle(key).getPeriodoAcessoDao().gravarInfoCheckinGoGood(cliente,
                        new EmpresaVO(empresa),
                        cliente.getTokenGoGood(),
                        periodoAcessoClienteVO,
                        IntegracoesEnum.GOGOOD,
                        objJson
                );
            }
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
        }
    }

    private void gravaLogGOGOOD(String key, ClienteVO cliente, String unique_token, String operacao, String retorno){
        try {
            LogVO obj = new LogVO();
            if (operacao.equals(GOGOOD_JSON_WEBHOOK)){
                obj.setChavePrimaria("JSON DO WEBHOOK" );
            }else{
                obj.setChavePrimaria(cliente.getPessoa() == null ? "CLIENTE NÃO ENCONTRADO" : cliente.getPessoa().getCodigo().toString());
            }
            obj.setNomeEntidade("GOGOOD");
            obj.setNomeEntidadeDescricao("Cliente - GoGood");
            obj.setOperacao(operacao);
            obj.setNomeCampo("GOGOOD-Cliente");
            obj.setValorCampoAlterado("TOKEN GOGOOD: " + unique_token + " RETORNO: " + retorno);
            obj.setDataAlteracao(Calendario.hoje());
            if (cliente.getPessoa() == null){
                obj.setPessoa(0);
            }else{
                obj.setPessoa(cliente.getPessoa().getCodigo());
            }
            DaoAuxiliar.retornarAcessoControle(key).getLogDao().incluirSemCommit(obj);
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }
}
