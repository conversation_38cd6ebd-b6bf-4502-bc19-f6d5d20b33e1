package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Athos Feitosa",
        data = "07/06/2025",
        descricao = "Adicionar coluna configtodabasealunos na tabela funil",
        motivacao = "Necessrio para definir se funil est configurado para olhar toda a base de alunos QUANDO o sistema executa o gatilho para a fase inicial"
)
public class AtualizacaoAddColunaConfigToDaBaseAlunos implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE funil ADD COLUMN configtodabasealunos BOOLEAN DEFAULT FALSE;", c
            );
        }
    }
}
