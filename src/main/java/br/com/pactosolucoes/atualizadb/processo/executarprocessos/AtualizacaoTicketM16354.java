package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "31/07/2025",
        descricao = "Corrigido compras com erro de autorização sem a quantidade de produtos",
        motivacao = "M1-6354")
public class AtualizacaoTicketM16354 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update compra set autorizada = null\n" +
                    "where autorizada = true and valortotal > 0.0\n" +
                    "and 0 = (select sum(COALESCE(quantidade, 0)) from compraitens ci where ci.compra = compra.codigo)\n" +
                    "and 0 < (select sum(COALESCE(quantidadeautorizada, 0)) from compraitens ci where ci.compra = compra.codigo);", c);
        }
    }

}
