package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.*;

public class ProcessoCorrigirOperadorasCartaoLive {

    private final Connection conOAMD;
    private final boolean simular;

    public ProcessoCorrigirOperadorasCartaoLive(boolean simular, String password) throws Exception {
        this.simular = simular;
        this.conOAMD = DriverManager.getConnection(Conexao.getInstance().getUrlOAMD(), Conexao.getInstance().getUsernameBD(), password);
    }

    public static void main(String[] args) throws Exception {

        Integer idRedeEmpresa = 0; //Não sei Id Rede Empresa Live
        boolean simular = false;
        String passwordBD = "";
        String[] chaves = new String[]{}; // OPCIONAL

        ProcessoCorrigirOperadorasCartaoLive processo = new ProcessoCorrigirOperadorasCartaoLive(simular, passwordBD);
        processo.corrigirOperadorasCartaoTodasUnidadesDaRede(idRedeEmpresa, chaves);

    }

    private void corrigirOperadorasCartaoTodasUnidadesDaRede(Integer idRedeEmpresa, String[] chaves) throws Exception {
        if (UteisValidacao.emptyNumber(idRedeEmpresa)) {
            throw new Exception("O id rede empresa não foi informado!");
        }
        String sqlEmpresas = "select e.* from empresa e \n" +
                " inner join empresafinanceiro ef on ef.chavezw = e.chave \n" +
                "where ef.redeempresa_id = " + idRedeEmpresa + " \n";
        if (!UteisValidacao.emptyArray(chaves)) {
            sqlEmpresas += " and e.chave in ('" +  String.join("','", chaves) + "') \n";
        }

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sqlEmpresas + ") as sql", conOAMD);

        if (total == 0) {
            throw new Exception("Nenhuma empresa encontrada para o redeEmpresaId" + idRedeEmpresa);
        }

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmpresas, conOAMD)) {
            while (rs.next()) {
                String host = rs.getString("hostBD");
                String porta = rs.getString("porta");
                String nomeBD = rs.getString("nomeBD");
                String user = rs.getString("userBD");
                String passWord = rs.getString("passwordBD");

                try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + nomeBD, user, passWord)) {
                    Conexao.guardarConexaoForJ2SE(con);
                    try (ResultSet rsEmpresa = SuperFacadeJDBC.criarConsulta("select codigo from empresa where ativa is true", con)) {
                        while (rsEmpresa.next()) {
                            ajusteDescricaoMastercardDebito(con);
                            ajusteDescricaoAmericanExpress(con);
                            ajusteOperadorasStoneConnect(con);
                            ajusteOperadorasStoneV5(con);
                            ajusteOperadorasCielo(con);
                            ajusteOperadorasSemPadrao(con);
                        }
                    }
                }
            }
        }
    }

    // AJUSTE DESCRICAO MASTERCARD DEBITO
    private void ajusteDescricaoMastercardDebito(Connection con) throws Exception {
        try {
            if (simular) {
                con.setAutoCommit(false);
            }

            String updateCR = "UPDATE operadoracartao SET descricao = 'MASTERCARD (DEBITO)' WHERE descricao = 'MAESTRO (DEBITO)';";
            try (Statement stCR = con.createStatement()) {
                stCR.execute(updateCR);
            }

            if (simular) {
                con.rollback();
            }
        } catch (Exception e) {
            if (simular && !con.getAutoCommit()) {
                try {
                    con.rollback();
                } catch (Exception rollbackEx) {
                    Uteis.logarDebug("Falha ao fazer rollback: " + rollbackEx.getMessage());
                }
            }
            e.printStackTrace();
        } finally {
            if (simular) {
                con.setAutoCommit(true);
            }
        }
    }

    // AJUSTE DESCRICAO AMEX
    private void ajusteDescricaoAmericanExpress(Connection con) throws Exception {
        Statement stConsulta = null;
        ResultSet rs = null;
        Statement stUpdate = null;

        try {
            if (simular) {
                con.setAutoCommit(false);
            }

            Uteis.logarDebug("INÍCIO | Ajustando descrições AMEX para AMERICAN EXPRESS");

            // Buscar todas as operadoras que contêm 'AMEX' na descrição
            String sqlConsulta = "SELECT codigo, descricao FROM operadoracartao WHERE descricao ILIKE '%AMEX%'";
            stConsulta = con.createStatement();
            rs = stConsulta.executeQuery(sqlConsulta);

            int registrosAtualizados = 0;
            stUpdate = con.createStatement();

            while (rs.next()) {
                Integer codigo = rs.getInt("codigo");
                String descricaoAtual = rs.getString("descricao");

                // Substituir 'AMEX' por 'AMERICAN EXPRESS' (case insensitive)
                String novaDescricao = descricaoAtual.replaceAll("(?i)AMEX", "AMERICAN EXPRESS");

                // Fazer o update apenas se a descrição mudou
                if (!descricaoAtual.equals(novaDescricao)) {
                    String sqlUpdate = "UPDATE operadoracartao SET descricao = '" + novaDescricao + "' WHERE codigo = " + codigo;
                    stUpdate.execute(sqlUpdate);
                    registrosAtualizados++;

                    Uteis.logarDebug("Operadora " + codigo + " atualizada: '" + descricaoAtual + "' -> '" + novaDescricao + "'");
                }
            }

            Uteis.logarDebug("FIM | " + registrosAtualizados + " operadoras atualizadas de AMEX para AMERICAN EXPRESS");

            if (simular) {
                con.rollback();
                Uteis.logarDebug("SIMULAÇÃO | Rollback executado - alterações não foram persistidas");
            }
        } catch (Exception e) {
            if (simular && !con.getAutoCommit()) {
                try {
                    con.rollback();
                } catch (Exception rollbackEx) {
                    Uteis.logarDebug("Falha ao fazer rollback: " + rollbackEx.getMessage());
                }
            }
            e.printStackTrace();
            Uteis.logarDebug("Falha ao ajustar descrições AMEX! " + e.getMessage());
        } finally {
            try {
                if (rs != null) rs.close();
                if (stConsulta != null) stConsulta.close();
                if (stUpdate != null) stUpdate.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            if (simular) {
                con.setAutoCommit(true);
            }
        }
    }

    // AJUSTE OPERADORAS STONE CONNECT
    private void ajusteOperadorasStoneConnect(Connection con) {
        processarAjusteOperadoras(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT, con);
    }

    // AJUSTE OPERADORAS STONE V5
    private void ajusteOperadorasStoneV5(Connection con) {
        processarAjusteOperadoras(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5, con);
    }

    // AJUSTE OPERADORAS CIELO
    private void ajusteOperadorasCielo(Connection con) {
        processarAjusteOperadoras(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE, con);
    }

    // AJUSTE OPERADORAS SEM PADRÃO
    private void ajusteOperadorasSemPadrao(Connection con) {
        processarAjusteOperadoras(TipoConvenioCobrancaEnum.NENHUM, con);
    }

    // METODO GENÉRICO PARA PROCESSAR AJUSTE DE OPERADORAS
    private void processarAjusteOperadoras(TipoConvenioCobrancaEnum processoEmExecucao, Connection connection) {
        Statement stm = null;
        ResultSet rs = null;
        PreparedStatement psConsulta = null;
        PreparedStatement psUpdate = null;
        PreparedStatement psDelete = null;
        try {
            // SQL para buscar todas as operadoras criadas indevidamente
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT codigo, descricao, credito FROM operadoracartao ");
            if (processoEmExecucao.equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
                sql.append("WHERE descricao LIKE '%STONE CONNECT%'");
            } else if (processoEmExecucao.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
                sql.append("WHERE descricao LIKE '%STONE ONLINE V5%'");
            } else if (processoEmExecucao.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                sql.append("WHERE descricao LIKE '%CIELO%'");
            } else if (processoEmExecucao.equals(TipoConvenioCobrancaEnum.NENHUM)) {
                sql.append("WHERE (descricao NOT LIKE '% %' OR descricao = 'AMERICAN EXPRESS') AND descricao != 'OUTRASBANDEIRAS'");
            }

            stm = connection.createStatement();
            rs = stm.executeQuery(sql.toString());

            while (rs.next()) {
                Integer codigoOperadoraIndevida = rs.getInt("codigo");
                String descricaoCompleta = rs.getString("descricao");
                boolean credito = rs.getBoolean("credito");

                ResultSet rsConsulta = null;
                try {
                    // Extrair a primeira palavra da descrição (VISA, MASTERCARD, DINNERS, etc.)
                    String primeiraPalavra = descricaoCompleta.split(" ")[0].trim().toUpperCase();

                    // Ajuste especial para AMERICAN EXPRESS
                    if ("AMERICAN".equals(primeiraPalavra)) {
                        primeiraPalavra = "AMERICAN EXPRESS";
                    }

                    // Criar descrição padrão baseada no tipo (crédito/débito)
                    String descricaoPadrao = primeiraPalavra + " (" + (credito ? "CRÉDITO" : "DÉBITO") + ")";

                    // Primeira tentativa: buscar operadora padrão equivalente (com acento)
                    String sqlConsulta = "SELECT codigo FROM operadoracartao WHERE ativo = true AND descricao ilike ? AND credito = ?";
                    psConsulta = connection.prepareStatement(sqlConsulta);
                    psConsulta.setString(1, "%" + descricaoPadrao + "%");
                    psConsulta.setBoolean(2, credito);

                    rsConsulta = psConsulta.executeQuery();
                    boolean encontrouOperadora = rsConsulta.next();

                    // Se não encontrou com acento, tentar sem acento
                    if (!encontrouOperadora) {
                        rsConsulta.close();
                        psConsulta.close();

                        String descricaoPadraoSemAcento = primeiraPalavra + " (" + (credito ? "CREDITO" : "DEBITO") + ")";
                        psConsulta = connection.prepareStatement(sqlConsulta);
                        psConsulta.setString(1, "%" + descricaoPadraoSemAcento + "%");
                        psConsulta.setBoolean(2, credito);
                        rsConsulta = psConsulta.executeQuery();
                        encontrouOperadora = rsConsulta.next();
                    }

                    if (encontrouOperadora) {
                        Integer codigoOperadoraPadrao = rsConsulta.getInt("codigo");

                        // Atualizar MovPagamento para usar a operadora padrão
                        String sqlUpdate = "UPDATE movpagamento SET operadoracartao = ? WHERE operadoracartao = ?";
                        psUpdate = connection.prepareStatement(sqlUpdate);
                        psUpdate.setInt(1, codigoOperadoraPadrao);
                        psUpdate.setInt(2, codigoOperadoraIndevida);
                        psUpdate.executeUpdate();

                        // Atualizar CartaoCredito para usar a operadora padrão
                        String sqlUpdateCartao = "UPDATE cartaocredito SET operadoracartao = ? WHERE operadoracartao = ?";
                        PreparedStatement psUpdateCartao = connection.prepareStatement(sqlUpdateCartao);
                        psUpdateCartao.setInt(1, codigoOperadoraPadrao);
                        psUpdateCartao.setInt(2, codigoOperadoraIndevida);
                        psUpdateCartao.executeUpdate();
                        psUpdateCartao.close();

                    }

                    // Excluir a operadora indevida (sempre, independente de ter encontrado padrão ou não)
                    String sqlDelete = "DELETE FROM operadoracartao WHERE codigo = ?";
                    psDelete = connection.prepareStatement(sqlDelete);
                    psDelete.setInt(1, codigoOperadoraIndevida);
                    psDelete.executeUpdate();

                } catch (Exception ex) {
                    ex.printStackTrace();
                    try {
                        if (rsConsulta != null) rsConsulta.close();
                    } catch (Exception e) { /* ignore */ }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (rs != null) rs.close();
                if (stm != null) stm.close();
                if (psConsulta != null) psConsulta.close();
                if (psUpdate != null) psUpdate.close();
                if (psDelete != null) psDelete.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

}
