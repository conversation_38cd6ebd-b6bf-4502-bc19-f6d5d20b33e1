package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.operacoes.midias.awss3.AmazonS3Client;
import servicos.operacoes.midias.awss3.MigracaoFotosS3ParaS3Service;

import java.sql.*;
import java.util.*;
import java.util.Date;

public class ImportarAtividadeEFichasDeUmaEmpresa {


    public static void main(String[] args) throws Exception {
        System.out.println("Início em : " + new Date());
        importarAtividadesFichasPredefinidas();
        System.out.println("Fim em : " + new Date());
    }

    private static void importarAtividadesFichasPredefinidas() throws Exception {
        Connection conBase = DriverManager.getConnection("******************************************************************************", "zillyonweb", "pactodb");
        Connection conDestino = DriverManager.getConnection("********************************************************************", "zillyonweb", "pactodb");
        String chaveBase ="442c3f0cf4adcba347aa73d42785bcc8";
        String chaveDestino ="pratique7";
        conDestino.setAutoCommit(false);
        try {
            importarAtividades(conBase, conDestino,chaveBase,chaveDestino);
            importarFichasPredefinidas(conBase, conDestino);
            importarProgramasPredefinidos(conBase, conDestino,chaveBase,chaveDestino);
            conDestino.commit();
        }catch (Exception e){
            conDestino.rollback();
            System.out.println("Erro: "+e.getMessage());
        } finally {
            conDestino.setAutoCommit(true);
        }
    }

    private static void ajustarFotosAtividadesImportadas(Connection conBase, Connection conDestino,String chaveBase, String chaveDestino) throws Exception {
        AmazonS3Client s3 = new AmazonS3Client();

        String sql = "select\n" +
                "\ta.codigo,\n" +
                "\ta.nome,\n" +
                "\tata.codigo as atividadeanimacao,\n" +
                "\tata.fotokey,\n" +
                "\tata.fotokeyminiatura,\n" +
                "\tata.fotokeypequena \n" +
                "from atividade a\n" +
                "\tinner join atividadeanimacao ata on ata.atividade_codigo = a.codigo \n" +
                "where 1 = 1 \n" +
                "and coalesce(ata.fotokey,'') <> '' \n";

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conBase);
        int atual = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conBase);
        while (rs.next()) {

            String nome = rs.getString("nome");
            String fotokey = rs.getString("fotokey");
            String fotokeyminiatura = rs.getString("fotokeyminiatura");
            String fotokeypequena = rs.getString("fotokeypequena");

            System.out.printf("%d\\%d - Ajustando fotokey atividade: %s \n", ++atual, total, nome);

            String sqlAtividadeDestino = sql + " and trim(upper(a.nome)) = trim(upper('" + nome.replace("'", "''") + "')) \n";
            ResultSet rsAtividadeDestino = SuperFacadeJDBC.criarConsulta(sqlAtividadeDestino, conDestino);
            if (rsAtividadeDestino.next()) {

                if (UteisValidacao.emptyString(fotokey) || UteisValidacao.emptyString(fotokeypequena) || UteisValidacao.emptyString(fotokeyminiatura)) {
                    continue;
                }
                s3.copy(fotokey, fotokey.replace(chaveBase, chaveDestino));
                s3.copy(fotokeypequena, fotokeypequena.replace(chaveBase, chaveDestino));
                s3.copy(fotokeyminiatura, fotokeyminiatura.replace(chaveBase, chaveDestino));

                String sqlUpdate = "update atividadeanimacao set fotokey = ?, fotokeyminiatura = ?, fotokeypequena = ? where codigo = ?";
                PreparedStatement pstm = conDestino.prepareStatement(sqlUpdate);
                pstm.setString(1, fotokey.replace(chaveBase, chaveDestino));
                pstm.setString(2, fotokeyminiatura.replace(chaveBase, chaveDestino));
                pstm.setString(3, fotokeypequena.replace(chaveBase, chaveDestino));
                pstm.setInt(4, rsAtividadeDestino.getInt("atividadeanimacao"));
                pstm.execute();
            }
        }
    }

    private static void importarAtividades(Connection conBase, Connection conDestino,String chaveBase, String chaveDestino) throws Exception {
        Map<String, Integer> mapaGrupoMusculares = obterCadastroBasico(conDestino, "grupomuscular", "codigo", "nome");
        Map<String, Integer> mapaAtividadeVideo = obterCadastroBasico(conDestino, "atividadevideo", "codigo", "linkvideo");
        Map<String, Integer> mapaMusculos = obterCadastroBasico(conDestino, "musculo", "codigo", "nome");
        Map<String, Integer> mapaCategoriaAtividade = obterCadastroBasico(conDestino, "categoriaatividade", "codigo", "nome");
        Map<String, Integer> mapaAparelhos = obterCadastroBasico(conDestino, "aparelho", "codigo", "nome");
        Map<String, Integer> mapaAtividades = obterCadastroBasico(conDestino, "atividade", "codigo", "nome");
        Map<String, Integer> mapaAtividadesAnimacao = obterCadastroBasico(conDestino, "atividadeanimacao", "codigo", "fotokey");
        Map<String, Integer> mapaNiveis = obterCadastroBasico(conDestino, "nivel", "codigo", "nome");

        String sql = "select  at.*, array_agg(ap.nome) as aparelhos, array_agg(cat.nome) as categorias,array_agg(mus.nome) as musculos, array_agg(gru.nome) as gruposmusculares,array_agg(nivel.nome) as niveis, " +
                "array_agg(atani.fotokey || ','||atani.fotokeyminiatura || ','||atani.fotokeypequena) as atividadeanimacoes, " +
                "array_agg(atv.linkvideo) as atividadevideo from atividade at "+
                "left join atividadeaparelho aap on aap.atividade_codigo = at.codigo left join aparelho ap on ap.codigo = aap.aparelho_codigo "+
                "left join atividadecategoriaatividade acat on acat.atividade_codigo = at.codigo left join categoriaatividade cat on cat.codigo = acat.categoriaatividade_codigo "+
                "left join atividademusculo amus on amus.atividade_codigo = at.codigo left join musculo mus on mus.codigo = amus.musculo_codigo "+
                "left join atividadegrupomuscular agru on agru.atividade_codigo = at.codigo left join grupomuscular gru on gru.codigo = agru.grupomuscular_codigo "+
                "left join atividadenivel anivel on anivel.atividade_codigo = at.codigo left join nivel nivel on nivel.codigo = anivel.nivel_codigo "+
                "left join atividadeanimacao atani on atani.atividade_codigo = at.codigo "+
                "left join atividadevideo atv on atv.atividade_codigo = at.codigo "+
                "group by 1,2,3,4,5,6,7,8,9,10,11,12";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conBase);

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conBase);
        int atual = 0;

        while(rs.next()){
            try {

                String descricaoAtividade = rs.getString("nome");
                System.out.printf("%d\\%d - Importando atividade %s\n", ++atual, total, descricaoAtividade);
                if (mapaAtividades.containsKey(descricaoAtividade)) {
                    System.out.printf("Atividade %s já existe, não será importada novamente.\n", descricaoAtividade);

                    System.out.printf("Vou importar apenas a tabela atividadevideo.\n", descricaoAtividade);
                    String[] atividadeVideos = rs.getArray("atividadevideo") == null ? new String[0] : (String[]) rs.getArray("atividadevideo").getArray();
                    inserirRelacionamentosAtividade(conDestino, mapaAtividades.get(descricaoAtividade), atividadeVideos, mapaAtividadeVideo, "atividadevideo", "linkvideo", "atividadevideo", "atividade_codigo", "codigo", "", "", chaveBase, chaveDestino);
                    continue;
                }
                Integer codigoNovo = inserirAtividade(conDestino, rs);
                mapaAtividades.put(descricaoAtividade, codigoNovo);

                String[] categorias = (String[]) rs.getArray("categorias").getArray();
                inserirRelacionamentosAtividade(conDestino, codigoNovo, categorias, mapaCategoriaAtividade, "categoriaatividade", "nome", "atividadecategoriaatividade", "atividade_codigo", "categoriaatividade_codigo", "", "", chaveBase, chaveDestino);

                String[] aparelhos = (String[]) rs.getArray("aparelhos").getArray();
                inserirRelacionamentosAtividade(conDestino, codigoNovo, aparelhos, mapaAparelhos, "aparelho", "nome", "atividadeaparelho", "atividade_codigo", "aparelho_codigo", ",crossfit", ", false", chaveBase, chaveDestino);

                String[] musculos = (String[]) rs.getArray("musculos").getArray();
                inserirRelacionamentosAtividade(conDestino, codigoNovo, musculos, mapaMusculos, "musculo", "nome", "atividademusculo", "atividade_codigo", "musculo_codigo", "", "", chaveBase, chaveDestino);

                String[] gruposmusculates = (String[]) rs.getArray("gruposmusculares").getArray();
                inserirRelacionamentosAtividade(conDestino, codigoNovo, gruposmusculates, mapaGrupoMusculares, "grupomuscular", "nome", "atividadegrupomuscular", "atividade_codigo", "grupomuscular_codigo", "", "", chaveBase, chaveDestino);

                String[] niveisatividade = (String[]) rs.getArray("niveis").getArray();
                inserirRelacionamentosAtividade(conDestino, codigoNovo, niveisatividade, mapaNiveis, "nivel", "nome", "atividadenivel", "atividade_codigo", "nivel_codigo", ",ordem", ",0", chaveBase, chaveDestino);

                String[] atividadesanimacao = (String[]) rs.getArray("atividadeanimacoes").getArray() == null ? new String[1] : (String[]) rs.getArray("atividadeanimacoes").getArray();
                inserirRelacionamentosAtividade(conDestino, codigoNovo, atividadesanimacao, mapaAtividadesAnimacao, "atividadeanimacao", "fotokey", "atividadeanimacao", "atividade_codigo", "", ",ordem", ",0", chaveBase, chaveDestino);

                String[] atividadeVideos = (String[]) rs.getArray("atividadevideo").getArray();
                inserirRelacionamentosAtividade(conDestino, mapaAtividades.get(descricaoAtividade), atividadeVideos, mapaAtividadeVideo, "atividadevideo", "linkvideo", "atividadevideo", "atividade_codigo", "codigo", "", "", chaveBase, chaveDestino);

            } catch (Exception e) {
                throw e;
            }
        }
    }

    private static void inserirRelacionamentosAtividade(Connection conDestino, Integer codigoNovo, String[] registros, Map<String, Integer> mapaCadastrados, String entidade, String colunaentidade, String entidadeRelacionamento, String colunaObjeto,
                                                        String colunaRegistro, String colunaComplemento, String valoresComplemento,String chave, String chaveDestino) throws Exception {
        List<String> jaAssociadas = new ArrayList<String>();
        AmazonS3Client s3 = new AmazonS3Client();

        for (String registro : registros){
            if(!UteisValidacao.emptyString(registro) && !registro.equals("NULL") && !jaAssociadas.contains(registro)){
                int codigoRegistro = 0;
                if(mapaCadastrados.containsKey(registro)){
                    codigoRegistro = mapaCadastrados.get(registro);
                } else {
                    if (entidade.equals("atividadeanimacao")){
                        String[] colunas = registro.split(",");
                        if (colunas != null && colunas.length > 0) {
                            String fotokey = colunas[0];
                            String fotokeyminiatura = colunas[1];
                            String fotokeypequena = colunas[2];

                            if (!UteisValidacao.emptyString(fotokey)) {
                                s3.copy(fotokey, fotokey.replace(chave, chaveDestino));
                            }
                            if (!UteisValidacao.emptyString(fotokeypequena)) {
                                s3.copy(fotokeypequena, fotokeypequena.replace(chave, chaveDestino));
                            }
                            if (!UteisValidacao.emptyString(fotokeyminiatura)) {
                                s3.copy(fotokeyminiatura, fotokeyminiatura.replace(chave, chaveDestino));
                            }

                            String sql = "insert into " + entidadeRelacionamento + "(fotokey,fotokeyminiatura,fotokeypequena," + colunaObjeto + ") values " +
                                    "('" + fotokey.replace(chave, chaveDestino) + "','" + fotokeyminiatura.replace(chave, chaveDestino) + "','" + fotokeypequena.replace(chave, chaveDestino) + "'," + codigoNovo + ") RETURNING codigo";
                            PreparedStatement sqlInserir = conDestino.prepareStatement(sql);
                            ResultSet rsNovo = sqlInserir.executeQuery();
                            rsNovo.next();
                            codigoRegistro = rsNovo.getInt(1);
                        }
                    }else {
                        String sql = "insert into " + entidade + " (" + colunaentidade + colunaComplemento + ") values (?" + valoresComplemento + ") RETURNING codigo";
                        PreparedStatement sqlInserir = conDestino.prepareStatement(sql);
                        int i = 1;
                        sqlInserir.setString(i++, registro);

                        ResultSet rsNovo = sqlInserir.executeQuery();
                        rsNovo.next();
                        codigoRegistro = rsNovo.getInt(1);
                        mapaCadastrados.put(registro, codigoRegistro);
                    }
                }
                if (!entidade.equals("atividadeanimacao") && !entidade.equals("atividadevideo")) {
                    SuperFacadeJDBC.executarConsulta("insert into " + entidadeRelacionamento + "(" + colunaObjeto + "," + colunaRegistro + ") values (" + codigoNovo + "," + codigoRegistro + ")", conDestino);
                }
                if (entidade.equals("atividadevideo")) {
                    SuperFacadeJDBC.executarConsulta("update " + entidadeRelacionamento + " set " + colunaObjeto + "=" + codigoNovo + " where " + colunaRegistro + "=" + codigoRegistro, conDestino);
                }
                jaAssociadas.add(registro);
            }
        }
    }

    private static Integer inserirAtividade(Connection conDestino, ResultSet rs) throws Exception {
        String sql = "INSERT INTO atividade(ativo, descricao, nome, seriesapenasduracao, tipo, todasempresas,versao, crossfit, linkvideo, categoriaatividadewod, unidademedida)\n" +
                "    VALUES (?,?,?,?,?,?,?,?,?,?,?) RETURNING codigo";
        PreparedStatement sqlInserir = conDestino.prepareStatement(sql);
        int i  = 1;
        sqlInserir.setBoolean(i++,  rs.getBoolean("ativo"));
        sqlInserir.setString(i++, rs.getString("descricao"));
        sqlInserir.setString(i++, rs.getString("nome"));
        sqlInserir.setBoolean(i++,  rs.getBoolean("seriesapenasduracao"));
        if(rs.getString("tipo") == null){
            sqlInserir.setNull(i++, 0);
        } else {
            sqlInserir.setInt(i++, rs.getInt("tipo"));
        }
        sqlInserir.setBoolean(i++,  rs.getBoolean("todasempresas"));
        if(rs.getString("versao") == null){
            sqlInserir.setNull(i++, 0);
        } else {
            sqlInserir.setInt(i++, rs.getInt("versao"));
        }
        sqlInserir.setBoolean(i++,  rs.getBoolean("crossfit"));
        sqlInserir.setString(i++, rs.getString("linkvideo"));
        if(rs.getString("categoriaatividadewod") == null){
            sqlInserir.setNull(i++, 0);
        } else {
            sqlInserir.setInt(i++, rs.getInt("categoriaatividadewod"));
        }
        if(rs.getString("unidademedida") == null){
            sqlInserir.setNull(i++, 0);
        } else {
            sqlInserir.setInt(i++, rs.getInt("unidademedida"));
        }

        ResultSet rsCodigo = sqlInserir.executeQuery();
        rsCodigo.next();
        return rsCodigo.getInt(1);
    }

    private static Map<String, Integer> obterCadastroBasico(Connection conDestino, String tabela, String colunaCodigo, String colunaDecricao) throws Exception {
        Map<String, Integer> mapa = new HashMap<String, Integer>();
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select "+colunaCodigo+","+colunaDecricao+" from "+tabela, conDestino);
        while(rs.next()){
            mapa.put(rs.getString(colunaDecricao), rs.getInt(colunaCodigo));
        }
        return mapa;
    }

    private static void importarFichasPredefinidas(Connection conBase, Connection conDestino) throws Exception {
        Map<Integer, Integer> mapaFichasImportadas = new HashMap<Integer, Integer>();
        Map<Integer, Integer> mapaAtividadesFicha = new HashMap<Integer, Integer>();
        Map<String, Integer> mapaNiveis = obterCadastroBasico(conDestino, "nivel", "codigo", "nome");
        Map<String, Integer> mapaCategoriaFicha = obterCadastroBasico(conDestino, "categoriaficha", "codigo", "nome");
        Map<String, Integer> mapaAtividades = obterCadastroBasico(conDestino, "atividade", "codigo", "nome");

        String sql = "select fc.codigo as codigofi, fc.ativo as ativoficha, fc.mensagemaluno as mensagemalunoficha, fc.nome as nomeficha, usarcomopredefinida, acat.nome as categoriaficha,fc.versao as versaoficha, aniv.nome as nivelnome,\n" +
                " af.codigo as codigo_atividadeficha, af.metodoexecucao as metodoexecucao_atividadeficha, af.nome as nome_atividadeficha, af.ordem as ordem_atividadeficha, af.versao as versao_atividadeficha,\n" +
                "            af.ficha_codigo as ficha_atividadeficha, af.nomeatividadealteradomanualmente as nomeatividadealteradomanualmente_atividadeficha, af.descanso as descanso_atividadeficha, af.setid as setid_atividadeficha, \n" +
                "            af.intensidade as intensidade_atividadeficha, af.complementonomeatividade as complementonomeatividade_atividadeficha, ati.nome as nomeatividade, \n" +
                "             ser.codigo as codigoserie, ser.cadencia as cadenciaserie, ser.carga as cargaserie, ser.cargacomp as cargacompserie, ser.complemento as complementoserie, \n" +
                "            ser.descanso as descansoserie, ser.distancia as distanciaserie, ser.duracao as duracaoserie, ser.ordem as ordemserie, ser.repeticao as repeticaoserie, ser.repeticaocomp as repeticaocompserie, \n" +
                "            ser.velocidade as velocidadeserie\n" +
                " from ficha fc \n" +
                " inner join atividadeficha af on af.ficha_codigo = fc.codigo\n" +
                " left join serie ser on ser.atividadeficha_codigo = af.codigo\n" +
                " left join atividade ati on ati.codigo = af.atividade_codigo\n" +
                " left join categoriaficha acat on acat.codigo = fc.categoria_codigo\n" +
                " left join nivel aniv on aniv.codigo = fc.nivel_codigo\n" +
                "where usarcomopredefinida  order by fc.codigo,af.ordem, ser.ordem";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conBase);
        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conBase);
        int atual = 0;

        while(rs.next()){

            System.out.printf("%d\\%d - Importando ficha predefinida %s\n", ++atual, total, rs.getString("nomeficha"));

            if(!mapaFichasImportadas.containsKey(rs.getInt("codigofi"))){
                mapaFichasImportadas.put(rs.getInt("codigofi"),inserirFicha(conDestino, mapaNiveis, mapaCategoriaFicha,rs));
            }
            Integer codigoFichaNova = mapaFichasImportadas.get(rs.getInt("codigofi"));

            if(!mapaAtividadesFicha.containsKey(rs.getInt("codigo_atividadeficha"))){
                mapaAtividadesFicha.put(rs.getInt("codigo_atividadeficha"),inserirAtividadeFicha(conDestino, codigoFichaNova,mapaAtividades,rs));
            }
            Integer codigoAtividadeFichaNovo = mapaAtividadesFicha.get(rs.getInt("codigo_atividadeficha"));

            inserirSerie(conDestino,codigoAtividadeFichaNovo, rs);


        }
    }

    private static void inserirSerie(Connection conDestino, Integer codigoAtividadeFichaNovo, ResultSet rs) throws Exception {
        try {
            String sql = "INSERT INTO serie(cadencia, carga, cargacomp, complemento, descanso, distancia, duracao, ordem, repeticao, repeticaocomp, velocidade, atividadeficha_codigo)    VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";
            PreparedStatement sqlInserir = conDestino.prepareStatement(sql);
            int i = 1;
            sqlInserir.setString(i++, rs.getString("cadenciaserie"));
            if (rs.getString("cargaserie") == null) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setDouble(i++, rs.getDouble("cargaserie"));
            }
            sqlInserir.setString(i++, rs.getString("cargacompserie"));
            sqlInserir.setString(i++, rs.getString("complementoserie"));
            if (rs.getString("descansoserie") == null) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, rs.getInt("descansoserie"));
            }
            if (rs.getString("distanciaserie") == null) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, rs.getInt("distanciaserie"));
            }
            if (rs.getString("duracaoserie") == null) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, rs.getInt("duracaoserie"));
            }
            if (rs.getString("ordemserie") == null) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, rs.getInt("ordemserie"));
            }
            if (rs.getString("repeticaoserie") == null) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, rs.getInt("repeticaoserie"));
            }
            sqlInserir.setString(i++, rs.getString("repeticaocompserie"));
            if (rs.getString("velocidadeserie") == null) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setDouble(i++, rs.getDouble("velocidadeserie"));
            }
            sqlInserir.setInt(i++, codigoAtividadeFichaNovo);
            sqlInserir.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    private static Integer inserirAtividadeFicha(Connection conDestino, Integer codigoFichaNova, Map<String, Integer> mapaAtividades, ResultSet rs) throws Exception {
        try {
            String sql = "INSERT INTO atividadeficha(metodoexecucao, nome, ordem, versao, atividade_codigo,ficha_codigo, nomeatividadealteradomanualmente, descanso, setid,intensidade, complementonomeatividade) VALUES (?,?,?,?,?,?,?,?,?,?,?) RETURNING codigo";
            PreparedStatement sqlInserir = conDestino.prepareStatement(sql);
            int i  = 1;
            if(rs.getString("metodoexecucao_atividadeficha") == null){
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, rs.getInt("metodoexecucao_atividadeficha"));
            }
            sqlInserir.setString(i++, rs.getString("nome_atividadeficha"));
            if(rs.getString("ordem_atividadeficha") == null){
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, rs.getInt("ordem_atividadeficha"));
            }
            if(rs.getString("versao_atividadeficha") == null){
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, rs.getInt("versao_atividadeficha"));
            }
            sqlInserir.setInt(i++, mapaAtividades.get(rs.getString("nomeatividade")));
            sqlInserir.setInt(i++, codigoFichaNova);
            sqlInserir.setBoolean(i++,  rs.getBoolean("nomeatividadealteradomanualmente_atividadeficha"));
            sqlInserir.setBoolean(i++,  rs.getBoolean("descanso_atividadeficha"));
            sqlInserir.setString(i++, rs.getString("setid_atividadeficha"));
            if(rs.getString("intensidade_atividadeficha") == null){
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, rs.getInt("intensidade_atividadeficha"));
            }
            sqlInserir.setString(i++, rs.getString("complementonomeatividade_atividadeficha"));

            ResultSet rsCodigo = sqlInserir.executeQuery();
            rsCodigo.next();
            return rsCodigo.getInt(1);
        } catch (Exception e) {
            throw e;
        }
    }

    private static Integer inserirFicha(Connection conDestino, Map<String, Integer> mapaNiveis, Map<String, Integer> mapaCategoriaFicha,ResultSet rs) throws Exception {
        inserirEntidadeSimples(conDestino,rs.getString("categoriaficha"), mapaCategoriaFicha, "categoriaficha", "nome","","");
        inserirEntidadeSimples(conDestino,rs.getString("nivelnome"), mapaNiveis, "nivel", "nome",",ordem",",0");

        String sql = "INSERT INTO ficha(ativo, mensagemaluno, nome, usarcomopredefinida,versao, categoria_codigo, nivel_codigo) VALUES (?,?,?,?,?,?,?) RETURNING codigo";
        PreparedStatement sqlInserir = conDestino.prepareStatement(sql);
        int i  = 1;
        sqlInserir.setBoolean(i++,  rs.getBoolean("ativoficha"));
        sqlInserir.setString(i++, rs.getString("mensagemalunoficha"));
        sqlInserir.setString(i++, rs.getString("nomeficha"));
        sqlInserir.setBoolean(i++,  rs.getBoolean("usarcomopredefinida"));
        if(rs.getString("versaoficha") == null){
            sqlInserir.setNull(i++, 0);
        } else {
            sqlInserir.setInt(i++, rs.getInt("versaoficha"));
        }
        if(rs.getString("categoriaficha") == null && !mapaCategoriaFicha.containsKey(rs.getString("categoriaficha"))){
            sqlInserir.setNull(i++, 0);
        } else {
            sqlInserir.setInt(i++, mapaCategoriaFicha.get(rs.getString("categoriaficha")));
        }
        if(rs.getString("nivelnome") == null && !mapaNiveis.containsKey(rs.getString("nivelnome"))){
            sqlInserir.setNull(i++, 0);
        } else {
            sqlInserir.setInt(i++, mapaNiveis.get(rs.getString("nivelnome")));
        }
        ResultSet rsCodigo = sqlInserir.executeQuery();
        rsCodigo.next();
        return rsCodigo.getInt(1);
    }

    private static void inserirEntidadeSimples(Connection conDestino,String novoRegistro, Map<String, Integer> mapaCadastrados, String entidade, String colunaentidade, String colunaComplemento, String valoresComplemento) throws Exception {
        if(!UteisValidacao.emptyString(novoRegistro) && !mapaCadastrados.containsKey(novoRegistro)){
            String sql = "insert into "+entidade+" ("+colunaentidade + colunaComplemento+") values (?"+ valoresComplemento+") RETURNING codigo";
            PreparedStatement sqlInserir = conDestino.prepareStatement(sql);
            int i  = 1;
            sqlInserir.setString(i++, novoRegistro);

            ResultSet rsNovo = sqlInserir.executeQuery();
            rsNovo.next();
            int codigoRegistro = rsNovo.getInt(1);
            mapaCadastrados.put(novoRegistro,codigoRegistro);
        }
    }

    private static void importarProgramasPredefinidos(Connection conBase, Connection conDestino,String chaveBase, String chaveDestino) throws Exception {
        Map<Integer, Integer> mapaProgramasImportados = new HashMap<Integer, Integer>();
        Map<Integer, Integer> mapaFichasImportadas = new HashMap<Integer, Integer>();
        Map<Integer, Integer> mapaAtividadesFicha = new HashMap<Integer, Integer>();
        Map<String, Integer>  mapaNiveis = obterCadastroBasico(conDestino, "nivel", "codigo", "nome");
        Map<String, Integer>  mapaCategoriaFicha = obterCadastroBasico(conDestino, "categoriaficha", "codigo", "nome");
        Map<String, Integer>  mapaAtividades = obterCadastroBasico(conDestino, "atividade", "codigo", "nome");

        String sql = "select \n" +
                "pt.codigo as codigopt, pt.datalancamento as datalancamentopt, pt.diasporsemana as diasporsemanapt, pt.genero as generopt, pt.nome as nomept, pt.predefinido as predefinidopt, pt.situacao as situacaopt, pt.totalaulasprevistas as totalaulasprevistaspt, \n" +
                "ptf.tipoexecucao as tipoexecucaoptf,\n" +
                "fc.codigo as codigofi, fc.ativo as ativoficha, fc.mensagemaluno as mensagemalunoficha, fc.nome as nomeficha, usarcomopredefinida,fc.versao as versaoficha,\n" +
                "af.codigo as codigo_atividadeficha, af.metodoexecucao as metodoexecucao_atividadeficha, af.nome as nome_atividadeficha, af.ordem as ordem_atividadeficha, af.versao as versao_atividadeficha, af.ficha_codigo as ficha_atividadeficha, af.nomeatividadealteradomanualmente as nomeatividadealteradomanualmente_atividadeficha, af.descanso as descanso_atividadeficha, af.setid as setid_atividadeficha, af.intensidade as intensidade_atividadeficha, af.complementonomeatividade as complementonomeatividade_atividadeficha,\n" +
                "a.nome as nomeatividade,\n" +
                "acat.nome as categoriaficha,\n" +
                "aniv.nome as nivelnome,\n" +
                "ser.codigo as codigoserie, ser.cadencia as cadenciaserie, ser.carga as cargaserie, ser.cargacomp as cargacompserie, ser.complemento as complementoserie, ser.descanso as descansoserie, ser.distancia as distanciaserie, ser.duracao as duracaoserie, ser.ordem as ordemserie, ser.repeticao as repeticaoserie, ser.repeticaocomp as repeticaocompserie, ser.velocidade as velocidadeserie\n" +
                "from programatreino pt\n" +
                "inner join programatreinoficha ptf on ptf.programa_codigo = pt.codigo \n" +
                "inner join ficha fc on fc.codigo = ptf.ficha_codigo \n" +
                "left join categoriaficha acat on acat.codigo = fc.categoria_codigo\n" +
                "inner join atividadeficha af on af.ficha_codigo = fc.codigo \n" +
                "inner join atividade a on a.codigo = af.atividade_codigo \n" +
                "left join nivel aniv on aniv.codigo = fc.nivel_codigo \n" +
                "left join serie ser on ser.atividadeficha_codigo = af.codigo \n" +
                "where pt.predefinido is true \n" +
                "order by pt.codigo, fc.codigo, af.ordem, ser.ordem";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conBase);

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conBase);
        int atual = 0;

        while (rs.next()) {
            try {

                System.out.printf("%d\\%d - Importando programa predefinido %s\n", ++atual, total, rs.getString("nomept"));

                if (!mapaProgramasImportados.containsKey(rs.getInt("codigopt"))) {
                    mapaProgramasImportados.put(rs.getInt("codigopt"), inserirProgramaTreino(conDestino, rs));
                }
                Integer codigoProgramaNovo = mapaProgramasImportados.get(rs.getInt("codigopt"));

                if (!mapaFichasImportadas.containsKey(rs.getInt("codigofi"))) {
                    mapaFichasImportadas.put(rs.getInt("codigofi"), inserirFicha(conDestino, mapaNiveis, mapaCategoriaFicha, rs));
                    inserirProgramaTreinoFicha(conDestino, codigoProgramaNovo, mapaFichasImportadas.get(rs.getInt("codigofi")), rs);
                }
                Integer codigoFichaNova = mapaFichasImportadas.get(rs.getInt("codigofi"));

                if (!mapaAtividadesFicha.containsKey(rs.getInt("codigo_atividadeficha"))) {
                    mapaAtividadesFicha.put(rs.getInt("codigo_atividadeficha"), inserirAtividadeFicha(conDestino, codigoFichaNova, mapaAtividades, rs));
                }
                Integer codigoAtividadeFichaNovo = mapaAtividadesFicha.get(rs.getInt("codigo_atividadeficha"));

                inserirSerie(conDestino, codigoAtividadeFichaNovo, rs);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static Integer inserirProgramaTreino(Connection conDestino, ResultSet rs) throws Exception {
        ResultSet rsPactoBr = SuperFacadeJDBC.criarConsulta("select professor_codigo from usuario u where username ilike 'pactobr' limit 1;", conDestino);
        Integer codProfessorMontou = null;
        if (rsPactoBr.next()) {
            codProfessorMontou = rsPactoBr.getInt("professor_codigo");
        }

        String sql = "INSERT INTO programatreino(datalancamento, diasporsemana, genero, nome, predefinido, situacao, totalaulasprevistas, professormontou_codigo, versao) VALUES (?,?,?,?,?,?,?,?,?) RETURNING codigo";
        PreparedStatement sqlInserir = conDestino.prepareStatement(sql);
        int i  = 1;

        if (rs.getDate("datalancamentopt") == null) {
            sqlInserir.setDate(i++, (java.sql.Date) new Date());
        } else {
            sqlInserir.setDate(i++, rs.getDate("datalancamentopt"));
        }

        sqlInserir.setInt(i++, rs.getInt("diasporsemanapt"));
        sqlInserir.setString(i++, rs.getString("generopt"));
        sqlInserir.setString(i++, rs.getString("nomept"));
        sqlInserir.setBoolean(i++, rs.getBoolean("predefinidopt"));
        sqlInserir.setInt(i++, rs.getInt("situacaopt"));
        sqlInserir.setInt(i++, rs.getInt("totalaulasprevistaspt"));
        sqlInserir.setInt(i++, codProfessorMontou);
        sqlInserir.setInt(i++, 0);

        ResultSet rsCodigo = sqlInserir.executeQuery();
        rsCodigo.next();
        return rsCodigo.getInt(1);
    }

    private static Integer inserirProgramaTreinoFicha(Connection conDestino, Integer codProgramaNovo, Integer codFichaNova, ResultSet rs) throws Exception {
        String sql = "INSERT INTO programatreinoficha(tipoexecucao, versao, ficha_codigo, programa_codigo) VALUES (?,?,?,?) RETURNING codigo";
        PreparedStatement sqlInserir = conDestino.prepareStatement(sql);
        int i  = 1;

        sqlInserir.setInt(i++, rs.getInt("tipoexecucaoptf"));
        sqlInserir.setInt(i++, 0);
        sqlInserir.setInt(i++, codFichaNova);
        sqlInserir.setInt(i++, codProgramaNovo);

        ResultSet rsCodigo = sqlInserir.executeQuery();
        rsCodigo.next();
        return rsCodigo.getInt(1);
    }

}
