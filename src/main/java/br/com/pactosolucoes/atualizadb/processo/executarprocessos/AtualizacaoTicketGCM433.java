package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Athos Feitosa",
        data = "23/05/2025",
        descricao = "Criao da tabela automacaofasefunil",
        motivacao = "GCM-433: [Go to Dev] Backend dos gatilhos"
)
public class AtualizacaoTicketGCM433 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE automacaofasefunil (" +
                            "codigo serial4 NOT NULL, " +
                            "fase int4 NOT NULL, " +
                            "funil int4 NOT NULL, " +
                            "evento varchar(100) NOT NULL, " +
                            "eventojson text, " +
                            "datacriacao timestamp DEFAULT CURRENT_TIMESTAMP, " +
                            "ativo bool DEFAULT TRUE, " +
                            "descricao text, " +
                            "CONSTRAINT automacaofasefunil_pk PRIMARY KEY (codigo), " +
                            "CONSTRAINT fk_automacaofasefunil_fase FOREIGN KEY (fase) REFERENCES fasefunil(codigo), " +
                            "CONSTRAINT fk_automacaofasefunil_funil FOREIGN KEY (funil) REFERENCES funil(codigo)" +
                            ");", c
            );
        }
    }
}
