/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package acesso.webservice;

import acesso.webservice.retorno.ResultadoWSEnum;
import br.com.pactosolucoes.totalpass.ApiTotalPass;
import acesso.webservice.retorno.RetornoRequisicaoDadosOffline;
import acesso.webservice.retorno.RetornoRequisicaoValidarPermissaoUsuario;
import br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.contrato.servico.impl.ContratoAssinaturaDigitalServiceImpl;
import br.com.pactosolucoes.ecf.cupomfiscal.comuns.dao.CupomFiscal;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.sms.SMSControle;
import br.com.pactosolucoes.socialmailing.dao.SocialMail;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import negocio.comuns.acesso.*;
import negocio.comuns.acesso.auxiliar.PessoaAcesso;
import negocio.comuns.acesso.auxiliar.TransacaoAcesso;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoLiberacaoEnum;
import negocio.comuns.arquitetura.LogTotalPassVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.PessoaAnexoEnum;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.HorarioDisponibilidadeVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.OpcoesPerfilAcesso;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.acesso.AcessoColaborador;
import negocio.facade.jdbc.acesso.AutorizacaoAcessoGrupoEmpresarial;
import negocio.facade.jdbc.acesso.Coletor;
import negocio.facade.jdbc.acesso.DadosAcessoOffline;
import negocio.facade.jdbc.acesso.IntegracaoAcessoGrupoEmpresarial;
import negocio.facade.jdbc.acesso.LiberacaoAcesso;
import negocio.facade.jdbc.acesso.LocalAcesso;
import negocio.facade.jdbc.acesso.PessoaFotoLocalAcesso;
import negocio.facade.jdbc.acesso.ServidorFacial;
import negocio.facade.jdbc.acesso.ValidacaoLocalAcesso;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.LogApi;
import negocio.facade.jdbc.arquitetura.LogTotalPass;
import negocio.facade.jdbc.arquitetura.PerfilAcesso;
import negocio.facade.jdbc.arquitetura.Permissao;
import negocio.facade.jdbc.arquitetura.Risco;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioEmail;
import negocio.facade.jdbc.arquitetura.UsuarioPerfilAcesso;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.ConfigTotalPass;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.basico.webservice.IntegracaoCadastros;
import negocio.facade.jdbc.basico.webservice.IntegracaoImportacao;
import negocio.facade.jdbc.contrato.AfastamentoContratoDependente;
import negocio.facade.jdbc.contrato.Atestado;
import negocio.facade.jdbc.contrato.ComissaoGeralConfiguracao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoAssinaturaDigital;
import negocio.facade.jdbc.contrato.ContratoCondicaoPagamento;
import negocio.facade.jdbc.contrato.ContratoDuracao;
import negocio.facade.jdbc.contrato.ContratoDuracaoCreditoTreino;
import negocio.facade.jdbc.contrato.ContratoHorario;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.contrato.ContratoModalidadeHorarioTurma;
import negocio.facade.jdbc.contrato.ContratoModalidadeTurma;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.contrato.ConvenioDesconto;
import negocio.facade.jdbc.contrato.HistoricoContrato;
import negocio.facade.jdbc.contrato.JustificativaOperacao;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.contrato.UtilizacaoAvaliacaoFisica;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.facade.jdbc.crm.Evento;
import negocio.facade.jdbc.crm.Feriado;
import negocio.facade.jdbc.crm.GrupoColaborador;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.crm.Indicacao;
import negocio.facade.jdbc.crm.ModeloMensagem;
import negocio.facade.jdbc.crm.Objecao;
import negocio.facade.jdbc.crm.Passivo;
import negocio.facade.jdbc.crm.TextoPadrao;
import negocio.facade.jdbc.estoque.Balanco;
import negocio.facade.jdbc.estoque.Cardex;
import negocio.facade.jdbc.estoque.Compra;
import negocio.facade.jdbc.estoque.ProdutoEstoque;
import negocio.facade.jdbc.financeiro.Banco;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.BoletoPJBank;
import negocio.facade.jdbc.financeiro.ContaCorrente;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.DFSinteticoDetalhe;
import negocio.facade.jdbc.financeiro.DadosGame;
import negocio.facade.jdbc.financeiro.DadosGerencialPmg;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.GestaoNotas;
import negocio.facade.jdbc.financeiro.MetaFinanceiraEmpresa;
import negocio.facade.jdbc.financeiro.Monitoramento;
import negocio.facade.jdbc.financeiro.MovConta;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.NFSeEmitida;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.PinPad;
import negocio.facade.jdbc.financeiro.RecebivelAvulso;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.TipoRemessa;
import negocio.facade.jdbc.financeiro.TipoRetorno;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.gestao.GestaoICV;
import negocio.facade.jdbc.nfe.NotaFiscalDeServico;
import negocio.facade.jdbc.plano.*;
import negocio.facade.jdbc.utilitarias.NotificacaoUsuario;
import negocio.facade.jdbc.vendas.VendasConfig;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.json.JSONObject;
import relatorio.controle.crm.CarteirasRel;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import relatorio.negocio.jdbc.financeiro.TicketMedio;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.AppGestorAlunoService;
import servicos.AppGestorService;
import servicos.adm.CreditoDCCService;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.api.TokenService;
import servicos.impl.boleto.BoletoService;
import servicos.impl.conviteaulaexperimental.ConviteAulaExperimentalService;
import servicos.impl.microsservice.integracoes.ThreadEnvioRegistroAcesso;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.impl.IntegracaoLeadGenericaServiceImpl;
import servicos.integracao.impl.buzzlead.IntegracaoBuzzLeadServiceImpl;
import servicos.integracao.impl.rd.IntegracaoRDServiceImpl;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import webservice.controle.WebserviceControle;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class AcessoControle extends WebserviceControle {

    private static final Integer TAM_CODIGOACESSO = 10;
    private static final String AUTORIZADO_TOTALPASS = "204";
    private static final String BLOQUEADO_TOTALPASS_CHECKIN = "422";
    private static final Integer TAM_CODIGOACESSO1 = 11;
    private static final Integer TAM_CODIGOACESSOALTERNATIVO = 8;
    private final Map<Integer, TimeZone> timeZoneMap = new HashMap<>();
    private TransacaoAcesso rxTx;
    private List<ContratoVO> listaContratos = new ArrayList<>();
    private HorarioTurmaVO horarioTurmaVO = new HorarioTurmaVO();
    private boolean validacaoAcessoOffline = false;
    private StringBuilder horariosTurmasPermitidosParaOffline = new StringBuilder();
    private StringBuilder horariosPlanosPermitidosParaOffline = new StringBuilder();
    private boolean contratoComLimiteDeAcessoExcedido = false;
    private List<AcessoClienteVO> validacoesAcessoGympass = new ArrayList<>();
    private Map<String, String> ultimosAcessosRegistrados;

    public AcessoControle(String key) throws Exception {
        super(key);
        inicializardados();
    }

    public AcessoControle(Connection con) throws Exception {
        super(con);
        inicializardados();
    }

    private void inicializardados() throws Exception {
        rxTx = new TransacaoAcesso();
        setMovParcelaDao(new MovParcela(getCon()));
        setMovProdutoParcelaDao(new MovProdutoParcela(getCon()));
        setSocialMailingDao(new SocialMail(getCon()));
        setEmpresaDao(new Empresa(getCon()));
        setLocalAcessoDao(new LocalAcesso(getCon()));
        setServidorFacialDao(new ServidorFacial(getCon()));
        setPeriodoAcessoDao(new PeriodoAcessoCliente(getCon()));
        setClienteMsgDao(new ClienteMensagem(getCon()));
        setContratoDao(new Contrato(getCon()));
        setColaboradorDao(new Colaborador(getCon()));
        setTipoColaboradorDao(new TipoColaborador(getCon()));
        setAcessoColaboradorDao(new AcessoColaborador(getCon()));
        setColetorDao(new Coletor(getCon()));
        setUsuarioDao(new Usuario(getCon()));
        setUsuarioEmailDao(new UsuarioEmail(getCon()));
        setAcessoClienteDao(new AcessoCliente(getCon()));
        setLiberacaoAcessoDao(new LiberacaoAcesso(getCon()));
        setClienteDao(new Cliente(getCon()));
        setVendasConfigDao(new VendasConfig(getCon()));
        setHorarioDisponibilidadeDao(new HorarioDisponibilidade(getCon()));
        setContratoModalidadeTurmaDao(new ContratoModalidadeTurma(getCon()));
        setContratoModalidadeHorarioTurmaDao(new ContratoModalidadeHorarioTurma(getCon()));
        setMovProdutoDao(new MovProduto(getCon()));
        setUtilizacaoAvaliacaoFisicaDao(new UtilizacaoAvaliacaoFisica(getCon()));
        setRiscoDao(new Risco(getCon()));
        setValidacaoLocalAcessoDao(new ValidacaoLocalAcesso(getCon()));
        setPermissaoDao(new Permissao(getCon()));
        setControleAcessoDao(new ControleAcesso(getCon()));
        setPessoaDao(new Pessoa(getCon()));
        setCategoriaDao(new Categoria(getCon()));
        setEnderecoDao(new Endereco(getCon()));
        setTelefoneDao(new Telefone(getCon()));
        setEmailDao(new Email(getCon()));
        setConfiguracaoDao(new ConfiguracaoSistema(getCon()));
        setConfiguracaoTotemDao(new ConfiguracaoEmpresaTotem(getCon()));
        setContratoHorarioDao(new ContratoHorario(getCon()));
        setHorarioDao(new Horario(getCon()));
        setPlanoDao(new Plano(getCon()));
        setPlanoExcecaoDao(new PlanoExcecao(getCon()));
        setContratoModalidadeDao(new ContratoModalidade(getCon()));
        setContratoCondicaoPagamentoDao(new ContratoCondicaoPagamento(getCon()));
        setContratoDuracaoDao(new ContratoDuracao(getCon()));
        setHistoricoContratoDao(new HistoricoContrato(getCon()));
        setHorarioTurmaDao(new HorarioTurma(getCon()));
        setUsuarioPerfilAcessoDao(new UsuarioPerfilAcesso(getCon()));
        setLogControleUsabilidadeDao(new LogControleUsabilidade(getCon()));
        setSituacaoClienteSinteticoDWDao(new SituacaoClienteSinteticoDW(getCon()));
        setPessoaFotoLocalAcessoDao(new PessoaFotoLocalAcesso(getCon()));
        setClienteClassificacaoDao(new ClienteClassificacao(getCon()));
        setDadosAcessoOffline(new DadosAcessoOffline(getCon()));
        setZwFacade(new ZillyonWebFacade(getCon()));
        setAutorizacaoDao(new AutorizacaoAcessoGrupoEmpresarial(getCon()));
        setUsuarioMovelDao(new UsuarioMovel(getCon()));
        setConfiguracaoCRMDao(new ConfiguracaoSistemaCRM(getCon()));
        setReposicaoDao(new Reposicao(getCon()));
        setAulaDesmarcadaDao(new AulaDesmarcada(getCon()));

        setHistoricoContatoDao(new HistoricoContato(getCon()));
        setClienteObservacaoDao(new ClienteObservacao(getCon()));
        setMatriculaAlunoHorarioTurmaDAO(new MatriculaAlunoHorarioTurma(getCon()));

        setProfissaoDao(new Profissao(getCon()));
        setGrauInstrucaoDao(new GrauInstrucao(getCon()));
        setClassificacaoDao(new Classificacao(getCon()));
        setGrupoDao(new Grupo(getCon()));
        setParentescoDao(new Parentesco(getCon()));
        setPaisDao(new Pais(getCon()));
        setCidadeDao(new Cidade(getCon()));
        setPerguntaDao(new Pergunta(getCon()));
        setQuestionarioDao(new Questionario(getCon()));
        setCategoriaProdutoDao(new CategoriaProduto(getCon()));
        setProdutoDao(new Produto(getCon()));
        setDescontoDao(new Desconto(getCon()));
        setCondicaoPagamentoDao(new CondicaoPagamento(getCon()));
        setModalidadeDao(new Modalidade(getCon()));
        setAmbienteDao(new Ambiente(getCon()));
        setNivelTurmaDao(new NivelTurma(getCon()));
        setTurmaDao(new Turma(getCon()));
        setMovPagamentoDao(new MovPagamento(getCon()));
        setVendaAvulsaDao(new VendaAvulsa(getCon()));
        setMovContaCorrenteClienteDao(new MovimentoContaCorrenteCliente(getCon()));
        setBancoDao(new Banco(getCon()));
        setContaCorrenteDao(new ContaCorrente(getCon()));
        setTipoRetornoDao(new TipoRetorno(getCon()));
        setTipoRemessaDao(new TipoRemessa(getCon()));
        setConvenioCobrancaDao(new ConvenioCobranca(getCon()));
        setFormaPagamentoDao(new FormaPagamento(getCon()));
        setPinPadDao(new PinPad(getCon()));
        setOperadoraCartaoDao(new OperadoraCartao(getCon()));
        setMetaFinanceiraEmpresaDao(new MetaFinanceiraEmpresa(getCon()));
        setPerfilAcessoDao(new PerfilAcesso(getCon()));
        setLogDao(new Log(getCon()));
        setLogApiDao(new LogApi(getCon()));
        setLogTotalPassDao(new LogTotalPass(getCon()));
        setPlanoTextoPadraoDao(new PlanoTextoPadrao(getCon()));
        setConvenioDescontoDao(new ConvenioDesconto(getCon()));
        setJustificativaOperacaoDao(new JustificativaOperacao(getCon()));

        setBalancoDao(new Balanco(getCon()));
        setCardexDao(new Cardex(getCon()));
        setCompraDao(new Compra(getCon()));
        setProdutoEstoqueDao(new ProdutoEstoque(getCon()));
        setCupomFiscalDao(new CupomFiscal(getCon()));
        setReciboPagamentoDao(new ReciboPagamento(getCon()));
        setComposicaoDao(new Composicao(getCon()));
        setAtualizadorBDDao(new AtualizadorBD(getCon()));

        setAutorizacaoDao(new AutorizacaoAcessoGrupoEmpresarial(getCon()));
        setIntegracaoDao(new IntegracaoAcessoGrupoEmpresarial(getCon()));

        setCarteirasRel(new CarteirasRel(getCon()));
        setRecebivelAvulso(new RecebivelAvulso(getCon()));
        setVinculoDao(new Vinculo(getCon()));

        setTextoPadraoDao(new TextoPadrao(getCon()));
        setComissaoGeralConfiguracaoDao(new ComissaoGeralConfiguracao(getCon()));
        setGestaoNotasDao(new GestaoNotas(getCon()));
        setLancamentoProdutoColetivoDao(new LancamentoProdutoColetivo(getCon()));

        setTicketMedioDao(new TicketMedio(getCon()));
        setGestaoICV(new GestaoICV(getCon()));
        setPassivoDao(new Passivo(getCon()));
        setNotaFiscalServicoDao(new NotaFiscalDeServico(getCon()));

        setPresencaDao(new Presenca(getCon()));
        setContratoOperacaoDao(new ContratoOperacao(getCon()));

        setCreditoDCC(new CreditoDCCService(getCon()));
        setNfSeEmitida(new NFSeEmitida(getCon()));
        setNotaFiscalConsumidorEletronica(new NotaFiscalConsumidorEletronica(getCon()));
        setIntegracaoCadastrosDao(new IntegracaoCadastros(getCon()));
        setIntegracaoImportacaoDao(new IntegracaoImportacao(getCon()));
        setConviteDao(new Convite(getCon()));
        setAutorizacaoCobrancaDao(new AutorizacaoCobrancaCliente(getCon()));
        setFeriadoDao(new Feriado(getCon()));
        setModeloMensagemDao(new ModeloMensagem(getCon()));
        setEventoDao(new Evento(getCon()));
        setObjecaoDao(new Objecao(getCon()));
        setGrupoColaboradorDao(new GrupoColaborador(getCon()));
        setIndicacaoDao(new Indicacao(getCon()));
        setContratoRecorrenciaDao(new ContratoRecorrencia(getCon()));
        setHistoricoVinculoDao(new HistoricoVinculo(getCon()));

        setDadosGameDao(new DadosGame(getCon()));
        setDadosGerenciaisDao(new DadosGerencialPmg(getCon()));
        setMonitoramentoDao(new Monitoramento(getCon()));
        setContratoDuracaoCreditoTreino(new ContratoDuracaoCreditoTreino(getCon()));
        setBoletoServiceInterface(new BoletoService(getCon()));
        setDfDetalheDao(new DFSinteticoDetalhe(getCon()));
        setTurmaService(new TurmasServiceImpl(getCon()));
        setMSFinanceiroService(new AppGestorService(getCon()));
        setMSAlunoService(new AppGestorAlunoService(getCon()));
        setContratoAssinaturaDigitalService(new ContratoAssinaturaDigitalServiceImpl(getCon()));
        updateConfiguracaoSistema();
        setTipoConviteAulaExperimental(new TipoConviteAulaExperimental(getCon()));
        setConviteAulaExperimentalService(new ConviteAulaExperimentalService(getCon()));
        setNotificacaoUsuarioDao(new NotificacaoUsuario(getCon()));
        setBaseAcessoWS(new BaseAcessoWS());
        setControleCreditoTreino(new ControleCreditoTreino(getCon()));
        setMovContaDao(new MovConta(getCon()));
        setEstadoDao(new Estado(getCon()));
        setHistoricoPontos(new HistoricoPontos(getCon()));
        setBrinde(new Brinde(getCon()));
        setIntegracaoRDService(new IntegracaoRDServiceImpl(getCon()));
        setIntegracaoBuzzService(new IntegracaoBuzzLeadServiceImpl(getCon()));
        setIntegracaoLeadGenericaService(new IntegracaoLeadGenericaServiceImpl(getCon()));
        setClienteTokenDao(new ClienteToken(getCon()));
        setTokenService(new TokenService(getCon()));
        setConfirmacaoEmailCompra(new ConfirmacaoEmailCompra(getCon()));
        setAtestadoDao(new Atestado(getCon()));
        setGympass(new Gympass(getCon()));
        setQuestionarioClienteDao(new QuestionarioCliente(getCon()));
        setBoletoPJBankInterfaceFacade(new BoletoPJBank(getCon()));
        setBoletoInterfaceFacade(new Boleto(getCon()));
        setContratoAssinaturaDigitalDao(new ContratoAssinaturaDigital(getCon()));
        setTotalpassDao(new ConfigTotalPass(getCon()));
        setAfastamentoContratoDependenteInterfaceFacade(new AfastamentoContratoDependente(getCon()));
        setContratoDependenteDao(new ContratoDependente(getCon()));
    }

    public TransacaoAcesso getRxTx() {
        return rxTx;
    }

    public List<ContratoVO> getListaContratos() {
        return listaContratos;
    }

    public void setListaContratos(List<ContratoVO> listaContratos) {
        this.listaContratos = listaContratos;
    }

    public boolean getValidacaoAcessoOffline() {
        return validacaoAcessoOffline;
    }

    public void setValidacaoAcessoOffline(boolean validacaoAcessoOffline) {
        this.validacaoAcessoOffline = validacaoAcessoOffline;
    }

    public String getHorariosPlanosPermitidosParaOffline() {
        String ret = horariosPlanosPermitidosParaOffline.toString().
                replace("_", " ").replace("  ", " ").replace(" /", "/");
        if (ret.contains("-")) {
            return ret;
        } else {
            return "";
        }
    }

    public String getHorariosTurmasPermitidosParaOffline() {
        String ret = horariosTurmasPermitidosParaOffline.toString().
                replace("_", " ").replace("  ", " ").replace(" /", "/");
        if (ret.contains("-")) {
            return ret;
        } else {
            return "";
        }
    }

    public SituacaoAcessoEnum tentarAcesso(String codigo,
                                           DirecaoAcessoEnum direcao, Integer empresa,
                                           Integer localAcesso, String terminal, boolean leitorSerial,
                                           boolean validacaoAula) throws Exception {
        return tentarAcesso(codigo, direcao, empresa, localAcesso, terminal, leitorSerial, validacaoAula, false);
    }

    public SituacaoAcessoEnum tentarAcesso(String codigo,
                                           DirecaoAcessoEnum direcao, Integer empresa,
                                           Integer localAcesso, String terminal, boolean leitorSerial,
                                           boolean validacaoAula,
                                           boolean validandoTitular) throws Exception {
        try {
            if (getValidacaoAcessoOffline()) {
                if (horariosTurmasPermitidosParaOffline.length() > 0) {
                    horariosTurmasPermitidosParaOffline = new StringBuilder();
                }
                if (horariosPlanosPermitidosParaOffline.length() > 0) {
                    horariosPlanosPermitidosParaOffline = new StringBuilder();
                }
            }

            rxTx = new TransacaoAcesso();

            rxTx.setTz(findTimeZoneByEmpresa(empresa));
            //atualiza a data/hora da transação com TimeZone da Empresa
            rxTx.setDataAcesso(super.getInstanceCalendar(rxTx.getTz()).getTime());

            rxTx.setAcessoEsperado(direcao);

            rxTx.setLocal(getLocalAcessoDao().consultarPorCodigo(localAcesso, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            rxTx.getLocal().setCodigo(localAcesso);

            rxTx.getCartao().setCd(codigo);

            boolean possuiCapacidadeLimite = rxTx.getLocal().getCapacidadeLimite() > 0;

            if (configuracaoSistemaVO.isForcarCodigoAlternativoAcesso() && !getValidacaoAcessoOffline() && leitorSerial) {//tamanho de código configurado, forçar sempre pesquisar pelo código alternativo
                rxTx.setForcarCodigoAlternativo(true);
                Integer cliente = getPessoaDao().obterCodigoClienteComCodigoAlternativo(codigo);
                if (cliente > 0 || codigo.startsWith("T")) {
                    rxTx.getCartao().setTipo(TipoAcessoEnum.TA_ALUNO.getId());
                } else {
                    Integer colab = getPessoaDao().obterCodigoColaboradorComCodigoAlternativo(codigo);
                    if (colab > 0) {
                        rxTx.getCartao().setTipo(TipoAcessoEnum.TA_COLABORADOR.getId());
                    }
                }

                rxTx.getCartao().setId("0");
                rxTx.getCartao().setVia("0");
                rxTx.getCartao().setDv(codigo.substring(codigo.length() - 1));

            } else {//código nos tamanhos padrões

                //Verifica se o código é valido
                if ((codigo.length() != TAM_CODIGOACESSO) && (codigo.length() != TAM_CODIGOACESSOALTERNATIVO) && (codigo.length() != TAM_CODIGOACESSO1)
                        && (!codigo.startsWith("T"))) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQTAMCARTAOINVALIDO);
                    if (UteisValidacao.emptyString(codigo)) {
                        rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNONAOCADASTRADO);
                        return SituacaoAcessoEnum.RV_BLOQALUNONAOCADASTRADO;
                    }
                    Integer cliente = getPessoaDao().obterCodigoClienteComCodigoAlternativo(codigo);
                    if (cliente > 0) {
                        rxTx.getCartao().setTipo(TipoAcessoEnum.TA_ALUNO.getId());
                        rxTx.setForcarCodigoAlternativo(true);
                        leitorSerial = true;
                    } else {
                        Integer colab = getPessoaDao().obterCodigoColaboradorComCodigoAlternativo(codigo);
                        if (colab > 0) {
                            rxTx.getCartao().setTipo(TipoAcessoEnum.TA_COLABORADOR.getId());
                            rxTx.setForcarCodigoAlternativo(true);
                            leitorSerial = true;
                        } else {
                            return SituacaoAcessoEnum.RV_BLOQTAMCARTAOINVALIDO;
                        }
                    }
                }

                /**
                 * Formato código de barras Pos 01 a 01 - Caracter que indica
                 * que tipo de pessoa está acessando Pos 02 a 06 - Código que
                 * identifica a pessoa Pos 07 a 08 - Identificador Pos 09 a 09 -
                 * Via da carteirinha Pos 10 a 10 - Dígito verificador
                 */
                if (codigo.length() == 10) {//código de acesso
                    rxTx.getCartao().setTipo(codigo.substring(0, 1));
                    rxTx.getCartao().setCodigo(codigo.substring(1, 6));
                    rxTx.getCartao().setId(codigo.substring(6, 8));
                    rxTx.getCartao().setVia(codigo.substring(8, 9));
                    rxTx.getCartao().setDv(codigo.substring(9, 10));
                    if (codigo.startsWith("1")) {
                        rxTx.getCartao().setTipo(TipoAcessoEnum.TA_ALUNO.getId());
                    } else if (codigo.startsWith("5")) {
                        rxTx.getCartao().setTipo(TipoAcessoEnum.TA_COLABORADOR.getId());
                    }
                } else if ((codigo.length() == 8) || (codigo.startsWith("T"))) {//código alternativo
                    if (codigo.startsWith("T")) {
                        rxTx.getCartao().setTipo(TipoAcessoEnum.TA_ALUNO.getId());
                    } else {
                        /*10/01/11 Ulisses...
                         * Formato de código alternativo para Colaborador: O primeiro dígito é 9
                         * Formato de código alternativo para Aluno: O primeiro dígito é diferente de 9
                         */
                        if (codigo.startsWith("9")) {
                            rxTx.getCartao().setTipo(TipoAcessoEnum.TA_COLABORADOR.getId());
                        } else {
                            rxTx.getCartao().setTipo(TipoAcessoEnum.TA_ALUNO.getId());
                        }
                    }
                } else if (codigo.length() == 11) {//código de acesso
                    /**
                     * Formato código de barras Pos 01 a 01 - Caracter que
                     * indica que tipo de pessoa está acessando Pos 02 a 07 -
                     * Código que identifica a pessoa Pos 08 a 09 -
                     * Identificador Pos 10 a 10 - Via da carteirinha Pos 11 a
                     * 11 - Dígito verificador
                     */
                    rxTx.getCartao().setTipo(codigo.substring(0, 1));
                    rxTx.getCartao().setCodigo(codigo.substring(1, 7));
                    rxTx.getCartao().setId(codigo.substring(7, 9));
                    rxTx.getCartao().setVia(codigo.substring(9, 10));
                    rxTx.getCartao().setDv(codigo.substring(10, 11));
                    if (codigo.startsWith("1")) {
                        rxTx.getCartao().setTipo(TipoAcessoEnum.TA_ALUNO.getId());
                    } else if (codigo.startsWith("5")) {
                        rxTx.getCartao().setTipo(TipoAcessoEnum.TA_COLABORADOR.getId());
                    }
                }
            }

            String quemEstaAcessando = rxTx.getCartao().getTipo();
            if (!(quemEstaAcessando.equals(TipoAcessoEnum.TA_COLABORADOR.getId()))) {
                ///////////////////  Início das validações do Aluno ////////////////////////////
                /**
                 * Verifica se o aluno está cadastrado na academia. Objeto
                 * cliente será instanciado neste método
                 */
                if (!existeAlunoCadastrado(codigo, localAcesso, leitorSerial)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNONAOCADASTRADO);
                    return SituacaoAcessoEnum.RV_BLOQALUNONAOCADASTRADO;
                }

                //Verifica se o dv bate com o dv calculado pelo codigo do aluno
                if ((codigo.length() == 10 || codigo.length() == 11) && !rxTx.isForcarCodigoAlternativo()) {
                    if (!dvConfere(rxTx.getCartao().getCd(), rxTx.getCliente().getEmpresa())) {
                        rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQDVNAOCONFERE);
                        return SituacaoAcessoEnum.RV_BLOQDVNAOCONFERE;
                    }
                }

                //Se nao passou a sentido do acesso entao validar pelo ultimo acesso
                if (direcao == DirecaoAcessoEnum.DA_OESPERADO) {
                    direcao = definirSentidoAcessoCliente(rxTx.getCliente());
                }

                rxTx.setAcessoEsperado(direcao);

                if (rxTx.getAcessoEsperado() == DirecaoAcessoEnum.DA_SAIDA) {
                    getAcessoClienteDao().consultarTicket(rxTx.getCliente(), rxTx.getDataAcesso());
                    if (!UteisValidacao.emptyString(rxTx.getCliente().getTicket())) {
                        rxTx.setTicket(rxTx.getCliente().getTicket());
                        rxTx.setPermanencia(rxTx.getCliente().getPermanencia());
                    }
                }

                String codAcessoTitular = rxTx.getCliente().getCodAcessoTitularPlanoCompartilhado();
                ContratoVO contratoDependente = null;
                if (UteisValidacao.emptyString(codAcessoTitular)) {
                    // Consultar o vencimento do último contrato do cliente.
                    Date dataUltimoVenct = getContratoDao().consultarUltimoVencimento(rxTx.getCliente().getPessoa().getCodigo());
                    rxTx.setDataLimiteAcesso(Uteis.getData(dataUltimoVenct, "br"));
                    rxTx.setDuracaoContrato(getContratoDao().consultarUltimaDuracao(rxTx.getCliente().getPessoa().getCodigo()));
                } else {
                    ContratoDependenteVO contratoDependenteVO = getContratoDependenteDao().findByDependente(rxTx.getCliente().getCodigo())
                            .orElse(null);
                    if (contratoDependenteVO != null) {
                        rxTx.setDataLimiteAcesso(Uteis.getData(contratoDependenteVO.getDataFinalAjustada(), "br"));

                        if (contratoDependenteVO.getContrato() != null) {
                            contratoDependente = getContratoDao().consultarPorCodigo(contratoDependenteVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                            rxTx.setDuracaoContrato(contratoDependente.getContratoDuracao().getNumeroMeses());
                        }
                    }
                }

                //Não fazer validacao para saída
                if (direcao == DirecaoAcessoEnum.DA_SAIDA) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO);
                    return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                }

                //Valida tempo mínimo entre acessos
                if (!validandoTitular && existeAcessosSeguidos(quemEstaAcessando, rxTx, localAcesso)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQACESSOSSEGUIDOS);
                    return SituacaoAcessoEnum.RV_BLOQACESSOSSEGUIDOS;
                }

                if (!validandoTitular && existeMsgBloqueio(rxTx.getDataAcesso(), rxTx.getCliente().getCodigo())) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQMSGPERSONALIZADA);
                    return SituacaoAcessoEnum.RV_BLOQMSGPERSONALIZADA;
                }

                if (!UteisValidacao.emptyString(codAcessoTitular)) {
                    if (existeAfastamentoParaDependente(this.rxTx.getCliente().getCodigo())) {
                        rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO);
                        return SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO;
                    }

                    if (contratoDependente != null && existeParcelaVencidaPorContrato(empresa, contratoDependente.getCodigo())) {
                        rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA);
                        return SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA;
                    }

                    if (contratoDependente != null) {
                        listaContratos.add(consultarContrato(contratoDependente.getCodigo()));
                    }
                    consultarContratosVendaCreditoVigentes(rxTx.getCliente().getPessoa().getCodigo());
                } else {
                    //Verifica se existe autorizacao de acesso
                    if (!existeAutorizacaoAcesso(rxTx.getDataAcesso(),
                            rxTx.getCliente().getPessoa().getCodigo(),
                            rxTx.getCliente().getCodigo(),
                            empresa)) {
                        rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO);
                        return SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO;
                    }
                }

                // Verifica se o aluno tem permissão para acesso a unidade
                // a partir de configurações de empresas do plano. Veja #12410
                if (!existeAutorizacaoAcessoEmpresa(empresa)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQPLANOEMPRESA);
                    return SituacaoAcessoEnum.RV_BLOQPLANOEMPRESA;
                }

                for (ContratoVO contratoVO: listaContratos) {
                    if (contratoVO.getPlano().isPermitirAcessoRedeEmpresa()
                            && contratoVO.getPlano().isAcessoRedeEmpresasEspecificas()) {
                        rxTx.getCliente().setChavesEmpresasPermiteAcesso(getPlanoDao().obterChavesEmpresasPermiteAcesso(contratoVO.getPlano().getCodigo()));
                    }
                }

                if (existeParcelaVencida(empresa, this.rxTx.getCliente().getPessoa().getCodigo())) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA);
                    return SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA;
                }

                for (ContratoVO contratoVO : listaContratos) {
                    if (!UteisValidacao.emptyNumber(contratoVO.getPessoaOriginal().getCodigo())) {
                        if (existeParcelaVencidaPorContrato(empresa, contratoVO.getCodigo())) {
                            rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA);
                            return SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA;
                        }
                    }
                }

                if (bloquearSeParcelaEmAberto(empresa)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA);
                    return SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA;
                }

                if (bloquearSeDebitoEmConta()) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA);
                    return SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA;
                }

                if (bloquearSeMatriculaRematricaTotemSemPagamento(empresa)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA);
                    return SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA;
                }

                if (bloquearSemCartaoDeVacina(empresa)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQSEMCARTAOVACINA);
                    return SituacaoAcessoEnum.RV_BLOQSEMCARTAOVACINA;
                }

                if (bloquearAcessoAlunoParqNaoAssinado()) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOSEMPARQASSINADO);
                    return SituacaoAcessoEnum.RV_BLOQALUNOSEMPARQASSINADO;
                }

                setContratoComLimiteDeAcessoExcedido(false);
                boolean liberadoNoHorarioDoPlano = estaNoHorarioDoPlano(rxTx.getDataAcesso());
                boolean liberadoNoHorarioDaTurma = estaNoHorarioDaTurma(rxTx.getDataAcesso());

                if (validarSaldoCreditoTreino(liberadoNoHorarioDoPlano, liberadoNoHorarioDaTurma) > 0) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOFREQUENCIAPLANO);
                    return rxTx.getResultado();
                }
                if (!liberadoNoHorarioDoPlano
                        && !liberadoNoHorarioDaTurma
                        && isContratoComLimiteDeAcessoExcedido()) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOFREQUENCIAPLANO);
                    return rxTx.getResultado();
                }

                String validacaoSomenteComAgendamento;
                if ((!liberadoNoHorarioDaTurma && !this.listaContratos.isEmpty()) && getEmpresaDao().isAcessoSomenteComAgendamento(empresa)) {
                    Date agora = super.getInstanceCalendar(rxTx.getTz()).getTime();
                    validacaoSomenteComAgendamento = getHorarioTurmaDao().alunoAgendadoHorarioDia(rxTx.getDataAcesso(), agora, rxTx.getCliente().getCodigo(), empresa, getValidacaoAcessoOffline());
                    if (!getValidacaoAcessoOffline() && !UteisValidacao.emptyString(validacaoSomenteComAgendamento)) {
                        liberadoNoHorarioDaTurma = true;
                    }

                    if (getValidacaoAcessoOffline()) {
                        horariosTurmasPermitidosParaOffline.append(validacaoSomenteComAgendamento);
                    }

                    if (UteisValidacao.emptyString(validacaoSomenteComAgendamento)) {
                        rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQFORAHORARIOTURMA);
                        if (!getValidacaoAcessoOffline()) {
                            return SituacaoAcessoEnum.RV_BLOQFORAHORARIOTURMA;
                        }
                    }
                }

                SituacaoAcessoEnum situacaoAcesso = validarPorTerminal(
                        Integer.valueOf(terminal),
                        liberadoNoHorarioDoPlano,
                        liberadoNoHorarioDaTurma);

                Uteis.logar("-- fim validacaoTerminal:" + situacaoAcesso.getId());
                /*
                 *   ATENÇÃO   ATENÇÃO   ATENÇÃO   ATENÇÃO   ATENÇÃO   ATENÇÃO
                 *
                 * A validação de horário deve sempre ser a última.
                 * Caso tenha que ser desenvolvida mais alguma validação ela deve ser colocada
                 * antes da validação do horário.
                 */

                if (getValidacaoAcessoOffline()
                        && situacaoAcesso == SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO
                        && horariosTurmasPermitidosParaOffline.length() < 10
                        && horariosPlanosPermitidosParaOffline.length() < 10) {
                    /*
                     * O aluno tem acesso livre mas nenhum horário foi preenchido.
                     * É usado "length() < 10" porque às vezes é preenchido alguma coisa
                     * mas não é o horário.
                     */

                    horariosTurmasPermitidosParaOffline.append("00:00-23:59_");
                    horariosPlanosPermitidosParaOffline.append("00:00-23:59_");
                } else if (getValidacaoAcessoOffline()//Validação Off-Line deve liberar o Acesso caso haja bloqueio por horário da turma ou do plano
                        && (horariosPlanosPermitidosParaOffline.length() > 5 || horariosTurmasPermitidosParaOffline.length() > 5)
                        && ((situacaoAcesso.equals(SituacaoAcessoEnum.RV_BLOQFORAHORARIO) || (situacaoAcesso.equals(SituacaoAcessoEnum.RV_BLOQFORAHORARIOTURMA))))) {
                    situacaoAcesso = SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                    rxTx.setResultado(situacaoAcesso);
                }

                if (situacaoAcesso.getBloqueadoLiberado().equals("B")) {
                    // Neste caso o aceso foi bloqueado.
                    rxTx.setResultado(situacaoAcesso);
                    return situacaoAcesso;
                }

                if (validacaoAula) {
                    boolean temFreePass = false;
                    if (rxTx.getCliente().getFreePass() > 0) {
                        ProdutoVO produto = getProdutoDao().consultarPorChavePrimaria(rxTx.getCliente().getFreePass(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        temFreePass = TipoProduto.FREEPASS.getCodigo().equals(produto.getTipoProduto());
                    }
                    if (!alunoMarcouAula(rxTx, terminal) && !temFreePass) {
                        rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQFORAHORARIOTURMA);
                        return SituacaoAcessoEnum.RV_BLOQFORAHORARIOTURMA;
                    }
                }

                /*if (configuracaoSistemaVO.getSesc()) {
                    if (!(getClienteDao().validarVencimentoCarteirinha(rxTx.getCliente().getCodigo()))) {
                        rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQCARTEIRINHAVENCIDA);
                        return rxTx.getResultado();
                    }
                }*/

                int capacidadeEmpresa = getEmpresaDao().obterCapacidade(empresa);
                if (capacidadeEmpresa > 0) {
                    JSONObject object = getAcessoClienteDao().consultarAlunosNaAcademia(empresa);
                    int alunosNaAcademia = object.getInt("naAcademia");
                    if (alunosNaAcademia >= capacidadeEmpresa) {
                        rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOCAPACIDADESIMULTANEA);
                        return SituacaoAcessoEnum.RV_BLOQALUNOCAPACIDADESIMULTANEA;
                    }
                }

                if (bloquearSemAssinaturaDigital(empresa) && !validandoTitular) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOSEMASSINATURA);
                    return SituacaoAcessoEnum.RV_BLOQALUNOSEMASSINATURA;
                }

                if (bloquearSemTermoResponsabilidade() && !validandoTitular) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQALUNOSEMTERMORESPONSABILIDADE);
                    return SituacaoAcessoEnum.RV_BLOQALUNOSEMTERMORESPONSABILIDADE;
                }

                if (!validandoTitular) {
                    if (!UteisValidacao.emptyString(codAcessoTitular)) {
                        //Verifica se existe autorizacao de acesso
                        if (!existeAutorizacaoAcessoDependente(rxTx.getDataAcesso(),
                                rxTx.getCliente().getCodigo())) {
                            rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO);
                            return SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO;
                        }
                    }
                }

                if (limiteDeAcessosDiariosGymPassAtingido(this.rxTx.getCliente(), empresa)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQGYMPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO);
                    return SituacaoAcessoEnum.RV_BLOQGYMPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO;
                }

                if (limiteDeAcessosDiarioTotalPassAtingido(this.rxTx.getCliente(), empresa)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQTOTALPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO);
                    return SituacaoAcessoEnum.RV_BLOQTOTALPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO;
                }

                ///////////////////  Fim das validações do Aluno ///////////////////////////////
            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(quemEstaAcessando)) {
                if (!existeColaboradorCadastrado(codigo, empresa, localAcesso, leitorSerial)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQCOLABORADORNAOCADASTRADO);
                    return SituacaoAcessoEnum.RV_BLOQCOLABORADORNAOCADASTRADO;
                }
                //Se nao passou o sentido do acesso entao validar pelo ultimo acesso
                if (direcao == DirecaoAcessoEnum.DA_OESPERADO) {
                    direcao = definirSentidoAcessoCliente(rxTx.getCliente());
                }

                rxTx.setAcessoEsperado(direcao);

                //Não fazer validacao para saída
                if (direcao == DirecaoAcessoEnum.DA_SAIDA) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO);
                    return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                }

                //Valida tempo mínimo entre acessos
                if (existeAcessosSeguidos(quemEstaAcessando, rxTx, localAcesso)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQACESSOSSEGUIDOS);
                    return SituacaoAcessoEnum.RV_BLOQACESSOSSEGUIDOS;
                }

                // Verifica se o Colaborador está inativo.
                if ((rxTx.getCliente().getSituacao() != null) && (rxTx.getCliente().getSituacao().equals("NA"))) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQCOLABORADORINATIVO);
                    return rxTx.getResultado();
                }

                if (bloquearSemCartaoDeVacina(empresa)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQSEMCARTAOVACINA);
                    return SituacaoAcessoEnum.RV_BLOQSEMCARTAOVACINA;
                }

                if (bloquearAcessoCrefVencido(codigo, leitorSerial, empresa)) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQCREFVENCIDO);
                    return SituacaoAcessoEnum.RV_BLOQCREFVENCIDO;
                }

                SituacaoAcessoEnum retornoBloqueioPersonal = bloquearPersonalTrainer(codigo, leitorSerial, empresa);
                if (!retornoBloqueioPersonal.equals(SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO)){
                    rxTx.setResultado(retornoBloqueioPersonal);
                    return retornoBloqueioPersonal;
                }
            }
            rxTx.setResultado(SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO);

            //VALIDACAO DA CAPACIDADE DE ACESSOS DO LOCAL DE ACESSO
            if (possuiCapacidadeLimite) {
                Integer capacidadeAtual = getAcessoClienteDao().consultarQtdAlunosNaAcademiaPorLocalAcesso(empresa, rxTx.getLocal().getCodigo());
                boolean ultrapassouCapacidade = capacidadeAtual >= rxTx.getLocal().getCapacidadeLimite();
                if(ultrapassouCapacidade) {
                    rxTx.setResultado(SituacaoAcessoEnum.RV_BLOQCAPACIDADEMAXIMA);
                    return SituacaoAcessoEnum.RV_BLOQCAPACIDADEMAXIMA;
                }
            }

            return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
        } finally {
            listaContratos.clear();
        }
    }

    private void consultarContratosVendaCreditoVigentes(Integer codigoPessoa) throws Exception {
        List<ContratoVO> contratoVOS = getContratoDao().consultarContratosVigentesPorPessoa(codigoPessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        for (ContratoVO contratoVO : contratoVOS) {
            if (contratoVO.isVendaCreditoTreino()) {
                listaContratos.add(consultarContrato(contratoVO.getCodigo()));
            }
        }
    }

    private boolean existeAutorizacaoAcessoDependente(Date dataAcesso, Integer codigoCliente) throws Exception {
        this.listaContratos.clear();
        // Pesquisar os periodoAcessos cuja dataAtual esteja entre a datainicioacesso e datafinalacesso
        List<ContratoDependenteVO> listContratosDependente = getContratoDependenteDao()
                .findAllVigentByCliente(codigoCliente, dataAcesso);

        boolean acessoAutorizado = false;
        for (ContratoDependenteVO contratoDependenteVO : listContratosDependente) {
            acessoAutorizado = true;
            ContratoVO contrato = consultarContrato(contratoDependenteVO.getContrato().getCodigo());
            listaContratos.add(contrato);
        }

        return acessoAutorizado;
    }

    private boolean limiteDeAcessosDiariosGymPassAtingido(PessoaAcesso cliente, Integer empresa) {
        try {
            if (cliente.getSituacao().equals(SituacaoClienteEnum.ATIVO.getCodigo())
                    || UteisValidacao.emptyString(cliente.getGympassUniqueToken())) {
                return false; // não validar para alunos ativos ou que não tem token Gympass
            }
            Integer limiteAcessosDiarios = getEmpresaDao().obterLimiteDeAcessosPorDiaGympass(empresa);
            if (UteisValidacao.emptyNumber(limiteAcessosDiarios)) {
                return false; // não validar se o limite de acessos diarios for zero ou nulo.
            }
            boolean existePeriodoAcessoGympassVigente = getPeriodoAcessoDao().existePeriodoAcessoGympassVigente(cliente.getPessoa().getCodigo());
            if (!existePeriodoAcessoGympassVigente) {
                return false; // não validar para alunos que não tem periodo de acesso gympass vigente
            }
            Integer quatidadeAcessosHoje = getAcessoClienteDao().consultarQtdAcessosHojePorCliente(cliente.getCodigo());
            return quatidadeAcessosHoje >= limiteAcessosDiarios;
        } catch (Exception e) {
            Uteis.logar(e, AcessoControle.class);
            return false;
        }

    }

    private boolean limiteDeAcessosDiarioTotalPassAtingido(PessoaAcesso cliente, Integer empresa) {
        try {
            if (cliente.getSituacao().equals(SituacaoClienteEnum.ATIVO.getCodigo())) {
                return false; // não validar para alunos ativos ou que não tem token TotalPass
            }
            Integer limiteAcessosDiarios = getTotalpassDao().obterLimiteDeAcessosPorDiaTotalpass(empresa);
            if (UteisValidacao.emptyNumber(limiteAcessosDiarios)) {
                return false; // não validar se o limite de acessos diarios for zero ou nulo.
            }
            boolean existePeriodoAcessoTotalpassVigente = getPeriodoAcessoDao().existePeriodoAcessoTotalpassVigente(cliente.getPessoa().getCodigo());
            if (!existePeriodoAcessoTotalpassVigente) {
                return false; // não validar para alunos que não tem periodo de acesso total pass vigente
            }
            Integer quatidadeAcessosHoje = getAcessoClienteDao().consultarQtdAcessosHojePorCliente(cliente.getCodigo());
            return quatidadeAcessosHoje >= limiteAcessosDiarios;
        } catch (Exception e) {
            Uteis.logar(e, AcessoControle.class);
            return false;
        }
    }

    private TimeZone findTimeZoneByEmpresa(Integer empresa) throws Exception {
        TimeZone timeZone = timeZoneMap.get(empresa);
        if (timeZone == null) {
            timeZone = TimeZone.getTimeZone(getEmpresaDao().obterTimeZoneDefault(empresa));
            timeZoneMap.put(empresa, timeZone);
        }
        return timeZone;
    }

    public void enviarAcesso(Date diaAcesso, Integer empresa) throws Exception {
        try {
            if (!UteisValidacao.emptyString(getRxTx().getCliente().getPessoa().getIdExternoIntegracao())) {
                EmpresaVO empresaVO = getEmpresaDao().consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (!UteisValidacao.emptyString(empresaVO.getUrlEnvioAcesso())) {

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("idExternoIntegracao", getRxTx().getCliente().getPessoa().getIdExternoIntegracao());
                    jsonObject.put("data", Uteis.getDataComHora(diaAcesso));
                    jsonObject.put("direction", getRxTx().getAcessoEsperado().toString());


                    final RequestConfig params = RequestConfig.custom().setConnectTimeout(5000).setSocketTimeout(5000).build();

                    try (CloseableHttpClient client = HttpClients.createDefault()) {
                        HttpPost httpPost = new HttpPost(empresaVO.getUrlEnvioAcesso());
                        httpPost.setConfig(params);

                        httpPost.setHeader("Content-type", "application/json");
                        if (!UteisValidacao.emptyString(empresaVO.getTokenEnvioAcesso())) {
                            httpPost.setHeader("Access-Token", empresaVO.getTokenEnvioAcesso());
                        }
                        httpPost.setEntity(new StringEntity(jsonObject.toString(), "UTF8"));


                        HttpResponse response = client.execute(httpPost);
                        int statusCode = response.getStatusLine().getStatusCode();
                    }
                }
            }

        } catch (Exception ignored) {
        }
    }

    public void enviarAcessoIntegracaoPratique(Date dataHoraAcesso, Integer empresa, String meioIdentificacao, String sentido) {
        try {
            ConfiguracaoIntegracaoAcessoPratiqueVO configAcessoPratique = getEmpresaDao().consultarConfiguracaoIntegracaoAcessoPratique(empresa);
            if (configAcessoPratique.isHabilitada()) {
                EmpresaVO empresaVO = getEmpresaDao().consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                Uteis.submitExecutor(new ThreadEnvioRegistroAcesso(PropsService.getPropertyValue(PropsService.URL_ENVIO_ACESSO_INTEG_PRATIQUE),
                        PropsService.getPropertyValue(PropsService.TOKEN_ENVIO_ACESSO_INTEG_PRATIQUE),
                        empresaVO.getIdExterno(),
                        getRxTx().getCliente().getMatricula(),
                        dataHoraAcesso, meioIdentificacao, sentido));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao iniciar thread integracao envio acesso pratique: " + ex.getMessage());
        }
    }

    private boolean bloquearSemCartaoDeVacina(Integer empresa) throws Exception {
        EmpresaVO empresaVo = getEmpresaDao().consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (empresaVo.getBloquearSemCartaoVacina()) {
            Date nascidoAntes = Uteis.somarCampoData(Calendario.hoje(), Calendar.YEAR, empresaVo.getIdadeMinimaCartaoVacina() * -1);
            if (rxTx.getCliente().getPessoa().getDataNasc() == null
                    || Calendario.menor(rxTx.getCliente().getPessoa().getDataNasc(), nascidoAntes)) {
                String tipoAnexo = empresaVo.getTipoAnexoCartaoVacina().equals(PessoaAnexoEnum.CARTAO_VACINACAO_PRIMEIRA_DOSE) ? PessoaAnexoEnum.CARTAO_VACINACAO_PRIMEIRA_DOSE.getCodigo() + "," + PessoaAnexoEnum.CARTAO_VACINACAO_SEGUNDA_UNICA.getCodigo()
                        : PessoaAnexoEnum.CARTAO_VACINACAO_SEGUNDA_UNICA.getCodigo().toString();
                return !getPessoaAnexoDao().existesAnexoPessoaTipoIdade(rxTx.getCliente().getPessoa().getCodigo(), tipoAnexo);
            }
        }
        return false;
    }

    private boolean bloquearAcessoAlunoParqNaoAssinado() throws Exception {
        boolean verificarParQAssinado = false;
        if (getRxTx().getCliente() != null && getRxTx().getCliente().getEmpresa() != 0) {
            verificarParQAssinado = getEmpresaDao().isBloquearAcessoAlunoParQNaoAssinado(getRxTx().getCliente().getEmpresa());
        }
        if (verificarParQAssinado) {
            String urlTreino = PropsService.getPropertyValue(getKey(), PropsService.urlTreino);
            String fullUrl = urlTreino + "/prest/avaliacao/" + getKey() + "/is-parq-aluno-assinado/" + getRxTx().getCliente().getEmpresa() + "/" + rxTx.getCliente().getMatricula();
            RequestHttpService httpService = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(fullUrl, null, null, null, MetodoHttpEnum.GET);
            String retornoConsulta = respostaHttpDTO.getResponse();
            if (new JSONObject(retornoConsulta).has("assinado")) {
                // se o retorno de assinado for false, o aluno deve ser bloqueado
                return !new JSONObject(retornoConsulta).optBoolean("assinado");
            }
        }
        return false;
    }

    private boolean bloquearAcessoCrefVencido(String codigo, boolean leitorSerial, Integer empresa) throws Exception {
        EmpresaVO empresaVO = getEmpresaDao().consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (empresaVO.getBloquearAcessoCrefVencido()) {
            boolean personalProfessor = false;
            ColaboradorVO colaborador = null;
            if (codigo.length() == 8 || (rxTx.isForcarCodigoAlternativo() && leitorSerial)) {
                colaborador = getColaboradorDao().consultarPorCodAlternativo(codigo, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else if (codigo.length() == 10) {
                colaborador = getColaboradorDao().consultarPorCodAcesso(codigo, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }

            if (colaborador == null) {
                return true;
            }

            List<ColaboradorVO> colaboradoresPessoas = getColaboradorDao().consultarPorCodigoPessoa(colaborador.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (colaboradoresPessoas.size() > 1) {
                for (ColaboradorVO col : colaboradoresPessoas) {
                    if (col.getEmpresa().getCodigo().equals(rxTx.getLocal().getEmpresa().getCodigo())) {
                        colaborador = col;
                        break;
                    }
                }
            }

            List<TipoColaboradorVO> tipos = getTipoColaboradorDao().consultarPorCodigoColaborador(
                    colaborador.getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            for (TipoColaboradorVO tipo : tipos) {
                if (tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_TRAINER.getSigla())
                        || tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla())
                        || tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla())
                        || tipo.getDescricao().equals(TipoColaboradorEnum.PROFESSOR.getSigla())
                        || tipo.getDescricao().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla())) {
                    personalProfessor = true;
                }
            }

            if (!personalProfessor) {
                return false;
            } else {
                if (colaborador.getValidadeCref() == null || Calendario.menor(colaborador.getValidadeCref(), rxTx.getDataAcesso())) {
                    return true;
                }
            }
            return false;
        }
        return false;
    }

    private boolean bloquearSemAssinaturaDigital(Integer empresa) throws Exception {
        boolean verificarAssinaturaDigital = false;
        if (getRxTx().getCliente() != null && getRxTx().getCliente().getEmpresa() != 0) {
            verificarAssinaturaDigital = getEmpresaDao().isBloquearAcessoSemAssinaturaDigital(getRxTx().getCliente().getEmpresa());
        }
        if (verificarAssinaturaDigital) {
            Integer contratosAtivosSemAssinaturaDigital = getContratoAssinaturaDigitalDao().countContratosAtivosSemAssinatura(getRxTx().getCliente().getCodigo());
            Integer contratosAditivosAtivosSemAssinaturaDigital = getContratoAssinaturaDigitalDao().countContratosAditivosAtivosSemAssinatura(getRxTx().getCliente().getCodigo());
            return ((contratosAtivosSemAssinaturaDigital > 0) || (contratosAditivosAtivosSemAssinaturaDigital > 0));
        }
        return false;
    }

    private boolean bloquearSemTermoResponsabilidade() throws Exception {
        // Se o aluno está ativo, não tem necessidade de verificar o termo de responsabilidade
        if (getRxTx().getCliente() != null && getRxTx().getCliente().getSituacao().equals("AT")){
            return false;
        }
        boolean verificarTermoResponsabilidade = false;
        if (getRxTx().getCliente() != null && getRxTx().getCliente().getEmpresa() != 0) {
            verificarTermoResponsabilidade = getEmpresaDao().bloquearAcessoSemTermoResponsabilidade(getRxTx().getCliente().getEmpresa());
        }
        if (verificarTermoResponsabilidade) {
            return !getContratoAssinaturaDigitalDao().consultaTermoAceiteAssinadoPorAluno(getRxTx().getCliente().getCodigo());
        }
        return false;
    }
    private AcessoClienteVO consultarAcessoGymPassAguardandoResposta(Integer cliente, Integer localAcesso, Double minutosToleranciaValidacao) {
        AcessoClienteVO acessoAguardandoResposta = null;

        Iterator<AcessoClienteVO> iterator = getValidacoesAcessoGympass().iterator();
        while (iterator.hasNext()) {
            AcessoClienteVO validacaoGymPass = iterator.next();

            if (validacaoGymPass.getSituacao().equals(SituacaoAcessoEnum.RV_GYMPASS_AGUARDANDO_RESPOSTA) &&
                    validacaoGymPass.getLocalAcesso().getCodigo().equals(localAcesso) &&
                    validacaoGymPass.getCliente().getCodigo().equals(cliente) &&
                    Calendario.diferencaEmMinutos(validacaoGymPass.getDataRegistro(), Calendario.hoje()) <= minutosToleranciaValidacao) {

                acessoAguardandoResposta = validacaoGymPass;
            }
        }

        return acessoAguardandoResposta;
    }

    private AcessoClienteVO consultarAcessoBloqueadoGymPass(Integer cliente, Integer localAcesso, Double minutosToleranciaValidacao) {
        AcessoClienteVO acessoBloqueado = null;

        Iterator<AcessoClienteVO> iterator = getValidacoesAcessoGympass().iterator();
        while (iterator.hasNext()) {
            AcessoClienteVO validacaoGymPass = iterator.next();

            if (validacaoGymPass.getSituacao().getId().contains("RV_BLOQGYMPASS") &&
                    validacaoGymPass.getLocalAcesso().getCodigo().equals(localAcesso) &&
                    validacaoGymPass.getCliente().getCodigo().equals(cliente) &&
                    Calendario.diferencaEmMinutos(validacaoGymPass.getDataRegistro(), Calendario.hoje()) <= minutosToleranciaValidacao) {

                acessoBloqueado = validacaoGymPass;
            }
        }

        return acessoBloqueado;
    }

    private AcessoClienteVO consultarAcessoLiberadoGymPass(int cliente, int localDeAcesso, int minutosToleranciaValidacao) {
        AcessoClienteVO acessoLiberado = null;

        Iterator<AcessoClienteVO> iterator = getValidacoesAcessoGympass().iterator();
        while (iterator.hasNext()) {
            AcessoClienteVO validacaoGymPass = iterator.next();

            if (validacaoGymPass.getSituacao().equals(SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADOGYMPASS) &&
                    validacaoGymPass.getLocalAcesso().getCodigo().equals(localDeAcesso) &&
                    validacaoGymPass.getCliente().getCodigo().equals(cliente) &&
                    Calendario.diferencaEmMinutos(validacaoGymPass.getDataRegistro(), Calendario.hoje()) <= minutosToleranciaValidacao) {

                acessoLiberado = validacaoGymPass;
            }
        }
        return acessoLiberado;
    }

    private boolean existeAutorizacaoAcessoEmpresa(Integer empresa) throws Exception {
        if (this.listaContratos.isEmpty()) {
            // Existe autorização mas não está vinculado a um contrato
            // Veja o metodo existeAutorizacaoAcesso()
            return true;
        }
        boolean autorizado;
        configuracaoSistemaVO = getConfiguracaoDao().buscarPorCodigo(1, false,
                Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        if (configuracaoSistemaVO.isControleAcessoMultiplasEmpresasPorPlano()) {
            PlanoEmpresa planoEmpresaDAO = new PlanoEmpresa(con);
            autorizado = planoEmpresaDAO.permiteAcessosEmpresa(listaContratos, empresa);
            planoEmpresaDAO = null;
        } else {
            autorizado = true;
        }
        return autorizado;
    }

    private boolean alunoMarcouAula(TransacaoAcesso rxTx, String terminal) throws Exception {
        return getHorarioTurmaDao().alunoAgendadoHorarioDiaPorTerminal(rxTx.getCliente().getCodigo(), rxTx.getDataAcesso(), terminal);
    }

    private boolean validarTipoHorario(ValidacaoLocalAcessoVO validacao,
                                       Boolean liberadoNoHorarioDoPlano,
                                       Boolean liberadoNoHorarioDaTurma) throws Exception {
        // Se foi escolhido o tipo de horário igual a "Todos", neste caso o aluno pode entrar em qualquer dia e horário
        if (validacao.getTipoHorario() == TipoHorarioEnum.TIPOHORARIO_Todos) {
            return true;
        }
        // Se foi escolhido o tipo de horário igual a "Horario Contrato", neste caso o aluno só pode entrar no horário do plano
        if (validacao.getTipoHorario() == TipoHorarioEnum.TIPOHORARIO_HorarioContrato) {
            if ((liberadoNoHorarioDoPlano) || (liberadoNoHorarioDaTurma)) {
                return true;
            }
        }
        // Se foi escolhido o tipo de horário igual a "Horario Turma", neste caso o aluno só pode entrar no horário da turma
        if (validacao.getTipoHorario() == TipoHorarioEnum.TIPOHORARIO_HorarioTurma) {
            if (liberadoNoHorarioDaTurma) {
                return true;
            }
        }
        // Se foi escolhido o tipo de horário igual a "Horario Específico", neste caso o aluno só pode entrar no horário definido na regra.
        if (validacao.getTipoHorario() == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico) {
            validacao.setHorario(getHorarioDao().consultarPorChavePrimaria(validacao.getHorario().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            if (validarHorario(validacao.getHorario(), rxTx.getDataAcesso())) {
                return true;
            }
        }
        return false;
    }

    private boolean definirHorariosPlanosPermitidosParaOffline(HorarioVO horario,
                                                               HorarioDisponibilidadeVO horarioDisp) throws Exception {
        if (horario.isLivre()) {
            horariosPlanosPermitidosParaOffline.append("00:00-23:59_");
            return true;
        } else if (horarioDisp != null) {
            String horariosDisponiveis = horarioDisp.montarStringHorariosPermitidos();
            if (!UteisValidacao.emptyString(horariosDisponiveis) && horariosDisponiveis.length() > 5) {
                horariosPlanosPermitidosParaOffline.append(horariosDisponiveis);
                return true;
            }
        }

        return false;
    }

    private void definirHorariosTurmasPermitidosParaOffline(HorarioTurmaVO horario) throws Exception {
        if (!getValidacaoAcessoOffline()) {
            return;
        }

        horariosTurmasPermitidosParaOffline.append(horario.getHoraInicialComTolerancia()).append("-").append(horario.getHoraFinal()).append("_");
    }

    public void marcarPresencaAulaCheia(ClienteVO cliente, Date data, int empresa, Integer colaborador) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
            Calendar dataAcesso = Calendar.getInstance();
            dataAcesso.setTime(data);
            String horaAcesso = Uteis.getDataAplicandoFormatacao(data, "HH:mm");
            Date agora = formatter.parse(horaAcesso);
            List<HorarioTurmaVO> listaHorarios = getHorarioTurmaDao().horarioTurmaAlunoAgendadoHorarioDia(cliente.getCodigo(), data);
            for (HorarioTurmaVO horarioTurmaVO : listaHorarios) {
                if (validarMarcacaoAcessoTurmaAulaCheia(dataAcesso, horarioTurmaVO, agora)) {
                    getTurmaService().inserirConfirmacaoAulaAluno(cliente.getCodigo(), horarioTurmaVO.getCodigo(), colaborador, data);
                    String chavePrimariaLog = horarioTurmaVO.getCodigo() + "_" + Calendario.getData(data, "dd/MM/yyyy");
                    UsuarioVO usuarioVO = new UsuarioVO();
                    try {
                        if (UteisValidacao.notEmptyNumber(colaborador)) {
                            usuarioVO = getUsuarioDao().consultarPorColaborador(colaborador, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        }
                    } catch (Exception ex) {
                        Uteis.logar(ex, AcessoControle.class);
                    }
                    incluirLogPresenca(chavePrimariaLog, usuarioVO, cliente);
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(AcessoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void marcarPresenca(ClienteVO cliente, Date data, int empresa, UsuarioVO usuarioVO) {
        try {
            PresencaVO presenca = new PresencaVO();
            presenca.setDiaSemana(Uteis.obterDiaSemanaData(data));
            presenca.setDataPresenca(data);
            presenca.setHoje(true);
            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
            Calendar dataAcesso = Calendar.getInstance();
            dataAcesso.setTime(data);
            String horaAcesso = Uteis.getDataAplicandoFormatacao(data, "HH:mm");
            Date agora = formatter.parse(horaAcesso);
            cliente = getClienteDao().consultarPorChavePrimaria(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            List<MatriculaAlunoHorarioTurmaVO> listaHorarios = getZwFacade().getMatriculaAlunoHorarioTurma().consultarMatriculaAtivaPorPessoa(cliente.getPessoa().getCodigo(), empresa, data, Uteis.NIVELMONTARDADOS_MINIMOS);
            for (MatriculaAlunoHorarioTurmaVO maht : listaHorarios) {
                HorarioTurmaVO hrTurmaVO = maht.getHorarioTurma();

                // Buscar dados do horário da turma.
                hrTurmaVO = getHorarioTurmaDao().consultarPorCodigo(hrTurmaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (!desmarcouAula(data, hrTurmaVO, maht.getContrato().getCodigo(), true) && validarDefinirHorariosTurmas(dataAcesso, hrTurmaVO, agora) && !getPresencaDao().presencaJaMarcada(data, maht.getCodigo())) {
                    presenca.setDadosTurma(maht.getCodigo());
                    getPresencaDao().incluirSemCommit(presenca);
                    String chavePrimariaLog = hrTurmaVO.getCodigo() + "_" + Calendario.getData(data, "dd/MM/yyyy");
                    incluirLogPresenca(chavePrimariaLog, usuarioVO, cliente);
                }
            }
            List<ReposicaoVO> reposicoes = getReposicaoDao().consultar(data, null, cliente.getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            for (ReposicaoVO reposicao : reposicoes) {
                if (validarDefinirHorariosTurmas(dataAcesso, reposicao.getHorarioTurma(), agora)) {
                    getReposicaoDao().atualizarPresenca(reposicao.getCodigo(), data);
                    String chavePrimariaLog = reposicao.getHorarioTurma().getCodigo() + "_" + Calendario.getData(data, "dd/MM/yyyy");
                    incluirLogPresenca(chavePrimariaLog, usuarioVO, cliente);
                }
            }


        } catch (Exception ex) {
            Logger.getLogger(AcessoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void incluirLogPresenca(String chavePrimaria, UsuarioVO usuario, ClienteVO cliente) {
        try {
            String nome = "";
            Integer matricula = 0;
            if (UteisValidacao.emptyNumber(cliente.getCodigoMatricula())) {
                try (PreparedStatement stm = getCon().prepareStatement("select c.codigomatricula, p.nome from cliente c inner join pessoa p on p.codigo = c.pessoa  where c.codigo = " + cliente.getCodigo());
                     ResultSet rs = stm.executeQuery()) {
                    if (rs.next()) {
                        nome = rs.getString("nome");
                        matricula = rs.getInt("codigomatricula");
                    }
                }
            } else if (cliente.getPessoa() != null) {
                nome = cliente.getPessoa().getNome();
                matricula = cliente.getCodigoMatricula();
            }
            StringBuilder descricao = new StringBuilder();
            descricao.append("<br/>[matricula: ").append(matricula).append("]<br/>");
            descricao.append("[aluno: ").append(nome).append("]<br/>");
            descricao.append("[origem: Registro de Acesso]");
            getTurmaService().incluirLog(
                    chavePrimaria,
                    "PRESENCA",
                    "Presença Confirmada",
                    usuario,
                    "ALTERAÇÃO",
                    "Presença Confirmada",
                    descricao.toString(),
                    "",
                    null,
                    cliente.getCodigo());
        } catch (Exception ex) {
            Uteis.logar(ex.getMessage());
        }
    }

    private SituacaoAcessoEnum validarPorTerminal(final int terminal,
                                                  Boolean liberadoNoHorarioDoPlano,
                                                  Boolean liberadoNoHorarioDaTurma) throws Exception {

        // Consultar o coletor.
        ColetorVO coletor = getColetorDao().consultarPorNumeroTerminal(String.valueOf(terminal), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        // Consultar as validações do Coletor.
        coletor.setPermissoes(getValidacaoLocalAcessoDao().consultarPorColetor(coletor.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        if (coletor.getPermissoes().isEmpty()) {
            // Não tem regra de validação para o coletor.
            if (liberadoNoHorarioDoPlano || liberadoNoHorarioDaTurma) {
                return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
            } else {
                return SituacaoAcessoEnum.RV_BLOQFORAHORARIO;
            }
        }
        /* Verificar as regras de validação de acesso ao local.
         * Obs.: Estas regras existem para restringir o acesso ao local. Ex.: Na catraca "X",
         * só pode entrar as pessoas que estão matriculados na modalidade "Lutas" e no
         * horário das "10:00" às "11:00 hs." .
         */

        boolean atende_A_RegraMasBloqueadoPeloHorario = false;
        boolean bloqueadoPeloHorarioTurma_RegraValidacao = false;


        List<PeriodoAcessoClienteVO> listaPeriodoAcesso = null;
        if (this.listaContratos.isEmpty()) {
            listaPeriodoAcesso = getPeriodoAcessoDao().consultarPorPessoaDataAtual(
                    rxTx.getCliente().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        }

        int qtdValidacoes = coletor.getPermissoes().size();
        for (ValidacaoLocalAcessoVO validacao : coletor.getPermissoes()) {

            boolean clienteCadastradoNaEmpresa = (validacao.getEmpresa().getCodigo().intValue() == rxTx.getCliente().getEmpresa().intValue());

            if (!clienteCadastradoNaEmpresa) {
                continue;
            }
            /* 1ª Validação
             * Tipo Validação : Todos
             * Neste caso todos os clientes que tem contrato com a empresa definida na regra,
             * pode entrar em qualquer dia e horário.
             */
            if ((validacao.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Todos)) {

                if (this.listaContratos.size() <= 0) {
                    // se não tem contrato, então liberar acesso.
                    return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                }

                // Se tem contrato, o aluno só pode entrar se o mesmo estiver no horário do Plano ou no horário da turma.
                if (liberadoNoHorarioDoPlano || liberadoNoHorarioDaTurma) {
                    return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                }
                if (!liberadoNoHorarioDoPlano) {
                    return SituacaoAcessoEnum.RV_BLOQFORAHORARIO;
                } else {
                    return SituacaoAcessoEnum.RV_BLOQFORAHORARIOTURMA;
                }
            }
            /* 2ª Validação
             * Tipo Validação : Modalidade
             */
            if (validacao.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Modalidade) {
                if (listaPeriodoAcesso != null) {
                    //Segundo o Zoslley, quando for Diária e estiver na mesma modalidade, vai considerar horário livre
                    for (PeriodoAcessoClienteVO periodoAcessoClienteVO : listaPeriodoAcesso) {
                        if ("DI".equals(periodoAcessoClienteVO.getTipoAcesso())) {
                            AulaAvulsaDiariaVO aulaAvulsaDiariaVO = getAulaAvulsaDiariaDao().consultarPorChavePrimaria(periodoAcessoClienteVO.getAulaAvulsaDiaria(), Uteis.NIVELMONTARDADOS_MINIMOS);

                            if (aulaAvulsaDiariaVO.getModalidade().getCodigo().equals(validacao.getChave())) {
                                horariosPlanosPermitidosParaOffline = new StringBuilder();
                                return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                            }
                        }
                    }
                }
                for (ContratoVO contrato : this.listaContratos) {
                    // Verifica se o aluno está matriculado na modalidade.
                    boolean matriculadoNaModalidade = false;
                    for (ContratoModalidadeVO contratoModalidade : contrato.getContratoModalidadeVOs()) {
                        if (contratoModalidade.getModalidade().getCodigo().intValue() == validacao.getChave().intValue()) {
                            if (validacao.getTipoHorario() == TipoHorarioEnum.TIPOHORARIO_HorarioTurma) {
                                // Verificar se está no horário da turma cadastrada para a validação
                                liberadoNoHorarioDaTurma = verificarHorarioDaTurma(rxTx.getDataAcesso(), contratoModalidade, contrato.getCodigo());
                                bloqueadoPeloHorarioTurma_RegraValidacao = !liberadoNoHorarioDaTurma;
                                // Limpando o horário do plano, pois se houver contrato concomitante não irá atrapalhar a validação por terminais
                                horariosPlanosPermitidosParaOffline = new StringBuilder("");
                            }
                            matriculadoNaModalidade = true;
                            break;
                        }
                    }
                    if (!matriculadoNaModalidade) {
                        continue;
                    }
                    // Validar os tipos de Horários: "Todos", "Horario Contrato", "Horario Turma" e "Horario Específico"
                    if (validarTipoHorario(validacao, liberadoNoHorarioDoPlano, liberadoNoHorarioDaTurma)) {
                        return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                    } else {
                        atende_A_RegraMasBloqueadoPeloHorario = true;
                    }
                }
            }
            /* 3ª Validação
             * Tipo Validação : Produto
             */
            if (validacao.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Produto) {
                ProdutoVO produto = getProdutoDao().consultarPorChavePrimaria(validacao.getChave(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (produto.getTipoProduto().equals("FR") && rxTx.getCliente().getFreePass().equals(validacao.getChave())) {
                    return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                } else {
                    if (getMovProdutoDao().produtoDentroDaValidade(validacao.getChave(), rxTx.getCliente().getPessoa().getCodigo())) {
                        // Validar os tipos de Horários: "Todos", "Horario Contrato", "Horario Turma" e "Horario Específico"
                        if (validarTipoHorario(validacao, liberadoNoHorarioDoPlano, liberadoNoHorarioDaTurma)) {
                            return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                        } else {
                            atende_A_RegraMasBloqueadoPeloHorario = true;
                        }

                    }
                }
            }
            /* 4ª Validação
             * Tipo Validação : Produto Gym Pass
             * Só valida clientes que possuem periodo de acesso vigente com token gympass*/
            if (validacao.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_ProdutoGymPass) {
                PeriodoAcessoClienteVO acessoCliente = getPeriodoAcessoDao().obterUltimoDiaPeriodoAcessoPessoa(
                        rxTx.getCliente().getPessoa().getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS,
                        true, true);

                if (acessoCliente != null) {
                    // Caso o token seja "skip_token_gympass", ignora o resto das verificacoes e valida so horário
                    // Skip adicionado para pular validação de token em ambiente dev (GCM-63)
                    if (acessoCliente.getTokenGymPass().equalsIgnoreCase("skip_token_gympass")) {
                        Uteis.logar("valor acessoCliente: " + acessoCliente.toString());
                        Uteis.logar("VALIDAÇAO TOKEN GYMPASS SKIPADA");
                        return validarHorarioAcessoGympass(validacao)
                                ? SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO
                                : SituacaoAcessoEnum.RV_BLOQFORAHORARIOGYMPASS;
                    }

                    // Validação do produto GymPass e horário
                    if (!UteisValidacao.emptyString(acessoCliente.getTokenGymPass())) {
                        String produtoGymPass = getPeriodoAcessoDao().obterProdutoGymPassInfoChekin(acessoCliente.getCodigo(), validacao.getEmpresa().getCodigo(), rxTx.getCliente().getCodigo(), true);
                        boolean validacaoProdutoGymPass = !UteisValidacao.emptyString(produtoGymPass) && Integer.valueOf(produtoGymPass).equals(validacao.getChave());
                        // Só valida o horário se o produto for o mesmo configurado na validação de acesso
                        if (validacaoProdutoGymPass) {
                            boolean validacaoHorarioEspecificoGymPass = validarHorarioAcessoGympass(validacao);

                            if (validacaoProdutoGymPass && validacaoHorarioEspecificoGymPass) {
                                return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                            }

                            if (!validacaoHorarioEspecificoGymPass) {
                                return SituacaoAcessoEnum.RV_BLOQFORAHORARIOGYMPASS;
                            }

                            if (qtdValidacoes == 1) {
                                atende_A_RegraMasBloqueadoPeloHorario = false;
                            }
                        }
                    }
                }
                Uteis.logar("acessoCliente está null");

                //se acessoCliente = null
                if (qtdValidacoes == 1) {
                    if ((liberadoNoHorarioDoPlano || liberadoNoHorarioDaTurma) && validarHorarioAcessoGympass(validacao)) {
                        return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                    }

                    return SituacaoAcessoEnum.RV_BLOQTOKENGYMPASSINVALIDO;
                }
            }


            /* 5ª Validação
             * Tipo Validação : Horario
             */
            if (validacao.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Horario) {
                // Validar os tipos de Horários: "Todos", "Horario Contrato", "Horario Turma" e "Horario Específico"
                if (validarTipoHorario(validacao, liberadoNoHorarioDoPlano, liberadoNoHorarioDaTurma)) {
                    return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                } else {
                    atende_A_RegraMasBloqueadoPeloHorario = true;
                }
            }
        } // fim do for coletor.getPermissoes()

        // Se chegou até aqui, é porque existe uma restrição de acesso e o aluno não
        // não se enquadrou em nenhuma das validações definidas para o coletor.
        horariosPlanosPermitidosParaOffline = new StringBuilder("");
        horariosTurmasPermitidosParaOffline = new StringBuilder("");
        if (atende_A_RegraMasBloqueadoPeloHorario) {
            if (bloqueadoPeloHorarioTurma_RegraValidacao) {
                return SituacaoAcessoEnum.RV_BLOQFORAHORARIOTURMA;
            } else if (!liberadoNoHorarioDoPlano) {
                return SituacaoAcessoEnum.RV_BLOQFORAHORARIO;
            } else {
                return SituacaoAcessoEnum.RV_BLOQFORAHORARIOTURMA;
            }

        } else {
            return SituacaoAcessoEnum.RV_BLOQREGRA_LIBERACAO;
        }
    }

    private boolean validarHorarioAcessoGympass(ValidacaoLocalAcessoVO validacao) {
        if (validacao.getTipoHorario() != TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico) {
            return true;
        }

        try {
            Uteis.logar("Validando horário acesso Gympass...");
            validacao.setHorario(getHorarioDao().consultarPorChavePrimaria(
                    validacao.getHorario().getCodigo(),
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA
            ));
            boolean resultadoValidacao = validarHorario(validacao.getHorario(), rxTx.getDataAcesso());
            Uteis.logar("resultado validacao horario gympass =", resultadoValidacao);
            return resultadoValidacao;
        } catch (Exception e) {
            Uteis.logar("ERRO ao validar horário acesso Gympass: " + e.getMessage());
            Uteis.logar(e, this.getClass());
            return false;
        }
    }


    private byte[] obterFotoPessoaSeNaoEnviada(final String chave, int pessoa, int localAcesso) throws Exception {
        if (getValidacaoAcessoOffline()) {
            return null;
        }

        if (!getPessoaFotoLocalAcessoDao().fotoJaEnviadaLocalAcesso(localAcesso, pessoa)) {
            byte[] ret = null;
            if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                final String fotokey = getPessoaDao().obterFotoKey(pessoa);
                if (fotokey != null && !fotokey.isEmpty()) {
                    ret = getPessoaDao().obterFoto(chave, pessoa);
                }
            } else {
                ret = getPessoaDao().obterFoto(chave, pessoa);
            }
            if (ret != null) {
                getPessoaFotoLocalAcessoDao().incluir(localAcesso, pessoa);
                logarFotoEnviada("obterFotoPessoaSeNaoEnviada", localAcesso, pessoa);
            }
            return ret;
        }
        return null;
    }

    private boolean existeAlunoCadastrado(String codigo, Integer localAcesso, boolean leitorSerial) throws Exception {
        ClienteVO cliente = null;
        if (rxTx.isForcarCodigoAlternativo() && leitorSerial) {
            if (codigo.startsWith("T")) {
                cliente = getClienteDao().consultarPorMatricula(codigo.substring(1), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            } else {
                cliente = getClienteDao().consultarPorCodAlternativo(codigo, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
        } else {
            if (codigo.length() == 8) {
                cliente = getClienteDao().consultarPorCodAlternativo(codigo, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            } else if (codigo.length() == 10 || codigo.length() == 11) {
                cliente = getClienteDao().consultarPorCodAcesso(codigo, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            } else if (codigo.startsWith("T")) {
                cliente = getClienteDao().consultarPorMatricula(codigo.substring(1), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
        }

        if (cliente == null) {
            return false;
        }

        // Consultar o último acesso do cliente para o local de acesso
        cliente.setUaCliente(getAcessoClienteDao().consultarUltimoAcessoPorLocal(cliente, rxTx.getDataAcesso(), localAcesso, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        // Consultar dados de pessoa
        cliente.setPessoa(getPessoaDao().consultarPorCodigo(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        // Consultar dados de categoria.
        cliente.setCategoria(getCategoriaDao().consultarPorCodigo(cliente.getCategoria().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        //preencher foto... ou não
        cliente.getPessoa().setFoto(obterFotoPessoaSeNaoEnviada(getKey(), cliente.getPessoa().getCodigo(), localAcesso));

        rxTx.getCliente().setCodigo(cliente.getCodigo());
        rxTx.getCliente().setEmpresa(cliente.getEmpresa().getCodigo());
        rxTx.getCliente().setMatricula(cliente.getCodigoMatricula().toString());
        rxTx.getCliente().setSituacao(cliente.getSituacao());
        rxTx.getCliente().setPessoa(cliente.getPessoa());
        rxTx.getCliente().setCategoria(cliente.getCategoria().getNome());
        rxTx.getCliente().setUaSentido(cliente.getUaCliente().getSentido());
        rxTx.getCliente().setUaData(cliente.getUaCliente().getDataHoraEntrada());
        rxTx.getCliente().setFreePass(cliente.getFreePass().getCodigo());
        rxTx.getCliente().setGympassUniqueToken(cliente.getGympasUniqueToken());
        rxTx.getCliente().setGympassTypeNumber(cliente.getGympassTypeNumber());
        if (!UteisValidacao.emptyNumber(cliente.getTitularPlanoCompartilhado())) {
            ClienteVO clienteTitular = getClienteDao().consultarPorCodigo(cliente.getTitularPlanoCompartilhado(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            rxTx.getCliente().setCodAcessoTitularPlanoCompartilhado(clienteTitular.getCodAcesso());
        }
        //rxTx.setDataLimiteAcesso(cliente.getDataValidadeCarteirinha() != null ? Uteis.getData(cliente.getDataValidadeCarteirinha(), "br") : "00/00/0000");
        return true;
    }

    private boolean existeAcessosSeguidos(String quemEstaAcessando, TransacaoAcesso rxTx, Integer localAcesso) throws Exception {
        //Não existe ultimo acesso
        //if ((uaData == null) || (Uteis.getCompareData(data, uaData) == 0))
        if (rxTx.getCliente().getUaData() == null) {
            return false;
        }

        LocalAcessoVO local = getLocalAcessoDao().consultarPorCodigo(localAcesso, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        int tempoEntreAcessos = 0;
        if (quemEstaAcessando.equals(TipoAcessoEnum.TA_ALUNO.getId())) {
            // Local não configurado para acesso seguidos.
            if ((rxTx.getLocal().getTempoEntreAcessos() == null) || (local.getTempoEntreAcessos() <= 0)) {
                return false;
            }

            tempoEntreAcessos = local.getTempoEntreAcessos();
        }

        if (quemEstaAcessando.equals(TipoAcessoEnum.TA_COLABORADOR.getId())) {
            if ((rxTx.getLocal().getTempoEntreAcessosColaborador() == null)) {
                return false;
            }
            ColaboradorVO colaboradorVO = getColaboradorDao().consultarPorChavePrimaria(rxTx.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            if (colaboradorVO.getTempoEntreAcessos() < 0) {
                tempoEntreAcessos = local.getTempoEntreAcessosColaborador();
            } else {
                tempoEntreAcessos = colaboradorVO.getTempoEntreAcessos();
            }
        }

        if (tempoEntreAcessos > 0) {
            Calendar dataAcesso = super.getInstanceCalendar(rxTx.getTz());
            dataAcesso.setTime(rxTx.getDataAcesso());

            Calendar ultimoAcesso = super.getInstanceCalendar(rxTx.getTz());
            ultimoAcesso.setTime(rxTx.getCliente().getUaData());

            ultimoAcesso.add(Calendar.MINUTE, tempoEntreAcessos);
            return dataAcesso.before(ultimoAcesso);
        }
        return false;

    }

    private boolean existeAutorizacaoAcesso(Date data, Integer codigoPessoa, int codigoCliente, int codigoEmpresa) throws Exception {
        this.listaContratos.clear();

        // Pesquisar os periodoAcessos cuja dataAtual esteja entre a datainicioacesso e datafinalacesso
        List<PeriodoAcessoClienteVO> listaPeriodoAcesso = getPeriodoAcessoDao().consultarPorPessoaDataAtual(
                codigoPessoa, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        boolean acessoAutorizado = false;
        for (PeriodoAcessoClienteVO objPeriodoAcesso : listaPeriodoAcesso) {
            if (objPeriodoAcesso.getPermiteAcesso()) {
                acessoAutorizado = true;
                if ((objPeriodoAcesso.getContrato() != null) && (objPeriodoAcesso.getContrato() > 0)) {
                    ContratoVO contrato = consultarContrato(objPeriodoAcesso.getContrato());
                    listaContratos.add(contrato);
                } else {
                    //Neste caso, um dos periodos de acesso que permite acesso para o dia, é proveniente de uma diária, aula avulsa ou free pass. Neste caso, esse periodo prevalece aos dos contratos.
                    if ((rxTx.getDataLimiteAcesso() == null)
                            || (rxTx.getDataLimiteAcesso().isEmpty())
                            || Calendario.maior(objPeriodoAcesso.getDataFinalAcesso(), Uteis.getDate(rxTx.getDataLimiteAcesso()))) {
                        rxTx.setDataLimiteAcesso(Uteis.getData(objPeriodoAcesso.getDataFinalAcesso(), "br"));
                        if(!UteisValidacao.emptyString(objPeriodoAcesso.getTokenGymPass())){
                            acessoAutorizado = getPeriodoAcessoDao().validarCheckinEmpresa(objPeriodoAcesso.getCodigo(), codigoEmpresa);
                        }
                    }

                    // Valida se a diária do aluno é da mesma empresa que ele está acessando
                    if (!UteisValidacao.emptyNumber(objPeriodoAcesso.getAulaAvulsaDiaria())){
                        acessoAutorizado = validarEmpresaDiaria(codigoEmpresa, objPeriodoAcesso.getAulaAvulsaDiaria());
                    }

                    listaContratos.clear();
                    break;
                }
            }
        }
        if (!acessoAutorizado) {
            acessoAutorizado = liberarAcessoPorToleranciaContratoVencido(codigoEmpresa, codigoCliente);
        }
        return acessoAutorizado;
    }

    private boolean validarEmpresaDiaria(int codigoEmpresa, int codVendaAvulsaDiaria) throws Exception{
        if (!UteisValidacao.emptyNumber(codVendaAvulsaDiaria)) {
            EmpresaVO empresa = getEmpresaDao().consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (!empresa.getBloquearAcessoDiariaEmpresaDiferente()) {
                return true;
            }
            AulaAvulsaDiariaVO aulaAvulsaDiariaVO = getAulaAvulsaDiariaDao().consultarPorChavePrimaria(codVendaAvulsaDiaria, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return aulaAvulsaDiariaVO.getEmpresa().getCodigo() == codigoEmpresa;
        }
        return true;
    }

    private boolean liberarAcessoPorToleranciaContratoVencido(int codigoEmpresa, int codigoCliente) throws Exception {
        /* 28/06/11 Ulisses...  Validar tolerância após o vencimento do contrato
         * Regra para conceder a tolerância de dias após o vencimento do contrato:
         *      1 - O aluno não tem período de acesso.
         *      2 - O contrato está vencido
         *      3 - A tolerância definida no cadastro da empresa, tem prioridade
         *          em relação a tolerância definida na configuração do sistema.
         */
        EmpresaVO empresa = getEmpresaDao().consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        int diasTolerancia = empresa.getToleranciaDiasContratoVencido();
        if (diasTolerancia <= 0) {
            // Pegar a tolerância definida nas configurações do sistema.
            diasTolerancia = configuracaoSistemaVO.getToleranciaDiasContratoVencido();
        }
        if (diasTolerancia > 0) {
            SituacaoClienteSinteticoDWVO situacaoCliente = getSituacaoClienteSinteticoDWDao().consultarCliente(codigoCliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if ((situacaoCliente != null) && (situacaoCliente.getSituacaoContrato().equals("VE"))) {
                Calendar dataAtual = super.getInstanceCalendar(rxTx.getTz());
                Calendar dataFimContrato = super.getInstanceCalendar(rxTx.getTz());
                dataFimContrato.setTime(situacaoCliente.getDataFimPeriodoAcesso());
                dataFimContrato.add(Calendar.DAY_OF_MONTH, diasTolerancia);
                Uteis.retirarHoraDaData(dataAtual);
                Uteis.retirarHoraDaData(dataFimContrato);
                if (!dataAtual.after(dataFimContrato)) {
                    //se tiver acesso com tolerancia, então pesquisar contrato.
                    ContratoVO contrato = consultarContrato(situacaoCliente.getCodigoContrato());
                    listaContratos.add(contrato);
                    return true;
                }

            }
        }
        return false;
    }

    private ContratoVO consultarContrato(Integer codigoContrato) throws Exception {
        // Consultar dados básico do contrato.
        ContratoVO contrato = getContratoDao().consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (contrato.isVendaCreditoTreino()) {
            contrato.setContratoDuracaoCreditoTreinoVO(getContratoDuracaoCreditoTreino().consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        }
        // Consultar os horários do contrato.
        contrato.setContratoHorario(getContratoHorarioDao().consultarContratoHorarios(
                contrato.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        // Consultar os dados do horário do plano
        Integer codigoHorario = contrato.getContratoHorario().getHorario().getCodigo();
        if ((codigoHorario != null) && (codigoHorario > 0)) {
            contrato.getContratoHorario().setHorario(getHorarioDao().consultarPorChavePrimaria(contrato.getContratoHorario().getHorario().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        }

        // Consultar dados do Plano
        if ((contrato.getPlano().getCodigo() != null) && (contrato.getPlano().getCodigo() >= 0)) {
            contrato.setPlano(getPlanoDao().consultarPorChavePrimaria(contrato.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        }
        // consultar o contrato da modalidade
        contrato.setContratoModalidadeVOs(getContratoModalidadeDao().consultarContratoModalidades(
                contrato.getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        return contrato;
    }

    public boolean existeParcelaVencidaPorContrato(final Integer empresa, final Integer codContrato) throws Exception {
        /*
         * Considera que os contratos já tenham sido carregados!!
         */
        EmpresaVO empresaVo = getEmpresaDao().consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        return (getMovParcelaDao().existeParcelaVencidaSegundoConfiguracoes(
                0, codContrato, empresaVo, configuracaoSistemaVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, 0));
    }

    public boolean existeParcelaVencida(final Integer empresa, final Integer codPessoa) throws Exception {
        /*
         * Considera que os contratos já tenham sido carregados!!
         */
        EmpresaVO empresaVo = getEmpresaDao().consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        return (getMovParcelaDao().existeParcelaVencidaSegundoConfiguracoes(
                codPessoa, empresaVo, configuracaoSistemaVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
    }

    private boolean estaNoHorarioDoPlano(Date data) throws Exception {
        if (this.listaContratos.size() <= 0) {
            //Existe autorização mas não está vinculado a um contrato
            return true;
        }
        boolean estaNoHorario = false;
        for (ContratoVO contrato : this.listaContratos) {
            HorarioVO horario = contrato.getContratoHorario().getHorario();
            if (!validarFrequenciaAcessoPlano(contrato)) {
                setContratoComLimiteDeAcessoExcedido(true);
            } else {
                if ((horario.getCodigo() > 0) && (validarHorario(horario, data, false))) {
                    if (verificarHorarioLivreObrigatorioMarcarAula(contrato)) {
                        // Um dos contratos está no horário do Plano.t
                        estaNoHorario = true;
                        if (!getValidacaoAcessoOffline()) {
                            break;
                        }
                    }
                }
            }
        }
        return estaNoHorario;
    }

    private boolean estaNoHorarioDoPlanoDoTitular(Date data, ContratoVO contrato) throws Exception {
        HorarioVO horario = getHorarioDao().consultarPorCodigoContrato(contrato.getCodigo());
        return (horario.getCodigo() > 0) && (validarHorario(horario, data, false));
    }


    private boolean verificarHorarioLivreObrigatorioMarcarAula(ContratoVO contratoVO) throws Exception {
        if ((contratoVO == null) || (!contratoVO.isVendaCreditoTreino())) {
            return true;
        }
        Calendar dataAcesso = super.getInstanceCalendar(rxTx.getTz());
        dataAcesso.setTime(rxTx.getDataAcesso());
        String horaAcesso = Uteis.getDataAplicandoFormatacao(rxTx.getDataAcesso(), "HH:mm");
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
        Date agora = formatter.parse(horaAcesso);

        if (UtilReflection.objetoMaiorQueZero(contratoVO, "getContratoDuracaoCreditoTreinoVO().getCodigo()")) {
            boolean marcouAula = true;
            if (contratoVO.getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.LIVRE_OBRIGATORIO_MARCAR_AULA) {
                marcouAula = false;
                horariosPlanosPermitidosParaOffline.delete(0, horariosPlanosPermitidosParaOffline.length());
                List<ReposicaoVO> listaReposicao = getReposicaoDao().consultar(rxTx.getDataAcesso(), contratoVO.getCodigo(), null, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                for (ReposicaoVO reposicaoVO : listaReposicao) {
                    marcouAula = validarDefinirHorariosTurmas(dataAcesso, reposicaoVO.getHorarioTurma(), agora);
                    if (marcouAula) {
                        return true;
                    }
                }
                if (listaReposicao.isEmpty()) {
                    List<HorarioTurmaVO> listaHorarios = getHorarioTurmaDao().horarioTurmaAlunoAgendadoHorarioDia(rxTx.getCliente().getCodigo(), rxTx.getDataAcesso());
                    for (HorarioTurmaVO horario : listaHorarios) {
                        marcouAula = validarDefinirHorariosTurmas(dataAcesso, horario, agora);
                        if (marcouAula) {
                            return true;
                        }
                    }

                }
            }
            return marcouAula;
        }
        return true;
    }

    private Date obterPrimeiroDiaSemanaAtual() {
        Calendar domingo = Calendar.getInstance();
        domingo.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        domingo.set(Calendar.HOUR, 00);
        domingo.set(Calendar.MINUTE, 00);

        return domingo.getTime();
    }

    private Date obterUltimoDiaSemanaAtual() {
        Calendar sabado = Calendar.getInstance();
        sabado.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
        sabado.set(Calendar.HOUR, 23);
        sabado.set(Calendar.MINUTE, 59);

        return sabado.getTime();
    }

    private Boolean existeAcessoNoDia(ClienteVO cliente, Date dia) {
        Boolean existeAcesso = false;

        Calendar inicioDia = Calendar.getInstance();
        inicioDia.setTime(dia);
        inicioDia.set(Calendar.HOUR, 00);
        inicioDia.set(Calendar.MINUTE, 00);

        Calendar fimDia = Calendar.getInstance();
        fimDia.setTime(dia);
        fimDia.set(Calendar.HOUR, 23);
        fimDia.set(Calendar.MINUTE, 59);

        try {
            existeAcesso = getAcessoClienteDao().consultarQtdAcessosEntreDatas(cliente, inicioDia.getTime(), fimDia.getTime(), true) > 0 ? true : false;
        } catch (Exception e) {
            Logger.getLogger(AcessoControle.class.getName()).log(Level.SEVERE, null, e);
        }

        return existeAcesso;
    }

    private Integer validarSaldoCreditoTreino(boolean estaNoHorarioPlano, boolean estaNoHorarioTurma) throws Exception {
        Integer validacaoCodigo = 0;
        if (estaNoHorarioPlano || estaNoHorarioTurma) {
            SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO = getSituacaoClienteSinteticoDWDao().consultarCliente(rxTx.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (situacaoClienteSinteticoDWVO.isValidarSaldoCreditoTreino()) {
                if (!(contratoNaoCreditoEstaLiberandoAcesso())) {
                    for (ContratoVO contratoVO : this.listaContratos) {
                        if (contratoVO.isVendaCreditoTreino()) {
                            Integer limiteAcessosSemana = contratoVO.getContratoDuracaoCreditoTreinoVO().getNumeroVezesSemana();

                            ClienteVO clienteVO = getClienteDao().consultarPorCodigo(rxTx.getCliente().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
                            Integer totalAcessosClienteSemana = getAcessoClienteDao().consultarQtdAcessosEntreDatas(clienteVO,
                                    obterPrimeiroDiaSemanaAtual(),
                                    obterUltimoDiaSemanaAtual(),
                                    true);

                            Boolean clienteAcessouNesseDia = existeAcessoNoDia(clienteVO, rxTx.getDataAcesso());
                            if (!clienteAcessouNesseDia) {
                                totalAcessosClienteSemana++;
                            }

                            // Só aplica a regra de vezes por semana se for LIVRE_OBRIGATÓRIO_MARCAR_AULA. Veja #11059
                            if (contratoVO.getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum()
                                    .equals(TipoHorarioCreditoTreinoEnum.LIVRE_OBRIGATORIO_MARCAR_AULA)) {

                                if (totalAcessosClienteSemana > limiteAcessosSemana) {
                                    validacaoCodigo = 2;
                                }
                            }

                            if (contratoVO.getContratoDuracaoCreditoTreinoVO().getQuantidadeCreditoDisponivel() <= 0) {
                                if (clienteAcessouNesseDia) {
                                    boolean diminuirCredito = false;
                                    AcessoClienteVO ultimoAcesso = getAcessoClienteDao().consultarUltimoAcessoComEmpresa(clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                                    if (ultimoAcesso != null) {
                                        long minutosAposUltimoAcesso = Calendario.diferencaEmMinutos((ultimoAcesso.getDataHoraSaida() != null) ? ultimoAcesso.getDataHoraSaida() : ultimoAcesso.getDataHoraEntrada(), rxTx.getDataAcesso());
                                        diminuirCredito = (Math.abs(minutosAposUltimoAcesso) >= ultimoAcesso.getCliente().getEmpresa().getMinutosAposUltimoAcessoDiminuirCredito());
                                    }
                                    if (diminuirCredito) {
                                        validacaoCodigo = 1;
                                    }
                                } else {
                                    validacaoCodigo = 1;
                                }
                            }

                            return validacaoCodigo;
                        }
                    }
                }
            }
        }
        return validacaoCodigo;
    }

    private boolean contratoNaoCreditoEstaLiberandoAcesso() throws Exception {

        // Verifica se tem algum contrato que não é de crédito está liberando o acesso pelo horário do plano.
        for (ContratoVO contratoVO : this.listaContratos) {
            if (contratoVO.isVendaCreditoTreino()) {
                continue;
            }
            HorarioVO horario = contratoVO.getContratoHorario().getHorario();
            if ((horario.getCodigo() > 0) && (validarHorario(horario, rxTx.getDataAcesso()))) {
                if (validarFrequenciaAcessoPlano(contratoVO)) {
                    return true;
                }
            }
        }
        // Verifica se tem algum contrato que não é de crédito está liberando o acesso pelo horário da turma.
        for (ContratoVO contratoVO : this.listaContratos) {
            if (contratoVO.isVendaCreditoTreino()) {
                continue;
            }
            List<ContratoModalidadeVO> listaContratoModalidade = contratoVO.getContratoModalidadeVOs();
            for (Iterator<ContratoModalidadeVO> it = listaContratoModalidade.iterator(); it.hasNext(); ) {
                ContratoModalidadeVO contratoMododalidade = (ContratoModalidadeVO) it.next();
                if (verificarHorarioDaTurma(rxTx.getDataAcesso(), contratoMododalidade, contratoVO.getCodigo())) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean validarFrequenciaAcessoPlano(ContratoVO contrato) throws Exception {
        TipoFrequenciaEnum tipoFrequenciaEnum = null;
        Integer quantidadeMaximaFrequencia = 0;
        if ((contrato.getQuantidadeMaximaFrequencia() != null) && (contrato.getQuantidadeMaximaFrequencia() > 0)) {
            tipoFrequenciaEnum = TipoFrequenciaEnum.getTipoFrequenciaEnum(contrato.getPlano().getTipoFrequencia());
            quantidadeMaximaFrequencia = contrato.getQuantidadeMaximaFrequencia();
        }
        if ((tipoFrequenciaEnum != null) && (tipoFrequenciaEnum == TipoFrequenciaEnum.SEMANAL)) {
            Integer qtdeFrequenciaCliente = getSituacaoClienteSinteticoDWDao().consultarFrequenciaSemanaAtual(rxTx.getCliente().getCodigo());
            return (qtdeFrequenciaCliente.intValue() < quantidadeMaximaFrequencia);
        }
        return true;

    }

    public boolean validarHorario(HorarioVO horario, Date data)
            throws NoSuchMethodException, IllegalAccessException,
            IllegalArgumentException, InvocationTargetException, Exception {
        return validarHorario(horario, data, true);
    }

    public boolean validarHorario(HorarioVO horario, Date data, boolean usaTolerancia)
            throws NoSuchMethodException, IllegalAccessException,
            IllegalArgumentException, InvocationTargetException, Exception {

        if (horario.getLivre()) {
            if (getValidacaoAcessoOffline()) {
                return definirHorariosPlanosPermitidosParaOffline(horario, null);
            } else {
                return true;
            }
        }
        Calendar dataAcesso = super.getInstanceCalendar(rxTx.getTz());
        dataAcesso.setTime(data);

        List<HorarioDisponibilidadeVO> disponibilidades =
                getHorarioDisponibilidadeDao().consultarHorarioDisponibilidades(
                        horario.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        for (HorarioDisponibilidadeVO disponibilidade : disponibilidades) {
            if (disponibilidade.getIdentificador().equals(Uteis.retornaDescricaoDiaSemana(dataAcesso))) {
                if (getValidacaoAcessoOffline()) {
                    return definirHorariosPlanosPermitidosParaOffline(horario, disponibilidade);
                }

                int hr1 = 0;
                String min1 = "00";

                hr1 = dataAcesso.get(Calendar.HOUR_OF_DAY);

                if (dataAcesso.get(Calendar.MINUTE) >= 30) {
                    min1 = "30";
                }

                DecimalFormat fmt = new DecimalFormat("00");
                Class cls = disponibilidade.getClass();
                Method meth = cls.getMethod("getHora" + fmt.format(hr1) + min1, null);
                Boolean hrIni = (Boolean) meth.invoke(disponibilidade, null);
                if (hrIni) {
                    return true;
                }
            }
        }

        if (usaTolerancia) {
            if (rxTx.getLocal() != null && rxTx.getLocal().getTempoToleranciaSaida() > 0) {// reduz a tolerancia para verificar disponibilidade
                dataAcesso.add(Calendar.MINUTE, -(rxTx.getLocal().getTempoToleranciaSaida()));
                return validarHorario(horario, dataAcesso.getTime(), false);
            }
        }

        return false;
    }

    private boolean verificarHorarioDaTurma(Date data, ContratoModalidadeVO contratoMod, Integer codContrato) throws Exception {
        boolean estaNoHorario = false;
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
        Calendar dataAcesso = super.getInstanceCalendar(rxTx.getTz());
        dataAcesso.setTime(data);
        //Separo hora de minutos
        String horaAcesso = Uteis.getDataAplicandoFormatacao(data, "HH:mm");
        Date agora = formatter.parse(horaAcesso);
        List<ContratoModalidadeTurmaVO> listaModTurmas = getContratoModalidadeTurmaDao().consultarContratoModalidadeTurmas(contratoMod.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        for (ContratoModalidadeTurmaVO contratoModTurma : listaModTurmas) {

            List<ContratoModalidadeHorarioTurmaVO> listaContModHorarioTurmaVO =
                    getContratoModalidadeHorarioTurmaDao().consultarPorCodigoContratoModalidadeTurma(
                            contratoModTurma.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            //Percorro lista de horarios da ContratoModalidadeHorarioTurmaVO para validar o horário da turma associado a este contrato modalidade
            for (Iterator<ContratoModalidadeHorarioTurmaVO> itContModHorarioTurma = listaContModHorarioTurmaVO.iterator(); itContModHorarioTurma.hasNext(); ) {

                HorarioTurmaVO hrTurmaVO = itContModHorarioTurma.next().getHorarioTurma();

                boolean existemReposicoes = getReposicaoDao().existemReposicoesPorHorarioTurmaOrigem(hrTurmaVO, Calendario.periodoSQL(data, data), codContrato);

                // Buscar dados do horário da turma.
                hrTurmaVO = getHorarioTurmaDao().consultarPorCodigo(hrTurmaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                if ((dataAcesso.get(Calendar.DAY_OF_WEEK) == hrTurmaVO.getDiaSemanaNumero()) && (desmarcouAula(data, hrTurmaVO, codContrato, false))) {
                    continue;
                }

                if (getMatriculaAlunoHorarioTurmaDAO().existeMatriculaVigenteNoHorario(data, contratoMod.getContrato(), hrTurmaVO.getCodigo())) {
                    estaNoHorario = validarDefinirHorariosTurmas(dataAcesso, hrTurmaVO, agora);
                }
                if (existemReposicoes) {
                    List<ReposicaoVO> reposicoes = getReposicaoDao().consultarReposicoesPorHorarioTurmaOrigem(hrTurmaVO, Calendario.periodoSQL(data, data), Uteis.NIVELMONTARDADOS_CONSULTA_WS, null);
                    for (ReposicaoVO reposicao : reposicoes) {
                        hrTurmaVO = reposicao.getHorarioTurma();

                        estaNoHorario = estaNoHorario || validarDefinirHorariosTurmas(dataAcesso, hrTurmaVO, agora);
                    }
                }
                if (estaNoHorario) {
                    setHorarioTurmaVO(hrTurmaVO);
                    break;
                }
            }
            if (estaNoHorario) {
                break;
            }
        }
        if (!estaNoHorario) {
            estaNoHorario = registrarHorarioReposicaoForaHorarioContratoTurmaModalidade(codContrato, data, agora, dataAcesso, estaNoHorario);
        }
        return estaNoHorario;
    }

    private boolean desmarcouAula(Date dataAcesso, HorarioTurmaVO horarioTurmaVO, Integer codContrato, boolean presenca) throws Exception {
        AulaDesmarcadaVO aulaDesmarcadaVO = getAulaDesmarcadaDao().consultarAulaDesmarcada(horarioTurmaVO, dataAcesso, codContrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getCodigo()")) {
            if (aulaDesmarcadaVO.getDataReposicao() == null || presenca) { // se for para marcação de presença, não importa data de reposição
                return true;
            } else {
                // Se a data de reposição for igual a data da aula desmarcada, então significa que o aluno desmarcou a aula e depois fez uma reposição para o mesmo horário.
                Date dataReposicao = Calendario.getDataComHoraZerada(aulaDesmarcadaVO.getDataReposicao());
                Date dataAcessar = Calendario.getDataComHoraZerada(dataAcesso);
                return (dataReposicao.compareTo(dataAcessar)) != 0;
            }
        }
        return false;
    }

    public boolean registrarHorarioReposicaoForaHorarioContratoTurmaModalidade(int codContrato, Date data, Date agora, Calendar dataAcesso, boolean estaNoHorario) throws Exception {
        int horarioTurmaOrigemReposicaoAulaForaDiaTurma = getReposicaoDao().existemReposicoesPorContratoDataReposicaoPegarHorarioTurmaOrigem(Calendario.periodoSQL(data, data), codContrato);
        if (horarioTurmaOrigemReposicaoAulaForaDiaTurma != 0) {
            HorarioTurmaVO hrTurmaVO = getHorarioTurmaDao().consultarPorCodigo(horarioTurmaOrigemReposicaoAulaForaDiaTurma, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            List<ReposicaoVO> reposicoes = getReposicaoDao().consultarReposicoesPorHorarioTurmaOrigem(hrTurmaVO, Calendario.periodoSQL(data, data), Uteis.NIVELMONTARDADOS_CONSULTA_WS, codContrato);
            for (ReposicaoVO reposicao : reposicoes) {
                hrTurmaVO = reposicao.getHorarioTurma();
                estaNoHorario = estaNoHorario || validarDefinirHorariosTurmas(dataAcesso, hrTurmaVO, agora);
            }
        }
        return estaNoHorario;
    }

    private boolean validarDefinirHorariosTurmas(Calendar dataAcesso, HorarioTurmaVO hrTurmaVO, Date agora) throws Exception {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
        boolean estaNoHorario = false;

        //Verifico se o dia do acesso na semana corresponde ao dia cadastrado para a turma
        if (dataAcesso.get(Calendar.DAY_OF_WEEK) == hrTurmaVO.getDiaSemanaNumero()) {

            String horaInicial = hrTurmaVO.getHoraInicialComTolerancia();
            String horaFinal = hrTurmaVO.getHoraFinal();

            Date hInicial = formatter.parse(horaInicial);
            Date hFinal = formatter.parse(horaFinal);

            if (!UteisValidacao.emptyNumber(hrTurmaVO.getToleranciaEntradaAposMinutos())) {
                hFinal = formatter.parse(hrTurmaVO.getHoraInicialComToleranciaApos());
                hrTurmaVO.setHoraFinal(formatter.format(hFinal));
            } else if (hrTurmaVO != null && getRxTx() != null && getRxTx().getLocal() != null) {
                Calendar addTolerancia = Calendario.getInstance(hFinal);
                addTolerancia.add(Calendar.MINUTE, getRxTx().getLocal().getTempoToleranciaSaida());
                hFinal = addTolerancia.getTime();

                hrTurmaVO.setHoraFinal(formatter.format(hFinal));
            }

            definirHorariosTurmasPermitidosParaOffline(hrTurmaVO);

            if (agora.getTime() >= hInicial.getTime()
                    && agora.getTime() <= hFinal.getTime()) {
                estaNoHorario = true;
            }
        }
        return estaNoHorario;
    }

    private boolean estaNoHorarioDaTurma(Date data) throws Exception {
        if (this.listaContratos.size() <= 0) {
            //Existe autorização mas não está vinculado a um contrato
            return true;
        }
        boolean estaNoHorario = false;
        for (ContratoVO contrato : this.listaContratos) {
            //Pego a lista de modalidades do contrato
            List<ContratoModalidadeVO> listaContratoModalidade = contrato.getContratoModalidadeVOs();
            //Percorro lista de modalidades do contrato
            for (Iterator<ContratoModalidadeVO> it = listaContratoModalidade.iterator(); it.hasNext(); ) {
                ContratoModalidadeVO contratoMododalidade = (ContratoModalidadeVO) it.next();
                if (verificarHorarioDaTurma(data, contratoMododalidade, contrato.getCodigo())) {
                    estaNoHorario = true;
                    if (!getValidacaoAcessoOffline()) {
                        break;
                    }
                }
            }
            if (estaNoHorario && !getValidacaoAcessoOffline()) {
                break;
            }
        }
        return estaNoHorario;
    }

    private DirecaoAcessoEnum definirSentidoAcessoCliente(PessoaAcesso pessoaAcesso) {

        if (pessoaAcesso.getUaSentido().equals(DirecaoAcessoEnum.DA_ENTRADA.getId())) {
            /* Verifica se o dia em que a pessoa está saindo é o mesmo dia em que a pessoa entrou.
             * Se os dias forem diferentes, foi porque a pessoa entrou em um dia pela catraca e saiu sem ser pela catraca,
             *  e no outro dia acessou a academia. Desta forma, o acesso deverá ser uma nova entrada.
             */
            if ((pessoaAcesso.getUaData() != null)
                    && (Uteis.getDiaMesData(pessoaAcesso.getUaData()) != (Uteis.getDiaMesData(negocio.comuns.utilitarias.Calendario.hoje())))) {
                return DirecaoAcessoEnum.DA_ENTRADA;
            } else {
                return DirecaoAcessoEnum.DA_SAIDA;
            }

        } else if (pessoaAcesso.getUaSentido().equals(DirecaoAcessoEnum.DA_SAIDA.getId())) {
            return DirecaoAcessoEnum.DA_ENTRADA;
        } else {
            return DirecaoAcessoEnum.DA_INDEFINIDA;
        }
    }

    private boolean dvConfere(String codAcesso, Integer empresaCliente) throws Exception {
        String cartaoClienteSemDv = codAcesso.substring(0,
                codAcesso.length() - 1);
        EmpresaVO empresa = getEmpresaDao().consultarPorCodigo(empresaCliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Integer dvCartaoCliente = Uteis.gerarDV(cartaoClienteSemDv, empresa.getSomaDv());
        //Se for igual entao confere o dv
        return rxTx.getCartao().getDv().equals(dvCartaoCliente.toString());
    }

    private boolean existeAfastamentoParaDependente(Integer codClienteDependente) throws Exception {
        return getAfastamentoContratoDependenteInterfaceFacade().existeAfastamentoParaData(codClienteDependente, rxTx.getDataAcesso());
    }

    private boolean existeMsgBloqueio(Date data, Integer codigoCliente) throws Exception {

        StringBuilder msg = new StringBuilder();

        ClienteMensagemVO mensagemVO = getClienteMsgDao().consultarPorCodigoTipoMensagemECliente(
                codigoCliente, TiposMensagensEnum.CATRACA.getSigla(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        msg.append(mensagemVO.getMensagem());

        //BLOQUEIO POR OPERACAO COLETIVA - GC-1116
        if (!UteisValidacao.emptyNumber(mensagemVO.getCodigoOperacaoColetiva())) {
            Calendar dataBloqueio = super.getInstanceCalendar(rxTx.getTz());
            Calendar dataAcesso = super.getInstanceCalendar(rxTx.getTz());
            dataAcesso.setTime(data);

            if (mensagemVO.getDataBloqueio() != null) {
                dataBloqueio.setTime(mensagemVO.getDataBloqueio());

                if (mensagemVO.getDataDesbloqueio() != null) {
                    Calendar dataDesbloqueio = super.getInstanceCalendar(rxTx.getTz());
                    dataDesbloqueio.setTime(mensagemVO.getDataDesbloqueio());

                    if (dataDesbloqueio.before(dataAcesso)) {
                        return false;
                    }
                }

                if (dataBloqueio.equals(dataAcesso) || dataBloqueio.before(dataAcesso)) {
                    rxTx.getCliente().setMensagem(msg.toString());
                    return true;
                }
            }

            return false;
        }

        List mensagens = getClienteMsgDao().consultarMensagemProdutoVencidoPorCliente(codigoCliente, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        boolean bloqueioPV = false;
        if (!mensagens.isEmpty()) {
            Iterator<ClienteMensagemVO> it = mensagens.iterator();
            while (it.hasNext()) {
                if (it.next().getBloqueio()) {
                    bloqueioPV = true;
                    msg.append(!msg.toString().isEmpty() ? "\n" : "").append("Produto vencido. Por favor, dirija-se à recepção.");
                    break;
                }
            }
        }
        // consultar o saldo de creditos do aluno.
        SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO = getSituacaoClienteSinteticoDWDao().consultarCliente(codigoCliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (UtilReflection.objetoMaiorQueZero(situacaoClienteSinteticoDWVO, "getCodigo()")) {
            if (situacaoClienteSinteticoDWVO.isValidarSaldoCreditoTreino()) {
                String saldo = (situacaoClienteSinteticoDWVO.getSaldoCreditoTreino() != null) ? String.valueOf(situacaoClienteSinteticoDWVO.getSaldoCreditoTreino()) : "0";
                if (!msg.toString().isEmpty()) {
                    msg.append("\n");
                }
                msg.append("SALDO DE CREDITO: " + saldo);
            }
        }

        rxTx.getCliente().setMensagem(msg.toString());

        //Verifica se está marcado para bloquear o acesso
        if (mensagemVO.getBloqueio() || bloqueioPV) {
            return true;
        } else {
            mensagens.add(mensagemVO);
            Iterator<ClienteMensagemVO> it = mensagens.iterator();
            while (it.hasNext()) {
                ClienteMensagemVO msgVO = it.next();
                if (msgVO.getDataBloqueio() != null) {
                    Calendar dataBloqueio = super.getInstanceCalendar(rxTx.getTz());
                    Calendar dataAcesso = super.getInstanceCalendar(rxTx.getTz());
                    dataAcesso.setTime(data);

                    dataBloqueio.setTime(msgVO.getDataBloqueio());
                    //Chegou a data de bloqueio ou já ultrapassou
                    if ((dataBloqueio.equals(dataAcesso)) || (dataBloqueio.before(dataAcesso))) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /*
     * <AUTHOR>
     * Data: 30/12/10
     * Objetivo: Validar se o usuário tem permissão para acessar determinado recurso do sistema.
     */
    public RetornoRequisicaoValidarPermissaoUsuario validarPermissaoUsuario(Integer usuario, String senha, Integer empresa, String recurso) throws Exception {

        RetornoRequisicaoValidarPermissaoUsuario retorno = new RetornoRequisicaoValidarPermissaoUsuario();
        String senhaEncriptada = "";
        UsuarioVO usuarioVo = getUsuarioDao().consultarPorCodigo(usuario, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        // * Validar usuário e senha.
        if (usuarioVo == null) {
            throw new Exception("Usuário não encontrado.");
        } else {
            retorno.setCodigoUsuario(usuario);
            retorno.setNomeUsuario(usuarioVo.getNome());
            senhaEncriptada = Uteis.encriptar(senha.toUpperCase());
            if (!usuarioVo.getAdministrador()) {
                //usuarioVo.setColaboradorVO(getColaboradorDao().consultarPorCodigo(usuarioVo.getColaboradorVO().getCodigo(), 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                usuarioVo.setColaboradorVO(getColaboradorDao().consultarPorCodigoPessoa(usuarioVo.getColaboradorVO().getPessoa().getCodigo(), empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

                if (usuarioVo.getColaboradorVO().getCodigo() > 0 && usuarioVo.getColaboradorVO().getSituacao().equals("NA")) {
                    throw new Exception("Usuário Inativo.");
                }
            }
            if (!usuarioVo.getSenha().equalsIgnoreCase(senhaEncriptada) && !usuarioVo.getPin().equalsIgnoreCase(senhaEncriptada)) {
                throw new Exception("Senha ou pin inválido. Digite novamente.");
            }
        }
        // Se o usuário for administrador, o mesmo tem acesso a todos os recursos do sistema.
        if (usuarioVo.getAdministrador()) {
            retorno.setTemPemissao(true);
            return retorno;
        }

        usuarioVo.setUsuarioPerfilAcessoVOs(getUsuarioPerfilAcessoDao().consultarUsuarioPerfilAcesso(usuarioVo.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

        // * Verificar se o usuário tem permissão ao recurso do sistema.
        if (usuarioVo.getUsuarioPerfilAcessoVOs().size() > 0) {
            for (Object o : usuarioVo.getUsuarioPerfilAcessoVOs()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
                if (empresa == usuarioPerfilAcesso.getEmpresa().getCodigo().intValue()) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getPermissaoDao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    String textoFuncionalidade = new OpcoesPerfilAcesso().getPermissaoPorEntidade(recurso);
                    getControleAcessoDao().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(), usuarioVo, recurso, textoFuncionalidade);
                    // Se não ocorreu nenhuma exceção, é porque o usuário tem permissão ao recurso
                    retorno.setTemPemissao(true);
                }

            }
        } else {
            throw new Exception("Usuário não tem permissão ao recurso \" " + recurso + "\"");
        }
        return retorno;
    }

    public void registrarLiberacaoAcesso(Date data,
                                         TipoLiberacaoEnum tipoLiberacao,
                                         Integer empresa,
                                         Integer local,
                                         Integer terminal,
                                         DirecaoAcessoEnum direcao,
                                         Integer usuario,
                                         String codAcesso,
                                         String justificativa,
                                         String nomeGenerico) throws Exception {

        //By.: Waller Maciel 23/08/2011
        //Atualizar a data/hora da transação com TimeZone da Empresa
        //a data que era enviada anteriormente agora é sobreposta pela data/hora do servidor de aplicação
        data = super.getInstanceCalendar(rxTx.getTz()).getTime();

        LocalAcessoVO localAcessoVO = getLocalAcessoDao().consultarPorCodigo(local, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        ColetorVO coletorVO = getColetorDao().consultarPorNumeroTerminal(terminal.toString(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        UsuarioVO usuarioVO = getUsuarioDao().consultarPorCodigo(usuario, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        PessoaVO pessoaVO = null;
        String situacaoCliente = null;
        ClienteVO clienteVO = null;
        ColaboradorVO colaboradorVO = null;
        if (codAcesso != null && codAcesso.startsWith(TipoAcessoEnum.TA_ALUNO.getId())) {
            clienteVO = getClienteDao().consultarPorCodAcesso(codAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            pessoaVO = clienteVO.getPessoa();
            situacaoCliente = clienteVO.getSituacao();
        } else if (codAcesso != null && codAcesso.startsWith(TipoAcessoEnum.TA_COLABORADOR.getId())) {
            colaboradorVO = getColaboradorDao().consultarPorCodAcesso(codAcesso, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            pessoaVO = colaboradorVO.getPessoa();
        }

        LiberacaoAcessoVO liberacaoAcessoVO = getLiberacaoAcessoDao().registrarAcesso(data, direcao, tipoLiberacao, localAcessoVO, coletorVO, usuarioVO, empresa, pessoaVO, situacaoCliente, justificativa, nomeGenerico);

        if (clienteVO != null) {
            AcessoCliente acessoClienteDAO = new AcessoCliente(con);
            ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
            ControleCreditoTreino ccDao = new ControleCreditoTreino(con);
            getAcessoClienteDao().registrarAcessoCliente(data, clienteVO, SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO, direcao, localAcessoVO, coletorVO, usuarioVO, MeioIdentificacaoEnum.AVULSO, ccDao, getKey(), "", liberacaoAcessoVO, "", null);
            AcessoClienteVO ultimoAcesso = acessoClienteDAO.consultarUltimoAcesso(clienteVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (!UteisValidacao.emptyNumber(ultimoAcesso.getCodigo())) {
                getClienteDao().registrarUltimoAcesso(clienteVO.getCodigo(), ultimoAcesso.getCodigo());
            }
            zwFacade.atualizarSintetico(clienteVO, data,
                    SituacaoClienteSinteticoEnum.GRUPO_DADOSACESSO, false);
        }
        if (colaboradorVO != null) {
            getAcessoColaboradorDao().registrarAcessoColaborador(data, colaboradorVO, direcao, localAcessoVO, coletorVO, MeioIdentificacaoEnum.AVULSO, liberacaoAcessoVO);
        }
    }

    public void registrarAcesso(Date data, String tipo, Integer codigo, SituacaoAcessoEnum situacao,
                                DirecaoAcessoEnum direcao, Integer local, Integer terminal, Integer usuario,
                                MeioIdentificacaoEnum meioIdentificacao, String key, Integer empresa, String nomeCodEmpresaAcessou, AutorizacaoAcessoGrupoEmpresarialVO autorizacao, Integer codigoMatricula) throws Exception {

        //By.: Waller Maciel 23/08/2011
        //atualiza a data/hora da transação com TimeZone da Empresa
        //a data que era enviada anteriormente agora é sobreposta pela data/hora do servidor de aplicação
        //data = super.getInstanceCalendar(rxTx.getTz()).getTime();
        // By.: XiquiN 09/12/2012
        // Não pode atualizar a data enviada. A data e hora do servidor não interessa neste caso, pois
        // pode estar chegando um registro de acesso offline, que pode ter sido realizado ontem

        LocalAcessoVO localAcessoVO = getLocalAcessoDao().consultarPorCodigo(local, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (UteisValidacao.emptyNumber(localAcessoVO.getCodigo())) {
            return;
        }

        ColetorVO coletorVO = getColetorDao().consultarPorNumeroTerminal(terminal.toString(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        UsuarioVO usuarioVO = null;

        // Não se faz mais necessária a condição abaixo, pois o Acesso já envia para o ZW a hora do computador em que está configurado o local de acesso,
        // caso o sistema execute o método obterDataIntime as horas irão ficar incorretas pois a mesma já está no GMT correto do estado

        String timeZone = getEmpresaDao().obterTimeZoneDefault(localAcessoVO.getEmpresa().getCodigo());
        //  if (!timeZone.equals("Brazil/East")) {
        //      data = Uteis.obterDataInTimeZone(data, timeZone);
        //  }
        if (usuario > 0) {
            usuarioVO = getUsuarioDao().consultarPorCodigo(usuario, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        }

        if (coletorVO.getCodigo() != 0
                && localAcessoVO.getCodigo() != 0) {
            if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipo)) {
                //Registrar acesso para clientes
                ClienteVO cli = new ClienteVO();
                if (!UteisValidacao.emptyNumber(codigoMatricula)) {
                    cli = getClienteDao().consultarPorCodigoMatricula(codigoMatricula, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } else {
                    cli.setCodigo(codigo);
                }
                cli.setUsuarioAux(getZwFacade().getUsuarioRecorrencia());

                String ultimoAcesso = getUltimosAcessosRegistrados().get(key);
                if (!verificarUltimoAcessoRegistrado(ultimoAcesso, data, cli, situacao, localAcessoVO)) {
                    AcessoClienteVO acResumido = getAcessoClienteDao().registrarAcessoCliente(
                            data, cli, situacao, direcao, localAcessoVO, coletorVO, usuarioVO,
                            meioIdentificacao, getControleCreditoTreino(), key, timeZone, nomeCodEmpresaAcessou, autorizacao);

                    if (situacao.isBloqueado()) {
                        return;
                    }

                    registrarPeriodoAcessoGympass(cli, situacao, direcao);
                    registrarUltimoAcesso(key, data, cli, situacao, localAcessoVO);

                    // Registrar ultimo acesso do cliente.
                    if (direcao == DirecaoAcessoEnum.DA_ENTRADA || (acResumido.getDataHoraEntrada() != null && acResumido.getDataHoraSaida() != null && acResumido.getDataHoraEntrada().equals(acResumido.getDataHoraSaida()))) {
                        getClienteDao().registrarUltimoAcesso(codigo, acResumido.getCodigo());
                        getSituacaoClienteSinteticoDWDao().registrarUltimoAcesso(codigo, data);
                        if (getUsuarioMovelDao().temUsuarioMovel(codigo)) {
                            TreinoWSConsumer.atualizarStatusAluno(getKey(), codigo, data, localAcessoVO.getEmpresa().getCodigo());
                        }
                        verificarClienteParaEnvioSmsAutomatico(cli);
                        if (configuracaoSistemaVO.isMarcarPresencaPeloAcesso()) {
                            marcarPresenca(cli, data, localAcessoVO.getEmpresa().getCodigo(), usuarioVO);
                            marcarPresencaAulaCheia(cli, data, localAcessoVO.getEmpresa().getCodigo(), null);
                        }
                        if (getEmpresaDao().integracaoMyWellnesHabilitada(localAcessoVO.getEmpresa().getCodigo(), false)) {
                            getZwFacade().startThreadMyWellness(cli.getCodigo(), null, true, null);
                        }
                    } else if (direcao == DirecaoAcessoEnum.DA_SAIDA) {
                        getSituacaoClienteSinteticoDWDao().registrarUltimoAcessoSaida(codigo, data);
                    }
                    enviarAcesso(data, empresa);
                    enviarAcessoIntegracaoPratique(data, empresa, meioIdentificacao.name(), direcao.getId());
                }
            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipo)) {
                //Registrar acesso para colaboradores
                ColaboradorVO colaborador = getColaboradorDao().consultarPorCodigo(codigo, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                // consultar último acesso do colaborador.
                if (colaborador != null && colaborador.getCodigo() != 0) {
                    Integer codigoAcesso = getAcessoColaboradorDao().registrarAcessoColaborador(data, colaborador, direcao, localAcessoVO, coletorVO, meioIdentificacao);
                    // Registrar ultimo acesso do colaborador.
                    if (direcao == DirecaoAcessoEnum.DA_ENTRADA) {
                        getColaboradorDao().registrarUltimoAcesso(colaborador.getCodigo(), codigoAcesso);
                    }
                } else {
                    throw new Exception("Colaborador não encontrado. Código " + codigo);
                }
            }
        }
    }

    private double calculaValorAcessoGympass(int cliente) throws Exception {
        double valorRetorno = 0.0;
        // busca a data de inicio do gympass na academia
        Date dataInicioGympassAcademia = getGympass().buscarDataInicio();
        // conta a qtde acessos gympass
        int qtdeAcessosCliente = getPeriodoAcessoDao().contarQtdAcessosPorClienteComGympassAPartirData(cliente, dataInicioGympassAcademia);
        // conta a qtde acessos gympass no mes atual
        int qtdeAcessosClienteNoMes = getPeriodoAcessoDao().contarQtdAcessosDoClienteComGympassNoMes(cliente, Uteis.getMesData(new Date()));

        GympassVO gympassVOAtual = getGympass().buscarPorTabelaAtiva(new Date(), Uteis.NIVELMONTARDADOS_TODOS);
        int progressaoValoresNoMes = gympassVOAtual.getDias().size() - 1;
        double somaDosAcessosDoCliente = 0.0;
        double valorMaximo = gympassVOAtual.getValorMaximo();
        int sequencia = qtdeAcessosCliente > progressaoValoresNoMes ? progressaoValoresNoMes : qtdeAcessosCliente;
        // soma os acessos do cliente para saber se extrapola o valor maximo no mes
        for (int i = 0; i <= qtdeAcessosClienteNoMes; i++) {
            int indice = qtdeAcessosCliente > qtdeAcessosClienteNoMes ? sequencia : i < sequencia ? i : sequencia;
            if (!gympassVOAtual.getDias().isEmpty()) {
                valorRetorno = gympassVOAtual.getDias().get(indice).getValorDia();
            }
            valorRetorno = somaDosAcessosDoCliente + valorRetorno > valorMaximo ? valorMaximo - somaDosAcessosDoCliente : valorRetorno;
            // se passar o valor nao precisa mais processar
            if (valorRetorno == 0.0) break;
            somaDosAcessosDoCliente += valorRetorno;
        }
        return valorRetorno;
    }

    private void registrarPeriodoAcessoGympass(ClienteVO clienteVO, SituacaoAcessoEnum situacao, DirecaoAcessoEnum direcao) throws Exception {
        // somente ENTRADAS com GYMPASS
        if (direcao == DirecaoAcessoEnum.DA_ENTRADA && situacao == SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADOGYMPASS) {
            ClienteVO cliente = getClienteDao().consultarPorCodigo(clienteVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_GYMPASS);
            double valor = calculaValorAcessoGympass(clienteVO.getCodigo());
            Date hoje = new Date();
            PeriodoAcessoClienteVO acesso = new PeriodoAcessoClienteVO();
            acesso.setPessoa(cliente.getPessoa().getCodigo());
            acesso.setResponsavel(clienteVO.getUsuarioAux().getCodigo());
            acesso.setTipoAcesso("PL");
            acesso.setDataInicioAcesso(hoje);
            acesso.setDataFinalAcesso(hoje);
            acesso.setDataLancamento(hoje);
            acesso.setTokenGymPass(cliente.getGympasUniqueToken());
            acesso.setTipoGymPass(cliente.getGympassTypeNumber());
            acesso.setValorGympass(valor);
            acesso.setCodigoEmpresa(cliente.getEmpresa().getCodigo());
            getPeriodoAcessoDao().incluir(acesso);
        }
    }

    private void registrarUltimoAcesso(String key, Date data, ClienteVO cli, SituacaoAcessoEnum situacao, LocalAcessoVO localAcessoVO) {
        String ultimoAcesso = data.getTime() + cli.getCodigo() + situacao.toString() + localAcessoVO.getCodigo();
        getUltimosAcessosRegistrados().put(key, ultimoAcesso);
    }

    private boolean verificarUltimoAcessoRegistrado(String ultimoAcesso, Date data, ClienteVO cli, SituacaoAcessoEnum situacao, LocalAcessoVO localAcessoVO) {
        String ultimoAcessoReg = data.getTime() + cli.getCodigo() + situacao.toString() + localAcessoVO.getCodigo();
        return ultimoAcesso != null && ultimoAcesso.equals(ultimoAcessoReg);
    }

    /**
     * Envia SMS para cada colaborador vinculado ao cliente dentro das seguintes
     * condições: * Cliente que está em alguma classificacao que permite enviar
     * sms automatico * Cliente com risco 6,7 ou 8 * Proximidade do vencimento
     * para menos de 15 dias ou já vencido.
     */
    public void verificarClienteParaEnvioSmsAutomatico(ClienteVO cli) throws Exception {
        try {
            //verificar se a configuracao do sistema permite enviar sms automatico
            if (configuracaoSistemaVO.isEnviarSMSAutomatico()) {
                ClienteVO cliente = getSituacaoClienteSinteticoDWDao().consultarEnvioSMS(cli);
                if (cliente == null) {
                    return;
                }
                boolean contratoVencidoOuMenos15DiasAVencer = false;
                if (cliente.getSituacaoClienteSinteticoVO().getDataVigenciaAteAjustada() != null
                        && cliente.getSituacaoClienteSinteticoVO().getDataRenovacaoContrato() == null) {
                    contratoVencidoOuMenos15DiasAVencer = Calendario.maiorOuIgual(
                            Uteis.obterDataFutura(Calendario.hoje(), 16),
                            cliente.getSituacaoClienteSinteticoVO().getDataVigenciaAteAjustada());
                }
                //verificar se aluno está em risco OU aluno está em periodo de vencimento OU cliente está com alguma classificação marcada com a opção de envio
                if ((cliente.getSituacaoClienteSinteticoVO().getPesoRisco() != null
                        && cliente.getSituacaoClienteSinteticoVO().getPesoRisco() >= 6
                        && !cliente.getSituacaoClienteSinteticoVO().getPesoRisco().equals(cliente.getSituacaoClienteSinteticoVO().getSMSRisco()))
                        || contratoVencidoOuMenos15DiasAVencer
                        || cliente.getSituacaoClienteSinteticoVO().isEnvioSMSMarcadoClassif()) {
                    enviarSMSAutomaticoParaColaboradoresVinculados(cliente, getKey(), contratoVencidoOuMenos15DiasAVencer);
                    cliente.getSituacaoClienteSinteticoVO().setSMSRisco(cliente.getSituacaoClienteSinteticoVO().getPesoRisco());
                    getSituacaoClienteSinteticoDWDao().atualizarSMSRisco(cliente.getSituacaoClienteSinteticoVO().getSMSRisco(), cliente.getCodigo());
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(AcessoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void enviarSMSAutomaticoParaColaboradoresVinculados(ClienteVO cliente, String key, boolean contratoVencidoOuMenos15DiasAVencer) throws Exception {
        String[] telefones = null;
        String retorno;
        if (!cliente.getSituacaoClienteSinteticoVO().getTelCelColVinculados().isEmpty()) {
            //separar os telefones
            telefones = cliente.getSituacaoClienteSinteticoVO().getTelCelColVinculados().split(";");
        }
        String tokenSMS = getEmpresaDao().obterTokenSMS(cliente.getEmpresa().getCodigo());
        SmsController smsController = new SmsController(tokenSMS, key, TimeZone.getTimeZone(cliente.getEmpresa().getTimeZoneDefault()));


        //enviar SMS para colaborador vinculado
        retorno = smsController.sendMessage(null, montarListaBeans(telefones,
                montarMensagemSMS(cliente,
                        contratoVencidoOuMenos15DiasAVencer).toString()));
        if (!"".equals(retorno) && !"ok".equals(retorno)) {
            Logger.getLogger(SMSControle.class.getName()).log(Level.SEVERE, retorno);
        }
    }

    public List<Message> montarListaBeans(String[] telefones, String mensagem) throws Exception {
        List<Message> listaBeans = new ArrayList<>();
        if (telefones != null) {
            for (String tel : telefones) {
                Message beanSms = new Message();
                beanSms.setMsg(mensagem);
                beanSms.setNumero(tel);
                listaBeans.add(beanSms);
            }
        }
        return listaBeans;
    }

    public StringBuilder montarMensagemSMS(ClienteVO cliente, boolean contratoVencidoOuMenos15DiasAVencer) throws Exception {
        //Nomeando o aluno
        StringBuilder mensagem = new StringBuilder("");
        if (cliente.getPessoa().getSexo().equals("M")) {
            mensagem.append("Seu aluno ");
        } else if (cliente.getPessoa().getSexo().equals("F")) {
            mensagem.append("Sua aluna ");
        } else {
            mensagem.append("Seu aluno(a) ");
        }
        mensagem.append(Uteis.obterPrimeiroNomeConcatenadoSobreNome(cliente.getSituacaoClienteSinteticoVO().getNomeCliente()));
        mensagem.append(" chegou. ");

        //Adicionando a informação do risco, caso necessário.
        if ((cliente.getSituacaoClienteSinteticoVO().getPesoRisco() != null
                && cliente.getSituacaoClienteSinteticoVO().getPesoRisco() >= 6)) {
            mensagem.append("Seu risco e ").append(cliente.getSituacaoClienteSinteticoVO().getPesoRisco()).append(".");

            if (cliente.getSituacaoClienteSinteticoVO().getDataUltimoAcesso() == null) {
                if (cliente.getPessoa().getSexo().equals("M")) {
                    mensagem.append(" Este aluno nao possuia acesso.");
                } else if (cliente.getPessoa().getSexo().equals("F")) {
                    mensagem.append(" Esta aluna nao possuia acesso.");
                } else {
                    mensagem.append(" Este aluno(a) nao possuia acesso.");
                }

            } else {
                mensagem.append(" Faz ");
                mensagem.append(Uteis.nrDiasEntreDatas(cliente.getSituacaoClienteSinteticoVO().getDataUltimoAcesso(), Calendario.hoje()));
                mensagem.append(" dias que acessou.");
            }
        }

        //Adicionando a informação do contrato, caso necessário.
        if (contratoVencidoOuMenos15DiasAVencer) {
            mensagem.append(" Seu contrato e de ");
            mensagem.append(cliente.getSituacaoClienteSinteticoVO().getDuracaoContratoMeses());
            mensagem.append(cliente.getSituacaoClienteSinteticoVO().getDuracaoContratoMeses() == 1 ? " mes" : " meses");
            long diasContrato = Uteis.nrDiasEntreDatas(cliente.getSituacaoClienteSinteticoVO().getDataVigenciaAteAjustada(), Calendario.hoje());
            if (diasContrato <= 0) {
                mensagem.append(" que ira vencer em ").append(Math.abs(diasContrato)).append((Math.abs(diasContrato) == 1) ? " dia." : " dias.");
            } else {
                mensagem.append((diasContrato == 1) ? " faz " : " fazem ").append(diasContrato).append((diasContrato == 1) ? " dia " : " dias ").append(" que venceu.");
            }
        }
        return mensagem;
    }

    private boolean existeColaboradorCadastrado(String codigo, Integer empresa, Integer localAcesso, boolean leitorSerial) throws Exception {

        ColaboradorVO colaborador = null;
        if (rxTx.isForcarCodigoAlternativo() && leitorSerial) {
            colaborador = getColaboradorDao().consultarPorCodAlternativo(codigo, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } else {
            if (codigo.length() == 10) {
                colaborador = getColaboradorDao().consultarPorCodAcesso(codigo, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else if (codigo.length() == 8) {
                colaborador = getColaboradorDao().consultarPorCodAlternativo(codigo, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
        }

        if (colaborador == null) {
            return false;
        }

        // Consultar o último acesso do colaborador por local de acesso.
        colaborador.setUaColaborador(getAcessoColaboradorDao().consultarUltimoAcessoPorLocal(colaborador, rxTx.getDataAcesso(), localAcesso, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        // Consultar dados de pessoa.
        colaborador.setPessoa(getPessoaDao().consultarPorCodigo(colaborador.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        // preencher foto... ou não
        colaborador.getPessoa().setFoto(obterFotoPessoaSeNaoEnviada(getKey(),
                colaborador.getPessoa().getCodigo(), localAcesso));

        rxTx.getCliente().setCodigo(colaborador.getCodigo());
        rxTx.getCliente().setEmpresa(colaborador.getEmpresa().getCodigo());
        rxTx.getCliente().setMatricula(colaborador.getCodAcesso());
        rxTx.getCliente().setSituacao(colaborador.getSituacao());
        rxTx.getCliente().setPessoa(colaborador.getPessoa());
        rxTx.getCliente().setUaData(colaborador.getUaColaborador().getDataHoraEntrada());
        rxTx.getCliente().setUaSentido(colaborador.getUaColaborador().getSentido());

        return true;

    }

    public List<ClienteVO> consultarClientesPeloNome(String nome, Integer empresa, int maxResults) throws Exception {
        List<ClienteVO> lista = getClienteDao().consultarPorNomeCliente(
                nome, empresa, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, maxResults);
        montarDadosPessoa(lista);
        return lista;
    }

    public List<ClienteVO> consultarClientesPeloCpf(String cpf, int maxResults) throws Exception {
        List<ClienteVO> lista = getClienteDao().consultarClientesPorCfp(
                cpf, null, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, maxResults);
        montarDadosPessoa(lista);
        return lista;
    }

    public List<ClienteVO> consultarClientesPelaMatricula(String matricula, Integer empresa) throws Exception {
        List<ClienteVO> lista = getClienteDao().consultarPorMatriculaComLimite(matricula, empresa, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (matricula.length() >= 10) {
            List<ClienteVO> listaCodAcesso = getClienteDao().consultarPorCodAcesso(matricula, empresa, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            lista.addAll(listaCodAcesso);
        }
        montarDadosPessoa(lista);
        return lista;
    }

    public List<ClienteVO> consultarClientesPelaMatriculaSemLimite(String matricula, Integer empresa) throws Exception {
        List<ClienteVO> lista = getClienteDao().consultarPorMatricula(
                matricula, empresa, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        montarDadosPessoa(lista);
        return lista;
    }

    private void montarDadosPessoa(List<ClienteVO> listaCliente) throws Exception {
        for (ClienteVO cliente : listaCliente) {
            Integer codPessoa = cliente.getPessoa().getCodigo();
            cliente.setPessoa(getPessoaDao().consultarPorCodigo(codPessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            cliente.getPessoa().setAssinaturaBiometriaFacial(getPessoaDao().obterAssinaturaBiometriaFacial(codPessoa));
            cliente.getPessoa().setAssinaturaBiometriaDigital(getPessoaDao().obterAssinaturaBiometriaDigital(codPessoa));
            cliente.getPessoa().setEnderecoVOs(getEnderecoDao().consultarEnderecos(codPessoa, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            cliente.getPessoa().setTelefoneVOs(getTelefoneDao().consultarTelefones(codPessoa, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            cliente.getPessoa().setEmailVOs(getEmailDao().consultarEmails(codPessoa, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        }

    }

    public List<ColaboradorVO> consultarProfessorPeloNome(String nome, Integer empresa) throws Exception {
        // Consultar dados básico de colaborador.
        List<ColaboradorVO> lista = getColaboradorDao().consultarPorNomeTipoColaborador(nome,
                empresa, false, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, TipoColaboradorEnum.PROFESSOR);
        // Montar dados de pessoa.
        for (ColaboradorVO colaborador : lista) {
            colaborador.setPessoa(getPessoaDao().consultarPorCodigo(colaborador.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        }
        return lista;
    }

    public void gravarLogSolicitacaoConfLocal(Integer codigoEmpresa, String nomeComputador) throws Exception {
        LogControleUsabilidadeVO obj = new LogControleUsabilidadeVO();
        EmpresaVO empresa = new EmpresaVO();
        empresa.setCodigo(codigoEmpresa);
        obj.setEmpresa(empresa);
        obj.setAcao("EnviarDadosLocal");
        obj.setEntidade("WSACESSO");
        obj.setMaquina(nomeComputador);
        obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setChavePrimaria(0);
        getLogControleUsabilidadeDao().incluirLog(obj);
    }

    private SituacaoAcessoEnum bloquearPersonalTrainer(String codigo, boolean leitorSerial, Integer codEmpresa) throws Exception {
        boolean personal = false;
        // foi necessario separar este metodo da validacao de colaborador pois a mensagem
        // de erro que precisa retornar é diferente
        ColaboradorVO colaborador = null;
        if (codigo.length() == 8 || (rxTx.isForcarCodigoAlternativo() && leitorSerial)) {
            colaborador = getColaboradorDao().consultarPorCodAlternativo(codigo, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } else if (codigo.length() == 10) {
            colaborador = getColaboradorDao().consultarPorCodAcesso(codigo, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        }

        // o colaborador nunca será null pois o metodo existeColaboradorCadastrado()
        // já testou esta situacao
        if (colaborador == null) {
            return SituacaoAcessoEnum.RV_BLOQPERSONAL;
        }

        List<ColaboradorVO> colaboradoresPessoas = getColaboradorDao().consultarPorCodigoPessoa(colaborador.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (colaboradoresPessoas.size() > 1) {
            for (ColaboradorVO col : colaboradoresPessoas) {
                if (col.getEmpresa().getCodigo().equals(rxTx.getLocal().getEmpresa().getCodigo())) {
                    colaborador = col;
                    break;
                }
            }
        }

        // busca os tipos que o colaborador possui
        List<TipoColaboradorVO> tipos = getTipoColaboradorDao().consultarPorCodigoColaborador(
                colaborador.getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        boolean personalInterno = false;
        // percorre a lista de tipos
        for (TipoColaboradorVO tipo : tipos) {
            // se colaborador é personal trainer
            if (tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_TRAINER.getSigla())
                    || tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla())
                    || tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla())) {
                personal = true;
                personalInterno = tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla());
            }
        }
        EmpresaVO empresa = getEmpresaDao().consultarPorChavePrimaria(colaborador.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (!empresa.getLiberarPersonalProfessorDebito() && !empresa.isLiberarPersonalComTaxaEmAberto() && personal) {
            //Avaliará se existem parcelas vencidas;
            List<MovParcelaVO> parcelas = getMovParcelaDao().consultarParcelasEmAbertoPorPersonal(colaborador.getEmpresa().getCodigo(), colaborador.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            // percorre a lista de parcelas
            for (MovParcelaVO parcela : parcelas) {
                // se tem alguma parcela vencida bloqueia o personal
                if (Calendario.maior(Calendario.hoje(), parcela.getDataVencimento())) {
                    return SituacaoAcessoEnum.RV_BLOQPERSONAL;
                }
            }
            // se nenhuma das situacoes acima entao nao bloqueia
        }
        if (empresa.isUsarGestaoCreditosPersonal()) {
            //se usa a gestão de personal validar de forma diferente
            //se o personal for interno, não bloqueia
            if (!personal || personalInterno) {
                return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
            }

            if (colaborador.getBloquearAcessoSemCheckin()) {
                try {
                    String urlTreino = PropsService.getPropertyValue(getKey(), PropsService.urlTreino);
                    String dados = ExecuteRequestHttpService.executeHttpRequestGETEncode(urlTreino + "/prest/professor/"
                            + getKey() + "/em-atendimento/" + colaborador.getCodigo(), new HashMap<>(), "UTF-8");
                    JSONObject json = new JSONObject(dados);
                    Boolean emAtendimento = json.optBoolean("return");
                    if (emAtendimento != null && !emAtendimento) {
                        return SituacaoAcessoEnum.RV_BLOQPERSONAL;
                    }
                } catch (Exception e) {
                    Uteis.logar(e.getMessage());
                }
            }

            //se o colaborador for pós pago
            if (colaborador.getUsoCreditosPersonal() != null
                    && colaborador.getUsoCreditosPersonal().equals(UsoCreditoPersonalEnum.PERMITIR_POS_PAGO.getCodigo())) {
                //validar parcela em aberto
                MovParcelaVO parcelaMaisAntiga = getMovParcelaDao().consultarParcelaEmAbertoMaisAntiga(colaborador.getPessoa().getCodigo());
                if (parcelaMaisAntiga == null) {
                    return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                }
                Long nrDiasParcelaAberta = Uteis.nrDiasEntreDatas(parcelaMaisAntiga.getDataVencimento(), Calendario.hoje());
                if (empresa.getConfigsPersonal().getDiasBloqueioParcelaEmAberto() > 0
                        && nrDiasParcelaAberta > empresa.getConfigsPersonal().getDiasBloqueioParcelaEmAberto()) {
                    return SituacaoAcessoEnum.RV_BLOQPERSONAL;
                } else {
                    return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                }
                //se o colaborador for pré-pago
            } else {
                if (!colaborador.isEmAtendimentoPersonal() && colaborador.getSaldoCreditoPersonal() <= 0
                        && empresa.getConfigsPersonal().isBloquearAcessoPersonalSemCredito()) {
                    return SituacaoAcessoEnum.RV_BLOQPERSONAL;
                } else {
                    return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                }
            }
        } else if (personal) {
            // se colaborador é personal trainer

            //possui plano personal vigente
            configuracaoSistemaVO = getConfiguracaoDao().buscarPorCodigo(1, false,
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            List<ControleTaxaPersonalVO> taxasPersonal = getTaxapersonalDao().consultarPorPessoaColaborador(colaborador.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
            for (ControleTaxaPersonalVO controle : taxasPersonal) {
                if (controle.getPlano() != null
                        && controle.getPlano().getCodigo() > 0
                        && Calendario.entre(Calendario.hoje(), controle.getDataInicioVigenciaPlano(), controle.getDataFimVigenciaPlano())
                        && !controle.getSituacao().equals("Cancelado")) {

                    //Avaliará se existem parcelas vencidas;
                    if (getMovParcelaDao().existeParcelaVencidaSegundoConfiguracoes(
                            0, 0, empresa, configuracaoSistemaVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, controle.getCodigo())){
                        return SituacaoAcessoEnum.RV_BLOQPERSONAL;
                    }

                    if (codEmpresa == controle.getPlano().getEmpresa().getCodigo()) {
                        return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                    }

                    boolean autorizado;
                    if (configuracaoSistemaVO.isControleAcessoMultiplasEmpresasPorPlano()) {
                        PlanoEmpresa planoEmpresaDAO = new PlanoEmpresa(con);
                        autorizado = planoEmpresaDAO.permiteAcessosEmpresaPlanoPersonal(taxasPersonal, codEmpresa);
                        planoEmpresaDAO = null;
                        if (!autorizado) {
                            return SituacaoAcessoEnum.RV_BLOQPLANOEMPRESA_PERSONAL;
                        } else {
                            return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                        }
                    } else {
                        return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                    }
                }
            }

            // busca as parcelas geradas para o personal no mes
            List<MovParcelaVO> parcelas = getMovParcelaDao().consultarPorMesReferenciaPersonal(colaborador.getEmpresa().getCodigo(), colaborador.getCodigo(),
                    Uteis.getMesReferencia(Calendario.hoje()) + "/" + Uteis.getAnoData(Calendario.hoje()),
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            boolean temParcelaPaga = parcelas.stream().filter(parcela1 -> parcela1.getSituacao().equals("PG")).findAny().orElse(null) != null;
            if (temParcelaPaga && empresa.isLiberarPersonalComTaxaEmAberto()) {
                return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
            }

            Calendar dia = super.getInstanceCalendar(rxTx.getTz());
            if (colaborador.getDiaVencimento() != 0) {
                dia.set(Calendar.DAY_OF_MONTH, colaborador.getDiaVencimento());
            } else {
                dia.set(Calendar.DAY_OF_MONTH, getConfiguracaoSistemaVO().getVencimentoColaborador());
            }
            // se ja venceu o periodo de negociacao do personal
            if (Calendario.hoje().after(dia.getTime())) {
                // se nao houve compromisso assumido bloqueia o personal
                if (parcelas.isEmpty()) {
                    return SituacaoAcessoEnum.RV_BLOQPERSONAL;
                } else {
                    //Como a configuração é para PELO MENOS UMA TAXA EM ABERTO, a validação deve ser feita aqui.
                    if (!temParcelaPaga && empresa.isLiberarPersonalComTaxaEmAberto()) {
                        return SituacaoAcessoEnum.RV_BLOQPERSONAL;
                    } else if (!empresa.isLiberarPersonalComTaxaEmAberto()) {
                        int qtdTaxasParaNegociar = obterQtdDeAlunosParaNegociar(colaborador);
                        int qtdTaxasNegociadas = obterQtdDeAlunosNegociados(colaborador);

                        if (qtdTaxasParaNegociar > qtdTaxasNegociadas) {
                            return SituacaoAcessoEnum.RV_BLOQPERSONAL;
                        } else {
                            return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
                        }
                    }
                }
            }
        }
        return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO;
    }

    private int obterQtdDeAlunosNegociados(ColaboradorVO personal) throws SQLException {
        Date mesReferencia = Calendario.hoje();
        String ref = Uteis.getMesReferencia(mesReferencia) + "/" + Uteis.getAnoData(mesReferencia);

        String sql = "SELECT\n" +
                "  count(item.aluno) AS total\n" +
                "  from itemtaxapersonal item where controle in ( select distinct ctp.codigo \n" +
                "FROM controletaxapersonal ctp\n" +
                "  INNER JOIN movparcela mp ON ctp.codigo = mp.personal \n" +
                "  left join movprodutoparcela  mpp on mp.codigo = mpp.movparcela\n" +
                "  left join movproduto mpd on mpd.codigo = mpp.movproduto \n" +
                "  where  mpd.mesreferencia = '" + ref + "' AND ctp.personal = " + personal.getCodigo() + ")";
        try (PreparedStatement stm = getCon().prepareStatement(sql);
             ResultSet tabelaResultado = stm.executeQuery()) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getInt("total");
            }
            return 0;
        }
    }

    private int obterQtdDeAlunosParaNegociar(ColaboradorVO personal) throws Exception {
        Date mesReferencia = Calendario.hoje();
        String ref = Uteis.getMesReferencia(mesReferencia) + "/" + Uteis.getAnoData(mesReferencia);

        String sql = "select count(foo.cliente) as total from ("
                + "SELECT\n" +
                "  item.aluno AS cliente\n" +
                "  from itemtaxapersonal item where controle in ( select distinct ctp.codigo \n" +
                "FROM controletaxapersonal ctp\n" +
                "  INNER JOIN movparcela mp ON ctp.codigo = mp.personal \n" +
                "  left join movprodutoparcela  mpp on mp.codigo = mpp.movparcela\n" +
                "  left join movproduto mpd on mpd.codigo = mpp.movproduto \n" +
                "  where  mpd.mesreferencia = '" + ref + "' AND ctp.personal = " + personal.getCodigo() + ")\n"
                + "UNION\n"
                + "SELECT\n" +
                " hv.cliente \n" +
                " FROM historicovinculo hv\n" +
                " WHERE 1 = 1\n" +
                "      AND hv.colaborador = " + personal.getCodigo() + "\n" +
                "      AND (hv.dataregistro <= '" + Uteis.obterUltimoDiaMesUltimaHora(mesReferencia) + "' AND tipohistoricovinculo = 'EN')" +
                "      AND cliente NOT IN (SELECT\n" +
                "                            cliente\n" +
                "                          FROM historicovinculo\n" +
                "                          WHERE 1 = 1\n" +
                "                                AND colaborador = " + personal.getCodigo() + "\n" +
                "                                AND tipohistoricovinculo = 'SD' AND historicovinculo.codigo > hv.codigo and historicovinculo.tipocolaborador = hv.tipocolaborador)\n" +
                "      AND hv.tipocolaborador IN ('PT','PI','PE')) as foo";
        PreparedStatement stm = getCon().prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("total");
        }
        return 0;
    }

    public String[] montarListaAcessosPessoasComFoto(Integer localAcesso) throws Exception {
        List<String> lista = new ArrayList<>();

        String sql = "select p.codigo from acessocliente ac "
                + "inner join cliente c on ac.cliente = c.codigo "
                + "inner join pessoa p on c.pessoa = p.codigo "
                + "left join pessoafotolocalacesso pflc on p.codigo = pflc.pessoa and ac.localacesso = pflc.localacesso "
                + "where ac.localacesso = ? and ac.dthrentrada > ? and ((length(p.foto) > 0) or (p.fotokey is not null)) and pflc.codigo is null "
                + "group by p.codigo "
                + "order by max(ac.dthrentrada) desc";
        PreparedStatement stm = getCon().prepareStatement(sql);
        stm.setInt(1, localAcesso);
        Calendar cal = super.getInstanceCalendar(rxTx.getTz());
        cal.setTime(Calendario.hoje());
        cal.add(Calendar.MONTH, -3);
        stm.setDate(2, Uteis.getDataJDBC(cal.getTime()));
        ResultSet tabelaResultado = stm.executeQuery();
        while (tabelaResultado.next()) {
            lista.add(tabelaResultado.getString(1));
        }

        sql = "select p.codigo from acessocolaborador ac "
                + "inner join colaborador c on ac.colaborador = c.codigo "
                + "inner join pessoa p on c.pessoa = p.codigo "
                + "left join pessoafotolocalacesso pflc on p.codigo = pflc.pessoa and ac.localacesso = pflc.localacesso "
                + "where ac.localacesso = ? and ac.dthrentrada > ? and ((length(p.foto) > 0) or (p.fotokey is not null)) and pflc.codigo is null "
                + "group by p.codigo "
                + "order by max(ac.dthrentrada) desc";
        stm = getCon().prepareStatement(sql);
        stm.setInt(1, localAcesso);
        stm.setDate(2, Uteis.getDataJDBC(cal.getTime()));
        tabelaResultado = stm.executeQuery();
        while (tabelaResultado.next()) {
            lista.add(tabelaResultado.getString(1));
        }

        String[] ret = new String[lista.size()];
        for (int i = 0; i < lista.size(); i++) {
            ret[i] = lista.get(i);
        }
        return ret;
    }

    public byte[] pegarFotoPessoa(Integer localAcesso, Integer pessoa) throws Exception {
        byte[] ret = getPessoaDao().obterFoto(getKey(), pessoa);
        if (ret != null && !getPessoaFotoLocalAcessoDao().fotoJaEnviadaLocalAcesso(localAcesso, pessoa)) {
            getPessoaFotoLocalAcessoDao().incluir(localAcesso, pessoa);
            logarFotoEnviada("pegarFotoPessoa", localAcesso, pessoa);
        }
        return ret;
    }

    public String pegarUrlFotoPessoa(Integer localAcesso, Integer pessoa) throws Exception {
        pegarFotoPessoa(localAcesso, pessoa);
        return Uteis.getPaintFotoDaNuvem(getPessoaDao().obterFotoKey(pessoa));
    }

    private void logarFotoEnviada(String ident, int localAcesso, int pessoa) throws Exception {
        Uteis.logar(null, "Foto enviada (" + ident + "): url - " + getCon().getMetaData().getURL()
                + " / pessoa - " + pessoa + " / local de acesso - " + localAcesso);
    }

    private boolean bloquearSeDebitoEmConta() throws Exception {
        boolean bloquearSeDebitoEmConta = false;

        if (getRxTx().getCliente() != null && getRxTx().getCliente().getEmpresa() != 0) {
            EmpresaVO empresaVO = getEmpresaDao().consultarPorChavePrimaria(getRxTx().getCliente().getEmpresa(),
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            bloquearSeDebitoEmConta = empresaVO.isBloquearAcessoSeDebitoEmConta();
        }

        if(bloquearSeDebitoEmConta && getRxTx().getCliente().getPessoa() != null && getRxTx().getCliente().getPessoa().getCodigo() != 0) {
            return getClienteDao().clientePossuiDebitoEmConta(getRxTx().getCliente().getPessoa().getCodigo());
        }

        return bloquearSeDebitoEmConta;
    }

    private boolean bloquearSeParcelaEmAberto(Integer empresa) throws Exception {
        boolean verificarParcelaEmAberto;

        // Verifica a configuração do sistema
        ConfiguracaoSistemaVO configuracao = getConfiguracaoDao().buscarPorCodigo(1, false,
                Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        verificarParcelaEmAberto = configuracao.isBloquearAcessoSeParcelaAberta();

        // Verifica a configuração da empresa do cliente caso a configuração do sistema esteja desativada
        // e o cliente esteja vinculado à uma empresa
        if (!verificarParcelaEmAberto && getRxTx().getCliente() != null && getRxTx().getCliente().getEmpresa() != 0) {
            EmpresaVO empresaVO = getEmpresaDao().consultarPorChavePrimaria(getRxTx().getCliente().getEmpresa(),
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            verificarParcelaEmAberto = empresaVO.isBloquearAcessoSeParcelaAberta();
        }

        // Verifica a configuração da empresa do local de acesso as configurações acima estejam desativadas
        if (!verificarParcelaEmAberto) {
            EmpresaVO empresaVO = getEmpresaDao().consultarPorChavePrimaria(empresa, true,
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (empresaVO != null) {
                verificarParcelaEmAberto = empresaVO.isBloquearAcessoSeParcelaAberta();
            }
        }

        // Uma das configurações está ativa
        if (verificarParcelaEmAberto) {
            return getMovParcelaDao().existeParcelaEmAbertoNaoRecorrenciaPorCodigoPessoa(
                    getRxTx().getCliente().getPessoa().getCodigo());
        }

        return false;
    }

    private boolean bloquearSeMatriculaRematricaTotemSemPagamento(Integer empresa) throws Exception {
        boolean bloquearSeMatriculaRematricaTotemSemPagamento = false;


        EmpresaVO empresaVO = getEmpresaDao().consultarPorChavePrimaria(empresa, true,
                Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (empresaVO != null) {
            bloquearSeMatriculaRematricaTotemSemPagamento = empresaVO.isBloquearAcessoMatriculaRematriculaTotemSemPagamento();
        }

        // Uma das configurações está ativa
        if (bloquearSeMatriculaRematricaTotemSemPagamento) {
            return getContratoDao().existeMatriculaRematriculaTotemSemPagamento(
                    getRxTx().getCliente().getPessoa().getCodigo());
        }

        return false;
    }

    public RetornoRequisicaoDadosOffline gerarArquivoOffline(String key, Integer localAcesso, Integer codigoPessoa) throws Exception {
        RetornoRequisicaoDadosOffline retornoRequisicaoDadosOffline = new RetornoRequisicaoDadosOffline();
        List<DadosAcessoOfflineVO> lista = null;
        String nomeArquivo = "";
        if (codigoPessoa != null) {
            lista = getDadosAcessoOffline().consultarDadosPessoa(localAcesso, codigoPessoa);
            nomeArquivo = localAcesso.toString() + "Pessoa";
        } else {
            lista = getDadosAcessoOffline().consultarDados(localAcesso);
            nomeArquivo = localAcesso.toString();
        }
        String dir = PropsService.getPropertyValue(PropsService.pathDataSourceZAcesso) + "/";
        File io = new File(dir);
        if (!io.exists()) {
            io.mkdirs();
        }

        String arquivo7z = key + "_" + nomeArquivo + ".7z";
        io = new File(dir + arquivo7z);
        if (io.exists()) {
            io.delete();
        }

        Uteis.compactarArquivo(gerarArquivoOffline(lista), dir + arquivo7z);
        StringBuilder dados = new StringBuilder();
        dados.append("Usuario=").append(PropsService.getPropertyValue(PropsService.usuarioHTTPAcesso)).append("\n");
        dados.append("Senha=").append(PropsService.getPropertyValue(PropsService.senhaHTTPAcesso)).append("\n");
        dados.append("URL=").append(PropsService.getPropertyValue(PropsService.urlDataSourceZAcesso)).
                append("/").append(arquivo7z);
        retornoRequisicaoDadosOffline.setDados(Uteis.encriptarRetornoZAWOffline(dados.toString()));
        retornoRequisicaoDadosOffline.setResultado(ResultadoWSEnum.SUCESSO);
        return retornoRequisicaoDadosOffline;
    }

    private String gerarArquivoOffline(List<DadosAcessoOfflineVO> dados) throws Exception {
        //List<DadosAcessoOfflineVO> dados = getDadosAcessoOffline().consultarDados(localAcesso);
        if (dados.isEmpty()) {
            throw new Exception("Não existem dados offline");
        }

        StringBuilder sb = new StringBuilder();
        Iterator iterator = dados.iterator();
        int i = 0;
        while (iterator.hasNext()) {
            i++;
            DadosAcessoOfflineVO vo = (DadosAcessoOfflineVO) iterator.next();

            sb.append("[R");
            sb.append(String.valueOf(i));
            sb.append("]");
            sb.append("\n");

            sb.append("tipopessoa=");
            sb.append(vo.getTipoPessoa());
            sb.append("\n");

            sb.append("codigopk=");
            sb.append(vo.getCodigoPK().toString());
            sb.append("\n");

            sb.append("pessoapk=");
            sb.append(vo.getPessoa().getCodigo().toString());
            sb.append("\n");

            sb.append("numeroterminal=");
            sb.append(vo.getColetor().getNumeroTerminal().toString());
            sb.append("\n");

            sb.append("codigoacesso=");
            if (vo.getCodigoAcesso() != null) {
                sb.append(vo.getCodigoAcesso());
            }
            sb.append("\n");

            sb.append("codigoacessoalternativo=");
            if (vo.getCodigoAcesso() != null) {
                sb.append(vo.getCodigoAcessoAlternativo());
            }
            sb.append("\n");

            sb.append("matricula=");
            if (vo.getMatricula() != 0) {
                sb.append(vo.getMatricula());
            }
            sb.append("\n");

            sb.append("senhaacesso=");
            if (vo.getSenhaAcesso() != null) {
                sb.append(vo.getSenhaAcesso());
            }
            sb.append("\n");


            if (vo.getDataUltimoVencimento() != null) {
                sb.append("vencimentocontrato=").append(Uteis.getData(vo.getDataUltimoVencimento()));
                sb.append("\n");
            }
            sb.append("duracaoContrato=").append(vo.getDuracaoContrato());
            sb.append("\n");

            sb.append("horariosturmas=");
            if (vo.getHorariosTurmas() != null) {
                sb.append(vo.getHorariosTurmas());
            }
            sb.append("\n");

            sb.append("horariosplanos=");
            if (vo.getHorariosPlanos() != null) {
                sb.append(vo.getHorariosPlanos());
            }
            sb.append("\n");

            sb.append("validarSaldoCreditoTreino=");
            sb.append(vo.isValidarSaldoCreditoTreino() ? "SIM" : "NAO");
            sb.append("\n");


            sb.append("codigousuario=");
            if (vo.getUsuario().getCodigo() != null) {
                sb.append(vo.getUsuario().getCodigo());
            }
            sb.append("\n");

            sb.append("senha=");
            if (vo.getUsuario().getSenha() != null) {
                sb.append(vo.getUsuario().getSenha());
            }
            sb.append("\n");

            sb.append("pin=");
            if (vo.getUsuario().getPin() != null) {
                sb.append(vo.getUsuario().getPin());
            }
            sb.append("\n");

            sb.append("idexterno=");
            if (vo.getIdexterno() != 0) {
                sb.append(vo.getIdexterno());
            }
            sb.append("\n");

            sb.append("idexternointegracao=");
            if (vo.getIdexternointegracao() != null && !vo.getIdexternointegracao().isEmpty()) {
                sb.append(vo.getIdexternointegracao());
            }
            sb.append("\n");

            sb.append("dia=");
            if (vo.getDia() != null) {
                sb.append(Uteis.getDataFormatoBD(vo.getDia()));
            }
            sb.append("\n");

            sb.append("iniciovalidacao=0\n");
            if (vo.getRetornoValidacao() != null) {
                sb.append(vo.getRetornoValidacao());
            }
            sb.append("fimvalidacao=0\n");
        }

        return sb.toString();
    }

    public HorarioTurmaVO getHorarioTurmaVO() {
        return horarioTurmaVO;
    }

    public void setHorarioTurmaVO(HorarioTurmaVO horarioTurmaVO) {
        this.horarioTurmaVO = horarioTurmaVO;
    }

    public ResultSet obterCodigoAcessoPorSenha(final String senhaCriptografada) throws Exception {

        String sql = "select cl.codacesso as codcliente, co.codacesso as codcolaborador,acc.codigo as acessocolaborador from pessoa p "
                + "left join cliente cl on cl.pessoa = p.codigo "
                + "left join colaborador co on co.pessoa = p.codigo "
                + "left join acessocolaborador  acc on acc.colaborador = co.codigo and dthrentrada::date = ? and dthrsaida is null "
                + "where senhaacesso = ?";
        PreparedStatement stm = getCon().prepareStatement(sql);

        stm.setDate(1, Uteis.getDataJDBC(Calendario.hoje()));
        stm.setString(2, senhaCriptografada);
        return stm.executeQuery();
    }

    public ResultSet obterCodigoAcessoPorSenhaEmpresa(final String senhaCriptografada) throws Exception {

        String sql = "select cl.codacesso as codcliente, co.codacesso as codcolaborador,acc.codigo as acessocolaborador from pessoa p "
                + "left join cliente cl on cl.pessoa = p.codigo \n"
                + "left join colaborador co on co.pessoa = p.codigo \n"
                + "left join acessocolaborador  acc on acc.colaborador = co.codigo and dthrentrada::date = ? and dthrsaida is null \n"
                + "where senhaacesso = ? \n"
                + "and ((upper(trim(co.situacao)) = 'AT' OR co.situacao IS NULL) OR (cl.situacao IS NOT NULL)) \n"
                + "and ((co.codigo is not null) or (co.codigo is null and cl.codigo is not null))";
        PreparedStatement stm = getCon().prepareStatement(sql, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);

        int i = 0;
        stm.setDate(++i, Uteis.getDataJDBC(Calendario.hoje()));
        stm.setString(++i, senhaCriptografada);
        return stm.executeQuery();
    }

    public boolean isContratoComLimiteDeAcessoExcedido() {
        return contratoComLimiteDeAcessoExcedido;
    }

    public void setContratoComLimiteDeAcessoExcedido(boolean contratoComLimiteDeAcessoExcedido) {
        this.contratoComLimiteDeAcessoExcedido = contratoComLimiteDeAcessoExcedido;
    }

    public Map<String, String> getUltimosAcessosRegistrados() {
        if (ultimosAcessosRegistrados == null) {
            ultimosAcessosRegistrados = new HashMap<>();
        }
        return ultimosAcessosRegistrados;
    }

    public void setUltimosAcessosRegistrados(Map<String, String> ultimosAcessosRegistrados) {
        this.ultimosAcessosRegistrados = ultimosAcessosRegistrados;
    }

    public List<AcessoClienteVO> getValidacoesAcessoGympass() {
        return validacoesAcessoGympass;
    }

    public void setValidacoesAcessoGympass(List<AcessoClienteVO> validacoesAcessoGympass) {
        this.validacoesAcessoGympass = validacoesAcessoGympass;
    }

    private boolean validarMarcacaoAcessoTurmaAulaCheia(Calendar dataAcesso, HorarioTurmaVO hrTurmaVO, Date agora) throws Exception {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
        boolean estaNoHorario = false;

        //Verifico se o dia do acesso na semana corresponde ao dia cadastrado para a turma
        if (dataAcesso.get(Calendar.DAY_OF_WEEK) == hrTurmaVO.getDiaSemanaNumero()) {

            String horaFinal = hrTurmaVO.getHoraFinal();

            Date hFinal = formatter.parse(horaFinal);
            //foi solicitado que apenas a hora final fosse validada
            if (agora.getTime() <= hFinal.getTime()) {
                estaNoHorario = true;
            }
        }
        return estaNoHorario;
    }

    private void registrarPeriodoAcessoTotalpass(ClienteVO clienteVO) throws Exception {
        Date hoje = new Date();
        ClienteVO cliente = getClienteDao().consultarPorCodigo(clienteVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_GYMPASS);

        cliente.setFreePass(getProdutoDao().criarOuConsultarProdutoTotalPass());
        cliente.setResponsavelFreePass(0);
        rxTx.getCliente().setFreePass(cliente.getFreePass().getCodigo());
        getClienteDao().incluirFreePassTotalPass(cliente, hoje,true);
    }

    public void salvarLogTotalPass(LogTotalPassVO logTotalPassVO) throws Exception {
         getLogTotalPassDao().incluir(logTotalPassVO);
    }

    public boolean isValidaAlunoTotalPass(String codigoAcesso) throws Exception {
        ClienteVO cliente = getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (cliente != null && !UteisValidacao.emptyNumber(cliente.getCodigo())) {
            return getClienteDao().consultarClienteUltimoAcessoTotalPass(cliente.getCodigo());
        } else {
            return false;
        }
    }

    public SituacaoAcessoEnum validaAcessoTotalPass(String codigoAcesso,
                                                    DirecaoAcessoEnum direcao, Integer empresa,
                                                    Integer localAcesso, String terminal, boolean leitorSerial,
                                                    boolean validacaoAula, AcessoControle validacao, boolean isvalida) throws Exception {
        ClienteVO cliente = getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (cliente == null || cliente.getPessoa() == null) {
            return SituacaoAcessoEnum.RV_BLOQALUNONAOCADASTRADO;
        }

        PessoaVO pessoa = getPessoaDao().consultarPorChavePrimaria(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        if (pessoa == null) {
            return SituacaoAcessoEnum.RV_BLOQALUNONAOCADASTRADO;
        }

        List<TotalPassVO> listaTotalPass = getTotalpassDao().consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (listaTotalPass.isEmpty()) {
            return SituacaoAcessoEnum.RV_BLOQALUNONAOCADASTRADO;
        }

        TotalPassVO item = listaTotalPass.get(0);
            if (item == null || item.getApiKey().trim().isEmpty() || item.getCodigoTotalpass().trim().isEmpty() || item.getInativo()) {
                return SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO;
            }

        ApiTotalPass apiTotalPass = new ApiTotalPass();
        if (getPeriodoAcessoDao().existePeriodoAcessoHojeTotalPassPorPessoa(pessoa.getCodigo())) {
            return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADOTOTALPASS;
        }
        LogTotalPassVO logTotalPassVO =  apiTotalPass.consumirAPI("Catraca",pessoa.getCodigo(), empresa,  item.getApiKey().trim(), Uteis.removerMascara(pessoa.getCfp()), item.getCodigoTotalpass().trim(), isvalida);
        getLogTotalPassDao().incluir(logTotalPassVO);
        if(logTotalPassVO.getResposta().equals(AUTORIZADO_TOTALPASS)) {
            registrarPeriodoAcessoTotalpass(cliente);
            SituacaoAcessoEnum situacaoAcesso = validacao.tentarAcesso(
                    codigoAcesso, direcao, empresa, localAcesso, terminal, leitorSerial, validacaoAula);
            if (situacaoAcesso.isLiberado()){
                return SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADOTOTALPASS;
            } else {
                return situacaoAcesso;
            }
        }else {
            if (logTotalPassVO.getResposta().equals(BLOQUEADO_TOTALPASS_CHECKIN)) {
                return SituacaoAcessoEnum.RV_BLOQTOTALPASS_NESCESSARIO_CHECKIN_TOTALPASS;
            } else {
                return SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO;
            }
        }
    }



}
