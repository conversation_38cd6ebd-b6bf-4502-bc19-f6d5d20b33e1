package importador.outros;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import importador.ExportadorExcel;
import importador.LeitorExcel2010;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.MovConta;
import negocio.facade.jdbc.financeiro.PlanoConta;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.json.JSONArray;
import org.json.JSONObject;
import org.postgresql.jdbc.PgConnection;

import java.io.File;
import java.io.FileInputStream;
import java.sql.*;
import java.util.*;
import java.util.Date;

public class AjustarImportacaoPlanoContasAcquafit {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";
    private static Map<String, Integer> totalizador = new HashMap<>();
    private static List<String> listaCompletaDescricaoExcluir = new ArrayList<>();
    private static JSONArray listaCompletaDescricaoExcluirObj = new JSONArray();
    private static JSONArray listaFornecedoresExcluir = new JSONArray();
    private static JSONArray listaContasDuplicadas = new JSONArray();
    private static Set<Integer> pessoasVerificarExcluirFornecedor = new HashSet<>();
    private static JSONArray listaContasAjustadas = new JSONArray();
    private static JSONArray listaContasSemPlanoContas = new JSONArray();
    private static List<String> listaUpdate = new ArrayList<>();


    public static void main(String[] args) throws Exception {

        String inicioProcesso = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss");

        String diretorioExcel = "C:\\Users\\<USER>\\Downloads\\MJ-903_attachments";
        String diretorioLog = "C:\\Processos\\";

        try (Connection con = DriverManager.getConnection("****************************************************", "postgres", "pactodb")) {
            try {
                nomeBanco = con.getCatalog();

                adicionarLog("Parâmetros:");
                adicionarLog("url_banco: " + ((PgConnection) con).getURL());
                adicionarLog("nomeBanco: " + nomeBanco);

                criarTabelaLogProcesso(con);

                con.setAutoCommit(false);

                Integer totalAntesProcesso = obterQtdItensSemPlanoContas(con);

                List<String> listaDescricoes = obterDescricoesContasAjustar(con);

                List<Map<String, File>> arquivos = FileUtilities.readListFilesDirectory(diretorioExcel);
                for (Map<String, File> map : arquivos) {
                    for (String arquivo : map.keySet()) {
                        adicionarLog("Processando | Arquivo: " + arquivo);

                        String nomeArquivo = arquivo.substring(arquivo.lastIndexOf("\\") + 1);

                        Conexao.guardarConexaoForJ2SE(con);
                        planilhaAjustaPlanoContas(arquivo, nomeArquivo, listaDescricoes, con);

                    }
                }

                Integer totalAposProcesso = obterQtdItensSemPlanoContas(con);
                adicionarLog("Total inicio processo: " + totalAntesProcesso);
                adicionarLog("Total final processo: " + totalAposProcesso);

//                con.rollback();
                con.commit();
            } catch (Exception ex) {
                ex.printStackTrace();
                adicionarLog(ex.getMessage());
                con.rollback();
            } finally {
                con.setAutoCommit(true);
            }
        }

        printTotalizador();

        String fimProcesso = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss");
        adicionarLog("Inicio processo: " + inicioProcesso);
        adicionarLog("Fim processo: " + fimProcesso);

        String nomeArquivo = (AjustarImportacaoPlanoContasAcquafit.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss"));
        try {
            File file1 = new ExportadorExcel().gerarArquivoExcelGeral(nomeArquivo + "-PLANO_CONTAS_NAO_ENCONTRADO", listaContasSemPlanoContas, "", (diretorioLog + File.separator));
            File file2 = new ExportadorExcel().gerarArquivoExcelGeral(nomeArquivo + "-PLANO_CONTAS", listaContasAjustadas, "", (diretorioLog + File.separator));
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        adicionarLog("UPDATE INICIO");
        for (String up : listaUpdate) {
            adicionarLog(up);
        }
        adicionarLog("UPDATE FIM");

        Uteis.salvarArquivo(nomeArquivo + ".txt", getLogGravar().toString(), diretorioLog + File.separator);
    }

    public static void main_CONTAS_DUPLICADAS(String[] args) throws Exception {

        try (Connection con = DriverManager.getConnection("****************************************************", "postgres", "pactodb")) {
            try {
                nomeBanco = con.getCatalog();

                adicionarLog("Parâmetros:");
                adicionarLog("url_banco: " + ((PgConnection) con).getURL());
                adicionarLog("nomeBanco: " + nomeBanco);

                criarTabelaLogProcesso(con);

                con.setAutoCommit(false);

                contasDuplicadas(con, TipoOperacaoLancamento.PAGAMENTO);
                contasDuplicadas(con, TipoOperacaoLancamento.RECEBIMENTO);

                con.rollback();
//                con.commit();
            } catch (Exception ex) {
                ex.printStackTrace();
                adicionarLog(ex.getMessage());
                con.rollback();
            } finally {
                con.setAutoCommit(true);
            }
        }

        String nomeArquivo = (AjustarImportacaoPlanoContasAcquafit.class.getSimpleName() + "_" + nomeBanco + "-CONTA_DUPLICADA-" + Calendario.getData("yyyyMMddHHmmss"));
        String diretorioLog = "C:\\Processos\\";
        Uteis.salvarArquivo(nomeArquivo + ".txt", getLogGravar().toString(), diretorioLog + File.separator);
    }

    public static void main_CONTAS_EXCEL(String[] args) throws Exception {

        String inicioProcesso = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss");

        String diretorioExcel = "C:\\Users\\<USER>\\Downloads\\MJ-903_attachments";
        String diretorioLog = "C:\\Processos\\";

        try (Connection con = DriverManager.getConnection("****************************************************", "postgres", "pactodb")) {
            try {
                nomeBanco = con.getCatalog();

                adicionarLog("Parâmetros:");
                adicionarLog("url_banco: " + ((PgConnection) con).getURL());
                adicionarLog("nomeBanco: " + nomeBanco);

                criarTabelaLogProcesso(con);

                con.setAutoCommit(false);

                List<Map<String, File>> arquivos = FileUtilities.readListFilesDirectory(diretorioExcel);
                for (Map<String, File> map : arquivos) {
                    for (String arquivo : map.keySet()) {
                        adicionarLog("Processando | Arquivo: " + arquivo);

                        String nomeArquivo = arquivo.substring(arquivo.lastIndexOf("\\") + 1);

                        Conexao.guardarConexaoForJ2SE(con);
                        processarPlanilha(arquivo, nomeArquivo, con);

                    }
                }
                excluirFornecedores(con);

                con.rollback();
//                con.commit();
            } catch (Exception ex) {
                ex.printStackTrace();
                adicionarLog(ex.getMessage());
                con.rollback();
            } finally {
                con.setAutoCommit(true);
            }
        }

        printTotalizador();

        if (!listaCompletaDescricaoExcluir.isEmpty()) {
            adicionarLog("------------------------------------------------------------");
            adicionarLog("------------------------------------------------------------");
            adicionarLog("------------------------------------------------------------");
            adicionarLog("---------- DESCRIÇÃO DAS CONTAS A EXCLUIR - GERAL ----------");
            adicionarLog("------------------------------------------------------------");
            List<String> lista2 = new ArrayList<>(listaCompletaDescricaoExcluir);
            Collections.sort(lista2);
            for (String desc : lista2) {
                adicionarLog(desc);
            }
            adicionarLog("------------------------------------------------------------");
            adicionarLog("------------------------------------------------------------");
        }

        String fimProcesso = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss");
        adicionarLog("Inicio processo: " + inicioProcesso);
        adicionarLog("Fim processo: " + fimProcesso);

        String nomeArquivo = (AjustarImportacaoPlanoContasAcquafit.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss"));
        try {
            File file = new ExportadorExcel().gerarArquivoExcelGeral(nomeArquivo, listaCompletaDescricaoExcluirObj, "", (diretorioLog + File.separator));
            File file2 = new ExportadorExcel().gerarArquivoExcelGeral(nomeArquivo + "-FORNECEDORES", listaFornecedoresExcluir, "", (diretorioLog + File.separator));
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        Uteis.salvarArquivo(nomeArquivo + ".txt", getLogGravar().toString(), diretorioLog + File.separator);
    }

    private static void criarTabelaLogProcesso(Connection con) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("CREATE TABLE processocorrecaoplanocontas( \n");
            sql.append("codigo serial PRIMARY KEY, \n");
            sql.append("dtregistro TIMESTAMP WITHOUT TIME ZONE, \n");
            sql.append("info text, \n");
            sql.append("dados text) \n");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void atualizarBanco(Connection con) {
        Set<String> atualiza = new HashSet<>();
        atualiza.add("DO $$ \n" +
                "    BEGIN\n" +
                "        BEGIN\n" +
                "            ALTER TABLE planoconta ADD COLUMN nomeconsulta text;\n" +
                "        EXCEPTION\n" +
                "            WHEN duplicate_column THEN RAISE NOTICE 'Coluna nomeconsulta já existe';\n" +
                "        END;\n" +
                "    END;\n" +
                "$$");
        atualiza.add("DO $$ \n" +
                "    BEGIN\n" +
                "        BEGIN\n" +
                "            ALTER TABLE planoconta ADD COLUMN importado BOOLEAN DEFAULT FALSE;\n" +
                "        EXCEPTION\n" +
                "            WHEN duplicate_column THEN RAISE NOTICE 'Coluna importado já existe';\n" +
                "        END;\n" +
                "    END;\n" +
                "$$");
        atualiza.add("UPDATE planoconta SET nomeconsulta = remove_acento_upper(nome);");
        for (String update : atualiza) {
            try {
                SuperFacadeJDBC.executarConsulta(update, con);
            } catch (Exception ignored) {
            }
        }
    }

    private static void processarPlanilha(String arquivo, String nomeArquivo, Connection con) throws Exception {
        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivo), 0);
        int linhaAtual = 1;
        for (XSSFRow linha : hssfRows) {
            StringBuilder sql = new StringBuilder();
            try {
                ++linhaAtual;
                int coluna = 0;
                String idExterno = LeitorExcel2010.obterString(linha, coluna++);
                String descricao = LeitorExcel2010.obterString(linha, coluna++);
                String pessoaNome = LeitorExcel2010.obterString(linha, coluna++);
                String pessoaCPF = LeitorExcel2010.obterString(linha, coluna++);
                String valor = LeitorExcel2010.obterString(linha, coluna);
                Double valorDouble = LeitorExcel2010.obterDouble(linha, coluna++);
                String tipoConta = LeitorExcel2010.obterString(linha, coluna++);
                Date dataLancamento = LeitorExcel2010.obterData(linha, coluna++, "dd/MM/yyyy");
                Date dataVencimento = LeitorExcel2010.obterData(linha, coluna++, "dd/MM/yyyy");
                Date dataCompetencia = LeitorExcel2010.obterData(linha, coluna++, "dd/MM/yyyy");
                Date dataQuitacao = LeitorExcel2010.obterData(linha, coluna++, "dd/MM/yyyy");
                String centroCusto = LeitorExcel2010.obterString(linha, coluna++);
                String planoConta = LeitorExcel2010.obterString(linha, coluna++);
                String observacao = LeitorExcel2010.obterString(linha, coluna++);
                if (descricao == null || UteisValidacao.emptyString(descricao.replaceAll(" ", ""))) {
                    adicionarTotalizador("TOTAL CONTAS SEM DESCRICAO");
                    continue;
                }
                if (tipoConta == null || UteisValidacao.emptyString(tipoConta)) {
                    adicionarTotalizador("TOTAL CONTAS TIPO CONTA VAZIA");
                    adicionarLog("OBSERVAÇÃO --> Arquivo: " + nomeArquivo + " | Linha: " + linhaAtual + " | Obs: TIPO CONTA VAZIA");
                    continue;
                }
                boolean entrada = tipoConta.trim().toUpperCase().equalsIgnoreCase("ENTRADA");
                boolean saida = tipoConta.trim().toUpperCase().equalsIgnoreCase("SAÍDA");
                if (tipoConta == null || (!entrada && !saida)) {
                    adicionarTotalizador("TOTAL CONTAS TIPO CONTA INVALIDA");
                    adicionarLog("OBSERVAÇÃO --> Arquivo: " + nomeArquivo + " | Linha: " + linhaAtual + " | Obs: Tipo de conta inválida | " + tipoConta);
                    continue;
                }
                if (!entrada) {
                    adicionarTotalizador("TOTAL CONTAS NÃO É ENTRADA");
                    adicionarLog("OBSERVAÇÃO --> Arquivo: " + nomeArquivo + " | Linha: " + linhaAtual + " | Obs: CONTA NÃO É ENTRADA ", false);
                    continue;
                }

                //conta PRONAMPE  vou ignorar
                if (descricao.toUpperCase().contains("PRONAMPE")) {
                    adicionarTotalizador("Total contas PRONAMPE");
                    adicionarLog("OBSERVAÇÃO --> Arquivo: " + nomeArquivo + " | Linha: " + linhaAtual + " | Obs: CONTA PRONAMPE - VOU IGNORAR");
                    continue;
                }
                if (planoConta.trim().toUpperCase().equalsIgnoreCase("001.003.004") ||
                        planoConta.trim().toUpperCase().equalsIgnoreCase("003.003") ||
                        planoConta.trim().toUpperCase().equalsIgnoreCase("003.001")) {
                    adicionarTotalizador("Total contas encontrada plano de contas correto | NÃO VOU EXCLUIR | Plano de contas: " + planoConta.trim().toUpperCase());
                    continue;
                }

                //aluguel não excluir
                //mesmo q plano de contas errado
                if (descricao.toUpperCase().contains("ALUGUEL")) {
                    adicionarTotalizador("Total contas ALUGUEL com plano de contas incorreto");
                    adicionarLog("OBSERVAÇÃO --> Arquivo: " + nomeArquivo + " | Linha: " + linhaAtual + " | Obs: NÃO EXCLUIR ALUGUEL | Descrição: " + descricao + " | Plano Conta: " + planoConta);
                    continue;
                }
                sql.append("select \n");
                sql.append("mv.codigo \n");
                sql.append("from movconta mv \n");
                sql.append("where mv.observacoes ilike '%CONTA IMPORTADA EXCEL%' \n");
                sql.append("and mv.descricao ilike '" + descricao + "' \n");
                sql.append("and mv.valor = " + valorDouble + " \n");
                sql.append("and mv.idexterno = '" + idExterno + "' \n");
                if (dataCompetencia != null) {
                    sql.append("and mv.datacompetencia::date = '" + Uteis.getDataFormatoBD(dataCompetencia) + "' \n");
                }
                if (dataVencimento != null) {
                    sql.append("and mv.dataVencimento::date = '" + Uteis.getDataFormatoBD(dataVencimento) + "' \n");
                }
                if (dataQuitacao != null) {
                    sql.append("and mv.dataQuitacao::date = '" + Uteis.getDataFormatoBD(dataQuitacao) + "' \n");
                }
                if (dataLancamento != null) {
                    sql.append("and mv.dataLancamento::date = '" + Uteis.getDataFormatoBD(dataLancamento) + "' \n");
                }
                Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
                if (total == 0) {
                    adicionarTotalizador("Total contas não encontrada");
                    adicionarLog("OBSERVAÇÃO --> Arquivo: " + nomeArquivo + " | Linha: " + linhaAtual + " | Obs: NAO FOI ENCONTRADO NENHUMA CONTA IMPORTADA | Descrição: " + descricao + " | Plano Conta: " + planoConta);
                    continue;
                }
                if (total > 1) {
                    adicionarTotalizador("Total contas mais de 1 registro");
                    adicionarLog("OBSERVAÇÃO --> Arquivo: " + nomeArquivo + " | Linha: " + linhaAtual + " | Obs: ENCONTRADO MAIS DE UMA CONTA IMPORTADA | Descrição: " + descricao + " | Plano Conta: " + planoConta);
                    continue;
                }
                adicionarTotalizador("Total contas encontrada para excluir");

//                listaCompletaDescricaoExcluir.add("DESCRIÇÃO: \"" + descricao.toUpperCase() + "\" | PLANO DE CONTAS: \"" + planoConta + "\" | ARQUIVO: \"" + nomeArquivo + "\"");

                JSONObject itemJson = new JSONObject();
                itemJson.put("descricao", descricao.toUpperCase());
                itemJson.put("plano_contas", planoConta);
                itemJson.put("valor", valorDouble);
                itemJson.put("arquivo", nomeArquivo);
                listaCompletaDescricaoExcluirObj.put(itemJson);

                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                if (rs.next()) {
                    MovConta movContaDAO = new MovConta(con);
                    MovContaVO movContaVO = movContaDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_TODOS);
                    excluirMovConta(movContaVO, "PROCESSO EXCLUSÃO DE MOVCONTA IMPORTADO INCORRETAMENTE", "", con);

                    pessoasVerificarExcluirFornecedor.add(movContaVO.getPessoaVO().getCodigo());
                }
            } catch (Exception ex) {
//                   ex.printStackTrace();
                adicionarLog("ERRO | " + nomeArquivo + " | LINHA: " + linhaAtual + " | ERRO: " + ex.getMessage());
                adicionarLog(sql.toString());
                throw ex;
            }
        }
    }

    private static void excluirFornecedores(Connection con) throws Exception {
        Integer totalInicial = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM ( select codigo from fornecedor) as qtd", con);
        for (Integer pessoa : pessoasVerificarExcluirFornecedor) {
            StringBuilder sqlF = new StringBuilder();
            sqlF.append("select \n");
            sqlF.append("p.codigo as pessoa,\n");
            sqlF.append("p.nome,\n");
            sqlF.append("(select codigo from fornecedor where pessoa = p.codigo) as fornecedor\n");
            sqlF.append("from pessoa p \n");
            sqlF.append("where p.codigo = ").append(pessoa).append(" \n");
            sqlF.append("and not exists(select codigo from movconta where pessoa = p.codigo) \n");
            ResultSet rsF = SuperFacadeJDBC.criarConsulta(sqlF.toString(), con);
            if (rsF.next()) {
                Integer pessoa1 = rsF.getInt("pessoa");
                Integer fornecedor = rsF.getInt("fornecedor");
                String nome = rsF.getString("nome");
                if (!UteisValidacao.emptyNumber(fornecedor)) {

                    String backup = obterJsonBackup("fornecedor", "select * from fornecedor where codigo = " + fornecedor, con);
                    incluirTabelaProcesso("backup_fornecedor_" + fornecedor, backup, con);

                    adicionarLog("EXCLUIR FORNECEDOR --> Código: " + fornecedor + " | Pessoa: " + pessoa1 + " | Nome: " + nome);
                    SuperFacadeJDBC.executarUpdate("DELETE FROM fornecedor WHERE CODIGO = " + fornecedor, con);

                    JSONObject itemJson = new JSONObject();
                    itemJson.put("codigo", fornecedor);
                    itemJson.put("nome", nome);
                    itemJson.put("pessoa", pessoa1);
                    listaFornecedoresExcluir.put(itemJson);
                }
            }
        }
        Integer totalFinal = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM ( select codigo from fornecedor) as qtd", con);
        adicionarLog("------------------------------------------------------------");
        adicionarLog("---------------------- FORNECEDOR --------------------------");
        adicionarLog("------------------------------------------------------------");
        adicionarLog("TOTAL FORNECEDOR INICIAL: " + totalInicial);
        adicionarLog("TOTAL FORNECEDOR FINAL: " + totalFinal);
        adicionarLog("------------------------------------------------------------");
    }

    private static void adicionarLog(String msg) {
        adicionarLog(msg, true);
    }

    private static void adicionarLog(String msg, boolean print) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        if (print) {
            System.out.println(s);
        }
        getLogGravar().append(s).append("\n");
    }

    private static void adicionarTotalizador(String identificador) {
        Integer atual = totalizador.get(identificador.toUpperCase());
        if (atual == null) {
            atual = 0;
        }
        totalizador.put(identificador.toUpperCase(), (atual + 1));
    }

    private static void printTotalizador() {
        adicionarLog("------------------------------------------------------------");
        for (String key : totalizador.keySet()) {
            adicionarLog(key + ": " + totalizador.get(key));
        }
        adicionarLog("------------------------------------------------------------");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    private static String obterJsonBackup(String tabela, String sql, Connection con) throws SQLException {
        JSONObject jsonTotal = new JSONObject();
        JSONObject json = new JSONObject();
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                final ResultSetMetaData meta = rs.getMetaData();
                final int columnCount = meta.getColumnCount();
                if (rs.next()) {
                    for (int column = 1; column <= columnCount; ++column) {
                        try {
                            final String nomeColuna = meta.getColumnName(column);
                            final Object value = rs.getObject(column);
                            json.put(nomeColuna.toLowerCase(), value);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            }
        }
        jsonTotal.put("tabela", tabela);
        jsonTotal.put("dados", json);
        return jsonTotal.toString();
    }

    private static void incluirTabelaProcesso(String info, String dados, Connection con) throws SQLException {
        String sql = "INSERT INTO processocorrecaoplanocontas(dtregistro,info,dados) VALUES (?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.setString(2, info);
        sqlInserir.setString(3, dados);
        sqlInserir.execute();
    }

    private static void contasDuplicadas(Connection con, TipoOperacaoLancamento tipoOperacaoLancamento) throws Exception {
        listaContasDuplicadas = new JSONArray();

        adicionarLog("VOU EXCLUIR DUPLICADAS -> " + tipoOperacaoLancamento.getDescricao().toUpperCase());

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("mv.descricao, \n");
        sql.append("mv.valor,  \n");
        sql.append("mv.datalancamento::date,  \n");
        sql.append("mv.datavencimento::date, \n");
        sql.append("max(mv.codigo) as movconta_excluir, \n");
        sql.append("count(*) as qtd_contas \n");
        sql.append("from movconta mv \n");
        sql.append("where mv.observacoes ilike '%CONTA IMPORTADA EXCEL%'  \n");
        sql.append("and mv.tipooperacao = ").append(tipoOperacaoLancamento.getCodigo()).append(" \n");
        sql.append("group by 1,2,3,4 \n");
        sql.append("having count (*) = 2 \n");
        sql.append("order by 1,2,3 \n");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        adicionarLog("Total de contas | " + tipoOperacaoLancamento.getDescricao().toUpperCase() + " | inicio processo: " + total);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        Integer atual = 0;
        while (rs.next()) {

            Integer movconta_excluir = rs.getInt("movconta_excluir");

            JSONObject itemJson = new JSONObject();
            itemJson.put("codigo", movconta_excluir);
            itemJson.put("descricao", rs.getString("descricao"));
            itemJson.put("tipo", tipoOperacaoLancamento.getDescricao().toUpperCase());
            itemJson.put("valor", Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
            itemJson.put("data_lancamento", rs.getDate("datalancamento"));
            itemJson.put("data_vencimento", rs.getDate("datavencimento"));
            itemJson.put("qtd_contas_semelhante", rs.getInt("qtd_contas"));
            listaContasDuplicadas.put(itemJson);

            MovConta movContaDAO = new MovConta(con);
            MovContaVO movContaVO = movContaDAO.consultarPorCodigo(movconta_excluir, Uteis.NIVELMONTARDADOS_TODOS);

            adicionarLog("EXCLUIR MOVCONTA " + ++atual + "/" + total + " --> " + movContaVO.getCodigo() + " | " + movContaVO.getDescricao() + " | idExterno: " + movContaVO.getIdExterno());
            excluirMovConta(movContaVO, "PROCESSO EXCLUSÃO DE MOVCONTA DUPLICADA", "conta_duplicada_", con);

        }

        total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        adicionarLog("Total de contas | " + tipoOperacaoLancamento.getDescricao().toUpperCase() + " | final processo: " + total);

        String diretorioLog = "C:\\Processos\\";
        String nomeArquivo = (AjustarImportacaoPlanoContasAcquafit.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss"));
        try {
            if (listaContasDuplicadas.length() > 0) {
                File file = new ExportadorExcel().gerarArquivoExcelGeral(nomeArquivo, listaContasDuplicadas, "", (diretorioLog + File.separator));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void excluirMovConta(MovContaVO movContaVO, String nomeProcessoLog, String infoLog, Connection con) throws Exception {
//        MovConta movContaDAO = new MovConta(con);
//        MovContaVO movContaVO = movContaDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_TODOS);

        String backupCaixaMovConta = obterJsonBackup("caixamovconta", "select * from caixamovconta where movconta = " + movContaVO.getCodigo(), con);
        incluirTabelaProcesso(infoLog + "backup_caixamovconta_" + movContaVO.getCodigo(), backupCaixaMovConta, con);
        SuperFacadeJDBC.executarUpdate("DELETE FROM caixamovconta WHERE movconta = " + movContaVO.getCodigo(), con);

        String backupMovConta = obterJsonBackup("movconta", "select * from movconta where codigo = " + movContaVO.getCodigo(), con);
        incluirTabelaProcesso(infoLog + "backup_movconta_" + movContaVO.getCodigo(), backupMovConta, con);
        SuperFacadeJDBC.executarUpdate("DELETE FROM movconta WHERE CODIGO = " + movContaVO.getCodigo(), con);

        Log logDAO = new Log(con);
        LogVO obj = new LogVO();
        obj.setChavePrimaria(movContaVO.getContaVO().getCodigo().toString());
        obj.setNomeEntidade(MovContaVO.NOME_ENTIDADE_LOG_ALTERACAO_MOVCONTA);
        obj.setNomeEntidadeDescricao("MOVIMENTAÇÃO DE CONTA");
        obj.setResponsavelAlteracao("ADMINISTRADOR");
        obj.setNomeCampo("MENSAGEM");
        obj.setValorCampoAlterado("");
        obj.setOperacao("ALTERAÇÃO");
        StringBuilder msg = new StringBuilder();
        msg.append("\n" + nomeProcessoLog + "\n");
        msg.append("\nEXCLUIDO MOVCONTA:").append(movContaVO.getCodigo()).append("\n");
        msg.append("Descrição: ").append(movContaVO.getDescricao()).append("\n");
        msg.append("Tipo Operação:").append(movContaVO.getTipoOperacaoLancamento_Apresentar()).append("\n");
        msg.append("Conta:").append(movContaVO.getContaVO().getDescricao()).append("\n");
        msg.append("Pessoa:").append(movContaVO.getPessoaVO().getNome()).append("\n");
        msg.append("Valor: ").append(Uteis.getDoubleFormatado(movContaVO.getValor())).append("\n");
        msg.append("Data Lançamento:").append(movContaVO.getDataLancamento_Apresentar()).append("\n");
        msg.append("Data Quitação:").append(movContaVO.getDataQuitacao_Apresentar()).append("\n");
        obj.setValorCampoAlterado(msg.toString());
        obj.setDataAlteracao(Calendario.hoje());
        logDAO.incluirSemCommit(obj);
    }

    private static void planilhaAjustaPlanoContas(String arquivo, String nomeArquivo, List<String> listaDescricoes, Connection con) throws Exception {
        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivo), 0);
        int linhaAtual = 1;
        for (XSSFRow linha : hssfRows) {
            StringBuilder sql = new StringBuilder();
            try {
                ++linhaAtual;
                int coluna = 0;
                String idExterno = LeitorExcel2010.obterString(linha, coluna++);
                String descricao = LeitorExcel2010.obterString(linha, coluna++);
                String pessoaNome = LeitorExcel2010.obterString(linha, coluna++);
                String pessoaCPF = LeitorExcel2010.obterString(linha, coluna++);
                String valor = LeitorExcel2010.obterString(linha, coluna);
                Double valorDouble = LeitorExcel2010.obterDouble(linha, coluna++);
                String tipoConta = LeitorExcel2010.obterString(linha, coluna++);
                Date dataLancamento = LeitorExcel2010.obterData(linha, coluna++, "dd/MM/yyyy");
                Date dataVencimento = LeitorExcel2010.obterData(linha, coluna++, "dd/MM/yyyy");
                Date dataCompetencia = LeitorExcel2010.obterData(linha, coluna++, "dd/MM/yyyy");
                Date dataQuitacao = LeitorExcel2010.obterData(linha, coluna++, "dd/MM/yyyy");
                String centroCusto = LeitorExcel2010.obterString(linha, coluna++);
                String planoConta = LeitorExcel2010.obterString(linha, coluna++);
                String observacao = LeitorExcel2010.obterString(linha, coluna++);
                if (descricao == null || UteisValidacao.emptyString(descricao.replaceAll(" ", ""))) {
                    continue;
                }

                if (!listaDescricoes.contains(descricao.toUpperCase())) {
                    continue;
                }

//                if (descricao.toUpperCase().contains("PRESTAÇÃO DE SERVIÇO REF. DEZEMBRO/2023")) {
//                    if (pessoaNome.toUpperCase().contains("LUIS FELIPE")) {
//                        System.out.println("aaaaaa");
//                    }
//                }


                sql.append("select \n");
                sql.append("(select planoconta from movcontarateio m where m.movconta = mv.codigo) as planoconta_codigo, \n");
                sql.append("(select codigo from movcontarateio m where m.movconta = mv.codigo) as movcontarateio_codigo, \n");
                sql.append("mv.codigo \n");
                sql.append("from movconta mv \n");
                sql.append("where mv.observacoes ilike '%CONTA IMPORTADA EXCEL%' \n");
                sql.append("and mv.descricao ilike '" + descricao.replace("'", "''") + "' \n");
                sql.append("and mv.valor = " + valorDouble + " \n");
//                sql.append("and mv.idexterno = '" + idExterno + "' \n");
                if (dataCompetencia != null) {
                    sql.append("and mv.datacompetencia::date = '" + Uteis.getDataFormatoBD(dataCompetencia) + "' \n");
                }
                if (dataVencimento != null) {
                    sql.append("and mv.dataVencimento::date = '" + Uteis.getDataFormatoBD(dataVencimento) + "' \n");
                }
                if (dataQuitacao != null) {
                    sql.append("and mv.dataQuitacao::date = '" + Uteis.getDataFormatoBD(dataQuitacao) + "' \n");
                }
                if (dataLancamento != null) {
                    sql.append("and mv.dataLancamento::date = '" + Uteis.getDataFormatoBD(dataLancamento) + "' \n");
                }
                adicionarTotalizador("Total Contas geral");
                Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
                if (total == 0) {
                    adicionarTotalizador("Total contas não encontrada");
//                    adicionarLog("OBSERVAÇÃO --> Arquivo: " + nomeArquivo + " | Linha: " + linhaAtual + " | Obs: NAO FOI ENCONTRADO NENHUMA CONTA IMPORTADA | Descrição: " + descricao + " | Plano Conta: " + planoConta);
                    continue;
                }
                if (total > 1) {
                    adicionarTotalizador("Total contas mais de 1 registro");
                    adicionarLog("OBSERVAÇÃO --> Arquivo: " + nomeArquivo + " | Linha: " + linhaAtual + " | Obs: ENCONTRADO MAIS DE UMA CONTA IMPORTADA | Descrição: " + descricao + " | Plano Conta: " + planoConta);
                    continue;
                }
                adicionarTotalizador("Total contas encontrada para ajustar");


                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                if (rs.next()) {

                    Integer planoconta_codigo = rs.getInt("planoconta_codigo");
                    if (!UteisValidacao.emptyNumber(planoconta_codigo)) {
                        adicionarTotalizador("Total contas com plano de contas rateio");
                        continue;
                    }

                    Integer movcontarateio_codigo = rs.getInt("movcontarateio_codigo");
                    if (UteisValidacao.emptyNumber(movcontarateio_codigo)) {
                        adicionarTotalizador("Total contas com plano sem movcontarateio");
                        continue;
                    }

                    MovConta movContaDAO = new MovConta(con);
                    MovContaVO movContaVO = movContaDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_TODOS);


                    PlanoContaTO planoContaTO = consultarPlanoDeContas(planoConta, con);
                    if (planoContaTO == null || UteisValidacao.emptyNumber(planoContaTO.getCodigo())) {
                        adicionarTotalizador("Total contas sem plano de contas encontrada no cadastro | Plano " + planoConta);

                        JSONObject itemJson = new JSONObject();
                        itemJson.put("codigo_movconta", movContaVO.getCodigo());
                        itemJson.put("descricao", descricao.toUpperCase());
                        itemJson.put("plano_contas_planilha", planoConta);
                        itemJson.put("valor", valorDouble);
                        itemJson.put("arquivo", nomeArquivo);

                        listaContasSemPlanoContas.put(itemJson);
                        continue;
                    }

                    JSONObject itemJson = new JSONObject();
                    itemJson.put("codigo_movconta", movContaVO.getCodigo());
                    itemJson.put("descricao", descricao.toUpperCase());
                    itemJson.put("plano_contas_planilha", planoConta);
                    itemJson.put("plano_contas_encontrada", planoContaTO.getDescricao());
                    itemJson.put("plano_contas_encontrada_codigo", planoContaTO.getCodigo());
                    itemJson.put("valor", valorDouble);
                    itemJson.put("arquivo", nomeArquivo);

                    String update = ("UPDATE movcontarateio SET planoconta = " + planoContaTO.getCodigo() + " WHERE codigo = " + movcontarateio_codigo + ";");
                    listaUpdate.add(update);
                    SuperFacadeJDBC.executarUpdate(update, con);
                    listaContasAjustadas.put(itemJson);
                    adicionarTotalizador("Total contas ajustadas");
                }
            } catch (Exception ex) {
//                   ex.printStackTrace();
                adicionarLog("ERRO | " + nomeArquivo + " | LINHA: " + linhaAtual + " | ERRO: " + ex.getMessage());
                adicionarLog(sql.toString());
                throw ex;
            }
        }
    }

    private static PlanoContaTO consultarPlanoDeContas(String codigoPlano, Connection con) throws Exception {
        PlanoConta planoContaDAO = new PlanoConta(con);
        return planoContaDAO.consultarPorCodigoPlano(codigoPlano);
    }

    private static Integer obterQtdItensSemPlanoContas(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ( \n");
        sql.append("select   \n");
        sql.append("(select planoconta from movcontarateio m where m.movconta = mv.codigo) as planoconta_codigo, \n");
        sql.append("(select codigo from movcontarateio m where m.movconta = mv.codigo) as movcontarateio_codigo, \n");
        sql.append("mv.codigo,  \n");
        sql.append("mv.descricao,  \n");
        sql.append("mv.valor,   \n");
        sql.append("mv.datalancamento::date,   \n");
        sql.append("mv.datavencimento::date, \n");
        sql.append("mv.observacoes \n");
        sql.append("from movconta mv \n");
        sql.append("where mv.observacoes ilike '%CONTA IMPORTADA EXCEL%' \n");
        sql.append("order by 1  \n");
        sql.append(") as sql \n");
        sql.append("where sql.planoconta_codigo is null  \n");
        return SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
    }

    private static List<String> obterDescricoesContasAjustar(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ( \n");
        sql.append("select   \n");
        sql.append("(select planoconta from movcontarateio m where m.movconta = mv.codigo) as planoconta_codigo, \n");
        sql.append("(select codigo from movcontarateio m where m.movconta = mv.codigo) as movcontarateio_codigo, \n");
        sql.append("mv.codigo,  \n");
        sql.append("mv.descricao,  \n");
        sql.append("mv.valor,   \n");
        sql.append("mv.datalancamento::date,   \n");
        sql.append("mv.datavencimento::date, \n");
        sql.append("mv.observacoes \n");
        sql.append("from movconta mv \n");
        sql.append("where mv.observacoes ilike '%CONTA IMPORTADA EXCEL%' \n");
        sql.append("order by 1  \n");
        sql.append(") as sql \n");
        sql.append("where sql.planoconta_codigo is null  \n");
        List<String> lista = new ArrayList<>();
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
            lista.add(rs.getString("descricao").toUpperCase());
        }
        return lista;
    }
}



