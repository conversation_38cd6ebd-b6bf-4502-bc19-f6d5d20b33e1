package negocio.interfaces.arquitetura;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.LogAgrupadoChavePrimaria;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.basico.HistoricoParcelaOriginalTO;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface LogInterfaceFacade extends SuperInterface {

    public LogVO novo() throws Exception;

    public List consultarPorNomeCodigoEntidadeAgrupado(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, Integer codigoPessoa, int nivelMontarDados, boolean agruparSegundo) throws Exception;

    public void incluir(LogVO obj) throws Exception;

    public void excluir(LogVO obj) throws Exception;

    public LogVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultar(ControleConsulta filtro, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultar(ControleConsulta filtro, boolean controlarAcesso, int nivelMontarDados, ConfPaginacao confPaginacao) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeEntidade(String nomeEntidade, Date dataInicio, Date dataFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeEntidade(String nomeEntidade, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoEntidade(Integer codigoEntidade, Date dataInicio, Date dataFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public LogVO consultarPorNomeCodigoEntidade(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeCodigoEntidade(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, Integer codigoPessoa, int nivelMontarDados) throws Exception;

    public List consultarPorConteudoLog(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorResponsavel(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorOpercao(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirSemCommit(LogVO obj) throws Exception;

    public List<LogVO> consultarPorNomeEntidadePorDataAlteracaoPorOperacao(String nomeEntidade, java.sql.Date dataAlteracaoInicio, java.sql.Date dataAlteracaoFim, String operacao, String nomeEmpresa, boolean buscarComAdministrador, List<ColaboradorVO> lista, int nivelMontarDados) throws SQLException, Exception;

    public int contarPorNomeEntidadePorDataAlteracaoPorOperacao(String nomeEntidade, java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim, String operacao, String nomeEmpresa, boolean buscarComAdministrador, List<ColaboradorVO> lista, int nivelMontarDados) throws SQLException, Exception;

    public List consultarPorNomeEntidadeOperacaoPorDataAlteracao(String nomeEntidade, String nomeEntidadeDescricao, String operacao, int nivelMontarDados, boolean considerarValorCampoAnterior, String labelDescricao, Date dataInicio, String horarioInicial, Date dataFinal, String horarioFinal) throws Exception;

    public int contarPorNomeEntidadePorDataAlteracaoPorOperacao(String nomeEntidade, java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim, int empresa, List<ColaboradorVO> lista, int nivelMontarDados) throws SQLException, Exception;

    public List<LogAgrupadoChavePrimaria> consultarPorNomeCodigoEntidadeAgrupadoPorChavePrimaria(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, Integer codigoPessoa, int codigoEmpresa, List<ColaboradorVO> lista, int nivelMontarDados,  boolean agruparSegundo) throws Exception;

    public List consultarPorNomeEmpresaCodigoEntidadeAgrupado(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, Integer codigoPessoa, Integer codigoEmpresa, List<ColaboradorVO> lista, int nivelMontarDados, boolean agruparSegundo) throws Exception;

    public String consultarJSON(String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar) throws Exception;

//    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

    public int contarPorNomeEntidadeOperacaoPorDataAlteracao(String nomeEntidade, String operacao, String nomeEmpresa, java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim, List<ColaboradorVO> lista,  int nivelMontarDados) throws Exception;

    public List<LogVO> consultarPorNomeEntidadeOperacaoPorDataAlteracao(String nomeEntidade, String operacao, String nomeEmpresa, java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim, List<ColaboradorVO> lista, int nivelMontarDados) throws Exception;

    Set<HistoricoParcelaOriginalTO> obterParcelasRenegociadasAPartirParcela(String codigoParcela, boolean somenteOriginais) throws Exception;

    String obterMovParcelaAnteriorOrigemRenegociacaoPartirParcelaRenegociada(String codigoParcela) throws Exception;

    public List consultarSemLimitacao(ControleConsulta filtro,  int nivelMontarDados) throws Exception;

    public List consultarApartirDoResponsavelDataAlteracao(String responsavel, String dataAlteracao, int nivelMontarDados) throws Exception;

    List<LogVO> consultarPorNomeentidadeNomecampoChaveprimariaOperacao(String nomeEntidade, String nomeCampo, String chavePrimaria, String operacao, int nivelMontarDados) throws Exception;

    /**
     * Retorna o ultimo log de uma determinada entidade.
     * @param nomeEntidade Nome da entidade que sera buscada o log
     * @param codigoEntidade Código identificador da entidade
     * @return
     * @throws Exception
     */
    LogVO consultarUltimoLogPessoa(Integer codigoPessoa,String... entidades) throws Exception;

    void incluirLogErroInclusaoContrato(Integer cliente, UsuarioVO usuarioAdmin, String erro);

    void incluirLogInclusaoClienteImportacao(Integer cliente, UsuarioVO usuarioAdmin);

    void incluirLogEstornoContratoSite(Integer cliente, UsuarioVO usuarioAdmin, String msg);

    List consultarPorNomesEntidadeAgrupado(List<String> nomesEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, Integer codigoPessoa, int nivelMontarDados,  boolean agruparSegundo) throws Exception;

    Integer vendasNaoRealizadasTotal(Integer empresa, Date dataInicial, Date dataFinal) throws Exception;

    List<ItemRelatorioTO> vendasNaoRealizadasLista(Integer empresa, Date dataInicial, Date dataFinal) throws Exception;

    void incluirLogItemImportacao(String entidade, Integer chavePrimaria, UsuarioVO usuarioVO, Integer codigoPessoa);

    void incluirLogCappta(String totem, String infoDados, String acao);

    Boolean consultarAlteracaoCobrarMultaJurosAntesDaGeracaoDaRemessa(Date dataHoraRemessaGerada, int codEmpresa) throws Exception;

    Double consultarAlteracaoMultaAntesDaGeracaoDaRemessa(Date dataHoraRemessaGerada, int codEmpresa) throws Exception;

    Double consultarAlteracaoJurosAntesDaGeracaoDaRemessa(Date dataHoraRemessaGerada, int codEmpresa) throws Exception;

    Boolean consultarAlteracaoMultaValorAbsolutoAntesDaGeracaoDaRemessa(Date dataHoraRemessaGerada, int codEmpresa) throws Exception;

    Boolean consultarAlteracaoJurosValorAbsolutoAntesDaGeracaoDaRemessa(Date dataHoraRemessaGerada, int codEmpresa) throws Exception;
}
