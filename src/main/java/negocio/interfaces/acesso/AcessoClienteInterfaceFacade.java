/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.acesso;

import negocio.comuns.acesso.*;
import negocio.comuns.acesso.auxiliar.PessoaAcesso;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.plano.AcessoBloqueadoTO;
import negocio.comuns.plano.AcessoRealizadoTO;
import negocio.comuns.plano.AlunoHorarioTurmaTO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.interfaces.basico.SuperInterface;
import negocio.interfaces.contrato.ControleCreditoTreinoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ResumoPessoaBIAcessoGymPassVO;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface AcessoClienteInterfaceFacade extends SuperInterface {

    public AcessoClienteVO registrarAcessoClienteSemControleTreino(Date dataAcesso,
                                                                   ClienteVO cliente,
                                                                   SituacaoAcessoEnum situacao,
                                                                   DirecaoAcessoEnum direcao,
                                                                   LocalAcessoVO local,
                                                                   ColetorVO coletor,
                                                                   UsuarioVO usuario,
                                                                   MeioIdentificacaoEnum meioIdentificacao,
                                                                   String key) throws Exception;

    AcessoClienteVO registrarAcessoCliente(Date dataAcesso, ClienteVO cliente, SituacaoAcessoEnum situacao,
            DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor,
            UsuarioVO usuario, MeioIdentificacaoEnum meioIdentificacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino, String key) throws Exception;

    AcessoClienteVO registrarAcessoCliente(Date dataAcesso, ClienteVO cliente, SituacaoAcessoEnum situacao,
                                           DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor,
                                           UsuarioVO usuario, MeioIdentificacaoEnum meioIdentificacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino, String key, String timeZoneId, String nomeCodEmpresaAcessou, AutorizacaoAcessoGrupoEmpresarialVO autorizacao) throws Exception;

    AcessoClienteVO registrarAcessoCliente(Date dataAcesso, ClienteVO cliente, SituacaoAcessoEnum situacao,
                                           DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor,
                                           UsuarioVO usuario, MeioIdentificacaoEnum meioIdentificacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino, String key, String timeZoneId, LiberacaoAcessoVO liberacaoAcessoVO, String nomeCodEmpresaAcessou, AutorizacaoAcessoGrupoEmpresarialVO autorizacao) throws Exception;

    AcessoClienteVO registrarAcessoCliente(Date dataAcesso, ClienteVO cliente, SituacaoAcessoEnum situacao,
                                           DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor,
                                           UsuarioVO usuario, MeioIdentificacaoEnum meioIdentificacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino, String key, String timeZoneId, LiberacaoAcessoVO liberacaoAcessoVO, String nomeCodEmpresaAcessou, AutorizacaoAcessoGrupoEmpresarialVO autorizacao, boolean descontarCreditoTreino) throws Exception;

    void excluir(AcessoClienteVO acessoVO,String observacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino) throws Exception;

    AcessoClienteVO consultarUltimoAcesso(ClienteVO cliente, int nivelMontarDados) throws Exception;

    List<AcessoClienteVO> consultarUltimos5Acessos(ClienteVO cliente, int nivelMontarDados) throws Exception;

    List<AcessoClienteVO> consultarUltimosAcessosDeterminadoPeloUsuario(ClienteVO cliente, Integer nrPaginaUltimosAcessos, int nivelMontarDados) throws Exception;

    List<AcessoClienteVO> consultarTodosAcessos(ClienteVO cliente, int nivelMontarDados) throws Exception;

    void setIdEntidade(String idEntidade);

    AcessoClienteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    AcessoClienteVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception;

    Integer consultarQtdAcessosEntreDatas(ClienteVO cliente, Date dataInicio,
            Date dataFim, boolean deveDistinguir) throws Exception;

    Integer consultarQtdAcessosHojePorCliente(Integer codigoCliente) throws Exception;

    String obterHoraUltimoAcessoCliente(Integer codigoCliente) throws Exception;

    List<AcessoClienteVO> consultarUltimoAcessoDia(ClienteVO cliente, Date dia, MeioIdentificacaoEnum meioIdentificacao, int nivelMontarDados) throws Exception;

    int consultarTotalAcessosFiltros(int empresa, java.sql.Date dataInicial, java.sql.Date dataFinal, String horaInicial, String horaFinal) throws SQLException;
    
    AcessoClienteVO consultarUltimoAcessoPorLocal(ClienteVO cliente,Date data, Integer localAcesso, int nivelMontarDados) throws Exception;
    
    JSONArray obterAcessosPorDia(Integer codigoprofessor, Date inicio, Date fim, Integer empresa) throws Exception;
    
    JSONArray obterListaAcessosPorDia(Integer codigoprofessor, Date inicio, Date fim, Integer empresa, boolean alunosTreino) throws Exception;
    
    JSONArray obterUltimosAcessos(Integer empresa, Date dia) throws Exception;
    
    String consultarUltimosAcessosJSON(Integer cliente, Integer nrPaginaUltimosAcessos) throws Exception;
    
    Date obterUltimoAcesso(Integer codigoCliente) throws Exception;

    Date obterUltimoAcessoAntesDe(Integer codigoCliente, Date data) throws Exception;

    List<Integer> alunosAcessoPorProfessorMes(int empresa, int professor, Date mesBase) throws Exception ;

    List<Integer> alunosAcessoPorProfessorPorPeriodo(int empresa, int professor, Date inicio, Date fim) throws Exception ;

    AcessoClienteVO consultarAcessoHorarioTurma(ClienteVO cliente, Date dia, MeioIdentificacaoEnum meioIdentificacao, HorarioTurmaVO horarioTurmaVO, int nivelMontarDados) throws Exception;

    List<AcessoClienteVO> consultarAcessosClienteDataMeioIdentificacao(ClienteVO cliente, Date dataInicial, Date dataFinal,  MeioIdentificacaoEnum meioIdentificacao, int nivelMontarDados) throws Exception;

    /**
     * Calcula a quantidade de acessos de um determinado cliente num período informado agrupando pelo dia
     *
     * @param codigoCliente o código do cliente que terá sua quantidade de acessos calculada
     * @param dataInicial   data inicial dos acessos
     * @param dataFinal     data final dos acessos
     * @return Um {@link AcessoClienteAgrupadoDataVO} em formato JSON populado com a quantidade total de acessos e o agrupamento de acessos por dia
     * @throws Exception Caso o codigoCliente, dataInicial ou dataFinal sejam nulos ou caso aconteça algum erro ao requisitar os dados do banco
     */
    String consultarQuantidadeAcessosClientePeriodo(Integer codigoCliente, Date dataInicial, Date dataFinal) throws Exception;

    AcessoClienteVO consultarUltimoAcessoDiferenteDe(ClienteVO cliente, Integer codigoDiferente, int nivelMontarDados) throws Exception;
    
    List<AcessoClienteVO> consultarAcessoFuturo(int empresa,Date data,int nivelMontarDados) throws Exception;
    
    void excluirComLog(AcessoClienteVO acessoClienteVO,String observacao, ControleCreditoTreinoInterfaceFacade controleCreditoTreino,UsuarioVO usuario) throws Exception;
    
    void alterarAcessoEntradaSaida(AcessoClienteVO acessoClienteVO) throws Exception;
    
    List<AcessoClienteVO> consultaAcessoClienteDia(Integer empresa, Date data,int nivelMontarDados) throws Exception;
    
    List<AcessoClienteVO> obterClientesPelaHora(Date dataInicial,Date dataFinal,Integer empresa,int nivelMontarDados) throws Exception ;
    
    Integer obterQuantidadeClientePelaHora(Date dataInicial,Date dataFinal,Integer empresa) throws Exception ;

    String obterMediaTempoAcademiaPeriodo(Date dataInicial,Date dataFinal,Integer empresa) throws Exception ;
    
    List<AcessoClienteVO> obterClientesPelaHoraComPendeciaFinanceira(Date dataInicial,Date dataFinal,Integer empresa,int nivelMontarDados) throws Exception ;

    /**
     *
     * @param dataInicial
     * @param dataFinal
     * @param empresa               0 para consultar de todas as empresas
     * @return
     * @throws Exception
     */
    Integer obterClientesPelaHoraComPendeciaFinanceira(Date dataInicial,Date dataFinal,Integer empresa) throws Exception ;
    
    List<AcessoClienteVO> obterClientesInativosPelaHora(Date dataInicial,Date dataFinal,Integer empresa,int nivelMontarDados) throws Exception ;

    /**
     *
     * @param dataInicial
     * @param dataFinal
     * @param empresa           0 para consultar de todas as empresas
     * @return
     * @throws Exception
     */
    Integer obterClientesInativosPelaHora(Date dataInicial,Date dataFinal,Integer empresa) throws Exception ;
    
    List<AcessoClienteVO> obterClientesOutraUnidadePelaHora(Date dataInicial,Date dataFinal,Integer empresa,int nivelMontarDados) throws Exception ;

    /**
     *
     * @param dataInicial
     * @param dataFinal
     * @param empresa           0 para consultar de todas as empresas
     * @return
     * @throws Exception
     */
    Integer obterClientesOutraUnidadePelaHora(Date dataInicial,Date dataFinal,Integer empresa) throws Exception ;


    String registrarTicket(String nomeOuMatriculaOuCodigoAcesso, String ticket, String dataHora) throws Exception;

    PessoaAcesso consultarTicket(PessoaAcesso cliente, Date dataAcesso) throws Exception;

    JSONArray obterAcessosDiaWeHelpClientes(Integer empresa, Date dia, boolean enviarCpfComoCodigoInterno) throws Exception;

    JSONArray obterAcessosDiaWeHelpColaboradores(Integer empresa, Date dia, boolean enviarCpfComoCodigoInterno) throws Exception;

    List<AcessoClienteVO> consultar(String sql, final int nivelMontarDados) throws SQLException, Exception;

    JSONObject consultarAlunosNaAcademia(Integer codEmpresa) throws Exception;

    Integer consultarQtdAlunosNaAcademiaPorLocalAcesso(Integer codEmpresa, Integer codigoLocalAcesso) throws Exception;

    JSONObject consultarAlunosNaAcademiaDaqui10minutos(Integer codEmpresa) throws Exception;

    JSONObject consultarAlunosNaAcademia(Integer codEmpresa, Date dataAvaliar) throws Exception;

    Integer obterClientesNaAcademiaAulasAgendadas(Date dataConsulta, Integer codEmpresa) throws SQLException;

    List<AlunoHorarioTurmaTO> consultarClientesNaAcademiaAulasAgendadas(Date dataConsulta, Integer codEmpresa) throws SQLException;

    List<AcessoBloqueadoTO> consultarAcessosBloqueados(String periodo, Date primeiroDiaMes, Date dataConsulta, Integer codEmpresa) throws SQLException;

    List<Integer> consultarClientesNoHorario(Date data, String horaInicial, Integer codEmpresa) throws Exception;

    int contarPresencasPorPeriodoModalidade(Date dataInicio, Date dataFim, Integer codEmpresa, Integer codModalidade) throws Exception;

    AcessoClienteVO consultarUltimoAcessoComEmpresa(Integer codigoCliente, int nivelMontarDados)throws Exception;

    JSONArray consultarClientesSemTemplateBiometrico() throws SQLException;

    JSONArray consultarColaboradoresSemTemplateBiometrico() throws SQLException;

    Map<Integer, Integer> consultarQtdClientesAcessoGymPass(Date dataInicio, Date dataFim, Integer codigoEmpresa)throws Exception;

    Map<Integer, Integer> consultarQtdClientesAcessoGymPassPorCheckin(Date dataInicio, Date dataFim, Integer codigoEmpresa)throws Exception;

    List<ResumoPessoaBIAcessoGymPassVO> consultarClientesAcessoGymPass(Date dataInicio, Date dataFim, Integer codigoEmpresa)throws Exception;

    List<AcessoRealizadoTO> consultarAcessosRealizadosDia(Date dataConsulta, Integer codEmpresa) throws SQLException;

    List<ResumoPessoaBIAcessoGymPassVO> consultarClientesAcessoGymPassPorCheckin(Date dataInicio, Date dataFim, Integer codigoEmpresa)throws Exception;
}
