package negocio.interfaces.plano;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import importador.json.ProdutoImportacaoJSON;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.plano.ProdutoImagemTO;
import org.json.JSONArray;
import negocio.comuns.plano.PacotePersonalVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a
 * camada de controle e camada de negócio (em especial com a classe Façade). Com
 * a utilização desta interface é possível substituir tecnologias de uma camada
 * da aplicação com mínimo de impacto nas demais. Além de padronizar as
 * funcionalidades que devem ser disponibilizadas pela camada de negócio, por
 * intermédio de sua classe Façade (responsável por persistir os dados das
 * classes VO).
 */
public interface ProdutoInterfaceFacade extends SuperInterface {

    public ProdutoVO novo() throws Exception;

    public void incluir(ProdutoVO obj) throws Exception;

    public void alterar(ProdutoVO obj) throws Exception;

    public void excluir(ProdutoVO obj) throws Exception;

    public ProdutoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPaginado(ControleConsulta controleConsulta, boolean controlarAcesso, int nivelMontarDados, ConfPaginacao confPaginacao) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoTipoProduto(Integer valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoDiferenteTipoProduto(Integer valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoDiferenteTipoProdutoAtivo(Integer valorConsulta, List<String> tipoProduto, boolean controlarAcesso,boolean produtosComEstoque,int codigoEmpresa, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoCategoriaProduto(String valorConsulta, int nivelMontarDados) throws Exception;

    public List<ProdutoVO> consultarPorCodigoCategoriaProduto(Integer codigoCategoriaProduto, TipoProduto tp, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoAtivo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoAtivo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, String arrayNotInTipos) throws Exception;

    public List consultarPorDescricaoTipoProduto(String valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorProdutoZillyon(boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ProdutoVO> consultarPorDescricaoTipoProdutoAtivo(String valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarProdutosComControleEstoque(Integer codigoEmpresa, Integer codigoCategoriaProduto, String descricaoProduto, boolean somenteProdutoEstoqueAtivo, int nivelMontarDados) throws Exception;

    public Optional<ProdutoVO> consultarProdutosComControleEstoquePorCodigoDeBarrasOuDescricao(String codigoBarras, String descricaoProduto, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoDiferenteTipoProduto(String valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoDiferenteTipoProdutoAtivo(String valorConsulta, List<String> listaTipoProduto, boolean controlarAcesso,Boolean somenteComEstoque,int codigoEmpresa, int nivelMontarDados) throws Exception ;

    public List consultarPorTipoVigencia(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ProdutoVO consultarPorTipoProduto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ProdutoVO> consultarProdutosPorTipoProdutoGympass(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ProdutoVO consultarPorCodigoProduto(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public ProdutoVO consultarPorCodigoProdutoAtivo(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public ProdutoVO consultarProdutoPorCodigoTipoProduto(Integer valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoTipoProdutoSemPMCDDE(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoTipoProdutoSemPMCDDEAtivo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoTipoProdutoSemPMCDDE(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoTipoProdutoSemPMCDDEAtivo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List<ProdutoVO> consultarProdutosPorTipoProduto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ProdutoVO> consultarProdutosPorTipoProdutoTipoVigencia(String tipoProduto, String tipoVigencia, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ProdutoVO> consultarProdutosPorTipoProdutoNrDiasVigencia(String tipoProduto, int nrDiasVigencia, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarProdutosPorTipoProduto(List<String> valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ProdutoVO> consultarProdutosComMovProduto(Date dataInicial, Date dataFinal, String tipoProduto, Integer empresas, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void incluir(ProdutoVO obj, boolean ignorarPermissao) throws Exception;

    public void alterar(ProdutoVO obj, boolean centralEventos) throws Exception;

    public void excluir(ProdutoVO obj, boolean centralEventos) throws Exception;

    public List consultarPorNomeProdutoComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeProdutoComLimite(String valorConsulta, boolean controlarAcesso, Integer codigoEmpresa, int nivelMontarDados) throws Exception;

    List consultarTodosProdutosSemPontuacaoPaginado(boolean controlarAcesso, Integer codigoEmpresa, int nivelMontarDados, ListaPaginadaTO listaPaginadaTO) throws Exception;

    List consultarPorTodosProdutosComLimit(boolean controlarAcesso, Integer codigoEmpresa, int limit, int nivelMontarDados) throws Exception;

    public ProdutoVO consultarPorNomeProduto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Map<Integer, String> consultarProdutosPorCategoriaSimplificado(Integer categoria, final String situacao) throws Exception;

    public String obterTipoProduto(Integer codigo) throws Exception;

    public List<ProdutoVO> obterProdutosDePacotesGenericosDoCliente(int cliente) throws SQLException, Exception;

    public List consultarPorDescricaoCategoriaTipoProdutoAtivo(String descricaoProduto, Integer codigoCategoria, String tipoProduto, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar, String situacao) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int empresa, String situacao) throws SQLException;

    public List<PacotePersonalVO> consultarPacotesPersonal(Integer produto) throws Exception;

    public List consultarPorTodosServicoComLimite(boolean controlarAcesso, Integer codigoEmpresa, int nivelMontarDados) throws Exception;

    public List consultarPorNomeServicoComLimite(String valorConsulta, boolean controlarAcesso, Integer codigoEmpresa, int nivelMontarDados) throws Exception;

    public List<ProdutoVO> consultarPorTamanhoArmario(Integer tamanho, Integer empresa) throws Exception;

    public List<ProdutoVO> consultarTodosArmario(Integer empresa) throws Exception;

    public List<ProdutoVO> obterProdutosDePacotesGenericosDaAgenda(int agenda) throws SQLException, Exception;

    public List consultarPorProdutoComVigencia(String valorConsulta, Integer codigoEmpresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;
    public ProdutoVO consultarPorDescricao(String descricao, int nivelMontarDados)throws Exception;

    public ProdutoVO consultarProdutoGenerico(boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public ProdutoVO consultarPorCodigo(Integer codigoProduto, int nivelMontarDados) throws Exception;

    ProdutoVO criarOuConsultarProdutoGymPass() throws Exception;

    ProdutoVO criarOuConsultarProdutoTotalPass() throws Exception;

    ProdutoVO criarOuConsultarProdutoGoGood() throws Exception;

    public JSONArray listaJsonProdutosPorTipo(TipoProduto tipo) throws Exception;

    ProdutoVO obterProdutoCfgEmpresa(Integer produto, Integer empresa) throws Exception;

    public List<ProdutoVO> consultarParaAulaCheia(Integer empresa, boolean controlarAcesso, int nivelMontarDados, boolean somenteAtivos) throws Exception;

    public void alterarConfigImpostos(ProdutoVO obj) throws Exception;

    public ProdutoVO criarOuConsultarProdutoPorTipoNrDiasVigencia(final String tipo, final int nrDiasVigencia, final String descricao, int nivelMontarDados) throws Exception;

    public ProdutoVO criarOuConsultarProdutoPorTipo(final String tipo, final String descricao, int nivelMontarDados) throws Exception;

    ProdutoVO criarOuConsultarExisteProdutoPorDescricao(String descricao, String tipo, Double valor) throws Exception;

    void alterarPontos(Integer codigo, Integer pontos) throws Exception;

    ProdutoVO obterProdutoPadraoProRata() throws Exception;

    ProdutoVO obterProdutoPadraoPersonal() throws Exception;

    ProdutoVO obterProdutoPadraoAdesaoPlanoRecorrente() throws Exception;

    ProdutoVO obterProdutoPadraoAnuidadePlanoRecorrente() throws Exception;

    void importarProdutos(List<ProdutoImportacaoJSON> listaProdutosJSON, boolean validarProdutoMesmoNome,
                          String tipoProdutoPadrao, Integer categoriaPadrao, UsuarioVO usuarioVO) throws SQLException;

    List<ProdutoVO> consultarParaVendasOnline(Integer codigo, Integer categoria, Integer empresa, int nivelMontarDados) throws Exception;

    void alterarImagens(List<ProdutoImagemTO> imagensSalvar, ProdutoVO obj, UsuarioVO usuarioVO) throws Exception;

    List consultarParaVendaAvulsa(String valorConsulta, String codigoBarrasGrama, Integer codigoEmpresa, int nivelMontarDados) throws Exception;

    boolean consultarExistePorCodigoTipoProduto(String tipoProduto) throws Exception;

    List<Integer> produtoNaoApresentaAppPacto(List<Integer> produto, Integer empresa) throws Exception;

    JSONArray obterProdutosGymPass(String gymID) throws Exception;

    void alterarFlagMesclado(Integer codigo, boolean mesclado) throws Exception;

}
