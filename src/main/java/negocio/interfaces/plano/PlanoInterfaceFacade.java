package negocio.interfaces.plano;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.enumerador.TipoReajuste;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface PlanoInterfaceFacade extends SuperInterface {

    public PlanoVO novo() throws Exception;

    public void incluir(PlanoVO obj) throws Exception;

    public void alterar(PlanoVO obj) throws Exception;

    public void excluir(PlanoVO obj) throws Exception;
    //public void gerarPlanoDefasado(PlanoVO obj, List listaLog) throws Exception;

    PlanoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    Date consultarDataInicioFutura(Integer codigo) throws Exception;

    PlanoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados, Boolean site) throws Exception;

    public PlanoVO consultarPorChavePrimaria(Integer codigoPrm, boolean controleAcesso, int nivelMontarDados) throws Exception;

    public List<PlanoVO> consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<PlanoVO> consultarPorDescricao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PlanoVO consultarPorDescricao(String descricao, int nivelMontarDados)throws Exception;

    public List<PlanoVO> consultarPorDescricaoBolsaVigente(String valorConsulta, Boolean controlarAcesso,Integer empresa, int nivelMontarDados) throws Exception;

    List<PlanoVO> consultarPorDescricaoDataIngresso(String valorConsulta,
                                                           Date ingressoate,
                                                           Integer empresa,
                                                           boolean controlarAcesso,
                                                           boolean consultarEmPlanoEmpresaVendaPermitida,
                                                           int nivelMontarDados) throws Exception;

    List<PlanoVO> consultarPorDescricaoDataIngresso(String valorConsulta,
                                                           Date ingressoate,
                                                           Integer empresa,
                                                           boolean controlarAcesso,
                                                           boolean consultarEmPlanoEmpresaVendaPermitida,
                                                           boolean planoPersonal,
                                                           int nivelMontarDados) throws Exception;

    List<PlanoVO> consultarPorDescricaoDataIngresso(String valorConsulta,
                                                    Date ingressoate,
                                                    Integer empresa,
                                                    boolean controlarAcesso,
                                                    boolean consultarEmPlanoEmpresaVendaPermitida,
                                                    boolean planoPersonal,
                                                    Integer categoria,
                                                    int nivelMontarDados) throws Exception;

    public List<PlanoVO> consultarPorDescricaoDataIngressoSite(String valorConsulta, Date ingressoate, Integer empresa, boolean controlarAcesso, boolean totem, Integer codigoPlano, int nivelMontarDados) throws Exception;

    public List<PlanoVO> consultarPorDescricaoDataIngressoSite(String valorConsulta, Date ingressoate, Integer empresa, boolean controlarAcesso, boolean planoSite, boolean totem, Integer codigoPlano, int nivelMontarDados) throws Exception;

    public List<PlanoVO> consultarPorCodigoEmpresa(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List<PlanoVO> consultarPlanosPorCodigoEmpresa(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorVigenciaDe(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorVigenciaAte(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Boolean planoDentroVigencia(int plano) throws Exception;

    boolean enviarDependenteFoguete(int plano) throws Exception;

    public PlanoVO consultarPorCodigoContrato(Integer codigo, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    /**
     * Responsável por realizar uma consulta de <code>Plano</code> através do valor do atributo
     * <code>Date vigenciaAte</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PlanoVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorComposicao(Integer codigo, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<PlanoVO> consultarIngressoAte(Date data, Boolean ativos, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void incluirSemCommit(PlanoVO obj) throws Exception;

    public String consultarDescricaoPorCodigo(Integer codigo) throws Exception;

    public StringBuilder alterarValoresContratos(TipoReajuste tipo, final double valor, final int plano, final Date dataVencimento,
                                                 final UsuarioVO usuario, boolean simular, final String tiposContrato,
                                                 final Date dataLancamentoInicio, final Date dataLancamentoFinal,
                                                 double valorMensalAntigo, double valorMensalNovo, Integer codigoContrato,
                                                 Integer duracaoContrato, String tipoAlteracao, boolean gerarNovaParcela,
                                                 Date dataLancamentoNovaParcela, Date dataLancamentoParcelaCancelada, Date anoReferencia) throws Exception;

    public String consultarJSON(Integer empresa,final String tipoPlano) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i,final String tipoPlano) throws Exception;
    
    public PlanoDuracaoVO calculoDuracao(PlanoDuracaoVO planoDuracao, Double valorModalidades) throws Exception, ConsistirException;

    public boolean consultarPlanoRenovacaoAutomatica(int codigo) throws Exception;


    Integer consultarTotalContratosAlterarQtdDiasCancelamentoAutomatico(Integer codigoPlano) throws Exception;

    void alterarContratosAlterarQtdDiasCancelamentoAutomatico(Integer codigoPlano, Integer diasCancelamentoAutomatico) throws Exception;
    public boolean existeContratoRenovavelAutomaticamenteComEssaConfiguracao(final int codigoPlano, final int codigoModalidade,final int nrVezesPorSemana, final int codigoHorario, final int duracao,final int condicaopagamento, final int pacote) throws Exception;

    public void setarContratoTextoPadrao(Integer texto, Integer plano, UsuarioVO usuarioVO, boolean permiteImpressaoContratoMutavel) throws Exception;

    public void setarPlanoPersonalTextoPadrao(Integer texto, Integer plano) throws Exception;

    void alterarFrequenciaMaximaContratosLancados(Integer quantidadeMaximaPermitida, Integer plano) throws Exception;

    boolean existePlanoComMesmoNome(PlanoVO obj) throws Exception;

    public boolean existeContratoDuracaoCondicao(Integer codigoPlano, Integer duracao, boolean validarCondicaoPagamento) throws Exception;

    /**
     * @param descricao                                    descrição do plano
     * @param empresa                                      código da empresa
     * @param nivelMontarDados                             Uteis.NIVELMONTARDADOS_*
     * @param site                                         para consultar somente planos vendidos no site
     * @param consultarEmPlanoEmpresaVendaPermitida        para consultar somente planos que estejam em planoempresa com venda=true
     * @return
     * @throws Exception
     */
    List<PlanoVO> consultarPorDescricaoPlanoVigente(String descricao, Integer empresa, int nivelMontarDados, Boolean site, boolean consultarEmPlanoEmpresaVendaPermitida) throws Exception;

    List<PlanoVO> consultarPorDescricaoPlanoVigente(String descricaoPlano, Integer empresa, Integer nivelMontarDados) throws Exception;

    List<PlanoVO> consultarPorPlanoVigente(Integer empresa, int nivelMontarDados) throws Exception;

    List<PlanoVO> consultarPlanoVigenteSemCampanhaPaginado(Integer empresa, int nivelMontarDados, ListaPaginadaTO listaPaginadaTO) throws Exception;

    Map<Integer, String> consultarPlanosSimplesVendaRapida(Integer empresa) throws Exception;

    Map<Integer, String> consultarPlanosSimplesVendaRapidaClienteSelecionado(Integer empresa, Integer codigoCategoriaCliente) throws Exception;

    List<PlanoVO> consultarPlanosSimplesVendaRapida(Integer empresa, int nivelMontarDados) throws Exception;

    /**
     * Consulta os planos que ficarão inativos no próximo mês.
     *
     * @param empresa             Código da empresa (caso seja necessário filtrar).
     * @param nivelMontarDados    Nível de detalhamento dos dados dos planos (NIVELMONTARDADOS_*).
     * @return                    Lista de Planos que ficarão inativos no mês seguinte.
     * @throws Exception          Em caso de falha de conexão ou inconsistências.
     */
    List<PlanoVO> consultarPlanosProximosInativar(Integer empresa, int nivelMontarDados) throws Exception;

    Integer consultarTotalContratosCancelamentoProporcional(Integer codigoPlano) throws Exception;

    void alterarContratosCancelamentoProporcional(Integer codigoPlano, boolean cancelamentoproporcional, Integer qtddiascobrarproximaparcela, Integer qtddiascobraranuidadetotal, boolean somenteContratosRenovados) throws Exception;

    List<PlanoVO> consultarTodos(Date dataReferencia, int nivelMontarDados) throws Exception;

    void alterarPontos(Integer codigo, Integer pontos, Integer tipoitem) throws Exception;

    List<PlanoVO> consultarPorDescricaoVigenciaDeAte(String descricao, Integer empresa, int nivelMontarDados, Boolean site,
                                                          boolean consultarEmPlanoEmpresaVendaPermitida) throws Exception;

    List<PlanoVO> consultarVigentesTodasEmpresas(int nivelMontarDados, Boolean site) throws Exception;

    public int contarContratosAtivosPorPlanoCliente(int codigoPlano, Integer codigo) throws Exception;

    List<PlanoVO> consultarVigentes(Integer empresa, int nivelMontarDados, Boolean site,
                                    boolean consultarEmPlanoEmpresaVendaPermitida, Boolean pactoFlow) throws Exception;

    List<PlanoVO> consultarVigentesPactoFlow(Integer empresa,
                                                    int nivelMontarDados,
                                                    Boolean site,
                                                    boolean consultarEmPlanoEmpresaVendaPermitida) throws Exception;

    /**
     * Consulta o código de todos os planos cadatrados no sistema.
     * @return
     * @throws Exception
     */
    public List<Integer> consultarTodosCodigos() throws  Exception;

    /**
     * Atualiza o campo descricao do plano.
     * @param codigo
     * @param descricao
     * @throws Exception
     */
    public void alterarDescricaoPlano(Integer codigo, String descricao) throws  Exception;

    public Integer consultarPlanoDataMaiorQueZero(Integer plano) throws Exception;

    public List<PlanoVO> consultarTodosOsPlanosVendas(Integer empresa) throws Exception;

    List<PlanoVO> consultarPorPermissaoVenda(Integer empresa, String filtroSelecionado, int nivelMontarDados) throws Exception;
    List<PlanoVO> consultarPlanosComBolsa(Integer empresa) throws Exception;
    PlanoVO consultaPlanoDelSoft(Integer empresa, Integer codigoPlano) throws Exception;

    List<PlanoVO> consultarPlanoParaTransferencia(String descricao, boolean controleAcessoMultiplasEmpresasPorPlano,
                                                  EmpresaVO empresaVO) throws Exception;

    List<String> obterChavesEmpresasPermiteAcesso(Integer codigo) throws Exception;

    PlanoVO findByIdExterno(String idExterno, int codigoEmpresa, int nivelMontarDados) throws Exception;
}
