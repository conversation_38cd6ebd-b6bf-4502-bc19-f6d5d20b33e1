package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.enumerador.BandeirasCapptaEnum;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoDebitoOnlineEnum;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface OperadoraCartaoInterfaceFacade extends SuperInterface {


    public OperadoraCartaoVO novo() throws Exception;

    public void incluir(OperadoraCartaoVO obj) throws Exception;

    public void alterar(OperadoraCartaoVO obj) throws Exception;

    public void excluir(OperadoraCartaoVO obj) throws Exception;

    public OperadoraCartaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoOperadora(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<OperadoraCartaoVO> consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<OperadoraCartaoVO> consultarPorDescricao(String valorConsulta, boolean somenteAtivos, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluir(OperadoraCartaoVO obj, boolean centralEventos) throws Exception;

    public void alterar(OperadoraCartaoVO obj, boolean centralEventos) throws Exception;

    public void alterarSemCommit(OperadoraCartaoVO obj) throws Exception ;

    public void excluir(OperadoraCartaoVO obj, boolean centralEventos) throws Exception;

    public List consultarPorCodigoIntegracaoAPF(int codigoIntegracao, int nivelMontarDados) throws Exception;

    List consultarPorCodigoIntegracaoAPF(int codigoIntegracao, Boolean ativo, Boolean credito, int nivelMontarDados) throws Exception;

    public List consultarPorTipo(boolean credito, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoGeoidt(boolean credito, boolean controlarAcesso, int nivelMontarDados, boolean geoitd) throws Exception;

    //public String consultarJSON(String situacao, Boolean isgeoitd) throws Exception;
    public String consultarJSON(String situacao) throws Exception;

    //public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, String situacao, Boolean isGeoitd) throws Exception;
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, String situacao) throws Exception;

    /**
     * Retorna a {@link OperadoraCartaoVO} a partir da {@link OperadorasExternasAprovaFacilEnum} vinculada ao atributo <code>codigoIntegracaoVindi</code>.
     * @param operadora
     * @return
     * @throws Exception
     */
    public OperadoraCartaoVO consultarPorCodigoIntegracaoVindi(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    public OperadoraCartaoVO consultarPorCodigoIntegracaoCielo(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    public List consultarPorTipoDebitoOnline(TipoDebitoOnlineEnum tipoDebitoOnlineEnum, int nivelMontarDados) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoERede(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoPagoLivre(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoFacilitePay(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoCeopag(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoDCCCaixaOnline(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoPinBank(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoOnePayment(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoMaxiPago(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoFitnessCard(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoGetNet(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoStoneOnline(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoMundiPagg(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoAPF(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoPagarMe(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoPagBank(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoStoneOnlineV5(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarPorCodigoIntegracaoStripe(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception;

    OperadoraCartaoVO consultarOuCriaSeNaoExistirPorCodigoIntegracaoCappta(BandeirasCapptaEnum bandeirasCapptaEnum, boolean credito, int nivelMontarDados);

    List consultarTodas(boolean ativo, boolean credito, int nivelMontarDados) throws Exception;

    List<OperadoraCartaoVO> consultarTodas(boolean ativo, int nivelMontarDados) throws Exception;

    List<OperadoraCartaoVO> consultarOperadorasTipoConvenio(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, boolean criarSeNaoExistir) throws Exception;

    void criarOperadorasAutomatico(List<OperadoraCartaoVO> listaAdicionar) throws Exception;

    OperadoraCartaoVO obterOperadoraCartaoPadrao(boolean credito);

    OperadoraCartaoVO consultarOuCriaPorCodigoIntegracao(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, Integer nrMaxParcelas,
                                                         OperadorasExternasAprovaFacilEnum operadoraEnum) throws Exception;

    OperadoraCartaoVO consultarOuCriaSeNaoExistirPinpad(OperadorasExternasAprovaFacilEnum operadoraEnum,
                                                        OpcoesPinpadEnum opcoesPinpadEnum,
                                                        boolean credito, int nivelMontarDados);

    boolean existeOperadoraComCodigoIntegracao(OperadoraCartaoVO operadoraCartaoVO) throws Exception;

    OperadoraCartaoVO consultarOperadoraPadraoPorDescricao(String valorConsulta, int nivelMontarDados) throws Exception;

}
