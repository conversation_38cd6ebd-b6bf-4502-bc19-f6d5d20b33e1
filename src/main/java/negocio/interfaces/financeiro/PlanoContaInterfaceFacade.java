
package negocio.interfaces.financeiro;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;

import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.interfaces.basico.SuperInterface;

public interface PlanoContaInterfaceFacade extends SuperInterface {

    public void incluir(final PlanoContaTO obj) throws Exception;
    public void incluirSemCommit(final PlanoContaTO obj) throws Exception;

    public void alterar(final PlanoContaTO obj) throws Exception;
    public void alterarSemCommit(PlanoContaTO obj) throws Exception;

    public void excluir(final PlanoContaTO obj) throws Exception;

    /**
     * Copia os nós, copiando também os filhos e mantendo a hierarquia entre os códigos dos planos
     * @param planoDestino PlanoContaTO plano onde se está colando
     * @param planoOrigem PlanoContaTO plano copiado
     * @throws Exception
     */
    public void moverNos(PlanoContaTO planoDestino, PlanoContaTO planoOrigem)
            throws Exception;

    /**
     * Retorna o código do próximo filho desse plano
     * @param planoDest String
     * @return String '###'
     * @throws SQLException
     * @throws Exception
     */
    public String obterCodigoProximoFilho(String planoDest)
            throws SQLException, Exception;

    public List<PlanoContaTO> consultar(String codigoplanocontas, String descricao, Integer codigoInterno, String tipoConsulta) throws Exception;

    public List<PlanoContaTO> consultarTodos() throws Exception;

     List<PlanoContaTO> consultarTodos(Integer tipoes, Integer empresa) throws Exception;

    List<BIPlanoContaDTO> consultarTodosPlanoContaComValoresPorPeriodoEEmpresa(Integer empresa, Date inicio, Date fim) throws Exception;

     List<PlanoContaTO> consultarTodasDespesas(Integer empresa) throws Exception;

     List<PlanoContaTO> consultarTodasReceitas(Integer empresa) throws Exception;

     List<Double> consultarValorRateioPorPlanoContaDataVencimentoEEmpresa(String codigosPlanoContaSeparadosPorVirgula, Date inicio, Date fim, Integer empresa) throws Exception;

     List<Double> consultarValoresServicos(Date inicio, Date fim, Integer empresa) throws Exception;

     HashMap<String, Double> valorMedioMensalContratos(Date inicio, Date fim, Integer empresa) throws Exception;

     HashMap<String, Double> valorMedioMensalContratosAtivos(Integer empresa) throws Exception;

     List<Double> consultarValoresProdutos(String codigosProdutosContaSeparadosPorVirgula, Date inicio, Date fim, Integer empresa) throws Exception;

     List<BIProdutoDTO>  consultarProdutos() throws Exception;

     int  quantidadeContratosAtivos(Integer empresa) throws Exception;

     int quantidadeContratosInativos(Date inicio, Date fim, Integer empresa) throws Exception;

    double churnRate(Date inicio, Date fim) throws Exception;

     double consultarValorRateioPorAluno(Integer codigoCliente) throws Exception;

     Date consultarDataContratoVigenciaAte(Integer codigoCliente, Integer contrato);
    Date consultarDataInicioVinculoAtual(Integer codigoCliente, Integer contrato);

     double valorTotalContratosPorPeriodoEEmpresa(Date inicio, Date fim, Integer empresa) throws Exception;

     double valorTotalDevolucaoPorPeriodoEEmpresa(Date inicio, Date fim, Integer empresa) throws Exception;

     int tempoDuracaoMedioContratos(Date inicio, Date fim, Integer empresa) throws Exception;

     boolean exibirBI(Integer codigo) throws Exception;

    HashMap<Integer, List<BILtvGraficoChurnRateDTO>> getContratosGafico(Integer emrpesa) throws Exception;

    List<BILtvChurnRateEmpresa> churnRatePorEmpresa() throws Exception;

    public List<PlanoContaTO> consultarPorEquivalenciaDRE(int codigoEquivalenciaDRE) throws Exception;

    public List<PlanoContaTO> consultarPorCaixa(int codigoCaixa) throws Exception;

    public void excluirTodosPlanos() throws Exception;
    
    public PlanoContaTO consultarPorCodigoPlano(String codigoPlano) throws Exception;

    public PlanoContaTO consultarPorCodigoPlanoTipo(String codigoPlano, TipoES tipoES) throws Exception;

    public Boolean verificarExistenciaPlano(String codigoPlano) throws Exception;
    
    public Map<Integer, String> obterCodigoPlanoIrmaos(String codigoPai) throws Exception;
    
    public List<Integer> obterCodigoFilhos(String codigoPai, String exceto) throws Exception;

    public void atualizarCodigoPlanoContas(Integer codigoPlano, String codigoNovo,String codigoAntesAlteracao,List<Integer> codigosFilhos, Integer tipoES) throws Exception;
    
    public Integer obterTipoES(String codigoPlano) throws Exception;
    
    public List<PlanoContaTO> consultarPlanoFilhos(PlanoContaTO planoContaPai) throws SQLException, Exception;

    public boolean consultarSeExistePlanoContas() throws Exception;

    public String consultarCodigoPaiContendoSomenteUmNumeroEAdicionaUm() throws SQLException, Exception;

    public PlanoContaTO obter(int pkey) throws Exception;

    public void alterarPlanosFilhos(PlanoContaTO obj) throws Exception;
    
    public Map<String, TipoEquivalenciaDRE> obterEquivalencias() throws Exception;


    public void trocarPlanoContas(Integer codigoPlanoAntigo, Integer codigoPlanoNovo) throws Exception;

    public boolean verificarExisteRelacionamento();

    public void rePovoarPlanoContas() throws Exception;

    public boolean verificarExisteRelacionamento(Integer codigo);

    PlanoContaTO consultarPorChavePrimaria(Integer codigo) throws Exception;

    public Integer consultarCodigoLumi(Integer codigoPlano) throws Exception;
}

