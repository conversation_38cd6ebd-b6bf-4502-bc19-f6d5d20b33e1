package negocio.interfaces.financeiro;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estudio.modelo.DisponibilidadeVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.financeiro.ClientesICV;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a
 * camada de controle e camada de negócio (em especial com a classe Façade). Com
 * a utilização desta interface é possível substituir tecnologias de uma camada
 * da aplicação com mínimo de impacto nas demais. Além de padronizar as
 * funcionalidades que devem ser disponibilizadas pela camada de negócio, por
 * intermédio de sua classe Façade (responsável por persistir os dados das
 * classes VO).
 */
public interface VendaAvulsaInterfaceFacade extends SuperInterface {

    public VendaAvulsaVO novo() throws Exception;

    public Integer incluir(VendaAvulsaVO obj, boolean parcelaPaga,List<DisponibilidadeVO> listaDisponibilidade, Date dataIniAgendar, Date dataFimAgendar) throws Exception;

    public void alterar(VendaAvulsaVO obj) throws Exception;

    public void excluir(VendaAvulsaVO obj) throws Exception;

    void excluirVendasOnline(VendaAvulsaVO obj) throws Exception;

    public void excluirVendaAvulsaConsumidor(Integer codMovProduto) throws Exception;

    public VendaAvulsaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    String obterContratoProdutoTextoPadrao(
            Integer vendaAvulsa, Integer produto) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeComprador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoComprador(String tipoComprador, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarConsumidorPorNomeComprador(String nomeComprador, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarConsumidorPorValorCompra(double valorCompra, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarConsumidorPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ItemVendaAvulsaVO> listarVendaTipoProdutoCliente(Integer idCliente, Integer idEmpresa, String tipoProduto) throws SQLException;

    void setIdEntidade(String aIdEntidade);

    List<ItemVendaAvulsaVO> consultarPaginado(Integer idCliente, Integer idEmpresa, ConfPaginacao confPaginacao) throws Exception;

    Integer incluirSemCommit(VendaAvulsaVO obj, boolean parcelaPaga, Date dataReferencia) throws Exception;

    public Integer incluirVendaAvulsaArmario(VendaAvulsaVO obj, boolean parcelaPaga, Date dataReferencia) throws Exception;

    public Integer incluirVendaAvulsaParaEdicaoPagamento(VendaAvulsaVO obj) throws Exception;

    public void excluirSemCommit(VendaAvulsaVO obj) throws Exception;

    public void excluirSemCommit(VendaAvulsaVO obj, boolean verificarAcesso) throws Exception;

    public Integer consultaQuantidadeSessaoICV(Date prmIni, Date prmFim, Integer empresa, List<ColaboradorVO> listaColaborador, List<TipoBVEnum> listaTipoBVEnum) throws Exception;

    public List<ClientesICV> consultaListaSessaoICV(Date prmIni, Date prmFim, Integer empresa, List<ColaboradorVO> listaColaborador, List<TipoBVEnum> listaTipoBVEnum) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;
    
    public String obterNomeComprador(Integer codigoVenda) throws Exception;
    
    public String gerarVendaCreditos(Integer codigoColaborador, Integer unidadesCredito, Integer codigoUsuario);

    public Boolean consultarPorClienteProdutoGenerico(int cliente, int produto) throws Exception;

    public VendaAvulsaVO consultarPorClienteProduto(int cliente, int produto) throws Exception;

    public String gerarVendaProdutoCliente(Integer codigoCliente, Integer codigoProduto, Integer codigoUsuario, Date vencimento, Double valor);

    VendaAvulsaVO gerarVendaAtestado(ClienteVO clienteVO, Date dataInicio, Date dataFinal, ProdutoVO produto, Double valor, UsuarioVO responsavel, EmpresaVO empresaVO) throws Exception;

    Integer incluir(VendaAvulsaVO obj, boolean parcelaPaga, Date dataReferencia,  List<DisponibilidadeVO> listaDisponibilidade, Date dataIniAgendar, Date dataFimAgendar) throws Exception;

    void alterarPessoa(Integer pessoa, Integer vendaAvulsa, String descricaoLog, UsuarioVO usuarioVO, boolean controlaTransacao) throws Exception;
}
