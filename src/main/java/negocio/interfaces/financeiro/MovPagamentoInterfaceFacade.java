package negocio.interfaces.financeiro;

import br.com.pactosolucoes.enumeradores.StatusMovimentacaoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.MovPagamentoIntegracaoTO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoOperacaoContaCorrenteEnum;
import negocio.interfaces.basico.SuperInterface;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import negocio.comuns.basico.EmpresaVO;
import servlet.caixaemaberto.PagamentoDTO;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface MovPagamentoInterfaceFacade extends SuperInterface {

    public MovPagamentoVO novo() throws Exception;

    public void incluir(MovPagamentoVO obj) throws Exception;

    public ReciboPagamentoVO incluirListaPagamento(List listaFormaPagamento, List listaParcela, MovimentoContaCorrenteClienteVO movimentoContaCorrenteCliente, ContratoVO contrato, Boolean receberTroco, Double valorResiduoParaDeposito) throws Exception;

    public ReciboPagamentoVO incluirListaPagamento(List listaFormaPagamento, List listaParcela, MovimentoContaCorrenteClienteVO movimentoContaCorrenteCliente, ContratoVO contrato, Boolean receberTroco, Double valorResiduoParaDeposito, Boolean controlarTransacao, ReciboPagamentoVO recibo) throws Exception;

    public ReciboPagamentoVO incluirListaPagamento(List lista, List listaParcela, MovimentoContaCorrenteClienteVO movimentoContaCorrenteCliente, ContratoVO contrato, Boolean receberTroco, Double valorResiduoParaDeposito, Boolean controlarTransacao, ReciboPagamentoVO recibo, TipoOperacaoContaCorrenteEnum tipoOperacaoContaCorrenteEnum) throws Exception;

    public void alterar(MovPagamentoVO obj) throws Exception;

    public void excluir(MovPagamentoVO obj) throws Exception;

    public MovPagamentoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomePessoa(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorDataPagamento(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDataLancamento(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoFormaPagamento(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomePagador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCpfPagador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNumeroCheque(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorAgenciaCheque(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorBancoCheque(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNumeroCartao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoOperacaoCartao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<MovPagamentoVO> consultarPorCodigoRecibo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void excluirSemCommit(MovPagamentoVO obj , boolean controlarAcesso) throws Exception;

    public List consultarPagamentoDeUmContrato(Integer codigoContrato, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPagamentoDeUmaParcela(Integer codigoParcela, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void excluirMovPagamentoEstornoContrato(Integer codigoReciboPagamento) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirMovPagamentos(List objetos) throws Exception;

    public List<MovPagamentoVO> consultarPorCodigoClienteParaHistoricoPagamento(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorClienteContratoParaHistoricoPagamento(Integer valorConsulta, int contrato, int nivelMontarDados) throws Exception;

    public void salvarPagamentoContratoEvento(Integer codigoContratoEvento) throws Exception;

    public MovimentoContaCorrenteClienteVO gerarMovimentoContaCorrenteCliente(MovPagamentoVO movPagamento,
                                                                              Double valorMovPagamento, String descricao, String tipoMovimento) throws Exception;

    public MovPagamentoVO obterPorCodigoEvento(Integer codigoEvento) throws Exception;

    public List consultarPorConsumidorParaHistoricoPagamento(String nomeComprador, int nivelMontarDados) throws Exception;

    public void incluirSemCommit(MovPagamentoVO obj) throws Exception;

    void incluirSemCommit(MovPagamentoVO obj, boolean controlarTransacao) throws Exception;

    public void excluirPagtoChequeIncluirOutro(MovPagamentoVO antigo, MovPagamentoVO novo) throws Exception;

    public void alterarPagtoChequeIncluiOutro(MovPagamentoVO antigo, MovPagamentoVO novo) throws Exception;

    public List consultarComFiltros(int empresa, String nomep, String nomeTerceiro, Date lanci, Date lancf, String horalanci, String horalancf,
                                    Date compi, Date compf, String horacompi, String horacompf, String formasPagamento, String cpf, String matricula,
                                    String nomeClienteContrato, ChequeVO chequeVO, Boolean pesquisarComLote, UsuarioVO nomeOperador,
                                    String codigoAutorizacao, Integer operadoraCartao,
                                    String nsu, boolean chequeAvista, boolean chequeAprazo, boolean considerarDataOriginal,
                                    List<Integer> codigoLote, boolean mostrarCancelados, int nivelMontarDados, Integer codigoCentroCusto, boolean antecipados,
                                    StatusMovimentacaoEnum statusMovimentacao, Integer adquirente, String numeroDocumento) throws Exception;

    public List consultarComFiltros(List<String> idEmpresas, String nomep, String nomeTerceiro, Date lanci, Date lancf, String horalanci, String horalancf,
                                    Date compi, Date compf, String horacompi, String horacompf, String formasPagamento, String cpf, String matricula,
                                    String nomeClienteContrato, ChequeVO chequeVO, Boolean pesquisarComLote, UsuarioVO nomeOperador,
                                    String codigoAutorizacao, Integer operadoraCartao,
                                    String nsu, boolean chequeAvista, boolean chequeAprazo, boolean considerarDataOriginal,
                                    List<Integer> codigoLote, boolean mostrarCancelados, int nivelMontarDados, Integer codigoCentroCusto, boolean antecipados,
                                    StatusMovimentacaoEnum statusMovimentacao, Integer adquirente, String numeroDocumento) throws Exception;

    public int contarPagamentoAlteracaoManual(Date inicio, Date fim, Integer empresa, List<ColaboradorVO> lista) throws Exception;

    public List<MovPagamentoVO> consultarPorIntervaloDatasEmpresaDataAlteracaoManual(Date inicio, Date fim, int empresa, List<ColaboradorVO> lista, final int nivelMontarDados) throws Exception;

    public LogVO alterarDataPagamentoCartaoDebito(Integer nrDiasCompensacao, UsuarioVO user, EmpresaVO empresa) throws Exception;

    public ReciboPagamentoVO incluirPagamentoParcela(MovParcelaVO parcela, ContratoVO contrato, Date dataCancelamento, UsuarioVO responsavel) throws Exception;

    public void incluirAutorizacao(int movPagamento, String autorizacao, String nsu) throws Exception;

    void alterarAdquirente(Integer adquirente, Integer movPagamento) throws Exception;

    public void incluirAutorizacaoDependentes(int movPagamento, String autorizacao, String nsu) throws Exception;

    public void alterarDataCompensacao(int codigo, Date data) throws Exception;
    public void alterarDataCompensacaoOrigemCredito(int codigo, Date data) throws Exception;

    public Date obterProximoDiaUtil(final Date dataCompensacao,EmpresaVO empresa) throws Exception;

    public void alterarSemCommit(MovPagamentoVO obj) throws Exception;

    public void alterarSemCommit(MovPagamentoVO obj, boolean controlarAcesso) throws Exception;

    public MovPagamentoVO atualizarChequeMovimentoCC(MovPagamentoVO mov) throws Exception;

    public MovPagamentoVO atualizarCartaoMovimentoCC(MovPagamentoVO mov) throws Exception;

    public MovPagamentoVO atualizarListaCartaoCredito(MovPagamentoVO mov) throws Exception;

    public MovPagamentoVO retiraChequesCancelados(MovPagamentoVO mov);

    public MovPagamentoVO atualizarListaCheques(MovPagamentoVO mov) throws Exception;

    public MovPagamentoVO retiraCartoesCancelados(MovPagamentoVO mov);

    public void setarProdutosPagos(int codigoRecibo) throws SQLException;

    public List<ResumoFormaPagamentoRelatorio> consultarLancamentosFechamentoCaixa(Date inicio, Date fim, int empresa) throws Exception;

    public MovPagamentoVO gerarMovpagamentoMovimentoCC(MovPagamentoVO movPagamento) throws Exception;

    public void alterarSomenteEntrouNoCaixa(MovPagamentoVO pagamento, Integer movconta) throws Exception;

    public void alterarPagamentoRetirandoDoCaixa(Integer movconta, Integer qtdDiasCartaoDtPagamento) throws Exception;

    public List<MovPagamentoVO> consultarCreditosDependentes(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List<MovPagamentoVO> consultarPagamentosEvento(Integer eventoInteresse, int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

    public String obterProdutosPagosMovPagamento(Integer movPagamento) throws Exception;

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar) throws Exception ;

    public void validarPermissaoDataCheque(UsuarioVO usuario, List<MovParcelaVO> listaParcelas,
            List<MovPagamentoVO> listaPagamentos) throws Exception;

    public List<MovPagamentoVO> consultarPorDataPagamentoPessoa(Date hoje, int empresa, int nivelMontarDados) throws Exception;

    void gerarMovPagamento(String nomeComprador, PessoaVO pessoaVO, MovParcelaVO parcela) throws Exception;

    public void separarCartoesCancelados(MovPagamentoVO mov);

    public void separarChequesCancelados(MovPagamentoVO mov);

    public List<MovPagamentoVO> consultarTelaCliente(Integer pessoa, Integer contrato, Integer limit,Integer offset) throws Exception;

    public Integer obterCountPagamentosCliente(Integer pessoa,Integer contrato) throws Exception;

    List<ItemFaturamentoTO> consultarItensFaturamento(Date dataInicio, Date dataFim) throws Exception;

    boolean existeNotaEmitidaMovPagamentoOrigem(Integer codigoMovPagamentoOrigem) throws Exception;
    public Integer obterMovContaPagamento(Integer codigoPagamento) throws Exception;

    public Integer incluirPagamentoTotem(List<Integer> parcelas, String tipo, String autorizacao, String totem, String comprovante, Integer codigoEmpresa) throws Exception;

     public List<MovPagamentoVO> consultarPagamentosItemExtrato(ExtratoDiarioItemVO item) throws Exception;

    public List<MovPagamentoVO> consultarSimplificadoCliente(Integer cliente) throws Exception;

    public void ajustarMovContaPagamentoCC(MovPagamentoVO pagamento) throws Exception;

    void gerarPontosParceiroFidelidade(final int codigoRecibo) throws Exception;

    void marcarParceiroFidelidadeProcessado(boolean processado, Integer movPagamento) throws Exception;

    List<MovPagamentoVO> consultarPorMovConta(Integer codigoMovConta) throws Exception;

    List<MovPagamentoVO> consultarPorMovConta(Integer codigoMovConta, int limit, int offset) throws Exception;

    Integer consultarPorMovContaCount(Integer codigoMovConta, int limit, int offset) throws Exception;

    void alterarNSU(String nsu, Integer movPagamento) throws Exception;

    void alterarNSUeCodAutorizacao(String codigoNSU, String autorizacaoCartao, int codMovpagamento) throws Exception;

    void liberarCobrancaBaixaProtheus(String recno) throws Exception;

    Integer incluirBaixaProtheus(String recno, String numerotitulo)throws Exception ;

    List<MovPagamentoVO> consultarSQL(String sql, int nivelMontarDados) throws Exception;

    void marcarEnviadoConciliadora(boolean enviado, Integer movPagamento) throws Exception;

    boolean existeMovPagamentoFormaPagamento(Integer codigoFormaPagamento) throws Exception;

    Integer acaoAtualizarDiasCompensacaoBoleto(Integer diasCompensacaoBoleto,Integer codigoConvenio,boolean atualizar) throws Exception;

    void alterarConvenioCobranca(Integer convenioCobranca, Integer movPagamento) throws Exception;

    ResultSet consultarMovimentacoesRecebiveisIntegracao(Date inicio, Date fim, int empresa, String matricula) throws Exception;

    ReciboPagamentoVO gravarPagamento(PagamentoDTO pagamentoDTO, Boolean pactoApp, Boolean controlarTransacao, boolean pagarVencida) throws Exception;

    void atualizarProdutosPagosMovPagamento(String produtosPagos, Integer codigoMovPagamento) throws Exception;

    List<MovPagamentoVO> consultarPagamentosPixPJBankDataCompensacaoIncorreta(Date inicio, Date fim) throws Exception;

    void corrigirPagamentosPixPJBankDataCompensacaoIncorreta(List<MovPagamentoVO> lista) throws Exception;

    Date consultarDataPagamentoByCodigo(int codMovPagamento) throws Exception;
}
