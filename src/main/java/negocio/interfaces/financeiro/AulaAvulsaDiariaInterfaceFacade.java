package negocio.interfaces.financeiro;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface AulaAvulsaDiariaInterfaceFacade extends SuperInterface {

    AulaAvulsaDiariaVO novo() throws Exception;

    void incluir(AulaAvulsaDiariaVO obj) throws Exception;

    void incluirSemCommit(AulaAvulsaDiariaVO obj) throws Exception;

    void incluirSemCommit(AulaAvulsaDiariaVO obj, OrigemSistemaEnum origem) throws Exception;

    void incluirSemCommit(AulaAvulsaDiariaVO obj, OrigemSistemaEnum origem, Boolean gerarParcela) throws Exception;

    void alterar(AulaAvulsaDiariaVO obj) throws Exception;

    AulaAvulsaDiariaVO consultarPorCliente(Integer cliente, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    void excluirSemCommit(Integer codigo) throws Exception;

    AulaAvulsaDiariaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    String obterContratoProdutoTextoPadraoDiaria(
            Integer aulaAvulsaDiaria, Integer produto) throws Exception;

    Integer vendasOnlineDiariaTotal(Integer empresa, Date dataInicial, Date dataFinal) throws Exception;

    List<ItemRelatorioTO> vendasOnlineDiariaLista(Integer empresa, Date dataInicial, Date dataFinal) throws Exception;
}
