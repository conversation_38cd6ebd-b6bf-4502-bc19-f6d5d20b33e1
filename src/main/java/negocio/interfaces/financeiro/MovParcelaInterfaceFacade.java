package negocio.interfaces.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoConsultaParcelasEnum;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.integracao.pactopay.front.FiltroPactoPayDTO;
import controle.financeiro.MovParcelaTransacaoTO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.interfaces.basico.SuperInterface;
import relatorio.controle.basico.RecorrenciaClienteTO;
import relatorio.negocio.comuns.basico.ResumoPessoaRelBIInadimplenciaVO;
import relatorio.negocio.jdbc.financeiro.ParcelaConsolidadaTO;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface MovParcelaInterfaceFacade extends SuperInterface {

    public MovParcelaVO novo() throws Exception;

    public void incluir(MovParcelaVO obj, boolean controleTransacao) throws Exception;
    void incluir(MovParcelaVO obj) throws Exception;

    public void incluirComProdutosSemCommit(MovParcelaVO obj) throws Exception;

    public void incluirParcelaSemCommit(MovParcelaVO obj) throws Exception;

    public void alterar(MovParcelaVO obj) throws Exception;

    public void alterar(MovParcelaVO obj, boolean atualizarDCC) throws Exception;

    public void renegociarParcelas(List<MovParcelaVO> listaMovParcelaRenegociar,
                                   List<MovParcelaVO> listaMovParcelaRenegociadas,
                                   MovParcelaVO movParcelaDesconto,
                                   MovParcelaVO movParcelaTaxa,
                                   String tipoProdutoExtra,
                                   boolean mapaParcelas,
                                   MovParcelaVO ultimaParcela,
                                   Double valorDesejado,
                                   double porcentagemDescontoDesejado,
                                   boolean temParcelaDeCC,
                                   UsuarioVO usuarioVO,
                                   boolean renegociacaoRedeEmpresa, boolean atualizarValorFaturado,
                                   boolean validarTransacaoPendente, String observacao, OrigemSistemaEnum origemSistemaEnum) throws Exception;


    public void excluir(MovParcelaVO obj) throws Exception;

    public MovParcelaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public MovParcelaVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<MovParcelaVO> consultarPorCodigoCliente(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public void alterarRegimeDCCParcela(int codigo,Boolean parcelaDCC) throws Exception;

    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<MovParcelaVO> consultarPorContrato(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public MovParcelaVO consultarPorNumeroParcelaNoContrato(Integer numeroParcela, Integer codigoContrato, int nivelMontarDados) throws Exception;

    public List<MovParcelaVO> consultarPorContrato(Integer valorConsulta, int nivelMontarDados, String situacao) throws Exception;

    /**
     * Consulta as parcelas em aberto que são do tipo mensalidade de contratos recorrentes
     *
     * @param codigoContrato
     * @param nivelMontarDados
     * @param dataVencimentoAPartir
     * @return
     * @throws Exception
     */
    List<MovParcelaVO> consultarMensalidadesEmAbertoPorContrato(Integer codigoContrato, int nivelMontarDados, Date dataVencimentoAPartir) throws Exception;

    MovParcelaVO consultarProximaEmAbertoPorContrato(Integer codigoContrato, int nivelMontarDados, Date dataVencimentoAPartir) throws Exception;

    List<MovParcelaVO> consultarEmAbertoPorContrato(Integer codigoContrato, int nivelMontarDados) throws Exception;

    public List<MovParcelaVO> consultarPorContratoNaoRenegociadaNegociada(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorSituacao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception;

    public int numeroPendenciasImportadas(boolean pagos, boolean embordero) throws Exception;

    public String pendenciasImportadas(boolean pagos, boolean embordero) throws Exception;

    public List consultarPorNomeCliente(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorDataRegistroVendaAvulsa(String valorConsultar, Integer empresa, Date prmIni, Date prmTerm, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorVendaAvulsa(String valorConsultar, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public MovParcelaVO consultarPorCodigoVendaAvulsa(Integer codigoVenda,
                                                      String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDataRegistroAulaAvulsaDiaria(String valorConsultar, Integer empresa, Date prmIni, Date prmTerm, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorAulaAvulsaDiaria(String valorConsultar, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public MovParcelaVO consultarPorCodigoAulaAvulsaDiaria(Integer codigoVenda, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<MovParcelaVO> consultarPorCodigoRecibo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void alterarSomenteSituacaoSemCommit(MovParcelaVO obj) throws Exception;

    List consultarParcelas(Integer contrato, Date dataVencimento, int nivelMontarDados) throws Exception;

    public List<MovParcelaVO> consultarParcelas(Integer contrato, Date dataInicioVencimento, Date dataVencimento,  int nivelMontarDados) throws Exception;

    public List consultarPorMovProduto(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorMovProduto(Integer valorConsulta, String situacao, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List<MovParcelaVO> consultarPorCodigoVendaAvulsaLista(Integer codigoVenda, Date dataInicioVencimento,
                                                                 Date dataVencimento, String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception ;
    public void excluirSemCommit(MovParcelaVO obj) throws Exception;

    public void excluirSemCommit(Integer codigo) throws Exception;

    public void cancelarParcelasArmarioEmAberto(List<MovParcelaVO> parcelasIgnorar,int empresa,int vendaAvulsa) throws Exception;

    public void validarMovParcelaPagar(MovParcelaVO obj) throws  Exception;

    public boolean existeParcelaVencidaSegundoConfiguracoes(
            Integer codigoPessoaContrato, EmpresaVO empresa,
            ConfiguracaoSistemaVO configuracaoSistema,
            int nivelMontarDados) throws Exception;

    public boolean existeParcelaVencidaSegundoConfiguracoes(
            Integer codigoPessoa, Integer codigoContrato, EmpresaVO empresa,
            ConfiguracaoSistemaVO configuracaoSistema,
            int nivelMontarDados, Integer codigoControlePersonal) throws Exception;


    /**
     * Responsável por realizar uma consulta de <code>MovParcela</code> através
     * do valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovParcelaVO</code>
     *         resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<MovParcelaVO> consultarPorCodigoPessoa(Integer valorConsulta, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>MovParcela</code> através
     * do valor dos atributos <code>situacao</code>, <code>regimerecorrencia</code> e
     * <code>pessoa</code> da classe <code>MovParcela</code>. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovParcelaVO</code>
     *         resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public boolean existeParcelaEmAbertoNaoRecorrenciaPorCodigoPessoa(Integer valorConsulta) throws Exception;

    /**
     * Verifica se o cliente possui alguma {@link MovParcelaVO} com a situação definida.
     * @param cliente Cliente que pertence a parcela.
     * @param empresa Código da {@link EmpresaVO} que pertence a {@link MovParcela}
     * @return true caso possua alguma {@link MovParcelaVO} com o status definido, false caso contrario.
     * @throws Exception
     */
    public boolean existeParcelaEmSituacao(Integer cliente, String situacao, Integer empresa) throws  Exception;

    public boolean existeParcelaVencidaEmSituacao(Integer cliente, String situacao, Integer empresa) throws  Exception;

    public List consultarPorCodigoPessoaContrato(Integer valorConsulta, int contrato, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>MovParcelaVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação <code>alterar</code> da
     * superclasse.
     *
     * @param obj Objeto da classe <code>MovParcelaVO</code> que será alterada
     *            no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     *                   validação de dados.
     */
    public void alterarSemCommit(MovParcelaVO obj) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>MovParcela</code> através
     * do valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovParcelaVO</code>
     *         resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    List<MovParcelaVO> consultarParcelaSomentePlanoMensalOrMatriculaOrRenovacaoOrRematricaPorContrato(Integer valorConsulta, boolean incluirParcelaAnuidade, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>MovParcelaVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação <code>alterar</code> da
     * superclasse.
     *
     * @param obj Objeto da classe <code>MovParcelaVO</code> que será alterada
     *            no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     *                   validação de dados.
     */
    public void alterarSemCommit(MovParcelaVO obj, ReciboPagamentoVO reciboPagamentoVO) throws Exception;

    /**
     * @param empresa               0 para consultar de todas as empresas
     */
    ResultSet consultarPendenciaParcelaEmAbertoAPagar(int empresa, final String colaboradores,ConfPaginacao paginacao,Date dataBase) throws Exception;

    /**
     * @param empresa   0 para consultar de todas as empresas
     */
    ResultSet consultarPendenciaParcelaEmAbertoAtraso(int empresa, Date data, final String colaboradores,ConfPaginacao paginacao,Date dataBaseInicio, Date dataBase) throws Exception;

    public ResultSet contarPendenciaParcelaEmAbertoAPagar(int empresa,Date dataBase, final String colaboradores,Boolean remgimeRecorrencia,Boolean parcelaDCC, Boolean contarCliente, List<Integer> convenios) throws Exception;

    /**
     * @param empresa 0 para consultar de todas as empresas
     */
    ResultSet contarPendenciaParcelaEmAbertoAPagar(int empresa,Date dataBaseInicio, Date dataBaseFim, final String colaboradores,Boolean remgimeRecorrencia,Boolean parcelaDCC, Boolean contarCliente, List<Integer> convenios) throws Exception;

    public ResultSet contarPendenciaParcelaEmAbertoAtraso(int empresa, Date data, final String colaboradores) throws Exception;

    /**
     * @param empresa           0 para consultar de todas as empresas
     */
    ResultSet contarPendenciaParcelaEmAbertoAtraso(int empresa, Date dataInicial, Date data, final String colaboradores) throws Exception;

    public void incluirParcelaSemValidar(MovParcelaVO obj) throws Exception;

    public List consultarPorContratoEvento(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public List consultarPorNomeConsumidor(String nomeComprador, int nivelMontarDados) throws Exception;

    public MovParcelaVO consultarPorMovProduto(int movproduto, int nivelMontarDados) throws Exception;

    public List<MovParcelaVO> consultarPorListMovProduto(Set<Integer> movproduto, int nivelMontarDados) throws Exception;

    public List consultarParcelasColaboradorPorCodigoPessoa(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPaginado(MovParcelaFiltroVO filtro, ConfPaginacao confPaginacao) throws Exception;

    public List<MovParcelaVO> consultarPorMesReferenciaPersonal(int empresa, int personal, String mesReferencia, int nivelMontarDados) throws Exception;

    public List<MovParcelaVO> consultarParcelasEmAbertoPorPersonal(int empresa, int personal, int nivelMontarDados) throws Exception;

    public List consultarPorNegociacaoPersonal(String valorConsulta, String situacao, int empresa, int nivelMontarDados) throws Exception;

    public List consultarPorDataRegistroNegociacaoPersonal(String valorConsulta, String situacao, int empresa, Date dataInicio, Date dataTermino, int nivelMontarDados) throws Exception;

    public ResultSet contarPendenciaParcelaEmAbertoAPagarColaborador(int empresa,  final String colaboradores) throws Exception;

    /**
     *
     * @param empresa          0 para consultar de todas as empresas
     * @param colaboradores
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
    ResultSet contarPendenciaParcelaEmAbertoAPagarColaborador(int empresa, final String colaboradores, Date dataBaseInicio) throws Exception;

    /**
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param colaboradores
     * @param paginacao
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
    ResultSet consultarPendenciaParcelaEmAbertoAPagarColaborador(int empresa, final String colaboradores,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception;

    public List<MovParcelaVO> consultarParcelasRecorrencia(Date dataVencimento) throws Exception;

    List<MovParcelaVO> consultarParcelasParaCancelamentoAutomatico(Date dataVencimento) throws Exception;

    public MovParcelaVO consultarProximaParcelaContrato(Integer contrato, Integer nivelMontarDados) throws Exception;

    /**
     * Recebe uma lista de Parcelas com suas datas de vencimento alteradas e tenta efetuar as alterações.
     * Pré-requisitos:
     * 1. Todas as parcelas devem estar com situação "Em aberto";
     * 2. Não permite que a nova data de vencimento seja menor ou igual a data atual, deve ser no mínimo amanhã;
     *
     * @throws Exception
     */
    void alterarVencimentoListaParcelas(List<MovParcelaVO> listaModificada, List<MovParcelaVO> listaOriginal, boolean remessas,
                                        String usuarioLogado, String origemAlteracao, boolean limparNrTentativas) throws Exception;

    void alterarVencimentoListaParcelas(List<MovParcelaVO> listaModificada, List<MovParcelaVO> listaOriginal, boolean remessas,
                                        String usuarioLogado, String origemAlteracao, boolean limparNrTentativas, boolean permitirAlterarAnteriorDataAtual) throws Exception;

    void alterarVencimentoListaParcelas(List<MovParcelaVO> listaModificada, List<MovParcelaVO> listaOriginal, boolean remessas,
                                        boolean permitirAlterarAnteriorDataAtual, String usuarioLogado, String origemAlteracao, boolean limparNrTentativas) throws Exception;

    public boolean consultarPorDescricao(String valorConsulta, int nivelMontarDados) throws Exception;

    public List<MovParcelaVO> consultarParcelasRecorrenciaParaRepescagem(Date dataVencimento,Date dataLimite) throws Exception;

    List<CaixaAbertoTO> consultaCaixaEmAberto(String matriculaCliente, String nomeCliente, Integer empresa, Date dataInicio, Date dataFim, ConfPaginacao confPaginacao, boolean recorrentes, boolean ordenarPorDataLancamento, List<Integer> parcelasEscolhidas, boolean naoApresentarVencimentosFuturos) throws Exception;

    List<CaixaAbertoTO> consultaCaixaEmAberto(String matriculaCliente, String nomeCliente, String descricaoProduto, Integer empresa, Date dataInicio, Date dataFim, ConfPaginacao confPaginacao, boolean recorrentes, boolean ordenarPorDataLancamento, List<Integer> parcelasEscolhidas, boolean naoApresentarVencimentosFuturos, Integer paginaInicialExterna) throws Exception;

    public MovParcelaVO gerarParcelaQuitacaoCancelamento(Double valorParcela, MovProdutoVO produtoQuitacao, UsuarioVO responsavel, Date dataCancelamento, ContratoVO contrato);

    public List<String> consultaTiposProdutosMovParcela(final int codMovParcela) throws Exception;

    void incrementarNrTentativasParcelaConvenio(RemessaItemVO remessaItemVO, ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    void incrementarNrTentativasParcelaConvenio(MovParcelaVO movParcela, ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    void incrementarNrTentativasProcessoRetentativa(int codMovParcela) throws Exception;

    public List<MovParcelaVO> consultarParcelasParaRemessaRepescagem(ConvenioCobrancaVO convenio, final Date inicio, final Date fim,  final Date inicioCobranca, final Date fimCobranca, Integer codigoEmpresa, Integer plano) throws Exception;

    public List<MovParcelaVO> consultarParcelasComAutorizacaoCobranca(ConvenioCobrancaVO convenio, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca, Integer codigoEmpresa, Integer plano) throws Exception;

    public List<MovParcelaVO> consultarParcelasEventoPorRecibo(Integer codigoRecibo) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

    public Double consultarPorAluno(Integer codPessoa) throws Exception;

    public List<MovParcelaVO> consultarPorAluno(Integer codPessoa,int nivelMontarDados) throws Exception;

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar) throws Exception ;

    public MovParcelaVO consultarPorNumeroContrato(Integer numero, Integer contrato) throws Exception;

    List<MovParcelaVO> consultarPorCodigoAulaAvulsaDiariaLista(Integer codigoVenda,final String situacao, boolean controlarAcesso, Date dataInicioVencimento,
                                                               Date dataVencimento, int nivelMontarDados) throws Exception;
    
    public MovParcelaVO consultarParcelaEmAbertoMaisAntiga(Integer codigoPessoa) throws Exception;

    boolean existeParcelaEmAbertaNoMesAnterior(Integer codigoPessoa) throws Exception;

    public List<MovParcelaVO> consultarParcelasDCC(ConvenioCobrancaVO convenio, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                                   List<Integer> empresas, Integer plano, Boolean repescagem, boolean somenteParcelaRemessa, boolean somenteParcelasSemBoletoGerado,
                                                   Integer modalidade, boolean consultarRemessa, boolean apresentarAlunosBloqueioCobranca, PaginadorDTO paginadorDTO) throws Exception;

    public List<MovParcelaVO> consultarParcelasDCC(List<Integer> convenios, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                                   List<Integer> empresas, Integer plano, Boolean repescagem,boolean somenteParcelaRemessa, boolean somenteParcelasSemBoletoGerado,
                                                   Integer modalidade, boolean consultarRemessa, boolean apresentarAlunosBloqueioCobranca) throws Exception;

    public List<MovParcelaVO> consultarParcelasDCCResumido(List<Integer> convenios, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                                           List<Integer> empresas, Integer plano, Boolean repescagem, boolean somenteParcelaRemessa, boolean somenteParcelasSemBoletoGerado,
                                                           Integer modalidade, boolean consultarRemessa, boolean apresentarAlunosBloqueioCobranca) throws Exception;

    public List<MovParcelaVO> consultarParcelasDCCBIParcelasEmAberto(ConvenioCobrancaVO convenio, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                                                     List<Integer> empresas, Integer plano, Boolean repescagem, boolean somenteParcelaRemessa, boolean somenteParcelasSemBoletoGerado,
                                                                     Integer modalidade, boolean consultarRemessa, boolean apresentarAlunosBloqueioCobranca) throws Exception;

    public List<MovParcelaTransacaoTO> consultarParcelasDCCBIParcelasEmAbertoResumido(List<Integer> convenios, List<Integer> colaboradores, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                                                                      List<Integer> empresas, Integer plano, Boolean repescagem, boolean somenteParcelaRemessa,
                                                                                      boolean somenteParcelasSemBoletoGerado, Integer modalidade, boolean consultarRemessa, boolean apresentarAlunosBloqueioCobranca) throws Exception;

    public ResultSet consultarParcelasDCC(Integer codigoAutorizacao,int clienteAutorizacao) throws Exception;

    Integer consultarParcelasDCCCount(ConvenioCobrancaVO convenio, final Date inicio, final Date fim, final Date inicioCobranca,
                                      final Date fimCobranca, Integer codigoEmpresa, Integer plano, boolean somenteParcelasSemBoletoGerado,
                                      Integer modalidade, boolean apresentarAlunosBloqueioCobranca) throws Exception;

    Integer consultarParcelasDCCCount(Integer codigoEmpresa, final Date fim, List<Integer> convenios, List<Integer> colaboradores) throws Exception;

    Double consultarParcelasDCCValor(ConvenioCobrancaVO convenio, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                     Integer codigoEmpresa, Integer plano, boolean somenteParcelasSemBoletoGerado, Integer modalidade, boolean apresentarAlunosBloqueioCobranca) throws Exception;

    Double consultarParcelasDCCValor(Integer codigoEmpresa, final Date fim, List<Integer> convenios, List<Integer> colaboradores) throws Exception;

    public List<MovParcelaVO> consultarPorContratoParcelasEmRemessa(String contrato) throws Exception;

    public Integer consultarParcelasVencidasContratosRecorrencia(Date dataVencimento, int contrato) throws Exception;

    public Boolean consultarParcelasRemessasComBoletosItauDeContrato(int contrato) throws Exception;

    public boolean parcelaEmAberto(Integer codigoPessoa, Date mesVencimentoAte)throws Exception;

    public List<MovParcelaVO> consultarPorCodigoPessoaSituacao(Integer valorConsulta, String situacao, Integer contrato, int nivelMontarDados, boolean ignorarEmRemessa, Integer limit) throws Exception;

    public List<MovParcelaVO> consultarParcelasRecorrenciaParaCancelamentoAutomatico(Date dataVencimento) throws Exception;

    void cancelarProdutosVinculados(List<MovParcelaVO> parcelasParaCancelar) throws Exception;

    List<MovParcelaVO> criarParcelaMultaJuros(List<MovParcelaVO> listaParcelasPagar, EmpresaVO empresaVO, UsuarioVO usuarioVO, Date dataPagamento, Double multiplicadorMultaJuros, Double valorMoraBanco, boolean controlarTransacao) throws Exception;

    Double montarMultaJurosParcelaVencida(EmpresaVO empresaVO, List<MovParcelaVO> movParcelas, Date dataPagamento, boolean forcarCalculo, Double multiplicadorMultaJuros, Double valorMoraBanco) throws Exception;

    void cancelarMultaJurosPelaParcelaOriginal(Integer codMovParcela) throws Exception;

    void atualizarParcelaDCC(Integer movparcela, Integer pessoa, Integer codigoCliente, Integer codigoColaborador) throws  Exception;

    List<MovParcelaVO> consultarPorCodigoPersonalLista(Integer codigoPersonal,final String situacao, boolean controlarAcesso, Date dataInicioVencimento,
                                                               Date dataVencimento, int nivelMontarDados) throws Exception;

    public boolean existeParcelaContratoEmRemessaGeradaOuAguardando(Integer contrato, List<Integer> tiposConveniosIgnorar) throws Exception;

    List<MovParcelaVO> consultarPorCodigoPessoaDiasEmAberto(Integer codigoPessoa, MalaDiretaVO malaDiretaVO, int nivelMontarDados) throws Exception;
    
    public List<MovParcelaVO> consultarTelaCliente(Integer pessoa, Integer contrato, Integer limit,Integer offset, String orderBy, boolean orderByDesc) throws Exception;

    
    boolean deveGerarMulta(Date dataPagamento, Date dataVencimento, EmpresaVO empresaVO) throws Exception;

    public MovParcelaVO consultarPorMovPagamentoContaCorrente(String movpagamento, int nivelMontarDados) throws Exception;

    /**
     * Valida se uma determinada {@link MovParcelaVO} está bloqueada aguardando retorno do retorno.
     * @param movParcela {@link MovParcelaVO} que será analisada
     * @return true se a parcela tiver remessa que a bloqueie.
     * @throws Exception
     */
    Boolean parcelaEstaBloqueadaPorCobranca(MovParcelaVO movParcela) throws  Exception;

    /**
     * Valida se uma determinada {@link MovParcelaVO} está em boleto pendente.
     * @param movParcela {@link MovParcelaVO} que será analisada
     * @param desconsiderarPagos se true, boletos pagos serão desconsiderados na verificação de bloqueio
     * @return true se a parcela tiver em boleto pendente.
     * @throws Exception
     */
    Boolean parcelaEstaBloqueadaPorBoletoPendente(MovParcelaVO movParcela, boolean desconsiderarPagos) throws  Exception;

    /**
     * Realiza a consulta dos dados a serem utilizados no BI de inadimplência.
     * @param meses
     * @return
     * @throws Exception
     */
    List<BIInadimplenciaTO> consultarBIInadimplenciaTO(Integer meses, Integer empresa, Date dataBase, List<Integer> codigoConvenios, Boolean incluirContratosCancelados) throws Exception;
    /**
     * Realiza a consulta das parcelas em aberto caso o cliente possua o tipo de convenio informado.
     * @param tipoConvenio
     * @param diasRepescagem
     * @param diasMaximoVencimento
     * @return
     * @throws Exception
     */
    List<MovParcelaVO> consultarParcelasEmAbertoParaPagamento(ConvenioCobrancaVO convenioCobrancaVO, Date dia, int nivelMontarDados) throws Exception;

    public Date consultarUltimoVencimentoContrato(int contrato) throws Exception;

    public Integer obterCountParcelaCliente(Integer pessoa,Integer contrato) throws Exception;

    public Integer obterTotalParcelaCliente(Integer pessoa,Integer contrato) throws Exception;
    
    public boolean verificarExisteParcelaContratoMes(Integer contrato, Date dia) throws Exception;

    public boolean existeParcelaEmSituacaoPorContrato(Integer contrato, String situacao) throws  Exception;

    public List<MovParcelaVO> consultarParcelasVencidasPessoa(Integer pessoa, int nivelMontarDados) throws  Exception;

    public List<MovParcelaVO> consultarParcelasVencidasOuUltimaRenovacaoPessoa(Integer pessoa, int nivelMontarDados) throws  Exception;

    List<MovParcelaVO> consultarParcelasVencidasPorMatricula(String matricula, int nivelMontarDados) throws  Exception;

    List<MovParcelaVO> consultarParcelasPorMatriculaOuCpf(String matriculaCpf, int nivelMontarDados, boolean somenteVencidas) throws Exception;

    public List<MovParcelaVO>consultarPrimeirasParcelasContrato(Integer contrato, int limit) throws Exception;

    public List<MovParcelaVO> consultarParcelasContratoAlterarData(Integer contrato) throws Exception;

    public boolean parcelaEmAberto(MovParcelaVO parcela) throws Exception;

    List<MovParcelaVO> consultarPrimeirasParcelasContrato(Integer contrato, boolean forcarAdesao) throws Exception;

    public List<MovParcelaVO> consultarApenasDescricaoPorTransacao(Integer transacao) throws Exception;

    void totalizadorBIInadimplencia(InadimplenciaTO totalizador, Integer codigoEmpresa,
                                    Date dataInicio, Date dataFim, String situacaoParcela, Date dataMatricula, Integer diaPagamento) throws Exception;

    String listaCodigosParcelaBIInadimplenciaDetalhado(Integer codigoEmpresa,
                                                       Date dataInicio, Date dataFim, String situacaoParcela,
                                                       Date dataMatricula, Integer diaPagamento) throws Exception;

    List<ResumoPessoaRelBIInadimplenciaVO> listaBIInadimplencia(Integer codigoEmpresa,
                                                                Date dataInicio, Date dataFim, String situacaoParcela,
                                                                Date dataMatricula, Integer diaPagamento, Boolean distinctAlunos) throws Exception;
    
    public boolean parcelaPagaChequeDevolvido(final Integer codigoParcela) throws Exception;
    
    public void validarMovParcelaComTransacaoConcluidaOuPendente(final Integer codigoParcela) throws  Exception;

    public boolean temParcelasAberto(Integer cliente) throws Exception;
    
    public boolean validarSituacaoParcela(Integer codigoParcela, String situacao) throws  Exception;

    public RecorrenciaClienteTO parcelaRecorrencia(MovParcelaVO parcela, boolean consultarFormaPagamento) throws Exception;

    public void preencherCamposEspeciais(List<MovParcelaVO> parcelas) throws Exception;

    MovParcelaVO criarParcelaMultaJuros(MovParcelaVO parcelaParaPagar, Double valorMulta, Double valorJuros, UsuarioVO usuarioVO) throws Exception;

    void montarMultaJurosParcelaVencida(EmpresaVO empresaVO, TipoCobrancaEnum tipoCobrancaEnum,
                                        List<MovParcelaVO> listaParcelas, Date dataPagamento) throws Exception;

    void montarInformacoesExtratoAluno(List<MovParcelaVO> lista, String orderBy, boolean orderByDesc) throws Exception;
    
    List<MovParcelaVO> consultarParcelasEmAbertoPessoa(Integer pessoa, int nivelMontarDados) throws  Exception;

    List<MovParcelaVO> consultarParcelasFuturasEmAberto(Integer codigoContrato, int nivelMontarDados) throws Exception;

    /**
     * Calcula valor de pro rata de uma parcela com base em uma nova data de vencimento da parcela
     *
     * @param dataVencimentoOriginalParcela
     * @param novaDataVencimentoParcela
     * @param valorMensalParcela
     * @param usuarioResponsavel
     * @param empresa
     * @param pessoa
     * @param contrato
     * @return
     * @throws Exception
     */
    MovParcelaVO calcularParcelaProRata(Date dataVencimentoOriginalParcela,
                                        Date novaDataVencimentoParcela,
                                        Double valorMensalParcela,
                                        UsuarioVO usuarioResponsavel,
                                        EmpresaVO empresa,
                                        PessoaVO pessoa,
                                        ContratoVO contrato) throws Exception;

    /**
     * Obtem parcelas em aberto que foram geradas a partir de uma venda de plano personal recorrente
     * Veja o ticket #12312 (assembla) para mais detalhes sobre esse recurso
     */
    List<MovParcelaVO> consultarParcelasPlanoPersonalAVencerParaCancelamento(int controleTaxaPersonal, int nivelMontarDados) throws Exception;

    MovParcelaVO consultarProximaParcelaAnuidadeEmAberto(ContratoVO contrato) throws Exception;

    MovParcelaVO consultarUltimaParcelaAnuidadePaga(List<ContratoVO> contratosTransferencia) throws Exception;

    Boolean verificarParcelaVencidaPorPessoa(Integer pessoa, Date data) throws Exception;

    public List<MovParcelaVO> consultar(String sql, final int nivelMontarDados) throws SQLException, Exception;

    void incluirListaMovParcelas(List<MovParcelaVO> lista) throws Exception;

    Integer consultarParcelasVencidasContrato(Date dataVencimento, int contrato) throws  Exception;

    public List<MovParcelaVO> consultarParcelasPagar(ContratoVO contrato, int nivelMontarDados) throws  Exception;

    public Double consultarValorPagarContrato(ContratoVO contratoVO) throws Exception;

    public List<MovParcelaVO> consultarParcelasPagarContrato(ContratoVO contratoVO) throws Exception;
    public Date consultarDataPrimeiraParcelaASerPaga(ContratoVO contratoVO) throws Exception;
    public List<MovParcelaVO> consultarParcelasFuturasPMContrato(final Integer contrato, final int nivelMontarDados) throws SQLException, Exception;

    List<MovParcelaVO> consultarParcelasVencidasPessoaVendasOnline(Integer pessoa, Integer empresa, int nivelMontarDados) throws Exception;

    List<MovParcelaVO> consultarParcelasCanceladas(ContratoVO contratoVO, Date dataCancelamento) throws Exception;

    public void alterarSituacaoPorCodigoPessoa(Integer codigoPessoa) throws Exception;

    List<MovParcelaVO> consultarPorMatricula(String valorConsulta, int nivelMontarDados) throws Exception;

    List<InadimplenteTO> buscarParcelasVencidasPorQtdDias(Integer diasVencido, PaginadorDTO paginadorDTO) throws Exception;

    List<MovParcelaVO> consultarParcelasEmAbertoPactoPay(ConvenioCobrancaVO convenioCobrancaVO, TipoConsultaParcelasEnum tipoConsultaParcelasEnum,
                                                         Date inicio, Date fim, PaginadorDTO paginadorDTO, FiltroPactoPayDTO filtroDTO,
                                                         Integer codigoPessoa) throws Exception;

    boolean parcelaExiste(Integer codigoParcela) throws Exception;

    List<ParcelaConsolidadaTO> consultarConsolidadoParcelas(Date mesConsulta, boolean isPossuiIdExterno) throws SQLException;

    boolean existeColunasCampoExterno() throws SQLException;

    void alterarSituacaoSPC(Integer codigo, Boolean estaNoSPC, String retornoSPC) throws Exception;

    void salvarJSONEnvio(Integer codigo, String jsonEnvio) throws Exception;

    void alterarSituacaoSPCCliente(Integer codigoPessoa) throws Exception;

    List<MovParcelaVO> consultarPorPessoaAndIncluidaSPC(Integer codigoPessoa, boolean incluidaSPC) throws Exception;

    List<MovParcelaVO> findByCPFAndVencimentoAndValor(String cpf, Date dataVencimento, BigDecimal valorDebito, int nivelMontarDados) throws Exception;

    Integer consultarPessoaDaMovParcela(int idMovParcela) throws Exception;

    Integer encontrarEntidadeZWPorMovParcelaParaCappta(String coluna, Integer codigoMovParcela) throws  Exception;

    List<MovParcelaVO> consultarPorContratoAndIncluidaSPC(Integer codigoPessoa, boolean incluidaSPC) throws Exception;

    List<MovParcelaVO> consultarParcelasEmAbertoParaGerarBoletosOnline(ConvenioCobrancaVO convenioCobrancaVO, Date dataInicio, Date dataFim, EmpresaVO empresaVO) throws Exception;

    void mudarSituacaoGerandoBoletoGestaoBoletosOnline(boolean situacao, List<MovParcelaVO> movParcelas) throws Exception;

    void alterarSomenteVencimentoUltimaParcelaContratoSemCommit(Integer contrato, Date novoVencimento) throws Exception;

}
