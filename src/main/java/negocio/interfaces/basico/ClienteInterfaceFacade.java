package negocio.interfaces.basico;

import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.integracao.amigoFit.ClienteAmigoFitJSON;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONArray;
import org.json.JSONException;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.ModalidadeVO;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import relatorio.negocio.comuns.basico.ResumoPessoaBIAcessoGymPassVO;
import relatorio.negocio.comuns.financeiro.ClientesICV;
import relatorio.negocio.comuns.financeiro.DescontoBoletoTO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import servicos.vendasonline.dto.NowLocationIpVendaDTO;
import servlet.cliente.ClienteADJSON;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Interface responsável por criar uma estrutura padrão de comunicação entre a camada de controle
 * e camada de negócio (em especial com a classe Facade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Facade (responsável por persistir os dados das classes VO).
 */
public interface ClienteInterfaceFacade extends SuperInterface {

     ClienteVO novo() throws Exception;

     void incluir(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema) throws Exception;

     void incluir(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema, boolean ce, boolean validardados) throws Exception;

     void incluir(ClienteVO obj, QuestionarioClienteVO quest, ConfiguracaoSistemaVO configuracaoSistema, Boolean validarQuestionario) throws Exception;

     void incluirClienteSemIncluirPessoa(ClienteVO clienteVO, QuestionarioClienteVO quest, ConfiguracaoSistemaVO configuracaoSistema, Boolean validarQuestionario, boolean controleTransacao) throws Exception;

     void incluirSemPessoa(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema) throws Exception;

    public void incluirSemPessoaSemCommit(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema) throws Exception ;

     void incluirAlterandoPessoa(ClienteVO obj, QuestionarioClienteVO quest, ConfiguracaoSistemaVO configuracaoSistema, Boolean validarQuestionario) throws Exception;

     PeriodoAcessoClienteVO incluirFreePass(ClienteVO obj, Date dataInicio, String tokenGymPass, String tipoGymPass, String tokenGoGood) throws Exception;

    void incluirFreePassTotalPass(ClienteVO obj,Date dataInicio,  Boolean tipoTotalPass) throws Exception;

     boolean tentouCheckin(Date dia, String tokenGymPass) throws Exception;

     void alterarDadoFreePass(ClienteVO obj) throws Exception;

     void alterar(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema) throws Exception;

     void alterar(ClienteVO obj, QuestionarioClienteVO quest, ConfiguracaoSistemaVO configuracaoSistema, Boolean validarQuestionario) throws Exception;

     void alterarClienteIncluirQuestionario(ClienteVO obj, QuestionarioClienteVO quest, Boolean validarQuestionario, ConfiguracaoSistemaVO configuracaoSistema) throws Exception;

     void alterarGymPassUniqueToken(ClienteVO clienteVO, UsuarioVO usuarioVO) throws Exception;

    void alterarGoGoodToken(ClienteVO clienteVO, UsuarioVO usuarioVO) throws Exception;

    void validarTokenGymPass(String chave, ClienteVO clienteVO, UsuarioVO usuarioVO, EmpresaVO empresaVO, boolean alterarDados) throws Exception;

    void validarTokenGoGood(String chave, ClienteVO clienteVO, UsuarioVO usuarioVO,
                                   EmpresaVO empresaVO, boolean alterarDados) throws Exception;

    String nomeMatricuaClienteGymPassUniqueToken(String gympassUniqueToken) throws SQLException;

    String nomeMatricuaClienteGoGoodToken(String gogoodtoken) throws SQLException;

    public boolean alterarClienteSituacao() throws Exception;

    void validarInformacoesTokenGymPass(ClienteVO clienteVO, EmpresaVO empresaVO) throws Exception;

     void excluir(ClienteVO obj) throws Exception;

     void excluirSemPessoa(ClienteVO obj) throws Exception;

     void alterarParqCliente(ClienteVO obj) throws Exception;

     void alterarSituacaoClienteSemPessoaCommit(ClienteVO obj) throws Exception;

     ClienteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

     List consultarPorDescricaoProfissao(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

     List consultarPorDescricaoProfissao(String valorConsulta, Integer empresa,
                                               Integer categoria, String situacao,
                                               Date dataInicialCadastroCliente, Date dataFinalCadastroCliente, Integer evento, int codigoModalidade, int nMesesDuracaoPlano,
                                               int codigoColaboradorVinculado, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List<ClienteVO> consultarPorGrupo(String valorConsulta, Integer empresa, Integer categoria, String situacao,
                                             Date dataInicialCadastroCliente, Date dataFinalCadastroCliente, Integer evento, int codigoColaboradorVinculado,
                                             int codigoModalidade, int nNumeroMesesDuracaoPlano, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List<ClienteVO> consultarPorClassificacao(String valorConsulta, Integer empresa, Integer categoria, String situacao,
                                                     Date dataInicialCadastroCliente, Date dataFinalCadastroCliente, Integer evento, int codigoColaboradorVinculado,
                                                     int codigoModalidade, int nNumeroMesesDuracaoPlano, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorDescricaoGrauInstrucao(String valorConsulta, Integer empresa, Integer categoria, String situacao,
                                                   Date dataInicial, Date dataFinal, int evento, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorCfp(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorCfp(String valorConsulta, Integer empresa, Integer codCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<ClienteVO> consultarPorNomeCPF(String nome, String cpf, Integer empresa, int nivelMontarDados) throws Exception;

    List<ClienteVO> consultarPorCPF(String cpf, Integer empresa, int nivelMontarDados) throws Exception;

     List consultarPorCfp(String valorConsulta, Integer empresa, Integer categoria, String situacao,
                                Date dataInicialCadastroCliente, Date dataFinalCadastroCliente, Integer evento, int codigoColaboradorVinculado,
                                int codigoModalidade, int nMesesDuracaoPlano, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     ClienteVO consultarPorCfp(String valorConsulta, int nivelMontarDados) throws Exception;

     ClienteVO consultarPorCodigoPessoa(Integer codigoPrm, Integer empresa, int nivelMontarDados) throws Exception;

     List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     ClienteVO consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorCodigo(Integer valorConsulta, Integer empresa, Integer codCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorCodigo(Integer valorConsulta, Integer empresa, Integer categoria, String situacao,
                                   Date dataInicialCadastroCliente, Date dataFinalCadastroCliente, Integer evento,
                                   int codigoColaboradorVinculado, int codigoModalidade, int nMesesDuracaoPlano,
                                   boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorSituacaoPeriodo(String valorConsulta, Integer empresa, Date inicio, Date fim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception;

     List consultarPorNomeEmpresa(String valorConsulta, Integer categoria, String situacao,
                                        Date dataInicialCadastroCliente, Date dataFinalCadastroCliente, Integer evento, int codigoColaboradorVinculado,
                                        int codigoModalidade, int nMesesDuracaoPlano, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List<ClienteVO> consultarPorNomePessoa(String valorConsulta, Integer empresa, int nivelMontarDados, Integer maxRecords) throws Exception;

     List<ClienteVO> consultarPorNomePessoa(String valorConsulta, Integer empresa, int nivelMontarDados, Integer maxRecords, String situacao) throws Exception;

    List<ClienteVO> consultarPorNomePessoaSituacaoDiferente(String nome, String situacaoDiferente, Integer empresa, int nivelMontarDados, Integer maxRecords) throws Exception;

    List<ClienteVO> consultarTodos(int nivelMontarDados) throws Exception;

     List consultarPorNomePessoa(String valorConsulta, Integer empresa, Integer categoria,
                                       String situacao, Date dataInicialCadastroCliente, Date dataFinalCadastroCliente, Integer evento,
                                       int codigoColaboradorVinculado, int nMesesDuracaoPlano, int codigoModalidade,
                                       boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<ClienteVO> consultarPorNomePessoaVisitanteEDesistente(String valorConsulta, Integer empresa, int nivelMontarDados, Integer maxRecords) throws Exception;

     List consultarPorSituacao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorSituacao(String valorConsulta, Integer empresa, Integer codCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<ClienteVO> consultarPontuacaoSituacao(Integer empresa, int nivelMontarDados,boolean somenteInativos) throws Exception;

    List<ClienteVO> consultarPorMatricula(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorMatriculaComLimite(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List<ClienteVO> consultarPorMatricula(String valorConsulta, Integer empresa, Integer codCliente, String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List<ClienteVO> consultarPorMatriculaOuCPF(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<ClienteVO> consultarPorEmailOuCPF(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorMatricula(String valorConsulta, Integer empresa, Integer categoria,
                                      String situacao, Date dataInicialCadastroCliente, Date dataFinalCadastroCliente, Integer evento,
                                      int codigoModalidade, int nMesesDuracaoPlano, int codigoColaboradorVinculado,
                                      boolean controlarAcesso, int nivelMontarDados) throws Exception;

     ClienteVO consultarPorMatricula(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorNomeCategoria(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

     List consultarPorNomeCategoria(String valorConsulta, Integer empresa, Integer codCliente, int nivelMontarDados) throws Exception;

     List consultarPorCodAcesso(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     ClienteVO consultarPorCodAcesso(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorCodAlternativo(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     ClienteVO consultarPorCodAlternativo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorBanco(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorAgencia(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorConta(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorValorConsultaParametrizada(Integer empresa, String valorConsultaParametrizada, int nivelMontarDados) throws Exception;

     void alterarSituacaoClienteQueComecamHojeSemCommit(Date data, Integer empresa, Integer cliente) throws Exception;

     void alterarSituacaoClienteQueContratoOperacaoComecamHojeSemCommit(String situacao, Integer contrato, Integer empresa) throws Exception;

     List consultarClientePorSituacaoContrato(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarClientePorSituacaoContratoInativoECancelados(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     void setIdEntidade(String aIdEntidade);

     void registrarUltimoAcesso(int codigoCliente, int codigoAcessoCliente) throws Exception;

     List<ClienteVO> consultarPorNomeCliente(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados, int maxResults) throws Exception;

     List consultarPorNomeClienteComAutorizacao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados, int maxResults) throws Exception;

     List consultarPorCpfClienteComAutorizacao(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados, int maxResults) throws Exception;

     ClienteVO consultarPorNomeCliente(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarClientesResumidos(int nivelMontarDados) throws Exception;

     List consultarClientesResumidos(int nivelMontarDados, int empresa) throws Exception;

     ClienteVO consultarPorCodigoPessoa(Integer codigoPrm, int nivelMontarDados) throws Exception;

     List consultarPorCodigoPessoaSemClienteCancelamento(Integer valorConsulta, Integer empresa, Integer categoria, String situacao, Integer codigoCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorMatriculaPessoaSemClienteCancelamento(String valorConsulta, Integer empresa, Integer categoria, String situacao, Integer codigoCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorNomePessoaSemClienteCancelamento(String valorConsulta, Integer empresa, Integer categoria, String situacao, int codigoCliente, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarTodosClienteComLimite(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List<ClienteVO> consultarPaginado(ClienteFiltroVO filtro, ConfPaginacao confPaginacao) throws Exception;

     List<ConsultaClienteTO> consultarPaginadoTelaConsulta(ClienteFiltroVO filtro, ConfPaginacao confPaginacao) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>Pessoa</code> através do
     * valor do atributo <code>nome</code> da classe <code>Cidade</code> Faz uso
     * da operação <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>PessoaVO</code>
     * resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
     ClienteVO consultarExisteCliente(String valorConsulta, Date data, int nivelMontarDados) throws Exception;

     ClienteVO consultarExisteClienteAlterar(String valorConsulta, Date data, Integer codigo, int nivelMontarDados) throws Exception;

    ResultSet consultarParcelaEmAbertoClientePorEmpresaDataVencimento(Integer empresa,
                                                                      Date dataVencimento, Boolean controlarAcesso,
                                                                      List<ClienteVO> listaClientes,boolean parcelasEmRemessa) throws Exception;

     ResultSet consultarProdutoVencido(Date dataVencimento, Boolean controlarAcesso, List<ClienteVO> listaClientes) throws Exception;

     ClienteVO consultarPorCodigoMatricula(Integer codigoMatricula,
                                                 Integer empresa, int nivelMontarDados) throws Exception;

    List<ClienteVO> consultarPorCodigoMatriculaOuCPF(String matricula, Integer empresa, int nivelMontarDados) throws Exception;

    ClienteVO consultarPorCodigoMatriculaExterna(Integer codigoMatriculaExterna, int nivelMontarDados) throws Exception ;

     ResultSet consultarAniversarioClientes(int empresa, final String colaboradores,ConfPaginacao paginacao,Date dataBaseInicio, Date dataBase) throws Exception;

     List consultarAniversarioClientes(int empresa, Date inicio, Date fim, List<String> listaSituacao, Integer plano,TipoPessoaEnum tipoPessoa) throws Exception;

    /**
     * consulta os aniversariantes de hoje
     *
     * @return
     */
     ResultSet contarAniversarioClientes(int empresa, final String colaboradores,Date dataBase, Date dataBaseInicio) throws Exception;

    /**
     * consulta clientes por contratos previstos para renovar E renovados no
     * periodo informado por parametro
     *
     * @param dataInicio
     * @param dataFim
     * @param empresa
     * @return list
     * @throws Exception
     */
     List<ClienteVO> consultarClientePorContratoPrevistoRenovado(Date dataInicio, Date dataFim, Integer empresa, List<ColaboradorVO> lista) throws Exception;

    /**
     * consulta clientes por contratos que foram renovados no periodo informado
     * por parametro, independentemente da previsao
     *
     * @param empresa
     * @param dataInicio
     * @param dataFim
     * @return list
     * @throws Exception
     */
     List<ClienteVO> consultarClientePorContratoRenovado(Integer empresa, Date dataInicio, Date dataFim,
                                                               Date dataRenovar, List<ColaboradorVO> lista) throws Exception;

    /**
     * consulta clientes por contratos previstos para o periodo E que foram
     * renovados no periodo informado
     *
     * @param empresa
     * @param dataInicio
     * @param dataFim
     * @param dataInicioRealizado
     * @param dataFimRealizado
     * @return list
     * @throws Exception
     */
     List<ClienteVO> consultarClientePorContratoRenovado(Integer empresa, Date dataInicio, Date dataFim, Date dataInicioRealizado, Date dataFimRealizado, List<ColaboradorVO> lista) throws Exception;

     List<ClienteVO> consultarClientePorPeriodoEmpresaQuestionarioColaborador(Date dataIni, Date dataFim, Integer empresa, List<ColaboradorVO> colaboradores, Boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorPeriodoContrato(Date inicio, Date fim, Integer empresa, List<String> situacoes, List<ColaboradorVO> lista, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List consultarPorPeriodoContrato(Date inicio, Date fim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * método usado na tela de resumo de ICV
     *
     * @throws Exception
     */
     Map<Integer,Integer> consultarClientesMapaICV(Date inicio, Date fim, Integer empresa, String situacao, String codigoColaboradores,
                                                       List<TipoContratoEnum> listaTiposContrato,
                                                       List<TipoBVEnum> listaTiposBV, boolean noDia, boolean incluirBolsistas) throws Exception;

    /**
     * consulta clientes por contratos renovados antecipados no periodo
     * informado por parametro
     *
     * @param empresa
     * @param dataInicio
     * @param dataFim
     * @return qtde
     * @throws Exception
     */
     List<ClienteSimplificadoTO> consultarClientePorContratoRenovadoAntecipado(Integer empresa, Date dataInicio, Date dataFim, List<ColaboradorVO> lista) throws Exception;

    /**
     * Consulta genérica para classe de clientes, conforme condicao (facultativa) informada
     *
     * @param condicao
     * @param nivelMontarDados
     * @return List<ClienteVO>
     * @throws Exception
     */
     List<ClienteVO> consultar(String condicao, final int nivelMontarDados)
            throws Exception;

    List<ClienteVO> consultarProfessor(String condicao, final int nivelMontarDados)
            throws Exception;

    /**
     *
     * @param dataInicio
     * @param dataFim
     * @param empresa      0 para consultar de todas as empresas
     * @param lista
     * @return
     * @throws Exception
     */
     List<ClienteSimplificadoTO> consultarClientePorContratoPrevistoRenovadoIndiferente(
            Date dataInicio, Date dataFim, Integer empresa, List<ColaboradorVO> lista) throws Exception;

    /**
     * consulta clientes por contratos renovados atrasados no periodo
     * informado por parametro
     *
     * @param empresa
     * @param dataInicio
     * @param dataFim
     * @return qtde
     * @throws Exception
     */
     List<ClienteSimplificadoTO> consultarClientePorContratoRenovadoAtrasado(Integer empresa,
                                                                       Date dataInicio, Date dataFim, List<ColaboradorVO> lista) throws Exception;

     ClienteVO consultarPorCodigoAulaAVulsaDiara(Integer codigoPrm, int nivelMontarDados) throws Exception;

     void alterar(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema, boolean centralEventos, boolean validardados) throws Exception;

     void incluirSemCommit(ClienteVO obj, QuestionarioClienteVO quest, ConfiguracaoSistemaVO configuracaoSistema, Boolean validarQuestionario) throws Exception;

     List consultarPorNomeClienteComLimite(Integer empresa, String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Conta os contratos trancados vencidos
     * @param empresa              0 para consultar de todas as empresas
     * @param dataBaseInicio
     * @param inicio
     * @param fim
     * @param colaboradores
     * @return
     * @throws Exception
     */
     ResultSet contarTrancamentosVencidosContratos(int empresa, Date dataBaseInicio, Date inicio, Date fim, final String colaboradores) throws Exception;

     ResultSet consultarTrancamentosVencidos(int empresa, Date dataBaseInicio, Date inicio, Date fim, final String colaboradores,ConfPaginacao paginacao) throws Exception;

     void incluirClienteSimplificado(ClienteVO obj) throws Exception;

    void incluirClienteSimplificado(ClienteVO obj, boolean controlarTransacao) throws Exception;

     int contarClientesInativosComPeriodoAcesso(int empresa, Date inicio, Date fim, List<ColaboradorVO> lista) throws Exception;

     List<ClienteVO> consultarClientesInativosComPeriodoAcesso(int empresa, Date inicio, Date fim, List<ColaboradorVO> lista) throws Exception;

     Integer consultaQuantidadeSituacaoContratoPorDataLancamentoEmpresaColaborador(Date prmIni,
                                                                                         Date prmFim, Integer empresa, String codigoColaboradores, String situacao,
                                                                                         List<TipoContratoEnum> listaTiposContrato,
                                                                                         List<TipoBVEnum> listaTiposBV,
                                                                                         boolean controlarAcesso, boolean noDia) throws Exception;

    HashMap<String, Integer> montarMapaConversaoPorQuestionarioResposta(Date inicioBV, Date fimBV, Date inicioContrato, Date fimContrato, Integer empresa, Integer codigoBV, String descricaoPergunta, String filtroConsultores) throws Exception;

    public Integer contarConversoesPorQuestionarioPeriodo(Date inicioBV, Date fimBV, Date inicioContrato, Date fimContrato, Integer empresa,Integer codigoBV, String descricaoPergunta, String filtroConsultores) throws Exception;

     List<ClienteVO> consultarClientePorSituacaoContratoSemAtestadoECarencia(String valorConsulta,
                                                                                   Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

     List<ClienteVO> consultarPorNomePessoaComLimite(
            String valorConsulta, Integer empresa, Integer codCliente, int nivelMontarDados) throws SQLException;

     List<AmostraClienteTO> consultarAmostra(String sql, Map<Integer, Integer> erros) throws Exception;
     List<ItemRelatorioTO> consultarClientesIdadeSexo() throws Exception;

    List<ItemRelatorioTO> relatorioGeralAlunos(Date inicioVencimento, Date fimVencimento, Date inicioCadastro, Date fimCadastro, Date inicioMatricula, Date fimMatricula,
                                               Date inicioRematricula, Date finalRematricula, Date inicioRenovacao, Date finalRenovacao,
                                               String nome, String sexo, String situacaoCliente, String situacaoContrato, Integer codigoEmpresa, Integer inicioIdade, Integer fimIdade, Integer codigoMes,
                                               Integer inicioDiasSemAparecer, Integer fimDiasSemAparecer, String bairro, ColaboradorVO consultor, ColaboradorVO professor, List<ColaboradorVO> listaConsultores,
                                               List<ColaboradorVO> listaProfessores, List<ColaboradorVO> listaProfessoresTreinoWeb, List<ModalidadeVO> listaModalidade, Integer duracao,
                                               Integer convenio, Integer plano, String rg, boolean planoTipoBolsa, boolean planoTipoCredito, Integer categoria, List<GenericoTO> vezes, Integer pacote,
                                               Date inicioLancamento, Date finalLancamento,  boolean situacaoGympass, boolean situacaoAdimplentes, boolean situacaoInadimplentes, boolean ultimoContratoCliente) throws Exception;

    List<ItemRelatorioTO> relatorioGeralAlunos(Date inicioVencimento, Date fimVencimento, Date inicioCadastro, Date fimCadastro, Date inicioMatricula, Date fimMatricula,
                                               Date inicioRematricula, Date finalRematricula, Date inicioRenovacao, Date finalRenovacao,
                                               String nome, String sexo, String situacaoCliente, String situacaoContrato, Integer codigoEmpresa, Integer inicioIdade, Integer fimIdade, Integer codigoMes,
                                               Integer inicioDiasSemAparecer, Integer fimDiasSemAparecer, String bairro, ColaboradorVO consultor, ColaboradorVO professor, List<ColaboradorVO> listaConsultores,
                                               List<ColaboradorVO> listaProfessores, List<ColaboradorVO> listaProfessoresTreinoWeb, List<ModalidadeVO> listaModalidade, Integer duracao,
                                               Integer convenio, Integer plano, String rg, boolean planoTipoBolsa, boolean planoTipoCredito, Integer categoria, List<GenericoTO> vezes, Integer pacote,
                                               Date inicioLancamento, Date finalLancamento, boolean ultimoContratoCliente, String codigosOrigemSistema, Integer codigoEvento, boolean dependente, boolean situacaoGympass, boolean situacaoAdimplentes, boolean situacaoInadimplentes, String listMatAlunosFiltroParQ,String filtroParQ) throws Exception;

     List<ItemRelatorioTO> consultarRelatorioVi(Date inicioBv, Date fimBv, ColaboradorVO consultor, List<ColaboradorVO> listaConsultores,
                                                      String situacao, String bvCompIncomp, Integer codigoEmpresa) throws Exception;

    List<ItemRelatorioTO> consultarAlunosCancelados(Date inicioCancelamento, Date fimCancelamento, ColaboradorVO consultor, ColaboradorVO professor,
                                                    List<ColaboradorVO> listaConsultores, List<ColaboradorVO> listaProfessores, String situacao,
                                                    String situacaoContrato, Integer codigoEmpresa, Integer vezesSemana, String modalidades, String categoria,
                                                    boolean filtrarCancelamentosComValorADevolver) throws Exception;

     List<ItemRelatorioTO> consultarAlunosTrancados(ColaboradorVO consultor, List<ColaboradorVO> listaConsultores, ColaboradorVO professor,
                                                          List<ColaboradorVO> listaProfessores, Integer codigoEmpresa) throws Exception;

     List<ItemRelatorioTO> consultarAlunosBonus(ColaboradorVO consultor, List<ColaboradorVO> listaConsultores, ColaboradorVO professor,
                                                      List<ColaboradorVO> listaProfessores, String bonus, Integer codigoEmpresa) throws Exception;

     List<ItemRelatorioTO> consultarAlunosAtestado(ColaboradorVO consultor, List<ColaboradorVO> listaConsultores, ColaboradorVO professor,
                                                         List<ColaboradorVO> listaProfessores, Date inicioAtestado, Date fimAtestado, Integer codigoEmpresa) throws Exception;

     String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar, String situacao) throws Exception;

     void validarCodAcessoAlternativo(final ClienteVO obj) throws Exception;

     List<ClienteVO> consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa,String situacao) throws SQLException;

     List<ClienteSimplificadoTO> consultarLista(EmpresaVO empresaLogada) throws Exception;

     List<AmostraClienteTO> consultarEnviadosMailing(String codigos, boolean indicados, boolean passivos) throws Exception;

     List<AmostraClienteTO> consultarNaoEnviadosMailing(String codigos, boolean indicados) throws Exception;

     void atualizarMensagensCadastroClientes(ConfiguracaoSistemaVO configuracao) throws Exception;

     void alterarClienteEmpresa(ClienteVO obj, EmpresaVO empresaAntiga) throws Exception;

     List<ClienteVO> consultarPreCadastro(final String chave, final String termoConsulta, 
            Integer empresa, boolean maisDeUmaEmpresa, int nivelMontarDados) throws Exception;

     List<ClienteVO> consultarClienteFuncionalidade(final String chave, final String termoConsulta,
                                                    Integer empresa, int nivelMontarDados) throws Exception;

     void gerarPendenciaCliente(ClienteVO obj, Boolean validarQuestionario,
                                      QuestionarioClienteVO quest, ConfiguracaoSistemaVO configuracaoSistema) throws Exception;

     int contarQtdClienteVinculos(List<ClienteSimplificadoTO> listaClientes, String tipoVinculo, int qtdMinima) throws Exception;

     List<ClienteSimplificadoTO> consultarClienteVinculos(List<ClienteSimplificadoTO> listaClientes, String tipoVinculo, int qtdMinima) throws Exception;

     void gerarNumeroMatricula(ClienteVO obj, EmpresaVO empresaLogada, ConfiguracaoSistemaVO configuracaoSistemaVO) throws Exception;

     void atualizarMatriculaAluno(int codigoMatriculaAtual) throws Exception;

    public void atualizarMatriculaExternaAluno(Integer codigoCliente, Integer matriculaExterna) throws Exception;
    
     List<ClienteVO> consultarClientesPorCfp(String valorConsulta, Integer clienteIgnorado, int nivelMontarDados, int maxResults) throws Exception;

     List<ClienteVO> consultarClientesPorRNE(String valorConsulta, Integer clienteIgnorado, int nivelMontarDados, int maxResults) throws Exception;


     List<ClienteVO> consultarClientesPorNomeComCpf(String valorConsulta, Integer clienteIgnorado, int nivelMontarDados) throws Exception;

    /**
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param colaboradores
     * @param paginacao
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
     ResultSet consultarPessoaSemFoto(boolean contar, int empresa,final String colaboradores,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception;

    /**
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param colaboradores
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
     ResultSet contarPessoaSemFoto(int empresa, final String colaboradores, Date dataBaseInicio) throws Exception;
    
     List<ClienteVO> consultarClientesDepententesCfp(Integer pessoaResponsavel, int nivelMontarDados) throws Exception ;

    JSONArray obterAlunos(String codigos) throws Exception;

     JSONArray obterAlunos(String codigos, String filter) throws Exception;

    Map<Integer, Integer> montarMapaPessoasTitulares() throws Exception;
    
     Integer obterNrClientesMesmoCfp(String valorConsulta, Integer clienteIgnorado) throws Exception;

     Integer obterNrClientesMesmoRNE(String valorConsulta, Integer clienteIgnorado) throws Exception;

     List<ClienteVO> consultarPreCadastroPassivo(final String termoConsulta, boolean maisDeUmaEmpresa) throws Exception;

     List<ClienteVO> consultarPreCadastroIndicado(final String termoConsulta, boolean maisDeUmaEmpresa) throws Exception;

     ClienteTitularDependenteTO consultarParticipanteFamiliaPorCodigoCliente(int codigoCliente,Boolean participanteDependente,int nivelMontarDados) throws Exception;

     ClienteTitularDependenteTO consultarClienteFamiliaChavePrimaria(int codigo,Boolean dependente,int nivelMontarDados) throws Exception;

     ClienteTitularDependenteTO consultarTitularPorDependente(int codigo,int nivelMontarDados) throws Exception;

     List<ClienteVO> consultarClienteFamiliaPorNome(String valorConsultar,int codigoTitular,int empresa,int limit,int nivelMontarDados) throws Exception;

     List<ClienteTitularDependenteTO> consultarFamiliaPorClienteTitular(int codigoTitular) throws Exception;

     void alterarTitularClienteDependente(ClienteTitularDependenteTO clienteTitular) throws Exception;

     ClienteTitularDependenteTO incluirParticipanteFamilia(ClienteTitularDependenteTO clienteTitular,Boolean proprietario,UsuarioVO usuario) throws Exception;

     void incluirHistoricoFamiliar(ClienteTitularDependenteTO clienteCategoriaHistoricoVO, UsuarioVO usuarioVO) throws Exception;

     void excluirFamiliar(int codigo) throws Exception;

     Integer obterMatriculaAluno() throws Exception;

    ClienteVO consultarPorEmail(String email, Integer empresa, int nivelMontarDados) throws Exception;
    ClienteVO consultarDadosClienteUnicaConsulta(Integer codigoCliente)throws Exception;

    ClienteVO consultarPorCodigoContrato(Integer codigoContrato, int nivelMontarDados) throws Exception;

    ClienteVO incluirClienteViaWebService(ColaboradorVO colaboradorVO, EmpresaVO empresaVO, String nome, String email, String telResidencial, String telCelular) throws Exception;

    ClienteVO sortearCliente(ConfiguracaoSorteioVO config, int nivelMontarDados) throws Exception;

    boolean clientePossuiRemessa(ClienteVO clienteVO) throws Exception;

    void excluirClienteETodosSeusRelacionamentos(ClienteVO clienteVO, UsuarioVO usuarioLogado) throws Exception;

    void excluirTokenGymPassCliente(ClienteVO clienteVO, UsuarioVO usuarioVO) throws Exception;

    void excluirTokenGoGoodCliente(ClienteVO clienteVO, UsuarioVO usuarioVO) throws Exception;

    Integer consultarQtdClientesPorSituacaoComAutorizacaoCobranca(String situacao, Integer empresa) throws Exception;

    List<ClienteVO> consultarClientesPorContaCorrente(int contaCorrente, Integer contaCorrenteDv, String orderBy) throws Exception;

    String obterImportacaoClinteEstacionamento(String sql,Integer codigoEmpresa) throws Exception;

    String obterImportacaoClinteEstacionamento(String sql, Integer codigoEmpresa, EmpresaConfigEstacionamentoVO estConfig) throws Exception;

    Integer consultarCodEmpresaClientePorContrato(Integer contrato) throws Exception;
    String consultarNomeEmpresaClientePorContrato(Integer contrato) throws Exception;

    String montarEmailCancelamento(ContratoOperacaoVO contratoOperacaoVO, EmpresaVO empresaVO, String urlAssinatura) throws Exception;

    String montarEmailAssinaturaContrato (ContratoVO contratoVO) throws Exception;

    public ClienteSimplificadoTO consultarClienteSimplificado(int codigoPessoa) throws Exception;

    public void marcarCliente(Integer cliente, Integer usuario) throws Exception;
    
    public void desmarcarCliente(Integer cliente, Integer usuario) throws Exception;
        
    public void adicionarObservacaoMarcacao(Integer cliente, Integer usuario, String observacao) throws Exception;
    
    public List<ClienteVO> obterClientesMarcados(Integer usuario);

    public List<ClienteVO> consultarPorNomeClienteFamilia(Integer codigoEmpresa, String nomeCliente, int limit, int nivelMontarDados) throws Exception;

    public List<Integer> obterListaTodosClientes() throws Exception;

    public void gerarPendenciaCadastroCliente(Integer codigo,
                                              UsuarioVO usuario,
                                              ConfiguracaoSistemaVO configuracao,
                                              List<ConfiguracaoSistemaCadastroClienteVO> listaCli,
                                              List<ConfiguracaoSistemaCadastroClienteVO> listaVi);

    public void atualizarVersaoClienteTW(String key,ClienteVO cliente) throws Exception;

    void alterarClienteSimplesSite(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema, boolean controlarTransacao) throws Exception;

    void verificarCliente(ClienteVO cliente, UsuarioVO usuarioVO) throws Exception;

    void desverificarCliente(ClienteVO cliente, UsuarioVO usuarioVO) throws Exception;

    int contarClientesParaVerificar(Integer codEmpresa, Boolean verificado) throws Exception;

    List<ClienteSimplificadoTO> consultarClientesParaVerificar(Integer codEmpresa, Boolean verificado) throws Exception;

    Date consultarPrimeiraVerificacao(Integer codEmpresa) throws Exception;

    Date consultarUltimaVerificacao(Integer codEmpresa) throws Exception;

    void ativarVerificacaoClientesAtivos(Integer codEmpresa) throws Exception;

    DescontoBoletoTO descobrirDescontoBoletoParaCliente(List<MovParcelaVO> parcelas) throws Exception;

    void alterarObjecaoCliente(ClienteVO clienteVO, UsuarioVO usuarioVO, boolean controlarTransacao) throws Exception;

    int contarClientesComObjecaoDefinitiva(Integer codEmpresa) throws Exception;

    List<ItemRelatorioTO> listaClientesComObjecaoDefinitiva(Integer codEmpresa) throws  Exception;

    void alterarSomenteTemMaisDeUmaDigital(String codAcesso, boolean temMaisDeUmaDigital) throws Exception;

    void alterarCodigoAcessoAlternativo(String codAcesso, String codAcessoAlternativo) throws Exception;
    
    public ClienteVO consultarPorIdentificadorRemessaItem(Integer identificador,int codigoEmpresa,int nivelMontarDados) throws Exception;

    public String consultarCodigoAcesso(Integer codigo) throws Exception;

    ConsultaClienteWS consultarCliente(Integer codigoEmpresaZW, String email, String cpf, String codacesso)throws Exception;

    ConsultaClienteWS consultarClienteComTelefone(Integer codigoEmpresaZW, String email, String cpf, String codacesso, String telefone)throws Exception;

    public JSONArray consultarClientesJSON(String cpf, String nome, String matricula, Date limite, Integer codEmpresa) throws Exception;
    
    List<ClientesICV> consultarClientesListaICV(Map<Integer,Integer> mapaClienteContrato, boolean isRematricula) throws Exception;

    public List<ItemRelatorioTO> listaClientePorObjecaoDefinida(Date dataIncial, Date dataFinal, boolean porFase, List<UsuarioVO> usuarioVOList, String... objecoes) throws Exception;

    void excluirAnexo(ClienteVO cliente, UsuarioVO usuarioVO) throws Exception;
    public void gravarChaveAnexo(ClienteVO cliente)throws Exception;

    /**
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param colaboradores
     * @param paginacao
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
    ResultSet consultarClienteSemAssinaturaDigital(int empresa,final String colaboradores,ConfPaginacao paginacao, Date dataBaseInicio, Boolean assinaturaCancelamento) throws Exception;

    /**
     *
     * @param empresa           0 para consultar de todas as empresas
     * @param colaboradores
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
    ResultSet contarClienteSemAssinaturaDigital(int empresa, final String colaboradores, Date dataBaseInicio, Boolean assinaturaCancelamento) throws Exception;

    void removerFreePass(ClienteVO obj) throws Exception;

    boolean consultarSeResponsavelPorAlguem(Integer codigo) throws Exception;

    public List<ClienteADJSON> consultarClientesAD(String nomePlano, boolean reset) throws Exception;

    public List consultarPorCodigosMatricula(String valorConsulta, int nivelMontarDados) throws Exception;
    Boolean validarVencimentoCarteirinha(Integer codigoCliente) throws Exception;

    void alterarIndicacao(int codigoCliente, int codigoClienteIndicacao) throws SQLException;

    ResultSet consultarClienteSemGeoLocalizacao(int codigoEmpresaFiltro, String colaboradores, boolean b, ConfPaginacao confPaginacao, Date dataBaseInicialFiltroBI) throws Exception;

    ResultSet contarPendenciaClientePorEmpresaColaboradorSemGeoLocalizacao(int codigoEmpresaFiltro, String s, String at, boolean b, Date dataBaseInicialFiltroBI) throws Exception;

    /**
     * Consultar indicadores por cliente
     *
     * @param empresa      0 para consultar todas
     * @param situacao     SituacaoClienteEnum
     * @param limit
     *
     * @return
     * @throws JSONException
     * @throws SQLException
     */
    JSONArray consultarIndicadoresJSON(int empresa, SituacaoClienteEnum situacao, int limit, int offset) throws JSONException, SQLException;

    int consultarIndicadoresCount(int empresa, SituacaoClienteEnum situacaoEnum, int limit, int offset) throws SQLException;

    void alterarStatusChaveDevolvidaAluguelArmario(Integer codigoAluguel, Boolean chaveDevolvida,
                                                   Integer cliente, Integer empresa,
                                                   boolean habilitarGestaoArmarios) throws Exception;

    ResultSet consultarClientesComAluguelArmarioVencidoChaveDevolvida(Date dia, boolean b, List<ClienteVO> clienteVOS, int codigoEmpresa) throws Exception;

    InfoClienteTO obterInfoCliente(String chave, Integer pessoa) throws Exception;

    List<ConsultaClienteWS> consultarClienteEmailOuCPFouCnpj(String email, String cpf, String cnpj, Integer empresa, Integer cliente, boolean consultarEndereco, boolean consultarQtdDependentes) throws Exception;

    List<ClienteVO> consultarTotalizadorBiRegataramBrindes(Date dataInicial, Date dataFinal, EmpresaVO empresa)throws Exception;

    ClienteVO consultarGymPass(String gympassuniquetoken,int empresa, int nivelMontarDados) throws Exception;

    ClienteVO consultarGoGood(String gogoodtoken, String tokenAcademy, int empresa, int nivelMontarDados) throws Exception;

    List<ClienteSimplificadoTO> consultarClienteMaisProfessor(List<ClienteSimplificadoTO> lista) throws Exception;

    List<ConsultaIntegracaoFera> consultarIntegracaoFera() throws Exception;

    void alterarCodigoExternoAutorizacaoClienteVindi(Integer codigo) throws SQLException, Exception;

    JSONArray consultarDadosClienteConsultaTW(Integer codigoCliente)  throws JSONException, SQLException;

    boolean possuiPeriodoDeAcesso(Integer codigoCliente) throws Exception;

    JSONObject consultarTodosCodigoPessoa() throws Exception;

    ClienteVO consultarPorGympassUniqueToken(String gympassuniquetoken, Integer empresa, int nivelMontarDados) throws Exception;

    Integer consultarCodigoClientePorGympassUniqueToken(String gympassuniquetoken, Integer empresa) throws Exception;

    void removerTokenGympass(Integer codPessoa, Integer codEmpresa) throws Exception;

    UsuarioMovelVO gerarUsuarioMovelAluno(String key, Integer codigoCliente, String senha, String username) throws Exception;

    void alterarGymPassUniqueToken(String gympassUniqueToken, int codigo) throws SQLException;

    Integer jaTemFreePass(Date dia, Integer cliente);

    String consultarExisteAlunoZwTreino(Integer codigoCliente, Integer empresa)throws Exception;

    Integer consultarQuantidadeProdutoVigenteVendaAvulsa(String tipoProduto, Date dataVencimento, Integer codCliente, Integer empresa, Boolean desconsideraDataVigencia) throws Exception;

    Integer consultarQuantidadeProdutoVigenteContrato(String tipoProduto, Date dataVencimento, Integer codCliente, Integer empresa, Boolean desconsideraDataVigencia) throws Exception;

    ResultSet consultarClienteSemBiometriaFacial(int codigoEmpresaFiltro, String colaboradores, ConfPaginacao confPaginacao, Date dataBaseInicialFiltroBI) throws SQLException;

    ResultSet contarClienteSemBiometriaFacial(int codigoEmpresaFiltro, String colaboradores, Date dataBaseInicialFiltroBI) throws SQLException;

    ClienteSescTO consultarPorMatriculaSESC(String termoConsulta, ConfiguracaoSistemaVO configuracaoSistemaVO) throws Exception;

    ClienteSescTO consultarPorCpfSESC(String termoConsulta, ConfiguracaoSistemaVO configuracaoSistemaVO) throws Exception;

    ClienteSescTO consultarPorCpfOuMatriculaApiSescGo(String termoConsulta, ConfiguracaoSistemaVO configSistema) throws Exception;

    ClienteSescTO consultarPorCpfApiSescDf(String termoConsulta, String token) throws Exception;

    PessoaVO obterPessoaResponsavelCliente(Integer cliente, Integer pessoaCliente, int nivelMontarDados) throws Exception;

    Integer obterCodigoClientePorPessoa(Integer codigoPessoa) throws Exception;

    void incluirAtualizacaoCadastral(Integer codigoUsuario, boolean conclusao) throws Exception;

    ClienteAmigoFitJSON consultarIntegracaoAmigoFit(String cpf) throws Exception;

    Integer consultarCodigoPessoaPorMatricula(String gympassuniquetoken) throws Exception;

    void alterarSituacaoClientePorCodigoPessoa(Integer codigoPessoa) throws Exception;

    double consultarSaldoCliente(ClienteVO clienteVO) throws Exception;

    Integer consultaQtdClienteGymPass (Integer codigoEmpresa) throws Exception;

    List<ResumoPessoaBIAcessoGymPassVO> consultarClienteGymPass(Integer codigoEmpresa) throws Exception;

    String consultaClientePorMatriculaSesc (String matriculaSesc) throws Exception;

    Boolean verificaSeClienteEColaborador(Integer codigoPessoa) throws Exception;

    List<ClienteVO> consultarClientesRecentes(Integer codUsuarioLogado, Integer limit) throws Exception;

    List<ClienteVO> consultarClientesRecentes(Integer codUsuarioLogado) throws Exception;

    List<ClienteVO> consultarClientesFavoritos(Integer codigo, Integer limit) throws Exception;

    List<ClienteVO> consultarClientesFavoritos(Integer codigo) throws Exception;

    void marcaCliente(Integer codCliente, Integer codUsuarioLogado)throws Exception;

    void desmarcarClienteComoFavorito(Integer codigo)throws Exception;

    ClienteVO consultaSeClienteJaFoiFavorito(Integer codCliente, Integer codUsuarioLogado) throws Exception;

    void remarcarClienteComoFavorito(Integer codigo)throws Exception;

    void alterarSincronizadoRedeEmpresa(Integer codCliente) throws Exception;

    void preencherTitularPlanoCompartilhado(Integer titularPlanoCompartilhado, Integer clienteBeneficiado) throws Exception;

    boolean possuiTitularPlanoCompartilhadoDependente(Integer codigoCliente) throws Exception;

    void finalizarDependenciaPlanoCompartilhado(Integer clienteDependente, boolean alterarFamiliar) throws Exception;

    void removerVinculoTitularDependenteSemContratoDependenteVigente(Integer clienteDependente) throws Exception;

    String consultarNomePorCodigoCliente(Integer codigo) throws Exception;

     void inserirClienteMarcado(Integer codCliente, Integer codUsuarioLogado, boolean favorito) throws SQLException;

    List<ClienteVO> consultarClientesVipsNaoSincronizados(Integer codigo) throws Exception;

    String consultarTokenGymPassCliente(Integer codigoCliente) throws Exception;

    void removerSincronizadoRedeEmpresa(String codAcesso) throws Exception;

    void incluirIpLocalizacaoCliente(ClienteVO obj, String nowLocationIpJson) throws Exception;

    NowLocationIpVendaDTO consultarUltimoIpLocalizacaoCliente(Integer codCliente) throws Exception;

    boolean consultarClienteTotalPass(Integer codigoCliente) throws Exception;

    boolean consultarClienteUltimoAcessoTotalPass(Integer codigoCliente) throws Exception;

    void registrarLogGynPass(ClienteVO clienteVO, String oep, String detalhes, UsuarioVO usuarioVO) throws Exception;

    void validarCompartilharLink(ClienteVO clienteVO, boolean cadastrarCartao, EmpresaVO empresaVO, UsuarioVO usuarioVO, Boolean todasEmAberto, String parcelasSelecionadas) throws Exception;

    void preencherAcessoFreePass(Integer codigoCliente, SituacaoClienteSinteticoDWVO scDWVO) throws Exception;

    void marcarAulaExperimentalConsumidaLinkVisitante(Integer codigo) throws Exception;

    Boolean validarAulaExperimentalConsumidaLinkVisitante(Integer codigo) throws Exception;

    void validarMatricula(ClienteVO obj) throws Exception;

    List<ClienteVO> consultarClientesInadinplentesSincronizarRestricao(Integer empresa, boolean clientesIncluirRestricao) throws Exception;

    Boolean clienteEstaInadimplente(Integer codigoCliente, Integer codigoEmpresa) throws Exception;

    void atualizarDataInclusaoClienteRestricao(Integer codigoCliente, Date data) throws Exception;

    void validarTotalPass(ClienteVO clienteVO, EmpresaVO empresaVO, UsuarioVO usuarioVO) throws Exception;

    String obterMatriculaPorCodigoPessoa(Integer codigoPessoa) throws Exception;

    boolean clientePossuiDebitoEmConta(Integer pessoa) throws SQLException;

    List<ConsultaClienteGymnamic> consultarClienteEmailOuCPFGymnamic(String email, String cpf, Integer empresa) throws Exception;

    List<ClienteVO> consultarClientesNaoSincronizadosRedeEmpresa(Integer empresa) throws Exception;

    void atualizarDataSincronizacaoFranqueadora(Integer codigoCliente, Date data) throws Exception;

    void updateIdManyChat(Integer codigoCliente, Long idManyChat) throws Exception;

    String obterTipoCategoriaCliente(Integer pessoaOperacao) throws Exception;

    String desvincularAlunoTotalpass(Long codigoPessoa, UsuarioVO usuarioVO) throws Exception;

    String inserirWebhookGogood(String chave, EmpresaVO empresaVO);

}
