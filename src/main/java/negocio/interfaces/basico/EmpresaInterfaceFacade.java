package negocio.interfaces.basico;

import negocio.comuns.basico.ConfiguracaoIntegracaoAcessoPratiqueVO;
import negocio.comuns.basico.ConfiguracaoIntegracaoFogueteVO;
import negocio.comuns.crm.ConfiguracaoEmpresaBitrixVO;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface EmpresaInterfaceFacade extends SuperInterface {

    EmpresaVO novo() throws Exception;

    void incluir(EmpresaVO obj) throws Exception;

    void alterar(EmpresaVO obj) throws Exception;

    void excluir(EmpresaVO obj) throws Exception;

    /**
     * Consulta todas as empresas
     *
     * @return List
     * @throws Exception
     * <AUTHOR> felipe
     */
    List<EmpresaVO> consultarTodas(final Boolean situacao, int nivelMontarDados) throws Exception;

    /**
     * Quantidade de emprsas cadastradas
     *
     * @param situacao      TRUE para empresas ativas
     * @return
     * @throws Exception
     */

    public int quantidadeEmpresas(final Boolean situacao) throws Exception;

    int countEmpresas(final Boolean situacao) throws Exception;

    EmpresaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    EmpresaVO consultarPorChavePrimariaLogin(Integer codigo, int nivelMontarDados) throws Exception;

    EmpresaVO consultarPorChavePrimaria(Integer codigo, Boolean administrador, int nivelMontarDados) throws Exception;

    EmpresaVO consultarPorChavePrimariaLogin(Integer codigo, Boolean administrador, int nivelMontarDados) throws Exception;

    List<EmpresaVO> consultarPorCodigo(Integer valorConsulta, final Boolean situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    EmpresaVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception;

    EmpresaVO consultarPorCnpj(String valorConsulta, int nivelMontarDados) throws Exception;

    List<EmpresaVO> consultarPorNome(String valorConsulta, Boolean situacao, boolean controlarAcesso, int nivelMontarDados, Integer codigoEmpresaNaoConsultar) throws Exception;

    List<EmpresaVO> consultarPorNome(String valorConsulta, Boolean situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<EmpresaVO> consultarPorNomeClubeVantagens(String valorConsulta, Boolean situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorNomeLogin(String valorConsulta, final Boolean situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorRazaoSocial(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorCidade(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorInscEstadual(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List consultarPorQuestionarioPrimeiraVisita(String valorConsulta, int nivelMontarDados) throws Exception;

    List consultarPorQuestionarioRetorno(String valorConsulta, int nivelMontarDados) throws Exception;

    List consultarPorQuestionarioRematricula(String valorConsulta, int nivelMontarDados) throws Exception;

    void setIdEntidade(String aIdEntidade);

    void finalizarConexaoAtivaSeHouver() throws Exception;

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    String obterMascaraMatricula(Integer empresa) throws Exception;

    void incluir(EmpresaVO obj, boolean centralEventos) throws Exception;

    void alterar(EmpresaVO obj, boolean centralEventos) throws Exception;

    void alterarSemCommit(EmpresaVO obj) throws Exception;

    void excluir(EmpresaVO obj, boolean centralEventos) throws Exception;

    EmpresaVO consultarPorNomeEmpresa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    String obterTimeZoneDefault(Integer empresa) throws Exception;

    EmpresaVO obterEmpresaDeUmaListaParcelas(List<MovParcelaVO> listaParc) throws Exception;

    byte[] obterFoto(final String chave, final Integer codigoEmpresa,
                     MidiaEntidadeEnum tipo) throws Exception;

    byte[] obterFotoPadrao(final String chave, final Integer codigoEmpresa,
                     MidiaEntidadeEnum tipo) throws Exception;

    void atualizarNrdiascompensacao(Integer nrdiascompensacao, Integer codigoEmpresa) throws Exception;

    String obterTokenSMS(final int empresa) throws Exception;

    String obterTokenMQV(final int empresa) throws Exception;

    String obterTokenSMSShortCode(final int empresa) throws Exception;

    Date obterDataExpiracao(final int empresa) throws Exception;

    String consultarJSON() throws Exception;

    List<EmpresaVO> consultarEmpresas() throws Exception;

    List<EmpresaVO> consultarEmpresas(int nivelMontarDados) throws Exception;

    List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

    void debitarCreditoDCC(Integer quantidadeSubtrair, Integer empresa, String observacao) throws Exception;

    void updateFotos(EmpresaVO obj);

    Boolean obterConfiguracaoLiberarPersonalComDebito(Integer empresa) throws Exception;

    Boolean isCobrarCreditoPactoBoleto(Integer empresa) throws Exception;

    JSONArray consultarEmpresasJson() throws Exception;

    JSONObject obterParametrosAtendimentoSol(int codigoEmpresa, int codigoUsuario) throws Exception;

    Map<Integer, EmpresaVO> obterMapaEmpresas() throws Exception;

    Map<Integer, EmpresaVO> obterMapaEmpresas(int nivelMontarDados) throws Exception;

    int consultarSaldoDCC(int codigoEmpresa) throws Exception;

    void atualizarSequencialLoteRPS(Integer sequencialLoteRPS, Integer codEmpresa) throws Exception;

    String obterCnpjEmpresa(Integer empresa) throws Exception;

    boolean toggleBloqueioTemporario(Integer empresa, boolean consulta) throws Exception;

    public ConfiguracaoEmpresaBitrixVO consultarConfigIntegracaEmpresaBitrix24(String chave, Connection con) throws Exception;

    void atualizarLimiteInicialItensBIPendencia(EmpresaVO empresaVO, Date limiteInicialItensBIPendencia) throws Exception;

    JSONObject consultarParaMapa(Integer codigo) throws Exception;

    JSONObject obterInfoRedeDCC(String urlOamd, String chave) throws Exception;

    void debitarCreditoDCCRede(String urlOamd, String chave, Integer quantidadeSubtrair, Integer codigoEmpresa) throws Exception;

    void addCreditoDCCRede(String urlOamd, String chave) throws Exception;

    void alterarConfiguracoesReenvioAutomaticoRemessa(boolean habilitarReenvioAutomaticoRemessa, Integer qtdExecucoesRetentativa, Integer empresa) throws Exception;

    void alteraPontosAcesso(Integer codigo, Integer pontos) throws Exception;

    JSONArray obterInfoClima(String urlOamd, String chave, Integer codigoEmpresa, long data) throws Exception;

    void alteraPontosChuva(Integer codigo, Integer pontos) throws Exception;

    void alteraPontosFrio(Integer codigo, Integer pontos) throws Exception;

    void alteraPontosCalor(Integer codigo, Integer pontos) throws Exception;

    Date concederDiaExtra(Integer codigo) throws Exception;

    Date concederDiasExtras(Integer codigo, int dias) throws Exception;

    int consultarTotalDiasConcedidos(Integer codigoEmpresa) throws Exception;

    EmpresaVO consultarPorIdExterno(String idexterno, int nivelMontarDados) throws Exception;

    void alterarConfiguracoesClubeVantagens(EmpresaVO empresaSelecionada) throws Exception;

    /**
     * Consulta o código de todas as empresas cadatrados no sistema.
     * @return
     * @throws Exception
     */
    public List<Integer> consultarTodosCodigos() throws  Exception;


    /**
     * Realiza a alteração do nome da empresa.
     * @param nome
     * @param  codigo
     * @throws Exception
     */
    public void alterarNomeEmpresa(String nome,String razaoSocial, Integer codigo) throws  Exception;

    public boolean isIntegracaoSpivi(final int codigo) throws Exception;

    void alterarSomenteAtualizarDadosCadastroSemCommit(EmpresaVO obj) throws SQLException;

    boolean atualizarEmpresaFinanceiroOAMD(String chave, Integer empresa, String classificacao, String gestor, String financeiro);

    void alterarDataSincronizacaoFinanceiro(EmpresaVO obj) throws Exception;

    boolean isAcessoSomenteComAgendamento(Integer empresa);

    void alterarAcessoSomenteComAgendamentoTodasEmpresas(boolean acessoSomenteComAgendamento) throws Exception;

    Integer obterCapacidade(Integer codEmpresa) throws SQLException;

    Boolean integracaoMyWellnesHabilitada(Integer empresa, boolean validarEnviarColaborador) throws Exception;

    Boolean isBloquearAcessoSemAssinaturaDigital(Integer empresa);

    Boolean isBloquearAcessoAlunoParQNaoAssinado(Integer empresa);

    Boolean isHabilitarCobrancaAutomaticaNaVenda(Integer empresa);

    Boolean bloquearAcessoSemTermoResponsabilidade(Integer empresa);

    Integer obterEmpresaClientePessoa(Integer cliente, Integer pessoa) throws Exception;

    Integer obterEmpresaColaborador(Integer colaborador) throws Exception;

    boolean isNotificarWebhook(Integer empresa);

    Integer consultarCarenciaRenovacaoPrimeiraEmpresaAtiva() throws Exception;

    Integer obterConvenioCobrancaPix(Integer codigoEmpresa);

    Integer obterConvenioCobrancaCartao(Integer codigoEmpresa);

    Integer obterConvenioCobrancaBoleto(Integer codigoEmpresa);

    boolean isGerarBoletoCaixaAberto(Integer empresa);

    boolean isCobrarAutomaticamenteMultaJuros(Integer empresa);

    public boolean verificaSeUtilizaVitio(Integer empresa);

    public String consultarLinkVitio(Integer empresa);

    public String consultarLinkEbookVitio (Integer empresa);

    public String mensagemWpp(Boolean aderiu, Integer empresa);

    JSONArray gymIds() throws Exception;

    Integer obterLimiteDeAcessosPorDiaGympass(Integer codEmpresa) throws SQLException;

    Integer obterLimiteDeAulasPorDiaGympass(Integer codEmpresa) throws SQLException;

    Integer obterCodEmpresaPorGymID(String gymId) throws Exception;

    Integer obterCodEmpresaPorGoGoodTokenAcademy(final String tokenAcademy) throws Exception;

    String obterGymIDPorCodEmpresa(final Integer codEmpresa) throws Exception;

    boolean temPlanoPacote(Integer empresa);

    void alteraDiasPontuacaoAtiva(Integer codigo, List<String> diasDaSemanaAtivos) throws Exception;

    Boolean configsRdStationMarketingEstaoHabilitadas(int codigoEmpresa) throws Exception;

    boolean isReenvioAutomaticoDeCobranca(Integer codEmpresa) throws Exception;

    ConfiguracaoIntegracaoFogueteVO consultarConfiguracaoIntegracaoFoguete(int codigoEmpresa) throws Exception;

    ConfiguracaoIntegracaoAcessoPratiqueVO consultarConfiguracaoIntegracaoAcessoPratique(int codigoEmpresa);

    Boolean integracaoManyChatHabilitada(int empresa) throws Exception;

    String obterTokenApiIntegracaoManyChat(int empresa) throws Exception;

    void reiniciarServicosNovaEmpresaOuAtivaInativa() throws Exception;


    boolean empresaUsaPinpad(int codigoEmpresa) throws Exception;
}
