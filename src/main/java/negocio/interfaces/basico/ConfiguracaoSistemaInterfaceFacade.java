package negocio.interfaces.basico;

import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.enumerador.FiltrosEnum;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ConfiguracaoSistemaInterfaceFacade extends SuperInterface {

    public ConfiguracaoSistemaVO novo() throws Exception;

    public void incluir(ConfiguracaoSistemaVO obj) throws Exception;

    public void alterar(ConfiguracaoSistemaVO obj) throws Exception;

    public void excluir(ConfiguracaoSistemaVO obj) throws Exception;

    public ConfiguracaoSistemaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public boolean obterPermiteReplicarFornecedorConfiguracao() throws Exception;

    public List consultarPorDescricaoQuestionario(String valorConsulta, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public ConfiguracaoSistemaVO buscarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public String obterMascaraMatricula(Integer configuracaoSistema) throws Exception;

    public boolean consultarCampoRodarSqlsBancoInicial() throws Exception;

    public void alterarSemCommit(ConfiguracaoSistemaVO obj) throws Exception;

    public boolean consultarSePermiteAlteracaoDataBaseContrato() throws Exception;

    /**
     * Consulta o parâmetro usaecf da configuração do sistema
     *
     * @return boolean
     * @throws Exception
     */
    public boolean consultarSeInsereCupom() throws Exception;

    public void montarEmailsRecorrencia(ConfiguracaoSistemaVO config) throws Exception;

    public void gravarEmailsRecorrencia(ConfiguracaoSistemaVO config) throws Exception;

    public Integer buscarQtdDiasParaExpirarSenha() throws Exception;

    public void gravarEmailsFechamentoAcessos(ConfiguracaoSistemaVO conf) throws Exception;

    public String consultarEmailsFechamentoAcessos(ConfiguracaoSistemaVO config) throws Exception;

    public Boolean verificarSePermiteEnviarSMSAutomatico() throws Exception;

    public Boolean verificarValidacaoContatoMeta() throws Exception;

    public boolean consultarUsaEcf() throws Exception;

    public boolean consultarEcfApenasPlano() throws Exception;

    public boolean consultarUsaEcfPagamento() throws Exception;

    public boolean maisDeUmaEmpresa() throws Exception;

    public boolean maisEmpresa() throws Exception;

    public String obterNumeroCielo() throws Exception;

    boolean obterReplicarRedeEmpresa(String nomeConfig) throws Exception;

    public ConfiguracaoSistemaVO consultarConfigs(int nivelMontarDados) throws Exception;

    public boolean usarNomeResponsavelNota() throws Exception;

    public boolean utilizarSistemaParaClube() throws Exception;

    public boolean imprimirReciboPagtoMatricial() throws Exception;

    public boolean validarCPF() throws Exception;

    Integer incrementarSequencialItem() throws Exception;

    Integer incrementarSequencialArquivo() throws Exception;

    public void incluirFiltro(Integer usuario, FiltrosEnum filtro, String valor) throws Exception;

    public void deleteFiltro(Integer usuario, FiltrosEnum filtro) throws Exception;

    public String obterFiltro(Integer usuario, FiltrosEnum filtro) throws Exception;

    public Integer obterDiasQuestionarioPorTipo(Integer codigo) throws Exception;

    public boolean usarDigitalComoAssinatura() throws Exception;

    boolean isNomeArquivoRemessaPadraoTivit() throws Exception;

    boolean isDefinirDataInicioPlanosRecorrencia() throws Exception;

    Integer obterSequencialNotaFamilia() throws Exception;

    void incrementarSequencialNotaFamilia() throws Exception;

    Integer obterSequencialProcessoImportacao() throws Exception;

    void incrementarSequencialProcessoImportacao() throws Exception;

    Integer obterNrDiasAvencer() throws Exception;

    void alterarBancoApresentacao() throws Exception;

    boolean isPriorizarVendaRapida() throws Exception;

    boolean isApresentarMarketPlace() throws Exception;

    boolean isAgruparRemessasCartaoEDI();

    void persistirChavePublicaPrivadaSesc(ConfiguracaoSistemaVO obj) throws Exception;

    boolean isEnviarRemessasRemotamente();

    boolean isPropagarAssinaturaDigital();

    boolean utilizarServicoSesiSC();

    boolean realizarEnvioSesiSC();

    public Boolean verificarAssinaturaDigital() throws Exception;

    public ConfiguracaoSistemaVO consultarConfigsParaAtualizarDadosBI(int nivelMontarDados) throws Exception;

    boolean isTermoResponsabilidade();

    boolean isTermoResponsabilidadeExaluno();

    boolean isPermiteTrocaEmpresaMultiChave();

    boolean isPermiteImpressaoContratoMutavel();

    boolean isPermiteEstornarContrato30MinAposLancamento();

    public boolean isConciliarSemNumeroParcela() throws Exception;

    public boolean isManterContratoAssinadoNaRenovacaoContrato() throws Exception;

    ConfiguracaoSistemaVO buscarConfiguracaoSistema() throws Exception;
}