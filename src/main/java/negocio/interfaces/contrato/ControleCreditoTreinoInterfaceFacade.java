package negocio.interfaces.contrato;

import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.plano.AlunoHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Reposicao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidadeHorarioTurma;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONArray;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

import java.util.Date;
import java.util.List;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ReposicaoVO;

/**
 * Created by ulisses on 17/11/2015.
 */
public interface ControleCreditoTreinoInterfaceFacade extends SuperInterface {

    void incluir(ControleCreditoTreinoVO controleCreditoTreinoVO, Integer codigoCliente, SituacaoClienteSinteticoDW situacaoClienteSinteticoDW)throws Exception;
    void excluirPorCodigoContrato(Integer codigoContrato)throws Exception;
    void excluirPorCodigo(Integer codigo)throws Exception;
    void ajustarOperacaoExcluirAulaDesmarcada(AulaDesmarcadaVO aulaDesmarcada)throws Exception;
    void ajustarOperacaoExcluirReposicao(ReposicaoVO reposicao, String parcelaVencida)throws Exception;
    void incluirSemCommit(ControleCreditoTreinoVO controleCreditoTreinoVO, Integer codigoCliente, SituacaoClienteSinteticoDW situacaoClienteSinteticoDW,Date dataBase)throws Exception;
    List<ControleCreditoTreinoVO>consultar(Integer codigoContrato)throws Exception;
    List<ControleCreditoTreinoVO>consultarPorOperacao(Integer codigoContrato, Integer codigoTipoOperacao, Boolean aulasMarcadasComCreditoExtra, Date dataInicioOperacao, Date dataFimOperacao)throws Exception;
    boolean houveAcesso(Date dataValidar, Integer codigoContrato, HorarioTurmaVO horarioTurmaVO) throws Exception;
    void diminuirCreditoTreinoNaoComparecimento(ContratoVO contratoVO, Date dataOperacao, String observacao, Integer codigoCliente, HorarioTurmaVO horarioTurmaFalta, SituacaoClienteSinteticoDW situacaoClienteSinteticoDW)throws Exception;
    void diminuirCreditoTreinoNaoComparecimentoSemCommit(ContratoVO contratoVO, Date dataOperacao, String observacao, Integer codigoCliente, HorarioTurmaVO horarioTurmaFalta, SituacaoClienteSinteticoDW situacaoClienteSinteticoDW)throws Exception;
    void verificarNaoComparecimentoAulaCreditoTreino(Date diaProcessamentoRobo)throws Exception;
    List<ControleCreditoTreinoVO> consultarSimples(Integer matricula, Date data, int nivelMontarDados)throws Exception;
    ControleCreditoTreinoVO consultarOperacaoTransferenciaSaldo(Integer codigoContrato, boolean retiradaSaldo, int nivelMontarDados)throws Exception;
    void diminuirCreditoPorAcesso(String key, SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO, AcessoClienteVO acessoClienteVOe)throws Exception;
    HorarioTurma getHorarioTurmaDao() throws Exception;
    Usuario getUsuarioDao() throws Exception;
    SituacaoClienteSinteticoDW getSituacaoClienteSinteticoDWDao()throws Exception;
    AulaDesmarcada getAulaDesmarcadaDao() throws Exception ;
    ContratoModalidadeHorarioTurma getContratoModalidadeHorarioTurmaDao() throws Exception;
    Reposicao getReposicaoDao() throws Exception;
    Contrato getContratoDao()throws Exception;
    Integer consultarTotalOperacao(Integer codigoContrato, TipoOperacaoCreditoTreinoEnum tipoOperacaoCreditoTreinoEnum)throws Exception;
    List<ControleCreditoTreinoVO> consultar(Integer codigoContrato, TipoOperacaoCreditoTreinoEnum tipoOperacaoCreditoTreinoEnum, Date dataInicioOperacao, Date dataFimOperacao, int nivelMontarDados)throws Exception;
    public void zerarPorCancelamento(Integer codigoCliente, ContratoVO contratoVO) throws Exception;

    boolean existeControleCredito(Integer horarioTurma) throws Exception;
    void atualizarUtilizacaoParaFaltaExclusaoAcesso(AcessoClienteVO acessoVO,String observacao, boolean forcarExclusao)throws Exception;

    Integer consultarUtilizacaoCreditoPorContrato(Integer codigoContrato) throws Exception;

    Integer saldoVirtualAluno(Integer matricula, Integer contrato);

    Integer consultarSaldoCredito(Integer codigoContrato) throws Exception;

    boolean existeDescontoParaAulaDesmarcada(AulaDesmarcadaVO aulaDesmarcadaVO) throws Exception;

    JSONArray validarCreditos(boolean debitar) throws Exception;

    void descontarCreditoAulaMarcada(Integer codigoCliente, Integer codigoContrato, AlunoHorarioTurmaVO alunoHorarioTurmaVO, String nomeModalidade) throws Exception;

    boolean descontouCreditoAoMarcarAula(Integer codigoCliente, HorarioTurmaVO horarioTurmaVO, Date data) throws Exception;

    void atualizarControleCreditoTreinoAulaColetivaDesmarcada(Integer codigoCliente, Integer codigoHorarioTurma, Date data, UsuarioVO usuarioVO);

    public Integer consultarQuantosCreditosOAlunoPodeTransferir(Integer codigoContrato) throws Exception;

}
