package negocio.interfaces.contrato;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.plano.AlunoHorarioTurmaVO;
import org.json.JSONArray;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.EstornoMovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.PeriodoMensal;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoMesVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoProdutoMesVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoProdutoVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoTipoProdutoVO;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface MovProdutoInterfaceFacade extends SuperInterface {

    public MovProdutoVO novo() throws Exception;

    public void incluir(MovProdutoVO obj) throws Exception;

    public void alterar(MovProdutoVO obj) throws Exception;

    public void excluir(MovProdutoVO obj) throws Exception;

    public MovProdutoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorDescricaoProduto(String valorConsulta, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorCodigoContrato(Integer valorConsulta, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorCodigoContrato(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List<MovProdutoVO> consultarEmAbertoPorContrato(Integer codigoContrato, int nivelMontarDados, boolean verificarAcesso) throws Exception;

    public List consultarPorNomePessoa(String valorConsulta, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception;

    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados, Integer empresa) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List consultarPorPeriodoLancamentoETipoProduto(Date dataInicio, Date dataTermino, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorPeriodoLancamentoEEmpresa(Date dataInicio, Date dataTermino, int idEmpresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    FaturamentoSinteticoProdutoMesVO consultarFaturamentoSinteticoResumoPessoa(Date dataInicial, Date dataFinal,
                                                                               List<String> tipoProduto, int operador, int colaborador, Integer empresa,
                                                                               Integer duracao, Integer plano, Integer produto,
                                                                               String nomePlano, String nomeProduto, String descricaoProduto,
                                                                               boolean somenteFaturamentoRecebido, boolean bolsa,
                                                                               boolean desconsiderarProdutoCancelado, boolean somenteContratoVendaAvulsaMesmoMesReferencia, EmpresaVO empresaLogado,
                                                                               boolean evento ) throws Exception;

    List<FaturamentoSinteticoProdutoVO> consultarProdutosMovimentadoParaFaturaPeriodo(Date dataInicial, Date dataFinal, List<String> tipoProduto,
                                                                                             int operador, int colaborador, Integer empresa,
                                                                                             String agrupamentoPlano, boolean controlarAcesso,
                                                                                             boolean somenteFaturamentoRecebido,
                                                                                             int nivelMontarDados, boolean bolsa,
                                                                                             boolean desconsiderarProdutoCancelado, boolean somenteContratoVendaAvulsaMesmoMesReferencia,boolean evento) throws Exception;

    public CompetenciaSinteticoProdutoMesVO consultarCompetenciaSinteticoResumoPessoa(String mesreferencia, List<String> tipoProduto, Integer empresa, Integer duracao, Integer plano, Integer produto, String nomePlano, String nomeProduto) throws Exception;

    public List<CompetenciaSinteticoProdutoVO> consultarProdutosMovimentadoParaCompetenciaPeriodo(Date dataInicial,
                                                                                                  Date dataFinal,
                                                                                                  List<String> tipoProduto,
                                                                                                  Integer empresa,
                                                                                                  String agrupamentoPlano,
                                                                                                  boolean controlarAcesso,
                                                                                                  int nivelMontarDados,
                                                                                                  Boolean desconsiderarAlunosInadimplentes) throws Exception;

    public List consultarProdutoComValidadePorCodigoPessoa(Integer codigo, int nivelMontarDados) throws Exception;

    public void estornarMovProduto(EstornoMovProdutoVO obj, MovParcelaVO parcela, String key) throws Exception;

    public void estornarMovProduto(EstornoMovProdutoVO obj, MovParcelaVO parcela, String key, boolean controlarTransacao) throws Exception;

    public void alterarSomenteSituacaoSemCommit(Integer codigo, String situacao) throws Exception;

    public List consultarPorCodigoParcela(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public Double cosultarValorAbertoContratoAteEstaData(Integer codigoContrato)
            throws Exception;

    public void incluirMovProdutoContratos(ContratoVO contratoPrm, List objetos) throws Exception;

    public void alterarSemCommit(MovProdutoVO obj) throws Exception;

    public List<MovProdutoVO> consultarPorCodigoPessoaParaHistoricoCompras(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorPessoaContratoParaHistoricoCompras(Integer valorConsulta, int contrato, int nivelMontarDados) throws Exception;

    public void incluirSemCommit(MovProdutoVO obj) throws Exception;

    public void incluirSemValidar(MovProdutoVO obj) throws Exception;

    public void excluirSemCommit(MovProdutoVO obj) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>codigo</code> da classe <code>Contrato</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoContratoTipoProduto(Integer valorConsulta, String tipoProduto, boolean incluirParcelasCanceladas, int nivelMontarDados) throws Exception;
    public List consultarPorCodigoContratoTipoProduto(Integer valorConsulta, String tipoProduto, int nivelMontarDados) throws Exception;

    public boolean produtoDentroDaValidade(Integer Codigoproduto, Integer codigoPessoa) throws Exception;

    public List consultarPorNomeConsumidorParaHistoricoCompras(String nomeComprador, int nivelMontarDados) throws Exception;

    public List consultarPaginado(ControleConsulta controleConsulta, boolean controlarAcesso, int nivelMontarDados, ConfPaginacao confPaginacao) throws Exception;

    public MovProdutoVO consultarProdutoPlanoPorContrato(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List<MovProdutoVO> consultarProdutoContrato(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public Double obterValorProdutoMatriculaContratoPessoa(Integer pessoa, Integer contrato) throws Exception;

    public void obterValorParcialmentPago(MovProdutoVO produto) throws Exception;

    public Double obterValorProdutoRematriculaContratoPessoa(Integer pessoa, Integer contrato) throws Exception;

    public List<MovProdutoVO> consultar(String sql, int nivelMontarDados) throws Exception;

    public boolean consultarSeExisteSaldoDevedorCCEmAberto(int codigoPessoa, int codigoEmpresa) throws SQLException;

    public void alterarNaoGerarMensagem(MovProdutoVO movProduto, boolean valor) throws Exception;

    public void alterarNaoGerarMensagem(ClienteMensagemVO clienteMensagem, boolean valor) throws Exception;

    public ResultSet contarQuantidadeEValorProdutosVencidos(int empresa) throws Exception;

    public ResultSet consultarClientesProdutosVencidos(int empresa,Date dataBaseInicio, Date dataBase) throws Exception;

    public List consultarPorCodigoContratoOrdenado(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public boolean consultarPorCodigoProduto(int produto) throws Exception;

    public void atualizarDescricaoMovProdutoModalidade(Integer contrato) throws Exception;

    public List<MovProdutoVO> consultarPorMovPagamentoContaCorrente(String movpagamento, int nivelMontarDados) throws Exception;

    public void estornarMovProdutoSemCommit(EstornoMovProdutoVO obj, String key) throws Exception;

    public String consultarJSON(String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar,
                                Date dtInicio, Date dtFim, Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa, java.sql.Date inicio, java.sql.Date fim, String filtroData) throws Exception;

    public void consultaFaturamentoRecebido(Integer empresa,
            List<FaturamentoSinteticoTipoProdutoVO> listaTipoProdutoVO, List<PeriodoMensal> periodos,
            Map<String, Boolean> mapaTipoProduto, FaturamentoSinteticoProdutoVO totalGeral,String agrupamento,
            Integer operador, Integer consultor, boolean contaCorrente) throws Exception;

    void consultaFaturamentoRecebidoComChequeDevolvido(Integer empresa, List<FaturamentoSinteticoTipoProdutoVO> listaTipoProdutoVO,
                                                       List<PeriodoMensal> periodos, Map<String, Boolean> mapaTipoProduto,
                                                       FaturamentoSinteticoProdutoVO totalGeral,String agrupamento,
                                                       Integer operador, Integer consultor, boolean contaCorrente, boolean incluirEdicaoPagamento, boolean incluirDevolucaoCheque) throws Exception;

    void consultaFaturamentoRecebidoComChequeDevolvido(Integer empresa, List<FaturamentoSinteticoTipoProdutoVO> listaTipoProdutoVO,
                                                       List<PeriodoMensal> periodos, Map<String, Boolean> mapaTipoProduto,
                                                       FaturamentoSinteticoProdutoVO totalGeral,String agrupamento,
                                                       Integer operador, Integer consultor, boolean contaCorrente, boolean incluirEdicaoPagamento, boolean incluirDevolucaoCheque, Map<String, Integer> filtroFaturamentoRecebido) throws Exception;

    public List<MovProdutoVO> consultarProdutoPorPessoaDataCompra(Integer codigo, Integer codigoProduto, Date hoje, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoParcelaAgrupado(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public Integer consultarPorCodigoVendaAvulsaRetornaCodigo(Integer valorConsulta) throws Exception;
    
    public MovProdutoVO consultarPorVendaAvulsa(Integer vendaavulsa) throws Exception;

    public MovProdutoVO getMontarMovProdutoPorTipo(String descricaoTipo, String codigoTipo, Double valor, PessoaVO pessoaVO, ContratoVO contratoVO, EmpresaVO empresaVO, UsuarioVO usuarioVO ) throws Exception;

    public void excluirSemCommit(Integer codigo) throws Exception;

    MovProdutoVO consultaMovProdutoEmAberto(TipoProduto tipoProduto,Integer codigoContrato, int nivelMontarDados)throws Exception;
    
    public List<MovProdutoVO> consultarTelaCliente(Integer pessoa,Integer contrato, Integer limit,Integer offset) throws Exception;

    public Integer obterCountProdutosCliente(Integer pessoa,Integer contrato) throws Exception;

    public List<MovProdutoVO> consultarPorMovPagamento(Integer codigoMovPagamento, int nivelMontarDados) throws Exception;

    public Integer incluirLista(List<MovProdutoVO> lista) throws Exception;

    MovProdutoVO criarMovProdutoMultaJuros(MovParcelaVO movParcelaAtrasada, VendaAvulsaVO vendaAvulsaVO, double multa, double juros, UsuarioVO usuarioVO, Double multiplicadorMultaJuros) throws Exception;
    
    public List consultarPorVendidoJuntoAoContratoOrdenado(Integer codigoContrato, boolean incluirParcelaAnuidade, int nivelMontarDados) throws Exception ;

    public void alterarDevolucaoCheque(ChequeVO chequeVO)throws Exception;

    public JSONArray consultarProdutosVigenciaCliente(Integer cliente, Integer produto, Boolean retornarQuantidade) throws Exception;

    public List<DescontoTO> consultarDescontos(Date inicio, Date fim, Integer empresa, List<ColaboradorVO> consultores) throws SQLException;

    public Double consultarTotalDescontos(Date inicio, Date fim, Integer empresa, List<ColaboradorVO> consultores) throws SQLException;
    
    public boolean existeProdutoPagoDevolucaoCheque(int codigoCheque) throws SQLException;

    public boolean alterarValorFaturado() throws Exception;

    void alterarMultaJurosNaoRecebidos(MovProdutoVO movProdutoVO) throws SQLException;

    Double obterValorCheioProdutoMatriculaContratoPessoa(Integer pessoa, Integer contrato, boolean buscarValorDesconto) throws SQLException;

    Double obterValorCheioProdutoRematriculaContratoPessoa(Integer pessoa, Integer contrato, boolean buscarValorDesconto) throws SQLException;

    Double obterValorDescontoAnuidadeRecorrenciaContratoPessoa(Integer pessoa, Integer contrato, boolean buscarValorFinal) throws SQLException;

    /**
     * Verifica se o aluno tem produto do tipo 'DS - Desafio', pago e vigente para poder marcar aulas no Aula cheia.
     * @param alunoHorario
     * @return True ou False
     * @throws SQLException
     */
    boolean verificarDesafioVigente(AlunoHorarioTurmaVO alunoHorario) throws Exception;

    void incluirListaMovProdutos(List<MovProdutoVO> lista) throws Exception;

    MovProdutoVO consultarAvaliacaoVigente(Integer codCliente, Date dataAvaliacao, int nivelMontarDados) throws Exception;

    List<MovProdutoVO> consultarProdutoParaRenovacaoAutomatica(Date dataAvaliar, Integer empresa, int nivelMontarDados) throws Exception;

    void alterarPermiteRenovacaoAutomaticaMovProduto(MovProdutoVO movProdutoVO, boolean renovarAutomaticamente, UsuarioVO usuarioVO) throws Exception;

    public List<String[]> consultarParaInsertCartaoCreditoProdutosDiariaImportacao() throws Exception;

    public boolean existeMovprodutoPorProdutoAPartirDe(Integer codProduto, Date data) throws Exception;

}
