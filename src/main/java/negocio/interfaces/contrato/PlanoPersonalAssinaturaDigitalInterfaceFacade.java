package negocio.interfaces.contrato;

import negocio.comuns.contrato.PlanoPersonalAssinaturaDigitalVO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface PlanoPersonalAssinaturaDigitalInterfaceFacade extends SuperInterface {

    public void atualizaPlanoPersonalParaAssinar() throws Exception;

    public void incluir(PlanoPersonalAssinaturaDigitalVO obj) throws Exception;

    public void excluir(PlanoPersonalAssinaturaDigitalVO obj) throws Exception;

    public void alterar(PlanoPersonalAssinaturaDigitalVO obj) throws Exception;

    public List<ControleTaxaPersonalVO> consultarPlanos(boolean assinados, String filtro, Integer empresa, boolean todos) throws Exception;

    public Integer countPlanos(boolean assinados, String filtro, Integer empresa) throws Exception;

    public Integer countPlanosAtivosSemAssinatura(Integer codigoCliente, Integer empresa) throws Exception;

    public String obterPorPlano(Integer contrato) throws Exception;

    public PlanoPersonalAssinaturaDigitalVO consultarPorPlano(Integer contrato) throws Exception;

    PlanoPersonalAssinaturaDigitalVO consultarPorCodigoPlano(Integer contrato) throws Exception;

    public void excluirPorPlano(Integer contrato) throws Exception;

    public List<PlanoPersonalAssinaturaDigitalVO> consultarTodosPlanosDigitais(Integer montarDados, boolean assinados) throws Exception;

    void excluirAssinaturaPersonal(Integer codigoTaxa) throws Exception;
}
