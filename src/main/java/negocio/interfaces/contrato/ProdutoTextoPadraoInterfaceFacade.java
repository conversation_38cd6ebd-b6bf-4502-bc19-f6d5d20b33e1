package negocio.interfaces.contrato;

import negocio.comuns.financeiro.ProdutoTextoPadraoVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ProdutoTextoPadraoInterfaceFacade extends SuperInterface {

    ProdutoTextoPadraoVO novo() throws Exception;

    ProdutoTextoPadraoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    ProdutoTextoPadraoVO consultarPorProdutoEVenda(Integer codigoProduto, Integer codigoVenda, int nivelMontarDados) throws Exception;

    ProdutoTextoPadraoVO consultarPorProdutoEAulaAvulsaDiaria(Integer codigoProduto, Integer codigoVendaDiaria, int nivelMontarDados) throws Exception;

    void setIdEntidade(String aIdEntidade);

    void alterarAssinaturaHtmlContratoProduto(Integer codigoContrato) throws Exception;
}
