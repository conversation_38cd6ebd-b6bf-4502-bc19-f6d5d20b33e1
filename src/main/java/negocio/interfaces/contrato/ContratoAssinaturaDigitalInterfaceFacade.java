/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.contrato;

import negocio.comuns.contrato.CancelamentoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface ContratoAssinaturaDigitalInterfaceFacade extends SuperInterface {
    
    void incluir(ContratoAssinaturaDigitalVO obj) throws Exception;
    
    void excluir(ContratoAssinaturaDigitalVO obj) throws Exception;
    
    void alterar(ContratoAssinaturaDigitalVO obj) throws Exception;

    void alterarSomenteAnexoCancelamento(ContratoAssinaturaDigitalVO obj) throws Exception;

    void alterarSomenteAssinaturaContrato(ContratoAssinaturaDigitalVO obj) throws Exception;
    
    List<ContratoVO> consultarContratos(boolean assinados, String filtro, Integer empresa, boolean todos, Boolean contratosCancelados) throws Exception;
    
    Integer countContratos(boolean assinados, String filtro, Integer empresa, Boolean contratosCancelados) throws Exception;

    Integer countContratosAtivosSemAssinatura(Integer codigoCliente) throws Exception;

    Integer countContratosAditivosAtivosSemAssinatura(Integer codigoCliente) throws Exception;

    boolean consultaTermoAceiteAssinadoPorAluno(Integer codigoCliente) throws Exception;
    
    String obterPorContrato(Integer contrato, boolean assinatura2) throws Exception;
    
    ContratoAssinaturaDigitalVO consultarPorContrato(Integer contrato) throws Exception;

    ContratoAssinaturaDigitalVO consultarPorContratoEAditivo(Integer contrato, Integer aditivo) throws Exception;

    CancelamentoAssinaturaDigitalVO consultarPorContratoCancelamento(Integer contrato) throws Exception;

    ContratoAssinaturaDigitalVO consultarPorCodigoContrato(Integer contrato) throws Exception;

    void excluirPorContrato(Integer contrato) throws Exception;

    List<ContratoAssinaturaDigitalVO> consultarTodosContratosDigitais(Integer montarDados, boolean assinados) throws Exception;

    List<ContratoVO> consultarClientesParQ(boolean assinados, String filtro, Integer empresa, boolean todos) throws Exception;
    List<ContratoVO> consultarTermoResponsabilidade(boolean assinados, String filtro, Integer empresa, boolean todos, boolean isTermoResponsabilidadeExAluno) throws Exception;
    Integer consultarQtdTermoResponsabilidade(boolean assinados, String filtro, Integer empresa, boolean isTermoResponsabilidadeExAluno) throws Exception;
    void excluirAssinaturaPeloContrato(Integer contrato) throws Exception;
    void excluirAssinaturaPeloContratoEAditivo(Integer contrato, Integer aditivo) throws Exception;
    void excluirAssinatura2PeloContrato(Integer contrato) throws Exception;
    void excluirAssinaturaEletronicaContrato(Integer contrato) throws Exception;
}
