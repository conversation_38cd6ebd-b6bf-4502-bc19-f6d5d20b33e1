package negocio.interfaces.contrato;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoTextoPadraoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ContratoTextoPadraoInterfaceFacade extends SuperInterface {

    ContratoTextoPadraoVO novo() throws Exception;

    void incluir(ContratoTextoPadraoVO obj) throws Exception;

    void alterar(ContratoTextoPadraoVO obj) throws Exception;

    void excluir(ContratoTextoPadraoVO obj) throws Exception;

    ContratoTextoPadraoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    void setIdEntidade(String aIdEntidade);

    ContratoTextoPadraoVO consultarContratoTextoPadrao(Integer contrato, int nivelMontarDados) throws Exception;

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoTextoPadraoVO</code>
     * através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    ContratoTextoPadraoVO consultarPorCodigoContrato(Integer codigoPrm, int nivelMontarDados) throws Exception;

    String gravarHtmlContrato(Integer codigoContrato, UsuarioVO usuarioVO, Integer aditivo) throws Exception;

    String gravarHtmlContrato(Integer codigoContrato, UsuarioVO usuarioVO) throws Exception;

    String consultarHtmlContrato(Integer codigoContrato, boolean comBotaoImprimir) throws Exception;

    String consultarHtmlContrato(Integer codigoContrato, boolean comBotaoImprimir, boolean semTagAssinaturaVazia) throws Exception;

    String consultarHtmlContrato(Integer codigoContrato, boolean comBotaoImprimir,
                                 boolean semTagAssinaturaVazia, boolean telaNova, Integer aditivo) throws Exception;

    void alterarAssinaturaHtmlContrato(Integer codigoContrato) throws Exception;

    void alterarTextoPadraoContrato(Integer codigoContrato, Integer codigoPlanoTextoPadrao) throws Exception;

    void anularContratosHtml() throws Exception;

    String montarContratoAditivoAssinaturaConfirmacao(String texto, Integer contrato) throws Exception;
}
