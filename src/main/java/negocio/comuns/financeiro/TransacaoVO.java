/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.integracao.pactopay.dto.RemessaDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.TransacaoDTO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.InformacaoErroTransacao;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.stone.StoneRetornoEnum;
import servicos.impl.stone.TransacaoStoneOnlineVO;
import servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static negocio.comuns.utilitarias.Uteis.voltarDiasAPartirHoje;

/**
 *
 * <AUTHOR>
 */
public class TransacaoVO extends SuperVO {

    private SituacaoTransacaoEnum situacao;
    private String codigoExterno = "";
    private String codigoExterno2 = "";
    private Long gateway_id;
    private Date dataProcessamento = Calendario.hoje();
    private Integer reciboPagamento = 0;
    private Double valor = 0.0;
    private String paramsEnvio = "";
    private String paramsResposta = "";
    private String paramsEnvioPessoa = "";
    private String paramsRespostaPessoa = "";
    private String resultadoCaptura = "";
    private String resultadoCancelamento = "";
    private TipoTransacaoEnum tipo;
    private TipoTransacaoEnum tipoOrigem;
    private OrigemCobrancaEnum origem;
    private List<MovParcelaVO> listaParcelas = new ArrayList();
    private Integer movPagamento = 0;
    private String nomePessoa = "";
    private UsuarioVO usuarioResponsavel;
    private String urlTransiente = "";
    private Date dataHoraCancelamento;
    private String logEstorno = "";
    private Integer empresa = 0;
    private String ipTransiente = "";
    private InformacaoErroTransacao informacaoErro;
    private String valorCodigoExterno;
    private String valorCartaoMascarado;
    private String valorUltimaTransacaoAprovada;
    private Boolean permiteRepescagem = Boolean.FALSE;
    private PessoaVO pessoaPagador;
    private Boolean aguardandoConfirmacao = Boolean.FALSE;
    private boolean processarNovamente = false;
    private br.com.pactosolucoes.comuns.to.CartaoCreditoTO cartaoCreditoNovo;
    private String orderid;
    private String codigoAutorizacao;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private Date dataCobranca; // atributo transient
    private boolean pagamentoDinheiro = false; // atributo transient
    private OperadorasExternasAprovaFacilEnum bandeiraPagamento; // atributo transient
    private OperadorasExternasAprovaFacilEnum bandeiraEnum; // atributo transient
    private Integer nrVezes; // atributo transient
    private EmpresaVO empresaVO;
    private String outrasInformacoes;
    private String erroProcessamento;
    private String urlComprovanteCancelamento; // atributo transient
    private boolean contabilizadaMultiplosConvenios = false;
    private String tokenAragorn;
    private String codigoRetorno;
    private AmbienteEnum ambiente;
    private boolean ignorarSituacaoCancelamento = false;
    private CartaoCreditoTO cartaoCreditoTO;
    private Integer qtdParcelasTransacao;
    @NaoControlarLogAlteracao
    private UsuarioVO usuarioResponsavelCancelamento;
    private Date dataAtualizacao;
    private Date proximaTentativa;
    @NaoControlarLogAlteracao
    private String origemSincronizacao;
    private boolean transacaoVerificarCartao = false;
    @NaoControlarLogAlteracao
    private boolean atualizarParamsResposta = false;
    @NaoControlarLogAlteracao
    private boolean utilizarUsuarioGerarRecibo = false;
    private String codigoRetornoDescricao;
    @NaoControlarLogAlteracao
    private String idCartaoPinBank;
    private boolean verificador = false;
    @NaoControlarLogAlteracao
    private boolean selecionada = false;
    @NaoControlarLogAlteracao
    private boolean compensado = false;
    @NaoControlarLogAlteracao
    private Date dtCompensacao;
    @NaoControlarLogAlteracao
    private ClienteVO clienteVO;
    @NaoControlarLogAlteracao
    private Double desconto;
    private String codigoNSU;
    @NaoControlarLogAlteracao
    private String nsuTransacao;
    @NaoControlarLogAlteracao
    private boolean errorExisteTransacaoRecenteParaMovParcela = false;
    @NaoControlarLogAlteracao
    private boolean errorMovParcelaDaTransacaoNaoEstaEmAberto = false;
    private boolean erroIncluirPessoa = false;
    private boolean erroAtualizarPessoa = false;


    public String getDescricaoParcelas(){
        if(UteisValidacao.emptyList(listaParcelas)){
            return "";
        }
        String desc = "";
        for(MovParcelaVO mm : listaParcelas){
            desc += mm.getDescricao() + "<br/>";
        }
        return desc;
    }

    public String getDescricaoParcelasTitle(){
        if(UteisValidacao.emptyList(listaParcelas)){
            return "";
        }
        String desc = "";
        for(MovParcelaVO mm : listaParcelas){
            desc += ("Pessoa: " + mm.getPessoa().getNome() + " | Parcela: " + mm.getCodigo() + " - " + mm.getDescricao() + "<br/>");
        }
        return desc;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public String getCodigoExternoExibir() {
        StringBuilder sb = new StringBuilder();
        sb.append("<b>Código Interno: </b>").append(codigo);
        if (!UteisValidacao.emptyString(codigoExterno)) {
            if (getTipo().equals(TipoTransacaoEnum.DCC_STONE_ONLINE_V5)) {
                sb.append("<br/><b>Order ID: </b>").append(codigoExterno);
            } else {
                sb.append("<br/><b>Código Externo: </b>").append(codigoExterno);
            }
        }
        if (!UteisValidacao.emptyString(codigoExterno2)) {
            if (getTipo().equals(TipoTransacaoEnum.DCC_STONE_ONLINE_V5)) {
                sb.append("<br/><b>Charge ID: </b>").append(codigoExterno2);
            } else {
                sb.append("<br/><b>Código Externo 2: </b>").append(codigoExterno2);
            }
        }
        if (gateway_id != null && !UteisValidacao.emptyNumber(gateway_id)) {
            sb.append("<br/><b>Gateway ID: </b>").append(gateway_id);
        }
        if (UteisValidacao.emptyString(sb.toString())) {
            return "";
        }
        return sb.toString();
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public Date getDataProcessamento() {
        return dataProcessamento;
    }

    public String getDataProcessamento_Apresentar() {
        return Uteis.getDataComHora(dataProcessamento);
    }

    public void setDataProcessamento(Date dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    public String getParamsEnvio() {
        return paramsEnvio;
    }

    public void setParamsEnvio(String paramsEnvio) {
        this.paramsEnvio = paramsEnvio;
    }

    public String getParamsResposta() {
        return paramsResposta;
    }

    public void setParamsResposta(String paramsResposta) {
        this.paramsResposta = paramsResposta;
    }

    public SituacaoTransacaoEnum getSituacao() {
        if (situacao == null) {
            situacao = SituacaoTransacaoEnum.NENHUMA;
        }
        return situacao;
    }
    public String getSituacao_Apresentar() {
        return situacao.name();
    }

    public String getSituacao_Title() {
        if (getSituacao().equals(SituacaoTransacaoEnum.APROVADA) &&
                getTipo().equals(TipoTransacaoEnum.VINDI)) {

            StringBuilder title = new StringBuilder();
            title.append("Aguardando retorno do processamento na Vindi");

            String nrTentativas = nrTentativas();
            if (!UteisValidacao.emptyString(nrTentativas)) {

                title.append("<br/><br/>Nr. tentativas: <b>").append(nrTentativas).append("</b>");

                String dataProximaTentativa = dataProximaTentativa();
                if (!UteisValidacao.emptyString(dataProximaTentativa)) {
                    title.append("<br/>Próxima tentativa na Vindi: <b>").append(dataProximaTentativa).append("</b>");
                    title.append("<br/><br/><b>Obs.:</b> O Sistema Pacto consulta a situação da cobrança<br/>sempre um dia após a data da próxima tentativa.");
                }
            }
            return title.toString();
        } else if (getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && getDataHoraCancelamento() != null) {
            return getSituacao().getHint() + "<br/><br/>Data Cancelamento: <b>" + getDataHoraCancelamento_Apresentar() + "</b>";
        } else {
            return getSituacao().getHint();
        }
    }

    public void setSituacao(SituacaoTransacaoEnum situacao) {
        this.situacao = situacao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(Integer reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getUrlTransiente() {
        return urlTransiente;
    }

    public void setUrlTransiente(String urlTransiente) {
        this.urlTransiente = urlTransiente;
    }

    public static void validarDados(TransacaoVO transacaoVO) throws ConsistirException {
        if (transacaoVO.getValidarDados()) {

            if (transacaoVO.getTipo() == TipoTransacaoEnum.NENHUMA) {
                throw new ConsistirException("Deve ser informado um TipoTransacaoEnum.");
            }
            if (transacaoVO.getTipo() == TipoTransacaoEnum.NENHUMA) {
                throw new ConsistirException("Deve ser informado um TipoTransacaoEnum Original.");
            }

            if (!transacaoVO.isTransacaoVerificarCartao() &&
                    transacaoVO.getListaParcelas() != null
                    && transacaoVO.getListaParcelas().isEmpty()
                    && transacaoVO.getCodigo() == 0) {
                throw new ConsistirException("Transacao deve possuir MovParcelas relacionadas.");
            }
            if (transacaoVO.getEmpresa() == 0) {
                throw new ConsistirException("Transacao deve possuir uma Empresa relacionada.");
            }
        }
    }

    public TipoTransacaoEnum getTipo() {
        if (tipo == null) {
            tipo = TipoTransacaoEnum.NENHUMA;
        }
        return tipo;
    }

    public void setTipo(TipoTransacaoEnum tipo) {
        this.tipo = tipo;
    }

    public List<MovParcelaVO> getListaParcelas() {
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public String getValorAtributoEnvio(String nomeAtributo) throws Exception {

        Map<String, String> params = Uteis.obterMapFromString(this.getParamsEnvio());
        String s = params.get(nomeAtributo);
        return (s != null ? s : "");
    }

    public String getValorAtributoResposta(String nomeAtributo) throws Exception {
        return Uteis.getValorTAG(this.getParamsResposta(), nomeAtributo);
    }

    public String getValorAtributoCancelamento(String nomeAtributo) throws Exception {
        if (this.getResultadoCancelamento() != null) {
            return Uteis.getValorTAG(this.getResultadoCancelamento(), nomeAtributo);
        } else {
            return "";
        }
    }

    public String getValorAtributoCaptura(String nomeAtributo) throws Exception {
        return Uteis.getValorTAG(this.getResultadoCaptura(), nomeAtributo);
    }

    public Integer getMovPagamento() {
        return movPagamento;
    }

    public void setMovPagamento(Integer movPagamento) {
        this.movPagamento = movPagamento;
    }

    public String getResultadoCaptura() {
        return resultadoCaptura;
    }

    public void setResultadoCaptura(String resultadoCaptura) {
        this.resultadoCaptura = resultadoCaptura;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetario(valor);
    }

    public UsuarioVO getUsuarioResponsavel() {
        if (usuarioResponsavel == null) {
            usuarioResponsavel = new UsuarioVO();
        }
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public String getUsuario_Apresentar(){
        return getUsuarioResponsavel().getNome();
    }

    public String getIpTransiente() {
        return ipTransiente;
    }

    public void setIpTransiente(String ipTransiente) {
        this.ipTransiente = ipTransiente;
    }

    /**
     *
     * @return:
     * TRUE:
     *      1. Existem parcelas E;
     *      2. Essas parcelas estão todas em aberto;
     * FALSE:
     *      1. Não possui parcelas relacionadas OU;
     *      2. Pelo menos uma possui situação DIFERENTE de 'EA';
     */
    private boolean existeParcelaEmRemessa() {
        List<MovParcelaVO> parcelas = this.getListaParcelas();
        if (parcelas != null) {
            for (MovParcelaVO parc : parcelas) {
                if (parc.isEmRemessaAindaNaoProcessada()) {
                    return true;
                }
            }
        }
        return false;
    }
    
    private boolean todasParcelasEmAberto() {
        List<MovParcelaVO> parcelas = this.getListaParcelas();
        boolean todasParcelasAbertas = false;
        if (parcelas != null) {
            todasParcelasAbertas = true;
            for (MovParcelaVO parc : parcelas) {
                if (!parc.getSituacao().equals("EA")) {
                    todasParcelasAbertas = false;
                    break;
                }
            }
        }
        return todasParcelasAbertas;
    }

    /**
     * Permite retransmitir a transação apenas se:
     * 1. NÃO possui nenhuma parcela quitada;
     * 2. A situação é igual a NAO_APROVADA
     * @return
     */
    public boolean isPermiteRetransmitir() {
        return this.todasParcelasEmAberto() && !existeParcelaEmRemessa() &&  (!this.getListaParcelas().isEmpty())
                && this.getSituacao() == SituacaoTransacaoEnum.NAO_APROVADA
                && this.getTipo().equals (TipoTransacaoEnum.AprovaFacilCB);
    }

    /**
     * Permite confirmar a transação apenas se:
     * 1. A situação é igual a APROVADA ou ERRO_CAPTURA;
     * 2. TODAS as parcelas estão em aberto ainda;
     * @return
     */
    public boolean isPermiteConfirmar() {
        return (this.getSituacao() == SituacaoTransacaoEnum.APROVADA
                || this.getSituacao() == SituacaoTransacaoEnum.ERRO_CAPTURA)
                && this.todasParcelasEmAberto() && existeParcelaEmRemessa() && this.getTipo().equals(TipoTransacaoEnum.AprovaFacilCB);
    }

    /**
     * Permite descartar a transação apenas se:
     * A situação é igual a: COM_ERRO, APROVADA, NAO_APROVADA ou ERRO_CAPTURA
     * @return
     */
    public boolean isPermiteDescartar() {
        if (!this.getTipo().equals (TipoTransacaoEnum.AprovaFacilCB)) {
            return false;
        }

        switch (this.getSituacao()) {
            case COM_ERRO:
                return true;
            case APROVADA:
                return true;
            case NAO_APROVADA:
                return true;
            case ERRO_CAPTURA:
                return false;
        }
        return false;
    }

    /**
     * Possui testes unitários, veja em na classe <b>TransacaoVOTest</b>.
     */
    public boolean isPermiteCancelar() {
        return isPermiteCancelarPeloTipo() && isPermiteCancelarPeloStatus() && isPermiteCancelarPeloData();
    }

    public boolean isAprovaFacil() {
        return getTipo() != null && getTipo().equals(TipoTransacaoEnum.AprovaFacilCB);
    }

    private boolean isPermiteCancelarPeloTipo() {
        switch (getTipo()) {
            case CIELO_DEBITO_ONLINE:
                return false;
            case E_REDE_DEBITO:
                return false;
            default:
                return true;
        }
    }

    private boolean isPermiteCancelarPeloStatus() {
        if (getTipo() != null && (getTipo().equals(TipoTransacaoEnum.GETNET_ONLINE) || getTipo().equals(TipoTransacaoEnum.DCC_STONE_ONLINE_V5)) && isTransacaoVerificarCartao()) {
            return false;
        }
        //Transação de Verificação Zero Dollar Stone
        if (getTipo() != null && getTipo().equals(TipoTransacaoEnum.STONE_ONLINE) && getValor() != null && UteisValidacao.emptyNumber(getValor())) {
            return false;
        }
        //Transação de Verificação Zero Dollar Rede
        if (getTipo() != null && getTipo().equals(TipoTransacaoEnum.E_REDE) && getValor() != null && UteisValidacao.emptyNumber(getValor())) {
            return false;
        }
        return getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                (getSituacao().equals(SituacaoTransacaoEnum.APROVADA) && !getTipo().equals(TipoTransacaoEnum.PAGBANK)) ||
                (getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA) && (getTipo().equals(TipoTransacaoEnum.MUNDIPAGG)
                        || getTipo().equals(TipoTransacaoEnum.PAGAR_ME) || getTipo().equals(TipoTransacaoEnum.STRIPE)));
    }

    private boolean isPermiteCancelarPeloData() {
        if (isDataProcessamentoHoje()) {
            return true;
        }

        switch (getTipo()) {
            case VINDI:
            case CIELO_ONLINE:
                return true;
            case E_REDE:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(60));
            case GETNET_ONLINE:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(30));
            case STONE_ONLINE:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(350));
            case DCC_STONE_ONLINE_V5:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(350));
            case MUNDIPAGG:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(30));
            case PAGAR_ME:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(30));
            case PAGBANK:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(30));
            case STRIPE:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(30));
            case PAGOLIVRE:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(180));
            case FACILITEPAY:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(180));
            case CEOPAG:
                return Calendario.maiorOuIgual(dataProcessamento, voltarDiasAPartirHoje(350));
            case DCC_CAIXA_ONLINE:
                return true; //Não encontramos a regra de cancelamento na documentação https://dev.softwareexpress.com.br/docs/e-sitef/cancelamento/
            default:
                return false;
        }
    }

    private boolean isDataProcessamentoHoje() {
        return Calendario.igual(Calendario.hoje(), dataProcessamento);
    }

    public String getAutorizacao() {
        try {
            String codigoAutorizacao = this.getValorAtributoResposta(APF.CodigoAutorizacao);
            if (UteisValidacao.emptyString(codigoAutorizacao)) {
                codigoAutorizacao = this.getCodigoAutorizacao();
            }
            return codigoAutorizacao;
        } catch (Exception ex) {
            return "";
        }
    }

    public String getAutorizacaoGestaoTransacaoTitle() {
        try {
            StringBuilder title = new StringBuilder();

            String autorizacao = getAutorizacao();
            if (!UteisValidacao.emptyString(autorizacao)) {
                title.append("Autorização: <b>").append(autorizacao).append("</b>");
            }

            String nsu = getNSU();
            if (!UteisValidacao.emptyString(nsu)) {
                if (title.length() > 0) {
                    title.append("<br/><br/>");
                }
                title.append("NSU: <b>").append(nsu).append("</b>");
            }

            if (title.length() == 0) {
                return "Codigo de autorização";
            }
            return title.toString();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getBandeira() {
        try {
            String bandeira = this.getValorAtributoEnvio(APF.Bandeira);
            if (UteisValidacao.emptyString(bandeira)) {
                bandeira = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira);
            }
            return bandeira;
        } catch (Exception ex) {
            return "";
        }
    }

    public String getCartaoMascarado() {
        try {
            String cardMas = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoMascarado);
            if (!UteisValidacao.emptyString(cardMas)) {
                return APF.getCartaoMascarado(cardMas);
            }
            return this.getValorAtributoResposta(APF.CartaoMascarado);
        } catch (Exception ex) {
            return "";
        }
    }

    public String getNomeUsuario() {
        return getUsuarioResponsavel().getNome() != null ? getUsuarioResponsavel().getNomeAbreviado() : "";
    }

    public String getResultadoCancelamento() {
        return resultadoCancelamento;
    }

    public void setResultadoCancelamento(String resultadoCancelamento) {
        this.resultadoCancelamento = resultadoCancelamento;
    }

    public Date getDataHoraCancelamento() {
        return dataHoraCancelamento;
    }

    public void setDataHoraCancelamento(Date dataHoraCancelamento) {
        this.dataHoraCancelamento = dataHoraCancelamento;
    }

    public String getDataHoraCancelamento_Apresentar() {
        if (dataHoraCancelamento != null) {
            return Uteis.getDataComHora(dataHoraCancelamento);
        } else {
            return "";
        }
    }

    public String getLogEstorno() {
        return logEstorno;
    }

    public void setLogEstorno(String logEstorno) {
        this.logEstorno = logEstorno;
    }

    public String getCodErroExterno() {
        try {
            //if (this.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
            String tmp = this.getValorAtributoResposta(APF.ResultSolicAprovacao);
            String src = "Não Autorizado - ";
            int ind1 = tmp.indexOf(src);
            if (ind1 != -1) {
                tmp = tmp.replace(src, "");
                tmp = tmp.substring(0, tmp.indexOf(" "));
                if (!tmp.isEmpty()) {
                    tmp = tmp.replaceAll("[^0-9]", "");
                    if (!tmp.isEmpty()) {
                        return String.valueOf(Integer.valueOf(tmp));
                    }
                }

            }
            //}
        } catch (Exception ex) {
            Logger.getLogger(TransacaoVO.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "0";
    }

    public Integer getRepescagemInt() {
        try {
            String repescagem = getRepescagem();
            return repescagem.isEmpty() ? null : Integer.valueOf(repescagem);
        } catch (Exception e) {
            return null;
        }
    }
    public String getRepescagem() {
        try {
            String tmp = this.getValorAtributoEnvio(APF.NumTentativasRepescagem);
            if (!tmp.isEmpty() && (!tmp.equals("0"))) {
                return tmp;
            }
        } catch (Exception ex) {
            Logger.getLogger(TransacaoVO.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    @Override
    public String toString() {
        return String.format("Codigo: %s \nCodigoExterno: %s \nSituacao: %s \nData: %s \nRecibo: %s \nValor: %s \nNome: %s \nUsuario: %s \nNr_Parcelas: %s \nEmpresa: %s",
                new Object[]{
                    this.codigo,
                    this.codigoExterno,
                    this.situacao,
                    this.dataProcessamento,
                    this.reciboPagamento,
                    this.valor,
                    this.nomePessoa,
                    this.usuarioResponsavel,
                    this.listaParcelas == null ? " NULO " : this.listaParcelas.size(),
                    this.empresa
                });
    }

    public InformacaoErroTransacao getInformacaoErro() {
        return informacaoErro;
    }

    public void setInformacaoErro(InformacaoErroTransacao informacaoErro) {
        this.informacaoErro = informacaoErro;
    }

    public String getValorCodigoExterno() throws Exception{
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getValorCartaoMascarado() throws Exception{
        return getValorAtributoResposta(APF.CartaoMascarado);
    }

    public String getValorUltimaTransacaoAprovada() throws Exception{
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getResultadoRequisicao() throws Exception{
        return getValorAtributoResposta("ResultadoSolicitacaoAprovacao");
    }

    public Boolean getPermiteRepescagem() {
        return permiteRepescagem;
    }

    public void setPermiteRepescagem(Boolean permiteRepescagem) {
        this.permiteRepescagem = permiteRepescagem;
    }

    public PessoaVO getPessoaPagador() {
        return pessoaPagador;
    }

    public void setPessoaPagador(PessoaVO pessoaPagador) {
        this.pessoaPagador = pessoaPagador;
    }

    public Boolean getAguardandoConfirmacao() {
        return aguardandoConfirmacao;
    }

    public void setAguardandoConfirmacao(Boolean aguardandoConfirmacao) {
        this.aguardandoConfirmacao = aguardandoConfirmacao;
    }

    public boolean isProcessarNovamente() {
        return processarNovamente;
    }

    public void setProcessarNovamente(boolean processarNovamente) {
        this.processarNovamente = processarNovamente;
    }


    public CartaoCreditoTO getCartaoCreditoNovo() {
        if (cartaoCreditoNovo == null) {
            cartaoCreditoNovo = new CartaoCreditoTO();
        }
        return cartaoCreditoNovo;
    }

    public void setCartaoCreditoNovo(CartaoCreditoTO cartaoCreditoNovo) {
        this.cartaoCreditoNovo = cartaoCreditoNovo;
    }

    public String getOrderid() {
        if (orderid == null) {
            orderid = "";
        }
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public String getCodigoAutorizacao() {
        if (codigoAutorizacao == null) {
            codigoAutorizacao = "";
        }
        return codigoAutorizacao;
    }

    public void setCodigoAutorizacao(String codigoAutorizacao) {
        this.codigoAutorizacao = codigoAutorizacao;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public Date getDataCobranca() {
        return dataCobranca;
    }

    public void setDataCobranca(Date dataCobranca) {
        this.dataCobranca = dataCobranca;
    }

    public boolean isPagamentoDinheiro() {
        return pagamentoDinheiro;
    }

    public void setPagamentoDinheiro(boolean pagamentoDinheiro) {
        this.pagamentoDinheiro = pagamentoDinheiro;
    }

    public OperadorasExternasAprovaFacilEnum getBandeiraPagamento() {
        return bandeiraPagamento;
    }

    public void setBandeiraPagamento(OperadorasExternasAprovaFacilEnum bandeiraPagamento) {
        this.bandeiraPagamento = bandeiraPagamento;
    }

    public Integer getNrVezes() {
        return nrVezes;
    }

    public void setNrVezes(Integer nrVezes) {
        this.nrVezes = nrVezes;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getOutrasInformacoes() {
        if (outrasInformacoes == null) {
            outrasInformacoes = "";
        }
        return outrasInformacoes;
    }

    public void setOutrasInformacoes(String outrasInformacoes) {
        this.outrasInformacoes = outrasInformacoes;
    }

    public String getUrlComprovanteCancelamento() {
        if (urlComprovanteCancelamento == null) {
            urlComprovanteCancelamento = "";
        }
        return urlComprovanteCancelamento;
    }

    public void setUrlComprovanteCancelamento(String urlComprovanteCancelamento) {
        this.urlComprovanteCancelamento = urlComprovanteCancelamento;
    }

    public String getNSU() {
        try {
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getAdquirente(){
        return "";
    }

    public String getTID() {
        return "";
    }

    public String getErroProcessamento() {
        return erroProcessamento;
    }

    public void setErroProcessamento(String erroProcessamento) {
        this.erroProcessamento = erroProcessamento;
    }

    public boolean isContabilizadaMultiplosConvenios() {
        return contabilizadaMultiplosConvenios;
    }

    public void setContabilizadaMultiplosConvenios(boolean contabilizadaMultiplosConvenios) {
        this.contabilizadaMultiplosConvenios = contabilizadaMultiplosConvenios;
    }

    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof TransacaoVO)) {
            return false;
        }
        if(!this.codigo.equals(((TransacaoVO) obj).getCodigo())){
            return false;
        }
        return true;
    }

    public String getTokenAragorn() {
        if (tokenAragorn == null) {
            tokenAragorn = "";
        }
        return tokenAragorn;
    }

    public void setTokenAragorn(String tokenAragorn) {
        this.tokenAragorn = tokenAragorn;
    }

    public String getCodigoRetornoGestaoTransacao() {
        String retorno = this.getCodigoRetornoGestaoTransacaoGenerico();
        if (this.getTipo().equals(TipoTransacaoEnum.FACILITEPAY) && retorno != null) {
            retorno = retorno.replaceAll("PagoLivre", "Fypay").replaceAll("Pago Livre", "Fypay");
        }
        return retorno;
    }

    private String getCodigoRetornoGestaoTransacaoGenerico() {
        try {

            String codigoRetornoPacto = getCodigoRetornoPacto();
            String codErroExterno = getCodErroExterno();
            String codigoRetorno = getCodigoRetorno();

            if ((UteisValidacao.emptyString(codErroExterno) || codErroExterno.equalsIgnoreCase("?")) && !UteisValidacao.emptyString(codigoRetornoPacto)) {
                return codigoRetornoPacto;
            }

            if (!UteisValidacao.emptyString(codErroExterno)) {
                return codErroExterno;
            }

            if (!UteisValidacao.emptyString(codigoRetorno)) {
                return codigoRetorno;
            }

            //se tovos os códigos estiverem em branco e ela estiver Concluida com sucesso
            //marcar ela como "0"
            if (UteisValidacao.emptyString(codigoRetornoPacto) &&
                    UteisValidacao.emptyString(codErroExterno) &&
                    UteisValidacao.emptyString(codigoRetorno) &&
                    getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                return "0";
            }

        } catch (Exception ex) {
            Logger.getLogger(TransacaoVO.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "?";
    }

    public String getCodigoRetornoGestaoTransacaoMotivo() {
        return getCodigoRetornoGestaoTransacaoExplicacao(false);
    }

    public String getCodigoRetornoGestaoTransacaoTooltipster() {
        return getCodigoRetornoGestaoTransacaoExplicacao(true);
    }

    private String getCodigoRetornoGestaoTransacaoExplicacao(boolean tooltipster) {
        try {

            StringBuilder title = new StringBuilder();

            String codigoRetornoPacto = getCodigoRetornoPacto();

            if (!UteisValidacao.emptyString(codigoRetornoPacto) && !this.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                CodigoRetornoPactoEnum codigoRetornoPactoEnum = CodigoRetornoPactoEnum.consultarPorCodigo(codigoRetornoPacto);
                if (codigoRetornoPactoEnum != null) {
                    title.append(codigoRetornoPactoEnum.getDescricao());
                }
            }
            if (getTipo().equals(TipoTransacaoEnum.VINDI)) {
                if (!UteisValidacao.emptyString(getCodigoExterno()) &&
                        getCodigoExterno().equals("0") &&
                        getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO)) {
                    title.append(getParamsResposta());
                }
                try {
                    if (getParamsResposta().contains("errors")) {
                        JSONObject json = new JSONObject(getParamsResposta());
                        String errors = json.optString("errors");
                        if (errors.contains("invalid_parameter") && errors.contains("payment_profile") && errors.contains("não encontrado"))
                            return "Não foi possível obter o perfil de pagamento do aluno lá na VINDI.<br> O cartão dele pode ter sido excluído lá no portal VINDI. <br>" +
                                    "Exclua a autorização de cobrança do aluno e inclua novamente.";
                    }
                } catch (Exception e) {
                    //ignored
                }
            }

            if (!UteisValidacao.emptyString(title.toString()) && title.toString().equals("Verifique o retorno da adquirente.") && getTipo().equals(TipoTransacaoEnum.GETNET_ONLINE)) {
                title = new StringBuilder();
            }

            if (title.length() == 0) {
                try {
                    title.append(getResultadoRequisicao());
                } catch (Exception ignored) {
                }
            }

            //Se for Credenciais Invalidas, já retorna a mensagem tratada
            if (!UteisValidacao.emptyString(title.toString()) && title.toString().contains("Credenciais inválidas é a causa mais provável") && getTipo().equals(TipoTransacaoEnum.GETNET_ONLINE)) {
                return title.toString();
            }

            //Se for erro de comunicação com a GetNet 502 - UnexpectedEOFAtTarget já retorna a mensagem tratada, pois pelo fluxo é preenchido com o que retorna no Json e não entra na validação para tratar mensagens do tipo
            if(!UteisValidacao.emptyString(getParamsResposta()) && getTipo().equals(TipoTransacaoEnum.GETNET_ONLINE)) {
                JSONObject obj = new JSONObject(getParamsResposta());
                if(obj != null && obj.optString("name").equals("UnexpectedEOFAtTarget")) {
                    title = new StringBuilder();
                    title.append("Houve uma instabilidade na GetNet durante esta transação. <br>" +
                            "Verifique no Portal da GetNet se a cobrança foi efetivada com sucesso. Se sim, realize a baixa manual da parcela no sistema Pacto, caso contrário tente realizar a transação novamente mais tarde!");
                    return title.toString();
                }
            }

            //Se não vier mensagem no XML, buscar mensagem no Enum pelo Código de Retorno
            if (getTipo().equals(TipoTransacaoEnum.STONE_ONLINE) && !UteisValidacao.emptyString(codigoRetorno)) {
                if(title.toString().equals("Erro no formato da mensagem")) {
                    StoneRetornoEnum stoneRetornoEnum = StoneRetornoEnum.valueOff(codigoRetorno);
                    if (!stoneRetornoEnum.equals(StoneRetornoEnum.StatusNENHUM)) {
                        title = new StringBuilder();
                        title.append(stoneRetornoEnum.getDescricao());
                    }
                }
                if(this.getCodigoRetorno().equals("?")) {
                    title = new StringBuilder();
                    title.append("Houve algum problema de comunicação com o E-Commerce da Stone. <br> " +
                            "Verifique no Portal da Stone se ocorreu a cobrança. Se sim, realizar a baixa manual da parcela no sistema Pacto e se não, tente novamente mais tarde!");
                }
            }

            if (getTipo().equals(TipoTransacaoEnum.GETNET_ONLINE) && UteisValidacao.emptyString(title.toString())) {
                if (isTransacaoVerificarCartao()) {
                    try {
                        if (getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                            title.append("Verificação do cartão aprovada");
                        } else {
                            title.append("Não foi possível verificar este cartão");
                        }
                    } catch (Exception ex) {
                    }
                } else {
                    try {
                        JSONObject obj = new JSONObject(getParamsResposta());
                        JSONArray detailsArray = new JSONArray(obj.optString("details"));
                        JSONObject details = new JSONObject(detailsArray.get(0).toString());
                        String mensagem = details.optString("description");
                        title.append(mensagem);
                    } catch (Exception ex) {
                        title.append("Erro ao obter mensagem de erro (GETNET), verifique no suporte!");
                    }
                }
            }

            String mensagemErro = obterMensagemErro(title);
            if (!UteisValidacao.emptyString(mensagemErro)) {
                if (title.length() > 0) {
                    //adicionar quebra de linha se já tiver alguma informação
                    title.append("<br/><br/>");
                }
                title.append("<b>Motivo:</b><br/>").append(mensagemErro);
            }


            //se não tiver nenhuma informação e estiver como concluida com sucesso ou cancelada
            if (title.length() == 0 && (getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                    getSituacao().equals(SituacaoTransacaoEnum.CANCELADA))) {
                return "Aprovado";
            }

            if (title.length() == 0 &&
                    getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE) &&
                    getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                title.append("Clique no <b>\"Consulta Situação da Transação\"</b> para verificar se a transação teve alterações");
            }

            if (
                    title.length() == 0 &&
                    getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE) &&
                    getSituacao().equals(SituacaoTransacaoEnum.NENHUMA) &&
                    UteisValidacao.emptyString(getParamsResposta()) &&
                    UteisValidacao.emptyString(getCodigoExterno())
            ) {
                title.append("Essa transação teve problemas de comunicação com a Cielo. </br>" +
                        "O sistema Pacto fez o envio da cobrança para a Cielo, mas não teve resposta da Cielo. </br>" +
                        "Verificar no Portal da Cielo se essa cobrança foi Aprovada e se sim, fazer a quitação da parcela no Caixa em Aberto.");
            }

            if (getTipo().equals(TipoTransacaoEnum.FACILITEPAY)) {
                String ant = title.toString().replaceAll("PagoLivre", "Fypay").replaceAll("Pago Livre", "Fypay");
                title = new StringBuilder();
                title.append(ant);
            }

            if (getTipo().equals(TipoTransacaoEnum.PAGOLIVRE) || getTipo().equals(TipoTransacaoEnum.FACILITEPAY)) {
                if (title.toString().contains("PA002")) {
                    title = new StringBuilder();
                    title.append("MerchantId não autorizado para cobrança. Contatar a Adquirente para conseguir um MerchantId válido.");
                }
                try {
                    JSONObject obj = new JSONObject(getParamsResposta());

                    if (this.getCodigoRetorno().equals("?") && this.getCodigoRetornoDescricao().contains("Transação não autorizada") &&
                            obj.optString("code").equals("")) {
                        title.append(" - A adquirente não nos informou o motivo da não autorização.");
                    } else if (obj.has("payment")) {
                        JSONObject paymentJSON = obj.optJSONObject("payment");
                        if(paymentJSON != null) {
                            String mensagemRetornoAdquirente = "";
                            mensagemRetornoAdquirente = paymentJSON.optString("authorizer_message");

                            if((getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO) || getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA))
                                    && (mensagemRetornoAdquirente.contains("Temporary failure") || mensagemRetornoAdquirente.contains("Temporary"))){
                                title.append("Houve uma instabilidade com a API da Pago Livre. Tente novamente mais tarde ou entre em contato com o suporte!");
                                Uteis.logarDebug("Ocorreu um erro ao processar o pagamento. Mensagem de erro: " + mensagemRetornoAdquirente);
                            }
                        }
                    }

                } catch (Exception ex) {
                }
            }

            if (getTipo().equals(TipoTransacaoEnum.CEOPAG) && getCodigoRetorno().contains("PAC001") && getParamsResposta().contains("504 Gateway Time-out")) {
                title = new StringBuilder();
                title.append("Essa transação teve problemas de comunicação com a Ceopag. </br>" +
                        "O sistema Pacto fez o envio da cobrança para a Ceopag, mas não teve resposta da Ceopag. </br>" +
                        "Verificar no Portal da Ceopag se essa cobrança foi Aprovada e se sim, fazer a quitação da parcela no Caixa em Aberto.");
            }

            if (getTipo().equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE) && getCodigoRetorno().contains("PAC001") && getParamsResposta().contains("504 Gateway Time-out")) {
                title = new StringBuilder();
                title.append("Essa transao teve problemas de comunicao com a Caixa. </br>" +
                        "O sistema Pacto fez o envio da cobrana para a Caixa, mas no teve resposta da Caixa. </br>" +
                        "Verificar no Portal da Caixa se essa cobrana foi Aprovada e se sim, fazer a quitao manual da parcela no Caixa em Aberto.");
            }

            //Se no vier mensagem no JSON, buscar mensagem no Enum pelo Cdigo de Retorno
            if (getTipo().equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE) && !UteisValidacao.emptyString(title.toString()) &&
                    (getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO) || getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA))) {
                try {
                    JSONObject retornoJSON = new JSONObject(getParamsResposta());
                    JSONObject paymentJSON = retornoJSON.getJSONObject("payment");
                    String mensagemRetorno = retornoJSON.optString("message");

                    String mensagemRetornoAdquirente = "";
                    try {
                        mensagemRetornoAdquirente = paymentJSON.getString("authorizer_message");
                    } catch (Exception ex) {
                        mensagemRetornoAdquirente = mensagemRetorno;
                    }

                    if (!UteisValidacao.emptyString(mensagemRetornoAdquirente)) {
                        title = new StringBuilder();
                        title.append("Verifique o retorno da Adquirente.");
                        title.append("<br/><br/>");
                        title.append("<b>Motivo:</b><br/>");
                        title.append(mensagemRetornoAdquirente);
                    }
                } catch (Exception e) {
                    return title.toString();
                }
            }

            if (tooltipster) {
                return title.toString();
            } else {
                try {
                    return title.toString().replace("<br/>", "\n").replace("<b>", "").replace("</b>", "");
                } catch (Exception ex) {
                    return title.toString();
                }
            }
        } catch (Exception ex) {
//            Logger.getLogger(TransacaoVO.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public String getCodigoRetorno() {
        if (codigoRetorno == null) {
            codigoRetorno = "";
        }
        return codigoRetorno;
    }

    public void setCodigoRetorno(String codigoRetorno) {
        this.codigoRetorno = codigoRetorno;
    }

    public String getTipoGestaoTransacao() {
        TipoTransacaoEnum tipoTransacaoEnum = this.getTipo();
        if (this.getTipo().equals(TipoTransacaoEnum.PACTO_PAY)) {
            tipoTransacaoEnum = getTipoOrigem();
        }

        String adquirenteTransacao = getAdquirente();
        if (!UteisValidacao.emptyString(adquirenteTransacao)) {
            return tipoTransacaoEnum.getDescricao() + " (" + adquirenteTransacao.replace("_", " ").toUpperCase() + ")";
        }
        return tipoTransacaoEnum.getDescricao();
    }

    public boolean isAmbienteHomologacao() {
        return getAmbiente().equals(AmbienteEnum.HOMOLOGACAO);
    }

    public AmbienteEnum getAmbiente() {
        if (ambiente == null) {
            ambiente = AmbienteEnum.NENHUM;
        }
        return ambiente;
    }

    public void setAmbiente(AmbienteEnum ambiente) {
        this.ambiente = ambiente;
    }

    public String getCodigoRetornoPacto() {
        try {
            JSONObject obj = new JSONObject(getOutrasInformacoes());
            return obj.optString(AtributoTransacaoEnum.codigoRetornoPacto.name());
        } catch (Exception ex) {
            return "";
        }
    }

    private String obterMensagemErro(StringBuilder title) {
        try {
            if (this.getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE)) {
                if ((title.length() > 0) &&
                        (title.toString().toLowerCase().contains("inválido") || title.toString().toLowerCase().contains("notfinished"))) {
                    return "";
                }
                if (!UteisValidacao.emptyString(getParamsResposta())
                        && getParamsResposta().contains("Credenciais Inválidas ou bloqueadas")) {
                    return getParamsResposta();
                }
                if (!UteisValidacao.emptyString(getParamsResposta())
                        && getParamsResposta().contains("MerchantId is required")) {
                    return "MerchantId is required or Invalid";
                }
                if (!UteisValidacao.emptyString(getParamsResposta())) {
                    try {
                        JSONArray array = new JSONArray(getParamsResposta());
                        if (array.length() > 0) {
                            JSONObject json = array.getJSONObject(0);
                            String message = json.optString("Message");
                            if (!UteisValidacao.emptyString(message)) {
                                return message;
                            }
                        }
                    } catch (Exception ex) {
//                        ex.printStackTrace();
                    }
                }
            }
            JSONObject json = new JSONObject(getOutrasInformacoes());
            String msg = json.optString(AtributoTransacaoEnum.erroGenericoTransacao.name());
            if (UteisValidacao.emptyString(msg)) {
                msg = json.optString(AtributoTransacaoEnum.msgErro.name());
            }
            if (UteisValidacao.emptyString(msg)) {
                msg = json.optString(AtributoTransacaoEnum.mensagemErro.name());
            }

            if (this.getTipo().equals(TipoTransacaoEnum.DCC_STONE_ONLINE_V5) && msg.contains("api.pagar.me")) {
                msg = "API da Stone indisponível ou instável no momento da tentativa. Você pode tentar novamente mais tarde.";
            }

            if (this.getTipo().equals(TipoTransacaoEnum.E_REDE) && msg.equals("api.userede.com.br")) {
                msg = "e-rede não nos retornou o motivo correto.";
            }

            if (this.getTipo().equals(TipoTransacaoEnum.GETNET_ONLINE) && msg.contains("A JSONObject text must")) {
                msg = "Getnet não nos retornou o motivo correto.";
            }

            if (this.getTipo().equals(TipoTransacaoEnum.VINDI)) {

                if (!UteisValidacao.emptyString(msg) && msg.contains("invalid_parameter") && msg.contains("phones.number") && msg.contains("inválido")) {
                    msg =  "O telefone informado é inválido. </br> <b>Obs:</b> Se o aluno possuir responsável, verifique o telefone do responsável.";
                } else if (!UteisValidacao.emptyString(getParamsResposta())) {
                    JSONObject jsonResposta = new JSONObject(getParamsResposta());

                    if (jsonResposta.has("errors")) {
                        for (int i = 0; i < jsonResposta.getJSONArray("errors").length(); i++) {
                            String conteudo = jsonResposta.getJSONArray("errors").get(i).toString();
                            if (conteudo.contains("payment_company_id") && msg.equals("não pode ficar em branco")){
                                msg = "Bandeira não autorizada, verifique no portal da VINDI as bandeiras autorizadas";
                            }
                        }
                    }
                }
            }

            return msg.replace("Cadastre e volte aqui para efetuar o pagamento.", "");
        } catch (Exception ex) {
            return "";
        }
    }

    public boolean isPermiteSincronizar() {
        if (getSituacao().equals(SituacaoTransacaoEnum.NENHUMA) && getTipo().equals(TipoTransacaoEnum.STONE_ONLINE)) {
            return true;
        } else if (getTipo().equals(TipoTransacaoEnum.PAGAR_ME) &&
                getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO) &&
                getParamsResposta().toUpperCase().contains("<HTML>")) {
            return true;
        } else if (getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE) && (getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO) || getSituacao().equals(SituacaoTransacaoEnum.APROVADA))) {
            return true;
        } else if (getTipo().equals(TipoTransacaoEnum.VINDI) && (getSituacao().equals(SituacaoTransacaoEnum.APROVADA) ||
                (getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO) && !UteisValidacao.emptyString(getCodigoExterno())))) {
            return true;
        } else if ((getTipo().equals(TipoTransacaoEnum.PAGOLIVRE) || getTipo().equals(TipoTransacaoEnum.FACILITEPAY)) &&
                (getSituacao().equals(SituacaoTransacaoEnum.APROVADA) || getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO))) {
            return true;
        } else if (getTipo().equals(TipoTransacaoEnum.ONE_PAYMENT) && (getSituacao().equals(SituacaoTransacaoEnum.APROVADA) ||
                (getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO)))) {
            return true;
        } else if (getTipo().equals(TipoTransacaoEnum.PAGBANK) && (getSituacao().equals(SituacaoTransacaoEnum.APROVADA))) {
            return true;
        }else {
            return (getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) || getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA) || getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) &&
                    (getTipo().equals(TipoTransacaoEnum.MUNDIPAGG) || getTipo().equals(TipoTransacaoEnum.PAGAR_ME));
        }
    }

    public boolean isPermiteRetentativa() {
        return getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA) ||
                getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO) ||
                getSituacao().equals(SituacaoTransacaoEnum.CANCELADA);
    }

    public String dataProximaTentativa() {
        return "";
    }

    public String nrTentativas() {
        return "";
    }

    public boolean isIgnorarSituacaoCancelamento() {
        return ignorarSituacaoCancelamento;
    }

    public void setIgnorarSituacaoCancelamento(boolean ignorarSituacaoCancelamento) {
        this.ignorarSituacaoCancelamento = ignorarSituacaoCancelamento;
    }

    public CartaoCreditoTO getCartaoCreditoTO() {
        return cartaoCreditoTO;
    }

    public void setCartaoCreditoTO(CartaoCreditoTO cartaoCreditoTO) {
        this.cartaoCreditoTO = cartaoCreditoTO;
    }

    public Integer getQtdParcelasTransacao() {
        if (qtdParcelasTransacao == null) {
            qtdParcelasTransacao = 0;
        }
        return qtdParcelasTransacao;
    }

    public void setQtdParcelasTransacao(Integer qtdParcelasTransacao) {
        this.qtdParcelasTransacao = qtdParcelasTransacao;
    }

    public UsuarioVO getUsuarioResponsavelCancelamento() {
        if (usuarioResponsavelCancelamento == null) {
            usuarioResponsavelCancelamento = new UsuarioVO();
        }
        return usuarioResponsavelCancelamento;
    }

    public void setUsuarioResponsavelCancelamento(UsuarioVO usuarioResponsavelCancelamento) {
        this.usuarioResponsavelCancelamento = usuarioResponsavelCancelamento;
    }

    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }

    public Date getProximaTentativa() {
        return proximaTentativa;
    }

    public void setProximaTentativa(Date proximaTentativa) {
        this.proximaTentativa = proximaTentativa;
    }

    public String getOrigemSincronizacao() {
        if (origemSincronizacao == null) {
            origemSincronizacao = "";
        }
        return origemSincronizacao;
    }

    public void setOrigemSincronizacao(String origemSincronizacao) {
        this.origemSincronizacao = origemSincronizacao;
    }

    public boolean isTransacaoVerificarCartao() {
        return transacaoVerificarCartao;
    }

    public void setTransacaoVerificarCartao(boolean transacaoVerificarCartao) {
        this.transacaoVerificarCartao = transacaoVerificarCartao;
    }

    public boolean isAtualizarParamsResposta() {
        return atualizarParamsResposta;
    }

    public void setAtualizarParamsResposta(boolean atualizarParamsResposta) {
        this.atualizarParamsResposta = atualizarParamsResposta;
    }

    public RemessaDTO toRemessaDTO(String chave, CartaoCreditoTO cartaoCreditoTO) {
        RemessaDTO remessaDTO = new RemessaDTO();
        remessaDTO.setChave(chave);
        remessaDTO.setId(this.getCodigoExterno());
        remessaDTO.setIdReferencia(this.getCodigo());
        remessaDTO.setResponsavel(this.getUsuarioResponsavel().getNome());
        remessaDTO.setDataRegistro(Calendario.getDataAplicandoFormatacao(this.getDataProcessamento(), "yyyyMMddHHmmss"));
        remessaDTO.setConvenio(this.getConvenioCobrancaVO().toConvenioDTO());
        remessaDTO.setEmpresa(this.getEmpresaVO().toEmpresaDTO());
        remessaDTO.setTransacoes(new ArrayList<>());
        remessaDTO.getTransacoes().add(this.toTransacaoDTO(cartaoCreditoTO));
        return remessaDTO;
    }

    public TransacaoDTO toTransacaoDTO(CartaoCreditoTO cartaoCreditoTO) {
        TransacaoDTO transacaoDTO = new TransacaoDTO();
        transacaoDTO.setId(this.getCodigoExterno());
        transacaoDTO.setIdReferencia(this.getCodigo());
        transacaoDTO.setIdTransacaoAdquirente(this.getCodigoExterno());
        transacaoDTO.setCliente(this.getPessoaPagador().toClienteDTO(this.getConvenioCobrancaVO().getTipo()));
        transacaoDTO.setTokenAragorn(this.getTokenAragorn());
        transacaoDTO.setValor(this.getValor());
        transacaoDTO.setNrParcelas(this.getNumeroParcelas());
        transacaoDTO.setDescricao(this.getDescricaoParcelas());
        transacaoDTO.setCapturar(!this.isTransacaoVerificarCartao());
        transacaoDTO.setDataRegistro(Calendario.getDataAplicandoFormatacao(this.getDataProcessamento(), "yyyyMMddHHmmss"));
        transacaoDTO.setVencimento(Calendario.getDataAplicandoFormatacao(this.getDataProcessamento(), "yyyyMMdd"));
        transacaoDTO.setSoftDescriptor(this.getEmpresaVO().getRazaoSocialParaSoftDescriptor(this.isTransacaoVerificarCartao()));

        if (cartaoCreditoTO != null) {
            transacaoDTO.setCodigoSeguranca(cartaoCreditoTO.getCodigoSeguranca());
            transacaoDTO.setRecorrente(!cartaoCreditoTO.isTransacaoPresencial());
            transacaoDTO.setGatewayTokenVindi(cartaoCreditoTO.getGatewayTokenVindi());

            if (cartaoCreditoTO.getTipoParcelamentoStone() != null) {
                transacaoDTO.setTipoParcelamento(cartaoCreditoTO.getTipoParcelamentoStone().getValor());
            }

            String ipOrigem = cartaoCreditoTO.getIpClientePacto();
            if (UteisValidacao.emptyString(ipOrigem)) {
                ipOrigem = this.getIpTransiente();
            }
            transacaoDTO.setIpOrigem(ipOrigem);
        }
        return transacaoDTO;
    }

    public Integer getNumeroParcelas() {
        try {
            JSONObject obj = new JSONObject(getOutrasInformacoes());
            return Integer.parseInt(obj.getString(AtributoTransacaoEnum.numeroParcelas.name()));
        } catch (Exception ex) {
            return 1;
        }
    }

    public OrigemCobrancaEnum getOrigem() {
        if (origem == null) {
            origem = OrigemCobrancaEnum.NENHUM;
        }
        return origem;
    }

    public void setOrigem(OrigemCobrancaEnum origem) {
        this.origem = origem;
    }

    public TipoTransacaoEnum getTipoOrigem() {
        if (tipoOrigem == null) {
            tipoOrigem = TipoTransacaoEnum.NENHUMA;
        }
        return tipoOrigem;
    }

    public void setTipoOrigem(TipoTransacaoEnum tipoOrigem) {
        this.tipoOrigem = tipoOrigem;
    }

    public boolean isUtilizarUsuarioGerarRecibo() {
        return utilizarUsuarioGerarRecibo;
    }

    public void setUtilizarUsuarioGerarRecibo(boolean utilizarUsuarioGerarRecibo) {
        this.utilizarUsuarioGerarRecibo = utilizarUsuarioGerarRecibo;
    }

    public String getValorTitleApresentar() {
        try {
            if (getTipo().equals(TipoTransacaoEnum.STONE_ONLINE)) {
                TransacaoStoneOnlineVO transacaoStoneOnlineVO = (TransacaoStoneOnlineVO) this;
                String tipoParcelamento = transacaoStoneOnlineVO.obterValorParametroXML(this.getParamsEnvio(), "InstlmtTp");
                if (tipoParcelamento.equalsIgnoreCase(InstalmentTypeInstlmtTp.COM_JUROS_EMISSOR.getValor())) {
                    StringBuilder title = new StringBuilder();
                    title.append("A cobrança foi parcelada com tipo de parcelamento <b>EMISSOR (COM JUROS)</b>.<br/>");
                    title.append("O valor cobrado no cartão do cliente pode ser superior devido ao juros da operadora.");
                    return title.toString();
                }
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getCodigoStatusPactoPay() {
        return getSituacao().getStatusPactoPayEnum().getCodigo();
    }

    public void adicionarOutrasInformacoes(AtributoTransacaoEnum atributoEnum, String value) {
        JSONObject jsonOutrasInformacoes = new JSONObject();
        if (!UteisValidacao.emptyString(this.getOutrasInformacoes())) {
            try {
                jsonOutrasInformacoes = new JSONObject(this.getOutrasInformacoes());
            } catch (Exception ex) {
                jsonOutrasInformacoes = new JSONObject();
                jsonOutrasInformacoes.put(AtributoTransacaoEnum.outras_informacoes_anterior.name(), this.getOutrasInformacoes());
            }
        }
        try {
            jsonOutrasInformacoes.put(atributoEnum.name(), value);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        this.setOutrasInformacoes(jsonOutrasInformacoes.toString());
    }

    public String obterItemOutrasInformacoes(AtributoTransacaoEnum atributoEnum) {
        try {
            JSONObject jsonOutrasInformacoes = new JSONObject(this.getOutrasInformacoes());
            try {
                return jsonOutrasInformacoes.get(atributoEnum.name()).toString();
            } catch (Exception ex) {
//                Uteis.logarPrintStackTrace(ex);
            }
            return jsonOutrasInformacoes.getString(atributoEnum.name());
        } catch (Exception ex) {
//            Uteis.logarPrintStackTrace(ex);
            return "";
        }
    }

    public String obterValorParametroXML(String xml, String parametro) {
        return "";
    }

    public String getCodigoRetornoDescricao() {
        if (codigoRetornoDescricao == null) {
            codigoRetornoDescricao = "";
        }
        return codigoRetornoDescricao;
    }

    public void setCodigoRetornoDescricao(String codigoRetornoDescricao) {
        this.codigoRetornoDescricao = codigoRetornoDescricao;
    }

    public Integer getNrVezesCobranca() {
        if (!UteisValidacao.emptyNumber(getNrVezes())) {
            return this.getNrVezes();
        }
        String nrParcelas = obterItemOutrasInformacoes(AtributoTransacaoEnum.numeroParcelas);
        if (!UteisValidacao.emptyString(nrParcelas)) {
            return Integer.parseInt(nrParcelas);
        }
        return null;
    }

    public OperadorasExternasAprovaFacilEnum getOperadoraEnum() {
        if (getBandeiraPagamento() != null) {
            return this.getBandeiraPagamento();
        }
        String operadora = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira);
        if (!UteisValidacao.emptyString(operadora)) {
            return OperadorasExternasAprovaFacilEnum.obterPorDescricao(operadora);
        }
        return null;
    }

    public String getIdCartaoPinBank() {
        if (idCartaoPinBank == null){
            idCartaoPinBank = "";
        }
        return idCartaoPinBank;
    }

    public void setIdCartaoPinBank(String idCartaoPinBank) {
        this.idCartaoPinBank = idCartaoPinBank;
    }

    public String getIdentificadorPacto() {
        String identificador = obterItemOutrasInformacoes(AtributoTransacaoEnum.identificadorPacto);
        if (!UteisValidacao.emptyString(identificador)) {
            return identificador;
        }
        return "";
    }

    public boolean isVerificador() {
        return verificador;
    }

    public void setVerificador(boolean verificador) {
        this.verificador = verificador;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public String getUltimos4Digitos() {
        try {
            return this.getCartaoMascarado().substring(this.getCartaoMascarado().length() - 4);
        } catch (Exception ex) {
            return "";
        }
    }

    public boolean isSelecionada() {
        return selecionada;
    }

    public void setSelecionada(boolean selecionada) {
        this.selecionada = selecionada;
    }

    public boolean isCompensado() {
        return compensado;
    }

    public void setCompensado(boolean compensado) {
        this.compensado = compensado;
    }

    public Date getDtCompensacao() {
        return dtCompensacao;
    }

    public void setDtCompensacao(Date dtCompensacao) {
        this.dtCompensacao = dtCompensacao;
    }

    public Double getDesconto() {
        if (desconto == null) {
            desconto = 0.0;
        }
        return desconto;
    }

    public void setDesconto(Double desconto) {
        this.desconto = desconto;
    }

    public String getCodigoExterno2() {
        if (UteisValidacao.emptyString(this.codigoExterno2)) {
            return "";
        }
        return codigoExterno2;
    }

    public void setCodigoExterno2(String codigoExterno2) {
        this.codigoExterno2 = codigoExterno2;
    }

    public String getNsuTransacao() {
        if (UteisValidacao.emptyString(nsuTransacao)) {
            nsuTransacao = "";
        }
        return nsuTransacao;
    }

    public void setNsuTransacao(String nsuTransacao) {
        this.nsuTransacao = nsuTransacao;
    }

    public String getCodigoNSU() {
        if (UteisValidacao.emptyString(this.codigoNSU)) {
            return "";
        }
        return codigoNSU;
    }

    public void setCodigoNSU(String codigoNSU) {
        this.codigoNSU = codigoNSU;
    }

    public Long getGateway_id() {
        if (UteisValidacao.emptyNumber(this.gateway_id)) {
            return null;
        }
        return gateway_id;
    }

    public void setGateway_id(Long gateway_id) {
        this.gateway_id = gateway_id;
    }

    public OperadorasExternasAprovaFacilEnum getBandeiraEnum() {
        return bandeiraEnum;
    }

    public void setBandeiraEnum(OperadorasExternasAprovaFacilEnum bandeiraEnum) {
        this.bandeiraEnum = bandeiraEnum;
    }

    public boolean isErrorExisteTransacaoRecenteParaMovParcela() {
        return errorExisteTransacaoRecenteParaMovParcela;
    }

    public void setErrorExisteTransacaoRecenteParaMovParcela(boolean errorExisteTransacaoRecenteParaMovParcela) {
        this.errorExisteTransacaoRecenteParaMovParcela = errorExisteTransacaoRecenteParaMovParcela;
    }

    public boolean isErrorMovParcelaDaTransacaoNaoEstaEmAberto() {
        return errorMovParcelaDaTransacaoNaoEstaEmAberto;
    }

    public void setErrorMovParcelaDaTransacaoNaoEstaEmAberto(boolean errorMovParcelaDaTransacaoNaoEstaEmAberto) {
        this.errorMovParcelaDaTransacaoNaoEstaEmAberto = errorMovParcelaDaTransacaoNaoEstaEmAberto;
    }

    public String getParamsEnvioPessoa() {
        if (UteisValidacao.emptyString(paramsEnvioPessoa)) {
            return "";
        }
        return paramsEnvioPessoa;
    }

    public void setParamsEnvioPessoa(String paramsEnvioPessoa) {
        this.paramsEnvioPessoa = paramsEnvioPessoa;
    }

    public String getParamsRespostaPessoa() {
        if (UteisValidacao.emptyString(paramsRespostaPessoa)) {
            return "";
        }
        return paramsRespostaPessoa;
    }

    public void setParamsRespostaPessoa(String paramsRespostaPessoa) {
        this.paramsRespostaPessoa = paramsRespostaPessoa;
    }

    public boolean isErroIncluirPessoa() {
        return erroIncluirPessoa;
    }

    public void setErroIncluirPessoa(boolean erroIncluirPessoa) {
        this.erroIncluirPessoa = erroIncluirPessoa;
    }

    public boolean isErroAtualizarPessoa() {
        return erroAtualizarPessoa;
    }

    public void setErroAtualizarPessoa(boolean erroAtualizarPessoa) {
        this.erroAtualizarPessoa = erroAtualizarPessoa;
    }
}
