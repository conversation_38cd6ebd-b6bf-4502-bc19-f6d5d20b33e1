package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoDebitoOnlineEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Random;

/**
 * Reponsável por manter os dados da entidade FormaPagamento. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class FormaPagamentoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = 0;
    protected String descricao;
    protected String tipoFormaPagamento;
    protected Double taxaCartao;
    private double taxaPix;
    @ChaveEstrangeira
    @FKJson
    protected ConvenioCobrancaVO convenioCobrancaVO;
    private Boolean defaultRecorrencia = false;
    private Boolean defaultDCO = false;
    private double valor = 0.0; // atributo utilizado no Demonstrativo Financeiro
    private Date dataCompensacao;// atributo utilizado no Demonstrativo Financeiro
    @NaoControlarLogAlteracao
    private Boolean formaPagamentoEscolhida;
    private boolean ativo = true;
    private boolean somenteFinanceiro = true;
    private boolean compensacaoDiasUteis = false;
    private boolean apresentarNSU = false;
    @NaoControlarLogAlteracao
    @Lista
    @ListJson(clazz = TaxaCartaoVO.class)
    private List<TaxaCartaoVO> taxasCartao = new ArrayList<TaxaCartaoVO>();
    @NaoControlarLogAlteracao
    private List<TaxaCartaoVO> taxasCartaoAntesAlteracao = new ArrayList<TaxaCartaoVO>();
    private List<PinPadVO> pinPadAntesAlteracao = new ArrayList<PinPadVO>();
    private int diasCompensacaoCartaoCredito = 30;
    private String cor;
    @NaoControlarLogAlteracao
    private int quantidade = 0;
    @NaoControlarLogAlteracao
    private double percentual = 0;
    private boolean exigeCodAutorizacao = false;
    private boolean cartaoDebitoOnline = false;
    private TipoDebitoOnlineEnum tipoDebitoOnline;
    private String merchantid;
    private String merchantkey;


    private List<FormaPagamentoEmpresaVO> formasEmpresas = new ArrayList<FormaPagamentoEmpresaVO>();
    private List<FormaPagamentoPerfilAcessoVO> formasPerfilAcesso;
    private TipoParceiroEnum tipoParceiro = TipoParceiroEnum.NENHUM;
    private boolean gerarPontos = false;//pontos de fidelidade
    private List<PinPadVO> listaPinPad;
    private TipoConvenioCobrancaEnum tipoConvenioCobranca;
    private PinPadVO pinpad;
    private boolean receberSomenteViaPinPad = false;
    private Integer nrMaxParcelasPinpad;
    private String posIDGeoitd;
    private String systemIdGeoitd;
    private long quantidadeAlunos = 0;
    private boolean stoneConnect = false;
    private boolean getCard = false;
    private boolean taxaPixValorAbsoluto;
    private boolean contaDeConsumo = false;

    public FormaPagamentoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>FormaPagamentoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(FormaPagamentoVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }

        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Forma de Pagamento) deve ser informado.");
        }
        if (obj.getTipoFormaPagamento().equals("")) {
            throw new ConsistirException("O campo TIPO FORMA PAGAMENTO (Forma de Pagamento) deve ser informado.");
        }
        if (obj.getDefaultRecorrencia() && getFacade().getFormaPagamento().existeOutraFormaPagamentoDefault(obj, true)) {
            throw new ConsistirException("Já existe outra FORMA DE PAGAMENTO como 'defaultRecorrencia', é permitido apenas uma.");
        }

        if (obj.getDefaultDCO() && getFacade().getFormaPagamento().existeOutraFormaPagamentoDefault(obj, false)) {
            throw new ConsistirException("Já existe outra FORMA DE PAGAMENTO como 'defaultDCO', é permitido apenas uma.");
        }
        if(Uteis.retirarAcentuacao(obj.getDescricao().toUpperCase()).equals("BOLETO BANCARIO") && !obj.getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla())){
            throw new ConsistirException("O Sistema não permite o uso da descrição BOLETO BANCÁRIO para uma forma de pagamento de um tipo diferente de Boleto Bancário.");
        }
        if(Uteis.retirarAcentuacao(obj.getDescricao().toUpperCase()).equals("CARTAO RECORRENTE") && !obj.getDefaultRecorrencia()){
            throw new ConsistirException("O Sistema não permite o uso da descrição CARTÃO RECORRENTE para uma forma de pagamento que não seja Default Recorrência.");
        }
        if(Uteis.retirarAcentuacao(obj.getDescricao().toUpperCase()).equals("DEBITO CONTA CORRENTE") && !obj.getDefaultDCO()){
            throw new ConsistirException("O Sistema não permite o uso da descrição DÉBITO CONTA CORRENTE para uma forma de pagamento que não seja Default DCO.");
        }
        
        if(getFacade().getFormaPagamento().existeOutraFormaPagamentoMesmaDescricao(obj)){
            throw new ConsistirException("Já existe outra FORMA DE PAGAMENTO com a descrição " + obj.getDescricao() +" cadastrado na base de dados.");
        }
        if (!obj.isSomenteFinanceiro()) {
            if (obj.getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla()) && getFacade().getFormaPagamento().existeOutraFormaPagamentoZW(obj)) {
                throw new ConsistirException("Já existe outra FORMA DE PAGAMENTO do '" + TipoFormaPagto.CREDITOCONTACORRENTE.getDescricao() + "' no ZillyonWeb, é permitido apenas uma.");
            }
            if (obj.getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla()) && getFacade().getFormaPagamento().existeOutraFormaPagamentoZW(obj)) {
                throw new ConsistirException("Já existe outra FORMA DE PAGAMENTO do '" + TipoFormaPagto.CHEQUE.getDescricao() + "' no ZillyonWeb, é permitido apenas uma.");
            }
        }
    }

    public String getConvenio_Apresentar() {
        return getConvenioCobrancaVO().getDescricao();
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setTipoFormaPagamento(getTipoFormaPagamento().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setDescricao("");
        setTipoFormaPagamento("");
        setTaxaCartao(0.0);
        setAtivo(true);
        setSomenteFinanceiro(true);
        setTaxasCartao(new ArrayList<>());
    }

    /**
     * Retorna o objeto da classe <code>ConvenioDesconto</code> relacionado com (<code>FormaPagamento</code>).
     */
    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    /**
     * Define o objeto da classe <code>ConvenioDesconto</code> relacionado com (<code>FormaPagamento</code>).
     */
    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public Double getTaxaCartao() {
        return (taxaCartao);
    }

    public String getTaxaCartaoApresentar() {
        return (Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTaxaCartao())).replace(",",".") + "%" ;
    }

    public void setTaxaCartao(Double taxaCartao) {
        this.taxaCartao = taxaCartao;
    }

    public String getTipoFormaPagamento() {
        if (tipoFormaPagamento == null) {
            tipoFormaPagamento = "";
        }
        return (tipoFormaPagamento);
    }

    public void setTipoFormaPagamento(String tipoFormaPagamento) {
        this.tipoFormaPagamento = tipoFormaPagamento;
    }

    public TipoFormaPagto getTipoFormaPagamentoEnum() {
        return TipoFormaPagto.getTipoFormaPagtoSigla(getTipoFormaPagamento());
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico.
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc.
     */
    public String getTipoFormaPagamento_Apresentar() {
        if (tipoFormaPagamento == null) {
            tipoFormaPagamento = "";
        }
        if (tipoFormaPagamento.equals("CA")) {
            return "Cartão de Crédito";
        }
        if (tipoFormaPagamento.equals("CD")) {
            return "Cartão de Débito";
        }
        if (tipoFormaPagamento.equals("CH")) {
            return "Cheque";
        }
        if (tipoFormaPagamento.equals("AV")) {
            return "A vista";
        }
        if (tipoFormaPagamento.equals("CC")) {
            return "Conta Corrente";
        }
        if (tipoFormaPagamento.equals("BB")) {
            return "Boleto Bancário";
        }
        if (tipoFormaPagamento.equals("CO")) {
            return "Convênio";
        }
        if (tipoFormaPagamento.equals("PD")) {
            return "Pagamento Digital";
        }
        if (tipoFormaPagamento.equals("LO")) {
            return "Lote";
        }
        if (tipoFormaPagamento.equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla())) {
            //DEVE SER AJUSTADO QUANDO ESTIVER OUTRO PARCEIRO DEVE SER ALTERADO FUTURAMENTE
            return TipoParceiroEnum.DOTZ.getNome();
        }
        if (tipoFormaPagamento.equals(TipoFormaPagto.TRANSFERENCIA_BANCARIA.getSigla())) {
            return TipoFormaPagto.TRANSFERENCIA_BANCARIA.getDescricao();
        }

        if (tipoFormaPagamento.equals(TipoFormaPagto.PIX.getSigla())) {
            return TipoFormaPagto.PIX.getDescricao();
        }

        return (tipoFormaPagamento);

    }
    public boolean isApresentarCampoTaxaPix(){
        if(tipoFormaPagamento.equals("PX")){
            return true;
        }
        return false;
    }
    public boolean isApresentarCampoCompensacao(){
        if(tipoFormaPagamento.equals("AV") || tipoFormaPagamento.equals("CO")  || tipoFormaPagamento.equals("CC")
                || tipoFormaPagamento.equals("PF") || tipoFormaPagamento.equals(TipoFormaPagto.TRANSFERENCIA_BANCARIA.getSigla()) ||
                tipoFormaPagamento.equals(TipoFormaPagto.PIX.getSigla())){
            return false;
        }
        return true;
    }
    public boolean isApresentarCampoNSU(){
        if(tipoFormaPagamento.equals("CA") || tipoFormaPagamento.equals("CD")){
            return true;
        }
        return false;
    }
    public boolean isApresentarDefaultRecorrencia(){
        if(tipoFormaPagamento.equals("CA") && !tipoFormaPagamento.equals("PF")){
            return true;
        }
        return false;
    }
    public boolean isApresentarCodigoAutorizacao(){
        if(tipoFormaPagamento.equals("CA") || tipoFormaPagamento.equals("CD")){
            return true;
        }
        return false;
    }
    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Boolean getDefaultRecorrencia() {
        return defaultRecorrencia;
    }

    public void setDefaultRecorrencia(Boolean defaultRecorrencia) {
        this.defaultRecorrencia = defaultRecorrencia;
    }

    public String getDefaultRecorrencia_Apresentar() {
        if (defaultRecorrencia) {
            return "Sim";
        } else {
            return "Não";
        }
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public double getValor() {
        return valor;
    }

    public String getValorApresentar() {
//        Locale locale = Formatador.BRASIL;
//
//        try {
//            locale = ((LoginControle) JSFUtilities.getFromSession("LoginControle")).getEmpresa().getLocale();
//        } catch (Exception ignored) {
//
//        }
        return Formatador.formatarValorMonetarioSemMoeda(valor);
        //return Formatador.formatarValorMonetario(valor, locale);
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public String getDataCompensacaoApresentar() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyy");
        return sdf.format(this.dataCompensacao);
    }

    /**
     * @return the formaPagamentoEscolhida
     */
    public Boolean getFormaPagamentoEscolhida() {
        if (formaPagamentoEscolhida == null) {
            formaPagamentoEscolhida = Boolean.FALSE;
        }
        return formaPagamentoEscolhida;
    }

    /**
     * @param formaPagamentoEscolhida the formaPagamentoEscolhida to set
     */
    public void setFormaPagamentoEscolhida(Boolean formaPagamentoEscolhida) {
        this.formaPagamentoEscolhida = formaPagamentoEscolhida;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public boolean isPix(){
        return getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla());
    }

    public boolean isBoleto(){
        return getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla());
    }

    public boolean isTransferencia(){
        return getTipoFormaPagamento().equals(TipoFormaPagto.TRANSFERENCIA_BANCARIA.getSigla());
    }

    public boolean isPixAVista(){
        return getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla()) || (getTipoFormaPagamento().equals(TipoFormaPagto.AVISTA.getSigla()) && getDescricao().contains("PIX"));
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public String getSituacao_Apresentar() {
        if (ativo) {
            return ("Ativo");
        } else {
            return ("Inativo");
        }
    }

    public boolean isSomenteFinanceiro() {
        return somenteFinanceiro;
    }

    public void setSomenteFinanceiro(boolean somenteFinanceiro) {
        this.somenteFinanceiro = somenteFinanceiro;
    }

    @Override
    public boolean equals(Object o) {
        boolean equal = false;
        if (o instanceof FormaPagamentoVO) {
            FormaPagamentoVO fpagamento = (FormaPagamentoVO) o;
            equal = fpagamento.getCodigo().equals(this.getCodigo());
        }
        return equal;
    }

    @Override
    public int hashCode() {
        return this.getCodigo().hashCode() + this.getDescricao().hashCode();
    }

    public boolean isCompensacaoDiasUteis() {
        return compensacaoDiasUteis;
    }

    public void setCompensacaoDiasUteis(boolean compensacaoDiasUteis) {
        this.compensacaoDiasUteis = compensacaoDiasUteis;
    }

    public boolean isApresentarNSU() {
        return apresentarNSU;
    }

    public void setApresentarNSU(boolean apresentarNSU) {
        this.apresentarNSU = apresentarNSU;
    }

    public List<TaxaCartaoVO> getTaxasCartao() {
        return taxasCartao;
    }

    public void setTaxasCartao(List<TaxaCartaoVO> taxasCartao) {
        this.taxasCartao = taxasCartao;
    }

    public double getTaxaCartao(int nrVezes, Date dataLancamento, Integer adquirente, Integer operadora) {
        if (getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
            for (TaxaCartaoVO taxaCartaoVO : getTaxasCartao()) {
                if (taxaCartaoVO.getNrmeses() == nrVezes
                        && (Uteis.compareNullOrZero(taxaCartaoVO.getAdquirenteVO().getCodigo(), adquirente))
                        && (Uteis.compareNullOrZero(taxaCartaoVO.getBandeira().getCodigo(), operadora))) {
                    if ((taxaCartaoVO.getVigenciaInicial() != null && taxaCartaoVO.getVigenciaFinal() == null) && Calendario.maiorOuIgual(dataLancamento, taxaCartaoVO.getVigenciaInicial())) {
                        return taxaCartaoVO.getTaxa();
                    } else if ((taxaCartaoVO.getVigenciaInicial() != null && taxaCartaoVO.getVigenciaFinal() != null)
                            && (Calendario.maiorOuIgual(dataLancamento, taxaCartaoVO.getVigenciaInicial()) && Calendario.menorOuIgual(dataLancamento, taxaCartaoVO.getVigenciaFinal()))) {
                        return taxaCartaoVO.getTaxa();
                    }
                }
            }
        } else if (getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())) {
            Double taxa = getTaxaCartaoDebito(dataLancamento, adquirente, operadora);
            if(!UteisValidacao.emptyNumber(taxa)){
                return taxa;
            }
            taxa = getTaxaCartaoDebito(dataLancamento, 0, operadora);
            if(!UteisValidacao.emptyNumber(taxa)){
                return taxa;
            }
            return getTaxaCartaoDebito(dataLancamento, 0, 0);
        }

        return 0.0;
    }

    public double getTaxaCartaoDebito(Date dataLancamento, Integer adquirente, Integer operadora) {
        for (TaxaCartaoVO taxaCartaoVO : getTaxasCartao()) {
            if (Uteis.compareNullOrZero(taxaCartaoVO.getAdquirenteVO().getCodigo(), adquirente)
                    && (Uteis.compareNullOrZero(taxaCartaoVO.getBandeira().getCodigo(), operadora))) {
                if ((taxaCartaoVO.getVigenciaInicial() != null && taxaCartaoVO.getVigenciaFinal() == null) && Calendario.maiorOuIgual(dataLancamento, taxaCartaoVO.getVigenciaInicial())) {
                    return taxaCartaoVO.getTaxa();
                } else if ((taxaCartaoVO.getVigenciaInicial() != null && taxaCartaoVO.getVigenciaFinal() != null)
                        && (Calendario.maiorOuIgual(dataLancamento, taxaCartaoVO.getVigenciaInicial()) && Calendario.menorOuIgual(dataLancamento, taxaCartaoVO.getVigenciaFinal()))) {
                    return taxaCartaoVO.getTaxa();
                }
            }
        }
        return 0.0;
    }

    public Boolean getDefaultDCO() {
        return defaultDCO;
    }

    public void setDefaultDCO(Boolean defaultDCO) {
        this.defaultDCO = defaultDCO;
    }


    public int getDiasCompensacaoCartaoCredito() {
        return diasCompensacaoCartaoCredito;
    }

    public void setDiasCompensacaoCartaoCredito(int diasCompensacaoCartaoCredito) {
        this.diasCompensacaoCartaoCredito = diasCompensacaoCartaoCredito;
    }

    public String getCor() {
        try {
            if(cor != null && cor.length() == 6 ){
                cor = "#" + cor;
            }else if(cor != null && cor.length() != 7 ){
                cor = "";
            }
            return cor;
        }catch (Exception e){
            return "";
        }
    }

    public void setCor(String cor) {
        this.cor = cor;
    }
    
    public String getCorRandom(){
        try {
            String[] cores = new String[]{"#60C2EC","#DDB77C","#FEDB52", "#FD6775", "#BA6721","#0F8C3D", "#D5F29B","#D5B123", "#005E86"};
            Random RANDOM = new Random();
            return cores[RANDOM.nextInt(cores.length)];
        } catch (Exception e) {
            return "#e5e5e5";
        }
    }

    public int getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(int quantidade) {
        this.quantidade = quantidade;
    }

    public double getPercentual() {
        return percentual;
    }

    public void setPercentual(double percentual) {
        this.percentual = percentual;
    }
    
    public String getPercentualApresentar(){
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(percentual);
    }
    
    public String getSigla(){
        try {
            String sgl = "";
            String desc = getDescricao().toUpperCase().replace(" DO ", " ").replace(" DE ", " ").replace(" OU ", " ");
            if(!desc.contains(" ")){
                return desc;
            }
            String[] split = desc.split(" ");
            int i = 1;
            for(String spl : split){
                sgl += ((spl.length() <= 3 ? spl : (spl.substring(0, 3))+ ".") +(i == 2 ? "\n" : " "));
                i++;
            }
            return sgl;
        } catch (Exception e) {
            return getDescricao();
        }
    }

    public List<TaxaCartaoVO> getTaxasCartaoAntesAlteracao() {
        return taxasCartaoAntesAlteracao;
    }

    public void setTaxasCartaoAntesAlteracao(List<TaxaCartaoVO> taxasCartaoAntesAlteracao) {
        this.taxasCartaoAntesAlteracao = taxasCartaoAntesAlteracao;
    }

    public void registrarTaxasAntesAlteracao() throws Exception{
        taxasCartaoAntesAlteracao = new ArrayList<TaxaCartaoVO>();
        for (TaxaCartaoVO taxa : taxasCartao){
            taxasCartaoAntesAlteracao.add((TaxaCartaoVO)taxa.getClone(true));
        }
    }

    public List<PinPadVO> getPinPadAntesAlteracao() {
        return pinPadAntesAlteracao;
    }

    public void setPinPadAntesAlteracao(List<PinPadVO> pinPadAntesAlteracao) {
        this.pinPadAntesAlteracao = pinPadAntesAlteracao;
    }

    public void registrarPinPadsAntesAlteracao() throws Exception{
        pinPadAntesAlteracao = new ArrayList<PinPadVO>();
        for (PinPadVO pinpad : getListaPinPad()){
            pinPadAntesAlteracao.add((PinPadVO)pinpad.getClone(true));
        }
    }
    
    public String getClasseCssPorTipoForma(){
        try{
            if(this.getTipoFormaPagamento().equals("AV")
               || this.getTipoFormaPagamento().equals("CA") 
               ||this.getTipoFormaPagamento().equals("CD")    
               ||this.getTipoFormaPagamento().equals("TB")
               ||this.getTipoFormaPagamento().equals("CH")){
                return "formaEspecie";
            }
            if(this.getTipoFormaPagamento().equals("BB")){
                return "formaBoleto";
            }
            if(this.getTipoFormaPagamento().equals("CC")){
                return "formaCC";
            }
        }catch(Exception ignored){
        }
        return "";
    }

    public void setExigeCodAutorizacao(boolean exigeCodAutorizacao) {
        this.exigeCodAutorizacao = exigeCodAutorizacao;
    }

    public boolean isExigeCodAutorizacao() {
        return exigeCodAutorizacao;
    }

    public boolean isCartaoDebitoOnline() {
        return cartaoDebitoOnline;
    }

    public void setCartaoDebitoOnline(boolean cartaoDebitoOnline) {
        this.cartaoDebitoOnline = cartaoDebitoOnline;
    }

    public TipoDebitoOnlineEnum getTipoDebitoOnline() {
        return tipoDebitoOnline;
    }

    public void setTipoDebitoOnline(TipoDebitoOnlineEnum tipoDebitoOnline) {
        this.tipoDebitoOnline = tipoDebitoOnline;
    }

    public String getMerchantid() {
        if (merchantid == null) {
            merchantid = "";
        }
        return merchantid;
    }

    public void setMerchantid(String merchantid) {
        this.merchantid = merchantid;
    }

    public String getMerchantkey() {
        if (merchantkey == null) {
            merchantkey = "";
        }
        return merchantkey;
    }

    public void setMerchantkey(String merchantkey) {
        this.merchantkey = merchantkey;
    }

    public boolean getApresentarCamposMerchant() {
        return getTipoDebitoOnline() != null && (getTipoDebitoOnline().equals(TipoDebitoOnlineEnum.CIELO) || getTipoDebitoOnline().equals(TipoDebitoOnlineEnum.E_REDE));
    }
    
    public List<FormaPagamentoEmpresaVO> getFormasEmpresas() {
        return formasEmpresas;
    }

    public void setFormasEmpresas(List<FormaPagamentoEmpresaVO> formasEmpresas) {
        this.formasEmpresas = formasEmpresas;
    }

    public List<FormaPagamentoPerfilAcessoVO> getFormasPerfilAcesso() {
        if (formasPerfilAcesso == null) {
            formasPerfilAcesso = new ArrayList<FormaPagamentoPerfilAcessoVO>();
        }
        return formasPerfilAcesso;
    }

    public void setFormasPerfilAcesso(List<FormaPagamentoPerfilAcessoVO> formasPerfilAcesso) {
        this.formasPerfilAcesso = formasPerfilAcesso;
    }

    public TipoParceiroEnum getTipoParceiro() {
        return tipoParceiro;
    }

    public void setTipoParceiro(TipoParceiroEnum tipoParceiro) {
        this.tipoParceiro = tipoParceiro;
    }

    public boolean isGerarPontos() {
        return gerarPontos;
    }

    public void setGerarPontos(boolean gerarPontos) {
        this.gerarPontos = gerarPontos;
    }

    public boolean isApresentarGerarPontos() {
        return !getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla());
    }

    public boolean isApresentarTipoParceiro() {
        return getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla()) || isGerarPontos();
    }

    public boolean isParceiroFidelidade() {
        return getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla());
    }

    public List<PinPadVO> getListaPinPad() {
        if(listaPinPad == null){
            listaPinPad = new ArrayList<PinPadVO>();
        }
        return listaPinPad;
    }

    public void setListaPinPad(List<PinPadVO> listaPinPad) {
        this.listaPinPad = listaPinPad;
    }

    public boolean validarPerfilAcesso(PerfilAcessoVO perfilUsuarioLogado) {
        for (FormaPagamentoPerfilAcessoVO fppaVO : getFormasPerfilAcesso()) {
            if (fppaVO.getPerfilAcessoVO().getCodigo().equals(perfilUsuarioLogado.getCodigo())) {
                return true;
            }
        }
        return getFormasPerfilAcesso().isEmpty();
    }

    public TipoConvenioCobrancaEnum getTipoConvenioCobranca() {
        if (tipoConvenioCobranca == null) {
            tipoConvenioCobranca = TipoConvenioCobrancaEnum.NENHUM;
        }
        return tipoConvenioCobranca;
    }

    public void setTipoConvenioCobranca(TipoConvenioCobrancaEnum tipoConvenioCobranca) {
        this.tipoConvenioCobranca = tipoConvenioCobranca;
    }

    public PinPadVO getPinpad() {
        if(pinpad == null){
            pinpad = new PinPadVO();
        }
        return pinpad;
    }

    public boolean isApresentarAdicionarPinPad() {
        return this.getPinpad().getOpcoesPinpadEnum() != null &&
                (this.getPinpad().getOpcoesPinpadEnum().equals(OpcoesPinpadEnum.STONE_CONNECT) ||
                        this.getPinpad().getOpcoesPinpadEnum().equals(OpcoesPinpadEnum.CAPPTA) ||
                this.getPinpad().getOpcoesPinpadEnum().equals(OpcoesPinpadEnum.GETCARD));
    }

    public void setPinpad(PinPadVO pinpad) {
        this.pinpad = pinpad;
    }

    public boolean isReceberSomenteViaPinPad() {
        return receberSomenteViaPinPad;
    }

    public void setReceberSomenteViaPinPad(boolean receberSomenteViaPinPad) {
        this.receberSomenteViaPinPad = receberSomenteViaPinPad;
    }

    public Integer getNrMaxParcelasPinpad() {
        if (nrMaxParcelasPinpad == null) {
            nrMaxParcelasPinpad = 1;
        }
        return nrMaxParcelasPinpad;
    }

    public void setNrMaxParcelasPinpad(Integer nrMaxParcelasPinpad) {
        this.nrMaxParcelasPinpad = nrMaxParcelasPinpad;
    }

    public String getPosIDGeoitd() {
        return posIDGeoitd;
    }

    public void setPosIDGeoitd(String posIDGeoitd) {
        this.posIDGeoitd = posIDGeoitd;
    }

    public String getSystemIdGeoitd() {
        return systemIdGeoitd;
    }

    public void setSystemIdGeoitd(String systemIdGeoitd) {
        this.systemIdGeoitd = systemIdGeoitd;
    }

    public long getQuantidadeAlunos() {
        return quantidadeAlunos;
    }

    public void setQuantidadeAlunos(long quantidadeAlunos) {
        this.quantidadeAlunos = quantidadeAlunos;
    }

    public double getTaxaPix() {
        return taxaPix;
    }

    public String getTaxaPixApresentar() {
        if (isTaxaPixValorAbsoluto()) {
            return "R$ " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(taxaPix);
        }
        return taxaPix + "%";
    }

    public void setTaxaPix(double taxaPix) {
        this.taxaPix = taxaPix;
    }

    public JSONObject toJSON() {
        JSONObject json =  new JSONObject();
        json.put("codigo", this.getCodigo());
        json.put("descricao", this.getDescricao());
        json.put("tipoFormaPagamento", this.getTipoFormaPagamentoEnum().toJSON());
        json.put("convenioCobranca", this.getConvenioCobrancaVO() != null && this.getConvenioCobrancaVO().getCodigo() != 0  ? this.getConvenioCobrancaVO().toJSON(this.getTipoConvenioCobranca()) : null);
        json.put("defaultRecorrencia", this.isApresentarDefaultRecorrencia());
        json.put("cartaoDebitoOnline", this.isCartaoDebitoOnline());
        json.put("apresentarCodigoAutorizacao", this.isApresentarCodigoAutorizacao());
        json.put("defaultDCO", this.getDefaultDCO());
        json.put("somenteFinanceiro", this.isSomenteFinanceiro());
        json.put("compensacaoDiasUteis", this.isCompensacaoDiasUteis());
        json.put("ativo", this.isAtivo());
        json.put("apresentarNSU", this.isApresentarNSU());
        json.put("cor", this.getCor());
        json.put("gerarPontos", this.isGerarPontos());
        json.put("perfilsDeAcesso", formasPerfilAcessoToJSON(this.getFormasPerfilAcesso()));

        return json;
    }

    private JSONArray formasPerfilAcessoToJSON(List<FormaPagamentoPerfilAcessoVO> formasPerfilAcesso) {
        JSONArray formasPefilAcessoJSON = new JSONArray();
        for(FormaPagamentoPerfilAcessoVO formaPagamentoPerfilAcessoVO: formasPerfilAcesso){
            formasPefilAcessoJSON.put(formaPagamentoPerfilAcessoVO.getPerfilAcessoVO().toJSON());
        }

        return formasPefilAcessoJSON;
    }

    public boolean isStoneConnect() {
        return stoneConnect;
    }

    public void setStoneConnect(boolean stoneConnect) {
        this.stoneConnect = stoneConnect;
    }

    public boolean isGetCard() {
        return getCard;
    }

    public void setGetCard(boolean getCard) {
        this.getCard = getCard;
    }

    public boolean isTaxaPixValorAbsoluto() {
        return taxaPixValorAbsoluto;
    }

    public void setTaxaPixValorAbsoluto(boolean taxaPixValorAbsoluto) {
        this.taxaPixValorAbsoluto = taxaPixValorAbsoluto;
    }

    public String getLabelSimboloTaxaPix() {
        if (isTaxaPixValorAbsoluto()) {
            return "R$";
        }
        return "%";
    }

    public boolean isContaDeConsumo() {
        return contaDeConsumo;
    }

    public void setContaDeConsumo(boolean contaDeConsumo) {
        this.contaDeConsumo = contaDeConsumo;
    }

    public void registrarObjetoVOAntesDaAlteracao() {
        super.registrarObjetoVOAntesDaAlteracao();
        if (getFormasPerfilAcesso() != null) {
            List<FormaPagamentoPerfilAcessoVO> lista = new ArrayList<>();
            lista.addAll(getFormasPerfilAcesso());
            ((FormaPagamentoVO)getObjetoVOAntesAlteracao()).setFormasPerfilAcesso(lista);
        }
    }
}
