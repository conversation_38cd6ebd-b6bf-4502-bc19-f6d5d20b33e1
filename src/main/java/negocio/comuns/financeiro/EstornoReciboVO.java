package negocio.comuns.financeiro;

import br.com.pactosolucoes.ce.negocio.facade.jdbc.negociacao.NegociacaoEvento;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteClienteComposicao;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.Cheque;
import negocio.facade.jdbc.financeiro.ExtratoDiarioItem;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.facade.jdbc.financeiro.RemessaItemMovParcela;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.interfaces.financeiro.MovPagamentoInterfaceFacade;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class EstornoReciboVO extends SuperVO {

    protected ReciboPagamentoVO reciboPagamentoVO;
    protected UsuarioVO responsavelEstornoRecivo;
    protected List<MovParcelaVO> listaMovParcela;
    protected List<MovPagamentoVO> listaMovPagamento;
    private List<TransacaoVO> listaTransacoes = new ArrayList();
    private List<RemessaItemVO> listaItensRemessa;
    private boolean estornarOperadora = true;
    private boolean excluirNFSe = false; // atributo transient
    private boolean mostrarMsgExcluirNFse = false; // atributo transient
    private boolean validarNFSeProdutosPagos = true; // notas de referentes a compentencia podem ou não ser excluídas. Por padrão sim, pois já era um comportamento do sistema

    public EstornoReciboVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
//        setListaMovPagamento(new ArrayList<MovPagamentoVO>());
//        setListaMovParcela(new ArrayList<MovParcelaVO>());
//        setResponsavelEstornoRecivo(new UsuarioVO());
//        setReciboPagamentoVO(new ReciboPagamentoVO());
    }

    public void alterarSituacaoMovParcela(Connection con) throws Exception {
        MovParcela movParcelaDAO;
        MovProdutoParcela movProdutoParcelaDAO;
        try {
            movParcelaDAO = new MovParcela(con);
            movProdutoParcelaDAO = new MovProdutoParcela(con);

            MovParcelaVO parcelaCancelados = null;
            for (MovParcelaVO parcela : getListaMovParcela()) {
                if (parcela.getSituacao().equals("PG")
                        || parcela.getSituacao().equals("EA")) { // situação EA pode acontecer  e é necessário atualizar os movprodutoparcelas
                    parcela.setSituacao("EA");
                    parcela.setMovProdutoParcelaVOs(movProdutoParcelaDAO.consultarMovProdutoParcelasPorParcelas(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    parcelaCancelados = alterarSituacaoMovProdutoParcelaEMovProduto(parcela, parcelaCancelados, con);
                    movParcelaDAO.alterarSemCommit(parcela);
                }
            }
            if (parcelaCancelados != null) {
                parcelaCancelados.setDescricao("ALTERAÇÕES - RECIBO " + this.getReciboPagamentoVO().getCodigo());
                parcelaCancelados.setDataRegistro(Calendario.hoje());
                parcelaCancelados.setDataVencimento(Calendario.hoje());
                movParcelaDAO.incluirParcelaSemCommit(parcelaCancelados);
                for (MovProdutoParcelaVO mpp : parcelaCancelados.getMovProdutoParcelaVOs()) {
                    mpp.setMovParcela(parcelaCancelados.getCodigo());
                }
                movProdutoParcelaDAO.alterarMovProdutoParcelas(parcelaCancelados.getCodigo(), parcelaCancelados.getMovProdutoParcelaVOs());
            }
        } finally {
            movParcelaDAO = null;
            movProdutoParcelaDAO = null;
        }
    }

    public MovParcelaVO alterarSituacaoMovProdutoParcelaEMovProduto(MovParcelaVO parcela, MovParcelaVO parcelaCancelados,
                                                                    Connection con) throws Exception {
        MovProduto movProdutoDAO;
        MovProdutoParcela movProdutoParcelaDAO;
        try {
            movProdutoDAO = new MovProduto(con);
            movProdutoParcelaDAO = new MovProdutoParcela(con);

            Iterator i = parcela.getMovProdutoParcelaVOs().iterator();
            List<MovProdutoParcelaVO> mppNovos = new ArrayList<MovProdutoParcelaVO>();
            List<MovProdutoParcelaVO> mppCancelados = new ArrayList<MovProdutoParcelaVO>();
            MovProdutoVO produto;
            Double valorInicial = Uteis.arredondarForcando2CasasDecimais(parcela.getValorParcela());
            while (i.hasNext()) {
                MovProdutoParcelaVO movProdutoParcela = (MovProdutoParcelaVO) i.next();
                movProdutoParcela.setReciboPagamento(new ReciboPagamentoVO());
                movProdutoParcelaDAO.alterarSomenteReciboPagamentoSemCommit(movProdutoParcela);
                produto = movProdutoDAO.consultarPorChavePrimaria(movProdutoParcela.getMovProduto(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                movProdutoParcela.setMovProdutoVO(produto);
                if (produto.getSituacao().equals("CA")) {
                    mppCancelados.add(movProdutoParcela);
                    parcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(parcela.getValorParcela() - movProdutoParcela.getValorPago()));
                } else {
                    mppNovos.add(movProdutoParcela);
                    movProdutoDAO.alterarSomenteSituacaoSemCommit(movProdutoParcela.getMovProduto(), "EA");
                }
            }
            parcela.setMovProdutoParcelaVOs(mppNovos);
            if (mppNovos.isEmpty()) {
                parcela.setValorParcela(valorInicial);
                parcela.setMovProdutoParcelaVOs(mppCancelados);
                parcela.setSituacao("CA");
                return parcelaCancelados;
            }
            if (!mppCancelados.isEmpty()) {
                if (parcelaCancelados == null) {
                    parcelaCancelados = (MovParcelaVO) parcela.getClone(true);
                    parcelaCancelados.setSituacao("CA");
                    parcelaCancelados.setValorParcela(0.0);
                    parcelaCancelados.setMovProdutoParcelaVOs(new ArrayList<MovProdutoParcelaVO>());
                    parcelaCancelados.setNovoObj(true);
                }
                parcelaCancelados.setValorParcela(Uteis.arredondarForcando2CasasDecimais(parcelaCancelados.getValorParcela() + (valorInicial - parcela.getValorParcela())));
                parcelaCancelados.getMovProdutoParcelaVOs().addAll(mppCancelados);
            }
            return parcelaCancelados;
        } finally {
            movProdutoDAO = null;
            movProdutoParcelaDAO = null;
        }
    }

    public static void validarSeExisteMovimentoContaCorrente(EstornoReciboVO estornoReciboVO, Connection con) throws Exception {
        MovimentoContaCorrenteClienteVO atual = new MovimentoContaCorrenteClienteVO();
        MovimentoContaCorrenteClienteVO novo = new MovimentoContaCorrenteClienteVO();
        novo.setValor(new Double(0));
        boolean inserir = false;
        MovimentoContaCorrenteCliente movimentoContaCorrenteClienteDAO;
        MovPagamento movPagamentoDAO;
        Cheque chequeDAO;
        CartaoCredito cartaoCreditoDAO;
        MovimentoContaCorrenteClienteComposicao movimentoContaCorrenteClienteComposicaoDAO;
        try {
            movimentoContaCorrenteClienteDAO = new MovimentoContaCorrenteCliente(con);
            movPagamentoDAO = new MovPagamento(con);
            chequeDAO = new Cheque(con);
            cartaoCreditoDAO = new CartaoCredito(con);
            movimentoContaCorrenteClienteComposicaoDAO = new MovimentoContaCorrenteClienteComposicao(con);

            for (MovPagamentoVO pagamento : estornoReciboVO.getListaMovPagamento()) {
                if (pagamento.getValor() < pagamento.getValorTotal()) {
                    movimentoContaCorrenteClienteDAO.estornarCreditosMovpagamento(pagamento.getCodigo(), estornoReciboVO.getResponsavelEstornoRecivo(), estornoReciboVO.getReciboPagamentoVO().getCodigo());
                } else if (pagamento.getCredito()) {
                    if(!verificarMovpagamentoOrigem(pagamento.getMovPagamentoOrigemCredito(), estornoReciboVO.getListaMovPagamento() )){
                              inserir = true;
                          pagamento.setReciboPagamento(new ReciboPagamentoVO());
                          pagamento.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
                          pagamento.setProdutosPagos("");
                        pagamento.setProdutosPagosCancelados("");
                        movPagamentoDAO.alterarSemCommit(pagamento);
                          if(pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")){
                              for(ChequeVO cheque : pagamento.getChequeVOs()){
                                  cheque.setProdutosPagos("");
                                  chequeDAO.alterar(cheque);
                              }
                          }
                          if(pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")){
                              for(CartaoCreditoVO cartao : pagamento.getCartaoCreditoVOs()){
                                  cartao.setProdutosPagos("");
                                  cartaoCreditoDAO.alterar(cartao);
                              }
                              
                          }
                          novo.getMovPagamentosVOs().add(pagamento);
                          novo.setValor(novo.getValor() + pagamento.getValor());
                    }
                } else if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                    atual = movimentoContaCorrenteClienteDAO.consultarPorCodigoPessoa(pagamento.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    boolean alterarProduto = true;
                    if(atual != null){
                        for (MovPagamentoVO pagContaCorrente : atual.getMovPagamentosVOs()) {
                            if (pagamento.getCodigo().intValue() == pagContaCorrente.getCodigo().intValue()) {
                                alterarProduto = false;
                                movimentoContaCorrenteClienteComposicaoDAO.excluirComposicaoMovimento(atual.getCodigo());
                                List novosPagamentos = new ArrayList<MovPagamentoVO>();
                                for (MovPagamentoVO pagMovimento : atual.getMovPagamentosVOs()) {
                                    if (pagMovimento.getCodigo().intValue() != pagamento.getCodigo().intValue()) {
                                        novosPagamentos.add(pagMovimento);
                                    }
                                }
                                atual.setMovPagamentosVOs(novosPagamentos);
                                atual.setValor(pagamento.getValor());
                                atual.setSaldoAnterior(atual.getSaldoAtual());
                                atual.setSaldoAtual(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(atual.getSaldoAtual() + atual.getValor()));
                                atual.setTipoMovimentacao("CR");
                                atual.setDescricao("ESTORNO DO RECIBO: " + estornoReciboVO.getReciboPagamentoVO().getCodigo());
                                atual.setResponsavelAutorizacao(estornoReciboVO.getResponsavelEstornoRecivo());
                                movimentoContaCorrenteClienteDAO.incluirSemCommit(atual);
                                break;
                            }
                        }
                    }
                    if (alterarProduto) {
                        estornarFormaPagamentoContaCorrente(pagamento, estornoReciboVO, con);
                    }

                }
            }

            if (inserir && novo.getValor() > 0) {
                novo.setResponsavelAutorizacao(estornoReciboVO.getResponsavelEstornoRecivo());
                novo.setPessoa(estornoReciboVO.getReciboPagamentoVO().getPessoaPagador());
                novo.setNovoObj(true);
                novo.setTipoMovimentacao("CR");
                novo.setDescricao("ESTORNO DO RECIBO N°: " + estornoReciboVO.getReciboPagamentoVO().getCodigo());
                novo.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
                novo.setContratoOperacaoVO(new ContratoOperacaoVO());
                movimentoContaCorrenteClienteDAO.adicionarmovimento(novo);
            }
        } catch (Exception e) {
            if(e.getMessage().startsWith("ERRO:")){
                throw new Exception(e.getMessage().replace("ERRO:", ""));
            }
            if(e.getMessage().toUpperCase().contains("PERMISSÃO")){
                throw new Exception(e.getMessage());
            }
            throw new Exception("Para realizar essa ação, primeiro estorne o recibo "+estornoReciboVO.getReciboPagamentoVO().getCodigo());
        } finally {
            movimentoContaCorrenteClienteDAO = null;
            movPagamentoDAO = null;
            chequeDAO = null;
            cartaoCreditoDAO = null;
            movimentoContaCorrenteClienteComposicaoDAO = null;
        }

    }

    private static boolean verificarMovpagamentoOrigem(
			Integer movPagamentoOrigemCredito,
			List<MovPagamentoVO> listaMovPagamento) {
    	 for (MovPagamentoVO pagamento : listaMovPagamento) {
    		 if (movPagamentoOrigemCredito.equals(pagamento.getCodigo())){
    			 return true;
    		 }
    	 }
		return false;
	}

    private static void estornarFormaPagamentoContaCorrente(MovPagamentoVO pagamento, EstornoReciboVO estornoReciboVO,
                                                            Connection con) throws Exception{
        List<BoletoVO> listaBoletosCancelar = new ArrayList<>();
        MovProduto movProdutoDAO;
        MovParcela movParcelaDAO;
        MovProdutoParcela movProdutoParcelaDAO;
        VendaAvulsa vendaAvulsaDAO;
        ClienteMensagem clienteMensagemDAO;
        Boleto boletoDAO = null;
        try {
            movProdutoDAO = new MovProduto(con);
            movParcelaDAO = new MovParcela(con);
            movProdutoParcelaDAO = new MovProdutoParcela(con);
            vendaAvulsaDAO = new VendaAvulsa(con);
            clienteMensagemDAO = new ClienteMensagem(con);
            boletoDAO = new Boleto(con);

            List produtos = movProdutoDAO.consultarPorMovPagamentoContaCorrente(pagamento.getCodigo().toString(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator i = produtos.iterator();
            if (!produtos.isEmpty()) {
                while (i.hasNext()) {
                    MovProdutoVO produto = (MovProdutoVO) i.next();
                    String[] pagamentosDebito = produto.getMovpagamentocc().split(",");
                    for (int j = 0; j < pagamentosDebito.length; j++) {
                        if (Integer.parseInt(pagamentosDebito[j]) == pagamento.getCodigo().intValue()) {
                            if (Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal()) > Uteis.arredondarForcando2CasasDecimais(pagamento.getValor())) {
                                String novoMovPagamentoCC = "";
                                for (int k = 0; k < pagamentosDebito.length; k++) {
                                    if (Integer.parseInt(pagamentosDebito[k]) != pagamento.getCodigo().intValue()) {
                                        if (novoMovPagamentoCC.equals("")) {
                                            novoMovPagamentoCC = pagamentosDebito[k];
                                        } else {
                                            novoMovPagamentoCC += "," + pagamentosDebito[k];
                                        }

                                    }
                                }
                                produto.setMovpagamentocc(novoMovPagamentoCC);
                                Double novoValor = Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal() - pagamento.getValor());
                                produto.setTotalFinal(novoValor);
                                produto.setTotalFinal(novoValor);
                                Iterator p = produto.getMovProdutoParcelaVOs().iterator();
                                while (p.hasNext()) {
                                    MovProdutoParcelaVO produtoParcela = (MovProdutoParcelaVO) p.next();
                                    produtoParcela.setValorPago(novoValor);
                                    MovParcelaVO parcela = movParcelaDAO.consultarPorChavePrimaria(produtoParcela.getMovParcela(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    verificarParcelaEmRemessa(parcela, con);
                                    parcela.setValorParcela(novoValor);
                                    parcela.setMovPagamentoCC(novoMovPagamentoCC);
                                    movParcelaDAO.alterarSemCommit(parcela);
                                    movProdutoParcelaDAO.alterar(produtoParcela);
                                }
                                movProdutoDAO.alterarSemCommit(produto);
                                break;
                            } else {
                                EstornoMovProdutoVO estornoProduto = new EstornoMovProdutoVO();
                                estornoProduto.getClienteVO().setPessoa(produto.getPessoa());
                                estornoProduto.setMovProdutoVO(produto);
                                estornoProduto.getListaMovProduto().add(produto);
                                estornoProduto.setResponsavelEstorno(estornoReciboVO.getResponsavelEstornoRecivo());
                                MovParcelaVO movParcela = movParcelaDAO.consultarPorMovProduto(estornoProduto.getMovProdutoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_MINIMOS);
                                verificarParcelaEmRemessa(movParcela, con);
                                VendaAvulsaVO vendaAvulsa = new VendaAvulsaVO();
                                vendaAvulsa.setCodigo(movParcela.getVendaAvulsaVO().getCodigo().intValue());
                                movProdutoDAO.estornarMovProdutoSemCommit(estornoProduto, null);
                                vendaAvulsaDAO.excluirSemCommit(vendaAvulsa, false);
                                
                                break;
                            }
                        }
                    }

                }
            } else { // parcelas de pagamento de débito que foram renegociadas
                MovParcelaVO parcela = movParcelaDAO.consultarPorMovPagamentoContaCorrente(pagamento.getCodigo().toString(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (parcela != null) {
					verificarParcelaEmRemessa(parcela, con);
                    parcela.setMovProdutoParcelaVOs(movProdutoParcelaDAO.consultarPorCodigoMovParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                    String[] pagamentosDebito = parcela.getMovPagamentoCC().split(",");
                    for (int j = 0; j < pagamentosDebito.length; j++) {
                        if (Integer.parseInt(pagamentosDebito[j]) == pagamento.getCodigo().intValue()) {
                            if (Uteis.arredondarForcando2CasasDecimais(parcela.getValorParcela()) > Uteis.arredondarForcando2CasasDecimais(pagamento.getValor())) {
                                String novoMovPagamentoCC = "";
                                for (int k = 0; k < pagamentosDebito.length; k++) {
                                    if (Integer.parseInt(pagamentosDebito[k]) != pagamento.getCodigo().intValue()) {
                                        if (novoMovPagamentoCC.equals("")) {
                                            novoMovPagamentoCC = pagamentosDebito[k];
                                        } else {
                                            novoMovPagamentoCC += "," + pagamentosDebito[k];
                                        }

                                    }
                                }
                                parcela.setMovPagamentoCC(novoMovPagamentoCC);
                                Double novoValor = Uteis.arredondarForcando2CasasDecimais(parcela.getValorParcela() - pagamento.getValor());
                                Iterator p = parcela.getMovProdutoParcelaVOs().iterator();
                                while (p.hasNext()) {
                                    MovProdutoParcelaVO produtoParcela = (MovProdutoParcelaVO) p.next();
                                    produtoParcela.setValorPago(novoValor);
                                    MovProdutoVO produto = movProdutoDAO.consultarPorChavePrimaria(produtoParcela.getMovProduto(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    produto.setTotalFinal(novoValor);
                                    produto.setPrecoUnitario(novoValor);
                                    produto.setMovpagamentocc(novoMovPagamentoCC);
                                    movProdutoDAO.alterarSemCommit(produto);
                                    movProdutoParcelaDAO.alterar(produtoParcela);
                                }
                                movParcelaDAO.alterarSemCommit(parcela);
                                break;
                            } else {
                                MovProdutoVO produto = new MovProdutoVO();
                                try {
                                    produto = (MovProdutoVO) movProdutoDAO.consultarPorCodigoParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).get(0);
                                } catch (Exception e) {
                                }
                                if(!UteisValidacao.emptyNumber(produto.getCodigo())){
                                    if (Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal()) > Uteis.arredondarForcando2CasasDecimais(pagamento.getValor())) {
                                        Iterator p = produto.getMovProdutoParcelaVOs().iterator();
                                        boolean produtopago = true;
                                        while (p.hasNext()) {
                                            MovProdutoParcelaVO produtoParcela = (MovProdutoParcelaVO) p.next();
                                            if (produtoParcela.getMovParcela().equals(parcela.getCodigo())) {
                                                produto.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal() - pagamento.getValor()));
                                                produto.setTotalFinal(produto.getPrecoUnitario());
                                                movProdutoParcelaDAO.excluir(produtoParcela);
                                                
                                            } else if(UteisValidacao.emptyNumber(produtoParcela.getReciboPagamento().getCodigo())) {
                                                produtopago = false;
                                            }
                                        }
                                        if(produtopago){
                                            produto.setSituacao("PG");
                                        }
                                        movProdutoDAO.alterarSemCommit(produto);
                                    } else {
                                        EstornoMovProdutoVO estornoProduto = new EstornoMovProdutoVO();
                                        estornoProduto.getClienteVO().setPessoa(produto.getPessoa());
                                        estornoProduto.setMovProdutoVO(produto);
                                        estornoProduto.getListaMovProduto().add(produto);
                                        estornoProduto.setResponsavelEstorno(estornoReciboVO.getResponsavelEstornoRecivo());
                                        movProdutoDAO.estornarMovProdutoSemCommit(estornoProduto, null);
                                    }
                                }
                                listaBoletosCancelar.addAll(boletoDAO.excluirBoletoMovParcela(parcela, estornoReciboVO.getResponsavelEstornoRecivo()));
                                movParcelaDAO.excluirSemCommit(parcela);
                                clienteMensagemDAO.excluirClienteMensagemPorMovParcela(parcela.getCodigo());
                                break;
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            listaBoletosCancelar = new ArrayList<>();
            throw e;
        } finally {
            if (!UteisValidacao.emptyList(listaBoletosCancelar)) {
                try {
                    if (boletoDAO == null) {
                        boletoDAO = new Boleto(con);
                    }
                    boletoDAO.cancelarBoletos(listaBoletosCancelar, estornoReciboVO.getResponsavelEstornoRecivo(), "EstornoContrato");
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            movProdutoDAO = null;
            movParcelaDAO = null;
            movProdutoParcelaDAO = null;
            vendaAvulsaDAO = null;
            clienteMensagemDAO = null;
            boletoDAO = null;
        }
    }

    public void excluirProdutosPagamentoDebito(Connection con) throws Exception {
        MovParcela movParcelaDAO;
        MovProduto movProdutoDAO;
        Boleto boletoDAO;
        try {
            movParcelaDAO = new MovParcela(con);
            movProdutoDAO = new MovProduto(con);
            boletoDAO = new Boleto(con);

            List movparcelas = movParcelaDAO.consultarPorCodigoRecibo(getReciboPagamentoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator i = movparcelas.iterator();
            Map<Integer, Double> mapValorProduto = new HashMap<Integer, Double>();
            while (i.hasNext()) {
                MovParcelaVO parcela = (MovParcelaVO) i.next();
                if (parcela.getMovPagamentoCC() != null && !parcela.getMovPagamentoCC().equals("")) {
                    Iterator j = movProdutoDAO.consultarPorCodigoParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).iterator();
                    while (j.hasNext()) {
                        MovProdutoVO produto = (MovProdutoVO) j.next();
                        if (Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal()) == Uteis.arredondarForcando2CasasDecimais(parcela.getCodigo())) {// debito pago por apenas uma parcela
                            movProdutoDAO.excluirSemCommit(produto);
                        } else { // debito pago por mais de uma parcela(renegociacao)
                            if (!mapValorProduto.containsKey(produto.getCodigo())) {
                                mapValorProduto.put(produto.getCodigo(), 0.0);
                            }
                            mapValorProduto.put(produto.getCodigo(), Uteis.arredondarForcando2CasasDecimais(mapValorProduto.get(produto.getCodigo()) + parcela.getValorParcela()));
                            if (Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal()) == Uteis.arredondarForcando2CasasDecimais(mapValorProduto.get(produto.getCodigo()))) { // todas as parcelas que pagam o débito estão sendo estornadas
                                movProdutoDAO.excluirSemCommit(produto);
                                mapValorProduto.remove(produto.getCodigo());
                            }
                        }
                    }
                    boletoDAO.estornarMovParcela(parcela.getCodigo(), getResponsavelEstornoRecivo(), "excluirProdutosPagamentoDebito");
                    movParcelaDAO.excluirSemCommit(parcela);
                }
            }
            for (Map.Entry<Integer, Double> entrySet : mapValorProduto.entrySet()) {
                MovProdutoVO produto = movProdutoDAO.consultarPorChavePrimaria(entrySet.getKey(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                produto.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(produto.getPrecoUnitario() - entrySet.getValue()));
                produto.setTotalFinal(produto.getPrecoUnitario());
                if (produto.getSituacao().equals("EA") && Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal()) ==
                        Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago())) { //unica parcela que faltava para pagar o débito foi estornada
                    produto.setSituacao("PG");
                }
                movProdutoDAO.alterarSemCommit(produto);

            }
        } finally {
            movParcelaDAO = null;
            movProdutoDAO = null;
            boletoDAO = null;
        }
    }

    public void excluirMovPagamento(Connection con) throws Exception {
        NegociacaoEvento negociacaoEventoDAO;
        MovPagamento movPagamentoDAO;
        try {
            negociacaoEventoDAO = new NegociacaoEvento(con);
            movPagamentoDAO = new MovPagamento(con);
            for (MovPagamentoVO pagamento : getListaMovPagamento()) {
                if (pagamento.getReciboPagamento().getCodigo() != 0) {
                    negociacaoEventoDAO.excluirPagamentoSemCommit(pagamento);
                    movPagamentoDAO.removerMovPagamentoNfeEmitida(pagamento);
                    movPagamentoDAO.excluirSemCommit(pagamento, true);
                }
            }
        } finally {
            negociacaoEventoDAO = null;
            movPagamentoDAO = null;
        }
    }

    public void excluirReferenciaMovPagamentoExtratoDiarioItem(Connection con) throws Exception {
        ExtratoDiarioItem extratoDiarioItemDAO;
        try {
            extratoDiarioItemDAO = new ExtratoDiarioItem(con);
            for (MovPagamentoVO pagamento : getListaMovPagamento()) {
                if (pagamento.getReciboPagamento().getCodigo() != 0) {
                    if (!UteisValidacao.emptyNumber(pagamento.getCodigo())) {
                        extratoDiarioItemDAO.excluirMovPagamento(pagamento.getCodigo());
                    }
                }
            }
        } finally {
            extratoDiarioItemDAO = null;
        }
    }

    public List<LogVO> criarLogEstornoRecibo(MovPagamentoInterfaceFacade movPagamentoFacade,
                                             MovProdutoParcela movProdutoParcelaFacade, Connection con) throws Exception {
        Transacao transacaoDAO = null;
        RemessaItem remessaItemDAO = null;
        try {
            transacaoDAO = new Transacao(con);
            remessaItemDAO = new RemessaItem(con);

            List<LogVO> listaLog = new ArrayList<LogVO>();
            LogVO obj = new LogVO();
            montarLogReciboPagamento(listaLog, obj);
            montarLogMovPagamento(listaLog, obj, movPagamentoFacade);
            montarLogMovParcela(listaLog, obj, movProdutoParcelaFacade);
            transacaoDAO.gerarLogEstornarTransacoes(this.isEstornarOperadora(), this.getReciboPagamentoVO().getPessoaPagador().getCodigo(), getResponsavelEstornoRecivo(), "RECIBO");
            transacaoDAO.estornarTransacoes(this.estornarOperadora, obj, getListaTransacoes(), getResponsavelEstornoRecivo(), false);
            remessaItemDAO.estornarItensRemessa(obj, listaItensRemessa, false, getResponsavelEstornoRecivo());
            listaLog.add(obj);
            return listaLog;
        } finally {
            transacaoDAO = null;
            remessaItemDAO = null;
        }
    }

    public void montarLogReciboPagamento(List<LogVO> listaLog, LogVO obj) throws Exception {
        String pessoaPagador = "NULL";
        String contrato = "NULL";
        obj.setChavePrimaria(getReciboPagamentoVO().getCodigo().toString());
        obj.setNomeEntidade("RECIBOPAGAMENTO");
        obj.setNomeEntidadeDescricao("Recibo Pagamento");
        obj.setOperacao("ESTORNO - RECIBO PAGAMENTO");
        obj.setResponsavelAlteracao(getResponsavelEstornoRecivo().getNome());
        obj.setUserOAMD(getResponsavelEstornoRecivo().getUserOamd());
        obj.setNomeCampo("TODOS");
        if (getReciboPagamentoVO().getPessoaPagador() != null && getReciboPagamentoVO().getPessoaPagador().getCodigo().intValue() != 0) {
            pessoaPagador = getReciboPagamentoVO().getPessoaPagador().getCodigo().toString();
        }
        if (getReciboPagamentoVO().getContrato() != null && getReciboPagamentoVO().getContrato().getCodigo().intValue() != 0) {
            contrato = getReciboPagamentoVO().getContrato().getCodigo().toString();
        }
        obj.setValorCampoAlterado("--------------------------------------\n\r");
        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "Empresa = " + getReciboPagamentoVO().getEmpresa_Apresentar() + "\n\rcódigo do recibo = " + getReciboPagamentoVO().getCodigo() + "\n\r" + "data entrada no caixa = " + getReciboPagamentoVO().getData_Apresentar() + "\n\r" + "valor Total = R$ " + Uteis.getDoubleFormatado(getReciboPagamentoVO().getValorTotal()) + "\n\r" + "nome do Cliente = " + getReciboPagamentoVO().getNomePessoaPagador() + "\n\r" + "codigo do cliente = " + pessoaPagador + "\n\r" + "responsável lançamento = " + getReciboPagamentoVO().getResponsavelLancamento().getNome() + "\n\r" + "contrato= " + contrato + "\n\r");
        obj.setValorCampoAnterior("");
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
    }

    public void montarLogMovPagamento(List<LogVO> listaLog, LogVO obj, MovPagamentoInterfaceFacade movPagamentoFacade) throws Exception {
        String pessoa = "NULL";
        for (MovPagamentoVO mp : getListaMovPagamento()) {
            mp = movPagamentoFacade.consultarPorChavePrimaria(mp.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            if (mp.getPessoa() != null && mp.getPessoa().getCodigo().intValue() != 0) {
                pessoa = mp.getPessoa().getCodigo().toString();
            }
            obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "\n\r --------------------------------------\n\r");
            obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "Empresa = " + getReciboPagamentoVO().getEmpresa_Apresentar() + "\n\rcódigo do pagamento = " + mp.getCodigo() + "\n\r" + "data do pagamento = " + mp.getDataPagamento_Apresentar() + "\n\r" + "data do lançamento = " + mp.getDataLancamento_Apresentar() + "\n\r" + "valor pago= R$ " + Uteis.getDoubleFormatado(mp.getValor()) + "\n\r" + "nome do pagador = " + mp.getNomePagador() + "\n\r" + "código do pagador = " + pessoa + "\n\r" + "código forma de pagamento = " + mp.getFormaPagamento().getCodigo() + "\n\r" + "descrição forma de pagamento = " + mp.getFormaPagamento().getDescricao() + "\n\r" + "código responsável pagamento = " + mp.getResponsavelPagamento().getCodigo() + "\n\r" + "nome responsável pagamento = " + mp.getResponsavelPagamento().getCodigo() + "\n\r" + "código recibo pagamento = " + mp.getReciboPagamento().getCodigo() + "\n\r" + "nrParcelaCartaoCredito = " + mp.getNrParcelaCartaoCredito() + "\n\r" + "autorização cartão = " + mp.getAutorizacaoCartao() + "\n\r" + "nsu = " + mp.getNsu() + "\n\r");
        }
    }

    public void montarLogMovParcela(List<LogVO> listaLog, LogVO obj, MovProdutoParcela movProdutoParcelaFacade) throws Exception {
        String contrato = "";
        for (MovParcelaVO parcela : getListaMovParcela()) {
            parcela.setMovProdutoParcelaVOs(movProdutoParcelaFacade.consultarMovProdutoParcelasPorParcelas(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (parcela.getContrato() != null) {
                contrato = parcela.getContrato().getCodigo().toString();
            }
            obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "\n\r --------------------------------------\n\r");
            obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "Empresa = " + getReciboPagamentoVO().getEmpresa_Apresentar() + "\n\rcódigo da parcela = " + parcela.getCodigo() + " \n\rdescrição parcela = " + parcela.getDescricao() + " \n\rnome do aluno = " + parcela.getPessoa().getNome() + " \n\rvalor da parcela = R$ " + Uteis.getDoubleFormatado(parcela.getValorParcela()) + " \n\rnumero contrato = " + contrato + " \n\rsituação = Em Aberto");
        }
    }

    /**
     * Separar as transações de cartão de crédito associadas às parcelas
     * informadas como argumento, para que o estorno do contrato
     * possa retirar as dependências e manter o histórico das transações.
     * @param listParc
     * @throws Exception
     */
    public void montarListaTransacoes(List<MovParcelaVO> listParc, Connection con) throws Exception {
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(con);
            this.setListaTransacoes(transacaoDAO.obterListaTransacoesEstorno(listParc));
        } finally {
            transacaoDAO = null;
        }
    }

    public void montarListaItensRemessa(List<MovParcelaVO> listParc, List<MovPagamentoVO> listaPags, Connection con) throws Exception {
        RemessaItem remessaItemDAO;
        RemessaItemMovParcela remessaItemMovParcelaDAO;
        MovPagamento movPagamentoDAO;
        try {
            remessaItemDAO = new RemessaItem(con);
            remessaItemMovParcelaDAO = new RemessaItemMovParcela(con);
            movPagamentoDAO = new MovPagamento(con);

            Set<RemessaItemVO> itens = new HashSet<RemessaItemVO>();
            HashMap<String, RemessaItemVO> map = new HashMap<String, RemessaItemVO>();

            for (MovParcelaVO parcela : listParc) {
                itens.addAll(remessaItemDAO.consultarPorCodigoParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                for (RemessaItemMovParcelaVO obj : remessaItemMovParcelaDAO.consultarPorParcelaItem(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS)) {
                    map.put(obj.getRemessaItemVO().getCodigo().toString(), obj.getRemessaItemVO());
                }
            }

            for (MovPagamentoVO pagamento : listaPags) {
                itens.addAll(remessaItemDAO.consultarPorMovPagamento(pagamento.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }

            itens.addAll(map.values());

            List<RemessaItemVO> listaFinal = new ArrayList<>();
            for (RemessaItemVO remessaItemVO : itens) {
                remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(remessaItemVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                remessaItemVO.setMovParcelas(remessaItemMovParcelaDAO.consultarPorRemessaItem(remessaItemVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                if (remessaItemVO.getMovPagamento() != null && !UteisValidacao.emptyNumber(remessaItemVO.getMovPagamento().getCodigo())) {
                    remessaItemVO.setMovPagamento(movPagamentoDAO.consultarPorChavePrimaria(remessaItemVO.getMovPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
                listaFinal.add(remessaItemVO);
            }

            List<RemessaItemVO> list = new ArrayList<RemessaItemVO>(listaFinal);
            Ordenacao.ordenarListaReverse(list, "codigo");
            setListaItensRemessa(list);
        } finally {
            remessaItemDAO = null;
            remessaItemMovParcelaDAO = null;
            movPagamentoDAO = null;
        }
    }

    public List<MovParcelaVO> getListaMovParcela() {
        if (listaMovParcela == null) {
            listaMovParcela = new ArrayList<MovParcelaVO>();
        }
        return listaMovParcela;
    }

    public void setListaMovParcela(List<MovParcelaVO> listaMovParcela) {
        this.listaMovParcela = listaMovParcela;
    }

    public List<MovPagamentoVO> getListaMovPagamento() {
        if (listaMovPagamento == null) {
            listaMovPagamento = new ArrayList<MovPagamentoVO>();
        }
        return listaMovPagamento;
    }

    public void setListaMovPagamento(List<MovPagamentoVO> listaMovPagamento) {
        this.listaMovPagamento = listaMovPagamento;
    }

    public UsuarioVO getResponsavelEstornoRecivo() {
        if (responsavelEstornoRecivo == null) {
            responsavelEstornoRecivo = new UsuarioVO();
        }
        return responsavelEstornoRecivo;
    }

    public void setResponsavelEstornoRecivo(UsuarioVO responsavelEstornoRecivo) {
        this.responsavelEstornoRecivo = responsavelEstornoRecivo;
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        if (reciboPagamentoVO == null) {
            reciboPagamentoVO = new ReciboPagamentoVO();
        }
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    public Boolean getApresentarDadosContrato() {
        if (getReciboPagamentoVO().getContrato() != null && getReciboPagamentoVO().getContrato().getCodigo().intValue() != 0) {
            return true;
        }
        return false;
    }

    public boolean getApresentarNomePorContrato(MovParcelaVO obj) {
        if (obj.getContrato() != null && obj.getContrato().getCodigo().intValue() != 0) {
            return true;
        }
        return false;
    }

    public boolean getApresentarNomePorVendaAvulsa(MovParcelaVO obj) {
        if (obj.getVendaAvulsaVO() != null && obj.getVendaAvulsaVO().getCodigo().intValue() != 0) {
            return true;
        }
        return false;
    }

    public boolean getApresentarNomePorAulaAvulsaDiaria(MovParcelaVO obj) {
        if (obj.getAulaAvulsaDiariaVO() != null && obj.getAulaAvulsaDiariaVO().getCodigo().intValue() != 0) {
            return true;
        }
        return false;
    }

    public List<TransacaoVO> getListaTransacoes() {
        return listaTransacoes;
    }

    public void setListaTransacoes(List<TransacaoVO> listaTransacoes) {
        this.listaTransacoes = listaTransacoes;
    }

    public List<RemessaItemVO> getListaItensRemessa() {
        return listaItensRemessa;
    }

    public void setListaItensRemessa(List<RemessaItemVO> listaItensRemessa) {
        this.listaItensRemessa = listaItensRemessa;
    }

    public boolean isEstornarOperadora() {
        return estornarOperadora;
    }

    public void setEstornarOperadora(boolean estornarOperadora) {
        this.estornarOperadora = estornarOperadora;
    }

    public boolean isExcluirNFSe() {
        return excluirNFSe;
    }

    public void setExcluirNFSe(boolean excluirNFSe) {
        this.excluirNFSe = excluirNFSe;
    }

    public boolean isMostrarMsgExcluirNFse() {
        return mostrarMsgExcluirNFse;
    }

    public void setMostrarMsgExcluirNFse(boolean mostrarMsgExcluirNFse) {
        this.mostrarMsgExcluirNFse = mostrarMsgExcluirNFse;
    }

    public boolean isValidarNFSeProdutosPagos() {
        return validarNFSeProdutosPagos;
    }

    public void setValidarNFSeProdutosPagos(boolean validarNFSeProdutosPagos) {
        this.validarNFSeProdutosPagos = validarNFSeProdutosPagos;
    }
    
    private static void verificarParcelaEmRemessa(MovParcelaVO movParcela, Connection con) throws Exception {
        RemessaItem remessaItemDAO;
        try {
            remessaItemDAO = new RemessaItem(con);

            List<RemessaItemVO> lista = remessaItemDAO.consultarPorCodigoParcela(movParcela.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (RemessaItemVO item : lista) {
                if (item.getRemessa().getSituacaoRemessa() == SituacaoRemessaEnum.REMESSA_ENVIADA) {
                    throw new Exception("ERRO: Não é possível realizar o estorno, pois a parcela " + movParcela.getCodigo() + " está em uma remessa aguardando retorno");
                } else {
                    item.setMovParcela(new MovParcelaVO());
                    remessaItemDAO.alterar(item);
                }
            }
        } finally {
            remessaItemDAO = null;
        }
    }
}
