/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

/**
 * <AUTHOR>
 */
public enum OpcoesPinpadEnum {

    CAPPTA(1, "Linx"),
    GEOITD(2, "Geoitd"),
    STONE_CONNECT(3, "Stone Connect"),
    GETCARD(4, "GetCard");

    private Integer codigo;
    private String nome;

    private OpcoesPinpadEnum(Integer codigo, String nome) {
        this.codigo = codigo;
        this.nome = nome;
    }

    public static OpcoesPinpadEnum fromCodigo(Integer codigo) {
        for (OpcoesPinpadEnum value : values()) {
            if (value.getCodigo().equals(codigo)) {
                return value;
            }
        }
        return null;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }


}
