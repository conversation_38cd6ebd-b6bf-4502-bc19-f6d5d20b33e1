package negocio.comuns.financeiro.enumerador;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 24/02/2025.
 */

public enum RetornoAbecsBandeirasEnum {

// TODO documentação: https://api.abecs.org.br/wp-content/uploads/2023/04/20230406-Normativo-21-V3-aprovado-publicacao.pdf

    // CÓD APROVADAS
    APROVADA_0("0", "Transação autorizada", true, null),
    APROVADA_0000("0000", "Transação autorizada", true, null),

    ELO_5("5", "Genérica. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_05("05", "Genérica. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_51("51", "Saldo/Limite insuficiente", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_55("55", "Senha inválida", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_14("14", "Número do cartão não pertence ao emissor", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_56("56", "Número do cartão não pertence ao emissor", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_63("63", "Violação de segurança", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_59("59", "Suspeita de fraude/aviso de viagem. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_58("58", "Comerciante inválido", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_4("4", "Favor refazer a transação", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_04("04", "Favor refazer a transação", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_6("6", "Favor consultar credenciador. Lojista contate a adquirente.", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_06("06", "Favor consultar credenciador. Lojista contate a adquirente.", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_19("19", "Problema no adquirente", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_12("12", "Erro no cartão. Verifique os dados do cartão", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_30("30", "Erro de formato", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_13("13", "Valor da transação inválido", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_23("23", "Valor da parcela inválido. Parcelamento inválido", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_38("38", "Excedidas tentativas de senha. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_41("41", "Cartão perdido", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_43("43", "Cartão roubado", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_54("54", "Cartão expirado/vencido. Verifique os dados do cartão", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_61("61", "Valor excessivo para saque. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_62("62", "Bloqueio temporário. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_64("64", "Valor mínimo da transação inválido", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_65("65", "Quantidade de saques excedida. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_83("83", "Senha vencida/inválida", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_75("75", "Excedidas tentativas de senha. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_76("76", "Conta destino inválida ou inexistente", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_77("77", "Conta origem inválida ou inexistente", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_78("78", "Cartão novo sem desbloqueio", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_82("82", "Cartão inválido (criptograma)", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_91("91", "Emissor fora do ar. Falha na comunicação, favor tentar mais tarde", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_96("96", "Falha do sistema", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_AB("AB", "Função incorreta (Débito). Utilize a função crédito", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_AC("AC", "Função incorreta (Crédito). Utilize a função débito", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_P5("P5", "Troca de senha / Desbloqueio", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_P6("P6", "Senha inválida, utilize a nova senha", true, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_57("57", "Fraude confirmada ou fraude confirmada ou transação negada por infração de lei", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_46("46", "Conta encerrada", false, OperadorasExternasAprovaFacilEnum.ELO),
    ELO_FM("FM", "Utilizar o Chip", false, OperadorasExternasAprovaFacilEnum.ELO),

    // VISA
    VISA_5("5", "Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_05("05", "Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_51("51", "Saldo/limite insuficiente", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_55("55", "Senha inválida", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_86("86", "Senha inválida", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_57("57", "Transação não permitida para o cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_14("14", "Número do cartão não pertence ao emissor", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_63("63", "Violação de segurança", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_N7("N7", "Violação de segurança", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_59("59", "Suspeita de fraude/aviso de viagem. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_3("3", "Comerciante inválido", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_03("03", "Comerciante inválido", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_19("19", "Problema no adquirente", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_06("06", "Erro no cartão. Verifique os dados do cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_6("6", "Erro no cartão. Verifique os dados do cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_12("12", "Erro de formato", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_13("13", "Valor da transação inválido", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_75("75", "Excedidas tentativas de senha. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_41("41", "Cartão perdido", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_43("43", "Cartão roubado", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_54("54", "Cartão expirado/vencido. Verifique os dados do cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_58("58", "Transação não permitida para o cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_61("61", "Valor excessivo para saque. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_N4("N4", "Valor excessivo para saque. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_62("62", "Bloqueio temporário. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_65("65", "Quantidade de saques excedida. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_74("74", "Senha vencida/inválida", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_81("81", "Senha vencida/inválida", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_78("78", "Cartão novo sem desbloqueio", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_82("82", "Cartão inválido (criptograma)", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_91("91", "Emissor fora do ar. Falha na comunicação, favor tentar mais tarde", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_96("96", "Falha do sistema", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_N8("N8", "Valor diferente da pré-autorização", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_94("94", "Diferença na pré-autorização ou Valor do tracing data duplicado. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_52("52", "Função incorreta (Débito). Utilize a função crédito", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_53("53", "Função incorreta (Débito). Utilize a função crédito", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_39("39", "Função incorreta (Crédito). Utilize a função débito", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_4("4", "Recolher cartão. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_04("04", "Recolher cartão. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_7("7", "Fraude confirmada", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_07("07", "Fraude confirmada", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_15("15", "Emissor não localizado, BIN incorreto. Dados do cartão inválido, não tente novamente", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_64("64", "Não cumprimento pelas leis de lavagem de dinheiro. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_76("76", "Reversão inválida. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_92("92", "Não localizado pelo roteador. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_93("93", "Transação negada por infração de lei. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_46("46", "Conta encerrada", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_B1("B1", "Surcharge não suportado. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_B2("B2", "Surcharge não suportado pela rede de débito. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_N0("N0", "Forçar Stip. Contate a central do seu cartão. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_N3("N3", "Saque não disponível. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_R0("R0", "Suspensão de pagamento recorrente para um serviço", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_R1("R1", "Suspensão de pagamento recorrente para todos os serviços", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_R2("R2", "Transação não qualificada para VISA PIN", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_R3("R3", "Suspensão de todas as ordens de autorização", false, OperadorasExternasAprovaFacilEnum.VISA),
    VISA_6P("6P", "Falha validação de ID", false, OperadorasExternasAprovaFacilEnum.VISA),

    // MASTERCARD
    MASTERCARD_5("5", "Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_05("05", "Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_51("51", "Saldo/limite insuficiente", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_55("55", "Senha inválida ou nova senha não aceita", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_86("86", "Senha inválida", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_57("57", "Transação não permitida para o cartão ou Quantidade de saques excedida ou Cartão novo sem desbloqueio ou bloqueio temporário. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_14("14", "Número do cartão não pertence ao emissor", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_1("1", "Número do cartão não pertence ao emissor", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_01("01", "Número do cartão não pertence ao emissor", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_63("63", "Violação de segurança ou suspeita de fraude/aviso de viagem. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_3("3", "Comerciante inválido", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_03("03", "Comerciante inválido", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_30("30", "Problema no adquirente ou erro no formato", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_13("13", "Valor mínimo da transação inválido", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_75("75", "Excedidas tentativas de senha ou saque. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_41("41", "Cartão perdido", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_43("43", "Cartão roubado", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_54("54", "Cartão expirado/vencido. Verifique os dados do cartão", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_58("58", "Transação não permitida para o cartão", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_61("61", "Valor excessivo para saque. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_65("65", "Quantidade de saques excedida. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_88("88", "Senha vencida/inválida ou Cartão inválido", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_91("91", "Emissor fora do ar. Falha na comunicação, favor tentar mais tarde", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_96("96", "Falha do sistema", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_4("4", "Recolher cartão ou fraude confirmada. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_04("04", "Recolher cartão ou fraude confirmada. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_15("15", "Emissor não localizado, BIN incorreto. Dados do cartão inválido, não tente novamente", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_92("92", "Não localizado pelo roteador. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_62("62", "Transação negada por infração de lei ou Conta encerrada", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_94("94", "Valor do tracing data duplicado. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_12("12", "Valor da parcela inválida. Parcelamento inválido", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_83("83", "Cartão bloqueado. Contate a central do seu cartão ou desbloqueie no app do cartão", true, OperadorasExternasAprovaFacilEnum.MASTERCARD),
    MASTERCARD_82("82", "Cartão inválido", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),

    //MASTERCAR NAO INFORMADO DOCUMENTO ABECS - NORMATIVA-21
    MASTERCARD_78("78", "Retorno da bandeira: O cartão utilizado é débito, utilize um cartão com função CRÉDITO.", false, OperadorasExternasAprovaFacilEnum.MASTERCARD),

    //HIPERCARD
    HIPERCARD_5("5", "Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_05("05", "Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_51("51", "Saldo/limite insuficiente", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_55("55", "Senha inválida ou nova senha não aceita", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_86("86", "Senha inválida", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_57("57", "Transação não permitida para o cartão ou Quantidade de saques excedida ou Cartão novo sem desbloqueio. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_14("14", "Número do cartão não pertence ao emissor", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_1("1", "Número do cartão não pertence ao emissor", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_01("01", "Número do cartão não pertence ao emissor", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_63("63", "Violação de segurança ou suspeita de fraude/aviso de viagem", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_3("3", "Comerciante inválido", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_03("03", "Comerciante inválido", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_30("30", "Problema no adquirente ou erro no formato", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_13("13", "Valor mínimo da transação inválido", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_75("75", "Excedidas tentativas de senha ou saque. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_41("41", "Cartão perdido", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_43("43", "Cartão roubado", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_54("54", "Cartão expirado/vencido. Verifique os dados do cartão", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_58("58", "Transação não permitida para o cartão", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_61("61", "Valor excessivo para saque. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_65("65", "Quantidade de saques excedida. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_88("88", "Senha vencida/inválida ou Cartão inválido", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_91("91", "Emissor fora do ar. Falha na comunicação, favor tentar mais tarde", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_96("96", "Falha do sistema", true, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_4("4", "Recolher cartão ou fraude confirmada", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_04("04", "Recolher cartão ou fraude confirmada", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_15("15", "Emissor não localizado, BIN incorreto. Dados do cartão inválido, não tente novamente", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_92("92", "Não localizado pelo roteador. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_62("62", "Transação negada por infração de lei ou Conta encerrada", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_94("94", "Valor do tracing data duplicado. Contate a central do seu cartão", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),
    HIPERCARD_12("12", "Valor da parcela inválida. Parcelamento inválido", false, OperadorasExternasAprovaFacilEnum.HIPERCARD),


    //AMERICAN EXPRESS (AMEX)
    AMEX_100("100", "Genérico ou Suspeita de fraude/aviso de viagem. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_116("116", "Saldo/limite insuficiente", true, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_117("117", "Senha inválida", true, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_200("200", "Cartão perdido ou Cartão roubado ou Transação não permitida para o cartão ou fraude confirmada", true, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_122("122", "Número do cartão não pertence ao emissor ou violação de segurança", false, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_109("109", "Comerciante inválido", false, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_115("115", "Erro no cartão ou Valor da parcela inválida. Verifique os dados do cartão e o valor", false, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_181("181", "Erro no formato", false, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_110("110", "Valor da transação inválido", false, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_106("106", "Excedidas tentativas de senha ou saque. Contate a central do seu cartão", true, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_101("101", "Cartão expirado/vencido. Verifique os dados do cartão", false, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_180("180", "Senha vencida/inválida ou Cartão inválido", false, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_912("912", "Emissor fora do ar. Falha na comunicação, favor tentar mais tarde", true, OperadorasExternasAprovaFacilEnum.AMEX),
    AMEX_911("911", "Falha do sistema", true, OperadorasExternasAprovaFacilEnum.AMEX),
    ;


    private final String codigo;
    private final String motivo;
    private final boolean reversivel;
    private final OperadorasExternasAprovaFacilEnum bandeira;

    RetornoAbecsBandeirasEnum(String codigo, String motivo, boolean reversivel, OperadorasExternasAprovaFacilEnum bandeira) {
        this.codigo = codigo;
        this.motivo = motivo;
        this.reversivel = reversivel;
        this.bandeira = bandeira;
    }

    public static String obterMotivoPorCodigoEBandeira(String codigo, OperadorasExternasAprovaFacilEnum bandeira) {
        if (codigo.equals("0000") || codigo.equals("0")) {
            return obterAprovadaPorCodigo(codigo).getMotivo();
        }
        for (RetornoAbecsBandeirasEnum retorno : RetornoAbecsBandeirasEnum.values()) {
            if (retorno.getCodigo().equals(codigo) && retorno.getBandeira().getId() == bandeira.getId()) {
                return retorno.getMotivo();
            }
        }
        return "Motivo não informado para a bandeira.";
    }

    public static RetornoAbecsBandeirasEnum obterPorCodigoEBandeira(String codigo, OperadorasExternasAprovaFacilEnum bandeira) {
        if (codigo.equals("0000") || codigo.equals("0")) {
            return obterAprovadaPorCodigo(codigo);
        }
        for (RetornoAbecsBandeirasEnum retorno : RetornoAbecsBandeirasEnum.values()) {
            if (retorno.getCodigo().equals(codigo) && retorno.getBandeira().getId() == bandeira.getId()) {
                return retorno;
            }
        }
        return null; // Retorna null se não encontrar
    }

    public static RetornoAbecsBandeirasEnum obterAprovadaPorCodigo(String codigoAprovada) {
        for (RetornoAbecsBandeirasEnum retorno : RetornoAbecsBandeirasEnum.values()) {
            if (retorno.getCodigo().equals(codigoAprovada)) {
                return retorno;
            }
        }
        return null; // Retorna null se não encontrar
    }

    public boolean isRetornoPorFaltaDeLimite(RetornoAbecsBandeirasEnum retorno) {
        return retorno.equals(RetornoAbecsBandeirasEnum.ELO_51) ||
                retorno.equals(RetornoAbecsBandeirasEnum.VISA_51) ||
                retorno.equals(RetornoAbecsBandeirasEnum.MASTERCARD_51) ||
                retorno.equals(RetornoAbecsBandeirasEnum.HIPERCARD_51) ||
                retorno.equals(RetornoAbecsBandeirasEnum.AMEX_116);
    }

    public boolean isRetornoPorCartaoVencido(RetornoAbecsBandeirasEnum retorno) {
        return retorno.equals(RetornoAbecsBandeirasEnum.MASTERCARD_54) ||
                retorno.equals(RetornoAbecsBandeirasEnum.VISA_54) ||
                retorno.equals(RetornoAbecsBandeirasEnum.ELO_54) ||
                retorno.equals(RetornoAbecsBandeirasEnum.HIPERCARD_54) ||
                retorno.equals(RetornoAbecsBandeirasEnum.AMEX_101);
    }

    public boolean isRetornoPorErroEstabelecimentoOuCredencial(RetornoAbecsBandeirasEnum retorno) {
        return isRetornoComercianteInvalido(retorno) || isRetornoProblemaNaAdquirente(retorno);
    }

    public boolean isRetornoComercianteInvalido(RetornoAbecsBandeirasEnum retorno) {
        return retorno.equals(RetornoAbecsBandeirasEnum.ELO_58) ||
                retorno.equals(RetornoAbecsBandeirasEnum.VISA_3) ||
                retorno.equals(RetornoAbecsBandeirasEnum.VISA_03) ||
                retorno.equals(RetornoAbecsBandeirasEnum.MASTERCARD_3) ||
                retorno.equals(RetornoAbecsBandeirasEnum.MASTERCARD_03) ||
                retorno.equals(RetornoAbecsBandeirasEnum.HIPERCARD_3) ||
                retorno.equals(RetornoAbecsBandeirasEnum.HIPERCARD_03) ||
                retorno.equals(RetornoAbecsBandeirasEnum.AMEX_109);
    }

    public boolean isRetornoProblemaNaAdquirente(RetornoAbecsBandeirasEnum retorno) {
        return retorno.equals(RetornoAbecsBandeirasEnum.ELO_19) ||
                retorno.equals(RetornoAbecsBandeirasEnum.VISA_19) ||
                retorno.equals(RetornoAbecsBandeirasEnum.MASTERCARD_30) ||
                retorno.equals(RetornoAbecsBandeirasEnum.HIPERCARD_30);
    }

    public String getCodigo() {
        return codigo;
    }

    public String getMotivo() {
        return motivo;
    }

    public boolean isReversivel() {
        return reversivel;
    }

    public OperadorasExternasAprovaFacilEnum getBandeira() {
        return bandeira;
    }
}
