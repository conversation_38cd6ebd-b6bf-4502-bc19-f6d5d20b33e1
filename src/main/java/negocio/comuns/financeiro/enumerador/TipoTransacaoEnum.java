/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import negocio.comuns.financeiro.*;
import negocio.comuns.utilitarias.Ordenacao;
import servicos.impl.mundiPagg.TransacaoMundiPaggVO;
import servicos.impl.onepayment.TransacaoOnePaymentVO;
import servicos.impl.pagarMe.TransacaoPagarMeVO;
import servicos.impl.pagbank.TransacaoPagBankVO;
import servicos.impl.stone.TransacaoStoneOnlineVO;
import servicos.impl.stripe.TransacaoStripeVO;
import servicos.impl.pinbank.TransacaoPinBankVO;
import servicos.impl.stoneV5.TransacaoStoneOnlineV5VO;


import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public enum TipoTransacaoEnum {

//   NÃO ALTERAR CODIGOS POIS É UTILIZADO PARA DESENHAR A TELA DE GESTAO DE TRANSAÇÃO

    NENHUMA(0, "Nenhuma", new TransacaoVO()),
    AprovaFacilCB(1, "Aprova Fácil - CobreBem", new TransacaoVO()),
    VINDI(2, "Vindi", new TransacaoVindiVO()),
    CIELO_ONLINE(3, "Cielo", new TransacaoCieloVO()),
    CIELO_DEBITO_ONLINE(5, "Cielo Débito Online", new TransacaoCieloVO()),
    MAXIPAGO(6, "MaxiPago", new TransacaoMaxiPagoVO()),
    E_REDE(7, "Rede", new TransacaoERedeVO()),
    E_REDE_DEBITO(8, "Rede Débito Online", new TransacaoERedeVO()),
    FITNESS_CARD(9, "Fitness Card", new TransacaoFitnessCardVO()),
    GETNET_ONLINE(10, "Getnet", new TransacaoGetNetVO()),
    STONE_ONLINE(11, "Stone", new TransacaoStoneOnlineVO()),
    MUNDIPAGG(12, "Mundipagg", new TransacaoMundiPaggVO()),
    PAGAR_ME(13, "Pagar.me", new TransacaoPagarMeVO()),
    PACTO_PAY(14, "PactoPay", new TransacaoPactoPayVO()),
    STRIPE(15, "Stripe", new TransacaoStripeVO()),
    PAGOLIVRE(16, "PagoLivre", new TransacaoPagoLivreVO()),
    PINBANK(17, "PinBank", new TransacaoPinBankVO()),
    ONE_PAYMENT(18, "OnePayment", new TransacaoOnePaymentVO()),
    FACILITEPAY(19, "Fypay", new TransacaoPagoLivreVO()),
    FACILITEPAY_MS(20, "FacilitePayMs", new TransacaoPagoLivreVO()), //momentaneamente TransacaoPagoLivreVO
    CEOPAG(21, "Ceopag", new TransacaoCeopagVO()),
    DCC_CAIXA_ONLINE(22, "DCC Caixa Online", new TransacaoCaixaVO()),
    PAGBANK(23, "PagBank", new TransacaoPagBankVO()),
    DCC_STONE_ONLINE_V5(24, "Stone Online v5", new TransacaoStoneOnlineV5VO()),
    AFINZ(25, "Afinz", new TransacaoPagoLivreVO()),
    ;

//   NÃO ALTERAR CODIGOS POIS É UTILIZADO PARA DESENHAR A TELA DE GESTAO DE TRANSAÇÃO

    private int id;
    private String descricao;
    private TransacaoVO transacaoVO;

    private static String tipoTransacaoSwitch;

    TipoTransacaoEnum(int id, String descricao, TransacaoVO transacaoVO) {
        this.id = id;
        this.descricao = descricao;
        this.transacaoVO = transacaoVO;
    }

    public String getDescricao() {
        return descricao;
    }

    public int getId() {
        return id;
    }

    public static TipoTransacaoEnum getTipoTransacaoEnum(final int codigo) {
        for (TipoTransacaoEnum situacao : TipoTransacaoEnum.values()) {
            if (situacao.getId() == codigo) {
                return situacao;
            }
        }
        return TipoTransacaoEnum.NENHUMA;
    }

    public static List getSelectListTipoTransacao() {
        List<SelectItem> temp = new ArrayList<SelectItem>();
        for (TipoTransacaoEnum tipo : TipoTransacaoEnum.values()) {
            if (!tipo.equals(TipoTransacaoEnum.NENHUMA) &&
                    !tipo.equals(TipoTransacaoEnum.E_REDE_DEBITO) &&
                    !tipo.equals(TipoTransacaoEnum.PACTO_PAY) &&
                    !tipo.equals(TipoTransacaoEnum.CIELO_DEBITO_ONLINE)) {
                temp.add(new SelectItem(tipo, tipo.getDescricao().toUpperCase()));
            }
        }
        Ordenacao.ordenarLista(temp, "label");
        temp.add(0, new SelectItem(TipoTransacaoEnum.NENHUMA, "(Todas)"));
        return temp;
    }

    public static String getSwitchCaseSSQL(String prefixoTransacao) {
        if (tipoTransacaoSwitch == null) {
            StringBuilder sbSwitch = new StringBuilder();
            sbSwitch.append("CASE\n");
            Arrays.stream(TipoTransacaoEnum.values()).forEach(tipoTransacaoEnum -> {
                sbSwitch.append("WHEN ").append(prefixoTransacao).append(".tipo = ").append(tipoTransacaoEnum.id).append(" THEN '").append(tipoTransacaoEnum.descricao).append("'\n");
            });
            sbSwitch.append("ELSE ''\n");
            sbSwitch.append("END\n");
            tipoTransacaoSwitch = sbSwitch.toString();

        }
        return tipoTransacaoSwitch;
    }

    public TransacaoVO getTransacaoVO() {
        return transacaoVO;
    }
}
