package negocio.comuns.financeiro.enumerador;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import edu.emory.mathcs.backport.java.util.Arrays;
import negocio.comuns.utilitarias.ColecaoUtils;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.reflexao.Propriedade;
import negocio.comuns.utilitarias.reflexao.PropriedadePadrao;
import org.apache.commons.collections.Predicate;
import org.jboleto.JBoleto;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static negocio.comuns.utilitarias.reflexao.PropriedadePadrao.criar;

/**
 * <AUTHOR>
 */
public enum TipoConvenioCobrancaEnum {

    NENHUM(0, "Nenhum", TipoRemessaEnum.DESCONHECIDO, TipoCobrancaEnum.NENHUM, ""),
    BOLETO(1, "Boleto Bancário", TipoRemessaEnum.BOLETO, TipoCobrancaEnum.BOLETO, "./imagens/bandeiras/boleto.png"),
    BOLETO_ITAU(9, "Boleto Bancário Itaú", TipoRemessaEnum.ITAU_BOLETO, JBoleto.ITAU, TipoCobrancaEnum.BOLETO, "./imagens/bandeiras/boleto.png"),

    DCC(2, "DCC Cielo EDI", TipoRemessaEnum.EDI_CIELO, TipoCobrancaEnum.EDI_DCC, "./imagens/bandeiras/cartao.png"),
    DCC_GETNET(8, "DCC Getnet EDI", TipoRemessaEnum.GET_NET, TipoCobrancaEnum.EDI_DCC, "./imagens/bandeiras/cartao.png"),
    DCC_BIN(11, "DCC Bin EDI", TipoRemessaEnum.DCC_BIN, TipoCobrancaEnum.EDI_DCC, "./imagens/bandeiras/cartao.png"),

    DCO_BB(3, "DCO Banco do Brasil", TipoRemessaEnum.BB_DCO, JBoleto.BANCO_DO_BRASIL, TipoCobrancaEnum.EDI_DCO, "./imagens/bancos/bancobrasil.png"),
    DCO_BRADESCO(4, "DCO Bradesco", TipoRemessaEnum.BRADESCO_DCO, JBoleto.BRADESCO, TipoCobrancaEnum.EDI_DCO, "./imagens/bancos/bradesco.png"),
    DCO_ITAU(5, "DCO Itaú", TipoRemessaEnum.ITAU_DCO, JBoleto.ITAU, TipoCobrancaEnum.EDI_DCO, "./imagens/bancos/itau.png"),
    DCO_CAIXA(6, "DCO Caixa (SIACC 150)", TipoRemessaEnum.CAIXA_DCO, JBoleto.CAIXA_ECONOMICA, TipoCobrancaEnum.EDI_DCO, "./imagens/bancos/caixa.png"),
    DCO_HSBC(7, "DCO HSBC", TipoRemessaEnum.HSBC_DCO, JBoleto.HSBC, TipoCobrancaEnum.EDI_DCO, "./imagens/bandeiras/generico.png"),
    DCO_SANTANDER(10, "DCO Santander", TipoRemessaEnum.SANTANDER_DCO, JBoleto.SANTANDER, TipoCobrancaEnum.EDI_DCO, "./imagens/bancos/santander.png"),
    DCC_VINDI(12, "DCC Vindi Online", TipoRemessaEnum.DCC_VINDI, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    DCC_CIELO_ONLINE(13, "DCC Cielo Online", TipoRemessaEnum.DCC_CIELO_ONLINE, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    DCC_MAXIPAGO(15, "DCC MaxiPago Online", TipoRemessaEnum.DCC_MAXIPAGO, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    DCC_E_REDE(16, "DCC Rede Online", TipoRemessaEnum.DCC_E_REDE, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    DCC_FITNESS_CARD(18, "DCC Fitness Card Online", TipoRemessaEnum.DCC_FITNESS_CARD, TipoConvenioPrivateLabelEnum.FITNESS_CARD, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    BOLETO_DAYCOVAL(17, "Boleto Bancário Daycoval", TipoRemessaEnum.DAYCOVAL_BOLETO, JBoleto.ITAU, TipoCobrancaEnum.BOLETO, "./imagens/bandeiras/boleto.png"),
    DCO_BRADESCO_240(19, "DCO Bradesco 240", TipoRemessaEnum.BRADESCO_DCO, TipoCobrancaEnum.EDI_DCO, "./imagens/bancos/bradesco.png"),
    DCC_GETNET_ONLINE(20, "DCC Getnet Online", TipoRemessaEnum.DCC_GETNET_ONLINE, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/generico.png"),
    DCC_STONE_ONLINE(21, "DCC Stone Online", TipoRemessaEnum.DCC_STONE, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    DCO_SANTANDER_150(22, "DCO Santander Layout Febraban 150", TipoRemessaEnum.SANTANDER_DCO, JBoleto.SANTANDER, TipoCobrancaEnum.EDI_DCO, "./imagens/bancos/santander.png"),
    DCO_CAIXA_SICOV(23, "DCO Caixa (SICOV 150)", TipoRemessaEnum.CAIXA_SICOV_DCO, JBoleto.CAIXA_ECONOMICA, TipoCobrancaEnum.EDI_DCO, "./imagens/bancos/caixa.png"),
    BOLETO_PJBANK(24, "Boleto Bancário PJBank", TipoRemessaEnum.DESCONHECIDO, 301, TipoCobrancaEnum.BOLETO_ONLINE, "./imagens/bandeiras/pjbank.png"),
    ITAU(25, "Boleto Bancário Itaú CNAB400", TipoRemessaEnum.ITAU_BOLETO, JBoleto.ITAU, TipoCobrancaEnum.BOLETO, "./imagens/bandeiras/boleto.png"),
    DCC_MUNDIPAGG(26, "DCC Mundipagg Online", TipoRemessaEnum.DCC_MUNDIPAGG, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    DCC_PAGAR_ME(27, "DCC Pagar.me Online", TipoRemessaEnum.DCC_PAGAR_ME, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    PIX_BB(28, "Pix Banco do Brasil", TipoRemessaEnum.PIX, TipoCobrancaEnum.PIX, "./imagens/bandeiras/pix.png"),
    PIX_BRADESCO(29, "Pix Bradesco", TipoRemessaEnum.PIX, TipoCobrancaEnum.PIX, "./imagens/bandeiras/pix.png"),
    PIX_SANTANDER(30, "Pix Santander", TipoRemessaEnum.PIX, TipoCobrancaEnum.PIX, "./imagens/bandeiras/pix.png"),
    DCC_STRIPE(31, "DCC Stripe Online", TipoRemessaEnum.DCC_STRIPE, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    DCC_PAGOLIVRE(32, "DCC PagoLivre Online", TipoRemessaEnum.DCC_PAGOLIVRE, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    BOLETO_ITAU_ONLINE(33, "Boleto Bancário Itaú - Registro Online", TipoRemessaEnum.DESCONHECIDO, JBoleto.ITAU, TipoCobrancaEnum.BOLETO_ONLINE, "./imagens/bandeiras/boleto.png"),
    DCC_PINBANK(34, "DCC PinBank Online", TipoRemessaEnum.DCC_PINBANK, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    DCC_ONE_PAYMENT(35, "DCC One Payment Online", TipoRemessaEnum.DCC_ONE_PAYMENT, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    PINPAD_STONE_CONNECT(36, "PinPad - Stone Connect 2.0 (POS)", TipoRemessaEnum.DESCONHECIDO, TipoCobrancaEnum.PINPAD, "./imagens/bandeiras/cartao.png"),
    PINPAD_GETCARD_SCOPE(37, "PinPad - GetCard (Scope)", TipoRemessaEnum.DESCONHECIDO, TipoCobrancaEnum.PINPAD, "./imagens/bandeiras/cartao.png"),
    PIX_PJBANK(38, "Pix PjBank", TipoRemessaEnum.PIX, TipoCobrancaEnum.PIX, "./imagens/bandeiras/pix.png"),
    BOLETO_ASAAS(39, "Boleto Bancário Asaas", TipoRemessaEnum.DESCONHECIDO, 461, TipoCobrancaEnum.BOLETO_ONLINE, "./imagens/bandeiras/asaas.png"),
    PIX_ASAAS(40, "Pix Asaas", TipoRemessaEnum.PIX, TipoCobrancaEnum.PIX, "./imagens/bandeiras/pix.png"),
    DCC_FACILITEPAY(41, "DCC Fypay Online", TipoRemessaEnum.DCC_FACILITEPAY, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    DCC_FACILITEPAY_MS(42, "DCC FacilitePay MS Online", TipoRemessaEnum.DCC_FACILITEPAY_MS, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    PIX_INTER(43, "Pix Inter", TipoRemessaEnum.PIX, TipoCobrancaEnum.PIX, "./imagens/bandeiras/pix.png"),
    DCC_CEOPAG(44, "DCC CeoPag Online", TipoRemessaEnum.DCC_CEOPAG, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    BOLETO_CAIXA_ONLINE(45, "Boleto Bancário Caixa - Registro Online", TipoRemessaEnum.DESCONHECIDO, JBoleto.CAIXA_ECONOMICA, TipoCobrancaEnum.BOLETO_ONLINE, "./imagens/bancos/caixa.png"),
    DCC_CAIXA_ONLINE(46, "DCC Caixa Online", TipoRemessaEnum.DCC_CAIXA_ONLINE, TipoCobrancaEnum.ONLINE, "./imagens/bancos/caixa.png"),
    BOLETO_BANCO_BRASIL_ONLINE(47, "Boleto Bancário Banco do Brasil - Registro Online", TipoRemessaEnum.BOLETO, JBoleto.BANCO_DO_BRASIL, TipoCobrancaEnum.BOLETO_ONLINE, "./imagens/bancos/bancobrasil.png"),
    DCC_PAGBANK(48, "DCC PagBank Online", TipoRemessaEnum.DCC_PAGBANK, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    PIX_ITAU(49, "Pix Itaú", TipoRemessaEnum.PIX, TipoCobrancaEnum.PIX, "./imagens/bandeiras/pix.png"),
    DCC_STONE_ONLINE_V5(50, "DCC Stone Online v5", TipoRemessaEnum.DCC_STONE_ONLINE_V5, TipoCobrancaEnum.ONLINE, "./imagens/bandeiras/cartao.png"),
    PIX_AFINZ(51, "Pix Afinz", TipoRemessaEnum.PIX, TipoCobrancaEnum.PIX, "./imagens/bandeiras/pix.png"),
    ;

    static {
        //tipoAutorizacao
        NENHUM.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.NENHUM);
        BOLETO.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
        BOLETO_ITAU.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
        ITAU.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
        BOLETO_DAYCOVAL.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
        BOLETO_PJBANK.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
        BOLETO_ITAU_ONLINE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
        BOLETO_CAIXA_ONLINE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
        BOLETO_BANCO_BRASIL_ONLINE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
        BOLETO_ASAAS.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);

        DCC.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_GETNET.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_BIN.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_VINDI.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_CIELO_ONLINE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_MAXIPAGO.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_E_REDE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_FITNESS_CARD.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_GETNET_ONLINE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_STONE_ONLINE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_STONE_ONLINE_V5.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_MUNDIPAGG.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_PAGAR_ME.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_STRIPE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_PAGOLIVRE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_PINBANK.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_ONE_PAYMENT.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_FACILITEPAY.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_FACILITEPAY_MS.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_CEOPAG.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_PAGBANK.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        DCC_CAIXA_ONLINE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);


        DCO_BB.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
        DCO_BRADESCO.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
        DCO_BRADESCO_240.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
        DCO_ITAU.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
        DCO_CAIXA.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
        DCO_CAIXA_SICOV.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
        DCO_HSBC.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
        DCO_SANTANDER.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
        DCO_SANTANDER_150.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);


        PINPAD_STONE_CONNECT.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.NENHUM);
        PINPAD_GETCARD_SCOPE.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.NENHUM);


        //tipoTransacao
        DCC_FITNESS_CARD.setTipoTransacao(TipoTransacaoEnum.FITNESS_CARD);
        DCC_MAXIPAGO.setTipoTransacao(TipoTransacaoEnum.MAXIPAGO);
        DCC_VINDI.setTipoTransacao(TipoTransacaoEnum.VINDI);
        DCC_CIELO_ONLINE.setTipoTransacao(TipoTransacaoEnum.CIELO_ONLINE);
        DCC_E_REDE.setTipoTransacao(TipoTransacaoEnum.E_REDE);
        DCC_GETNET_ONLINE.setTipoTransacao(TipoTransacaoEnum.GETNET_ONLINE);
        DCC_STONE_ONLINE.setTipoTransacao(TipoTransacaoEnum.STONE_ONLINE);
        DCC_STONE_ONLINE_V5.setTipoTransacao(TipoTransacaoEnum.DCC_STONE_ONLINE_V5);
        DCC_MUNDIPAGG.setTipoTransacao(TipoTransacaoEnum.MUNDIPAGG);
        DCC_PAGAR_ME.setTipoTransacao(TipoTransacaoEnum.PAGAR_ME);
        DCC_STRIPE.setTipoTransacao(TipoTransacaoEnum.STRIPE);
        DCC_PAGOLIVRE.setTipoTransacao(TipoTransacaoEnum.PAGOLIVRE);
        DCC_PINBANK.setTipoTransacao(TipoTransacaoEnum.PINBANK);
        DCC_ONE_PAYMENT.setTipoTransacao(TipoTransacaoEnum.ONE_PAYMENT);
        DCC_FACILITEPAY.setTipoTransacao(TipoTransacaoEnum.FACILITEPAY);
        DCC_FACILITEPAY_MS.setTipoTransacao(TipoTransacaoEnum.FACILITEPAY_MS);
        DCC_CEOPAG.setTipoTransacao(TipoTransacaoEnum.CEOPAG);
        DCC_CAIXA_ONLINE.setTipoTransacao(TipoTransacaoEnum.DCC_CAIXA_ONLINE);
        DCC_PAGBANK.setTipoTransacao(TipoTransacaoEnum.PAGBANK);
        PIX_AFINZ.setTipoTransacao(TipoTransacaoEnum.AFINZ);


        BOLETO.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")
                , criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)});
        BOLETO_ITAU.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")
                , criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)});

        ITAU.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")
                , criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)});

        BOLETO_DAYCOVAL.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")
                , criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)});

        DCC_GETNET.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")
                , criar(Propriedade.CONVENIO_HOST_SFTP, "getsftp1.getnet.com.br")
                , criar(Propriedade.CONVENIO_PORTA_SFTP, "22")
                , criar(Propriedade.CONVENIO_DIRETORIO_OUT, "/publico/GA_PROD/PagamentoRecorrente/processados", Propriedade.CONVENIO_USUARIO_SFTP)
                , criar(Propriedade.CONVENIO_DIRETORIO_IN, "/publico/GA_PROD/PagamentoRecorrente", Propriedade.CONVENIO_USUARIO_SFTP)
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL, "/opt/ZW_GETNET/")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL_UPLOAD, "up/")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL_DOWNLOAD, "down/")
                , criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO_CANCELAMENTO, 1)
                , criar(Propriedade.CONVENIO_DIRETORIO_CANCELAMENTO_OUT, "/publico/GA_PROD/CancelamentoEletronico/processados", Propriedade.CONVENIO_USUARIO_SFTP)
                , criar(Propriedade.CONVENIO_DIRETORIO_CANCELAMENTO_IN, "/publico/GA_PROD/CancelamentoEletronico", Propriedade.CONVENIO_USUARIO_SFTP)});

        DCC_BIN.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")
                , criar(Propriedade.CONVENIO_HOST_SFTP, "prod-gw-lac.firstdataclients.com")
                , criar(Propriedade.CONVENIO_PORTA_SFTP, "6522")
                , criar(Propriedade.CONVENIO_USUARIO_SFTP, "LAGW-ECWCD001")
                , criar(Propriedade.CONVENIO_SENHA_SFTP, "rG~WY6fYR")
                , criar(Propriedade.CONVENIO_DIRETORIO_OUT, "/available", Propriedade.CONVENIO_NUMERO_CONTRADO)
                , criar(Propriedade.CONVENIO_DIRETORIO_IN, "/")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL, "/opt/ZW_BIN/")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL_UPLOAD, "up/")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL_DOWNLOAD, "down/")
                , criar(Propriedade.NOME_CHAVE_BIN, "chave_bin_padrao.asc")
                , criar(Propriedade.CHAVE_BIN, "-----BEGIN PGP PUBLIC KEY BLOCK-----\n" +
                "Version: GnuPG v2.0.9 (GNU/Linux)\n" +
                "\n" +
                "mQGiBFu7jWYRBADkiQ4dNX+/9ZsPeKfW8bH6dPBrI87fp9gtlj0uBSKSmfWfu5zU\n" +
                "dt7IUqdLA064i1GY1ls8doZ1p1Q2OstS0zVZJhM2e3/LZhCr+SARksqAVErjz21/\n" +
                "0PCrbd0QN98qkSry+Iqcwq4jrEpvoxgtu0rPHx8SptGUxAAdlrPr3J65YwCg8MKz\n" +
                "kv/dV/R4yj5ShMHFjEd17JcD/jY6gHhpqe3aRQBF/4IybVDOI95bQ894ubJyyxyT\n" +
                "Otgp72yaSu+SO7adPU6P13jj4loQTZJYvH4kHKYObtWFu2+NqKYKwyOL1UcS+c1R\n" +
                "b3ZHF80OtZO69M/4/ksAX9DGzs4uKrhBUMTDuvSsD4HPXkDis1YV+SZK3kLH7hGE\n" +
                "34GVA/9otZCWWCCA/vBpDTDMEUFXoELc3IOYJBTYdGQuYUQBDJouafZQSijjjCVe\n" +
                "mMqTsHgfnD/CLYiQWnhdzLnsMikSC+y8oS7llu5ByrG6xcsa85FbTtz5mrtjy0gH\n" +
                "ewT79eGltqV85YNJ0sroslWofffnbmlMifSPkh8FJD73uywna7RARkQgQnJhc2ls\n" +
                "IEhJU08gKEZpcnN0IERhdGEgQnJhc2lsIEhJU08pIDxmZGJfaGlzb0BmaXJzdGRh\n" +
                "dGEuY29tPohgBBMRAgAgBQJbu41mAhsDBgsJCAcDAgQVAggDBBYCAwECHgECF4AA\n" +
                "CgkQHsecy0xbM8AjmgCZAWor+urwve+bbBruZR6FOGeFD1QAmgMgACz2qjs7hJrF\n" +
                "2k7VH2k/UXO+uQINBFu7jWYQCACU2uL2UW5EX7ftqSUmCXGct18+8Ddg7thQFSHp\n" +
                "BSjWK/KFmO3nWF04afdKJP2d66x52jYBTZQkVI4A/iBU29Q6RzgCYonY9bWa4/6L\n" +
                "RqQGCp5cVR3IhZ4fxcP7PyIXFF0gKjQ9xZrSINNP/p9Dly/jdkGpAt2E1Iip1Cfl\n" +
                "Xf4KzN/lUlZeBrNOYDP1HWJEzW3ekgTBQMom12gU6EjONTqmVdRUYyknG9OFyrhE\n" +
                "wRMPBLNg8bscIGFHoswBnieLrzfeODgxw9IGmviUXB+ocmHfY9qD4BQy+JyYJO3D\n" +
                "pAGYXH5xv9F6amAIqEhupMKPlyFQOvgnBH0+rNS/sq64Qs+fAAMFB/40qiyjDgs/\n" +
                "CDc4aY1eDMSMk4SzVkrgnGhZiC4PoD64RihSAUE12NMyRTpBOLFyqfzUzGDzwYyw\n" +
                "p9OQ9Z5OTnVQVa7FSHsR3xf7MixTwrHQmG1pDjN0X8vpd4BJYf6Nh1KmXt2zK9rG\n" +
                "x8ZUlOQ6qBy2GQ38qNxUrWXOGslntfXiM9YmUaDlRLGuP9stqf83H+/xs6gdI7gC\n" +
                "D9wIu+FWLDhLbQ657kYiBAo+/e+pVoYE9sKDxeHibsVhEX+gbgqYqAjyz+0erMOL\n" +
                "JnS9YirGislPtRwfjA151++EaGzMs0O0GBXHOYeDsHM/tUv4uldLwUGhaXRmgY8K\n" +
                "i2cLEGKhm6hYiEkEGBECAAkFAlu7jWYCGwwACgkQHsecy0xbM8DAfQCfQstcLzQH\n" +
                "tEJgKiDgH48vt1o0oZUAnRO2eSVqjPad2uIADeE8zH8VjYha\n" +
                "=wvxO\n" +
                "-----END PGP PUBLIC KEY BLOCK-----\n")});


        DCC_E_REDE.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")
                , criar(Propriedade.CONVENIO_DIRETORIO_IN, "/")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL, "/opt/ZW_REDE/")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL_UPLOAD, "up/")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL_DOWNLOAD, "down/")});

        DCC.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")
                , criar(Propriedade.CONVENIO_HOST_SFTP, "**************")
                , criar(Propriedade.CONVENIO_PORTA_SFTP, "22")
                , criar(Propriedade.CONVENIO_USUARIO_SFTP, "lxv685")
                , criar(Propriedade.CONVENIO_SENHA_SFTP, "lxv685.123")
                , criar(Propriedade.CONVENIO_DIRETORIO_OUT, "/out")
                , criar(Propriedade.CONVENIO_DIRETORIO_IN, "/in")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL, "/opt/ZW_TIVIT/")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL_UPLOAD, "up/")
                , criar(Propriedade.CONVENIO_DIRETORIO_LOCAL_DOWNLOAD, "down/")});

        DCO_BB.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")});
        DCO_BRADESCO.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")});
        DCO_ITAU.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")});
        DCO_CAIXA.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")});
        DCO_CAIXA_SICOV.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")});
        DCO_HSBC.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")});
        DCO_SANTANDER.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")});

        DCO_BRADESCO_240.setPropriedadesPadrao(new PropriedadePadrao[]{criar(Propriedade.CONVENIO_SEQUENCIAL_ARQUIVO, 1)
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_REMESSA, ".TXT")
                , criar(Propriedade.CONVENIO_EXTANSAO_ARQUIVO_RETORNO, ".TXT")});
    }

    private int codigo;
    private String descricao;
    private TipoRemessaEnum tipoRemessa;
    private TipoAutorizacaoCobrancaEnum tipoAutorizacao;
    private int codigoBanco;
    private PropriedadePadrao[] propriedadesPadrao;
    private TipoConvenioPrivateLabelEnum tipoConvenioPrivateLabel;
    private TipoCobrancaEnum tipoCobranca;
    private String logomarca;
    private TipoTransacaoEnum tipoTransacao;

    private TipoConvenioCobrancaEnum(int codigo, String descricao, TipoRemessaEnum tipoRemessa, TipoCobrancaEnum tipoCobranca, String logomarca) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.tipoRemessa = tipoRemessa;
        this.codigoBanco = 0;
        this.propriedadesPadrao = new PropriedadePadrao[0];
        this.tipoConvenioPrivateLabel = TipoConvenioPrivateLabelEnum.NENHUM;
        this.tipoCobranca = tipoCobranca;
        this.logomarca = logomarca;
    }

    private TipoConvenioCobrancaEnum(int codigo, String descricao, TipoRemessaEnum tipoRemessa, TipoConvenioPrivateLabelEnum tipoConvenioPrivateLabel, TipoCobrancaEnum tipoCobranca, String logomarca) {
        this(codigo, descricao, tipoRemessa, tipoCobranca, logomarca);
        this.tipoConvenioPrivateLabel = tipoConvenioPrivateLabel;
    }

    private TipoConvenioCobrancaEnum(int codigo, String descricao, TipoRemessaEnum tipoRemessa, int codigoBanco, TipoCobrancaEnum tipoCobranca, String logomarca) {
        this(codigo, descricao, tipoRemessa, tipoCobranca, logomarca);
        this.codigoBanco = codigoBanco;
    }

    public String getDocumentacao() {
        if (this.equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) || this.equals(TipoConvenioCobrancaEnum.PIX_PJBANK)) {
            return "https://docs.pjbank.com.br/?version=latest";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
            return "https://developercielo.github.io/manual/cielo-ecommerce#vis%C3%A3o-geral-api-cielo-ecommerce";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
            return "https://developers.getnet.com.br/api";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
            return "https://online.stone.com.br/reference#introducao";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            return "https://www.userede.com.br/desenvolvedores/pt/produto/e-Rede#introducao";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
            return "https://vindi.github.io/api-docs/dist/#/payment_profiles/postV1PaymentProfiles";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
            return "https://docs.mundipagg.com/docs";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
            return "https://docs.pagar.me/docs";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
            return "https://developer.pagbank.com.br/reference/introducao";
        } else if (this.equals(TipoConvenioCobrancaEnum.PIX_BB)) {
            return "https://apoio.developers.bb.com.br/referency";
        } else if (this.equals(TipoConvenioCobrancaEnum.PIX_BRADESCO)) {
            return "https://wspf.banco.bradesco/wsValidadorUniversal/Content/Pdf/Layout_API_PIX.pdf";
        } else if (this.equals(TipoConvenioCobrancaEnum.PIX_INTER)) {
            return "https://developers.inter.co/references/pix";
        } else if (this.equals(TipoConvenioCobrancaEnum.PIX_SANTANDER)) {
            return "https://developer.santander.com.br/api/documentacao/pix-visao-geral#/";
        } else if (this.equals(TipoConvenioCobrancaEnum.PIX_ITAU)) {
            return "https://devportal.itau.com.br/baas/#/catalog/home-product/p/pix";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
            return "https://stripe.com/docs/api";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT)) {
            return "https://secure.nmi.com/merchants/resources/integration/integration_portal.php#integration_overview";
        } else if (this.equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
            return "https://connect-v2.stone.com.br/reference";
        } else if (this.equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) || this.equals(TipoConvenioCobrancaEnum.PIX_ASAAS)) {
            return "https://docs.asaas.com/";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) || this.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY) || this.equals(TipoConvenioCobrancaEnum.PIX_AFINZ)) {
            return "https://api.sbx.pagolivre.com.br/doc";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_CEOPAG)) {
            return "https://ceopag-docs.aditum.com.br/#primeiros-passos";
        } else if (this.equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE)) {
            return "https://www.caixa.gov.br/Downloads/cobranca-caixa/WEBSERVICE-XML-COBRANCA-BANCARIA.pdf";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE)) {
            return "https://dev.softwareexpress.com.br/docs/e-sitef/pagamento-rest-quickstart/";
        } else if (this.equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE)) {
            return "https://googlegroups.com/group/uninfe/attach/1a29a9b24a6a0/Documenta%C3%A7%C3%A3o%20da%20API%20de%20Registros%20de%20Cobran%C3%A7a%20-%20Portugu%C3%AAs.pdf?part=0.1";
        } else if (this.equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE)) {
            return "https://apoio.developers.bb.com.br/sandbox/spec/5f4e6f6cb71fb5001268c96a";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            return "https://docs.pagar.me/reference/introdu%C3%A7%C3%A3o-1";
        } else if (this.equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "https://dev.pinbank.com.br/ManualIntegracao/Home/ManualIntegracao";
        } else {
            return "";
        }
    }

    public static TipoConvenioCobrancaEnum valueOf(final int id) {
        TipoConvenioCobrancaEnum[] lista = TipoConvenioCobrancaEnum.values();
        TipoConvenioCobrancaEnum tce = null;
        for (TipoConvenioCobrancaEnum tipoObjetosCobrarEnum : lista) {
            if (tipoObjetosCobrarEnum.getCodigo() == id) {
                tce = tipoObjetosCobrarEnum;
            }

        }
        return tce;
    }

    public static List<TipoConvenioCobrancaEnum> getConveniosTransacaoOnline() {
        List<TipoConvenioCobrancaEnum> conveniosOnline = new ArrayList<>();
        for (TipoConvenioCobrancaEnum convenio : TipoConvenioCobrancaEnum.values()) {
            if (convenio.getTipoCobranca() == TipoCobrancaEnum.ONLINE) {
                conveniosOnline.add(convenio);
            }
        }
        return conveniosOnline;
    }


    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TipoRemessaEnum getTipoRemessa() {
        return tipoRemessa;
    }

    public void setTipoRemessa(TipoRemessaEnum tipoRemessa) {
        this.tipoRemessa = tipoRemessa;
    }

    public TipoAutorizacaoCobrancaEnum getTipoAutorizacao() {
        return tipoAutorizacao;
    }

    public void setTipoAutorizacao(TipoAutorizacaoCobrancaEnum tipoAutorizacao) {
        this.tipoAutorizacao = tipoAutorizacao;
    }

    public int getCodigoBanco() {
        return codigoBanco;
    }

    public void setCodigoBanco(int codigoBanco) {
        this.codigoBanco = codigoBanco;
    }

    public PropriedadePadrao[] getPropriedadesPadrao() {
        return propriedadesPadrao;
    }

    public void setPropriedadesPadrao(PropriedadePadrao[] propriedadesPadrao) {
        this.propriedadesPadrao = propriedadesPadrao;
    }

    public PropriedadePadrao getPropriedadePadrao(Propriedade prop) {
        final Propriedade key = prop;
        return (PropriedadePadrao) ColecaoUtils.find(
                Arrays.asList(propriedadesPadrao), new Predicate() {
                    @Override
                    public boolean evaluate(Object o) {
                        return ((PropriedadePadrao) o).getPropriedade() == key;
                    }
                });
    }

    public TipoConvenioPrivateLabelEnum getTipoConvenioPrivateLabel() {
        return tipoConvenioPrivateLabel;
    }

    public void setTipoConvenioPrivateLabel(TipoConvenioPrivateLabelEnum tipoConvenioPrivateLabel) {
        this.tipoConvenioPrivateLabel = tipoConvenioPrivateLabel;
    }

    public TipoCobrancaEnum getTipoCobranca() {
        if (tipoCobranca == null) {
            tipoCobranca = TipoCobrancaEnum.NENHUM;
        }
        return tipoCobranca;
    }

    public void setTipoCobranca(TipoCobrancaEnum tipoCobranca) {
        this.tipoCobranca = tipoCobranca;
    }

    public boolean isTransacaoOnline() {
        return getTipoCobranca().equals(TipoCobrancaEnum.ONLINE);
    }

    public String getLogomarca() {
        return logomarca;
    }

    public void setLogomarca(String logomarca) {
        this.logomarca = logomarca;
    }

    public static List<TipoConvenioCobrancaEnum> obterListaTipoAutorizacao(TipoAutorizacaoCobrancaEnum tipoAutorizacaoEnum) {
        List<TipoConvenioCobrancaEnum> lista = new ArrayList<>();
        for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
            if (tipoAutorizacaoEnum != null && tipo.getTipoAutorizacao() != null &&
                    tipo.getTipoAutorizacao().equals(tipoAutorizacaoEnum)) {
                lista.add(tipo);
            }
        }
        return lista;
    }

    public static TipoConvenioCobrancaEnum[] obterArray(TipoAutorizacaoCobrancaEnum tipoAutorizacaoEnum) {
        List<TipoConvenioCobrancaEnum> listaTipo = TipoConvenioCobrancaEnum.obterListaTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
        TipoConvenioCobrancaEnum[] tipos = new TipoConvenioCobrancaEnum[listaTipo.size()];
        return listaTipo.toArray(tipos);
    }

    public static List<Integer> obterCodigosTipoPix() {
        Set<Integer> lista = new HashSet<>();
        for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
            if (tipo.isPix()) {
                lista.add(tipo.getCodigo());
            }
        }
        return new ArrayList<>(lista);
    }

    public static List<Integer> obterCodigosTipoAutorizacao(TipoAutorizacaoCobrancaEnum tipoAutorizacaoEnum) {
        Set<Integer> lista = new HashSet<>();
        for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
            if (tipoAutorizacaoEnum != null && tipo.getTipoAutorizacao() != null &&
                    tipo.getTipoAutorizacao().equals(tipoAutorizacaoEnum)) {
                lista.add(tipo.getCodigo());
            }
        }
        return new ArrayList<>(lista);
    }

    public String getWebhookURL(String key) {
        if (!UteisValidacao.emptyString(key) &&
                (this.getTipoTransacao().equals(TipoTransacaoEnum.MUNDIPAGG) ||
                        this.getTipoTransacao().equals(TipoTransacaoEnum.PAGAR_ME) ||
                        this.getTipoTransacao().equals(TipoTransacaoEnum.CEOPAG) ||
                        this.getTipoTransacao().equals(TipoTransacaoEnum.VINDI))) {
//            return Uteis.getUrlOAMD() + "/prest/transacao/" + this.getTipoTransacao().getId() + "/" + key;
            return Uteis.getUrlAPI() + "/prest/pactopay/transacao/" + this.getTipoTransacao().getId() + "/" + key;
        }
        return "";
    }

    public TipoTransacaoEnum getTipoTransacao() {
        if (tipoTransacao == null) {
            tipoTransacao = TipoTransacaoEnum.NENHUMA;
        }
        return tipoTransacao;
    }

    public void setTipoTransacao(TipoTransacaoEnum tipoTransacao) {
        this.tipoTransacao = tipoTransacao;
    }

    public static List<TipoConvenioCobrancaEnum> obterListaTipoCobranca(TipoCobrancaEnum tipoCobrancaEnum) {
        List<TipoConvenioCobrancaEnum> lista = new ArrayList<>();
        for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
            if (tipoCobrancaEnum != null && tipo.getTipoCobranca() != null &&
                    tipo.getTipoCobranca().equals(tipoCobrancaEnum)) {
                lista.add(tipo);
            }
        }
        return lista;
    }

    public boolean isPix() {
        return this.tipoRemessa.isPix();
    }

    public TipoBoletoEnum getTipoBoleto() {
        if (this.equals(BOLETO_PJBANK)) {
            return TipoBoletoEnum.PJ_BANK;
        } else if (this.equals(BOLETO_ITAU_ONLINE)) {
            return TipoBoletoEnum.ITAU;
        } else if (this.equals(BOLETO_ASAAS)) {
            return TipoBoletoEnum.ASAAS;
        } else if (this.equals(BOLETO_CAIXA_ONLINE)) {
            return TipoBoletoEnum.CAIXA;
        } else if (this.equals(BOLETO_BANCO_BRASIL_ONLINE)) {
            return TipoBoletoEnum.BANCO_BRASIL;
        } else {
            return null;
        }
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("codigo", this.codigo);
        json.put("descricao", this.descricao);
        json.put("nome", this.name());

        return json;
    }

    public String getDownloadAuxiliar() {

        if (this.equals(TipoConvenioCobrancaEnum.PINPAD_GETCARD_SCOPE)) {
            return "https://drive.google.com/drive/folders/13m5uJlnc5aTY7eUFk_OQlXXMN4XvX3yD?usp=sharing";
        } else {
            return "";
        }
    }

    public OpcoesPinpadEnum getOpcoesPinpadEnum() {
        if (this.equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
            return OpcoesPinpadEnum.STONE_CONNECT;
        } else if (this.equals(TipoConvenioCobrancaEnum.PINPAD_GETCARD_SCOPE)) {
            return OpcoesPinpadEnum.GETCARD;
        }
        return null;
    }

    public static TipoConvenioCobrancaEnum obterPorCodigo(final int codigo) {
        for (TipoConvenioCobrancaEnum obj : TipoConvenioCobrancaEnum.values()) {
            if (obj.getCodigo() == codigo) {
                return obj;
            }
        }
        return null;
    }

    public static Integer[] ARRAY_CONVENIOS_DCC = new Integer[]{
            TipoConvenioCobrancaEnum.DCC.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_BIN.getCodigo()};

    public static Integer[] ARRAY_CONVENIOS_DCO = new Integer[]{
            TipoConvenioCobrancaEnum.DCO_ITAU.getCodigo(),
            TipoConvenioCobrancaEnum.DCO_BB.getCodigo(),
            TipoConvenioCobrancaEnum.DCO_SANTANDER.getCodigo(),
            TipoConvenioCobrancaEnum.DCO_BRADESCO_240.getCodigo(),
            TipoConvenioCobrancaEnum.DCO_BRADESCO.getCodigo(),
            TipoConvenioCobrancaEnum.DCO_HSBC.getCodigo(),
            TipoConvenioCobrancaEnum.DCO_CAIXA.getCodigo()};

    public static Integer[] ARRAY_CONVENIOS_DCC_ONLINE = new Integer[]{
            //transação online
            TipoConvenioCobrancaEnum.DCC_VINDI.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_MAXIPAGO.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_E_REDE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_FITNESS_CARD.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_MUNDIPAGG.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_PAGAR_ME.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_PAGBANK.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_STRIPE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_PAGOLIVRE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_FACILITEPAY.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_CEOPAG.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_PINBANK.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE.getCodigo()};

    public static Integer[] ARRAY_CONVENIOS_DCC_RETENTATIVA = new Integer[]{
            TipoConvenioCobrancaEnum.DCC.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_BIN.getCodigo(),
            //transação online
            TipoConvenioCobrancaEnum.DCC_VINDI.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_MAXIPAGO.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_E_REDE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_FITNESS_CARD.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_MUNDIPAGG.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_PAGAR_ME.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_PAGBANK.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_STRIPE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_PAGOLIVRE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_FACILITEPAY.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_CEOPAG.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_PINBANK.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT.getCodigo()};

    public boolean isBoletoOnline() {
        return this.equals(BOLETO_PJBANK) || this.equals(BOLETO_ITAU_ONLINE) || this.equals(BOLETO_ASAAS) || this.equals(BOLETO_CAIXA_ONLINE) || this.equals(BOLETO_BANCO_BRASIL_ONLINE);
    }

}
