/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;


public enum StatusPinpadEnum {

    GERADO(1, "<PERSON><PERSON>D<PERSON>"),
    AG<PERSON>ARDANDO(2, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"),
    FAL<PERSON>(3, "<PERSON><PERSON><PERSON>"),
    PAGO(4, "PAGO"),
    FINALIZADO(5, "FINAL<PERSON><PERSON><PERSON><PERSON>"),
    CANC<PERSON>ADO(6, "CA<PERSON><PERSON>AD<PERSON>"),
    ESTORNADA(7, "ESTORNADA"),
    ABORTADO(8, "ABORTADO"),
    AGUARDANDO_CANCELAMENTO(9, "AGUARDANDO_CANCELAMENTO");

    private Integer codigo;
    private String descricao;

    private StatusPinpadEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static StatusPinpadEnum fromCodigo(Integer codigo) {
        for (StatusPinpadEnum value : values()) {
            if (value.codigo.equals(codigo)) {
                return value;
            }
        }

        return null;
    }
}
