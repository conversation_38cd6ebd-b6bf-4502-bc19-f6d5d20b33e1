package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import annotations.arquitetura.FKJson;
import br.com.pactosolucoes.integracao.pactopay.dto.ContaCorrenteDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.ConvenioDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.SftpDTO;
import br.com.pactosolucoes.integracao.pactopay.TipoDocumentoEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.plano.ConvenioCobrancaEmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.jboleto.JBoleto;
import org.json.JSONObject;
import servicos.impl.boleto.BancoEnum;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;
import negocio.comuns.financeiro.enumerador.CurrencyConvenioEnum;
import servicos.impl.redepay.ERedeStatusConciliacaoEnum;

import java.util.*;

/**
 * Reponsável por manter os dados da entidade ConvenioCobranca. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class ConvenioCobrancaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = 0;
    protected String descricao;
    protected Double multa;
    protected Double juros;
    protected String extensaoArquivoRemessa;
    protected String extensaoArquivoRetorno;
    protected String diretorioGravaRemessa;
    protected String diretorioLerRetorno;
    protected String mensagem;
    protected Integer sequencialDoArquivo = 0;
    protected String numeroContrato;
    protected Integer carteira;
    protected Integer carteiraBoleto;
    protected Integer variacao;
    private Integer nrDiasProtesto;
    private Integer nrDiasBaixaAutomatica;
    protected EmpresaVO empresa;
    protected String cnpj;
    @ChaveEstrangeira
    @FKJson
    protected ContaCorrenteVO contaEmpresa;
    /** Atributo responsável por manter o objeto relacionado da classe <code>TipoRemessa </code>.*/
    @ChaveEstrangeira
    @FKJson
    protected TipoRemessaVO tipoRemessa;
    private TipoConvenioCobrancaEnum tipo;
    @ChaveEstrangeira
    @FKJson
    private BancoVO banco = new BancoVO();
    private IdentificadorClienteEmpresaEnum identificadorClienteEmpresa;

    private String hostSFTP = "";
    private String portSFTP = "";
    private String userSFTP = "";
    private String pwdSFTP = "";
    private String diretorioRemotoTIVIT_OUT = "";
    private String diretorioRemotoTIVIT_IN = "";
    private Boolean enviarRemessaSFTPNow = false;

    private String diretorioLocalTIVIT = "";
    private String diretorioLocalUploadTIVIT = "";
    private String diretorioLocalDownloadTIVIT = "";

    private String chaveGETNET;
    private String chaveBIN;
    private String nossaChave;
    private String nossaSenha;

    private String cieloClientId;

    private String nomeNossaChave;
    private String nomeChaveGETNET;
    private String nomeChaveBIN;

    private String chavePJBank;
    private String credencialPJBank;
    private String webhWookPJBank;

    private String instrucoesBoleto = "";
    private String sitesBoleto = "";
    private String codigoTransmissao = "";

    private String diretorioGETNET_CANCELAMENTO_OUT = "";
    private String diretorioGETNET_CANCELAMENTO_IN = "";
    private String numeroLogico;
    private Integer sequencialArquivoCancelamento = 0;
    private SituacaoConvenioCobranca situacao = SituacaoConvenioCobranca.ATIVO;
    //Atributo responsavel por manter o sequencial dos itens (RemessaItem) gerados para este convênio.
    private Integer sequencialItem = 1;
    private Boolean usarSequencialUnico = false;
    private String chaveAPI = "";
    private Map<String, String> props = new HashMap<String, String>();

    private String codigoAutenticacao01;
    public String codigoAutenticacao01Exibir;
    private String codigoAutenticacao02;
    public String codigoAutenticacao02Exibir;
    private String codigoAutenticacao03;
    public String codigoAutenticacao03Exibir;
    private String codigoAutenticacao04;
    public String codigoAutenticacao04Exibir;
    private String codigoAutenticacao05;
    public String codigoAutenticacao05Exibir;
    private String codigoAutenticacao06;
    public String codigoAutenticacao06Exibir;
    private String codigoAutenticacao07;
    public String codigoAutenticacao07Exibir;
    private boolean permitirReceberBoletoAposVencimento = false;
    private Date dataCadastro;
    private Integer sequenciaNrParcelaInicio;
    private Integer sequenciaNrParcelaFim;
    private boolean selecionado = false;
    private boolean tarifaBoletoSeparadaValorPago = false;
    private boolean adicionarData = false; //define se irá adicionar data no diretório para buscar extratos da REDE
    private boolean buscarCentralPacto = false; //define se irá buscar extratos da REDE na central Pacto
    private String mascaraDataArquivo; //máscara dos arquivos da REDE
    private Date dataChaveAPI;
    private String operacao;
    @NaoControlarLogAlteracao
    private List<ConvenioCobrancaEmpresaVO> configuracoesEmpresa;
    private String codigoGenerico;
    private boolean processarRemessasAutomatico = false; //ATUALMENTE UTILIZADO SOMENTE PARA PARA DCO
    private boolean gerarArquivoUnico = false; //ATUALMENTE UTILIZADO SOMENTE PARA PARA DCO
    private int diasParaCompensacao = 0;
    private int diasAntecipacaoRemessaDCO = 0;
    private Integer diasLimiteVencimentoParcelaDCO;
    private boolean agruparPorPessoaParcela = false;
    private boolean omitirSequencialArquivo = false;
    private Integer limiteItensRemessa;
    private NomenclaturaArquivoEnum nomenclaturaArquivo;
    private boolean utilizaExtrato = false;
    private String hostSFTPExtrato; //100
    private Integer portSFTPExtrato; //INTEGER
    private String userSFTPExtrato; //50
    private String pwdSFTPExtrato; //50
    private String nomenclaturaExtrato; //text
    private String diretorioRemotoExtrato; //100
    private String mascaraDataDiretorioRemotoExtrato; //20
    private String diretorioLocalExtrato;
    private String diretorioLocalLogExtrato;
    private String mascaraDataDiretorioLocalExtrato; //20
    private boolean obterCentralPactoExtrato = false;
    private boolean ignorarValidacoesExtrato = false;
    private VanExtratoEnum vanExtrato;
    private Double descontoBoleto;
    private String numeroCompromisso;
    private String tipoParcelamentoStone = "";
    private AmbienteEnum ambiente;
    private boolean bloquearCobrancaAutomatica = false;
    @NaoControlarLogAlteracao
    private List<ConvenioCobrancaRateioVO> listaConvenioCobrancaRateioVO;
    private boolean somenteExtrato = false; //quando será processado somente extrato

    // Credenciamento Extrato Cielo (API)
    private String accessTokenCielo;
    private String refreshTokenCielo;
    private String RegisterIDCredenciamentoCielo;

    private boolean pactoPay = false;
    private String pixClientId;
    private String pixClientSecret;
    private String pixAppKey;
    private String pixBasicAuth;
    private String pixChave;
    private Integer pixExpiracao;
    private CurrencyConvenioEnum currencyConvenioEnum;
    private ArquivoLayoutRemessaEnum layoutBoletoOnline;
    @NaoControlarLogAlteracao
    private List<ConvenioCobrancaArquivoVO> listaConvenioCobrancaArquivoVO;
    private AdquirenteMaxiPagoEnum adquirenteMaxiPago;
    private TipoBoletoPJBankEnum tipoBoletoPJBank;
    private Integer diasExpirarPix;
    private boolean enviarNotificacoes = false;
    private boolean gerarMultaEJurosRemessaItauCNAB400 = false;
    private boolean apresentarInativoNoPactoPay = false;
    private boolean verificacaoZeroDollar = false;
    private ERedeStatusConciliacaoEnum statusConciliacaoRedeOnline;
    private String requestIdConciliacaoRedeOnline;
    private String refreshToken;
    private Date dataGeracaoAccessToken;
    private boolean registrarBoletoOnlineSomenteNaImpressao = false;
    private boolean usaSplitPagamentoStoneV5;
    private TipoCredencialStoneEnum tipoCredencialStoneEnum;
    @NaoControlarLogAlteracao
    private boolean permiteVisualizarCredenciaisOriginais;

    /**
     * Construtor padrão da classe <code>ConvenioCobranca</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ConvenioCobrancaVO() {
        super();
        inicializarDados();
    }

    public ConvenioCobrancaVO(Integer codigo) {
        super();
        inicializarDados();
        this.codigo = codigo;
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public String getBanco_Apresentar() {
        return getBanco().getNome();
    }

    public String getTipo_Apresentar() {
        return getTipo().getDescricao();
    }

    public String getContaCorrente_Apresentar() {
        return "AG: " + getContaEmpresa().getAgencia() + "-" + getContaEmpresa().getAgenciaDV() + " / CC:" + getContaEmpresa().getContaCorrente() + "-" + getContaEmpresa().getContaCorrenteDV();
    }

    public String getContaCorrenteBoleto() {
        return getContaEmpresa().getAgencia() + getContaEmpresa().getAgenciaDV() + "/" + getContaEmpresa().getContaCorrente() + "-" + getContaEmpresa().getContaCorrenteDV();
    }

    public static void validarDadosPix(ConvenioCobrancaVO convenioCobrancaVO) throws ConsistirException {
        if (convenioCobrancaVO.isPixPjBank()) {
            if (convenioCobrancaVO.getDiasExpirarPix() == null) {
                throw new ConsistirException("O valor informado no campo 'Tempo de validade do pix, em dias' deve ser informado");
            }
            if (convenioCobrancaVO.getDiasExpirarPix() <= 0) {
                throw new ConsistirException("O valor informado no campo 'Tempo de validade do pix, em dias' deve ser maior que 0");
            }
            if (UteisValidacao.emptyString(convenioCobrancaVO.getChavePJBank())) {
                throw new ConsistirException("O campo 'Chave PJBank' deve ser informado.");
            }
            if (UteisValidacao.emptyString(convenioCobrancaVO.getCredencialPJBank())) {
                throw new ConsistirException("O campo 'Credencial PJBank' deve ser informado.");
            }
        }else if (convenioCobrancaVO.isPixAfinz()) {
            if (convenioCobrancaVO.isApresentarCodigoAutenticacao01() && UteisValidacao.emptyString(convenioCobrancaVO.codigoAutenticacao01)) {
                throw new ConsistirException("O campo '" + convenioCobrancaVO.getLabelCodigoAutenticacao01() + "' deve ser informado.");
            }
        }else if (convenioCobrancaVO.getTipo().isPix() && !convenioCobrancaVO.isPixAsaas()) {
            convenioCobrancaVO.getTipo().setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.PIX);

            //Campos Convênio Pix Geral
            if (convenioCobrancaVO.getPixExpiracao() > 259200) {
                throw new ConsistirException("O valor informado no campo 'Tempo de validade do pix' não pode ser maior do que 259200 (3 dias | 72 horas)");
            }
            if (convenioCobrancaVO.getPixExpiracao() <= 0) {
                throw new ConsistirException("O valor informado no campo 'Tempo de validade do pix' não pode ser menor ou igual a 0");
            }
            if (convenioCobrancaVO.getPixChave() == null || convenioCobrancaVO.getPixChave().trim().isEmpty()) {
                throw new ConsistirException("O campo 'Chave de conta pix' do convênio de cobrança deve ser informado.");
            }
            if ((convenioCobrancaVO.getPixClientId() == null || convenioCobrancaVO.getPixClientId().trim().isEmpty()) && !convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_BRADESCO)) {
                throw new ConsistirException("O campo 'Client ID' do convênio de cobrança deve ser informado.");
            }
            if (convenioCobrancaVO.getPixClientSecret() == null || convenioCobrancaVO.getPixClientSecret().trim().isEmpty()) {
                throw new ConsistirException("O campo 'Client secret' do convênio de cobrança deve ser informado.");
            }

            //Campos Convênio Pix BB
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_BB)) {
                if (convenioCobrancaVO.getPixBasicAuth() == null || convenioCobrancaVO.getPixBasicAuth().trim().isEmpty()) {
                    throw new ConsistirException("O campo 'Client basic auth token' do convênio de cobrança deve ser informado.");
                }
            }

            //Campos Convênio Pix Bradesco
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_BRADESCO)) {
                if (convenioCobrancaVO.getPixClientId() == null || convenioCobrancaVO.getPixClientId().trim().isEmpty()) {
                    throw new ConsistirException("O campo 'Client key' do convênio de cobrança deve ser informado.");
                }
            }
        }
    }

    public static void validarDados(ConvenioCobrancaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }

        validarDadosPix(obj);

        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Convênio Cobrança) deve ser informado.");
        }
        if (UteisValidacao.emptyList(obj.getConfiguracoesEmpresa())) {
            throw new ConsistirException("Pelo menos uma empresa deve ser selecionada.");
        }
        if (obj.getTipo() == null || obj.getTipo().equals(TipoConvenioCobrancaEnum.NENHUM)) {
            throw new ConsistirException("O campo Tipo Convênio deve ser informado.");
        }
        if (obj.isSomenteExtrato() &&
                !obj.isUtilizaExtrato() &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
            throw new ConsistirException("Convênio marcado como \"Somente para Extrato\" deve ser preenchido a aba \"Extrato (Conciliação)\".");
        }
        if (obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE) && obj.getCodigoAutenticacao02().contains("pk_")) {
            throw new ConsistirException("Você colocou a chave pública no campo \"Secret Key\". A integração só pode ser realizada com a chave privada. Volte no portal, pegue a \"Secret Key\" e configure novamente.");
        }
        if (obj.getTipo().getTipoAutorizacao() != null &&
                obj.getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {
            validarDadosBoletoBancario(obj);
        }
        if (obj.getTipo().getTipoAutorizacao() != null &&
                obj.getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            validarDadosCartaoCredito(obj);
        }
        if (obj.getTipo().getTipoAutorizacao() != null &&
                obj.getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
            validarDadosDebitoConta(obj);
        }
        if (obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD)) {
            validarDadosPinpad(obj);
        }

        if (!obj.isSomenteExtrato() &&
                !obj.getTipo().isTransacaoOnline() &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) &&
                !obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !obj.isBoletoAsaas() &&
                !obj.getTipo().isPix() &&
                (obj.getTipoRemessa() == null || UteisValidacao.emptyNumber(obj.getTipoRemessa().getCodigo())) && obj.getBanco().codigoBanco != 301) {
            throw new ConsistirException("O campo 'Tipo Remessa' deve ser informado.");
        }
        if (!obj.isSomenteExtrato() &&
                UteisValidacao.emptyNumber(obj.getSequencialDoArquivo()) &&
                !obj.getTipo().isTransacaoOnline() &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) &&
                !obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !obj.isBoletoAsaas() &&
                !obj.getTipo().isPix() &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)&& obj.getBanco().codigoBanco != 301) {
            throw new ConsistirException("O campo 'Sequencial do Arquivo' deve ser informado.");
        }
        if (!obj.getTipo().isTransacaoOnline() &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) &&
                !obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !obj.isBoletoAsaas() &&
                !obj.getTipo().isPix() &&
                obj.getBanco().codigoBanco != 301) {
            if (UteisValidacao.emptyString(obj.getNumeroContrato())) {
                throw new ConsistirException("O campo \"Num. Contrato/Num. Estabelecimento\" deve ser informado.");
            }
        }

        if (obj.isSomenteExtrato() &&
                UteisValidacao.emptyString(obj.getNumeroContrato()) &&
                (obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) ||
                obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE) ||
                obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC) ||
                obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) ||
                obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE))) {
            throw new ConsistirException("O campo \"Num. Contrato/Num. Estabelecimento\" deve ser informado.");
        }

        if (!obj.isSomenteExtrato() &&
                obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) &&
                UteisValidacao.emptyString(obj.getTipoParcelamentoStone())) {
            throw new ConsistirException("O campo 'Tipo de parcelamento' deve ser informado.");
        }

        if (obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            if (obj.usaSplitPagamentoStoneV5 && UteisValidacao.emptyList(obj.getListaConvenioCobrancaRateioVO())) {
                throw new ConsistirException("É necessário configurar o Split na aba 'Split de pagamentos' para gravar o convênio.");
            }
            if (obj.getTipoCredencialStoneEnum() == null || obj.getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.NENHUM)) {
                throw new ConsistirException("O campo 'Tipo da credencial' deve ser informado.");
            }

            String chaveAPIVerificar = "";
            if (obj.usaSplitPagamentoStoneV5) {
                chaveAPIVerificar = obj.getCodigoAutenticacao04();
            } else {
                chaveAPIVerificar = obj.getCodigoAutenticacao03();
            }
            if (obj.getAmbiente().equals(AmbienteEnum.HOMOLOGACAO) && !chaveAPIVerificar.toLowerCase().contains("_test_")) {
                throw new ConsistirException("Chave da API inválida. Foi selecionado o ambiente de HOMOLOGAÇÃO, porém a Chave de API informada não contém a palavra \"_test_\", o que significa que a credencial informada pode ser de produção e não de homologação.");
            } else if (obj.getAmbiente().equals(AmbienteEnum.PRODUCAO) && chaveAPIVerificar.toLowerCase().contains("_test_")) {
                throw new ConsistirException("Chave da API inválida. Foi selecionado o ambiente de PRODUÇÃO, porém a Chave de API informada contém a palavra \"_test_\", o que significa que a credencial informada é de testes e não de produção.");
            }
        }

        if (obj.isApresentarAmbiente() && obj.getAmbiente().equals(AmbienteEnum.NENHUM)) {
            throw new ConsistirException("Selecione o ambiente.");
        }
    }

    private static void validarDadosBoletoBancario(ConvenioCobrancaVO obj) throws ConsistirException{
        if (obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE)) {
            validarConvenioItauOnline(obj);
            return;
        }
        if (obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE)) {
            validarConvenioCaixaOnline(obj);
            return;
        }
        if (obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE)) {
            validarConvenioBancoBrasilOnline(obj);
            return;
        }
        if (obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) || obj.getTipo().equals(TipoConvenioCobrancaEnum.PIX_ASAAS)) {
            validarConvenioAsaasOnline(obj);
            return;
        }
        if(obj.getBanco() == null || UteisValidacao.emptyNumber(obj.getBanco().getCodigo())){
            throw  new ConsistirException("O campo 'Banco' deve ser informado.");
        }
        if(obj.getContaEmpresa() == null || UteisValidacao.emptyNumber(obj.getContaEmpresa().getCodigo())){
            throw  new ConsistirException("O campo 'Conta Corrente' deve ser informado.");
        }
        if(!obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) && !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) && obj.getBanco().codigoBanco != 301) {
            if (UteisValidacao.emptyString(obj.getExtensaoArquivoRemessa())) {
                throw new ConsistirException("O campo 'Extensão do Arquivo de Remessa' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getExtensaoArquivoRetorno())) {
                throw new ConsistirException("O campo 'Extensão do Arquivo de Retorno' deve ser informado.");
            }
            if (UteisValidacao.emptyNumber(obj.getCarteira())) {
                throw new ConsistirException("O campo 'Carteira' deve ser informado.");
            }
            if (obj.getBanco().getCodigoBanco().equals(JBoleto.ITAU) && UteisValidacao.emptyNumber(obj.getCarteiraBoleto())) {
                throw new ConsistirException("O campo 'Carteira para o Boleto' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getInstrucoesBoleto())) {
                throw new ConsistirException("O campo 'Instruções do Boleto' deve ser informado.");
            }
            if(!UteisValidacao.somenteNumeros(obj.getNumeroContrato()) && obj.getBanco().getCodigoBanco().equals(JBoleto.BRADESCO)){
                    throw new ConsistirException("O campo \"Num. Contrato/Num. Estabelecimento\" deve conter somente números.");
            }
        }
        if(obj.getBanco().getCodigoBanco().equals(JBoleto.SANTANDER)){
            if(UteisValidacao.emptyString(obj.getCodigoTransmissao())){
                throw  new ConsistirException("O campo 'Código de Transmissão' deve ser informado.");
            }  if (!UteisValidacao.somenteNumeros(obj.getNumeroContrato())) {
                throw new ConsistirException("O campo \"Num. Contrato/Num. Estabelecimento\" deve conter somente números.");
            }
        }

        if (!obj.getBanco().getCodigoBanco().equals(obj.getContaEmpresa().getBanco().getCodigoBanco()) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS)){
            throw  new ConsistirException("O código do banco informado no campo 'banco' não corresponde ao código informado lá dentro do cadastro da conta corrente informada. Favor verifique e tente novamente.!");
        }
    }

    private static void validarConvenioItauOnline(ConvenioCobrancaVO obj) throws ConsistirException {
        if (obj.isApresentarAmbiente() && obj.getAmbiente().equals(AmbienteEnum.NENHUM)) {
            throw new ConsistirException("Selecione o ambiente.");
        }
        if (UteisValidacao.emptyNumber(obj.getBanco().getCodigo())) {
            throw new ConsistirException("O campo 'Banco' deve ser informado.");
        }
        if (obj.getContaEmpresa() == null || UteisValidacao.emptyNumber(obj.getContaEmpresa().getCodigo())) {
            throw new ConsistirException("O campo 'Conta Corrente' deve ser informado.");
        }
        if (UteisValidacao.emptyString(obj.getCnpj())) {
            throw new ConsistirException("O campo \"CNPJ\" deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getSequencialItem())) {
            throw new ConsistirException("O campo 'Sequencial' deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getCarteira())) {
            throw new ConsistirException("O campo 'Carteira' deve ser informado.");
        }
        if (obj.getLayoutBoletoOnline().equals(ArquivoLayoutRemessaEnum.NENHUM)) {
            throw new ConsistirException("O campo \"Layout impressão boleto\" deve ser selecionado.");
        }
        if (UteisValidacao.emptyString(obj.getCodigoAutenticacao02())) {
            throw new ConsistirException("O campo \"" + obj.getLabelCodigoAutenticacao02() + "\" deve ser informado.");
        }
        if (UteisValidacao.emptyString(obj.getCodigoAutenticacao03())) {
            throw new ConsistirException("O campo \"" + obj.getLabelCodigoAutenticacao03() + "\" deve ser informado.");
        }
    }

    private static void validarConvenioCaixaOnline(ConvenioCobrancaVO obj) throws ConsistirException {
        if (obj.isApresentarAmbiente() && obj.getAmbiente().equals(AmbienteEnum.NENHUM)) {
            throw new ConsistirException("Selecione o ambiente.");
        }
        if (UteisValidacao.emptyNumber(obj.getBanco().getCodigo())) {
            throw new ConsistirException("O campo 'Banco' deve ser informado.");
        }
        if (obj.getContaEmpresa() == null || UteisValidacao.emptyNumber(obj.getContaEmpresa().getCodigo())) {
            throw new ConsistirException("O campo 'Conta Corrente' deve ser informado.");
        }
        if (UteisValidacao.emptyString(obj.getCnpj())) {
            throw new ConsistirException("O campo \"CNPJ\" deve ser informado.");
        }
        if (!UteisValidacao.somenteNumeros(obj.getNumeroContrato())) {
            throw new ConsistirException("O campo \"Num. Contrato/Cód. Beneficiário\" deve conter somente números.");
        } if (obj.getInstrucoesBoleto().length() > 80) {
            throw new ConsistirException("A API da Caixa só aceita até 80 caracteres.");
        }
    }

    private static void validarConvenioBancoBrasilOnline(ConvenioCobrancaVO obj) throws ConsistirException {
        if (obj.isApresentarAmbiente() && obj.getAmbiente().equals(AmbienteEnum.NENHUM)) {
            throw new ConsistirException("Selecione o ambiente.");
        }
        if (!UteisValidacao.somenteNumeros(obj.getNumeroContrato())) {
            throw new ConsistirException("O campo 'Código Convênio' deve conter somente números.");
        }
        if (UteisValidacao.emptyString(obj.getNumeroContrato())) {
            throw new ConsistirException("O campo 'Código Convênio' deve ser informado.");
        }
        if (UteisValidacao.emptyString(obj.getDescricao())) {
            throw new ConsistirException("O campo 'Descrição' deve ser informado.");
        }
        if (UteisValidacao.emptyString(obj.getPixClientId())) {
            throw new ConsistirException("O campo 'Client Id' deve ser informado.");
        }
        if (UteisValidacao.emptyString(obj.getPixClientSecret())) {
            throw new ConsistirException("O campo 'Client Secret' deve ser informado.");
        }
        if (UteisValidacao.emptyString(obj.getPixBasicAuth())) {
            throw new ConsistirException("O campo 'Client Basic Auth Token' deve ser informado.");
        }
        if (!UteisValidacao.emptyString(obj.getInstrucoesBoleto()) && obj.getInstrucoesBoleto().length() > 160) {
            throw new ConsistirException("O campo 'Instrução do Boleto' deve ter menos de 160 caracteres.");
        }
        if (UteisValidacao.emptyNumber(obj.getCarteiraBoleto())) {
            throw new ConsistirException("O campo 'Carteira para o Boleto' deve ser informado. Consiga o mesmo com o seu gerente. " +
                    "Se informar um diferente do esperado pelo banco, vai retornar erro no momento da geração dos boletos.");
        }
        if (UteisValidacao.emptyNumber(obj.getVariacao())) {
            throw new ConsistirException("O campo 'Variação' deve ser informado. Consiga o mesmo com o seu gerente. " +
                    "Se informar um diferente do esperado pelo banco, vai retornar erro no momento da geração dos boletos.");
        }
        if (UteisValidacao.emptyNumber(obj.getSequencialItem())) {
            throw new ConsistirException("O campo 'Nosso Número' deve ser informado. Identifique qual o último número que já foi utilizado. " +
                    "Se informar um número já utilizado, vai retornar o seguinte erro 'Título já incluído anteriormente' no momento de gerar o boleto.");
        }
        if(obj.getBanco() == null || UteisValidacao.emptyNumber(obj.getBanco().getCodigo())){
            throw  new ConsistirException("O campo 'Banco' deve ser informado.");
        }
        if(obj.getContaEmpresa() == null || UteisValidacao.emptyNumber(obj.getContaEmpresa().getCodigo())){
            throw  new ConsistirException("O campo 'Conta Corrente' deve ser informado.");
        }
    }

    private static void validarConvenioAsaasOnline(ConvenioCobrancaVO obj) throws ConsistirException {
        if (UteisValidacao.emptyString(obj.getCodigoAutenticacao01())) {
            throw new ConsistirException("O campo \"" + obj.getLabelCodigoAutenticacao01() + "\" deve ser informado.");
        }
    }

    private static void validarDadosCartaoCredito(ConvenioCobrancaVO obj) throws  ConsistirException{
        if (obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
            if (UteisValidacao.emptyString(obj.getNumeroLogico())) {
                throw new ConsistirException("O campo 'Num. Lógico' deve ser informado.");
            }
        } else if (obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            if (UteisValidacao.emptyNumber(obj.getSequencialArquivoCancelamento()) && !obj.isSomenteExtrato()) {
                throw new ConsistirException("O campo 'Sequencial Do Arquivo Cancelamento' deve ser informado.");
            }
            if (obj.getUsuarioVO().getAdministrador() && !obj.isSomenteExtrato()) {
                if (UteisValidacao.emptyString(obj.getNossaChave())) {
                    throw new ConsistirException("O campo 'Nossa chave' deve ser informado.");
                }
                if (UteisValidacao.emptyString(obj.getChaveGETNET())) {
                    throw new ConsistirException("O campo 'Chave criptografia GETNET' deve ser informado.");
                }
                if (UteisValidacao.emptyString(obj.getDiretorioGETNET_CANCELAMENTO_OUT())) {
                    throw new ConsistirException("O campo 'Diretório GETNET Cancelamento OUT' deve ser informado.");
                }
                if (UteisValidacao.emptyString(obj.getDiretorioGETNET_CANCELAMENTO_IN())) {
                    throw new ConsistirException("O campo 'Diretório GETNET Cancelamento IN' deve ser informado.");
                }
            }
        } else if (obj.isApresentarCodigoAutenticacao01() && UteisValidacao.emptyString(obj.getCodigoAutenticacao01()) && !obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao01() + "' deve ser informado.");
        } else if (obj.isApresentarCodigoAutenticacao02() && UteisValidacao.emptyString(obj.getCodigoAutenticacao02())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao02() + "' deve ser informado.");
        } else if (obj.isApresentarCodigoAutenticacao03() && UteisValidacao.emptyString(obj.getCodigoAutenticacao03())) {
            if (!obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) && !obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) { //o campo é apresentado porem não é obrigatório para pago livre
                throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao03() + "' deve ser informado.");
            }
        } else if (obj.isApresentarCodigoAutenticacao04() && UteisValidacao.emptyString(obj.getCodigoAutenticacao04())) {
            if (!obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) &&
                    !obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) { //o campo é apresentado porem não é obrigatório para pago livre
                throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao04() + "' deve ser informado.");
            }
        } else if (obj.isApresentarCodigoAutenticacao05() && UteisValidacao.emptyString(obj.getCodigoAutenticacao05())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao05() + "' deve ser informado.");
        } else if (obj.isApresentarCodigoAutenticacao06() && UteisValidacao.emptyString(obj.getCodigoAutenticacao06())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao06() + "' deve ser informado.");
        } else if (obj.isApresentarCodigoAutenticacao07() && UteisValidacao.emptyString(obj.getCodigoAutenticacao07())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao07() + "' deve ser informado.");
        }

        if (!obj.isSomenteExtrato() &&
                obj.getUsuarioVO().getAdministrador() &&
                (!obj.getTipo().isTransacaoOnline())) {

            if (UteisValidacao.emptyString(obj.getHostSFTP())) {
                throw new ConsistirException("O campo 'Host SFTP' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getPortSFTP())) {
                throw new ConsistirException("O campo 'Porta SFTP' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getUserSFTP())) {
                throw new ConsistirException("O campo 'Usuário SFTP' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getPwdSFTP())) {
                throw new ConsistirException("O campo 'Senha SFTP' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getDiretorioRemotoTIVIT_OUT())) {
                throw new ConsistirException("O campo 'Diretório OUT' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getDiretorioRemotoTIVIT_IN())) {
                throw new ConsistirException("O campo 'Diretório IN' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getDiretorioLocalTIVIT())) {
                throw new ConsistirException("O campo 'Diretório Local' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getDiretorioLocalUploadTIVIT())) {
                throw new ConsistirException("O campo 'Diretório Local Upload' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getDiretorioLocalDownloadTIVIT())) {
                throw new ConsistirException("O campo 'Diretório Local Download' deve ser informado.");
            }
        }
    }

    private static void validarDadosDebitoConta(ConvenioCobrancaVO obj) throws  ConsistirException{
        if(obj.getBanco() == null || UteisValidacao.emptyNumber(obj.getBanco().getCodigo())){
            throw  new ConsistirException("O campo 'Banco' deve ser informado.");
        }
        if(obj.getContaEmpresa() == null || UteisValidacao.emptyNumber(obj.getContaEmpresa().getCodigo())){
            throw  new ConsistirException("O campo 'Conta Corrente' deve ser informado.");
        }
        if(obj.getIdentificadorClienteEmpresa() == null){
            throw  new ConsistirException("O campo 'Identificador Cliente Empresa' deve ser informado.");
        }

        if (obj.getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
            if (!UteisValidacao.somenteNumeros(obj.getNumeroContrato())) {
                throw new ConsistirException("O campo \"Num. Contrato/Num. Estabelecimento\" deve conter somente números.");
            }
            if (obj.getCarteira() == null) {
                throw new ConsistirException("O campo \"Carteira\" deve ser preenchido");
            }
            if (obj.getCarteira() == 0) {
                throw new ConsistirException("O campo \"Carteira\" não pode ser \"0\"");
            }
        }
    }

    private static void validarDadosPinpad(ConvenioCobrancaVO obj) throws  ConsistirException{
        if (obj.getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
            if (UteisValidacao.emptyString(obj.getTipoParcelamentoStone())) {
                throw new ConsistirException("Selecione o tipo de parcelamento");
            }
            if (UteisValidacao.emptyString(obj.getCodigoAutenticacao01())) {
                throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao01().replace(":", "") + "' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getCodigoAutenticacao02())) {
                throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao02().replace(":", "") + "' deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getCodigoAutenticacao03())) {
                throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao03().replace(":", "") + "' deve ser informado.");
            }
        }

        if (obj.isApresentarCodigoAutenticacao01() && UteisValidacao.emptyString(obj.getCodigoAutenticacao01())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao01() + "' deve ser informado.");
        } else if (obj.isApresentarCodigoAutenticacao02() && UteisValidacao.emptyString(obj.getCodigoAutenticacao02())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao02() + "' deve ser informado.");
        } else if (obj.isApresentarCodigoAutenticacao03() && UteisValidacao.emptyString(obj.getCodigoAutenticacao03())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao03() + "' deve ser informado.");
        } else if (obj.isApresentarCodigoAutenticacao04() && UteisValidacao.emptyString(obj.getCodigoAutenticacao04())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao04() + "' deve ser informado.");
        } else if (obj.isApresentarCodigoAutenticacao05() && UteisValidacao.emptyString(obj.getCodigoAutenticacao05())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao05() + "' deve ser informado.");
        } else if (obj.isApresentarCodigoAutenticacao06() && UteisValidacao.emptyString(obj.getCodigoAutenticacao06())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao06() + "' deve ser informado.");
        } else if (obj.isApresentarCodigoAutenticacao07() && UteisValidacao.emptyString(obj.getCodigoAutenticacao07())) {
            throw new ConsistirException("O campo '" + obj.getLabelCodigoAutenticacao07() + "' deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setExtensaoArquivoRemessa(getExtensaoArquivoRemessa().toUpperCase());
        setExtensaoArquivoRetorno(getExtensaoArquivoRetorno().toUpperCase());
        setDiretorioGravaRemessa(getDiretorioGravaRemessa().toUpperCase());
        setDiretorioLerRetorno(getDiretorioLerRetorno().toUpperCase());
        setMensagem(getMensagem().toUpperCase());
        setNumeroContrato(getNumeroContrato().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setDescricao("");
        setMulta(0.0);
        setJuros(0.0);
        setExtensaoArquivoRemessa("");
        setExtensaoArquivoRetorno("");
        setDiretorioGravaRemessa("");
        setDiretorioLerRetorno("");
        setMensagem("");
        setSequencialDoArquivo(0);
        setNumeroContrato("");
        setCarteira(0);
        setVariacao(0);
        setVanExtrato(VanExtratoEnum.OUTRO);
    }

    /**
     * Retorna o objeto da classe <code>TipoRemessa</code> relacionado com (<code>ConvenioCobranca</code>).
     */
    public TipoRemessaVO getTipoRemessa() {
        if (tipoRemessa == null) {
            tipoRemessa = new TipoRemessaVO();
        }
        return (tipoRemessa);
    }

    /**
     * Define o objeto da classe <code>TipoRemessa</code> relacionado com (<code>ConvenioCobranca</code>).
     */
    public void setTipoRemessa(TipoRemessaVO obj) {
        this.tipoRemessa = obj;
    }

    public ContaCorrenteVO getContaEmpresa() {
        if (contaEmpresa == null) {
            contaEmpresa = new ContaCorrenteVO();
        }
        return contaEmpresa;
    }

    public void setContaEmpresa(ContaCorrenteVO contaEmpresa) {
        this.contaEmpresa = contaEmpresa;
    }

    /**
     * Retorna o objeto da classe <code>Empresa</code> relacionado com (<code>ConvenioCobranca</code>).
     */
    /**
     * Retorna o objeto da classe <code>Empresa</code> relacionado com (<code>ConvenioCobranca</code>).
     */
    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    /**
     * Define o objeto da classe <code>Empresa</code> relacionado com (<code>ConvenioCobranca</code>).
     */
    public void setEmpresa(EmpresaVO obj) {
        this.empresa = obj;
    }

    public Integer getVariacao() {
        if (variacao == null) {
            variacao = 0;
        }
        return variacao;
    }

    public void setVariacao(Integer variacao) {
        this.variacao = variacao;
    }

    public Integer getNrDiasProtesto() {
        if (nrDiasProtesto == null) {
            nrDiasProtesto = 0;
        }
        return nrDiasProtesto;
    }

    public void setNrDiasProtesto(Integer nrDiasProtesto) {
        this.nrDiasProtesto = nrDiasProtesto;
    }

    public Integer getNrDiasBaixaAutomatica() {
        if (nrDiasBaixaAutomatica == null) {
            nrDiasBaixaAutomatica = 0;
        }
        return nrDiasBaixaAutomatica;
    }

    public void setNrDiasBaixaAutomatica(Integer nrDiasBaixaAutomatica) {
        this.nrDiasBaixaAutomatica = nrDiasBaixaAutomatica;
    }

    public Integer getCarteira() {
        return (carteira);
    }

    public void setCarteira(Integer carteira) {
        this.carteira = carteira;
    }

    public Integer getCarteiraBoleto() {
        return carteiraBoleto;
    }

    public void setCarteiraBoleto(Integer carteiraBoleto) {
        this.carteiraBoleto = carteiraBoleto;
    }

    public String getNumeroContrato() {
        if (numeroContrato == null) {
            numeroContrato = "";
        }
        return (numeroContrato.trim());
    }

    public void setNumeroContrato(String numeroContrato) {
        this.numeroContrato = numeroContrato;
    }

    public Integer getSequencialDoArquivo() {
        return (sequencialDoArquivo);
    }

    public void setSequencialDoArquivo(Integer sequencialDoArquivo) {
        this.sequencialDoArquivo = sequencialDoArquivo;
    }

    public String getMensagem() {
        if (mensagem == null) {
            mensagem = "";
        }
        return (mensagem);
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getDiretorioLerRetorno() {
        if (diretorioLerRetorno == null) {
            diretorioLerRetorno = "";
        }
        return (diretorioLerRetorno);
    }

    public void setDiretorioLerRetorno(String diretorioLerRetorno) {
        this.diretorioLerRetorno = diretorioLerRetorno;
    }

    public String getDiretorioGravaRemessa() {
        if (diretorioGravaRemessa == null) {
            diretorioGravaRemessa = "";
        }
        return (diretorioGravaRemessa);
    }

    public void setDiretorioGravaRemessa(String diretorioGravaRemessa) {
        this.diretorioGravaRemessa = diretorioGravaRemessa;
    }

    public String getExtensaoArquivoRetorno() {
        if (extensaoArquivoRetorno == null) {
            extensaoArquivoRetorno = "";
        }
        return (extensaoArquivoRetorno);
    }

    public void setExtensaoArquivoRetorno(String extensaoArquivoRetorno) {
        this.extensaoArquivoRetorno = extensaoArquivoRetorno;
    }

    public String getExtensaoArquivoRemessa() {
        if (extensaoArquivoRemessa == null) {
            extensaoArquivoRemessa = "";
        }
        return (extensaoArquivoRemessa);
    }

    public void setExtensaoArquivoRemessa(String extensaoArquivoRemessa) {
        this.extensaoArquivoRemessa = extensaoArquivoRemessa;
    }

    public Double getJuros() {
        return (juros);
    }

    public void setJuros(Double juros) {
        this.juros = juros;
    }

    public Double getMulta() {
        return (multa);
    }

    public void setMulta(Double multa) {
        this.multa = multa;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoConvenioCobrancaEnum getTipo() {
        if (tipo == null) {
            tipo = TipoConvenioCobrancaEnum.NENHUM;
        }
        return tipo;
    }

    public void setTipo(TipoConvenioCobrancaEnum tipo) {
        this.tipo = tipo;
    }

    public BancoVO getBanco() {
        if (banco == null) {
            banco = new BancoVO();
        }
        return banco;
    }

    public void setBanco(BancoVO banco) {
        this.banco = banco;
    }

    public ConvenioCobrancaWS toWS() {
        ConvenioCobrancaWS convenioCobrancaWS = new ConvenioCobrancaWS();
        convenioCobrancaWS.setCodigoConvenio(this.getCodigo());
        convenioCobrancaWS.setDescricaoConvenio(this.getDescricao());
        convenioCobrancaWS.setCodBanco(this.getBanco().getCodigoBanco());
        convenioCobrancaWS.setBanco(this.getBanco_Apresentar());
        convenioCobrancaWS.setCodigoIdentificacaoBanco(this.getBanco().getCodigoBanco());
        if (this.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC)) {
            convenioCobrancaWS.setTipoConvenio("DCC");
        } else if (this.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {
            convenioCobrancaWS.setTipoConvenio("DCO");
        } else if (this.getTipo().isTransacaoOnline()) {
            convenioCobrancaWS.setTipoConvenio("TransacaoOnline");
        } else if (this.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO)) {
            convenioCobrancaWS.setTipoConvenio("BOLETO");
        } else {
            convenioCobrancaWS.setTipoConvenio("");
        }

        return convenioCobrancaWS;
    }

    public String getHostSFTP() {
        return hostSFTP;
    }

    public void setHostSFTP(String hostSFTP) {
        this.hostSFTP = hostSFTP;
    }

    public String getPortSFTP() {
        return portSFTP;
    }

    public void setPortSFTP(String portSFTP) {
        this.portSFTP = portSFTP;
    }

    public String getUserSFTP() {
        return userSFTP;
    }

    public void setUserSFTP(String userSFTP) {
        this.userSFTP = userSFTP;
    }

    public String getPwdSFTP() {
        return pwdSFTP;
    }

    public void setPwdSFTP(String pwdSFTP) {
        this.pwdSFTP = pwdSFTP;
    }

    public String getDiretorioRemotoTIVIT_OUT() {
        return diretorioRemotoTIVIT_OUT;
    }

    public void setDiretorioRemotoTIVIT_OUT(String diretorioRemotoTIVIT_OUT) {
        this.diretorioRemotoTIVIT_OUT = diretorioRemotoTIVIT_OUT;
    }

    public String getDiretorioRemotoTIVIT_IN() {
        return diretorioRemotoTIVIT_IN;
    }

    public void setDiretorioRemotoTIVIT_IN(String diretorioRemotoTIVIT_IN) {
        this.diretorioRemotoTIVIT_IN = diretorioRemotoTIVIT_IN;
    }

    public Boolean getEnviarRemessaSFTPNow() {
        return enviarRemessaSFTPNow;
    }

    public void setEnviarRemessaSFTPNow(Boolean enviarRemessaSFTPNow) {
        this.enviarRemessaSFTPNow = enviarRemessaSFTPNow;
    }

    public String getDiretorioLocalTIVIT() {
        return diretorioLocalTIVIT;
    }

    public void setDiretorioLocalTIVIT(String diretorioLocalTIVIT) {
        this.diretorioLocalTIVIT = diretorioLocalTIVIT;
    }

    public String getDiretorioLocalUploadTIVIT() {
        return diretorioLocalUploadTIVIT;
    }

    public void setDiretorioLocalUploadTIVIT(String diretorioLocalUploadTIVIT) {
        this.diretorioLocalUploadTIVIT = diretorioLocalUploadTIVIT;
    }

    public String getDiretorioLocalDownloadTIVIT() {
        return diretorioLocalDownloadTIVIT;
    }

    public void setDiretorioLocalDownloadTIVIT(String diretorioLocalDownloadTIVIT) {
        this.diretorioLocalDownloadTIVIT = diretorioLocalDownloadTIVIT;
    }

    public String getCieloClientId() {
        if (cieloClientId == null) {
            cieloClientId = "";
        }
        return (cieloClientId);
    }

    public void setCieloClientId(String cieloClientId) {
        this.cieloClientId = cieloClientId;
    }

    public String getChaveGETNET() {
        return chaveGETNET;
    }

    public void setChaveGETNET(String chaveGETNET) {
        this.chaveGETNET = chaveGETNET;
    }

    public String getNossaChave() {
        return nossaChave;
    }

    public void setNossaChave(String nossaChave) {
        this.nossaChave = nossaChave;
    }

    public String getNossaSenha() {
        if(nossaSenha == null){
            nossaSenha = "";
        }
        return nossaSenha;
    }

    public void setNossaSenha(String nossaSenha) {
        this.nossaSenha = nossaSenha;
    }

    public String getNomeNossaChave() {
        return nomeNossaChave;
    }

    public void setNomeNossaChave(String nomeNossaChave) {
        this.nomeNossaChave = nomeNossaChave;
    }

    public String getNomeChaveGETNET() {
        return nomeChaveGETNET;
    }

    public void setNomeChaveGETNET(String nomeChaveGETNET) {
        this.nomeChaveGETNET = nomeChaveGETNET;
    }

    public boolean isBoleto() {
        return getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU)
                || getTipo().equals(TipoConvenioCobrancaEnum.ITAU)
                || getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE)
                || getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL)
                || getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)
                || getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS)
                || getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE)
                || getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE);
    }
    public boolean isBoletoPjBank() {
        return getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK);
    }

    public boolean isBoletoAsaas() {
        return getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS);
    }

    public boolean isBoletoItauOnline() {
        return getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE);
    }

    public boolean isPixPjBank() {
        return getTipo().equals(TipoConvenioCobrancaEnum.PIX_PJBANK);
    }

    public boolean isPixAsaas() {
        return getTipo().equals(TipoConvenioCobrancaEnum.PIX_ASAAS);
    }

    public String getInstrucoesBoleto() {
        return instrucoesBoleto;
    }

    public void setInstrucoesBoleto(String instrucoesBoleto) {
        this.instrucoesBoleto = instrucoesBoleto;
    }

    public String getSitesBoleto() {
        return sitesBoleto;
    }

    public void setSitesBoleto(String sitesBoleto) {
        this.sitesBoleto = sitesBoleto;
    }

    public boolean isLayoutFebrabanDCO() {
        return (getTipo() != null) &&
                (getTipo().equals(TipoConvenioCobrancaEnum.DCO_SANTANDER) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCO_SANTANDER_150) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCO_BB) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA_SICOV))||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA);
    }

    public IdentificadorClienteEmpresaEnum getIdentificadorClienteEmpresa() {
        if (identificadorClienteEmpresa == null) {
            identificadorClienteEmpresa = IdentificadorClienteEmpresaEnum.COD_PESSOA;
        }
        return identificadorClienteEmpresa;
    }

    public void setIdentificadorClienteEmpresa(IdentificadorClienteEmpresaEnum identificadorClienteEmpresa) {
        this.identificadorClienteEmpresa = identificadorClienteEmpresa;
    }

    public String getCodigoTransmissao() {
        if (codigoTransmissao == null) {
            codigoTransmissao = "";
        }
        return codigoTransmissao;
    }

    public void setCodigoTransmissao(String codigoTransmissao) {
        this.codigoTransmissao = codigoTransmissao;
    }

    public ArquivoLayoutRemessaEnum getArquivoLayoutRemessa() {
        return getTipoRemessa().getArquivoLayoutRemessa();
    }

    public Integer getCodigoBanco() {
        return getBanco().getCodigo();
    }

    public String getDiretorioGETNET_CANCELAMENTO_OUT() {
        return diretorioGETNET_CANCELAMENTO_OUT;
    }

    public void setDiretorioGETNET_CANCELAMENTO_OUT(String diretorioGETNET_CANCELAMENTO_OUT) {
        this.diretorioGETNET_CANCELAMENTO_OUT = diretorioGETNET_CANCELAMENTO_OUT;
    }

    public String getDiretorioGETNET_CANCELAMENTO_IN() {
        return diretorioGETNET_CANCELAMENTO_IN;
    }

    public void setDiretorioGETNET_CANCELAMENTO_IN(String diretorioGETNET_CANCELAMENTO_IN) {
        this.diretorioGETNET_CANCELAMENTO_IN = diretorioGETNET_CANCELAMENTO_IN;
    }
    public boolean isApresentarNumeroLogico(){
        return getTipo() == TipoConvenioCobrancaEnum.DCC_BIN ? true : false;
    }

    public String getNumeroLogico() {
        if(numeroLogico == null) numeroLogico ="";
        return numeroLogico.trim();
    }

    public void setNumeroLogico(String numeroLogico) {
        this.numeroLogico = numeroLogico;
    }

    public boolean validarArquivoRetorno(RemessaVO retornoBoleto) {
        if (getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)) {
            if (BancoEnum.BNB.getCodigo() == getBanco().getCodigoBanco()) {
                String agencia = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.AgenciaCompensacao.name());
                String contaCorrente = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.ContaCorrente.name());
                String dvContaCorrente = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.ContaCorrenteDigito.name());

                return Integer.parseInt(agencia) == Integer.parseInt(getContaEmpresa().getAgencia())
                        && Integer.parseInt(contaCorrente) == Integer.parseInt(getContaEmpresa().getContaCorrente())
                        && Integer.parseInt(dvContaCorrente) == Integer.parseInt(getContaEmpresa().getContaCorrenteDV());
            } else if (BancoEnum.ITAU.getCodigo() == getBanco().getCodigoBanco()) {
                String agencia = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.AgenciaDepositaria.name());
                String contaCorrente = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.ContaCorrente.name());
                String dvContaCorrente = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.ContaCorrenteDigito.name());

                return Integer.parseInt(agencia) == Integer.parseInt(getContaEmpresa().getAgencia())
                        && Integer.parseInt(contaCorrente) == Integer.parseInt(getContaEmpresa().getContaCorrente())
                        && Integer.parseInt(dvContaCorrente) == Integer.parseInt(getContaEmpresa().getContaCorrenteDV());
            } else if (retornoBoleto.getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) || retornoBoleto.getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE)) {
                String agencia = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.AgenciaDebito.name());
                String contaCorrente = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.ContaCorrenteDebito.name());
                return Integer.valueOf(contaEmpresa.agencia).equals(Integer.valueOf(agencia)) && Integer.valueOf(contaEmpresa.contaCorrente).equals(Integer.valueOf(contaCorrente));
            } else if (BancoEnum.SICREDI.getCodigo() == getBanco().getCodigoBanco()) {
                String codigoBeneficiario = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.CodigoBeneficiario.name());
                return Integer.valueOf(getNumeroContrato()).equals(Integer.valueOf(codigoBeneficiario));
            } else {
                String numEstabelecimento = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.NumeroEstabelecimento.name());
                return Integer.valueOf(getNumeroContrato()).equals(Integer.valueOf(numEstabelecimento));
            }
        }

        return true;
    }

    public Integer getSequencialArquivoCancelamento() {
        return sequencialArquivoCancelamento;
    }

    public void setSequencialArquivoCancelamento(Integer sequencialArquivoCancelamento) {
        this.sequencialArquivoCancelamento = sequencialArquivoCancelamento;
    }

    public SituacaoConvenioCobranca getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoConvenioCobranca situacao) {
        this.situacao = situacao;
    }

    public Integer getSequencialItem() {
        return sequencialItem;
    }

    public void setSequencialItem(Integer sequencialItem) {
        this.sequencialItem = sequencialItem;
    }

    public boolean isApresentarCampoSequencialUnico() {
        return isPertenceConjuntoTipoConvenioCobrancaNaoUtilizaSequencial();
    }

    public boolean isApresentarCampoGerarMultaEJurosRemessaItauCNAB400() {
        return getTipo().equals(TipoConvenioCobrancaEnum.ITAU);
    }

    public boolean isApresentarCampoSequencialItem() {
        return getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) ||
                (isPertenceConjuntoTipoConvenioCobrancaNaoUtilizaSequencial() && !getUsarSequencialUnico());
    }

    public boolean isApresentarApenasSequencialItem() {
        return getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) || getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE);
    }

    private boolean isPertenceConjuntoTipoConvenioCobrancaNaoUtilizaSequencial() {
        return !TipoConvenioCobrancaEnum.NENHUM.equals(getTipo()) &&
                !getTipo().isTransacaoOnline() &&
                !getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !getTipo().isPix();
    }

    public Boolean getUsarSequencialUnico() {
        return usarSequencialUnico;
    }

    public void setUsarSequencialUnico(Boolean usarSequencialUnico) {
        this.usarSequencialUnico = usarSequencialUnico;
    }

    public String getChaveAPI() {
        return chaveAPI;
    }

    public void setChaveAPI(String chaveAPI) {
        this.chaveAPI = chaveAPI;
    }

    public Map<String, String> getProps() {
        return props;
    }

    public void setProps(Map<String, String> props) {
        this.props = props;
    }

    public boolean isUsarIdentificador() {
        return getBanco().getCodigoBanco().equals(BancoEnum.BNB.getCodigo()) ||
                getBanco().getCodigoBanco().equals(BancoEnum.BRB.getCodigo()) ||
                getBanco().getCodigoBanco().equals(BancoEnum.CAIXA.getCodigo()) ||
                getBanco().getCodigoBanco().equals(BancoEnum.BANCOOB.getCodigo()) ||
                getBanco().getCodigoBanco().equals(BancoEnum.BANCODOBRASIL.getCodigo()) ||
                getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo()) ||
                getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL);

    }

    public String getCodigoAutenticacao01() {
        if (codigoAutenticacao01 == null) {
            codigoAutenticacao01 = "";
        }
        return codigoAutenticacao01;
    }

    public void setCodigoAutenticacao01(String codigoAutenticacao01) {
        this.codigoAutenticacao01 = codigoAutenticacao01;
    }

    public String getCodigoAutenticacao02() {
        if (codigoAutenticacao02 == null) {
            codigoAutenticacao02 = "";
        }
        return codigoAutenticacao02;
    }

    public void setCodigoAutenticacao02(String codigoAutenticacao02) {
        this.codigoAutenticacao02 = codigoAutenticacao02;
    }

    public String getLabelCodigoAutenticacao01() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
            return "MerchantId:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO)) {
            return "ID da Loja:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            return "Número Filiação:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
            return "Client ID:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            return "StoneCode:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
            return "Chave pública:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
            return "Chave de API:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
            return "Access Token:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
            return "Chave privada:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
            return "Publishable key:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "UserName:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
            return "Merchant_id:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
            return "Merchant_id:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.PIX_AFINZ)) {
            return "Merchant_id:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT)) {
            return "Private Security Key:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
            return "StoneCode:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_GETCARD_SCOPE)) {
            return "PinPad Empresa:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) || getTipo().equals(TipoConvenioCobrancaEnum.PIX_ASAAS)) {
            return "Chave da API:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG)) {
            return "Token (MerchantToken):";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE)) {
            return "MerchantId:";
        }
        return "CodigoAutenticacao01:";
    }

    public String getLabelCodigoAutenticacao02() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
            return "MerchantKey:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
            return "Code:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO)) {
            return "Chave:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            return "Token:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
            return "Client Secret:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
            return "SAK (SaleAffiliationKey):";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
            return "Chave secreta:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
            return "Chave de criptografia:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
            return "Chave pública:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
            return "Secret key:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE)) {
            return "Client_id:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "KeyValue:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
            return "Chave secreta:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_GETCARD_SCOPE)) {
            return "PinPad Filial:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG)) {
            return "ID:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE)) {
            return "MerchantKey:";
        }
        return "CodigoAutenticacao02:";
    }

    public String getLabelCodigoAutenticacao03() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
            return "Seller ID:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE)) {
            return "Client_secret:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "Grant_type:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
            return "Chave pública:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
            return "Token:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG)) {
            return "Chave Privada:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
            return "Cód. Autorização (Code):";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            if (getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.GATEWAY)) {
                return "Chave da API (Gateway):";
            } else if (getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.PSP)) {
                return "Chave da API (PSP):";
            }
            return "Chave da API:";
        }
        return "CodigoAutenticacao03:";
    }

    public String getLabelCodigoAutenticacao04() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            return "Chave da API (PSP):";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "RequestOrigin:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG)) {
            return "CNPJ:";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
            return "Token Conciliação:";
        }
        return "CodigoAutenticacao04:";
    }

    public String getLabelCodigoAutenticacao05() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "CodigoCanal:";
        }
        return "CodigoAutenticacao05:";
    }

    public String getLabelCodigoAutenticacao06() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "CodigoCliente:";
        }
        return "CodigoAutenticacao06:";
    }

    public String getLabelCodigoAutenticacao07() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "KeyLoja:";
        }
        return "CodigoAutenticacao07:";
    }

    public String getTitleCodigoAutenticacao01() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
            return "Informe a 'Chave Pública' que se encontra no portal da Mundipagg.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
            return "Informe a 'Chave de API' que se encontra no portal da Pagar.Me.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
            return "O Access Token é exibido aqui e ele é gerado e salvo automaticamente assim que você termina de realizar a autorização com o PagBank através do fluxo do botão 'Gerar Autorização PagBank X Pacto'.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
            return "Informe o 'MerchantId' fornecido pela Cielo.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            return "Informe o 'StoneCode' fornecido pela Stone.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
            return "Informe o 'Client ID' fornecido pela Getnet.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO)) {
            return "Informe o 'ID da Loja' fornecido pela MaxiPago.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            return "Informe o 'Número Filiação' fornecido pela Rede.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
            return "Informe a 'Chave privada' que se encontra nas configurações do próprio portal da Vindi.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
            return "Informe a 'Chave pública' fornecida no dasboard da Stripe.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "Informe o \"UserName\" foi fornecido pela PinBank";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
            return "O Merchant_id é utilizado para efetuar todas as requisições na PagoLivre, é criado automaticamente quando se cria um novo Merchant lá no cadastro da empresa na aba 'PagoLivre'";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
            return "O Merchant_id é utilizado para efetuar todas as requisições na Fypay, é criado automaticamente quando se cria um novo Merchant lá no cadastro da empresa na aba 'Fypay'";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.PIX_AFINZ)) {
            return "O Merchant_id é utilizado para efetuar todas as requisições na Afinz.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT)) {
            return "Informe o Security Key privado. A chave deve ser gerada no portal da One Payments em: Settings-> Security Keys-> Private Security Keys";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
            return "Informe o '" + this.getLabelCodigoAutenticacao01().replace(":", "") + "' fornecido pela Stone.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) || getTipo().equals(TipoConvenioCobrancaEnum.PIX_ASAAS)) {
            return "Informe a Chave da API fornecida no portal ASAAS. A chave pode ser gerada lá no portal Asaas em: Configurações da conta-> Integração-> Gerar nova chave de API";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG)) {
            return "Ceopag";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE)) {
            return "Informe o 'MerchantId' fornecido pela Caixa.";
        }
        return null;
    }

    public String getTitleCodigoAutenticacao02() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
            return "Informe a 'Chave Secreta' que se encontra no portal da Mundipagg.";
        }        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
            return "O Code é exibido aqui e ele é gerado e salvo automaticamente assim que você termina de realizar a autorização com o PagBank através do fluxo do botão 'Gerar Autorização PagBank X Pacto'.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
            return "Informe a 'Chave de Criptografia' que se encontra no portal da Pagar.Me.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
            return "Informe o 'MerchantKey' fornecido pela Cielo.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
            return "Informe o 'SaleAffiliationKey' fornecido pela Stone.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            return "Informe a 'Chave da API'.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
            return "Informe o 'Client Secret' fornecido pela Getnet.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO)) {
            return "Informe a 'Chave' fornecida pela MaxiPago.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            return "Informe o 'Token' fornecido pela Rede.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
            return "Informe a 'Chave pública' que se encontra nas configurações do próprio portal da Vindi.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
            return "Informe a 'Chave secreta' fornecida no dasboard da Stripe.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "Informe o \"KeyValue\" fornecido pela PinBank";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
            return "Informe o '" + this.getLabelCodigoAutenticacao02().replace(":", "") + "' fornecido pela Stone.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE)) {
            return "Informe o 'MerchantKey' fornecido pela Caixa.";
        }
        return null;
    }

    public String getTitleCodigoAutenticacao03() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
            return "Informe o 'Seller ID' fornecido pela Getnet.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "Informe o \"Grant_type\" fornecido pela PinBank.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
            return "Informe o '" + this.getLabelCodigoAutenticacao03().replace(":", "") + "' fornecido pela Stone.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
            return "Esse campo não é obrigatório.<br/>Esse token só é necessário caso a academia não tenha sido cadastrada pela Pacto.<br/>Então deve ser solicitado a PagoLivre o token para o cliente.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            return "Informe a 'Chave da API'.";
        }
        return null;
    }

    public String getTitleCodigoAutenticacao04() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "Informe o \"RequestOrigin\" fornecido pela PinBank.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
            return "Esse campo não é obrigatório.<br/>\n" +
                    "Esse token só é necessário caso a academia deseje ativar as conciliações.<br/>\n" +
                    "Então deve ser solicitado a adquirente o Token Conciliação.";
        }
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            return "Informe a 'Chave da API'.";
        }
        return null;
    }

    public String getTitleCodigoAutenticacao05() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "Informe o \"CodigoCanal\" fornecido pela PinBank.";
        }
        return null;
    }

    public String getTitleCodigoAutenticacao06() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "Informe o \"CodigoCliente\" fornecido pela PinBank.";
        }
        return null;
    }

    public String getTitleCodigoAutenticacao07() {
        if (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            return "Informe o \"KeyLoja\" fornecido pela PinBank.";
        }
        return null;
    }

    public boolean isApresentarCodigoAutenticacao01() {
        if (!TipoConvenioCobrancaEnum.NENHUM.equals(getTipo()) &&
                (TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE.equals(getTipo()) || TipoConvenioCobrancaEnum.DCC_E_REDE.equals(getTipo()) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) || getTipo().equals(TipoConvenioCobrancaEnum.PIX_AFINZ) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT) || getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_GETCARD_SCOPE) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) || getTipo().equals(TipoConvenioCobrancaEnum.PIX_ASAAS) ||
                        (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5) && !isUsaSplitPagamentoStoneV5() &&
                                getTipoCredencialStoneEnum() != null && getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.GATEWAY)) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE))) {

            if (isSomenteExtrato() && !getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    public boolean isApresentarCodigoAutenticacao02() {
        if (!TipoConvenioCobrancaEnum.NENHUM.equals(getTipo()) &&
                (TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE.equals(getTipo()) || TipoConvenioCobrancaEnum.DCC_E_REDE.equals(getTipo()) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) || getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_GETCARD_SCOPE) || getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE))) {
            if (isSomenteExtrato()) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    public boolean isApresentarCodigoAutenticacao03() {
        if (!TipoConvenioCobrancaEnum.NENHUM.equals(getTipo()) &&
                (getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG) ||
                        (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5) && !isUsaSplitPagamentoStoneV5()) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT))) {
            if (isSomenteExtrato()) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    public boolean isApresentarCodigoAutenticacao04() {
        if (!TipoConvenioCobrancaEnum.NENHUM.equals(getTipo()) &&
                (getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG) ||
                        (getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5) && isUsaSplitPagamentoStoneV5()) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                        getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY) ||
                getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK))) {
            if (isSomenteExtrato()) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    public boolean isApresentarCodigoAutenticacao05() {
        if (!TipoConvenioCobrancaEnum.NENHUM.equals(getTipo()) &&
                (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK))) {
            if (isSomenteExtrato()) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    public boolean isApresentarCodigoAutenticacao06() {
        if (!TipoConvenioCobrancaEnum.NENHUM.equals(getTipo()) &&
                (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK))) {
            if (isSomenteExtrato()) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    public boolean isApresentarCodigoAutenticacao07() {
        if (!TipoConvenioCobrancaEnum.NENHUM.equals(getTipo()) &&
                (getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK))) {
            if (isSomenteExtrato()) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    public boolean isPermitirReceberBoletoAposVencimento() {
        return permitirReceberBoletoAposVencimento;
    }

    public void setPermitirReceberBoletoAposVencimento(boolean permitirReceberBoletoAposVencimento) {
        this.permitirReceberBoletoAposVencimento = permitirReceberBoletoAposVencimento;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Integer getSequenciaNrParcelaInicio() {
        if (sequenciaNrParcelaInicio == null) {
            sequenciaNrParcelaInicio = 0;
        }
        return sequenciaNrParcelaInicio;
    }

    public void setSequenciaNrParcelaInicio(Integer sequenciaNrParcelaInicio) {
        this.sequenciaNrParcelaInicio = sequenciaNrParcelaInicio;
    }

    public Integer getSequenciaNrParcelaFim() {
        if (sequenciaNrParcelaFim == null) {
            sequenciaNrParcelaFim = 0;
        }
        return sequenciaNrParcelaFim;
    }

    public void setSequenciaNrParcelaFim(Integer sequenciaNrParcelaFim) {
        this.sequenciaNrParcelaFim = sequenciaNrParcelaFim;
    }

    public boolean getSelecionado() {
        return selecionado;
    }
    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public boolean isTarifaBoletoSeparadaValorPago() {
        return tarifaBoletoSeparadaValorPago;
    }

    public void setTarifaBoletoSeparadaValorPago(boolean tarifaBoletoSeparadaValorPago) {
        this.tarifaBoletoSeparadaValorPago = tarifaBoletoSeparadaValorPago;
    }

    public boolean isAdicionarData() {
        return adicionarData;
    }

    public void setAdicionarData(boolean adicionarData) {
        this.adicionarData = adicionarData;
    }

    public boolean isApresentarOpcaoAdicionarData() {
        return !UteisValidacao.emptyNumber(getCodigo()) &&
                !isUtilizaExtrato() &&
                !TipoConvenioCobrancaEnum.NENHUM.equals(getTipo()) &&
                TipoConvenioCobrancaEnum.DCC_E_REDE.equals(getTipo());
    }

    public boolean isBuscarCentralPacto() {
        return buscarCentralPacto;
    }

    public void setBuscarCentralPacto(boolean buscarCentralPacto) {
        this.buscarCentralPacto = buscarCentralPacto;
    }

    public String getMascaraDataArquivo() {
        if (mascaraDataArquivo == null) {
            mascaraDataArquivo = "";
        }
        return mascaraDataArquivo;
    }

    public void setMascaraDataArquivo(String mascaraDataArquivo) {
        this.mascaraDataArquivo = mascaraDataArquivo;
    }

    public String getChaveBIN() {
        return chaveBIN;
    }

    public void setChaveBIN(String chaveBIN) {
        this.chaveBIN = chaveBIN;
    }

    public String getNomeChaveBIN() {
        return nomeChaveBIN;
    }

    public void setNomeChaveBIN(String nomeChaveBIN) {
        this.nomeChaveBIN = nomeChaveBIN;
    }

    public String getOperacao() {
        if (operacao == null) {
            operacao = "";
        }
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Date getDataChaveAPI() {
        return dataChaveAPI;
    }

    public void setDataChaveAPI(Date dataChaveAPI) {
        this.dataChaveAPI = dataChaveAPI;
    }

    public String getCodigoAutenticacao03() {
        if (codigoAutenticacao03 == null) {
            codigoAutenticacao03 = "";
        }
        return codigoAutenticacao03;
    }

    public void setCodigoAutenticacao03(String codigoAutenticacao03) {
        this.codigoAutenticacao03 = codigoAutenticacao03;
    }

    public String getCodigoAutenticacao04() {
        if (codigoAutenticacao04 == null) {
            codigoAutenticacao04 = "";
        }
        return codigoAutenticacao04;
    }

    public void setCodigoAutenticacao04(String codigoAutenticacao04) {
        this.codigoAutenticacao04 = codigoAutenticacao04;
    }

    public String getCodigoAutenticacao05() {
        if (codigoAutenticacao05 == null) {
            codigoAutenticacao05 = "";
        }
        return codigoAutenticacao05;
    }

    public void setCodigoAutenticacao05(String codigoAutenticacao05) {
        this.codigoAutenticacao05 = codigoAutenticacao05;
    }

    public String getCodigoAutenticacao06() {
        if (codigoAutenticacao06 == null) {
            codigoAutenticacao06 = "";
        }
        return codigoAutenticacao06;
    }

    public void setCodigoAutenticacao06(String codigoAutenticacao06) {
        this.codigoAutenticacao06 = codigoAutenticacao06;
    }

    public String getCodigoAutenticacao07() {
        if (codigoAutenticacao07 == null) {
            codigoAutenticacao07 = "";
        }
        return codigoAutenticacao07;
    }

    public void setCodigoAutenticacao07(String codigoAutenticacao07) {
        this.codigoAutenticacao07 = codigoAutenticacao07;
    }

    public String getCodigoAutorizacao01PagBank_Exibir(){
        if (UteisValidacao.emptyString(codigoAutenticacao01)) {
            return "Autorização ainda não realizada para gerar o Access Token";
        }
        return codigoAutenticacao01;
    }

    public String getCodigoAutorizacao02PagBank_Exibir(){
        if (UteisValidacao.emptyString(codigoAutenticacao02)) {
            return "Autorização ainda não realizada para gerar o Code";
        }
        return codigoAutenticacao02;
    }

    public String getDataExpiracaoAccessToken_Apresentar(){
        if (getDataGeracaoAccessToken() == null) {
            return "";
        }
        //token sempre vence 1 ano pra frente, em relação à data em que foi gerado
        return Uteis.getDataAplicandoFormatacao(Uteis.somarDias(getDataGeracaoAccessToken(), 365), "dd/MM/yyyy HH:mm:ss");
    }

    public List<ConvenioCobrancaEmpresaVO> getConfiguracoesEmpresa() {
        if (configuracoesEmpresa == null) {
            configuracoesEmpresa = new ArrayList<ConvenioCobrancaEmpresaVO>();
        }
        return configuracoesEmpresa;
    }

    public void setConfiguracoesEmpresa(List<ConvenioCobrancaEmpresaVO> configuracoesEmpresa) {
        this.configuracoesEmpresa = configuracoesEmpresa;
    }

    public String getCodigoGenerico() {
        if (codigoGenerico == null) {
            codigoGenerico = "";
        }
        return codigoGenerico;
    }

    public void setCodigoGenerico(String codigoGenerico) {
        this.codigoGenerico = codigoGenerico;
    }

    public boolean isProcessarRemessasAutomatico() {
        //ATUALMENTE UTILIZADO SOMENTE PARA PARA DCO
        return processarRemessasAutomatico;
    }

    public void setProcessarRemessasAutomatico(boolean processarRemessasAutomatico) {
        this.processarRemessasAutomatico = processarRemessasAutomatico;
    }

    public boolean isGerarArquivoUnico() {
        return gerarArquivoUnico;
    }

    public void setGerarArquivoUnico(boolean gerarArquivoUnico) {
        this.gerarArquivoUnico = gerarArquivoUnico;
    }

    public int getDiasParaCompensacao() {
        return diasParaCompensacao;
    }

    public void setDiasParaCompensacao(int diasParaCompensacao) {
        this.diasParaCompensacao = diasParaCompensacao;
    }

    public int getDiasAntecipacaoRemessaDCO() {
        return diasAntecipacaoRemessaDCO;
    }

    public void setDiasAntecipacaoRemessaDCO(int diasAntecipacaoRemessaDCO) {
        this.diasAntecipacaoRemessaDCO = diasAntecipacaoRemessaDCO;
    }

    public boolean isAgruparPorPessoaParcela() {
        return agruparPorPessoaParcela;
    }

    public void setAgruparPorPessoaParcela(boolean agruparPorPessoaParcela) {
        this.agruparPorPessoaParcela = agruparPorPessoaParcela;
    }

    public Integer getDiasLimiteVencimentoParcelaDCO() {
        if (diasLimiteVencimentoParcelaDCO == null) {
            diasLimiteVencimentoParcelaDCO = 0;
        }
        return diasLimiteVencimentoParcelaDCO;
    }

    public String getChavePJBank() {
        return chavePJBank;
    }

    public void setChavePJBank(String chavePJBank) {
        this.chavePJBank = chavePJBank;
    }

    public String getCredencialPJBank() {
        return credencialPJBank;
    }

    public void setCredencialPJBank(String credencialPJBank) {
        this.credencialPJBank = credencialPJBank;
    }

    public String getWebhWookPJBank() {
        return webhWookPJBank;
    }

    public void setWebhWookPJBank(String webhWookPJBank) {
        this.webhWookPJBank = webhWookPJBank;
    }

    public void setDiasLimiteVencimentoParcelaDCO(Integer diasLimiteVencimentoParcelaDCO) {
        this.diasLimiteVencimentoParcelaDCO = diasLimiteVencimentoParcelaDCO;
    }


    public boolean isOmitirSequencialArquivo() {
        return omitirSequencialArquivo;
    }

    public void setOmitirSequencialArquivo(boolean omitirSequencialArquivo) {
        this.omitirSequencialArquivo = omitirSequencialArquivo;
    }

    public String getDiretorioRemotoExtrato() {
        if (diretorioRemotoExtrato == null) {
            diretorioRemotoExtrato ="";
        }
        return diretorioRemotoExtrato;
    }

    public void setDiretorioRemotoExtrato(String diretorioRemotoExtrato) {
        this.diretorioRemotoExtrato = diretorioRemotoExtrato;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConvenioCobrancaVO that = (ConvenioCobrancaVO) o;
        return processarRemessasAutomatico == that.processarRemessasAutomatico;
    }

    @Override
    public int hashCode() {
        return Objects.hash(processarRemessasAutomatico);
    }

    public Integer getLimiteItensRemessa() {
        if (limiteItensRemessa == null) {
            limiteItensRemessa = 500;
        }
        return limiteItensRemessa;
    }

    public void setLimiteItensRemessa(Integer limiteItensRemessa) {
        this.limiteItensRemessa = limiteItensRemessa;
    }

    public boolean isCobrancaCobrancaDCO() {
        return getTipo() != null && getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO);
    }

    public NomenclaturaArquivoEnum getNomenclaturaArquivo() {
        if (nomenclaturaArquivo == null) {
            nomenclaturaArquivo = NomenclaturaArquivoEnum.NENHUM;
        }
        return nomenclaturaArquivo;
    }

    public void setNomenclaturaArquivo(NomenclaturaArquivoEnum nomenclaturaArquivo) {
        this.nomenclaturaArquivo = nomenclaturaArquivo;
    }

    public boolean isUtilizaExtrato() {
        return utilizaExtrato;
    }

    public void setUtilizaExtrato(boolean utilizaExtrato) {
        this.utilizaExtrato = utilizaExtrato;
    }

    public String getHostSFTPExtrato() {
        if (hostSFTPExtrato == null) {
            hostSFTPExtrato = "";
        }
        return hostSFTPExtrato;
    }

    public void setHostSFTPExtrato(String hostSFTPExtrato) {
        this.hostSFTPExtrato = hostSFTPExtrato;
    }

    public Integer getPortSFTPExtrato() {
        if (portSFTPExtrato == null) {
            portSFTPExtrato = 0;
        }
        return portSFTPExtrato;
    }

    public void setPortSFTPExtrato(Integer portSFTPExtrato) {
        this.portSFTPExtrato = portSFTPExtrato;
    }

    public String getUserSFTPExtrato() {
        if (userSFTPExtrato == null) {
            userSFTPExtrato = "";
        }
        return userSFTPExtrato;
    }

    public void setUserSFTPExtrato(String userSFTPExtrato) {
        this.userSFTPExtrato = userSFTPExtrato;
    }

    public String getPwdSFTPExtrato() {
        if (pwdSFTPExtrato == null) {
            pwdSFTPExtrato = "";
        }
        return pwdSFTPExtrato;
    }

    public void setPwdSFTPExtrato(String pwdSFTPExtrato) {
        this.pwdSFTPExtrato = pwdSFTPExtrato;
    }

    public String getNomenclaturaExtrato() {
        if (nomenclaturaExtrato == null) {
            nomenclaturaExtrato = "";
        }
        return nomenclaturaExtrato;
    }

    public void setNomenclaturaExtrato(String nomenclaturaExtrato) {
        this.nomenclaturaExtrato = nomenclaturaExtrato;
    }

    public String getMascaraDataDiretorioRemotoExtrato() {
        if (mascaraDataDiretorioRemotoExtrato == null) {
            mascaraDataDiretorioRemotoExtrato = "";
        }
        return mascaraDataDiretorioRemotoExtrato;
    }

    public void setMascaraDataDiretorioRemotoExtrato(String mascaraDataDiretorioRemotoExtrato) {
        this.mascaraDataDiretorioRemotoExtrato = mascaraDataDiretorioRemotoExtrato;
    }

    public String getDiretorioLocalExtrato() {
        if (diretorioLocalExtrato == null) {
            diretorioLocalExtrato = "";
        }
        return diretorioLocalExtrato;
    }

    public void setDiretorioLocalExtrato(String diretorioLocalExtrato) {
        this.diretorioLocalExtrato = diretorioLocalExtrato;
    }

    public String getDiretorioLocalLogExtrato() {
        if (diretorioLocalLogExtrato == null) {
            diretorioLocalLogExtrato = "";
        }
        return diretorioLocalLogExtrato;
    }

    public void setDiretorioLocalLogExtrato(String diretorioLocalLogExtrato) {
        this.diretorioLocalLogExtrato = diretorioLocalLogExtrato;
    }

    public boolean isObterCentralPactoExtrato() {
        return obterCentralPactoExtrato;
    }

    public void setObterCentralPactoExtrato(boolean obterCentralPactoExtrato) {
        this.obterCentralPactoExtrato = obterCentralPactoExtrato;
    }

    public String getMascaraDataDiretorioLocalExtrato() {
        if (mascaraDataDiretorioLocalExtrato == null) {
            mascaraDataDiretorioLocalExtrato = "";
        }
        return mascaraDataDiretorioLocalExtrato;
    }

    public void setMascaraDataDiretorioLocalExtrato(String mascaraDataDiretorioLocalExtrato) {
        this.mascaraDataDiretorioLocalExtrato = mascaraDataDiretorioLocalExtrato;
    }

    public boolean isIgnorarValidacoesExtrato() {
        return ignorarValidacoesExtrato;
    }

    public void setIgnorarValidacoesExtrato(boolean ignorarValidacoesExtrato) {
        this.ignorarValidacoesExtrato = ignorarValidacoesExtrato;
    }

    public VanExtratoEnum getVanExtrato() {
        if (vanExtrato == null) {
            vanExtrato = VanExtratoEnum.OUTRO;
        }
        return vanExtrato;
    }

    public void setVanExtrato(VanExtratoEnum vanExtrato) {
        this.vanExtrato = vanExtrato;
    }

    public boolean isVanExtratoCielo() {
        return this.getVanExtrato().equals(VanExtratoEnum.CIELO);
    }

    public String getDiretorioRemotoExtrato_Preparado(Date data) {
        String nome = getDiretorioRemotoExtrato();
        nome = nome.replaceAll("#DATA#", Uteis.getDataAplicandoFormatacao(data, getMascaraDataDiretorioRemotoExtrato()));
        return nome;
    }

    public String getDiretorioLocalExtrato_Preparado(Date data) {
        String nome = getDiretorioLocalExtrato();
        nome = nome.replaceAll("#DATA#", Uteis.getDataAplicandoFormatacao(data, getMascaraDataDiretorioLocalExtrato()));
        return nome;
    }

    public String getMascaraDiretorioExtrato_Title() {
        return "<div class=\"esquerda\" style=\"width: 500px\">\n" +
                "<div class=\"display:block;\" style=\"\n" +
                "    text-align: center;\n" +
                "\"><span class=\"text\" style=\"font-weight: bold;\">Máscara de DATA para diretório</span></div>\n" +
                "<div class=\"display:block;\" style=\"padding-top: 10px;\"><span class=\"text\">Caso o diretório tenha alguma pasta de data informar a TAG </span><span class=\"text\" style=\"font-weight: bold;\">#DATA#</span></div>\n" +
                "\n" +
                "<div class=\"display:block;\" style=\"padding-bottom: 10px;font-style: italic;padding-top: 10px;\"><span class=\"text\">Exemplo de diretório: \"/opt/ZW_EXTRATO/08-07-2020\"</span></div>\n" +
                "<div class=\"display:block;\"><span class=\"text\">Informar no diretório: </span><span class=\"text\" style=\"font-weight: bold;\">/opt/ZW_EXTRATO/#DATA#</span></div>\n" +
                "<div class=\"display:block;\"><span class=\"text\">Informar no campo de máscara: </span><span class=\"text\" style=\"font-weight: bold;\">dd-MM-yyyy</span></div>\n" +
                "</div>" +
                "";
    }

    public String getNomenclaturaExtrato_Title() {
        return "<div class=\"esquerda\" style=\"width: 500px\">\n" +
                "<div class=\"display:block;\" style=\"\n" +
                "    text-align: center;\n" +
                "\"><span class=\"text\" style=\"font-weight: bold;\">Padrão da nomenclatura dos arquivos</span></div>\n" +
                "<div class=\"display:block;\" style=\"padding-top: 10px;padding-bottom: 10px;\"><span class=\"text\">Tags disponíveis:</span></div>\n" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#CNPJ#</span><span class=\"text\"> para CNPJ da empresa</span></div>\n" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#NR_CONTRATO#</span><span class=\"text\"> para número do contrato (Convênio de Cobrança)</span></div>\n" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#NR_LOGICO#</span><span class=\"text\"> para número lógico</span></div>\n" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#CHAVE#</span><span class=\"text\"> para CHAVE da Empresa </span></div>\n" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#ARQ_REDE#</span><span class=\"text\"> para tipo do arquivo (REDE)</span></div>\n" +
                "<div class=\"display:block;\" style=\"padding-top: 10px;\"><span class=\"text\">Tags disponíveis para DATA:</span></div>\n" +
                "<div class=\"display:block;\" style=\"padding-bottom: 10px;\"><span class=\"text\">(Data Exemplo 08/07/2020)</span></div>\n" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#ddMMyyyy#</span><span class=\"text\"> Para o padrão: \"08072020\" </span></div></div>" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#yyyyMMdd#</span><span class=\"text\"> Para o padrão: \"20200708\" </span></div></div>" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#ddMMyy#</span><span class=\"text\"> Para o padrão: \"080720\" </span></div></div>" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#yyMMdd#</span><span class=\"text\"> Para o padrão: \"200708\" </span></div></div>" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#yyyy#</span><span class=\"text\"> Para o ANO: \"2020\" </span></div></div>" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#MM#</span><span class=\"text\"> Para o MÊS: \"07\" </span></div></div>" +
                "<div class=\"display:block;\"><span class=\"text\" style=\"font-weight: bold;\">#dd#</span><span class=\"text\"> Para o DIA: \"08\" </span></div></div>" +
                "";
    }
    public String getNomenclaturaExtrato_Preenchido(String chave, Date data, String tipoArquivoRede) {
        if (chave == null) {
            chave = "";
        }
        if (tipoArquivoRede == null) {
            tipoArquivoRede = "";
        }

        String nome = getNomenclaturaExtrato();
        nome = nome.replaceAll("#CNPJ#", Uteis.removerMascara(getEmpresa().getCNPJ()));
        nome = nome.replaceAll("#CHAVE#", chave);
        nome = nome.replaceAll("#NR_CONTRATO#", getNumeroContrato());
        nome = nome.replaceAll("#NR_LOGICO#", getNumeroLogico());
        if (data != null) {
            nome = nome.replaceAll("#ddMMyyyy#", Uteis.getDataAplicandoFormatacao(data, "ddMMyyyy"));
            nome = nome.replaceAll("#yyyyMMdd#", Uteis.getDataAplicandoFormatacao(data, "yyyyMMdd"));
            nome = nome.replaceAll("#ddMMyy#", Uteis.getDataAplicandoFormatacao(data, "ddMMyy"));
            nome = nome.replaceAll("#yyMMdd#", Uteis.getDataAplicandoFormatacao(data, "yyMMdd"));
            nome = nome.replaceAll("#yyyy#", Uteis.getDataAplicandoFormatacao(data, "yyyy"));
            nome = nome.replaceAll("#yy#", Uteis.getDataAplicandoFormatacao(data, "yy"));
            nome = nome.replaceAll("#MM#", Uteis.getDataAplicandoFormatacao(data, "MM"));
            nome = nome.replaceAll("#dd#", Uteis.getDataAplicandoFormatacao(data, "dd"));
        }
        nome = nome.replaceAll("#ARQ_REDE#", tipoArquivoRede);
        return nome;
    }

    public void gerarPadraoDiretorioLocalExtrato() {
        setDiretorioLocalExtrato("/opt/ZW_EXTRATO/#DATA#");
        setMascaraDataDiretorioLocalExtrato("dd-MM-yyyy");
    }

    public void gerarPadraoExtratoTivit() {
        setVanExtrato(VanExtratoEnum.TIVIT);
        setObterCentralPactoExtrato(false);
        setHostSFTPExtrato("**************");
        setPortSFTPExtrato(22);
        setUserSFTPExtrato("lxv685");
        setPwdSFTPExtrato("lxv685.123");
        setDiretorioRemotoExtrato("/out");
        gerarPadraoDiretorioLocalExtrato();
    }

    public void gerarPadraobuscarCentralPactoRedeTivit() {
            setDiretorioLocalExtrato("/opt/ZW_EXTRATO/#DATA#");
            setDiretorioRemotoExtrato("/opt/ZW_TIVIT/down/#DATA#_ED");
            setMascaraDataDiretorioLocalExtrato("dd-MM-yyyy");
            setMascaraDataDiretorioRemotoExtrato("dd-MM-yyyy");
            setNomenclaturaExtrato("#CNPJ#_#ARQ_REDE#");
            setVanExtrato(VanExtratoEnum.TIVIT);
    }

    public void gerarPadraoExtratoCielo() {
        setVanExtrato(VanExtratoEnum.CIELO);
        setObterCentralPactoExtrato(true);
        setDiretorioRemotoExtrato("/home/<USER>/extrato-diario");
        setNomenclaturaExtrato("_#NR_CONTRATO#_");
        gerarPadraoDiretorioLocalExtrato();
    }

    public void gerarPadraoExtratoRedeTivit() {
        setObterCentralPactoExtrato(true);
        gerarPadraobuscarCentralPactoRedeTivit();
    }

    public void gerarPadraoCentralPactoExtrato() {
        if (isObterCentralPactoExtrato()) {
            if (this.vanExtrato.equals(VanExtratoEnum.TIVIT)) {
                gerarPadraobuscarCentralPactoRedeTivit();
            } else {
                gerarPadraoDiretorioLocalExtrato();
            }
        }
    }

    public String getNumeroCompromisso() {
        if (numeroCompromisso == null) {
            numeroCompromisso = "";
        }
        return numeroCompromisso;
    }

    public void setNumeroCompromisso(String numeroCompromisso) {
        this.numeroCompromisso = numeroCompromisso;
    }

    public Double getDescontoBoleto() {
        return descontoBoleto == null ? 0.0 : descontoBoleto;
    }

    public void setDescontoBoleto(Double descontoBoleto) {
        this.descontoBoleto = descontoBoleto;
    }

    public String getTipoParcelamentoStone() {
        return tipoParcelamentoStone;
    }

    public void setTipoParcelamentoStone(String tipoParcelamentoStone) {
        this.tipoParcelamentoStone = tipoParcelamentoStone;
    }

    public boolean isApresentarAmbiente() {
        return this.getTipo() != null &&
                this.getTipo().getTipoCobranca() != null &&
                !this.isSomenteExtrato() &&
                (this.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) ||
                        this.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO_ONLINE) ||
                        this.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PIX));
    }

    public AmbienteEnum getAmbiente() {
        if (ambiente == null) {
            ambiente = AmbienteEnum.NENHUM;
        }
        return ambiente;
    }

    public void setAmbiente(AmbienteEnum ambiente) {
        this.ambiente = ambiente;
    }

    public boolean isApresentarOperacao() {
        return !getTipo().equals(TipoConvenioCobrancaEnum.NENHUM) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) &&
                !getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE) &&
                !getTipo().isPix();
    }

    public boolean isApresentarNumeroContrato() {
        return (!getTipo().equals(TipoConvenioCobrancaEnum.NENHUM) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) &&
                (!getTipo().isTransacaoOnline() || getTipo().isTransacaoOnline() && getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) &&
                !getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !getTipo().isPix())
                || isApresentarAbaExtrato();
    }

    public boolean isApresentarAbaExtrato() {
        return  !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT) &&
                !this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE) &&
                !this.getTipo().isPix() &&
                (this.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) || this.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC));
    }

    public boolean isApresentarExtensaoRemessa() {
        return getTipo().getTipoAutorizacao() != null &&
                getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE);
    }

    public boolean isApresentarSequencialDoArquivo() {
        return !getTipo().equals(TipoConvenioCobrancaEnum.NENHUM) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE) &&
                !getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) &&
                !getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !getTipo().isPix();
    }

    public boolean isApresentarTipoRemessa() {
        return !getTipo().equals(TipoConvenioCobrancaEnum.NENHUM) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) &&
                !getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE) &&
                !getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) &&
                !getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !getTipo().isPix();
    }

    public boolean isApresentarEnviarRemessaSFTPNow() {
        return !getTipo().equals(TipoConvenioCobrancaEnum.NENHUM) &&
                !getTipo().isPix() &&
                (getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC) || getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO));
    }

    public boolean isConvenioGetnetEDI() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET);
    }

    public boolean isApresentarConfiguracaoEnvioRemessa() {
        return (getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC) || getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO));
    }

    public boolean isConvenioVindi() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI);
    }

    public boolean isStone() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE);
    }

    public boolean isCieloOnline() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE);
    }

    public boolean isStoneV5() {
        return getTipo() != null && getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5);
    }

    public boolean isStoneConnect() {
        return getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT);
    }

    public boolean isRedeOnline() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE);
    }

    public String getBloquearCobrancaAutomaticaTitle() {
        StringBuilder title = new StringBuilder();
        title.append("<b>BLOQUEAR COBRANÇAS AUTOMÁTICAS</b>");
        title.append("<br/><br/>Ao marcar essa opção, <b>SOMENTE AS COBRANÇAS AUTOMÁTICAS SERÃO BLOQUEADAS.</b>");
        title.append("<br/>As demais funções do convênio de cobrança ainda serão realizadas, por exemplo:<br/>");
        title.append("<br/><b>-</b> Geração manual de remessa EDI DCC ou DCO");
        title.append("<br/><b>-</b> Retorno automático de remessas");
        title.append("<br/><b>-</b> Envio automático de remessas já geradas");
        title.append("<br/><b>-</b> Cobrança manual de transação online");
        title.append("<br/><b>-</b> Verificador de transação pendente (Aprovada Não Capturada)");
        return title.toString();
    }

    public boolean isBloquearCobrancaAutomatica() {
        return bloquearCobrancaAutomatica;
    }

    public void setBloquearCobrancaAutomatica(boolean bloquearCobrancaAutomatica) {
        this.bloquearCobrancaAutomatica = bloquearCobrancaAutomatica;
    }

    public List<ConvenioCobrancaRateioVO> getListaConvenioCobrancaRateioVO() {
        if (listaConvenioCobrancaRateioVO == null) {
            listaConvenioCobrancaRateioVO = new ArrayList<>();
        }
        return listaConvenioCobrancaRateioVO;
    }

    public void setListaConvenioCobrancaRateioVO(List<ConvenioCobrancaRateioVO> listaConvenioCobrancaRateioVO) {
        this.listaConvenioCobrancaRateioVO = listaConvenioCobrancaRateioVO;
    }

    public boolean isPagarMe() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME);
    }

    public boolean isPagBank() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK);
    }

    public boolean isPagoLivre() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE);
    }

    public boolean isFacilitePay() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY);
    }

    public boolean isCeopag() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG);
    }

    public boolean isPinBank() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK);
    }

    public boolean isMundiPagg() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG);
    }

    public boolean isGetnetOnline() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE);
    }

    public boolean isStripe() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE);
    }

    public boolean isTipoNenhum() {
        return getTipo().equals(TipoConvenioCobrancaEnum.NENHUM);
    }

    public boolean isSomenteExtrato() {
        return somenteExtrato;
    }

    public void setSomenteExtrato(boolean somenteExtrato) {
        this.somenteExtrato = somenteExtrato;
    }

    public String getAccessTokenCielo() {
        return accessTokenCielo;
    }

    public void setAccessTokenCielo(String accessTokenCielo) {
        this.accessTokenCielo = accessTokenCielo;
    }

    public String getRefreshTokenCielo() {
        return refreshTokenCielo;
    }

    public void setRefreshTokenCielo(String refreshTokenCielo) {
        this.refreshTokenCielo = refreshTokenCielo;
    }

    public String getRegisterIDCredenciamentoCielo() {
        return RegisterIDCredenciamentoCielo;
    }

    public void setRegisterIDCredenciamentoCielo(String registerIDCredenciamentoCielo) {
        RegisterIDCredenciamentoCielo = registerIDCredenciamentoCielo;
    }

    public boolean isPactoPay() {
        return pactoPay;
    }

    public void setPactoPay(boolean pactoPay) {
        this.pactoPay = pactoPay;
    }

    public ConvenioDTO toConvenioDTO() {
        ConvenioDTO convenioDTO = new ConvenioDTO();
        convenioDTO.setIdReferencia(this.getCodigo());
        convenioDTO.setDescricao(this.getDescricao());
        convenioDTO.setAmbiente(this.getAmbiente().getCodigo());
        convenioDTO.setTipo(this.getTipo().getCodigo());
        convenioDTO.setNumeroContrato(this.getNumeroContrato());
        convenioDTO.setDataRegistro(Calendario.getDataAplicandoFormatacao(this.getDataCadastro(), "yyyyMMddHHmmss"));


        if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            convenioDTO.setNumeroFiliacao(this.getCodigoAutenticacao01());
            convenioDTO.setToken(this.getCodigoAutenticacao02());
        } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
            convenioDTO.setMerchantId(this.getCodigoAutenticacao01());
            convenioDTO.setMerchantKey(this.getCodigoAutenticacao02());
        } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
            convenioDTO.setClientId(this.getCodigoAutenticacao01());
            convenioDTO.setClientSecret(this.getCodigoAutenticacao02());
            convenioDTO.setSellerId(this.getCodigoAutenticacao03());
        } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
            convenioDTO.setStoneCode(this.getCodigoAutenticacao01());
            convenioDTO.setSaleAffiliationKey(this.getCodigoAutenticacao02());
        } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
            convenioDTO.setChavePrivada(this.getCodigoAutenticacao01());
            convenioDTO.setChavePublica(this.getCodigoAutenticacao02());
        } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
            convenioDTO.setChaveAPI(this.getCodigoAutenticacao01());
            convenioDTO.setChaveCriptografia(this.getCodigoAutenticacao02());
        } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
            convenioDTO.setTokenAPI(this.getCodigoAutenticacao01());
        } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
            convenioDTO.setChavePublica(this.getCodigoAutenticacao01());
            convenioDTO.setChaveSecreta(this.getCodigoAutenticacao02());
        } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
            convenioDTO.setSecretKey(this.getCodigoAutenticacao02());
        } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            convenioDTO.setUserName(this.getCodigoAutenticacao01());
            convenioDTO.setKeyValue(this.getCodigoAutenticacao02());
            convenioDTO.setGrantType(this.getCodigoAutenticacao03());
            convenioDTO.setRequestOrigin(this.getCodigoAutenticacao04());
            convenioDTO.setCodigoCanal(this.getCodigoAutenticacao05());
            convenioDTO.setCodigoCliente(this.getCodigoAutenticacao06());
            convenioDTO.setKeyLoja(this.getCodigoAutenticacao07());
        } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT)) {
            convenioDTO.setPrivateSecretKey(this.getCodigoAutenticacao01());
        } else {

            convenioDTO.setNumeroLogico(this.getNumeroLogico());

            if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                convenioDTO.setChaveCriptografiaArquivo(this.getChaveBIN());
            } else if (this.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                convenioDTO.setChaveCriptografiaArquivo(this.getChaveGETNET());
            }

            convenioDTO.setChaveDescriptografiaArquivo(this.getNossaChave());
            convenioDTO.setSenhaDescriptografiaArquivo(this.getNossaSenha());

            SftpDTO sftpDTO = new SftpDTO();
            sftpDTO.setUsersftp(this.getUserSFTP());
            sftpDTO.setPwdsftp(this.getPwdSFTP());
            sftpDTO.setHostsftp(this.getHostSFTP());
            try {
                sftpDTO.setPortsftp(Integer.parseInt(this.getPortSFTP()));
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            sftpDTO.setDiretorioDown(this.getDiretorioRemotoTIVIT_IN());
            sftpDTO.setDiretorioUp(this.getDiretorioRemotoTIVIT_OUT());
            convenioDTO.setSftpConfig(sftpDTO);

            ContaCorrenteDTO contaCorrenteDTO = new ContaCorrenteDTO();
            contaCorrenteDTO.setAgencia(this.getContaEmpresa().getAgencia());
            contaCorrenteDTO.setAgenciaDigitoVerificador(this.getContaEmpresa().getAgenciaDV());
            contaCorrenteDTO.setDocumento(this.getEmpresa().getCNPJ());
            contaCorrenteDTO.setTipoDocumento(TipoDocumentoEnum.CNPJ.name());
            contaCorrenteDTO.setContaCorrente(this.getContaEmpresa().getContaCorrente());
            contaCorrenteDTO.setContaCorrenteDigitoVerificador(this.getContaEmpresa().getContaCorrenteDV());
            contaCorrenteDTO.setCodigoBanco(this.getContaEmpresa().getBanco().getCodigoBanco().toString());
            contaCorrenteDTO.setOperacao(this.getContaEmpresa().getCodigoOperacao());
            convenioDTO.setContaCorrente(contaCorrenteDTO);
        }
        return convenioDTO;
    }

    public String getValueCampoChavePix() {
        //Usado no Hint do campo "Chave Pix da conta"
        try {
            String banco = "";
            banco = getTipo().getDescricao().replace("Pix ","");
            return "Chave Pix da conta " + banco + ":";
        } catch (Exception ignore) {
        }
        return "Chave Pix da Conta";
    }

    public String getTitleApenasNomeBancoPix() {
        //Usado no Hint do campo "Chave Pix da conta"
        try {
            String banco = "";
            banco = getTipo().getDescricao().replace("Pix ","");
            return "Informe a CHAVE PIX utilizada para receber pix na conta bancária da empresa. Pode ser que tenha sido gerado uma chave pix de cnpj, telefone, email ou aleatória.</br> " +
                    "<b>Se a sua chave pix for telefone</b>, deve ser informado no formato <b>+55+DDD+NUMERO</b> ex: +5562984525252 (não colocar zero a esquerda no DDD e não esquecer de colocar o +55 antes de tudo também)." +
                    "</br><b>Se a sua chave pix for CNPJ</b>, deve ser informado apenas os número." +
                    "</br>Caso não tenha uma chave ainda, gere na sua conta bancária " + banco + " uma nova chave pix.";
        } catch (Exception ignore) {
        }
        return "";
    }

    public String getPixClientId() {
        return pixClientId;
    }

    public void setPixClientId(String pixClientId) {
        this.pixClientId = pixClientId;
    }

    public String getPixClientSecret() {
        return pixClientSecret;
    }

    public void setPixClientSecret(String pixClientSecret) {
        this.pixClientSecret = pixClientSecret;
    }

    public String getPixAppKey() {
        return pixAppKey;
    }

    public void setPixAppKey(String pixAppKey) {
        this.pixAppKey = pixAppKey;
    }

    public String getPixBasicAuth() {
        return pixBasicAuth;
    }

    public void setPixBasicAuth(String pixBasicAuth) {
        this.pixBasicAuth = pixBasicAuth;
    }

    public String getPixChave() {
        return pixChave;
    }

    public void setPixChave(String pixChave) {
        this.pixChave = pixChave;
    }

    public Integer getPixExpiracao() {
        return pixExpiracao;
    }

    public void setPixExpiracao(Integer pixExpiracao) {
        this.pixExpiracao = pixExpiracao;
    }

    public boolean isPixBB() {
        return getTipo().equals(TipoConvenioCobrancaEnum.PIX_BB);
    }

    public boolean isBoletoBancoBrasil() {
        return getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE);
    }

    public boolean isPixBradesco() {
        return getTipo().equals(TipoConvenioCobrancaEnum.PIX_BRADESCO);
    }

    public boolean isPixSantander() {
        return getTipo().equals(TipoConvenioCobrancaEnum.PIX_SANTANDER);
    }

    public boolean isPixInter() {
        return getTipo().equals(TipoConvenioCobrancaEnum.PIX_INTER);
    }

    public boolean isDccCaixaOnline() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE);
    }

    public boolean isPixItau() {
        return getTipo().equals(TipoConvenioCobrancaEnum.PIX_ITAU);
    }

    public boolean isPixAfinz() {
        return getTipo().equals(TipoConvenioCobrancaEnum.PIX_AFINZ);
    }

    public CurrencyConvenioEnum getCurrencyConvenioEnum() {
        return currencyConvenioEnum;
    }

    public void setCurrencyConvenioEnum(CurrencyConvenioEnum currencyConvenioEnum) {
        this.currencyConvenioEnum = currencyConvenioEnum;
    }

    public ArquivoLayoutRemessaEnum getLayoutBoletoOnline() {
        if (layoutBoletoOnline == null) {
            layoutBoletoOnline = ArquivoLayoutRemessaEnum.NENHUM;
        }
        return layoutBoletoOnline;
    }

    public void setLayoutBoletoOnline(ArquivoLayoutRemessaEnum layoutBoletoOnline) {
        this.layoutBoletoOnline = layoutBoletoOnline;
    }

    public boolean isApresentarLayoutBoletoOnline() {
        return getTipo().getTipoBoleto() != null &&
                getTipo().getTipoBoleto().equals(TipoBoletoEnum.ITAU);
    }

    public boolean isItauRegistroOnline() {
        return getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE);
    }

    public boolean isCaixaRegistroOnline() {
        return getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE);
    }

    public boolean isApresentarCampoCurrency() {
        if (!TipoConvenioCobrancaEnum.NENHUM.equals(getTipo()) &&
                (TipoConvenioCobrancaEnum.DCC_STRIPE.equals(getTipo()) || TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT.equals(getTipo()))) {
            return true;
        } else {
            return false;
        }
    }

    public boolean isUtilizaCertificadoPix() {
        return getTipo().equals(TipoConvenioCobrancaEnum.PIX_BRADESCO);
    }

    public List<ConvenioCobrancaArquivoVO> getListaConvenioCobrancaArquivoVO() {
        if (UteisValidacao.emptyList(listaConvenioCobrancaArquivoVO)) {
            return new ArrayList<>();
        }
        return listaConvenioCobrancaArquivoVO;
    }

    public void setListaConvenioCobrancaArquivoVO(List<ConvenioCobrancaArquivoVO> listaConvenioCobrancaArquivoVO) {
        this.listaConvenioCobrancaArquivoVO = listaConvenioCobrancaArquivoVO;
    }

    public boolean isMaxipago() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO);
    }

    public AdquirenteMaxiPagoEnum getAdquirenteMaxiPago() {
        if (adquirenteMaxiPago == null) {
            return AdquirenteMaxiPagoEnum.NENHUM;
        }
        return adquirenteMaxiPago;
    }

    public void setAdquirenteMaxiPago(AdquirenteMaxiPagoEnum adquirenteMaxiPago) {
        this.adquirenteMaxiPago = adquirenteMaxiPago;
    }

    public boolean isOnePayment() {
        return getTipo().equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT);
    }

    public boolean isSandbox() {
        return getAmbiente().equals(AmbienteEnum.HOMOLOGACAO);
    }

    public JSONObject toJSON(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) {
        JSONObject json = new JSONObject();
        json.put("codigo", this.codigo);
        json.put("descricao", this.descricao);
        json.put("tipo", this.tipo != null ? this.tipo.toJSON() : tipoConvenioCobrancaEnum.toJSON());

        return json;
    }
    public TipoBoletoPJBankEnum getTipoBoletoPJBank() {
        if (tipoBoletoPJBank == null) {
            tipoBoletoPJBank = TipoBoletoPJBankEnum.BOLETO;
        }
        return tipoBoletoPJBank;
    }
    public void setTipoBoletoPJBank(TipoBoletoPJBankEnum tipoBoletoPJBank) {
        this.tipoBoletoPJBank = tipoBoletoPJBank;
    }

    public Integer getDiasExpirarPix() {
        if (diasExpirarPix == null) {
            return 0;
        } else {
            return diasExpirarPix;
        }
    }

    public void setDiasExpirarPix(Integer diasExpirarPix) {
        this.diasExpirarPix = diasExpirarPix;
    }

    public boolean isEnviarNotificacoes() {
        return enviarNotificacoes;
    }

    public void setEnviarNotificacoes(boolean enviarNotificacoes) {
        this.enviarNotificacoes = enviarNotificacoes;
    }

    public boolean isGerarMultaEJurosRemessaItauCNAB400() {
        return gerarMultaEJurosRemessaItauCNAB400;
    }

    public void setGerarMultaEJurosRemessaItauCNAB400(boolean gerarMultaEJurosRemessaItauCNAB400) {
        this.gerarMultaEJurosRemessaItauCNAB400 = gerarMultaEJurosRemessaItauCNAB400;
    }

    public boolean getApresentarInativoNoPactoPay() {
        return apresentarInativoNoPactoPay;
    }

    public void setApresentarInativoNoPactoPay(boolean apresentarInativoNoPactoPay) {
        this.apresentarInativoNoPactoPay = apresentarInativoNoPactoPay;
    }

    public boolean isVerificacaoZeroDollar() {
        return verificacaoZeroDollar;
    }

    public void setVerificacaoZeroDollar(boolean verificacaoZeroDollar) {
        this.verificacaoZeroDollar = verificacaoZeroDollar;
    }

    public boolean isTipoPix() {
        return isPixBB() || isPixPjBank() || isPixAsaas() || isPixBradesco() || isPixSantander() || isPixInter() || isPixItau() || isPixAfinz();
    }


    public ERedeStatusConciliacaoEnum getStatusConciliacaoRedeOnline() {
        if (statusConciliacaoRedeOnline == null) {
            statusConciliacaoRedeOnline = ERedeStatusConciliacaoEnum.NENHUM;
        }
        return statusConciliacaoRedeOnline;
    }
    public void setStatusConciliacaoRedeOnline(ERedeStatusConciliacaoEnum statusConciliacaoRedeOnline) {
        this.statusConciliacaoRedeOnline = statusConciliacaoRedeOnline;
    }

    public String getRequestIdConciliacaoRedeOnline() {
        if (requestIdConciliacaoRedeOnline == null) {
            return "";
        } else {
            return requestIdConciliacaoRedeOnline;
        }
    }

    public void setRequestIdConciliacaoRedeOnline(String requestIdConciliacaoRedeOnline) {
        this.requestIdConciliacaoRedeOnline = requestIdConciliacaoRedeOnline;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Date getDataGeracaoAccessToken() {
        return dataGeracaoAccessToken;
    }

    public void setDataGeracaoAccessToken(Date dataGeracaoAccessToken) {
        this.dataGeracaoAccessToken = dataGeracaoAccessToken;
    }

    public boolean isRegistrarBoletoOnlineSomenteNaImpressao() {
        return registrarBoletoOnlineSomenteNaImpressao;
    }

    public void setRegistrarBoletoOnlineSomenteNaImpressao(boolean registrarBoletoOnlineSomenteNaImpressao) {
        this.registrarBoletoOnlineSomenteNaImpressao = registrarBoletoOnlineSomenteNaImpressao;
    }

    public boolean isUsaSplitPagamentoStoneV5() {
        return usaSplitPagamentoStoneV5;
    }

    public void setUsaSplitPagamentoStoneV5(boolean usaSplitPagamentoStoneV5) {
        this.usaSplitPagamentoStoneV5 = usaSplitPagamentoStoneV5;
    }

    public TipoCredencialStoneEnum getTipoCredencialStoneEnum() {
        if (tipoCredencialStoneEnum == null) {
            tipoCredencialStoneEnum = TipoCredencialStoneEnum.NENHUM;
        }
        return tipoCredencialStoneEnum;
    }

    public void setTipoCredencialStoneEnum(TipoCredencialStoneEnum tipoCredencialStoneEnum) {
        this.tipoCredencialStoneEnum = tipoCredencialStoneEnum;
    }

    public String getCodigoAutenticacao01Exibir() {
        if (isPermiteVisualizarCredenciaisOriginais() || isStone() || isStoneConnect()) {
            return codigoAutenticacao01;
        }
        return mascararCodigoAutenticacao(getCodigoAutenticacao01());
    }

    public void setCodigoAutenticacao01Exibir(String codigoAutenticacao01Exibir) {
        this.codigoAutenticacao01Exibir = codigoAutenticacao01Exibir;
    }

    public String getCodigoAutenticacao02Exibir() {
        if (isPermiteVisualizarCredenciaisOriginais()) {
            return codigoAutenticacao02;
        }
        return mascararCodigoAutenticacao(getCodigoAutenticacao02());
    }

    public void setCodigoAutenticacao02Exibir(String codigoAutenticacao02Exibir) {
        this.codigoAutenticacao02Exibir = codigoAutenticacao02Exibir;
    }

    public String getCodigoAutenticacao03Exibir() {
        if (isPermiteVisualizarCredenciaisOriginais()) {
            return codigoAutenticacao03;
        }
        return mascararCodigoAutenticacao(getCodigoAutenticacao03());
    }

    public void setCodigoAutenticacao03Exibir(String codigoAutenticacao03Exibir) {
        this.codigoAutenticacao03Exibir = codigoAutenticacao03Exibir;
    }

    public String getCodigoAutenticacao04Exibir() {
        if (isPermiteVisualizarCredenciaisOriginais()) {
            return codigoAutenticacao04;
        }
        return mascararCodigoAutenticacao(getCodigoAutenticacao04());
    }

    public void setCodigoAutenticacao04Exibir(String codigoAutenticacao04Exibir) {
        this.codigoAutenticacao04Exibir = codigoAutenticacao04Exibir;
    }

    public String getCodigoAutenticacao05Exibir() {
        if (isPermiteVisualizarCredenciaisOriginais()) {
            return codigoAutenticacao05;
        }
        return mascararCodigoAutenticacao(getCodigoAutenticacao05());
    }

    public void setCodigoAutenticacao05Exibir(String codigoAutenticacao05Exibir) {
        this.codigoAutenticacao05Exibir = codigoAutenticacao05Exibir;
    }

    public String getCodigoAutenticacao06Exibir() {
        if (isPermiteVisualizarCredenciaisOriginais()) {
            return codigoAutenticacao06;
        }
        return mascararCodigoAutenticacao(getCodigoAutenticacao06());
    }

    public void setCodigoAutenticacao06Exibir(String codigoAutenticacao06Exibir) {
        this.codigoAutenticacao06Exibir = codigoAutenticacao06Exibir;
    }

    public String getCodigoAutenticacao07Exibir() {
        if (isPermiteVisualizarCredenciaisOriginais()) {
            return codigoAutenticacao07;
        }
        return mascararCodigoAutenticacao(getCodigoAutenticacao07());
    }

    public void setCodigoAutenticacao07Exibir(String codigoAutenticacao07Exibir) {
        this.codigoAutenticacao07Exibir = codigoAutenticacao07Exibir;
    }

    public String mascararCodigoAutenticacao(String valor) {
        if (UteisValidacao.emptyString(valor)) {
            return "";
        }
        if (valor.length() <= 7) {
            return valor; //valores curtos, não mascarar
        }
        String inicio = valor.substring(0, 2);
        String fim = valor.substring(valor.length() - 3);
        String meio = new String(new char[valor.length() - 5]).replace('\0', '*');
        return inicio + meio + fim;
    }

    public boolean isPermiteVisualizarCredenciaisOriginais() {
        return permiteVisualizarCredenciaisOriginais;
    }

    public void setPermiteVisualizarCredenciaisOriginais(boolean permiteVisualizarCredenciaisOriginais) {
        this.permiteVisualizarCredenciaisOriginais = permiteVisualizarCredenciaisOriginais;
    }
}
