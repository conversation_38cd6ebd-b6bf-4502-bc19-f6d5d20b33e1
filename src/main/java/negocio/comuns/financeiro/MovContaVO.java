/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import annotations.arquitetura.CampoCalendario;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoContaPagarLoteEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import controle.financeiro.HistoricoContabilEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.enumerador.MovContaNaoElegivelLotePagamentoEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.openbanking.stone.PagamentoStoneVO;
import negocio.comuns.financeiro.openbanking.stone.RetornoStoneVO;
import negocio.comuns.financeiro.openbanking.stone.TransferenciaStoneVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MovContaVO extends SuperVO {
	
	@ChavePrimaria
    private Integer codigo;
	@ChaveEstrangeira
    private PessoaVO pessoaVO;
	@ChaveEstrangeira
    private EmpresaVO empresaVO;
	@ChaveEstrangeira
	private ContaVO contaVO;
	private String descricao;
    private String observacoes;
    @NaoControlarLogAlteracao
    @Lista
    private List<MovContaRateioVO> movContaRateios;
	@NaoControlarLogAlteracao
    private String mudarCorLinkValor;
	@ChaveEstrangeira
    private UsuarioVO usuarioVO;
	@CampoCalendario
    private Date dataLancamento;
    private Double valor;
    private Double valorPago;
    private Double valorOriginalAlterado;
    private Boolean app = false;
    @CampoCalendario
    private Date dataQuitacao;
    private TipoOperacaoLancamento tipoOperacaoLancamento;
    @Lista
    private List<ChequeVO> cheques = new ArrayList<ChequeVO>();
    @Lista
    private List<CartaoCreditoVO> cartoes = new ArrayList<CartaoCreditoVO>();
    @CampoCalendario
    private Date dataCompetencia;
    @CampoCalendario
    private Date dataVencimento;
    private int agendamentoFinanceiro = 0;
    private int nrParcela = 0;
    @NaoControlarLogAlteracao
    private String styleLinhaSelecionada;
    //atributo usado para mostrar links de Recebiveis
    private Boolean tipoRecebivel = false;
    @NaoControlarLogAlteracao
    private ResumoFormaPagamentoTO resumoFormaPagamentoTO = new ResumoFormaPagamentoTO();
    @NaoControlarLogAlteracao
    private LoteVO lote = new LoteVO();
    @NaoControlarLogAlteracao
    private Integer contaOrigem = 0;
    @NaoControlarLogAlteracao
    private OperadoraCartaoVO operadoraCartaoVO = new OperadoraCartaoVO();
    @NaoControlarLogAlteracao
    private Integer nrParcelaCartaoCredito = 0;
    private String autorizacaoCartao;
    @NaoControlarLogAlteracao
    private boolean temLote = false;
    @NaoControlarLogAlteracao
    private Double valorLiquido = 0.0;
    @NaoControlarLogAlteracao
    private boolean liquido = false;
    @NaoControlarLogAlteracao
    private boolean apresentarNoCaixa = false;
    @NaoControlarLogAlteracao
    private Integer caixa;
    @NaoControlarLogAlteracao
    private List<ChequeTO> chequesRetirados = new ArrayList<ChequeTO>();
    @NaoControlarLogAlteracao
    private List<CartaoCreditoTO> cartoesRetirados = new ArrayList<CartaoCreditoTO>();
    @NaoControlarLogAlteracao
    private boolean lancamentoSelecionado = false;
    @NaoControlarLogAlteracao
    private List<MovContaVO> contasAPagarConjunto;
    @NaoControlarLogAlteracao
    private String conjuntoPagamento;
    private Integer movProduto;
    private Boolean nfseEmitida = false;
    private Boolean nfceEmitida = false;
    @NaoControlarLogAlteracao
    private String matricula;
    private String nomePlanoConta; // atributo transient
    private String nomeCentroCusto;  // atributo transient
    @NaoControlarLogAlteracao
    private MovContaContabilVO movContaContabilVO = new MovContaContabilVO();
    private List<AnexoMovContaVO> anexos = null;
    private boolean incluirMovContaContabil = false;
    public static String NOME_ENTIDADE_LOG_ALTERACAO_MOVCONTA = "OPERACAOMOVCONTA";
    @NaoControlarLogAlteracao
    private LoteVO lotePagouConta;
    @NaoControlarLogAlteracao
    private boolean validarFavorecido = true;

    @NaoControlarLogAlteracao
    private Date dataUltimaAlteracao;

    @NaoControlarLogAlteracao
    private boolean excluido = false;
    @NaoControlarLogAlteracao
    private LogVO logExclusao;
    @NaoControlarLogAlteracao
    private boolean depositoAVouCD = false;
    private EmpresaVO empresa;
    private String codigosParcelas = "";
    private String vencimentosParcelas = "";
    private String numerosParcelas = "";
    private AnexoMovContaVO anexo = new AnexoMovContaVO();
    private Integer codigoNotaEmitida;
    private String codigoBarras = "";
    private String chaveArquivoConta;
    private String chaveArquivoComprovante;
    private String extensaoArquivoComprovanteMovConta;
    private String extensaoArquivoContaMovConta;
    private ContaVO contaDestinoOpenBank;
    private TipoFormaPagto tipoForma;
    private Integer compraEstoque;
    private String numeroDocumento;
    private String tipoDocumento = "";
    @NaoControlarLogAlteracao
    private boolean usarHoraAtual = true;
    @NaoControlarLogAlteracao
    private String idImportacao;

    private Double taxaAntecipacao;
    private String identificadorOrigem;
    private Integer identificadorOrigemTipo;
    private String identificadorDados;
    private String valorApresentar;
    private Double valorOriginalAntesDaConciliacao;
    @NaoControlarLogAlteracao
    private boolean definirPlanoContaCentroCustoValorSuperior = false;
    @NaoControlarLogAlteracao
    private PlanoContaTO planoContaValorSuperior;
    @NaoControlarLogAlteracao
    private CentroCustoTO centroCustoValorSuperior;
    @NaoControlarLogAlteracao
    private boolean movimentandoTipoFormaPix;
    private boolean retiradaAutomaticaRecebivelOrigemCancelamento;
    private String infoMovimentacaoAutomaticaConciliacao;
    private TipoContaPagarLoteEnum tipoContaPagarLoteEnum;
    private String payloadPix;
    @NaoControlarLogAlteracao
    private boolean elegivelParaLoteDePagamento;
    private List<MovContaNaoElegivelLotePagamentoEnum> listaMotivoNaoElegivelLotePagamentoEnum;
    private boolean contaDeConsumo;
    private boolean fornecedorPossuiDadosBancarios;
    private ContaBancariaFornecedorVO contaBancariaFornecedorVO;
    private boolean presaEmLoteDePagamento;
    private Integer loteDePagamento;
    private boolean pagoOrigemWebhook;
    private String cpfOuCnpjBeneficiario;
    private String idExterno;

    /**
     * <AUTHOR> Alcides
     * 16/12/2011
     */
    public static void verificaSelecionadoSetaStyle(Integer nrParcela, List<MovContaVO> lista){
    	for(MovContaVO movConta : lista){
    		if(!UteisValidacao.emptyNumber(nrParcela) && nrParcela.equals(movConta.nrParcela)){
    			movConta.styleLinhaSelecionada = "background-color: #D7D7D7;";
        	}else{
        		movConta.styleLinhaSelecionada = "";
        	}
    	}
    }
    
    public List<MovContaRateioVO> getListaRateiosClone() throws Exception{
    	List<MovContaRateioVO> lista = new ArrayList<MovContaRateioVO>();
    	for(MovContaRateioVO rateio : this.getMovContaRateios()){
    		lista.add((MovContaRateioVO) rateio.getClone(true));
    	}
    	return lista;
    }
    /**
	 * @return the dataCompetencia
	 */
	public Date getDataCompetencia() {
		return dataCompetencia;
	}

	/**
	 * @param dataCompetencia the dataCompetencia to set
	 */
	public void setDataCompetencia(Date dataCompetencia) {
		this.dataCompetencia = dataCompetencia;
	}
	/**
	 * @return the datavencimento
	 */
	public Date getDataVencimento() {
		return dataVencimento;
	}

	/**
	 * @param datavencimento the datavencimento to set
	 */
	public void setDataVencimento(Date datavencimento) {
		this.dataVencimento = datavencimento;
	}

	public MovContaVO() {
        inicializarDados();
    }

    public MovContaVO(JSONObject json) throws Exception {
        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setCodigo(json.optInt("pessoa"));
        this.pessoaVO = pessoaVO;
        UsuarioVO usuarioVO = new UsuarioVO();
        usuarioVO.setCodigo(json.optInt("usuario"));
        this.usuarioVO = usuarioVO;
        EmpresaVO empresaVO = new EmpresaVO();
        empresaVO.setCodigo(json.optInt("empresa"));
        this.empresaVO = empresaVO;
        this.descricao = json.optString("descricao");
        this.observacoes = json.optString("observacoes");
        this.valor = json.optDouble("valor");
        this.codigoBarras = json.optString("codigoBarras");
        this.payloadPix = json.optString("payloadPix");

        if (!UteisValidacao.emptyString(json.optString("dataLancamento"))) {
            try {
                this.dataLancamento = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").parse(json.optString("dataLancamento"));
            } catch (Exception e) {
                throw new Exception("Data de Lançamento informada está no formato inválido. Formato esperado é dd/MM/yyyy HH:mm:ss.");
            }
        } else {
            this.dataLancamento = Calendario.hoje();
        }

        if (!UteisValidacao.emptyString(json.optString("dataVencimento"))) {
            if (json.optString("dataVencimento").length() != 10) {
                throw new Exception("Data de Vencimento informada está no formato inválido. Formato esperado é dd/MM/yyyy.");
            }
            try {
                this.dataVencimento = new SimpleDateFormat("dd/MM/yyyy").parse(json.optString("dataVencimento"));
            } catch (Exception e) {
                throw new Exception("Data de Vencimento informada está no formato inválido. Formato esperado é dd/MM/yyyy.");
            }
        } else {
            throw new Exception("Data de Vencimento é obrigatória. Formato esperado é dd/MM/yyyy.");
        }

        if (!UteisValidacao.emptyString(json.optString("dataCompetencia"))) {
            if (json.optString("dataCompetencia").length() != 10) {
                throw new Exception("Data de Competência informada está no formato inválido. Formato esperado é dd/MM/yyyy.");
            }
            try {
                this.dataCompetencia = new SimpleDateFormat("dd/MM/yyyy").parse(json.optString("dataCompetencia"));
            } catch (Exception e) {
                throw new Exception("Data de Competência informada está no formato inválido. Formato esperado é dd/MM/yyyy.");
            }
        } else {
            throw new Exception("Data de Competência é obrigatória. Formato esperado é dd/MM/yyyy.");
        }

        this.tipoContaPagarLoteEnum = TipoContaPagarLoteEnum.obterPorCodigo(json.optInt("tipoContaPagarLoteEnum"));
        this.tipoOperacaoLancamento = TipoOperacaoLancamento.getTipoOperacaoLancamento(json.optInt("tipoOperacaoLancamentoEnum"));
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setObservacoes(getObservacoes().toUpperCase());
    }

    public void inicializarDados() {
        setCodigo(0);
        setPessoaVO(new PessoaVO());
        setDescricao("");
        setObservacoes("");
        setDataLancamento(Calendario.hoje());
        setDataQuitacao(null);
        setContaVO(new ContaVO());
        setValor(0.0);
        setConjuntoPagamento("");
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>MovContaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception \ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(MovContaVO obj) throws ConsistirException {

        MovContaVO objAntesAlteracao = (MovContaVO) obj.getObjetoVOAntesAlteracao();

        if (!obj.getValidarDados().booleanValue()) {
            return;
        }

        if (obj.getEmpresaVO().getCodigo() == null || obj.getEmpresaVO().getCodigo() == 0) {
            throw new ConsistirException("O campo EMPRESA deve ser informado.");
        }
        if (obj.getTipoOperacaoLancamento() == null) {
            throw new ConsistirException("O campo Tipo de Operação do Lançamento deve ser informado.");
        }
        if (obj.isValidarFavorecido() && obj.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.RECEBIMENTO) &&
        		(obj.getPessoaVO() == null || UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo()))) {
            throw new ConsistirException("O campo  'Receber de' deve ser informado. Selecione um FORNECEDOR/PESSOA existente ou crie um novo.");
        }
        if (obj.isValidarFavorecido() && obj.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.PAGAMENTO) &&
        		(obj.getPessoaVO() == null || UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo()))) {
            throw new ConsistirException("O campo  'Pagar para' deve ser informado. Selecione um FORNECEDOR/PESSOA existente ou crie um novo.");
        }
        
        if (obj.isValidarFavorecido() && (obj.getPessoaVO() == null || UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo()))) {
            throw new ConsistirException("O campo  'Favorecido' deve ser informado. Selecione um FORNECEDOR/PESSOA existente ou crie um novo.");
        }
        
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Lançamento) deve ser informado.");
        }
        if (obj.getValor() == null || obj.getValor() == 0.0) {
            throw new ConsistirException("O campo VALOR (Lançamento) deve ser informado.");
        }
        if (obj.getValor() < 0.0) {
            throw new ConsistirException("O campo VALOR (Lançamento) deve ser maior que R$ 0,00");
        }
        if (obj.getDataVencimento() == null) {
            throw new ConsistirException("O campo DATA DE VENCIMENTO (Lançamento) deve ser informado.");
        }
        if (obj.getDataCompetencia() == null) {
            throw new ConsistirException("O campo DATA DE COMPETÊNCIA (Lançamento) deve ser informado.");
        }
        if (obj.getDataLancamento() == null) {
            throw new ConsistirException("O campo DATA DE LANÇAMENTO deve ser informado.");
        }
        if (obj.getLiquido() && (obj.getValor() < obj.getValorLiquido())) {
            throw new ConsistirException("O VALOR LÍQUIDO (Lançamento) não pode ser maior do que o VALOR do lançamento.");
        }
        //OBS: Adicionado aqui para tentar englobar o máximo de caso possível, mas se gerar algum impacto em local especifico, mudar essa validação para os respectivos Controle de cada tela que
        // precisar ajustar. Ex: Contas a Pagar, Conta a Receber, Resumo de Contas
        if (objAntesAlteracao != null && obj.getDataQuitacao() != null && objAntesAlteracao.getContaVO() != null && objAntesAlteracao.getContaVO().getCodigo() != obj.getContaVO().getCodigo()) {
            throw new ConsistirException("Conta a Pagar/Receber Quitada não pode ter a Conta alterada. Estornar o Pagamento para poder realizar essa operação.");
        }
        //OBS: Adicionado aqui para tentar englobar o máximo de caso possível, mas se gerar algum impacto em local especifico, mudar essa validação para os respectivos Controle de cada tela que
        // precisar ajustar. Ex: Contas a Pagar, Conta a Receber, Resumo de Contas
        if (
                objAntesAlteracao != null
                && obj.getDataQuitacao() != null
                && !UteisValidacao.emptyNumber(obj.getValor())
                && !UteisValidacao.emptyNumber(objAntesAlteracao.getValor())
                && !objAntesAlteracao.getValor().equals(obj.getValor())
        ) {
            throw new ConsistirException("Conta a Pagar/Receber Quitada não pode ter o Valor alterado. Estornar o Pagamento para poder realizar essa operação.");
        }
    }

    public  static void validarDadosTipoFormaPagamento(MovContaVO obj) throws ConsistirException {
        if(obj.getTipoForma() == null) {
            throw new ConsistirException("É necessário selecionar a FORMA DE PAGAMENTO.");
        }
    }
    public static void validarDadosOperacao(MovContaVO obj) throws ConsistirException {
    	validarDados(obj);
    	if (UteisValidacao.emptyNumber(obj.getContaVO().getCodigo())) {
            throw new ConsistirException("O campo CONTA (Lançamento) deve ser informado.");
        }
    }	
    public void validarDadosQuitacao(CaixaVO caixaVo, boolean validarFormaPagamento, boolean usarMovimentacao, boolean quintandoOnline)throws Exception{
        if(quintandoOnline){
            return;
        }
    	if(!usarMovimentacao){
    		return;
    	}
        if ((caixaVo == null) || (caixaVo.getCodigo() == 0))
            throw new Exception("O usuário não possui Caixa aberto.\\r\\nÉ necessário abrir o Caixa antes de realizar a operação.");
        if ((this.contaVO == null) || (this.contaVO.getCodigo() == null) || (this.contaVO.getCodigo() == 0))
            throw new Exception("O campo (Conta) deve ser informado.");
        // Verifica se a conta que o usuário escolheu está vinculada ao caixa do usuário.
        boolean contaVinculadaCaixa = false;
        for (CaixaContaVO obj: caixaVo.getListaCaixaConta()){
            if (obj.getContaVo().getCodigo().equals(this.getContaVO().getCodigo())){
               contaVinculadaCaixa = true;
               break;
            }
        }
        if (!contaVinculadaCaixa){
            throw new Exception("Este Caixa não pode operar a conta informada.");
        }
       
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the pessoaVO
     */
    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    /**
     * @param pessoaVO the pessoaVO to set
     */
    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    /**
     * @return the usuarioVO
     */
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    /**
     * @param usuarioVO the usuarioVO to set
     */
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    /**
     * @return the empresaVO
     */
    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    /**
     * @param empresaVO the empresaVO to set
     */
    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * @param descricao the descricao to set
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    /**
     * @return the observacoes
     */
    public String getObservacoes() {
        if (UteisValidacao.emptyString(observacoes)) {
            return "";
        }
        return observacoes;
    }
    
    public String getObservacoes_Apresentar(){
    	String obs = "";
    	obs = observacoes.replaceAll("<p>", "");
    	obs = obs.replaceAll("</p>", "");
    	return obs;
    }

    public String getObservacoes_title() {
        if (UteisValidacao.emptyString(getObservacoes())) {
            return "";
        }
        return this.getObservacoes().replaceAll("\n", "<br/>");
    }

    /**
     * @param observacoes the observacoes to set
     */
    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }

    /**
     * @return the dataLancamento
     */
    public Date getDataLancamento() {
        return dataLancamento;
    }

    /**
     * @param dataLancamento the dataLancamento to set
     */
    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    /**
     * @return the valor
     */
    public Double getValor() {
        if(valor == null){
            valor = 0.00D;
        }
        return valor;
    }

    public Double getValorDeduzirMultaEJuro() throws Exception{
        double valorRet = this.valor;
        if(UtilReflection.objetoMaiorQueZero(this, "getMovContaContabilVO().getCodigo()")){
            if ((this.movContaContabilVO.getValorJuro() != null) && (this.movContaContabilVO.getValorJuro()> 0)){
                valorRet = valorRet - this.movContaContabilVO.getValorJuro();
            }
            if ((this.movContaContabilVO.getValorMulta() != null) && (this.movContaContabilVO.getValorMulta()> 0)){
                valorRet = valorRet - this.movContaContabilVO.getValorMulta();
            }
        }
        return valorRet;
    }

    /**
     * @param valor the valor to set
     */
    public void setValor(Double valor) {
        this.valor = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valor);
    }

    /**
     * @return the dataQuitacao
     */
    public Date getDataQuitacao() {
        return dataQuitacao;
    }
    
    /**
     * @param dataQuitacao the dataQuitacao to set
     */
    public void setDataQuitacao(Date dataQuitacao) {
        this.dataQuitacao = dataQuitacao;
    }

    public String getDataQuitacao_Apresentar() {
    	if(dataQuitacao == null){
    		return "";
    	}
        return Uteis.getDataComHora(getDataQuitacao());
    }
    
    public String getDataQuitacao_ApresentarSemHora() {
    	if(dataQuitacao == null){
    		return "";
    	}
        return Uteis.getData(getDataQuitacao());
    }
    
    public void setDataQuitacao_Apresentar(String data) throws Exception {
    	try{
    		this.setDataQuitacao(Uteis.getDateTime(Uteis.getDate(data.substring(0, 10)), Integer.parseInt(data.substring(13, 15)),
					Integer.parseInt(data.substring(16, 18)), Integer.parseInt(data.substring(19, 21))));
    	}catch (Exception e) {
    		this.setDataQuitacao(null);
		}
    }

    public boolean getContaQuitada() {
        return dataQuitacao != null;
    }

    public Boolean getPagar_Apresentar() {
        return dataQuitacao == null;
    }
    
    public String getDataLancamento_Apresentar() {
    	if(dataLancamento == null){
    		return "";
    	}
        return Uteis.getDataComHora(getDataLancamento());
    }
    
    /**
	 * @return the datavencimento
	 */
	public String getDataVencimento_Apresentar() {
		if(dataVencimento == null){
    		return "";
    	}
		return Uteis.getData(getDataVencimento());
	}

    public String getDataUltimaAlteracao_Apresentar() {
        if(dataUltimaAlteracao == null){
            return "";
        }
        return Uteis.getDataComHora(getDataUltimaAlteracao());
    }

	
    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValor());
//        return Formatador.formatarValorMonetario(valor);
    }

    public String getValor_ApresentarOriginalAlterado(){
        return Formatador.formatarValorMonetarioSemMoeda(getValorOriginalAlterado());
    }

    /**
     * @return the tipoOperacao
     */
    public TipoOperacaoLancamento getTipoOperacaoLancamento() {
        return tipoOperacaoLancamento;
    }

    /**
     * @param tipoOperacao the tipoOperacao to set
     */
    public void setTipoOperacaoLancamento(TipoOperacaoLancamento tipoOperacao) {
        this.tipoOperacaoLancamento = tipoOperacao;
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico.
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc.
     */
    public String getTipoOperacaoLancamento_Apresentar() {
        if (tipoOperacaoLancamento != null) {
            if (tipoOperacaoLancamento.getCodigo() != 0) {
                return tipoOperacaoLancamento.getDescricao();
            }
        }
        return "";
    }

    public String getTipoOperacaoLancamento_ApresentarResumido() {
        if (tipoOperacaoLancamento != null) {
            if (tipoOperacaoLancamento.getCodigo() != 0) {
                if (tipoOperacaoLancamento.getDescricao().equals("Pagamento")) {
                    setMudarCorLinkValor("red");
                    return tipoOperacaoLancamento.getDescricaoCurta();
                } else if (tipoOperacaoLancamento.getDescricao().equals("Recebimento")) {
                    setMudarCorLinkValor("green");
                    return tipoOperacaoLancamento.getDescricaoCurta();
                }
                return tipoOperacaoLancamento.getDescricaoCurta();
            }
        }
        return "";
    }

    /**
     * @return the contaVO
     */
    public ContaVO getContaVO() {
    	if(contaVO == null){
    		contaVO = new ContaVO();
    	}
        return contaVO;
    }

    /**
     * @param contaVO the contaVO to set
     */
    public void setContaVO(ContaVO contaVO) {
        this.contaVO = contaVO;
    }

    /**
     * @return the movContaRateios
     */
    public List<MovContaRateioVO> getMovContaRateios() {
        if (movContaRateios == null) {
            movContaRateios = new ArrayList<>();
        }
        return movContaRateios;
    }

    /**
     * @param movContaRateios the movContaRateios to set
     */
    public void setMovContaRateios(List<MovContaRateioVO> movContaRateios) {
        this.movContaRateios = movContaRateios;
    }

    public String getMudarCorLinkValor() {
    	if (tipoOperacaoLancamento != null) {
            if (tipoOperacaoLancamento.getCodigo() != 0) {
                if (tipoOperacaoLancamento.equals(TipoOperacaoLancamento.PAGAMENTO)) {
                    setMudarCorLinkValor("red");
                } else if (tipoOperacaoLancamento.equals(TipoOperacaoLancamento.RECEBIMENTO)) {
                    setMudarCorLinkValor("green");
                }
            }
        }
        return mudarCorLinkValor;
    }

    public void setMudarCorLinkValor(String mudarCorLinkValor) {
        this.mudarCorLinkValor = mudarCorLinkValor;
    }

    public int getAgendamentoFinanceiro() {
        return agendamentoFinanceiro;
    }

    public void setAgendamentoFinanceiro(int agendamentoFinanceiro) {
        this.agendamentoFinanceiro = agendamentoFinanceiro;
    }

    public int getNrParcela() {
        return nrParcela;
    }

    public void setNrParcela(int nrParcela) {
        this.nrParcela = nrParcela;
    }

	/**
	 * @param styleLinhaSelecionada the styleLinhaSelecionada to set
	 */
	public void setStyleLinhaSelecionada(String styleLinhaSelecionada) {
		this.styleLinhaSelecionada = styleLinhaSelecionada;
	}

	/**
	 * @return the styleLinhaSelecionada
	 */
	public String getStyleLinhaSelecionada() {
		if(styleLinhaSelecionada == null){
			styleLinhaSelecionada = "";
		}
		return styleLinhaSelecionada;
	}
	
	public String getTituloLinkQuitar(){
	    if (this.getTipoOperacaoLancamento() == null){
	        return "";
        }
		if(this.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.PAGAMENTO)){
			return "Pagar";
		}
		if(this.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.RECEBIMENTO)){
			return "Receber";	
		}
		return "";
	}

    /**
     * @return the tipoRecebivel
     */
    public Boolean getTipoRecebivel() {
        return tipoRecebivel;
    }

    /**
     * @param tipoRecebivel the tipoRecebivel to set
     */
    public void setTipoRecebivel(Boolean tipoRecebivel) {
        this.tipoRecebivel = tipoRecebivel;
    }

    /**
     * @return the resumoFormaPagamentoTO
     */
    public ResumoFormaPagamentoTO getResumoFormaPagamentoTO() {
        return resumoFormaPagamentoTO;
    }

    /**
     * @param resumoFormaPagamentoTO the resumoFormaPagamentoTO to set
     */
    public void setResumoFormaPagamentoTO(ResumoFormaPagamentoTO resumoFormaPagamentoTO) {
        this.resumoFormaPagamentoTO = resumoFormaPagamentoTO;
    }

	public void setCheques(List<ChequeVO> cheques) {
		this.cheques = cheques;
	}

	public List<ChequeVO> getCheques() {
		return cheques;
	}

	public void setLote(LoteVO lote) {
		this.lote = lote;
	}

	public LoteVO getLote() {
		return lote;
	}

	public void setCartoes(List<CartaoCreditoVO> cartoes) {
		this.cartoes = cartoes;
	}

	public List<CartaoCreditoVO> getCartoes() {
		return cartoes;
	}

	public void setContaOrigem(Integer contaOrigem) {
		this.contaOrigem = contaOrigem;
	}

	public Integer getContaOrigem() {
		return contaOrigem;
	}

	public void setOperadoraCartaoVO(OperadoraCartaoVO operadoraCartaoVO) {
		this.operadoraCartaoVO = operadoraCartaoVO;
	}

	public OperadoraCartaoVO getOperadoraCartaoVO() {
		return operadoraCartaoVO;
	}

	public void setNrParcelaCartaoCredito(Integer nrParcelaCartaoCredito) {
		this.nrParcelaCartaoCredito = nrParcelaCartaoCredito;
	}

	public Integer getNrParcelaCartaoCredito() {
		return nrParcelaCartaoCredito;
	}

	public void setAutorizacaoCartao(String autorizacaoCartao) {
		this.autorizacaoCartao = autorizacaoCartao;
	}

	public String getAutorizacaoCartao() {
		if(autorizacaoCartao == null){
			autorizacaoCartao = "";
		}
		return autorizacaoCartao;
	}

	public void setTemLote(boolean temLote) {
		this.temLote = temLote;
	}

	public boolean getTemLote() {
		return temLote;
	}

	public void setValorLiquido(Double valorLiquido) {
		this.valorLiquido = valorLiquido;
	}

	public Double getValorLiquido() {
		return valorLiquido;
	}

	public void setLiquido(boolean liquido) {
		this.liquido = liquido;
	}

	public boolean getLiquido() {
		return liquido;
	}
	public String getFavorecido(){
		return getPessoaVO().getNome();
	}
	public String getDescricaoConta(){
		return getContaVO().getDescricao();
	}
	public boolean getBotaoQuitar(){
		return dataQuitacao == null;
	}
	
	public static MovContaVO getMovContaParaLoteAvulso(Integer conta, Double valor){
		MovContaVO movConta = new MovContaVO();
		movConta.setDataCompetencia(Calendario.hoje());
		movConta.setDataLancamento(Calendario.hoje());
		movConta.setDataQuitacao(Calendario.hoje());
		movConta.setDataVencimento(Calendario.hoje());
		movConta.getContaVO().setCodigo(conta);
		movConta.setValor(valor);
		return movConta;
		
	}
	
	
    public void registrarObjetoVOAntesDaAlteracao() {
        try {
        	objetoVOAntesAlteracao = getClone(true);
        } catch (Exception e) {
            objetoVOAntesAlteracao = null;
        }
    }

	public void setApresentarNoCaixa(boolean apresentarNoCaixa) {
		this.apresentarNoCaixa = apresentarNoCaixa;
	}

	public boolean getApresentarNoCaixa() {
		return apresentarNoCaixa;
	}
	
	public Boolean getApresentarRecebido(){
		return tipoOperacaoLancamento != null 
				&& !UteisValidacao.emptyNumber(getCodigo())
				&& (tipoOperacaoLancamento.equals(TipoOperacaoLancamento.CUSTODIA)
						|| tipoOperacaoLancamento.equals(TipoOperacaoLancamento.DEPOSITO)
						|| tipoOperacaoLancamento.equals(TipoOperacaoLancamento.TRANSFERENCIA));
	}
	
	public boolean getContaAPagarNaoQuitada(){
		return (tipoOperacaoLancamento != null &&
                tipoOperacaoLancamento.equals(TipoOperacaoLancamento.PAGAMENTO)) &&
                getDataQuitacao() == null;
	}

    public boolean isContaAPagar(){
        return tipoOperacaoLancamento != null &&
                tipoOperacaoLancamento.equals(TipoOperacaoLancamento.PAGAMENTO);
    }

    public boolean isContaAReceber(){
        return tipoOperacaoLancamento != null &&
                tipoOperacaoLancamento.equals(TipoOperacaoLancamento.RECEBIMENTO);
    }

	public void setCaixa(Integer caixa) {
		this.caixa = caixa;
	}

	public Integer getCaixa() {
		return caixa;
	}

	public void setChequesRetirados(List<ChequeTO> chequesRetirados) {
		this.chequesRetirados = chequesRetirados;
	}

	public List<ChequeTO> getChequesRetirados() {
		return chequesRetirados;
	}

	public void setCartoesRetirados(List<CartaoCreditoTO> cartoesRetirados) {
		this.cartoesRetirados = cartoesRetirados;
	}

	public List<CartaoCreditoTO> getCartoesRetirados() {
		return cartoesRetirados;
	}

	public void setLancamentoSelecionado(boolean lancamentoSelecionado) {
		this.lancamentoSelecionado = lancamentoSelecionado;
	}

	public boolean getLancamentoSelecionado() {
		return lancamentoSelecionado;
	}

	public void setContasAPagarConjunto(List<MovContaVO> contasAPagarConjunto) {
		this.contasAPagarConjunto = contasAPagarConjunto;
	}

	public List<MovContaVO> getContasAPagarConjunto() {
		return contasAPagarConjunto;
	}
	
	public boolean getPagamentoSimples(){
		return UteisValidacao.emptyList(contasAPagarConjunto) || contasAPagarConjunto.size() == 1;
	}

	public void setConjuntoPagamento(String conjuntoPagamento) {
		this.conjuntoPagamento = conjuntoPagamento;
	}

	public String getConjuntoPagamento() {
		return conjuntoPagamento;
	}
	
	public String getCssLinha(){
		return lancamentoSelecionado ? "linhaSelecionada" : "";
	}

    public Integer getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(Integer movProduto) {
        this.movProduto = movProduto;
    }

     public RecebivelTO getRecebivelTO() throws Exception {
        RecebivelTO recebivelTO = new RecebivelTO();
        recebivelTO.setCodigo(this.getCodigo());
         recebivelTO.setCodigoUnico("MC-"+this.getCodigo());
        //recebivelTO.setRecibo(this.getRecibo());
        recebivelTO.setNumeroLote(this.getLote().getCodigo() == 0 ? null : this.getLote().getCodigo());
        recebivelTO.setNomePagador(this.getPessoaVO().getNome());
        if(!UteisValidacao.emptyString(this.getPessoaVO().getCfp())) {
            recebivelTO.setCpfPagador(this.getPessoaVO().getCfp());
        }
        recebivelTO.setDataLancamento(this.getDataLancamento());
        recebivelTO.setValor(Uteis.arredondarForcando2CasasDecimais(this.getValor()));
        //cartaoEscolhido?
        recebivelTO.setCodigoContaContido(this.getContaVO().getCodigo() == 0 ? null : this.getContaVO().getCodigo() );
        recebivelTO.setContaContido(this.getContaVO().getDescricao());
        //removido?
        ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(this.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        recebivelTO.setMatricula(cliente.getMatricula());
        recebivelTO.setCodigoPessoa(this.getPessoaVO().getCodigo());
        recebivelTO.setMovConta(this.getCodigo());

         recebivelTO.setEmpresa(this.getEmpresa());
         recebivelTO.setVencimentosParcelas(this.getVencimentosParcelas());
         recebivelTO.setNumerosParcelas(this.getNumerosParcelas());
         recebivelTO.setCodigosParcelas(this.getCodigosParcelas());
         recebivelTO.setUsuarioVO(this.getUsuarioVO());
        return recebivelTO;
    }

    public Boolean getNfseEmitida() {
        return nfseEmitida;
    }

    public void setNfseEmitida(Boolean nfseEmitida) {
        this.nfseEmitida = nfseEmitida;
    }
    
    public String getApresentarConta() {
        return getContaVO().getDescricao();
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }


    public String getNomePlanoConta() {
        return nomePlanoConta;
    }

    public void setNomePlanoConta(String nomePlanoConta) {
        this.nomePlanoConta = nomePlanoConta;
    }

    public String getNomeCentroCusto() {
        return nomeCentroCusto;
    }

    public void setNomeCentroCusto(String nomeCentroCusto) {
        this.nomeCentroCusto = nomeCentroCusto;
    }

    public MovContaContabilVO getMovContaContabilVO() {
        return movContaContabilVO;
    }

    public void setMovContaContabilVO(MovContaContabilVO movContaContabilVO) {
        this.movContaContabilVO = movContaContabilVO;
    }

    public List<AnexoMovContaVO> getAnexos() {
        return anexos;
}

    public void setAnexos(List<AnexoMovContaVO> anexos) {
        this.anexos = anexos;
    }

    public Boolean getApp() {
        return app;
    }

    public void setApp(Boolean app) {
        this.app = app;
    }

    public boolean isIncluirMovContaContabil() {
        return incluirMovContaContabil;
    }

    public void setIncluirMovContaContabil(boolean incluirMovContaContabil) {
        this.incluirMovContaContabil = incluirMovContaContabil;
    }

    public LoteVO getLotePagouConta() {
        return lotePagouConta;
}

    public void setLotePagouConta(LoteVO lotePagouConta) {
        this.lotePagouConta = lotePagouConta;
    }

    public boolean isValidarFavorecido() {
        return validarFavorecido;
    }

    public void setValidarFavorecido(boolean validarFavorecido) {
        this.validarFavorecido = validarFavorecido;
    }
    public Date getDataUltimaAlteracao() {
        return dataUltimaAlteracao;
    }

    public void setDataUltimaAlteracao(Date dataUltimaAlteracao) {
        this.dataUltimaAlteracao = dataUltimaAlteracao;
    }

    public boolean isExcluido() {
        return excluido;
    }

    public void setExcluido(boolean excluido) {
        this.excluido = excluido;
    }


    private static void montarLancamentoApartirDoLog(MovContaVO movContaVO, LogVO logVO)throws Exception{
        String msg = logVO.getValorCampoAlterado();
        int indexFimFav = msg.indexOf("-");
        String favorecido = msg.substring(msg.indexOf("Favorecido:") + 12, indexFimFav);
        movContaVO.setPessoaVO(new PessoaVO());
        movContaVO.getPessoaVO().setNome(favorecido.trim());

        int indexIniDescricao = msg.indexOf("Descrição:");
        int indexIniConta = msg.indexOf("Conta:");
        int indexIniTipoOperacao = msg.indexOf("Tipo de operação:");
        int indexIniValor = msg.indexOf("Valor:");

        // descricao
        String descricao = msg.substring(indexIniDescricao + 11, indexIniConta-2);
        movContaVO.setDescricao(descricao.trim());
        // conta
        String contaDestino = msg.substring(indexIniConta + 7, indexIniTipoOperacao-2);
        movContaVO.setContaVO(new ContaVO());
        movContaVO.getContaVO().setDescricao(contaDestino.trim());
        // tipo operacao
        String tipoOperacao =  msg.substring(indexIniTipoOperacao + 18, indexIniValor-2);
        TipoOperacaoLancamento tipoOperacaoLancamento = TipoOperacaoLancamento.getTipoOperacaoLancamento(tipoOperacao.trim());
        movContaVO.setTipoOperacaoLancamento(tipoOperacaoLancamento);

        int indexFimValor = msg.indexOf("-", indexIniValor+1);
        String valor =  msg.substring(indexIniValor + 7, indexFimValor);
        movContaVO.setValor(Double.parseDouble(valor.trim()));
    }


    public static MovContaVO montarObjetoApartirDoLog(LogVO logVO){
        MovContaVO movContaVO = new MovContaVO();
        String msg = logVO.getValorCampoAlterado();
        movContaVO.setDataUltimaAlteracao(logVO.getDataAlteracao());
	    try{
            montarLancamentoApartirDoLog(movContaVO, logVO);
        }catch (Exception e){
	        movContaVO.setDescricao(msg);
	        e.printStackTrace();
        }
        return movContaVO;
    }
    public LogVO getLogExclusao() {
        return logExclusao;
    }

    public void setLogExclusao(LogVO logExclusao) {
        this.logExclusao = logExclusao;
    }
    
    public boolean validarDataBloqueioParaDtLancamento(){
        if ((this.codigo == null) || (this.codigo <= 0)){
            return true;
        }
        MovContaVO movContaAnterior = (MovContaVO) getObjetoVOAntesAlteracao();
        if((movContaAnterior == null) || (movContaAnterior.getDataLancamento() == null)){
            return true;
        }
        return (!Calendario.getDataComHoraZerada(getDataLancamento()).equals(Calendario.getDataComHoraZerada(movContaAnterior.getDataLancamento())));
    }

    public boolean validarDataBloqueioParaDtCompetencia(){
        if ((this.codigo == null) || (this.codigo <= 0)){
            return true;
        }
        MovContaVO movContaAnterior = (MovContaVO) getObjetoVOAntesAlteracao();
        if((movContaAnterior == null) || (movContaAnterior.getDataCompetencia() == null)){
            return true;
        }
        return  (!Calendario.getDataComHoraZerada(getDataCompetencia()).equals(Calendario.getDataComHoraZerada(movContaAnterior.getDataCompetencia())));

    }

    public boolean validarDataBloqueioParaDtVencimento(){
        if ((this.codigo == null) || (this.codigo <= 0)){
            return true;
        }
        MovContaVO movContaAnterior = (MovContaVO) getObjetoVOAntesAlteracao();
        if((movContaAnterior == null) || (movContaAnterior.getDataVencimento() == null)){
            return true;
        }
        return  (!Calendario.getDataComHoraZerada(getDataVencimento()).equals(Calendario.getDataComHoraZerada(movContaAnterior.getDataVencimento())));
    }

    public boolean validarDataBloqueioParaDtQuitacao(){
        if ((this.codigo == null) || (this.codigo <= 0)){
            return true;
        }
        MovContaVO movContaAnterior = (MovContaVO) getObjetoVOAntesAlteracao();
        if((movContaAnterior == null) || (movContaAnterior.getDataQuitacao() == null)){
            return true;
        }
        if ((getDataQuitacao() == null) && (movContaAnterior.getDataQuitacao() == null)){
            return false;
        }
        if ((getDataQuitacao() == null) && (movContaAnterior.getDataQuitacao() != null)){
            return true;
        }
        if ((getDataQuitacao() != null) && (movContaAnterior.getDataQuitacao() == null)){
            return true;
        }
        return  (!Calendario.getDataComHoraZerada(getDataQuitacao()).equals(Calendario.getDataComHoraZerada(movContaAnterior.getDataQuitacao())));
    }

    public String getStatusPagamentoStoneOpenBanking() {
        try {
            PagamentoStoneVO pagamentoStone = getFacade().getPagamentoStone().buscarPagamentoStoneByMovConta(this.getCodigo(), this.getEmpresaVO().getCodigo());
            RetornoStoneVO retornoStone = getFacade().getRetornoStone().buscarRetornoStoneByEventId(pagamentoStone.getEventIdWebhook(), pagamentoStone.getEmpresa());
            return retornoStone.getStatus();
        }catch (Exception e){
            return "";
        }
    }

    public String getStatusTransferenciaStoneOpenBanking() {
        try {
            TransferenciaStoneVO transferenciaStone = getFacade().getTransferenciaStone().buscarTransferenciaStoneByMovConta(this.getCodigo(), this.getEmpresaVO().getCodigo());
            RetornoStoneVO retornoStone = getFacade().getRetornoStone().buscarRetornoStoneByEventId(transferenciaStone.getEventIdWebhook(), transferenciaStone.getEmpresa());
            return retornoStone.getStatus();
        }catch (Exception e){
            return "";
        }
    }

    public String getStatusRetornoStoneOpenBanking(){
        String ret = "";
        if(!UteisValidacao.emptyString(getStatusPagamentoStoneOpenBanking())){
            if(getStatusPagamentoStoneOpenBanking().equals("CREATED")){
                ret = "Pagamento Aguardando Aprovação";
            }else if(getStatusPagamentoStoneOpenBanking().equals("SCHEDULED")){
                ret = "Pagamento Agendado";
            }else if(getStatusPagamentoStoneOpenBanking().equals("REJECTED")){
                ret = "Pagamento Recusado";
            }else if(getStatusPagamentoStoneOpenBanking().equals("FINISHED")){
                ret = "Pagamento Relizado";
            }else{
                getStatusPagamentoStoneOpenBanking();
            }
        }else if(!UteisValidacao.emptyString(getStatusTransferenciaStoneOpenBanking())){
            if(getStatusTransferenciaStoneOpenBanking().equals("CREATED")){
                ret = "Transferência Aguardando Aprovação";
            }else if(getStatusTransferenciaStoneOpenBanking().equals("SCHEDULED")){
                ret = "Transferência Agendada";
            }else if(getStatusTransferenciaStoneOpenBanking().equals("REJECTED")){
                ret = "Transferência Recusada";
            }else if(getStatusTransferenciaStoneOpenBanking().equals("FINISHED")){
                ret = "Transferência Relizada";
            }else{
                getStatusTransferenciaStoneOpenBanking();
            }
        }
        return this.getContaVO().getDescricao()+(UteisValidacao.emptyString(ret) ? "" : ", - "+ret);
    }

    public String getStatusRetornoStoneOpenBankingCaixa(){
        String ret = "";
        if(!UteisValidacao.emptyString(getStatusPagamentoStoneOpenBanking())){
            if(getStatusPagamentoStoneOpenBanking().equals("CREATED")){
                ret = "Pagamento Aguardando Aprovação";
            }else if(getStatusPagamentoStoneOpenBanking().equals("SCHEDULED")){
                ret = "Pagamento Agendado";
            }else if(getStatusPagamentoStoneOpenBanking().equals("REJECTED")){
                ret = "Pagamento Recusado";
            }else if(getStatusPagamentoStoneOpenBanking().equals("FINISHED")){
                ret = "Pagamento Relizado";
            }else{
                getStatusPagamentoStoneOpenBanking();
            }
        }else if(!UteisValidacao.emptyString(getStatusTransferenciaStoneOpenBanking())){
            if(getStatusTransferenciaStoneOpenBanking().equals("CREATED")){
                ret = "Transferência Aguardando Aprovação";
            }else if(getStatusTransferenciaStoneOpenBanking().equals("SCHEDULED")){
                ret = "Transferência Agendada";
            }else if(getStatusTransferenciaStoneOpenBanking().equals("REJECTED")){
                ret = "Transferência Recusada";
            }else if(getStatusTransferenciaStoneOpenBanking().equals("FINISHED")){
                ret = "Transferência Relizada";
            }else{
                getStatusTransferenciaStoneOpenBanking();
            }
        }
        return UteisValidacao.emptyString(ret) ? " - " : ret;
    }

    public Boolean getNfceEmitida() {
        return nfceEmitida;
    }

    public void setNfceEmitida(Boolean nfceEmitida) {
        this.nfceEmitida = nfceEmitida;
    }

    private Boolean isTipoNaoRecebivel() {
        return !getTipoRecebivel();
    }

    private Boolean isNaoExcluido() {
        return !isExcluido();
    }

    private boolean isOperacaoEstornavel() {
        return tipoOperacaoLancamento != null && tipoOperacaoLancamento.isEstornavel();
    }

    public boolean validarDataBloqueioParaValor(){
        if ((this.codigo == null) || (this.codigo <= 0)){
            return false;
        }
        MovContaVO movContaAnterior = (MovContaVO) getObjetoVOAntesAlteracao();
        if(movContaAnterior == null){
            return false;
        }
        return  (Uteis.arredondarForcando2CasasDecimais(getValor()) != Uteis.arredondarForcando2CasasDecimais(movContaAnterior.getValor()));
    }

    public Boolean getExcluir_Apresentar() {
        return isTipoNaoRecebivel() && isNaoExcluido() && (isOperacaoEstornavel() || isDepositoAVouCD());
    }

    public boolean isDepositoAVouCD() {
        return depositoAVouCD;
    }

    public void setDepositoAVouCD(boolean depositoAVouCD) {
        this.depositoAVouCD = depositoAVouCD;
    }

    public EmpresaVO getEmpresa() {
        if(empresa == null){
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getCodigosParcelas() {
        return codigosParcelas;
    }

    public void setCodigosParcelas(String codigosParcelas) {
        this.codigosParcelas = codigosParcelas;
    }

    public String getVencimentosParcelas() {
        return vencimentosParcelas;
    }

    public void setVencimentosParcelas(String vencimentosParcelas) {
        this.vencimentosParcelas = vencimentosParcelas;
    }

    public String getNumerosParcelas() {
        return numerosParcelas;
    }

    public void setNumerosParcelas(String numerosParcelas) {
        this.numerosParcelas = numerosParcelas;
    }

    public AnexoMovContaVO getAnexo() {
        return anexo;
    }

    public void setAnexo(AnexoMovContaVO anexo) {
        this.anexo = anexo;
    }

    public Integer getCodigoNotaEmitida() {
        return codigoNotaEmitida;
    }

    public void setCodigoNotaEmitida(Integer codigoNotaEmitida) {
        this.codigoNotaEmitida = codigoNotaEmitida;
    }
    public String getCodigoNotaEmitidaMostrar(){
        String codigo;
        if(UteisValidacao.emptyNumber(getCodigoNotaEmitida())){
            return codigo = "";
        }
        return codigo = getCodigoNotaEmitida().toString();
    }

    public String getCodigoBarras() {
        if (UteisValidacao.emptyString(codigoBarras)) {
            return "";
        }
        return codigoBarras;
    }

    public void setCodigoBarras(String codigoBarras) {
        this.codigoBarras = codigoBarras;
    }

    public String getContaDestino(){
        return getContaVO() == null ? "" : getContaVO().getDescricao();
    }

    public String getChaveArquivoConta() {
        return chaveArquivoConta;
    }

    public void setChaveArquivoConta(String chaveArquivoConta) {
        this.chaveArquivoConta = chaveArquivoConta;
    }

    public String getChaveArquivoComprovante() {
        return chaveArquivoComprovante;
    }

    public void setChaveArquivoComprovante(String chaveArquivoComprovante) {
        this.chaveArquivoComprovante = chaveArquivoComprovante;
    }

    public String getExtensaoArquivoComprovanteMovConta() {
        return extensaoArquivoComprovanteMovConta;
    }

    public void setExtensaoArquivoComprovanteMovConta(String extensaoArquivoComprovanteMovConta) {
        this.extensaoArquivoComprovanteMovConta = extensaoArquivoComprovanteMovConta;
    }

    public String getExtensaoArquivoContaMovConta() {
        return extensaoArquivoContaMovConta;
    }

    public void setExtensaoArquivoContaMovConta(String extensaoArquivoContaMovConta) {
        this.extensaoArquivoContaMovConta = extensaoArquivoContaMovConta;
    }

    public ContaVO getContaDestinoOpenBank() {
        return contaDestinoOpenBank;
    }

    public void setContaDestinoOpenBank(ContaVO contaDestinoOpenBank) {
        this.contaDestinoOpenBank = contaDestinoOpenBank;
    }

    public TipoFormaPagto getTipoForma() {
        return tipoForma;
    }

    public void setTipoForma(TipoFormaPagto tipoForma) {
        this.tipoForma = tipoForma;
    }

    public Integer getCompraEstoque() {
        return compraEstoque;
    }

    public void setCompraEstoque(Integer compraEstoque) {
        this.compraEstoque = compraEstoque;
    }

    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public String getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(String tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public boolean isUsarHoraAtual() {
        return usarHoraAtual;
    }

    public void setUsarHoraAtual(boolean usarHoraAtual) {
        this.usarHoraAtual = usarHoraAtual;
    }

    public void setIdImportacao(String idImportacao) {
        this.idImportacao = idImportacao;
    }

    public String getIdImportacao() {
        if (idImportacao == null) {
            idImportacao = "";
        }
        return idImportacao;
    }

    public String getIdImportacaoSalvar() {
        return (this.getDescricao() + "|" +
                this.getValor() + "|" +
                Calendario.getDataAplicandoFormatacao(this.getDataVencimento(), "ddMMyyyy") + "|" +
                Calendario.getDataAplicandoFormatacao(this.getDataLancamento(), "ddMMyyyy") +
                (UteisValidacao.emptyString(this.getIdImportacao()) ? "" : ("|" + this.getIdImportacao()))).toUpperCase();
    }

    public Double getTaxaAntecipacao() {
        if (taxaAntecipacao == null) {
            return 0.0;
        }
        return taxaAntecipacao;
    }

    public String getTaxaAntecipacaoApresentar() {
        return (Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTaxaAntecipacao())).replace(",",".") + "%" ;
    }

    public void setTaxaAntecipacao(Double taxaAntecipacao) {
        this.taxaAntecipacao = taxaAntecipacao;
    }

    public String getIdentificadorOrigem() {
        if (identificadorOrigem == null) {
            identificadorOrigem = "";
        }
        return identificadorOrigem;
    }

    public void setIdentificadorOrigem(String identificadorOrigem) {
        this.identificadorOrigem = identificadorOrigem;
    }

    public Integer getIdentificadorOrigemTipo() {
        if (identificadorOrigemTipo == null) {
            identificadorOrigemTipo = 0;
        }
        return identificadorOrigemTipo;
    }

    public void setIdentificadorOrigemTipo(Integer identificadorOrigemTipo) {
        this.identificadorOrigemTipo = identificadorOrigemTipo;
    }

    /**
     * @return the valorPago
     */
    public Double getValorPago() {
        if(valorPago == null){
            valorPago = 0.00;
        }
        return valorPago;
    }

    public void setValorPago(Double valorPago) {
        this.valorPago = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorPago);
    }

    public boolean isApresentarValorPago() {
        return this.getValor() != null && !this.getValor().equals(this.getValorPago());
    }

    public String getValorPago_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorPago());
    }

    public Double getValorOriginalAlterado() {
        if(valorOriginalAlterado == null){
            valorOriginalAlterado = 0.00;
        }
        return valorOriginalAlterado;
    }

    public void setValorOriginalAlterado(Double valorOriginalAlterado) {
        this.valorOriginalAlterado = valorOriginalAlterado;
    }

    public String getDataCompetencia_Apresentar() {
        return dataCompetencia != null ? Uteis.getData(getDataCompetencia()) : "";
    }

    public String getValorApresentar() {
        if (UteisValidacao.emptyNumber(getValor())) {
            return "";
        }
        return Uteis.formataDuasCasasDecimaisEPontoCasasMilenio(getValor());
    }

    public void setValorApresentar(String valorApresentar) {
        this.valorApresentar = valorApresentar;
    }

    public Double getValorOriginalAntesDaConciliacao() {
        return valorOriginalAntesDaConciliacao;
    }

    public void setValorOriginalAntesDaConciliacao(Double valorOriginalAntesDaConciliacao) {
        this.valorOriginalAntesDaConciliacao = valorOriginalAntesDaConciliacao;
    }

    public PlanoContaTO getPlanoContaValorSuperior() {
        if (planoContaValorSuperior == null) {
            planoContaValorSuperior = new PlanoContaTO();
        }
        return planoContaValorSuperior;
    }

    public void setPlanoContaValorSuperior(PlanoContaTO planoContaValorSuperior) {
        this.planoContaValorSuperior = planoContaValorSuperior;
    }

    public CentroCustoTO getCentroCustoValorSuperior() {
        if (centroCustoValorSuperior == null) {
            centroCustoValorSuperior = new CentroCustoTO();
        }
        return centroCustoValorSuperior;
    }

    public void setCentroCustoValorSuperior(CentroCustoTO centroCustoValorSuperior) {
        this.centroCustoValorSuperior = centroCustoValorSuperior;
    }

    public boolean isDefinirPlanoContaCentroCustoValorSuperior() {
        return definirPlanoContaCentroCustoValorSuperior;
    }

    public void setDefinirPlanoContaCentroCustoValorSuperior(boolean definirPlanoContaCentroCustoValorSuperior) {
        this.definirPlanoContaCentroCustoValorSuperior = definirPlanoContaCentroCustoValorSuperior;
    }

    public String getIdentificadorDados() {
        if (identificadorDados == null) {
            identificadorDados = "";
        }
        return identificadorDados;
    }

    public void setIdentificadorDados(String identificadorDados) {
        this.identificadorDados = identificadorDados;
    }

    public boolean isMovimentandoTipoFormaPix() {
        return movimentandoTipoFormaPix;
    }

    public void setMovimentandoTipoFormaPix(boolean movimentandoTipoFormaPix) {
        this.movimentandoTipoFormaPix = movimentandoTipoFormaPix;
    }

    public boolean isRetiradaAutomaticaRecebivelOrigemCancelamento() {
        return retiradaAutomaticaRecebivelOrigemCancelamento;
    }

    public void setRetiradaAutomaticaRecebivelOrigemCancelamento(boolean retiradaAutomaticaRecebivelOrigemCancelamento) {
        this.retiradaAutomaticaRecebivelOrigemCancelamento = retiradaAutomaticaRecebivelOrigemCancelamento;
    }

    public String getInfoMovimentacaoAutomaticaConciliacao() {
        if (UteisValidacao.emptyString(infoMovimentacaoAutomaticaConciliacao)) {
            return "";
        }
        return infoMovimentacaoAutomaticaConciliacao;
    }

    public void setInfoMovimentacaoAutomaticaConciliacao(String infoMovimentacaoAutomaticaConciliacao) {this.infoMovimentacaoAutomaticaConciliacao = infoMovimentacaoAutomaticaConciliacao;}

    public String getPayloadPix() {
        if (UteisValidacao.emptyString(payloadPix)) {
            return "";
        }
        return payloadPix;
    }

    public void setPayloadPix(String payloadPix) {
        this.payloadPix = payloadPix;
    }

    public TipoContaPagarLoteEnum getTipoContaPagarLoteEnum() {
        return tipoContaPagarLoteEnum;
    }

    public void setTipoContaPagarLoteEnum(TipoContaPagarLoteEnum tipoContaPagarLoteEnum) {
        this.tipoContaPagarLoteEnum = tipoContaPagarLoteEnum;
    }

    //Utilizado no Exportar, não apagar só por estar cinza na IDE
    public String getMovContaContabilCreditoExportar() {
        return this.movContaContabilVO.getContaContabilCreditoValor().getDescricao();
    }

    //Utilizado no Exportar, não apagar só por estar cinza na IDE
    public String getMovContaContabilDebitoExportar() {
        return this.movContaContabilVO.getContaContabilDebitoValor().getDescricao();
    }

    //Utilizado no Exportar, não apagar só por estar cinza na IDE
    public String getMovContaHistoricoContabilExportar() {
        return HistoricoContabilEnum.getPorCodigo(this.movContaContabilVO.getHistoricoContabil()).getDescricao();
    }

    //Utilizado no Exportar, não apagar só por estar cinza na IDE
    public String getMovContaComplementoHistoricoContabilExportar() {
        return this.movContaContabilVO.getComplementoHistoricoContabil();
    }


    public boolean isElegivelParaLoteDePagamento() {
        return elegivelParaLoteDePagamento;
    }

    public void setElegivelParaLoteDePagamento(boolean elegivelParaLoteDePagamento) {
        this.elegivelParaLoteDePagamento = elegivelParaLoteDePagamento;
    }

    public List<MovContaNaoElegivelLotePagamentoEnum> getListaMotivoNaoElegivelLotePagamentoEnum() {
        return listaMotivoNaoElegivelLotePagamentoEnum;
    }

    public void setListaMotivoNaoElegivelLotePagamentoEnum(List<MovContaNaoElegivelLotePagamentoEnum> listaMotivoNaoElegivelLotePagamentoEnum) {
        this.listaMotivoNaoElegivelLotePagamentoEnum = listaMotivoNaoElegivelLotePagamentoEnum;
    }

    public String getMovContaNaoElegivelLotePagamentoApresentar() {
        if (UteisValidacao.emptyList(listaMotivoNaoElegivelLotePagamentoEnum)) {
            return "Erro ao obter o motivo a exibir.";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("Esta conta não será incluída no lote de pagamento: </br></br>");
        int i = 1;
        for (MovContaNaoElegivelLotePagamentoEnum motivo : listaMotivoNaoElegivelLotePagamentoEnum) {
            sb.append("<b>" + i + "<b>. ").append(motivo.getDescricao()).append("</br>");
            i++;
        }
        return sb.toString();
    }

    public boolean isContaDeConsumo() {
        return contaDeConsumo;
    }

    public void setContaDeConsumo(boolean contaDeConsumo) {
        this.contaDeConsumo = contaDeConsumo;
    }

    public boolean isFornecedorPossuiDadosBancarios() {
        return fornecedorPossuiDadosBancarios;
    }

    public void setFornecedorPossuiDadosBancarios(boolean fornecedorPossuiDadosBancarios) {
        this.fornecedorPossuiDadosBancarios = fornecedorPossuiDadosBancarios;
    }

    public ContaBancariaFornecedorVO getContaBancariaFornecedorVO() {
        if (contaBancariaFornecedorVO == null) {
            contaBancariaFornecedorVO = new ContaBancariaFornecedorVO();
        }
        return contaBancariaFornecedorVO;
    }

    public void setContaBancariaFornecedorVO(ContaBancariaFornecedorVO contaBancariaFornecedorVO) {
        this.contaBancariaFornecedorVO = contaBancariaFornecedorVO;
    }

    public boolean isPresaEmLoteDePagamento() {
        return presaEmLoteDePagamento;
    }

    public void setPresaEmLoteDePagamento(boolean presaEmLoteDePagamento) {
        this.presaEmLoteDePagamento = presaEmLoteDePagamento;
    }

    public boolean getTemLoteDePagamento() {
        if (UteisValidacao.emptyNumber(getLoteDePagamento())) {
            return false;
        }
        return true;
    }

    public Integer getLoteDePagamento() {
        return loteDePagamento;
    }

    public void setLoteDePagamento(Integer loteDePagamento) {
        this.loteDePagamento = loteDePagamento;
    }

    public String getMsgPresaLoteDePagamento_Apresentar() {
        if (isPresaEmLoteDePagamento()) {
            if (UteisValidacao.emptyNumber(getLoteDePagamento())) {
                return "Esta conta está em um processo automático de lote de pagamento. </br> É necessário aguardar o retorno do banco!";
            } else {
                return "Esta conta está em um processo automático de pagamento através do Lote: " + getLoteDePagamento() + ". </br> É necessário aguardar o retorno do banco!";
            }
        } else {
            return "";
        }
    }

    public String getCpfOuCnpjBeneficiario() {
        if (UteisValidacao.emptyString(cpfOuCnpjBeneficiario)) {
            return "";
        }
        return cpfOuCnpjBeneficiario;
    }

    public void setCpfOuCnpjBeneficiario(String cpfOuCnpjBeneficiario) {
        this.cpfOuCnpjBeneficiario = cpfOuCnpjBeneficiario;
    }

    public boolean isPagoOrigemWebhook() {
        return pagoOrigemWebhook;
    }

    public void setPagoOrigemWebhook(boolean pagoOrigemWebhook) {
        this.pagoOrigemWebhook = pagoOrigemWebhook;
    }

    public String getIdExterno() {
        if (idExterno == null) {
            idExterno = "";
        }
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }
}
