package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.AcoesRemessasEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovParcelaWS;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.webservice.ParcelasRenegociadasJSON;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.jdbc.financeiro.ParcelaSPCTO;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade MovParcela. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class MovParcelaVO extends SuperVO {

    protected Integer codigo;
    protected Date dataRegistro;
    protected Date dataVencimento;
    protected String descricao;
    protected String situacao;
    protected boolean movimentoCC;
    protected String movPagamentoCC;
    protected Double percentualMulta;
    protected Double percentualJuro;
    protected Double valorParcela;
    protected Double valorBaseCalculo;
    protected Boolean utilizaConvenio;
    protected ConvenioCobrancaVO convenioCobranca;
    protected Boolean imprimirBoletoParcela;
    protected Boolean parcelaEscolhida;
    /** Atributo responsável por manter o objeto relacionado da classe <code>FormaPagamento </code>.*/
    //protected FormaPagamentoVO formaPagamento;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    protected UsuarioVO responsavel;
    protected ContratoVO contrato;
    protected ReciboPagamentoVO reciboPagamento;
    protected VendaAvulsaVO vendaAvulsaVO;
    protected AulaAvulsaDiariaVO aulaAvulsaDiariaVO;
    protected List<MovProdutoParcelaVO> MovProdutoParcelaVOs;
    @NaoControlarLogAlteracao
    private String mudarCorSituacaoEmAberto;
    private ControleTaxaPersonalVO personal;
    private Date dataAlteracaoManual;
    private Integer nrTentativas = 0;
    private Boolean regimeRecorrencia = false;
    private EmpresaVO empresa;
    private PessoaVO pessoa;
    private List<String> tipoProdutos;
    @NaoControlarLogAlteracao
    private Date dataPagamento;
    @NaoControlarLogAlteracao
    private Double valorEditar;
    private RemessaItemVO itemRemessa;
    @NaoControlarLogAlteracao
    private AcoesRemessasEnum acao;
    @NaoControlarLogAlteracao
    private boolean acessoBloqueado;
    private boolean reagendada;
    @NaoControlarLogAlteracao
    private boolean lancamentoColetivo = Boolean.FALSE;
    @NaoControlarLogAlteracao
    private CategoriaVO categoriaAluno;

    private Date dataCobranca;

    ///atributos transient
    @NaoControlarLogAlteracao
    private Integer remessa;
    @NaoControlarLogAlteracao
    private Date dtGeracao;
    @NaoControlarLogAlteracao
    private Date dtEnvio;
    @NaoControlarLogAlteracao
    private Double valorMultaJuros;
    private boolean emRemessaAindaNaoProcessada = false;

    private boolean naoRealizarCobrancaAutomatica = false;
    private String situacaoRetorno; 

    @NaoControlarLogAlteracao
    private MovParcelaDetalhamentoVO movParcelaDetalhamentoVO;

    private Boolean parcelaDCC;
    private Boolean parcelaMultaEJuro;
    private Integer numeroParcelasOperadora;
    

    ///atributos transient
    @NaoControlarLogAlteracao
    private String cupomDesconto;

    @NaoControlarLogAlteracao
    private String prefixo;

    @NaoControlarLogAlteracao
    private Date dataRegistroAnterior;

    @NaoControlarLogAlteracao
    private Double valorMulta;
    private Double valorJuros;

    @NaoControlarLogAlteracao
    private InfoExtratoParcelaTO infoExtrato;
    private Integer planoPersonal;
    private String parcelasRenegociadas;
    private String justificativaParcelasRenegociadas;
    private boolean existeProdutoProRata = false;
    private List<ParcelasRenegociadasJSON> parcelasRenegociadasJSONS ;
    private Date dataCancelamento;
    @NaoControlarLogAlteracao
    private String justificativaCancelamento = "";
    @NaoControlarLogAlteracao
    private AutorizacaoCobrancaVO autorizacaoCobrancaVO;
    private String modalidade;
    private String ultimoCodigoRetorno;
    private String ultimoConvenioCobranca;
    private boolean existeMovProdutoParcela = false;
    @NaoControlarLogAlteracao
    private ClienteVO clienteVO;

    @NaoControlarLogAlteracao
    private int codRemessaQueEstaVinculada = 0;    //preenchida somente em memória

    private boolean incluidaSPC;
    private String situacaoSPC;
    private String jsonSPC;
    private boolean seraCobradaHojeAutomaticamente;

    @NaoControlarLogAlteracao
    private Integer idExterno;
    @NaoControlarLogAlteracao
    private Boolean gerandoBoletoPeloGestaoBoletosOnline;
    private String nomePLano;
    private boolean foiCobradaHojeManualmente;

    /**
     * Construtor padrão da classe <code>MovParcela</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public MovParcelaVO() {
        super();
        inicializarDados();
    }

    public MovParcelaVO(Integer codigo) {
        this.codigo = codigo;
    }


    public Date getDataRegistroAnterior() {
        return dataRegistroAnterior;
    }

    public void setDataRegistroAnterior(Date dataRegistroAnterior) {
        this.dataRegistroAnterior = dataRegistroAnterior;
    }

    public int getContrato_Apresentar() {
        return getContrato().getCodigo();
    }

    public String getContratoPessoa_Apresentar() {
        return getContrato().getPessoa().getNome();
    }

    public String getResponsavel_Apresentar() {
        return getUsuarioVO().getNome();
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public String getPessoa_Apresentar() {
        return getPessoa().getNome();
    }

    public String getPessoa_Apresentar_UpperCase() {
        return getPessoa().getNome().toUpperCase();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>MovParcelaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(MovParcelaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getEmpresa() == null || obj.getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("Empresa deve ser informada."); // campo novo
        }
        if (obj.getDataRegistro() == null) {
            throw new ConsistirException("O campo DATA DE REGISTRO (Movimento da Parcela) deve ser informado.");
        }
        if (obj.getSituacao() == null) {
            throw new ConsistirException("O campo SITUAÇÃO (Movimento da Parcela) deve ser informado.");
        }
        if ((obj.getVendaAvulsaVO() == null || UteisValidacao.emptyNumber(obj.getVendaAvulsaVO().getCodigo()))
                && (obj.getAulaAvulsaDiariaVO() == null || UteisValidacao.emptyNumber(obj.getAulaAvulsaDiariaVO().getCodigo()))
                && (obj.getContrato() == null || UteisValidacao.emptyNumber(obj.getContrato().getCodigo()))
                && (obj.getPersonal() == null || UteisValidacao.emptyNumber(obj.getPersonal().getCodigo()))
                && !obj.isMovimentoCC()) {
            throw new ConsistirException("O campo CONTRATO (Movimento da Parcela) deve ser informado.");
        }
        if ((obj.getResponsavel() == null) || (obj.getResponsavel().getCodigo() == 0)) {
            throw new ConsistirException("O campo RESPONSÁVEL  (Movimento da Parcela) deve ser informado.");
        }
        if ((obj.getConvenioCobranca() == null) && (obj.getUtilizaConvenio().equals(true))) {
            throw new ConsistirException("O campo CONVÊNIO DE COBRANÇA (Movimento da Parcela) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setSituacao(getSituacao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        
        setDataRegistro(Calendario.hoje());
        setDataVencimento(Calendario.hoje());
        setDescricao("");
        setSituacao("");
        setPercentualMulta(0.0);
        setPercentualJuro(0.0);
        setValorParcela(0.0);
        setValorBaseCalculo(0.0);
        setUtilizaConvenio(false);
        setImprimirBoletoParcela(false);
        setParcelaEscolhida(false);
        setMudarCorSituacaoEmAberto("blue");
    }

    public UsuarioVO getResponsavel() {
        if (responsavel== null) {
            responsavel = new UsuarioVO();
        }
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>HistoricoContratoVO</code>
     * ao List <code>historicoContratoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>HistoricoContrato</code> - getDataRegistro() - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>HistoricoContratoVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjMovProdutoParcelaVOs(MovProdutoParcelaVO obj) throws Exception {
        MovProdutoParcelaVO.validarDados(obj);
        int index = 0;
        Iterator i = getMovProdutoParcelaVOs().iterator();
        while (i.hasNext()) {
            MovProdutoParcelaVO objExistente = (MovProdutoParcelaVO) i.next();
            if (objExistente.getMovProduto().equals(obj.getMovProduto())) {
                getMovProdutoParcelaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getMovProdutoParcelaVOs().add(obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>MovProdutoVO</code>
     * no List <code>MovProdutoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>MovProduto</code> - getDataRegistro() - como identificador (key) do objeto no List.
     *
     * @param movProduto Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjMovProdutoParcelaVOs(Integer movProduto) throws Exception {
        int index = 0;
        Iterator i = getMovProdutoParcelaVOs().iterator();
        while (i.hasNext()) {
            MovProdutoParcelaVO objExistente = (MovProdutoParcelaVO) i.next();
            if (objExistente.getMovProduto().equals(movProduto)) {
                getMovProdutoParcelaVOs().remove(index);
                return;
            }
            index++;
        }
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>MovProdutoParcelaVO</code>
     * no List <code>MovProdutoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>MovProduto</code> - getDataRegistro() - como identificador (key) do objeto no List.
     *
     * @param movProduto Parâmetro para localizar o objeto do List.
     */
    public MovProdutoParcelaVO consultarObjMovProdutoParcelaVO(Integer movProduto) throws Exception {
        Iterator i = getMovProdutoParcelaVOs().iterator();
        while (i.hasNext()) {
            MovProdutoParcelaVO objExistente = (MovProdutoParcelaVO) i.next();
            if (objExistente.getMovProduto().equals(movProduto)) {
                return objExistente;
            }
        }
        return null;
        //consultarObjSubordinadoOC
    }

    public Boolean getImprimirBoletoParcela() {
        return (imprimirBoletoParcela);
    }

    public Boolean isImprimirBoletoParcela() {
        return (imprimirBoletoParcela);
    }

    public void setImprimirBoletoParcela(Boolean imprimirBoletoParcela) {
        this.imprimirBoletoParcela = imprimirBoletoParcela;
    }

    public ConvenioCobrancaVO getConvenioCobranca() {
        if (convenioCobranca == null) {
            convenioCobranca = new ConvenioCobrancaVO();
        }
        return convenioCobranca;
    }

    public void setConvenioCobranca(ConvenioCobrancaVO convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public Boolean getUtilizaConvenio() {
        return (utilizaConvenio);
    }

    public Boolean isUtilizaConvenio() {
        return (utilizaConvenio);
    }

    public void setUtilizaConvenio(Boolean utilizaConvenio) {
        this.utilizaConvenio = utilizaConvenio;
    }

    public Double getPercentualJuro() {
        return (percentualJuro);
    }

    public void setPercentualJuro(Double percentualJuro) {
        this.percentualJuro = percentualJuro;
    }

    public Double getPercentualMulta() {
        return (percentualMulta);
    }

    public void setPercentualMulta(Double percentualMulta) {
        this.percentualMulta = percentualMulta;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return (situacao);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico.
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc.
     */
    public String getSituacao_Apresentar() {
        if (situacao == null) {
            situacao = "";
        }
        if (situacao.equals("EA")) {
            setMudarCorSituacaoEmAberto("red");
            return "Em Aberto";
        }
        if (situacao.equals("PG")) {
            return "Pago";
        }
        if (situacao.equals("CA")) {
            return "Cancelado";
        }
        if (situacao.equals("RG")) {
            return "Renegociado";
        }
        return situacao;
    }

    public boolean isMovimentoCC() {
        return movimentoCC;
    }

    public void setMovimentoCC(boolean movimentoCC) {
        this.movimentoCC = movimentoCC;
    }

    public boolean isPago() {
        return situacao.equals("PG");
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataVencimento() {
        return (dataVencimento);
    }

    public String getDataPagamento_Apresentar() {
        return (Uteis.getData(dataPagamento));
    }

    public String getDataPagamento_Hint() {
        if (dataPagamento == null) {
            return getSituacao_Apresentar();
        }
        return "Parcela paga na data " + Calendario.getData(dataPagamento, "dd/MM/yyyy");
    }

    public String getDataEnvioApresentar() {
        if (getNrTentativas() == 0) {
            return "Não enviada";
        }
        return (Uteis.getDataAplicandoFormatacao(getDataCobranca(), "dd/MM/yyyy"));
    }


    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataVencimento_Apresentar() {
        if(dataVencimento == null){
            return "";
        }
        return (Uteis.getData(dataVencimento));
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Date getDataRegistro() {
        return (dataRegistro);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataRegistro_Apresentar() {
        return (Uteis.getData(dataRegistro));
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ContratoVO getContrato() {
        if (contrato == null) {
            contrato = new ContratoVO();
        }
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public Double getValorParcela() {
        return valorParcela;
    }

    public String getValorParcela_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorParcela);
    }

    public String getValorParcela_Apresentar_Moeda() {
        return Formatador.formatarValorMonetario(valorParcela);
    }

    public String getValorParcelaNumerico() {
        return Formatador.formatarValorNumerico(this.getValorParcela(), Formatador.FRMT_NUM_PADRAO);
    }

    public String getValorMultaJuros_Apresentar() {
        return Formatador.formatarValorNumerico(this.getValorMultaJuros(), Formatador.FRMT_NUM_PADRAO);
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public List<MovProdutoParcelaVO> getMovProdutoParcelaVOs() {

        if (MovProdutoParcelaVOs == null) {
            MovProdutoParcelaVOs = new ArrayList<MovProdutoParcelaVO>();
        }
        return MovProdutoParcelaVOs;
    }

    public void setMovProdutoParcelaVOs(List<MovProdutoParcelaVO> MovProdutoParcelaVOs) {
        this.MovProdutoParcelaVOs = MovProdutoParcelaVOs;
    }

    public Double getValorBaseCalculo() {
        return valorBaseCalculo;
    }

    public void setValorBaseCalculo(Double valorBaseCalculo) {
        this.valorBaseCalculo = valorBaseCalculo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public String getDescricaoLimitada(){
        String aux = null;
        if(descricao != null){
            aux = descricao.length() > 18 ? descricao.substring(0, 18) + "..." : descricao;
        }
        return aux;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Boolean getParcelaEscolhida() {
        return parcelaEscolhida;
    }

    public void setParcelaEscolhida(Boolean parcelaEscolhida) {
        this.parcelaEscolhida = parcelaEscolhida;
    }

    public ReciboPagamentoVO getReciboPagamento() {
        if (reciboPagamento == null) {
            reciboPagamento = new ReciboPagamentoVO();
        }
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamentoVO reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public VendaAvulsaVO getVendaAvulsaVO() {
        if (vendaAvulsaVO == null) {
            vendaAvulsaVO = new VendaAvulsaVO();
        }
        return vendaAvulsaVO;
    }

    public void setVendaAvulsaVO(VendaAvulsaVO vendaAvulsaVO) {
        this.vendaAvulsaVO = vendaAvulsaVO;
    }

    public AulaAvulsaDiariaVO getAulaAvulsaDiariaVO() {
        if (aulaAvulsaDiariaVO == null) {
            aulaAvulsaDiariaVO = new AulaAvulsaDiariaVO();
        }
        return aulaAvulsaDiariaVO;
    }

    public void setAulaAvulsaDiariaVO(AulaAvulsaDiariaVO aulaAvulsaDiariaVO) {
        this.aulaAvulsaDiariaVO = aulaAvulsaDiariaVO;
    }

    /**
     * @return the mudarCorSituacaoEmAberto
     */
    public String getMudarCorSituacaoEmAberto() {
        return mudarCorSituacaoEmAberto;
    }

    /**
     * @param mudarCorSituacaoEmAberto the mudarCorSituacaoEmAberto to set
     */
    public void setMudarCorSituacaoEmAberto(String mudarCorSituacaoEmAberto) {
        this.mudarCorSituacaoEmAberto = mudarCorSituacaoEmAberto;
    }

    /**
     * Responsável por formatar uma string como valor da parcela
     *
     * @param valor
     * <AUTHOR>
     * 17/03/2011
     */
    public void setValorParcelaFormatado(final String valor) {
        this.valorParcela = Formatador.obterValorNumerico(valor);
    }

    /**
     * @return O campo valor parcela.
     */
    public String getValorParcelaFormatado() {
        return Formatador.formatarValorNumerico(this.valorParcela);
    }

    public ControleTaxaPersonalVO getPersonal() {
        if (personal == null) {
            personal = new ControleTaxaPersonalVO(0);
        }
        return personal;
    }

    public void setPersonal(ControleTaxaPersonalVO personal) {
        this.personal = personal;
    }

    public boolean equals(Object obj) {
        if (obj instanceof MovParcelaVO) {
            MovParcelaVO aux = (MovParcelaVO) obj;
            return this.getCodigo().intValue() == aux.getCodigo().intValue();
        }
        return false;
    }

    public Date getDataAlteracaoManual() {
        return dataAlteracaoManual;
    }

    public void setDataAlteracaoManual(Date dataAlteracaoManual) {
        this.dataAlteracaoManual = dataAlteracaoManual;
    }

    public String getDataAlteracaoManual_Apresentar() {
        return Uteis.getData(dataAlteracaoManual);
    }

    public Integer getNrTentativas() {
        return nrTentativas;
    }

    public void setNrTentativas(Integer nrTentativas) {
        this.nrTentativas = nrTentativas;
    }

    public Boolean getRegimeRecorrencia() {
        return regimeRecorrencia;
    }

    public void setRegimeRecorrencia(Boolean regimeRecorrencia) {
        this.regimeRecorrencia = regimeRecorrencia;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public PessoaVO getPessoa() {
        if (pessoa == null) {
            pessoa = new PessoaVO();
        }
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public List<String> getTipoProdutos() {
        if(tipoProdutos == null){
            tipoProdutos = new ArrayList<String>();
        }
        return tipoProdutos;
    }

    public void setTipoProdutos(List<String> tipoProdutos) {
        this.tipoProdutos = tipoProdutos;
    }

    public String toString() {
        return "Parcela: " + this.getCodigo();
    }

    public String getCorRemessa() {

        if (nrTentativas > 0) {
            if ( acao!=null && acao.getReenvio() && !Calendario.igual(itemRemessa.getRemessa().getDataRegistro(),getDataCobranca())) {
                return "color:#696052;";
            } else {
                return "color:red;";
            }
        } else {
            return "color:blue;";
        }

    }

    public String getMovPagamentoCC() {
        return movPagamentoCC;
    }

    public void setMovPagamentoCC(String movPagamentoCC) {
        this.movPagamentoCC = movPagamentoCC;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setValorEditar(Double valorEditar) {
        this.valorEditar = valorEditar;
    }

    public LogVO gerarlogCancelamentoParcela(UsuarioVO responsavel) {
        String pessoaPagador = "NULL";
        String contrato = "NULL";
        LogVO obj = new LogVO();
        obj.setChavePrimaria(getCodigo().toString());
        obj.setNomeEntidade("MOVPARCELA");
        obj.setNomeEntidadeDescricao("parcela");
        obj.setOperacao("CANCELAMENTO - PARCELA");
        obj.setResponsavelAlteracao(responsavel.getNome());
        obj.setUserOAMD(responsavel.getUserOamd());
        obj.setNomeCampo("TODOS");
        if (getPessoa() != null && getPessoa().getCodigo() != 0) {
            pessoaPagador = getPessoa().getCodigo().toString();
            obj.setPessoa(getPessoa().getCodigo());
        } else {
            obj.setPessoa(0);
        }
        if (getContrato() != null && getContrato().getCodigo() != 0) {
            contrato = getContrato().getCodigo().toString();
        }

        obj.setValorCampoAlterado("--------------------------------------\n\r");
        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + " \n\rcódigo da Parcela = " + getCodigo() + "\n\r" + "data vencimento = " + getDataVencimento_Apresentar() + "\n\r" + "valor Total = R$ " + Uteis.getDoubleFormatado(getValorParcela()) + "\n\r" + "nome do Cliente = " + getPessoa().getNome() + "\n\r" + "codigo pessoa do cliente = "
                + pessoaPagador + "\n\r" + "responsável lançamento = " + responsavel.getNome() + "\n\r" + "contrato= " + contrato + "\n\r " + "Situacao= Cancelado\n\r ");
        obj.setValorCampoAnterior("Situacao= " + getSituacao_Apresentar() + "\n\r ");
        obj.setDataAlteracao(Calendario.hoje());
        return obj;
    }

    public Double getValorEditar() {
        return valorEditar;
    }

    public RemessaItemVO getItemRemessa() {
        if (itemRemessa == null) {
            itemRemessa = new RemessaItemVO();
        }
        return itemRemessa;
    }

    public void setItemRemessa(RemessaItemVO itemRemessa) {
        this.itemRemessa = itemRemessa;
    }

    public AcoesRemessasEnum getAcao() {
        return acao;
    }

    public void setAcao(AcoesRemessasEnum acao) {
        this.acao = acao;
    }

    public boolean isAcessoBloqueado() {
        return acessoBloqueado;
    }

    public void setAcessoBloqueado(boolean acessoBloqueado) {
        this.acessoBloqueado = acessoBloqueado;
    }

    public boolean isReagendada() {
        return reagendada;
    }

    public void setReagendada(boolean reagendada) {
        this.reagendada = reagendada;
    }

    public boolean isLancamentoColetivo() {
        return lancamentoColetivo;
    }

    public void setLancamentoColetivo(boolean lancamentoColetivo) {
        this.lancamentoColetivo = lancamentoColetivo;
    }

    public Date getDataCobranca() {
        return dataCobranca;
    }

    public void setDataCobranca(Date dataCobranca) {
        this.dataCobranca = dataCobranca;
    }

    public Integer getCodigoEmpresa() {
        return getEmpresa().getCodigo();
    }

    public Integer getCodigoContrato() {
        return getContrato().getCodigo();
    }

    public Double getValorParcelaArredondar() {
        try {
            return Uteis.arredondarForcando2CasasDecimais(valorParcela);
        } catch (Exception e) {
            return 0.0;
        }
    }

    public CategoriaVO getCategoriaAluno() {
        return categoriaAluno;
    }

    public void setCategoriaAluno(CategoriaVO categoriaAluno) {
        this.categoriaAluno = categoriaAluno;
    }

    public String getCategoriaAlunoApresentar() {
        return getCategoriaAluno() == null || UteisValidacao.emptyString(getCategoriaAluno().getNome()) ? "" : getCategoriaAluno().getNome();
    }


    public Integer getRemessa() {
        return remessa;
    }

    public void setRemessa(Integer remessa) {
        this.remessa = remessa;
    }

    public Date getDtGeracao() {
        return dtGeracao;
    }

    public void setDtGeracao(Date dtGeracao) {
        this.dtGeracao = dtGeracao;
    }

    public Date getDtEnvio() {
        return dtEnvio;
    }

    public void setDtEnvio(Date dtEnvio) {
        this.dtEnvio = dtEnvio;
    }

    public String getDtGeracao_Apresentar() {
        return (Uteis.getData(getDtGeracao()));
    }

    public String getDtEnvio_Apresentar() {
        return (Uteis.getData(getDtEnvio()));
    }

    public Double getValorMultaJuros() {
        if (valorMultaJuros == null){
            valorMultaJuros = 0.0;
        }
        return valorMultaJuros;
    }

    public void setValorMultaJuros(Double valorMultaJuros) {
        this.valorMultaJuros = valorMultaJuros;
    }

    public Boolean getMostrarMultaJuros() {
        return getValorMultaJuros() != 0.0;
    }

    public MovParcelaWS toWS() {
        MovParcelaWS movParcelaWS = new MovParcelaWS();
        movParcelaWS.setCodigo(this.codigo);
        movParcelaWS.setContrato(this.getContrato().getCodigo());
        movParcelaWS.setDescricao(this.getDescricao());
        movParcelaWS.setDataLancamento(this.getDataRegistro_Apresentar());
        movParcelaWS.setDataVencimento(this.getDataVencimento_Apresentar());
        movParcelaWS.setValor(Uteis.arredondarForcando2CasasDecimais(this.getValorParcela()));
        movParcelaWS.setMulta(Uteis.arredondarForcando2CasasDecimais(this.getValorMulta()));
        movParcelaWS.setJuros(Uteis.arredondarForcando2CasasDecimais(this.getValorJuros()));
        movParcelaWS.setSituacao(this.getSituacao_Apresentar());
        movParcelaWS.setPlano(this.getContrato().getPlano().getDescricao());
        try {
            int nr = getDescricao().startsWith("PARCELA") ? Integer.valueOf(getDescricao().replace("PARCELA ","").trim()) : 0;
            movParcelaWS.setNrParcela(nr);
        }catch (Exception e){
            //
        }
        try {
            if(this.getContrato().getPlano().getParcelamentoOperadora()){
                movParcelaWS.setParcelamentooperadora(this.getContrato().getPlano().getMaximoVezesParcelar());
            } else {
                movParcelaWS.setParcelamentooperadora(1);
            }
        }catch (Exception e){
            movParcelaWS.setParcelamentooperadora(1);
        }

        return movParcelaWS;
    }

    public String toJSON(String versao) {
        JSONObject json = new JSONObject();
        if(!UteisValidacao.emptyString(versao) && versao.equals("v2")) {
            json.put("codigoContrato", this.contrato.getCodigo());
            if(!UteisValidacao.emptyString(this.nomePLano)){
                json.put("descricao", this.descricao + " | " + this.nomePLano);
            } else {
                json.put("descricao", this.descricao);
            }
            json.put("codigoParcela", this.codigo);
            json.put("dataVencimento", this.getDataVencimento_Apresentar());
            json.put("valor", Uteis.arredondarForcando2CasasDecimais(this.getValorParcela()));
        }
        return json.toString();
    }


    public boolean isNaoRealizarCobrancaAutomatica() {
        return naoRealizarCobrancaAutomatica;
    }

    public void setNaoRealizarCobrancaAutomatica(boolean naoRealizarCobrancaAutomatica) {
        this.naoRealizarCobrancaAutomatica = naoRealizarCobrancaAutomatica;
    }

    public boolean isEmRemessaAindaNaoProcessada() {
        return emRemessaAindaNaoProcessada;
    }

    public void setEmRemessaAindaNaoProcessada(boolean emRemessaAindaNaoProcessada) {
        this.emRemessaAindaNaoProcessada = emRemessaAindaNaoProcessada;
    }

    public MovParcelaDetalhamentoVO getMovParcelaDetalhamentoVO() {
        return movParcelaDetalhamentoVO;
    }

    public void setMovParcelaDetalhamentoVO(MovParcelaDetalhamentoVO movParcelaDetalhamentoVO) {
        this.movParcelaDetalhamentoVO = movParcelaDetalhamentoVO;
    }

    public String getSituacaoRetorno() {
        return getItemRemessa().getCodigoStatus();
    }

    public void setSituacaoRetorno(String situacaoRetorno) {
        this.situacaoRetorno = situacaoRetorno;
    }

    public Boolean getParcelaDCC() {
        return parcelaDCC;
    }

    public void setParcelaDCC(Boolean parcelaDCC) {
        this.parcelaDCC = parcelaDCC;
    }
    
    public Boolean getParcelaMultaEJuro() {
        return parcelaMultaEJuro;
    }

    public void setParcelaMultaEJuro(Boolean parcelaMultaEJuro) {
        this.parcelaMultaEJuro = parcelaMultaEJuro;
    }

    public boolean isParceladoLojista() {
        return numeroParcelasOperadora != null && numeroParcelasOperadora > 1;
    }

    public Integer getNumeroParcelasOperadora() {
        return numeroParcelasOperadora;
    }

    public void setNumeroParcelasOperadora(Integer numeroParcelasOperadora) {
        this.numeroParcelasOperadora = numeroParcelasOperadora;
    }

    public String getCupomDesconto() {
        if (cupomDesconto == null) {
            cupomDesconto = "";
        }
        return UteisValidacao.emptyString(cupomDesconto) ? cupomDesconto : cupomDesconto.toUpperCase();
    }

    public void setCupomDesconto(String cupomDesconto) {
        this.cupomDesconto = cupomDesconto;
    }

    public Integer getNumeroParcela(){
        try{
            // considerar que o número da parcela são as duas últimas letras da descrição da parcela.
            if (this.descricao != null){
                String nrParcela = descricao.substring(descricao.indexOf("PARCELA") + 7, descricao.length()).trim();
                return Integer.parseInt(nrParcela);
}
        }catch (Exception e){
            // ignored
        }
        return null;
    }

    public String getPrefixo() {
        return prefixo;
    }

    public void setPrefixo(String prefixo) {
        this.prefixo = prefixo;
    }

    public Double getValorMulta() {
        if (valorMulta == null) {
            valorMulta = 0.0;
        }
        return valorMulta;
    }

    public void setValorMulta(Double valorMulta) {
        this.valorMulta = valorMulta;
    }

    public Double getValorJuros() {
        if (valorJuros == null) {
            valorJuros = 0.0;
        }
        return valorJuros;
    }

    public void setValorJuros(Double valorJuros) {
        this.valorJuros = valorJuros;
    }

    /**
     * @todo Atributo responsável pelo valor da cobrança incluindo Multa e Juros
     */
    public Double getValorCobranca() {
        return getValorParcela() + getValorMulta() + getValorJuros();
    }

    public String getValorCobranca_Apresentar() {
        return Formatador.formatarValorNumerico(this.getValorCobranca(), Formatador.FRMT_NUM_PADRAO);
    }

    public InfoExtratoParcelaTO getInfoExtrato() {
        return infoExtrato;
    }

    public void setInfoExtrato(InfoExtratoParcelaTO infoExtrato) {
        this.infoExtrato = infoExtrato;
    }

    public Integer getDiaVencimento() {
        if(getDataVencimento() != null){
            return Calendario.getDiaMes(getDataVencimento());
        }else{
            return null;
        }
    }

    public Integer getPlanoPersonal() {
        if(planoPersonal == null){
            planoPersonal = 0;
        }
        return planoPersonal;
    }

    public void setPlanoPersonal(Integer planoPersonal) {
        this.planoPersonal = planoPersonal;
    }

    public String getParcelasRenegociadas() {
        if (parcelasRenegociadas == null) {
            parcelasRenegociadas = "";
        }
        return parcelasRenegociadas;
    }

    public void setParcelasRenegociadas(String parcelasRenegociadas) {
        this.parcelasRenegociadas = parcelasRenegociadas;
    }

    public String getToolTipsterDescricaoParcela() {
        if (getDescricao().contains("RENEGOCIADA") && !UteisValidacao.emptyString(getParcelasRenegociadas())) {
            try {
                StringBuilder retorno = new StringBuilder();
                retorno.append("<b>Parcela(s) origem: </b><br/><br/>");

                JSONObject jsonObject = new JSONObject(getParcelasRenegociadas());
                JSONArray lista = new JSONArray(jsonObject.get("listaMovParcelaRenegociar").toString());
                for (int e = 0; e < lista.length(); e++) {
                    JSONObject obj = lista.getJSONObject(e);
                    retorno.append("Parcela ").append(obj.getInt("codigo"));
                    retorno.append(" - ").append(obj.getString("descricao"));
                    retorno.append(" - ").append(Formatador.formatarValorMonetario(obj.getDouble("valorParcela"))).append("<br/>");
                }
                return retorno.toString();
            } catch (Exception ex) {
                return "";
            }
        }
        return "";
    }

    public String getToolTipsterParcelasRenegociadas() {
        try {
            StringBuilder retorno = new StringBuilder();

            JSONObject jsonObject = new JSONObject(getParcelasRenegociadas());
            JSONArray lista = new JSONArray(jsonObject.get("listaMovParcelaRenegociar").toString());
            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                retorno.append("Parcela ").append(obj.getInt("codigo"));
                retorno.append(" - ").append(obj.getString("descricao"));
                retorno.append(" - ").append(Formatador.formatarValorMonetario(obj.getDouble("valorParcela"))).append("<br/>");
            }

            return retorno.toString();
        } catch (Exception ex) {
            return getCodigo().toString();
        }
    }

    public boolean isExisteProdutoProRata() {
        return existeProdutoProRata;
    }

    public void setExisteProdutoProRata(boolean existeProdutoProRata) {
        this.existeProdutoProRata = existeProdutoProRata;
    }

    public List<ParcelasRenegociadasJSON> getParcelasRenegociadasJSONS() {
        if (parcelasRenegociadasJSONS == null){
            parcelasRenegociadasJSONS = new ArrayList<ParcelasRenegociadasJSON>();
        }
        return parcelasRenegociadasJSONS;
    }

    public void setParcelasRenegociadasJSONS(List<ParcelasRenegociadasJSON> parcelasRenegociadasJSONS) {
        this.parcelasRenegociadasJSONS = parcelasRenegociadasJSONS;
    }

    public JRDataSource getDsParcelasRenegociadas() {
        JRDataSource jr1 = new JRBeanArrayDataSource(getParcelasRenegociadasJSONS().toArray());
        return jr1;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public String getDataCancelamento_Hint() {
        if (dataCancelamento == null) {
            return getSituacao_Apresentar();
        }
        return "Contrato ou parcela cancelado(a) na data " + Calendario.getData(dataCancelamento, "dd/MM/yyyy");
    }

    public String getJustificativaCancelamento() {
        return justificativaCancelamento;
    }

    public void setJustificativaCancelamento(String justificativaCancelamento) {
        this.justificativaCancelamento = justificativaCancelamento;
    }

    public AutorizacaoCobrancaVO getAutorizacaoCobrancaVO() {
        return autorizacaoCobrancaVO;
    }

    public void setAutorizacaoCobrancaVO(AutorizacaoCobrancaVO autorizacaoCobrancaVO) {
        this.autorizacaoCobrancaVO = autorizacaoCobrancaVO;
    }

    public String getModalidade() {
        return modalidade == null ? "-" : modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getUltimoCodigoRetorno() {
        return ultimoCodigoRetorno;
    }

    public void setUltimoCodigoRetorno(String ultimoCodigoRetorno) {
        this.ultimoCodigoRetorno = ultimoCodigoRetorno;
    }

    public String getUltimoConvenioCobranca() {
        return ultimoConvenioCobranca;
    }

    public void setUltimoConvenioCobranca(String ultimoConvenioCobranca) {
        this.ultimoConvenioCobranca = ultimoConvenioCobranca;
    }

    public String getTipoOperacaoRealizar() {
        return "";
    }

    public boolean isExisteMovProdutoParcela() {
        return existeMovProdutoParcela;
    }

    public void setExisteMovProdutoParcela(boolean existeMovProdutoParcela) {
        this.existeMovProdutoParcela = existeMovProdutoParcela;
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("codigo", codigo);
        json.put("dataRegistro", dataRegistro);
        json.put("dataVencimento", dataVencimento);
        json.put("descricao", descricao);
        json.put("situacao", situacao);
        json.put("valorParcela", valorParcela);


        return json;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public int getCodRemessaQueEstaVinculada() {
        return codRemessaQueEstaVinculada;
    }

    public void setCodRemessaQueEstaVinculada(int codRemessaQueEstaVinculada) {
        this.codRemessaQueEstaVinculada = codRemessaQueEstaVinculada;
    }

    public boolean isIncluidaSPC() {
        return incluidaSPC;
    }

    public void setIncluidaSPC(boolean incluidaSPC) {
        this.incluidaSPC = incluidaSPC;
    }

    public String getSituacaoSPC() {
        return situacaoSPC;
    }

    public void setSituacaoSPC(String situacaoSPC) {
        this.situacaoSPC = situacaoSPC;
    }

    public String getJsonSPC() {
        return jsonSPC;
    }

    public void setJsonSPC(String jsonSPC) {
        this.jsonSPC = jsonSPC;
    }

    public ParcelaSPCTO toParcelaSPCTO() {
        ParcelaSPCTO parcelaSPCTO = new ParcelaSPCTO();
        parcelaSPCTO.setParcela(getCodigo());
        parcelaSPCTO.setCodigoPessoa(getPessoa().getCodigo());
        parcelaSPCTO.setIncluidaSpc(isIncluidaSPC());
        parcelaSPCTO.setSituacaoSpc(getSituacaoSPC());
        parcelaSPCTO.setJsonSpc(getJsonSPC());
        return parcelaSPCTO;
    }

    public boolean isSeraCobradaHojeAutomaticamente() {
        return seraCobradaHojeAutomaticamente;
    }

    public void setSeraCobradaHojeAutomaticamente(boolean seraCobradaHojeAutomaticamente) {
        this.seraCobradaHojeAutomaticamente = seraCobradaHojeAutomaticamente;
    }

    public Integer getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public Boolean getGerandoBoletoPeloGestaoBoletosOnline() {
        if (gerandoBoletoPeloGestaoBoletosOnline == null) {
            gerandoBoletoPeloGestaoBoletosOnline = false;
        }
        return gerandoBoletoPeloGestaoBoletosOnline;
    }

    public void setGerandoBoletoPeloGestaoBoletosOnline(Boolean gerandoBoletoPeloGestaoBoletosOnline) {
        this.gerandoBoletoPeloGestaoBoletosOnline = gerandoBoletoPeloGestaoBoletosOnline;
    }


    public String getJustificativaParcelasRenegociadas() {
        return justificativaParcelasRenegociadas;
    }

    public void setJustificativaParcelasRenegociadas(String justificativaParcelasRenegociadas) {
        this.justificativaParcelasRenegociadas = justificativaParcelasRenegociadas;
    }
    public String getNomePLano() {
        return nomePLano;
    }

    public void setNomePLano(String nomePLano) {
        this.nomePLano = nomePLano;
    }

    public boolean isFoiCobradaHojeManualmente() {
        return foiCobradaHojeManualmente;
    }

    public void setFoiCobradaHojeManualmente(boolean foiCobradaHojeManualmente) {
        this.foiCobradaHojeManualmente = foiCobradaHojeManualmente;
    }

}
