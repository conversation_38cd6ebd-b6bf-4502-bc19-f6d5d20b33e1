package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.MovProduto;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import org.json.JSONObject;
import servicos.vendasonline.dto.ParcelaDTO;

/**
 * Reponsável por manter os dados da entidade AulaAvulsa. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class AulaAvulsaDiariaVO extends SuperVO {
    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected EmpresaVO empresa;
    @NaoControlarLogAlteracao
    protected ClienteVO cliente;
    @ChaveEstrangeira
    protected ProdutoVO produto;
    @ChaveEstrangeira
    protected ModalidadeVO modalidade;
    protected UsuarioVO responsavel;
    protected Date dataRegistro;
    protected Double valor;
    @NaoControlarLogAlteracao
    protected Boolean apresentarEmpresa;
    protected String nomeComprador;
    @NaoControlarLogAlteracao
    private boolean descontoManual = false;
    private Double valorDescontoManual = 0.0;
    @NaoControlarLogAlteracao
    private boolean apresentarDescontoManual = false;
    private Date dataInicio = Calendario.hoje();
    private Date dataLancamento = Calendario.hoje();
    private UsuarioVO usuarioResponsavelAlteracaoDataLancamento;
    protected MovParcelaVO parcela;
    private List<MovProdutoVO> movProdutoVOs;
    protected PlanoTextoPadraoVO textoPadrao;
    private List<MovParcelaVO> movParcelaVOs;

    public AulaAvulsaDiariaVO() {
        super();
        inicializarDados();
    }

    public AulaAvulsaDiariaVO(AulaAvulsaDiariaVO diariaVO) {
        this.codigo = diariaVO.getCodigo();
        this.valor = diariaVO.getValor();
    }

    public static void validarDados(AulaAvulsaDiariaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getEmpresa() == null || obj.getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("O campo EMPRESA deve ser informado.");
        }
        if (obj.getNomeComprador().equals("") || UteisValidacao.emptyNumber(obj.getCliente().getCodigo())) {
            throw new ConsistirException("O campo Cliente (Diária) deve ser informado.");
        }
        if (obj.getProduto() == null || obj.getProduto().getCodigo() == 0) {
            throw new ConsistirException("O campo PRODUTO deve ser informado.");
        }
        if (obj.getModalidade() == null || obj.getModalidade().getCodigo() == 0) {
            throw new ConsistirException("O campo MODALIDADE deve ser informado.");
        }
        if(obj.getDataInicio() == null) {
            throw new ConsistirException("O campo DATA INÍCIO deve ser informado.");
        }
    }

    public void gerarPacela(AulaAvulsaDiariaVO obj, boolean parcelaPaga, Connection con) throws Exception {
        MovParcelaVO movParcelaVO = new MovParcelaVO();
        movParcelaVO.setAulaAvulsaDiariaVO(obj);
        movParcelaVO.setResponsavel(obj.getResponsavel());
        movParcelaVO.setDataRegistro(obj.getDataLancamento());
        movParcelaVO.setDataVencimento(Calendario.hoje());
        if (obj.getProduto().getTipoProduto().equals("DI")) {
            movParcelaVO.setDescricao("Diaria");
        } else {
            movParcelaVO.setDescricao("Aula Avulso");
        }
        movParcelaVO.setEmpresa(empresa);
        movParcelaVO.setPessoa(obj.getCliente().getPessoa());
        if (parcelaPaga) {
            movParcelaVO.setSituacao("PG");
        } else {
            movParcelaVO.setSituacao("EA");
        }
        movParcelaVO.setValorParcela(obj.getValor());
        gerarMovProduto(obj, movParcelaVO, parcelaPaga, con);
        ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
        zillyonWebFacade.incluirMovParcelaSemCommit(movParcelaVO);
        if (parcelaPaga){
            gerarPagamentoParcela(obj, movParcelaVO, con);
        }

        zillyonWebFacade = null;
    }

    private void gerarPagamentoParcela(AulaAvulsaDiariaVO obj, MovParcelaVO movParcelaVO, Connection con){
        try {
            ReciboPagamento reciboDao = new ReciboPagamento(con);
            FormaPagamento formaPagamentoDao = new FormaPagamento(con);
            MovPagamento pagamentoDao = new MovPagamento(con);
            PagamentoMovParcela pagamentoMovparcelaDao = new PagamentoMovParcela(con);

            ReciboPagamentoVO recibo = new ReciboPagamentoVO();
            recibo.setContrato(null);
            recibo.setData(obj.getDataLancamento());
            recibo.setNomePessoaPagador(obj.getNomeComprador());
            recibo.setPessoaPagador(obj.getCliente().getPessoa());
            recibo.setResponsavelLancamento(obj.getResponsavel());
            recibo.setValorTotal(0.0);
            recibo.setEmpresa(obj.getEmpresa());
            reciboDao.incluir(recibo);

            MovPagamentoVO movPagamento = new MovPagamentoVO();
            movPagamento.setPessoa(movParcelaVO.getPessoa());
            movPagamento.setDataLancamento(obj.getDataLancamento());
            movPagamento.setDataPagamento(movParcelaVO.getDataVencimento());
            movPagamento.setDataQuitacao(movParcelaVO.getDataVencimento());
            movPagamento.setResponsavelPagamento(obj.getResponsavel());
            movPagamento.setValor(0.0);
            movPagamento.setValorTotal(0.0);
            movPagamento.setEmpresa(empresa);
            movPagamento.getFormaPagamento().setCodigo(formaPagamentoDao.consultarPrimeiraFormaPagamentoAVista(Uteis.NIVELMONTARDADOS_MINIMOS).getCodigo());
            movPagamento.setNomePagador(obj.getNomeComprador());
            movPagamento.setMovPagamentoEscolhida(true);
            movPagamento.setReciboPagamento(recibo);
            pagamentoDao.incluirSemCommit(movPagamento, false);

            PagamentoMovParcelaVO ppmp = new PagamentoMovParcelaVO();
            ppmp.setMovPagamento(movPagamento.getCodigo());
            ppmp.setMovParcela(movParcelaVO);
            ppmp.setValorPago(0.0);
            ppmp.setReciboPagamento(recibo);
            pagamentoMovparcelaDao.incluir(ppmp);

            reciboDao = null;
            formaPagamentoDao = null;
            pagamentoDao = null;
            pagamentoMovparcelaDao = null;
        } catch (Exception ex){
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Erro ao gerar pagamento Aula Avulsa - " + ex.getMessage());
        }
    }

    private void gerarMovProduto(final AulaAvulsaDiariaVO obj, MovParcelaVO movParcela, boolean parcelaPaga, final Connection con) throws Exception {
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setProduto(obj.getProduto());
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao(obj.getProduto().getDescricao());
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(Calendario.hoje()));
        movProdutoVO.setRenovavelAutomaticamente(obj.getProduto().getRenovavelAutomaticamente());
        movProdutoVO.setQuantidade(1);
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(Calendario.hoje()));
        if (obj.getProduto().getDataInicioVigencia() == null){
            movProdutoVO.setDataInicioVigencia(obj.getDataInicio());
        }else{
            movProdutoVO.setDataInicioVigencia(obj.getProduto().getDataInicioVigencia());
        }
        movProdutoVO.setDataFinalVigencia(obj.getProduto().getDataFinalVigencia());
        movProdutoVO.setDataLancamento(obj.getDataLancamento());
        movProdutoVO.setResponsavelLancamento(obj.getResponsavel());
        movProdutoVO.setPrecoUnitario(obj.getProduto().getValorFinal());
        movProdutoVO.setEmpresa(obj.getEmpresa());
        movProdutoVO.setPessoa(obj.getCliente().getPessoa());
        movProdutoVO.setQuitado(false);
        if (parcelaPaga) {
            movProdutoVO.setSituacao("PG");
        } else {
            movProdutoVO.setSituacao("EA");
        }
        if (isDescontoManual()) {
            movProdutoVO.setValorDesconto(getValorDescontoManual());
        } else {
            movProdutoVO.setValorDesconto(0.0);
        }
        movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais((movProdutoVO.getPrecoUnitario() - movProdutoVO.getValorDesconto())));
        MovProduto movProdutoDao = new MovProduto(con);
        movProdutoDao.incluirSemCommit(movProdutoVO);
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProdutoVO.getCodigo());
        movProdutoParcela.setValorPago(movProdutoVO.getTotalFinal());
        movProdutoParcela.setMovProdutoVO(movProdutoVO);
        movParcela.getMovProdutoParcelaVOs().add(movProdutoParcela);

        movProdutoDao = null;
    }

    public void gerarPeriodoAcesso(AulaAvulsaDiariaVO obj, Connection con) throws Exception {
        PeriodoAcessoClienteVO peridoAcesso = new PeriodoAcessoClienteVO();
        if (obj.getProduto().getTipoProduto().equals("DI")) {
            peridoAcesso.setDataInicioAcesso(obj.getDataInicio());
            peridoAcesso.setDataFinalAcesso(Uteis.obterDataFutura2(obj.getDataInicio(), UteisValidacao.emptyNumber(obj.getProduto().getNrDiasVigencia()) ?
                    0 :
                    (obj.getProduto().getNrDiasVigencia() - 1)));
            peridoAcesso.setTipoAcesso("DI");
        } else {
            peridoAcesso.setDataInicioAcesso(obj.getDataInicio());
            peridoAcesso.setDataFinalAcesso(Uteis.obterDataFutura2(obj.getDataInicio(), UteisValidacao.emptyNumber(obj.getProduto().getNrDiasVigencia()) ?
                    0 :
                    (obj.getProduto().getNrDiasVigencia() - 1)));
            peridoAcesso.setTipoAcesso("AA");
        }
        peridoAcesso.setAulaAvulsaDiaria(obj.getCodigo());
        peridoAcesso.setPessoa(obj.getCliente().getPessoa().getCodigo());
        PeriodoAcessoCliente periodoAcessoClienteDao = new PeriodoAcessoCliente(con);
        periodoAcessoClienteDao.incluirSemCommit(peridoAcesso);
        periodoAcessoClienteDao = null;

    }

    public void inicializarDados() {
        setCliente(new ClienteVO());
        setCodigo(new Integer(0));
        setEmpresa(new EmpresaVO());
        setModalidade(new ModalidadeVO());
        setProduto(new ProdutoVO());
        setResponsavel(new UsuarioVO());
        setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        setUsuarioVO(new UsuarioVO());
        setValor(0.0);
        setApresentarEmpresa(false);
        setNomeComprador("");
        setValorDescontoManual(0.0);
        setDescontoManual(false);

    }

    public void realizarUpperCaseDados() {
        // setTipoComprador(getTipoComprador().toUpperCase());
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataRegistro_Apresentar() {
        return Uteis.getData(dataRegistro);
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public ModalidadeVO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeVO modalidade) {
        this.modalidade = modalidade;
    }

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        if ((getValorDescontoManual() != 0.0) && getValorDescontoManual() <= valor) {
            return valor - getValorDescontoManual();
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Boolean getApresentarEmpresa() {
        return apresentarEmpresa;
    }

    public void setApresentarEmpresa(Boolean apresentarEmpresa) {
        this.apresentarEmpresa = apresentarEmpresa;
    }

    public String getNomeComprador() {
        if (nomeComprador == null) {
            this.nomeComprador = "";
        }
        return nomeComprador;
    }

    public void setNomeComprador(String nomeComprador) {
        this.nomeComprador = nomeComprador;
    }

    public boolean isDescontoManual() {
        return descontoManual;
    }

    public void setDescontoManual(boolean descontoManual) {
        this.descontoManual = descontoManual;
    }

    public Double getValorDescontoManual() {
        return valorDescontoManual;
    }

    public void setValorDescontoManual(Double valorDescontoManual) {
        this.valorDescontoManual = valorDescontoManual;
    }

    public boolean isApresentarDescontoManual() {
        return apresentarDescontoManual;
    }

    public void setApresentarDescontoManual(boolean apresentarDescontoManual) {
        this.apresentarDescontoManual = apresentarDescontoManual;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public UsuarioVO getUsuarioResponsavelAlteracaoDataLancamento() {
        return usuarioResponsavelAlteracaoDataLancamento;
    }

    public void setUsuarioResponsavelAlteracaoDataLancamento(UsuarioVO usuarioResponsavelAlteracaoDataLancamento) {
        this.usuarioResponsavelAlteracaoDataLancamento = usuarioResponsavelAlteracaoDataLancamento;
    }
    public JSONObject toJSON() {
        return new JSONObject(this);
    }

    public MovParcelaVO getParcela() {
        return parcela;
    }

    public void setParcela(MovParcelaVO parcela) {
        this.parcela = parcela;
    }

    public List<MovProdutoVO> getMovProdutoVOs() {
        if (movProdutoVOs == null) {
            movProdutoVOs = new ArrayList<MovProdutoVO>();
        }
        return movProdutoVOs;
    }

    public void setMovProdutoVOs(List<MovProdutoVO> movProdutoVOs) {
        this.movProdutoVOs = movProdutoVOs;
    }

    public PlanoTextoPadraoVO getTextoPadrao() {
        return textoPadrao;
    }

    public void setTextoPadrao(PlanoTextoPadraoVO textoPadrao) {
        this.textoPadrao = textoPadrao;
    }

    public List<MovParcelaVO> getMovParcelaVOs() {
        if (movParcelaVOs == null) {
            movParcelaVOs = new ArrayList<MovParcelaVO>();
        }
        return movParcelaVOs;
    }

    public void setMovParcelaVOs(List<MovParcelaVO> movParcelaVOs) {
        this.movParcelaVOs = movParcelaVOs;
    }
}
