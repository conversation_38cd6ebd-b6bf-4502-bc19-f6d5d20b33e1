package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;

public class ConfiguracaoFinanceiroVO extends SuperVO {

    @ChaveEstrangeira
    private PlanoContaTO planoContasDevolucao;
    @ChaveEstrangeira
    private CentroCustoTO centroCustoDevolucao;
    @ChaveEstrangeira
    private PlanoContaTO planoContasTaxaPix;
    @ChaveEstrangeira
    private CentroCustoTO centroCustoTaxaPix;
    @ChaveEstrangeira
    private PlanoContaTO planoContasTaxa;
    @ChaveEstrangeira
    private PlanoContaTO planoContasTaxaBoleto;
    @ChaveEstrangeira
    private CentroCustoTO centroCustosTaxa;
    @ChaveEstrangeira
    private CentroCustoTO centroCustosTaxaBoleto;
    private boolean usarCentralEventos = false;
    private boolean adicionarDevolucaoRelatorioComissao = true;
    private boolean usarMovimentacaoContas = false;
    private boolean solicitaSenhaLancarConta = true;
    private boolean permitirContaOutraUnidade = false;
    private boolean especificarCompetencia = false;
    private boolean habilitarExportacaoAlterData = false;
    private boolean fecharCaixaAutomaticamente = false;
    private boolean bloquearAlunoChequeDevolvido = false;
    private String mensagembloqueio = "";
    private boolean cnpjObrigatorioFornecedor = false;
    private boolean contaPagarCompraEstoque = false;
    private boolean ordemCompraEstoque = false;
    private boolean contaPagarReceberColabInativo = false;
    private boolean criarContaPagarAutomatico = false;
    @ChaveEstrangeira
    private ContaVO contaCriarContaPagarAutomaticoVO;
    private boolean apresentarValorPago = false;

    private boolean permitirTipoPlanoContaFilho = false;
    private boolean metaFinanceiraPorFaturamento = false;
    @ChaveEstrangeira
    private PlanoContaTO planoContasLancamentoAutomaticoSaida;
    @ChaveEstrangeira
    private PlanoContaTO planoContasLancamentoAutomaticoEntrada;
    @ChaveEstrangeira
    private CentroCustoTO centroCustoLancamentoAutomaticoSaida;
    @ChaveEstrangeira
    private CentroCustoTO centroCustoLancamentoAutomaticoEntrada;
    private boolean informarFavorecidoAoRealizarMovimentacaoRecebiveis = false;




    private String descricaomovimentacaoautomaticaDebito;
    private String descricaomovimentacaoautomaticaCredito;

    @ChaveEstrangeira
    private ContaVO contamovimentacaoautomaticadebito;
    @ChaveEstrangeira
    private ContaVO contamovimentacaoautomaticacredito;
    private PessoaVO favorecidomovimentacaoautomaticaDebito;
    private PessoaVO favorecidomovimentacaoautomaticaCredito;
    private boolean movimentacaoAutomaticaRecebiveisConciliacao = false;
    private boolean buscarFornecedorTodasUnidades;

    private boolean habilitarAlteracaoValorPrevisto;
    private boolean alterarDtPgtoZWAutomaticamenteConc;

    private boolean obrigarPreenchimentoManualDtCompetencia;
    private boolean bloquearCriacaoPlanoConta = false;

    public void setPlanoContasTaxa(PlanoContaTO planoContasTaxa) {
        this.planoContasTaxa = planoContasTaxa;
    }

    public PlanoContaTO getPlanoContasTaxa() {
        return planoContasTaxa;
    }

    public void setCentroCustosTaxa(CentroCustoTO centroCustosTaxa) {
        this.centroCustosTaxa = centroCustosTaxa;
    }

    public CentroCustoTO getCentroCustosTaxa() {
        return centroCustosTaxa;
    }

    public void setUsarCentralEventos(boolean usarCentralEventos) {
        this.usarCentralEventos = usarCentralEventos;
    }

    public boolean getUsarCentralEventos() {
        return usarCentralEventos;
    }

    public void setUsarMovimentacaoContas(boolean usarMovimentacao) {
        this.usarMovimentacaoContas = usarMovimentacao;
    }

    public boolean getUsarMovimentacaoContas() {
        return usarMovimentacaoContas;
    }

    public void setSolicitaSenhaLancarConta(boolean solicitaSenhaLancarConta) {
        this.solicitaSenhaLancarConta = solicitaSenhaLancarConta;
    }

    public boolean getSolicitaSenhaLancarConta() {
        return solicitaSenhaLancarConta;
    }

    public boolean isPermitirContaOutraUnidade() {
        return permitirContaOutraUnidade;
    }

    public void setPermitirContaOutraUnidade(boolean permitirContaOutraUnidade) {
        this.permitirContaOutraUnidade = permitirContaOutraUnidade;
    }

    public boolean isEspecificarCompetencia() {
        return especificarCompetencia;
    }

    public void setEspecificarCompetencia(boolean especificarCompetencia) {
        this.especificarCompetencia = especificarCompetencia;
    }

    public boolean isHabilitarExportacaoAlterData() {
        return habilitarExportacaoAlterData;
    }

    public void setHabilitarExportacaoAlterData(boolean habilitarExportacaoAlterData) {
        this.habilitarExportacaoAlterData = habilitarExportacaoAlterData;
    }

    public boolean isFecharCaixaAutomaticamente() {
        return fecharCaixaAutomaticamente;
    }

    public void setFecharCaixaAutomaticamente(boolean fecharCaixaAutomaticamente) {
        this.fecharCaixaAutomaticamente = fecharCaixaAutomaticamente;
    }

    public boolean isUsarCentralEventos() {
        return usarCentralEventos;
    }

    public boolean isUsarMovimentacaoContas() {
        return usarMovimentacaoContas;
    }

    public boolean isSolicitaSenhaLancarConta() {
        return solicitaSenhaLancarConta;
    }

    public boolean isBloquearAlunoChequeDevolvido() {
        return bloquearAlunoChequeDevolvido;
    }

    public void setBloquearAlunoChequeDevolvido(boolean bloquearAlunoChequeDevolvido) {
        this.bloquearAlunoChequeDevolvido = bloquearAlunoChequeDevolvido;
    }

    public String getMensagembloqueio() {
        return mensagembloqueio;
    }

    public void setMensagembloqueio(String mensagembloqueio) {
        this.mensagembloqueio = mensagembloqueio;
    }

    public PlanoContaTO getPlanoContasDevolucao() {
        return planoContasDevolucao;
    }

    public void setPlanoContasDevolucao(PlanoContaTO planoContasDevolucao) {
        this.planoContasDevolucao = planoContasDevolucao;
    }

    public boolean isAdicionarDevolucaoRelatorioComissao() {
        return adicionarDevolucaoRelatorioComissao;
    }

    public void setAdicionarDevolucaoRelatorioComissao(boolean adicionarDevolucaoRelatorioComissao) {
        this.adicionarDevolucaoRelatorioComissao = adicionarDevolucaoRelatorioComissao;
    }

    public CentroCustoTO getCentroCustoDevolucao() {
        return centroCustoDevolucao;
    }

    public void setCentroCustoDevolucao(CentroCustoTO centroCustoDevolucao) {
        this.centroCustoDevolucao = centroCustoDevolucao;
    }


    public PlanoContaTO getPlanoContasTaxaBoleto() {
        return planoContasTaxaBoleto;
    }

    public void setPlanoContasTaxaBoleto(PlanoContaTO planoContasTaxaBoleto) {
        this.planoContasTaxaBoleto = planoContasTaxaBoleto;
    }

    public CentroCustoTO getCentroCustosTaxaBoleto() {
        return centroCustosTaxaBoleto;
    }

    public void setCentroCustosTaxaBoleto(CentroCustoTO centroCustosTaxaBoleto) {
        this.centroCustosTaxaBoleto = centroCustosTaxaBoleto;
    }

    public boolean isCnpjObrigatorioFornecedor() {
        return cnpjObrigatorioFornecedor;
    }

    public void setCnpjObrigatorioFornecedor(boolean cnpjObrigatorioFornecedor) {
        this.cnpjObrigatorioFornecedor = cnpjObrigatorioFornecedor;
    }

    public boolean isContaPagarCompraEstoque() {
        return contaPagarCompraEstoque;
    }

    public void setContaPagarCompraEstoque(boolean contaPagarCompraEstoque) {
        this.contaPagarCompraEstoque = contaPagarCompraEstoque;
    }

    public boolean isOrdemCompraEstoque() {
        return ordemCompraEstoque;
    }

    public void setOrdemCompraEstoque(boolean ordemCompraEstoque) {
        this.ordemCompraEstoque = ordemCompraEstoque;
    }

    public boolean isContaPagarReceberColabInativo() {
        return contaPagarReceberColabInativo;
    }

    public void setContaPagarReceberColabInativo(boolean contaPagarReceberColabInativo) {
        this.contaPagarReceberColabInativo = contaPagarReceberColabInativo;
    }

    public boolean isCriarContaPagarAutomatico() {
        return criarContaPagarAutomatico;
    }

    public void setCriarContaPagarAutomatico(boolean criarContaPagarAutomatico) {
        this.criarContaPagarAutomatico = criarContaPagarAutomatico;
    }

    public ContaVO getContaCriarContaPagarAutomaticoVO() {
        if (contaCriarContaPagarAutomaticoVO == null) {
            contaCriarContaPagarAutomaticoVO = new ContaVO();
        }
        return contaCriarContaPagarAutomaticoVO;
    }

    public void setContaCriarContaPagarAutomaticoVO(ContaVO contaCriarContaPagarAutomaticoVO) {
        this.contaCriarContaPagarAutomaticoVO = contaCriarContaPagarAutomaticoVO;
    }

    public boolean isApresentarValorPago() {
        return apresentarValorPago;
    }

    public void setApresentarValorPago(boolean apresentarValorPago) {
        this.apresentarValorPago = apresentarValorPago;
    }

    public boolean isPermitirTipoPlanoContaFilho() {
        return permitirTipoPlanoContaFilho;
    }

    public void setPermitirTipoPlanoContaFilho(boolean permitirTipoPlanoContaFilho) {
        this.permitirTipoPlanoContaFilho = permitirTipoPlanoContaFilho;
    }

    public PlanoContaTO getPlanoContasLancamentoAutomaticoSaida() {
        return planoContasLancamentoAutomaticoSaida;
    }

    public void setPlanoContasLancamentoAutomaticoSaida(PlanoContaTO planoContasLancamentoAutomaticoSaida) {
        this.planoContasLancamentoAutomaticoSaida = planoContasLancamentoAutomaticoSaida;
    }

    public PlanoContaTO getPlanoContasLancamentoAutomaticoEntrada() {
        return planoContasLancamentoAutomaticoEntrada;
    }

    public void setPlanoContasLancamentoAutomaticoEntrada(PlanoContaTO planoContasLancamentoAutomaticoEntrada) {
        this.planoContasLancamentoAutomaticoEntrada = planoContasLancamentoAutomaticoEntrada;
    }

    public CentroCustoTO getCentroCustoLancamentoAutomaticoEntrada() {
        return centroCustoLancamentoAutomaticoEntrada;
    }

    public void setCentroCustoLancamentoAutomaticoEntrada(CentroCustoTO centroCustoLancamentoAutomaticoEntrada) {
        this.centroCustoLancamentoAutomaticoEntrada = centroCustoLancamentoAutomaticoEntrada;
    }

    public CentroCustoTO getCentroCustoLancamentoAutomaticoSaida() {
        return centroCustoLancamentoAutomaticoSaida;
    }

    public void setCentroCustoLancamentoAutomaticoSaida(CentroCustoTO centroCustoLancamentoAutomaticoSaida) {
        this.centroCustoLancamentoAutomaticoSaida = centroCustoLancamentoAutomaticoSaida;
    }

    public boolean isInformarFavorecidoAoRealizarMovimentacaoRecebiveis() {
        return informarFavorecidoAoRealizarMovimentacaoRecebiveis;
    }

    public void setInformarFavorecidoAoRealizarMovimentacaoRecebiveis(boolean informarFavorecidoAoRealizarMovimentacaoRecebiveis) {
        this.informarFavorecidoAoRealizarMovimentacaoRecebiveis = informarFavorecidoAoRealizarMovimentacaoRecebiveis;
    }

    public PlanoContaTO getPlanoContasTaxaPix() {
        return planoContasTaxaPix;
    }

    public void setPlanoContasTaxaPix(PlanoContaTO planoContasTaxaPix) {
        this.planoContasTaxaPix = planoContasTaxaPix;
    }

    public CentroCustoTO getCentroCustoTaxaPix() {
        return centroCustoTaxaPix;
    }

    public void setCentroCustoTaxaPix(CentroCustoTO centroCustoTaxaPix) {
        this.centroCustoTaxaPix = centroCustoTaxaPix;
    }


    public String getDescricaomovimentacaoautomaticaDebito() {
        return descricaomovimentacaoautomaticaDebito;
    }

    public void setDescricaomovimentacaoautomaticaDebito(String descricaomovimentacaoautomaticaDebito) {
        this.descricaomovimentacaoautomaticaDebito = descricaomovimentacaoautomaticaDebito;
    }

    public String getDescricaomovimentacaoautomaticaCredito() {
        return descricaomovimentacaoautomaticaCredito;
    }

    public void setDescricaomovimentacaoautomaticaCredito(String descricaomovimentacaoautomaticaCredito) {
        this.descricaomovimentacaoautomaticaCredito = descricaomovimentacaoautomaticaCredito;
    }

    public ContaVO getContamovimentacaoautomaticadebito() {
        if(contamovimentacaoautomaticadebito == null){
            contamovimentacaoautomaticadebito = new ContaVO();
        }
        return contamovimentacaoautomaticadebito;
    }

    public void setContamovimentacaoautomaticadebito(ContaVO contamovimentacaoautomaticadebito) {
        this.contamovimentacaoautomaticadebito = contamovimentacaoautomaticadebito;
    }

    public PessoaVO getFavorecidomovimentacaoautomaticaDebito() {
        if (favorecidomovimentacaoautomaticaDebito == null) {
            favorecidomovimentacaoautomaticaDebito = new PessoaVO();
        }
        return favorecidomovimentacaoautomaticaDebito;
    }


    public ContaVO getContamovimentacaoautomaticacredito() {
        if(contamovimentacaoautomaticacredito == null){
            contamovimentacaoautomaticacredito = new ContaVO();
        }
        return contamovimentacaoautomaticacredito;
    }
    public void setContamovimentacaoautomaticacredito(ContaVO contamovimentacaoautomaticacredito) {
        this.contamovimentacaoautomaticacredito = contamovimentacaoautomaticacredito;
    }

    public void setFavorecidomovimentacaoautomaticaDebito(PessoaVO favorecidomovimentacaoautomaticaDebito) {
        this.favorecidomovimentacaoautomaticaDebito = favorecidomovimentacaoautomaticaDebito;
    }

    public PessoaVO getFavorecidomovimentacaoautomaticaCredito() {
        if (favorecidomovimentacaoautomaticaCredito == null) {
            favorecidomovimentacaoautomaticaCredito = new PessoaVO();
        }
        return favorecidomovimentacaoautomaticaCredito;
    }

    public void setFavorecidomovimentacaoautomaticaCredito(PessoaVO favorecidomovimentacaoautomaticaCredito) {
        this.favorecidomovimentacaoautomaticaCredito = favorecidomovimentacaoautomaticaCredito;
    }

    public boolean isMovimentacaoAutomaticaRecebiveisConciliacao() {
        return movimentacaoAutomaticaRecebiveisConciliacao;
    }

    public void setMovimentacaoAutomaticaRecebiveisConciliacao(boolean movimentacaoAutomaticaRecebiveisConciliacao) {
        this.movimentacaoAutomaticaRecebiveisConciliacao = movimentacaoAutomaticaRecebiveisConciliacao;
    }

    public boolean isBuscarFornecedorTodasUnidades() {
        return buscarFornecedorTodasUnidades;
    }

    public void setBuscarFornecedorTodasUnidades(boolean buscarFornecedorTodasUnidades) {
        this.buscarFornecedorTodasUnidades = buscarFornecedorTodasUnidades;
    }

    public boolean isMetaFinanceiraPorFaturamento() {
        return metaFinanceiraPorFaturamento;
    }

    public void setMetaFinanceiraPorFaturamento(boolean metaFinanceiraPorFaturamento) {
        this.metaFinanceiraPorFaturamento = metaFinanceiraPorFaturamento;
    }

    public boolean isHabilitarAlteracaoValorPrevisto() {
        return habilitarAlteracaoValorPrevisto;
    }

    public void setHabilitarAlteracaoValorPrevisto(boolean habilitarAlteracaoValorPrevisto) {
        this.habilitarAlteracaoValorPrevisto = habilitarAlteracaoValorPrevisto;
    }

    public boolean isAlterarDtPgtoZWAutomaticamenteConc() {
        return alterarDtPgtoZWAutomaticamenteConc;
    }

    public void setAlterarDtPgtoZWAutomaticamenteConc(boolean alterarDtPgtoZWAutomaticamenteConc) {
        this.alterarDtPgtoZWAutomaticamenteConc = alterarDtPgtoZWAutomaticamenteConc;
    }

    public boolean isObrigarPreenchimentoManualDtCompetencia() {
        return obrigarPreenchimentoManualDtCompetencia;
    }

    public void setObrigarPreenchimentoManualDtCompetencia(boolean obrigarPreenchimentoManualDtCompetencia) {
        this.obrigarPreenchimentoManualDtCompetencia = obrigarPreenchimentoManualDtCompetencia;
    }

    public boolean isBloquearCriacaoPlanoConta() {
        return bloquearCriacaoPlanoConta;
    }

    public void setBloquearCriacaoPlanoConta(boolean bloquearCriacaoPlanoConta) {
        this.bloquearCriacaoPlanoConta = bloquearCriacaoPlanoConta;
    }
}
