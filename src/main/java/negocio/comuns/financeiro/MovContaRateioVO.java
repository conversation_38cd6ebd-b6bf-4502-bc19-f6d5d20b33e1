/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class MovContaRateioVO extends SuperVO {
	@NaoControlarLogAlteracao
    private Integer codigo;
	@ChavePrimaria
	private int codigoLog;
	@NaoControlarLogAlteracao
	private int movContaVO;
	@ChaveEstrangeira
    private PlanoContaTO planoContaVO;
	@ChaveEstrangeira
    private CentroCustoTO centroCustoVO;
	@ChaveEstrangeira
	private TipoDocumentoVO tipoDocumentoVO;
	@ChaveEstrangeira
	private FormaPagamentoVO formaPagamentoVO;
    private TipoES tipoes;
    private String descricao;
    private Double valor;
    private String numeroDocumento;
    @NaoControlarLogAlteracao
    private MovContaVO movConta = new MovContaVO();
    @NaoControlarLogAlteracao
    private Boolean escolhido = Boolean.FALSE;
    @NaoControlarLogAlteracao
    private Double saldo = 0.0;
    private EmpresaVO empresaVO;

    public MovContaRateioVO() {
        inicializarDados();
    }

    public void inicializarDados() {
        setCodigo(0);
        setMovContaVO(0);
        setPlanoContaVO(new PlanoContaTO());
        setCentroCustoVO(new CentroCustoTO());
        setTipoDocumentoVO(new TipoDocumentoVO());
        setFormaPagamentoVO(new FormaPagamentoVO());
        setDescricao("");
        setNumeroDocumento("");
        setValor(0.0);
        setEmpresaVO(new EmpresaVO());
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>MovContaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(MovContaRateioVO obj, boolean validarTodosCampos, boolean validarFormaPagamento) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
         if (validarTodosCampos) {
            if (obj.getPlanoContaVO().getCodigo() == null || obj.getPlanoContaVO().getCodigo() == 0) {
                throw new ConsistirException("O campo PLANO DE CONTAS deve ser informado.");
            }
            if (obj.getCentroCustoVO().getCodigo() == null || obj.getCentroCustoVO().getCodigo() == 0) {
                throw new ConsistirException("O campo CENTRO DE CUSTOS deve ser informado.");
            }
            /*if (obj.getTipoDocumentoVO() == null || obj.getTipoDocumentoVO().getCodigo() == 0) {
                throw new ConsistirException("O campo TIPO DE DOCUMENTO (LançamentoRateio) deve ser informado.");
            }*/
        }
        if (validarFormaPagamento && (obj.getFormaPagamentoVO().getCodigo() == null || obj.getFormaPagamentoVO().getCodigo() == 0)) {
            throw new ConsistirException("O campo FORMA DE PAGAMENTO deve ser informado.");
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO deve ser informado.");
        }
        if (obj.getValor() == null || obj.getValor() == 0.00) {
            throw new ConsistirException("O campo VALOR deve ser informado.");
        }
        if(obj.getValor()< 0.0){
            throw new ConsistirException("O campo VALOR deve ser maior que R$ 0,00");
        }
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the movContaVO
     */
    public int getMovContaVO() {
        return movContaVO;
    }

    /**
     * @param movContaVO the movContaVO to set
     */
    public void setMovContaVO(int movContaVO) {
        this.movContaVO = movContaVO;
    }

    /**
     * @return the planoContaVO
     */
    public PlanoContaTO getPlanoContaVO() {
        return planoContaVO;
    }

    /**
     * @param planoContaVO the planoContaVO to set
     */
    public void setPlanoContaVO(PlanoContaTO planoContaVO) {
        this.planoContaVO = planoContaVO;
    }

    /**
     * @return the centroCustoVO
     */
    public CentroCustoTO getCentroCustoVO() {
        return centroCustoVO;
    }

    /**
     * @param centroCustoVO the centroCustoVO to set
     */
    public void setCentroCustoVO(CentroCustoTO centroCustoVO) {
        this.centroCustoVO = centroCustoVO;
    }

    /**
     * @return the tipoDocumentoVO
     */
    public TipoDocumentoVO getTipoDocumentoVO() {
    	if(tipoDocumentoVO == null){
    		tipoDocumentoVO = new TipoDocumentoVO();
    	}
        return tipoDocumentoVO;
    }

    /**
     * @param tipoDocumentoVO the tipoDocumentoVO to set
     */
    public void setTipoDocumentoVO(TipoDocumentoVO tipoDocumentoVO) {
        this.tipoDocumentoVO = tipoDocumentoVO;
    }

    /**
     * @return the formaPagamentoVO
     */
    public FormaPagamentoVO getFormaPagamentoVO() {
    	if(formaPagamentoVO == null){
    		formaPagamentoVO = new FormaPagamentoVO();
    	}
        return formaPagamentoVO;
    }

    /**
     * @param formaPagamentoVO the formaPagamentoVO to set
     */
    public void setFormaPagamentoVO(FormaPagamentoVO formaPagamentoVO) {
        this.formaPagamentoVO = formaPagamentoVO;
    }

    /**
     * @return the tipo
     */
    public TipoES getTipoES() {
        return tipoes;
    }

    /**
     * @param tipo the tipo to set
     */
    public void setTipoES(TipoES tipoes) {
        this.tipoes = tipoes;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * @param descricao the descricao to set
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    /**
     * @return the valor
     */
    public Double getValor() {
        return valor;
    }

	public String getValorFormatado() {
		return Formatador.formatarValorMonetarioSemMoeda(this.getValor());
	}
	
	public String getSaldoFormatado() {
		return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(this.getSaldo());
	}

    /**
     * @param valor the valor to set
     */
    public void setValor(Double valor) {
        this.valor = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valor);
    }

    public String getTipo_Apresentar() {
        if (this.tipoes.getCodigo() != 0) {
            return this.tipoes.getDescricao();
        }
        return "";
    }

	public void setMovConta(MovContaVO movConta) {
		this.movConta = movConta;
	}

	public MovContaVO getMovConta() {
		return movConta;
	}

	public void setEscolhido(Boolean escolhido) {
		this.escolhido = escolhido;
	}

	public Boolean getEscolhido() {
		return escolhido;
	}

	public void setCodigoLog(int codigoLog) {
		this.codigoLog = codigoLog;
	}

	public int getCodigoLog() {
		if(UteisValidacao.emptyNumber(codigoLog))
			codigoLog = codigo;
		return codigoLog;
	}

	public void setSaldo(Double saldo) {
		this.saldo = saldo;
	}

	public Double getSaldo() {
		return saldo;
	}
        
        public String getDataQuitacaoMovConta(){
            return getMovConta().getDataQuitacao_Apresentar();
        }
        
        public String getDataLancamentoMovConta(){
            return getMovConta().getDataLancamento_Apresentar();
        }
        
        public String getFormaPagamento(){
            return getFormaPagamentoVO().getTipoFormaPagamento_Apresentar();
        }
        
        public String getTipoEsDescricao(){
            return getTipoES().getDescricao();
        }
        
        public String getTipoOperacaoLancamentoDescricao(){
            return getMovConta().getTipoOperacaoLancamento().getDescricao();
        }
        
        public String getLote(){
            if (getMovConta().getLote().getCodigo() == 0) {
                return "";
            }else{
                return getMovConta().getLote().getCodigo().toString();
            }
        }

    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }
}
