package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoBaixaEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.boleto.AtributoBoletoEnum;
import servicos.impl.dcc.itau.BBCnab400ItauStatusEnum;
import servicos.propriedades.PropsService;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 07/12/2021
 */
public class BoletoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date dataRegistro;
    private String idExterno;
    private TipoBoletoEnum tipo;
    private UsuarioVO usuarioVO;
    @ChaveEstrangeira
    private PessoaVO pessoaVO;
    @ChaveEstrangeira
    private EmpresaVO empresaVO;
    private SituacaoBoletoEnum situacao;
    private Double valor;
    private Double valorLiquido;
    private Double valorPago;
    private Double valorTarifa;
    private Date dataVencimento;
    private Date dataCredito;
    private Date dataPagamento;
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenioCobrancaVO;
    @ChaveEstrangeira
    private MovPagamentoVO movPagamentoVO;
    @ChaveEstrangeira
    private ReciboPagamentoVO reciboPagamentoVO;
    private OrigemCobrancaEnum origem;
    private String paramsEnvio;
    private String paramsResposta;
    private String outrasInformacoes;
    private String nossoNumero;
    private String numeroInterno;
    private String numeroExterno;
    private String linhaDigitavel;
    private String linkBoleto;
    private String jsonEstorno;
    private Integer diaMesDescontoPagAntecipado;
    private Double porcentagemDescontoPagAntecipado;
    private Double valorDescontoPagAntecipado;
    private Integer identificador;
    @NaoControlarLogAlteracao
    private List<MovParcelaVO> listaParcelas;
    @NaoControlarLogAlteracao
    private List<BoletoMovParcelaVO> listaBoletoMovParcela;
    @NaoControlarLogAlteracao
    private boolean cancelarBoleto = true;
    @NaoControlarLogAlteracao
    private boolean selecionado = false;
    @NaoControlarLogAlteracao
    private TipoBaixaEnum tipoBaixa;
    @NaoControlarLogAlteracao
    private String codigosRetorno = "";
    @NaoControlarLogAlteracao
    private Double valorPossivelDesconto;
    private boolean pagoUsandoDesconto = false;
    private String transactionReceiptUrl;
    private String codigoBarrasNumerico;

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getInformacoesTitleTelaCliente() {
        StringBuilder title = new StringBuilder();
        title.append("<b>Código:</b> ").append(this.getCodigo()).append("<br/>\n");
        if (!UteisValidacao.emptyString(this.getIdExterno()) && this.getTipo().equals(TipoBoletoEnum.ASAAS)) {
            title.append("<b>ID Externo ASAAS: </b> ").append(this.getIdExterno()).append("<br/>\n");
        }
        if (!UteisValidacao.emptyString(this.getNossoNumero())) {
            title.append("<b>Nosso número:</b> ").append(this.getNossoNumero()).append("<br/>\n");
        }
        title.append("<b>Data Registro:</b> ").append(getDataRegistroApresentar()).append("<br/>\n");
        title.append("<b>Data Vencimento:</b> ").append(getDataVencimentoApresentar()).append("<br/>\n");
        if (!UteisValidacao.emptyNumber(getValorPago())) {
            title.append("<b>Valor Pago:</b> ").append(getValorPagoApresentar()).append("<br/>\n");
        }
        if (!UteisValidacao.emptyString(getDataCreditoApresentar())) {
            title.append("<b>Previsão de crédito PjBank:</b> ").append(getDataCreditoApresentar()).append("<br/>\n");
        }
        if (!UteisValidacao.emptyString(getDataPagamentoApresentar())) {
            title.append("<b>Data Pagamento:</b> ").append(getDataPagamentoApresentar()).append("<br/>\n");
        }
        if (!UteisValidacao.emptyString(this.getConvenioCobrancaVO().getDescricao())) {
            title.append("<b>Convênio de Cobrança:</b> ").append(this.getConvenioCobrancaVO().getDescricao()).append("<br/>\n");
        }
        title.append("<b>Tipo:</b> ").append(this.getTipo().getDescricao()).append("<br/>\n");
        if (!this.getOrigem().equals(OrigemCobrancaEnum.NENHUM)) {
            title.append("<b>Origem:</b> ").append(this.getOrigem().getDescricao()).append("<br/>\n");
        }
        if (isPagoUsandoDesconto()) {
            title.append("<b>Usou desconto de pagamento antecipado</b> ");
        }
        return title.toString();
    }

    public String getIdExterno() {
        if (idExterno == null) {
            idExterno = "";
        }
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }

    public String getSituacaoTitleTelaCliente() {
        try {
            if (this.getSituacao().equals(SituacaoBoletoEnum.PAGO) && !UteisValidacao.emptyNumber(getReciboPagamentoVO().getCodigo())) {
                return getReciboPagamentoTitleTelaCliente();
            } else if (this.getSituacao().equals(SituacaoBoletoEnum.ERRO)) {
                return getErroMensagem();
            } else if ((this.getSituacao().equals(SituacaoBoletoEnum.CANCELADO) ||
                    this.getSituacao().equals(SituacaoBoletoEnum.ESTORNADO)) &&
                    !UteisValidacao.emptyString(getJsonEstorno())) {
                JSONObject json = new JSONObject(getJsonEstorno());
                StringBuilder title = new StringBuilder();
                if (this.getSituacao().equals(SituacaoBoletoEnum.ESTORNADO) && !getJsonEstorno().contains("Processo")) {
                    title.append("<b>A parcela que estava vinculada ao boleto foi estornada no ZillyonWeb.<br/>Não foi possivel cancelar o boleto na ").append(this.getTipo().getDescricao()).append(" pois o boleto está PAGO.</b><br/>");
                } else {
                    String prefixo = this.getTipo().equals(TipoBoletoEnum.ASAAS) || this.getTipo().equals(TipoBoletoEnum.BANCO_BRASIL) ? "no" : "na";
                    title.append("<b>Boleto foi cancelado ").append(prefixo).append(" ").append(this.getTipo().getDescricao()).append(".</b><br/>Isso acontece automaticamente quando se estorna/cancela o contrato do aluno<br/>");
                    title.append("ou então pelo cancelamento manual através do botão \"Cancelar Boleto\" disponível aqui nesta tela.");
                }
                if (this.getTipo().equals(TipoBoletoEnum.BANCO_BRASIL)) {
                    title.append("<br/><b>Operação:</b> ").append(" Cancelamento de boleto");
                    title.append("<br/><b>Data:</b> ").append(json.optString("dataBaixa"));
                } else {
                    title.append("<br/><b>Operação:</b> ").append(json.optString("operacao"));
                    title.append("<br/><b>Data:</b> ").append(json.optString("data"));
                }
                String usuario = json.optString("usuario");
                if (UteisValidacao.emptyString(usuario)) {
                    usuario = json.optString("usuario_nome");
                }
                if (!UteisValidacao.emptyString(usuario)) {
                    title.append("<br/><b>Usuário:</b> ").append(usuario);
                }
                String msgCancelarBoleto = json.optString("msgCancelarBoleto");
                if (!UteisValidacao.emptyString(msgCancelarBoleto)) {
                    title.append("<br/><b>Retorno cancelar boleto:</b> ").append(msgCancelarBoleto);
                }
                if (getJsonEstorno().contains("Processo")) {
                    String origem = json.optString("origem");
                    title.append("<br/><b>Origem:</b> ").append(origem);
                }
                return title.toString();

            } else if (this.getSituacao().equals(SituacaoBoletoEnum.AGUARDANDO_REGISTRO)){
                return "Aguardando registro bancário.</br> O boleto estará apto para pagamento em alguns minutos.";
            } else {
                return this.getSituacao().getDescricao();
            }
        } catch (Exception ex) {
            return this.getSituacao().getDescricao();
        }
    }

    public String getErroMensagem() {
        try {
            if (!UteisValidacao.emptyString(getOutrasInformacoes())) {
                JSONObject json = new JSONObject(getOutrasInformacoes());
                String jsonString = json.optString(AtributoBoletoEnum.msg_erro.name());
                if (!UteisValidacao.emptyString(jsonString) && (getConvenioCobrancaVO().isBoletoPjBank() || this.isPJBank())){
                    if (jsonString.contains("já está pago e não pode ser alterado")){
                        return "Houve uma falha na pjbank no momento da geração do boleto. Por favor gere o boleto novamente.";
                    }
                    if (jsonString.contains("Replica ativa. Não é possível realizar esta operação")){
                        return jsonString + "</br>Falha da pjbank. Por favor gere o boleto novamente.";
                    }
                    if (jsonString.equals("Endpoint request timed out")){
                        return jsonString + "</br>Falha de comunicação na API da pjbank. Por favor gere o boleto novamente.";
                    }
                    if (jsonString.contains("A JSONObject text must begin with")){
                        return "Falha de comunicação na API da pjbank. Por favor gere o boleto novamente.";
                    }
                    if (jsonString.contains("Estamos trabalhando para corrigir")){
                        return "Erro interno da PJBank. Eles já estão trabalhando para resolver o problema, tente novamente mais tarde!";
                    }
                }
                return jsonString;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    public SituacaoBoletoEnum getSituacao() {
        if (situacao == null) {
            situacao = SituacaoBoletoEnum.NENHUMA;
        }
        return situacao;
    }

    public void setSituacao(SituacaoBoletoEnum situacao) {
        this.situacao = situacao;
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetario(getValor());
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getUsuarioApresentar() {
        return getUsuarioVO().getNome();
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        if (movPagamentoVO == null) {
            movPagamentoVO = new MovPagamentoVO();
        }
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public String getDataRegistroApresentar() {
        return Calendario.getDataAplicandoFormatacao(getDataRegistro(), "dd/MM/yyyy HH:mm:ss");
    }

    public String getDataRegistroTitle() {
        StringBuilder title = new StringBuilder();
        title.append("<b>Data de Registro:</b> ").append(getDataRegistroApresentar()).append(" <br/>");
        if (!UteisValidacao.emptyString(getUsuarioApresentar())) {
            title.append("<b>Usuário:</b> ").append(getUsuarioApresentar()).append(" <br/>");
        }
        if (!getOrigem().equals(OrigemCobrancaEnum.NENHUM)) {
            title.append("<b>Origem:</b> ").append(getOrigem().getDescricao()).append(" <br/>");
        }
        return title.toString();
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataVencimentoApresentar() {
        return Calendario.getDataAplicandoFormatacao(getDataVencimento(), "dd/MM/yyyy");
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public TipoBoletoEnum getTipo() {
        if (tipo == null) {
            tipo = TipoBoletoEnum.NENHUMA;
        }
        return tipo;
    }

    public void setTipo(TipoBoletoEnum tipo) {
        this.tipo = tipo;
    }

    public OrigemCobrancaEnum getOrigem() {
        if (origem == null) {
            origem = OrigemCobrancaEnum.NENHUM;
        }
        return origem;
    }

    public void setOrigem(OrigemCobrancaEnum origem) {
        this.origem = origem;
    }

    public List<MovParcelaVO> getListaParcelas() {
        if (listaParcelas == null) {
            listaParcelas = new ArrayList<>();
        }
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getParamsEnvio() {
        if (paramsEnvio == null) {
            paramsEnvio = "";
        }
        return paramsEnvio;
    }

    public void setParamsEnvio(String paramsEnvio) {
        this.paramsEnvio = paramsEnvio;
    }

    public String getParamsResposta() {
        if (paramsResposta == null) {
            paramsResposta = "";
        }
        return paramsResposta;
    }

    public void setParamsResposta(String paramsResposta) {
        this.paramsResposta = paramsResposta;
    }

    public static void validarDados(BoletoVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getTipo().equals(TipoBoletoEnum.NENHUMA)) {
            throw new ConsistirException("Tipo boleto não informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
            throw new ConsistirException("Pessoa não informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            throw new ConsistirException("Empresa não informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo())) {
            throw new ConsistirException("Convênio de cobrança não informado.");
        }
        if (UteisValidacao.emptyList(obj.getListaParcelas())) {
            throw new ConsistirException("Nenhuma parcela informada.");
        }
    }

    public void adicionarOutrasInformacoes(AtributoBoletoEnum atributoBoletoEnum, String value) {
        JSONObject jsonOutrasInformacoes = new JSONObject();
        if (!UteisValidacao.emptyString(this.getOutrasInformacoes())) {
            try {
                jsonOutrasInformacoes = new JSONObject(this.getOutrasInformacoes());
            } catch (Exception ex) {
                jsonOutrasInformacoes = new JSONObject();
                jsonOutrasInformacoes.put(AtributoBoletoEnum.outras_informacoes_anterior.name(), this.getOutrasInformacoes());
            }
        }
        try {
            jsonOutrasInformacoes.put(atributoBoletoEnum.name(), value);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        this.setOutrasInformacoes(jsonOutrasInformacoes.toString());
    }

    public String obterItemOutrasInformacoes(AtributoBoletoEnum atributoBoletoEnum) {
        try {
            JSONObject json = new JSONObject(getOutrasInformacoes());
            return json.getString(atributoBoletoEnum.name());
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public String getOutrasInformacoes() {
        if (outrasInformacoes == null) {
            outrasInformacoes = "";
        }
        return outrasInformacoes;
    }

    public void setOutrasInformacoes(String outrasInformacoes) {
        this.outrasInformacoes = outrasInformacoes;
    }

    public List<BoletoMovParcelaVO> getListaBoletoMovParcela() {
        if (listaBoletoMovParcela == null) {
            listaBoletoMovParcela = new ArrayList<>();
        }
        return listaBoletoMovParcela;
    }

    public void setListaBoletoMovParcela(List<BoletoMovParcelaVO> listaBoletoMovParcela) {
        this.listaBoletoMovParcela = listaBoletoMovParcela;
    }

    public Double getValorLiquido() {
        return valorLiquido;
    }

    public void setValorLiquido(Double valorLiquido) {
        this.valorLiquido = valorLiquido;
    }

    public String getValorPagoApresentar() {
        return Formatador.formatarValorMonetario(getValorPago());
    }

    public Double getValorPago() {
        return valorPago;
    }

    public void setValorPago(Double valorPago) {
        this.valorPago = valorPago;
    }

    public String getDataCreditoApresentar() {
        return Calendario.getDataAplicandoFormatacao(getDataCredito(), "dd/MM/yyyy");
    }

    public Date getDataCredito() {
        return dataCredito;
    }

    public void setDataCredito(Date dataCredito) {
        this.dataCredito = dataCredito;
    }

    public String getDataPagamentoApresentar() {
        return Calendario.getDataAplicandoFormatacao(getDataPagamento(), "dd/MM/yyyy HH:mm:ss");
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getJsonEstorno() {
        if (jsonEstorno == null) {
            jsonEstorno = "";
        }
        return jsonEstorno;
    }

    public void setJsonEstorno(String jsonEstorno) {
        this.jsonEstorno = jsonEstorno;
    }

    public boolean isApresentarImprimirBoleto() {
        return (this.getTipo().equals(TipoBoletoEnum.ITAU) && !this.getSituacao().equals(SituacaoBoletoEnum.PAGO) && !UteisValidacao.emptyString(this.getLinkBoleto())) ||
                ((this.getTipo().equals(TipoBoletoEnum.PJ_BANK) || this.getTipo().equals(TipoBoletoEnum.ASAAS)) && !UteisValidacao.emptyString(this.getLinkBoleto())) ||
                (this.getTipo().equals(TipoBoletoEnum.CAIXA) && (this.getSituacao().equals(SituacaoBoletoEnum.GERADO) || this.getSituacao().equals(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO)));
    }

    public boolean isApresentarImprimirBoletoBancoBrasil() {
        return (this.getTipo().equals(TipoBoletoEnum.BANCO_BRASIL) && this.getSituacao().equals(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO));
    }

    public boolean isApresentarImprimirReciboBoleto() {
        return (this.getTipo().equals(TipoBoletoEnum.ASAAS) && !UteisValidacao.emptyString(this.getTransactionReceiptUrl()) && this.getSituacao().equals(SituacaoBoletoEnum.PAGO));
    }

    public String getLinkBoleto() {
        if (linkBoleto == null) {
            linkBoleto = "";
        }
        return linkBoleto;
    }

    public void setLinkBoleto(String linkBoleto) {
        this.linkBoleto = linkBoleto;
    }

    public String getNossoNumero() {
        if (nossoNumero == null) {
            nossoNumero = "";
        }
        return nossoNumero;
    }

    public void setNossoNumero(String nossoNumero) {
        this.nossoNumero = nossoNumero;
    }

    public String getNumeroInterno() {
        if (numeroInterno == null) {
            numeroInterno = "";
        }
        return numeroInterno;
    }

    public void setNumeroInterno(String numeroInterno) {
        this.numeroInterno = numeroInterno;
    }

    public String getNumeroExterno() {
        if (numeroExterno == null) {
            numeroExterno = "";
        }
        return numeroExterno;
    }

    public void setNumeroExterno(String numeroExterno) {
        this.numeroExterno = numeroExterno;
    }

    public String getReciboPagamentoApresentar() {
        if (UteisValidacao.emptyNumber(getReciboPagamentoVO().getCodigo())) {
            return "";
        } else {
            return getReciboPagamentoVO().getCodigo().toString();
        }
    }

    public String getReciboPagamentoTitleTelaCliente() {
        StringBuilder title = new StringBuilder();
        if (!UteisValidacao.emptyNumber(getReciboPagamentoVO().getCodigo())) {
            title.append("<b>Recibo:</b> ").append(this.getReciboPagamentoVO().getCodigo()).append("<br/>\n");
        }
        if (!UteisValidacao.emptyNumber(getMovPagamentoVO().getCodigo())) {
            title.append("<b>Pagamento:</b> ").append(this.getMovPagamentoVO().getCodigo()).append("<br/>\n");
        }

        if (!UteisValidacao.emptyNumber(this.getValorPago())) {
            title.append("<b>Valor Pago:</b> ").append(Formatador.formatarValorMonetario(getValorPago())).append("<br/>\n");
        }
        if (!UteisValidacao.emptyNumber(this.getValorTarifa())) {
            title.append("<b>Valor Tarifa:</b> ").append(Formatador.formatarValorMonetario(getValorTarifa())).append("<br/>\n");
        }
        if (!UteisValidacao.emptyNumber(this.getValorLiquido())) {
            title.append("<b>Valor Líquido:</b> ").append(Formatador.formatarValorMonetario(getValorLiquido())).append("<br/>\n");
        }

        if (getDataPagamento() != null) {
            title.append("<b>Data Pagamento:</b> ").append(Calendario.getDataAplicandoFormatacao(getDataPagamento(), "dd/MM/yyyy HH:mm:ss")).append("<br/>\n");
        }
        if (getDataCredito() != null) {
            title.append("<b>Data Crédito:</b> ").append(Calendario.getDataAplicandoFormatacao(getDataCredito(), "dd/MM/yyyy HH:mm:ss")).append("<br/>\n");
        }
        return title.toString();
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        if (reciboPagamentoVO == null) {
            reciboPagamentoVO = new ReciboPagamentoVO();
        }
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    public boolean isPodeCancelar() {
        return !getSituacao().equals(SituacaoBoletoEnum.PAGO) &&
                !getSituacao().equals(SituacaoBoletoEnum.REJEITADO) &&
                !getSituacao().equals(SituacaoBoletoEnum.ERRO) &&
                !getSituacao().equals(SituacaoBoletoEnum.CANCELADO) &&
                !getSituacao().equals(SituacaoBoletoEnum.ESTORNADO) &&
                UteisValidacao.emptyNumber(getMovPagamentoVO().getCodigo()) &&
                UteisValidacao.emptyNumber(getReciboPagamentoVO().getCodigo());
    }

    public String getTitleCancelar() {
        if (this.getTipo().equals(TipoBoletoEnum.ITAU)) {
            return "O Banco Itaú não disponibiliza o cancelamento online." +
                    "<br/>Será alterado a situação do boleto para CANCELADO porém no banco o boleto não sofrerá alterações.";
        } else {
            return "Cancelar boleto";
        }
    }

    public boolean isPodeEnviarEmail() {
        return (isApresentarImprimirBoleto() || isApresentarImprimirBoletoBancoBrasil()) &&
                (getSituacao().equals(SituacaoBoletoEnum.AGUARDANDO_REGISTRO) ||
                getSituacao().equals(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO)) ||
                (getSituacao().equals(SituacaoBoletoEnum.GERADO) && getTipo().equals(TipoBoletoEnum.CAIXA));
    }

    public boolean isPodeSincronizar() {
        return (this.getTipo().equals(TipoBoletoEnum.PJ_BANK) || this.getTipo().equals(TipoBoletoEnum.ASAAS) || this.getTipo().equals(TipoBoletoEnum.CAIXA) ||
                this.getTipo().equals(TipoBoletoEnum.BANCO_BRASIL) || this.getTipo().equals(TipoBoletoEnum.ITAU)) &&
                (getSituacao().equals(SituacaoBoletoEnum.NENHUMA) ||
                getSituacao().equals(SituacaoBoletoEnum.AGUARDANDO_REGISTRO) ||
                getSituacao().equals(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO) ||
                (getSituacao().equals(SituacaoBoletoEnum.PAGO) && (UteisValidacao.emptyNumber(this.getMovPagamentoVO().getCodigo()) ||
                        UteisValidacao.emptyNumber(this.getReciboPagamentoVO().getCodigo())))) ||
                getSituacao().equals(SituacaoBoletoEnum.CANCELADO) && this.getTipo().equals(TipoBoletoEnum.ASAAS);
    }

    public boolean isApresentarDetalhes() {
        return this.getTipo().equals(TipoBoletoEnum.PJ_BANK) &&
                !this.getSituacao().equals(SituacaoBoletoEnum.CANCELADO) &&
                !this.getSituacao().equals(SituacaoBoletoEnum.ERRO) &&
                !this.getSituacao().equals(SituacaoBoletoEnum.ESTORNADO);
    }

    public boolean isApresentarDetalhesAsaas() {
        return this.getTipo().equals(TipoBoletoEnum.ASAAS);
    }

    public boolean isCanceladoOuEstornado() {
        return getSituacao().equals(SituacaoBoletoEnum.CANCELADO) || getSituacao().equals(SituacaoBoletoEnum.ESTORNADO);
    }

    public boolean isPago() {
        return getSituacao().equals(SituacaoBoletoEnum.PAGO);
    }

    public boolean isErro() {
        return this.getSituacao().equals(SituacaoBoletoEnum.ERRO);
    }

    public boolean isEstornado() {
        return this.getSituacao().equals(SituacaoBoletoEnum.ESTORNADO);
    }

    public String getLinhaDigitavel() {
        if (linhaDigitavel == null) {
            linhaDigitavel = "";
        }
        return linhaDigitavel;
    }

    public void setLinhaDigitavel(String linhaDigitavel) {
        this.linhaDigitavel = linhaDigitavel;
    }

    public Integer getDiaMesDescontoPagAntecipado() {
        return diaMesDescontoPagAntecipado;
    }

    public void setDiaMesDescontoPagAntecipado(Integer diaMesDescontoPagAntecipado) {
        this.diaMesDescontoPagAntecipado = diaMesDescontoPagAntecipado;
    }

    public Double getPorcentagemDescontoPagAntecipado() {
        return porcentagemDescontoPagAntecipado;
    }

    public void setPorcentagemDescontoPagAntecipado(Double porcentagemDescontoPagAntecipado) {
        this.porcentagemDescontoPagAntecipado = porcentagemDescontoPagAntecipado;
    }

    public Double getValorDescontoPagAntecipado() {
        if (valorDescontoPagAntecipado == null) {
            valorDescontoPagAntecipado = 0.0;
        }
        return valorDescontoPagAntecipado;
    }

    public void setValorDescontoPagAntecipado(Double valorDescontoPagAntecipado) {
        this.valorDescontoPagAntecipado = valorDescontoPagAntecipado;
    }

    public boolean isPossuiDesconto() {
        return !UteisValidacao.emptyNumber(getDiaMesDescontoPagAntecipado()) && (!UteisValidacao.emptyNumber(getPorcentagemDescontoPagAntecipado()) || !UteisValidacao.emptyNumber(getValorDescontoPagAntecipado()));
    }

    public Double getValorTarifa() {
        return valorTarifa;
    }

    public void setValorTarifa(Double valorTarifa) {
        this.valorTarifa = valorTarifa;
    }

    public boolean isCancelarBoleto() {
        return cancelarBoleto;
    }

    public void setCancelarBoleto(boolean cancelarBoleto) {
        this.cancelarBoleto = cancelarBoleto;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public static LinkedHashSet<String> obterListaImpressaoBoleto(List<BoletoVO> listaBoletos) {
        LinkedHashSet<String> pedidoNumeroPJBank = new LinkedHashSet<>();
        Ordenacao.ordenarLista(listaBoletos, "dataVencimento");
        for (BoletoVO boletoVO : listaBoletos) {
            if (!UteisValidacao.emptyString(boletoVO.getNumeroExterno())) {
                pedidoNumeroPJBank.add(boletoVO.getNumeroExterno());
            } else {
                pedidoNumeroPJBank.add(boletoVO.getNumeroInterno());
            }
        }
        return pedidoNumeroPJBank;
    }

    public TipoBaixaEnum getTipoBaixa() {
        if (tipoBaixa == null) {
            tipoBaixa = TipoBaixaEnum.NAO_BAIXADO;
        }
        return tipoBaixa;
    }

    public void setTipoBaixa(TipoBaixaEnum tipoBaixa) {
        this.tipoBaixa = tipoBaixa;
    }

    public String getCodigosRetorno() {
        if (codigosRetorno == null) {
            codigosRetorno = "";
        }
        return codigosRetorno;
    }

    public void setCodigosRetorno(String codigosRetorno) {
        this.codigosRetorno = codigosRetorno;
    }

    public boolean processouRetorno(String codigoRetorno) {
        return getCodigosRetorno().contains(codigoRetorno + "|");
    }

    public boolean isLiquidado(String statusVenda) {
        if (getTipo().equals(TipoBoletoEnum.ITAU)) {
            return statusVenda.equals(BBCnab400ItauStatusEnum.Status06.getId());
        }
        return false;
    }

    public boolean isRegistroAceito(String statusVenda) {
        if (getTipo().equals(TipoBoletoEnum.ITAU)) {
            return statusVenda.equals(BBCnab400ItauStatusEnum.Status02.getId());
        }
        return false;
    }

    public boolean isStatusPrevisto(String statusVenda) {
        if (getTipo().equals(TipoBoletoEnum.ITAU)) {
            for (BBCnab400ItauStatusEnum e : BBCnab400ItauStatusEnum.values()) {
                if (statusVenda.equals(e.getId())) {
                    return true;
                }
            }
        }
        return false;
    }

    public String obterUrlLinkPDF(String chave) {
        JSONObject json = new JSONObject();
        json.put("chave", chave);
        json.put("boleto", this.getCodigo());
        //criptografia compartilhada com a API
        return (Uteis.getUrlAPI() + "/prest/boleto/visualizar/" + Uteis.encriptar(json.toString(), PropsService.getPropertyValue(PropsService.chaveCriptoBoleto)));
    }

    public Integer getIdentificador() {
        if (identificador == null) {
            identificador = 0;
        }
        return identificador;
    }

    public void setIdentificador(Integer identificador) {
        this.identificador = identificador;
    }

    public Double getValorPossivelDesconto() {
        if (valorPossivelDesconto == null) {
            return 0.0;
        }
        return valorPossivelDesconto;
    }

    public void setValorPossivelDesconto(Double valorPossivelDesconto) {
        this.valorPossivelDesconto = valorPossivelDesconto;
    }

    public boolean isPagoUsandoDesconto() {
        return pagoUsandoDesconto;
    }

    public void setPagoUsandoDesconto(boolean pagoUsandoDesconto) {
        this.pagoUsandoDesconto = pagoUsandoDesconto;
    }

    public String getNomePagadorBoleto() {
        try {
            if (this.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                JSONObject json = new JSONObject(this.getParamsEnvio());
                if (!UteisValidacao.emptyString(json.optString("nome_cliente"))) {
                    return json.optString("nome_cliente");
                }
            }
            return this.getPessoaVO().getNome();
        } catch (Exception ex) {
            return this.getPessoaVO().getNome();
        }
    }

    public String getTransactionReceiptUrl() {
        if (UteisValidacao.emptyString(transactionReceiptUrl)) {
            return "";
        }
        return transactionReceiptUrl;
    }

    public void setTransactionReceiptUrl(String transactionReceiptUrl) {
        this.transactionReceiptUrl = transactionReceiptUrl;
    }

    public boolean isPJBank() {
        return getTipo().equals(TipoBoletoEnum.PJ_BANK);
    }

    public boolean isASAAS() {
        return getTipo().equals(TipoBoletoEnum.ASAAS);
    }

    public String getCodigoBarrasNumerico() {
        if (UteisValidacao.emptyString(codigoBarrasNumerico)) {
            return "";
        }
        return codigoBarrasNumerico;
    }

    public void setCodigoBarrasNumerico(String codigoBarrasNumerico) {
        this.codigoBarrasNumerico = codigoBarrasNumerico;
    }
}
