package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;

import java.util.Date;

/**
 * Reponsável por manter os dados da entidade VendaAvulsa. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class ProdutoTextoPadraoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    private Integer vendaAvulsa;
    private Integer aulaavulsadiaria;
    private Integer produto;
    private Integer planoTextoPadrao;
    private Date dataAssinaturaContrato;
    private String ipAssinaturaContrato;
    private String emailRecebimento;
    @NaoControlarLogAlteracao
    private Date assinadoEm;
    private String texto;
    private Date dataLancamento;
    @NaoControlarLogAlteracao
    protected ClienteVO cliente;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataAssinaturaContrato() {
        return dataAssinaturaContrato;
    }

    public void setDataAssinaturaContrato(Date dataAssinaturaContrato) {
        this.dataAssinaturaContrato = dataAssinaturaContrato;
    }

    public String getIpAssinaturaContrato() {
        return ipAssinaturaContrato;
    }

    public void setIpAssinaturaContrato(String ipAssinaturaContrato) {
        this.ipAssinaturaContrato = ipAssinaturaContrato;
    }

    public String getEmailRecebimento() {
        return emailRecebimento;
    }

    public void setEmailRecebimento(String emailRecebimento) {
        this.emailRecebimento = emailRecebimento;
    }

    public Date getAssinadoEm() {
        return assinadoEm;
    }

    public void setAssinadoEm(Date assinadoEm) {
        this.assinadoEm = assinadoEm;
    }

    public Integer getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(Integer vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public Integer getPlanoTextoPadrao() {
        return planoTextoPadrao;
    }

    public void setPlanoTextoPadrao(Integer planoTextoPadrao) {
        this.planoTextoPadrao = planoTextoPadrao;
    }

    public Integer getAulaavulsadiaria() {
        return aulaavulsadiaria;
    }

    public void setAulaavulsadiaria(Integer aulaavulsadiaria) {
        this.aulaavulsadiaria = aulaavulsadiaria;
    }
}
