/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

import java.util.Date;

import br.com.pactosolucoes.integracao.log.LogJSON;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class LogVO extends SuperVO {

    protected Integer codigo;
    protected String nomeEntidade;
    protected String nomeEntidadeDescricao;
    protected String chavePrimaria;
    protected String chavePrimariaEntidadeSubordinada;
    protected String nomeCampo;
    protected String valorCampoAnterior;
    protected String valorCampoAlterado;
    protected Date dataAlteracao;
    protected String responsavelAlteracao;
    protected String operacao;
    protected Integer pessoa;
    private String descricao;
    private String userOAMD = "";
    private Integer cliente;
    private String clienteNome;

    private String nomePessoa;

    private String origem;

    public LogVO() {
        inicializarDados();
    }

    public LogVO(LogJSON logJSON) {
        codigo = logJSON.getCodigo();

        nomeEntidade = logJSON.getNomeEntidade();
        nomeEntidadeDescricao = logJSON.getNomeEntidadeDescricao();
        chavePrimaria = logJSON.getChavePrimaria();
        chavePrimariaEntidadeSubordinada = logJSON.getChavePrimariaEntidadeSubordinada();

        nomeCampo = logJSON.getNomeCampo();
        valorCampoAnterior = logJSON.getValorCampoAnterior();
        valorCampoAlterado = logJSON.getValorCampoAlterado();
        dataAlteracao = logJSON.getDataAlteracao();

        responsavelAlteracao = logJSON.getResponsavelAlteracao();
        operacao = logJSON.getOperacao();
        pessoa = logJSON.getPessoa();
        descricao = logJSON.getDescricao();

        userOAMD = logJSON.getUserOAMD();
        cliente = logJSON.getCliente();
        clienteNome = logJSON.getClienteNome();
        nomePessoa = logJSON.getNomePessoa();
    }

    public void inicializarDados() {
        setNomeEntidade("");
        setNomeEntidadeDescricao("");
        setChavePrimaria("");
        setChavePrimariaEntidadeSubordinada("");
        setValorCampoAnterior("");
        setValorCampoAlterado("");
        setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        setNomeCampo("");
        setResponsavelAlteracao("");
        setOperacao("");
        setPessoa(new Integer(0));
        setUserOAMD("");
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the nomeEntidade
     */
    public String getNomeEntidade() {
        if (nomeEntidade == null) {
            nomeEntidade = "";
        }
        return nomeEntidade;
    }

    public String getNomeEntidade_Apresentar() {
        if (!getChavePrimariaEntidadeSubordinada().equals("")) {
            return "(" + getNomeEntidadeDescricao() + ")";
        } else {
            return getNomeEntidade();
        }
    }

    /**
     * @param nomeEntidade the nomeEntidade to set
     */
    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    /**
     * @return the chavePrimaria
     */
    public String getChavePrimaria() {
        if (chavePrimaria == null) {
            chavePrimaria = "";
        }
        return chavePrimaria;
    }

    /**
     * @param chavePrimaria the chavePrimaria to set
     */
    public void setChavePrimaria(String chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    /**
     * @return the nomeCampo
     */
    public String getNomeCampo() {
        if (nomeCampo == null) {
            nomeCampo = "";
        }
        return nomeCampo;
    }

    /**
     * @param nomeCampo the nomeCampo to set
     */
    public void setNomeCampo(String nomeCampo) {
        this.nomeCampo = nomeCampo;
    }

    /**
     * @return the valorCampoAnterior
     */
    public String getValorCampoAnterior() {
        if (valorCampoAnterior == null) {
            valorCampoAnterior = "";
        }
        if (valorCampoAnterior.equals("true")) {
            valorCampoAnterior = "Sim";
        }
        if (valorCampoAnterior.equals("false")) {
            valorCampoAnterior = "Não";
        }
        return valorCampoAnterior;
    }

    public String getValorCampoAnteriorSemQuebra(){
        return getValorCampoAnterior().replaceAll("\n","")
                .replaceAll("Parcela=","Parcela =")
                .replaceAll(";","; ")
                .replaceAll("Produtos que foram", "\n\nProdutos que foram");
    }

    public String getValorCampoAlteradoSemQuebra() {
        String valor = getValorCampoAlterado();
        String[] valores = valor.split("--------------------------------------");
        String quebras = "";
        try {
            for (int x = (valor.contains("Foi aplicada uma taxa") || valor.contains("Foi aplicado um desconto")) ? 4 : 3; x < getValorCampoAnterior().split("Produto").length; x++) {
                quebras += "\n";
            }
            valor = valores[valores.length - 1] + "\n" + valores[0] + "\n" + valores[1];
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (valor.replaceAll("\n", "")
                .replaceAll("Parcela no valor:", " Parcela no valor:")
                .replaceAll("Respons", "\n\nRespons")) + quebras;
    }

    /**
     * @param valorCampoAnterior the valorCampoAnterior to set
     */
    public void setValorCampoAnterior(String valorCampoAnterior) {
        this.valorCampoAnterior = valorCampoAnterior;
    }

    /**
     * @return the valorCampoAlterado
     */
    public String getValorCampoAlterado() {
        if (valorCampoAlterado == null) {
            valorCampoAlterado = "";
        }
        if (valorCampoAlterado.equals("true")) {
            valorCampoAlterado = "Sim";
        }
        if (valorCampoAlterado.equals("false")) {
            valorCampoAlterado = "Não";
        }
        return valorCampoAlterado;
    }

    /**
     * @param valorCampoAlterado the valorCampoAlterado to set
     */
    public void setValorCampoAlterado(String valorCampoAlterado) {
        this.valorCampoAlterado = valorCampoAlterado;
    }

    /**
     * @return the dataAlteracao
     */
    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public String getDataHoraAlteracao_Apresentar() {
        if (dataAlteracao != null) {
            return Uteis.getData(dataAlteracao) + " ás "
                    + Uteis.gethoraHHMMSSAjustado(dataAlteracao);
        } else {
            return "";
        }
    }

    public String getDataAlteracao_Apresentar() {
        if (dataAlteracao != null) {
            return Uteis.getData(dataAlteracao);
        } else {
            return "";
        }
    }

    public String getHoraAlteracao_Apresentar() {
        if (dataAlteracao != null) {
            return Uteis.gethoraHHMMSSAjustado(dataAlteracao);
        } else {
            return "";
        }
    }

    /**
     * @param dataAlteracao the dataAlteracao to set
     */
    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }

    /**
     * @return the responsavelAlteracao
     */
    public String getResponsavelAlteracao() {
        if (responsavelAlteracao == null) {
            responsavelAlteracao = "";
        }
        return responsavelAlteracao;
    }

    public String getJustificativa() {
        if (!UteisValidacao.emptyString(valorCampoAlterado)) {
            try {
                return valorCampoAlterado.split("Justificativa: ")[1].split("\n\rResponsável")[0];
            }catch (Exception ignore){
                return "";
            }
        }
        return "";
    }

    /**
     * @param responsavelAlteracao the responsavelAlteracao to set
     */
    public void setResponsavelAlteracao(String responsavelAlteracao) {
        this.responsavelAlteracao = responsavelAlteracao;
    }

    /**
     * @return the operacao
     */
    public String getOperacao() {
        if (operacao == null) {
            operacao = "";
        }
        return operacao;
    }

    /**
     * @param operacao the operacao to set
     */
    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    /**
     * @return the chavePrimariaEntidadeSubordinada
     */
    public String getChavePrimariaEntidadeSubordinada() {
        if (chavePrimariaEntidadeSubordinada == null) {
            chavePrimariaEntidadeSubordinada = "";
        }
        return chavePrimariaEntidadeSubordinada;
    }

    /**
     * @param chavePrimariaEntidadeSubordinada the chavePrimariaEntidadeSubordinada to set
     */
    public void setChavePrimariaEntidadeSubordinada(String chavePrimariaEntidadeSubordinada) {
        this.chavePrimariaEntidadeSubordinada = chavePrimariaEntidadeSubordinada;
    }

    /**
     * @return the nomeEntidadeDescricao
     */
    public String getNomeEntidadeDescricao() {
        return nomeEntidadeDescricao;
    }

    /**
     * @param nomeEntidadeDescricao the nomeEntidadeDescricao to set
     */
    public void setNomeEntidadeDescricao(String nomeEntidadeDescricao) {
        this.nomeEntidadeDescricao = nomeEntidadeDescricao;
    }

    /**
     * @return O campo pessoa.
     */
    public Integer getPessoa() {
        return this.pessoa;
    }

    /**
     * @param pessoa O novo valor de pessoa.
     */
    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    /**
     * @return O campo descricao.
     */
    public String getDescricao() {
        return this.descricao;
    }

    /**
     * @param descricao O novo valor de descricao.
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    /**
     * Método usado no BI - Controle de operações de Exceção
     * para obter o nome do aluno que está no log
     * @return
     */
    public String getNomeExclusaoVisitantes() {
        //nome do aluno gravado no log - uso na tela de resumo para facilitar
        final String PREFIXO_NOME = "Nome = ";
        if (valorCampoAnterior.contains(PREFIXO_NOME)) {
            return valorCampoAnterior.substring(valorCampoAlterado.indexOf(PREFIXO_NOME) + PREFIXO_NOME.length());
        }
        if(valorCampoAlterado.contains(PREFIXO_NOME.replace("=", ":"))) {
            return valorCampoAnterior.substring(valorCampoAnterior.indexOf(PREFIXO_NOME) + PREFIXO_NOME.length());
        }
        if (!valorCampoAlterado.isEmpty()) {
            if (valorCampoAlterado.contains(PREFIXO_NOME)) {
                int indexNome = valorCampoAlterado.indexOf(PREFIXO_NOME) + 7;
                int indexAntesNome = valorCampoAlterado.indexOf("Data de Nasc. = ");
                if (indexNome >= 0 && indexAntesNome >= 0) {
                    return valorCampoAlterado.substring(indexNome, indexAntesNome);
                }
            } else if (valorCampoAlterado.contains("nome do aluno =")) {
                int indexNome = valorCampoAlterado.indexOf("nome do aluno =") + 16;
                if (valorCampoAlterado.contains("valor da parcela = ")) {
                    int indexAntesNome = valorCampoAlterado.indexOf("valor da parcela = ");
                    if (indexNome >= 0 && indexAntesNome >= 0) {
                        return valorCampoAlterado.substring(indexNome, indexAntesNome);
                    }
                } else {
                    return valorCampoAlterado.substring(indexNome);
                }
            }
        }

        return "";
    }

    public String getUserOAMD() {
        if (userOAMD != null && userOAMD != ""){
            return " ^ "+userOAMD;
        }
        return userOAMD;
    }

    public void setUserOAMD(String userOAMD) {
        if (userOAMD == null || "undefined".equals(userOAMD)) {
            this.userOAMD = "";
        } else {
            this.userOAMD = userOAMD;
        }
    }
    
    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public String getClienteNome() {
        return clienteNome;
    }

    public void setClienteNome(String clienteNome) {
        this.clienteNome = clienteNome;
    }


    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }
}
