package negocio.comuns.arquitetura;

import negocio.facade.jdbc.arquitetura.ControleAcesso;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.List;

/**
 * Classe reponsável por manter um registro de todos os módulos do sistema,
 * juntamente com suas respectivas entidades. Estas informações são úteis para
 * permitir a configuração de um menu dinâmico apartir de um perfil de acesso
 * para um grupo de usuários.
 *
 * <AUTHOR>
 */
public class PermissaoAcessoMenuVO implements Serializable {

    //Pacote basico
    private Boolean categoria;
    private Boolean cidade;
    private Boolean classificacao;
    private Boolean cliente;
    private Boolean colaborador;
    private Boolean configuracaoSistema;
    private Boolean empresa;
    private Boolean grupo;
    private Boolean movimentoContaCorrenteCliente;
    private Boolean selecionarColaboradorMetas;
    private Boolean pais;
    private Boolean parentesco;
    private Boolean perguntaCliente;
    private Boolean pergunta;
    private Boolean profissao;
    private Boolean grauInstrucao;
    private Boolean questionario;
    private Boolean pesquisa;
    private Boolean alterarMatricula;
    private Boolean conviteAulaExperimental;
    private Boolean saldoCreditosClientes;
    private Boolean campanhaCupomDesconto;
    private Boolean indiceFinanceiroReajustePreco;
    private Boolean sorteio;
    private Boolean operacoesColetivas;
    private Boolean alterarIdVindi;
    private Boolean mostrarAbaNotaFiscaisPerfilAluno;
    private Boolean alunoAbaNotaFiscal;
    private Boolean permiteAlterarRPS;
    //Pacote Contrato
    private Boolean convenioDesconto;
    private Boolean movProduto;
    private Boolean justificativaOperacao;
    private Boolean permiteEditarValorParcelaNegociacao;
    private Boolean configurarProdutosEspecificos;
    private Boolean permiteAlterarLancamentoContratosIguais;
    private Boolean permiteConferirNegociacaoSemInformarCartao;
    //Pacote Financeiro
    private Boolean banco;
    private Boolean contaCorrente;
    private Boolean convenioCobranca;
    private Boolean formaPagamento;
    private Boolean movPagamento;
    private Boolean movParcela;
    private Boolean tipoRemessa;
    private Boolean tipoRetorno;
    private Boolean vendaAvulsa;
    private Boolean vendaRapida;
    private Boolean estornoRecibo;
    private Boolean vendaConsumidor;
    private Boolean operadoraCartao;
    private Boolean cupomFiscal;
    private Boolean gestaoTransacoes;
    private Boolean gestaoRemessas;
    private Boolean gestaoBoletosOnline;
    private Boolean gestaoComissao;
    private Boolean taxasComissao;
    private Boolean comissaoVariavel;
    private Boolean gestaoNegativacoes;
    private Boolean gestaoNotas;
    private Boolean gestaoNFCe;
    private Boolean boletosSistema;
    private Boolean notificacaoExpiracaoSistema;
    private Boolean configuracaoFinanceiro;
    private Boolean modeloOrcamento;
    //Pacote Plano
    private Boolean ambiente;
    private Boolean tipoAmbiente;
    private Boolean categoriaProduto;
    private Boolean composicao;
    private Boolean condicaoPagamento;
    private Boolean consultaTurma;
    private Boolean relatorioFrequenciaTurma;
    private Boolean desconto;
    private Boolean duracao;
    private Boolean horario;
    //    private Boolean horarioTurma;
    //    private Boolean imprimirContrato;
    private Boolean modalidade;
    private Boolean tipoModalidade;
    private Boolean nivelTurma;
    private Boolean plano;
    private Boolean planoTipo;
    //COMO IRA FUNCIONAR
    //    private Boolean vezesSemana;
    private Boolean planoTextoPadrao;
    private Boolean produto;
    //    private Boolean produtoSugerido;
    private Boolean turma;
    private Boolean visualizarContrato;
    //Pacote Arquitetura
    private Boolean perfilAcesso;
    private Boolean perfilAcessoUnificado;
    private Boolean perfilAcessoUnificadoSincronizado;
    private Boolean usuario;
    private Boolean log;
    private Boolean localAcesso;
    private Boolean geradorConsultas;
    private Boolean servidorFacial;
    private Boolean importacao;
    private Boolean importacaoProduto;
    private Boolean importacaoColaborador;
    private Boolean importacaoFornecedor;
    private Boolean importacaoConta;
    private Boolean importacaoAlunoTurma;
    private Boolean importacaoTurma;
    //Pacote Relatório
    private Boolean listaChamada;
    private Boolean caixaPorOperadorRel;

    private Boolean frequenciaOcupacaoTurmasRel;
    private Boolean competenciaMensalRel;
    private Boolean faturamentoSinteticoRel;
    private Boolean receitaPorPeriodoSinteticoRel;
    private Boolean parcelaEmAbertoRel;
    private Boolean relatorioParcelasConsolidado;
    private Boolean saldoContaCorrenteRel;
    private Boolean totalizadorFrequenciaRel;
    private Boolean clientePorDuracaoRel;
    private Boolean clienteRel;
    private Boolean gameOfResults;
    private Boolean indiceRenovacao;
    private Boolean business;
    private Boolean indiceConversao;
    private Boolean rotatividade;
    private Boolean pendenciaCliente;
    private Boolean biAulaExperimental;
    private Boolean relatorioAlteracaoDataBaseContrato;
    private Boolean contratoRecorrencia;
    private Boolean visualizarBI;
    private Boolean caixaEmAberto;
    private Boolean aulaAvulsaDiaria;
    private Boolean consultorVendaAvulsa;
    private Boolean apresentarLinkCadastrarCartaoOnline;
    private Boolean vendaRapidaTelaPadraoLancarContrato;
    private Boolean permissaoFreePass;
    private Boolean permissaoTransferirClienteEmpresa;
    private Boolean fecharNegociacaoContrato;
    private Boolean enviarSMSSocialMailing;
    private Boolean permissaoAcessarSocialMailing;
    private Boolean geralClientes;
    private Boolean relatorioClientes;
    private Boolean mapaEstatistico;
    private Boolean incluirAutorizacaoAGE;
    private Boolean mapaTurma;
    private Boolean gestaoPersonal;
    private Boolean relatorioPersonal;
    private Boolean gestaoArmario;
    private Boolean gestaoTurma;
    private Boolean relatorioBVs;
    private Boolean relatorioClientesComAtestado;
    private Boolean relatorioClientesComBonus;
    private Boolean relatorioClientesTrancados;
    private Boolean relatorioDescontoPorOcupacao;
    private Boolean relatorioFechamentoDeAcessos;
    private Boolean relatorioIndicadorDeAcessos;
    private Boolean relatorioDeRepasse;
    private Boolean relatorioDeVisitantes;
    private Boolean relatorioDePedidosPinpad;
    private Boolean relatorioDeTotalizadorDeTickets;
    private Boolean relatorioDeTransacoesPix;
    private Boolean relatorioDeCliente;
    private Boolean relatorioDeConsultaRecibo;
    private Boolean relatorioDeArmarios;
    private Boolean relatorioDePrevisaoRenovacao;
    private Boolean relatorioDeFaturamentoRecebidoPeríodo;
    private Boolean relatorioDeMovimentacaoContaCorrenteCliente;
    private Boolean relatorioDeProdutosComVigencia;
    private Boolean relatorioDeClientesCancelados;
    private Boolean relatorioDeTotalizadorDeAcessos;
    private Boolean permitirImprimirReciboEmBranco;
    private Boolean permitirExportarDados;
    private Boolean apresentarNfseRecibo;




    //CRM
    private Boolean moduloCRM;
    private Boolean modeloMensagem;
    private Boolean malaDireta;
    private Boolean configuracaoSistemaCRM;
    private Boolean definirLayout;
    private Boolean indiceRenovacaoCRM;
    private Boolean indiceConversaoCRM;
    private Boolean rotatividadeCRM;
    private Boolean pendenciaClienteCRM;
    private Boolean grupoColaborador;
    private Boolean organizadorCarteira;
    private Boolean passivo;
    private Boolean indicacao;
    private Boolean feriado;
    private Boolean agenda;
    private Boolean aberturaMeta;
    private Boolean evento;
    private Boolean indicadorVenda;
    private Boolean indicadorRetencao;
    private Boolean script;
    private Boolean relatorioAgendamentos;
    private Boolean relatorioContatosAPP;
    //pedro
    //    carlos
    private Boolean objecao;
    private Boolean visualizarMeta;
    private Boolean historicoContato;
    private Boolean realizarContato;
    private Boolean metaAgendamento;
    private Boolean metaLigacaoAgendamentoAmanha;
    private Boolean metaVinteQuatroHoras;
    private Boolean metaRenovacao;
    private Boolean metaPosVenda;
    private Boolean metaFaturamento;
    private Boolean metaQtdeVenda;
    private Boolean metaIndicado;
    private Boolean metaPassivo;
    private Boolean metaGrupoRisco;
    private Boolean metaPerda;
    private Boolean metaAniversariante;
    private Boolean metaFaltosos;
    private Boolean metaVencidos;
    private Boolean metaConversaoAgendados;
    private Boolean metaExAlunos;
    private Boolean metaConversaoExAlunos;
    private Boolean metaVisitantesAntigos;
    private Boolean todosGruposColaboradoresCRM;
    private Boolean somenteSeuGrupoColaboradorCRM;
    private Boolean crmExtraCRM;
    private Boolean crmQuarentena;
    private Boolean crmEditarSimplesRegistro;
    private Boolean totalizadorMeta;
    private Boolean pesquisarPeriodoCRM;
    private Boolean pesquisarMetaPassadaCRM;
    private Boolean businessIntelligenceCRM;
    /* ------------------   INÍCIO - CENTRAL DE EVENTOS   ------------------- */
    private Boolean moduloCE;
    //funcionalidades
    private Boolean perfilEvento;
    private Boolean cadastroInicial;
    private Boolean consultaConversas;
    private Boolean orcamentoDetalhado;
    private Boolean consultaDisponibilidades;
    private Boolean agendaVisita;
    private Boolean listaProspects;
    private Boolean pesquisaGeral;
    private Boolean conversa;
    private Boolean alterarValorManutencaoModalidade;
    private Boolean campanhaDuracao;

    //entidades
    private Boolean produtoLocacao;
    /* ------------------     FIM - CENTRAL DE EVENTOS    ------------------- */

 /*-------------------Financeiro----------------------------*/
    private Boolean centroCustos;
    private Boolean bloqueioCaixa;
    private Boolean pessoa;
    private Boolean conta;
    private Boolean tipoConta;
    private Boolean tipoDocumento;
    private Boolean fornecedor;
    private Boolean lancarContasPagar;
    private Boolean quitarContasPagar;
    private Boolean visualizarLancamentos;
    private Boolean gestaoRecebiveis;
    private Boolean gestaoLotes;
    private Boolean resumoContas;
    private Boolean planoContas;
    private Boolean lancamentosAvulsos;
    private Boolean rateioIntegracao;
    private Boolean cadastroMetas;
    private Boolean historicoMetas;
    private Boolean lancarVisualizarLancamentosEmpresas;
    private Boolean visualizaRMetaFinanceiraEmpresaBI;
    private Boolean visualizarMetasFinanceirasTodasEmpresas;
    private Boolean abrirConsultarHistCaixaAdmTodasEmpresas;
    private Boolean consultarHistCaixaAdm;
    private Boolean abrirCaixaAdm;
    private Boolean fecharCaixaOutroUsuario;
    private Boolean conciliarSaldo;
    private Boolean biFinanceiro;
    private Boolean fluxoCaixa;
    /**
     * SMARTBOX
     */
    private Boolean telaInicialSmartBox = false;
    private Boolean ******************************** = false;

    // Início Permissões para o controle de estoque.
    private Boolean cadastrarCompra;
    private Boolean cancelarCompra;
    private Boolean cadastrarBalanco;
    private Boolean cancelarBalanco;
    private Boolean visualizarCardex;
    private Boolean alterarTipoProduto;
    private Boolean configurarProdutoEstoque;
    private Boolean alterarSituacaoProdutoEstoque;
    private Boolean visualizarPosicaoEstoque;

    private Boolean lancamentoProdutoColetivo;

    private Boolean ticketMedio;
    private Boolean conviteBI;
    // Fim Permissões para o controle de estoque.

    private Boolean tamanhoArmario;

    private Boolean adicionarAlterarSenhaAcesso;
    private Boolean totalClienteConsulta;

    private Boolean ativarVerificacaoClientesAtivos;
    private Boolean verificarClientesAtivos;

    private Boolean lancarMensagemObservacaoGeral;
    private Boolean gympass;
    private Boolean permiteVisualizaGymPassPeriodo;
    private Boolean permiteVisualizaGogoodPeriodo;
    private Boolean permiteVisualizaConvitesPeriodo;

    private Boolean permiteRegistrarAcessoAvulso;

    private Boolean permiteDownloadRemessa;
    private Boolean permiteBloquearDesbloquearClienteCobrancaAutomatica;
    private Boolean permiteRelatorioClientesCobrancaBloqueada;
    private Boolean permiteVisualizaSMDPeriodo;

    //Estúdio
    private Boolean agendaMensal;
    private Boolean agendaAmbiente;
    private Boolean agendaProfissional;
    private Boolean agendaIndividual;

    private Boolean brinde;

    private Boolean historicoPontos;
    
    private Boolean aniversariantes;
    private Boolean biInadimplencia;
    private Boolean vendasOnline;
    private Boolean permiteAlterarCPFDotz;
    private Boolean configurarClubeDeVantagens;
    private Boolean excluirTokemGympass;

    //MODULO NOTA FISCAL
    private Boolean moduloNotaFiscal;
    private Boolean permiteCancelarNotaFiscal;
    private Boolean permiteInutilizarNotaFiscal;
    private Boolean permiteExcluirNotaFiscal;

    private Boolean biOperacaoExececoes;
    private Boolean biGestaoAcesso;
    private Boolean biCobrancaConvenio;
    private Boolean biVerificarcaoClientes;
    private Boolean biGymPass;
    private Boolean biCicloDeVida;


    private Boolean moduloPactoPay;

    private Boolean desvincularUsuarioAcademia;
    private Boolean permiteAlterarStatusNotaFiscal;

    // TREINO
    private Boolean treinoBI;
    private Boolean treinoBiPersonalizado;
    private Boolean treinoEmCasa;
    private Boolean treinoAcompanhamentoPersonal;
    private Boolean treinoColaboradores;
    private Boolean treinoGestaoCreditos;
    private Boolean treinoPersonais;
    private Boolean ************************;
    private Boolean treinoRelatorioAplicativosAtivos;
    private Boolean treinoRelatorioGestaoCreditos;
    private Boolean treinoRelatorioIndicadoresCarteiraProfessores;
    private Boolean treinoRelatorioIndicadoresAtividadesProfessores;
    private Boolean treinoRelatorioProfessoresAlunosAvisosMedicos;
    private Boolean treinoRelatorioProfessoresSubstitutos;
    private Boolean treinoRelatorioRanking;
    private Boolean treinoCadastroAparelhos;
    private Boolean treinoCadastroAtividades;
    private Boolean treinoCadastroCategoriaAtividades;
    private Boolean treinoCadastroNiveis;
    private Boolean treinoCadastroFichasPredefinidas;
    private Boolean treinoCadastroProgramasPredefinidos;
    private Boolean treinoPrescricaoTreino;
    private Boolean treinoCadastroAlunos;
    private Boolean treinoCadastroColaboradoresTreino;
    private Boolean treinoUsuarios;
    private Boolean treinoPerfilAcesso;

    // Avaliação fisica
    private Boolean avaliacaoFisicaBI;
    private Boolean avaliacaoFisicaCadastroAnamnese;
    private Boolean avaliacaoFisicaCadastroObjetivos;

    // PactoPay
    private Boolean pactoPayBI;
    private Boolean pactoPayCartaoCredito;
    private Boolean pactoPayParcelasEmAberto;
    private Boolean pactoPayCreditoOnline;
    private Boolean pactoPayPix;
    private Boolean pactoPayReguaCobranca;
    private Boolean pactoPayReguaCobrancaDashboard;
    private Boolean pactoPayReguaCobrancaConfigEmail;

    // Cross
    private Boolean crossBi;
    private Boolean crossWod;
    private Boolean crossMonitor;
    private Boolean crossCadastroAparelhos;
    private Boolean crossCadastroAtividades;
    private Boolean crossCadastroBenchmarks;
    private Boolean crossCadastroTiposBenchmark;
    private Boolean crossCadastroTipoWod;

    // Graduação
    private Boolean graduacaoAvaliacoesProgresso;
    private Boolean graduacaoAtividades;
    private Boolean graduacaoFichaTenica;

    // Agenda
    private Boolean agendaBi;
    private Boolean agendaTvAula;
    private Boolean agendaTvGestor;
    private Boolean agendaAulas;
    private Boolean agendaServicos;
    private Boolean agendaModalidade;
    private Boolean agendaIndicadores;
    private Boolean agendaAulaExcluida;
    private Boolean agendaConfigurarAula;
    private Boolean agendaTipoAgendamento;

    private Boolean visualizarCPFBusca;
    private Boolean verPendenciasClienteListaAcessos;
    private Boolean permitirAvisosInternos;
    private Boolean permitirAtualizacaoContratoAssinado;
    private Boolean impostoProduto;

    public PermissaoAcessoMenuVO() {
        inicializarDados();
    }

    private void inicializarDados() {
        setVendasOnline(false);
        setCategoria(false);
        setConviteAulaExperimental(false);
        setSaldoCreditosClientes(false);
        setCampanhaCupomDesconto(false);
        setIndiceFinanceiroReajustePreco(false);
        setSorteio(false);
        setOperacoesColetivas(false);
        setAlterarIdVindi(false);
        setMostrarAbaNotaFiscaisPerfilAluno(false);
        setAlunoAbaNotaFiscal(false);
        setCidade(false);
        setClassificacao(false);
        setCliente(false);
        setColaborador(false);
        setConfiguracaoSistema(false);
        setEmpresa(false);
        setGrupo(false);
        //setGestaoRemessas(false);
        setGestaoBoletosOnline(false);
        setMovimentoContaCorrenteCliente(false);
        setSelecionarColaboradorMetas(false);
        setPermiteAlterarRPS(false);
        setPais(false);
        setParentesco(false);
        setPerguntaCliente(false);
        setPergunta(false);
        setProfissao(false);
        setGrauInstrucao(false);
        setQuestionario(false);
        setPesquisa(false);
        setGameOfResults(false);
        setAlterarMatricula(false);
        setConvenioDesconto(false);
        setMovProduto(false);
        setBanco(false);
        setCupomFiscal(false);
        setContaCorrente(false);
        setConvenioCobranca(false);
        setFormaPagamento(false);
        setMovPagamento(false);
        setMovParcela(false);
        setTipoRemessa(false);
        setTipoRetorno(false);
        setVendaAvulsa(false);
        setVendaRapida(false);
        setAmbiente(false);
        setCategoriaProduto(false);
        setComposicao(false);
        setCondicaoPagamento(false);
        setDesconto(false);
        setDuracao(false);
        setHorario(false);
        setModalidade(false);
        setTipoModalidade(false);
        setNivelTurma(false);
        setPlano(false);
        setPlanoTipo(false);
        setPlanoTextoPadrao(false);
        setProduto(false);
        setTurma(false);
        setVisualizarContrato(false);
        setPerfilAcesso(false);
        setUsuario(false);
        setDesvincularUsuarioAcademia(false);
        setLocalAcesso(false);
        setGeradorConsultas(false);
        setServidorFacial(false);
        setImportacao(false);
        setImportacaoProduto(false);
        setImportacaoColaborador(false);
        setImportacaoFornecedor(false);
        setImportacaoConta(false);
        setImportacaoTurma(false);
        setLog(false);
        setListaChamada(false);
        setCaixaPorOperadorRel(false);
        setFrequenciaOcupacaoTurmasRel(false);
        setJustificativaOperacao(false);
        setPermiteEditarValorParcelaNegociacao(false);
        setConfigurarProdutosEspecificos(false);
        setPermiteAlterarLancamentoContratosIguais(false);
        setPermiteConferirNegociacaoSemInformarCartao(false);
        setCompetenciaMensalRel(false);
        setFaturamentoSinteticoRel(false);
        setReceitaPorPeriodoSinteticoRel(false);
        setParcelaEmAbertoRel(false);
        setRelatorioParcelasConsolidado(false);
        setSaldoContaCorrenteRel(false);
        setTotalizadorFrequenciaRel(false);
        setCliente(false);
        setClientePorDuracaoRel(false);
        setIndiceConversao(false);
        setIndiceRenovacao(false);
        setBiAulaExperimental(false);
        setRotatividade(false);
        setBusiness(false);
        setVisualizarBI(false);
        setCaixaEmAberto(false);
        setAulaAvulsaDiaria(false);
        setConsultorVendaAvulsa(false);
        setApresentarLinkCadastrarCartaoOnline(false);
        setVendaRapidaTelaPadraoLancarContrato(false);
        setPendenciaCliente(false);
        setClienteRel(false);
        setOperadoraCartao(false);
        setEstornoRecibo(false);
        setVendaConsumidor(false);
        setRelatorioAlteracaoDataBaseContrato(false);
        setPermissaoFreePass(false);
        setPermissaoTransferirClienteEmpresa(false);
        setFecharNegociacaoContrato(false);
        setEnviarSMSSocialMailing(false);
        setPermissaoAcessarSocialMailing(false);
        setGeralClientes(false);
        setRelatorioClientes(false);
        setMapaEstatistico(false);
        setPessoa(false);
//        if (ControleAcesso.verificarPermissaoCRM()) {
        setModuloCRM(false);
        setDefinirLayout(false);
        setGrupoColaborador(false);
        setIndiceRenovacaoCRM(false);
        setConfiguracaoSistemaCRM(false);
        setModeloMensagem(false);
        setMalaDireta(false);
        setIndiceConversaoCRM(false);
        setRotatividadeCRM(false);
        setPendenciaClienteCRM(false);
        setOrganizadorCarteira(false);
        setPassivo(false);
        setIndicacao(false);
        setFeriado(false);
        setAgenda(false);
        setAberturaMeta(false);
        setEvento(false);
        setScript(false);
        setRelatorioAgendamentos(false);
        setRelatorioContatosAPP(false);
        setIndicadorRetencao(false);
        setIndicadorVenda(false);
        setObjecao(false);
        setVisualizarMeta(false);
        setHistoricoContato(false);
        setRealizarContato(false);
        setMetaAgendamento(false);
        setMetaAniversariante(false);
        setMetaFaltosos(false);
        setMetaFaturamento(false);
        setMetaGrupoRisco(false);
        setMetaIndicado(false);
        setMetaPassivo(false);
        setMetaPerda(false);
        setMetaPosVenda(false);
        setMetaQtdeVenda(false);
        setMetaRenovacao(false);
        setMetaVinteQuatroHoras(false);
        setMetaVencidos(false);
        setMetaConversaoAgendados(false);
        setMetaLigacaoAgendamentoAmanha(false);
        setMetaExAlunos(false);
        setMetaConversaoExAlunos(false);
        setMetaVisitantesAntigos(false);
        setPermiteVisualizaGymPassPeriodo(false);
        setPermiteVisualizaGogoodPeriodo(false);
        setPermiteVisualizaConvitesPeriodo(false);
        setPermiteVisualizaSMDPeriodo(false);
        setPermiteRegistrarAcessoAvulso(false);
        setPermiteDownloadRemessa(false);
        setPermiteBloquearDesbloquearClienteCobrancaAutomatica(false);
        setPermiteRelatorioClientesCobrancaBloqueada(false);
        setConsultaTurma(false);
        setRelatorioFrequenciaTurma();
        setIncluirAutorizacaoAGE(false);
        setMapaTurma(false);
        setGestaoPersonal(false);
        setRelatorioPersonal(false);
        setGestaoArmario(false);
        setGestaoTurma(false);
//        }
        /* ------------------   INÍCIO - CENTRAL DE EVENTOS   ------------------- */
        setModuloCE(false);
        setPerfilEvento(false);
        setCadastroInicial(false);
        setOrcamentoDetalhado(false);
        setConsultaDisponibilidades(false);
        setAgendaVisita(false);
        setListaProspects(false);
        setPesquisaGeral(false);
        setConversa(false);
        setProdutoLocacao(false);
        /* ------------------     FIM - CENTRAL DE EVENTOS    ------------------- */
 /*---------Financeiro---------*/
        setConta(false);
        setTipoConta(false);
        setTipoDocumento(false);
        setFornecedor(false);
        setLancarContasPagar(false);
        setQuitarContasPagar(false);
        setVisualizarLancamentos(false);
        setGestaoRecebiveis(false);
        setGestaoLotes(false);
        setResumoContas(false);
        setPlanoContas(false);
        setLancamentosAvulsos(false);
        setRateioIntegracao(false);
        setHistoricoMetas(false);
        setLancarVisualizarLancamentosEmpresas(false);
        setCadastroMetas(false);
        setVisualizaRMetaFinanceiraEmpresaBI(false);
        setVisualizarMetasFinanceirasTodasEmpresas(false);
        setAbrirCaixaAdm(false);
        setConsultarHistCaixaAdm(false);
        setAbrirConsultarHistCaixaAdmTodasEmpresas(false);
        setFecharCaixaOutroUsuario(false);
        setConciliarSaldo(false);
        setBIFinanceiro(false);
        setFluxoCaixa(false);

        setTaxasComissao(false);
        setComissaoVariavel(false);
        setGestaoNegativacoes(false);
        setGestaoNotas(false);
        setGestaoNFCe(false);
        setBoletosSistema(false);
        setNotificacaoExpiracaoSistema(false);
        setConfiguracaoFinanceiro(false);
        setModeloOrcamento(false);
        // Inicio controle de estoque
        setCadastrarCompra(false);
        setCancelarCompra(false);
        setCadastrarBalanco(false);
        setCancelarBalanco(false);
        setVisualizarCardex(false);
        //setAlterarTipoProduto(false);
        setConfigurarProdutoEstoque(false);
        setAlterarSituacaoProdutoEstoque(false);
        setVisualizarPosicaoEstoque(false);
        // fim  controle de estoque

        setLancamentoProdutoColetivo(false);
        setTicketMedio(false);
        setConviteBI(false);

        setAlterarValorManutencaoModalidade(false);
        setTamanhoArmario(false);
        setTodosGruposColaboradoresCRM(false);
        setSomenteSeuGrupoColaboradorCRM(false);
        setCrmExtraCRM(false);
        setCrmQuarentena(false);
        setCrmEditarSimplesRegistro(false);
        setPesquisarPeriodoCRM(false);
        setPesquisarMetaPassadaCRM(false);
        setBusinessIntelligenceCRM(false);
        setAdicionarAlterarSenhaAcesso(false);
        setTotalClienteConsulta(false);

        setAtivarVerificacaoClientesAtivos(false);
        setVerificarClientesAtivos(false);

        setLancarMensagemObservacaoGeral(false);
        /* ------------------   INÍCIO - ESTÚDIO   ------------------- */
        setAgendaMensal(false);
        setAgendaAmbiente(false);
        setAgendaProfissional(false);
        setAgendaIndividual(false);
        /* ------------------     FIM - ESTÚDIO    ------------------- */
        setGympass(false);
        setBrinde(false);
        setHistoricoPontos(false);
        
        setAniversariantes(false);
        setBiInadimplencia(false);
        setPermiteAlterarCPFDotz(false);
        setConfigurarClubeDeVantagens(false);
        setExcluirTokemGympass(false);

        setModuloNotaFiscal(false);
        setPermiteCancelarNotaFiscal(false);
        setPermiteInutilizarNotaFiscal(false);
        setPermiteExcluirNotaFiscal(false);
        setPermiteAlterarStatusNotaFiscal(false);
        setBiOperacaoExececoes(false);
        setBiCobrancaConvenio(false);
        setBiGestaoAcesso(false);
        setBiVerificarcaoClientes(false);
        setBiCicloDeVida(false);
        setBiGymPass(false);
        setRelatorioBVs(false);
        setRelatorioClientesComAtestado(false);
        setRelatorioClientesComBonus(false);
        setRelatorioClientesTrancados(false);
        setRelatorioDescontoPorOcupacao(false);
        setRelatorioFechamentoDeAcessos(false);
        setRelatorioIndicadorDeAcessos(false);
        setRelatorioDeRepasse(false);
        setRelatorioDeVisitantes(false);
        setRelatorioDePedidosPinpad(false);
        setRelatorioDeTotalizadorDeTickets(false);
        setRelatorioDeTransacoesPix(false);
        setRelatorioDeCliente(false);
        setRelatorioDeClientesCancelados(false);
        setRelatorioDeConsultaRecibo(false);
        setRelatorioDeArmarios(false);
        setRelatorioDePrevisaoRenovacao(false);
        setRelatorioDeFaturamentoRecebidoPeríodo(false);
        setRelatorioDeMovimentacaoContaCorrenteCliente(false);
        setRelatorioDeProdutosComVigencia(false);
        setRelatorioDeTotalizadorDeAcessos(false);
        setPermitirImprimirReciboEmBranco(false);
        setPermitirExportarDados(false);
        setApresentarNfseRecibo(false);


        setModuloPactoPay(false);

        // TREINO
        setTreinoBI(false);
        setTreinoBiPersonalizado(false);
        setTreinoEmCasa(false);
        setTreinoAcompanhamentoPersonal(false);
        setTreinoColaboradores(false);
        setTreinoGestaoCreditos(false);
        setTreinoPersonais(false);
        setTreinoRelatorioAndamento(false);
        setTreinoRelatorioAplicativosAtivos(false);
        setTreinoRelatorioGestaoCreditos(false);
        setTreinoRelatorioIndicadoresCarteiraProfessores(false);
        setTreinoRelatorioIndicadoresAtividadesProfessores(false);
        setTreinoRelatorioProfessoresAlunosAvisosMedicos(false);
        setTreinoRelatorioProfessoresSubstitutos(false);
        setTreinoRelatorioRanking(false);
        setTreinoCadastroAparelhos(false);
        setTreinoCadastroAtividades(false);
        setTreinoCadastroCategoriaAtividades(false);
        setTreinoCadastroNiveis(false);
        setTreinoCadastroFichasPredefinidas(false);
        setTreinoCadastroProgramasPredefinidos(false);
        setTreinoPrescricaoTreino(false);
        setTreinoCadastroAlunos(true);
        setTreinoCadastroColaboradoresTreino(false);
        setTreinoUsuarios(true);

        // Avaliação Fisica
        setAvaliacaoFisicaBI(false);
        setAvaliacaoFisicaCadastroObjetivos(false);
        setAvaliacaoFisicaCadastroAnamnese(false);

        // Pacto Pay
        setPactoPayBI(false);
        setPactoPayCartaoCredito(false);
        setPactoPayParcelasEmAberto(false);
        setPactoPayCreditoOnline(false);
        setPactoPayPix(false);
        setPactoPayReguaCobranca(false);
        setPactoPayReguaCobrancaDashboard(false);
        setPactoPayReguaCobrancaConfigEmail(false);

        // Cross
        setCrossBi(false);
        setCrossWod(false);
        setCrossMonitor(false);
        setCrossCadastroAparelhos(false);
        setCrossCadastroAtividades(false);
        setCrossCadastroBenchmarks(false);
        setCrossCadastroTiposBenchmark(false);
        setCrossCadastroTipoWod(false);

        // Graduação
        setGraduacaoAvaliacoesProgresso(false);
        setGraduacaoFichaTenica(false);
        setGraduacaoAtividades(false);

        // Agenda
        setAgendaBi(false);
        setAgendaTvAula(false);
        setAgendaTvGestor(false);
        setAgendaAulas(false);
        setAgendaServicos(false);
        setAgendaModalidade(false);
        setAgendaIndicadores(false);
        setAgendaAulaExcluida(false);
        setAgendaConfigurarAula(false);
        setAgendaTipoAgendamento(false);

        setVisualizarCPFBusca(false);
        setVerPendenciasClienteListaAcessos(false);
        setPermitirAvisosInternos(false);
        setPermitirAtualizacaoContratoAssinado(false);
        setImpostoProduto(false);
    }

    private void inicializarDadosAdministrador() {
        setVendasOnline(true);
        setCategoria(true);
        setGameOfResults(true);
        setCidade(true);
        setConviteAulaExperimental(true);
        setCampanhaCupomDesconto(true);
        setIndiceFinanceiroReajustePreco(true);
        setSorteio(true);
        setOperacoesColetivas(true);
        setAlterarIdVindi(true);
        setMostrarAbaNotaFiscaisPerfilAluno(true);
        setAlunoAbaNotaFiscal(true);
        setClassificacao(true);
        setGestaoRemessas(true);
        setGestaoBoletosOnline(true);
        setCliente(true);
        setColaborador(true);
        setConfiguracaoSistema(true);
        setEmpresa(true);
        setGrupo(true);
        setMovimentoContaCorrenteCliente(true);
        setSelecionarColaboradorMetas(true);
        setPermiteAlterarRPS(true);
        setPais(true);
        setParentesco(true);
        setPerguntaCliente(true);
        setPergunta(true);
        setProfissao(true);
        setGrauInstrucao(true);
        setQuestionario(true);
        setPesquisa(true);
        setAlterarMatricula(true);
        setConvenioDesconto(true);
        setMovProduto(true);
        setBanco(true);
        setCupomFiscal(true);
        setContaCorrente(true);
        setConvenioCobranca(true);
        setFormaPagamento(true);
        setMovPagamento(true);
        setMovParcela(true);
        setTipoRemessa(true);
        setTipoRetorno(true);
        setVendaAvulsa(true);
        setVendaRapida(true);
        setAmbiente(true);
        setCategoriaProduto(true);
        setComposicao(true);
        setCondicaoPagamento(true);
        setDesconto(true);
        setDuracao(true);
        setHorario(true);
        setModalidade(true);
        setTipoModalidade(true);
        setNivelTurma(true);
        setPlano(true);
        setPlanoTipo(true);
        setPlanoTextoPadrao(true);
        setProduto(true);
        setTurma(true);
        setVisualizarContrato(true);
        setPerfilAcesso(true);
        setPerfilAcessoUnificado(true);
        setUsuario(true);
        setDesvincularUsuarioAcademia(true);
        setLog(true);
        setLocalAcesso(true);
        setGeradorConsultas(true);
        setServidorFacial(true);
        setImportacao(true);
        setImportacaoProduto(true);
        setImportacaoColaborador(true);
        setImportacaoFornecedor(true);
        setImportacaoConta(true);
        setImportacaoTurma(true);
        setListaChamada(true);
        setCaixaPorOperadorRel(true);
        setFrequenciaOcupacaoTurmasRel(true);
        setJustificativaOperacao(true);
        setPermiteEditarValorParcelaNegociacao(true);
        setConfigurarProdutosEspecificos(true);
        setPermiteAlterarLancamentoContratosIguais(true);
        setPermiteConferirNegociacaoSemInformarCartao(true);
        setCompetenciaMensalRel(true);
        setFaturamentoSinteticoRel(true);
        setReceitaPorPeriodoSinteticoRel(true);
        setParcelaEmAbertoRel(true);
        setRelatorioParcelasConsolidado(true);
        setSaldoContaCorrenteRel(true);
        setTotalizadorFrequenciaRel(true);
        setCliente(true);
        setClientePorDuracaoRel(true);
        setIndiceConversao(true);
        setIndiceRenovacao(true);
        setBiAulaExperimental(true);
        setRotatividade(true);
        setBusiness(true);
        setVisualizarBI(true);
        setCaixaEmAberto(true);
        setAulaAvulsaDiaria(true);
        setConsultorVendaAvulsa(true);
        setApresentarLinkCadastrarCartaoOnline(true);
        setVendaRapidaTelaPadraoLancarContrato(true);
        setPermissaoFreePass(true);
        setPermissaoTransferirClienteEmpresa(true);
        setFecharNegociacaoContrato(true);
        setEnviarSMSSocialMailing(true);
        setPermissaoAcessarSocialMailing(true);
        setGeralClientes(true);
        setRelatorioClientes(true);
        setMapaEstatistico(true);
        setPendenciaCliente(true);
        setClienteRel(true);
        setSaldoCreditosClientes(true);
        setEstornoRecibo(true);
        setVendaConsumidor(true);
        setOperadoraCartao(true);
        setGestaoTransacoes(true);
        setRelatorioAlteracaoDataBaseContrato(true);
        setContratoRecorrencia(true);
        setGestaoComissao(true);
        setTaxasComissao(true);
        setComissaoVariavel(true);
        setGestaoNegativacoes(true);
        setGestaoNotas(true);
        setGestaoNFCe(true);
        setBoletosSistema(true);
        setNotificacaoExpiracaoSistema(true);
        setConfiguracaoFinanceiro(true);
        setModeloOrcamento(true);
        setPermiteVisualizaGymPassPeriodo(true);
        setPermiteVisualizaGogoodPeriodo(true);
        setPermiteVisualizaConvitesPeriodo(true);
        setPermiteRegistrarAcessoAvulso(true);
        setPermiteDownloadRemessa(true);
        setPermiteBloquearDesbloquearClienteCobrancaAutomatica(true);
        setPermiteRelatorioClientesCobrancaBloqueada(true);
        setConsultaTurma(true);
        setRelatorioFrequenciaTurma();
        setIncluirAutorizacaoAGE(true);
        setMapaTurma(true);
        setGestaoPersonal(true);
        setRelatorioPersonal(true);
        setGestaoArmario(true);
        setGestaoTurma(true);
        setPessoa(true);
        setPermiteVisualizaSMDPeriodo(true);

        if (ControleAcesso.verificarPermissaoCRM()) {
            setModuloCRM(true);
            setDefinirLayout(true);
            setIndiceRenovacaoCRM(true);
            setConfiguracaoSistemaCRM(true);
            setModeloMensagem(true);
            setMalaDireta(true);
            setIndiceConversaoCRM(true);
            setRotatividadeCRM(true);
            setPendenciaClienteCRM(true);
            setGrupoColaborador(true);
            setOrganizadorCarteira(true);
            setPassivo(true);
            setIndicacao(true);
            setFeriado(true);
            setAgenda(true);
            setAberturaMeta(true);
            setEvento(true);
            setScript(true);
            setRelatorioAgendamentos(true);
            setRelatorioContatosAPP(true);
            setIndicadorRetencao(true);
            setIndicadorVenda(true);
            setObjecao(true);
            setVisualizarMeta(true);
            setHistoricoContato(true);
            setRealizarContato(true);
            setMetaAgendamento(true);
            setMetaAniversariante(true);
            setMetaFaltosos(true);
            setMetaFaturamento(true);
            setMetaGrupoRisco(true);
            setMetaIndicado(true);
            setMetaPassivo(true);
            setMetaPerda(true);
            setMetaPosVenda(true);
            setMetaQtdeVenda(true);
            setMetaRenovacao(true);
            setMetaVencidos(true);
            setMetaVinteQuatroHoras(true);
            setMetaConversaoAgendados(true);
            setMetaLigacaoAgendamentoAmanha(true);
            setMetaExAlunos(true);
            setMetaConversaoExAlunos(true);
            setMetaVisitantesAntigos(true);
        }

        /* ------------------   INÍCIO - CENTRAL DE EVENTOS   ------------------- */
        //funcionalidade
        setPerfilEvento(true);
        setCadastroInicial(true);
        setConsultaConversas(true);
        setOrcamentoDetalhado(true);
        setConsultaDisponibilidades(true);
        setAgendaVisita(true);
        setListaProspects(true);
        setPesquisaGeral(true);
        setConversa(true);
        //entidades
        setProdutoLocacao(true);
        /* ------------------     FIM - CENTRAL DE EVENTOS    ------------------- */
 /*---------Financeiro---------*/
        setConta(true);
        setTipoConta(true);
        setTipoDocumento(true);
        setFornecedor(true);
        setLancarContasPagar(true);
        setQuitarContasPagar(true);
        setVisualizarLancamentos(true);
        setGestaoRecebiveis(true);
        setGestaoLotes(true);
        setResumoContas(true);
        setPlanoContas(true);
        setLancamentosAvulsos(true);
        setRateioIntegracao(true);
        setHistoricoMetas(true);
        setLancarVisualizarLancamentosEmpresas(true);
        setCadastroMetas(true);
        setFecharCaixaOutroUsuario(true);
        setConciliarSaldo(true);
        setBIFinanceiro(true);
        setFluxoCaixa(true);
        /*
         * SmartBox
         */
        setTelaInicialSmartBox(true);
        setSelecionarMultiplosColaboradores(true);
        setVisualizaRMetaFinanceiraEmpresaBI(true);
        setVisualizarMetasFinanceirasTodasEmpresas(true);
        setAbrirCaixaAdm(true);
        setConsultarHistCaixaAdm(true);
        setAbrirConsultarHistCaixaAdmTodasEmpresas(true);

        // Inicio controle de estoque
        setCadastrarCompra(true);
        setCancelarCompra(true);
        setCadastrarBalanco(true);
        setCancelarBalanco(true);
        setVisualizarCardex(true);
        //setAlterarTipoProduto(true);
        setConfigurarProdutoEstoque(true);
        setAlterarSituacaoProdutoEstoque(true);
        setVisualizarPosicaoEstoque(true);
        // fim  controle de estoque

        setLancamentoProdutoColetivo(true);
        setTicketMedio(true);
        setConviteBI(true);
        setTamanhoArmario(true);
        setTodosGruposColaboradoresCRM(true);
        setSomenteSeuGrupoColaboradorCRM(true);
        setCrmExtraCRM(true);
        setCrmQuarentena(true);
        setCrmEditarSimplesRegistro(true);
        setPesquisarPeriodoCRM(true);
        setPesquisarMetaPassadaCRM(true);
        setBusinessIntelligenceCRM(true);

        setAdicionarAlterarSenhaAcesso(true);
        setTotalClienteConsulta(true);

        setAtivarVerificacaoClientesAtivos(true);
        setVerificarClientesAtivos(true);

        setLancarMensagemObservacaoGeral(true);
        /* ------------------   INÍCIO - ESTÚDIO   ------------------- */
        setAgendaMensal(true);
        setAgendaAmbiente(true);
        setAgendaProfissional(true);
        setAgendaIndividual(true);
        /* ------------------     FIM - ESTÚDIO    ------------------- */
        setGympass(true);
        setBrinde(true);
        setHistoricoPontos(false);
        
        setAniversariantes(true);
        setBiInadimplencia(true);
        setPermiteAlterarCPFDotz(true);
        setConfigurarClubeDeVantagens(true);
        setExcluirTokemGympass((true));

        setModuloNotaFiscal(true);
        setPermiteCancelarNotaFiscal(true);
        setPermiteInutilizarNotaFiscal(true);
        setPermiteExcluirNotaFiscal(true);
        setPermiteAlterarStatusNotaFiscal(true);
        setBiOperacaoExececoes(true);
        setBiCobrancaConvenio(true);
        setBiGestaoAcesso(true);
        setBiVerificarcaoClientes(true);
        setModuloPactoPay(true);
        setBiCicloDeVida(true);
        setBiGymPass(true);
        setRelatorioBVs(true);
        setRelatorioClientesComAtestado(true);
        setRelatorioClientesComBonus(true);
        setRelatorioClientesTrancados(true);
        setRelatorioDescontoPorOcupacao(true);
        setRelatorioFechamentoDeAcessos(true);
        setRelatorioIndicadorDeAcessos(true);
        setRelatorioDeRepasse(true);
        setRelatorioDeVisitantes(true);
        setRelatorioDePedidosPinpad(true);
        setRelatorioDeTotalizadorDeTickets(true);
        setRelatorioDeTransacoesPix(true);
        setRelatorioDeCliente(true);
        setRelatorioDeClientesCancelados(true);
        setRelatorioDeConsultaRecibo(true);
        setRelatorioDeArmarios(true);
        setRelatorioDePrevisaoRenovacao(true);
        setRelatorioDeFaturamentoRecebidoPeríodo(true);
        setRelatorioDeMovimentacaoContaCorrenteCliente(true);
        setRelatorioDeProdutosComVigencia(true);
        setRelatorioDeTotalizadorDeAcessos(true);
        setPermitirImprimirReciboEmBranco(true);
        setPermitirExportarDados(true);
        setApresentarNfseRecibo(true);

        // TREINO
        setTreinoBI(true);
        setTreinoBiPersonalizado(true);
        setTreinoEmCasa(true);
        setTreinoAcompanhamentoPersonal(true);
        setTreinoColaboradores(true);
        setTreinoGestaoCreditos(true);
        setTreinoPersonais(true);
        setTreinoRelatorioAndamento(true);
        setTreinoRelatorioAplicativosAtivos(true);
        setTreinoRelatorioGestaoCreditos(true);
        setTreinoRelatorioIndicadoresCarteiraProfessores(true);
        setTreinoRelatorioIndicadoresAtividadesProfessores(true);
        setTreinoRelatorioProfessoresAlunosAvisosMedicos(true);
        setTreinoRelatorioProfessoresSubstitutos(true);
        setTreinoRelatorioRanking(true);
        setTreinoCadastroAparelhos(true);
        setTreinoCadastroAtividades(true);
        setTreinoCadastroCategoriaAtividades(true);
        setTreinoCadastroNiveis(true);
        setTreinoCadastroFichasPredefinidas(true);
        setTreinoCadastroProgramasPredefinidos(true);
        setTreinoPrescricaoTreino(true);
        setTreinoCadastroAlunos(true);
        setTreinoCadastroColaboradoresTreino(true);
        setTreinoUsuarios(true);

        // Avaliação fisica
        setAvaliacaoFisicaBI(true);
        setAvaliacaoFisicaCadastroObjetivos(true);
        setAvaliacaoFisicaCadastroAnamnese(true);

        // Pacto Pay
        setPactoPayBI(true);
        setPactoPayCartaoCredito(true);
        setPactoPayParcelasEmAberto(true);
        setPactoPayCreditoOnline(true);
        setPactoPayPix(true);
        setPactoPayReguaCobranca(true);
        setPactoPayReguaCobrancaDashboard(true);
        setPactoPayReguaCobrancaConfigEmail(true);

        // Cross
        setCrossBi(true);
        setCrossWod(true);
        setCrossMonitor(true);
        setCrossCadastroAparelhos(true);
        setCrossCadastroAtividades(true);
        setCrossCadastroBenchmarks(true);
        setCrossCadastroTiposBenchmark(true);
        setCrossCadastroTipoWod(true);

        // Graduação
        setGraduacaoAvaliacoesProgresso(true);
        setGraduacaoFichaTenica(true);
        setGraduacaoAtividades(true);

        // Agenda
        setAgendaBi(true);
        setAgendaTvAula(true);
        setAgendaTvGestor(true);
        setAgendaAulas(true);
        setAgendaServicos(true);
        setAgendaModalidade(true);
        setAgendaIndicadores(true);
        setAgendaAulaExcluida(true);
        setAgendaConfigurarAula(true);
        setAgendaTipoAgendamento(true);

        setVisualizarCPFBusca(true);
        setVerPendenciasClienteListaAcessos(true);
        setPermitirAvisosInternos(true);
        setPermitirAtualizacaoContratoAssinado(true);
        setImpostoProduto(true);
    }

    public PermissaoAcessoMenuVO montarPermissoesMenu(List permissoes, UsuarioVO usuario, boolean perfilAcessoUnificado) {
        PermissaoAcessoMenuVO permissaoAcessoMenu = new PermissaoAcessoMenuVO();
        if (usuario.getAdministrador()) {
            permissaoAcessoMenu.inicializarDadosAdministrador();
        } else {
            Iterator j = permissoes.iterator();
            while (j.hasNext()) {
                try {
                    PermissaoVO obj = (PermissaoVO) j.next();

                    Class classeGenerica = Class.forName(obj.getClass().getName());
                    Method metodoGet = classeGenerica.getMethod("getNomeEntidade");
                    metodoGet.invoke(obj).toString();

                    Class permissaoMenu = Class.forName(permissaoAcessoMenu.getClass().getName());
                    Method metodoSet = permissaoMenu.getMethod("set" + metodoGet.invoke(obj).toString(), Boolean.class);
                    metodoSet.invoke(permissaoAcessoMenu, true);

                } catch (Exception ignored) {
                }
            }

            permissaoAcessoMenu.setPerfilAcessoUnificadoSincronizado(perfilAcessoUnificado);
            if (perfilAcessoUnificado) {
                permissaoAcessoMenu.setPerfilAcessoUnificado(permissaoAcessoMenu.getPerfilAcesso());
                permissaoAcessoMenu.setPerfilAcesso(false);
            }
        }
        return permissaoAcessoMenu;
    }

    public Boolean getPerfilAcesso() {
        return perfilAcesso;
    }

    public void setPerfilAcesso(Boolean perfilAcesso) {
        this.perfilAcesso = perfilAcesso;
    }

    public Boolean getUsuario() {
        return usuario;
    }

    public Boolean getTipoAmbiente() {
        return tipoAmbiente;
    }

    public void setTipoAmbiente(Boolean tipoAmbiente) {
        this.tipoAmbiente = tipoAmbiente;
    }

    public void setUsuario(Boolean usuario) {
        this.usuario = usuario;
    }

    public Boolean getCategoria() {
        return categoria;
    }

    public void setCategoria(Boolean categoria) {
        this.categoria = categoria;
    }

    public Boolean getCidade() {
        return cidade;
    }

    public void setCidade(Boolean cidade) {
        this.cidade = cidade;
    }

    public Boolean getClassificacao() {
        return classificacao;
    }

    public Boolean getTotalizadorFrequenciaRel() {
        return totalizadorFrequenciaRel;
    }

    public void setTotalizadorFrequenciaRel(Boolean totalizadorFrequenciaRel) {
        this.totalizadorFrequenciaRel = totalizadorFrequenciaRel;
    }

    public void setClassificacao(Boolean classificacao) {
        this.classificacao = classificacao;
    }

    public Boolean getCliente() {
        return cliente;
    }

    public void setCliente(Boolean cliente) {
        this.cliente = cliente;
    }

    public Boolean getImportacaoAlunoTurma() {
        return importacaoAlunoTurma;
    }

    public void setImportacaoAlunoTurma(Boolean importacaoAlunoTurma) {
        this.importacaoAlunoTurma = importacaoAlunoTurma;
    }

    public Boolean getColaborador() {
        return colaborador;
    }

    public void setColaborador(Boolean colaborador) {
        this.colaborador = colaborador;
    }

    public Boolean getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(Boolean configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public Boolean getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Boolean empresa) {
        this.empresa = empresa;
    }

    public Boolean getGrupo() {
        return grupo;
    }

    public void setGrupo(Boolean grupo) {
        this.grupo = grupo;
    }

    public Boolean getMovimentoContaCorrenteCliente() {
        return movimentoContaCorrenteCliente;
    }

    public void setMovimentoContaCorrenteCliente(Boolean movimentoContaCorrenteCliente) {
        this.movimentoContaCorrenteCliente = movimentoContaCorrenteCliente;
    }

    public Boolean getSelecionarColaboradorMetas() {
        return selecionarColaboradorMetas;
    }

    public void setSelecionarColaboradorMetas(Boolean selecionarColaboradorMetas) {
        this.selecionarColaboradorMetas = selecionarColaboradorMetas;
    }

    public Boolean getPais() {
        return pais;
    }

    public void setPais(Boolean pais) {
        this.pais = pais;
    }

    public Boolean getParentesco() {
        return parentesco;
    }

    public void setParentesco(Boolean parentesco) {
        this.parentesco = parentesco;
    }

    public Boolean getPerguntaCliente() {
        return perguntaCliente;
    }

    public void setPerguntaCliente(Boolean perguntaCliente) {
        this.perguntaCliente = perguntaCliente;
    }

    public Boolean getPergunta() {
        return pergunta;
    }

    public void setPergunta(Boolean pergunta) {
        this.pergunta = pergunta;
    }

    public Boolean getProfissao() {
        return profissao;
    }

    public void setProfissao(Boolean profissao) {
        this.profissao = profissao;
    }

    public Boolean getQuestionario() {
        return questionario;
    }

    public void setQuestionario(Boolean questionario) {
        this.questionario = questionario;
    }

    public Boolean getConvenioDesconto() {
        return convenioDesconto;
    }

    public void setConvenioDesconto(Boolean convenioDesconto) {
        this.convenioDesconto = convenioDesconto;
    }

    public Boolean getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(Boolean movProduto) {
        this.movProduto = movProduto;
    }

    public Boolean getBanco() {
        return banco;
    }

    public void setBanco(Boolean banco) {
        this.banco = banco;
    }

    public Boolean getContaCorrente() {
        return contaCorrente;
    }

    public void setContaCorrente(Boolean contaCorrente) {
        this.contaCorrente = contaCorrente;
    }

    public Boolean getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(Boolean convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public Boolean getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(Boolean formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Boolean getMovPagamento() {
        return movPagamento;
    }

    public void setMovPagamento(Boolean movPagamento) {
        this.movPagamento = movPagamento;
    }

    public Boolean getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(Boolean movParcela) {
        this.movParcela = movParcela;
    }

    public Boolean getTipoRemessa() {
        return tipoRemessa;
    }

    public void setTipoRemessa(Boolean tipoRemessa) {
        this.tipoRemessa = tipoRemessa;
    }

    public Boolean getTipoRetorno() {
        return tipoRetorno;
    }

    public void setTipoRetorno(Boolean tipoRetorno) {
        this.tipoRetorno = tipoRetorno;
    }

    public Boolean getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(Boolean vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Boolean getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Boolean ambiente) {
        this.ambiente = ambiente;
    }

    public Boolean getCategoriaProduto() {
        return categoriaProduto;
    }

    public void setCategoriaProduto(Boolean categoriaProduto) {
        this.categoriaProduto = categoriaProduto;
    }

    public Boolean getComposicao() {
        return composicao;
    }

    public void setComposicao(Boolean composicao) {
        this.composicao = composicao;
    }

    public Boolean getCondicaoPagamento() {
        return condicaoPagamento;
    }

    public void setCondicaoPagamento(Boolean condicaoPagamento) {
        this.condicaoPagamento = condicaoPagamento;
    }

    public Boolean getDesconto() {
        return desconto;
    }

    public void setDesconto(Boolean desconto) {
        this.desconto = desconto;
    }

    public Boolean getDuracao() {
        return duracao;
    }

    public void setDuracao(Boolean duracao) {
        this.duracao = duracao;
    }

    public Boolean getHorario() {
        return horario;
    }

    public void setHorario(Boolean horario) {
        this.horario = horario;
    }

    public Boolean getModalidade() {
        return modalidade;
    }

    public void setModalidade(Boolean modalidade) {
        this.modalidade = modalidade;
    }

    public Boolean getTipoModalidade() {
        return tipoModalidade;
    }

    public void setTipoModalidade(Boolean tipoModalidade) {
        this.tipoModalidade = tipoModalidade;
    }

    public Boolean getNivelTurma() {
        return nivelTurma;
    }

    public void setNivelTurma(Boolean nivelTurma) {
        this.nivelTurma = nivelTurma;
    }

    public Boolean getPlano() {
        return plano;
    }

    public void setPlano(Boolean plano) {
        this.plano = plano;
    }

    public Boolean getPlanoTextoPadrao() {
        return planoTextoPadrao;
    }

    public void setPlanoTextoPadrao(Boolean planoTextoPadrao) {
        this.planoTextoPadrao = planoTextoPadrao;
    }

    public Boolean getPermiteVisualizaConvitesPeriodo() {
        return permiteVisualizaConvitesPeriodo;
    }

    public void setPermiteVisualizaConvitesPeriodo(Boolean permiteVisualizaConvitesPeriodo) {
        this.permiteVisualizaConvitesPeriodo = permiteVisualizaConvitesPeriodo;
    }

    public Boolean getProduto() {
        return produto;
    }

    public void setProduto(Boolean produto) {
        this.produto = produto;
    }

    public Boolean getTurma() {
        return turma;
    }

    public void setTurma(Boolean turma) {
        this.turma = turma;
    }

    public Boolean getVisualizarContrato() {
        return visualizarContrato;
    }

    public void setVisualizarContrato(Boolean visualizarContrato) {
        this.visualizarContrato = visualizarContrato;
    }

    public Boolean getLog() {
        return log;
    }

    public void setLog(Boolean log) {
        this.log = log;
    }

    public Boolean getCaixaPorOperadorRel() {
        return caixaPorOperadorRel;
    }

    public void setCaixaPorOperadorRel(Boolean caixaPorOperadorRel) {
        this.caixaPorOperadorRel = caixaPorOperadorRel;
    }

    public Boolean getFrequenciaOcupacaoTurmasRel() {
        return frequenciaOcupacaoTurmasRel;
    }

    public void setFrequenciaOcupacaoTurmasRel(Boolean frequenciaOcupacaoTurmasRel) {
        this.frequenciaOcupacaoTurmasRel = frequenciaOcupacaoTurmasRel;
    }

    public Boolean getListaChamada() {
        return listaChamada;
    }

    public void setListaChamada(Boolean listaChamada) {
        this.listaChamada = listaChamada;
    }

    public Boolean getJustificativaOperacao() {
        return justificativaOperacao;
    }

    public void setJustificativaOperacao(Boolean justificativaOperacao) {
        this.justificativaOperacao = justificativaOperacao;
    }

    public Boolean getCompetenciaMensalRel() {
        return competenciaMensalRel;
    }

    public void setCompetenciaMensalRel(Boolean competenciaMensalRel) {
        this.competenciaMensalRel = competenciaMensalRel;
    }

    public Boolean getFaturamentoSinteticoRel() {
        return faturamentoSinteticoRel;
    }

    public Boolean getReceitaPorPeriodoSinteticoRel() {
        return receitaPorPeriodoSinteticoRel;
    }

    public void setReceitaPorPeriodoSinteticoRel(Boolean receitaPorPeriodoSinteticoRel) {
        this.receitaPorPeriodoSinteticoRel = receitaPorPeriodoSinteticoRel;
    }

    public Boolean getParcelaEmAbertoRel() {
        return parcelaEmAbertoRel;
    }

    public void setParcelaEmAbertoRel(Boolean parcelaEmAbertoRel) {
        this.parcelaEmAbertoRel = parcelaEmAbertoRel;
    }

    public Boolean getRelatorioParcelasConsolidado() {
        return relatorioParcelasConsolidado;
    }

    public void setRelatorioParcelasConsolidado(Boolean relatorioParcelasConsolidado) {
        this.relatorioParcelasConsolidado = relatorioParcelasConsolidado;
    }

    public Boolean getGrauInstrucao() {
        return grauInstrucao;
    }

    public void setGrauInstrucao(Boolean grauInstrucao) {
        this.grauInstrucao = grauInstrucao;
    }

    public void setFaturamentoSinteticoRel(Boolean faturamentoSinteticoRel) {
        this.faturamentoSinteticoRel = faturamentoSinteticoRel;
    }

    public Boolean getSaldoContaCorrenteRel() {
        return saldoContaCorrenteRel;
    }

    public void setSaldoContaCorrenteRel(Boolean saldoContaCorrenteRel) {
        this.saldoContaCorrenteRel = saldoContaCorrenteRel;
    }

    public Boolean getBusiness() {
        return business;
    }

    public void setBusiness(Boolean business) {
        this.business = business;
    }

    public Boolean getIndiceConversao() {
        return indiceConversao;
    }

    public void setIndiceConversao(Boolean indiceConversao) {
        this.indiceConversao = indiceConversao;
    }

    public Boolean getIndiceRenovacao() {
        return indiceRenovacao;
    }

    public void setIndiceRenovacao(Boolean indiceRenovacao) {
        this.indiceRenovacao = indiceRenovacao;
    }

    public Boolean getRotatividade() {
        return rotatividade;
    }

    public void setRotatividade(Boolean rotatividade) {
        this.rotatividade = rotatividade;
    }

    public Boolean getClientePorDuracaoRel() {
        return clientePorDuracaoRel;
    }

    public void setClientePorDuracaoRel(Boolean clientePorDuracaoRel) {
        this.clientePorDuracaoRel = clientePorDuracaoRel;
    }

    public Boolean getClienteRel() {
        return clienteRel;
    }

    public void setClienteRel(Boolean clienteRel) {
        this.clienteRel = clienteRel;
    }

    public Boolean getPendenciaCliente() {
        return pendenciaCliente;
    }

    public void setPendenciaCliente(Boolean pendenciaCliente) {
        this.pendenciaCliente = pendenciaCliente;
    }

    public void setEstornoRecibo(Boolean estornoRecibo) {
        this.estornoRecibo = estornoRecibo;
    }

    public Boolean getEstornoRecibo() {
        return estornoRecibo;
    }

    public Boolean getModuloCRM() {
        return moduloCRM;
    }

    public void setModuloCRM(Boolean moduloCRM) {
        this.moduloCRM = moduloCRM;
    }

    public void setDefinirLayout(Boolean definirLayout) {
        this.definirLayout = definirLayout;
    }

    public Boolean getDefinirLayout() {
        return definirLayout;
    }

    public Boolean getConfiguracaoSistemaCRM() {
        return configuracaoSistemaCRM;
    }

    public void setConfiguracaoSistemaCRM(Boolean configuracaoSistemaCRM) {
        this.configuracaoSistemaCRM = configuracaoSistemaCRM;
    }

    public Boolean getIndiceRenovacaoCRM() {
        return indiceRenovacaoCRM;
    }

    public void setIndiceRenovacaoCRM(Boolean indiceRenovacaoCRM) {
        this.indiceRenovacaoCRM = indiceRenovacaoCRM;
    }

    public Boolean getIndiceConversaoCRM() {
        return indiceConversaoCRM;
    }

    public void setIndiceConversaoCRM(Boolean indiceConversaoCRM) {
        this.indiceConversaoCRM = indiceConversaoCRM;
    }

    public Boolean getRotatividadeCRM() {
        return rotatividadeCRM;
    }

    public void setRotatividadeCRM(Boolean rotatividadeCRM) {
        this.rotatividadeCRM = rotatividadeCRM;
    }

    public Boolean getPendenciaClienteCRM() {
        return pendenciaClienteCRM;
    }

    public void setPendenciaClienteCRM(Boolean pendenciaClienteCRM) {
        this.pendenciaClienteCRM = pendenciaClienteCRM;
    }

    public Boolean getGrupoColaborador() {
        return grupoColaborador;
    }

    public void setGrupoColaborador(Boolean grupoColaborador) {
        this.grupoColaborador = grupoColaborador;
    }

    public Boolean getOrganizadorCarteira() {
        return organizadorCarteira;
    }

    public Boolean getAgenda() {
        return agenda;
    }

    public void setAgenda(Boolean agenda) {
        this.agenda = agenda;
    }

    public Boolean getAberturaMeta() {
        return aberturaMeta;
    }

    public void setAberturaMeta(Boolean aberturaMeta) {
        this.aberturaMeta = aberturaMeta;
    }

    public void setOrganizadorCarteira(Boolean organizadorCarteira) {
        this.organizadorCarteira = organizadorCarteira;
    }

    public Boolean getPassivo() {
        return passivo;
    }

    public void setPassivo(Boolean passivo) {
        this.passivo = passivo;
    }

    public Boolean getIndicacao() {
        return indicacao;
    }

    public void setIndicacao(Boolean indicacao) {
        this.indicacao = indicacao;
    }

    public Boolean getFeriado() {
        return feriado;
    }

    public void setFeriado(Boolean feriado) {
        this.feriado = feriado;
    }

    public Boolean getModeloMensagem() {
        return modeloMensagem;
    }

    public void setModeloMensagem(Boolean modeloMensagem) {
        this.modeloMensagem = modeloMensagem;
    }

    public Boolean getMalaDireta() {
        return malaDireta;
    }

    public void setMalaDireta(Boolean malaDireta) {
        this.malaDireta = malaDireta;
    }

    public Boolean getEvento() {
        return evento;
    }

    public void setEvento(Boolean evento) {
        this.evento = evento;
    }

    public Boolean getIndicadorVenda() {
        return indicadorVenda;
    }

    public void setIndicadorVenda(Boolean indicadorVenda) {
        this.indicadorVenda = indicadorVenda;
    }

    public Boolean getIndicadorRetencao() {
        return indicadorRetencao;
    }

    public void setIndicadorRetencao(Boolean indicadorRetencao) {
        this.indicadorRetencao = indicadorRetencao;
    }

    public Boolean getObjecao() {
        return objecao;
    }

    public void setObjecao(Boolean objecao) {
        this.objecao = objecao;
    }

    public Boolean getVisualizarMeta() {
        return visualizarMeta;
    }

    public void setVisualizarMeta(Boolean visualizarMeta) {
        this.visualizarMeta = visualizarMeta;
    }

    public Boolean getOperadoraCartao() {
        return operadoraCartao;
    }

    public void setOperadoraCartao(Boolean operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }

    public Boolean getHistoricoContato() {
        return historicoContato;
    }

    public void setHistoricoContato(Boolean historicoContato) {
        this.historicoContato = historicoContato;
    }

    public Boolean getRealizarContato() {
        return realizarContato;
    }

    public void setRealizarContato(Boolean realizarContato) {
        this.realizarContato = realizarContato;
    }

    public Boolean getMetaAgendamento() {
        return metaAgendamento;
    }

    public void setMetaAgendamento(Boolean metaAgendamento) {
        this.metaAgendamento = metaAgendamento;
    }

    public Boolean getMetaVinteQuatroHoras() {
        return metaVinteQuatroHoras;
    }

    public void setMetaVinteQuatroHoras(Boolean metaVinteQuatroHoras) {
        this.metaVinteQuatroHoras = metaVinteQuatroHoras;
    }

    public Boolean getMetaRenovacao() {
        return metaRenovacao;
    }

    public void setMetaRenovacao(Boolean metaRenovacao) {
        this.metaRenovacao = metaRenovacao;
    }

    public Boolean getMetaPosVenda() {
        return metaPosVenda;
    }

    public void setMetaPosVenda(Boolean metaPosVenda) {
        this.metaPosVenda = metaPosVenda;
    }

    public Boolean getMetaFaturamento() {
        return metaFaturamento;
    }

    public void setMetaFaturamento(Boolean metaFaturamento) {
        this.metaFaturamento = metaFaturamento;
    }

    public Boolean getMetaQtdeVenda() {
        return metaQtdeVenda;
    }

    public void setMetaQtdeVenda(Boolean metaQtdeVenda) {
        this.metaQtdeVenda = metaQtdeVenda;
    }

    public Boolean getMetaIndicado() {
        return metaIndicado;
    }

    public void setMetaIndicado(Boolean metaIndicado) {
        this.metaIndicado = metaIndicado;
    }

    public Boolean getMetaPassivo() {
        return metaPassivo;
    }

    public void setMetaPassivo(Boolean metaPassivo) {
        this.metaPassivo = metaPassivo;
    }

    public Boolean getMetaGrupoRisco() {
        return metaGrupoRisco;
    }

    public void setMetaGrupoRisco(Boolean metaGrupoRisco) {
        this.metaGrupoRisco = metaGrupoRisco;
    }

    public Boolean getMetaPerda() {
        return metaPerda;
    }

    public void setMetaPerda(Boolean metaPerda) {
        this.metaPerda = metaPerda;
    }

    public Boolean getMetaAniversariante() {
        return metaAniversariante;
    }

    public void setMetaAniversariante(Boolean metaAniversariante) {
        this.metaAniversariante = metaAniversariante;
    }

    public Boolean getMetaFaltosos() {
        return metaFaltosos;
    }

    public void setMetaFaltosos(Boolean metaFaltosos) {
        this.metaFaltosos = metaFaltosos;
    }

    public Boolean getPerfilEvento() {
        return perfilEvento;
    }

    public void setPerfilEvento(Boolean perfilEvento) {
        this.perfilEvento = perfilEvento;
    }

    public Boolean getCadastroInicial() {
        return cadastroInicial;
    }

    public void setCadastroInicial(Boolean cadastroInicial) {
        this.cadastroInicial = cadastroInicial;
    }

    public Boolean getConsultaConversas() {
        return consultaConversas;
    }

    public void setConsultaConversas(Boolean consultaConversas) {
        this.cadastroInicial = consultaConversas;
    }

    public Boolean getOrcamentoDetalhado() {
        return orcamentoDetalhado;
    }

    public void setOrcamentoDetalhado(Boolean orcamentoDetalhado) {
        this.orcamentoDetalhado = orcamentoDetalhado;
    }

    public void setConsultaDisponibilidades(Boolean consultaDisponibilidades) {
        this.consultaDisponibilidades = consultaDisponibilidades;
    }

    public Boolean getConsultaDisponibilidades() {
        return consultaDisponibilidades;
    }

    public void setAgendaVisita(Boolean agendaVisita) {
        this.agendaVisita = agendaVisita;
    }

    public Boolean getAgendaVisita() {
        return agendaVisita;
    }

    public void setListaProspects(Boolean listaProspects) {
        this.listaProspects = listaProspects;
    }

    public Boolean getListaProspects() {
        return listaProspects;
    }

    public void setPesquisaGeral(Boolean pesquisaGeral) {
        this.pesquisaGeral = pesquisaGeral;
    }

    public Boolean getPesquisaGeral() {
        return pesquisaGeral;
    }

    public void setConversa(Boolean conversa) {
        this.conversa = conversa;
    }

    public Boolean getConversa() {
        return conversa;
    }

    public Boolean getProdutoLocacao() {
        return produtoLocacao;
    }

    public void setProdutoLocacao(Boolean produtoLocacao) {
        this.produtoLocacao = produtoLocacao;
    }

    public Boolean getVendaConsumidor() {
        return vendaConsumidor;
    }

    public void setVendaConsumidor(Boolean vendaConsumidor) {
        this.vendaConsumidor = vendaConsumidor;
    }

    public Boolean getRelatorioAlteracaoDataBaseContrato() {
        return relatorioAlteracaoDataBaseContrato;
    }

    public void setRelatorioAlteracaoDataBaseContrato(Boolean relatorioAlteracaoDataBaseContrato) {
        this.relatorioAlteracaoDataBaseContrato = relatorioAlteracaoDataBaseContrato;
    }

    public Boolean getCupomFiscal() {
        return cupomFiscal;
    }

    public void setCupomFiscal(Boolean cupomFiscal) {
        this.cupomFiscal = cupomFiscal;
    }

    public Boolean getGestaoTransacoes() {
        return gestaoTransacoes;
    }

    public void setGestaoTransacoes(Boolean gestaoTransacoes) {
        this.gestaoTransacoes = gestaoTransacoes;
    }

    public Boolean getConta() {
        return conta;
    }

    public void setConta(Boolean conta) {
        this.conta = conta;
    }

    public Boolean getTipoConta() {
        return tipoConta;
    }

    public void setTipoConta(Boolean tipoConta) {
        this.tipoConta = tipoConta;
    }

    public Boolean getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(Boolean tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public void setContratoRecorrencia(Boolean contratoRecorrencia) {
        this.contratoRecorrencia = contratoRecorrencia;
    }

    public Boolean getContratoRecorrencia() {
        return contratoRecorrencia;
    }

    public Boolean getSelecionarMultiplosColaboradores() {
        return ********************************;
    }

    public void setSelecionarMultiplosColaboradores(Boolean ********************************) {
        this.******************************** = ********************************;
    }

    public Boolean getTelaInicialSmartBox() {
        return telaInicialSmartBox;
    }

    public void setTelaInicialSmartBox(Boolean telaInicialSmartBox) {
        this.telaInicialSmartBox = telaInicialSmartBox;
    }

    public void setLancarVisualizarLancamentosEmpresas(Boolean lancarVisualizarLancamentosEmpresas) {
        this.lancarVisualizarLancamentosEmpresas = lancarVisualizarLancamentosEmpresas;
    }

    public Boolean getLancarVisualizarLancamentosEmpresas() {
        return lancarVisualizarLancamentosEmpresas;
    }

    public void setHistoricoMetas(Boolean historicoMetas) {
        this.historicoMetas = historicoMetas;
    }

    public Boolean getHistoricoMetas() {
        return historicoMetas;
    }

    public void setCadastroMetas(Boolean cadastroMetas) {
        this.cadastroMetas = cadastroMetas;
    }

    public Boolean getCadastroMetas() {
        return cadastroMetas;
    }

    public void setVisualizaRMetaFinanceiraEmpresaBI(Boolean visualizaRMetaFinanceiraEmpresaBI) {
        this.visualizaRMetaFinanceiraEmpresaBI = visualizaRMetaFinanceiraEmpresaBI;
    }

    public Boolean getVisualizaRMetaFinanceiraEmpresaBI() {
        return visualizaRMetaFinanceiraEmpresaBI;
    }

    public void setMetaVencidos(Boolean metaVencidos) {
        this.metaVencidos = metaVencidos;
    }

    public Boolean getMetaVencidos() {
        return metaVencidos;
    }

    public void setVisualizarMetasFinanceirasTodasEmpresas(Boolean visualizarMetasFinanceirasTodasEmpresas) {
        this.visualizarMetasFinanceirasTodasEmpresas = visualizarMetasFinanceirasTodasEmpresas;
    }

    public Boolean getVisualizarMetasFinanceirasTodasEmpresas() {
        return visualizarMetasFinanceirasTodasEmpresas;
    }

    public void setMetaConversaoAgendados(Boolean metaConversaoAgendados) {
        this.metaConversaoAgendados = metaConversaoAgendados;
    }

    public Boolean getMetaConversaoAgendados() {
        return metaConversaoAgendados;
    }

    public void setAbrirConsultarHistCaixaAdmTodasEmpresas(Boolean abrirConsultarHistCaixaAdm) {
        this.abrirConsultarHistCaixaAdmTodasEmpresas = abrirConsultarHistCaixaAdm;
    }

    public Boolean getAbrirConsultarHistCaixaAdmTodasEmpresas() {
        return abrirConsultarHistCaixaAdmTodasEmpresas;
    }

    public void setConsultarHistCaixaAdm(Boolean consultarHistCaixaAdm) {
        this.consultarHistCaixaAdm = consultarHistCaixaAdm;
    }

    public Boolean getConsultarHistCaixaAdm() {
        return consultarHistCaixaAdm;
    }

    public void setAbrirCaixaAdm(Boolean abrirCaixaAdm) {
        this.abrirCaixaAdm = abrirCaixaAdm;
    }

    public Boolean getAbrirCaixaAdm() {
        return abrirCaixaAdm;
    }

    public Boolean getMetaLigacaoAgendamentoAmanha() {
        return metaLigacaoAgendamentoAmanha;
    }

    public void setMetaLigacaoAgendamentoAmanha(Boolean metaLigacaoAgendamentoAmanha) {
        this.metaLigacaoAgendamentoAmanha = metaLigacaoAgendamentoAmanha;
    }

    public void setFecharCaixaOutroUsuario(Boolean fecharCaixaOutroUsuario) {
        this.fecharCaixaOutroUsuario = fecharCaixaOutroUsuario;
    }

    public Boolean getFecharCaixaOutroUsuario() {
        return fecharCaixaOutroUsuario;
    }

    public Boolean getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(Boolean localAcesso) {
        this.localAcesso = localAcesso;
    }

    public void setConciliarSaldo(Boolean conciliarSaldo) {
        this.conciliarSaldo = conciliarSaldo;
    }

    public Boolean getConciliarSaldo() {
        return conciliarSaldo;
    }

    public Boolean getGestaoComissao() {
        return gestaoComissao;
    }

    public void setGestaoComissao(Boolean gestaoComissao) {
        this.gestaoComissao = gestaoComissao;
    }

    public Boolean getAlterarTipoProduto() {
        return alterarTipoProduto;
    }

    public void setAlterarTipoProduto(Boolean alterarTipoProduto) {
        this.alterarTipoProduto = alterarTipoProduto;
    }

    public Boolean getCadastrarBalanco() {
        return cadastrarBalanco;
    }

    public void setCadastrarBalanco(Boolean cadastrarBalanco) {
        this.cadastrarBalanco = cadastrarBalanco;
    }

    public Boolean getCadastrarCompra() {
        return cadastrarCompra;
    }

    public void setCadastrarCompra(Boolean cadastrarCompra) {
        this.cadastrarCompra = cadastrarCompra;
    }

    public Boolean getCancelarBalanco() {
        return cancelarBalanco;
    }

    public void setCancelarBalanco(Boolean cancelarBalanco) {
        this.cancelarBalanco = cancelarBalanco;
    }

    public Boolean getCancelarCompra() {
        return cancelarCompra;
    }

    public void setCancelarCompra(Boolean cancelarCompra) {
        this.cancelarCompra = cancelarCompra;
    }

    public Boolean getConfigurarProdutoEstoque() {
        return configurarProdutoEstoque;
    }

    public void setConfigurarProdutoEstoque(Boolean configurarProdutoEstoque) {
        this.configurarProdutoEstoque = configurarProdutoEstoque;
    }

    public Boolean getVisualizarPosicaoEstoque() {
        return visualizarPosicaoEstoque;
    }

    public void setVisualizarPosicaoEstoque(Boolean visualizarPosicaoEstoque) {
        this.visualizarPosicaoEstoque = visualizarPosicaoEstoque;
    }

    public Boolean getVisualizarCardex() {
        return visualizarCardex;
    }

    public void setVisualizarCardex(Boolean visualizarCardex) {
        this.visualizarCardex = visualizarCardex;
    }

    public Boolean getAlterarSituacaoProdutoEstoque() {
        return alterarSituacaoProdutoEstoque;
    }

    public void setAlterarSituacaoProdutoEstoque(Boolean alterarSituacaoProdutoEstoque) {
        this.alterarSituacaoProdutoEstoque = alterarSituacaoProdutoEstoque;
    }

    public Boolean getMetaExAlunos() {
        return metaExAlunos;
    }

    public void setMetaExAlunos(Boolean metaExAlunos) {
        this.metaExAlunos = metaExAlunos;
    }

    public Boolean getMetaConversaoExAlunos() {
        return metaConversaoExAlunos;
    }

    public void setMetaConversaoExAlunos(Boolean metaConversaoExAlunos) {
        this.metaConversaoExAlunos = metaConversaoExAlunos;
    }

    public Boolean getBIFinanceiro() {
        return biFinanceiro;
    }

    public void setBIFinanceiro(Boolean biFinanceiro) {
        this.biFinanceiro = biFinanceiro;
    }

    public Boolean getGeradorConsultas() {
        return geradorConsultas;
    }

    public void setGeradorConsultas(Boolean geradorConsultas) {
        this.geradorConsultas = geradorConsultas;
    }

    public Boolean getTaxasComissao() {
        return taxasComissao;
    }

    public void setTaxasComissao(Boolean taxasComissao) {
        this.taxasComissao = taxasComissao;
    }

    public Boolean getComissaoVariavel() {
        return comissaoVariavel;
    }

    public void setComissaoVariavel(Boolean comissaoVariavel) {
        this.comissaoVariavel = comissaoVariavel;
    }

    public Boolean getVisualizarBI() {
        return visualizarBI;
    }

    public void setVisualizarBI(Boolean visualizarBI) {
        this.visualizarBI = visualizarBI;
    }

    public Boolean getCaixaEmAberto() {
        return caixaEmAberto;
    }

    public void setCaixaEmAberto(Boolean caixaEmAberto) {
        this.caixaEmAberto = caixaEmAberto;
    }

    public Boolean getAulaAvulsaDiaria() {
        return aulaAvulsaDiaria;
    }

    public void setAulaAvulsaDiaria(Boolean aulaAvulsaDiaria) {
        this.aulaAvulsaDiaria = aulaAvulsaDiaria;
    }

    public Boolean getConsultorVendaAvulsa() {
        return consultorVendaAvulsa;
    }

    public void setConsultorVendaAvulsa(Boolean consultorVendaAvulsa) {
        this.consultorVendaAvulsa = consultorVendaAvulsa;
    }

    public Boolean getApresentarLinkCadastrarCartaoOnline() {
        return apresentarLinkCadastrarCartaoOnline;
    }

    public void setApresentarLinkCadastrarCartaoOnline(Boolean apresentarLinkCadastrarCartaoOnline) {
        this.apresentarLinkCadastrarCartaoOnline = apresentarLinkCadastrarCartaoOnline;
    }

    public Boolean getPermissaoTransferirClienteEmpresa() {
        return permissaoTransferirClienteEmpresa;
    }

    public void setPermissaoTransferirClienteEmpresa(Boolean permissaoTransferirClienteEmpresa) {
        this.permissaoTransferirClienteEmpresa = permissaoTransferirClienteEmpresa;
    }

    public Boolean getPermissaoFreePass() {
        return permissaoFreePass;
    }

    public void setPermissaoFreePass(Boolean permissaoFreePass) {
        this.permissaoFreePass = permissaoFreePass;
    }

    public Boolean getFecharNegociacaoContrato() {
        return fecharNegociacaoContrato;
    }

    public void setFecharNegociacaoContrato(Boolean fecharNegociacaoContrato) {
        this.fecharNegociacaoContrato = fecharNegociacaoContrato;
    }

    public Boolean getGestaoNegativacoes() {
        return gestaoNegativacoes;
    }

    public void setGestaoNegativacoes(Boolean gestaoNegativacoes) {
        this.gestaoNegativacoes = gestaoNegativacoes;
    }

    public Boolean getGestaoNotas() {
        return gestaoNotas;
    }

    public void setGestaoNotas(Boolean gestaoNotas) {
        this.gestaoNotas = gestaoNotas;
    }

    public Boolean getLancamentoProdutoColetivo() {
        return lancamentoProdutoColetivo;
    }

    public void setLancamentoProdutoColetivo(Boolean lancamentoProdutoColetivo) {
        this.lancamentoProdutoColetivo = lancamentoProdutoColetivo;
    }

    public Boolean getMetaVisitantesAntigos() {
        return metaVisitantesAntigos;
    }

    public void setMetaVisitantesAntigos(Boolean metaVisitantesAntigos) {
        this.metaVisitantesAntigos = metaVisitantesAntigos;
    }

    public Boolean getTicketMedio() {
        return ticketMedio;
    }

    public void setTicketMedio(Boolean ticketMedio) {
        this.ticketMedio = ticketMedio;
    }

    public Boolean getAlterarValorManutencaoModalidade() {
        return alterarValorManutencaoModalidade;
    }

    public void setAlterarValorManutencaoModalidade(Boolean alterarValorManutencaoModalidade) {
        this.alterarValorManutencaoModalidade = alterarValorManutencaoModalidade;
    }

    public Boolean getBoletosSistema() {
        return boletosSistema;
    }

    public void setBoletosSistema(Boolean boletosSistema) {
        this.boletosSistema = boletosSistema;
    }

    public Boolean getEnviarSMSSocialMailing() {
        return enviarSMSSocialMailing;
    }

    public void setEnviarSMSSocialMailing(Boolean enviarSMSSocialMailing) {
        this.enviarSMSSocialMailing = enviarSMSSocialMailing;
    }

    public Boolean getGeralClientes() {
        return geralClientes;
    }

    public void setGeralClientes(Boolean geralClientes) {
        this.geralClientes = geralClientes;
    }

    public Boolean getRelatorioClientes() {
        return relatorioClientes;
    }

    public void setRelatorioClientes(Boolean relatorioClientes) {
        this.relatorioClientes = relatorioClientes;
    }

    public Boolean getTamanhoArmario() {
        return tamanhoArmario;
    }

    public void setTamanhoArmario(Boolean tamanhoArmario) {
        this.tamanhoArmario = tamanhoArmario;
    }

    public Boolean getTodosGruposColaboradoresCRM() {
        return todosGruposColaboradoresCRM;
    }

    public void setTodosGruposColaboradoresCRM(Boolean todosGruposColaboradoresCRM) {
        this.todosGruposColaboradoresCRM = todosGruposColaboradoresCRM;
    }

    public Boolean getSomenteSeuGrupoColaboradorCRM() {
        return somenteSeuGrupoColaboradorCRM;
    }

    public void setSomenteSeuGrupoColaboradorCRM(Boolean somenteSeuGrupoColaboradorCRM) {
        this.somenteSeuGrupoColaboradorCRM = somenteSeuGrupoColaboradorCRM;
    }

    public Boolean getTotalizadorMeta() {
        return totalizadorMeta;
    }

    public void setTotalizadorMeta(Boolean totalizadorMeta) {
        this.totalizadorMeta = totalizadorMeta;
    }

    public Boolean getCrmExtraCRM() {
        return crmExtraCRM;
    }

    public void setCrmExtraCRM(Boolean crmExtraCRM) {
        this.crmExtraCRM = crmExtraCRM;
    }

    public Boolean getAlterarMatricula() {
        return alterarMatricula;
    }

    public void setAlterarMatricula(Boolean alterarMatricula) {
        this.alterarMatricula = alterarMatricula;
    }

    public Boolean getPesquisarPeriodoCRM() {
        return pesquisarPeriodoCRM;
    }

    public void setPesquisarPeriodoCRM(Boolean pesquisarPeriodoCRM) {
        this.pesquisarPeriodoCRM = pesquisarPeriodoCRM;
    }

    public Boolean getPesquisarMetaPassadaCRM() {
        return pesquisarMetaPassadaCRM;
    }

    public void setPesquisarMetaPassadaCRM(Boolean pesquisarMetaPassadaCRM) {
        this.pesquisarMetaPassadaCRM = pesquisarMetaPassadaCRM;
    }

    public Boolean getConviteAulaExperimental() {
        return conviteAulaExperimental;
    }

    public void setConviteAulaExperimental(Boolean conviteAulaExperimental) {
        this.conviteAulaExperimental = conviteAulaExperimental;
    }

    public Boolean getSaldoCreditosClientes() {
        return saldoCreditosClientes;
    }

    public void setSaldoCreditosClientes(Boolean saldoCreditosClientes) {
        this.saldoCreditosClientes = saldoCreditosClientes;
    }

    public Boolean getBusinessIntelligenceCRM() {
        return businessIntelligenceCRM;
    }

    public void setBusinessIntelligenceCRM(Boolean businessIntelligenceCRM) {
        this.businessIntelligenceCRM = businessIntelligenceCRM;
    }

    public Boolean getModuloCE() {
        return moduloCE;
    }

    public void setModuloCE(Boolean moduloCE) {
        this.moduloCE = moduloCE;
    }

    public Boolean getAdicionarAlterarSenhaAcesso() {
        return adicionarAlterarSenhaAcesso;
    }

    public void setAdicionarAlterarSenhaAcesso(Boolean adicionarAlterarSenhaAcesso) {
        this.adicionarAlterarSenhaAcesso = adicionarAlterarSenhaAcesso;
    }

    public Boolean getNotificacaoExpiracaoSistema() {
        return notificacaoExpiracaoSistema;
    }

    public void setNotificacaoExpiracaoSistema(Boolean notificacaoExpiracaoSistema) {
        this.notificacaoExpiracaoSistema = notificacaoExpiracaoSistema;
    }

    public Boolean getCampanhaCupomDesconto() {
        return campanhaCupomDesconto;
    }

    public void setCampanhaCupomDesconto(Boolean campanhaCupomDesconto) {
        this.campanhaCupomDesconto = campanhaCupomDesconto;
    }

    public Boolean getSorteio() {
        return sorteio;
    }

    public void setSorteio(Boolean sorteio) {
        this.sorteio = sorteio;
    }


    public Boolean getOperacoesColetivas() {
        return operacoesColetivas;
    }

    public void setOperacoesColetivas(Boolean operacoesColetivas) {
        this.operacoesColetivas = operacoesColetivas;
    }

    public Boolean getMostrarAbaNotaFiscaisPerfilAluno() {
        return mostrarAbaNotaFiscaisPerfilAluno;
    }

    public void setMostrarAbaNotaFiscaisPerfilAluno(Boolean mostrarAbaNotaFiscaisPerfilAluno) {
        this.mostrarAbaNotaFiscaisPerfilAluno = mostrarAbaNotaFiscaisPerfilAluno;
    }

    public Boolean getGameOfResults() {
        return gameOfResults;
    }

    public void setGameOfResults(Boolean gameOfResults) {
        this.gameOfResults = gameOfResults;
    }

    public Boolean getBiFinanceiro() {
        return biFinanceiro;
    }

    public void setBiFinanceiro(Boolean biFinanceiro) {
        this.biFinanceiro = biFinanceiro;
    }

    public Boolean getConviteBI() {
        return conviteBI;
    }

    public void setConviteBI(Boolean conviteBI) {
        this.conviteBI = conviteBI;
    }

    public Boolean getIndiceFinanceiroReajustePreco() {
        return indiceFinanceiroReajustePreco;
    }

    public void setIndiceFinanceiroReajustePreco(Boolean indiceFinanceiroReajustePreco) {
        this.indiceFinanceiroReajustePreco = indiceFinanceiroReajustePreco;
    }

    public Boolean getTotalClienteConsulta() {
        return totalClienteConsulta;
    }

    public void setTotalClienteConsulta(Boolean totalClienteConsulta) {
        this.totalClienteConsulta = totalClienteConsulta;
    }

    public Boolean getAtivarVerificacaoClientesAtivos() {
        return ativarVerificacaoClientesAtivos;
    }

    public void setAtivarVerificacaoClientesAtivos(Boolean ativarVerificacaoClientesAtivos) {
        this.ativarVerificacaoClientesAtivos = ativarVerificacaoClientesAtivos;
    }

    public Boolean getVerificarClientesAtivos() {
        return verificarClientesAtivos;
    }

    public void setVerificarClientesAtivos(Boolean verificarClientesAtivos) {
        this.verificarClientesAtivos = verificarClientesAtivos;
    }

    public Boolean getGestaoRemessas() {
        return gestaoRemessas;
    }

    public void setGestaoRemessas(Boolean gestaoRemessas) {
        this.gestaoRemessas = gestaoRemessas;
    }

    public Boolean getLancarMensagemObservacaoGeral() {
        return lancarMensagemObservacaoGeral;
    }

    public void setLancarMensagemObservacaoGeral(Boolean lancarMensagemObservacaoGeral) {
        this.lancarMensagemObservacaoGeral = lancarMensagemObservacaoGeral;
    }

    public Boolean getScript() {
        return script;
    }

    public void setScript(Boolean script) {
        this.script = script;
    }

    public Boolean getRelatorioAgendamentos() {
        return relatorioAgendamentos;
    }

    public void setRelatorioAgendamentos(Boolean relatorioAgendamentos) {
        this.relatorioAgendamentos = relatorioAgendamentos;
    }

    public Boolean getRelatorioContatosAPP() {
        return relatorioContatosAPP;
    }

    public void setRelatorioContatosAPP(Boolean relatorioContatosAPP) {
        this.relatorioContatosAPP = relatorioContatosAPP;
    }

    public Boolean getConfiguracaoFinanceiro() {
        return configuracaoFinanceiro;
    }

    public void setConfiguracaoFinanceiro(Boolean configuracaoFinanceiro) {
        this.configuracaoFinanceiro = configuracaoFinanceiro;
    }

    public Boolean getBiAulaExperimental() {
        return biAulaExperimental;
    }

    public void setBiAulaExperimental(Boolean biAulaExperimental) {
        this.biAulaExperimental = biAulaExperimental;
    }

    public Boolean getPermiteVisualizaGymPassPeriodo() {
        return permiteVisualizaGymPassPeriodo;
    }

    public void setPermiteVisualizaGymPassPeriodo(Boolean permiteVisualizaGymPassPeriodo) {
        this.permiteVisualizaGymPassPeriodo = permiteVisualizaGymPassPeriodo;
    }

    public Boolean getPermiteVisualizaGogoodPeriodo() {
        return permiteVisualizaGogoodPeriodo;
    }

    public void setPermiteVisualizaGogoodPeriodo(Boolean permiteVisualizaGogoodPeriodo) {
        this.permiteVisualizaGogoodPeriodo = permiteVisualizaGogoodPeriodo;
    }

    public Boolean getAgendaMensal() {
        return agendaMensal;
    }

    public void setAgendaMensal(Boolean agendaMensal) {
        this.agendaMensal = agendaMensal;
    }

    public Boolean getAgendaAmbiente() {
        return agendaAmbiente;
    }

    public void setAgendaAmbiente(Boolean agendaAmbiente) {
        this.agendaAmbiente = agendaAmbiente;
    }

    public Boolean getAgendaProfissional() {
        return agendaProfissional;
    }

    public void setAgendaProfissional(Boolean agendaProfissional) {
        this.agendaProfissional = agendaProfissional;
    }

    public Boolean getAgendaIndividual() {
        return agendaIndividual;
    }

    public void setAgendaIndividual(Boolean agendaIndividual) {
        this.agendaIndividual = agendaIndividual;
    }

    public Boolean getPermiteRegistrarAcessoAvulso() {
        return permiteRegistrarAcessoAvulso;
    }

    public void setPermiteRegistrarAcessoAvulso(Boolean permiteRegistrarAcessoAvulso) {
        this.permiteRegistrarAcessoAvulso = permiteRegistrarAcessoAvulso;
    }

    public Boolean getPermiteDownloadRemessa() {
        return permiteDownloadRemessa;
    }

    public void setPermiteDownloadRemessa(Boolean permiteDownloadRemessa) {
        this.permiteDownloadRemessa = permiteDownloadRemessa;
    }

    public Boolean getBrinde() {
        return brinde;
    }

    public void setBrinde(Boolean brinde) {
        this.brinde = brinde;
    }

    public Boolean getHistoricoPontos() {
        return historicoPontos;
    }

    public void setHistoricoPontos(Boolean historicoPontos) {
        this.historicoPontos = historicoPontos;
    }

    
    public Boolean getConsultaTurma() {
        return consultaTurma;
    }

    public void setConsultaTurma(Boolean consultaTurma) {
        this.consultaTurma = consultaTurma;
    }

    public Boolean getRelatorioFrequenciaTurma() {
        return relatorioFrequenciaTurma;
    }

    public void setRelatorioFrequenciaTurma() {
        this.relatorioFrequenciaTurma = ControleAcesso.verificarPermissaoRelatorioFrequenciaTurmas();
    }

    public Boolean getIncluirAutorizacaoAGE() {
        return incluirAutorizacaoAGE;
    }

    public void setIncluirAutorizacaoAGE(Boolean incluirAutorizacaoAGE) {
        this.incluirAutorizacaoAGE = incluirAutorizacaoAGE;
    }

    public Boolean getMapaTurma() {
        return mapaTurma;
    }

    public void setMapaTurma(Boolean mapaTurma) {
        this.mapaTurma = mapaTurma;
    }

    public Boolean getGestaoPersonal() {
        return gestaoPersonal;
    }

    public void setGestaoPersonal(Boolean gestaoPersonal) {
        this.gestaoPersonal = gestaoPersonal;
    }

    public Boolean getRelatorioPersonal() {
        return relatorioPersonal;
    }

    public void setRelatorioPersonal(Boolean relatorioPersonal) {
        this.relatorioPersonal = relatorioPersonal;
    }

    public Boolean getGestaoArmario() {
        return gestaoArmario;
    }

    public void setGestaoArmario(Boolean gestaoArmario) {
        this.gestaoArmario = gestaoArmario;
    }
    
    public Boolean getAniversariantes() {
        return aniversariantes;
    }

    public void setAniversariantes(Boolean aniversariantes) {
        this.aniversariantes = aniversariantes;
    }

    public Boolean getServidorFacial() {
        return servidorFacial;
    }

    public void setServidorFacial(Boolean servidorFacial) {
        this.servidorFacial = servidorFacial;
    }

    public Boolean getBiInadimplencia() {
        return biInadimplencia;
    }

    public void setBiInadimplencia(Boolean biInadimplencia) {
        this.biInadimplencia = biInadimplencia;
    }

    public Boolean getGestaoNFCe() {
        return gestaoNFCe;
    }

    public void setGestaoNFCe(Boolean gestaoNFCe) {
        this.gestaoNFCe = gestaoNFCe;
    }

    public Boolean getVendaRapida() {
        return vendaRapida;
    }

    public void setVendaRapida(Boolean vendaRapida) {
        this.vendaRapida = vendaRapida;
    }

    public Boolean getMapaEstatistico() {
        return mapaEstatistico;
    }

    public void setMapaEstatistico(Boolean mapaEstatistico) {
        this.mapaEstatistico = mapaEstatistico;
    }

    public Boolean getPlanoTipo() {
        return planoTipo;
    }

    public void setPlanoTipo(Boolean planoTipo) {
        this.planoTipo = planoTipo;
    }

    public Boolean getGympass() {
        return gympass;
    }

    public void setGympass(Boolean gympass) {
        this.gympass = gympass;
    }

    public Boolean getGestaoTurma() {
        return gestaoTurma;
    }

    public void setGestaoTurma(Boolean gestaoTurma) {
        this.gestaoTurma = gestaoTurma;
    }

    public Boolean getPermiteAlterarCPFDotz() {
        return permiteAlterarCPFDotz;
    }

    public void setPermiteAlterarCPFDotz(Boolean permiteAlterarCPFDotz) {
        this.permiteAlterarCPFDotz = permiteAlterarCPFDotz;
    }

    public Boolean getPermiteEditarValorParcelaNegociacao() {
        return permiteEditarValorParcelaNegociacao;
    }

    public void setPermiteEditarValorParcelaNegociacao(Boolean permiteEditarValorParcelaNegociacao) {
        this.permiteEditarValorParcelaNegociacao = permiteEditarValorParcelaNegociacao;
    }

    public Boolean getConfigurarProdutosEspecificos() {
        return configurarProdutosEspecificos;
    }

    public void setConfigurarProdutosEspecificos(Boolean configurarProdutosEspecificos) {
        this.configurarProdutosEspecificos = configurarProdutosEspecificos;
    }

    public Boolean getPermiteAlterarLancamentoContratosIguais() {
        return permiteAlterarLancamentoContratosIguais;
    }

    public void setPermiteAlterarLancamentoContratosIguais(Boolean permiteAlterarLancamentoContratosIguais) {
        this.permiteAlterarLancamentoContratosIguais = permiteAlterarLancamentoContratosIguais;
    }

    public Boolean getPesquisa() {
        return pesquisa;
    }

    public void setPesquisa(Boolean pesquisa) {
        this.pesquisa = pesquisa;
    }

    public Boolean getVendasOnline() {
        return vendasOnline;
    }

    public void setVendasOnline(Boolean vendasOnline) {
        this.vendasOnline = vendasOnline;
    }

    public Boolean getModeloOrcamento() {
        return modeloOrcamento;
    }

    public void setModeloOrcamento(Boolean modeloOrcamento) {
        this.modeloOrcamento = modeloOrcamento;
    }

    public Boolean getConfigurarClubeDeVantagens() {
        return configurarClubeDeVantagens;
    }

    public void setConfigurarClubeDeVantagens(Boolean configurarClubeDeVantagens) {
        this.configurarClubeDeVantagens = configurarClubeDeVantagens;
    }

    public Boolean getModuloNotaFiscal() {
        if (moduloNotaFiscal == null)
            moduloNotaFiscal = false;
        return moduloNotaFiscal;
    }

    public void setModuloNotaFiscal(Boolean moduloNotaFiscal) {
        this.moduloNotaFiscal = moduloNotaFiscal;
    }

    public Boolean getPermiteCancelarNotaFiscal() {
        return permiteCancelarNotaFiscal;
    }

    public void setPermiteCancelarNotaFiscal(Boolean permiteCancelarNotaFiscal) {
        this.permiteCancelarNotaFiscal = permiteCancelarNotaFiscal;
    }

    public Boolean getPermiteInutilizarNotaFiscal() {
        return permiteInutilizarNotaFiscal;
    }

    public void setPermiteInutilizarNotaFiscal(Boolean permiteInutilizarNotaFiscal) {
        this.permiteInutilizarNotaFiscal = permiteInutilizarNotaFiscal;
    }

    public Boolean getPermiteExcluirNotaFiscal() {
        return permiteExcluirNotaFiscal;
    }

    public void setPermiteExcluirNotaFiscal(Boolean permiteExcluirNotaFiscal) {
        this.permiteExcluirNotaFiscal = permiteExcluirNotaFiscal;
    }

    public Boolean getExcluirTokemGympass() {
        return excluirTokemGympass;
    }

    public void setExcluirTokemGympass(Boolean excluirTokemGympass) {
        this.excluirTokemGympass = excluirTokemGympass;
    }

    public Boolean getPermiteAlterarRPS() {
        return permiteAlterarRPS;
    }

    public void setPermiteAlterarRPS(Boolean permiteAlterarRPS) {
        this.permiteAlterarRPS = permiteAlterarRPS;
    }

    public Boolean getPermiteConferirNegociacaoSemInformarCartao() {
        return permiteConferirNegociacaoSemInformarCartao;
    }

    public void setPermiteConferirNegociacaoSemInformarCartao(Boolean permiteConferirNegociacaoSemInformarCartao) {
        this.permiteConferirNegociacaoSemInformarCartao = permiteConferirNegociacaoSemInformarCartao;
    }

    public Boolean getImportacao() {
        return importacao;
    }

    public void setImportacao(Boolean importacao) {
        this.importacao = importacao;
    }

    public Boolean getImportacaoProduto() {
        return importacaoProduto;
    }

    public void setImportacaoProduto(Boolean importacaoProduto) {
        this.importacaoProduto = importacaoProduto;
    }

    public Boolean getBiOperacaoExececoes() {
        return biOperacaoExececoes;
    }

    public void setBiOperacaoExececoes(Boolean biOperacaoExececoes) {
        this.biOperacaoExececoes = biOperacaoExececoes;
    }

    public Boolean getBiGestaoAcesso() {
        return biGestaoAcesso;
    }

    public void setBiGestaoAcesso(Boolean biGestaoAcesso) {
        this.biGestaoAcesso = biGestaoAcesso;
    }

    public Boolean getBiCobrancaConvenio() {
        return biCobrancaConvenio;
    }

    public void setBiCobrancaConvenio(Boolean biCobrancaConvenio) {
        this.biCobrancaConvenio = biCobrancaConvenio;
    }

    public Boolean getBiVerificarcaoClientes() {
        return biVerificarcaoClientes;
    }

    public void setBiVerificarcaoClientes(Boolean biVerificarcaoClientes) {
        this.biVerificarcaoClientes = biVerificarcaoClientes;
    }

    public Boolean getImportacaoColaborador() {
        return importacaoColaborador;
    }

    public void setImportacaoColaborador(Boolean importacaoColaborador) {
        this.importacaoColaborador = importacaoColaborador;
    }

    public Boolean getImportacaoFornecedor() {
        return importacaoFornecedor;
    }

    public void setImportacaoFornecedor(Boolean importacaoFornecedor) {
        this.importacaoFornecedor = importacaoFornecedor;
    }

    public Boolean getImportacaoConta() {
        return importacaoConta;
    }

    public void setImportacaoConta(Boolean importacaoConta) {
        this.importacaoConta = importacaoConta;
    }

    public Boolean getImportacaoTurma() {
        return importacaoTurma;
    }

    public void setImportacaoTurma(Boolean importacaoTurma) {
        this.importacaoTurma = importacaoTurma;
    }

    public Boolean getPermiteRelatorioClientesCobrancaBloqueada() {
        return permiteRelatorioClientesCobrancaBloqueada;
    }

    public void setPermiteRelatorioClientesCobrancaBloqueada(Boolean permiteRelatorioClientesCobrancaBloqueada) {
        this.permiteRelatorioClientesCobrancaBloqueada = permiteRelatorioClientesCobrancaBloqueada;
    }

    public Boolean getPermiteBloquearDesbloquearClienteCobrancaAutomatica() {
        return permiteBloquearDesbloquearClienteCobrancaAutomatica;
    }

    public void setPermiteBloquearDesbloquearClienteCobrancaAutomatica(Boolean permiteBloquearDesbloquearClienteCobrancaAutomatica) {
        this.permiteBloquearDesbloquearClienteCobrancaAutomatica = permiteBloquearDesbloquearClienteCobrancaAutomatica;
    }

    public Boolean getCrmQuarentena() {
        return crmQuarentena;
    }

    public void setCrmQuarentena(Boolean crmQuarentena) {
        this.crmQuarentena = crmQuarentena;
    }

    public Boolean getAlterarIdVindi() {
        return alterarIdVindi;
    }

    public void setAlterarIdVindi(Boolean alterarIdVindi) {
        this.alterarIdVindi = alterarIdVindi;
    }

    public Boolean getCrmEditarSimplesRegistro() {
        return crmEditarSimplesRegistro;
    }

    public void setCrmEditarSimplesRegistro(Boolean crmEditarSimplesRegistro) {
        this.crmEditarSimplesRegistro = crmEditarSimplesRegistro;
    }

    public Boolean getModuloPactoPay() {
        return moduloPactoPay;
    }

    public void setModuloPactoPay(Boolean moduloPactoPay) {
        this.moduloPactoPay = moduloPactoPay;
    }

    public Boolean getFluxoCaixa() {
        return fluxoCaixa;
    }

    public void setFluxoCaixa(Boolean fluxoCaixa) {
        this.fluxoCaixa = fluxoCaixa;
    }

    public Boolean getFornecedor() {
        return fornecedor;
    }

    public void setFornecedor(Boolean fornecedor) {
        this.fornecedor = fornecedor;
    }

    public Boolean getLancarContasPagar() {
        return lancarContasPagar;
    }

    public void setLancarContasPagar(Boolean lancarContasPagar) {
        this.lancarContasPagar = lancarContasPagar;
    }

    public Boolean getQuitarContasPagar() {
        return quitarContasPagar;
    }

    public void setQuitarContasPagar(Boolean quitarContasPagar) {
        this.quitarContasPagar = quitarContasPagar;
    }

    public Boolean getVisualizarLancamentos() {
        return visualizarLancamentos;
    }

    public void setVisualizarLancamentos(Boolean visualizarLancamentos) {
        this.visualizarLancamentos = visualizarLancamentos;
    }

    public Boolean getGestaoRecebiveis() {
        return gestaoRecebiveis;
    }

    public void setGestaoRecebiveis(Boolean gestaoRecebiveis) {
        this.gestaoRecebiveis = gestaoRecebiveis;
    }

    public Boolean getGestaoLotes() {
        return gestaoLotes;
    }

    public void setGestaoLotes(Boolean gestaoLotes) {
        this.gestaoLotes = gestaoLotes;
    }

    public Boolean getResumoContas() {
        return resumoContas;
    }

    public void setResumoContas(Boolean resumoContas) {
        this.resumoContas = resumoContas;
    }

    public Boolean getPlanoContas() {
        return planoContas;
    }

    public void setPlanoContas(Boolean planoContas) {
        this.planoContas = planoContas;
    }

    public Boolean getLancamentosAvulsos() {
        return lancamentosAvulsos;
    }

    public void setLancamentosAvulsos(Boolean lancamentosAvulsos) {
        this.lancamentosAvulsos = lancamentosAvulsos;
    }

    public Boolean getRateioIntegracao() {
        return rateioIntegracao;
    }

    public void setRateioIntegracao(Boolean rateioIntegracao) {
        this.rateioIntegracao = rateioIntegracao;
    }


    public Boolean getDesvincularUsuarioAcademia() {
        return desvincularUsuarioAcademia;
    }

    public void setDesvincularUsuarioAcademia(Boolean desvincularUsuarioAcademia) {
        this.desvincularUsuarioAcademia = desvincularUsuarioAcademia;
    }

    public Boolean getVendaRapidaTelaPadraoLancarContrato() {
        return vendaRapidaTelaPadraoLancarContrato;
    }

    public void setVendaRapidaTelaPadraoLancarContrato(Boolean vendaRapidaTelaPadraoLancarContrato) {
        this.vendaRapidaTelaPadraoLancarContrato = vendaRapidaTelaPadraoLancarContrato;
    }

    public Boolean getTreinoBI() {
        return treinoBI;
    }

    public void setTreinoBI(Boolean treinoBI) {
        this.treinoBI = treinoBI;
    }

    public Boolean getTreinoEmCasa() {
        return treinoEmCasa;
    }

    public void setTreinoEmCasa(Boolean treinoEmCasa) {
        this.treinoEmCasa = treinoEmCasa;
    }

    public Boolean getTreinoAcompanhamentoPersonal() {
        return treinoAcompanhamentoPersonal;
    }

    public void setTreinoAcompanhamentoPersonal(Boolean treinoAcompanhamentoPersonal) {
        this.treinoAcompanhamentoPersonal = treinoAcompanhamentoPersonal;
    }

    public Boolean getTreinoColaboradores() {
        return treinoColaboradores;
    }

    public void setTreinoColaboradores(Boolean treinoColaboradores) {
        this.treinoColaboradores = treinoColaboradores;
    }

    public Boolean getTreinoGestaoCreditos() {
        return treinoGestaoCreditos;
    }

    public void setTreinoGestaoCreditos(Boolean treinoGestaoCreditos) {
        this.treinoGestaoCreditos = treinoGestaoCreditos;
    }

    public Boolean getTreinoPersonais() {
        return treinoPersonais;
    }

    public void setTreinoPersonais(Boolean treinoPersonais) {
        this.treinoPersonais = treinoPersonais;
    }

    public Boolean getTreinoRelatorioAndamento() {
        return ************************;
    }

    public void setTreinoRelatorioAndamento(Boolean ************************) {
        this.************************ = ************************;
    }

    public Boolean getTreinoRelatorioAplicativosAtivos() {
        return treinoRelatorioAplicativosAtivos;
    }

    public void setTreinoRelatorioAplicativosAtivos(Boolean treinoRelatorioAplicativosAtivos) {
        this.treinoRelatorioAplicativosAtivos = treinoRelatorioAplicativosAtivos;
    }

    public Boolean getTreinoRelatorioGestaoCreditos() {
        return treinoRelatorioGestaoCreditos;
    }

    public void setTreinoRelatorioGestaoCreditos(Boolean treinoRelatorioGestaoCreditos) {
        this.treinoRelatorioGestaoCreditos = treinoRelatorioGestaoCreditos;
    }

    public Boolean getTreinoRelatorioIndicadoresCarteiraProfessores() {
        return treinoRelatorioIndicadoresCarteiraProfessores;
    }

    public void setTreinoRelatorioIndicadoresCarteiraProfessores(Boolean treinoRelatorioIndicadoresCarteiraProfessores) {
        this.treinoRelatorioIndicadoresCarteiraProfessores = treinoRelatorioIndicadoresCarteiraProfessores;
    }

    public Boolean getTreinoRelatorioIndicadoresAtividadesProfessores() {
        return treinoRelatorioIndicadoresAtividadesProfessores;
    }

    public void setTreinoRelatorioIndicadoresAtividadesProfessores(Boolean treinoRelatorioIndicadoresAtividadesProfessores) {
        this.treinoRelatorioIndicadoresAtividadesProfessores = treinoRelatorioIndicadoresAtividadesProfessores;
    }

    public Boolean getTreinoRelatorioProfessoresAlunosAvisosMedicos() {
        return treinoRelatorioProfessoresAlunosAvisosMedicos;
    }

    public void setTreinoRelatorioProfessoresAlunosAvisosMedicos(Boolean treinoRelatorioProfessoresAlunosAvisosMedicos) {
        this.treinoRelatorioProfessoresAlunosAvisosMedicos = treinoRelatorioProfessoresAlunosAvisosMedicos;
    }

    public Boolean getTreinoRelatorioProfessoresSubstitutos() {
        return treinoRelatorioProfessoresSubstitutos;
    }

    public void setTreinoRelatorioProfessoresSubstitutos(Boolean treinoRelatorioProfessoresSubstitutos) {
        this.treinoRelatorioProfessoresSubstitutos = treinoRelatorioProfessoresSubstitutos;
    }

    public Boolean getTreinoRelatorioRanking() {
        return treinoRelatorioRanking;
    }

    public void setTreinoRelatorioRanking(Boolean treinoRelatorioRanking) {
        this.treinoRelatorioRanking = treinoRelatorioRanking;
    }

    public Boolean getTreinoCadastroAparelhos() {
        return treinoCadastroAparelhos;
    }

    public void setTreinoCadastroAparelhos(Boolean treinoCadastroAparelhos) {
        this.treinoCadastroAparelhos = treinoCadastroAparelhos;
    }

    public Boolean getTreinoCadastroAtividades() {
        return treinoCadastroAtividades;
    }

    public void setTreinoCadastroAtividades(Boolean treinoCadastroAtividades) {
        this.treinoCadastroAtividades = treinoCadastroAtividades;
    }

    public Boolean getTreinoCadastroCategoriaAtividades() {
        return treinoCadastroCategoriaAtividades;
    }

    public void setTreinoCadastroCategoriaAtividades(Boolean treinoCadastroCategoriaAtividades) {
        this.treinoCadastroCategoriaAtividades = treinoCadastroCategoriaAtividades;
    }

    public Boolean getTreinoCadastroNiveis() {
        return treinoCadastroNiveis;
    }

    public void setTreinoCadastroNiveis(Boolean treinoCadastroNiveis) {
        this.treinoCadastroNiveis = treinoCadastroNiveis;
    }

    public Boolean getTreinoCadastroFichasPredefinidas() {
        return treinoCadastroFichasPredefinidas;
    }

    public void setTreinoCadastroFichasPredefinidas(Boolean treinoCadastroFichasPredefinidas) {
        this.treinoCadastroFichasPredefinidas = treinoCadastroFichasPredefinidas;
    }

    public Boolean getTreinoBiPersonalizado() {
        return treinoBiPersonalizado;
    }

    public void setTreinoBiPersonalizado(Boolean treinoBiPersonalizado) {
        this.treinoBiPersonalizado = treinoBiPersonalizado;
    }

    public void setTreinoCadastroProgramasPredefinidos(Boolean treinoCadastroProgramasPredefinidos) {
        this.treinoCadastroProgramasPredefinidos = treinoCadastroProgramasPredefinidos;
    }

    public Boolean getTreinoCadastroProgramasPredefinidos() {
        return treinoCadastroProgramasPredefinidos;
    }

    public void setAvaliacaoFisicaCadastroAnamnese(Boolean avaliacaoFisicaCadastroAnamnese) {
        this.avaliacaoFisicaCadastroAnamnese = avaliacaoFisicaCadastroAnamnese;
    }

    public Boolean getAvaliacaoFisicaCadastroAnamnese() {
        return avaliacaoFisicaCadastroAnamnese;
    }

    public void setAvaliacaoFisicaCadastroObjetivos(Boolean avaliacaoFisicaCadastroObjetivos) {
        this.avaliacaoFisicaCadastroObjetivos = avaliacaoFisicaCadastroObjetivos;
    }

    public Boolean getAvaliacaoFisicaCadastroObjetivos() {
        return avaliacaoFisicaCadastroObjetivos;
    }

    public Boolean getPactoPayBI() {
        return pactoPayBI;
    }

    public void setPactoPayBI(Boolean pactoPayBI) {
        this.pactoPayBI = pactoPayBI;
    }

    public Boolean getPactoPayCartaoCredito() {
        return pactoPayCartaoCredito;
    }

    public void setPactoPayCartaoCredito(Boolean pactoPayCartaoCredito) {
        this.pactoPayCartaoCredito = pactoPayCartaoCredito;
    }

    public Boolean getPactoPayParcelasEmAberto() {
        return pactoPayParcelasEmAberto;
    }

    public void setPactoPayParcelasEmAberto(Boolean pactoPayParcelasEmAberto) {
        this.pactoPayParcelasEmAberto = pactoPayParcelasEmAberto;
    }

    public Boolean getPactoPayReguaCobranca() {
        return pactoPayReguaCobranca;
    }

    public void setPactoPayReguaCobranca(Boolean pactoPayReguaCobranca) {
        this.pactoPayReguaCobranca = pactoPayReguaCobranca;
    }

    public Boolean getPactoPayCreditoOnline() {
        return pactoPayCreditoOnline;
    }

    public void setPactoPayCreditoOnline(Boolean pactoPayCreditoOnline) {
        this.pactoPayCreditoOnline = pactoPayCreditoOnline;
    }

    public Boolean getPactoPayPix() {
        return pactoPayPix;
    }

    public void setPactoPayPix(Boolean pactoPayPix) {
        this.pactoPayPix = pactoPayPix;
    }

    public Boolean getCrossBi() {
        return crossBi;
    }

    public void setCrossBi(Boolean crossBi) {
        this.crossBi = crossBi;
    }

    public Boolean getCrossWod() {
        return crossWod;
    }

    public void setCrossWod(Boolean crossWod) {
        this.crossWod = crossWod;
    }

    public Boolean getCrossMonitor() {
        return crossMonitor;
    }

    public void setCrossMonitor(Boolean crossMonitor) {
        this.crossMonitor = crossMonitor;
    }

    public Boolean getCrossCadastroAparelhos() {
        return crossCadastroAparelhos;
    }

    public void setCrossCadastroAparelhos(Boolean crossCadastroAparelhos) {
        this.crossCadastroAparelhos = crossCadastroAparelhos;
    }

    public Boolean getCrossCadastroAtividades() {
        return crossCadastroAtividades;
    }

    public void setCrossCadastroAtividades(Boolean crossCadastroAtividades) {
        this.crossCadastroAtividades = crossCadastroAtividades;
    }

    public Boolean getCrossCadastroBenchmarks() {
        return crossCadastroBenchmarks;
    }

    public void setCrossCadastroBenchmarks(Boolean crossCadastroBenchmarks) {
        this.crossCadastroBenchmarks = crossCadastroBenchmarks;
    }

    public Boolean getCrossCadastroTiposBenchmark() {
        return crossCadastroTiposBenchmark;
    }

    public void setCrossCadastroTiposBenchmark(Boolean crossCadastroTiposBenchmark) {
        this.crossCadastroTiposBenchmark = crossCadastroTiposBenchmark;
    }

    public Boolean getCrossCadastroTipoWod() {
        return crossCadastroTipoWod;
    }

    public void setCrossCadastroTipoWod(Boolean crossCadastroTipoWod) {
        this.crossCadastroTipoWod = crossCadastroTipoWod;
    }

    public Boolean getGraduacaoAvaliacoesProgresso() {
        return graduacaoAvaliacoesProgresso;
    }

    public void setGraduacaoAvaliacoesProgresso(Boolean graduacaoAvaliacoesProgresso) {
        this.graduacaoAvaliacoesProgresso = graduacaoAvaliacoesProgresso;
    }

    public Boolean getGraduacaoAtividades() {
        return graduacaoAtividades;
    }

    public void setGraduacaoAtividades(Boolean graduacaoAtividades) {
        this.graduacaoAtividades = graduacaoAtividades;
    }

    public Boolean getGraduacaoFichaTenica() {
        return graduacaoFichaTenica;
    }

    public void setGraduacaoFichaTenica(Boolean graduacaoFichaTenica) {
        this.graduacaoFichaTenica = graduacaoFichaTenica;
    }

    public Boolean getAvaliacaoFisicaBI() {
        return avaliacaoFisicaBI;
    }

    public void setAvaliacaoFisicaBI(Boolean avaliacaoFisicaBI) {
        this.avaliacaoFisicaBI = avaliacaoFisicaBI;
    }

    public Boolean getTreinoPrescricaoTreino() {
        return treinoPrescricaoTreino;
    }

    public void setTreinoPrescricaoTreino(Boolean treinoPrescricaoTreino) {
        this.treinoPrescricaoTreino = treinoPrescricaoTreino;
    }

    public void setTreinoCadastroAlunos(Boolean treinoCadastroAlunos) {
        this.treinoCadastroAlunos = treinoCadastroAlunos;
    }

    public Boolean getTreinoCadastroAlunos() {
        return treinoCadastroAlunos;
    }

    public Boolean getTreinoCadastroColaboradoresTreino() {
        return treinoCadastroColaboradoresTreino;
    }

    public void setTreinoCadastroColaboradoresTreino(Boolean treinoCadastroColaboradoresTreino) {
        this.treinoCadastroColaboradoresTreino = treinoCadastroColaboradoresTreino;
    }

    public Boolean getTreinoUsuarios() {
        return treinoUsuarios;
    }

    public void setTreinoUsuarios(Boolean treinoUsuarios) {
        this.treinoUsuarios = treinoUsuarios;
    }

    public void setTreinoPerfilAcesso(Boolean treinoPerfilAcesso) {
        this.treinoPerfilAcesso = treinoPerfilAcesso;
    }

    public Boolean getTreinoPerfilAcesso() {
        return treinoPerfilAcesso;
    }

    public Boolean getAgendaBi() {
        return agendaBi;
    }

    public void setAgendaBi(Boolean agendaBi) {
        this.agendaBi = agendaBi;
    }

    public Boolean getAgendaTvAula() {
        return agendaTvAula;
    }

    public void setAgendaTvAula(Boolean agendaTvAula) {
        this.agendaTvAula = agendaTvAula;
    }

    public Boolean getAgendaTvGestor() {
        return agendaTvGestor;
    }

    public void setAgendaTvGestor(Boolean agendaTvGestor) {
        this.agendaTvGestor = agendaTvGestor;
    }

    public Boolean getAgendaAulas() {
        return agendaAulas;
    }

    public void setAgendaAulas(Boolean agendaAulas) {
        this.agendaAulas = agendaAulas;
    }

    public Boolean getAgendaServicos() {
        return agendaServicos;
    }

    public void setAgendaServicos(Boolean agendaServicos) {
        this.agendaServicos = agendaServicos;
    }

    public Boolean getAgendaIndicadores() {
        return agendaIndicadores;
    }

    public void setAgendaIndicadores(Boolean agendaIndicadores) {
        this.agendaIndicadores = agendaIndicadores;
    }

    public Boolean getAgendaAulaExcluida() {
        return agendaAulaExcluida;
    }

    public void setAgendaAulaExcluida(Boolean agendaAulaExcluida) {
        this.agendaAulaExcluida = agendaAulaExcluida;
    }

    public Boolean getAgendaConfigurarAula() {
        return agendaConfigurarAula;
    }

    public void setAgendaConfigurarAula(Boolean agendaConfigurarAula) {
        this.agendaConfigurarAula = agendaConfigurarAula;
    }

    public Boolean getAgendaTipoAgendamento() {
        return agendaTipoAgendamento;
    }

    public void setAgendaTipoAgendamento(Boolean agendaTipoAgendamento) {
        this.agendaTipoAgendamento = agendaTipoAgendamento;
    }

    public Boolean getAgendaModalidade() {
        return agendaModalidade;
    }

    public void setAgendaModalidade(Boolean agendaModalidade) {
        this.agendaModalidade = agendaModalidade;
    }

    public Boolean getPessoa() {
        return pessoa;
    }

    public void setPessoa(Boolean pessoa) {
        this.pessoa = pessoa;
    }

    public Boolean getBloqueioCaixa() {
        return bloqueioCaixa;
    }

    public void setBloqueioCaixa(Boolean bloqueioCaixa) {
        this.bloqueioCaixa = bloqueioCaixa;
    }

    public Boolean getCampanhaDuracao() {
        return campanhaDuracao;
    }

    public void setCampanhaDuracao(Boolean campanhaDuracao) {
        this.campanhaDuracao = campanhaDuracao;
    }

    public Boolean getCentroCustos() {
        return centroCustos;
    }

    public void setCentroCustos(Boolean centroCustos) {
        this.centroCustos = centroCustos;
    }

    public Boolean getPerfilAcessoUnificado() {
        return perfilAcessoUnificado;
    }

    public void setPerfilAcessoUnificado(Boolean perfilAcessoUnificado) {
        this.perfilAcessoUnificado = perfilAcessoUnificado;
    }

    public Boolean getPerfilAcessoUnificadoSincronizado() {
        return perfilAcessoUnificadoSincronizado;
    }

    public void setPerfilAcessoUnificadoSincronizado(Boolean perfilAcessoUnificadoSincronizado) {
        this.perfilAcessoUnificadoSincronizado = perfilAcessoUnificadoSincronizado;
    }

    public Boolean getVisualizarCPFBusca() {
        return visualizarCPFBusca;
    }

    public void setVisualizarCPFBusca(Boolean visualizarCPFBusca) {
        this.visualizarCPFBusca = visualizarCPFBusca;
    }

    public Boolean getVerPendenciasClienteListaAcessos() {
        return verPendenciasClienteListaAcessos;
    }

    public void setVerPendenciasClienteListaAcessos(Boolean verPendenciasClienteListaAcessos) {
        verPendenciasClienteListaAcessos = verPendenciasClienteListaAcessos;
    }

    public Boolean getPermissaoAcessarSocialMailing() {
        return permissaoAcessarSocialMailing;
    }

    public void setPermissaoAcessarSocialMailing(Boolean permissaoAcessarSocialMailing) {
        this.permissaoAcessarSocialMailing = permissaoAcessarSocialMailing;
    }

    public Boolean getAlunoAbaNotaFiscal() {
        return alunoAbaNotaFiscal;
    }

    public void setAlunoAbaNotaFiscal(Boolean alunoAbaNotaFiscal) {
        this.alunoAbaNotaFiscal = alunoAbaNotaFiscal;
    }

    public Boolean getPactoPayReguaCobrancaDashboard() {
        return pactoPayReguaCobrancaDashboard;
    }

    public void setPactoPayReguaCobrancaDashboard(Boolean pactoPayReguaCobrancaDashboard) {
        this.pactoPayReguaCobrancaDashboard = pactoPayReguaCobrancaDashboard;
    }

    public Boolean getPactoPayReguaCobrancaConfigEmail() {
        return pactoPayReguaCobrancaConfigEmail;
    }

    public void setPactoPayReguaCobrancaConfigEmail(Boolean pactoPayReguaCobrancaConfigEmail) {
        this.pactoPayReguaCobrancaConfigEmail = pactoPayReguaCobrancaConfigEmail;
    }

    public Boolean getRelatorioBVs() {
        return relatorioBVs;
    }

    public void setRelatorioBVs(Boolean relatorioBVs) {
        this.relatorioBVs = relatorioBVs;
    }

    public Boolean getRelatorioClientesComAtestado() {
        return relatorioClientesComAtestado;
    }

    public void setRelatorioClientesComAtestado(Boolean relatorioClientesComAtestado) {
        this.relatorioClientesComAtestado = relatorioClientesComAtestado;
    }

    public Boolean getRelatorioClientesComBonus() {
        return relatorioClientesComBonus;
    }

    public void setRelatorioClientesComBonus(Boolean relatorioClientesComBonus) {
        this.relatorioClientesComBonus = relatorioClientesComBonus;
    }

    public Boolean getRelatorioClientesTrancados() {
        return relatorioClientesTrancados;
    }

    public void setRelatorioClientesTrancados(Boolean relatorioClientesTrancados) {
        this.relatorioClientesTrancados = relatorioClientesTrancados;
    }

    public Boolean getRelatorioDescontoPorOcupacao() {
        return relatorioDescontoPorOcupacao;
    }

    public void setRelatorioDescontoPorOcupacao(Boolean relatorioDescontoPorOcupacao) {
        this.relatorioDescontoPorOcupacao = relatorioDescontoPorOcupacao;
    }

    public Boolean getRelatorioFechamentoDeAcessos() {
        return relatorioFechamentoDeAcessos;
    }

    public void setRelatorioFechamentoDeAcessos(Boolean relatorioFechamentoDeAcessos) {
        this.relatorioFechamentoDeAcessos = relatorioFechamentoDeAcessos;
    }

    public Boolean getRelatorioIndicadorDeAcessos() {
        return relatorioIndicadorDeAcessos;
    }

    public void setRelatorioIndicadorDeAcessos(Boolean relatorioIndicadorDeAcessos) {
        this.relatorioIndicadorDeAcessos = relatorioIndicadorDeAcessos;
    }

    public Boolean getRelatorioDeRepasse() {
        return relatorioDeRepasse;
    }

    public void setRelatorioDeRepasse(Boolean relatorioDeRepasse) {
        this.relatorioDeRepasse = relatorioDeRepasse;
    }

    public Boolean getRelatorioDeVisitantes() {
        return relatorioDeVisitantes;
    }

    public void setRelatorioDeVisitantes(Boolean relatorioDeVisitantes) {
        this.relatorioDeVisitantes = relatorioDeVisitantes;
    }

    public Boolean getRelatorioDePedidosPinpad() {
        return relatorioDePedidosPinpad;
    }

    public void setRelatorioDePedidosPinpad(Boolean relatorioDePedidosPinpad) {
        this.relatorioDePedidosPinpad = relatorioDePedidosPinpad;
    }

    public Boolean getRelatorioDeTotalizadorDeTickets() {
        return relatorioDeTotalizadorDeTickets;
    }

    public void setRelatorioDeTotalizadorDeTickets(Boolean relatorioDeTotalizadorDeTickets) {
        this.relatorioDeTotalizadorDeTickets = relatorioDeTotalizadorDeTickets;
    }

    public Boolean getRelatorioDeTransacoesPix() {
        return relatorioDeTransacoesPix;
    }

    public void setRelatorioDeTransacoesPix(Boolean relatorioDeTransacoesPix) {
        this.relatorioDeTransacoesPix = relatorioDeTransacoesPix;
    }

    public Boolean getBiGymPass() {
        return biGymPass;
    }

    public void setBiGymPass(Boolean biGymPass) {
        this.biGymPass = biGymPass;
    }

    public Boolean getBiCicloDeVida() {
        return biCicloDeVida;
    }

    public void setBiCicloDeVida(Boolean biCicloDeVida) {
        this.biCicloDeVida = biCicloDeVida;
    }

    public Boolean getRelatorioDeCliente() {
        return relatorioDeCliente;
    }

    public void setRelatorioDeCliente(Boolean relatorioDeCliente) {
        this.relatorioDeCliente = relatorioDeCliente;
    }

    public Boolean getRelatorioDeConsultaRecibo() {
        return relatorioDeConsultaRecibo;
    }

    public void setRelatorioDeConsultaRecibo(Boolean relatorioDeConsultaRecibo) {
        this.relatorioDeConsultaRecibo = relatorioDeConsultaRecibo;
    }

    public Boolean getRelatorioDeArmarios() {
        return relatorioDeArmarios;
    }

    public void setRelatorioDeArmarios(Boolean relatorioDeArmarios) {
        this.relatorioDeArmarios = relatorioDeArmarios;
    }

    public Boolean getRelatorioDePrevisaoRenovacao() {
        return relatorioDePrevisaoRenovacao;
    }

    public void setRelatorioDePrevisaoRenovacao(Boolean relatorioDePrevisaoRenovacao) {
        this.relatorioDePrevisaoRenovacao = relatorioDePrevisaoRenovacao;
    }

    public Boolean getRelatorioDeFaturamentoRecebidoPeríodo() {
        return relatorioDeFaturamentoRecebidoPeríodo;
    }

    public void setRelatorioDeFaturamentoRecebidoPeríodo(Boolean relatorioDeFaturamentoRecebidoPeríodo) {
        this.relatorioDeFaturamentoRecebidoPeríodo = relatorioDeFaturamentoRecebidoPeríodo;
    }

    public Boolean getRelatorioDeMovimentacaoContaCorrenteCliente() {
        return relatorioDeMovimentacaoContaCorrenteCliente;
    }

    public void setRelatorioDeMovimentacaoContaCorrenteCliente(Boolean relatorioDeMovimentacaoContaCorrenteCliente) {
        this.relatorioDeMovimentacaoContaCorrenteCliente = relatorioDeMovimentacaoContaCorrenteCliente;
    }

    public Boolean getRelatorioDeProdutosComVigencia() {
        return relatorioDeProdutosComVigencia;
    }

    public void setRelatorioDeProdutosComVigencia(Boolean relatorioDeProdutosComVigencia) {
        this.relatorioDeProdutosComVigencia = relatorioDeProdutosComVigencia;
    }

    public Boolean getRelatorioDeClientesCancelados() {
        return relatorioDeClientesCancelados;
    }

    public void setRelatorioDeClientesCancelados(Boolean relatorioDeClientesCancelados) {
        this.relatorioDeClientesCancelados = relatorioDeClientesCancelados;
    }

    public Boolean getRelatorioDeTotalizadorDeAcessos() {
        return relatorioDeTotalizadorDeAcessos;
    }

    public void setRelatorioDeTotalizadorDeAcessos(Boolean relatorioDeTotalizadorDeAcessos) {
        this.relatorioDeTotalizadorDeAcessos = relatorioDeTotalizadorDeAcessos;
    }

    public Boolean getPermitirImprimirReciboEmBranco() {
        return permitirImprimirReciboEmBranco;
    }

    public void setPermitirImprimirReciboEmBranco(Boolean permitirImprimirReciboEmBranco) {
        this.permitirImprimirReciboEmBranco = permitirImprimirReciboEmBranco;
    }

    public Boolean getPermitirExportarDados() {
        return permitirExportarDados;
    }

    public void setPermitirExportarDados(Boolean permitirExportarDados) {
        this.permitirExportarDados = permitirExportarDados;
    }

    public Boolean getApresentarNfseRecibo() {
        return apresentarNfseRecibo;
    }

    public void setApresentarNfseRecibo(Boolean apresentarNfseRecibo) {
        this.apresentarNfseRecibo = apresentarNfseRecibo;
    }

    public Boolean getPermitirAvisosInternos() {
        return permitirAvisosInternos;
    }

    public void setPermitirAvisosInternos(Boolean permitirAvisosInternos) {
        this.permitirAvisosInternos = permitirAvisosInternos;
    }

    public Boolean getGestaoBoletosOnline() {
        return gestaoBoletosOnline;
    }

    public void setGestaoBoletosOnline(Boolean gestaoBoletosOnline) {
        this.gestaoBoletosOnline = gestaoBoletosOnline;
    }

    public Boolean getPermiteAlterarStatusNotaFiscal() {
        return permiteAlterarStatusNotaFiscal;
    }

    public void setPermiteAlterarStatusNotaFiscal(Boolean permiteAlterarStatusNotaFiscal) {
        this.permiteAlterarStatusNotaFiscal = permiteAlterarStatusNotaFiscal;
    }

    public Boolean getPermitirAtualizacaoContratoAssinado() {
        return permitirAtualizacaoContratoAssinado;
    }

    public void setPermitirAtualizacaoContratoAssinado(Boolean permitirAtualizacaoContratoAssinado) {
        this.permitirAtualizacaoContratoAssinado = permitirAtualizacaoContratoAssinado;
    }

    public Boolean getImpostoProduto() {
        return impostoProduto;
    }

    public void setImpostoProduto(Boolean impostoProduto) {
        this.impostoProduto = impostoProduto;
    }

    public Boolean getPermiteVisualizaSMDPeriodo() {
        return permiteVisualizaSMDPeriodo;
    }

    public void setPermiteVisualizaSMDPeriodo(Boolean permiteVisualizaSMDPeriodo) {
        this.permiteVisualizaSMDPeriodo = permiteVisualizaSMDPeriodo;
    }
}
