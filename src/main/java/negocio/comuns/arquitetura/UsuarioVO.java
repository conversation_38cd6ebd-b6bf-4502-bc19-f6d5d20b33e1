package negocio.comuns.arquitetura;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.NotificacaoUsuarioUCPTO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.Usuario;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.WordUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.security.MessageDigest;
import java.sql.Connection;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Reponsável por manter os dados da entidade Usuario. Classe do tipo VO - Value
 * Object composta pelos atributos da entidade com visibilidade protegida e os
 * métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class UsuarioVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private String nome;
    private String username;
    @NaoControlarLogAlteracao
    private String senha;
    @NaoControlarLogAlteracao
    private String pin;
    private String senhaConfirmar;
    private String linguagem;
    private String siglaIdiomaNovaPlataforma;
    private boolean alterouSenha = false;
    private boolean alterouPIN = false;
    @ListJson(clazz = UsuarioPerfilAcessoVO.class)
    private List usuarioPerfilAcessoVOs;
    private Boolean administrador;
    @NaoControlarLogAlteracao
    private String tipoUsuario;
    @ChaveEstrangeira
    @FKJson
    private ColaboradorVO colaboradorVO;
    @ChaveEstrangeira
    private ClienteVO clienteVO;
    private Boolean usuarioEscolhido;
    //atributo que sera usado para fazer validações se o usuario poderá trabalhar com metas;
    @ChaveEstrangeira
    private PermissaoAcessoMenuVO permissaoAcessoMenuVO;
    private boolean pseudo = false;
    private Date dataUltimaAlteracaoSenha;
    private Boolean permiteAlterarPropriaSenha = true;
    @Lista
    @NaoControlarLogAlteracao
    @ListJson(clazz = HorarioAcessoSistemaVO.class)
    private List<HorarioAcessoSistemaVO> usuarioHorarioAcessoSistemaVOs;
    private String serviceUsuario = "";
    @NaoControlarLogAlteracao
    private String serviceSenha = "";
    @NaoControlarLogAlteracao
    private Integer nrMensagensNaoLidas = 0;
    @NaoControlarLogAlteracao
    private List<String> dicasEsconder;
    private String perfisUsuario;
    @NaoControlarLogAlteracao
    private String senhaNaoCriptografada;
    @NaoControlarLogAlteracao
    private EmailVO emailVO;
    @NaoControlarLogAlteracao
    private String chave;
    @NaoControlarLogAlteracao
    private Integer codEmpresaLogada;
    @NaoControlarLogAlteracao
    private String timeZoneEmpresa;
    @NaoControlarLogAlteracao
    private String modulosHabilitados;
    private String userOamd;
    @NaoControlarLogAlteracao
    private String email;
    @NaoControlarLogAlteracao
    private TelefoneVO telefone;

    @NaoControlarLogAlteracao
    private List<ColaboradorVO> listaVinculoConsultor; // Atributo transient.

    @NaoControlarLogAlteracao
    private UsuarioVO usuarioLogado; // Atributo transient.

    @NaoControlarLogAlteracao
    private List<NotificacaoUsuarioUCPTO> notificacoesUsuarioUCP;
    private List<SocialMailGrupoVO> listaGrupoSolicitacao;
    private List<SocialMailGrupoVO> listaSolicitacoesRespostasNaoCompreendidas;
    private boolean existeConversaSolicitacao = false;
    @NaoControlarLogAlteracao
    private String permissoesOAMD = null;

    @NaoControlarLogAlteracao
    private TipoColaboradorEnum tipoColaboradorEnum; // Atributo transient.

    private boolean selecionado = false;

    @NaoControlarLogAlteracao
    private boolean encriptarSenha = true;

    @NaoControlarLogAlteracao
    private Date dataApresentacaoModalSolicitacoes; // Atributo transient.

    private boolean pedirSenhaFuncionalidade = false;

    @NaoControlarLogAlteracao
    private boolean apresentarModalSolicitacoesPendentes = false; // Atributo transient.

    private boolean permissaoAlterarRPS = false;
    private boolean permiteExecutarProcessos = false;
    private boolean internacional = false;
    private boolean showModalInativar = false;
    private boolean showModalPlanos = false;
    private Date ultimoAcesso;
    private Integer diasSemAcessar;
    private Date exibirModalInativarUsersHoje;
    private Date dataExibirModalPlanos;
    private String usuarioGeral;
    @NaoControlarLogAlteracao
    private boolean usuarioMultiChave = false; // Atributo transient.

    @NaoControlarLogAlteracao
    private String tokenNT;
    @NaoControlarLogAlteracao
    protected String nomeCapitalizado;

    private boolean permiteExclusaoCliente;

    @NaoControlarLogAlteracao
    private Double porcetagemDescontoContrato = 0.0;
    private Integer perfilTw;
    private Integer statusTw;
    private Integer tipoTw;
    private Boolean acessoPactoApp = false;
    private Boolean permiteAcionarBotaoPanico = false;


    /**
     * Construtor padrão da classe
     * <code>Usuario</code>. Cria uma nova instância desta entidade,
     * inicializando automaticamente seus atributos (Classe VO).
     */
    public UsuarioVO() {
        super();
        inicializarDados();
    }

    public UsuarioVO(Integer codigo) {
        this.codigo = codigo;
    }

    public String getSituacao_Usuario() {
        return getColaboradorVO().getSituacao_Apresentar();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>UsuarioVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada
     *                            aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     *                            ocorrido.
     */
    public static void validarDados(UsuarioVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getTipoUsuario().equals("") && !obj.getAdministrador()) {
            throw new ConsistirException("O campo TIPO USUÁRIO (Usuário) deve ser informado.");
        }
        /*if (obj.getNome().equals("")) {
         throw new ConsistirException("O campo NOME DA PESSOA (Usuário) deve ser informado.");
         }*/
        if (obj.getTipoUsuario().equals("CI")) {
            if (obj.getClienteVO().getPessoa().getNome().equals("")) {
                throw new ConsistirException("O campo NOME DO CLIENTE (Usuário) deve ser informado.");
            }
            if (Pattern.compile("\\d*[!@#$%&\\*\\{\\}\\[\\]\\?:><,|\\\\/\"\\+]\\d*").matcher(obj.getClienteVO().getPessoa().getNome()).find()) {
                throw new ConsistirException("O campo NOME DO CLIENTE (Usuário) não pode conter caracteres especiais (!@#$%&*}{][?:><,\\|/\"+).");
            }
        }
        if (obj.getTipoUsuario().equals("NC")) {
            if (obj.getColaboradorVO().getPessoa().getNome().equals("")) {
                throw new ConsistirException("O campo NOME DO COLABORADOR (Usuário) deve ser informado.");
            }
            if (Pattern.compile("\\d*[!@#$%&\\*\\{\\}\\[\\]\\?:><,|\\\\/\"\\+]\\d*").matcher(obj.getColaboradorVO().getPessoa().getNome()).find()) {
                throw new ConsistirException("O campo NOME DO COLABORADOR (Usuário) não pode conter caracteres especiais (!@#$%&*}{][?:><,\\|/\"+).");
            }
        }
        if (obj.getTipoUsuario().equals("NC") && obj.getColaboradorVO().getPessoa().getDataNasc() == null) {
            throw new ConsistirException("O campo DATA NASCIMENTO (Usuário) deve ser informado.");
        }
        if (!obj.isInternacional()) {
            if (obj.getTipoUsuario().equals("NC") && obj.getColaboradorVO().getPessoa().getCfp().equals("")) {
                throw new ConsistirException("O campo CPF (Usuário) deve ser informado.");
            }
        }

        if (obj.getUsername().equals("")) {
            throw new ConsistirException("O campo NOME DE USUÁRIO (Usuário) deve ser informado.");
        }

        if (!Normalizer.isNormalized(obj.getUsername(), Normalizer.Form.NFD)) {
            throw new ConsistirException("O campo NOME DE USUÁRIO (Usuário) não deve conter caracteres com acentuação.");
        }

        if (obj.getSenha().equals("")) {
            throw new ConsistirException("O campo SENHA (Usuário) deve ser informado.");
        }

        if (obj.getTipoUsuario() == null) {
            obj.setTipoUsuario("");
        }

        if (!obj.getAdministrador() && obj.getUsuarioPerfilAcessoVOs().isEmpty() && (!UtilReflection.objetoMaiorQueZero(obj.getColaboradorVO(), "getCodigo()") || obj.getColaboradorVO().getSituacao().equals("AT"))) {
            throw new ConsistirException("Nenhum perfil de acesso informado (aba Usuário Perfil Acesso)");
        }

        if (obj.getServiceUsuario() != null && obj.getServiceSenha() != null
                && !obj.getServiceUsuario().isEmpty() && obj.getServiceSenha().isEmpty()) {
            throw new ConsistirException("O usuário do Service foi informado mas não a senha.");
        }

        if (obj.isNovoObj()) {
            if (obj.getEmailVO().getEmail().equals("")) {
                throw new ConsistirException("O campo EMAIL (Usuário) deve ser informado.");
            } else {
                if (!UteisValidacao.validaEmail(obj.getEmailVO().getEmail())) {
                    throw new ConsistirException("O campo EMAIL (Usuário) é inválido!");
                }
            }

            if (obj.getUsername().length() < 4) {
                throw new ConsistirException("O campo NOME DE USUÁRIO (Usuário) deve ser conter pelo menos 4 caracteres.");
            }
        }

        if (obj.isAlterouPIN() && !UteisValidacao.validarPinSeguro(obj.getPin())) {
            throw new Exception("O PIN deve ser apenas números, ter quatro caracteres e não pode ser em sequência crescente ou decrescente!");
        }


    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados(boolean incluir, final Connection con) throws Exception {
        setNome(getNome().toUpperCase());
        if (incluir || !MessageDigest.isEqual(
                new Usuario(con).consultarSenhaPorCodigoUsuario(getCodigo()).getBytes(), getSenha().getBytes())) {
            if (isEncriptarSenha()) {
                setSenha(getSenha().toUpperCase());
            }
        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
//        setCodigo(new Integer(0));
//        setNome("");
//        setUsername(new String(""));
//        setSenha("");
//        setUsuarioPerfilAcessoVOs(new ArrayList());
//        setClienteVO(new ClienteVO());
//        setColaboradorVO(new ColaboradorVO());
//        setTipoUsuario("");
//        setAdministrador(new Boolean(false));
//        setAlterarSenha(new Boolean(false));
    }

    public void adicionarObjUsuarioPerfilAcessoVOs(UsuarioPerfilAcessoVO obj) throws Exception {

        if ((getTipoUsuario() == null)) {
            setTipoUsuario("");
        }
        if (getTipoUsuario().equals("") && (!getAdministrador())) {
            throw new ConsistirException("O campo TIPO USUÁRIO (Usuário) deve ser informado.");
        }
        if (getTipoUsuario().equals("CI") && getClienteVO().getCodigo() == 0) {
            throw new ConsistirException("O campo Cliente (Usuário) deve ser informado.");
        }
        if (getTipoUsuario().equals("CE") && getColaboradorVO().getCodigo() == 0) {
            throw new ConsistirException("O campo Colaborador (Usuário) deve ser informado.");
        }
        if (getTipoUsuario().equals("NC") && getColaboradorVO().getPessoa().getNome().equals("")) {
            throw new ConsistirException("O campo Nome (Usuário) deve ser informado.");
        }
        if (getTipoUsuario().equals("NC") && getColaboradorVO().getPessoa().getDataNasc() == null) {
            throw new ConsistirException("O campo Data Nascimento (Usuário) deve ser informado.");
        }
        if (!isInternacional()) {
            if (getTipoUsuario().equals("NC") && getColaboradorVO().getPessoa().getCfp().equals("")) {
                throw new ConsistirException("O campo CPF (Usuário) deve ser informado.");
            }
        }
        if (!getAdministrador()) {
            UsuarioPerfilAcessoVO.validarDados(obj);
        }
        int index = 0;
        Iterator i = getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO objExistente = (UsuarioPerfilAcessoVO) i.next();
            if (objExistente.getEmpresa().getCodigo().equals(obj.getEmpresa().getCodigo())) {
                getUsuarioPerfilAcessoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getUsuarioPerfilAcessoVOs().add(obj);
    }

    /**
     * Retorna o objeto da classe
     * <code>PerfilAcesso</code> relacionado com (
     * <code>Usuario</code>).
     */
    public String getSenha() {
        if (senha == null) {
            senha = "";
        }
        return (senha);
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getUsername() {
        if (username == null) {
            username = "";
        }
        return (username);
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPrimeiroNomeConcatenado() {
        if (nome == null) {
            nome = "";
        }
        return (Uteis.obterPrimeiroNomeConcatenadoSobreNome(nome));
    }

    public String getNomeSemAcentuacao() {
        if (nome == null) {
            nome = "";
        }
        return (Uteis.retirarAcentuacao(nome));
    }


    public String getNomeCapitalizado() {
        return nomeCapitalizado;
    }

    public void setNomeCapitalizado(String nomeCapitalizado) {
        this.nomeCapitalizado = nomeCapitalizado;
    }


    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return (nome);
    }

    public String getNomePrimeiraLetraMaiuscula() {
        if(nomeCapitalizado == null || nomeCapitalizado.equals("")) {
            StringBuilder novoNomeCapitalizado = new StringBuilder();
            String aux = "";
            if (getNome().contains("*")) {
                for (String letra : getNome().toLowerCase().split("")) {
                    if ((letra.matches(".*[a-z].*")) && (aux.equals(" ") || aux.equals("") || !aux.matches(".*[a-z].*"))) {
                        letra = WordUtils.capitalize(letra);
                    }
                    novoNomeCapitalizado.append(letra);
                    aux = letra.toLowerCase();
                }
                setNomeCapitalizado(novoNomeCapitalizado.toString());
                return nomeCapitalizado;
            }
            setNomeCapitalizado(WordUtils.capitalize(getNome().toLowerCase()));
        }
        return nomeCapitalizado;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List<UsuarioPerfilAcessoVO> getUsuarioPerfilAcessoVOs() {
        if (usuarioPerfilAcessoVOs == null) {
            usuarioPerfilAcessoVOs = new ArrayList();
        }
        return usuarioPerfilAcessoVOs;
    }

    public void setUsuarioPerfilAcessoVOs(List usuarioPerfilAcessoVOs) {
        this.usuarioPerfilAcessoVOs = usuarioPerfilAcessoVOs;
    }

    public List getUsuarioHorarioAcessoSistemaVOs() {
        if (usuarioHorarioAcessoSistemaVOs == null) {
            usuarioHorarioAcessoSistemaVOs = new ArrayList<>();
        }
        return usuarioHorarioAcessoSistemaVOs;
    }

    public void setUsuarioHorarioAcessoSistemaVOs(List usuarioHorarioAcessoSistemaVOs) {
        this.usuarioHorarioAcessoSistemaVOs = usuarioHorarioAcessoSistemaVOs;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public ColaboradorVO getColaboradorVO() {
        if (colaboradorVO == null) {
            colaboradorVO = new ColaboradorVO();
        }
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public String getTipoUsuario() {
        if (tipoUsuario == null) {
            tipoUsuario = "";
        }
        return tipoUsuario;
    }

    public void setTipoUsuario(String tipoUsuario) {
        this.tipoUsuario = tipoUsuario;
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo
     * com um domínio específico. Com base no valor de armazenamento do atributo
     * esta função é capaz de retornar o de apresentação correspondente. Útil
     * para campos como sexo, escolaridade, etc.
     */
    public String getTipoUsuario_Apresentar() {
        if (tipoUsuario == null) {
            tipoUsuario = "";
        }
        if (tipoUsuario.equals("CI")) {
            return "Cliente";
        }

        if (tipoUsuario.equals("CE")) {
            return "Colaborador Existente";
        }
        if (tipoUsuario.equals("NC")) {
            return "Novo Colaborador";
        }
        return (tipoUsuario);
    }

    public Boolean getAdministrador() {
        if (administrador == null) {
            administrador = false;
        }
        return administrador;
    }

    public void setAdministrador(Boolean administrador) {
        this.administrador = administrador;
    }

    /**
     * @return the usuarioEscolhido
     */
    public Boolean getUsuarioEscolhido() {
        if (usuarioEscolhido == null) {
            usuarioEscolhido = false;
        }
        return usuarioEscolhido;
    }

    /**
     * @param usuarioEscolhido the usuarioEscolhido to set
     */
    public void setUsuarioEscolhido(Boolean usuarioEscolhido) {
        this.usuarioEscolhido = usuarioEscolhido;
    }

    public String getNomeAbreviado() {
        return Uteis.obterPrimeiroNomeConcatenadoSobreNome(this.getNome());
    }

    public PermissaoAcessoMenuVO getPermissaoAcessoMenuVO() {
        if (permissaoAcessoMenuVO == null) {
            permissaoAcessoMenuVO = new PermissaoAcessoMenuVO();
        }
        return permissaoAcessoMenuVO;
    }

    public void setPermissaoAcessoMenuVO(PermissaoAcessoMenuVO permissaoAcessoMenuVO) {
        this.permissaoAcessoMenuVO = permissaoAcessoMenuVO;
    }

    public UsuarioVO clone() throws CloneNotSupportedException {
        return (UsuarioVO) super.clone();
    }

    public boolean isPseudo() {
        return pseudo;
    }

    public void setPseudo(boolean pseudo) {
        this.pseudo = pseudo;
    }

    public String getNomesPerfisUsuario() {
        String nomes = "";
        if ((getUsuarioPerfilAcessoVOs() != null)
                && (!getUsuarioPerfilAcessoVOs().isEmpty())) {
            for (Iterator<UsuarioPerfilAcessoVO> it = getUsuarioPerfilAcessoVOs().iterator(); it.hasNext(); ) {
                UsuarioPerfilAcessoVO obj = it.next();
                nomes += obj.getPerfilAcesso().getNome() + ",";
            }
            if (nomes.substring(nomes.length() - 1).equals(",")) {
                nomes = nomes.substring(0, nomes.length() - 1);
            }
        }
        return nomes;
    }

    public void setPerfisUsuario(String perfisUsuario) {
        this.perfisUsuario = perfisUsuario;
    }

    public String getPerfisUsuario() {
        if (perfisUsuario == null) {
            return " ";
        }
        return perfisUsuario;
    }

    public String toString() {
        return !UteisValidacao.emptyString(this.userOamd) ? String.format("%s (%s)",
                this.getNomeAbreviado(), this.getUserOamd()) : this.getNomeAbreviado();
    }

    /**
     * @return the dataUltimaAlteracaoSenha
     */
    public Date getDataUltimaAlteracaoSenha() {
        return dataUltimaAlteracaoSenha;
    }

    /**
     * @param dataUltimaAlteracaoSenha the dataUltimaAlteracaoSenha to set
     */
    public void setDataUltimaAlteracaoSenha(Date dataUltimaAlteracaoSenha) {
        this.dataUltimaAlteracaoSenha = dataUltimaAlteracaoSenha;
    }

    public String getDataUltimaAlteracaoSenha_Apresentar() {
        if (getDataUltimaAlteracaoSenha() != null) {
            return Uteis.getDataComHora(getDataUltimaAlteracaoSenha());
        }
        return "";
    }

    /**
     * @return the permiteAlterarPropriaSenha
     */
    public Boolean getPermiteAlterarPropriaSenha() {
        return permiteAlterarPropriaSenha;
    }

    /**
     * @param permiteAlterarPropriaSenha the permiteAlterarPropriaSenha to set
     */
    public void setPermiteAlterarPropriaSenha(Boolean permiteAlterarPropriaSenha) {
        this.permiteAlterarPropriaSenha = permiteAlterarPropriaSenha;
    }

    public String getServiceSenha() {
        return serviceSenha;
    }

    public void setServiceSenha(String serviceSenha) {
        this.serviceSenha = serviceSenha;
    }

    public String getServiceUsuario() {
        return serviceUsuario;
    }

    public void setServiceUsuario(String serviceUsuario) {
        this.serviceUsuario = serviceUsuario;
    }

    public void setNrMensagensNaoLidas(Integer nrMensagensNaoLidas) {
        this.nrMensagensNaoLidas = nrMensagensNaoLidas;
    }

    public Integer getNrMensagensNaoLidas() {
        return nrMensagensNaoLidas;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof UsuarioVO) {
            UsuarioVO aux = (UsuarioVO) obj;
            return aux.getCodigo().intValue() == this.getCodigo().intValue();
        }

        return false;
    }

    public boolean isAlterouSenha() {
        return alterouSenha;
    }

    public void setAlterouSenha(boolean alterouSenha) {
        this.alterouSenha = alterouSenha;
    }

    public boolean isAlterouPIN() {
        return alterouPIN;
    }

    public void setAlterouPIN(boolean alterouPIN) {
        this.alterouPIN = alterouPIN;
    }

    public List<String> getDicasEsconder() {
        if (dicasEsconder == null) {
            dicasEsconder = new ArrayList<>();
        }
        return dicasEsconder;
    }

    public void setDicasEsconder(List<String> dicasEsconder) {
        this.dicasEsconder = dicasEsconder;
    }

    public String getSenhaNaoCriptografada() {
        if (senhaNaoCriptografada == null) {
            senhaNaoCriptografada = "";
        }
        return senhaNaoCriptografada;
    }

    public void setSenhaNaoCriptografada(String senhaNaoCriptografada) {
        this.senhaNaoCriptografada = senhaNaoCriptografada;
    }

    public Boolean getNovoUsuario() {
        return getCodigo() == 0;
    }

    public EmailVO getEmailVO() {
        if (emailVO == null) {
            emailVO = new EmailVO();
        }
        return emailVO;
    }

    public void setEmailVO(EmailVO emailVO) {
        this.emailVO = emailVO;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getCodEmpresaLogada() {
        return codEmpresaLogada;
    }

    public void setCodEmpresaLogada(Integer codEmpresaLogada) {
        this.codEmpresaLogada = codEmpresaLogada;
    }

    public String getTimeZoneEmpresa() {
        return timeZoneEmpresa;
    }

    public void setTimeZoneEmpresa(String timeZoneEmpresa) {
        this.timeZoneEmpresa = timeZoneEmpresa;
    }

    public String getModulosHabilitados() {
        return modulosHabilitados;
    }

    public void setModulosHabilitados(String modulosHabilitados) {
        this.modulosHabilitados = modulosHabilitados;
    }

    public String toJSON() throws JSONException {
        JSONObject json = new JSONObject();
        json.put("id", this.codigo);
        json.put("k", this.chave);
        json.put("un", this.username);
        json.put("pwd", this.getSenhaNaoCriptografada());
        json.put("empresa", this.getCodEmpresaLogada());
        json.put("tz", this.getTimeZoneEmpresa());
        json.put(JSFUtilities.MODULOS, this.getModulosHabilitados());
        json.put(JSFUtilities.DICAS_ESCONDER, new JSONArray(dicasEsconder));
        return json.toString();
    }

    public String getUserOamd() {
        if (userOamd == null) {
            return "";
        }
        return userOamd;
    }

    public void setUserOamd(String userOamd) {
        this.userOamd = userOamd;
    }

    public List<ColaboradorVO> getListaVinculoConsultor() {
        return listaVinculoConsultor;
    }

    public void setListaVinculoConsultor(List<ColaboradorVO> listaVinculoConsultor) {
        this.listaVinculoConsultor = listaVinculoConsultor;
    }

    public UsuarioVO getUsuarioLogado() {
        return usuarioLogado;
    }

    public void setUsuarioLogado(UsuarioVO usuarioLogado) {
        this.usuarioLogado = usuarioLogado;
    }

    public Integer getUsuarioPerfilAcessoVOsSize() {
        return getUsuarioPerfilAcessoVOs().size();
    }


    public String getSenhaConfirmar() {
        if (senhaConfirmar == null) {
            senhaConfirmar = "";
        }
        return senhaConfirmar;
    }

    public void setSenhaConfirmar(String senhaConfirmar) {
        this.senhaConfirmar = senhaConfirmar;
    }

    public List<NotificacaoUsuarioUCPTO> getNotificacoesUsuarioUCP() {
        if (notificacoesUsuarioUCP == null) {
            notificacoesUsuarioUCP = new ArrayList<NotificacaoUsuarioUCPTO>();
        }
        return notificacoesUsuarioUCP;
    }

    public void setNotificacoesUsuarioUCP(List<NotificacaoUsuarioUCPTO> notificacoesUsuarioUCP) {
        this.notificacoesUsuarioUCP = notificacoesUsuarioUCP;
    }

    public Integer getTotalNotificacoesUsuarioUCP() {
        return getNotificacoesUsuarioUCP().size();
    }

    public Integer getTotalGeralNotificaoesUsuarioUCP() {
        Integer total = 0;
        for (NotificacaoUsuarioUCPTO noti : getNotificacoesUsuarioUCP()) {
            total += noti.getListaNotificacoes().size();
        }
        return total + getTotalGruposSolicitacao();
    }

    public List<SocialMailGrupoVO> getListaGrupoSolicitacao() {
        if (listaGrupoSolicitacao == null) {
            listaGrupoSolicitacao = new ArrayList<SocialMailGrupoVO>();
        }
        return listaGrupoSolicitacao;
    }

    public void setListaGrupoSolicitacao(List<SocialMailGrupoVO> listaGrupoSolicitacao) {
        this.listaGrupoSolicitacao = listaGrupoSolicitacao;
    }

    public Integer getTotalGruposSolicitacao() {
        return getListaGrupoSolicitacao().size();
    }

    public boolean getUsuarioPACTOBR() {
        return getUsername().equalsIgnoreCase("PACTOBR");
    }

    public boolean getUsuarioAdminPACTO() {
        return getUsername().equalsIgnoreCase("ADMIN");
    }

    public static UsuarioVO getUsuarioAdmin() {
        UsuarioVO usuarioAdmin = new UsuarioVO();
        usuarioAdmin.setCodigo(1);
        usuarioAdmin.setUsername("admin");
        usuarioAdmin.setNome("ADMINISTRADOR");
        usuarioAdmin.setAdministrador(true);
        return usuarioAdmin;
    }

    public boolean getUsuarioRecorrencia() {
        return getUsername().equalsIgnoreCase("RECOR");
    }

    public boolean isExisteConversaSolicitacao() {
        return existeConversaSolicitacao;
    }

    public void setExisteConversaSolicitacao(boolean existeConversaSolicitacao) {
        this.existeConversaSolicitacao = existeConversaSolicitacao;
    }

    public Date getUltimoAcesso() {
        return ultimoAcesso;
    }

    public void setUltimoAcesso(Date ultimoAcesso) {
        this.ultimoAcesso = ultimoAcesso;
    }

    public String getPermissoesOAMD() {
        return permissoesOAMD;
    }

    public void setPermissoesOAMD(String permissoesOAMD) {
        this.permissoesOAMD = permissoesOAMD;
    }

    public Boolean getPermiteOperacaoOAMD(final String nomeRecurso) {
        if (permissoesOAMD != null && !permissoesOAMD.isEmpty()) {
            List<String> lista = Arrays.asList(permissoesOAMD.split(","));
            for (String permissao : lista) {
                if (permissao.contains(nomeRecurso)) {
                    return true;
                }
            }
        }
        return false;
    }

    public Boolean getPermiteOperacaoOAMDEquals(final String nomeRecurso) {
        if (nomeRecurso.equals("PERMITE_ALTERAR_CONVENIO_STONE")) {
            Uteis.logarDebug("PERMITE_ALTERAR_CONVENIO_STONE IDENTIFICAR USUARIO OAMD CONVENIO COBRANCA | validando Permissões: " + permissoesOAMD);
        }
        if (nomeRecurso.equals("PERMITE_ALTERAR_CONVENIO")) {
            Uteis.logarDebug("PERMITE_ALTERAR_CONVENIO IDENTIFICAR USUARIO OAMD CONVENIO COBRANCA | validando Permissões: " + permissoesOAMD);
        }
        if (permissoesOAMD != null && !permissoesOAMD.isEmpty()) {
            List<String> lista = Arrays.asList(permissoesOAMD.split(","));
            for (String permissao : lista) {
                if (permissao.replaceAll("\"","").
                        replaceAll("\\[","").
                        replaceAll("\\]","").
                        equals(nomeRecurso)) {
                    return true;
                }
            }
        }
        return false;
    }

    public Boolean getPermiteOperacaoAjusteBD() {
        //apenas pelo OAMD e com a permissão adequada
        if (UteisValidacao.emptyString(userOamd) && getUsuarioPactoSolucoes()) {//em ambiente desenv
            return true;
        } else {
            return userOamd != null && getPermiteOperacaoOAMD("EXECUTAR_AJUSTE_BD");
        }
    }

    public TipoColaboradorEnum getTipoColaboradorEnum() {
        return tipoColaboradorEnum;
    }

    public void setTipoColaboradorEnum(TipoColaboradorEnum tipoColaboradorEnum) {
        this.tipoColaboradorEnum = tipoColaboradorEnum;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public boolean isEncriptarSenha() {
        return encriptarSenha;
    }

    public void setEncriptarSenha(boolean encriptarSenha) {
        this.encriptarSenha = encriptarSenha;
    }

    public List<SocialMailGrupoVO> getListaSolicitacoesRespostasNaoCompreendidas() {
        if (listaSolicitacoesRespostasNaoCompreendidas == null) {
            listaSolicitacoesRespostasNaoCompreendidas = new ArrayList<SocialMailGrupoVO>();
        }
        return listaSolicitacoesRespostasNaoCompreendidas;
    }

    public void setListaSolicitacoesRespostasNaoCompreendidas(List<SocialMailGrupoVO> listaSolicitacoesRespostasNaoCompreendidas) {
        this.listaSolicitacoesRespostasNaoCompreendidas = listaSolicitacoesRespostasNaoCompreendidas;
    }

    public Date getDataApresentacaoModalSolicitacoes() {
        return dataApresentacaoModalSolicitacoes;
    }

    public void setDataApresentacaoModalSolicitacoes(Date dataApresentacaoModalSolicitacoes) {
        this.dataApresentacaoModalSolicitacoes = dataApresentacaoModalSolicitacoes;
    }

    public boolean isApresentarModalSolicitacoesPendentes() {
        return apresentarModalSolicitacoesPendentes;
    }

    public void setApresentarModalSolicitacoesPendentes(boolean apresentarModalSolicitacoesPendentes) {
        this.apresentarModalSolicitacoesPendentes = apresentarModalSolicitacoesPendentes;
    }

    public boolean isPedirSenhaFuncionalidade() {
        return pedirSenhaFuncionalidade;
    }

    public void setPedirSenhaFuncionalidade(boolean pedirSenhaFuncionalidade) {
        this.pedirSenhaFuncionalidade = pedirSenhaFuncionalidade;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isPermissaoAlterarRPS() {
        return permissaoAlterarRPS;
    }

    public void setPermissaoAlterarRPS(boolean permissaoAlterarRPS) {
        this.permissaoAlterarRPS = permissaoAlterarRPS;
    }

    public boolean isPermiteExecutarProcessos() {
        return permiteExecutarProcessos;
    }

    public void setPermiteExecutarProcessos(boolean permiteExecutarProcessos) {
        this.permiteExecutarProcessos = permiteExecutarProcessos;
    }

    public boolean isInternacional() {
        return internacional;
    }

    public void setInternacional(boolean internacional) {
        this.internacional = internacional;
    }

    public boolean getPossuiPerfilAcessoAdministrador() {
        if (CollectionUtils.isEmpty(getUsuarioPerfilAcessoVOs())) {
            return false;
        }

        for (final UsuarioPerfilAcessoVO usuarioPerfilAcessoVO : (List<UsuarioPerfilAcessoVO>) getUsuarioPerfilAcessoVOs()) {
            if (usuarioPerfilAcessoVO.getPerfilAcesso().getTipo() == PerfilUsuarioEnum.ADMINISTRADOR) {
                return true;
            }
        }

        return false;
    }

    public boolean getUsuarioPactoSolucoes() {
        return getUsuarioPACTOBR() || getUsuarioAdminPACTO() || getUsuarioRecorrencia();
    }

    public String getLinguagem() {
        return linguagem;
    }

    public void setLinguagem(String linguagem) {
        this.linguagem = linguagem;
    }

    public TelefoneVO getTelefone() {
        return telefone;
    }

    public void setTelefone(TelefoneVO telefone) {
        this.telefone = telefone;
    }

    public String getTokenNT() {
        return tokenNT;
    }

    public void setTokenNT(String tokenNT) {
        this.tokenNT = tokenNT;
    }

    public String getSiglaIdiomaNovaPlataforma() {
        return siglaIdiomaNovaPlataforma;
    }

    public void setSiglaIdiomaNovaPlataforma(String siglaIdiomaNovaPlataforma) {
        this.siglaIdiomaNovaPlataforma = siglaIdiomaNovaPlataforma;
    }

    public Integer getDiasSemAcessar() {
        return diasSemAcessar;
    }

    public void setDiasSemAcessar(Integer diasSemAcessar) {
        this.diasSemAcessar = diasSemAcessar;
    }

    public boolean isModalInativar() {
        return showModalInativar;
    }

    public void setShowModalInativar(boolean showModalInativar) {
        this.showModalInativar = showModalInativar;
    }

    public boolean isShowModalPlanos() {
        return showModalPlanos;
    }

    public void setShowModalPlanos(boolean showModalPlanos) {
        this.showModalPlanos = showModalPlanos;
    }

    public Date getDataExibirModalPlanos() {
        return dataExibirModalPlanos;
    }

    public void setDataExibirModalPlanos(Date dataExibirModalPlanos) {
        this.dataExibirModalPlanos = dataExibirModalPlanos;
    }

    public Date getExibirModalInativarUsersHoje() {
        return exibirModalInativarUsersHoje;
    }


    public void setExibirModalInativarUsersHoje(Date exibirModalInativarUsersHoje) {
        this.exibirModalInativarUsersHoje = exibirModalInativarUsersHoje;
    }

    public String getPin() {
        if (pin == null) {
            pin = "";
        }
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public boolean isUsuarioLogadoPodeAlterarConvenio() {
        return this.getPermiteOperacaoOAMDEquals("PERMITE_ALTERAR_CONVENIO");
    }

    public boolean isUsuarioLogadoPodeAlterarCodigoAutenticacaoStone() {
        return this.getPermiteOperacaoOAMDEquals("PERMITE_ALTERAR_CONVENIO_STONE");
    }

    public boolean isUsuarioLogadoPodeAlterarCodigoAutenticacaoFacilitePay() {
        return this.getPermiteOperacaoOAMDEquals("PERMITE_ALTERAR_CONVENIO_FACILITE");
    }

    public boolean isUsuarioLogadoPodeConfigurarEmergencia() {
        Uteis.logar("PERMITE_CONFIGURAR_EMERGENCIA is " + getPermiteOperacaoOAMDEquals("PERMITE_CONFIGURAR_EMERGENCIA").toString());
        return this.getPermiteOperacaoOAMDEquals("PERMITE_CONFIGURAR_EMERGENCIA");
    }

    public boolean isIgnorarTokenOperacao() {
        return this.getPermiteOperacaoOAMDEquals("PERMITE_IGNORAR_TOKEN_OPERACOES");
    }

    public boolean isUsuarioLogadoPodeVisualizarCredenciaisOriginaisConvenio() {
        return this.getPermiteOperacaoOAMDEquals("PERMITE_VISUALIZAR_CREDENCIAIS_ORIGINAIS_CONVENIO");
    }

    public String getUsuarioGeral() {
        if (usuarioGeral == null) {
            usuarioGeral = "";
        }
        return usuarioGeral;
    }

    public void setUsuarioGeral(String usuarioGeral) {
        this.usuarioGeral = usuarioGeral;
    }

    public boolean isUsuarioMultiChave() {
        return usuarioMultiChave;
    }

    public void setUsuarioMultiChave(boolean usuarioMultiChave) {
        this.usuarioMultiChave = usuarioMultiChave;
    }


    public boolean isPermiteExclusaoCliente() {
        return permiteExclusaoCliente;
    }

    public void setPermiteExclusaoCliente(boolean permiteExclusaoCliente) {
        this.permiteExclusaoCliente = permiteExclusaoCliente;
    }

    public Double getPorcetagemDescontoContrato() {
        return porcetagemDescontoContrato;
    }

    public void setPorcetagemDescontoContrato(Double porcetagemDescontoContrato) {
        this.porcetagemDescontoContrato = porcetagemDescontoContrato;
    }

    public Integer getPerfilTw() {
        return perfilTw;
    }

    public void setPerfilTw(Integer perfilTw) {
        this.perfilTw = perfilTw;
    }

    public Integer getStatusTw() {
        return statusTw;
    }

    public void setStatusTw(Integer statusTw) {
        this.statusTw = statusTw;
    }

    public Integer getTipoTw() {
        return tipoTw;
    }

    public void setTipoTw(Integer tipoTw) {
        this.tipoTw = tipoTw;
    }

    public Boolean getAcessoPactoApp() {
        if (acessoPactoApp == null) {
            acessoPactoApp = false;
        }
        return acessoPactoApp;
    }

    public void setAcessoPactoApp(Boolean acessoPactoApp) {
        this.acessoPactoApp = acessoPactoApp;
    }

    public boolean isUsuarioLogadoPodeCadastrarConfigNotaCnpjDiferente() {
        return this.getPermiteOperacaoOAMDEquals("PERMITE_CADASTRAR-CONFIG-CNPJ-DIFERENTE");
    }

    public Boolean getPermiteAcionarBotaoPanico() {
        if (permiteAcionarBotaoPanico == null) {
            permiteAcionarBotaoPanico = false;
        }
        return permiteAcionarBotaoPanico;
    }

    public void setPermiteAcionarBotaoPanico(Boolean permiteBotaoPanico) {
        this.permiteAcionarBotaoPanico = permiteBotaoPanico;
    }
}
