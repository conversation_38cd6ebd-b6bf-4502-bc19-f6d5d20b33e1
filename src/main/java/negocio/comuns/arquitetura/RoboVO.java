/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TemporalRemocaoPontoEnum;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.TrancamentoContratoVO;
import negocio.comuns.contrato.ValidacaoContratoOperacao;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.ClienteMensagem;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.ResultSet;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class RoboVO extends SuperVO {

    protected Integer codigo;
    protected Date dia;
    protected String texto;
    protected Boolean RotinaProcessada;
    protected ConfiguracaoSistemaVO configuracaoSistema;
    protected List<EmpresaVO> listaEmpresa;
    protected Boolean atualizarTabelaSituacaoContrato;
    protected Date dataHoraInicio;
    protected Date dataHoraFim;
    protected Boolean rotinaProcessadaParcialmente;
    protected String textoErroProcessamento;
    //By: Waller Maciel
    //Poderá ser falso em ambiente clusterizado, não precisa ter a thread
    //rodando em mais de uma instância (seria redundante)
    //será setado, por script de deployment em servidor de aplicação que suporte clustering
    private boolean usarRobo = true;
    private boolean deveGerarSintetico = true;
    private boolean forcarGeracaoRetroativa = false;
    private boolean processarValidador = false; // isso define que o processamento do robo é soliciatado pelo verificador de inconsistencias
    private List<ClienteVO> listaClientes = new ArrayList<ClienteVO>();
    private List<ContratoVO> listaContratos = new ArrayList<ContratoVO>();
    private String clienteAtualizados = ""; // clientea que já foram atualizados pelo sintetico
    private String clienteJaProcessados = ""; // clientea que já processaram frequencimetro;


    public RoboVO() throws Exception {
        inicializarDados();
        inicializarFacades();
        usarRobo = (this.getClass().getResource("useRobo.key") != null);
    }

    public void inicializarConfiguracaoSistema() throws Exception {
        setConfiguracaoSistema(getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_ROBO));
    }

    public void inicializarDados() throws Exception {
        setCodigo(new Integer(0));
        setDia(Calendario.hoje());
        setDataHoraInicio(Calendario.hoje());
        //setDataHoraFim(Calendario.hoje());
        setTexto("");
        setRotinaProcessada(false);
        setAtualizarTabelaSituacaoContrato(false);
        inicializarConfiguracaoSistema();
        setListaEmpresa(new ArrayList<EmpresaVO>());
    }

    public void realizarUpperCaseDados() {
        setTexto(getTexto().toUpperCase());
        setTextoErroProcessamento(getTextoErroProcessamento().toUpperCase());
    }

    /**
     * Metodo que vai validar os contratos que começam hoje assim alterando a
     * situação do cliente que agora passar a ser ativo. ha alteração do cliente
     * será direto por um sql
     *
     * @throws java.lang.Exception
     */
    public void validarExisteOperacaoContratoAgendado() throws Exception {
        for (EmpresaVO empresa : getListaEmpresa()) {
            if (isProcessamentoControlado()) {
                for (ClienteVO cli : this.getListClientesControlado()) {
                    atualizarAlunosComContratoIniciandoNoDia(getDia(), empresa.getCodigo(), cli.getCodigo());
                }
            } else {
                if (empresa.isAtiva()) {
                    atualizarAlunosComContratoIniciandoNoDia(getDia(), empresa.getCodigo(), null);
                }
            }

            obterContratoComOperacaoDeTrancamentoOuCancelamentoParaHoje(empresa);
        }
    }
    
    public void validarSituacoesInconsistentes() throws Exception {
        for (EmpresaVO empresa : getListaEmpresa()) {
            if(isProcessamentoControlado()){
                for(ClienteVO cli : this.getListClientesControlado()){
                    atualizarAlunosComSituacoesInconsistentes(getDia(), empresa.getCodigo(),cli.getCodigo());
                }
            } else {
                atualizarAlunosComSituacoesInconsistentes(getDia(), empresa.getCodigo(), null);
            }
        }
    }

    /**
     * Será consultados por empresa os contratos que tenha operação de
     * trancamento e cancelamento a ser realizada no dia de hoje.
     *
     * @param empresa
     * @throws java.lang.Exception
     */
    public void obterContratoComOperacaoDeTrancamentoOuCancelamentoParaHoje(EmpresaVO empresa) throws Exception {
        List<ContratoVO> listaContrato = this.isProcessamentoControlado() ? this.getListContratosControlado()
                : getFacade().getContrato().consultarContratoMudancaDeSituacao(getDia(), empresa.getCodigo(), "TR",  false, Uteis.NIVELMONTARDADOS_ROBO);
        if(!this.isProcessamentoControlado()){
            listaContrato.addAll(getFacade().getContrato().consultarContratoMudancaDeSituacao(getDia(), empresa.getCodigo(), "CA",  false, Uteis.NIVELMONTARDADOS_ROBO));
        }
        for (ContratoVO contrato : listaContrato) {
            List<ContratoOperacaoVO> listaOperacao = getFacade().getContratoOperacao().consultarPorContrato(contrato.getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (ContratoOperacaoVO operacao : listaOperacao) {
                if (Calendario.menorOuIgual(operacao.getDataInicioEfetivacaoOperacao(), getDia())) {
                    if (operacao.getTrancamento() && Calendario.maiorOuIgual(operacao.getDataFimEfetivacaoOperacao(), getDia()) && !contrato.getSituacao().equals("TR")) {
                        realizarOperacaoTrancamento(contrato, empresa, operacao.getDataInicioEfetivacaoOperacao());
                    }
                    if (operacao.getCancelamento()) {
                        if(!contrato.getSituacao().equals("CA")){
                            realizarOperacaoCancelamento(contrato, empresa,(Calendario.maior(contrato.getVigenciaDe(),operacao.getDataInicioEfetivacaoOperacao()) ? contrato.getVigenciaDe() : operacao.getDataInicioEfetivacaoOperacao()));
                        }
                        continue;
                    }
                }
            }
        }
    }

    private void atualizarSintetico(final SituacaoClienteSinteticoEnum tipo, final int pessoa) throws Exception {
        ClienteVO cli = getFacade().getSituacaoClienteSinteticoDW().consultarClientePreparado(pessoa);
        if (cli.getCodigo() != 0) {
            getFacade().getZWFacade().atualizarSintetico(cli, getDia(), tipo, true);
            if (tipo.equals(SituacaoClienteSinteticoEnum.GRUPO_TODOS)) {
                clienteAtualizados += "," + cli.getCodigo();
            }
        }
    }

    public void realizarOperacaoTrancamento(ContratoVO contrato, EmpresaVO empresa, Date inicioOperacao) throws Exception {
        contrato.setSituacao("TR");
        contrato.setDataPrevistaRenovar(inicioOperacao);
        contrato.setDataPrevistaRematricula(inicioOperacao);
        getFacade().getContrato().alterarSituacaoContrato(contrato, false);
        getFacade().getContrato().alterarDatasVigenciaContrato(contrato, false);
        if (empresa.getPermiteContratosConcomintante()) {
            Integer nrContratos = getFacade().getContrato().obterQuantidadeDeContratoPorCliente(contrato.getPessoa().getCodigo(), contrato.getEmpresa().getCodigo(), getDia(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (nrContratos == 1) {
                getFacade().getCliente().alterarSituacaoClienteQueContratoOperacaoComecamHojeSemCommit("TR", contrato.getCodigo(), empresa.getCodigo());
            } else {
                ContratoVO ct = getFacade().getContrato().obterContratoVigenteConcomitante(contrato.getPessoa().getCodigo(), contrato.getEmpresa().getCodigo(), getDia(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
                if (ct.getCodigo().equals(contrato.getCodigo())) {
                    getFacade().getCliente().alterarSituacaoClienteQueContratoOperacaoComecamHojeSemCommit("TR", contrato.getCodigo(), empresa.getCodigo());
                }
            }
        } else {
            getFacade().getCliente().alterarSituacaoClienteQueContratoOperacaoComecamHojeSemCommit("TR", contrato.getCodigo(), empresa.getCodigo());
        }
        atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, contrato.getPessoa().getCodigo());
        this.atualizarListasProcessamentoControlado();
    }

    public void realizarOperacaoCancelamento(ContratoVO contrato, EmpresaVO empresa, Date inicioOperacao) throws Exception {
        try {
            contrato.setSituacao("CA");
            contrato.setVigenciaAteAjustada(inicioOperacao);
            contrato.setDataPrevistaRenovar(inicioOperacao);
            contrato.setDataPrevistaRematricula(inicioOperacao);
            getFacade().getContrato().alterarSituacaoContrato(contrato, false);
            getFacade().getContrato().alterarDatasVigenciaContrato(contrato, false);
            if (empresa.getPermiteContratosConcomintante()) {
                Integer nrContratos = getFacade().getContrato().obterQuantidadeDeContratoPorCliente(contrato.getPessoa().getCodigo(), contrato.getEmpresa().getCodigo(), getDia(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (nrContratos == 1) {
                    getFacade().getCliente().alterarSituacaoClienteQueContratoOperacaoComecamHojeSemCommit("IN", contrato.getCodigo(), empresa.getCodigo());
                    zerarHistoricoPontuacao(contrato, empresa);
                } else {
                    ContratoVO ct = getFacade().getContrato().obterContratoVigenteConcomitante(contrato.getPessoa().getCodigo(), contrato.getEmpresa().getCodigo(), getDia(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (ct.getCodigo().equals(contrato.getCodigo())) {
                        getFacade().getCliente().alterarSituacaoClienteQueContratoOperacaoComecamHojeSemCommit("IN", contrato.getCodigo(), empresa.getCodigo());
                        zerarHistoricoPontuacao(contrato, empresa);
                    }
                }
            } else {
                getFacade().getCliente().alterarSituacaoClienteQueContratoOperacaoComecamHojeSemCommit("IN", contrato.getCodigo(), empresa.getCodigo());
                zerarHistoricoPontuacao(contrato, empresa);
            }

            atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, contrato.getPessoa().getCodigo());
            this.atualizarListasProcessamentoControlado();
        } catch (Exception e) {
            throw e;
        }
    }

    private void zerarHistoricoPontuacao(ContratoVO contrato, EmpresaVO empresa) throws Exception {
        //Nova Configuração no Clube de Vantagens para zerar pontos quando ...
        if(empresa.getZerarPontosAposVencimento()==TemporalRemocaoPontoEnum.QUANDO_CANCELADO_DESISTENTE ) {
            if (!getFacade().getContrato().existeAlgumContratoConcomitanteVigenteOuVencidoPessoaNaData(contrato.getPessoa().getCodigo(), contrato.getCodigo(), getDia())) {
                getFacade().getHistoricoPontos().zerarPontuacaoRobo(contrato.getPessoa());
            }
        } else if (empresa.getZerarPontosAposVencimento()==TemporalRemocaoPontoEnum.QUANDO_CANCELADO_VENCIDO) {
            if (!getFacade().getContrato().existeAlgumContratoConcomitanteVigentePessoaNaData(contrato.getPessoa().getCodigo(), contrato.getCodigo(), getDia())) {
                getFacade().getHistoricoPontos().zerarPontuacaoRobo(contrato.getPessoa());
            }
        }
    }

    public void gerarRegistroSituacaoAnaliticoDW() throws Exception {
        for (EmpresaVO empresa : getListaEmpresa()) {
            //metodos comentados por SituacaoContratoAnaliticoDW foi abolido pelo Histórico de Contrato e Contrato Operacao
            removerFreePassVencido(empresa);
            obterQuantidadeClienteTrancado(empresa);
            registarContratosAvencerVencidosDesistentes(empresa);
            obterQuantidadeClienteAtivo(empresa);
            //obterQuantidadeClienteInativo(empresa);
        }
    }

    public void removerFreePassVencido(EmpresaVO empresa) throws Exception {
        if (!empresa.isAtiva()) {
            return;
        }
        StringBuilder sqlCondicao = new StringBuilder();
        sqlCondicao.append("freePass is not null and empresa = ").append(empresa.getCodigo());
        sqlCondicao.append(" AND pessoa IN (SELECT pessoa FROM periodoacessocliente WHERE tipoacesso = 'PL' AND datafinalacesso :: DATE < '").append(Uteis.getDataJDBC(getDia())).append("') ");
        sqlCondicao.append(" AND pessoa NOT IN (SELECT pessoa FROM periodoacessocliente WHERE tipoacesso = 'PL' AND datafinalacesso :: DATE >= '").append(Uteis.getDataJDBC(getDia())).append("') ");

        if (this.isProcessamentoControlado()) {
            List<Integer> codigos = new ArrayList<Integer>();
            for (ClienteVO cliente : this.getListClientesControlado()) {
                codigos.add(cliente.getCodigo());
            }

            if (codigos.size() > 0) {
                Integer[] ids = new Integer[codigos.size()];
                ids = codigos.toArray(ids);

                sqlCondicao.append("and codigo in (").append(Uteis.splitFromArray(ids, false)).append(")");
            }
        }

        List<ClienteVO> listaClientes = getFacade().getCliente().consultar(sqlCondicao.toString(), Uteis.NIVELMONTARDADOS_ROBO);
        if (!listaClientes.isEmpty()) {
            for (ClienteVO cliente : listaClientes) {
                // cliente nao esta mais de free pass entao salvo no banco nulo nos campos chave referenciados
                this.removerFreePass(cliente);
                atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_TODOS, cliente.getPessoa().getCodigo());
            }
        }
    }
    /*WM - 25/10/2010 - Método aparentemente redundante ao Cliente.removerFreePass.
     * Acontece que este aqui estará dentro de uma transação, evita DeadLock no SGBD e,
     * reusa a conexão do Robo facade.
     **/

    public void removerFreePass(ClienteVO obj) throws Exception {
        getFacade().getCliente().removerFreePass(obj);
    }

    public void obterQuantidadeClienteAtivo(EmpresaVO empresa) throws Exception {
        List<ClienteVO> listaClienteAtivo = this.isProcessamentoControlado() ? this.getListClientesControlado()
                : getFacade().getCliente().consultarClientePorSituacaoContrato("AT", empresa.getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_ROBO);
        if (empresa.getPermiteContratosConcomintante()) {
            for (ClienteVO cliente : listaClienteAtivo) {
                try {
                    validarContratosConcomitantes(empresa, cliente);
                } catch (Exception e) {
                    setTextoErroProcessamento(getTextoErroProcessamento() + "Erro obterQuantidadeClienteAtivo cliente -> " + cliente.getCodigo() + ": " + e.getMessage() + "\n");
                    setRotinaProcessadaParcialmente(true);
                }
            }
        } else {
            for (ClienteVO cliente : listaClienteAtivo) {
                try {
                    validarContratosSemConcomitantes(empresa, cliente);
                } catch (Exception e) {
                    setTextoErroProcessamento(getTextoErroProcessamento() + "Erro obterQuantidadeClienteAtivo cliente -> " + cliente.getCodigo() + ": " + e.getMessage() + "\n");
                    setRotinaProcessadaParcialmente(true);
                }
            }
        }
    }

    public void validarContratosConcomitantes(EmpresaVO empresa, ClienteVO cliente) throws Exception {
        ContratoVO contrato = getFacade().getContrato().consultarPorSituacaoCodigoPessoa(cliente.getPessoa().getCodigo(), "AT", false, Uteis.NIVELMONTARDADOS_ROBO);
        if (contrato.getCodigo() != 0) {
            if ((Uteis.getCompareData(getDia(), contrato.getVigenciaAteAjustada()) > 0) && contrato.getContratoResponsavelRenovacaoMatricula() != 0) {
                contrato.setSituacao("IN");
                getFacade().getContrato().alterarSituacaoContrato(contrato, false);
                atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, contrato.getPessoa().getCodigo());
            } else if ((Uteis.getCompareData(getDia(), contrato.getVigenciaAteAjustada()) > 0) && contrato.getContratoResponsavelRenovacaoMatricula() == 0) {
                validarContratoComAtivoSeEstaVencido(contrato, empresa, cliente);
            }
        }
        validarSituacaoContratoAnterior(contrato);
    }

    public void validarContratosSemConcomitantes(EmpresaVO empresa, ClienteVO cliente) throws Exception {
        try {
            ContratoVO contrato = getFacade().getContrato().consultarUltimoContratoVigerntePorCodigoPessoa(
                    cliente.getPessoa().getCodigo(), "AT", true, false, Uteis.NIVELMONTARDADOS_ROBO, false);

            if (contrato.getCodigo().intValue() != 0) {
                if ((Uteis.getCompareData(getDia(), contrato.getVigenciaAteAjustada()) > 0)
                        && (contrato.getContratoResponsavelRenovacaoMatricula().intValue() == 0
                        && contrato.getContratoResponsavelRematriculaMatricula().intValue() == 0)) {
                    validarContratoComAtivoSeEstaVencido(contrato, empresa, cliente);
                }
            }

            validarSituacaoContratoAnterior(contrato);
        } catch (Exception e) {
            Uteis.logar(null, "Erro no cliente -> " + cliente.getCodigo());
            throw e;
        }

    }

    public void validarContratoComAtivoSeEstaVencido(ContratoVO contrato, EmpresaVO empresa, ClienteVO cliente) throws Exception {
        Date dataBackup = (Date) getDia().clone();
        try {
            Date dataVencido;
            if (empresa.getCarenciaRenovacao() != 0) {
                dataVencido = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), empresa.getCarenciaRenovacao());
            } else {
                dataVencido = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), getConfiguracaoSistema().getCarenciaRenovacao());
            }
            if (forcarGeracaoRetroativa) {
                if (Uteis.getCompareData(getDia(), contrato.getVigenciaAteAjustada()) > 0) {
                    setDia(dataVencido);
                    int cod = contrato.getCodigo();
                    if ((!contrato.existeContratoSucessorIniciadoNestaData(getDia()))
                            && (!getFacade().getContratoOperacao().existeOperacaoRetroativoAEstaData(cod, "CA", getDia()))
                            && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(cod, "TR", getDia()))
                            && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(cod, "TV", getDia()))) {

                        cliente.setSituacao("IN");
                        getFacade().getCliente().alterarSituacaoClienteSemPessoaCommit(cliente);
                        if (Calendario.maiorOuIgual(dataBackup, dataVencido)){
                            if(empresa.getZerarPontosAposVencimento()==TemporalRemocaoPontoEnum.QUANDO_CANCELADO_DESISTENTE) {
                                if (!getFacade().getContrato().existeAlgumContratoConcomitanteVigenteOuVencidoPessoaNaData(contrato.getPessoa().getCodigo(), contrato.getCodigo(), getDia())) {
                                    getFacade().getHistoricoPontos().zerarPontuacaoRobo(contrato.getPessoa());
                                }
                            }
                        } else {
                            if(empresa.getZerarPontosAposVencimento()==TemporalRemocaoPontoEnum.QUANDO_CANCELADO_VENCIDO) {
                                if(!getFacade().getContrato().existeAlgumContratoConcomitanteVigentePessoaNaData(contrato.getPessoa().getCodigo(), contrato.getCodigo(), getDia())) {
                                    getFacade().getHistoricoPontos().zerarPontuacaoRobo(contrato.getPessoa());
                                }
                            }
                        }

                        if (!contrato.getSituacao().equals("CA")) {
                            contrato.setSituacao("IN");
                            getFacade().getContrato().alterarSituacaoContrato(contrato, false);
                            atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, contrato.getPessoa().getCodigo());
                        }
                    }

                }
            } else {
                if ((Uteis.getCompareData(getDia(), contrato.getVigenciaAteAjustada()) > 0) && (Uteis.getCompareData(getDia(), dataVencido) <= 0)) {
                    int cod = contrato.getCodigo();
                    if ((!contrato.existeContratoSucessorIniciadoNestaData(getDia()))
                            && (!getFacade().getContratoOperacao().existeOperacaoRetroativoAEstaData(
                            cod, "CA", getDia()))
                            && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(
                            cod, "TR", getDia()))
                            && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(
                            cod, "TV", getDia()))) {

                        cliente.setSituacao("IN");
                        getFacade().getCliente().alterarSituacaoClienteSemPessoaCommit(cliente);
                        if (Calendario.maiorOuIgual(dataBackup, dataVencido)){
                            if(empresa.getZerarPontosAposVencimento()==TemporalRemocaoPontoEnum.QUANDO_CANCELADO_DESISTENTE) {
                                if(!getFacade().getContrato().existeAlgumContratoConcomitanteVigenteOuVencidoPessoaNaData(contrato.getPessoa().getCodigo(), contrato.getCodigo(), getDia())) {
                                    getFacade().getHistoricoPontos().zerarPontuacaoRobo(contrato.getPessoa());
                                }
                            }
                        } else {
                            if(empresa.getZerarPontosAposVencimento()==TemporalRemocaoPontoEnum.QUANDO_CANCELADO_VENCIDO) {
                                if(!getFacade().getContrato().existeAlgumContratoConcomitanteVigentePessoaNaData(contrato.getPessoa().getCodigo(), contrato.getCodigo(), getDia())) {
                                    getFacade().getHistoricoPontos().zerarPontuacaoRobo(contrato.getPessoa());
                                }
                            }
                        }
                        if (!contrato.getSituacao().equals("CA")) {
                            contrato.setSituacao("IN");
                            getFacade().getContrato().alterarSituacaoContrato(contrato, false);
                            atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO,
                                    contrato.getPessoa().getCodigo());
                        }
                    }
                }

            }
        } finally {
            setDia(dataBackup);
        }
    }

    /**
     * metodo que vai validar se o contrato anterior ja esta finalizado
     */
    public boolean validarSituacaoContratoAnterior(ContratoVO contra) throws Exception {
        try {
            if(UteisValidacao.notEmptyNumber(contra.getContratoBaseadoRenovacao())){
                ContratoVO contrato = getFacade().getContrato().consultarContratoPorContratoBaseadoRenovacao(contra.getContratoBaseadoRenovacao(), false, Uteis.NIVELMONTARDADOS_ROBO);
                if (contrato.getCodigo().intValue() != 0 && contrato.getSituacao().equals("AT") && (Uteis.getCompareData(getDia(), contrato.getVigenciaAteAjustada()) > 0)) {
                    contrato.setSituacao("IN");
                    getFacade().getContrato().alterarSituacaoContrato(contrato, false);
                    atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO,
                            contrato.getPessoa().getCodigo());
                    return true;
                }
            } else if(Calendario.igual(contra.getVigenciaDe(), getDia() )){ //matriculas ou rematriculas futuras que foram lançadas para  alunos que estavam inativos
                atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO,
                        contra.getPessoa().getCodigo());
            }


        } catch (Exception e) {
            throw e;
        }
        return false;
    }

    public void obterQuantidadeClienteTrancado(EmpresaVO empresa) throws Exception {
        List<ClienteVO> listaClienteTrancado = this.isProcessamentoControlado() ? this.getListClientesControlado()
                : getFacade().getCliente().consultarClientePorSituacaoContrato("TR", empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_ROBO);
        for (ClienteVO cliente : listaClienteTrancado) {
            List<ContratoVO> contratos = getFacade().getContrato().consultarListaPorSituacaoCodigoPessoa(cliente.getPessoa().getCodigo(), "TR", false, Uteis.NIVELMONTARDADOS_ROBO);
            for (ContratoVO contrato : contratos) {
                if (contrato.getCodigo() != null && contrato.getCodigo() != 0) {
                    if ((Uteis.getCompareData(getDia(), contrato.getVigenciaDe()) >= 0) && (Uteis.getCompareData(getDia(), contrato.getVigenciaAteAjustada()) <= 0)) {

                        if (validarTrancamentoVencido(cliente, contrato, empresa)) {
                            continue;
                        }
                    } else if ((Uteis.getCompareData(getDia(), contrato.getVigenciaAteAjustada()) > 0) && contrato.getContratoResponsavelRenovacaoMatricula() == 0) {
                        if (validarTrancamentoVencido(cliente, contrato, empresa)) {
                            continue;
                        }
                    }
                }
            }

        }
    }

    public Boolean validarTrancamentoVencido(ClienteVO cliente, ContratoVO contrato, EmpresaVO empresa) throws Exception {
        try {
            // lista de trancamento do contrato
            List listaOperacaoTrancamento = getFacade().getContratoOperacao().consultarPorTipoOperacaoCodigoContrato("TR", contrato.getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_ROBO);
            if (!listaOperacaoTrancamento.isEmpty()) {
                // pego o ultimo trancamento
                TrancamentoContratoVO trancado = getFacade().getTrancamentoContrato().obterUltimoDiaRetornoContrato(contrato.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_ROBO);
                if(trancado == null){
                    setTextoErroProcessamento(getTextoErroProcessamento() +"Erro validarTrancamentoVencido cliente -> "
                                    + cliente.getCodigo() + ": Contrato " + contrato.getCodigo() + " sem registros na tabela TrancamentoContrato" + "\n");
                    setRotinaProcessadaParcialmente(true);
                    return false;
                }
                // valido se a data de hoje é maior que a data de retorno no
                // trancamento
                if (Uteis.getCompareData(getDia(), trancado.getDataRetorno()) >= 0) {
                    // consultor a ultima operacao de retorno do contrato
                    ContratoOperacaoVO obj = getFacade().getContratoOperacao().obterUltimaOperacaoContratoPorCodigoContratoTipoOperacao(contrato.getCodigo().intValue(), "RT", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (obj == null) {
                        validarSeJaExisteOperacaoTrancamentoVencindoParaContrato(contrato, trancado);
                        return true;
                    } else if (Uteis.getCompareData(obj.getDataInicioEfetivacaoOperacao(), trancado.getDataFimTrancamento()) > 0) {
                        return false;
                    } else {
                        validarSeJaExisteOperacaoTrancamentoVencindoParaContrato(contrato, trancado);
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarSeJaExisteOperacaoTrancamentoVencindoParaContrato(ContratoVO contrato, TrancamentoContratoVO trancado) throws Exception {
        try {
            ContratoOperacaoVO operacaoRetorno = getFacade().getContratoOperacao().
                    obterUltimaOperacaoContratoPorCodigoContratoTipoOperacao(
                    contrato.getCodigo().intValue(), "TV",
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (operacaoRetorno == null) {
                gerarRegistroOperacao(contrato, trancado);
            } else if (Uteis.getCompareData(operacaoRetorno.getDataInicioEfetivacaoOperacao(), trancado.getDataFimTrancamento()) <= 0) {
                gerarRegistroOperacao(contrato, trancado);
            } else if (Uteis.getCompareData(operacaoRetorno.getDataInicioEfetivacaoOperacao(), trancado.getDataFimTrancamento()) > 0) {
                operacaoRetorno.setDataFimEfetivacaoOperacao(getDia());
                getFacade().getContratoOperacao().alterarSemCommit(operacaoRetorno, false);
                atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO,
                        contrato.getPessoa().getCodigo());
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void gerarRegistroOperacao(ContratoVO contrato,
            TrancamentoContratoVO trancado) throws Exception {
        try {
            ContratoOperacaoVO operacao = new ContratoOperacaoVO();
            operacao.setContrato(contrato.getCodigo().intValue());
            operacao.setDataInicioEfetivacaoOperacao(Uteis.obterDataFutura2(
                    trancado.getDataFimTrancamento(), 1));
            operacao.setDataFimEfetivacaoOperacao(getDia());
            operacao.setDataOperacao(getDia());
            operacao.setDescricaoCalculo("");
            operacao.setObservacao("");
            operacao.setOperacaoPaga(false);
            operacao.setResponsavel(getUsuarioVO());
            operacao.setTipoOperacao("TV");
            getFacade().getContratoOperacao().incluirSemCommit(operacao, false);
            atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO,
                    contrato.getPessoa().getCodigo());
        } catch (Exception e) {
            throw e;
        }
    }

    public Boolean validarContratoPeriodoVencido(EmpresaVO empresa, ContratoVO contrato) throws ParseException, Exception {
        Date dataVencido;
        if (empresa.getCarenciaRenovacao() != 0) {
            dataVencido = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), empresa.getCarenciaRenovacao());
        } else {
            dataVencido = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), getConfiguracaoSistema().getCarenciaRenovacao());
        }
        return (Uteis.getCompareData(getDia(), contrato.getVigenciaAteAjustada()) > 0) && (Uteis.getCompareData(getDia(), dataVencido) <= 0);

    }

    public void registarContratosAvencerVencidosDesistentes(EmpresaVO empresa) throws Exception {
        validarContratoSituacaoAvencer(empresa);
        validarContratoSituacaoVencido(empresa);
        validarContratoSituacaoDesistente(empresa);
        if (empresa.isRemoverVinculosAposDesistencia()) {
            validarContratosDesitentesParaTreinoWeb(empresa);
        }
    }

    public void validarContratoSituacaoAvencer(EmpresaVO empresa) throws Exception {
        int nrDiasAvencer = empresa.getNrDiasAvencer() != 0 ? empresa.getNrDiasAvencer()
                : getConfiguracaoSistema().getNrDiasAvencer();
        List<Integer> listaContratos = this.isProcessamentoControlado() ? this.getListCodigoContratosControlado()
                : getFacade().getContrato().consultarCodigoPotenciaisAVencer(getDia(),
                empresa.getCodigo(), false, nrDiasAvencer);
        for (Integer codContrato : listaContratos) {
            try {
                ContratoVO contratoVO = getFacade().getContrato().consultarPorCodigo(codContrato, Uteis.NIVELMONTARDADOS_ROBO);
                trataContratoAVencer(empresa, contratoVO);
            } catch (Exception e){
                setTextoErroProcessamento(getTextoErroProcessamento() + "Erro validarContratoSituacaoAvencer contrato -> " + codContrato + ": " + e.getMessage() + "\n");
                setRotinaProcessadaParcialmente(true);
            }
        }
    }

    public void trataContratoAVencer(EmpresaVO empresa, ContratoVO contrato) throws Exception {
        if (empresa.getCodigo().intValue() == contrato.getEmpresa().getCodigo().intValue()) {
            Date dataBackup = (Date) getDia().clone();
            try {
                Integer nrDiasAvencer;
                Date dataAvencer;

                // obtenho a data de inicio que o contrato entra na situação
                // a vencer
                //se não houve um cancelamento retroativo para este contrato
                boolean existeTrancamentoFuturo = isExisteTrancamentoFuturo(contrato);
                if (!getFacade().getContratoOperacao().existeOperacaoParaEsteContrato(contrato.getCodigo(), "CA") && !existeTrancamentoFuturo) {

                    if (empresa.getNrDiasAvencer() != 0) {
                        dataAvencer = Uteis.obterDataAnterior(contrato.getVigenciaAteAjustada(), (empresa.getNrDiasAvencer() - 1));
                        nrDiasAvencer = empresa.getNrDiasAvencer();
                    } else {
                        dataAvencer = Uteis.obterDataAnterior(contrato.getVigenciaAteAjustada(), (getConfiguracaoSistema().getNrDiasAvencer() - 1));
                        nrDiasAvencer = getConfiguracaoSistema().getNrDiasAvencer();
                    }
                    Date ultimaOperacao = getFacade().getHistoricoContrato().obterDataInicioUltimoHistoricoOperacaoContrato(contrato.getCodigo());
                    Date dataConsulta =  null;
                    // valido se o contrato esta entre o periodo de a vencer
                    if (forcarGeracaoRetroativa) {
                        if (Calendario.maiorOuIgual(getDia(), dataAvencer)) {
                            dataConsulta = ultimaOperacao == null || Calendario.menor(ultimaOperacao, dataAvencer) ? dataAvencer : ultimaOperacao;
                            if (!getFacade().getHistoricoContrato().
                                    obterHistoricoContratoPorCodigoContratoTipoHistoricoDataInicioDataFim(
                                            contrato.getCodigo(), "AV", dataConsulta, Uteis.NIVELMONTARDADOS_ROBO)) {
                                setDia(dataConsulta);
                                //so podemos alterar a situacao do contrato se neste período
                                //não houver um cancelamento, trancamento ou um outro contrato iniciado
                                final Date dataPesquisa = Uteis.obterDataFutura2(
                                        contrato.getVigenciaAteAjustada(), nrDiasAvencer);
                                final int cod = contrato.getCodigo();
                                if ((!contrato.existeContratoSucessorIniciadoNestaData(getDia()))
                                        && (!getFacade().getContratoOperacao().existeOperacaoRetroativoAEstaData(
                                        cod, "CA", getDia()))
                                        && (!(getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(
                                        cod, "TR", getDia()) && getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(
                                        cod, "TR", Calendario.hoje())))
                                        && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(
                                        cod, "TR", dataPesquisa))
                                        && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(cod, "TV", getDia()))
                                        && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(
                                        cod, "TV", dataPesquisa))) {
                                    setDia(dataBackup);
                                    gerarHistoricoContratoAvencer(contrato, nrDiasAvencer,ultimaOperacao == null || Calendario.menor(ultimaOperacao,dataAvencer) ? dataAvencer : ultimaOperacao,false);
                                }
                            }
                        }
                    } else {

                        if (Calendario.maiorOuIgual(getDia(), dataAvencer)) {
                            dataConsulta = Calendario.maior(getDia(), contrato.getVigenciaAteAjustada()) ? contrato.getVigenciaAteAjustada() : getDia();
                            dataConsulta = ultimaOperacao == null || Calendario.menor(ultimaOperacao,dataConsulta) ? dataConsulta : ultimaOperacao;
                            if (!getFacade().getHistoricoContrato().obterHistoricoContratoPorCodigoContratoTipoHistoricoDataInicioDataFim(contrato.getCodigo(), "AV", dataConsulta, Uteis.NIVELMONTARDADOS_ROBO)) {
                                final Date dataPesquisa = Uteis.obterDataFutura2(
                                        contrato.getVigenciaAteAjustada(), nrDiasAvencer);
                                final int cod = contrato.getCodigo();
                                if ((!contrato.existeContratoSucessorIniciadoNestaData(getDia()))
                                        && (!getFacade().getContratoOperacao().existeOperacaoRetroativoAEstaData(
                                        cod, "CA", getDia()))
                                        && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(
                                        cod, "TR", getDia()))
                                        && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(
                                        cod, "TR", dataPesquisa))
                                        && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(cod, "TV", getDia()))
                                        && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(
                                        cod, "TV", dataPesquisa))) {

                                    gerarHistoricoContratoAvencer(contrato, nrDiasAvencer,ultimaOperacao == null || Calendario.menor(ultimaOperacao,dataAvencer) ? dataAvencer : ultimaOperacao , false);
                                }
                            }
                        }

                    }
                }
            } catch (Exception e) {
                System.out.println("Erro em 'trataContratoAVencer' contrato " + contrato.getCodigo()
                        + " EXC.: " + e.getMessage());
                throw e;
            } finally {
                setDia(dataBackup);
            }
        }
    }

    private static boolean isExisteTrancamentoFuturo(ContratoVO contrato) throws Exception {
        try {
            ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(contrato);
        } catch (ConsistirException ex) {
            return true;
        }
        return false;
    }

    public void gerarHistoricoContratoAvencer(ContratoVO contrato, Integer nrDiasAvencer, Date dataAVencer,boolean OpConflitante) throws Exception {
        try {
            HistoricoContratoVO hist = new HistoricoContratoVO();
            hist = getFacade().getHistoricoContrato().obterHistoricoContratoPorDataEspecifica(contrato.getCodigo().intValue(), dataAVencer, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (hist != null) {
                if(hist.getAtestado() || hist.getCarencia() || hist.getTrancado()){ //trata casos em que operações foram lançadas no periodo de a Vencer.
                    gerarHistoricoContratoAvencer(contrato,nrDiasAvencer , Uteis.obterDataFutura2(hist.getDataFinalSituacao() , 1), true);
                    return;
                }
                if(OpConflitante){
                    if(getFacade().getHistoricoContrato().
                                    obterHistoricoContratoPorCodigoContratoTipoHistoricoDataInicioDataFim(
                                    contrato.getCodigo().intValue(), "AV", Uteis.obterDataFutura2(hist.getDataFinalSituacao() , 1), Uteis.NIVELMONTARDADOS_ROBO)){
                        return;
                    }
                }
                if (Uteis.getCompareData(dataAVencer, hist.getDataInicioSituacao()) == 0) {
                    hist.setDataFinalSituacao(dataAVencer);
                    getFacade().getHistoricoContrato().alterarSemCommit(hist, false);
                } else {
                    hist.setDataFinalSituacao(Uteis.obterDataAnterior(dataAVencer, 1));
                    getFacade().getHistoricoContrato().alterarSemCommit(hist, false);
                }
            }

            hist = new HistoricoContratoVO();
            hist.setContrato(contrato.getCodigo().intValue());
            hist.setDataInicioSituacao(dataAVencer);
            if (Calendario.menor(hist.getDataInicioSituacao(), contrato.getVigenciaDe())) {
                hist.setDataInicioSituacao(contrato.getVigenciaDe());
            }
            // validar se exite uma operação agendada.
            registarClienteAvencerValindadoAntesHistoricoLancadoFuturo(contrato, hist);
            hist.setDataFinalSituacao(contrato.getVigenciaAteAjustada());
            if (Calendario.menor(hist.getDataFinalSituacao(), hist.getDataInicioSituacao())) {
                hist.setDataFinalSituacao(hist.getDataInicioSituacao());
            }

            hist.setDataRegistro(getDia());
            hist.setDescricao("A VENCER");
            hist.setTipoHistorico("AV");
            hist.setResponsavelRegistro(getUsuarioVO());
            getFacade().getHistoricoContrato().incluirSemCommit(hist, false);
            atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO,
                    contrato.getPessoa().getCodigo());
        } catch (Exception e) {
            throw e;
        }
    }

    public void registarClienteAvencerValindadoAntesHistoricoLancadoFuturo(ContratoVO contrato, HistoricoContratoVO histNovo) throws Exception {
        try {
            HistoricoContratoVO hist = getFacade().getHistoricoContrato().obterHistoricoContratoLancadoFuturoPorCodigoContratoDataInicioDataFim(contrato.getCodigo().intValue(), getDia(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (hist != null) {
                histNovo.setDataFinalSituacao(Uteis.obterDataAnterior(hist.getDataInicioSituacao(), 1));
            } else {
                histNovo.setDataFinalSituacao(contrato.getVigenciaAteAjustada());
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarContratoSituacaoVencido(EmpresaVO empresa) throws Exception {
        // inicializo a dataVencido com o valor do dia anterior possou vou
        // realizar uma consulta no banco para saber se o cliente nessa data
        // nao renovou um contrato entao ele vai estar vencido
        Date dataVencido = Uteis.obterDataAnterior(getDia(), 1);
        List<Integer> listaCodContratos = this.isProcessamentoControlado() ? this.getListCodigoContratosControlado()
                : getFacade().getContrato().consultarCodigoPotenciaisVencidos(dataVencido, empresa.getCodigo(), false);
        for (Integer codContrato : listaCodContratos) {
            try {
                ContratoVO contratoVO = getFacade().getContrato().consultarPorCodigo(codContrato, Uteis.NIVELMONTARDADOS_ROBO);
                if (contratoVO.getSituacao().equals("CA") || contratoVO.getSituacao().equals("TR")) {
                    continue;
                }
                trataContratoVencido(empresa, contratoVO, dataVencido);
            } catch (Exception e){
                setTextoErroProcessamento(getTextoErroProcessamento() + "Erro validarContratoSituacaoVencido contrato -> " + codContrato + ": " + e.getMessage() + "\n");
                setRotinaProcessadaParcialmente(true);
            }
        }
    }

    public void trataContratoVencido(EmpresaVO empresa, ContratoVO contrato, Date dataVencido) throws Exception {
        if (empresa.getCodigo().intValue() == contrato.getEmpresa().getCodigo().intValue()) {
            Integer nrDiasVencido;
            Date dataBackup = (Date) getDia().clone();
            try {

                // obtenho a data de inicio do periodo de vencido
                //se este  contrato não possui um cancelamento retroativo...
                if (!getFacade().getContratoOperacao().existeOperacaoRetroativaParaEsteContrato(
                        contrato.getCodigo(), "CA")) {//se não há cancelamento utiliza a data de término ajustada

                    if (empresa.getCarenciaRenovacao() != 0) {
                        dataVencido = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), empresa.getCarenciaRenovacao());
                        nrDiasVencido = empresa.getCarenciaRenovacao();
                    } else {
                        dataVencido = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), getConfiguracaoSistema().getCarenciaRenovacao());
                        nrDiasVencido = getConfiguracaoSistema().getCarenciaRenovacao();
                    }


                    // valido se o contrar esta entre o periodo de vencido
                    if (forcarGeracaoRetroativa) {
                        if (Calendario.maior(getDia(), contrato.getVigenciaAteAjustada())) {
                            if (!getFacade().getHistoricoContrato()
                                    .obterHistoricoContratoPorCodigoContratoTipoHistoricoDataInicioDataFim(
                                            contrato.getCodigo(), "VE", dataVencido, Uteis.NIVELMONTARDADOS_ROBO)) {
                                setDia(Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), 1));
                                validarEGerarHistoricoContrato(empresa, contrato, nrDiasVencido);
                            }
                        }

                    } else {

                        if (Calendario.maior(getDia(), contrato.getVigenciaAteAjustada())) {
                            if (!getFacade().getHistoricoContrato()
                                    .obterHistoricoContratoPorCodigoContratoTipoHistoricoDataInicioDataFim(
                                            contrato.getCodigo(), "VE", getDia(), Uteis.NIVELMONTARDADOS_ROBO)) {
                                validarEGerarHistoricoContrato(empresa, contrato, nrDiasVencido);
                            }
                        }
                    }
                }
            } finally {
                setDia(dataBackup);
            }
        }
    }

    /**
     *  So podemos alterar a situacao do contrato se neste período:
     *   - não houver um cancelamento,
     *   - trancamento ou
     *   - um outro contrato iniciado
     *
     * @param contrato
     * @param nrDiasVencido
     * @throws Exception
     */
    private void validarEGerarHistoricoContrato(EmpresaVO empresa,ContratoVO contrato,Integer nrDiasVencido) throws Exception{

        final Date dataPesquisa = Uteis.obterDataFutura2( contrato.getVigenciaAteAjustada(), nrDiasVencido);
        final int cod = contrato.getCodigo();
        if ((!contrato.existeContratoSucessorIniciadoNestaData(getDia()))
                && (!getFacade().getContratoOperacao().existeOperacaoRetroativoAEstaData( cod, "CA", getDia()))
                && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData( cod, "TR", getDia()))
                && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData( cod, "TR", dataPesquisa))
                && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData( cod, "TV", getDia()))
                && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData( cod, "TV", dataPesquisa))) {
            gerarHistoricoContratoVencido(empresa, contrato, nrDiasVencido,SituacaoClienteSinteticoEnum.GRUPO_TODOS);
        }
    }

    public void gerarHistoricoContratoVencido(EmpresaVO empresa, ContratoVO contrato, Integer nrDiasVencido, SituacaoClienteSinteticoEnum tipo) throws Exception {
        HistoricoContratoVO hist = getFacade().getHistoricoContrato().
                obterUltimoHistoricoContratoPorContratoTipoHistorico(
                        contrato.getCodigo(), "AV",
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (hist != null) {
            hist.setDataFinalSituacao(contrato.getVigenciaAteAjustada());
            getFacade().getHistoricoContrato().alterarSemCommit(hist, false);

        }

        // caso seja identificado com contrato vencido.
        // na geração de registro nao grava o valor da data do mesmo dia
        // pois ele vai ficar somente vencido no dia de amanha.
        hist = new HistoricoContratoVO();
        hist.setContrato(contrato.getCodigo());
        hist.setDataInicioSituacao(Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), 1));
        hist.setDataFinalSituacao(Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), nrDiasVencido));
        hist.setDataRegistro(getDia());
        hist.setDescricao("VENCIDO");
        hist.setTipoHistorico("VE");
        hist.setResponsavelRegistro(getUsuarioVO());
        getFacade().getHistoricoContrato().incluirSemCommit(hist, false);
        //Nova Configuração no Clube de Vantagens para zerar pontos quando ...
        if (empresa.getZerarPontosAposVencimento() == TemporalRemocaoPontoEnum.QUANDO_CANCELADO_VENCIDO) {
            if (!getFacade().getContrato().existeAlgumContratoConcomitanteVigentePessoaNaData(contrato.getPessoa().getCodigo(), contrato.getCodigo(), getDia())) {
                getFacade().getHistoricoPontos().zerarPontuacaoRobo(contrato.getPessoa());
            }
        }
        getFacade().getZWFacade().notificarContratoVencido(contrato);

        atualizarSintetico(tipo, contrato.getPessoa().getCodigo());
    }

    public void validarContratosDesitentesParaTreinoWeb(EmpresaVO empresa) throws Exception {
        Date data = Uteis.obterDataAnterior(getDia(), empresa.getNrDiasDesistenteRemoverVinculoTreino());

        List<ContratoVO> listaContratos = this.isProcessamentoControlado()
                ? getFacade().getContrato().consultarPorHistoricoContratoAndContrato(data, "'DE'", empresa.getCodigo(), this.getListContratosControlado(), Uteis.NIVELMONTARDADOS_ROBO)
                : getFacade().getContrato().consultarPorHistoricoComVinculoDiferenteDeConsultor(data, "'DE'", empresa.getCodigo(), Uteis.NIVELMONTARDADOS_ROBO);
        for (ContratoVO contrato : listaContratos) {
            Date  ultimoVencimento = getFacade().getContrato().consultarUltimoVencimento(contrato.getPessoa().getCodigo());
            if(this.isProcessamentoControlado() && !getFacade().getHistoricoContrato().obterHistoricoContratoPorCodigoContratoTipoHistorico(contrato.getCodigo(), "DE")){
                continue;
            }
            if(Calendario.maiorOuIgual(contrato.getVigenciaAteAjustada(), ultimoVencimento)){
                removerVinculosDoContrato(contrato);
            }
        }
    }


    public void removerVinculosDoContrato(ContratoVO contrato) throws Exception {
        List<VinculoVO> vinculos = getFacade().getVinculo().consultarVinculosDiferentesDeConsultorPorCliente(contrato.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

        getFacade().getVinculo().removerVinculosSemCommit(vinculos, "PROCESSAMENTO DIÁRIO", getUsuarioVO(), true, null);
    }

    public void validarContratoSituacaoDesistente(EmpresaVO empresa) throws Exception {
        Date data = Uteis.obterDataAnterior(getDia(), 1);
        List<Integer> listaCodContratos = this.isProcessamentoControlado()
                ? this.getListCodigoContratosControlado()
                : getFacade().getContrato().consultarCodigoPotenciaisDesistentes(data, empresa.getCodigo(), false);
        for (Integer codContrato : listaCodContratos) {
            try {
                ContratoVO contratoVO = getFacade().getContrato().consultarPorCodigo(codContrato, Uteis.NIVELMONTARDADOS_ROBO);
                if(contratoVO.getSituacao().equals("CA") ||contratoVO.getSituacao().equals("TR")){
                    continue;
                }
                trataContratoDesistente(empresa, contratoVO);
            } catch (Exception e){
                setTextoErroProcessamento(getTextoErroProcessamento() + "Erro validarContratoSituacaoDesistente contrato -> " + codContrato + ": " + e.getMessage() + "\n");
                setRotinaProcessadaParcialmente(true);
            }
        }
    }

    public void trataContratoDesistente(EmpresaVO empresa, ContratoVO contrato) throws Exception {
        if (empresa.getCodigo().intValue() == contrato.getEmpresa().getCodigo().intValue()) {
            Date dataBackup = (Date) getDia().clone();
            try {
                Date dataDesistente;
                //se este contrato não possui um cancelamento retroativo...
                if (!getFacade().getContratoOperacao().existeOperacaoRetroativaParaEsteContrato(contrato.getCodigo(), "CA")) {
                    if (empresa.getCarenciaRenovacao() != 0) {
                        dataDesistente = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), (empresa.getCarenciaRenovacao() + 1));
                    } else {
                        dataDesistente = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), (getConfiguracaoSistema().getCarenciaRenovacao() + 1));
                    }

                    if (Calendario.maiorOuIgual(getDia(), dataDesistente)) {
                        if (!getFacade().getHistoricoContrato().obterHistoricoContratoPorCodigoContratoTipoHistorico(contrato.getCodigo(), "DE")) {

                            final int cod = contrato.getCodigo();
                            if ((!contrato.existeContratoSucessorIniciadoNestaData(getDia()))
                                    && (!getFacade().getContratoOperacao().existeOperacaoRetroativoAEstaData(cod, "CA", getDia()))
                                    && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(cod, "TR", getDia()))
                                    && (!getFacade().getContratoOperacao().existeOperacaoInterceptaEstaData(cod, "TV", getDia()))) {
                                gerarHistoricoContratoDesistente(contrato, dataDesistente);
                                //Nova Configuração no Clube de Vantagens para zerar pontos quando ...
                                if (empresa.getZerarPontosAposVencimento() == TemporalRemocaoPontoEnum.QUANDO_CANCELADO_DESISTENTE) {
                                    if (!getFacade().getContrato().existeAlgumContratoConcomitanteVigenteOuVencidoPessoaNaData(contrato.getPessoa().getCodigo(), contrato.getCodigo(), getDia())){
                                        getFacade().getHistoricoPontos().zerarPontuacaoRobo(contrato.getPessoa());
                                    }
                                }
                            }
                        }
                    }
                }


            } finally {
                setDia(dataBackup);
            }
        }
    }

    public void gerarHistoricoContratoDesistente(ContratoVO contrato, final Date dataDesistente) throws Exception {
        HistoricoContratoVO hist = new HistoricoContratoVO();
        hist.setContrato(contrato.getCodigo());
        hist.setDataInicioSituacao(dataDesistente);
        hist.setDataFinalSituacao(dataDesistente);
        hist.setDataRegistro(getDia());
        hist.setDescricao("DESISTENTE");
        hist.setTipoHistorico("DE");
        hist.setResponsavelRegistro(getUsuarioVO());
        getFacade().getHistoricoContrato().incluirSemCommit(hist, false);
        contrato.setSituacao("IN");
        getFacade().getContrato().alterarSituacaoContrato(contrato, false);
        atualizarSintetico(SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, contrato.getPessoa().getCodigo());

        getFacade().getZWFacade().notificarContratoDesistente(contrato);
    }

    public void processarDadosDemonstrativoFinanceiro(Integer nrMeses) throws Exception {
        Logger.getLogger(this.getClass().getName()).log(
                Level.INFO, " processando DFSintetico em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});
        getFacade().getFinanceiro().getDFSinteticoDW().gerarDadosSintetico(null, true, nrMeses);
    }

    public void processarDadosDRE(Integer nrMeses) throws Exception {
        Logger.getLogger(this.getClass().getName()).log(
                Level.INFO, " processando DRESintetico em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});
        getFacade().getFinanceiro().getDRESinteticoDW().gerarDadosSintetico(null, true, nrMeses);
    }

    public void processarDadosGerencialPmg(final String chave) throws Exception {
        Logger.getLogger(this.getClass().getName()).log(
                Level.INFO, " processando Dados Gerenciais PMG em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});
        getFacade().getDadosGerencialPmg().gerarDadosPMG(0, chave, Calendario.hoje(), null);
        getFacade().getDadosGerencialPmg().gerarDadosDiaPMG(0, chave, Uteis.somarDias(Calendario.hoje(),-1), null);
    }
    
    public void processarDadosTicketMedio() throws Exception {
        Logger.getLogger(this.getClass().getName()).log(
                Level.INFO, " processando Ticket Médio em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});
        getFacade().getTicketMedio().gerarDadosSintetico(null);
    }
    
    public void processarFeedsGestao() throws Exception{
        getFacade().getZWFacade().processarFeedsGestao();
    }
    public void processarArmarioGestao() throws Exception{
        getFacade().getZWFacade().processarArmarioGestao();
    }

    public void processarClientesComParcelaVencida() throws Exception {
        try {
            Uteis.logar(null, "Iniciando processamento (Parcelas Vencidas) do dia "
                    + Calendario.hoje().toString() + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());
            getFacade().getClienteMensagem().excluirMensagemParcelaEmRemessa();
            getFacade().getClienteMensagem().excluirMensagemErradaPorMovParcelaVencida();
            getFacade().getClienteMensagem().excluirMensagensErradasParcelasNaoVencidasEmAberto(this.getDia());
            getFacade().getClienteMensagem().excluirMensagensParcelasVencidasNaPessoaErradaContratosConcedidos();
            for (EmpresaVO empresa : getListaEmpresa()) {
                if (!empresa.isAtiva()) {
                    continue;
                }

                Date dataVencimentoComTolerancia = getDia();
                if (empresa.getToleranciaPagamento() != 0) {
                    dataVencimentoComTolerancia = Uteis.obterDataAnterior(dataVencimentoComTolerancia, empresa.getToleranciaPagamento());
                } else if (configuracaoSistema.getToleranciaPagamento() != 0) {
                    dataVencimentoComTolerancia = Uteis.obterDataAnterior(dataVencimentoComTolerancia, configuracaoSistema.getToleranciaPagamento());
                }
                Uteis.logar(null, "## data vencimento com tolerancia = " + Uteis.getData(dataVencimentoComTolerancia));
                ResultSet lista = getFacade().getCliente().consultarParcelaEmAbertoClientePorEmpresaDataVencimento(
                        empresa.getCodigo(), dataVencimentoComTolerancia, false,
                        this.isProcessamentoControlado() ? this.getListClientesControlado() : null,false);
                StringBuilder parcelasEnvolvidas = new StringBuilder();
                while (lista.next()) {
                    Integer cliente = lista.getInt("cliente");
                    Integer parcela = lista.getInt("parcela");
                    Integer responsavel = lista.getInt("responsavel");
                    Boolean existeParcela = getFacade().getClienteMensagem().consultarClienteMensagemPorMovParcela(parcela, false);
                    parcelasEnvolvidas.append(parcela).append(" - ").append(existeParcela ? "já tem mensagem,": "não tem mensagem");
                    if (!existeParcela) {
                        ClienteMensagemVO msg = new ClienteMensagemVO();
                        msg.getCliente().setCodigo(cliente);
                        msg.setMensagem(TiposMensagensEnum.PARCELA_ATRASO.getMensagem().replace("X", parcela.toString()));
                        msg.setTipomensagem(TiposMensagensEnum.PARCELA_ATRASO);
                        msg.getUsuario().setCodigo(responsavel);
                        msg.getMovParcela().setCodigo(parcela);
                        getFacade().getClienteMensagem().incluirSemCommit(msg);
                    }
                }
                Uteis.logar(null, "## Parcelas Vencidas empresa " + empresa.getNome() +": "+parcelasEnvolvidas.toString());
            }
            Uteis.logar(null, "Terminando processamento (Parcelas Vencidas) do dia "
                    + Calendario.hoje().toString() + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());
        } catch (Exception e) {
            throw e;
        }
    }

    public void processarClientesComProdutoVencido() throws Exception {
        ResultSet lista = getFacade().getCliente().consultarProdutoVencido(getDia(), false,
                this.isProcessamentoControlado() ? this.getListClientesControlado() : null);
        ClienteMensagem clienteMensagem = new ClienteMensagem();
        while (lista.next()) {
            Integer cliente = lista.getInt("cliente");
            Integer produto = lista.getInt("produto");
            String produtoDesc = lista.getString("descricao");
            Date finalVigencia = lista.getDate("datafinalvigencia");
            Boolean bloqueia = lista.getBoolean("bloqueiapelavigencia");
            Boolean existeMensagem = clienteMensagem.consultarClienteMensagemPorProdutoVencido(cliente, produto, false);
            if (!existeMensagem) {
                ClienteMensagemVO msg = new ClienteMensagemVO();
                msg.getCliente().setCodigo(cliente);
                msg.setMensagem(TiposMensagensEnum.PRODUTO_VENCIDO.getMensagem().replace("Z", produtoDesc).replace("xx/xx/xx",
                        Uteis.getData(finalVigencia, "br")));
                msg.setTipomensagem(TiposMensagensEnum.PRODUTO_VENCIDO);
                msg.getUsuario().setCodigo(getUsuarioVO().getCodigo());
                msg.getProduto().setCodigo(produto);
                msg.setBloqueio(bloqueia);
                getFacade().getClienteMensagem().incluirSemCommit(msg);
            }
        }
    }

    public void processarClientesComCartaoCreditoVencido() throws Exception {
        getFacade().getClienteMensagem().processarTodosClientesComCartaoCreditoVencido(getUsuarioVO(), null);
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getTexto() {
        if (texto == null) {
            texto = "";
        }
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public List<EmpresaVO> getListaEmpresa() {
        return listaEmpresa;
    }

    public void setListaEmpresa(List<EmpresaVO> listaEmpresa) {
        this.listaEmpresa = listaEmpresa;
    }

    public Boolean getRotinaProcessada() {
        return RotinaProcessada;
    }

    public void setRotinaProcessada(Boolean RotinaProcessada) {
        this.RotinaProcessada = RotinaProcessada;
    }

    public Boolean getRotinaProcessadaParcialmente() {
        if (rotinaProcessadaParcialmente == null){
            rotinaProcessadaParcialmente = false;
        }
        return rotinaProcessadaParcialmente;
    }

    public void setRotinaProcessadaParcialmente(Boolean rotinaProcessadaParcialmente) {
        this.rotinaProcessadaParcialmente = rotinaProcessadaParcialmente;
    }

    public String getTextoErroProcessamento() {
        if (textoErroProcessamento == null) {
            textoErroProcessamento = "";
        }
        return textoErroProcessamento;
    }

    public void setTextoErroProcessamento(String textoErroProcessamento) {
        this.textoErroProcessamento = textoErroProcessamento;
    }

    protected boolean inicializarFacades() throws Exception {
        try {
            return true;
        } catch (Exception e) {
            throw e;

        }
    }

    /**
     * @return the atualizarTabelaSintetico
     */
    public Boolean getAtualizarTabelaSituacaoContrato() {
        if (atualizarTabelaSituacaoContrato == null) {
            atualizarTabelaSituacaoContrato = false;
        }
        return atualizarTabelaSituacaoContrato;
    }

    public void setAtualizarTabelaSituacaoContrato(Boolean atualizarTabelaSituacaoContrato) {
        this.atualizarTabelaSituacaoContrato = atualizarTabelaSituacaoContrato;
    }

    public void setDataHoraFim(Date dataHoraFim) {
        this.dataHoraFim = dataHoraFim;
    }

    public Date getDataHoraFim() {
        return dataHoraFim;
    }

    public void setDataHoraInicio(Date dataHoraInicio) {
        this.dataHoraInicio = dataHoraInicio;
    }

    public Date getDataHoraInicio() {
        return dataHoraInicio;
    }

    public void setUsarRobo(boolean usarRobo) {
        this.usarRobo = usarRobo;
    }

    public boolean isUsarRobo() {
        return usarRobo;
    }

    public void testeInTransaction() throws Exception {
        ContratoVO c = getFacade().getContrato().consultarPorChavePrimaria(10,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        getFacade().getContrato().alterarSituacaoContrato(c, false);
    }

    public void setDeveGerarSintetico(boolean deveGerarSintetico) {
        this.deveGerarSintetico = deveGerarSintetico;
    }

    public boolean isDeveGerarSintetico() {
        return deveGerarSintetico;
    }

    public boolean isForcarGeracaoRetroativa() {
        return forcarGeracaoRetroativa;
    }

    public void setForcarGeracaoRetroativa(boolean forcarGeracaoRetroativa) {
        this.forcarGeracaoRetroativa = forcarGeracaoRetroativa;
    }

    public boolean isProcessamentoControlado() {
        return isProcessarValidador() || (JSFUtilities.isJSFContext() && JSFUtilities.getFromSession(RoboTransientObjectsVO.class) != null);
    }

    public List<ContratoVO> getListContratosControlado() {
        if (this.isProcessamentoControlado()) {
            if (JSFUtilities.isJSFContext()) {
                RoboTransientObjectsVO r = (RoboTransientObjectsVO) JSFUtilities.getFromSession(RoboTransientObjectsVO.class);
                return r.getListaContratos();
            } else {
                return this.getListaContratos();
            }
        } else {
            return new ArrayList<ContratoVO>();
        }
    }

    public List<Integer> getListCodigoContratosControlado() {
        List<Integer> listaCodigoContratosControlado = new ArrayList<>();
        if (this.isProcessamentoControlado()) {
            if (JSFUtilities.isJSFContext()) {
                RoboTransientObjectsVO r = (RoboTransientObjectsVO) JSFUtilities.getFromSession(RoboTransientObjectsVO.class);
                for (ContratoVO contratoVO: r.getListaContratos()){
                    listaCodigoContratosControlado.add(contratoVO.getCodigo());
                }
                return listaCodigoContratosControlado;
            } else {
                for (ContratoVO contratoVO: this.getListaContratos()){
                    listaCodigoContratosControlado.add(contratoVO.getCodigo());
                }
                return listaCodigoContratosControlado;
            }
        } else {
            return new ArrayList<>();
        }
    }

    public void atualizarListasProcessamentoControlado() throws Exception {
        if (this.isProcessamentoControlado()) {
            RoboTransientObjectsVO r = (RoboTransientObjectsVO) JSFUtilities.getFromSession(RoboTransientObjectsVO.class);
            if (r != null) {
                r.atualizarListas();
            }
        }
    }

    public List<ClienteVO> getListClientesControlado() {
        if (this.isProcessamentoControlado()) {
            if(JSFUtilities.isJSFContext()){
                RoboTransientObjectsVO r = (RoboTransientObjectsVO) JSFUtilities.getFromSession(RoboTransientObjectsVO.class);
                return r.getListaClientes();
            } else {
                return this.getListaClientes();
            }
        } else {
            return new ArrayList<ClienteVO>();
        }
    }

    public boolean isProcessarValidador() {
        return processarValidador;
    }

    public void setProcessarValidador(boolean processarValidador) {
        this.processarValidador = processarValidador;
    }

     public List<ClienteVO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClienteVO> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public List<ContratoVO> getListaContratos() {
        return listaContratos;
    }

    public void setListaContratos(List<ContratoVO> listaContratos) {
        this.listaContratos = listaContratos;
    }

    public String getClienteAtualizados() {
        return clienteAtualizados;
    }

    public void setClienteAtualizados(String clienteAtualizados) {
        this.clienteAtualizados = clienteAtualizados;
    }

    public String getClienteJaProcessados() {
        return clienteJaProcessados;
    }

    public void setClienteJaProcessados(String clienteJaProcessados) {
        this.clienteJaProcessados = clienteJaProcessados;
    }

    private void atualizarAlunosComSituacoesInconsistentes(Date dia, Integer empresa, Integer cliente)throws Exception{
        //verifiquei situações em que o cliente estava com uma situação fora do padrão, possivelmente por alterações manuais.
        String sql = "update cliente set situacao  = '' where situacao not in ('AT','IN','TR','VI')";
        //ajusta problema do cancelamento que muda data de inicio para o fururo
        SuperFacadeJDBC.executarConsulta(sql, getFacade().getRisco().getCon());
        sql = "update contrato c set vigenciade = (select coalesce(datainiciosituacao,c.vigenciade) from historicocontrato  where contrato = c.codigo and tipohistorico in ('MA','RE','RN','CT','TE') order by datainiciosituacao limit 1) where XX c.empresa = " + empresa + " and exists (select contrato from contratooperacao where contrato = c.codigo and tipooperacao = 'CA');";
        if(!UteisValidacao.emptyNumber(cliente)){
            sql = sql.replace("XX", " c.pessoa in (select cl.pessoa from cliente cl where cl.codigo = "+cliente+") and ");
        } else {
            sql = sql.replace("XX", "");
        }
        SuperFacadeJDBC.executarConsulta(sql, getFacade().getRisco().getCon());
        //ajusta contrato que tem vigencia negativa. problema de importacao.
        sql = "update contrato  set vigenciaateajustada  = vigenciade where XX empresa = " + empresa + " and vigenciade > vigenciaateajustada and vigenciade < '" + Uteis.getDataJDBC(dia) + "' ";
        if(!UteisValidacao.emptyNumber(cliente)){
            sql = sql.replace("XX", " pessoa in (select cl.pessoa from cliente cl where cl.codigo = "+cliente+") and ");
        } else {
            sql = sql.replace("XX", "");
        }
         SuperFacadeJDBC.executarConsulta(sql, getFacade().getRisco().getCon());
        
        //alunos cujo primeiro contrato foi lançado no fututo, deve ficar visitantes até o inicio do contrato.
        sql = "select distinct cli.pessoa from cliente cli \n" +
                "left join contrato ant on ant.pessoa = cli.pessoa and ant.vigenciade <= '" + Uteis.getDataJDBC(dia) + "' \n" +
                "left join contrato futuro on futuro.pessoa = cli.pessoa and futuro.vigenciade > '" + Uteis.getDataJDBC(dia) + "' \n" +
                "where XX cli.empresa = " + empresa + " and  cli.situacao <> 'VI' and ((ant.codigo is null and futuro.codigo is not null )\n" +
                " or not exists (select  codigo from contrato where pessoa = cli.pessoa))";
         if(!UteisValidacao.emptyNumber(cliente)){
            sql = sql.replace("XX", " cli.codigo = "+cliente+" and ");
        } else {
            sql = sql.replace("XX", "");
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql,
                        getFacade().getRisco().getCon());
        while(rs.next()){
            ClienteVO cli = getFacade().getSituacaoClienteSinteticoDW().consultarClientePreparado(rs.getInt("pessoa"));
            if (cli.getCodigo() != 0) {
                getFacade().getZWFacade().atualizarSintetico(cli, dia, SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, true);
            }
        }
        //alunos que deveriam estar inativos, tem contrato vencido que não está trancado, não tem contrato vigente, e pode ter contrato no fututo(deve ficar inativos até o inicio do contrato).
        sql = "select distinct cli.pessoa from cliente cli left join contrato ant on ant.pessoa = cli.pessoa and ant.vigenciade <  '" + Uteis.getDataJDBC(dia) + "'  and ant.situacao <> 'TR' \n" +
        "left join contrato vigente on vigente.pessoa = cli.pessoa and  '" + Uteis.getDataJDBC(dia) + "' between vigente.vigenciade and vigente.vigenciaateajustada \n" +
        " left join contrato tranc on tranc.pessoa = cli.pessoa and tranc.vigenciade <  '" + Uteis.getDataJDBC(dia) + "' and tranc.situacao = 'TR' "
                + " where XX cli.empresa = " + empresa + " and cli.situacao <> 'IN' and ant.codigo is not null and vigente.codigo is null and tranc.codigo is null  ";
        if(!UteisValidacao.emptyNumber(cliente)){
            sql =  sql.replace("XX", " cli.codigo = "+cliente+" and ");
        } else {
            sql = sql.replace("XX", "");
        }
        rs = SuperFacadeJDBC.criarConsulta(sql,
                        getFacade().getRisco().getCon());
        while(rs.next()){
            ClienteVO cli = getFacade().getSituacaoClienteSinteticoDW().consultarClientePreparado(rs.getInt("pessoa"));
            if (cli.getCodigo() != 0) {
                getFacade().getZWFacade().atualizarSintetico(cli, dia, SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, true);
            }
        }
        //alunos que tem contratos vigentes, não estão trancados, mas não estão ativos.
         sql = "select distinct cli.pessoa from cliente cli \n" +
                "inner join contrato vigente on vigente.pessoa = cli.pessoa and  '" + Uteis.getDataJDBC(dia) + "'\n" +
                "between vigente.vigenciade and vigente.vigenciaateajustada and vigente.situacao <> 'TR'\n" +
                "where XX cli.empresa = " + empresa + " and cli.situacao <> 'AT' and not exists (select codigo from contratooperacao where contrato = vigente.codigo and tipooperacao = 'CA' and datainicioefetivacaooperacao = '" + Uteis.getDataJDBC(dia) + "')";
        if(!UteisValidacao.emptyNumber(cliente)){
            sql =  sql.replace("XX", " cli.codigo = "+cliente+" and ");
        } else {
            sql = sql.replace("XX", "");
        }
        rs = SuperFacadeJDBC.criarConsulta(sql,
                        getFacade().getRisco().getCon());
        while(rs.next()){
            ClienteVO cli = getFacade().getSituacaoClienteSinteticoDW().consultarClientePreparado(rs.getInt("pessoa"));
            if (cli.getCodigo() != 0) {
                getFacade().getZWFacade().atualizarSintetico(cli, dia, SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, true);
            }
        }
    }

    private void atualizarAlunosComContratoIniciandoNoDia(Date dia, Integer empresa, Integer cliente) throws Exception {
         String sql = "select distinct cli.pessoa from cliente cli inner join contrato con on con.pessoa = cli.pessoa  where XX cli.empresa = " + empresa + " and vigenciaDe = '" + Uteis.getDataJDBC(dia) + "'";
         if(!UteisValidacao.emptyNumber(cliente)){
            sql = sql.replace("XX", " cli.codigo = "+cliente+" and ");
        } else {
            sql = sql.replace("XX", "");
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql,
                        getFacade().getRisco().getCon());
        while(rs.next()){
            ClienteVO cli = getFacade().getSituacaoClienteSinteticoDW().consultarClientePreparado(rs.getInt("pessoa"));
            if (cli.getCodigo() != 0) {
                cli.setSituacao("AT");
                getFacade().getZWFacade().atualizarSintetico(cli, dia, SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, true);
                getFacade().getSituacaoClienteSinteticoDW().atualizarInformacoesCreditoTreino(rs.getInt("pessoa"), this.getDia());
            }
        }
    }


    public void processarClientesComAluguelArmarioVencidoChaveDevolvida(int codigoEmpresa) throws Exception {
        try {
            ResultSet lista = getFacade().getCliente().consultarClientesComAluguelArmarioVencidoChaveDevolvida(getDia(), false,
                    this.isProcessamentoControlado() ? this.getListClientesControlado() : null, codigoEmpresa);
            ClienteMensagem clienteMensagem = new ClienteMensagem();
            while (lista.next()) {
                Integer cliente = new Integer(lista.getInt("cliente"));
                Integer produto = new Integer(lista.getInt("produto"));
                Date finalVigencia = lista.getDate("datafinalvigencia");
                Boolean existeMensagem = clienteMensagem.consultarClienteMensagemPorProdutoVencido(cliente, produto, false);
                if (!existeMensagem) {
                    ClienteMensagemVO msg = new ClienteMensagemVO();
                    msg.getCliente().setCodigo(cliente);
                    msg.setMensagem(TiposMensagensEnum.ARMARIO_ALUGUEL_VENCIDO_CHAVE_NAO_DEVOLVIDA.getMensagem().replace("xx/xx/xx",
                            Uteis.getData(finalVigencia, "br")));
                    msg.setTipomensagem(TiposMensagensEnum.ARMARIO_ALUGUEL_VENCIDO_CHAVE_NAO_DEVOLVIDA);
                    msg.getUsuario().setCodigo(getUsuarioVO().getCodigo());
                    msg.getProduto().setCodigo(produto);
                    msg.setBloqueio(true);
                    getFacade().getClienteMensagem().incluirSemCommit(msg);
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }
}
