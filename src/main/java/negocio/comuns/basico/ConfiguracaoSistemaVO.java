package negocio.comuns.basico;

import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.CadastroDinamicoItemVO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.OpcoesNomenclaturaVendaCreditoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Repons?vel por manter os dados da entidade ConfiguracaoSistema. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os m?todos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em mem?ria os dados desta entidade.
 * @see SuperVO
 */
public class ConfiguracaoSistemaVO extends SuperVO {

    private double juroParcela = 0;
    private double multa = 0;
    private String mascaraMatricula = "";
    @NaoControlarLogAlteracao
    private int hiddenQuantRegistrosConfCliente = 0;
    @NaoControlarLogAlteracao
    private int hiddenQuantRegistrosConfVisitante = 0;
    @FKJson
    private QuestionarioVO questionarioPrimeiraVisita;
    private int nrDiasVigenteQuestionarioVista = 0;
    @FKJson
    private QuestionarioVO questionarioRetorno;
    private int nrDiasVigenteQuestionarioRetorno = 0;
    @FKJson
    private QuestionarioVO questionarioReMatricula;
    private int nrDiasVigenteQuestionarioRematricula = 0;
    @FKJson
    private QuestionarioVO questionarioPrimeiraCompra;
    private int nrDiasVigenteQuestionarioPrimeiraCompra = 0;
    @FKJson
    private QuestionarioVO questionarioRetornoCompra;
    private int nrDiasVigenteQuestionarioRetornoCompra = 0;
    private boolean matriculaOb = false;
    private boolean matriculaApresentar = false;
    private boolean matriculaPendente = false;
    private boolean nomeOb = false;
    private boolean nomeApresentar = false;
    private boolean nomePendente = false;
    private boolean dataNascOb = false;
    private boolean dataNascApresentar = false;
    private boolean dataNascPendente = false;
    private boolean nomePaiOb = false;
    private boolean nomePaiApresentar = false;
    private boolean nomePaiPendente = false;
    private boolean nomeMaeOb = false;
    private boolean nomeMaeApresentar = false;
    private boolean nomeMaePendente = false;
    private boolean cfpOb = false;
    private boolean cpfApresentar = false;
    private boolean cpfPendente = false;
    private boolean rgOb = false;
    private boolean rgApresentar = false;
    private boolean rgPendente = false;
    private boolean estadoCivilOb = false;
    private boolean estadoCivilApresentar = false;
    private boolean estadoCivilPendente = false;
    private boolean sexoOb = false;
    private boolean sexoApresentar = false;
    private boolean sexoPendente = false;
    private boolean emailOb = false;
    private boolean emailApresentar = false;
    private boolean emailPendente = false;
    private boolean webPageOb = false;
    private boolean webPageApresentar = false;
    private boolean webPagePendente = false;
    private boolean telefoneOb = false;
    private boolean telefoneApresentar = false;
    private boolean telefonePendente = false;
    private boolean descricaoTefOb = false;
    private boolean descricaoTefObApresentar = false;
    private boolean descricaoTefObPendente = false;
    private boolean categoriaOb = false;
    private boolean categoriaApresentar = false;
    private boolean categoriaPendente = false;
    private boolean profissaoOb = false;
    private boolean profissaoApresentar = false;
    private boolean profissaoPendente = false;
    private boolean grauInstrucaoOb = false;
    private boolean grauInstrucaoApresentar = false;
    private boolean grauInstrucaoPendente = false;
    private boolean cidadeOb = false;
    private boolean cidadeApresentar = false;
    private boolean cidadePendente = false;
    private boolean estadoOb = false;
    private boolean estadoApresentar = false;
    private boolean estadoPendente = false;
    private boolean paisOb = false;
    private boolean paisApresentar = false;
    private boolean paisPendente = false;
    private boolean cepOb = false;
    private boolean cepApresentar = false;
    private boolean cepPendente = false;
    private boolean bairroOb = false;
    private boolean bairroApresentar = false;
    private boolean bairroPendente = false;
    private boolean numeroOb = false;
    private boolean numeroApresentar = false;
    private boolean numeroPendente = false;
    private boolean enderecoOb = false;
    private boolean enderecoApresentar = false;
    private boolean enderecoPendente = false;
    private boolean enderecoComplementoOb = false;
    private boolean enderecoComplementoApresentar = false;
    private boolean enderecoComplementoPendente = false;
    private boolean cpfPaiOb = false;
    private boolean cpfPaiApresentar = false;
    private boolean cpfPaiPendente = false;
    private boolean cpfMaeOb = false;
    private boolean cpfMaeApresentar = false;
    private boolean cpfMaePendente = false;
    private boolean rgPaiOb = false;
    private boolean rgPaiApresentar = false;
    private boolean rgPaiPendente = false;
    private boolean rgMaeOb = false;
    private boolean rgMaeApresentar = false;
    private boolean rgMaePendente = false;

    // ################# CAMPOS CADASTRO SIMPLIFICADO DE CLIENTE - RESPONSÁVEL FINANCEIRO #################
    private boolean nomeRespFinanceiroOb = false;
    private boolean nomeRespFinanceiroApresentar = false;
    private boolean nomeRespFinanceiroPendente = false;

    private boolean cpfRespFinanceiroOb = false;
    private boolean cpfRespFinanceiroApresentar = false;
    private boolean cpfRespFinanceiroPendente = false;

    private boolean rgRespFinanceiroOb = false;
    private boolean rgRespFinanceiroApresentar = false;
    private boolean rgRespFinanceiroPendente = false;

    private boolean emailRespFinanceiroOb = false;
    private boolean emailRespFinanceiroApresentar = false;
    private boolean emailRespFinanceiroPendente = false;

    // ################# ################# #################

    private int carenciaRenovacao = 0;
    private int nrDiasAvencer = 0;
    private int toleranciaPagamento = 0;
    private int qtdFaltaPeso1 = 0;
    private int qtdFaltaInicioPeso2 = 0;
    private int qtdFaltaTerminoPeso2 = 0;
    private int qtdFaltaPeso3 = 0;
    private int carencia = 0;
    private int qtdAcessoGymPassInicioFaixa1 = 0;
    private int qtdAcessoGymPassInicioFaixa2 = 0;
    private int qtdAcessoGymPassInicioFaixa3 = 0;
    private int qtdAcessoGymPassInicioFaixa4 = 0;
    private int qtdAcessoGymPassFinalFaixa1 = 0;
    private int qtdAcessoGymPassFinalFaixa2 = 0;
    private int qtdAcessoGymPassFinalFaixa3 = 0;
    private int qtdAcessoGymPassFinalFaixa4 = 0;
    private boolean cpfValidar = false;
    private boolean nomeDataNascValidar = true;
    private boolean acessoChamada = false;
    @FKJson
    private LocalAcessoVO localAcessoChamada = new LocalAcessoVO();
    @FKJson
    private ColetorVO coletorChamada = new ColetorVO();
    private int nrDiasProrata = 0;
    @NaoControlarLogAlteracao
    private List listaConfiguracaoCamposCliente;
    @NaoControlarLogAlteracao
    private List listaConfiguracaoCamposVisitante;
    private String urlGoogleAgenda = "";
    private boolean rodarSqlsBancoInicial;
    private int vencimentoColaborador = 1;
    private int toleranciaDiasContratoVencido = 0;
    private String urlRecorrencia;
    private List<String> listaEmailsRecorrencia;
    //tributa??o de produto para emiss?o e CF
    private double aliquotaServico;
    private double aliquotaProduto;
    private boolean usaEcf;
    private boolean ecfPorPagamento;
    private boolean ecfApenasPlano;
    //campo de configura??o de edi??o de contrato ( altera??o da data base do contrato)
    private boolean alteracaoDataBaseContrato = false;
    private int qtdDiasExpirarSenha = 0;
    /* Atributo quantidade de dias que o Sistema ir? esperar para cada contrato sem recebimento de parcelas
     * para efetuar o estorno autom?tico.
     * Se informado 0 (zero) n?o ? executado o estorno autom?tico.
     */
    private int qtdDiasEstornoAutomaticoContrato = 0;

    private int qtdDiaPrimeiraParcelaVencidaEstornarContrato = 0;

    private int qtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline = 0;

    private Date dataInicioDesconsiderarAcessoRisco;
    private Date dataFimDesconsiderarAcessoRisco;


    private Date dataUltimaRepescagem;
    private String emailsFechamentoAcessos;
    private boolean bloquearAcessoSeParcelaAberta;
    private boolean enviarSMSAutomatico;
    private boolean enviarRemessasRemotamente;
    private boolean validarContatoMeta;
    private boolean forcarCodigoAlternativoAcesso = false;
    private boolean itemVendaAvulsaAutomatico = true;
    private boolean validarCpfDuplicado = false;

    private boolean usarNomeResponsavelNota = false;
    private boolean validarCPFResponsaveis = true;
    private boolean utilizarSistemaParaClube = false;
    private boolean imprimirReciboPagtoMatricial = false;
    private Boolean habilitarGestaoArmarios = Boolean.FALSE;
    private int diaProrataArmario;


    private String numeroCielo = "";
    private boolean marcarPresencaPeloAcesso = false;

    private Boolean defaultEnderecoCorrespondecia = false;
    private String nomenclaturaVendaCredito;

    @NaoControlarLogAlteracao
    private List<ConfiguracaoSistemaCadastroClienteVO> listaConfiguracaoCamposClienteOriginal;
    @NaoControlarLogAlteracao
    private List<ConfiguracaoSistemaCadastroClienteVO> listaConfiguracaoCamposVisitanteOriginal;

    private Integer sequencialItem = 1;
    private Integer sequencialArquivo = 1;
    private Boolean sesc = false;
    private Boolean apiSescGo = false;

    private String usuarioApiSescGo;

    private String senhaApiSescGo;
    private Boolean sesiCe = false;
    private Boolean priorizarVendaRapida = false;
    private Boolean barrarDevedorVendaRapida = false;
    private boolean permitirMudarTipoParcelamentoVendaRapida = false;
    private Boolean lancamentoContratosIguais = false;

    private boolean controleAcessoMultiplasEmpresasPorPlano = false;
    private boolean usaAprovaFacil = true;
    private boolean usarDigitalComoAssinatura = false;
    private boolean propagaraAssinaturaDigital = false;
    private boolean termoResponsabilidade = false;

    private boolean termoResponsabilidadeExaluno = false;
    private boolean manterContratoAssinadoNaRenovacaoContrato = false;

    /**
     * Ao transferir este aluno, o contrato ser? cancelado na empresa de origem e um
     * novo contrato com a mesma vig?ncia final ser? gerado na empresa de destino.
     * Sendo assim, todas as parcelas em aberto do cliente ser?o transferidas para empresa de destino.
     */
    private boolean cancelarContratoNaUnidadeOrigemAoTransferirAluno = false;
    private Boolean utilizarTipoPlano = false;
    private boolean transferirAutorizacaoCobranca = false;
    private boolean nomeArquivoRemessaPadraoTivit = false;
    private boolean definirDataInicioPlanosRecorrencia = false;
    private boolean contatoEmergenciaApresentar = false;
    private boolean contatoEmergenciaOb = false;
    private boolean contatoEmergenciaPendente = false;
    private boolean exibirModalInativarUsuarios = false;
    private boolean usarSistemaInternacional = false;
    private boolean usarVerificadorRemessasRejeitadas = false;
    private boolean agruparRemessasGetnet = false;
    private boolean apresentarMarketPlace = false;
    private boolean telefoneEmergenciaPendente = false;
    private boolean telefoneEmergenciaApresentar = false;
    private boolean telefoneEmergenciaOb = false;
    private boolean agruparRemessasCartaoEDI = false;
    private boolean permitirReplicarPlanoRedeEmpresa = false;
    private boolean permitirReplicarFeriadoRedeEmpresa = false;
    private boolean permitirReplicarPerfilAcessoRedeEmpresa = false;
    private boolean permitirReplicarUsuarioRedeEmpresa = false;
    private boolean permitirReplicarFornecedorRedeEmpresa = false;
    private boolean forcarUtilizacaoPlanoAntigo = false;
    private boolean assinaturaContratoViaEmail = false;
    private String chavePublicaSESC;
    private String chavePrivadaSESC;
    private boolean generoOb = false;
    private boolean generoApresentar = false;
    private boolean generoPendente = false;

    private boolean nomeRegistroOb = false;
    private boolean nomeRegistroApresentar = false;
    private boolean nomeRegistroPendente = false;
    private boolean utilizarServicoSesiSC = false;
    private boolean realizarEnvioSesiSC = false;
    private String mascaraTelefone = "";
    private boolean utilizarFormatoMMDDYYYDtNascimento = false;
    private Boolean lumi = false;
    private boolean permiteTrocaEmpresaMultiChave = false;
    private boolean permiteLancarFeriasPlanoRecorrente = false;
    private boolean usaPlanoRecorrenteCompartilhado = false;
    private boolean permiteImpressaoContratoMutavel = false;
    private boolean conciliarSemNumeroParcela = false;
    private boolean loginatravesazuread = false;
    private String azureadtenatid = "";
    private String azureadclientid = "";
    private boolean apresentarCnpjSesi = false;
    private boolean objCnpjSesi = false;
    private boolean pendenteCnpjSesi = false;
    private String tituloConvite = "";
    private String descricaoConvite = "";
    private Integer tempoReabilitacaoExAluno = 0;
    private boolean permiteEstornarContrato30MinAposLancamento = false;
    private int idadeMinima = 0;

    public boolean getEnderecoComplementoOb() {
        return enderecoComplementoOb;
    }

    public boolean getNomeRespFinanceiroOb() {
        return nomeRespFinanceiroOb;
    }

    public void setNomeRespFinanceiroOb(boolean nomeRespFinanceiroOb) {
        this.nomeRespFinanceiroOb = nomeRespFinanceiroOb;
    }

    public boolean getNomeRespFinanceiroApresentar() {
        return nomeRespFinanceiroApresentar;
    }

    public void setNomeRespFinanceiroApresentar(boolean nomeRespFinanceiroApresentar) {
        this.nomeRespFinanceiroApresentar = nomeRespFinanceiroApresentar;
    }

    public boolean getNomeRespFinanceiroPendente() {
        return nomeRespFinanceiroPendente;
    }

    public void setNomeRespFinanceiroPendente(boolean nomeRespFinanceiroPendente) {
        this.nomeRespFinanceiroPendente = nomeRespFinanceiroPendente;
    }

    public boolean getCpfRespFinanceiroOb() {
        return cpfRespFinanceiroOb;
    }

    public void setCpfRespFinanceiroOb(boolean cpfRespFinanceiroOb) {
        this.cpfRespFinanceiroOb = cpfRespFinanceiroOb;
    }

    public boolean getCpfRespFinanceiroApresentar() {
        return cpfRespFinanceiroApresentar;
    }

    public void setCpfRespFinanceiroApresentar(boolean cpfRespFinanceiroApresentar) {
        this.cpfRespFinanceiroApresentar = cpfRespFinanceiroApresentar;
    }

    public boolean getCpfRespFinanceiroPendente() {
        return cpfRespFinanceiroPendente;
    }

    public void setCpfRespFinanceiroPendente(boolean cpfRespFinanceiroPendente) {
        this.cpfRespFinanceiroPendente = cpfRespFinanceiroPendente;
    }

    public boolean getRgRespFinanceiroOb() {
        return rgRespFinanceiroOb;
    }

    public void setRgRespFinanceiroOb(boolean rgRespFinanceiroOb) {
        this.rgRespFinanceiroOb = rgRespFinanceiroOb;
    }

    public boolean getRgRespFinanceiroApresentar() {
        return rgRespFinanceiroApresentar;
    }

    public void setRgRespFinanceiroApresentar(boolean rgRespFinanceiroApresentar) {
        this.rgRespFinanceiroApresentar = rgRespFinanceiroApresentar;
    }

    public boolean getRgRespFinanceiroPendente() {
        return rgRespFinanceiroPendente;
    }

    public void setRgRespFinanceiroPendente(boolean rgRespFinanceiroPendente) {
        this.rgRespFinanceiroPendente = rgRespFinanceiroPendente;
    }

    public boolean getEmailRespFinanceiroOb() {
        return emailRespFinanceiroOb;
    }

    public void setEmailRespFinanceiroOb(boolean emailRespFinanceiroOb) {
        this.emailRespFinanceiroOb = emailRespFinanceiroOb;
    }

    public boolean getEmailRespFinanceiroApresentar() {
        return emailRespFinanceiroApresentar;
    }

    public void setEmailRespFinanceiroApresentar(boolean emailRespFinanceiroApresentar) {
        this.emailRespFinanceiroApresentar = emailRespFinanceiroApresentar;
    }

    public boolean getEmailRespFinanceiroPendente() {
        return emailRespFinanceiroPendente;
    }

    public void setEmailRespFinanceiroPendente(boolean emailRespFinanceiroPendente) {
        this.emailRespFinanceiroPendente = emailRespFinanceiroPendente;
    }

    public boolean permitirReplicarModeloContratoRedeEmpresa = false;
    private boolean exibirModalPlanosInativos = false;

    public void setEnderecoComplementoOb(boolean enderecoComplementoOb) {
        this.enderecoComplementoOb = enderecoComplementoOb;
    }

    public ConfiguracaoSistemaVO() {
        super();
        inicializarDados();
    }

    /**
     * Opera??o respons?vel por validar os dados de um objeto da classe <code>ConfiguracaoSistemaVO</code>.
     * Todos os tipos de consist?ncia de dados s?o e devem ser implementadas neste método.
     * S?o valida??es t?picas: verifica??o de campos obrigat?rios, verifica??o de valores v?lidos para os atributos.
     * @exception ConsistirException Se uma inconsist?ncia for encontrada aumaticamente ? gerada uma exce??o descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ConfiguracaoSistemaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getQtdAcessoGymPassInicioFaixa1() >= obj.getQtdAcessoGymPassFinalFaixa1() ||
                obj.getQtdAcessoGymPassInicioFaixa2() >= obj.getQtdAcessoGymPassFinalFaixa2() ||
                obj.getQtdAcessoGymPassInicioFaixa3() >= obj.getQtdAcessoGymPassFinalFaixa3() ||
                obj.getQtdAcessoGymPassInicioFaixa4() >= obj.getQtdAcessoGymPassFinalFaixa4()) {
            throw new ConsistirException("O campo inicial deve ter um valor menor do que o campo final de cada faixa de quantidade de acessos!");
        }

        if ((obj.getQuestionarioPrimeiraVisita() == null)
                || (obj.getQuestionarioPrimeiraVisita().getCodigo() == 0)) {
            throw new ConsistirException("O campo QUESTIONÁRIO DE PRIMEIRA VISITA (Configuração do Sistema) deve ser informado.");
        }
        if (obj.getQuestionarioPrimeiraVisita().getCodigo() != 0 && obj.getNrDiasVigenteQuestionarioVista() == 0) {
            throw new ConsistirException("O campo NÚMERO DIAS VIGENTES PRIMEIRA VISITA (Configuração do Sistema) deve ser informado.");
        }
        if (!UteisValidacao.emptyNumber(obj.getNrDiasProrata()) && obj.getNrDiasProrata() > 30) {
            throw new ConsistirException("O NÚMERO MÁX.DE DIAS PARA GERAR UM MÊS PRÓ-RATA A MAIS não pode ser maior que 30.");
        }

        if ((obj.getQuestionarioRetorno() == null)
                || (obj.getQuestionarioRetorno().getCodigo() == 0)) {
            throw new ConsistirException("O campo QUESTIONÁRIO DE RETORNO (Configuração do Sistema) deve ser informado.");
        }
        if (obj.getQuestionarioRetorno().getCodigo() != 0 && obj.getNrDiasVigenteQuestionarioRetorno() == 0) {
            throw new ConsistirException("O campo NÚMERO DIAS VIGENTES RETORNO (Configuração do Sistema) deve ser informado.");
        }
        if ((obj.getQuestionarioReMatricula() == null)
                || (obj.getQuestionarioReMatricula().getCodigo() == 0)) {
            throw new ConsistirException("O campo QUESTIONÁRIO DE REMATRÍCULA (Configuração do Sistema) deve ser informado.");
        }
        if (obj.getQuestionarioReMatricula().getCodigo() != 0 && obj.getNrDiasVigenteQuestionarioRematricula() == 0) {
            throw new ConsistirException("O campo NÚMERO DIAS VIGENTES REMATRICULA (Configuração do Sistema) deve ser informado.");
        }
        if (obj.getQtdFaltaPeso1() != 0) {
            if (obj.getQtdFaltaInicioPeso2() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaInicioPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1 (Configuração do Sistema) não pode ser Maior ou Igual ao campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2.");
            }
            if (obj.getQtdFaltaTerminoPeso2() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaTerminoPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1 (Configuração do Sistema) não pode ser Maior ou Igual ao campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2.");
            }
            if (obj.getQtdFaltaPeso3() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1 (Configuração do Sistema) não pode ser Maior ou Igual ao campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3.");
            }
        }
        if (obj.getQtdFaltaInicioPeso2() != 0) {
            if (obj.getQtdFaltaPeso1() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaInicioPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Menor ou Igual ao campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1.");
            }
            if (obj.getQtdFaltaTerminoPeso2() != 0
                    && (obj.getQtdFaltaInicioPeso2() > obj.getQtdFaltaTerminoPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior que o campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2.");
            }
            if (obj.getQtdFaltaPeso3() != 0
                    && (obj.getQtdFaltaInicioPeso2() >= obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior que o campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3.");
            }
        }
        if (obj.getQtdFaltaTerminoPeso2() != 0) {
            if (obj.getQtdFaltaPeso1() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaTerminoPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Menor ou Igual ao campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1.");
            }
            if (obj.getQtdFaltaInicioPeso2() != 0
                    && (obj.getQtdFaltaInicioPeso2() > obj.getQtdFaltaTerminoPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior que o campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2.");
            }
            if (obj.getQtdFaltaPeso3() != 0
                    && (obj.getQtdFaltaTerminoPeso2() >= obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior ou Igual que o campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3.");
            }
        }
        if (obj.getQtdFaltaPeso3() != 0) {
            if (obj.getQtdFaltaPeso1() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3 (Configuração do Sistema) não pode ser Menor ou Igual ao campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1.");
            }
            if (obj.getQtdFaltaInicioPeso2() != 0
                    && (obj.getQtdFaltaInicioPeso2() > obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior que o campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3.");
            }
            if (obj.getQtdFaltaTerminoPeso2() != 0
                    && (obj.getQtdFaltaTerminoPeso2() >= obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior ou Igual que o campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3.");
            }
        }

        if ((obj.getDataInicioDesconsiderarAcessoRisco() == null && obj.getDataFimDesconsiderarAcessoRisco() != null) ||
                (obj.getDataInicioDesconsiderarAcessoRisco() != null && obj.getDataFimDesconsiderarAcessoRisco() == null) ||
                ((obj.getDataInicioDesconsiderarAcessoRisco() != null && obj.getDataFimDesconsiderarAcessoRisco() != null && Calendario.menor(obj.getDataFimDesconsiderarAcessoRisco(), obj.getDataInicioDesconsiderarAcessoRisco())))) {
            throw new ConsistirException("Peíodo a desconsiderar inválido, verifique!");
        }
        if (obj.getCarencia() == 0) {
            throw new ConsistirException("O campo NÚMERO MÍNIMO DE FÉRIAS (Configuração do Sistema) deve ser maior que zero.");
        }

        if (obj.getCarenciaRenovacao() > 28 || obj.getCarenciaRenovacao() < 1) {
            throw new ConsistirException("O campo CARÊNCIA RENOVAÇÃO deve ser entre 1 e 28 dias.");
        }

        if (obj.getNrDiasAvencer() > 28 || obj.getNrDiasAvencer() < 1) {
            throw new ConsistirException("O campo Número de Dias A Vencer Renovação deve ser entre 1 e 28 dias.");
        }

        if (obj.getCarenciaRenovacao() < 0) {
            throw new ConsistirException("O campo CARÊNCIA RENOVAÇÃO deve ser no mínimo 0 dias.");
        }

        if (obj.getVencimentoColaborador() < 0) {
            throw new ConsistirException("O Dia do Vencimento não pode ser Menor que Zero.");
        }
        if (obj.getVencimentoColaborador() > 30) {
            throw new ConsistirException("Ultimo Dia valido do Mês é o dia 30.");
        }

        if (!(obj.getQtdDiasExpirarSenha() > 7 && obj.getQtdDiasExpirarSenha() < 366)) {
            throw new ConsistirException("A quantidade de dias para a senha expirar deve ser maior do que 7 e menor do que 366 dias.");
        }
        // recurso foi passado para a empresa
//        if (obj.isAcessoChamada() && (obj.getLocalAcessoChamada().getCodigo() == 0 || obj.getColetorChamada().getCodigo() == 0)) {
//            throw new ConsistirException("Para usar Acesso na Chamada escolha uma Local de Acesso e um Coletor.");
//        }
        if (UteisValidacao.emptyString(obj.getMascaraMatricula())) {
            throw new ConsistirException("O campo Máscara Matrícula deve ser informado.");
        }

        if ((obj.getMascaraTelefone().contains("(") || obj.getMascaraTelefone().contains(")") || obj.getMascaraTelefone().contains("-")) && !obj.getMascaraTelefone().contains("#")){
            throw new ConsistirException("Campo Máscara Telefone inválido, favor corrigir.");
        }

        validarDadosConfiguracaoCadastrosCliente(obj);
    }

    /**
     * Opera??o repons?vel por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setQuestionarioPrimeiraVisita(new QuestionarioVO());
        setQuestionarioRetorno(new QuestionarioVO());
        setQuestionarioReMatricula(new QuestionarioVO());
        setListaConfiguracaoCamposCliente(new ArrayList());
        setEmailsFechamentoAcessos("");
    }

    public QuestionarioVO getQuestionarioReMatricula() {
        if (questionarioReMatricula == null) {
            questionarioReMatricula = new QuestionarioVO();
        }
        return (questionarioReMatricula);
    }

    public void setQuestionarioReMatricula(QuestionarioVO obj) {
        this.questionarioReMatricula = obj;
    }

    public QuestionarioVO getQuestionarioRetorno() {
        if (questionarioRetorno == null) {
            questionarioRetorno = new QuestionarioVO();
        }
        return (questionarioRetorno);
    }

    public void setQuestionarioRetorno(QuestionarioVO obj) {
        this.questionarioRetorno = obj;
    }

    public QuestionarioVO getQuestionarioPrimeiraVisita() {
        if (questionarioPrimeiraVisita == null) {
            questionarioPrimeiraVisita = new QuestionarioVO();
        }
        return (questionarioPrimeiraVisita);
    }

    public void setQuestionarioPrimeiraVisita(QuestionarioVO obj) {
        this.questionarioPrimeiraVisita = obj;
    }

    public boolean getBairroApresentar() {
        return bairroApresentar;
    }

    public boolean getBairroOb() {
        return bairroOb;
    }

    public boolean getBairroPendente() {
        return bairroPendente;
    }

    public int getCarencia() {
        return carencia;
    }

    public int getCarenciaRenovacao() {
        return carenciaRenovacao;
    }

    public boolean getCategoriaApresentar() {
        return categoriaApresentar;
    }

    public boolean getCategoriaOb() {
        return categoriaOb;
    }

    public boolean getCategoriaPendente() {
        return categoriaPendente;
    }

    public boolean getCepApresentar() {
        return cepApresentar;
    }

    public boolean getCepOb() {
        return cepOb;
    }

    public boolean getCepPendente() {
        return cepPendente;
    }

    public boolean getCfpOb() {
        return cfpOb;
    }

    public boolean getCidadeApresentar() {
        return cidadeApresentar;
    }

    public boolean getCidadeOb() {
        return cidadeOb;
    }

    public boolean getCidadePendente() {
        return cidadePendente;
    }

    public boolean getCpfApresentar() {
        return cpfApresentar;
    }

    public boolean getCpfPendente() {
        return cpfPendente;
    }

    public boolean isCpfValidar() {
        return cpfValidar;
    }

    public boolean getDataNascApresentar() {
        return dataNascApresentar;
    }

    public boolean getDataNascOb() {
        return dataNascOb;
    }

    public boolean getDataNascPendente() {
        return dataNascPendente;
    }

    public boolean getEmailApresentar() {
        return emailApresentar;
    }

    public boolean getEmailOb() {
        return emailOb;
    }

    public boolean getEmailPendente() {
        return emailPendente;
    }

    public boolean getEnderecoApresentar() {
        return enderecoApresentar;
    }

    public boolean getEnderecoComplementoApresentar() {
        return enderecoComplementoApresentar;
    }

    public boolean getEnderecoComplementoPendente() {
        return enderecoComplementoPendente;
    }

    public boolean getEnderecoOb() {
        return enderecoOb;
    }

    public boolean getEnderecoPendente() {
        return enderecoPendente;
    }

    public boolean getEstadoApresentar() {
        return estadoApresentar;
    }

    public boolean getEstadoCivilApresentar() {
        return estadoCivilApresentar;
    }

    public boolean getEstadoCivilOb() {
        return estadoCivilOb;
    }

    public boolean getEstadoCivilPendente() {
        return estadoCivilPendente;
    }

    public boolean getEstadoOb() {
        return estadoOb;
    }

    public boolean getEstadoPendente() {
        return estadoPendente;
    }

    public boolean getGrauInstrucaoApresentar() {
        return grauInstrucaoApresentar;
    }

    public boolean getGrauInstrucaoOb() {
        return grauInstrucaoOb;
    }

    public boolean getGrauInstrucaoPendente() {
        return grauInstrucaoPendente;
    }

    public int getHiddenQuantRegistrosConfCliente() {
        return hiddenQuantRegistrosConfCliente;
    }

    public double getJuroParcela() {
        return juroParcela;
    }

    public List getListaConfiguracaoCamposCliente() {
        return listaConfiguracaoCamposCliente;
    }

    public String getMascaraMatricula() {
        return mascaraMatricula;
    }

    public boolean getMatriculaApresentar() {
        return matriculaApresentar;
    }

    public boolean getMatriculaOb() {
        return matriculaOb;
    }

    public boolean getMatriculaPendente() {
        return matriculaPendente;
    }

    public double getMulta() {
        return multa;
    }

    public boolean getNomeApresentar() {
        return nomeApresentar;
    }

    public boolean getNomeMaeApresentar() {
        return nomeMaeApresentar;
    }

    public boolean getNomeMaeOb() {
        return nomeMaeOb;
    }

    public boolean getNomeMaePendente() {
        return nomeMaePendente;
    }

    public boolean getNomeOb() {
        return nomeOb;
    }

    public boolean getNomePaiApresentar() {
        return nomePaiApresentar;
    }

    public boolean getNomePaiOb() {
        return nomePaiOb;
    }

    public boolean getNomePaiPendente() {
        return nomePaiPendente;
    }

    public boolean getNomePendente() {
        return nomePendente;
    }

    public int getNrDiasAvencer() {
        return nrDiasAvencer;
    }

    public int getNrDiasVigenteQuestionarioRematricula() {
        return nrDiasVigenteQuestionarioRematricula;
    }

    public int getNrDiasVigenteQuestionarioRetorno() {
        return nrDiasVigenteQuestionarioRetorno;
    }

    public int getNrDiasVigenteQuestionarioVista() {
        return nrDiasVigenteQuestionarioVista;
    }

    public boolean getNumeroApresentar() {
        return numeroApresentar;
    }

    public boolean getNumeroOb() {
        return numeroOb;
    }

    public boolean getNumeroPendente() {
        return numeroPendente;
    }

    public boolean getPaisApresentar() {
        return paisApresentar;
    }

    public boolean getPaisOb() {
        return paisOb;
    }

    public boolean getPaisPendente() {
        return paisPendente;
    }

    public boolean getProfissaoApresentar() {
        return profissaoApresentar;
    }

    public boolean getProfissaoOb() {
        return profissaoOb;
    }

    public boolean getProfissaoPendente() {
        return profissaoPendente;
    }

    public int getQtdFaltaInicioPeso2() {
        return qtdFaltaInicioPeso2;
    }

    public int getQtdFaltaPeso1() {
        return qtdFaltaPeso1;
    }

    public int getQtdFaltaPeso3() {
        return qtdFaltaPeso3;
    }

    public int getQtdFaltaTerminoPeso2() {
        return qtdFaltaTerminoPeso2;
    }

    public int getQtdAcessoGymPassInicioFaixa1() {
        return qtdAcessoGymPassInicioFaixa1;
    }

    public void setQtdAcessoGymPassInicioFaixa1(int qtdAcessoGymPassInicioFaixa1) {
        this.qtdAcessoGymPassInicioFaixa1 = qtdAcessoGymPassInicioFaixa1;
    }

    public int getQtdAcessoGymPassInicioFaixa2() {
        return qtdAcessoGymPassInicioFaixa2;
    }

    public void setQtdAcessoGymPassInicioFaixa2(int qtdAcessoGymPassInicioFaixa2) {
        this.qtdAcessoGymPassInicioFaixa2 = qtdAcessoGymPassInicioFaixa2;
    }

    public int getQtdAcessoGymPassInicioFaixa3() {
        return qtdAcessoGymPassInicioFaixa3;
    }

    public void setQtdAcessoGymPassInicioFaixa3(int qtdAcessoGymPassInicioFaixa3) {
        this.qtdAcessoGymPassInicioFaixa3 = qtdAcessoGymPassInicioFaixa3;
    }

    public int getQtdAcessoGymPassInicioFaixa4() {
        return qtdAcessoGymPassInicioFaixa4;
    }

    public void setQtdAcessoGymPassInicioFaixa4(int qtdAcessoGymPassInicioFaixa4) {
        this.qtdAcessoGymPassInicioFaixa4 = qtdAcessoGymPassInicioFaixa4;
    }

    public int getQtdAcessoGymPassFinalFaixa1() {
        return qtdAcessoGymPassFinalFaixa1;
    }

    public void setQtdAcessoGymPassFinalFaixa1(int qtdAcessoGymPassFinalFaixa1) {
        this.qtdAcessoGymPassFinalFaixa1 = qtdAcessoGymPassFinalFaixa1;
    }

    public int getQtdAcessoGymPassFinalFaixa2() {
        return qtdAcessoGymPassFinalFaixa2;
    }

    public void setQtdAcessoGymPassFinalFaixa2(int qtdAcessoGymPassFinalFaixa2) {
        this.qtdAcessoGymPassFinalFaixa2 = qtdAcessoGymPassFinalFaixa2;
    }

    public int getQtdAcessoGymPassFinalFaixa3() {
        return qtdAcessoGymPassFinalFaixa3;
    }

    public void setQtdAcessoGymPassFinalFaixa3(int qtdAcessoGymPassFinalFaixa3) {
        this.qtdAcessoGymPassFinalFaixa3 = qtdAcessoGymPassFinalFaixa3;
    }

    public int getQtdAcessoGymPassFinalFaixa4() {
        return qtdAcessoGymPassFinalFaixa4;
    }

    public void setQtdAcessoGymPassFinalFaixa4(int qtdAcessoGymPassFinalFaixa4) {
        this.qtdAcessoGymPassFinalFaixa4 = qtdAcessoGymPassFinalFaixa4;
    }

    public boolean getRgApresentar() {
        return rgApresentar;
    }

    public boolean getRgOb() {
        return rgOb;
    }

    public boolean getRgPendente() {
        return rgPendente;
    }

    public boolean getSexoApresentar() {
        return sexoApresentar;
    }

    public boolean getSexoOb() {
        return sexoOb;
    }

    public boolean getSexoPendente() {
        return sexoPendente;
    }

    public boolean getTelefoneApresentar() {
        return telefoneApresentar;
    }

    public boolean getTelefoneOb() {
        return telefoneOb;
    }

    public boolean getTelefonePendente() {
        return telefonePendente;
    }

    public int getToleranciaPagamento() {
        return toleranciaPagamento;
    }

    public boolean getWebPageApresentar() {
        return webPageApresentar;
    }

    public boolean getWebPageOb() {
        return webPageOb;
    }

    public boolean getWebPagePendente() {
        return webPagePendente;
    }

    public void setBairroApresentar(boolean bairroApresentar) {
        this.bairroApresentar = bairroApresentar;
    }

    public void setBairroOb(boolean bairroOb) {
        this.bairroOb = bairroOb;
    }

    public void setBairroPendente(boolean bairroPendente) {
        this.bairroPendente = bairroPendente;
    }

    public void setCarencia(int carencia) {
        this.carencia = carencia;
    }

    public void setCarenciaRenovacao(int carenciaRenovacao) {
        this.carenciaRenovacao = carenciaRenovacao;
    }

    public void setCategoriaApresentar(boolean categoriaApresentar) {
        this.categoriaApresentar = categoriaApresentar;
    }

    public void setCategoriaOb(boolean categoriaOb) {
        this.categoriaOb = categoriaOb;
    }

    public void setCategoriaPendente(boolean categoriaPendente) {
        this.categoriaPendente = categoriaPendente;
    }

    public void setCepApresentar(boolean cepApresentar) {
        this.cepApresentar = cepApresentar;
    }

    public void setCepOb(boolean cepOb) {
        this.cepOb = cepOb;
    }

    public void setCepPendente(boolean cepPendente) {
        this.cepPendente = cepPendente;
    }

    public void setCfpOb(boolean cfpOb) {
        this.cfpOb = cfpOb;
    }

    public void setCidadeApresentar(boolean cidadeApresentar) {
        this.cidadeApresentar = cidadeApresentar;
    }

    public void setCidadeOb(boolean cidadeOb) {
        this.cidadeOb = cidadeOb;
    }

    public void setCidadePendente(boolean cidadePendente) {
        this.cidadePendente = cidadePendente;
    }

    public void setCpfApresentar(boolean cpfApresentar) {
        this.cpfApresentar = cpfApresentar;
    }

    public void setCpfPendente(boolean cpfPendente) {
        this.cpfPendente = cpfPendente;
    }

    public void setCpfValidar(boolean cpfValidar) {
        this.cpfValidar = cpfValidar;
    }

    public void setDataNascApresentar(boolean dataNascApresentar) {
        this.dataNascApresentar = dataNascApresentar;
    }

    public void setDataNascOb(boolean dataNascOb) {
        this.dataNascOb = dataNascOb;
    }

    public void setDataNascPendente(boolean dataNascPendente) {
        this.dataNascPendente = dataNascPendente;
    }

    public void setEmailApresentar(boolean emailApresentar) {
        this.emailApresentar = emailApresentar;
    }

    public void setEmailOb(boolean emailOb) {
        this.emailOb = emailOb;
    }

    public void setEmailPendente(boolean emailPendente) {
        this.emailPendente = emailPendente;
    }

    public void setEnderecoApresentar(boolean enderecoApresentar) {
        this.enderecoApresentar = enderecoApresentar;
    }

    public void setEnderecoComplementoApresentar(boolean enderecoComplementoApresentar) {
        this.enderecoComplementoApresentar = enderecoComplementoApresentar;
    }

    public void setEnderecoComplementoPendente(boolean enderecoComplementoPendente) {
        this.enderecoComplementoPendente = enderecoComplementoPendente;
    }

    public void setEnderecoOb(boolean enderecoOb) {
        this.enderecoOb = enderecoOb;
    }

    public void setEnderecoPendente(boolean enderecoPendente) {
        this.enderecoPendente = enderecoPendente;
    }

    public void setEstadoApresentar(boolean estadoApresentar) {
        this.estadoApresentar = estadoApresentar;
    }

    public void setEstadoCivilApresentar(boolean estadoCivilApresentar) {
        this.estadoCivilApresentar = estadoCivilApresentar;
    }

    public void setEstadoCivilOb(boolean estadoCivilOb) {
        this.estadoCivilOb = estadoCivilOb;
    }

    public void setEstadoCivilPendente(boolean estadoCivilPendente) {
        this.estadoCivilPendente = estadoCivilPendente;
    }

    public void setEstadoOb(boolean estadoOb) {
        this.estadoOb = estadoOb;
    }

    public void setEstadoPendente(boolean estadoPendente) {
        this.estadoPendente = estadoPendente;
    }

    public void setGrauInstrucaoApresentar(boolean grauInstrucaoApresentar) {
        this.grauInstrucaoApresentar = grauInstrucaoApresentar;
    }

    public void setGrauInstrucaoOb(boolean grauInstrucaoOb) {
        this.grauInstrucaoOb = grauInstrucaoOb;
    }

    public void setGrauInstrucaoPendente(boolean grauInstrucaoPendente) {
        this.grauInstrucaoPendente = grauInstrucaoPendente;
    }

    public void setHiddenQuantRegistrosConfCliente(int hiddenQuantRegistrosConfCliente) {
        this.hiddenQuantRegistrosConfCliente = hiddenQuantRegistrosConfCliente;
    }

    public void setJuroParcela(double juroParcela) {
        this.juroParcela = juroParcela;
    }

    public void setListaConfiguracaoCamposCliente(List listaConfiguracaoCamposCliente) {
        this.listaConfiguracaoCamposCliente = listaConfiguracaoCamposCliente;
    }

    public void setMascaraMatricula(String mascaraMatricula) {
        this.mascaraMatricula = mascaraMatricula;
    }

    public void setMatriculaApresentar(boolean matriculaApresentar) {
        this.matriculaApresentar = matriculaApresentar;
    }

    public void setMatriculaOb(boolean matriculaOb) {
        this.matriculaOb = matriculaOb;
    }

    public void setMatriculaPendente(boolean matriculaPendente) {
        this.matriculaPendente = matriculaPendente;
    }

    public void setMulta(double multa) {
        this.multa = multa;
    }

    public void setNomeApresentar(boolean nomeApresentar) {
        this.nomeApresentar = nomeApresentar;
    }

    public void setNomeMaeApresentar(boolean nomeMaeApresentar) {
        this.nomeMaeApresentar = nomeMaeApresentar;
    }

    public void setNomeMaeOb(boolean nomeMaeOb) {
        this.nomeMaeOb = nomeMaeOb;
    }

    public void setNomeMaePendente(boolean nomeMaePendente) {
        this.nomeMaePendente = nomeMaePendente;
    }

    public void setNomeOb(boolean nomeOb) {
        this.nomeOb = nomeOb;
    }

    public void setNomePaiApresentar(boolean nomePaiApresentar) {
        this.nomePaiApresentar = nomePaiApresentar;
    }

    public void setNomePaiOb(boolean nomePaiOb) {
        this.nomePaiOb = nomePaiOb;
    }

    public void setNomePaiPendente(boolean nomePaiPendente) {
        this.nomePaiPendente = nomePaiPendente;
    }

    public void setNomePendente(boolean nomePendente) {
        this.nomePendente = nomePendente;
    }

    public void setNrDiasAvencer(int nrDiasAvencer) {
        this.nrDiasAvencer = nrDiasAvencer;
    }

    public void setNrDiasVigenteQuestionarioRematricula(int nrDiasVigenteQuestionarioRematricula) {
        this.nrDiasVigenteQuestionarioRematricula = nrDiasVigenteQuestionarioRematricula;
    }

    public void setNrDiasVigenteQuestionarioRetorno(int nrDiasVigenteQuestionarioRetorno) {
        this.nrDiasVigenteQuestionarioRetorno = nrDiasVigenteQuestionarioRetorno;
    }

    public void setNrDiasVigenteQuestionarioVista(int nrDiasVigenteQuestionarioVista) {
        this.nrDiasVigenteQuestionarioVista = nrDiasVigenteQuestionarioVista;
    }

    public void setNumeroApresentar(boolean numeroApresentar) {
        this.numeroApresentar = numeroApresentar;
    }

    public void setNumeroOb(boolean numeroOb) {
        this.numeroOb = numeroOb;
    }

    public void setNumeroPendente(boolean numeroPendente) {
        this.numeroPendente = numeroPendente;
    }

    public void setPaisApresentar(boolean paisApresentar) {
        this.paisApresentar = paisApresentar;
    }

    public void setPaisOb(boolean paisOb) {
        this.paisOb = paisOb;
    }

    public void setPaisPendente(boolean paisPendente) {
        this.paisPendente = paisPendente;
    }

    public void setProfissaoApresentar(boolean profissaoApresentar) {
        this.profissaoApresentar = profissaoApresentar;
    }

    public void setProfissaoOb(boolean profissaoOb) {
        this.profissaoOb = profissaoOb;
    }

    public void setProfissaoPendente(boolean profissaoPendente) {
        this.profissaoPendente = profissaoPendente;
    }

    public void setQtdFaltaInicioPeso2(int qtdFaltaInicioPeso2) {
        this.qtdFaltaInicioPeso2 = qtdFaltaInicioPeso2;
    }

    public void setQtdFaltaPeso1(int qtdFaltaPeso1) {
        this.qtdFaltaPeso1 = qtdFaltaPeso1;
    }

    public void setQtdFaltaPeso3(int qtdFaltaPeso3) {
        this.qtdFaltaPeso3 = qtdFaltaPeso3;
    }

    public void setQtdFaltaTerminoPeso2(int qtdFaltaTerminoPeso2) {
        this.qtdFaltaTerminoPeso2 = qtdFaltaTerminoPeso2;
    }

    public void setRgApresentar(boolean rgApresentar) {
        this.rgApresentar = rgApresentar;
    }

    public void setRgOb(boolean rgOb) {
        this.rgOb = rgOb;
    }

    public void setRgPendente(boolean rgPendente) {
        this.rgPendente = rgPendente;
    }

    public void setSexoApresentar(boolean sexoApresentar) {
        this.sexoApresentar = sexoApresentar;
    }

    public void setSexoOb(boolean sexoOb) {
        this.sexoOb = sexoOb;
    }

    public void setSexoPendente(boolean sexoPendente) {
        this.sexoPendente = sexoPendente;
    }

    public void setTelefoneApresentar(boolean telefoneApresentar) {
        this.telefoneApresentar = telefoneApresentar;
    }

    public void setTelefoneOb(boolean telefoneOb) {
        this.telefoneOb = telefoneOb;
    }

    public void setTelefonePendente(boolean telefonePendente) {
        this.telefonePendente = telefonePendente;
    }

    public void setToleranciaPagamento(int toleranciaPagamento) {
        this.toleranciaPagamento = toleranciaPagamento;
    }

    public void setWebPageApresentar(boolean webPageApresentar) {
        this.webPageApresentar = webPageApresentar;
    }

    public void setWebPageOb(boolean webPageOb) {
        this.webPageOb = webPageOb;
    }

    public void setWebPagePendente(boolean webPagePendente) {
        this.webPagePendente = webPagePendente;
    }

    public int getNrDiasProrata() {
        return nrDiasProrata;
    }

    public void setNrDiasProrata(int nrDiasProrata) {
        this.nrDiasProrata = nrDiasProrata;
    }

    public void setUrlGoogleAgenda(String urlGoogleAgenda) {
        this.urlGoogleAgenda = urlGoogleAgenda;
    }

    public String getUrlGoogleAgenda() {
        return urlGoogleAgenda;
    }

    public int getVencimentoColaborador() {
        return vencimentoColaborador;
    }

    public void setVencimentoColaborador(int vencimentoColaborador) {
        this.vencimentoColaborador = vencimentoColaborador;
    }

    public boolean isRodarSqlsBancoInicial() {
        return rodarSqlsBancoInicial;
    }

    public void setRodarSqlsBancoInicial(boolean rodarSqlsBancoInicial) {
        this.rodarSqlsBancoInicial = rodarSqlsBancoInicial;
    }

    public double getAliquotaServico() {
        return aliquotaServico;
    }

    public void setAliquotaServico(double aliquotaServico) {
        this.aliquotaServico = aliquotaServico;
    }

    public double getAliquotaProduto() {
        return aliquotaProduto;
    }

    public void setAliquotaProduto(double aliquotaProduto) {
        this.aliquotaProduto = aliquotaProduto;
    }

    public boolean isUsaEcf() {
        return usaEcf;
    }

    public void setUsaEcf(boolean usaEcf) {
        this.usaEcf = usaEcf;
    }

    public boolean isEcfPorPagamento() {
        return ecfPorPagamento;
    }

    public void setEcfPorPagamento(boolean ecfPorPagamento) {
        this.ecfPorPagamento = ecfPorPagamento;
    }

    public boolean isEcfApenasPlano() {
        return ecfApenasPlano;
    }

    public void setEcfApenasPlano(boolean ecfApenasPlano) {
        this.ecfApenasPlano = ecfApenasPlano;
    }

    public boolean isAlteracaoDataBaseContrato() {
        return alteracaoDataBaseContrato;
    }

    public void setAlteracaoDataBaseContrato(boolean alteracaoDataBaseContrato) {
        this.alteracaoDataBaseContrato = alteracaoDataBaseContrato;
    }

    public int getToleranciaDiasContratoVencido() {
        return toleranciaDiasContratoVencido;
    }

    public void setToleranciaDiasContratoVencido(int toleranciaDiasContratoVencido) {
        this.toleranciaDiasContratoVencido = toleranciaDiasContratoVencido;
    }

    public void setListaEmailsRecorrencia(List<String> listaEmailsRecorrencia) {
        this.listaEmailsRecorrencia = listaEmailsRecorrencia;
    }

    public List<String> getListaEmailsRecorrencia() {
        if (listaEmailsRecorrencia == null) {
            listaEmailsRecorrencia = new ArrayList<>();
        }
        return listaEmailsRecorrencia;
    }

    public void setUrlRecorrencia(String urlRecorrencia) {
        this.urlRecorrencia = urlRecorrencia;
    }

    public String getUrlRecorrencia() {
        if (urlRecorrencia == null) {
            urlRecorrencia = "";
        }
        return urlRecorrencia;
    }

    public int getQtdDiasExpirarSenha() {
        return qtdDiasExpirarSenha;
    }

    public void setQtdDiasExpirarSenha(int qtdDiasExpirarSenha) {
        this.qtdDiasExpirarSenha = qtdDiasExpirarSenha;
    }

    public int getQtdDiasEstornoAutomaticoContrato() {
        return qtdDiasEstornoAutomaticoContrato;
    }

    public void setQtdDiasEstornoAutomaticoContrato(int qtdDiasEstornoAutomaticoContrato) {
        this.qtdDiasEstornoAutomaticoContrato = qtdDiasEstornoAutomaticoContrato;
    }

    public boolean isAcessoChamada() {
        return acessoChamada;
    }

    public void setAcessoChamada(boolean acessoChamada) {
        this.acessoChamada = acessoChamada;
    }

    public LocalAcessoVO getLocalAcessoChamada() {
        return localAcessoChamada;
    }

    public void setLocalAcessoChamada(LocalAcessoVO localAcessoChamada) {
        this.localAcessoChamada = localAcessoChamada;
    }

    public ColetorVO getColetorChamada() {
        return coletorChamada;
    }

    public void setColetorChamada(ColetorVO coletorChamada) {
        this.coletorChamada = coletorChamada;
    }

    public Date getDataUltimaRepescagem() {
        return dataUltimaRepescagem;
    }

    public void setDataUltimaRepescagem(Date dataUltimaRepescagem) {
        this.dataUltimaRepescagem = dataUltimaRepescagem;
    }

    public String getEmailsFechamentoAcessos() {
        return emailsFechamentoAcessos;
    }

    public void setEmailsFechamentoAcessos(String emailsFechamentoAcessos) {
        this.emailsFechamentoAcessos = emailsFechamentoAcessos;
    }

    /**
     * Como a URL Recorr?ncia pode ser definida por empresa e pela Configura??o Global do Sistema,
     * este método escolhe entre a URL da empresa espec?fica ou a URL da Configura??o o Sistema
     *
     * @param empresa empresa
     * @param configSistema configSistema
     * @return urlEscolhida
     */
    public static String obterURLRecorrencia(EmpresaVO empresa, ConfiguracaoSistemaVO configSistema) {
        String urlEscolhida = "";
        if (empresa != null && empresa.getUrlRecorrencia() != null && !empresa.getUrlRecorrencia().isEmpty()) {
            urlEscolhida = empresa.getUrlRecorrencia();
        } else if (configSistema != null && !configSistema.getUrlRecorrencia().isEmpty()) {
            urlEscolhida = configSistema.getUrlRecorrencia();
        }
        return urlEscolhida;

    }

    public boolean isBloquearAcessoSeParcelaAberta() {
        return bloquearAcessoSeParcelaAberta;
    }

    public void setBloquearAcessoSeParcelaAberta(boolean bloquearAcessoSeParcelaAberta) {
        this.bloquearAcessoSeParcelaAberta = bloquearAcessoSeParcelaAberta;
    }

    public boolean isEnviarSMSAutomatico() {
        return enviarSMSAutomatico;
    }

    public void setEnviarSMSAutomatico(boolean enviarSMSAutomatico) {
        this.enviarSMSAutomatico = enviarSMSAutomatico;
    }

    public void setNomeDataNascValidar(boolean nomeDataNascValidar) {
        this.nomeDataNascValidar = nomeDataNascValidar;
    }

    public boolean isNomeDataNascValidar() {
        return nomeDataNascValidar;
    }

    public boolean isEnviarRemessasRemotamente() {
        return enviarRemessasRemotamente;
    }

    public void setEnviarRemessasRemotamente(boolean enviarRemessasRemotamente) {
        this.enviarRemessasRemotamente = enviarRemessasRemotamente;
    }

    public void setValidarContatoMeta(boolean validarContatoMeta) {
        this.validarContatoMeta = validarContatoMeta;
    }

    public boolean isValidarContatoMeta() {
        return validarContatoMeta;
    }

    public boolean isForcarCodigoAlternativoAcesso() {
        return forcarCodigoAlternativoAcesso;
    }

    public void setForcarCodigoAlternativoAcesso(boolean forcarCodigoAlternativoAcesso) {
        this.forcarCodigoAlternativoAcesso = forcarCodigoAlternativoAcesso;
    }

    public void setItemVendaAvulsaAutomatico(boolean itemVendaAvulsaAutomatico) {
        this.itemVendaAvulsaAutomatico = itemVendaAvulsaAutomatico;
    }

    public boolean isItemVendaAvulsaAutomatico() {
        return itemVendaAvulsaAutomatico;
    }

    public int getNrDiasVigenteQuestionarioPrimeiraCompra() {
        return nrDiasVigenteQuestionarioPrimeiraCompra;
    }

    public void setNrDiasVigenteQuestionarioPrimeiraCompra(int nrDiasVigenteQuestionarioPrimeiraCompra) {
        this.nrDiasVigenteQuestionarioPrimeiraCompra = nrDiasVigenteQuestionarioPrimeiraCompra;
    }

    public QuestionarioVO getQuestionarioPrimeiraCompra() {
        if (questionarioPrimeiraCompra == null) {
            questionarioPrimeiraCompra = new QuestionarioVO();
        }
        return (questionarioPrimeiraCompra);
    }

    public void setQuestionarioPrimeiraCompra(QuestionarioVO obj) {
        this.questionarioPrimeiraCompra = obj;
    }

    public QuestionarioVO getQuestionarioRetornoCompra() {
        if (questionarioRetornoCompra == null) {
            questionarioRetornoCompra = new QuestionarioVO();
        }
        return (questionarioRetornoCompra);
    }

    public void setQuestionarioRetornoCompra(QuestionarioVO questionarioRetornoCompra) {
        this.questionarioRetornoCompra = questionarioRetornoCompra;
    }

    public int getNrDiasVigenteQuestionarioRetornoCompra() {
        return nrDiasVigenteQuestionarioRetornoCompra;
    }

    public void setNrDiasVigenteQuestionarioRetornoCompra(int nrDiasVigenteQuestionarioRetornoCompra) {
        this.nrDiasVigenteQuestionarioRetornoCompra = nrDiasVigenteQuestionarioRetornoCompra;
    }

    public String getNumeroCielo() {
        return numeroCielo;
    }

    public void setNumeroCielo(String numeroCielo) {
        this.numeroCielo = numeroCielo;
    }

    public List getListaConfiguracaoCamposVisitante() {
        return listaConfiguracaoCamposVisitante;
    }

    public void setListaConfiguracaoCamposVisitante(List listaConfiguracaoCamposVisitante) {
        this.listaConfiguracaoCamposVisitante = listaConfiguracaoCamposVisitante;
    }

    public int getHiddenQuantRegistrosConfVisitante() {
        return hiddenQuantRegistrosConfVisitante;
    }

    public void setHiddenQuantRegistrosConfVisitante(int hiddenQuantRegistrosConfVisitante) {
        this.hiddenQuantRegistrosConfVisitante = hiddenQuantRegistrosConfVisitante;
    }

    public void inicializarConfiguracaoSistemaVisitante() throws Exception {
        inicializarConfiguracaoSistema(true, listaConfiguracaoCamposVisitante);
    }

    public void inicializarConfiguracaoSistemaCliente() throws Exception {
        inicializarConfiguracaoSistema(false, listaConfiguracaoCamposCliente);
    }

    public void inicializarConfiguracaoSistema(boolean visitante, List<ConfiguracaoSistemaCadastroClienteVO> lista) throws Exception {
        if (UteisValidacao.emptyList(lista)) {
            lista = getFacade().getConfiguracaoSistemaCadastroCliente().consultar(visitante);
        }
        setarDadosConfiguracaoCadastroCliente(lista);
    }

    public void setarDadosConfiguracaoCadastroCliente(List lista) {
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            ConfiguracaoSistemaCadastroClienteVO configuracaoCliente = (ConfiguracaoSistemaCadastroClienteVO) i.next();

            switch (configuracaoCliente.getNome()) {
                case "Nome":
                    setNomeApresentar(configuracaoCliente.getMostrar());
                    setNomeOb(configuracaoCliente.getObrigatorio());
                    setNomePendente(configuracaoCliente.getPendente());
                    break;
                case "Endereço":
                    setEnderecoApresentar(configuracaoCliente.getMostrar());
                    setEnderecoOb(configuracaoCliente.getObrigatorio());
                    setEnderecoPendente(configuracaoCliente.getPendente());
                    break;
                case "Endereço Complemento":
                    setEnderecoComplementoApresentar(configuracaoCliente.getMostrar());
                    setEnderecoComplementoOb(configuracaoCliente.getObrigatorio());
                    setEnderecoComplementoPendente(configuracaoCliente.getPendente());
                    break;
                case "Email":
                    setEmailApresentar(configuracaoCliente.getMostrar());
                    setEmailOb(configuracaoCliente.getObrigatorio());
                    setEmailPendente(configuracaoCliente.getPendente());
                    break;
                case "Cidade":
                    setCidadeApresentar(configuracaoCliente.getMostrar());
                    setCidadeOb(configuracaoCliente.getObrigatorio());
                    setCidadePendente(configuracaoCliente.getPendente());
                    break;
                case "Telefone":
                    setTelefoneApresentar(configuracaoCliente.getMostrar());
                    setTelefoneOb(configuracaoCliente.getObrigatorio());
                    setTelefonePendente(configuracaoCliente.getPendente());
                    break;
                case "Sexo":
                    setSexoApresentar(configuracaoCliente.getMostrar());
                    setSexoOb(configuracaoCliente.getObrigatorio());
                    setSexoPendente(configuracaoCliente.getPendente());
                    break;
                case "Gênero":
                    setGeneroApresentar(configuracaoCliente.getMostrar());
                    setGeneroOb(configuracaoCliente.getObrigatorio());
                    setGeneroPendente(configuracaoCliente.getPendente());
                    break;
                case "Nome Registro":
                    setNomeRegistroApresentar(configuracaoCliente.getMostrar());
                    setNomeRegistroOb(configuracaoCliente.getObrigatorio());
                    setNomeRegistroPendente(configuracaoCliente.getPendente());
                    break;
                case "Estado Civil":
                    setEstadoCivilApresentar(configuracaoCliente.getMostrar());
                    setEstadoCivilOb(configuracaoCliente.getObrigatorio());
                    setEstadoCivilPendente(configuracaoCliente.getPendente());
                    break;
                case "Data Nascimento":
                    setDataNascApresentar(configuracaoCliente.getMostrar());
                    setDataNascOb(configuracaoCliente.getObrigatorio());
                    setDataNascPendente(configuracaoCliente.getPendente());
                    break;
                case "Categoria":
                    setCategoriaApresentar(configuracaoCliente.getMostrar());
                    setCategoriaOb(configuracaoCliente.getObrigatorio());
                    setCategoriaPendente(configuracaoCliente.getPendente());
                    break;
                case "CPF":
                    setCpfApresentar(configuracaoCliente.getMostrar());
                    setCfpOb(configuracaoCliente.getObrigatorio());
                    setCpfPendente(configuracaoCliente.getPendente());
                    break;
                case "CNPJ Sesi Indústria":
                    setApresentarCnpjSesi(configuracaoCliente.getMostrar());
                    setObjCnpjSesi(configuracaoCliente.getObrigatorio());
                    setPendenteCnpjSesi(configuracaoCliente.getPendente());
                    break;
                case "Matrícula":
                    setMatriculaApresentar(configuracaoCliente.getMostrar());
                    setMatriculaOb(configuracaoCliente.getObrigatorio());
                    setMatriculaPendente(configuracaoCliente.getPendente());
                    break;
                case "Estado":
                    setEstadoApresentar(configuracaoCliente.getMostrar());
                    setEstadoOb(configuracaoCliente.getObrigatorio());
                    setEstadoPendente(configuracaoCliente.getPendente());
                    break;
                case "País":
                    setPaisApresentar(configuracaoCliente.getMostrar());
                    setPaisOb(configuracaoCliente.getObrigatorio());
                    setPaisPendente(configuracaoCliente.getPendente());
                    break;
                case "Profissão":
                    setProfissaoApresentar(configuracaoCliente.getMostrar());
                    setProfissaoOb(configuracaoCliente.getObrigatorio());
                    setProfissaoPendente(configuracaoCliente.getPendente());
                    break;
                case "RG":
                    setRgApresentar(configuracaoCliente.getMostrar());
                    setRgOb(configuracaoCliente.getObrigatorio());
                    setRgPendente(configuracaoCliente.getPendente());
                    break;
                case "Nome Pai ou Responsável":
                    setNomePaiApresentar(configuracaoCliente.getMostrar());
                    setNomePaiOb(configuracaoCliente.getObrigatorio());
                    setNomePaiPendente(configuracaoCliente.getPendente());
                    break;
                case "Nome Mãe ou Responsável":
                    setNomeMaeApresentar(configuracaoCliente.getMostrar());
                    setNomeMaeOb(configuracaoCliente.getObrigatorio());
                    setNomeMaePendente(configuracaoCliente.getPendente());
                    break;
                case "Web Page":
                    setWebPageApresentar(configuracaoCliente.getMostrar());
                    setWebPageOb(configuracaoCliente.getObrigatorio());
                    setWebPagePendente(configuracaoCliente.getPendente());
                    break;
                case "Grau Instrução":
                case "Grau de Instrução":
                    setGrauInstrucaoApresentar(configuracaoCliente.getMostrar());
                    setGrauInstrucaoOb(configuracaoCliente.getObrigatorio());
                    setGrauInstrucaoPendente(configuracaoCliente.getPendente());
                    break;
                case "Número":
                    setNumeroApresentar(configuracaoCliente.getMostrar());
                    setNumeroOb(configuracaoCliente.getObrigatorio());
                    setNumeroPendente(configuracaoCliente.getPendente());
                    break;
                case "Bairro":
                    setBairroApresentar(configuracaoCliente.getMostrar());
                    setBairroOb(configuracaoCliente.getObrigatorio());
                    setBairroPendente(configuracaoCliente.getPendente());
                    break;
                case "Cep":
                    setCepApresentar(configuracaoCliente.getMostrar());
                    setCepOb(configuracaoCliente.getObrigatorio());
                    setCepPendente(configuracaoCliente.getPendente());
                    break;
                case "Contato Emergência":
                    setContatoEmergenciaApresentar(configuracaoCliente.getMostrar());
                    setContatoEmergenciaOb(configuracaoCliente.getObrigatorio());
                    setContatoEmergenciaPendente(configuracaoCliente.getPendente());
                    break;
                case "Telefone Emergência":
                    setTelefoneEmergenciaApresentar(configuracaoCliente.getMostrar());
                    setTelefoneEmergenciaOb(configuracaoCliente.getObrigatorio());
                    setTelefoneEmergenciaPendente(configuracaoCliente.getPendente());
                    break;
                case "CPF Pai":
                    setCpfPaiApresentar(configuracaoCliente.getMostrar());
                    setCpfPaiOb(configuracaoCliente.getObrigatorio());
                    setCpfPaiPendente(configuracaoCliente.getPendente());
                    break;
                case "CPF Mãe":
                    setCpfMaeApresentar(configuracaoCliente.getMostrar());
                    setCpfMaeOb(configuracaoCliente.getObrigatorio());
                    setCpfMaePendente(configuracaoCliente.getPendente());
                    break;
                case "Descrição Telefone":
                    setDescricaoTefObApresentar(configuracaoCliente.getMostrar());
                    setDescricaoTefOb(configuracaoCliente.getObrigatorio());
                    setDescricaoTefObPendente(configuracaoCliente.getPendente());
                    break;
                case "Nome Resp. Financeiro":
                    setNomeRespFinanceiroApresentar(configuracaoCliente.getMostrar());
                    setNomeRespFinanceiroOb(configuracaoCliente.getObrigatorio());
                    setNomeRespFinanceiroPendente(configuracaoCliente.getPendente());
                case "CPF Resp. Financeiro":
                    setCpfRespFinanceiroApresentar(configuracaoCliente.getMostrar());
                    setCpfRespFinanceiroOb(configuracaoCliente.getObrigatorio());
                    setCpfRespFinanceiroPendente(configuracaoCliente.getPendente());
                case "RG Resp. Financeiro":
                    setRgRespFinanceiroApresentar(configuracaoCliente.getMostrar());
                    setRgRespFinanceiroOb(configuracaoCliente.getObrigatorio());
                    setRgRespFinanceiroPendente(configuracaoCliente.getPendente());
                case "E-mail Resp. Financeiro":
                    setEmailRespFinanceiroApresentar(configuracaoCliente.getMostrar());
                    setEmailRespFinanceiroOb(configuracaoCliente.getObrigatorio());
                    setEmailRespFinanceiroPendente(configuracaoCliente.getPendente());
            }
        }

    }

    public boolean isValidarCpfDuplicado() {
        return validarCpfDuplicado;
    }

    public void setValidarCpfDuplicado(boolean validarCpfDuplicado) {
        this.validarCpfDuplicado = validarCpfDuplicado;
    }


    public boolean isMarcarPresencaPeloAcesso() {
        return marcarPresencaPeloAcesso;
    }

    public void setMarcarPresencaPeloAcesso(boolean marcarPresencaPeloAcesso) {
        this.marcarPresencaPeloAcesso = marcarPresencaPeloAcesso;
    }

    public boolean isUsarNomeResponsavelNota() {
        return usarNomeResponsavelNota;
    }

    public void setUsarNomeResponsavelNota(boolean usarNomeResponsavelNota) {
        this.usarNomeResponsavelNota = usarNomeResponsavelNota;
    }

    public boolean isUtilizarSistemaParaClube() {
        return utilizarSistemaParaClube;
    }

    public void setUtilizarSistemaParaClube(boolean utilizarSistemaParaClube) {
        this.utilizarSistemaParaClube = utilizarSistemaParaClube;
    }

    public boolean isImprimirReciboPagtoMatricial() {
        return imprimirReciboPagtoMatricial;
    }

    public void setImprimirReciboPagtoMatricial(boolean imprimirReciboPagtoMatricial) {
        this.imprimirReciboPagtoMatricial = imprimirReciboPagtoMatricial;
    }

    public boolean getDescricaoTefOb() {
        return descricaoTefOb;
    }

    public void setDescricaoTefOb(boolean descricaoTefOb) {
        this.descricaoTefOb = descricaoTefOb;
    }

    public boolean getDescricaoTefObApresentar() {
        return descricaoTefObApresentar;
    }

    public void setDescricaoTefObApresentar(boolean descricaoTefObApresentar) {
        this.descricaoTefObApresentar = descricaoTefObApresentar;
    }

    public boolean getDescricaoTefObPendente() {
        return descricaoTefObPendente;
    }

    public void setDescricaoTefObPendente(boolean descricaoTefObPendente) {
        this.descricaoTefObPendente = descricaoTefObPendente;
    }

    public List<ConfiguracaoSistemaCadastroClienteVO> getListaConfiguracaoCamposClienteOriginal() {
        return listaConfiguracaoCamposClienteOriginal;
    }

    public void setListaConfiguracaoCamposClienteOriginal(List<ConfiguracaoSistemaCadastroClienteVO> listaConfiguracaoCamposClienteOriginal) {
        this.listaConfiguracaoCamposClienteOriginal = listaConfiguracaoCamposClienteOriginal;
    }

    public List<ConfiguracaoSistemaCadastroClienteVO> getListaConfiguracaoCamposVisitanteOriginal() {
        return listaConfiguracaoCamposVisitanteOriginal;
    }

    public void setListaConfiguracaoCamposVisitanteOriginal(List<ConfiguracaoSistemaCadastroClienteVO> listaConfiguracaoCamposVisitanteOriginal) {
        this.listaConfiguracaoCamposVisitanteOriginal = listaConfiguracaoCamposVisitanteOriginal;
    }

    public int getQtdDiaPrimeiraParcelaVencidaEstornarContrato() {
        return qtdDiaPrimeiraParcelaVencidaEstornarContrato;
    }

    public void setQtdDiaPrimeiraParcelaVencidaEstornarContrato(int qtdDiaPrimeiraParcelaVencidaEstornarContrato) {
        this.qtdDiaPrimeiraParcelaVencidaEstornarContrato = qtdDiaPrimeiraParcelaVencidaEstornarContrato;
    }

    public Boolean getDefaultEnderecoCorrespondecia() {
        return defaultEnderecoCorrespondecia;
    }

    public void setDefaultEnderecoCorrespondecia(Boolean defaultEnderecoCorrespondecia) {
        this.defaultEnderecoCorrespondecia = defaultEnderecoCorrespondecia;
    }

    public Boolean getHabilitarGestaoArmarios() {
        try {
            return habilitarGestaoArmarios;
        }catch (Exception err){
            return null;
        }
    }

    public void setHabilitarGestaoArmarios(Boolean habilitarGestaoArmarios) {
        this.habilitarGestaoArmarios = habilitarGestaoArmarios;
    }

    public int getDiaProrataArmario() {
        return diaProrataArmario;
    }

    public void setDiaProrataArmario(int diaProrataArmario) {
        this.diaProrataArmario = diaProrataArmario;
    }

    public List<SelectItem> getOpcoesNomenclaturaVendaCredito() {
        return OpcoesNomenclaturaVendaCreditoEnum.getSelectList();
    }
    public String getNomenclaturaVendaCredito() {
        return nomenclaturaVendaCredito;
    }

    public void setNomenclaturaVendaCredito(String nomenclaturaVendaCredito) {
        this.nomenclaturaVendaCredito = nomenclaturaVendaCredito;
    }

    public Integer getSequencialItem() {
        if (sequencialItem == null) {
            sequencialItem = 1;
        }
        return sequencialItem;
    }

    public void setSequencialItem(Integer sequencialItem) {
        this.sequencialItem = sequencialItem;
    }

    public Integer getSequencialArquivo() {
        if (sequencialArquivo == null) {
            sequencialArquivo = 1;
        }
        return sequencialArquivo;
    }

    public void setSequencialArquivo(Integer sequencialArquivo) {
        this.sequencialArquivo = sequencialArquivo;
    }

    public Boolean getSesc() {
        if (sesc == null) {
            sesc = false;
        }
        return sesc;
    }

    public void setSesc(Boolean sesc) {
        this.sesc = sesc;
    }

    public Boolean getSesiCe() {
        return sesiCe;
    }

    public void setSesiCe(Boolean sesiCe) {
        this.sesiCe = sesiCe;
    }

    public Boolean getApiSescGo() {
        return apiSescGo;
    }

    public void setApiSescGo(Boolean apiSescGo) {
        this.apiSescGo = apiSescGo;
    }

    public boolean isControleAcessoMultiplasEmpresasPorPlano() {
        return controleAcessoMultiplasEmpresasPorPlano;
    }

    public void setControleAcessoMultiplasEmpresasPorPlano(boolean controleAcessoMultiplasEmpresasPorPlano) {
        this.controleAcessoMultiplasEmpresasPorPlano = controleAcessoMultiplasEmpresasPorPlano;
    }
    public Boolean getPriorizarVendaRapida() {
        return priorizarVendaRapida;
    }

    public void setPriorizarVendaRapida(Boolean priorizarVendaRapida) {
        this.priorizarVendaRapida = priorizarVendaRapida;
    }

    public Boolean getBarrarDevedorVendaRapida() {
        return barrarDevedorVendaRapida;
    }

    public void setBarrarDevedorVendaRapida(Boolean barrarDevedorVendaRapida) {
        this.barrarDevedorVendaRapida = barrarDevedorVendaRapida;
    }

    public Boolean getLancamentoContratosIguais() {
        return lancamentoContratosIguais;
    }

    public void setLancamentoContratosIguais(Boolean lancamentoContratosIguais) {
        this.lancamentoContratosIguais = lancamentoContratosIguais;
    }

    public boolean isUsaAprovaFacil() {
        return usaAprovaFacil;
    }

    public void setUsaAprovaFacil(boolean usaAprovaFacil) {
        this.usaAprovaFacil = usaAprovaFacil;
    }

    public boolean isCancelarContratoNaUnidadeOrigemAoTransferirAluno() {
        return cancelarContratoNaUnidadeOrigemAoTransferirAluno;
    }

    public void setCancelarContratoNaUnidadeOrigemAoTransferirAluno(boolean cancelarContratoNaUnidadeOrigemAoTransferirAluno) {
        this.cancelarContratoNaUnidadeOrigemAoTransferirAluno = cancelarContratoNaUnidadeOrigemAoTransferirAluno;
    }

    public boolean isUsarDigitalComoAssinatura() {
        return usarDigitalComoAssinatura;
    }

    public void setUsarDigitalComoAssinatura(boolean usarDigitalComoAssinatura) {
        this.usarDigitalComoAssinatura = usarDigitalComoAssinatura;
    }

    public boolean isPropagaraAssinaturaDigital() {
        return propagaraAssinaturaDigital;
    }

    public void setPropagaraAssinaturaDigital(boolean propagaraAssinaturaDigital) {
        this.propagaraAssinaturaDigital = propagaraAssinaturaDigital;
    }

    public boolean isTermoResponsabilidade() {
        return termoResponsabilidade;
    }

    public void setTermoResponsabilidade(boolean termoResponsabilidade) {
        this.termoResponsabilidade = termoResponsabilidade;
    }

    public Boolean getUtilizarTipoPlano(){
        if(utilizarTipoPlano == null)
            return false;

        return utilizarTipoPlano;
    }

    public Boolean isUtilizarTipoPlano() {
        return getUtilizarTipoPlano();
    }

    public void setUtilizarTipoPlano(Boolean utilizarTipoPlano) {
        this.utilizarTipoPlano = utilizarTipoPlano;
    }

    public boolean isTransferirAutorizacaoCobranca() {
        return transferirAutorizacaoCobranca;
    }

    public void setTransferirAutorizacaoCobranca(boolean transferirAutorizacaoCobranca) {
        this.transferirAutorizacaoCobranca = transferirAutorizacaoCobranca;
    }
    public void setTransferirAutorizacaoCobranca() {
        this.transferirAutorizacaoCobranca = false;
    }

    public boolean isUsarSistemaInternacional() {
        return usarSistemaInternacional;
    }

    public void setUsarSistemaInternacional(boolean usarSistemaInternacional) {
        this.usarSistemaInternacional = usarSistemaInternacional;
    }

    public boolean isCpfPaiOb() {
        return cpfPaiOb;
    }

    public void setCpfPaiOb(boolean cpfPaiOb) {
        this.cpfPaiOb = cpfPaiOb;
    }

    public boolean isCpfPaiApresentar() {
        return cpfPaiApresentar;
    }

    public void setCpfPaiApresentar(boolean cpfPaiApresentar) {
        this.cpfPaiApresentar = cpfPaiApresentar;
    }

    public boolean isCpfPaiPendente() {
        return cpfPaiPendente;
    }

    public void setCpfPaiPendente(boolean cpfPaiPendente) {
        this.cpfPaiPendente = cpfPaiPendente;
    }

    public boolean isCpfMaeOb() {
        return cpfMaeOb;
    }

    public void setCpfMaeOb(boolean cpfMaeOb) {
        this.cpfMaeOb = cpfMaeOb;
    }

    public boolean isCpfMaeApresentar() {
        return cpfMaeApresentar;
    }

    public void setCpfMaeApresentar(boolean cpfMaeApresentar) {
        this.cpfMaeApresentar = cpfMaeApresentar;
    }

    public boolean isCpfMaePendente() {
        return cpfMaePendente;
    }

    public void setCpfMaePendente(boolean cpfMaePendente) {
        this.cpfMaePendente = cpfMaePendente;
    }

    public boolean isValidarCPFResponsaveis() {
        return validarCPFResponsaveis;
    }

    public void setValidarCPFResponsaveis(boolean validarCPFResponsaveis) {
        this.validarCPFResponsaveis = validarCPFResponsaveis;
    }

    public boolean isNomeArquivoRemessaPadraoTivit() {
        return nomeArquivoRemessaPadraoTivit;
    }

    public void setNomeArquivoRemessaPadraoTivit(boolean nomeArquivoRemessaPadraoTivit) {
        this.nomeArquivoRemessaPadraoTivit = nomeArquivoRemessaPadraoTivit;
    }

    public boolean isDefinirDataInicioPlanosRecorrencia() {
        return definirDataInicioPlanosRecorrencia;
    }

    public void setDefinirDataInicioPlanosRecorrencia(boolean definirDataInicioPlanosRecorrencia) {
        this.definirDataInicioPlanosRecorrencia = definirDataInicioPlanosRecorrencia;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConfiguracaoSistemaVO that = (ConfiguracaoSistemaVO) o;
        return cancelarContratoNaUnidadeOrigemAoTransferirAluno == that.cancelarContratoNaUnidadeOrigemAoTransferirAluno;
    }

    @Override
    public int hashCode() {
        return Objects.hash(cancelarContratoNaUnidadeOrigemAoTransferirAluno);
    }

    public boolean isExibirModalInativarUsuarios() {
        return exibirModalInativarUsuarios;
    }

    public void setExibirModalInativarUsuarios(boolean exibirModalInativarUsuarios) {
        this.exibirModalInativarUsuarios = exibirModalInativarUsuarios;
    }

    public boolean isExibirModalPlanosInativos() {
        return exibirModalPlanosInativos;
    }

    public void setExibirModalPlanosInativos(boolean exibirModalPlanosInativos) {
        this.exibirModalPlanosInativos = exibirModalPlanosInativos;
    }


    public boolean isUsarVerificadorRemessasRejeitadas() {
        return usarVerificadorRemessasRejeitadas;
    }

    public void setUsarVerificadorRemessasRejeitadas(boolean usarVerificadorRemessasRejeitadas) {
        this.usarVerificadorRemessasRejeitadas = usarVerificadorRemessasRejeitadas;
    }

    public boolean isAgruparRemessasGetnet() {
        return agruparRemessasGetnet;
    }

    public void setAgruparRemessasGetnet(boolean agruparRemessasGetnet) {
        this.agruparRemessasGetnet = agruparRemessasGetnet;
    }

    public boolean isRgPaiOb() {
        return rgPaiOb;
    }

    public void setRgPaiOb(boolean rgPaiOb) {
        this.rgPaiOb = rgPaiOb;
    }

    public boolean isRgPaiApresentar() {
        return rgPaiApresentar;
    }

    public void setRgPaiApresentar(boolean rgPaiApresentar) {
        this.rgPaiApresentar = rgPaiApresentar;
    }

    public boolean isRgPaiPendente() {
        return rgPaiPendente;
    }

    public void setRgPaiPendente(boolean rgPaiPendente) {
        this.rgPaiPendente = rgPaiPendente;
    }

    public boolean isRgMaeOb() {
        return rgMaeOb;
    }

    public void setRgMaeOb(boolean rgMaeOb) {
        this.rgMaeOb = rgMaeOb;
    }

    public boolean isRgMaeApresentar() {
        return rgMaeApresentar;
    }

    public void setRgMaeApresentar(boolean rgMaeApresentar) {
        this.rgMaeApresentar = rgMaeApresentar;
    }

    public boolean isRgMaePendente() {
        return rgMaePendente;
    }

    public void setRgMaePendente(boolean rgMaePendente) {
        this.rgMaePendente = rgMaePendente;
    }

    public Date getDataInicioDesconsiderarAcessoRisco() {
        return dataInicioDesconsiderarAcessoRisco;
    }

    public void setDataInicioDesconsiderarAcessoRisco(Date dataInicioDesconsiderarAcessoRisco) {
        this.dataInicioDesconsiderarAcessoRisco = dataInicioDesconsiderarAcessoRisco;
    }

    public Date getDataFimDesconsiderarAcessoRisco() {
        return dataFimDesconsiderarAcessoRisco;
    }

    public void setDataFimDesconsiderarAcessoRisco(Date dataFimDesconsiderarAcessoRisco) {
        this.dataFimDesconsiderarAcessoRisco = dataFimDesconsiderarAcessoRisco;
    }

    public static void validarDadosConfiguracaoCadastrosCliente(ConfiguracaoSistemaVO obj) throws ConsistirException {
        if(!UteisValidacao.emptyList(obj.listaConfiguracaoCamposCliente)) {
            validarDadosConfiguracaoCidadeEstadoPais(obj.listaConfiguracaoCamposCliente, "Campos Cliente");
        }
        if(!UteisValidacao.emptyList(obj.listaConfiguracaoCamposVisitante)) {
            validarDadosConfiguracaoCidadeEstadoPais(obj.listaConfiguracaoCamposVisitante, "Campos Visitante");
        }
    }

    public static void validarDadosConfiguracaoCidadeEstadoPais(List configuracoes, String nomeConfiguracao) throws ConsistirException {
        Optional configuracaoClienteCidade;
        Optional configuracaoClienteEstado;
        Optional configuracaoClientePais;
        if(configuracoes.get(0) instanceof ConfiguracaoSistemaCadastroClienteVO) {
            configuracaoClienteCidade = configuracoes.stream().map(config -> (ConfiguracaoSistemaCadastroClienteVO) config)
                    .filter(config -> "Cidade".equals(((ConfiguracaoSistemaCadastroClienteVO) config).getNome()) && ((ConfiguracaoSistemaCadastroClienteVO) config).getMostrar()).findAny();

            configuracaoClienteEstado = configuracoes.stream().map(config -> (ConfiguracaoSistemaCadastroClienteVO) config)
                    .filter(config -> "Estado".equals(((ConfiguracaoSistemaCadastroClienteVO) config).getNome()) && ((ConfiguracaoSistemaCadastroClienteVO) config).getMostrar()).findAny();

            configuracaoClientePais = configuracoes.stream().map(config -> (ConfiguracaoSistemaCadastroClienteVO) config)
                    .filter(config -> "País".equals(((ConfiguracaoSistemaCadastroClienteVO) config).getNome()) && ((ConfiguracaoSistemaCadastroClienteVO) config).getMostrar()).findAny();
        } else {
            configuracaoClienteCidade = configuracoes.stream().map(config -> (CadastroDinamicoItemVO) config)
                    .filter(config -> "CIDADE".equals(((CadastroDinamicoItemVO) config).getNomeCampo()) && ((CadastroDinamicoItemVO) config).isMostrarCampo()).findAny();

            configuracaoClienteEstado = configuracoes.stream().map(config -> (CadastroDinamicoItemVO) config)
                    .filter(config -> "ESTADO".equals(((CadastroDinamicoItemVO) config).getNomeCampo()) && ((CadastroDinamicoItemVO) config).isMostrarCampo()).findAny();

            configuracaoClientePais = configuracoes.stream().map(config -> (CadastroDinamicoItemVO) config)
                    .filter(config -> "PAIS".equals(((CadastroDinamicoItemVO) config).getNomeCampo()) && ((CadastroDinamicoItemVO) config).isMostrarCampo()).findAny();
        }
        if(configuracaoClienteCidade.isPresent() && !configuracaoClienteEstado.isPresent()){
            throw new ConsistirException("Na configuração de "+nomeConfiguracao+", para que o campo Cidade seja mostrado, os campos Estado e País também devem ser apresentados");
        }
        if(configuracaoClienteEstado.isPresent() && !configuracaoClientePais.isPresent()){
            throw new ConsistirException("Na configuração de "+nomeConfiguracao+", para que o campo Estado seja mostrado, o campo País também deve ser apresentado");
        }
    }

    public boolean isApresentarMarketPlace() {
        return apresentarMarketPlace;
    }

    public void setApresentarMarketPlace(boolean apresentarMarketPlace) {
        this.apresentarMarketPlace = apresentarMarketPlace;
    }

    public boolean isTelefoneEmergenciaPendente() {
        return telefoneEmergenciaPendente;
    }

    public void setTelefoneEmergenciaPendente(boolean telefoneEmergenciaPendente) {
        this.telefoneEmergenciaPendente = telefoneEmergenciaPendente;
    }

    public boolean isTelefoneEmergenciaApresentar() {
        return telefoneEmergenciaApresentar;
    }

    public void setTelefoneEmergenciaApresentar(boolean telefoneEmergenciaApresentar) {
        this.telefoneEmergenciaApresentar = telefoneEmergenciaApresentar;
    }

    public boolean isTelefoneEmergenciaOb() {
        return telefoneEmergenciaOb;
    }

    public void setTelefoneEmergenciaOb(boolean telefoneEmergenciaOb) {
        this.telefoneEmergenciaOb = telefoneEmergenciaOb;
    }

    public boolean isAgruparRemessasCartaoEDI() {
        return agruparRemessasCartaoEDI;
    }

    public void setAgruparRemessasCartaoEDI(boolean agruparRemessasCartaoEDI) {
        this.agruparRemessasCartaoEDI = agruparRemessasCartaoEDI;
    }

    public String getChavePublicaSESC() {
        if (chavePublicaSESC == null) {
            chavePublicaSESC = "";
        }
        return chavePublicaSESC;
    }

    public void setChavePublicaSESC(String chavePublicaSESC) {
        this.chavePublicaSESC = chavePublicaSESC;
    }

    public String getChavePrivadaSESC() {
        if (chavePrivadaSESC == null) {
            chavePrivadaSESC = "";
        }
        return chavePrivadaSESC;
    }

    public void setChavePrivadaSESC(String chavePrivadaSESC) {
        this.chavePrivadaSESC = chavePrivadaSESC;
    }

    public boolean isPermitirReplicarPlanoRedeEmpresa() {
        return permitirReplicarPlanoRedeEmpresa;
    }

    public void setPermitirReplicarPlanoRedeEmpresa(boolean permitirReplicarPlanoRedeEmpresa) {
        this.permitirReplicarPlanoRedeEmpresa = permitirReplicarPlanoRedeEmpresa;
    }

    public boolean isPermitirReplicarFeriadoRedeEmpresa() {
        return permitirReplicarFeriadoRedeEmpresa;
    }

    public void setPermitirReplicarFeriadoRedeEmpresa(boolean permitirReplicarFeriadoRedeEmpresa) {
        this.permitirReplicarFeriadoRedeEmpresa = permitirReplicarFeriadoRedeEmpresa;
    }

    public boolean isPermitirReplicarPerfilAcessoRedeEmpresa() {
        return permitirReplicarPerfilAcessoRedeEmpresa;
    }

    public void setPermitirReplicarPerfilAcessoRedeEmpresa(boolean permitirReplicarPerfilAcessoRedeEmpresa) {
        this.permitirReplicarPerfilAcessoRedeEmpresa = permitirReplicarPerfilAcessoRedeEmpresa;
    }

    public boolean getForcarUtilizacaoPlanoAntigo() {
        return forcarUtilizacaoPlanoAntigo;
    }

    public void setForcarUtilizacaoPlanoAntigo(boolean forcarUtilizacaoPlanoAntigo) {
        this.forcarUtilizacaoPlanoAntigo = forcarUtilizacaoPlanoAntigo;
    }

    public boolean isGeneroOb() {
        return generoOb;
    }

    public void setGeneroOb(boolean generoOb) {
        this.generoOb = generoOb;
    }

    public boolean isGeneroApresentar() {
        return generoApresentar;
    }

    public void setGeneroApresentar(boolean generoApresentar) {
        this.generoApresentar = generoApresentar;
    }

    public boolean isGeneroPendente() {
        return generoPendente;
    }

    public void setGeneroPendente(boolean generoPendente) {
        this.generoPendente = generoPendente;
    }

    public boolean isNomeRegistroOb() {
        return nomeRegistroOb;
    }

    public void setNomeRegistroOb(boolean nomeRegistroOb) {
        this.nomeRegistroOb = nomeRegistroOb;
    }

    public boolean isNomeRegistroApresentar() {
        return nomeRegistroApresentar;
    }

    public void setNomeRegistroApresentar(boolean nomeRegistroApresentar) {
        this.nomeRegistroApresentar = nomeRegistroApresentar;
    }

    public boolean isNomeRegistroPendente() {
        return nomeRegistroPendente;
    }

    public void setNomeRegistroPendente(boolean nomeRegistroPendente) {
        this.nomeRegistroPendente = nomeRegistroPendente;
    }

    public boolean isUtilizarServicoSesiSC() {
        return utilizarServicoSesiSC;
    }

    public void setUtilizarServicoSesiSC(boolean utilizarServicoSesiSC) {
        this.utilizarServicoSesiSC = utilizarServicoSesiSC;
    }

    public String getMascaraTelefone() {
        if (mascaraTelefone == null){
            mascaraTelefone = "";
        }
        return mascaraTelefone;
    }

    public void setMascaraTelefone(String mascaraTelefone) {
        this.mascaraTelefone = mascaraTelefone;
    }


    public boolean isUtilizarFormatoMMDDYYYDtNascimento() {
        return utilizarFormatoMMDDYYYDtNascimento;
    }

    public void setUtilizarFormatoMMDDYYYDtNascimento(boolean utilizarFormatoMMDDYYYDtNascimento) {
        this.utilizarFormatoMMDDYYYDtNascimento = utilizarFormatoMMDDYYYDtNascimento;
    }

    public boolean isAssinaturaContratoViaEmail() {
        return assinaturaContratoViaEmail;
    }

    public void setAssinaturaContratoViaEmail(boolean assinaturaContratoViaEmail) {
        this.assinaturaContratoViaEmail = assinaturaContratoViaEmail;
    }

    public boolean isContatoEmergenciaApresentar() {
        return contatoEmergenciaApresentar;
    }

    public void setContatoEmergenciaApresentar(boolean contatoEmergenciaApresentar) {
        this.contatoEmergenciaApresentar = contatoEmergenciaApresentar;
    }

    public boolean isContatoEmergenciaOb() {
        return contatoEmergenciaOb;
    }

    public void setContatoEmergenciaOb(boolean contatoEmergenciaOb) {
        this.contatoEmergenciaOb = contatoEmergenciaOb;
    }

    public boolean isContatoEmergenciaPendente() {
        return contatoEmergenciaPendente;
    }

    public void setContatoEmergenciaPendente(boolean contatoEmergenciaPendente) {
        this.contatoEmergenciaPendente = contatoEmergenciaPendente;
    }

    public Boolean getLumi() {
        return lumi;
    }

    public void setLumi(Boolean lumi) {
        this.lumi = lumi;
    }

    public boolean isRealizarEnvioSesiSC() {
        return realizarEnvioSesiSC;
    }

    public void setRealizarEnvioSesiSC(boolean realizarEnvioSesiSC) {
        this.realizarEnvioSesiSC = realizarEnvioSesiSC;
    }

    public boolean isPermiteTrocaEmpresaMultiChave() {
        return permiteTrocaEmpresaMultiChave;
    }

    public void setPermiteTrocaEmpresaMultiChave(boolean permiteTrocaEmpresaMultiChave) {
        this.permiteTrocaEmpresaMultiChave = permiteTrocaEmpresaMultiChave;
    }

    public boolean isPermiteLancarFeriasPlanoRecorrente() {
        return permiteLancarFeriasPlanoRecorrente;
    }

    public void setPermiteLancarFeriasPlanoRecorrente(boolean permiteLancarFeriasPlanoRecorrente) {
        this.permiteLancarFeriasPlanoRecorrente = permiteLancarFeriasPlanoRecorrente;
    }

    public boolean isPermitirMudarTipoParcelamentoVendaRapida() {
        return permitirMudarTipoParcelamentoVendaRapida;
    }

    public void setPermitirMudarTipoParcelamentoVendaRapida(boolean permitirMudarTipoParcelamentoVendaRapida) {
        this.permitirMudarTipoParcelamentoVendaRapida = permitirMudarTipoParcelamentoVendaRapida;
    }

    public boolean isUsaPlanoRecorrenteCompartilhado() {
        return usaPlanoRecorrenteCompartilhado;
    }

    public void setUsaPlanoRecorrenteCompartilhado(boolean usaPlanoRecorrenteCompartilhado) {
        this.usaPlanoRecorrenteCompartilhado = usaPlanoRecorrenteCompartilhado;
    }

    public boolean isPermiteImpressaoContratoMutavel() {
        return permiteImpressaoContratoMutavel;
    }

    public void setPermiteImpressaoContratoMutavel(boolean permiteImpressaoContratoMutavel) {
        this.permiteImpressaoContratoMutavel = permiteImpressaoContratoMutavel;
    }

    public boolean isPermitirReplicarUsuarioRedeEmpresa() {
        return permitirReplicarUsuarioRedeEmpresa;
    }

    public void setPermitirReplicarUsuarioRedeEmpresa(boolean permitirReplicarUsuarioRedeEmpresa) {
        this.permitirReplicarUsuarioRedeEmpresa = permitirReplicarUsuarioRedeEmpresa;
    }

    public boolean isPermitirReplicarFornecedorRedeEmpresa() {
        return permitirReplicarFornecedorRedeEmpresa;
    }

    public void setPermitirReplicarFornecedorRedeEmpresa(boolean permitirReplicarFornecedorRedeEmpresa) {
        this.permitirReplicarFornecedorRedeEmpresa = permitirReplicarFornecedorRedeEmpresa;
    }

    public boolean isPermitirReplicarModeloContratoRedeEmpresa() {
        return permitirReplicarModeloContratoRedeEmpresa;
    }

    public void setPermitirReplicarModeloContratoRedeEmpresa(boolean permitirReplicarModeloContratoRedeEmpresa) {
        this.permitirReplicarModeloContratoRedeEmpresa = permitirReplicarModeloContratoRedeEmpresa;
    }

    public boolean isConciliarSemNumeroParcela() {
        return conciliarSemNumeroParcela;
    }

    public void setConciliarSemNumeroParcela(boolean conciliarSemNumeroParcela) {
        this.conciliarSemNumeroParcela = conciliarSemNumeroParcela;
    }

    public boolean isManterContratoAssinadoNaRenovacaoContrato() {
        return manterContratoAssinadoNaRenovacaoContrato;
    }

    public void setManterContratoAssinadoNaRenovacaoContrato(boolean manterContratoAssinadoNaRenovacaoContrato) {
        this.manterContratoAssinadoNaRenovacaoContrato = manterContratoAssinadoNaRenovacaoContrato;
    }

    public boolean isTermoResponsabilidadeExaluno() {
        return termoResponsabilidadeExaluno;
    }

    public void setTermoResponsabilidadeExaluno(boolean termoResponsabilidadeExaluno) {
        this.termoResponsabilidadeExaluno = termoResponsabilidadeExaluno;
    }

    public int getQtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline() {
        return qtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline;
    }

    public void setQtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline(int qtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline) {
        this.qtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline = qtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline;
    }

    public boolean isLoginatravesazuread() {
        return loginatravesazuread;
    }

    public void setLoginatravesazuread(boolean loginatravesazuread) {
        this.loginatravesazuread = loginatravesazuread;
    }

    public String getAzureadtenatid() {
        return azureadtenatid;
    }

    public void setAzureadtenatid(String azureadtenatid) {
        this.azureadtenatid = azureadtenatid;
    }

    public String getAzureadclientid() {
        return azureadclientid;
    }

    public void setAzureadclientid(String azureadclientid) {
        this.azureadclientid = azureadclientid;
    }

    public boolean getApresentarCnpjSesi() {
        return apresentarCnpjSesi;
    }

    public void setApresentarCnpjSesi(boolean apresentarCnpjSesi) {
        this.apresentarCnpjSesi = apresentarCnpjSesi;
    }

    public boolean getObjCnpjSesi() {
        return objCnpjSesi;
    }

    public void setObjCnpjSesi(boolean objCnpjSesi) {
        this.objCnpjSesi = objCnpjSesi;
    }

    public boolean getPendenteCnpjSesi() {
        return pendenteCnpjSesi;
    }

    public void setPendenteCnpjSesi(boolean pendenteCnpjSesi) {
        this.pendenteCnpjSesi = pendenteCnpjSesi;
    }

    public String getUsuarioApiSescGo() {
        return usuarioApiSescGo;
    }

    public void setUsuarioApiSescGo(String usuarioApiSescGo) {
        this.usuarioApiSescGo = usuarioApiSescGo;
    }

    public String getSenhaApiSescGo() {
        return senhaApiSescGo;
    }

    public void setSenhaApiSescGo(String senhaApiSescGo) {
        this.senhaApiSescGo = senhaApiSescGo;
    }

    public boolean isPermiteEstornarContrato30MinAposLancamento() {
        return permiteEstornarContrato30MinAposLancamento;
    }

    public void setPermiteEstornarContrato30MinAposLancamento(boolean permiteEstornarContrato30MinAposLancamento) {
        this.permiteEstornarContrato30MinAposLancamento = permiteEstornarContrato30MinAposLancamento;
    }

    public int getIdadeMinima() {
        return idadeMinima;
    }

    public void setIdadeMinima(int idadeMinima) {
        this.idadeMinima = idadeMinima;
    }

    public String getTituloConvite() {
        return tituloConvite;
    }

    public void setTituloConvite(String tituloConvite) {
        this.tituloConvite = tituloConvite;
    }

    public String getDescricaoConvite() {
        return descricaoConvite;
    }

    public void setDescricaoConvite(String descricaoConvite) {
        this.descricaoConvite = descricaoConvite;
    }

    public Integer getTempoReabilitacaoExAluno() {
        return tempoReabilitacaoExAluno;
    }

    public void setTempoReabilitacaoExAluno(Integer tempoReabilitacaoExAluno) {
        this.tempoReabilitacaoExAluno = tempoReabilitacaoExAluno;
    }
}
