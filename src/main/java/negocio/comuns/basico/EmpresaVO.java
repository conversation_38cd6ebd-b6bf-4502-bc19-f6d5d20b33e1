package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import annotations.arquitetura.PassWord;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.json.ConfigCobrancaMensalJSON;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TemporalRemocaoPontoEnum;
import br.com.pactosolucoes.enumeradores.TipoCancelamentoEnum;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.enumeradores.TipoParcelaCancelamento;
import br.com.pactosolucoes.enumeradores.TipoVigenciaMyWellnessGymPassEnum;
import br.com.pactosolucoes.enumeradores.UsoArredondamentoEnum;
import br.com.pactosolucoes.integracao.pactopay.dto.EmpresaDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.EnderecoDTO;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.ClassificacaoNegocioEnum;
import negocio.comuns.basico.enumerador.PessoaAnexoEnum;
import negocio.comuns.basico.enumerador.TimeZoneEnum;
import negocio.comuns.basico.enumerador.TipoEmpresaFinanceiro;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoEmpresaBitrixVO;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.CampanhaDuracao;
import negocio.facade.jdbc.basico.PlanoTipoVO;
import org.apache.commons.lang.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import servicos.propriedades.PropsService;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Reponsável por manter os dados da entidade Empresa. Classe do tipo VO - Value
 * Object composta pelos atributos da entidade com visibilidade protegida e os
 * métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 * @see SuperEmpresaVO
 */
public class EmpresaVO extends SuperEmpresaVO {

    public static String TIPOS_PRODUTOS_EMITIR_NFSe_PADRAO = "PM|SE|MA|RE|RN|AA|DI|TD|TA|SS";
    public static String TIPOS_PRODUTOS_EMITIR_NFCe_PADRAO = "PM|SE|MA|RE|RN|AA|DI|TD|TA|SS";

    public static final String SEM_TELEFONE = "SEM TELEFONE";

    @ChavePrimaria
    protected Boolean permiteSituacaoAtestadoContrato;
    protected Boolean permiteContratosConcomintante;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Questionario </code>.
     */
    @ChaveEstrangeira
    @FKJson
    protected QuestionarioVO questionarioPrimeiraVisita;
    protected Integer nrDiasVigenteQuestionarioVista;

    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Questionario </code>.
     */
    @ChaveEstrangeira
    @FKJson
    protected QuestionarioVO questionarioRetorno;
    protected Integer nrDiasVigenteQuestionarioRetorno;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Questionario </code>.
     */
    @ChaveEstrangeira
    @FKJson
    protected QuestionarioVO questionarioReMatricula;
    protected Integer nrDiasVigenteQuestionarioRematricula;
    @ChaveEstrangeira
    @FKJson
    private QuestionarioVO questionarioPrimeiraCompra;
    private Integer nrDiasVigenteQuestionarioPrimeiraCompra = 0;
    @ChaveEstrangeira
    @FKJson
    private QuestionarioVO questionarioRetornoCompra;
    private Integer nrDiasVigenteQuestionarioRetornoCompra = 0;
    protected Double juroParcela;
    protected Double multa;
    protected String mascaraMatricula;
    protected Integer carenciaRenovacao;
    protected Integer nrDiasAvencer;
    protected Integer somaDv;
    protected Integer toleranciaPagamento;
    protected Integer toleranciaOcupacaoTurma = 0;
    protected Integer tentativasLiberarParcelaVencida = 1;
    protected Integer qtdFaltaPeso1;
    protected Integer qtdFaltaInicioPeso2;
    protected Integer qtdFaltaTerminoPeso2;
    protected Integer qtdFaltaPeso3;
    protected Integer qtdDiasParaLiberacaoDeVagaEmTrancamento;
    protected boolean usarDataInicioDeContratoNoBI_ICV;
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>ContaCorrenteEmpresa</code>.
     */
    @Lista
    @ListJson(clazz = ContaCorrenteEmpresaVO.class)
    @NaoControlarLogAlteracao
    private List<ContaCorrenteEmpresaVO> contaCorrenteEmpresaVOs;
    @NaoControlarLogAlteracao
    private List<ProdutoDevolverCancelamentoEmpresaVO> produtoDevolverCancelamentoEmpresaVOS;
    @NaoControlarLogAlteracao
    private List<ConfiguracaoReenvioMovParcelaEmpresaVO> configuracaoReenvioMovParcelaEmpresaVOS;
    @NaoControlarLogAlteracao
    private List<ContaCorrenteEmpresaVO> contaCorrenteEmpresaVOsAntesAlteracao;
    @NaoControlarLogAlteracao
    private List<ConfiguracaoReenvioMovParcelaEmpresaVO> configuracaoReenvioMovParcelaEmpresaVOSAntesAlteracao;
    private int nrDiasProrata;
    private int toleranciaProrata = 3;
    private int nrDiasCompensacao;
    private int nrDiasChequeAVista;
    private int toleranciaDiasContratoVencido = 0;
    private String timeZoneDefault = TimeZoneEnum.Brazil_East.getId();
    private Boolean empresaEscolhida;
    private String urlRecorrencia = "";
    private String serviceUsuario = "";
    @PassWord
    private String serviceSenha = "";
    private boolean bloquearAcessoSeParcelaAberta;
    private boolean bloquearAcessoSeDebitoEmConta;
    private boolean bloquearAcessoArmarioVigenciaVencida;
    private boolean bloquearAcessoMatriculaRematriculaTotemSemPagamento = false;
    @ChaveEstrangeira
    @FKJson
    private ColaboradorVO consultorVendaAvulsa;
    private boolean permiteHorariosConcorrentesParaProfessor = false;
    private boolean professorEmAmbientesDiferentesMesmoHorario = false;
    private boolean permiteReposicaoEmTurmasDiferentes = false;
    private boolean permiteRenovarContratosEmTurmasLotadas = false;
    private boolean bloquearRenovacaoAutomaticaPlanosForaDaVigencia = false;
    private boolean bloquearRenovacaoAutomaticaPlanosForaCategoriaCliente = false;
    private String tokenSMS;
    private String tokenSMSShortCode;
    @FKJson
    private CidadeVO cidade;
    private String CidadeNome = "";
    private String EstadoSigla = "";
    private boolean bvObrigatorio = false;
    private int tempoAposFaltaReposicao = 60;
    private boolean comissaoMatriculaRematricula = false;
    private String chaveNFSe = "";
    private boolean usarNFSe = false;
    private boolean usarNFSePorPagamento = false;
    private boolean enviarNFSeAutomatico = false;
    private int qtdDiasCobrarRematricula = 0;
    private boolean permiteAlterarDataEmissaoNFSe = false;
    private boolean permiteGerarNotaManual = false;
    /**
     * Recibo
     */
    private boolean mostrarCnpj = true;
    private boolean mostrarModalidade = false;
    private int qtdVias = 2;
    private boolean quebrarPaginaRecibo = false;
    private boolean detalharPeriodoProduto = true;
    private boolean detalharParcelas = true;
    private boolean detalharPagamentos = true;
    private boolean detalharDescontos = true;
    private boolean apresentarAssinaturas = true;
    private boolean detalharNiveisModalidades = false;
    private boolean exigirAssinaturaDigitalResponsavelFinanceiro = false;
    private boolean emitirNoNomeResponsavel = false;
    private String observacaoRecibo = "";
    private int tipoGestaoNFSe = 1;
    private boolean retrocederValorMensalPlanoCancelamento = true;
    private boolean usarGestaoCreditosPersonal = false;
    private boolean fecharNegociacaoSemAutorizacaoDCC = true;
    private boolean removerVinculosAposDesistencia = false;
    private int nrDiasDesistenteRemoverVinculoTreino = 0;
    private boolean liberarPersonalComTaxaEmAberto = true;
    private boolean reciboParaImpressoraTermica = false;
    private boolean usarManutencaoModalidadeComissao = false;
    private boolean devolucaoEntraNoCaixa = true; //não usado mais, a partir do projeto #4999
    private int creditoDCC = 0;
    private Date dataExpiracaoCreditoDCC = null;
    private TipoCancelamentoEnum tipoCancelamento;
    private UsoArredondamentoEnum arredondamento;
    private ConfiguracaoGestaoPersonalVO configsPersonal = new ConfiguracaoGestaoPersonalVO();
    private boolean forcarMinimoVencimento2parcela = false;
    @NaoControlarLogAlteracao
    private boolean feitoUploadAlgumaFoto = false;
    @NaoControlarLogAlteracao
    private String fotoKey = null;
    private String latitude = "";
    private String longitude = "";

    private boolean acessoChamada = false;
    @ChaveEstrangeira
    @FKJson
    private LocalAcessoVO localAcessoChamada;
    @ChaveEstrangeira
    @FKJson
    private ColetorVO coletorChamada;

    private Integer diasRenovacaoAutomaticaAntecipada;
    private boolean mostrarMensagemValoresRodape = true;
    private Boolean cobrarAutomaticamenteMultaJuros;
    private Double multaCobrancaAutomatica;
    private boolean utilizarMultaValorAbsoluto = false;
    private Double jurosCobrancaAutomatica;
    private boolean utilizarJurosValorAbsoluto = false;
    private Boolean liberarPersonalProfessorDebito;
    @ChaveEstrangeira
    @FKJson
    private ColaboradorVO consultorSite;
    private boolean criarBvVendaSite = false;
    private TipoObjetosCobrarEnum tipoParcelasCobrarVendaSite;
    private TipoObjetosCobrarEnum tipoParcelasCobrarVendaSiteRegua;
    private Boolean emiteNFSEPorDataCompensacao;

    private boolean pagarComissaoSeAtingirMetaFinanceira = false;
    private boolean pagarComissaoManutencaoModalidade = false;
    private boolean pagarComissaoProdutos = false;
    private Boolean somenteVendaProdutosComEstoque = false;
    private Integer minutosAposUltimoAcessoDiminuirCredito = 0;
    @FKJson
    private ModeloMensagemVO modeloMensagemVendasOnline;
    @FKJson
    private ModeloMensagemVO modeloMensagemEsqueciMinhaSenhaVendasOnline;

    private boolean cancelamentoObrigatoriedadePagamento = false;
    private boolean cancelamentoAntecipado = false;
    private Integer cancelamentoAntecipadoDias;
    private Double cancelamentoAntecipadoMulta;
    private String cancelamentoAntecipadoPlanos;
    private Double cancelamentoAntecipadoPlanosMulta;
    private Date cancelamentoAntecipadoPlanosData;
    private Date cancelamentoAntecipadoContratosDepoisDe;
    private boolean cancelamentoAntecipadoGerarParcelaMultaSeparada = false;
    private boolean permitirAlterarDataFinalContratoNoCancelamento = false;
    private boolean cancelamentoAutomaticoAntecipadoContratoForaRecorrencia = false;
    private boolean cancelamentoAvaliandoParcelas = false;

    private boolean enviarEmailCancelamento = false;
    private boolean gerarRemessaContratoCancelado = false;
    private boolean permiteContratoPosPagoRenovacaoAuto = false;

    //CONFIGURACOES DE COBRANCA - PRE-PAGO --- POS-PAGO PACTO
    private Integer tipoCobrancaPacto;
    private boolean gerarCobrancaAutomaticaPacto = false;
    private Date dtUltimaCobrancaPacto;
    private Integer qtdDiasFechamentoCobrancaPacto;
    private Double valorCreditoPacto;
    private boolean gerarNotaFiscalCobrancaPacto = false;
    private Integer qtdParcelasCobrancaPacto;
    private Integer qtdCreditoRenovarPrePagoCobrancaPacto;
    @NaoControlarLogAlteracao
    private Integer qtdCreditoRemessa;
    @NaoControlarLogAlteracao
    private Integer qtdCreditoTransacao;
    @NaoControlarLogAlteracao
    private Integer qtdCreditoPix;
    @NaoControlarLogAlteracao
    private Integer creditoDCCBonus;

    private Integer qtdDiasVencimentoBoleto;

    private boolean existemNovosBoletosPacto = false;
    private ConvenioCobrancaVO convenioBoletoPadrao;
    private boolean gerarLoginAPIAoIncluirContrato = false;
    private String tipoParcelaCancelamento = TipoParcelaCancelamento.TODAS_PARCELAS.getSigla();
    private int quantidadeParcelasSeguidasCancelamento = 0;
    private boolean enviarNotaCidadeEmpresa = false;
    private boolean permitirMaillingGerarAutorizacaoCobrancaBoleto = false;
    private boolean permiteMaillingCriarBoleto = false;
    private boolean gerarNFSeContaCorrente = false;
    private String inscMunicipal;
    private Integer sequencialLoteRPS;
    private boolean permiteGerarArquivoLoteRPS = false;
    private PessoaVO pessoaFinan;
    private boolean impedirVendaContratoPorConflitoReposicao = false;
    private boolean gerarQuitacaoCancelamentoAuto = false;
    private boolean gerarQuitacaoCancelamentoRemanescente = false;
    private boolean aplicarMultaeJurosNoCancelamentoAutomatico = false;
    private boolean aplicarMultaMudancaPlano = false;
    private boolean naoGerarResiduoCancelamentoAutomatico = false;
    private boolean depositarResiduoCancelamentoNaContaCorrente = false;
    private boolean gerarBoletoCaixaAberto = true;

    //UTILIZADOS PARA NFC-e
    private boolean usarNFCe = false;
    private boolean usarNFCePorPagamento = false;
    private boolean usarNomeResponsavelNFCe = false;
    private boolean utilizarNomeResponsavelNoBoleto = false;

    private boolean utilizarNomeResponsavelNoBoletoMaiorIdade = false;
    private boolean utilizarNomeResponsavelNoPixMenorIdade = false;
    private boolean utilizarNomeResponsavelNoPixMaiorIdade = false;
    private boolean alterarDataHoraCheckGestaoPersonal = true;
    private boolean senhaAcessoOnzeDigitos = false;
    private boolean bloqueioTemporario = false;

    //UTILIZAR PARA INDICE FINANCEIRO
    private boolean naoRenovarContratoSemIndiceFinanceiro = false;

    //GYMPASS
    private String codigoGymPass;
    private String tokenApiGymPass;


    private boolean habilitarSomaDeAulaNaoVigente = false;

    private boolean definirCpfComoSenhaCatraca = false;

    private boolean mostrarNotaPorDiaCompetencia = false;

    private String diasAtivosPontuacaoAcesso;
    private Integer pontosAlunoAcesso = 0;
    private Integer pontosAlunoAcessoChuva = 0;
    private Integer pontosAlunoAcessoFrio = 0;
    private Integer pontosAlunoAcessoCalor = 0;

    private boolean trabalharComPontuacao = false;

    private ConfiguracaoEmpresaRDStationVO configuracaoRDStation;
    private ConfiguracaoIntegracaoBuzzLeadVO configuracaoIntegracaoBuzzLeadVO;
    private ConfiguracaoIntegracaoWordPressVO configuracaoIntegracaoWordPressVO;
    private ConfiguracaoIntegracaoJoinVO configuracaoIntegracaoJoinVO;
    private ConfiguracaoIntegracaoGenericaLeadsVO configuracaoIntegracaoGenericaLeadsVO;
    private ConfiguracaoIntegracaoGenericaLeadsGymbotVO configuracaoIntegracaoGenericaLeadsGymbotVO;

    private ConfiguracaoIntegracaoFogueteVO configuracaoIntegracaoFogueteVO;
    private boolean retirarEdicaoPagamento = false;

    private boolean adicionarAulasDesmarcadasContratoAnterior = false;

    private String tipoParcelaCancelamentoForaRegimeRecorrencia = TipoParcelaCancelamento.TODAS_PARCELAS.getSigla();
    private int quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia = 0;
    private int quantidadeDiasUteisAposVencimentoParaCancelarContrato = 0;
    private boolean considerarSomenteParcelasPlanos = false;

    private Date limiteInicialItensBIPendencia;

    //Utilizar para Tempo medio de saida da academia para quem não tem catraca e em fora de saida.
    private Integer tempoSaidaAcademia;

    //Utilizar para Tempo medio de saida da academia para quem não tem catraca e nao tem registro de saida formatado.
    @NaoControlarLogAlteracao
    private String tempoSaidaAcademiaFormatada = "";
    private boolean registrarTentativasAcesso = false;
    private boolean permiteRenovarContratoViaAPP = false;
    private Double valorMensalEmitirNFSe;
    private boolean emitirNotaSomenteRecorrencia = false;
    private boolean emitirNomeAlunoNotaFamilia = true;
    private boolean cobrarParcelaComBoletoGerado = true;
    private boolean manterMarcacoesFuturasCreditoRenovacao = false;
    private boolean cobrarParcelaVencidaSemTentativaCobranca = false;
    private boolean habilitarReenvioAutomaticoRemessa = false;
    private boolean irTelaPagamentoCartaoCreditoRecorrente = false;
    private boolean irTelaPagamentoCartaoCreditoFormaPagamento = true;
    private boolean enviarNFCeAutomatico = false;
    private boolean emitirNFCeSomenteRecorrencia = false;
    private boolean emitirMesReferenciaNFCe = false;
    private boolean usaIntegracoesCrm = false;
    private String tokenBuzzLead = "";
    private String urlLinkSiteCadastro;
    private String tiposProduto;
    private boolean integracaoWeHelpHabilitada = false;
    private boolean cpfCodigoInternoWeHelp = false;

    private boolean usarDataOriginalCompensacaoNFSe = true;

    private boolean permitirLancarVariasParcelasSaldoDevedor = false;

    private boolean utilizaSistemaEstacionamento = false;
    @ChaveEstrangeira
    @FKJson
    private EmpresaConfigEstacionamentoVO configEstacionamento;


    private boolean permMarcarAulaFeriado;
    private String horaAberturaFeriado = "";
    private String horaFechamentoFeriado = "";

    private boolean permAlunoMarcarAulaOutraEmpresa;
    private Integer produtoEmissaoNFCeFinanceiro;
    private Integer produtoEmissaoNFSeFinanceiro;
    private Integer qtdExecucoesRetentativa;
    private boolean consultarDiasAnterioresNFSe = false;
    private boolean consultarDiasAnterioresNFCe = false;
    private boolean emImportacao = false;
    private boolean permitirEstornarContratoComParcelasPG = true;

    private boolean integracaoSpiviHabilitada = false;
    private String integracaoSpiviSourceName;
    private Integer integracaoSpiviSiteID;
    private String integracaoSpiviPassword;

    /**
     * Campo para definir se devem geradas notas automaticamente quando o pagamento é retroativo
     */
    private boolean notasAutoPgRetroativo = true;
    private boolean validarVencimentoCartaoAutorizacao = true;
    private String formasPagamentoEmissaoNFCe;
    private String formasPagamentoEmissaoNFSe;
    private boolean validarCertificado = true;
    private boolean cobrarMultaJurosTransacao = false;
    private boolean cobrarMultaJurosDCO = false;
    private boolean cobrarMultaJurosDCC = false;
    private boolean cobrarMultaJurosPix = false;
    private boolean addAutoClienteTreinoWeb = false;
    private boolean naoCobrarMultaDeContratoRenovado = false;
    private boolean naoCobrarMultaDeTodasParcelasPagas = false;
    private boolean mostrarValoresZeradosRel = true;
    private boolean utilizaLeitorCodigoBarras = false;
    private String nomeUsuarioAmigoFit;
    @PassWord
    private String senhaUsuarioAmigoFit;

    @NaoControlarLogAlteracao
    private ParceiroFidelidadeVO parceiroFidelidade;

    private boolean usarParceiroFidelidade = false;
    @NaoControlarLogAlteracao
    private String idExterno;
    private boolean isEmpresaInternacional;
    private boolean zerarValorCancelamentoTransferencia = true;
    /**
     * Define se deve enviar notificações automáticas sobre NFSes (Canceladas, Não Autorizadas, etc).
     */
    private boolean envioNotificacaoNotasNFSe = false;
    /**
     * Define se deve enviar notificações automáticas sobre NFCes (Canceladas, Não Autorizadas, etc).
     */
    private boolean envioNotificacaoNotasNFCe = false;
    /**
     * Guarda os emails que serão enviados notificações automáticas sobre notas. Os e-mails são separados por ponto-virgula.
     */
    private String emailsNotificacaoAutomaticaNotas = "";
    /**
     * Email para recebimento de notificações provenientes do Vendas Online.
     */
    private String emailNotificacaoVendasOnline;
    @NaoControlarLogAlteracao
    private List<LogCobrancaPactoVO> logCobrancaPacto;
    private Date dataConcessaoDiaExtra;
    private int totalDiasExtras;
    private Date dataSuspensao;
    private boolean sincronizadoMovidesk;
    private String emailMovidesk;

    private boolean usarConciliadora = false;
    private String empresaConciliadora;
    @PassWord
    private String senhaConciliadora;

    /**
     * Dias para consulta de relatóro de fechamento de caixa
     * na alteração para os clinemtes será setado 7 como default
     */
    private Integer diasParaRetirarRelFechamentoDeCaixa = 7;
    private Integer diasParaVencimentoParq = 0;

    private String moeda;
    private String descMoeda;
    private String localeTexto;
    private Locale locale;
    @NaoControlarLogAlteracao
    private String responsavelFinanceiro;
    @NaoControlarLogAlteracao
    private String gestor;
    @NaoControlarLogAlteracao
    private ClassificacaoNegocioEnum classificacaoNegocioEnum;
    private boolean atualizarDadosCadastro = false;

    /**
     * Utilizada para Gerar credenciais PJBank
     * */
    @NaoControlarLogAlteracao
    private Integer ddPjbank;
    @NaoControlarLogAlteracao
    private Integer fonePjbank;
    @NaoControlarLogAlteracao
    private Integer valueConta = 0;
    @NaoControlarLogAlteracao
    private String emailPjbank;

    private boolean apenasPrimeiroAcessoClubeVantagens;
    private Integer minutosCreditarProximoPontoClubeVantagens;
    private TemporalRemocaoPontoEnum zerarPontosAposVencimento;
    private boolean aplicarIndicacaoQlqrPlano=false;
    private boolean pontuarApenasCategoriasEmCampanhasAtivas =true;

    @ChaveEstrangeira
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFSe;
    @ChaveEstrangeira
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFCe;
    private String tipoProdutoEmissaoNFSe;
    private String tipoProdutoEmissaoNFCe;
    private String URLWebHookGympass;
    private boolean enviarApenasAlunosAtivosTW = false;
    private boolean emitirDuplicataNFSe = false;

    private boolean emiteValorTotalCompetencia = false;

    private boolean emiteValorTotalFaturamento = false;

    private boolean gerarNotaFiscalComDesconto = false;

    private boolean ignorarCodigoDeBarrasEmissaoNfce = false;
    @NaoControlarLogAlteracao
    private boolean usaEnotas = false;
    @NaoControlarLogAlteracao
    private boolean usaNotasDelphi = false;
    private boolean utilizarDataCancelamentoParaConsultarParcelas = false;
    private Integer codEmpresaFinanceiro = 0;
    private Date sincronizacaoFinanceiro = null;
    private Boolean bloquearAcessoSemAssinaturaDigital;
    private Boolean bloquearAcessoDiariaEmpresaDiferente;
    private Boolean bloquearAcessoCrefVencido;
    private Boolean bloquearAcessoAlunoParqNaoAssinado;
    private boolean cobrarCreditoVindi = false;
    private Integer qtdDiasLimiteCobrancaParcelasRecorrencia;
    private Integer qtdDiasRepetirCobrancaParcelasRecorrencia;
    private String nomeApresentar;
    private boolean tentativaUnicaDeCobranca = false;
    private boolean mostrarDescricaoParcelaRenegociada = false;
    private boolean habilitarCobrancaAutomaticaNaVenda = false;

    private boolean acessoSomenteComAgendamento = false;
    private Integer capacidadeSimultanea = 0;
    private Integer toleranciaAcessoAula = 0;

    private boolean integracaoMyWellnessHabilitada = false;
    private boolean integracaoMyWellnessEnviarVinculos = true;
    private boolean integracaoMyWellnessEnviarGrupos = true;
    private String integracaoMyWellnessFacilityUrl= "";
    private String integracaoMyWellnessApiKey = "";
    private String integracaoMyWellnessUser = "<EMAIL>";
    private String integracaoMyWellnessPassword = "Pac03092020@";
    private TipoVigenciaMyWellnessGymPassEnum tipoVigenciaMyWellnessGymPass = TipoVigenciaMyWellnessGymPassEnum.APENAS_NO_DIA;
    private Integer nrDiasVigenciaMyWellnessGymPass = 1;
    private boolean agruparParcelasPorCartao = false;
    private Double agruparParcelasPorCartaoValorLimite;
    private boolean somenteUmEnvioCartaoTentativa = false;
    private Integer diaVencimentoCobrancaPacto;
    private boolean empresaResponsavelCobrancaPacto = false;
    @NaoControlarLogAlteracao
    private ConfigCobrancaMensalJSON configCobrancaMensalJSON;

    private Date dataExpiracao = null;
    private Date dataExpiracaoNFe = null;
    private Date dataExpiracaoVendasOnline = null;
    private Date dataExpiracaoApp = null;
    private Date dataExpiracaoOutros = null;
    private String motivoExpiracaoOutros = null;

    private Double valorLimiteCaixaAbertoVendaAvulsa;
    private boolean notificarWebhook = false;
    private String urlWebhookNotificar;

    private boolean integracaoMentorWebHabilitada = false;
    private String integracaoMentorWebUrl = "";
    private String integracaoMentorWebServico = "";
    private String integracaoMentorWebUser = "";
    private String integracaoMentorWebPassword = "";

    private boolean integracaoF360RelFatHabilitada = false;
    private boolean usaVitio = false;
    private String linkCheckoutVitio = "";
    private String linkEbook = "";
    private String mensagemVitioQuerComprar = "";
    private String mensagemVitioWhatsapp = "";

    private String integracaoF360FtpServer = "";
    private Integer integracaoF360FtpPort;
    private String integracaoF360User = "";
    private String integracaoF360Password = "";
    private String integracaoF360Dir = "";
    private boolean integracaoF360Quinzenal = false;

    private boolean integracaoAmigoFitHabilitada = false;
    private boolean permiteCadastrarCartaoMesmoAssim = true;
    private boolean enviarEmailPagamento = false; //config para enviar email após pagamento via link de pagamento
    private boolean enviarEmailPagamentoRegua = false; //config para enviar email após pagamento via link de pagamento da régua
    private ConvenioCobrancaVO convenioVerificacaoCartao;

    private Boolean transferida;
    private String novaChaveTransferencia;
    private Integer novoCodigoTransferencia;
    //link de pagamento
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenioCobrancaPix;
    //link de pagamento
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenioCobrancaBoleto;
    //link de pagamento
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenioCobrancaCartao;
    //link de pagamento (Régua)
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenioCobrancaPixRegua;
    //link de pagamento (Régua)
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenioCobrancaBoletoRegua;
    //link de pagamento (Régua)
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenioCobrancaCartaoRegua;

    private Boolean bloquearSemCartaoVacina = false;
    private Integer idadeMinimaCartaoVacina = 18;
    private PessoaAnexoEnum tipoAnexoCartaoVacina = PessoaAnexoEnum.CARTAO_VACINACAO_PRIMEIRA_DOSE;
    private TipoEmpresaFinanceiro tipoEmpresa = TipoEmpresaFinanceiro.NAO_TIPIFICADO;

    private Boolean restringirConvidadoUmaVezPorMes;
    //Day Use
    private ProdutoVO produtoDiaPlus;
    private PlanoTipoVO tipoPlanoDiaPlus;
    private ModalidadeVO modalidadeDiaPlus;

    private ProdutoVO produtoDayUse;
    private ModalidadeVO modalidadeDayUse;
    private String urlEnvioAcesso;
    private String tokenEnvioAcesso;
    private boolean usarSistemaInternacional = false;
    private Integer idContabancariaSesi;
    private Integer codExternoUnidadeSesi;

    private String cnpjClienteSesi;

    private Boolean validaUtilizacaoVitio;


    private Boolean utilizaIntegracaoDelsoft;
    private String hostIntegracaoDelsoft;
    private Integer portaIntegracaoDelsoft;
    private String tokenIntegracaoDelsoft;
    private String nomeAplicacaoDelsoft;
    private String usuarioAplicacaoDelsoft;
    @PassWord
    private String senhaAplicacaoDelsoft;
    private PlanoVO planoAplicacaoDelsoft;
    private boolean responderBVNaVendaRapida=true;

    private String codigoRede;
    private boolean aplicarMultaSobreValorTotalContrato = false;
    private boolean cancelamentoApresentarTransacoes = false;
    private boolean isentarCancelamento7Dias = false;
    private boolean cancelarContratosNaoRenovaveisForaRecorrencia = false;
    private boolean marcarAutoRecebiveisCartaoChequeCancelamento = false;
    private Integer toleranciaCancelarContratosNaoAssinados = 0;

    private int qtdDiasEnvioSPC = 0;
    private boolean consultarNovoCadastroSPC = false;
    private boolean envioAutomaticoSPC = false;
    private String operadorSpc;
    @PassWord
    private String senhaSpc;
    private Long codigoAssociadoSpc;

    private Boolean usarSescDf;
    private String tokenSescDf;

    private ConfiguracaoEmpresaHubspotVO configuracaoEmpresaHubspot;

    private ConfiguracaoEmpresaBitrixVO configuracaoEmpresaBitrix = new ConfiguracaoEmpresaBitrixVO();

    private boolean limitarDescontosPorPerfil = false;
    private boolean pesquisaAutomaticaSPC = false;

    private boolean cobrarMultaJurosAsaas = false;
    private double valorMultaAsaas = 0.0;
    private double valorJurosAsaas = 0.0;
    private boolean concContasPagarFacilitePay = false;
    private boolean concContasReceberFacilitePay = false;
    private int qtdLmtContasConcFacilitePay = 0;
    private boolean facilitePayReguaCobranca = false;
    private boolean facilitePayConciliacaoCartao = false;
    private boolean facilitePayStoneConnect = false;
    private boolean facilitePayCDLSPC = false;
    private boolean facilitePayReguaCobrancaEmail = false;
    private boolean facilitePayReguaCobrancaSms = false;
    private boolean facilitePayReguaCobrancaApp = false;
    private boolean facilitePayReguaCobrancaWhatsApp = false;
    private boolean facilitePayReguaCobrancaGymbotPro = false;

    private boolean gerarAutCobrancaComCobAutBloqueada = false;
    private boolean gerarAutCobrancaComCobAutBloqueadaRegua = false;

    private boolean obrigatorioPreencherCamposCartao = false;
    private Double valorMetaFacilitePay;
    private boolean habilitarCadastroEmpresaSesi = false;

    private Boolean bloquearAcessoSemTermoResponsabilidade;

    private boolean utilizaGestaoClientesComRestricoes = false;

    private String integracaoNuvemshopNomeApp;

    private String integracaoNuvemshopEmail;

    private String integracaoNuvemshopTokenAcesso;

    private String integracaoNuvemshopStoreId;
    private boolean integracaoNuvemshopHabilitada = false;

    private boolean permiteCompartilhamentoPlanoClienteAtivoPlanoCredito = false;
    private boolean utilizaConfigCancelamentoSesc = false;
    @NaoControlarLogAlteracao
    private String contratoProblematicos;
    @NaoControlarLogAlteracao
    private String matriculasProblematicas;

    // Inicio configs pacto print
    private Boolean utilizarPactoPrint = false;
    private Integer validadeMesesCarteirinhaSocio = 0;
    private String presidente;
    private String superintendente;

    // Fim configs pacto print

    private String tokenAcademyGoGood;

    private boolean cobrarCreditoPactoBoleto;

    private boolean integracaoManyChatHabilitada = false;
    private String integracaoManyChatTokenApi = "";
    private String integracaoManyChatTagUnidade = "";
    private boolean envioNFCeAutomaticoNoPagamento = false;
    private boolean horariocapacidadeporcategoria = false;


    private Boolean habilitarValidacaoHorariosMesmaTurma;
    /**
     * Construtor padrão da classe
     * <code>Empresa</code>. Cria uma nova instância desta entidade,
     * inicializando automaticamente seus atributos (Classe VO).
     */
    public EmpresaVO() {
        super();
        inicializarDados();
    }

    public EmpresaVO(Integer codigo, String nome) {
        super();
        inicializarDados();
        this.codigo = codigo;
        this.nome = nome;
    }

    public EmpresaVO(Integer codigo){
        this.codigo = codigo;
    }

    public EmpresaVO(JSONObject mJsonObject) {
        super();
        try {
            setCodigo(mJsonObject.getInt("codigo"));
            setNome(mJsonObject.getString("nome"));
            setCodigoChaveIntegracaoDigitais(mJsonObject.getInt("codigoChaveIntegracaoDigitais"));
            // TODO mapear o objeto de acordo com as necessidades
        } catch (JSONException ex) {
            Logger.getLogger(EmpresaVO.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>EmpresaVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada
     *                            aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     *                            ocorrido.
     */

    public static void validarDados(EmpresaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNome().equals("")) {
            throw new ConsistirException("O campo NOME (Empresa) deve ser informado.");
        }
        if (obj.getRazaoSocial().equals("")) {
            throw new ConsistirException("O campo RAZÃO SOCIAL (Empresa) deve ser informado.");
        }
        if (!UteisValidacao.emptyNumber(obj.getNrDiasProrata()) && obj.getNrDiasProrata() > 30) {
            throw new ConsistirException("O NÚMERO MÁX.DE DIAS PARA GERAR UM MÊS PRÓ-RATA A MAIS não pode ser maior que 30.");
        }
        if (obj.getCNPJ().equals("") && !obj.isEmpresaInternacional()) {
            throw new ConsistirException("O campo CNPJ (Empresa) deve ser informado.");
        }
        if (obj.getEndereco().equals("")) {
            throw new ConsistirException("O campo ENDEREÇO (Empresa) deve ser informado.");
        }
        if(obj.getQuestionarioPrimeiraVisita() == null || UteisValidacao.emptyNumber(obj.getQuestionarioPrimeiraVisita().getCodigo())){
            throw new ConsistirException("O campo QUESTIONÁRIO DE PRIMEIRA VISITA deve ser informado.");
        }
        if (obj.getQuestionarioPrimeiraVisita().getCodigo() != 0 && obj.getNrDiasVigenteQuestionarioVista() == 0) {
            throw new ConsistirException("O campo NÚMERO DIAS VIGENTES PRIMEIRA VISITA (Configuração do Sistema) deve ser informado.");
        }
        if (obj.getPais().getCodigo() == 0) {
            throw new ConsistirException("O campo PAÍS (Empresa) deve ser informado.");
        }
        if (obj.getEstado().getCodigo() == 0) {
            throw new ConsistirException("O campo ESTADO (Empresa) deve ser informado.");
        }
        if (obj.getCidade().getCodigo() == 0) {
            throw new ConsistirException("O campo CIDADE (Empresa) deve ser informado.");
        }
        if (obj.getQuestionarioRetorno().getCodigo() != 0 && obj.getNrDiasVigenteQuestionarioRetorno() == 0) {
            throw new ConsistirException("O campo NÚMERO DIAS VIGENTES RETORNO (Configuração do Sistema) deve ser informado.");
        }

        if (obj.getQuestionarioReMatricula().getCodigo() != 0 && obj.getNrDiasVigenteQuestionarioRematricula() == 0) {
            throw new ConsistirException("O campo NÚMERO DIAS VIGENTES REMATRICULA (Configuração do Sistema) deve ser informado.");
        }

        if (obj.getCarenciaRenovacao() > 28 || obj.getCarenciaRenovacao() < 1) {
            throw new ConsistirException("O campo CARÊNCIA DE RENOVAÇÃO deve ser entre 1 e 28 dias.");
        }

        if (obj.getNrDiasAvencer() > 28 || obj.getNrDiasAvencer() < 1) {
            throw new ConsistirException("O campo Número de Dias A Vencer Renovação deve ser entre 1 e 28 dias.");
        }

        if (obj.getToleranciaOcupacaoTurma() < 0 || obj.getToleranciaOcupacaoTurma() > obj.getCarenciaRenovacao()) {
            throw new ConsistirException("O campo de TOLERÂNCIA OCUPAÇÃO TURMA deve ser maior ou igual a zero e menor ou igual ao campo CARÊNCIA DE RENOVAÇÃO.");
        }
        if (obj.getTentativasLiberarParcelaVencida() < 1) {
            throw new ConsistirException("O campo NÚMERO DE TENTATIVAS PERMITIDAS PARA LIBERAR ACESSO DE PARCELAS VENCIDAS EM REMESSA deve ser maior ou igual a um.");
        }

        if (obj.getQtdFaltaPeso1() != 0) {
            if (obj.getQtdFaltaInicioPeso2() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaInicioPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1 (Configuração do Sistema) não pode ser Maior ou Igual ao campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2.");
            }
            if (obj.getQtdFaltaTerminoPeso2() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaTerminoPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1 (Configuração do Sistema) não pode ser Maior ou Igual ao campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2.");
            }
            if (obj.getQtdFaltaPeso3() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1 (Configuração do Sistema) não pode ser Maior ou Igual ao campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3.");
            }

        }
        if (obj.getQtdFaltaInicioPeso2() != 0) {
            if (obj.getQtdFaltaPeso1() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaInicioPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Menor ou Igual ao campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1.");
            }
            if (obj.getQtdFaltaTerminoPeso2() != 0
                    && (obj.getQtdFaltaInicioPeso2() > obj.getQtdFaltaTerminoPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior que o campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2.");
            }
            if (obj.getQtdFaltaPeso3() != 0
                    && (obj.getQtdFaltaInicioPeso2() >= obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior que o campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3.");
            }

        }
        if (obj.getQtdFaltaTerminoPeso2() != 0) {
            if (obj.getQtdFaltaPeso1() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaTerminoPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Menor ou Igual ao campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1.");
            }
            if (obj.getQtdFaltaInicioPeso2() != 0
                    && (obj.getQtdFaltaInicioPeso2() > obj.getQtdFaltaTerminoPeso2())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior que o campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2.");
            }
            if (obj.getQtdFaltaPeso3() != 0
                    && (obj.getQtdFaltaTerminoPeso2() >= obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior ou Igual que o campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3.");
            }

        }
        if (obj.getQtdFaltaPeso3() != 0) {
            if (obj.getQtdFaltaPeso1() != 0
                    && (obj.getQtdFaltaPeso1() >= obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3 (Configuração do Sistema) não pode ser Menor ou Igual ao campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 1.");
            }
            if (obj.getQtdFaltaInicioPeso2() != 0
                    && (obj.getQtdFaltaInicioPeso2() > obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior que o campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3.");
            }
            if (obj.getQtdFaltaTerminoPeso2() != 0
                    && (obj.getQtdFaltaTerminoPeso2() >= obj.getQtdFaltaPeso3())) {
                throw new ConsistirException("O campo QUANTIDADE MÁXIMA DE FALTAS PARA CLIENTE OBTER PESO 2 (Configuração do Sistema) não pode ser Maior ou Igual que o campo QUANTIDADE MÍNIMA DE FALTAS PARA CLIENTE OBTER PESO 3.");
            }

        }
        if (obj.getTimeZoneDefault().isEmpty()) {
            throw new ConsistirException("O campo FUSO HORÁRIO (Empresa) deve ser informado.");
        }
        if (obj.getNrDiasProrata() < 0) {
            throw new ConsistirException("O campo NÚMERO DE DIAS DO PRORATA deve ser maior ou igual a zero.");
        }
        if (obj.getServiceUsuario() != null && obj.getServiceSenha() != null
                && !obj.getServiceUsuario().isEmpty() && obj.getServiceSenha().isEmpty()) {
            throw new ConsistirException("O usuário do Service foi informado mas não a senha.");
        }

        if (obj.getQtdVias() < 1) {
            throw new ConsistirException("A quantidade de vias deve ser maior ou igual a 1.");
        }
        if (obj.isAcessoChamada() && (obj.getLocalAcessoChamada().getCodigo() == 0 || obj.getColetorChamada().getCodigo() == 0)) {
            throw new ConsistirException("Para usar Acesso na Chamada escolha uma Local de Acesso e um Coletor.");
        }
        if (obj.getQtdDiasParaLiberacaoDeVagaEmTrancamento() < 0) {
            throw new ConsistirException("A quantidade de dias para 'Liberar vagas para trancamentos acima de' deve ser maior ou igual a 0.");
        }

        if (obj.isCancelamentoAntecipado() && !UteisValidacao.emptyString(obj.getCancelamentoAntecipadoPlanos()) && UteisValidacao.emptyNumber(obj.getCancelamentoAntecipadoPlanosMulta())) {
            throw new ConsistirException("Você selecionou o tipo de cancelamento sendo: \"Cancelamento com cálculo de antecedência para vencimento da próxima parcela\", portanto preencha também o campo 'Porcentagem da multa sobre parcelas restantes em aberto'.");
        }

        if (obj.isCancelamentoAntecipado() && !UteisValidacao.emptyString(obj.getCancelamentoAntecipadoPlanos()) && obj.getCancelamentoAntecipadoPlanosData() == null) {
            throw new ConsistirException("Você selecionou o tipo de cancelamento sendo: \"Cancelamento com cálculo de antecedência para vencimento da próxima parcela\", portanto preencha também o campo 'Cobrar multa sobre parcelas restantes para planos lançados até a data'.");
        }

        if ((obj.isCancelamentoAntecipado() && !obj.isCancelamentoObrigatoriedadePagamento()) && (obj.getCancelamentoAntecipadoDias() == 0 || obj.getCancelamentoAntecipadoDias() > 31)) {
            throw new ConsistirException("O campo 'Avisar com até quantos dias de antecedência' deve ser entre 1 e 31 dias.");
        }

        if (obj.isPermiteGerarArquivoLoteRPS() && UteisValidacao.emptyString(obj.getInscMunicipal())) {
            throw new ConsistirException("Inscrição Municipal não está preenchida.");
        }
        if (obj.getCodigoChaveIntegracaoDigitais() != 0 && (obj.getCodigoChaveIntegracaoDigitais() < 1000 || obj.getCodigoChaveIntegracaoDigitais() > 9999)) {
            throw new ConsistirException("O código da chave para Biometrias Unificadas deve conter 4 dígitos");
        }
        if (obj.getConfiguracaoRDStation().isEmpresaUsaRD() && !Calendario.validarStringHora(obj.getConfiguracaoRDStation().getHoraLimite())) {
            throw new ConsistirException("Hora limite informada na configuração do RD Station não é válida. O formado é hh:mm, sendo que hh deve ser entre 00 e 23, e mm entre 00 e 59.");
        }

        if (obj.getTempoSaidaAcademiaFormatada() == null || obj.getTempoSaidaAcademiaFormatada().isEmpty()) {
            throw new ConsistirException("O tempo medio de saida da academia deve ser informado");
        }
        if (obj.permMarcarAulaFeriado) {
            if (obj.getHoraAberturaFeriado() == null || obj.getHoraAberturaFeriado().isEmpty()) {
                throw new ConsistirException("O Horario de Abertura da Academia deve ser informado");
            }
            if (obj.getHoraFechamentoFeriado() == null || obj.getHoraFechamentoFeriado().isEmpty()) {
                throw new ConsistirException("O Horario de Fechamento da Academia deve ser informado");
            }
        }

        if (UteisValidacao.emptyString(obj.getMascaraMatricula())) {
            throw new ConsistirException("O campo Máscara Matrícula deve ser informado.");
        }

        if (obj.isUsarConciliadora()) {
            if (UteisValidacao.emptyString(obj.getEmpresaConciliadora())) {
                throw new ConsistirException("O campo \"Id Empresa (Conciliadora)\" deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getSenhaConciliadora())) {
                throw new ConsistirException("O campo \"Senha (Conciliadora)\" deve ser informado.");
            }
        }

        if (obj.getUsarNFSe() && UteisValidacao.emptyNumber(obj.getConfiguracaoNotaFiscalNFSe().getCodigo())) {
            throw new ConsistirException("Uma configuração de emissão para NFS-e deve ser selecionada.");
        }
        if (obj.isUsarNFCe() && UteisValidacao.emptyNumber(obj.getConfiguracaoNotaFiscalNFCe().getCodigo())) {
            throw new ConsistirException("Uma configuração de emissão para NFC-e deve ser selecionada.");
        }
        if(obj.isIntegracaoMyWellnessHabilitada()){
            if(UteisValidacao.emptyString(obj.getIntegracaoMyWellnessApiKey())){
                throw new ConsistirException("Integracao myWellness está habilitada na aba Integrações. O campo \"ApiKey\" deve ser informado");
            }
            if(UteisValidacao.emptyString(obj.getIntegracaoMyWellnessFacilityUrl())){
                throw new ConsistirException("Integracao myWellness está habilitada na aba Integrações. O campo \"FacilityUrl\" deve ser informado");
            }
        }
        if (obj.isNotificarWebhook() &&
                UteisValidacao.emptyString(obj.getUrlWebhookNotificar())) {
            throw new ConsistirException("Integração Notificação via Webhook está habilitada na aba Integrações. O campo \"URL Webhook\" deve ser informado");
        }

        if(obj.isIntegracaoMentorWebHabilitada()) {
            if(UteisValidacao.emptyString(obj.getIntegracaoMentorWebUrl())) {
                throw new ConsistirException("Integracao Mentor Web está habilitada na aba Integrações. O campo \"Url\" deve ser informado.");
            }
            if(UteisValidacao.emptyString(obj.getIntegracaoMentorWebServico())) {
                throw new ConsistirException("Integracao Mentor Web está habilitada na aba Integrações. O campo \"Nome do serviço\" deve ser informado.");
            }
            if(UteisValidacao.emptyString(obj.getIntegracaoMentorWebUser())) {
                throw new ConsistirException("Integracao Mentor Web está habilitada na aba Integrações. O campo \"Usuário\" deve ser informado.");
            }
            if(UteisValidacao.emptyString(obj.getIntegracaoMentorWebPassword())) {
                throw new ConsistirException("Integracao Mentor Web está habilitada na aba Integrações. O campo \"Senha\" deve ser informado.");
            }
        }

        if(obj.isUtilizaSistemaEstacionamento() && obj.getConfigEstacionamento() != null) {
            EmpresaConfigEstacionamentoVO.validarDados(obj.getConfigEstacionamento());
        }

        if(obj.isIntegracaoF360RelFatHabilitada()) {
            validarDadosF360(obj);
        }

        if(obj.isIntegracaoAmigoFitHabilitada()) {
            if(UteisValidacao.emptyString(obj.getNomeUsuarioAmigoFit())) {
                throw new ConsistirException("Integração Amigo Fit está habilitada na aba Integrações CRM. O campo \"Usuario\" deve ser informado.");
            }
            if(UteisValidacao.emptyString(obj.getSenhaUsuarioAmigoFit())) {
                throw new ConsistirException("Integração Amigo Fit está habilitada na aba Integrações CRM. O campo \"Senha\" deve ser informado.");
            }
        }

        if (!obj.isAtiva() && obj.getTransferida()) {
            if (UteisValidacao.emptyString(obj.getNovaChaveTransferencia())) {
                throw new ConsistirException("O campo 'Transferida de banco' está marcado, portanto o campo \"Nova chave\" deve ser informado.");
            }

            if (UteisValidacao.emptyNumber(obj.getNovoCodigoTransferencia())) {
                throw new ConsistirException("O campo 'Transferida de banco' está marcado, portanto o campo \"Nova código\" deve ser informado.");
            }
        }

        if (obj.isCobrarMultaJurosAsaas()) {
            if (!UteisValidacao.emptyNumber(obj.getValorJurosAsaas()) && obj.getValorJurosAsaas() > 0.33) {
                //limite do asaas é de 10% ao mês, calculado da 0,33 ao dia
                throw new ConsistirException("O valor máximo de juros do Asaas é de 0,33% ao dia.");
            }
        }
    }

    public static void validarDadosF360(EmpresaVO obj) throws ConsistirException{
        if(UteisValidacao.emptyString(obj.getIntegracaoF360FtpServer())) {
            throw new ConsistirException("Integracao F360 está habilitada na aba Integrações. O campo \"FTP Servidor\" deve ser informado.");
        }
        if(UteisValidacao.emptyNumber(obj.getIntegracaoF360FtpPort())) {
            throw new ConsistirException("Integracao F360 está habilitada na aba Integrações. O campo \"FTP Porta\" deve ser informado.");
        }
        if(UteisValidacao.emptyString(obj.getIntegracaoF360User())) {
            throw new ConsistirException("Integracao F360 está habilitada na aba Integrações. O campo \"Usuario\" deve ser informado.");
        }
    }

    public String getCidade_Apresentar() {
        return getCidade().getNome();
    }

    public String getCidade_ApresentarSemAcentuacao() {
        if (getCidade().getNome() == null) {
            nome = "";
        }
        return (Uteis.retirarAcentuacao(getCidade().getNome()));
    }

    public void realizarUpperCaseDados() {
        super.realizarUpperCaseDados();
    }

    public void inicializarDados() {
        super.inicializarDados();
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ContaCorrenteEmpresaVO</code> ao List
     * <code>contaCorrenteEmpresaVOs</code>. Utiliza o atributo padrão de
     * consulta da classe
     * <code>ContaCorrenteEmpresa</code> - getContaCorrente().getCodigo() - como
     * identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ContaCorrenteEmpresaVO</code> que será
     *            adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjContaCorrenteEmpresaVOs(ContaCorrenteEmpresaVO obj) throws Exception {
        ContaCorrenteEmpresaVO.validarDados(obj);
        int index = 0;
        Iterator i = getContaCorrenteEmpresaVOs().iterator();
        while (i.hasNext()) {
            ContaCorrenteEmpresaVO objExistente = (ContaCorrenteEmpresaVO) i.next();
            if (objExistente.getContaCorrente().getCodigo().equals(obj.getContaCorrente().getCodigo())) {
                getContaCorrenteEmpresaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getContaCorrenteEmpresaVOs().add(obj);
    }

    public void adicionarObjProdDevolvCancelEmpresaVOs(ProdutoDevolverCancelamentoEmpresaVO obj) throws Exception {
        if(getProdutoDevolverCancelamentoEmpresaVOS() == null){
            setProdutoDevolverCancelamentoEmpresaVOS(new ArrayList<>());
        }
        ProdutoDevolverCancelamentoEmpresaVO.validarDados(obj);
        int index = 0;
        Iterator i = getProdutoDevolverCancelamentoEmpresaVOS().iterator();
        while (i.hasNext()) {
            ProdutoDevolverCancelamentoEmpresaVO objExistente = (ProdutoDevolverCancelamentoEmpresaVO) i.next();
            if (objExistente.getProduto().equals(obj.getProduto())) {
                getProdutoDevolverCancelamentoEmpresaVOS().set(index, obj);
                return;
            }
            index++;
        }
        getProdutoDevolverCancelamentoEmpresaVOS().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>ContaCorrenteEmpresaVO</code> no List
     * <code>contaCorrenteEmpresaVOs</code>. Utiliza o atributo padrão de
     * consulta da classe
     * <code>ContaCorrenteEmpresa</code> - getContaCorrente().getCodigo() - como
     * identificador (key) do objeto no List.
     *
     * @param contaCorrente Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjContaCorrenteEmpresaVOs(Integer contaCorrente) throws Exception {
        int index = 0;
        Iterator i = getContaCorrenteEmpresaVOs().iterator();
        while (i.hasNext()) {
            ContaCorrenteEmpresaVO objExistente = (ContaCorrenteEmpresaVO) i.next();
            if (objExistente.getContaCorrente().getCodigo().equals(contaCorrente)) {
                getContaCorrenteEmpresaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public void excluirObjProdDevolvCancelEmpresaVOs(Integer produto) throws Exception {
        int index = 0;
        Iterator i = getProdutoDevolverCancelamentoEmpresaVOS().iterator();
        while (i.hasNext()) {
            ProdutoDevolverCancelamentoEmpresaVO objExistente = (ProdutoDevolverCancelamentoEmpresaVO) i.next();
            if (objExistente.getProduto().equals(produto)) {
                getProdutoDevolverCancelamentoEmpresaVOS().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>ContaCorrenteEmpresaVO</code> no List
     * <code>contaCorrenteEmpresaVOs</code>. Utiliza o atributo padrão de
     * consulta da classe
     * <code>ContaCorrenteEmpresa</code> - getContaCorrente().getCodigo() - como
     * identificador (key) do objeto no List.
     *
     * @param contaCorrente Parâmetro para localizar o objeto do List.
     */
    public ContaCorrenteEmpresaVO consultarObjContaCorrenteEmpresaVO(Integer contaCorrente) throws Exception {
        Iterator i = getContaCorrenteEmpresaVOs().iterator();
        while (i.hasNext()) {
            ContaCorrenteEmpresaVO objExistente = (ContaCorrenteEmpresaVO) i.next();
            if (objExistente.getContaCorrente().getCodigo().equals(contaCorrente)) {
                return objExistente;
            }
        }
        return null;
    }

    public QuestionarioVO getQuestionarioReMatricula() {
        if (questionarioReMatricula == null) {
            questionarioReMatricula = new QuestionarioVO();
        }
        return (questionarioReMatricula);
    }

    public void setQuestionarioReMatricula(QuestionarioVO obj) {
        this.questionarioReMatricula = obj;
    }

    public QuestionarioVO getQuestionarioRetorno() {
        if (questionarioRetorno == null) {
            questionarioRetorno = new QuestionarioVO();
        }
        return (questionarioRetorno);
    }

    public void setQuestionarioRetorno(QuestionarioVO obj) {
        this.questionarioRetorno = obj;
    }

    public QuestionarioVO getQuestionarioPrimeiraVisita() {
        if (questionarioPrimeiraVisita == null) {
            questionarioPrimeiraVisita = new QuestionarioVO();
        }
        return (questionarioPrimeiraVisita);
    }

    public void setQuestionarioPrimeiraVisita(QuestionarioVO obj) {
        this.questionarioPrimeiraVisita = obj;
    }

    public Boolean getPermiteContratosConcomintante() {
        if (permiteContratosConcomintante == null) {
            permiteContratosConcomintante = false;
        }
        return (permiteContratosConcomintante);
    }

    public void setPermiteContratosConcomintante(Boolean permiteContratosConcomintante) {
        this.permiteContratosConcomintante = permiteContratosConcomintante;
    }

    public Boolean isPermiteContratosConcomintante() {
        if (permiteContratosConcomintante == null) {
            permiteContratosConcomintante = false;
        }
        return (permiteContratosConcomintante);
    }

    public Boolean getPermiteSituacaoAtestadoContrato() {
        if (permiteSituacaoAtestadoContrato == null) {
            permiteSituacaoAtestadoContrato = false;
        }
        return (permiteSituacaoAtestadoContrato);
    }

    public void setPermiteSituacaoAtestadoContrato(Boolean permiteSituacaoAtestadoContrato) {
        this.permiteSituacaoAtestadoContrato = permiteSituacaoAtestadoContrato;
    }

    public Boolean isPermiteSituacaoAtestadoContrato() {
        if (permiteSituacaoAtestadoContrato == null) {
            permiteSituacaoAtestadoContrato = false;
        }
        return (permiteSituacaoAtestadoContrato);
    }

    public List<ContaCorrenteEmpresaVO> getContaCorrenteEmpresaVOs() {
        if (contaCorrenteEmpresaVOs == null) {
            contaCorrenteEmpresaVOs = new ArrayList<ContaCorrenteEmpresaVO>();
        }
        return (contaCorrenteEmpresaVOs);
    }

    public void setContaCorrenteEmpresaVOs(List contaCorrenteEmpresaVOs) {
        this.contaCorrenteEmpresaVOs = contaCorrenteEmpresaVOs;
    }

    public List<ProdutoDevolverCancelamentoEmpresaVO> getProdutoDevolverCancelamentoEmpresaVOS() {
        if (produtoDevolverCancelamentoEmpresaVOS == null) {
            produtoDevolverCancelamentoEmpresaVOS = new ArrayList<>();
        }
        return produtoDevolverCancelamentoEmpresaVOS;
    }

    public void setProdutoDevolverCancelamentoEmpresaVOS(List<ProdutoDevolverCancelamentoEmpresaVO> produtoDevolverCancelamentoEmpresaVOS) {
        this.produtoDevolverCancelamentoEmpresaVOS = produtoDevolverCancelamentoEmpresaVOS;
    }

    public Double getJuroParcela() {
        if (juroParcela == null) {
            juroParcela = 0.0;
        }
        return juroParcela;
    }

    public void setJuroParcela(Double juroParcela) {
        this.juroParcela = juroParcela;
    }

    public Double getMulta() {
        if (multa == null) {
            multa = 0.0;
        }
        return multa;
    }

    public void setMulta(Double multa) {
        this.multa = multa;
    }

    public String getMascaraMatricula() {
        if (mascaraMatricula == null) {
            mascaraMatricula = "";
        }
        return mascaraMatricula;
    }

    public void setMascaraMatricula(String mascaraMatricula) {
        this.mascaraMatricula = mascaraMatricula;
    }

    public Integer getNrDiasVigenteQuestionarioRematricula() {
        if (nrDiasVigenteQuestionarioRematricula == null) {
            nrDiasVigenteQuestionarioRematricula = 0;
        }
        return nrDiasVigenteQuestionarioRematricula;
    }

    public void setNrDiasVigenteQuestionarioRematricula(Integer nrDiasVigenteQuestionarioRematricula) {
        this.nrDiasVigenteQuestionarioRematricula = nrDiasVigenteQuestionarioRematricula;
    }

    public Integer getNrDiasVigenteQuestionarioRetorno() {
        if (nrDiasVigenteQuestionarioRetorno == null) {
            nrDiasVigenteQuestionarioRetorno = 0;
        }
        return nrDiasVigenteQuestionarioRetorno;
    }

    public void setNrDiasVigenteQuestionarioRetorno(Integer nrDiasVigenteQuestionarioRetorno) {
        this.nrDiasVigenteQuestionarioRetorno = nrDiasVigenteQuestionarioRetorno;
    }

    public Integer getNrDiasVigenteQuestionarioVista() {
        if (nrDiasVigenteQuestionarioVista == null) {
            nrDiasVigenteQuestionarioVista = 0;
        }
        return nrDiasVigenteQuestionarioVista;
    }

    public void setNrDiasVigenteQuestionarioVista(Integer nrDiasVigenteQuestionarioVista) {
        this.nrDiasVigenteQuestionarioVista = nrDiasVigenteQuestionarioVista;
    }

    public Integer getCarenciaRenovacao() {
        if (carenciaRenovacao == null) {
            carenciaRenovacao = 0;
        }
        return carenciaRenovacao;
    }

    public void setCarenciaRenovacao(Integer carenciaRenovacao) {
        this.carenciaRenovacao = carenciaRenovacao;
    }

    public Integer getNrDiasAvencer() {
        if (nrDiasAvencer == null) {
            nrDiasAvencer = 0;
        }
        return nrDiasAvencer;
    }

    public void setNrDiasAvencer(Integer nrDiasAvencer) {
        this.nrDiasAvencer = nrDiasAvencer;
    }

    public Integer getSomaDv() {
        if (somaDv == null) {
            somaDv = 0;
        }
        return somaDv;
    }

    public void setSomaDv(Integer somaDv) {
        this.somaDv = somaDv;
    }

    public Integer getToleranciaPagamento() {
        if (toleranciaPagamento == null) {
            toleranciaPagamento = 0;
        }
        return toleranciaPagamento;
    }

    public void setToleranciaPagamento(Integer toleranciaPagamento) {
        this.toleranciaPagamento = toleranciaPagamento;
    }

    public Integer getToleranciaOcupacaoTurma() {
        return toleranciaOcupacaoTurma;
    }

    public void setToleranciaOcupacaoTurma(Integer toleranciaOcupacaoTurma) {
        this.toleranciaOcupacaoTurma = toleranciaOcupacaoTurma;
    }

    public Integer getQtdFaltaInicioPeso2() {
        if (qtdFaltaInicioPeso2 == null) {
            qtdFaltaInicioPeso2 = 0;
        }
        return qtdFaltaInicioPeso2;
    }

    public void setQtdFaltaInicioPeso2(Integer qtdFaltaInicioPeso2) {
        this.qtdFaltaInicioPeso2 = qtdFaltaInicioPeso2;
    }

    public Integer getQtdFaltaPeso1() {
        if (qtdFaltaPeso1 == null) {
            qtdFaltaPeso1 = 0;
        }
        return qtdFaltaPeso1;
    }

    public void setQtdFaltaPeso1(Integer qtdFaltaPeso1) {
        this.qtdFaltaPeso1 = qtdFaltaPeso1;
    }

    public Integer getQtdFaltaPeso3() {
        if (qtdFaltaPeso3 == null) {
            qtdFaltaPeso3 = 0;
        }
        return qtdFaltaPeso3;
    }

    public void setQtdFaltaPeso3(Integer qtdFaltaPeso3) {
        this.qtdFaltaPeso3 = qtdFaltaPeso3;
    }

    public Integer getQtdFaltaTerminoPeso2() {
        if (qtdFaltaTerminoPeso2 == null) {
            qtdFaltaTerminoPeso2 = 0;
        }
        return qtdFaltaTerminoPeso2;
    }

    public void setQtdFaltaTerminoPeso2(Integer qtdFaltaTerminoPeso2) {
        this.qtdFaltaTerminoPeso2 = qtdFaltaTerminoPeso2;
    }

    public int getNrDiasProrata() {
        return nrDiasProrata;
    }

    public void setNrDiasProrata(int nrDiasProrata) {
        this.nrDiasProrata = nrDiasProrata;
    }

    public int getToleranciaDiasContratoVencido() {
        return toleranciaDiasContratoVencido;
    }

    public void setToleranciaDiasContratoVencido(int toleranciaDiasContratoVencido) {
        this.toleranciaDiasContratoVencido = toleranciaDiasContratoVencido;
    }

    @Override
    public String toString() {
        return this.getNome();
    }

    public String getTimeZoneDefault() {
        return timeZoneDefault;
    }

    public void setTimeZoneDefault(String timeZoneDefault) {
        this.timeZoneDefault = timeZoneDefault;
    }

    public Boolean getEmpresaEscolhida() {
        if (empresaEscolhida == null) {
            empresaEscolhida = Boolean.FALSE;
        }
        return empresaEscolhida;
    }

    public void setEmpresaEscolhida(Boolean empresaEscolhida) {
        this.empresaEscolhida = empresaEscolhida;
    }

    public String getUrlRecorrencia() {
        return urlRecorrencia;
    }

    public void setUrlRecorrencia(String urlRecorrencia) {
        this.urlRecorrencia = urlRecorrencia;
    }

    public int getNrDiasCompensacao() {
        return nrDiasCompensacao;
    }

    public void setNrDiasCompensacao(int nrDiasCompensacao) {
        this.nrDiasCompensacao = nrDiasCompensacao;
    }

    public String getServiceSenha() {
        return serviceSenha;
    }

    public void setServiceSenha(String serviceSenha) {
        this.serviceSenha = serviceSenha;
    }

    public String getServiceUsuario() {
        return serviceUsuario;
    }

    public void setServiceUsuario(String serviceUsuario) {
        this.serviceUsuario = serviceUsuario;
    }

    public boolean isBloquearAcessoSeParcelaAberta() {
        return bloquearAcessoSeParcelaAberta;
    }

    public void setBloquearAcessoSeParcelaAberta(boolean bloquearAcessoSeParcelaAberta) {
        this.bloquearAcessoSeParcelaAberta = bloquearAcessoSeParcelaAberta;
    }

    public ColaboradorVO getConsultorVendaAvulsa() {
        if (consultorVendaAvulsa == null) {
            consultorVendaAvulsa = new ColaboradorVO();
        }
        return consultorVendaAvulsa;
    }

    public void setConsultorVendaAvulsa(ColaboradorVO consultorVendaAvulsa) {
        this.consultorVendaAvulsa = consultorVendaAvulsa;
    }

    public boolean isPermiteHorariosConcorrentesParaProfessor() {
        return permiteHorariosConcorrentesParaProfessor;
    }

    public void setPermiteHorariosConcorrentesParaProfessor(boolean permiteHorariosConcorrentesParaProfessor) {
        this.permiteHorariosConcorrentesParaProfessor = permiteHorariosConcorrentesParaProfessor;
    }

    public boolean isProfessorEmAmbientesDiferentesMesmoHorario() {
        return professorEmAmbientesDiferentesMesmoHorario;
    }

    public void setProfessorEmAmbientesDiferentesMesmoHorario(boolean professorEmAmbientesDiferentesMesmoHorario) {
        this.professorEmAmbientesDiferentesMesmoHorario = professorEmAmbientesDiferentesMesmoHorario;
    }

    public boolean isMostrarCnpj() {
        return mostrarCnpj;
    }

    public void setMostrarCnpj(boolean mostrarCnpj) {
        this.mostrarCnpj = mostrarCnpj;
    }

    public boolean isApresentarBotoesNFSe() {
        TipoRelatorioDF tipoGestaoNFSe = TipoRelatorioDF.getTipoRelatorioDF(getTipoGestaoNFSe());
        return getUsarNFSe() && tipoGestaoNFSe != null && (tipoGestaoNFSe.equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA) || tipoGestaoNFSe.equals(TipoRelatorioDF.RECEITA));
    }

    public boolean isApresentarBotoesFaturamentoNFSe() {
        TipoRelatorioDF tipoGestaoNFSe = TipoRelatorioDF.getTipoRelatorioDF(getTipoGestaoNFSe());
        return getUsarNFSe() && tipoGestaoNFSe != null && tipoGestaoNFSe.equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA);
    }

    public boolean getUsarNFSe() {
        return usarNFSe;
    }

    public void setUsarNFSe(boolean usarNFSe) {
        this.usarNFSe = usarNFSe;
    }

    public String getChaveNFSe() {
        return chaveNFSe;
    }

    public void setChaveNFSe(String chaveNFSe) {
        this.chaveNFSe = chaveNFSe;
    }

    public String getTokenSMSApresentar() {
        return Uteis.mascararDado(this.getTokenSMS(), 8);
    }

    public String getTokenSMS() {
        return tokenSMS;
    }

    public void setTokenSMS(String tokenSMS) {
        this.tokenSMS = tokenSMS;
    }

    public String getTokenSMSShortCodeApresentar() {
        return Uteis.mascararDado(this.getTokenSMSShortCode(), 8);
    }

    public String getTokenSMSShortCode() {
        return tokenSMSShortCode;
    }

    public void setTokenSMSShortCode(String tokenSMSShortCode) {
        this.tokenSMSShortCode = tokenSMSShortCode;
    }

    public JSONObject toJSON() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getSuperclass().getDeclaredFields();
        try {
            for (Field field : fields) {
                o.put(field.getName(), field.get(this));
            }
        } catch (Exception ignored) {
        }
        return o;
    }

    public CidadeVO getCidade() {
        if (cidade == null) {
            cidade = new CidadeVO();
        }
        return (cidade);
    }

    public void setCidade(CidadeVO obj) {
        this.cidade = obj;
    }

    public String getNome() {
        return super.getNome();
    }

    public String getNomeWeHelp() {
        return Uteis.retirarAcentuacao(super.getNome());
    }

    public String getCidadeNome() {
        return CidadeNome;
    }

    public void setCidadeNome(String cidadeNome) {
        CidadeNome = cidadeNome;
    }

    public String getEstadoSigla() {
        return EstadoSigla;
    }

    public void setEstadoSigla(String estadoSigla) {
        EstadoSigla = estadoSigla;
    }

    public int getNrDiasChequeAVista() {
        return nrDiasChequeAVista;
    }

    public void setNrDiasChequeAVista(int nrDiasChequeAVista) {
        this.nrDiasChequeAVista = nrDiasChequeAVista;
    }

    public boolean isMostrarModalidade() {
        return mostrarModalidade;
    }

    public void setMostrarModalidade(boolean mostrarModalidade) {
        this.mostrarModalidade = mostrarModalidade;
    }

    public boolean isBvObrigatorio() {
        return bvObrigatorio;
    }

    public void setBvObrigatorio(boolean bvObrigatorio) {
        this.bvObrigatorio = bvObrigatorio;
    }

    public int getTempoAposFaltaReposicao() {
        return tempoAposFaltaReposicao;
    }

    public void setTempoAposFaltaReposicao(int tempoAposFaltaReposicao) {
        this.tempoAposFaltaReposicao = tempoAposFaltaReposicao;
    }

    public boolean isEnviarNFSeAutomatico() {
        return enviarNFSeAutomatico;
    }

    public void setEnviarNFSeAutomatico(boolean enviarNFSeAutomatico) {
        this.enviarNFSeAutomatico = enviarNFSeAutomatico;
    }

    public boolean isComissaoMatriculaRematricula() {
        return comissaoMatriculaRematricula;
    }

    public void setComissaoMatriculaRematricula(boolean comissaoMatriculaRematricula) {
        this.comissaoMatriculaRematricula = comissaoMatriculaRematricula;
    }

    public QuestionarioVO getQuestionarioPrimeiraCompra() {
        if (questionarioPrimeiraCompra == null) {
            questionarioPrimeiraCompra = new QuestionarioVO();
        }
        return questionarioPrimeiraCompra;
    }

    public void setQuestionarioPrimeiraCompra(QuestionarioVO questionarioPrimeiraCompra) {
        this.questionarioPrimeiraCompra = questionarioPrimeiraCompra;
    }

    public Integer getNrDiasVigenteQuestionarioPrimeiraCompra() {
        if (nrDiasVigenteQuestionarioPrimeiraCompra == null) {
            nrDiasVigenteQuestionarioPrimeiraCompra = 0;
        }
        return nrDiasVigenteQuestionarioPrimeiraCompra;
    }

    public void setNrDiasVigenteQuestionarioPrimeiraCompra(Integer nrDiasVigenteQuestionarioPrimeiraCompra) {
        this.nrDiasVigenteQuestionarioPrimeiraCompra = nrDiasVigenteQuestionarioPrimeiraCompra;
    }

    public QuestionarioVO getQuestionarioRetornoCompra() {
        if (questionarioRetornoCompra == null) {
            questionarioRetornoCompra = new QuestionarioVO();
        }
        return questionarioRetornoCompra;
    }

    public void setQuestionarioRetornoCompra(QuestionarioVO questionarioRetornoCompra) {
        this.questionarioRetornoCompra = questionarioRetornoCompra;
    }

    public Integer getNrDiasVigenteQuestionarioRetornoCompra() {
        if (nrDiasVigenteQuestionarioRetornoCompra == null) {
            nrDiasVigenteQuestionarioRetornoCompra = 0;
        }
        return nrDiasVigenteQuestionarioRetornoCompra;
    }

    public void setNrDiasVigenteQuestionarioRetornoCompra(Integer nrDiasVigenteQuestionarioRetornoCompra) {
        this.nrDiasVigenteQuestionarioRetornoCompra = nrDiasVigenteQuestionarioRetornoCompra;
    }

    public boolean isUsarNFSePorPagamento() {
        return usarNFSePorPagamento;
    }

    public void setUsarNFSePorPagamento(boolean usarNFSePorPagamento) {
        this.usarNFSePorPagamento = usarNFSePorPagamento;
    }

    public int getQtdDiasCobrarRematricula() {
        return qtdDiasCobrarRematricula;
    }

    public void setQtdDiasCobrarRematricula(int qtdDiasCobrarRematricula) {
        this.qtdDiasCobrarRematricula = qtdDiasCobrarRematricula;
    }

    public int getQtdVias() {
        return qtdVias;
    }

    public void setQtdVias(int qtdVias) {
        this.qtdVias = qtdVias;
    }

    public boolean isQuebrarPaginaRecibo() {
        return quebrarPaginaRecibo;
    }

    public void setQuebrarPaginaRecibo(boolean quebrarPaginaRecibo) {
        this.quebrarPaginaRecibo = quebrarPaginaRecibo;
    }

    public boolean isDetalharPeriodoProduto() {
        return detalharPeriodoProduto;
    }

    public void setDetalharPeriodoProduto(boolean detalharPeriodoProduto) {
        this.detalharPeriodoProduto = detalharPeriodoProduto;
    }

    public boolean isDetalharParcelas() {
        return detalharParcelas;
    }

    public void setDetalharParcelas(boolean detalharParcelas) {
        this.detalharParcelas = detalharParcelas;
    }

    public boolean isDetalharPagamentos() {
        return detalharPagamentos;
    }

    public void setDetalharPagamentos(boolean detalharPagamentos) {
        this.detalharPagamentos = detalharPagamentos;
    }

    public boolean isDetalharDescontos() {
        return detalharDescontos;
    }

    public void setDetalharDescontos(boolean detalharDescontos) {
        this.detalharDescontos = detalharDescontos;
    }

    public boolean isDetalharNiveisModalidades() {
        return detalharNiveisModalidades;
    }

    public void setDetalharNiveisModalidades(boolean detalharNiveisModalidades) {
        this.detalharNiveisModalidades = detalharNiveisModalidades;
    }

    public boolean isExigirAssinaturaDigitalResponsavelFinanceiro() {
        return exigirAssinaturaDigitalResponsavelFinanceiro;
    }

    public void setExigirAssinaturaDigitalResponsavelFinanceiro(boolean exigirAssinaturaDigitalResponsavelFinanceiro) {
        this.exigirAssinaturaDigitalResponsavelFinanceiro = exigirAssinaturaDigitalResponsavelFinanceiro;
    }

    public boolean isApresentarAssinaturas() {
        return apresentarAssinaturas;
    }

    public void setApresentarAssinaturas(boolean apresentarAssinaturas) {
        this.apresentarAssinaturas = apresentarAssinaturas;
    }

    public int getTipoGestaoNFSe() {
        return tipoGestaoNFSe;
    }

    public TipoRelatorioDF getTipoGestaoNFSeEnum() {
        return TipoRelatorioDF.getTipoRelatorioDF(getTipoGestaoNFSe());
    }

    public void setTipoGestaoNFSe(int tipoGestaoNFSe) {
        this.tipoGestaoNFSe = tipoGestaoNFSe;
    }

    public boolean isRetrocederValorMensalPlanoCancelamento() {
        return retrocederValorMensalPlanoCancelamento;
    }

    public void setRetrocederValorMensalPlanoCancelamento(boolean retrocederValorMensalPlanoCancelamento) {
        this.retrocederValorMensalPlanoCancelamento = retrocederValorMensalPlanoCancelamento;
    }

    public boolean isUsarGestaoCreditosPersonal() {
        return usarGestaoCreditosPersonal;
    }

    public void setUsarGestaoCreditosPersonal(boolean usarGestaoCreditosPersonal) {
        this.usarGestaoCreditosPersonal = usarGestaoCreditosPersonal;
    }

    public boolean isFecharNegociacaoSemAutorizacaoDCC() {
        return fecharNegociacaoSemAutorizacaoDCC;
    }

    public void setFecharNegociacaoSemAutorizacaoDCC(boolean fecharNegociacaoSemAutorizacaoDCC) {
        this.fecharNegociacaoSemAutorizacaoDCC = fecharNegociacaoSemAutorizacaoDCC;
    }

    public String getObservacaoRecibo() {
        return observacaoRecibo;
    }

    public void setObservacaoRecibo(String observacaoRecibo) {
        this.observacaoRecibo = observacaoRecibo;
    }

    public int getNrDiasDesistenteRemoverVinculoTreino() {
        return nrDiasDesistenteRemoverVinculoTreino;
    }

    public void setNrDiasDesistenteRemoverVinculoTreino(int nrDiasDesistenteRemoverVinculoTreino) {
        this.nrDiasDesistenteRemoverVinculoTreino = nrDiasDesistenteRemoverVinculoTreino;
    }

    public boolean isLiberarPersonalComTaxaEmAberto() {
        return liberarPersonalComTaxaEmAberto;
    }

    public void setLiberarPersonalComTaxaEmAberto(boolean liberarPersonalComTaxaEmAberto) {
        this.liberarPersonalComTaxaEmAberto = liberarPersonalComTaxaEmAberto;
    }

    public boolean isRemoverVinculosAposDesistencia() {
        return removerVinculosAposDesistencia;
    }

    public void setRemoverVinculosAposDesistencia(boolean removerVinculosAposDesistencia) {
        this.removerVinculosAposDesistencia = removerVinculosAposDesistencia;
    }

    public boolean isReciboParaImpressoraTermica() {
        return reciboParaImpressoraTermica;
    }

    public void setReciboParaImpressoraTermica(boolean reciboParaImpressoraTermica) {
        this.reciboParaImpressoraTermica = reciboParaImpressoraTermica;
    }

    public boolean isUsarManutencaoModalidadeComissao() {
        return usarManutencaoModalidadeComissao;
    }

    public void setUsarManutencaoModalidadeComissao(boolean usarManutencaoModalidadeComissao) {
        this.usarManutencaoModalidadeComissao = usarManutencaoModalidadeComissao;
    }

    public boolean isDevolucaoEntraNoCaixa() {
        return devolucaoEntraNoCaixa;
    }

    public void setDevolucaoEntraNoCaixa(boolean devolucaoEntraNoCaixa) {
        this.devolucaoEntraNoCaixa = devolucaoEntraNoCaixa;
    }

    public int getCreditoDCC() {
        return creditoDCC;
    }

    public void setCreditoDCC(int creditoDCC) {
        this.creditoDCC = creditoDCC;
    }

    public void preencherTipoCancelamento() {
        if (this.cancelamentoAntecipado && !this.cancelamentoObrigatoriedadePagamento) {
            tipoCancelamento = TipoCancelamentoEnum.CANCELAMENTO_ANTECEDENCIA;
        } else if (this.cancelamentoObrigatoriedadePagamento) {
            tipoCancelamento = TipoCancelamentoEnum.CANCELAMENTO_OBRIGATORIEDADE_PAGAMETO;
        } else if (this.cancelamentoAvaliandoParcelas) {
            tipoCancelamento = TipoCancelamentoEnum.CANCELAMENTO_AVALIANDO_PARCELAS;
        } else {
            tipoCancelamento = TipoCancelamentoEnum.CANCELAMENTO_PADRAO;
        }
    }
    public TipoCancelamentoEnum getTipoCancelamento() {
        preencherTipoCancelamento();
        return tipoCancelamento;
    }

    public void setTipoCancelamento(TipoCancelamentoEnum tipoCancelamento) {
        this.cancelamentoAntecipado = tipoCancelamento.equals(TipoCancelamentoEnum.CANCELAMENTO_ANTECEDENCIA) || tipoCancelamento.equals(TipoCancelamentoEnum.CANCELAMENTO_OBRIGATORIEDADE_PAGAMETO);
        this.cancelamentoObrigatoriedadePagamento = tipoCancelamento.equals(TipoCancelamentoEnum.CANCELAMENTO_OBRIGATORIEDADE_PAGAMETO);
        this.cancelamentoAvaliandoParcelas = tipoCancelamento.equals(TipoCancelamentoEnum.CANCELAMENTO_AVALIANDO_PARCELAS);

        this.tipoCancelamento = tipoCancelamento;
    }

    public UsoArredondamentoEnum getArredondamento() {
        if (arredondamento == null) {
            arredondamento = UsoArredondamentoEnum.NAO_USA;
        }
        return arredondamento;
    }

    public void setArredondamento(UsoArredondamentoEnum arredondamento) {
        this.arredondamento = arredondamento;
    }

    public int getToleranciaProrata() {
        return toleranciaProrata;
    }

    public void setToleranciaProrata(int toleranciaProrata) {
        this.toleranciaProrata = toleranciaProrata;
    }

    public ConfiguracaoGestaoPersonalVO getConfigsPersonal() {
        if (configsPersonal == null) {
            configsPersonal = new ConfiguracaoGestaoPersonalVO();
        }
        return configsPersonal;
    }

    public void setConfigsPersonal(ConfiguracaoGestaoPersonalVO configsPersonal) {
        this.configsPersonal = configsPersonal;
    }

    public boolean isPermiteReposicaoEmTurmasDiferentes() {
        return permiteReposicaoEmTurmasDiferentes;
    }

    public void setPermiteReposicaoEmTurmasDiferentes(boolean permiteReposicaoEmTurmasDiferentes) {
        this.permiteReposicaoEmTurmasDiferentes = permiteReposicaoEmTurmasDiferentes;
    }

    public boolean isBloquearRenovacaoAutomaticaPlanosForaDaVigencia() {
        return bloquearRenovacaoAutomaticaPlanosForaDaVigencia;
    }

    public void setBloquearRenovacaoAutomaticaPlanosForaDaVigencia(boolean bloquearRenovacaoAutomaticaPlanosForaDaVigencia) {
        this.bloquearRenovacaoAutomaticaPlanosForaDaVigencia = bloquearRenovacaoAutomaticaPlanosForaDaVigencia;
    }

    public boolean isBloquearRenovacaoAutomaticaPlanosForaCategoriaCliente() {
        return bloquearRenovacaoAutomaticaPlanosForaCategoriaCliente;
    }

    public void setBloquearRenovacaoAutomaticaPlanosForaCategoriaCliente(boolean bloquearRenovacaoAutomaticaPlanosForaCategoriaCliente) {
        this.bloquearRenovacaoAutomaticaPlanosForaCategoriaCliente = bloquearRenovacaoAutomaticaPlanosForaCategoriaCliente;
    }

    public boolean isPermiteRenovarContratosEmTurmasLotadas() {
        return permiteRenovarContratosEmTurmasLotadas;
    }

    public void setPermiteRenovarContratosEmTurmasLotadas(boolean permiteRenovarContratosEmTurmasLotadas) {
        this.permiteRenovarContratosEmTurmasLotadas = permiteRenovarContratosEmTurmasLotadas;
    }

    public boolean isForcarMinimoVencimento2parcela() {
        return forcarMinimoVencimento2parcela;
    }

    public void setForcarMinimoVencimento2parcela(boolean forcarMinimoVencimento2parcela) {
        this.forcarMinimoVencimento2parcela = forcarMinimoVencimento2parcela;
    }

    public boolean isFeitoUploadAlgumaFoto() {
        return feitoUploadAlgumaFoto;
    }

    public void setFeitoUploadAlgumaFoto(boolean feitoUploadAlgumaFoto) {
        this.feitoUploadAlgumaFoto = feitoUploadAlgumaFoto;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public Date getDataExpiracaoCreditoDCC() {
        return dataExpiracaoCreditoDCC;
    }

    public void setDataExpiracaoCreditoDCC(Date dataExpiracaoCreditoDCC) {
        this.dataExpiracaoCreditoDCC = dataExpiracaoCreditoDCC;
    }

    public Date getDataExpiracao() {
        return dataExpiracao;
    }

    public void setDataExpiracao(Date dataExpiracao) {
        this.dataExpiracao = dataExpiracao;
    }

    public Date getDataExpiracaoNFe() {
        return dataExpiracaoNFe;
    }

    public void setDataExpiracaoNFe(Date dataExpiracaoNFe) {
        this.dataExpiracaoNFe = dataExpiracaoNFe;
    }

    public Date getDataExpiracaoVendasOnline() {
        return dataExpiracaoVendasOnline;
    }

    public void setDataExpiracaoVendasOnline(Date dataExpiracaoVendasOnline) {
        this.dataExpiracaoVendasOnline = dataExpiracaoVendasOnline;
    }

    public Date getDataExpiracaoApp() {
        return dataExpiracaoApp;
    }

    public void setDataExpiracaoApp(Date dataExpiracaoApp) {
        this.dataExpiracaoApp = dataExpiracaoApp;
    }

    public Date getDataExpiracaoOutros() {
        return dataExpiracaoOutros;
    }

    public void setDataExpiracaoOutros(Date dataExpiracaoOutros) {
        this.dataExpiracaoOutros = dataExpiracaoOutros;
    }

    public String getMotivoExpiracaoOutros() {
        return motivoExpiracaoOutros;
    }

    public void setMotivoExpiracaoOutros(String motivoExpiracaoOutros) {
        this.motivoExpiracaoOutros = motivoExpiracaoOutros;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public boolean isAcessoChamada() {
        return acessoChamada;
    }

    public void setAcessoChamada(boolean acessoChamada) {
        this.acessoChamada = acessoChamada;
    }

    public LocalAcessoVO getLocalAcessoChamada() {
        if (localAcessoChamada == null) {
            localAcessoChamada = new LocalAcessoVO();
        }
        return localAcessoChamada;
    }

    public void setLocalAcessoChamada(LocalAcessoVO obj) {
        this.localAcessoChamada = obj;
    }

    public ColetorVO getColetorChamada() {
        if (coletorChamada == null) {
            coletorChamada = new ColetorVO();
        }
        return coletorChamada;
    }

    public void setColetorChamada(ColetorVO obj) {
        this.coletorChamada = obj;
    }

    public Integer getDiasRenovacaoAutomaticaAntecipada() {
        return diasRenovacaoAutomaticaAntecipada;
    }

    public void setDiasRenovacaoAutomaticaAntecipada(Integer diasRenovacaoAutomaticaAntecipada) {
        this.diasRenovacaoAutomaticaAntecipada = diasRenovacaoAutomaticaAntecipada;
    }

    public boolean isMostrarMensagemValoresRodape() {
        return mostrarMensagemValoresRodape;
    }

    public void setMostrarMensagemValoresRodape(boolean mostrarMensagemValoresRodape) {
        this.mostrarMensagemValoresRodape = mostrarMensagemValoresRodape;
    }

    public Boolean getCobrarAutomaticamenteMultaJuros() {
        if (cobrarAutomaticamenteMultaJuros == null) {
            cobrarAutomaticamenteMultaJuros = false;
        }
        return cobrarAutomaticamenteMultaJuros;
    }

    public void setCobrarAutomaticamenteMultaJuros(Boolean cobrarAutomaticamenteMultaJuros) {
        this.cobrarAutomaticamenteMultaJuros = cobrarAutomaticamenteMultaJuros;
    }

    public Double getJurosCobrancaAutomatica() {
        if (jurosCobrancaAutomatica == null) {
            jurosCobrancaAutomatica = 0.0;
        }
        return jurosCobrancaAutomatica;
    }

    public void setJurosCobrancaAutomatica(Double jurosCobrancaAutomatica) {
        this.jurosCobrancaAutomatica = jurosCobrancaAutomatica;
    }

    public boolean isUtilizarJurosValorAbsoluto() {
        return utilizarJurosValorAbsoluto;
    }

    public void setUtilizarJurosValorAbsoluto(boolean utilizarJurosValorAbsoluto) {
        this.utilizarJurosValorAbsoluto = utilizarJurosValorAbsoluto;
    }

    public Double getMultaCobrancaAutomatica() {
        if (multaCobrancaAutomatica == null) {
            multaCobrancaAutomatica = 0.0;
        }
        return multaCobrancaAutomatica;
    }

    public void setMultaCobrancaAutomatica(Double multaCobrancaAutomatica) {
        this.multaCobrancaAutomatica = multaCobrancaAutomatica;
    }

    public boolean isUtilizarMultaValorAbsoluto() {
        return utilizarMultaValorAbsoluto;
    }

    public void setUtilizarMultaValorAbsoluto(boolean utilizarMultaValorAbsoluto) {
        this.utilizarMultaValorAbsoluto = utilizarMultaValorAbsoluto;
    }

    public String getMultaCobrancaAutomaticaApresentar() {
        return Formatador.formatarValorPercentual(getMultaCobrancaAutomatica() / 100);
    }


    public String getJurosCobrancaAutomaticaApresentar() {
        return Formatador.formatarValorPercentual(getJurosCobrancaAutomatica() / 100);
    }

    public Boolean getLiberarPersonalProfessorDebito() {
        if (liberarPersonalProfessorDebito == null) {
            liberarPersonalProfessorDebito = false;
        }
        return liberarPersonalProfessorDebito;
    }

    public void setLiberarPersonalProfessorDebito(Boolean liberarPersonalProfessorDebito) {
        this.liberarPersonalProfessorDebito = liberarPersonalProfessorDebito;
    }


    public ColaboradorVO getConsultorSite() {
        if (consultorSite == null) {
            consultorSite = new ColaboradorVO();
        }
        return consultorSite;
    }

    public void setConsultorSite(ColaboradorVO consultorSite) {
        this.consultorSite = consultorSite;
    }

    public boolean isCriarBvVendaSite() {
        return criarBvVendaSite;
    }

    public void setCriarBvVendaSite(boolean criarBvVendaSite) {
        this.criarBvVendaSite = criarBvVendaSite;
    }

    public Boolean getEmiteNFSEPorDataCompensacao() {
        if (emiteNFSEPorDataCompensacao == null) {
            emiteNFSEPorDataCompensacao = false;
        }
        return emiteNFSEPorDataCompensacao;
    }

    public void setEmiteNFSEPorDataCompensacao(Boolean emiteNFSEPorDataCompensacao) {
        this.emiteNFSEPorDataCompensacao = emiteNFSEPorDataCompensacao;
    }

    public boolean isPagarComissaoSeAtingirMetaFinanceira() {
        return pagarComissaoSeAtingirMetaFinanceira;
    }

    public void setPagarComissaoSeAtingirMetaFinanceira(boolean pagarComissaoSeAtingirMetaFinanceira) {
        this.pagarComissaoSeAtingirMetaFinanceira = pagarComissaoSeAtingirMetaFinanceira;
    }

    public boolean isPagarComissaoManutencaoModalidade() {
        return pagarComissaoManutencaoModalidade;
    }

    public void setPagarComissaoManutencaoModalidade(boolean pagarComissaoManutencaoModalidade) {
        this.pagarComissaoManutencaoModalidade = pagarComissaoManutencaoModalidade;
    }

    public boolean isPagarComissaoProdutos() {
        return pagarComissaoProdutos;
    }

    public void setPagarComissaoProdutos(boolean pagarComissaoProdutos) {
        this.pagarComissaoProdutos = pagarComissaoProdutos;
    }

    public Boolean getSomenteVendaProdutosComEstoque() {
        return somenteVendaProdutosComEstoque;
    }

    public void setSomenteVendaProdutosComEstoque(Boolean somenteVendaProdutosComEstoque) {
        this.somenteVendaProdutosComEstoque = somenteVendaProdutosComEstoque;
    }

    public boolean isPermiteAlterarDataEmissaoNFSe() {
        return permiteAlterarDataEmissaoNFSe;
    }

    public void setPermiteAlterarDataEmissaoNFSe(boolean permiteAlterarDataEmissaoNFSe) {
        this.permiteAlterarDataEmissaoNFSe = permiteAlterarDataEmissaoNFSe;
    }

    public Integer getMinutosAposUltimoAcessoDiminuirCredito() {
        return minutosAposUltimoAcessoDiminuirCredito;
    }

    public void setMinutosAposUltimoAcessoDiminuirCredito(Integer minutosAposUltimoAcessoDiminuirCredito) {
        this.minutosAposUltimoAcessoDiminuirCredito = minutosAposUltimoAcessoDiminuirCredito;
    }

    public ModeloMensagemVO getModeloMensagemVendasOnline() {
        if (modeloMensagemVendasOnline == null) {
            modeloMensagemVendasOnline = new ModeloMensagemVO();
        }
        return modeloMensagemVendasOnline;
    }

    public void setModeloMensagemVendasOnline(ModeloMensagemVO modeloMensagemVendasOnline) {
        this.modeloMensagemVendasOnline = modeloMensagemVendasOnline;
    }

    public boolean isCancelamentoObrigatoriedadePagamento() {
        return cancelamentoObrigatoriedadePagamento;
    }

    public void setCancelamentoObrigatoriedadePagamento(boolean cancelamentoObrigatoriedadePagamento) {
        this.cancelamentoObrigatoriedadePagamento = cancelamentoObrigatoriedadePagamento;
    }

    public boolean isCancelamentoAntecipado() {
        return cancelamentoAntecipado;
    }

    public boolean isCancelamentoObrigatoriedadePagamento(ContratoVO contrato) {
        return cancelamentoObrigatoriedadePagamento;
    }

    public boolean isCancelamentoAntecipado(ContratoVO contrato) {
        return cancelamentoAntecipado &&
                (getCancelamentoAntecipadoContratosDepoisDe() == null || Calendario.maior(contrato.getDataLancamento(), getCancelamentoAntecipadoContratosDepoisDe()));
    }

    public void setCancelamentoAntecipado(boolean cancelamentoAntecipado) {
        this.cancelamentoAntecipado = cancelamentoAntecipado;
    }

    public Integer getCancelamentoAntecipadoDias() {
        if (cancelamentoAntecipadoDias == null) {
            cancelamentoAntecipadoDias = 0;
        }
        return cancelamentoAntecipadoDias;
    }

    public void setCancelamentoAntecipadoDias(Integer cancelamentoAntecipadoDias) {
        this.cancelamentoAntecipadoDias = cancelamentoAntecipadoDias;
    }

    public Double getCancelamentoAntecipadoMulta() {
        if (cancelamentoAntecipadoMulta == null) {
            cancelamentoAntecipadoMulta = 0.0;
        }
        return cancelamentoAntecipadoMulta;
    }

    public void setCancelamentoAntecipadoMulta(Double cancelamentoAntecipadoMulta) {
        this.cancelamentoAntecipadoMulta = cancelamentoAntecipadoMulta;
    }

    public String getCancelamentoAntecipadoPlanos() {
        if (cancelamentoAntecipadoPlanos == null) {
            cancelamentoAntecipadoPlanos = "";
        }
        return cancelamentoAntecipadoPlanos.replaceAll(" ", "");
    }

    public void setCancelamentoAntecipadoPlanos(String cancelamentoAntecipadoPlanos) {
        this.cancelamentoAntecipadoPlanos = cancelamentoAntecipadoPlanos;
    }

    public Double getCancelamentoAntecipadoPlanosMulta() {
        if (cancelamentoAntecipadoPlanosMulta == null) {
            cancelamentoAntecipadoPlanosMulta = 0.0;
        }
        return cancelamentoAntecipadoPlanosMulta;
    }

    public void setCancelamentoAntecipadoPlanosMulta(Double cancelamentoAntecipadoPlanosMulta) {
        this.cancelamentoAntecipadoPlanosMulta = cancelamentoAntecipadoPlanosMulta;
    }

    public Date getCancelamentoAntecipadoContratosDepoisDe() {
        return cancelamentoAntecipadoContratosDepoisDe;
    }

    public void setCancelamentoAntecipadoContratosDepoisDe(Date cancelamentoAntecipadoContratosDepoisDe) {
        this.cancelamentoAntecipadoContratosDepoisDe = cancelamentoAntecipadoContratosDepoisDe;
    }

    public Date getCancelamentoAntecipadoPlanosData() {
        return cancelamentoAntecipadoPlanosData;
    }

    public void setCancelamentoAntecipadoPlanosData(Date cancelamentoAntecipadoPlanosData) {
        this.cancelamentoAntecipadoPlanosData = cancelamentoAntecipadoPlanosData;
    }

    public boolean isCancelamentoAntecipadoGerarParcelaMultaSeparada() {
        return cancelamentoAntecipadoGerarParcelaMultaSeparada;
    }

    public void setCancelamentoAntecipadoGerarParcelaMultaSeparada(boolean cancelamentoAntecipadoGerarParcelaMultaSeparada) {
        this.cancelamentoAntecipadoGerarParcelaMultaSeparada = cancelamentoAntecipadoGerarParcelaMultaSeparada;
    }

    public boolean isPermitirAlterarDataFinalContratoNoCancelamento() {
        return permitirAlterarDataFinalContratoNoCancelamento;
    }

    public void setPermitirAlterarDataFinalContratoNoCancelamento(boolean permitirAlterarDataFinalContratoNoCancelamento) {
        this.permitirAlterarDataFinalContratoNoCancelamento = permitirAlterarDataFinalContratoNoCancelamento;
    }

    public boolean isCancelamentoAutomaticoAntecipadoContratoForaRecorrencia() {
        return cancelamentoAutomaticoAntecipadoContratoForaRecorrencia;
    }

    public void setCancelamentoAutomaticoAntecipadoContratoForaRecorrencia(boolean cancelamentoAutomaticoAntecipadoContratoForaRecorrencia) {
        this.cancelamentoAutomaticoAntecipadoContratoForaRecorrencia = cancelamentoAutomaticoAntecipadoContratoForaRecorrencia;
    }

    public boolean isEnviarEmailCancelamento() {
        return enviarEmailCancelamento;
    }

    public void setEnviarEmailCancelamento(boolean enviarEmailCancelamento) {
        this.enviarEmailCancelamento = enviarEmailCancelamento;
    }

    public boolean isCancelamentoAvaliandoParcelas() {
        return cancelamentoAvaliandoParcelas;
    }

    public void setCancelamentoAvaliandoParcelas(boolean cancelamentoAvaliandoParcelas) {
        this.cancelamentoAvaliandoParcelas = cancelamentoAvaliandoParcelas;
    }

    public boolean isPermiteContratoPosPagoRenovacaoAuto() {
        return permiteContratoPosPagoRenovacaoAuto;
    }

    public void setPermiteContratoPosPagoRenovacaoAuto(boolean permiteContratoPosPagoRenovacaoAuto) {
        this.permiteContratoPosPagoRenovacaoAuto = permiteContratoPosPagoRenovacaoAuto;
    }



    public Integer getTipoCobrancaPacto() {
        if (tipoCobrancaPacto == null) {
            tipoCobrancaPacto = TipoCobrancaPactoEnum.PRE_PAGO.getCodigo();
        }
        return tipoCobrancaPacto;
    }

    public Boolean getTipoCobrancaPactoPrePago() {
        return getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo());
    }

    public Boolean getTipoCobrancaPactoPosPago() {
        return getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO.getCodigo()) ||
                getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_TENTATIVA.getCodigo());
    }

    public Boolean getTipoCobrancaPactoPosPagoMensal() {
        return getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo()) ||
                getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS.getCodigo());
    }

    public Boolean getTipoCobrancaPactoPrePagoRede() {
        return getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo());
    }

    public Boolean getTipoCobrancaPactoPrePagoEfetivado() {
        return getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo());
    }

    public String getTipoCobrancaPactoApresentar() {
        return TipoCobrancaPactoEnum.getConsultarPorCodigo(getTipoCobrancaPacto()).getDescricao();
    }

    public String getValorCreditoPactoApresentar() {
        return Formatador.formatarValorMonetario(getValorCreditoPacto());
    }

    public void setTipoCobrancaPacto(Integer tipoCobrancaPacto) {
        this.tipoCobrancaPacto = tipoCobrancaPacto;
    }

    public boolean isGerarCobrancaAutomaticaPacto() {
        return gerarCobrancaAutomaticaPacto;
    }

    public String getGerarCobrancaAutomaticaPactoApresentar() {
        if (isGerarCobrancaAutomaticaPacto()) {
            return "SIM";
        } else {
            return "NÃO";
        }
    }

    public void setGerarCobrancaAutomaticaPacto(boolean gerarCobrancaAutomaticaPacto) {
        this.gerarCobrancaAutomaticaPacto = gerarCobrancaAutomaticaPacto;
    }

    public String getTipoParcelaCancelamento() {
        return tipoParcelaCancelamento;
    }

    public boolean isCancelamentoTodasParcelas() {
        return TipoParcelaCancelamento.obterEnumPorSigla(tipoParcelaCancelamento).equals(TipoParcelaCancelamento.TODAS_PARCELAS);
    }

    public void setTipoParcelaCancelamento(String tipoParcelaCancelamento) {
        this.tipoParcelaCancelamento = tipoParcelaCancelamento;
    }

    public int getQuantidadeParcelasSeguidasCancelamento() {
        return quantidadeParcelasSeguidasCancelamento;
    }

    public void setQuantidadeParcelasSeguidasCancelamento(int quantidadeParcelasSeguidasCancelamento) {
        this.quantidadeParcelasSeguidasCancelamento = quantidadeParcelasSeguidasCancelamento;
    }

    public Date getDtUltimaCobrancaPacto() {
        return dtUltimaCobrancaPacto;
    }

    public String getDtUltimaCobrancaPactoApresentar() {
        return Uteis.getData(getDtUltimaCobrancaPacto());
    }

    public void setDtUltimaCobrancaPacto(Date dtUltimaCobrancaPacto) {
        this.dtUltimaCobrancaPacto = dtUltimaCobrancaPacto;
    }

    public Integer getQtdDiasFechamentoCobrancaPacto() {
        if (qtdDiasFechamentoCobrancaPacto == null) {
            qtdDiasFechamentoCobrancaPacto = 0;
        }
        return qtdDiasFechamentoCobrancaPacto;
    }

    public void setQtdDiasFechamentoCobrancaPacto(Integer qtdDiasFechamentoCobrancaPacto) {
        this.qtdDiasFechamentoCobrancaPacto = qtdDiasFechamentoCobrancaPacto;
    }

    public Double getValorCreditoPacto() {
        if (valorCreditoPacto == null) {
            valorCreditoPacto = 0.0;
        }
        return valorCreditoPacto;
    }

    public void setValorCreditoPacto(Double valorCreditoPacto) {
        this.valorCreditoPacto = valorCreditoPacto;
    }

    public boolean isGerarNotaFiscalCobrancaPacto() {
        return gerarNotaFiscalCobrancaPacto;
    }

    public String getGerarNotaFiscalCobrancaPactoApresentar() {
        if (isGerarNotaFiscalCobrancaPacto()) {
            return "SIM";
        } else {
            return "NÃO";
        }
    }

    public void setGerarNotaFiscalCobrancaPacto(boolean gerarNotaFiscalCobrancaPacto) {
        this.gerarNotaFiscalCobrancaPacto = gerarNotaFiscalCobrancaPacto;
    }

    public Integer getQtdParcelasCobrancaPacto() {
        //SE A QUANTIDADE FOR 0 COLOCAR COMO 1
        if (qtdParcelasCobrancaPacto == null || qtdParcelasCobrancaPacto == 0) {
            qtdParcelasCobrancaPacto = 1;
        }
        return qtdParcelasCobrancaPacto;
    }

    public void setQtdParcelasCobrancaPacto(Integer qtdParcelasCobrancaPacto) {
        this.qtdParcelasCobrancaPacto = qtdParcelasCobrancaPacto;
    }

    public Integer getQtdCreditoRenovarPrePagoCobrancaPacto() {
        if (qtdCreditoRenovarPrePagoCobrancaPacto == null) {
            qtdCreditoRenovarPrePagoCobrancaPacto = 0;
        }
        return qtdCreditoRenovarPrePagoCobrancaPacto;
    }

    public void setQtdCreditoRenovarPrePagoCobrancaPacto(Integer qtdCreditoRenovarPrePagoCobrancaPacto) {
        this.qtdCreditoRenovarPrePagoCobrancaPacto = qtdCreditoRenovarPrePagoCobrancaPacto;
    }

    public Integer getQtdDiasParaLiberacaoDeVagaEmTrancamento() {
        if (qtdDiasParaLiberacaoDeVagaEmTrancamento == null)
            qtdDiasParaLiberacaoDeVagaEmTrancamento = 0;

        return qtdDiasParaLiberacaoDeVagaEmTrancamento;
    }

    public void setQtdDiasParaLiberacaoDeVagaEmTrancamento(Integer qtdDiasParaLiberacaoDeVagaEmTrancamento) {
        this.qtdDiasParaLiberacaoDeVagaEmTrancamento = qtdDiasParaLiberacaoDeVagaEmTrancamento;
    }

    public boolean isUsarDataInicioDeContratoNoBI_ICV() {
        return usarDataInicioDeContratoNoBI_ICV;
    }

    public void setUsarDataInicioDeContratoNoBI_ICV(boolean usarDataInicioDeContratoNoBI_ICV) {
        this.usarDataInicioDeContratoNoBI_ICV = usarDataInicioDeContratoNoBI_ICV;
    }

    public boolean deveLiberarVaga(int qtdeDeDiasDoTrancamento) {
        boolean deveLiberar = false;
        if (qtdDiasParaLiberacaoDeVagaEmTrancamento != null) {
            deveLiberar = qtdDiasParaLiberacaoDeVagaEmTrancamento > 0 && qtdeDeDiasDoTrancamento > 0 && qtdeDeDiasDoTrancamento > qtdDiasParaLiberacaoDeVagaEmTrancamento;
        }
        return deveLiberar;
    }

    public Integer getTentativasLiberarParcelaVencida() {
        return tentativasLiberarParcelaVencida;
    }

    public void setTentativasLiberarParcelaVencida(Integer tentativasLiberarParcelaVencida) {
        this.tentativasLiberarParcelaVencida = tentativasLiberarParcelaVencida;
    }

    public Integer getQtdDiasVencimentoBoleto() {
        if (qtdDiasVencimentoBoleto == null) {
            qtdDiasVencimentoBoleto = 5;
        }
        return qtdDiasVencimentoBoleto;
    }

    public void setQtdDiasVencimentoBoleto(Integer qtdDiasVencimentoBoleto) {
        this.qtdDiasVencimentoBoleto = qtdDiasVencimentoBoleto;
    }

    public boolean isExistemNovosBoletosPacto() {
        return existemNovosBoletosPacto;
    }

    public void setExistemNovosBoletosPacto(boolean existemNovosBoletosPacto) {
        this.existemNovosBoletosPacto = existemNovosBoletosPacto;
    }

    public ConvenioCobrancaVO getConvenioBoletoPadrao() {
        return convenioBoletoPadrao;
    }

    public void setConvenioBoletoPadrao(ConvenioCobrancaVO convenioBoletoPadrao) {
        this.convenioBoletoPadrao = convenioBoletoPadrao;
    }

    public TipoObjetosCobrarEnum getTipoParcelasCobrarVendaSite() {
        if (tipoParcelasCobrarVendaSite == null) {
            tipoParcelasCobrarVendaSite = TipoObjetosCobrarEnum.TUDO;
        }
        return tipoParcelasCobrarVendaSite;
    }

    public void setTipoParcelasCobrarVendaSite(TipoObjetosCobrarEnum tipoParcelasCobrarVendaSite) {
        this.tipoParcelasCobrarVendaSite = tipoParcelasCobrarVendaSite;
    }

    public TipoObjetosCobrarEnum getTipoParcelasCobrarVendaSiteRegua() {
        if (tipoParcelasCobrarVendaSiteRegua == null) {
            tipoParcelasCobrarVendaSiteRegua = TipoObjetosCobrarEnum.TUDO;
        }
        return tipoParcelasCobrarVendaSiteRegua;    }

    public void setTipoParcelasCobrarVendaSiteRegua(TipoObjetosCobrarEnum tipoParcelasCobrarVendaSiteRegua) {
        this.tipoParcelasCobrarVendaSiteRegua = tipoParcelasCobrarVendaSiteRegua;
    }

    public boolean isGerarLoginAPIAoIncluirContrato() {
        return gerarLoginAPIAoIncluirContrato;
    }

    public void setGerarLoginAPIAoIncluirContrato(boolean gerarLoginAPIAoIncluirContrato) {
        this.gerarLoginAPIAoIncluirContrato = gerarLoginAPIAoIncluirContrato;
    }

    public ModeloMensagemVO getModeloMensagemEsqueciMinhaSenhaVendasOnline() {
        if (modeloMensagemEsqueciMinhaSenhaVendasOnline == null)
            modeloMensagemEsqueciMinhaSenhaVendasOnline = new ModeloMensagemVO();
        return modeloMensagemEsqueciMinhaSenhaVendasOnline;
    }

    public void setModeloMensagemEsqueciMinhaSenhaVendasOnline(ModeloMensagemVO modeloMensagemEsqueciMinhaSenhaVendasOnline) {
        this.modeloMensagemEsqueciMinhaSenhaVendasOnline = modeloMensagemEsqueciMinhaSenhaVendasOnline;
    }

    public boolean isEnviarNotaCidadeEmpresa() {
        return enviarNotaCidadeEmpresa;
    }

    public void setEnviarNotaCidadeEmpresa(boolean enviarNotaCidadeEmpresa) {
        this.enviarNotaCidadeEmpresa = enviarNotaCidadeEmpresa;
    }

    public boolean isPermitirMaillingGerarAutorizacaoCobrancaBoleto() {
        return permitirMaillingGerarAutorizacaoCobrancaBoleto;
    }

    public void setPermitirMaillingGerarAutorizacaoCobrancaBoleto(boolean permitirMaillingGerarAutorizacaoCobrancaBoleto) {
        this.permitirMaillingGerarAutorizacaoCobrancaBoleto = permitirMaillingGerarAutorizacaoCobrancaBoleto;
    }

    public boolean isGerarNFSeContaCorrente() {
        return gerarNFSeContaCorrente;
    }

    public void setGerarNFSeContaCorrente(boolean gerarNFSeContaCorrente) {
        this.gerarNFSeContaCorrente = gerarNFSeContaCorrente;
    }

    public String getInscMunicipal() {
        if (inscMunicipal == null) {
            inscMunicipal = "";
        }
        return inscMunicipal;
    }

    public void setInscMunicipal(String inscMunicipal) {
        this.inscMunicipal = inscMunicipal;
    }

    public Integer getSequencialLoteRPS() {
        if (sequencialLoteRPS == null) {
            sequencialLoteRPS = 0;
        }
        return sequencialLoteRPS;
    }

    public void setSequencialLoteRPS(Integer sequencialLoteRPS) {
        this.sequencialLoteRPS = sequencialLoteRPS;
    }

    public boolean isPermiteGerarArquivoLoteRPS() {
        return permiteGerarArquivoLoteRPS;
    }

    public void setPermiteGerarArquivoLoteRPS(boolean permiteGerarArquivoLoteRPS) {
        this.permiteGerarArquivoLoteRPS = permiteGerarArquivoLoteRPS;
    }

    public PessoaVO getPessoaFinan() {
        return pessoaFinan;
    }

    public void setPessoaFinan(PessoaVO pessoaFinan) {
        this.pessoaFinan = pessoaFinan;
    }

    public boolean isPermiteGerarNotaManual() {
        return permiteGerarNotaManual;
    }

    public void setPermiteGerarNotaManual(boolean permiteGerarNotaManual) {
        this.permiteGerarNotaManual = permiteGerarNotaManual;
    }

    public boolean isImpedirVendaContratoPorConflitoReposicao() {
        return impedirVendaContratoPorConflitoReposicao;
    }

    public void setImpedirVendaContratoPorConflitoReposicao(boolean impedirVendaContratoPorConflitoReposicao) {
        this.impedirVendaContratoPorConflitoReposicao = impedirVendaContratoPorConflitoReposicao;
    }

    public boolean isUsarNFCe() {
        return usarNFCe;
    }

    public void setUsarNFCe(boolean usarNFCe) {
        this.usarNFCe = usarNFCe;
    }

    public boolean isUtilizarNomeResponsavelNoBoleto() {
        return utilizarNomeResponsavelNoBoleto;
    }

    public void setUtilizarNomeResponsavelNoBoleto(boolean utilizarNomeResponsavelNoBoleto) {
        this.utilizarNomeResponsavelNoBoleto = utilizarNomeResponsavelNoBoleto;
    }

    public boolean isGerarQuitacaoCancelamentoAuto() {
        return gerarQuitacaoCancelamentoAuto;
    }

    public void setGerarQuitacaoCancelamentoAuto(boolean gerarQuitacaoCancelamentoAuto) {
        this.gerarQuitacaoCancelamentoAuto = gerarQuitacaoCancelamentoAuto;
    }

    public boolean isGerarQuitacaoCancelamentoRemanescente() {
        return gerarQuitacaoCancelamentoRemanescente;
    }

    public void setGerarQuitacaoCancelamentoRemanescente(boolean gerarQuitacaoCancelamentoRemanescente) {
        this.gerarQuitacaoCancelamentoRemanescente = gerarQuitacaoCancelamentoRemanescente;
    }

    public boolean isAplicarMultaeJurosNoCancelamentoAutomatico() {
        return aplicarMultaeJurosNoCancelamentoAutomatico;
    }

    public void setAplicarMultaeJurosNoCancelamentoAutomatico(boolean aplicarMultaeJurosNoCancelamentoAutomatico) {
        this.aplicarMultaeJurosNoCancelamentoAutomatico = aplicarMultaeJurosNoCancelamentoAutomatico;
    }

    public boolean isAplicarMultaMudancaPlano() {
        return aplicarMultaMudancaPlano;
    }

    public void setAplicarMultaMudancaPlano(boolean aplicarMultaMudancaPlano) {
        this.aplicarMultaMudancaPlano = aplicarMultaMudancaPlano;
    }

    public boolean isNaoGerarResiduoCancelamentoAutomatico() {
        return naoGerarResiduoCancelamentoAutomatico;
    }

    public void setNaoGerarResiduoCancelamentoAutomatico(boolean naoGerarResiduoCancelamentoAutomatico) {
        this.naoGerarResiduoCancelamentoAutomatico = naoGerarResiduoCancelamentoAutomatico;
    }

    public boolean isDepositarResiduoCancelamentoNaContaCorrente() {
        return depositarResiduoCancelamentoNaContaCorrente;
    }

    public void setDepositarResiduoCancelamentoNaContaCorrente(boolean depositarResiduoCancelamentoNaContaCorrente) {
        this.depositarResiduoCancelamentoNaContaCorrente = depositarResiduoCancelamentoNaContaCorrente;
    }

    public List<ContaCorrenteEmpresaVO> getContaCorrenteEmpresaVOsAntesAlteracao() {
        if (contaCorrenteEmpresaVOsAntesAlteracao == null) {
            contaCorrenteEmpresaVOsAntesAlteracao = new ArrayList<ContaCorrenteEmpresaVO>();
        }
        return contaCorrenteEmpresaVOsAntesAlteracao;
    }

    public void setContaCorrenteEmpresaVOsAntesAlteracao(List<ContaCorrenteEmpresaVO> contaCorrenteEmpresaVOsAntesAlteracao) {
        this.contaCorrenteEmpresaVOsAntesAlteracao = contaCorrenteEmpresaVOsAntesAlteracao;
    }

    public void registrarContaCorrenteEmpresaVOsAntesAlteracao() throws Exception {
        contaCorrenteEmpresaVOsAntesAlteracao = new ArrayList<ContaCorrenteEmpresaVO>();
        for (ContaCorrenteEmpresaVO conta : this.getContaCorrenteEmpresaVOs()) {
            contaCorrenteEmpresaVOsAntesAlteracao.add((ContaCorrenteEmpresaVO) conta.getClone(true));
        }
    }

    public boolean isAlterarDataHoraCheckGestaoPersonal() {
        return alterarDataHoraCheckGestaoPersonal;
    }

    public void setAlterarDataHoraCheckGestaoPersonal(boolean alterarDataHoraCheckGestaoPersonal) {
        this.alterarDataHoraCheckGestaoPersonal = alterarDataHoraCheckGestaoPersonal;
    }

    public boolean isSenhaAcessoOnzeDigitos() {
        return senhaAcessoOnzeDigitos;
    }

    public void setSenhaAcessoOnzeDigitos(boolean senhaAcessoOnzeDigitos) {
        this.senhaAcessoOnzeDigitos = senhaAcessoOnzeDigitos;
    }

    public void setNaoRenovarContratoSemIndiceFinanceiro(boolean naoRenovarContratoSemIndiceFinanceiro) {
        this.naoRenovarContratoSemIndiceFinanceiro = naoRenovarContratoSemIndiceFinanceiro;
    }

    public boolean isNaoRenovarContratoSemIndiceFinanceiro() {
        return naoRenovarContratoSemIndiceFinanceiro;
    }

    public String getCodigoGymPass() {
        if (codigoGymPass == null) {
            codigoGymPass = "";
        }
        return codigoGymPass;
    }

    public void setCodigoGymPass(String codigoGymPass) {
        this.codigoGymPass = codigoGymPass;
    }

    public String getTokenApiGymPass() {
        if (tokenApiGymPass == null) {
            tokenApiGymPass = "";
        }
        return tokenApiGymPass;
    }

    public void setTokenApiGymPass(String tokenApiGymPass) {
        this.tokenApiGymPass = tokenApiGymPass;
    }

    public boolean isGerarRemessaContratoCancelado() {
        return gerarRemessaContratoCancelado;
    }

    public void setGerarRemessaContratoCancelado(boolean gerarRemessaContratoCancelado) {
        this.gerarRemessaContratoCancelado = gerarRemessaContratoCancelado;
    }

    public boolean isHabilitarSomaDeAulaNaoVigente() {
        return habilitarSomaDeAulaNaoVigente;
    }

    public void setHabilitarSomaDeAulaNaoVigente(boolean habilitarSomaDeAulaNaoVigente) {
        this.habilitarSomaDeAulaNaoVigente = habilitarSomaDeAulaNaoVigente;
    }

    public boolean isDefinirCpfComoSenhaCatraca() {
        return definirCpfComoSenhaCatraca;
    }

    public void setDefinirCpfComoSenhaCatraca(boolean definirCpfComoSenhaCatraca) {
        this.definirCpfComoSenhaCatraca = definirCpfComoSenhaCatraca;
    }

    public boolean isBloqueioTemporario() {
        return bloqueioTemporario;
    }

    public void setBloqueioTemporario(boolean bloqueioTemporario) {
        this.bloqueioTemporario = bloqueioTemporario;
    }

    public boolean isMostrarNotaPorDiaCompetencia() {
        return mostrarNotaPorDiaCompetencia;
    }

    public void setMostrarNotaPorDiaCompetencia(boolean mostrarNotaPorDiaCompetencia) {
        this.mostrarNotaPorDiaCompetencia = mostrarNotaPorDiaCompetencia;
    }

    public Integer getPontosAlunoAcesso() {
        return pontosAlunoAcesso;
    }

    public void setPontosAlunoAcesso(Integer pontosAlunoAcesso) {
        this.pontosAlunoAcesso = pontosAlunoAcesso;
    }

    public boolean isTrabalharComPontuacao() {
        return trabalharComPontuacao;
    }

    public void setTrabalharComPontuacao(boolean trabalharComPontuacao) {
        this.trabalharComPontuacao = trabalharComPontuacao;
    }


    public ConfiguracaoEmpresaRDStationVO getConfiguracaoRDStation() {
        if (configuracaoRDStation == null) {
            configuracaoRDStation = new ConfiguracaoEmpresaRDStationVO();
        }
        return configuracaoRDStation;
    }

    public void setConfiguracaoRDStation(ConfiguracaoEmpresaRDStationVO configuracaoRDStation) {
        this.configuracaoRDStation = configuracaoRDStation;
    }

    public ConfiguracaoIntegracaoBuzzLeadVO getConfiguracaoIntegracaoBuzzLeadVO() {
        if (configuracaoIntegracaoBuzzLeadVO == null) {
            configuracaoIntegracaoBuzzLeadVO = new ConfiguracaoIntegracaoBuzzLeadVO();
        }
        return configuracaoIntegracaoBuzzLeadVO;
    }

    public void setConfiguracaoIntegracaoBuzzLeadVO(ConfiguracaoIntegracaoBuzzLeadVO configuracaoIntegracaoBuzzLeadVO) {
        this.configuracaoIntegracaoBuzzLeadVO = configuracaoIntegracaoBuzzLeadVO;
    }

    public ConfiguracaoIntegracaoWordPressVO getConfiguracaoIntegracaoWordPressVO() {
        if (configuracaoIntegracaoWordPressVO == null) {
            configuracaoIntegracaoWordPressVO = new ConfiguracaoIntegracaoWordPressVO();
        }
        return configuracaoIntegracaoWordPressVO;
    }

    public void setConfiguracaoIntegracaoWordPressVO(ConfiguracaoIntegracaoWordPressVO configuracaoIntegracaoWordPressVO) {
        this.configuracaoIntegracaoWordPressVO = configuracaoIntegracaoWordPressVO;
    }

    public ConfiguracaoIntegracaoJoinVO getConfiguracaoIntegracaoJoinVO() {
        if (configuracaoIntegracaoJoinVO == null) {
            configuracaoIntegracaoJoinVO = new ConfiguracaoIntegracaoJoinVO();
        }
        return configuracaoIntegracaoJoinVO;
    }

    public void setConfiguracaoIntegracaoJoinVO(final ConfiguracaoIntegracaoJoinVO configuracaoIntegracaoJoinVO) {
        this.configuracaoIntegracaoJoinVO = configuracaoIntegracaoJoinVO;
    }

    public ConfiguracaoIntegracaoGenericaLeadsVO getConfiguracaoIntegracaoGenericaLeadsVO() {
        if (configuracaoIntegracaoGenericaLeadsVO == null) {
            configuracaoIntegracaoGenericaLeadsVO = new ConfiguracaoIntegracaoGenericaLeadsVO();
        }
        return configuracaoIntegracaoGenericaLeadsVO;
    }

    public void setConfiguracaoIntegracaoGenericaLeadsVO(final ConfiguracaoIntegracaoGenericaLeadsVO configuracaoIntegracaoGenericaLeadsVO) {
        this.configuracaoIntegracaoGenericaLeadsVO = configuracaoIntegracaoGenericaLeadsVO;
    }

    public ConfiguracaoIntegracaoGenericaLeadsGymbotVO getConfiguracaoIntegracaoGenericaLeadsGymbotVO() {
        if (configuracaoIntegracaoGenericaLeadsGymbotVO == null) {
            configuracaoIntegracaoGenericaLeadsGymbotVO = new ConfiguracaoIntegracaoGenericaLeadsGymbotVO();
        }
        return configuracaoIntegracaoGenericaLeadsGymbotVO;
    }

    public void setConfiguracaoIntegracaoGenericaLeadsGymbotVO(final ConfiguracaoIntegracaoGenericaLeadsGymbotVO configuracaoIntegracaoGenericaLeadsGymbotVO) {
        this.configuracaoIntegracaoGenericaLeadsGymbotVO = configuracaoIntegracaoGenericaLeadsGymbotVO;
    }

    public ConfiguracaoIntegracaoFogueteVO getConfiguracaoIntegracaoFogueteVO() {
        return configuracaoIntegracaoFogueteVO;
    }

    public void setConfiguracaoIntegracaoFogueteVO(ConfiguracaoIntegracaoFogueteVO configuracaoIntegracaoFogueteVO) {
        this.configuracaoIntegracaoFogueteVO = configuracaoIntegracaoFogueteVO;
    }

    public boolean isRetirarEdicaoPagamento() {
        return retirarEdicaoPagamento;
    }

    public void setRetirarEdicaoPagamento(boolean retirarEdicaoPagamento) {
        this.retirarEdicaoPagamento = retirarEdicaoPagamento;
    }

    public boolean isAdicionarAulasDesmarcadasContratoAnterior() {
        return adicionarAulasDesmarcadasContratoAnterior;
    }

    public void setAdicionarAulasDesmarcadasContratoAnterior(boolean adicionarAulasDesmarcadasContratoAnterior) {
        this.adicionarAulasDesmarcadasContratoAnterior = adicionarAulasDesmarcadasContratoAnterior;
    }

    public String getTipoParcelaCancelamentoForaRegimeRecorrencia() {
        return tipoParcelaCancelamentoForaRegimeRecorrencia;
    }

    public void setTipoParcelaCancelamentoForaRegimeRecorrencia(String tipoParcelaCancelamentoForaRegimeRecorrencia) {
        this.tipoParcelaCancelamentoForaRegimeRecorrencia = tipoParcelaCancelamentoForaRegimeRecorrencia;
    }

    public int getQuantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia() {
        return quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia;
    }

    public void setQuantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia(int quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia) {
        this.quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia = quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia;
    }

    public int getQuantidadeDiasUteisAposVencimentoParaCancelarContrato() {
        return quantidadeDiasUteisAposVencimentoParaCancelarContrato;
    }

    public void setQuantidadeDiasUteisAposVencimentoParaCancelarContrato(int quantidadeDiasUteisAposVencimentoParaCancelarContrato) {
        this.quantidadeDiasUteisAposVencimentoParaCancelarContrato = quantidadeDiasUteisAposVencimentoParaCancelarContrato;
    }

    public boolean isCancelamentoTodasParcelasForaRegimeRecorrencia() {
        return TipoParcelaCancelamento.obterEnumPorSigla(tipoParcelaCancelamentoForaRegimeRecorrencia).equals(TipoParcelaCancelamento.TODAS_PARCELAS);
    }

    public Date getLimiteInicialItensBIPendencia() {
        return limiteInicialItensBIPendencia;
    }

    public void setLimiteInicialItensBIPendencia(Date limiteInicialItensBIPendencia) {
        this.limiteInicialItensBIPendencia = limiteInicialItensBIPendencia;
    }

    public void setTempoSaidaAcademia(Integer tempoSaidaAcademia) {
        this.tempoSaidaAcademia = tempoSaidaAcademia;
    }

    public Integer getTempoSaidaAcademia() {
        return tempoSaidaAcademia;
    }

    public void setTempoSaidaAcademiaFormatada(String tempoSaidaAcademiaFormatada) {
        this.tempoSaidaAcademiaFormatada = tempoSaidaAcademiaFormatada;
    }

    public String getTempoSaidaAcademiaFormatada() {
        if (tempoSaidaAcademiaFormatada.equals("")) {
            tempoSaidaAcademiaFormatada = "00:00";
        }
        return tempoSaidaAcademiaFormatada;
    }

    public boolean isPermiteRenovarContratoViaAPP() {
        return permiteRenovarContratoViaAPP;
    }

    public void setPermiteRenovarContratoViaAPP(boolean permiteRenovarContratoViaAPP) {
        this.permiteRenovarContratoViaAPP = permiteRenovarContratoViaAPP;
    }

    public Double getValorMensalEmitirNFSe() {
        if (valorMensalEmitirNFSe == null) {
            valorMensalEmitirNFSe = 0.0;
        }
        return valorMensalEmitirNFSe;
    }

    public void setValorMensalEmitirNFSe(Double valorMensalEmitirNFSe) {
        this.valorMensalEmitirNFSe = valorMensalEmitirNFSe;
    }

    public List<ConfiguracaoReenvioMovParcelaEmpresaVO> getConfiguracaoReenvioMovParcelaEmpresaVOS() {
        if (configuracaoReenvioMovParcelaEmpresaVOS == null) {
            configuracaoReenvioMovParcelaEmpresaVOS = new ArrayList<ConfiguracaoReenvioMovParcelaEmpresaVO>();
        }
        return configuracaoReenvioMovParcelaEmpresaVOS;
    }

    public void setConfiguracaoReenvioMovParcelaEmpresaVOS(List<ConfiguracaoReenvioMovParcelaEmpresaVO> configuracaoReenvioMovParcelaEmpresaVOS) {
        this.configuracaoReenvioMovParcelaEmpresaVOS = configuracaoReenvioMovParcelaEmpresaVOS;
    }

    public void adicionarObjConfiguracaoReenvioMovParcelaEmpresaVOs(ConfiguracaoReenvioMovParcelaEmpresaVO obj) throws Exception {
        getConfiguracaoReenvioMovParcelaEmpresaVOS().add(obj);
        ordenarListaConfiguracaoReenvioMovParcelaEmpresa();
    }

    public void excluirObjConfiguracaoReenvioMovParcelaEmpresas(ConfiguracaoReenvioMovParcelaEmpresaVO obj) throws Exception {
        int index = 0;
        Iterator i = getConfiguracaoReenvioMovParcelaEmpresaVOS().iterator();
        while (i.hasNext()) {
            ConfiguracaoReenvioMovParcelaEmpresaVO objExistente = (ConfiguracaoReenvioMovParcelaEmpresaVO) i.next();
            if (objExistente.getCodigo().equals(obj.getCodigo())) {
                getConfiguracaoReenvioMovParcelaEmpresaVOS().remove(index);
                return;
            }
            index++;
        }
        ordenarListaConfiguracaoReenvioMovParcelaEmpresa();
    }

    public void ordenarListaConfiguracaoReenvioMovParcelaEmpresa() {
        int posicao = 1;
        Ordenacao.ordenarLista(getConfiguracaoReenvioMovParcelaEmpresaVOS(), "posicao");
        for (ConfiguracaoReenvioMovParcelaEmpresaVO obj : getConfiguracaoReenvioMovParcelaEmpresaVOS()) {
            obj.setPosicao(posicao++);
        }
        Ordenacao.ordenarLista(getConfiguracaoReenvioMovParcelaEmpresaVOS(), "posicao");
    }

    public ConfiguracaoReenvioMovParcelaEmpresaVO consultarObjConfiguracaoReenvioMovParcelaEmpresaVO(Integer convenioCobranca) throws Exception {
        Iterator i = getConfiguracaoReenvioMovParcelaEmpresaVOS().iterator();
        while (i.hasNext()) {
            ConfiguracaoReenvioMovParcelaEmpresaVO objExistente = (ConfiguracaoReenvioMovParcelaEmpresaVO) i.next();
            if (objExistente.getConvenioCobrancaVO().getCodigo().equals(convenioCobranca)) {
                return objExistente;
            }
        }
        return null;
    }

    public boolean isHabilitarReenvioAutomaticoRemessa() {
        return habilitarReenvioAutomaticoRemessa;
    }

    public void setHabilitarReenvioAutomaticoRemessa(boolean habilitarReenvioAutomaticoRemessa) {
        this.habilitarReenvioAutomaticoRemessa = habilitarReenvioAutomaticoRemessa;
    }

    public boolean isEmitirNotaSomenteRecorrencia() {
        return emitirNotaSomenteRecorrencia;
    }

    public void setEmitirNotaSomenteRecorrencia(boolean emitirNotaSomenteRecorrencia) {
        this.emitirNotaSomenteRecorrencia = emitirNotaSomenteRecorrencia;
    }

    public boolean isEmitirNomeAlunoNotaFamilia() {
        return emitirNomeAlunoNotaFamilia;
    }

    public void setEmitirNomeAlunoNotaFamilia(boolean emitirNomeAlunoNotaFamilia) {
        this.emitirNomeAlunoNotaFamilia = emitirNomeAlunoNotaFamilia;
    }

    public boolean isIrTelaPagamentoCartaoCreditoRecorrente() {
        return irTelaPagamentoCartaoCreditoRecorrente;
    }

    public void setIrTelaPagamentoCartaoCreditoRecorrente(boolean irTelaPagamentoCartaoCreditoRecorrente) {
        this.irTelaPagamentoCartaoCreditoRecorrente = irTelaPagamentoCartaoCreditoRecorrente;
    }

    public boolean isEnviarNFCeAutomatico() {
        return enviarNFCeAutomatico;
    }

    public void setEnviarNFCeAutomatico(boolean enviarNFCeAutomatico) {
        this.enviarNFCeAutomatico = enviarNFCeAutomatico;
    }

    public boolean isEmitirNFCeSomenteRecorrencia() {
        return emitirNFCeSomenteRecorrencia;
    }

    public void setEmitirNFCeSomenteRecorrencia(boolean emitirNFCeSomenteRecorrencia) {
        this.emitirNFCeSomenteRecorrencia = emitirNFCeSomenteRecorrencia;
    }

    public boolean isEmitirMesReferenciaNFCe() {
        return emitirMesReferenciaNFCe;
    }

    public void setEmitirMesReferenciaNFCe(boolean emitirMesReferenciaNFCe) {
        this.emitirMesReferenciaNFCe = emitirMesReferenciaNFCe;
    }

    public boolean isUsaIntegracoesCrm() {
        return usaIntegracoesCrm;
    }

    public void setUsaIntegracoesCrm(boolean usaIntegracoesCrm) {
        this.usaIntegracoesCrm = usaIntegracoesCrm;
    }


    public String getTokenBuzzLead() {
        return tokenBuzzLead;
    }

    public void setTokenBuzzLead(String tokenBuzzLead) {
        this.tokenBuzzLead = tokenBuzzLead;
    }

    public String getUrlLinkSiteCadastro() {
        if (urlLinkSiteCadastro == null) {
            urlLinkSiteCadastro = "";
        }
        return urlLinkSiteCadastro;
    }

    public String getTiposProduto() {
        if (tiposProduto == null) {
            tiposProduto = "";
        }
        return tiposProduto;
    }

    public void setTiposProduto(String tiposProduto) {
        this.tiposProduto = tiposProduto;
    }

    public void setUrlLinkSiteCadastro(String urlLinkSiteCadastro) {
        this.urlLinkSiteCadastro = urlLinkSiteCadastro;
    }

    public boolean isUsarDataOriginalCompensacaoNFSe() {
        return usarDataOriginalCompensacaoNFSe;
    }

    public void setUsarDataOriginalCompensacaoNFSe(boolean usarDataOriginalCompensacaoNFSe) {
        this.usarDataOriginalCompensacaoNFSe = usarDataOriginalCompensacaoNFSe;
    }

    public boolean isUtilizaSistemaEstacionamento() {
        return utilizaSistemaEstacionamento;
    }

    public void setUtilizaSistemaEstacionamento(boolean utilizaSistemaEstacionamento) {
        this.utilizaSistemaEstacionamento = utilizaSistemaEstacionamento;
    }

    public EmpresaConfigEstacionamentoVO getConfigEstacionamento() {
        if (null == configEstacionamento) {
            configEstacionamento = new EmpresaConfigEstacionamentoVO();
        }
        return configEstacionamento;
    }

    public void setConfigEstacionamento(EmpresaConfigEstacionamentoVO configEstacionamento) {
        this.configEstacionamento = configEstacionamento;
    }

    public boolean isPermitirLancarVariasParcelasSaldoDevedor() {
        return permitirLancarVariasParcelasSaldoDevedor;
    }

    public void setPermitirLancarVariasParcelasSaldoDevedor(boolean permitirLancarVariasParcelasSaldoDevedor) {
        this.permitirLancarVariasParcelasSaldoDevedor = permitirLancarVariasParcelasSaldoDevedor;
    }

    public Integer getProdutoEmissaoNFCeFinanceiro() {
        if (produtoEmissaoNFCeFinanceiro == null) {
            produtoEmissaoNFCeFinanceiro = 0;
        }
        return produtoEmissaoNFCeFinanceiro;
    }

    public void setProdutoEmissaoNFCeFinanceiro(Integer produtoEmissaoNFCeFinanceiro) {
        this.produtoEmissaoNFCeFinanceiro = produtoEmissaoNFCeFinanceiro;
    }

    public Integer getProdutoEmissaoNFSeFinanceiro() {
        if (produtoEmissaoNFSeFinanceiro == null) {
            produtoEmissaoNFSeFinanceiro = 0;
        }
        return produtoEmissaoNFSeFinanceiro;
    }

    public void setProdutoEmissaoNFSeFinanceiro(Integer produtoEmissaoNFSeFinanceiro) {
        this.produtoEmissaoNFSeFinanceiro = produtoEmissaoNFSeFinanceiro;
    }

    public Integer getQtdExecucoesRetentativa() {
        if (qtdExecucoesRetentativa == null) {
            qtdExecucoesRetentativa = 0;
        }
        return qtdExecucoesRetentativa;
    }

    public void setQtdExecucoesRetentativa(Integer qtdExecucoesRetentativa) {
        this.qtdExecucoesRetentativa = qtdExecucoesRetentativa;
    }

    public Integer getQtdCreditoRemessa() {
        if (qtdCreditoRemessa == null) {
            qtdCreditoRemessa = 0;
        }
        return qtdCreditoRemessa;
    }

    public void setQtdCreditoRemessa(Integer qtdCreditoRemessa) {
        this.qtdCreditoRemessa = qtdCreditoRemessa;
    }

    public Integer getQtdCreditoTransacao() {
        if (qtdCreditoTransacao == null) {
            qtdCreditoTransacao = 0;
        }
        return qtdCreditoTransacao;
    }

    public void setQtdCreditoTransacao(Integer qtdCreditoTransacao) {
        this.qtdCreditoTransacao = qtdCreditoTransacao;
    }

    public boolean isUsarNFCePorPagamento() {
        return usarNFCePorPagamento;
    }

    public void setUsarNFCePorPagamento(boolean usarNFCePorPagamento) {
        this.usarNFCePorPagamento = usarNFCePorPagamento;
    }

    public boolean isConsultarDiasAnterioresNFSe() {
        return consultarDiasAnterioresNFSe;
    }

    public void setConsultarDiasAnterioresNFSe(boolean consultarDiasAnterioresNFSe) {
        this.consultarDiasAnterioresNFSe = consultarDiasAnterioresNFSe;
    }

    public boolean isConsultarDiasAnterioresNFCe() {
        return consultarDiasAnterioresNFCe;
    }

    public void setConsultarDiasAnterioresNFCe(boolean consultarDiasAnterioresNFCe) {
        this.consultarDiasAnterioresNFCe = consultarDiasAnterioresNFCe;
    }

    public boolean isEmImportacao() {
        return emImportacao;
    }

    public void setEmImportacao(boolean emImportacao) {
        this.emImportacao = emImportacao;
    }

    public boolean isNotasAutoPgRetroativo() {
        return notasAutoPgRetroativo;
    }

    public void setNotasAutoPgRetroativo(boolean notasAutoPgRetroativo) {
        this.notasAutoPgRetroativo = notasAutoPgRetroativo;
    }

    public boolean isValidarVencimentoCartaoAutorizacao() {
        return validarVencimentoCartaoAutorizacao;
    }

    public void setValidarVencimentoCartaoAutorizacao(boolean validarVencimentoCartaoAutorizacao) {
        this.validarVencimentoCartaoAutorizacao = validarVencimentoCartaoAutorizacao;
    }

    public boolean isPermitirEstornarContratoComParcelasPG() {
        return permitirEstornarContratoComParcelasPG;
    }

    public void setPermitirEstornarContratoComParcelasPG(boolean permitirEstornarContratoComParcelasPG) {
        this.permitirEstornarContratoComParcelasPG = permitirEstornarContratoComParcelasPG;
    }

    public boolean isPermMarcarAulaFeriado() {
        return permMarcarAulaFeriado;
    }

    public void setPermMarcarAulaFeriado(boolean permMarcarAulaFeriado) {
        this.permMarcarAulaFeriado = permMarcarAulaFeriado;
    }

    public String getHoraAberturaFeriado() {
        return horaAberturaFeriado;
    }

    public void setHoraAberturaFeriado(String horaAberturaFeriado) {
        this.horaAberturaFeriado = horaAberturaFeriado;
    }

    public String getHoraFechamentoFeriado() {
        return horaFechamentoFeriado;
    }

    public void setHoraFechamentoFeriado(String horaFechamentoFeriado) {
        this.horaFechamentoFeriado = horaFechamentoFeriado;
    }

    public boolean isPermAlunoMarcarAulaOutraEmpresa() {
        return permAlunoMarcarAulaOutraEmpresa;
    }

    public void setPermAlunoMarcarAulaOutraEmpresa(boolean permAlunoMarcarAulaOutraEmpresa) {
        this.permAlunoMarcarAulaOutraEmpresa = permAlunoMarcarAulaOutraEmpresa;
    }

    public String getFormasPagamentoEmissaoNFCe() {
        return formasPagamentoEmissaoNFCe;
    }

    public void setFormasPagamentoEmissaoNFCe(String formasPagamentoEmissaoNFCe) {
        this.formasPagamentoEmissaoNFCe = formasPagamentoEmissaoNFCe;
    }

    public void setFormasPagamentoEmissaoNFSe(String formasPagamentoEmissaoNFSe) {
        this.formasPagamentoEmissaoNFSe = formasPagamentoEmissaoNFSe;
    }

    public String getFormasPagamentoEmissaoNFSe() {
        return formasPagamentoEmissaoNFSe;
    }

    public boolean isConsiderarSomenteParcelasPlanos() {
        return considerarSomenteParcelasPlanos;
    }

    public void setConsiderarSomenteParcelasPlanos(boolean considerarSomenteParcelasPlanos) {
        this.considerarSomenteParcelasPlanos = considerarSomenteParcelasPlanos;
    }

    public boolean isCobrarMultaJurosTransacao() {
        return cobrarMultaJurosTransacao;
    }

    public void setCobrarMultaJurosTransacao(boolean cobrarMultaJurosTransacao) {
        this.cobrarMultaJurosTransacao = cobrarMultaJurosTransacao;
    }

    public boolean isCobrarMultaJurosDCO() {
        return cobrarMultaJurosDCO;
    }

    public void setCobrarMultaJurosDCO(boolean cobrarMultaJurosDCO) {
        this.cobrarMultaJurosDCO = cobrarMultaJurosDCO;
    }

    public boolean isCobrarMultaJurosDCC() {
        return cobrarMultaJurosDCC;
    }

    public void setCobrarMultaJurosDCC(boolean cobrarMultaJurosDCC) {
        this.cobrarMultaJurosDCC = cobrarMultaJurosDCC;
    }



    public Integer getPontuacaoDaOperacaoAcessoChuva(EmpresaVO empresaVO,Connection con) throws Exception {
        Integer pontos = 0;
        if (empresaVO != null) {
            CampanhaDuracaoVO maiorCampanhaAtiva = new CampanhaDuracao(con).campanhaVigenteMultiplicador(Calendario.hoje(), TipoItemCampanhaEnum.ACESSOCHUVA,empresaVO.getCodigo());
            pontos = (maiorCampanhaAtiva.getMultiplicador() > 0 ? maiorCampanhaAtiva.getMultiplicador() * empresaVO.getPontosAlunoAcesso() : empresaVO.getPontosAlunoAcesso());
        }
        return pontos;
    }

    public Integer getPontosAlunoAcessoChuva() {
        return pontosAlunoAcessoChuva;
    }

    public void setPontosAlunoAcessoChuva(Integer pontosAlunoAcessoChuva) {
        this.pontosAlunoAcessoChuva = pontosAlunoAcessoChuva;
    }

    public Integer getPontosAlunoAcessoFrio() {
        return pontosAlunoAcessoFrio;
    }

    public void setPontosAlunoAcessoFrio(Integer pontosAlunoAcessoFrio) {
        this.pontosAlunoAcessoFrio = pontosAlunoAcessoFrio;
    }

    public Integer getPontosAlunoAcessoCalor() {
        return pontosAlunoAcessoCalor;
    }

    public void setPontosAlunoAcessoCalor(Integer pontosAlunoAcessoCalor) {
        this.pontosAlunoAcessoCalor = pontosAlunoAcessoCalor;
    }

    public boolean isAddAutoClienteTreinoWeb() {
        return addAutoClienteTreinoWeb;
    }

    public void setAddAutoClienteTreinoWeb(boolean addAutoClienteTreinoWeb) {
        this.addAutoClienteTreinoWeb = addAutoClienteTreinoWeb;
    }

    public ParceiroFidelidadeVO getParceiroFidelidade() {
        if (parceiroFidelidade == null) {
            parceiroFidelidade = new ParceiroFidelidadeVO();
            parceiroFidelidade.setEmpresa(this);
        }
        return parceiroFidelidade;
    }

    public void setParceiroFidelidade(ParceiroFidelidadeVO parceiroFidelidade) {
        this.parceiroFidelidade = parceiroFidelidade;
    }

    public String getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }

    public boolean isUsarParceiroFidelidade() {
        return usarParceiroFidelidade;
    }

    public void setUsarParceiroFidelidade(boolean usarParceiroFidelidade) {
        this.usarParceiroFidelidade = usarParceiroFidelidade;
    }

    public boolean isEnvioNotificacaoNotasNFSe() {
        return envioNotificacaoNotasNFSe;
    }

    public void setEnvioNotificacaoNotasNFSe(boolean envioNotificacaoNotasNFSe) {
        this.envioNotificacaoNotasNFSe = envioNotificacaoNotasNFSe;
    }

    public boolean isEnvioNotificacaoNotasNFCe() {
        return envioNotificacaoNotasNFCe;
    }

    public void setEnvioNotificacaoNotasNFCe(boolean envioNotificacaoNotasNFCe) {
        this.envioNotificacaoNotasNFCe = envioNotificacaoNotasNFCe;
    }

    public String getEmailsNotificacaoAutomaticaNotas() {
        return emailsNotificacaoAutomaticaNotas;
    }

    public void setEmailsNotificacaoAutomaticaNotas(String emailsNotificacaoAutomaticaNotas) {
        this.emailsNotificacaoAutomaticaNotas = emailsNotificacaoAutomaticaNotas;
    }

    public String getEmailNotificacaoVendasOnline() {
        return emailNotificacaoVendasOnline;
    }

    public void setEmailNotificacaoVendasOnline(String emailNotificacaoVendasOnline) {
        this.emailNotificacaoVendasOnline = emailNotificacaoVendasOnline;
    }

    public boolean isValidarCertificado() {
        return validarCertificado;
    }

    public void setValidarCertificado(boolean validarCertificado) {
        this.validarCertificado = validarCertificado;
    }

    public boolean isBloquearAcessoArmarioVigenciaVencida() {
        return bloquearAcessoArmarioVigenciaVencida;
    }

    public void setBloquearAcessoArmarioVigenciaVencida(boolean bloquearAcessoArmarioVigenciaVencida) {
        this.bloquearAcessoArmarioVigenciaVencida = bloquearAcessoArmarioVigenciaVencida;
    }

    public List<LogCobrancaPactoVO> getLogCobrancaPacto() {
        if (logCobrancaPacto == null) {
            logCobrancaPacto = new ArrayList<LogCobrancaPactoVO>();
        }
        return logCobrancaPacto;
    }

    public void setLogCobrancaPacto(List<LogCobrancaPactoVO> logCobrancaPacto) {
        this.logCobrancaPacto = logCobrancaPacto;
    }

    public Date getDataConcessaoDiaExtra() {
        return dataConcessaoDiaExtra;
    }

    public void setDataConcessaoDiaExtra(Date dataConcessaoDiaExtra) {
        this.dataConcessaoDiaExtra = dataConcessaoDiaExtra;
    }

    public String getPrimeiroTelefoneNaoNulo() {
        if (StringUtils.isNotBlank(telComercial1)) {
            return telComercial1;
        }

        if (StringUtils.isNotBlank(telComercial2)) {
            return telComercial2;
        }

        if (StringUtils.isNotBlank(telComercial3)) {
            return telComercial3;
        }

        return SEM_TELEFONE;
    }

    public void setSincronizadoMovidesk(boolean sincronizadoMovidesk) {
        this.sincronizadoMovidesk = sincronizadoMovidesk;
    }

    public boolean getSincronizadoMovidesk() {
        return sincronizadoMovidesk;
    }

    public void setEmailMovidesk(String emailMovidesk) {
        this.emailMovidesk = emailMovidesk;
    }

    public String getEmailMovidesk() {
        return emailMovidesk;
    }

    public boolean isUsarConciliadora() {
        return usarConciliadora;
    }

    public void setUsarConciliadora(boolean usarConciliadora) {
        this.usarConciliadora = usarConciliadora;
    }

    public String getEmpresaConciliadora() {
        if (empresaConciliadora == null) {
            empresaConciliadora = "";
        }
        return empresaConciliadora;
    }

    public void setEmpresaConciliadora(String empresaConciliadora) {
        this.empresaConciliadora = empresaConciliadora;
    }

    public String getSenhaConciliadora() {
        if (senhaConciliadora == null) {
            senhaConciliadora = "";
        }
        return senhaConciliadora;
    }

    public void setSenhaConciliadora(String senhaConciliadora) {
        this.senhaConciliadora = senhaConciliadora;
    }

    public Integer getDiasParaRetirarRelFechamentoDeCaixa() {
        return diasParaRetirarRelFechamentoDeCaixa;
    }

    public void setDiasParaRetirarRelFechamentoDeCaixa(Integer diasParaRetirarRelFechamentoDeCaixa) {
        this.diasParaRetirarRelFechamentoDeCaixa = diasParaRetirarRelFechamentoDeCaixa;
    }

    public String getMoeda() {
        if (moeda == null) {
            moeda = "R$";
        }
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    public String getLocaleTexto() {
        if (localeTexto == null) {
            localeTexto = "pt_BR";
        }
        return localeTexto;
    }

    public void setLocaleTexto(String localeTexto) {
        this.localeTexto = localeTexto;
    }

    public Locale getLocale() {
        if (locale == null) {
            String[] partes = getLocaleTexto().split("_");
            try {
                switch (partes.length) {
                    case 1:
                        locale = new Locale(partes[0]);
                        break;
                    case 2:
                        locale = new Locale(partes[0], partes[1]);
                        break;
                    case 3:
                        locale = new Locale(partes[0], partes[1], partes[2]);
                        break;
                    default:
                        locale = Formatador.BRASIL;
                        break;
                }
            } catch (Exception e) {
                locale = Formatador.BRASIL;
                Uteis.logar(e, EmpresaVO.class);
            }
        }
        return locale;
    }

    public void validarDadosPJBankCadastroEmpresa() throws ConsistirException {
        if (valueConta == null) {
            throw new ConsistirException("Selecione uma conta para recebimento PJBank");
        }

        if (ddPjbank == null || ddPjbank == 0) {
            throw new ConsistirException("Informe um DD para integração PJBank");
        }

        if (fonePjbank == null || fonePjbank == 0) {
            throw new ConsistirException("Informe um telefone para integração PJBank");
        }
        if (UteisValidacao.emptyString(getEmailPjbank().trim())) {
            throw new ConsistirException("Informe o email para integração PJBank");
        }
        if (!UteisEmail.getValidEmail(getEmailPjbank())) {
            throw new ConsistirException("Informe um email válido para integração PJBank");
        }
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalNFSe() {
        if (configuracaoNotaFiscalNFSe == null) {
            configuracaoNotaFiscalNFSe = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalNFSe;
    }

    public void setConfiguracaoNotaFiscalNFSe(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFSe) {
        this.configuracaoNotaFiscalNFSe = configuracaoNotaFiscalNFSe;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalNFCe() {
        if (configuracaoNotaFiscalNFCe == null) {
            configuracaoNotaFiscalNFCe = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalNFCe;
    }

    public void setConfiguracaoNotaFiscalNFCe(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFCe) {
        this.configuracaoNotaFiscalNFCe = configuracaoNotaFiscalNFCe;
    }

    public boolean isUsarNomeResponsavelNFCe() {
        return usarNomeResponsavelNFCe;
    }

    public void setUsarNomeResponsavelNFCe(boolean usarNomeResponsavelNFCe) {
        this.usarNomeResponsavelNFCe = usarNomeResponsavelNFCe;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EmpresaVO empresaVO = (EmpresaVO) o;
        return permitirEstornarContratoComParcelasPG == empresaVO.permitirEstornarContratoComParcelasPG;
    }

    @Override
    public int hashCode() {
        return Objects.hash(permitirEstornarContratoComParcelasPG);
    }

    public boolean isApenasPrimeiroAcessoClubeVantagens() {
        return apenasPrimeiroAcessoClubeVantagens;
    }

    public void setApenasPrimeiroAcessoClubeVantagens(boolean apenasPrimeiroAcessoClubeVantagens) {
        this.apenasPrimeiroAcessoClubeVantagens = apenasPrimeiroAcessoClubeVantagens;
    }

    public Integer getMinutosCreditarProximoPontoClubeVantagens() {
        if (minutosCreditarProximoPontoClubeVantagens == null) {
            minutosCreditarProximoPontoClubeVantagens = 0;
        }
        return minutosCreditarProximoPontoClubeVantagens;
    }

    public void setMinutosCreditarProximoPontoClubeVantagens(Integer minutosCreditarProximoPontoClubeVantagens) {
        this.minutosCreditarProximoPontoClubeVantagens = minutosCreditarProximoPontoClubeVantagens;
    }

    public TemporalRemocaoPontoEnum getZerarPontosAposVencimento() {
        if (zerarPontosAposVencimento == null) {
            zerarPontosAposVencimento = TemporalRemocaoPontoEnum.NUNCA;
        }
        return zerarPontosAposVencimento;
    }

    public void setZerarPontosAposVencimento(TemporalRemocaoPontoEnum zerarPontosAposVencimento) {
        this.zerarPontosAposVencimento = zerarPontosAposVencimento;
    }

    public boolean isAplicarIndicacaoQlqrPlano() {
        return aplicarIndicacaoQlqrPlano;
    }

    public void setAplicarIndicacaoQlqrPlano(boolean aplicarIndicacaoQlqrPlano) {
        this.aplicarIndicacaoQlqrPlano = aplicarIndicacaoQlqrPlano;
    }

    public boolean isPontuarApenasCategoriasEmCampanhasAtivas() {
        return pontuarApenasCategoriasEmCampanhasAtivas;
    }

    public void setPontuarApenasCategoriasEmCampanhasAtivas(boolean pontuarApenasCategoriasEmCampanhasAtivas) {
        this.pontuarApenasCategoriasEmCampanhasAtivas = pontuarApenasCategoriasEmCampanhasAtivas;
    }

    public String getTipoProdutoEmissaoNFSe() {
        if (tipoProdutoEmissaoNFSe == null) {
            tipoProdutoEmissaoNFSe = TIPOS_PRODUTOS_EMITIR_NFSe_PADRAO;
        }
        return tipoProdutoEmissaoNFSe;
    }

    public void setTipoProdutoEmissaoNFSe(String tipoProdutoEmissaoNFSe) {
        this.tipoProdutoEmissaoNFSe = tipoProdutoEmissaoNFSe;
    }

    public String getTipoProdutoEmissaoNFCe() {
        if (tipoProdutoEmissaoNFCe == null) {
            tipoProdutoEmissaoNFCe = "";
        }
        return tipoProdutoEmissaoNFCe;
    }

    public void setTipoProdutoEmissaoNFCe(String tipoProdutoEmissaoNFCe) {
        this.tipoProdutoEmissaoNFCe = tipoProdutoEmissaoNFCe;
    }

    public String getDescMoeda() {
        if (descMoeda == null) {
            descMoeda = "Real";
        }
        return descMoeda;
    }

    public void setDescMoeda(String descMoeda) {
        this.descMoeda = descMoeda;
    }

    public boolean isEmpresaInternacional() {
        return isEmpresaInternacional;
    }

    public void setEmpresaInternacional(boolean empresaInternacional) {
        isEmpresaInternacional = empresaInternacional;
    }

    public String getURLWebHookGympass() {
        if (URLWebHookGympass == null) {
            if (JSFUtilities.isJSFContext()) {
                URLWebHookGympass = "https://ms1.pactosolucoes.com.br/gympass/webhook";
            } else {
                URLWebHookGympass = "";
            }
        }
        return URLWebHookGympass;
    }

    public boolean isNaoCobrarMultaDeContratoRenovado() {
        return naoCobrarMultaDeContratoRenovado;
    }

    public void setNaoCobrarMultaDeContratoRenovado(boolean naoCobrarMultaDeContratoRenovado) {
        this.naoCobrarMultaDeContratoRenovado = naoCobrarMultaDeContratoRenovado;
    }

    public boolean isNaoCobrarMultaDeTodasParcelasPagas() {
        return naoCobrarMultaDeTodasParcelasPagas;
    }

    public void setNaoCobrarMultaDeTodasParcelasPagas(boolean naoCobrarMultaDeTodasParcelasPagas) {
        this.naoCobrarMultaDeTodasParcelasPagas = naoCobrarMultaDeTodasParcelasPagas;
    }

    public boolean isMostrarValoresZeradosRel() {
        return mostrarValoresZeradosRel;
    }

    public void setMostrarValoresZeradosRel(boolean mostrarValoresZeradosRel) {
        this.mostrarValoresZeradosRel = mostrarValoresZeradosRel;
    }

    public boolean isEmitirDuplicataNFSe() {
        return emitirDuplicataNFSe;
    }

    public void setEmitirDuplicataNFSe(boolean emitirDuplicataNFSe) {
        this.emitirDuplicataNFSe = emitirDuplicataNFSe;
    }

    public boolean isEmiteValorTotalCompetencia() {
        return emiteValorTotalCompetencia;
    }

    public void setEmiteValorTotalCompetencia(boolean emiteValorTotalCompetencia) {
        this.emiteValorTotalCompetencia = emiteValorTotalCompetencia;
    }
    public boolean isUsaEnotas() {
        return usaEnotas;
    }

    public void setUsaEnotas(boolean usaEnotas) {
        this.usaEnotas = usaEnotas;
    }

    public boolean isUsaNotasDelphi() {
        return usaNotasDelphi;
    }

    public void setUsaNotasDelphi(boolean usaNotasDelphi) {
        this.usaNotasDelphi = usaNotasDelphi;
    }

    public boolean isIntegracaoSpiviHabilitada() {
        return integracaoSpiviHabilitada;
    }

    public void setIntegracaoSpiviHabilitada(final boolean integracaoSpiviHabilitada) {
        this.integracaoSpiviHabilitada = integracaoSpiviHabilitada;
    }

    public String getIntegracaoSpiviSourceName() {
        return integracaoSpiviSourceName;
    }

    public void setIntegracaoSpiviSourceName(final String integracaoSpiviSourceName) {
        this.integracaoSpiviSourceName = integracaoSpiviSourceName;
    }

    public Integer getIntegracaoSpiviSiteID() {
        if (integracaoSpiviSiteID == null) {
            integracaoSpiviSiteID = 0;
        }
        return integracaoSpiviSiteID;
    }

    public void setIntegracaoSpiviSiteID(final Integer integracaoSpiviSiteID) {
        this.integracaoSpiviSiteID = integracaoSpiviSiteID;
    }

    public String getIntegracaoSpiviPassword() {
        return integracaoSpiviPassword;
    }

    public void setIntegracaoSpiviPassword(final String integracaoSpiviPassword) {
        this.integracaoSpiviPassword = integracaoSpiviPassword;
    }

    public boolean isUtilizarDataCancelamentoParaValidarParcelas() {
        return utilizarDataCancelamentoParaConsultarParcelas;
    }

    public void setUtilizarDataCancelamentoParaValidarParcelas(boolean utilizarDataCancelamentoParaConsultarParcelas) {
        this.utilizarDataCancelamentoParaConsultarParcelas = utilizarDataCancelamentoParaConsultarParcelas;
    }

    public Integer getDdPjbank() {
        return ddPjbank;
    }

    public void setDdPjbank(Integer ddPjbank) {
        this.ddPjbank = ddPjbank;
    }

    public Integer getFonePjbank() {
        return fonePjbank;
    }

    public void setFonePjbank(Integer fonePjbank) {
        this.fonePjbank = fonePjbank;
    }

    public Integer getValueConta() {
        return valueConta;
    }

    public void setValueConta(Integer valueConta) {
        this.valueConta = valueConta;
    }

    public boolean isEnviarApenasAlunosAtivosTW() {
        return enviarApenasAlunosAtivosTW;
    }

    public void setEnviarApenasAlunosAtivosTW(boolean enviarApenasAlunosAtivosTW) {
        this.enviarApenasAlunosAtivosTW = enviarApenasAlunosAtivosTW;
    }


    public Integer getCodEmpresaFinanceiro() {
        return codEmpresaFinanceiro;
    }

    public void setCodEmpresaFinanceiro(Integer codEmpresaFinanceiro) {
        this.codEmpresaFinanceiro = codEmpresaFinanceiro;
    }

    public String getResponsavelFinanceiro() {
        if(responsavelFinanceiro == null){
            responsavelFinanceiro = "";
        }
        return responsavelFinanceiro;
    }

    public void setResponsavelFinanceiro(String responsavelFinanceiro) {
        this.responsavelFinanceiro = responsavelFinanceiro;
    }

    public String getGestor() {
        if(gestor == null){
            gestor = "";
        }
        return gestor;
    }

    public void setGestor(String gestor) {
        this.gestor = gestor;
    }

    public boolean isAtualizarDadosCadastro() {
        return atualizarDadosCadastro;
    }

    public void setAtualizarDadosCadastro(boolean atualizarDadosCadastro) {
        this.atualizarDadosCadastro = atualizarDadosCadastro;
    }

    public ClassificacaoNegocioEnum getClassificacaoNegocioEnum() {
        return classificacaoNegocioEnum;
    }

    public void setClassificacaoNegocioEnum(ClassificacaoNegocioEnum classificacaoNegocioEnum) {
        this.classificacaoNegocioEnum = classificacaoNegocioEnum;
    }

    public boolean isZerarValorCancelamentoTransferencia() {
        return zerarValorCancelamentoTransferencia;
    }

    public void setZerarValorCancelamentoTransferencia(boolean zerarValorCancelamentoTransferencia) {
        this.zerarValorCancelamentoTransferencia = zerarValorCancelamentoTransferencia;
    }

    public Integer getCreditoDCCBonus() {
        if (creditoDCCBonus == null) {
            creditoDCCBonus = 0;
        }
        return creditoDCCBonus;
    }

    public void setCreditoDCCBonus(Integer creditoDCCBonus) {
        this.creditoDCCBonus = creditoDCCBonus;
    }

    public Boolean getBloquearAcessoSemAssinaturaDigital() {
        if (bloquearAcessoSemAssinaturaDigital == null) {
            bloquearAcessoSemAssinaturaDigital = false;
        }
        return bloquearAcessoSemAssinaturaDigital;
    }

    public void setBloquearAcessoSemAssinaturaDigital(Boolean bloquearAcessoSemAssinaturaDigital) {
        this.bloquearAcessoSemAssinaturaDigital = bloquearAcessoSemAssinaturaDigital;
    }

    public Boolean getBloquearAcessoDiariaEmpresaDiferente() {
        if (bloquearAcessoDiariaEmpresaDiferente == null) {
            bloquearAcessoDiariaEmpresaDiferente = false;
        }
        return bloquearAcessoDiariaEmpresaDiferente;
    }

    public void setBloquearAcessoDiariaEmpresaDiferente(Boolean bloquearAcessoDiariaEmpresaDiferente) {
        this.bloquearAcessoDiariaEmpresaDiferente = bloquearAcessoDiariaEmpresaDiferente;
    }

    public boolean isCobrarCreditoVindi() {
        return cobrarCreditoVindi;
    }

    public void setCobrarCreditoVindi(boolean cobrarCreditoVindi) {
        this.cobrarCreditoVindi = cobrarCreditoVindi;
    }

    public Date getSincronizacaoFinanceiro() {
        return sincronizacaoFinanceiro;
    }

    public void setSincronizacaoFinanceiro(Date sincronizacaoFinanceiro) {
        this.sincronizacaoFinanceiro = sincronizacaoFinanceiro;
    }

    public String getSincronizacaoFinanceiro_Apresentar() {
        if (getSincronizacaoFinanceiro() == null) {
            return "Ainda não foi sincronizado";
        }
        return "Sincronizado em " + Uteis.getDataComHora(getSincronizacaoFinanceiro());
    }

    public String getQtdDiasLimiteCobrancaParcelasRecorrencia_Title() {
        Date dia = Calendario.hoje();
        StringBuilder title = new StringBuilder();
        title.append("Será realizado a tentativa de cobrança das parcelas que tem data de vencimento entre HOJE e X dias atrás.<br/>");
        title.append("Se configurado 0 (zero) dias o sistema irá cobrar somente as parcelas com vencimento no dia ").append(Uteis.getData(dia)).append(" (Hoje).<br/><br/>");
        title.append("Por questões de boas práticas de cobrança, não é possível informar um valor maior que \"95\".");
        return title.toString();
    }

    public Integer getQtdDiasLimiteCobrancaParcelasRecorrencia() {
        if (qtdDiasLimiteCobrancaParcelasRecorrencia == null) {
            qtdDiasLimiteCobrancaParcelasRecorrencia = 0;
        }
        return qtdDiasLimiteCobrancaParcelasRecorrencia;
    }

    public void setQtdDiasLimiteCobrancaParcelasRecorrencia(Integer qtdDiasLimiteCobrancaParcelasRecorrencia) {
        this.qtdDiasLimiteCobrancaParcelasRecorrencia = qtdDiasLimiteCobrancaParcelasRecorrencia;
    }

    public String getQtdDiasRepetirCobrancaParcelasRecorrencia_Title() {
//        Intervalo de dias para repetir transa\u00E7\u00F5es de parcelas n\u00E3o aprovadas (0 \= desabilitado)
        StringBuilder title = new StringBuilder();
        title.append("Se configurado 3 dias por exemplo, caso a parcela não seja aprovada, será realizado uma nova tentativa de cobrança após 3 dias.<br/>");
        title.append("Caso queira desativar a retentativa automática utilize a configuração <b>\"Tentar cobrar somente 1 vez automaticamente\"</b><br/>nas configurações da empresa na aba \"Configurações da Empresa\".<br/><br/>");
        title.append("Por questões de boas práticas de cobrança, não é possível informar um valor menor que '3'.");
        return title.toString();
    }

    public Integer getQtdDiasRepetirCobrancaParcelasRecorrencia() {
        if (qtdDiasRepetirCobrancaParcelasRecorrencia == null) {
            qtdDiasRepetirCobrancaParcelasRecorrencia = 0;
        }
        return qtdDiasRepetirCobrancaParcelasRecorrencia;
    }

    public void setQtdDiasRepetirCobrancaParcelasRecorrencia(Integer qtdDiasRepetirCobrancaParcelasRecorrencia) {
        this.qtdDiasRepetirCobrancaParcelasRecorrencia = qtdDiasRepetirCobrancaParcelasRecorrencia;
    }

    public String getNomeApresentar() {
        return nomeApresentar;
    }

    public void setNomeApresentar(String nomeApresentar) {
        this.nomeApresentar = nomeApresentar;
    }

    public boolean isTentativaUnicaDeCobranca() {
        return tentativaUnicaDeCobranca;
    }

    public void setTentativaUnicaDeCobranca(boolean tentativaUnicaDeCobranca) {
        this.tentativaUnicaDeCobranca = tentativaUnicaDeCobranca;
    }

    public boolean isMostrarDescricaoParcelaRenegociada() {
        return mostrarDescricaoParcelaRenegociada;
    }

    public void setMostrarDescricaoParcelaRenegociada(boolean mostrarDescricaoParcelaRenegociada) {
        this.mostrarDescricaoParcelaRenegociada = mostrarDescricaoParcelaRenegociada;
    }

    public boolean isAcessoSomenteComAgendamento() {
        return acessoSomenteComAgendamento;
    }

    public void setAcessoSomenteComAgendamento(boolean acessoSomenteComAgendamento) {
        this.acessoSomenteComAgendamento = acessoSomenteComAgendamento;
    }

    public Integer getCapacidadeSimultanea() {
        if (capacidadeSimultanea == null) {
            capacidadeSimultanea = 0;
        }
        return capacidadeSimultanea;
    }

    public void setCapacidadeSimultanea(Integer capacidadeSimultanea) {
        this.capacidadeSimultanea = capacidadeSimultanea;
    }

    public Integer getToleranciaAcessoAula() {
        if (toleranciaAcessoAula == null) {
            toleranciaAcessoAula = 0;
        }
        return toleranciaAcessoAula;
    }

    public void setToleranciaAcessoAula(Integer toleranciaAcessoAula) {
        this.toleranciaAcessoAula = toleranciaAcessoAula;
    }

    public int getTotalDiasExtras() {
        return totalDiasExtras;
    }

    public void setTotalDiasExtras(int totalDiasExtras) {
        this.totalDiasExtras = totalDiasExtras;
    }

    public Date getDataSuspensao() {
        return dataSuspensao;
    }

    public void setDataSuspensao(Date dataSuspensao) {
        this.dataSuspensao = dataSuspensao;
    }

    public boolean isUtilizaLeitorCodigoBarras() {
        return utilizaLeitorCodigoBarras;
    }

    public void setUtilizaLeitorCodigoBarras(boolean utilizaLeitorCodigoBarras) {
        this.utilizaLeitorCodigoBarras = utilizaLeitorCodigoBarras;
    }

    public String getNomeUsuarioAmigoFit() {
        return nomeUsuarioAmigoFit;
    }

    public void setNomeUsuarioAmigoFit(String nomeUsuarioAmigoFit) {
        this.nomeUsuarioAmigoFit = nomeUsuarioAmigoFit;
    }

    public String getSenhaUsuarioAmigoFit() {
        return senhaUsuarioAmigoFit;
    }

    public void setSenhaUsuarioAmigoFit(String senhaUsuarioAmigoFit) {
        this.senhaUsuarioAmigoFit = senhaUsuarioAmigoFit;
    }

    public boolean isIntegracaoMyWellnessHabilitada() {
        return integracaoMyWellnessHabilitada;
    }

    public void setIntegracaoMyWellnessHabilitada(boolean integracaoMyWellnessHabilitada) {
        this.integracaoMyWellnessHabilitada = integracaoMyWellnessHabilitada;
    }

    public String getIntegracaoMyWellnessApiKey() {
        if(integracaoMyWellnessApiKey == null){
            integracaoMyWellnessApiKey = "";
        }
        return integracaoMyWellnessApiKey;
    }

    public void setIntegracaoMyWellnessApiKey(String integracaoMyWellnessApiKey) {
        this.integracaoMyWellnessApiKey = integracaoMyWellnessApiKey;
    }

    public String getIntegracaoMyWellnessUser() {
        if(integracaoMyWellnessUser == null){
            integracaoMyWellnessUser = "";
        }
        return integracaoMyWellnessUser;
    }

    public void setIntegracaoMyWellnessUser(String integracaoMyWellnessUser) {
        this.integracaoMyWellnessUser = integracaoMyWellnessUser;
    }

    public String getIntegracaoMyWellnessPassword() {
        if(integracaoMyWellnessPassword == null){
            integracaoMyWellnessPassword = "";
        }
        return integracaoMyWellnessPassword;
    }

    public void setIntegracaoMyWellnessPassword(String integracaoMyWellnessPassword) {
        this.integracaoMyWellnessPassword = integracaoMyWellnessPassword;
    }

    public String getIntegracaoMyWellnessFacilityUrl() {
        if(integracaoMyWellnessFacilityUrl == null){
            integracaoMyWellnessFacilityUrl = "";
        }
        return integracaoMyWellnessFacilityUrl;
    }

    public void setIntegracaoMyWellnessFacilityUrl(String integracaoMyWellnessFacilityUrl) {
        this.integracaoMyWellnessFacilityUrl = integracaoMyWellnessFacilityUrl;
    }

    public boolean isAgruparParcelasPorCartao() {
        return agruparParcelasPorCartao;
    }

    public void setAgruparParcelasPorCartao(boolean agruparParcelasPorCartao) {
        this.agruparParcelasPorCartao = agruparParcelasPorCartao;
    }

    public Double getAgruparParcelasPorCartaoValorLimite() {
        if (agruparParcelasPorCartaoValorLimite == null) {
            agruparParcelasPorCartaoValorLimite = 0.0;
        }
        return agruparParcelasPorCartaoValorLimite;
    }

    public void setAgruparParcelasPorCartaoValorLimite(Double agruparParcelasPorCartaoValorLimite) {
        this.agruparParcelasPorCartaoValorLimite = agruparParcelasPorCartaoValorLimite;
    }

    public boolean isSomenteUmEnvioCartaoTentativa() {
        return somenteUmEnvioCartaoTentativa;
    }

    public void setSomenteUmEnvioCartaoTentativa(boolean somenteUmEnvioCartaoTentativa) {
        this.somenteUmEnvioCartaoTentativa = somenteUmEnvioCartaoTentativa;
    }
    public String getCadastrarCartaoMesmoAssimTitle() {
        StringBuilder title = new StringBuilder();
        title.append("Sabe-se que o sistema faz uma verificação todas as vezes que se inclui um cartão, tentando efetuar uma cobrança teste no cartão informado.");
        title.append("<br/>Habilitado, o sistema dará ao usuário a decisão se vai incluir cartões que tiveram a cobrança de teste negada.");
        title.append("<br/>Desabilitado, o sistema não irá permitir a inclusão de cartões que tiveram a cobrança de teste negada.");
        return title.toString();
    }

    public String getBloqueioMaisDeUmaParcelaMesmoCartaoTitle() {
        StringBuilder title = new StringBuilder();
        title.append("<b>CONFIGURAÇÃO VÁLIDA SOMENTE PARA TRANSAÇÃO ONLINE</b>");
        title.append("<br/><br/>A PACTO recomenda que você utilize essa configuração HABILITADA, pois você garante que o seu score no antifraude da sua adquirente não diminua");
        title.append("<br/>devido ao alto número de tentativas em um mesmo cartão de crédito e consequentemente que essas cobranças não caiam no antifraude deles.");
        title.append("<br/>Essa configuração é válida somente para transações online pois para as remessas EDI o sistema sempre irá enviar todas as parcelas dentro da mesma remessa.");
        title.append("<br/><br/>Com essa configuração habilitada, no momento em que o processo automático de cobranças for processar, o sistema irá verificar se existe mais de");
        title.append("<br/>uma parcela para o mesmo cartão. Caso ele encontre, então será realizado a tentativa de cobrança da primeira parcela identificada, já as demais serão");
        title.append("<br/>cobradas nas próximas vezes em que o processo automático for processado.");
        title.append("<br/><b>EX:</b> Aluno A possui um determinado número de cartão de crédito X na sua autorização de cobrança e o Aluno B também possui esse mesmo número de cartão em");
        title.append("<br/>sua autorização de cobrança. Supondo que ambos os alunos possuem uma parcela vencendo hoje (cada um), no momento de realizar as cobranças, o sistema");
        title.append("<br/>irá identificar que existe mais de uma parcela para o mesmo cartão e realizará a tentativa de cobrança de uma delas, já a segunda parcela será cobrada");
        title.append("<br/>somente na próxima vez em que o processo automático for processado.");
        title.append("<br/><br/><b>Como vou saber que a parcela não foi cobrada por esse motivo?</b>");
        title.append("<br/>Mesmo que o sistema não envie essa cobrança para a operadora pelo motivo de ter mais de uma parcela com o mesmo cartão, será criado uma transação");
        title.append("<br/>onde você conseguirá ver na cor amarela o motivo do retorno <b>").append(CodigoRetornoPactoEnum.BLOQUEIO_AGUARDAR.getCodigo()).append("</b> e ao passar o mouse em cima, o sistema irá exibir a seguinte mensagem:");
        title.append("<br/>\"Bloqueio devido configuração de somente uma tentativa por cartão em cada processo automático.\"");
        title.append("<br/>Essa transação poderá ser verificada através do \"Gestão de Transações\" ou no perfil do próprio aluno, através da aba \"Cobranças\".");
        title.append("<br/><br/><b>Observação:</b> O processo de cobrança automática ocorre 2 vezes ao dia, às 12:30 e às 22:00.");
        title.append("<br/>Se você configurar \"Definir intervalo de dias para repetir parcela não aprovada\" lá na aba de retentativas, saiba que o sistema respeitará essa configuração");
        title.append("<br/> tentando novamente somente X dias depois e não já no próximo processo como mencionado anteriormente.");
        title.append("<br/><br/><b>Essa configuração só pode ser vista e alterada pelos usuários ADMIN e PACTOBR e recomenda-se deixar sempre HABILITADA</b>");
        return title.toString();
    }

    public String getAgruparCobrancasMesmoCartaoTitle() {
        StringBuilder title = new StringBuilder();
        title.append("<b>CONFIGURAÇÃO VÁLIDA SOMENTE PARA TRANSAÇÃO ONLINE</b>");
        title.append("<br/><br/>A PACTO recomenda que você utilize essa configuração HABILITADA, pois você garante que o seu score no antifraude da sua adquirente não diminua devido ao");
        title.append("<br/>alto número de tentativas em um mesmo cartão de crédito e consequentemente que essas cobranças não caiam no antifraude deles.");
        title.append("<br/>Essa configuração é válida somente para transações online pois para as remessas EDI o sistema sempre irá agrupar todas as parcelas do mesmo cartão dentro");
        title.append("<br/>da mesma remessa");
        title.append("<br/><br/>Com essa configuração habilitada, no momento em que o processo automático de cobranças for processar, o sistema irá verificar se existe mais de");
        title.append("<br/>uma parcela para o mesmo cartão. Caso ele encontre, irá agrupar todas essas parcelas e irá realizar <b>UMA</b> tentativa do montante total da soma dos valores dessas parcelas.");
        title.append("<br/><b>EX:</b> Sistema identificou que é para cobrar a Parcela 1 de R$100,00 que pertence ao aluno A e a Parcela 4 de R$50,00 que pertence ao aluno B. Sistema identificou que o ");
        title.append("<br/>cartão de crédito dos dois alunos é o mesmo. No momento de realizar a transação, será criado UMA transação de R$150,00 que é o montante da soma das 2 parcelas.\n");
        title.append("<br/><br/><b>Será considerado parcelas do mesmo cartão somente de alunos diferentes?</b>");
        title.append("<br/>Não, essa configuração também vale para mais de uma parcela sendo elas do mesmo aluno.");
        title.append("<br/><br/><b>RECOMENDAÇÃO:</b>");
        title.append("<br/><b>1-</b>Ao marcar/utilizar essa configuração, irá ser exibido um novo campo logo abaixo, onde você deverá indicar um valor limite para este agrupamento de parcelas, pois caso");
        title.append("<br/>o sistema encontre por exemplo 4 ou mais parcelas para este mesmo cartão e agrupe tudo, a chance de o aluno não ter o limite no cartão para ser cobrado o montante é alta,");
        title.append("<br/> portanto recomendamos que defina um valor limite na configuração abaixo.</b>");
        title.append("<br/><br/><b>2-</b> Recomendamos que você utilize esta configuração juntamente com a anterior (\"Quando houver mais de uma parcela para o mesmo cartão, enviar na próxima tentativa\"");
        title.append("<br/>pois desta forma, o sistema irá respeitar o limite do valor agrupamento que você definir e ainda enviará a próxima tentativa do restante somente na próxima vez em que o");
        title.append("<br/>processamento automático de cobranças for processar, desta forma não irá correr riscos de cair no antifraude da sua adquirente por mais de uma tentativa no mesmo cartão.");
        return title.toString();
    }
    public String getValorMaximoAgrupamentoTitle() {
        StringBuilder title = new StringBuilder();
        title.append("<b>CONFIGURAÇÃO VÁLIDA SOMENTE PARA TRANSAÇÃO ONLINE</b>");
        title.append("<br/><br/>Defina um valor máximo para o agrupamento das cobranças das parcelas de um mesmo cartão de crédito.");
        title.append("<br/>A PACTO recomenda que você <b>não utilize um valor limite alto</b> nessa configuração, pois quanto maior o valor informado, maior a chance do seu aluno não ter limite no cartão");
        title.append("<br/>de crédito para cobrar todo o montante do agrupamento.");
        title.append("<br/><br/><b>EXEMPLO:</b>");
        title.append("<br/>Sistema identificou que é para cobrar a Parcela 1 de R$100,00 que pertence ao aluno A, a Parcela 4 de R$50,00 que pertence ao aluno B e a PARCELA 3 de R$50,00 que pertence");
        title.append("<br/>ao aluno C. O Sistema também identificou que o cartão de crédito dos três alunos é o mesmo.");
        title.append("<br/><b>1-</b> Se você definir o valor limite de R$200,00 por exemplo, no momento de realizar a transação, será criado UMA transação de R$200,00 que é o montante da soma das 3 parcelas. ");
        title.append("<br/><b>2-</b> Se você definir o valor limite de R$170,00 por exemplo, no momento de realizar a transação, será criado UMA transação de R$150,00 que é o montante da soma das 2 parcelas");
        title.append("<br/>e mais uma outra transação de R$50,00 que é o valor que não se adequou com o valor limite do agrupamento.");
        return title.toString();
    }

    public boolean isIntegracaoMyWellnessEnviarVinculos() {
        return integracaoMyWellnessEnviarVinculos;
    }

    public void setIntegracaoMyWellnessEnviarVinculos(boolean integracaoMyWellnessEnviarVinculos) {
        this.integracaoMyWellnessEnviarVinculos = integracaoMyWellnessEnviarVinculos;
    }

    public boolean isIntegracaoMyWellnessEnviarGrupos() {
        return integracaoMyWellnessEnviarGrupos;
    }

    public void setIntegracaoMyWellnessEnviarGrupos(boolean integracaoMyWellnessEnviarGrupos) {
        this.integracaoMyWellnessEnviarGrupos = integracaoMyWellnessEnviarGrupos;
    }

    public ConfigCobrancaMensalJSON getConfigCobrancaMensalJSON() {
        if (configCobrancaMensalJSON == null) {
            configCobrancaMensalJSON = new ConfigCobrancaMensalJSON();
        }
        return configCobrancaMensalJSON;
    }

    public void setConfigCobrancaMensalJSON(ConfigCobrancaMensalJSON configCobrancaMensalJSON) {
        this.configCobrancaMensalJSON = configCobrancaMensalJSON;
    }

    public Integer getDiaVencimentoCobrancaPacto() {
        if (diaVencimentoCobrancaPacto == null) {
            diaVencimentoCobrancaPacto = 0;
        }
        return diaVencimentoCobrancaPacto;
    }

    public void setDiaVencimentoCobrancaPacto(Integer diaVencimentoCobrancaPacto) {
        this.diaVencimentoCobrancaPacto = diaVencimentoCobrancaPacto;
    }

    public boolean isEmpresaResponsavelCobrancaPacto() {
        return empresaResponsavelCobrancaPacto;
    }

    public void setEmpresaResponsavelCobrancaPacto(boolean empresaResponsavelCobrancaPacto) {
        this.empresaResponsavelCobrancaPacto = empresaResponsavelCobrancaPacto;
    }

    public boolean isTemBloqueioModulo() {
        return getDataExpiracaoApp() != null ||
                getDataExpiracaoCreditoDCC() != null ||
                getDataExpiracaoNFe() != null ||
                getDataExpiracaoVendasOnline() != null;
    }

    public List<ConfiguracaoReenvioMovParcelaEmpresaVO> getConfiguracaoReenvioMovParcelaEmpresaVOSAntesAlteracao() {
        return configuracaoReenvioMovParcelaEmpresaVOSAntesAlteracao;
    }

    public void setConfiguracaoReenvioMovParcelaEmpresaVOSAntesAlteracao(List<ConfiguracaoReenvioMovParcelaEmpresaVO> configuracaoReenvioMovParcelaEmpresaVOSAntesAlteracao) {
        this.configuracaoReenvioMovParcelaEmpresaVOSAntesAlteracao = configuracaoReenvioMovParcelaEmpresaVOSAntesAlteracao;
    }

    public void registrarConfiguracaoReenvioMovParcelaEmpresaVOSAntesAlteracao() throws Exception {
        configuracaoReenvioMovParcelaEmpresaVOSAntesAlteracao = new ArrayList<>();
        for (ConfiguracaoReenvioMovParcelaEmpresaVO obj : this.getConfiguracaoReenvioMovParcelaEmpresaVOS()) {
            configuracaoReenvioMovParcelaEmpresaVOSAntesAlteracao.add((ConfiguracaoReenvioMovParcelaEmpresaVO) obj.getClone(true));
        }
    }

    public Double getValorLimiteCaixaAbertoVendaAvulsa() {
        if (valorLimiteCaixaAbertoVendaAvulsa == null) {
            valorLimiteCaixaAbertoVendaAvulsa = 0.0;
        }
        return valorLimiteCaixaAbertoVendaAvulsa;
    }

    public void setValorLimiteCaixaAbertoVendaAvulsa(Double valorLimiteCaixaAbertoVendaAvulsa) {
        this.valorLimiteCaixaAbertoVendaAvulsa = valorLimiteCaixaAbertoVendaAvulsa;
    }

    public boolean isNotificarWebhook() {
        return notificarWebhook;
    }

    public void setNotificarWebhook(boolean notificarWebhook) {
        this.notificarWebhook = notificarWebhook;
    }

    public String getUrlWebhookNotificar() {
        if (urlWebhookNotificar == null) {
            urlWebhookNotificar = "";
        }
        return urlWebhookNotificar;
    }

    public void setUrlWebhookNotificar(String urlWebhookNotificar) {
        this.urlWebhookNotificar = urlWebhookNotificar;
    }

    public EmpresaDTO toEmpresaDTO() {
        EmpresaDTO empresaDTO = new EmpresaDTO();
        empresaDTO.setNomeFantasia(this.getNome());
        empresaDTO.setRazaoSocial(this.getRazaoSocial());
        empresaDTO.setCnpj(this.getCNPJ());
        empresaDTO.setEmail(this.getEmail());
        empresaDTO.setIdReferencia(this.getCodigo());

        EnderecoDTO enderecoDTO = new EnderecoDTO();
        enderecoDTO.setCep(this.getCEP());
        enderecoDTO.setEndereco(this.getEndereco());
        enderecoDTO.setNumero(this.getNumero());
        enderecoDTO.setComplemento(this.getComplemento());
        enderecoDTO.setBairro(this.getSetor());
        enderecoDTO.setCidade(this.getCidade_Apresentar());
        enderecoDTO.setUf(this.getEstadoSigla());
        enderecoDTO.setPais(this.getPais().getNome());
        empresaDTO.setEndereco(enderecoDTO);
        return empresaDTO;
    }

    public boolean isIntegracaoMentorWebHabilitada() {
        return integracaoMentorWebHabilitada;
    }

    public void setIntegracaoMentorWebHabilitada(boolean integracaoMentorWebHabilitada) {
        this.integracaoMentorWebHabilitada = integracaoMentorWebHabilitada;
    }

    public String getIntegracaoMentorWebUrl() {
        if(integracaoMentorWebUrl == null) {
            integracaoMentorWebUrl = "";
        }
        return integracaoMentorWebUrl;
    }

    public void setIntegracaoMentorWebUrl(String integracaoMentorWebUrl) {
        this.integracaoMentorWebUrl = integracaoMentorWebUrl;
    }

    public String getIntegracaoMentorWebServico() {
        if(integracaoMentorWebServico == null) {
            integracaoMentorWebServico = "";
        }
        return integracaoMentorWebServico;
    }

    public void setIntegracaoMentorWebServico(String integracaoMentorWebServico) {
        this.integracaoMentorWebServico = integracaoMentorWebServico;
    }

    public String getIntegracaoMentorWebUser() {
        if(integracaoMentorWebUser == null) {
            integracaoMentorWebUser = "";
        }
        return integracaoMentorWebUser;
    }

    public void setIntegracaoMentorWebUser(String integracaoMentorWebUser) {
        this.integracaoMentorWebUser = integracaoMentorWebUser;
    }

    public String getIntegracaoMentorWebPassword() {
        if(integracaoMentorWebPassword == null) {
            integracaoMentorWebPassword = "";
        }
        return integracaoMentorWebPassword;
    }

    public void setIntegracaoMentorWebPassword(String integracaoMentorWebPassword) {
        this.integracaoMentorWebPassword = integracaoMentorWebPassword;
    }

    public boolean isUsaVitio() {
        return usaVitio;
    }

    public Boolean getValidaUtilizacaoVitio() throws Exception {
        if(this.codigo != null) {
            return getFacade().getEmpresa().verificaSeUtilizaVitio(this.codigo);
        }
        return false;
    }

    public String getLinkCheckoutVitio() {
        return linkCheckoutVitio;
    }

    public void setLinkCheckoutVitio(String linkCheckoutVitio) {
        this.linkCheckoutVitio = linkCheckoutVitio;
    }

    public String getLinkEbook() {
        return linkEbook;
    }

    public void setLinkEbook(String linkEbook) {
        this.linkEbook = linkEbook;
    }

    public String getMensagemVitioQuerComprar() {
        return mensagemVitioQuerComprar;
    }

    public void setMensagemVitioQuerComprar(String mensagemVitioQuerComprar) {
        this.mensagemVitioQuerComprar = mensagemVitioQuerComprar;
    }

    public String getMensagemVitioWhatsapp() {
        return mensagemVitioWhatsapp;
    }

    public void setMensagemVitioWhatsapp(String mensagemVitioWhatsapp) {
        this.mensagemVitioWhatsapp = mensagemVitioWhatsapp;
    }

    public void setUsaVitio(boolean usaVitio) {
        this.usaVitio = usaVitio;
    }

    public boolean isIntegracaoF360RelFatHabilitada() {
        return integracaoF360RelFatHabilitada;
    }

    public void setIntegracaoF360RelFatHabilitada(boolean integracaoF360RelFatHabilitada) {
        this.integracaoF360RelFatHabilitada = integracaoF360RelFatHabilitada;
    }

    public String getIntegracaoF360FtpServer() {
        if(integracaoF360FtpServer == null) {
            integracaoF360FtpServer = "";
        }
        return integracaoF360FtpServer;
    }

    public void setIntegracaoF360FtpServer(String integracaoF360FtpServer) {
        this.integracaoF360FtpServer = integracaoF360FtpServer;
    }

    public Integer getIntegracaoF360FtpPort() {
        return integracaoF360FtpPort;
    }

    public void setIntegracaoF360FtpPort(Integer integracaoF360FtpPort) {
        this.integracaoF360FtpPort = integracaoF360FtpPort;
    }

    public String getIntegracaoF360User() {
        if(integracaoF360User == null) {
            integracaoF360User = "";
        }
        return integracaoF360User;
    }

    public void setIntegracaoF360User(String integracaoF360User) {
        this.integracaoF360User = integracaoF360User;
    }

    public String getIntegracaoF360Password() {
        try {
            return Uteis.desencriptar(PropsService.getPropertyValue(PropsService.senhaCriptFTP360PasswordServer),PropsService.getPropertyValue(PropsService.chaveCriptFTP360PasswordServer));
        } catch (Exception ex) {
            return "";
        }
    }

    public void setIntegracaoF360Password(String integracaoF360Password) {
        this.integracaoF360Password = integracaoF360Password;
    }

    public String getIntegracaoF360Dir() {
        return integracaoF360Dir;
    }

    public void setIntegracaoF360Dir(String integracaoF360Dir) {
        this.integracaoF360Dir = integracaoF360Dir;
    }

    public boolean isIntegracaoF360Quinzenal() {
        return integracaoF360Quinzenal;
    }

    public void setIntegracaoF360Quinzenal(boolean integracaoF360Quinzenal) {
        this.integracaoF360Quinzenal = integracaoF360Quinzenal;
    }

    public TipoVigenciaMyWellnessGymPassEnum getTipoVigenciaMyWellnessGymPass() {
        return tipoVigenciaMyWellnessGymPass;
    }

    public void setTipoVigenciaMyWellnessGymPass(TipoVigenciaMyWellnessGymPassEnum tipoVigenciaMyWellnessGymPass) {
        this.tipoVigenciaMyWellnessGymPass = tipoVigenciaMyWellnessGymPass;
    }

    public Integer getNrDiasVigenciaMyWellnessGymPass() {
        return nrDiasVigenciaMyWellnessGymPass;
    }

    public void setNrDiasVigenciaMyWellnessGymPass(Integer nrDiasVigenciaMyWellnessGymPass) {
        this.nrDiasVigenciaMyWellnessGymPass = nrDiasVigenciaMyWellnessGymPass;
    }

    public Integer getQtdCreditoPix() {
        if (qtdCreditoPix == null) {
            qtdCreditoPix = 0;
        }
        return qtdCreditoPix;
    }

    public void setQtdCreditoPix(Integer qtdCreditoPix) {
        this.qtdCreditoPix = qtdCreditoPix;
    }

    public boolean isIntegracaoAmigoFitHabilitada() {
        return integracaoAmigoFitHabilitada;
    }

    public void setIntegracaoAmigoFitHabilitada(boolean integracaoAmigoFitHabilitada) {
        this.integracaoAmigoFitHabilitada = integracaoAmigoFitHabilitada;
    }

    public boolean isPermiteCadastrarCartaoMesmoAssim() {
        return permiteCadastrarCartaoMesmoAssim;
    }

    public void setPermiteCadastrarCartaoMesmoAssim(boolean permiteCadastrarCartaoMesmoAssim) {
        this.permiteCadastrarCartaoMesmoAssim = permiteCadastrarCartaoMesmoAssim;
    }

    public ConvenioCobrancaVO getConvenioVerificacaoCartao() {
        if (convenioVerificacaoCartao == null) {
            convenioVerificacaoCartao = new ConvenioCobrancaVO();
        }
        return convenioVerificacaoCartao;
    }

    public void setConvenioVerificacaoCartao(ConvenioCobrancaVO convenioVerificacaoCartao) {
        this.convenioVerificacaoCartao = convenioVerificacaoCartao;
    }

    public Boolean getTransferida() {
        if (transferida == null) {
            transferida = false;
        }
        return transferida;
    }

    public void setTransferida(Boolean transferida) {
        this.transferida = transferida;
    }

    public String getNovaChaveTransferencia() {
        if (novaChaveTransferencia == null) {
            novaChaveTransferencia = "";
        }
        return novaChaveTransferencia;
    }

    public void setNovaChaveTransferencia(String novaChaveTransferencia) {
        this.novaChaveTransferencia = novaChaveTransferencia;
    }

    public Integer getNovoCodigoTransferencia() {
        if (novoCodigoTransferencia == null) {
            novoCodigoTransferencia = 0;
        }
        return novoCodigoTransferencia;
    }

    public void setNovoCodigoTransferencia(Integer novoCodigoTransferencia) {
        this.novoCodigoTransferencia = novoCodigoTransferencia;
    }

    public boolean isPermiteMaillingCriarBoleto() {
        return permiteMaillingCriarBoleto;
    }

    public void setPermiteMaillingCriarBoleto(boolean permiteMaillingCriarBoleto) {
        this.permiteMaillingCriarBoleto = permiteMaillingCriarBoleto;
    }

    public ConvenioCobrancaVO getConvenioCobrancaPix() {
        if (convenioCobrancaPix == null) {
            convenioCobrancaPix = new ConvenioCobrancaVO();
        }
        return convenioCobrancaPix;
    }

    public void setConvenioCobrancaPix(ConvenioCobrancaVO convenioCobrancaPix) {
        this.convenioCobrancaPix = convenioCobrancaPix;
    }

    public String getEmailPjbank() {
        if (emailPjbank == null) {
            emailPjbank = "";
        }
        return emailPjbank;
    }

    public void setEmailPjbank(String emailPjbank) {
        this.emailPjbank = emailPjbank;
    }

    public Boolean getBloquearSemCartaoVacina() {
        return bloquearSemCartaoVacina;
    }

    public void setBloquearSemCartaoVacina(Boolean bloquearSemCartaoVacina) {
        this.bloquearSemCartaoVacina = bloquearSemCartaoVacina;
    }

    public Integer getIdadeMinimaCartaoVacina() {
        return idadeMinimaCartaoVacina;
    }

    public void setIdadeMinimaCartaoVacina(Integer idadeMinimaCartaoVacina) {
        this.idadeMinimaCartaoVacina = idadeMinimaCartaoVacina;
    }

    public PessoaAnexoEnum getTipoAnexoCartaoVacina() {
        return tipoAnexoCartaoVacina;
    }

    public void setTipoAnexoCartaoVacina(PessoaAnexoEnum tipoAnexoCartaoVacina) {
        this.tipoAnexoCartaoVacina = tipoAnexoCartaoVacina;
    }

    public TipoEmpresaFinanceiro getTipoEmpresa() {
        return tipoEmpresa;
    }

    public void setTipoEmpresa(TipoEmpresaFinanceiro tipoEmpresa) {
        this.tipoEmpresa = tipoEmpresa;
    }

    public boolean isEnviarEmailPagamento() {
        return enviarEmailPagamento;
    }

    public void setEnviarEmailPagamento(boolean enviarEmailPagamento) {
        this.enviarEmailPagamento = enviarEmailPagamento;
    }

    public boolean isEnviarEmailPagamentoRegua() {
        return enviarEmailPagamentoRegua;
    }

    public void setEnviarEmailPagamentoRegua(boolean enviarEmailPagamentoRegua) {
        this.enviarEmailPagamentoRegua = enviarEmailPagamentoRegua;
    }

    public boolean isCobrarMultaJurosPix() {
        return cobrarMultaJurosPix;
    }

    public void setCobrarMultaJurosPix(boolean cobrarMultaJurosPix) {
        this.cobrarMultaJurosPix = cobrarMultaJurosPix;
    }

    public Boolean getRestringirConvidadoUmaVezPorMes() {
        if (restringirConvidadoUmaVezPorMes == null) {
            restringirConvidadoUmaVezPorMes = false;
        }
        return restringirConvidadoUmaVezPorMes;
    }

    public void setRestringirConvidadoUmaVezPorMes(Boolean restringirConvidadoUmaVezPorMes) {
        this.restringirConvidadoUmaVezPorMes = restringirConvidadoUmaVezPorMes;
    }

    public ProdutoVO getProdutoDiaPlus() {
        if (produtoDiaPlus == null) {
            produtoDiaPlus = new ProdutoVO();
        }
        return produtoDiaPlus;
    }

    public void setProdutoDiaPlus(ProdutoVO produtoDiaPlus) {
        this.produtoDiaPlus = produtoDiaPlus;
    }

    public PlanoTipoVO getTipoPlanoDiaPlus() {
        if (tipoPlanoDiaPlus == null) {
            tipoPlanoDiaPlus = new PlanoTipoVO();
        }
        return tipoPlanoDiaPlus;
    }

    public void setTipoPlanoDiaPlus(PlanoTipoVO tipoPlanoDiaPlus) {
        this.tipoPlanoDiaPlus = tipoPlanoDiaPlus;
    }

    public ModalidadeVO getModalidadeDiaPlus() {
        if (modalidadeDiaPlus == null) {
            modalidadeDiaPlus = new ModalidadeVO();
        }
        return modalidadeDiaPlus;
    }

    public void setModalidadeDiaPlus(ModalidadeVO modalidadeDiaPlus) {
        this.modalidadeDiaPlus = modalidadeDiaPlus;
    }

    public ProdutoVO getProdutoDayUse() {
        if (produtoDayUse == null) {
            produtoDayUse = new ProdutoVO();
        }
        return produtoDayUse;
    }

    public void setProdutoDayUse(ProdutoVO produtoDayUse) {
        this.produtoDayUse = produtoDayUse;
    }

    public ModalidadeVO getModalidadeDayUse() {
        if (modalidadeDayUse == null) {
            modalidadeDayUse = new ModalidadeVO();
        }
        return modalidadeDayUse;
    }

    public void setModalidadeDayUse(ModalidadeVO modalidadeDayUse) {
        this.modalidadeDayUse = modalidadeDayUse;
    }

    public boolean isGerarBoletoCaixaAberto() {
        return gerarBoletoCaixaAberto;
    }

    public void setGerarBoletoCaixaAberto(boolean gerarBoletoCaixaAberto) {
        this.gerarBoletoCaixaAberto = gerarBoletoCaixaAberto;
    }

    public boolean isIntegracaoWeHelpHabilitada() {
        return this.integracaoWeHelpHabilitada;
    }

    public void setIntegracaoWeHelpHabilitada(final boolean integracaoWeHelpHabilitada) {
        this.integracaoWeHelpHabilitada = integracaoWeHelpHabilitada;
    }

    public boolean isCpfCodigoInternoWeHelp() {
        return cpfCodigoInternoWeHelp;
    }

    public void setCpfCodigoInternoWeHelp(boolean cpfCodigoInternoWeHelp) {
        this.cpfCodigoInternoWeHelp = cpfCodigoInternoWeHelp;
    }

    public String getUrlEnvioAcesso() {
        if (urlEnvioAcesso == null){
            urlEnvioAcesso = "";
        }
        return urlEnvioAcesso;
    }

    public void setUrlEnvioAcesso(String urlEnvioAcesso) {
        this.urlEnvioAcesso = urlEnvioAcesso;
    }

    public String getTokenEnvioAcesso() {
        if (tokenEnvioAcesso == null){
            tokenEnvioAcesso = "";
        }
        return tokenEnvioAcesso;
    }

    public void setTokenEnvioAcesso(String tokenEnvioAcesso) {
        this.tokenEnvioAcesso = tokenEnvioAcesso;
    }

    public boolean isUsarSistemaInternacional() {
        return usarSistemaInternacional;
    }

    public void setUsarSistemaInternacional(boolean usarSistemaInternacional) {
        this.usarSistemaInternacional = usarSistemaInternacional;
    }

    public void validarConfiguracaoMultiplosConvenios() throws Exception {
        if (!this.isHabilitarReenvioAutomaticoRemessa()) {
            return;
        }
        if (UteisValidacao.emptyList(this.getConfiguracaoReenvioMovParcelaEmpresaVOS())) {
            throw new Exception("Preencha a aba Retentativa Automática Cobrança.");
        }
        if (UteisValidacao.emptyNumber(this.getQtdExecucoesRetentativa())) {
            throw new Exception("Informe a \"Quantidade de tentativas de cobrança da parcela\".");
        }

        //validações padrão Pacto cobrança
        if (this.getQtdExecucoesRetentativa() <= 0 || this.getQtdExecucoesRetentativa() > 5) {
            throw new Exception("A configuração \"Quantidade de tentativas de cobrança da parcela\" deve ser entre 1 e 5");
        }
    }

    public void validarConfiguracaoRetentativa() throws Exception {
        if (this.getQtdDiasLimiteCobrancaParcelasRecorrencia() > 95) {
            throw new Exception("A configuração \"Limite de dias após o vencimento para parcela entrar na Retentativa de Cobrança\" deve ser no máximo 95 dias");
        }

        if (this.getQtdDiasRepetirCobrancaParcelasRecorrencia() < 3) {
            throw new Exception("A configuração \"Intervalo de dias para Retentativa de Cobrança da parcela\" deve ser no mínimo 3 dias");
        }
    }

    public ConvenioCobrancaVO getConvenioCobrancaBoleto() {
        if (convenioCobrancaBoleto == null) {
            convenioCobrancaBoleto = new ConvenioCobrancaVO();
        }
        return convenioCobrancaBoleto;
    }

    public void setConvenioCobrancaBoleto(ConvenioCobrancaVO convenioCobrancaBoleto) {
        this.convenioCobrancaBoleto = convenioCobrancaBoleto;
    }

    public Integer getIdContabancariaSesi() {
        return idContabancariaSesi;
    }

    public void setIdContabancariaSesi(Integer idContabancariaSesi) {
        this.idContabancariaSesi = idContabancariaSesi;
    }

    public Integer getCodExternoUnidadeSesi() {
        return codExternoUnidadeSesi;
    }

    public void setCodExternoUnidadeSesi(Integer codExternoUnidadeSesi) {
        this.codExternoUnidadeSesi = codExternoUnidadeSesi;
    }

    public Boolean getUtilizaIntegracaoDelsoft() {
        if (utilizaIntegracaoDelsoft == null){
           utilizaIntegracaoDelsoft = false;
        }
        return utilizaIntegracaoDelsoft;
    }

    public void setUtilizaIntegracaoDelsoft(Boolean utilizaIntegracaoDelsoft) {
        this.utilizaIntegracaoDelsoft = utilizaIntegracaoDelsoft;
    }

    public String getHostIntegracaoDelsoft() {
        if (hostIntegracaoDelsoft == null){
            hostIntegracaoDelsoft = "";
        }
        return hostIntegracaoDelsoft;
    }

    public void setHostIntegracaoDelsoft(String hostIntegracaoDelsoft) {
        this.hostIntegracaoDelsoft = hostIntegracaoDelsoft;
    }

    public Integer getPortaIntegracaoDelsoft() {
        if (portaIntegracaoDelsoft == null){
            portaIntegracaoDelsoft = 0;
        }
        return portaIntegracaoDelsoft;
    }

    public void setPortaIntegracaoDelsoft(Integer portaIntegracaoDelsoft) {
        this.portaIntegracaoDelsoft = portaIntegracaoDelsoft;
    }

    public String getTokenIntegracaoDelsoft() {
        if (tokenIntegracaoDelsoft == null){
            tokenIntegracaoDelsoft = "";
        }
        return tokenIntegracaoDelsoft;
    }

    public void setTokenIntegracaoDelsoft(String tokenIntegracaoDelsoft) {
        this.tokenIntegracaoDelsoft = tokenIntegracaoDelsoft;
    }

    public String getNomeAplicacaoDelsoft() {
        if (nomeAplicacaoDelsoft == null){
            nomeAplicacaoDelsoft = "";
        }
        return nomeAplicacaoDelsoft;
    }

    public void setNomeAplicacaoDelsoft(String nomeAplicacaoDelsoft) {
        this.nomeAplicacaoDelsoft = nomeAplicacaoDelsoft;
    }

    public String getUsuarioAplicacaoDelsoft() {
        return usuarioAplicacaoDelsoft;
    }

    public void setUsuarioAplicacaoDelsoft(String usuarioAplicacaoDelsoft) {
        this.usuarioAplicacaoDelsoft = usuarioAplicacaoDelsoft;
    }

    public String getSenhaAplicacaoDelsoft() {
        return senhaAplicacaoDelsoft;
    }

    public PlanoVO getPlanoAplicacaoDelsoft() {
        if (planoAplicacaoDelsoft == null) {
            planoAplicacaoDelsoft = new PlanoVO();
        }
        return planoAplicacaoDelsoft;
    }

    public void setPlanoAplicacaoDelsoft(PlanoVO planoAplicacaoDelsoft) {
        this.planoAplicacaoDelsoft = planoAplicacaoDelsoft;
    }

    public void setSenhaAplicacaoDelsoft(String senhaAplicacaoDelsoft) {
        this.senhaAplicacaoDelsoft = senhaAplicacaoDelsoft;
    }

    public boolean isResponderBVNaVendaRapida() {
        return responderBVNaVendaRapida;
    }

    public void setResponderBVNaVendaRapida(boolean responderBVNaVendaRapida) {
        this.responderBVNaVendaRapida = responderBVNaVendaRapida;
    }

    public String getCodigoRede() {
        if (codigoRede == null) {
            codigoRede = "";
        }
        return codigoRede;
    }

    public void setCodigoRede(String codigoRede) {
        this.codigoRede = codigoRede;
    }

    public boolean isAplicarMultaSobreValorTotalContrato() {
        return aplicarMultaSobreValorTotalContrato;
    }

    public void setAplicarMultaSobreValorTotalContrato(boolean aplicarMultaSobreValorTotalContrato) {
        this.aplicarMultaSobreValorTotalContrato = aplicarMultaSobreValorTotalContrato;
    }

    public boolean isEmitirNoNomeResponsavel() {
        return emitirNoNomeResponsavel;
    }

    public void setEmitirNoNomeResponsavel(boolean emitirNoNomeResponsavel) {
        this.emitirNoNomeResponsavel = emitirNoNomeResponsavel;
    }

    public String getCnpjClienteSesi() {
        return cnpjClienteSesi;
    }

    public void setCnpjClienteSesi(String cnpjClienteSesi) {
        this.cnpjClienteSesi = cnpjClienteSesi;
    }

    public Boolean getBloquearAcessoCrefVencido() {
        if (bloquearAcessoCrefVencido == null) {
            bloquearAcessoCrefVencido = false;
        }
        return bloquearAcessoCrefVencido;
    }

    public void setBloquearAcessoCrefVencido(Boolean bloquearAcessoCrefVencido) {
        this.bloquearAcessoCrefVencido = bloquearAcessoCrefVencido;
    }

    public Boolean getBloquearAcessoAlunoParqNaoAssinado() {
        if (bloquearAcessoAlunoParqNaoAssinado == null) {
            bloquearAcessoAlunoParqNaoAssinado = false;
        }
        return bloquearAcessoAlunoParqNaoAssinado;
    }

    public void setBloquearAcessoAlunoParqNaoAssinado(Boolean bloquearAcessoAlunoParqNaoAssinado) {
        this.bloquearAcessoAlunoParqNaoAssinado = bloquearAcessoAlunoParqNaoAssinado;
    }

    public boolean isCobrarParcelaComBoletoGerado() {
        return cobrarParcelaComBoletoGerado;
    }

    public void setCobrarParcelaComBoletoGerado(boolean cobrarParcelaComBoletoGerado) {
        this.cobrarParcelaComBoletoGerado = cobrarParcelaComBoletoGerado;
    }

    public boolean isCobrarParcelaVencidaSemTentativaCobranca() {
        return cobrarParcelaVencidaSemTentativaCobranca;
    }

    public void setCobrarParcelaVencidaSemTentativaCobranca(boolean cobrarParcelaVencidaSemTentativaCobranca) {
        this.cobrarParcelaVencidaSemTentativaCobranca = cobrarParcelaVencidaSemTentativaCobranca;
    }

    public boolean isUtilizarNomeResponsavelNoBoletoMaiorIdade() {
        return utilizarNomeResponsavelNoBoletoMaiorIdade;
    }

    public void setUtilizarNomeResponsavelNoBoletoMaiorIdade(boolean utilizarNomeResponsavelNoBoletoMaiorIdade) {
        this.utilizarNomeResponsavelNoBoletoMaiorIdade = utilizarNomeResponsavelNoBoletoMaiorIdade;
    }

    public ConvenioCobrancaVO getConvenioCobrancaCartao() {
        if (convenioCobrancaCartao == null) {
            convenioCobrancaCartao = new ConvenioCobrancaVO();
        }
        return convenioCobrancaCartao;
    }

    public void setConvenioCobrancaCartao(ConvenioCobrancaVO convenioCobrancaCartao) {
        this.convenioCobrancaCartao = convenioCobrancaCartao;
    }

    public int getQtdDiasEnvioSPC() {
        return qtdDiasEnvioSPC;
    }

    public void setQtdDiasEnvioSPC(int qtdDiasEnvioSPC) {
        this.qtdDiasEnvioSPC = qtdDiasEnvioSPC;
    }

    public boolean isConsultarNovoCadastroSPC() {
        return consultarNovoCadastroSPC;
    }

    public void setConsultarNovoCadastroSPC(boolean consultarNovoCadastroSPC) {
        this.consultarNovoCadastroSPC = consultarNovoCadastroSPC;
    }

    public boolean isEnvioAutomaticoSPC() {
        return envioAutomaticoSPC;
    }

    public void setEnvioAutomaticoSPC(boolean envioAutomaticoSPC) {
        this.envioAutomaticoSPC = envioAutomaticoSPC;
    }

    public String getOperadorSpc() {
        if (operadorSpc == null) {
            operadorSpc = "";
        }
        return operadorSpc;
    }

    public void setOperadorSpc(String operadorSpc) {
        this.operadorSpc = operadorSpc;
    }

    public String getSenhaSpc() {
        if (senhaSpc == null) {
            senhaSpc = "";
        }
        return senhaSpc;
    }

    public void setSenhaSpc(String senhaSpc) {
        this.senhaSpc = senhaSpc;
    }

    public Long getCodigoAssociadoSpc() {
        if (codigoAssociadoSpc == null) {
            codigoAssociadoSpc = 0L;
        }
        return codigoAssociadoSpc;
    }

    public void setCodigoAssociadoSpc(Long codigoAssociadoSpc) {
        this.codigoAssociadoSpc = codigoAssociadoSpc;
    }

    public boolean isManterMarcacoesFuturasCreditoRenovacao() {
        return manterMarcacoesFuturasCreditoRenovacao;
    }

    public void setManterMarcacoesFuturasCreditoRenovacao(boolean manterMarcacoesFuturasCreditoRenovacao) {
        this.manterMarcacoesFuturasCreditoRenovacao = manterMarcacoesFuturasCreditoRenovacao;
    }

    public boolean isCancelamentoApresentarTransacoes() {
        return cancelamentoApresentarTransacoes;
    }

    public void setCancelamentoApresentarTransacoes(boolean cancelamentoApresentarTransacoes) {
        this.cancelamentoApresentarTransacoes = cancelamentoApresentarTransacoes;
    }

    public boolean isIsentarCancelamento7Dias() {
        return isentarCancelamento7Dias;
    }

    public void setIsentarCancelamento7Dias(boolean isentarCancelamento7Dias) {
        this.isentarCancelamento7Dias = isentarCancelamento7Dias;
    }

    public boolean isCancelarContratosNaoRenovaveisForaRecorrencia() {
        return cancelarContratosNaoRenovaveisForaRecorrencia;
    }

    public void setCancelarContratosNaoRenovaveisForaRecorrencia(boolean cancelarContratosNaoRenovaveisForaRecorrencia) {
        this.cancelarContratosNaoRenovaveisForaRecorrencia = cancelarContratosNaoRenovaveisForaRecorrencia;
    }

    public boolean isLimitarDescontosPorPerfil() {
        return limitarDescontosPorPerfil;
    }

    public void setLimitarDescontosPorPerfil(boolean limitarDescontosPorPerfil) {
        this.limitarDescontosPorPerfil = limitarDescontosPorPerfil;
    }

    public boolean isRegistrarTentativasAcesso() {
        return registrarTentativasAcesso;
    }

    public void setRegistrarTentativasAcesso(boolean registrarTentativasAcesso) {
        this.registrarTentativasAcesso = registrarTentativasAcesso;
    }

    public boolean isCobrarMultaJurosAsaas() {
        return cobrarMultaJurosAsaas;
    }

    public void setCobrarMultaJurosAsaas(boolean cobrarMultaJurosAsaas) {
        this.cobrarMultaJurosAsaas = cobrarMultaJurosAsaas;
    }

    public double getValorMultaAsaas() {
        if (UteisValidacao.emptyNumber(valorMultaAsaas)) {
            return 0.0;
        }
        return valorMultaAsaas;
    }

    public void setValorMultaAsaas(double valorMultaAsaas) {
        this.valorMultaAsaas = valorMultaAsaas;
    }

    public double getValorJurosAsaas() {
        if (UteisValidacao.emptyNumber(valorJurosAsaas)) {
            return 0.0;
        }
        return valorJurosAsaas;
    }

    public void setValorJurosAsaas(double valorJurosAsaas) {
        this.valorJurosAsaas = valorJurosAsaas;
    }

    public boolean isPesquisaAutomaticaSPC() {
        return pesquisaAutomaticaSPC;
    }

    public void setPesquisaAutomaticaSPC(boolean pesquisaAutomaticaSPC) {
        this.pesquisaAutomaticaSPC = pesquisaAutomaticaSPC;
    }

    public ConfiguracaoEmpresaBitrixVO getConfiguracaoEmpresaBitrix(){
        if (configuracaoEmpresaBitrix == null) {
            configuracaoEmpresaBitrix = new ConfiguracaoEmpresaBitrixVO();
        }
        return configuracaoEmpresaBitrix;
    }
    public void setConfiguracaoEmpresaBitrix(ConfiguracaoEmpresaBitrixVO configuracaoEmpresaBitrix){
        this.configuracaoEmpresaBitrix = configuracaoEmpresaBitrix;
    }

    public ConfiguracaoEmpresaHubspotVO getConfiguracaoEmpresaHubspot() {
        if (configuracaoEmpresaHubspot == null) {
            configuracaoEmpresaHubspot = new ConfiguracaoEmpresaHubspotVO();
        }
        return configuracaoEmpresaHubspot;
    }

    public void setConfiguracaoEmpresaHubspot(ConfiguracaoEmpresaHubspotVO configuracaoEmpresaHubspot) {
        this.configuracaoEmpresaHubspot = configuracaoEmpresaHubspot;
    }

    public boolean utilizaAlgumaIntegracaoLeadsCrm() {
        try {
            return getConfiguracaoRDStation().isEmpresaUsaRD()
                    || getConfiguracaoIntegracaoBuzzLeadVO().isHabilitada()
                    || getConfiguracaoIntegracaoWordPressVO().isHabilitada()
                    || getConfiguracaoIntegracaoJoinVO().isHabilitada()
                    || getConfiguracaoEmpresaBitrix().isHabilitada()
                    || getConfiguracaoIntegracaoGenericaLeadsVO().isHabilitada();
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isConcContasPagarFacilitePay() {
        return concContasPagarFacilitePay;
    }

    public void setConcContasPagarFacilitePay(boolean concContasPagarFacilitePay) {
        this.concContasPagarFacilitePay = concContasPagarFacilitePay;
    }

    public boolean isConcContasReceberFacilitePay() {
        return concContasReceberFacilitePay;
    }

    public void setConcContasReceberFacilitePay(boolean concContasReceberFacilitePay) {
        this.concContasReceberFacilitePay = concContasReceberFacilitePay;
    }

    public int getQtdLmtContasConcFacilitePay() {
        return qtdLmtContasConcFacilitePay;
    }

    public void setQtdLmtContasConcFacilitePay(int qtdLmtContasConcFacilitePay) {
        this.qtdLmtContasConcFacilitePay = qtdLmtContasConcFacilitePay;
    }

    public boolean isUtilizarNomeResponsavelNoPixMenorIdade() {
        return utilizarNomeResponsavelNoPixMenorIdade;
    }

    public void setUtilizarNomeResponsavelNoPixMenorIdade(boolean utilizarNomeResponsavelNoPixMenorIdade) {
        this.utilizarNomeResponsavelNoPixMenorIdade = utilizarNomeResponsavelNoPixMenorIdade;
    }

    public boolean isUtilizarNomeResponsavelNoPixMaiorIdade() {
        return utilizarNomeResponsavelNoPixMaiorIdade;
    }

    public void setUtilizarNomeResponsavelNoPixMaiorIdade(boolean utilizarNomeResponsavelNoPixMaiorIdade) {
        this.utilizarNomeResponsavelNoPixMaiorIdade = utilizarNomeResponsavelNoPixMaiorIdade;
    }

    public boolean isIrTelaPagamentoCartaoCreditoFormaPagamento() {
        return irTelaPagamentoCartaoCreditoFormaPagamento;
    }

    public void setIrTelaPagamentoCartaoCreditoFormaPagamento(boolean irTelaPagamentoCartaoCreditoFormaPagamento) {
        this.irTelaPagamentoCartaoCreditoFormaPagamento = irTelaPagamentoCartaoCreditoFormaPagamento;
    }

    public boolean isGerarAutCobrancaComCobAutBloqueada() {
        return gerarAutCobrancaComCobAutBloqueada;
    }

    public void setGerarAutCobrancaComCobAutBloqueada(boolean gerarAutCobrancaComCobAutBloqueada) {
        this.gerarAutCobrancaComCobAutBloqueada = gerarAutCobrancaComCobAutBloqueada;
    }

    public boolean isEmiteValorTotalFaturamento() {
        return emiteValorTotalFaturamento;
    }

    public void setEmiteValorTotalFaturamento(boolean emiteValorTotalFaturamento) {
        this.emiteValorTotalFaturamento = emiteValorTotalFaturamento;
    }

    public boolean isGerarNotaFiscalComDesconto() {
        return gerarNotaFiscalComDesconto;
    }

    public void setGerarNotaFiscalComDesconto(boolean gerarNotaFiscalComDesconto) {
        this.gerarNotaFiscalComDesconto = gerarNotaFiscalComDesconto;
    }

    public boolean isFacilitePayReguaCobranca() {
        return facilitePayReguaCobranca;
    }

    public void setFacilitePayReguaCobranca(boolean facilitePayReguaCobranca) {
        this.facilitePayReguaCobranca = facilitePayReguaCobranca;
    }

    public boolean isObrigatorioPreencherCamposCartao() {
        return obrigatorioPreencherCamposCartao;
    }

    public void setObrigatorioPreencherCamposCartao(boolean obrigatorioPreencherCamposCartao) {
        this.obrigatorioPreencherCamposCartao = obrigatorioPreencherCamposCartao;
    }

    public Double getValorMetaFacilitePay() {
        if (valorMetaFacilitePay == null) {
            valorMetaFacilitePay = 0.0;
        }
        return valorMetaFacilitePay;
    }

    public void setValorMetaFacilitePay(Double valorMetaFacilitePay) {
        this.valorMetaFacilitePay = valorMetaFacilitePay;
    }

    public boolean isIgnorarCodigoDeBarrasEmissaoNfce() {
        return ignorarCodigoDeBarrasEmissaoNfce;
    }

    public void setIgnorarCodigoDeBarrasEmissaoNfce(boolean ignorarCodigoDeBarrasEmissaoNfce) {
        this.ignorarCodigoDeBarrasEmissaoNfce = ignorarCodigoDeBarrasEmissaoNfce;
    }

    public boolean isFacilitePayConciliacaoCartao() {
        return facilitePayConciliacaoCartao;
    }

    public void setFacilitePayConciliacaoCartao(boolean facilitePayConciliacaoCartao) {
        this.facilitePayConciliacaoCartao = facilitePayConciliacaoCartao;
    }

    public Integer getDiasParaVencimentoParq() {
        return diasParaVencimentoParq;
    }

    public void setDiasParaVencimentoParq(Integer diasParaVencimentoParq) {
        this.diasParaVencimentoParq = diasParaVencimentoParq;
    }

    public Boolean getBloquearAcessoSemTermoResponsabilidade() {
        if (bloquearAcessoSemTermoResponsabilidade == null){
            bloquearAcessoSemTermoResponsabilidade = false;
        }
        return bloquearAcessoSemTermoResponsabilidade;
    }

    public void setBloquearAcessoSemTermoResponsabilidade(Boolean bloquearAcessoSemTermoResponsabilidade) {
        this.bloquearAcessoSemTermoResponsabilidade = bloquearAcessoSemTermoResponsabilidade;
    }

    public boolean isFacilitePayStoneConnect() {
        return facilitePayStoneConnect;
    }

    public void setFacilitePayStoneConnect(boolean facilitePayStoneConnect) {
        this.facilitePayStoneConnect = facilitePayStoneConnect;
    }

    public boolean isFacilitePayCDLSPC() {
        return facilitePayCDLSPC;
    }

    public void setFacilitePayCDLSPC(boolean facilitePayCDLSPC) {
        this.facilitePayCDLSPC = facilitePayCDLSPC;
    }

    public boolean isFacilitePayReguaCobrancaEmail() {
        return facilitePayReguaCobrancaEmail;
    }

    public void setFacilitePayReguaCobrancaEmail(boolean facilitePayReguaCobrancaEmail) {
        this.facilitePayReguaCobrancaEmail = facilitePayReguaCobrancaEmail;
    }

    public boolean isFacilitePayReguaCobrancaSms() {
        return facilitePayReguaCobrancaSms;
    }

    public void setFacilitePayReguaCobrancaSms(boolean facilitePayReguaCobrancaSms) {
        this.facilitePayReguaCobrancaSms = facilitePayReguaCobrancaSms;
    }

    public boolean isFacilitePayReguaCobrancaApp() {
        return facilitePayReguaCobrancaApp;
    }

    public void setFacilitePayReguaCobrancaApp(boolean facilitePayReguaCobrancaApp) {
        this.facilitePayReguaCobrancaApp = facilitePayReguaCobrancaApp;
    }

    public boolean isFacilitePayReguaCobrancaWhatsApp() {
        return facilitePayReguaCobrancaWhatsApp;
    }

    public void setFacilitePayReguaCobrancaWhatsApp(boolean facilitePayReguaCobrancaWhatsApp) {
        this.facilitePayReguaCobrancaWhatsApp = facilitePayReguaCobrancaWhatsApp;
    }

    public boolean isFacilitePayReguaCobrancaGymbotPro() {
        return facilitePayReguaCobrancaGymbotPro;
    }

    public void setFacilitePayReguaCobrancaGymbotPro(boolean facilitePayReguaCobrancaGymbotPro) {
        this.facilitePayReguaCobrancaGymbotPro = facilitePayReguaCobrancaGymbotPro;
    }

    public String getDiasAtivosPontuacaoAcesso() {
        return diasAtivosPontuacaoAcesso;
    }

    public void setDiasAtivosPontuacaoAcesso(String diasAtivosPontuacaoAcesso) {
        this.diasAtivosPontuacaoAcesso = diasAtivosPontuacaoAcesso;
    }

    public boolean isUtilizaGestaoClientesComRestricoes() {
        return utilizaGestaoClientesComRestricoes;
    }

    public void setUtilizaGestaoClientesComRestricoes(boolean utilizaGestaoClientesComRestricoes) {
        this.utilizaGestaoClientesComRestricoes = utilizaGestaoClientesComRestricoes;
    }

    public boolean isMarcarAutoRecebiveisCartaoChequeCancelamento() {
        return marcarAutoRecebiveisCartaoChequeCancelamento;
    }

    public void setMarcarAutoRecebiveisCartaoChequeCancelamento(boolean marcarAutoRecebiveisCartaoChequeCancelamento) {
        this.marcarAutoRecebiveisCartaoChequeCancelamento = marcarAutoRecebiveisCartaoChequeCancelamento;
    }

    public boolean isHabilitarCadastroEmpresaSesi() {
        return habilitarCadastroEmpresaSesi;
    }

    public void setHabilitarCadastroEmpresaSesi(boolean habilitarCadastroEmpresaSesi) {
        this.habilitarCadastroEmpresaSesi = habilitarCadastroEmpresaSesi;
    }

    public String getIntegracaoNuvemshopNomeApp() {
        return integracaoNuvemshopNomeApp;
    }

    public void setIntegracaoNuvemshopNomeApp(String integracaoNuvemshopNomeApp) {
        this.integracaoNuvemshopNomeApp = integracaoNuvemshopNomeApp;
    }

    public String getIntegracaoNuvemshopEmail() {
        return integracaoNuvemshopEmail;
    }

    public void setIntegracaoNuvemshopEmail(String integracaoNuvemshopEmail) {
        this.integracaoNuvemshopEmail = integracaoNuvemshopEmail;
    }

    public String getIntegracaoNuvemshopTokenAcesso() {
        return integracaoNuvemshopTokenAcesso;
    }

    public void setIntegracaoNuvemshopTokenAcesso(String integracaoNuvemshopTokenAcesso) {
        this.integracaoNuvemshopTokenAcesso = integracaoNuvemshopTokenAcesso;
    }

    public String getIntegracaoNuvemshopStoreId() {
        return integracaoNuvemshopStoreId;
    }

    public void setIntegracaoNuvemshopStoreId(String integracaoNuvemshopStoreId) {
        this.integracaoNuvemshopStoreId = integracaoNuvemshopStoreId;
    }



    public boolean isIntegracaoNuvemshopHabilitada() {
        return integracaoNuvemshopHabilitada;
    }

    public void setIntegracaoNuvemshopHabilitada(boolean integracaoNuvemshopHabilitada) {
        this.integracaoNuvemshopHabilitada = integracaoNuvemshopHabilitada;
    }

    public boolean isPermiteCompartilhamentoPlanoClienteAtivoPlanoCredito() {
        return permiteCompartilhamentoPlanoClienteAtivoPlanoCredito;
    }

    public void setPermiteCompartilhamentoPlanoClienteAtivoPlanoCredito(boolean permiteCompartilhamentoPlanoClienteAtivoPlanoCredito) {
        this.permiteCompartilhamentoPlanoClienteAtivoPlanoCredito = permiteCompartilhamentoPlanoClienteAtivoPlanoCredito;
    }

    public boolean isEnvioNFCeAutomaticoNoPagamento() {
        return envioNFCeAutomaticoNoPagamento;
    }

    public void setEnvioNFCeAutomaticoNoPagamento(boolean envioNFCeAutomaticoNoPagamento) {
        this.envioNFCeAutomaticoNoPagamento = envioNFCeAutomaticoNoPagamento;
    }

    public Boolean getUtilizarPactoPrint() {
        return utilizarPactoPrint;
    }

    public void setUtilizarPactoPrint(Boolean utilizarPactoPrint) {
        this.utilizarPactoPrint = utilizarPactoPrint;
    }

    public Integer getValidadeMesesCarteirinhaSocio() {
        return validadeMesesCarteirinhaSocio;
    }

    public void setValidadeMesesCarteirinhaSocio(Integer validadeMesesCarteirinhaSocio) {
        this.validadeMesesCarteirinhaSocio = validadeMesesCarteirinhaSocio;
    }

    public String getPresidente() {
        return presidente;
    }

    public void setPresidente(String presidente) {
        this.presidente = presidente;
    }

    public String getSuperintendente() {
        return superintendente;
    }

    public void setSuperintendente(String superintendente) {
        this.superintendente = superintendente;
    }

    public boolean isUtilizaConfigCancelamentoSesc() {
        return utilizaConfigCancelamentoSesc;
    }

    public void setUtilizaConfigCancelamentoSesc(boolean utilizaConfigCancelamentoSesc) {
        this.utilizaConfigCancelamentoSesc = utilizaConfigCancelamentoSesc;
    }

    public String getContratoProblematicos() {
        if(contratoProblematicos == null){
            contratoProblematicos = "";
        }
        return contratoProblematicos;
    }

    public void setContratoProblematicos(String contratoProblematicos) {
        this.contratoProblematicos = contratoProblematicos;
    }

    public String getMatriculasProblematicas() {
        if(matriculasProblematicas == null){
            matriculasProblematicas = "";
        }
        return matriculasProblematicas;
    }

    public void setMatriculasProblematicas(String matriculasProblematicas) {
        this.matriculasProblematicas = matriculasProblematicas;
    }

    public boolean isBloquearAcessoMatriculaRematriculaTotemSemPagamento() {
        return bloquearAcessoMatriculaRematriculaTotemSemPagamento;
    }

    public void setBloquearAcessoMatriculaRematriculaTotemSemPagamento(boolean bloquearAcessoMatriculaRematriculaTotemSemPagamento) {
        this.bloquearAcessoMatriculaRematriculaTotemSemPagamento = bloquearAcessoMatriculaRematriculaTotemSemPagamento;
    }

    public String getTokenAcademyGoGood() {
        return tokenAcademyGoGood;
    }

    public void setTokenAcademyGoGood(String tokenAcademyGoGood) {
        this.tokenAcademyGoGood = tokenAcademyGoGood;
    }

    public boolean isBloquearAcessoSeDebitoEmConta() {
        return bloquearAcessoSeDebitoEmConta;
    }

    public void setBloquearAcessoSeDebitoEmConta(boolean bloquearAcessoSeDebitoEmConta) {
        this.bloquearAcessoSeDebitoEmConta = bloquearAcessoSeDebitoEmConta;
    }

    public boolean isHabilitarCobrancaAutomaticaNaVenda() {
        return habilitarCobrancaAutomaticaNaVenda;
    }

    public void setHabilitarCobrancaAutomaticaNaVenda(boolean habilitarCobrancaAutomaticaNaVenda) {
        this.habilitarCobrancaAutomaticaNaVenda = habilitarCobrancaAutomaticaNaVenda;
    }

    public boolean isCobrarCreditoPactoBoleto() {
        return cobrarCreditoPactoBoleto;
    }

    public void setCobrarCreditoPactoBoleto(boolean cobrarCreditoPactoBoleto) {
        this.cobrarCreditoPactoBoleto = cobrarCreditoPactoBoleto;
    }

    public ConvenioCobrancaVO getConvenioCobrancaCartaoRegua() {
        if (convenioCobrancaCartaoRegua == null) {
            convenioCobrancaCartaoRegua = new ConvenioCobrancaVO();
        }
        return convenioCobrancaCartaoRegua;
    }

    public void setConvenioCobrancaCartaoRegua(ConvenioCobrancaVO convenioCobrancaCartaoRegua) {
        this.convenioCobrancaCartaoRegua = convenioCobrancaCartaoRegua;
    }

    public ConvenioCobrancaVO getConvenioCobrancaBoletoRegua() {
        if (convenioCobrancaBoletoRegua == null) {
            convenioCobrancaBoletoRegua = new ConvenioCobrancaVO();
        }
        return convenioCobrancaBoletoRegua;
    }

    public void setConvenioCobrancaBoletoRegua(ConvenioCobrancaVO convenioCobrancaBoletoRegua) {
        this.convenioCobrancaBoletoRegua = convenioCobrancaBoletoRegua;
    }

    public ConvenioCobrancaVO getConvenioCobrancaPixRegua() {
        if (convenioCobrancaPixRegua == null) {
            convenioCobrancaPixRegua = new ConvenioCobrancaVO();
        }
        return convenioCobrancaPixRegua;
    }

    public void setConvenioCobrancaPixRegua(ConvenioCobrancaVO convenioCobrancaPixRegua) {
        this.convenioCobrancaPixRegua = convenioCobrancaPixRegua;
    }

    public boolean isGerarAutCobrancaComCobAutBloqueadaRegua() {
        return gerarAutCobrancaComCobAutBloqueadaRegua;
    }

    public void setGerarAutCobrancaComCobAutBloqueadaRegua(boolean gerarAutCobrancaComCobAutBloqueadaRegua) {
        this.gerarAutCobrancaComCobAutBloqueadaRegua = gerarAutCobrancaComCobAutBloqueadaRegua;
    }

    public Integer getToleranciaCancelarContratosNaoAssinados() {
        return toleranciaCancelarContratosNaoAssinados;
    }

    public void setToleranciaCancelarContratosNaoAssinados(Integer toleranciaCancelarContratosNaoAssinados) {
        this.toleranciaCancelarContratosNaoAssinados = toleranciaCancelarContratosNaoAssinados;
    }

    public boolean isIntegracaoManyChatHabilitada() {
        return integracaoManyChatHabilitada;
    }

    public void setIntegracaoManyChatHabilitada(boolean integracaoManyChatHabilitada) {
        this.integracaoManyChatHabilitada = integracaoManyChatHabilitada;
    }

    public String getIntegracaoManyChatTokenApi() {
        return integracaoManyChatTokenApi;
    }

    public void setIntegracaoManyChatTokenApi(String integracaoManyChatTokenApi) {
        this.integracaoManyChatTokenApi = integracaoManyChatTokenApi;
    }

    public String getIntegracaoManyChatTagUnidade() {
        return integracaoManyChatTagUnidade;
    }

    public void setIntegracaoManyChatTagUnidade(String integracaoManyChatTagUnidade) {
        this.integracaoManyChatTagUnidade = integracaoManyChatTagUnidade;
    }

    public Boolean getHabilitarValidacaoHorariosMesmaTurma() {
        return habilitarValidacaoHorariosMesmaTurma;
    }

    public void setHabilitarValidacaoHorariosMesmaTurma(Boolean habilitarValidacaoHorariosMesmaTurma) {
        this.habilitarValidacaoHorariosMesmaTurma = habilitarValidacaoHorariosMesmaTurma;
    }

    public Boolean getUsarSescDf() {
        return usarSescDf;
    }

    public void setUsarSescDf(Boolean usarSescDf) {
        this.usarSescDf = usarSescDf;
    }

    public String getTokenSescDf() {
        return tokenSescDf;
    }

    public void setTokenSescDf(String tokenSescDf) {
        this.tokenSescDf = tokenSescDf;
    }

    public boolean isHorariocapacidadeporcategoria() {
        return horariocapacidadeporcategoria;
    }

    public void setHorariocapacidadeporcategoria(boolean horariocapacidadeporcategoria) {
        this.horariocapacidadeporcategoria = horariocapacidadeporcategoria;
    }
}
