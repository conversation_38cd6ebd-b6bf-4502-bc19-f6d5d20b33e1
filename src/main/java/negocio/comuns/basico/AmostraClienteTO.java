/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import controle.crm.MetaExtraImportadoDTO;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.cielo.DCCCieloStatusEnum;

/**
 *
 * <AUTHOR>
 */
@XStreamAlias("Cliente")
public class AmostraClienteTO extends SuperTO{
    private String nome = "";
    private String nomeColaborador = "";
    private String matricula = "";
    private String situacao  = "";
    private String telefones = "";
    private String emails = "";
    private String categoria = "";
    private String idade = "";
    private String risco = "";
    private Date dataCadastro = null;
    private String aniversario = "";
    private String diasFalta = "";
    private String erro = "";
    private Integer contrato = null;
    private Integer codigoCliente;
    private Integer codigoPessoa;
    private Integer quantidadeAcessos;
    private String nomeEmpresa;
    private RemessaItemVO remessaItemVO;
    private MovParcelaVO movParcelaVO;
    private InfoClienteTO infoCliente;

    public AmostraClienteTO() {
    }

    public AmostraClienteTO(MetaExtraImportadoDTO meta) {
        this.nome = meta.getNome();
        this.emails = meta.getEmail();
        this.telefones = meta.getTelefone();
        this.matricula = meta.getMatricula();
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }

    public String getDiasFalta() {
        return diasFalta;
    }

    public void setDiasFalta(String diasFalta) {
        this.diasFalta = diasFalta;
    }

    public String getRisco() {
        return risco;
    }

    public void setRisco(String risco) {
        this.risco = risco;
    }

    public String getAniversario() {
        return aniversario;
    }

    public void setAniversario(String aniversario) {
        this.aniversario = aniversario;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public String getEmails() {
        return emails;
    }

    public void setEmails(String emails) {
        this.emails = emails;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomeColaborador() {
        return nomeColaborador;
    }

    public void setNomeColaborador(String nomeColaborador) {
        this.nomeColaborador = nomeColaborador;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getTelefones() {
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }
    
    public String getDataCadastroApresentar() {
        return Uteis.getData(dataCadastro);
    }
    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getIdade() {
        return idade;
    }

    public void setIdade(String idade) {
        this.idade = idade;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getCodigoCliente() {
        if (codigoCliente == null) {
            codigoCliente = 0;
        }
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public void setQuantidadeAcessos(Integer quantidadeAcessos) {
        this.quantidadeAcessos = quantidadeAcessos;
    }

    public Integer getQuantidadeAcessos() {
        return quantidadeAcessos;
    }

    public RemessaItemVO getRemessaItemVO() {
        if (remessaItemVO == null) {
            remessaItemVO = new RemessaItemVO();
        }
        return remessaItemVO;
    }

    public void setRemessaItemVO(RemessaItemVO remessaItemVO) {
        this.remessaItemVO = remessaItemVO;
    }

    public MovParcelaVO getMovParcelaVO() {
        if (movParcelaVO == null) {
            movParcelaVO = new MovParcelaVO();
        }
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }

    public String getCodRetorno() {
        try {
            return getRemessaItemVO().getProps().get(DCCAttEnum.StatusVenda.name());
        } catch (Exception ex) {
            return "";
        }
    }

    public String getDescricaoRetorno() {
        try {
            return DCCCieloStatusEnum.valueOff(getCodRetorno()).getDescricao();
        } catch (Exception ex) {
            return "";
        }
    }

    public InfoClienteTO getInfoCliente() {
        if (infoCliente == null) {
            infoCliente = new InfoClienteTO();
        }
        return infoCliente;
    }

    public void setInfoCliente(InfoClienteTO infoCliente) {
        this.infoCliente = infoCliente;
    }

    public String getNomeEmpresa() {
        if (nomeEmpresa == null) {
            nomeEmpresa = "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("nome", this.nome);
        json.put("nomeColaborador", this.nomeColaborador);
        json.put("matricula", this.matricula);
        json.put("situacao", this.situacao);
        json.put("telefones", this.telefones);
        json.put("emails", this.emails);
        json.put("categoria", this.categoria);
        json.put("idade", this.idade);
        json.put("risco", this.risco);
        json.put("dataCadastro", this.dataCadastro);
        json.put("aniversario", this.aniversario);
        json.put("diasFalta", this.diasFalta);
        json.put("contrato", this.contrato);
        json.put("codigoCliente", this.codigoCliente);
        json.put("codigoPessoa", this.codigoPessoa);
        json.put("quantidadeAcessos", this.quantidadeAcessos);
        json.put("nomeEmpresa", this.nomeEmpresa);
        return json;
    }

}
