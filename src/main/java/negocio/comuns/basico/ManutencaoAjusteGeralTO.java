package negocio.comuns.basico;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoBloqueioCobrancaEnum;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 24/03/2016.
 */
public class ManutencaoAjusteGeralTO extends SuperTO {

    private Integer totalContratos = 0;
    private Integer codigoPlano = 0;
    private String matriculaClienteExclusao;
    private String matriculaCliente = "";
    private String matriculaClienteTokenGymPass = "";
    private String nomePessoa;
    private String descricaoNotaFiscalEmitida;
    private ClienteVO clienteVO;
    private Integer qtdDiasBonus;
    private String listaIdRPS;
    private String listaCodigosFaseMetaExcluir;
    private Integer codigoCupom = 0;
    private CupomFiscalVO cupomFiscal;
    private String descricaoCupomFiscal;
    private boolean sucesso = false;
    private boolean erro = false;
    private boolean aviso = false;
    private boolean consultou = false;
    private boolean consultouRecibos = false;
    private String listaIdNFCe;
    private String listaIdNotaFamilia;
    private String msgResultado ="";
    private Date dataInicioProcessoStone = Calendario.ontem();
    private Date dataFimProcessoStone = Calendario.ontem();
    private Date reprocessarAPartirDe = Calendario.hoje();
    private Date exclusaoAPartirDe = Calendario.hoje();
    private Date processarDataCompensacaoPjbankAPartirDe = Calendario.hoje();
    private Date processarDataCompensacaoPjbankAte = Calendario.hoje();
    private Date reprocessarAte = Calendario.hoje();
    private Date exclusaoAte = Calendario.hoje();
    private Date processarAPartirDe = Calendario.hoje();

    //processo estorno de transacao
    private boolean estornarRecibo = true;
    private boolean estornarMovProduto = false;
    private String listaTransacaoEstornar;
    private String listaRecibosEstornarString;
    private List<ReciboPagamentoVO> listaRecibosEstornar;
    private List<EstornoTransacaoTO> listaEstornoTransacao;
    private List<RemessaVO> remessasSemSituacao;
    private Integer remessaExcluir;

    private Date dataBloqueioCobrancaAutomatica;
    private TipoBloqueioCobrancaEnum tipoBloqueioCobrancaAutomatica;
    private List<PessoaBloqueioCobrancaTO> listaPessoasBloqueioCobrancaAutomatica;
    private boolean pessoasSemDataBloqueioCobrancaAutomatica = true;
    private EmpresaVO empresaVO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private String situacao;
    private PlanoVO planoVO;
    private ModalidadeVO modalidadeVO;
    private Integer remessaStatusGerada;
    private static Integer informadoRemessaMudarStatus;
    private Integer informadoRemessaExcluir;
    private static Integer informadoParcelaMudarStatus;
    private List<AutorizacaoCobrancaClienteVO> listaAutorizacaoCobrancaCliente;
    private Integer tipoObjetosCobrar;
    private String codigosNotas = "";
    private String justificativaCancelamentoNotas = "";
    private String listaCodigosPix;
    private String listaCodigosGenericos;
    private Date dataPagamentoPix;

    private EmpresaVO empresaVOBoletoCancelar;
    private ConvenioCobrancaVO convenioCobrancaVOBoletoCancelar;
    private String listaCodigosGenericosBoletoCancelar;

    private String celular;
    private String email;
    private String modelo;
    private String matriculaRegua;
    private boolean enviarUsandoJenkinsRegua = false;
    private String transacoesRegua;
    private String modeloExemploRegua;
    private String emailRegua;
    private String celularRegua;
    private String codigoContratoAtualizarImpressao;
    private Boolean atualizarImpressaoContratoNaoAssinado;
    private Boolean atualizarModeloContrato;
    private EmpresaVO empresaVOPagamentoPjBank;
    private EmpresaVO empresaVOPagamentoBancoItau;
    private Integer diasProcessarPagamentoPJBank;
    private Integer diasProcessarPagamentoBoletoItau;

    private String msgAviso;
    private Integer alterarParcelasRecibo;
    private String alterarParcelasParcelas;
    private String parcelasBoleto;

    private String codigoContratoCancelado;
    private Date reprocessarMovimentacaoAutomaticaConciliacaoAPartirDe = Calendario.hoje();
    private Date reprocessarMovimentacaoAutomaticaConciliacaoAte = Calendario.hoje();

    private Date novaDataFinalContratoCancelado;

    private Date dtInicioAjustarPagamentoPixPjBank = null;
    private Date dtFimAjustarPagamentoPixPjBank = null;
    private List<MovPagamentoVO> listaMovPgtoAjustarPagamentoPixPjBank = new ArrayList<>();
    private String msgExibirAjustarPagamentoPixPjBank = "";
    private String msgAposCorrecaoExibirAjustarPagamentoPixPjBank = "";
    private String pagadoresExibirAjustarPagamentoPixPjBank = "";
    private boolean sucessoAposCorrecaoAjustarPagamentoPixPjBank;
    private String codigoCidadeMescladaMantido = "";
    private String codigoCidadeMescladaRemovido = "";

    private String codigoMovParcelaDesvincularBoleto = "";
    private String codigosTurmasMigrarParaAulas = "";

    private String codigoMovParcelaDesvincularTransacao = "";
    private String codigoPixAlterarSituacaoAguardandoPagametno = "";
    private String codigoBoletoOnlineMudarSituacaoCanceladoParaAguardandoPagamento = "";
    private String codigoTransacaoAlterarSituacao = "";

    private String listaMovContasRetiradaLoteExcluirString = "";
    private String listaNossoNumeroBoletoCaixaOnlineCancelar;

    private boolean excluirMovContaTodosTipoOperacaoLancamento;
    private boolean tentarCancelarBoletosCaixaSemRegistro;

    private String cpf = "";
    private Date dataLancamento = null;

    public ManutencaoAjusteGeralTO(){
        this.clienteVO = new ClienteVO();
    }

    public Integer getTotalContratos() {
        return totalContratos;
    }

    public void setTotalContratos(Integer totalContratos) {
        this.totalContratos = totalContratos;
    }

    public String getMsgResultado() {
        return msgResultado;
    }

    public void setMsgResultado(String msgResultado) {
        this.msgResultado = msgResultado;
    }

    public Integer getCodigoPlano() {
        return codigoPlano;
    }

    public void setCodigoPlano(Integer codigoPlano) {
        this.codigoPlano = codigoPlano;
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public String getMatriculaClienteTokenGymPass() {
        return matriculaClienteTokenGymPass;
    }

    public void setMatriculaClienteTokenGymPass(String matriculaClienteTokenGymPass) {
        this.matriculaClienteTokenGymPass = matriculaClienteTokenGymPass;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public String getDescricaoNotaFiscalEmitida() {
        if (descricaoNotaFiscalEmitida == null) {
            descricaoNotaFiscalEmitida = "";
        }
        return descricaoNotaFiscalEmitida;
    }

    public void setDescricaoNotaFiscalEmitida(String descricaoNotaFiscalEmitida) {
        this.descricaoNotaFiscalEmitida = descricaoNotaFiscalEmitida;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public boolean isEncontrouCliente(){
        try {
            return UtilReflection.objetoMaiorQueZero(this, "getClienteVO().getPessoa().getCodigo()");
        }catch (Exception e){
            //
        }
        return false;
    }

    public Integer getQtdDiasBonus() {
        if (qtdDiasBonus == null) {
            qtdDiasBonus = 1;
        }
        return qtdDiasBonus;
    }

    public void setQtdDiasBonus(Integer qtdDiasBonus) {
        this.qtdDiasBonus = qtdDiasBonus;
    }

    public String getListaIdRPS() {
        if (listaIdRPS == null) {
            listaIdRPS = "";
        }
        return listaIdRPS;
    }

    public void setListaIdRPS(String listaIdRPS) {
        this.listaIdRPS = listaIdRPS;
    }
    
    
    public Integer getCodigoCupom() {
        return codigoCupom;
    }

    public void setCodigoCupom(Integer codigoCupom) {
        this.codigoCupom = codigoCupom;
    }

    public CupomFiscalVO getCupomFiscal() {
        return cupomFiscal;
    }

    public void setCupomFiscal(CupomFiscalVO cupomFiscal) {
        this.cupomFiscal = cupomFiscal;
    }

    public String getDescricaoCupomFiscal() {
        return descricaoCupomFiscal;
    }

    public void setDescricaoCupomFiscal(String descricaoCupomFiscal) {
        this.descricaoCupomFiscal = descricaoCupomFiscal;
    }

    public boolean isSucesso() {
        return sucesso;
}

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getListaIdNFCe() {
        if (listaIdNFCe == null) {
            listaIdNFCe = "";
        }
        return listaIdNFCe;
    }

    public void setListaIdNFCe(String listaIdNFCe) {
        this.listaIdNFCe = listaIdNFCe;
    }

    public String getListaIdNotaFamilia() {
        if(listaIdNotaFamilia == null) {
            listaIdNotaFamilia = "";
        }
        return listaIdNotaFamilia;
    }

    public void setListaIdNotaFamilia(String listaIdNotaFamilia) {
        this.listaIdNotaFamilia = listaIdNotaFamilia;
    }

    public Date getReprocessarAPartirDe() {
        return reprocessarAPartirDe;
    }

    public void setReprocessarAPartirDe(Date reprocessarAPartirDe) {
        this.reprocessarAPartirDe = reprocessarAPartirDe;
    }

    public Date getProcessarDataCompensacaoPjbankAPartirDe() {
        return processarDataCompensacaoPjbankAPartirDe;
    }

    public void setProcessarDataCompensacaoPjbankAPartirDe(Date processarDataCompensacaoPjbankAPartirDe) {
        this.processarDataCompensacaoPjbankAPartirDe = processarDataCompensacaoPjbankAPartirDe;
    }

    public Date getProcessarDataCompensacaoPjbankAte() {
        return processarDataCompensacaoPjbankAte;
    }

    public void setProcessarDataCompensacaoPjbankAte(Date processarDataCompensacaoPjbankAte) {
        this.processarDataCompensacaoPjbankAte = processarDataCompensacaoPjbankAte;
    }

    public boolean isEstornarRecibo() {
        return estornarRecibo;
    }

    public void setEstornarRecibo(boolean estornarRecibo) {
        this.estornarRecibo = estornarRecibo;
    }

    public List<EstornoTransacaoTO> getListaEstornoTransacao() {
        if (listaEstornoTransacao == null) {
            listaEstornoTransacao = new ArrayList<>();
        }
        return listaEstornoTransacao;
    }

    public void setListaEstornoTransacao(List<EstornoTransacaoTO> listaEstornoTransacao) {
        this.listaEstornoTransacao = listaEstornoTransacao;
    }

    public String getListaTransacaoEstornar() {
        if (listaTransacaoEstornar == null) {
            listaTransacaoEstornar = "";
        }
        return listaTransacaoEstornar;
    }

    public void setListaTransacaoEstornar(String listaTransacaoEstornar) {
        this.listaTransacaoEstornar = listaTransacaoEstornar;
    }

    public boolean isAviso() {
        return aviso;
    }

    public void setAviso(boolean aviso) {
        this.aviso = aviso;
    }

    public boolean isErro() {
        return erro;
    }

    public void setErro(boolean erro) {
        this.erro = erro;
    }

    public void limpar() {
        this.setSucesso(false);
        this.setAviso(false);
        this.setErro(false);
        this.setMsgResultado("");
    }

    public boolean isConsultou() {
        return consultou;
    }

    public void setConsultou(boolean consultou) {
        this.consultou = consultou;
    }

    public List<RemessaVO> getRemessasSemSituacao() {
        if (remessasSemSituacao == null) {
            remessasSemSituacao = new ArrayList<>();
        }
        return remessasSemSituacao;
    }

    public void setRemessasSemSituacao(List<RemessaVO> remessasSemSituacao) {
        this.remessasSemSituacao = remessasSemSituacao;
    }

    public Integer getRemessaExcluir() {
        return remessaExcluir;
    }

    public void setRemessaExcluir(Integer remessaExcluir) {
        this.remessaExcluir = remessaExcluir;
    }

    public Date getDataBloqueioCobrancaAutomatica() {
        return dataBloqueioCobrancaAutomatica;
    }

    public void setDataBloqueioCobrancaAutomatica(Date dataBloqueioCobrancaAutomatica) {
        this.dataBloqueioCobrancaAutomatica = dataBloqueioCobrancaAutomatica;
    }

    public TipoBloqueioCobrancaEnum getTipoBloqueioCobrancaAutomatica() {
        return tipoBloqueioCobrancaAutomatica;
    }

    public void setTipoBloqueioCobrancaAutomatica(TipoBloqueioCobrancaEnum tipoBloqueioCobrancaAutomatica) {
        this.tipoBloqueioCobrancaAutomatica = tipoBloqueioCobrancaAutomatica;
    }

    public List<PessoaBloqueioCobrancaTO> getListaPessoasBloqueioCobrancaAutomatica() {
        if (listaPessoasBloqueioCobrancaAutomatica == null) {
            listaPessoasBloqueioCobrancaAutomatica = new ArrayList<>();
        }
        return listaPessoasBloqueioCobrancaAutomatica;
    }

    public void setListaPessoasBloqueioCobrancaAutomatica(List<PessoaBloqueioCobrancaTO> listaPessoasBloqueioCobrancaAutomatica) {
        this.listaPessoasBloqueioCobrancaAutomatica = listaPessoasBloqueioCobrancaAutomatica;
    }

    public boolean isPessoasSemDataBloqueioCobrancaAutomatica() {
        return pessoasSemDataBloqueioCobrancaAutomatica;
    }

    public void setPessoasSemDataBloqueioCobrancaAutomatica(boolean pessoasSemDataBloqueioCobrancaAutomatica) {
        this.pessoasSemDataBloqueioCobrancaAutomatica = pessoasSemDataBloqueioCobrancaAutomatica;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public PlanoVO getPlanoVO() {
        if (planoVO == null) {
            planoVO = new PlanoVO();
        }
        return planoVO;
    }

    public void setPlanoVO(PlanoVO planoVO) {
        this.planoVO = planoVO;
    }

    public ModalidadeVO getModalidadeVO() {
        if (modalidadeVO == null) {
            modalidadeVO = new ModalidadeVO();
        }
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }

    public void setInformadoParcelaMudarStatus(int informadoParcelaMudarStatus){
        this.informadoParcelaMudarStatus = informadoParcelaMudarStatus;
    }

    public int getInformadoParcelaMudarStatus() {
        if(informadoParcelaMudarStatus == null){
            informadoParcelaMudarStatus = 0;
        }
        return informadoParcelaMudarStatus;
    }

    public Date getProcessarAPartirDe() {
        return processarAPartirDe;
    }

    public void setProcessarAPartirDe(Date processarAPartirDe) {
        this.processarAPartirDe = processarAPartirDe;
    }

    public Integer getInformadoRemessaMudarStatus() {
        if (informadoRemessaMudarStatus == null) {
            informadoRemessaMudarStatus = 0;
        }
        return informadoRemessaMudarStatus;
    }

    public void setInformadoRemessaMudarStatus(Integer informadoRemessaMudarStatus) {
        this.informadoRemessaMudarStatus = informadoRemessaMudarStatus;
    }

    public Integer getInformadoRemessaExcluir() {
        if (informadoRemessaExcluir == null) {
            informadoRemessaExcluir = 0;
        }
        return informadoRemessaExcluir;
    }

    public void setInformadoRemessaExcluir(Integer informadoRemessaExcluir) {
        this.informadoRemessaExcluir = informadoRemessaExcluir;
    }

    public List<AutorizacaoCobrancaClienteVO> getListaAutorizacaoCobrancaCliente() {
        if (listaAutorizacaoCobrancaCliente == null) {
            listaAutorizacaoCobrancaCliente = new ArrayList<>();
        }
        return listaAutorizacaoCobrancaCliente;
    }

    public void setListaAutorizacaoCobrancaCliente(List<AutorizacaoCobrancaClienteVO> listaAutorizacaoCobrancaCliente) {
        this.listaAutorizacaoCobrancaCliente = listaAutorizacaoCobrancaCliente;
    }

    public Integer getTipoObjetosCobrar() {
        return tipoObjetosCobrar;
    }

    public void setTipoObjetosCobrar(Integer tipoObjetosCobrar) {
        this.tipoObjetosCobrar = tipoObjetosCobrar;
    }

    public Date getReprocessarAte() {
        return reprocessarAte;
    }

    public void setReprocessarAte(Date reprocessarAte) {
        this.reprocessarAte = reprocessarAte;
    }

    public String getMatriculaClienteExclusao() {
        if (matriculaCliente == null){
            return "";
        }
        return matriculaClienteExclusao;
    }

    public void setMatriculaClienteExclusao(String matriculaClienteExclusao) {
        this.matriculaClienteExclusao = matriculaClienteExclusao;
    }

    public String getListaCodigosFaseMetaExcluir() {
        return listaCodigosFaseMetaExcluir;
    }

    public void setListaCodigosFaseMetaExcluir(String listaCodigosFaseMetaExcluir) {
        this.listaCodigosFaseMetaExcluir = listaCodigosFaseMetaExcluir;
    }

    public Date getExclusaoAPartirDe() {
        return exclusaoAPartirDe;
    }

    public void setExclusaoAPartirDe(Date exclusaoAPartirDe) {
        this.exclusaoAPartirDe = exclusaoAPartirDe;
    }

    public Date getExclusaoAte() {
        return exclusaoAte;
    }

    public void setExclusaoAte(Date exclusaoAte) {
        this.exclusaoAte = exclusaoAte;
    }

    public Integer getRemessaStatusGerada() {
        if (remessaStatusGerada == null) {
            remessaStatusGerada = 0;
        }
        return remessaStatusGerada;
    }

    public void setRemessaStatusGerada(Integer remessaStatusGerada) {
        this.remessaStatusGerada = remessaStatusGerada;
    }

    public String getCodigosNotas() {
        return codigosNotas;
    }

    public void setCodigosNotas(String codigosNotas) {
        this.codigosNotas = codigosNotas;
    }

    public String getJustificativaCancelamentoNotas() {
        return justificativaCancelamentoNotas;
    }

    public void setJustificativaCancelamentoNotas(String justificativaCancelamentoNotas) {
        this.justificativaCancelamentoNotas = justificativaCancelamentoNotas;
    }

    public String getListaCodigosPix() {
        return listaCodigosPix;
    }

    public void setListaCodigosPix(String listaCodigosPix) {
        this.listaCodigosPix = listaCodigosPix;
    }

    public String getListaCodigosGenericos() {
        if (listaCodigosGenericos == null) {
            listaCodigosGenericos = "";
        }
        return listaCodigosGenericos;
    }

    public void setListaCodigosGenericos(String listaCodigosGenericos) {
        this.listaCodigosGenericos = listaCodigosGenericos;
    }

    public Date getDataPagamentoPix() {
        return dataPagamentoPix;
    }

    public void setDataPagamentoPix(Date dataPagamentoPix) {
        this.dataPagamentoPix = dataPagamentoPix;
    }

    public String getListaCodigosGenericosBoletoCancelar() {
        if (listaCodigosGenericosBoletoCancelar == null) {
            listaCodigosGenericosBoletoCancelar = "";
        }
        return listaCodigosGenericosBoletoCancelar;
    }

    public void setListaCodigosGenericosBoletoCancelar(String listaCodigosGenericosBoletoCancelar) {
        this.listaCodigosGenericosBoletoCancelar = listaCodigosGenericosBoletoCancelar;
    }

    public EmpresaVO getEmpresaVOBoletoCancelar() {
        if (empresaVOBoletoCancelar == null) {
            empresaVOBoletoCancelar = new EmpresaVO();
        }
        return empresaVOBoletoCancelar;
    }

    public void setEmpresaVOBoletoCancelar(EmpresaVO empresaVOBoletoCancelar) {
        this.empresaVOBoletoCancelar = empresaVOBoletoCancelar;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVOBoletoCancelar() {
        if (convenioCobrancaVOBoletoCancelar == null) {
            convenioCobrancaVOBoletoCancelar = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVOBoletoCancelar;
    }

    public void setConvenioCobrancaVOBoletoCancelar(ConvenioCobrancaVO convenioCobrancaVOBoletoCancelar) {
        this.convenioCobrancaVOBoletoCancelar = convenioCobrancaVOBoletoCancelar;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getModelo() {
        return modelo;
    }

    public void setModelo(String modelo) {
        this.modelo = modelo;
    }

    public String getMatriculaRegua() {
        return matriculaRegua;
    }

    public void setMatriculaRegua(String matriculaRegua) {
        this.matriculaRegua = matriculaRegua;
    }

    public String getTransacoesRegua() {
        return transacoesRegua;
    }

    public void setTransacoesRegua(String transacoesRegua) {
        this.transacoesRegua = transacoesRegua;
    }

    public String getModeloExemploRegua() {
        return modeloExemploRegua;
    }

    public void setModeloExemploRegua(String modeloExemploRegua) {
        this.modeloExemploRegua = modeloExemploRegua;
    }

    public String getEmailRegua() {
        return emailRegua;
    }

    public void setEmailRegua(String emailRegua) {
        this.emailRegua = emailRegua;
    }

    public String getCelularRegua() {
        return celularRegua;
    }

    public void setCelularRegua(String celularRegua) {
        this.celularRegua = celularRegua;
    }

    public String getCodigoContratoAtualizarImpressao() {
        return codigoContratoAtualizarImpressao;
    }

    public void setCodigoContratoAtualizarImpressao(String codigoContratoAtualizarImpressao) {
        this.codigoContratoAtualizarImpressao = codigoContratoAtualizarImpressao;
    }

    public Boolean getAtualizarModeloContrato() {
        if (atualizarModeloContrato == null){
            atualizarModeloContrato = false;
        }
        return atualizarModeloContrato;
    }

    public void setAtualizarModeloContrato(Boolean atualizarModeloContrato) {
        this.atualizarModeloContrato = atualizarModeloContrato;
    }

    public Boolean getAtualizarImpressaoContratoNaoAssinado() {
        if (atualizarImpressaoContratoNaoAssinado == null){
            atualizarImpressaoContratoNaoAssinado = false;
        }
        return atualizarImpressaoContratoNaoAssinado;
    }

    public void setAtualizarImpressaoContratoNaoAssinado(Boolean atualizarImpressaoContratoNaoAssinado) {
        this.atualizarImpressaoContratoNaoAssinado = atualizarImpressaoContratoNaoAssinado;
    }

    public boolean isEncontrouPessoa(){
        return !UteisValidacao.emptyString(nomePessoa);
    }

    public Integer getDiasProcessarPagamentoPJBank() {
        if (diasProcessarPagamentoPJBank == null) {
            diasProcessarPagamentoPJBank = 2;
        }
        return diasProcessarPagamentoPJBank;
    }

    public void setDiasProcessarPagamentoPJBank(Integer diasProcessarPagamentoPJBank) {
        this.diasProcessarPagamentoPJBank = diasProcessarPagamentoPJBank;
    }

    public Integer getDiasProcessarPagamentoBoletoItau() {
        if (diasProcessarPagamentoBoletoItau == null) {
            diasProcessarPagamentoBoletoItau = 7;
        }
        return diasProcessarPagamentoBoletoItau;
    }

    public void setDiasProcessarPagamentoBoletoItau(Integer diasProcessarPagamentoBoletoItau) {
        this.diasProcessarPagamentoBoletoItau = diasProcessarPagamentoBoletoItau;
    }

    public EmpresaVO getEmpresaVOPagamentoPjBank() {
        if (empresaVOPagamentoPjBank == null) {
            empresaVOPagamentoPjBank = new EmpresaVO();
        }
        return empresaVOPagamentoPjBank;
    }

    public void setEmpresaVOPagamentoPjBank(EmpresaVO empresaVOPagamentoPjBank) {
        this.empresaVOPagamentoPjBank = empresaVOPagamentoPjBank;
    }

    public EmpresaVO getEmpresaVOPagamentoBancoItau() {
        if (empresaVOPagamentoBancoItau == null) {
            empresaVOPagamentoBancoItau = new EmpresaVO();
        }
        return empresaVOPagamentoBancoItau;
    }

    public void setEmpresaVOPagamentoBancoItau(EmpresaVO empresaVOPagamentoBancoItau) {
        this.empresaVOPagamentoBancoItau = empresaVOPagamentoBancoItau;
    }

    public String getMsgAviso() {
        return msgAviso;
    }

    public void setMsgAviso(String msgAviso) {
        this.msgAviso = msgAviso;
    }

    public Integer getAlterarParcelasRecibo() {
        return alterarParcelasRecibo;
    }

    public void setAlterarParcelasRecibo(Integer alterarParcelasRecibo) {
        this.alterarParcelasRecibo = alterarParcelasRecibo;
    }

    public String getAlterarParcelasParcelas() {
        return alterarParcelasParcelas;
    }

    public void setAlterarParcelasParcelas(String alterarParcelasParcelas) {
        this.alterarParcelasParcelas = alterarParcelasParcelas;
    }

    public String getCodigoContratoCancelado() {
        return codigoContratoCancelado;
    }

    public void setCodigoContratoCancelado(String codigoContratoCancelado) {
        this.codigoContratoCancelado = codigoContratoCancelado;
    }

    public Date getNovaDataFinalContratoCancelado() {
        return novaDataFinalContratoCancelado;
    }

    public void setNovaDataFinalContratoCancelado(Date novaDataFinalContratoCancelado) {
        this.novaDataFinalContratoCancelado = novaDataFinalContratoCancelado;
    }

    public Date getReprocessarMovimentacaoAutomaticaConciliacaoAPartirDe() {
        return reprocessarMovimentacaoAutomaticaConciliacaoAPartirDe;
    }

    public void setReprocessarMovimentacaoAutomaticaConciliacaoAPartirDe(Date reprocessarMovimentacaoAutomaticaConciliacaoAPartirDe) {
        this.reprocessarMovimentacaoAutomaticaConciliacaoAPartirDe = reprocessarMovimentacaoAutomaticaConciliacaoAPartirDe;
    }

    public Date getReprocessarMovimentacaoAutomaticaConciliacaoAte() {
        return reprocessarMovimentacaoAutomaticaConciliacaoAte;
    }

    public void setReprocessarMovimentacaoAutomaticaConciliacaoAte(Date reprocessarMovimentacaoAutomaticaConciliacaoAte) {
        this.reprocessarMovimentacaoAutomaticaConciliacaoAte = reprocessarMovimentacaoAutomaticaConciliacaoAte;
    }

    public Date getDtInicioAjustarPagamentoPixPjBank() {
        return dtInicioAjustarPagamentoPixPjBank;
    }

    public void setDtInicioAjustarPagamentoPixPjBank(Date dtInicioAjustarPagamentoPixPjBank) {
        this.dtInicioAjustarPagamentoPixPjBank = dtInicioAjustarPagamentoPixPjBank;
    }

    public Date getDtFimAjustarPagamentoPixPjBank() {
        return dtFimAjustarPagamentoPixPjBank;
    }

    public void setDtFimAjustarPagamentoPixPjBank(Date dtFimAjustarPagamentoPixPjBank) {
        this.dtFimAjustarPagamentoPixPjBank = dtFimAjustarPagamentoPixPjBank;
    }

    public List<MovPagamentoVO> getListaMovPgtoAjustarPagamentoPixPjBank() {
        return listaMovPgtoAjustarPagamentoPixPjBank;
    }

    public void setListaMovPgtoAjustarPagamentoPixPjBank(List<MovPagamentoVO> listaMovPgtoAjustarPagamentoPixPjBank) {
        this.listaMovPgtoAjustarPagamentoPixPjBank = listaMovPgtoAjustarPagamentoPixPjBank;
    }

    public String getMsgExibirAjustarPagamentoPixPjBank() {
        return msgExibirAjustarPagamentoPixPjBank;
    }

    public void setMsgExibirAjustarPagamentoPixPjBank(String msgExibirAjustarPagamentoPixPjBank) {
        this.msgExibirAjustarPagamentoPixPjBank = msgExibirAjustarPagamentoPixPjBank;
    }

    public String getPagadoresExibirAjustarPagamentoPixPjBank() {
        return pagadoresExibirAjustarPagamentoPixPjBank;
    }

    public void setPagadoresExibirAjustarPagamentoPixPjBank(String pagadoresExibirAjustarPagamentoPixPjBank) {
        this.pagadoresExibirAjustarPagamentoPixPjBank = pagadoresExibirAjustarPagamentoPixPjBank;
    }

    public String getMsgAposCorrecaoExibirAjustarPagamentoPixPjBank() {
        return msgAposCorrecaoExibirAjustarPagamentoPixPjBank;
    }

    public void setMsgAposCorrecaoExibirAjustarPagamentoPixPjBank(String msgAposCorrecaoExibirAjustarPagamentoPixPjBank) {
        this.msgAposCorrecaoExibirAjustarPagamentoPixPjBank = msgAposCorrecaoExibirAjustarPagamentoPixPjBank;
    }

    public boolean isSucessoAposCorrecaoAjustarPagamentoPixPjBank() {
        return sucessoAposCorrecaoAjustarPagamentoPixPjBank;
    }

    public void setSucessoAposCorrecaoAjustarPagamentoPixPjBank(boolean sucessoAposCorrecaoAjustarPagamentoPixPjBank) {
        this.sucessoAposCorrecaoAjustarPagamentoPixPjBank = sucessoAposCorrecaoAjustarPagamentoPixPjBank;
    }

    public boolean isConsultouRecibos() {
        return consultouRecibos;
    }

    public void setConsultouRecibos(boolean consultouRecibos) {
        this.consultouRecibos = consultouRecibos;
    }

    public String getListaRecibosEstornarString() {
        return listaRecibosEstornarString;
    }

    public void setListaRecibosEstornarString(String listaRecibosEstornarString) {
        this.listaRecibosEstornarString = listaRecibosEstornarString;
    }

    public List<ReciboPagamentoVO> getListaRecibosEstornar() {
        if (UteisValidacao.emptyList(listaRecibosEstornar)) {
            return new ArrayList<>();
        }
        return listaRecibosEstornar;
    }

    public void setListaRecibosEstornar(List<ReciboPagamentoVO> listaRecibosEstornar) {
        this.listaRecibosEstornar = listaRecibosEstornar;
    }

    public boolean isEstornarMovProduto() {
        return estornarMovProduto;
    }

    public void setEstornarMovProduto(boolean estornarMovProduto) {
        this.estornarMovProduto = estornarMovProduto;
    }

    public boolean isEnviarUsandoJenkinsRegua() {
        return enviarUsandoJenkinsRegua;
    }

    public void setEnviarUsandoJenkinsRegua(boolean enviarUsandoJenkinsRegua) {
        this.enviarUsandoJenkinsRegua = enviarUsandoJenkinsRegua;
    }

    public Date getDataInicioProcessoStone() {
        return dataInicioProcessoStone;
    }

    public void setDataInicioProcessoStone(Date dataInicioProcessoStone) {
        this.dataInicioProcessoStone = dataInicioProcessoStone;
    }

    public Date getDataFimProcessoStone() {
        return dataFimProcessoStone;
    }

    public void setDataFimProcessoStone(Date dataFimProcessoStone) {
        this.dataFimProcessoStone = dataFimProcessoStone;
    }

    public String getCodigoCidadeMescladaMantido() {
        return codigoCidadeMescladaMantido;
    }

    public void setCodigoCidadeMescladaMantido(String codigoCidadeMescladaMantido) {
        if (codigoCidadeMescladaMantido == null) {
            codigoCidadeMescladaMantido = "";
        }
        this.codigoCidadeMescladaMantido = codigoCidadeMescladaMantido;
    }

    public String getCodigoCidadeMescladaRemovido() {
        return codigoCidadeMescladaRemovido;
    }

    public void setCodigoCidadeMescladaRemovido(String codigoCidadeMescladaRemovido) {
        if (codigoCidadeMescladaRemovido == null) {
            codigoCidadeMescladaRemovido = "";
        }
        this.codigoCidadeMescladaRemovido = codigoCidadeMescladaRemovido;
    }

    public String getCodigoMovParcelaDesvincularBoleto() {
        return codigoMovParcelaDesvincularBoleto;
    }

    public void setCodigoMovParcelaDesvincularBoleto(String codigoMovParcelaDesvincularBoleto) {
        this.codigoMovParcelaDesvincularBoleto = codigoMovParcelaDesvincularBoleto;
    }

    public String getCodigoMovParcelaDesvincularTransacao() {
        if (UteisValidacao.emptyString(codigoMovParcelaDesvincularTransacao)) {
            return "";
        }
        return codigoMovParcelaDesvincularTransacao;
    }

    public void setCodigoPixAlterarSituacaoAguardandoPagametno(String codigoPixAlterarSituacaoAguardandoPagametno) {
        this.codigoPixAlterarSituacaoAguardandoPagametno = codigoPixAlterarSituacaoAguardandoPagametno;
    }

    public String getCodigoPixAlterarSituacaoAguardandoPagametno() {
        if (UteisValidacao.emptyString(codigoPixAlterarSituacaoAguardandoPagametno)) {
            return "";
        }
        return codigoPixAlterarSituacaoAguardandoPagametno;
    }

    public void setCodigoMovParcelaDesvincularTransacao(String codigoMovParcelaDesvincularTransacao) {
        this.codigoMovParcelaDesvincularTransacao = codigoMovParcelaDesvincularTransacao;
    }

    public String getCodigoBoletoOnlineMudarSituacaoCanceladoParaAguardandoPagamento() {
        return codigoBoletoOnlineMudarSituacaoCanceladoParaAguardandoPagamento;
    }

    public void setCodigoBoletoOnlineMudarSituacaoCanceladoParaAguardandoPagamento(String codigoBoletoOnlineMudarSituacaoCanceladoParaAguardandoPagamento) {
        this.codigoBoletoOnlineMudarSituacaoCanceladoParaAguardandoPagamento = codigoBoletoOnlineMudarSituacaoCanceladoParaAguardandoPagamento;
    }

    public String getCodigoTransacaoAlterarSituacao() {
        return codigoTransacaoAlterarSituacao;
    }

    public void setCodigoTransacaoAlterarSituacao(String codigoTransacaoAlterarSituacao) {
        this.codigoTransacaoAlterarSituacao = codigoTransacaoAlterarSituacao;
    }

    public String getListaMovContasRetiradaLoteExcluirString() {
        return listaMovContasRetiradaLoteExcluirString;
    }

    public void setListaMovContasRetiradaLoteExcluirString(String listaMovContasRetiradaLoteExcluirString) {
        this.listaMovContasRetiradaLoteExcluirString = listaMovContasRetiradaLoteExcluirString;
    }

    public String getCodigosTurmasMigrarParaAulas() {
        return codigosTurmasMigrarParaAulas;
    }

    public void setCodigosTurmasMigrarParaAulas(String codigosTurmasMigrarParaAulas) {
        this.codigosTurmasMigrarParaAulas = codigosTurmasMigrarParaAulas;
    }

    public String getParcelasBoleto() {
        return parcelasBoleto;
    }

    public void setParcelasBoleto(String parcelasBoleto) {
        this.parcelasBoleto = parcelasBoleto;
    }

    public String getListaNossoNumeroBoletoCaixaOnlineCancelar() {
        if (UteisValidacao.emptyString(listaNossoNumeroBoletoCaixaOnlineCancelar)) {
            return "";
        }
        return listaNossoNumeroBoletoCaixaOnlineCancelar;
    }

    public void setListaNossoNumeroBoletoCaixaOnlineCancelar(String listaNossoNumeroBoletoCaixaOnlineCancelar) {
        this.listaNossoNumeroBoletoCaixaOnlineCancelar = listaNossoNumeroBoletoCaixaOnlineCancelar;
    }

    public boolean isExcluirMovContaTodosTipoOperacaoLancamento() {
        return excluirMovContaTodosTipoOperacaoLancamento;
    }

    public void setExcluirMovContaTodosTipoOperacaoLancamento(boolean excluirMovContaTodosTipoOperacaoLancamento) {
        this.excluirMovContaTodosTipoOperacaoLancamento = excluirMovContaTodosTipoOperacaoLancamento;
    }

    public boolean isTentarCancelarBoletosCaixaSemRegistro() {
        return tentarCancelarBoletosCaixaSemRegistro;
    }

    public void setTentarCancelarBoletosCaixaSemRegistro(boolean tentarCancelarBoletosCaixaSemRegistro) {
        this.tentarCancelarBoletosCaixaSemRegistro = tentarCancelarBoletosCaixaSemRegistro;
    }

    public String getCpf() { return this.cpf; }

    public void setCpf(String cpf) { this.cpf = cpf; }

    public Date getDataLancamento() { return this.dataLancamento; }

    public void setDataLancamento(Date dataLancamento) { this.dataLancamento = dataLancamento; }
}
