package negocio.comuns.basico.enumerador;

import br.com.pactosolucoes.comuns.util.JSFUtilities;

import javax.faces.model.SelectItem;
import java.util.List;

/**
 * Created by <PERSON> on 26/11/2015.
 */
public enum TipoPessoa {
    FISICA(0,"Física"),
    JURIDICA(1,"Jurídica");

    private int codigo;
    private String label;

    private TipoPessoa(int codigo,String label){
        this.codigo = codigo;
        this.label = label;
    }

    public static TipoPessoa obterPorCodigo(int codigo){
        for(TipoPessoa item : TipoPessoa.values()){
            if(item.getCodigo() == codigo)
                return item;
        }
        return null;
    }

    public static TipoPessoa obterPorLabel(String descricao) {
        for (TipoPessoa obj : TipoPessoa.values()) {
            if (obj.name().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return null;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
    public static List<SelectItem> getObterListaSI(){
        return JSFUtilities.getSelectItemListFromEnum(TipoPessoa.class,"label",false);
    }
}
