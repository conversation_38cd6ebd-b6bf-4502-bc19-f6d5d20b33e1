package negocio.comuns.contrato;

import annotations.arquitetura.CampoCalendario;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade ConvenioDesconto. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class ConvenioDescontoVO extends SuperVO {

    protected Integer codigo;
    protected String descricao = "";
    @CampoCalendario
    protected Date dataAssinatura;
    @CampoCalendario
    protected Date dataInicioVigencia;
    @CampoCalendario
    protected Date dataFinalVigencia;
    @FKJson
    protected UsuarioVO responsavelAutorizacao;
    @CampoCalendario
    protected Date dataAutorizacao;

    private Boolean isentarMatricula;

    private Boolean isentarRematricula;
    protected Boolean convenioDescontoEscolhida;
    /**
     * Atributo responsável por manter os objetos da classe <code>ConvenioDescontoConfiguracao</code>.
     */
    @NaoControlarLogAlteracao
    @ListJson(clazz = ConvenioDescontoConfiguracaoVO.class)
    private List<ConvenioDescontoConfiguracaoVO> convenioDescontoConfiguracaoVOs;
    @NaoControlarLogAlteracao
    private List<ConvenioDescontoConfiguracaoVO> convenioDescontoConfiguracaoVOsAntesAlteracao;
    @NaoControlarLogAlteracao
    @ListJson(clazz = ConvenioDescontoPlanoConfiguracaoVO.class)
    private List<ConvenioDescontoPlanoConfiguracaoVO> convenioDescontoPlanoConfiguracaoVOs;
    @ChaveEstrangeira
    @FKJson
    private EmpresaVO empresa;

    /**
     * Construtor padrão da classe <code>ConvenioDesconto</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ConvenioDescontoVO() {
        super();
        inicializarDados();
    }

    public ConvenioDescontoVO(Date data) {
        super();
        inicializarDados(data);
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ConvenioDescontoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(ConvenioDescontoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Convênio Desconto) deve ser informado.");
        }
        if (obj.getDataAssinatura() == null) {
            throw new ConsistirException("O campo DATA DE ASSINATURA (Convênio Desconto) deve ser informado.");
        }
        if (obj.getDataInicioVigencia() == null) {
            throw new ConsistirException("O campo DATA INICIO DE VIGENCIA (Convênio Desconto) deve ser informado.");
        }
        if (obj.getDataFinalVigencia() == null) {
            throw new ConsistirException("O campo DATA FINAL DE VIGENCIA (Convênio Desconto) deve ser informado.");
        }

        if (obj.getDataAutorizacao() == null) {
            throw new ConsistirException("O campo DATA AUTORIZAÇÃO (Convênio Desconto) deve ser informado.");
        }
    }

    public String getResponsavel_Apresentar() {
        return getResponsavelAutorizacao().getNome();
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        inicializarDados(null);
    }

    public void inicializarDados(Date data) {
        if(data == null) {
            data = negocio.comuns.utilitarias.Calendario.hoje();
        }
        setCodigo(new Integer(0));
        setDescricao("");
        setDataAssinatura(data);
        setDataInicioVigencia(data);
        setDataFinalVigencia(data);
        setResponsavelAutorizacao(new UsuarioVO());
        setDataAutorizacao(data);
        setIsentarMatricula(new Boolean(false));
        setIsentarRematricula(new Boolean(false));
        setConvenioDescontoEscolhida(new Boolean(false));
        setConvenioDescontoConfiguracaoVOs(new ArrayList());
        setConvenioDescontoPlanoConfiguracaoVOs(new ArrayList<>());
        setConvenioDescontoConfiguracaoVOsAntesAlteracao(new ArrayList<ConvenioDescontoConfiguracaoVO>());
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>ConvenioDescontoConfiguracaoVO</code>
     * ao List <code>convenioDescontoConfiguracaoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ConvenioDescontoConfiguracao</code> - getDuracao().getCodigo() - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ConvenioDescontoConfiguracaoVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjConvenioDescontoConfiguracaoVOs(ConvenioDescontoConfiguracaoVO obj) throws Exception {
        ConvenioDescontoConfiguracaoVO.validarDados(obj);
        int index = 0;
        Iterator i = getConvenioDescontoConfiguracaoVOs().iterator();
        while (i.hasNext()) {
            ConvenioDescontoConfiguracaoVO objExistente = (ConvenioDescontoConfiguracaoVO) i.next();
            if (objExistente.getDuracao().equals(obj.getDuracao())) {
                getConvenioDescontoConfiguracaoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getConvenioDescontoConfiguracaoVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>ConvenioDescontoConfiguracaoVO</code>
     * no List <code>convenioDescontoConfiguracaoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ConvenioDescontoConfiguracao</code> - getDuracao().getCodigo() - como identificador (key) do objeto no List.
     *
     * @param duracao Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjConvenioDescontoConfiguracaoVOs(Integer duracao) throws Exception {
        int index = 0;
        Iterator i = getConvenioDescontoConfiguracaoVOs().iterator();
        while (i.hasNext()) {
            ConvenioDescontoConfiguracaoVO objExistente = (ConvenioDescontoConfiguracaoVO) i.next();
            if (objExistente.getDuracao().equals(duracao)) {
                getConvenioDescontoConfiguracaoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>ConvenioDescontoConfiguracaoVO</code>
     * no List <code>convenioDescontoConfiguracaoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ConvenioDescontoConfiguracao</code> - getDuracao().getCodigo() - como identificador (key) do objeto no List.
     *
     * @param duracao Parâmetro para localizar o objeto do List.
     */
    public ConvenioDescontoConfiguracaoVO consultarObjConvenioDescontoConfiguracaoVO(Integer duracao) throws Exception {
        Iterator i = getConvenioDescontoConfiguracaoVOs().iterator();
        while (i.hasNext()) {
            ConvenioDescontoConfiguracaoVO objExistente = (ConvenioDescontoConfiguracaoVO) i.next();
            if (objExistente.getDuracao().equals(duracao)) {
                return objExistente;
            }
        }
        return null;
    }

    public List<ConvenioDescontoConfiguracaoVO> getConvenioDescontoConfiguracaoVOs() {
        return convenioDescontoConfiguracaoVOs;
    }

    public void setConvenioDescontoConfiguracaoVOs(List<ConvenioDescontoConfiguracaoVO> convenioDescontoConfiguracaoVOs) {
        this.convenioDescontoConfiguracaoVOs = convenioDescontoConfiguracaoVOs;
    }

    public Boolean getIsentarMatricula() {
        if (isentarMatricula == null){
            isentarMatricula = false;
        }
        return isentarMatricula;
    }

    public void setIsentarMatricula(Boolean isentarMatricula) {
        this.isentarMatricula = isentarMatricula;
    }

    public Boolean getIsentarRematricula() {
        if (isentarRematricula == null){
            isentarRematricula = false;
        }
        return isentarRematricula;
    }

    public void setIsentarRematricula(Boolean isentarRematricula) {
        this.isentarRematricula = isentarRematricula;
    }

    public Date getDataAutorizacao() {
        return (dataAutorizacao);
    }

    public void setDataAutorizacao(Date dataAutorizacao) {
        this.dataAutorizacao = dataAutorizacao;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataAutorizacao_Apresentar() {
        return (Uteis.getData(dataAutorizacao));
    }

    public UsuarioVO getResponsavelAutorizacao() {
        return responsavelAutorizacao;
    }

    public void setResponsavelAutorizacao(UsuarioVO responsavelAutorizacao) {
        this.responsavelAutorizacao = responsavelAutorizacao;
    }


    public Date getDataFinalVigencia() {
        return (dataFinalVigencia);
    }

    public void setDataFinalVigencia(Date dataFinalVigencia) {
        this.dataFinalVigencia = dataFinalVigencia;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataFinalVigencia_Apresentar() {
        return (Uteis.getData(dataFinalVigencia));
    }

    public Date getDataInicioVigencia() {
        return (dataInicioVigencia);
    }

    public void setDataInicioVigencia(Date dataInicioVigencia) {
        this.dataInicioVigencia = dataInicioVigencia;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataInicioVigencia_Apresentar() {
        return (Uteis.getData(dataInicioVigencia));
    }

    public Date getDataAssinatura() {
        return (dataAssinatura);
    }

    public void setDataAssinatura(Date dataAssinatura) {
        this.dataAssinatura = dataAssinatura;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataAssinatura_Apresentar() {
        return (Uteis.getData(dataAssinatura));
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getConvenioDescontoEscolhida() {
        return convenioDescontoEscolhida;
    }

    public void setConvenioDescontoEscolhida(Boolean convenioDescontoEscolhida) {
        this.convenioDescontoEscolhida = convenioDescontoEscolhida;
    }


    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public List<ConvenioDescontoConfiguracaoVO> getConvenioDescontoConfiguracaoVOsAntesAlteracao() {
        return convenioDescontoConfiguracaoVOsAntesAlteracao;
    }

    public void setConvenioDescontoConfiguracaoVOsAntesAlteracao(List<ConvenioDescontoConfiguracaoVO> convenioDescontoConfiguracaoVOsAntesAlteracao) {
        this.convenioDescontoConfiguracaoVOsAntesAlteracao = convenioDescontoConfiguracaoVOsAntesAlteracao;
    }
    
    public void registrarConfiguracaoVOsAntesDaAlteracao() throws Exception {
        convenioDescontoConfiguracaoVOsAntesAlteracao = new ArrayList<ConvenioDescontoConfiguracaoVO>();
        for(ConvenioDescontoConfiguracaoVO config: convenioDescontoConfiguracaoVOs){
            convenioDescontoConfiguracaoVOsAntesAlteracao.add((ConvenioDescontoConfiguracaoVO) config.getClone(true));
        }
    }

    public List<ConvenioDescontoPlanoConfiguracaoVO> getConvenioDescontoPlanoConfiguracaoVOs() {
        if (convenioDescontoPlanoConfiguracaoVOs == null) {
            convenioDescontoPlanoConfiguracaoVOs = new ArrayList<>();
        }
        return convenioDescontoPlanoConfiguracaoVOs;
    }

    public void setConvenioDescontoPlanoConfiguracaoVOs(List<ConvenioDescontoPlanoConfiguracaoVO> convenioDescontoPlanoConfiguracaoVOs) {
        this.convenioDescontoPlanoConfiguracaoVOs = convenioDescontoPlanoConfiguracaoVOs;
    }
}
