package negocio.comuns.contrato;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.ProdutoTextoPadraoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

public class ProdutoAssinaturaDigitalVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo;
    private UsuarioVO usuarioResponsavel;
    private ProdutoTextoPadraoVO produtoTextoPadrao;
    private String documentos;
    private String endereco;
    private String assinatura;
    private String assinatura2;
    private String atestado;
    private String anexo1;
    private String anexo2;
    private String anexoCancelamento;
    private Date lancamento;
    @NaoControlarLogAlteracao
    private String documentos_Apresentar;
    @NaoControlarLogAlteracao
    private String assinatura_Apresentar;
    private String assinatura2_Apresentar;
    @NaoControlarLogAlteracao
    private String atestado_Apresentar;
    @NaoControlarLogAlteracao
    private String endereco_Apresentar;
    @NaoControlarLogAlteracao
    private String anexo1_Apresentar;
    @NaoControlarLogAlteracao
    private String anexo2_Apresentar;
    @NaoControlarLogAlteracao
    private String anexoCancelamento_Apresentar;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public ProdutoTextoPadraoVO getProdutoTextoPadrao() {
        return produtoTextoPadrao;
    }

    public void setProdutoTextoPadrao(ProdutoTextoPadraoVO produtoTextoPadrao) {
        this.produtoTextoPadrao = produtoTextoPadrao;
    }

    public String getDocumentos() {
        return documentos;
    }

    public void setDocumentos(String documentos ) {
        this.documentos = documentos;
        if (documentos != null && !UteisValidacao.emptyString(documentos) && !documentos.contains("image_icon.jpg")){
            setDocumentos_Apresentar(Uteis.getPaintFotoDaNuvem(documentos));
        }
    }

    public String getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(String assinatura) {
        this.assinatura = assinatura;
        if (assinatura != null){
            setAssinatura_Apresentar(Uteis.getPaintFotoDaNuvem(assinatura));
        }
    }

    public String getAtestado() {
        return atestado;
    }

    public void setAtestado(String atestado) {
        this.atestado = atestado;
        if (atestado != null && !UteisValidacao.emptyString(atestado) && !atestado.contains("image_icon.jpg")) {
            setAtestado_Apresentar(Uteis.getPaintFotoDaNuvem(atestado));
        }
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
        if (endereco != null && !UteisValidacao.emptyString(endereco) && !endereco.contains("image_icon.jpg")) {
            setEndereco_Apresentar(Uteis.getPaintFotoDaNuvem(endereco));
        }
    }

    public void setAnexo1(String anexo1) {
        this.anexo1 = anexo1;
        if (anexo1 != null && !UteisValidacao.emptyString(anexo1) && !anexo1.contains("image_icon.jpg")) {
            setAnexo1_Apresentar(Uteis.getPaintFotoDaNuvem(anexo1));
        }
    }

    public String getAnexo1() {
        return anexo1;
    }

    public void setAnexo2(String anexo2) {
        this.anexo2 = anexo2;
        if (anexo2 != null && !UteisValidacao.emptyString(anexo2) && !anexo2.contains("image_icon.jpg")) {
            setAnexo2_Apresentar(Uteis.getPaintFotoDaNuvem(anexo2));
        }
    }

    public String getAnexo2() {
        return anexo2;
    }

    public void setAnexoCancelamento(String anexoCancelamento) {
        this.anexoCancelamento = anexoCancelamento;
        if (anexoCancelamento != null && !UteisValidacao.emptyString(anexoCancelamento) && !anexoCancelamento.contains("image_icon.jpg")) {
            setAnexoCancelamento_Apresentar(Uteis.getPaintFotoDaNuvem(anexoCancelamento));
        }
    }

    public String getAnexoCancelamento() {
        return anexoCancelamento;
    }


    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public void initImagens() {
        String imagemPadrao = Uteis.getPaintImagemDaNuvem("", "../");
        if (getDocumentos() == null || getDocumentos().isEmpty())
            setDocumentos_Apresentar(imagemPadrao);
        if (getEndereco() == null || getEndereco().isEmpty())
            setEndereco_Apresentar(imagemPadrao);
        if (getAtestado() == null || getAtestado().isEmpty())
            setAtestado_Apresentar(imagemPadrao);
        if (getAnexo1() == null || getAnexo1().isEmpty())
            setAnexo1_Apresentar(imagemPadrao);
        if (getAnexo2() == null || getAnexo2().isEmpty())
            setAnexo2_Apresentar(imagemPadrao);
        if (getAnexoCancelamento() == null || getAnexoCancelamento().isEmpty())
            setAnexoCancelamento_Apresentar(imagemPadrao);
    }

    public String getDocumentos_Apresentar() {
        return documentos_Apresentar;
    }
    public void setDocumentos_Apresentar(String documentos_Apresentar){
        this.documentos_Apresentar = documentos_Apresentar;
    }


    public String getAssinatura_Apresentar() {
        return assinatura_Apresentar;
    }

    public void setAssinatura_Apresentar(String assinatura_Apresentar) {
        this.assinatura_Apresentar = assinatura_Apresentar;
    }

    public String getAtestado_Apresentar() {
        return atestado_Apresentar;
    }

    public void setAtestado_Apresentar(String atestado_Apresentar) {
        this.atestado_Apresentar = atestado_Apresentar;
    }

    public String getEndereco_Apresentar() {
        return endereco_Apresentar;
    }

    public void setEndereco_Apresentar(String endereco_Apresentar) {
            this.endereco_Apresentar = endereco_Apresentar;
    }

    public String getAnexo1_Apresentar() {
        return anexo1_Apresentar;
    }

    public void setAnexo1_Apresentar(String anexo1_Apresentar) {
        this.anexo1_Apresentar = anexo1_Apresentar;
    }

    public String getAnexo2_Apresentar() {
        return anexo2_Apresentar;
    }

    public void setAnexo2_Apresentar(String anexo2) {
            this.anexo2_Apresentar = anexo2;
    }

    public String getAnexoCancelamento_Apresentar() {
        return anexoCancelamento_Apresentar;
    }

    public void setAnexoCancelamento_Apresentar(String anexoCancelamento_Apresentar) {
        this.anexoCancelamento_Apresentar = anexoCancelamento_Apresentar;
    }

    public String getAssinatura2_Apresentar() {
        return assinatura2_Apresentar;
    }

    public void setAssinatura2_Apresentar(String assinatura2_Apresentar) {
        this.assinatura2_Apresentar = assinatura2_Apresentar;
    }

    public String getAssinatura2() {
        return assinatura2;
    }

    public void setAssinatura2(String assinatura2) {
        this.assinatura2 = assinatura2;
        if (assinatura2 != null){
            setAssinatura2_Apresentar(Uteis.getPaintFotoDaNuvem(assinatura2));
        }
    }
}
