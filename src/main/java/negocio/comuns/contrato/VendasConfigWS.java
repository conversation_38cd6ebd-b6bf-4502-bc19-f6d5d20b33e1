package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by GlaucoT on 05/02/2015
 */
public class VendasConfigWS extends SuperTO {

    private Integer codigo;
    private Integer empresa;
    private boolean redirecionarApp = true;
    private String paginaRedirecionar;
    private String urlvenda;
    private String emailAvisar;
    private String cor;
    private String camposAdicionais;
    private String analyticsId;
    private String pixelId;
    private String tokenApiConversao;
    private String googleTagId;
    private String googleTagIdHotsite;
    private boolean cobrarPrimeiraParcelaCompra;
    private boolean cobrarPrimeiraParcelaCompraRenovacao;
    private boolean detalharParcelaTelaCheckout;
    private boolean cobrarProdutosJuntoAdesao;
    private String tituloCheckout;
    private boolean apresentarValorAnuidade;
    private boolean apresentarProdutoSemEstoque;
    private boolean usarConvenioPlanoProduto;
    private boolean usarFormaPagamentoPlanoProduto;
    private boolean apresentarCPFLinkPag;
    private boolean apresentarDtFaturaLinkPag;
    private boolean apresentarTermoAceiteLinkPag;
    private boolean renovarContratoAntigo;
    private boolean cobrarParcelasMesSeguinteRenovacao;
    private boolean permitirMudarTipoParcelamento;
    private boolean habilitarPreCadastro;
    private boolean configSescHabilitada;
    private String igopassUrl;
    private String igopassQrCodeUrl;
    private String igopassEstabelecimento;
    private String igopassUsuario;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public boolean isRedirecionarApp() {
        return redirecionarApp;
    }

    public void setRedirecionarApp(boolean redirecionarApp) {
        this.redirecionarApp = redirecionarApp;
    }

    public String getPaginaRedirecionar() {
        return paginaRedirecionar;
    }

    public void setPaginaRedirecionar(String paginaRedirecionar) {
        this.paginaRedirecionar = paginaRedirecionar;
    }

    public String getUrlvenda() {
        return urlvenda;
    }

    public void setUrlvenda(String urlvenda) {
        this.urlvenda = urlvenda;
    }

    public String getEmailAvisar() {
        return emailAvisar;
    }

    public void setEmailAvisar(String emailAvisar) {
        this.emailAvisar = emailAvisar;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getCamposAdicionais() {
        return camposAdicionais;
    }

    public void setCamposAdicionais(String camposAdicionais) {
        this.camposAdicionais = camposAdicionais;
    }

    public String getAnalyticsId() {
        return analyticsId;
    }

    public void setAnalyticsId(String analyticsId) {
        this.analyticsId = analyticsId;
    }

    public String getPixelId() {
        return pixelId;
    }

    public void setPixelId(String pixelId) {
        this.pixelId = pixelId;
    }

    public String getTokenApiConversao() {
        return tokenApiConversao;
    }

    public void setTokenApiConversao(String tokenApiConversao) {
        this.tokenApiConversao = tokenApiConversao;
    }

    public boolean isCobrarPrimeiraParcelaCompra() {
        return cobrarPrimeiraParcelaCompra;
    }

    public void setCobrarPrimeiraParcelaCompra(boolean cobrarPrimeiraParcelaCompra) {
        this.cobrarPrimeiraParcelaCompra = cobrarPrimeiraParcelaCompra;
    }

    public boolean isCobrarPrimeiraParcelaCompraRenovacao() {
        return cobrarPrimeiraParcelaCompraRenovacao;
    }

    public void setCobrarPrimeiraParcelaCompraRenovacao(boolean cobrarPrimeiraParcelaCompraRenovacao) {
        this.cobrarPrimeiraParcelaCompraRenovacao = cobrarPrimeiraParcelaCompraRenovacao;
    }

    public boolean isDetalharParcelaTelaCheckout() {
        return detalharParcelaTelaCheckout;
    }

    public void setDetalharParcelaTelaCheckout(boolean detalharParcelaTelaCheckout) {
        this.detalharParcelaTelaCheckout = detalharParcelaTelaCheckout;
    }

    public boolean isCobrarProdutosJuntoAdesao() {
        return cobrarProdutosJuntoAdesao;
    }

    public void setCobrarProdutosJuntoAdesao(boolean cobrarProdutosJuntoAdesao) {
        this.cobrarProdutosJuntoAdesao = cobrarProdutosJuntoAdesao;
    }

    public String getTituloCheckout() {
        return tituloCheckout;
    }

    public void setTituloCheckout(String tituloCheckout) {
        this.tituloCheckout = tituloCheckout;
    }

    public boolean isApresentarValorAnuidade() {
        return apresentarValorAnuidade;
    }

    public void setApresentarValorAnuidade(boolean apresentarValorAnuidade) {
        this.apresentarValorAnuidade = apresentarValorAnuidade;
    }

    public boolean isApresentarProdutoSemEstoque() {
        return apresentarProdutoSemEstoque;
    }

    public void setApresentarProdutoSemEstoque(boolean apresentarProdutoSemEstoque) {
        this.apresentarProdutoSemEstoque = apresentarProdutoSemEstoque;
    }

    public boolean isUsarConvenioPlanoProduto() {
        return usarConvenioPlanoProduto;
    }

    public void setUsarConvenioPlanoProduto(boolean usarConvenioPlanoProduto) {
        this.usarConvenioPlanoProduto = usarConvenioPlanoProduto;
    }

    public boolean isApresentarCPFLinkPag() {
        return apresentarCPFLinkPag;
    }

    public void setApresentarCPFLinkPag(boolean apresentarCPFLinkPag) {
        this.apresentarCPFLinkPag = apresentarCPFLinkPag;
    }

    public boolean isApresentarDtFaturaLinkPag() {
        return apresentarDtFaturaLinkPag;
    }

    public void setApresentarDtFaturaLinkPag(boolean apresentarDtFaturaLinkPag) {
        this.apresentarDtFaturaLinkPag = apresentarDtFaturaLinkPag;
    }

    public boolean isApresentarTermoAceiteLinkPag() {
        return apresentarTermoAceiteLinkPag;
    }

    public void setApresentarTermoAceiteLinkPag(boolean apresentarTermoAceiteLinkPag) {
        this.apresentarTermoAceiteLinkPag = apresentarTermoAceiteLinkPag;
    }

    public boolean isRenovarContratoAntigo() {
        return renovarContratoAntigo;
    }

    public void setRenovarContratoAntigo(boolean renovarContratoAntigo) {
        this.renovarContratoAntigo = renovarContratoAntigo;
    }

    public boolean isCobrarParcelasMesSeguinteRenovacao() {
        return cobrarParcelasMesSeguinteRenovacao;
    }

    public void setCobrarParcelasMesSeguinteRenovacao(boolean cobrarParcelasMesSeguinteRenovacao) {
        this.cobrarParcelasMesSeguinteRenovacao = cobrarParcelasMesSeguinteRenovacao;
    }

    public String getGoogleTagId() {
        return googleTagId;
    }

    public void setGoogleTagId(String googleTagId) {
        this.googleTagId = googleTagId;
    }

    public boolean isPermitirMudarTipoParcelamento() {
        return permitirMudarTipoParcelamento;
    }

    public void setPermitirMudarTipoParcelamento(boolean permitirMudarTipoParcelamento) {
        this.permitirMudarTipoParcelamento = permitirMudarTipoParcelamento;
    }

    public boolean isUsarFormaPagamentoPlanoProduto() {
        return usarFormaPagamentoPlanoProduto;
    }

    public void setUsarFormaPagamentoPlanoProduto(boolean usarFormaPagamentoPlanoProduto) {
        this.usarFormaPagamentoPlanoProduto = usarFormaPagamentoPlanoProduto;
    }

    public boolean isHabilitarPreCadastro() {
        return habilitarPreCadastro;
    }

    public void setHabilitarPreCadastro(boolean habilitarPreCadastro) {
        this.habilitarPreCadastro = habilitarPreCadastro;
    }

    public boolean isConfigSescHabilitada() {
        return configSescHabilitada;
    }

    public void setConfigSescHabilitada(boolean configSescHabilitada) {
        this.configSescHabilitada = configSescHabilitada;
    }

    public String getGoogleTagIdHotsite() {
        return googleTagIdHotsite;
    }

    public void setGoogleTagIdHotsite(String googleTagIdHotsite) {
        this.googleTagIdHotsite = googleTagIdHotsite;
    }

    // Integração Igopass - controle de trava magnetica para armários, geladeiras e etc
    public String getIgopassUrl() {
        return igopassUrl;
    }

    public void setIgopassUrl(String igopassUrl) {
        this.igopassUrl = igopassUrl;
    }

    public String getIgopassQrCodeUrl() {
        return igopassQrCodeUrl;
    }

    public void setIgopassQrCodeUrl(String igopassQrCodeUrl) {
        this.igopassQrCodeUrl = igopassQrCodeUrl;
    }

    public String getIgopassEstabelecimento() {
        return igopassEstabelecimento;
    }

    public void setIgopassEstabelecimento(String igopassEstabelecimento) {
        this.igopassEstabelecimento = igopassEstabelecimento;
    }

    public String getIgopassUsuario() {
        return igopassUsuario;
    }

    public void setIgopassUsuario(String igopassUsuario) {
        this.igopassUsuario = igopassUsuario;
    }
}
