package negocio.comuns.contrato;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.SituacaoRenovacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import controle.contrato.ParcelasEditarNegociacaoNovo;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.GrupoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.plano.ComposicaoModalidadeVO;
import negocio.comuns.plano.HistoricoTurmasContratoTO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.PlanoComposicaoVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoEmpresaVO;
import negocio.comuns.plano.PlanoExcecaoVO;
import negocio.comuns.plano.PlanoHorarioVO;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.comuns.plano.PlanoRecorrenciaParcelaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.ProdutoWS;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoDuracaoCreditoTreino;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.contrato.HistoricoContrato;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.vendas.VendasConfigVO;
import negocio.interfaces.contrato.HistoricoContratoInterfaceFacade;

import java.sql.Connection;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Reponsável por manter os dados da entidade Contrato. Classe do tipo VO -
 * Value Object composta pelos atributos da entidade com visibilidade protegida
 * e os métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class ContratoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String situacao;
    protected String observacao;
    protected String nomeModalidades;
    protected Boolean estendeCoberturaFamiliares;
    protected Boolean bolsa;
    protected Boolean vendaContrato = false;
    protected Date vigenciaDe;
    protected Date vigenciaAte;
    protected Double valorBaseCalculo;
    protected Double valorBaseCalculoAlteracao;
    protected Double valorFinal;
    private double valorContrato;
    private double valorPrimeiraParcela = 0.0;
    private String duracaoContratoApresentar;
    @Lista
    private List historicoContratoVOs;
    @Lista
    private List periodoAcessoClienteVOs;
    @Lista
    private List<ContratoModalidadeVO> contratoModalidadeVOs;
    @Lista
    private List contratoComposicaoVOs;
    @Lista
    private List contratoPlanoProdutoSugeridoVOs;
    @Lista
    private List movProdutoVOs;
    @Lista
    private List movParcelaVOs;
    @ChaveEstrangeira
    protected EmpresaVO empresa;
    @ChaveEstrangeira
    protected PessoaVO pessoa;
    @ChaveEstrangeira
    protected ColaboradorVO consultor;
    @ChaveEstrangeira
    protected PlanoVO plano;
    @ChaveEstrangeira
    protected PlanoDuracaoVO planoDuracao;
    @ChaveEstrangeira
    protected ContratoDuracaoVO contratoDuracao;
    @ChaveEstrangeira
    protected PlanoHorarioVO planoHorario;
    @ChaveEstrangeira
    protected ContratoHorarioVO contratoHorario;
    @ChaveEstrangeira
    protected ConvenioDescontoVO convenioDesconto;
    @ChaveEstrangeira
    protected ConvenioDescontoConfiguracaoVO convenioDescontoConfiguracaoVO = new ConvenioDescontoConfiguracaoVO();
    @ChaveEstrangeira
    protected PlanoCondicaoPagamentoVO planoCondicaoPagamento;
    @ChaveEstrangeira
    protected ContratoCondicaoPagamentoVO contratoCondicaoPagamento;
    @ChaveEstrangeira
    protected ColaboradorVO responsavelLiberacaoCondicaoPagamento;
    @ChaveEstrangeira
    protected UsuarioVO responsavelContrato;
    @ChaveEstrangeira
    protected UsuarioVO responsavelAutorizacaoDesconto;
    protected UsuarioVO responsavelAutorizacaoDescontoConvenio;
    protected Boolean pagarComBoleto;
    protected Boolean dividirProdutosNasParcelas;
    private int diaVencimentoProrata;
    protected Boolean renovarContrato;
    protected Boolean apresentarBotaoRenovarContrato;
    @ChaveEstrangeira
    protected ProdutoVO desconto;
    protected String tipoDesconto;
    protected Double valorDescontoEspecifico;
    protected Double valorDescontoPorcentagem;
    protected Double valorDesconto;
    // valor temporaria para guarda o valor do desconto lançado a mao
    protected Double valorTemporarioDescontoPorcentagem;
    // valor temporario para guardar o valor do desconto por renovacao antecipada
    private double valorTemporarioDescontoAntecipado;
    private double valorTemporarioDescontoAntecipadoTotal;
    protected Double somaProduto;
    private Double somaAdesao;
    protected Boolean selecionarTodasParcela;
    protected Date vigenciaAteAjustada;
    protected Date dataLancamento;
    // Se ele é Matricula, Renovação, Rematricula;
    protected String situacaoContrato;
    // Se matricula;
    protected Date dataMatricula;
    // campo responsavel por informa se um contrato do tipo matricula foi
    // renovado.
    protected Integer contratoResponsavelRenovacaoMatricula;
    // campo responsavel por informa se um contrato do tipo rematricula foi
    // renovado.
    protected Integer contratoResponsavelRematriculaMatricula;
    // Se é renovacao;
    protected Date dataPrevistaRenovar;
    protected Date dataRenovarRealizada;
    // --- Antecipada, No dia, Atrasada Vencida e Atrasada Desistente (Após o
    // vencimento e dentro da tolerância);
    protected String situacaoRenovacao;
    protected Integer contratoBaseadoRenovacao;
    // Se é rematricula;
    protected Date dataPrevistaRematricula;
    protected Date dataRematriculaRealizada;
    // --- Aluno Bem Recente(Até 20 dias após o periodo de renovacao), Aluno
    // Recente(Menos de 21 dias a 4 meses
    // --- do vencimento do ultimo contrato), Aluno Antigo (mais de 4 meses
    // fora)
    protected String situacaoRematricula;
    protected Integer contratoBaseadoRematricula;
    @NaoControlarLogAlteracao
    protected ConfiguracaoSistemaVO configuracaoSistema;
    protected Boolean rematricularContrato;
    // protected Boolean apresentarValorDescontoEspecifico;
    // protected Boolean apresentarValorDescontoPorcentagem;
    @ChaveEstrangeira
    protected ContratoTextoPadraoVO contratoTextoPadrao;
    // --- atributo usado para validar qual e a situacao do contrato quando a
    // empresa permite contratos concomitantes;
    protected String situacaoClienteContratoComitantes;
    // Atributo usado quando um cliente term um contrato inativo e nao deseja
    // mais continuar com ele e realizar um novo contrato
    protected Boolean naoPermitirRenovacaoRematriculaDeContratoAnteriores;
    // Atributo usado para realizar o estorno do contrato caso tenha algum
    // recibo
    @Lista
    protected List<EstornoReciboVO> listaEstornoRecibo;
    @Lista
    protected List<TransacaoVO> listaEstornoTransacoes;
    @Lista
    protected List<RemessaItemVO> listaItensRemessa;
    // atributo usado na emissao do relatorio de renovacao
    private int duracaoRenovacao;
    private Date dataPrimeiraParcela;
    private ClienteVO cliente = new ClienteVO();
    @NaoControlarLogAlteracao
    private ClienteVO clienteDependente;
    @NaoControlarLogAlteracao
    private boolean indicadorDependente = false;
    private Date dataAlteracaoManual;
    private UsuarioVO responsavelDataBase;
    private ContratoRecorrenciaVO contratoRecorrenciaVO;
    private Boolean regimeRecorrencia = false;
    private int diaVencimentoCartaoRecorrencia = 0;
    private TipoContratoEnum contratoAgendadoEspontaneo;
    private boolean forcarDatasParcelasDiferentes = false;
    @NaoControlarLogAlteracao
    private AgendaVO agendamentoOrigem;
    @NaoControlarLogAlteracao
    boolean precisaEstornarTransacoes = true;
    @NaoControlarLogAlteracao
    boolean importacao = false;
    private Boolean tentativaDuplicacao = false;
    @NaoControlarLogAlteracao
    private Boolean gerarParcelaParaProdutos = false;
    @NaoControlarLogAlteracao
    private Integer nrParcelasAdesao;
    @NaoControlarLogAlteracao
    private Double valorProRata;
    private Double valorArredondamento;
    @NaoControlarLogAlteracao
    private List<PlanoExcecaoVO> excecoes = new ArrayList<PlanoExcecaoVO>();

    @NaoControlarLogAlteracao
    private Double valorMatricula = 0.0;
    @NaoControlarLogAlteracao
    private MovProdutoVO produtoPlano;
    @NaoControlarLogAlteracao
    private Double valorRematricula = 0.0;
    @NaoControlarLogAlteracao
    private String descricaoModalidade;
    @NaoControlarLogAlteracao
    private String nomeModalidadesConcatenado;
    private ContratoVO contratoResponsavelRenovacaoMatriculaVO;
    private Boolean contratoPossuiRemessaBoleto;
    private Boolean renovavelAutomaticamente;
    protected Double valorConvenioDesconto = 0.0;
    @NaoControlarLogAlteracao
    protected Double valorDescontoManual = 0.0;

    private Integer quantidadeMaximaFrequencia = 0;

    private boolean vendaCreditoTreino = false;
    private boolean vendaCreditoSessao = false;
    private List<ControleCreditoTreinoVO>listaControleCreditoTreino;

    // atributo transient
    private ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO;

    //data que será utilizada no ContratoControle calcularDescontoAntecipado
    private Date dataPrevistaRenovarAntecipado;
    private double somaAnuidade;
    private ContratoVO contratoOrigemRenovacao;
    private Integer diasRestanteContratoOrigemRenovacao= 0;
    private Integer saldoCreditoContratoOrigemRenovacao= 0;
    private String informacaoRenovacaoCreditoTreino;
    private Date vigenciaTurmaCreditoTreinoAte; // atributo transient.
    private Integer diferencaQtdeCreditoTreinoManutencaoModalidade; // atributo transient.
    private Integer qtdeVezesSemanaAposManutencaoModalidade; // atributo transient.

    @NaoControlarLogAlteracao
    private ContratoOperacaoVO cancelamentoContratoVO;
    @NaoControlarLogAlteracao
    private CancelamentoContratoVO cancelamentoContratoVOOrignal;
    @NaoControlarLogAlteracao
    private Integer posTlTabelaPreto = null;
    @NaoControlarLogAlteracao
    private Double totalFinalProdutos = 0.0;
    private String numeroCupomDesconto;
    private Double premioProdutosCupomDesconto; // atributo transient
    @NaoControlarLogAlteracao
    private Integer idCampanhaCupomDesconto;// atributo transient
    @NaoControlarLogAlteracao
    private double descontoParcelaUsandoCupomDesconto = 0;// atributo transient
    private String chave;// atributo transient --
    private boolean gerarAdesao = true;
    private boolean gerarAnuidade = true;
    private boolean possuiRemessaBoleto = false;
    private boolean contratoEhDeTurma = false;
    private boolean excluirNFSe = false; // atributo transient
    private boolean mostrarMsgExcluirNFse = false; // atributo transient
    @NaoControlarLogAlteracao
    private Integer nrParcelasPagamento = 0;
    @NaoControlarLogAlteracao
    public static final String KEY_NEGOCIACAO_EM_ANDAMENTO = "NEGOCIACAO_EM_ANDAMENTO";
    @NaoControlarLogAlteracao
    private String opcaoSelecionadaRenovacaoPlanoCredito="";
    @NaoControlarLogAlteracao
    private Date assinadoEm;
    private boolean calcularAnuidade = true;
    private Boolean permiteRenovacaoAutomatica = Boolean.TRUE;
     @NaoControlarLogAlteracao
    private boolean cobrarMatriculaSeparada = false;
    @NaoControlarLogAlteracao
    private Integer nrVezesParcelarMatricula = 1;
    @NaoControlarLogAlteracao
    private boolean cobrarProdutoSeparado = false;
    @NaoControlarLogAlteracao
    private Integer nrVezesParcelarProduto = 1;

    @NaoControlarLogAlteracao
    private double totalProdutosCobrarSeparado = 0;

    @NaoControlarLogAlteracao
    private boolean crossfit = false;
    @NaoControlarLogAlteracao
    private String situacaoSubordinada;
    private OrigemSistemaEnum origemSistema;

    @NaoControlarLogAlteracao
    private String mensagemSemGrupoDescontoAplicavel = "";
    @NaoControlarLogAlteracao
    private String nomeConvenioDesconto;
    @NaoControlarLogAlteracao
    private Double valorContratoConvenioDesconto;
    @NaoControlarLogAlteracao
    private Double percentualConvenioDesconto;

    private GrupoVO grupo = new GrupoVO();
    @NaoControlarLogAlteracao
    private String descricaoRegraCancelamento;// atributo transient
     @NaoControlarLogAlteracao
    private Integer idExterno;
    @NaoControlarLogAlteracao
    private Double valorMensalExcecao = 0.0; //usado para evitar problemas com desconto na duração
    private Double somaDescontoAdesao;
    @NaoControlarLogAlteracao
    private HistoricoTurmasContratoTO historicoTurmasContrato;
    private Integer primeiroContratoBaseadoRenovacao = 0;
    @NaoControlarLogAlteracao
    private boolean ehAditivo; // Atributo transient
    @NaoControlarLogAlteracao
    private Integer aditivo; // Atributo transient

    /**
     * Se TRUE, as parcelas geradas durante a inclusão do contrato
     * serão exatamente como a lista de parcelas definidas no atributo ContratoVO.movParcelaVOs
     */
    @NaoControlarLogAlteracao
    private boolean gerarParcelasAPartirDeMovParcelaVOs = false;

    /**
     * Construtor padrão da classe <code>Contrato</code>. Cria uma nova
     * instância desta entidade, inicializando automaticamente seus atributos
     * (Classe VO).
     */

    /** Atributo que guarda o multiplicador maior da campanha para mostrar no hint na tela de negociação de contrato*/
    private int planoMaiorMultiplicadorCampanhaAtiva = 0;
    private int planoDuracaoMaiorMultiplicadorCampanhaAtiva = 0;

    private Integer pontuacaoPlano = 0;
    private Integer pontuacaoPlanoDuracao = 0;
    private Integer pontuacaoProduto = 0;


    private String descricaoParaHistoricoPontuacaoPlano = "";
    private Boolean vendaVitio = false;

    @NaoControlarLogAlteracao
    private Double valorDescontoMatricula = 0.0;
    @NaoControlarLogAlteracao
    private Double valorDescontoRematricula = 0.0;
    @NaoControlarLogAlteracao
    private Double valorCheioMatricula = 0.0;
    @NaoControlarLogAlteracao
    private Double valorCheioRematricula = 0.0;
    @NaoControlarLogAlteracao
    private Double valorDescontoAnuidadeRecorrencia = 0.0;
    @NaoControlarLogAlteracao
    private Double valorFinalAnuidadeRecorrencia = 0.0;
    @NaoControlarLogAlteracao
    private Date dataAnuidade;
    @NaoControlarLogAlteracao
    private Integer contratoOrigemTransferencia;
    @NaoControlarLogAlteracao
    private List<ParcelasEditarNegociacaoNovo> listParcelasEditadas = new ArrayList<ParcelasEditarNegociacaoNovo>();
    @NaoControlarLogAlteracao
    private double valorPrimeiraParcelaEdicao = 0.0;
    @NaoControlarLogAlteracao
    private boolean todasAsParcelasEstaoPagas = true;
    @NaoControlarLogAlteracao
    private List movParcelasVOValidarPagamento;
    private UsuarioVO responsavelAutorizacaoBolsa;
    private Double descontoExtraParcelasValorDiferente = 0.0;
    private EventoVO eventoVO;

    /**
     * Data base da segunda parcela, normalmente ele terá o valor da dataPrimeiraParcela
     * mas em alguns casos ela pode mudar para que a data a partir da 2º parcela siga ela
     */
    private Date dataBaseSegundaParcela;

    private Date dataEnvioContrato;

    private String emailRecebimento;
    @ChaveEstrangeira
    private PessoaVO pessoaOriginal;
    // atributo transient
    @NaoControlarLogAlteracao
    private VendasConfigVO vendasConfigVO;

    private String ipassinaturacontrato;

    private Double valorBaseNegociado;

    @NaoControlarLogAlteracao
    protected UsuarioVO responsavelAutorizacaoDataPrimeiraParcela;

    private String xnumpro;

    private boolean incluindoTurma = false;
    private Date dataSincronizacaoIntegracaoFoguete;

    private boolean isRenovacao = false;

    private boolean isRematricula = false;

    private boolean alterouDataInicioContrato = false;
    private Boolean alterouTipoDoContrato;

    @NaoControlarLogAlteracao
    private Boolean agregador;
    @NaoControlarLogAlteracao
    private Date dataUltimoAcessoAgregador;
    @NaoControlarLogAlteracao
    private String tipoAgregador;
    @NaoControlarLogAlteracao
    private Integer qtdCheckInAgregador;

    public Date getDataAssinaturaContrato() {
        return dataAssinaturaContrato;
    }

    public void setDataAssinaturaContrato(Date dataAssinaturaContrato) {
        this.dataAssinaturaContrato = dataAssinaturaContrato;
    }

    private Date dataAssinaturaContrato;

    public Date getDataEnvioContrato() {
        return dataEnvioContrato;
    }

    public void setDataEnvioContrato(Date dataEnvioContrato) {
        this.dataEnvioContrato = dataEnvioContrato;
    }

    public String getEmailRecebimento() {
        return emailRecebimento;
    }

    public void setEmailRecebimento(String emailRecebimento) {
        this.emailRecebimento = emailRecebimento;
    }


    public Boolean getVendaVitio() {
        return vendaVitio;
    }

    public void setVendaVitio(Boolean vendaVitio) {
        this.vendaVitio = vendaVitio;
    }

    public ContratoVO() {
        super();
        inicializarDados();
    }

    public int getPlanoMaiorMultiplicadorCampanhaAtiva() {
        return planoMaiorMultiplicadorCampanhaAtiva;
    }

    public void setPlanoMaiorMultiplicadorCampanhaAtiva(int planoMaiorMultiplicadorCampanhaAtiva) {
        this.planoMaiorMultiplicadorCampanhaAtiva = planoMaiorMultiplicadorCampanhaAtiva;
    }

    public int getPlanoDuracaoMaiorMultiplicadorCampanhaAtiva() {
        return planoDuracaoMaiorMultiplicadorCampanhaAtiva;
    }

    public void setPlanoDuracaoMaiorMultiplicadorCampanhaAtiva(int planoDuracaoMaiorMultiplicadorCampanhaAtiva) {
        this.planoDuracaoMaiorMultiplicadorCampanhaAtiva = planoDuracaoMaiorMultiplicadorCampanhaAtiva;
    }

    public String getMatricula_Apresentar() {
        return getCliente().getMatricula();
    }

    public String getNome_Apresentar() {
        return getPessoa().getNome();
    }

    public Integer getDuracao_Apresentar() {
        return getContratoDuracao().getNumeroMeses();
    }

    public String getNascimento_Apresentar() {
        return getPessoa().getDataNasc_Apresentar();
    }

    public String getPlanoDescricao_Apresentar() { return getPlano().getDescricao_Apresentar(); }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ContratoVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @exception ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */

    public static void validarDados(ContratoVO obj) throws Exception {
        validarDados(obj, null);
    }

    public static void validarDados(ContratoVO obj, ContratoVO contrato) throws Exception {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }

        if ((obj.getEmpresa() == null) || (obj.getEmpresa().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo EMPRESA deve ser informado.");
        }
        if (!obj.isIncluindoTurma() && !obj.isContratoRenovacao()
                && !obj.getImportacao()
                && obj.getVigenciaDe().compareTo(Uteis.getDataComHoraZerada(negocio.comuns.utilitarias.Calendario.hoje())) < 0) {
            throw new ConsistirException("A Data de Início do Contrato não pode ser retroativa.");
        }
        if ((obj.getPlano() == null) || (obj.getPlano().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo PLANO deve ser informado.");
        }
        if (obj.getPlano().getBolsa()) {
            obj.setBolsa(true);
        } else {
            obj.setBolsa(false);
        }
        if (obj.getPlano().isVendaCreditoTreino()){
            if (obj.getContratoDuracao().getContratoDuracaoCreditoTreinoVO() == null){
                throw new ConsistirException("O campo DURAÇÃO e QUANTIDADE CRÉDITO (Contrato) devem ser informados.");
            }
        }


        if (obj.getContratoModalidadeVOs().isEmpty()) {
            throw new ConsistirException("O campo MODALIDADE deve ser informado.");
        } else {
            int passo = 0;
            for (ContratoModalidadeVO contratoModalidade : obj.getContratoModalidadeVOs()) {
                if (contratoModalidade.getModalidade().getModalidadeEscolhida()) {
                    passo = 1;
                }
            }
            if (passo == 0) {
                throw new ConsistirException("O campo MODALIDADE deve ser informado.");
            }
        }
        int totalHorarioTurma = 0;
        Iterator i = obj.getContratoModalidadeVOs().iterator();
        while (i.hasNext()) {
            int cont = 0;
            ContratoModalidadeVO contratoModalidade = (ContratoModalidadeVO) i.next();
            for (Object objModTurma: contratoModalidade.getContratoModalidadeTurmaVOs()){
                ContratoModalidadeTurmaVO contratoModalidadeTurmaVO = (ContratoModalidadeTurmaVO)objModTurma;
                totalHorarioTurma += contratoModalidadeTurmaVO.getContratoModalidadeHorarioTurmaVOs().size();
            }
            if (contratoModalidade.getPlanoVezesSemanaVO().getNrVezes().intValue() == 0 && contratoModalidade.getModalidade().getModalidadeEscolhida()) {
                throw new ConsistirException("O campo  VEZES SEMANA da Modalidade " + contratoModalidade.getModalidade().getNome() + " deve ser informado.");
            }
            if (contratoModalidade.getModalidade().getUtilizarTurma() && contratoModalidade.getModalidade().getModalidadeEscolhida() && contratoModalidade.getContratoModalidadeTurmaVOs().isEmpty()) {
                throw new ConsistirException("O campo TURMA da Modalidade " + contratoModalidade.getModalidade().getNome() + " deve ser informado.");
            }
            Iterator j = contratoModalidade.getContratoModalidadeTurmaVOs().iterator();
            while (j.hasNext()) {
                ContratoModalidadeTurmaVO contratoModalidadeTurma = (ContratoModalidadeTurmaVO) j.next();
                if (contratoModalidadeTurma.getTurma().getTurmaEscolhida()) {
                    Iterator k = contratoModalidadeTurma.getContratoModalidadeHorarioTurmaVOs().iterator();
                    while (k.hasNext()) {
                        ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurma = (ContratoModalidadeHorarioTurmaVO) k.next();
                        if (contratoModalidadeHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                            cont++;
                        }
                    }
                }
            }
            if (contratoModalidade.getModalidade().getUtilizarTurma()
                    && (!obj.getPlano().isVendaCreditoTreino())
                    && contratoModalidade.getModalidade().getModalidadeEscolhida()
                    && contratoModalidade.getPlanoVezesSemanaVO().getNrVezes() != cont) {
                throw new ConsistirException("A Quantidade de Horário escolhido para Modalidade " + contratoModalidade.getModalidade().getNome() + " não é compatível para quantidade de vezes semana que ela pode ser realizada.");
            }
        }
        if ((obj.getPlanoDuracao() == null) || (obj.getPlanoDuracao().getCodigo() == 0)) {
            throw new ConsistirException("O campo DURAÇÃO deve ser informado.");
        }

        if (obj.getPlano().isVendaCreditoTreino() && !obj.getPlano().isCreditoSessao()){
            if ((obj.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getNumeroVezesSemana() != totalHorarioTurma ) &&
               (obj.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA)){
                throw new ConsistirException("O Número de vezes por semana para a(s) Modalidade(s) selecionadas não bate com o número de vezes do plano que é de " +obj.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getNumeroVezesSemana()  +" x semana");
            }
        }


        if(contrato != null){

            boolean existeMesmoHorario = false;
            for(PlanoHorarioVO planoHorario : obj.getPlano().getPlanoHorarioVOs()){
                if(planoHorario.getHorario().getCodigo().equals(contrato.getContratoHorario().getHorario().getCodigo())){
                    existeMesmoHorario = true;
                    obj.setPlanoHorario(planoHorario);
                    break;
                }
            }

            if(!existeMesmoHorario){
                throw new ConsistirException("O plano selecionado não tem o mesmo horario disponivel do plano atual do aluno.");
            }
        } else  if (!obj.getPlano().getPlanoHorarioVOs().isEmpty()) {
            // se estiver sido cadastrado pelo menos um horário no cadastro de Plano,
            //  então é necessário informar um horário do plano.
            if ((obj.getPlanoHorario() == null) || (obj.getPlanoHorario().getCodigo() == 0)) {
                throw new ConsistirException("O campo HORÁRIO deve ser informado.");
            }
        } else if (obj.getPlano().getPlanoHorarioVOs().isEmpty()) {
            throw new ConsistirException("Não existe horário ativo cadastrado no plano selecionado. Ajuste o cadastro deste plano para realizar a negocicação.");
        }

        if ((obj.getDesconto() != null) && (!UteisValidacao.emptyNumber(obj.getDesconto().getCodigo()))) {
            if (obj.getTipoDesconto().equals("")) {
                throw new ConsistirException("O campo TIPO DESCONTO deve ser informado.");
            }
            if (obj.getTipoDesconto().equals("VA") && obj.getValorDescontoEspecifico() == 0.0) {
                throw new ConsistirException("O campo VALOR DESCONTO ESPECÍFICO deve ser informado.");
            }
            if (obj.getTipoDesconto().equals("PE") && obj.getValorDescontoPorcentagem() == 0.0) {
                throw new ConsistirException("O campo VALOR DESCONTO PORCENTAGEM deve ser informado.");
            }
        }
        if ((obj.getPlanoCondicaoPagamento().getCondicaoPagamento() == null) || (obj.getPlanoCondicaoPagamento().getCodigo() == 0)) {
            throw new ConsistirException("O campo CONDIÇÃO DE PAGAMENTO (Contrato) deve ser informado.");
        }
//        if (!UtilReflection.objetoMaiorQueZero(obj, "getConsultor().getCodigo()")){
//            throw new ConsistirException("O campo CONSULTOR (Contrato) deve ser informado.");
//        }

    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ContratoVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @exception ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDadosCondicaoPagamento(ContratoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getPlanoCondicaoPagamento().getCondicaoPagamento() == null) || (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getCodigo() == 0)) {
            throw new ConsistirException("O campo Condição de Pagamento deve ser informado.");
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        setSituacao(getSituacao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public final void inicializarDados() {
    }

    public void adicionarModalidadeContratoModalidade(PlanoModalidadeVO obj, ContratoModalidadeVO contratoModalidadeVO) {
        int passo = 0;
        for (ContratoModalidadeVO objExistente : getContratoModalidadeVOs()) {
            if (obj.getModalidade().getCodigo().equals(objExistente.getModalidade().getCodigo().intValue())) {
                passo = 1;
            }
        }
        if (passo == 0) {
            contratoModalidadeVO.setModalidade(obj.getModalidade());
            getContratoModalidadeVOs().add(contratoModalidadeVO);
        }
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ContratoModalidadeVO</code> ao List
     * <code>contratoModalidadeVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ContratoModalidade</code> - getModalidade().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ContratoModalidadeVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjContratoModalidadeVOs(ContratoModalidadeVO obj) throws Exception {
        ContratoModalidadeVO.validarDados(obj);
        int index = 0;
        for (ContratoModalidadeVO objExistente : getContratoModalidadeVOs()) {
            if (objExistente.getModalidade().getCodigo().equals(obj.getModalidade().getCodigo())) {
                getContratoModalidadeVOs().set(index, obj);
                return;
            }
            index++;
        }
        getContratoModalidadeVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>ContratoModalidadeVO</code> no List
     * <code>contratoModalidadeVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ContratoModalidade</code> - getModalidade().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param modalidade Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjContratoModalidadeVOs(Integer modalidade) throws Exception {
        int index = 0;
        for (ContratoModalidadeVO objExistente : getContratoModalidadeVOs()) {
            if (objExistente.getModalidade().getCodigo().equals(modalidade)) {
                getContratoModalidadeVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>ContratoModalidadeVO</code> no List
     * <code>contratoModalidadeVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ContratoModalidade</code> - getModalidade().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param modalidade Parâmetro para localizar o objeto do List.
     */
    public ContratoModalidadeVO consultarObjContratoModalidadeVO(Integer modalidade) throws Exception {
        for (ContratoModalidadeVO objExistente : getContratoModalidadeVOs()) {
            if (objExistente.getModalidade().getCodigo().equals(modalidade)) {
                return objExistente;
            }
        }
        return null;
    }

    public void adiconarModalidadeContratoComposicao(PlanoComposicaoVO obj, ContratoComposicaoVO contratoComposicaoVO) {
        int passo = 0;
        for (Object o : getContratoComposicaoVOs()) {
            ContratoComposicaoVO objExistente = (ContratoComposicaoVO) o;
            if (obj.getComposicao().getCodigo().equals(objExistente.getComposicaoVO().getCodigo())) {
                passo = 1;
            }
        }
        if (passo == 0) {
            contratoComposicaoVO.setComposicaoVO(obj.getComposicao());
            getContratoComposicaoVOs().add(contratoComposicaoVO);
        }
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ContratoComposicaoVO</code> ao List
     * <code>ContratoComposicaoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ContratoComposicao</code> - getModalidade().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ContratoComposicaoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjContratoComposicaoVOs(ContratoComposicaoVO obj) throws Exception {
        ContratoComposicaoVO.validarDados(obj);
        int index = 0;
        for (Object o : getContratoComposicaoVOs()) {
            ContratoComposicaoVO objExistente = (ContratoComposicaoVO) o;
            if (objExistente.getComposicaoVO().getCodigo().equals(obj.getComposicaoVO().getCodigo().intValue())) {
                getContratoComposicaoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getContratoComposicaoVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>ContratoComposicaoVO</code> no List
     * <code>ContratoComposicaoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ContratoComposicao</code> - getModalidade().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param composiao Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjContratoComposicaoVOs(Integer composiao) throws Exception {
        int index = 0;
        for (Object o : getContratoComposicaoVOs()) {
            ContratoComposicaoVO objExistente = (ContratoComposicaoVO) o;
            if (objExistente.getComposicaoVO().getCodigo().equals(composiao)) {
                getContratoComposicaoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>ContratoComposicaoVO</code> no List
     * <code>ContratoComposicaoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ContratoComposicao</code> - getModalidade().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param composicao Parâmetro para localizar o objeto do List.
     */
    public ContratoComposicaoVO consultarObjContratoComposicaoVO(Integer composicao) throws Exception {
        for (Object o : getContratoComposicaoVOs()) {
            ContratoComposicaoVO objExistente = (ContratoComposicaoVO) o;
            if (objExistente.getComposicaoVO().getCodigo().equals(composicao)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ContratoModalidadeVO</code> ao List
     * <code>contratoModalidadeVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ContratoModalidade</code> - getModalidade().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ContratoModalidadeVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjContratoPlanoProdutoSugeridoVOs(ContratoPlanoProdutoSugeridoVO obj) throws Exception {
        ContratoPlanoProdutoSugeridoVO.validarDados(obj);
        int index = 0;
        for (Object o : getContratoPlanoProdutoSugeridoVOs()) {
            ContratoPlanoProdutoSugeridoVO objExistente = (ContratoPlanoProdutoSugeridoVO) o;
            if (objExistente.getPlanoProdutoSugerido().getProduto().getCodigo().equals(obj.getPlanoProdutoSugerido().getProduto().getCodigo())) {
                getContratoPlanoProdutoSugeridoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getContratoPlanoProdutoSugeridoVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>ContratoModalidadeVO</code> no List
     * <code>contratoModalidadeVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ContratoModalidade</code> - getModalidade().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param produto Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjContratoPlanoProdutoSugeridoVOs(Integer produto) {
        int index = 0;
        for (Object o : getContratoPlanoProdutoSugeridoVOs()) {
            ContratoPlanoProdutoSugeridoVO objExistente = (ContratoPlanoProdutoSugeridoVO) o;
            if (objExistente.getPlanoProdutoSugerido().getProduto().getCodigo().equals(produto)) {
                getContratoPlanoProdutoSugeridoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>ContratoModalidadeVO</code> no List
     * <code>contratoModalidadeVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ContratoModalidade</code> - getModalidade().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param produto Parâmetro para localizar o objeto do List.
     */
    public ContratoPlanoProdutoSugeridoVO consultarObjContratoPlanoProdutoSugeridoVO(Integer produto) throws Exception {
        for (Object o : getContratoPlanoProdutoSugeridoVOs()) {
            ContratoPlanoProdutoSugeridoVO objExistente = (ContratoPlanoProdutoSugeridoVO) o;
            if (objExistente.getPlanoProdutoSugerido().getProduto().getCodigo().equals(produto)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>HistoricoContratoVO</code> ao List
     * <code>historicoContratoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>HistoricoContrato</code> - getDataRegistro() - como
     * identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>HistoricoContratoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjHistoricoContratoVOs(HistoricoContratoVO obj) throws Exception {
        HistoricoContratoVO.validarDados(obj);
        int index = 0;
        for (Object o : getHistoricoContratoVOs()) {
            HistoricoContratoVO objExistente = (HistoricoContratoVO) o;
            if ((objExistente.getDataInicioSituacao().equals(obj.getDataInicioSituacao())) || (objExistente.getDataFinalSituacao().equals(obj.getDataFinalSituacao()))) {
                getHistoricoContratoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getHistoricoContratoVOs().add(obj);
    }

    public void adicionarObjPeriodoAcessoClienteVOs(PeriodoAcessoClienteVO obj) throws Exception {
        PeriodoAcessoClienteVO.validarDados(obj);
        int index = 0;
        for (Object o : getPeriodoAcessoClienteVOs()) {
            PeriodoAcessoClienteVO objExistente = (PeriodoAcessoClienteVO) o;
            if ((objExistente.getDataInicioAcesso().equals(obj.getDataInicioAcesso())) || (objExistente.getDataFinalAcesso().equals(obj.getDataFinalAcesso()))) {
                getPeriodoAcessoClienteVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPeriodoAcessoClienteVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>HistoricoContratoVO</code> no List
     * <code>historicoContratoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>HistoricoContrato</code> - getDataRegistro() - como
     * identificador (key) do objeto no List.
     *
     * @param dataRegistro Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjHistoricoContratoVOs(Date dataRegistro) throws Exception {
        int index = 0;
        for (Object o : getHistoricoContratoVOs()) {
            HistoricoContratoVO objExistente = (HistoricoContratoVO) o;
            if (objExistente.getDataRegistro().equals(dataRegistro)) {
                getHistoricoContratoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>HistoricoContratoVO</code> no List
     * <code>historicoContratoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>HistoricoContrato</code> - getDataRegistro() - como
     * identificador (key) do objeto no List.
     *
     * @param dataRegistro Parâmetro para localizar o objeto do List.
     */
    public HistoricoContratoVO consultarObjHistoricoContratoVO(Date dataRegistro) throws Exception {
        for (Object o : getHistoricoContratoVOs()) {
            HistoricoContratoVO objExistente = (HistoricoContratoVO) o;
            if (objExistente.getDataRegistro().equals(dataRegistro)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>HistoricoContratoVO</code> ao List
     * <code>historicoContratoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>HistoricoContrato</code> - getDataRegistro() - como
     * identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>HistoricoContratoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjMovProdutoVOs(MovProdutoVO obj, boolean produtoSugerido) throws Exception {
        MovProdutoVO.validarDados(obj);
        int index = 0;
        for (Object o : getMovProdutoVOs()) {
            MovProdutoVO objExistente = (MovProdutoVO) o;
            if (objExistente.getDescricao().equals(obj.getDescricao()) && objExistente.getProduto().getCodigo().equals(obj.getProduto().getCodigo())) {
                if (produtoSugerido) {
                    throw new ConsistirException("Produto  " + obj.getDescricao() + " é sugerido e selecionado na modalidade e no plano. Ele deve ser selecionado apenas uma vez, na modalidade ou no plano");
                }
                getMovProdutoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getMovProdutoVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>MovProdutoVO</code> no List <code>MovProdutoVOs</code>. Utiliza o
     * atributo padrão de consulta da classe <code>MovProduto</code> -
     * getDataRegistro() - como identificador (key) do objeto no List.
     *
     * @param codigoProduto Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjMovProdutoVOs(Integer codigoProduto) throws Exception {
        int index = 0;
        for (Object o : getMovProdutoVOs()) {
            MovProdutoVO objExistente = (MovProdutoVO) o;
            if (objExistente.getProduto().getCodigo().equals(codigoProduto.intValue())) {
                getMovProdutoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>MovProdutoVO</code> no List <code>MovProdutoVOs</code>. Utiliza o
     * atributo padrão de consulta da classe <code>MovProduto</code> -
     * getDataRegistro() - como identificador (key) do objeto no List.
     *
     * @param codigoContrato Parâmetro para localizar o objeto do List.
     */
    public MovProdutoVO consultarObjMovProdutoVO(Integer codigoContrato) throws Exception {
        for (Object o : getMovProdutoVOs()) {
            MovProdutoVO objExistente = (MovProdutoVO) o;
            if (objExistente.getContrato().getCodigo().equals(codigoContrato)) {
                return objExistente;
            }
        }
        return null;
    }

    public void inicializarDadosContrato() {
        inicializarDadosContratoDuracao();
        inicializarDadosContratoHorario();
        inicializarDadosContratoCondicaoPagamento();
        inicializarDadosContratoTextoPadrao();
    }

    public void inicializarDadosContratoDuracao() {
        ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = getContratoDuracao().getContratoDuracaoCreditoTreinoVO();
        setContratoDuracao(new ContratoDuracaoVO());
        getContratoDuracao().setContrato(codigo);
        getContratoDuracao().setDuracaoEscolhida(true);
        getContratoDuracao().setNrMaximoParcelasCondPagamento(getPlanoDuracao().getNrMaximoParcelasCondPagamento());
        getContratoDuracao().setCarencia(getPlanoDuracao().getCarencia());
        getContratoDuracao().setNumeroMeses(getPlanoDuracao().getNumeroMeses());
        getContratoDuracao().setPercentualDesconto(getPlanoDuracao().getPercentualDesconto());
        getContratoDuracao().setTipoOperacao(getPlanoDuracao().getTipoOperacao());
        getContratoDuracao().setTipoValor(getPlanoDuracao().getTipoValor());
        getContratoDuracao().setValorDesejado(getPlanoDuracao().getValorDesejado());
        getContratoDuracao().setValorDesejadoMensal(getPlanoDuracao().getValorDesejadoMensal());
        getContratoDuracao().setValorDesejadoParcela(getPlanoDuracao().getValorDesejadoParcela());
        getContratoDuracao().setValorEspecifico(getPlanoDuracao().getValorEspecifico());
        if (getSaldoCreditoContratoOrigemRenovacao() >0){
            getContratoDuracao().setQuantidadeDiasExtra(getPlanoDuracao().getQuantidadeDiasExtra() + getDiasRestanteContratoOrigemRenovacao());
        }else{
            getContratoDuracao().setQuantidadeDiasExtra(getPlanoDuracao().getQuantidadeDiasExtra());
        }

        if (contratoDuracaoCreditoTreinoVO != null){
            contratoDuracaoCreditoTreinoVO.setContratoDuracaoVO(getContratoDuracao());
        }
        getContratoDuracao().setContratoDuracaoCreditoTreinoVO(contratoDuracaoCreditoTreinoVO);
    }

    public void inicializarDadosContratoHorario() {
        setContratoHorario(new ContratoHorarioVO());
        getContratoHorario().setContrato(codigo);
        getContratoHorario().setHorario(getPlanoHorario().getHorario());
        getContratoHorario().setPercentualDesconto(getPlanoHorario().getPercentualDesconto());
        getContratoHorario().setTipoOperacao(getPlanoHorario().getTipoOperacao());
        getContratoHorario().setTipoValor(getPlanoHorario().getTipoValor());
        getContratoHorario().setValorEspecifico(getPlanoHorario().getValorEspecifico());
    }

    public void inicializarDadosContratoCondicaoPagamento() {
        setContratoCondicaoPagamento(new ContratoCondicaoPagamentoVO());
        getContratoCondicaoPagamento().setContrato(codigo);
        getContratoCondicaoPagamento().setCondicaoPagamento(getPlanoCondicaoPagamento().getCondicaoPagamento());
        getContratoCondicaoPagamento().setPercentualDesconto(getPlanoCondicaoPagamento().getPercentualDesconto());
        getContratoCondicaoPagamento().setTipoOperacao(getPlanoCondicaoPagamento().getTipoOperacao());
        getContratoCondicaoPagamento().setTipoValor(getPlanoCondicaoPagamento().getTipoValor());
        getContratoCondicaoPagamento().setValorEspecifico(getPlanoCondicaoPagamento().getValorEspecifico());
    }

    public void inicializarDadosContratoTextoPadrao() {
        setContratoTextoPadrao(new ContratoTextoPadraoVO());
        getContratoTextoPadrao().setContrato(codigo);
        getContratoTextoPadrao().setPlanoTextoPadrao(getPlano().getPlanoTextoPadrao());
    }

    public void inicializarDadosPlanoDuracao() {
        setPlanoDuracao(new PlanoDuracaoVO());
        getPlanoDuracao().setDuracaoEscolhida(true);
        getPlanoDuracao().setCodigo(getContratoDuracao().getCodigo());
        getPlanoDuracao().setNrMaximoParcelasCondPagamento(getContratoDuracao().getNrMaximoParcelasCondPagamento());
        getPlanoDuracao().setNumeroMeses(getContratoDuracao().getNumeroMeses());
        getPlanoDuracao().setCarencia(getContratoDuracao().getCarencia());
        getPlanoDuracao().setPercentualDesconto(getContratoDuracao().getPercentualDesconto());
        getPlanoDuracao().setTipoOperacao(getContratoDuracao().getTipoOperacao());
        getPlanoDuracao().setTipoValor(getContratoDuracao().getTipoValor());
        getPlanoDuracao().setValorDesejado(getContratoDuracao().getValorDesejado());
        getPlanoDuracao().setValorDesejadoMensal(getContratoDuracao().getValorDesejadoMensal());
        getPlanoDuracao().setValorDesejadoParcela(getContratoDuracao().getValorDesejadoParcela());
        getPlanoDuracao().setValorEspecifico(getContratoDuracao().getValorEspecifico());
    }

    public void inicializarDadosPlanoHorario() {
        setPlanoHorario(new PlanoHorarioVO());
        getPlanoHorario().setCodigo(getContratoHorario().getCodigo());
        getPlanoHorario().getHorario().setHorarioEscolhida(true);
        getPlanoHorario().setHorario(getContratoHorario().getHorario());
        getPlanoHorario().setPercentualDesconto(getContratoHorario().getPercentualDesconto());
        getPlanoHorario().setTipoOperacao(getContratoHorario().getTipoOperacao());
        getPlanoHorario().setTipoValor(getContratoHorario().getTipoValor());
        getPlanoHorario().setValorEspecifico(getContratoHorario().getValorEspecifico());
    }

    public void inicializarDadosPlanoCondicaoPagamento() {
        setPlanoCondicaoPagamento(new PlanoCondicaoPagamentoVO());
        getPlanoCondicaoPagamento().setCodigo(getContratoCondicaoPagamento().getCodigo());
        getPlanoCondicaoPagamento().setCondicaoPagamento(getContratoCondicaoPagamento().getCondicaoPagamento());
        getPlanoCondicaoPagamento().setPercentualDesconto(getContratoCondicaoPagamento().getPercentualDesconto());
        getPlanoCondicaoPagamento().setTipoOperacao(getContratoCondicaoPagamento().getTipoOperacao());
        getPlanoCondicaoPagamento().setTipoValor(getContratoCondicaoPagamento().getTipoValor());
        getPlanoCondicaoPagamento().setValorEspecifico(getContratoCondicaoPagamento().getValorEspecifico());
        getPlanoCondicaoPagamento().getCondicaoPagamento().setCondicaoPagamentoEscolhida(true);
    }

    public ContratoHorarioVO getContratoHorario() {
        if (contratoHorario == null) {
            contratoHorario = new ContratoHorarioVO();
        }
        return contratoHorario;
    }

    public void setContratoHorario(ContratoHorarioVO contratoHorario) {
        this.contratoHorario = contratoHorario;
    }

    public ContratoCondicaoPagamentoVO getContratoCondicaoPagamento() {
        if (contratoCondicaoPagamento == null) {
            contratoCondicaoPagamento = new ContratoCondicaoPagamentoVO();
        }
        return contratoCondicaoPagamento;
    }

    public void setContratoCondicaoPagamento(ContratoCondicaoPagamentoVO contratoCondicaoPagamento) {
        this.contratoCondicaoPagamento = contratoCondicaoPagamento;
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (
     * <code>Contrato</code>).
     */
    public ColaboradorVO getResponsavelLiberacaoCondicaoPagamento() {
        if (responsavelLiberacaoCondicaoPagamento == null) {
            responsavelLiberacaoCondicaoPagamento = new ColaboradorVO();
        }
        return (responsavelLiberacaoCondicaoPagamento);
    }

    /**
     * Define o objeto da classe <code>Colaborador</code> relacionado com (
     * <code>Contrato</code>).
     */
    public void setResponsavelLiberacaoCondicaoPagamento(ColaboradorVO obj) {
        this.responsavelLiberacaoCondicaoPagamento = obj;
    }

    public PlanoCondicaoPagamentoVO getPlanoCondicaoPagamento() {
        if (planoCondicaoPagamento == null) {
            planoCondicaoPagamento = new PlanoCondicaoPagamentoVO();
        }
        return planoCondicaoPagamento;
    }

    public void setPlanoCondicaoPagamento(PlanoCondicaoPagamentoVO planoCondicaoPagamento) {
        this.planoCondicaoPagamento = planoCondicaoPagamento;
    }

    public PlanoHorarioVO getPlanoHorario() {
        if (planoHorario == null) {
            planoHorario = new PlanoHorarioVO();
        }
        return planoHorario;
    }

    public void setPlanoHorario(PlanoHorarioVO planoHorario) {
        this.planoHorario = planoHorario;
    }

    public PlanoDuracaoVO getPlanoDuracao() {
        if (planoDuracao == null) {
            planoDuracao = new PlanoDuracaoVO();
        }
        return planoDuracao;
    }

    public void setPlanoDuracao(PlanoDuracaoVO planoDuracao) {
        this.planoDuracao = planoDuracao;
    }

    /**
     * Retorna o objeto da classe <code>Plano</code> relacionado com (
     * <code>Contrato</code>).
     */
    public PlanoVO getPlano() {
        if (plano == null) {
            plano = new PlanoVO();
        }
        return (plano);
    }

    /**
     * Define o objeto da classe <code>Plano</code> relacionado com (
     * <code>Contrato</code>).
     */
    public void setPlano(PlanoVO obj) {
         this.plano = obj;
    }

    /**
     * Retorna o objeto da classe <code>Pessoa</code> relacionado com (
     * <code>Contrato</code>).
     */
    public PessoaVO getPessoa() {
        if (pessoa == null) {
            pessoa = new PessoaVO();
        }
        return (pessoa);
    }

    /**
     * Define o objeto da classe <code>Pessoa</code> relacionado com (
     * <code>Contrato</code>).
     */
    public void setPessoa(PessoaVO obj) {
        this.pessoa = obj;
    }

    public String getTelefonesCliente () throws Exception {
        if(this.pessoa.getCodigo() != null) {
            return getFacade().getPessoa().obtemNumeroTelefone(this.pessoa.getCodigo());
        }
        return "";
    }

    public String getTelefonesColaborador () throws Exception {
        if(this.consultor.getPessoa().getCodigo() != null) {
            return getFacade().getPessoa().obtemNumeroTelefone(this.consultor.getPessoa().getCodigo());
        }
        return "";
    }

    public List<String> getCodigoVitio () throws Exception {
        List <String> codigo = new ArrayList<>();
        if(this.getConsultor().getCodigo() != null) {
            String codigoVitio = getFacade().getPessoa().obterCodigoViteo(this.getConsultor().getCodigo());
            this.consultor.setCodigoAfiliadoVitio(codigoVitio);
            if(codigoVitio.split("").length == 6) {
                return Arrays.asList(codigoVitio.split(""));
            }
        }
        return codigo;
    }

    /**
     * Retorna o objeto da classe <code>Empresa</code> relacionado com (
     * <code>Contrato</code>).
     */
    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    /**
     * Define o objeto da classe <code>Empresa</code> relacionado com (
     * <code>Contrato</code>).
     */
    public void setEmpresa(EmpresaVO obj) {
        this.empresa = obj;
    }

    public boolean isCancelamentoObrigatoriedadePagamento(){
        return getEmpresa().isCancelamentoObrigatoriedadePagamento(this);
    }

    public boolean isCancelamentoAvaliandoParcelas() {
        return getEmpresa().isCancelamentoAvaliandoParcelas();
    }

    public boolean isCancelamentoAntecipado(){
        return getEmpresa().isCancelamentoAntecipado(this);
    }

    public void setCancelamentoAntecipadoEmpresa(Boolean antecipado){
        getEmpresa().setCancelamentoAntecipado(antecipado);
    }

    public void setCancelamentoObrigatoriedadePagamentoEmpresa(Boolean obrigatoriedade){
        getEmpresa().setCancelamentoObrigatoriedadePagamento(obrigatoriedade);
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>ContratoModalidade</code>.
     */
    public List<ContratoModalidadeVO> getContratoModalidadeVOs() {
        if (contratoModalidadeVOs == null) {
            contratoModalidadeVOs = new ArrayList();
        }
        return (contratoModalidadeVOs);
    }
    public String getNumeroContratoModalidade(){
        if(contratoModalidadeVOs==null)
            return "0";
        else
        return contratoModalidadeVOs.size()+"";
    }

    /**
     * Define Atributo responsável por manter os objetos da classe
     * <code>ContratoModalidade</code>.
     */
    public void setContratoModalidadeVOs(List<ContratoModalidadeVO> contratoModalidadeVOs) {
        this.contratoModalidadeVOs = contratoModalidadeVOs;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>HistoricoContrato</code>.
     */
    public List getHistoricoContratoVOs() {
        if (historicoContratoVOs == null) {
            historicoContratoVOs = new ArrayList();
        }
        return (historicoContratoVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe
     * <code>HistoricoContrato</code>.
     */
    public void setHistoricoContratoVOs(List historicoContratoVOs) {
        this.historicoContratoVOs = historicoContratoVOs;
    }

    public Double getValorFinal() {
        if (valorFinal == null) {
            valorFinal = 0.0;
        }
        return (valorFinal);
    }

    public Double getValorFinalComDescontos() {
        if (getValorFinal() > getTotalDescontosProdutoEParcelas()){
            return (getValorFinal() - getTotalDescontosProdutoEParcelas());
        }
        return 0.0;
    }

    public Double getSubTotalComDescontos() {
        if (getValorBaseCalculo() + getTotalFinalProdutos() - getTotalDescontosProdutoEParcelas() > 0){
            return (getValorBaseCalculo() + getTotalFinalProdutos() - getTotalDescontosProdutoEParcelas());
        }
        return 0.0;
    }


    public Double getValorFinalApresentarConferirNegociacao() {
        if ((getNumeroCupomDesconto() != null) && (!getNumeroCupomDesconto().equals(""))) {
            return (getValorFinal() - descontoParcelaUsandoCupomDesconto);
        }
        return getValorFinal();
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorFinal);
    }

    public Double getValorBaseCalculo() {
        if (valorBaseCalculo == null || getPlano().getBolsa()) {
            valorBaseCalculo = 0.0;
        }
        return (valorBaseCalculo);
    }

    public String getValorBaseCalculoApresentar(){
        NumberFormat df = NumberFormat.getCurrencyInstance(new Locale("pt", "BR"));
        return df.format(getValorBaseCalculo());
    }

    public Double getValorBaseCalculoApresentarConferirNegociacao() {
        if ((getNumeroCupomDesconto() != null) && (!getNumeroCupomDesconto().equals(""))) {
            return (getValorBaseCalculo() - descontoParcelaUsandoCupomDesconto);
        }
        return getValorBaseCalculo();
    }

    public Double getTotalDescontosProdutoEParcelas(){

        return getDescontoParcelaUsandoCupomDesconto() + getPremioProdutosCupomDesconto();
    }

    public void setValorBaseCalculo(Double valorBaseCalculo) {
        this.valorBaseCalculo = valorBaseCalculo;
    }

    public Date getVigenciaAte() {
        if (vigenciaAte == null) {
            vigenciaAte = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (vigenciaAte);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato
     * padrão dd/mm/aaaa.
     */
    public String getVigenciaAte_Apresentar() {
        return (Uteis.getData(vigenciaAte));
    }

    public void setVigenciaAte(Date vigenciaAte) {
        this.vigenciaAte = vigenciaAte;
    }

    public Date getVigenciaDe() {
        if (vigenciaDe == null) {
            vigenciaDe = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (vigenciaDe);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato
     * padrão dd/mm/aaaa.
     */
    public String getVigenciaDe_ApresentarTela7() {
        return Uteis.getData(vigenciaDe);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato
     * padrão dd/mm/aaaa.
     */
    public String getVigenciaDe_Apresentar() {
        if (codigo != null && codigo != 0) {
            return (Uteis.getData(vigenciaDe));
        }
        return " - ";
    }

    public String getVigenciaDe_Ordenar() {
        if (vigenciaDe != null) {
            return (Uteis.getDataAplicandoFormatacao(vigenciaDe, "yyyyMMdd"));
        }
        return " - ";
    }

    public void setVigenciaDe(Date vigenciaDe) {
        this.vigenciaDe = vigenciaDe;
    }

    public Boolean getEstendeCoberturaFamiliares() {
        if (estendeCoberturaFamiliares == null) {
            estendeCoberturaFamiliares = false;
        }
        return (estendeCoberturaFamiliares);
    }

    public Boolean isEstendeCoberturaFamiliares() {
        return (estendeCoberturaFamiliares);
    }

    public void setEstendeCoberturaFamiliares(Boolean estendeCoberturaFamiliares) {
        this.estendeCoberturaFamiliares = estendeCoberturaFamiliares;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return (situacao);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo
     * com um domínio específico. Com base no valor de armazenamento do atributo
     * esta função é capaz de retornar o de apresentação correspondente. Útil
     * para campos como sexo, escolaridade, etc.
     */
    public String getSituacao_Apresentar() {
        if (situacao == null) {
            situacao = "";
        }
        if (situacao.equals("AT")) {
            return "Ativo";
        }
        if (situacao.equals("AE")) {
            return "Atestado";
        }
        if (situacao.equals("CR")) {
            return "Férias";
        }
        if (situacao.equals("CA")) {
            return "Cancelado";
        }
        if (situacao.equals("FE")) {
            return "Férias";
        }
        if (situacao.equals("TR")) {
            return "Trancado";
        }
        if (situacao.equals("CO")) {
            return "Congelado";
        }
        if (situacao.equals("IN")) {
            return "Inativo";
        }
        return (situacao);
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List getContratoPlanoProdutoSugeridoVOs() {
        if (contratoPlanoProdutoSugeridoVOs == null) {
            contratoPlanoProdutoSugeridoVOs = new ArrayList<>();
        }
        return contratoPlanoProdutoSugeridoVOs;
    }

    public void setContratoPlanoProdutoSugeridoVOs(List contratoPlanoProdutoSugeridoVOs) {
        this.contratoPlanoProdutoSugeridoVOs = contratoPlanoProdutoSugeridoVOs;
    }

    public ConvenioDescontoVO getConvenioDesconto() {
        if (convenioDesconto == null) {
            convenioDesconto = new ConvenioDescontoVO();
        }
        return convenioDesconto;
    }

    public void setConvenioDesconto(ConvenioDescontoVO convenioDesconto) {
        this.convenioDesconto = convenioDesconto;
    }

    public List<MovProdutoVO> getMovProdutoVOs() {
        if (movProdutoVOs == null) {
            movProdutoVOs = new ArrayList<>();
        }
        return movProdutoVOs;
    }

    public void setMovProdutoVOs(List movProdutoVOs) {
        this.movProdutoVOs = movProdutoVOs;
    }

    public List<MovParcelaVO> getMovParcelaVOs() {
        if (movParcelaVOs == null) {
            movParcelaVOs = new ArrayList<>();
        }
        return movParcelaVOs;
    }

    public void setMovParcelaVOs(List movParcelaVOs) {
        this.movParcelaVOs = movParcelaVOs;
    }

    public String getObservacao() {
        if (observacao == null || observacao.contains("</td></tr></tbody></table></td>")) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
         this.observacao = observacao;
    }

    public UsuarioVO getResponsavelContrato() {
        if (responsavelContrato == null) {
            responsavelContrato = new UsuarioVO();
        }
        return responsavelContrato;
    }

    public void setResponsavelContrato(UsuarioVO responsavelContrato) {
        this.responsavelContrato = responsavelContrato;
    }

    public UsuarioVO getResponsavelAutorizacaoDesconto() {
        if (responsavelAutorizacaoDesconto == null) {
            responsavelAutorizacaoDesconto = new UsuarioVO();
        }
        return responsavelAutorizacaoDesconto;
    }

    public void setResponsavelAutorizacaoDesconto(UsuarioVO responsavelAutorizacaoDesconto) {
        this.responsavelAutorizacaoDesconto = responsavelAutorizacaoDesconto;
    }

    public UsuarioVO getResponsavelAutorizacaoDescontoConvenio() {
        if (responsavelAutorizacaoDescontoConvenio == null) {
            responsavelAutorizacaoDescontoConvenio = new UsuarioVO();
        }
        return responsavelAutorizacaoDescontoConvenio;
    }

    public void setResponsavelAutorizacaoDescontoConvenio(UsuarioVO responsavelAutorizacaoDescontoConvenio) {
        this.responsavelAutorizacaoDescontoConvenio = responsavelAutorizacaoDescontoConvenio;
    }

    public Boolean getPagarComBoleto() {
        if (pagarComBoleto == null) {
            pagarComBoleto = false;
        }
        return pagarComBoleto;
    }

    public void setPagarComBoleto(Boolean pagarComBoleto) {
        this.pagarComBoleto = pagarComBoleto;
    }

    public Boolean getDividirProdutosNasParcelas() {
        if (dividirProdutosNasParcelas == null) {
            dividirProdutosNasParcelas = false;
        }
        return dividirProdutosNasParcelas;
    }

    public void setDividirProdutosNasParcelas(Boolean dividirProdutosNasParcelas) {
        this.dividirProdutosNasParcelas = dividirProdutosNasParcelas;
    }

    public ProdutoVO getDesconto() {
        if (desconto == null) {
            desconto = new ProdutoVO();
        }
        return desconto;
    }

    public void setDesconto(ProdutoVO desconto) {
        this.desconto = desconto;
    }

    public String getTipoDesconto() {
        if (tipoDesconto == null) {
            tipoDesconto = "";
        }
        return tipoDesconto;
    }

    public void setTipoDesconto(String tipoDesconto) {
        this.tipoDesconto = tipoDesconto;
    }

    public Double getValorDescontoEspecifico() {
        if (valorDescontoEspecifico == null) {
            valorDescontoEspecifico = (double) 0;
        }
        return valorDescontoEspecifico;
    }

    public void setValorDescontoEspecifico(Double valorDescontoEspecifico) {
        this.valorDescontoEspecifico = valorDescontoEspecifico;
    }

    public Double getValorDescontoPorcentagem() {
        if (valorDescontoPorcentagem == null) {
            valorDescontoPorcentagem = (double) 0;
        }
        return valorDescontoPorcentagem;
    }

    public void setValorDescontoPorcentagem(Double valorDescontoPorcentagem) {
        this.valorDescontoPorcentagem = valorDescontoPorcentagem;
    }

    public Boolean getRenovarContrato() {
        if (renovarContrato == null) {
            renovarContrato = false;
        }
        return renovarContrato;
    }

    public void setRenovarContrato(Boolean renovarContrato) {
        this.renovarContrato = renovarContrato;
    }

    public Double getSomaProduto() {
        if (somaProduto == null) {
            somaProduto = 0.0;
        }
        return somaProduto;
    }

    public void setSomaProduto(Double somaProduto) {
        this.somaProduto = somaProduto;
    }

    public Boolean getSelecionarTodasParcela() {
        if (selecionarTodasParcela == null) {
            selecionarTodasParcela = false;
        }
        return selecionarTodasParcela;
    }

    public void setSelecionarTodasParcela(Boolean selecionarTodasParcela) {
        this.selecionarTodasParcela = selecionarTodasParcela;
    }

    public Date getVigenciaAteAjustada() {
        return vigenciaAteAjustada;
    }

    public String getVigenciaAteAjustada_Apresentar() {
        String data = Uteis.getData(vigenciaAteAjustada);
        if (UteisValidacao.emptyString(data)) {
            return " - ";
        }
        return data;
    }

    public String getVigenciaAteAjustada_Ordenar() {
        if (vigenciaAteAjustada != null) {
            return (Uteis.getDataAplicandoFormatacao(vigenciaAteAjustada, "yyyyMMdd"));
        }
        return " - ";
    }

    public void setVigenciaAteAjustada(Date vigenciaAteAjustada) {
        this.vigenciaAteAjustada = vigenciaAteAjustada;
    }

    public void inicializarConfiguracaoSistema() throws Exception {

        setConfiguracaoSistema(getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS));

    }

    public void inserirDadosReferentesAoContratoRenovacao() {
        this.setDataPrevistaRenovar(this.getVigenciaAteAjustada());
        this.setDataPrevistaRenovarAntecipado(this.getVigenciaAteAjustada());
        this.setContratoBaseadoRenovacao(this.getCodigo());
        this.setDataRenovarRealizada(negocio.comuns.utilitarias.Calendario.hoje());
        this.setContratoBaseadoRematricula(0);
        this.setDataRematriculaRealizada(null);
        this.setContratoOrigemTransferencia(null);
    }

    public void inserirDadosReferentesAoContratoRematricula() {
        this.setDataPrevistaRematricula(this.getVigenciaAteAjustada());
        this.setContratoBaseadoRematricula(this.getCodigo());
        this.setDataRematriculaRealizada(negocio.comuns.utilitarias.Calendario.hoje());
        this.setContratoBaseadoRenovacao(0);
        this.setDataRenovarRealizada(null);
        this.setContratoOrigemTransferencia(null);
    }

    public void obterDataFinalContrato(Date dataInicio) throws Exception {
        int i = 0;
        this.vigenciaDe = dataInicio;
        //Date dataAtual = Uteis.obterDataAnterior(this.getVigenciaDe(), 1);
        //Date data = Uteis.obterDataAnterior(this.getVigenciaDe(), 1);
        //data = Uteis.obterDataFuturaParcela(dataAtual,this.getPlanoDuracao().getNumeroMeses() );
        Date data = Uteis.obterDataFuturaParcela(this.getVigenciaDe(), this.getPlanoDuracao().getNumeroMeses());


        Integer diasExtra = 0;
        if (getSaldoCreditoContratoOrigemRenovacao() > 0){
            diasExtra = this.getPlanoDuracao().getQuantidadeDiasExtra() + (getDiasRestanteContratoOrigemRenovacao());
        }else{
            diasExtra = this.getPlanoDuracao().getQuantidadeDiasExtra();
        }
//        this.getPlanoDuracao().setQuantidadeDiasExtra(diasExtra);
        if (diasExtra > 0){
            data = Calendario.somarDias(Calendario.getDataComHoraZerada(this.getVigenciaDe()), diasExtra + this.getPlanoDuracao().getNumeroMeses() * 30);
        }

        data = Calendario.somarDias(Calendario.getDataComHoraZerada(data), -1);

        this.setVigenciaAte(data);
        this.setVigenciaAteAjustada(data);
        this.setDataPrevistaRenovar(data);
        this.setDataPrevistaRematricula(data);
        if(dataLancamento == null) this.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
    }

    public void obterDataFinalContratoComContratoDuracao(Date dataInicio) throws Exception {
        int i = 0;
        Long nrDiasReal = Uteis.nrDiasEntreDatas(this.vigenciaDe, this.vigenciaAteAjustada);
        this.vigenciaDe = dataInicio;
        Date dataAtual = Uteis.obterDataAnterior(this.getVigenciaDe(), 1);
        Date data = (Uteis.obterDataFuturaParcela(dataAtual, this.getContratoDuracao().getNumeroMeses()));

        if(this.getContratoDuracao().getQuantidadeDiasExtra() > 0)
        {
            data = Calendario.somarDias(Calendario.getDataComHoraZerada(this.getVigenciaDe()), this.getContratoDuracao().getQuantidadeDiasExtra() + this.getPlanoDuracao().getNumeroMeses() * 30);
        }

        if(Calendario.igual(this.vigenciaAte, this.vigenciaAteAjustada)){
            this.setVigenciaAteAjustada(data);
            this.setDataPrevistaRenovar(data);
            this.setDataPrevistaRematricula(data);
        }else{
            Date dataAjustada = Calendario.somarDias(dataAtual, nrDiasReal.intValue());
            this.setVigenciaAteAjustada(dataAjustada);
            this.setDataPrevistaRenovar(dataAjustada);
            this.setDataPrevistaRematricula(dataAjustada);
        }
        this.setVigenciaAte(data);
        this.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
    }

    /**
     * Metodo responsavel pela apresentação do Botao renovar contrato da tela de
     * edicao do cliente(tela6) que somente será permitido a renovação do
     * contrato se o dia de vencimento do mesmo não estive vencindo.
     *
     * @throws java.lang.Exception
     */
    public void verificarQualBotaoReferenteASituacaoContratoSeraApresentado(ClienteVO clienteVO, List listaContrato) throws Exception {
        Integer nrDiasVencido = 0;
        if (clienteVO.getEmpresa().getCarenciaRenovacao() != 0) {
            nrDiasVencido = clienteVO.getEmpresa().getCarenciaRenovacao();
        } else {
            inicializarConfiguracaoSistema();
            nrDiasVencido = getConfiguracaoSistema().getCarenciaRenovacao();
        }
        if (clienteVO.getSituacao().equals("AT")) {
            for (Object aListaContrato : listaContrato) {
                ContratoVO contrato = (ContratoVO) aListaContrato;
                Date dataAtual = Calendario.hoje();
                Date dataVigenciaAjustada = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), 1);
                Date dataVigenciaRenovacao = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), (nrDiasVencido + 1));
                if (contrato.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores()) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if ((contrato.getContratoResponsavelRenovacaoMatricula() != 0) || (contrato.getContratoResponsavelRematriculaMatricula() != 0)) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if (getFacade().getContratoOperacao().existeOperacaoParaEsteContrato(contrato.getCodigo(), "CA")) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(true);
                } else if (ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetornoBoolean(contrato)) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if (dataAtual.before(dataVigenciaAjustada)) {
                    contrato.setRenovarContrato(true);
                    contrato.setRematricularContrato(false);
                } else if (dataAtual.before(dataVigenciaRenovacao)) {
                    contrato.setRenovarContrato(true);
                    contrato.setRematricularContrato(false);
                } else if (contrato.getSituacao().equals("IN")) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(true);
                } else {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                }
            }
        } else if (clienteVO.getSituacao().equals("IN") || clienteVO.getSituacao().equals("TR")) {
            for (Object aListaContrato : listaContrato) {
                ContratoVO contrato = (ContratoVO) aListaContrato;
                String sit = getFacade().getZWFacade().obterSituacaoClienteInativo(contrato, clienteVO);
                // Date dataAtual = negocio.comuns.utilitarias.Calendario.hoje();
                // Date dataVigenciaRenovacao = Uteis.obterDataFutura2(contrato.getVigenciaAteAjustada(), (nrDiasVencido + 1));
                if (contrato.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores()) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if ((contrato.getContratoResponsavelRenovacaoMatricula() != 0) || (contrato.getContratoResponsavelRematriculaMatricula() != 0)) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if (contrato.getSituacao().equals("CA")) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(true);
                } else if (sit.equals("VE")
                        || (contrato.getSituacao().equals("AT") && Calendario.maiorOuIgual(contrato.getVigenciaAteAjustada(), Calendario.hoje()))) { // contratos rematriculados futuramente
                    contrato.setRenovarContrato(true);
                    contrato.setRematricularContrato(false);
                } else if (sit.equals("DE")) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(true);
                } else {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                }
            }
        } else {
            for (Object aListaContrato : listaContrato) {
                ContratoVO contrato = (ContratoVO) aListaContrato;
                if (contrato.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores() || (contrato.getContratoResponsavelRenovacaoMatricula() != 0) || (contrato.getContratoResponsavelRematriculaMatricula() != 0)) {
                    contrato.setRenovarContrato(false);
                    contrato.setRematricularContrato(false);
                } else if (contrato.getSituacao().equals("AT") && Calendario.maiorOuIgual(contrato.getVigenciaAteAjustada(), Calendario.hoje())) {
                    contrato.setRenovarContrato(true);
                    contrato.setRematricularContrato(false);
                } else if (UtilReflection.objetoMaiorQueZero(contrato ,"getPessoaOriginal().getCodigo()") && contrato.getPessoaOriginal().getCodigo().equals(clienteVO.getPessoa().getCodigo())) {
                    String sit = getFacade().getZWFacade().obterSituacaoClienteInativo(contrato, clienteVO);
                    if (contrato.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores()) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    } else if ((contrato.getContratoResponsavelRenovacaoMatricula() != 0) || (contrato.getContratoResponsavelRematriculaMatricula() != 0)) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    } else if (contrato.getSituacao().equals("CA")) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(true);
                    } else if (sit.equals("VE")
                            || (contrato.getSituacao().equals("AT") && Calendario.maiorOuIgual(contrato.getVigenciaAteAjustada(), Calendario.hoje()))) { // contratos rematriculados futuramente
                        contrato.setRenovarContrato(true);
                        contrato.setRematricularContrato(false);
                    } else if (sit.equals("DE")) {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(true);
                    } else {
                        contrato.setRenovarContrato(false);
                        contrato.setRematricularContrato(false);
                    }
                }
            }
        }
    }

    /**
     * Metodo responsavel pela apresentação do Botao renovar contrato da tela de
     * edicao do cliente(tela6) que somente será permitido a renovação do
     * contrato se o dia de vencimento do mesmo não estive vencindo.
     *
     * @throws java.lang.Exception
     */
    public void obterSituacaoContratoRenovacaoRematricula() throws Exception {
        Integer nrDiasVencido = 0;
        if (getEmpresa().getCarenciaRenovacao() != 0) {
            nrDiasVencido = getEmpresa().getCarenciaRenovacao();
        } else {
            inicializarConfiguracaoSistema();
            nrDiasVencido = getConfiguracaoSistema().getCarenciaRenovacao();
        }
        Date dataAtual = negocio.comuns.utilitarias.Calendario.hoje();
        Date dataVigenciaAjustada = Uteis.obterDataFutura2(this.getVigenciaAteAjustada(), 1);
        Date dataVigenciaRenovacao = Uteis.obterDataFutura2(this.getVigenciaAteAjustada(), (nrDiasVencido + 1));
        boolean existeCancelamento = getFacade().getContratoOperacao().existeOperacaoParaEsteContrato(getCodigo(), "CA");
        if (getSituacao().equals("IN")|| existeCancelamento) {
            if (existeCancelamento) {
                this.setSituacaoRematricula("CA");
                this.setRenovarContrato(false);
                this.setRematricularContrato(true);
                this.setSituacaoContrato("RE");
            } else if (dataAtual.before(dataVigenciaRenovacao)) {
                this.setRenovarContrato(true);
                this.setRematricularContrato(false);
                this.setSituacaoContrato("RN");
            } else {
                this.setSituacaoRematricula("DE");
                this.setRenovarContrato(false);
                this.setRematricularContrato(true);
                this.setSituacaoContrato("RE");
            }
        } else if (dataAtual.before(dataVigenciaAjustada)) {
            this.setRenovarContrato(true);
            this.setSituacaoContrato("RN");
            this.setRematricularContrato(false);
        } else if (dataAtual.before(dataVigenciaRenovacao)) {
            this.setRenovarContrato(true);
            this.setRematricularContrato(false);
            this.setSituacaoContrato("RN");
        } else {
            this.setRenovarContrato(false);
            this.setRematricularContrato(true);
            this.setSituacaoContrato("RE");
        }
    }

    public Integer obterNrDiasContratoAteDataVigenciaAjustada() {
        long nrDias = Uteis.nrDiasEntreDatas(getVigenciaDe(), getVigenciaAteAjustada()) + 1;
        return new Long(nrDias).intValue();
    }

    public Double obterValorContratoReferenteMensal() {
        Double valorMensal = 0.0;
        for (ContratoModalidadeVO contratoModalidade : getContratoModalidadeVOs()) {
            valorMensal = valorMensal + contratoModalidade.getValorFinalModalidade();
        }

        if(valorMensal == 0.0 && getValorFinal() > 0.0) {
            try {
                Integer nrDiasContrato = getFacade().getZWFacade().obterNrDiasContrato(this);
                valorMensal = getValorFinal() / nrDiasContrato * 30;
            }catch (Exception e) {}
        }

        return Uteis.arredondarForcando2CasasDecimais(valorMensal);
    }

    public Double obterValorDiaContratoValorMensal(Double valorMensal) {
        double valorDiaContrato = valorMensal / 30;
        return Uteis.arredondarForcando2CasasDecimais(valorDiaContrato);
    }

    public Integer obterNrDiasRestantesProFinalDoContrato(Integer nrDiasContrato, Integer nrDiasUtilizadosPeloCliente) {
        return nrDiasContrato - nrDiasUtilizadosPeloCliente;
    }
    public Double obterCreditoRestanteContrato(Integer nrDiasUtilizadosPeloCliente, Double valorDiaContratoValorBase, boolean manterSinal) {
        return obterCreditoRestanteContrato(nrDiasUtilizadosPeloCliente, valorDiaContratoValorBase, manterSinal, 0D);

    }
    public Double obterCreditoRestanteContrato(Integer nrDiasUtilizadosPeloCliente, Double valorDiaContratoValorBase, boolean manterSinal, double valorAnuidade) {
        /**
         * Se a anuidade estiver vencida, o valor dela entra no cálculo da multa, ticket #18927
         * */
        double valorRestanteCredito = (getValorBaseCalculo() + valorAnuidade) - (nrDiasUtilizadosPeloCliente * valorDiaContratoValorBase);
        return manterSinal ? Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorRestanteCredito) : Uteis.arredondarForcando2CasasDecimais(valorRestanteCredito);
    }

    public Integer obterNrDiasContratoOperacaoBonus() throws Exception {
        Integer nrDiasOperacoesAux = 0;
        List listaOperacoesContrato = getFacade().getContratoOperacao().consultarPorContrato(getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        for (Object aListaOperacoesContrato : listaOperacoesContrato) {
            ContratoOperacaoVO obj = (ContratoOperacaoVO) aListaOperacoesContrato;
            if (obj.getBonusAcrescimo()) {
                nrDiasOperacoesAux += obj.obterNrDiasContratoOperacao();
            }
        }
        return nrDiasOperacoesAux;
    }

    public PlanoComposicaoVO processarContratoComposicaoIgualPlanoComposicao(ContratoComposicaoVO contratoCompo) throws Exception {
        for (Object o : getPlano().getPlanoComposicaoVOs()) {
            PlanoComposicaoVO planoCompo = (PlanoComposicaoVO) o;
            if (contratoCompo.getComposicaoVO().getCodigo().intValue() == planoCompo.getComposicao().getCodigo().intValue()) {
                planoCompo.getComposicao().setComposicaoEscolhida(true);
                return planoCompo;
            }
        }
        return null;
    }

    public void processarModalidadeQueFazemParteComposicao(ComposicaoModalidadeVO composicaoModalidade) throws Exception {
        boolean modalidadeExistente = false;
        for (ContratoModalidadeVO contratoModali : getContratoModalidadeVOs()) {
            if (contratoModali.getModalidade().getCodigo().equals(composicaoModalidade.getModalidade().getCodigo())) {
                contratoModali.getModalidade().setComposicao(true);
                contratoModali.getModalidade().setModalidadeEscolhida(true);
                contratoModali.getModalidade().setValorMensal(composicaoModalidade.getValorMensalComposicao());
                modalidadeExistente = true;
                break;
            }
        }
        if (!modalidadeExistente) {
            ContratoModalidadeVO obj = new ContratoModalidadeVO();
            obj.setModalidade(composicaoModalidade.getModalidade());
            obj.getModalidade().setComposicao(true);
            obj.getModalidade().setModalidadeEscolhida(true);
            obj.getModalidade().setValorMensal(composicaoModalidade.getValorMensalComposicao());
            adicionarObjContratoModalidadeVOs(obj);
        }
    }

    public void removerMatriculaAlunoTurma(Connection con) throws Exception {
        MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurmaDAO;
        try {
            matriculaAlunoHorarioTurmaDAO = new MatriculaAlunoHorarioTurma(con);

            List<MatriculaAlunoHorarioTurmaVO> lista = matriculaAlunoHorarioTurmaDAO.consultarPorCodigoContrato(getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            for (MatriculaAlunoHorarioTurmaVO aux : lista) {
                //getFacade().getHorarioTurma().consultarPorChavePrimaria(aux.getHorarioTurma().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                matriculaAlunoHorarioTurmaDAO.excluirSemCommit(aux);
            }
        } finally {
            matriculaAlunoHorarioTurmaDAO = null;
        }
    }

    public void gerarVigenciaMatriculaPlanoCreditoTreino(Date dataInicio, Integer quantidadeCredito)throws Exception{
        if (!(this.getPlano().isVendaCreditoTreino() && this.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA)){
            return ;
        }
        if (getVigenciaTurmaCreditoTreinoAte() != null){
            // neste caso já foi povoada na tela de manutenção de modalidade.
            return ;
        }
        List<HorarioTurmaVO>listaHorarioTurma = new ArrayList<HorarioTurmaVO>();
        for  (ContratoModalidadeVO contratoModalidadeVO : getContratoModalidadeVOs()) {
            if (contratoModalidadeVO.getModalidade().getUtilizarTurma()) {
                for (Object obj : contratoModalidadeVO.getContratoModalidadeTurmaVOs()) {
                    ContratoModalidadeTurmaVO contratoModalidadeTurmaVO = (ContratoModalidadeTurmaVO) obj;
                    if (contratoModalidadeTurmaVO.getTurma().getTurmaEscolhida()) {
                        for (Object objModTurmaHorario : contratoModalidadeTurmaVO.getContratoModalidadeHorarioTurmaVOs()) {
                            ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurmaVO = (ContratoModalidadeHorarioTurmaVO) objModTurmaHorario;
                            if (contratoModalidadeHorarioTurmaVO.getHorarioTurma().getHorarioTurmaEscolhida()) {
                                listaHorarioTurma.add(contratoModalidadeHorarioTurmaVO.getHorarioTurma());
                            }
                        }
                    }
                }
            }
        }
        List<Date> listaDias = Uteis.getDiasEntreDatas(dataInicio, this.getVigenciaAteAjustada());
        int totalCredito = quantidadeCredito;
        Date ultimoDiaUtilizar = this.getVigenciaAteAjustada();
        for (Date dia: listaDias){
            Calendar diaVerificar = Calendario.getInstance();
            diaVerificar.setTime(dia);
            for (HorarioTurmaVO horarioTurmaVO: listaHorarioTurma){
                if (horarioTurmaVO.getDiaSemanaNumero() == diaVerificar.get(Calendar.DAY_OF_WEEK)){
                    totalCredito = totalCredito -1;
                    break;
                }
            }
            if (totalCredito <= 0){
                ultimoDiaUtilizar = dia;
                break;
            }
        }
        setVigenciaTurmaCreditoTreinoAte(ultimoDiaUtilizar);
    }


    public Map<Integer, Date> verificarTotalCreditoPossivelUtilizar(Date dataInicio, List listaContratoModalidadeTurma, Date fim,final Integer saldo)throws Exception{
        List<HorarioTurmaVO>listaHorarioTurma = new ArrayList<HorarioTurmaVO>();
        for (Object obj : listaContratoModalidadeTurma) {
            ContratoModalidadeTurmaVO contratoModalidadeTurmaVO = (ContratoModalidadeTurmaVO) obj;
            if (contratoModalidadeTurmaVO.getTurma().getTurmaEscolhida()) {
                for (Object objModTurmaHorario : contratoModalidadeTurmaVO.getContratoModalidadeHorarioTurmaVOs()) {
                    ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurmaVO = (ContratoModalidadeHorarioTurmaVO) objModTurmaHorario;
                    if (contratoModalidadeHorarioTurmaVO.getHorarioTurma().getHorarioTurmaEscolhida()) {
                        listaHorarioTurma.add(contratoModalidadeHorarioTurmaVO.getHorarioTurma());
                    }
                }
            }
        }
        List<Date> listaDias = Uteis.getDiasEntreDatas(dataInicio, fim);
        Integer totalCreditoPossivelUtilizar = 0;
        Date ultimoDia = null;
        for (Date dia: listaDias){
            Calendar diaVerificar = Calendario.getInstance();
            diaVerificar.setTime(dia);
            for (HorarioTurmaVO horarioTurmaVO: listaHorarioTurma){
                if (horarioTurmaVO.getDiaSemanaNumero() == diaVerificar.get(Calendar.DAY_OF_WEEK)){
                    totalCreditoPossivelUtilizar ++;
                    ultimoDia = dia;
                    break;
                }
            }
            if(saldo !=null && totalCreditoPossivelUtilizar >= saldo){ // caso o saldo seja informado, sistema irá limitar ao saldo
                break;
            }
        }
        Map<Integer, Date> mapaDia = new HashMap<Integer, Date>();
        mapaDia.put(totalCreditoPossivelUtilizar,ultimoDia);
        return mapaDia;
    }


    public void removerMatriculaAlunoTurma(ContratoModalidadeVO cont, ContratoModalidadeTurmaVO modTurma, ContratoModalidadeHorarioTurmaVO modalidadeHorarioTurma, MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurma) throws Exception {
        if (modalidadeHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida() && modalidadeHorarioTurma.getHorarioTurma().getNrAlunoMatriculado() == 0) {
            throw new Exception("Não foi possivel remover o  aluno desse Horário, pois o mesmo encontra-se vazio.");
        }
    }

    public double valorProRataContrato() {
        int qtdePM = 0;
        double valorPM = 0.0;
        double valor = 0.0;

        for (Object o : getMovProdutoVOs()) {
            MovProdutoVO aux = (MovProdutoVO) o;
            // se é um produto mensal independente de ser pro-rata ou nao
            if (aux.getProduto().getTipoProduto().equals("PM")) {
                qtdePM++;
            }
            // se é pro-rata
            if (aux.getDescricao().contains("PRO-RATA")) {
                valor = aux.getTotalFinal();
                continue;
            }
            // se é produto mensal mas nao é pro-rata
            if (aux.getProduto().getTipoProduto().equals("PM")) {
                valorPM = aux.getTotalFinal();
            }
        }
        // se pro-rata de mes proporcional nao gerado a mais
        if (getPlanoDuracao().getNumeroMeses() == qtdePM && valor > 0) {
            return valorPM - valor;
        }
        // se pro-rata com produto mensal extra
        return valor * (-1);
    }

    public Double obterValorParcelas(ContratoVO contrato, Boolean entrada) throws Exception {
        return obterValorParcelas(contrato, entrada, 0);
    }

    @Deprecated
    public Double obterValorParcelas(ContratoVO obj, Boolean entrada, int numeroParcela) throws Exception {
        Double valorBaseadoEntrada = 1.0;
        Integer numeroMes = 0;

        // Gerar parcela com valor diferente das outras, para plano recorrentes
        // Definições da parcela no cadastro de plano, aba recorrência, campo "Cobrar anuidade junto com a parcela"
        if(obj.isDeveGerarParcelasComValorDiferente() && numeroParcela > 0) {
            Double valor = getFacade().getPlanorecorrenciaParcela().consultarValorPorNumeroParcela(numeroParcela, obj.getPlano().getPlanoRecorrencia().getCodigo());
            if(valor != null){
                return valor;
            }
        }
        if (entrada) {
            numeroMes = obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas() - 1;
            if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getPercentualValorEntrada() > 0) {
                valorBaseadoEntrada = Uteis.arredondar(((100 - obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getPercentualValorEntrada()) / 100),4, 1);
            } else {
                Double percentualEntrada = 1.0 / obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas();
                percentualEntrada = percentualEntrada * 100;
                if (percentualEntrada < 100) {
                    valorBaseadoEntrada = (100 - percentualEntrada) / 100;
                } else {
                    valorBaseadoEntrada = 1.0;
                }
            }
        } else {
            numeroMes = obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas();
        }
        if (numeroMes != 0) {
            if (obj.getDividirProdutosNasParcelas()) {
                if (obj.getGerarParcelaParaProdutos() && obj.getSomaAdesao() > 0) {
                    if (obj.getPlano().getRegimeRecorrencia() && obj.getGerarParcelaAnuidadeSeparada()) {
                        return Uteis.arredondarForcando2CasasDecimais(((obj.getValorFinal() - obj.getSomaAdesao() - obj.getSomaAnuidade()) * valorBaseadoEntrada) / numeroMes);
                    } else {
                        return Uteis.arredondarForcando2CasasDecimais(((obj.getValorFinal() - obj.getSomaAdesao()) * valorBaseadoEntrada) / numeroMes);
                    }
                } else {
                    if(obj.getGerarParcelaAnuidadeSeparada()){
                        return Uteis.arredondarForcando2CasasDecimais(((obj.getValorFinal() -  obj.getSomaAnuidade()) * valorBaseadoEntrada) / numeroMes);
                    } else {
                        return Uteis.arredondarForcando2CasasDecimais((obj.getValorFinal() * valorBaseadoEntrada) / numeroMes);
                    }
                }

            } else {
                Double valorContratoSemProduto = 0.0;
                Double valorParcela = 0.0;

                // Quando existem parcelas com valor diferente (ticket #13324)
                // O valor da da parcela não pode ser calculado com base no valorFinal / Número de meses.
                // Neste caso utiliza-se o valor mensal do plano recorrente
                if (obj.isDeveGerarParcelasComValorDiferente()) {

                    valorParcela = (obj.getValorFinal() - obj.getSomaProduto() - (obj.getPlano().getPlanoRecorrencia().getValorTotalParcelasValorDirefente() - obj.getDescontoExtraParcelasValorDiferente())) / (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas()- obj.getPlano().getPlanoRecorrencia().getQuantidadeParcelasValorDiferente());
                } else if (obj.getPlano().getRegimeRecorrencia()) {
                    // qdo é recorrencia soma o valor do pro-rata
                    valorContratoSemProduto = obj.getValorFinal() - obj.getSomaProduto() + valorProRataContrato();
                    if (obj.isDeveGerarParcelasComValorDiferente()) {
                        valorParcela = (valorContratoSemProduto - obj.getPlano().getPlanoRecorrencia().getValorTotalParcelasValorDirefente() - obj.getDescontoExtraParcelasValorDiferente()) / (numeroMes - obj.getPlano().getPlanoRecorrencia().getQuantidadeParcelasValorDiferente());
                    } else {
                        valorParcela = (valorContratoSemProduto * valorBaseadoEntrada) / numeroMes;
                    }
                } else {
                    valorContratoSemProduto = obj.getValorFinal() - obj.getSomaProduto() - obj.getValorProRata();
                    valorParcela = (valorContratoSemProduto * valorBaseadoEntrada) / numeroMes;
                }
                return Uteis.arredondarForcando2CasasDecimais(valorParcela);
            }
        } else if (obj.getDividirProdutosNasParcelas()) {
            if (obj.getGerarParcelaParaProdutos() && obj.getSomaAdesao() > 0) {
                return Uteis.arredondarForcando2CasasDecimais(((obj.getValorFinal() - obj.getSomaProduto()) * valorBaseadoEntrada));
            } else {
                return Uteis.arredondarForcando2CasasDecimais((obj.getValorFinal() * valorBaseadoEntrada));
            }
        } else {
            Double valorContratoSemProduto = obj.getValorFinal() - obj.getSomaProduto();
            valorContratoSemProduto = (valorContratoSemProduto * valorBaseadoEntrada);
            return Uteis.arredondarForcando2CasasDecimais(valorContratoSemProduto);
        }
    }

    public boolean isDeveGerarParcelasComValorDiferente() {
        return getPlano().getRecorrencia()
                && getPlano().getPlanoRecorrencia() != null
                && getPlano().getPlanoRecorrencia().getCodigo() > 0
                && getPlano().getPlanoRecorrencia().isGerarParcelasValorDiferente()
                && getPlano().getPlanoRecorrencia().getQuantidadeParcelasValorDiferente() > 0
                && (!isContratoRenovacao() || getPlano().getPlanoRecorrencia().isGerarParcelasValorDiferenteRenovacao());
    }

    public Double obterValorPrimeiraParcelas(ContratoVO obj, double valorParcelas) throws Exception {
        double totalProdutos  = getSomaProduto();
        if (obj.cobrarMatriculaSeparada){
            if (obj.getSituacaoContrato().equals("MA")) {
                totalProdutos = totalProdutos - obj.valorMatricula;
            } else if (obj.getSituacaoContrato().equals("RE")) {
                totalProdutos = totalProdutos - obj.getValorRematricula();
            }
        }
        if (obj.cobrarProdutoSeparado){
            totalProdutos = totalProdutos - obj.totalProdutosCobrarSeparado;
        }
        if (obj.getPlano().getRegimeRecorrencia() && obj.getGerarParcelaAnuidadeSeparada()) {
            totalProdutos = totalProdutos - obj.getSomaAnuidade();
        }

        // Gerar parcela com valor diferente das outras, para plano recorrentes
        // Definições da parcela no cadastro de plano, aba recorrência, campo "Cobrar anuidade junto com a parcela"
        if(isDeveGerarParcelasComValorDiferente()) {
            if (UteisValidacao.emptyList(getPlano().getPlanoRecorrencia().getParcelas())){
                Double valor = getFacade().getPlanorecorrenciaParcela().consultarValorPorNumeroParcela(1, obj.getPlano().getPlanoRecorrencia().getCodigo());
                if(valor != null){
                    return valor + totalProdutos;
                }
            } else {
                for (PlanoRecorrenciaParcelaVO p : getPlano().getPlanoRecorrencia().getParcelas()) {
                    if (p.getNumero() == 1) {
                        return p.getValorFinal() + totalProdutos;
                    }
                }
            }
        }

        double valorPrimeiraParcela = 0.0;
        double numeroMes = obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas() - 1;
        if (obj.getDividirProdutosNasParcelas()) {
            if (obj.getPlano().getRegimeRecorrencia() && obj.getGerarParcelaParaProdutos() && obj.getSomaAdesao() > 0) {
                if (obj.getGerarParcelaAnuidadeSeparada()) {
                    valorPrimeiraParcela = obj.getValorFinal() - (numeroMes * valorParcelas) - obj.getSomaAdesao() - obj.getSomaAnuidade();
                } else {
                    valorPrimeiraParcela = obj.getValorFinal() - (numeroMes * valorParcelas) - obj.getSomaAdesao();
                }
            } else {
                if (obj.getGerarParcelaAnuidadeSeparada()) {
                    valorPrimeiraParcela = obj.getValorFinal() - obj.getSomaAnuidade() - (numeroMes * valorParcelas);
                } else {
                    valorPrimeiraParcela = obj.getValorFinal() - (numeroMes * valorParcelas);
                }
            }

            return Uteis.arredondarForcando2CasasDecimais(valorPrimeiraParcela);

        } else {
            double valorSemProduto = obj.getValorFinal() - obj.getSomaProduto();
            if (obj.isDeveGerarParcelasComValorDiferente()) {
                valorSemProduto = valorSemProduto - ((numeroMes - obj.getPlano().getPlanoRecorrencia().getQuantidadeParcelasValorDiferente()) * valorParcelas) - obj.getPlano().getPlanoRecorrencia().getValorTotalParcelasValorDirefente() - obj.getDescontoExtraParcelasValorDiferente();
            } else {
                valorSemProduto = valorSemProduto - (numeroMes * valorParcelas);
            }


            // Quando é recorrencia coloca-se o valor do prorata gerado a mais na primeira parcela
            if (obj.getPlano().getRegimeRecorrencia() && obj.getGerarParcelaParaProdutos() && obj.getSomaAdesao() > 0) {
                 valorPrimeiraParcela = valorSemProduto + totalProdutos - obj.getSomaAdesao();
            } else {
                 valorPrimeiraParcela = valorSemProduto + totalProdutos;
            }
            return Uteis.arredondarForcando2CasasDecimais(valorPrimeiraParcela);
        }
    }

    public void obterNomeModalidadeCliente() {
        setNomeModalidades("");
        for (ContratoModalidadeVO mod : getContratoModalidadeVOs()) {
            if (mod.getModalidade().getModalidadeEscolhida()) {
                setNomeModalidades(getNomeModalidades() + mod.getModalidade().getNome().substring(0, 1));
            }
        }
    }
    public void concatenarNomesModalidades(){
        StringBuilder descricao = new StringBuilder();
        descricao.append("");
        for (ContratoModalidadeVO mod : getContratoModalidadeVOs()) {
            descricao.append(mod.getModalidade().getNome()).append("\n ");
        }
        setNomeModalidadesConcatenado(descricao.toString());
    }

    public List estornarContrato(ClienteVO cliente, UsuarioVO usuarioEstornoVO, Connection con) throws Exception {
        return estornarContrato(cliente, usuarioEstornoVO, null, con);
    }

    public List estornarContrato(ClienteVO cliente, UsuarioVO usuarioEstornoVO, String estornoObservacao, Connection con) throws Exception {
        Transacao transacaoDAO = null;
        RemessaItem remessaItemDAO = null;
        PeriodoAcessoCliente periodoAcessoClienteDAO = null;
        ContratoDuracaoCreditoTreino contratoDuracaoCreditoTreinoDAO = null;
        ControleCreditoTreino controleCreditoTreinoDAO = null;
        try {
            transacaoDAO = new Transacao(con);
            remessaItemDAO = new RemessaItem(con);
            periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);
            contratoDuracaoCreditoTreinoDAO = new ContratoDuracaoCreditoTreino(con);
            controleCreditoTreinoDAO = new ControleCreditoTreino(con);

            //LoginControle loginControle = (LoginControle) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("LoginControle");
            List<LogVO> listaLog = new ArrayList<LogVO>();
            LogVO log = new LogVO();
            montarLogDadosContrato(log, estornoObservacao, null);
            montarLogDadosCliente(log, cliente);
            montarLogDadosParcelas(log, con);
            transacaoDAO.gerarLogEstornarTransacoes(this.isPrecisaEstornarTransacoes(), this.getPessoa().getCodigo(), usuarioEstornoVO, "CONTRATO");
            transacaoDAO.estornarTransacoes(precisaEstornarTransacoes, log, getListaEstornoTransacoes(), getUsuarioVO(), true);
            remessaItemDAO.estornarItensRemessa(log, getListaItensRemessa(), true, usuarioEstornoVO);
            validarSeExisteReciboContrato(log, con);
            periodoAcessoClienteDAO.excluirPeriodoAcessoClienteContrato(getCodigo());
            removerMatriculaAlunoTurma(con);
            contratoDuracaoCreditoTreinoDAO.excluir(getCodigo());
            controleCreditoTreinoDAO.excluirPorCodigoContrato(getCodigo());
            listaLog.add(log);
            return listaLog;
        } finally {
            transacaoDAO = null;
            remessaItemDAO = null;
            periodoAcessoClienteDAO = null;
            contratoDuracaoCreditoTreinoDAO = null;
            controleCreditoTreinoDAO = null;
        }
    }

    public void montarLogAlteracaoTipo(LogVO obj, String valorAnterior, String valorNovo) throws Exception {
        obj.setChavePrimaria(getCodigo().toString());
        obj.setNomeEntidade("CONTRATO");
        obj.setNomeEntidadeDescricao("Contrato");
        obj.setOperacao("ALTERAÇÃO - TIPO CONTRATO");
        obj.setNomeCampo("contratoAgendadoEspontaneo");
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setValorCampoAnterior(valorAnterior);
        obj.setValorCampoAlterado(valorNovo);
    }
    public void montarLogDadosContrato(LogVO obj, String estornoObservacao, String logAdicional) throws Exception {
        obj.setChavePrimaria(getCodigo().toString());
        obj.setNomeEntidade("CONTRATO");
        obj.setNomeEntidadeDescricao("Contrato");
        obj.setOperacao("ESTORNO - CONTRATO");
        obj.setResponsavelAlteracao(getUsuarioVO().getNome());
        obj.setUserOAMD(getUsuarioVO().getUserOamd());
        obj.setNomeCampo("TODOS");
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado("--------------------------------------\n\r");

        StringBuilder campoAlterado = new StringBuilder();
        campoAlterado.append(obj.getValorCampoAlterado()).append(" \n\rCódigo do Contrato = ");
        campoAlterado.append(getCodigo()).append("\n\r").append("Valor Contrato = R$");
        campoAlterado.append(Uteis.getDoubleFormatado(getValorFinal())).append("\n\r").append("Empresa = ");
        campoAlterado.append(getEmpresa().getNome()).append("\n\r").append("Plano = ");
        campoAlterado.append(getPlano().getDescricao()).append( "\n\r").append("Responsável pelo Contrato = ");
        campoAlterado.append(getResponsavelContrato().getNome()).append("\n\r");
        if (!UteisValidacao.emptyString(estornoObservacao)){
            campoAlterado.append("Observação sobre o Estorno = ").append(estornoObservacao).append("\n\r");
        }
        if (getContratoRecorrenciaVO() != null && !UteisValidacao.emptyNumber(getContratoRecorrenciaVO().getCodigo()) && !UteisValidacao.emptyNumber(getContratoRecorrenciaVO().getDiaVencimentoCartao())){
            campoAlterado.append("Dia Vencimento = ").append(getContratoRecorrenciaVO().getDiaVencimentoCartao()).append("\n\r");
        }
        if (!UteisValidacao.emptyNumber(getDiaVencimentoProrata())){
            campoAlterado.append("Dia Pro-Rata = ").append(getDiaVencimentoProrata()).append("\n\r");
        }
        if(getContratoAgendadoEspontaneo()!=null){
            campoAlterado.append("Tipo Contrato = ").append(getContratoAgendadoEspontaneo().getDescricao()).append("\n\r");
        }
        if(dataLancamento!=null){
            campoAlterado.append("Data Lançamento = ").append(getDataLancamento_Apresentar()).append("\n\r");
        }
        if(!UteisValidacao.emptyNumber(getValorDescontoEspecifico()) || !UteisValidacao.emptyNumber(getValorDescontoPorcentagem())) {
            campoAlterado.append("Responsável Desconto Extra = ").append(getResponsavelAutorizacaoDesconto().getNome()).append("\n\r");
        }
        if(getPlano().getBolsa()) {
            campoAlterado.append("Responsável Autorização Bolsa = ").append(this.getResponsavelAutorizacaoBolsa().getNome()).append("\n\r");
        }
        if (!UteisValidacao.emptyNumber(getConvenioDesconto().getCodigo())) {
            campoAlterado.append("Responsável Convênio Desconto = ").append(getResponsavelAutorizacaoDescontoConvenio().getNome()).append("\n\r");
        }
        if (!UteisValidacao.emptyNumber(getResponsavelAutorizacaoDataPrimeiraParcela().getCodigo())) {
            campoAlterado.append("Responsável Data Primeira Parcela = ").append(getResponsavelAutorizacaoDataPrimeiraParcela().getNome()).append("\n\r");
        }
        if ((getOpcaoSelecionadaRenovacaoPlanoCredito() != null) && (!getOpcaoSelecionadaRenovacaoPlanoCredito().trim().equals(""))){
            campoAlterado.append("Opção selecionada ao renovar contrato de crédito para um contrato não crédito = ").append(getOpcaoSelecionadaRenovacaoPlanoCredito()).append("\n\r");
        }
        if (isAlterouDataInicioContrato()) {
            campoAlterado.append("Alterou a data de início do contrato para " + Calendario.getDataAplicandoFormatacao(getVigenciaDe(), "dd/MM/yyyy")).append("\n\r");
        }
        if ((logAdicional != null) && ((!logAdicional.trim().equals("")))){
            campoAlterado.append(logAdicional).append("\n\r");
        }
        obj.setValorCampoAlterado(campoAlterado.toString());
        Iterator i = getContratoModalidadeVOs().iterator();
        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "--------------------------------------\n\r");
        while (i.hasNext()) {
            ContratoModalidadeVO cm = (ContratoModalidadeVO) i.next();
            if (cm != null) {
                obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "Modalidade = " + cm.getModalidade().getNome() + " Nº Vezes Por Semana= " + cm.getNrVezesSemana() + "\n\r");
                if (cm.getModalidade().getUtilizarTurma() != null && cm.getModalidade().getUtilizarTurma()) {
                    Iterator j = cm.getContratoModalidadeTurmaVOs().iterator();
                    obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "--------------------------------------\n\r");
                    while (j.hasNext()) {
                        ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO) j.next();
                        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "Turma = " + cmt.getTurma().getIdentificador() + "\n\r");
                    }
                }
            }
        }
    }

    public void montarLogDadosCliente(LogVO obj, ClienteVO cliente) throws Exception {
        if (cliente == null) {
            return;
        }
        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "--------------------------------------\n\r");
        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + " \n\rCódigo do Cliente = " + cliente.getCodigo() + "\n\r" + "Matricula = " + cliente.getMatricula() + "\n\r" + "Nome do Cliente = " + cliente.getPessoa().getNome() + "\n\r");
        obj.setPessoa(cliente.getPessoa().getCodigo());
    }

    /**
     * Separar as transações de cartão de crédito associadas às parcelas
     * informadas como argumento, para que o estorno do contrato possa retirar
     * as dependências e manter o histórico das transações.
     *
     * @param listParc
     * @throws Exception
     */
    public void montarListaTransacoes(List<MovParcelaVO> listParc, Connection con) throws Exception {
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(con);
            this.getListaEstornoTransacoes().addAll(transacaoDAO.obterListaTransacoesEstorno(listParc));
        } finally {
            transacaoDAO = null;
        }
    }

    public void montarListaItensRemessa(List<MovParcelaVO> listParc, Connection con) throws Exception {
        RemessaItem remessaItemDAO;
        RemessaItemMovParcela remessaItemMovParcelaDAO;
        try {
            remessaItemDAO = new RemessaItem(con);
            remessaItemMovParcelaDAO = new RemessaItemMovParcela(con);

            List<RemessaItemVO> itens = new ArrayList();
            HashMap<String, RemessaItemVO> map = new HashMap<String, RemessaItemVO>();
            boolean remessaBoleto = false;
            for (MovParcelaVO parcela : listParc) {
                itens.addAll(remessaItemDAO.consultarPorCodigoParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                for (RemessaItemMovParcelaVO obj : remessaItemMovParcelaDAO.consultarPorParcelaItem(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS)) {
                    remessaBoleto = true;
                    map.put(obj.getRemessaItemVO().getCodigo().toString(), obj.getRemessaItemVO());

                }
            }
            setPossuiRemessaBoleto(remessaBoleto);
            itens.addAll(map.values());
            setListaItensRemessa(Ordenacao.ordenarLista(itens, "codigo"));
        } finally {
            remessaItemDAO = null;
            remessaItemMovParcelaDAO = null;
        }
    }

    public void montarLogDadosParcelas(LogVO obj, Connection con) throws Exception {
        if (!getListaEstornoRecibo().isEmpty()) {
            StringBuilder sbPagamentos = new StringBuilder(obj.getValorCampoAlterado()).append("\n\r");
            for (EstornoReciboVO estornoRecibo : getListaEstornoRecibo()) {
                for (MovPagamentoVO movPagamentoVO : estornoRecibo.getListaMovPagamento()) {
                    sbPagamentos.append("--------------------------------------\n\r");
                    sbPagamentos.append("Pagamento em = ").append(movPagamentoVO.getDataPagamento_Apresentar()).append(";\n\r");
                    sbPagamentos.append("Valor = ").append(movPagamentoVO.getValorTotal_Apresentar()).append(";\n\r");
                    sbPagamentos.append("Forma de Pagamento = ").append(movPagamentoVO.getFormaPagamento().getDescricao()).append(";\n\r");
                    if (movPagamentoVO.getNrParcelaCartaoCredito() > 0) {
                        sbPagamentos.append("N. Vezes Cartão Crédito = ").append(movPagamentoVO.getNrParcelaCartaoCredito()).append(";\n\r");
                    }
                    if (!UteisValidacao.emptyString(movPagamentoVO.getAutorizacaoCartao())) {
                        sbPagamentos.append("Autorização =  ").append(movPagamentoVO.getAutorizacaoCartao()).append(";\n\r");
                    }
                    int i = 1;
                    for (ChequeVO chequeVO : movPagamentoVO.getChequeVOs()) {
                        sbPagamentos.append(" Cheque ").append(i).append(": \n\r");
                        sbPagamentos.append("      N.: ").append(chequeVO.getNumero()).append("; ");
                        sbPagamentos.append("      Banco.: ").append(chequeVO.getBanco().getCodigo()).append(" - ").append(chequeVO.getBanco().getNome()).append("; ");
                        sbPagamentos.append("      Ag.: ").append(chequeVO.getAgencia()).append("; ");
                        sbPagamentos.append("      Conta.: ").append(chequeVO.getConta()).append("; ");
                        sbPagamentos.append("      Valor.: ").append(chequeVO.getValorTotal()).append("; ");
                        if (!UteisValidacao.emptyString(chequeVO.getNomeNoCheque())) {
                            sbPagamentos.append("      Nome no Cheque.: ").append(chequeVO.getNomeNoCheque()).append(";\n\r");
                        }
                        i++;
                    }

                    i = 1;
                    for (CartaoCreditoVO cartaoCreditoVO : movPagamentoVO.getCartaoCreditoVOs()) {
                        sbPagamentos.append(" Cartão Crédito ").append(i).append(": \n\r");
                        sbPagamentos.append("      Dt. Compensação: ").append(cartaoCreditoVO.getDataCompensacao_Apresentar()).append("; ");
                        sbPagamentos.append("      Valor.: ").append(cartaoCreditoVO.getValorTotal()).append(";");
                        sbPagamentos.append("      Operadora: ").append(cartaoCreditoVO.getOperadora().getDescricao()).append(";\n\r");
                        i++;
                    }
                }
            }
            obj.setValorCampoAlterado(sbPagamentos.toString());

            for (EstornoReciboVO estornoRecibo : getListaEstornoRecibo()) {
                Iterator j = estornoRecibo.getListaMovParcela().iterator();
                this.montarListaTransacoes(estornoRecibo.getListaMovParcela(), con);
                obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "\n\r --------------------------------------\n\r");
                while (j.hasNext()) {
                    MovParcelaVO parcela = (MovParcelaVO) j.next();
                    obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "código da parcela = " + parcela.getCodigo() + " \n\rdescrição parcela = " + parcela.getDescricao() + " \n\rnome do aluno = " + parcela.getContrato().getPessoa().getNome() + " \n\rvalor da parcela = R$ " + Uteis.getDoubleFormatado(parcela.getValorParcela()) + " \n\rnumero contrato = " + parcela.getContrato().getCodigo() + " \n\rsituação = " + parcela.getSituacao_Apresentar() + "\n\r");
                }
            }
        } else {
            Iterator i = getMovParcelaVOs().iterator();
            this.montarListaTransacoes(getMovParcelaVOs(), con);
            obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "\n\r --------------------------------------\n\r");
            while (i.hasNext()) {
                MovParcelaVO parcela = (MovParcelaVO) i.next();
                obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "código da parcela = " + parcela.getCodigo() + " \n\rdescrição parcela = " + parcela.getDescricao() + " \n\rnome do aluno = " + parcela.getContrato().getPessoa().getNome() + " \n\rvalor da parcela = R$ " + Uteis.getDoubleFormatado(parcela.getValorParcela()) + " \n\rnumero contrato = " + parcela.getContrato().getCodigo() + " \n\rsituação = " + parcela.getSituacao_Apresentar() + "\n\r");
            }
        }
    }

    public void validarSeExisteReciboContrato(LogVO obj, Connection con) throws Exception {
        ReciboPagamento reciboPagamentoDAO;
        Pix pixDAO;
        Boleto boletoDAO = null;
        try {
            reciboPagamentoDAO = new ReciboPagamento(con);
            pixDAO = new Pix(con);
            boletoDAO = new Boleto(con);

            if (!getListaEstornoRecibo().isEmpty()) {
                for (EstornoReciboVO estornoRecibo : getListaEstornoRecibo()) {
                    try {
                        ConciliadoraEstorno conciliadoraEstornoDAO = new ConciliadoraEstorno(getFacade().getReciboPagamento().getCon());
                        conciliadoraEstornoDAO.estornarConciliadora(estornoRecibo.getReciboPagamentoVO().getCodigo());
                        conciliadoraEstornoDAO = null;
                    } catch (Exception e) {
                        System.out.println("Erro ao estornar conciliadora | validarSeExisteReciboContrato");
                    }

                    reciboPagamentoDAO.estornarNotasFiscais(estornoRecibo, this);
                    reciboPagamentoDAO.atualizarConciliacaoEstornoPagamentos(estornoRecibo);
                    boletoDAO.estornarReciboPagamento(estornoRecibo.getReciboPagamentoVO().getCodigo(), getUsuarioVO(), "Estorno de Contrato");
                    obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "--------------------------------------\n\r");
                    obj.setValorCampoAlterado(obj.getValorCampoAlterado() + " \n\rcódigo do recibo = " + estornoRecibo.getReciboPagamentoVO().getCodigo() + "\n\r" + "data entrada no caixa = " + estornoRecibo.getReciboPagamentoVO().getData_Apresentar() + "\n\r" + "valor Total = R$ " + Uteis.getDoubleFormatado(estornoRecibo.getReciboPagamentoVO().getValorTotal()) + "\n\r" + "nome do Cliente = " + estornoRecibo.getReciboPagamentoVO().getNomePessoaPagador() + "\n\r" + "responsável lançamento = " + estornoRecibo.getReciboPagamentoVO().getResponsavelLancamento().getNome() + "\n\r" + "contrato= " + estornoRecibo.getReciboPagamentoVO().getContrato().getCodigo() + "\n\r");
                    estornoRecibo.alterarSituacaoMovParcela(con);
                    estornoRecibo.setResponsavelEstornoRecivo(getUsuarioVO());
                    EstornoReciboVO.validarSeExisteMovimentoContaCorrente(estornoRecibo, con);
                    estornoRecibo.excluirMovPagamento(con);
                    pixDAO.excluirPorReciboPagamento(estornoRecibo.getReciboPagamentoVO(), estornoRecibo.getResponsavelEstornoRecivo());
                    reciboPagamentoDAO.excluir(estornoRecibo.getReciboPagamentoVO());
                }
            }

        } finally {
            reciboPagamentoDAO = null;
            pixDAO = null;
            boletoDAO = null;
        }
    }

    public String getDataLancamento_Apresentar() {
        return Uteis.getDataComHora(dataLancamento);
    }

    public String getDataLancamento_Apresentar2() {
        return Uteis.getData(dataLancamento);
    }

    public Date getDataLancamento() {
        if (dataLancamento == null) {
            dataLancamento = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getSituacaoContrato() {
        if (situacaoContrato == null) {
            situacaoContrato = "MA";
        }
        return situacaoContrato;
    }

    public String getSituacaoContrato_Apresentar() {

        if (getSituacaoContrato().equals("")) {
            return situacaoContrato;
        }
        if (getSituacaoContrato().equals("MA")) {
            return "Matrícula";
        }
        if (getSituacaoContrato().equals("RE")) {
            return "Rematrícula";
        }
        if (getSituacaoContrato().equals("RN")) {
            return "Renovação";
        }
        if (getSituacaoContrato().equals("TF")) {
            return "Transferência";
        }
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public Date getDataMatricula() {
        if (dataMatricula == null) {
            dataMatricula = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return dataMatricula;
    }

    public void setDataMatricula(Date dataMatricula) {
        this.dataMatricula = dataMatricula;
    }

    public Date getDataPrevistaRenovar() {
        return dataPrevistaRenovar;
    }

    public String getDataPrevistaRenovar_Apresentar() {
        return Uteis.getData(dataPrevistaRenovar);
    }

    public void setDataPrevistaRenovar(Date dataPrevistaRenovar) {
        this.dataPrevistaRenovar = dataPrevistaRenovar;
    }

    public void setApresentarBotaoRenovarContrato(Boolean apresentarBotaoRenovarContrato) {
        this.apresentarBotaoRenovarContrato = apresentarBotaoRenovarContrato;
    }

    public Boolean getApresentarBotaoRenovarContrato() {
        if (apresentarBotaoRenovarContrato == null) {
            apresentarBotaoRenovarContrato = false;
        }
        return apresentarBotaoRenovarContrato;
    }

    public Date getDataRenovarRealizada() {
        return dataRenovarRealizada;
    }

    public String getDataRenovarRealizada_Apresentar() {
        return (Uteis.getData(dataRenovarRealizada));
    }

    public void setDataRenovarRealizada(Date dataRenovarRealizada) {
        this.dataRenovarRealizada = dataRenovarRealizada;
    }

    public String getSituacaoRenovacao() {
        if (situacaoRenovacao == null) {
            situacaoRenovacao = "";
        }
        return situacaoRenovacao;
    }

    public String getSituacaoRenovacao_Apresentar() {
        if (getSituacaoRenovacao().equals("")) {
            return "";
        }
        if (getSituacaoRenovacao().equals("AN")) {
            return "Antecipada";
        }
        if (getSituacaoRenovacao().equals("ND")) {
            return "No Dia";
        }
        if (getSituacaoRenovacao().equals("AV")) {
            return "Atrasada Vencida";
        }
        if (getSituacaoRenovacao().equals("AD")) {
            return "Atrasada Desistente";
        }
        return situacaoRenovacao;
    }

    public void setSituacaoRenovacao(String situacaoRenovacao) {
        this.situacaoRenovacao = situacaoRenovacao;
    }

    public Integer getContratoBaseadoRenovacao() {
        if (contratoBaseadoRenovacao == null) {
            contratoBaseadoRenovacao = 0;
        }
        return contratoBaseadoRenovacao;
    }

    /**
     * Seta o Contrato anterior que foi renovado por este.
     *
     * @param contratoBaseadoRenovacao
     */
    public void setContratoBaseadoRenovacao(Integer contratoBaseadoRenovacao) {
        this.contratoBaseadoRenovacao = contratoBaseadoRenovacao;
    }

    public Date getDataPrevistaRematricula() {
        return dataPrevistaRematricula;
    }

    public void setDataPrevistaRematricula(Date dataPrevistaRematricula) {
        this.dataPrevistaRematricula = dataPrevistaRematricula;
    }

    public String getDataRematriculaRealizada_Apresentar() {
        return (Uteis.getData(dataRematriculaRealizada));
    }

    public Date getDataRematriculaRealizada() {
        return dataRematriculaRealizada;
    }

    public void setDataRematriculaRealizada(Date dataRematriculaRealizada) {
        this.dataRematriculaRealizada = dataRematriculaRealizada;
    }

    public String getSituacaoRematricula_Apresentar() {
        if (situacaoRematricula.equals("DE")) {
            return "Desistente";
        }
        if (situacaoRematricula.equals("CA")) {
            return "Cancelado";
        }
        return situacaoRematricula;
    }

    public String getSituacaoRematricula() {
        if (situacaoRematricula == null) {
            situacaoRematricula = "";
        }
        return situacaoRematricula;
    }

    public void setSituacaoRematricula(String situacaoRematricula) {
        this.situacaoRematricula = situacaoRematricula;
    }

    public Integer getContratoBaseadoRematricula() {
        if (contratoBaseadoRematricula == null) {
            contratoBaseadoRematricula = 0;
        }
        return contratoBaseadoRematricula;
    }

    /**
     * Seta o contrato anterior que foi rematriculado por este
     *
     * @param contratoBaseadoRematricula
     */
    public void setContratoBaseadoRematricula(Integer contratoBaseadoRematricula) {
        this.contratoBaseadoRematricula = contratoBaseadoRematricula;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        if (configuracaoSistema == null) {
            configuracaoSistema = new ConfiguracaoSistemaVO();
        }
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public Boolean getRematricularContrato() {
        if (rematricularContrato == null) {
            rematricularContrato = false;
        }
        return rematricularContrato;
    }

    public void setRematricularContrato(Boolean rematricularContrato) {
        this.rematricularContrato = rematricularContrato;
    }

    public List getContratoComposicaoVOs() {
        if (contratoComposicaoVOs == null) {
            contratoComposicaoVOs = new ArrayList();
        }
        return contratoComposicaoVOs;
    }

    public void setContratoComposicaoVOs(List contratoComposicaoVOs) {
        this.contratoComposicaoVOs = contratoComposicaoVOs;
    }

    public ContratoDuracaoVO getContratoDuracao() {
        if (contratoDuracao == null) {
            contratoDuracao = new ContratoDuracaoVO();
        }
        return contratoDuracao;
    }

    public void setContratoDuracao(ContratoDuracaoVO contratoDuracao) {
        this.contratoDuracao = contratoDuracao;
    }

    public Integer getContratoResponsavelRenovacaoMatricula() {
        if (contratoResponsavelRenovacaoMatricula == null) {
            contratoResponsavelRenovacaoMatricula = 0;
        }
        return contratoResponsavelRenovacaoMatricula;
    }

    public void setContratoResponsavelRenovacaoMatricula(Integer contratoResponsavelRenovacaoMatricula) {
        this.contratoResponsavelRenovacaoMatricula = contratoResponsavelRenovacaoMatricula;
    }

    public Integer getContratoResponsavelRematriculaMatricula() {
        if (contratoResponsavelRematriculaMatricula == null) {
            contratoResponsavelRematriculaMatricula = 0;
        }
        return contratoResponsavelRematriculaMatricula;
    }

    public String getNomeModalidadesConcatenado() {
        if(nomeModalidadesConcatenado == null)
            concatenarNomesModalidades();
        return nomeModalidadesConcatenado;
    }

    public void setNomeModalidadesConcatenado(String nomeModalidadesConcatenado) {
        this.nomeModalidadesConcatenado = nomeModalidadesConcatenado;
    }

    public void setContratoResponsavelRematriculaMatricula(Integer contratoResponsavelRematriculaMatricula) {
        this.contratoResponsavelRematriculaMatricula = contratoResponsavelRematriculaMatricula;
    }

    public Boolean getApresentarSituacaoContrato() {
        return (getRenovarContrato() || getRematricularContrato());
    }

    public Boolean getApresentarDataInicio() {
        return (isContratoMatricula() || isContratoRematricula());
    }

    public Boolean getApresentarDiasAtrasoRenovacao() {
        return (getDiasAtrasoRenovacao() > 0);
    }

    public Integer getDiasAtrasoRenovacao() {
        Long nrDias = 0L;
        if (getSituacaoContrato().equals("RN")) {
            nrDias = Uteis.nrDiasEntreDatas(getDataPrevistaRenovarAntecipado(), getDataRenovarRealizada());
        }
        return nrDias.intValue();
    }

    public int getDiasAntecipadoRenovacao() {
        Long nrDias = 0L;
        if (getSituacaoContrato().equals("RN")) {
            nrDias = Uteis.nrDiasEntreDatas(getDataRenovarRealizada(), getDataPrevistaRenovar());
        }
        return nrDias.intValue();
    }

    public Boolean isContratoAtestado() {
        return (this.getSituacao().equals("AE"));
    }

    public Boolean isContratoCarencia() {
        return (this.getSituacao().equals("CR"));
    }

    public Boolean isContratoFerias() {
        return (this.getSituacao().equals("FE"));
    }

    public Boolean isContratoTrancado() {
        return (this.getSituacao().equals("TR"));
    }

    public Boolean isContratoCongelado() {
        return (this.getSituacao().equals("CO"));
    }

    public Boolean getContratoAtivo() {
        return (this.getSituacao().equals("AT"));
    }

    public Boolean getContratoCancelado() {
        return (this.getSituacao().equals("CA"));
    }

    public Boolean getContratoInativo() {
        return (this.getSituacao().equals("IN"));
    }

    public Boolean getContratoTrancado() {
        return (this.getSituacao().equals("TR"));
    }

    public Boolean isContratoMatricula() {
        return (this.getSituacaoContrato().equals("MA"));
    }

    public Boolean isContratoTransferencia() {
        return (this.getSituacaoContrato().equals("TF"));
    }

    public Boolean isVendaContrato() {
        return vendaContrato;
    }

    public void setVendaContrato(Boolean vendaContrato){
        this.vendaContrato = vendaContrato;
    }

    public Boolean isContratoRenovacao() {
        return (this.getSituacaoContrato().equals("RN"));
    }

    public Boolean isContratoRematricula() {
        return (this.getSituacaoContrato().equals("RE"));
    }

    public Boolean isPlanoNoPeriodoDeRenovacaoRematricula() throws Exception {
        Date dataInicioVigencia = Uteis.getDataJDBC(getVigenciaDe());
        return (dataInicioVigencia.compareTo(this.getPlano().getVigenciaAte()) <= 0);
    }

    public Boolean isPlanoNoPeriodoDeIngresso() throws Exception {
        Date dataInicioVigencia = Uteis.getDataJDBC(getVigenciaDe());
        return (dataInicioVigencia.compareTo(this.getPlano().getIngressoAte()) <= 0);
    }

    public Boolean validarExisteModalidadeEscolhida(List listaComposicaoModalidade) {
        for (Object aListaComposicaoModalidade : listaComposicaoModalidade) {
            ComposicaoModalidadeVO composicaoModalidade = (ComposicaoModalidadeVO) aListaComposicaoModalidade;
            for (ContratoModalidadeVO obj : getContratoModalidadeVOs()) {
                if (!composicaoModalidade.getModalidade().getCodigo().equals(obj.getModalidade().getCodigo().intValue()) && obj.getModalidade().getModalidadeEscolhida()) {
                    return true;
                }
            }
        }
        return false;
    }

    public Boolean validarComposicaoCompleta(List listaComposicaoModalidade) {
        for (Object aListaComposicaoModalidade : listaComposicaoModalidade) {
            ComposicaoModalidadeVO composicaoModalidade = (ComposicaoModalidadeVO) aListaComposicaoModalidade;
            boolean modalidadeExiste = false;
            for (ContratoModalidadeVO obj : getContratoModalidadeVOs()) {
                if (composicaoModalidade.getModalidade().getCodigo().intValue() == obj.getModalidade().getCodigo()) {
                    if (!obj.getModalidade().getModalidadeEscolhida()) {
                        return false;
                    }
                    modalidadeExiste = true;
                }
            }
            if (!modalidadeExiste) {
                return false;
            }
        }
        return true;
    }

    public List<PeriodoAcessoClienteVO> getPeriodoAcessoClienteVOs() {
        if (periodoAcessoClienteVOs == null) {
            periodoAcessoClienteVOs = new ArrayList();
        }
        return periodoAcessoClienteVOs;
    }

    public void setPeriodoAcessoClienteVOs(List periodoAcessoClienteVOs) {
        this.periodoAcessoClienteVOs = periodoAcessoClienteVOs;
    }

    public ContratoTextoPadraoVO getContratoTextoPadrao() {
        if (contratoTextoPadrao == null) {
            contratoTextoPadrao = new ContratoTextoPadraoVO();
        }
        return contratoTextoPadrao;
    }

    public void setContratoTextoPadrao(ContratoTextoPadraoVO contratoTextoPadrao) {
        this.contratoTextoPadrao = contratoTextoPadrao;
    }

    public Double getValorBaseCalculoAlteracao() {
        if (valorBaseCalculoAlteracao == null) {
            valorBaseCalculoAlteracao = (double) 0;
        }
        return valorBaseCalculoAlteracao;
    }

    public void setValorBaseCalculoAlteracao(Double valorBaseCalculoAlteracao) {
        this.valorBaseCalculoAlteracao = valorBaseCalculoAlteracao;
    }

    public String getNomeModalidades() {
        if (UteisValidacao.emptyString(nomeModalidades)) {
            nomeModalidades = "";
        }
        return nomeModalidades;
    }

    public void setNomeModalidades(String nomeModalidades) {
        this.nomeModalidades = nomeModalidades;
    }

    public String getBolsa_Apresentar() {
        return getBolsa() ? "Sim" : "Não";
    }

    public Boolean getBolsa() {
        if (bolsa == null) {
            bolsa = false;
        }
        return bolsa;
    }

    public void setBolsa(Boolean bolsa) {
        this.bolsa = bolsa;
    }

    public String getSituacaoClienteContratoComitantes() {
        if (situacaoClienteContratoComitantes == null) {
            situacaoClienteContratoComitantes = "";
        }
        return situacaoClienteContratoComitantes;
    }

    public void setSituacaoClienteContratoComitantes(String situacaoClienteContratoComitantes) {
        this.situacaoClienteContratoComitantes = situacaoClienteContratoComitantes;
    }

    public Boolean getNaoPermitirRenovacaoRematriculaDeContratoAnteriores() {
        if (naoPermitirRenovacaoRematriculaDeContratoAnteriores == null) {
            naoPermitirRenovacaoRematriculaDeContratoAnteriores = false;
        }
        return naoPermitirRenovacaoRematriculaDeContratoAnteriores;
    }

    public void setNaoPermitirRenovacaoRematriculaDeContratoAnteriores(Boolean naoPermitirRenovacaoRematriculaDeContratoAnteriores) {
        this.naoPermitirRenovacaoRematriculaDeContratoAnteriores = naoPermitirRenovacaoRematriculaDeContratoAnteriores;
    }

    public Double getValorTemporarioDescontoPorcentagem() {
        if (valorTemporarioDescontoPorcentagem == null) {
            valorTemporarioDescontoPorcentagem = (double) 0;
        }
        return valorTemporarioDescontoPorcentagem;
    }

    public void setValorTemporarioDescontoPorcentagem(Double valorTemporarioDescontoPorcentagem) {
        this.valorTemporarioDescontoPorcentagem = valorTemporarioDescontoPorcentagem;
    }

    public List<EstornoReciboVO> getListaEstornoRecibo() {
        if (listaEstornoRecibo == null) {
            listaEstornoRecibo = new ArrayList<EstornoReciboVO>();
        }
        return listaEstornoRecibo;
    }

    public void setListaEstornoRecibo(List<EstornoReciboVO> listaEstornoRecibo) {
        this.listaEstornoRecibo = listaEstornoRecibo;
    }

    public double getValorContrato() {
        if (valorContrato == 0.0) {
            valorContrato = (double) 0;
        }
        return valorContrato;
    }

    public String getValorContratoNovo() {
        return Uteis.formatarValorEmReal(getValorContrato());
    }

    public void setValorContrato(double valorContrato) {
        this.valorContrato = valorContrato;
    }

    public ColaboradorVO getConsultor() {
        if (consultor == null) {
            consultor = new ColaboradorVO();
        }
        return consultor;
    }

    public void setConsultor(ColaboradorVO consultor) {
        this.consultor = consultor;
    }

    public void setDuracaoRenovacao(int duracaoRenovacao) {
        this.duracaoRenovacao = duracaoRenovacao;
    }

    public int getDuracaoRenovacao() {
        return duracaoRenovacao;
    }

    public int getDiaVencimentoProrata() {
        return diaVencimentoProrata;
    }

    public void setDiaVencimentoProrata(int diaVencimentoProrata) {
        this.diaVencimentoProrata = diaVencimentoProrata;
    }

    public String getDescricaoResumida() {
        return getCodigo() + " - " + getSituacao() + " - " + getPlano().getDescricao() + " - " + getNomeModalidades();
    }

    public Date getDataPrimeiraParcela() {
        return dataPrimeiraParcela;
    }

    public void setDataPrimeiraParcela(Date dataPrimeiraParcela) {
        this.dataPrimeiraParcela = dataPrimeiraParcela;
    }

    public boolean existeContratoSucessorIniciadoNestaData(Date dataInicio) throws Exception {
        int cod = this.getContratoResponsavelRenovacaoMatricula();
        return cod != 0 && getFacade().getContrato().existeOutroContratoComecandoNaData(cod, dataInicio);
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public double getValorTemporarioDescontoAntecipado() {
        return valorTemporarioDescontoAntecipado;
    }

    public void setValorTemporarioDescontoAntecipado(double valorTemporarioDescontoAntecipado) {
        this.valorTemporarioDescontoAntecipado = valorTemporarioDescontoAntecipado;
    }

    /**
     * Retorna 3 possíveis valores, vide retornos; O botão "Retorno-XXX" será
     * visualizado somente se todos os casos abaixo forem verdadeiros: i.	O
     * campo "retornoManual" é igual a "false". ii.	Se a data atual está entre a
     * data início e fim da operação de origem iii.	O retorno não pode ser no
     * mesmo dia da operação de origem iv. Não pode haver uma outra operação
     * após este retorno automático
     *
     * @param contrato
     * @param operacaoOriginal (AT, CR, TR, etc.)
     * @param operacaoRetorno (RA, RC, RT, etc.)
     * @return <i>1</i> se existe uma operacao de origem e está dentro do prazo
     * para retorno manual
     * @return <i>0</i> se o retorno manual foi dado, ou não está dentro do
     * período para retorno manual
     * @return <i>-1</i>se não existe nenhuma operação deste tipo pendente
     * <AUTHOR> Maciel
     */
    public int verificarSePermiteOperacaoERetorno(ContratoVO contrato,
            String operacaoOriginal, String operacaoRetorno, Connection con) throws Exception {

        ContratoOperacao contratoOperacaoDAO = null;
        HistoricoContrato historicoContratoDAO = null;
        try {
            contratoOperacaoDAO = new ContratoOperacao(con);
            historicoContratoDAO = new HistoricoContrato(con);

            List listaOperacao = contratoOperacaoDAO.consultarPorContrato(
                    contrato.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            for (Object aListaOperacao : listaOperacao) {
                ContratoOperacaoVO obj = (ContratoOperacaoVO) aListaOperacao;
                if (obj.getTipoOperacao().equals(operacaoOriginal)) {

                    if (Calendario.igual(obj.getDataInicioEfetivacaoOperacao(),
                            Calendario.hoje())) {//iii. O retorno não pode ser no mesmo dia da operação de origem
                        return 0;
                    }

                    HistoricoContratoVO operacao
                            = historicoContratoDAO.
                            obterUltimoHistoricoContratoPorContratoTipoHistorico(
                                    contrato.getCodigo(),
                                    operacaoOriginal,
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    HistoricoContratoVO retornoPrevisto
                            = historicoContratoDAO.
                            obterUltimoHistoricoContratoPorContratoTipoHistorico(
                                    contrato.getCodigo(),
                                    operacaoRetorno,
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    /**
                     * WM. 12/09/2011 - Se a data final do retorno previsto é menor
                     * do que a data de inicio da situacao, significa que esse
                     * retorno previsto não é o equivalente a operação que está
                     * sendo tratada. Deve-se então, descartá-lo.
                     */
                    if (retornoPrevisto != null && operacao != null) {
                        if (Calendario.menor(retornoPrevisto.getDataFinalSituacao(), operacao.getDataInicioSituacao())) {
                            retornoPrevisto = null;
                        }
                    }
                    //trancamento não possui retorno previsto, o retorno é dado no momento do retorno do aluno à academia
                    if (operacao != null && retornoPrevisto != null && !operacaoOriginal.equals("TR")) {//em operações futuras/passado retorno pode não existir
                        if ((!retornoPrevisto.isRetornoManual())//Se ainda nao houve retorno manual.(i)
                                && (Calendario.entre(Calendario.hoje(),//Se ainda está na operação de origem(atestado,carencia,trancamento),
                                operacao.getDataInicioSituacao(),//caso contrário já pode ter entrado
                                operacao.getDataFinalSituacao()) || (Calendario.igual(Calendario.hoje(), operacao.getDataFinalSituacao())
                                || Calendario.igual(Calendario.hoje(), operacao.getDataInicioSituacao())))//no período de retorno automático e não mais pode permitir o retorno manual.(ii)
                                && !contrato.isExisteOutraOperacaoAposEstaData(
                                retornoPrevisto.getDataInicioSituacao(), con)) {//iv. não pode haver uma outra operação após este retorno automático

                            return 1;//retorno manual ainda é permitido

                        } else {
                            return 0;//retorno manual já foi dado ou passou da época
                        }
                    } else if (operacao != null && retornoPrevisto == null) {//se existe so a operacao de origem e ainda não tem retorno previsto,
                        //permite lançar o retorno desde que esteja dentro do período da operação original.
                        if (Calendario.entre(Calendario.getDataComHoraZerada(Calendario.hoje()),
                                operacao.getDataInicioSituacao(),
                                operacao.getDataFinalSituacao())
                                || Calendario.igual(operacao.getDataFinalSituacao(), Calendario.hoje())) {
                            return 1;//retorno manual ainda é permitido
                        } else {
                            return 0;//retorno manual já foi dado ou passou da época
                        }
                    }
                }
            }
            return -1;//não existe operação desse tipo para ser verificado
        } finally {
            contratoOperacaoDAO = null;
            historicoContratoDAO = null;
        }
    }

    /**
     * Verifica se este contrato possui uma operação "Trancamento Vencido" na
     * data atual (hoje)
     *
     * @return estaEmTrancamentoVencido
     * @throws Exception
     */
    public boolean estaEmTrancamentoVencido() throws Exception {
        List listaOperacao = getFacade().getContratoOperacao().consultarPorContrato(
                this.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
        for (Object aListaOperacao : listaOperacao) {
            ContratoOperacaoVO obj = (ContratoOperacaoVO) aListaOperacao;
            if (obj.getTipoOperacao().equals("TV")) {
                if (Calendario.maiorOuIgual(Calendario.hoje(), obj.getDataInicioEfetivacaoOperacao())
                        && Calendario.menorOuIgual(Calendario.hoje(), obj.getDataFimEfetivacaoOperacao())) {

                    return true;

                }

            }
        }
        return false;
    }

    public boolean isVigente() {
        if (getVigenciaDe() != null && getVigenciaAteAjustada() != null) {
            return (Calendario.entre(Calendario.hoje(), getVigenciaDe(), getVigenciaAteAjustada())
                    || Calendario.igual(Calendario.hoje(), getVigenciaAteAjustada()));
        }
        return false;
    }

    public String getStyleContratoVigenteOuNaoVigente() {
        return isVigente() ? "blue" : "gray";
    }

    public String getStyleContratoVigenteOuNaoVigenteDataTermino() {
        return isVigente() ? "red" : "gray";
    }

    public boolean isExisteOutraOperacaoAposEstaData(final Date dataBase, Connection con) throws Exception {
        return Contrato.isExisteOutraOperacaoAposEstaData(dataBase, this.getCodigo(), con);
    }

    public void atualizarHistoricosContratoAnteriorRenovado() throws Exception {
        atualizarHistoricosContratoAnteriorRenovado(getFacade().getHistoricoContrato());
    }

    /**
     * Atualiza datas dos históricos de contrato do contrato anterior que foi
     * renovado Situacoes de <i>A Vencer</i> e <i>Vencido</i>, devem ter suas
     * datas finais atualizadas um dia antes do lançamento do novo contrato
     *
     * <AUTHOR> Maciel
     * @date 26/04/2011
     *
     */
    public void atualizarHistoricosContratoAnteriorRenovado(HistoricoContratoInterfaceFacade historicoContratoDao) throws Exception {
        if (getContratoBaseadoRenovacao() != 0) {
            HistoricoContratoVO hist = historicoContratoDao.obterUltimoHistoricoContratoPorContratoTipoHistorico(
                    getContratoBaseadoRenovacao(), "VE", Uteis.NIVELMONTARDADOS_TODOS);
            if (hist != null) {//atualiza o registro de vencido
                hist.setDataFinalSituacao(Uteis.obterDataAnterior(this.getDataLancamento(),
                        1));
                //se a data final ficar menor que a data inicial, deixa iguais a data inicio.
                if (Calendario.menor(hist.getDataFinalSituacao(), hist.getDataInicioSituacao())) {
                    hist.setDataFinalSituacao(hist.getDataInicioSituacao());
                }
                historicoContratoDao.alterar(hist, false);
            }/* else {//ou se houver, atualiza o registro de A Vencer
             hist = getFacade().getHistoricoContrato().
             obterUltimoHistoricoContratoPorContratoTipoHistorico(
             getContratoBaseadoRenovacao(), "AV", Uteis.NIVELMONTARDADOS_TODOS);
             if (hist != null) {
             hist.setDataFinalSituacao(Uteis.obterDataAnterior(this.getDataLancamento(),
             1));
             //se a data final ficar menor que a data inicial, deixa iguais a data inicio.
             if (Calendario.menor(hist.getDataFinalSituacao(), hist.getDataInicioSituacao())) {
             hist.setDataFinalSituacao(hist.getDataInicioSituacao());
             }
             getFacade().getHistoricoContrato().alterar(hist, false);
             }

             }*/

        }

    }

    /**
     * Atualiza datas dos históricos de contrato do contrato anterior que foi
     * renatriculado Situação de <i>Desistente</i>, deve ter sua data final
     * atualizada um dia antes do lançamento do novo contrato
     *
     * <AUTHOR> Maciel
     * @date 26/04/2011
     *
     */
    public void atualizarHistoricosContratoAnteriorRematriculado() throws Exception {
        if (getContratoBaseadoRematricula() != 0) {
            HistoricoContratoVO hist = getFacade().getHistoricoContrato().
                    obterUltimoHistoricoContratoPorContratoTipoHistorico(
                            getContratoBaseadoRematricula(), "DE", Uteis.NIVELMONTARDADOS_TODOS);
            if (hist != null) {//atualiza o registro de vencido
                hist.setDataFinalSituacao(Uteis.obterDataAnterior(this.getDataLancamento(),
                        1));
                //se a data final ficar menor que a data inicial, deixa iguais a data inicio.
                if (Calendario.menor(hist.getDataFinalSituacao(), hist.getDataInicioSituacao())) {
                    hist.setDataFinalSituacao(hist.getDataInicioSituacao());
                }
                getFacade().getHistoricoContrato().alterar(hist, false);
            }
        }

    }

    public String getSituacaoOrdenar() {
        String data = Uteis.getDataAplicandoFormatacao(getVigenciaDe(),"yyyy/MM/dd");
        int numeroOdenar = 0;
        if (situacao.equals("AT")) {
            numeroOdenar =  1 ;
        } else
        if (situacao.equals("AE")) {
            numeroOdenar =  2;
        } else
        if (situacao.equals("CR")) {
            numeroOdenar =  3;
        } else
        if (situacao.equals("CA")) {
            numeroOdenar =  4;
        } else
        if (situacao.equals("FE")) {
            numeroOdenar =  5;
        } else
        if (situacao.equals("TR")) {
            numeroOdenar =  6;
        } else
        if (situacao.equals("CO")) {
            numeroOdenar =  7;
        } else
        if (situacao.equals("IN")) {
            numeroOdenar =  8;
        }
        return numeroOdenar + "/"+data;
    }

    public Date getDataAlteracaoManual() {
        return dataAlteracaoManual;
    }

    public void setDataAlteracaoManual(Date dataAlteracaoManual) {
        this.dataAlteracaoManual = dataAlteracaoManual;
    }

    public UsuarioVO getResponsavelDataBase() {
        return responsavelDataBase;
    }

    public void setResponsavelDataBase(UsuarioVO responsavelDataBase) {
        this.responsavelDataBase = responsavelDataBase;
    }

    public String getDataAlteracaoManual_Apresentar() {
        return Uteis.getData(dataAlteracaoManual);
    }

    public int getDiaVencimentoCartaoRecorrencia() {
        return diaVencimentoCartaoRecorrencia;
    }

    public void setDiaVencimentoCartaoRecorrencia(int diaVencimentoCartaoRecorrencia) {
        this.diaVencimentoCartaoRecorrencia = diaVencimentoCartaoRecorrencia;
    }

    public ContratoRecorrenciaVO getContratoRecorrenciaVO() {
        if (contratoRecorrenciaVO == null) {
            contratoRecorrenciaVO = new ContratoRecorrenciaVO();
        }
        return contratoRecorrenciaVO;
    }

    public void setContratoRecorrenciaVO(ContratoRecorrenciaVO contratoRecorrenciaVO) {
        this.contratoRecorrenciaVO = contratoRecorrenciaVO;
    }

    public String getDescricaoModalidade() {
        return descricaoModalidade;
    }

    public void setDescricaoModalidade(String descricaoModalidade) {
        this.descricaoModalidade = descricaoModalidade;
    }

    public Boolean getRegimeRecorrencia() {
        return regimeRecorrencia;
    }

    public Boolean isRegimeRecorrencia(){
        if (regimeRecorrencia == null){
            regimeRecorrencia = false;
        }
        return regimeRecorrencia;
    }

    public void setRegimeRecorrencia(Boolean regimeRecorrencia) {
        this.regimeRecorrencia = regimeRecorrencia;
    }

    public List<TransacaoVO> getListaEstornoTransacoes() {
        if (listaEstornoTransacoes == null) {
            listaEstornoTransacoes = new ArrayList<>();
        }
        return listaEstornoTransacoes;
    }

    public void setListaEstornoTransacoes(List<TransacaoVO> listaEstornoTransacoes) {
        this.listaEstornoTransacoes = listaEstornoTransacoes;
    }

    public void setContratoAgendadoEspontaneo(TipoContratoEnum contratoAgendadoEspontaneo) {
        this.contratoAgendadoEspontaneo = contratoAgendadoEspontaneo;
    }

    public TipoContratoEnum getContratoAgendadoEspontaneo() {
        return contratoAgendadoEspontaneo;
    }

    public Boolean getContratoAgendado() {
        if (getContratoAgendadoEspontaneo() == null) {
            return Boolean.FALSE;
        } else {
            return getContratoAgendadoEspontaneo().equals(TipoContratoEnum.AGENDADO);
        }
    }

    public void setAgendamentoOrigem(AgendaVO agendamentoOrigem) {
        this.agendamentoOrigem = agendamentoOrigem;
    }

    public AgendaVO getAgendamentoOrigem() {
        if (agendamentoOrigem == null) {
            agendamentoOrigem = new AgendaVO();
        }
        return agendamentoOrigem;
    }

    public boolean isForcarDatasParcelasDiferentes() {
        return forcarDatasParcelasDiferentes;
    }

    public void setForcarDatasParcelasDiferentes(boolean forcarDatasParcelasDiferentes) {
        this.forcarDatasParcelasDiferentes = forcarDatasParcelasDiferentes;
    }

    public List<RemessaItemVO> getListaItensRemessa() {
        return listaItensRemessa;
    }

    public void setListaItensRemessa(List<RemessaItemVO> listaItensRemessa) {
        this.listaItensRemessa = listaItensRemessa;
    }

    public boolean isPrecisaEstornarTransacoes() {
        return precisaEstornarTransacoes;
    }

    public void setPrecisaEstornarTransacoes(boolean precisaEstornarTransacoes) {
        this.precisaEstornarTransacoes = precisaEstornarTransacoes;
    }

    public ConvenioDescontoConfiguracaoVO getConvenioDescontoConfiguracaoVO() {
        return convenioDescontoConfiguracaoVO;
    }

    public void setConvenioDescontoConfiguracaoVO(ConvenioDescontoConfiguracaoVO convenioDescontoConfiguracaoVO) {
        this.convenioDescontoConfiguracaoVO = convenioDescontoConfiguracaoVO;
    }

    public boolean getImportacao() {
        return importacao;
    }

    public void setImportacao(boolean importacao) {
        this.importacao = importacao;
    }

    public Boolean getTentativaDuplicacao() {
        return tentativaDuplicacao;
    }

    public void setTentativaDuplicacao(Boolean tentativaDuplicacao) {
        this.tentativaDuplicacao = tentativaDuplicacao;
    }

    public Boolean getGerarParcelaParaProdutos() {
        return gerarParcelaParaProdutos;
    }

    public void setGerarParcelaParaProdutos(Boolean gerarParcelaParaProdutos) {
        this.gerarParcelaParaProdutos = gerarParcelaParaProdutos;
    }

    public String getSomaAdesaoApresentar(){
        return Uteis.formatarValorEmReal(somaAdesao);
    }

    public Double getSomaAdesao() {
        if (somaAdesao == null) {
            somaAdesao = 0.0;
        }
        return somaAdesao;
    }

    public void setSomaAdesao(Double somaAdesao) {
        this.somaAdesao = somaAdesao;
    }

    public Integer getNrParcelasAdesao() {
        if (nrParcelasAdesao == null) {
            nrParcelasAdesao = 1;
        }
        return nrParcelasAdesao;
    }

    public void setNrParcelasAdesao(Integer nrParcelasAdesao) {
        this.nrParcelasAdesao = nrParcelasAdesao;
    }

    public Double getValorParcelasAdesao() {
        if (UteisValidacao.emptyNumber(nrParcelasAdesao) || nrParcelasAdesao == 1) {
            return getSomaAdesao();
        } else {
            return getSomaAdesao() / getNrParcelasAdesao();
        }
    }

    public Double getValorParcelasMatricula() {
        if ((nrVezesParcelarMatricula == null) || (nrVezesParcelarMatricula <=1)) {
            return getValorMatricula();
        } else {
            return getValorMatricula() / nrVezesParcelarMatricula;
        }
    }

    public Double getValorParcelasRematricula() {
        if ((nrVezesParcelarMatricula == null) || (nrVezesParcelarMatricula <=1)) {
            return getValorRematricula();
        } else {
            return getValorRematricula() / nrVezesParcelarMatricula;
        }
    }

    public Double getValorParcelasProduto() {
        if ((nrVezesParcelarProduto == null) || (nrVezesParcelarProduto <=1)) {
            return getTotalProdutosCobrarSeparado();
        } else {
            return getTotalProdutosCobrarSeparado() / nrVezesParcelarProduto;
        }
    }

    public Double getValorProRata() {
        if (valorProRata == null) {
            valorProRata = 0.0;
        }
        return valorProRata;
    }

    public void setValorProRata(Double valorProRata) {
        this.valorProRata = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorProRata);
    }

    public Double getValorArredondamento() {
        if(valorArredondamento == null){
            valorArredondamento = 0.0;
        }
        return valorArredondamento;
    }

    public void setValorArredondamento(Double valorArredondamento) {
        this.valorArredondamento = valorArredondamento;
    }

    public String getDuracaoContratoApresentar() {
        return duracaoContratoApresentar;
    }

    public void setDuracaoContratoApresentar(String duracaoContratoApresentar) {
        this.duracaoContratoApresentar = duracaoContratoApresentar;
    }

    public double getValorPrimeiraParcela() {
        return valorPrimeiraParcela;
    }

    public void setValorPrimeiraParcela(double valorPrimeiraParcela) {
        this.valorPrimeiraParcela = valorPrimeiraParcela;
    }

    public List<PlanoExcecaoVO> getExcecoes() {
        return excecoes;
    }

    public void setExcecoes(List<PlanoExcecaoVO> excecoes) {
        this.excecoes = excecoes;
    }

    public double getTotalPercentualDesconto() {
        if (getPlano().getBolsa()) {
            return 100.0;
        }
        if (getValorContrato() == 0.0) {
            return 0.0;
        }
        return Uteis.arredondarForcando2CasasDecimais(100 * (getValorContrato() - getValorBaseCalculo()) / getValorContrato());

    }

    public ContratoWS toWS(Boolean app) {
        ContratoWS contratoWS = new ContratoWS();
        contratoWS.setCodigo(this.codigo);
        contratoWS.setCodigoCliente(this.getCliente().getCodigo());
        contratoWS.setValorFinal(Uteis.arredondarForcando2CasasDecimais(this.valorFinal));
        contratoWS.setValorProRata(Uteis.arredondarForcando2CasasDecimais(getValorProRata()));
        contratoWS.setValorPrimeiraParcela(Uteis.arredondarForcando2CasasDecimais(this.valorPrimeiraParcela));
        contratoWS.setSituacaoContrato(this.getSituacaoContrato_Apresentar());
        contratoWS.setDataLancamento(this.getDataLancamento_Apresentar());
        contratoWS.setVigenciaDe(this.getVigenciaDe_Apresentar());
        contratoWS.setVigenciaAteAjustada(this.getVigenciaAteAjustada_Apresentar());
        contratoWS.setSituacao(this.getSituacao_Apresentar());
        contratoWS.setNomePlano(this.getPlano().getDescricao());
        contratoWS.setValorBase(this.getValorBaseCalculo());
        contratoWS.setNumeroMeses(this.getContratoDuracao().getNumeroMeses());
        contratoWS.setSituacaoSubordinada(this.getSituacaoSubordinada());
        contratoWS.setCrossfit(this.isCrossfit());
        contratoWS.setPermiteMarcarAula(this.situacao.equals("AT") && (this.situacaoSubordinada.equals("NO") ||  this.situacaoSubordinada.equals("AV")));
        contratoWS.setBolsa(this.getBolsa());
        for (Object obj : getContratoModalidadeVOs()) {
            ContratoModalidadeVO contratoModalidadeVO = (ContratoModalidadeVO) obj;
            contratoWS.getModalidades().add(contratoModalidadeVO.toWS());
        }

        contratoWS.setValorMensal(this.getContratoRecorrenciaVO().getValorMensal());
        contratoWS.setValorAnuidade(this.getContratoRecorrenciaVO().getValorAnuidade());
        contratoWS.setDiaVencimentoAnuidade(this.getContratoRecorrenciaVO().getDiaVencimentoAnuidade());
        contratoWS.setDescricaoCobrancaPrimeiraParcela("Hoje");

        try {
            Date dataPrimeiraParcela = this.getDataPrimeiraParcela() != null ? this.getDataPrimeiraParcela() : this.getMovProdutoVOs().get(0).dataInicioVigencia;
            if (getVendasConfigVO().getCobrarPrimeiraParcelaCompra() != null &&
                    !getVendasConfigVO().getCobrarPrimeiraParcelaCompra() && Uteis.nrDiasEntreDatasSemHoraZerada(dataPrimeiraParcela, Calendario.hoje()) != 0 &&
                    this.getPlano().getPlanoRecorrencia().getTaxaAdesao() == 0) {
                contratoWS.setDescricaoCobrancaPrimeiraParcela("em " + Uteis.getData(dataPrimeiraParcela, "br"));
            }
        }catch (Exception e) {}

        for (Object obj : Mes.values()) {
            Mes mes = (Mes) obj;
            if (this.getContratoRecorrenciaVO().getMesVencimentoAnuidade().equals(mes.getCodigo())) {
                contratoWS.setMesVencimentoAnuidade(mes.getDescricao());
            }
        }

        PlanoEmpresaVO planoEmpresaVO = null;
        if (this.getPlano().getEmpresas() != null && !this.getPlano().getEmpresas().isEmpty() && this.getPlano().getRegimeRecorrencia()) {
            for (PlanoEmpresaVO emp : this.getPlano().getEmpresas()) {
                if (emp.getEmpresa().getCodigo().equals(this.getCliente().getEmpresa().getCodigo())) {
                    planoEmpresaVO = emp;
                    break;
                }
            }
        }

        contratoWS.setValorAdesao(this.getPlano().getPlanoRecorrencia().getTaxaAdesao());
        if (planoEmpresaVO != null && !UteisValidacao.emptyNumber(planoEmpresaVO.getTaxaAdesao())) {
            contratoWS.setValorAdesao(planoEmpresaVO.getTaxaAdesao());
        }

        contratoWS.setCodigoPlano(this.getPlano().getCodigo());
        contratoWS.setVendaCreditoTreino(this.isVendaCreditoTreino());
        contratoWS.setNrParcelas(this.getContratoCondicaoPagamento().getCondicaoPagamento().getNrParcelas());
        contratoWS.setHorarioDescricao(this.getContratoHorario().getHorario().getDescricao());
        contratoWS.setPermiteRenovar(this.getRenovarContrato());

        contratoWS.setProdutos(new ArrayList<ProdutoWS>());
        if(movProdutoVOs != null){
            Date dataDescricao = Calendario.hoje();
            if (getDataPrimeiraParcela() != null){
                dataDescricao = new Date(getDataPrimeiraParcela().getTime());
            }

            //PAY-1197 - Se a duração do plano for diferente do nrParcelas da condição de pagamento, e estiver vindo do vendas online, gera as parcelas
            //baseado na condição de pagamento
			boolean qtdParcelasDiferenteDuracaoPlano = false;
            if(this.getPlanoCondicaoPagamento() != null && this.getPlanoCondicaoPagamento().getCondicaoPagamento() != null
                    && !UteisValidacao.emptyNumber(this.getPlanoDuracao().getNumeroMeses()) && this.getPlano() != null
                    && this.getPlano().getSite() != null && !UteisValidacao.emptyNumber(this.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas())
                    && !(this.getPlanoDuracao().getNumeroMeses().equals(this.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas()))
                    && this.origemSistema != null && this.origemSistema.equals(OrigemSistemaEnum.VENDAS_ONLINE_2) && this.getPlano().getSite()){
                qtdParcelasDiferenteDuracaoPlano = true;
                int nrParcelas = this.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas();
                int i = 1;

				while(i <= nrParcelas) {
                    double valorParcelas = 0;
                    try {
                        valorParcelas = obterValorParcelas(this, true);
                    } catch (Exception ignore) {
                        qtdParcelasDiferenteDuracaoPlano = false;
                        break;
                    }

                    double valorPrimeiraParcela = 0;
                    try {
                        valorPrimeiraParcela = obterValorPrimeiraParcelas(this, valorParcelas);
                    } catch (Exception ignore) {
                        qtdParcelasDiferenteDuracaoPlano = false;
                        break;
                    }

                    String descricao = getPlano().getDescricao() + " - " + Uteis.getDataMesAnoConcatenado(dataDescricao);
                    dataDescricao = Uteis.somarMeses(dataDescricao, 1);


                    ProdutoWS parcela = new ProdutoWS();
                    parcela.setDescricao(descricao);
                    //Primeira Parcela
                    if(i == 1) {
                        parcela.setValor(Uteis.arredondarForcando2CasasDecimais(valorPrimeiraParcela));
                    }else{
						parcela.setValor(Uteis.arredondarForcando2CasasDecimais(valorParcelas));
                    }

                    parcela.setTipoProduto(TipoProduto.MES_REFERENCIA_PLANO.getCodigo());
                    contratoWS.getParcelas().add(parcela);
                    i++;
                }
        	}


            gerarEncerramentoContratoDia(true, this, movProdutoVOs);

            for(MovProdutoVO mop : new ArrayList<MovProdutoVO>(movProdutoVOs)){
                ProdutoWS parcela = new ProdutoWS();
                if(mop.getProduto().getTipoProduto().equals(TipoProduto.MES_REFERENCIA_PLANO.getCodigo())){
                    if(qtdParcelasDiferenteDuracaoPlano) {
						continue;
                    }

                    String descricao = mop.getDescricao();

                    if (Calendario.maior(dataDescricao, Calendario.hoje())){
                        String[] descricaoArray = mop.getDescricao().split(" - ");
                        String descricaoPlano = "";
                        for(int i = 0; i < (descricaoArray.length - 1); i++){
                            descricaoPlano += (descricaoArray[i] + " - ");
                        }
                        descricao = descricaoPlano + Uteis.getDataMesAnoConcatenado(dataDescricao);
                        dataDescricao = Uteis.somarMeses(dataDescricao, 1);
                    }

                    parcela.setDescricao(descricao);
                    parcela.setValor(Uteis.arredondarForcando2CasasDecimais(mop.getTotalFinal()));
                    parcela.setTipoProduto(mop.getTipoProduto());
                    contratoWS.getParcelas().add(parcela);
                    continue;
                }
                ProdutoWS prod = new ProdutoWS();
                prod.setDescricao(mop.getDescricao());
                prod.setValor(mop.getTotalFinal());
                if(mop.getProduto().getTipoProduto().equals(TipoProduto.MATRICULA.getCodigo())){
                    contratoWS.setValorMatricula(mop.getTotalFinal());
                }
                if(mop.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())){
                    try {
                        int ano = Uteis.getAnoData(this.getVigenciaDe());
                        Date cobranca = Uteis.getDate(getPlano().getPlanoRecorrencia().getDiaAnuidade(),
                                getPlano().getPlanoRecorrencia().getMesAnuidade() - 1,
                                ano);
                        if(Calendario.menor(cobranca, this.getVigenciaDe())){
                            cobranca = Uteis.somarCampoData(cobranca, Calendar.YEAR, 1);
                        }
                        prod.setDataCobranca(Uteis.getData(cobranca));
                        contratoWS.setAnoCobrancaAnuidade(Uteis.getAnoData(cobranca));
                    }catch (Exception e){
                        //
                    }
                }else{
                    prod.setDataCobranca(mop.getTotalFinal() > 0.0 ? "PRIMEIRA PARCELA" : " - ");
                }
                prod.setTipoProduto(mop.getTipoProduto());
                contratoWS.getProdutos().add(prod);
            }
            contratoWS.setProdutos(Ordenacao.ordenarLista(contratoWS.getProdutos(), "descricao"));

            if(this.getPlano().getPlanoRecorrencia().getRenovavelAutomaticamente() || this.getContratoRecorrenciaVO().getRenovavelAutomaticamente()){
                contratoWS.setPermiterenovacaoautomatica(true);
                contratoWS.setRenovacaoautomaticasimnao(this.getPermiteRenovacaoAutomatica());
            } else if (app) {
                Boolean renovaAutomaticamente = this.getPermiteRenovacaoAutomatica() && this.getPlano().getRenovavelAutomaticamente();
                contratoWS.setPermiterenovacaoautomatica(renovaAutomaticamente);
                contratoWS.setRenovacaoautomaticasimnao(renovaAutomaticamente);
            } else{
                contratoWS.setPermiterenovacaoautomatica(false);
                contratoWS.setRenovacaoautomaticasimnao(false);
            }

        }

        if (!UteisValidacao.emptyNumber(getDiaVencimentoProrata()) && !getSituacao().equalsIgnoreCase("CA")) {
            contratoWS.setDiasCartao(getDiaVencimentoProrata());
        }

        //Parâmetros definidos para definir qual taxa de contrato mostrar nas parcelas do front do vendas online
        contratoWS.setRenovacao(this.isRenovacao);
        contratoWS.setRematricula(this.isRematricula);

        return contratoWS;
    }

    public void povoarInformacoesLancarContratoEncerramentoContratoDia(ContratoVO novoContrato) {
        if (novoContrato.getPlano().getContratosEncerramDia() != null) {
            boolean acabaAntes = Calendario.menor( novoContrato.getVigenciaAteAjustada(), novoContrato.getPlano().getContratosEncerramDia());
            if (!acabaAntes) {
                novoContrato.setListParcelasEditadas(new ArrayList<>());
                novoContrato.setObservacao("Este contrato irá sofrer ajustes e será encerrado no dia " +
                        Uteis.getData(novoContrato.getPlano().getContratosEncerramDia()) + " de acordo com as configurações do plano.");
                Integer nrParcelasRemover = Calendario.diferencaEmMeses(novoContrato.getVigenciaAte(), novoContrato.getPlano().getContratosEncerramDia());
                if (nrParcelasRemover < 0) {
                    if (!contratoIniciaDia1AcabaDia30()) {
                        nrParcelasRemover += 1; // ajuste no intervalo da diferença
                    }
                    nrParcelasRemover = Math.abs(nrParcelasRemover);
                }
                Integer nrParcelas = novoContrato.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas() - nrParcelasRemover;
                novoContrato.setVigenciaAteAjustada(novoContrato.getPlano().getContratosEncerramDia());
                novoContrato.getPlanoCondicaoPagamento().getCondicaoPagamento().setNrParcelas(nrParcelas);
                double valorMensal = (novoContrato.getValorContrato() / novoContrato.getPlanoDuracao().getNumeroMeses());
                novoContrato.setValorFinal((
                        nrParcelas > 1 ? ((valorMensal * (nrParcelas - 1)) + Uteis.calcularProRataEncerramentoContratoDia(novoContrato, valorMensal)) :
                                Uteis.calcularProRataEncerramentoContratoDia(novoContrato, valorMensal)) +
                        novoContrato.getTotalFinalProdutos());

                // gerar parcelas
                boolean somenteParcela1 = nrParcelas == 1;
                int parcelaFinal = nrParcelas;
                while (nrParcelas > 0) {

                    if (nrParcelas == 1) {
                        double valorParcela1 = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorMensal +
                                ((novoContrato.getSituacaoContrato().equals("MA") ? novoContrato.getValorMatricula() : novoContrato.getValorRematricula()) +
                                        novoContrato.getTotalProdutosCobrarSeparado() + novoContrato.getSomaAdesao() + novoContrato.getSomaAnuidade()));
                        novoContrato.setValorPrimeiraParcelaEdicao((somenteParcela1 ? Uteis.calcularProRataEncerramentoContratoDia(novoContrato, valorParcela1) :
                                valorParcela1));
                    } else {
                        ParcelasEditarNegociacaoNovo parcelaEditada = new ParcelasEditarNegociacaoNovo();
                        parcelaEditada.setDescricao("Parcela " + nrParcelas);
                        parcelaEditada.setValorParcela(((parcelaFinal == nrParcelas) ?
                                Uteis.calcularProRataEncerramentoContratoDia(novoContrato, valorMensal) :
                                valorMensal));
                        parcelaEditada.setNrParcela(nrParcelas);
                        novoContrato.getListParcelasEditadas().add(parcelaEditada);
                    }
                    nrParcelas--;
                }
            }
        }
    }

    public boolean contratoIniciaDia1AcabaDia30() {
        if (this.getVigenciaDe() != null && this.getVigenciaAte() != null) {
            return (Calendario.getDia(this.getVigenciaDe()) == 1 && Calendario.getDia(this.getVigenciaAte()) == 30);
        }
        return false;
    }

    public void gerarEncerramentoContratoDia(boolean simular, ContratoVO contratoPrm, List objetos) {
        if (contratoPrm.getPlano().getContratosEncerramDia() != null) {
            boolean acabaAntes = Calendario.menor(contratoPrm.getVigenciaAteAjustada(), contratoPrm.getPlano().getContratosEncerramDia());
            if (!acabaAntes) {
                int parcelasRemover = Calendario.diferencaEmMeses(
                        contratoPrm.getVigenciaAte(),
                        contratoPrm.getPlano().getContratosEncerramDia()
                );

                Ordenacao.ordenarListaReverse(objetos, "codigo");
                if (parcelasRemover < 0) {
                    if (!contratoIniciaDia1AcabaDia30()) {
                        parcelasRemover += 1; // ajuste no intervalo da diferença
                    }
                    int quantidadeParaRemover = Math.abs(parcelasRemover);
                    Iterator<MovProdutoVO> iterator = objetos.iterator();
                    while (iterator.hasNext() && quantidadeParaRemover > 0) {
                        MovProdutoVO movProduto = iterator.next();
                        if (movProduto.getTipoProduto().equals("PM")) {
                            iterator.remove();
                            quantidadeParaRemover--;
                        }
                    }
                }

                Iterator e = objetos.iterator();
                contratoPrm.setValorFinal(0.0);
                boolean ultimo = true;
                while (e.hasNext()) {
                    MovProdutoVO obj = (MovProdutoVO) e.next();
                    if (obj.getTipoProduto().equals("PM")) {
                        if (ultimo) {
                            obj.setTotalFinal(Uteis.calcularProRataEncerramentoContratoDia(contratoPrm, obj.getTotalFinal()));
                            ultimo = false;
                        }
                    }
                    contratoPrm.setValorFinal(contratoPrm.getValorFinal() + obj.getTotalFinal());
                }
                Ordenacao.ordenarListaReverse(objetos, "codigo");
            }
        }
    }

    public Double getValorMatricula() {
        return valorMatricula;
    }

    public void setValorMatricula(Double valorMatricula) {
        this.valorMatricula = valorMatricula;
    }

    public MovProdutoVO getProdutoPlano() {
        return produtoPlano;
    }

    public void setProdutoPlano(MovProdutoVO produtoPlano) {
        this.produtoPlano = produtoPlano;
    }

    public Double getValorRematricula() {
        return valorRematricula;
    }

    public void setValorRematricula(Double valorRematricula) {
        this.valorRematricula = valorRematricula;
    }

    public String getModalidades() {
        try {
            String mod = "";
            for (ContratoModalidadeVO cmVo : contratoModalidadeVOs){
               mod = "|"+cmVo.getModalidade().getCodigo();
            }
            return mod.replaceFirst("\\|", "");
        } catch (Exception e) {
            return "";
        }
    }

    public ContratoVO getContratoResponsavelRenovacaoMatriculaVO() {
        return contratoResponsavelRenovacaoMatriculaVO;
    }

    public void setContratoResponsavelRenovacaoMatriculaVO(ContratoVO contratoResponsavelRenovacaoMatriculaVO) {
        this.contratoResponsavelRenovacaoMatriculaVO = contratoResponsavelRenovacaoMatriculaVO;
    }

    public Boolean getContratoPossuiRemessaBoleto() {
        if (contratoPossuiRemessaBoleto == null){
            contratoPossuiRemessaBoleto = false;
        }
        return contratoPossuiRemessaBoleto;
    }

    public void setContratoPossuiRemessaBoleto(Boolean contratoPossuiRemessaBoleto) {
        this.contratoPossuiRemessaBoleto = contratoPossuiRemessaBoleto;
    }

    public Boolean getRenovavelAutomaticamente() {
        if (renovavelAutomaticamente == null){
            renovavelAutomaticamente = false;
        }
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(Boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public Double getValorConvenioDesconto() {
        return valorConvenioDesconto;
    }

    public void setValorConvenioDesconto(Double valorConvenioDesconto) {
        this.valorConvenioDesconto = valorConvenioDesconto;
    }

    public void obterSituacaoRenovacao(Integer diasRestanteContratoOrigemRenovacao) {
        if(this.getDataRenovarRealizada() == null){
            this.setSituacaoRenovacao("");
        }
        Date vigenciaAte = this.getVigenciaAteAjustada();
        if ((diasRestanteContratoOrigemRenovacao != null) && (diasRestanteContratoOrigemRenovacao > 0)){
            vigenciaAte = getVigenciaAte();
        }

        if (Calendario.igual(this.getDataRenovarRealizada(), vigenciaAte)) {
             this.setSituacaoRenovacao(SituacaoRenovacaoEnum.RenovacaoNoDia.getSigla());
        } else if (Calendario.menor(this.getDataRenovarRealizada(), vigenciaAte)) {
            this.setSituacaoRenovacao(SituacaoRenovacaoEnum.RenovacaoAntecipada.getSigla());
        } else {
             this.setSituacaoRenovacao(SituacaoRenovacaoEnum.RenovacaoAtrasada.getSigla());
        }
    }

    public Integer getQuantidadeMaximaFrequencia() {
        return quantidadeMaximaFrequencia;
    }

    public void setQuantidadeMaximaFrequencia(Integer quantidadeMaximaFrequencia) {
        this.quantidadeMaximaFrequencia = quantidadeMaximaFrequencia;
    }

    public boolean isVendaCreditoTreino() {
        return vendaCreditoTreino;
    }

    public void setVendaCreditoTreino(boolean vendaCreditoTreino) {
        this.vendaCreditoTreino = vendaCreditoTreino;
    }

    public List<ControleCreditoTreinoVO> getListaControleCreditoTreino() {
        return listaControleCreditoTreino;
    }

    public void setListaControleCreditoTreino(List<ControleCreditoTreinoVO> listaControleCreditoTreino) {
        this.listaControleCreditoTreino = listaControleCreditoTreino;
    }

    public ContratoDuracaoCreditoTreinoVO getContratoDuracaoCreditoTreinoVO() {
        return contratoDuracaoCreditoTreinoVO;
    }

    public void setContratoDuracaoCreditoTreinoVO(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO) {
        this.contratoDuracaoCreditoTreinoVO = contratoDuracaoCreditoTreinoVO;
    }

    public String getDescricaoHorarioContrato_Apresentar(){
        if ((this.isVendaCreditoTreino()) && (this.contratoDuracao.getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.LIVRE_OBRIGATORIO_MARCAR_AULA)) {
            return this.contratoDuracao.getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum().getDescricao();
        }else{
            return this.contratoHorario.horario.getDescricao();
        }
    }

    public Date getDataPrevistaRenovarAntecipado() {
        if (dataPrevistaRenovarAntecipado == null) {
            dataPrevistaRenovarAntecipado = getDataPrevistaRenovar();
        }
        return dataPrevistaRenovarAntecipado;
    }

    public void setDataPrevistaRenovarAntecipado(Date dataPrevistaRenovarAntecipado) {
        this.dataPrevistaRenovarAntecipado = dataPrevistaRenovarAntecipado;
    }

    public Integer getNrParcelasAnuidades() {
        Integer nrParcelasAnuidades = 0;
        for (Object object : getContratoPlanoProdutoSugeridoVOs()) {
            ContratoPlanoProdutoSugeridoVO contratoProdSugerido = (ContratoPlanoProdutoSugeridoVO) object;
            if (contratoProdSugerido.getPlanoProdutoSugerido().getObrigatorio() || contratoProdSugerido.getPlanoProdutoSugerido().getProdutoSugeridoEscolhida()) {
                if (getPlano().getRegimeRecorrencia() && contratoProdSugerido.getPlanoProdutoSugerido().getProduto().getTipoProduto().equals("TA")) {
                    nrParcelasAnuidades = contratoProdSugerido.getPlanoProdutoSugerido().getQuantidade();
                    break;
                }
            }
        }

        return nrParcelasAnuidades;
    }

    public Double getValorPorAnuidade() {
        Double valorPorAnuidade = 0.0;
        for (Object object : getContratoPlanoProdutoSugeridoVOs()) {
            ContratoPlanoProdutoSugeridoVO contratoProdSugerido = (ContratoPlanoProdutoSugeridoVO) object;
            if (contratoProdSugerido.getPlanoProdutoSugerido().getObrigatorio() || contratoProdSugerido.getPlanoProdutoSugerido().getProdutoSugeridoEscolhida()) {
                if (getPlano().getRegimeRecorrencia() && contratoProdSugerido.getPlanoProdutoSugerido().getProduto().getTipoProduto().equals("TA")) {
                    int nrParcelasAnuidades = contratoProdSugerido.getPlanoProdutoSugerido().getQuantidade();
                    if (nrParcelasAnuidades > 0) {
                        Double valorAnuidade = contratoProdSugerido.getPlanoProdutoSugerido().getValorProdutoQtdDesconto();
                        valorPorAnuidade = valorAnuidade / nrParcelasAnuidades;
                    }
                }
            }
        }
        return valorPorAnuidade;
    }

    public void setSomaAnuidade(double somaAnuidade) {
        this.somaAnuidade = somaAnuidade;
    }

    public double getSomaAnuidade() {
        return somaAnuidade;
    }

    public ContratoVO getContratoOrigemRenovacao() {
        return contratoOrigemRenovacao;
    }

    public void setContratoOrigemRenovacao(ContratoVO contratoOrigemRenovacao) {
        this.contratoOrigemRenovacao = contratoOrigemRenovacao;
    }

    public Integer getDiasRestanteContratoOrigemRenovacao() {
        return diasRestanteContratoOrigemRenovacao;
    }

    public void setDiasRestanteContratoOrigemRenovacao(Integer diasRestanteContratoOrigemRenovacao) {
        this.diasRestanteContratoOrigemRenovacao = diasRestanteContratoOrigemRenovacao;
    }

    public String getDescricaoDuracaoTelaNegociacao(){
        String descricao = planoDuracao.getDescricaoDuracao();
        if ((getDiasRestanteContratoOrigemRenovacao() > 0) && (getSaldoCreditoContratoOrigemRenovacao() > 0)){
            descricao = descricao + " + " + getDiasRestanteContratoOrigemRenovacao() + " dias restante do contrato anterior.";
        }
        return descricao;
    }

    public String getInformacaoRenovacaoCreditoTreino() {
        if(informacaoRenovacaoCreditoTreino != null && informacaoRenovacaoCreditoTreino.trim().isEmpty()){
            informacaoRenovacaoCreditoTreino = null;
        }
        return informacaoRenovacaoCreditoTreino;
    }

    public void setInformacaoRenovacaoCreditoTreino(String informacaoRenovacaoCreditoTreino) {
        this.informacaoRenovacaoCreditoTreino = informacaoRenovacaoCreditoTreino;
    }


    public Integer getPosTlTabelaPreto(){
        try {
            if(posTlTabelaPreto == null){
                Integer diasDeContrato = obterNrDiasContratoAteDataVigenciaAjustada();
                long nrDiasAteHoje = Uteis.nrDiasEntreDatas(getVigenciaDe(), Calendario.hoje()) + 1;
            }
        } catch (Exception e) {
            posTlTabelaPreto = 130;
        }
        return posTlTabelaPreto;
    }

    public boolean isMostrarLabelPrevisaoRenovacaoAntecipada(){
        return (isVendaCreditoTreino()) &&
                (Calendario.maior(getDataPrevistaRenovar(),getVigenciaAteAjustada()));
    }

    public Double getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public Integer getSaldoCreditoContratoOrigemRenovacao() {
        return saldoCreditoContratoOrigemRenovacao;
    }

    public void setSaldoCreditoContratoOrigemRenovacao(Integer saldoCreditoContratoOrigemRenovacao) {
        this.saldoCreditoContratoOrigemRenovacao = saldoCreditoContratoOrigemRenovacao;
    }


    public Date getVigenciaTurmaCreditoTreinoAte() {
        return vigenciaTurmaCreditoTreinoAte;
    }

    public void setVigenciaTurmaCreditoTreinoAte(Date vigenciaTurmaCreditoTreinoAte) {
        this.vigenciaTurmaCreditoTreinoAte = vigenciaTurmaCreditoTreinoAte;
    }

    public Integer getDiferencaQtdeCreditoTreinoManutencaoModalidade() {
        return diferencaQtdeCreditoTreinoManutencaoModalidade;
    }

    public void setDiferencaQtdeCreditoTreinoManutencaoModalidade(Integer diferencaQtdeCreditoTreinoManutencaoModalidade) {
        this.diferencaQtdeCreditoTreinoManutencaoModalidade = diferencaQtdeCreditoTreinoManutencaoModalidade;
    }

    public Integer getQtdeVezesSemanaAposManutencaoModalidade() {
        return qtdeVezesSemanaAposManutencaoModalidade;
    }

    public void setQtdeVezesSemanaAposManutencaoModalidade(Integer qtdeVezesSemanaAposManutencaoModalidade) {
        this.qtdeVezesSemanaAposManutencaoModalidade = qtdeVezesSemanaAposManutencaoModalidade;
    }

    public Double getPercentualAndamento(){
        return Uteis.obterPosicaoData(Calendario.hoje(), vigenciaDe, vigenciaAteAjustada);
    }

    public boolean deveLiberarVaga(int qtdeDeDiasDoTrancamento) {
        return empresa.deveLiberarVaga(qtdeDeDiasDoTrancamento);
    }

    public boolean foiTransferidoDeEmpresa() throws  Exception {
        return !empresa.getCodigo().equals(getFacade().getCliente().consultarCodEmpresaClientePorContrato(this.getCodigo()));
    }

    public String empresaAtual() throws Exception {
        return getFacade().getCliente().consultarNomeEmpresaClientePorContrato(this.getCodigo());
    }

    public ContratoOperacaoVO getCancelamentoContratoVO() {
        if(cancelamentoContratoVO == null){
            cancelamentoContratoVO = new ContratoOperacaoVO();
        }
        return cancelamentoContratoVO;
    }

    public void setCancelamentoContratoVO(ContratoOperacaoVO cancelamentoContratoVO) {
        this.cancelamentoContratoVO = cancelamentoContratoVO;
    }
    public String getJustificativaCancelamento_Apresentar(){
        return getCancelamentoContratoVO().getTipoJustificativa().getDescricao();
    }

    public Double getTotalFinalProdutos() {
        return totalFinalProdutos;
    }

    public void setTotalFinalProdutos(Double totalFinalProdutos) {
        this.totalFinalProdutos = totalFinalProdutos;
    }

    public String getNumeroCupomDesconto() {
        return numeroCupomDesconto;
    }

    public void setNumeroCupomDesconto(String numeroCupomDesconto) {
        this.numeroCupomDesconto = numeroCupomDesconto;
    }

    public Double getPremioProdutosCupomDesconto() {
        return premioProdutosCupomDesconto != null ? premioProdutosCupomDesconto : 0.0;
    }

    public void setPremioProdutosCupomDesconto(Double premioProdutosCupomDesconto) {
        this.premioProdutosCupomDesconto = premioProdutosCupomDesconto;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public boolean isUsouCupomDescontoParaAdesao() {
        return (this.premioProdutosCupomDesconto != null) && (this.premioProdutosCupomDesconto > 0);
    }


    public boolean isGerarAdesao() {
        return gerarAdesao;
    }

    public void setGerarAdesao(boolean gerarAdesao) {
        this.gerarAdesao = gerarAdesao;
    }

    public boolean isGerarAnuidade() {
        return gerarAnuidade;
    }

    public boolean isPossuiRemessaBoleto() {
        return possuiRemessaBoleto;
    }

    public void setPossuiRemessaBoleto(boolean possuiRemessaBoleto) {
        this.possuiRemessaBoleto = possuiRemessaBoleto;
    }

    public void setGerarAnuidade(boolean gerarAnuidade) {
        this.gerarAnuidade = gerarAnuidade;
    }

    public void setContratoEhDeTurma(boolean contratoEhDeTurma) {
        this.contratoEhDeTurma = contratoEhDeTurma;
    }

    public boolean isContratoEhDeTurma() {
        return contratoEhDeTurma;
    }

    public boolean isExcluirNFSe() {
        return excluirNFSe;
    }

    public void setExcluirNFSe(boolean excluirNFSe) {
        this.excluirNFSe = excluirNFSe;
    }

    public boolean isMostrarMsgExcluirNFse() {
        return mostrarMsgExcluirNFse;
    }

    public void setMostrarMsgExcluirNFse(boolean mostrarMsgExcluirNFse) {
        this.mostrarMsgExcluirNFse = mostrarMsgExcluirNFse;
    }

    public Integer getNrParcelasPagamento() {
        return nrParcelasPagamento;
    }

    public void setNrParcelasPagamento(Integer nrParcelasPagamento) {
        this.nrParcelasPagamento = nrParcelasPagamento;
    }

    public String getOpcaoSelecionadaRenovacaoPlanoCredito() {
        return opcaoSelecionadaRenovacaoPlanoCredito;
    }

    public void setOpcaoSelecionadaRenovacaoPlanoCredito(String opcaoSelecionadaRenovacaoPlanoCredito) {
        this.opcaoSelecionadaRenovacaoPlanoCredito = opcaoSelecionadaRenovacaoPlanoCredito;
    }

    public Date getAssinadoEm() {
        return assinadoEm;
    }

    public void setAssinadoEm(Date assinadoEm) {
        this.assinadoEm = assinadoEm;
    }

    public void removerProdutoProRata() {
        int index = 0;
        for (Object o : getMovProdutoVOs()) {
            MovProdutoVO objExistente = (MovProdutoVO) o;
            if (objExistente.getDescricao().contains("PRO-RATA")) {
                getMovProdutoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public Integer getIdCampanhaCupomDesconto() {
        return idCampanhaCupomDesconto;
    }

    public void setIdCampanhaCupomDesconto(Integer idCampanhaCupomDesconto) {
        this.idCampanhaCupomDesconto = idCampanhaCupomDesconto;
    }

    public double getDescontoParcelaUsandoCupomDesconto() {
        if (getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas() > 1){
            return descontoParcelaUsandoCupomDesconto;
        }
        else {
            return 0.0;
        }
    }

    public void setDescontoParcelaUsandoCupomDesconto(double descontoParcelaUsandoCupomDesconto) {
        this.descontoParcelaUsandoCupomDesconto = descontoParcelaUsandoCupomDesconto;
    }

    public boolean isCalcularAnuidade() {
        return calcularAnuidade;
    }

    public void setCalcularAnuidade(boolean calcularAnuidade) {
        this.calcularAnuidade = calcularAnuidade;
    }

    public Boolean getPermiteRenovacaoAutomatica() {
        return permiteRenovacaoAutomatica;
    }

    public void setPermiteRenovacaoAutomatica(Boolean permiteRenovacaoAutomatica) {
        this.permiteRenovacaoAutomatica = permiteRenovacaoAutomatica;
    }

    public String getSituacaoCliente(){
        return getCliente().getSituacao();
    }

     public String getProfessorTreinoWeb(){
        String retorno = "";
        for (VinculoVO vinculoVO : getCliente().getVinculoVOs()) {
            if (vinculoVO.getTipoColaboradorVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO)) {
                retorno = vinculoVO.getColaborador().getPessoa().getNome();
            }
        }

        return retorno;
    }

    public boolean isCobrarMatriculaSeparada() {
        return cobrarMatriculaSeparada;
    }

    public void setCobrarMatriculaSeparada(boolean cobrarMatriculaSeparada) {
        this.cobrarMatriculaSeparada = cobrarMatriculaSeparada;
    }

    public Integer getNrVezesParcelarMatricula() {
        return nrVezesParcelarMatricula;
    }

    public void setNrVezesParcelarMatricula(Integer nrVezesParcelarMatricula) {
        this.nrVezesParcelarMatricula = nrVezesParcelarMatricula;
    }

    public boolean isCobrarProdutoSeparado() {
        return cobrarProdutoSeparado;
    }

    public void setCobrarProdutoSeparado(boolean cobrarProdutoSeparado) {
        this.cobrarProdutoSeparado = cobrarProdutoSeparado;
    }

    public Integer getNrVezesParcelarProduto() {
        return nrVezesParcelarProduto;
    }

    public void setNrVezesParcelarProduto(Integer nrVezesParcelarProduto) {
        this.nrVezesParcelarProduto = nrVezesParcelarProduto;
    }

    public double getTotalProdutosCobrarSeparado() {
        return totalProdutosCobrarSeparado;
    }

    public void setTotalProdutosCobrarSeparado(double totalProdutosCobrarSeparado) {
        this.totalProdutosCobrarSeparado = totalProdutosCobrarSeparado;
    }

    public boolean getGerarParcelaAnuidadeSeparada(){
        return this.getPlano().getPlanoRecorrencia().getNaoCobrarAnuidadeProporcional() || (this.isContratoRenovacao() && this.getPlano().isRenovarAnuidadeAutomaticamente());
    }

    public boolean isCrossfit() {
        return crossfit;
    }

    public void setCrossfit(boolean crossfit) {
        this.crossfit = crossfit;
    }

    public String getSituacaoSubordinada() {
        if (situacaoSubordinada == null) {
            situacaoSubordinada = "";
        }
        return situacaoSubordinada;
    }

    public void setSituacaoSubordinada(String situacaoSubordinada) {
        this.situacaoSubordinada = situacaoSubordinada;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        if (origemSistema == null) {
            origemSistema = OrigemSistemaEnum.ZW;
        }
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public void setMensagemSemGrupoDescontoAplicavel(String mensagemSemGrupoDescontoAplicavel) {
        this.mensagemSemGrupoDescontoAplicavel = mensagemSemGrupoDescontoAplicavel;
    }

    public String getMensagemSemGrupoDescontoAplicavel() {
        return mensagemSemGrupoDescontoAplicavel;
    }

    public GrupoVO getGrupo() {
        return grupo;
    }

    public void setGrupo(GrupoVO grupo) {
        this.grupo = grupo;
    }

    public Boolean getPossuiGrupoDesconto() {
        return getGrupo() != null && getGrupo().getCodigo() > 0;
    }

    public String getNomeConvenioDesconto() {
        if (nomeConvenioDesconto == null) {
            nomeConvenioDesconto = "";
        }
        return nomeConvenioDesconto;
    }

    public void setNomeConvenioDesconto(String nomeConvenioDesconto) {
        this.nomeConvenioDesconto = nomeConvenioDesconto;
    }

    public Double getValorContratoConvenioDesconto() {
        if (valorContratoConvenioDesconto == null) {
            valorContratoConvenioDesconto = 0.0;
        }
        return valorContratoConvenioDesconto;
    }

    public void setValorContratoConvenioDesconto(Double valorContratoConvenioDesconto) {
        this.valorContratoConvenioDesconto = valorContratoConvenioDesconto;
    }

    public Double getPercentualConvenioDesconto() {
        if (percentualConvenioDesconto == null) {
            percentualConvenioDesconto = 0.0;
        }
        return percentualConvenioDesconto;
    }

    public void setPercentualConvenioDesconto(Double percentualConvenioDesconto) {
        this.percentualConvenioDesconto = percentualConvenioDesconto;
    }

    public String getDescricaoRegraCancelamento() {
        if (descricaoRegraCancelamento == null) {
            descricaoRegraCancelamento = "";
        }
        return descricaoRegraCancelamento;
    }

    public void setDescricaoRegraCancelamento(String descricaoRegraCancelamento) {
        this.descricaoRegraCancelamento = descricaoRegraCancelamento;
    }

    public Integer getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public boolean isGerarParcelasAPartirDeMovParcelaVOs() {
        return gerarParcelasAPartirDeMovParcelaVOs;
    }

    public void setGerarParcelasAPartirDeMovParcelaVOs(boolean gerarParcelasAPartirDeMovParcelaVOs) {
        this.gerarParcelasAPartirDeMovParcelaVOs = gerarParcelasAPartirDeMovParcelaVOs;
    }

    public Double getValorMensalExcecao() {
        if(valorMensalExcecao == null){
            return 0.0;
        }
        return valorMensalExcecao;
    }

    public void setValorMensalExcecao(Double valorMensalExcecao) {
        this.valorMensalExcecao = valorMensalExcecao;
    }


    public Integer getDiaPrimeiraParcelaInteiro() {
        if(getDataPrimeiraParcela() == null){
            return null;
        }

        return new Integer(Calendario.getData(getDataPrimeiraParcela(), "dd"));
    }

    public String getValorPorAnuidadeApresentar() {
        return Uteis.formatarValorEmReal(getValorPorAnuidade());
    }

    public String getValorContratoReferenteMensalApresentar() {
        return Uteis.formatarValorEmReal(obterValorContratoReferenteMensal());
    }

    public String getDiaPrimeiraParcela() {
        if(getDataPrimeiraParcela() == null){
            return null;
        }

        return Calendario.getData(getDataPrimeiraParcela(), "dd");
    }

    public String getDiaLancamento() {
        return Calendario.getData(getDataLancamento(), "dd");
    }

    public Integer getDiaLancamentoInteiro(){
        return new Integer(getDiaLancamento());
    }

    public Double getSomaDescontoAdesao() {
        if (somaDescontoAdesao == null) {
            somaDescontoAdesao = 0.0;
        }
        return somaDescontoAdesao;
    }

    public void setSomaDescontoAdesao(Double somaDescontoAdesao) {
        this.somaDescontoAdesao = somaDescontoAdesao;
    }

    public Double getValorDescontoMatricula() {
        return valorDescontoMatricula;
    }

    public void setValorDescontoMatricula(Double valorDescontoMatricula) {
        this.valorDescontoMatricula = valorDescontoMatricula;
    }

    public Double getValorDescontoRematricula() {
        return valorDescontoRematricula;
    }

    public void setValorDescontoRematricula(Double valorDescontoRematricula) {
        this.valorDescontoRematricula = valorDescontoRematricula;
    }

    public Double getValorCheioMatricula() {
        return valorCheioMatricula;
    }

    public void setValorCheioMatricula(Double valorCheioMatricula) {
        this.valorCheioMatricula = valorCheioMatricula;
    }

    public Double getValorCheioRematricula() {
        return valorCheioRematricula;
    }

    public void setValorCheioRematricula(Double valorCheioRematricula) {
        this.valorCheioRematricula = valorCheioRematricula;
    }

    public Double getValorDescontoAnuidadeRecorrencia() {
        return valorDescontoAnuidadeRecorrencia;
    }

    public void setValorDescontoAnuidadeRecorrencia(Double valorDescontoAnuidadeRecorrencia) {
        this.valorDescontoAnuidadeRecorrencia = valorDescontoAnuidadeRecorrencia;
    }

    public Double getValorFinalAnuidadeRecorrencia() {
        return valorFinalAnuidadeRecorrencia;
    }

    public void setValorFinalAnuidadeRecorrencia(Double valorFinalAnuidadeRecorrencia) {
        this.valorFinalAnuidadeRecorrencia = valorFinalAnuidadeRecorrencia;
    }

    public Date getDataAnuidade() {
        return dataAnuidade;
    }

    public void setDataAnuidade(Date dataAnuidade) {
        this.dataAnuidade = dataAnuidade;
    }

    public HistoricoTurmasContratoTO getHistoricoTurmasContrato() {
        if(historicoTurmasContrato == null){
            historicoTurmasContrato = new HistoricoTurmasContratoTO();
        }
        return historicoTurmasContrato;
    }

    public void setHistoricoTurmasContrato(HistoricoTurmasContratoTO historicoTurmasContrato) {
        this.historicoTurmasContrato = historicoTurmasContrato;
    }

    public Integer getContratoOrigemTransferencia() {
        if (contratoOrigemTransferencia == null) {
            contratoOrigemTransferencia = 0;
        }
        return contratoOrigemTransferencia;
    }

    public void setContratoOrigemTransferencia(Integer contratoOrigemTransferencia) {
        this.contratoOrigemTransferencia = contratoOrigemTransferencia;
    }

    public List<ParcelasEditarNegociacaoNovo> getListParcelasEditadas() {
        return listParcelasEditadas;
    }

    public void setListParcelasEditadas(List<ParcelasEditarNegociacaoNovo> listParcelasEditadas) {
        this.listParcelasEditadas = listParcelasEditadas;
    }

    public double getValorPrimeiraParcelaEdicao() {
        return valorPrimeiraParcelaEdicao;
    }

    public void setValorPrimeiraParcelaEdicao(double valorPrimeiraParcelaEdicao) {
        this.valorPrimeiraParcelaEdicao = valorPrimeiraParcelaEdicao;
    }

    @Override
    public boolean equals(Object obj) {
        return obj instanceof ContratoVO &&
                ((ContratoVO) obj).getCodigo().intValue() == this.getCodigo().intValue();
    }

    public boolean isVendaCreditoSessao() {
        return vendaCreditoSessao;
    }

    public void setVendaCreditoSessao(boolean vendaCreditoSessao) {
        this.vendaCreditoSessao = vendaCreditoSessao;
    }

    public boolean isTodasAsParcelasEstaoPagas() {
        if (movParcelasVOValidarPagamento != null) {
            for (Object m : movParcelasVOValidarPagamento) {
                if (((MovParcelaVO) m).getSituacao().equals("EA") || ((MovParcelaVO) m).getSituacao().equals("RG")) {
                    todasAsParcelasEstaoPagas = false;
                    break;
                }
            }
        }else{
            todasAsParcelasEstaoPagas = false;
        }
        return todasAsParcelasEstaoPagas;
    }

    public void setTodasAsParcelasEstaoPagas(boolean todasAsParcelasEstaoPagas) {
        this.todasAsParcelasEstaoPagas = todasAsParcelasEstaoPagas;
    }

    public List getMovParcelasVOValidarPagamento() {
        return movParcelasVOValidarPagamento;
    }

    public void setMovParcelasVOValidarPagamento(List movParcelasVOValidarPagamento) {
        this.movParcelasVOValidarPagamento = movParcelasVOValidarPagamento;
    }

    public Integer getPontuacaoPlano() {
        return pontuacaoPlano;
    }

    public void setPontuacaoPlano(Integer pontuacaoPlano) {
        this.pontuacaoPlano = pontuacaoPlano;
    }

    public Integer getPontuacaoPlanoDuracao() {
        return pontuacaoPlanoDuracao;
    }

    public void setPontuacaoPlanoDuracao(Integer pontuacaoPlanoDuracao) {
        this.pontuacaoPlanoDuracao = pontuacaoPlanoDuracao;
    }

    public Integer getPontuacaoProduto() {
        return pontuacaoProduto;
    }

    public void setPontuacaoProduto(Integer pontuacaoProduto) {
        this.pontuacaoProduto = pontuacaoProduto;
    }

    public UsuarioVO getResponsavelAutorizacaoBolsa() {
        if(responsavelAutorizacaoBolsa == null) {
            return new UsuarioVO();
        }
        return responsavelAutorizacaoBolsa;
    }

    public void setResponsavelAutorizacaoBolsa(UsuarioVO responsavelAutorizacaoBolsa) {
        this.responsavelAutorizacaoBolsa = responsavelAutorizacaoBolsa;
    }

    public Double getDescontoExtraParcelasValorDiferente() {
        return descontoExtraParcelasValorDiferente;
    }

    public void setDescontoExtraParcelasValorDiferente(Double descontoExtraParcelasValorDiferente) {
        this.descontoExtraParcelasValorDiferente = descontoExtraParcelasValorDiferente;
    }

    public Date getDataBaseSegundaParcela() {
        return dataBaseSegundaParcela;
    }

    public void setDataBaseSegundaParcela(Date dataBaseSegundaParcela) {
        this.dataBaseSegundaParcela = dataBaseSegundaParcela;
    }

    public EventoVO getEventoVO() {
        if (eventoVO == null){
            eventoVO = new EventoVO();
        }
        return eventoVO;
    }

    public void setEventoVO(EventoVO eventoVO) {
        this.eventoVO = eventoVO;
    }

    public PessoaVO getPessoaOriginal() {
        if (pessoaOriginal == null) {
            pessoaOriginal = new PessoaVO();
        }
        return pessoaOriginal;
    }

    public void setPessoaOriginal(PessoaVO pessoaOriginal) {
        this.pessoaOriginal = pessoaOriginal;
    }

    public ClienteVO getClienteDependente() {
        if(clienteDependente == null){
            clienteDependente = new ClienteVO();
        }
        return clienteDependente;
    }



    public void setClienteDependente(ClienteVO clienteDependente) {
        this.clienteDependente = clienteDependente;
    }

    public boolean isIndicadorDependente() {
        return indicadorDependente;
    }

    public void setIndicadorDependente(boolean indicadorDependente) {
        this.indicadorDependente = indicadorDependente;
    }

    public VendasConfigVO getVendasConfigVO() {
        if(vendasConfigVO == null){
            return new VendasConfigVO();
        }
        return vendasConfigVO;
    }

    public void setVendasConfigVO(VendasConfigVO vendasConfigVO) {
        this.vendasConfigVO = vendasConfigVO;
    }

    public Double getValorDescontoManual() {
        return valorDescontoManual;
    }

    public void setValorDescontoManual(Double valorDescontoManual) {
        this.valorDescontoManual = valorDescontoManual;
    }

    public Integer getPrimeiroContratoBaseadoRenovacao() {
        if (primeiroContratoBaseadoRenovacao == null) {
            primeiroContratoBaseadoRenovacao = 0;
        }
        return primeiroContratoBaseadoRenovacao;
    }

    public void setPrimeiroContratoBaseadoRenovacao(Integer primeiroContratoBaseadoRenovacao) {
        this.primeiroContratoBaseadoRenovacao = primeiroContratoBaseadoRenovacao;
    }

    public String getIpassinaturacontrato() {
        return ipassinaturacontrato;
    }

    public void setIpassinaturacontrato(String ipassinaturacontrato) {
        this.ipassinaturacontrato = ipassinaturacontrato;
    }

    public Double getValorBaseNegociado() {
        if (valorBaseNegociado == null || getPlano().getBolsa()) {
            valorBaseNegociado = 0.0;
        }
        return valorBaseNegociado;
    }

    public void setValorBaseNegociado(Double valorBaseNegociado) {
        this.valorBaseNegociado = valorBaseNegociado;
    }

    public UsuarioVO getResponsavelAutorizacaoDataPrimeiraParcela() {
        if (responsavelAutorizacaoDataPrimeiraParcela == null) {
            responsavelAutorizacaoDataPrimeiraParcela = new UsuarioVO();
        }
        return responsavelAutorizacaoDataPrimeiraParcela;
    }

    public void setResponsavelAutorizacaoDataPrimeiraParcela(UsuarioVO responsavelAutorizacaoDataPrimeiraParcela) {
        this.responsavelAutorizacaoDataPrimeiraParcela = responsavelAutorizacaoDataPrimeiraParcela;
    }

    public String getXnumpro() {
        if (xnumpro == null) {
            xnumpro = "";
        }
        return xnumpro;
    }

    public void setXnumpro(String xnumpro) {
        this.xnumpro = xnumpro;
    }

    public boolean isIncluindoTurma() {
        return incluindoTurma;
    }

    public void setIncluindoTurma(boolean incluindoTurma) {
        this.incluindoTurma = incluindoTurma;
    }

    public double getValorTemporarioDescontoAntecipadoTotal() {
        return valorTemporarioDescontoAntecipadoTotal;
    }

    public void setValorTemporarioDescontoAntecipadoTotal(double valorTemporarioDescontoAntecipadoTotal) {
        this.valorTemporarioDescontoAntecipadoTotal = valorTemporarioDescontoAntecipadoTotal;
    }

    public boolean isRenovacao() {
        return isRenovacao;
    }

    public void setRenovacao(boolean renovacao) {
        isRenovacao = renovacao;
    }

    public boolean isRematricula() {
        return isRematricula;
    }

    public void setRematricula(boolean rematricula) {
        isRematricula = rematricula;
    }

    public boolean isAlterouDataInicioContrato() {
        return alterouDataInicioContrato;
    }

    public void setAlterouDataInicioContrato(boolean alterouDataInicioContrato) {
        this.alterouDataInicioContrato = alterouDataInicioContrato;
    }

    public Boolean getAlterouTipoDoContrato() {
        return alterouTipoDoContrato;
    }

    public void setAlterouTipoDoContrato(Boolean alterouTipoDoContrato) {
        this.alterouTipoDoContrato = alterouTipoDoContrato;
    }

    public Boolean getAgregador() {
        if (agregador == null){
            agregador = false;
        }
        return agregador;
    }

    public void setAgregador(Boolean agregador) {
        this.agregador = agregador;
    }

    public String getDataUltimoAcessoAgregador_Apresentar() {
        if (dataUltimoAcessoAgregador == null) {
            return "-";
        }
        return Uteis.getData(dataUltimoAcessoAgregador);
    }

    public Date getDataUltimoAcessoAgregador() {
        return dataUltimoAcessoAgregador;
    }

    public void setDataUltimoAcessoAgregador(Date dataUltimoAcessoAgregador) {
        this.dataUltimoAcessoAgregador = dataUltimoAcessoAgregador;
    }

    public String getTipoAgregador_Apresentar() {
        if (tipoAgregador == null) {
            return "-";
        }
        return tipoAgregador.replaceAll("\\{", "").replaceAll("\\}", "");
    }

    public String getTipoAgregador() {
        if (tipoAgregador == null) {
            tipoAgregador = "";
        }
        return tipoAgregador;
    }

    public void setTipoAgregador(String tipoAgregador) {
        this.tipoAgregador = tipoAgregador;
    }

    public String getQtdCheckInAgregador_Apresentar() {
        if(UteisValidacao.emptyNumber(qtdCheckInAgregador)){
            return "-";
        }
        return qtdCheckInAgregador + "";
    }

    public Integer getQtdCheckInAgregador() {
        if(qtdCheckInAgregador == null){
            qtdCheckInAgregador = 0;
        }
        return qtdCheckInAgregador;
    }

    public void setQtdCheckInAgregador(Integer qtdCheckInAgregador) {
        this.qtdCheckInAgregador = qtdCheckInAgregador;
    }

    public Date getDataSincronizacaoIntegracaoFoguete() {
        return dataSincronizacaoIntegracaoFoguete;
    }

    public void setDataSincronizacaoIntegracaoFoguete(Date dataSincronizacaoIntegracaoFoguete) {
        this.dataSincronizacaoIntegracaoFoguete = dataSincronizacaoIntegracaoFoguete;
    }

    public CancelamentoContratoVO getCancelamentoContratoVOOrignal() {
        return cancelamentoContratoVOOrignal;
    }

    public void setCancelamentoContratoVOOrignal(CancelamentoContratoVO cancelamentoContratoVOOrignal) {
        this.cancelamentoContratoVOOrignal = cancelamentoContratoVOOrignal;
    }

    public boolean isEhAditivo() {
        return ehAditivo;
    }

    public void setEhAditivo(boolean ehAditivo) {
        this.ehAditivo = ehAditivo;
    }

    public Integer getAditivo() {
        return aditivo;
    }

    public void setAditivo(Integer aditivo) {
        this.aditivo = aditivo;
    }
}
