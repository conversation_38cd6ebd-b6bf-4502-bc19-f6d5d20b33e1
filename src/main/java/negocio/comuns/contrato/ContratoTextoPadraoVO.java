package negocio.comuns.contrato;

import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.plano.PlanoTextoPadraoVO;

/**
 * Reponsável por manter os dados da entidade ContratoTextoPadrao. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class ContratoTextoPadraoVO extends SuperVO {

    protected Integer codigo;
    protected Integer contrato;
    protected PlanoTextoPadraoVO planoTextoPadrao;
    protected AditivoVO aditivo;

    /**
     * Construtor padrão da classe <code>ContratoTextoPadrao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ContratoTextoPadraoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ContratoTextoPadraoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ContratoTextoPadraoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
       
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
//        setCodigo(new Integer(0));
//        setContrato(new Integer(0));
//        setPlanoTextoPadrao(new PlanoTextoPadraoVO());
    }

    public Integer getContrato() {
    	if(contrato == null){
    		contrato = new Integer(0);
    	}
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public PlanoTextoPadraoVO getPlanoTextoPadrao() {
    	if(planoTextoPadrao == null){
    		planoTextoPadrao = new PlanoTextoPadraoVO();
    	}
        return planoTextoPadrao;
    }

    public void setPlanoTextoPadrao(PlanoTextoPadraoVO planoTextoPadrao) {
        this.planoTextoPadrao = planoTextoPadrao;
    }

    public Integer getCodigo() {
    	if(codigo == null){
    		codigo = new Integer(0);
    	}
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public AditivoVO getAditivo() {
        return aditivo;
    }

    public void setAditivo(AditivoVO aditivo) {
        this.aditivo = aditivo;
    }
}