/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pacto.priv.utils.Uteis;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;

import java.util.Date;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class AcessoClienteVO extends SuperVO {

    private Integer codigo = 0;
    private ClienteVO cliente = new ClienteVO();
    private Date dataHoraEntrada;
    private Date dataHoraSaida;
    private String sentido = "";
    private SituacaoAcessoEnum situacao;
    private LocalAcessoVO localAcesso = new LocalAcessoVO();
    private ColetorVO coletor = new ColetorVO();
    private UsuarioVO usuario = new UsuarioVO();
    private String nomeDiaSemanaAcesso = "";
    private String intervaloDataHoras="";
    private MeioIdentificacaoEnum  meioIdentificacaoEntrada;
    private MeioIdentificacaoEnum meioIdentificacaoSaida;
    private Date dataRegistro;
    @NaoControlarLogAlteracao
    private String tipoAcesso;
    private String ticket;
    private LiberacaoAcessoVO liberacaoAcessoVO;
    private String nomeCodEmpresaAcessou;
    private String nomeCodEmpresaOrigem;
    private String nomeCpfEmailClienteOrigem;


    private String key; // atributo transient.

    public MeioIdentificacaoEnum getMeioIdentificacaoEntrada() {
        return meioIdentificacaoEntrada;
    }

    public void setMeioIdentificacaoEntrada(MeioIdentificacaoEnum meioIdentificacaoEntrada) {
        this.meioIdentificacaoEntrada = meioIdentificacaoEntrada;
    }

    public MeioIdentificacaoEnum getMeioIdentificacaoSaida() {
        return meioIdentificacaoSaida;
    }

    public void setMeioIdentificacaoSaida(MeioIdentificacaoEnum meioIdentificacaoSaida) {
        this.meioIdentificacaoSaida = meioIdentificacaoSaida;
    }


    
    public AcessoClienteVO() {
        
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ColetorVO getColetor() {
        return coletor;
    }

    public void setColetor(ColetorVO coletor) {
        this.coletor = coletor;
    }

    public Date getDataHoraEntrada() {
        return dataHoraEntrada;
    }

    public void setDataHoraEntrada(Date dataHoraEntrada) {
        this.dataHoraEntrada = dataHoraEntrada;
    }

    public Date getDataHoraSaida() {
        return dataHoraSaida;
    }

    public void setDataHoraSaida(Date dataHoraSaida) {
        this.dataHoraSaida = dataHoraSaida;
    }

    public LocalAcessoVO getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(LocalAcessoVO localAcesso) {
        this.localAcesso = localAcesso;
    }

    public String getSentido() {
        return sentido;
    }

    public void setSentido(String sentido) {
        this.sentido = sentido;
    }

    public SituacaoAcessoEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoAcessoEnum situacao) {
        this.situacao = situacao;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public String getNomeUsuarioApresentar(){
        return usuario.getNome();
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public void setNomeDiaSemanaAcesso(String nomeDiaSemanaAcesso) {
        this.nomeDiaSemanaAcesso = nomeDiaSemanaAcesso;
    }

    public String getNomeDiaSemanaAcesso() {
        return super.retornaNomeDiaSemanaData(dataHoraEntrada);
    }
    public String getIntervaloDataHoras() {
        return intervaloDataHoras;
    }
    public void setIntervaloDataHoras(String intervaloDataHoras) {
        this.intervaloDataHoras = intervaloDataHoras;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }
    
    public String getDataHoraEntradaApresentar(){
        return Uteis.getData(dataHoraEntrada);
    }
    
    public String getDataComHoraEntradaApresentar(){
        return Uteis.getDataComHHMM(dataHoraEntrada);
    }
    
    public String getNomeCliente(){
        return getCliente().getPessoa().getNome();
    }
    
    public String getMeioIdentificacaoEntrada_Apresentar(){
        return getMeioIdentificacaoEntrada().getDescricao();
    }
    
    public String getNomeEmpresaCliente(){
        return getCliente().getEmpresa_Apresentar();
    }

    public String getTipoAcesso() {
        if (tipoAcesso == null) {
            tipoAcesso = "";
        }
        return tipoAcesso;
    }

    public void setTipoAcesso(String tipoAcesso) {
        this.tipoAcesso = tipoAcesso;
    }
    
    public String getMatriculaCliente(){
        return getCliente().getMatricula();
    }

    public String getTicket() {
        if (ticket == null) {
            ticket = "";
        }
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public LiberacaoAcessoVO getLiberacaoAcessoVO() {
        if (liberacaoAcessoVO == null){
            liberacaoAcessoVO = new LiberacaoAcessoVO();
        }
        return liberacaoAcessoVO;
    }

    public void setLiberacaoAcessoVO(LiberacaoAcessoVO liberacaoAcessoVO) {
        this.liberacaoAcessoVO = liberacaoAcessoVO;
    }

    public String getNomeCodEmpresaAcessou() {
        return nomeCodEmpresaAcessou;
    }

    public void setNomeCodEmpresaAcessou(String nomeCodEmpresaAcessou) {
        this.nomeCodEmpresaAcessou = nomeCodEmpresaAcessou;
    }

    public String getNomeCodEmpresaOrigem() {
        return nomeCodEmpresaOrigem;
    }

    public void setNomeCodEmpresaOrigem(String nomeCodEmpresaOrigem) {
        this.nomeCodEmpresaOrigem = nomeCodEmpresaOrigem;
    }

    public String getNomeCpfEmailClienteOrigem() {
        return nomeCpfEmailClienteOrigem;
    }

    public void setNomeCpfEmailClienteOrigem(String nomeCpfEmailClienteOrigem) {
        this.nomeCpfEmailClienteOrigem = nomeCpfEmailClienteOrigem;
    }

    public String getMatriculaCliente_Apresentar() {
        if (cliente != null && !UteisValidacao.emptyNumber(cliente.getCodigo())) {
            return getCliente().getMatricula();
        } else if (!UteisValidacao.emptyString(nomeCpfEmailClienteOrigem)){
            String dados[] = nomeCpfEmailClienteOrigem.split(";");
            return dados.length > 1 ? dados[0] : "";
        }
        return "";
    }

    public String getNomeCliente_Apresentar() {
        if (cliente != null && !UteisValidacao.emptyNumber(cliente.getCodigo())) {
            return getCliente().getPessoa().getNome();
        } else if (!UteisValidacao.emptyString(nomeCpfEmailClienteOrigem)){
            String dados[] = nomeCpfEmailClienteOrigem.split(";");
            return dados.length > 1 ? dados[1] : "";
        }
        return "";
    }
}
