package negocio.comuns.utilitarias;

import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.faces.context.FacesContext;
import javax.imageio.ImageIO;
import javax.mail.AuthenticationFailedException;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.Part;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.SuperTO;


import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayEnvioEmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.MalaDiretaEnviadaVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.financeiro.enumerador.TagReguaCobrancaPactoPayEnum;
import org.apache.commons.io.FilenameUtils;
import org.hazlewood.connor.bottema.emailaddress.EmailAddressCriteria;
import org.json.JSONObject;
import org.simplejavamail.email.Email;
import org.simplejavamail.email.EmailBuilder;
import org.simplejavamail.email.EmailPopulatingBuilder;
import org.simplejavamail.mailer.Mailer;
import org.simplejavamail.mailer.MailerBuilder;
import org.simplejavamail.mailer.config.TransportStrategy;
import org.apache.commons.lang.StringUtils;
import servicos.BounceService;
import servicos.integracao.sendy.services.SendyImpl;
import servicos.integracao.sendy.to.CampaignTO;
import servicos.integracao.sendy.to.SubscriberTO;
import servicos.operacoes.MailingItensController;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

/**
 * <AUTHOR> Maciel
 */
public class UteisEmail extends SuperTO {

    private static final long serialVersionUID = 3048499584639227602L;
    private static final String SSL_FACTORY = "javax.net.ssl.SSLSocketFactory";
    private static final int timeout_ms = 5000;
    public int timeout_read_write_ms = 60000;
    public static Map<String, String> EMAILS_BOUNCE = null;
    private static String[] DOMAINS_BOUNCE = {"hotmail.fr"};
    public String emailRemet;
    public String smtpPadrao;
    public String loginServidorSmtp;
    public String senhaServidorSmtp;
    public boolean conexaoSegura;
    public boolean iniciarTLS;
    public String portaPadrao;
    public boolean usaSmtps;
    private UsuarioVO remetente;
    private String emailReponderPara;
    private String nomeRemet;
    private String assunto;
    private Map<String, File> mapaAnexo = new HashMap<String, File>();
    private List<ImageEmailHtml> listaImagem = new ArrayList<ImageEmailHtml>();
    private int tentarNovamente = 1;
    private int tentativa = 1;
    private String chave;
    private EmpresaVO empresaVO;
    private MalaDiretaVO malaDiretaVO;
    private PactoPayComunicacaoVO pactoPayComunicacaoVO;
    private PactoPayEnvioEmailVO pactoPayEnvioEmailVO;


    public UteisEmail() throws Exception {
        povoarEmailsEmBounce();
    }

    public UteisEmail novo(String assunto, ConfiguracaoSistemaCRMVO config) throws Exception {
        inicializarDados(config);
        this.assunto = assunto;
        povoarEmailsEmBounce();
        return this;
    }

    /**
     * - Recupera as configurações de email, obtidas em configuracaoSistema. -
     * Recupera o nome do remetente padrão, o nome da instituição educacional. -
     * Se necessário formata a mensagem utilizando o template HTML padrão.
     *
     * @throws Exception
     */
    private void inicializarDados(ConfiguracaoSistemaCRMVO config) throws Exception {
        this.remetente = null;
        this.assunto = "";
        remetente = null;
        mapaAnexo = new HashMap<String, File>();
        listaImagem = new ArrayList<ImageEmailHtml>();
        preencherConfiguracaoEmailPadrao(config);
    }

    public void preencherConfiguracaoEmailPadrao(ConfiguracaoSistemaCRMVO config) throws Exception {
        if (config == null) {
            throw new Exception("");
        }
        // Setando configurações de email padrão
        loginServidorSmtp = config.getLogin();
        senhaServidorSmtp = config.getSenha();
        smtpPadrao = config.getMailServer();
        emailRemet = config.getEmailPadrao();
        portaPadrao = config.getPortaServer();
        nomeRemet = config.getRemetentePadrao();
        conexaoSegura = config.isConexaoSegura();
        iniciarTLS = config.isIniciarTLS();
        emailReponderPara = config.getEmailPadrao();
        usaSmtps = config.getUsaSMTPS();

        if (smtpPadrao == null || loginServidorSmtp == null || senhaServidorSmtp == null || emailRemet == null) {
            // verifico mais uma vez se existe uma configuracao de e-mail
            throw new Exception("");
        }
    }
    
    public void preencherConfiguracaoEmailPadrao(final String login,
            final String senha, final String hostName, final String portaPadrao, 
            final String emailRemetente,
            final String nomeRemetente, final String assunto, boolean conexaoSegura, boolean iniciarTLS) throws Exception {
        // Setando configurações de email padrão
        this.loginServidorSmtp = login;
        this.senhaServidorSmtp = senha;
        this.smtpPadrao = hostName;
        this.emailRemet = emailRemetente;
        this.portaPadrao = portaPadrao == null ? "587" : portaPadrao;
        this.nomeRemet = nomeRemetente;
        this.conexaoSegura = conexaoSegura;
        this.iniciarTLS = iniciarTLS;
        this.assunto = assunto;

        if (smtpPadrao == null || loginServidorSmtp == null || senhaServidorSmtp == null || emailRemet == null) {
            // verifico mais uma vez se existe uma configuracao de e-mail
            throw new Exception("");
        }
    }

    /**
     * Adiciona um anexo ao email.
     *
     * @param nomeAnexo
     * @param anexo
     * @return
     */
    public UteisEmail addAnexo(String nomeAnexo, File anexo) {
        this.mapaAnexo.put(nomeAnexo, anexo);
        return this;
    }

    /**
     * Caso este método nao seja utilizado, o rementente será a instituição
     * educacional.
     *
     * @param remetente
     * @return
     */
    public UteisEmail setRemetente(UsuarioVO remetente) {
        this.remetente = remetente;
        this.nomeRemet = remetente.getNome();
        return this;
    }

    public UteisEmail setRemetentePersonalizado(String remetentePersonalizado) {
        this.nomeRemet = remetentePersonalizado;
        return this;
    }

    public UteisEmail addDestinatarios(List<PessoaVO> listaDestinatario, MalaDiretaVO mensagem) {
        gerarListaMalaDiretaEnviada(listaDestinatario, mensagem);
        return this;
    }

    public UteisEmail addDestinatario(PessoaVO destinatario, MalaDiretaVO mensagem) {
        gerarListaMalaDiretaEnviada(destinatario, mensagem);
        return this;
    }

    /**
     * Adiciona todos os arquivos contidos no diretorio informado na lista de
     * imagens.
     *
     * @param diretorioImagens
     * @return
     */
    public UteisEmail addImagensEmDiretorio(ImageEmailHtml diretorioImagens) {
        for (File file : diretorioImagens.getArquivo().listFiles()) {
            listaImagem.add(diretorioImagens);
        }
        return this;
    }

    /**
     * Adiciona um arquivo de imagem ao email.
     *
     * @param imagem
     * @return
     */
    public UteisEmail addImagem(ImageEmailHtml imagem) {
        this.listaImagem.add(imagem);
        return this;
    }

    /**
     * Metodo que cria uma imagem a partir de um array de bytes no caminho que
     * foi passado com o nome passado.
     *
     * Obs.: - Caso o diretorio nao exista ele cria;
     *
     * Autor: Pedro Y. Saito Criado em 08/04/2011
     */
    public static void criarImagem(String path, byte[] imagem, String arquivo) throws Exception {
        if ((path == null) || ("".equals(path))) {
            return;
        }
        if ((imagem == null) || (imagem.length <= 0)) {
            return;
        }
        if ((arquivo == null) || ("".equals(arquivo))) {
            return;
        }

        //Criando um apontamento para o diretorio
        File arq = new File(path);
        //Verificando se o diretorio existe
        if (!arq.exists()) {
            //Criando o diretorio
            arq.mkdirs();
        }

        path = path.replaceAll("\\\\", "/");

        arq = new File(path + arquivo.trim());

        if (!arq.exists()) {
            InputStream in = new ByteArrayInputStream(imagem);
            BufferedImage outImage = ImageIO.read(in);

            if (outImage != null) {
                String[] extensao = arquivo.split("\\.");
                if ("png".equalsIgnoreCase(extensao[extensao.length - 1])) {
                    ImageIO.write(outImage, "PNG", arq);
                } else {
                    ImageIO.write(outImage, "JPEG", arq);
                }
            }
        }
    }

    /**
     * Método que envia um email, para um destinatário por vez.
     *
     * @throws ConsistirException
     * @throws Exception
     */
    public void enviarEmail(String emailDest, String nomeDest, String mensagem, String nomeEmpresa) throws Exception {
        enviarEmail(emailDest, nomeDest, mensagem, nomeEmpresa, false,null,0,0);
    }

    public void enviarEmail(String emailDest, String nomeDest, String mensagem, String nomeEmpresa, boolean usarSendy, CampaignTO campaignTO) throws Exception {
        if (usarSendy && campaignTO != null) {
            enviarEmailComSendy(new String[]{emailDest}, mensagem, assunto, nomeEmpresa, campaignTO, 0, 0, null, null);
        } else {
            if (usaSmtps) {
                this.sendBySimpleJavaMail(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
            } else {
                this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
            }
        }
    }
    public void enviarEmail(String emailDest, String nomeDest, String mensagem, String nomeEmpresa, boolean usarSendy, CampaignTO campaignTO, Integer idMailing, Integer codigoEmpresa) throws Exception {
        if (usarSendy && campaignTO != null) {
            enviarEmailComSendy(new String[]{emailDest}, mensagem, assunto, nomeEmpresa, campaignTO, idMailing, codigoEmpresa, null, null);
        } else {
            if (usaSmtps) {
                this.sendBySimpleJavaMail(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
            } else {
                this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
            }
        }
    }

    public void configurarMail(String emailDest, String mensagem, String nomeEmpresa) throws Exception {
        configurarMail(emailDest, mensagem, nomeEmpresa, false, null );
    }

    public void configurarMail(String emailDest, String mensagem, String nomeEmpresa, boolean usarSendy, CampaignTO campaignTO) throws Exception {
        try {
            if (usarSendy && campaignTO != null) {
                enviarEmailComSendy(new String[]{emailDest}, mensagem, "Teste", nomeEmpresa, campaignTO,0,0, null, null);
            } else {
                if (usaSmtps) {
                    this.sendBySimpleJavaMail(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                } else {
                    String emailRemetente = loginServidorSmtp;
                    if (loginServidorSmtp.contains("@")) {
                        try {
                            emailRemetente = loginServidorSmtp.substring(loginServidorSmtp.indexOf("@") + 1);
                        } catch (Exception e) {
                            tentarNovamente = 0;
                        }
                    } else if (loginServidorSmtp.contains("=")) {
                        try {
                            emailRemetente = loginServidorSmtp.substring(loginServidorSmtp.indexOf("=") + 1);
                        } catch (Exception e) {
                            tentarNovamente = 0;
                        }
                    }
                    switch (tentativa) {
                        case 1:
                            if (emailRemetente.toLowerCase().contains("gmail") || emailRemetente.toLowerCase().contains("pacto")) {
                                smtpPadrao = "smtp.gmail.com";
                                conexaoSegura = false;
                                iniciarTLS = true;
                                portaPadrao = "587";
                                tentativa = 1;
                                this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                                break;
                            }
                        case 2:
                            if (emailRemetente.toLowerCase().contains("outlook") || emailRemetente.toLowerCase().contains("hotmail")) {
                                smtpPadrao = "smtp-mail.outlook.com";
                                conexaoSegura = false;
                                iniciarTLS = true;
                                portaPadrao = "587";
                                tentativa = 2;
                                this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                                break;
                            }
                        case 3:
                            if (emailRemetente.toLowerCase().contains("yahoo")) {
                                smtpPadrao = "smtp.mail.yahoo.com";
                                conexaoSegura = true;
                                iniciarTLS = false;
                                portaPadrao = "465";
                                tentativa = 3;
                                this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                                break;
                            }
                        case 4:
                            if (emailRemetente.toLowerCase().contains("sendgrid")) {
                                smtpPadrao = "smtp.sendgrid.net";
                                conexaoSegura = true;
                                portaPadrao = "465";
                                iniciarTLS = false;
                                tentativa = 4;
                                this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                                break;
                            }
                        case 5:
                            if (emailRemetente.toLowerCase().contains("terra")) {
                                smtpPadrao = "smtp.vix.terra.com.br";
                                conexaoSegura = false;
                                iniciarTLS = true;
                                portaPadrao = "587";
                                tentativa = 5;
                                this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                                break;
                            }
                        case 6:
                            if (emailRemetente.toLowerCase().contains("uol")) {
                                smtpPadrao = "smtp.uol.com.br";
                                conexaoSegura = false;
                                iniciarTLS = true;
                                portaPadrao = "587";
                                tentativa = 6;
                                this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                                break;
                            }

                        case 7:
                            smtpPadrao = "smtp.gmail.com";
                            conexaoSegura = false;
                            iniciarTLS = true;
                            portaPadrao = "587";
                            tentativa = 7;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 8:
                            smtpPadrao = "smtp-mail.outlook.com";
                            conexaoSegura = false;
                            iniciarTLS = true;
                            portaPadrao = "587";
                            tentativa = 8;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 9:
                            smtpPadrao = "smtp.mail.yahoo.com";
                            conexaoSegura = true;
                            iniciarTLS = false;
                            portaPadrao = "465";
                            tentativa = 9;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 10:
                            smtpPadrao = "smtp.sendgrid.net";
                            conexaoSegura = true;
                            portaPadrao = "465";
                            iniciarTLS = false;
                            tentativa = 10;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 11:
                            smtpPadrao = "smtp.vix.terra.com.br";
                            conexaoSegura = false;
                            iniciarTLS = true;
                            portaPadrao = "587";
                            tentativa = 11;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;
                        case 12:
                            smtpPadrao = "smtp.uol.com.br";
                            conexaoSegura = false;
                            iniciarTLS = true;
                            portaPadrao = "587";
                            tentativa = 12;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 13:
                            smtpPadrao = "smtp.kinghost.net";
                            conexaoSegura = true;
                            iniciarTLS = false;
                            portaPadrao = "465";
                            tentativa = 13;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 14:
                            smtpPadrao = "smtp." + emailRemetente;
                            conexaoSegura = true;
                            iniciarTLS = false;
                            portaPadrao = "465";
                            tentativa = 14;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 15:
                            smtpPadrao = "smtp." + emailRemetente;
                            conexaoSegura = false;
                            iniciarTLS = true;
                            portaPadrao = "587";
                            tentativa = 15;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 16:
                            smtpPadrao = "smtp.uhserver.com";
                            conexaoSegura = false;
                            iniciarTLS = true;
                            portaPadrao = "587";
                            tentativa = 16;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 17:
                            smtpPadrao = "smtp.vix.terra.com.br";
                            conexaoSegura = true;
                            iniciarTLS = false;
                            portaPadrao = "465";
                            tentativa = 17;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 18:
                            smtpPadrao = "smtp." + emailRemetente;
                            portaPadrao = "26";
                            conexaoSegura = true;
                            iniciarTLS = false;
                            tentativa = 18;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 19:
                            smtpPadrao = "smtplw.com.br";
                            conexaoSegura = false;
                            iniciarTLS = true;
                            portaPadrao = "587";
                            tentativa = 19;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 20:
                            smtpPadrao = "smtp.hostinger.com";
                            conexaoSegura = true;
                            iniciarTLS = false;
                            portaPadrao = "465";
                            tentativa = 20;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 21:
                            smtpPadrao = "email-smtp.us-west-2.amazonaws.com";
                            conexaoSegura = true;
                            iniciarTLS = false;
                            portaPadrao = "465";
                            tentativa = 21;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 22:
                            smtpPadrao = "email-smtp.us-west-2.amazonaws.com";
                            conexaoSegura = false;
                            iniciarTLS = true;
                            portaPadrao = "587";
                            tentativa = 22;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 23:
                            smtpPadrao = "smtp.zoho.com";
                            conexaoSegura = false;
                            iniciarTLS = true;
                            portaPadrao = "587";
                            tentativa = 23;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 24:
                            smtpPadrao = "smtp.zoho.com";
                            conexaoSegura = true;
                            iniciarTLS = false;
                            portaPadrao = "465";
                            tentativa = 24;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 25:
                            smtpPadrao = "smtp.titan.email";
                            conexaoSegura = true;
                            iniciarTLS = true;
                            portaPadrao = "465";
                            tentativa = 25;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;

                        case 26:
                            smtpPadrao = "email-ssl.com.br";
                            conexaoSegura = true;
                            iniciarTLS = false;
                            portaPadrao = "465";
                            tentativa = 26;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;
                        case 27:
                            smtpPadrao = "mail.clubjoyacademia.com.br";
                            conexaoSegura = true;
                            iniciarTLS = false;
                            portaPadrao = "465";
                            tentativa = 27;
                            this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa);
                            break;
                        default:
                            tentarNovamente = 0;
                            throw new ConsistirException("As configurações de email estão incorretas, verifique se \"Usa protocolo SMTPS\" está desmarcada. Se estiver entre em contato com o administrador.");
                    }
                }
            }
        }catch (Exception e){
            if(!usaSmtps) {
                if (tentarNovamente == 1) {
                    tentativa += 1;
                    configurarMail(emailDest, "Este é um teste de email pelo ZillyonWeb.", nomeEmpresa);
                }else throw new ConsistirException("As configurações de email estão incorretas. Entre em contato com o administrador.");
            }
            else throw new ConsistirException("As configurações de email estão incorretas, verifique se \"Usa protocolo SMTPS\" está desmarcada. Se estiver entre em contato com o administrador.");
        }
    }

    public String enviarEmailComSendy(String[] emailDest, String mensagem, String oAssunto, String nomeEmpresa, CampaignTO campaignTO,
                                      Integer idMailing, Integer codigoEmpresa, List<MailingItensController.MailingItem> emails, String appKey) throws ConsistirException {
        boolean pactoPayReguaCobranca = ((this.getPactoPayComunicacaoVO() != null && !UteisValidacao.emptyNumber(this.getPactoPayComunicacaoVO().getCodigo())) ||
                (this.getPactoPayEnvioEmailVO() != null && !UteisValidacao.emptyNumber(this.getPactoPayEnvioEmailVO().getCodigo())));
        if ((emailDest != null && emailDest.length > 0) || (emails != null && !emails.isEmpty())) {
            String nomeCampaings ="";
            if ((campaignTO.getAppKey() == null || campaignTO.getAppKey().equalsIgnoreCase("null") || campaignTO.getAppKey().trim().isEmpty()) || pactoPayReguaCobranca) {
                nomeCampaings = getChave() +"|"+  codigoEmpresa + "|" + idMailing;
                if (this.getPactoPayEnvioEmailVO() != null && !UteisValidacao.emptyNumber(this.getPactoPayEnvioEmailVO().getCodigo())) {
                    nomeCampaings = (getChave() +"|"+  codigoEmpresa + "|" + idMailing + "|LISTA");
                }
                campaignTO.setAppKey(getChave());
                campaignTO.setTitle(nomeCampaings);
            } else {
                nomeCampaings = oAssunto;
                campaignTO.setTitle(nomeCampaings);
            }

            if (!UteisValidacao.emptyString(appKey)) {
                campaignTO.setAppKey(appKey);
            }

            if (Uteis.isAmbienteDesenvolvimentoTeste()){
                campaignTO.setAppKey(Uteis.getChaveEmpresaSwarmParaSendy());
            }

            SendyImpl sendyImpl = new SendyImpl();
            campaignTO.setFromName(nomeEmpresa);
            campaignTO.getListTO().setNome("Lista para " +  nomeCampaings +"|"+ Calendario.getData("dd-MM-yyyy-HH-mm-ss-mm"));
            campaignTO.setHtmlText(mensagem);
            campaignTO.setSubject(oAssunto);

            if ((this.getPactoPayComunicacaoVO() != null &&
                    !UteisValidacao.emptyNumber(this.getPactoPayComunicacaoVO().getCodigo()))
                    || (this.getPactoPayEnvioEmailVO() != null &&
                            !UteisValidacao.emptyNumber(this.getPactoPayEnvioEmailVO().getCodigo()))) {
                campaignTO.getListTO().setCustom_fields(getCustomFieldsSendyPactoPay());
            } else if (malaDiretaVO != null && malaDiretaVO.getMensagem().contains("TAG_PIX")) {
                campaignTO.getListTO().setCustom_fields(getCustomFieldsSendyPix());
            } else {
                campaignTO.getListTO().setCustom_fields(getCustomFieldsSendy());
            }

            campaignTO.getListTO().setAppKey(campaignTO.getAppKey());
            campaignTO.getListTO().setSubscribers(new ArrayList<>());
            if(emailDest == null && emails != null){
                if(PropsService.isTrue(PropsService.useBounceService)){
                    emails = removeBounceEmails(emails);
                }
                for (MailingItensController.MailingItem email : emails) {

                    SubscriberTO subscriberTO = new SubscriberTO(email.getValor(), email.getValor(), email.getCustom_fields(),email.getBounced());
                    campaignTO.getListTO().getSubscribers().add(subscriberTO);
                }
            }else {
                if(PropsService.isTrue(PropsService.useBounceService)){
                    emailDest = removeBounceEmails(emailDest);
                }
                for (String subscribe : emailDest) {
                    SubscriberTO subscriberTO = new SubscriberTO(subscribe, subscribe, "",0);
                    campaignTO.getListTO().getSubscribers().add(subscriberTO);
                }
            }
            Uteis.logarDebug(String.format("Campaign %s Subscribers List => %s", campaignTO.getName(), campaignTO.getListTO().getSubscribers()));
            String retorno = sendyImpl.sendCampaign(campaignTO);
            if (!retorno.startsWith("Enviado com sucesso!")) {
                String msg = "Não foi possível enviar e-mail via Sendy. Entre em contato com o administrador. Motivo da falha: " + retorno;
                Uteis.logarDebug(msg);
                throw new ConsistirException(msg);
            }
            if (pactoPayReguaCobranca) {
                JSONObject json = new JSONObject();
                json.put("idMailing", idMailing);
                json.put("codigoEmpresa", codigoEmpresa);
                json.put("appKey", appKey);
                json.put("chave", this.getChave());
                json.put("nomeCampaings", nomeCampaings);
                json.put("retorno", retorno);
                json.put("envio", campaignTO.getJSON());
                return json.toString();
            } else {
                return retorno;
            }
        } else {
            return "Emails vazios emailDest e emails";
        }
    }

    private String getCustomFieldsSendy() {
        String separador = "%s%";

        return "TAG_NOME:Text"
                +separador+"TAG_PNOME:Text"
                +separador+"TAG_CODCLIENTE:Text"
                +separador+"TAG_EMAIL_CLIENTE:Text"
                +separador+"TAG_BOLETO:Text"
                +separador+"TAG_PARCELAS_COBRANCA:Text"
                +separador+"TAG_PAGONLINE:Text"
                +separador+"TAG_PESQUISA:Text";
    }

    private String getCustomFieldsSendyPix() {
        String separador = "%s%";

        return "ZW_URL:Text"
                +separador+"VALOR_PIX:Text"
                +separador+"EMPRESA_PIX:Text"
                +separador+"QRCODE_URL_PIX:Text";
    }

    private String getCustomFieldsSendyPactoPay() {
        String separador = "%s%";
        String tipo = ":Text";
        StringBuilder customFields = new StringBuilder();

        for (TagReguaCobrancaPactoPayEnum tag : TagReguaCobrancaPactoPayEnum.values()) {
            if (customFields.length() > 0) {
                customFields.append(separador);
            }
            customFields.append(tag.getTag()).append(tipo);
        }
        return customFields.toString();
    }

    /**
     * Método que envia um e-mail para vários destinatários com BCC (Cópia
     * Oculta) ou não de uma só vez.
     *
     * @throws ConsistirException
     * @throws Exception
     */
    public void enviarEmailN(String[] emailDest, String mensagem, String oAssunto, String nomeEmpresa) throws Exception {
         enviarEmailN(emailDest, mensagem, oAssunto, nomeEmpresa, null);
    }

    public List<MailingItensController.MailingItem> validarEmailsEnviarHoje(int emailEnviadosHoje, List<MailingItensController.MailingItem> emailDest, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO){
       List<MailingItensController.MailingItem> emails = new ArrayList<>();
        if(configuracaoSistemaCRMVO != null && !UteisValidacao.emptyNumber(configuracaoSistemaCRMVO.getLimiteDiarioEmails())) {
            int enviadosHojeComAEnviarAgora = emailEnviadosHoje + emailDest.size();
            if (enviadosHojeComAEnviarAgora <= configuracaoSistemaCRMVO.getLimiteDiarioEmails()) {
                return emails;
            } else {
                int enviadosHojeComAEnviarAgora2 = emailEnviadosHoje;
                for (int i = 0; i < emailDest.size(); i++) {
                    if (enviadosHojeComAEnviarAgora2 < configuracaoSistemaCRMVO.getLimiteDiarioEmails()) {
                        enviadosHojeComAEnviarAgora2++;
                    } else {
                        int qtdPosicaoEmailDest = emailDest.size() - 1;
                        while (i <= qtdPosicaoEmailDest) {
                            emails.add(emailDest.get(qtdPosicaoEmailDest));
                            emailDest.remove(qtdPosicaoEmailDest);
                            qtdPosicaoEmailDest--;
                        }
                        break;
                    }
                }
                return emails;
            }
        }else{
            return emails;
        }
    }

    public List<MailingItensController.MailingItem> validarEmailsEnviarMensal(int emailEnviadosMensal, List<MailingItensController.MailingItem> emailDest, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO){
        List<MailingItensController.MailingItem> emails = new ArrayList<>();
        if(configuracaoSistemaCRMVO != null && configuracaoSistemaCRMVO.getIntegracaoPacto()) {
            int enviadosMensalComAEnviarAgora = emailEnviadosMensal + emailDest.size();
            if (enviadosMensalComAEnviarAgora <= configuracaoSistemaCRMVO.getLimiteMensalPacto()) {
                return emails;
            } else {
                int enviadosMensalComAEnviarAgora2 = emailEnviadosMensal;
                for (int i = 0; i < emailDest.size(); i++) {
                    if (enviadosMensalComAEnviarAgora2 < configuracaoSistemaCRMVO.getLimiteMensalPacto()) {
                        enviadosMensalComAEnviarAgora2++;
                    } else {
                        int qtdPosicaoEmailDest = emailDest.size() - 1;
                        while (i <= qtdPosicaoEmailDest) {
                            emails.add(emailDest.get(qtdPosicaoEmailDest));
                            emailDest.remove(qtdPosicaoEmailDest);
                            qtdPosicaoEmailDest--;
                        }
                        break;
                    }
                }
                return emails;
            }
        }else{
            return emails;
        }
    }

    public void enviarEmailN(String[] emailDest, String mensagem, String oAssunto, String nomeEmpresa, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO,
                             Integer idMailing, Integer codigoEmpresa, List<MailingItensController.MailingItem> emails) throws Exception {
        CampaignTO campaignTO = configuracaoSistemaCRMVO != null ? configuracaoSistemaCRMVO.preparaEnvioSendy() : null;
        if (configuracaoSistemaCRMVO != null && configuracaoSistemaCRMVO.getIntegracaoPacto() && campaignTO != null) {
            enviarEmailComSendy((emails == null ? emailDest : null), mensagem, oAssunto, nomeEmpresa, campaignTO,idMailing,codigoEmpresa, emails, null);
        } else {
            if (usaSmtps) {
                this.sendBySimpleJavaMail(emailDest, mensagem, oAssunto, true, nomeEmpresa);
            } else {
                this.send(emailDest, mensagem, oAssunto, true, nomeEmpresa);
            }
        }
    }

    public void enviarEmailN(String[] emailDest, String mensagem, String oAssunto, String nomeEmpresa, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) throws Exception {
        enviarEmailN(emailDest, mensagem, oAssunto, nomeEmpresa, configuracaoSistemaCRMVO, null, null, null);
    }

    public void enviarEmailN(String[] emailDest, String mensagem, String oAssunto, String nomeEmpresa,
                             ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO, String appKey,
                             Integer idMailing) throws Exception {
        CampaignTO campaignTO = configuracaoSistemaCRMVO != null ? configuracaoSistemaCRMVO.preparaEnvioSendy() : null;
        if (configuracaoSistemaCRMVO != null && configuracaoSistemaCRMVO.getIntegracaoPacto() && campaignTO != null) {
            enviarEmailComSendy(emailDest, mensagem, oAssunto, nomeEmpresa, campaignTO,idMailing,0, null, appKey);
        } else {
            if (usaSmtps) {
                this.sendBySimpleJavaMail(emailDest, mensagem, oAssunto, true, nomeEmpresa);
            } else {
                this.send(emailDest, mensagem, oAssunto, true, nomeEmpresa);
            }
        }
    }

    public String enviarEmailPactoPay(String mensagem, String assunto, String nomeEmpresa,
                                      ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO, String appKey,
                                      Integer idMailing, List<MailingItensController.MailingItem> mailingItems,
                                      Integer codigoEmpresa) throws Exception {
        CampaignTO campaignTO = configuracaoSistemaCRMVO != null ? configuracaoSistemaCRMVO.preparaEnvioSendy() : null;
        if (configuracaoSistemaCRMVO != null && configuracaoSistemaCRMVO.getIntegracaoPacto() && campaignTO != null) {
            return enviarEmailComSendy(null, mensagem, assunto, nomeEmpresa, campaignTO, idMailing, codigoEmpresa, mailingItems, appKey);
        } else {
            throw new Exception("Configuração de email inválido");
        }
    }


    private String retirarTagsNaoPreenchidas(String mensagem) {
        return mensagem.replace("TAG_NOME", "")
                .replace("TAG_PNOME", "")
                .replace("TAG_PESQUISA", "")
                .replace("TAG_BOLETO", "")
                .replace("TAG_PAGONLINE", "")
                .replace("TAG_CADCARTAO", "")
                .replace("ENDERECO_TAG", "")
                .replace("CIDADE_ESTADO_TAG", "")
                .replace("TELEFONE_TAG", "")
                .replace("WEB_SITE_TAG", "")
                .replace("LOGO_EMPRESA_TAG", "")
                .replace("NOME_EMPRESA", "")
                .replace("TAG_PARCELA_DESCRICAO", "")
                .replace("TAG_PARCELA_VALOR", "")
                .replace("TAG_CODIGO_RETORNO", "")
                .replace("TAG_ACAO_REALIZAR_RETORNO", "")
                .replace("TAG_PARCELA_VENCIMENTO", "")
                .replace("TAG_CARTAO_MASCARADO", "")
                .replace("TAG_DESCRICAO_RETORNO", "")
                .replace("TAG_DATA_RETORNO", "")
                .replace("TAG_DATA_RETORNO", "")
                .replace("TAG_DATA_CADASTRO", "")
                .replace("TAG_DESCRICAO_FINANCEIRA", "")
                .replace("USUARIO_VENDAS_ONLINE", "")
                .replace("TAG_VENCIMENTO_PLANO", "")
                .replace("TAG_CARTAO_MASCARADO", "")
                .replace("SENHA_VENDAS_ONLINE", "");
    }

    public void sendBySimpleJavaMail(String[] emailDest, String mensagem, String oAssunto, boolean copiaOculta, String nomeEmpresa) throws Exception{
        mensagem = retirarTagsNaoPreenchidas(mensagem);
        removeBounceEmails(emailDest);
        if (emailDest != null && emailDest.length > 0) {


            if (emailDest.length <= 1) {copiaOculta = false; }
            boolean debug = false;

            int port = (conexaoSegura ? 465 : iniciarTLS ? 587 : 25);
            TransportStrategy ts = (conexaoSegura ? TransportStrategy.SMTPS : iniciarTLS ? TransportStrategy.SMTP_TLS : TransportStrategy.SMTP);
            if (!portaPadrao.isEmpty()) {port = Integer.parseInt(portaPadrao.trim());}

            final Mailer mailer = buildMailer(smtpPadrao, port, loginServidorSmtp.trim(), senhaServidorSmtp.trim(), ts, debug);

            listaImagem.addAll(gerarListaImagensHtmlParaEmail(mensagem));

            EmailPopulatingBuilder eb = EmailBuilder.startingBlank();

            eb.withHeader("chave", getChave());
            eb.withHeader("empresa", getEmpresaVO().getCodigo().toString());
            eb.withHeader("malaDireta", getMalaDiretaVO().getCodigo().toString());
            eb.withHeader("dataHora", Uteis.getDataComHora(Calendario.hoje()));


            if(copiaOculta){
                eb.bccMultiple(emailDest);
            }else{
                eb.toMultiple(emailDest);
            }
            eb.from(new InternetAddress(getEmailRemet(), getRemetenteFormatado(nomeEmpresa, nomeRemet)));
            eb.withSubject(oAssunto);
            if (emailReponderPara != null && !emailReponderPara.isEmpty()) {
                eb.withReplyTo(new InternetAddress(emailReponderPara));
            }

            //Anexando imagem
            if (!listaImagem.isEmpty()) {
                for (ImageEmailHtml imagem : listaImagem) {
                    String cidName = FilenameUtils.removeExtension(imagem.getArquivo().getName());
                    mensagem = mensagem.replaceAll("src=\"" + imagem.getCaminhoReplace(),
                            "src=\"cid:" + cidName);
                    eb.withEmbeddedImage(cidName, new FileDataSource(imagem.getArquivo()));
                }
            }

            // enviando anexo
            if (!mapaAnexo.isEmpty()) {
                for (String nomeAnexo : mapaAnexo.keySet()) {
                    File anexo = mapaAnexo.get(nomeAnexo);
                    eb.withAttachment(nomeAnexo, new FileDataSource(anexo));
                }
            }

            eb.withHTMLText(mensagem);
            Email email = eb.buildEmail();

            mailer.sendMail(email);


        }

    }

    private final Mailer buildMailer(String host, int port, String userName, String password, TransportStrategy strategy, boolean debug) {
        return MailerBuilder
                .withSMTPServer(host, port, userName, password)
                .withTransportStrategy(strategy)
                .withDebugLogging(debug)
                .withSessionTimeout(timeout_ms)
                .withEmailAddressCriteria(EmailAddressCriteria.RFC_COMPLIANT)
                .clearProxy()
                .buildMailer();
    }

    private void send(String[] emailDest, String mensagem,
            String oAssunto, boolean copiaOculta, String nomeEmpresa) throws Exception {
        mensagem = retirarTagsNaoPreenchidas(mensagem);

        if(PropsService.isTrue(PropsService.useBounceService)){
            emailDest = removeBounceEmails(emailDest);
        }

        if (emailDest != null && emailDest.length > 0) {
            if (emailDest.length <= 1) {
                copiaOculta = false;
            }
            boolean debug = false;
            Session session = null;
            try {
                String port = "";
                port = (conexaoSegura ? "465" : "587");
                if (null != portaPadrao){
                    if (!portaPadrao.isEmpty()) {
                        port = portaPadrao.trim();
                    }
                }
                Properties props = new Properties();
                props.put("mail.smtp.host", smtpPadrao);
                props.put("mail.smtp.auth", "true");
                props.put("mail.debug", debug);
                props.put("mail.smtp.port", port);
                props.put("mail.smtp.socketFactory.port", port);
                props.put("mail.smtp.connectiontimeout", timeout_ms);
                props.put("mail.smtp.timeout", timeout_read_write_ms);
                props.put("mail.smtp.writetimeout", timeout_read_write_ms);
                if (iniciarTLS) {
                    props.put("mail.smtp.starttls.enable", "true");
                }
                //caso usar SSL
                if (conexaoSegura) {
                    props.put("mail.smtp.socketFactory.class", SSL_FACTORY);
                }

                session = Session.getInstance(props,
                        new javax.mail.Authenticator() {
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(loginServidorSmtp.trim(),
                                senhaServidorSmtp.trim());
                    }
                });
                // com essa flag aqui ele imprimira todos os
                // dados da conexão e do envio,
                // setar isso para false se quiser que rode
                // no silent mode.
                session.setDebug(debug);

                MimeMultipart mpRoot = new MimeMultipart("mixed");
                MimeMultipart mpContent = new MimeMultipart("related");
                MimeBodyPart contentPartRoot = new MimeBodyPart();
                contentPartRoot.setContent(mpContent);
                mpRoot.addBodyPart(contentPartRoot);

                //enviando html
                MimeBodyPart mbp1 = new MimeBodyPart();
                listaImagem.addAll(gerarListaImagensHtmlParaEmail(mensagem));

                // enviando anexo
                if (!mapaAnexo.isEmpty()) {
                    for (String nomeAnexo : mapaAnexo.keySet()) {
                        File anexo = mapaAnexo.get(nomeAnexo);

                        MimeBodyPart mbp2 = new MimeBodyPart();
                        DataSource fds = new FileDataSource(anexo);
                        mbp2.setDisposition(Part.ATTACHMENT);
                        mbp2.setDataHandler(new DataHandler(fds));
                        mbp2.setFileName(nomeAnexo);
                        mpRoot.addBodyPart(mbp2);
                    }
                }
                List<BodyPart> bp = new ArrayList<BodyPart>();
                // adicionando as imagens do html
                if (!listaImagem.isEmpty()) {
                    for (ImageEmailHtml imagem : listaImagem) {
                        MimeBodyPart imagePart = new MimeBodyPart();
                        imagePart.attachFile(imagem.getArquivo());
                        imagePart.setHeader("Content-ID", "<" + imagem.getArquivo().getName() + ">");
                        bp.add(imagePart);

                        mensagem = mensagem.replace("src=\"" + imagem.getCaminhoReplace(),
                                "src=\"cid:" + imagem.getArquivo().getName());
                    }
                }
                mbp1.setHeader("Content-Type", "text/html; charset=\"iso-8859-1\"");
                mbp1.setContent(mensagem, mensagem.toLowerCase().contains("utf-8")? "text/html; charset=UTF-8" : "text/html; charset=iso-8859-1");
                mbp1.setHeader("Content-Transfer-Encoding", "quoted-printable");
                mpContent.addBodyPart(mbp1);
                for (BodyPart imagePart : bp) {
                    mpContent.addBodyPart(imagePart);
                }
                //preparar definitivamente o envio
                //
                //definir quem são os endereços destinatários
                InternetAddress[] emails = new InternetAddress[emailDest.length];
                for (int i = 0; i < emailDest.length; i++) {
                    String email = emailDest[i];
                    InternetAddress ia = new InternetAddress(email);
                    emails[i] = ia;
                }
                MimeMessage message = new MimeMessage(session);
                message.setSentDate(negocio.comuns.utilitarias.Calendario.hoje());
                message.setFrom(new InternetAddress(getEmailRemet(), getRemetenteFormatado(nomeEmpresa, nomeRemet)));
                if (emailReponderPara != null && !emailReponderPara.isEmpty()) {
                    message.setReplyTo(new InternetAddress[]{new InternetAddress(emailReponderPara)});                    
                }
                message.addRecipients(copiaOculta ? Message.RecipientType.BCC
                        : Message.RecipientType.TO, emails);
                message.setSubject(oAssunto);

                message.addHeader("chave", getChave());
                message.addHeader("empresa", getEmpresaVO().getCodigo().toString());
                message.addHeader("malaDireta", getMalaDiretaVO().getCodigo().toString());
                message.addHeader("dataHora", Uteis.getDataComHora(Calendario.hoje()));

                message.setContent(mpRoot);
                message.saveChanges();
                //enviar!
                if (PropsService.isTrue(PropsService.uteisEmailSend)) {
                    Transport.send(message);
                }
//                System.out.println("Enviou...");
            } catch (AuthenticationFailedException e) {
                Uteis.logar(e, UteisEmail.class);
                if (e.getMessage().contains("Username and Password not accepted")){
                    throw new ConsistirException("Usuário e/ou senha incorreta(s).");
                } else {
                    throw new ConsistirException("As configurações de email estão incorretas, entre em contato com o administrador.");
                }
            }
        }
    }

    public static boolean getValidEmail(String email) {
        Pattern p = Pattern.compile("^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$");
        Matcher m = p.matcher(email);
        return m.find();
    }

    protected void limparRecursosMemoria() {
        this.emailRemet = null;
        this.nomeRemet = null;
        this.assunto = null;
        this.smtpPadrao = null;
        this.loginServidorSmtp = null;
        this.senhaServidorSmtp = null;
    }

    public String getAssunto() {
        return assunto;
    }

    public String getEmailRemet() {
        return emailRemet;
    }

    public String getNomeRemet() {
        return nomeRemet;
    }

    public UsuarioVO getRemetente() {
        return remetente;
    }

    private void gerarListaMalaDiretaEnviada(List<PessoaVO> listaDestinatario, MalaDiretaVO mensagem) {
        for (PessoaVO destinatarioOriginal : listaDestinatario) {
            gerarListaMalaDiretaEnviada(destinatarioOriginal, mensagem);
        }
    }

    private void gerarListaMalaDiretaEnviada(PessoaVO destinatarioOriginal, MalaDiretaVO mensagem) {
        MalaDiretaEnviadaVO malaDiretaNova = novaMalaDireta(destinatarioOriginal, mensagem);
        mensagem.getMalaDiretaEnviadaVOs().add(malaDiretaNova);

    }

    private MalaDiretaEnviadaVO novaMalaDireta(PessoaVO destinatario, MalaDiretaVO mensagem) {
        MalaDiretaEnviadaVO malaDiretaEnviadaVO = new MalaDiretaEnviadaVO();
        malaDiretaEnviadaVO.setMalaDiretaVO(mensagem);
        malaDiretaEnviadaVO.getClienteVO().setPessoa(destinatario);
        return malaDiretaEnviadaVO;
    }

    /**
     * Operação responsável por varrer um conteúdo html procurando tags <img> e
     * gerar uma lista de imagens presentes nesse contexto. Este método utiliza
     * Regex (Regular Expressions).
     *
     * @param html Objeto da classe <code>String</code> que servirá de base para
     * a varredura.
     * @exception// Caso haja problemas da aplicação da expressão
     * regular.
     */
    public List gerarListaImagensHtmlParaEmail(String html) {

        ArrayList<ImageEmailHtml> lista = new ArrayList<ImageEmailHtml>();

        String source = html;

        //String regexTag = "<?\\s*\\w*\\s*src='?\\s*([\\w\\s%#\\/\\.;:_-])'?.*?>";
        String regexTag = "<?\\s*\\w*\\s*src\\s*=\\s*'?\\s*([\\w\\s%#\\/\\.;:_-]*)\\s*'?.*?>";

        Pattern pattern = Pattern.compile(regexTag);

        Matcher matcher = pattern.matcher(source);

        // Mostra as similaridades
        while (matcher.find()) {

            String temp = source.substring(matcher.start(), matcher.end());
            int ini = temp.indexOf("src=");
            int fim = temp.indexOf("\"", ini);
            fim = temp.indexOf("\"", fim + 1);
            String caminhoArquivo = temp.substring(ini + 5, fim);

            try {
                //
                String nomeArquivo = (getCaminhoDasImagens() + File.separator
                        + caminhoArquivo);
                nomeArquivo = nomeArquivo.replaceAll("////", File.separator);
                if (nomeArquivo != null) {
                    File imagem = new File(nomeArquivo);
                    if ((imagem != null) && (imagem.exists())) {
                        ImageEmailHtml imgEmail = new ImageEmailHtml();
                        imgEmail.setArquivo(imagem);
                        imgEmail.setCaminhoReplace(caminhoArquivo);
                        lista.add(imgEmail);
                    }
                }

            } catch (Exception ex) {
                Logger.getLogger(UteisEmail.class.getName()).log(Level.SEVERE, null, ex);
            }

        }

        return lista;

    }

    public static String getCaminhoDasImagens() {
        return PropsService.getPropertyValue(PropsService.pathImagensEmail);
    }

    public static void criarImagensEmailTreino(Map<String, File> mapaImagens) throws Exception {
        String path = UteisEmail.getCaminhoDasImagens() + "/";
        //Criando um apontamento para o diretorio
        File arq = new File(path);
        //Verificando se o diretorio existe
        if (!arq.exists()) {
            //Criando o diretorio
            arq.mkdirs();
        }
        for (String nomeImagem : mapaImagens.keySet()) {
            path = path.replaceAll("\\\\", "/");
            arq = new File(path + nomeImagem);
            if (!arq.exists()) {
                File imagem = mapaImagens.get(nomeImagem);
                InputStream in = new FileInputStream(imagem);
                BufferedImage outImage = ImageIO.read(in);
                if (outImage != null) {
                    ImageIO.write(outImage, "PNG", arq);
                }
            }
        }
    }

    public static byte[] obterFoto(ModeloMensagemVO modelo, String chave) throws Exception {
        byte[] foto = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                MidiaEntidadeEnum.FOTO_MODELO_MENSAGEM, modelo.getCodigo().toString(), null);
        return foto;
    }

    public static byte[] obterFotoEmpresa(final Integer codigoEmpresa) throws Exception {
        byte[] foto = MidiaService.getInstance().downloadObjectAsByteArray(obterChave("key"),
                MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, codigoEmpresa.toString(), null);
        return foto;
    }

    private static String obterChave(String key) {
        final FacesContext ctx = FacesContext.getCurrentInstance();
        if (ctx != null) {
            final Map sessionState = ctx.getExternalContext().getSessionMap();
            return sessionState.get(key) != null ? sessionState.get(key).toString() : "";
        } else {
            return null;
        }
    }

    public static void criarImagem(String path, ModeloMensagemVO modelo, String chave) throws Exception {
        if ((path == null) || ("".equals(path))) {
            return;
        }
        if ((modelo.getNomeImagem() == null) || (modelo.getNomeImagem().isEmpty())) {
            return;
        }

        //Criando um apontamento para o diretorio
        File arq = new File(path);
        //Verificando se o diretorio existe
        if (!arq.exists()) {
            //Criando o diretorio
            arq.mkdirs();
        }

        path = path.replaceAll("\\\\", "/");

        arq = new File(path + modelo.getNomeImagem().trim());

        if (!arq.exists()) {
            byte[] imagem;
            if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                imagem = obterFoto(modelo, chave);
            } else {
                imagem = modelo.getImagemModelo();
            }
            if(imagem == null){
                return;
            }
            InputStream in = new ByteArrayInputStream(imagem);
            BufferedImage outImage = ImageIO.read(in);
            if (outImage != null) {
                String[] extensao = modelo.getNomeImagem().split("\\.");
                if ("png".equalsIgnoreCase(extensao[extensao.length - 1])) {
                    ImageIO.write(outImage, "PNG", arq);
                } else {
                    ImageIO.write(outImage, "JPEG", arq);
                }
            }
        }
    }

    public String getChave() {
        if (chave == null) {
            chave = "";
        }
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public MalaDiretaVO getMalaDiretaVO() {
        if (malaDiretaVO == null) {
            malaDiretaVO = new MalaDiretaVO();
        }
        return malaDiretaVO;
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }
    
    public static void povoarEmailsEmBounce() {
        if (PropsService.isTrue(PropsService.useBounceService)) {
            if (EMAILS_BOUNCE == null) {
                EMAILS_BOUNCE = new HashMap<>();
            }
            if (EMAILS_BOUNCE.isEmpty()) {
                Uteis.logarDebug("Povoando emails em bounce para lista negra...");
                EMAILS_BOUNCE.putAll(BounceService.preencherEmailsBounce());

                Uteis.logarDebug("Povoando emails denunciantes para lista negra...");
                EMAILS_BOUNCE.putAll(BounceService.preencherEmailsDenunciantes());

                //Uteis.logarDebug(EMAILS_BOUNCE.toString());
                Uteis.logarDebug(String.format("Concluiu consulta de emails para lista negra com %s itens",
                        EMAILS_BOUNCE.size()));
            }
        }
    }

    public static void forcarPovoarEmailsEmBounce() {
        if (PropsService.isTrue(PropsService.useBounceService)) {
            EMAILS_BOUNCE = null;
            povoarEmailsEmBounce();
        }
    }

    public List<MailingItensController.MailingItem> removeBounceEmails(List<MailingItensController.MailingItem> destinations) {
        List<MailingItensController.MailingItem> lista = new ArrayList<>();
        final String domainsBlocked = Uteis.splitFromArray(DOMAINS_BOUNCE, false);
        for(MailingItensController.MailingItem e : destinations){
            final String email = e.getValor().toLowerCase();
            if (email.contains("@")) {
                final String domain = email.substring(email.indexOf("@")).replace("@", "");
                if (domainsBlocked.contains(domain)) {
                    continue;
                }
                if (EMAILS_BOUNCE != null && !EMAILS_BOUNCE.containsKey(email)) {
                    lista.add(e);
                } else {
                    Uteis.logarDebug("UteisEmail IGNORANDO ==> e-mail em bounce/denúncia: " + email);
                }
            } else {
                Uteis.logarDebug("UteisEmail IGNORANDO ==> e-mail inválido: " + email);
            }
        }
        return lista;
    }

    public String[] removeBounceEmails(String[] destinations) {
        List<String> lista = new ArrayList<String>();
        final String domainsBlocked = Uteis.splitFromArray(DOMAINS_BOUNCE, false);
        for (int i = 0; i < destinations.length; i++) {
            final String email = destinations[i].toLowerCase();
            if (email.contains("@")) {
                final String domain = email.substring(email.indexOf("@")).replace("@", "");
                if (domainsBlocked.contains(domain)) {
                    continue;
                }
                if (EMAILS_BOUNCE != null && !EMAILS_BOUNCE.containsKey(email)) {
                    lista.add(email);
                } else {
                    Uteis.logarDebug("UteisEmail IGNORANDO ==> e-mail em bounce/denúncia: " + email);
                }
            } else {
                Uteis.logarDebug("UteisEmail IGNORANDO ==> e-mail inválido: " + email);
            }
        }
        return lista.toArray(new String[0]);
    }

    private String getRemetenteFormatado(String nomeEmpresa, String nomeRemet) {
        String remetenteFormatado = UteisValidacao.emptyString(nomeEmpresa) ? "" : nomeEmpresa + "";
        remetenteFormatado += UteisValidacao.emptyString(nomeRemet) ? "" : (UteisValidacao.emptyString(remetenteFormatado) ? "" : " - " + nomeRemet);
        return remetenteFormatado;
    }

    /**
     * Veja em {@link #retornarEmailsSeparadosPorSeparador(List, String)}.
     */
    public static String retornarEmailsSeparadosPorPontoVirgula(List<String> emailsNotificacaoAutomaticaNotas) {
        return retornarEmailsSeparadosPorSeparador(emailsNotificacaoAutomaticaNotas, ";");
    }

    /**
     * @param emails lista contendo os e-mails.
     *
     * @return caso você tenha uma lista com 3 elementos e o <code>separador</code> seja <b>; (ponto-virgula)</b>:
     * <pre>
     *     (0) - <EMAIL>     <br>
     *     (1) - <EMAIL> <br>
     *     (2) - <EMAIL>    <br>
     * </pre>
     *
     * será retornado: <b><EMAIL>;<EMAIL>;<EMAIL></b>
     */
    public static String retornarEmailsSeparadosPorSeparador(List<String> emails, final String separador) {
        if (emails != null && !emails.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (String email : emails) {
                sb.append(email).append(separador);
            }
            String emailSeparados = sb.toString();
            int posicaoUltimoSeparador = emailSeparados.lastIndexOf(separador);

            return emailSeparados.substring(0, posicaoUltimoSeparador);
        }

        return null;
    }

    /**
     * Veja em {@link #retornarListaEmailStringComSeparadores(String, String, boolean)}.
     */
    public static List<String> retornarListaEmailStringSeparadoPorPontoVirgulaIgnorandoInvalidos(String emails) {
        return retornarListaEmailStringComSeparadores(emails, ";", true);
    }

    /**
     * Veja em {@link #retornarListaEmailStringComSeparadores(String, String, boolean)}.
     */
    public static String[] retornarArrayEmailStringSeparadoPorPontoVirgulaIgnorandoInvalidos(String emails) {
        return retornarListaEmailStringComSeparadores(emails, ";", true)
                .toArray(new String[0]);
    }

    /**
     * @param emails contendo os e-mails separados pelo <code>separador</code> informado.
     * @param separador que separa os e-mails na String <code>emails</code>.
     * @param ignorarEmailsInvalidos <b>TRUE</b>, caso queria que adicione somente os e-mails válidos.
     *
     * @return caso você tenha a String:
     * <b><EMAIL>;<EMAIL>;<EMAIL></b> <br>
     * e o separador seja <b>; (ponto-virgula)</b>, será retornado a seguinte lista com 3 itens:
     * <pre>
     *     (0) - <EMAIL>     <br>
     *     (1) - <EMAIL> <br>
     *     (2) - <EMAIL>    <br>
     * </pre>
     */
    public static List<String> retornarListaEmailStringComSeparadores(String emails, final String separador, boolean ignorarEmailsInvalidos) {
        if (StringUtils.isNotBlank(emails)) {
            String[] emailsCarregados = emails.split(separador);

            List<String> emailsCorretos = new ArrayList<String>();
            for (String emailCarregado : emailsCarregados) {

                if (!ignorarEmailsInvalidos) {
                    emailsCorretos.add(emailCarregado);
                } else if (UteisEmail.getValidEmail(emailCarregado)) {
                    emailsCorretos.add(emailCarregado);
                }
            }

            return emailsCorretos;
        }

        return new ArrayList<String>();
    }

    public static void enviarExcecao(final Exception ex, String... dest) {
        try {
            UteisEmail u = new UteisEmail();
            u.preencherConfiguracaoEmailPadrao(
                    PropsService.getPropertyValue(PropsService.smtpLoginRobo),
                    PropsService.getPropertyValue(PropsService.smtpSenhaRobo),
                    PropsService.getPropertyValue(PropsService.smtpServerRobo),
                    "587",
                    PropsService.getPropertyValue(PropsService.smtpEmailRobo),
                    "ZW - ERRO NO SERVIDOR", "EXCECAO GERADA - " + ex.getMessage() + " - " + new Date().getTime(), false, true);

            StringWriter errors = new StringWriter();
            ex.printStackTrace(new PrintWriter(errors));

            u.enviarEmailN(dest, errors.toString() + " - " + new Date().getTime(), "ERRO GERADO NO SERVIDOR", "DESENVOLVIMENTO PACTO SOLUCOES");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void enviarEmailRobo(String email, String remetente, String nomeDest, String assunto, String mensagem, String nomeEmpresa) throws Exception {
        try {
            ConfiguracaoSistemaCRMVO configCRMVO = SuperControle.getConfiguracaoSMTPRobo();
            configCRMVO.setRemetentePadrao(remetente);
            if (!configCRMVO.isConfiguracaoEmailValida()) {
                throw new Exception("ConfiguracaoSistemaCRMVO não é válida");
            }
            UteisEmail uteis = new UteisEmail();
            uteis.novo(assunto, configCRMVO);
            uteis.enviarEmail(email, nomeDest, mensagem, nomeEmpresa,
                    configCRMVO.getIntegracaoPacto(), configCRMVO.preparaEnvioSendy());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public static void enviarDebug(final String texto, String... dest) {
        try {
            UteisEmail u = new UteisEmail();
            u.preencherConfiguracaoEmailPadrao(
                    PropsService.getPropertyValue(PropsService.smtpLoginRobo),
                    PropsService.getPropertyValue(PropsService.smtpSenhaRobo),
                    PropsService.getPropertyValue(PropsService.smtpServerRobo),
                    "587",
                    PropsService.getPropertyValue(PropsService.smtpEmailRobo),
                    "ZW - DEBUG NO SERVIDOR", "DEBUG - " + " - " + new Date().getTime(), false, true);

            u.enviarEmailN(dest, texto + " - " + new Date().getTime(), "DEBUG GERADO NO SERVIDOR", "DESENVOLVIMENTO PACTO SOLUCOES");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String... args) throws Exception {
        UteisEmail u = new UteisEmail();
        u.preencherConfiguracaoEmailPadrao(
                PropsService.getPropertyValue(PropsService.smtpLoginRobo),
                PropsService.getPropertyValue(PropsService.smtpSenhaRobo),
                PropsService.getPropertyValue(PropsService.smtpServerRobo),
                "587",
                PropsService.getPropertyValue(PropsService.smtpEmailRobo),
                "NAO RESPONDER PACTO", "Assunto Teste - GUN - " + new Date().getTime(), false, true);
        u.enviarEmail("<EMAIL>", "WALLER MACIEL", "OLA ISSO EH UM TESTE - GUN - " + new Date().getTime(), "EMPRESA PACTO");
    }

    public PactoPayComunicacaoVO getPactoPayComunicacaoVO() {
        return pactoPayComunicacaoVO;
    }

    public void setPactoPayComunicacaoVO(PactoPayComunicacaoVO pactoPayComunicacaoVO) {
        this.pactoPayComunicacaoVO = pactoPayComunicacaoVO;
    }

    public PactoPayEnvioEmailVO getPactoPayEnvioEmailVO() {
        return pactoPayEnvioEmailVO;
    }

    public void setPactoPayEnvioEmailVO(PactoPayEnvioEmailVO pactoPayEnvioEmailVO) {
        this.pactoPayEnvioEmailVO = pactoPayEnvioEmailVO;
    }
}
