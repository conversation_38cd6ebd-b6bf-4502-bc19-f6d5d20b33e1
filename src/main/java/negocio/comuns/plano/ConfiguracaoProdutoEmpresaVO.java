/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class ConfiguracaoProdutoEmpresaVO extends SuperVO {

    private Integer produto;
    private EmpresaVO empresa;
    private Double valor;
    private Double valorAlunoGympass;
    private Double valorAlunoGogood;
    private Double valorAlunoTotalpass;

    public ConfiguracaoProdutoEmpresaVO(Integer produto, EmpresaVO empresa, Double valor, Double valorAlunoGympass, Double valorAlunoTotalpass, Double valorAlunoGogood) {
        this.produto = produto;
        this.empresa = empresa;
        this.valor = valor;
        this.valorAlunoGympass = valorAlunoGympass;
        this.valorAlunoTotalpass = valorAlunoTotalpass;
        this.valorAlunoGogood = valorAlunoGogood;
    }
    
    @Override
    public ConfiguracaoProdutoEmpresaVO clone(){
        return new ConfiguracaoProdutoEmpresaVO(this.getCodigo(), 
                    new EmpresaVO(this.getEmpresa().getCodigo(), this.getEmpresa().getNome()), this.getValor(), this.getValorAlunoGympass(), this.valorAlunoTotalpass, this.valorAlunoGogood);
    }
    
    public static void validarDados(ConfiguracaoProdutoEmpresaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (UteisValidacao.emptyNumber(obj.getProduto())) {
            throw new ConsistirException("O campo PRODUTO (Configuração do Produto na Empresa) deve ser informado.");
        }
        if (obj.getEmpresa() == null || UteisValidacao.emptyNumber(obj.getEmpresa().getCodigo())) {
            throw new ConsistirException("O campo EMPRESA (Configuração do Produto na Empresa) deve ser informado.");
        }
        if (obj.getValor() == null) {
            throw new ConsistirException("O campo VALOR (Configuração do Produto na Empresa) não pode ser vazio.");
        }
        
    }

    public ConfiguracaoProdutoEmpresaVO() {
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getValorAlunoGympass() {
        return valorAlunoGympass;
    }

    public void setValorAlunoGympass(Double valorAlunoGympass) {
        this.valorAlunoGympass = valorAlunoGympass;
    }

    public Double getValorAlunoGogood() {
        return valorAlunoGogood;
    }

    public void setValorAlunoGogood(Double valorAlunoGogood) {
        this.valorAlunoGogood = valorAlunoGogood;
    }

    public Double getValorAlunoTotalpass() {
        return valorAlunoTotalpass;
    }

    public void setValorAlunoTotalpass(Double valorAlunoTotalpass) {
        this.valorAlunoTotalpass = valorAlunoTotalpass;
    }

    public boolean isValorAlunoTotalpassPreenchido() {
        return valorAlunoTotalpass != null && valorAlunoTotalpass > 0.0;
    }
    public boolean isValorAlunoGympassPreenchido() {
        return valorAlunoGympass != null && valorAlunoGympass > 0.0;
    }
    public boolean isValorAlunoGogoodPreenchido() {
        return valorAlunoGogood != null && valorAlunoGogood > 0.0;
    }
}
