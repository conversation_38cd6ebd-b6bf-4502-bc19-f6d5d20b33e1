package negocio.comuns.plano;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.plano.Plano;

import java.util.*;

/**
 * Reponsável por manter os dados da entidade PlanoRecorrencia. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see Plano
 */
public class PlanoRecorrenciaVO extends SuperVO {


    protected Double taxaAdesao = 0.0;
    protected Double valorAnuidade = 0.0;
    protected Integer diaAnuidade = 0;
    protected Integer mesAnuidade = 0;
    protected Integer duracaoPlano = 1;
    protected Integer qtdDiasAposVencimentoCancelamentoAutomatico = 0;
    protected Boolean renovavelAutomaticamente = false;
    private Boolean naoRenovarParcelaVencida;
    protected Integer plano = 0;
    protected Double valorMensal = 0.0;
    private Boolean naoCobrarAnuidadeProporcional;
    private boolean anuidadeNaParcela = false;
    private Integer parcelaAnuidade;
    private boolean cancelamentoProporcional = false;
    private boolean cancelamentoProporcionalSomenteRenovacao = false;
    private Integer qtdDiasCobrarProximaParcela;
    private Integer qtdDiasCobrarAnuidadeTotal;
    private boolean gerarParcelasValorDiferente = false;
    private boolean gerarParcelasValorDiferenteRenovacao = false;
    private List<PlanoRecorrenciaParcelaVO> parcelas = new ArrayList<PlanoRecorrenciaParcelaVO>();
    private Double valorTotalParcelasValorDirefente;
    private int quantidadeParcelasValorDiferente = 0;
    private boolean parcelarAnuidade = false;
    private List<PlanoAnuidadeParcelaVO> parcelasAnuidade;
    private PlanoDuracaoVO planoDuracao; // atributo transient

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PlanoRecorrenciaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(PlanoRecorrenciaVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getValorMensal() <= 0.0) {
            throw new ConsistirException("Informe o VALOR DA MENSALIDADE do plano de recorrência", "recorrencia");
        }
        if (obj.getTaxaAdesao() == null || obj.getTaxaAdesao() < 0.0) {
            throw new ConsistirException("Informe a TAXA DE ADESÃO do plano de recorrência", "recorrencia");
        }
        if (obj.getValorAnuidade() == null || obj.getValorAnuidade() < 0.0) {
            throw new ConsistirException("Informe o VALOR DA ANUIDADE a ser pago para o plano", "recorrencia");
        } else if ((obj.getDiaAnuidade() == null || obj.getMesAnuidade() == null
                || obj.getDiaAnuidade() < 0 || obj.getMesAnuidade() < 0 || obj.getMesAnuidade() > 12) && !obj.isAnuidadeNaParcela()) {
            throw new ConsistirException("Informe o DIA e MÊS de vencimento da anuidade", "recorrencia");
        }
        if (obj.getDiaAnuidade() != null && obj.getMesAnuidade() == null) {

            if (!UteisValidacao.validaDiaMesCalendar(obj.getDiaAnuidade(), obj.getMesAnuidade())) {
                throw new ConsistirException("Informe DIA e MÊS válidos", "recorrencia");
            }
        }
        if (obj.getDuracaoPlano() == 0) {
            throw new ConsistirException("Informe a FIDELIDADE DO PLANO em meses", "recorrencia");
        }

        if (obj.isParcelarAnuidade()) {

            if (UteisValidacao.emptyList(obj.getParcelasAnuidade())) {
                throw new ConsistirException("Informe as parcelas da anuidade", "recorrencia");
            }

            Double valorTotalParcelas = 0.0;
            for (PlanoAnuidadeParcelaVO parc : obj.getParcelasAnuidade()) {
                valorTotalParcelas += parc.getValor();
            }

            if (!UteisValidacao.emptyNumber(obj.getValorAnuidade())) {
                Double valorAnuidade = Uteis.arredondarForcando2CasasDecimais(obj.getValorAnuidade());
                if (!valorAnuidade.equals(Uteis.arredondarForcando2CasasDecimais(valorTotalParcelas))) {
                    throw new ConsistirException("O valor das parcelas da anuidade não condiz com o valor da anuidade", "recorrencia");
                }
            }
        } else {
            obj.setParcelasAnuidade(new ArrayList<PlanoAnuidadeParcelaVO>());
        }
    }

    public Integer getPlano() {
        return (plano);
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Double getTaxaAdesao() {
        return taxaAdesao;
    }

    public void setTaxaAdesao(Double taxaAdesao) {
        this.taxaAdesao = taxaAdesao;
    }

    public Double getValorAnuidade() {
        return valorAnuidade;
    }

    public void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public Integer getDuracaoPlano() {
        return duracaoPlano;
    }

    public void setDuracaoPlano(Integer duracaoPlano) {
        this.duracaoPlano = duracaoPlano;
    }

    public Integer getQtdDiasAposVencimentoCancelamentoAutomatico() {
        return qtdDiasAposVencimentoCancelamentoAutomatico;
    }

    public void setQtdDiasAposVencimentoCancelamentoAutomatico(Integer qtdDiasAposVencimentoCancelamentoAutomatico) {
        this.qtdDiasAposVencimentoCancelamentoAutomatico = qtdDiasAposVencimentoCancelamentoAutomatico;
    }

    public Boolean getRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(Boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public Integer getDiaAnuidade() {
        return diaAnuidade;
    }

    public void setDiaAnuidade(Integer diaAnuidade) {
        this.diaAnuidade = diaAnuidade;
    }

    public Integer getMesAnuidade() {
        return mesAnuidade;
    }

    public void setMesAnuidade(Integer mesAnuidade) {
        this.mesAnuidade = mesAnuidade;
    }

    public Double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public int calcularQuantidadeDeAnuidades(Date dataInicioContrato, Date dataFinalContrato, boolean gerarParcelaSeparado, boolean gerarJuntoParcela) {
        if (gerarJuntoParcela && this.getDuracaoPlano() == 12) {
            return 1;
        }
        if (this.getValorAnuidade() > 0 && !gerarParcelaSeparado) {
            return  1;
        } else if (this.getValorAnuidade() > 0 && gerarParcelaSeparado) {
            // Configuração de gerar parcela separado só gera outra parcela de anuidade quando é configurado no plano
            List<PlanoAnuidadeParcelaVO> planoAnuidadeParcelaVOS = Ordenacao.ordenarListaReverse(getParcelasAnuidade(), "parcela");
            if (planoAnuidadeParcelaVOS.size() > 0) {
                int qtdParcelasAux = planoAnuidadeParcelaVOS.get(0).getParcela() / 12;
                if (qtdParcelasAux <= 1) {
                    return 1;
                }
            }
            int dia = this.getDiaAnuidade();
            int mes = this.getMesAnuidade();

            if (gerarJuntoParcela) {
                dia = Uteis.getDiaMesData(dataFinalContrato);
                mes = Uteis.getMesData(dataFinalContrato);
            }

            //ano atual
            int ano = Calendario.getInstance(dataInicioContrato).get(Calendar.YEAR);

            //data do inicio do contrato
            Date dataParcelaAnuidade = Calendario.getInstance(ano, mes, dia).getTime();

            int qtdParcelas = 0;
            int anoParcelaAnuidade = ano;
            int anoFimContrato = Calendario.getInstance(dataFinalContrato).get(Calendar.YEAR);
            while (anoParcelaAnuidade <=  anoFimContrato) {
                if (Calendario.maiorOuIgual(dataParcelaAnuidade, dataInicioContrato) && Calendario.menorOuIgual(dataParcelaAnuidade, dataFinalContrato)) {
                    qtdParcelas++;
                }
                dataParcelaAnuidade = Uteis.somarCampoData(dataParcelaAnuidade, Calendar.YEAR, 1);
                anoParcelaAnuidade++;
            }
            return qtdParcelas;
        }
        return 0;
    }

    public Double calcularValorProporcionalAnuidade(Date dataInicioContrato, boolean gerarParcelaSeparada) {
        if (this.getValorAnuidade() > 0 && !gerarParcelaSeparada) {
            int dia = this.getDiaAnuidade();
            int mes = this.getMesAnuidade();
            //ano atual
            int ano = Uteis.getAnoData(dataInicioContrato);

            int diaInicioContrato = Uteis.getDiaMesData(dataInicioContrato);
            int mesInicioContrato = Uteis.getMesData(dataInicioContrato);
            //data do inicio do contrato
            Date dataInicial = Calendario.getDataComHoraZerada(dataInicioContrato);
            //data da anuidade do ano que vem
            Date dataFinal = Calendario.getInstance(ano + 1, mes, dia).getTime();

            //calcula diferença de dias da data de vencimento da anuidade até a data de inicio do contrato
            long diferenca = 0;
            if(dia != diaInicioContrato || mesInicioContrato != mes){ // isso porque ano bissexto por dar 366 cobrando errado do cliente
                diferenca = Uteis.nrDiasEntreDatas(dataInicial, dataFinal);
            }

            if ((diferenca > 0 && diferenca < 365)) {
                return Uteis.arredondarForcando2CasasDecimais(diferenca * (this.getValorAnuidade() / 365));
            } else if (diferenca > 365) {
                diferenca = diferenca - 365;
                return Uteis.arredondarForcando2CasasDecimais(diferenca * (this.getValorAnuidade() / 365));
            } else {
                return Uteis.arredondarForcando2CasasDecimais(this.getValorAnuidade());
            }
        } else if (this.getValorAnuidade() > 0 && gerarParcelaSeparada) {
            return this.getValorAnuidade();
        }
        return 0.0;
    }

    public String getVencimentoAnuidade_Apresentar() {
        double dia = this.getDiaAnuidade();
        double mes = this.getMesAnuidade();
        String sDia = Formatador.formatarValorNumerico(dia, "00");
        String sMes = Formatador.formatarValorNumerico(mes, "00");
        return String.format("%s/%s", sDia, sMes);
    }


    public Boolean getNaoRenovarParcelaVencida() {
        if (naoRenovarParcelaVencida == null){
            naoRenovarParcelaVencida = false;
        }
        return naoRenovarParcelaVencida;
    }

    public void setNaoRenovarParcelaVencida(Boolean naoRenovarParcelaVencida) {
        this.naoRenovarParcelaVencida = naoRenovarParcelaVencida;
    }


    public Boolean getNaoCobrarAnuidadeProporcional() {
        if (naoCobrarAnuidadeProporcional == null) {
            naoCobrarAnuidadeProporcional = false;
        }
        return naoCobrarAnuidadeProporcional;
    }

    public void setNaoCobrarAnuidadeProporcional(Boolean naoCobrarAnuidadeProporcional) {
        this.naoCobrarAnuidadeProporcional = naoCobrarAnuidadeProporcional;
    }

    public Integer getParcelaAnuidade() {
        if (parcelaAnuidade == null) {
            parcelaAnuidade = 0;
        }
        return parcelaAnuidade;
    }

    public void setParcelaAnuidade(Integer parcelaAnuidade) {
        this.parcelaAnuidade = parcelaAnuidade;
    }

    public boolean isAnuidadeNaParcela() {
        return anuidadeNaParcela;
    }

    public void setAnuidadeNaParcela(boolean anuidadeNaParcela) {
        this.anuidadeNaParcela = anuidadeNaParcela;
    }

    public boolean isCancelamentoProporcional() {
        return cancelamentoProporcional;
    }

    public void setCancelamentoProporcional(boolean cancelamentoProporcional) {
        this.cancelamentoProporcional = cancelamentoProporcional;
    }

    public boolean isCancelamentoProporcionalSomenteRenovacao() {
        return cancelamentoProporcionalSomenteRenovacao;
    }

    public void setCancelamentoProporcionalSomenteRenovacao(boolean cancelamentoProporcionalSomenteRenovacao) {
        this.cancelamentoProporcionalSomenteRenovacao = cancelamentoProporcionalSomenteRenovacao;
    }

    public Integer getQtdDiasCobrarProximaParcela() {
        if (qtdDiasCobrarProximaParcela == null) {
            qtdDiasCobrarProximaParcela = 0;
        }
        return qtdDiasCobrarProximaParcela;
    }

    public void setQtdDiasCobrarProximaParcela(Integer qtdDiasCobrarProximaParcela) {
        this.qtdDiasCobrarProximaParcela = qtdDiasCobrarProximaParcela;
    }

    public Integer getQtdDiasCobrarAnuidadeTotal() {
        if (qtdDiasCobrarAnuidadeTotal == null) {
            qtdDiasCobrarAnuidadeTotal = 0;
        }
        return qtdDiasCobrarAnuidadeTotal;
    }

    public void setQtdDiasCobrarAnuidadeTotal(Integer qtdDiasCobrarAnuidadeTotal) {
        this.qtdDiasCobrarAnuidadeTotal = qtdDiasCobrarAnuidadeTotal;
    }

    public boolean isGerarParcelasValorDiferente() {
        return gerarParcelasValorDiferente;
    }

    public void setGerarParcelasValorDiferente(boolean gerarParcelasValorDiferente) {
        this.gerarParcelasValorDiferente = gerarParcelasValorDiferente;
    }

    public boolean isGerarParcelasValorDiferenteRenovacao() {
        return gerarParcelasValorDiferenteRenovacao;
    }

    public void setGerarParcelasValorDiferenteRenovacao(boolean gerarParcelasValorDiferenteRenovacao) {
        this.gerarParcelasValorDiferenteRenovacao = gerarParcelasValorDiferenteRenovacao;
    }

    public List<PlanoRecorrenciaParcelaVO> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<PlanoRecorrenciaParcelaVO> parcelas) {
        this.parcelas = parcelas;
    }

    public Double getValorTotalParcelasValorDirefente() {
        if(valorTotalParcelasValorDirefente == null || valorTotalParcelasValorDirefente == 0){
            valorTotalParcelasValorDirefente = 0.0D;
            for(PlanoRecorrenciaParcelaVO p : parcelas){
                valorTotalParcelasValorDirefente += p.getValor();
            }
        }
        return valorTotalParcelasValorDirefente;
    }

    public void setValorTotalParcelasValorDirefente(Double valorTotalParcelasValorDirefente) {
        this.valorTotalParcelasValorDirefente = valorTotalParcelasValorDirefente;
    }

    public int getQuantidadeParcelasValorDiferente() {
        if(quantidadeParcelasValorDiferente == 0){
            quantidadeParcelasValorDiferente = getParcelas() != null ? getParcelas().size() : 0;
        }
        return quantidadeParcelasValorDiferente;
    }

    public void setQuantidadeParcelasValorDiferente(int quantidadeParcelasValorDiferente) {
        this.quantidadeParcelasValorDiferente = quantidadeParcelasValorDiferente;
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PlanoRecorrenciaVO that = (PlanoRecorrenciaVO) o;
        return anuidadeNaParcela == that.anuidadeNaParcela &&
                cancelamentoProporcional == that.cancelamentoProporcional &&
                gerarParcelasValorDiferente == that.gerarParcelasValorDiferente;
    }

    @Override
    public int hashCode() {
        return Objects.hash(anuidadeNaParcela, cancelamentoProporcional, gerarParcelasValorDiferente);
    }

    public List<PlanoAnuidadeParcelaVO> getParcelasAnuidade() {
        if (parcelasAnuidade == null) {
            parcelasAnuidade = new ArrayList<PlanoAnuidadeParcelaVO>();
        }
        return parcelasAnuidade;
    }

    public void setParcelasAnuidade(List<PlanoAnuidadeParcelaVO> parcelasAnuidade) {
        this.parcelasAnuidade = parcelasAnuidade;
    }

    public boolean isParcelarAnuidade() {
        return parcelarAnuidade;
    }

    public void setParcelarAnuidade(boolean parcelarAnuidade) {
        this.parcelarAnuidade = parcelarAnuidade;
    }

    public Double getValorAnuidadeTotalParcelada() {
        if (isParcelarAnuidade()) {
            Double valorTotalParcelas = 0.0;
            for (PlanoAnuidadeParcelaVO parc : getParcelasAnuidade()) {
                valorTotalParcelas += parc.getValor();
            }
            return valorTotalParcelas;
        } else {
            return getValorAnuidade();
        }
    }

    public PlanoDuracaoVO getPlanoDuracao() {
        return planoDuracao;
    }

    public void setPlanoDuracao(PlanoDuracaoVO planoDuracao) {
        this.planoDuracao = planoDuracao;
    }
}
