package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estudio.util.Validador;
import negocio.armario.TamanhoArmarioVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.contrato.ComissaoProdutoConfiguracaoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.plano.enumerador.UnidadeMedidaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.plano.ConfiguracaoProdutoEmpresaPlano;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProdutoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected Double valorBaseCalculo;
    protected String tipoVigencia;
    protected Double valorFinal;
    protected Date dataInicioVigencia;
    protected Date dataFinalVigencia;
    protected Integer nrDiasVigencia;
    protected String tipoProduto;
    protected Boolean desativado;
    protected Boolean selecionado;
    protected Integer capacidade;
    protected Boolean bloqueiaPelaVigencia;
    protected String ncm;
    protected String prefixo;
    private String codigoBarras;
    private boolean prevalecerVigenciaContrato = false;
    private boolean aparecerAulaCheia = false;
    @Lista
    private List<PacotePersonalVO> pacotesPersonal;
    protected String observacao;
    @Lista
    private List<ConfiguracaoProdutoEmpresaVO> configuracoesEmpresa;
    @Lista
    private List<ConfiguracaoProdutoEmpresaPlanoVO> configuracoesValorPorPlano;
    @NaoControlarLogAlteracao
    private ConfiguracaoProdutoEmpresaVO cfgEmpresa;
    @NaoControlarLogAlteracao
    private ConfiguracaoProdutoEmpresaPlanoVO cfgValorPorPlano;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>CategoriaProduto </code>.
     */
    @ChaveEstrangeira
    @FKJson
    protected CategoriaProdutoVO categoriaProduto;
    @ChaveEstrangeira
    @FKJson
    protected DescontoVO desconto;
    @ChaveEstrangeira
    private TamanhoArmarioVO tamanhoArmario;

    @Lista
    private List<ComissaoProdutoConfiguracaoVO> comissaoProdutos;

    //UTILIZADOS PARA NFC-e
    private String cfop;
    private String codigoListaServico;
    private String codigoTributacaoMunicipio;
    private double aliquotaPIS = 0.0;
    private double aliquotaCOFINS = 0.0;
    private double aliquotaICMS = 0.0;
    private double aliquotaISSQN = 0.0;
    private String ncmNFCe;
    private Integer pontos;
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFSe;
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFCe;
    private String situacaoTributariaICMS;
    private String situacaoTributariaISSQN;
    private String situacaoTributariaPIS;
    private String situacaoTributariaCOFINS;
    private String unidadeMedida;
    private boolean enviarPercentualImposto = true;
    private Double percentualFederal;
    private Double percentualEstadual;
    private Double percentualMunicipal;
    private boolean isentoPIS = false;
    private boolean isentoCOFINS = false;
    private boolean isentoICMS = false;
    private boolean enviaAliquotaNFePIS = false;
    private boolean enviaAliquotaNFeCOFINS = false;
    private boolean enviaAliquotaNFeICMS = false;
    private String cest;
    private String codigoBeneficioFiscal;

    private Integer id_externo;
    private Integer qtdePontos;
    private boolean apresentarVendasOnline = false;
    private boolean apresentarPactoFlow;
    private boolean apresentarPactoApp = false;
    private String imagens;
    private Integer maxDivisao;
    private Integer modalidadeVendasOnline;
    @NaoControlarLogAlteracao
    private String posicaoEstoque;
    private Double precoCusto;
    private Double margemLucro;
    private Integer qtdConvites;
    private Integer codigoProdutoSesi;

    private Integer negocioSesi;

    private Integer cRSesi;

    private Integer projetoSesi;

    private Integer contaFinanceiraSesi;
    private Boolean renovavelAutomaticamente = false;
    private String descricaoServicoMunicipio;
    private Integer contratoTextoPadrao;

    private boolean mesclado = false;
    private Integer idProdutoSMD;
    private Integer codigoFormulario;
    private boolean exibirRelatorioSMD = false;
    private boolean destravarTranca = false;

    public ProdutoVO() {
        super();
        inicializarDados();
    }

    public String getCategoria_Apresentar() {
        return getCategoriaProduto().getDescricao();

    }

    public static void validarDados(ProdutoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Produto) deve ser informado.");
        }
        if (obj.getDescricao().contains("'") || obj.getDescricao().contains("\"")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Produto) não pode conter aspas.");
        }
        if (obj.getDescricao().length() > 100) {
            throw new ConsistirException("O campo DESCRIÇÃO (Produto) não ser maior do que 100 caracteres.");
        }
        if (obj.getTipoProduto() == null || obj.getTipoProduto().equals("")) {
            throw new ConsistirException("O campo TIPO DE PRODUTO (Produto) deve ser informado.");
        }
        if (obj.getTipoProduto().equals("DE") || obj.getTipoProduto().equals("DR") || obj.getTipoProduto().equals("DV") || obj.getTipoProduto().equals("QU") || obj.getTipoProduto().equals("AH") || obj.getTipoProduto().equals("MM")) {
            return;
        }
        if (obj.getTipoProduto().equals("SS") && !Validador.isValidaInteger(obj.getCapacidade())) {
            throw new ConsistirException("O campo CAPACIDADE (Produto) deve ser informado.");
        }
        if ((obj.getCategoriaProduto() == null)
                || (obj.getCategoriaProduto().getCodigo() == 0)) {
            throw new ConsistirException("O campo CATEGORIA DO PRODUTO (Produto) deve ser informado.");
        }
        if(obj.getTipoProduto().equals(TipoProduto.ARMARIO.getCodigo())
                && (obj.getTipoVigencia() == null || obj.getTipoVigencia().isEmpty() 
                    || !obj.getTipoVigencia().equals("ID"))){
            throw new ConsistirException("Produtos do tipo ARMÁRIO devem ter o TIPO DE VIGÊNCIA em INTERVALO DE DIAS.");
        }
        if (obj.getTipoVigencia().equals("PF")) {
            if (obj.getDataInicioVigencia() == null) {
                throw new ConsistirException("A DATA INICIO DE VIGÊNCIA deve ser informado.");
            }
            if (obj.getDataFinalVigencia() == null) {
                throw new ConsistirException("A DATA FINAL DE VIGÊNCIA deve ser informado.");
            }
            if (obj.getDataFinalVigencia().before(obj.getDataInicioVigencia())) {
                throw new ConsistirException("A DATA FINAL DE VIGÊNCIA deve ser igual ou maior que a DATA DE INÍCIO DE VIGÊNCIA.");
            }
        }
        if (obj.getTipoVigencia().equals("ID") && obj.getNrDiasVigencia() == 0) {
            throw new ConsistirException("O NÚMERO DE DIAS VIGÊNCIA deve ser informado");
        }
        if (obj.isApresentarVendasOnline() && UteisValidacao.emptyNumber(obj.getMaxDivisao())) {
            throw new ConsistirException("A QUANTIDADE DE PARCELAS não pode ser 0, para \"À vista\" informe 1)");
        }

        if (obj.isApresentarVendasOnline() && UteisValidacao.emptyNumber(obj.getModalidadeVendasOnline())
                && obj.getTipoProduto().equals(TipoProduto.DIARIA.getCodigo())) {
            throw new ConsistirException("O campo MODALIDADE não pode ficar vazio para produtos do tipo Diária que são apresentados no Vendas Online)");
        }

        if (obj.getTipoProduto().equals("TR") && obj.getNrDiasVigencia() > 730){
            throw new ConsistirException("O campo NÚMERO DE DIAS VIGÊNCIA para produtos de TRANCAMENTO não pode ser maior que 730.");
        }

        if (obj.getNrDiasVigencia() > 36500) {
            throw new ConsistirException("O campo NÚMERO DE DIAS VIGÊNCIA não pode ser maior que 36500.");
        }
        if(!obj.getPodeSerRenovadoAutomatica()){
            obj.setRenovavelAutomaticamente(false);
        }
        if(obj.getRenovavelAutomaticamente()){
            obj.setPrevalecerVigenciaContrato(false);
        }



    }

    public void validarProdutoExistente(ProdutoVO obj) throws Exception {
        if (obj.getTipoProduto().equals("MA") && (obj.getCodigo() != 0) && (!getCodigo().equals(obj.getCodigo()))) {
            throw new Exception("Já existe um produto cadastro do TIPO MATRÍCULA. Descrição do produto: " + obj.getDescricao());
        }
        if (obj.getTipoProduto().equals("RE") && (obj.getCodigo() != 0) && (!getCodigo().equals(obj.getCodigo()))) {
            throw new Exception("Já existe um produto cadastro do TIPO REMATRÍCULA. Descrição do produto: " + obj.getDescricao());
        }
        if (obj.getTipoProduto().equals("RN") && (obj.getCodigo() != 0) && (!getCodigo().equals(obj.getCodigo()))) {
            throw new Exception("Já existe um produto cadastro do TIPO RENOVAÇÃO. Descrição do produto: " + obj.getDescricao());
        }
        if (obj.getTipoProduto().equals("FR") && (obj.getCodigo() != 0) && (!getCodigo().equals(obj.getCodigo()))) {
            throw new Exception("Já existe um produto cadastro do TIPO FREEPASS. Descrição do produto: " + obj.getDescricao());
        }
        if (obj.getTipoProduto().equals("TR") && (obj.getCodigo() != 0) && (!getCodigo().equals(obj.getCodigo()))) {
            throw new Exception("Já existe um produto cadastro do TIPO TRANCAMENTO. Descrição do produto: " + obj.getDescricao());
        }
    }
    //!descricao.equals(obj.getDescricao()) ||

    public static void validarProdutoVendido(ProdutoVO obj, String tipoProduto, boolean naoPermiteEditar) throws ConsistirException {
        if (!obj.getNovoObj() && naoPermiteEditar && (!tipoProduto.equals(obj.getTipoProduto()))) {
            throw new ConsistirException("O TIPO DE PRODUTO  não pode ser alterado, pois o mesmo já foi vendido.");
        }
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setTipoVigencia(getTipoVigencia().toUpperCase());
        setTipoProduto(getTipoProduto().toUpperCase());
    }

    public void inicializarDados() {
        setCodigo(0);
        setDescricao("");
        setTipoVigencia("");
        setDataInicioVigencia(null);
        setDataFinalVigencia(null);
        setNrDiasVigencia(0);
        setValorFinal(0.0);
        setValorBaseCalculo(0.0);
        setTipoProduto("");
        setCategoriaProduto(new CategoriaProdutoVO());
        setDesconto(new DescontoVO());
        setDesativado(false);
        setCodigoBarras("");
        setCodigoBeneficioFiscal("");
    }

    public CategoriaProdutoVO getCategoriaProduto() {
        if (categoriaProduto == null) {
            categoriaProduto = new CategoriaProdutoVO();
        }
        return (categoriaProduto);
    }

    public void setCategoriaProduto(CategoriaProdutoVO obj) {
        this.categoriaProduto = obj;
    }

    public DescontoVO getDesconto() {
        if (desconto == null) {
            desconto = new DescontoVO();
        }
        return (desconto);
    }

    public void setDesconto(DescontoVO desconto) {
        this.desconto = desconto;
    }

    public String getTipoProduto() {
        if (tipoProduto == null) {
            tipoProduto = "";
        }
        return (tipoProduto);
    }

    public String getTipoProduto_Apresentar() {
        if (tipoProduto == null) {
            tipoProduto = "";
        }
        if (tipoProduto.equals("MA")) {
            return "Matrícula";
        }
        if (tipoProduto.equals("RE")) {
            return "Rematrícula";
        }
        if (tipoProduto.equals("RN")) {
            return "Renovação";
        }
        if (tipoProduto.equals("PE")) {
            return "Produto Estoque";
        }
        if (tipoProduto.equals("PM")) {
            return "Mês de Referência Plano";
        }
        if (tipoProduto.equals("SE")) {
            return "Serviço";
        }
        if (tipoProduto.equals("CD")) {
            return "Convênio de Desconto";
        }
        if (tipoProduto.equals("DE")) {
            return "Desconto";
        }
        if (tipoProduto.equals("DV")) {
            return "Devolução";
        }
        if (tipoProduto.equals("TR")) {
            return "Trancamento";
        }
        if (tipoProduto.equals("RT")) {
            return "Retorno Trancamento";
        }
        if (tipoProduto.equals("AA")) {
            return "Aula Avulsa";
        }
        if (tipoProduto.equals("DI")) {
            return "Diária";
        }
        if (tipoProduto.equals("FR")) {
            return "FreePass";
        }
        if (tipoProduto.equals("AH")) {
            return "Alterar - Horário";
        }
        if (tipoProduto.equals("MM")) {
            return "Manutenção Modalidade";
        }
        if (tipoProduto.equals("MC")) {
            return "Manutenção Conta Corrente";
        }
        if (tipoProduto.equals("DR")) {
            return "Desconto em Renovação Antecipada";
        }
        if (tipoProduto.equals("TP")) {
            return "Taxa de Personal";
        }
        if (tipoProduto.equals("SS")) {
            return "Sessão";
        }
        if (tipoProduto.equals("DS")) {
            return "Desafio";
        }
        if (tipoProduto.equals("HM")) {
            return "App Home Fit";
        }
        if (tipoProduto.equals("BT")) {
            return "Bio Totem";
        }
        if (tipoProduto.equals("CN")) {
            return "Consulta Nutricional";
        }
        if (tipoProduto.equals("LC")) {
            return "Locação";
        }
        return (tipoProduto);
    }

    public static String getTipoProduto_Apresentar(String dominio) {
        if (dominio == null) {
            dominio = "";
        }
        if (dominio.equals("MA")) {
            return "Matrícula";
        }
        if (dominio.equals("RE")) {
            return "Rematrícula";
        }
        if (dominio.equals("RN")) {
            return "Renovação";
        }
        if (dominio.equals("PE")) {
            return "Produto Estoque";
        }
        if (dominio.equals("PM")) {
            return "Mês de Referência Plano";
        }
        if (dominio.equals("SE")) {
            return "Serviço";
        }
        if (dominio.equals("CD")) {
            return "Convênio de Desconto";
        }
        if (dominio.equals("DE")) {
            return "Desconto";
        }
        if (dominio.equals("DV")) {
            return "Devolução";
        }
        if (dominio.equals("TR")) {
            return "Trancamento";
        }
        if (dominio.equals("RT")) {
            return "Retorno Trancamento";
        }
        if (dominio.equals("AA")) {
            return "Aula Avulsa";
        }
        if (dominio.equals("DI")) {
            return "Diária";
        }
        if (dominio.equals("FR")) {
            return "FreePass";
        }
        if (dominio.equals("AH")) {
            return "Alterar - Horário";
        }
        if (dominio.equals("MM")) {
            return "Manutenção Modalidade";
        }
        if (dominio.equals("MC")) {
            return "Manutenção Conta Corrente";
        }
        if (dominio.equals("DR")) {
            return "Desconto em Renovação Antecipada";
        }
        if (dominio.equals("TP")) {
            return "Taxa de Personal";
        }
        return (dominio);
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public Integer getNrDiasVigencia() {
        return (nrDiasVigencia);
    }

    public void setNrDiasVigencia(Integer nrDiasVigencia) {
        this.nrDiasVigencia = nrDiasVigencia;
    }

    public Date getDataFinalVigencia() {
        return (dataFinalVigencia);
    }

    public String getDataFinalVigencia_Apresentar() {
        return (Uteis.getData(dataFinalVigencia));
    }

    public void setDataFinalVigencia(Date dataFinalVigencia) {
        this.dataFinalVigencia = dataFinalVigencia;
    }

    public Date getDataInicioVigencia() {
        return (dataInicioVigencia);
    }

    public String getDataInicioVigencia_Apresentar() {
        return (Uteis.getData(dataInicioVigencia));
    }

    public void setDataInicioVigencia(Date dataInicioVigencia) {
        this.dataInicioVigencia = dataInicioVigencia;
    }

    public String getTipoVigencia() {
        if (tipoVigencia == null) {
            tipoVigencia = "";
        }
        return (tipoVigencia);
    }

    public String getTipoVigencia_Apresentar() {
        if (tipoVigencia == null) {
            tipoVigencia = "";
        }
        if (tipoVigencia.equals("PF")) {
            return "Período Fixo";
        }
        if (tipoVigencia.equals("ID")) {
            return "Intervalo de Dias";
        }
        return (tipoVigencia);
    }

    public void setTipoVigencia(String tipoVigencia) {
        this.tipoVigencia = tipoVigencia;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorBaseCalculo() {
        return valorBaseCalculo;
    }

    public void setValorBaseCalculo(Double valorBaseCalculo) {
        this.valorBaseCalculo = valorBaseCalculo;
    }

    public String getValorFinal_Apresentar() {
        return Formatador.formatarValorMonetario(getValorFinal());
    }

    public Double getValorFinal() {
        if (valorFinal == null) {
            valorFinal = 0.0;
        }
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }

    public Boolean getDesativado() {
        return desativado;
    }

    public void setDesativado(Boolean desativado) {
        this.desativado = desativado;
    }

    public Boolean getDesenharDataVigencia() {
        if (getTipoVigencia() == null || getTipoVigencia().equals("")) {
            return false;
        }
        if (getTipoVigencia().equals("PF")) {
            setNrDiasVigencia(0);
            return true;
        }

        return false;
    }

    public Boolean getDesenharPeriodoVigencia() {
        if (getTipoVigencia() == null || getTipoVigencia().equals("")) {
            return false;
        }
        if (getTipoVigencia().equals("ID")) {
            setDataInicioVigencia(null);
            setDataFinalVigencia(null);
            return true;
        }

        return false;
    }

    public Boolean getPodeSerRenovadoAutomatica() {
        if(UteisValidacao.emptyString(getTipoProduto()) || (!TipoProduto.SERVICO.getCodigo().equals(getTipoProduto())
                && !TipoProduto.PRODUTO_ESTOQUE.getCodigo().equals(getTipoProduto())
                && !TipoProduto.ARMARIO.getCodigo().equals(getTipoProduto())
                && !TipoProduto.ATESTADO.getCodigo().equals(getTipoProduto())
                && !TipoProduto.BIO_TOTEM.getCodigo().equals(getTipoProduto())
                )){
            return false;

        }
        if (UteisValidacao.emptyString(getTipoVigencia()) || !getTipoVigencia().equals("ID")) {
            return false;
        }
        return true;
    }

    public Boolean getDesenharVigenciaVariavel() {
        return !(getTipoVigencia() == null || getTipoVigencia().equals("")) && getTipoVigencia().equals("VV");
    }

    public Boolean getDesenharValorProduto() {
        if (getTipoProduto() == null || getTipoProduto().equals("")) {
            return false;
        }
        if (getTipoProduto().equals("DE") || 
                getTipoProduto().equals("DR") || 
                getTipoProduto().equals("FR") || 
                getTipoProduto().equals("DV") || 
                getTipoProduto().equals("QU") || 
                getTipoProduto().equals("AH") || 
                getTipoProduto().equals("MM") ||
                !UteisValidacao.emptyList(getConfiguracoesEmpresa())
                ) {
            return false;
        }
        if (!getTipoProduto().equals("PM")) {
            return true;
        }
        return false;
    }

    public Boolean getDesenhartela() {
        if (getTipoProduto() == null || getTipoProduto().equals("")) {
            return true;
        }
        if (getTipoProduto().equals("DE") || getTipoProduto().equals("DR") || getTipoProduto().equals("DV") || getTipoProduto().equals("QU") || getTipoProduto().equals("AH") || getTipoProduto().equals("MM")) {
            return false;
        }
        if (getTipoProduto().equals("FR")) {
            setTipoVigencia("ID");
        }
        return true;
    }
    
    public Boolean getArmario() {
        return !UteisValidacao.emptyString(getTipoProduto()) && TipoProduto.ARMARIO.getCodigo().equals(getTipoProduto());
    }

    public Boolean getDesenharTipoVigencia() {
        if (getTipoProduto() == null || getTipoProduto().equals("")) {
            return true;
        }
        if (getTipoProduto().equals("DE") || getTipoProduto().equals("DR") || getTipoProduto().equals("PM") || getTipoProduto().equals("DV") || getTipoProduto().equals("QU") || getTipoProduto().equals("RE") || getTipoProduto().equals("MA") || getTipoProduto().equals("RN") || getTipoProduto().equals("AH") || getTipoProduto().equals("MM") || getTipoProduto().equals("LC")) {
            setTipoVigencia("");
            return false;
        }
        if (getTipoProduto().equals("FR") || getTipoProduto().equals("AA") || getTipoProduto().equals("DI") || getTipoProduto().equals("TR")) {
            setTipoVigencia("ID");
        }
        if (getTipoProduto().equals(TipoProduto.ATESTADO.getCodigo())) {
            setTipoVigencia("VV");
        }
        return true;
    }

    public String getSituacao() {
        if (this.getDesativado()) {
            return "Inativo";
        } else {
            return "Ativo";
        }
    }

    public Boolean getDesativarComboBox() {
        return !(getTipoProduto() == null || getTipoProduto().equals(""))
                && (getTipoProduto().equals("FR") || getTipoProduto().equals("AA")
                || getTipoProduto().equals("DI") || getTipoProduto().equals("TR")
                || getTipoProduto().equals("AT"));
    }

    public Boolean getDesativarUnidadeMedida() {
        if (getTipoProduto().equalsIgnoreCase("LC")) {
            setUnidadeMedida(UnidadeMedidaEnum.TEMPO_HORA.getCodigo());
            return true;
        } else {
            if (getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.TEMPO_HORA.getCodigo())) {
                setUnidadeMedida(UnidadeMedidaEnum.UNIDADE.getCodigo());
            }
            return false;
        }
    }

    public boolean isProdutoZillyonWeb() {
        return getTipoProduto().equals("QU") || (getTipoProduto().equals("SS") && getDescricao().equals("PRODUTO GENÉRICO"));
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof ProdutoVO) {
            ProdutoVO aux = (ProdutoVO) obj;
            return aux.getCodigo().intValue() == this.getCodigo().intValue();
        }

        return false;
    }

    public Boolean getBloqueiaPelaVigencia() {
        if (bloqueiaPelaVigencia == null) {
            bloqueiaPelaVigencia = false;
        }
        return bloqueiaPelaVigencia;
    }

    public void setBloqueiaPelaVigencia(Boolean bloqueiaPelaVigencia) {
        this.bloqueiaPelaVigencia = bloqueiaPelaVigencia;
    }

    public String getNcm() {
        return ncm;
    }

    public void setNcm(String ncm) {
        this.ncm = ncm;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public boolean isPrevalecerVigenciaContrato() {
        return prevalecerVigenciaContrato;
    }

    public void setPrevalecerVigenciaContrato(boolean prevalecerVigenciaContrato) {
        this.prevalecerVigenciaContrato = prevalecerVigenciaContrato;
    }

    public String getCodigoBarrasTitle() {
        if (getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo())) {
            return "Para produto de Unidade de Medida \"Grama\" informe os primeiros 7 dígitos do código de barras.";
        } else {
            return "Informe o código de barras do produto";
        }
    }
    public String getCodigoBarras() {
        if (codigoBarras == null) {
            codigoBarras = "";
        }
        return codigoBarras;
    }

    public void setCodigoBarras(String codigoBarras) {
        this.codigoBarras = codigoBarras;
    }

    public Boolean getApresentarRenovar() {
        return !getTipoVigencia().equals("VV") && !getTipoProduto().equals("TR");
    }

    public List<PacotePersonalVO> getPacotesPersonal() {
        if (pacotesPersonal ==  null) {
            pacotesPersonal = new ArrayList<>();
        }
        return pacotesPersonal;
    }

    public void setPacotesPersonal(List<PacotePersonalVO> pacotesPersonal) {
        this.pacotesPersonal = pacotesPersonal;
    }

    public List<ConfiguracaoProdutoEmpresaVO> getConfiguracoesEmpresa() {
        if (configuracoesEmpresa == null) {
            configuracoesEmpresa = new ArrayList<>();
        }
        return configuracoesEmpresa;
    }

    public void setConfiguracoesEmpresa(List<ConfiguracaoProdutoEmpresaVO> configuracoesEmpresa) {
        this.configuracoesEmpresa = configuracoesEmpresa;
    }

    public List<ConfiguracaoProdutoEmpresaPlanoVO> getConfiguracoesValorPorPlano() {
        if(configuracoesValorPorPlano == null) {
            configuracoesValorPorPlano = new ArrayList<>();
        }
        return configuracoesValorPorPlano;
    }

    public void setConfiguracoesValorPorPlano(List<ConfiguracaoProdutoEmpresaPlanoVO> configuracoesValorPorPlano) {
        this.configuracoesValorPorPlano = configuracoesValorPorPlano;
    }

    public ConfiguracaoProdutoEmpresaVO getCfgEmpresa() {
        if (cfgEmpresa == null) {
            cfgEmpresa = new ConfiguracaoProdutoEmpresaVO();
        }
        return cfgEmpresa;
    }

    public void setCfgEmpresa(ConfiguracaoProdutoEmpresaVO cfgEmpresa) {
        this.cfgEmpresa = cfgEmpresa;
    }

    public ConfiguracaoProdutoEmpresaPlanoVO getCfgValorPorPlano() {
        if (cfgValorPorPlano == null) {
            cfgValorPorPlano = new ConfiguracaoProdutoEmpresaPlanoVO();
        }
        return cfgValorPorPlano;
    }

    public void setCfgValorPorPlano(ConfiguracaoProdutoEmpresaPlanoVO cfgValorPorPlano) {
        this.cfgValorPorPlano = cfgValorPorPlano;
    }

    public TamanhoArmarioVO getTamanhoArmario() {
        if(tamanhoArmario == null){
            tamanhoArmario = new TamanhoArmarioVO();
        }
        return tamanhoArmario;
    }

    public void setTamanhoArmario(TamanhoArmarioVO TamanhoArmario) {
        this.tamanhoArmario = TamanhoArmario;
    }

    public String getCfop() {
        if (cfop == null) {
            cfop = "";
        }
        return cfop;
    }

    public void setCfop(String cfop) {
        this.cfop = cfop;
    }

    public String getCodigoListaServico() {
        if (codigoListaServico == null) {
            codigoListaServico = "";
        }
        return codigoListaServico;
    }

    public void setCodigoListaServico(String codigoListaServico) {
        this.codigoListaServico = codigoListaServico;
    }

    public String getCodigoTributacaoMunicipio() {
        if (codigoTributacaoMunicipio == null) {
            codigoTributacaoMunicipio = "";
        }
        return codigoTributacaoMunicipio;
    }

    public void setCodigoTributacaoMunicipio(String codigoTributacaoMunicipio) {
        this.codigoTributacaoMunicipio = codigoTributacaoMunicipio;
    }

    public double getAliquotaPIS() {
        return aliquotaPIS;
    }

    public void setAliquotaPIS(double aliquotaPIS) {
        this.aliquotaPIS = aliquotaPIS;
    }

    public double getAliquotaCOFINS() {
        return aliquotaCOFINS;
    }

    public void setAliquotaCOFINS(double aliquotaCOFINS) {
        this.aliquotaCOFINS = aliquotaCOFINS;
    }

    public double getAliquotaICMS() {
        return aliquotaICMS;
    }

    public void setAliquotaICMS(double aliquotaICMS) {
        this.aliquotaICMS = aliquotaICMS;
    }

    public double getAliquotaISSQN() {
        return aliquotaISSQN;
    }

    public void setAliquotaISSQN(double aliquotaISSQN) {
        this.aliquotaISSQN = aliquotaISSQN;
    }

    public String getNcmNFCe() {
        return ncmNFCe;
    }

    public void setNcmNFCe(String ncmNFCe) {
        this.ncmNFCe = ncmNFCe;
    }

    public List<ComissaoProdutoConfiguracaoVO> getComissaoProdutos() {
        if (comissaoProdutos == null) {
            comissaoProdutos = new ArrayList<>();
        }
        return comissaoProdutos;
    }

    public void setComissaoProdutos(List<ComissaoProdutoConfiguracaoVO> comissaoProdutos) {
        this.comissaoProdutos = comissaoProdutos;
    }

    public static boolean isProdutoPodeCobrarSeparado(String tipoProduto) {
        return tipoProduto != null &&
                (tipoProduto.equals(TipoProduto.PRODUTO_ESTOQUE.getCodigo()) ||
                        tipoProduto.equals(TipoProduto.SERVICO.getCodigo()) ||
                        tipoProduto.equals(TipoProduto.ARMARIO.getCodigo()));
    }
    
    public ProdutoWS toWS(){
        ProdutoWS produtoWs = new ProdutoWS();
        produtoWs.setCodigo(this.codigo);
        produtoWs.setDescricao(this.descricao);
        produtoWs.setValor(this.valorFinal);
        produtoWs.setNrDiasVigencia(this.nrDiasVigencia);
        produtoWs.setTipoProduto(this.tipoProduto);
        return produtoWs;
    }

    public Integer getPontos() {
        if (pontos == null) {
            pontos = 0;
        }
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public boolean isAparecerAulaCheia() {
        return aparecerAulaCheia;
    }

    public void setAparecerAulaCheia(boolean aparecerAulaCheia) {
        this.aparecerAulaCheia = aparecerAulaCheia;
    }

    public String getPrefixo() {
        return prefixo;
    }

    public void setPrefixo(String prefixo) {
        this.prefixo = prefixo;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalNFSe() {
        if (configuracaoNotaFiscalNFSe == null) {
            configuracaoNotaFiscalNFSe = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalNFSe;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalNFSeNullable() {
        return configuracaoNotaFiscalNFSe;
    }


    public void setConfiguracaoNotaFiscalNFSe(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFSe) {
        this.configuracaoNotaFiscalNFSe = configuracaoNotaFiscalNFSe;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalNFCe() {
        if (configuracaoNotaFiscalNFCe == null) {
            configuracaoNotaFiscalNFCe = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalNFCe;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalNFCeNullable() {
        return configuracaoNotaFiscalNFCe;
    }


    public void setConfiguracaoNotaFiscalNFCe(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFCe) {
        this.configuracaoNotaFiscalNFCe = configuracaoNotaFiscalNFCe;
    }

    public String getSituacaoTributariaISSQN() {
        if (situacaoTributariaISSQN == null) {
            situacaoTributariaISSQN = "";
        }
        return situacaoTributariaISSQN;
    }

    public void setSituacaoTributariaISSQN(String situacaoTributariaISSQN) {
        this.situacaoTributariaISSQN = situacaoTributariaISSQN;
    }

    public String getSituacaoTributariaICMS() {
        if (situacaoTributariaICMS == null) {
            situacaoTributariaICMS = "";
        }
        return situacaoTributariaICMS;
    }

    public void setSituacaoTributariaICMS(String situacaoTributariaICMS) {
        this.situacaoTributariaICMS = situacaoTributariaICMS;
    }

    public String getSituacaoTributariaPIS() {
        if (situacaoTributariaPIS == null) {
            situacaoTributariaPIS = "";
        }
        return situacaoTributariaPIS;
    }

    public void setSituacaoTributariaPIS(String situacaoTributariaPIS) {
        this.situacaoTributariaPIS = situacaoTributariaPIS;
    }

    public String getSituacaoTributariaCOFINS() {
        if (situacaoTributariaCOFINS == null) {
            situacaoTributariaCOFINS = "";
        }
        return situacaoTributariaCOFINS;
    }

    public void setSituacaoTributariaCOFINS(String situacaoTributariaCOFINS) {
        this.situacaoTributariaCOFINS = situacaoTributariaCOFINS;
    }

    public String getUnidadeMedida() {
        if (unidadeMedida == null) {
            unidadeMedida = UnidadeMedidaEnum.UNIDADE.getCodigo();
        }
        return unidadeMedida;
    }

    public void setUnidadeMedida(String unidadeMedida) {
        this.unidadeMedida = unidadeMedida;
    }

    public boolean isProdutoEstoque() {
        return tipoProduto != null && tipoProduto.equals(TipoProduto.PRODUTO_ESTOQUE.getCodigo());
    }

    public boolean isProdutoApresentarIcms() {
        return tipoProduto != null && (tipoProduto.equals(TipoProduto.PRODUTO_ESTOQUE.getCodigo())
                || tipoProduto.equals(TipoProduto.HOMEFIT.getCodigo()));
    }
    public Double getPercentualFederal() {
        if (percentualFederal == null) {
            percentualFederal = 0.0;
        }
        return percentualFederal;
    }

    public void setPercentualFederal(Double percentualFederal) {
        this.percentualFederal = percentualFederal;
    }

    public Double getPercentualEstadual() {
        if (percentualEstadual == null) {
            percentualEstadual = 0.0;
        }
        return percentualEstadual;
    }

    public void setPercentualEstadual(Double percentualEstadual) {
        this.percentualEstadual = percentualEstadual;
    }

    public Double getPercentualMunicipal() {
        if (percentualMunicipal == null) {
            percentualMunicipal = 0.0;
        }
        return percentualMunicipal;
    }

    public void setPercentualMunicipal(Double percentualMunicipal) {
        this.percentualMunicipal = percentualMunicipal;
    }

    public boolean isIsentoPIS() {
        return isentoPIS;
    }

    public void setIsentoPIS(boolean isentoPIS) {
        this.isentoPIS = isentoPIS;
    }

    public boolean isIsentoCOFINS() {
        return isentoCOFINS;
    }

    public void setIsentoCOFINS(boolean isentoCOFINS) {
        this.isentoCOFINS = isentoCOFINS;
    }

    public boolean isIsentoICMS() {
        return isentoICMS;
    }

    public void setIsentoICMS(boolean isentoICMS) {
        this.isentoICMS = isentoICMS;
    }

    public boolean isEnviarPercentualImposto() {
        return enviarPercentualImposto;
    }

    public void setEnviarPercentualImposto(boolean enviarPercentualImposto) {
        this.enviarPercentualImposto = enviarPercentualImposto;
    }

    public Integer getId_externo() {
        return id_externo;
    }

    public void setId_externo(Integer id_externo) {
        this.id_externo = id_externo;
    }

    public Integer getQtdePontos() {
        return qtdePontos;
    }

    public void setQtdePontos(Integer qtdePontos) {
        this.qtdePontos = qtdePontos;
    }

    public boolean isApresentarVendasOnline() {
        return apresentarVendasOnline;
    }

    public void setApresentarVendasOnline(boolean apresentarVendasOnline) {
        this.apresentarVendasOnline = apresentarVendasOnline;
    }

    public boolean isApresentarPactoApp() {
        return apresentarPactoApp;
    }

    public void setDestravarTranca(boolean destravarTranca) {
        this.destravarTranca = destravarTranca;
    }

    public boolean isDestravarTranca() {
        return destravarTranca;
    }

    public void setApresentarPactoApp(boolean apresentarPactoApp) {
        this.apresentarPactoApp = apresentarPactoApp;
    }

    public String getPosicaoEstoque() {
        if (posicaoEstoque == null) {
            posicaoEstoque = "";
        }
        return posicaoEstoque;
    }

    public void setPosicaoEstoque(String posicaoEstoque) {
        this.posicaoEstoque = posicaoEstoque;
    }

    public String getImagens() {
        if (imagens == null) {
            imagens = "";
        }
        return imagens;
    }

    public void setImagens(String imagens) {
        this.imagens = imagens;
    }

    public Integer getMaxDivisao() {
        if (maxDivisao == null) {
            maxDivisao = 0;
        }
        return maxDivisao;
    }

    public void setMaxDivisao(Integer maxDivisao) {
        this.maxDivisao = maxDivisao;
    }

    public String getValorExplicacaoUnidadeMedida() {
        if (getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo())) {
            return "por Kg";
        } else if (getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.TEMPO_HORA.getCodigo())) {
            return "por hora";
        } else {
            return "";
        }
    }

    public String getValorFinalConvertidoUnidadeMedida_Apresentar() {
        return Formatador.formatarValorMonetario(getValorFinalConvertidoUnidadeMedida());
    }

    public Double getValorFinalConvertidoUnidadeMedida() {
        if (getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo())) {
            return getValorFinal() * 1000;
        } else {
            return getValorFinal();
        }
    }

    public String getQuantidadeExplicacaoUnidadeMedida() {
        if (getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo())) {
            return "Qtd em Gramas";
        } else {
            return "Quantidade";
        }
    }

    public boolean isEnviaAliquotaNFePIS() {
        return enviaAliquotaNFePIS;
    }

    public void setEnviaAliquotaNFePIS(boolean enviaAliquotaNFePIS) {
        this.enviaAliquotaNFePIS = enviaAliquotaNFePIS;
    }

    public boolean isEnviaAliquotaNFeCOFINS() {
        return enviaAliquotaNFeCOFINS;
    }

    public void setEnviaAliquotaNFeCOFINS(boolean enviaAliquotaNFeCOFINS) {
        this.enviaAliquotaNFeCOFINS = enviaAliquotaNFeCOFINS;
    }

    public boolean isEnviaAliquotaNFeICMS() {
        return enviaAliquotaNFeICMS;
    }

    public void setEnviaAliquotaNFeICMS(boolean enviaAliquotaNFeICMS) {
        this.enviaAliquotaNFeICMS = enviaAliquotaNFeICMS;
    }

    public Integer getModalidadeVendasOnline() {
        return modalidadeVendasOnline;
    }

    public void setModalidadeVendasOnline(Integer modalidadeVendasOnline) {
        this.modalidadeVendasOnline = modalidadeVendasOnline;
    }

    public Double getPrecoCusto() {
        if (precoCusto == null) {
            precoCusto = 0.0;
        }
        return precoCusto;
    }

    public void setPrecoCusto(Double precoCusto) {
        this.precoCusto = precoCusto;
    }

    public Double getMargemLucro() {
        if (margemLucro == null) {
            margemLucro = 0.0;
        }
        return margemLucro;
    }

    public void setMargemLucro(Double margemLucro) {
        this.margemLucro = margemLucro;
    }

    public Integer getQtdConvites() {
        if (qtdConvites == null) {
            qtdConvites = 0;
        }
        return qtdConvites;
    }

    public void setQtdConvites(Integer qtdConvites) {
        this.qtdConvites = qtdConvites;
    }

    public String getCest() {
        if (cest == null) {
            cest = "";
        }
        return cest;
    }

    public void setCest(String cest) {
        this.cest = cest;
    }

    public Integer getCodigoProdutoSesi() {
        return codigoProdutoSesi;
    }

    public void setCodigoProdutoSesi(Integer codigoProdutoSesi) {
        this.codigoProdutoSesi = codigoProdutoSesi;
    }

    public Integer getNegocioSesi() {
        if(negocioSesi == null){
            negocioSesi = 0;
        }
        return negocioSesi;
    }

    public void setNegocioSesi(Integer negocioSesi) {
        this.negocioSesi = negocioSesi;
    }

    public String getCodigoBeneficioFiscal() {
        return codigoBeneficioFiscal;
    }

    public void setCodigoBeneficioFiscal(String codigoBeneficioFiscal) {
        this.codigoBeneficioFiscal = codigoBeneficioFiscal;
    }

    public Boolean getRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(Boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public Integer getcRSesi() {
        return cRSesi;
    }

    public void setcRSesi(Integer cRSesi) {
        this.cRSesi = cRSesi;
    }

    public Integer getProjetoSesi() {
        return projetoSesi;
    }

    public void setProjetoSesi(Integer projetoSesi) {
        this.projetoSesi = projetoSesi;
    }

    public Integer getContaFinanceiraSesi() {
        return contaFinanceiraSesi;
    }

    public void setContaFinanceiraSesi(Integer contaFinanceiraSesi) {
        this.contaFinanceiraSesi = contaFinanceiraSesi;
    }

    public boolean isApresentarPactoFlow() {
        return apresentarPactoFlow;
    }

    public void setApresentarPactoFlow(boolean apresentarPactoFlow) {
        this.apresentarPactoFlow = apresentarPactoFlow;
    }

    public String getDescricaoServicoMunicipio() {
        if(descricaoServicoMunicipio == null){
            descricaoServicoMunicipio = "" ;
        }
        return descricaoServicoMunicipio;
    }

    public void setDescricaoServicoMunicipio(String descricaoServicoMunicipio) {
        this.descricaoServicoMunicipio = descricaoServicoMunicipio;
    }

    public Integer getContratoTextoPadrao() {
        return contratoTextoPadrao;
    }

    public void setContratoTextoPadrao(Integer contratoTextoPadrao) {
        this.contratoTextoPadrao = contratoTextoPadrao;
    }

    public boolean isMesclado() {
        return mesclado;
    }

    public void setMesclado(boolean mesclado) {
        this.mesclado = mesclado;
    }

    public Integer getIdProdutoSMD() { return idProdutoSMD; }

    public void setIdProdutoSMD(Integer idProdutoSMD) { this.idProdutoSMD = idProdutoSMD; }

    public Integer getCodigoFormulario() { return codigoFormulario; }

    public void setCodigoFormulario(Integer codigoFormulario) { this.codigoFormulario = codigoFormulario; }

    public boolean isExibirRelatorioSMD() { return exibirRelatorioSMD; } // padrão javabeans

    public void setExibirRelatorioSMD(boolean exibirRelatorioSMD) { this.exibirRelatorioSMD = exibirRelatorioSMD; }
}
