package negocio.comuns.plano.webservice;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.arquitetura.exceptions.ServiceException;
import controle.contrato.ContratoControle;
import negocio.comuns.ConfiguracaoEmpresaTotemVO;
import negocio.comuns.TotemTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.ConfigTotemEnum;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ContratoWS;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.ConvenioCobrancaWS;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoEmpresaVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.PlanoWS;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoEmpresaTotem;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.facade.jdbc.vendas.VendasConfigVO;
import negocio.oamd.CampanhaCupomDescontoVO;
import negocio.oamd.CupomDescontoVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.gatewaypagamento.PagamentoService;
import servicos.impl.gatewaypagamento.ThreadNotificacaoTransacao;
import servicos.impl.oamd.OAMDService;
import servicos.operacoes.RenovacaoAutomaticaRecorrenciaService;
import servicos.operacoes.RenovacaoAutomaticaService;
import servicos.propriedades.PropsService;
import servicos.vendasonline.ClienteService;
import servicos.vendasonline.VendasOnlineService;
import servicos.vendasonline.dto.RetornoVendaDTO;
import servicos.vendasonline.dto.VendaDTO;

import javax.annotation.Resource;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.xml.ws.WebServiceContext;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by glauco on 28/11/2014
 */
@WebService(name = "NegociacaoWS", serviceName = "NegociacaoWS")
public class NegociacaoWS {


    @Resource
    private WebServiceContext context;

    @WebMethod(operationName = "consultarPlanos")
    public String consultarPlanos(@WebParam(name = "key") String key,
            @WebParam(name = "empresa") int empresa,
            @WebParam(name = "codigoPlano") int codigoPlano) {

        VendasOnlineService service = null;
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            List<PlanoVO> planos = new ArrayList<PlanoVO>();
            if (codigoPlano == 0 && empresa == 0) {
                planos = acessoControle.getPlanoDao().consultarVigentesTodasEmpresas(Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS,true);
            }else if(codigoPlano != 0 && empresa == 0){
                planos.add(acessoControle.getPlanoDao().consultarPorChavePrimaria(codigoPlano, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, true));
            }else {
                planos = acessoControle.getPlanoDao().consultarVigentes(empresa, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS,true, true, false);
            }
            service = new VendasOnlineService(null, acessoControle.getCon());
            Boolean cobrarPrimeiraParcelaCompra = service.obterConfigPrimeiraParcela(empresa);

            JSONArray planosJson = new JSONArray();
            for (PlanoVO plano : planos) {
                if(plano != null){
                    try {
                        if (plano.getParcelamentoOperadora() != null && plano.getParcelamentoOperadora() &&
                                plano.isParcelamentoOperadoraDuracao()) {
                            Integer parcelamentoDuracao = 0;
                            if (plano.getRecorrencia() != null && plano.getRecorrencia() && plano.getPlanoRecorrencia() != null) {
                                parcelamentoDuracao = plano.getPlanoRecorrencia().getDuracaoPlano();
                            } else if (!UteisValidacao.emptyList(plano.getPlanoDuracaoVOs())) {
                                Integer max = 0;
                                for (PlanoDuracaoVO planoDuracaoVO : plano.getPlanoDuracaoVOs()) {
                                    if (planoDuracaoVO.getNumeroMeses() > max) {
                                        max = planoDuracaoVO.getNumeroMeses();
                                    }
                                }
                                parcelamentoDuracao = max;
                            }

                            //limitar no max de vezes parcelar
                            if (parcelamentoDuracao < plano.getMaximoVezesParcelar()) {
                                plano.setMaximoVezesParcelar(parcelamentoDuracao);
                            }
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                    PlanoWS planoWS = obterPlanoWSValidandoEmpresa(empresa, plano, acessoControle.getCon());
                    planoWS.setCobrarPrimeiraParcelaCompra(cobrarPrimeiraParcelaNaCompra(cobrarPrimeiraParcelaCompra, plano));

                    if(!UteisValidacao.emptyNumber(empresa) && service.obterConfigApresentarValorTotalDoPlano(empresa)){
                        planoWS.setValorTotalDoPlano(getValorPlano(key, empresa, service, plano));
                    }

                    JSONObject planoJsonObj = new JSONObject(planoWS);
                    planosJson.put(planoJsonObj);
                }
            }
            return planosJson.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        } finally {
            service = null;
        }
    }

    private Double getValorPlano(String key, int empresa, VendasOnlineService service, PlanoVO plano) throws ServiceException {
        VendaDTO vendaDTO = new VendaDTO();
        vendaDTO.setUnidade(empresa);
        vendaDTO.setPlano(plano.getCodigo());
        vendaDTO.setNrVezesDividir(plano.getPlanoDuracaoVOs().get(0).getNumeroMeses());
        vendaDTO.setDiaVencimento(Calendario.hoje().getDate());

        ContratoVO contratoVO = service.simular(key, vendaDTO, empresa);
        return contratoVO.getValorFinal();
    }

    @WebMethod(operationName = "consultarPlanoContrato")
    public String consultarPlanoContrato(@WebParam(name = "key") String key,
                                  @WebParam(name = "codigoContrato") int codigoContrato) {

        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            PlanoVO plano = acessoControle.getPlanoDao().consultarPorCodigoContrato(codigoContrato, false, Uteis.NIVELMONTARDADOS_TODOS);
            JSONObject planoJsonObj = new JSONObject();
            if(plano != null){
                PlanoWS planoWS = plano.toWS(acessoControle.getCon());
                planoJsonObj = new JSONObject(planoWS);
            }
            return planoJsonObj.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultarConveniosCobranca")
    public List<ConvenioCobrancaWS> consultarConveniosCobranca(@WebParam(name = "key") String key,
            @WebParam(name = "empresa") int empresa) {
        try {
            List<ConvenioCobrancaVO> list = DaoAuxiliar.retornarAcessoControle(key).getConvenioCobrancaDao().consultarPorEmpresa(empresa, SituacaoConvenioCobranca.ATIVO, false, null, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            List<ConvenioCobrancaWS> conveniosCobranca = new ArrayList<ConvenioCobrancaWS>();
            for (ConvenioCobrancaVO obj : list) {
                conveniosCobranca.add(obj.toWS());
            }

            return conveniosCobranca;
        } catch (Exception ex) {
            return null;
        }
    }

    @WebMethod(operationName = "incluirAutorizacaoCobrancaCliente")
    public String incluirAutorizacaoCobrancaCliente(@WebParam(name = "key") final String key,
                                                    @WebParam(name = "cliente") int cliente,
                                                    @WebParam(name = "operadoraCartao") OperadorasExternasAprovaFacilEnum operadoraCartao,
                                                    @WebParam(name = "numeroCartao") final String numerocartao,
                                                    @WebParam(name = "validadeCartao") String validadeCartao,
                                                    @WebParam(name = "convenioCobranca") int convenioCobranca,
                                                    @WebParam(name = "cpfTitular") String cpfTitular,
                                                    @WebParam(name = "titularCartao") String titularCartao,
                                                    @WebParam(name = "cvv") String cvv,
                                                    @WebParam(name = "usarConfConvEmpresa") boolean usarConfConvEmpresa) {
        Cliente clienteDAO;
        AutorizacaoCobrancaCliente autoDAO;
        ConfiguracaoEmpresaTotem totemDAO;
        ConvenioCobranca convenioDAO;
        Connection con = null;
        try {

            if (validadeCartao == null || validadeCartao.length() != 5) {
                throw new Exception("Validade do cartão inválida.");
            }

            con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(DaoAuxiliar.retornarAcessoControle(key).getCon());
            con.setAutoCommit(false);

            clienteDAO = new Cliente(con);
            autoDAO = new AutorizacaoCobrancaCliente(con);
            totemDAO = new ConfiguracaoEmpresaTotem(con);
            convenioDAO = new ConvenioCobranca(con);

            String mesValidade = "";
            String anoValidade = "";
            mesValidade = validadeCartao.substring(0, 2);
            anoValidade = validadeCartao.substring(3, 5);
            validadeCartao = mesValidade + "/" + (Integer.parseInt(anoValidade) + 2000);

            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);

            String where = " ativa = true and cliente = " + clienteVO.getCodigo() + " and tipoautorizacao = " + TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId();
            List<AutorizacaoCobrancaClienteVO> listaAutorizacaoBD = autoDAO.consulta(where, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO : listaAutorizacaoBD) {
                autoDAO.alterarSituacaoAutorizacaoCobranca(false, autorizacaoCobrancaClienteVO, "Exclusão de cartão pelo Serviço NegociacaoWS metodo {incluirAutorizacaoCobrancaCliente}");
            }

            AutorizacaoCobrancaClienteVO autorizacao = new AutorizacaoCobrancaClienteVO();
            autorizacao.setCliente(clienteVO);
            autorizacao.setCpfTitular(cpfTitular);
            autorizacao.setNomeTitularCartao(titularCartao);
            autorizacao.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
            autorizacao.setOperadoraCartao(operadoraCartao);
            autorizacao.setNumeroCartao(numerocartao);
            autorizacao.setValidadeCartao(validadeCartao);
            autorizacao.setTipoACobrar(clienteVO.getEmpresa().getTipoParcelasCobrarVendaSite());
            if (usarConfConvEmpresa) {
                List<TotemTO> totemTOS = totemDAO.obterConfigs(clienteVO.getEmpresa().getCodigo(), key);
                if (!UteisValidacao.emptyList(totemTOS)) {
                    Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO> mapa = totemTOS.get(0).getConfigs();
                    autorizacao.getConvenio().setCodigo(mapa.get(ConfigTotemEnum.CONVENIO_COBRANCA).getValorAsInt());
                }
            } else {
                autorizacao.getConvenio().setCodigo(convenioCobranca);
            }


            if (UteisValidacao.emptyNumber(autorizacao.getConvenio().getCodigo())) {
                //não encontrou nenhum convenio.
                //verificar se a academia só existe 1 convênio, se sim.. utilizar ele.
                List<ConvenioCobrancaVO> convenios = convenioDAO.consultarPorEmpresaSituacaoTipoAutorizacao(clienteVO.getEmpresa().getCodigo(),
                        SituacaoConvenioCobranca.ATIVO, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (convenios.size() == 1) {
                    autorizacao.setConvenio(convenios.get(0));
                }
            }

            autoDAO.incluir(autorizacao);
            autoDAO.incluirLogAlteracaoAutorizacaoCobrancaFeitoViaSite(autorizacao, OrigemCobrancaEnum.TOTEM.getDescricao(), listaAutorizacaoBD.size() > 0 ? false : true, null);

            con.commit();
            return "ok";
        } catch (Exception ex) {
            try {
                if (con != null) {
                    con.rollback();
                }
            } catch (Exception ex1) {
                ex1.printStackTrace();
            }
            return "ERRO: " + ex.getMessage();
        } finally {
            clienteDAO = null;
            autoDAO = null;
            totemDAO = null;
            convenioDAO = null;
            try {
                if (con != null) {
                    con.setAutoCommit(true);
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @WebMethod(operationName = "alterarAutorizacaoCobrancaCliente")
    public String alterarAutorizacaoCobrancaCliente(@WebParam(name = "key") final String key,
                                                    @WebParam(name = "codigoCliente") int codigoCliente,
                                                    @WebParam(name = "operadoraCartao") OperadorasExternasAprovaFacilEnum operadoraCartao,
                                                    @WebParam(name = "numeroCartao") final String numerocartao,
                                                    @WebParam(name = "validadeCartao") String validadeCartao,
                                                    @WebParam(name = "convenioCobranca") int convenioCobranca,
                                                    @WebParam(name = "cpfTitular") String cpfTitular,
                                                    @WebParam(name = "titularCartao") String titularCartao,
                                                    @WebParam(name = "cvv") String cvv) {

        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            if (validadeCartao.isEmpty() || validadeCartao.length() != 5) {
                throw new Exception("Validade do cartão inválida.");
            }

            String mesValidade = "";
            String anoValidade = "";
            mesValidade = validadeCartao.substring(0, 2);
            anoValidade = validadeCartao.substring(3, 5);
            validadeCartao = mesValidade + "/" + (Integer.valueOf(anoValidade) + 2000);

            ClienteVO clienteVO = acessoControle.getClienteDao().consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);

            AutorizacaoCobrancaClienteVO autorizacaoBD = acessoControle.getAutorizacaoCobrancaDao().consultar(codigoCliente, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
            if (autorizacaoBD == null){
                AutorizacaoCobrancaClienteVO autorizacao = new AutorizacaoCobrancaClienteVO();
                autorizacao.setCodigo(autorizacaoBD.getCodigo());
                autorizacao.getCliente().setCodigo(codigoCliente);
                autorizacao.setCpfTitular(cpfTitular);
                autorizacao.setNomeTitularCartao(titularCartao);
                autorizacao.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
                autorizacao.setOperadoraCartao(operadoraCartao);
                autorizacao.setNumeroCartao(numerocartao);
                autorizacao.setValidadeCartao(validadeCartao);
                autorizacao.setTipoACobrar(clienteVO.getEmpresa().getTipoParcelasCobrarVendaSite());
                autorizacao.getConvenio().setCodigo(convenioCobranca);
                acessoControle.getAutorizacaoCobrancaDao().incluir(autorizacao);
            }else{
                autorizacaoBD.setNovoObj(false);
                autorizacaoBD.getCliente().setCodigo(codigoCliente);
                autorizacaoBD.setCpfTitular(cpfTitular);
                autorizacaoBD.setNomeTitularCartao(titularCartao);
                autorizacaoBD.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
                autorizacaoBD.setOperadoraCartao(operadoraCartao);
                autorizacaoBD.setNumeroCartao(numerocartao);
                autorizacaoBD.setValidadeCartao(validadeCartao);
                autorizacaoBD.setTipoACobrar(clienteVO.getEmpresa().getTipoParcelasCobrarVendaSite());
                autorizacaoBD.getConvenio().setCodigo(convenioCobranca);
                autorizacaoBD.setGravarLogAlteracaoViaSite(true);

                acessoControle.getAutorizacaoCobrancaDao().alterar(autorizacaoBD);
            }


            return "ok";
        } catch (Exception ex) {
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "incluirAutorizacaoCobrancaDCOCliente")
    public String incluirAutorizacaoCobrancaDCOCliente(@WebParam(name = "key") final String key,
                                                       @WebParam(name = "cliente") int cliente,
                                                       @WebParam(name = "agencia") int agencia,
                                                       @WebParam(name = "agenciaDV") String agenciaDV,
                                                       @WebParam(name = "contaCorrente") int contaCorrente,
                                                       @WebParam(name = "contaCorrenteDV") String contaCorrenteDV,
                                                       @WebParam(name = "codigoOperacao") String codigoOperacao,
                                                       @WebParam(name = "banco") int banco,
                                                       @WebParam(name = "convenioCobranca") int convenioCobranca,
                                                       @WebParam(name = "usarConfConvEmpresa") boolean usarConfConvEmpresa,
                                                       @WebParam(name = "cpfTitular") String cpfTitular,
                                                       @WebParam(name = "nomeTitular") String nomeTitular) {

        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            ClienteVO clienteVO = acessoControle.getClienteDao().consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);


            String where = " ativa = true and tipoacobrar = " + clienteVO.getEmpresa().getTipoParcelasCobrarVendaSite() + " and cliente = " + clienteVO.getCodigo() + " and tipoautorizacao = " + TipoAutorizacaoCobrancaEnum.DEBITOCONTA.getId();
            List<AutorizacaoCobrancaClienteVO> listaAutorizacaoBD = acessoControle.getAutorizacaoCobrancaDao().consulta(where, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO : listaAutorizacaoBD) {
                acessoControle.getAutorizacaoCobrancaDao().alterarSituacaoAutorizacaoCobranca(false, autorizacaoCobrancaClienteVO, "Inclusão de cartão pelo Serviço NegociacaoWS metodo {incluirAutorizacaoCobrancaDCOCliente}");
            }

            AutorizacaoCobrancaClienteVO autorizacao = new AutorizacaoCobrancaClienteVO();
            autorizacao.getCliente().setCodigo(cliente);
            autorizacao.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
            autorizacao.setAgencia(agencia);
            autorizacao.setAgenciaDV(agenciaDV);
            autorizacao.setContaCorrente((long) contaCorrente);
            autorizacao.setContaCorrenteDV(contaCorrenteDV);
            autorizacao.setCodigoOperacao(codigoOperacao);
            autorizacao.setCpfTitular(cpfTitular);
            autorizacao.setNomeTitularCartao(nomeTitular);
            autorizacao.setTipoACobrar(clienteVO.getEmpresa().getTipoParcelasCobrarVendaSite());

            ConvenioCobrancaVO convenioCobrancaVO;
            if (usarConfConvEmpresa) {
                convenioCobrancaVO = acessoControle.getConvenioCobrancaDao().consultarPorTipoEBanco(null, null, banco);
            } else {
                convenioCobrancaVO = acessoControle.getConvenioCobrancaDao().consultarPorCodigoEmpresa(convenioCobranca, clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            autorizacao.setBanco(convenioCobrancaVO.getBanco());
            autorizacao.setConvenio(convenioCobrancaVO);

            acessoControle.getAutorizacaoCobrancaDao().incluir(autorizacao);
            return "ok";
        } catch (Exception ex) {
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "calcularValorPlano")
    public String calcularValorPlano(@WebParam(name = "key") final String key,
                                     @WebParam(name = "plano") int plano,
                                     @WebParam(name = "cliente") int cliente,
                                     @WebParam(name = "nrParcelasAdesao") int nrParcelasAdesao) {
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);

            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            ContratoControle contratoControle = obterContratoControle(plano, cliente, acessoControle);
            if (contratoControle.getContratoVO().getPlano().getInicioMinimoContrato() != null && Calendario.maior(contratoControle.getContratoVO().getPlano().getInicioMinimoContrato() , Calendario.hoje())) {
                contratoControle.setDiaVencimentoCartaoRecorrencia(Uteis.getDiaMesData(contratoControle.getContratoVO().getPlano().getInicioMinimoContrato()));
            } else {
                contratoControle.setDiaVencimentoCartaoRecorrencia(Calendario.getInstance(Calendario.hoje()).get(Calendar.DAY_OF_MONTH));
            }
            int parcelasAdesao = (nrParcelasAdesao > 0) ? nrParcelasAdesao : 1;
            contratoControle.getContratoVO().setNrParcelasAdesao(parcelasAdesao);

            String fecharNegociacao = contratoControle.fecharNegociacao();
            if (fecharNegociacao.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }

            return fecharNegociacao + " - " + Formatador.formatarValorMonetario(contratoControle.getContratoVO().getValorFinal());
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultarEquivalenciaPlanoUnidadeDestino")
    public String consultarEquivalenciaPlanoUnidadeDestino(@WebParam(name = "chaveDestino") final String chaveDestino,
                                                           @WebParam(name = "codigoEmpresaFinanceiroOrigem") int codigoEmpresaFinanceiroOrigem,
                                                           @WebParam(name = "codigoPlanoOrigem") int codigoPlanoOrigem) {
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(chaveDestino);
            Conexao.guardarConexaoForJ2SE(chaveDestino, acessoControle.getCon());

            OAMDService oamdService = new OAMDService();
            PlanoWS planoWS = oamdService.consultarEquivalenciaPlanoUnidadeDestino(codigoPlanoOrigem,codigoEmpresaFinanceiroOrigem,chaveDestino,acessoControle.getCon());
            oamdService = null;
            JSONObject jsonObject = new JSONObject(planoWS);
            return jsonObject.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            PlanoWS planoWS = new PlanoWS();
            planoWS.setMsgValidacao(ex.getMessage());
            return (new JSONObject(planoWS)).toString();
        }
    }

    @WebMethod(operationName = "transferirAlunoDeUnidade")
    public String transferirAlunoDeUnidade(@WebParam(name = "codigoEmpresaFinanceiro") final int codigoEmpresaFinanceiro,
                                           @WebParam(name = "chaveDestino") final String chaveDestino,
                                           @WebParam(name = "codigoCliente") int codigoCliente,
                                           @WebParam(name = "codigoPlano") int codigoPlano,
                                           @WebParam(name = "emailCliente") final String emailCliente) {
        try{
            OAMDService oamdService = new OAMDService();
            ContratoVO contratoVO = oamdService.transferirAlunoDeUnidade(codigoEmpresaFinanceiro,chaveDestino,codigoCliente,codigoPlano,emailCliente);
            oamdService = null;
            JSONObject jsonObject = new JSONObject(contratoVO.toWS(false));
            return jsonObject.toString();

        }catch (Exception ex){
            return tratarException(ex);
        }
    }

    @WebMethod(operationName = "gerarBoletoParcela")
    public String gerarBoletoParcela(@WebParam(name = "key") final String key,
                                    @WebParam(name = "codigoPessoa") int codigoPessoa,
                                    @WebParam(name = "codigoParcela") int codigoParcela) {
        String retorno = "";
        try{
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            String requestURL = Uteis.obterRequestURL(context);
            retorno = acessoControle.getBoletoServiceInterface().gerarBoleto(key,codigoParcela,codigoPessoa,true,requestURL);
        }catch (Exception ex) {
          retorno = "ERRO: " + ex.getMessage();
          Uteis.logar(ex, this.getClass());
        }
        return retorno;

    }

    @WebMethod(operationName = "gerarBoletoContrato")
    public String gerarBoletoContrato(@WebParam(name = "key") final String key,
                                     @WebParam(name = "codigoPessoa") int codigoPessoa,
                                     @WebParam(name = "codigoContrato") int codigoContrato) {
        String retorno = "";
        try{
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            String requestURL = Uteis.obterRequestURL(context);
            retorno = acessoControle.getBoletoServiceInterface().gerarBoletoTodasParcelasDoContrato(key,codigoContrato,codigoPessoa,true,requestURL);
        }catch (Exception ex) {
            retorno = "ERRO: " + ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }
        return retorno;

    }

    @WebMethod(operationName = "verificarMulta")
    public Double verificarMulta(@WebParam(name = "key") final String key,
                                 @WebParam(name = "empresa") final Integer empresa,
                                 @WebParam(name = "parcelas") final String parcelas) {

        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            String[] splitparcelas = parcelas.split("\\_");
            List<Integer> prcls = new ArrayList<Integer>();

            for(String p : splitparcelas){
                if(!UteisValidacao.emptyString(p)){
                    prcls.add(Integer.valueOf(p));
                }
            }

            List<MovParcelaVO> listaParcelas = new ArrayList<MovParcelaVO>();
            for(Integer parcela : prcls){
                MovParcelaVO parc = acessoControle.getMovParcelaDao().consultarPorCodigo(parcela, Uteis.NIVELMONTARDADOS_PARCELA);
                parc.setMovProdutoParcelaVOs(acessoControle.getMovProdutoParcelaDao().consultarPorCodigoMovParcela(parc.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                listaParcelas.add(parc);
            }

            EmpresaVO empresaVO = acessoControle.getEmpresaDao().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            return acessoControle.getMovParcelaDao().montarMultaJurosParcelaVencida(empresaVO, listaParcelas, Calendario.hoje(),
                    false, 1.0, null);
        }catch (Exception e){
            return 0.0;
        }
    }
    @WebMethod(operationName = "gravarPagamento")
    public String gravarPagamento(@WebParam(name = "key") final String key,
                                 @WebParam(name = "autorizacao") final String autorizacao,
                                 @WebParam(name = "parcelas") final String parcelas,
                                 @WebParam(name = "tipo") final String tipo,
                                 @WebParam(name = "valor") double valor,
                                 @WebParam(name = "totem") final String totem,
                                 @WebParam(name = "comprovante") final String comprovante,
                                 @WebParam(name = "empresa") final Integer empresa
                                  ) {
        String retorno;
        Log logDAO = null;
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            logDAO = new Log(Conexao.getInstance().obterNovaConexaoBaseadaOutra(acessoControle.getCon()));
            logDAO.incluirLogCappta(totem, comprovante, "gravarPagamento-inicio");

            String[] splitparcelas = parcelas.split("\\_");
            List<Integer> prcls = new ArrayList<Integer>();
            for(String p : splitparcelas){
                if(!UteisValidacao.emptyString(p)){
                    prcls.add(Integer.valueOf(p));
                }
            }
            Integer pagamentoTotem = acessoControle.getMovPagamentoDao().incluirPagamentoTotem(prcls, tipo, autorizacao, totem, comprovante, empresa);
            retorno = pagamentoTotem.toString();
            logDAO.incluirLogCappta(totem, comprovante, "gravarPagamento-fim");
        } catch (Exception ex) {
            retorno = "ERRO: " + ex.getMessage();
            Uteis.logar(ex, this.getClass());
            if (logDAO != null && !UteisValidacao.emptyString(comprovante) && comprovante.contains("acquirerAuthorizationCode")) {
                logDAO.incluirLogCappta(totem, ex.getMessage(), "gravarPagamentoErro");
                logDAO.incluirLogCappta(totem, comprovante, "gravarPagamentoComprovanteErro");
                notificarAPIChatGoogle(ex.getMessage(), totem, key, empresa);
            }
        } finally {
            logDAO = null;
        }
        return retorno;
    }

    private void notificarAPIChatGoogle(String msgErro, String totem, String chaveOAMD, Integer codEmpresa) {
        try {
            String url = PropsService.getPropertyValue(PropsService.urlApiChatGoogleCappta);

            StringBuilder sb = new StringBuilder();
            sb.append("*Falha na baixa da Parcela pela Linx* \n");
            sb.append("*Código Empresa:* " + codEmpresa + "\n");
            sb.append("*Chave ZW:* " + chaveOAMD + "\n");
            sb.append("*Horário:* " + Uteis.getDataComHora(Calendario.hoje()) + "\n");
            sb.append("*Totem:* " + totem + "\n");
            sb.append("*Motivo:* " + msgErro + ") \n");

            JSONObject envio = new JSONObject();
            envio.put("text", sb.toString());

            RequestHttpService service = new RequestHttpService();
            Uteis.logarDebug("Vou enviar a notificação de erro Linx para a API do chat google.");
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, null, null, envio.toString(), MetodoHttpEnum.POST);

            if (respostaHttpDTO.getHttpStatus() == 200) {
                Uteis.logarDebug("Notificação para a API do chat google enviada com SUCESSO");
            } else {
                Uteis.logarDebug("ERRO ao notificar a API do google. | Resposta API Google: " + respostaHttpDTO.getResponse());
            }
        } catch (Exception e) {
        }
    }

    @WebMethod(operationName = "gravarContrato")
    public String gravarContrato(@WebParam(name = "key") final String key,
                                 @WebParam(name = "plano") int plano,
                                 @WebParam(name = "cliente") int cliente,
                                 @WebParam(name = "nrParcelasAdesao") int nrParcelasAdesao,
                                 @WebParam(name = "nrParcelasProduto") int nrParcelasProduto,
                                 @WebParam(name = "numeroCupomDesconto") String numeroCupomDesconto,
                                 @WebParam(name = "nrParcelasPagamento") int nrParcelasPagamento,
                                 @WebParam(name = "diaVencimento") Integer diaVencimento,
                                 @WebParam(name = "consultor") Integer consultor) {
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        } catch (Exception ex) {
            return tratarException(ex);
        }

        UsuarioVO usuarioAdmin;
        try {
            usuarioAdmin = acessoControle.getUsuarioDao().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
        } catch (Exception ex) {
            return tratarException(ex);
        }

        try {
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            Integer empresaCod = acessoControle.getPlanoDao().consultarPorChavePrimaria(plano, Uteis.NIVELMONTARDADOS_MINIMOS).getEmpresa().getCodigo();
            ContratoVO contratoVO = acessoControle.getContratoDao().gravarContratoSite(key, plano, cliente, nrParcelasAdesao, nrParcelasProduto, numeroCupomDesconto, true, true, nrParcelasPagamento, false, 0, empresaCod, false, null, diaVencimento, consultor);
            ContratoWS contratoWS = contratoVO.toWS(false);
            JSONObject contrato = new JSONObject(contratoWS);
            try {
                if (contratoVO.getPlano().getTotem()) {
                    SuperControle.notificarRecursoEmpresa(key, RecursoSistema.CONTRATOS_AUTOTENDIMENTO, usuarioAdmin,
                            (acessoControle.getEmpresaDao().consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS)));
                }
            } catch (Exception e) {

            }
            return contrato.toString();
        } catch (Exception ex) {
            acessoControle.getLogDao().incluirLogErroInclusaoContrato(cliente, usuarioAdmin, ex.getMessage());
            return tratarException(ex);
        }
    }

    private String tratarException(Exception ex) {
        Uteis.logar(ex, this.getClass());
        ContratoWS contratoWS = new ContratoWS();
        contratoWS.setMsgValidacao(ex.getMessage());
        return (new JSONObject(contratoWS)).toString();
    }


    @WebMethod(operationName = "imprimirContrato")
    public String imprimirContrato(@WebParam(name = "key") final String key,
            @WebParam(name = "contrato") int contrato) {
        String retorno = "";
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);

            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            ContratoTextoPadrao contratoTextoPadraoDao = new ContratoTextoPadrao(acessoControle.getCon());
            return contratoTextoPadraoDao.consultarHtmlContrato(contrato,false);
        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }

        return retorno;
    }

    @WebMethod(operationName = "imprimirTermoAceite")
    public String imprimirTermoAceite(@WebParam(name = "key") final String key,
                                      @WebParam(name = "codigoPlano") Integer codigoPlano) {
        String retorno = "";
        AcessoControle acessoControle;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            boolean encontrouTermoEspecifico = false;
            if (codigoPlano != null && codigoPlano > 0) {
                PlanoVO planoVO = acessoControle.getPlanoDao().consultarPorChavePrimaria(codigoPlano, Uteis.NIVELMONTARDADOS_MINIMOS);
                if (planoVO.getTermoAceite().getCodigo() > 0) {
                    PlanoTextoPadraoVO planoTextoPadraoVO = acessoControle.getPlanoTextoPadraoDao().consultarPorChavePrimariaSituacaoTipo(planoVO.getTermoAceite().getCodigo(), "AT", "VO", false, Uteis.NIVELMONTARDADOS_TODOS);
                    retorno = planoTextoPadraoVO.getTexto();
                    encontrouTermoEspecifico = true;
                }
            }

            if (!encontrouTermoEspecifico) {
                List<PlanoTextoPadraoVO> modelosTermoAceite = acessoControle.getPlanoTextoPadraoDao().consultarPorCodigoSituacaoTipo(0, "AT", "VO", false, Uteis.NIVELMONTARDADOS_TODOS);
                if (modelosTermoAceite.size() > 0) {
                    retorno = modelosTermoAceite.get(0).getTexto();
                }
            }
        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }

        return retorno;
    }

    private ContratoControle obterContratoControle(int plano, int cliente, AcessoControle acessoControle) {
        try {
            UsuarioVO usuarioVO = acessoControle.getUsuarioDao().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            ClienteVO clienteVO = acessoControle.getClienteDao().consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PlanoVO planoVO = acessoControle.getPlanoDao().consultarPorChavePrimaria(plano, false, Uteis.NIVELMONTARDADOS_TODOS);
            planoVO.filtrarPlanoProdutoSugeridoAtivoPlano();
            ContratoVO novoContrato = new ContratoVO();
            novoContrato.setPlano(planoVO);
            novoContrato.setEmpresa(clienteVO.getEmpresa());
            novoContrato.setPessoa(clienteVO.getPessoa());

            ContratoControle contratoControle = new ContratoControle(clienteVO);
            contratoControle.inicializarDadosReferenteAoPlano();
            contratoControle.setContratoVO(novoContrato);
            contratoControle.selecionarPlano(usuarioVO);

            for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
                contratoModalidadeVO.getModalidade().setModalidadeEscolhida(true);
                contratoControle.selecionarModalidade(contratoModalidadeVO);
            }

            for (Object pcpVO : contratoControle.getContratoVO().getPlanoDuracao().getPlanoCondicaoPagamentoVOs()) {
                PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = (PlanoCondicaoPagamentoVO) pcpVO;
                planoCondicaoPagamentoVO.getCondicaoPagamento().setCondicaoPagamentoEscolhida(true);
                contratoControle.selecionarCondicaoPagamento(planoCondicaoPagamentoVO);
            }

            return contratoControle;
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return null;
        }
    }

    @WebMethod(operationName = "enviarEmail")
    public String enviarEmail(@WebParam(name = "key") String key,
            @WebParam(name = "contrato") int contrato,
            @WebParam(name = "recibo") int recibo) {

        String retorno = "";
        AcessoControle acessoControle = null;
        try {

            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = SuperControle.getConfiguracaoSMTPNoReply();
            if (UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer())) {
                throw new Exception("Configure o servidor de envio de e-mail!");
            }

            if (contrato != 0) {
                ContratoVO c = acessoControle.getContratoDao().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
                c.setMovParcelaVOs(acessoControle.getMovParcelaDao().consultarPorContrato(c.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                c.setPessoa(acessoControle.getPessoaDao().consultarPorChavePrimaria(c.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                c.setEmpresa(acessoControle.getEmpresaDao().consultarPorChavePrimaria(c.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                c.setResponsavelContrato(acessoControle.getUsuarioDao().consultarPorChavePrimaria(c.getResponsavelContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                c.getPlano().setPlanoTextoPadrao(acessoControle.getPlanoTextoPadraoDao().consultarPorChavePrimaria(c.getContratoTextoPadrao().getPlanoTextoPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

                String texto = new ContratoTextoPadrao(acessoControle.getClienteDao().getCon()).consultarHtmlContrato(contrato,false);
                UteisEmail email = new UteisEmail();
//                texto = superControle.arranjarImagens(texto, c);
                email.novo(c.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", configuracaoSistemaCRMVO);
                String[] emails = new String[c.getPessoa().getEmailVOs().size()];
                int i = 0;
                for (Object obj : c.getPessoa().getEmailVOs()) {
                    EmailVO emailVO = (EmailVO) obj;
                    emails[i] = emailVO.getEmail();
                    i++;
                }
                email.enviarEmailN(emails, texto, c.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", "");
                retorno = "Sucesso";

            } else if (recibo != 0) {
            }

            //ClienteVO cli = acessoControle.getClienteDao().consultarPorCodigoPessoa(cliente, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }
        return retorno;
    }

    @WebMethod(operationName = "obterDiasBonusContrato")
    public String obterDiasBonusContrato(@WebParam(name = "key") final String key, @WebParam(name = "contrato") int contrato) {
        String retorno = "";
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            Integer nrDiasOperacoesAux = 0;
            List listaOperacoesContrato = acessoControle.getContratoOperacaoDao().consultarPorContrato(contrato, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (Object aListaOperacoesContrato : listaOperacoesContrato) {
                ContratoOperacaoVO obj = (ContratoOperacaoVO) aListaOperacoesContrato;
                if (obj.getBonusAcrescimo()) {
                    nrDiasOperacoesAux += obj.obterNrDiasContratoOperacao();
                }
            }

            retorno = nrDiasOperacoesAux.toString();
        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }

        return retorno;
    }


    @WebMethod(operationName = "horariosTurma")
    public String horariosTurma(@WebParam(name = "key") final String key, @WebParam(name = "contrato") int contrato) {
        String retorno = "";
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            retorno = acessoControle.getHorarioTurmaDao().horarioTurmaSimplificado(contrato);
        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }

        return retorno;
    }

    @WebMethod(operationName = "obterAutorizacaoMaisRecenteAluno")
    public String obterAutorizacaoMaisRecenteAluno(@WebParam(name = "key") final String key, @WebParam(name = "cliente") int cliente){
        String retorno = "";
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            JSONObject cartao = acessoControle.getAutorizacaoCobrancaDao().obterUltimoCartaoAtivoAluno(cliente);
            retorno = cartao.toString();
        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }

        return retorno;
    }

    @WebMethod(operationName = "obterRecibosCliente")
    public String obterRecibosCliente(@WebParam(name = "key") final String key, @WebParam(name = "cliente") int cliente){
        String retorno = "";
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            List<MovPagamentoVO> movPagamentoVOS = acessoControle.getMovPagamentoDao().consultarSimplificadoCliente(cliente);
            JSONArray array = new JSONArray();
            for(MovPagamentoVO mpto : movPagamentoVOS){
                JSONObject json = new JSONObject();
                json.put("recibo", mpto.getReciboPagamento().getCodigo());
                json.put("datapagamento", Calendario.getDataAplicandoFormatacao(mpto.getDataLancamento(), "dd/MM/yyyy"));
                json.put("valor", Formatador.formatarValorMonetarioSemMoeda(mpto.getValor()));
                json.put("forma", TipoFormaPagto.getTipoFormaPagtoSigla(mpto.getFormaPagamento().getTipoFormaPagamento()).getDescricao());
                array.put(json);
            }
            retorno = array.toString();
        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }

        return retorno;
    }

    @WebMethod(operationName = "renovarContrato")
    public String renovarContrato(@WebParam(name = "key") final String key,
                                  @WebParam(name = "cliente") int cliente,
                                  @WebParam(name = "contrato") int contrato,
                                  @WebParam(name = "simulacao") boolean simulacao,
                                  @WebParam(name = "origemSistema") Integer origemSistema) {
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        } catch (Exception ex) {
            return tratarException(ex);
        }

        UsuarioVO usuarioAdmin;
        try {
            usuarioAdmin = acessoControle.getUsuarioDao().consultarPorNomeUsuario("admin", false, Uteis.NIVELMONTARDADOS_MINIMOS);
        } catch (Exception ex) {
            return tratarException(ex);
        }

        try {
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            ContratoVO contratoVO = acessoControle.getContratoDao().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_TODOS);

            if (contratoVO == null) {
                throw new Exception("Contrato não encontrado.");
            }
            if (contratoVO.getDataRenovarRealizada() != null) {
                throw new Exception("Contrato já foi renovado.");
            }
            if (Calendario.maior(Calendario.hoje(), contratoVO.getPlano().getIngressoAte())) {
                throw new Exception("Data limite para ingressar neste plano foi atingida, verifique!");
            }
            if (contratoVO.getPlano().getBloquearRecompra()) {
                throw new Exception("O plano " + contratoVO.getPlano().getDescricao() + " não permite renovação.");
            }

            ContratoWS contratoWS;
            JSONObject contratoJSON;

            if(!contratoVO.getPlano().getRecorrencia()) {
                RenovacaoAutomaticaService renovacaoAutomatica = new RenovacaoAutomaticaService(acessoControle.getCon());
                contratoVO.setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(origemSistema));
                ContratoVO contratoNovo = renovacaoAutomatica.renovarAutomatico(contratoVO, simulacao);
                contratoWS = contratoNovo.toWS(false);
            } else {
                ContratoRecorrenciaVO contratoRecorrenciaVO = acessoControle.getContratoRecorrenciaDao().consultarPorContrato(contrato, Uteis.NIVELMONTARDADOS_TODOS);
                RenovacaoAutomaticaRecorrenciaService renovacaoAutomaticaRecorrenciaService = new RenovacaoAutomaticaRecorrenciaService(acessoControle.getCon());
                ContratoRecorrenciaVO contratoNovo = renovacaoAutomaticaRecorrenciaService.renovarAutomatico(contratoRecorrenciaVO, simulacao, origemSistema);

                if(contratoNovo == null) {
                    throw new Exception("Contrato Recorrencia sem Indice Financeiro.");
                }

                contratoWS = contratoNovo.getContrato().toWS(false);
                contratoWS.setSimulacao(simulacao);
            }

            contratoJSON = new JSONObject(contratoWS);
            return contratoJSON.toString();
        } catch (Exception ex) {
            if (!simulacao) {
                acessoControle.getLogDao().incluirLogErroInclusaoContrato(cliente, usuarioAdmin, ex.getMessage());
            }
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "obterBandeirasConvenio")
    public String obterBandeirasConvenio(@WebParam(name = "key") final String key,
                                         @WebParam(name = "matricula") String matricula) {
        AcessoControle acessoControle = null;
        try {
            JSONObject jsonRetorno = new JSONObject();
            List<String> listaBandeiras = new ArrayList<String>();

            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            ClienteVO clienteVO = acessoControle.getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);

            List<TotemTO> totem = acessoControle.getConfiguracaoTotemDao().
                    obterConfigs(clienteVO.getEmpresa().getCodigo(), key);

            Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO> mapa = totem.get(0).getConfigs();

            //REGRAS DEFINIDAS PARA O APP DO ALUNO ---- LUIZ FELIPE
            Integer codConvenioDCO = mapa.get(ConfigTotemEnum.CONVENIO_COBRANCA_DCO).getValorAsInt();
            if (!UteisValidacao.emptyNumber(codConvenioDCO)) {
                ConvenioCobrancaVO convenioCobrancaVODCO = acessoControle.getConvenioCobrancaDao().consultarPorCodigoSemInfoEmpresa(codConvenioDCO, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                jsonRetorno.put("bancoDCO", convenioCobrancaVODCO.getBanco().getCodigoBanco().toString());
            } else {
                jsonRetorno.put("bancoDCO", "");
            }

            Integer codConvenio = mapa.get(ConfigTotemEnum.CONVENIO_COBRANCA).getValorAsInt();
            if (!UteisValidacao.emptyNumber(codConvenio)) {
                ConvenioCobrancaVO convenioCobrancaVO = acessoControle.getConvenioCobrancaDao().consultarPorCodigoSemInfoEmpresa(codConvenio, Uteis.NIVELMONTARDADOS_DADOSBASICOS);


                List<OperadorasExternasAprovaFacilEnum> vet = OperadorasExternasAprovaFacilEnum.operadorasConvenio(convenioCobrancaVO.getTipo());
                List<OperadoraCartaoVO> lista = acessoControle.getOperadoraCartaoDao().consultarPorCodigo(0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                for (OperadoraCartaoVO operadoraCartaoVO : lista) {
                    for (OperadorasExternasAprovaFacilEnum operadora : vet) {
                        if ((operadora == operadoraCartaoVO.getCodigoIntegracaoAPF()
                                && (convenioCobrancaVO.getTipo() == null ||
                                (!convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)
                                        && !convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)
                                        && !convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO)
                                        && !convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE))))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoVindi()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoCielo()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoMaxiPago()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoERede()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoFitnessCard()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FITNESS_CARD))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoGetNet()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoStoneOnline()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoStoneOnlineV5()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoMundiPagg()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoPagarMe()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoPagBank()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK))
                                ||
                                (operadora == operadoraCartaoVO.getCodigoIntegracaoPagarMe()
                                        && convenioCobrancaVO.getTipo() != null
                                        && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE))) {
                            if (!listaBandeiras.contains(operadora.getDescricao())) {
                                listaBandeiras.add(operadora.getDescricao());
                                break;
                            }
                        }
                    }
                }
            }
            jsonRetorno.put("bandeirasDCC", listaBandeiras);
            return jsonRetorno.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();

        }
    }

    @WebMethod(operationName = "consultarPlanosTotem")
    public String consultarPlanosTotem(@WebParam(name = "key") String key,
                                       @WebParam(name = "empresa") int empresa) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            List<PlanoVO> list = acessoControle.getPlanoDao().consultarPorDescricaoDataIngressoSite(
                        "", negocio.comuns.utilitarias.Calendario.hoje(), empresa, false, false, true,
                    0, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            JSONArray planos = new JSONArray();
            for (PlanoVO plano : list) {
                if (plano.getRegimeRecorrencia() && null != plano.getPlanoRecorrencia() && plano.getPlanoRecorrencia().getCodigo() > 0) {
                    PlanoWS planoWS = obterPlanoWSValidandoEmpresa(empresa, plano, acessoControle.getCon());
                    JSONObject planoObj = new JSONObject(planoWS);
                    planos.put(planoObj);
                }
            }
            return planos.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    private PlanoWS obterPlanoWSValidandoEmpresa(int empresa, PlanoVO plano, Connection con) throws Exception {
        PlanoEmpresaVO planoEmpresaVO = plano.obterPlanoEmpresa(empresa);
        PlanoWS planoWs = plano.toWS(planoEmpresaVO, con);
        planoWs.setNrVezesParcelarMatricula(plano.getNrVezesParcelarAdesao());
        return planoWs;
    }

    @WebMethod(operationName = "simularContrato")
    public String simularContrato(@WebParam(name = "key") final String key,
                                  @WebParam(name = "plano") int plano,
                                  @WebParam(name = "cliente") int cliente,
                                  @WebParam(name = "empresa") int empresa,
                                  @WebParam(name = "nrParcelasAdesao") int nrParcelasAdesao,
                                  @WebParam(name = "nrParcelasProduto") int nrParcelasProduto,
                                  @WebParam(name = "numeroCupomDesconto") String numeroCupomDesconto,
                                  @WebParam(name = "nrParcelasPagamento") int nrParcelasPagamento,
                                  @WebParam(name = "contratoRenovar") int contratoRenovar) {
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        } catch (Exception ex) {
            return tratarException(ex);
        }

        UsuarioVO usuarioAdmin;
        try {
            usuarioAdmin = acessoControle.getUsuarioDao().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
        } catch (Exception ex) {
            return tratarException(ex);
        }

        try {
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            ContratoVO contratoVO = acessoControle.getContratoDao().simularContratoSite(key, plano, cliente, empresa, nrParcelasAdesao, nrParcelasProduto, numeroCupomDesconto,
                    true,true, nrParcelasPagamento, contratoRenovar);
            ContratoWS contratoWS = contratoVO.toWS(false);
            contratoWS.setVigenciaDe(Uteis.getData(contratoVO.getVigenciaDe()));
            contratoWS.setVigenciaAteAjustada(Uteis.getData(contratoVO.getVigenciaAteAjustada()));
            contratoWS.setMaxVezesParcelarAdesao(contratoVO.getPlano().getNrVezesParcelarAdesao());
            contratoWS.setMaxVezesParcelarProduto(contratoVO.getPlano().getNrVezesParcelarProduto());

            JSONObject contrato = new JSONObject(contratoWS);
            return contrato.toString();
        } catch (Exception ex) {
            acessoControle.getLogDao().incluirLogErroInclusaoContrato(cliente, usuarioAdmin, ex.getMessage());
            return tratarException(ex);
        }
    }

    @WebMethod(operationName = "renovarContratoOutroPlano")
    public String renovarContratoOutroPlano(@WebParam(name = "key") final String key,
                                 @WebParam(name = "plano") int plano,
                                 @WebParam(name = "cliente") int cliente,
                                 @WebParam(name = "nrParcelasAdesao") int nrParcelasAdesao,
                                 @WebParam(name = "nrParcelasProduto") int nrParcelasProduto,
                                 @WebParam(name = "numeroCupomDesconto") String numeroCupomDesconto,
                                 @WebParam(name = "nrParcelasPagamento") int nrParcelasPagamento,
                                 @WebParam(name = "contratoRenovar") int contratoRenovar) {
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        } catch (Exception ex) {
            return tratarException(ex);
        }

        UsuarioVO usuarioAdmin;
        try {
            usuarioAdmin = acessoControle.getUsuarioDao().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
        } catch (Exception ex) {
            return tratarException(ex);
        }

        try {
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            ContratoVO contratoVO = acessoControle.getContratoDao().gravarContratoSite(key, plano, cliente, nrParcelasAdesao, nrParcelasProduto,
                    numeroCupomDesconto,true,true, nrParcelasPagamento, contratoRenovar);
            ContratoWS contratoWS = contratoVO.toWS(false);
            JSONObject contrato = new JSONObject(contratoWS);
            return contrato.toString();
        } catch (Exception ex) {
            acessoControle.getLogDao().incluirLogErroInclusaoContrato(cliente, usuarioAdmin, ex.getMessage());
            return tratarException(ex);
        }
    }

    @WebMethod(operationName = "alterarVencimentoParcelas")
    public String alterarVencimentoParcelas(@WebParam(name = "key") final String key,
                                         @WebParam(name = "dataSegunda") final String dataSegunda,
                                         @WebParam(name = "contrato") final Integer contrato) {
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        } catch (Exception ex) {
            return tratarException(ex);
        }
        try {
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            if(UteisValidacao.emptyString(dataSegunda)){
                throw new Exception("Data não informada");
            }
            Date dia = Uteis.getDate(dataSegunda);
            List<MovParcelaVO> movParcelaVOSOriginais = acessoControle.getMovParcelaDao().consultarParcelasContratoAlterarData(contrato);
            List<MovParcelaVO> movParcelaVOS = new ArrayList<MovParcelaVO>(movParcelaVOSOriginais);
            for (MovParcelaVO m : movParcelaVOS) {
                if(m.getDescricao().startsWith("PARCELA")){
                    m.setDataVencimento(dia);
                    dia = Uteis.obterDataFutura3(dia, 1);
                }else if(m.getDescricao().startsWith("ADES")){
                    m.setDataVencimento(dia);
                }
            }
            acessoControle.getContratoRecorrenciaDao().alterarDiaVencimento(Uteis.getDiaMesData(dia), contrato, "RECORRÊNCIA");
            acessoControle.getMovParcelaDao().alterarVencimentoListaParcelas(movParcelaVOS, movParcelaVOSOriginais, false, "RECORRÊNCIA", "WebService - Vendas Online", false);
            return "sucesso";
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
            return tratarException(e);
        }
    }


    @WebMethod(operationName = "obterContaMaisRecenteAluno")
    public String obterContaMaisRecenteAluno(@WebParam(name = "key") final String key, @WebParam(name = "cliente") int cliente){
        String retorno = "";
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            JSONObject cartao = acessoControle.getAutorizacaoCobrancaDao().obterUltimoCartaoAtivoAluno(cliente, TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
            retorno = cartao.toString();
        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }

        return retorno;
    }

    @WebMethod(operationName = "realizarCobrancaOnlineParcela")
    public String realizarCobrancaOnlineParcela(@WebParam(name = "key") final String key,
                                                @WebParam(name = "convenio") int convenio,
                                                @WebParam(name = "movParcela") int movParcela,
                                                @WebParam(name = "nrParcelas") int nrParcelas) {

        String retorno = "";
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            MovParcelaVO movParcelaVO = acessoControle.getMovParcelaDao().consultarPorChavePrimaria(movParcela, Uteis.NIVELMONTARDADOS_MINIMOS);
            ClienteVO clienteVO = acessoControle.getClienteDao().consultarPorCodigoPessoa(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            if (UteisValidacao.emptyNumber(movParcelaVO.getNumeroParcelasOperadora())) {
                //Há um tratamento para o 0, não utilizando alguns parâmetros da requisição
                nrParcelas = 0;
            }

            ConvenioCobrancaVO convenioCobrancaVO = acessoControle.getConvenioCobrancaDao().consultarPorCodigoEmpresa(convenio, movParcelaVO.getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
            if (!convenioCobrancaVO.getTipo().isTransacaoOnline()) {
                throw new Exception("Convênio não é de Transação Online");
            }

            UsuarioVO usuarioVO = acessoControle.getUsuarioDao().getUsuarioRecorrencia();
            PagamentoService pagamentoService = new PagamentoService(acessoControle.getCon(), convenioCobrancaVO);
            TransacaoVO transacaoVO = pagamentoService.realizarCobrancaWS(movParcelaVO, usuarioVO, nrParcelas);
            if (transacaoVO == null) {
                throw new Exception("Erro ao processar pagamento.");
            } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                retorno = "Sucesso.";
            } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                retorno = "Aguardando captura.";
            } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                retorno = transacaoVO.getSituacao().getHint();
            } else {
                retorno = transacaoVO.getResultadoRequisicao();
            }

            ThreadNotificacaoTransacao threadNotificacaoTransacao = new ThreadNotificacaoTransacao(movParcelaVO, transacaoVO, clienteVO, key, acessoControle.getCon());
            threadNotificacaoTransacao.run();
        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }
        return retorno;
    }

    @WebMethod(operationName = "obterPrimeirasParcelasContrato")
    public String obterPrimeirasParcelasContrato(@WebParam(name = "key") final String key,
                                                 @WebParam(name = "contrato") int contrato) {
        String retorno;
        AcessoControle acessoControle;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            org.json.JSONArray arrayParcelas = new org.json.JSONArray();

            List<MovParcelaVO> movParcelas = acessoControle.getMovParcelaDao().consultarPrimeirasParcelasContrato(contrato, false);
            for (MovParcelaVO movParcelaVO : movParcelas) {
                org.json.JSONObject parcela = new org.json.JSONObject();
                parcela.put("codigo", movParcelaVO.getCodigo());
                parcela.put("descricao", movParcelaVO.getDescricao());
                arrayParcelas.put(parcela);
            }
            retorno = arrayParcelas.toString();
        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }
        return retorno;
    }

    @WebMethod(operationName = "enviarEmailConfirmacaoCompra")
    public String enviarEmailConfirmacaoCompra(@WebParam(name = "key") final String key,
                                               @WebParam(name = "cliente") int cliente,
                                               @WebParam(name = "plano") int plano,
                                               @WebParam(name = "nrParcelasAdesao") int nrParcelasAdesao,
                                               @WebParam(name = "numeroCupomDesconto") final String numeroCupomDesconto,
                                               @WebParam(name = "nrParcelasPagamento") int nrParcelasPagamento,
                                               @WebParam(name = "diaVencimento") int diaVencimento) {
        AcessoControle acessoControle = null;
        JSONObject result = new JSONObject();
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            ClienteVO clienteVO = acessoControle.getClienteDao().consultarPorCodigo(cliente,false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            String email = clienteVO.getPessoa().getEmail();

            Date data = Calendario.getDateInTimeZone(Calendario.hoje(), acessoControle.getEmpresaDao().consultarPorChavePrimaria(clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).getTimeZoneDefault());
            String tokenEmail = Criptografia.encrypt((clienteVO.getCodigo() + "|" + data.getTime()), SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM);

            acessoControle.getConfirmacaoEmailCompra().incluirCadastroEmailConfirmacaoCompra(clienteVO, tokenEmail, nrParcelasAdesao, numeroCupomDesconto, nrParcelasPagamento, diaVencimento, plano);
            ModeloMensagemVO msg = acessoControle.getModeloMensagemDao().consultarPorTipo("EC", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PlanoVO planoDao = acessoControle.getPlanoDao().consultarPorChavePrimaria(plano, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            result.put("codigo", clienteVO.getCodigo());
            result.put("email", email);
            StringBuilder st = new StringBuilder();
            tokenEmail = tokenEmail + "&key=" + key;


            String url = Uteis.obterRequestURL(context)+"/confirmacaoEmailCompra?token="+tokenEmail;

            String titulo = msg.getTitulo();
            st.append(msg.getMensagem());
            String corpoEmail = st.toString().replace("LINK_URL", url).replace("NR_PARCELAS", String.valueOf(nrParcelasPagamento)).replace("PLANO", planoDao.getDescricao()).replace("NOME_CLIENTE", clienteVO.getPessoa().getNome());


            acessoControle.enviarEmail(new String[]{email}, titulo, new StringBuilder(corpoEmail));
            result.put("mensagem","Enviado com Sucesso!!!");


        } catch (Exception e) {
            return tratarException(e);
        }
        return result.toString();
    }

    @WebMethod(operationName = "validarCupomDesconto")
    public String validarCupomDesconto(@WebParam(name = "key") final String key,
                                       @WebParam(name = "numeroCupomDesconto") String numeroCupomDesconto,
                                       @WebParam(name = "descricaoPlano") String descricaoPlano) {
        try {
            OAMDService oamdService = new OAMDService();

            Integer codFavorecido = 0;

            try {
                AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
               if(numeroCupomDesconto.contains("|")){
                   codFavorecido = Integer.parseInt(numeroCupomDesconto.split("\\|")[1]);
                   numeroCupomDesconto = numeroCupomDesconto.split("\\|")[0];
               }
                List<EmpresaVO> empresas = acessoControle.getEmpresaDao().consultarTodas(true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!empresas.isEmpty()) {
                    if(codFavorecido == 0) codFavorecido = empresas.get(0).getCodEmpresaFinanceiro();
                }
            } catch (Exception ex) {
                Uteis.logar(ex, this.getClass());
            }

            CupomDescontoVO cupomDescontoVO = oamdService.validarCupomPortadorCupom(codFavorecido, numeroCupomDesconto, descricaoPlano);
            oamdService = null;
            return cupomDescontoVO.getMsgValidacao();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod(operationName = "adicionarCupomDescontoSite")
    public String adicionarCupomDescontoSite(@WebParam(name = "key") final String key,
                                       @WebParam(name = "numeroCupomDesconto") String numeroCupomDesconto,
                                       @WebParam(name = "descricaoPlano") String descricaoPlano) {
        try {
            OAMDService oamdService = new OAMDService();

            int codEmpresa = Integer.valueOf(descricaoPlano.split(";empresa=")[1]);
            descricaoPlano = descricaoPlano.split(";empresa=")[0];

            EmpresaVO empresa = null;
            try {
                AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                empresa = acessoControle.getEmpresaDao().consultarPorCodigo(codEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } catch (Exception ex) {
                Uteis.logar(ex, this.getClass());
            }

            CupomDescontoVO cupomDescontoVO = oamdService.validarCupomPortadorCupom(empresa.getCodEmpresaFinanceiro(), numeroCupomDesconto, descricaoPlano);
            if((cupomDescontoVO != null || cupomDescontoVO.getCodigo() != 0) && UteisValidacao.emptyString(cupomDescontoVO.getMsgValidacao())) {
                CampanhaCupomDescontoVO campanha = oamdService.consultarCampanhaCupomDescontoPorId(cupomDescontoVO.getCampanhaCupomDescontoVO().getId(), Uteis.NIVELMONTARDADOS_TODOS);
                if(campanha != null || campanha.getId() != 0){
                    return oamdService.montarCampanhaCupomDescontoSite(numeroCupomDesconto, campanha.getListaPremioPortador());
                }
            }else{
                throw new Exception(cupomDescontoVO != null && !UteisValidacao.emptyString(cupomDescontoVO.getMsgValidacao()) ? cupomDescontoVO.getMsgValidacao() : "Cupom desconto inválido!");
            }
        } catch (Exception e) {
            return "ERRO: "+e.getMessage();
        }

        return "Cupom desconto não encontrado.";
    }

    @WebMethod(operationName = "gravarAlunoVendaOnline")
    public String gravarAlunoVendaOnline(@WebParam(name = "key") final String key,
                                       @WebParam(name = "dados") String dados) {
        VendaDTO venda =  new VendaDTO(dados);
        try (Connection connection = new DAO().obterConexaoEspecifica(key)) {
            VendasOnlineService service = new VendasOnlineService(key, connection);
            return service.incluirAlunoEVendaOnline(key,venda, connection);
        } catch (Exception e) {
            if(!UteisValidacao.emptyString(e.getMessage()) && e.getMessage().contains("ERRO")) {
                return e.getMessage();
            }else{
                return "ERRO: " + e.getMessage();
            }
        }
    }

    @WebMethod(operationName = "gravarAlunoVendaConsultor")
    public RetornoVendaDTO gravarAlunoVendaApp(@WebParam(name = "key") final String key,
                                                  @WebParam(name = "dados") String dados) {
        VendaDTO venda =  new VendaDTO(dados);
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            VendasOnlineService service = new VendasOnlineService(key, acessoControle.getCon());
            return service.incluirAlunoVendaApp(key,venda);
        } catch (Exception e) {
            return new RetornoVendaDTO(e.getMessage());
        }
    }

    @WebMethod(operationName = "gravarCadastroAlunoVendaOnline")
    public String gravarCadastroAlunoVendaOnline(@WebParam(name = "key") final String key,
                                         @WebParam(name = "dados") String dados) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            VendasOnlineService service = new VendasOnlineService(key, acessoControle.getCon());
            return service.incluirAlunoOnline(key, new VendaDTO(dados), false);
        } catch (Exception e) {
            return "ERRO: "+e.getMessage();
        }
    }

    @WebMethod(operationName = "simularContratoVendaOnlineV2")
    public String simularContratoVendaOnlineV2(@WebParam(name = "key") final String key,
                                             @WebParam(name = "dados") String dados,
                                             @WebParam(name = "unidade") Integer unidade) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            VendasOnlineService service = new VendasOnlineService(key, acessoControle.getCon());
            ContratoVO contratoVO = service.simular(key, new VendaDTO(dados), unidade);
            try {
                contratoVO.setVendasConfigVO(new VendasConfigVO());
                contratoVO.getVendasConfigVO().setCobrarPrimeiraParcelaCompra(service.obterConfigPrimeiraParcela(unidade));
            }catch (Exception e) {}
            ContratoWS contratoWS = contratoVO.toWS(false);
            JSONObject contrato = new JSONObject(contratoWS);
            return contrato.toString();
        } catch (Exception e) {
            return "ERRO: "+e.getMessage();
        }
    }

    @WebMethod(operationName = "permitirRenovacaoContrato")
    public String permitirRenovacaoContrato(@WebParam(name = "key") final String key,
                                               @WebParam(name = "unidade") Integer unidade) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            VendasOnlineService service = new VendasOnlineService(key, acessoControle.getCon());
            JSONObject contrato = new JSONObject(service.obterConfigPrimeiraParcelaRenovacao(unidade));
            return contrato.toString();
        } catch (Exception e) {
            return "ERRO: "+e.getMessage();
        }
    }

    @WebMethod(operationName = "simularContratoVendaOnline")
    public String simularContratoVendaOnline(@WebParam(name = "key") final String key,
                                             @WebParam(name = "plano") Integer plano,
                                             @WebParam(name = "unidade") Integer unidade) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            VendasOnlineService service = new VendasOnlineService(key, acessoControle.getCon());
            ContratoVO contratoVO = service.simular(key, plano, unidade);
            ContratoWS contratoWS = contratoVO.toWS(false);
            JSONObject contrato = new JSONObject(contratoWS);
            return contrato.toString();
        } catch (Exception e) {
            return "ERRO: "+e.getMessage();
        }
    }

    @WebMethod(operationName = "realizarCobrancaParcelasAberto")
    public String realizarCobrancaParcelasAberto(@WebParam(name = "key") final String key,
                                                 @WebParam(name = "matricula") String matricula,
                                                 @WebParam(name = "dados") String dados) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            VendasOnlineService service = new VendasOnlineService(key, acessoControle.getCon());
            JSONObject jsonRetorno = service.cobrarParcelasAbertas(matricula, new VendaDTO(dados));
            return jsonRetorno == null ? "ERRO" : jsonRetorno.toString();
        } catch (Exception e) {
            return "ERRO: "+e.getMessage();
        }
    }

    @WebMethod(operationName = "urlRedirectVendaConcluida")
    public String urlRedirectVendaConcluida(@WebParam(name = "key") final String key,
                                       @WebParam(name = "empresa") Integer empresa) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            VendasOnlineService service = new VendasOnlineService(key, acessoControle.getCon());
            JSONObject configsVendas = service.obterConfigsVendas(empresa);
//            String modulos = PropsService.getPropertyValue(key, PropsService.modulos);
//            if (!modulos.contains("NVO")) {
//                throw new Exception("Módulo Vendas Online não está ativo!");
//            }
            return configsVendas.toString();
        } catch (Exception e) {
            JSONObject jsonErro = new JSONObject();
            jsonErro.put("erro", e.getMessage());
            return jsonErro.toString();
        }
    }

    @WebMethod(operationName = "imagensVendasOnline")
    public String imagensAcademiaVendas(@WebParam(name = "key") final String key,
                                       @WebParam(name = "empresa") Integer empresa) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            VendasOnlineService service = new VendasOnlineService(key, acessoControle.getCon());
            JSONArray imagens = service.obterImagens(empresa);
            return imagens.toString();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod(operationName = "excluirAlunoTesteVendas")
    public String excluirAlunoTesteVendas(@WebParam(name = "key") final String key) {
        try {
            ClienteService clienteService = new ClienteService();
            clienteService.excluirClienteVendas(PropsService.getPropertyValue(PropsService.cpftestevendasonline), DaoAuxiliar.retornarAcessoControle(key).getCon());
            JSONObject json = new JSONObject();
            json.put("resultado", "sucesso");
            return json.toString();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod(operationName = "testeValidarValorASerCobradoClienteParcela1")
    public String testeValidarValorASerCobradoClienteParcela1(@WebParam(name = "key") final String key,
                                                              @WebParam(name = "valor") double valor,
                                                              @WebParam(name = "codigoPlano") Integer codigoPlano) {
        try {
            boolean valorBate = false;
            ClienteService clienteService = new ClienteService();
            valorBate = clienteService.validarValorParcelaClienteVendas(PropsService.getPropertyValue(PropsService.cpftestevendasonline), valor, codigoPlano,  DaoAuxiliar.retornarAcessoControle(key).getCon());
            return valorBate ? "sucess" : "fail";
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod(operationName = "imprimirContratoEmbranco")
    public String imprimirContratoEmbranco(@WebParam(name = "key") final String key,
                                           @WebParam(name = "empresa") Integer empresa,
                                      @WebParam(name = "codigoPlano") Integer codigoPlano) {
        String retorno = "";
        AcessoControle acessoControle;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());

            if (codigoPlano != null && codigoPlano > 0) {
                PlanoVO planoVO = acessoControle.getPlanoDao().consultarPorChavePrimaria(codigoPlano, Uteis.NIVELMONTARDADOS_TODOS);
                if (planoVO.getPlanoTextoPadrao().getCodigo() > 0){
                    PlanoTextoPadraoVO planoTextoPadraoVO = acessoControle.getPlanoTextoPadraoDao().consultarPorChavePrimaria(planoVO.getPlanoTextoPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    retorno = planoTextoPadraoVO.contratoEmBranco(empresa, Conexao.getFromSession(), planoVO);
                }
            }

        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }

        return retorno;
    }

    private boolean cobrarPrimeiraParcelaNaCompra(Boolean cobrarPrimeiraParcelaCompra, PlanoVO plano) {
        boolean retorno = false;
        try {
            /*O Vendas Online não cobra a primeira parcela no ato da venda se for uma parcela futura com a configuração do "Cobrar Primeira Parcela Compra" desmarcada no Gestão do Vendas Online
            Por isso, ele precisa ir nas Condições de Pagamento do plano para descobrir se Entrada está configurado.*/
            if(cobrarPrimeiraParcelaCompra){
                retorno = true;
            } else if (!cobrarPrimeiraParcelaCompra && !UteisValidacao.emptyList(plano.getPlanoDuracaoVOs())
                    && !UteisValidacao.emptyList(plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs())
                    && plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getEntrada() != null
                    && plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getEntrada()) {
                retorno = true;
            }
        } catch (Exception e) {

        }
        return retorno;
    }

}
