package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteGrupoVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.ContratoComposicaoVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ConvenioDescontoConfiguracaoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.FormatadorNumerico;
import negocio.comuns.utilitarias.NumeroPorExtenso;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteGrupo;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.ContratoAssinaturaDigital;
import negocio.facade.jdbc.contrato.PlanoPersonalAssinaturaDigital;
import negocio.facade.jdbc.contrato.ProdutoAssinaturaDigital;
import negocio.facade.jdbc.contrato.ProdutoTextoPadrao;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.plano.Plano;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.vendasonline.dto.NowLocationIpVendaDTO;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Reponsável por manter os dados da entidade PlanoTextoPadrao. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class PlanoTextoPadraoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected String situacao;
    protected String tipoContrato = "";
    protected Date dataDefinicao;
    @NaoControlarLogAlteracao
    protected String texto;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    @NaoControlarLogAlteracao
    @FKJson
    protected UsuarioVO responsavelDefinicao;
    @NaoControlarLogAlteracao
    protected List listaTagUtilizado;
    @NaoControlarLogAlteracao
    protected String tagGeral;
    @NaoControlarLogAlteracao
    private transient byte imagemLogo[];

    /**
     * Construtor padrão da classe <code>PlanoTextoPadrao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public PlanoTextoPadraoVO() {
        super();
        inicializarDados();
    }

    public String getResponsavel_Apresentar() {
        return getResponsavelDefinicao().getNome();
    }

    public static void validarDados(PlanoTextoPadraoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (UteisValidacao.emptyString(obj.getDescricao())) {
            throw new ConsistirException("O campo DESCRIÇÃO (Plano Texto Padrao) deve ser informado.");
        }
        if (UteisValidacao.emptyString(obj.getTipoContrato())) {
            throw new ConsistirException("O campo Tipo Contrato (Plano Texto Padrao) deve ser informado.");
        }
        if (UteisValidacao.emptyString(obj.getSituacao())){
            throw new ConsistirException("O campo SITUAÇÃO (Plano Texto Padrao) deve ser informado.");
        }
        if (obj.getDataDefinicao() == null) {
            throw new ConsistirException("O campo DATA DEFINIÇÃO (Plano Texto Padrao) deve ser informado.");
        }
        if ((obj.getResponsavelDefinicao() == null)
                || (obj.getResponsavelDefinicao().getCodigo() == 0)) {
            throw new ConsistirException("O campo RESPONSÁVEL DEFINIÇÃO (Plano Texto Padrao) deve ser informado.");
        }
        if (obj.getTexto().equals("")) {
            throw new ConsistirException("O campo TEXTO (Plano Texto Padrao) deve ser informado.");
        }
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    public void inicializarDados() {
    }

    protected FacesContext context() {
        return (FacesContext.getCurrentInstance());
    }

    public String substituirTagsTextoEnvio(String key, ContratoVO contrato, ClienteVO cliente, List<MovPagamentoVO> pagamentos, Connection con, String descMoedaEmpresa) throws Exception {
        String htmlBotao = "<form> ";
        String textoSub = getTexto();
        if (UteisValidacao.emptyString(textoSub)) {
            throw new Exception("Não há modelo de contrato para este plano");
        }
        textoSub = textoSub.replace("Untitled document", "CONTRATO DE PRESTA&Ccedil;&Atilde;O DE SERVI&Ccedil;OS");
        int posicaoBody = textoSub.indexOf("<body>");
        String parte1 = textoSub.substring(0, posicaoBody + 6);
        String parte2 = textoSub.substring(posicaoBody + 6);
        textoSub = parte1 + htmlBotao + parte2;
        posicaoBody = textoSub.indexOf("</body>");
        textoSub = varrerListaTagUtilizadoTextoPadrao(key, textoSub, cliente, contrato, pagamentos, con, descMoedaEmpresa);
        textoSub = preencherAssinaturaDigital(textoSub, contrato.getCodigo(), con);
        return textoSub;
    }

    public String preencherAssinaturaDigital(String textoSub, Integer contrato, Connection con) throws Exception {
        if(!textoSub.contains("idassinatur4digit4l")){
            ContratoAssinaturaDigital coass = new ContratoAssinaturaDigital(con);
            String assinatura = coass.obterPorContrato(contrato, false);
            coass = null;
            if (!UteisValidacao.emptyString(assinatura)) {
                textoSub = addAssinatura(textoSub, Uteis.getPaintFotoDaNuvem(assinatura));
            }
        }
        if(!textoSub.contains("idassinatur4digit4l2")){
            ContratoAssinaturaDigital coass = new ContratoAssinaturaDigital(con);
            String assinatura = coass.obterPorContrato(contrato, true);
            if (!UteisValidacao.emptyString(assinatura)) {
                textoSub = addAssinatura2(textoSub, Uteis.getPaintFotoDaNuvem(assinatura));
            }
        }

        return textoSub;
    }

    public String substituirTagsTextoEnvioPlanoPersonal(String key, ControleTaxaPersonalVO taxaPersonal, ColaboradorVO colaborador, Connection con, String descMoedaEmpresa) throws Exception {
        String htmlBotao = "<form> ";
        String textoSub = getTexto();
        textoSub = textoSub.replace("Untitled document", "CONTRATO DE PRESTA&Ccedil;&Atilde;O DE SERVI&Ccedil;OS");
        int posicaoBody = textoSub.indexOf("<body>");
        String parte1 = textoSub.substring(0, posicaoBody + 6);
        String parte2 = textoSub.substring(posicaoBody + 6);
        textoSub = parte1 + htmlBotao + parte2;
        posicaoBody = textoSub.indexOf("</body>");
        textoSub = varrerListaTagUtilizadoTextoPadraoPlanoPersonal(key, textoSub, colaborador, taxaPersonal, con, descMoedaEmpresa);
        if(!textoSub.contains("idassinatur4digit4l")){
            PlanoPersonalAssinaturaDigital coass = new PlanoPersonalAssinaturaDigital(con);
            String assinatura = coass.obterPorPlano(taxaPersonal.getCodigo());
            if (UteisValidacao.emptyString(assinatura)) {
                textoSub = textoSub.replaceAll("__4ssin4tur4digit4l__", " ");
            } else {
                textoSub = addAssinatura(textoSub, Uteis.getPaintFotoDaNuvem(assinatura));
            }
        }

        return textoSub;
    }

    public String substituirTagsTextoPadraoPlanoPersonal (String key, ColaboradorVO colaborador, ControleTaxaPersonalVO taxaPersonal, Connection con, String descMoedaEmpresa) throws Exception{
        return substituirTagsTextoPadraoPlanoPersonal(key, colaborador, taxaPersonal, true, con, descMoedaEmpresa);
    }

    public String substituirTagsTextoPadraoPlanoPersonal(String key, ColaboradorVO colaborador, ControleTaxaPersonalVO taxaPersonal, boolean temRequest, Connection con, String descMoedaEmpresa) throws Exception {
        String textoContrato = "";
        if (taxaPersonal.getPlanoPersonalTextoPadrao().getPlanoTextoPadrao().getTexto().length() != 0) {
            char aspas = '"';
            String espaco = "<p style=" + aspas + "text-align: left;" + aspas + ">";
            String htmlBotao = "<form> ";
            String htmlBotao2 = "</div><form> <input id=" + aspas + "imprimir" + aspas + "type=" + aspas + "image" + aspas + " src=" + aspas + "./imagens/imprimirContrato.png" + aspas + " name=" + aspas + "imprimir" + aspas + " alt=" + aspas + "Imprimir Contrato" + aspas + "onclick=" + aspas + "window.print();" + aspas + "/>";
            String textoSub = taxaPersonal.getPlanoPersonalTextoPadrao().getPlanoTextoPadrao().getTexto();
            textoSub = textoSub.replace("Untitled document", "CONTRATO DE PRESTA&Ccedil;&Atilde;O DE SERVI&Ccedil;OS");
            int posicaoBody = textoSub.indexOf("<body>");
            String parte1 = textoSub.substring(0, posicaoBody + 6);
            String parte2 = textoSub.substring(posicaoBody + 6);
            parte1 = parte1.replace("</head>", "<style>@media print {#imprimir {display: none;}}</style></head>");
            parte2 = "<div id="+aspas+"divimpressao"+aspas+">"+parte2;
            textoSub = parte1 + htmlBotao + parte2;

            posicaoBody = textoSub.indexOf("</body>");
            parte1 = textoSub.substring(0, posicaoBody);
            parte2 = textoSub.substring(posicaoBody);
            textoSub = parte1 + htmlBotao2 + espaco + "</form>" + parte2;
            StringBuilder textoSubBuilder = new StringBuilder();
            textoSubBuilder.append(textoSub);
            //replaceLogomarca(textoSubBuilder);
            textoSub = textoSubBuilder.toString();
            textoSub = varrerListaTagUtilizadoTextoPadraoPlanoPersonal(key, textoSub, colaborador, taxaPersonal, con, descMoedaEmpresa);
            textoContrato = textoSub;
            if(!textoSub.contains("idassinatur4digit4l")){
                PlanoPersonalAssinaturaDigital coass = new PlanoPersonalAssinaturaDigital(con);
                String assinatura = coass.obterPorPlano(taxaPersonal.getCodigo());
                if(!UteisValidacao.emptyString(assinatura)){
                    textoSub = addAssinatura(textoSub, Uteis.getPaintFotoDaNuvem(assinatura));
                }
            }

            if (temRequest) {
                HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
                request.getSession().setAttribute("textoRelatorio", textoSub);
            }
        }
        return textoContrato;
    }

    public static String addAssinatura(String texto, String urlAssinatura){
            String divAssinatura = "<div id=\"idassinatur4digit4l\" style=\"width: 20%; text-align: center; margin: 0 auto;\">"
                + (texto.contains("__4ssin4tur4digit4l__") ? "" : "Assinatura digital: ")
                + "<img style=\"width: 100%;\" src=\""
                .concat(urlAssinatura).concat("\"/></div>");
        if(texto.contains("__4ssin4tur4digit4l__")){
            return texto.replace("__4ssin4tur4digit4l__", divAssinatura);
        }else{
            int iIni = texto.indexOf("idassinatur4digit4l");
            if(iIni > 0) {
                int iFinal = texto.indexOf("</div>", iIni);
                if (iFinal > iIni){
                    String divApagar = texto.substring(iIni-9,iFinal+6);
                    texto = texto.replace(divApagar,"");
                }
            }
            StringBuilder divNovaAssinatura = new StringBuilder(texto.replace("</body>", "").replace("</html>", ""));
            divNovaAssinatura.append(divAssinatura);
            divNovaAssinatura.append("</body>");
            divNovaAssinatura.append("</html>");

            return divNovaAssinatura.toString();
        }
    }

    public static String addAssinatura2(String texto, String urlAssinatura){
        String divAssinatura = "<div id=\"idassinatur4digit4l2\" style=\"width: 20%; text-align: center; margin: 0 auto;\">"
                + (texto.contains("__4ssin4tur4digit4l2__") ? "" : "Assinatura digital resp. financeiro: ")
                + "<img style=\"width: 100%;\" src=\""
                .concat(urlAssinatura).concat("\"/></div>");
        if(texto.contains("__4ssin4tur4digit4l2__")){
            return texto.replace("__4ssin4tur4digit4l2__", divAssinatura);
        }else{
            int iIni = texto.indexOf("idassinatur4digit4l2");
            if(iIni > 0) {
                int iFinal = texto.indexOf("</div>", iIni);
                if (iFinal > iIni){
                    String divApagar = texto.substring(iIni-9,iFinal+6);
                    texto = texto.replace(divApagar,"");
                }
            }
            StringBuilder divNovaAssinatura = new StringBuilder(texto.replace("</body>", "").replace("</html>", ""));
            divNovaAssinatura.append(divAssinatura);
            divNovaAssinatura.append("</body>");
            divNovaAssinatura.append("</html>");

            return divNovaAssinatura.toString();
        }
    }

    public void substituirTagsTextoPadraoVendaAvulsa(String key, Connection con, VendaAvulsaVO venda, List<MovPagamentoVO> pagamentos, String descMoedaEmpresa) throws Exception {
        substituirTagsTextoPadraoVendaAvulsa(key, con, venda, null, pagamentos, descMoedaEmpresa, null, false, false, null);
    }

    public void substituirTagsTextoPadraoVendaAvulsa(String key, Connection con, VendaAvulsaVO venda, AulaAvulsaDiariaVO vendaDiaria, List<MovPagamentoVO> pagamentos, String descMoedaEmpresa, HttpServletRequest request, boolean telaNova, boolean removerImprimir, Integer codProduto) throws Exception {
        try {
            if (getTexto().length() != 0) {
                char aspas = '"';
                String espaco = "<p style=" + aspas + "text-align: left;" + aspas + ">";
                String htmlBotao = "<form> ";
                String htmlBotao2 = "<form> <input id=" + aspas + "imprimir" + aspas + "type=" + aspas + "image" + aspas + " src=" + aspas + "./imagens/imprimirContrato.png" + aspas + " name=" + aspas + "imprimir" + aspas + " alt=" + aspas + "Imprimir Contrato" + aspas + "onclick=" + aspas + "window.print();" + aspas + "/>";
                String textoSub = getTexto();
                textoSub = textoSub.replace("Untitled document", "CONTRATO DE PRESTA&Ccedil;&Atilde;O DE SERVI&Ccedil;OS");
                int posicaoBody = textoSub.indexOf("<body>");
                String parte1 = textoSub.substring(0, posicaoBody + 6);
                String parte2 = textoSub.substring(posicaoBody + 6);
                parte1 = parte1.replace("</head>", "<style>@media print {#imprimir {display: none;}}</style></head>");
                textoSub = parte1 + htmlBotao + parte2;

                posicaoBody = textoSub.indexOf("</body>");
                parte1 = textoSub.substring(0, posicaoBody);
                parte2 = textoSub.substring(posicaoBody);
                textoSub = parte1 + htmlBotao2 + espaco + "</form>" + parte2;
                StringBuilder textoSubBuilder = new StringBuilder();
                textoSubBuilder.append(textoSub);
//                replaceLogomarca(textoSubBuilder);
                textoSub = textoSubBuilder.toString();
                textoSub = venda != null ? varrerListaTagUtilizadoTextoPadraoVendaAvulsa(key, textoSub, venda, pagamentos, con, descMoedaEmpresa, codProduto) :
                        varrerListaTagUtilizadoTextoPadraoAulaAvulsaDiaria(key, textoSub, vendaDiaria, pagamentos, con, descMoedaEmpresa, codProduto);
                if (removerImprimir) {
                    textoSub = textoSub.replace("<input id=\"imprimir\"type=\"image\" src=\"./imagens/imprimirContrato.png\" name=\"imprimir\" alt=\"Imprimir Contrato\"onclick=\"window.print();\"/>", "");
                }
                textoSub = limparTagsAnexoAssinatura(textoSub);
                if (request == null) {
                    request = (HttpServletRequest) context().getExternalContext().getRequest();
                }
                request.getSession().setAttribute("textoRelatorio", textoSub);

                if (!telaNova) {
                    int codEmpresa = venda != null ? venda.getEmpresa().getCodigo() : vendaDiaria.getEmpresa().getCodigo();
                    Empresa empDao = new Empresa(con);
                    EmpresaVO emp = empDao.consultarPorChavePrimaria(
                            codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (context() != null) {
                        JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", emp);
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public String limparTagsAnexoAssinatura(String html){
        html = html.replace("[(1){}docRg]", "");
        html = html.replace("[(1){}endereco]", "");
        html = html.replace("[(1){}atestado]", "");
        html = html.replace("[(1){}anexo1]", "");
        html = html.replace("[(1){}anexo2]", "");
        return html;
    }

    public void substituirTagsTextoEmBranco(int codigoEmpresa, Connection con) throws Exception {
        try {
            if (getTexto().length() != 0) {
                char aspas = '"';
                String espaco = "<p style=" + aspas + "text-align: left;" + aspas + ">";
                String htmlBotao = "<form> ";
                String htmlBotao2 = "<form> <input id=" + aspas + "imprimir" + aspas + "type=" + aspas + "image" + aspas + " src=" + aspas + "./imagens/imprimirContrato.png" + aspas + " name=" + aspas + "imprimir" + aspas + " alt=" + aspas + "Imprimir Contrato" + aspas + "onclick=" + aspas + "window.print();" + aspas + "/>";
                String textoSub = getTexto();
                textoSub = textoSub.replace("Untitled document", "CONTRATO DE PRESTA&Ccedil;&Atilde;O DE SERVI&Ccedil;OS");
                int posicaoBody = textoSub.indexOf("<body>");
                String parte1 = textoSub.substring(0, posicaoBody + 6);
                String parte2 = textoSub.substring(posicaoBody + 6);
                parte1 = parte1.replace("</head>", "<style>@media print {#imprimir {display: none;}}</style></head>");
                textoSub = parte1 + htmlBotao + parte2;

                posicaoBody = textoSub.indexOf("</body>");
                parte1 = textoSub.substring(0, posicaoBody);
                parte2 = textoSub.substring(posicaoBody);
                textoSub = parte1 + htmlBotao2 + espaco + "</form>" + parte2;

                textoSub = varrerListaTagUtilizadoTextoEmBranco(codigoEmpresa, textoSub, con, false);
                HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
                request.getSession().setAttribute("textoRelatorio", textoSub);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public String verificaQualMetodoParaSubstituicaoInvocarPlanoPersonal(String key, MarcadorVO marcador,
                                                            String texto, ColaboradorVO colaborador, ControleTaxaPersonalVO taxaPersonal, VendaAvulsaVO venda, Connection con, String descMoedaEmpresa) throws Exception {

        try {
            if (marcador.getTag().contains("Contrato")) {
                return substituirTagPlanoPersonal(texto, taxaPersonal, marcador,descMoedaEmpresa);
            } else if (marcador.getTag().contains("Plano")) {
                return substituirTagPlanoTaxaPersonal(texto, taxaPersonal, marcador, con);
            } else if (marcador.getTag().contains("Usuario")) {
                return substituirTagUsuarioTaxaPersonal(key, texto, taxaPersonal, venda, marcador);
            } else if (marcador.getTag().contains("Empresa")) {
                return substituirTagEmpresaTaxaPersonal(key, texto, taxaPersonal, venda, marcador);
            } else if (marcador.getTag().contains("Cliente")) {
                return substituirTagClienteTaxaPersonal(texto, colaborador, taxaPersonal, venda, marcador, con);
            } //else if (marcador.getTag().contains("Recibo")) {
             //   ReciboPagamento rec = new ReciboPagamento(con);
           //     List<ReciboPagamentoVO> reciboPagamentoVOs = rec.consultarPorCodigo(taxaPersonal.get,, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            //    return substituirTagReciboPagamento(texto, reciboPagamentoVOs, marcador, descMoedaEmpresa);
          //  }
            return texto;
        } catch (Exception e) {
            throw e;
        }
    }

    public String verificaQualMetodoParaSubstituicaoInvocar(String key, MarcadorVO marcador,
                                                             String texto, ClienteVO cliente, ContratoVO contrato,
                                                            VendaAvulsaVO venda, AulaAvulsaDiariaVO vendaDiaria, List<MovPagamentoVO> movPagamentoVOs,
                                                            Connection con, String descMoedaEmpresa, boolean manterTagAssinaturaDigital, Integer codProduto) throws Exception {
        ReciboPagamento reciboPagamentoDAO;
        try {
             reciboPagamentoDAO = new ReciboPagamento(con);

            if (marcador.getTag().contains("Contrato")) {
                return substituirTagContrato(texto, contrato, marcador,descMoedaEmpresa);
            } else if (marcador.getTag().contains("Plano")) {
                return substituirTagPlano(texto, contrato, marcador, con);
            } else if (marcador.getTag().contains("Usuario")) {
                return substituirTagUsuario(key, texto, contrato, venda, vendaDiaria, marcador);
            } else if (marcador.getTag().contains("Empresa")) {
                return substituirTagEmpresa(key, texto, contrato, venda, vendaDiaria, marcador);
            } else if (marcador.getTag().contains("Modalidade")) {
                return substituirTagModalidade(texto, contrato, marcador);
            } else if (marcador.getTag().contains("Composicao")) {
                return substituirTagComposicao(texto, contrato, marcador);
            } else if (marcador.getTag().contains("MovParcela")) {
                if (contrato != null) {
                    return substituirTagMovParcela(texto, contrato.getMovParcelaVOs(), marcador);
                } else {
                    List<MovParcelaVO> parcelas = new ArrayList<>();
                    if (venda != null) {
                        if (venda.getMovParcelaVOs().size() > 1) {
                            parcelas.addAll(venda.getMovParcelaVOs());
                        } else {
                            parcelas.add(venda.getParcela());
                        }
                    } else {
                        if (vendaDiaria.getMovParcelaVOs().size() > 1) {
                            parcelas.addAll(vendaDiaria.getMovParcelaVOs());
                        } else {
                            parcelas.add(vendaDiaria.getParcela());
                        }
                    }
                    return substituirTagMovParcela(texto, parcelas, marcador);
                }
            } else if (marcador.getTag().contains("Turma")) {
                return substituirTagTurma(texto, contrato, marcador);
            } else if (marcador.getTag().contains("Cliente")) {
                return substituirTagCliente(texto, cliente, contrato, venda, vendaDiaria, marcador, con,manterTagAssinaturaDigital, codProduto);
            } else if (marcador.getTag().contains("MovPagamento") || marcador.getTag().contains("Cheque")) {
                return substituirTagMovPagamento(texto, movPagamentoVOs, marcador);
            } else if (marcador.getTag().contains("Recibo")) {
                List<ReciboPagamentoVO> reciboPagamentoVOs = reciboPagamentoDAO.consultarPorCodigoContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                return substituirTagReciboPagamento(texto, reciboPagamentoVOs, marcador, descMoedaEmpresa);
            } else if (marcador.getTag().contains("Venda")) {
                return venda != null ? substituirTagVendaAvulsa(texto, venda, marcador) : substituirTagVendaAulaAvulsaDiaria(texto, vendaDiaria, marcador);
            } else if (marcador.getTag().contains("Itens")) {
                return substituirTagItensVenda(texto, venda, marcador);
            } else if (marcador.getTag().contains("PacoteItem")) {
                return substituirTagPacoteVenda(texto, venda, marcador);
            }
            return texto;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            reciboPagamentoDAO = null;
        }
    }

    public String verificaQualMetodoParaSubstituicaoEmBranco(int codigoEmpresa, MarcadorVO marcador, String texto, Connection con, boolean site) throws Exception {
        try {
            if (marcador.getTag().contains("Contrato")) {
                return substituirTagContratoEmBranco(texto, marcador, site);
            } else if (marcador.getTag().contains("Plano")) {
                return substituirTagPlanoEmBranco(texto, marcador);
            } else if (marcador.getTag().contains("Usuario")) {
                return substituirTagUsuarioEmBranco(texto, marcador);
            } else if (marcador.getTag().contains("Empresa")) {
                return substituirTagEmpresaEmBranco(codigoEmpresa, texto, marcador, con);
            } else if (marcador.getTag().contains("Modalidade")) {
                return texto.replace(marcador.getTag(), obterStringEmBrancoParaTag(marcador));
            } else if (marcador.getTag().contains("Composicao")) {
                return texto.replace(marcador.getTag(), obterStringEmBrancoParaTag(marcador));
            } else if (marcador.getTag().contains("MovParcela")) {
                return texto.replace(marcador.getTag(), "</br></br></br>");
            } else if (marcador.getTag().contains("Turma")) {
                return texto.replace(marcador.getTag(), "</br></br></br>");
            } else if (marcador.getTag().contains("Cliente")) {
                return substituirTagClienteEmBranco(texto, marcador);
            } else if (marcador.getTag().contains("MovPagamento") || marcador.getTag().contains("Cheque")) {
                return texto.replace(marcador.getTag(), obterStringEmBrancoParaTag(marcador));
            } else if (marcador.getTag().contains("Recibo")) {
                return substituirTagReciboPagamentoEmBranco(texto, marcador);
            } else if (marcador.getTag().contains("Venda")) {
                return substituirTagVendaAvulsaEmBranco(texto, marcador);
            } else if (marcador.getTag().contains("Itens")) {
                return texto.replace(marcador.getTag(), "</br></br></br>");
            } else if (marcador.getTag().contains("PacoteItem")) {
                return substituirTagPacoteVendaEmBranco(texto, marcador);
            }
            return texto;
        } catch (Exception e) {
            throw e;
        }
    }

    public String varrerListaTagUtilizadoReciboPadrao(String key, String texto, ClienteVO cliente, ContratoVO contrato,
            List<MovPagamentoVO> movPagamentoVOs, List<MovParcelaVO> movParcelaVOs,
            ReciboPagamentoVO reciboPagamento, Connection con, String descMoedaEmpresa) throws Exception {
        try {
            MarcadorVO marcador = new MarcadorVO();
            Iterator i = contrato.getPlano().getReciboTextoPadrao().getListaTagUtilizado().iterator();
            while (i.hasNext()) {
                PlanoTextoPadraoTagVO p = (PlanoTextoPadraoTagVO) i.next();
                marcador.setTag(p.getTag());
                texto = verificaQualMetodoParaSubstituicaoInvocar(key, marcador, texto, cliente, contrato, null, null, movPagamentoVOs, con,descMoedaEmpresa,true, null);
            }
            return texto;
        } catch (Exception e) {
            throw e;
        }
    }

    public String varrerListaTagUtilizadoTextoPadrao(String key, String texto, ClienteVO cliente, ContratoVO contrato, List<MovPagamentoVO> pagamentos, Connection con, String descMoedaEmpresa) throws Exception {
        MarcadorVO marcador = new MarcadorVO();
        Iterator i = contrato.getPlano().getPlanoTextoPadrao().getListaTagUtilizado().iterator();
        while (i.hasNext()) {

                PlanoTextoPadraoTagVO p = (PlanoTextoPadraoTagVO) i.next();
                marcador.setTag(p.getTag());
            try {
                texto = verificaQualMetodoParaSubstituicaoInvocar(key, marcador, texto, cliente, contrato, null, null, pagamentos, con, descMoedaEmpresa,true, null);
            }catch (Exception e){
                throw  new ConsistirException("Erro ao substituir tag \""+marcador.getTag()+"\": "+e.getMessage());
            }
        }
        return texto;
    }

    public String varrerListaTagUtilizadoTextoPadraoPlanoPersonal(String key, String texto, ColaboradorVO personal, ControleTaxaPersonalVO taxaPersonal, Connection con, String descMoedaEmpresa) throws Exception {
        // String key, ControleTaxaPersonalVO taxaPersonal, ColaboradorVO colaborador, Connection con, String descMoedaEmpresa
        MarcadorVO marcador = new MarcadorVO();
        Iterator i = taxaPersonal.getPlano().getPlanoTextoPadrao().getListaTagUtilizado().size() > 0 ?
                     taxaPersonal.getPlano().getPlanoTextoPadrao().getListaTagUtilizado().iterator() :
                     taxaPersonal.getPlanoPersonalTextoPadrao().getPlanoTextoPadrao().getListaTagUtilizado().iterator();
        while (i.hasNext()) {
            PlanoTextoPadraoTagVO p = (PlanoTextoPadraoTagVO) i.next();
            marcador.setTag(p.getTag());
            texto = verificaQualMetodoParaSubstituicaoInvocarPlanoPersonal(key, marcador, texto, personal, taxaPersonal, null, con,descMoedaEmpresa);
        }
        return texto;
    }

    public String varrerListaTagUtilizadoTextoPadraoVendaAvulsa(String key, String texto, VendaAvulsaVO venda, List<MovPagamentoVO> pagamentos, Connection con, String descMoedaEmpresa, Integer codProduto) throws Exception {
        try {
            MarcadorVO marcador = new MarcadorVO();
            Iterator i = venda.getTextoPadrao().getListaTagUtilizado().iterator();
            while (i.hasNext()) {
                PlanoTextoPadraoTagVO p = (PlanoTextoPadraoTagVO) i.next();
                marcador.setTag(p.getTag());
                texto = verificaQualMetodoParaSubstituicaoInvocar(key, marcador, texto, venda.getCliente(), null, venda, null, pagamentos, con, descMoedaEmpresa,false, codProduto);
            }
            return texto;
        } catch (Exception e) {
            throw e;
        }
    }

    public String varrerListaTagUtilizadoTextoPadraoAulaAvulsaDiaria(String key, String texto, AulaAvulsaDiariaVO vendaDiaria, List<MovPagamentoVO> pagamentos, Connection con, String descMoedaEmpresa, Integer codProduto) throws Exception {
        try {
            MarcadorVO marcador = new MarcadorVO();
            Iterator i = vendaDiaria.getTextoPadrao().getListaTagUtilizado().iterator();
            while (i.hasNext()) {
                PlanoTextoPadraoTagVO p = (PlanoTextoPadraoTagVO) i.next();
                marcador.setTag(p.getTag());
                texto = verificaQualMetodoParaSubstituicaoInvocar(key, marcador, texto, vendaDiaria.getCliente(), null, null, vendaDiaria, pagamentos, con, descMoedaEmpresa,false, codProduto);
            }
            return texto;
        } catch (Exception e) {
            throw e;
        }
    }

    public String varrerListaTagUtilizadoTextoEmBranco(int codigoEmpresa, String texto, Connection con, boolean site) throws Exception {
        try {
            MarcadorVO marcador = new MarcadorVO();
            Iterator i = this.getListaTagUtilizado().iterator();
            while (i.hasNext()) {
                PlanoTextoPadraoTagVO p = (PlanoTextoPadraoTagVO) i.next();
                marcador.setTag(p.getTag());
                texto = verificaQualMetodoParaSubstituicaoEmBranco(codigoEmpresa, marcador, texto, con, site);
            }
            return texto;
        } catch (Exception e) {
            throw e;
        }
    }
    public Double valorConvenioDescontoContrato(ContratoVO contrato){

         if (contrato.getConvenioDesconto().getCodigo() != 0) {
                Iterator i = contrato.getConvenioDesconto().getConvenioDescontoConfiguracaoVOs().iterator();
                while (i.hasNext()) {
                    ConvenioDescontoConfiguracaoVO obj = (ConvenioDescontoConfiguracaoVO) i.next();
                    if (obj.getConvenioDescontoConfiguracaoEscolhida()) {
                        if (obj.getTipoDesconto().equals("VE")) {
                            return (obj.getValorDesconto());
                        } else {
                            return (Uteis.arredondarForcando2CasasDecimais(contrato.getValorBaseCalculo() * (obj.getPorcentagemDesconto() / 100)));
                        }
                    }
                }
            }

         return 0.0;
    }

    public String substituirTagPlanoPersonal(String texto, ControleTaxaPersonalVO controleTaxaPersonal, MarcadorVO marcador, String descMoedaEmpresa) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String codigo = controleTaxaPersonal.getCodigo().toString();
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }
        String vigenciaDe = controleTaxaPersonal.getDataInicioVigencia_apresentar();
        if (vigenciaDe.equals("0") || vigenciaDe.equals("")) {
            vigenciaDe = emBranco;
        }
        String vigenciaAte = controleTaxaPersonal.getDataFimVigencia_apresentar();
        if (vigenciaAte.equals("0") || vigenciaAte.equals("")) {
            vigenciaAte = emBranco;
        }

        String dtLancamento = Uteis.getDataComHora(controleTaxaPersonal.getPlano().getDataLancamentoContratoInicio());
        if (dtLancamento.equals("")) {
                dtLancamento = emBranco;
        }

        String convenio = controleTaxaPersonal.getPlano().getConvenioCobrancaPrivateLabel().getDescricao();
            if (dtLancamento.equals("")) {
            dtLancamento = emBranco;
        }

        Double valor = 0.0;
        for (ItemTaxaPersonalVO item: controleTaxaPersonal.getAlunos()) {
            valor += item.getValorFinal();
        }

        //controleTaxaPersonal.getAlunos().

        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());
            if (mar.equalsIgnoreCase("Codigo_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaDe_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), vigenciaDe, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaAte_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), vigenciaAte, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), controleTaxaPersonal.getResponsavel().getNome(), textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("DtLancamento_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), dtLancamento, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("DtLancamento_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), dtLancamento, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Convenio_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), convenio, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaAteAjustada_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorBaseCalculo_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorFinal_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Observacao_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Duracao_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("quantidadeCreditoTreino_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("totalDias_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Horario_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NomeModalidades_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NomeCompletoModalidades_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NrVezesNomeCompletoModalidades_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NomeModalidadesNrVezes_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ConsultorResponsavel_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorPorExtenso_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ConvenioDescontoResumo_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorTotalSemDesconto_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorTotalDescontoContrato_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("DiasCarencia_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorTotalContratoSemDescontoExtra_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorParcelaMensal_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorMensal_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorMensalAdequado_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorMensalBase_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorDescontoExtra_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorMatricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorRematricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("CondicaoPagamento_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NomeProduto_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("QtdProduto_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorProduto_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorDescontoProduto_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorUnitarioProduto_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("TabelaProdutos_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("diaVencimentoCartao_ContratoRecorrencia")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorAnuidade_ContratoRecorrencia")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorAdesao_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Saldo_Credito_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorCheioMatricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorCheioRematricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorDescontoMatricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorDescontoRematricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorDescontoAnuidade_ContratoRecorrencia")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorFinalAnuidade_ContratoRecorrencia")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            }
            else {
                throw new Exception("<br>Tag " + mar + " não existe<br>");
            }
        }
        return texto;
    }

    public String substituirTagContrato(String texto, ContratoVO contrato, MarcadorVO marcador, String descMoedaEmpresa) throws Exception {
        if (contrato == null) {
            return texto;
        }
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String codigo = contrato.getCodigo().toString();
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }
        String vigenciaDe = contrato.getVigenciaDe_Apresentar();
        if (vigenciaDe.equals("0") || vigenciaDe.equals("")) {
            vigenciaDe = emBranco;
        }
        String vigenciaAte = contrato.getVigenciaAte_Apresentar();
        if (vigenciaAte.equals("0") || vigenciaAte.equals("")) {
            vigenciaAte = emBranco;
        }
        String vigenciaAteAjustada = contrato.getVigenciaAteAjustada_Apresentar();
        if (vigenciaAteAjustada.equals("0") || vigenciaAteAjustada.equals("")) {
            vigenciaAteAjustada = emBranco;
        }
        String valorBaseCalculo = Uteis.getDoubleFormatado(contrato.getValorBaseCalculo());
        if (valorBaseCalculo.equals("0") || valorBaseCalculo.equals("")) {
            valorBaseCalculo = emBranco;
        }
        String valorFinal = Uteis.getDoubleFormatado(contrato.getValorFinal());
        if (valorFinal.equals("0") || valorFinal.equals("")) {
            valorFinal = emBranco;
        }
        String observacao = contrato.getObservacao();
        if (observacao.equals("0") || observacao.equals("")) {
            observacao = emBranco;
        }
        String duracao = contrato.getContratoDuracao().getNumeroMeses().toString();
        if (duracao.equals("0") || duracao.equals("")) {
            duracao = emBranco;
        }
        String horario = contrato.getContratoHorario().getHorario().getDescricao();
        if (horario.equals("0") || horario.equals("")) {
            horario = emBranco;
        }
        String dtLancamento = contrato.getDataLancamento_Apresentar();
        if (dtLancamento.equals("")) {
            dtLancamento = emBranco;
        }

        String convenio = contrato.getConvenioDesconto().getDescricao();
        if (dtLancamento.equals("")) {
            dtLancamento = emBranco;
        }
        Double valor = contrato.getValorMatricula();
        String valorMatricula = Formatador.formatarValorMonetario(valor);
        String valorCheioMatricula = Formatador.formatarValorMonetario(contrato.getValorCheioMatricula());
        String valorCheioRematricula = Formatador.formatarValorMonetario(contrato.getValorCheioRematricula());
        String valorDescontoMatricula = Formatador.formatarValorMonetario(contrato.getValorDescontoMatricula());
        String valorDescontoRematricula = Formatador.formatarValorMonetario(contrato.getValorDescontoRematricula());
        Double valorDescontoExtraSemFormatacao = 0.0;
        String valorDescontoExtra = Formatador.formatarValorMonetario(0.0);
        NumeroPorExtenso npe = new NumeroPorExtenso(contrato.getValorFinal(),descMoedaEmpresa);
        String valorPorExtenso = npe.toString().toUpperCase();

        String qtdeCredito = "";
        String saldoCredito = "";
        if (contrato.isVendaCreditoTreino()){
            if (UtilReflection.objetoMaiorQueZero(contrato, "getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getCodigo()")){
                qtdeCredito = contrato.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getQuantidadeCreditoCompra().toString();
                saldoCredito = contrato.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getQuantidadeCreditoDisponivel().toString();
            }
        }
        String qtdeDias = contrato.getContratoDuracao().getTotalDias().toString();

        if (!UteisValidacao.emptyString(contrato.getTipoDesconto())) {
            //caso o desconto seja do tipo valor, apenas formatá-lo e apresentar
            if (contrato.getTipoDesconto().equals("VA")) {
                valorDescontoExtraSemFormatacao = contrato.getValorDescontoEspecifico();
                valorDescontoExtra = Formatador.formatarValorMonetario(valorDescontoExtraSemFormatacao);
            } else {
                //caso o valor seja do tipo porcentagem, calculá-lo
                //primeiro obter o valor do contrato sem desconto
                Double valorContratoSemDesconto = contrato.getValorBaseCalculo() / ((100 - contrato.getValorDescontoPorcentagem()) / 100);
                //em seguida obter o valor do desconto
                Double valorDesconto = valorContratoSemDesconto - contrato.getValorBaseCalculo();
                valorDescontoExtraSemFormatacao = valorDesconto;
                valorDescontoExtra = Formatador.formatarValorMonetario(valorDescontoExtraSemFormatacao);
            }
        }
        //pesquisar o primeiro produto plano do contrato
        MovProdutoVO produto = contrato.getProdutoPlano();
        //se o valor do produto não for nulo
        String valorMensal = Formatador.formatarValorMonetario(0.0);
        String valorMensalDesconto = Formatador.formatarValorMonetario(0.0);
        String valorMensalAdequado = Formatador.formatarValorMonetario(0.0);
        if (produto != null) {
            int i = 1;
            valorMensal = Formatador.formatarValorMonetario(produto.getPrecoUnitario());
            valorMensalAdequado = Formatador.formatarValorMonetario(produto.getTotalFinal());
            valorMensalDesconto = Formatador.formatarValorMonetario(produto.getValorDesconto());
        }

        //valor da rematricula
        Double valorRemat = contrato.getValorRematricula();
        String valorRematricula = Formatador.formatarValorMonetario(valorRemat);

        //valor base mensal por modalidade
        String valorMensalBase = Formatador.formatarValorMonetario(contrato.obterValorContratoReferenteMensal());

        //valor total do contrato sem os descontos na tela de negociação
        Double valorTotalContratoSemDescontoSemFormatacao = (contrato.obterValorContratoReferenteMensal() * contrato.getDuracao_Apresentar());
        String valorTotalContratoSemDesconto = Formatador.formatarValorMonetario(valorTotalContratoSemDescontoSemFormatacao);

        //valor total do contrato sem desconto extra
        Double valorTotalContratoSemDescontoExtraSemFormatacao = (contrato.getValorFinal() + valorDescontoExtraSemFormatacao);
        String valorTotalContratoSemDescontoExtra = Formatador.formatarValorMonetario(valorTotalContratoSemDescontoExtraSemFormatacao);

        //*****//valor total do contrato sem desconto extra e convenio de cobrança
        //Double valorTotalContratoFinalFormatacao = (valorTotalContratoSemDescontoSemFormatacao - contrato.getValorFinal());
        //Double valorTesteMensal = ((valorTotalContratoSemDescontoSemFormatacao - valorTotalContratoFinalFormatacao
              //  + contrato.getValorBaseCalculo() + valorDescontoExtraSemFormatacao - contrato.getSomaProduto() ) / contrato.getDuracao_Apresentar());
        //Double  valorTesteMensal = (contrato.getValorBaseCalculo());
        Double valorMensalConsiderar = 0.0;
        if (valorMensal != null) {
            valorMensalConsiderar = produto.getPrecoUnitario();
        }
        String valorParcelaMensal = Formatador.formatarValorMonetario(valorMensalConsiderar);

        //valor total de descontos do contrato
        String valorTotalDescontoContrato = Formatador.formatarValorMonetario(valorTotalContratoSemDescontoSemFormatacao - contrato.getValorFinal());

        // Dias de Carência do contrato
        String diasCarenciaContrato = contrato.getPlanoDuracao().getCarencia().toString();

        //convênio desconto
        String conveniodescontotexto = "";
        if (contrato.getConvenioDesconto().getCodigo() > 0) {

            String desconto = "";

            List lista = contrato.getConvenioDesconto().getConvenioDescontoConfiguracaoVOs();


            for (Object obj : lista) {
                ConvenioDescontoConfiguracaoVO convenioconf = (ConvenioDescontoConfiguracaoVO) obj;
                if (convenioconf.getDuracao().equals(contrato.getDuracao_Apresentar())) {
                    contrato.getConvenioDescontoConfiguracaoVO().setPorcentagemDesconto(convenioconf.getPorcentagemDesconto());
                    contrato.getConvenioDescontoConfiguracaoVO().setValorDesconto(convenioconf.getValorDesconto());
                    contrato.getConvenioDescontoConfiguracaoVO().setTipoDesconto(convenioconf.getTipoDesconto());
                }
            }

            if (contrato.getConvenioDescontoConfiguracaoVO().getPorcentagemDesconto() != 0.0) {
                desconto = Uteis.arrendondarForcando2CadasDecimaisComVirgula(contrato.getConvenioDescontoConfiguracaoVO().getPorcentagemDesconto())+"%";
            }
            if (contrato.getConvenioDescontoConfiguracaoVO().getValorDesconto() != 0.0) {
                desconto = "R$ " + contrato.getConvenioDescontoConfiguracaoVO().getValorDesconto();
            }

            conveniodescontotexto = "Convênio: " + contrato.getConvenioDesconto().getDescricao() +  " - " + contrato.getConvenioDescontoConfiguracaoVO().getTipoDesconto_Apresentar() + " " + desconto;

        }

        Double valorAnuidade = 0.00;
        String diaVencimentoCartao ="";

        Double valorAdesao = 0.00;

        String valorDescontoAnuidadeRecorrencia = Formatador.formatarValorMonetario(0.0);
        String valorFinalAnuidadeRecorrencia = Formatador.formatarValorMonetario(0.0);

        if (contrato.getRegimeRecorrencia()) {
            if (contrato.getContratoRecorrenciaVO() != null) {
                valorAnuidade = contrato.getContratoRecorrenciaVO().getValorAnuidade();
                valorDescontoAnuidadeRecorrencia = Formatador.formatarValorMonetario(contrato.getValorDescontoAnuidadeRecorrencia());
                valorFinalAnuidadeRecorrencia = Formatador.formatarValorMonetario(contrato.getValorFinalAnuidadeRecorrencia());
                diaVencimentoCartao = contrato.getContratoRecorrenciaVO().getDiaVencimentoCartao().toString();
            }

            if (contrato.getPlano().getPlanoRecorrencia() != null) {
                valorAdesao = contrato.getPlano().getPlanoRecorrencia().getTaxaAdesao();

                PlanoEmpresaVO planoEmpresaVO = null;
                if (contrato.getPlano().getEmpresas() != null && !contrato.getPlano().getEmpresas().isEmpty()) {
                    for (PlanoEmpresaVO emp : contrato.getPlano().getEmpresas()) {
                        if (emp.getEmpresa().getCodigo().equals(contrato.getEmpresa().getCodigo())) {
                            planoEmpresaVO = emp;
                            break;
                        }
                    }
                    if (planoEmpresaVO != null && !UteisValidacao.emptyNumber(planoEmpresaVO.getTaxaAdesao())) {
                        valorAdesao = planoEmpresaVO.getTaxaAdesao();
                    }
                }
            }
        }



        List<MovProdutoVO> produtosVOContrato = contrato.getMovProdutoVOs();

        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());
            if (mar.equalsIgnoreCase("Codigo_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaDe_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), vigenciaDe, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaAte_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), vigenciaAte, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaAteAjustada_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), vigenciaAteAjustada, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorBaseCalculo_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorBaseCalculo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorFinal_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorFinal, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Observacao_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), observacao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Duracao_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), duracao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("quantidadeCreditoTreino_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), qtdeCredito, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("totalDias_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), qtdeDias, textoTag, tamanho);
            }
            else if (mar.equalsIgnoreCase("Horario_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), horario, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NomeModalidades_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), contrato.getNomeModalidades(), textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NomeCompletoModalidades_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), obterNomeNrVezesModalidade(contrato, false, false), textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NrVezesNomeCompletoModalidades_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), obterNomeNrVezesModalidade(contrato, true, false), textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NomeModalidadesNrVezes_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), obterNomeNrVezesModalidade(contrato, true, true), textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), contrato.getResponsavelContrato().getNome(), textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ConsultorResponsavel_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), contrato.getConsultor().getPessoa().getNome(), textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("DtLancamento_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), dtLancamento, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("DtLancamento_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), dtLancamento, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorPorExtenso_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorPorExtenso, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Convenio_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), convenio, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ConvenioDescontoResumo_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), conveniodescontotexto, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorTotalSemDesconto_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorTotalContratoSemDesconto, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorTotalDescontoContrato_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorTotalDescontoContrato, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("DiasCarencia_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), diasCarenciaContrato, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorTotalContratoSemDescontoExtra_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorTotalContratoSemDescontoExtra, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorParcelaMensal_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorParcelaMensal, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorMensal_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorMensal, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorMensalDesconto_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorMensalDesconto, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorMensalAdequado_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorMensalAdequado, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorMensalBase_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorMensalBase, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorDescontoExtra_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorDescontoExtra, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorMatricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorMatricula, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorRematricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorRematricula, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("CondicaoPagamento_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), contrato.getContratoCondicaoPagamento().getCondicaoPagamento().getDescricao(), textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NomeProduto_Contrato")) {
                if (!UteisValidacao.emptyNumber(produtosVOContrato.size())) texto = substituirTagProdutoContrato(texto, produtosVOContrato, marcador);
                if (UteisValidacao.emptyNumber(produtosVOContrato.size()) && contrato.getProdutoPlano() != null)  texto = substituirTag(texto, marcador.getTag(), contrato.getProdutoPlano().getDescricao(), textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("QtdProduto_Contrato")) {
                texto = substituirTagProdutoContrato(texto, produtosVOContrato, marcador);
            } else if (mar.equalsIgnoreCase("ValorProduto_Contrato")) {
                texto = substituirTagProdutoContrato(texto, produtosVOContrato, marcador);
            } else if (mar.equalsIgnoreCase("ValorDescontoProduto_Contrato")) {
                texto = substituirTagProdutoContrato(texto, produtosVOContrato, marcador);
            } else if (mar.equalsIgnoreCase("ValorUnitarioProduto_Contrato")) {
                texto = substituirTagProdutoContrato(texto, produtosVOContrato, marcador);
            } else if (mar.equalsIgnoreCase("TabelaProdutos_Contrato")) {
                texto = substituirTagProdutoContratoTabela(texto, produtosVOContrato, marcador);
            } else if (mar.equalsIgnoreCase("diaVencimentoCartao_ContratoRecorrencia")) {
                texto = substituirTag(texto, marcador.getTag(), diaVencimentoCartao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorAnuidade_ContratoRecorrencia")) {
                texto = substituirTag(texto, marcador.getTag(),Formatador.formatarValorMonetario(valorAnuidade) , textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorAdesao_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(),Formatador.formatarValorMonetario(valorAdesao) , textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Saldo_Credito_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), saldoCredito, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorCheioMatricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorCheioMatricula, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorCheioRematricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorCheioRematricula, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorDescontoMatricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorDescontoMatricula, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorDescontoRematricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), valorDescontoRematricula, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorDescontoAnuidade_ContratoRecorrencia")) {
                texto = substituirTag(texto, marcador.getTag(), valorDescontoAnuidadeRecorrencia, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorFinalAnuidade_ContratoRecorrencia")) {
                texto = substituirTag(texto, marcador.getTag(), valorFinalAnuidadeRecorrencia, textoTag, tamanho);
            } else {
                throw new Exception("<br>Tag " + mar + " não existe<br>");
            }
        }
        return texto;
    }
    public String substituirTagProdutoContratoTabela(String texto, List<MovProdutoVO> movProdutoVOs, MarcadorVO marcador) {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        StringBuilder tabela = new StringBuilder("<br/>");
        if (movProdutoVOs.size() == 0) {
            texto = texto.replace(marcador.getTag(), emBranco);
        } else {
            tabela.append("<table border='1' cellspacing='0'>");
            tabela.append("<tr>");
            tabela.append("<th>").append("Descrição").append("</th>");
            tabela.append("<th>").append("Quantidade").append("</th>");
            tabela.append("<th>").append("Valor total").append("</th>");
            tabela.append("</tr>");
            for (MovProdutoVO movProdutoVO1 : movProdutoVOs) {
                tabela.append("<tr>");
                tabela.append("<td>").append(movProdutoVO1.getDescricao()).append("</td>");
                tabela.append("<td align='center'>").append(movProdutoVO1.getQuantidade()).append("</td>");
                tabela.append("<td>").append(Formatador.formatarValorMonetario(movProdutoVO1.getTotalFinal())).append("</td>");
                tabela.append("</tr>");
            }
            tabela.append("</table>");
        }
        texto = texto.replace(marcador.getTag(), tabela.toString());
        return texto;
    }

    public String substituirTagProdutoContrato(String texto, List<MovProdutoVO> movProdutoVOs, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        tagGeral = marcador.getTag();
        if (movProdutoVOs.size() == 0) {
            texto = texto.replace(marcador.getTag(), emBranco);
        } else {
            for (MovProdutoVO movProdutoVO1 : movProdutoVOs) {
                texto = gerarLinhasHtml(texto, marcador);

                String nomeProduto = movProdutoVO1.getDescricao();
                if (nomeProduto.equals("")) {
                    nomeProduto = emBranco;
                }

                String quantidade = movProdutoVO1.getQuantidade().toString();
                if (quantidade.equals("0") || quantidade.equals("")) {
                    quantidade = emBranco;
                }

                String valorDesconto = Uteis.getDoubleFormatado(movProdutoVO1.getValorDesconto());
                if (valorDesconto.equals("")) {
                    valorDesconto = emBranco;
                }

                String valorUnitario = Uteis.getDoubleFormatado(movProdutoVO1.getPrecoUnitario());
                if (valorUnitario.equals("")) {
                    valorUnitario = emBranco;
                }


                String valorTotal = Uteis.getDoubleFormatado(movProdutoVO1.getTotalFinal());
                if (valorTotal.equals("")) {
                    valorTotal = emBranco;
                }

                tagGeral = marcador.getTag();
                while ((texto.contains(tagGeral)) && !tagGeral.equals("")) {
                    int tamanho = obterTamanhoTag(tagGeral);
                    String textoTag = obterTextoTag(tagGeral);
                    String mar = obterTagSemTextoSemTamanhoQuandoConcatenadaAOutraTag(tagGeral);

                    if (mar.contains("NomeProduto_Contrato")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, nomeProduto, textoTag, tamanho);
                    } else if (mar.contains("QtdProduto_Contrato")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, quantidade, textoTag, tamanho);
                    } else if (mar.contains("ValorProduto_Contrato")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, valorTotal, textoTag, tamanho);
                    } else if (mar.contains("ValorDescontoProduto_Contrato")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, valorDesconto, textoTag, tamanho);
                    } else if (mar.contains("ValorUnitarioProduto_Contrato")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, valorUnitario, textoTag, tamanho);
                    } else {
                        return ("<br>Tag " + mar + " não existe<br>");
                    }
                }
            }
            texto = retirarLinhasHtml(texto, marcador.getTag());
        }
        return texto;
    }

    public String substituirTagContratoEmBranco(String texto, MarcadorVO marcador, boolean site) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());
            if (mar.equalsIgnoreCase("Codigo_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaDe_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaAte_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaAteAjustada_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorBaseCalculo_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorFinal_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Observacao_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Duracao_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Horario_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NomeModalidades_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NomeCompletoModalidades_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NrVezesNomeCompletoModalidades_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ConsultorResponsavel_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("DtLancamento_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("DtLancamento_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorPorExtenso_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorMensal_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ConvenioDescontoResumo_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorTotalSemDesconto_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorTotalDescontoContrato_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorTotalContratoSemDescontoExtra_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorMensalAdequado_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("valorDescontoExtra_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorMatricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorCheioMatricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorCheioRematricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorDescontoMatricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorDescontoRematricula_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorDescontoAnuidade_Contrato")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if(site){
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            }else{
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            }
        }
        return texto;
    }

    private String obterAutorizacoesCobrancaTaxaPersonal(ColaboradorVO colaborador) throws Exception {
        StringBuilder autorizacoes = new StringBuilder();
        if (!colaborador.getAutorizacoes().isEmpty()) {
            for (AutorizacaoCobrancaColaboradorVO obj : colaborador.getAutorizacoes()) {
                autorizacoes.append(obj.getDescricaoObjeto());
            }
        }
        return autorizacoes.toString();
    }

    private String obterAutorizacoesCobrancaCliente(ClienteVO cliente) throws Exception {
        StringBuilder autorizacoes = new StringBuilder();
        if (!cliente.getAutorizacoes().isEmpty()) {
            for (AutorizacaoCobrancaClienteVO obj : cliente.getAutorizacoes()) {
                autorizacoes.append(obj.getDescricaoObjeto());
            }
        }
        return autorizacoes.toString();
    }

    private String obterGruposCliente(ClienteVO cliente) throws Exception {
        String grupos = "";
        if (cliente.getClienteGrupoVOs().size() > 0) {
            for (Object obj : cliente.getClienteGrupoVOs()) {
                ClienteGrupoVO cg = (ClienteGrupoVO) obj;
                grupos += ", " + cg.getGrupo().getDescricao();
            }
            grupos = grupos.replaceFirst(",", "");
        }
        return grupos;
    }

    private String obterGruposCliente(Integer pessoa, Connection con) throws Exception {
        ClienteVO cliente = new ClienteVO();
        ClienteGrupo cgDao = new ClienteGrupo(con);
        cliente.setClienteGrupoVOs(cgDao.consultarClienteGruposPorPessoa(pessoa, Uteis.NIVELMONTARDADOS_MINIMOS));
        String grupos = "";
        if (cliente.getClienteGrupoVOs().size() > 0) {
            for (Object obj : cliente.getClienteGrupoVOs()) {
                ClienteGrupoVO cg = (ClienteGrupoVO) obj;
                grupos += ", " + cg.getGrupo().getDescricao();
            }
            grupos = grupos.replaceFirst(",", "");
        }
        return grupos;
    }

    public String substituirTagPlanoTaxaPersonal(String texto, ControleTaxaPersonalVO controleTaxaPersonal, MarcadorVO marcador, Connection con) throws Exception {
        Plano planoDao = new Plano(con);
        PlanoVO plano = planoDao.consultarPorChavePrimaria(controleTaxaPersonal.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String codigo = controleTaxaPersonal.getPlano().getCodigo().toString();
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }
        String descricao = plano.getDescricao();
        if (descricao.equals("0") || descricao.equals("")) {
            descricao = emBranco;
        }
        String vigenciaAte = plano.getVigenciaAte_Apresentar();
        if (vigenciaAte.equals("0") || vigenciaAte.equals("")) {
            vigenciaAte = emBranco;
        }
        String vigenciaDe = plano.getVigenciaDe_Apresentar();
        if (vigenciaDe.equals("0") || vigenciaDe.equals("")) {
            vigenciaDe = emBranco;
        }
        String ingressoAte = plano.getIngressoAte_Apresentar();
        if (ingressoAte.equals("0") || ingressoAte.equals("")) {
            ingressoAte = emBranco;
        }

        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());
            if (mar.equalsIgnoreCase("Codigo_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Descricao_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), descricao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaDe_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), vigenciaDe, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaAte_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), vigenciaAte, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("IngressoAte_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), ingressoAte, textoTag, tamanho);
            } else {
                return ("<br>Tag " + mar + " não existe<br>");
            }
        }
        return texto;
    }

    public String substituirTagPlano(String texto, ContratoVO contrato, MarcadorVO marcador, Connection con) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String codigo = "";
        String descricao = "";
        String vigenciaAte = "";
        String vigenciaDe = "";
        String ingressoAte = "";
        if (contrato != null &&
                !UteisValidacao.emptyNumber(contrato.getPlano().getCodigo())) {
            Plano planoDao = new Plano(con);
            PlanoVO plano = planoDao.consultarPorChavePrimaria(contrato.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            planoDao = null;
            codigo = contrato.getPlano().getCodigo().toString();
            descricao = plano.getDescricao();
            vigenciaAte = plano.getVigenciaAte_Apresentar();
            vigenciaDe = plano.getVigenciaDe_Apresentar();
            ingressoAte = plano.getIngressoAte_Apresentar();
        }

        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }
        if (descricao.equals("0") || descricao.equals("")) {
            descricao = emBranco;
        }
        if (vigenciaAte.equals("0") || vigenciaAte.equals("")) {
            vigenciaAte = emBranco;
        }
        if (vigenciaDe.equals("0") || vigenciaDe.equals("")) {
            vigenciaDe = emBranco;
        }
        if (ingressoAte.equals("0") || ingressoAte.equals("")) {
            ingressoAte = emBranco;
        }

        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());
            if (mar.equalsIgnoreCase("Codigo_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Descricao_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), descricao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaDe_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), vigenciaDe, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaAte_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), vigenciaAte, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("IngressoAte_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), ingressoAte, textoTag, tamanho);
            } else {
                return ("<br>Tag " + mar + " não existe<br>");
            }
        }
        return texto;
    }

    public String substituirTagPlanoEmBranco(String texto, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());
            if (mar.equalsIgnoreCase("Codigo_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Descricao_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaDe_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("VigenciaAte_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("IngressoAte_Plano")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            }
        }
        return texto;
    }

    public String substituirTagUsuarioTaxaPersonal(String key, String texto, ControleTaxaPersonalVO controleTaxaPersonal, VendaAvulsaVO venda, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String codigo = "";
        String nome = "";
        String username = "";
        if (controleTaxaPersonal != null) {
            codigo = controleTaxaPersonal.getResponsavel().getCodigo().toString();
            nome = controleTaxaPersonal.getResponsavel().getNome();
            username = controleTaxaPersonal.getResponsavel().getUsername();
        } else {
            codigo = venda.getResponsavel().getCodigo().toString();
            nome = venda.getResponsavel().getNome();
            username = venda.getResponsavel().getUsername();
        }
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }
        if (nome.equals("0") || nome.equals("")) {
            nome = emBranco;
        }
        if (username.equals("0") || username.equals("")) {
            username = emBranco;
        }

        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_Usuario")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_Usuario")) {
                texto = substituirTag(texto, marcador.getTag(), nome, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Username_Usuario")) {
                texto = substituirTag(texto, marcador.getTag(), username, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Usuario_Assinatura")) {
                texto = substituirTag(texto, marcador.getTag(), "__4ssin4tur4digit4lUsu4rio__", textoTag, "__4ssin4tur4digit4lUsu4rio__".length());

                String urlObject ="";
                try {
                    urlObject = MidiaService.getInstance().downloadObject(key, MidiaEntidadeEnum.ASSINATURA_USUARIO, codigo);
                } catch (Exception e) {
                    Uteis.logar(null, "ASSINATURA_USUARIO não encontrada no Mídia Server");
                }

                if (UteisValidacao.emptyString(urlObject)) {
                    texto = texto.replace("__4ssin4tur4digit4lUsu4rio__", "");
                } else {
                    String urlImagemAssinatura = UteisValidacao.emptyString(urlObject)
                            ? "" : (Uteis.getPaintFotoDaNuvem(urlObject + "?time=" + System.currentTimeMillis()));
                    String divAssinatura = "<div style=\"width: 20%; text-align: center; margin: 0 auto;\"><img style=\"width: "+tamanho+"%;\" src=\""
                            .concat(urlImagemAssinatura).concat("\"/>");
                    texto = texto.replace("__4ssin4tur4digit4lUsu4rio__", divAssinatura);
                }


            } else {
                return ("<br>Tag " + mar + " não existe<br>");
            }
        }
        return texto;
    }

    public String substituirTagUsuario(String key, String texto, ContratoVO contrato, VendaAvulsaVO venda, AulaAvulsaDiariaVO vendaDiaria, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String codigo = "";
        String nome = "";
        String username = "";
        if (contrato != null) {
            codigo = contrato.getResponsavelContrato().getCodigo().toString();
            nome = contrato.getResponsavelContrato().getNome();
            username = contrato.getResponsavelContrato().getUsername();
        } else {
            if (venda != null) {
                codigo = venda.getResponsavel().getCodigo().toString();
                nome = venda.getResponsavel().getNome();
                username = venda.getResponsavel().getUsername();
            } else {
                codigo = vendaDiaria.getResponsavel().getCodigo().toString();
                nome = vendaDiaria.getResponsavel().getNome();
                username = vendaDiaria.getResponsavel().getUsername();
            }
        }
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }
        if (nome.equals("0") || nome.equals("")) {
            nome = emBranco;
        }
        if (username.equals("0") || username.equals("")) {
            username = emBranco;
        }

        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_Usuario")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_Usuario")) {
                texto = substituirTag(texto, marcador.getTag(), nome, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Username_Usuario")) {
                texto = substituirTag(texto, marcador.getTag(), username, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Usuario_Assinatura")) {
                texto = substituirTag(texto, marcador.getTag(), "__4ssin4tur4digit4lUsu4rio__", textoTag, "__4ssin4tur4digit4lUsu4rio__".length());

                String urlObject ="";
                try {
                    urlObject = MidiaService.getInstance().downloadObject(key, MidiaEntidadeEnum.ASSINATURA_USUARIO, codigo);
                } catch (Exception e) {
                    Uteis.logar(null, "ASSINATURA_USUARIO não encontrada no Mídia Server");
                }

                if (UteisValidacao.emptyString(urlObject)) {
                    texto = texto.replace("__4ssin4tur4digit4lUsu4rio__", "");
                } else {
                    String urlImagemAssinatura = UteisValidacao.emptyString(urlObject)
                            ? "" : (Uteis.getPaintFotoDaNuvem(urlObject + "?time=" + System.currentTimeMillis()));
                    String divAssinatura = "<div style=\"width: 20%; text-align: center; margin: 0 auto;\"><img style=\"width: "+tamanho+"%;\" src=\""
                            .concat(urlImagemAssinatura).concat("\"/>");
                    texto = texto.replace("__4ssin4tur4digit4lUsu4rio__", divAssinatura);
                }


            } else {
                return ("<br>Tag " + mar + " não existe<br>");
            }
        }
        return texto;
    }

    public String substituirTagVendaAvulsa(String texto, VendaAvulsaVO venda, MarcadorVO marcador) throws Exception {
        if (venda == null) {
            return texto;
        }
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String dataVenda = venda.getDataRegistro_Apresentar();
        if (dataVenda.equals("0") || dataVenda.equals("")) {
            dataVenda = emBranco;
        }
        String valorTotal = Uteis.getDoubleFormatado(venda.getValorTotal());
        if (valorTotal.equals("0.0") || valorTotal.equals("")) {
            valorTotal = emBranco;
        }
        String valorFinal = Uteis.getDoubleFormatado(venda.getParcela().getValorParcela());
        if (valorFinal.equals("0.0") || valorFinal.equals("")) {
            valorFinal  = emBranco;
        }

        String valorDesconto = Uteis.getDoubleFormatado(venda.getValorTotal() - venda.getParcela().getValorParcela());
        if (valorFinal.equals("0.0") || valorFinal.equals("")) {
            valorFinal  = emBranco;
        }
        String codigo = venda.getCodigo().toString();
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }

        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorTotal_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), valorTotal, textoTag, tamanho);
             } else if (mar.equalsIgnoreCase("ValorFinal_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), valorFinal, textoTag, tamanho);
             } else if (mar.equalsIgnoreCase("ValorDesconto_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), valorDesconto, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Data_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), dataVenda, textoTag, tamanho);
            } else {
                return ("<br>Tag " + mar + " não existe<br>");
            }
        }
        return texto;
    }

    public String substituirTagVendaAulaAvulsaDiaria(String texto, AulaAvulsaDiariaVO vendaDiaria, MarcadorVO marcador) throws Exception {
        if (vendaDiaria == null) {
            return texto;
        }
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String dataVenda = vendaDiaria.getDataRegistro_Apresentar();
        if (dataVenda.equals("0") || dataVenda.equals("")) {
            dataVenda = emBranco;
        }
        String valorTotal = Uteis.getDoubleFormatado(vendaDiaria.getValor());
        if (valorTotal.equals("0.0") || valorTotal.equals("")) {
            valorTotal = emBranco;
        }
        String valorFinal = Uteis.getDoubleFormatado(vendaDiaria.getParcela().getValorParcela());
        if (valorFinal.equals("0.0") || valorFinal.equals("")) {
            valorFinal  = emBranco;
        }

        String valorDesconto = Uteis.getDoubleFormatado(vendaDiaria.getValor() - vendaDiaria.getParcela().getValorParcela());
        if (valorFinal.equals("0.0") || valorFinal.equals("")) {
            valorFinal  = emBranco;
        }
        String codigo = vendaDiaria.getCodigo().toString();
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }

        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorTotal_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), valorTotal, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorFinal_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), valorFinal, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ValorDesconto_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), valorDesconto, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Data_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), dataVenda, textoTag, tamanho);
            } else {
                return ("<br>Tag " + mar + " não existe<br>");
            }
        }
        return texto;
    }

    public String substituirTagVendaAvulsaEmBranco(String texto, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Valor_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Data_Venda")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            }
        }
        return texto;
    }

    public String substituirTagItensVenda(String texto, VendaAvulsaVO venda, MarcadorVO marcador) throws Exception {
        if (venda == null) {
            return texto;
        }
        String emBranco = obterStringEmBrancoParaTag(marcador);
        tagGeral = marcador.getTag();
        if (venda.getItemVendaAvulsaVOs().size() == 0) {
            texto = texto.replace(marcador.getTag(), emBranco);
        } else {
            Iterator i = venda.getItemVendaAvulsaVOs().iterator();
            while (i.hasNext()) {
                texto = gerarLinhasHtml(texto, marcador);
                ItemVendaAvulsaVO itemVenda = (ItemVendaAvulsaVO) i.next();
                Iterator j = venda.getMovProdutoVOs().iterator();
                 while (j.hasNext()) {
                     MovProdutoVO produto = (MovProdutoVO) j.next();
                     if (produto.getProduto().getCodigo().equals(itemVenda.getProduto().getCodigo()) && produto.getQuantidade().equals(itemVenda.getQuantidade())){
                        String codigo = produto.getProduto().getCodigo().toString();
                        if (codigo.equals("0") || codigo.equals("")) {
                            codigo = emBranco;
                        }
                        String descricao = itemVenda.getProduto().getDescricao();
                        if (descricao.equals("")) {
                            descricao = emBranco;
                        }

                        String quantidade = itemVenda.getQuantidade().toString();
                        if (quantidade.equals("") || quantidade.equals("0")) {
                            quantidade = emBranco;
                        }
                        String valorUnitario = "0.0";
                        String valorDesconto = "0.0";
                        String valorFinal = Uteis.getDoubleFormatado(produto.getTotalFinal());
                        if(itemVenda.getPacoteVO() != null && itemVenda.getPacoteVO().getCodigo() > 0 ){
                           valorUnitario = Uteis.getDoubleFormatado(itemVenda.getValorParcial());
                           valorDesconto = Uteis.getDoubleFormatado(itemVenda.getValorParcial() - produto.getTotalFinal());

                         } else {
                           valorUnitario = Uteis.getDoubleFormatado(produto.getPrecoUnitario());
                           valorDesconto = Uteis.getDoubleFormatado(produto.getValorDesconto());
                         }
                         if (valorUnitario.equals("")) {
                                valorUnitario = emBranco;

                          }
                         if (valorFinal.equals("")) {
                                valorFinal = emBranco;

                          }
                         if (valorDesconto.equals("")) {
                                valorUnitario = emBranco;

                          }

                        tagGeral = marcador.getTag();

                        while ((texto.indexOf(tagGeral) != -1) && !tagGeral.equals("")) {
                            int tamanho = obterTamanhoTag(tagGeral);
                            String textoTag = obterTextoTag(tagGeral);
                            String mar = obterTagSemTextoSemTamanhoQuandoConcatenadaAOutraTag(tagGeral);

                            if (mar.contains("Codigo_Itens")) {
                                texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, codigo, textoTag, tamanho);
                            } else if (mar.contains("Quantidade_Itens")) {
                                texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, quantidade, textoTag, tamanho);
                            } else if (mar.contains("ValorUnitario_Itens")) {
                                texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, valorUnitario, textoTag, tamanho);
                            } else if (mar.contains("ValorDesconto_Itens")) {
                                texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, valorDesconto, textoTag, tamanho);
                            } else if (mar.contains("ValorFinal_Itens")) {
                                texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, valorFinal, textoTag, tamanho);
                            } else if (mar.contains("Descricao_Itens")) {
                                texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, descricao, textoTag, tamanho);
                            } else {
                                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
                            }
                        }
                    }
                }
            }
            texto = retirarLinhasHtml(texto, marcador.getTag());
        }
        return texto;
    }

    public String substituirTagPacoteVenda(String texto, VendaAvulsaVO venda, MarcadorVO marcador) throws Exception {
        if (venda == null) {
            return texto;
        }
        String emBranco = obterStringEmBrancoParaTag(marcador);

        if (venda.getItemVendaAvulsaVOs().size() == 0) {
            texto = texto.replace(marcador.getTag(), emBranco);
        } else {

            Iterator i = venda.getItemVendaAvulsaVOs().iterator();
            ItemVendaAvulsaVO itemVenda = (ItemVendaAvulsaVO) i.next();
            if (itemVenda.getPacoteVO() == null || itemVenda.getPacoteVO().getCodigo() == 0) {
                texto = texto.replace(marcador.getTag(), emBranco);
            } else {


                String descricao = itemVenda.getPacoteVO().getTitulo();
                if (descricao.equals("")) {
                    descricao = emBranco;
                }
                String valorPacote = Uteis.getDoubleFormatado(itemVenda.getPacoteVO().getValorTotal());
                if (valorPacote.equals("0.0") || valorPacote.equals("")) {
                    valorPacote = emBranco;
                }
                String codigo = itemVenda.getPacoteVO().toString();
                if (codigo.equals("0") || codigo.equals("")) {
                    codigo = emBranco;
                }

                while (texto.indexOf(marcador.getTag()) != -1) {
                    int tamanho = obterTamanhoTag(marcador.getTag());
                    String mar = obterTagSemTextoSemTamanho(marcador.getTag());
                    String textoTag = obterTextoTag(marcador.getTag());

                    if (mar.equalsIgnoreCase("Codigo_PacoteItem")) {
                        texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
                    } else if (mar.equalsIgnoreCase("Valor_PacoteItem")) {
                        texto = substituirTag(texto, marcador.getTag(), valorPacote, textoTag, tamanho);
                    } else if (mar.equalsIgnoreCase("Descricao_PacoteItem")) {
                        texto = substituirTag(texto, marcador.getTag(), descricao, textoTag, tamanho);
                    } else {
                        texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
                    }
                }
            }
        }
        return texto;
    }

     public String substituirTagPacoteVendaEmBranco(String texto, MarcadorVO marcador) throws Exception {

        String emBranco = obterStringEmBrancoParaTag(marcador);
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_PacoteItem")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Valor_PacoteItem")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Descricao_PacoteItem")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            }
        }

        return texto;
    }

    public String substituirTagUsuarioEmBranco(String texto, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());
            if (mar.equalsIgnoreCase("Codigo_Usuario")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_Usuario")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Username_Usuario")) {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            } else {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            }
        }
        return texto;
    }

    private void replaceLogomarca(final StringBuilder texto) {
        //Pattern imagem = Pattern.compile("<\\s*[iI][mM][gG][^>]*[sS][rR][cC]\\s*=\\s*[\\\"']{0,1}([^\\\"'\\s>]*)");
        //reconhece tags
        //</?(?:img|p|h[1-9]|em)+((\s+\w+(\s*=\s*(?:".*?"|'.*?'|[^'">\s]+))?)+\s*|\s*)/?>
        String pattern = "</?(?:img)+((\\s+\\w+(\\s*=\\s*(?:\".*?\"|'.*?'|[^'\">\\s]+))?)+\\s*|\\s*)/?>";
        Pattern imagem = Pattern.compile(pattern);
        Matcher matcher = imagem.matcher(texto.toString());

        while (matcher.find()) {
            String img = matcher.group();
            StringBuilder newImg = new StringBuilder();
            newImg.append("<img style=\"width:200px;height:56px;border:none;\" src=\"acesso?emp\"/>");
            StringUtilities.substituirTodos(texto, img, newImg.toString());
        }

    }

    public String substituirTagEmpresaTaxaPersonal(String key, String texto, ControleTaxaPersonalVO controleTaxaPersonal, VendaAvulsaVO venda, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String codigo = "";
        String fax = "";
        String site = "";
        String email = "";
        String telComercial1 = "";
        String telComercial2 = "";
        String telComercial3 = "";
        String inscEstadual = "";
        String cnpj = "";
        String cep = "";
        String complemento = "";
        String numero = "";
        String setor = "";
        String endereco = "";
        String razaoSocial = "";
        String nome = "";
        String cidade = "";
        String estado = "";
        if (controleTaxaPersonal != null) {
            codigo = controleTaxaPersonal.getEmpresa().getCodigo().toString();
            fax = controleTaxaPersonal.getEmpresa().getFax();
            site = controleTaxaPersonal.getEmpresa().getSite();
            email = controleTaxaPersonal.getEmpresa().getEmail();
            telComercial1 = controleTaxaPersonal.getEmpresa().getTelComercial1();
            telComercial2 = controleTaxaPersonal.getEmpresa().getTelComercial2();
            telComercial3 = controleTaxaPersonal.getEmpresa().getTelComercial3();
            inscEstadual = controleTaxaPersonal.getEmpresa().getInscEstadual();
            cnpj = controleTaxaPersonal.getEmpresa().getCNPJ();
            cep = controleTaxaPersonal.getEmpresa().getCEP();
            complemento = controleTaxaPersonal.getEmpresa().getComplemento();
            numero = controleTaxaPersonal.getEmpresa().getNumero();
            setor = controleTaxaPersonal.getEmpresa().getSetor();
            endereco = controleTaxaPersonal.getEmpresa().getEndereco();
            razaoSocial = controleTaxaPersonal.getEmpresa().getRazaoSocial();
            nome = controleTaxaPersonal.getEmpresa().getNome();
            cidade = controleTaxaPersonal.getEmpresa().getCidade().getNome();
            estado = controleTaxaPersonal.getEmpresa().getEstado().getDescricao();
        } else {
            codigo = venda.getEmpresa().getCodigo().toString();
            fax = venda.getEmpresa().getFax();
            site = venda.getEmpresa().getSite();
            email = venda.getEmpresa().getEmail();
            telComercial1 = venda.getEmpresa().getTelComercial1();
            telComercial2 = venda.getEmpresa().getTelComercial2();
            telComercial3 = venda.getEmpresa().getTelComercial3();
            inscEstadual = venda.getEmpresa().getInscEstadual();
            cnpj = venda.getEmpresa().getCNPJ();
            cep = venda.getEmpresa().getCEP();
            complemento = venda.getEmpresa().getComplemento();
            numero = venda.getEmpresa().getNumero();
            setor = venda.getEmpresa().getSetor();
            endereco = venda.getEmpresa().getEndereco();
            razaoSocial = venda.getEmpresa().getRazaoSocial();
            nome = venda.getEmpresa().getNome();
            cidade = venda.getEmpresa().getCidade().getNome();
            estado = venda.getEmpresa().getEstado().getDescricao();
        }
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }
        if (fax.equals("0") || fax.equals("")) {
            fax = emBranco;
        }
        if (site.equals("0") || site.equals("")) {
            site = emBranco;
        }
        if (email.equals("0") || email.equals("")) {
            email = emBranco;
        }
        if (telComercial1.equals("0") || telComercial1.equals("")) {
            telComercial1 = emBranco;
        }
        if (telComercial2.equals("0") || telComercial2.equals("")) {
            telComercial2 = emBranco;
        }
        if (telComercial3.equals("0") || telComercial3.equals("")) {
            telComercial3 = emBranco;
        }
        if (inscEstadual.equals("0") || inscEstadual.equals("")) {
            inscEstadual = emBranco;
        }
        if (cnpj.equals("0") || cnpj.equals("")) {
            cnpj = emBranco;
        }
        if (cep.equals("0") || cep.equals("")) {
            cep = emBranco;
        }
        if (complemento.equals("0") || complemento.equals("")) {
            complemento = emBranco;
        }
        if (numero.equals("0") || numero.equals("")) {
            numero = emBranco;
        }
        if (setor.equals("0") || setor.equals("")) {
            setor = emBranco;
        }
        if (endereco.equals("0") || endereco.equals("")) {
            endereco = emBranco;
        }
        if (razaoSocial.equals("0") || razaoSocial.equals("")) {
            razaoSocial = emBranco;
        }
        if (nome.equals("0") || nome.equals("")) {
            nome = emBranco;
        }
        if (cidade.equals("0") || cidade.equals("")) {
            cidade = emBranco;
        }
        if (estado.equals("0") || estado.equals("")) {
            estado = emBranco;
        }
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Assinatura_Empresa")) {

                texto = substituirTag(texto, marcador.getTag(), "__4ssin4tur4digit4lEmpres4__", textoTag, "__4ssin4tur4digit4lEmpres4__".length());

                String urlObject ="";
                try {
                    urlObject = MidiaService.getInstance().downloadObject(key, MidiaEntidadeEnum.ASSINATURA_EMPRESA, codigo);
                } catch (Exception e) {
                    Uteis.logar(null, "ASSINATURA_EMPRESA não encontrada no Mídia Server");
                }

                if (UteisValidacao.emptyString(urlObject)) {
                    texto = texto.replace("__4ssin4tur4digit4lEmpres4__", "");
                } else {
                    String urlImagemAssinatura = UteisValidacao.emptyString(urlObject)
                            ? "" : (Uteis.getPaintFotoDaNuvem(urlObject + "?time=" + System.currentTimeMillis()));
                    String divAssinatura = "<div style=\"width: 20%; text-align: center; margin: 0 auto;\"><img style=\"width: "+tamanho+"%;\" src=\""
                            .concat(urlImagemAssinatura).concat("\"/>");
                    texto = texto.replace("__4ssin4tur4digit4lEmpres4__", divAssinatura);
                }

            } else if (mar.equalsIgnoreCase("Fax_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), fax, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Site_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), site, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Email_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), email, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("TelComercial1_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), telComercial1, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("TelComercial2_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), telComercial2, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("TelComercial3_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), telComercial3, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("InscEstadual_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), inscEstadual, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cnpj_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), cnpj, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cep_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), cep, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Complemento_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), complemento, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Numero_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), numero, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Setor_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), setor, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), endereco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("RazaoSocial_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), razaoSocial, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), nome, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cidade_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), cidade, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Estado_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), estado, textoTag, tamanho);
            } else {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            }
        }
        return texto;
    }

    public String substituirTagEmpresa(String key, String texto, ContratoVO contrato, VendaAvulsaVO venda, AulaAvulsaDiariaVO vendaDiaria, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String codigo = "";
        String fax = "";
        String site = "";
        String email = "";
        String telComercial1 = "";
        String telComercial2 = "";
        String telComercial3 = "";
        String inscEstadual = "";
        String cnpj = "";
        String cep = "";
        String complemento = "";
        String numero = "";
        String setor = "";
        String endereco = "";
        String razaoSocial = "";
        String nome = "";
        String cidade = "";
        String estado = "";
        if (contrato != null) {
            codigo = contrato.getEmpresa().getCodigo().toString();
            fax = contrato.getEmpresa().getFax();
            site = contrato.getEmpresa().getSite();
            email = contrato.getEmpresa().getEmail();
            telComercial1 = contrato.getEmpresa().getTelComercial1();
            telComercial2 = contrato.getEmpresa().getTelComercial2();
            telComercial3 = contrato.getEmpresa().getTelComercial3();
            inscEstadual = contrato.getEmpresa().getInscEstadual();
            cnpj = contrato.getEmpresa().getCNPJ();
            cep = contrato.getEmpresa().getCEP();
            complemento = contrato.getEmpresa().getComplemento();
            numero = contrato.getEmpresa().getNumero();
            setor = contrato.getEmpresa().getSetor();
            endereco = contrato.getEmpresa().getEndereco();
            razaoSocial = contrato.getEmpresa().getRazaoSocial();
            nome = contrato.getEmpresa().getNome();
            cidade = contrato.getEmpresa().getCidade().getNome();
            estado = contrato.getEmpresa().getEstado().getDescricao();
        } else if(venda != null){
            codigo = venda.getEmpresa().getCodigo().toString();
            fax = venda.getEmpresa().getFax();
            site = venda.getEmpresa().getSite();
            email = venda.getEmpresa().getEmail();
            telComercial1 = venda.getEmpresa().getTelComercial1();
            telComercial2 = venda.getEmpresa().getTelComercial2();
            telComercial3 = venda.getEmpresa().getTelComercial3();
            inscEstadual = venda.getEmpresa().getInscEstadual();
            cnpj = venda.getEmpresa().getCNPJ();
            cep = venda.getEmpresa().getCEP();
            complemento = venda.getEmpresa().getComplemento();
            numero = venda.getEmpresa().getNumero();
            setor = venda.getEmpresa().getSetor();
            endereco = venda.getEmpresa().getEndereco();
            razaoSocial = venda.getEmpresa().getRazaoSocial();
            nome = venda.getEmpresa().getNome();
            cidade = venda.getEmpresa().getCidade().getNome();
            estado = venda.getEmpresa().getEstado().getDescricao();
        } else {
            codigo = vendaDiaria.getEmpresa().getCodigo().toString();
            fax = vendaDiaria.getEmpresa().getFax();
            site = vendaDiaria.getEmpresa().getSite();
            email = vendaDiaria.getEmpresa().getEmail();
            telComercial1 = vendaDiaria.getEmpresa().getTelComercial1();
            telComercial2 = vendaDiaria.getEmpresa().getTelComercial2();
            telComercial3 = vendaDiaria.getEmpresa().getTelComercial3();
            inscEstadual = vendaDiaria.getEmpresa().getInscEstadual();
            cnpj = vendaDiaria.getEmpresa().getCNPJ();
            cep = vendaDiaria.getEmpresa().getCEP();
            complemento = vendaDiaria.getEmpresa().getComplemento();
            numero = vendaDiaria.getEmpresa().getNumero();
            setor = vendaDiaria.getEmpresa().getSetor();
            endereco = vendaDiaria.getEmpresa().getEndereco();
            razaoSocial = vendaDiaria.getEmpresa().getRazaoSocial();
            nome = vendaDiaria.getEmpresa().getNome();
            cidade = vendaDiaria.getEmpresa().getCidade().getNome();
            estado = vendaDiaria.getEmpresa().getEstado().getDescricao();
        }
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }
        if (fax.equals("0") || fax.equals("")) {
            fax = emBranco;
        }
        if (site.equals("0") || site.equals("")) {
            site = emBranco;
        }
        if (email.equals("0") || email.equals("")) {
            email = emBranco;
        }
        if (telComercial1.equals("0") || telComercial1.equals("")) {
            telComercial1 = emBranco;
        }
        if (telComercial2.equals("0") || telComercial2.equals("")) {
            telComercial2 = emBranco;
        }
        if (telComercial3.equals("0") || telComercial3.equals("")) {
            telComercial3 = emBranco;
        }
        if (inscEstadual.equals("0") || inscEstadual.equals("")) {
            inscEstadual = emBranco;
        }
        if (cnpj.equals("0") || cnpj.equals("")) {
            cnpj = emBranco;
        }
        if (cep.equals("0") || cep.equals("")) {
            cep = emBranco;
        }
        if (complemento.equals("0") || complemento.equals("")) {
            complemento = emBranco;
        }
        if (numero.equals("0") || numero.equals("")) {
            numero = emBranco;
        }
        if (setor.equals("0") || setor.equals("")) {
            setor = emBranco;
        }
        if (endereco.equals("0") || endereco.equals("")) {
            endereco = emBranco;
        }
        if (razaoSocial.equals("0") || razaoSocial.equals("")) {
            razaoSocial = emBranco;
        }
        if (nome.equals("0") || nome.equals("")) {
            nome = emBranco;
        }
        if (cidade.equals("0") || cidade.equals("")) {
            cidade = emBranco;
        }
        if (estado.equals("0") || estado.equals("")) {
            estado = emBranco;
        }
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Assinatura_Empresa")) {

                texto = substituirTag(texto, marcador.getTag(), "__4ssin4tur4digit4lEmpres4__", textoTag, "__4ssin4tur4digit4lEmpres4__".length());

                String urlObject ="";
                try {
                    urlObject = MidiaService.getInstance().downloadObject(key, MidiaEntidadeEnum.ASSINATURA_EMPRESA, codigo);
                } catch (Exception e) {
                    Uteis.logar(null, "ASSINATURA_EMPRESA não encontrada no Mídia Server");
                }

                if (UteisValidacao.emptyString(urlObject)) {
                    texto = texto.replace("__4ssin4tur4digit4lEmpres4__", "");
                } else {
                    String urlImagemAssinatura = UteisValidacao.emptyString(urlObject)
                            ? "" : (Uteis.getPaintFotoDaNuvem(urlObject + "?time=" + System.currentTimeMillis()));
                    String divAssinatura = "<div style=\"width: 20%; text-align: center; margin: 0 auto;\"><img style=\"width: "+tamanho+"%;\" src=\""
                            .concat(urlImagemAssinatura).concat("\"/>");
                    texto = texto.replace("__4ssin4tur4digit4lEmpres4__", divAssinatura);
                }

            } else if (mar.equalsIgnoreCase("Fax_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), fax, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Site_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), site, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Email_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), email, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("TelComercial1_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), telComercial1, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("TelComercial2_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), telComercial2, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("TelComercial3_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), telComercial3, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("InscEstadual_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), inscEstadual, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cnpj_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), cnpj, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cep_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), cep, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Complemento_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), complemento, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Numero_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), numero, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Setor_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), setor, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), endereco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("RazaoSocial_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), razaoSocial, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), nome, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cidade_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), cidade, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Estado_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), estado, textoTag, tamanho);
            } else {
                texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
            }
        }
        return texto;
    }

    public String substituirTagEmpresaEmBranco(int codigoEmpresa, String texto, MarcadorVO marcador, Connection con) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        Empresa empDao = new Empresa(con);
        EmpresaVO empresa = empDao.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        String cod = empresa.getCodigo().toString();
        if (cod.equals("0") || cod.equals("")) {
            cod = emBranco;
        }
        String fax = empresa.getFax();
        if (fax.equals("0") || fax.equals("")) {
            fax = emBranco;
        }
        String site = empresa.getSite();
        if (site.equals("0") || site.equals("")) {
            site = emBranco;
        }
        String email = empresa.getEmail();
        if (email.equals("0") || email.equals("")) {
            email = emBranco;
        }
        String telComercial1 = empresa.getTelComercial1();
        if (telComercial1.equals("0") || telComercial1.equals("")) {
            telComercial1 = emBranco;
        }
        String telComercial2 = empresa.getTelComercial2();
        if (telComercial2.equals("0") || telComercial2.equals("")) {
            telComercial2 = emBranco;
        }
        String telComercial3 = empresa.getTelComercial3();
        if (telComercial3.equals("0") || telComercial3.equals("")) {
            telComercial3 = emBranco;
        }
        String inscEstadual = empresa.getInscEstadual();
        if (inscEstadual.equals("0") || inscEstadual.equals("")) {
            inscEstadual = emBranco;
        }
        String cnpj = empresa.getCNPJ();
        if (cnpj.equals("0") || cnpj.equals("")) {
            cnpj = emBranco;
        }
        String cep = empresa.getCEP();
        if (cep.equals("0") || cep.equals("")) {
            cep = emBranco;
        }
        String complemento = empresa.getComplemento();
        if (complemento.equals("0") || complemento.equals("")) {
            complemento = emBranco;
        }
        String numero = empresa.getNumero();
        if (numero.equals("0") || numero.equals("")) {
            numero = emBranco;
        }
        String setor = empresa.getSetor();
        if (setor.equals("0") || setor.equals("")) {
            setor = emBranco;
        }
        String endereco = empresa.getEndereco();
        if (endereco.equals("0") || endereco.equals("")) {
            endereco = emBranco;
        }
        String razaoSocial = empresa.getRazaoSocial();
        if (razaoSocial.equals("0") || razaoSocial.equals("")) {
            razaoSocial = emBranco;
        }
        String nome = empresa.getNome();
        if (nome.equals("0") || nome.equals("")) {
            nome = emBranco;
        }
        String cidade = empresa.getCidade().getNome();
        if (cidade.equals("0") || cidade.equals("")) {
            cidade = emBranco;
        }
        String estado = empresa.getEstado().getDescricao();
        if (estado.equals("0") || estado.equals("")) {
            estado = emBranco;
        }
        StringBuilder sb = new StringBuilder(texto);
        texto = sb.toString();
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), cod, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Fax_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), fax, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Site_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), site, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Email_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), email, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("TelComercial1_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), telComercial1, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("TelComercial2_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), telComercial2, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("TelComercial3_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), telComercial3, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("InscEstadual_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), inscEstadual, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cnpj_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), cnpj, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cep_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), cep, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Complemento_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), complemento, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Numero_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), numero, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Setor_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), setor, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), endereco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("RazaoSocial_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), razaoSocial, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), nome, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cidade_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), cidade, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Estado_Empresa")) {
                texto = substituirTag(texto, marcador.getTag(), estado, textoTag, tamanho);
            } else {
                texto = substituirTag(texto, marcador.getTag(), estado, textoTag, tamanho);
            }
        }
        return texto;
    }

    public String substituirTagClienteTaxaPersonal(String texto, ColaboradorVO colaborador, ControleTaxaPersonalVO controleTaxaPersonal, VendaAvulsaVO venda, MarcadorVO marcador, Connection con) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        String codigo = "";
        String nome = "";
        String dataNasc = "";
        String cpf = "";
        String rg = "";
        String estadoCivil = "";
        String naturalidade = "";
        String sexo = "";
        String webPage = "";
        String telefone = "";
        String contatoEmergencia = "";
        String telefoneEmergencia = "";
        String numeroCartao = "";
        String nomeTitularCartao = "";
        String cpfTitularCartao = "";
        String validadeCartao = "";
        String bandeiraCartao = "";
        //telefone celular
        String telefoneCelular = "";
        //telefone comercial
        String telefoneComercial = "";
        String endereco = "";
        String numeroEndereco = "";
        String cidade = "";
        String estado = "";
        String cep = "";
        String profissao = "";
        String responsavel = "";
        String nowLocationIpVendasOnline = "";
        String conta = "";
        String agencia = "";
        String banco = "";
        String complemento = "";
        String email = "";
        String grupo = "";
        String bairro = "";
        String consultorAtual = "";

        if (controleTaxaPersonal != null) {
            codigo = controleTaxaPersonal.getPersonal().getPessoa().getCodigo().toString();
            nome = controleTaxaPersonal.getPersonal().getPessoa().getNome();
            dataNasc = controleTaxaPersonal.getPersonal().getPessoa().getDataNasc_Apresentar();
            cpf = controleTaxaPersonal.getPersonal().getPessoa().getCfp();
            rg = controleTaxaPersonal.getPersonal().getPessoa().getRg();
            estadoCivil = controleTaxaPersonal.getPersonal().getPessoa().getEstadoCivil_Apresentar();
            naturalidade = controleTaxaPersonal.getPersonal().getPessoa().getNaturalidade();
            sexo = controleTaxaPersonal.getPersonal().getPessoa().getSexo();
            webPage = controleTaxaPersonal.getPersonal().getPessoa().getWebPage();
            telefone = obterTelefoneCliente(controleTaxaPersonal.getPersonal().getPessoa());
            telefoneCelular = obterTelefoneClientePorTipo(controleTaxaPersonal.getPersonal().getPessoa(), "CE");
            telefoneComercial = obterTelefoneClientePorTipo(controleTaxaPersonal.getPersonal().getPessoa(), "CO");
            endereco = obterEnderecoCliente(controleTaxaPersonal.getPersonal().getPessoa());
            numeroEndereco = obterNumeroEnderecoCliente(controleTaxaPersonal.getPersonal().getPessoa());
            cidade = controleTaxaPersonal.getPersonal().getPessoa().getCidade().getNome();
            estado = controleTaxaPersonal.getPersonal().getPessoa().getCidade().getEstado().getDescricao();
            cep = obterCepCliente(controleTaxaPersonal.getPersonal().getPessoa());
            profissao = controleTaxaPersonal.getPersonal().getPessoa().getProfissao().getDescricao();
            responsavel = controleTaxaPersonal.getPersonal().getPessoa().getNomeMae();
            contatoEmergencia = (controleTaxaPersonal.getPersonal().getPessoa().getContatoEmergencia() == null ? "" : controleTaxaPersonal.getPersonal().getPessoa().getContatoEmergencia());
            telefoneEmergencia = (controleTaxaPersonal.getPersonal().getPessoa().getTelefoneEmergencia() == null ? "" : controleTaxaPersonal.getPersonal().getPessoa().getTelefoneEmergencia());
            if (venda != null && !UteisValidacao.emptyNumber(venda.getCodigo())) {
                Cliente cliDao = new Cliente(con);
                NowLocationIpVendaDTO nowLocationIpVendaDTO = cliDao.consultarUltimoIpLocalizacaoCliente(venda.getCliente().getCodigo());
                if (nowLocationIpVendaDTO != null) {
                    nowLocationIpVendasOnline = nowLocationIpVendaDTO.getData() + " - " +
                            nowLocationIpVendaDTO.getCidade() + "/" + nowLocationIpVendaDTO.getEstado() + " - (" +
                            nowLocationIpVendaDTO.getLocalizacao() + ") - " + nowLocationIpVendaDTO.getIp();
                }
                cliDao = null;
            }
            complemento = obterComplemento(controleTaxaPersonal.getPersonal().getPessoa());
            bairro = obterBairro(controleTaxaPersonal.getPersonal().getPessoa());
            email = obterEmailCliente(controleTaxaPersonal.getPersonal().getPessoa());
            grupo = colaborador.getGrupoEmQueFoiSelecionado().getDescricao();

            for (int i = 0; i <  colaborador.getAutorizacoes().size(); i++) {
                TipoAutorizacaoCobrancaEnum tipo = colaborador.getAutorizacoes().get(i).getTipoAutorizacao();
                if (tipo == TipoAutorizacaoCobrancaEnum.CARTAOCREDITO && colaborador.getAutorizacoes().get(i).getNomeTitularCartao() != null) {
                    nomeTitularCartao = colaborador.getAutorizacoes().get(i).getNomeTitularCartao();
                    numeroCartao = colaborador.getAutorizacoes().get(i).getNumeroCartao() == null ? "" : colaborador.getAutorizacoes().get(i).getNumeroCartao();
                    cpfTitularCartao = colaborador.getAutorizacoes().get(i).getCpfTitular() == null ? "" : colaborador.getAutorizacoes().get(i).getCpfTitular();
                    validadeCartao = colaborador.getAutorizacoes().get(i).getValidadeCartao() == null ? "" : colaborador.getAutorizacoes().get(i).getValidadeCartao();
                    bandeiraCartao = colaborador.getAutorizacoes().get(i).getImagemBandeira() == null ? "" : colaborador.getAutorizacoes().get(i).getImagemBandeira();
                    break;
                } else {
                    //SE ENTRAR NO ELSE
                    //SETAR VAZIO PORQUE A AUTORIZAÇÃO DE COBRANÇA JÁ FOI IMPORTADA
                    nomeTitularCartao = "";
                    numeroCartao = "";
                    cpfTitularCartao = "";
                    validadeCartao = "";
                    bandeiraCartao = "";
                }
            }
            if (dataNasc.equals("0") || dataNasc.equals("")) {
                dataNasc = emBranco;
            }

        } else {
            codigo = venda.getParcela().getPessoa().getCodigo().toString();
            nome = venda.getParcela().getPessoa().getNome();
            dataNasc = venda.getParcela().getPessoa().getDataNasc_Apresentar();
            cpf = venda.getParcela().getPessoa().getCfp();
            rg = venda.getParcela().getPessoa().getRg();
            estadoCivil = venda.getParcela().getPessoa().getEstadoCivil_Apresentar();
            naturalidade = venda.getParcela().getPessoa().getNaturalidade();
            sexo = venda.getParcela().getPessoa().getSexo();
            webPage = venda.getParcela().getPessoa().getWebPage();
            telefone = obterTelefoneCliente(venda.getParcela().getPessoa());
            telefoneCelular = obterTelefoneClientePorTipo(venda.getParcela().getPessoa(), "CE");
            telefoneComercial = obterTelefoneClientePorTipo(venda.getParcela().getPessoa(), "CO");
            endereco = obterEnderecoCliente(venda.getParcela().getPessoa());
            numeroEndereco = obterNumeroEnderecoCliente(venda.getParcela().getPessoa());
            cidade = venda.getParcela().getPessoa().getCidade().getNome();
            estado = venda.getParcela().getPessoa().getCidade().getEstado().getDescricao();
            cep = obterCepCliente(venda.getParcela().getPessoa());
            profissao = venda.getParcela().getPessoa().getProfissao().getDescricao();
            responsavel = venda.getParcela().getPessoa().getNomeMae();
            Colaborador colabDao = new Colaborador(con);
            colaborador = colabDao.consultarPorCodigoPessoa(venda.getParcela().getPessoa().getCodigo(), venda.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            colabDao = null;
            Cliente cliDao = new Cliente(con);
            NowLocationIpVendaDTO nowLocationIpVendaDTO = cliDao.consultarUltimoIpLocalizacaoCliente(venda.getCliente().getCodigo());
            if (nowLocationIpVendaDTO != null) {
                nowLocationIpVendasOnline = nowLocationIpVendaDTO.getData() + " - " +
                        nowLocationIpVendaDTO.getCidade() + "/" + nowLocationIpVendaDTO.getEstado() + " - (" +
                        nowLocationIpVendaDTO.getLocalizacao() + ") - " + nowLocationIpVendaDTO.getIp();
            }
            cliDao = null;
            complemento = obterComplemento(venda.getParcela().getPessoa());
            bairro = obterBairro(venda.getParcela().getPessoa());
            email = obterEmailCliente(venda.getParcela().getPessoa());
            grupo = obterGruposCliente(venda.getParcela().getPessoa().getCodigo(), con);
            if (dataNasc.equals("0") || dataNasc.equals("")) {
                dataNasc = emBranco;
            }
        }
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }
        if (nome.equals("0") || nome.equals("")) {
            nome = emBranco;
        }
        if (cpf.equals("0") || cpf.equals("")) {
            cpf = emBranco;
        }
        if (rg.equals("0") || rg.equals("")) {
            rg = emBranco;
        }
        if (estadoCivil.equals("0") || estadoCivil.equals("")) {
            estadoCivil = emBranco;
        }
        if (naturalidade.equals("0") || naturalidade.equals("")) {
            naturalidade = emBranco;
        }
        if (sexo.equals("0") || sexo.equals("")) {
            sexo = emBranco;
        }
        if (webPage.equals("0") || webPage.equals("")) {
            webPage = emBranco;
        }
        if (telefone.equals("")) {
            telefone = emBranco;
        }
        if (telefoneCelular.equals("")) {
            telefoneCelular = emBranco;
        }
        if (telefoneComercial.equals("")) {
            telefoneComercial = emBranco;
        }
        if (endereco.equals("")) {
            endereco = emBranco;
        }
        if (numeroEndereco.equals("")) {
            numeroEndereco = emBranco;
        }
        if (cidade.equals("")) {
            cidade = emBranco;
        }
        if (estado.equals("")) {
            estado = emBranco;
        }
        if (cep.equals("")) {
            cep = emBranco;
        }
        if (profissao.equals("")) {
            profissao = emBranco;
        }
        if (nowLocationIpVendasOnline.equals("")) {
            nowLocationIpVendasOnline = emBranco;
        }
        if (conta.equals("0") || conta.equals("")) {
            conta = emBranco;
        }
        if (agencia.equals("0") || agencia.equals("")) {
            agencia = emBranco;
        }
        if (banco.equals("0") || banco.equals("")) {
            banco = emBranco;
        }
        if (complemento.equals("")) {
            complemento = emBranco;
        }
        if (email.equals("")) {
            email = emBranco;
        }
        if (grupo.equals("")) {
            grupo = emBranco;
        }
        if (bairro.equals("")) {
            bairro = emBranco;
        }
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), nome, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("DataNasc_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), dataNasc, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cpf_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), cpf, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Rg_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), rg, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("EstadoCivil_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), estadoCivil, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Naturalidade_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), naturalidade, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Sexo_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), sexo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Webpage_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), webPage, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Matricula_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NowLocationIpVOnline_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), nowLocationIpVendasOnline, textoTag, tamanho);
            }  else if (mar.equalsIgnoreCase("Conta_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), conta, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Agencia_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), agencia, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Banco_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), banco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), endereco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Numero_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), numeroEndereco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Cidade_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), cidade, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Estado_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), estado, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Telefone_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), telefone, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("CEP_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), cep, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Profissao_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), profissao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), responsavel, textoTag, tamanho, false);
            } else if (mar.equalsIgnoreCase("Contato_Emergencia_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), contatoEmergencia, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Telefone_Emergencia_Cliente")){
                texto = substituirTag(texto, marcador.getTag(), telefoneEmergencia, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ComplementoEnd_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), complemento, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Telefone_Celular_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), telefoneCelular, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("BairroEnd_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), bairro, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ConsultorAtual_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), consultorAtual, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Telefone_Comercial_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), telefoneComercial, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Email_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), email, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Grupo_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), grupo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Numero_Cartao_Cliente")){
                texto = substituirTag(texto, marcador.getTag(), numeroCartao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("aValidade_Cartao_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), validadeCartao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_Titular_Cartao_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), nomeTitularCartao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("CPF_Titular_Cartao_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), cpfTitularCartao, textoTag, tamanho);
            }else if (mar.equalsIgnoreCase("Bandeira_Cartao_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), bandeiraCartao.toUpperCase(), textoTag, tamanho);
            }else if (mar.equalsIgnoreCase("AutorizacaoCobranca_Cliente")) {
                String tmp = obterAutorizacoesCobrancaTaxaPersonal(colaborador);
                texto = substituirTag(texto, marcador.getTag(), tmp, textoTag, tmp.length());
            } else if (mar.equalsIgnoreCase("Observacao_Cliente")) {
                ClienteMensagem cmDao = new ClienteMensagem(con);
                List<ClienteMensagemVO> mensagens = cmDao.consultarPorCodigoCliente(colaborador.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String tmp = "";
                for (ClienteMensagemVO mensagemVO : mensagens) {
                    if (mensagemVO.getTipomensagem().equals(TiposMensagensEnum.OBSERVACAO)) {
                        tmp = mensagemVO.getMensagem();
                    }
                }

                texto = substituirTag(texto, marcador.getTag(), tmp, textoTag, tmp.length());
            } else if (mar.equalsIgnoreCase("AssinaturaDigital_Cliente") && controleTaxaPersonal != null) {
                //AssinaturaDigital_Cliente
                texto = substituirTag(texto, marcador.getTag(), "__4ssin4tur4digit4l__", textoTag, "__4ssin4tur4digit4l__".length());
                ContratoAssinaturaDigital coass = new ContratoAssinaturaDigital(con);
                String assinatura = coass.obterPorContrato(controleTaxaPersonal.getCodigo(), false);
                if(UteisValidacao.emptyString(assinatura)){
                    texto = texto.replaceAll("__4ssin4tur4digit4l__", " ");
                }else{
                    texto = addAssinatura(texto, Uteis.getPaintFotoDaNuvem(assinatura));
                }
            } else if(mar.equalsIgnoreCase("AssinaturaBiometriaDigital_Cliente")){
                Pessoa pessoaDAO = new Pessoa(con);
                String assinaturaBiometriaDigital = pessoaDAO.obterAssinaturaBiometriaDigital(colaborador.getPessoa().getCodigo());
                pessoaDAO = null;

                String assinatura = "<div style='word-break: break-all;'>" + assinaturaBiometriaDigital + "</div>";

                texto = substituirTag(texto,
                        marcador.getTag(),
                        assinatura,
                        textoTag,
                        colaborador.getPessoa().getAssinaturaBiometriaDigital().length());
            } else {
                return ("<br>Tag " + mar + " não existe<br>");
            }
        }
        return texto;
    }

    public String substituirTagCliente(String texto, ClienteVO cliente, ContratoVO contrato, VendaAvulsaVO venda, AulaAvulsaDiariaVO vendaDiaria, MarcadorVO marcador, Connection con,boolean manterTagAssinaturaDigital, Integer codProduto) throws Exception {
            String emBranco = obterStringEmBrancoParaTag(marcador);
        String codigo = "";
        String nome = "";
        String dataNasc = "";
        String cpf = "";
        String rg = "";
        String estadoCivil = "";
        String naturalidade = "";
        String sexo = "";
        String webPage = "";
        String telefone = "";
        String contatoEmergencia = "";
        String telefoneEmergencia = "";
        String numeroCartao = "";
        String nomeTitularCartao = "";
        String cpfTitularCartao = "";
        String validadeCartao = "";
        String bandeiraCartao = "";
        //telefone celular
        String telefoneCelular = "";
        //telefone comercial
        String telefoneComercial = "";
        String endereco = "";
        String numeroEndereco = "";
        String cidade = "";
        String estado = "";
        String cep = "";
        String profissao = "";
        String mae = "";
        String pai = "";
        String responsavel = "";
        String responsavelMae = "";
        String responsavelPai = "";
        String responsavelFinanceiroNome = "";
        String matricula = "";
        String nowLocationIpVendasOnline = "";
        String conta = "";
        String agencia = "";
        String banco = "";
        String complemento = "";
        String email = "";
        String grupo = "";
        String bairro = "";
        String consultorAtual = "";
        String responsavelCpf = "";
        String responsavelRg = "";
        String responsavelCpfPai = "";
        String responsavelCpfMae = "";
        String responsavelFinanceiroCpf = "";
        String responsavelCpfNome = "";
        String responsavelRgPai = "";
        String responsavelRgMae = "";
        String responsavelFinanceiroRg = "";
        String responsavelFinanceiroEmail = "";
        String habilitacaoSesc = "";
        if (contrato != null) {
            codigo = contrato.getPessoa().getCodigo().toString();
            nome = contrato.getPessoa().getNome();
            dataNasc = contrato.getPessoa().getDataNasc_Apresentar();
            cpf = contrato.getPessoa().getCfp();
            rg = contrato.getPessoa().getRg();
            estadoCivil = contrato.getPessoa().getEstadoCivil_Apresentar();
            naturalidade = contrato.getPessoa().getNaturalidade();
            sexo = contrato.getPessoa().getSexo();
            webPage = contrato.getPessoa().getWebPage();
            telefone = obterTelefoneCliente(contrato.getPessoa());
            telefoneCelular = obterTelefoneClientePorTipo(contrato.getPessoa(), "CE");
            telefoneComercial = obterTelefoneClientePorTipo(contrato.getPessoa(), "CO");
            endereco = obterEnderecoCliente(contrato.getPessoa());
            numeroEndereco = obterNumeroEnderecoCliente(contrato.getPessoa());
            cidade = contrato.getPessoa().getCidade().getNome();
            estado = contrato.getPessoa().getCidade().getEstado().getDescricao();
            cep = obterCepCliente(contrato.getPessoa());
            profissao = contrato.getPessoa().getProfissao().getDescricao();
            mae = contrato.getPessoa().getNomeMae();
            pai = contrato.getPessoa().getNomePai();
            if(!UteisValidacao.emptyString(contrato.getPessoa().getCpfMae()) && !UteisValidacao.emptyString(contrato.getPessoa().getNomeMae())) {
                responsavel = contrato.getPessoa().getNomeMae();
                responsavelCpf = contrato.getPessoa().getCpfMae();
            } else {
                responsavel = contrato.getPessoa().getNomePai();
                responsavelCpf = contrato.getPessoa().getCpfPai();
            }

            if(responsavel == contrato.getPessoa().getNomePai()) {
                responsavelRg = contrato.getPessoa().getRgPai();
            } else {
                responsavelRg = contrato.getPessoa().getRgMae();
            }

            responsavelPai = contrato.getPessoa().getNomePai();
            responsavelMae = contrato.getPessoa().getNomeMae();
            responsavelCpfPai = contrato.getPessoa().getCpfPai();
            responsavelCpfMae = contrato.getPessoa().getCpfMae();
            responsavelRgPai = contrato.getPessoa().getRgPai();
            responsavelRgMae = contrato.getPessoa().getRgMae();
            responsavelFinanceiroNome = contrato.getPessoa().getNomeRespFinanceiro();
            responsavelFinanceiroCpf = contrato.getPessoa().getCpfRespFinanceiro();
            responsavelFinanceiroRg = contrato.getPessoa().getRgRespFinanceiro();
            responsavelFinanceiroEmail = contrato.getPessoa().getEmailRespFinanceiro();
            contatoEmergencia = (contrato.getPessoa().getContatoEmergencia() == null ? "" : contrato.getPessoa().getContatoEmergencia());
            telefoneEmergencia = (contrato.getPessoa().getTelefoneEmergencia() == null ? "" : contrato.getPessoa().getTelefoneEmergencia());
            matricula = cliente.getMatricula();
            Cliente cliDao = new Cliente(con);
            NowLocationIpVendaDTO nowLocationIpVendaDTO = cliDao.consultarUltimoIpLocalizacaoCliente(cliente.getCodigo());
            if (nowLocationIpVendaDTO != null) {
                nowLocationIpVendasOnline = nowLocationIpVendaDTO.getData() + " - " +
                        nowLocationIpVendaDTO.getCidade() + "/" + nowLocationIpVendaDTO.getEstado() + " - (" +
                        nowLocationIpVendaDTO.getLocalizacao() + ") - " + nowLocationIpVendaDTO.getIp();
            }
            cliDao = null;
            conta = cliente.getConta();
            agencia = cliente.getAgencia();
            banco = cliente.getBanco();
            complemento = obterComplemento(contrato.getPessoa());
            bairro = obterBairro(contrato.getPessoa());
            habilitacaoSesc = cliente.getMatriculaSesc();
            consultorAtual = obterConsultorAtual(cliente.getVinculoVOs());
            email = obterEmailCliente(contrato.getPessoa());
            grupo = obterGruposCliente(cliente);

            for (int i = 0; i <  cliente.getAutorizacoes().size(); i++) {
                TipoAutorizacaoCobrancaEnum tipo = cliente.getAutorizacoes().get(i).getTipoAutorizacao();
                if (tipo == TipoAutorizacaoCobrancaEnum.CARTAOCREDITO && cliente.getAutorizacoes().get(i).getNomeTitularCartao() != null) {
                    nomeTitularCartao = cliente.getAutorizacoes().get(i).getNomeTitularCartao();
                    numeroCartao = cliente.getAutorizacoes().get(i).toWS().getNumeroCartao() == null ? "" : cliente.getAutorizacoes().get(i).toWS().getNumeroCartao();
                    cpfTitularCartao = cliente.getAutorizacoes().get(i).getCpfTitular() == null ? "" : cliente.getAutorizacoes().get(i).getCpfTitular();
                    validadeCartao = cliente.getAutorizacoes().get(i).getValidadeCartao() == null ? "" : cliente.getAutorizacoes().get(i).getValidadeCartao();
                    bandeiraCartao = cliente.getAutorizacoes().get(i).getImagemBandeira() == null ? "" : cliente.getAutorizacoes().get(i).getImagemBandeira();
                    break;
                } else {
                    //SE ENTRAR NO ELSE
                    //SETAR VAZIO PORQUE A AUTORIZAÇÃO DE COBRANÇA JÁ FOI IMPORTADA
                    nomeTitularCartao = "";
                    numeroCartao = "";
                    cpfTitularCartao = "";
                    validadeCartao = "";
                    bandeiraCartao = "";
                }
            }
            if (dataNasc.equals("0") || dataNasc.equals("")) {
                dataNasc = emBranco;
            } else {
                //verificar se o cliente é maior de idade ( >= 18 anos ) considerando o início do contrato
                if (Uteis.calcularIdadePessoa(contrato.getVigenciaDe(), contrato.getPessoa().getDataNasc()) >= 18) {
                    responsavel = emBranco;
                    responsavelCpf = emBranco;
                    responsavelRg = emBranco;
                    responsavelCpfNome = emBranco;

                    responsavelPai = emBranco;
                    responsavelMae = emBranco;
                }
            }

        } else if (venda != null){
            codigo = venda.getParcela().getPessoa().getCodigo().toString();
            nome = venda.getParcela().getPessoa().getNome();
            dataNasc = venda.getParcela().getPessoa().getDataNasc_Apresentar();
            cpf = venda.getParcela().getPessoa().getCfp();
            rg = venda.getParcela().getPessoa().getRg();
            estadoCivil = venda.getParcela().getPessoa().getEstadoCivil_Apresentar();
            naturalidade = venda.getParcela().getPessoa().getNaturalidade();
            sexo = venda.getParcela().getPessoa().getSexo();
            webPage = venda.getParcela().getPessoa().getWebPage();
            telefone = obterTelefoneCliente(venda.getParcela().getPessoa());
            telefoneCelular = obterTelefoneClientePorTipo(venda.getParcela().getPessoa(), "CE");
            telefoneComercial = obterTelefoneClientePorTipo(venda.getParcela().getPessoa(), "CO");
            endereco = obterEnderecoCliente(venda.getParcela().getPessoa());
            numeroEndereco = obterNumeroEnderecoCliente(venda.getParcela().getPessoa());
            cidade = venda.getParcela().getPessoa().getCidade().getNome();
            estado = venda.getParcela().getPessoa().getCidade().getEstado().getDescricao();
            cep = obterCepCliente(venda.getParcela().getPessoa());
            profissao = venda.getParcela().getPessoa().getProfissao().getDescricao();
            mae = venda.getParcela().getPessoa().getNomeMae();
            pai = venda.getParcela().getPessoa().getNomePai();
            responsavel = venda.getParcela().getPessoa().getNomeMae();

            if (UteisValidacao.emptyString(responsavel)) {
                responsavel = venda.getParcela().getPessoa().getNomePai();
            }

            responsavelCpf = venda.getParcela().getPessoa().getCpfMae();
            if (UteisValidacao.emptyString(responsavelCpf)) {
                responsavelCpf = venda.getParcela().getPessoa().getCpfPai();
            }

            responsavelRg = venda.getParcela().getPessoa().getRgMae();
            if (UteisValidacao.emptyString(responsavelRg)) {
                responsavelRg = venda.getParcela().getPessoa().getRgPai();
            }

            responsavelPai = venda.getParcela().getPessoa().getNomePai();
            responsavelMae = venda.getParcela().getPessoa().getNomeMae();
            responsavelCpfPai = venda.getParcela().getPessoa().getCpfPai();
            responsavelCpfMae = venda.getParcela().getPessoa().getCpfMae();
            responsavelRgMae = venda.getParcela().getPessoa().getRgMae();
            responsavelRgPai = venda.getParcela().getPessoa().getRgPai();
            responsavelFinanceiroNome = venda.getParcela().getPessoa().getNomeRespFinanceiro();
            responsavelFinanceiroCpf = venda.getParcela().getPessoa().getCpfRespFinanceiro();
            responsavelFinanceiroRg = venda.getParcela().getPessoa().getRgRespFinanceiro();
            responsavelFinanceiroEmail = venda.getParcela().getPessoa().getEmailRespFinanceiro();
            Cliente cliDao = new Cliente(con);
            cliente = cliDao.consultarPorCodigoPessoa(venda.getParcela().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            matricula = cliente.getMatricula();
            habilitacaoSesc = cliente.getMatriculaSesc();
            NowLocationIpVendaDTO nowLocationIpVendaDTO = cliDao.consultarUltimoIpLocalizacaoCliente(cliente.getCodigo());
            if (nowLocationIpVendaDTO != null) {
                nowLocationIpVendasOnline = nowLocationIpVendaDTO.getData() + " - " +
                        nowLocationIpVendaDTO.getCidade() + "/" + nowLocationIpVendaDTO.getEstado() + " - (" +
                        nowLocationIpVendaDTO.getLocalizacao() + ") - " + nowLocationIpVendaDTO.getIp();
            }
            for (int i = 0; i <  cliente.getAutorizacoes().size(); i++) {
                TipoAutorizacaoCobrancaEnum tipo = cliente.getAutorizacoes().get(i).getTipoAutorizacao();
                if (tipo == TipoAutorizacaoCobrancaEnum.CARTAOCREDITO && cliente.getAutorizacoes().get(i).getNomeTitularCartao() != null) {
                    nomeTitularCartao = cliente.getAutorizacoes().get(i).getNomeTitularCartao();
                    numeroCartao = cliente.getAutorizacoes().get(i).toWS().getNumeroCartao() == null ? "" : cliente.getAutorizacoes().get(i).toWS().getNumeroCartao();
                    cpfTitularCartao = cliente.getAutorizacoes().get(i).getCpfTitular() == null ? "" : cliente.getAutorizacoes().get(i).getCpfTitular();
                    validadeCartao = cliente.getAutorizacoes().get(i).getValidadeCartao() == null ? "" : cliente.getAutorizacoes().get(i).getValidadeCartao();
                    bandeiraCartao = cliente.getAutorizacoes().get(i).getImagemBandeira() == null ? "" : cliente.getAutorizacoes().get(i).getImagemBandeira();
                    break;
                }
            }
            cliDao = null;
            conta = cliente.getConta();
            agencia = cliente.getAgencia();
            banco = cliente.getBanco();
            complemento = obterComplemento(venda.getParcela().getPessoa());
            bairro = obterBairro(venda.getParcela().getPessoa());
            email = obterEmailCliente(venda.getParcela().getPessoa());
            grupo = obterGruposCliente(venda.getParcela().getPessoa().getCodigo(), con);
            if (dataNasc.equals("0") || dataNasc.equals("")) {
                dataNasc = emBranco;
            } else {
                //verificar se o cliente é maior de idade ( >= 18 anos )
                if (Uteis.calcularIdadePessoa(Calendario.hoje(), venda.getParcela().getPessoa().getDataNasc()) >= 18) {
                    responsavel = emBranco;
                    responsavelCpf = emBranco;
                    responsavelRg = emBranco;
                    responsavelCpfNome = emBranco;

                    responsavelPai = emBranco;
                    responsavelMae = emBranco;
                }
            }
        } else {
            codigo = vendaDiaria.getParcela().getPessoa().getCodigo().toString();
            nome = vendaDiaria.getParcela().getPessoa().getNome();
            dataNasc = vendaDiaria.getParcela().getPessoa().getDataNasc_Apresentar();
            cpf = vendaDiaria.getParcela().getPessoa().getCfp();
            rg = vendaDiaria.getParcela().getPessoa().getRg();
            estadoCivil = vendaDiaria.getParcela().getPessoa().getEstadoCivil_Apresentar();
            naturalidade = vendaDiaria.getParcela().getPessoa().getNaturalidade();
            sexo = vendaDiaria.getParcela().getPessoa().getSexo();
            webPage = vendaDiaria.getParcela().getPessoa().getWebPage();
            telefone = obterTelefoneCliente(vendaDiaria.getParcela().getPessoa());
            telefoneCelular = obterTelefoneClientePorTipo(vendaDiaria.getParcela().getPessoa(), "CE");
            telefoneComercial = obterTelefoneClientePorTipo(vendaDiaria.getParcela().getPessoa(), "CO");
            endereco = obterEnderecoCliente(vendaDiaria.getParcela().getPessoa());
            numeroEndereco = obterNumeroEnderecoCliente(vendaDiaria.getParcela().getPessoa());
            cidade = vendaDiaria.getParcela().getPessoa().getCidade().getNome();
            estado = vendaDiaria.getParcela().getPessoa().getCidade().getEstado().getDescricao();
            cep = obterCepCliente(vendaDiaria.getParcela().getPessoa());
            profissao = vendaDiaria.getParcela().getPessoa().getProfissao().getDescricao();
            mae = vendaDiaria.getParcela().getPessoa().getNomeMae();
            pai = vendaDiaria.getParcela().getPessoa().getNomePai();
            responsavel = vendaDiaria.getParcela().getPessoa().getNomeMae();

            if (UteisValidacao.emptyString(responsavel)) {
                responsavel = vendaDiaria.getParcela().getPessoa().getNomePai();
            }

            responsavelCpf = vendaDiaria.getParcela().getPessoa().getCpfMae();
            if (UteisValidacao.emptyString(responsavelCpf)) {
                responsavelCpf = vendaDiaria.getParcela().getPessoa().getCpfPai();
            }

            responsavelRg = vendaDiaria.getParcela().getPessoa().getRgMae();
            if (UteisValidacao.emptyString(responsavelRg)) {
                responsavelRg = vendaDiaria.getParcela().getPessoa().getRgPai();
            }

            responsavelPai = vendaDiaria.getParcela().getPessoa().getNomePai();
            responsavelMae = vendaDiaria.getParcela().getPessoa().getNomeMae();
            responsavelCpfPai = vendaDiaria.getParcela().getPessoa().getCpfPai();
            responsavelCpfMae = vendaDiaria.getParcela().getPessoa().getCpfMae();
            responsavelRgMae = vendaDiaria.getParcela().getPessoa().getRgMae();
            responsavelRgPai = vendaDiaria.getParcela().getPessoa().getRgPai();
            responsavelFinanceiroNome = vendaDiaria.getParcela().getPessoa().getNomeRespFinanceiro();
            responsavelFinanceiroCpf = vendaDiaria.getParcela().getPessoa().getCpfRespFinanceiro();
            responsavelFinanceiroRg = vendaDiaria.getParcela().getPessoa().getRgRespFinanceiro();
            responsavelFinanceiroEmail = vendaDiaria.getParcela().getPessoa().getEmailRespFinanceiro();
            Cliente cliDao = new Cliente(con);
            cliente = cliDao.consultarPorCodigoPessoa(vendaDiaria.getParcela().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            matricula = cliente.getMatricula();
            habilitacaoSesc = cliente.getMatriculaSesc();
            NowLocationIpVendaDTO nowLocationIpVendaDTO = cliDao.consultarUltimoIpLocalizacaoCliente(cliente.getCodigo());
            if (nowLocationIpVendaDTO != null) {
                nowLocationIpVendasOnline = nowLocationIpVendaDTO.getData() + " - " +
                        nowLocationIpVendaDTO.getCidade() + "/" + nowLocationIpVendaDTO.getEstado() + " - (" +
                        nowLocationIpVendaDTO.getLocalizacao() + ") - " + nowLocationIpVendaDTO.getIp();
            }
            for (int i = 0; i < cliente.getAutorizacoes().size(); i++) {
                TipoAutorizacaoCobrancaEnum tipo = cliente.getAutorizacoes().get(i).getTipoAutorizacao();
                if (tipo == TipoAutorizacaoCobrancaEnum.CARTAOCREDITO && cliente.getAutorizacoes().get(i).getNomeTitularCartao() != null) {
                    nomeTitularCartao = cliente.getAutorizacoes().get(i).getNomeTitularCartao();
                    numeroCartao = cliente.getAutorizacoes().get(i).toWS().getNumeroCartao() == null ? "" : cliente.getAutorizacoes().get(i).toWS().getNumeroCartao();
                    cpfTitularCartao = cliente.getAutorizacoes().get(i).getCpfTitular() == null ? "" : cliente.getAutorizacoes().get(i).getCpfTitular();
                    validadeCartao = cliente.getAutorizacoes().get(i).getValidadeCartao() == null ? "" : cliente.getAutorizacoes().get(i).getValidadeCartao();
                    bandeiraCartao = cliente.getAutorizacoes().get(i).getImagemBandeira() == null ? "" : cliente.getAutorizacoes().get(i).getImagemBandeira();
                    break;
                }
            }
            cliDao = null;
            conta = cliente.getConta();
            agencia = cliente.getAgencia();
            banco = cliente.getBanco();
            complemento = obterComplemento(vendaDiaria.getParcela().getPessoa());
            bairro = obterBairro(vendaDiaria.getParcela().getPessoa());
            email = obterEmailCliente(vendaDiaria.getParcela().getPessoa());
            grupo = obterGruposCliente(vendaDiaria.getParcela().getPessoa().getCodigo(), con);
            if (dataNasc.equals("0") || dataNasc.equals("")) {
                dataNasc = emBranco;
            } else {
                //verificar se o cliente é maior de idade ( >= 18 anos )
                if (Uteis.calcularIdadePessoa(Calendario.hoje(), vendaDiaria.getParcela().getPessoa().getDataNasc()) >= 18) {
                    responsavel = emBranco;
                    responsavelCpf = emBranco;
                    responsavelRg = emBranco;
                    responsavelCpfNome = emBranco;

                    responsavelPai = emBranco;
                    responsavelMae = emBranco;
                }
            }
        }
        if (!codigo.equals("0") && !codigo.equals("") && !UteisValidacao.emptyNumber(cliente.getPessoaResponsavel().getCodigo())) {
            Pessoa pessoaDao = new Pessoa(con);
            PessoaVO pessoaResponsavelCpf = pessoaDao.consultarPorChavePrimaria(cliente.getPessoaResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            responsavelCpf = pessoaResponsavelCpf.getCfp();
            responsavelCpfNome = pessoaResponsavelCpf.getNome();
            responsavelRg = pessoaResponsavelCpf.getRg();
        }
        if (codigo.equals("0") || codigo.equals("")) {
            codigo = emBranco;
        }
        if (nome.equals("0") || nome.equals("")) {
            nome = emBranco;
        }
        if (mae.equals("") && pai.equals("")) {
             responsavel = emBranco;
        }
        if (cpf.equals("0") || cpf.equals("")) {
            cpf = emBranco;
        }
        if (rg.equals("0") || rg.equals("")) {
            rg = emBranco;
        }
        if (estadoCivil.equals("0") || estadoCivil.equals("")) {
            estadoCivil = emBranco;
        }
        if (naturalidade.equals("0") || naturalidade.equals("")) {
            naturalidade = emBranco;
        }
        if (sexo.equals("0") || sexo.equals("")) {
            sexo = emBranco;
        }
        if (webPage.equals("0") || webPage.equals("")) {
            webPage = emBranco;
        }
        if (telefone.equals("")) {
            telefone = emBranco;
        }
        if (telefoneCelular.equals("")) {
            telefoneCelular = emBranco;
        }
        if (telefoneComercial.equals("")) {
            telefoneComercial = emBranco;
        }
        if (endereco.equals("")) {
            endereco = emBranco;
        }
        if (numeroEndereco.equals("")) {
            numeroEndereco = emBranco;
        }
        if (cidade.equals("")) {
            cidade = emBranco;
        }
        if (estado.equals("")) {
            estado = emBranco;
        }
        if (cep.equals("")) {
            cep = emBranco;
        }
        if (profissao.equals("")) {
            profissao = emBranco;
        }
        if (mae.equals("")) {
            mae = emBranco;
        }
        if (pai.equals("")) {
            pai = emBranco;
        }
        if (matricula.equals("0") || matricula.equals("")) {
            matricula = emBranco;
        }
        if (nowLocationIpVendasOnline.equals("")) {
            nowLocationIpVendasOnline = emBranco;
        }
        if (conta.equals("0") || conta.equals("")) {
            conta = emBranco;
        }
        if (agencia.equals("0") || agencia.equals("")) {
            agencia = emBranco;
        }
        if (banco.equals("0") || banco.equals("")) {
            banco = emBranco;
        }
        if (complemento.equals("")) {
            complemento = emBranco;
        }
        if (email.equals("")) {
            email = emBranco;
        }
        if (grupo.equals("")) {
            grupo = emBranco;
        }
        if (bairro.equals("")) {
            bairro = emBranco;
        }
        if (responsavelCpf.equals("")) {
            responsavelCpf = emBranco;
        }
        if (responsavelRg.equals("")) {
            responsavelRg = emBranco;
        }
        if (responsavelCpfPai.equals("")) {
            responsavelCpfPai = emBranco;
        }
        if (responsavelCpfMae.equals("")) {
            responsavelCpfMae = emBranco;
        }
        if (responsavelRgPai.equals("")) {
            responsavelRgPai = emBranco;
        }
        if (responsavelRgMae.equals("")) {
            responsavelRgMae = emBranco;
        }
        if (responsavelCpfNome.equals("")) {
            responsavelCpfNome = emBranco;
        }
        if (UteisValidacao.emptyString(responsavelFinanceiroNome)) {
            responsavelFinanceiroNome = emBranco;
        }
        if (UteisValidacao.emptyString(responsavelFinanceiroCpf)) {
            responsavelFinanceiroCpf = emBranco;
        }
        if (UteisValidacao.emptyString(responsavelFinanceiroRg)) {
            responsavelFinanceiroRg = emBranco;
        }
        if (UteisValidacao.emptyString(responsavelFinanceiroEmail)) {
            responsavelFinanceiroEmail = emBranco;
        }
        if (UteisValidacao.emptyString(habilitacaoSesc)) {
            habilitacaoSesc = emBranco;
        }
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String mar = obterTagSemTextoSemTamanho(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());

            if (mar.equalsIgnoreCase("Codigo_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), codigo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), nome, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_ResponsavelLegal_Cliente")) {
                String nomeResponsavelLegal = nome;
                if (cliente.getPessoa().getIdade() < 18) {
                    if (responsavelCpfNome != null && !UteisValidacao.emptyString(responsavelCpfNome.trim())) {
                        nomeResponsavelLegal = responsavelCpfNome;
                    } else if (responsavelMae != null && !UteisValidacao.emptyString(responsavelMae.trim())) {
                        nomeResponsavelLegal = responsavelMae;
                    } else if (responsavelPai != null && !UteisValidacao.emptyString(responsavelPai)) {
                        nomeResponsavelLegal = responsavelPai;
                    }
                }
                texto = substituirTag(texto, marcador.getTag(), nomeResponsavelLegal, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("DataNasc_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), dataNasc, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cpf_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), cpf, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Cpf_ResponsavelLegal_Cliente")) {
                String cpfResponsavelLegal = cpf;
                if (cliente.getPessoa().getIdade() < 18) {
                    if (!UteisValidacao.emptyString(responsavelCpf)) {
                        cpfResponsavelLegal = responsavelCpf;
                    } else if (!UteisValidacao.emptyString(responsavelCpfMae)) {
                        cpfResponsavelLegal = responsavelCpfMae;
                    } else if (!UteisValidacao.emptyString(responsavelCpfPai)) {
                        cpfResponsavelLegal = responsavelCpfPai;
                    }
                }
                texto = substituirTag(texto, marcador.getTag(), cpfResponsavelLegal, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Rg_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), rg, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("EstadoCivil_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), estadoCivil, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Naturalidade_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), naturalidade, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Sexo_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), sexo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Webpage_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), webPage, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Matricula_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), matricula, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("NowLocationIpVOnline_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), nowLocationIpVendasOnline, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Conta_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), conta, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Agencia_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), agencia, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Banco_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), banco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), endereco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Numero_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), numeroEndereco, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Cidade_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), cidade, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Endereco_Estado_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), estado, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Telefone_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), telefone, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("CEP_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), cep, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Profissao_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), profissao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Mae_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), mae, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Pai_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), pai, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), responsavel, textoTag, tamanho, false);
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente_Pai")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelPai, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente_Mae")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelMae, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Contato_Emergencia_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), contatoEmergencia, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Telefone_Emergencia_Cliente")){
                texto = substituirTag(texto, marcador.getTag(), telefoneEmergencia, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ComplementoEnd_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), complemento, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Telefone_Celular_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), telefoneCelular, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("BairroEnd_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), bairro, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("ConsultorAtual_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), consultorAtual, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Telefone_Comercial_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), telefoneComercial, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Email_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), email, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Grupo_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), grupo, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Numero_Cartao_Cliente")){
                texto = substituirTag(texto, marcador.getTag(), numeroCartao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("aValidade_Cartao_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), validadeCartao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Nome_Titular_Cartao_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), nomeTitularCartao, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("CPF_Titular_Cartao_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), cpfTitularCartao, textoTag, tamanho);
            }else if (mar.equalsIgnoreCase("Bandeira_Cartao_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), bandeiraCartao.toUpperCase(), textoTag, tamanho);
            }else if (mar.equalsIgnoreCase("AutorizacaoCobranca_Cliente")) {
                String tmp = obterAutorizacoesCobrancaCliente(cliente);
                texto = substituirTag(texto, marcador.getTag(), tmp, textoTag, tmp.length());
            } else if (mar.equalsIgnoreCase("Observacao_Cliente")) {
                ClienteMensagem cmDao = new ClienteMensagem(con);
                List<ClienteMensagemVO> mensagens = cmDao.consultarPorCodigoCliente(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String tmp = "";
                for (ClienteMensagemVO mensagemVO : mensagens) {
                    if (mensagemVO.getTipomensagem().equals(TiposMensagensEnum.OBSERVACAO)) {
                        tmp = mensagemVO.getMensagem();
                    }
                }

                texto = substituirTag(texto, marcador.getTag(), tmp, textoTag, tmp.length());
            } else if (mar.equalsIgnoreCase("Categoria_Cliente")) {
                String categoria = cliente.getCategoria_Apresentar();
                texto = substituirTag(texto, marcador.getTag(), categoria, textoTag, categoria.length());
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente_Cpf")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelCpf, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente_Rg")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelRg, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente_Pai_Cpf")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelCpfPai, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente_Mae_Cpf")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelCpfMae, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente_Pai_Rg")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelRgPai, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente_Mae_Rg")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelRgMae, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Financeiro_Nome_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelFinanceiroNome, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Financeiro_Cpf_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelFinanceiroCpf, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Financeiro_Rg_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelFinanceiroRg, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Financeiro_Email_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelFinanceiroEmail, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Responsavel_Cliente_Cpf_Nome")) {
                texto = substituirTag(texto, marcador.getTag(), responsavelCpfNome, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("Habilitacao_Sesc_Cliente")) {
                texto = substituirTag(texto, marcador.getTag(), habilitacaoSesc, textoTag, tamanho);
            } else if (mar.equalsIgnoreCase("AssinaturaDigital_Cliente") && (contrato != null || ((venda != null || vendaDiaria != null) && !UteisValidacao.emptyNumber(codProduto)))) {
                //AssinaturaDigital_Cliente
                texto = substituirTag(texto, marcador.getTag(), "__4ssin4tur4digit4l__", textoTag, "__4ssin4tur4digit4l__".length());
                if ((venda != null || vendaDiaria != null) && !UteisValidacao.emptyNumber(codProduto)) {
                    ProdutoTextoPadrao ptp = new ProdutoTextoPadrao(con);
                    ProdutoTextoPadraoVO textoContrato = venda != null ? ptp.consultarPorProdutoEVenda(codProduto, venda.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS) :
                            ptp.consultarPorProdutoEAulaAvulsaDiaria(codProduto, vendaDiaria.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ProdutoAssinaturaDigital coass = new ProdutoAssinaturaDigital(con);
                    String assinatura = textoContrato != null ? coass.obterPorContrato(textoContrato.getCodigo()) : "";
                    if (UteisValidacao.emptyString(assinatura)) {
                        if (!manterTagAssinaturaDigital) {
                            texto = texto.replaceAll("__4ssin4tur4digit4l__", " ");
                        }
                    } else {
                        String divAssinatura = "<div id=\"idassinatur4digit4l\" style=\"width: 20%; text-align: center; margin: 0 auto;\">Assinatura digital: <img style=\"width: 100%;\" src=\""+Uteis.getPaintFotoDaNuvem(assinatura)+"\"/></div>";
                        texto = texto.replace(divAssinatura, "");
                        texto = addAssinatura(texto, Uteis.getPaintFotoDaNuvem(assinatura));
                    }
                } else {
                    ContratoAssinaturaDigital coass = new ContratoAssinaturaDigital(con);
                    String assinatura = coass.obterPorContrato(contrato.getCodigo(), false);
                    if (UteisValidacao.emptyString(assinatura)) {
                        if (!manterTagAssinaturaDigital) {
                            texto = texto.replaceAll("__4ssin4tur4digit4l__", " ");
                        }
                    } else {
                        texto = addAssinatura(texto, Uteis.getPaintFotoDaNuvem(assinatura));
                    }
                }
            } else if(mar.equalsIgnoreCase("AssinaturaBiometriaDigital_Cliente")){
                Pessoa pessoaDAO = new Pessoa(con);
                String assinaturaBiometriaDigital = pessoaDAO.obterAssinaturaBiometriaDigital(cliente.getPessoa().getCodigo());
                pessoaDAO = null;

                String assinatura = "<div style='word-break: break-all;'>" + assinaturaBiometriaDigital + "</div>";

                texto = substituirTag(texto,
                        marcador.getTag(),
                        assinatura,
                        textoTag,
                        cliente.getPessoa().getAssinaturaBiometriaDigital().length());
            } else if(mar.equalsIgnoreCase("AssinaturaDigital_RespFinanceiro_Cliente")) {
                //AssinaturaDigital_Cliente
                texto = substituirTag(texto, marcador.getTag(), "__4ssin4tur4digit4l2__", textoTag, "__4ssin4tur4digit4l2__".length());

                if (contrato != null && !UteisValidacao.emptyNumber(contrato.getCodigo())) {
                    ContratoAssinaturaDigital coass = new ContratoAssinaturaDigital(con);
                    String assinatura = coass.obterPorContrato(contrato.getCodigo(), true);
                    if (UteisValidacao.emptyString(assinatura)) {
                        if (!manterTagAssinaturaDigital) {
                            texto = texto.replaceAll("__4ssin4tur4digit4l2__", " ");
                        }
                    } else {
                        texto = addAssinatura2(texto, Uteis.getPaintFotoDaNuvem(assinatura));
                    }
                } else {
                    texto = texto.replaceAll("__4ssin4tur4digit4l2__", " ");
                }
            } else {
                return ("<br>Tag " + mar + " não existe<br>");
            }
        }
        return texto;
    }

    private String obterConsultorAtual(List<VinculoVO> vinculoVOs) {
        for (VinculoVO vinculoVO : vinculoVOs) {
            if (TipoColaboradorEnum.CONSULTOR.getSigla().equals(vinculoVO.getTipoVinculo())) {
                return vinculoVO.getColaborador().getPessoa().getNome();
            }
        }
        return "";
    }

    public String substituirTagClienteEmBranco(String texto, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        while (texto.indexOf(marcador.getTag()) != -1) {
            int tamanho = obterTamanhoTag(marcador.getTag());
            String textoTag = obterTextoTag(marcador.getTag());
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        }
        return texto;
    }

    public String retirarLinhasHtml(String texto, String tag) {
        if (texto.indexOf(tag) == -1) {
            return texto;
        }
        String novoTexto = texto.substring(0, texto.indexOf(tag) + tag.length());
        novoTexto = novoTexto.replace(tag, "");
        texto = novoTexto + texto.substring(texto.indexOf(tag) + tag.length());
        return texto;

    }

    public String gerarLinhasHtml(String texto, MarcadorVO marcador) {
        int posicaoTag = texto.indexOf(marcador.getTag());
        if (posicaoTag == -1) {
            posicaoTag = 0;
        }
        String parteStr1 = texto.substring(0, posicaoTag);
        String parteStr2 = texto.substring(posicaoTag);
        texto = parteStr1 + "<br/>" + marcador.getTag() + parteStr2;
        return texto;

    }

    public String substituirTagModalidade(String texto, ContratoVO contrato, MarcadorVO marcador) throws Exception {
        tagGeral = marcador.getTag();
        String emBranco = obterStringEmBrancoParaTag(marcador);
        if (contrato.getContratoModalidadeVOs().size() == 0) {
            texto = texto.replace(marcador.getTag(), emBranco);
        } else {
            boolean imprimirTagNrVezesNome = true;
            String nomeNrVezes = obterNomeNrVezesModalidade(contrato, true, false);
            Iterator i = contrato.getContratoModalidadeVOs().iterator();
            while (i.hasNext()) {
                texto = gerarLinhasHtml(texto, marcador);
                ContratoModalidadeVO contratoMod = (ContratoModalidadeVO) i.next();
                ModalidadeVO mod = contratoMod.getModalidade();
                String codigo = mod.getCodigo().toString();
                if (codigo.equals("0") || codigo.equals("")) {
                    codigo = emBranco;
                }
                String nome = mod.getNome();
                if (nome.equals("")) {
                    nome = emBranco;
                }
                String nrVezes = contratoMod.getNrVezesSemana().toString();
                if (nrVezes.equals("0") || nrVezes.equals("")) {
                    nrVezes = emBranco;
                }
                String valor = Uteis.getDoubleFormatado(contratoMod.getValorFinalModalidade());
                if (valor.equals("0.0") || valor.equals("")) {
                    valor = emBranco;
                }

                tagGeral = marcador.getTag();
                while ((texto.indexOf(tagGeral) != -1) && !tagGeral.equals("")) {
                    int tamanho = obterTamanhoTag(tagGeral);
                    String textoTag = obterTextoTag(tagGeral);
                    String mar = obterTagSemTextoSemTamanhoQuandoConcatenadaAOutraTag(tagGeral);


                    if (mar.contains("Codigo_Modalidade")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, codigo, textoTag, tamanho);
                    } else if (mar.contains("ValorMensal_Modalidade")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, valor, textoTag, tamanho);
                    } else if (mar.contains("NrVezes_Modalidade")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, nrVezes, textoTag, tamanho);
                    } else if (mar.contains("Nome_Modalidade")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, nome, textoTag, tamanho);
                    } else if (mar.contains("NomeVezes_Modalidade")) {
                        if (imprimirTagNrVezesNome) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, nomeNrVezes, textoTag, tamanho);
                        } else {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, "", textoTag, tamanho);
                        }
                        imprimirTagNrVezesNome = false;
                    } else {
                        return ("<br>Tag " + mar + " não existe<br>");
                    }
                }
            }
            texto = retirarLinhasHtml(texto, marcador.getTag());
        }
        return texto;
    }

    private String obterNomeNrVezesModalidadeTaxaPersonal(ControleTaxaPersonalVO taxaPersonal, boolean nrvezes, boolean nomePrimeiro) {
        String nomeVezes = "";
        for (PlanoModalidadeVO cm : taxaPersonal.getPlano().getPlanoModalidadeVOs()) {
            if (nomePrimeiro) {
                if(nrvezes) {
                    nomeVezes += "<br>" + cm.getModalidade().getNome() + " - " + cm.getListaVezesSemana() + " Vez(es)" ;
                } else {
                    nomeVezes += "<br> " + cm.getModalidade().getNome();
                }
            } else {
                if(nrvezes) {
                    nomeVezes += ", " + cm.getListaVezesSemana() + " " + cm.getModalidade().getNome();
                } else {
                    nomeVezes += ", " + cm.getModalidade().getNome();
                }
            }

        }
        nomeVezes = nomeVezes.replaceFirst(",", "");
        return nomeVezes;
    }

    private String obterNomeNrVezesModalidade(ContratoVO contrato, boolean nrvezes, boolean nomePrimeiro) {
        String nomeVezes = "";
        for (ContratoModalidadeVO cm : contrato.getContratoModalidadeVOs()) {
            if (nomePrimeiro) {
                if(nrvezes) {
                    nomeVezes += "<br>" + cm.getModalidade().getNome() + " - " + cm.getNrVezesSemana() + " Vez(es)" ;
                } else {
                    nomeVezes += "<br> " + cm.getModalidade().getNome();
                }
            } else {
                if(nrvezes) {
                    nomeVezes += ", " + cm.getNrVezesSemana() + " " + cm.getModalidade().getNome();
                } else {
                    nomeVezes += ", " + cm.getModalidade().getNome();
                }
            }

        }
        nomeVezes = nomeVezes.replaceFirst(",", "");
        return nomeVezes;
    }

    public String substituirTagMovParcela(String texto, List<MovParcelaVO> movParcelaVOs, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        tagGeral = marcador.getTag();
        if (movParcelaVOs.size() == 0) {
            texto = texto.replace(marcador.getTag(), emBranco);
        } else {
            Iterator i = movParcelaVOs.iterator();
            while (i.hasNext()) {
                MovParcelaVO movParcela = (MovParcelaVO) i.next();

                if ((marcador.getTag().contains("_Renegociadas") && !movParcela.getSituacao().equals("RG")) ||
                        !marcador.getTag().contains("_Renegociadas"))  {

                    texto = gerarLinhasHtml(texto, marcador);
                    String codigo = movParcela.getCodigo().toString();
                    if (codigo.equals("0") || codigo.equals("")) {
                        codigo = emBranco;
                    }
                    String descricao = movParcela.getDescricao();
                    if (descricao.equals("")) {
                        descricao = emBranco;
                    }
                    String valorParcela = Uteis.getDoubleFormatado(movParcela.getValorParcela());
                    if (valorParcela.equals("0.0") || valorParcela.equals("")) {
                        valorParcela = emBranco;
                    }
                    String dataVencimento = movParcela.getDataVencimento_Apresentar();
                    if (dataVencimento.equals("")) {
                        dataVencimento = emBranco;
                    }
                    String percentualMulta = movParcela.getPercentualMulta().toString();
                    if (percentualMulta.equals("0") || percentualMulta.equals("")) {
                        percentualMulta = emBranco;
                    }
                    String percentualJuro = movParcela.getPercentualJuro().toString();
                    if (percentualJuro.equals("0") || percentualJuro.equals("")) {
                        percentualJuro = emBranco;
                    }
                    tagGeral = marcador.getTag();

                    while ((texto.indexOf(tagGeral) != -1) && !tagGeral.equals("")) {
                        int tamanho = obterTamanhoTag(tagGeral);
                        String textoTag = obterTextoTag(tagGeral);
                        String mar = obterTagSemTextoSemTamanhoQuandoConcatenadaAOutraTag(tagGeral);

                        if (mar.contains("Codigo_MovParcela]")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, codigo, textoTag, tamanho);
                        } else if (mar.contains("Codigo_MovParcela_Sem_Renegociadas]")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, codigo, textoTag, tamanho);
                        } else if (mar.contains("DataVencimento_MovParcela]")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, dataVencimento, textoTag, tamanho);
                        } else if (mar.contains("DataVencimento_MovParcela_Sem_Renegociadas]")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, dataVencimento, textoTag, tamanho);
                        } else if (mar.contains("ValorParcela_MovParcela]")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, valorParcela, textoTag, tamanho);
                        } else if (mar.contains("ValorParcela_MovParcela_Sem_Renegociadas]")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, valorParcela, textoTag, tamanho);
                        } else if (mar.contains("PercentualMulta_MovParcela]")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, percentualMulta, textoTag, tamanho);
                        } else if (mar.contains("PercentualJuro_MovParcela]")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, percentualJuro, textoTag, tamanho);
                        } else if (mar.contains("Descricao_MovParcela]")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, descricao, textoTag, tamanho);
                        } else if (mar.contains("Descricao_MovParcela_Sem_Renegociadas]")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, descricao, textoTag, tamanho);
                        }else {
                            return ("<br>Tag " + mar + " não existe<br>");
                        }
                    }
                }
            }
            texto = retirarLinhasHtml(texto, marcador.getTag());
        }
        return texto;
    }

    public String substituirTagTurma(String texto, ContratoVO contrato, MarcadorVO marcador) throws Exception {
        Iterator i = contrato.getContratoModalidadeVOs().iterator();
        String emBranco = obterStringEmBrancoParaTag(marcador);
        while (i.hasNext()) {
            ContratoModalidadeVO contratoMod = (ContratoModalidadeVO) i.next();
            tagGeral = marcador.getTag();
            if (contratoMod.getContratoModalidadeTurmaVOs().size() != 0) {
                Iterator j = contratoMod.getContratoModalidadeTurmaVOs().iterator();
                while (j.hasNext()) {
                    texto = gerarLinhasHtml(texto, marcador);
                    ContratoModalidadeTurmaVO modalidadeTurma = (ContratoModalidadeTurmaVO) j.next();
                    TurmaVO turma = modalidadeTurma.getTurma();
                    String codigo = turma.getCodigo().toString();
                    if (codigo.equals("0") || codigo.equals("")) {
                        codigo = emBranco;
                    }
                    String descricao = turma.getDescricao();
                    if (descricao.equals("")) {
                        descricao = emBranco;
                    }
                    String identificador = turma.getIdentificador();
                    if (identificador.equals("")) {
                        identificador = emBranco;
                    }
                    String dataInicioVigencia = turma.getDataInicialVigencia_Apresentar();
                    if (dataInicioVigencia.equals("")) {
                        dataInicioVigencia = emBranco;
                    }
                    String dataFinalVigencia = turma.getDataFinalVigencia_Apresentar();
                    if (dataFinalVigencia.equals("")) {
                        dataFinalVigencia = emBranco;
                    }
                    String idadeMinima = turma.getIdadeMinima().toString();
                    if (idadeMinima.equals("")) {
                        idadeMinima = emBranco;
                    }
                    String idadeMaxima = turma.getIdadeMaxima().toString();
                    if (idadeMaxima.equals("0") || idadeMaxima.equals("")) {
                        idadeMaxima = emBranco;
                    }
                    String diaDaSemana = obterDiaDaSemanaTurmaCliente(modalidadeTurma, false);
                    if (diaDaSemana.equals("")) {
                        diaDaSemana = emBranco;
                    }

                    String diaDaSemanaResumido = obterDiaDaSemanaTurmaCliente(modalidadeTurma, true);
                    if (diaDaSemanaResumido.equals("")) {
                        diaDaSemanaResumido = emBranco;
                    }
                    tagGeral = marcador.getTag();

                    while ((texto.indexOf(tagGeral) != -1) && !tagGeral.equals("")) {
                        int tamanho = obterTamanhoTag(tagGeral);
                        String textoTag = obterTextoTag(tagGeral);
                        String mar = obterTagSemTextoSemTamanhoQuandoConcatenadaAOutraTag(tagGeral);

                        if (mar.contains("Codigo_Turma")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, codigo, textoTag, tamanho);
                        } else if (mar.contains("Descricao_Turma")) {
                            descricao += " : " + diaDaSemana;
                            texto = substituirTagQuandoConcatenadaAOutrasTAGsComQuebra(texto, mar, descricao, textoTag, tamanho);
                        } else if (mar.contains("DescricaoCurta_Turma")) {
                            descricao += ": " + diaDaSemanaResumido;
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, descricao, textoTag, tamanho);
                        } else if (mar.contains("Identificador_Turma")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, identificador, textoTag, tamanho);
                        } else if (mar.contains("DataInicioVigencia_Turma")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, dataInicioVigencia, textoTag, tamanho);
                        } else if (mar.contains("DataFinalVigencia_Turma")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, dataFinalVigencia, textoTag, tamanho);
                        } else if (mar.contains("IdadeMinima_Turma")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, idadeMinima, textoTag, tamanho);
                        } else if (mar.contains("IdadeMaxima_Turma")) {
                            texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, idadeMaxima, textoTag, tamanho);
                        } else {
                            return ("<br>Tag " + mar + " não existe<br>");
                        }
                    }
                }
            }
        }
        texto = retirarLinhasHtml(texto, marcador.getTag());
        return texto;
    }

    public String substituirTagComposicao(String texto, ContratoVO contrato, MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        tagGeral = marcador.getTag();
        if (contrato.getPlano().getPlanoComposicaoVOs().size() == 0) {
            texto = texto.replace(marcador.getTag(), emBranco);
        } else {
            Iterator i = contrato.getContratoComposicaoVOs().iterator();
            while (i.hasNext()) {
                if (contrato.getContratoComposicaoVOs().size() > 1) {
                    texto = gerarLinhasHtml(texto, marcador);
                }
                ContratoComposicaoVO contratoComp = (ContratoComposicaoVO) i.next();
                ComposicaoVO comp = contratoComp.getComposicaoVO();
                String codigo = comp.getCodigo().toString();
                if (codigo.equals("0") || codigo.equals("")) {
                    codigo = emBranco;
                }
                String descricao = comp.getDescricao();
                if (descricao.equals("")) {
                    descricao = emBranco;
                }
                String preco = comp.getPrecoComposicao_Apresentar();
                if (preco.equals("0") || preco.equals("")) {
                    preco = emBranco;
                }
                tagGeral = marcador.getTag();

                while ((texto.indexOf(obterTagSemTextoSemTamanho(tagGeral)) != -1) && !tagGeral.equals("")) {
                    int tamanho = obterTamanhoTag(tagGeral);
                    String textoTag = obterTextoTag(tagGeral);
                    String mar = obterTagSemTextoSemTamanhoQuandoConcatenadaAOutraTag(tagGeral);

                    if (mar.contains("Codigo_Composicao")) {
                        texto = substituirTag(texto, mar, codigo, textoTag, tamanho);
                    } else if (mar.contains("Descricao_Composicao")) {
                        texto = substituirTag(texto, mar, descricao, textoTag, tamanho);
                    } else if (mar.contains("PrecoComposicao_Composicao")) {
                        texto = substituirTag(texto, mar, preco, textoTag, tamanho);
                    } else {
                        return ("<br>Tag " + mar + " não existe<br>");
                    }
                }
            }
            texto = retirarLinhasHtml(texto, marcador.getTag());
        }
        return texto;
    }

    public String substituirTagMovPagamento(String texto, List<MovPagamentoVO> movPagamentoVOs,
            MarcadorVO marcador) throws Exception {
        String emBranco = obterStringEmBrancoParaTag(marcador);
        tagGeral = marcador.getTag();
        if (movPagamentoVOs.isEmpty()) {
            texto = texto.replace(marcador.getTag(), emBranco);
        } else {
            //prepara lista de MovPagamentoVO incluindo os cheques
            ArrayList<MovPagamentoVO> listaTempMovPagVOs = new ArrayList<MovPagamentoVO>();
            listaTempMovPagVOs.addAll(movPagamentoVOs);
            Iterator j = listaTempMovPagVOs.iterator();
            ArrayList<MovPagamentoVO> listaTempDestMovPagVOs = new ArrayList<MovPagamentoVO>();
            listaTempDestMovPagVOs.addAll(movPagamentoVOs);
            while (j.hasNext()) {
                MovPagamentoVO movPagamentoVO = (MovPagamentoVO) j.next();
                MovPagamentoVO.adicionarChequesEmListaMovPagamento(listaTempDestMovPagVOs,
                        movPagamentoVO.getChequeVOs());
            }
            Iterator i = listaTempDestMovPagVOs.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamentoVO = (MovPagamentoVO) i.next();
                if ((movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CH"))
                        && (movPagamentoVO.getChequeTransiente() == null)) {
                    continue;
                }
                texto = gerarLinhasHtml(texto, marcador);
                String tipoFormaPagamento = movPagamentoVO.getFormaPagamento().getTipoFormaPagamento_Apresentar();
                if (tipoFormaPagamento.equals("")) {
                    tipoFormaPagamento = emBranco;
                }
                FormatadorNumerico format = new FormatadorNumerico();
                String valor = format.getAsString(context(), null, movPagamentoVO.getValor());
                if (valor.equals("0") || valor.equals("")) {
                    valor = emBranco;
                }
                tagGeral = marcador.getTag();
                String dataCompensacaoCheque = "";
                String bancoCheque = "";
                String agenciaCheque = "";
                String contaCorrenteCheque = "";
                String numeroCheque = "";
                if (movPagamentoVO.getChequeTransiente() != null) {
                    dataCompensacaoCheque = movPagamentoVO.getChequeTransiente().getDataCompensacao_Apresentar();
                    bancoCheque = movPagamentoVO.getChequeTransiente().getBanco().getNome();
                    agenciaCheque = movPagamentoVO.getChequeTransiente().getAgencia();
                    contaCorrenteCheque = movPagamentoVO.getChequeTransiente().getConta();
                    numeroCheque = movPagamentoVO.getChequeTransiente().getNumero();
                }
                while ((texto.indexOf(tagGeral) != -1) && !tagGeral.equals("")) {
                    int tamanho = obterTamanhoTag(tagGeral);
                    String textoTag = obterTextoTag(tagGeral);
                    String mar = obterTagSemTextoSemTamanhoQuandoConcatenadaAOutraTag(tagGeral);
                    if (mar.contains("tipoFormaPagamento_MovPagamento")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, tipoFormaPagamento, textoTag, tamanho);
                    } else if (mar.contains("valor_MovPagamento")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto,
                                mar,
                                movPagamentoVO.getChequeTransiente() != null ? "" : valor,
                                textoTag, tamanho);
                    } //tag  para representar o nr de parcelas que o cliente escolheu para pagamento em cartão de crédito
                    else if (mar.contains("parcelasCC_MovPagamento")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto,
                                mar,
                                UteisValidacao.emptyNumber(movPagamentoVO.getNrParcelaCartaoCredito())
                                ? emBranco : movPagamentoVO.getNrParcelaCartaoCredito().toString(),
                                UteisValidacao.emptyNumber(movPagamentoVO.getNrParcelaCartaoCredito())
                                ? emBranco : textoTag,
                                tamanho);
                    }else if (mar.contains("operadoraCC_MovPagamento")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto,
                                mar,
                                UteisValidacao.emptyString(movPagamentoVO.getOperadoraCartaoVO().getDescricao())
                                        ? emBranco : movPagamentoVO.getOperadoraCartaoVO().getDescricao(),
                                UteisValidacao.emptyString(movPagamentoVO.getOperadoraCartaoVO().getDescricao())
                                        ? emBranco : textoTag,
                                tamanho);
                    }else if (mar.contains("dataCompensacao_Cheque")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar,
                                movPagamentoVO.getChequeTransiente() != null ? dataCompensacaoCheque : "",
                                movPagamentoVO.getChequeTransiente() != null ? textoTag : "",
                                tamanho);
                    } else if (mar.contains("valor_Cheque")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar,
                                movPagamentoVO.getChequeTransiente() != null ? valor : "",
                                movPagamentoVO.getChequeTransiente() != null ? textoTag : "",
                                tamanho);
                    } else if (mar.contains("banco_Cheque")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar,
                                movPagamentoVO.getChequeTransiente() != null ? bancoCheque : "",
                                movPagamentoVO.getChequeTransiente() != null ? textoTag : "",
                                tamanho);
                    } else if (mar.contains("agencia_Cheque")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar,
                                movPagamentoVO.getChequeTransiente() != null ? agenciaCheque : "",
                                movPagamentoVO.getChequeTransiente() != null ? textoTag : "",
                                tamanho);
                    } else if (mar.contains("contaCorrente_Cheque")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar,
                                movPagamentoVO.getChequeTransiente() != null ? contaCorrenteCheque : "",
                                movPagamentoVO.getChequeTransiente() != null ? textoTag : "",
                                tamanho);
                    } else if (mar.contains("numero_Cheque")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar,
                                movPagamentoVO.getChequeTransiente() != null ? numeroCheque : "",
                                movPagamentoVO.getChequeTransiente() != null ? textoTag : "",
                                tamanho);
                    } else {
                        return ("<br>Tag '" + mar + "' não existe<br>");
                    }
                }
            }
            texto = retirarLinhasHtml(texto, marcador.getTag());
        }
        return texto;
    }

    private String getRegexSubTag(String nomeSubTag) {
        String regexSubTag = Pattern.quote("(") + "[\\d]*" + Pattern.quote(")")
                + Pattern.quote("{") + "*" + Pattern.quote("}") + nomeSubTag;
        return regexSubTag;
    }

    private String getRegexTag() {
        String regexTag = Pattern.quote("[") + ".*?" + Pattern.quote("]");
        return regexTag;
    }

    public String substituirTagReciboPagamento(String texto, List<ReciboPagamentoVO> reciboPagamentoVOs, MarcadorVO marcador, String descMoedaEmpresa) throws Exception {
        FormatadorNumerico fmt = new FormatadorNumerico();
        String emBranco = obterStringEmBrancoParaTag(marcador);
        tagGeral = marcador.getTag();
        if (reciboPagamentoVOs.size() == 0) {
            texto = texto.replace(marcador.getTag(), emBranco);
        } else {
            for (ReciboPagamentoVO reciboPagamentoVO1 : reciboPagamentoVOs) {
                texto = gerarLinhasHtml(texto, marcador);

                NumeroPorExtenso npe = new NumeroPorExtenso(reciboPagamentoVO1.getValorTotal(), descMoedaEmpresa);

                String valor = reciboPagamentoVO1.getValorTotal().toString();
                valor = fmt.getAsString(context(), null, valor);
                if (valor.equals("")) {
                    valor = emBranco;
                }

                tagGeral = marcador.getTag();
                while ((texto.contains(tagGeral)) && !tagGeral.equals("")) {
                    int tamanho = obterTamanhoTag(tagGeral);
                    String textoTag = obterTextoTag(tagGeral);
                    String mar = obterTagSemTextoSemTamanhoQuandoConcatenadaAOutraTag(tagGeral);

                    if (mar.contains("codigo_Recibo")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, reciboPagamentoVO1.getCodigo().toString(), textoTag, tamanho);
                    } else if (mar.contains("valorTotal_Recibo")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, valor, textoTag, tamanho);
                    } else if (mar.contains("valorPorExtenso_Recibo")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, npe.toString(), textoTag, tamanho);
                    } else if (mar.contains("nomePessoaPagador_Recibo")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, reciboPagamentoVO1.getNomePessoaPagador(), textoTag, tamanho);
                    } else if (mar.contains("responsavelLancamento_Recibo")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, reciboPagamentoVO1.getResponsavelLancamento().getNome(), textoTag, tamanho);
                    } else if (mar.contains("contrato_Recibo")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, reciboPagamentoVO1.getContrato().getCodigo().toString(), textoTag, tamanho);
                    } else if (mar.contains("data_Recibo")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar, reciboPagamentoVO1.getData_Apresentar(), textoTag, tamanho);
                    } else if (mar.contains("dataImpressao_Recibo")) {
                        texto = substituirTagQuandoConcatenadaAOutrasTAGs(texto, mar,
                                DateFormat.getDateTimeInstance(DateFormat.MEDIUM,
                                        DateFormat.MEDIUM).format(negocio.comuns.utilitarias.Calendario.hoje()), textoTag, tamanho);
                    } else {
                        return ("<br>Tag " + mar + " não existe<br>");
                    }
                }
            }
            texto = retirarLinhasHtml(texto, marcador.getTag());
        }
        return texto;
    }

    public String substituirTagReciboPagamentoEmBranco(String texto, MarcadorVO marcador) throws Exception {
        int tamanho = obterTamanhoTag(marcador.getTag());
        String mar = obterTagSemTextoSemTamanho(marcador.getTag());
        String textoTag = obterTextoTag(marcador.getTag());
        String emBranco = obterStringEmBrancoParaTag(marcador);
        if (mar.equalsIgnoreCase("codigo_Recibo")) {
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        } else if (mar.equalsIgnoreCase("valorTotal_Recibo")) {
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        } else if (mar.equalsIgnoreCase("valorPorExtenso_Recibo")) {
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        } else if (mar.equalsIgnoreCase("pessoaPagador_Recibo")) {
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        } else if (mar.equalsIgnoreCase("nomePessoaPagador_Recibo")) {
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        } else if (mar.equalsIgnoreCase("responsavelLancamento_Recibo")) {
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        } else if (mar.equalsIgnoreCase("contrato_Recibo")) {
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        } else if (mar.equalsIgnoreCase("data_Recibo")) {
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        } else if (mar.equalsIgnoreCase("dataImpressao_Recibo")) {
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        } else {
            texto = substituirTag(texto, marcador.getTag(), emBranco, textoTag, tamanho);
        }
        return texto;
    }

    // Metodo responsável por retornar uma String com caracteres em branco, de acordo com o tamanho da tag
    // assim, a rotina substitui a tag, deixando assim os campos contrato em branco quando não possuem dados.
    public String obterStringEmBrancoParaTag(MarcadorVO marcador) {
        int tamanho = marcador.getTag().length();
        String retorno = "";
        int cont = 0;
        while (cont < tamanho) {
            retorno += " ";
            cont++;
        }
        return retorno;
    }

    public int obterTamanhoTag(String tag) throws Exception {
        if (!UteisValidacao.emptyString(tag) &&
                tag.equalsIgnoreCase("[(50){}Descricao_Plano]")) { // Paliativa para troca de tamanho de tag não impactar antigos
            tag = "[(200){}Descricao_Plano]";
        }
        int posicaoIni = tag.indexOf("(");
        int posicaoFim = tag.indexOf(")");
        if ((posicaoIni == -1) || (posicaoFim == -1)) {
            return 0;
        }
        Integer num = new Integer(tag.substring(posicaoIni + 1, posicaoFim));
        return num.intValue();
    }

    public String obterTextoTag(String tag) {
        int posicaoIni = tag.indexOf("{");
        int posicaoFim = tag.indexOf("}");
        if ((posicaoIni == -1) || (posicaoFim == -1)) {
            return "";
        }

        return tag.substring(posicaoIni + 1, posicaoFim);
    }

    public String obterTagSemTextoSemTamanhoQuandoConcatenadaAOutraTag(String tag) {
        int posicao1 = tag.indexOf(",");
        if (posicao1 != -1) {
            tagGeral = tag.substring(posicao1 + 1);
            return tag.substring(0, posicao1 + 1);
        } else {
            tagGeral = "";
            return tag;
        }
    }

    public String obterTagSemTextoSemTamanho(String tag) {
        int posicao1 = tag.indexOf(")");
        int posicao2 = tag.indexOf("}");
        if (posicao2 != -1) {
            return tag.substring(posicao2 + 1, tag.length() - 1);
        } else if (posicao1 != -1) {
            return tag.substring(posicao1 + 1, tag.length() - 1);
        } else {
            return "";
        }
    }

    public String obterTagSemTextoSemTamanhoTagComposta(String tag) {
        String stringFinal = tag;
        while ((stringFinal.indexOf("(") != -1) && (stringFinal.indexOf("}") != -1)) {
            int posicao1 = stringFinal.indexOf("(");
            int posicao2 = stringFinal.indexOf(")");
            if (posicao1 != -1) {
                stringFinal = stringFinal.substring(0, posicao1) + stringFinal.substring(posicao2 + 1);
            }
            int posicao3 = stringFinal.indexOf("{");
            int posicao4 = stringFinal.indexOf("}");
            if (posicao3 != -1) {
                stringFinal = stringFinal.substring(0, posicao3) + stringFinal.substring(posicao4 + 1);
            }
            if ((posicao1 != -1) && (posicao3 != -1)) {
                stringFinal += "";
            }
        }
        return stringFinal;
    }

    public String substituirTag(String texto, String tag, String valor, String textoTag, int tamanhoOcupacaoTag) {
        return substituirTag(texto, tag, valor, textoTag, tamanhoOcupacaoTag, true);
    }

    public String substituirTag(String texto, String tag, String valor, String textoTag, int tamanhoOcupacaoTag, boolean apresentarLabelConteudoVazio) {
        int tam = valor.length();
        if (tamanhoOcupacaoTag < tam) {
            valor = valor.substring(0, tamanhoOcupacaoTag);
        }
        if (!apresentarLabelConteudoVazio && UteisValidacao.emptyString(valor.trim())) {
            texto = texto.replace(tag, valor);
        } else {
            texto = texto.replace(tag, textoTag + valor);
        }
        return texto;
    }

    public String substituirTagQuandoConcatenadaAOutrasTAGsComQuebra(String texto, String tag,
            String valor, String textoTag, int tamanhoOcupacaoTag) {
        int tam = valor.length();
        if (tamanhoOcupacaoTag < tam) {
            valor = valor.substring(0, tamanhoOcupacaoTag);
        }
        String novoTexto = texto.substring(0, texto.indexOf(tag) + tag.length());
        novoTexto = novoTexto.replace(tag, textoTag + valor + "</br>");
        texto = novoTexto + texto.substring(texto.indexOf(tag) + tag.length());
        return texto;
    }

    public String substituirTagQuandoConcatenadaAOutrasTAGs(String texto, String tag,
            String valor, String textoTag, int tamanhoOcupacaoTag) {
        int tam = valor.length();
        if (tamanhoOcupacaoTag < tam) {
            valor = valor.substring(0, tamanhoOcupacaoTag);
        }
        String novoTexto = texto.substring(0, texto.indexOf(tag) + tag.length());
        novoTexto = novoTexto.replace(tag, textoTag + valor);
        texto = novoTexto + texto.substring(texto.indexOf(tag) + tag.length());
        return texto;
    }

    public String obterTelefoneCliente(PessoaVO pessoa) {
        try {
            String telefone = "";
            String telefone1 = "";
            if (pessoa.getTelefoneVOs().size() != 0) {
                if (pessoa.getTelefoneVOs().size() <= 1) {
                    Iterator i = pessoa.getTelefoneVOs().iterator();
                    while (i.hasNext()) {
                        TelefoneVO tel = (TelefoneVO) i.next();
                        telefone = tel.getNumero();
                        return telefone;
                    }
                } else {
                    Iterator i = pessoa.getTelefoneVOs().iterator();
                    while (i.hasNext()) {
                        TelefoneVO tel = (TelefoneVO) i.next();
                        if (tel.getTipoTelefone().equals("RE")) {
                            telefone = tel.getNumero();
                        } else {
                            telefone1 = tel.getNumero();
                        }
                    }
                    if (telefone.equals("")) {
                        telefone = telefone1;
                    }
                }
            }
            return telefone;
        } catch (Exception e) {
            return "";
        }
    }

    public String obterTelefoneClientePorTipo(PessoaVO pessoa, String tipo) {
        try {
            String telefone = "";
            for (TelefoneVO tel : pessoa.getTelefoneVOs()) {
                if (tel.getTipoTelefone().equals(tipo)) {
                    telefone += ", " + tel.getNumero();
                }
            }
            telefone = telefone.replaceFirst(",", "");

            return telefone;
        } catch (Exception e) {
            return "";
        }
    }

    public String obterNumeroEnderecoCliente(PessoaVO pessoa) {
        try {
            String numero = "";
            String numero1 = "";
            if (pessoa.getEnderecoVOs().size() != 0) {
                if (pessoa.getEnderecoVOs().size() <= 1) {
                    EnderecoVO end = (EnderecoVO) pessoa.getEnderecoVOs().get(0);
                    numero = end.getNumero();
                    return numero;
                } else {
                    for (Object o : pessoa.getEnderecoVOs()) {
                        EnderecoVO end = (EnderecoVO) o;
                        if (end.getEnderecoCorrespondencia()) {
                            numero = end.getNumero();
                            return numero;
                        } else {
                            numero1 = end.getNumero();
                        }
                    }
                }
            }
            if (numero.equals("")) {
                numero = numero1;
            }
            return numero;
        } catch (Exception e) {
            return "";
        }
    }

    public String obterEnderecoCliente(PessoaVO pessoa) {
        try {
            String endereco = "";
            String endereco1 = "";
            if (pessoa.getEnderecoVOs().size() != 0) {
                if (pessoa.getEnderecoVOs().size() <= 1) {
                    Iterator i = pessoa.getEnderecoVOs().iterator();
                    while (i.hasNext()) {
                        EnderecoVO end = (EnderecoVO) i.next();
                        endereco = (end.getEndereco() + ", Nº: " + end.getNumero());
                        return endereco;
                    }
                } else {
                    Iterator i = pessoa.getEnderecoVOs().iterator();
                    while (i.hasNext()) {
                        EnderecoVO end = (EnderecoVO) i.next();
                        if (end.getEnderecoCorrespondencia()) {
                            endereco = (end.getEndereco() + ", Nº: " + end.getNumero());
                            return endereco;
                        } else {
                            endereco1 = end.getEndereco() + ", Nº: " + end.getNumero();
                        }
                    }
                }
            }
            if (endereco.equals("")) {
                endereco = endereco1;
            }
            return endereco;
        } catch (Exception e) {
            return "";
        }
    }

    public String obterCepCliente(PessoaVO pessoa) {
        try {
            String endereco = "";
            String endereco1 = "";
            if (pessoa.getEnderecoVOs().size() != 0) {
                if (pessoa.getEnderecoVOs().size() <= 1) {
                    Iterator i = pessoa.getEnderecoVOs().iterator();
                    while (i.hasNext()) {
                        EnderecoVO end = (EnderecoVO) i.next();
                        endereco = (end.getCep());
                        return endereco;
                    }
                } else {
                    Iterator i = pessoa.getEnderecoVOs().iterator();
                    while (i.hasNext()) {
                        EnderecoVO end = (EnderecoVO) i.next();
                        if (end.getEnderecoCorrespondencia()) {
                            endereco = (end.getCep());
                            return endereco;
                        } else {
                            endereco1 = end.getCep();
                        }
                    }
                }
            }
            if (endereco.equals("")) {
                endereco = endereco1;
            }
            return endereco;
        } catch (Exception e) {
            return "";
        }
    }

    public String obterComplemento(PessoaVO pessoa) {
        try {
            String complemento = "";
            String complemento1 = "";
            if (pessoa.getEnderecoVOs().size() != 0) {
                if (pessoa.getEnderecoVOs().size() <= 1) {
                    Iterator i = pessoa.getEnderecoVOs().iterator();
                    while (i.hasNext()) {
                        EnderecoVO end = (EnderecoVO) i.next();
                        complemento = (end.getComplemento());
                        return complemento;
                    }
                } else {
                    Iterator i = pessoa.getEnderecoVOs().iterator();
                    while (i.hasNext()) {
                        EnderecoVO end = (EnderecoVO) i.next();
                        if (end.getEnderecoCorrespondencia()) {
                            complemento = (end.getComplemento());
                            return complemento;
                        } else {
                            complemento1 = end.getComplemento();
                        }
                    }
                }
            }
            if (complemento.equals("")) {
                complemento = complemento1;
            }
            return complemento;
        } catch (Exception e) {
            return "";
        }
    }

    public String obterBairro(PessoaVO pessoa){
        try {
            String bairro = "";
            String bairro1 = "";
            if (pessoa.getEnderecoVOs().size() != 0) {
                if (pessoa.getEnderecoVOs().size() <= 1) {
                    Iterator i = pessoa.getEnderecoVOs().iterator();
                    while (i.hasNext()) {
                        EnderecoVO end = (EnderecoVO) i.next();
                        bairro = (end.getBairro());
                        return bairro;
                    }
                } else {
                    Iterator i = pessoa.getEnderecoVOs().iterator();
                    while (i.hasNext()) {
                        EnderecoVO end = (EnderecoVO) i.next();
                        if (end.getEnderecoCorrespondencia()) {
                            bairro = (end.getBairro());
                            return bairro;
                        } else {
                            bairro1 = end.getBairro();
                        }
                    }
                }
            }
            if (bairro.equals("")) {
                bairro = bairro1;
            }
            return bairro;
        } catch (Exception e) {
            return "";
        }

    }

    public String obterEmailCliente(PessoaVO pessoa) {
        String email = "";
        for (Object obj : pessoa.getEmailVOs()) {
            EmailVO em = (EmailVO) obj;
            email += ", " + em.getEmail();
        }
        email = email.replaceFirst(",", "");
        return email;
    }

    public String obterDiaDaSemanaTurmaCliente(ContratoModalidadeTurmaVO contratoModalidadeTurma, boolean resumido) {
        try {
            String diaDaSemana = "";
            if (!contratoModalidadeTurma.getContratoModalidadeHorarioTurmaVOs().isEmpty()) {

                for (int i = 0; i < contratoModalidadeTurma.getContratoModalidadeHorarioTurmaVOs().size(); i++) {
                    ContratoModalidadeHorarioTurmaVO contratoModalidade = (ContratoModalidadeHorarioTurmaVO) contratoModalidadeTurma.getContratoModalidadeHorarioTurmaVOs().get(i);
                    DiaSemana diaDaSemanaFormatado = DiaSemana.getDiaSemana(contratoModalidade.getHorarioTurma().getDiaSemana());
                    if (resumido) {
                        if (i == 0) {
                            diaDaSemana = diaDaSemanaFormatado.getCodigo() + " " + contratoModalidade.getHorarioTurma().getHoraInicial() + " - " + contratoModalidade.getHorarioTurma().getHoraFinal();
                        } else {
                            diaDaSemana += " / " + diaDaSemanaFormatado.getCodigo() + " " + contratoModalidade.getHorarioTurma().getHoraInicial() + " - " + contratoModalidade.getHorarioTurma().getHoraFinal();
                        }
                    } else {
                        if (i == 0) {
                            diaDaSemana = diaDaSemanaFormatado.getDescricao() + " " + contratoModalidade.getHorarioTurma().getHoraInicial() + "-" + contratoModalidade.getHorarioTurma().getHoraFinal();
                        } else {
                            diaDaSemana += " / " + diaDaSemanaFormatado.getDescricao() + " " + contratoModalidade.getHorarioTurma().getHoraInicial() + "-" + contratoModalidade.getHorarioTurma().getHoraFinal();
                        }
                    }


                }
            }
            return diaDaSemana;
        } catch (Exception e) {
            return "";
        }
    }

    public UsuarioVO getResponsavelDefinicao() {
        if (responsavelDefinicao == null) {
            responsavelDefinicao = new UsuarioVO();
        }
        return responsavelDefinicao;
    }

    public void setResponsavelDefinicao(UsuarioVO responsavelDefinicao) {
        this.responsavelDefinicao = responsavelDefinicao;
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (<code>PlanoTextoPadrao</code>).
     */
    public String getTexto() {
        if (texto == null) {
            texto = "";
        }
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public Date getDataDefinicao() {
        if (dataDefinicao == null) {
            dataDefinicao = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return dataDefinicao;
    }

    public String getDataDefinicao_Apresentar() {
        return Formatador.formatarDataPadrao(dataDefinicao);
    }

    public void setDataDefinicao(Date dataDefinicao) {
        this.dataDefinicao = dataDefinicao;
    }

    public String setDataDefinicao_Apresentar(Date dataDefinicao) {
        this.dataDefinicao = dataDefinicao;
        return Formatador.formatarDataPadrao(dataDefinicao);
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public List getListaTagUtilizado() {
        if (listaTagUtilizado == null) {
            listaTagUtilizado = new ArrayList();
        }
        return listaTagUtilizado;
    }

    public void setListaTagUtilizado(List listaTagUtilizado) {
        this.listaTagUtilizado = listaTagUtilizado;
    }

    public byte[] getImagemLogo() {
        return imagemLogo;
    }

    public void setImagemLogo(byte[] imagemLogo) {
        this.imagemLogo = imagemLogo;
    }

    public String getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(String tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public boolean isTermoAceite() {
        return getTipoContrato().equals("VO");
    }

    public boolean isTermoAceiteLink() {
        return getTipoContrato().equals("LP");
    }

    public boolean isApresentarMarcadores() {
        return getTipoContrato() != null && !getTipoContrato().equals("AM") && !getTipoContrato().equals("VO") && !getTipoContrato().equals("LP") && !getTipoContrato().equals("TR");
    }

    public String contratoEmBranco(int codigoEmpresa, Connection con, PlanoVO plano) throws Exception {
        try {
            if (getTexto().length() != 0) {
                char aspas = '"';
                String espaco = "<p style=" + aspas + "text-align: left;" + aspas + ">";
                String htmlBotao = "<form> ";
                String textoSub = getTexto();
                textoSub = textoSub.replace("Untitled document", "CONTRATO DE PRESTA&Ccedil;&Atilde;O DE SERVI&Ccedil;OS");
                int posicaoBody = textoSub.indexOf("<body>");
                String parte1 = textoSub.substring(0, posicaoBody + 6);
                String parte2 = textoSub.substring(posicaoBody + 6);
                textoSub = parte1 + htmlBotao + parte2;

                posicaoBody = textoSub.indexOf("</body>");
                parte1 = textoSub.substring(0, posicaoBody);
                parte2 = textoSub.substring(posicaoBody);
                textoSub = parte1 + parte2;

                textoSub = carregarTagsNoModeloContratoExibidoVendasOnline(textoSub, plano);
                textoSub = varrerListaTagUtilizadoTextoEmBranco(codigoEmpresa, textoSub, con, plano.getSite());
                return textoSub;
            }
        } catch (Exception e) {
            throw e;
        }
        return "";
    }

    public void removerComentariosHtml() {
        this.texto = Uteis.removerComentariosHtml(this.texto);
    }

    public String carregarTagsNoModeloContratoExibidoVendasOnline(String texto, PlanoVO plano) {
        //Para contratos Recorrentes exibidos no Vendas Online
        String valorParcelaMensal = "";
        if(plano.getPlanoRecorrencia() != null && !UteisValidacao.emptyNumber(plano.getPlanoRecorrencia().getCodigo())){
            valorParcelaMensal = Formatador.formatarValorMonetario(plano.getPlanoRecorrencia().getValorMensal());
        } else if (!UteisValidacao.emptyList(plano.getPlanoExcecaoVOs())){
            valorParcelaMensal = Formatador.formatarValorMonetario(plano.getPlanoExcecaoVOs().get(0).getValor());
        }
        texto = substituirTag(texto, "[(50){}valorParcelaMensal_Contrato]", valorParcelaMensal, "", 50);
        texto = substituirTag(texto, "[(50){}Descricao_Plano]", plano.getDescricao_Apresentar(), "", 50);
        texto = substituirTag(texto, "[(200){}Descricao_Plano]", plano.getDescricao_Apresentar(), "", 200);

        if (plano.getPlanoRecorrencia() != null && !UteisValidacao.emptyNumber(plano.getPlanoRecorrencia().getCodigo())) {
            Double valorContratoSemDesconto = (plano.getPlanoRecorrencia().getValorMensal() * plano.getPlanoRecorrencia().getDuracaoPlano()) + plano.getPlanoRecorrencia().getTaxaAdesao();
            Double valorFinal = 0.0;

            // Verifica se o plano possui parcelas editadas
            if (!UteisValidacao.emptyList(plano.getPlanoRecorrencia().getParcelas())) {
                // Caso tenha, calcula o valor final do contrato
                Integer numeroParcelasEditadas = plano.getPlanoRecorrencia().getParcelas().size();
                Double valorParcelasNormalRetirar = plano.getPlanoRecorrencia().getValorMensal() * numeroParcelasEditadas;
                Double valorParcelasEditadas = 0.0;
                for (PlanoRecorrenciaParcelaVO parcela : plano.getPlanoRecorrencia().getParcelas()) {
                    valorParcelasEditadas += parcela.getValor();
                }
                valorFinal = valorContratoSemDesconto - valorParcelasNormalRetirar + valorParcelasEditadas;
            } else {
                // Caso não tenha parcelas editadas, o valor final é o valor do contrato sem desconto
                valorFinal = valorContratoSemDesconto;
            }

            String valorFinalString = Formatador.formatarValorMonetario(valorFinal);
            texto = substituirTag(texto, "[(15){}ValorFinal_Contrato]", valorFinalString, "", 15);
            texto = substituirTag(texto, "[(10){}Duracao_Contrato]", plano.getPlanoRecorrencia().getDuracaoPlano().toString(), "", 10);
        }
        if (!UteisValidacao.emptyList(plano.getPlanoDuracaoVOs()) && !UteisValidacao.emptyList(plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs()) &&
            !UteisValidacao.emptyString(plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getDescricao())) {
            texto = substituirTag(texto, "[(50){}CondicaoPagamento_Contrato]", plano.getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getDescricao(), "", 50);
        }

        return texto;
    }

}
