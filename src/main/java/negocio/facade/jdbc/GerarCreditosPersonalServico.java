/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.UsoCreditoPersonalEnum;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.PacotePersonalVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.ColaboradorInterfaceFacade;
import negocio.interfaces.financeiro.VendaAvulsaInterfaceFacade;
import negocio.interfaces.plano.ProdutoInterfaceFacade;
import servicos.integracao.TreinoWSConsumer;

/**
 *
 * <AUTHOR>
 */
public class GerarCreditosPersonalServico {

    private Connection con;

    public GerarCreditosPersonalServico(Connection con) {
        this.con = con;
    }

    public String gerarCreditosColaborador(String key, Integer codigoRecibo) {
        try {
            ColaboradorInterfaceFacade colaboradorDao = new Colaborador(con);
            Integer empresa = null;
            // CORREÇÃO DE SEGURANÇA: Usar PreparedStatement para prevenir SQL injection
            String sqlEmpresa = "SELECT empresa FROM recibopagamento WHERE codigo = ?";
            try (PreparedStatement psEmpresa = con.prepareStatement(sqlEmpresa)) {
                psEmpresa.setInt(1, codigoRecibo);
                try (ResultSet rsEmpresa = psEmpresa.executeQuery()) {
                    if (rsEmpresa.next()) {
                        empresa = rsEmpresa.getInt("empresa");
                    }
                }
            }

            String sqlColaborador = "SELECT c.codigo FROM colaborador c " +
                    "WHERE c.pessoa = (SELECT pessoapagador FROM recibopagamento WHERE codigo = ?) " +
                    "and c.empresa = ?";
            ResultSet rsColaborador;
            try (PreparedStatement psColaborador = con.prepareStatement(sqlColaborador)) {
                psColaborador.setInt(1, codigoRecibo);
                psColaborador.setInt(2, empresa);
                rsColaborador = psColaborador.executeQuery();
            }
            Date dataExpiracao = null;
            Integer colaborador = null;
            if (rsColaborador.next()) {
                colaborador = rsColaborador.getInt("codigo");
            }
            if(colaborador == null){
                ResultSet rsColaboradorEmpresa = SuperFacadeJDBC.criarConsulta("SELECT c.codigo FROM colaborador c\n"
                        + "WHERE c.pessoa = (SELECT pessoapagador FROM recibopagamento WHERE codigo = " + codigoRecibo + ")", con);
                if (rsColaboradorEmpresa.next()) {
                    colaborador = rsColaboradorEmpresa.getInt("codigo");
                }
            }
            if(colaborador != null){
                Integer quantidadeCreditos = 0;
                ResultSet rsCreditos = SuperFacadeJDBC.criarConsulta(" SELECT p.tipoproduto, mp.quantidade, mp.datafinalvigencia, mp.codigo FROM movproduto mp \n"
                        + " INNER JOIN produto p ON p.codigo = mp.produto AND p.tipoproduto = 'CP'\n"
                        + " INNER JOIN movprodutoparcela mpp ON mpp.movproduto = mp.codigo AND mpp.recibopagamento = " + codigoRecibo
                        + " INNER JOIN movparcela mpar ON mpar.codigo = mpp.movparcela AND mpar.descricao NOT LIKE 'FECHAMENTO DE CREDITOS DE PERSONAL'", con);
                while (rsCreditos.next()) {
                    quantidadeCreditos = quantidadeCreditos + rsCreditos.getInt("quantidade");
                    if(rsCreditos.getDate("datafinalvigencia") != null
                            && (dataExpiracao == null || Calendario.maior(rsCreditos.getDate("datafinalvigencia"), dataExpiracao))){
                        dataExpiracao = rsCreditos.getDate("datafinalvigencia");
                    }
                }
                if (quantidadeCreditos > 0) {
                    ResultSet rsSaldo = SuperFacadeJDBC.criarConsulta("SELECT saldoCreditoPersonal FROM colaborador WHERE codigo = " + colaborador, con);
                    if (rsSaldo.next()) {
                        Integer saldo = rsSaldo.getInt("saldoCreditoPersonal");
                        colaboradorDao.atualizarSaldoPersonal(colaborador, saldo + quantidadeCreditos);
                    }
                    return TreinoWSConsumer.inserirCreditos(key, colaborador, quantidadeCreditos, codigoRecibo, dataExpiracao);
                }
            }
            return "ok";
        } catch (Exception e) {
            return "Erro: " + e.getMessage();
        }

    }

    public String gerarVendaCreditos(Integer codigoColaborador, Integer unidadesCredito, Integer codigoUsuario) {
        try {
            Conexao.guardarConexaoForJ2SE(con);
            ProdutoInterfaceFacade produtoDao = new Produto(con);
            ColaboradorInterfaceFacade colaboradorDao = new Colaborador(con);
            UsuarioInterfaceFacade usuarioDao = new Usuario(con);
            UsuarioVO usuario = usuarioDao.consultarPorCodigo(codigoUsuario, Uteis.NIVELMONTARDADOS_MINIMOS);
            ColaboradorVO colaborador = colaboradorDao.consultarPorCodigo(codigoColaborador, 0, Uteis.NIVELMONTARDADOS_TODOS);
            ProdutoVO produto;
            List<ProdutoVO> produtos = produtoDao.consultarPorDescricaoTipoProduto(
                    colaborador.temTipoColaborador(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla()) ? "CRÉDITO DE PERSONAL INTERNO" : "CRÉDITO DE PERSONAL EXTERNO",
                    TipoProduto.CREDITO_PERSONAL.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            if (produtos != null && !produtos.isEmpty()) {
                produto = produtos.get(0);
                produto.setDescricao("FECHAMENTO DE CREDITOS DE PERSONAL");
                VendaAvulsaVO vendaAvulsa = new VendaAvulsaVO();
                vendaAvulsa.setDescricaoAdicional("FECHAMENTO DE CREDITOS DE PERSONAL");
                vendaAvulsa.setColaborador(colaborador);
                vendaAvulsa.setNomeComprador(colaborador.getPessoa().getNome());
                vendaAvulsa.setDataRegistro(Calendario.hoje());
                vendaAvulsa.setEmpresa(colaborador.getEmpresa());
                vendaAvulsa.setResponsavel(usuario);
                unidadesCredito = unidadesCredito < 0 ? unidadesCredito * -1 : unidadesCredito;
                ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
                item.setProduto(produto);
                item.setQuantidade(unidadesCredito);
                List<PacotePersonalVO> pacotes = produtoDao.consultarPacotesPersonal(produto.getCodigo());

                if (produto.getValorFinal() > 0.0 || pacotes.isEmpty()) {
                    item.setValorParcial(produto.getValorFinal() * unidadesCredito);
                } else {
                    PacotePersonalVO pacote = pacotes.get(0);
                    item.setPacotePersonal(pacote.getCodigo());
                    Double valor;
                    if (colaborador.getUsoCreditosPersonal() != null && colaborador.getUsoCreditosPersonal().equals(UsoCreditoPersonalEnum.PERMITIR_POS_PAGO.getCodigo())) {
                        valor = pacote.getValorPosPago();
                    } else {
                        valor = pacote.getValorPrePago();
                    }
                    Double valorPacote = valor / pacote.getQuantidade();
                    item.setValorParcial(valorPacote * unidadesCredito);
                }
                vendaAvulsa.setValorTotal(item.getValorParcial());
                vendaAvulsa.setTipoComprador("CO");
                vendaAvulsa.getItemVendaAvulsaVOs().add(item);
                VendaAvulsaInterfaceFacade vendaAvulsaDao = new VendaAvulsa(con);
                Integer codigoVenda = vendaAvulsaDao.incluir(vendaAvulsa, false, null, null, null);
                return "ok|"+codigoVenda;
            }
            return "Erro: Produto não encontrado";
        } catch (Exception e) {
            return "Erro: " + e.getMessage();
        }
    }
}
