package negocio.facade.jdbc.arquitetura;

import br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaColaborador;
import br.com.pactosolucoes.autorizacaocobranca.interfaces.AutorizacaoCobrancaClienteInterfaceFacade;
import br.com.pactosolucoes.autorizacaocobranca.interfaces.AutorizacaoCobrancaColaboradorInterfaceFacade;
import br.com.pactosolucoes.bi.service.impl.BiMSService;
import br.com.pactosolucoes.bi.service.impl.BiMSServiceImpl;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CentralEventosFacade;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.evento.TipoAmbiente;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.interessado.FormaContato;
import br.com.pactosolucoes.ce.negocio.interfaces.evento.TipoAmbienteInterfaceFacade;
import br.com.pactosolucoes.ecf.cupomfiscal.comuns.dao.CupomFiscal;
import br.com.pactosolucoes.ecf.cupomfiscal.comuns.servico.impl.CupomFiscalServiceImpl;
import br.com.pactosolucoes.ecf.cupomfiscal.comuns.servico.interfaces.CupomFiscalServiceFacade;
import br.com.pactosolucoes.estudio.dao.AgendaEstudio;
import br.com.pactosolucoes.estudio.dao.AmbienteIndisp;
import br.com.pactosolucoes.estudio.dao.ColaboradorIndisp;
import br.com.pactosolucoes.estudio.dao.ConfiguracaoEstudio;
import br.com.pactosolucoes.estudio.dao.Disponibilidade;
import br.com.pactosolucoes.estudio.dao.EmpresaFechada;
import br.com.pactosolucoes.estudio.dao.Pacote;
import br.com.pactosolucoes.estudio.dao.PreferenciaAgenda;
import br.com.pactosolucoes.estudio.dao.RelProdutoAmbiente;
import br.com.pactosolucoes.estudio.dao.RelProdutoColaborador;
import br.com.pactosolucoes.estudio.dao.RelatorioComissao;
import br.com.pactosolucoes.estudio.dao.RelatorioFechamentoDiario;
import br.com.pactosolucoes.estudio.dao.RelatorioGeralAgendamentos;
import br.com.pactosolucoes.estudio.dao.RelatorioGeralClientesInativos;
import br.com.pactosolucoes.estudio.dao.TipoHorario;
import br.com.pactosolucoes.estudio.interfaces.AgendaEstudioInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.AmbienteIndispInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.ColaboradorIndispInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.ConfiguracaoEstudioInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.DisponibilidadeInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.EmpresaFechadaInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.PacoteInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.PreferenciaAgendaInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.RelProdutoAmbienteInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.RelProdutoColaboradorInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.RelatorioComissaoInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.RelatorioFechamentoDiarioInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.RelatorioGeralAgendamentosInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.RelatorioGeralClientesInativosInterfaceFacade;
import br.com.pactosolucoes.estudio.interfaces.TipoHorarioInterfaceFacade;
import br.com.pactosolucoes.linhatempocontrato.dao.LinhaTempoContrato;
import br.com.pactosolucoes.linhatempocontrato.interfaces.LinhaTempoContratoInterfaceFacade;
import br.com.pactosolucoes.socialmailing.dao.SocialMail;
import br.com.pactosolucoes.socialmailing.dao.SocialMailGrupo;
import br.com.pactosolucoes.socialmailing.interfaces.SocialMailGrupoInterfaceFacade;
import br.com.pactosolucoes.socialmailing.interfaces.SocialMailingInterfaceFacade;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import negocio.comuns.financeiro.IntegracaoKobanaVO;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.acesso.AcessoColaborador;
import negocio.facade.jdbc.acesso.AutorizacaoAcessoGrupoEmpresarial;
import negocio.facade.jdbc.acesso.Camera;
import negocio.facade.jdbc.acesso.Coletor;
import negocio.facade.jdbc.acesso.IntegracaoAcessoGrupoEmpresarial;
import negocio.facade.jdbc.acesso.LiberacaoAcesso;
import negocio.facade.jdbc.acesso.LocalAcesso;
import negocio.facade.jdbc.acesso.ServidorFacial;
import negocio.facade.jdbc.acesso.ValidacaoLocalAcesso;
import negocio.facade.jdbc.armario.Armario;
import negocio.facade.jdbc.armario.TamanhoArmario;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.basico.renovacaosintetico.RenovacaoSintetico;
import negocio.facade.jdbc.contrato.AfastamentoContratoDependente;
import negocio.facade.jdbc.contrato.Atestado;
import negocio.facade.jdbc.contrato.ComissaoGeralConfiguracao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoAssinaturaDigital;
import negocio.facade.jdbc.contrato.ContratoComposicao;
import negocio.facade.jdbc.contrato.ContratoCondicaoPagamento;
import negocio.facade.jdbc.contrato.ContratoDuracao;
import negocio.facade.jdbc.contrato.ContratoDuracaoCreditoTreino;
import negocio.facade.jdbc.contrato.ContratoHorario;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.contrato.ContratoModalidadeCredito;
import negocio.facade.jdbc.contrato.ContratoModalidadeHorarioTurma;
import negocio.facade.jdbc.contrato.ContratoModalidadeProdutoSugerido;
import negocio.facade.jdbc.contrato.ContratoModalidadeTurma;
import negocio.facade.jdbc.contrato.ContratoModalidadeVezesSemana;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.ContratoPlanoProdutoSugerido;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.contrato.Convenio;
import negocio.facade.jdbc.contrato.ConvenioDesconto;
import negocio.facade.jdbc.contrato.ConvenioDescontoConfiguracao;
import negocio.facade.jdbc.contrato.HistoricoContrato;
import negocio.facade.jdbc.contrato.JustificativaOperacao;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;
import negocio.facade.jdbc.contrato.OperacaoColetiva;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.contrato.PlanoPersonalAssinaturaDigital;
import negocio.facade.jdbc.contrato.PlanoPersonalTextoPadrao;
import negocio.facade.jdbc.contrato.ReajusteContrato;
import negocio.facade.jdbc.contrato.TrancamentoContrato;
import negocio.facade.jdbc.contrato.UtilizacaoAvaliacaoFisica;
import negocio.facade.jdbc.crm.*;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.facade.jdbc.crm.optin.Optin;
import negocio.facade.jdbc.estoque.Balanco;
import negocio.facade.jdbc.estoque.BalancoItens;
import negocio.facade.jdbc.estoque.Cardex;
import negocio.facade.jdbc.estoque.Compra;
import negocio.facade.jdbc.estoque.CompraItens;
import negocio.facade.jdbc.estoque.ProdutoEstoque;
import negocio.facade.jdbc.estoque.CompraDocumentos;
import negocio.facade.jdbc.estoque.ContaFinanceiroCompra;
import negocio.facade.jdbc.feed.FeedGestao;
import negocio.facade.jdbc.feed.PaginaInicialFeed;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.financeiro.openbanking.stone.ContaStone;
import negocio.facade.jdbc.financeiro.openbanking.stone.PagamentoStone;
import negocio.facade.jdbc.financeiro.openbanking.stone.RetornoStone;
import negocio.facade.jdbc.financeiro.openbanking.stone.TransferenciaStone;
import negocio.facade.jdbc.integracoes.IntegracaoSesi;
import negocio.facade.jdbc.memcached.Wiki;
import negocio.facade.jdbc.nfe.EmpresaNFe;
import negocio.facade.jdbc.nfe.ItemRPSNFe;
import negocio.facade.jdbc.nfe.LoteNFe;
import negocio.facade.jdbc.nfe.MunicipioNFe;
import negocio.facade.jdbc.nfe.NotaFiscalDeServico;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.notaFiscal.NotaFiscalFamilia;
import negocio.facade.jdbc.notaFiscal.NotaFiscalHistorico;
import negocio.facade.jdbc.notaFiscal.NotaFiscalOperacao;
import negocio.facade.jdbc.pactoprint.CarteirinhaCliente;
import negocio.facade.jdbc.pactoprint.LocalImpressao;
import negocio.facade.jdbc.plano.*;
import negocio.facade.jdbc.utilitarias.Cep;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.facade.jdbc.utilitarias.ConfiguracaoSistemaUsuario;
import negocio.facade.jdbc.utilitarias.NotificacaoUsuario;
import negocio.facade.jdbc.utilitarias.Solicitacao;
import negocio.facade.jdbc.vendas.VendasConfig;
import negocio.facade.jdbc.vendas.VendasOnlineCampanha;
import negocio.facade.jdbc.vendas.VendasOnlineConvenio;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioTentativa;
import negocio.interfaces.OperacaoColetivaInterfaceFacade;
import negocio.interfaces.acesso.AcessoClienteInterfaceFacade;
import negocio.interfaces.acesso.AcessoColaboradorInterfaceFacade;
import negocio.interfaces.acesso.AutorizacaoAcessoGrupoEmpresarialInterfaceFacade;
import negocio.interfaces.acesso.CameraInterfaceFacade;
import negocio.interfaces.acesso.ColetorInterfaceFacade;
import negocio.interfaces.acesso.IntegracaoAcessoGrupoEmpresarialInterfaceFacade;
import negocio.interfaces.acesso.LiberacaoAcessoInterfaceFacade;
import negocio.interfaces.acesso.LocalAcessoInterfaceFacade;
import negocio.interfaces.acesso.ServidorFacialInterfaceFacade;
import negocio.interfaces.acesso.ValidacaoLocalAcessoInterfaceFacade;
import negocio.interfaces.armario.ArmarioInterfaceFacade;
import negocio.interfaces.armario.TamanhoArmarioInterfaceFacade;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.interfaces.arquitetura.LogProcessoSistemaInterfaceFacade;
import negocio.interfaces.arquitetura.LogTotalPassInterfaceFacade;
import negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade;
import negocio.interfaces.arquitetura.PerfilUsuarioNFeInterfaceFacade;
import negocio.interfaces.arquitetura.ResultadoServicosInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioEmailInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioNFeIntefaceFacade;
import negocio.interfaces.arquitetura.UsuarioTelefoneInterfaceFacade;
import negocio.interfaces.basico.*;
import negocio.interfaces.contrato.AfastamentoContratoDependenteInterfaceFacade;
import negocio.interfaces.contrato.AtestadoInterfaceFacade;
import negocio.interfaces.contrato.ComissaoGeralConfiguracaoInterfaceFacade;
import negocio.interfaces.contrato.ContratoAssinaturaDigitalInterfaceFacade;
import negocio.interfaces.contrato.ContratoDuracaoCreditoTreinoInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import negocio.interfaces.contrato.ContratoModalidadeCreditoInterfaceFacade;
import negocio.interfaces.contrato.ContratoModalidadeHorarioTurmaInterfaceFacade;
import negocio.interfaces.contrato.ContratoModalidadeTurmaInterfaceFacade;
import negocio.interfaces.contrato.ContratoOperacaoInterfaceFacade;
import negocio.interfaces.contrato.ContratoRecorrenciaInterfaceFacade;
import negocio.interfaces.contrato.ContratoTextoPadraoInterfaceFacade;
import negocio.interfaces.contrato.ControleCreditoTreinoInterfaceFacade;
import negocio.interfaces.contrato.ConvenioDescontoInterfaceFacade;
import negocio.interfaces.contrato.ConvenioInterfaceFacade;
import negocio.interfaces.contrato.HistoricoContratoInterfaceFacade;
import negocio.interfaces.contrato.JustificativaOperacaoInterfaceFacade;
import negocio.interfaces.contrato.MatriculaAlunoHorarioTurmaInterfaceFacade;
import negocio.interfaces.contrato.MovProdutoInterfaceFacade;
import negocio.interfaces.contrato.MovProdutoModalidadeInterfaceFacade;
import negocio.interfaces.contrato.PeriodoAcessoClienteInterfaceFacade;
import negocio.interfaces.contrato.PlanoPersonalAssinaturaDigitalInterfaceFacade;
import negocio.interfaces.contrato.PlanoPersonalTextoPadraoInterfaceFacade;
import negocio.interfaces.contrato.ReajusteContratoInterfaceFacade;
import negocio.interfaces.contrato.TrancamentoContratoInterfaceFacade;
import negocio.interfaces.contrato.UtilizacaoAvaliacaoFisicaInterfaceFacade;
import negocio.interfaces.crm.AberturaMetaInterfaceFacade;
import negocio.interfaces.crm.AgendaInterfaceFacade;
import negocio.interfaces.crm.ConfiguracaoSistemaCRMInterfaceFacade;
import negocio.interfaces.crm.ConversaoLeadInterfaceFacade;
import negocio.interfaces.crm.DefinirLayoutInterfaceFacade;
import negocio.interfaces.crm.EventoInterfaceFacade;
import negocio.interfaces.crm.FecharMetaDetalhadoInterfaceFacade;
import negocio.interfaces.crm.FecharMetaInterfaceFacade;
import negocio.interfaces.crm.FeriadoInterfaceFacade;
import negocio.interfaces.crm.GrupoColaboradorInterfaceFacade;
import negocio.interfaces.crm.HistoricoContatoInterfaceFacade;
import negocio.interfaces.crm.IndicacaoInterfaceFacade;
import negocio.interfaces.crm.IndicadoInterfaceFacade;
import negocio.interfaces.crm.LeadInterfaceFacade;
import negocio.interfaces.crm.MailingAgendamentoInterfaceFacade;
import negocio.interfaces.crm.MailingHistoricoInterfaceFacade;
import negocio.interfaces.crm.MalaDiretaCRMExtraClienteInterfaceFacade;
import negocio.interfaces.crm.MalaDiretaCRMExtraColaboradorInterfaceFacade;
import negocio.interfaces.crm.MalaDiretaInterfaceFacade;
import negocio.interfaces.crm.ModeloMensagemInterfaceFacade;
import negocio.interfaces.crm.ObjecaoInterfaceFacade;
import negocio.interfaces.crm.PassivoInterfaceFacade;
import negocio.interfaces.crm.QuarentenaInterfaceFacade;
import negocio.interfaces.crm.TextoPadraoInterfaceFacade;
import negocio.interfaces.crm.TiposVinculosFaseInterfaceFacade;
import negocio.interfaces.crm.optin.OptinInterfaceFacade;
import negocio.interfaces.estoque.BalancoInterfaceFacade;
import negocio.interfaces.estoque.BalancoItensInterfaceFacade;
import negocio.interfaces.estoque.CardexInterfaceFacade;
import negocio.interfaces.estoque.CompraInterfaceFacade;
import negocio.interfaces.estoque.CompraItensInterfaceFacade;
import negocio.interfaces.estoque.ProdutoEstoqueInterfaceFacade;
import negocio.interfaces.estoque.CompraDocumentosInterfaceFacade;
import negocio.interfaces.estoque.ContaFinanceiroCompraInterfaceFacade;
import negocio.interfaces.feed.FeedGestaoInterfaceFacade;
import negocio.interfaces.feed.PaginaInicialInterfaceFacade;
import negocio.interfaces.financeiro.*;
import negocio.interfaces.financeiro.openbanking.stone.ContaStoneInterfaceFacade;
import negocio.interfaces.financeiro.openbanking.stone.PagamentoStoneInterfaceFacade;
import negocio.interfaces.financeiro.openbanking.stone.RetornoStoneInterfaceFacade;
import negocio.interfaces.financeiro.openbanking.stone.TransferenciaStoneInterfaceFacade;
import negocio.interfaces.integracoes.IntegracaoSesiInterfaceFacade;
import negocio.interfaces.nfe.EmpresaNFeInterfaceFacade;
import negocio.interfaces.nfe.ItemRPSNFeInterfaceFacade;
import negocio.interfaces.nfe.LoteNFeInterfaceFacade;
import negocio.interfaces.nfe.MunicipioNFeInterfaceFacade;
import negocio.interfaces.nfe.NotaFiscalDeServicoInterfaceFacade;
import negocio.interfaces.notaFiscal.NotaFiscalFamiliaInterfaceFacade;
import negocio.interfaces.notaFiscal.NotaFiscalHistoricoInterfaceFacade;
import negocio.interfaces.notaFiscal.NotaFiscalInterfaceFacade;
import negocio.interfaces.notaFiscal.NotaFiscalOperacaoInterfaceFacade;
import negocio.interfaces.pactoprint.CarteirinhaClienteInterfaceFacade;
import negocio.interfaces.pactoprint.LocalImpressaoInterfaceFacade;
import negocio.interfaces.plano.AmbienteInterfaceFacade;
import negocio.interfaces.plano.CategoriaProdutoInterfaceFacade;
import negocio.interfaces.plano.ComposicaoInterfaceFacade;
import negocio.interfaces.plano.CondicaoPagamentoInterfaceFacade;
import negocio.interfaces.plano.ConfiguracaoProdutoEmpresaInterfaceFacade;
import negocio.interfaces.plano.ConvenioCobrancaEmpresaInterfaceFacade;
import negocio.interfaces.plano.DescontoInterfaceFacade;
import negocio.interfaces.plano.DescontoRenovacaoInterfaceFacade;
import negocio.interfaces.plano.DuracaoInterfaceFacade;
import negocio.interfaces.plano.HistoricoProfessorTurmaInterfaceFacade;
import negocio.interfaces.plano.HorarioDisponibilidadeInterfaceFacade;
import negocio.interfaces.plano.HorarioInterfaceFacade;
import negocio.interfaces.plano.HorarioTurmaInterfaceFacade;
import negocio.interfaces.plano.ImpostoProdutoCfopInterfaceFacade;
import negocio.interfaces.plano.ModalidadeInterfaceFacade;
import negocio.interfaces.plano.ModeloOrcamentoInterfaceFacade;
import negocio.interfaces.plano.NivelTurmaInterfaceFacade;
import negocio.interfaces.plano.OrcamentoInterfaceFacade;
import negocio.interfaces.plano.OrcamentoRelInterfaceFacade;
import negocio.interfaces.plano.PlanoDuracaoCreditoTreinoInterfaceFacade;
import negocio.interfaces.plano.PlanoExcecaoInterfaceFacade;
import negocio.interfaces.plano.PlanoInterfaceFacade;
import negocio.interfaces.plano.PlanoModalidadeVezesSemanaInterfaceFacade;
import negocio.interfaces.plano.PlanoProdutoSugeridoInterfaceFacade;
import negocio.interfaces.plano.PlanoTextoPadraoInterfaceFacade;
import negocio.interfaces.plano.ProdutoInterfaceFacade;
import negocio.interfaces.plano.ProdutoMescladoInterfaceFacade;
import negocio.interfaces.plano.ProdutoSugeridoInterfaceFacade;
import negocio.interfaces.plano.TipoModalidadeInterfaceFacade;
import negocio.interfaces.plano.TurmaInterfaceFacade;
import negocio.interfaces.plano.VezesSemanaInterfaceFacade;
import negocio.interfaces.vendas.VendasConfigInterfaceFacade;
import negocio.interfaces.vendas.VendasOnlineConvenioInterfaceFacade;
import negocio.interfaces.vendas.VendasOnlineConvenioTentativaInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelInterfaceFacade;
import negocio.tokenOperacao.TokenOperacao;
import negocio.vendasOnline.TokenVendasOnline;
import relatorio.controle.crm.CarteirasRel;
import relatorio.negocio.jdbc.basico.*;
import relatorio.negocio.jdbc.financeiro.ProdutoRel;
import relatorio.negocio.jdbc.financeiro.RelatorioRepasseRel;
import relatorio.negocio.jdbc.financeiro.TicketMedio;
import relatorio.negocio.jdbc.sad.MetaCrescimento;
import relatorio.negocio.jdbc.sad.RelatorioCliente;
import relatorio.negocio.jdbc.sad.RotatividadeAnaliticoDW;
import relatorio.negocio.jdbc.sad.RotatividadeSinteticoDW;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import relatorio.negocio.jdbc.sad.SituacaoContratoAnaliticoDW;
import relatorio.negocio.jdbc.sad.SituacaoContratoSinteticoDW;
import servicos.adm.CreditoDCCService;
import servicos.impl.cliente.VendaRapidaRecorrenteServiceImpl;
import servicos.impl.conviteaulaexperimental.ConviteAulaExperimentalService;
import servicos.impl.kobana.IntegracaoKobanaService;
import servicos.impl.pluggy.PluggyService;
import servicos.impl.redepay.ERedeServiceConciliacao;
import servicos.impl.turma.HistoricoProfessorTurmaServiceImpl;
import servicos.integracao.intranet.service.DashboardService;
import servicos.integracao.mgb.impl.MgbServiceImpl;
import servicos.integracao.mgb.intf.MgbService;
import servicos.interfaces.ConviteAulaExperimentalServiceInterface;
import servicos.interfaces.cliente.VendaRapidaRecorrenteService;
import servicos.interfaces.turma.HistoricoProfessorTurmaService;
import servicos.pix.PixService;

import javax.servlet.http.HttpSession;
import java.io.Serializable;
import java.sql.Connection;

public class FacadeFactory implements Serializable {

    // pacote acesso
    private AcessoClienteInterfaceFacade acessoCliente;
    private LiberacaoAcessoInterfaceFacade liberacaoAcesso;
    private AcessoColaboradorInterfaceFacade acessoColaborador;
    private ColetorInterfaceFacade coletor;
    private LocalAcessoInterfaceFacade localAcesso;
    private ValidacaoLocalAcessoInterfaceFacade validacaoLocalAcesso;
    private IntegracaoAcessoGrupoEmpresarialInterfaceFacade integracao;
    private AutorizacaoAcessoGrupoEmpresarialInterfaceFacade autorizacao;
    private ServidorFacialInterfaceFacade servidorFacial;
    private CameraInterfaceFacade camera;
    // pacote arquitetura
    private ControleAcesso controleAcesso;
    private LogInterfaceFacade log;
    private LogProcessoSistema logProcessoSistema;
    private LogTotalPassInterfaceFacade logtotalpass;
    private PerfilAcessoInterfaceFacade perfilAcesso;
    private Permissao permissao;
    private Risco risco;
    private Robo robo;
    private UsuarioInterfaceFacade usuario;
    private UsuarioPerfilAcesso usuarioPerfilAcesso;
    private UsuarioNFeIntefaceFacade usuarioNFe;
    private PerfilUsuarioNFeInterfaceFacade perfilUsuarioNFe;
    // pacote basico
    private ConviteInterfaceFacade convite;
    private EstornoObservacaoInterfaceFacade estornoObservacao;
    private ObservacaoOperacaoInterfaceFacade observacaoOperacao;
    private ClienteObservacaoInterfaceFacade clienteObservacao;
    private CategoriaInterfaceFacade categoria;
    private CidadeInterfaceFacade cidade;
    private PessoaInterfaceFacade pessoa;
    private EstadoInterfaceFacade estado;
    private ClassificacaoInterfaceFacade classificacao;
    private ClienteInterfaceFacade cliente;
    private ClienteRestricaoInterfaceFacade clienteRestricao;
    private LinhaTempoContratoInterfaceFacade linhaTempoContrato;
    private ClienteClassificacao clienteClassificacao;
    private ClienteGrupo clienteGrupo;
    private ClienteMensagemInterfaceFacade clienteMensagem;
    private ColaboradorInterfaceFacade colaborador;
    private ColaboradorModalidade colaboradorModalidade;
    private ConfiguracaoSistemaInterfaceFacade configuracaoSistema;
    private ConfiguracaoSistemaCadastroClienteInterfaceFacade configuracaoSistemaCadastroCliente;
    private ContaCorrenteEmpresa contaCorrenteEmpresa;
    private ConfiguracaoReenvioMovParcelaEmpresaInterfaceFacade configuracaoReenvioMovParcelaEmpresa;
    private MovParcelaTentativaConvenioInterfaceFacade movParcelaTentativaConvenio;
    private MovParcelaResultadoCobranca movParcelaResultadoCobranca;
    private Email email;
    private EmpresaInterfaceFacade empresa;
    private ConfiguracaoEmpresaTotemInterfaceFacade configuracaoEmpresaTotem;
    private Endereco endereco;
    private Familiar familiar;
    private GrauInstrucaoInterfaceFacade grauInstrucao;
    private GrupoInterfaceFacade grupo;
    private Favorito favorito;
    private LogControleUsabilidadeInterfaceFacade logControleUsabilidade;
    private MovimentoContaCorrenteClienteInterfaceFacade movimentoContaCorrenteCliente;
    private MovimentoContaCorrenteClienteComposicaoInterfaceFacade movimentoContaCorrenteClienteComposicao;
    private PaisInterfaceFacade pais;
    private ParentescoInterfaceFacade parentesco;
    private PerguntaInterfaceFacade pergunta;
    private PerguntaClienteInterfaceFacade perguntaCliente;
    private ProfissaoInterfaceFacade profissao;
    private QuestionarioInterfaceFacade questionario;
    private QuestionarioClienteInterfaceFacade questionarioCliente;
    private QuestionarioPergunta questionarioPergunta;
    private QuestionarioPerguntaCliente questionarioPerguntaCliente;
    private RespostaPergCliente respostaPergCliente;
    private RespostaPerguntaInterfaceFacade respostaPergunta;
    private Telefone telefone;
    private TipoColaboradorInterfaceFacade tipoColaborador;
    private VinculoInterfaceFacade vinculo;
    private UsuarioMovelInterfaceFacade usuarioMovel;
    private UsuarioEmailInterfaceFacade usuarioEmail;
    private UsuarioTelefoneInterfaceFacade usuarioTelefone;
    private ArquivoInterfaceFacade arquivo;
    private PlacaInterfaceFacade placa;
    private LogAjusteGeralInterfaceFacade logAjusteGeral;
    private NotificacaoUsuarioInterfaceFacade notificacaoUsuario;
    private RenovacaoSinteticoFacade renovacaoSinteticoFacade;
    private GympassInterfaceFacade gympass;
    private VendasConfigInterfaceFacade vendas;
    private VendasOnlineConvenioInterfaceFacade vendasOnlineConvenio;
    private VendasOnlineConvenioTentativaInterfaceFacade vendasOnlineConvenioTentativa;
    private GympassDiaInterfaceFacade gympassDia;
    // pacote contrato
    private ContratoInterfaceFacade contrato;
    private ContratoComposicao contratoComposicao;
    private ContratoCondicaoPagamento contratoCondicaoPagamento;
    private ContratoDuracao contratoDuracao;
    private ContratoHorario contratoHorario;
    private ContratoModalidade contratoModalidade;
    private ContratoModalidadeHorarioTurmaInterfaceFacade contratoModalidadeHorarioTurma;
    private ContratoModalidadeProdutoSugerido contratoModalidadeProdutoSugerido;
    private ContratoModalidadeTurmaInterfaceFacade contratoModalidadeTurma;
    private ContratoModalidadeVezesSemana contratoModalidadeVezesSemana;
    private ContratoModalidadeCreditoInterfaceFacade contratoModalidadeCredito;
    private ContratoOperacaoInterfaceFacade contratoOperacao;
    private ContratoPlanoProdutoSugerido contratoPlanoProdutoSugerido;
    private ContratoTextoPadraoInterfaceFacade contratoTextoPadrao;
    private ContratoDependenteInterfaceFacade contratoDependente;
    private AfastamentoContratoDependenteInterfaceFacade afastamentoContratoDependente;
    private PlanoPersonalTextoPadraoInterfaceFacade planoPersonalTextoPadrao;
    private ConvenioInterfaceFacade convenio;
    private ConvenioDescontoInterfaceFacade convenioDesconto;
    private ConvenioDescontoConfiguracao convenioDescontoConfiguracao;
    private HistoricoContratoInterfaceFacade historicoContrato;
    private JustificativaOperacaoInterfaceFacade justificativaOperacao;
    private MatriculaAlunoHorarioTurmaInterfaceFacade matriculaAlunoHorarioTurma;
    private MovProdutoInterfaceFacade movProduto;
    private UtilizacaoAvaliacaoFisicaInterfaceFacade utilizacaoAvaliacaoFisica;
    private PeriodoAcessoClienteInterfaceFacade periodoAcessoCliente;
    private TrancamentoContratoInterfaceFacade trancamentoContrato;
    private ReciboDevolucao reciboDevolucao;
    private MovProdutoModalidadeInterfaceFacade movProdutoModalidade;
    private ComissaoGeralConfiguracaoInterfaceFacade comissaoGeralConfiguracao;
    private ComissaoProdutoConfiguracaoInterfaceFacade comissaoProdutoConfiguracao;
    private AtestadoInterfaceFacade atestado;
    // pacote crm
    private ConfiguracaoSistemaCRMInterfaceFacade configuracaoSistemaCRM;
    private ConfiguracaoDiasPosVenda configuracaoDiasPosVenda;
    private DefinirLayoutInterfaceFacade definirLayout;
    private FeriadoInterfaceFacade feriado;
    private GrupoColaboradorInterfaceFacade grupoColaborador;
    private GrupoColaboradorParticipante grupoColaboradorParticipante;
    private IndicacaoInterfaceFacade indicacao;
    private IndicadoInterfaceFacade indicado;
    private MalaDiretaInterfaceFacade malaDireta;
    private MailingAgendamentoInterfaceFacade mailingAgendamento;
    private MailingHistoricoInterfaceFacade mailingHistorico;
    private MalaDiretaEnviada malaDiretaEnviada;
    private ModeloMensagemInterfaceFacade modeloMensagem;
    private PassivoInterfaceFacade passivo;
    private ClientePotencial clientePotencial;
    private FaixaHorarioAcessoCliente faixaHorarioAcessoCliente;
    private TiposVinculosFaseInterfaceFacade tiposVinculosFase;
    private MalaDiretaCRMExtraClienteInterfaceFacade malaDiretaCRMExtraCliente;
    private MalaDiretaCRMExtraColaboradorInterfaceFacade malaDiretaCRMExtraColaborador;
    private QuarentenaInterfaceFacade quarentena;
    // pacote financeiro
    private AulaAvulsaDiariaInterfaceFacade aulaAvulsaDiaria;
    private BancoInterfaceFacade banco;
    private ConfiguracaoNotaFiscalInterfaceFacade configuracaoNotaFiscal;
    private ConfiguracaoNotaFiscalAmbienteInterfaceFacade configuracaoNotaFiscalAmbiente;
    private NotaFiscalInterfaceFacade notaFiscal;
    private NotaFiscalOperacaoInterfaceFacade notaFiscalOperacao;
    private NotaFiscalHistoricoInterfaceFacade notaFiscalHistorico;
    private NotaFiscalFamiliaInterfaceFacade notaFiscalFamilia;
    private Cheque cheque;
    private ContaCorrenteInterfaceFacade contaCorrente;
    private ContaBancariaFornecedorInterfaceFacade contaBancariaFornecedor;
    private ProdutoDevolverCancelamentoEmpresa produtoDevolverCancelamentoEmpresa;
    private FormaPagamentoInterfaceFacade formaPagamento;
    private AdquirenteInterfaceFacade adquirente;
    private FormaPagamentoEmpresaInterfaceFacade formaPagamentoEmpresa;
    private FormaPagamentoPerfilAcessoInterfaceFacade formaPagamentoPerfilAcesso;
    private PinPadInterfaceFacade pinpad;
    private GestaoNotasInterfaceFacade gestaoNotas;
    private ItemVendaAvulsa itemVendaAvulsa;
    private ExtratoDiarioItemInterfaceFacade extratoDiarioItem;
    private MovPagamentoInterfaceFacade movPagamento;
    private MovParcelaInterfaceFacade movParcela;
    private MovProdutoParcela movProdutoParcela;
    private PagamentoMovParcela pagamentoMovParcela;
    private ReciboPagamento reciboPagamento;
    private ReciboClienteConsultorInterfaceFacade reciboClienteConsultor;
    private TipoRemessaInterfaceFacade tipoRemessa;
    private TipoRetornoInterfaceFacade tipoRetorno;
    private VendaAvulsaInterfaceFacade vendaAvulsa;
    private CartaoCredito cartaoCredito;
    private OperadoraCartaoInterfaceFacade operadoraCartao;
    private TaxaCartao taxaCartao;
    private TaxaBoleto taxaBoleto;
    private ConfiguracaoMovimentacaoAutomaticaInterfaceFacade movimentacaoAutomatica;
    private CreditoDCCService creditoDCC;
    private LogCobrancaPactoInterfaceFacade logCobrancaPacto;
    private BoletoPJBankInterfaceFacade boletoPJBankInterfaceFacade;
    private LogPJBankInterfaceFacade logPJBankInterfaceFacade;
    private BoletoInterfaceFacade boletoInterfaceFacade;
    //Módulo Financeiro
    private FinanceiroFacade financeiro;
    private ContaStoneInterfaceFacade contaStone;
    private PagamentoStoneInterfaceFacade pagamentoStone;
    private TransferenciaStoneInterfaceFacade transferenciaStone;
    private RetornoStoneInterfaceFacade retornoStone;
    private MetaFinanceiroBI metaFinanceiroBI;
    private MovContaRateioInterfaceFacade movContaRateio;
    private MovContaInterfaceFacade movConta;
    private CaixaMovContaInterfaceFacade caixaMovConta;
    private CaixaInterfaceFacade caixa;
    // pacote plano
    private MovContaTransactionPluggyInterfaceFacade movContaTransactionPluggy;
    private PluggyItemInterfaceFacade pluggyItem;
    private IntegracaoKobanaInterfaceFacade integracaoKobana;
    private LoteKobanaInterfaceFacade loteKobana;
    private LoteKobanaItemInterfaceFacade loteKobanaItem;
    private PluggyAccountBloqueio pluggyAccountBloqueio;
    private AmbienteInterfaceFacade ambiente;
    private CategoriaProdutoInterfaceFacade categoriaProduto;
    private ComposicaoInterfaceFacade composicao;
    private ComposicaoModalidade composicaoModalidade;
    private CondicaoPagamentoInterfaceFacade condicaoPagamento;
    private CondicaoPagamentoParcela condicaoPagamentoParcela;
    private DescontoInterfaceFacade desconto;
    private DescontoRenovacaoInterfaceFacade descontoRenovacao;
    private DuracaoInterfaceFacade duracao;
    private HorarioInterfaceFacade horario;
    private HorarioDisponibilidadeInterfaceFacade horarioDisponibilidade;
    private HorarioTurmaInterfaceFacade horarioTurma;
    private ModalidadeInterfaceFacade modalidade;
    private ModalidadeEmpresa modalidadeEmpresa;
    private TipoModalidadeInterfaceFacade tipoModalidade;
    private NivelTurmaInterfaceFacade nivelTurma;
    private PlanoComposicao planoComposicao;
    private PlanoCategoria planoCategoria;
    private PlanoCondicaoPagamento planoPagamento;
    private PlanoDuracao planoDuracao;
    private PlanoHorario planoHorario;
    private PlanoModalidade planoModalidade;
    private PlanoModalidadeVezesSemanaInterfaceFacade planoModalidadeVezesSemana;
    private PlanoProdutoSugeridoInterfaceFacade planoProdutoSugerido;
    private PlanoTextoPadraoInterfaceFacade planoTextoPadrao;
    private ModeloOrcamentoInterfaceFacade modeloOrcamento;
    private OrcamentoRelInterfaceFacade orcamentoRel;
    private OrcamentoInterfaceFacade orcamento;
    private PlanoTextoPadraoTag planoTextoPadraoTag;
    private PlanoRecorrencia planoRecorrencia;
    private ProdutoInterfaceFacade produto;
    private ImpostoProdutoCfopInterfaceFacade impostoProdutoCfop;
    private TipoAmbienteInterfaceFacade tipoAmbiente;
    private ProdutoSugeridoInterfaceFacade produtoSugerido;
    private TurmaInterfaceFacade turma;
    private VezesSemanaInterfaceFacade vezesSemana;
    private HistoricoProfessorTurmaInterfaceFacade historicoProfessor;
    private HistoricoProfessorTurmaService hpService;
    //Pacote Relatório
    private SituacaoContratoAnaliticoDW situacaoContratoAnaliticoDW;
    private SituacaoContratoSinteticoDW situacaoContratoSinteticoDW;
    private RotatividadeAnaliticoDW rotatividadeAnaliticoDW;
    private RelatorioCliente relatorioCliente;
    private MetaCrescimento metaCrescimento;
    private Conexao conexao;
    private PlanoCondicaoPagamento planoCondicaoPagamento;
    private PlanoInterfaceFacade plano;
    private FecharMetaDetalhadoInterfaceFacade fecharMetaDetalhado;
    private ConvenioCobrancaInterfaceFacade convenioCobranca;
    private ConvenioCobrancaEmpresaInterfaceFacade convenioCobrancaEmpresa;
    private ConvenioCobrancaRateioInterfaceFacade convenioCobrancaRateio;
    private ConvenioCobrancaRateioItemInterfaceFacade convenioCobrancaRateioItem;
    private ConvenioCobrancaArquivoInterfaceFacade convenioCobrancaArquivo;
    private Cep cep;
    private AberturaMetaInterfaceFacade aberturaMeta;
    private AgendaInterfaceFacade agenda;
    private FecharMetaInterfaceFacade fecharMeta;
    private RotatividadeSinteticoDW rotatividadeSinteticoDW;
    private SituacaoClienteSinteticoDW situacaoClienteSinteticoDW;
    private EventoInterfaceFacade evento;
    private HistoricoVinculoInterfaceFacade historicoVinculo;
    private HistoricoContatoInterfaceFacade historicoContato;
    private ObjecaoInterfaceFacade objecao;
    private ControleTaxaPersonalInterfaceFacade controleTaxaPersonal;
    private ItemTaxaPersonalInterfaceFacade itemTaxaPersonal;
    private ContratoRecorrenciaInterfaceFacade contratoRecorrencia;
    private TransacaoInterfaceFacade transacao;
    private TurmasServiceInterface turmaService;
    private ZillyonWebFacade zwFacade;
    private VendaRapidaRecorrenteService vendaService;
    private LoteInterfaceFacade lote;
    private PresencaInterfaceFacade presenca;
    private FornecedorInterfaceFacade fornecedor;
    private MetaFinanceiraEmpresaInterfaceFacade metaFinanceiraEmpresa;
    private MetaFinanceiraEmpresaValoresInterfaceFacade metaFinanceiraEmpresaValores;
    private MetaFinanceiraConsultorInterfaceFacade metaFinanceiraConsultor;

    private RelatorioOrcamentarioConfigInterfaceFacade relatorioOrcamentarioConfig;
    private RelatorioOrcamentarioConfigPrevisaoInterfaceFacade relatorioOrcamentarioConfigPrevisao;
    /////ECF
    //Cupom Fiscal
    private CupomFiscal cupomFiscal;
    private NFSeEmitida nfSeEmitida;
    private NFSeEmitidaHistorico nfSeEmitidaHistorico;
    private CupomFiscalServiceFacade cupomFiscalService;
    private NotaFiscalConsumidorEletronicaInterfaceFacade notaFiscalConsumidorEletronica;
    /* ------- INÍCIO - ATUALIZADOR DE BANCO DE DADOS ------- */
    private AtualizadorBD atualizadorBD;
    private HorarioAcessoSistemaInterfaceFacade horarioAcessoSistema;
    // PACOTE ESTUDIO
    private ConfiguracaoEstudioInterfaceFacade configuracaoEstudio;
    private PreferenciaAgendaInterfaceFacade preferenciaAgenda;
    private DisponibilidadeInterfaceFacade disponibilidade;
    private EmpresaFechadaInterfaceFacade empresaFechada;
    private TipoHorarioInterfaceFacade tipoHorario;
    private PacoteInterfaceFacade pacote;
    private RelProdutoColaboradorInterfaceFacade relProdutoColaborador;
    private RelProdutoAmbienteInterfaceFacade relProdutoAmbiente;
    private AgendaEstudioInterfaceFacade agendaEstudio;
    private ColaboradorIndispInterfaceFacade colaboradorIndisponibilidade;
    private AmbienteIndispInterfaceFacade ambienteIndisponibilidade;
    private RelatorioComissaoInterfaceFacade relatorioComissao;
    private RelatorioGeralAgendamentosInterfaceFacade relatorioGeralAgendamentos;
    private RelatorioGeralClientesInativosInterfaceFacade relatorioGeralClientesInativos;
    private RelatorioFechamentoDiarioInterfaceFacade relatorioFechamentoDiario;
    /* */
    private RelatorioBVs relatorioBVs;
    //social mailing
    private SocialMailingInterfaceFacade socialMailing;
    private SocialMailGrupoInterfaceFacade socialMailGrupo;
    private ReposicaoInterfaceFacade reposicao;
    private ModalidadeComissaoColaboradorInterfaceFacade modalidadeComissaoColaborador;
    private TurmaComissaoColaboradorInterfaceFacade turmaComissaoColaborador;
    private AlunoComissaoColaboradorInterfaceFacade alunoComissaoColaborador;
    private AutorizacaoCobrancaClienteInterfaceFacade autorizacaoCobrancaCliente;
    private AutorizacaoCobrancaColaboradorInterfaceFacade autorizacaoCobrancaColaborador;
    //PACOTE NFE
    private EmpresaNFeInterfaceFacade empresaNFe;
    private MunicipioNFeInterfaceFacade municipioNFe;
    private NotaFiscalDeServicoInterfaceFacade notaNFSe;
    private ItemRPSNFeInterfaceFacade itemNFSe;
    private LoteNFeInterfaceFacade loteNFSe;
    private RecebivelAvulsoInterfaceFacade recebivelAvulso;
    private ConfiguracaoFinanceiroInterfaceFacade configuracaoFinanceiro;
    private CompraInterfaceFacade compra;
    private CompraDocumentosInterfaceFacade compraDocumentos;
    private ContaFinanceiroCompraInterfaceFacade contaFinanceiroCompra;
    private CompraItensInterfaceFacade compraItens;
    private BalancoInterfaceFacade balanco;
    private BalancoItensInterfaceFacade balancoItens;
    private ProdutoEstoqueInterfaceFacade produtoEstoque;
    private CardexInterfaceFacade cardex;
    //RELATÓRIOS BI
    private ProdutoRel produtoRel;
    private ClientePorDuracaoRel clientePorDuracaoRel;
    private RelatorioRepasseRel relatorioRepasseRel;
    private CarteirasRel carteirasRel;
    private SGPModalidadeComTurmaRel sGPModalidadeComTurmaRel;
    private SGPTurmaRel sGPTurmaRel;
    private SGPModalidadeSemTurmaRel sGPModalidadeSemTurmaRel;
    private SGPAvaliacaoFisicaRel sgpAvaliacaoFisicaRel;
    /* ------- INÍCIO - CENTRAL DE EVENTOS ------- */
    private CentralEventosFacade centralEventosFacade;
    private TextoPadraoInterfaceFacade textoPadrao;
    /* ------- FIM - ATUALIZADOR DE BANCO DE DADOS ------- */
    private PlanoContasSugeridoServico planoContasSugerido;
    private TicketMedioInterfaceFacade ticketMedio;
    private LancamentoProdutoColetivoInterfaceFacade lancamentoProdutoColetivo;
    private AcoesStatusRemessaInterfaceFacade acoesStatus;
    private RemessaItemInterfaceFacade remessaItem;
    private RemessaItemMovParcelaInterfaceFacade remessaItemMovParcela;
    private RetornoRemessaInterfaceFacade retornoRemessa;
    private RemessaCancelamentoItemInterfaceFacade remessaCancelamentoItem;
    private FeedGestaoInterfaceFacade feedGestao;
    private PaginaInicialInterfaceFacade paginaInicialFeed;
    private DadosGerencialPmgInterfaceFacade dadosGerencialPmg;
    private PlanoExcecaoInterfaceFacade planoExcecao;
    private ConfiguracaoProdutoEmpresaInterfaceFacade configuracaoProdutoEmpresa;
    private CadastroDinamicoInterfaceFacade cadastroDinamico;
    private CadastroDinamicoItemInterfaceFacade cadastroDinamicoItem;
    private ComissaoMetaFinanceiraInterfaceFacade comissaoMetaFinanceira;

    private TamanhoArmarioInterfaceFacade TamanhoArmario;
    private ArmarioInterfaceFacade armario;
    private IndiceFinanceiroReajustePrecoInterfaceFacade indiceFinanceiroReajustePreco;
    private ReajusteContratoInterfaceFacade reajusteContrato;

    private ColaboradorIndisponivelCrmInterfaceFacade colaboradorIndisponivelCrm;
    private AulaDesmarcadaInterfaceFacade aulaDesmarcada;
    private PlanoDuracaoCreditoTreinoInterfaceFacade planoDuracaoCreditoTreino;
    private ContratoDuracaoCreditoTreinoInterfaceFacade contratoDuracaoCreditoTreino;
    private ControleCreditoTreinoInterfaceFacade controleCreditoTreino;
    private TipoConviteAulaExperimentalInterfaceFacade tipoConviteAulaExperimental;
    private ConviteAulaExperimentalInterfaceFacade conviteAulaExperimental;
    private TipoConviteAulaExperimentalModalidadeInterfaceFacade tipoConviteAulaExperimentalModalidade;
    private TipoConviteAulaExperimentalModalidadeHorarioInterfaceFacade tipoConviteAulaExperimentalModalidadeHorario;
    private ConviteAulaExperimentalServiceInterface conviteAulaExperimentalService;
    private MovParcelaCupomDescontoInterfaceFacade movParcelaCupomDesconto;
    private ConfiguracaoBIInterfaceFacade configuracaoBi;
    private SorteioInterfaceFacade sorteio;
    private ConfiguracaoSorteioInterfaceFacade configuracaoSorteio;
    private ConfiguracaoSistemaUsuarioInterfaceFacade ordemItemBI;
    private MemCachedManager memCachedManager;
    private Wiki wikiManager;
    private ClienteTitularDependenteInterfaceFacade clienteTitularDependente;
    private SinalizadorSistemaInterfaceFacade sinalizadorSistema;
    private CentroCustoInterfaceFacade centroCusto;
    private Remessa remessa;
    private ContratoAssinaturaDigitalInterfaceFacade contratoAssinatura;
    private PlanoPersonalAssinaturaDigitalInterfaceFacade planoPersonalAssinaturaDigital;
    private DashboardService dashboardService;
    private ResultadoServicosInterfaceFacade resultadoServicos;

    // INICIO CLUBE //
    private DepartamentoInterfaceFacade departamento;
    // FIM CLUBE //

    private BrindeInterfaceFacade brinde;
    private HistoricoPontosInterfaceFacade historicoPontos;
    private LeadInterfaceFacade lead;
    private ConversaoLeadInterfaceFacade conversaoLead;

    //INICIO PONTO - CLUBE VANTANGENS//
    private CampanhaDuracaoInterfaceFacade campanhaDuracao;
    private ItemCampanhaInterfaceFacade itemCampanha;
    private PlanoEmpresa planoEmpresa;
    private PlanoRedeEmpresa planorRedeEmpresa;
    private ProdutoRedeEmpresa produtoRedeEmpresa;
    private FornecedorRedeEmpresa fornecedorRedeEmpresa;
    private FeriadoRedeEmpresa feriadoRedeEmpresa;
    private PerfilAcessoRedeEmpresa perfilAcessoRedeEmpresa;
    private UsuarioRedeEmpresa usuarioRedeEmpresa;
    private ColaboradorRedeEmpresa colaboradorRedeEmpresa;
    private MalaDiretaRedeEmpresa malaDiretaRedeEmpresa;
    private PlanoContaRedeEmpresa planoContaRedeEmpresa;
    private DescontoEmpresa descontoEmpresa;
    private ConfirmacaoEmailCompraInterfaceFacede confirmacaoEmailCompra;
    private BrindePlanoInterfaceFacade brindePlano;
    private PlanoRecorrenciaParcela planorecorrenciaParcela;
    private PlanoAnuidadeParcela planoAnuidadeParcela;
    private PlanoTipo planoTipo;
    private ParceiroFidelidadeInterfaceFacade parceiroFidelidade;
    private TabelaParceiroFidelidadeInterfaceFacade tabelaParceiroFidelidade;
    private TabelaParceiroFidelidadeItemInterfaceFacade tabelaParceiroFidelidadeItem;
    private ParceiroFidelidadePontosInterfaceFacade parceiroFidelidadePontos;
    private ProdutoParceiroFidelidadeInterfaceFacade produtoParceiroFidelidade;
    private OperacaoColetivaInterfaceFacade operacaoColetiva;
    private ClienteInterfaceFacade cliente2;
    //FIM PONTO//

    private Aditivo aditivo;


    //INICIO - PROCESSO IMPORTACAO//
    private ProcessoImportacaoInterfaceFacade processoImportacao;
    private ProcessoImportacaoLogInterfaceFacade processoImportacaoLog;
    private ProcessoImportacaoItemInterfaceFacade processoImportacaoItem;
    //FIM - PROCESSO IMPORTACAO//
    private Pix pix;
    private PixService pixService;
    private ERedeServiceConciliacao eRedeServiceConciliacao;
    private PixMovParcela pixMovParcela;
    private BiMSService biMSService;
    private MgbService mgbService;
    private PactoPayConfigInterfaceFacade pactoPayConfig;
    private TokenVendasOnline tokenVendasOnline;
    private ColaboradorDocumentoRh colaboradorDocumentoRh;
    private ColaboradorInfoRh colaboradorInfoRh;

    private IntegracaoSesiInterfaceFacade integracaoSesi;
    private VendasOnlineCampanha vendasOnlineCampanha;
    private PinPadPedido pinPadPedido;
    private PactoPayComunicacao pactoPayComunicacao;
    private AsaasEmpresaInterfaceFacade asaasEmpresa;
    private TotalpassInterfaceFacade totalpass;
    private PluggyService pluggyService;
    private IntegracaoKobanaService integracaoKobanaService;
    private PinPadPedidoMovParcela pinPadPedidoMovParcela;
    private TokenOperacao tokenOperacao;
    private InfoNovoMerchantPagoLivre infoNovoMerchantPagoLivre;

    private SolicitacaoInterfaceFacade solicitacao;

    private OptinInterfaceFacade optin;

    private LocalImpressaoInterfaceFacade localImpressao;
    private CarteirinhaClienteInterfaceFacade carteirinhaCliente;

    private ProdutoMescladoInterfaceFacade produtoMesclado;

    public AtualizadorBD getAtualizadorBD() throws Exception {
        if (atualizadorBD == null) {
            atualizadorBD = new AtualizadorBD(true);
        }
        atualizadorBD.prepararConexao();
        return atualizadorBD;
    }

    public CentralEventosFacade getCentralEventosFacade() throws Exception {
        if (centralEventosFacade == null) {
            centralEventosFacade = new CentralEventosFacade();
        }
        centralEventosFacade.prepararConexao();
        return centralEventosFacade;
    }

    public ZillyonWebFacade getZWFacade() throws Exception {
        if (zwFacade == null) {
            zwFacade = new ZillyonWebFacade();
        }
        zwFacade.prepararConexao();
        return zwFacade;
    }

    public CentralEventosFacade getCentralEventosFacade(HttpSession session) throws Exception {
        if (centralEventosFacade == null) {
            centralEventosFacade = new CentralEventosFacade(session);
        }
        centralEventosFacade.prepararConexao();
        return centralEventosFacade;
    }

    /* ------- FIM - CENTRAL DE EVENTOS ------- */
    public HistoricoContatoInterfaceFacade getHistoricoContato() throws Exception {
        if (historicoContato == null) {
            historicoContato = new HistoricoContato();
        }
        historicoContato.prepararConexao();
        return historicoContato;
    }

    public AcessoClienteInterfaceFacade getAcessoCliente() throws Exception {
        if (acessoCliente == null) {
            acessoCliente = new AcessoCliente();
        }
        acessoCliente.prepararConexao();
        return acessoCliente;
    }

    public LiberacaoAcessoInterfaceFacade getLiberacaoAcesso() throws Exception {
        if (liberacaoAcesso == null) {
            liberacaoAcesso = new LiberacaoAcesso();
        }
        liberacaoAcesso.prepararConexao();
        return liberacaoAcesso;
    }

    public AcessoColaboradorInterfaceFacade getAcessoColaborador() throws Exception {
        if (acessoColaborador == null) {
            acessoColaborador = new AcessoColaborador();
        }
        acessoColaborador.prepararConexao();
        return acessoColaborador;
    }

    public ColetorInterfaceFacade getColetor() throws Exception {
        if (coletor == null) {
            coletor = new Coletor();
        }
        coletor.prepararConexao();
        return coletor;
    }

    public LocalAcessoInterfaceFacade getLocalAcesso() throws Exception {
        if (localAcesso == null) {
            localAcesso = new LocalAcesso();
        }
        localAcesso.prepararConexao();
        return localAcesso;
    }

    public ServidorFacialInterfaceFacade getServidorFacial() throws Exception {
        if (servidorFacial == null) {
            servidorFacial = new ServidorFacial();
        }
        servidorFacial.prepararConexao();
        return servidorFacial;
    }

    public CameraInterfaceFacade getCamera() throws Exception {
        if (camera == null) {
            camera = new Camera();
        }
        camera.prepararConexao();
        return camera;
    }

    public ControleAcesso getControleAcesso() throws Exception {
        if (controleAcesso == null) {
            controleAcesso = new ControleAcesso();
        }
        controleAcesso.prepararConexao();
        return controleAcesso;
    }

    public LogInterfaceFacade getLog() throws Exception {
        if (log == null) {
            log = new Log();
        }
        log.prepararConexao();
        return log;
    }

    public LogProcessoSistemaInterfaceFacade getLogProcessoSistema() throws Exception {
        if (logProcessoSistema == null) {
            logProcessoSistema = new LogProcessoSistema();
        }
        logProcessoSistema.prepararConexao();
        return logProcessoSistema;
    }

    public LogTotalPassInterfaceFacade getLogTotalPass() throws Exception {
        if (logtotalpass == null) {
            logtotalpass = new LogTotalPass();
        }
        logtotalpass.prepararConexao();
        return logtotalpass;
    }

    public PerfilAcessoInterfaceFacade getPerfilAcesso() throws Exception {
        if (perfilAcesso == null) {
            perfilAcesso = new PerfilAcesso();
        }
        perfilAcesso.prepararConexao();
        return perfilAcesso;
    }

    public Permissao getPermissao() throws Exception {
        if (permissao == null) {
            permissao = new Permissao();
        }
        permissao.prepararConexao();
        return permissao;
    }

    public Risco getRisco() throws Exception {
        if (risco == null) {
            risco = new Risco();
        }
        risco.prepararConexao();
        return risco;
    }

    public Robo getRobo() throws Exception {
        if (robo == null) {
            robo = new Robo();
        }
        robo.prepararConexao();
        return robo;
    }

    public UsuarioInterfaceFacade getUsuario() throws Exception {
        if (usuario == null) {
            usuario = new Usuario();
        }
        usuario.prepararConexao();
        return usuario;
    }

    public UsuarioNFeIntefaceFacade getUsuarioNFe() throws Exception {
        if (usuarioNFe == null) {
            usuarioNFe = new UsuarioNFe();
        }
        usuarioNFe.prepararConexao();
        return usuarioNFe;
    }

    public PerfilUsuarioNFeInterfaceFacade getPerfilUsuarioNFe() throws Exception {
        if (perfilUsuarioNFe == null) {
            perfilUsuarioNFe = new PerfilUsuarioNFe();
        }
        perfilUsuarioNFe.prepararConexao();
        return perfilUsuarioNFe;
    }

    public UsuarioPerfilAcesso getUsuarioPerfilAcesso() throws Exception {
        if (usuarioPerfilAcesso == null) {
            usuarioPerfilAcesso = new UsuarioPerfilAcesso();
        }
        usuarioPerfilAcesso.prepararConexao();
        return usuarioPerfilAcesso;
    }

    public UsuarioPerfilAcesso getUsuarioPerfilAcesso(Connection con) throws Exception {
        if (usuarioPerfilAcesso == null) {
            usuarioPerfilAcesso = new UsuarioPerfilAcesso(con);
        }
        usuarioPerfilAcesso.prepararConexao();
        return usuarioPerfilAcesso;
    }

    public EstornoObservacaoInterfaceFacade getEstornoObservacao() throws Exception {
        if (estornoObservacao == null) {
            estornoObservacao = new EstornoObservacao();
        }
        estornoObservacao.prepararConexao();
        return estornoObservacao;
    }

    public ObservacaoOperacaoInterfaceFacade getObservacaoOperacao() throws Exception {
        if (observacaoOperacao == null) {
            observacaoOperacao = new ObservacaoOperacao();
        }
        observacaoOperacao.prepararConexao();
        return observacaoOperacao;
    }

    public ClienteObservacaoInterfaceFacade getClienteObservacao() throws Exception {
        if (clienteObservacao == null) {
            clienteObservacao = new ClienteObservacao();
        }
        clienteObservacao.prepararConexao();
        return clienteObservacao;
    }

    public CategoriaInterfaceFacade getCategoria() throws Exception {
        if (categoria == null) {
            categoria = new Categoria();
        }
        categoria.prepararConexao();
        return categoria;
    }

    public CidadeInterfaceFacade getCidade() throws Exception {
        if (cidade == null) {
            cidade = new Cidade();
        }
        cidade.prepararConexao();
        return cidade;
    }

    public PessoaInterfaceFacade getPessoa() throws Exception {
        if (pessoa == null) {
            pessoa = new Pessoa();
        }
        pessoa.prepararConexao();
        return pessoa;
    }

    public EstadoInterfaceFacade getEstado() throws Exception {
        if (estado == null) {
            estado = new Estado();
        }
        estado.prepararConexao();
        return estado;
    }

    public ClassificacaoInterfaceFacade getClassificacao() throws Exception {
        if (classificacao == null) {
            classificacao = new Classificacao();
        }
        classificacao.prepararConexao();
        return classificacao;
    }

    public ClienteInterfaceFacade getCliente() throws Exception {
        if (cliente == null) {
            cliente = new Cliente();
        }
        cliente.prepararConexao();
        return cliente;
    }

    public ClienteRestricaoInterfaceFacade getClienteRestricao() throws Exception {
        if (clienteRestricao == null) {
            clienteRestricao = new ClienteRestricao();
        }
        clienteRestricao.prepararConexao();
        return clienteRestricao;
    }

    public ClienteClassificacao getClienteClassificacao() throws Exception {
        if (clienteClassificacao == null) {
            clienteClassificacao = new ClienteClassificacao();
        }
        clienteClassificacao.prepararConexao();
        return clienteClassificacao;
    }

    public ClienteGrupo getClienteGrupo() throws Exception {
        if (clienteGrupo == null) {
            clienteGrupo = new ClienteGrupo();
        }
        clienteGrupo.prepararConexao();
        return clienteGrupo;
    }

    public ClienteMensagemInterfaceFacade getClienteMensagem() throws Exception {
        if (clienteMensagem == null) {
            clienteMensagem = new ClienteMensagem();
        }
        clienteMensagem.prepararConexao();
        return clienteMensagem;
    }

    public ColaboradorInterfaceFacade getColaborador() throws Exception {
        if (colaborador == null) {
            colaborador = new Colaborador();
        }
        colaborador.prepararConexao();
        return colaborador;
    }

    public ColaboradorModalidade getColaboradorModalidade() throws Exception {
        if (colaboradorModalidade == null) {
            colaboradorModalidade = new ColaboradorModalidade();
        }
        colaboradorModalidade.prepararConexao();
        return colaboradorModalidade;
    }

    public ConfiguracaoSistemaInterfaceFacade getConfiguracaoSistema() throws Exception {
        if (configuracaoSistema == null) {
            configuracaoSistema = new ConfiguracaoSistema();
        }
        configuracaoSistema.prepararConexao();
        return configuracaoSistema;
    }

    public ContaCorrenteEmpresa getContaCorrenteEmpresa() throws Exception {
        if (contaCorrenteEmpresa == null) {
            contaCorrenteEmpresa = new ContaCorrenteEmpresa();
        }
        contaCorrenteEmpresa.prepararConexao();
        return contaCorrenteEmpresa;
    }

    public ConfiguracaoReenvioMovParcelaEmpresaInterfaceFacade getConfiguracaoReenvioMovParcelaEmpresa() throws Exception {
        if (configuracaoReenvioMovParcelaEmpresa == null) {
            configuracaoReenvioMovParcelaEmpresa = new ConfiguracaoReenvioMovParcelaEmpresa();
        }
        configuracaoReenvioMovParcelaEmpresa.prepararConexao();
        return configuracaoReenvioMovParcelaEmpresa;
    }

    public MovParcelaTentativaConvenioInterfaceFacade getMovParcelaTentativaConvenio() throws Exception {
        if (movParcelaTentativaConvenio == null) {
            movParcelaTentativaConvenio = new MovParcelaTentativaConvenio();
        }
        movParcelaTentativaConvenio.prepararConexao();
        return movParcelaTentativaConvenio;
    }

    public Email getEmail() throws Exception {
        if (email == null) {
            email = new Email();
        }
        email.prepararConexao();
        return email;
    }

    public EmpresaInterfaceFacade getEmpresa() throws Exception {
        if (empresa == null) {
            empresa = new Empresa();
        }
        empresa.prepararConexao();
        return empresa;
    }

    public ConfiguracaoEmpresaTotemInterfaceFacade getConfiguracaoEmpresaTotem() throws Exception {
        if (configuracaoEmpresaTotem == null) {
            configuracaoEmpresaTotem = new ConfiguracaoEmpresaTotem();
        }
        configuracaoEmpresaTotem.prepararConexao();
        return configuracaoEmpresaTotem;
    }

    public Endereco getEndereco() throws Exception {
        if (endereco == null) {
            endereco = new Endereco();
        }
        endereco.prepararConexao();
        return endereco;
    }

    public Familiar getFamiliar() throws Exception {
        if (familiar == null) {
            familiar = new Familiar();
        }
        familiar.prepararConexao();
        return familiar;
    }

    public GrauInstrucaoInterfaceFacade getGrauInstrucao() throws Exception {
        if (grauInstrucao == null) {
            grauInstrucao = new GrauInstrucao();
        }
        grauInstrucao.prepararConexao();
        return grauInstrucao;
    }

    public GrupoInterfaceFacade getGrupo() throws Exception {
        if (grupo == null) {
            grupo = new Grupo();
        }
        grupo.prepararConexao();
        return grupo;
    }

    public Favorito getFavorito() throws Exception {
        if(favorito == null){
            favorito = new Favorito();
        }

        favorito.prepararConexao();
        return favorito;
    }

    public LogControleUsabilidadeInterfaceFacade getLogControleUsabilidade() throws Exception {
        if (logControleUsabilidade == null) {
            logControleUsabilidade = new LogControleUsabilidade();
        }
        logControleUsabilidade.prepararConexao();
        return logControleUsabilidade;
    }

    public MovimentoContaCorrenteClienteInterfaceFacade getMovimentoContaCorrenteCliente() throws Exception {
        if (movimentoContaCorrenteCliente == null) {
            movimentoContaCorrenteCliente = new MovimentoContaCorrenteCliente();
        }
        movimentoContaCorrenteCliente.prepararConexao();
        return movimentoContaCorrenteCliente;
    }

    public MovimentoContaCorrenteClienteComposicaoInterfaceFacade getMovimentoContaCorrenteClienteComposicao() throws Exception {
        if (movimentoContaCorrenteClienteComposicao == null) {
            movimentoContaCorrenteClienteComposicao = new MovimentoContaCorrenteClienteComposicao();
        }
        movimentoContaCorrenteClienteComposicao.prepararConexao();
        return movimentoContaCorrenteClienteComposicao;
    }

    public PaisInterfaceFacade getPais() throws Exception {
        if (pais == null) {
            pais = new Pais();
        }
        pais.prepararConexao();
        return pais;
    }

    public ParentescoInterfaceFacade getParentesco() throws Exception {
        if (parentesco == null) {
            parentesco = new Parentesco();
        }
        parentesco.prepararConexao();
        return parentesco;
    }

    public PerguntaInterfaceFacade getPergunta() throws Exception {
        if (pergunta == null) {
            pergunta = new Pergunta();
        }
        pergunta.prepararConexao();
        return pergunta;
    }

    public PerguntaClienteInterfaceFacade getPerguntaCliente() throws Exception {
        if (perguntaCliente == null) {
            perguntaCliente = new PerguntaCliente();
        }
        perguntaCliente.prepararConexao();
        return perguntaCliente;
    }

    public ProfissaoInterfaceFacade getProfissao() throws Exception {
        if (profissao == null) {
            profissao = new Profissao();
        }
        profissao.prepararConexao();
        return profissao;
    }

    public TipoAmbienteInterfaceFacade getTipoAmbiente() throws Exception {
        if (tipoAmbiente == null) {
            tipoAmbiente = new TipoAmbiente();
        }
        tipoAmbiente.prepararConexao();
        return tipoAmbiente;
    }

    public QuestionarioInterfaceFacade getQuestionario() throws Exception {
        if (questionario == null) {
            questionario = new Questionario();
        }
        questionario.prepararConexao();
        return questionario;
    }

    public QuestionarioClienteInterfaceFacade getQuestionarioCliente() throws Exception {
        if (questionarioCliente == null) {
            questionarioCliente = new QuestionarioCliente();
        }
        questionarioCliente.prepararConexao();
        return questionarioCliente;
    }

    public QuestionarioPergunta getQuestionarioPergunta() throws Exception {
        if (questionarioPergunta == null) {
            questionarioPergunta = new QuestionarioPergunta();
        }
        questionarioPergunta.prepararConexao();
        return questionarioPergunta;
    }

    public QuestionarioPerguntaCliente getQuestionarioPerguntaCliente() throws Exception {
        if (questionarioPerguntaCliente == null) {
            questionarioPerguntaCliente = new QuestionarioPerguntaCliente();
        }
        questionarioPerguntaCliente.prepararConexao();
        return questionarioPerguntaCliente;
    }

    public RespostaPergCliente getRespostaPergCliente() throws Exception {
        if (respostaPergCliente == null) {
            respostaPergCliente = new RespostaPergCliente();
        }
        respostaPergCliente.prepararConexao();
        return respostaPergCliente;
    }

    public RespostaPerguntaInterfaceFacade getRespostaPergunta() throws Exception {
        if (respostaPergunta == null) {
            respostaPergunta = new RespostaPergunta();
        }
        respostaPergunta.prepararConexao();
        return respostaPergunta;
    }

    public Telefone getTelefone() throws Exception {
        if (telefone == null) {
            telefone = new Telefone();
        }
        telefone.prepararConexao();
        return telefone;
    }

    public TipoColaboradorInterfaceFacade getTipoColaborador() throws Exception {
        if (tipoColaborador == null) {
            tipoColaborador = new TipoColaborador();
        }
        tipoColaborador.prepararConexao();
        return tipoColaborador;
    }

    public VinculoInterfaceFacade getVinculo() throws Exception {
        if (vinculo == null) {
            vinculo = new Vinculo();
        }
        vinculo.prepararConexao();
        return vinculo;
    }

    public ContratoInterfaceFacade getContrato() throws Exception {
        if (contrato == null) {
            contrato = new Contrato();
        }
        contrato.prepararConexao();
        return contrato;
    }

    public RenovacaoSinteticoFacade getRenovacaoSinteticoFacade() throws Exception {
        if (renovacaoSinteticoFacade == null) {
            renovacaoSinteticoFacade = new RenovacaoSintetico();
        }
        renovacaoSinteticoFacade.prepararConexao();
        return renovacaoSinteticoFacade;
    }

    public ContratoComposicao getContratoComposicao() throws Exception {
        if (contratoComposicao == null) {
            contratoComposicao = new ContratoComposicao();
        }
        contratoComposicao.prepararConexao();
        return contratoComposicao;
    }

    public ContratoCondicaoPagamento getContratoCondicaoPagamento() throws Exception {
        if (contratoCondicaoPagamento == null) {
            contratoCondicaoPagamento = new ContratoCondicaoPagamento();
        }
        contratoCondicaoPagamento.prepararConexao();
        return contratoCondicaoPagamento;
    }

    public ContratoDuracao getContratoDuracao() throws Exception {
        if (contratoDuracao == null) {
            contratoDuracao = new ContratoDuracao();
        }
        contratoDuracao.prepararConexao();
        return contratoDuracao;
    }

    public ContratoHorario getContratoHorario() throws Exception {
        if (contratoHorario == null) {
            contratoHorario = new ContratoHorario();
        }
        contratoHorario.prepararConexao();
        return contratoHorario;
    }

    public ImpostoProdutoCfopInterfaceFacade getImpostoProdutoCfop() throws Exception {
        if (impostoProdutoCfop == null) {
            impostoProdutoCfop = new ImpostoProdutoCfop();
        }
        impostoProdutoCfop.prepararConexao();
        return impostoProdutoCfop;
    }

    public ContratoModalidade getContratoModalidade() throws Exception {
        if (contratoModalidade == null) {
            contratoModalidade = new ContratoModalidade();
        }
        contratoModalidade.prepararConexao();
        return contratoModalidade;
    }

    public ContratoModalidadeHorarioTurmaInterfaceFacade getContratoModalidadeHorarioTurma() throws Exception {
        if (contratoModalidadeHorarioTurma == null) {
            contratoModalidadeHorarioTurma = new ContratoModalidadeHorarioTurma();
        }
        contratoModalidadeHorarioTurma.prepararConexao();
        return contratoModalidadeHorarioTurma;
    }

    public HistoricoProfessorTurmaInterfaceFacade getHistoricoProfessor() throws Exception {
        if (historicoProfessor == null) {
            historicoProfessor = new HistoricoProfessorTurma();
        }
        historicoProfessor.prepararConexao();
        return historicoProfessor;
    }

    public HistoricoProfessorTurmaService getHpService() throws Exception {
        if (hpService == null) {
            hpService = new HistoricoProfessorTurmaServiceImpl();
        }
        hpService.prepararConexao();
        return hpService;
    }

    public ContratoModalidadeProdutoSugerido getContratoModalidadeProdutoSugerido() throws Exception {
        if (contratoModalidadeProdutoSugerido == null) {
            contratoModalidadeProdutoSugerido = new ContratoModalidadeProdutoSugerido();
        }
        contratoModalidadeProdutoSugerido.prepararConexao();
        return contratoModalidadeProdutoSugerido;
    }

    public ContratoModalidadeTurmaInterfaceFacade getContratoModalidadeTurma() throws Exception {
        if (contratoModalidadeTurma == null) {
            contratoModalidadeTurma = new ContratoModalidadeTurma();
        }
        contratoModalidadeTurma.prepararConexao();
        return contratoModalidadeTurma;
    }

    public ContratoModalidadeVezesSemana getContratoModalidadeVezesSemana() throws Exception {
        if (contratoModalidadeVezesSemana == null) {
            contratoModalidadeVezesSemana = new ContratoModalidadeVezesSemana();
        }
        contratoModalidadeVezesSemana.prepararConexao();
        return contratoModalidadeVezesSemana;
    }

    public ContratoOperacaoInterfaceFacade getContratoOperacao() throws Exception {
        if (contratoOperacao == null) {
            contratoOperacao = new ContratoOperacao();
        }
        contratoOperacao.prepararConexao();
        return contratoOperacao;
    }

    public ContratoPlanoProdutoSugerido getContratoPlanoProdutoSugerido() throws Exception {
        if (contratoPlanoProdutoSugerido == null) {
            contratoPlanoProdutoSugerido = new ContratoPlanoProdutoSugerido();
        }
        contratoPlanoProdutoSugerido.prepararConexao();
        return contratoPlanoProdutoSugerido;
    }

    public ContratoTextoPadraoInterfaceFacade getContratoTextoPadrao() throws Exception {
        if (contratoTextoPadrao == null) {
            contratoTextoPadrao = new ContratoTextoPadrao();
        }
        contratoTextoPadrao.prepararConexao();
        return contratoTextoPadrao;
    }

    public ContratoDependenteInterfaceFacade getContratoDependente() throws Exception {
        if (contratoDependente == null) {
            contratoDependente = new ContratoDependente();
        }
        contratoDependente.prepararConexao();
        return contratoDependente;
    }

    public AfastamentoContratoDependenteInterfaceFacade getAfastamentoContratoDependente() throws Exception {
        if (afastamentoContratoDependente == null) {
            afastamentoContratoDependente = new AfastamentoContratoDependente();
        }
        afastamentoContratoDependente.prepararConexao();
        return afastamentoContratoDependente;
    }

    public ConvenioInterfaceFacade getConvenio() throws Exception {
        if (convenio == null) {
            convenio = new Convenio();
        }
        convenio.prepararConexao();
        return convenio;
    }

    public ConvenioDescontoInterfaceFacade getConvenioDesconto() throws Exception {
        if (convenioDesconto == null) {
            convenioDesconto = new ConvenioDesconto();
        }
        convenioDesconto.prepararConexao();
        return convenioDesconto;
    }

    public ConvenioDescontoConfiguracao getConvenioDescontoConfiguracao() throws Exception {
        if (convenioDescontoConfiguracao == null) {
            convenioDescontoConfiguracao = new ConvenioDescontoConfiguracao();
        }
        convenioDescontoConfiguracao.prepararConexao();
        return convenioDescontoConfiguracao;
    }

    public HistoricoContratoInterfaceFacade getHistoricoContrato() throws Exception {
        if (historicoContrato == null) {
            historicoContrato = new HistoricoContrato();
        }
        historicoContrato.prepararConexao();
        return historicoContrato;
    }

    public JustificativaOperacaoInterfaceFacade getJustificativaOperacao() throws Exception {
        if (justificativaOperacao == null) {
            justificativaOperacao = new JustificativaOperacao();
        }
        justificativaOperacao.prepararConexao();
        return justificativaOperacao;
    }

    public MatriculaAlunoHorarioTurmaInterfaceFacade getMatriculaAlunoHorarioTurma() throws Exception {
        if (matriculaAlunoHorarioTurma == null) {
            matriculaAlunoHorarioTurma = new MatriculaAlunoHorarioTurma();
        }
        matriculaAlunoHorarioTurma.prepararConexao();
        return matriculaAlunoHorarioTurma;
    }

    public MovProdutoInterfaceFacade getMovProduto() throws Exception {
        if (movProduto == null) {
            movProduto = new MovProduto();
        }
        movProduto.prepararConexao();
        return movProduto;
    }

    public UtilizacaoAvaliacaoFisicaInterfaceFacade getUtilizacaoAvaliacaoFisica() throws Exception {
        if (utilizacaoAvaliacaoFisica == null) {
            utilizacaoAvaliacaoFisica = new UtilizacaoAvaliacaoFisica();
        }
        utilizacaoAvaliacaoFisica.prepararConexao();
        return utilizacaoAvaliacaoFisica;
    }

    public PeriodoAcessoClienteInterfaceFacade getPeriodoAcessoCliente() throws Exception {
        if (periodoAcessoCliente == null) {
            periodoAcessoCliente = new PeriodoAcessoCliente();
        }
        periodoAcessoCliente.prepararConexao();
        return periodoAcessoCliente;
    }

    public TrancamentoContratoInterfaceFacade getTrancamentoContrato() throws Exception {
        if (trancamentoContrato == null) {
            trancamentoContrato = new TrancamentoContrato();
        }
        trancamentoContrato.prepararConexao();
        return trancamentoContrato;
    }

    public ConfiguracaoSistemaCRMInterfaceFacade getConfiguracaoSistemaCRM() throws Exception {
        if (configuracaoSistemaCRM == null) {
            configuracaoSistemaCRM = new ConfiguracaoSistemaCRM();
        }
        configuracaoSistemaCRM.prepararConexao();
        return configuracaoSistemaCRM;
    }

    public ConfiguracaoDiasPosVenda getConfiguracaoDiasPosVenda() throws Exception {
        if (configuracaoDiasPosVenda == null) {
            configuracaoDiasPosVenda = new ConfiguracaoDiasPosVenda();
        }
        configuracaoDiasPosVenda.prepararConexao();
        return configuracaoDiasPosVenda;
    }

    public DefinirLayoutInterfaceFacade getDefinirLayout() throws Exception {
        if (definirLayout == null) {
            definirLayout = new DefinirLayout();
        }
        definirLayout.prepararConexao();
        return definirLayout;
    }

    public FeriadoInterfaceFacade getFeriado() throws Exception {
        if (feriado == null) {
            feriado = new Feriado();
        }
        feriado.prepararConexao();
        return feriado;
    }

    public GrupoColaboradorInterfaceFacade getGrupoColaborador() throws Exception {
        if (grupoColaborador == null) {
            grupoColaborador = new GrupoColaborador();
        }
        grupoColaborador.prepararConexao();
        return grupoColaborador;
    }

    public GrupoColaboradorParticipante getGrupoColaboradorParticipante() throws Exception {
        if (grupoColaboradorParticipante == null) {
            grupoColaboradorParticipante = new GrupoColaboradorParticipante();
        }
        grupoColaboradorParticipante.prepararConexao();
        return grupoColaboradorParticipante;
    }

    public IndicacaoInterfaceFacade getIndicacao() throws Exception {
        if (indicacao == null) {
            indicacao = new Indicacao();
        }
        indicacao.prepararConexao();
        return indicacao;
    }

    public IndicadoInterfaceFacade getIndicado() throws Exception {
        if (indicado == null) {
            indicado = new Indicado();
        }
        indicado.prepararConexao();
        return indicado;
    }

    public MalaDiretaInterfaceFacade getMalaDireta() throws Exception {
        if (malaDireta == null) {
            malaDireta = new MalaDireta();
        }
        malaDireta.prepararConexao();
        return malaDireta;
    }

    public MalaDiretaEnviada getMalaDiretaEnviada() throws Exception {
        if (malaDiretaEnviada == null) {
            malaDiretaEnviada = new MalaDiretaEnviada();
        }
        malaDiretaEnviada.prepararConexao();
        return malaDiretaEnviada;
    }

    public ModeloMensagemInterfaceFacade getModeloMensagem() throws Exception {
        if (modeloMensagem == null) {
            modeloMensagem = new ModeloMensagem();
        }
        modeloMensagem.prepararConexao();
        return modeloMensagem;
    }

    public PassivoInterfaceFacade getPassivo() throws Exception {
        if (passivo == null) {
            passivo = new Passivo();
        }
        passivo.prepararConexao();
        return passivo;
    }

    public AulaAvulsaDiariaInterfaceFacade getAulaAvulsaDiaria() throws Exception {
        if (aulaAvulsaDiaria == null) {
            aulaAvulsaDiaria = new AulaAvulsaDiaria();
        }
        aulaAvulsaDiaria.prepararConexao();
        return aulaAvulsaDiaria;
    }

    public BancoInterfaceFacade getBanco() throws Exception {
        if (banco == null) {
            banco = new Banco();
        }
        banco.prepararConexao();
        return banco;
    }

    public Cheque getCheque() throws Exception {
        if (cheque == null) {
            cheque = new Cheque();
        }
        cheque.prepararConexao();
        return cheque;
    }

    public ContaCorrenteInterfaceFacade getContaCorrente() throws Exception {
        if (contaCorrente == null) {
            contaCorrente = new ContaCorrente();
        }
        contaCorrente.prepararConexao();
        return contaCorrente;
    }

    public ContaBancariaFornecedorInterfaceFacade getContaBancariaFornecedor() throws Exception {
        if (contaBancariaFornecedor == null) {
            contaBancariaFornecedor = new ContaBancariaFornecedor();
        }
        contaBancariaFornecedor.prepararConexao();
        return contaBancariaFornecedor;
    }

    public ProdutoDevolverCancelamentoEmpresaInterfaceFacade getProdutoDevolverCancelamentoEmpresa() throws Exception {
        if (produtoDevolverCancelamentoEmpresa == null) {
            produtoDevolverCancelamentoEmpresa = new ProdutoDevolverCancelamentoEmpresa();
        }
        produtoDevolverCancelamentoEmpresa.prepararConexao();
        return produtoDevolverCancelamentoEmpresa;
    }

    public FormaPagamentoInterfaceFacade getFormaPagamento() throws Exception {
        if (formaPagamento == null) {
            formaPagamento = new FormaPagamento();
        }
        formaPagamento.prepararConexao();
        return formaPagamento;
    }

    public AdquirenteInterfaceFacade getAdquirente() throws Exception {
        if (adquirente == null) {
            adquirente = new Adquirente();
        }
        adquirente.prepararConexao();
        return adquirente;
    }

    public FormaPagamentoEmpresaInterfaceFacade getFormaPagamentoEmpresa() throws Exception {
        if (formaPagamentoEmpresa == null) {
            formaPagamentoEmpresa = new FormaPagamentoEmpresa();
        }
        formaPagamentoEmpresa.prepararConexao();
        return formaPagamentoEmpresa;
    }

    public FormaPagamentoPerfilAcessoInterfaceFacade getFormaPagamentoPerfilAcesso() throws Exception {
        if (formaPagamentoPerfilAcesso == null) {
            formaPagamentoPerfilAcesso = new FormaPagamentoPerfilAcesso();
        }
        formaPagamentoPerfilAcesso.prepararConexao();
        return formaPagamentoPerfilAcesso;
    }

    public PinPadInterfaceFacade getPinPad() throws Exception {
        if (pinpad == null) {
            pinpad = new PinPad();
        }
        pinpad.prepararConexao();
        return pinpad;
    }

    public ItemVendaAvulsa getItemVendaAvulsa() throws Exception {
        if (itemVendaAvulsa == null) {
            itemVendaAvulsa = new ItemVendaAvulsa();
        }
        itemVendaAvulsa.prepararConexao();
        return itemVendaAvulsa;
    }

    public MovPagamentoInterfaceFacade getMovPagamento() throws Exception {
        if (movPagamento == null) {
            movPagamento = new MovPagamento();
        }
        movPagamento.prepararConexao();
        return movPagamento;
    }

    public ExtratoDiarioItemInterfaceFacade getExtratoDiarioItem() throws Exception {
        if (extratoDiarioItem == null) {
            extratoDiarioItem = new ExtratoDiarioItem();
        }
        extratoDiarioItem.prepararConexao();
        return extratoDiarioItem;
    }

    public MovParcelaInterfaceFacade getMovParcela() throws Exception {
        if (movParcela == null) {
            movParcela = new MovParcela();
        }
        movParcela.prepararConexao();
        return movParcela;
    }

    public MovProdutoParcela getMovProdutoParcela() throws Exception {
        if (movProdutoParcela == null) {
            movProdutoParcela = new MovProdutoParcela();
        }
        movProdutoParcela.prepararConexao();
        return movProdutoParcela;
    }

    public PagamentoMovParcela getPagamentoMovParcela() throws Exception {
        if (pagamentoMovParcela == null) {
            pagamentoMovParcela = new PagamentoMovParcela();
        }
        pagamentoMovParcela.prepararConexao();
        return pagamentoMovParcela;
    }

    public ReciboPagamento getReciboPagamento() throws Exception {
        if (reciboPagamento == null) {
            reciboPagamento = new ReciboPagamento();
        }
        reciboPagamento.prepararConexao();
        return reciboPagamento;
    }

    public ReciboClienteConsultorInterfaceFacade getReciboClienteConsultor() throws Exception {
        if (reciboClienteConsultor == null) {
            reciboClienteConsultor = new ReciboClienteConsultor();
        }
        reciboClienteConsultor.prepararConexao();
        return reciboClienteConsultor;
    }

    public TipoRemessaInterfaceFacade getTipoRemessa() throws Exception {
        if (tipoRemessa == null) {
            tipoRemessa = new TipoRemessa();
        }
        tipoRemessa.prepararConexao();
        return tipoRemessa;
    }

    public TipoRetornoInterfaceFacade getTipoRetorno() throws Exception {
        if (tipoRetorno == null) {
            tipoRetorno = new TipoRetorno();
        }
        tipoRetorno.prepararConexao();
        return tipoRetorno;
    }

    public VendaAvulsaInterfaceFacade getVendaAvulsa() throws Exception {
        if (vendaAvulsa == null) {
            vendaAvulsa = new VendaAvulsa();
        }
        vendaAvulsa.prepararConexao();
        return vendaAvulsa;
    }

    public AmbienteInterfaceFacade getAmbiente() throws Exception {
        if (ambiente == null) {
            ambiente = new Ambiente();
        }
        ambiente.prepararConexao();
        return ambiente;
    }

    public CategoriaProdutoInterfaceFacade getCategoriaProduto() throws Exception {
        if (categoriaProduto == null) {
            categoriaProduto = new CategoriaProduto();
        }
        categoriaProduto.prepararConexao();
        return categoriaProduto;
    }

    public ComposicaoInterfaceFacade getComposicao() throws Exception {
        if (composicao == null) {
            composicao = new Composicao();
        }
        composicao.prepararConexao();
        return composicao;
    }

    public ComposicaoModalidade getComposicaoModalidade() throws Exception {
        if (composicaoModalidade == null) {
            composicaoModalidade = new ComposicaoModalidade();
        }
        composicaoModalidade.prepararConexao();
        return composicaoModalidade;
    }

    public CondicaoPagamentoInterfaceFacade getCondicaoPagamento() throws Exception {
        if (condicaoPagamento == null) {
            condicaoPagamento = new CondicaoPagamento();
        }
        condicaoPagamento.prepararConexao();
        return condicaoPagamento;
    }

    public CondicaoPagamentoParcela getCondicaoPagamentoParcela() throws Exception {
        if (condicaoPagamentoParcela == null) {
            condicaoPagamentoParcela = new CondicaoPagamentoParcela();
        }
        condicaoPagamentoParcela.prepararConexao();
        return condicaoPagamentoParcela;
    }

    public DescontoInterfaceFacade getDesconto() throws Exception {
        if (desconto == null) {
            desconto = new Desconto();
        }
        desconto.prepararConexao();
        return desconto;
    }

    public DuracaoInterfaceFacade getDuracao() throws Exception {
        if (duracao == null) {
            duracao = new Duracao();
        }
        duracao.prepararConexao();
        return duracao;
    }

    public HorarioInterfaceFacade getHorario() throws Exception {
        if (horario == null) {
            horario = new Horario();
        }
        horario.prepararConexao();
        return horario;
    }

    public HorarioAcessoSistemaInterfaceFacade getHorarioAcessoSistema() throws Exception {
        if (horarioAcessoSistema == null) {
            horarioAcessoSistema = new HorarioAcessoSistema();
        }
        horarioAcessoSistema.prepararConexao();
        return horarioAcessoSistema;
    }

    public HorarioDisponibilidadeInterfaceFacade getHorarioDisponibilidade() throws Exception {
        if (horarioDisponibilidade == null) {
            horarioDisponibilidade = new HorarioDisponibilidade();
        }
        horarioDisponibilidade.prepararConexao();
        return horarioDisponibilidade;
    }

    public HorarioTurmaInterfaceFacade getHorarioTurma() throws Exception {
        if (horarioTurma == null) {
            horarioTurma = new HorarioTurma();
        }
        horarioTurma.prepararConexao();
        return horarioTurma;
    }

    public ModalidadeInterfaceFacade getModalidade() throws Exception {
        if (modalidade == null) {
            modalidade = new Modalidade();
        }
        modalidade.prepararConexao();
        return modalidade;
    }

    public TipoModalidadeInterfaceFacade getTipoModalidade() throws Exception {
        if (tipoModalidade == null) {
            tipoModalidade = new TipoModalidade();
        }
        tipoModalidade.prepararConexao();
        return tipoModalidade;
    }

    public ModalidadeEmpresa getModalidadeEmpresa() throws Exception {
        if (modalidadeEmpresa == null) {
            modalidadeEmpresa = new ModalidadeEmpresa();
        }
        modalidadeEmpresa.prepararConexao();
        return modalidadeEmpresa;
    }

    public NivelTurmaInterfaceFacade getNivelTurma() throws Exception {
        if (nivelTurma == null) {
            nivelTurma = new NivelTurma();
        }
        nivelTurma.prepararConexao();
        return nivelTurma;
    }

    public PlanoComposicao getPlanoComposicao() throws Exception {
        if (planoComposicao == null) {
            planoComposicao = new PlanoComposicao();
        }
        planoComposicao.prepararConexao();
        return planoComposicao;
    }

    public PlanoCondicaoPagamento getPlanoPagamento() throws Exception {
        if (planoPagamento == null) {
            planoPagamento = new PlanoCondicaoPagamento();
        }
        planoPagamento.prepararConexao();
        return planoPagamento;
    }

    public PlanoDuracao getPlanoDuracao() throws Exception {
        if (planoDuracao == null) {
            planoDuracao = new PlanoDuracao();
        }
        planoDuracao.prepararConexao();
        return planoDuracao;
    }

    public PlanoHorario getPlanoHorario() throws Exception {
        if (planoHorario == null) {
            planoHorario = new PlanoHorario();
        }
        planoHorario.prepararConexao();
        return planoHorario;
    }

    public PlanoModalidade getPlanoModalidade() throws Exception {
        if (planoModalidade == null) {
            planoModalidade = new PlanoModalidade();
        }
        planoModalidade.prepararConexao();
        return planoModalidade;
    }

    public PlanoModalidadeVezesSemanaInterfaceFacade getPlanoModalidadeVezesSemana() throws Exception {
        if (planoModalidadeVezesSemana == null) {
            planoModalidadeVezesSemana = new PlanoModalidadeVezesSemana();
        }
        planoModalidadeVezesSemana.prepararConexao();
        return planoModalidadeVezesSemana;
    }

    public PlanoProdutoSugeridoInterfaceFacade getPlanoProdutoSugerido() throws Exception {
        if (planoProdutoSugerido == null) {
            planoProdutoSugerido = new PlanoProdutoSugerido();
        }
        planoProdutoSugerido.prepararConexao();
        return planoProdutoSugerido;
    }

    public PlanoTextoPadraoInterfaceFacade getPlanoTextoPadrao() throws Exception {
        if (planoTextoPadrao == null) {
            planoTextoPadrao = new PlanoTextoPadrao();
        }
        planoTextoPadrao.prepararConexao();
        return planoTextoPadrao;
    }

    public ModeloOrcamentoInterfaceFacade getModeloOrcamento() throws Exception {
        if (modeloOrcamento == null) {
            modeloOrcamento = new ModeloOrcamento();
        }
        modeloOrcamento.prepararConexao();
        return modeloOrcamento;
    }

    public OrcamentoRelInterfaceFacade getOrcamentoRel() throws Exception {
        if (orcamentoRel == null) {
            orcamentoRel = new OrcamentoRel();
        }
        orcamentoRel.prepararConexao();
        return orcamentoRel;
    }

    public OrcamentoInterfaceFacade getOrcamento() throws Exception {
        if (orcamento == null) {
            orcamento = new Orcamento();
        }
        orcamento.prepararConexao();
        return orcamento;
    }

    public PlanoTextoPadraoTag getPlanoTextoPadraoTag() throws Exception {
        if (planoTextoPadraoTag == null) {
            planoTextoPadraoTag = new PlanoTextoPadraoTag();
        }
        planoTextoPadraoTag.prepararConexao();
        return planoTextoPadraoTag;
    }

    public ProdutoInterfaceFacade getProduto() throws Exception {
        if (produto == null) {
            produto = new Produto();
        }
        produto.prepararConexao();
        return produto;
    }

    public ProdutoSugeridoInterfaceFacade getProdutoSugerido() throws Exception {
        if (produtoSugerido == null) {
            produtoSugerido = new ProdutoSugerido();
        }
        produtoSugerido.prepararConexao();
        return produtoSugerido;
    }

    public CardexInterfaceFacade getCardex() throws Exception {
        if (cardex == null) {
            cardex = new Cardex();
        }
        cardex.prepararConexao();
        return cardex;
    }

    public CompraItensInterfaceFacade getCompraItens() throws Exception {
        if (compraItens == null) {
            compraItens = new CompraItens();
        }
        compraItens.prepararConexao();
        return compraItens;
    }

    public CompraInterfaceFacade getCompra() throws Exception {
        if (compra == null) {
            compra = new Compra();
        }
        compra.prepararConexao();
        return compra;
    }

    public CompraDocumentosInterfaceFacade getCompraDocumentos() throws Exception {
        if (compraDocumentos == null) {
            compraDocumentos = new CompraDocumentos();
        }
        compraDocumentos.prepararConexao();
        return compraDocumentos;
    }

    public ContaFinanceiroCompraInterfaceFacade getContaFinanceiroCompra() throws Exception {
        if (contaFinanceiroCompra == null) {
            contaFinanceiroCompra = new ContaFinanceiroCompra();
        }
        contaFinanceiroCompra.prepararConexao();
        return contaFinanceiroCompra;
    }

    public ProdutoEstoqueInterfaceFacade getProdutoEstoque() throws Exception {
        if (produtoEstoque == null) {
            produtoEstoque = new ProdutoEstoque();
        }
        produtoEstoque.prepararConexao();
        return produtoEstoque;
    }

    public BalancoInterfaceFacade getBalanco() throws Exception {
        if (balanco == null) {
            balanco = new Balanco();
        }
        balanco.prepararConexao();
        return balanco;
    }

    public BalancoItensInterfaceFacade getBalancoItens() throws Exception {
        if (balancoItens == null) {
            balancoItens = new BalancoItens();
        }
        balancoItens.prepararConexao();
        return balancoItens;
    }

    public TurmaInterfaceFacade getTurma() throws Exception {
        if (turma == null) {
            turma = new Turma();
        }
        turma.prepararConexao();
        return turma;
    }

    public VezesSemanaInterfaceFacade getVezesSemana() throws Exception {
        if (vezesSemana == null) {
            vezesSemana = new VezesSemana();
        }
        vezesSemana.prepararConexao();
        return vezesSemana;
    }

    public SituacaoContratoAnaliticoDW getSituacaoContratoAnaliticoDW() throws Exception {
        if (situacaoContratoAnaliticoDW == null) {
            situacaoContratoAnaliticoDW = new SituacaoContratoAnaliticoDW();
        }
        situacaoContratoAnaliticoDW.prepararConexao();
        return situacaoContratoAnaliticoDW;
    }

    public SituacaoContratoSinteticoDW getSituacaoContratoSinteticoDW() throws Exception {
        if (situacaoContratoSinteticoDW == null) {
            situacaoContratoSinteticoDW = new SituacaoContratoSinteticoDW();
        }
        situacaoContratoSinteticoDW.prepararConexao();
        return situacaoContratoSinteticoDW;
    }

    public RotatividadeAnaliticoDW getRotatividadeAnaliticoDW() throws Exception {
        if (rotatividadeAnaliticoDW == null) {
            rotatividadeAnaliticoDW = new RotatividadeAnaliticoDW();
        }
        rotatividadeAnaliticoDW.prepararConexao();
        return rotatividadeAnaliticoDW;
    }

    public RelatorioCliente getRelatorioCliente() throws Exception {
        if (relatorioCliente == null) {
            relatorioCliente = new RelatorioCliente();
        }
        relatorioCliente.prepararConexao();
        return relatorioCliente;
    }

    public RelatorioBVs getRelatorioBVs() throws Exception {
        if (relatorioBVs == null) {
            relatorioBVs = new RelatorioBVs();
        }
        relatorioBVs.prepararConexao();
        return relatorioBVs;
    }

    public SGPModalidadeComTurmaRel getSGPModalidadeComTurmaRel() throws Exception {
        if (sGPModalidadeComTurmaRel == null) {
            sGPModalidadeComTurmaRel = new SGPModalidadeComTurmaRel();
        }
        sGPModalidadeComTurmaRel.prepararConexao();
        return sGPModalidadeComTurmaRel;
    }

    public SGPTurmaRel getSGPTurmaRel() throws Exception {
        if (sGPTurmaRel == null) {
            sGPTurmaRel = new SGPTurmaRel();
        }
        sGPTurmaRel.prepararConexao();
        return sGPTurmaRel;
    }

    public SGPModalidadeSemTurmaRel getsGPModalidadeSemTurmaRel() throws Exception {
        if (sGPModalidadeSemTurmaRel == null) {
            sGPModalidadeSemTurmaRel = new SGPModalidadeSemTurmaRel();
        }
        sGPModalidadeSemTurmaRel.prepararConexao();
        return sGPModalidadeSemTurmaRel;
    }

    public SGPAvaliacaoFisicaRel getSGPAvaliacaoFisicaRel() throws Exception {
        if (sgpAvaliacaoFisicaRel == null) {
            sgpAvaliacaoFisicaRel = new SGPAvaliacaoFisicaRel();
        }
        sgpAvaliacaoFisicaRel.prepararConexao();
        return sgpAvaliacaoFisicaRel;
    }

    public PlanoCondicaoPagamento getPlanoCondicaoPagamento() throws Exception {
        if (planoCondicaoPagamento == null) {
            planoCondicaoPagamento = new PlanoCondicaoPagamento();
        }
        planoCondicaoPagamento.prepararConexao();
        return planoCondicaoPagamento;
    }

    public PlanoInterfaceFacade getPlano() throws Exception {
        if (plano == null) {
            plano = new Plano();
        }
        plano.prepararConexao();
        return plano;
    }

    public PlanoEmpresa getPlanoEmpresa() throws Exception {
        if(planoEmpresa == null){
            planoEmpresa =  new PlanoEmpresa();
        }

        planoEmpresa.prepararConexao();
        return planoEmpresa;
    }

    public Aditivo getAditivo() throws Exception {
        if (aditivo == null ){
            aditivo =  new Aditivo();
        }

        aditivo.prepararConexao();
        return aditivo;
    }

    public PlanoRedeEmpresa getPlanoRedeEmpresa() throws Exception {
        if( planorRedeEmpresa == null ){
            planorRedeEmpresa =  new PlanoRedeEmpresa();
        }

        planorRedeEmpresa.prepararConexao();
        return planorRedeEmpresa;
    }

    public ProdutoRedeEmpresa getProdutoRedeEmpresa() throws Exception {
        if (produtoRedeEmpresa == null){
            produtoRedeEmpresa = new ProdutoRedeEmpresa();
        }
        produtoRedeEmpresa.prepararConexao();
        return produtoRedeEmpresa;
    }

    public FornecedorRedeEmpresa getFornecedorRedeEmpresa() throws Exception {
        if (fornecedorRedeEmpresa == null){
            fornecedorRedeEmpresa = new FornecedorRedeEmpresa();
        }
        fornecedorRedeEmpresa.prepararConexao();
        return fornecedorRedeEmpresa;
    }

    public FeriadoRedeEmpresa getFeriadoRedeEmpresa() throws Exception {
        if( feriadoRedeEmpresa == null ){
            feriadoRedeEmpresa =  new FeriadoRedeEmpresa();
        }

        feriadoRedeEmpresa.prepararConexao();
        return feriadoRedeEmpresa;
    }

    public PerfilAcessoRedeEmpresa getPerfilAcessoRedeEmpresa() throws Exception {
        if( perfilAcessoRedeEmpresa == null ){
            perfilAcessoRedeEmpresa =  new PerfilAcessoRedeEmpresa();
        }

        perfilAcessoRedeEmpresa.prepararConexao();
        return perfilAcessoRedeEmpresa;
    }

    public UsuarioRedeEmpresa getUsuarioRedeEmpresa() throws Exception {
        if( usuarioRedeEmpresa == null ){
            usuarioRedeEmpresa =  new UsuarioRedeEmpresa();
        }

        usuarioRedeEmpresa.prepararConexao();
        return usuarioRedeEmpresa;
    }

    public ColaboradorRedeEmpresa getColaboradorRedeEmpresa() throws Exception {
        if( colaboradorRedeEmpresa == null ){
            colaboradorRedeEmpresa =  new ColaboradorRedeEmpresa();
        }

        colaboradorRedeEmpresa.prepararConexao();
        return colaboradorRedeEmpresa;
    }

    public MalaDiretaRedeEmpresa getMalaDiretaRedeEmpresa() throws Exception {
        if( malaDiretaRedeEmpresa == null ){
            malaDiretaRedeEmpresa =  new MalaDiretaRedeEmpresa();
        }

        malaDiretaRedeEmpresa.prepararConexao();
        return malaDiretaRedeEmpresa;
    }

    public PlanoContaRedeEmpresa getPlanoContaRedeEmpresa() throws Exception {
        if( planoContaRedeEmpresa == null ){
            planoContaRedeEmpresa =  new PlanoContaRedeEmpresa();
        }

        planoContaRedeEmpresa.prepararConexao();
        return planoContaRedeEmpresa;
    }

    public FecharMetaDetalhadoInterfaceFacade getFecharMetaDetalhado() throws Exception {
        if (fecharMetaDetalhado == null) {
            fecharMetaDetalhado = new FecharMetaDetalhado();
        }
        fecharMetaDetalhado.prepararConexao();
        return fecharMetaDetalhado;
    }

    public ConvenioCobrancaInterfaceFacade getConvenioCobranca() throws Exception {
        if (convenioCobranca == null) {
            convenioCobranca = new ConvenioCobranca();
        }
        convenioCobranca.prepararConexao();
        return convenioCobranca;
    }

    public Cep getCep() throws Exception {
        if (cep == null) {
            cep = new Cep();
        }
        cep.prepararConexao();
        return cep;
    }

    public AberturaMetaInterfaceFacade getAberturaMeta() throws Exception {
        if (aberturaMeta == null) {
            aberturaMeta = new AberturaMeta();
        }
        aberturaMeta.prepararConexao();
        return aberturaMeta;
    }

    public AgendaInterfaceFacade getAgenda() throws Exception {
        if (agenda == null) {
            agenda = new Agenda();
        }
        agenda.prepararConexao();
        return agenda;
    }

    public FecharMetaInterfaceFacade getFecharMeta() throws Exception {
        if (fecharMeta == null) {
            fecharMeta = new FecharMeta();
        }
        fecharMeta.prepararConexao();
        return fecharMeta;
    }

    public RotatividadeSinteticoDW getRotatividadeSinteticoDW() throws Exception {
        if (rotatividadeSinteticoDW == null) {
            rotatividadeSinteticoDW = new RotatividadeSinteticoDW();
        }
        rotatividadeSinteticoDW.prepararConexao();
        return rotatividadeSinteticoDW;
    }

    public SituacaoClienteSinteticoDW getSituacaoClienteSinteticoDW() throws Exception {
        if (situacaoClienteSinteticoDW == null) {
            situacaoClienteSinteticoDW = new SituacaoClienteSinteticoDW();
        }
        situacaoClienteSinteticoDW.prepararConexao();
        return situacaoClienteSinteticoDW;
    }

    public EventoInterfaceFacade getEvento() throws Exception {
        if (evento == null) {
            evento = new Evento();
        }
        evento.prepararConexao();
        return evento;
    }

    public HistoricoVinculoInterfaceFacade getHistoricoVinculo() throws Exception {
        if (historicoVinculo == null) {
            historicoVinculo = new HistoricoVinculo();
        }
        historicoVinculo.prepararConexao();
        return historicoVinculo;
    }

    public ObjecaoInterfaceFacade getObjecao() throws Exception {
        if (objecao == null) {
            objecao = new Objecao();
        }
        objecao.prepararConexao();
        return objecao;
    }

    public CartaoCredito getCartaoCredito() throws Exception {
        if (cartaoCredito == null) {
            cartaoCredito = new CartaoCredito();
        }
        cartaoCredito.prepararConexao();
        return cartaoCredito;
    }

    public OperadoraCartaoInterfaceFacade getOperadoraCartao() throws Exception {
        if (operadoraCartao == null) {
            operadoraCartao = new OperadoraCartao();
        }
        operadoraCartao.prepararConexao();
        return operadoraCartao;
    }

    public TaxaCartao getTaxaCartao() throws Exception {
        if (taxaCartao == null) {
            taxaCartao = new TaxaCartao();
        }
        taxaCartao.prepararConexao();
        return taxaCartao;
    }

    public TaxaBoleto getTaxaBoleto() throws Exception {
        if (taxaBoleto == null) {
            taxaBoleto = new TaxaBoleto();
        }
        taxaBoleto.prepararConexao();
        return taxaBoleto;
    }

    public ConfiguracaoSistemaCadastroClienteInterfaceFacade getConfiguracaoSistemaCadastroCliente() throws Exception {
        if (configuracaoSistemaCadastroCliente == null) {
            configuracaoSistemaCadastroCliente = new ConfiguracaoSistemaCadastroCliente();
        }
        configuracaoSistemaCadastroCliente.prepararConexao();
        return configuracaoSistemaCadastroCliente;
    }

    public ClientePotencial getClientePotencial() throws Exception {
        if (clientePotencial == null) {
            clientePotencial = new ClientePotencial();
        }
        clientePotencial.prepararConexao();
        return clientePotencial;
    }

    public MetaCrescimento getMetaCrescimento() throws Exception {
        if (metaCrescimento == null) {
            metaCrescimento = new MetaCrescimento();
        }
        metaCrescimento.prepararConexao();
        return metaCrescimento;
    }

    public ValidacaoLocalAcessoInterfaceFacade getValidacaoLocalAcesso() throws Exception {
        if (validacaoLocalAcesso == null) {
            validacaoLocalAcesso = new ValidacaoLocalAcesso();
        }
        validacaoLocalAcesso.prepararConexao();
        return validacaoLocalAcesso;
    }

    public DescontoRenovacaoInterfaceFacade getDescontoRenovacao() throws Exception {
        if (descontoRenovacao == null) {
            descontoRenovacao = new DescontoRenovacao();
        }
        descontoRenovacao.prepararConexao();
        return descontoRenovacao;
    }

    public ControleTaxaPersonalInterfaceFacade getControleTaxaPersonal() throws Exception {
        if (controleTaxaPersonal == null) {
            controleTaxaPersonal = new ControleTaxaPersonal();
        }
        controleTaxaPersonal.prepararConexao();
        return controleTaxaPersonal;
    }

    public ItemTaxaPersonalInterfaceFacade getItemTaxaPersonal() throws Exception {
        if (itemTaxaPersonal == null) {
            itemTaxaPersonal = new ItemTaxaPersonal();
        }
        itemTaxaPersonal.prepararConexao();
        return itemTaxaPersonal;
    }

    public CupomFiscal getCupomFiscal() throws Exception {
        if (cupomFiscal == null) {
            cupomFiscal = new CupomFiscal();
        }
        cupomFiscal.prepararConexao();
        return cupomFiscal;
    }

    public NFSeEmitida getNFSeEmitida() throws Exception {
        if (nfSeEmitida == null) {
            nfSeEmitida = new NFSeEmitida();
        }
        nfSeEmitida.prepararConexao();
        return nfSeEmitida;
    }

    public FinanceiroFacade getFinanceiro() throws Exception {
        if (financeiro == null) {
            financeiro = new FinanceiroFacade();
        }
        financeiro.prepararConexao();
        return financeiro;
    }

    public ContaStoneInterfaceFacade getContaStone() throws Exception {
        if (contaStone == null) {
            contaStone = new ContaStone();
        }
        contaStone.prepararConexao();
        return contaStone;
    }

    public PagamentoStoneInterfaceFacade getPagamentoStone() throws Exception {
        if (pagamentoStone == null) {
            pagamentoStone = new PagamentoStone();
        }
        pagamentoStone.prepararConexao();
        return pagamentoStone;
    }

    public TransferenciaStoneInterfaceFacade getTransferenciaStone() throws Exception {
        if (transferenciaStone == null) {
            transferenciaStone = new TransferenciaStone();
        }
        transferenciaStone.prepararConexao();
        return transferenciaStone;
    }

    public RetornoStoneInterfaceFacade getRetornoStone() throws Exception {
        if (retornoStone == null) {
            retornoStone = new RetornoStone();
        }
        retornoStone.prepararConexao();
        return retornoStone;
    }

    public PlanoRecorrencia getPlanoRecorrencia() throws Exception {
        if (planoRecorrencia == null) {
            planoRecorrencia = new PlanoRecorrencia();
        }
        planoRecorrencia.prepararConexao();
        return planoRecorrencia;
    }

    public ContratoRecorrenciaInterfaceFacade getContratoRecorrencia() throws Exception {
        if (contratoRecorrencia == null) {
            contratoRecorrencia = new ContratoRecorrencia();
        }
        contratoRecorrencia.prepararConexao();
        return contratoRecorrencia;
    }

    public TransacaoInterfaceFacade getTransacao() throws Exception {
        if (transacao == null) {
            transacao = new Transacao();
        }
        transacao.prepararConexao();
        return transacao;
    }

    public LoteInterfaceFacade getLote() throws Exception {
        if (lote == null) {
            lote = new Lote();
        }
        lote.prepararConexao();
        return lote;
    }

    public VendaRapidaRecorrenteService getVendaService() throws Exception {
        if (vendaService == null) {
            vendaService = new VendaRapidaRecorrenteServiceImpl();
        }
        vendaService.prepararConexao();
        return vendaService;
    }

    public PresencaInterfaceFacade getPresenca() throws Exception {
        if (presenca == null) {
            presenca = new Presenca();
        }
        presenca.prepararConexao();
        return presenca;
    }

    public FaixaHorarioAcessoClienteInterfaceFacade getFaixaHorarioAcessoCliente() throws Exception {
        if (faixaHorarioAcessoCliente == null) {
            faixaHorarioAcessoCliente = new FaixaHorarioAcessoCliente();
        }
        faixaHorarioAcessoCliente.prepararConexao();
        return faixaHorarioAcessoCliente;
    }

    public TiposVinculosFaseInterfaceFacade getTiposVinculosFase() throws Exception {
        if (tiposVinculosFase == null) {
            tiposVinculosFase = new TiposVinculosFase();
        }
        tiposVinculosFase.prepararConexao();
        return tiposVinculosFase;
    }

    public FornecedorInterfaceFacade getFornecedor() throws Exception {
        if (fornecedor == null) {
            fornecedor = new Fornecedor();
        }
        fornecedor.prepararConexao();
        return fornecedor;
    }

    public MetaFinanceiraEmpresaInterfaceFacade getMetaFinanceiraEmpresa() throws Exception {
        if (metaFinanceiraEmpresa == null) {
            metaFinanceiraEmpresa = new MetaFinanceiraEmpresa();
        }
        metaFinanceiraEmpresa.prepararConexao();
        return metaFinanceiraEmpresa;
    }

    public RelatorioOrcamentarioConfigInterfaceFacade getRelatorioOrcamentarioConfig() throws Exception {
        if (relatorioOrcamentarioConfig == null) {
            relatorioOrcamentarioConfig = new RelatorioOrcamentarioConfig();
        }
        relatorioOrcamentarioConfig.prepararConexao();
        return relatorioOrcamentarioConfig;
    }

    public RelatorioOrcamentarioConfigPrevisaoInterfaceFacade getRelatorioOrcamentarioConfigPrevisao() throws Exception {
        if (relatorioOrcamentarioConfigPrevisao == null) {
            relatorioOrcamentarioConfigPrevisao = new RelatorioOrcamentarioConfigPrevisao();
        }
        relatorioOrcamentarioConfigPrevisao.prepararConexao();
        return relatorioOrcamentarioConfigPrevisao;
    }

    public MetaFinanceiraEmpresaValoresInterfaceFacade getMetaFinanceiraEmpresaValores() throws Exception {
        if (metaFinanceiraEmpresaValores == null) {
            metaFinanceiraEmpresaValores = new MetaFinanceiraEmpresaValores();
        }
        metaFinanceiraEmpresaValores.prepararConexao();
        return metaFinanceiraEmpresaValores;
    }

    public MetaFinanceiraConsultorInterfaceFacade getMetaFinanceiraConsultor() throws Exception {
        if (metaFinanceiraConsultor == null) {
            metaFinanceiraConsultor = new MetaFinanceiraConsultor();
        }
        metaFinanceiraConsultor.prepararConexao();
        return metaFinanceiraConsultor;
    }

    public MetaFinanceiroBI getMetaFinanceiroBI() throws Exception {
        if (metaFinanceiroBI == null) {
            metaFinanceiroBI = new MetaFinanceiroBI();
        }
        metaFinanceiroBI.prepararConexao();
        return metaFinanceiroBI;
    }

    public ReciboDevolucao getReciboDevolucao() throws Exception {
        if (reciboDevolucao == null) {
            reciboDevolucao = new ReciboDevolucao();
        }
        reciboDevolucao.prepararConexao();
        return reciboDevolucao;
    }

    // INTERFACES ESTUDIO
    public ConfiguracaoEstudioInterfaceFacade getConfiguracaoEstudio() throws Exception {
        if (configuracaoEstudio == null) {
            configuracaoEstudio = new ConfiguracaoEstudio();
        }
        configuracaoEstudio.prepararConexao();
        return configuracaoEstudio;
    }

    public PreferenciaAgendaInterfaceFacade getPreferenciaAgenda() throws Exception {
        if (preferenciaAgenda == null) {
            preferenciaAgenda = new PreferenciaAgenda();
        }
        preferenciaAgenda.prepararConexao();
        return preferenciaAgenda;
    }

    public DisponibilidadeInterfaceFacade getDisponibilidade() throws Exception {
        if (disponibilidade == null) {
            disponibilidade = new Disponibilidade();
        }
        disponibilidade.prepararConexao();
        return disponibilidade;
    }

    public EmpresaFechadaInterfaceFacade getEmpresaFechada() throws Exception {
        if (empresaFechada == null) {
            empresaFechada = new EmpresaFechada();
        }
        empresaFechada.prepararConexao();
        return empresaFechada;
    }

    public TipoHorarioInterfaceFacade getTipoHorario() throws Exception {
        if (tipoHorario == null) {
            tipoHorario = new TipoHorario();
        }
        tipoHorario.prepararConexao();
        return tipoHorario;
    }

    public PacoteInterfaceFacade getPacote() throws Exception {
        if (pacote == null) {
            pacote = new Pacote();
        }
        pacote.prepararConexao();
        return pacote;
    }

    public RelProdutoColaboradorInterfaceFacade getRelProdutoColaborador() throws Exception {

        if (relProdutoColaborador == null) {
            relProdutoColaborador = new RelProdutoColaborador();
        }
        relProdutoColaborador.prepararConexao();

        return relProdutoColaborador;
    }

    public RelProdutoAmbienteInterfaceFacade getRelProdutoAmbiente() throws Exception {

        if (relProdutoAmbiente == null) {
            relProdutoAmbiente = new RelProdutoAmbiente();
        }
        relProdutoAmbiente.prepararConexao();

        return relProdutoAmbiente;
    }

    public AgendaEstudioInterfaceFacade getAgendaEstudio() throws Exception {
        if (agendaEstudio == null) {
            agendaEstudio = new AgendaEstudio();
        }
        agendaEstudio.prepararConexao();
        return agendaEstudio;
    }

    public ColaboradorIndispInterfaceFacade getColaboradorIndisponibilidade() throws Exception {
        if (colaboradorIndisponibilidade == null) {
            colaboradorIndisponibilidade = new ColaboradorIndisp();
        }
        colaboradorIndisponibilidade.prepararConexao();

        return colaboradorIndisponibilidade;
    }

    public AmbienteIndispInterfaceFacade getAmbienteIndisponibilidade() throws Exception {
        if (ambienteIndisponibilidade == null) {
            ambienteIndisponibilidade = new AmbienteIndisp();
        }
        ambienteIndisponibilidade.prepararConexao();

        return ambienteIndisponibilidade;
    }

    public CupomFiscalServiceFacade getCupomFiscalService() throws Exception {
        if (cupomFiscalService == null) {
            cupomFiscalService = new CupomFiscalServiceImpl();
        }
        cupomFiscalService.prepararConexao();

        return cupomFiscalService;
    }

    public NotaFiscalConsumidorEletronicaInterfaceFacade getNotaFiscalConsumidorEletronica() throws Exception {
        if (notaFiscalConsumidorEletronica == null) {
            notaFiscalConsumidorEletronica = new NotaFiscalConsumidorEletronica();
        }
        notaFiscalConsumidorEletronica.prepararConexao();
        return notaFiscalConsumidorEletronica;
    }

    public MovProdutoModalidadeInterfaceFacade getMovProdutoModalidade() throws Exception {
        if (movProdutoModalidade == null) {
            movProdutoModalidade = new MovProdutoModalidade();
        }
        movProdutoModalidade.prepararConexao();
        return movProdutoModalidade;
    }

    public RelatorioComissaoInterfaceFacade getRelatorioComissao() throws Exception {
        if (relatorioComissao == null) {
            relatorioComissao = new RelatorioComissao();
        }
        relatorioComissao.prepararConexao();
        return relatorioComissao;
    }

    public RelatorioGeralAgendamentosInterfaceFacade getRelatorioGeralAgendamentos() throws Exception {
        if (relatorioGeralAgendamentos == null) {
            relatorioGeralAgendamentos = new RelatorioGeralAgendamentos();
        }
        relatorioGeralAgendamentos.prepararConexao();
        return relatorioGeralAgendamentos;
    }

    public RelatorioGeralClientesInativosInterfaceFacade getRelatorioGeralClientesInativos() throws Exception {
        if (relatorioGeralClientesInativos == null) {
            relatorioGeralClientesInativos = new RelatorioGeralClientesInativos();
        }
        relatorioGeralClientesInativos.prepararConexao();
        return relatorioGeralClientesInativos;
    }

    public SocialMailingInterfaceFacade getSocialMailing() throws Exception {
        if (socialMailing == null) {
            socialMailing = new SocialMail();
        }
        socialMailing.prepararConexao();
        return socialMailing;
    }

    public SocialMailGrupoInterfaceFacade getSocialMailGrupo() throws Exception {
        if (socialMailGrupo == null) {
            socialMailGrupo = new SocialMailGrupo();
        }
        socialMailGrupo.prepararConexao();
        return socialMailGrupo;
    }

    public ReposicaoInterfaceFacade getReposicao() throws Exception {
        if (reposicao == null) {
            reposicao = new Reposicao();
        }
        reposicao.prepararConexao();
        return reposicao;
    }

    public RelatorioFechamentoDiarioInterfaceFacade getRelatorioFechamentoDiario() throws Exception {

        if (relatorioFechamentoDiario == null) {
            relatorioFechamentoDiario = new RelatorioFechamentoDiario();
        }
        relatorioFechamentoDiario.prepararConexao();

        return relatorioFechamentoDiario;
    }

    public ModalidadeComissaoColaboradorInterfaceFacade getModalidadeComissaoColaborador() throws Exception {
        if (modalidadeComissaoColaborador == null) {
            modalidadeComissaoColaborador = new ModalidadeComissaoColaborador();
        }
        modalidadeComissaoColaborador.prepararConexao();
        return modalidadeComissaoColaborador;
    }

    public TurmaComissaoColaboradorInterfaceFacade getTurmaComissaoColaborador() throws Exception {
        if (turmaComissaoColaborador == null) {
            turmaComissaoColaborador = new TurmaComissaoColaborador();
        }
        turmaComissaoColaborador.prepararConexao();
        return turmaComissaoColaborador;
    }

    public AlunoComissaoColaboradorInterfaceFacade getAlunoComissaoColaborador() throws Exception {
        if (alunoComissaoColaborador == null) {
            alunoComissaoColaborador = new AlunoComissaoColaborador();
        }
        alunoComissaoColaborador.prepararConexao();
        return alunoComissaoColaborador;
    }

    public AutorizacaoCobrancaClienteInterfaceFacade getAutorizacaoCobrancaCliente() throws Exception {
        if (autorizacaoCobrancaCliente == null) {
            autorizacaoCobrancaCliente = new AutorizacaoCobrancaCliente();
        }
        autorizacaoCobrancaCliente.prepararConexao();
        return autorizacaoCobrancaCliente;
    }

    public AutorizacaoCobrancaColaboradorInterfaceFacade getAutorizacaoCobrancaColaborador() throws Exception {
        if (autorizacaoCobrancaColaborador == null) {
            autorizacaoCobrancaColaborador = new AutorizacaoCobrancaColaborador();
        }
        autorizacaoCobrancaColaborador.prepararConexao();
        return autorizacaoCobrancaColaborador;
    }

    public NotaFiscalDeServicoInterfaceFacade getNotaNFSe() throws Exception {
        if (notaNFSe == null) {
            notaNFSe = new NotaFiscalDeServico();
        }
        notaNFSe.prepararConexao();
        return notaNFSe;
    }

    public ItemRPSNFeInterfaceFacade getItemNFSe() throws Exception {
        if (itemNFSe == null) {
            itemNFSe = new ItemRPSNFe();
        }
        itemNFSe.prepararConexao();
        return itemNFSe;
    }

    public EmpresaNFeInterfaceFacade getEmpresaNFe() throws Exception {
        if (empresaNFe == null) {
            empresaNFe = new EmpresaNFe();
        }
        empresaNFe.prepararConexao();
        return empresaNFe;
    }

    public MunicipioNFeInterfaceFacade getMunicipioNFe() throws Exception {
        if (municipioNFe == null) {
            municipioNFe = new MunicipioNFe();
        }
        municipioNFe.prepararConexao();
        return municipioNFe;
    }

    public RecebivelAvulsoInterfaceFacade getRecebivelAvulso() throws Exception {
        if (recebivelAvulso == null) {
            recebivelAvulso = new RecebivelAvulso();
        }
        recebivelAvulso.prepararConexao();
        return recebivelAvulso;
    }

    public ConfiguracaoFinanceiroInterfaceFacade getConfiguracaoFinanceiro() throws Exception {
        if (configuracaoFinanceiro == null) {
            configuracaoFinanceiro = new ConfiguracaoFinanceiro();
        }
        configuracaoFinanceiro.prepararConexao();
        return configuracaoFinanceiro;
    }

    public LoteNFeInterfaceFacade getLoteNFSe() throws Exception {
        if (loteNFSe == null) {
            loteNFSe = new LoteNFe();
        }
        loteNFSe.prepararConexao();
        return loteNFSe;
    }
    public LoteNFeInterfaceFacade getLoteNFSe(Connection con) throws Exception {
        if (loteNFSe == null) {
            loteNFSe = new LoteNFe(con);
        }
        loteNFSe.prepararConexao();
        return loteNFSe;
    }

    public MailingAgendamentoInterfaceFacade getMailingAgendamento() throws Exception {
        if (mailingAgendamento == null) {
            mailingAgendamento = new MailingAgendamento();
        }
        mailingAgendamento.prepararConexao();
        return mailingAgendamento;
    }

    public MailingHistoricoInterfaceFacade getMailingHistorico() throws Exception {
        if (mailingHistorico == null) {
            mailingHistorico = new MailingHistorico();
        }
        mailingHistorico.prepararConexao();
        return mailingHistorico;
    }

    public UsuarioMovelInterfaceFacade getUsuarioMovel() throws Exception {
        if (usuarioMovel == null) {
            usuarioMovel = new UsuarioMovel();
        }
        usuarioMovel.prepararConexao();
        return usuarioMovel;
    }

    public UsuarioEmailInterfaceFacade getUsuarioEmail() throws Exception {
        if (usuarioEmail == null) {
            usuarioEmail = new UsuarioEmail();
        }
        usuarioEmail.prepararConexao();
        return usuarioEmail;
    }

    public UsuarioTelefoneInterfaceFacade getUsuarioTelefone() throws Exception {
        if (usuarioTelefone == null) {
            usuarioTelefone = new UsuarioTelefone();
        }
        usuarioTelefone.prepararConexao();
        return usuarioTelefone;
    }

    public AutorizacaoAcessoGrupoEmpresarialInterfaceFacade getAutorizacaoAcessoGrupoEmpresarial() throws Exception {
        if (autorizacao == null) {
            autorizacao = new AutorizacaoAcessoGrupoEmpresarial();
        }
        autorizacao.prepararConexao();
        return autorizacao;
    }

    public IntegracaoAcessoGrupoEmpresarialInterfaceFacade getIntegracaoAcessoGrupoEmpresarial() throws Exception {
        if (integracao == null) {
            integracao = new IntegracaoAcessoGrupoEmpresarial();
        }
        integracao.prepararConexao();
        return integracao;
    }

    public ProdutoRel getProdutoRel() throws Exception {
        if (produtoRel == null) {
            produtoRel = new ProdutoRel();
        }
        produtoRel.prepararConexao();
        return produtoRel;
    }

    public ClientePorDuracaoRel getClientePorDuracaoRel() throws Exception {
        if (clientePorDuracaoRel == null) {
            clientePorDuracaoRel = new ClientePorDuracaoRel();
        }
        clientePorDuracaoRel.prepararConexao();
        return clientePorDuracaoRel;
    }

    public RelatorioRepasseRel getRelatorioRepasseRel() throws Exception {
        if (relatorioRepasseRel == null) {
            relatorioRepasseRel = new RelatorioRepasseRel();
        }
        relatorioRepasseRel.prepararConexao();
        return relatorioRepasseRel;
    }

    public CarteirasRel getCarteirasRel() throws Exception {
        if (carteirasRel == null) {
            carteirasRel = new CarteirasRel();
        }
        carteirasRel.prepararConexao();
        return carteirasRel;
    }

    public TextoPadraoInterfaceFacade getTextoPadrao() throws Exception {
        if (textoPadrao == null) {
            textoPadrao = new TextoPadrao();
        }
        textoPadrao.prepararConexao();
        return textoPadrao;
    }

    public ComissaoGeralConfiguracaoInterfaceFacade getComissaoGeralConfiguracao() throws Exception {
        if (comissaoGeralConfiguracao == null) {
            comissaoGeralConfiguracao = new ComissaoGeralConfiguracao();
        }
        comissaoGeralConfiguracao.prepararConexao();
        return comissaoGeralConfiguracao;
    }

    public ComissaoProdutoConfiguracaoInterfaceFacade getComissaoProdutoConfiguracao() throws Exception {
        if (comissaoProdutoConfiguracao == null) {
            comissaoProdutoConfiguracao = new ComissaoProdutoConfiguracao();
        }
        comissaoProdutoConfiguracao.prepararConexao();
        return comissaoProdutoConfiguracao;
    }

    public GestaoNotasInterfaceFacade getGestaoNotas() throws Exception {
        if (gestaoNotas == null) {
            gestaoNotas = new GestaoNotas();
        }
        gestaoNotas.prepararConexao();
        return gestaoNotas;
    }

    public AcoesStatusRemessaInterfaceFacade getAcoesStatus() throws Exception {
        if (acoesStatus == null) {
            acoesStatus = new AcoesStatusRemessa();
        }
        acoesStatus.prepararConexao();
        return acoesStatus;
    }

    public LancamentoProdutoColetivoInterfaceFacade getLancamentoProdutoColetivo() throws Exception {
        if (lancamentoProdutoColetivo == null) {
            lancamentoProdutoColetivo = new LancamentoProdutoColetivo();
        }
        lancamentoProdutoColetivo.prepararConexao();
        return lancamentoProdutoColetivo;
    }

    public AtestadoInterfaceFacade getAtestado() throws Exception {
        if (atestado == null) {
            atestado = new Atestado();
        }
        atestado.prepararConexao();
        return atestado;
    }

    public ArquivoInterfaceFacade getArquivo() throws Exception {
        if (arquivo == null) {
            arquivo = new Arquivo();
        }
        arquivo.prepararConexao();
        return arquivo;
    }

    public PlacaInterfaceFacade getPlaca() throws Exception {
        if (placa == null) {
            placa = new Placa();
        }
        placa.prepararConexao();
        return placa;
    }

    public LogAjusteGeralInterfaceFacade getLogAjusteGeral() throws Exception {
        if (logAjusteGeral == null) {
            logAjusteGeral = new LogAjusteGeral();
        }
        logAjusteGeral.prepararConexao();
        return logAjusteGeral;
    }

    public TicketMedioInterfaceFacade getTicketMedio() throws Exception {
        if (ticketMedio == null) {
            ticketMedio = new TicketMedio();
        }
        ticketMedio.prepararConexao();
        return ticketMedio;
    }

    public RemessaItemInterfaceFacade getRemessaItem() throws Exception {
        if (remessaItem == null) {
            remessaItem = new RemessaItem();
        }
        remessaItem.prepararConexao();
        return remessaItem;
    }

    public RemessaItemMovParcelaInterfaceFacade getRemessaItemMovParcela() throws Exception {
        if (remessaItemMovParcela == null) {
            remessaItemMovParcela = new RemessaItemMovParcela();
        }
        remessaItemMovParcela.prepararConexao();
        return remessaItemMovParcela;
    }

    public RetornoRemessaInterfaceFacade getRetornoRemessa() throws Exception {
        if (retornoRemessa == null) {
            retornoRemessa = new RetornoRemessa();
        }
        retornoRemessa.prepararConexao();
        return retornoRemessa;
    }

    public ContratoAssinaturaDigitalInterfaceFacade getContratoAssinaturaDigital() throws Exception {
        if (contratoAssinatura == null) {
            contratoAssinatura = new ContratoAssinaturaDigital();
        }
        contratoAssinatura.prepararConexao();
        return contratoAssinatura;
    }

    public RemessaCancelamentoItemInterfaceFacade getRemessaCancelamentoItem() throws Exception {
        if (remessaCancelamentoItem == null) {
            remessaCancelamentoItem = new RemessaCancelamentoItem();
        }
        remessaCancelamentoItem.prepararConexao();
        return remessaCancelamentoItem;
    }

    public Remessa getRemessa() throws  Exception{
        if(remessa == null){
            remessa = new Remessa();
        }
        remessa.prepararConexao();
        return remessa;
    }

    public FeedGestaoInterfaceFacade getFeedGestao() throws Exception {
        if (feedGestao == null) {
            feedGestao = new FeedGestao();
        }
        feedGestao.prepararConexao();
        return feedGestao;
    }

    public PaginaInicialInterfaceFacade getPaginaInicialFeed() throws Exception {
        if (paginaInicialFeed == null) {
            paginaInicialFeed = new PaginaInicialFeed();
        }
        paginaInicialFeed.prepararConexao();
        return paginaInicialFeed;
    }

    public DadosGerencialPmgInterfaceFacade getDadosGerencialPmg() throws Exception {
        if (dadosGerencialPmg == null) {
            dadosGerencialPmg = new DadosGerencialPmg();
        }
        dadosGerencialPmg.prepararConexao();
        return dadosGerencialPmg;
    }

    public PlanoExcecaoInterfaceFacade getPlanoExcecao() throws Exception {
        if (planoExcecao == null) {
            planoExcecao = new PlanoExcecao();
        }
        planoExcecao.prepararConexao();
        return planoExcecao;
    }

    public ComissaoMetaFinanceiraInterfaceFacade getComissaoMetaFinanceira() throws Exception {
        if (comissaoMetaFinanceira == null) {
            comissaoMetaFinanceira = new ComissaoMetaFinanceira();
        }
        comissaoMetaFinanceira.prepararConexao();
        return comissaoMetaFinanceira;
    }

    public TamanhoArmarioInterfaceFacade getTamanhoArmario() throws Exception {
        if (TamanhoArmario == null) {
            TamanhoArmario = new TamanhoArmario();
        }
        TamanhoArmario.prepararConexao();
        return TamanhoArmario;
    }

    public ConviteInterfaceFacade getConvite() throws Exception {
        if (convite == null) {
            convite = new Convite();
        }
        convite.prepararConexao();
        return convite;
    }

    public ArmarioInterfaceFacade getArmario() throws Exception {
        if (armario == null) {
            armario = new Armario();
        }
        armario.prepararConexao();
        return armario;
    }

    public CadastroDinamicoInterfaceFacade getCadastroDinamico() throws Exception {
        if (cadastroDinamico == null) {
            cadastroDinamico = new CadastroDinamico();
        }
        cadastroDinamico.prepararConexao();
        return cadastroDinamico;
    }

    public IndiceFinanceiroReajustePrecoInterfaceFacade getIndiceFinanceiroReajustePreco()throws Exception{
        if (indiceFinanceiroReajustePreco == null){
            indiceFinanceiroReajustePreco = new IndiceFinanceiroReajustePreco();
        }
        indiceFinanceiroReajustePreco.prepararConexao();
        return indiceFinanceiroReajustePreco;
    }

    public ReajusteContratoInterfaceFacade getReajusteContrato() throws Exception{
        if (reajusteContrato == null){
            reajusteContrato = new ReajusteContrato();
        }
        reajusteContrato.prepararConexao();
        return reajusteContrato;
    }

    public CadastroDinamicoItemInterfaceFacade getCadastroDinamicoItem() throws Exception {
        if (cadastroDinamicoItem == null) {
            cadastroDinamicoItem = new CadastroDinamicoItem();
        }
        cadastroDinamicoItem.prepararConexao();
        return cadastroDinamicoItem;
    }

    public ConfiguracaoProdutoEmpresaInterfaceFacade getConfiguracaoProdutoEmpresa() throws Exception {
        if (configuracaoProdutoEmpresa == null) {
            configuracaoProdutoEmpresa = new ConfiguracaoProdutoEmpresa();
        }
        configuracaoProdutoEmpresa.prepararConexao();
        return configuracaoProdutoEmpresa;
    }


    public ColaboradorIndisponivelCrmInterfaceFacade getColaboradorIndisponivelCrm() throws Exception {
        if (colaboradorIndisponivelCrm == null){
            colaboradorIndisponivelCrm = new ColaboradorIndisponivelCrm();
        }
        colaboradorIndisponivelCrm.prepararConexao();
        return colaboradorIndisponivelCrm;
    }

    public PlanoDuracaoCreditoTreinoInterfaceFacade getPlanoDuracaoCreditoTreino() throws Exception{
        if (planoDuracaoCreditoTreino == null){
            planoDuracaoCreditoTreino = new PlanoDuracaoCreditoTreino();
        }
        planoDuracaoCreditoTreino.prepararConexao();
        return planoDuracaoCreditoTreino;
    }

    public ContratoDuracaoCreditoTreinoInterfaceFacade getContratoDuracaoCreditoTreino() throws Exception{
        if (contratoDuracaoCreditoTreino == null){
            contratoDuracaoCreditoTreino = new ContratoDuracaoCreditoTreino();
        }
        contratoDuracaoCreditoTreino.prepararConexao();
        return contratoDuracaoCreditoTreino;
    }

    public ControleCreditoTreinoInterfaceFacade getControleCreditoTreino() throws Exception{
        if (controleCreditoTreino == null){
            controleCreditoTreino = new ControleCreditoTreino();
        }
        controleCreditoTreino.prepararConexao();
        return controleCreditoTreino;
    }

    public TipoConviteAulaExperimentalInterfaceFacade getTipoConviteAulaExperimental() throws Exception{
        if (tipoConviteAulaExperimental == null){
            tipoConviteAulaExperimental = new TipoConviteAulaExperimental();
        }
        tipoConviteAulaExperimental.prepararConexao();
        return tipoConviteAulaExperimental;
    }

    public ConviteAulaExperimentalInterfaceFacade getConviteAulaExperimental() throws Exception{
        if (conviteAulaExperimental == null){
            conviteAulaExperimental = new ConviteAulaExperimental();
        }
        conviteAulaExperimental.prepararConexao();
        return conviteAulaExperimental;
    }

    public TipoConviteAulaExperimentalModalidadeInterfaceFacade getTipoConviteAulaExperimentalModalidade() throws Exception{
        if (tipoConviteAulaExperimentalModalidade == null){
            tipoConviteAulaExperimentalModalidade = new TipoConviteAulaExperimentalModalidade();
        }
        tipoConviteAulaExperimentalModalidade.prepararConexao();
        return tipoConviteAulaExperimentalModalidade;
    }

    public TipoConviteAulaExperimentalModalidadeHorarioInterfaceFacade getTipoConviteAulaExperimentalModalidadeHorario() throws Exception{
        if (tipoConviteAulaExperimentalModalidadeHorario == null){
            tipoConviteAulaExperimentalModalidadeHorario = new TipoConviteAulaExperimentalModalidadeHorario();
        }
        tipoConviteAulaExperimentalModalidadeHorario.prepararConexao();
        return tipoConviteAulaExperimentalModalidadeHorario;
    }

    public ConviteAulaExperimentalServiceInterface getConviteAulaExperimentalService() throws Exception{
        if (conviteAulaExperimentalService == null){
            conviteAulaExperimentalService = new ConviteAulaExperimentalService();
        }
        conviteAulaExperimentalService.prepararConexao();
        return conviteAulaExperimentalService;
    }


    public AulaDesmarcadaInterfaceFacade getAulaDesmarcada() throws Exception{
        if (aulaDesmarcada == null){
            aulaDesmarcada = new AulaDesmarcada();
        }
        aulaDesmarcada.prepararConexao();
        return aulaDesmarcada;
    }

    public MalaDiretaCRMExtraClienteInterfaceFacade getMalaDiretaCRMExtraCliente() throws Exception {
        if (malaDiretaCRMExtraCliente == null) {
            malaDiretaCRMExtraCliente = new MalaDiretaCRMExtraCliente();
        }
        malaDiretaCRMExtraCliente.prepararConexao();
        return malaDiretaCRMExtraCliente;
    }

    public MalaDiretaCRMExtraColaboradorInterfaceFacade getMalaDiretaCRMExtraColaborador() throws Exception {
        if (malaDiretaCRMExtraColaborador == null) {
            malaDiretaCRMExtraColaborador = new MalaDiretaCRMExtraColaborador();
        }
        malaDiretaCRMExtraColaborador.prepararConexao();
        return malaDiretaCRMExtraColaborador;
    }

    public ConfiguracaoBIInterfaceFacade getConfiguracaoBI() throws Exception {
        if (configuracaoBi == null) {
            configuracaoBi = new ConfiguracaoBI();
        }
        configuracaoBi.prepararConexao();
        return configuracaoBi;
    }

    public SorteioInterfaceFacade getSorteio() throws Exception {
        if (sorteio == null) {
            sorteio = new Sorteio();
        }
        sorteio.prepararConexao();
        return sorteio;
    }

    public ConfiguracaoSorteioInterfaceFacade getConfiguracaoSorteio() throws Exception {
        if (configuracaoSorteio == null) {
            configuracaoSorteio = new ConfiguracaoSorteio();
        }
        configuracaoSorteio.prepararConexao();
        return configuracaoSorteio;
    }

    public ConfiguracaoSistemaUsuarioInterfaceFacade getOrdemItemBIInterfaceFacade() throws Exception {
        if (ordemItemBI == null) {
            ordemItemBI = new ConfiguracaoSistemaUsuario();
        }
        ordemItemBI.prepararConexao();
        return ordemItemBI;
    }

    public ConfiguracaoSistemaUsuarioInterfaceFacade getConfiguracaoSistemaUsuario() throws Exception {
        if (ordemItemBI == null) {
            ordemItemBI = new ConfiguracaoSistemaUsuario();
        }
        ordemItemBI.prepararConexao();
        return ordemItemBI;
    }

    public DepartamentoInterfaceFacade getDepartamento() throws Exception {
        if (this.departamento == null) {
            this.departamento = new Departamento();
        }
        departamento.prepararConexao();
        return departamento;
    }

    public MovParcelaCupomDescontoInterfaceFacade getMovParcelaCupomDesconto()throws Exception{
        if (this.movParcelaCupomDesconto == null) {
            this.movParcelaCupomDesconto = new MovParcelaCupomDesconto();
        }
        movParcelaCupomDesconto.prepararConexao();
        return movParcelaCupomDesconto;
    }

    public LinhaTempoContratoInterfaceFacade getLinhaTempoContrato() throws Exception {
        if (this.linhaTempoContrato == null) {
            this.linhaTempoContrato = new LinhaTempoContrato();
        }
        linhaTempoContrato.prepararConexao();
        return linhaTempoContrato;
    }

    public MemCachedManager getMemCachedManager()throws Exception{
        /*if (this.memCachedManager == null){
            this.memCachedManager = new MemCachedManager();
        }
        return memCachedManager;
        */
        return MemCachedManager.getInstance();
    }
    public Wiki getWikiManager() throws Exception {
        if (wikiManager == null) {
            wikiManager = new Wiki();
        }
        return wikiManager;
    }

    public ClienteTitularDependenteInterfaceFacade getClienteTitularDependente()throws Exception{
        if (clienteTitularDependente == null){
            clienteTitularDependente = new ClienteTitularDependente();
        }
        clienteTitularDependente.prepararConexao();
        return clienteTitularDependente;
    }

    public SinalizadorSistemaInterfaceFacade getSinalizadorSistema() throws Exception {
        if (sinalizadorSistema == null) {
            sinalizadorSistema = new SinalizadorSistema();
        }
        sinalizadorSistema.prepararConexao();
        return sinalizadorSistema;
    }

    public CentroCustoInterfaceFacade getCentroCusto()throws Exception {
        if (centroCusto == null) {
            centroCusto = new CentroCusto();
        }
        centroCusto.prepararConexao();

        return centroCusto;
    }

    public NotificacaoUsuarioInterfaceFacade getNotificacaoUsuario() throws Exception {
        if (notificacaoUsuario == null) {
            notificacaoUsuario = new NotificacaoUsuario();
        }
        notificacaoUsuario.prepararConexao();
        return notificacaoUsuario;
    }

    public PlanoContaInterfaceFacade getPlanoConta()throws Exception{
        return getFinanceiro().getPlanoConta();
    }

    public TipoDocumentoInterfaceFacade getTipoDocumento() throws Exception {
        return getFinanceiro().getTipoDocumento();
    }

    public TipoContaInterfaceFacade getTipoConta() throws Exception {
        return getFinanceiro().getTipoConta();
    }

    public ContaInterfaceFacade getConta() throws Exception {
        return getFinanceiro().getConta();
    }

    public RateioIntegracaoInterfaceFacade getRateioIntegracao()throws Exception{
        return getFinanceiro().getRateioIntegracao();
    }
    public FormaContato getFormaContato() throws Exception {
        return getCentralEventosFacade().getFormaContato();
    }

    public DashboardService getDashboardService() {
        if (dashboardService == null){
            dashboardService = new DashboardService();
        }
        return dashboardService;
    }

    public BrindeInterfaceFacade getBrinde() throws Exception {
        if (this.brinde == null) {
            brinde = new Brinde();
        }
        brinde.prepararConexao();
        return brinde;
    }

    public BrindePlanoInterfaceFacade getBrindePlano() throws Exception {
        if(this.brindePlano == null)
            brindePlano = new BrindePlano();

        brindePlano.prepararConexao();
        return brindePlano;
    }

    public HistoricoPontosInterfaceFacade getHistoricoPontos() throws Exception {
        if (this.historicoPontos == null) {
            historicoPontos = new HistoricoPontos();
        }
        historicoPontos.prepararConexao();
        return historicoPontos;
    }

    public LeadInterfaceFacade getLead() throws Exception {
        if (lead == null) {
            lead = new Lead();
        }
        lead.prepararConexao();
        return lead;
    }

    public ConversaoLeadInterfaceFacade getConversaoLead() throws Exception {
        if (conversaoLead == null) {
            conversaoLead = new ConversaoLead();
        }
        conversaoLead.prepararConexao();
        return conversaoLead;
    }

    public NFSeEmitidaHistorico getNfSeEmitidaHistorico() throws Exception {
        if (nfSeEmitidaHistorico == null) {
            nfSeEmitidaHistorico = new NFSeEmitidaHistorico();
        }
        nfSeEmitidaHistorico.prepararConexao();
        return nfSeEmitidaHistorico;
    }

    public CampanhaDuracaoInterfaceFacade getCampanhaDuracao() throws Exception {
        if (campanhaDuracao == null) {
            campanhaDuracao = new CampanhaDuracao();
        }
        campanhaDuracao.prepararConexao();
        return campanhaDuracao;
    }

    public ItemCampanhaInterfaceFacade getItemCampanha() throws Exception {
        if (itemCampanha == null) {
            itemCampanha = new ItemCampanha();
        }
        itemCampanha.prepararConexao();
        return itemCampanha;
    }

    public DescontoEmpresa getDescontoEmpresa() throws Exception {
        if(descontoEmpresa == null){
            descontoEmpresa = new DescontoEmpresa();
        }

        descontoEmpresa.prepararConexao();
        return descontoEmpresa;
    }

    public ConvenioCobrancaEmpresaInterfaceFacade getConvenioCobrancaEmpresa() throws Exception {
        if (convenioCobrancaEmpresa == null) {
            convenioCobrancaEmpresa = new ConvenioCobrancaEmpresa();
        }
        convenioCobrancaEmpresa.prepararConexao();
        return convenioCobrancaEmpresa;
    }

    public ConfirmacaoEmailCompraInterfaceFacede getConfirmacaoEmailCompra() throws Exception{
        if(confirmacaoEmailCompra == null){
            confirmacaoEmailCompra = new ConfirmacaoEmailCompra();
        }
        confirmacaoEmailCompra.prepararConexao();
        return confirmacaoEmailCompra;
    }

    public PlanoRecorrenciaParcela getPlanorecorrenciaParcela() throws Exception {
        if( planorecorrenciaParcela == null){
            planorecorrenciaParcela = new PlanoRecorrenciaParcela();
        }
        planorecorrenciaParcela.prepararConexao();
        return planorecorrenciaParcela;
    }

    public PlanoTipo getPlanoTipo() throws Exception {
        if( planoTipo == null){
            planoTipo = new PlanoTipo();
        }
        planoTipo.prepararConexao();
        return planoTipo;
    }

    public ConfiguracaoMovimentacaoAutomaticaInterfaceFacade getMovimentacaoAutomatica() throws Exception {
        if (movimentacaoAutomatica == null) {
            movimentacaoAutomatica = new ConfiguracaoMovimentacaoAutomatica();
        }
        movimentacaoAutomatica.prepararConexao();
        return movimentacaoAutomatica;
    }

    public ResultadoServicosInterfaceFacade getResultadoServicos() throws Exception {
        if (resultadoServicos == null) {
            resultadoServicos = new ResultadoServicos();
        }
        resultadoServicos.prepararConexao();
        return resultadoServicos;
    }

    public ParceiroFidelidadeInterfaceFacade getParceiroFidelidade() throws Exception {
        if (parceiroFidelidade == null) {
            parceiroFidelidade = new ParceiroFidelidade();
        }
        parceiroFidelidade.prepararConexao();
        return parceiroFidelidade;
    }

    public TabelaParceiroFidelidadeInterfaceFacade getTabelaParceiroFidelidade() throws Exception {
        if (tabelaParceiroFidelidade == null) {
            tabelaParceiroFidelidade = new TabelaParceiroFidelidade();
        }
        tabelaParceiroFidelidade.prepararConexao();
        return tabelaParceiroFidelidade;
    }

    public TabelaParceiroFidelidadeItemInterfaceFacade getTabelaParceiroFidelidadeItem() throws Exception {
        if (tabelaParceiroFidelidadeItem == null) {
            tabelaParceiroFidelidadeItem = new TabelaParceiroFidelidadeItem();
        }
        tabelaParceiroFidelidadeItem.prepararConexao();
        return tabelaParceiroFidelidadeItem;
    }

    public ParceiroFidelidadePontosInterfaceFacade getParceiroFidelidadePontos() throws Exception {
        if (parceiroFidelidadePontos == null) {
            parceiroFidelidadePontos = new ParceiroFidelidadePontos();
        }
        parceiroFidelidadePontos.prepararConexao();
        return parceiroFidelidadePontos;
    }

    public GympassInterfaceFacade getGympass() throws Exception {
        if (gympass == null) {
            gympass = new Gympass();
        }
        gympass.prepararConexao();
        return gympass;
    }

    public GympassDiaInterfaceFacade getGympassDia() throws Exception {
        if (gympassDia == null) {
            gympassDia = new GympassDia();
        }
        gympassDia.prepararConexao();
        return gympassDia;
    }

    public TotalpassInterfaceFacade getTotalPass() throws Exception {
        if (totalpass == null) {
            totalpass = new ConfigTotalPass();
        }
        totalpass.prepararConexao();
        return totalpass;
    }

    public ProdutoParceiroFidelidadeInterfaceFacade getProdutoParceiroFidelidade() throws Exception {
        if (produtoParceiroFidelidade == null) {
            produtoParceiroFidelidade = new ProdutoParceiroFidelidade();
        }
        produtoParceiroFidelidade.prepararConexao();
        return produtoParceiroFidelidade;
    }

    public CreditoDCCService getCreditoDCC() throws Exception {
        if (creditoDCC == null) {
            creditoDCC = new CreditoDCCService();
        }
        creditoDCC.prepararConexao();
        return creditoDCC;
    }

    public LogCobrancaPactoInterfaceFacade getLogCobrancaPacto() throws Exception {
        if (logCobrancaPacto == null) {
            logCobrancaPacto = new LogCobrancaPacto();
        }
        logCobrancaPacto.prepararConexao();
        return logCobrancaPacto;
    }

    public ContratoModalidadeCreditoInterfaceFacade getContratoModalidadeCredito() throws Exception {
        if (contratoModalidadeCredito == null) {
            contratoModalidadeCredito = new ContratoModalidadeCredito();
        }
        contratoModalidadeCredito.prepararConexao();
        return contratoModalidadeCredito;
    }

    public VendasConfigInterfaceFacade getVendasOnline() throws Exception {
        if (vendas == null) {
            vendas = new VendasConfig();
        }
        vendas.prepararConexao();
        return vendas;
    }

    public NotaFiscalInterfaceFacade getNotaFiscal() throws Exception {
        if (notaFiscal == null) {
            notaFiscal = new NotaFiscal();
        }
        notaFiscal.prepararConexao();
        return notaFiscal;
    }

    public ConfiguracaoNotaFiscalInterfaceFacade getConfiguracaoNotaFiscal() throws Exception {
        if (configuracaoNotaFiscal == null) {
            configuracaoNotaFiscal = new ConfiguracaoNotaFiscal();
        }
        configuracaoNotaFiscal.prepararConexao();
        return configuracaoNotaFiscal;
    }

    public ConfiguracaoNotaFiscalAmbienteInterfaceFacade getConfiguracaoNotaFiscalAmbiente() throws Exception {
        if (configuracaoNotaFiscalAmbiente == null) {
            configuracaoNotaFiscalAmbiente = new ConfiguracaoNotaFiscalAmbiente();
        }
        configuracaoNotaFiscalAmbiente.prepararConexao();
        return configuracaoNotaFiscalAmbiente;
    }

    public NotaFiscalOperacaoInterfaceFacade getNotaFiscalOperacao() throws Exception {
        if (notaFiscalOperacao == null) {
            notaFiscalOperacao = new NotaFiscalOperacao();
        }
        notaFiscalOperacao.prepararConexao();
        return notaFiscalOperacao;
    }

    public NotaFiscalHistoricoInterfaceFacade getNotaFiscalHistorico() throws Exception {
        if (notaFiscalHistorico == null) {
            notaFiscalHistorico = new NotaFiscalHistorico();
        }
        notaFiscalHistorico.prepararConexao();
        return notaFiscalHistorico;
    }

    public NotaFiscalFamiliaInterfaceFacade getNotaFiscalFamilia() throws Exception {
        if (notaFiscalFamilia == null) {
            notaFiscalFamilia = new NotaFiscalFamilia();
        }
        notaFiscalFamilia.prepararConexao();
        return notaFiscalFamilia;
    }

    public BoletoPJBankInterfaceFacade getBoletoPJBankInterfaceFacade() throws Exception {
        if(boletoPJBankInterfaceFacade == null) {
            boletoPJBankInterfaceFacade = new BoletoPJBank();
        }
        boletoPJBankInterfaceFacade.prepararConexao();
        return boletoPJBankInterfaceFacade;
    }

    public LogPJBankInterfaceFacade getLogPJBankInterfaceFacade() throws Exception {
        if(logPJBankInterfaceFacade == null) {
            logPJBankInterfaceFacade = new LogPJBank();
        }
        logPJBankInterfaceFacade.prepararConexao();
        return logPJBankInterfaceFacade;
    }

    public OperacaoColetivaInterfaceFacade getOperacaoColetiva() throws Exception {
        if (operacaoColetiva == null) {
            operacaoColetiva  = new OperacaoColetiva();
        }
        operacaoColetiva.prepararConexao();
        return  operacaoColetiva;
    }

    public ProcessoImportacaoInterfaceFacade getProcessoImportacao() throws Exception {
        if (processoImportacao == null) {
            processoImportacao = new ProcessoImportacao();
        }
        processoImportacao.prepararConexao();
        return processoImportacao;
    }

    public ProcessoImportacaoLogInterfaceFacade getProcessoImportacaoLog() throws Exception {
        if (processoImportacaoLog == null) {
            processoImportacaoLog = new ProcessoImportacaoLog();
        }
        processoImportacaoLog.prepararConexao();
        return processoImportacaoLog;
    }

    public ProcessoImportacaoItemInterfaceFacade getProcessoImportacaoItem() throws Exception {
        if (processoImportacaoItem == null) {
            processoImportacaoItem = new ProcessoImportacaoItem();
        }
        processoImportacaoItem.prepararConexao();
        return processoImportacaoItem;
    }

    public PlanoAnuidadeParcela getPlanoAnuidadeParcela() throws Exception {
        if( planoAnuidadeParcela == null){
            planoAnuidadeParcela = new PlanoAnuidadeParcela();
        }
        planoAnuidadeParcela.prepararConexao();
        return planoAnuidadeParcela;
    }

    public ConvenioCobrancaRateioInterfaceFacade getConvenioCobrancaRateio() throws Exception {
        if (convenioCobrancaRateio == null) {
            convenioCobrancaRateio = new ConvenioCobrancaRateio();
        }
        convenioCobrancaRateio.prepararConexao();
        return convenioCobrancaRateio;
    }

    public ConvenioCobrancaRateioItemInterfaceFacade getConvenioCobrancaRateioItem() throws Exception {
        if (convenioCobrancaRateioItem == null) {
            convenioCobrancaRateioItem = new ConvenioCobrancaRateioItem();
        }
        convenioCobrancaRateioItem.prepararConexao();
        return convenioCobrancaRateioItem;
    }

    public ConvenioCobrancaArquivoInterfaceFacade getConvenioCobrancaArquivo() throws Exception {
        if (convenioCobrancaArquivo == null) {
            convenioCobrancaArquivo = new ConvenioCobrancaArquivo();
        }
        convenioCobrancaArquivo.prepararConexao();
        return convenioCobrancaArquivo;
    }

    public MovParcelaResultadoCobranca getMovParcelaResultadoCobranca() throws Exception {
        if (movParcelaResultadoCobranca == null) {
            movParcelaResultadoCobranca = new MovParcelaResultadoCobranca();
        }
        movParcelaResultadoCobranca.prepararConexao();
        return movParcelaResultadoCobranca;
    }

    public QuarentenaInterfaceFacade getQuarentena() throws Exception {
        if( quarentena == null){
            quarentena = new Quarentena();
        }
        quarentena.prepararConexao();
        return quarentena;
    }

    public VendasOnlineConvenioInterfaceFacade getVendasOnlineConvenio() throws Exception {
        if (vendasOnlineConvenio == null) {
            vendasOnlineConvenio = new VendasOnlineConvenio();
        }
        vendasOnlineConvenio.prepararConexao();
        return vendasOnlineConvenio;
    }

    public VendasOnlineConvenioTentativaInterfaceFacade getVendasOnlineConvenioTentativa() throws Exception {
        if (vendasOnlineConvenioTentativa == null) {
            vendasOnlineConvenioTentativa = new VendasOnlineConvenioTentativa();
        }
        vendasOnlineConvenioTentativa.prepararConexao();
        return vendasOnlineConvenioTentativa;
    }

    public PlanoPersonalAssinaturaDigitalInterfaceFacade getPlanoPersonalAssinaturaDigital() throws Exception {
        if (planoPersonalAssinaturaDigital == null){
            planoPersonalAssinaturaDigital = new PlanoPersonalAssinaturaDigital();
        }
        planoPersonalAssinaturaDigital.prepararConexao();
        return planoPersonalAssinaturaDigital;
    }

    public PlanoPersonalTextoPadraoInterfaceFacade getPlanoPersonalTextoPadrao() throws Exception {
        if (planoPersonalTextoPadrao == null){
            planoPersonalTextoPadrao = new PlanoPersonalTextoPadrao();
        }
        planoPersonalTextoPadrao.prepararConexao();
        return planoPersonalTextoPadrao;
    }

    public MovContaRateioInterfaceFacade getMovContaRateio() throws Exception{
        if (movContaRateio == null){
            movContaRateio = new MovContaRateio();
        }
        movContaRateio.prepararConexao();
        return movContaRateio;
    }

    public MovContaInterfaceFacade getMovConta() throws Exception{
        if (movConta == null){
            movConta = new MovConta();
        }
        movConta.prepararConexao();
        return movConta;
    }

    public CaixaMovContaInterfaceFacade getCaixaMovConta() throws Exception{
        if (caixaMovConta == null){
            caixaMovConta = new CaixaMovConta();
        }
        caixaMovConta.prepararConexao();
        return caixaMovConta;
    }

    public CaixaInterfaceFacade getCaixa() throws Exception{
        if (caixa == null){
            caixa = new Caixa();
        }
        caixa.prepararConexao();
        return caixa;
    }

    public PixInterfaceFacade getPix() throws Exception {
        if (pix == null){
            pix = new Pix();
        }
        pix.prepararConexao();
        return pix;
    }

    public PixMovParcelaInterfaceFacade getPixMovParcela() throws Exception {
        if (pixMovParcela == null){
            pixMovParcela = new PixMovParcela();
        }
        pixMovParcela.prepararConexao();
        return pixMovParcela;
    }

    public BiMSService getBiMSService() throws Exception {
        if (biMSService == null){
            biMSService = new BiMSServiceImpl();
        }
        return biMSService;
    }

    public MgbService getMgbService() throws Exception {
        if (mgbService == null){
            mgbService = new MgbServiceImpl();
        }
        mgbService.prepararConexao();
        return mgbService;
    }

    public PixService getPixService() throws Exception {
        if (pixService == null){
            pixService = new PixService();
        }
        pixService.prepararConexao();
        return pixService;
    }

    public ERedeServiceConciliacao getERedeServiceConciliacao() throws Exception {
        if (eRedeServiceConciliacao == null){
            eRedeServiceConciliacao = new ERedeServiceConciliacao();
        }
        eRedeServiceConciliacao.prepararConexao();
        return eRedeServiceConciliacao;
    }

    public PlanoCategoria getPlanoCategoria() throws Exception {
        if(planoCategoria == null){
            planoCategoria = new PlanoCategoria();
        }
        planoCategoria.prepararConexao();
        return planoCategoria;
    }

    public BoletoInterfaceFacade getBoleto() throws Exception {
        if(boletoInterfaceFacade == null) {
            boletoInterfaceFacade = new Boleto();
        }
        boletoInterfaceFacade.prepararConexao();
        return boletoInterfaceFacade;
    }

    public PactoPayConfigInterfaceFacade getPactoPayConfig() throws Exception {
        if (pactoPayConfig == null) {
            pactoPayConfig = new PactoPayConfig();
        }
        pactoPayConfig.prepararConexao();
        return pactoPayConfig;
    }

    public TokenVendasOnline getTokenVendasOnline() throws Exception {
        if (tokenVendasOnline == null) {
            tokenVendasOnline = new TokenVendasOnline();
        }
        tokenVendasOnline.prepararConexao();
        return tokenVendasOnline;
    }

    public ColaboradorDocumentoRh getColaboradorDocumentoRh()throws Exception{
        if (colaboradorDocumentoRh == null) {
            colaboradorDocumentoRh = new ColaboradorDocumentoRh();
        }
        colaboradorDocumentoRh.prepararConexao();
        return colaboradorDocumentoRh;
    }

    public ColaboradorInfoRh getColaboradorInfoRh()throws Exception {
        if (colaboradorInfoRh == null) {
            colaboradorInfoRh = new ColaboradorInfoRh();
        }
        colaboradorInfoRh.prepararConexao();

        return colaboradorInfoRh;
    }

    public IntegracaoSesiInterfaceFacade getIntegracaoSesi() throws Exception {
        if (integracaoSesi == null) {
            integracaoSesi = new IntegracaoSesi();
        }
        integracaoSesi.prepararConexao();
        return integracaoSesi;
    }

    public PinPadPedido getPinPadPedido() throws Exception {
        if (pinPadPedido == null) {
            pinPadPedido = new PinPadPedido();
        }
        pinPadPedido.prepararConexao();
        return pinPadPedido;
    }

    public VendasOnlineCampanha getVendasOnlineCampanha() throws Exception {
        if (vendasOnlineCampanha == null) {
            vendasOnlineCampanha = new VendasOnlineCampanha();
        }
        vendasOnlineCampanha.prepararConexao();
        return vendasOnlineCampanha;
    }

    public AsaasEmpresaInterfaceFacade getAsaasEmpresa() throws Exception {
        if (asaasEmpresa == null) {
            asaasEmpresa = new AsaasEmpresa();
        }
        asaasEmpresa.prepararConexao();
        return asaasEmpresa;
    }

    public PluggyService getPluggyService() throws Exception {
        if (pluggyService == null){
            pluggyService = new PluggyService();
        }
        pluggyService.prepararConexao();
        return pluggyService;
    }

    public IntegracaoKobanaService getIntegracaoKobanaService(IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        if (integracaoKobanaService == null){
            integracaoKobanaService = new IntegracaoKobanaService(integracaoKobanaVO);
        }
        integracaoKobanaService.prepararConexao();
        return integracaoKobanaService;
    }

    public IntegracaoKobanaInterfaceFacade getIntegracaoKobana() throws Exception {
        if (integracaoKobana == null){
            integracaoKobana = new IntegracaoKobana();
        }
        integracaoKobana.prepararConexao();
        return integracaoKobana;
    }

    public LoteKobanaInterfaceFacade getLoteKobana() throws Exception {
        if (loteKobana == null){
            loteKobana = new LoteKobana();
        }
        loteKobana.prepararConexao();
        return loteKobana;
    }

    public LoteKobanaItemInterfaceFacade getLoteKobanaItem() throws Exception {
        if (loteKobanaItem == null){
            loteKobanaItem = new LoteKobanaItem();
        }
        loteKobanaItem.prepararConexao();
        return loteKobanaItem;
    }

    public PluggyItemInterfaceFacade getPluggyItem() throws Exception{
        if (pluggyItem == null){
            pluggyItem = new PluggyItem();
        }
        pluggyItem.prepararConexao();
        return pluggyItem;
    }

    public PluggyAccountBloqueio getPluggyAccountBloqueio() throws Exception{
        if (pluggyAccountBloqueio == null){
            pluggyAccountBloqueio = new PluggyAccountBloqueio();
        }
        pluggyAccountBloqueio.prepararConexao();
        return pluggyAccountBloqueio;
    }

    public MovContaTransactionPluggyInterfaceFacade getMovContaTransactionPluggy() throws Exception{
        if (movContaTransactionPluggy == null){
            movContaTransactionPluggy = new MovContaTransactionPluggy();
        }
        movContaTransactionPluggy.prepararConexao();
        return movContaTransactionPluggy;
    }

    public PinPadPedidoMovParcela getPinPadPedidoMovParcela() throws Exception {
        if (pinPadPedidoMovParcela == null) {
            pinPadPedidoMovParcela = new PinPadPedidoMovParcela();
        }
        pinPadPedidoMovParcela.prepararConexao();
        return pinPadPedidoMovParcela;
    }

    public TokenOperacao getTokenOperacao() throws Exception {
        if (tokenOperacao == null) {
            tokenOperacao = new TokenOperacao();
        }
        tokenOperacao.prepararConexao();
        return tokenOperacao;
    }

    public InfoNovoMerchantPagoLivre getInfoNovoMerchantPagoLivre() throws Exception {
        if (infoNovoMerchantPagoLivre == null) {
            infoNovoMerchantPagoLivre = new InfoNovoMerchantPagoLivre();
        }
        infoNovoMerchantPagoLivre.prepararConexao();
        return infoNovoMerchantPagoLivre;
    }

    public SolicitacaoInterfaceFacade getSolicitacao() throws Exception {
        if(solicitacao == null){
            solicitacao = new Solicitacao();
        }
        solicitacao.prepararConexao();
        return solicitacao;
    }

    public PactoPayComunicacao getPactoPayComunicacao() throws Exception {
        if (pactoPayComunicacao == null) {
            pactoPayComunicacao = new PactoPayComunicacao();
        }
        pactoPayComunicacao.prepararConexao();
        return pactoPayComunicacao;
    }

    public OptinInterfaceFacade getOptin() throws Exception {
        if (optin == null) {
            optin = new Optin();
        }
        optin.prepararConexao();
        return optin;
    }

    public LocalImpressaoInterfaceFacade getLocalImpressao() throws Exception {
        if (this.localImpressao == null) {
            this.localImpressao = new LocalImpressao();
        }
        this.localImpressao.prepararConexao();
        return this.localImpressao;
    }

    public CarteirinhaClienteInterfaceFacade getCarteirinhaCliente() throws Exception {
        if (this.carteirinhaCliente == null) {
            this.carteirinhaCliente = new CarteirinhaCliente();
        }
        this.carteirinhaCliente.prepararConexao();
        return this.carteirinhaCliente;
    }

    public ProdutoMescladoInterfaceFacade getProdutoMesclado() throws Exception {
        if (this.produtoMesclado == null) {
            this.produtoMesclado = new ProdutoMesclado();
        }
        this.produtoMesclado.prepararConexao();
        return this.produtoMesclado;
    }



}
