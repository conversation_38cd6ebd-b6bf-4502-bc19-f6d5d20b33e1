package negocio.facade.jdbc.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LogoutControle;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.propriedades.PropsService;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpSession;
import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Responsável por implementar características comuns relativas a conexão com o
 * banco de dados.
 */
public abstract class SuperFacadeJDBC extends FacadeManager implements Serializable {

    //TODO. O acesso ao CRM e CE estão hard coded.
    protected static final Boolean cobreCRM = true;
    protected static final Boolean cobreCE = true;
    private static final String VALIDATION_SQL = "SELECT 1";
    private static final ThreadLocal<Long> LAST_CHECK = new ThreadLocal<>();
    /**
     * con Conexão com o BD. Única para todas as classes filhas desta classe.
     */
    protected transient Connection con = null;
    private String idEntidade = "Entidade";
    //adicionado para poder armazenar a sessão em um contexto não JSF
    private HttpSession session;
    private transient ThreadLocal<String> conexaoSendoPreparada = new ThreadLocal<>();
    public SuperFacadeJDBC(HttpSession session) throws Exception {
        this.setSession(session);
        this.inicializar();
    }

    public SuperFacadeJDBC(Connection conexao) throws Exception {
        this.con = conexao;
    }

    /**
     * @return the session
     */
    public HttpSession getSession() {
        return session;
    }

    /**
     * @param session the session to set
     */
    public void setSession(HttpSession session) {
        this.session = session;
    }

    /**
     * Creates a new instance of Funcionalidade
     */
    public SuperFacadeJDBC() {
    }

    public void incluir(String idEntidade) throws Exception {
        inicializar();
    }

    public void alterar(String idEntidade) throws Exception {
        inicializar();
    }

    public void excluir(String idEntidade) throws Exception {
        inicializar();
    }

    public void consultar(String idEntidade) throws Exception {
        inicializar();
    }

    public void emitirRelatorio(String idEntidade) throws Exception {
        inicializar();
    }

    public void verificarPermissaoUsuarioFuncionalidade(String funcionalidade) throws Exception {
        inicializar();
    }

    private Connection obterConnexaoAtiva() throws Exception {
        FacesContext fc = FacesContext.getCurrentInstance();
        Connection conexao = null;
        if (fc != null) {
            //Caso haja um faces context, ou seja, estejamos em contexto JSF
            conexao = Conexao.getFromSession();
//            if (conexao == null || conexao.isClosed()) {
//                if (fc.getExternalContext().getRequestCookieMap() != null) {
//                    Cookie c = (Cookie) fc.getExternalContext().getRequestCookieMap().get(SessionFilter.COOKIE_ZWKEY);
//                    if (c != null && c.getValue() != null && !c.getValue().isEmpty()) {
//                        conexao = Conexao.initSession(c.getValue());
//                    }
//                }
//            }

            if (conexao == null || conexao.isClosed()) {
                if (fc.getExternalContext().getSessionMap().get("key") != null) {
                    final String k = (String) fc.getExternalContext().getSessionMap().get("key");
                    if (k != null && !k.isEmpty()) {
                        conexao = Conexao.initSession(k);
                    }
                }
            }
        } else if (session != null) {
            //Caso o objeto sessão esteja setado
            conexao = Conexao.getFromSession(session);
        } else if (Conexao.getConexaoForJ2SE() != null) {
            //Serviço RobotRunner
            return Conexao.getConexaoForJ2SE();
        } else {
            //Em último caso a conexão será criada pelo CFG.db
            Conexao c = Conexao.getInstance();
            if (c.getUrlOAMD().trim().isEmpty()) {
                conexao = c.getConexao();
            }
        }
        return conexao;
    }

    public void finalizarConexaoAtivaSeHouver() throws Exception {
        if (con != null) {
            con.close();
            con = null;
        }
    }

    private void setarConnexaoAtiva(Connection conexao) throws Exception {
        Conexao.storeOnSession(conexao);
    }

    /**
     * Operação de inicialização padrão com o banco de dados. Inicializa o campo
     * ano para ser utilizado por operações de geração automática de chave
     * primária. Também, inicializa a conexão do BD caso a mesma esteja fechada
     * ou inativa.
     *
     * @exception Exception Caso haja problemas de conexão.
     */
    public void inicializar() throws Exception {
        LogoutControle control = null;
        try {
            if (JSFUtilities.isJSFContext()) {
                control = (LogoutControle) JSFUtilities.getManagedBeanValue(LogoutControle.class);
            } 
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
        if (control != null && control.isDeveEfetuarLogout()) {
            setCon(null);
            setarConnexaoAtiva(null);
        } else {
            if (getCon() == null) {
                Connection conex = obterConnexaoAtiva();
                if (conex == null) {
                    String msg = "DEBUG ====> SuperFacadeJDBC.inicializar(): "
                            + "Não foi possível obter a conexão de banco de dados ativa nesse momento.";

                    Exception e = new Exception(msg);
                    StringBuilder stackTrace = new StringBuilder();
                    stackTrace.append(e.getStackTrace()[0]);
                    for (int i = 1; i < e.getStackTrace().length; i++) {
                        stackTrace.append("\n").append(e.getStackTrace()[i]);
                    }

                    msg = String.format("%s STACK TRACE -> %s",
                            new Object[]{msg, stackTrace});

                    Logger.getLogger(this.getClass().getName()).log(
                            Level.SEVERE, msg);
                    e = new Exception(msg);
                    throw e;
                } else if (conex.isClosed()) {
                    Conexao conexao = Conexao.getInstance();
                    Connection connection = conexao.obterNovaConexaoBaseadaOutra(conex);
                    setCon(connection);
                    setarConnexaoAtiva(connection);
                } else {
                    setCon(conex);
                }
            }
            if (getCon().isClosed()) {
                Connection conex = obterConnexaoAtiva();
                if (conex == null) {
                    Conexao conexao = Conexao.getInstance();
                    Connection connection = conexao.obterNovaConexaoBaseadaOutra(getCon());
                    setCon(connection);
                    setarConnexaoAtiva(connection);
                } else if (conex.isClosed()) {
                    Conexao conexao = Conexao.getInstance();
                    Connection connection = conexao.obterNovaConexaoBaseadaOutra(conex);
                    setCon(connection);
                    setarConnexaoAtiva(connection);
                } else {
                    setCon(conex);
                }
            }
        }

    }

    public void finalizarConexaoBD() throws SQLException {
        if (getCon() != null) {
            getCon().close();
            setCon(null);
        }
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection aCon) {
        con = aCon;
    }

    public String getIdEntidade() {
        return idEntidade;
    }

    public void setIdEntidade(String idEntidade) {
        this.idEntidade = idEntidade;
    }

    public boolean isConexaoSendoPreparada() {
        return (con != null && conexaoSendoPreparada != null
                && conexaoSendoPreparada.get() != null
                && conexaoSendoPreparada.get().equals(con.toString()));
    }


    public void prepararConexao() throws Exception {
        try {
            if (isConexaoSendoPreparada()) {//conexão já está sendo preparada
                return;
            }
            inicializar();
            conexaoSendoPreparada.set(con.toString());

            Long lastCheck = LAST_CHECK.get();
            if (lastCheck == null || System.currentTimeMillis() > lastCheck) {
                try (Statement stm = con.createStatement()) {
                    stm.execute(VALIDATION_SQL);
                }
                LAST_CHECK.set(System.currentTimeMillis() + 10000);
            }

            if (PropsService.isTrue(PropsService.debugJDBC)){
                Uteis.logarDebug(String.format("%s:%s => autoCommit => %s", con.toString(), con.getMetaData().getURL(),
                        con.getAutoCommit()));
            }
        } catch (SQLException ex) {
            try {
                //
                System.out.println(Uteis.getDataComHora(new Date())
                        + " Erro na conexão (" + con.toString() + ") do PostegreSQL, "
                        + "vou tentar reiniciar a conexão do Banco de Dados."
                        + ex.getMessage());
                setarConnexaoAtiva(null);
                try{
                    if (getCon() != null){
                        getCon().close();
                    }
                }catch (Exception e){
                    System.out.println(" Erro ao fechar conexão que estava com erro: " + e.getMessage());
                }
                setCon(null);
                inicializar();
                setarConnexaoAtiva(con);
                System.out.println("    -> Nova conexão: " + con.toString() + " obtida com sucesso!");
            } catch (Exception ex1) {
                throw new Exception("Não foi possível se recuperar de uma perda de conexão: " + ex1.getMessage());
            }
        } finally {
            conexaoSendoPreparada.set("");
        }

    }

    public static ResultSet criarConsulta(final String sql, Connection con) throws SQLException, Exception {
        Statement stm = con.createStatement();
        return stm.executeQuery(sql);
    }

    public static PreparedStatement criarQuery(final String sql, Connection con) throws SQLException, Exception {
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        return sqlAlterar;
    }

    public static ResultSet criarConsultaRolavel(final String sql,  Connection con) throws SQLException, Exception {
        Statement stm = con.createStatement(
                ResultSet.TYPE_SCROLL_INSENSITIVE,
                ResultSet.CONCUR_READ_ONLY);
        return stm.executeQuery(sql);
    }

    public static boolean executarConsulta(final String sql, final Connection con) throws SQLException, Exception {
        try (Statement stm = con.createStatement()) {
            return stm.execute(sql);
        }
    }

    /**
     * Executa instrução ocultando a exceção, deve ser utilizado para execução
     * em massa. Cria uma conexão própria sem transação.
     *
     * @param sql
     * @return
     */
    public static boolean executarConsultaUpdate(final String sql, final Connection con) throws Exception {
        try (Statement stm = con.createStatement()) {
            return stm.execute(sql);
        }
    }

    /**
     * Executa instrução ocultando a exceção, deve ser utilizado para execução
     * em massa. Cria uma conexão própria sem transação.
     *
     * @param sql
     * @return Quantidade de registros afetados pelo Insert, Update ou Delete
     */
    public static int executarUpdate(final String sql, final Connection con) throws SQLException {
        return SuperFacadeJDBC.executarUpdate(sql, con, false);
    }

    public static int executarUpdate(final String sql, final Connection con, final boolean tratarErro) throws SQLException {
        try {
            try (Statement stm = con.createStatement()) {
                return stm.executeUpdate(sql);
            }
        } catch (Exception ex) {
            if (tratarErro) {
                //para instruções sql em lote não abortar entre uma instrução e outra.
                StringBuilder sb = new StringBuilder("WARNING em \"SuperFacadeJDBC.executarUpdate\" -> " + ex.getMessage());
                StackTraceElement[] arr = ex.getStackTrace();
                for (StackTraceElement stackTraceElement : arr) {
                    if (stackTraceElement.getClassName().contains("ExecutarProcessos")) {
                        sb.append("\n            ocorrido em \"").append(stackTraceElement.toString()).append("\"");
                    }
                }
                Uteis.logar(null, sb.toString());
            } else {
                throw ex;
            }
        }

        return 0;
    }


    /**
     * Executa instrução ocultando a exceção, deve ser utilizado para execução
     * em massa. Cria uma conexão própria sem transação.
     *
     * @param sql
     * @return
     */
    public static boolean executarUpdateExecutarProcessos(final String sql, final Connection con) {
        try {
            try (Statement stm = con.createStatement()) {
                return stm.execute(sql);
            }
        } catch (Exception ex) {
            //para instruções sql em lote não abortar entre uma instrução e outra.
            StringBuffer sb = new StringBuffer("WARNING em \"SuperFacadeJDBC.executarConsultaUpdate\" -> " + ex.getMessage());
            StackTraceElement[] arr = ex.getStackTrace();
            for (int i = 0; i < arr.length; i++) {
                StackTraceElement stackTraceElement = arr[i];
                if (stackTraceElement.getClassName().contains("ExecutarProcessos")) {
                    sb.append("\n            ocorrido em \"").append(stackTraceElement.toString()).append("\"");
                }

            }
            Uteis.logar(null, sb.toString());
        }

        return false;
    }

    public static PreparedStatement resolveFKNull(PreparedStatement ps, int indice, Integer valor) throws SQLException {
        if (valor == null || valor.intValue() == 0) {
            ps.setNull(indice, Types.NULL);
        } else {
            ps.setInt(indice, valor);
        }
        return ps;
    }

    public static <E extends Enum<E>> PreparedStatement resolveEnumNull(PreparedStatement ps,
            int indice, E elem, final String attName) throws SQLException {
        if (elem == null) {
            ps.setNull(indice, Types.NULL);
        } else {
            ps.setInt(indice, (Integer) UtilReflection.getValor(elem, attName));
        }
        return ps;
    }

    public static PreparedStatement resolveDateNull(PreparedStatement ps, int indice, Date valor) throws Exception {
        if (valor == null) {
            ps.setNull(indice, Types.NULL);
        } else {
            ps.setDate(indice, Uteis.getDataJDBC(valor));
        }
        return ps;
    }

    /**
     * Percorre todos o sequences existentes no banco de dados e atualiza-os
     * caso a tabela ainda exista.
     *
     * @throws Exception
     */
    public static void atualizarSequences(Connection c) throws Exception {
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta(
                    "select relname from pg_class where relname like('%_codigo_seq')", c);
            while (rs.next()) {
                String nomeSeq = rs.getString(1);
                String nomeTabela = nomeSeq.substring(0, nomeSeq.indexOf("_"));
                ResultSet rsMax;
                try {
                    rsMax = SuperFacadeJDBC.criarConsulta("select max(codigo) from "
                            + nomeTabela, c);
                } catch (Exception e) {
                    System.out.println(e.getMessage());
                    continue;
                }

                rsMax.next();
                int max = rsMax.getInt(1);
                if (max > 0) {
                    try {
                        SuperFacadeJDBC.executarConsulta("SELECT setval('" + nomeSeq + "', " + max + ", true)", c);
                        System.out.println("Atualizado " + nomeSeq + " para " + max);

                    } catch (Exception e) {
                        System.out.println(e.getMessage());
                    }

                }

            }
        } catch (Exception e) {
            throw e;
        }
    }

    /***
     * Utilizar esse método para obter o count de alguma tabela
     * Ex.:
     * SELECT COUNT(*) FROM TABELA
     * @param sql - Query count a ser executada
     * @param con - Conexão
     * @return valor Inteiro da primeira coluna
     * @throws SQLException
     * @throws Exception
     */
    public static Integer contar(String sql, Connection con) throws SQLException, Exception {
        ResultSet tabelaResultado = SuperFacadeJDBC.criarConsulta(sql, con);
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt(1);
        } else {
            return 0;
        }

    }

    public static boolean existe(final String sql, Connection con) throws Exception {
        ResultSet tabelaResultado = SuperFacadeJDBC.criarConsulta(
                "select exists(" + sql + ")", con);
        tabelaResultado.next();
        return tabelaResultado.getBoolean(1);
    }

    public static boolean verificarExistenciaDeVinculos(final String sql, Connection con) throws Exception {
        ResultSet tabelaResultado = SuperFacadeJDBC.criarConsulta(
                sql, con);
        tabelaResultado.next();
        return tabelaResultado.getInt("total") > 0;
    }

    public static PreparedStatement resolveIntegerNull(PreparedStatement ps, int indice, Integer valor) throws SQLException {
        if (UteisValidacao.emptyNumber(valor)) {
            ps.setNull(indice, Types.NULL);
        } else {
            ps.setInt(indice, valor);
        }
        return ps;
    }

    public static PreparedStatement resolveLongNull(PreparedStatement ps, int indice, Long valor) throws SQLException {
        if (UteisValidacao.emptyNumber(valor)) {
            ps.setNull(indice, Types.NULL);
        } else {
            ps.setLong(indice, valor);
        }
        return ps;
    }

    public static Object consultarValorColunaTop(final String sql, Connection con) throws Exception {
        ResultSet rs = criarConsulta(sql + " limit 1", con);
        if (rs.next()) {
            return rs.getObject(1);
        } else {
            return null;
        }
    }

    public static List consultarValorColunaList(final String sql, Connection con) throws Exception {
        ResultSet rs = criarConsulta(sql, con);
        List lista = new ArrayList();
        while (rs.next()) {
            lista.add(rs.getObject(1));
        }
        return lista;
    }

    public String getCampoStringTratandoExcessao(ResultSet rs, String campo) {
        try {
            return rs.getString(campo);
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getCampoIntegerTratandoExcessao(ResultSet rs, String campo) {
        try {
            return rs.getInt(campo);
        } catch (Exception e) {
            return null;
        }
    }

    public Date getCampoDateTratandoExcessao(ResultSet rs, String campo) {
        try {
            return rs.getDate(campo);
        } catch (Exception e) {
            return null;
        }
    }

    public static List<GenericoTO> consultarSimples(String sql, Connection con) throws Exception {
        ResultSet rs = criarConsulta(sql, con);
        List<GenericoTO> lista = new ArrayList<GenericoTO>();
        while (rs.next()) {
            lista.add(new GenericoTO(rs.getInt("codigo"), rs.getString("label")));
        }
        return lista;
    }

    public static PreparedStatement resolveDoubleNull(PreparedStatement ps, int indice, Double valor) throws SQLException {
        if (valor == null) {
            ps.setNull(indice, Types.NULL);
        } else {
            ps.setDouble(indice, valor);
        }
        return ps;
    }

    public static PreparedStatement resolveIntegerNullComZero(PreparedStatement ps, int indice, Integer valor) throws SQLException {
        if (valor == null) {
            ps.setNull(indice, Types.NULL);
        } else {
            ps.setInt(indice, valor);
        }
        return ps;
    }

    public static PreparedStatement resolveStringNull(PreparedStatement ps, int indice, String valor) throws SQLException {
        if (UteisValidacao.emptyString(valor)) {
            ps.setNull(indice, Types.NULL);
        } else {
            ps.setString(indice, valor);
        }
        return ps;
    }

    public static PreparedStatement resolveTimestampNull(PreparedStatement ps, int indice, Date valor) throws SQLException {
        if (valor == null) {
            ps.setNull(indice, Types.NULL);
        } else {
            ps.setTimestamp(indice, Uteis.getDataJDBCTimestamp(valor));
        }
        return ps;
    }


}
