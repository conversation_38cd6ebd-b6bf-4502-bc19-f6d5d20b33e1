package negocio.facade.jdbc.arquitetura;

import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.financeiro.lancamentos.LancamentosMovConta;
import negocio.interfaces.financeiro.*;
import servicos.impl.devolucaocheque.DevolucaoChequeServiceImpl;
import servicos.impl.recebiveis.GestaoRecebiveisServiceImpl;
import servicos.interfaces.devolucaocheque.DevolucaoChequeService;

import javax.servlet.http.HttpSession;

public class FinanceiroFacade extends SuperFacadeJDBC {

    private ContaInterfaceFacade conta;
    private TipoContaInterfaceFacade tipoConta;
    private TipoDocumentoInterfaceFacade tipoDocumento;
    private MovContaInterfaceFacade movConta;
    private LancamentosMovContaFacade lancamentosMovConta;
    private MovContaRateioInterfaceFacade movContaRateio;
    private PlanoContaInterfaceFacade planoConta;
    private CentroCustoInterfaceFacade centroCusto;
    private RateioIntegracaoInterfaceFacade rateioIntegracao;
    private AgendamentoFinanceiroInterfaceFacade agendamentoFinanceiro;
    private CaixaInterfaceFacade caixa;
    private BloqueioCaixaInterfaceFacade bloqueioCaixa;
    private CaixaContaInterfaceFacade caixaConta;
    private CaixaMovContaInterfaceFacade caixaMovConta;
    private HistoricoChequeInterfaceFacade historicoCheque;
    private DevolucaoChequeService servico;
    private GestaoRecebiveisServiceImpl recebiveis;
    private ContaContabilInterfaceFacade contaContabil;
    private ConfiguracaoFinanceiro configuracaoFinanceiro;
    private MovContaContabilInterfaceFacade movContaContabil;
    private HistoricoImportacaoContaInterfaceFacade historicoImportacao;

    public HistoricoImportacaoContaInterfaceFacade getHistoricoImportacao() throws Exception {
        if (this.historicoImportacao == null) {
            this.historicoImportacao = new HistoricoImportacaoConta();
        }
        historicoImportacao.prepararConexao();
        return historicoImportacao;
    }

    public void setHistoricoImportacao(HistoricoImportacaoContaInterfaceFacade historicoImportacao) {
        this.historicoImportacao = historicoImportacao;
    }

    public HistoricoCartaoInterfaceFacade getHistoricoCartao()throws Exception {
        if (this.historicoCartao == null) {
            this.historicoCartao = new HistoricoCartao();
        }
        historicoCartao.prepararConexao();
        return historicoCartao;
    }

    public void setHistoricoCartao(HistoricoCartaoInterfaceFacade historicoCartao) {
        this.historicoCartao = historicoCartao;
    }

    private HistoricoCartaoInterfaceFacade historicoCartao;
    private DFSinteticoDWInterfaceFacade dfSinteticoDW;
    private DRESinteticoDWInterfaceFacade dreSinteticoDW;

    public FinanceiroFacade() throws Exception {
        super();
        super.inicializar();
    }

    public FinanceiroFacade(HttpSession session) throws Exception {
        super(session);
        super.inicializar();
    }

    public CaixaInterfaceFacade getCaixa() throws Exception {
        if (this.caixa == null) {
            this.caixa = new Caixa();
        }
        caixa.prepararConexao();
        return caixa;
    }

    public BloqueioCaixaInterfaceFacade getBloqueioCaixa() throws Exception {
        if (this.bloqueioCaixa == null) {
            this.bloqueioCaixa = new BloqueioCaixa();
        }
        bloqueioCaixa.prepararConexao();
        return bloqueioCaixa;
    }

    public CaixaContaInterfaceFacade getCaixaConta() throws Exception {
        if (this.caixaConta == null) {
            this.caixaConta = new CaixaConta();
        }
        caixaConta.prepararConexao();
        return caixaConta;
    }

    public CaixaMovContaInterfaceFacade getCaixaMovConta() throws Exception {
        if (this.caixaMovConta == null) {
            this.caixaMovConta = new CaixaMovConta();
        }
        caixaMovConta.prepararConexao();
        return caixaMovConta;
    }

    // /GETTERS
    public ContaInterfaceFacade getConta() throws Exception {
        if (conta == null) {
            conta = new Conta();
        }
        conta.prepararConexao();
        return conta;
    }

    public TipoContaInterfaceFacade getTipoConta() throws Exception {
        if (tipoConta == null) {
            tipoConta = new TipoConta();
        }
        tipoConta.prepararConexao();
        return tipoConta;
    }

    public TipoDocumentoInterfaceFacade getTipoDocumento() throws Exception {
        if (tipoDocumento == null) {
            tipoDocumento = new TipoDocumento();
        }
        tipoDocumento.prepararConexao();
        return tipoDocumento;
    }

    public MovContaInterfaceFacade getMovConta() throws Exception {
        if (movConta == null) {
            movConta = new MovConta();
        }
        movConta.prepararConexao();
        return movConta;
    }

    public LancamentosMovContaFacade getLancamentosMovConta() throws Exception {
        if (lancamentosMovConta == null) {
            lancamentosMovConta = new LancamentosMovConta();
        }
        lancamentosMovConta.prepararConexao();
        return lancamentosMovConta;
    }

    public MovContaRateioInterfaceFacade getMovContaRateio() throws Exception {
        if (movContaRateio == null) {
            movContaRateio = new MovContaRateio();
        }
        movContaRateio.prepararConexao();
        return movContaRateio;
    }

    public PlanoContaInterfaceFacade getPlanoConta() throws Exception {
        if (planoConta == null) {
            planoConta = new PlanoConta();
        }
        planoConta.prepararConexao();
        return planoConta;
    }

    public CentroCustoInterfaceFacade getCentroCusto() throws Exception {
        if (centroCusto == null) {
            centroCusto = new CentroCusto();
        }
        centroCusto.prepararConexao();
        return centroCusto;
    }

    public RateioIntegracaoInterfaceFacade getRateioIntegracao() throws Exception {
        if (rateioIntegracao == null) {
            rateioIntegracao = new RateioIntegracao();
        }
        rateioIntegracao.prepararConexao();
        return rateioIntegracao;
    }

    public AgendamentoFinanceiroInterfaceFacade getAgendamentoFinanceiro() throws Exception {
        if (agendamentoFinanceiro == null) {
            agendamentoFinanceiro = new AgendamentoFinanceiro();
        }
        agendamentoFinanceiro.prepararConexao();
        return agendamentoFinanceiro;
    }

    public HistoricoChequeInterfaceFacade getHistoricoCheque() throws Exception {
        if (historicoCheque == null) {
            historicoCheque = new HistoricoCheque();
        }
        historicoCheque.prepararConexao();
        return historicoCheque;
    }

    public ContaContabilInterfaceFacade getContaContabil() throws Exception{
        if (contaContabil == null) {
            contaContabil = new ContaContabil();
        }
        contaContabil.prepararConexao();

        return contaContabil;
    }

    public ConfiguracaoFinanceiro getConfiguracaoFinanceiro() throws Exception{
        if (configuracaoFinanceiro == null) {
            configuracaoFinanceiro = new ConfiguracaoFinanceiro();
        }
        configuracaoFinanceiro.prepararConexao();
        return configuracaoFinanceiro;
    }

    public void setConfiguracaoFinanceiro(ConfiguracaoFinanceiro configuracaoFinanceiro) {
        this.configuracaoFinanceiro = configuracaoFinanceiro;
    }

    public void setContaContabil(ContaContabilInterfaceFacade contaContabil) {
        this.contaContabil = contaContabil;
    }

    public DFSinteticoDWInterfaceFacade getDFSinteticoDW() throws Exception {
        if (dfSinteticoDW == null) {
            dfSinteticoDW = new DFSinteticoDW();
        }
        dfSinteticoDW.prepararConexao();
        return dfSinteticoDW;
    }

    public DRESinteticoDWInterfaceFacade getDRESinteticoDW() throws Exception {
        if (dreSinteticoDW == null) {
            dreSinteticoDW = new DRESinteticoDW();
        }
        dreSinteticoDW.prepararConexao();
        return dreSinteticoDW;
    }

    public MovContaContabilInterfaceFacade getMovContaContabil() throws Exception{
        if (movContaContabil == null) {
            movContaContabil = new MovContaContabil();
        }
        movContaContabil.prepararConexao();

        return movContaContabil;
    }

    public GestaoRecebiveisServiceImpl getRecebiveis() throws Exception{
        if (recebiveis == null) {
            recebiveis = new GestaoRecebiveisServiceImpl();
        }
        recebiveis.prepararConexao();
        return recebiveis;
    }
    public DevolucaoChequeService getDevolucaoServico() throws Exception{
        if (servico == null) {
            servico = new DevolucaoChequeServiceImpl();
        }
        servico.prepararConexao();
        return servico;
    }

    public void setMovContaContabil(MovContaContabilInterfaceFacade movContaContabil) {
        this.movContaContabil = movContaContabil;
    }
}
