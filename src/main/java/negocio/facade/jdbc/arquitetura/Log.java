package negocio.facade.jdbc.arquitetura;

import br.com.pactosolucoes.enumeradores.LogGenericoTipoEnum;
import br.com.pactosolucoes.enumeradores.OrigemCadEAlteracaoCliente;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estrutura.paginacao.PreparedStatementPersonalizado;
import negocio.comuns.arquitetura.LogAgrupadoChavePrimaria;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import relatorio.negocio.comuns.basico.HistoricoParcelaOriginalTO;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import servicos.vendasonline.dto.VendaDTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>UsuarioVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>UsuarioVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see UsuarioVO
 * @see SuperEntidade
 */
public class Log extends SuperEntidade implements LogInterfaceFacade {

    public Log() throws Exception {
        super();

    }

    public Log(Connection con) throws Exception {
        super(con);

    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>UsuarioVO</code>.
     */
    public LogVO novo() throws Exception {
        LogVO obj = new LogVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>UsuarioVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>UsuarioVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(LogVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>UsuarioVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>UsuarioVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(LogVO obj) throws Exception {
        String sql = "INSERT INTO Log( nomeEntidade, nomeEntidadeDescricao, chavePrimaria, chavePrimariaEntidadeSubordinada, nomeCampo, valorCampoAnterior, valorCampoAlterado, dataAlteracao, responsavelAlteracao, operacao, pessoa, cliente, origem) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) RETURNING codigo";
        PreparedStatement sqlInserir = con.prepareStatement(sql);

        int i = 1;
        sqlInserir.setString(i++, obj.getNomeEntidade().toUpperCase());
        sqlInserir.setString(i++, obj.getNomeEntidadeDescricao());
        sqlInserir.setString(i++, obj.getChavePrimaria());
        sqlInserir.setString(i++, obj.getChavePrimariaEntidadeSubordinada());
        sqlInserir.setString(i++, obj.getNomeCampo());
        sqlInserir.setString(i++, obj.getValorCampoAnterior());
        sqlInserir.setString(i++, obj.getValorCampoAlterado());
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataAlteracao()));
        sqlInserir.setString(i++, obj.getResponsavelAlteracao() + obj.getUserOAMD());
        sqlInserir.setString(i++, obj.getOperacao());
        sqlInserir.setInt(i++, obj.getPessoa());

        if (obj.getCliente() == null) {
            sqlInserir.setNull(i++, Types.NUMERIC);
        } else {
            sqlInserir.setInt(i++, obj.getCliente());
        }

        if (UteisValidacao.emptyString(obj.getOrigem())) {
            sqlInserir.setNull(i++, Types.VARCHAR);
        } else {
            sqlInserir.setString(i++, obj.getOrigem());
        }

        try (ResultSet rs = sqlInserir.executeQuery()) {
            if (rs.next()) {
                obj.setCodigo(rs.getInt("codigo"));
                obj.setNovoObj(false);
            }
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>UsuarioVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>UsuarioVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(LogVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>UsuarioVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>UsuarioVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterarSemCommit(LogVO obj) throws Exception {
        try {
            String sql = "UPDATE Log set nomeEntidade=?, nomeEntidadeDescricao=?, chavePrimaria=?, chavePrimariaEntidadeSubordinada=?, nomeCampo=?, valorCampoAnterior=?, valorCampoAlterado=?, dataAlteracao=?, responsavelAlteracao=?, operacao=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getNomeEntidade().toUpperCase());
            sqlAlterar.setString(2, obj.getNomeEntidadeDescricao());
            sqlAlterar.setString(3, obj.getChavePrimaria());
            sqlAlterar.setString(4, obj.getChavePrimariaEntidadeSubordinada());
            sqlAlterar.setString(5, obj.getNomeCampo());
            sqlAlterar.setString(6, obj.getValorCampoAnterior());
            sqlAlterar.setString(7, obj.getValorCampoAlterado());
            sqlAlterar.setTimestamp(8, Uteis.getDataJDBCTimestamp(obj.getDataAlteracao()));
            sqlAlterar.setString(9, obj.getResponsavelAlteracao() + obj.getUserOAMD());
            sqlAlterar.setString(10, obj.getOperacao());
            sqlAlterar.setInt(11, obj.getCodigo());
            sqlAlterar.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>UsuarioVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>UsuarioVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(LogVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            //   Log.excluir(getIdEntidade());
            String sql = "DELETE FROM Log WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Usuario</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>UsuarioVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        //  SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM LOG WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY dataAlteracao desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorNomeEntidade(String nomeEntidade, Date dataInicio, Date dataFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        //  SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Log WHERE nomeEntidade like ('%" + nomeEntidade.toUpperCase() + "%') ";
        if (dataInicio != null) {
            sqlStr += "AND dataAlteracao >=  '" + Uteis.getDataJDBC(dataInicio) + "' ";
        }
        if (dataFim != null) {
            sqlStr += "AND dataAlteracao <=  '" + Uteis.getDataJDBC(dataFim) + "' ";
        }
        sqlStr += " ORDER BY dataAlteracao desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * * Metodo consultar foi criado para encapsular os metodos:
     * 		public List consultarPorNomeEntidade(String nomeEntidade, boolean controlarAcesso, int nivelMontarDados) throws Exception;
     * 		public List consultarPorResponsavel(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception;
     * 		public List consultarPorOpercao(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception;
     * 		public List consultarPorConteudoLog(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception;
     *
     * Que recebera um objeto ControleConsulta com os respectivos filtros para a tabela.
     *
     * Autor: Pedro Y. Saito
     * Criado em 29/12/2010
     *
     */
    @SuppressWarnings("unchecked")
    public List consultar(ControleConsulta filtro, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultar(filtro, controlarAcesso, nivelMontarDados, null);
    }

    /**
     * * Metodo consultar foi criado para encapsular os metodos:
     * 		public List consultarPorNomeEntidade(String nomeEntidade, boolean controlarAcesso, int nivelMontarDados) throws Exception;
     * 		public List consultarPorResponsavel(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception;
     * 		public List consultarPorOpercao(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception;
     * 		public List consultarPorConteudoLog(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception;
     *
     * Que recebera um objeto ControleConsulta com os respectivos filtros para a tabela.
     *
     * Autor: Pedro Y. Saito
     * Criado em 27/12/2010
     *
     */
    @SuppressWarnings("unchecked")
    public List consultar(ControleConsulta filtro, boolean controlarAcesso, int nivelMontarDados, ConfPaginacao confPaginacao) throws Exception {
        StringBuffer sqlSelect = new StringBuffer();

        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //sql principal
        sqlSelect.append("SELECT codigo, operacao, responsavelalteracao, dataalteracao, valorcampoalterado, valorcampoanterior  ");
        sqlSelect.append(", nomecampo, chaveprimariaentidadesubordinada, chaveprimaria, nomeentidadedescricao, nomeentidade , pessoa");

        sqlSelect.append(" FROM Log ");

        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);

        //a utilização dos filtros deve ser feita utilizando confPaginacao.getSqlFiltro()
        filtros(filtro, confPaginacao.getSqlFiltro());
        if (!"".equals(confPaginacao.getSqlFiltro().toString())) {
            sqlSelect.append(confPaginacao.getSqlFiltro().toString());
        }

        sqlSelect.append(" ORDER BY dataAlteracao desc");

        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlSelect);

        filtrosValoresStatement(filtro, confPaginacao.getStm());

        //4 - REALIZA A CONSULTA COM PAGINACAO
        ResultSet tabelaResultado = confPaginacao.consultaPaginada();

        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }



    public List consultarSemLimitacao(ControleConsulta filtro,  int nivelMontarDados) throws Exception {

        try {

            if (filtro.getFim() != null && filtro.getInicio() != null){
                if (filtro.getFim().compareTo(filtro.getInicio()) < 0){
                    throw new ConsistirException("Data final menor que data inicial!");
                }
            }

            StringBuffer sqlSelect = new StringBuffer("SELECT distinct(date_trunc('min', dataalteracao)) dataalteracao, operacao, responsavelalteracao, nomeentidade FROM Log ");
            filtros(filtro, sqlSelect);
            sqlSelect.append(" ORDER BY date_trunc('min', dataalteracao) desc");

            PreparedStatement stm = con.prepareStatement(sqlSelect.toString());
            int i = 1;
            if (!filtro.getValorConsulta().equals("")){
                if(filtro.getCampoConsulta().equals("conteudoLog")){
                     stm.setString(i++, "%"+filtro.getValorConsulta()+"%");
                     stm.setString(i++, "%"+filtro.getValorConsulta()+"%");
                } else {
                    stm.setString(i++, filtro.getValorConsulta()+"%");
                }
            }
            if (!filtro.getValorConsulta2().equals("")){
                if(filtro.getCampoConsulta2().equals("conteudoLog")){
                    stm.setString(i++, "%"+filtro.getValorConsulta2()+"%");
                     stm.setString(i++, "%"+filtro.getValorConsulta2()+"%");
                } else {
                    stm.setString(i++, filtro.getValorConsulta2()+"%");
                }
            }
                
            ResultSet tabelaResultado = stm.executeQuery();

            List<LogVO> lista = new ArrayList<LogVO>(0);
            while (tabelaResultado.next()){
                LogVO obj = new LogVO();
                obj.setDataAlteracao(tabelaResultado.getTimestamp("dataalteracao"));
                obj.setOperacao(tabelaResultado.getString("operacao"));
                obj.setResponsavelAlteracao(tabelaResultado.getString("responsavelalteracao"));
                obj.setNomeEntidade(tabelaResultado.getString("nomeentidade"));
                lista.add(obj);
            }
            return lista;

        }catch (Exception e){
            throw e;
        }

    }

    public List consultarApartirDoResponsavelDataAlteracao(String responsavel, String dataAlteracao, int nivelMontarDados) throws Exception {
        StringBuffer sqlSelect = new StringBuffer("SELECT valorcampoalterado, valorcampoanterior, nomecampo, pessoa.nome \n");
        sqlSelect.append("FROM Log left join pessoa on pessoa.codigo = log.pessoa \n");
        sqlSelect.append("WHERE responsavelalteracao ilike '%").append(responsavel).append("%' and date_trunc('min', dataalteracao) = '").append(dataAlteracao).append("' ");
        sqlSelect.append("ORDER BY dataAlteracao desc \n");

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlSelect.toString());
        List<LogVO> lista = new ArrayList<LogVO>(0);
        while (tabelaResultado.next()){
            LogVO obj = new LogVO();
            obj.setNomePessoa(tabelaResultado.getString("nome"));
            obj.setNomeCampo(tabelaResultado.getString("nomecampo"));
            obj.setValorCampoAnterior(tabelaResultado.getString("valorcampoanterior"));
            obj.setValorCampoAlterado(tabelaResultado.getString("valorcampoalterado"));
            lista.add(obj);
        }
        return lista;
    }

    @Override
    public LogVO consultarUltimoLogPessoa(Integer codigoPessoa,String... entidades) throws Exception {
        String sql = "SELECT * FROM log WHERE pessoa = ? ";
        if(!UteisValidacao.emptyArray(entidades)){
            sql += " and nomeentidade in ";
            for(String entidade: entidades){
                sql += ",?";
            }
            sql = sql.replaceFirst(",", "(");
            sql += ") ";
        }        
        sql += " ORDER BY dataAlteracao DESC LIMIT 1";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 1;
        ps.setInt(i++, codigoPessoa);
        if(!UteisValidacao.emptyArray(entidades)){
            for(String entidade: entidades){
               ps.setString(i++, entidade);
            }
        }
        ResultSet rs = ps.executeQuery();
        return rs.next() ? montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS) : null;
    }

    /**
     * Autor: Pedro Y. Saito
     * Criado em 21/01/2011
     */
    private void filtrosValoresStatement(ControleConsulta filtro, PreparedStatementPersonalizado stm) throws SQLException {
        int i = 0;

        if (!"".equals(filtro.getValorConsulta())) {
            if (!"".equals(filtro.getCampoConsulta()) && filtro.getCampoConsulta().equals("responsavel")) {
                stm.setString(i++, filtro.getValorConsulta());
            } else if (!"".equals(filtro.getCampoConsulta()) && !filtro.getCampoConsulta().equals("conteudoLog")) {
                stm.setString(i++, "%" + filtro.getValorConsulta() + "%");
            } else if (!"".equals(filtro.getCampoConsulta()) && filtro.getCampoConsulta().equals("conteudoLog")) {
                stm.setString(i++, "%" + filtro.getValorConsulta() + "%");
                stm.setString(i++, "%" + filtro.getValorConsulta() + "%");
            }
        }
        if (!"".equals(filtro.getValorConsulta2())) {
            if (!"".equals(filtro.getCampoConsulta2()) && filtro.getCampoConsulta2().equals("responsavel")) {
                stm.setString(i++, filtro.getValorConsulta2());
            } else if (!"".equals(filtro.getCampoConsulta2()) && !filtro.getCampoConsulta2().equals("conteudoLog")) {
                stm.setString(i++, "%" + filtro.getValorConsulta2() + "%");
            } else if (!"".equals(filtro.getCampoConsulta2()) && filtro.getCampoConsulta2().equals("conteudoLog")) {
                stm.setString(i++, "%" + filtro.getValorConsulta2() + "%");
                stm.setString(i++, "%" + filtro.getValorConsulta2() + "%");
            }
        }
    }

    /**
     * Autor: Pedro Y. Saito
     * Criado em 21/01/2011
     * @throws Exception 
     */
    private void filtros(ControleConsulta filtro, StringBuffer sqlFiltro) throws Exception {

        sqlFiltro.append(" WHERE 1 = 1");

        if (!UteisValidacao.emptyString(filtro.getValorConsulta())) {
            if (!UteisValidacao.emptyString(filtro.getCampoConsulta()) && filtro.getCampoConsulta().equals("nomeEntidade")) {
                sqlFiltro.append(" AND nomeEntidade ilike ? ");
            } else if (!"".equals(filtro.getCampoConsulta()) && filtro.getCampoConsulta().equals("responsavel")) {
                sqlFiltro.append(" AND responsavelAlteracao ilike ? ");
            } else if (!"".equals(filtro.getCampoConsulta()) && filtro.getCampoConsulta().equals("operacao")) {
                sqlFiltro.append(" AND operacao ilike ? ");
            } else if (!"".equals(filtro.getCampoConsulta()) && filtro.getCampoConsulta().equals("conteudoLog")) {
                sqlFiltro.append(" AND (valorCampoAnterior ilike ? or valorCampoAlterado ilike ?)");
            }
        }

        if (!UteisValidacao.emptyString(filtro.getValorConsulta2())) {
            if (!UteisValidacao.emptyString(filtro.getCampoConsulta2()) && filtro.getCampoConsulta2().equals("nomeEntidade")) {
                sqlFiltro.append(" AND nomeEntidade ilike ? ");
            } else if (!UteisValidacao.emptyString(filtro.getCampoConsulta2()) && filtro.getCampoConsulta2().equals("responsavel")) {
                sqlFiltro.append(" AND responsavelAlteracao ilike ? ");
            } else if (!UteisValidacao.emptyString(filtro.getCampoConsulta2()) && filtro.getCampoConsulta2().equals("operacao")) {
                sqlFiltro.append(" AND operacao ilike ? ");
            } else if (!UteisValidacao.emptyString(filtro.getCampoConsulta2()) && filtro.getCampoConsulta2().equals("conteudoLog")) {
                sqlFiltro.append(" AND (valorCampoAnterior ilike ? or valorCampoAlterado ilike ?)");
            }
        }

        if (filtro.getInicio() != null && filtro.getFim() != null) {
            sqlFiltro.append(" AND dataalteracao BETWEEN '").append(Uteis.getDataJDBC(filtro.getInicio())).append(" 00:00:00' AND '").append(Uteis.getDataJDBC(filtro.getFim())).append(" 23:59:59'");
        }
    }

    public List consultarPorNomeEntidadePorDataAlteracaoPorOperacao(String nomeEntidade,
            java.sql.Date dataAlteracaoInicio, java.sql.Date dataAlteracaoFim,
            String operacao, String nomeEmpresa, boolean buscarComAdministrador, List<ColaboradorVO> lista,
            int nivelMontarDados) throws SQLException, Exception {
        int qtde = 0;
        //consultar(getIdEntidade(),true);
        String sqlStr = "SELECT * FROM Log WHERE nomeEntidade ilike '" + nomeEntidade.toUpperCase() + "' "
                + "and dataalteracao between '" + dataAlteracaoInicio + " 00:00:00' and '" + dataAlteracaoFim
                + " 23:59:59' and operacao ilike '" + operacao + "' ";
        if (!buscarComAdministrador) {
            sqlStr += " and not (responsavelalteracao ilike 'ADMINISTRADOR' )";
        }

        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr += " AND ( ";
                    qtde++;
                } else {
                    sqlStr += " OR ";
                }
                sqlStr += " (responsavelalteracao LIKE '" + co.getPessoa().getNome() + "%')\n";
            }
        }

        if (!nomeEmpresa.isEmpty()) {
            sqlStr += " and valorcampoalterado ilike '%Empresa = " + nomeEmpresa + "%'";
        }
        sqlStr += (qtde > 0 ? ")" : "");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public int contarPorNomeEntidadePorDataAlteracaoPorOperacao(String nomeEntidade,
            java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim, String operacao,
            String nomeEmpresa, boolean buscarComAdministrador,
            List<ColaboradorVO> lista, int nivelMontarDados) throws SQLException, Exception {
        int qtde = 0;
        //consultar(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(codigo) as cod FROM Log\n");
        sql.append("WHERE (nomeEntidade = '").append(nomeEntidade.toUpperCase()).append("' OR nomeEntidade = '").append(nomeEntidade).append("')\n");
        sql.append("AND dataAlteracao between '").append(dataAlteracaoInicial).append(" 00:00:00' AND '").append(dataAlteracaoFim).append(" 23:59:59'\n");
        sql.append("AND (operacao LIKE '").append(operacao.toUpperCase()).append("' OR operacao =  '").append(operacao).append("')\n");

        if (!buscarComAdministrador) {
            sql.append(" and not (responsavelalteracao  = 'ADMINISTRADOR' )");
        }
        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sql.append(" AND ( ");
                    qtde++;
                } else {
                    sql.append(" OR ");
                }
                sql.append(" (responsavelAlteracao LIKE '").append(co.getPessoa().getNome()).append("%')\n");
            }
        }
        sql.append((qtde > 0 ? ")" : ""));

        if (!nomeEmpresa.isEmpty()) {
            sql.append("AND valorcampoalterado LIKE '%Empresa = ").append(nomeEmpresa).append("%'\n");
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt("cod");
    }

    public int contarPorNomeEntidadePorDataAlteracaoPorOperacao(String nomeEntidade,
            java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim, int empresa,
            List<ColaboradorVO> lista, int nivelMontarDados) throws SQLException, Exception {
        int qtde = 0;
        String sqlStr = "SELECT count(distinct(chaveprimaria)) as cod FROM Log "
                //+ " inner join pessoa on pessoa.codigo= log.pessoa "
                + " inner join cliente on cliente.pessoa= log.pessoa "
                + " WHERE UPPER(nomeEntidade) like '" + nomeEntidade.toUpperCase() + "' "
                + "and dataalteracao between '" + dataAlteracaoInicial + " 00:00:00' and '"
                + dataAlteracaoFim + " 23:59:59' ";

        if (empresa > 0) {
            sqlStr += " and cliente.empresa =" + empresa;
        }
        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr += " AND ( ";
                    qtde++;
                } else {
                    sqlStr += " OR ";
                }
                sqlStr += " (responsavelalteracao LIKE '" + co.getPessoa().getNome() + "%')\n";
            }
        }
        sqlStr += (qtde > 0 ? ")" : "");

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt("cod");
    }

    public List<LogAgrupadoChavePrimaria> consultarPorNomeCodigoEntidadeAgrupadoPorChavePrimaria(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim,
            Integer codigoPessoa, int codigoEmpresa, List<ColaboradorVO> lista, int nivelMontarDados,  boolean agruparSegundo) throws Exception {
        //lista de logs agrupados por DATA/HORA, OPERACAO E RESPONSAVEL IGUAIS
        List<LogVO> listaLogsAgrupados = consultarPorNomeEmpresaCodigoEntidadeAgrupado(nomeEntidade, codigoEntidade, dataInicio, dataFim, codigoPessoa, codigoEmpresa, lista, nivelMontarDados, agruparSegundo);
        List<LogAgrupadoChavePrimaria> listaLogsAgrupadosPorChavePrimaria = new ArrayList<LogAgrupadoChavePrimaria>();
        //indice usado para verificar se é o primeiro registro analisado
        int index = 0;
        //armazena a chave primária anterior para efeitos de comparação
        int chavePrimariaAnterior = 0;
        LogAgrupadoChavePrimaria logAgrupadoChavePrimaria = new LogAgrupadoChavePrimaria();
        //itera na lista da consulta para adicionar na nova lista os registros agrupados por chave primária
        for (LogVO log : listaLogsAgrupados) {
            try{
                //se for o primeiro registro ou a chave primária não for igual a anterior preenche o log
                if (index == 0 || Integer.parseInt(log.getChavePrimaria()) != chavePrimariaAnterior) {
                    if (index != 0) {
                        listaLogsAgrupadosPorChavePrimaria.add(logAgrupadoChavePrimaria);
                    }
                    index++;
                    chavePrimariaAnterior = Integer.parseInt(log.getChavePrimaria());
                    logAgrupadoChavePrimaria = new LogAgrupadoChavePrimaria();
                    logAgrupadoChavePrimaria.setChavePrimaria(log.getChavePrimaria());
                    logAgrupadoChavePrimaria.setNomeEntidade(log.getNomeEntidade());
                    logAgrupadoChavePrimaria.setDataAlteracao(log.getDataAlteracao());
                    logAgrupadoChavePrimaria.montarOperacao(log, logAgrupadoChavePrimaria);
                    logAgrupadoChavePrimaria.setResponsavelAlteracao(log.getResponsavelAlteracao());
                    logAgrupadoChavePrimaria.setPessoa(log.getPessoa());
                } else {
                    logAgrupadoChavePrimaria.montarOperacao(log, logAgrupadoChavePrimaria);
                }
            }catch(Exception e){

            }
        }
        index = 0;
        if (listaLogsAgrupados != null && !listaLogsAgrupados.isEmpty()) {
            listaLogsAgrupadosPorChavePrimaria.add(logAgrupadoChavePrimaria);
        }
        for (LogAgrupadoChavePrimaria logAgrupado : listaLogsAgrupadosPorChavePrimaria) {
            for (LogVO log : listaLogsAgrupados) {
                if (log.getChavePrimaria().equals(logAgrupado.getChavePrimaria())) {
                    logAgrupado.getListaLogsChavePrimaria().add(log);
                }
            }
        }
        return listaLogsAgrupadosPorChavePrimaria;
    }

    public List consultarPorNomeEntidade(String nomeEntidade, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        //  SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Log WHERE nomeEntidade like ('%" + nomeEntidade.toUpperCase() + "%') ORDER BY dataAlteracao desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorCodigoEntidade(Integer codigoEntidade, Date dataInicio, Date dataFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        //  SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Log WHERE chavePrimaria = (" + codigoEntidade.intValue() + ") ";
        if (dataInicio != null) {
            sqlStr += "AND dataAlteracao >=  '" + Uteis.getDataJDBC(dataInicio) + "' ";
        }
        if (dataFim != null) {
            sqlStr += "AND dataAlteracao <=  '" + Uteis.getDataJDBC(dataFim) + "' ";
        }
        sqlStr += " ORDER BY dataAlteracao desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public LogVO consultarPorNomeCodigoEntidade(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        //  SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Log WHERE nomeEntidade like ('%" + nomeEntidade + "%') and chavePrimaria = " + codigoEntidade.intValue();
        if (dataInicio != null) {
            sqlStr += "AND dataAlteracao >=  '" + Uteis.getDataJDBC(dataInicio) + "' ";
        }
        if (dataFim != null) {
            sqlStr += "AND dataAlteracao <=  '" + Uteis.getDataJDBC(dataFim) + "' ";
        }
        sqlStr += " ORDER BY dataAlteracao desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    /**
     * Consulta de logs pesquisando por empresa e lista de colaboradores
     * @param nomeEntidade
     * @param codigoEntidade
     * @param dataInicio
     * @param dataFim
     * @param codigoPessoa
     * @param codigoEmpresa
     * @param lista
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public List consultarPorNomeEmpresaCodigoEntidadeAgrupado(String nomeEntidade, Integer codigoEntidade,
            Date dataInicio, Date dataFim, Integer codigoPessoa, Integer codigoEmpresa,
            List<ColaboradorVO> lista, int nivelMontarDados, boolean agruparSegundo) throws Exception {
        StringBuffer sqlSelect = new StringBuffer("SELECT log.* FROM Log "
                + " inner join cliente on cliente.pessoa= log.pessoa");

        String sqlStr = "";
        if (!nomeEntidade.isEmpty()) {
            sqlStr = " nomeEntidade like ('%" + nomeEntidade + "%') ";
        }
        if (codigoEntidade > 0) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr += "and chavePrimaria = '" + codigoEntidade.intValue() + "'";
            } else {
                sqlStr += " chavePrimaria = '" + codigoEntidade.intValue() + "'";
            }
        }
        if (dataInicio != null) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr += " AND dataAlteracao >=  '" + Uteis.getDataHoraJDBC(dataInicio, "00:00:00") + "'";
            } else {
                sqlStr += " dataAlteracao >=  '" + Uteis.getDataHoraJDBC(dataInicio, "00:00:00") + "'";
            }
        }
        if (dataFim != null) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr += " AND dataAlteracao <=  '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'";
            } else {
                sqlStr += " dataAlteracao <=  '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'";
            }
        }
        if (codigoPessoa != null && codigoPessoa != 0) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                if (codigoPessoa != codigoEntidade) {
                    sqlStr = "(" + sqlStr + " ) or (pessoa.codigo =  " + codigoPessoa + ")";
                } else {
                    sqlStr = "(" + sqlStr + " ) and (pessoa.codigo =  " + codigoPessoa + ")";
                }
            } else {
                sqlStr += " log.pessoa =  " + codigoPessoa;
            }
        }
        if (codigoEmpresa > 0) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr += "and  cliente.empresa = " + codigoEmpresa;
            } else {
                sqlStr += " cliente.empresa = " + codigoEmpresa;
            }
        }
        int qtde = 0;
        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr += " AND ( ";
                    qtde++;
                } else {
                    sqlStr += " OR ";
                }
                sqlStr += " (responsavelalteracao LIKE '" + co.getPessoa().getNome() + "%')\n";
            }
        }
        sqlStr += (qtde > 0 ? ")" : "");

        if (sqlStr != null && !"".equals(sqlStr)) {
            sqlSelect.append(" WHERE " + sqlStr);
        }
        sqlSelect.append(" ORDER BY dataAlteracao desc");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlSelect.toString());

        return montarDadosAgrupado(tabelaResultado, nivelMontarDados, agruparSegundo);
    }

    /**
     * Metodo que consulta os logs respeitando os filtros passados e faz o agrupamento
     * dos logs quando DATA/HORA, OPERACAO E RESPONSAVEL sao iguais
     *
     * Autor: Pedro Y. Saito
     * Criado em 18/01/2011
     */
    @SuppressWarnings("unchecked")
    public List consultarPorNomeCodigoEntidadeAgrupado(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, Integer codigoPessoa, int nivelMontarDados,  boolean agruparSegundo) throws Exception {

//    	//1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
//		confPaginacao.configurarNavegacao();
        //INICIO - MONTANDO CONSULTA============================================================================
        StringBuffer sqlSelect = new StringBuffer("SELECT * FROM Log ");

        String sqlStr = "";
        if (!nomeEntidade.isEmpty()) {
            sqlStr = " nomeEntidade like ('" + nomeEntidade + "%') ";
        }
        if (!UteisValidacao.emptyNumber(codigoEntidade)) {
            sqlStr += "and chavePrimaria = '" + codigoEntidade + "'";
        }
        if (dataInicio != null) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr += " AND dataAlteracao >=  '" + Uteis.getDataHoraJDBC(dataInicio, "00:00:00") + "'";
            } else {
                sqlStr += " dataAlteracao >=  '" + Uteis.getDataHoraJDBC(dataInicio, "00:00:00") + "'";
            }
        }
        if (dataFim != null) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr += " AND dataAlteracao <=  '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'";
            } else {
                sqlStr += " dataAlteracao <=  '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'";
            }
        }
        if (codigoPessoa != null && codigoPessoa != 0) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                if (codigoEntidade > 0 && codigoPessoa != codigoEntidade) {
                    sqlStr = "(" + sqlStr + " and chavePrimaria::text::integer <> pessoa  ) or (pessoa =  " + codigoPessoa + ")";
                } else {
                    sqlStr = "(" + sqlStr + " ) and (pessoa =  " + codigoPessoa + ")";
                }
            } else {
                sqlStr += " pessoa =  " + codigoPessoa;
            }
        }
        if (sqlStr != null && !"".equals(sqlStr)) {
            sqlSelect.append(" WHERE " + sqlStr);
        }

//        sqlSelect.append(" ORDER BY operacao, nomeentidade, responsavelalteracao, dataAlteracao desc");
        sqlSelect.append(" ORDER BY dataAlteracao desc, codigo desc");

        if (sqlStr == null || "".equals(sqlStr)) {
            //limita consulta sem where
            sqlSelect.append(" LIMIT 3000");
        }
        //FIM - MONTANDO CONSULTA============================================================================

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlSelect.toString());
        return montarDadosAgrupado(tabelaResultado, nivelMontarDados, agruparSegundo);
    }

    /**
     * Montar dados que agrupa quando DATA/HORA, OPERACAO E RESPONSAVEL forem iguais
     * @param tabelaResultado
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    private List montarDadosAgrupado(ResultSet tabelaResultado, int nivelMontarDados, boolean agruparSegundo) throws Exception {
        List vetResultado = new ArrayList();
        LogVO obj = new LogVO();
        LogVO objMontado = null;
        LogVO objAnterior = null;
        boolean add = false;
        String descricao = null;
        while (tabelaResultado.next()) {
            descricao = "";
            objMontado = new LogVO();
            objMontado = montarDados(tabelaResultado, nivelMontarDados);
            add = false;

            if (objMontado != null) {
                if (objAnterior != null) {
                    String operacao1 = objMontado.getOperacao();
                    String operacao2 = objAnterior.getOperacao();
                    try {
                        if (objMontado.getOperacao().contains("-")) {
                            String[] aux = objMontado.getOperacao().split("-");
                            operacao1 = aux[0].trim();
                        }
                        if (objAnterior.getOperacao().contains("-")) {
                            String[] aux = objAnterior.getOperacao().split("-");
                            operacao2 = aux[0].trim();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //VERIFICANDO SE O LOG DEVERA SER AGRUPADO OU NAO, INICIALMENTE O AGRUPAMENTO OCORRE
                    //QUANDO DATA/HORA, OPERACAO E RESPONSAVEL SAO IGUAIS
                    if (((agruparSegundo && (Uteis.getDataComHHMM(objMontado.getDataAlteracao()).equals(Uteis.getDataComHHMM(objAnterior.getDataAlteracao()))))
                            || (!agruparSegundo && (Uteis.gethoraHHMMSSAjustado(objMontado.getDataAlteracao()).equals(Uteis.gethoraHHMMSSAjustado(objAnterior.getDataAlteracao())))))
                            && operacao1.equals(operacao2)
                            && objMontado.getResponsavelAlteracao().equals(objAnterior.getResponsavelAlteracao())
                            && (!UteisValidacao.emptyNumber(objMontado.getPessoa()) ||  objMontado.getChavePrimaria().equals(objAnterior.getChavePrimaria()))) {
                        if (!"".equals(objMontado.getNomeCampo()) && !"".equals(objMontado.getNomeCampo())) {
                            if (!"".equals(obj.getDescricao())) {
                                obj.setNomeCampo(obj.getNomeCampo() + " - [" + objMontado.getNomeCampo() + "]");
                            } else {
                                obj.setNomeCampo("[" + objMontado.getNomeCampo() + "]");
                            }

                            if (objMontado.getValorCampoAnterior() != null) {
                                if (!"".equals(objMontado.getNomeCampo())) {
                                    obj.setValorCampoAnterior(obj.getValorCampoAnterior().trim() + "\n[" + objMontado.getNomeCampo().trim() + ": " + objMontado.getValorCampoAnterior().trim() + "]");
                                } else {
                                    obj.setValorCampoAnterior(obj.getValorCampoAnterior().trim() + "\n[" + objMontado.getValorCampoAnterior().trim() + "]");
                                }

                                if (!"".equals(objMontado.getValorCampoAnterior())) {
                                    descricao = "'" + objMontado.getValorCampoAnterior().trim() + "'";
                                } else {
                                    descricao = "'Sem valor'";
                                }
                            }
                            if (objMontado.getValorCampoAlterado() != null) {
                                if (!"".equals(objMontado.getNomeCampo())) {
                                    obj.setValorCampoAlterado(obj.getValorCampoAlterado().trim() + "\n[" + objMontado.getNomeCampo().trim() + ": " + objMontado.getValorCampoAlterado().trim() + "]");
                                } else {
                                    obj.setValorCampoAlterado(obj.getValorCampoAlterado().trim() + "\n[" + objMontado.getValorCampoAlterado().trim() + "]");
                                }

                                if (!"".equals(objMontado.getValorCampoAlterado())) {
                                    descricao = descricao + " para '" + objMontado.getValorCampoAlterado().trim() + "'";
                                } else {
                                    descricao = descricao + " para 'Sem valor'";
                                }
                            }
                            if (!"".equals(descricao)) {
                                obj.setDescricao(obj.getDescricao() + "<br>[" + objMontado.getNomeCampo().trim() + ": " + descricao + "]");
                            }
                        }
                    } else {
                        add = true;
                    }
                } else {
                    iniciarNovoLog(obj, objMontado);
                }
                if (add) {
                    vetResultado.add(obj);
                    obj = new LogVO();
                    iniciarNovoLog(obj, objMontado);
                    //continue;
                }
            }
            if (tabelaResultado.isLast() && objAnterior!=null && objAnterior.getCodigo().intValue()!=objMontado.getCodigo().intValue()) {
                vetResultado.add(obj);
            }
            objAnterior = objMontado;
        }
        if(vetResultado.isEmpty() && obj != null && !UteisValidacao.emptyNumber(obj.getCodigo())){
        	vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Autor: Pedro Y. Saito
     * Criado em 18/01/2011
     */
    private void iniciarNovoLog(LogVO obj, LogVO objMontado) {
        String descricao = "";

        obj.setDescricao("");
        obj.setNomeCampo("");
        if (objMontado.getNomeCampo() != null && !"".equals(objMontado.getNomeCampo())) {
            obj.setNomeCampo("[" + objMontado.getNomeCampo().trim() + "]");
            obj.setValorCampoAnterior("");
            if (objMontado.getValorCampoAnterior() != null) {
                obj.setValorCampoAnterior("[" + objMontado.getNomeCampo().trim() + ": " + objMontado.getValorCampoAnterior().trim() + "]");
                if (!"".equals(objMontado.getValorCampoAnterior())) {
                    descricao = "'" + objMontado.getValorCampoAnterior().trim() + "'";
                } else {
                    descricao = "'Sem valor'";
                }
            }
            obj.setValorCampoAlterado("");
            if (objMontado.getValorCampoAlterado() != null) {
                obj.setValorCampoAlterado("[" + objMontado.getNomeCampo().trim() + ": " + objMontado.getValorCampoAlterado().trim() + "]");
                if ("".equals(objMontado.getValorCampoAlterado())) {
                    descricao = descricao + " para 'Sem valor'";
                }else{
                    descricao = descricao + " para '" + objMontado.getValorCampoAlterado().trim() + "'";
                }
            }

            if (!"".equals(descricao)) {
                obj.setDescricao("[" + objMontado.getNomeCampo().trim() + ": " + descricao + "]");
            }
        }
        if (!UteisValidacao.emptyString(objMontado.getOrigem())){
            obj.setOperacao(objMontado.getOperacao() + " realizado por " + objMontado.getResponsavelAlteracao().trim() + " no dia " + Uteis.getDataComHora(objMontado.getDataAlteracao()) + " Origem: " + objMontado.getOrigem());
        }else{
            obj.setOperacao(objMontado.getOperacao() + " realizado por " + objMontado.getResponsavelAlteracao().trim() + " no dia " + Uteis.getDataComHora(objMontado.getDataAlteracao()));
        }
        obj.setDataAlteracao(objMontado.getDataAlteracao());
        obj.setResponsavelAlteracao(objMontado.getResponsavelAlteracao());
        obj.setNomeEntidade(objMontado.getNomeEntidade());
        obj.setChavePrimaria(objMontado.getChavePrimaria());
        obj.setPessoa(objMontado.getPessoa());
        obj.setCodigo(objMontado.getCodigo());
    }

    @SuppressWarnings("unchecked")
    public List consultarPorNomeCodigoEntidade(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, Integer codigoPessoa, int nivelMontarDados) throws Exception {
        StringBuffer sqlSelect = new StringBuffer("SELECT * FROM Log WHERE ");

        String sqlStr = "";
        if (codigoEntidade > 0) {
            sqlStr = " nomeEntidade like ('%" + nomeEntidade + "%') and chavePrimaria = '" + codigoEntidade.intValue() + "'";
        }
        if (dataInicio != null) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr += " AND dataAlteracao >=  '" + Uteis.getDataJDBC(dataInicio) + "'";
            } else {
                sqlStr += " dataAlteracao >=  '" + Uteis.getDataJDBC(dataInicio) + "'";
            }
        }
        if (dataFim != null) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr += " AND dataAlteracao <=  '" + Uteis.getDataJDBC(dataFim) + "'";
            } else {
                sqlStr += " dataAlteracao <=  '" + Uteis.getDataJDBC(dataFim) + "'";
            }
        }
        if (codigoPessoa != null && codigoPessoa > 0) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr = "(" + sqlStr + " ) OR (pessoa =  " + codigoPessoa + ")";
            } else {
                sqlStr += " pessoa =  " + codigoPessoa;
            }
        }

        if (sqlStr != null && !"".equals(sqlStr)) {
            sqlSelect.append(sqlStr);
        }

        sqlSelect.append(" ORDER BY dataAlteracao desc");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlSelect.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorOpercao(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        // SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Log WHERE operacao like ('" + valorConsultar.toUpperCase() + "%') ORDER BY dataAlteracao desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorNomeEntidadeOperacaoPorDataAlteracao(String nomeEntidade, String nomeEntidadeDescricao, String operacao, int nivelMontarDados, boolean considerarValorCampoAnterior,
            String labelDescricao, Date dataInicio, String horarioInicial, Date dataFinal, String horarioFinal) throws Exception {
        String sqlStr = "SELECT * FROM Log WHERE operacao like ('" + operacao.toUpperCase() + "') "
                + "and nomeentidade ilike '" + nomeEntidade + "' "
                + "and nomeEntidadeDescricao ilike '" + nomeEntidadeDescricao + "' "
                + "and dataAlteracao between '" + Uteis.getDataJDBC(dataInicio) + " " + horarioInicial + "' and '" + Uteis.getDataJDBC(dataFinal) + " " + horarioFinal
                + "' ORDER BY dataAlteracao desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            LogVO obj = new LogVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            obj.setOperacao(obj.getOperacao() + " realizado por " + obj.getResponsavelAlteracao() + " no dia " + obj.getDataHoraAlteracao_Apresentar());
            if (considerarValorCampoAnterior) {
                obj.setDescricao("[" + labelDescricao + ":\"" + obj.getValorCampoAnterior() + "\" para \"" + obj.getValorCampoAlterado() + "\"]");
            } else {
                obj.setDescricao("[" + labelDescricao + ":" + obj.getValorCampoAlterado() + "]");
            }
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public List consultarPorResponsavel(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        // SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Log WHERE responsavelAlteracao like ('" + valorConsultar.toUpperCase() + "%') ORDER BY dataAlteracao desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorConteudoLog(String valorConsultar, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        // SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Log WHERE valorCampoAnterior like ('" + valorConsultar + "%') and valorCampoAlterado  like ('" + valorConsultar + "%') ORDER BY dataAlteracao desc";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public Boolean consultarAlteracaoCobrarMultaJurosAntesDaGeracaoDaRemessa(Date dataHoraRemessaGerada, int codEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT valorcampoalterado as cobrarAutomaticamenteMultaJuros  \n");
        sql.append("FROM log l \n");
        sql.append("WHERE nomeentidade = 'EMPRESA' \n");
        sql.append("AND dataalteracao < '").append(Uteis.getData(dataHoraRemessaGerada, "bdtimestamp")).append("' \n");
        sql.append("AND l.nomecampo = 'cobrarAutomaticamenteMultaJuros' \n");
        sql.append("AND chaveprimaria = '").append(codEmpresa).append("' \n");
        sql.append("ORDER BY codigo DESC LIMIT 1");

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        if (rs.next()) {
            String valor = rs.getString("cobrarAutomaticamenteMultaJuros");
            if (valor != null && valor.equals("Sim")) {
                return true;
            } else {
                return false;
            }
        }
        return null;
    }

    public Double consultarAlteracaoMultaAntesDaGeracaoDaRemessa(Date dataHoraRemessaGerada, int codEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT valorcampoalterado as multaCobrancaAutomatica \n");
        sql.append("FROM log l \n");
        sql.append("WHERE nomeentidade = 'EMPRESA' \n");
        sql.append("AND dataalteracao < '").append(Uteis.getData(dataHoraRemessaGerada, "bdtimestamp")).append("' \n");
        sql.append("and l.nomecampo = 'multaCobrancaAutomatica' \n");
        sql.append("AND chaveprimaria = '").append(codEmpresa).append("' \n");
        sql.append("ORDER BY codigo DESC LIMIT 1");

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        if (rs.next()) {
            return rs.getDouble("multaCobrancaAutomatica");
        }
        return null;
    }

    public Double consultarAlteracaoJurosAntesDaGeracaoDaRemessa(Date dataHoraRemessaGerada, int codEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT valorcampoalterado as jurosCobrancaAutomatica \n");
        sql.append("FROM log l \n");
        sql.append("WHERE nomeentidade = 'EMPRESA' \n");
        sql.append("AND dataalteracao < '").append(Uteis.getData(dataHoraRemessaGerada, "bdtimestamp")).append("' \n");
        sql.append("and l.nomecampo = 'jurosCobrancaAutomatica' \n");
        sql.append("AND chaveprimaria = '").append(codEmpresa).append("' \n");
        sql.append("ORDER BY codigo DESC LIMIT 1");

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        if (rs.next()) {
            return rs.getDouble("jurosCobrancaAutomatica");
        }
        return null;
    }

    public Boolean consultarAlteracaoMultaValorAbsolutoAntesDaGeracaoDaRemessa(Date dataHoraRemessaGerada, int codEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT valorcampoalterado as utilizarMultaValorAbsoluto  \n");
        sql.append("FROM log l \n");
        sql.append("WHERE nomeentidade = 'EMPRESA' \n");
        sql.append("AND dataalteracao < '").append(Uteis.getData(dataHoraRemessaGerada, "bdtimestamp")).append("' \n");
        sql.append("and l.nomecampo = 'utilizarMultaValorAbsoluto' \n");
        sql.append("AND chaveprimaria = '").append(codEmpresa).append("' \n");
        sql.append("ORDER BY codigo DESC LIMIT 1");

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        if (rs.next()) {
            String valor = rs.getString("utilizarMultaValorAbsoluto");
            if (valor != null && valor.equals("Sim")) {
                return true;
            } else {
                return false;
            }
        }
        return null;
    }

    public Boolean consultarAlteracaoJurosValorAbsolutoAntesDaGeracaoDaRemessa(Date dataHoraRemessaGerada, int codEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT valorcampoalterado as utilizarJurosValorAbsoluto  \n");
        sql.append("FROM log l \n");
        sql.append("WHERE nomeentidade = 'EMPRESA' \n");
        sql.append("AND dataalteracao < '").append(Uteis.getData(dataHoraRemessaGerada, "bdtimestamp")).append("' \n");
        sql.append("and l.nomecampo = 'utilizarJurosValorAbsoluto' \n");
        sql.append("AND chaveprimaria = '").append(codEmpresa).append("' \n");
        sql.append("ORDER BY codigo DESC LIMIT 1");

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        if (rs.next()) {
            String valor = rs.getString("utilizarJurosValorAbsoluto");
            if (valor != null && valor.equals("Sim")) {
                return true;
            } else {
                return false;
            }
        }
        return null;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>UsuarioVO</code> resultantes da consulta.
     */
    @SuppressWarnings("unchecked")
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList(0);
        while (tabelaResultado.next()) {
            vetResultado.add(montarDados(tabelaResultado, nivelMontarDados));
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>UsuarioVO</code>.
     * @return  O objeto da classe <code>UsuarioVO</code> com os dados devidamente montados.
     */
    public static LogVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        LogVO obj = new LogVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setNomeEntidade(dadosSQL.getString("nomeEntidade"));
        obj.setNomeEntidadeDescricao(dadosSQL.getString("nomeEntidadeDescricao"));
        obj.setChavePrimaria(dadosSQL.getString("chavePrimaria"));
        obj.setChavePrimariaEntidadeSubordinada(dadosSQL.getString("chavePrimariaEntidadeSubordinada"));
        obj.setNomeCampo(dadosSQL.getString("nomeCampo"));
        obj.setValorCampoAnterior(dadosSQL.getString("valorCampoAnterior"));
        obj.setValorCampoAlterado(dadosSQL.getString("valorCampoAlterado"));
        obj.setDataAlteracao(dadosSQL.getTimestamp("dataAlteracao"));
        obj.setResponsavelAlteracao(dadosSQL.getString("responsavelAlteracao"));
        obj.setOperacao(dadosSQL.getString("operacao"));
        obj.setPessoa(dadosSQL.getInt("pessoa"));
        obj.setNovoObj(new Boolean(false));
        obj.setCliente(dadosSQL.getInt("cliente"));

        try{
            obj.setClienteNome(dadosSQL.getString("nome"));
        }catch (Exception e){
            obj.setClienteNome("");
        }
        obj.setOrigem(dadosSQL.getString("origem"));
        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>UsuarioVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public LogVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        //  SuperEntidade.consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM log WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Usuario ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public String consultarJSON(String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar) throws Exception {
        String sqlCount = "select count(codigo) from log";

        StringBuilder sql = new StringBuilder("SELECT codigo, nomeentidade, operacao, nomecampo, dataalteracao, responsavelalteracao\n");
        sql.append("FROM log\n");
        sql.append(" WHERE 1 = 1\n");
        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append(" AND (");
            sql.append("lower(codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(nomeentidade) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(operacao) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(nomecampo) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(dataalteracao::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(responsavelalteracao) ~ '").append(clausulaLike).append("'\n");
            sql.append(")");
        }
        sql.append("  ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
        if (limit > 0) {
            sql.append(" limit ").append(limit).append("\n");
        }
        sql.append(" offset ").append(offset).append("\n");

        StringBuilder sqlContarFiltrados = new StringBuilder("SELECT count(codigo)\n");
        sqlContarFiltrados.append("FROM log\n");
        sqlContarFiltrados.append(" WHERE 1 = 1\n");
        if (!UteisValidacao.emptyString(clausulaLike)) {
            sqlContarFiltrados.append(" AND (");
            sqlContarFiltrados.append("lower(codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(nomeentidade) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(operacao) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(nomecampo) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(dataalteracao::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(responsavelalteracao) ~ '").append(clausulaLike).append("'\n");
            sqlContarFiltrados.append(")");
        }

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();

        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"iTotalRecords\":\"").append(contar(sqlCount, getCon())).append("\",");
        json.append("\"iTotalDisplayRecords\":\"").append(contar(sqlContarFiltrados.toString(), getCon())).append("\",");
        json.append("\"sEcho\":\"").append(sEcho).append("\",");
        json.append("\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("nomeentidade")).append("\",");
            json.append("\"").append(rs.getString("operacao")).append("\",");
            json.append("\"").append(rs.getString("nomecampo")).append("\",");
            json.append("\"").append(rs.getTimestamp("dataalteracao")).append("\",");
            json.append("\"").append(rs.getString("responsavelalteracao")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public Set<HistoricoParcelaOriginalTO> obterParcelasRenegociadasAPartirParcela(String codigoParcela, boolean somenteOriginais) throws Exception {
        Set<HistoricoParcelaOriginalTO> parcelas = new HashSet<>();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT ON (mp.codigo)\n");
        sql.append("  item->>'codigo' AS movParcelaAnterior,\n");
        sql.append("  item->>'descricao' AS descricaoAnterior,\n");
        sql.append("  item->>'valorParcela' AS valorAnterior,\n");
        sql.append("  item->>'dataVencimento' AS vencimentoAnterior,\n");
        sql.append("  mp.codigo as movParcelaAtual,\n");
        sql.append("  mp.valorParcela as valorParcelaAtual,\n");
        sql.append("  mp.datavencimento as dataVencimentoAtual,\n");
        sql.append("  mp.situacao as situacaoAtual\n");
        sql.append("FROM (\n");
        sql.append("  SELECT *\n");
        sql.append("  FROM movparcela\n");
        sql.append("  WHERE parcelasrenegociadas IS NOT NULL\n");
        sql.append("    AND parcelasrenegociadas LIKE '{%'\n");
        sql.append("    AND parcelasrenegociadas LIKE '%}'\n");
        sql.append("    AND EXISTS (\n");
        sql.append("      SELECT 1\n");
        sql.append("      FROM json_array_elements(parcelasrenegociadas::json -> 'listaMovParcelaRenegociar') AS x\n");
        sql.append("      WHERE x->>'codigo' = '").append(codigoParcela).append("'\n");
        sql.append("    )\n");
        sql.append(") AS mp,\n");
        sql.append("LATERAL json_array_elements(mp.parcelasrenegociadas::json -> 'listaMovParcelaRenegociar') AS item;\n");


        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                HashMap<String, String> movParcelaAnterior = new HashMap<>();
                MovParcela mpDAO = new MovParcela(con);
                while (rs.next()) {
                    if (somenteOriginais) {
                        if (!movParcelaAnterior.containsKey(rs.getString("movParcelaAnterior"))) {
                            movParcelaAnterior.put(rs.getString("movParcelaAnterior"), rs.getString("movParcelaAnterior"));
                            String idParcelaOrigemRenegociacao = obterMovParcelaAnteriorOrigemRenegociacaoPartirParcelaRenegociada(rs.getString("movParcelaAnterior"));
                            HistoricoParcelaOriginalTO parcela = new HistoricoParcelaOriginalTO();
                            MovParcelaVO mp = mpDAO.consultarPorCodigo(rs.getInt("movParcelaAnterior"), Uteis.NIVELMONTARDADOS_MINIMOS);
                            parcela.setId(rs.getString("movParcelaAnterior"));
                            parcela.setValor(rs.getDouble("valorAnterior"));
                            parcela.setVencimento(Calendario.getDate("dd/MM/yyyy", rs.getString("vencimentoAnterior")));
                            parcela.setDataOperacao(mp.getDataRegistro());
                            parcela.setSituacao(mp.getSituacao_Apresentar());
                            parcela.setOrigem(UteisValidacao.emptyString(idParcelaOrigemRenegociacao) ? "Original" : "Renegociada de " + idParcelaOrigemRenegociacao);
                            parcela.setIdRenegociada(idParcelaOrigemRenegociacao);
                            parcelas.add(parcela);

                        }
                    } else {
                        HistoricoParcelaOriginalTO parcela2 = new HistoricoParcelaOriginalTO();
                        MovParcelaVO mp = mpDAO.consultarPorCodigo(rs.getInt("movParcelaAtual"), Uteis.NIVELMONTARDADOS_MINIMOS);
                        parcela2.setId(rs.getString("movParcelaAtual"));
                        parcela2.setValor(rs.getDouble("valorParcelaAtual"));
                        parcela2.setVencimento(rs.getDate("dataVencimentoAtual"));
                        parcela2.setDataOperacao(mp.getDataRegistro());
                        parcela2.setSituacao(mp.getSituacao_Apresentar());
                        parcela2.setOrigem("Renegociada de " + rs.getString("movParcelaAnterior"));
                        parcelas.add(parcela2);
                    }
                }
                mpDAO = null;
                return parcelas;
            }
        }
    }

    public String obterMovParcelaAnteriorOrigemRenegociacaoPartirParcelaRenegociada(String codigoParcela) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT ON (mp.codigo)\n");
        sql.append("  item->>'codigo' AS movParcelaAnterior,\n");
        sql.append("  item->>'descricao' AS descricaoAnterior,\n");
        sql.append("  item->>'valorParcela' AS valorAnterior,\n");
        sql.append("  item->>'dataVencimento' AS vencimentoAnterior\n");
        sql.append("FROM movparcela mp,\n");
        sql.append("LATERAL json_array_elements(mp.parcelasrenegociadas::json -> 'listaMovParcelaRenegociar') AS item\n");
        sql.append("WHERE mp.codigo = ").append(codigoParcela).append("\n");
        sql.append(" AND mp.parcelasrenegociadas IS NOT NULL\n");
        sql.append(" AND parcelasrenegociadas LIKE '{%'\n");
        sql.append(" AND parcelasrenegociadas LIKE '%}'\n");
        sql.append("ORDER BY mp.codigo, item->>'codigo';");


        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getString("movParcelaAnterior");
                }
            }
        }
        return null;
    }

    public List<LogVO> consultarPorNomeEntidadeOperacaoPorDataAlteracao(String nomeEntidade, String operacao, String nomeEmpresa,
            java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim,
            List<ColaboradorVO> lista, int nivelMontarDados) throws Exception {
        int qtde = 0;
        String sqlStr = "SELECT log.*, pessoaCliente.nome " +
                "FROM" +
                "  log" +
                "  LEFT JOIN cliente ON cliente.codigo = log.cliente " +
                "  LEFT JOIN pessoa pessoaCliente ON pessoaCliente.codigo = cliente.pessoa " +
                "WHERE nomeentidade = '"+nomeEntidade+"' " +
                "      and operacao = '"+operacao+"' " +
                "      and dataalteracao " +
                "      between '"+ dataAlteracaoInicial + " 00:00:00' " +
                "      and '"+ dataAlteracaoFim + " 23:59:59' ";

        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr += " AND ( ";
                    qtde++;
                } else {
                    sqlStr += " OR ";
                }
                sqlStr += " (responsavelalteracao LIKE '" + co.getPessoa().getNome() + "%')\n";
            }
        }

        if (!nomeEmpresa.isEmpty()) {
            sqlStr += " and valorcampoalterado ilike '%Empresa = " + nomeEmpresa + "%'";
        }

        sqlStr += (qtde > 0 ? ")" : "");

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    public int contarPorNomeEntidadeOperacaoPorDataAlteracao(String nomeEntidade, String operacao, String nomeEmpresa,
            java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim, List<ColaboradorVO> lista,  int nivelMontarDados) throws Exception {
        int qtde = 0;
        String sqlStr = "SELECT count(codigo) as cod FROM log WHERE nomeentidade = '" + nomeEntidade + "' and operacao = '" + operacao +"' and dataalteracao between '" + dataAlteracaoInicial + " 00:00:00' and '"
                + dataAlteracaoFim + " 23:59:59'";
        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sqlStr += " AND ( ";
                    qtde++;
                } else {
                    sqlStr += " OR ";
                }
                sqlStr += " (responsavelalteracao LIKE '" + co.getPessoa().getNome() + "%')\n";
            }
        }

        if (!nomeEmpresa.isEmpty()) {
            sqlStr += " and valorcampoalterado like '%Empresa = " + nomeEmpresa + "%'";
        }

        sqlStr += (qtde > 0 ? ")" : "");

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt("cod");
    }

    public List<LogVO> consultarPorNomeentidadeNomecampoChaveprimariaOperacao(String nomeEntidade, String nomeCampo, String chavePrimaria, String operacao, int nivelMontarDados) throws Exception {
        StringBuilder sqlSelect = new StringBuilder();
        sqlSelect.append("SELECT * \n");
        sqlSelect.append("FROM log \n");
        sqlSelect.append("WHERE nomeentidade = '").append(nomeEntidade).append("'\n");
        sqlSelect.append("AND chaveprimaria = '").append(chavePrimaria).append("'\n");
        sqlSelect.append("and nomecampo = '").append(nomeCampo).append("'\n");
        sqlSelect.append("and operacao = '").append(operacao).append("'\n");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlSelect.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    public void incluirLogErroInclusaoContrato(Integer cliente, UsuarioVO usuarioAdmin, String erro) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(cliente.toString());
        obj.setNomeEntidade("CLIENTE");
        obj.setNomeEntidadeDescricao("Cliente");
        obj.setOperacao("ERRO INCLUSÃO CONTRATO");
        obj.setResponsavelAlteracao(usuarioAdmin.getNome());
        obj.setUserOAMD(usuarioAdmin.getUserOamd());
        obj.setNomeCampo("TODOS");
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado(erro);

        try {
            incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
    }

    public void incluirLogInclusaoClienteSite(ClienteVO clienteVO, UsuarioVO usuarioVO) throws Exception {
        try {
            clienteVO.setObjetoVOAntesAlteracao(clienteVO);

            LogVO log = new LogVO();
            log.setOperacao("INCLUSÃO");

            log.setChavePrimaria(clienteVO.getCodigo().toString());
            log.setNomeEntidade("CLIENTE");
            log.setNomeCampo("matricula");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(clienteVO.getMatricula());
            log.setPessoa(clienteVO.getPessoa().getCodigo());
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
            incluirSemCommit(log);

            log.setChavePrimaria(clienteVO.getCodigo().toString());
            log.setNomeEntidade("CLIENTE");
            log.setNomeCampo("empresa");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(clienteVO.getEmpresa_Apresentar());
            log.setPessoa(clienteVO.getPessoa().getCodigo());
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
            incluirSemCommit(log);

            log.setChavePrimaria(clienteVO.getCodigo().toString());
            log.setNomeEntidade("CLIENTE");
            log.setNomeCampo("situacao");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(clienteVO.getSituacao());
            log.setPessoa(clienteVO.getPessoa().getCodigo());
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
            incluirSemCommit(log);

            log.setChavePrimaria(clienteVO.getPessoa().getCodigo().toString());
            log.setNomeEntidade("PESSOA");
            log.setNomeCampo("nome");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(clienteVO.getPessoa().getNome());
            log.setPessoa(clienteVO.getPessoa().getCodigo());
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
            incluirSemCommit(log);

            log.setChavePrimaria(clienteVO.getPessoa().getCodigo().toString());
            log.setNomeEntidade("PESSOA");
            log.setNomeCampo("cpf");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(clienteVO.getPessoa().getCfp());
            log.setPessoa(clienteVO.getPessoa().getCodigo());
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
            incluirSemCommit(log);

            if (!UteisValidacao.emptyList(clienteVO.getPessoa().getEmailVOs())) {
                for (EmailVO email : clienteVO.getPessoa().getEmailVOs()) {
                    log.setChavePrimaria(email.getCodigo().toString());
                    log.setNomeEntidade("EMAIL");
                    log.setNomeCampo("e-mail");
                    log.setValorCampoAnterior("");
                    log.setValorCampoAlterado(email.getEmail());
                    log.setPessoa(clienteVO.getPessoa().getCodigo());
                    log.setResponsavelAlteracao(usuarioVO.getNome());
                    log.setUserOAMD(usuarioVO.getUserOamd());
                    log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                    incluirSemCommit(log);
                }
            }

        } catch (Exception ignored) {

            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade("CLIENTE");
            log.setPessoa(clienteVO.getPessoa().getCodigo());
            log.setValorCampoAnterior("ERRO AO GERAR LOG DE INCLUSÃO DE CLIENTE");
            log.setValorCampoAlterado("ERRO AO GERAR LOG DE INCLUSÃO DE CLIENTE");
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            incluirSemCommit(log);

        }

    }

    public void incluirLogEditarClienteSite(VendaDTO vendaDTO, PessoaVO pessoaAntesAlteracao, UsuarioVO usuarioVO) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setOperacao("ALTERAÇÃO");

            if (!pessoaAntesAlteracao.getNome().toUpperCase().equals(vendaDTO.getNome().toUpperCase())) {
                log.setChavePrimaria(pessoaAntesAlteracao.getCodigo().toString());
                log.setNomeEntidade("PESSOA");
                log.setNomeCampo("nome");
                log.setValorCampoAnterior(pessoaAntesAlteracao.getNome());
                log.setValorCampoAlterado(vendaDTO.getNome());
                log.setPessoa(pessoaAntesAlteracao.getCodigo());
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                incluirSemCommit(log);
            }

            if (!pessoaAntesAlteracao.getCfp().equals(vendaDTO.getCpf())) {
                log.setChavePrimaria(pessoaAntesAlteracao.getCodigo().toString());
                log.setNomeEntidade("PESSOA");
                log.setNomeCampo("cpf");
                log.setValorCampoAnterior(pessoaAntesAlteracao.getCfp());
                log.setValorCampoAlterado(vendaDTO.getCpf());
                log.setPessoa(pessoaAntesAlteracao.getCodigo());
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                incluirSemCommit(log);
            }

            if (!UteisValidacao.emptyList(pessoaAntesAlteracao.getEmailVOs())) {
                for (EmailVO email : pessoaAntesAlteracao.getEmailVOs()) {
                    if (!email.getEmail().toUpperCase().equals(vendaDTO.getEmail().toUpperCase())) {
                        log.setChavePrimaria(email.getCodigo().toString());
                        log.setNomeEntidade("EMAIL");
                        log.setNomeCampo("e-mail");
                        log.setValorCampoAnterior(email.getEmail());
                        log.setValorCampoAlterado(vendaDTO.getEmail());
                        log.setPessoa(pessoaAntesAlteracao.getCodigo());
                        log.setResponsavelAlteracao(usuarioVO.getNome());
                        log.setUserOAMD(usuarioVO.getUserOamd());
                        log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                        incluirSemCommit(log);
                    }
                }
            } else if (!UteisValidacao.emptyString(vendaDTO.getEmail())) {
                log.setChavePrimaria(pessoaAntesAlteracao.getCodigo().toString());
                log.setNomeEntidade("EMAIL");
                log.setNomeCampo("e-mail");
                log.setValorCampoAnterior("");
                log.setValorCampoAlterado(vendaDTO.getEmail());
                log.setPessoa(pessoaAntesAlteracao.getCodigo());
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                incluirSemCommit(log);

            }

            if (!pessoaAntesAlteracao.getRg().equals(vendaDTO.getRg())) {
                log.setChavePrimaria(pessoaAntesAlteracao.getCodigo().toString());
                log.setNomeEntidade("PESSOA");
                log.setNomeCampo("rg");
                log.setValorCampoAnterior(pessoaAntesAlteracao.getRg());
                log.setValorCampoAlterado(vendaDTO.getRg());
                log.setPessoa(pessoaAntesAlteracao.getCodigo());
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                incluirSemCommit(log);
            }

            if (!pessoaAntesAlteracao.getNomeMae().equals(vendaDTO.getResponsavelMae())) {
                log.setChavePrimaria(pessoaAntesAlteracao.getCodigo().toString());
                log.setNomeEntidade("PESSOA");
                log.setNomeCampo("nome_responsavel_mae");
                log.setValorCampoAnterior(pessoaAntesAlteracao.getNomeMae());
                log.setValorCampoAlterado(vendaDTO.getResponsavelMae());
                log.setPessoa(pessoaAntesAlteracao.getCodigo());
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                incluirSemCommit(log);
            }

            if (!pessoaAntesAlteracao.getNomePai().equals(vendaDTO.getResponsavelPai())) {
                log.setChavePrimaria(pessoaAntesAlteracao.getCodigo().toString());
                log.setNomeEntidade("PESSOA");
                log.setNomeCampo("nome_responsavel_pai");
                log.setValorCampoAnterior(pessoaAntesAlteracao.getNomePai());
                log.setValorCampoAlterado(vendaDTO.getResponsavelPai());
                log.setPessoa(pessoaAntesAlteracao.getCodigo());
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                incluirSemCommit(log);
            }

            if (!pessoaAntesAlteracao.getCpfMae().equals(vendaDTO.getCpfMae())) {
                log.setChavePrimaria(pessoaAntesAlteracao.getCodigo().toString());
                log.setNomeEntidade("PESSOA");
                log.setNomeCampo("cpf_responsavel_mae");
                log.setValorCampoAnterior(pessoaAntesAlteracao.getCpfMae());
                log.setValorCampoAlterado(vendaDTO.getCpfMae());
                log.setPessoa(pessoaAntesAlteracao.getCodigo());
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                incluirSemCommit(log);
            }

            if (!pessoaAntesAlteracao.getCpfPai().equals(vendaDTO.getCpfPai())) {
                log.setChavePrimaria(pessoaAntesAlteracao.getCodigo().toString());
                log.setNomeEntidade("PESSOA");
                log.setNomeCampo("cpf_responsavel_pai");
                log.setValorCampoAnterior(pessoaAntesAlteracao.getCpfPai());
                log.setValorCampoAlterado(vendaDTO.getCpfPai());
                log.setPessoa(pessoaAntesAlteracao.getCodigo());
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                incluirSemCommit(log);
            }

            if (!pessoaAntesAlteracao.getDataNasc_Apresentar().equals(vendaDTO.getDataNascimento())) {
                log.setChavePrimaria(pessoaAntesAlteracao.getCodigo().toString());
                log.setNomeEntidade("PESSOA");
                log.setNomeCampo("data_nascimento");
                log.setValorCampoAnterior(pessoaAntesAlteracao.getDataNasc_Apresentar());
                log.setValorCampoAlterado(vendaDTO.getDataNascimento());
                log.setPessoa(pessoaAntesAlteracao.getCodigo());
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
                incluirSemCommit(log);
            }

        } catch (Exception ignored) {

            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade("PESSOA");
            log.setPessoa(pessoaAntesAlteracao.getCodigo());
            log.setValorCampoAnterior("ERRO AO GERAR LOG DE INCLUSÃO DE CLIENTE");
            log.setValorCampoAlterado("ERRO AO GERAR LOG DE INCLUSÃO DE CLIENTE");
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setOrigem(OrigemCadEAlteracaoCliente.VENDAS_ONLINE.getDescricao());
            incluirSemCommit(log);

        }

    }

    public void incluirLogInclusaoClienteImportacao(Integer cliente, UsuarioVO usuarioAdmin) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(cliente.toString());
        obj.setNomeEntidade("CLIENTE");
        obj.setNomeEntidadeDescricao("Cliente");
        obj.setOperacao("INCLUSÃO DE CLIENTE IMPORTAÇÃO");
        obj.setResponsavelAlteracao(usuarioAdmin.getNome());
        obj.setUserOAMD(usuarioAdmin.getUserOamd());
        obj.setNomeCampo("TODOS");
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado("*Informados via processo Importação*");
        try {
            incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
    }

    public void incluirLogEstornoContratoSite(Integer cliente, UsuarioVO usuarioAdmin, String msg) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(cliente.toString());
        obj.setNomeEntidade("CLIENTE");
        obj.setNomeEntidadeDescricao("Cliente");
        obj.setOperacao("ESTORNO_COBRANCA_NAO_AUTORIZADA");
        obj.setResponsavelAlteracao(usuarioAdmin.getNome());
        obj.setUserOAMD(usuarioAdmin.getUserOamd());
        obj.setNomeCampo("TODOS");
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado(msg);

        try {
            incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
    }
    
     @SuppressWarnings("unchecked")
    public List consultarPorNomesEntidadeAgrupado(List<String> nomesEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, Integer codigoPessoa, int nivelMontarDados,  boolean agruparSegundo) throws Exception {

//    	//1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
//		confPaginacao.configurarNavegacao();
        //INICIO - MONTANDO CONSULTA============================================================================
        StringBuffer sqlSelect = new StringBuffer("SELECT * FROM Log ");

        String sqlStr = "";
        if (!nomesEntidade.isEmpty()) {
            sqlStr += "(";
            for(String nomeEntidade: nomesEntidade){ 
                if(!sqlStr.endsWith("(")){
                    sqlStr += " or ";
                }
                sqlStr += "nomeEntidade like ('" + nomeEntidade + "%')";
            }
            sqlStr += ")";
        }
        if (codigoEntidade > 0) {
            sqlStr += "and chavePrimaria = '" + codigoEntidade.intValue() + "'";
        }
        if (dataInicio != null) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr += " AND dataAlteracao >=  '" + Uteis.getDataHoraJDBC(dataInicio, "00:00:00") + "'";
            } else {
                sqlStr += " dataAlteracao >=  '" + Uteis.getDataHoraJDBC(dataInicio, "00:00:00") + "'";
            }
        }
        if (dataFim != null) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                sqlStr += " AND dataAlteracao <=  '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'";
            } else {
                sqlStr += " dataAlteracao <=  '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'";
            }
        }
        if (codigoPessoa != null && codigoPessoa != 0) {
            if (sqlStr != null && !"".equals(sqlStr)) {
                if (codigoEntidade > 0 && codigoPessoa != codigoEntidade) {
                    sqlStr = "(" + sqlStr + " and chavePrimaria::text::integer <> pessoa  ) or (pessoa =  " + codigoPessoa + ")";
                } else {
                    sqlStr = "(" + sqlStr + " ) and (pessoa =  " + codigoPessoa + ")";
                }
            } else {
                sqlStr += " pessoa =  " + codigoPessoa;
            }
        }
        if (sqlStr != null && !"".equals(sqlStr)) {
            sqlSelect.append(" WHERE " + sqlStr);
        }

//        sqlSelect.append(" ORDER BY operacao, nomeentidade, responsavelalteracao, dataAlteracao desc");
        sqlSelect.append(" ORDER BY dataAlteracao desc, codigo desc");
        //FIM - MONTANDO CONSULTA============================================================================

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlSelect.toString());
        return montarDadosAgrupado(tabelaResultado, nivelMontarDados, agruparSegundo);
    }

    private String vendasNaoRealizadasSQL(Integer empresa, Date dataInicial, Date dataFinal) {
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct nome, matricula, cliente, max (dataalteracao) as dataalteracao, mensagem from ( \n");
        sql.append("select  \n");
        sql.append("distinct \n");
        sql.append("p.nome,  \n");
        sql.append("cli.matricula,  \n");
        sql.append("cli.codigo as cliente, \n");
        sql.append("l.dataalteracao, \n");
        sql.append("l.valorcampoalterado as mensagem \n");
        sql.append("from cliente cli  \n");
        sql.append("inner join pessoa p on cli.pessoa = p.codigo  \n");
        sql.append("inner join log l on cast(l.chaveprimaria as integer) = cli.codigo  \n");
        sql.append("where operacao = 'ESTORNO_COBRANCA_NAO_AUTORIZADA'  \n");
        sql.append("and cli.situacao <> 'AT'  \n");
        sql.append("and l.dataalteracao::date between '").append(Uteis.getDataFormatoBD(dataInicial)).append("' and '").append(Uteis.getDataFormatoBD(dataFinal)).append("'  \n");
        sql.append("and cli.empresa = ").append(empresa).append(" \n");
        sql.append("order by dataAlteracao desc) as t \n");
        sql.append("group by t.nome, t.matricula, t.cliente, t.mensagem");
        return sql.toString();
    }

    public Integer vendasNaoRealizadasTotal(Integer empresa, Date dataInicial, Date dataFinal) throws Exception {
        String sql = vendasNaoRealizadasSQL(empresa, dataInicial, dataFinal);
        return SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
    }

    public List<ItemRelatorioTO> vendasNaoRealizadasLista(Integer empresa, Date dataInicial, Date dataFinal) throws Exception{
        List<ItemRelatorioTO> realizadas = new ArrayList<>();
        String sql = vendasNaoRealizadasSQL(empresa, dataInicial, dataFinal);
        try (ResultSet resultSet = criarConsulta(sql, con)) {
            while (resultSet.next()) {
                ItemRelatorioTO item = new ItemRelatorioTO();
                item.setMatricula(resultSet.getString("matricula"));
                item.setCliCodigo(resultSet.getInt("cliente"));
                item.setNome(resultSet.getString("nome"));
                item.setDataLancamento(resultSet.getTimestamp("dataalteracao"));
                item.setMensagemVendasOnline(resultSet.getString("mensagem"));
                realizadas.add(item);
            }
        }
        return realizadas;
    }

    public void registrarLogObjetoVO(SuperVO ObjetoVO, UsuarioVO usuarioVO,
                                     String chavePrimaria, String nomeEntidade,
                                     int codPessoa, boolean inclusao) throws Exception {
        List lista = ObjetoVO.gerarLogAlteracaoObjetoVO();
        for (Object aLista : lista) {
            LogVO log = (LogVO) aLista;

            if (inclusao) {
                log.setOperacao("INCLUSÃO");
            } else {
                log.setOperacao("ALTERAÇÃO");
            }

            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setChavePrimaria(chavePrimaria);
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            incluirSemCommit(log);
        }
    }

    public void incluirLogItemImportacao(String entidade, Integer chavePrimaria, UsuarioVO usuarioVO, Integer codigoPessoa) {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(chavePrimaria.toString());
            obj.setNomeEntidade(entidade.toUpperCase());
            obj.setNomeEntidadeDescricao(Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(entidade));
            obj.setOperacao("INCLUSÃO DE " + entidade.toUpperCase() + " IMPORTAÇÃO");
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setNomeCampo("TODOS");
            obj.setDataAlteracao(Calendario.hoje());
            if(UteisValidacao.notEmptyNumber(codigoPessoa)){
                obj.setPessoa(codigoPessoa);
            }
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado("*Informados via processo Importação*");
            incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
    }

    public void incluirLogCappta(String totem, String infoDados, String acao) {
        LogVO obj = new LogVO();
        obj.setNomeEntidade("LINX");
        obj.setNomeEntidadeDescricao("LINX");
        obj.setOperacao("LINX - " + acao);
        obj.setResponsavelAlteracao("Totem - " + totem);
        obj.setNomeCampo("TODOS");
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado(infoDados);

        try {
            incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
    }

    public void incluirLogGenerico(LogGenericoTipoEnum tipoEnum, UsuarioVO usuarioVO, String info) {
        try {
            String sql = "INSERT INTO loggenerico(dataregistro, tipo, usuario, info) VALUES (?,?,?,?)";
            PreparedStatement ps = con.prepareStatement(sql);
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(++i, tipoEnum.getId());
            resolveIntegerNull(ps, ++i, (usuarioVO != null ? usuarioVO.getCodigo() : 0));
            ps.setString(++i, info);
            ps.execute();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void incluirLogProcessarExclusaoContasPagarRecebrer(UsuarioVO usuario, String infoDados) {
        LogVO obj = new LogVO();
        obj.setNomeEntidade("MOVCONTA");
        obj.setNomeEntidadeDescricao("MOVCONTA");
        obj.setOperacao("EXCLUIR_MOVCONTAS_PROCESSO_MANUAL");
        obj.setResponsavelAlteracao(usuario.getNome());
        obj.setNomeCampo("TODOS");
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado(infoDados);

        try {
            incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
    }

}
