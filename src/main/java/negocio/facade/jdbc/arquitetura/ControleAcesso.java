package negocio.facade.jdbc.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.arquitetura.UsuarioNFeVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.*;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;

import static org.apache.commons.lang3.StringUtils.isBlank;

/**
 * SuperClasse padrão para a classe <code>SuperEntidade</code>. Responsável por
 * implementar características comuns relativas ao controle de acesso e a
 * conexão com o banco de dados.
 */
public class ControleAcesso extends SuperFacadeJDBC {

    /**
     * Usuario logado no sistema, para qual deve ser verificado as permissões de
     * acesso.
     */
    protected static UsuarioVO usuario = null;
    /**
     * PerfilAcesso objeto que contém as regras de permissão de acesso do
     * usuário, para funcionalidade da aplicação.
     */
    private static PerfilAcessoVO perfilAcesso = null;

    protected static UsuarioVO usuarioLogadoControleAcesso = null;

    /**
     * Creates a new instance of Funcionalidade
     */
    public ControleAcesso() {
    }

    public ControleAcesso(HttpSession session) throws Exception {
        super(session);
    }

    public ControleAcesso(Connection conexao) throws Exception {
        super(conexao);
    }

    protected static HttpServletRequest request() {
        return (HttpServletRequest) context().getExternalContext().getRequest();
    }

    protected static FacesContext context() {
        return (FacesContext.getCurrentInstance());
    }

    /**
     * Operação padrão para realizar o INCLUIR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação INCLUIR.
     *
     * @param idEntidade Nome da entidade para a qual se deseja realizar a operação.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void incluir(String idEntidade) throws Exception {
        super.incluir(idEntidade);
        if (!verificarPermissaoUsuario(idEntidade, ConstantesAcesso.INCLUIR)) {
            String nomeUsuario = "";
            if (obterUsuarioLogado() != null) {
                nomeUsuario = obterUsuarioLogado().getNome().toUpperCase();
                if (obterUsuarioLogado().getAdministrador().booleanValue()) {
                    obterLogControleUsabilidade("Incluir", idEntidade);
                    return;
                }
            }

            OpcoesPerfilAcesso opcoesPerfilAcesso = new OpcoesPerfilAcesso();
            String permissao = opcoesPerfilAcesso.getPermissaoPorEntidade(idEntidade);

            perfilAcesso = obterPerfilAcessoUsuarioLogado();
            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"INCLUIR " + permissao.toUpperCase() + "\"";
            throw (new AcessoException(msgErro));
        }
        obterLogControleUsabilidade("Incluir", idEntidade);
    }

    /**
     * Método incluir sem a verificação de autorização
     *
     * @param idEntidade
     * @throws Exception
     * @see negocio.facade.jdbc.arquitetura.ControleAcesso#incluirObj(java.lang.String)
     */
    public void incluirObj(String idEntidade) throws Exception {
        super.incluir(idEntidade);
        // obterLogControleUsabilidade("Incluir", idEntidade);
    }

    /**
     * Operação padrão para realizar o ALTERAR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação ALTERAR.
     *
     * @param idEntidade Nome da entidade para a qual se deseja realizar a operação.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void alterar(String idEntidade) throws Exception {
        super.alterar(idEntidade);
        if (!verificarPermissaoUsuario(idEntidade, ConstantesAcesso.ALTERAR)) {
            String nomeUsuario = "";
            if (obterUsuarioLogado() != null) {
                nomeUsuario = obterUsuarioLogado().getNome().toUpperCase();
                if (obterUsuarioLogado().getAdministrador().booleanValue()) {
                    obterLogControleUsabilidade("Alterar", idEntidade);
                    return;
                }
            }
            OpcoesPerfilAcesso opcoesPerfilAcesso = new OpcoesPerfilAcesso();
            String permissao = opcoesPerfilAcesso.getPermissaoPorEntidade(idEntidade);

            perfilAcesso = obterPerfilAcessoUsuarioLogado();
            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"ALTERAR " + permissao.toUpperCase() + "\"";
            throw (new AcessoException(msgErro));
        }
        obterLogControleUsabilidade("Alterar", idEntidade);
    }

    public void alterarObj(String idEntidade) throws Exception {
        super.alterar(idEntidade);
        obterLogControleUsabilidade("Alterar", idEntidade);
    }

    /**
     * Operação padrão para realizar o EXCLUIR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação EXCLUIR.
     *
     * @param idEntidade Nome da entidade para a qual se deseja realizar a operação.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void excluir(String idEntidade) throws Exception {
        super.excluir(idEntidade);
        if (!verificarPermissaoUsuario(idEntidade, ConstantesAcesso.EXCLUIR)) {
            String nomeUsuario = "";
            if (obterUsuarioLogado() != null) {
                nomeUsuario = obterUsuarioLogado().getNome().toUpperCase();
                if (obterUsuarioLogado().getAdministrador().booleanValue()) {
                    obterLogControleUsabilidade("Excluir", idEntidade);
                    return;
                }
            }
            perfilAcesso = obterPerfilAcessoUsuarioLogado();
            OpcoesPerfilAcesso opcoesPerfilAcesso = new OpcoesPerfilAcesso();
            String permissao = opcoesPerfilAcesso.getPermissaoPorEntidade(idEntidade);

            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"EXCLUIR " + permissao.toUpperCase() + "\"";
            throw (new AcessoException(msgErro));
        }
        obterLogControleUsabilidade("Excluir", idEntidade);
    }

    /**
     * Responsável por
     *
     * @param idEntidade
     * @throws Exception
     * <AUTHOR> 22/03/2011
     */
    public void excluirObj(String idEntidade) throws Exception {
        super.excluir(idEntidade);
        obterLogControleUsabilidade("Excluir", idEntidade);
    }

    /**
     * Operação padrão para realizar o CONSULTAR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação CONSULTAR.
     *
     * @param idEntidade Nome da entidade para a qual se deseja realizar a operação.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void consultar(String idEntidade, boolean verificarAcesso)
            throws Exception {
        super.consultar(idEntidade);
        if (!verificarAcesso) {
            return;
        }
        if (!verificarPermissaoUsuario(idEntidade, ConstantesAcesso.CONSULTAR)) {
            String nomeUsuario = "";
            if (obterUsuarioLogado() != null) {
                nomeUsuario = obterUsuarioLogado().getNome().toUpperCase();
                if (obterUsuarioLogado().getAdministrador().booleanValue()) {
                    return;
                }
            }
            perfilAcesso = obterPerfilAcessoUsuarioLogado();
            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"CONSULTAR " + idEntidade.toUpperCase() + "\"";
            throw (new AcessoException(msgErro));
        }
    }

    public void consultarObj(String idEntidade) throws Exception {
        super.consultar(idEntidade);

    }

    public static void obterLogControleUsabilidade(String acao, String entidade)
            throws Exception {
        /*TODO: Removido LogControleUsabilidade
        if (FacesContext.getCurrentInstance() != null) {
            if (!entidade.equals("LogControleUsabilidade")) {
                LogControleUsabilidadeVO obj = new LogControleUsabilidadeVO();
                obj.setUsuario(obterUsuarioLogado());
                obj.setEmpresa(obterEmpresaLogado());
                obj.setAcao(acao);
                obj.setEntidade(entidade);
                String ip = (String) JSFUtilities.getFromSession("ip");
                if (ip == null || ip.isEmpty()) {
                    ip = "(Sem IP - Problema de acesso a Internet no cliente)";
                }
                obj.setMaquina(ip);
                obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
                obj.setChavePrimaria(0);
                getFacade().getLogControleUsabilidade().incluirSemCommit(obj);
            }
        }
        */
    }

    // public static void pegarIp() throws UnknownHostException {
    // try {
    // InetAddress end = InetAddress.getLocalHost();
    //
    // } catch (UnknownHostException uhex) {
    // }
    //
    // }
    // public static String pegarIp() {
    // HttpServletRequest request = (HttpServletRequest)
    // FacesContext.getCurrentInstance().getExternalContext().getRequest();
    // String ip = request.getRemoteAddr();
    // return ip;
    // }
    /**
     * Operação padrão para realizar o EMITIR UM RELATÓRIO de dados de uma
     * entidade no BD. Verifica e inicializa (se necessário) a conexão com o BD.
     * Verifica se o usuário logado possui permissão de acesso a operação
     * EMITIRRELATORIO.
     *
     * @param idEntidade Nome da entidade para a qual se deseja realizar a operação.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void emitirRelatorio(String idEntidade, boolean verificarAcesso)
            throws Exception {
        super.emitirRelatorio(idEntidade);
        if (!verificarAcesso) {
            obterLogControleUsabilidade("Emitir Relatório", idEntidade);
            return;
        }
        if (obterUsuarioLogado().getAdministrador().booleanValue()) {
            obterLogControleUsabilidade("Emitir Relatório", idEntidade);
            return;
        }
        if (!verificarPermissaoUsuario(idEntidade, ConstantesAcesso.EMITIRRELATORIO)) {
            String nomeUsuario = "";
            if (obterUsuarioLogado() != null) {
                nomeUsuario = obterUsuarioLogado().getNome().toUpperCase();
            }
            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"EMITIR RELATÓRIO " + idEntidade.toUpperCase() + "\"";
            throw (new AcessoException(msgErro));
        }
        obterLogControleUsabilidade("Emitir Relatório", idEntidade);
    }

    /**
     * Operação padrão para verificar acesso do usuário a determinada
     * funcionalidade registrada no Perfil de Acesso do Usuário. Pode ser, por
     * exemplo, alterar um valor de um campo. Neste caso deverá existir no
     * PerfilAcesso do usuário o key (apelido) identificando esta
     * funcionalidade. Esta funcinalidade deverá estar gravada com a opção TOTAL
     * ou ALTERAR para garantir que o usuário tenha acesso à mesma.
     *
     * @param funcionalidade Nome da funcionalidade para a qual se deseja realizar a
     *                       verificação de permissão do usuário.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void verificarPermissaoUsuarioFuncionalidade(String funcionalidade,
            String textoFuncionalidade) throws Exception {
        super.verificarPermissaoUsuarioFuncionalidade(funcionalidade);
        if (!verificarPermissaoUsuario(funcionalidade, ConstantesAcesso.ALTERAR)) {
            String nomeUsuario = "";
            if (obterUsuarioLogado() != null) {
                nomeUsuario = obterUsuarioLogado().getNome().toUpperCase();
            }
            if (obterUsuarioLogado().getAdministrador().booleanValue()) {
                return;
            }
            perfilAcesso = obterPerfilAcessoUsuarioLogado();
            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"" + textoFuncionalidade.toUpperCase() + "\"";
            throw (new AcessoException(msgErro));
        }
    }

    public Boolean verificarPermissaoFuncionalidade(String funcionalidade) throws Exception {
        return verificarPermissaoFuncionalidade(funcionalidade, null);
    }

    public Boolean verificarPermissaoFuncionalidade(String funcionalidade, UsuarioVO usuario) throws Exception {
        return verificarPermissaoFuncionalidade(funcionalidade, usuario ,ConstantesAcesso.ALTERAR);
    }
    public Boolean verificarPermissaoFuncionalidade(String funcionalidade, UsuarioVO usuario ,int constantesAcesso) throws Exception {
        super.verificarPermissaoUsuarioFuncionalidade(funcionalidade);
        UsuarioVO usuarioLogado = usuario == null ? obterUsuarioLogado() : usuario;
        return usuarioLogado.getAdministrador() || verificarPermissaoUsuario(funcionalidade, constantesAcesso);
    }

    /**
     * Operação padrão para verificar acesso do usuário a determinada
     * funcionalidade registrada no Perfil de Acesso do Usuário. Pode ser, por
     * exemplo, alterar um valor de um campo. Neste caso deverá existir no
     * PerfilAcesso do usuário o key (apelido) identificando esta
     * funcionalidade. Esta funcinalidade deverá estar gravada com a opção TOTAL
     * ou ALTERAR para garantir que o usuário tenha acesso à mesma.
     *
     * @param funcionalidade Nome da funcionalidade para a qual se deseja realizar a
     *                       verificação de permissão do usuário.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void verificarPermissaoUsuarioFuncionalidade(
            PerfilAcessoVO perfilAcesso, UsuarioVO usuario,
            String funcionalidade, String textoFuncionalidade) throws Exception {
        verificarPermissaoUsuarioFuncionalidade(perfilAcesso, usuario, funcionalidade, textoFuncionalidade, null);
    }
    public void verificarPermissaoUsuarioFuncionalidade(
            PerfilAcessoVO perfilAcesso, UsuarioVO usuario,
            String funcionalidade, String textoFuncionalidade, String operacaoString) throws Exception {
        super.verificarPermissaoUsuarioFuncionalidade(funcionalidade);

        if (funcionalidade.equals("")) {
            throw (new AcessoException(
                    "O nome da Ação verificada está em Branco."));
        }
        if ((usuario == null) || (usuario.getUsername().equalsIgnoreCase(""))) {
            throw (new AcessoException(
                    "O USUÁRIO a ser verificado está em Branco "));
        }
        if (perfilAcesso == null) {
            throw (new AcessoException(
                    "O Perfil Acesso do usuário a ser verificado está em Branco"));
        }
        boolean permitir = verificarPermissaoOperacao(perfilAcesso.getPermissaoVOs(), funcionalidade, ConstantesAcesso.INCLUIR, ConstantesAcesso.getPermissaoValueString(operacaoString));
        if (funcionalidade.equals("LancamentoCarenciaRetroativa") && !permitir){
            String nomeUsuario = "";
            nomeUsuario = usuario.getNome().toUpperCase();
            if (usuario.getAdministrador()) {
                return;
            }
            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"" + textoFuncionalidade.toUpperCase() + "\"";

            throw (new AcessoException(msgErro));
        }
        if (!permitir) {
            String nomeUsuario = "";
            if (usuario != null) {
                nomeUsuario = usuario.getNome().toUpperCase();
            }
            if (usuario.getAdministrador().booleanValue()) {
                return;
            }
            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"" + textoFuncionalidade.toUpperCase() + "\"";

            if (funcionalidade.equals("LiberarTodosColaboradoresRelatorioFechamentoCaixaOperador")) {
                msgErro = "Para gerar o relatório escolha seu nome de operador ou inclua a permissão LIBERAR RELATÓRIO DE FECHAMENTO DE CAIXA POR OPERADOR - TODOS OS COLABORADORES";
            }
            throw (new AcessoException(msgErro));
        }
    }

    /**
     * Operação de responsável por verificar se uma operação está definida
     * dentro de um objeto de <code>PermissaoVO</code>. Caso o codigo da
     * operação (parâmetro <code>int operacao</code>) exista no atributo
     * <code>permissoes</code> de <code>PermissaoVO</code> significa que existe
     * acesso liberado para esta operação. O atributo permissoes assume valores
     * do tipo "(1)(2)(4)", ou seja, os códigos da operações pertinentes
     * cercados por parênteses.
     *
     * @param permissaoVO objeto contendo as permissões para uma determinada entidade,
     *                    de um perfil de acesso específico.
     * @param operacao    código de uma operação para qual se deseja realizar esta
     *                    verificação.
     * @return boolean true caso exista liberação para uso da operação neste
     *         perfil de acesso.
     */
    private static boolean verificarPermissaoOperacao(PermissaoVO permissaoVO,
            int operacao, String operacaoConcat) {
        String operStr = "(" + operacao + ")";
        if (!isBlank(operacaoConcat)) {
            operStr = operacaoConcat;
        }
        if (permissaoVO == null) {
            return false;
        }
        int resultado = permissaoVO.getPermissoes().indexOf(operStr);
        if (resultado == -1) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * Operação responsável por verificar se uma operação está definida dentro
     * de um conjunto de <code>PermissaoVO</code>, mantidos em um List. Isto
     * para uma determinada entidade. Com base no nome da entidade (key do List)
     * é possível obter o objeto da classe <code>PermissaoVO</code> pertinente a
     * esta entidade. Para que posteriormente seja verificado o acesso a
     * operação através da operação
     * </code>verificarPermissaoOperacao(PermissaoVO permissao, int
     * operacao)</code>.
     *
     * @param permissoes   Todos os objetos PermissaoVO do perfil de acesso em questão.
     * @param nomeEntidade Código de uma operação para qual se deseja realizar esta
     *                     verificação.
     * @param operacao     Código de uma operação para qual se deseja realizar esta
     *                     verificação.
     * @return boolean true caso exista liberação para uso da operação neste
     *         perfil de acesso.
     */
    private boolean verificarPermissaoOperacao(List permissoes,
            String nomeEntidade, int operacao, String operacaoConcat) {
        Iterator i = permissoes.iterator();
        while (i.hasNext()) {
            PermissaoVO obj = (PermissaoVO) i.next();
            if (obj.getNomeEntidade().equals(nomeEntidade)) {
                return obj.getTipoPermissao().equals(OpcaoPerfilAcesso.TP_ENTIDADE) ? verificarPermissaoOperacao(obj, operacao, operacaoConcat) : true;
            }
        }
        return false;
    }

    private static String getOperacaoStr(int codOper) {
        return "(" + codOper + ")";
    }

    /**
     * Operação responsável por verificar se um usuário existe (através de ser
     * Username e Senha) e retornar os dados deste usuário para registro na
     * sessão da aplicação. Caso o usuário não exista é retornado um Exception.
     * da operação </code>verificarPermissaoOperacao(PermissaoVO permissao, int
     * operacao)</code>.
     *
     * @param username Username do usuário.
     * @param senha    Senha do usuário.
     * @return UsuarioVO Dados do usuário localizado.
     * @throws Exception Erro alertando que o usuário não existe.
     */
    public UsuarioVO verificarLoginUsuario(String username, String senha,
            Boolean webService) throws Exception {

        return this.verificarLoginUsuarioInner(username, senha, webService,false);
    }

    public UsuarioVO verificarLoginUsuarioApp(String username, String senha,
            Boolean webService) throws Exception {

        return this.verificarLoginUsuarioInner(username, senha, webService,true);
    }

    /**
     * Oferece o mesmo comportamento de verificarLoginUsuarioInner já setando um
     * contexto de não usar WS
     *
     * @param username
     * @param senha
     * @return
     * @throws Exception
     */
    public UsuarioVO verificarLoginUsuario(String username, String senha)
            throws Exception {
        return this.verificarLoginUsuarioInner(username, senha, false,false);
    }

    /**
     * Operação responsável por verificar se um usuário existe (através de ser
     * Username e Senha) e retornar os dados deste usuário para registro na
     * sessão da aplicação. Caso o usuário não exista é retornado um Exception.
     * da operação </code>verificarPermissaoOperacao(PermissaoVO permissao, int
     * operacao)</code>.
     *
     * @param username   Username do usuário.
     * @param senha      Senha do usuário.
     * @param webService Se está em contexto de WS ou não
     * @return
     * @throws Exception
     */
    private UsuarioVO verificarLoginUsuarioInner(String username, String senha,
                                                 Boolean webService, Boolean logarComEmail) throws Exception {

        username = Uteis.removerEspacosInicioFimString(username); //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
        senha = Uteis.removerEspacosInicioFimString(senha); //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!

        if (!webService) {
            // em cenário de webservice não executa o método inicializar
            inicializar();
        }

        boolean isNFe = false;
        if (JSFUtilities.isJSFContext()) {
            LoginControle login = (LoginControle) JSFUtilities.getManagedBean(LoginControle.class.getSimpleName());
            isNFe = login.isNFe();
        }
        if (!isNFe) {
            UsuarioVO usu = executarLoginSeguro(username, senha, logarComEmail, webService);

            if (usu.getColaboradorVO() != null && usu.getColaboradorVO().getSituacao().equals("NA")) {
                throw new ConsistirException("Usuário desativado!");
            }

            // Verificar telefone duplicado com try-with-resources
            try (PreparedStatement stmTel = con.prepareStatement("select t.numero from telefone t " +
                    "where numero in (select numero from telefone where pessoa = ?) " +
                    "and t.pessoa <> ?")) {
                stmTel.setInt(1, usu.getColaboradorVO().getPessoa().getCodigo());
                stmTel.setInt(2, usu.getColaboradorVO().getPessoa().getCodigo());
                try (ResultSet tabelaResultado = stmTel.executeQuery()) {
                    if (tabelaResultado.next()) {
                        if(tabelaResultado.getString("numero").replaceAll("[()]", "").equals(username.replaceAll("[()]", ""))) {
                            throw new ConsistirException("Telefone cadastrado para mais de um usuário. Favor cadastrar outro número");
                        }
                    }
                }
            }

            usuarioLogadoControleAcesso = usu;
            return usu;
        } else {
            return realizarLoginNFe(username, senha, this.con);
        }
    }

    public UsuarioVO verificarLoginUsuarioInnerV4(Integer usuario) throws Exception {
        PreparedStatement stm = con.prepareStatement("select * from usuario where codigo = ?");
        stm.setInt(1, usuario);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Usuário e Senha não Confere.");
        }
        // caso esteja usando web service somente os dados básicos + usuarioPerfilAcesso serão
        // montados para evitar consultas aninhadas
        UsuarioVO usu = Usuario.montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_VALIDACAOACESSO, this.con);
        if (usu.getColaboradorVO() != null && usu.getColaboradorVO().getSituacao().equals("NA")) {
            throw new ConsistirException("Usuário desativado!");
        }
        return usu;
    }

    /**
     * Método seguro para executar login com PreparedStatement e binding correto de parâmetros
     */
    private UsuarioVO executarLoginSeguro(String username, String senha, Boolean logarComEmail, boolean webService) throws Exception {
        // VALIDAÇÃO DE SEGURANÇA: Verificar se username é seguro
        if (!Uteis.isValidStringValue(username)) {
            throw new SecurityException("Username contém caracteres não permitidos");
        }

        String senhaEcriptadaUpperCase = Uteis.encriptar(senha.toUpperCase());
        String senhaEcriptadaSemUpperCase = Uteis.encriptar(senha);

        if (logarComEmail) {
            return executarLoginComEmail(username, senhaEcriptadaUpperCase, senhaEcriptadaSemUpperCase, webService);
        } else {
            if (JSFUtilities.isJSFContext() && JSFUtilities.getRequest().getAttribute("pseudoUser") != null) {
                return executarLoginPseudoUser(username, webService);
            } else {
                return executarLoginCompleto(username, senha, senhaEcriptadaUpperCase, senhaEcriptadaSemUpperCase, webService);
            }
        }
    }

    private UsuarioVO executarLoginComEmail(String username, String senhaEcriptadaUpperCase, String senhaEcriptadaSemUpperCase, boolean webService) throws Exception {
        String sql = "SELECT * FROM Usuario u " +
                    "LEFT JOIN UsuarioEmail ue ON ue.usuario = u.codigo " +
                    "WHERE ( upper(u.username) = ? OR upper(ue.email) = ?) " +
                    "AND u.senha in (?, ?) ";

        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setString(1, username.toUpperCase());
            stm.setString(2, username.toUpperCase());
            stm.setString(3, senhaEcriptadaUpperCase);
            stm.setString(4, senhaEcriptadaSemUpperCase);

            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Usuário e Senha não Confere.");
                }
                return Usuario.montarDados(tabelaResultado,
                        webService ? Uteis.NIVELMONTARDADOS_VALIDACAOACESSO : Uteis.NIVELMONTARDADOS_LOGIN, this.con);
            }
        }
    }

    private UsuarioVO executarLoginPseudoUser(String username, boolean webService) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT * FROM Usuario WHERE upper(username) = ? ");
        boolean temPseudoUserCode = JSFUtilities.getRequest().getAttribute("pseudoUserCode") != null;
        if (temPseudoUserCode) {
            sql.append(" and codigo = ? ");
        }

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setString(1, username.toUpperCase());
            if (temPseudoUserCode) {
                stm.setInt(2, (Integer) JSFUtilities.getRequest().getAttribute("pseudoUserCode"));
            }

            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Usuário e Senha não Confere.");
                }
                return Usuario.montarDados(tabelaResultado,
                        webService ? Uteis.NIVELMONTARDADOS_VALIDACAOACESSO : Uteis.NIVELMONTARDADOS_LOGIN, this.con);
            }
        }
    }

    private UsuarioVO executarLoginCompleto(String username, String senha, String senhaEcriptadaUpperCase, String senhaEcriptadaSemUpperCase, boolean webService) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select u.*, t.numero as telefone ");
        sql.append("from usuario u ");
        sql.append("left join colaborador c on c.codigo = u.colaborador ");
        sql.append("left join pessoa p on p.codigo = c.pessoa ");
        sql.append("left join telefone t on t.pessoa = p.codigo ");
        sql.append("left join usuarioemail ue on ue.usuario = u.codigo ");
        sql.append("where u.senha in (?, ?");

        boolean temSenhaCriptografada = !JSFUtilities.isJSFContext() && senha.length() >= 64;
        if (temSenhaCriptografada) {
            sql.append(", ?");
        }
        sql.append(") and (upper(u.username) = ? ");

        String cpf = Uteis.tirarCaracteres(username, true);
        boolean temCpf = cpf != null && cpf.length() == 11;
        if (temCpf) {
            sql.append("OR replace(replace(p.cfp, '.', ''), '-', '') = ? ");
        }

        String telefone = Uteis.tirarCaracteres(username, true);
        boolean temTelefone = telefone != null && telefone.length() >= 10;
        if (temTelefone) {
            sql.append("OR replace(replace(replace(t.numero, '(', ''), ')', ''), '-', '') = ? ");
        }

        boolean temEmail = username.contains("@");
        if (temEmail) {
            sql.append("OR upper(ue.email) = ? ");
        }
        sql.append(") ");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            int paramIndex = 1;
            stm.setString(paramIndex++, senhaEcriptadaUpperCase);
            stm.setString(paramIndex++, senhaEcriptadaSemUpperCase);
            if (temSenhaCriptografada) {
                stm.setString(paramIndex++, senha);
            }
            stm.setString(paramIndex++, username.toUpperCase());
            if (temCpf) {
                stm.setString(paramIndex++, cpf);
            }
            if (temTelefone) {
                stm.setString(paramIndex++, telefone);
            }
            if (temEmail) {
                stm.setString(paramIndex++, username.toUpperCase());
            }

            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Usuário e Senha não Confere.");
                }
                return Usuario.montarDados(tabelaResultado,
                        webService ? Uteis.NIVELMONTARDADOS_VALIDACAOACESSO : Uteis.NIVELMONTARDADOS_LOGIN, this.con);
            }
        }
    }

    /**
     * @deprecated Use executarLoginSeguro() instead. Este método não faz binding de parâmetros corretamente.
     */
    @Deprecated
    public String obterSQLLogin(String username, String senha, Boolean logarComEmail) throws UnsupportedEncodingException {
        // Método mantido apenas para compatibilidade, mas marcado como deprecated
        // TODO: Remover após migração completa para executarLoginSeguro()
        throw new UnsupportedOperationException("Método deprecated. Use executarLoginSeguro() instead.");
    }

    public UsuarioVO realizarLoginNFe(String usuario, String senha, Connection con) throws Exception {
        String codigoEmpresa = (String) JSFUtilities.getFromSession("key");

        PreparedStatement stm = selecionarUsuarioNFe(usuario, codigoEmpresa);

        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Usuário e Senha não Confere.");
        }

        UsuarioNFeVO usu = UsuarioNFe.montarDados(tabelaResultado, con);
        if (!senha.equals(Uteis.desencriptarNFe(usu.getSenha()).toUpperCase())) {
            throw new ConsistirException("Usuário e Senha não Confere.");
        }

        if (!usu.isAdministrador()) {
            if (!usu.getStatus()) {
                throw new ConsistirException("Usuário inativo.");
            }

            if (!usu.getPerfilUsuarioNFe().getEmpresa().isAtiva()) {
                throw new ConsistirException("Empresa inativa.");
            }
        }

        return usu;
    }

    /**
     * Cria o SQL de consulta a tabela de usuários por senha e login caso seja NFe
     *
     * @return PreparedStatement
     * @throws SQLException
     * <AUTHOR>
     */
    private PreparedStatement selecionarUsuarioNFe(String usuario, String chave) throws SQLException {
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM Usuario u ");
        sqlStr.append("LEFT JOIN PerfilUsuario pu ON u.Id_PerfilUsuario = pu.Id_PerfilUsuario ");
        sqlStr.append("LEFT JOIN Empresa e ON pu.Id_Empresa = e.Id_Empresa ");
        sqlStr.append("WHERE u.Usuario = '").append(usuario).append("' ");

        if (!usuario.equalsIgnoreCase("admin")) {
            sqlStr.append("AND e.Chave LIKE '").append(chave).append("'");
        }

        return con.prepareStatement(sqlStr.toString());
    }

    /**
     * Metodo que obtem o numero de dias para bloqueio do sistema
     * <p/>
     * Autor: Pedro Y. Saito Criado em 26/01/2011
     */
    public int getDiasParaBloqueio(int empresa) throws Exception {
        int result = -1;
        try {
            String sqlStr = "SELECT conf.dataExpiracao as expiracaoConf, "
                    + "e.dataExpiracao as expiracaoEmpresa, e.concessao_dia_extra as concessao_dia_extra "
                    + "FROM configuracaosistema conf "
                    + "left outer join empresa e on e.codigo = " + empresa
                    + " where conf.dataExpiracao is not null "
                    + "or e.dataExpiracao is not null";
            PreparedStatement stm = con.prepareStatement(sqlStr);
            ResultSet tabelaResultado = stm.executeQuery();
            if (tabelaResultado.next()) {
                Date dataExpiracao = tabelaResultado.getDate("expiracaoEmpresa") != null
                        ? tabelaResultado.getDate("expiracaoEmpresa")
                        : tabelaResultado.getDate("expiracaoConf");
                if (dataExpiracao != null) {
                    JSFUtilities.storeOnSession("dataExpiracao", Uteis.getData(dataExpiracao));
                }
                Long l = Uteis.nrDiasEntreDatas(Calendario.hoje(), dataExpiracao);
                if (l != null && (l.intValue() <= 0 || l.intValue() <= 5)) {
                    if (l.intValue() < 0) {
                        l = 0L;
                    }
                    result = l.intValue();
                } else {
                    result = -1;
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            result = 0;
        }
        JSFUtilities.storeOnSession(JSFUtilities.DIAS_PARA_EXPIRAR, result);
        return result;
    }

    /**
     * Operação responsável por verificar se um usuário existe (através de ser
     * Username e Senha) e retornar os dados deste usuário para registro na
     * sessão da aplicação. Caso o usuário não exista é retornado um Exception.
     * da operação </code>verificarPermissaoOperacao(PermissaoVO permissao, int
     * operacao)</code>.
     *
     * @param senha Senha do usuário.
     * @return UsuarioVO Dados do usuário localizado.
     * @throws Exception Erro alertando que o usuário não existe.
     */
    public UsuarioVO verificarLoginUsuario(Integer codigo, String senha)
            throws Exception {
        inicializar();
        String sqlStr = "SELECT * FROM Usuario WHERE ((codigo = ?) AND (senha = ? or pin = ?))";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        stm.setInt(1, codigo.intValue());
        stm.setString(2, Uteis.encriptar(senha));
        stm.setString(3, Uteis.encriptar(senha));
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Usuário e Senha não Confere.");
        }
         UsuarioVO usu = Usuario.montarDados(tabelaResultado,
                Uteis.NIVELMONTARDADOS_TODOS, this.con);
         if (usu.getColaboradorVO() != null && usu.getColaboradorVO().getSituacao().equals("NA")) {
                throw new ConsistirException("Usuário desativado!");
            }
        return usu;
    }

    public UsuarioVO verificarLoginUsuarioPIN(String username, String senha)
            throws Exception {
        username = Uteis.removerEspacosInicioFimString(username); //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
        senha = Uteis.removerEspacosInicioFimString(senha);
        String sqlStr = "SELECT * FROM Usuario WHERE ((username = ?) AND (senha = ? or pin = ?))";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        stm.setString(1, username);
        stm.setString(2, Uteis.encriptar(senha));
        stm.setString(3, Uteis.encriptar(senha));
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Usuário e Senha não Confere.");
        }
        UsuarioVO usu = Usuario.montarDados(tabelaResultado,
                Uteis.NIVELMONTARDADOS_TODOS, this.con);
        if (usu.getColaboradorVO() != null && usu.getColaboradorVO().getSituacao().equals("NA")) {
            throw new ConsistirException("Usuário desativado!");
        }
        return usu;
    }
    
    public UsuarioVO verificarLoginUsuarioSemSenhaCappta(Integer codigo)
            throws Exception {
        inicializar();
        String sqlStr = "SELECT * FROM Usuario WHERE (codigo = ?)";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        stm.setInt(1, codigo.intValue());
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("msg_err_usuario_senha");
        }
         UsuarioVO usu = Usuario.montarDados(tabelaResultado,
                Uteis.NIVELMONTARDADOS_TODOS, this.con);
         if (usu.getColaboradorVO() != null && usu.getColaboradorVO().getSituacao().equals("NA")) {
                throw new ConsistirException("msg_err_usuario_desativado");
            }
        return usu;
    }

    private static UsuarioVO obterUsuarioLogado() throws Exception {
        UsuarioVO u = null;
        if(FacesContext.getCurrentInstance() != null) {
            u = (UsuarioVO)context().getExternalContext().getRequestMap().get("responsavelOperacao");
        }
        if(context() == null && getUsuario() != null){
            u = getUsuario();
        }
        if (u == null){
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            u = loginControle.getUsuario();
        }
        return u;
    }

    private static EmpresaVO obterEmpresaLogado() throws Exception {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        EmpresaVO empresa = loginControle.getEmpresa();
        return empresa;
    }

    private static PerfilAcessoVO obterPerfilAcessoUsuarioLogado() throws Exception {
        PerfilAcessoVO p = null;
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if (FacesContext.getCurrentInstance() != null) {
            UsuarioVO u = (UsuarioVO) context().getExternalContext().getRequestMap().get("responsavelOperacao");
            if (u != null && !u.getCodigo().equals(loginControle.getUsuario().getCodigo())) {
                EmpresaVO empresa = obterEmpresaLogado();
                Iterator i = u.getUsuarioPerfilAcessoVOs().iterator();
                while (i.hasNext()) {
                    UsuarioPerfilAcessoVO usuarioPerfilAcessoVO = (UsuarioPerfilAcessoVO) i.next();
                    if (usuarioPerfilAcessoVO.getEmpresa().getCodigo().equals(empresa.getCodigo())) {
                        p = getFacade().getPerfilAcesso().consultarPorChavePrimaria(usuarioPerfilAcessoVO.getPerfilAcesso().getCodigo(),
                                Uteis.NIVELMONTARDADOS_TODOS);
                        break;
                    }
                }

            } else {
                p = loginControle.getPerfilAcesso();
            }
        } else {
            p = loginControle.getPerfilAcesso();
        }
        return p;
    }

    /**
     * Operação responsável por verificar se um usuário possui acesso a uma
     * determinada operação, de uma entidade específica. Faz uso da operação
     * </code>verificarPermissaoOperacao(getPerfilAcesso().getPermissaoVOs(),
     * nomeEntidade, operacao)</code> para realizar a verificação para um perfil
     * de acesso específico. Libera a consulta para as entidades PerfilAcesso e
     * Permissoes, para que seja possível consultar se o usuário possui ou não
     * acesso a uma determinada funcionalidade.
     *
     * @param nomeEntidade Código de uma operação para qual se deseja realizar esta
     *                     verificação.
     * @param operacao     Código de uma operação para qual se deseja realizar esta
     *                     verificação.
     * @return boolean true caso exista liberação para uso da operação neste
     * perfil de acesso.
     */
    private boolean verificarPermissaoUsuario(String nomeEntidade, int operacao)
            throws Exception {
        return verificarPermissaoUsuario(nomeEntidade, operacao, null);
    }

    /**
     * Operação responsável por verificar se um usuário possui acesso a uma
     * determinada operação, de uma entidade específica. Faz uso da operação
     * </code>verificarPermissaoOperacao(getPerfilAcesso().getPermissaoVOs(),
     * nomeEntidade, operacao)</code> para realizar a verificação para um perfil
     * de acesso específico. Libera a consulta para as entidades PerfilAcesso e
     * Permissoes, para que seja possível consultar se o usuário possui ou não
     * acesso a uma determinada funcionalidade.
     *
     * @param nomeEntidade Código de uma operação para qual se deseja realizar esta
     *                     verificação.
     * @param operacao     Código de uma operação para qual se deseja realizar esta
     *                     verificação.
     * @return boolean true caso exista liberação para uso da operação neste
     *         perfil de acesso.
     */
    private boolean verificarPermissaoUsuario(String nomeEntidade, int operacao, UsuarioVO usuario)
            throws Exception {
        if (FacesContext.getCurrentInstance() != null) {
            PerfilAcessoVO perfilAcesso = obterPerfilAcessoUsuarioLogado();

            UsuarioVO usuarioLogado = usuario == null ? obterUsuarioLogado() : usuario;

            if (usuarioLogado.isPseudo()) {
                return true;
            }
            if (nomeEntidade.equals("")) {
                return false;
            }
            if ((usuarioLogado == null)
                    || (usuarioLogado.getUsername().equalsIgnoreCase(""))) {
                return false;
            }
            if (perfilAcesso == null) {
                return false;
            }
            return verificarPermissaoOperacao(perfilAcesso.getPermissaoVOs(),
                    nomeEntidade, operacao, null);
        } else {
            return true;
        }

    }

    /**
     * Operação padrão para realizar o INCLUIR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação INCLUIR.
     *
     * @param idEntidade Nome da entidade para a qual se deseja realizar a operação.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void incluirCRM(String idEntidade) throws Exception {
        // O povoador de banco inicial utiliza métodos via reflection para incluir/alterar registros, e neste caso não é necessário validar a permissão de incluir/alterar
        if (JSFUtilities.isJSFContext()){
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            if (loginControle != null){
                super.incluir(idEntidade);
        /*
         * if (!getCobrecrm()) { String msgErro = "O Sistema não cobre o CRM.";
         * throw (new AcessoException(msgErro)); }
         */
                if (!verificarPermissaoUsuario(idEntidade, ConstantesAcesso.INCLUIR)) {
                    String nomeUsuario = "";
                    if (obterUsuarioLogado() != null) {
                        nomeUsuario = obterUsuarioLogado().getNome().toUpperCase();
                        if (obterUsuarioLogado().getAdministrador().booleanValue()) {
                            obterLogControleUsabilidade("Incluir", idEntidade);
                            return;
                        }
                    }
                    perfilAcesso = obterPerfilAcessoUsuarioLogado();
                    if(idEntidade.equalsIgnoreCase("Passivo")){
                        idEntidade = "receptivo";
                    }
                    String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                            + ") não possui permissão para esta operação, \"INCLUIR " + idEntidade.toUpperCase() + "\"";
                    throw (new AcessoException(msgErro));
                }
                obterLogControleUsabilidade("Incluir", idEntidade);
            }
        }

    }

    /**
     * Operação padrão para realizar o ALTERAR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação ALTERAR.
     *
     * @param idEntidade Nome da entidade para a qual se deseja realizar a operação.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void alterarCRM(String idEntidade) throws Exception {
        super.alterar(idEntidade);
        /*
         * if (!getCobrecrm()) { String msgErro = "O Sistema não cobre o CRM.";
         * throw (new AcessoException(msgErro)); }
         */

        if (!verificarPermissaoUsuario(idEntidade, ConstantesAcesso.INCLUIR)) {
            if(!verificarPermissaoUsuario(idEntidade, ConstantesAcesso.ALTERAR)) {
                String nomeUsuario = "";
                if (obterUsuarioLogado() != null) {
                    nomeUsuario = obterUsuarioLogado().getNome().toUpperCase();
                    if (obterUsuarioLogado().getAdministrador().booleanValue()) {
                        obterLogControleUsabilidade("Alterar", idEntidade);
                        return;
                    }
                }
                perfilAcesso = obterPerfilAcessoUsuarioLogado();
                if (idEntidade.equalsIgnoreCase("Passivo")) {
                    idEntidade = "receptivo";
                }
                String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                        + ") não possui permissão para esta operação, \"INCLUIR/ALTERAR " + idEntidade.toUpperCase() + "\"";
                throw (new AcessoException(msgErro));
            }
        }
        obterLogControleUsabilidade("Alterar", idEntidade);
    }

    /**
     * Operação padrão para realizar o EXCLUIR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação EXCLUIR.
     *
     * @param idEntidade Nome da entidade para a qual se deseja realizar a operação.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void excluirCRM(String idEntidade) throws Exception {
        super.excluir(idEntidade);
        /*
         * if (!getCobrecrm()) { String msgErro = "O Sistema não cobre o CRM.";
         * throw (new AcessoException(msgErro)); }
         */
        if (!verificarPermissaoUsuario(idEntidade, ConstantesAcesso.EXCLUIR)) {
            String nomeUsuario = "";
            if (obterUsuarioLogado() != null) {
                nomeUsuario = obterUsuarioLogado().getNome().toUpperCase();
                if (obterUsuarioLogado().getAdministrador().booleanValue()) {
                    obterLogControleUsabilidade("Excluir", idEntidade);
                    return;
                }
            }
            perfilAcesso = obterPerfilAcessoUsuarioLogado();
            if(idEntidade.equalsIgnoreCase("Passivo")){
                idEntidade = "receptivo";
            }
            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"EXCLUIR " + idEntidade.toUpperCase() + "\"";
            throw (new AcessoException(msgErro));
        }
        obterLogControleUsabilidade("Excluir", idEntidade);
    }

    /**
     * Operação padrão para realizar o CONSULTAR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação CONSULTAR.
     *
     * @param idEntidade Nome da entidade para a qual se deseja realizar a operação.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso a
     *                   esta operação.
     */
    public void consultarCRM(String idEntidade, boolean verificarAcesso)
            throws Exception {
        super.consultar(idEntidade);
        /*
         * if (!getCobrecrm()) { String msgErro = "O Sistema não cobre o CRM.";
         * throw (new AcessoException(msgErro)); }
         */
        if (!verificarAcesso) {
            return;
        }
        if (!verificarPermissaoUsuario(idEntidade, ConstantesAcesso.CONSULTAR)) {
            String nomeUsuario = "";
            if (obterUsuarioLogado() != null) {
                nomeUsuario = obterUsuarioLogado().getNome().toUpperCase();
                if (obterUsuarioLogado().getAdministrador().booleanValue()) {
                    return;
                }
            }
            perfilAcesso = obterPerfilAcessoUsuarioLogado();
            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"CONSULTAR " + idEntidade.toUpperCase() + "\"";
            throw (new AcessoException(msgErro));
        }
    }

    public static Boolean verificarPermissaoCRM() {
        if (context() != null) {
            return (Boolean) JSFUtilities.getManagedBeanValue("LoginControle.apresentarLinkCRM");
        } else {
            return false;
        }
    }

    public static Boolean verificarPermissaoCE() {
        if (context() != null) {
            return (Boolean) JSFUtilities.getManagedBeanValue("LoginControle.apresentarLinkCE");
        } else {
            return false;
        }
    }

    public static Boolean verificarPermissaoFinanceiro() {
        if (context() != null) {
            return (Boolean) JSFUtilities.getManagedBeanValue("LoginControle.apresentarLinkFinanceiro");
        } else {
            return false;
        }
    }

    public static boolean isPermiteMultiEmpresas() {
        if (context() != null) {
            return (Boolean) JSFUtilities.getFromSession("permitemultiempresas");
        } else {
            return true;
        }
    }

    public static UsuarioVO getUsuario() {
        return usuario;
    }

    public static void setSomenteUsuario(UsuarioVO aUsuario) throws Exception {
        usuario = aUsuario;
    }

    public static void setUsuario(UsuarioVO aUsuario) throws Exception {
        usuario = aUsuario;
    }

    public static PerfilAcessoVO getPerfilAcesso() {
        return perfilAcesso;
    }

    public static void setPerfilAcesso(PerfilAcessoVO aPerfilAcesso) {
        perfilAcesso = aPerfilAcesso;
    }

    public boolean existeEmail(String email) throws Exception {
        String sql = "select exists (select email from email e where e.email = '" + email + "' limit 1) as existe";
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
        }
        return false;
    }

    public boolean verificaSePossuiEmail(Integer codigoPessoa) throws Exception {
        String sql = "SELECT codigo FROM email WHERE pessoa = " + codigoPessoa;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return true;
            }
            return false;
        }
    }

    public static UsuarioVO getUsuarioLogadoControleAcesso() {
        return usuarioLogadoControleAcesso;
    }

    public static Boolean verificarPermissaoRelatorioFrequenciaTurmas() {
        if (context() != null) {
            return (Boolean) JSFUtilities.getManagedBeanValue("LoginControle.abrirRelatorioFrequenciaTurma");
        } else {
            return false;
        }
    }

}
