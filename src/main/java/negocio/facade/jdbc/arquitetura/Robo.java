/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.arquitetura;

import br.com.pactosolucoes.atualizadb.processo.ProcessarAcessoContratosCredito;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import controle.crm.AberturaMetaControle;
import negocio.comuns.arquitetura.ResultadoServicosVO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.ServicoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.sad.SituacaoClienteSinteticoDWControle;
import servicos.integracao.impl.parceirofidelidade.base.ParceiroFidelidadeZW;
import servicos.operacoes.RiscoService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class Robo extends SuperEntidade {

    public Robo() throws Exception {
        super();
    }

    public Robo(Connection con) throws Exception {
        super(con);
    }

    public void start(RoboVO robo, String urlOamd, String key) throws Exception {
        try {
            if (robo.isUsarRobo()) {
                ResultadoServicosVO resultadoRobo = new ResultadoServicosVO(ServicoEnum.ROBO);
                robo.setTexto("Rotina foi processada com sucesso nesse dia.");
                robo.setDataHoraInicio(Calendario.hoje());
                robo.setRotinaProcessada(false);
                if (!robo.isProcessamentoControlado()) {
                    incluirSemCommit(robo);
                }
                //logar início
                Logger.getLogger(this.getClass().getName()).log(
                        Level.INFO,
                        "{0} - iniciando processamento do dia {1} em: {2}",
                        new Object[]{
                            robo.getListaEmpresa().get(0).getNome(),
                            Uteis.getData(robo.getDia()),
                            Uteis.getDataComHora(Calendario.hoje())
                        });

                robo.validarExisteOperacaoContratoAgendado();
                robo.gerarRegistroSituacaoAnaliticoDW();
                executarRotinasQueNaoDependemDoSintetico(robo, urlOamd, key);
                SituacaoClienteSinteticoDWControle sinteticoDWControle = new SituacaoClienteSinteticoDWControle();
                if (robo.isDeveGerarSintetico()) {
                    
                    robo.setClienteAtualizados(robo.getClienteAtualizados() + sinteticoDWControle.processarDia(robo.getDia(), true));
                    robo.setClienteJaProcessados(robo.getClienteJaProcessados() + sinteticoDWControle.processarDiaAlunosFrequencia(robo.getDia(), robo.getClienteAtualizados(), robo.getClienteJaProcessados()));
                    sinteticoDWControle.processarDiaAlunosDadosAcesso(robo.getClienteAtualizados());
                    robo.validarSituacoesInconsistentes();
                    
                    new RiscoService(robo.isProcessamentoControlado(),
                            robo.getListaEmpresa(),
                            robo.getListClientesControlado(),
                            robo.getDia(), robo.getConfiguracaoSistema(),
                            robo.getUsuarioVO()).processarClientesEmRisco();
                    robo.processarClientesComParcelaVencida();
                    if (!robo.isProcessamentoControlado() || !robo.getListClientesControlado().isEmpty()) {
                        robo.processarClientesComProdutoVencido();
                    }
                    for(EmpresaVO empresa : robo.getListaEmpresa()){
                        if(empresa.isBloquearAcessoArmarioVigenciaVencida()) {
                            robo.processarClientesComAluguelArmarioVencidoChaveDevolvida(empresa.getCodigo());
                        }
                    }
                    try {
                        robo.processarClientesComCartaoCreditoVencido();
                    } catch (Exception ex) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(" Não foi possível executar o processo de verificação de cartão vencidos no dia ").append(Uteis.getData(robo.getDia()));
                        sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
                        sb.append(" - ERRO: ").append(ex.getMessage());
                        Uteis.logar(null, sb.toString());
                    }

                    if (!robo.isProcessamentoControlado()) {
                        corrigirInformacoesCreditoTreinoTodosContratos(robo);
                        processoDesmarcarAulasParcelasVencidas (key);
                        getFacade().getZWFacade().getSituacaoClienteSinteticoDW().sincronizarFrequenciaSemanalComSistemaTreino();
                    }

                } else {
                    robo.setClienteJaProcessados(robo.getClienteJaProcessados() + sinteticoDWControle.processarDiaAlunosFrequencia(robo.getDia(), robo.getClienteAtualizados(), robo.getClienteJaProcessados()));
                }
                verificarContratosAtivosSemCreditoTreino(robo);//deve ser executado todos os dias, e se o sintetico rodar no dia, deve ser executado depois.
                getFacade().getZWFacade().getSituacaoClienteSinteticoDW().atualizarIdade(robo.getDia());
                getFacade().getZWFacade().getSituacaoClienteSinteticoDW().atualizarFrequenciaSemana();
                getFacade().getAulaDesmarcada().atualizarContratoDasAulasDesmarcadas();

                //Como o robo roda de madrugada, deve fechar as metas do dia anterior;
                try {
                    if (!robo.isProcessamentoControlado()) {
                        new AberturaMetaControle().fecharMetas(Uteis.somarDias(robo.getDia(), -1));
                        new AberturaMetaControle().abrirMetas(robo.getDia());
                    }
                } catch (Exception ex) {
                    StringBuilder sb = new StringBuilder();
                    if (robo.getListaEmpresa().size() > 0) {
                        sb.append(robo.getListaEmpresa().get(0).getNome()).append(" -");
                    }
                    sb.append(" Não foi possível executar o processo de metas corretamente no dia").append(Uteis.getData(robo.getDia()));
                    sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
                    sb.append(" - ERRO: ").append(ex.getMessage());
                    Uteis.logar(null, sb.toString());
                }

                //logar término
                Logger.getLogger(this.getClass().getName()).log(Level.INFO,
                        "{0} - terminando processamento do dia {1} em: {2}",
                        new Object[]{
                            robo.getListaEmpresa().get(0).getNome(),
                            Uteis.getData(robo.getDia()),
                            Uteis.getDataComHora(Calendario.hoje())
                        });
                robo.setDataHoraFim(Calendario.hoje());
                robo.setRotinaProcessada(true);
                if (!robo.isProcessamentoControlado()) {
                    alterarSemCommit(robo);
                }
                getFacade().getZWFacade().getResultadoServicos().gravarResultado(resultadoRobo);
            }

        } catch (Exception e) {
            robo.setRotinaProcessada(false);
            robo.setTexto(e.getMessage());
            robo.setDataHoraFim(Calendario.hoje());
            if (!robo.isProcessamentoControlado()) {
                alterarSemCommit(robo);
            }
            throw e;
        }
    }


    public void startPontos(RoboVO robo, String urlOamd, String key, Date dataProcessamento) throws Exception {
        try {
            if (robo.isUsarRobo()) {
                ResultadoServicosVO resultadoRobo = new ResultadoServicosVO(ServicoEnum.ROBO_PONTUACAO);
                robo.setTexto("Rotina de Pontuação processada com sucesso no dia: "+ Uteis.getData(Calendario.hoje()));
                robo.setDataHoraInicio(Calendario.hoje());
                robo.setDia(dataProcessamento);
                robo.setRotinaProcessada(false);

                verificarAulasComfirmadasParaPontucao(robo);
                verificarAlunosComAcesso(robo, urlOamd, key);

                Uteis.logar(null,"Terminando processamento do dia "+Uteis.getData(robo.getDia())+" em: "+Uteis.getDataComHora(Calendario.hoje()));
                robo.setDataHoraFim(Calendario.hoje());
            }

        } catch (Exception e) {
            throw e;
        }
    }

    private void executarRotinasQueNaoDependemDoSintetico(RoboVO robo, String urlOamd, String key) {
        processarAcessoSemConsumoContratosCredito(robo, key);
        verificarNaoComparecimentoAulaCreditoTreino(robo);
        processarCreditoDisponivelMensalCreditoTreino(robo);
        processarCreditoDisponivelMensalZerarCreditoContratoInativo(robo);
        verificarAulasComfirmadasParaPontucao(robo);
        verificarAlunosComAcesso(robo, urlOamd, key);
        processarPontosDotz(robo);
    }

    private void processarCreditoDisponivelMensalCreditoTreino(RoboVO robo){
        try{
            getFacade().getContratoDuracaoCreditoTreino().processarCreditoDisponivelMensal(robo.getDia());
        }catch (Exception e){
            StringBuilder sb = new StringBuilder();
            sb.append(" Erro ao executar processo 'processarCreditoDisponivelMensalCreditoTreino'. Dia do processamento:").append(Uteis.getData(robo.getDia()));
            sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
            sb.append(" - ERRO: ").append(e.getMessage());
            Uteis.logar(null, sb.toString());
        }
    }

    private void processarCreditoDisponivelMensalZerarCreditoContratoInativo(RoboVO robo){
        try{
            getFacade().getContratoDuracaoCreditoTreino().processarCreditoDisponivelMensalZerarCreditoContratoInativo(robo.getDia());
        }catch (Exception e){
            StringBuilder sb = new StringBuilder();
            sb.append(" Erro ao executar processo 'processarCreditoDisponivelMensalZerarCreditoContratoInativo'. Dia do processamento:").append(Uteis.getData(robo.getDia()));
            sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
            sb.append(" - ERRO: ").append(e.getMessage());
            Uteis.logar(null, sb.toString());
        }
    }

    private void verificarNaoComparecimentoAulaCreditoTreino(RoboVO robo){
        try{
            getFacade().getControleCreditoTreino().verificarNaoComparecimentoAulaCreditoTreino(robo.getDia());
        }catch (Exception e){
            StringBuilder sb = new StringBuilder();
            sb.append(" Erro ao executar processo 'verificarNaoComparecimentoAulaCreditoTreino'. Dia do processamento:").append(Uteis.getData(robo.getDia()));
            sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
            sb.append(" - ERRO: ").append(e.getMessage());
            Uteis.logar(null, sb.toString());
        }
    }

    private void verificarContratosAtivosSemCreditoTreino(RoboVO robo){
        try{
            getFacade().getZWFacade().finalizarContratosSemCreditos(robo.getDia());
        }catch (Exception e){
            StringBuilder sb = new StringBuilder();
            sb.append(" Erro ao executar processo 'verificarContratosAtivosSemCreditoTreino'. Dia do processamento:").append(Uteis.getData(robo.getDia()));
            sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
            sb.append(" - ERRO: ").append(e.getMessage());
            Uteis.logar(null, sb.toString());
        }
    }

    private void verificarAulasComfirmadasParaPontucao(RoboVO robo){
        try {
            getFacade().getZWFacade().inserirPontuacaoCliente(Uteis.somarDias(robo.getDia(), -1));
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append(" Erro ao executar processo 'verificarAulasComfirmadasParaPontucao'. Dia do processamento:").append(Uteis.getData(robo.getDia()));
            sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
            sb.append(" - ERRO: ").append(e.getMessage());
            Uteis.logar(null, sb.toString());
        }
    }
    
    private void verificarAlunosComAcesso(RoboVO robo, String urlOamd, String key){
        try {
            getFacade().getZWFacade().inserirPontuacaoPorAcessoCatraca(Uteis.somarDias(robo.getDia(), -1), robo.getListaEmpresa(), urlOamd, key);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append(" Erro ao executar processo 'verificarAlunosComAcesso'. Dia do processamento:").append(Uteis.getData(robo.getDia()));
            sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
            sb.append(" - ERRO: ").append(e.getMessage());
            Uteis.logar(null, sb.toString());
        }
    }

    private void processoDesmarcarAulasParcelasVencidas (String key) {
        try{
            getFacade().getReposicao().removerMarcacoesFuturasParcelasVencidas(key);
        }catch (Exception e){
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO EXECUTAR processoDesmarcarAulasParcelasVencidas. ERRO: " + e.getMessage());
        }
    }

    private void corrigirInformacoesCreditoTreinoTodosContratos(RoboVO roboVO){
        try{
            getFacade().getSituacaoClienteSinteticoDW().atualizarInformacoesCreditoTreino(null, roboVO.getDia());
        }catch (Exception e){
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO EXECUTAR corrigirInformacoesCreditoTreinoTodosContratos. ERRO: " + e.getMessage());
        }
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>RoboVO</code>.
     */
    public RoboVO novo() throws Exception {
        incluir(getIdEntidade());
        return new RoboVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>RoboVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>RoboVO</code> que será gravado no banco
     * de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(RoboVO obj) throws Exception {
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Robo( dia, texto,rotinaProcessada,dataHoraInicio, dataHoraFim ) "
                + "VALUES ( ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDia()));
        sqlInserir.setString(2, obj.getTexto());
        sqlInserir.setBoolean(3, obj.getRotinaProcessada());
        sqlInserir.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getDataHoraInicio()));
        sqlInserir.setTimestamp(5, Uteis.getDataJDBCTimestamp(obj.getDataHoraFim()));
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>RoboVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>RoboVO</code> que será gravado no banco
     * de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluirSemCommit(RoboVO obj) throws Exception {
        try {
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Robo( dia, texto,rotinaProcessada,dataHoraInicio, dataHoraFim,rotinaProcessadaParcialmente,textoErroProcessamento ) "
                    + "VALUES ( ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDia()));
            sqlInserir.setString(2, obj.getTexto());
            sqlInserir.setBoolean(3, obj.getRotinaProcessada());
            sqlInserir.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getDataHoraInicio()));
            sqlInserir.setTimestamp(5, Uteis.getDataJDBCTimestamp(obj.getDataHoraFim()));
            sqlInserir.setBoolean(6, obj.getRotinaProcessadaParcialmente());
            sqlInserir.setString(7, obj.getTextoErroProcessamento());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setRotinaProcessada(false);
            throw e;
        }
    }

    public void alterarSemCommit(RoboVO obj) throws Exception {
        try {
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Robo set dia=?, texto=?,rotinaProcessada=?,dataHoraInicio=?, dataHoraFim=?, rotinaProcessadaParcialmente=?, textoErroProcessamento=? "
                    + "WHERE codigo = ?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDia()));
            sqlAlterar.setString(2, obj.getTexto());
            sqlAlterar.setBoolean(3, obj.getRotinaProcessada());
            sqlAlterar.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getDataHoraInicio()));
            sqlAlterar.setTimestamp(5, Uteis.getDataJDBCTimestamp(obj.getDataHoraFim()));
            sqlAlterar.setBoolean(6, obj.getRotinaProcessadaParcialmente());
            sqlAlterar.setString(7, obj.getTextoErroProcessamento());
            sqlAlterar.setInt(8, obj.getCodigo());
            sqlAlterar.execute();
        } catch (Exception e) {
            obj.setRotinaProcessada(false);
            throw e;
        }
    }

    public Boolean consultarRotinaRobo(Date dia) throws Exception {
        String sql = "SELECT rotinaProcessada FROM Robo where CAST( dia AS DATE) = CAST( '"
                + Uteis.getDataJDBC(dia) + "' AS DATE) "
                + "and rotinaprocessada = true "
                + "order by codigo desc limit 1";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        return tabelaResultado.next() && tabelaResultado.getBoolean("rotinaProcessada");
    }

    public Boolean validarSeExisteDia(Date dia) throws Exception {
        String sql = "SELECT * FROM Robo where dia > '" + Uteis.getDataJDBC(dia) + " 23:59:59' limit 1";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        return tabelaResultado.next();
    }

    public Date consultaParaObterUltimoDiaprocessado() throws Exception {
        String sql = "SELECT MAX(Robo.dia) as dia  FROM Robo  where  rotinaProcessada = true ";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            throw new Exception("Nao foi encontrado a última data processada.");
        }
        return tabelaResultado.getDate("dia");
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (<code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>RoboVO</code>
     * resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<RoboVO> vetResultado = new ArrayList<RoboVO>();
        while (tabelaResultado.next()) {
            RoboVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>) em um objeto da classe <code>RoboVO</code>
     * .
     *
     * @return O objeto da classe <code>RoboVO</code> com os dados devidamente
     * montados.
     */
    public static RoboVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        RoboVO obj = new RoboVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDia(dadosSQL.getDate("dia"));
        obj.setDataHoraInicio(dadosSQL.getTimestamp("dataHoraInicio"));
        obj.setDataHoraFim(dadosSQL.getTimestamp("dataHoraFim"));
        obj.setTexto(dadosSQL.getString("texto"));
        obj.setRotinaProcessada(dadosSQL.getBoolean("rotinaProcessada"));
        try{
            obj.setRotinaProcessadaParcialmente(dadosSQL.getBoolean("rotinaProcessadaParcialmente"));
            obj.setTextoErroProcessamento(dadosSQL.getString("textoErroProcessamento"));
        } catch (Exception ignored){}

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>RoboVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public RoboVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        // SuperEntidade.consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Robo WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Robo ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public boolean diaAtualEstaEmProcessamento(Date dia) throws Exception {
        String sql = "select exists(SELECT codigo FROM Robo where cast(dia as date) = '"
                + Uteis.getDataJDBC(dia) + "' "
                + "and rotinaprocessada is true "
                + "and datahorainicio is not null "
                + "and datahorafim is null)";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        //Uteis.logar(null, " ROBO EXECUTANDO ? -> " + tabelaResultado.getBoolean(1));
        return tabelaResultado.getBoolean(1);
    }
    
    public void inicializarDadosAcessoSintetico(Date data) throws Exception{
        getFacade().getSituacaoClienteSinteticoDW().inicializarDadosAcesso(Calendario.hoje());
        
    }

    private void processarPontosDotz(RoboVO robo){
        try{
            ParceiroFidelidadeZW parceiroFidelidadeZW = new ParceiroFidelidadeZW(con);
            parceiroFidelidadeZW.processarPontosDotz(robo.getDia());
            parceiroFidelidadeZW = null;
        }catch (Exception e){
            StringBuilder sb = new StringBuilder();
            sb.append(" Erro ao executar processo 'processarDotz'. Dia do processamento:").append(Uteis.getData(robo.getDia()));
            sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
            sb.append(" - ERRO: ").append(e.getMessage());
            Uteis.logar(null, sb.toString());
        }
    }

    private void processarAcessoSemConsumoContratosCredito(RoboVO robo, String key){
        try{
            Uteis.logar("Iniciando processo 'processarAcessoSemConsumoContratosCredito'. Dia do processamento: " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()));
            ProcessarAcessoContratosCredito.processarConsumo(getFacade().getRisco().con, key, false, robo.getDia());

        }catch (Exception e){
            StringBuilder sb = new StringBuilder();
            sb.append(" Erro ao executar processo 'processarAcessoSemConsumoContratosCredito'. Dia do processamento:").append(Uteis.getData(robo.getDia()));
            sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
            sb.append(" - ERRO: ").append(e.getMessage());
            Uteis.logar(null, sb.toString());
        }
        Uteis.logar("Finalizando processo 'processarAcessoSemConsumoContratosCredito'. Dia do processamento: " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()));
    }
}
