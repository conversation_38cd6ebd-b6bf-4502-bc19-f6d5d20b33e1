package negocio.facade.jdbc.contrato;

import br.com.pactosolucoes.comuns.util.Formatador;
import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.ComissaoGeralConfiguracaoVO;
import negocio.comuns.contrato.ComissaoProdutoConfiguracaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ComissaoMetaFinananceiraVO;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.interfaces.contrato.ComissaoGeralConfiguracaoInterfaceFacade;
import relatorio.negocio.jdbc.contrato.ComissaoRel;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ComissaoGeralConfiguracao extends SuperEntidade implements ComissaoGeralConfiguracaoInterfaceFacade {
    public ComissaoGeralConfiguracao() throws Exception {
    }

    public ComissaoGeralConfiguracao(Connection conexao) throws Exception {
        super(conexao);
    }

    public static ComissaoGeralConfiguracaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ComissaoGeralConfiguracaoVO obj = new ComissaoGeralConfiguracaoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDuracao(dadosSQL.getInt("duracao"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setValorFixoAgendado(dadosSQL.getDouble("valorfixoagendado"));
        obj.setValorFixoEspontaneo(dadosSQL.getDouble("valorfixoespontaneo"));
        obj.setPorcentagemAgendado(dadosSQL.getDouble("porcentagemagendado"));
        obj.setPorcentagemEspontaneo(dadosSQL.getDouble("porcentagemespontaneo"));
        if (dadosSQL.getString("vigenciainicio") != null) {
            obj.setVigenciaInicio(Calendario.getDate("MM/yyyy", dadosSQL.getString("vigenciainicio")));
        }
        if (dadosSQL.getString("vigenciafinal") != null) {
            obj.setVigenciaFinal(Calendario.getDate("MM/yyyy", dadosSQL.getString("vigenciafinal")));
        }
        obj.setEmpresa(getFacade().getEmpresa().consultarPorCodigo(dadosSQL.getInt("empresa"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (obj.getEmpresa().isPagarComissaoSeAtingirMetaFinanceira()){
            obj.setListaComissaoMeta(getFacade().getComissaoMetaFinanceira().consultar(obj,Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        obj.setNovoObj(false);
        return obj;
    }

    public static List<ComissaoGeralConfiguracaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<ComissaoGeralConfiguracaoVO> vetResultado = new ArrayList<ComissaoGeralConfiguracaoVO>();
        while (tabelaResultado.next()) {
            ComissaoGeralConfiguracaoVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public void incluir(ComissaoGeralConfiguracaoVO obj) throws Exception {
        obj.validarDados();
        try{
            con.setAutoCommit(false);
            String sql = "INSERT INTO comissaogeralconfiguracao (duracao, situacao, valorfixoespontaneo, valorfixoagendado, porcentagemespontaneo, porcentagemagendado, vigenciainicio, vigenciafinal, empresa)\n" +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setInt(1, obj.getDuracao());
                sqlInserir.setString(2, obj.getSituacao());
                sqlInserir.setDouble(3, obj.getValorFixoEspontaneo());
                sqlInserir.setDouble(4, obj.getValorFixoAgendado());
                sqlInserir.setDouble(5, obj.getPorcentagemEspontaneo());
                sqlInserir.setDouble(6, obj.getPorcentagemAgendado());
                sqlInserir.setString(7, Uteis.getDataAplicandoFormatacao(obj.getVigenciaInicio(), "MM/yyyy"));
                sqlInserir.setString(8, Uteis.getDataAplicandoFormatacao(obj.getVigenciaFinal(), "MM/yyyy"));
                sqlInserir.setInt(9, obj.getEmpresa().getCodigo());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);


            getFacade().getComissaoMetaFinanceira().incluirSemCommit(obj);


            con.commit();
        }catch (Exception e){
            obj.setNovoObj(true);
            con.rollback();
            throw e;
        }finally{
            con.setAutoCommit(true);
        }
    }

    public void alterar(ComissaoGeralConfiguracaoVO obj) throws Exception {
        try {
            obj.validarDados();
            con.setAutoCommit(false);
            String sql = "UPDATE comissaogeralconfiguracao\n" +
                    "SET duracao = ?, situacao = ?, valorfixoespontaneo = ?, valorfixoagendado = ?, porcentagemespontaneo = ?, porcentagemagendado = ?,\n" +
                    "vigenciainicio = ?, vigenciafinal = ?, empresa = ?\n" +
                    "WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setInt(1, obj.getDuracao());
                sqlAlterar.setString(2, obj.getSituacao());
                sqlAlterar.setDouble(3, obj.getValorFixoEspontaneo());
                sqlAlterar.setDouble(4, obj.getValorFixoAgendado());
                sqlAlterar.setDouble(5, obj.getPorcentagemEspontaneo());
                sqlAlterar.setDouble(6, obj.getPorcentagemAgendado());
                sqlAlterar.setString(7, Uteis.getDataAplicandoFormatacao(obj.getVigenciaInicio(), "MM/yyyy"));
                sqlAlterar.setString(8, Uteis.getDataAplicandoFormatacao(obj.getVigenciaFinal(), "MM/yyyy"));
                sqlAlterar.setInt(9, obj.getEmpresa().getCodigo());
                sqlAlterar.setInt(10, obj.getCodigo());
                sqlAlterar.execute();
            }

            getFacade().getComissaoMetaFinanceira().incluirSemCommit(obj);

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluir(ComissaoGeralConfiguracaoVO comissaoVO) throws Exception {
        try {
            con.setAutoCommit(false);
            getFacade().getComissaoMetaFinanceira().excluirSemCommmit(comissaoVO.getCodigo());

            String sql = "DELETE FROM comissaogeralconfiguracao WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, comissaoVO.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public boolean existe(ComissaoGeralConfiguracaoVO comissaoVO) throws Exception {
        String sql = "SELECT codigo FROM comissaogeralconfiguracao " +
                "WHERE duracao = " + comissaoVO.getDuracao() +
                " AND situacao = '" + comissaoVO.getSituacao() + "' " +
                " AND empresa = " + comissaoVO.getEmpresa().getCodigo() +
                " AND (('01/" + Uteis.getDataAplicandoFormatacao(comissaoVO.getVigenciaInicio(), "MM/yyyy") + "' BETWEEN ('01/'||vigenciainicio) :: DATE AND ('28/'||vigenciafinal) :: DATE) " +
                " OR ('28/" + Uteis.getDataAplicandoFormatacao(comissaoVO.getVigenciaFinal(), "MM/yyyy") + "' BETWEEN ('01/'||vigenciainicio) :: DATE AND ('28/'||vigenciafinal) :: DATE))" +
                " AND codigo <> " + comissaoVO.getCodigo();
        return existe(sql, getCon());
    }

    public ComissaoGeralConfiguracaoVO consultarPorChavePrimaria(Integer codigoConsulta, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM comissaogeralconfiguracao WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoConsulta);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ComissãoGeral ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        String sql = "SELECT\n" +
                "  cgc.*,\n" +
                "  emp.nome AS nomeEmpresa, \n" +
                " emp.pagarComissaoSeAtingirMetaFinanceira \n" +
                "FROM comissaogeralconfiguracao cgc\n" +
                "  LEFT JOIN empresa emp\n" +
                "    ON cgc.empresa = emp.codigo\n" +
                "WHERE cgc.empresa = " + empresa + "\n" +
                "ORDER BY duracao";
        return con.prepareStatement(sql);
    }

    public String consultarJSON(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                Double valorfixoespontaneo = rs.getDouble("valorfixoespontaneo");
                Double valorfixoagendado = rs.getDouble("valorfixoagendado");
                Double porcentagemespontaneo = rs.getDouble("porcentagemespontaneo");
                Double porcentagemagendado = rs.getDouble("porcentagemagendado");
                if (rs.getBoolean("pagarComissaoSeAtingirMetaFinanceira")) {
                    ComissaoMetaFinananceiraVO comissaoMetaFinananceiraVO = getFacade().getComissaoMetaFinanceira().consultar(rs.getInt("codigo"), 1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (comissaoMetaFinananceiraVO != null) {
                        valorfixoespontaneo = comissaoMetaFinananceiraVO.getValorEspontaneo();
                        valorfixoagendado = comissaoMetaFinananceiraVO.getValorAgendado();
                        porcentagemespontaneo = comissaoMetaFinananceiraVO.getPorcentagemEspontaneo();
                        porcentagemagendado = comissaoMetaFinananceiraVO.getPorcentagemAgendado();
                    }
                }
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(rs.getString("duracao")).append("\",");
                json.append("\"").append(rs.getString("situacao")).append("\",");
                json.append("\"").append(valorfixoespontaneo.toString()).append("\",");
                json.append("\"").append(valorfixoagendado.toString()).append("\",");
                json.append("\"").append(porcentagemespontaneo.toString()).append("%\",");
                json.append("\"").append(porcentagemagendado.toString()).append("%\",");
                json.append("\"").append(rs.getString("vigenciainicio")).append("\",");
                json.append("\"").append(rs.getString("vigenciafinal")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomeEmpresa"))).append("\",");
                json.append("\"").append("<a onclick='deletarTaxa(").append(rs.getString("codigo")).append(")' style='font-size: 14px;'><i class='fa-icon-trash'></i></a>").append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public List<ComissaoRel> processarDadosServlet(TipoRelatorioDF tipoRelatorio, Integer codEmpresa,
                                            Date dataIniLanc, Date dataFimLanc, Date dataReceb,
                                            Date dataContratosLancadosAPartir,
                                            Date dataComp,
                                            String impressaoPor, String tipoValorComissoes,
                                            boolean aceitaMatriculaRematricula,
                                            boolean aceitaManutencaoModalidade,
                                            boolean calcularComissaoProdutos,
                                            boolean retiraEdicaoPagamento,
                                            boolean retirarRecebiveisComPendencia,
                                            boolean considerarCompensacaoOriginal,
                                            int page, int size
                                           ) throws Exception {
        List<ComissaoRel> obj = new ArrayList<ComissaoRel>();
        switch (tipoRelatorio) {
            case COMPETENCIA:
                String competencia = Formatador.formatarData(dataComp, "MM/yyyy");
                int ano = Calendario.getInstance(dataComp).get(Calendar.YEAR);
                obj = consultarCompetenciaServlet(codEmpresa, ano,dataIniLanc, dataFimLanc, competencia, aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos, dataContratosLancadosAPartir, page, size);
                break;
            case FATURAMENTO:
                obj = consultarFaturamento(codEmpresa, dataIniLanc, dataFimLanc, aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos);
                break;
            case FATURAMENTO_DE_CAIXA:
                obj = consultarFaturamentoCaixa(codEmpresa, dataIniLanc, dataFimLanc, dataReceb, dataContratosLancadosAPartir, aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos,retiraEdicaoPagamento,retirarRecebiveisComPendencia);
                break;
            case RECEITA:
                obj = consultarReceita(codEmpresa, dataIniLanc, dataFimLanc, dataReceb, aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos,retirarRecebiveisComPendencia, dataContratosLancadosAPartir, considerarCompensacaoOriginal);
                break;
        }
        resolverChequesDevolvidos(obj, tipoRelatorio, codEmpresa, dataIniLanc, dataFimLanc, dataReceb, dataComp, impressaoPor, tipoValorComissoes,
                aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos, retiraEdicaoPagamento,retirarRecebiveisComPendencia);

        obj = processarContratos(obj, codEmpresa, impressaoPor, tipoValorComissoes);
        return obj;
    }



    public List<ComissaoRel> processarDados(TipoRelatorioDF tipoRelatorio, Integer codEmpresa,
                                            Date dataIniLanc, Date dataFimLanc, Date dataReceb,
                                            Date dataContratosLancadosAPartir,
                                            Date dataComp,
                                            String impressaoPor, String tipoValorComissoes,
                                            boolean aceitaMatriculaRematricula,
                                            boolean aceitaManutencaoModalidade,
                                            boolean calcularComissaoProdutos,
                                            boolean retiraEdicaoPagamento,
                                            boolean retirarRecebiveisComPendencia,
                                            boolean considerarCompensacaoOriginal) throws Exception {
        List<ComissaoRel> obj = new ArrayList<ComissaoRel>();
        switch (tipoRelatorio) {
            case COMPETENCIA:
                String competencia = Formatador.formatarData(dataComp, "MM/yyyy");
                int ano = Calendario.getInstance(dataComp).get(Calendar.YEAR);
                obj = consultarCompetencia(codEmpresa, ano, competencia, aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos, dataContratosLancadosAPartir);
                break;
            case FATURAMENTO:
                obj = consultarFaturamento(codEmpresa, dataIniLanc, dataFimLanc, aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos);
                break;
            case FATURAMENTO_DE_CAIXA:
                obj = consultarFaturamentoCaixa(codEmpresa, dataIniLanc, dataFimLanc, dataReceb, dataContratosLancadosAPartir, aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos,retiraEdicaoPagamento,retirarRecebiveisComPendencia);
                break;
            case RECEITA:
                obj = consultarReceita(codEmpresa, dataIniLanc, dataFimLanc, dataReceb, aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos,retirarRecebiveisComPendencia, dataContratosLancadosAPartir, considerarCompensacaoOriginal);
                break;
        }

        resolverChequesDevolvidos(obj, tipoRelatorio, codEmpresa, dataIniLanc, dataFimLanc, dataReceb, dataComp, impressaoPor, tipoValorComissoes,
                aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos, retiraEdicaoPagamento,retirarRecebiveisComPendencia);

        obj = processarContratos(obj, codEmpresa, impressaoPor, tipoValorComissoes);
        return obj;
    }

    private void resolverChequesDevolvidos(List<ComissaoRel> obj, TipoRelatorioDF tipoRelatorio, Integer codEmpresa,
                                           Date dataIniLanc, Date dataFimLanc, Date dataReceb, Date dataComp,
                                           String impressaoPor, String tipoValorComissoes,
                                           boolean aceitaMatriculaRematricula,
                                           boolean aceitaManutencaoModalidade,
                                           boolean calcularComissaoProdutos,
                                           boolean retiraEdicaoPagamento,
                                           boolean retirarRecebiveisComPendencia) throws Exception{
        ResultSet rsConfig = SuperFacadeJDBC.criarConsulta("select adicionarDevolucaoRelatorioComissao from configuracaofinanceiro", con);
        if(rsConfig.next() &&
                rsConfig.getBoolean("adicionarDevolucaoRelatorioComissao")){
            obj.addAll(processarChequesDevolvidos(tipoRelatorio, codEmpresa,
                    dataIniLanc, dataFimLanc, dataReceb, dataComp,
                    impressaoPor, tipoValorComissoes,
                    aceitaMatriculaRematricula,
                    aceitaManutencaoModalidade,
                    calcularComissaoProdutos,
                    retiraEdicaoPagamento,
                    retirarRecebiveisComPendencia));

            for(ComissaoRel o : new ArrayList<ComissaoRel>(obj)){
                if (!o.getMapaProdutoValor().isEmpty()) {
                    List<MovProdutoVO> movProdutos = obterMovProdutos(o.getMovProdutos());
                    for(MovProdutoVO m: movProdutos){
                        if(m.getChequeDevolucao() != null
                                && !UteisValidacao.emptyNumber(m.getChequeDevolucao().getCodigo())){

                            List<ComissaoRel> comissaoRels = obterItemCheque(codEmpresa, dataIniLanc, dataFimLanc, dataReceb, aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos,
                                    retirarRecebiveisComPendencia, m.getChequeDevolucao().getCodigo(),
                                    m.getDataLancamento(), false);
                            for(ComissaoRel c : comissaoRels){
                                obj.remove(o);
                                c.setDataPagamento(o.getDataPagamento());
                                c.setNomePlano(o.getNomePlano());
                                c.setDataCompensacao(o.getDataCompensacao());
                                c.setRetornoCheque(true);
                                c.setCodigoRecibo(o.getCodigoRecibo());
                                obj.add(c);
                            }

                        }
                    }
                }
            }
        }
    }


    private List<ComissaoRel> processarChequesDevolvidos(TipoRelatorioDF tipoRelatorio, Integer codEmpresa,
                                                         Date dataIniLanc, Date dataFimLanc, Date dataReceb, Date dataComp,
                                                         String impressaoPor, String tipoValorComissoes,
                                                         boolean aceitaMatriculaRematricula,
                                                         boolean aceitaManutencaoModalidade,
                                                         boolean calcularComissaoProdutos,
                                                         boolean retiraEdicaoPagamento,
                                                         boolean retirarRecebiveisComPendencia) throws Exception{
        List<ComissaoRel> lista = new ArrayList<ComissaoRel>();
        StringBuilder sql = new StringBuilder();
        sql.append("select mp.* from movproduto mp ");
        sql.append("inner join produto p on p.codigo = mp.produto ");
        sql.append("where tipoproduto = 'CH' ");
        sql.append("AND datalancamento between '");
        sql.append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIniLanc), "00:00:00")).append("' and '");
        sql.append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataFimLanc), "23:59:59")).append("' ");
        ResultSet resultSet = criarConsulta(sql.toString(), con);
        while(resultSet.next()){
            lista.addAll(obterItemCheque(codEmpresa, dataIniLanc, dataFimLanc, dataReceb, aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos,
                    retirarRecebiveisComPendencia, resultSet.getInt("chequedevolucao"),
                    resultSet.getDate("datalancamento"),
                    true));
        }
        return lista;

    }

    private List<ComissaoRel> obterItemCheque(Integer codEmpresa, Date dataIniLanc, Date dataFimLanc, Date dataReceb,
                                 boolean aceitaMatriculaRematricula, boolean aceitaManutencaoModalidade,
                                 boolean calcularComissaoProdutos, boolean retirarRecebiveisComPendencia, Integer chequeDevolucao, Date data,
                                              boolean devolucaoCheque) throws Exception {
        StringBuilder sqlcheque = ThreadDemonstrativoFinanceiro.consultaPagamentosEmCheque(
                codEmpresa, dataIniLanc, dataFimLanc, true, dataReceb, false,
                null, false, null, null,retirarRecebiveisComPendencia, chequeDevolucao, false, null, "", null, false);
        List<ComissaoRel> listach = new ArrayList<ComissaoRel>();
        obterItens(listach, sqlcheque, aceitaMatriculaRematricula, true, aceitaManutencaoModalidade, calcularComissaoProdutos);
        for(ComissaoRel comissaoRel : listach){
            comissaoRel.setDataCompensacao(data);
            comissaoRel.setDataPagamento(data);
            if(devolucaoCheque){
                comissaoRel.setFormaPagamento("DEVOLUÇÃO CH BANCO");
            }
            comissaoRel.setDevolucaoCheque(devolucaoCheque);
        }
        return listach;
    }

    private List<ComissaoRel> consultarFaturamentoCaixa(Integer codEmpresa, Date dataIni, Date dataFim, Date dataReceb, Date dataContratosLancadosAPartir, boolean aceitaMatriculaRematricula, boolean aceitaManutencaoModalidade, boolean calcularComissaoProdutos, boolean retiraEdicaoPagamento,boolean retirarRecebiveisComPendencia) throws Exception {
        List<ComissaoRel> lista = new ArrayList<ComissaoRel>();
        StringBuilder sql = ThreadDemonstrativoFinanceiro.consultaFaturamentoRecebido(codEmpresa, dataIni, dataFim, dataReceb, dataContratosLancadosAPartir, true, null, false, null, null, null,false,null,retiraEdicaoPagamento,retirarRecebiveisComPendencia, null);
        obterItens(lista, sql, aceitaMatriculaRematricula, true, aceitaManutencaoModalidade, calcularComissaoProdutos);
        return lista;
    }

    private List<ComissaoRel> consultarCompetencia(Integer codEmpresa, int ano, String mes, boolean aceitaMatriculaRematricula, boolean aceitaManutencaoModalidade, boolean calcularComissaoProdutos, Date dataContratosLancadosAPartir) throws Exception {
        List<ComissaoRel> lista = new ArrayList<ComissaoRel>();
        StringBuilder sql = ThreadDemonstrativoFinanceiro.consultaCompetenciaFaturamento(ano, mes, null, null, codEmpresa, true, TipoRelatorioDF.COMPETENCIA, false, dataContratosLancadosAPartir);
        obterItens(lista, sql, aceitaMatriculaRematricula, false, aceitaManutencaoModalidade, calcularComissaoProdutos);
        return lista;
    }

    private List<ComissaoRel> consultarCompetenciaServlet(Integer codEmpresa, int ano, Date dataIni, Date dataFim, String mes, boolean aceitaMatriculaRematricula, boolean aceitaManutencaoModalidade, boolean calcularComissaoProdutos, Date dataContratosLancadosAPartir, Integer limit, Integer offSet) throws Exception {
        List<ComissaoRel> lista = new ArrayList<ComissaoRel>();
        StringBuilder sql = ThreadDemonstrativoFinanceiro.consultaCompetenciaFaturamentoPaginada(ano, mes, dataIni, dataFim, codEmpresa, true, TipoRelatorioDF.COMPETENCIA, false, dataContratosLancadosAPartir, limit, offSet);
        obterItens(lista, sql, aceitaMatriculaRematricula, false, aceitaManutencaoModalidade, calcularComissaoProdutos);
        return lista;
    }

    private List<ComissaoRel> consultarFaturamento(Integer codEmpresa, Date dataIni, Date dataFim, boolean aceitaMatriculaRematricula, boolean aceitaManutencaoModalidade, boolean calcularComissaoProdutos) throws Exception {
        List<ComissaoRel> lista = new ArrayList<ComissaoRel>();
        StringBuilder sql = ThreadDemonstrativoFinanceiro.consultaCompetenciaFaturamento(0, null, dataIni, dataFim, codEmpresa, true, TipoRelatorioDF.FATURAMENTO, true, null);
        obterItens(lista, sql, aceitaMatriculaRematricula, false, aceitaManutencaoModalidade, calcularComissaoProdutos);
        return lista;
    }

    private List<ComissaoRel> consultarReceita(Integer codEmpresa, Date dataIni, Date dataFim, Date dataReceb, boolean aceitaMatriculaRematricula, boolean aceitaManutencaoModalidade, boolean calcularComissaoProdutos,boolean retirarRecebiveisComPendencia, Date dataContratoLancamentoAPartir, boolean considerarCompensacaoOriginal) throws Exception {
        List<ComissaoRel> lista = new ArrayList<ComissaoRel>();

        StringBuilder sql = ThreadDemonstrativoFinanceiro.consultaPagamentoEmCartaoCredito(codEmpresa, dataIni, dataFim, true, dataReceb, false, null, false, null, null, false, null, "", dataContratoLancamentoAPartir, considerarCompensacaoOriginal);
        obterItens(lista, sql, aceitaMatriculaRematricula, true,aceitaManutencaoModalidade, calcularComissaoProdutos);

        sql = ThreadDemonstrativoFinanceiro.consultaPagamentosEmCheque(codEmpresa, dataIni, dataFim, true, dataReceb, false, null, false, null, null,retirarRecebiveisComPendencia, false, null, "", dataContratoLancamentoAPartir, considerarCompensacaoOriginal);
        obterItens(lista, sql, aceitaMatriculaRematricula, true, aceitaManutencaoModalidade, calcularComissaoProdutos);

        sql = ThreadDemonstrativoFinanceiro.consultaPagamentosDiferentesDeChequeECartaoCredito(codEmpresa, dataIni, dataFim, true, dataReceb, false, null, false, null, null, null, dataContratoLancamentoAPartir);
        obterItens(lista, sql, aceitaMatriculaRematricula, true, aceitaManutencaoModalidade, calcularComissaoProdutos);
        return lista;
    }

    private void    obterItens(List<ComissaoRel> lista, StringBuilder sql, boolean aceitaMatriculaRematricula, boolean temRecibo, boolean aceitaManutencaoModalidade, boolean calcularComissaoProdutos) throws Exception {
        try (PreparedStatement ps = getCon().prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ComissaoRel comissaoRel = new ComissaoRel();
                    comissaoRel.setValor(rs.getDouble("valor"));
                    comissaoRel.setFormaPagamento(rs.getString("pagDesc"));
                    comissaoRel.setProdutosPagos(rs.getString("produtospagos"));
                    comissaoRel.setDataPagamento(rs.getTimestamp("datalancamento"));
                    comissaoRel.setDataCompensacao(rs.getTimestamp("datacompesancao"));
                    comissaoRel.setCodigoRecibo(rs.getInt("recibopagamento"));
                    if (temRecibo) {
                        comissaoRel.getResponsavelRecebimento().setCodigo(rs.getInt("atendente"));
                    }
                    comissaoRel.processarProdutosPagos(aceitaMatriculaRematricula, aceitaManutencaoModalidade, calcularComissaoProdutos);
                    lista.add(comissaoRel);
                }
            }
        }
    }


    private List<ComissaoRel> processarContratos(List<ComissaoRel> lista, Integer codEmpresa,
                                                 String impressaoPor, String tipoValorComissoes) throws Exception {
        List<ComissaoRel> listaSaida = new ArrayList<ComissaoRel>();

        List<ComissaoGeralConfiguracaoVO> comissoesEmpresa = consultarPorEmpresa(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Map<ComissaoGeralConfiguracaoVO, ArrayList<ComissaoRel>> mapaComissoes = new HashMap<ComissaoGeralConfiguracaoVO, ArrayList<ComissaoRel>>();
        Map<ComissaoProdutoConfiguracaoVO, ArrayList<ComissaoRel>> mapaComissoesProdutos = new HashMap<ComissaoProdutoConfiguracaoVO, ArrayList<ComissaoRel>>();

        for (ComissaoRel item : lista) {
            List<ContratoVO> contratos = obterContratosProdutos(item.getContratos());
            for (ContratoVO contrato : contratos) {
                //Se o plano não for comissionável, não irá nem colocar na lista.
                if (contrato.getPlano().getComissao()) {
                    if ("TF".equals(contrato.getSituacaoContrato())) {
                        continue;
                    }
                    ComissaoRel comissaoRel = new ComissaoRel();
                    comissaoRel.setCodigoContrato(contrato.getCodigo());
                    comissaoRel.setSituacaoContrato(contrato.getSituacao_Apresentar());
                    comissaoRel.setContratoAgendadoEspontaneo(contrato.getContratoAgendadoEspontaneo());
                    comissaoRel.setDuracaoContrato(contrato.getContratoDuracao().getNumeroMeses());
                    comissaoRel.setValorContrato(contrato.getValorFinal());
                    comissaoRel.setTipoContrato(contrato.getSituacaoContrato().equals("TF") ? obterTipoContratoOrigemTransferencia(contrato.getContratoOrigemTransferencia()) : contrato.getSituacaoContrato());
                    comissaoRel.setNomePlano(contrato.getPlano().getDescricao());

                    comissaoRel.setResponsavelLancamento(contrato.getResponsavelContrato());

                    if (impressaoPor.equals("SO")) {
                        Usuario usuario = new Usuario();
                        UsuarioVO usuarioVO = usuario.consultarPorCodigoColaborador(item.getResponsavelRecebimento().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        comissaoRel.setResponsavelLancamento(usuarioVO);
                    }

                    comissaoRel.setMatriculaCliente(contrato.getCliente().getMatricula());
                    comissaoRel.setCodigoCliente(contrato.getCliente().getCodigo());

                    //Filtro de Operador não pode excluir os filtros de consultores
                    if (item.getResponsavelRecebimento().getCodigo() != 0) {
                        ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(item.getResponsavelRecebimento().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        comissaoRel.setResponsavelRecebimento(colaboradorVO);
                    }

                    //Impressão por Consultor Atual
                    if (impressaoPor.equals("CA")) {
                        ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarConsultorDoClienteNaData(contrato.getCliente().getCodigo(), Calendario.hoje());
                        if (!colaboradorVO.getCodigo().equals(0)){
                            colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(colaboradorVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                            comissaoRel.setConsultorResponsavel(colaboradorVO);
                        }else {
                            throw new Exception("Vínculo de Consultor não encontrado para a matrícula " + contrato.getCliente().getMatricula()+", verifique o cadastro do aluno!");
                        }
                    } else if (impressaoPor.equals("CO")) {
                        comissaoRel.setConsultorResponsavel(contrato.getConsultor());
                    }

                    comissaoRel.setCodigoPessoa(contrato.getCliente().getPessoa().getCodigo());
                    comissaoRel.setNomePessoa(contrato.getCliente().getPessoa().getNome());

                    comissaoRel.setValor(item.getMapaContratoValor().get(contrato.getCodigo()));
                    comissaoRel.setFormaPagamento(item.getFormaPagamento());

                    comissaoRel.setDataCompensacao(item.getDataCompensacao());
                    comissaoRel.setDataPagamento(item.getDataPagamento());
                    comissaoRel.setCodigoRecibo(item.getCodigoRecibo());

                    comissaoRel.setProdutosPagos(item.getProdutosPagos());
                    comissaoRel.setMapaContratoValor(item.getMapaContratoValor());

                    comissaoRel.calcularValorComissao(comissoesEmpresa, mapaComissoes, tipoValorComissoes);

                    if(item.getDevolucaoCheque()){
                        comissaoRel.setValorDaComissao(comissaoRel.getValorDaComissao() * -1.0);
                        comissaoRel.setValor(comissaoRel.getValor() * -1.0);
                        comissaoRel.setNomePlano("DEVOLUÇÃO CH");
                    }else if(item.getRetornoCheque()){
                        comissaoRel.setNomePlano("RETORNO CH");
                    }
                    listaSaida.add(comissaoRel);
                }
            }

            if (!item.getMapaProdutoValor().isEmpty()) {
                List<MovProdutoVO> movProdutos = obterMovProdutos(item.getMovProdutos());
                for (MovProdutoVO movProdutoVO : movProdutos) {
                    ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(movProdutoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

                    //Se o produto não for comissionável não irá nem analisar
                    if (movProdutoVO.isComissionavel() && clienteVO.getCodigo() != 0) {
                        ComissaoRel comissaoRel = new ComissaoRel();
                        comissaoRel.setCodigoContrato(movProdutoVO.getCodigo());
                        comissaoRel.setValorContrato(movProdutoVO.getTotalFinal());
                        comissaoRel.setNomePlano(movProdutoVO.getDescricao());

                        comissaoRel.setResponsavelLancamento(movProdutoVO.getResponsavelLancamento());

                        comissaoRel.setMatriculaCliente(clienteVO.getMatricula());
                        comissaoRel.setCodigoCliente(clienteVO.getCodigo());

                        //Filtro de Operador não pode excluir os filtros de consultores
                        if (item.getResponsavelRecebimento().getCodigo() != 0) {
                            ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(item.getResponsavelRecebimento().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                            comissaoRel.setResponsavelRecebimento(colaboradorVO);
                        }

                        if (impressaoPor.equals("SO")) {
                            Usuario usuario = new Usuario();
                            UsuarioVO usuarioVO = usuario.consultarPorCodigoColaborador(item.getResponsavelRecebimento().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            comissaoRel.setResponsavelLancamento(usuarioVO);
                        }

                        //Impressão por Consultor Atual
                        if (impressaoPor.equals("CA")) {
                            ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarConsultorDoClienteNaData(clienteVO.getCodigo(), Calendario.hoje());
                            colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(colaboradorVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                            comissaoRel.setConsultorResponsavel(colaboradorVO);
                        } else if (impressaoPor.equals("CO")) {
                            ColaboradorVO colVO;
                            if (!UteisValidacao.emptyNumber(movProdutoVO.getContrato().getCodigo())) {
                                ContratoVO contratoVO = getFacade().getContrato().consultarPorChavePrimaria(movProdutoVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                                colVO = contratoVO.getConsultor();
                            } else {
                                colVO = getFacade().getReciboClienteConsultor().consultarPorReciboCliente(movProdutoVO.getEmpresa().getCodigo(), item.getCodigoRecibo(), clienteVO.getCodigo());
                                if (colVO == null) {
                                    colVO = getFacade().getColaborador().consultarPorCodigoUsuario(movProdutoVO.getResponsavelLancamento().getCodigo(), movProdutoVO.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
                        }
                            }
                            comissaoRel.setConsultorResponsavel(colVO);
                        }


                        comissaoRel.setCodigoPessoa(clienteVO.getPessoa().getCodigo());
                        comissaoRel.setNomePessoa(clienteVO.getPessoa().getNome());

                        comissaoRel.setValor(item.getMapaProdutoValor().get(movProdutoVO.getCodigo()));
                        comissaoRel.setFormaPagamento(item.getFormaPagamento());

                        comissaoRel.setDataCompensacao(item.getDataCompensacao());
                        comissaoRel.setDataPagamento(item.getDataPagamento());
                        comissaoRel.setCodigoRecibo(item.getCodigoRecibo());

                        comissaoRel.setProdutosPagos(item.getProdutosPagos());
                        comissaoRel.setMapaProdutoValor(item.getMapaProdutoValor());

                        comissaoRel.calcularValorComissaoProduto(movProdutoVO, mapaComissoesProdutos, tipoValorComissoes);

                        if(item.getDevolucaoCheque()){
                            comissaoRel.setValorDaComissao(comissaoRel.getValorDaComissao() * -1.0);
                            comissaoRel.setValor(comissaoRel.getValor() * -1.0);
                            comissaoRel.setNomePlano("DEVOLUÇÃO DE CH");
                        }if(item.getRetornoCheque()){
                            comissaoRel.setNomePlano("RETORNO CH");
                        }
                        listaSaida.add(comissaoRel);
                    }
                }
            }
        }

        listaSaida = processarListaSaida(tipoValorComissoes, listaSaida);

        return listaSaida;
    }

    private List<ComissaoRel> processarListaSaida(String tipoValorComissoes, List<ComissaoRel> listaSaida) {
        if (!tipoValorComissoes.equals("FIXO")) {
            return listaSaida;
        }

        Map<Integer, Integer> contagemContratos = new HashMap<Integer, Integer>();

        for (ComissaoRel comissaoRel : listaSaida) {
            Integer qtd = contagemContratos.get(comissaoRel.getCodigoContrato());
            if (qtd == null) {
                qtd = 0;
                contagemContratos.put(comissaoRel.getCodigoContrato(), 0);
            }
            contagemContratos.put(comissaoRel.getCodigoContrato(), qtd + 1);
        }

        return listaSaida;
    }

    public List<ComissaoGeralConfiguracaoVO> consultarPorEmpresa(Integer empresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT\n" +
                "  *\n" +
                "FROM comissaogeralconfiguracao cgc\n" +
                "WHERE cgc.empresa = ?\n" +
                "ORDER BY duracao";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, empresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    private List<ContratoVO> obterContratosProdutos(String codigosContrato) throws Exception {
        if (!codigosContrato.isEmpty()) {
            StringBuilder sql = new StringBuilder("SELECT\n");
            sql.append("  *\n");
            sql.append("from contrato\n");
            sql.append("where codigo in (").append(codigosContrato).append(")\n");
            try (PreparedStatement ps = getCon().prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    return Contrato.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, getCon());
                }
            }
        } else {
            return new ArrayList<ContratoVO>();
        }
    }

    private List<MovProdutoVO> obterMovProdutos(String codigoMovProdutos) throws Exception {
        if (!codigoMovProdutos.isEmpty()) {
            StringBuilder sql = new StringBuilder("SELECT\n");
            sql.append("  *\n");
            sql.append("from movproduto\n");
            sql.append("where codigo in (").append(codigoMovProdutos).append(")\n");
            try (PreparedStatement ps = getCon().prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    return MovProduto.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_COMISSAO, getCon());
                }
            }
        } else {
            return new ArrayList<MovProdutoVO>();
        }
    }


    private String obterTipoContratoOrigemTransferencia(Integer codigoContratoOrigem) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT\n");
        sql.append("  contratoorigemtransferencia, situacaocontrato \n");
        sql.append("from contrato\n");
        sql.append("where codigo =").append(codigoContratoOrigem);
        try (PreparedStatement ps = getCon().prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                if(rs.next()){
                    if(rs.getString("situacaocontrato").equals("TF")){
                        return obterTipoContratoOrigemTransferencia(rs.getInt("contratoorigemtransferencia"));
                    } else {
                        return rs.getString("situacaocontrato");
                    }
                }
            }
        }
        return "MA";
    }
}
