package negocio.facade.jdbc.contrato;

import negocio.comuns.contrato.ContratoVO;
import negocio.interfaces.contrato.HistoricoContratoInterfaceFacade;
import java.util.Iterator;
import java.util.Date;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>HistoricoContratoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>HistoricoContratoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see HistoricoContratoVO
 * @see SuperEntidade
 * @see Contrato
 */
public class HistoricoContrato extends SuperEntidade implements HistoricoContratoInterfaceFacade {

    public HistoricoContrato() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public HistoricoContrato(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>HistoricoContratoVO</code>.
     */
    public HistoricoContratoVO novo() throws Exception {
        incluir(getIdEntidade());
        HistoricoContratoVO obj = new HistoricoContratoVO();
        return obj;
    }

    public void incluir(HistoricoContratoVO obj, Boolean controleAcesso) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, controleAcesso);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>HistoricoContratoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>HistoricoContratoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(HistoricoContratoVO obj, Boolean controleAcesso) throws Exception {
        HistoricoContratoVO.validarDados(obj);
        if (controleAcesso) {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO HistoricoContrato( contrato, descricao, "
                + "responsavelRegistro, responsavelLiberacaoMudancaHistorico, "
                + "dataRegistro, situacaoRelativaHistorico, dataInicioSituacao, "
                + "dataFinalSituacao, tipoHistorico, retornoManual, DataInicioTemporal ) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getContrato() != 0) {
                sqlInserir.setInt(1, obj.getContrato());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setString(2, obj.getDescricao());
            if (obj.getResponsavelRegistro().getCodigo() != 0) {
                sqlInserir.setInt(3, obj.getResponsavelRegistro().getCodigo());
            } else {
                sqlInserir.setNull(3, 0);
            }
            if (obj.getResponsavelLiberacaoMudancaHistorico().getCodigo() != 0) {
                sqlInserir.setInt(4, obj.getResponsavelLiberacaoMudancaHistorico().getCodigo());
            } else {
                sqlInserir.setNull(4, 0);
            }
            sqlInserir.setDate(5, Uteis.getDataJDBC(obj.getDataRegistro()));
            sqlInserir.setString(6, obj.getSituacaoRelativaHistorico());
            sqlInserir.setDate(7, Uteis.getDataJDBC(obj.getDataInicioSituacao()));
            sqlInserir.setDate(8, Uteis.getDataJDBC(obj.getDataFinalSituacao()));
            sqlInserir.setString(9, obj.getTipoHistorico());
            sqlInserir.setBoolean(10, obj.isRetornoManual());
            sqlInserir.setDate(11, Uteis.getDataJDBC(obj.getDataInicioTemporal()));
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        }
    }

    public void alterar(HistoricoContratoVO obj) throws Exception {
        try {
            alterarSemCommit(obj, true);
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterar(HistoricoContratoVO obj, Boolean controleAcesso) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj, controleAcesso);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>HistoricoContratoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>HistoricoContratoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterarSemCommit(HistoricoContratoVO obj, Boolean controleAcesso) throws Exception {
        HistoricoContratoVO.validarDados(obj);
        if (controleAcesso) {
            alterar(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "UPDATE HistoricoContrato set contrato=?, "
                + "descricao=?, responsavelRegistro=?, "
                + "responsavelLiberacaoMudancaHistorico=?, "
                + "dataRegistro=?, situacaoRelativaHistorico=?, "
                + "dataInicioSituacao=?, dataFinalSituacao=?, "
                + "tipoHistorico=?, retornoManual=?, dataInicioTemporal=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
        if (obj.getContrato().intValue() != 0) {
            sqlAlterar.setInt(1, obj.getContrato().intValue());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        sqlAlterar.setString(2, obj.getDescricao());
        if (obj.getResponsavelRegistro().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(3, obj.getResponsavelRegistro().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(3, 0);
        }
        if (obj.getResponsavelLiberacaoMudancaHistorico().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(4, obj.getResponsavelLiberacaoMudancaHistorico().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(4, 0);
        }
        sqlAlterar.setDate(5, Uteis.getDataJDBC(obj.getDataRegistro()));
        sqlAlterar.setString(6, obj.getSituacaoRelativaHistorico());
        sqlAlterar.setDate(7, Uteis.getDataJDBC(obj.getDataInicioSituacao()));
        sqlAlterar.setDate(8, Uteis.getDataJDBC(obj.getDataFinalSituacao()));
        sqlAlterar.setString(9, obj.getTipoHistorico());
        sqlAlterar.setBoolean(10, obj.isRetornoManual());
        sqlAlterar.setDate(11, Uteis.getDataJDBC(obj.getDataInicioTemporal()));
        sqlAlterar.setInt(12, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>HistoricoContratoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>HistoricoContratoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(HistoricoContratoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM HistoricoContrato WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }
    }

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo 
     * <code>String situacaoRelativaHistorico</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoRelativaHistorico(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE upper( situacaoRelativaHistorico ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY situacaoRelativaHistorico";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo 
     * <code>Date dataRegistro</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE ((dataRegistro >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataRegistro <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataRegistro";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataInicio(Date prmIni, Integer contrato, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE (dataInicioSituacao > '" + Uteis.getDataJDBC(prmIni) + "') and contrato= " + contrato.intValue() + " ORDER BY dataInicioSituacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo 
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT HistoricoContrato.* FROM HistoricoContrato, Colaborador WHERE HistoricoContrato.responsavelRegistro = Colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo 
     * <code>situacao</code> da classe <code>Contrato</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoContrato(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT HistoricoContrato.* FROM HistoricoContrato, Contrato WHERE HistoricoContrato.contrato = Contrato.codigo and upper( Contrato.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY dataInicioSituacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HistoricoContrato</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY contrato";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoOperacao</code> através do valor do atributo
     * <code>Integer contrato</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<HistoricoContratoVO> consultarPorContrato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE contrato = " + valorConsulta + " ORDER BY datainiciosituacao,datafinalsituacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoOperacao</code> através do valor do atributo
     * <code>Integer contrato</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoOperacaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorContratoDescricao(Integer valorConsulta, String descricao, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE contrato = " + valorConsulta.intValue() + " and descricao = '" + descricao.toUpperCase() + "' ORDER BY contrato";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     */
    public static List<HistoricoContratoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<HistoricoContratoVO> vetResultado = new ArrayList<HistoricoContratoVO>();
        while (tabelaResultado.next()) {
            HistoricoContratoVO obj = new HistoricoContratoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>HistoricoContratoVO</code>.
     * @return  O objeto da classe <code>HistoricoContratoVO</code> com os dados devidamente montados.
     */
    public static HistoricoContratoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        HistoricoContratoVO obj = new HistoricoContratoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setContrato(new Integer(dadosSQL.getInt("contrato")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.getResponsavelRegistro().setCodigo(new Integer(dadosSQL.getInt("responsavelRegistro")));
        obj.getResponsavelLiberacaoMudancaHistorico().setCodigo(new Integer(dadosSQL.getInt("responsavelLiberacaoMudancaHistorico")));
        obj.setDataRegistro(dadosSQL.getDate("dataRegistro"));
        obj.setSituacaoRelativaHistorico(dadosSQL.getString("situacaoRelativaHistorico"));
        obj.setDataInicioSituacao(dadosSQL.getDate("dataInicioSituacao"));
        obj.setDataFinalSituacao(dadosSQL.getDate("dataFinalSituacao"));
        obj.setTipoHistorico(dadosSQL.getString("tipoHistorico"));
        obj.setRetornoManual(dadosSQL.getBoolean("retornoManual"));
        obj.setDataInicioTemporal(dadosSQL.getDate("dataInicioTemporal"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosResponsavelRegistro(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosResponsavelLiberacaoMudancaHistorico(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ColaboradorVO</code> relacionado ao objeto <code>HistoricoContratoVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelLiberacaoMudancaHistorico(HistoricoContratoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelLiberacaoMudancaHistorico().getCodigo().intValue() == 0) {
            obj.setResponsavelLiberacaoMudancaHistorico(new UsuarioVO());
            return;
        }
        Usuario usuarioDAO = new Usuario(con);
        obj.setResponsavelLiberacaoMudancaHistorico(usuarioDAO.consultarPorChavePrimaria(obj.getResponsavelLiberacaoMudancaHistorico().getCodigo(), nivelMontarDados));
        usuarioDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ColaboradorVO</code> relacionado ao objeto <code>HistoricoContratoVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelRegistro(HistoricoContratoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelRegistro().getCodigo().intValue() == 0) {
            obj.setResponsavelRegistro(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setResponsavelRegistro(usuario.consultarPorChavePrimaria(obj.getResponsavelRegistro().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>HistoricoContratoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>HistoricoContrato</code>.
     * @param <code>contrato</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirHistoricoContratos(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM HistoricoContrato WHERE (contrato = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
        sqlExcluir.setInt(1, contrato.intValue());
        sqlExcluir.execute();
    }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>HistoricoContratoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirHistoricoContratos</code> e <code>incluirHistoricoContratos</code> disponíveis na classe <code>HistoricoContrato</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarHistoricoContratos(Integer contrato, List objetos) throws Exception {
        excluirHistoricoContratos(contrato);
        incluirHistoricoContratos(contrato, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>HistoricoContratoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>contrato.Contrato</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirHistoricoContratos(Integer contratoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            HistoricoContratoVO obj = (HistoricoContratoVO) e.next();
            obj.setContrato(contratoPrm);
            incluirSemCommit(obj, true);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>HistoricoContratoVO</code> relacionados a um objeto da classe <code>contrato.Contrato</code>.
     * @param contrato  Atributo de <code>contrato.Contrato</code> a ser utilizado para localizar os objetos da classe <code>HistoricoContratoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>HistoricoContratoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarHistoricoContratos(Integer contrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM HistoricoContrato WHERE contrato = ? order by dataRegistro";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, contrato.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    HistoricoContratoVO novoObj = new HistoricoContratoVO();
                    novoObj = HistoricoContrato.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>HistoricoContratoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public HistoricoContratoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM HistoricoContrato WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( HistoricoContrato ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>HistoricoContratoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public HistoricoContratoVO obterUltimaHistoricoContratoDoMes(Integer codigoPrm, Integer mes, Integer ano, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select * from historicoContrato  as hc "
                + "where hc.contrato = " + codigoPrm + " and date_part('month', hc.datainiciosituacao) = " + mes + " "
                + "and  date_part('year', hc.datainiciosituacao) = " + ano + " "
                + "and hc.datainiciosituacao in (select MAX(h.datainiciosituacao) from historicoContrato  as h "
                + "where h.contrato = " + codigoPrm + "  and date_part('month', h.datainiciosituacao) = " + mes + " "
                + "and  date_part('year', hc.datainiciosituacao) = " + ano + " )";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContratoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public HistoricoContratoVO obterHistoricoContratoPorDataEspecifica(Integer contrato, Date data, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE contrato = " + contrato.intValue() + " and ((dataInicioSituacao <= '" + Uteis.getDataJDBC(data) + "') and (dataFinalSituacao >= '" + Uteis.getDataJDBC(data) + "'))";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

        public List <HistoricoContratoVO> obterHistoricoContratoFuturo(Integer contrato, Date data, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE contrato = " + contrato.intValue() + " and dataInicioSituacao > '" + Uteis.getDataJDBC(data) + "'";
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                    return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
                }
            }
        }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public HistoricoContratoVO obterHistoricoContratoPorCodigoContratoDataInicioDataFim(Integer contrato, Date data, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE contrato = " + contrato.intValue() + " and  ((dataInicioSituacao <= '" + Uteis.getDataJDBC(data) + "') and (dataFinalSituacao >= '" + Uteis.getDataJDBC(data) + "'))";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public HistoricoContratoVO obterHistoricoContratoPorStatusCodigoContratoDataInicioDataFim(Integer contrato, Date data, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE contrato = " + contrato.intValue() +
                " AND tipoHistorico = 'VE' AND  ((dataInicioSituacao <= '" + Uteis.getDataJDBC(data) + "') AND (dataFinalSituacao >= '" + Uteis.getDataJDBC(data) + "'))";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public HistoricoContratoVO obterHistoricoContratoLancadoFuturoPorCodigoContratoDataInicioDataFim(Integer contrato, Date data, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE contrato = " + contrato.intValue() + " and  ((dataInicioSituacao >= '" + Uteis.getDataJDBC(data) + "') and (dataFinalSituacao >= '" + Uteis.getDataJDBC(data) + "')) order by dataInicioSituacao limit 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public HistoricoContratoVO obterUltimoHistoricoContratoPorContratoTipoHistorico(Integer contrato, String tipoHistorico, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE datafinalsituacao IN (SELECT MAX(datafinalsituacao) FROM HistoricoContrato WHERE contrato = " + contrato.intValue() + " and tipoHistorico ='" + tipoHistorico.toUpperCase() + "') and contrato = " + contrato.intValue()+ " and tipoHistorico ='" + tipoHistorico.toUpperCase() + "'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public HistoricoContratoVO obterUltimoHistoricoContratoPorContrato(Integer contrato,
            Date dataBase, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato "
                + "WHERE '" + Uteis.getData(dataBase) + "' "
                + "between datainiciosituacao and datafinalsituacao "
                + "and contrato = " + contrato.intValue() + " order by codigo ASC";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContratoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public HistoricoContratoVO obterUltimoHistoricoContratoPorContrato(Integer contrato,
            int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato where contrato = " + contrato.intValue() + " order by datafinalsituacao DESC limit 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new HistoricoContratoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public HistoricoContratoVO obterHistoricoContratoPorDescricao(Integer contrato, String descricao, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE contrato = " + contrato.intValue() + " and descricao ='" + descricao.toUpperCase() + "'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public Boolean obterHistoricoContratoPorCodigoContratoTipoHistoricoDataInicioDataFim(Integer contrato, String tipoHistorico, Date data, int nivelMontarDados) throws Exception {
        String sqlStr = "select exists (SELECT codigo FROM HistoricoContrato WHERE contrato = "
                + contrato.intValue() + " and tipoHistorico ='" + tipoHistorico.toUpperCase()
                + "' and  ((dataInicioSituacao <= '" + Uteis.getDataJDBC(data)
                + "') and (dataFinalSituacao >= '" + Uteis.getDataJDBC(data) + "')) limit 1) as existe";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    public  void excluirHistoricosFuturosContrato (Integer contrato, Date data) throws Exception{
        String sql = "DELETE FROM HistoricoContrato WHERE contrato = ? and datainiciosituacao >= ? ";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
        sqlExcluir.setInt(1, contrato.intValue());
        sqlExcluir.setDate(2, Uteis.getDataJDBC(data));
        sqlExcluir.execute();
    }
    }

    public boolean obterHistoricoContratoPorCodigoContratoDecricao(Integer contrato, String descricao, int nivelMontarDados) throws Exception {
        String sqlStr = "select exists (SELECT codigo FROM HistoricoContrato WHERE contrato = " + contrato.intValue() + " and descricao ='" + descricao.toUpperCase() + "') as existe";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    public Boolean obterHistoricoContratoPorCodigoContratoTipoHistorico(Integer contrato, String tipoHistorico) throws Exception {
        String sqlStr = "select exists (SELECT codigo FROM HistoricoContrato WHERE contrato = " + contrato.intValue() + " and tipoHistorico ='" + tipoHistorico.toUpperCase() + "') as existe";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    public List<Integer> obterListaTodosHistoricoContrato() throws Exception {
        List<Integer> lista = new ArrayList<Integer>();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select count(contrato) from historicocontrato group by contrato", con)) {
            while (rs.next()) {
                lista.add(rs.getInt("count"));
            }
        }
        return lista;
    }

    @Override
    public Boolean obterHistoricoContratoPorCodigoContratoDecricaoDataInicio(Integer contrato, String descricao, Date data, int nivelMontarDados) throws Exception {
        String sqlStr = "select exists (SELECT codigo FROM HistoricoContrato WHERE contrato = " + contrato.intValue() + " and descricao ='" + descricao.toUpperCase() + "' and  ((dataInicioSituacao <= '" + Uteis.getDataJDBC(data) + "'))) as existe";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    @Override
    public Integer consultarQuantidadeHistoricoContratoPorSituacao(Date dataInicio,
            Date dataFim, String situacao, Integer empresa) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("select count(h.codigo) as total from historicocontrato h ");
        sb.append("inner join contrato c on c.codigo = h.contrato ");
        sb.append("where h.tipohistorico = '%1$s' ");
        sb.append("and c.empresa = %2$d ");
        sb.append("and (cast (h.datainiciosituacao as date) between '%3$s' and '%4$s 23:59:59')");

        String sqlStr = String.format(sb.toString(),
                new Object[]{
                    situacao,
                    empresa,
                    Uteis.getDataJDBC(dataInicio),
                    Uteis.getDataJDBC(dataFim)});

        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return tabelaResultado.getInt("total");
            }
        }
    }
    
    public void excluirHistoricoAVencerContrato(Integer contrato) throws Exception{
    	String sql = "DELETE FROM historicocontrato WHERE codigo in (select codigo from historicocontrato where  contrato = "+contrato +" AND tipohistorico LIKE 'AV' order by datafinalsituacao DESC limit 1)";
    	executarConsulta(sql, con);
    }
    
    @Override
    public List<HistoricoContratoVO> consultarPorContratoSimplificado(Integer valorConsulta) throws Exception {
        String sqlStr = "SELECT descricao, dataregistro, datainiciosituacao, datafinalsituacao, u.nome as responsavelregistro "+
                        " FROM historicocontrato hc\n" +
                        " LEFT JOIN usuario u ON u.codigo = hc.responsavelregistro\n" +
                        " WHERE hc.contrato = " + valorConsulta.intValue() + 
                        " ORDER BY datainiciosituacao,datafinalsituacao";
        List<HistoricoContratoVO> lista;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                lista = new ArrayList<HistoricoContratoVO>();
                while (rs.next()) {
                    HistoricoContratoVO hist = new HistoricoContratoVO();
                    hist.setDescricao(rs.getString("descricao"));
                    hist.setDataRegistro(rs.getDate("dataregistro"));
                    hist.setDataInicioSituacao(rs.getDate("datainiciosituacao"));
                    hist.setDataFinalSituacao(rs.getDate("datafinalsituacao"));
                    hist.setResponsavelRegistro(new UsuarioVO());
                    hist.getResponsavelRegistro().setNome(rs.getString("responsavelregistro"));
                    lista.add(hist);
                }
            }
        }
        return lista;
    }
    
    
    public Date obterDataInicioUltimoHistoricoOperacaoContrato(Integer contrato) throws Exception{
        String sqlStr = "SELECT datainiciosituacao FROM HistoricoContrato where contrato = " + contrato.intValue() + " and tipohistorico in ('AT', 'RA', 'CA', 'CR', 'RC','RT','TR') order by datainiciosituacao DESC limit 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return tabelaResultado.getDate("datainiciosituacao");
            }
        }
    }
    
    public List<HistoricoContratoVO> obterHistoricosContratoPorDataEspecifica(Integer contrato, Date data, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM HistoricoContrato WHERE contrato = " + contrato.intValue() + " and ((dataInicioSituacao <= '" + Uteis.getDataJDBC(data) + "') and (dataFinalSituacao >= '" + Uteis.getDataJDBC(data) + "'))";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public void incluirPrimeiroBaseadoSituacaoContrato(ContratoVO contratoVO, boolean podePossuirHistorico) throws Exception {
        Date datafinalHistorico = contratoVO.getVigenciaAteAjustada();

        List<HistoricoContratoVO> listaHistoricos;
        if (podePossuirHistorico) {
            listaHistoricos = consultarPorContrato(contratoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!listaHistoricos.isEmpty()) {
                if (listaHistoricos.get(0).getMatricula()
                        || listaHistoricos.get(0).getReMatricula()
                        || listaHistoricos.get(0).getRenovacao()
                        || listaHistoricos.get(0).getTipoHistorico().equals("CT")
                        || listaHistoricos.get(0).getTransferidoDeOutraEmpresa()) {
                    return;
                }
                datafinalHistorico = listaHistoricos.get(0).getDataInicioSituacao();
            }
        }

        HistoricoContratoVO historicoContratoVO = new HistoricoContratoVO();
        historicoContratoVO.setContrato(contratoVO.getCodigo());
        historicoContratoVO.setResponsavelRegistro(contratoVO.getResponsavelContrato());
        historicoContratoVO.setDataRegistro(contratoVO.getDataLancamento());
        historicoContratoVO.setDataInicioSituacao(contratoVO.getVigenciaDe());
        historicoContratoVO.setDataFinalSituacao(datafinalHistorico);
        historicoContratoVO.setDataInicioTemporal(contratoVO.getVigenciaDe());
        if (contratoVO.getSituacaoContrato().equals("MA")) {
            if (UteisValidacao.emptyNumber(contratoVO.getContratoOrigemTransferencia())) {
                historicoContratoVO.setDescricao("Matriculado");
                historicoContratoVO.setTipoHistorico("MA");
            } else {
                historicoContratoVO.setDescricao(" TRANSFERENCIA (CONTRATO ANTERIOR: " + contratoVO.getContratoOrigemTransferencia() + ")");
                historicoContratoVO.setTipoHistorico("CT");
            }
        } else if (contratoVO.getSituacaoContrato().equals("RN")) {
            historicoContratoVO.setDescricao("Renovado");
            historicoContratoVO.setTipoHistorico("RN");
        } else if (contratoVO.getSituacaoContrato().equals("RE")) {
            historicoContratoVO.setDescricao("Rematriculado");
            historicoContratoVO.setTipoHistorico("RE");
        }
        incluirSemCommit(historicoContratoVO, false);
    }
}
