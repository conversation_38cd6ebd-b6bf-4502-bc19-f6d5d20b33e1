package negocio.facade.jdbc.contrato;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoCondicaoPagamentoVO;
import negocio.comuns.contrato.ContratoDuracaoCreditoTreinoVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ContratoHorarioVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoModalidadeVezesSemanaVO;
import negocio.comuns.contrato.ContratoTextoPadraoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.plano.AlunoHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.*;
import negocio.comuns.plano.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Reposicao;
import negocio.facade.jdbc.plano.CondicaoPagamento;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.PlanoModalidadeVezesSemana;
import negocio.facade.jdbc.plano.Turma;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.contrato.ControleCreditoTreinoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.integracao.treino.dto.SinteticoMsDTO;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 17/11/2015.
 */
public class ControleCreditoTreino extends SuperEntidade implements ControleCreditoTreinoInterfaceFacade {

    private Contrato contratoDao;
    private ContratoDuracaoCreditoTreino contratoDuracaoCreditoTreinoDao;
    private ContratoModalidadeHorarioTurma contratoModalidadeHorarioTurmaDao;
    private Reposicao reposicaoDao;
    private AulaDesmarcada aulaDesmarcadaDao;
    private Usuario usuarioDao;
    private HorarioTurma horarioTurmaDao;
    private SituacaoClienteSinteticoDW situacaoClienteSinteticoDWDao;
    private Log logDao;
    private ContratoModalidade contratoModalidadeDao;
    private Turma turmaDao;


    public ControleCreditoTreino() throws Exception {
        super();
    }

    public ControleCreditoTreino(Connection conexao) throws Exception {
        super(conexao);
    }

    public static Integer consultarSaldoCredito(Connection connection, Integer codigoContrato)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select sum(quantidade) as total \n");
        sql.append("from controleCreditoTreino \n");
        sql.append("where contrato = ").append(codigoContrato);
        PreparedStatement pst = connection.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return rs.getInt("total");
        }
        return 0;
    }

    public void zerarPorCancelamento(Integer codigoCliente, ContratoVO contratoVO) throws Exception {
        ResultSet rs = criarConsulta("select saldocreditotreino from situacaoclientesinteticodw  where codigocontrato = "
                                          +contratoVO.getCodigo(), con);
        Integer saldoCreditosTreino = rs.next() ? rs.getInt("saldocreditotreino") : 0;
        if(!UteisValidacao.emptyNumber(saldoCreditosTreino)){
            ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
            controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.CANCELAMENTO_CONTRATO);
            controleCreditoTreinoVO.setObservacao("Cancelamento do contrato "+contratoVO.getCodigo());
            controleCreditoTreinoVO.setQuantidade(-1*saldoCreditosTreino);
            controleCreditoTreinoVO.setContratoVO(contratoVO);
            controleCreditoTreinoVO.setDataOperacao(Calendario.hoje());
            controleCreditoTreinoVO.setUsuarioVO(getUsuarioDao().getUsuarioRecorrencia());
            incluir(controleCreditoTreinoVO, codigoCliente, getFacade().getSituacaoClienteSinteticoDW());
        }
    }


    public void diminuirCreditoTreinoNaoComparecimentoSemCommit(ContratoVO contratoVO, Date dataOperacao, String observacao,
                                                                Integer codigoCliente, HorarioTurmaVO horarioTurmaFalta,
                                                                SituacaoClienteSinteticoDW situacaoClienteSinteticoDW) throws Exception {
        diminuirCreditoTreinoNaoComparecimento(contratoVO, dataOperacao, observacao, codigoCliente, horarioTurmaFalta, situacaoClienteSinteticoDW, false);
    }

    public void diminuirCreditoTreinoNaoComparecimento(ContratoVO contratoVO, Date dataOperacao, String observacao,
                                                       Integer codigoCliente, HorarioTurmaVO horarioTurmaFalta,
                                                       SituacaoClienteSinteticoDW situacaoClienteSinteticoDW) throws Exception {
        diminuirCreditoTreinoNaoComparecimento(contratoVO, dataOperacao, observacao, codigoCliente, horarioTurmaFalta, situacaoClienteSinteticoDW, true);
    }

    private void diminuirCreditoTreinoNaoComparecimento(ContratoVO contratoVO, Date dataOperacao, String observacao,
                                                        Integer codigoCliente, HorarioTurmaVO horarioTurmaFalta,
                                                        SituacaoClienteSinteticoDW situacaoClienteSinteticoDW, boolean commit) throws Exception {
        if (!jaMarcouFaltaParaHorarioTurma(contratoVO, horarioTurmaFalta, dataOperacao)) {
            ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
            controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO);
            controleCreditoTreinoVO.setObservacao(observacao);
            controleCreditoTreinoVO.setHorarioTurmaFalta(horarioTurmaFalta);
            controleCreditoTreinoVO.setQuantidade(-1);
            controleCreditoTreinoVO.setContratoVO(contratoVO);
            controleCreditoTreinoVO.setDataOperacao(dataOperacao);
            controleCreditoTreinoVO.setUsuarioVO(getUsuarioDao().getUsuarioRecorrencia());
            if (commit) {
                incluir(controleCreditoTreinoVO, codigoCliente, situacaoClienteSinteticoDW);
            } else {
                incluirSemCommit(controleCreditoTreinoVO, codigoCliente, situacaoClienteSinteticoDW, null);
            }
        }
    }

    private boolean jaMarcouFaltaParaHorarioTurma(ContratoVO contratoVO, HorarioTurmaVO horarioTurmaFalta, Date dataOperacao)throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * \n");
        sql.append("FROM controleCreditoTreino  \n");
        sql.append("where tipoOperacaoCreditoTreino = ").append(TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO.getCodigo()).append(" and contrato = ").append(contratoVO.getCodigo()).append(" \n");
        sql.append(" and dataOperacao >= '").append(sdf.format(dataOperacao)).append(" 00:00:00'").append(" and  dataOperacao <= '").append(sdf.format(dataOperacao)).append(" 23:59:59' \n ");
        sql.append(" and coalesce(horarioTurmaFalta,0) = ").append(horarioTurmaFalta.getCodigo());
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        return rs.next();
    }

    public List<ControleCreditoTreinoVO> consultar(Integer codigoContrato)throws Exception{
        String sql = getSqlOtimizada("where cred.contrato = " + codigoContrato, "");
        return montarDadosConsultaOtimizada(sql.toString());
    }

    public List<ControleCreditoTreinoVO>consultarPorOperacao(Integer codigoContrato, Integer codigoTipoOperacao, Boolean aulasMarcadasComCreditoExtra, Date dataInicioOperacao, Date dataFimOperacao)throws Exception{
        if ((dataInicioOperacao != null) && (dataFimOperacao == null)){
            throw new ConsistirException("Informe a data fim da operação");
        }
        if ((dataInicioOperacao == null) && (dataFimOperacao != null)){
            throw new ConsistirException("Informe a data início da operação");
        }
        if ((dataInicioOperacao != null) && (dataFimOperacao != null)){
            if (Calendario.maior(dataInicioOperacao, dataFimOperacao)){
                throw new ConsistirException("A data início da operação deve ser menor que a data fim operação");
            }
        }
        StringBuilder sqlWhere = new StringBuilder();
        sqlWhere.append("where cred.contrato = ").append(codigoContrato).append(" \n");
        String sqlJoin = "";
        if (aulasMarcadasComCreditoExtra){
            sqlJoin = "left join aulaDesmarcada adrep on adrep.reposicao = rep.codigo";
            sqlWhere.append("and tipoOperacaoCreditoTreino = ").append(TipoOperacaoCreditoTreinoEnum.MARCOU_AULA.getCodigo()).append(" \n");
            sqlWhere.append(" and adrep.reposicao is null \n");
        }else{
            if (codigoTipoOperacao != TipoOperacaoCreditoTreinoEnum.CODIGO_TODOS){
            if (codigoTipoOperacao == TipoOperacaoCreditoTreinoEnum.CODIGO_AJUSTE_MANUAL_NEGATIVO){
                sqlWhere.append("and tipoOperacaoCreditoTreino = ").append(TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL.getCodigo());
                sqlWhere.append(" and quantidade < 0 \n");
            }else if (codigoTipoOperacao == TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL.getCodigo()){
                sqlWhere.append("and tipoOperacaoCreditoTreino = ").append(TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL.getCodigo());
                sqlWhere.append(" and quantidade >0 \n");
            }else{
                sqlWhere.append("and tipoOperacaoCreditoTreino = ").append(codigoTipoOperacao);
            }
        }
        }
        if ((dataInicioOperacao != null) && (dataFimOperacao != null)){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sqlWhere.append(" and cred.dataOperacao >= '").append(sdf.format(dataInicioOperacao)).append("' and cred.dataOperacao <= '").append(sdf.format(dataFimOperacao)).append(" 23:59:59' ");
        }
        String sqlOtimizada = getSqlOtimizada(sqlWhere.toString(), sqlJoin);
        return montarDadosConsultaOtimizada(sqlOtimizada);
    }

    private List<ControleCreditoTreinoVO> montarDadosConsultaOtimizada(String sql)throws Exception{
        List<ControleCreditoTreinoVO> lista = new ArrayList<ControleCreditoTreinoVO>();
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql);
        while (rs.next()){
            ControleCreditoTreinoVO obj = montarDadosConsulta(rs);
            lista.add(obj);
        }
        return lista;
    }
    
    private ControleCreditoTreinoVO montarDadosConsulta(ResultSet rs) throws Exception{
        ControleCreditoTreinoVO obj = montarDadosBasico(rs);
        obj.getUsuarioVO().setNome(rs.getString("nomeUsuario"));
        if (UtilReflection.objetoMaiorQueZero(obj, "getReposicaoVO().getCodigo()")){
            obj.getReposicaoVO().setDataReposicao(rs.getDate("dataReposicao"));
            obj.getReposicaoVO().setTurmaDestino(new TurmaVO());
            obj.getReposicaoVO().getTurmaDestino().setModalidade(new ModalidadeVO());
            obj.getReposicaoVO().getTurmaDestino().getModalidade().setNome(rs.getString("modalidadeReposicao"));
            obj.getReposicaoVO().setHorarioTurma(new HorarioTurmaVO());
            obj.getReposicaoVO().getHorarioTurma().setDiaSemana(rs.getString("diaSemanaReposicao"));
            obj.getReposicaoVO().getHorarioTurma().setHoraInicial(rs.getString("horaInicialReposicao"));
            obj.getReposicaoVO().getHorarioTurma().setHoraFinal(rs.getString("horaFinalReposicao"));
            obj.getReposicaoVO().setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(rs.getInt("origemSistemaReposicao")));
            obj.getReposicaoVO().setMarcacaoAula(rs.getBoolean("marcacaoaulareposicao"));

            obj.getReposicaoVO().setDataOrigem(rs.getDate("dataOrigemReposicao"));
            obj.getReposicaoVO().setTurmaOrigem(new TurmaVO());
            obj.getReposicaoVO().getTurmaOrigem().setModalidade(new ModalidadeVO());
            obj.getReposicaoVO().getTurmaOrigem().getModalidade().setNome(rs.getString("modalidadeOrigemReposicao"));
            obj.getReposicaoVO().setHorarioTurmaOrigem(new HorarioTurmaVO());
            obj.getReposicaoVO().getHorarioTurmaOrigem().setDiaSemana(rs.getString("diaSemanaOrigemReposicao"));
            obj.getReposicaoVO().getHorarioTurmaOrigem().setHoraInicial(rs.getString("horaInicialOrigemReposicao"));
            obj.getReposicaoVO().getHorarioTurmaOrigem().setHoraFinal(rs.getString("horaFinalOrigemReposicao"));

        }
        if (UtilReflection.objetoMaiorQueZero(obj, "getAulaDesmarcadaVO().getCodigo()")){
            obj.getAulaDesmarcadaVO().setDataOrigem(rs.getDate("dataOrigem"));
            obj.getAulaDesmarcadaVO().setTurmaVO(new TurmaVO());
            obj.getAulaDesmarcadaVO().getTurmaVO().setModalidade(new ModalidadeVO());
            obj.getAulaDesmarcadaVO().getTurmaVO().getModalidade().setNome(rs.getString("modalidadeAulaDesmarcada"));
            obj.getAulaDesmarcadaVO().setHorarioTurmaVO(new HorarioTurmaVO());
            obj.getAulaDesmarcadaVO().getHorarioTurmaVO().setDiaSemana(rs.getString("diaSemanaAulaDesmarcada"));
            obj.getAulaDesmarcadaVO().getHorarioTurmaVO().setHoraInicial(rs.getString("horaInicialAulaDesmarcada"));
            obj.getAulaDesmarcadaVO().getHorarioTurmaVO().setHoraFinal(rs.getString("horaFinalAulaDesmarcada"));
            obj.getAulaDesmarcadaVO().setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(rs.getInt("origemSistemaAulaDesmarcada")));
        }
        obj.setSaldo(rs.getInt("saldo"));
        return obj;
    }

    private String getSqlOtimizada(String sqlWhere, String sqlJoin){
        StringBuilder sql = new StringBuilder();

        sql.append("select \n");
        sql.append("cred.*, \n");
        sql.append("        u.nome as nomeUsuario, \n");
        sql.append("        rep.codigo as codigoReposicao, \n");
        sql.append("        rep.dataReposicao, \n");
        sql.append("        rep.marcacaoaula as marcacaoaulareposicao, \n");
        sql.append("        modTurmaDestinoRep.nome as modalidadeReposicao, \n");
        sql.append("        hrTurmaReposicao.diaSemana as diaSemanaReposicao, \n");
        sql.append("        hrTurmaReposicao.horaInicial as horaInicialReposicao, \n");
        sql.append("        hrTurmaReposicao.horaFinal as horaFinalReposicao, \n");
        sql.append("        rep.origemSistema as origemSistemaReposicao, \n");

        sql.append("        rep.dataOrigem as dataOrigemReposicao, \n");
        sql.append("        modTurmaOrigemRep.nome as modalidadeOrigemReposicao, \n");
        sql.append("        hrTurmaOrigemReposicao.diaSemana as diaSemanaOrigemReposicao, \n");
        sql.append("        hrTurmaOrigemReposicao.horaInicial as horaInicialOrigemReposicao, \n");
        sql.append("        hrTurmaOrigemReposicao.horaFinal as horaFinalOrigemReposicao, \n");

        sql.append("        ad.codigo as codigoAulaDesmarcada, \n");
        sql.append("        ad.dataOrigem, \n");
        sql.append("        modTurmaAulaDesmarcada.nome as modalidadeAulaDesmarcada, \n");
        sql.append("        hrTurmaAulaDesmarcada.diaSemana as diaSemanaAulaDesmarcada, \n");
        sql.append("        hrTurmaAulaDesmarcada.horaInicial as horaInicialAulaDesmarcada, \n");
        sql.append("        hrTurmaAulaDesmarcada.horaFinal as horaFinalAulaDesmarcada, \n");
        sql.append("        ad.origemSistema as origemSistemaAulaDesmarcada, \n");
        sql.append("sum(quantidade) over (partition by cred.contrato order by dataOperacao) saldo \n");
        sql.append("from controlecreditotreino cred \n");
        sql.append("inner join usuario u on u.codigo = cred.usuario \n");
        sql.append("left join reposicao rep on rep.codigo = cred.reposicao \n");
        sql.append("left join turma turmaDestinoRep on turmaDestinoRep.codigo = rep.turmaDestino \n");
        sql.append("left join modalidade modTurmaDestinoRep on modTurmaDestinoRep.codigo = turmaDestinoRep.modalidade \n");
        sql.append("left join horarioTurma hrTurmaReposicao on  hrTurmaReposicao.codigo = rep.horarioTurma \n");

        sql.append("left join turma turmaOrigemRep on turmaOrigemRep.codigo = rep.turmaOrigem \n");
        sql.append("left join modalidade modTurmaOrigemRep on modTurmaOrigemRep.codigo = turmaOrigemRep.modalidade \n");
        sql.append("left join horarioTurma hrTurmaOrigemReposicao on  hrTurmaOrigemReposicao.codigo = rep.horarioTurmaOrigem \n");

        sql.append("left join aulaDesmarcada ad on ad.codigo = cred.aulaDesmarcada \n");
        sql.append("left join turma turmaAulaDesmarcada on turmaAulaDesmarcada.codigo = ad.turma \n");
        sql.append("left join modalidade modTurmaAulaDesmarcada on modTurmaAulaDesmarcada.codigo = turmaAulaDesmarcada.modalidade \n");
        sql.append("left join horarioTurma hrTurmaAulaDesmarcada on  hrTurmaAulaDesmarcada.codigo = ad.horarioTurma \n");
        sql.append(sqlJoin).append(" \n");
        sql.append(sqlWhere).append(" \n");
        sql.append("order by dataOperacao \n");
        return sql.toString();
    }


    public void verificarNaoComparecimentoAulaCreditoTreino(Date diaProcessamentoRobo)throws Exception{
        Date dataReferencia = Uteis.somarDias(diaProcessamentoRobo, -1);
        ReposicaoVO reposicaoVO;
        AulaDesmarcadaVO aulaDesmarcadaVO;
        // Se os alunos de horário da turma não frequentou a aula prevista e não fez reposição então diminuir crédito.
        List<HorarioTurmaVO> listaHorarioTurma = getHorarioTurmaDao().consultarHorarioTurmaContratoCreditoTreino(dataReferencia);
        for (HorarioTurmaVO horarioTurmaVO: listaHorarioTurma){
            int diaSemana = Calendario.getDiaSemana(dataReferencia);
            if (horarioTurmaVO.getDiaSemanaNumero().equals(diaSemana)){
                // verifica se houve acesso no dia.
                boolean houveAcesso = (houveAcesso(dataReferencia, horarioTurmaVO.getContratoVO().getCodigo(), horarioTurmaVO));
                if (!houveAcesso){
                    reposicaoVO = getReposicaoDao().consultar(horarioTurmaVO,dataReferencia,horarioTurmaVO.getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    aulaDesmarcadaVO = getAulaDesmarcadaDao().consultarAulaDesmarcada(horarioTurmaVO, dataReferencia, horarioTurmaVO.getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if ((!(UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getCodigo()"))) &&
                            (!(UtilReflection.objetoMaiorQueZero(reposicaoVO, "getCodigo()")))){
                        // diminuir credito por não comperecimento a aula.
                        Calendar dataAula = Calendario.getInstance();
                        dataAula.setTime(dataReferencia);
                        String horaIni = horarioTurmaVO.getHoraInicial();
                        dataAula.set(Calendar.HOUR, Integer.parseInt(horaIni.split(":")[0]));
                        dataAula.set(Calendar.MINUTE, Integer.parseInt(horaIni.split(":")[1]));
                        dataAula.set(Calendar.SECOND, 0);
                        dataAula.set(Calendar.MILLISECOND, 0);
                        if(Calendario.maiorComHora(Uteis.somarCampoData(horarioTurmaVO.getContratoVO().getDataLancamento(),Calendar.MINUTE, horarioTurmaVO.getMinutosAntecedenciaDesmarcarAula()), dataAula.getTime())){
                            continue;
                        }
                        diminuirCreditoTreinoNaoComparecimento(horarioTurmaVO.getContratoVO(), dataAula.getTime(), "", null, horarioTurmaVO, getSituacaoClienteSinteticoDWDao());
                    }
                }
            }
        }

        // verificar se houve acesso para as reposições ou para as aulas marcadas de horário livre.
        List<ReposicaoVO> listaReposicao = getReposicaoDao().consultarReposicaoContratoCreditoTreino(dataReferencia);
        StringBuilder auxMsg = new StringBuilder();
        for (ReposicaoVO obj : listaReposicao){
            boolean houveAcesso = (houveAcesso(dataReferencia, obj.getContrato().getCodigo(), obj.getHorarioTurma()));
            boolean desmarcouReposicao = false;
            if (!houveAcesso){
                // verificar se a reposição foi desmarcada.
                aulaDesmarcadaVO = getAulaDesmarcadaDao().consultarAulaDesmarcada(obj.getHorarioTurmaOrigem(), obj.getDataOrigem(), obj.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getCodigo()")){
                    if (Calendario.maiorComHora(aulaDesmarcadaVO.getDataLancamento(), obj.getDataLancamento())){
                        // O usuário pode desmarcar a aula e depois fazer a reposição. Desta forma, considerar que desmarcou a reposição somente se a data da aula desmarcada for maior que a data da reposição.
                        desmarcouReposicao = true;
                    }
                }
            }
            if ((!houveAcesso) && (!desmarcouReposicao)){
                auxMsg.delete(0, auxMsg.length());
                if (Calendario.igual(obj.getDataReposicao(), obj.getDataOrigem())){
                    //Registro de marcação de aula para Planos de Horário Livre
                    auxMsg.append("Foi marcada a aula ").append(obj.getDescricaoOrigemCurta()).append(" e o aluno não compareceu.");

                }else{
                    //Reposiçao de aula para Planos de Horário da Turma.
                    auxMsg.append("A aula ").append(obj.getDescricaoOrigemCurta()).append(" foi remarcada para ").append(obj.getDescricaoDestinoCurta()).append(" e o aluno não compareceu.");
                }
                Calendar dataAula = Calendario.getInstance();
                dataAula.setTime(dataReferencia);
                String horaIni = obj.getHorarioTurma().getHoraInicial();
                dataAula.set(Calendar.HOUR, Integer.parseInt(horaIni.split(":")[0]));
                dataAula.set(Calendar.MINUTE, Integer.parseInt(horaIni.split(":")[1]));
                dataAula.set(Calendar.SECOND, 0);
                dataAula.set(Calendar.MILLISECOND, 0);
                diminuirCreditoTreinoNaoComparecimento(obj.getContrato(), dataAula.getTime(),auxMsg.toString(), obj.getCliente().getCodigo(), obj.getHorarioTurma(), getSituacaoClienteSinteticoDWDao());
            }
        }
    }


    public boolean houveAcesso(Date dataValidar, Integer codigoContrato, HorarioTurmaVO horarioTurmaVO) throws Exception{
        String[] horaIni = horarioTurmaVO.getHoraInicial().split(":");
        String[] horaFim = horarioTurmaVO.getHoraFinal().split(":");
        // montar hora inicial
        Calendar horaInicialTurma = Calendario.getInstance();
        horaInicialTurma.setTime(Calendario.getDataComHoraZerada(dataValidar));
        horaInicialTurma.set(Calendar.HOUR, ((Integer.parseInt(horaIni[0])) -1));// consultar com 01 hora de antecedência.
        horaInicialTurma.set(Calendar.MINUTE, Integer.parseInt(horaIni[1]));
        // montar hora final
        Calendar horaFinalTurma = Calendario.getInstance();
        horaFinalTurma.setTime(Calendario.getDataComHoraZerada(dataValidar));
        horaFinalTurma.set(Calendar.HOUR, Integer.parseInt(horaFim[0]));
        horaFinalTurma.set(Calendar.MINUTE, Integer.parseInt(horaFim[1]));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * \n");
        sql.append("FROM controleCreditoTreino \n");
        sql.append("WHERE tipoOperacaoCreditoTreino = ? and contrato =? \n");
        sql.append(" and dataOperacao >= '").append(sdf.format(horaInicialTurma.getTime()))
                                   .append("' and dataOperacao <= '").append(sdf.format(horaFinalTurma.getTime())).append("'");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, TipoOperacaoCreditoTreinoEnum.UTILIZACAO.getCodigo());
        pst.setInt(2, codigoContrato);
        ResultSet rs = pst.executeQuery();
        return rs.next();
    }

    private ControleCreditoTreinoVO montarDados(ResultSet rs, int nivelMontarDados)throws Exception{
        ControleCreditoTreinoVO controleCreditoTreinoVO = montarDadosBasico(rs);
        UsuarioInterfaceFacade usuarioDao = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDao.consultarPorCodigo(controleCreditoTreinoVO.getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        controleCreditoTreinoVO.setUsuarioVO(usuarioVO);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS){
            if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getReposicaoVO().getCodigo()")){
                controleCreditoTreinoVO.setReposicaoVO(getFacade().getReposicao().consultarPorChavePrimaria(controleCreditoTreinoVO.getReposicaoVO().getCodigo(),Uteis.NIVELMONTARDADOS_TODOS));
                if ((controleCreditoTreinoVO.getReposicaoVO().getTurmaOrigem().getModalidade().getNome() == null) || (controleCreditoTreinoVO.getReposicaoVO().getTurmaOrigem().getModalidade().getNome().equals(""))){
                    controleCreditoTreinoVO.getReposicaoVO().getTurmaOrigem().setModalidade(getFacade().getModalidade().consultarPorChavePrimaria(controleCreditoTreinoVO.getReposicaoVO().getTurmaOrigem().getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    controleCreditoTreinoVO.getReposicaoVO().getTurmaDestino().setModalidade(getFacade().getModalidade().consultarPorChavePrimaria(controleCreditoTreinoVO.getReposicaoVO().getTurmaDestino().getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
            }
            if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getAulaDesmarcadaVO().getCodigo()")){
                controleCreditoTreinoVO.setAulaDesmarcadaVO(getFacade().getAulaDesmarcada().consultarPorCodigo(controleCreditoTreinoVO.getAulaDesmarcadaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                controleCreditoTreinoVO.getAulaDesmarcadaVO().setTurmaVO(getFacade().getTurma().consultarPorChavePrimaria(controleCreditoTreinoVO.getAulaDesmarcadaVO().getTurmaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            }
        }
        return controleCreditoTreinoVO;
    }

    private ControleCreditoTreinoVO montarDadosBasico(ResultSet rs)throws Exception{

        ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
        controleCreditoTreinoVO.setCodigo(rs.getInt("codigo"));
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(rs.getInt("contrato"));
        controleCreditoTreinoVO.setContratoVO(contratoVO);
        controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.getTipo(rs.getInt("tipoOperacaoCreditoTreino")));
        controleCreditoTreinoVO.setQuantidade(rs.getInt("quantidade"));
        controleCreditoTreinoVO.setDatalancamento(rs.getTimestamp("dataLancamento"));
        controleCreditoTreinoVO.setDataOperacao(rs.getTimestamp("dataOperacao"));
        controleCreditoTreinoVO.setUsuarioVO(new UsuarioVO());
        controleCreditoTreinoVO.getUsuarioVO().setCodigo(rs.getInt("usuario"));
        AcessoClienteVO acessoClienteVO = new AcessoClienteVO();
        acessoClienteVO.setCodigo(rs.getInt("acessoCliente"));
        controleCreditoTreinoVO.setAcessoClienteVO(acessoClienteVO);
        ReposicaoVO reposicaoVO = new ReposicaoVO();
        reposicaoVO.setCodigo(rs.getInt("reposicao"));
        controleCreditoTreinoVO.setReposicaoVO(reposicaoVO);
        AulaDesmarcadaVO aulaDesmarcadaVO = new AulaDesmarcadaVO();
        aulaDesmarcadaVO.setCodigo(rs.getInt("aulaDesmarcada"));
        controleCreditoTreinoVO.setAulaDesmarcadaVO(aulaDesmarcadaVO);
        controleCreditoTreinoVO.setObservacao(rs.getString("observacao"));
        ContratoVO contratoOrigem = new ContratoVO();
        contratoOrigem.setCodigo(rs.getInt("contratoOrigem"));
        controleCreditoTreinoVO.setContratoOrigem(contratoOrigem);

        if (controleCreditoTreinoVO.getQuantidade() > 0)
          controleCreditoTreinoVO.setCodigoTipoAjusteManualCreditoTreino(1);
        else
          controleCreditoTreinoVO.setCodigoTipoAjusteManualCreditoTreino(2);
        controleCreditoTreinoVO.setHorarioTurmaFalta(new HorarioTurmaVO());
        controleCreditoTreinoVO.getHorarioTurmaFalta().setCodigo(rs.getInt("horarioTurmaFalta"));
        try {
            controleCreditoTreinoVO.setDescricaoAulaMarcada(rs.getString("descricaoAulaMarcada"));
        } catch (Exception e) {}

        return  controleCreditoTreinoVO;
    }

    private List<ControleCreditoTreinoVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados)throws Exception{
        List<ControleCreditoTreinoVO> lista = new ArrayList<ControleCreditoTreinoVO>();
        while (rs.next()){
            ControleCreditoTreinoVO controleCreditoTreinoVO = montarDados(rs, nivelMontarDados);
            lista.add(controleCreditoTreinoVO);
        }
        return lista;
    }

    public void incluir(ControleCreditoTreinoVO controleCreditoTreinoVO, Integer codigoCliente, 
            SituacaoClienteSinteticoDW situacaoClienteSinteticoDW)throws Exception{
        con.setAutoCommit(false);
        try{
            incluirSemCommit(controleCreditoTreinoVO, codigoCliente, situacaoClienteSinteticoDW, null);
            con.commit();
        }catch (Exception e){
            con.rollback();
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirPorCodigoContrato(Integer codigoContrato)throws Exception{
        String sql = "delete from controleCreditoTreino where contrato = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoContrato);
        pst.execute();
    }

    public void excluirPorCodigo(Integer codigo)throws Exception{
        String sql = "delete from controleCreditoTreino where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigo);
        pst.execute();
    }

    public void ajustarOperacaoExcluirAulaDesmarcada(AulaDesmarcadaVO aulaDesmarcada)throws Exception{
        ControleCreditoTreinoVO controleCreditoTreinoVO = consultarPorParametros(aulaDesmarcada.getCodigo(),null,null);
        if(UteisValidacao.emptyNumber(controleCreditoTreinoVO.getCodigo())){
            return;
        }
        String observacao = "Dia "+Uteis.getDataComHHMM(Calendario.hoje())+", o Usuario "+aulaDesmarcada.getUsuarioVO().getNomeAbreviado()+" excluiu a "+ controleCreditoTreinoVO.getTipoOperacaoCreditoTreinoEnum().getDescricaoCurta() 
                +": "+ controleCreditoTreinoVO.getAulaDesmarcada_Apresentar();
        
        controleCreditoTreinoVO.setAulaDesmarcadaVO(new AulaDesmarcadaVO());
        controleCreditoTreinoVO.setReposicaoVO(new ReposicaoVO());
        controleCreditoTreinoVO.setObservacao(observacao);
        alterarSemCommit(controleCreditoTreinoVO, null, null);
    }

     public void ajustarOperacaoExcluirReposicao(ReposicaoVO reposicao, String parcelaVencida)throws Exception{
        ControleCreditoTreinoVO controleCreditoTreinoVO = consultarPorParametros(null,reposicao.getCodigo(), null);
        if(UteisValidacao.emptyNumber(controleCreditoTreinoVO.getCodigo())){
            return;
        }
        String observacao;

        if(parcelaVencida == null){
            observacao = "Dia "+Uteis.getDataComHHMM(Calendario.hoje())
                    +", o Usuario "+reposicao.getUsuario().getNomeAbreviado()
                    +" excluiu a "+ controleCreditoTreinoVO.getTipoOperacaoCreditoTreinoEnum().getDescricaoCurta()
                    +": "+ controleCreditoTreinoVO.getAulaMarcada_Apresentar();
        } else {
            observacao = "Aula "+ controleCreditoTreinoVO.getTipoOperacaoCreditoTreinoEnum().getDescricaoCurta()
                    +": "+ controleCreditoTreinoVO.getAulaMarcada_Apresentar() + " foi removida devido a "
                    + parcelaVencida;
        }

        controleCreditoTreinoVO.setAulaDesmarcadaVO(new AulaDesmarcadaVO());
        controleCreditoTreinoVO.setReposicaoVO(new ReposicaoVO());
        controleCreditoTreinoVO.setObservacao(observacao);
        alterarSemCommit(controleCreditoTreinoVO, null,null);
        
    }

    private int calcularCreditosJaTransferidos(Integer codigoContrato) throws Exception {
        return consultar(codigoContrato, TipoOperacaoCreditoTreinoEnum.TRANSFERENCIA_ENTRE_ALUNOS_ORIGEM, null, null, Uteis.NIVELMONTARDADOS_MINIMOS)
                .stream()
                .mapToInt(ControleCreditoTreinoVO::getQuantidade)
                .sum() * -1;
    }


    public void realizarTransferenciaDeCredito(ControleCreditoTreinoVO controleCreditoTreinoVORecebedor, ControleCreditoTreinoVO controleCreditoTreinoVOTransferidor, SituacaoClienteSinteticoDW situacaoClienteSinteticoDW, Integer codClienteTransferidor, ClienteVO clienteRecebedorVO) throws Exception {

        Contrato contratoDAO = new Contrato(con);
        ContratoDuracao contratoDuracaoDAO = new ContratoDuracao(con);
        ContratoDuracaoCreditoTreino contratoDuracaoCreditoTreinoDAO = new ContratoDuracaoCreditoTreino(con);
        ContratoTextoPadrao contratoTextoPadraoDAO = new ContratoTextoPadrao(con);
        ContratoModalidade contratoModalidadeDAO = new ContratoModalidade(con);
        PlanoModalidadeVezesSemana planoModalidadeVezesSemanaDAO = new PlanoModalidadeVezesSemana(con);
        ContratoHorario contratoHorarioDAO = new ContratoHorario(con);

        ContratoVO contratoTransferidorVO = contratoDAO.consultarPorCodigo(controleCreditoTreinoVOTransferidor.getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

        int quantidadeDeCreditosJaTransferidos = calcularCreditosJaTransferidos(controleCreditoTreinoVOTransferidor.getContratoVO().getCodigo());

        int quantidadePermitidaATransferir = contratoTransferidorVO.getPlano().getQuantidadeATransferirPermitidaPorAluno();

        int quantidadeSendoTransferida = controleCreditoTreinoVOTransferidor.getQuantidade();

        if (quantidadeDeCreditosJaTransferidos >= quantidadePermitidaATransferir){
            throw new Exception("Você não pode mais transferir créditos pois já chegou a quantidade limite permitida a transferir do plano");
        }

        if (quantidadeDeCreditosJaTransferidos + quantidadeSendoTransferida > quantidadePermitidaATransferir) {
            if (quantidadeDeCreditosJaTransferidos > 0){
                throw new Exception("Você já transferiu " + quantidadeDeCreditosJaTransferidos + " créditos, e o limite do plano é " + quantidadePermitidaATransferir
                        + " então você só pode transferir: " + (quantidadePermitidaATransferir - quantidadeDeCreditosJaTransferidos) + " créditos");
            } else throw new Exception("Não é permitido transferir créditos acima do valor configurado no plano: " + quantidadePermitidaATransferir);
        }

        SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO = situacaoClienteSinteticoDW.consultarCliente(codClienteTransferidor, Uteis.NIVELMONTARDADOS_TODOS);

        if (situacaoClienteSinteticoDWVO.getSaldoCreditoTreino() < quantidadeSendoTransferida){
            throw new Exception("O saldo precisa ser maior ou igual a quantidade que esta sendo transferida");
        }

        Optional<ContratoVO> contratoRecebendoCreditos = contratoDAO.consultarUltimoContratoClienteComPlanoCredito(clienteRecebedorVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        ContratoDuracaoVO contratoDuracaoTransferidorVO = contratoDuracaoDAO.consultarPorContrato(controleCreditoTreinoVOTransferidor.getContratoVO().getCodigo(),  Uteis.NIVELMONTARDADOS_TODOS);
        ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoTransferidorVO = contratoDuracaoCreditoTreinoDAO.consultarPorContrato(controleCreditoTreinoVOTransferidor.getContratoVO().getCodigo(),  Uteis.NIVELMONTARDADOS_TODOS);
        ContratoTextoPadraoVO contratoTextoPadraoVO = contratoTextoPadraoDAO.consultarPorCodigoContrato(controleCreditoTreinoVORecebedor.getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

        controleCreditoTreinoVOTransferidor.setQuantidade(quantidadeSendoTransferida * -1);

        List listaDeContratosDoClienteRecebedor = contratoDAO.consultarPorCodigoPessoa(clienteRecebedorVO.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (contratoRecebendoCreditos.isPresent()){
            controleCreditoTreinoVORecebedor.setContratoVO(contratoRecebendoCreditos.get());
            incluirTransferenciaDeCredito(controleCreditoTreinoVORecebedor, situacaoClienteSinteticoDW, codClienteTransferidor, clienteRecebedorVO,
                    controleCreditoTreinoVOTransferidor, null, null, null,
                    null, null, null);

        } else if((!UtilReflection.objetoNulo(clienteRecebedorVO, "getSituacaoClienteSinteticoVO().getSituacao()")
                && clienteRecebedorVO.getSituacaoClienteSinteticoVO().getSituacao().equals("VI")
                && UteisValidacao.emptyList(listaDeContratosDoClienteRecebedor))
                || clienteRecebedorVO.getEmpresa().getPermiteContratosConcomintante()
                || clienteRecebedorVO.getSituacaoClienteSinteticoVO().getSituacao().equals("IN")){

            ContratoVO novoContratoRecebedorVO;
            ContratoDuracaoVO contratoDuracaoRecebedorVO;
            ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoRecebedorVO;

            novoContratoRecebedorVO = (ContratoVO) contratoTransferidorVO.getClone(false);
            contratoDuracaoRecebedorVO = (ContratoDuracaoVO) contratoDuracaoTransferidorVO.getClone(false);
            contratoDuracaoCreditoTreinoRecebedorVO = (ContratoDuracaoCreditoTreinoVO) contratoDuracaoCreditoTreinoTransferidorVO.getClone(false);
            contratoTextoPadraoVO = (ContratoTextoPadraoVO) contratoTextoPadraoVO.getClone(false);

            List<ContratoModalidadeVO> modalidadeVOS = contratoModalidadeDAO.consultarPorCodigoContrato(contratoTransferidorVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            novoContratoRecebedorVO.setContratoModalidadeVOs(modalidadeVOS);
            montarDadosNovoContratoPlanoCredito(novoContratoRecebedorVO, clienteRecebedorVO);

            for (ContratoModalidadeVO contratoModalidadeVO : novoContratoRecebedorVO.getContratoModalidadeVOs()) {
                contratoModalidadeVO.setPlanoVezesSemanaVO(
                        planoModalidadeVezesSemanaDAO
                                .consultarPlanoModalidadePorCodigoModalidadeECodigoPlano(contratoModalidadeVO.getModalidade().getCodigo(), contratoTransferidorVO.getPlano().getCodigo())
                );
            }

            ContratoHorarioVO contratoHorarioVO = contratoHorarioDAO.consultarContratoHorarios(contratoTransferidorVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            incluirTransferenciaDeCredito(
                    controleCreditoTreinoVORecebedor, situacaoClienteSinteticoDW, codClienteTransferidor,
                    clienteRecebedorVO, controleCreditoTreinoVOTransferidor, novoContratoRecebedorVO,
                    contratoDuracaoRecebedorVO, contratoDuracaoCreditoTreinoRecebedorVO, contratoTextoPadraoVO, modalidadeVOS, contratoHorarioVO
            );

        } else throw new Exception("O aluno não tem um contrato com o plano do tipo crédito e a empresa não permite contratos concomitantes");
    }

    private void montarDadosNovoContratoPlanoCredito(ContratoVO novoContratoRecebedorVO, ClienteVO clienteRecebedorVO){
        novoContratoRecebedorVO.setPessoa(clienteRecebedorVO.getPessoa());
        novoContratoRecebedorVO.setCliente(clienteRecebedorVO);
        novoContratoRecebedorVO.setValorContrato(0D);
        novoContratoRecebedorVO.setValorFinal(0D);
        novoContratoRecebedorVO.setCodigo(null);

        if (!novoContratoRecebedorVO.getSituacaoContrato().equals("MA")) {
            novoContratoRecebedorVO.setSituacaoContrato("MA");
            novoContratoRecebedorVO.setContratoBaseadoRenovacao(0);
            novoContratoRecebedorVO.setContratoBaseadoRematricula(0);
            novoContratoRecebedorVO.setContratoResponsavelRenovacaoMatricula(0);
            novoContratoRecebedorVO.setContratoResponsavelRematriculaMatricula(0);
        }
    }

    public Integer consultarQuantosCreditosOAlunoPodeTransferir(Integer codigoContrato) throws Exception {

        Contrato contratoDAO = new Contrato(con);

        ContratoVO contratoTransferidorVO = contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_TODOS);

        int quantidadeDeCreditosJaTransferidos = calcularCreditosJaTransferidos(codigoContrato);

        int quantidadePermitidaATransferir = contratoTransferidorVO.getPlano().getQuantidadeATransferirPermitidaPorAluno();

        return quantidadePermitidaATransferir - quantidadeDeCreditosJaTransferidos;
    }

    public void incluirTransferenciaDeCredito(ControleCreditoTreinoVO controleCreditoTreinoVORecebedor, SituacaoClienteSinteticoDW situacaoClienteSinteticoDW, Integer codClienteTransferidor,
                                              ClienteVO clienteRecebedorVO, ControleCreditoTreinoVO controleCreditoTreinoVOTransferidor, ContratoVO novoContratoRecebedorVO,
                                              ContratoDuracaoVO novoContratoDuracaoVO, ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoRecebedorVO, ContratoTextoPadraoVO contratoTextoPadraoVO,
                                              List<ContratoModalidadeVO> modalidadeVOS, ContratoHorarioVO contratoHorarioVO) throws Exception {

        Contrato contratoDAO = new Contrato(con);
        ContratoDuracao contratoDuracaoDAO = new ContratoDuracao(con);
        ContratoCondicaoPagamento contratoCondicaoPagamentoDAO = new ContratoCondicaoPagamento(con);
        ContratoTextoPadrao contratoTextoPadraoDAO = new ContratoTextoPadrao(con);
        ContratoDuracaoCreditoTreino contratoDuracaoCreditoTreinoDAO = new ContratoDuracaoCreditoTreino(con);
        ContratoHorario contratoHorarioDAO = new ContratoHorario(con);
        ContratoModalidade contratoModalidadeDAO = new ContratoModalidade(con);
        CondicaoPagamento condicaoPagamentoDAO = new CondicaoPagamento(con);

        con.setAutoCommit(false);
        try{

            controleCreditoTreinoVORecebedor.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.TRANSFERENCIA_ENTRE_ALUNOS_DESTINO);
            controleCreditoTreinoVOTransferidor.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.TRANSFERENCIA_ENTRE_ALUNOS_ORIGEM);

            if(novoContratoRecebedorVO != null){

                ContratoVO contratoRecebendoCreditos;

                novoContratoRecebedorVO.setDataLancamento(Calendario.hoje());
                novoContratoRecebedorVO.setVigenciaDe(Calendario.hoje());
                novoContratoRecebedorVO.setCodigo(null);
                contratoDAO.incluirContrato(novoContratoRecebedorVO);

                contratoRecebendoCreditos = contratoDAO.consultarUltimoContratoClienteComPlanoCredito(clienteRecebedorVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS)
                        .orElseThrow(() -> new Exception("Não foi possível encontrar o contrato que receberá os créditos"));

                novoContratoDuracaoVO.setContrato(contratoRecebendoCreditos.getCodigo());
                novoContratoDuracaoVO.setContratoVO(contratoRecebendoCreditos);
                novoContratoDuracaoVO.setCodigo(null);
                contratoDuracaoDAO.incluir(novoContratoDuracaoVO);

                novoContratoDuracaoVO = contratoDuracaoDAO.consultarPorContrato(contratoRecebendoCreditos.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                novoContratoDuracaoVO.setContratoVO(contratoRecebendoCreditos);

                ContratoCondicaoPagamentoVO novoContratoCondicaoPagamentoVO = new ContratoCondicaoPagamentoVO();
                novoContratoCondicaoPagamentoVO.setCondicaoPagamento(condicaoPagamentoDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS));
                novoContratoCondicaoPagamentoVO.setContrato(contratoRecebendoCreditos.getCodigo());
                contratoCondicaoPagamentoDAO.incluir(novoContratoCondicaoPagamentoVO);

                contratoTextoPadraoVO.setContrato(contratoRecebendoCreditos.getCodigo());
                contratoTextoPadraoVO.setCodigo(null);
                contratoTextoPadraoDAO.incluir(contratoTextoPadraoVO);

                contratoRecebendoCreditos = contratoDAO.consultarUltimoContratoClienteComPlanoCredito(clienteRecebedorVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS)
                        .orElseThrow(() -> new Exception("Não foi possível encontrar o contrato que receberá os créditos"));

                controleCreditoTreinoVORecebedor.setContratoVO(contratoRecebendoCreditos);

                incluirSemCommit(controleCreditoTreinoVORecebedor, clienteRecebedorVO.getCodigo(), situacaoClienteSinteticoDW, null);
                incluirSemCommit(controleCreditoTreinoVOTransferidor, codClienteTransferidor, situacaoClienteSinteticoDW, null);

                contratoDuracaoCreditoTreinoRecebedorVO.setQuantidadeCreditoCompra(0);
                contratoDuracaoCreditoTreinoRecebedorVO.setQuantidadeCreditoDisponivel(controleCreditoTreinoVORecebedor.getQuantidade());
                contratoDuracaoCreditoTreinoRecebedorVO.setQuantidadeCreditoUtilizado(0);
                contratoDuracaoCreditoTreinoRecebedorVO.setContratoDuracaoVO(novoContratoDuracaoVO);
                contratoDuracaoCreditoTreinoRecebedorVO.setVindoDeTransferencia(true);
                contratoDuracaoCreditoTreinoRecebedorVO.setCodigo(null);
                contratoDuracaoCreditoTreinoDAO.incluir(contratoDuracaoCreditoTreinoRecebedorVO);

                contratoModalidadeDAO.incluirContratoModalidades(contratoRecebendoCreditos.getCodigo(), modalidadeVOS);

                contratoHorarioVO.setCodigo(null);
                contratoHorarioVO.setContrato(contratoRecebendoCreditos.getCodigo());
                contratoHorarioDAO.incluir(contratoHorarioVO);

                atualizarSintetico(clienteRecebedorVO);

            } else {
                ContratoVO contratoRecebendoCreditos = contratoDAO.consultarUltimoContratoClienteComPlanoCredito(clienteRecebedorVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS)
                        .orElseThrow(() -> new Exception("Não foi possível encontrar o contrato que receberá os créditos"));

                controleCreditoTreinoVORecebedor.setContratoVO(contratoRecebendoCreditos);
                incluirSemCommit(controleCreditoTreinoVORecebedor, clienteRecebedorVO.getCodigo(), situacaoClienteSinteticoDW, null);
                incluirSemCommit(controleCreditoTreinoVOTransferidor, codClienteTransferidor, situacaoClienteSinteticoDW, null);
            }

            con.commit();
        }catch (Exception e){
            con.rollback();
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }


    private void atualizarSintetico(ClienteVO cliente) throws Exception {
        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
        zwFacade.atualizarSintetico(cliente,
                Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, true);
        zwFacade = null;
    }

    public void incluirSemCommit(ControleCreditoTreinoVO controleCreditoTreinoVO,
                                 Integer codigoCliente,
                                 SituacaoClienteSinteticoDW situacaoClienteSinteticoDW, Date dataBase) throws Exception {
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Vou incluir o ControleCreditoTreino");
        ControleCreditoTreinoVO.validarDados(controleCreditoTreinoVO);
        Integer saldo = ControleCreditoTreino.consultarSaldoCredito(con, controleCreditoTreinoVO.getContratoVO().getCodigo());
        if (controleCreditoTreinoVO.getTipoOperacaoCreditoTreinoEnum() == TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL) {
            if (controleCreditoTreinoVO.getQuantidade() < 0) {
                if (saldo < 0)
                    throw new ConsistirException("Não é permitido retirar crédito pois o saldo encontra-se negativo.");
                if (saldo == 0)
                    throw new ConsistirException("Não é permitido retirar crédito pois o saldo encontra-se zerado.");
                if ((saldo + controleCreditoTreinoVO.getQuantidade()) < 0) {
                    throw new ConsistirException("Operação não permitida. A quantidade de crédito que está sendo retirada '" + (controleCreditoTreinoVO.getQuantidade() * -1) + "' é maior que o saldo atual '" + saldo + "'");
                }
                String logLiberarVagaNaTurma = liberarVagaNaTurma(controleCreditoTreinoVO.getContratoVO().getCodigo(), codigoCliente, controleCreditoTreinoVO.getQuantidade().intValue() * -1, saldo, controleCreditoTreinoVO.getUsuarioVO());
                if ((controleCreditoTreinoVO.getObservacao() != null) && (!logLiberarVagaNaTurma.trim().equals(""))) {
                    controleCreditoTreinoVO.setObservacao(controleCreditoTreinoVO.getObservacao() + "\n" + logLiberarVagaNaTurma);
                } else if (!logLiberarVagaNaTurma.trim().equals("")) {
                    controleCreditoTreinoVO.setObservacao(logLiberarVagaNaTurma);
                }

            }
        }
        if (controleCreditoTreinoVO.getDatalancamento() == null) {
            controleCreditoTreinoVO.setDatalancamento(Calendario.hoje());
        }
        if (controleCreditoTreinoVO.getDataOperacao() == null) {
            controleCreditoTreinoVO.setDataOperacao(Calendario.hoje());
        }

        preencherModalidadeControleCreditoTreino(controleCreditoTreinoVO);
        String sql = "insert into controleCreditoTreino (contrato,tipoOperacaoCreditoTreino,quantidade,datalancamento, "
                + "dataOperacao,usuario,acessoCliente,reposicao,auladesmarcada,observacao, contratoOrigem, horarioTurmaFalta, modalidade, descricaoAulaMarcada, horarioturma, diaAula) "
                + " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, controleCreditoTreinoVO.getContratoVO().getCodigo());
        pst.setInt(2, controleCreditoTreinoVO.getTipoOperacaoCreditoTreinoEnum().getCodigo());
        pst.setInt(3, controleCreditoTreinoVO.getQuantidade());
        pst.setTimestamp(4, Uteis.getDataJDBCTimestamp(controleCreditoTreinoVO.getDatalancamento()));
        pst.setTimestamp(5, Uteis.getDataJDBCTimestamp(controleCreditoTreinoVO.getDataOperacao()));
        pst.setInt(6, controleCreditoTreinoVO.getUsuarioVO().getCodigo());
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getAcessoClienteVO().getCodigo()")) {
            pst.setInt(7, controleCreditoTreinoVO.getAcessoClienteVO().getCodigo());
        } else {
            pst.setNull(7, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getReposicaoVO().getCodigo()")) {
            pst.setInt(8, controleCreditoTreinoVO.getReposicaoVO().getCodigo());
        } else {
            pst.setNull(8, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getAulaDesmarcadaVO().getCodigo()")) {
            pst.setInt(9, controleCreditoTreinoVO.getAulaDesmarcadaVO().getCodigo());
        } else {
            pst.setNull(9, Types.NULL);
        }
        if ((controleCreditoTreinoVO.getObservacao() != null) && (!controleCreditoTreinoVO.getObservacao().trim().equals(""))) {
            pst.setString(10, controleCreditoTreinoVO.getObservacao());
        } else {
            pst.setNull(10, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getContratoOrigem().getCodigo()")) {
            pst.setInt(11, controleCreditoTreinoVO.getContratoOrigem().getCodigo());
        } else {
            pst.setNull(11, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getHorarioTurmaFalta().getCodigo()")) {
            pst.setInt(12, controleCreditoTreinoVO.getHorarioTurmaFalta().getCodigo());
        } else {
            pst.setNull(12, Types.NULL);
        }
        if (!UteisValidacao.emptyNumber(controleCreditoTreinoVO.getModalidadeVO().getCodigo())) {
            pst.setInt(13, controleCreditoTreinoVO.getModalidadeVO().getCodigo());
        } else {
            pst.setNull(13, Types.NULL);
        }
        if (!UteisValidacao.emptyString(controleCreditoTreinoVO.getDescricaoAulaMarcada())) {
            pst.setString(14, controleCreditoTreinoVO.getDescricaoAulaMarcada());
        } else {
            pst.setNull(14, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getHorarioTurma().getCodigo()")) {
            pst.setInt(15, controleCreditoTreinoVO.getHorarioTurma().getCodigo());
        } else {
            pst.setNull(15, Types.NULL);
        }
        if (controleCreditoTreinoVO.getDiaAula() != null) {
            pst.setDate(16, Uteis.getDataJDBC(controleCreditoTreinoVO.getDiaAula()));
        } else {
            pst.setNull(16, Types.NULL);
        }
        pst.execute();
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Terminei de incluir o ControleCreditoTreino");
        //Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### SQL UTILIZADO NO INSERT: " + pst.);
        if (controleCreditoTreinoVO.getQuantidade() != 0 && situacaoClienteSinteticoDW != null) {
            atualizarInformacoesCredito(controleCreditoTreinoVO.getContratoVO().getCodigo(), situacaoClienteSinteticoDW, dataBase);
        }
    }

    private void atualizarInformacoesCredito(Integer codContrato,SituacaoClienteSinteticoDW situacaoClienteSinteticoDW, Date dataBase) throws SQLException {
        Integer codigoPessoa = null;
        try (Statement st = con.createStatement()) {
            String sqlPessoaContrato = "select pessoa from contrato where codigo = " + codContrato;
            try (ResultSet rs = st.executeQuery(sqlPessoaContrato)) {
                if (rs.next()) {
                    codigoPessoa = rs.getInt("pessoa");
                }
            }
        }
        try {
            situacaoClienteSinteticoDW.atualizarInformacoesCreditoTreino(codigoPessoa, dataBase == null ? Calendario.hoje() : dataBase);
        } catch (Exception ex) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO ATUALIZAR INFORMACOES CREDITO TREINO(APÓS GRAVAR O ACESSO). ERRO: " + ex.getMessage());
        }
    }

    public void diminuirCreditoPorAcesso(String key, SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO, AcessoClienteVO acessoClienteVO)throws Exception{
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Entrou no método diminuirCreditoPorAcesso ");
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### PARÂMETROS");
        if (situacaoClienteSinteticoDWVO != null){
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### situacaoClienteSinteticoDWVO.getCodigoPessoa() " + situacaoClienteSinteticoDWVO.getCodigoPessoa());
        }
        if (acessoClienteVO == null){
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### acessoClienteVO está nulo ");
        }
        if (diminuirCreditoTreinoPorUtilizacao(acessoClienteVO,situacaoClienteSinteticoDWVO.getCodigoPessoa())){
            getSituacaoClienteSinteticoDWDao().atualizarBaseOffLineZillyonAcesso(key, situacaoClienteSinteticoDWVO.getCodigoPessoa());
        }
    }

    private String liberarVagaNaTurma(Integer codigoContrato, Integer codigoCliente, Integer qtdeCreditoRetirou, Integer saldoCredito, UsuarioVO usuarioVO)throws Exception{
        TipoHorarioCreditoTreinoEnum tipoHorarioCreditoTreinoEnum = consultarTipoHorario(codigoContrato);
        if (tipoHorarioCreditoTreinoEnum == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA){
            return liberarVagaNoHorarioTurma(codigoContrato, codigoCliente, qtdeCreditoRetirou, saldoCredito, usuarioVO);
        }else{
            return liberarVagaNoHorarioLivre(codigoContrato,qtdeCreditoRetirou, saldoCredito);
        }
    }

    private Calendar montarDataHoraHorarioTurma(Date dataAula, String horaMinuto)throws Exception{
        Calendar dataRetornar = Calendar.getInstance();
        dataRetornar.setTime(dataAula);
        String[]arrayHora = horaMinuto.split(":");
        dataRetornar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(arrayHora[0]));
        dataRetornar.set(Calendar.MINUTE, Integer.parseInt(arrayHora[1]));
        dataRetornar.set(Calendar.SECOND, 0);
        dataRetornar.set(Calendar.MILLISECOND, 0);
        return dataRetornar;
    }

    public Integer saldoVirtualAluno(Integer matricula, Integer contrato){
        try {
            TurmasServiceImpl turmaServiceDAO = new TurmasServiceImpl(con);
            String saldoAluno = turmaServiceDAO.consultarSaldoAluno(matricula, false, contrato);
            return Integer.valueOf(saldoAluno.split(";")[0]);
        }catch (Exception e){
            Uteis.logar(e, ControleCreditoTreino.class);
            return null;
        }
    }

    public String liberarVagaNoHorarioTurma(Integer codigoContrato, Integer codigoCliente, Integer qtdeCreditoRetirou, Integer saldoCredito, UsuarioVO usuarioVO)throws Exception{
        StringBuilder logRet = new StringBuilder();
        int totalAulaReporPerdeu = 0;
         Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("select codigomatricula from cliente where codigo = " + codigoCliente);
        rs.next();
        Integer matricula = rs.getInt("codigomatricula");
        TurmasServiceImpl turmaServiceDAO = new TurmasServiceImpl(con);
        Integer aulasExtra = turmaServiceDAO.nrAulasExtras(matricula, null);
        if(aulasExtra > 0){ //primeiro tem a tentativa de retirar aulas extras.
            if(qtdeCreditoRetirou > aulasExtra ){
                qtdeCreditoRetirou -= aulasExtra;
                totalAulaReporPerdeu = aulasExtra;
            } else {
                totalAulaReporPerdeu = qtdeCreditoRetirou;
                qtdeCreditoRetirou = 0;
            }
            logRet.insert(0, "- Aluno perdeu "+ totalAulaReporPerdeu + (totalAulaReporPerdeu > 1 ? " aulas " : " aula") +" a repor. Motivo: Saldo de crédito maior que a quantidade de aulas previstas. \n");
            if(qtdeCreditoRetirou.equals(0)){
                return logRet.toString();
            }
        }
        
        for (int i = 0; i< qtdeCreditoRetirou; i++){ // depois tentamos 
            List<ReposicaoVO> listaReposicao = getFacade().getReposicao().consultarReposicoesApartirDeUmaDataBase(Calendario.hoje(), codigoContrato, Uteis.NIVELMONTARDADOS_TODOS);
            List<HorarioTurmaVO> listaAulasPrevistas = getFacade().getMatriculaAlunoHorarioTurma().consultarAulasPrevistas(codigoContrato);
            List<HorarioTurmaVO> listaAulasPrevistaFinal = new ArrayList<HorarioTurmaVO>(listaAulasPrevistas);
            List<AulaDesmarcadaVO> listaAulaDesmarcadaNaoPodeSerReposta = getFacade().getAulaDesmarcada().consultarAulaDesmarcadaQueNaoPodeSerReposta(codigoContrato, Calendario.hoje(), Uteis.NIVELMONTARDADOS_TODOS);
            for (HorarioTurmaVO horarioTurmaPrevista: listaAulasPrevistas){
                for (ReposicaoVO reposicaoVO: listaReposicao){
                    // Retirar das aulas previstas as aulas futuras repostas que já foi utilizada pelo aluno.
                    Calendar dataHoraAulaOrigemReposicao = montarDataHoraHorarioTurma(reposicaoVO.getDataOrigem(), reposicaoVO.getHorarioTurmaOrigem().getHoraFinal());
                    if (Calendario.igualComHora(dataHoraAulaOrigemReposicao.getTime(), horarioTurmaPrevista.getDataAula())){
                        Calendar dataHoraAulaReposicao = montarDataHoraHorarioTurma(reposicaoVO.getDataReposicao(), reposicaoVO.getHorarioTurma().getHoraFinal());
                        if (Calendario.menorComHora(dataHoraAulaReposicao.getTime(), Calendario.hoje())){
                            // A reposição já passou, o aluno já usou a aula.
                            listaAulasPrevistaFinal.remove(horarioTurmaPrevista);
                        }
                    }
                }

                for (AulaDesmarcadaVO aulaDesmarcadaVO: listaAulaDesmarcadaNaoPodeSerReposta){
                    // Retirar das aulas previstas as aulas futuras desmarcadas que não podem ser repostas.
                    if (aulaDesmarcadaVO.getHorarioTurmaVO().getCodigo().equals(horarioTurmaPrevista.getCodigo())){
                        Calendar dataHoraAulaOrigemDesmarcada = montarDataHoraHorarioTurma(aulaDesmarcadaVO.getDataOrigem(), aulaDesmarcadaVO.getHorarioTurmaVO().getHoraFinal());
                        if (Calendario.igualComHora(dataHoraAulaOrigemDesmarcada.getTime(), horarioTurmaPrevista.getDataAula())){
                            listaAulasPrevistaFinal.remove(horarioTurmaPrevista);
                        }
                    }
                }
            }
            List<AulaDesmarcadaVO> desmarcadasPassado = getFacade().getAulaDesmarcada().consultarAulasDesmarcadasPassadasSemReposicao(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            // Ao lançar um ajuste manual negativo, o aluno perde a última aula prevista.
            List<AulaDesmarcadaVO> desmarcadasComReposicao = new ArrayList<AulaDesmarcadaVO>();//em ultimo caso, aulas desmarcadas com reposição serão retiradas
            Collections.sort(listaAulasPrevistaFinal, java.util.Collections.reverseOrder(HorarioTurmaVO.COMPARATOR_DATA_AULA));
            boolean aulaRetirada = false;
            for (HorarioTurmaVO ultimaAulaPrevista: listaAulasPrevistaFinal){
                AulaDesmarcadaVO aulaDesmarcadaVO = getFacade().getAulaDesmarcada().consultar(codigoContrato,ultimaAulaPrevista.getCodigo(),ultimaAulaPrevista.getDataAula(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (aulaDesmarcadaVO == null){
                    ContratoVO contratoVO = getFacade().getContrato().consultarPorCodigo(codigoContrato,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    AulaDesmarcadaVO obj = new AulaDesmarcadaVO();
                    obj.setPermiteReporAulaDesmarcada(false);
                    obj.setTurmaVO(new TurmaVO());
                    obj.getTurmaVO().setCodigo(ultimaAulaPrevista.getTurma());
                    obj.setHorarioTurmaVO(ultimaAulaPrevista);
                    obj.setDataOrigem(ultimaAulaPrevista.getDataAula());
                    obj.setContratoVO(contratoVO);
                    obj.setClienteVO(new ClienteVO());
                    obj.getClienteVO().setCodigo(codigoCliente);
                    obj.setEmpresaVO(contratoVO.getEmpresa());
                    obj.setUsuarioVO(usuarioVO);
                    obj.setOrigemSistemaEnum(OrigemSistemaEnum.ZW);
                    getFacade().getAulaDesmarcada().incluirSemCommit(obj);
                    logRet.append("- Aluno perdeu a aula do dia: ").append(retornarDataAulaApresentar(ultimaAulaPrevista,ultimaAulaPrevista.getDataAula())).append("\n");
                    getFacade().getAulaDesmarcada().atualizaPermiteReporAulaDesmarcada(obj.getCodigo(), false);
                    aulaRetirada = true;
                    break;
                }else{
                    if (UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getReposicaoVO().getCodigo()")){
                        desmarcadasComReposicao.add(aulaDesmarcadaVO);
                    }else{
                        logRet.append("- Aluno perdeu a aula do dia: ").append(retornarDataAulaApresentar(aulaDesmarcadaVO.getHorarioTurmaVO(),aulaDesmarcadaVO.getDataOrigem())).append("\n");
                        getFacade().getAulaDesmarcada().atualizaPermiteReporAulaDesmarcada(aulaDesmarcadaVO.getCodigo(), false);
                        aulaRetirada = true;
                        break;
                    }
                }
            }
            if(!aulaRetirada){
                for(AulaDesmarcadaVO aulaDesmarcadaVO : desmarcadasPassado){
                    logRet.append("- Aluno perdeu a aula do dia: ").append(retornarDataAulaApresentar(aulaDesmarcadaVO.getHorarioTurmaVO(),aulaDesmarcadaVO.getDataOrigem())).append("\n");
                    getFacade().getAulaDesmarcada().atualizaPermiteReporAulaDesmarcada(aulaDesmarcadaVO.getCodigo(), false);
                    aulaRetirada = true;
                    break;
                }
                if(!aulaRetirada){
                    for (AulaDesmarcadaVO aulaDesmarcadaVO : desmarcadasComReposicao){
                        ReposicaoVO reposicaoVO = getFacade().getReposicao().consultarPorCodigo(aulaDesmarcadaVO.getReposicaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                        boolean  excluiuReposicao = excluirReposicao(reposicaoVO);
                        if (excluiuReposicao){
                            logRet.append("- Aluno perdeu a aula do dia: ").append(retornarDataAulaApresentar(aulaDesmarcadaVO.getHorarioTurmaVO(),aulaDesmarcadaVO.getDataOrigem())).append(", ");
                            logRet.append("como o aluno já tinha reposto esta aula, a reposição também foi excluída:").append(retornarDataAulaApresentar(reposicaoVO.getHorarioTurma(),reposicaoVO.getDataReposicao())).append("\n");
                            getFacade().getAulaDesmarcada().atualizaPermiteReporAulaDesmarcada(aulaDesmarcadaVO.getCodigo(), false);
                            aulaRetirada = true;
                            break;
                        }
                    }
                } 
            }
        }
        return logRet.toString();
    }


    private String retornarDataAulaApresentar(HorarioTurmaVO horarioTurmaVO, Date data)throws Exception{
        StringBuilder log = new StringBuilder();
        Calendar dataRetornar = montarDataHoraHorarioTurma(data, horarioTurmaVO.getHoraInicial());
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        log.append(horarioTurmaVO.getIdentificadorTurma()).append(" ").append(sdf.format(dataRetornar.getTime()));
        return log.toString();
    }

    private boolean excluirReposicao(ReposicaoVO reposicaoVO)throws Exception{
        if (UtilReflection.objetoMaiorQueZero(reposicaoVO, "getCodigo()")){
            Calendar dataHoraAulaReposicao = montarDataHoraHorarioTurma(reposicaoVO.getDataReposicao(), reposicaoVO.getHorarioTurma().getHoraInicial());
            if (Calendario.maiorComHora(dataHoraAulaReposicao.getTime(), Calendario.hoje())){
                getFacade().getReposicao().excluir(reposicaoVO);
                return true;
            }
        }
        return false;
    }

    private String liberarVagaNoHorarioLivre(Integer codigoContrato, Integer qtdeCreditoRetirou, Integer saldoCredito)throws Exception{
        StringBuilder logRet = new StringBuilder();
        for (int i = 0; i< qtdeCreditoRetirou; i++) {
            List<ReposicaoVO> listaReposicao = getFacade().getReposicao().consultarReposicoesApartirDeUmaDataBase(Calendario.hoje(), codigoContrato, Uteis.NIVELMONTARDADOS_TODOS);
            if (listaReposicao.size() >= saldoCredito){
                // O aluno perde a última aula marcada.
                java.util.Collections.sort(listaReposicao, ReposicaoVO.COMPARATOR_DATA_HORA_AULA);
                ReposicaoVO ultimaAulaMarcada = listaReposicao.get(listaReposicao.size() -1);
                getFacade().getReposicao().excluir(ultimaAulaMarcada);
                logRet.append("Aula que o aluno vai perder: ").append(retornarDataAulaApresentar(ultimaAulaMarcada.getHorarioTurma(),ultimaAulaMarcada.getDataReposicao())).append("\n");
            }
            saldoCredito = saldoCredito - 1;
        }
        return logRet.toString();
    }


    private TipoHorarioCreditoTreinoEnum consultarTipoHorario(Integer codigoContrato)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cdt.tipoHorario \n");
        sql.append("from contratoduracaocreditotreino cdt \n");
        sql.append("inner join contratoduracao cd on cd.codigo = cdt.contratoDuracao \n");
        sql.append("where cd.contrato = ").append(codigoContrato);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
           return TipoHorarioCreditoTreinoEnum.getTipo(rs.getInt("tipoHorario"));
        }
        return null;
    }

    private boolean validaSeEstaNoHorarioTurma(HorarioTurmaVO horarioTurmaVO, Date dataVerificar)throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        String[] horaInicial = horarioTurmaVO.getHoraInicialComTolerancia().split(":");
        String[] horaFinal = horarioTurmaVO.getHoraFinal().split(":");
        //Date hInicial = sdf.parse(horaInicial);
        //Date hFinal = sdf.parse(horaFinal);
        Calendar hInicial = Calendario.getInstance();
        hInicial.setTime(dataVerificar);
        hInicial.set(Calendar.HOUR_OF_DAY, Integer.parseInt(horaInicial[0]));
        hInicial.set(Calendar.MINUTE, Integer.parseInt(horaInicial[1]));
        hInicial.set(Calendar.SECOND, 0);

        Calendar hFinal = Calendario.getInstance();
        hFinal.setTime(dataVerificar);
        hFinal.set(Calendar.HOUR_OF_DAY, Integer.parseInt(horaFinal[0]));
        hFinal.set(Calendar.MINUTE, Integer.parseInt(horaFinal[1]));
        hFinal.set(Calendar.SECOND, 0);
        if ((Calendario.igualComHora(dataVerificar,hInicial.getTime())) || (Calendario.entreComHora(dataVerificar,hInicial.getTime(),hFinal.getTime()))){
            return true;
        }

        return false;
    }

    private boolean diminuirCreditoTreinoPorUtilizacao(AcessoClienteVO acessoClienteVO, Integer codigoPessoa) throws Exception {
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Entrou no método diminuirCreditoTreinoPorUtilizacao ");
        boolean diminuirCredito = false;
        if(acessoClienteVO.getLocalAcesso().getIgnorarConsumoCredito()){
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO,"#### NÃO DIMINUIU OS CREDITOS LOCAL DE ACESSO MARCADO PARA IGNORAR CONSUMO CREDITO - " + acessoClienteVO.getLocalAcesso().getDescricao());
            return diminuirCredito;
        }
        //Logger.getLogger(AcessoCliente.class.getSimpleName()).log(Level.INFO,"ENTROU INICIO DIMINUIR CREDITO TREINO");
        boolean estaNoHorarioDaTurma = false;
        List<ContratoVO> listaContrato = getContratoDao().consultarContratosVigentesPorPessoa(codigoPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaContrato.size() <= 0) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO,"#### NÃO DIMINUIU OS CREDITOS LISTA DE CONTRATOS DO ALUNO ESTÁ VAZIA");
            return false;
        } else if (listaContrato.size() == 1 && !isDependentePlanoCompartilhado(acessoClienteVO)) {
            ContratoVO contratoVO = listaContrato.get(0);
            incluirControleCreditoTreinoUtilizacao(contratoVO.getCodigo(), codigoPessoa, acessoClienteVO);
            return true;
        }
        Calendar dataAcessoVerificar = Calendario.getInstance();
        dataAcessoVerificar.setTime(acessoClienteVO.getDataHoraEntrada());
        ContratoVO contratoCreditoHorarioLivre = null;
        ContratoVO contratoCreditoHorarioTurma = null;
        ContratoVO contratoNormalHorarioTurma = null;
        HorarioTurmaVO horarioTurmaVO = null;
        List<AlunoHorarioTurmaVO> listaAulasColetivas = getHorarioTurmaDao().consultarAulasColetivasAluno(acessoClienteVO.getDataHoraEntrada(), acessoClienteVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
        for (ContratoVO contratoVO : listaContrato) {
            List<ContratoModalidadeHorarioTurmaVO> lista = getContratoModalidadeHorarioTurmaDao().consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            if ((lista.size() <= 0) && (contratoVO.isVendaCreditoTreino())) {
                contratoVO.setContratoDuracaoCreditoTreinoVO(getContratoDuracaoCreditoTreinoDao().consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                if (contratoVO.getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum().equals(TipoHorarioCreditoTreinoEnum.LIVRE)) {
                    contratoCreditoHorarioLivre = contratoVO;
                    continue;
                }
            }
            estaNoHorarioDaTurma = false;
            for (ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurmaVO : lista) {
                if (contratoModalidadeHorarioTurmaVO.getHorarioTurma().getDiaSemanaNumero() == dataAcessoVerificar.get(Calendar.DAY_OF_WEEK)) {
                    if (validaSeEstaNoHorarioTurma(contratoModalidadeHorarioTurmaVO.getHorarioTurma(), acessoClienteVO.getDataHoraEntrada())) {
                        horarioTurmaVO = contratoModalidadeHorarioTurmaVO.getHorarioTurma();
                        estaNoHorarioDaTurma = true;
                        break;
                    }
                }
            }
            if (estaNoHorarioDaTurma) {
                if (contratoVO.isVendaCreditoTreino()) {
                    contratoCreditoHorarioTurma = contratoVO;
                } else {
                    contratoNormalHorarioTurma = contratoVO;
                }
                break;
            }
            if (contratoVO.isVendaCreditoTreino()) {
                List<ContratoModalidadeVO> modalidadesContrato = getContratoModalidadeDao().consultarPorCodigoContrato(contratoVO.getCodigo(),Uteis.NIVELMONTARDADOS_ROBO);
                for (AlunoHorarioTurmaVO alunoHorarioTurmaVO : listaAulasColetivas) {
                    if (validaSeEstaNoHorarioTurma(alunoHorarioTurmaVO.getHorarioTurma(), acessoClienteVO.getDataHoraEntrada())) {
                        TurmaVO turma = getTurmaDao().consultarPorChavePrimaria(alunoHorarioTurmaVO.getHorarioTurma().getTurma(),Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                        for (ContratoModalidadeVO contratoModalidadeVO: modalidadesContrato) {
                            if(contratoModalidadeVO.getModalidade().getCodigo().equals(turma.getModalidade().getCodigo()) ||
                                    (UteisValidacao.notEmptyNumber(turma.getModalidade().getTipo()) && contratoModalidadeVO.getModalidade().getTipo().equals(turma.getModalidade().getTipo()))){
                                incluirControleCreditoTreinoUtilizacao(contratoVO.getCodigo(), codigoPessoa, acessoClienteVO);
                                return true;
                            }

                        }
                    }
                }
            }

        }

        boolean reposicaoTurmaContratoNormal = false;
        // Verifica se o acesso trata-se de uma reposição de aula. Se a reposição for de um contrato de crédito, então diminuir o crédito do saldo.
        for (ContratoVO contratoVO : listaContrato) {
            List<ReposicaoVO> listaReposicao = getReposicaoDao().consultar(acessoClienteVO.getDataHoraEntrada(), contratoVO.getCodigo(), null, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            for (ReposicaoVO reposicaoVO : listaReposicao) {
                if (validaSeEstaNoHorarioTurma(reposicaoVO.getHorarioTurma(), acessoClienteVO.getDataHoraEntrada())) {
                    if (contratoVO.isVendaCreditoTreino()) {
                        incluirControleCreditoTreinoUtilizacao(contratoVO.getCodigo(), codigoPessoa, acessoClienteVO);
                        return true;
                    } else {
                        // Neste caso houve uma reposição de uma aula de um contrato que não é de crédito.
                        reposicaoTurmaContratoNormal = true;
                    }
                    break;
                }
            }
            if (reposicaoTurmaContratoNormal) {
                break;
            }
        }

        if ((estaNoHorarioDaTurma && contratoCreditoHorarioTurma == null) && (contratoCreditoHorarioLivre != null)) {
            // O aluno está no horário da turma de um plano que não é de crédito. Se a aula foi desmarcada, então diminuir crédito do contrato de crédito de horário livre.
            if (desmarcouAulaContrato(contratoNormalHorarioTurma, horarioTurmaVO, dataAcessoVerificar.getTime())) {
                incluirControleCreditoTreinoUtilizacao(contratoCreditoHorarioLivre.getCodigo(), codigoPessoa, acessoClienteVO);
                return true;
            }
        }

        if (!(reposicaoTurmaContratoNormal)) {
            if ((estaNoHorarioDaTurma && contratoCreditoHorarioTurma != null) ||
                    ((!estaNoHorarioDaTurma) && (contratoCreditoHorarioLivre != null))) {
                // diminuir crédito
                boolean flag = true;
                if ((contratoCreditoHorarioTurma != null) && (desmarcouAulaContrato(contratoCreditoHorarioTurma, horarioTurmaVO, dataAcessoVerificar.getTime()))) {
                    // Neste caso o aluno está acessando no horário da turma de um contrato de crédito. Porém se o mesmo tiver desmarcado a aula, então não diminuir crédito.
                    flag = false;
                }
                if (flag) {
                    Integer codigoContratoDiminuirCredito = (contratoCreditoHorarioTurma != null) ? contratoCreditoHorarioTurma.getCodigo() : contratoCreditoHorarioLivre.getCodigo();
                    incluirControleCreditoTreinoUtilizacao(codigoContratoDiminuirCredito, codigoPessoa, acessoClienteVO);
                    diminuirCredito = true;
                }
            }
        }

        return diminuirCredito;
    }

    private boolean isDependentePlanoCompartilhado(AcessoClienteVO acessoClienteVO) {
        try {
            Cliente clienteDAO = new Cliente(con);
            return clienteDAO.possuiTitularPlanoCompartilhadoDependente(acessoClienteVO.getCliente().getCodigo());
        } catch (Exception e) {
            Uteis.logar(e, ControleCreditoTreino.class);
        }
        return false;
    }

    private void excluirFaltaGeradaPeloRobo(Date dataAcesso, Integer codigoContrato, Integer codigoPessoa)throws Exception{
        /* Pode haver erro no zillyonAcesso onde não é enviado os acessos no mesmo dia. Desta forma, ao rodar o robô é gerado uma operação de FALTA e quando o
           zillyonAcesso consegue enviar o registro no dia seguinte, é incluido uma nova operação de UTILIZAÇÂO. Desta forma, o aluno perde dois créditos sendo
           que o correto seria perder somente 1(um)
        */
        List<HorarioTurmaVO> listaHorarioTurma = getHorarioTurmaDao().consultar(codigoContrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        List<ReposicaoVO> listaReposicao = getReposicaoDao().consultar(dataAcesso,codigoContrato,null,Uteis.NIVELMONTARDADOS_CONSULTA_WS);
        for (ReposicaoVO reposicaoVO: listaReposicao){
            listaHorarioTurma.add(reposicaoVO.getHorarioTurma());
        }
        Calendar dataOperacao = Calendario.getInstance();
        dataOperacao.setTime(dataAcesso);
        HorarioTurmaVO horarioTurmaValidar = null;
        String horaMinutoAcesso = dataOperacao.get(Calendar.HOUR_OF_DAY) + ":" + dataOperacao.get(Calendar.MINUTE);
        for (HorarioTurmaVO horarioTurmaVO: listaHorarioTurma){
            if (horarioTurmaVO.getDiaSemanaNumero() == dataOperacao.get(Calendar.DAY_OF_WEEK)){
                if (Calendario.horaEstaEntreIntervaloHoras(horaMinutoAcesso, horarioTurmaVO.getHoraInicialComTolerancia(), horarioTurmaVO.getHoraFinal())){
                    horarioTurmaValidar = horarioTurmaVO;
                    break;
                }
            }
        }
        if (horarioTurmaValidar != null){
            Calendar dataInicioValidar = montarDataHoraHorarioTurma(dataAcesso, horarioTurmaValidar.getHoraInicialComTolerancia());
            Calendar dataFimValidar = montarDataHoraHorarioTurma(dataAcesso, horarioTurmaValidar.getHoraFinal());
            List<ControleCreditoTreinoVO> lista = consultar(codigoContrato, TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO,dataInicioValidar.getTime(), dataFimValidar.getTime(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (ControleCreditoTreinoVO obj: lista){
                excluirPorCodigo(obj.getCodigo());
                incluirLogExclusaoFalta(obj, codigoContrato, codigoPessoa);
            }
        }
    }

    public Integer consultarTotalOperacao(Integer codigoContrato, TipoOperacaoCreditoTreinoEnum tipoOperacaoCreditoTreinoEnum)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) as total \n");
        sql.append("from controleCreditoTreino \n");
        sql.append("where contrato =  ").append(codigoContrato).append(" \n");
        sql.append("and tipoOperacaoCreditoTreino = ").append(tipoOperacaoCreditoTreinoEnum.getCodigo());
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return rs.getInt("total");
        }
        return 0;
    }

    public List<ControleCreditoTreinoVO> consultar(Integer codigoContrato, TipoOperacaoCreditoTreinoEnum tipoOperacaoCreditoTreinoEnum, Date dataInicioOperacao, Date dataFimOperacao, int nivelMontarDados)throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from controleCreditoTreino \n");
        sql.append("where contrato =  ").append(codigoContrato).append(" \n");
        sql.append("and tipoOperacaoCreditoTreino = ").append(tipoOperacaoCreditoTreinoEnum.getCodigo());
        if (dataInicioOperacao != null && dataFimOperacao != null){
            sql.append("and dataOperacao >= '").append(sdf.format(dataInicioOperacao)).append("' and dataOperacao <= '").append(sdf.format(dataFimOperacao)).append("'");
        }
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        return montarDadosConsulta(rs,nivelMontarDados);
    }

    private void incluirLogExclusaoFalta(ControleCreditoTreinoVO controleCreditoTreinoVO, Integer codigoContrato, Integer codigoPessoa) throws Exception {
        try{
            UsuarioVO usuarioVO = getUsuarioDao().getUsuarioRecorrencia();
            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAÇÃO");
            logVO.setChavePrimaria(String.valueOf(codigoContrato));
            logVO.setNomeEntidade("Contrato");
            logVO.setOperacao("ALTERAÇÃO - EXCLUSÃO DE FALTA.");
            logVO.setNomeEntidadeDescricao("Contrato");
            logVO.setResponsavelAlteracao(usuarioVO.getNome());
            logVO.setUserOAMD(usuarioVO.getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            StringBuilder msgLog = new StringBuilder();
            msgLog.append("Pode haver erro no zillyonAcesso onde não é enviado os acessos no mesmo dia. Desta forma, ao rodar o robô é gerado uma operação de FALTA. \n");
            msgLog.append("Portanto quando o zillyonAcesso enviar este acesso, devemos excluir a FALTA gerada pelo Robô. \n\n\n");
            msgLog.append("OPERAÇÃO EXCLUIDA: FALTA \n");
            msgLog.append("DATA DE LANÇAMENTO DA FALTA:").append(controleCreditoTreinoVO.getDataLancamento_Apresentar()).append("\n");
            msgLog.append("DATA PREVISTA DA AULA:").append(controleCreditoTreinoVO.getDataOperacao_Apresentar());
            logVO.setValorCampoAlterado(msgLog.toString());
            logVO.setPessoa(codigoPessoa);

            getLogDao().incluirSemCommit(logVO);

        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO GRAVAR LOG DE EXLUSÃO DE NÃO COMPARECIMENTO AULA DE CREDITO. CONTRATO =" + codigoContrato);
        }
    }

    private boolean desmarcouAulaContrato(ContratoVO contratoCreditoHorarioTurma, HorarioTurmaVO horarioTurmaVO, Date dataAcesso)throws Exception{
        AulaDesmarcadaVO aulaDesmarcadaVO = getAulaDesmarcadaDao().consultar(contratoCreditoHorarioTurma.getCodigo(), horarioTurmaVO.getCodigo(), dataAcesso,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getCodigo()")){
            if (UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getReposicaoVO().getCodigo()")){
                // verificar se aula desmarcada foi reposta para a mesma data/hora em que foi desmarcada.
                // Exemplo: Na segunda feira, o aluno desmarca a aula de terça 11:00 as 12:00 e depois muda de idéia e diz que vai na aula novamente.
                if (Calendario.igual(aulaDesmarcadaVO.getDataReposicao(), aulaDesmarcadaVO.getDataOrigem())){
                    ReposicaoVO reposicaoVO = getReposicaoDao().consultarPorChavePrimaria(aulaDesmarcadaVO.getReposicaoVO().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (reposicaoVO.getHorarioTurma().getCodigo().equals(horarioTurmaVO.getCodigo())){
                        return false;
                    }else{
                        return true;
                    }
                }else{
                    return true;
                }
            }else{
                return true;
            }
        }else{
            return false;
        }

    }

    private void incluirControleCreditoTreinoUtilizacao(Integer codigoContrato, Integer codigoPessoa, AcessoClienteVO acessoClienteVO) throws Exception {
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Entrou no método incluirControleCreditoTreinoUtilizacao ");
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### PARÂMETROS");
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### codigoContrato - " + codigoContrato);
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### codigoPessoa - " + codigoPessoa);
        if (acessoClienteVO != null){
            if (acessoClienteVO.getUsuario() != null) {
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### acessoClienteVO.getUsuario().getCodigo() - " + acessoClienteVO.getUsuario().getCodigo());
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### acessoClienteVO.getUsuario().getNome() - " + acessoClienteVO.getUsuario().getNome());
            } else {
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### acessoClienteVO.getUsuario() está nulo");
            }
            if (acessoClienteVO.getCliente() != null) {
                if (acessoClienteVO.getCliente().getUsuarioAux() != null) {
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### acessoClienteVO.getCliente().getUsuarioAux().getCodigo() - " + acessoClienteVO.getCliente().getUsuarioAux().getCodigo());
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### acessoClienteVO.getCliente().getUsuarioAux().getNome() - " + acessoClienteVO.getCliente().getUsuarioAux().getNome());
                } else {
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### acessoClienteVO.getCliente().getUsuarioAux() está nulo");
                }
            } else {
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### acessoClienteVO.getCliente() está nulo");
            }
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### acessoClienteVO.getDataHoraEntrada() - " + acessoClienteVO.getDataHoraEntrada());
        }
        excluirFaltaGeradaPeloRobo(acessoClienteVO.getDataHoraEntrada(), codigoContrato, codigoPessoa);
        //Logger.getLogger(AcessoCliente.class.getSimpleName()).log(Level.INFO,"ENTROU DIMINUIR CREDITO TREINO");
        ControleCreditoTreino controleCreditoTreino = new ControleCreditoTreino(con);
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Vou montar os dados do ControleCreditoTreino - " + codigoPessoa);
        ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
        controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.UTILIZACAO);
        controleCreditoTreinoVO.setAcessoClienteVO(acessoClienteVO);
        controleCreditoTreinoVO.setQuantidade(-1);
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(codigoContrato);
        controleCreditoTreinoVO.setContratoVO(contratoVO);
        controleCreditoTreinoVO.setDataOperacao(acessoClienteVO.getDataHoraEntrada());
        controleCreditoTreinoVO.setUsuarioVO((acessoClienteVO.getUsuario() != null) ? acessoClienteVO.getUsuario() : acessoClienteVO.getCliente().getUsuarioAux());
        controleCreditoTreino.incluirSemCommit(controleCreditoTreinoVO, acessoClienteVO.getCliente().getCodigo(), getSituacaoClienteSinteticoDWDao(), null);
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### ControleCreditoTreino incluído com sucesso - " + codigoPessoa);
        if(Calendario.menor(acessoClienteVO.getDataHoraEntrada(), Calendario.hoje())){
            verificarAjustarProcessamentoMensal(codigoContrato, acessoClienteVO, controleCreditoTreino, getSituacaoClienteSinteticoDWDao(), null);
        }
        controleCreditoTreino = null;
    }

    private void verificarAjustarProcessamentoMensal(Integer codigoContrato, AcessoClienteVO acessoClienteVO, ControleCreditoTreino controleCreditoTreino,SituacaoClienteSinteticoDW situacaoClienteSinteticoDW, Date dataBase) throws Exception {
        ControleCreditoTreinoVO ajustemensal = controleCreditoTreino.consultarOperacaoAjusteMensalFuturo(codigoContrato , acessoClienteVO.getDataHoraEntrada(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (ajustemensal != null) {
            ajustemensal.setQuantidade(ajustemensal.getQuantidade() + 1);
            if(ajustemensal.getQuantidade().equals(0)){
                controleCreditoTreino.excluirPorCodigo(ajustemensal.getCodigo());
                situacaoClienteSinteticoDW.atualizarInformacoesCreditoTreino(acessoClienteVO.getCliente().getPessoa().getCodigo(), dataBase);
            } else {
                controleCreditoTreino.alterarSemCommit(ajustemensal, situacaoClienteSinteticoDW, dataBase);
            }
        }
    }


    public ControleCreditoTreinoVO consultarOperacaoTransferenciaSaldo(Integer codigoContrato, boolean retiradaSaldo, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from controleCreditoTreino \n");
        sql.append("where contrato = ").append(codigoContrato).append(" and tipoOperacaoCreditoTreino = ").append(TipoOperacaoCreditoTreinoEnum.TRANSFERENCIA_SALDO.getCodigo()).append(" \n");
        if (retiradaSaldo){
            sql.append(" and quantidade < 0");
        }else{
            sql.append(" and quantidade > 0");
        }
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return montarDados(rs, nivelMontarDados);
        }
        return null;
    }

    @Override
    public List<ControleCreditoTreinoVO> consultarSimples(Integer matricula, Date data, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder("SELECT cc.* from controlecreditotreino cc\n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigocontrato = cc.contrato AND sc.matricula = ? \n");
        if(data != null){
            sql.append(" AND cc.dataoperacao <= ?\n");
        }
        sql.append(" ORDER BY cc.dataoperacao");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, matricula);
        if(data != null){
            pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(data));
        }
        ResultSet rs = pst.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados);
    }

    @Override
    public Contrato getContratoDao()throws Exception{
        if (contratoDao == null){
            contratoDao = new Contrato(con);
        }
        return contratoDao;
    }

    public void setContratoDao(Contrato contratoDao) {
        this.contratoDao = contratoDao;
    }

    public ContratoDuracaoCreditoTreino getContratoDuracaoCreditoTreinoDao() throws Exception{
        if (contratoDuracaoCreditoTreinoDao == null){
            contratoDuracaoCreditoTreinoDao = new ContratoDuracaoCreditoTreino(con);
        }
        return contratoDuracaoCreditoTreinoDao;
    }

    public void setContratoDuracaoCreditoTreinoDao(ContratoDuracaoCreditoTreino contratoDuracaoCreditoTreinoDao) {
        this.contratoDuracaoCreditoTreinoDao = contratoDuracaoCreditoTreinoDao;
    }

    @Override
    public Reposicao getReposicaoDao() throws Exception{
        if (reposicaoDao == null){
            reposicaoDao = new Reposicao(con);
        }
        return reposicaoDao;
    }

    public void setReposicaoDao(Reposicao reposicaoDao) {
        this.reposicaoDao = reposicaoDao;
    }

    @Override
    public AulaDesmarcada getAulaDesmarcadaDao() throws Exception {
        if (aulaDesmarcadaDao == null){
            aulaDesmarcadaDao = new AulaDesmarcada(con);
        }
        return aulaDesmarcadaDao;
    }

    public void setAulaDesmarcadaDao(AulaDesmarcada aulaDesmarcadaDao) {
        this.aulaDesmarcadaDao = aulaDesmarcadaDao;
    }

    @Override
    public ContratoModalidadeHorarioTurma getContratoModalidadeHorarioTurmaDao() throws Exception{
        if (contratoModalidadeHorarioTurmaDao == null){
            contratoModalidadeHorarioTurmaDao = new ContratoModalidadeHorarioTurma(con);
        }
        return contratoModalidadeHorarioTurmaDao;
    }

    public void setContratoModalidadeHorarioTurmaDao(ContratoModalidadeHorarioTurma contratoModalidadeHorarioTurmaDao) {
        this.contratoModalidadeHorarioTurmaDao = contratoModalidadeHorarioTurmaDao;
    }

    @Override
    public Usuario getUsuarioDao() throws Exception{
        if (usuarioDao == null){
            usuarioDao = new Usuario(con);
        }
        return usuarioDao;
    }

    public void setUsuarioDao(Usuario usuarioDao) {
        this.usuarioDao = usuarioDao;
    }

    @Override
    public HorarioTurma getHorarioTurmaDao() throws Exception{
        if (horarioTurmaDao == null){
            horarioTurmaDao = new HorarioTurma(con);
        }
        return horarioTurmaDao;
    }

    public void setHorarioTurmaDao(HorarioTurma horarioTurmaDao) {
        this.horarioTurmaDao = horarioTurmaDao;
    }

    @Override
    public SituacaoClienteSinteticoDW getSituacaoClienteSinteticoDWDao()throws Exception{
        if (situacaoClienteSinteticoDWDao == null){
            situacaoClienteSinteticoDWDao = new SituacaoClienteSinteticoDW(con);
        }
        return situacaoClienteSinteticoDWDao;
    }

    public void setSituacaoClienteSinteticoDWDao(SituacaoClienteSinteticoDW situacaoClienteSinteticoDWDao) {
        this.situacaoClienteSinteticoDWDao = situacaoClienteSinteticoDWDao;
    }

    public Log getLogDao()throws Exception {
        if (logDao == null){
            logDao = new Log(con);
        }
        return logDao;
    }

    public boolean existeControleCredito(Integer horarioTurma) throws Exception {
        return existe("SELECT codigo FROM controlecreditotreino WHERE horarioturmafalta = " + horarioTurma, con);
    }

    public void atualizarUtilizacaoParaFaltaExclusaoAcesso(AcessoClienteVO acessoVO, String observacao, boolean forcarExclusao)throws Exception{
        ControleCreditoTreinoVO controleCreditoTreinoVO = consultarPorParametros(null, null, acessoVO.getCodigo());
        if(UteisValidacao.notEmptyNumber(controleCreditoTreinoVO.getCodigo())) {
            TipoHorarioCreditoTreinoEnum tipoHorarioCreditoTreinoEnum = consultarTipoHorario(controleCreditoTreinoVO.getContratoVO().getCodigo());
            if (tipoHorarioCreditoTreinoEnum == TipoHorarioCreditoTreinoEnum.LIVRE || forcarExclusao) {
                excluirPorCodigo(controleCreditoTreinoVO.getCodigo());
                Date dataUltimoAjusteMensal = obterDataUltimaOperacao(controleCreditoTreinoVO.getContratoVO().getCodigo(), TipoOperacaoCreditoTreinoEnum.AJUSTE_MENSAL);
                if(dataUltimoAjusteMensal == null || Calendario.maiorComHora(controleCreditoTreinoVO.getDataOperacao(), dataUltimoAjusteMensal)){
                    SituacaoClienteSinteticoDW situacaoClienteSinteticoDW = new SituacaoClienteSinteticoDW(con);
                    situacaoClienteSinteticoDW.atualizarInformacoesCreditoTreino(acessoVO.getCliente().getPessoa().getCodigo(), Calendario.hoje());
                }
            } else {
                String sql = "UPDATE controlecreditotreino set tipooperacaocreditotreino = ?, observacao= ?, acessocliente = ?  where acessocliente = ?";
                PreparedStatement pst = con.prepareStatement(sql);

                pst.setInt(1, TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO.getCodigo());
                pst.setString(2, observacao);
                pst.setNull(3, Types.NULL);
                pst.setInt(4, acessoVO.getCodigo());
                pst.execute();
            }
        }
    }

    private Date obterDataUltimaOperacao(Integer codigoContrato, TipoOperacaoCreditoTreinoEnum tipoOperacao) throws SQLException {
        String sql = "select max(dataoperacao) as dataUltimaOperacao  from controlecreditotreino  where   contrato = "+codigoContrato+" and tipooperacaocreditotreino  = "+ tipoOperacao.getCodigo();
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql);
        if (rs.next()){
            return rs.getTimestamp("dataUltimaOperacao");
        }
        return null;
    }

    public ControleCreditoTreinoVO consultarPorParametros(Integer aulaDesmarcada, Integer reposicao, Integer codigoAcesso)throws Exception{
        ControleCreditoTreinoVO  novo = new ControleCreditoTreinoVO();
        String where = " where ";
        if(!UteisValidacao.emptyNumber(aulaDesmarcada)){
            where += " cred.auladesmarcada = " + aulaDesmarcada + " and cred.reposicao is null";
        }  else  if(!UteisValidacao.emptyNumber(reposicao)){
            where += " cred.reposicao = " + reposicao;
        }  else  if(!UteisValidacao.emptyNumber(codigoAcesso)){
            where += " cred.acessocliente = " + codigoAcesso;
        } else {
            return novo;
        }
        String sql = getSqlOtimizada(where, "");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql);
        if (rs.next()){
            return montarDadosConsulta(rs);
        }
        return novo;
    }
    
    
    public void alterarSemCommit(ControleCreditoTreinoVO controleCreditoTreinoVO,SituacaoClienteSinteticoDW situacaoClienteSinteticoDW, Date dataBase)throws Exception{
        ControleCreditoTreinoVO.validarDados(controleCreditoTreinoVO);
        preencherModalidadeControleCreditoTreino(controleCreditoTreinoVO);
        String sql = "update controleCreditoTreino set contrato = ?,tipoOperacaoCreditoTreino =?,quantidade=? ,datalancamento=?, dataOperacao=?, usuario=?, "
                + "acessoCliente=?,reposicao=?,auladesmarcada=?,observacao=?, contratoOrigem=?, horarioTurmaFalta=?, modalidade = ? where codigo = ?;";
        PreparedStatement pst = con.prepareStatement(sql.toString());
        int i = 1;
        pst.setInt(i++, controleCreditoTreinoVO.getContratoVO().getCodigo());
        pst.setInt(i++, controleCreditoTreinoVO.getTipoOperacaoCreditoTreinoEnum().getCodigo());
        pst.setInt(i++, controleCreditoTreinoVO.getQuantidade());
        pst.setTimestamp(i++, Uteis.getDataJDBCTimestamp(controleCreditoTreinoVO.getDatalancamento()));
        pst.setTimestamp(i++, Uteis.getDataJDBCTimestamp(controleCreditoTreinoVO.getDataOperacao()));
        pst.setInt(i++, controleCreditoTreinoVO.getUsuarioVO().getCodigo());
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getAcessoClienteVO().getCodigo()")){
            pst.setInt(i++, controleCreditoTreinoVO.getAcessoClienteVO().getCodigo());
        }else{
            pst.setNull(i++, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getReposicaoVO().getCodigo()")){
            pst.setInt(i++,controleCreditoTreinoVO.getReposicaoVO().getCodigo());
        }else{
            pst.setNull(i++, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getAulaDesmarcadaVO().getCodigo()")){
            pst.setInt(i++, controleCreditoTreinoVO.getAulaDesmarcadaVO().getCodigo());
        }else{
            pst.setNull(i++, Types.NULL);
        }
        if ((controleCreditoTreinoVO.getObservacao() != null) && (!controleCreditoTreinoVO.getObservacao().trim().equals(""))){
            pst.setString(i++, controleCreditoTreinoVO.getObservacao());
        }else{
            pst.setNull(i++, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getContratoOrigem().getCodigo()")){
            pst.setInt(i++, controleCreditoTreinoVO.getContratoOrigem().getCodigo());
        }else{
            pst.setNull(i++, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(controleCreditoTreinoVO, "getHorarioTurmaFalta().getCodigo()")){
            pst.setInt(i++, controleCreditoTreinoVO.getHorarioTurmaFalta().getCodigo());
        }else{
            pst.setNull(i++, Types.NULL);
        }
        if (!UteisValidacao.emptyNumber(controleCreditoTreinoVO.getModalidadeVO().getCodigo())){
            pst.setInt(i++, controleCreditoTreinoVO.getModalidadeVO().getCodigo());
        }else{
            pst.setNull(i++, Types.NULL);
        }
        
        pst.setInt(i++, controleCreditoTreinoVO.getCodigo());
        pst.execute();
        if (controleCreditoTreinoVO.getQuantidade() != 0 && situacaoClienteSinteticoDW != null) {
            atualizarInformacoesCredito(controleCreditoTreinoVO.getContratoVO().getCodigo(), situacaoClienteSinteticoDW, dataBase);
        }
    }

    private void preencherModalidadeControleCreditoTreino(ControleCreditoTreinoVO obj) {
        try {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Entrou no método preencherModalidadeControleCreditoTreino");
            if (!UteisValidacao.emptyNumber(obj.getModalidadeVO().getCodigo())) {
                return;
            }

            StringBuilder sql = new StringBuilder();
            if (obj.getHorarioTurmaFalta() != null  && !UteisValidacao.emptyNumber(obj.getHorarioTurmaFalta().getCodigo())) {

                sql.append("select \n");
                sql.append("t.modalidade as modalidade \n");
                sql.append("from horarioturma ht \n");
                sql.append("inner join turma t on t.codigo = ht.turma \n");
                sql.append("where ht.codigo = ").append(obj.getHorarioTurmaFalta().getCodigo());

            } else if (obj.getAulaDesmarcadaVO() != null  && !UteisValidacao.emptyNumber(obj.getAulaDesmarcadaVO().getCodigo())) {

                sql.append("select  \n");
                sql.append("t.modalidade as modalidade \n");
                sql.append("from auladesmarcada a  \n");
                sql.append("inner join turma t on t.codigo = a.turma \n");
                sql.append("where a.codigo = ").append(obj.getAulaDesmarcadaVO().getCodigo());

            } else if (obj.getReposicaoVO() != null  && !UteisValidacao.emptyNumber(obj.getReposicaoVO().getCodigo())) {

                sql.append("select \n");
                sql.append("t.modalidade as modalidade \n");
                sql.append("from reposicao r \n");
                sql.append("inner join turma t on t.codigo = r.turmaorigem \n");
                sql.append("where r.codigo = ").append(obj.getReposicaoVO().getCodigo());

            } else {
                obj.setModalidadeVO(new ModalidadeVO());
                return;
            }

            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sql.toString());
            Integer modalidade = 0;
            if (rs.next()) {
                modalidade = rs.getInt("modalidade");
            }
            obj.getModalidadeVO().setCodigo(modalidade);
        } catch (Exception ignored) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Erro aconteceu no método preencherModalidadeControleCreditoTreino - " + ignored.getMessage());
        } finally {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Saiu do método preencherModalidadeControleCreditoTreino");
        }
    }

    public ControleCreditoTreinoVO consultarOperacaoAjusteMensalFuturo(Integer codigoContrato, Date dataAvaliar, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from controleCreditoTreino \n");
        sql.append("where contrato = ").append(codigoContrato).append(" and tipoOperacaoCreditoTreino = ").append(TipoOperacaoCreditoTreinoEnum.AJUSTE_MENSAL.getCodigo()).append(" \n");
        sql.append(" and dataoperacao >  '").append(Uteis.getDataBDComHHMM(dataAvaliar)).append("'");
        sql.append(" order by codigo limit 1");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return montarDados(rs, nivelMontarDados);
        }
        return null;
    }


    public ContratoModalidade getContratoModalidadeDao() throws Exception {
        if (contratoModalidadeDao == null){
            contratoModalidadeDao = new ContratoModalidade(con);
        }
        return contratoModalidadeDao;
    }

    public void setContratoModalidadeDao(ContratoModalidade contratoModalidade) {
        this.contratoModalidadeDao = contratoModalidade;
    }

    public Turma getTurmaDao() throws Exception {
        if (turmaDao == null){
            turmaDao = new Turma(con);
        }
        return turmaDao;
    }

    public void setTurmaDao(Turma turmaDao) {
        this.turmaDao = turmaDao;
    }

    public Integer consultarUtilizacaoCreditoPorContrato(Integer codigoContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT SUM(quantidade) AS total FROM controlecreditotreino c\n");
        sql.append("WHERE contrato = ").append(codigoContrato).append("\n");
        sql.append("AND quantidade < 0;");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return rs.getInt("total");
        }
        return 0;
    }

    public Integer consultarSaldoCredito(Integer codigoContrato) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select sum(quantidade) as total \n");
        sql.append("from controleCreditoTreino \n");
        sql.append("where contrato = ").append(codigoContrato);
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return rs.getInt("total");
        }
        return 0;
    }

    public boolean existeDescontoParaAulaDesmarcada(AulaDesmarcadaVO aulaDesmarcadaVO) throws Exception {
        Calendar dataAula = Calendario.getInstance();
        dataAula.setTime(aulaDesmarcadaVO.getDataOrigem());
        String horaIni = aulaDesmarcadaVO.getHorarioTurmaVO().getHoraInicial();
        dataAula.set(Calendar.HOUR, Integer.parseInt(horaIni.split(":")[0]));
        dataAula.set(Calendar.MINUTE, Integer.parseInt(horaIni.split(":")[1]));
        dataAula.set(Calendar.SECOND, 0);
        dataAula.set(Calendar.MILLISECOND, 0);

        return existe("SELECT codigo FROM controlecreditotreino WHERE contrato = "+aulaDesmarcadaVO.getContratoVO().getCodigo()+" and tipooperacaocreditotreino in ("+TipoOperacaoCreditoTreinoEnum.UTILIZACAO.getCodigo()+","+TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO.getCodigo()+") and  DATE_TRUNC('minute', dataoperacao) = '" +Uteis.getDataHoraJDBC(aulaDesmarcadaVO.getDataOrigem(),horaIni ) +"'", con);
    }

    public JSONArray validarCreditos(boolean debitar) throws Exception {
        JSONArray invalidos = new JSONArray();
        String sql = "select cdt.quantidadecreditodisponivel, c.codigo, c.pessoa from contrato c " +
                " inner join contratoduracao cd on cd.contrato = c.codigo" +
                " inner join contratoduracaocreditotreino cdt on cd.codigo = cdt.contratoduracao" +
                " where c.situacao = 'AT' and c.vendaCreditoTreino = true and cdt.tipohorario = 2 ";
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()){
            while(rs.next()){
                String nomeCliente = "";
                int cliente = 0;
                try (ResultSet rsSintetico = con.prepareStatement("select nomecliente, codigocontrato, saldocreditotreino, codigocliente " +
                        " from situacaoclientesinteticodw s where codigopessoa = " + rs.getString("pessoa")).executeQuery()){
                    int saldoContrato = rs.getInt("quantidadecreditodisponivel");
                    while(rsSintetico.next()){
                        nomeCliente = rsSintetico.getString("nomecliente");
                        cliente = rsSintetico.getInt("codigocliente");

                    }
                    int contrato = rs.getInt("codigo");
                    ResultSet saldoCompra = con.prepareStatement("select sum(quantidade) as saldo from controlecreditotreino c\n" +
                            " where contrato = " + contrato +
                            " and quantidade > 0").executeQuery();
                    int saldoCompradosAjustados = 0;
                    if(saldoCompra.next()){
                        saldoCompradosAjustados = saldoCompra.getInt("saldo");
                    }

                    ResultSet saldoRemovidoRs = con.prepareStatement("select sum(quantidade) as saldo from controlecreditotreino c\n" +
                            " where contrato = " + contrato +
                            " and quantidade < 0").executeQuery();
                    int saldoRemovido = 0;
                    if(saldoRemovidoRs.next()){
                        saldoRemovido = saldoRemovidoRs.getInt("saldo");
                    }

                    ResultSet aulasMarcadasRs = con.prepareStatement("select count(c.codigo) as saldo from controlecreditotreino c\n" +
                            " inner join reposicao r on r.codigo = c.reposicao  " +
                            " where c.contrato = " + contrato +
                            " and r.datareposicao < current_date ").executeQuery();
                    int reposicoes = 0;
                    if(aulasMarcadasRs.next()){
                        reposicoes = aulasMarcadasRs.getInt("saldo");
                    }
                    int saldoReal = (saldoCompradosAjustados - saldoRemovido - (reposicoes  - saldoRemovido));
                    if(saldoReal < saldoContrato ){
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("cliente",  nomeCliente);
                        jsonObject.put("contrato",  contrato);
                        jsonObject.put("saldo contrato", saldoContrato);
                        jsonObject.put("saldo real", saldoReal);
                        if(debitar){
                            debitarCreditoPresenca(saldoContrato - saldoReal, contrato, cliente);
                        }
                        invalidos.put(jsonObject);
                    }

                }


            }
        }
        return invalidos;

    }


    public void debitarCreditoPresenca(Integer quantidade, Integer contrato,
                                              Integer cliente) throws Exception{
        ControleCreditoTreino controleCreditoTreino = new ControleCreditoTreino(con);
        ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
        controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO);
        controleCreditoTreinoVO.setQuantidade(-1 * quantidade);
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contrato);
        controleCreditoTreinoVO.setContratoVO(contratoVO);
        UsuarioVO usuarioVO = new UsuarioVO();
        usuarioVO.setCodigo(3);
        controleCreditoTreinoVO.setUsuarioVO(usuarioVO);
        controleCreditoTreino.incluirSemCommit(controleCreditoTreinoVO,
                cliente, null, null);
    }

    public void descontarCreditoAulaMarcada(Integer codigoCliente, Integer codigoContrato, AlunoHorarioTurmaVO alunoHorarioTurmaVO, String nomeModalidade) throws Exception {
        ControleCreditoTreino controleCreditoTreino = new ControleCreditoTreino(con);
        ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
        controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.MARCOU_AULA);
        controleCreditoTreinoVO.setQuantidade(-1);
        controleCreditoTreinoVO.setContratoVO(new ContratoVO());
        controleCreditoTreinoVO.getContratoVO().setCodigo(codigoContrato);
        controleCreditoTreinoVO.setUsuarioVO(new UsuarioVO());
        controleCreditoTreinoVO.getUsuarioVO().setCodigo(3);
        controleCreditoTreinoVO.setDataOperacao(Calendario.hoje());
        controleCreditoTreinoVO.setHorarioTurma(alunoHorarioTurmaVO.getHorarioTurma());
        controleCreditoTreinoVO.setDiaAula(alunoHorarioTurmaVO.getData());
        controleCreditoTreinoVO.setDescricaoAulaMarcada(String.format("%s - %s - %s - %s às %s ",
                nomeModalidade,
                Calendario.getDataAplicandoFormatacao(alunoHorarioTurmaVO.getData(), "dd/MM/yyyy"),
                alunoHorarioTurmaVO.getHorarioTurma().getDiaSemana_Apresentar   (),
                alunoHorarioTurmaVO.getHorarioTurma().getHoraInicial(),
                alunoHorarioTurmaVO.getHorarioTurma().getHoraFinal()));
        controleCreditoTreino.incluirSemCommit(controleCreditoTreinoVO, codigoCliente, getSituacaoClienteSinteticoDWDao(), null);
    }

    public boolean descontouCreditoAoMarcarAula(Integer codigoCliente, HorarioTurmaVO horarioTurmaVO, Date data) throws Exception {
        try {
            return existe(sqlConsultarDescontoCreditoPorAulaMarcada(codigoCliente, horarioTurmaVO, data), con);
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### ControleCreditoTreino -> descontouCreditoAoMarcarAula -> ERRO: " + e.getMessage());
            return false;
        }
    }

    public void atualizarControleCreditoTreinoAulaColetivaDesmarcada(Integer codigoCliente, Integer codigoHorarioTurma, Date data, UsuarioVO usuarioVO) {
        try {
            if (UteisValidacao.emptyNumber(codigoCliente)) {
                throw new ConsistirException("Cliente não informado");
            }

            if (data == null) {
                throw new ConsistirException("Data não informada");
            }

            HorarioTurmaVO horarioTurmaVO = getHorarioTurmaDao().consultarPorCodigo(codigoHorarioTurma, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (horarioTurmaVO == null || UteisValidacao.emptyNumber(horarioTurmaVO.getCodigo())) {
                throw new ConsistirException("Horário da turma não encontrado");
            }

            ResultSet rs = criarConsulta(sqlConsultarDescontoCreditoPorAulaMarcada(codigoCliente, horarioTurmaVO, data), con);
            if (rs.next()) {
                ResultSet rsControleCreditoTreino = criarConsulta(getSqlOtimizada(" WHERE cred.codigo = " + rs.getString("codigo"), ""), con);
                ControleCreditoTreinoVO controleCreditoTreinoVO = null;
                if (rsControleCreditoTreino.next()) {
                    controleCreditoTreinoVO = montarDados(rsControleCreditoTreino, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } else {
                    return;
                }

                String obs = "Dia " + Uteis.getDataComHHMM(Calendario.hoje());
                if (usuarioVO != null && !UteisValidacao.emptyString(usuarioVO.getNomeAbreviado())) {
                    obs += ", o usuario " + usuarioVO.getNomeAbreviado()
                            + " excluiu a " + controleCreditoTreinoVO.getTipoOperacaoCreditoTreinoEnum().getDescricaoCurta()
                            + ": " + controleCreditoTreinoVO.getDescricaoAulaMarcada();
                } else {
                    obs += ", houve exclusão da " + controleCreditoTreinoVO.getTipoOperacaoCreditoTreinoEnum().getDescricaoCurta()
                            + ": " + controleCreditoTreinoVO.getDescricaoAulaMarcada();
                }
                Date dataHoraAula = Calendario.getDataComHora(data, horarioTurmaVO.getHoraInicial());
                if (Calendario.menorComHora(Calendario.hoje(), dataHoraAula)) {
                    controleCreditoTreinoVO.setQuantidade(0);
                } else {
                    controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO);
                }
                controleCreditoTreinoVO.setObservacao(obs);
                alterarSemCommit(controleCreditoTreinoVO, getSituacaoClienteSinteticoDWDao(), null);
                if (UteisValidacao.emptyNumber(controleCreditoTreinoVO.getQuantidade())) {
                    atualizarInformacoesCredito(controleCreditoTreinoVO.getContratoVO().getCodigo(),  getSituacaoClienteSinteticoDWDao(), null);
                }
            }
        } catch (ConsistirException e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### ControleCreditoTreino -> atualizarControleCreditoTreinoAulaColetivaDesmarcada -> " + e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### ControleCreditoTreino -> atualizarControleCreditoTreinoAulaColetivaDesmarcada -> " + e.getMessage());
            e.printStackTrace();
        }
    }

    private String sqlConsultarDescontoCreditoPorAulaMarcada(Integer codigoCliente, HorarioTurmaVO horarioTurmaVO, Date data) throws Exception {
        if (horarioTurmaVO == null || UteisValidacao.emptyNumber(horarioTurmaVO.getCodigo())) {
            throw new ConsistirException("Horário da turma não encontrado");
        } else if (UteisValidacao.emptyString(horarioTurmaVO.getHoraInicial())) {
            horarioTurmaVO = getHorarioTurmaDao().consultarPorCodigo(horarioTurmaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        Date dataHoraAula = Calendario.getDataComHora(data, horarioTurmaVO.getHoraInicial());
        return "SELECT cct.codigo FROM controlecreditotreino cct\n" +
                "\tINNER JOIN contrato con ON con.codigo = cct.contrato \n" +
                "\tINNER JOIN cliente cli ON cli.pessoa = con.pessoa \n" +
                "\tINNER JOIN horarioturma ht ON ht.codigo = cct.horarioturma \n" +
                "WHERE cli.codigo = " + codigoCliente + " \n" +
                "AND cct.horarioturma = " + horarioTurmaVO.getCodigo() + " \n" +
                "AND DATE_TRUNC('minute', cct.diaaula::date + ht.horainicial::time) = '" +  Calendario.getDataAplicandoFormatacao(dataHoraAula, "yyyy-MM-dd HH:mm") + "'\n" +
                "AND cct.quantidade < 0 \n" +
                "AND cct.tipooperacaocreditotreino = " + TipoOperacaoCreditoTreinoEnum.MARCOU_AULA.getCodigo() + " \n";
    }
}

