package negocio.facade.jdbc.contrato;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ConvenioDescontoConfiguracaoVO;
import negocio.comuns.contrato.ConvenioDescontoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.interfaces.contrato.ConvenioDescontoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Hashtable;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ConvenioDescontoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ConvenioDescontoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ConvenioDescontoVO
 * @see SuperEntidade
 */
public class ConvenioDesconto extends SuperEntidade implements ConvenioDescontoInterfaceFacade {
    private Hashtable convenioDescontoConfiguracaos;

    public ConvenioDesconto() throws Exception {
        super();
        setIdEntidade("ConvenioDesconto");
        setConvenioDescontoConfiguracaos(new Hashtable());
    }

    public ConvenioDesconto(Connection con) throws Exception {
        super(con);
        setIdEntidade("ConvenioDesconto");
        setConvenioDescontoConfiguracaos(new Hashtable());
    }

	/**
     * Operação responsável por retornar um novo objeto da classe <code>ConvenioDescontoVO</code>.
     */
    public ConvenioDescontoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ConvenioDescontoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ConvenioDescontoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ConvenioDescontoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ConvenioDescontoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ConvenioDescontoVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO ConvenioDesconto( descricao, dataAssinatura, dataInicioVigencia, dataFinalVigencia, responsavelAutorizacao, dataAutorizacao, isentarMatricula, isentarRematricula, empresa ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? , ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setDate(2, Uteis.getDataJDBC(obj.getDataAssinatura()));
                sqlInserir.setDate(3, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
                sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
                sqlInserir.setInt(5, obj.getResponsavelAutorizacao().getCodigo());
                sqlInserir.setDate(6, Uteis.getDataJDBC(obj.getDataAutorizacao()));
                sqlInserir.setBoolean(7, obj.getIsentarMatricula());
                sqlInserir.setBoolean(8, obj.getIsentarRematricula());
                sqlInserir.setInt(9, obj.getEmpresa().getCodigo());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            getFacade().getConvenioDescontoConfiguracao().incluirConvenioDescontoConfiguracaos(obj.getCodigo(), obj.getConvenioDescontoConfiguracaoVOs());
            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
/**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ConvenioDescontoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ConvenioDescontoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(ConvenioDescontoVO obj) throws Exception {
        try {
            ConvenioDescontoVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO ConvenioDesconto( descricao, dataAssinatura, dataInicioVigencia, dataFinalVigencia, responsavelAutorizacao, dataAutorizacao, isentarMatricula, isentarRematricula, empresa ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setDate(2, Uteis.getDataJDBC(obj.getDataAssinatura()));
                sqlInserir.setDate(3, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
                sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
                sqlInserir.setInt(5, obj.getResponsavelAutorizacao().getCodigo());
                sqlInserir.setDate(6, Uteis.getDataJDBC(obj.getDataAutorizacao()));
                sqlInserir.setBoolean(7, obj.getIsentarMatricula());
                sqlInserir.setBoolean(8, obj.getIsentarRematricula());
                sqlInserir.setInt(9, obj.getEmpresa().getCodigo());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            getFacade().getConvenioDescontoConfiguracao().incluirConvenioDescontoConfiguracaos(obj.getCodigo(), obj.getConvenioDescontoConfiguracaoVOs());
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        } 
    }
    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ConvenioDescontoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ConvenioDescontoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ConvenioDescontoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(ConvenioDescontoVO obj) throws Exception {
            ConvenioDescontoVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE ConvenioDesconto set descricao=?, dataAssinatura=?, dataInicioVigencia=?, dataFinalVigencia=?, responsavelAutorizacao=?, dataAutorizacao=?, isentarMatricula=?, isentarRematricula=?, empresa=? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setDate(2, Uteis.getDataJDBC(obj.getDataAssinatura()));
            sqlAlterar.setDate(3, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
            sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
            sqlAlterar.setInt(5, obj.getResponsavelAutorizacao().getCodigo());
            sqlAlterar.setDate(6, Uteis.getDataJDBC(obj.getDataAutorizacao()));
            sqlAlterar.setBoolean(7, obj.getIsentarMatricula());
            sqlAlterar.setBoolean(8, obj.getIsentarRematricula());
            sqlAlterar.setInt(9, obj.getEmpresa().getCodigo());
            sqlAlterar.setInt(10, obj.getCodigo());
            sqlAlterar.execute();
        }
        getFacade().getConvenioDescontoConfiguracao().alterarConvenioDescontoConfiguracaos(obj.getCodigo(), obj.getConvenioDescontoConfiguracaoVOs());
        }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ConvenioDescontoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ConvenioDescontoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ConvenioDescontoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM ConvenioDesconto WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            getFacade().getConvenioDescontoConfiguracao().excluirConvenioDescontoConfiguracaos(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDesconto</code> através do valor do atributo 
     * <code>Date dataAutorizacao</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataAutorizacao(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDesconto WHERE ((dataAutorizacao >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataAutorizacao <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataAutorizacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDesconto</code> através do valor do atributo 
     * <code>Integer responsavelAutorizacao</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorResponsavelAutorizacao(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDesconto WHERE responsavelAutorizacao >= " + valorConsulta.intValue() + " ORDER BY responsavelAutorizacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDesconto</code> através do valor do atributo 
     * <code>Double descontoParcela</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescontoParcela(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDesconto WHERE descontoParcela >= " + valorConsulta.doubleValue() + " ORDER BY descontoParcela";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDesconto</code> através do valor do atributo 
     * <code>Date dataFinalVigencia</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataFinalVigencia(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDesconto WHERE ((dataFinalVigencia >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataFinalVigencia <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataFinalVigencia";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDesconto</code> através do valor do atributo 
     * <code>Date dataInicioVigencia</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataInicioVigencia(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDesconto WHERE ((dataInicioVigencia >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataInicioVigencia <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataInicioVigencia";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorConvenioVigente(Date data, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDesconto " +
                "WHERE ((dataInicioVigencia <= '" + Uteis.getDataJDBC(data) + "') and (dataFinalVigencia >= '" + Uteis.getDataJDBC(data) + "')) " +
                " AND empresa = " + empresa +
                "  ORDER BY dataInicioVigencia";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDesconto</code> através do valor do atributo 
     * <code>Date dataAssinatura</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataAssinatura(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDesconto WHERE ((dataAssinatura >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataAssinatura <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataAssinatura";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDesconto</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDesconto WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConvenioDesconto</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConvenioDesconto WHERE codigo >= " + valorConsulta + " AND empresa = " + empresa + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ConvenioDescontoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ConvenioDescontoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ConvenioDescontoVO</code>.
     * @return  O objeto da classe <code>ConvenioDescontoVO</code> com os dados devidamente montados.
     */
    public static ConvenioDescontoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ConvenioDescontoVO obj = new ConvenioDescontoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setDataAssinatura(dadosSQL.getDate("dataAssinatura"));
        obj.setDataInicioVigencia(dadosSQL.getDate("dataInicioVigencia"));
        obj.setDataFinalVigencia(dadosSQL.getDate("dataFinalVigencia"));
        obj.getResponsavelAutorizacao().setCodigo(new Integer(dadosSQL.getInt("responsavelAutorizacao")));
        obj.setDataAutorizacao(dadosSQL.getDate("dataAutorizacao"));
        obj.setIsentarMatricula(dadosSQL.getBoolean("isentarMatricula"));
        obj.setIsentarRematricula(dadosSQL.getBoolean("isentarRematricula"));
        try {
            obj.setEmpresa(getCachedEmpresa(dadosSQL.getInt("empresa"), con));
        } catch (Exception ignored) {

        }
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        ConvenioDescontoConfiguracao convenioDescontoConfiguracao = new ConvenioDescontoConfiguracao(con);
        obj.setConvenioDescontoConfiguracaoVOs(convenioDescontoConfiguracao.consultarConvenioDescontoConfiguracaos(obj.getCodigo(), nivelMontarDados));
        convenioDescontoConfiguracao = null;

        ConvenioDescontoPlanoConfiguracao convenioDescontoPlanoConfiguracao = new ConvenioDescontoPlanoConfiguracao(con);
        obj.setConvenioDescontoPlanoConfiguracaoVOs(convenioDescontoPlanoConfiguracao.consultarConvenioDescontoPlanoConfiguraces(obj.getCodigo(), nivelMontarDados));
        convenioDescontoPlanoConfiguracao = null;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            return obj;
        }
        montarDadosResponsavelAutorizacao(obj, nivelMontarDados, con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ColaboradorVO</code> relacionado ao objeto <code>PlanoTextoPadraoVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelAutorizacao(ConvenioDescontoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelAutorizacao().getCodigo().intValue() == 0) {
            obj.setResponsavelAutorizacao(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setResponsavelAutorizacao(usuario.consultarPorChavePrimaria(obj.getResponsavelAutorizacao().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    /**
     * Operação responsável por adicionar um objeto da <code>ConvenioDescontoConfiguracaoVO</code> no Hashtable <code>ConvenioDescontoConfiguracaos</code>.
     * Neste Hashtable são mantidos todos os objetos de ConvenioDescontoConfiguracao de uma determinada ConvenioDesconto.
     * @param obj  Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjConvenioDescontoConfiguracaos(ConvenioDescontoConfiguracaoVO obj) throws Exception {
        getConvenioDescontoConfiguracaos().put(obj.getDuracao() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>ConvenioDescontoConfiguracaoVO</code> do Hashtable <code>ConvenioDescontoConfiguracaos</code>.
     * Neste Hashtable são mantidos todos os objetos de ConvenioDescontoConfiguracao de uma determinada ConvenioDesconto.
     * @param Duracao Atributo da classe <code>ConvenioDescontoConfiguracaoVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjConvenioDescontoConfiguracaos(Integer Duracao) throws Exception {
        getConvenioDescontoConfiguracaos().remove(Duracao + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ConvenioDescontoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ConvenioDescontoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ConvenioDesconto WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ConvenioDesconto ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    

    public Hashtable getConvenioDescontoConfiguracaos() {
        return (convenioDescontoConfiguracaos);
    }

    public void setConvenioDescontoConfiguracaos(Hashtable convenioDescontoConfiguracaos) {
        this.convenioDescontoConfiguracaos = convenioDescontoConfiguracaos;
    }

    public String consultarJSON(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao").trim())).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("responsavelautorizacao").trim())).append("\",");
                json.append("\"").append(rs.getDate("datainiciovigencia")).append("\",");
                json.append("\"").append(rs.getDate("datafinalvigencia")).append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        String condicaoEmpresa = "";
        if (empresa > 0) {
             condicaoEmpresa = "AND cd.empresa = " + empresa;
        }

        String sql = "SELECT\n"
                + "  cd.codigo, descricao, u.nome as responsavelautorizacao, datainiciovigencia, datafinalvigencia\n"
                + "FROM conveniodesconto cd \n"
                + "  LEFT JOIN usuario u ON cd.responsavelautorizacao = u.codigo"
                + "  WHERE 1 = 1 " + condicaoEmpresa
                + "  ORDER BY descricao";
        return con.prepareStatement(sql);
    }
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                ConvenioDescontoVO convenioDesc = new ConvenioDescontoVO();
                String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("responsavelautorizacao") + rs.getString("datainiciovigencia") + rs.getString("datafinalvigencia");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    convenioDesc.setCodigo(rs.getInt("codigo"));
                    convenioDesc.setDescricao(rs.getString("descricao"));
                    convenioDesc.getResponsavelAutorizacao().setNome(rs.getString("responsavelautorizacao"));
                    convenioDesc.setDataInicioVigencia(rs.getDate("datainiciovigencia"));
                    convenioDesc.setDataFinalVigencia(rs.getDate("datafinalvigencia"));
                    lista.add(convenioDesc);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Responsável")) {
            Ordenacao.ordenarLista(lista, "responsavel_Apresentar");
        } else if (campoOrdenacao.equals("Início")) {
            Ordenacao.ordenarLista(lista, "dataInicioVigencia");
        } else if (campoOrdenacao.equals("Fim")) {
            Ordenacao.ordenarLista(lista, "dataFinalVigencia");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}
