package negocio.facade.jdbc.contrato;

import java.sql.*;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.interfaces.contrato.*;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.facade.jdbc.arquitetura.*;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.*;
import servicos.integracao.enumerador.IntegracoesEnum;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>PeriodoAcessoClienteVO</code>. Responsável por implementar operações
 * como incluir, alterar, excluir e consultar pertinentes a classe
 * <code>PeriodoAcessoClienteVO</code>. Encapsula toda a interação com o banco
 * de dados.
 *
 * @see PeriodoAcessoClienteVO
 * @see SuperEntidade
 */
public class PeriodoAcessoCliente extends SuperEntidade implements PeriodoAcessoClienteInterfaceFacade {

    public PeriodoAcessoCliente() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public PeriodoAcessoCliente(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    public PeriodoAcessoClienteVO novo() throws Exception {
        incluir(getIdEntidade());
        PeriodoAcessoClienteVO obj = new PeriodoAcessoClienteVO();
        return obj;
    }

    public void incluir(PeriodoAcessoClienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(PeriodoAcessoClienteVO obj) throws Exception {
        PeriodoAcessoClienteVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        int i = 1;
        String sql = "INSERT INTO PeriodoAcessoCliente(pessoa, contrato, dataInicioAcesso, dataFinalAcesso, tipoAcesso, " +
                "contratoBaseadoRenovacao, aulaAvulsaDiaria, responsavel, dataLancamento, " +
                "tokenGymPass, reposicao, tipogympass, valorgympass, tipototalpass, tokengogood, produto) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getPessoa() != 0) {
                sqlInserir.setInt(i++, obj.getPessoa());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getContrato() != 0) {
                sqlInserir.setInt(i++, obj.getContrato());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataInicioAcesso()));
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataFinalAcesso()));
            sqlInserir.setString(i++, obj.getTipoAcesso());
            if (obj.getContratoBaseadoRenovacao() == 0) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getContratoBaseadoRenovacao());
            }
            if (obj.getAulaAvulsaDiaria() != 0) {
                sqlInserir.setInt(i++, obj.getAulaAvulsaDiaria());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (!UteisValidacao.emptyNumber(obj.getResponsavel())) {
                sqlInserir.setInt(i++, obj.getResponsavel());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setDate(i++, Uteis.getDataJDBC(Calendario.hoje()));
            sqlInserir.setString(i++, obj.getTokenGymPass());
            if (UteisValidacao.emptyNumber(obj.getReposicao())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getReposicao());
            }
            sqlInserir.setString(i++, obj.getTipoGymPass());
            resolveDoubleNull(sqlInserir, i++, obj.getValorGympass());

            sqlInserir.setBoolean(i++, obj.getTipototalpass());
            sqlInserir.setString(i++, obj.getTokenGoGood());
            resolveIntegerNull(sqlInserir, i++, obj.getProduto());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
        if(!UteisValidacao.emptyString(obj.getTokenGymPass())){
            try {
                ZillyonWebFacade zillyonDAO = new ZillyonWebFacade(con);
                if(zillyonDAO.getEmpresa().integracaoMyWellnesHabilitada(obj.getCodigoEmpresa(), false)) {
                    zillyonDAO.startThreadMyWellness(null, null, false, obj.getPessoa());
                }
                zillyonDAO = null;
            }catch (Exception ignored){
            }
        }
    }

    public void excluirSemCommit(final String condicao) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM PeriodoAcessoCliente WHERE " + condicao;
            try (Statement sqlExcluir = con.createStatement()) {
                sqlExcluir.execute(sql);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterar(PeriodoAcessoClienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(PeriodoAcessoClienteVO obj) throws Exception {
        PeriodoAcessoClienteVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE PeriodoAcessoCliente set pessoa=?, contrato=?, dataInicioAcesso=?, dataFinalAcesso=?, tipoAcesso=?, contratobaseadorenovacao=?, aulaAvulsaDiaria=?, responsavel=?, tokenGymPass = ?, tipoGymPass = ?, tipototalpass = ? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getPessoa() != 0) {
                sqlAlterar.setInt(1, obj.getPessoa());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getContrato() != 0) {
                sqlAlterar.setInt(2, obj.getContrato());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setDate(3, Uteis.getDataJDBC(obj.getDataInicioAcesso()));
            sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataFinalAcesso()));
            sqlAlterar.setString(5, obj.getTipoAcesso());
            if (obj.getContratoBaseadoRenovacao() == 0) {
                sqlAlterar.setNull(6, 0);
            } else {
                sqlAlterar.setInt(6, obj.getContratoBaseadoRenovacao());
            }
            if (obj.getAulaAvulsaDiaria() != 0) {
                sqlAlterar.setInt(7, obj.getAulaAvulsaDiaria());
            } else {
                sqlAlterar.setNull(7, 0);
            }
            if (!UteisValidacao.emptyNumber(obj.getResponsavel())) {
                sqlAlterar.setInt(8, obj.getResponsavel());
            } else {
                sqlAlterar.setNull(8, 0);
            }
            sqlAlterar.setString(9, obj.getTokenGymPass());
            sqlAlterar.setString(10, obj.getTipoGymPass());
            sqlAlterar.setBoolean(11, obj.getTipototalpass());
            sqlAlterar.setInt(12, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void excluir(PeriodoAcessoClienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(PeriodoAcessoClienteVO obj) throws Exception {
        try {
//            excluir(getIdEntidade());
            String sql = "DELETE FROM PeriodoAcessoCliente WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public void excluirPeriodoAcessoClienteContrato(Integer codigoContrato) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM PeriodoAcessoCliente WHERE contrato = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, codigoContrato);
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluirPeriodoAcessoClienteAulaAvulsaDiaria(Integer codigoAulaAvulsaDiaria) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM PeriodoAcessoCliente WHERE aulaavulsadiaria = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, codigoAulaAvulsaDiaria);
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public List consultarPorDataInicioContrato(Date data, Integer contrato, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE (dataInicioAcesso > '" + Uteis.getDataJDBC(data) + "') and contrato= " + contrato + " ORDER BY dataInicioAcesso";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public int contarQtdAcessosPorClienteComGympassAPartirData(int cliente, Date dataInicial) throws Exception {
        int i = 1;
        String sql = "SELECT COUNT(pac.codigo) AS qtd FROM periodoacessocliente pac " +
                     "INNER JOIN cliente ON cliente.pessoa = pac.pessoa " +
                     "WHERE tokengympass != '' AND datainicioacesso >= ? AND cliente.codigo = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setDate(i++, Uteis.getDataJDBC(dataInicial));
            stm.setInt(i++, cliente);
            try (ResultSet rs = stm.executeQuery()) {
                return rs.next() ? rs.getInt("qtd") : 0;
            }
        }
    }

    public int contarQtdAcessosDoClienteComGympassNoMes(int cliente, int mes) throws Exception {
        int i = 1;
        String sql = "SELECT COUNT(pac.codigo) AS qtd FROM periodoacessocliente pac " +
                     "INNER JOIN cliente ON cliente.pessoa = pac.pessoa " +
                     "WHERE tokengympass != '' AND Extract(month from datainicioacesso) = ? AND cliente.codigo = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(i++, mes);
            stm.setInt(i++, cliente);
            try (ResultSet rs = stm.executeQuery()) {
                return rs.next() ? rs.getInt("qtd") : 0;
            }
        }
    }

    public List consultarPorTipoAcesso(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE upper( tipoAcesso ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoAcesso";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public PeriodoAcessoClienteVO consultarPorContratoTipoAcesso(Integer codigoContrato, String tipoAcesso, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlSelect = new StringBuilder();
        sqlSelect.append("SELECT *\n");
        sqlSelect.append("FROM periodoacessocliente\n");
        sqlSelect.append("WHERE contrato = ?\n");
        sqlSelect.append("AND tipoacesso = ?\n");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlSelect.toString())) {
            sqlConsultar.setInt(1, codigoContrato);
            sqlConsultar.setString(2, tipoAcesso);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( PeriodoAcessoCliente ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorContrato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE contrato = " + valorConsulta + " ORDER BY dataInicioAcesso";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorPessoa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE pessoa = " + valorConsulta + " ORDER BY dataInicioAcesso DESC, codigo ASC";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public PeriodoAcessoClienteVO consultarPorDataEspecificaECodigoContrato(Date data, Integer contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE dataInicioAcesso <= '" + Uteis.getDataJDBC(data) + "' and dataFinalAcesso >= '" + Uteis.getDataJDBC(data) + "' and contrato =  " + contrato + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public PeriodoAcessoClienteVO consultarPorDataEspecificaCodigoContratoETipoAcesso(Date data, Integer contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE dataInicioAcesso <= '" + Uteis.getDataJDBC(data) + "' and dataFinalAcesso >= '" + Uteis.getDataJDBC(data) + "' and contrato =  " + contrato + "and tipoacesso = 'TR'  ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public PeriodoAcessoClienteVO obterUltimoDiaPeriodoAcessoContratoTipo(Integer contrato, String tipoAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT PeriodoAcessoCliente.* FROM PeriodoAcessoCliente WHERE dataFinalAcesso IN (SELECT MAX(dataFinalAcesso) FROM PeriodoAcessoCliente WHERE contrato = " + contrato + " and tipoAcesso ='" + tipoAcesso.toUpperCase() + "' ) and contrato = " + contrato;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }
    public Boolean consultarPorDataEspecificaECodigoPessoaETipoAcesso(Date data, Integer pessoa, String tipoAcesso, boolean controlarAcesso, int nivelMontarDados, boolean gympass) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE " + (gympass ? "" : "dataInicioAcesso <= '" + Uteis.getDataJDBC(data) + "' and dataFinalAcesso >= '" + Uteis.getDataJDBC(data) + "' and ") +
                "pessoa =  " + pessoa + " and tipoAcesso ='" + tipoAcesso.toUpperCase() + "'" + (gympass ? " and tokengympass is not null and tokengympass <> ''" : "") + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return tabelaResultado.next();
            }
        }
    }

    public PeriodoAcessoClienteVO consultarPorDataPessoaTipoAcesso(Date data, Integer pessoa, String tipoAcesso, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE dataInicioAcesso <= '" + Uteis.getDataJDBC(data) + "' and dataFinalAcesso >= '" + Uteis.getDataJDBC(data) + "' and pessoa =  " + pessoa + " and tipoAcesso ='" + tipoAcesso.toUpperCase() + "' ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return montarDados(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public List consultarPorPessoaDataAtual(Integer pessoa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente "
                + "where (current_date >= datainicioacesso) and (current_date <= datafinalacesso)"
                + " and pessoa = " + pessoa + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public Boolean existePeriodoAcessoHojeTotalPassPorPessoa(Integer pessoa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT EXISTS ( \n");
        sql.append("SELECT * FROM PeriodoAcessoCliente \n");
        sql.append("WHERE (current_date >= datainicioacesso::date)  AND (current_date <= datafinalacesso::date) \n");
        sql.append("AND pessoa = ").append(pessoa).append(" \n");
        sql.append("AND tipototalpass)");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return rs.getBoolean("exists");
            }
        }
        return false;
    }

    public PeriodoAcessoClienteVO obterUltimoDiaPeriodoAcessoContrato(Integer contrato, int nivelMontarDados) throws Exception {
        inicializar();
        String sqlStr = "SELECT PeriodoAcessoCliente.* FROM PeriodoAcessoCliente "
                + "WHERE dataFinalAcesso IN (SELECT MAX(dataFinalAcesso) "
                + "FROM PeriodoAcessoCliente WHERE contrato = " + contrato + ") and contrato = " + contrato;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public PeriodoAcessoClienteVO obterUltimoDiaPeriodoAcessoPessoa(Integer pessoa, int nivelMontarDados, boolean somenteVigente, boolean gympass) throws Exception {
        inicializar();
        String sqlStr = "SELECT PeriodoAcessoCliente.* FROM PeriodoAcessoCliente WHERE dataFinalAcesso IN (SELECT MAX(dataFinalAcesso) FROM PeriodoAcessoCliente WHERE pessoa = " + pessoa + ") and pessoa = " + pessoa;
        if (gympass) {
            sqlStr = sqlStr.replace(") and", " and tokengympass <> '' and tipoacesso = 'PL') and");
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                if (somenteVigente) {
                    PeriodoAcessoClienteVO obj = montarDados(tabelaResultado, nivelMontarDados);

                    if (!Calendario.entre(Calendario.hoje(), obj.getDataInicioAcesso(), obj.getDataFinalAcesso())) {
                        return null;
                    }
                    return obj;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public void incluirPeriodoAcessoClienteContrato(ContratoVO contrato, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PeriodoAcessoClienteVO obj = (PeriodoAcessoClienteVO) e.next();
            if (!obj.getTipoAcesso().equals("CA")) {
                alterarSemCommit(obj);
            } else {
                obj.setContrato(contrato.getCodigo());
                obj.setContratoBaseadoRenovacao(contrato.getContratoBaseadoRenovacao());
                incluirSemCommit(obj);
            }
        }
    }

    public static List<PeriodoAcessoClienteVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<PeriodoAcessoClienteVO> vetResultado = new ArrayList<PeriodoAcessoClienteVO>();
        while (tabelaResultado.next()) {
            PeriodoAcessoClienteVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static PeriodoAcessoClienteVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        PeriodoAcessoClienteVO obj = new PeriodoAcessoClienteVO();
        obj.setNovoObj(new Boolean(false));
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setPessoa(new Integer(dadosSQL.getInt("pessoa")));
        obj.setContrato(new Integer(dadosSQL.getInt("contrato")));
        obj.setAulaAvulsaDiaria(new Integer(dadosSQL.getInt("aulaAvulsaDiaria")));
        obj.setDataInicioAcesso(dadosSQL.getDate("dataInicioAcesso"));
        obj.setDataFinalAcesso(dadosSQL.getDate("dataFinalAcesso"));
        obj.setTipoAcesso(dadosSQL.getString("tipoAcesso"));
        obj.setContratoBaseadoRenovacao(dadosSQL.getInt("contratobaseadorenovacao"));
        obj.setResponsavel(dadosSQL.getInt("responsavel"));
        obj.setDataLancamento(dadosSQL.getDate("dataLancamento"));
        obj.setTokenGymPass(dadosSQL.getString("tokenGymPass"));
        obj.setTipoGymPass(dadosSQL.getString("tipoGymPass"));
        obj.setValorGympass(dadosSQL.getDouble("valorgympass"));
        obj.setTipototalpass(dadosSQL.getBoolean("tipototalpass"));
        try{
            obj.setProdutoGymPass(dadosSQL.getString("produtoGymPass"));
        }catch (Exception ignore){}
        try{
            obj.setTokenGoGood(dadosSQL.getString("tokengogood"));
        }catch (Exception ignore){}
        return obj;
    }

    public static PeriodoAcessoClienteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        PeriodoAcessoClienteVO obj = montarDadosBasico(dadosSQL);
        //if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA)
        return obj;
    }

    public PeriodoAcessoClienteVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PeriodoAcessoCliente WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( PeriodoAcessoCliente ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<PeriodoAcessoClienteVO> consultar(String sql, int nivelMontarDados) throws Exception {
        List<PeriodoAcessoClienteVO> lista = new ArrayList<PeriodoAcessoClienteVO>();
        try (ResultSet resultDados = criarConsulta(sql, con)) {
            return montarDadosConsulta(resultDados, nivelMontarDados);
        }
    }

    public Date consultarFinalPeriodoVigente(Integer codigoPessoa) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT datafinalacesso FROM periodoacessocliente WHERE datafinalacesso > '"
                + Uteis.getDataJDBC(Calendario.hoje()) + "' AND pessoa = " + codigoPessoa, con)) {
            if (rs.next()) {
                return rs.getDate("datafinalacesso");
            }
        }
        return null;
    }

    public Boolean possuiPeriodoAcesso(int pessoa,String tipo,Date dateInicio,Date dataFim,int nivelMontarDados) throws Exception {
        StringBuilder builder = new StringBuilder();
            builder.append(" SELECT * FROM PeriodoACessoCliente pac\n");
            builder.append(" WHERE true ");

        if(!UteisValidacao.emptyString(tipo)){
            builder.append(" AND tipoAcesso = '").append(tipo).append("'\n");
        }
        if(!UteisValidacao.emptyNumber(pessoa)){
            builder.append(" AND pessoa = ").append(pessoa).append("\n");
        }
        if(dateInicio!=null && dataFim!=null){
            builder.append(" AND (('").append(Uteis.getDataJDBC(dateInicio)).append("' between dataInicioAcesso AND  dataFinalAcesso ").append(")\n");
            builder.append(" OR ('").append(Uteis.getDataJDBC(dataFim)).append("' between dataInicioAcesso AND  dataFinalAcesso ").append(")\n");
            builder.append(" OR ('").append(Uteis.getDataJDBC(dataFim)).append("' <  dataFinalAcesso  AND '");
            builder.append(Uteis.getDataJDBC(dateInicio)).append("' <  dataInicioAcesso ").append("))\n");
        }else if(dateInicio !=null){
            builder.append(" AND ('").append(Uteis.getDataJDBC(dateInicio)).append("' between dataInicioAcesso AND  dataFinalAcesso ").append(")\n");
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(builder.toString())) {
                while (rs.next()) {
                    return true;
                }
            }
        }
        return false;
    }
    
    public boolean existePermissaoAcessoPeriodo(Integer pessoa,Date datainicio, Date datafinal) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select exists (select codigo from periodoacessocliente  where pessoa = " + pessoa + " AND  tipoacesso in (" + PeriodoAcessoClienteVO.getTiposQuePermiteAcesso() + ") AND (('" + Uteis.getDataJDBC(datainicio) + "' between datainicioacesso AND  datafinalacesso) OR ('" + Uteis.getDataJDBC(datafinal) + "' between datainicioacesso and  datafinalacesso)) ) as existe", con)) {
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
        }
        return false;
    }
    
     public PeriodoAcessoClienteVO consultarUltimoPorDataPessoaTipoAcesso(Date data, Integer pessoa, String tipoAcesso, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE dataFinalAcesso >= '" + Uteis.getDataJDBC(data) + "' and pessoa =  " + pessoa + " and tipoAcesso ='" + tipoAcesso.toUpperCase() + "' ORDER BY dataFinalAcesso desc";
         try (Statement stm = con.createStatement()) {
             try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                 if (!tabelaResultado.next()) {
                     return null;
                 }
                 return montarDados(tabelaResultado, nivelMontarDados);
             }
         }
     }

    public PeriodoAcessoClienteVO consultarExisteTokenGymPass(String tokenGymPass, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("p.* \n");
        sql.append("from periodoacessocliente p \n");
        sql.append("inner join cliente c on c.pessoa = p.pessoa \n");
        sql.append("where 1 = 1 \n");
        sql.append("and c.empresa = ").append(empresa).append(" \n");
        sql.append("and p.tokengympass ilike ('").append(tokenGymPass).append("')");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (!rs.next()) {
                return null;
            }
            return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
    }

    public List<PeriodoAcessoClienteVO> consultarPorPessoaGymPass(Integer pessoa) throws Exception {
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente pc" +
                " left join infocheckin ic on ic.periodoacesso = pc.codigo" +
                " WHERE pc.tokengympass is not null and pc.tokengympass <> '' and pc.pessoa = " + pessoa + " order by pc.codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        }
    }

    public List<PeriodoAcessoClienteVO> consultarPorPessoaGoGood(Integer pessoa) throws Exception {
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente pc" +
                " left join infocheckingogood ic on ic.periodoacesso = pc.codigo" +
                " WHERE pc.tokengogood is not null and pc.tokengogood <> '' and pc.pessoa = " + pessoa + " order by pc.codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        }
    }

    @Override
    public PeriodoAcessoClienteVO consultarPorDataPessoa(Date data, Integer pessoa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE dataInicioAcesso <= '" + Uteis.getDataJDBC(data) + "' and dataFinalAcesso >= '" + Uteis.getDataJDBC(data) + "' and pessoa =  " + pessoa + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return montarDados(tabelaResultado, nivelMontarDados);
            }
        }
    }

    @Override
    public boolean existeCheckinGympassHojePorToken(String tokenGymPass) throws Exception {
        try {
            Date dataHoje = Calendario.hoje();
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT EXISTS ( \n");
            sql.append(" SELECT p.codigo FROM periodoacessocliente p \n");
            sql.append(" WHERE COALESCE(p.tokengympass,'') != '' \n");
            sql.append(" AND p.tokengympass = ? \n");
            sql.append(" AND p.datainicioacesso::DATE = ?::DATE \n");
            sql.append(" AND p.datafinalacesso::DATE = ?::DATE \n");
            sql.append(") as existe");

            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                ps.setString(1, tokenGymPass);
                ps.setDate(2, Uteis.getDataJDBC(dataHoje));
                ps.setDate(3, Uteis.getDataJDBC(dataHoje));
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        return rs.getBoolean("existe");
                    }
                }
            }
        } catch (Exception e) {
            // Em caso de erro, retorna false para não afetar o checkin do aluno
            e.printStackTrace();
            Uteis.logarDebug("Erro ao verificar se existe checkin para o gympass: " + e.getMessage());
        }
        return false;
    }

    @Override
    public boolean existeTokenGymPass(String tokenGymPass) throws Exception {
        String sql = "select exists (select \n" +
                "codigo\n" +
                "from cliente c \n" +
                "where c.gympassuniquetoken ilike ('" + tokenGymPass + "')\n" +
                "limit 1) as existe";
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
        }
        return false;
    }

    @Override
    public boolean existeTokenGoGood(String tokenGoGood, String tokenAcademy) throws Exception {
        tokenAcademy = tokenAcademy.replaceAll("\\s+", "");
        tokenGoGood = tokenGoGood.replaceAll("\\s+", "");
        String tokenSemHash = tokenGoGood.replace("#", "");
        String sql = "select exists (select \n" +
                "c.codigo \n" +
                "from cliente c \n" +
                "inner join empresa emp on emp.codigo = c.empresa \n" +
                "where ((upper(c.gogoodtoken) = '" + tokenGoGood.toUpperCase() + "') or (upper(c.gogoodtoken) = '"+tokenSemHash.toUpperCase()+"')) \n" +
                "and upper(emp.tokenacademygogood) = '" + tokenAcademy.toUpperCase() + "' \n" +
                "limit 1) as existe";
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
        }
        return false;
    }

    public List consultarPorVigenteOuFuturoContrato(Date data, Integer contrato, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM PeriodoAcessoCliente WHERE contrato =  " + contrato + " and  ((dataInicioAcesso <= '" + Uteis.getDataJDBC(data) + "' and dataFinalAcesso >= '" + Uteis.getDataJDBC(data) + "') or dataInicioAcesso > '" + Uteis.getDataJDBC(data) + "') ORDER BY dataInicioAcesso";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    @Override
    public boolean existePeriodoAcessoGympassVigente(Integer codPessoa) throws Exception {
        String data = Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT EXISTS ( \n");
        sql.append(" SELECT p.codigo FROM periodoacessocliente p \n");
        sql.append(" WHERE p.pessoa = " + codPessoa + " \n");
        sql.append(" AND p.datainicioacesso::DATE <= '" + data + "' \n");
        sql.append(" AND p.datafinalacesso::DATE >= '" + data + "' \n");
        sql.append(" AND COALESCE(p.tokengympass,'') != '' \n");
        sql.append(") as existe");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
        }
        return false;
    }

    public boolean existePeriodoAcessoGympassVigentePorCliente(Integer codCliente) throws Exception {
        String data = Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT EXISTS ( \n");
        sql.append(" SELECT p.codigo FROM periodoacessocliente p \n");
        sql.append(" inner join cliente c on c.pessoa = p.pessoa  \n");
        sql.append(" WHERE c.codigo = " + codCliente + " \n");
        sql.append(" AND p.datainicioacesso::DATE <= '" + data + "' \n");
        sql.append(" AND p.datafinalacesso::DATE >= '" + data + "' \n");
        sql.append(" AND COALESCE(p.tokengympass,'') != '' \n");
        sql.append(") as existe");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
        }
        return false;
    }

    @Override
    public boolean existePeriodoAcessoTotalpassVigente(Integer codPessoa) throws Exception {
        String data = Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT EXISTS ( \n");
        sql.append(" SELECT p.codigo FROM periodoacessocliente p \n");
        sql.append(" WHERE p.pessoa = " + codPessoa + " \n");
        sql.append(" AND p.datainicioacesso::DATE <= '" + data + "' \n");
        sql.append(" AND p.datafinalacesso::DATE >= '" + data + "' \n");
        sql.append(" AND tipototalpass = true  \n");
        sql.append(") as existe");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
        }
        return false;
    }

    @Override
    public boolean existiuPeriodoAcessoTotalpass(Integer codPessoa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT EXISTS ( \n");
        sql.append(" SELECT p.codigo FROM periodoacessocliente p \n");
        sql.append(" WHERE p.pessoa = " + codPessoa + " \n");
        sql.append(" AND tipototalpass = true limit 1 \n");
        sql.append(") as existe");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
        }
        return false;
    }

    public void gravarInfoCheckin(ClienteVO cliente,
                                  EmpresaVO empresaVO,
                                  String token,
                                  PeriodoAcessoClienteVO periodoAcessoClienteVO, IntegracoesEnum integracao, String produtoGymPass) throws Exception {
        String sql = "INSERT INTO public.infocheckin (cliente, empresa, token, integracao, cancelado, periodoacesso, produtoGymPass) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?);";
        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            preparedStatement.setInt(1, cliente.getCodigo());
            preparedStatement.setInt(2, empresaVO.getCodigo());
            preparedStatement.setString(3, token);
            preparedStatement.setInt(4, integracao.getCodigo());
            preparedStatement.setBoolean(5, false);
            preparedStatement.setInt(6, periodoAcessoClienteVO.getCodigo());
            if (UteisValidacao.emptyString(produtoGymPass)) {
                preparedStatement.setNull(7, Types.VARCHAR);
            } else {
                preparedStatement.setString(7, produtoGymPass);
            }
            preparedStatement.executeUpdate();
        }
    }

    public void gravarInfoCheckinGoGood(ClienteVO cliente,
                                  EmpresaVO empresaVO,
                                  String token,
                                  PeriodoAcessoClienteVO periodoAcessoClienteVO, IntegracoesEnum integracao, String retorno) throws Exception {
        String sql = "INSERT INTO public.infocheckingogood (cliente, empresa, token, integracao, cancelado, periodoacesso, retorno) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?);";
        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            preparedStatement.setInt(1, cliente.getCodigo());
            preparedStatement.setInt(2, empresaVO.getCodigo());
            preparedStatement.setString(3, token);
            preparedStatement.setInt(4, integracao.getCodigo());
            preparedStatement.setBoolean(5, false);
            preparedStatement.setInt(6, periodoAcessoClienteVO.getCodigo());
            if (UteisValidacao.emptyString(retorno)) {
                preparedStatement.setNull(7, Types.VARCHAR);
            } else {
                preparedStatement.setString(7, retorno);
            }
            preparedStatement.executeUpdate();
        }
    }

    public String obterProdutoGymPassInfoChekin(Integer periodoAcesso, Integer empresa, Integer codigoCliente, boolean somenteCodigo) throws Exception {
        String sqlStr = "SELECT produtoGymPass from infocheckin where periodoacesso = " + periodoAcesso + " and empresa = " + empresa + " and cliente = " + codigoCliente;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                if (rs.next()) {
                    if (somenteCodigo) {
                        try {
                            return (!UteisValidacao.emptyString(rs.getString("produtoGymPass")) ?
                                    rs.getString("produtoGymPass").split(";")[0] :
                                    null);
                        } catch (Exception ignore) {
                            return null;
                        }
                    }
                    return rs.getString("produtoGymPass");
                }
            }
        }
        return null;
    }

    public Boolean validarCheckinEmpresa(Integer periodoAcesso, Integer empresa) throws Exception {
        String sqlStr = "SELECT empresa from infocheckin where periodoacesso = " + periodoAcesso;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                if(rs.next()){
                    return rs.getInt("empresa") == empresa;
                }
            }
        }
        return true;
    }
}
