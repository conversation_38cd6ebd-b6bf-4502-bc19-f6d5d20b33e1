package negocio.facade.jdbc.contrato;

import negocio.comuns.contrato.ContratoTextoPadraoVO;
import negocio.comuns.financeiro.ProdutoTextoPadraoVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.PlanoTextoPadraoTag;
import negocio.interfaces.contrato.ProdutoTextoPadraoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoTextoPadraoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoTextoPadraoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoTextoPadraoVO
 * @see SuperEntidade
 */
public class ProdutoTextoPadrao extends SuperEntidade implements ProdutoTextoPadraoInterfaceFacade {

    public ProdutoTextoPadrao() throws Exception {
        super();
        setIdEntidade("Contrato");
    }
    public ProdutoTextoPadrao(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoTextoPadraoVO</code>.
     */
    public ProdutoTextoPadraoVO novo() throws Exception {
        incluir(getIdEntidade());
        ProdutoTextoPadraoVO obj = new ProdutoTextoPadraoVO();
        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoTextoPadraoVO</code>.
     * @return  O objeto da classe <code>ContratoTextoPadraoVO</code> com os dados devidamente montados.
     */
    public static ProdutoTextoPadraoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ProdutoTextoPadraoVO obj = new ProdutoTextoPadraoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setPlanoTextoPadrao(new Integer(dadosSQL.getInt("planoTextoPadrao")));
        obj.setVendaAvulsa(new Integer(dadosSQL.getInt("vendaAvulsa")));
        obj.setAulaavulsadiaria(new Integer(dadosSQL.getInt("aulaavulsadiaria")));
        obj.setProduto(new Integer(dadosSQL.getInt("produto")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoTextoPadraoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ProdutoTextoPadraoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ProdutoTextoPadrao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ProdutoTextoPadrao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
    public ProdutoTextoPadraoVO consultarPorProdutoEVenda(Integer codigoProduto, Integer codigoVenda, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ProdutoTextoPadrao WHERE produto = ? and vendaavulsa = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoProduto.intValue());
        sqlConsultar.setInt(2, codigoVenda.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public ProdutoTextoPadraoVO consultarPorProdutoEAulaAvulsaDiaria(Integer codigoProduto, Integer codigoVendaDiaria, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ProdutoTextoPadrao WHERE produto = ? and aulaavulsadiaria = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoProduto.intValue());
        sqlConsultar.setInt(2, codigoVendaDiaria.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public void alterarAssinaturaHtmlContratoProduto(Integer codigoContrato) throws Exception {
        try (ResultSet rs = con.createStatement().executeQuery("select texto from produtoTextoPadrao where codigo =" + codigoContrato)) {
            String html = "";
            if (rs.next()) {
                html = rs.getString("texto");
                if (!UteisValidacao.emptyString(html)) {
                    ProdutoAssinaturaDigital coass = new ProdutoAssinaturaDigital(con);
                    String assinatura = coass.obterPorContrato(codigoContrato);
                    if (!UteisValidacao.emptyString(assinatura)) {
                        html = PlanoTextoPadraoVO.addAssinatura(html, Uteis.getPaintFotoDaNuvem(assinatura));
                    }
                    try (PreparedStatement pst = con.prepareStatement("update produtoTextoPadrao set texto=? where codigo = ?")) {
                        pst.setString(1, html);
                        pst.setInt(2, codigoContrato);
                        pst.execute();
                    }
                }
            }

        }

    }

    public void incluir(ProdutoTextoPadraoVO produtoTextoPadraoVO) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(produtoTextoPadraoVO);
            con.commit();
        } catch (Exception e) {
            produtoTextoPadraoVO.setNovoObj(Boolean.TRUE);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(ProdutoTextoPadraoVO produtoTextoPadraoVO) throws Exception {
        incluir(getIdEntidade());

        String sqlInsert = "INSERT INTO ProdutoTextoPadrao("
                + " vendaavulsa, produto, planotextopadrao, texto, datalancamento, dataassinaturacontrato, ipassinaturacontrato, emailrecebimento ) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?)";

        PreparedStatement sqlInserir = con.prepareStatement(sqlInsert);
        sqlInserir.setInt(1, produtoTextoPadraoVO.getVendaAvulsa());
        sqlInserir.setInt(2, produtoTextoPadraoVO.getProduto());
        sqlInserir.setInt(3, produtoTextoPadraoVO.getPlanoTextoPadrao());
        sqlInserir.setString(4, produtoTextoPadraoVO.getTexto());
        sqlInserir.setDate(5, Uteis.getDataJDBC(produtoTextoPadraoVO.getDataLancamento()));
        sqlInserir.setDate(6, Uteis.getDataJDBC(produtoTextoPadraoVO.getDataAssinaturaContrato()));
        sqlInserir.setString(7, produtoTextoPadraoVO.getIpAssinaturaContrato());
        sqlInserir.setString(8, produtoTextoPadraoVO.getEmailRecebimento());

        sqlInserir.execute();
    }
}
