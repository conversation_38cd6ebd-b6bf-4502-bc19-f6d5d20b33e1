package negocio.facade.jdbc.contrato;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.AditivoVO;
import negocio.comuns.contrato.ContratoTextoPadraoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.plano.Aditivo;
import negocio.facade.jdbc.plano.PlanoTextoPadrao;
import negocio.interfaces.contrato.ContratoTextoPadraoInterfaceFacade;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoTextoPadraoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoTextoPadraoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoTextoPadraoVO
 * @see SuperEntidade
 */
public class ContratoTextoPadrao extends SuperEntidade implements ContratoTextoPadraoInterfaceFacade {

    public ContratoTextoPadrao() throws Exception {
        super();
        setIdEntidade("Contrato");
    }
    public ContratoTextoPadrao(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoTextoPadraoVO</code>.
     */
    public ContratoTextoPadraoVO novo() throws Exception {
        incluir(getIdEntidade());
        ContratoTextoPadraoVO obj = new ContratoTextoPadraoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoTextoPadraoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoTextoPadraoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ContratoTextoPadraoVO obj) throws Exception {
        try {
            ContratoTextoPadraoVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO ContratoTextoPadrao( contrato, planoTextoPadrao ) VALUES ( ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, obj.getContrato().intValue());
            if (obj.getPlanoTextoPadrao().getCodigo().intValue() != 0) {
                sqlInserir.setInt(2, obj.getPlanoTextoPadrao().getCodigo().intValue());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoTextoPadraoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoTextoPadraoVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContratoTextoPadraoVO obj) throws Exception {
        try {
            ContratoTextoPadraoVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE ContratoTextoPadrao set contrato=?, planoTextoPadrao=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setInt(1, obj.getContrato().intValue());
            if (obj.getPlanoTextoPadrao().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(2, obj.getPlanoTextoPadrao().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setInt(3, obj.getCodigo().intValue());
            sqlAlterar.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoTextoPadraoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoTextoPadraoVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContratoTextoPadraoVO obj) throws Exception {
        try {

            excluir(getIdEntidade());
            String sql = "DELETE FROM ContratoTextoPadrao WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();

        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoTextoPadrao</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoTextoPadraoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoTextoPadrao WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoTextoPadraoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoTextoPadraoVO consultarPorCodigoContrato(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoTextoPadrao WHERE contrato = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoTextoPadrao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoTextoPadraoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoTextoPadraoVO obj = new ContratoTextoPadraoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoTextoPadraoVO</code>.
     * @return  O objeto da classe <code>ContratoTextoPadraoVO</code> com os dados devidamente montados.
     */
    public static ContratoTextoPadraoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContratoTextoPadraoVO obj = new ContratoTextoPadraoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getPlanoTextoPadrao().setCodigo(new Integer(dadosSQL.getInt("planoTextoPadrao")));
        obj.setContrato(new Integer(dadosSQL.getInt("contrato")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosPlanoTextoPadrao(obj, nivelMontarDados, con);
        return obj;
    }

    /**
     * Operação responsável por consultar todos os <code>ContratoComposicaoVO</code> relacionados a um objeto da classe <code>contrato.Contrato</code>.
     * @param contrato  Atributo de <code>contrato.Contrato</code> a ser utilizado para localizar os objetos da classe <code>ContratoComposicaoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoComposicaoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public ContratoTextoPadraoVO consultarContratoTextoPadrao(Integer contrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ContratoTextoPadrao WHERE contrato = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, contrato.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        if (!resultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoTextoPadrao ).");
        }
        return (montarDados(resultado, nivelMontarDados, this.con));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoTextoPadraoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoTextoPadraoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoTextoPadrao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoTextoPadrao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>EmpresaVO</code> relacionado ao objeto <code>ContratoVO</code>.
     * Faz uso da chave primária da classe <code>EmpresaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPlanoTextoPadrao(ContratoTextoPadraoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPlanoTextoPadrao().getCodigo().intValue() == 0) {
            obj.setPlanoTextoPadrao(new PlanoTextoPadraoVO());
            return;
        }
        PlanoTextoPadrao planoTextoPadrao = new PlanoTextoPadrao(con);
        obj.setPlanoTextoPadrao(planoTextoPadrao.consultarPorChavePrimaria(obj.getPlanoTextoPadrao().getCodigo(), nivelMontarDados));
        planoTextoPadrao = null;
    }

    public String gravarHtmlContrato(Integer codigoContrato, UsuarioVO usuarioVO, boolean atualizarContrato) throws Exception {
        return gravarHtmlContrato(codigoContrato, usuarioVO, atualizarContrato, null);
    }

    public String gravarHtmlContrato(Integer codigoContrato, UsuarioVO usuarioVO, Integer aditivo) throws Exception {
        return gravarHtmlContrato(codigoContrato, usuarioVO, false, aditivo);
    }

    public String gravarHtmlContrato(Integer codigoContrato, UsuarioVO usuarioVO, boolean atualizarContrato, Integer aditivo) throws Exception {
        Contrato contratoDao = new Contrato(con);
        MovParcela movParcelaDao = new MovParcela(con);
        Pessoa pessoaDao = new Pessoa(con);
        Empresa empresaDao = new Empresa(con);
        Usuario usuarioDao = new Usuario(con);
        PlanoTextoPadrao planoTextoPadraoDao = new PlanoTextoPadrao(con);
        MovPagamento movPagamentoDao = new MovPagamento(con);
        Cliente clienteDao = new Cliente(con);
        ContratoDuracaoCreditoTreino contratoDuracaoCreditoTreinoDao = new ContratoDuracaoCreditoTreino(con);
        ContratoVO contratoVO = contratoDao.consultarPorChavePrimaria(codigoContrato, Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);

        contratoVO.setMovParcelaVOs(movParcelaDao.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        contratoVO.setPessoa(pessoaDao.consultarPorChavePrimaria(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        contratoVO.setEmpresa(empresaDao.consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        contratoVO.setResponsavelContrato(usuarioDao.consultarPorChavePrimaria(contratoVO.getResponsavelContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        contratoVO.getPlano().setPlanoTextoPadrao(planoTextoPadraoDao.consultarPorChavePrimaria(contratoVO.getContratoTextoPadrao().getPlanoTextoPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        if (contratoVO.isVendaCreditoTreino()) {
            contratoVO.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(contratoDuracaoCreditoTreinoDao.consultarPorContratoDuracao(contratoVO.getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }

        // pesquisar pagamentos já efetuados para informar no contrato.
        List<MovPagamentoVO> pagamentos = movPagamentoDao.consultarPagamentoDeUmContrato(contratoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        ClienteVO cliente = clienteDao.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        String html = "";
        ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
        Boolean manterContratoAssinadoNaRenovacaoContrato = configuracaoSistema.isManterContratoAssinadoNaRenovacaoContrato();
        if(contratoVO.getSituacaoContrato().equals("RN")
                && contratoVO.getResponsavelContrato().getUsuarioRecorrencia()
                && manterContratoAssinadoNaRenovacaoContrato) {
            Integer codigoPrimeiroContratoBaseadoRenovacao = contratoVO.getPrimeiroContratoBaseadoRenovacao() > 0 ?
                    contratoVO.getPrimeiroContratoBaseadoRenovacao() : contratoDao.obterCodigoPrimeiroContratoBaseadoRenovacao(contratoVO.getCodigo());
            if (!UteisValidacao.emptyNumber(codigoPrimeiroContratoBaseadoRenovacao)) {
                html = consultarHtmlContrato(codigoPrimeiroContratoBaseadoRenovacao, false);
            }
        }
        if (UteisValidacao.emptyString(html)) {
            try {
                if (aditivo == null) {
                    html = contratoVO.getPlano().getPlanoTextoPadrao().substituirTagsTextoEnvio(DAO.resolveKeyFromConnection(con), contratoVO,cliente, pagamentos, con,contratoVO.getEmpresa().getDescMoeda());
                } else {
                    final Aditivo aditivoDao = new Aditivo(con);
                    final AditivoVO entity = aditivoDao.buscarPorCodigo(aditivo);
                    html = new PlanoTextoPadraoVO().varrerListaTagUtilizadoTextoPadrao(DAO.resolveKeyFromConnection(con), entity.getDescricao(), cliente, contratoVO, pagamentos, con, contratoVO.getEmpresa().getDescMoeda());
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new ConsistirException("Erro ao gerar texto do contrato: " + e.getMessage());
            }
        }

        boolean permiteImpressaoContratoMutavel = configuracaoSistema.isPermiteImpressaoContratoMutavel();
        if (!permiteImpressaoContratoMutavel || atualizarContrato) {

            String query = "update contratoTextoPadrao set contratoHtml = ?, dataGravacaoContratoHtml = ?, usuarioGravacaoContratoHtml = ? where contrato = ?";
            if (aditivo == null) {
                query += " and aditivo is null";
            } else {
                query += " and aditivo = ?";
            }

            try (PreparedStatement pst = con.prepareStatement(query)) {
                pst.setString(1, html);
                pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                String nomeUsuario = "admin-background";
                if (usuarioVO != null) {
                    nomeUsuario = usuarioVO.getNome();
                    if (!usuarioVO.getUserOamd().equals("")) {
                        nomeUsuario = nomeUsuario + " userOamd:" + usuarioVO.getUserOamd();
                    }
                }
                pst.setString(3, nomeUsuario);
                pst.setInt(4, codigoContrato);
                if (aditivo != null) {
                    pst.setInt(5, codigoContrato);
                }
                pst.execute();
            }
        }
        return html;
    }
    public String gravarHtmlContrato(Integer codigoContrato, UsuarioVO usuarioVO)throws Exception{
        return gravarHtmlContrato(codigoContrato,usuarioVO,false);
    }

    public String consultarHtmlContratoAditivo(Integer codigoContrato, boolean comBotaoImprimir, Integer aditivo, boolean telaNova)throws Exception{
        return consultarHtmlContrato(codigoContrato, comBotaoImprimir, false, telaNova, aditivo);
    }

    public String consultarHtmlContrato(Integer codigoContrato, boolean comBotaoImprimir)throws Exception{
        return consultarHtmlContrato(codigoContrato, comBotaoImprimir, false, false, null);
    }

    public String consultarHtmlContratoTelaNova(Integer codigoContrato)throws Exception{
        return consultarHtmlContrato(codigoContrato, true, true, true, null);
    }

    public String consultarHtmlContrato(Integer codigoContrato, boolean comBotaoImprimir, boolean semTagAssinaturaVazia)throws Exception{
        return consultarHtmlContrato(codigoContrato, comBotaoImprimir, semTagAssinaturaVazia, false, null);
    }

    public String consultarHtmlContrato(Integer codigoContrato, boolean comBotaoImprimir,
                                        boolean semTagAssinaturaVazia, boolean telaNova, Integer aditivo)throws Exception{
        ConfiguracaoSistema configDAO = new ConfiguracaoSistema(con);
        boolean permiteImpressaoContratoMutavel = configDAO.isPermiteImpressaoContratoMutavel();
        configDAO = null;

        String query = "select contratoHtml from contratoTextoPadrao where contrato =" + codigoContrato;
        if (aditivo == null) {
            query += " and aditivo is null";
        } else {
            query += " and aditivo = " + aditivo;
        }

        try(ResultSet rs = con.createStatement().executeQuery(query)){
            String html = "";
            if (rs.next()){
                html = rs.getString("contratoHtml");
                if (UteisValidacao.emptyString(html) || permiteImpressaoContratoMutavel){
                    html = gravarHtmlContrato(codigoContrato, null, aditivo);
                }
            }
            if (!UteisValidacao.emptyString(html)) {

                if (permiteImpressaoContratoMutavel &&
                        html.contains("__4ssin4tur4digit4l__")) {
                    html = new PlanoTextoPadraoVO().preencherAssinaturaDigital(html, codigoContrato, con);
                }

                if (semTagAssinaturaVazia) {
                    html = html.replaceAll("__4ssin4tur4digit4l__", " ");
                    html = html.replaceAll("__4ssin4tur4digit4l2__", " ");
                }

                final String joinAditivo = !UteisValidacao.emptyNumber(aditivo) ?
                        " INNER JOIN contratoTextoPadrao ctp on ctp.contrato = c.codigo\n" +
                        " INNER JOIN aditivo ad on ad.codigo = ctp.aditivo\n" +
                        " LEFT JOIN contratoassinaturadigital cad ON cad.contratoaditivo = c.codigo and cad.aditivo = ad.codigo\n" :
                        " LEFT JOIN contratoassinaturadigital cad ON cad.contrato = c.codigo ";

                final String whereAditivo = !UteisValidacao.emptyNumber(aditivo) ? " AND ad.codigo = " + aditivo : " ";

                final ResultSet rsAssinatura = con.createStatement().executeQuery("SELECT " +
                        "ipassinaturacontrato,dataassinaturacontrato,emailrecebimento, p.cfp, " +
                        "cad.documentos, cad.endereco, cad.atestado, cad.anexo1, cad.anexo2, cad.assinatura, cad.assinatura2, " +
                        "emp.exigirAssinaturaDigitalResponsavelFinanceiro " +
                        " from contrato c  " +
                        "INNER JOIN pessoa p ON p.codigo = COALESCE(c.pessoaoriginal, c.pessoa) " +
                        "INNER JOIN empresa emp ON emp.codigo = c.empresa " +
                        "INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa " +
                        joinAditivo +
                        "WHERE c.codigo  = " + codigoContrato + " " +
                        "AND (cad.assinatura is not null AND cad.assinatura <> '' or c.dataassinaturacontrato is not null)" +
                        whereAditivo);

                if (rsAssinatura.next()) {
                    if (telaNova) {
                        html = this.incluirAnexos("[(1){}docRg]", html, rsAssinatura.getString("documentos"));
                        html = this.incluirAnexos("[(1){}atestado]", html, rsAssinatura.getString("atestado"));
                        html = this.incluirAnexos("[(1){}endereco]", html, rsAssinatura.getString("endereco"));
                        html = this.incluirAnexos("[(1){}anexo1]", html, rsAssinatura.getString("anexo1"));
                        html = this.incluirAnexos("[(1){}anexo2]", html, rsAssinatura.getString("anexo2"));

                        if (!UteisValidacao.emptyNumber(aditivo) && !UteisValidacao.emptyString(rsAssinatura.getString("assinatura"))) {
                            html = PlanoTextoPadraoVO.addAssinatura(html, Uteis.getPaintFotoDaNuvem(rsAssinatura.getString("assinatura")));
                        }
                    } else {
                        html = this.limparTagsAnexoAssinatura(html);
                    }

                    if (!UteisValidacao.emptyString(rsAssinatura.getString("ipassinaturacontrato"))) {
                        String assinatura = "Assinado via email pelo IP: " + rsAssinatura.getString("ipassinaturacontrato") +
                                Uteis.getDataAplicandoFormatacao(rsAssinatura.getTimestamp("dataassinaturacontrato"), "' em ' dd/MM/yyyy 'as' HH:mm") + ". CPF confirmado: " + rsAssinatura.getString("cfp") + "; Email confirmado: " + rsAssinatura.getString("emailrecebimento") + " .";
                        html = this.incluirAssinatura(html, assinatura);
                    }
                }else{
                    html = this.limparTagsAnexoAssinatura(html);
                }
                if (comBotaoImprimir) {
                    html = incluirBotaoImprimirHtmlContrato(html);
                }
            }
            return html;
        }
    }


    public void alterarTextoPadraoContrato(Integer codigoContrato, Integer codigoPlanoTextoPadrao) throws Exception {
        String sql = "UPDATE ContratoTextoPadrao SET contratoHtml = NULL," +
                " datagravacaocontratohtml = NULL," +
                " usuariogravacaocontratohtml = NULL," +
                " planotextopadrao = " + codigoPlanoTextoPadrao +
                " WHERE contrato = " + codigoContrato;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.execute();
    }

    public void anularContratosHtml() throws Exception {
        String sql = "UPDATE ContratoTextoPadrao set contratoHtml = NULL, datagravacaocontratohtml = NULL, usuariogravacaocontratohtml = NULL";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.execute();
    }

    private String incluirBotaoImprimirHtmlContrato(String html) throws Exception{
        html = html.replace("</head>", "<style>@media print {#imprimir {display: none;}}</style></head>");
        char aspas = '"';
        String botaoImprimir = "</div><form> <input id=" + aspas + "imprimir" + aspas + "type=" + aspas + "image" + aspas + " src=" + aspas + "./imagens/imprimirContrato.png" + aspas + " name=" + aspas + "imprimir" + aspas + " alt=" + aspas + "Imprimir Contrato" + aspas + "onclick=" + aspas + "window.print();" + aspas + "/>";
        String espaco = "<p style=" + aspas + "text-align: left;" + aspas + ">";
        StringBuilder htmlComBotaoImprimir = new StringBuilder(html.replace("</form>", "").replace("</body>", "").replace("</html>", ""));
        htmlComBotaoImprimir.append(botaoImprimir + espaco);
        htmlComBotaoImprimir.append("</form>");
        htmlComBotaoImprimir.append("</body>");
        htmlComBotaoImprimir.append("</html>");
        if (JSFUtilities.isJSFContext()){
            HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
            request.getSession().setAttribute("textoRelatorio", htmlComBotaoImprimir.toString());
        }
        return htmlComBotaoImprimir.toString();
    }

    private String incluirAssinatura(String html, String assinatura)throws Exception{
        html.replace("</form>", "").replace("</body>", "").replace("</html>", "");

        String labelAssinatura = "<p><strong>"+assinatura+"</strong></p>";
        StringBuilder htmlComBotaoImprimir = new StringBuilder(html.replace("</form>", "").replace("</body>", "").replace("</html>", ""));
        html+=labelAssinatura;
        html+="</form>";
        html+="</body>";
        html+="</html>";
       return  html;
    }

    private String incluirAnexos(String tag, String html, String anexo) throws IOException {
        String id = tag.replace("[(1){}", "").replace("]", "");
        if (html.contains(tag) && !UteisValidacao.emptyString(anexo) && validarExtensaoFotoPermitida(anexo)) {
            String divAnexo = "";
            URL url = new URL(Uteis.getPaintFotoDaNuvem(anexo));
            BufferedImage imagem = ImageIO.read(url);

            int largura = imagem.getWidth();
            int altura = imagem.getHeight();

            if (largura > altura) { // horizontal
                divAnexo = "<span id=\"" + id + "\" style=\"text-align: center; margin: 0px auto 0px 5px;\">"
                        + "<img style=\"height: " + 350 + "px; width: " + 400 + "px;\" src=\""
                        .concat(Uteis.getPaintFotoDaNuvem(anexo)).concat("\"/></span>");
            } else { // vertical
                divAnexo = "<span id=\"" + id + "\" style=\"text-align: center; margin: 0px auto 0px 5px;\">"
                        + "<img style=\"height: " + 350 + "px; width: " + 300 + "px;\" src=\""
                        .concat(Uteis.getPaintFotoDaNuvem(anexo)).concat("\"/></span>");
            }
            html = html.replace(tag, divAnexo);
        } else {
            html = html.replace(tag, "");
        }
        return html;
    }

    private boolean validarExtensaoFotoPermitida(String arquivo) {
        if (arquivo.endsWith(".jpg") || arquivo.endsWith(".jpeg") || arquivo.endsWith(".png")) {
            return true;
        }
        return false;
    }

    public String limparTagsAnexoAssinatura(String html){
        html = html.replace("[(1){}docRg]", "");
        html = html.replace("[(1){}endereco]", "");
        html = html.replace("[(1){}atestado]", "");
        html = html.replace("[(1){}anexo1]", "");
        html = html.replace("[(1){}anexo2]", "");
        return html;
    }

    public void alterarAssinaturaHtmlContrato(Integer codigoContrato)throws Exception{
        try(ResultSet rs = con.createStatement().executeQuery("select contratoHtml from contratoTextoPadrao where contrato = " + codigoContrato + " and aditivo is null")){
            String html = "";
            if (rs.next()){
                html = rs.getString("contratoHtml");
                if (!UteisValidacao.emptyString(html)){
                    ContratoAssinaturaDigital coass = new ContratoAssinaturaDigital(con);
                    String assinatura = coass.obterPorContrato(codigoContrato, false);
                    if (!UteisValidacao.emptyString(assinatura)) {
                        html = PlanoTextoPadraoVO.addAssinatura(html, Uteis.getPaintFotoDaNuvem(assinatura));
                    }
                    String assinatura2 = coass.obterPorContrato(codigoContrato, true);
                    if (!UteisValidacao.emptyString(assinatura2)) {
                        html = PlanoTextoPadraoVO.addAssinatura2(html, Uteis.getPaintFotoDaNuvem(assinatura2));
                    }
                    try(PreparedStatement pst = con.prepareStatement("update contratoTextoPadrao set contratoHtml = ? where contrato = ?"  + " and aditivo is null")){
                        pst.setString(1,html);
                        pst.setInt(2,codigoContrato);
                        pst.execute();
                    }
                    ConfiguracaoSistema configSistema = new ConfiguracaoSistema(con);
                    if (configSistema.isManterContratoAssinadoNaRenovacaoContrato()) {
                        try(PreparedStatement pst = con.prepareStatement("update contratoTextoPadrao set contratoHtml = ? where contrato = ?" + " and aditivo is null")){
                            pst.setString(1,html);
                            pst.setInt(2,codigoContrato);
                            pst.execute();
                        }
                    }
                }
            }

        }
    }

    public void alterarAssinaturaHtmlContratoAditivo(Integer codigoContrato, Integer aditivo) throws Exception{
        try(ResultSet rs = con.createStatement().executeQuery("select contratoHtml from contratoTextoPadrao where contrato = " + codigoContrato + " and aditivo = " + aditivo)){
            String html = "";
            if (rs.next()){
                html = rs.getString("contratoHtml");
                if (!UteisValidacao.emptyString(html)){
                    ContratoAssinaturaDigital coass = new ContratoAssinaturaDigital(con);
                    String assinatura = coass.obterPorContratoEAditivo(codigoContrato, false, aditivo);
                    if (!UteisValidacao.emptyString(assinatura)) {
                        html = PlanoTextoPadraoVO.addAssinatura(html, Uteis.getPaintFotoDaNuvem(assinatura));
                    }
                    String assinatura2 = coass.obterPorContratoEAditivo(codigoContrato, true, aditivo);
                    if (!UteisValidacao.emptyString(assinatura2)) {
                        html = PlanoTextoPadraoVO.addAssinatura2(html, Uteis.getPaintFotoDaNuvem(assinatura2));
                    }
                    try(PreparedStatement pst = con.prepareStatement("update contratoTextoPadrao set contratoHtml = ? where contrato = ? and aditivo = ? ")){
                        pst.setString(1, html);
                        pst.setInt(2, codigoContrato);
                        pst.setInt(3, aditivo);
                        pst.execute();
                    }
                    ConfiguracaoSistema configSistema = new ConfiguracaoSistema(con);
                    if (configSistema.isManterContratoAssinadoNaRenovacaoContrato()) {
                        try(PreparedStatement pst = con.prepareStatement("update contratoTextoPadrao set contratoHtml = ? where contrato = ? and aditivo = ? ")){
                            pst.setString(1, html);
                            pst.setInt(2, codigoContrato);
                            pst.setInt(3, aditivo);
                            pst.execute();
                        }
                    }
                }
            }

        }
    }
    public String atualizarHtmlContrato(Integer codigoContrato, Integer pessoa)throws Exception{
        try(ResultSet rs = con.createStatement().executeQuery("select contratoHtml from contratoTextoPadrao where contrato ="+codigoContrato)){
            String html = "";
            if (rs.next()){
                html = gravarHtmlContrato(codigoContrato,
                            null
                            ,true);
            }
            return html;
        }
    }

    public String montarContratoAditivoAssinaturaConfirmacao(String texto, Integer contrato) throws Exception {
        try (ResultSet rs = con.createStatement().executeQuery("select ctp.aditivo, cad.documentos, cad.ip, cad.tipoautenticacao, cad.dadosautenticacao from contratotextopadrao ctp " +
                "inner join contratoassinaturadigital cad on cad.aditivo = ctp.aditivo" +
                " where ctp.contrato = " + contrato +
                " and cad.contrato is null and cad.assinatura is not null and cad.assinatura <> ''")) {
            while (rs.next()) {
                try {
                    if (rs.getInt("aditivo") > 0) {

                        texto += "\n<div class=\"section-title\">Contrato Aditivo</div>\n";
                        texto += "  <img class=\"foto-doc\" src=\""+Uteis.getPaintFotoDaNuvem(rs.getString("documentos"))+"\" alt=\"Foto documento\">\n";
                        texto += "  <div class=\"grid\">\n";
                        texto += "    <span><strong>Autentica&ccedil;&atilde;o via telefone:</strong> "+rs.getString("tipoautenticacao")+"</span>\n";
                        texto += "    <span><strong>IP do dispositivo de assinatura:</strong> "+rs.getString("ip")+"</span>\n";
                        texto += "  </div>\n";

                        texto += consultarHtmlContrato(contrato, false, true, true, rs.getInt("aditivo"));
                    }
                } catch (Exception e) {
                    Uteis.logar(e, ContratoTextoPadrao.class);
                }
            }
        }
        return texto;
    }

}
