package negocio.facade.jdbc.contrato;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.enumeradores.SituacaoParcelaEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estrutura.paginacao.PreparedStatementPersonalizado;
import br.com.pactosolucoes.estudio.dao.AgendaEstudio;
import controle.arquitetura.SuperControle;
import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.DescontoTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.GenericoAtributosVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.EstornoMovProdutoVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.plano.AlunoHorarioTurmaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.PeriodoMensal;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ControleTaxaPersonal;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.ReciboDevolucao;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.nfe.LoteNFe;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.facade.jdbc.utilitarias.CacheControl;
import negocio.interfaces.contrato.MovProdutoInterfaceFacade;
import negocio.interfaces.contrato.MovProdutoModalidadeInterfaceFacade;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoMesVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoResumoPessoaVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoProdutoMesVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoProdutoVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoResumoPessoaVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoTipoProdutoVO;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import relatorio.negocio.jdbc.financeiro.ProdutoRatear;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe <code>MovProdutoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e
 * consultar pertinentes a classe <code>MovProdutoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 *
 * @see MovProdutoVO
 * @see SuperEntidade
 */
public class MovProduto extends SuperEntidade implements MovProdutoInterfaceFacade {
    // CD=convenio de desconto, DE=desconto extra, DV=devolucao, DR=desconto de renovacao antecipada, CC=CONTA CORRENTE ALUNO, RD= Devolução de recebíveis, DC= Devolução de crédito de conta corrente do cliente, CHEQUE DEVOLVIDO

    public final static String PRODUTOS_IGNORADOS_FATURAMENTO_RECEBIDO = "'CD','DE','DV','DR', 'RD', 'DC', '"+TipoProduto.CHEQUE_DEVOLVIDO.getCodigo()+"'";
    public final static String PRODUTOS_IGNORADOS = PRODUTOS_IGNORADOS_FATURAMENTO_RECEBIDO + ", 'CC'";
    private Hashtable movProdutoParcelas = new Hashtable();

    public MovProduto() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public MovProduto(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    private EmpresaInterfaceFacade empresaDao;

    public EmpresaInterfaceFacade getEmpresaDao() throws Exception {
        if (empresaDao == null) {
            empresaDao = new Empresa(getCon());
        }
        empresaDao.setCon(getCon());
        return empresaDao;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para
     * um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     */
    public static List<MovProdutoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<MovProdutoVO> vetResultado = new ArrayList<MovProdutoVO>();
        while (tabelaResultado.next()) {
            MovProdutoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>)
     * em um objeto da classe <code>MovProdutoVO</code>.
     *
     * @return O objeto da classe <code>MovProdutoVO</code> com os dados
     *         devidamente montados.
     */
    public static MovProdutoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MovProdutoVO obj = new MovProdutoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getProduto().setCodigo(dadosSQL.getInt("produto"));
        obj.getContrato().setCodigo(dadosSQL.getInt("contrato"));
        obj.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
        obj.setEmpresa(getCachedEmpresa(dadosSQL.getInt("empresa"), con));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setQuantidade(dadosSQL.getInt("quantidade"));
        obj.setPrecoUnitario(dadosSQL.getDouble("precoUnitario"));
        obj.setValorDesconto(dadosSQL.getDouble("valorDesconto"));
        obj.setTotalFinal(dadosSQL.getDouble("totalFinal"));
        obj.setDataLancamento(dadosSQL.getTimestamp("dataLancamento"));
        obj.getResponsavelLancamento().setCodigo(new Integer(dadosSQL.getInt("responsavelLancamento")));
        obj.setMesReferencia(dadosSQL.getString("mesReferencia"));
        obj.setAnoReferencia(dadosSQL.getInt("anoReferencia"));
        obj.setDataInicioVigencia(dadosSQL.getDate("dataInicioVigencia"));
        obj.setDataFinalVigencia(dadosSQL.getDate("dataFinalVigencia"));
        obj.setDataFinalVigenciaOriginal(obj.getDataFinalVigencia());
        obj.setApresentarMovProduto(dadosSQL.getBoolean("apresentarMovProduto"));
        obj.setQuitado(dadosSQL.getBoolean("quitado"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setNaoGerarMensagem(dadosSQL.getBoolean("naoGerarMensagem"));
        obj.setMovpagamentocc(dadosSQL.getString("movpagamentocc"));
        obj.setValorFaturado(dadosSQL.getDouble("valorFaturado"));
        StringBuilder sb = dadosSQL.getString("descricaoMovProdutoModalidade") == null ? new StringBuilder()
                : new StringBuilder(dadosSQL.getString("descricaoMovProdutoModalidade"));
        obj.setDescricaoMovProdutoModalidade(sb);
        try{
            obj.setVendaAvulsa(dadosSQL.getInt("vendaavulsa"));
            obj.setLancamentoColetivo(dadosSQL.getInt("lancamentoColetivo"));
            obj.setJuros(dadosSQL.getDouble("juros"));
            obj.setMulta(dadosSQL.getDouble("multa"));
            obj.setNumeroCupomDesconto(dadosSQL.getString("numeroCupomDesconto"));
            obj.setChequeDevolucao(new ChequeVO());
            obj.getChequeDevolucao().setCodigo(dadosSQL.getInt("chequeDevolucao"));
            obj.setCodigoOperacaoFinanceira(dadosSQL.getString("codigooperacaofinanceira"));
            obj.setMultaNaoRecebida(dadosSQL.getDouble("multaNaoRecebida"));
            obj.setJurosNaoRecebidos(dadosSQL.getDouble("jurosNaoRecebidos"));
            obj.setMovProdutoBase(dadosSQL.getInt("movProdutoBase"));
            obj.setRenovavelAutomaticamente(dadosSQL.getBoolean("renovavelAutomaticamente"));
        }catch(Exception e){
            //desconsiderar
        }
        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_VENDA){
            montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_VENDA, con);
            return obj;
        }
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_COMISSAO) {
            montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_COMISSAO, con);
            montarDadosResponsavelLancamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_RENOVACAO_AUTOMATICA) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        MovProduto movProduto = new MovProduto(con);
        if (obj.getSituacao().equals("EA")) {
            movProduto.obterValorParcialmentPago(obj);
        }
        movProduto = null;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_PAGAMENTOS_TELA_CLIENTE) {
            montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            if (obj.getProduto().getTipoProduto().equals("SS") && obj.getSituacao().equals("CA")) {
                ReciboDevolucao recDev = new ReciboDevolucao(con);
                obj.setReciboDevolucao(recDev.consultarPorMovProduto(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                recDev = null;
            }
            return obj;
        }

        MovProdutoParcela movProdutoParcela = new MovProdutoParcela(con);
        obj.setMovProdutoParcelaVOs(movProdutoParcela.consultarMovProdutoParcelas(obj.getCodigo(), nivelMontarDados));
        movProdutoParcela = null;

        //nivel de montar dados usado para mostrar informacoes na tela de movimento de produto
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            obj.getPessoa().setNome(dadosSQL.getString("nomePessoa"));
            obj.getEmpresa().setNome(dadosSQL.getString("nomeEmpresa"));
            obj.getProduto().setDescricao(dadosSQL.getString("descricaoProduto"));
            obj.getResponsavelLancamento().setNome(dadosSQL.getString("nomeResponsavelLancamento"));
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_DADOSRESPONSAVELALTERACAO, con);
            montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            if (obj.getProduto().getTipoProduto().equals("SS") && obj.getSituacao().equals("CA")) {
                ReciboDevolucao recDev = new ReciboDevolucao(con);
                obj.setReciboDevolucao(recDev.consultarPorMovProduto(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                recDev = null;
            }
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosResponsavelLancamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosModalidades(con, obj);
        return obj;
    }

    /**
     * <AUTHOR> Alcides
     * 16/08/2012
     */
    private static void montarDadosModalidades(Connection con, MovProdutoVO obj) throws Exception {
        MovProdutoModalidade mpm = new MovProdutoModalidade(con);
        obj.setMovProdutoModalidades(mpm.consultarPorMovProduto(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>MovProdutoVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para
     * realizar a consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelLancamento(MovProdutoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelLancamento().getCodigo() == 0) {
            obj.setResponsavelLancamento(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setResponsavelLancamento(usuario.consultarPorChavePrimaria(obj.getResponsavelLancamento().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>EmpresaVO</code> relacionado ao objeto <code>MovProdutoVO</code>.
     * Faz uso da chave primária da classe <code>EmpresaVO</code> para realizar
     * a consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosEmpresa(MovProdutoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresa = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PessoaVO</code> relacionado ao objeto <code>MovProdutoVO</code>.
     * Faz uso da chave primária da classe <code>PessoaVO</code> para realizar a
     * consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPessoa(MovProdutoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPessoa().getCodigo() == 0) {
            obj.setPessoa(new PessoaVO());
            return;
        }
        Pessoa pessoa = new Pessoa(con);
        obj.setPessoa(pessoa.consultarPorChavePrimaria(obj.getPessoa().getCodigo(), nivelMontarDados));
        pessoa = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ContratoVO</code> relacionado ao objeto <code>MovProdutoVO</code>.
     * Faz uso da chave primária da classe <code>ContratoVO</code> para realizar
     * a consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosContrato(MovProdutoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getContrato().getCodigo() == 0) {
            obj.setContrato(new ContratoVO());
            return;
        }
        Contrato contrato = new Contrato(con);
        obj.setContrato(contrato.consultarPorChavePrimaria(obj.getContrato().getCodigo(), nivelMontarDados));
        contrato = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ProdutoVO</code> relacionado ao objeto <code>MovProdutoVO</code>.
     * Faz uso da chave primária da classe <code>ProdutoVO</code> para realizar
     * a consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosProduto(MovProdutoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProduto().getCodigo() == 0) {
            obj.setProduto(new ProdutoVO());
            return;
        }
        Produto produto = new Produto(con);
        obj.setProduto(produto.consultarPorChavePrimaria(obj.getProduto().getCodigo(), nivelMontarDados));
        produto = null;
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>MovProdutoVO</code>.
     */
    public MovProdutoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new MovProdutoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>MovProdutoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto.
     * Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj
     *            Objeto da classe <code>MovProdutoVO</code> que será gravado no
     *            banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão, restrição de acesso ou
     *                validação de dados.
     */
    public void incluir(MovProdutoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>MovProdutoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto.
     * Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj
     *            Objeto da classe <code>MovProdutoVO</code> que será gravado no
     *            banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão, restrição de acesso ou
     *                validação de dados.
     */
    public void incluirSemCommit(MovProdutoVO obj) throws Exception {
        MovProdutoVO.validarDados(obj);
        //Incluído validação para produtos com número de vigência diferente de nulo e
        //também para determinados produtos como aula avulsa e diária.
        if (obj.getProduto().getNrDiasVigencia() != null && !obj.getVigenciaJaCalculada()
                && obj.getDataLancamento() != null
                && (obj.getProduto().getTipoProduto().equals("AA") |
                obj.getProduto().getTipoProduto().equals("DI") |
                obj.getProduto().getTipoProduto().equals("SE") |
                obj.getProduto().getTipoProduto().equals("DS"))) {

            if (obj.getProduto().getTipoVigencia().equals("PF")) {
                obj.setDataInicioVigencia(obj.getProduto().getDataInicioVigencia());
                obj.setDataFinalVigencia(obj.getProduto().getDataFinalVigencia());
            } else {
                if (obj.getProduto().getTipoProduto().equals("DI")){
                    if (obj.getDataInicioVigencia() == null)
                      obj.setDataInicioVigencia(obj.getDataLancamento());
                }else{
                    obj.setDataInicioVigencia(obj.getDataLancamento());
                }
                if (obj.getContrato() != null && obj.getContrato().getVigenciaAteAjustada() != null
                        && obj.getProduto().isPrevalecerVigenciaContrato()) {
                    obj.setDataInicioVigencia(obj.getContrato().getVigenciaDe());
                    obj.setDataFinalVigencia(obj.getContrato().getVigenciaAteAjustada());
                } else {
                    obj.setDataFinalVigencia(Uteis.obterDataFutura(obj.getDataInicioVigencia(), obj.getProduto().getNrDiasVigencia()));
                }
            }
        }
        incluirSemValidar(obj);

    }

    private void excluirClienteMensagemProdutoVencido(MovProdutoVO obj) throws Exception {
        if (obj.getProduto().getTipoVigencia() != null
                && obj.getProduto().getTipoVigencia().equals("ID")) {
            Cliente clienteDAO = new Cliente(con);
            ClienteVO cliente = clienteDAO.consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            clienteDAO = null;

            ClienteMensagem clienteMensagemDAO = new ClienteMensagem(con);
            clienteMensagemDAO.excluirClienteMensagemProdutoVencido(cliente.getCodigo(), obj.getProduto().getCodigo());
            clienteMensagemDAO = null;
        }
    }

    /**
     * Método que contém parte do método incluirSemCommit, extraída para este novo método para
     * utilização nas operações de pagamento do Central de Eventos, tendo como razão a não compatibilidade
     * do validarDados com os dados vindo do CE.
     * <AUTHOR>
     * @param obj
     * @throws Exception
     */
    @Override
    public void incluirSemValidar(MovProdutoVO obj) throws Exception {
        incluir(getIdEntidade());
        excluirClienteMensagemProdutoVencido(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO MovProduto "
                + "(produto, contrato, pessoa, empresa, descricao, quantidade, "
                + "precoUnitario, valorDesconto, totalFinal, dataLancamento, "
                + "responsavelLancamento, mesReferencia, anoReferencia, "
                + "dataInicioVigencia, dataFinalVigencia, apresentarmovproduto, "
                + "quitado, situacao, naoGerarMensagem,descricaoMovProdutoModalidade, movpagamentocc, "
                + "lancamentoColetivo, vendaavulsa, multa, juros, valorFaturado, numeroCupomDesconto, codigooperacaofinanceira, jurosNaoRecebidos, multaNaoRecebida,movProdutoBase,renovavelAutomaticamente) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING codigo";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 1;
            if (obj.getProduto().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getProduto().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getContrato().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getContrato().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getPessoa().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getPessoa().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getEmpresa().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getEmpresa().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setString(i++, obj.getDescricao());
            sqlInserir.setInt(i++, obj.getQuantidade());
            sqlInserir.setDouble(i++, obj.getPrecoUnitario());
            sqlInserir.setDouble(i++, obj.getValorDesconto());
            sqlInserir.setDouble(i++, obj.getTotalFinal());
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            if (obj.getResponsavelLancamento().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getResponsavelLancamento().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }

            sqlInserir.setString(i++, obj.getMesReferencia());
            sqlInserir.setInt(i++, obj.getAnoReferencia());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
            sqlInserir.setBoolean(i++, obj.getApresentarMovProduto());
            sqlInserir.setBoolean(i++, obj.getQuitado());
            sqlInserir.setString(i++, obj.getSituacao());
            sqlInserir.setBoolean(i++, obj.getNaoGerarMensagem());
            sqlInserir.setString(i++, resolveDescricaoMovProdutoModalidade(obj).toString());
            sqlInserir.setString(i++, obj.getMovpagamentocc());
            resolveFKNull(sqlInserir, i++, obj.getLancamentoColetivo());
            resolveIntegerNull(sqlInserir, i++, obj.getVendaAvulsa());
            sqlInserir.setDouble(i++, obj.getMulta());
            sqlInserir.setDouble(i++, obj.getJuros());
            sqlInserir.setDouble(i++, obj.getValorFaturado() != null ? obj.getValorFaturado() : obj.getTotalFinal());
            if ((obj.getNumeroCupomDesconto() != null) && (!obj.getNumeroCupomDesconto().trim().isEmpty())) {
                sqlInserir.setString(i++, obj.getNumeroCupomDesconto());
            } else {
                sqlInserir.setNull(i++, Types.NULL);
            }

            sqlInserir.setString(i++, obj.getCodigoOperacaoFinanceira());
            sqlInserir.setDouble(i++, obj.getJurosNaoRecebidos());
            sqlInserir.setDouble(i++, obj.getMultaNaoRecebida());
            resolveIntegerNull(sqlInserir, i++, obj.getMovProdutoBase());
            sqlInserir.setBoolean(i++, obj.getRenovavelAutomaticamente());

            try (ResultSet rs = sqlInserir.executeQuery()) {
                if (rs.next()) {
                    obj.setCodigo(rs.getInt("codigo"));
                }
            }
        }

        MovProdutoModalidadeInterfaceFacade mpm = new MovProdutoModalidade(con);
        mpm.incluir(obj);
        //   new MovProdutoParcela().incluirMovProdutoParcelas(obj.getCodigo(), obj.getMovProdutoParcelaVOs());
        obj.setNovoObj(false);
    }

    private StringBuilder resolveDescricaoMovProdutoModalidade(MovProdutoVO obj) {
        StringBuilder sb = new StringBuilder();
        List<MovProdutoModalidadeVO> lista = obj.getMovProdutoModalidades();
        for (MovProdutoModalidadeVO movProdutoModalidadeVO : lista) {
            sb.append(movProdutoModalidadeVO.getDescricaoMovProdutoModalidade(obj.getTotalFinal()));
        }
        return UteisValidacao.emptyString(sb.toString()) && obj.getDescricaoMovProdutoModalidade() != null
                ? obj.getDescricaoMovProdutoModalidade() : sb;
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>MovProdutoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização
     * do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto.
     * Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj
     *            Objeto da classe <code>MovProdutoVO</code> que será alterada
     *            no banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão, restrição de acesso ou
     *                validação de dados.
     */
    public void alterar(MovProdutoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>MovProdutoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização
     * do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto.
     * Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj
     *            Objeto da classe <code>MovProdutoVO</code> que será alterada
     *            no banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão, restrição de acesso ou
     *                validação de dados.
     */
    @Override
    public void alterarSemCommit(MovProdutoVO obj) throws Exception {
        MovProdutoVO.validarDados(obj);
        //alterar(getIdEntidade());
        excluirClienteMensagemProdutoVencido(obj);
        obj.realizarUpperCaseDados();
        boolean alterarMovProdutoModalidades = !obj.getMovProdutoModalidades().isEmpty();
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE MovProduto set produto=?, contrato=?, pessoa=?, ");
        sql.append("empresa=?, descricao=?, quantidade=?, precoUnitario=?, ");
        sql.append("valorDesconto=?, totalFinal=?, dataLancamento=?, responsavelLancamento=?, ");
        sql.append("mesReferencia=?, anoReferencia=?, dataInicioVigencia=?, dataFinalVigencia=?, ");
        sql.append("apresentarMovProduto = ?  ,quitado = ?, situacao=?, naoGerarMensagem=?, movpagamentocc=?, lancamentoColetivo = ?, vendaavulsa=? ");
        if (alterarMovProdutoModalidades) {
            sql.append(", descricaoMovProdutoModalidade=? ");
        }
        sql.append(", multa = ?, juros = ? \n");
        sql.append(", valorFaturado = ?");
        sql.append(", codigooperacaofinanceira = ? ");
        sql.append(", jurosNaoRecebidos = ? ");
        sql.append(", multaNaoRecebida = ? ");
        sql.append(", numerocupomdesconto=? ");
        sql.append(", movProdutoBase=? ");
        sql.append(", renovavelAutomaticamente=?");
        sql.append(" WHERE codigo = ?");
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql.toString())) {
            int i = 1;
            if (obj.getProduto().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getProduto().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getContrato().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getContrato().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getPessoa().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getPessoa().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getEmpresa().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getEmpresa().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setString(i++, obj.getDescricao());
            sqlAlterar.setInt(i++, obj.getQuantidade());
            sqlAlterar.setDouble(i++, obj.getPrecoUnitario());
            sqlAlterar.setDouble(i++, obj.getValorDesconto());
            sqlAlterar.setDouble(i++, obj.getTotalFinal());
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            if (obj.getResponsavelLancamento().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getResponsavelLancamento().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setString(i++, obj.getMesReferencia());
            sqlAlterar.setInt(i++, obj.getAnoReferencia());
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
            sqlAlterar.setBoolean(i++, obj.getApresentarMovProduto());
            sqlAlterar.setBoolean(i++, obj.getQuitado());
            sqlAlterar.setString(i++, obj.getSituacao());
            sqlAlterar.setBoolean(i++, obj.getNaoGerarMensagem());
            sqlAlterar.setString(i++, obj.getMovpagamentocc());
            resolveFKNull(sqlAlterar, i++, obj.getLancamentoColetivo());
            resolveIntegerNull(sqlAlterar, i++, obj.getVendaAvulsa());
            if (alterarMovProdutoModalidades) {
                sqlAlterar.setString(i++, resolveDescricaoMovProdutoModalidade(obj).toString());
            }

            sqlAlterar.setDouble(i++, obj.getMulta());
            sqlAlterar.setDouble(i++, obj.getJuros());
            sqlAlterar.setDouble(i++, obj.getValorFaturado() != null ? obj.getValorFaturado() : obj.getTotalFinal());
            sqlAlterar.setString(i++, obj.getCodigoOperacaoFinanceira());
            sqlAlterar.setDouble(i++, obj.getJurosNaoRecebidos());
            sqlAlterar.setDouble(i++, obj.getMultaNaoRecebida());
            if ((obj.getNumeroCupomDesconto() != null) && (!obj.getNumeroCupomDesconto().trim().equals(""))) {
                sqlAlterar.setString(i++, obj.getNumeroCupomDesconto());
            } else {
                sqlAlterar.setNull(i++, Types.NULL);
            }
            resolveIntegerNull(sqlAlterar, i++, obj.getMovProdutoBase());
            sqlAlterar.setBoolean(i++, obj.getRenovavelAutomaticamente());

            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
        }
        // new MovProdutoParcela().alterarMovProdutoParcelas(obj.getCodigo(), obj.getMovProdutoParcelaVOs());

    }

    public void alterarSomenteSituacaoSemCommit(Integer codigo, String situacao) throws Exception {
        alterar(getIdEntidade());
        String sql = "UPDATE MovProduto set situacao=? WHERE ((codigo = ?))";
        int i = 1;
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(i++, situacao);
            sqlAlterar.setInt(i++, codigo);
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>MovProdutoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da
     * entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do
     * usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj
     *            Objeto da classe <code>MovProdutoVO</code> que será removido
     *            no banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(MovProdutoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>MovProdutoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da
     * entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do
     * usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj
     *            Objeto da classe <code>MovProdutoVO</code> que será removido
     *            no banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluirSemCommit(MovProdutoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM MovProduto WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }

    }

    public void excluirSemCommit(Integer codigo) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM MovProduto WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codigo.intValue());
            sqlExcluir.execute();
        }
        // new MovProdutoParcela().excluirMovProdutoParcelas(obj.getCodigo());

    }

    private boolean validarNFSeEmitida(NFSeEmitidaVO notaEmitida, boolean podeEstornar, List<NFSeEmitidaVO> notasEmitidas) throws Exception {
        if (notaEmitida != null && notaEmitida.getIdRps() != null) {
            LoteNFe loteNFeDAO = new LoteNFe(con);
            StatusNotaEnum statusNotaEnum = loteNFeDAO.retornarStatus(notaEmitida.getIdRps());
            loteNFeDAO = null;
            podeEstornar = statusNotaEnum.isPodeEstornar();
            notasEmitidas.add(notaEmitida);
        } else {
            if (notaEmitida != null) {
                podeEstornar = false;
            }
        }
        return podeEstornar;
    }

    private void estornarNotasFiscais(EstornoMovProdutoVO estornoMovProdutoVO) throws Exception {
        for (EstornoReciboVO estornoReciboVO : estornoMovProdutoVO.getListaEstornoRecibo()) {
            ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
            reciboPagamentoDAO.estornarNotasFiscais(estornoReciboVO, null);
            reciboPagamentoDAO = null;
        }
    }

    public void estornarMovProduto(EstornoMovProdutoVO obj, MovParcelaVO parcela, String key) throws Exception {
        estornarMovProduto(obj, parcela, key, true);
    }

    public void estornarMovProduto(EstornoMovProdutoVO obj, MovParcelaVO parcela, String key, boolean controlarTransacao) throws Exception {
        List<BoletoVO> listaBoletosCancelar = new ArrayList<>();
        try {
            if (controlarTransacao) {
                con.setAutoCommit(false);
            }
            estornarNotasFiscais(obj);

            Boleto boletoDAO = new Boleto(con);
            listaBoletosCancelar = boletoDAO.excluirBoletoMovProduto(obj);
            boletoDAO = null;

            List<LogVO> listaLog = obj.estornarMovProduto(key, obj.isEstornarOperadora(), con);
            if(!obj.getExiteOutroContratoPagouMinhaParcela()){
                if (parcela.getVendaAvulsaVO().getCodigo() != 0) {
                    VendaAvulsaVO vendaAvulsa = new VendaAvulsaVO();
                    vendaAvulsa.setCodigo(parcela.getVendaAvulsaVO().getCodigo().intValue());
                    AgendaEstudio agendaEstudioDAO = new AgendaEstudio(con);
                    agendaEstudioDAO.estornarAgendasPorVendaAvulsa(vendaAvulsa.getCodigo(), false);
                    agendaEstudioDAO = null;

                    VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
                    vendaAvulsaDAO.excluirSemCommit(vendaAvulsa);
                    vendaAvulsaDAO = null;
                } else if (parcela.getPersonal().getCodigo() != 0) {
                    ControleTaxaPersonal controleTaxaPersonalDAO = new ControleTaxaPersonal(con);
                    controleTaxaPersonalDAO.excluirSemCommit(parcela.getPersonal().getCodigo());
                    controleTaxaPersonalDAO = null;
                }
            }
            try {
                if (UteisValidacao.emptyNumber(obj.getClienteVO().getPessoa().getCodigo())){
                    Colaborador colaboradorDAO = new Colaborador(con);
                    Integer codigoColaborador = colaboradorDAO.consultarPorCodigoPessoa(obj.getMovProdutoVO().getPessoa().getCodigo(), 0, Uteis.NIVELMONTARDADOS_MINIMOS).getCodigo();
                    colaboradorDAO = null;
                    Iterator i = listaLog.iterator();
                    while (i.hasNext()) {
                        LogVO log = (LogVO) i.next();
                        if (log.getChavePrimaria().equals("0")){
                            log.setChavePrimaria(codigoColaborador.toString());
                            log.setNomeEntidade("COLABORADOR");
                        }
                    }
                    SuperControle.registrarLogObjetoVO(listaLog,obj.getMovProdutoVO().getPessoa().getCodigo());
                }else {
                    SuperControle.registrarLogObjetoVO(listaLog, obj.getClienteVO().getPessoa().getCodigo());
                }
            } catch (Exception e) {
                SuperControle.registrarLogErroObjetoVO("ESTORNOMOVPRODUTO", obj.getClienteVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ESTORNO MOV PRODUTO", obj.getUsuarioVO().getNome(),obj.getUsuarioVO().getUserOamd());
                e.printStackTrace();
            }
            if (controlarTransacao) {
                con.commit();
            }
        } catch (Exception e) {
            listaBoletosCancelar = new ArrayList<>();
            if (controlarTransacao) {
                con.rollback();
                con.setAutoCommit(true);
            }
            throw e;
        } finally {
            if (controlarTransacao) {
                con.setAutoCommit(true);
            }

            if (!UteisValidacao.emptyList(listaBoletosCancelar)) {
                Boleto boletoDAO = null;
                try {
                    boletoDAO = new Boleto(con);
                    boletoDAO.cancelarBoletos(listaBoletosCancelar, obj.getResponsavelEstorno(), "EstornoMovProduto");
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    boletoDAO = null;
                }
            }
        }
    }

    public void estornarMovProdutoSemCommit(EstornoMovProdutoVO obj, String key) throws Exception {
        try {
            List<LogVO> listaLog = obj.estornarMovProduto(key, obj.isEstornarOperadora(), this.con);
            try {
                SuperControle.registrarLogObjetoVO(listaLog, obj.getClienteVO().getPessoa().getCodigo());
            } catch (Exception e) {
                SuperControle.registrarLogErroObjetoVO("ESTORNOMOVPRODUTO", obj.getClienteVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ESTORNO MOV PRODUTO", obj.getUsuarioVO().getNome(),obj.getUsuarioVO().getUserOamd());
                e.printStackTrace();
            }
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarProdutoComValidadePorCodigoPessoa(Integer codigo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovProduto.* \n"
                + "FROM MovProduto,Produto \n"
                + "WHERE MovProduto.Produto = Produto.codigo \n"
                + "AND MovProduto.pessoa = " + codigo.intValue() + "\n"
                + "AND ((Produto.tipoVigencia = 'ID' OR Produto.tipoVigencia = 'VV') and (Produto.tipoproduto in ('SE','AT'))\n" +
                "       OR (Produto.tipoproduto IN ('"+TipoProduto.DESAFIO.getCodigo()+"')))\n"
                + "ORDER BY MovProduto.datafinalvigencia desc";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados, Integer empresa) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT MovProduto.* FROM MovProduto, Colaborador WHERE "
                    + "MovProduto.responsavelLancamento = Colaborador.codigo "
                    + "and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "ORDER BY Colaborador.situacao";
        } else {
            sqlStr = "SELECT MovProduto.* " + "FROM MovProduto, Colaborador "
                    + "WHERE MovProduto.responsavelLancamento = Colaborador.codigo "
                    + "AND MovProduto.empresa = " + empresa.intValue()
                    + "AND upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "ORDER BY Colaborador.situacao";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>String descricao</code>. Retorna os objetos,
     * com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso
     *            Indica se a aplicação deverá verificar se o usuário possui
     *            permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM MovProduto "
                    + "WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "ORDER BY descricao";
        } else {
            sqlStr = "SELECT * " + "FROM MovProduto "
                    + "WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "AND MovProduto.empresa = " + empresa.intValue() + " ORDER BY descricao";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public boolean consultarPorCodigoProduto(int produto) throws Exception {
        String sqlStr = "SELECT exists (select codigo FROM MovProduto "
                + "WHERE produto = " + produto + ") as existe";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>nome</code> da classe <code>Empresa</code> Faz
     * uso da operação <code>montarDadosConsulta</code> que realiza o trabalho
     * de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeEmpresa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovProduto.* FROM MovProduto, Empresa "
                + "WHERE MovProduto.empresa = Empresa.codigo "
                + "and upper( Empresa.nome ) like('" + valorConsulta.toUpperCase() + "%') "
                + "ORDER BY Empresa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>nome</code> da classe <code>Pessoa</code> Faz
     * uso da operação <code>montarDadosConsulta</code> que realiza o trabalho
     * de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomePessoa(String valorConsulta, int nivelMontarDados, Integer empresa) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT MovProduto.* FROM MovProduto, Pessoa "
                    + "WHERE MovProduto.pessoa = Pessoa.codigo "
                    + "and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "ORDER BY Pessoa.nome";
        } else {
            sqlStr = "SELECT MovProduto.* FROM MovProduto, Pessoa "
                    + "WHERE MovProduto.pessoa = Pessoa.codigo "
                    + "AND MovProduto.empresa = " + empresa.intValue()
                    + "AND upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%')"
                    + "ORDER BY Pessoa.nome ";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<MovProdutoVO> consultarPorCodigoPessoaParaHistoricoCompras(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovProduto.* "
                + "FROM MovProduto, Pessoa, Produto "
                + "WHERE MovProduto.pessoa = Pessoa.codigo "
                + "and Pessoa.codigo = " + valorConsulta
                + " and MovProduto.produto = Produto.codigo "
                + "and (Produto.tipoProduto <> 'DE' or Produto.tipoProduto <> 'DV' or Produto.tipoProduto <> 'QU' )  "
                +"\n and produto.categoriaproduto not in (SELECT codigo FROM categoriaproduto WHERE descricao LIKE 'CENTRAL%EVENTO%')"
                + "\n ORDER BY dataLancamento desc, codigo DESC";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Método usado para consultar movprodutos de vendas avulsas de consumidores
     * para montar a lista de historico de compras
     * @param nomeComprador
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public List consultarPorNomeConsumidorParaHistoricoCompras(String nomeComprador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT DISTINCT (MovProduto.*) FROM MovProduto "
                + "inner join produto on movproduto.produto = produto.codigo "
                + "inner join movprodutoparcela on movproduto.codigo = movprodutoparcela.movproduto "
                + "inner join movparcela on movprodutoparcela.movparcela = movparcela.codigo "
                + "inner join vendaavulsa on movparcela.vendaavulsa = vendaavulsa.codigo "
                + "where  (Produto.tipoProduto <> 'DE' or Produto.tipoProduto <> 'DV' or Produto.tipoProduto <> 'QU' ) "
                + "and vendaAvulsa.tipocomprador = 'CN' "
                + "and vendaavulsa.nomecomprador ilike '" + nomeComprador + "' and movproduto.pessoa is null "
                + "ORDER BY dataLancamento desc, codigo DESC; ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List consultarPorPessoaContratoParaHistoricoCompras(Integer valorConsulta, int contrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovProduto.* FROM MovProduto, Pessoa, Produto "
                + "WHERE MovProduto.pessoa = Pessoa.codigo "
                + "and Pessoa.codigo = " + valorConsulta
                + " and MovProduto.contrato = " + contrato
                + " and MovProduto.produto = Produto.codigo"
                + " and (Produto.tipoProduto <> 'DE' or Produto.tipoProduto <> 'DV' or Produto.tipoProduto <> 'QU' ) "
                + "ORDER BY dataLancamento desc, codigo DESC";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>codigo</code> da classe <code>Contrato</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoContrato(Integer valorConsulta, int nivelMontarDados, Integer empresa) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT MovProduto.* FROM MovProduto, Contrato "
                    + "WHERE MovProduto.contrato = Contrato.codigo "
                    + "and Contrato.codigo >= " + valorConsulta.intValue()
                    + " ORDER BY Contrato.codigo";
        } else {
            sqlStr = "SELECT MovProduto.* "
                    + "FROM MovProduto, Contrato "
                    + "WHERE MovProduto.contrato = Contrato.codigo "
                    + "AND MovProduto.empresa = " + empresa.intValue()
                    + " AND Contrato.codigo >= " + valorConsulta.intValue()
                    + " ORDER BY Contrato.codigo";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<MovProdutoVO> consultarPorCodigoContrato(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovProduto.* FROM MovProduto, Contrato "
                + "WHERE MovProduto.contrato = Contrato.codigo "
                + "and Contrato.codigo = " + valorConsulta.intValue()
                + " ORDER BY Contrato.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<MovProdutoVO> consultarEmAbertoPorContrato(Integer codigoContrato, int nivelMontarDados, boolean verificarAcesso) throws Exception {
        consultar(getIdEntidade(), verificarAcesso);
        String sqlStr = "SELECT MovProduto.* FROM MovProduto, Contrato "
                + "WHERE MovProduto.contrato = Contrato.codigo "
                + " AND Contrato.codigo = " + codigoContrato.intValue()
                + " AND MovProduto.situacao =  'EA' "
                + "ORDER BY Contrato.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(rs, nivelMontarDados, this.con);
            }
        }
    }

    public Integer consultarPorCodigoVendaAvulsaRetornaCodigo(Integer valorConsulta) throws Exception {
        String sqlStr = "SELECT MovProduto.codigo FROM MovProduto where movproduto.vendaavulsa = " +valorConsulta+" limit 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("codigo");
                }
            }
        }
        throw new ConsistirException("Feche a Tela e tente novamente!");
    }


    public List consultarPorCodigoContratoOrdenado(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = " SELECT * FROM movproduto WHERE movproduto.contrato = " + valorConsulta.intValue()
                + " ORDER BY anoreferencia, mesreferencia";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por consultar o primeiro registro do tipo PLANO de movproduto de um contrato,
     * útil no preenchimento das tags da impressão do contrato
     * <AUTHOR>
     * 14/03/2011
     * @param valorConsulta
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public MovProdutoVO consultarProdutoPlanoPorContrato(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        MovProdutoVO produto = new MovProdutoVO();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT MP.* FROM movproduto MP ");
        sql.append("INNER JOIN produto P ON MP.produto = P.codigo ");
        sql.append("WHERE P.tipoproduto LIKE 'PM' AND MP.contrato = ? ");
        sql.append("ORDER BY MP.totalfinal DESC LIMIT 2 ");
        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 1;
        dc.setInt(i, valorConsulta);
        try (ResultSet tabelaResultado = dc.executeQuery()) {
            if (tabelaResultado.next()) {
                produto = montarDados(tabelaResultado, nivelMontarDados, this.con);
                if (produto.getDescricao().contains("PRO-RATA") && tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados, this.con);
                } else {
                    return produto;
                }
            }
        }
        return produto;
    }

    public List<MovProdutoVO> consultarProdutoContrato(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("  *\n");
        sql.append("FROM movproduto\n");
        sql.append("  INNER JOIN produto\n");
        sql.append("    ON movproduto.produto = produto.codigo AND tipoproduto IN ('PE', 'SE', 'AA')\n");
        sql.append("WHERE contrato = ").append(valorConsulta).append(";");

        Declaracao dc = new Declaracao(sql.toString(), con);
        try (ResultSet tabelaResultado = dc.executeQuery()) {
            return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>String descricao</code>. Retorna os objetos,
     * com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso
     *            Indica se a aplicação deverá verificar se o usuário possui
     *            permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorPeriodoLancamentoETipoProduto(Date dataInicio, Date dataTermino, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);

        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT * FROM MovProduto ");
        sb.append(" left join Produto on MovProduto.produto = Produto.codigo");
        sb.append(" WHERE Produto.tipoProduto = '").append(tipoProduto).append("'");
        sb.append(" AND dataLancamento > '").append(Uteis.getDataJDBC(dataInicio)).append(" 00:00:00'");
        sb.append(" AND dataLancamento < '").append(Uteis.getDataJDBC(dataTermino)).append(" 23:59:59'");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorPeriodoLancamentoEEmpresa(Date dataInicio, Date dataTermino, int idEmpresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {

        consultar(getIdEntidade(), controlarAcesso);

        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT * FROM MovProduto ");
        sb.append(" LEFT JOIN Produto ON MovProduto.produto = Produto.codigo ");
        sb.append(" WHERE MovProduto.empresa = ").append(idEmpresa).append(" "); // Filtro pela empresa
        sb.append(" AND dataLancamento > '").append(Uteis.getDataJDBC(dataInicio)).append(" 00:00:00' ");
        sb.append(" AND dataLancamento < '").append(Uteis.getDataJDBC(dataTermino)).append(" 23:59:59' ");
        sb.append(" AND MovProduto.situacao = 'PG' "); // Filtro pela situaþÒo
        sb.append(" AND MovProduto.valorFaturado != 0 "); // Filtro pelo valor faturado


        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }


    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>codigo</code> da classe <code>Contrato</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoContratoTipoProduto(Integer valorConsulta, String tipoProduto, int nivelMontarDados) throws Exception {
        return consultarPorCodigoContratoTipoProduto(valorConsulta, tipoProduto, true, nivelMontarDados);
    }

    public List consultarPorCodigoContratoTipoProduto(Integer valorConsulta, String tipoProduto, boolean incluirParcelasCanceladas, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT mpd.*\n");
        sql.append("FROM movproduto mpd\n");
        sql.append("INNER JOIN contrato con ON con.codigo = mpd.contrato\n");
        sql.append("INNER JOIN produto prd ON prd.codigo = mpd.produto\n");
        sql.append("AND prd.tipoproduto = '").append(tipoProduto.toUpperCase()).append("'\n");
        sql.append("AND con.codigo = ").append(valorConsulta.intValue()).append("\n");
        if (!incluirParcelasCanceladas) {
            sql.append("AND mpd.situacao <> 'CA'\n");
        }
        sql.append("ORDER BY mpd.anoreferencia, mpd.mesreferencia\n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>codigo</code> da classe <code>Contrato</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<MovProdutoVO> consultarPorCodigoParcela(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT distinct(MovProduto.*) FROM MovProduto , MovParcela , MovProdutoParcela "
                + "WHERE MovProdutoParcela.MovProduto = MovProduto.codigo "
                + "and MovProdutoParcela.MovParcela = MovParcela.codigo "
                + "and MovParcela.codigo = " + valorConsulta.intValue();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }



    public List consultarPorCodigoParcelaAgrupado(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT distinct(MovProduto.*) FROM MovProduto , MovParcela , MovProdutoParcela "
                + "WHERE MovProdutoParcela.MovProduto = MovProduto.codigo "
                + "and MovProdutoParcela.MovParcela = MovParcela.codigo "
                + "and MovParcela.codigo = " + valorConsulta.intValue();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }


    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>descricao</code> da classe
     * <code>Produto</code> Faz uso da operação <code>montarDadosConsulta</code>
     * que realiza o trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoProduto(String valorConsulta, int nivelMontarDados, Integer empresa) throws Exception {
        String sqlStr = "";
        consultar(getIdEntidade(), true);
        if (empresa == 0) {
            sqlStr = "SELECT MovProduto.* FROM MovProduto, Produto "
                    + "WHERE MovProduto.produto = Produto.codigo "
                    + "and upper( Produto.descricao ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "ORDER BY Produto.descricao";

        } else {
            sqlStr = "SELECT MovProduto.* "
                    + "FROM MovProduto, Produto "
                    + "WHERE MovProduto.produto = Produto.codigo "
                    + "AND MovProduto.empresa = " + empresa.intValue()
                    + "AND upper( Produto.descricao ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "ORDER BY Produto.descricao";

        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>MovProduto</code> através
     * do valor do atributo <code>Integer codigo</code>. Retorna os objetos com
     * valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso
     *            Indica se a aplicação deverá verificar se o usuário possui
     *            permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>MovProdutoVO</code>
     *         resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        String sqlStr = "";
        consultar(getIdEntidade(), controlarAcesso);
        if (empresa == 0) {
            sqlStr = "SELECT * FROM MovProduto "
                    + "WHERE codigo >= " + valorConsulta.intValue()
                    + " ORDER BY codigo";
        } else {
            sqlStr = "SELECT *" + " FROM MovProduto "
                    + " WHERE MovProduto.codigo >= " + valorConsulta.intValue()
                    + " AND MovProduto.empresa = " + empresa.intValue()
                    + " ORDER BY codigo";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por excluir todos os objetos da
     * <code>HistoricoContratoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe
     * <code>HistoricoContrato</code>.
     *
     * @param contrato campo chave para exclusão dos objetos no BD.
     * @exception Exception
     */
    public void excluirMovProdutoContratos(Integer contrato) throws Exception {
        //MovProduto.excluir(getIdEntidade());
        String sql = "DELETE FROM MovProduto WHERE (contrato = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, contrato);
            sqlExcluir.execute();
        }
    }

    public void excluirMovProdutoVendaAvulsa(Integer vendaAvulsa) throws Exception {
        String sql = "DELETE FROM MovProduto WHERE vendaavulsa = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, vendaAvulsa);
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da
     * <code>HistoricoContratoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirHistoricoContratos</code> e
     * <code>incluirHistoricoContratos</code> disponíveis na classe
     * <code>HistoricoContrato</code>.
     *
     * @param objetos
     *            List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception
     *                Erro de conexão com o BD ou restrição de acesso a esta
     *                operação.
     */
    public void alterarMovProdutoContratos(ContratoVO contrato, List objetos) throws Exception {
        excluirMovProdutoContratos(contrato.getCodigo().intValue());
        incluirMovProdutoContratos(contrato, objetos);
    }

    /**
     * Operação responsável por incluir objetos da
     * <code>HistoricoContratoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal
     * <code>contrato.Contrato</code> através do atributo de vínculo.
     *
     * @param objetos
     *            List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception
     *                Erro de conexão com o BD ou restrição de acesso a esta
     *                operação.
     */
    public void incluirMovProdutoContratos(ContratoVO contratoPrm, List objetos) throws Exception {
        contratoPrm.gerarEncerramentoContratoDia(false, contratoPrm, objetos);

        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            MovProdutoVO obj = (MovProdutoVO) e.next();
            Integer codigoMovProdutoAntesInclusao = obj.getCodigo();
            obj.setContrato(contratoPrm);
            incluirSemCommit(obj);

            if(contratoPrm.isGerarParcelasAPartirDeMovParcelaVOs()){// contratos transferidos de empresa ou de pessoa

                if(contratoPrm.getMovParcelaVOs().size() == 1 && contratoPrm.getMovParcelaVOs().get(0).getDescricao().contains("PARCELA TRANSF.")){ //contratos transferidos de pessoa
                    MovProdutoParcelaVO novo = new MovProdutoParcelaVO();
                    novo.setMovProduto(obj.getCodigo());
                    novo.setValorPago(obj.getTotalFinal());
                    contratoPrm.getMovParcelaVOs().get(0).getMovProdutoParcelaVOs().add(novo);
                } else { //contratos transferidos de empresa
                    // Atribuir os códigos de produtos incluidos a lista de movprodutoparcela que serão
                    // utilizados para criar o relacionamento entre produtos e parcelas durante a inclusão de parcelas.
                    for (MovParcelaVO parcela : contratoPrm.getMovParcelaVOs()) {
                        for (MovProdutoParcelaVO produtoParcela : parcela.getMovProdutoParcelaVOs()) {
                            if (produtoParcela.getMovProduto().equals(codigoMovProdutoAntesInclusao)) {
                                produtoParcela.setMovProduto(obj.getCodigo());
                            }
                        }
                    }
                }
            }
        }
    }

    public void cancelarMovProdutoFuturos(ContratoVO contrato){
        StringBuffer sql  = new StringBuffer();
        sql.append("update movproduto set situacao = 'CA' WHERE codigo in (\n" +
                "select  codigo from movproduto \n" +
                "where to_date(mesreferencia, 'MM/YYYY') >= to_date(to_char(now(), 'MM/YYYY'), 'MM/YYYY')\n" +
                "and contrato = "+contrato.getCodigo()+"\n" +
                ")");

        try{
            try (Statement stm = con.createStatement()) {
                stm.executeQuery(sql.toString());
            }
        }catch (Exception e){
            Logger.getLogger(MovProduto.class.getName()).log(Level.SEVERE, e.getMessage(), e);
        }
    }

    public void modificarSituacaoCancelado(List movProduto) {
        Iterator e = movProduto.iterator();
        while (e.hasNext()) {
            MovProdutoVO obj = (MovProdutoVO) e.next();
            obj.setSituacao("CA");
            obj.setQuitado(true);
        }
    }

    public void modificarSituacaoMovProduto(List objetos) {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            MovProdutoVO obj = (MovProdutoVO) e.next();
            obj.setSituacao("PG");
            obj.setQuitado(true);
        }
    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco,
     * de acordo com os filtros passados
     *
     * Autor: Carla Pereira
     * Criado em 25/02/2011
     */
    @SuppressWarnings("unchecked")
    public List consultarPaginado(ControleConsulta controleConsulta, boolean controlarAcesso, int nivelMontarDados, ConfPaginacao confPaginacao) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);

        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //sql principal
        StringBuffer sqlSelect = new StringBuffer("SELECT movproduto.*,empresa.nome as nomeEmpresa,"
                + " pessoa.nome as nomePessoa,"
                + " produto.descricao as descricaoProduto,"
                + " usuario.nome as nomeResponsavelLancamento"
                + " FROM movproduto inner join pessoa on movproduto.pessoa = pessoa.codigo"
                + " inner join produto on produto.codigo = movproduto.produto"
                + " inner join empresa on empresa.codigo = movproduto.empresa"
                + " inner join usuario on usuario.codigo = movproduto.responsavellancamento ");


        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);

        //a utilização dos filtros deve ser feita utilizando confPaginacao.getSqlFiltro()
        filtros(controleConsulta, confPaginacao.getSqlFiltro());
        if (!"".equals(confPaginacao.getSqlFiltro().toString())) {
            sqlSelect.append(" WHERE ");
            sqlSelect.append(confPaginacao.getSqlFiltro().toString());
        }

        //concatena ordenações
        ordernacao(controleConsulta, sqlSelect);

        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlSelect);

        //seta os valores dos filtros
        filtrosValoresStatement(controleConsulta, confPaginacao.getStm());

        //4 - REALIZA A CONSULTA COM PAGINACAO
        try (ResultSet tabelaResultado = confPaginacao.consultaPaginada()) {
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
        }
    }

    /**
     * Autor: Carla Pereira
     * Criado em 25/02/2011
     */
    private void filtrosValoresStatement(ControleConsulta controleConsulta, PreparedStatementPersonalizado stm) throws SQLException {
        int i = 0;

        if (controleConsulta != null && controleConsulta.getCampoConsulta() != null && !"".equals(controleConsulta.getCampoConsulta())) {
            if (controleConsulta.getValorConsulta() != null && !"".equals(controleConsulta.getValorConsulta().trim())) {
                if (controleConsulta.getCampoConsulta().equals("codigo")) {
                    stm.setInt(i++, Integer.parseInt(controleConsulta.getValorConsulta()));
                } else if (controleConsulta.getCampoConsulta().equals("nomePessoa")) {
                    stm.setString(i++, controleConsulta.getValorConsulta() + "%");
                } else if (controleConsulta.getCampoConsulta().equals("descricaoProduto")) {
                    stm.setString(i++, controleConsulta.getValorConsulta() + "%");
                } else if (controleConsulta.getCampoConsulta().equals("codigoContrato")) {
                    stm.setInt(i++, Integer.parseInt(controleConsulta.getValorConsulta()));
                } else if (controleConsulta.getCampoConsulta().equals("nomeEmpresa")) {
                    stm.setString(i++, "%" + controleConsulta.getValorConsulta() + "%");
                } else if (controleConsulta.getCampoConsulta().equals("descricao")) {
                    stm.setString(i++, controleConsulta.getValorConsulta() + "%");
                }
            }
        }
    }

    /**
     * Autor: Carla Pereira
     * Criado em 25/02/2011
     */
    private void ordernacao(ControleConsulta controleConsulta, StringBuffer sql) {
        if (controleConsulta != null && controleConsulta.getCampoConsulta() != null && !"".equals(controleConsulta.getCampoConsulta())) {
            if (controleConsulta.getCampoConsulta().equals("codigo")) {
                sql.append(" ORDER BY movproduto.codigo desc ");
            } else if (controleConsulta.getCampoConsulta().equals("nomePessoa")) {
                sql.append(" ORDER BY pessoa.nome ");
            } else if (controleConsulta.getCampoConsulta().equals("descricaoProduto")) {
                sql.append(" ORDER BY produto.descricao ");
            } else if (controleConsulta.getCampoConsulta().equals("codigoContrato")) {
                sql.append(" ORDER BY movproduto.contrato ");
            } else if (controleConsulta.getCampoConsulta().equals("nomeEmpresa")) {
                sql.append(" ORDER BY empresa.nome ");
            } else if (controleConsulta.getCampoConsulta().equals("descricao")) {
                sql.append(" ORDER BY movproduto.descricao ");
            } else {
                sql.append(" ORDER BY movproduto.codigo desc ");
            }
        }
    }

    /**
     * Autor: Carla Pereira
     * Criado em 25/02/2011
     */
    private void filtros(ControleConsulta controleConsulta, StringBuffer sqlFiltro) {
        if (controleConsulta != null && controleConsulta.getCampoConsulta() != null && !"".equals(controleConsulta.getCampoConsulta())) {
            if (controleConsulta.getValorConsulta() != null && !"".equals(controleConsulta.getValorConsulta().trim())) {

                if (controleConsulta.getCampoConsulta().equals("codigo")) {
                    sqlFiltro.append(" movProduto.codigo = ? ");
                } else if (controleConsulta.getCampoConsulta().equals("nomePessoa")) {
                    sqlFiltro.append(" pessoa.nome ilike ? ");
                } else if (controleConsulta.getCampoConsulta().equals("descricaoProduto")) {
                    sqlFiltro.append(" produto.descricao ilike ? ");
                } else if (controleConsulta.getCampoConsulta().equals("codigoContrato")) {
                    sqlFiltro.append(" movproduto.contrato = ? ");
                } else if (controleConsulta.getCampoConsulta().equals("nomeEmpresa")) {
                    sqlFiltro.append(" empresa.nome ilike ?");
                } else if (controleConsulta.getCampoConsulta().equals("descricao")) {
                    sqlFiltro.append(" movproduto.descricao ilike ? ");
                }

            }
        }
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>MovProdutoParcelaVO</code> no Hashtable
     * <code>MovProdutoParcelas</code>.
     * Neste Hashtable são mantidos todos os objetos de MovProdutoParcela de uma
     * determinada MovProduto.
     *
     * @param obj
     *            Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjMovProdutoParcelas(MovProdutoParcelaVO obj) throws Exception {
        getMovProdutoParcelas().put(obj.getMovProduto() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>MovProdutoParcelaVO</code> do Hashtable
     * <code>MovProdutoParcelas</code>.
     * Neste Hashtable são mantidos todos os objetos de MovProdutoParcela de uma
     * determinada MovProduto.
     *
     * @param MovProduto
     *            Atributo da classe <code>MovProdutoParcelaVO</code> utilizado
     *            como apelido (key) no Hashtable.
     */
    public void excluirObjMovProdutoParcelas(Integer MovProduto) throws Exception {
        getMovProdutoParcelas().remove(MovProduto + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>MovProdutoVO</code> através de sua chave primária.
     *
     * @exception Exception
     *                Caso haja problemas de conexão ou localização do objeto
     *                procurado.
     */
    public MovProdutoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM MovProduto WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( MovProduto ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<FaturamentoSinteticoProdutoVO> consultarProdutosMovimentadoParaFaturaPeriodo(Date dataInicial, Date dataFinal, List<String> tipoProduto,
                                                                                             int operador, int colaborador, Integer empresa,
                                                                                             String agrupamentoPlano, boolean controlarAcesso,
                                                                                             boolean somenteFaturamentoRecebido,
                                                                                             int nivelMontarDados, boolean bolsa,
                                                                                             boolean desconsiderarProdutoCancelado,
                                                                                             boolean somenteContratoVendaAvulsaMesmoMesReferencia,
                                                                                             boolean evento) throws Exception {
        StringBuilder sb = new StringBuilder();

        List<FaturamentoSinteticoProdutoVO> listaProduto = new ArrayList<FaturamentoSinteticoProdutoVO>();

        if (tipoProduto.contains("PM")) {
            if (agrupamentoPlano.equalsIgnoreCase("nome") || agrupamentoPlano.equalsIgnoreCase("tipoContrato")) {
                sb.append("select produto.tipoproduto, contrato.plano, produto.codigo,  \n");
                sb.append("pla.descricao as movimentacaoDescricao \n");
            } else if (agrupamentoPlano.equalsIgnoreCase("duracao")) {
                sb.append("select contratoDuracao.numeromeses, produto.tipoproduto \n");
            } else {
                sb.append("select contratoDuracao.numeromeses as numeromeses, produto.codigo, produto.tipoproduto, contrato.plano, \n");
                sb.append("pla.descricao as movimentacaoDescricao \n");
            }
            sb.append(" from contrato ");
            sb.append(" left join contratoDuracao on contrato.codigo = contratoDuracao.contrato \n");
            sb.append(" left join movProduto on movproduto.contrato = contrato.codigo \n");
            sb.append(" left join vendaavulsa on vendaavulsa.codigo = movProduto.vendaavulsa \n");
            sb.append(" inner join plano pla on pla.codigo = contrato.plano\n");
            if (somenteFaturamentoRecebido) {
                sb.append(" INNER JOIN movprodutoparcela mpp ON mpp.movproduto = movproduto.codigo AND mpp.recibopagamento IS NOT NULL \n");
                sb.append(" INNER JOIN recibopagamento rp ON mpp.recibopagamento = rp.codigo \n");
            }
            sb.append(" left join produto on movProduto.produto = produto.codigo \n");
            if(!UteisValidacao.emptyNumber(operador)) {
                sb.append(" INNER JOIN usuario on movProduto.responsavellancamento = usuario.codigo ").append("\n");
            }
            if(!UteisValidacao.emptyNumber(colaborador)) {
                sb.append("INNER JOIN cliente on movProduto.pessoa = cliente.pessoa\n");
                sb.append("LEFT JOIN vinculo on cliente.codigo = vinculo.cliente AND vinculo.tipovinculo = 'CO' \n");
            }

            if (somenteFaturamentoRecebido) {
                sb.append(" where rp.data >= '").append(Uteis.getDataJDBC(dataInicial)).append(" 00:00:00.000' \n");
                sb.append(" and rp.data <= '").append(Uteis.getDataJDBC(dataFinal)).append(" 23:59:59.999' \n");
            }else{
                sb.append(" where movProduto.datalancamento >= '").append(Uteis.getDataJDBC(dataInicial)).append(" 00:00:00.000' \n");
                sb.append(" and movProduto.datalancamento <= '").append(Uteis.getDataJDBC(dataFinal)).append(" 23:59:59.999' \n");
            }

            sb.append(adicionarTipoProdutoSQL(tipoProduto, " produto.tipoproduto = '"));
            sb.append(" and movproduto.movpagamentocc is null \n");
            sb.append(" and movproduto.situacao <> 'CA' \n");
            if (!UteisValidacao.emptyNumber(empresa)) {
                sb.append(" and movProduto.empresa = ").append(empresa).append(" ");
            }

            if(!UteisValidacao.emptyNumber(operador)){
                sb.append(" AND usuario.colaborador = ").append(operador).append(" \n");
            }
            if (bolsa){
                sb.append(" and contrato.bolsa = true ");
            }
            if(!UteisValidacao.emptyNumber(colaborador)){
                sb.append(" AND vinculo.colaborador  = ").append(colaborador);
            }

            if (desconsiderarProdutoCancelado) {
                sb.append(" and movProduto.situacao not in ('CA') ");
            }

            if (somenteContratoVendaAvulsaMesmoMesReferencia) {
                sb.append(" and (");
                sb.append(" (movProduto.datalancamento::date = vendaavulsa.dataregistro::date) ");
                sb.append(" OR ");
                sb.append(" (movProduto.datalancamento::date = contrato.datalancamento::date) ");
                sb.append(" ) ");
            }
            if (agrupamentoPlano.equalsIgnoreCase("duracao")) {
                sb.append(" group by contratoDuracao.numeromeses,produto.tipoproduto");
                sb.append(" order by contratoDuracao.numeromeses ");
            } else {
                 sb.append(" and movProduto.descricao not like 'PLANO TRANSFERIDO -%' ");
                 if (agrupamentoPlano.equalsIgnoreCase("nome") || agrupamentoPlano.equalsIgnoreCase("tipoContrato")) {
                     sb.append(" group by movimentacaoDescricao, produto.tipoproduto,  plano, produto.codigo");
                 } else {
                     sb.append(" group by movimentacaoDescricao, contratoDuracao.numeromeses, produto.codigo,produto.tipoproduto, plano");
                 }
                sb.append(" UNION ALL ");
                if (agrupamentoPlano.equalsIgnoreCase("nome") || agrupamentoPlano.equalsIgnoreCase("tipoContrato")) {
                    sb.append("select produto.tipoproduto, 0 as plano, produto.codigo,  \n");
                    sb.append("'PLANO TRANSFERIDO' as movimentacaoDescricao \n");
                } else {
                    sb.append("select contratoDuracao.numeromeses as numeromeses, produto.codigo, produto.tipoproduto, 0 as plano, \n");
                    sb.append("'PLANO TRANSFERIDO' as movimentacaoDescricao \n");
                }
                sb.append(" from contrato ");
                sb.append(" left join contratoDuracao on contrato.codigo = contratoDuracao.contrato \n");
                sb.append(" left join movProduto on movproduto.contrato = contrato.codigo\n");
                sb.append(" inner join plano pla on pla.codigo = contrato.plano\n");
                if (somenteFaturamentoRecebido) {
                    sb.append(" INNER JOIN movprodutoparcela mpp ON mpp.movproduto = movproduto.codigo AND mpp.recibopagamento IS NOT NULL \n");
                    sb.append(" INNER JOIN recibopagamento rp ON mpp.recibopagamento = rp.codigo \n");
                }
                sb.append(" left join produto on movProduto.produto = produto.codigo \n");
                if(!UteisValidacao.emptyNumber(operador)) {
                    sb.append(" INNER JOIN usuario on movProduto.responsavellancamento = usuario.codigo ").append("\n");
                }
                if(!UteisValidacao.emptyNumber(colaborador)) {
                    sb.append("INNER JOIN cliente on movProduto.pessoa = cliente.pessoa\n");
                    sb.append("LEFT JOIN vinculo on cliente.codigo = vinculo.cliente AND vinculo.tipovinculo = 'CO' \n");
                }
                if (somenteFaturamentoRecebido) {
                    sb.append(" where rp.data >= '").append(Uteis.getDataJDBC(dataInicial)).append(" 00:00:00.000' \n");
                    sb.append(" and rp.data <= '").append(Uteis.getDataJDBC(dataFinal)).append(" 23:59:59.999' \n");
                }else{
                    sb.append(" where movProduto.datalancamento >= '").append(Uteis.getDataJDBC(dataInicial)).append(" 00:00:00.000' \n");
                    sb.append(" and movProduto.datalancamento <= '").append(Uteis.getDataJDBC(dataFinal)).append(" 23:59:59.999' \n");
                }

                sb.append(adicionarTipoProdutoSQL(tipoProduto, " produto.tipoproduto = '"));
                sb.append(" and movproduto.movpagamentocc is null \n");
                sb.append(" and movproduto.situacao <> 'CA' \n");
                if (!UteisValidacao.emptyNumber(empresa)) {
                    sb.append(" and movProduto.empresa = ").append(empresa).append(" ");
                }

                if(!UteisValidacao.emptyNumber(operador)){
                    sb.append(" AND usuario.colaborador = ").append(operador).append(" \n");
                }
                if (bolsa){
                    sb.append(" and contrato.bolsa = true ");
                }
                if(!UteisValidacao.emptyNumber(colaborador)){
                    sb.append(" AND vinculo.colaborador  = ").append(colaborador);
                }
                sb.append(" and movProduto.descricao like 'PLANO TRANSFERIDO -%'");
                if (agrupamentoPlano.equalsIgnoreCase("nome") || agrupamentoPlano.equalsIgnoreCase("tipoContrato")) {
                    sb.append(" group by produto.tipoproduto, produto.codigo");
                    sb.append(" order by movimentacaoDescricao ");
                } else {
                    sb.append(" group by  contratoDuracao.numeromeses, produto.codigo,produto.tipoproduto");
                    sb.append(" order by movimentacaoDescricao, numeromeses ");
                }
            }

            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {

                    while (tabelaResultado.next()) {
                        FaturamentoSinteticoProdutoVO obj = new FaturamentoSinteticoProdutoVO();
                        if (agrupamentoPlano.equalsIgnoreCase("nome") || agrupamentoPlano.equalsIgnoreCase("tipoContrato")) {
                            obj.setPlano(tabelaResultado.getInt("plano"));
                            obj.setDescricao(tabelaResultado.getString("movimentacaoDescricao") + " - cod. " + obj.getPlano());
                            obj.setNomePlano(tabelaResultado.getString("movimentacaoDescricao"));
                            obj.getProduto().setCodigo(tabelaResultado.getInt("codigo"));
                        } else if (agrupamentoPlano.equalsIgnoreCase("nomeDuracao")) {
                            obj.setDuracao(tabelaResultado.getInt("numeromeses"));
                            obj.setPlano(tabelaResultado.getInt("plano"));
                            obj.setDescricao(tabelaResultado.getString("movimentacaoDescricao") + " - " + obj.getDuracao().toString()+ " - cod. " + obj.getPlano());
                            obj.setNomePlano(tabelaResultado.getString("movimentacaoDescricao"));
                            obj.getProduto().setCodigo(tabelaResultado.getInt("codigo"));
                        } else if (agrupamentoPlano.equalsIgnoreCase("duracao")) {
                            obj.setDuracao(tabelaResultado.getInt("numeromeses"));
                            obj.setDescricao(obj.getDuracao().toString() + (obj.getDuracao() == 1 ? " mês " : " meses"));
                        }

                        obj.getProduto().setTipoProduto(tabelaResultado.getString("tipoproduto"));
                        boolean existe = false;
                        for (FaturamentoSinteticoProdutoVO fspvo : listaProduto) {
                            if (fspvo.getDescricao().equals(obj.getDescricao())) {
                                existe = true;
                                break;
                            }
                        }
                        if (!existe) {
                            listaProduto.add(obj);
                        }
                    }
                }
            }
        } else {

            consultarProdutosParaFaturamento(dataInicial, dataFinal, tipoProduto, operador, colaborador, empresa, listaProduto, false, somenteFaturamentoRecebido, desconsiderarProdutoCancelado, somenteContratoVendaAvulsaMesmoMesReferencia,evento);
            if (tipoProduto.isEmpty()) {
                consultarProdutosParaFaturamento(dataInicial, dataFinal, tipoProduto, operador, colaborador, empresa, listaProduto, true, somenteFaturamentoRecebido, desconsiderarProdutoCancelado, somenteContratoVendaAvulsaMesmoMesReferencia,evento);
            }
        }
        return listaProduto;
    }

    /**
     * <AUTHOR> Alcides
     * 15/08/2013
     */
    private void consultarProdutosParaFaturamento(Date dataInicial, Date dataFinal, List<String> tipoProduto, int operador,
                                                  int colaborador, Integer empresa, List<FaturamentoSinteticoProdutoVO> listaProduto, boolean produtoPlano,
                                                  boolean somenteFaturamentoRecebido, boolean desconsiderarProdutoCancelado, boolean somenteContratoVendaAvulsaMesmoMesReferencia,boolean evento) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("select produto.codigo, produto.descricao");
        sb.append(", produto.tipoproduto,  produto.valorfinal  ");
        sb.append(" from movProduto ");
        sb.append(" left join produto on movProduto.produto = produto.codigo ");
        sb.append(" left join contrato on movProduto.contrato = contrato.codigo ");
        sb.append(" left join vendaavulsa on movProduto.vendaavulsa = vendaavulsa.codigo ");
        if (operador != 0) {
            sb.append(" inner join usuario on usuario.colaborador = ").append(operador).append(" ");
        }
        if (colaborador != 0) {
            sb.append(" inner join cliente on movProduto.pessoa = cliente.pessoa ");
            sb.append(" left join vinculo on cliente.codigo = vinculo.cliente AND vinculo.tipovinculo = 'CO' ");
            sb.append(" and vinculo.colaborador = ").append(colaborador).append(" ");
        }
        sb.append(" where movProduto.datalancamento >= '").append(Uteis.getDataJDBC(dataInicial)).append(" 00:00:00.000' ");
        if (!somenteFaturamentoRecebido) {
            sb.append(" and movproduto.movpagamentocc is null ");
        }
        sb.append(" and movProduto.datalancamento <= '").append(Uteis.getDataJDBC(dataFinal)).append(" 23:59:59.999' ");
        if (tipoProduto.isEmpty()) {
            if (somenteFaturamentoRecebido) {
                sb.append(produtoPlano ? "and produto.tipoproduto = 'PM'" : (" and produto.tipoproduto not in (" + PRODUTOS_IGNORADOS_FATURAMENTO_RECEBIDO + ", 'PM') "));
            } else {
                sb.append(produtoPlano ? "and produto.tipoproduto = 'PM'" : (" and produto.tipoproduto not in (" + PRODUTOS_IGNORADOS + ", 'PM') "));
            }
        } else {
             sb.append(adicionarTipoProdutoSQL(tipoProduto, " produto.tipoproduto = '"));
             if (evento){
                 sb.append(" and movproduto.descricao ilike '%- evento%' ");
             }
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sb.append(" and movProduto.empresa = ").append(empresa).append(" ");
        }
        if (operador != 0) {
            sb.append(" and movProduto.responsavellancamento = usuario.codigo ");
        }

        if (desconsiderarProdutoCancelado) {
            sb.append(" and movProduto.situacao not in ('CA') ");
        }

        if (somenteContratoVendaAvulsaMesmoMesReferencia) {
            sb.append(" and (");
            sb.append(" (movProduto.datalancamento::date = vendaavulsa.dataregistro::date) ");
            sb.append(" OR ");
            sb.append(" (movProduto.datalancamento::date = contrato.datalancamento::date) ");
            sb.append(" ) ");
        }

        sb.append(" group by produto.codigo, produto.descricao");
        sb.append(", produto.tipoproduto,  produto.valorfinal ");
        sb.append(" order by produto.descricao");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                while (tabelaResultado.next()) {
                    FaturamentoSinteticoProdutoVO obj = new FaturamentoSinteticoProdutoVO();
                    obj.getProduto().setCodigo(tabelaResultado.getInt("codigo"));
                    obj.getProduto().setDescricao(tabelaResultado.getString("descricao"));
                    obj.getProduto().setTipoProduto(tabelaResultado.getString("tipoproduto"));
                    obj.getProduto().setValorFinal(tabelaResultado.getDouble("valorFinal"));
                    obj.setDescricao(obj.getProduto().getDescricao());
                    obj.setPlano(0);
                    obj.setDuracao(0);
                    listaProduto.add(obj);
                }
            }
        }
    }

    public FaturamentoSinteticoProdutoMesVO consultarFaturamentoSinteticoResumoPessoa(Date dataInicial, Date dataFinal,
                                                                                      List<String> tipoProduto, int operador, int colaborador, Integer empresa,
                                                                                      Integer duracao, Integer plano, Integer produto,
                                                                                      String nomePlano, String nomeProduto, String descricaoProduto,
                                                                                      boolean somenteFaturamentoRecebido, boolean bolsa,
                                                                                      boolean desconsiderarProdutoCancelado, boolean somenteContratoVendaAvulsaMesmoMesReferencia, EmpresaVO empresaLogado, boolean evento) throws Exception {
        StringBuilder sb = new StringBuilder();
        boolean tipoDiariaAulaPersonal = existeTipoProdutoDiariaAulaPersonal(tipoProduto);
        sb.append(" select ");
        sb.append(" pessoa.codigo as pessoa_codigo, movproduto.vendaavulsa,");
        sb.append(" vendaavulsa.nomeComprador, vendaavulsa.tipocomprador,");
        sb.append(" contrato.codigo as contrato_codigo, contrato.situacaoContrato as situacaoContrato,\n");
        sb.append(" empresa.nome as nomeEmpresa,\n");
        sb.append(" produto.descricao as nomeProduto, SUM(movproduto.quantidade) qtdProduto,");
        sb.append(" movProduto.datalancamento::date as movProduto_datalancamento, produto.tipoProduto as tipoProduto ,responsavel.nome as nomeResponsavel,");
        if(tipoDiariaAulaPersonal){
            sb.append(" dp.aulaavulsadiaria,dp.personal, dp.modalidadeDiaria,");
        }
        if (somenteFaturamentoRecebido) {
            sb.append(" sum (mpp.valorpago) as total ");
        } else {
            sb.append(" sum (movProduto.valorFaturado) as total ");
        }
        sb.append(" from movProduto ");
        if (somenteFaturamentoRecebido){
            sb.append(" inner join movprodutoparcela mpp ON mpp.movproduto = movproduto.codigo AND mpp.recibopagamento IS NOT NULL ");
        }
        sb.append(" left join produto on movProduto.produto = produto.codigo ");
        sb.append(" left join pessoa on movProduto.pessoa = pessoa.codigo ");
        sb.append(" left join vendaavulsa on movProduto.vendaavulsa = vendaavulsa.codigo ");
        sb.append(" left join empresa on empresa.codigo = movproduto.empresa ");

        if (bolsa && tipoProduto.get(0).equals("PM")) {
            sb.append(" inner join contrato on movProduto.contrato = contrato.codigo and contrato.bolsa = true ");
        } else {
            sb.append(" left join contrato on movProduto.contrato = contrato.codigo ");
        }
        if(tipoDiariaAulaPersonal){
            sb.append(" left join ( select distinct par.aulaavulsadiaria,par.personal, mod.nome as modalidadeDiaria,mpp.movproduto as codigoproduto from movparcela par  ");
            sb.append(" left join movprodutoparcela mpp  on par.codigo = mpp.movparcela left join aulaavulsadiaria  diaria on diaria.codigo = par.aulaavulsadiaria   ");
            sb.append(" left join modalidade mod on mod.codigo = diaria.modalidade  where par.aulaavulsadiaria is not null or par.personal is not null ) as dp on dp.codigoproduto = movProduto.codigo ");
        }
        if (duracao != 0) {
            sb.append(" left join contratoDuracao on movProduto.contrato = contratoDuracao.contrato ");
        }
        sb.append(" inner join usuario responsavel  ON  responsavel.codigo = movproduto.responsavellancamento ");

        if (colaborador != 0) {
            sb.append(" inner join cliente on movProduto.pessoa = cliente.pessoa ");
            sb.append(" left join vinculo on cliente.codigo = vinculo.cliente AND vinculo.tipovinculo = 'CO'");
        }
        sb.append(" where ");
        sb.append(" movProduto.datalancamento >= '").append(dataInicial).append(" 00:00:00' ");
        if (duracao != 0) {
            sb.append(" and contratoDuracao.numeromeses = ").append(duracao).append(" ");
        }
        for (String tipo : tipoProduto) {
            if ((!tipo.equals("PM") || (UteisValidacao.emptyNumber(plano) && (UteisValidacao.emptyString(nomePlano) || !nomePlano.equals("PLANO TRANSFERIDO")))) && produto != 0) {
                sb.append(" and produto.codigo = ").append(produto).append(" ");
            }
        }

        if (plano != 0) {
            sb.append(" and contrato.plano = ").append(plano).append(" and movproduto.descricao not like 'PLANO TRANSFERIDO -%' ");
        } else  if (!UteisValidacao.emptyString(nomePlano) && nomePlano.equals("PLANO TRANSFERIDO")) {
                sb.append(" and movproduto.descricao like 'PLANO TRANSFERIDO -%'");
        }

        sb.append(" and movProduto.datalancamento <= '").append(dataFinal).append(" 23:59:59' ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sb.append(" and movProduto.empresa = ").append(empresa).append(" ");
        }

        if (desconsiderarProdutoCancelado) {
            sb.append(" and movProduto.situacao not in ('CA') ");
        }

        if (somenteContratoVendaAvulsaMesmoMesReferencia) {
            sb.append(" and (");
            sb.append(" (movProduto.datalancamento::date = vendaavulsa.dataregistro::date) ");
            sb.append(" OR ");
            sb.append(" (movProduto.datalancamento::date = contrato.datalancamento::date) ");
            sb.append(" ) ");
        }

        sb.append(" and movProduto.movpagamentocc is null ");
        //sb.append(" and movProduto.situacao NOT LIKE 'CA' ");
        sb.append(" and movProduto.totalFinal >= 0 ");
        sb.append(adicionarTipoProdutoSQL(tipoProduto, " tipoProduto = '"));
        if (evento){
            sb.append(" and movProduto.descricao ilike '% - evento%' ");
        }
        if (colaborador != 0) {
            sb.append(" and vinculo.colaborador = ").append(colaborador).append(" ");
        }

        if(!UteisValidacao.emptyNumber(operador)){
            sb.append(" AND responsavel.colaborador = ").append(operador).append("\n");
        }
        sb.append(" group by pessoa.codigo, movProduto_datalancamento,contrato.codigo, contrato.situacaoContrato, empresa.nome, nomeProduto, movproduto.vendaavulsa,tipoProduto,nomeResponsavel,vendaavulsa.nomeComprador, vendaavulsa.tipocomprador ");
        if(tipoDiariaAulaPersonal){
            sb.append(",dp.aulaavulsadiaria,dp.personal, dp.modalidadeDiaria  ");
        }
        sb.append(" order by movProduto_datalancamento  ");

        FaturamentoSinteticoProdutoMesVO produtoMesVO;
        List<FaturamentoSinteticoResumoPessoaVO> listaResumoPessoa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {

                produtoMesVO = new FaturamentoSinteticoProdutoMesVO();
                listaResumoPessoa = new ArrayList<FaturamentoSinteticoResumoPessoaVO>();

                // inicializando valores de produtoMesVO
                produtoMesVO.setValor(0.0);
                produtoMesVO.setQtd(0);

                while (tabelaResultado.next()) {
                    // Montando dados de resumoPessoa
                    FaturamentoSinteticoResumoPessoaVO resumoPessoaVO = new FaturamentoSinteticoResumoPessoaVO();
                    Integer pessoaCodigo = tabelaResultado.getInt("pessoa_codigo");
                    Integer contratoCodigo = tabelaResultado.getInt("contrato_codigo");
                    String situacaoContrato = tabelaResultado.getString("situacaoContrato");

                    if (pessoaCodigo == null || pessoaCodigo.equals(0)) {
                        resumoPessoaVO.getCliente().getPessoa().setCodigo(new Integer(0));
                    } else {
                        resumoPessoaVO.getCliente().getPessoa().setCodigo(pessoaCodigo);
                    }
                    if (contratoCodigo == null || contratoCodigo.equals(0)) {
                        resumoPessoaVO.getContrato().setCodigo(0);
                    } else {
                        resumoPessoaVO.getContrato().setCodigo(contratoCodigo);
                        resumoPessoaVO.getContrato().setSituacaoContrato(situacaoContrato);
                    }

                    //Adicionar nome do consumidor no relatorio quando for venda avulsa.
                    if (StringUtils.isNotBlank(tabelaResultado.getString("nomeComprador")) && StringUtils.isNotBlank(tabelaResultado.getString("tipocomprador"))) {
                        if (tabelaResultado.getString("tipocomprador").equals("CN")) {
                            resumoPessoaVO.setConsumidor(tabelaResultado.getString("nomeComprador"));
                        }
                    }
                    // produtoQuitado , produto.descricao as nomeProduto, movproduto.quantidade qtdProduto,
                    resumoPessoaVO.setValor(tabelaResultado.getDouble("total"));
                    resumoPessoaVO.setNomeEmpresa(tabelaResultado.getString("nomeEmpresa"));
                    resumoPessoaVO.setNomeProduto(tabelaResultado.getString("nomeProduto"));
                    resumoPessoaVO.getProdutoVO().setTipoProduto(tabelaResultado.getString("tipoProduto"));
                    resumoPessoaVO.setQuantidade(tabelaResultado.getInt("qtdProduto"));
                    resumoPessoaVO.setDescricaoProduto(descricaoProduto);
                    resumoPessoaVO.setNomeResponsavelLancamento(tabelaResultado.getString("nomeResponsavel"));
                    // Incrementando valores de produtoXmes
                    resumoPessoaVO.setDataLancamentoMovProduto(tabelaResultado.getTimestamp("movProduto_datalancamento"));
                    if (tipoDiariaAulaPersonal) {
                        resumoPessoaVO.setNomeModalidadeDiaria(tabelaResultado.getString("modalidadeDiaria"));
                    }
                    produtoMesVO.setQtd(produtoMesVO.getQtd() + 1);
                    produtoMesVO.setValor(produtoMesVO.getValor() + resumoPessoaVO.getValor()); // adicionando ao valor do produtoXmes
                    // adicionando resumoPessoa ao produtoXmes
                    if(resumoPessoaVO.getValor()==0 && !empresaLogado.isMostrarValoresZeradosRel()) {
                        produtoMesVO.setQtd(produtoMesVO.getQtd() - 1);
                        continue;
                    }
                    listaResumoPessoa.add(resumoPessoaVO);
                }
            }
        }

        produtoMesVO.setListaResumoPessoa(listaResumoPessoa);
        produtoMesVO.setValor(Uteis.arredondarForcando2CasasDecimais(produtoMesVO.getValor()));
        return produtoMesVO;
    }

    public void verificarTipoProdutoTaxaPersonalSelecionado(List<String> tipoProduto) {
        for (String tipo : tipoProduto){
            if (tipo.equals(TipoProduto.TAXA_PERSONAL.getCodigo())){
                tipoProduto.add(TipoProduto.CREDITO_PERSONAL.getCodigo());
                break;
            }
        }
    }

    public StringBuilder adicionarTipoProdutoSQL(List<String> tipoProduto, String sqlAdicionado) {
        verificarTipoProdutoTaxaPersonalSelecionado(tipoProduto);
        StringBuilder sqlAnterior = new StringBuilder();
        if (!tipoProduto.isEmpty()) {
            int i = 0;
            String andOuOr = "";
            for (String tipo : tipoProduto) {
                if (i == 0) {
                    andOuOr = "and (";
                } else {
                    andOuOr = "or ";
                }
                i++;
                sqlAnterior.append(andOuOr).append(sqlAdicionado).append(tipo.toUpperCase()).append("' ");
            }
            sqlAnterior.append(")");
            i = 0;
        }
        return sqlAnterior;
    }

    public List<CompetenciaSinteticoProdutoVO> consultarProdutosMovimentadoParaCompetenciaPeriodo(Date dataInicial, Date dataFinal,
            List<String> tipoProduto, Integer empresa, String agrupamentoPlano, boolean controlarAcesso, int nivelMontarDados, Boolean desconsiderarAlunosInadimplentes) throws Exception {
        StringBuilder sb = new StringBuilder();
        //obter as referencias dos meses consultados
        String mesesReferencia = new String();
        List<Date> mesesEntreDatas = Uteis.getMesesEntreDatas(dataInicial, dataFinal);
        for (Date data : mesesEntreDatas) {
            mesesReferencia += " ,'" + Uteis.getMesReferenciaData(data) + "'";
        }
        mesesReferencia = mesesReferencia.replaceFirst(",", "");
        List<CompetenciaSinteticoProdutoVO> listaCompetencia = new ArrayList<CompetenciaSinteticoProdutoVO>();
        if (tipoProduto.contains("PM")) {
            if (agrupamentoPlano.equalsIgnoreCase("nome")) {
                sb.append("select produto.tipoproduto, contrato.plano AS plano, produto.codigo, ");
                sb.append("pla.descricao as movimentacaoDescricao \n");
            } else if (agrupamentoPlano.equalsIgnoreCase("duracao")) {
                sb.append("select contratoDuracao.numeromeses, produto.tipoproduto ");
            } else {
                sb.append("select contratoDuracao.numeromeses as numeromeses, produto.codigo, produto.tipoproduto, contrato.plano, ");
                sb.append("pla.descricao as movimentacaoDescricao \n");
            }
            sb.append(" from movProduto  ");
            sb.append(" inner join contrato on movProduto.contrato = contrato.codigo ");
            sb.append(" left join contratoDuracao on contratoDuracao.contrato = contrato.codigo ");
            sb.append(" left join plano pla on pla.codigo = contrato.plano ");
            sb.append(" left join produto on movProduto.produto = produto.codigo ");
            sb.append(" where movproduto.contrato =  contrato.codigo ");
            if(desconsiderarAlunosInadimplentes != null && desconsiderarAlunosInadimplentes) {
                sb.append(" and movproduto.situacao = 'PG'");
            }
            sb.append(" and movproduto.movpagamentocc is null");
            sb.append(" and contrato.codigo = contratoDuracao.contrato ");
            sb.append("AND movproduto.mesreferencia IN (" + mesesReferencia + ") ");
            sb.append(adicionarTipoProdutoSQL(tipoProduto, " produto.tipoproduto = '"));
            if (!UteisValidacao.emptyNumber(empresa)) {
                sb.append(" and movProduto.empresa = " + empresa + " ");
            }
            if (agrupamentoPlano.equalsIgnoreCase("duracao")){
                sb.append(" group by contratoDuracao.numeromeses,produto.tipoproduto");
                sb.append(" order by contratoDuracao.numeromeses ");
            } else {
                sb.append(" and movProduto.descricao not like 'PLANO TRANSFERIDO -%'");
                if (agrupamentoPlano.equalsIgnoreCase("nome")) {
                    sb.append(" group by movimentacaoDescricao, produto.tipoproduto, contrato.plano, produto.codigo");
                } else {
                    sb.append(" group by movimentacaoDescricao, contratoDuracao.numeromeses, produto.codigo,produto.tipoproduto, contrato.plano");
                }

                sb.append(" UNION ALL ");
                if (agrupamentoPlano.equalsIgnoreCase("nome")) {
                    sb.append("select produto.tipoproduto, 0 as plano, produto.codigo, ");
                    sb.append("'PLANO TRANSFERIDO' as movimentacaoDescricao \n");
                } else{
                    sb.append("select contratoDuracao.numeromeses, produto.codigo, produto.tipoproduto, 0 as plano, ");
                    sb.append("'PLANO TRANSFERIDO' as movimentacaoDescricao \n");
                }
                sb.append(" from movProduto  ");
                sb.append(" inner join contrato on movProduto.contrato = contrato.codigo ");
                sb.append(" left join contratoDuracao on contratoDuracao.contrato = contrato.codigo ");
                sb.append(" left join plano pla on pla.codigo = contrato.plano ");
                sb.append(" left join produto on movProduto.produto = produto.codigo ");
                sb.append(" where movproduto.contrato =  contrato.codigo ");
                sb.append(" and movproduto.movpagamentocc is null");
                sb.append(" and contrato.codigo = contratoDuracao.contrato ");
                sb.append("AND movproduto.mesreferencia IN (" + mesesReferencia + ") ");
                sb.append(adicionarTipoProdutoSQL(tipoProduto, " produto.tipoproduto = '"));
                if (!UteisValidacao.emptyNumber(empresa)) {
                    sb.append(" and movProduto.empresa = " + empresa + " ");
                }
                sb.append(" and movProduto.descricao like 'PLANO TRANSFERIDO -%'");
                if (agrupamentoPlano.equalsIgnoreCase("nome")) {
                    sb.append(" group by movimentacaoDescricao, produto.tipoproduto, plano, produto.codigo");
                    sb.append(" order by movimentacaoDescricao ");
                } else {
                    sb.append(" group by movimentacaoDescricao, contratoDuracao.numeromeses, produto.codigo,produto.tipoproduto, plano");
                    sb.append(" order by movimentacaoDescricao, numeromeses ");
                }
            }
            String sqlstr = sb.toString();
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sqlstr)) {

                    while (tabelaResultado.next()) {
                        CompetenciaSinteticoProdutoVO obj = new CompetenciaSinteticoProdutoVO();
                        if (agrupamentoPlano.equalsIgnoreCase("nome")) {
                            obj.setDescricao(tabelaResultado.getString("movimentacaoDescricao"));
                            obj.setPlano(tabelaResultado.getInt("plano"));
                            obj.setNomePlano(tabelaResultado.getString("movimentacaoDescricao"));
                            obj.getProduto().setCodigo(tabelaResultado.getInt("codigo"));
                        } else if (agrupamentoPlano.equalsIgnoreCase("nomeDuracao")) {
                            obj.setDuracao(tabelaResultado.getInt("numeromeses"));
                            obj.setDescricao(tabelaResultado.getString("plano") + " - " + tabelaResultado.getString("movimentacaoDescricao") + " - " + obj.getDuracao().toString());
                            obj.setPlano(tabelaResultado.getInt("plano"));
                            obj.setNomePlano(tabelaResultado.getString("movimentacaoDescricao"));
                            obj.getProduto().setCodigo(tabelaResultado.getInt("codigo"));
                        } else if (agrupamentoPlano.equalsIgnoreCase("duracao")) {
                            obj.setDuracao(tabelaResultado.getInt("numeromeses"));
                            obj.setDescricao(obj.getDuracao().toString() + (obj.getDuracao() == 1 ? " mês " : " meses"));
                        }

                        obj.getProduto().setTipoProduto(tabelaResultado.getString("tipoproduto"));

                        boolean existe = false;
                        for (CompetenciaSinteticoProdutoVO cspvo : listaCompetencia) {
                            if (cspvo.getDescricao().equals(obj.getDescricao())) {
                                existe = true;
                            }
                        }
                        if (!existe) {
                            listaCompetencia.add(obj);
                        }
                    }
                }
            }
        } else {
            consultarParaCompetencia(tipoProduto, empresa, mesesReferencia, listaCompetencia, false);
            if (tipoProduto.isEmpty()) {
                consultarParaCompetencia(tipoProduto, empresa, mesesReferencia, listaCompetencia, true);
            }
        }
        return listaCompetencia;
    }

    /**
     * Responsável por
     * <AUTHOR> Alcides
     * 15/08/2013
     */
    private void consultarParaCompetencia(List<String> tipoProduto, Integer empresa, String mesesReferencia,
            List<CompetenciaSinteticoProdutoVO> listaCompetencia, boolean produtoPlano) throws SQLException {

        StringBuilder sb = new StringBuilder();
        sb.append("select produto.codigo, produto.descricao");
        sb.append(", produto.tipoproduto, produto.valorfinal ");
        sb.append(" from movProduto ");
        sb.append(" left join produto on movProduto.produto = produto.codigo ");
        sb.append(" where movproduto.mesreferencia IN (" + mesesReferencia + ") ");
        sb.append(adicionarTipoProdutoSQL(tipoProduto, " produto.tipoproduto = '"));
        if (produtoPlano) {
            sb.append(" and produto.tipoproduto = 'PM' ");
        } else {
            sb.append(" and produto.tipoproduto not in (").append(PRODUTOS_IGNORADOS).append(",'PM')");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sb.append(" and movProduto.empresa = " + empresa + " ");
        }
        sb.append(" and movproduto.movpagamentocc is null");
        sb.append(" group by produto.codigo, produto.tipoproduto,  produto.valorfinal,produto.descricao");
        sb.append(" order by produto.descricao");

        String sqlstr = sb.toString();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlstr)) {

                while (tabelaResultado.next()) {
                    CompetenciaSinteticoProdutoVO obj = new CompetenciaSinteticoProdutoVO();
                    obj.getProduto().setCodigo(tabelaResultado.getInt("codigo"));
                    obj.getProduto().setDescricao(tabelaResultado.getString("descricao"));
                    obj.getProduto().setTipoProduto(tabelaResultado.getString("tipoproduto"));
                    obj.getProduto().setValorFinal(new Double(tabelaResultado.getDouble("valorFinal")));
                    obj.setDescricao(obj.getProduto().getDescricao());
                    //            if (produtoPlano) {
                    //                obj.setNomePlano(tabelaResultado.getString("movimentacaoDescricao"));
                    //            }
                    //            if (obj.getProduto().getDescricao().equalsIgnoreCase("PLANO") || obj.getProduto().getDescricao().equalsIgnoreCase("BOLSA") || (obj.getProduto().getDescricao().contains("PLANO") && !obj.getNomePlano().equals(""))) {
                    //                obj.setDescricao(obj.getNomePlano());
                    //            }
                    obj.setDuracao(new Integer(0));
                    obj.setPlano(new Integer(0));

                    boolean existe = false;
                    for (CompetenciaSinteticoProdutoVO cspvo : listaCompetencia) {
                        if (cspvo.getDescricao().equals(obj.getDescricao())
                                && cspvo.getProduto().getValorFinal().equals(obj.getProduto().getValorFinal())) {
                            existe = true;
                        }
                    }
                    if (!existe) {
                        listaCompetencia.add(obj);
                    }
                }
            }
        }
    }

    public CompetenciaSinteticoProdutoMesVO consultarCompetenciaSinteticoResumoPessoa(String mesreferencia,
            List<String> tipoProduto, Integer empresa, Integer duracao, Integer plano, Integer produto,
            String nomePlano, String nomeProduto) throws Exception {
        StringBuilder sb = new StringBuilder();
        boolean tipoDiariaAulaPersonal = existeTipoProdutoDiariaAulaPersonal(tipoProduto);
        sb.append(" select ");
        sb.append(" pessoa.codigo as pessoa_codigo, ");
        sb.append(" movProduto.codigo, ");
        sb.append(" contrato.codigo as contrato_codigo, ");
        sb.append(" movProduto.mesreferencia as movProduto_mesreferencia, movProduto.vendaavulsa, ");
        if(tipoDiariaAulaPersonal){
            sb.append(" dp.aulaavulsadiaria,dp.personal, dp.modalidadeDiaria,");
        }
        sb.append(" movProduto.totalFinal as total ");
        sb.append(" from movProduto ");
        sb.append(" left join produto on movProduto.produto = produto.codigo ");
        sb.append(" left join pessoa on movProduto.pessoa = pessoa.codigo ");
        sb.append(" left join contrato on movProduto.contrato = contrato.codigo ");
        if (duracao != 0) {
            sb.append(" left join contratoDuracao on movProduto.contrato = contratoDuracao.contrato ");
        }
        if(tipoDiariaAulaPersonal){
            sb.append("  left join ( select distinct par.aulaavulsadiaria,par.personal, mod.nome as modalidadeDiaria,mpp.movproduto as codigoproduto from movparcela par    ");
            sb.append(" left join movprodutoparcela mpp  on par.codigo = mpp.movparcela left join aulaavulsadiaria  diaria on diaria.codigo = par.aulaavulsadiaria  ");
            sb.append(" left join modalidade mod on mod.codigo = diaria.modalidade  where par.aulaavulsadiaria is not null or par.personal is not null ) as dp on dp.codigoproduto = movProduto.codigo ");
        }
        sb.append(" where ");
        sb.append(" movProduto.mesreferencia = '").append(mesreferencia.toUpperCase()).append("' ");
        if (duracao != 0) {
            sb.append(" and contratoDuracao.numeromeses = ").append(duracao.intValue()).append(" ");
        }
        for (String tipo : tipoProduto) {
            if ((!tipo.equals("PM") || (UteisValidacao.emptyNumber(plano) && (UteisValidacao.emptyString(nomePlano) || !nomePlano.equals("PLANO TRANSFERIDO")))) && produto != 0) {
                sb.append(" and produto.codigo = ").append(produto).append(" ");
            }
        }
        if (plano != 0) {
            sb.append(" and contrato.plano = ").append(plano).append("and movproduto.descricao not like 'PLANO TRANSFERIDO -%'  ");
        } else  if (!UteisValidacao.emptyString(nomePlano) && nomePlano.equals("PLANO TRANSFERIDO")) {
          sb.append(" and movproduto.descricao like 'PLANO TRANSFERIDO -%'");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sb.append(" and movProduto.empresa = ").append(empresa);
        }
        sb.append(adicionarTipoProdutoSQL(tipoProduto, " tipoProduto = '"));
        sb.append(" and produto.tipoproduto not in (").append(PRODUTOS_IGNORADOS).append(")");
        sb.append(" and movProduto.totalFinal > 0 ");
        sb.append(" and movProduto.movpagamentocc is null ");
        sb.append(" and movProduto.situacao NOT LIKE 'CA' ");
        sb.append(" group by pessoa.codigo, movProduto_mesreferencia, contrato.codigo, total,  movProduto.codigo ,movProduto.vendaavulsa");
        if(tipoDiariaAulaPersonal){
            sb.append(",dp.aulaavulsadiaria,dp.personal, dp.modalidadeDiaria ");
        }
        sb.append(" order by  movProduto_mesreferencia  ");
        String sqlstr = sb.toString();
        CompetenciaSinteticoProdutoMesVO produtoMesVO;
        List<CompetenciaSinteticoResumoPessoaVO> listaResumoPessoa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlstr)) {
                produtoMesVO = new CompetenciaSinteticoProdutoMesVO();
                listaResumoPessoa = new ArrayList<CompetenciaSinteticoResumoPessoaVO>();
                // inicializando valores de produtoMesVO
                produtoMesVO.setValor(0.0);
                produtoMesVO.setQtd(0);
                while (tabelaResultado.next()) {
                    CompetenciaSinteticoResumoPessoaVO resumoPessoaVO = new CompetenciaSinteticoResumoPessoaVO();
                    // Montando dados de resumoPessoa
                    Integer pessoaCodigo = tabelaResultado.getInt("pessoa_codigo");
                    Integer pessoaContrato = tabelaResultado.getInt("contrato_codigo");
                    if (pessoaCodigo == null || pessoaCodigo.equals(0)) {
                        resumoPessoaVO.getCliente().getPessoa().setCodigo(new Integer(0));
                    } else {
                        resumoPessoaVO.getCliente().getPessoa().setCodigo(pessoaCodigo);
                    }
                    if (pessoaContrato == null || pessoaContrato.equals(0)) {
                        resumoPessoaVO.getContrato().setCodigo(new Integer(0));
                    } else {
                        resumoPessoaVO.getContrato().setCodigo(pessoaContrato);
                    }
                    resumoPessoaVO.setValor(tabelaResultado.getDouble("total"));
                    if (tipoDiariaAulaPersonal) {
                        resumoPessoaVO.setNomeModalidadeDiaria(tabelaResultado.getString("modalidadeDiaria"));
                    }
                    // Incrementando valores de produtoXmes
                    produtoMesVO.setQtd(produtoMesVO.getQtd() + 1);
                    produtoMesVO.setValor(produtoMesVO.getValor() + resumoPessoaVO.getValor()); // adicionando ao valor do produtoXmes

                    // adicionando resumoPessoa ao produtoXmes
                    listaResumoPessoa.add(resumoPessoaVO);
                }
            }
        }
        produtoMesVO.setListaResumoPessoa(listaResumoPessoa);
        return produtoMesVO;
    }

    public Double cosultarValorAbertoContratoAteEstaData(Integer codigoContrato)
            throws Exception {

        consultar(getIdEntidade());
        String sql = "select sum(totalFinal) as totalAberto from MovProduto where "
                + "(contrato = ?) and (situacao = 'EA')";

        try {
            try (PreparedStatement stm = con.prepareStatement(sql)) {
                stm.setInt(1, codigoContrato);
                try (ResultSet rs = stm.executeQuery()) {
                    if (!rs.next()) {
                        return 0.0;
                    } else {
                        return rs.getDouble("totalAberto");
                    }
                }
            }
        } catch (SQLException ex) {
            Logger.getLogger(MovProduto.class.getName()).log(Level.SEVERE,
                    ex.getMessage(), ex);
        }
        return 0.0;
    }

    public Hashtable getMovProdutoParcelas() {
        return (movProdutoParcelas);
    }

    public void setMovProdutoParcelas(Hashtable movProdutoParcelas) {
        this.movProdutoParcelas = movProdutoParcelas;
    }

    /*
     * Ulissses... 09/02/11
     * Objetivo do método: Saber se um determinado produto que o cliente comprou está dentro da validade.
     * Retorno           : Se o produto for encontrado e o mesmo estiver dentro da validade, é retornado "true"
     *                     Se o produto não for encontrado ou o mesmo não estiver dentro da validade, é retornado "false"
     */
    public boolean produtoDentroDaValidade(Integer Codigoproduto, Integer codigoPessoa) throws Exception {
        String sql = "SELECT * FROM MovProduto "
                + "where (current_date >= datainiciovigencia::date) and (current_date <= datafinalvigencia::date)"
                + " and produto = " + Codigoproduto.intValue()
                + " and pessoa = " + codigoPessoa.intValue();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return tabelaResultado.next();
            }
        }
    }

    /**
     * Responsável por consultar o valor do produto matricula de um contrato específico de uma pessoa.
     * A consulta tem por objetivo ser mais enxuta do que as que trazem o histórico de produtos do cliente
     * <AUTHOR>
     * 13/04/2011
     * @param pessoa
     * @param contrato
     * @return
     * @throws Exception
     */
    public Double obterValorProdutoMatriculaContratoPessoa(Integer pessoa, Integer contrato) throws Exception {
        Double total = new Double(0.0);
        StringBuilder sql = new StringBuilder();
        //montar consulta
        sql.append("SELECT MP.totalfinal FROM movproduto MP, pessoa P, contrato C, produto PR ");
        sql.append("WHERE MP.produto = PR.codigo AND MP.contrato = C.codigo AND MP.pessoa = P.codigo ");
        sql.append(" AND PR.tipoproduto LIKE 'MA'");
        sql.append(" AND P.codigo = ?");
        sql.append(" AND C.codigo = ?");
        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 0;
        dc.setInt(++i, pessoa);
        dc.setInt(++i, contrato);
        try (ResultSet rs = dc.executeQuery()) {
            if (rs.next()) {
                total = rs.getDouble("totalfinal");
            }
        }
        return total;
    }

    /**
     * Responsável por consultar o valor do produto rematricula de um contrato específico de uma pessoa.
     * A consulta tem por objetivo ser mais enxuta do que as que trazem o histórico de produtos do cliente
     * <AUTHOR>
     * 29/11/2011
     * @param pessoa
     * @param contrato
     * @return
     * @throws Exception
     */
    public Double obterValorProdutoRematriculaContratoPessoa(Integer pessoa, Integer contrato) throws Exception {
        Double total = new Double(0.0);
        StringBuilder sql = new StringBuilder();
        //montar consulta
        sql.append("SELECT MP.totalfinal FROM movproduto MP, pessoa P, contrato C, produto PR ");
        sql.append("WHERE MP.produto = PR.codigo AND MP.contrato = C.codigo AND MP.pessoa = P.codigo ");
        sql.append(" AND PR.tipoproduto LIKE 'RE'");
        sql.append(" AND P.codigo = ?");
        sql.append(" AND C.codigo = ?");
        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 0;
        dc.setInt(++i, pessoa);
        dc.setInt(++i, contrato);
        try (ResultSet rs = dc.executeQuery()) {
            if (rs.next()) {
                total = rs.getDouble("totalfinal");
            }
        }
        return total;
    }

    /**
     * Responsável por consultar em banco o valor pago pelo cliente em um produto.
     * Produtos mensalidade podem ser divididos em mais de uma parcela, neste caso o aluno paga uma parcela mas pode
     * não pagar totalmente um produto relacionado. Este método irá consultar qual a parte já paga do produto.
     * <AUTHOR>
     * 21/06/2011
     */
    public void obterValorParcialmentPago(MovProdutoVO produto) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT SUM(valorpago) AS valor \n");
        sql.append("FROM   movprodutoparcela mpp \n");
        sql.append("       INNER JOIN movparcela mp \n");
        sql.append("       ON     mp.codigo      = mpp.movparcela \n");
        sql.append("       AND    mpp.movproduto = ? \n");
        sql.append("       AND    mp.situacao LIKE 'PG'");
        Declaracao dc = new Declaracao(sql.toString(), con);
        dc.setInt(1, produto.getCodigo());
        try (ResultSet rs = dc.executeQuery()) {
            if (rs.next()) {
                produto.setValorParcialmentePago(rs.getDouble("valor"));
            }
        }

    }

    @Override
    public List<MovProdutoVO> consultar(String sql, int nivelMontarDados) throws Exception {
        try (ResultSet resultDados = SuperFacadeJDBC.criarConsulta(sql, con)) {
            return montarDadosConsulta(resultDados, nivelMontarDados, con);
        }
    }

    /**
     * Verifica se já possui um movimento de produto lançado para determinada pessoa
     * em aberto do tipo PAGTO SALDO DEVEDOR C/C
     */
    public boolean consultarSeExisteSaldoDevedorCCEmAberto(int codigoPessoa, int codigoEmpresa) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select exists(select * from movproduto ");
        sql.append("where pessoa  = " + codigoPessoa);
        sql.append("and descricao ilike 'PAGTO. SALDO DEVEDOR C/C%' and situacao = 'EA'");
        sql.append("and empresa= " + codigoEmpresa + ") as existe");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    @Override
    public void alterarNaoGerarMensagem(MovProdutoVO movProduto, boolean valor) throws Exception {
        movProduto.setNaoGerarMensagem(valor);
        alterar(movProduto);
    }

    @Override
    public void alterarNaoGerarMensagem(ClienteMensagemVO clienteMensagem, boolean valor) throws Exception {
        if (clienteMensagem.getTipomensagem() != TiposMensagensEnum.PRODUTO_VENCIDO) {
            return;
        }
        String sql = "select mp.* "
                + "from movproduto mp "
                + "inner join cliente cl on mp.pessoa = cl.pessoa "
                + "where mp.produto = 14 "
                + "and cl.codigo = 1 "
                + "order by mp.datafinalvigencia desc "
                + "limit 1";
        List resultado = consultar(sql, Uteis.NIVELMONTARDADOS_TODOS);
        if (resultado.isEmpty()) {
            return;
        }
        MovProdutoVO movProduto = (MovProdutoVO) resultado.get(0);
        alterarNaoGerarMensagem(movProduto, valor);
    }

    public ResultSet contarQuantidadeEValorProdutosVencidos(int empresa) throws Exception {
        String sql = "select count(*) as qtd, sum(mp.totalfinal) as total "
                + "from movproduto mp "
                + "inner join (select mp.produto, mp.pessoa, max(mp.codigo) codigo "
                + "		from movproduto mp "
                + "		inner join produto pd on mp.produto = pd.codigo "
                + "		inner join cliente cl on mp.pessoa = cl.pessoa "
                + "		where pd.tipovigencia  = 'ID' "
                + "             and pd.tipoproduto = 'SE' "
                + "             and cl.situacao = 'AT' "
                + "		and mp.datafinalvigencia < '" + Uteis.getDataJDBC(Uteis.retirarHoraDaData(Calendario.hoje())) + "' "
                + "		and mp.empresa = " + String.valueOf(empresa)
                + "             and coalesce((select count(*) from movproduto mp1 where mp.pessoa = mp1.pessoa "
                + "             and mp.produto = mp1.produto and mp1.datalancamento > mp.datalancamento), 0) = 0 "
                + "		group by mp.produto, mp.pessoa) res on mp.codigo = res.codigo ";
        Statement stm = con.createStatement();
        return stm.executeQuery(sql);
    }

    public ResultSet consultarClientesProdutosVencidos(int empresa,Date dataBaseInicio, Date dataBase) throws Exception {
        String sql = "select mp.codigo, res.cli "
                + "from movproduto mp "
                + "inner join (select mp.produto, mp.pessoa, cl.codigo as cli, max(mp.codigo) codigo "
                + "		from movproduto mp "
                + "		inner join produto pd on mp.produto = pd.codigo "
                + "		inner join cliente cl on mp.pessoa = cl.pessoa "
                + "		where pd.tipovigencia  = 'ID' "
                + "             and pd.tipoproduto = 'SE' "
                + "             and cl.situacao = 'AT' "
                + "             :: "
                + "		and mp.datafinalvigencia < '" + Uteis.getDataJDBC(Uteis.retirarHoraDaData(dataBase)) + "' ";

        if(empresa != 0){
            sql += "		    and mp.empresa = " + String.valueOf(empresa);
        }

        sql += "             and coalesce((select count(*) from movproduto mp1 where mp.pessoa = mp1.pessoa "
                + "             and mp.produto = mp1.produto and mp1.datalancamento > mp.datalancamento), 0) = 0 "
                + "		group by mp.produto, mp.pessoa, cl.codigo) res on mp.codigo = res.codigo "
                + "order by mp.datafinalvigencia";
        String subSQL = "";
        if (dataBaseInicio != null) {
            subSQL = " AND EXISTS(SELECT pes.codigo FROM pessoa pes WHERE pes.codigo = cl.pessoa AND pes.datacadastro >= '" + Uteis.getDataJDBC(dataBaseInicio) + " 00:00:00') \n";
        }
        sql = sql.replace("::", subSQL);
        Statement stm = con.createStatement();
        return stm.executeQuery(sql);
    }

    public void atualizarDescricaoMovProdutoModalidade(Integer contrato) throws Exception {
        List<MovProdutoVO> dadosConsulta;
        try (ResultSet resultSet = criarConsulta("SELECT * FROM movproduto WHERE contrato = " + contrato, con)) {
            dadosConsulta = MovProduto.montarDadosConsulta(resultSet, Uteis.NIVELMONTARDADOS_TODOS, con);
        }

        for (MovProdutoVO mp : dadosConsulta) {
            executarConsulta("UPDATE movproduto SET descricaomovprodutomodalidade = '" + StringEscapeUtils.escapeSql(resolveDescricaoMovProdutoModalidade(mp).toString())  + "' where codigo = " + mp.getCodigo(),con);
        }

    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>MovProdutoVO</code> através do campo movpagamentocc.
     *
     * @exception Exception
     *                Caso haja problemas de conexão ou localização do objeto
     *                procurado.
     */
    public List<MovProdutoVO> consultarPorMovPagamentoContaCorrente(String movpagamento, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM MovProduto WHERE movpagamentocc = '" + movpagamento + "' or movpagamentocc like '" + movpagamento + ",%' or  movpagamentocc like '%," + movpagamento + ",%' or movpagamentocc like '%," + movpagamento + "'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public String consultarJSON(String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar,
                                Date dtInicio, Date dtFim, Integer empresa) throws Exception {

        StringBuilder sqlCount = new StringBuilder("SELECT count(mp.codigo) FROM movproduto mp");

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT mp.codigo      AS codigo, ");
        sql.append("        mp.contrato    AS contrato, ");
        sql.append("        usr.nome       AS responsavel, ");
        sql.append("        prod.descricao AS produto, ");
        sql.append("        pe.nome        AS cliente, ");
        sql.append("        emp.nome       AS empresa\n ");
        sql.append(" FROM movproduto mp ");


        StringBuilder sqlInner = new StringBuilder();
        sqlInner.append("   INNER JOIN pessoa pe ");
        sqlInner.append(" ON mp.pessoa = pe.codigo ");
        sqlInner.append("   INNER JOIN empresa emp ON mp.empresa = emp.codigo ");
        sqlInner.append("   INNER JOIN produto prod ON mp.produto = prod.codigo ");
        sqlInner.append("   INNER JOIN usuario usr ON mp.responsavellancamento = usr.codigo WHERE 1 = 1 ");

        StringBuilder sqlWhereEmp = new StringBuilder();
        if (empresa != 0) {
            sqlWhereEmp.append(" AND mp.empresa = " + empresa);
        }

        StringBuilder sqlWhereFiltro = new StringBuilder();

        if (dtInicio != null) {
            sqlWhereFiltro.append("  AND datalancamento >= '").append(Uteis.getDataJDBCTimestamp(dtInicio)).append("'\n");
        }

        if (dtFim != null) {
            sqlWhereFiltro.append("  AND datalancamento <= '").append(Uteis.getDataJDBC(dtFim)).append(" 23:59:59.999'\n");
        }


        if (!UteisValidacao.emptyString(clausulaLike)) {
            sqlWhereFiltro.append(" AND (");
            sqlWhereFiltro.append("lower(mp.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlWhereFiltro.append("lower(mp.contrato::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlWhereFiltro.append("lower(usr.nome::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlWhereFiltro.append("lower(prod.descricao::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlWhereFiltro.append("lower(pe.nome::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlWhereFiltro.append("lower(emp.nome::VARCHAR) ~ '").append(clausulaLike).append("'\n");
            sqlWhereFiltro.append(")");
        }

        StringBuilder sqlOrderByLimt = new StringBuilder();
        sqlOrderByLimt.append("  ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
        if (limit > 0) {
            sqlOrderByLimt.append(" LIMIT ").append(limit).append("\n");
        }
        sqlOrderByLimt.append(" OFFSET ").append(offset).append("\n");


        StringBuilder json;
        boolean dados;
        try (PreparedStatement sqlConsultar = getCon().prepareStatement(sql.append(sqlInner).append(sqlWhereEmp).append(sqlWhereFiltro).append(sqlOrderByLimt).toString())) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                json = new StringBuilder();
                json.append("{");
                json.append("\"draw\":\"").append((offset / limit) + 1).append("\",");
                json.append("\"iTotalRecords\":\"").append(contar(sqlCount.append(sqlInner).append(sqlWhereEmp).toString(), getCon())).append("\",");
                json.append("\"iTotalDisplayRecords\":\"").append(contar(sqlCount.append(sqlWhereFiltro).toString(), getCon())).append("\",");
                json.append("\"sEcho\":\"").append(sEcho).append("\",");
                json.append("\"aaData\":[");
                dados = false;
                while (rs.next()) {
                    dados = true;
                    json.append("[\"").append(rs.getString("codigo")).append("\",");
                    json.append("\"").append(rs.getString("contrato")).append("\",");
                    json.append("\"").append(rs.getString("responsavel").trim().replaceAll("\"", "\'")).append("\",");
                    json.append("\"").append(rs.getString("produto").trim().replaceAll("\"", "\'").replaceAll("\\\\", "")).append("\",");
                    json.append("\"").append(rs.getString("cliente").trim().replaceAll("\"", "\'")).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa").trim())).append("\"],");
                }
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        return getPS(empresa, null, null);
    }

    private PreparedStatement getPS(Integer empresa, java.sql.Date inicio, java.sql.Date fim) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT mp.codigo AS codigo, mp.contrato AS contrato, usr.nome AS responsavel,"
                + " prod.descricao AS produto, pe.nome AS cliente, emp.nome AS empresa, mp.datainiciovigencia AS inicio, mp.datafinalvigencia AS fim\n"
                + " FROM movproduto mp\n"
                + " INNER JOIN pessoa pe ON mp.pessoa = pe.codigo\n"
                + " INNER JOIN empresa emp ON mp.empresa = emp.codigo\n"
                + " INNER JOIN produto prod ON mp.produto = prod.codigo\n"
                + " INNER JOIN usuario usr ON mp.responsavellancamento = usr.codigo WHERE 1=1\n");
        if (empresa != 0) {
            sql.append(" and empresa = ? ");
        }
        if (inicio != null) {
            sql.append(" and mp.datalancamento >= ? ");
        }
        if (fim != null) {
            sql.append(" and mp.datalancamento <= ? ");
        }
        sql.append("  ORDER BY mp.codigo DESC");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        if (inicio != null) {
            sqlConsultar.setDate(2, inicio);
        }
        if (fim != null) {
            sqlConsultar.setDate(3, fim);
        }
        return sqlConsultar;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        return consultarParaImpressao(filtro, ordem, campoOrdenacao, empresa, null, null, null);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa, java.sql.Date inicio, java.sql.Date fim, String filtroData) throws SQLException {
        List lista;
        try (ResultSet rs = (inicio == null && fim == null) ? getPS(empresa).executeQuery() : getPS(empresa, inicio, fim).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                MovProdutoVO movProd = new MovProdutoVO();
                String geral = rs.getString("codigo") + rs.getString("contrato") + rs.getString("responsavel") + rs.getString("produto") + rs.getString("cliente") + rs.getString("empresa");
                String[] filtroTemp = filtro.split("\\s");
                String filtroNormalized = "";
                String geralNormalized = "";
                boolean contemFiltro = true;
                for (String temp : filtroTemp) {
                    geralNormalized = Normalizer.normalize(geral.toLowerCase(), Normalizer.Form.NFD);
                    geralNormalized = geralNormalized.replaceAll("[\\p{InCombiningDiacriticalMarks}]", "");
                    filtroNormalized = Normalizer.normalize(temp, Normalizer.Form.NFD);
                    filtroNormalized = filtroNormalized.replaceAll("[\\p{InCombiningDiacriticalMarks}]", "");
                    contemFiltro = contemFiltro && geralNormalized.contains(filtroNormalized);
                }

                if (contemFiltro) {
                    movProd.setCodigo(rs.getInt("codigo"));
                    movProd.getContrato().setCodigo(rs.getInt("contrato"));
                    movProd.getResponsavelLancamento().setNome(rs.getString("responsavel"));
                    movProd.getProduto().setDescricao(rs.getString("produto"));
                    movProd.getPessoa().setNome(rs.getString("cliente"));
                    movProd.getEmpresa().setNome(rs.getString("empresa"));
                    lista.add(movProd);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Contrato")) {
            Ordenacao.ordenarLista(lista, "contrato_Apresentar");
        } else if (campoOrdenacao.equals("Responsável")) {
            Ordenacao.ordenarLista(lista, "responsavel_Apresentar");
        } else if (campoOrdenacao.equals("Produto")) {
            Ordenacao.ordenarLista(lista, "produto_Apresentar");
        } else if (campoOrdenacao.equals("Cliente")) {
            Ordenacao.ordenarLista(lista, "pessoa_Apresentar");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public boolean algumMarcado(Map<String, Boolean> mapaTipoProduto) {
        for (String key : mapaTipoProduto.keySet()) {
            if (UteisValidacao.emptyString(key)) {
                continue;
            }
            if (mapaTipoProduto.get(key)) {
                return true;
            }
        }
        return false;

    }

    @Override
    public void consultaFaturamentoRecebido(Integer empresa,
            List<FaturamentoSinteticoTipoProdutoVO> listaTipoProdutoVO, List<PeriodoMensal> periodos,
            Map<String, Boolean> mapaTipoProduto,
            FaturamentoSinteticoProdutoVO totalGeral,String agrupamento,
            Integer operador, Integer consultor, boolean contaCorrente) throws Exception {

        Map<PeriodoMensal, FaturamentoSinteticoProdutoMesVO> gerais = new HashMap<PeriodoMensal, FaturamentoSinteticoProdutoMesVO>();


        for (String tipoProduto : mapaTipoProduto.keySet()) {
            Map<String, FaturamentoSinteticoProdutoVO> produtoLinhas = new HashMap<String, FaturamentoSinteticoProdutoVO>();
            FaturamentoSinteticoProdutoVO totalizador = new FaturamentoSinteticoProdutoVO();
            totalizador.setDescricao("TOTALIZADOR");

            if (tipoProduto.trim().isEmpty()) {
                if (algumMarcado(mapaTipoProduto)) {
                    continue;
                }
            } else if (tipoProduto.equals("CC") && !algumMarcado(mapaTipoProduto)) {//para que ao desmarcar todos, os produtos de deposito de credito sejam apresentados
            } else if (!mapaTipoProduto.get(tipoProduto)) {
                continue;
            }
            FaturamentoSinteticoTipoProdutoVO tipoProdutoVO = new FaturamentoSinteticoTipoProdutoVO();
            TipoProduto tipoProdutoEnum = TipoProduto.getTipoProdutoCodigo(tipoProduto);
            tipoProdutoVO.setTipoProduto(tipoProdutoEnum == null ?
                tipoProduto.equals("MARERN") ? "Matrícula, Rematrícula, Renovação" : ""
                : TipoProduto.getTipoProdutoCodigo(tipoProduto).getDescricao());



            for (PeriodoMensal periodo : periodos) {
                FaturamentoSinteticoProdutoMesVO fatProdMes = gerais.get(periodo);
                if(fatProdMes == null){
                    fatProdMes = new FaturamentoSinteticoProdutoMesVO();
                    fatProdMes.setQtd(0);
                    fatProdMes.setValor(0.0);
                    gerais.put(periodo, fatProdMes);
                }


                String tipos = tipoProduto.isEmpty() ? "" : tipoProduto.equals("MARERN") ? "'MA', 'RE', 'RN'" : "'"+tipoProduto+"'";

                StringBuilder sql = ThreadDemonstrativoFinanceiro.consultaFaturamentoRecebido(empresa, periodo.getDataInicio(),
                        periodo.getDataTermino(), null, null,false, tipos, false,
                        operador, null, null, false, null,null,null, null);

                if(contaCorrente){
                    sql.append(ThreadDemonstrativoFinanceiro.consultaFaturamentoRecebidoCC(empresa, periodo.getDataInicio(),
                        periodo.getDataTermino(), null, tipos, operador, consultor));
                }

                List<LancamentoDF> listaLancamentosDF;
                try (Statement stm = con.createStatement()) {
                    try (ResultSet dadosMovPagamento = stm.executeQuery(sql.toString())) {
                        listaLancamentosDF = new ArrayList<LancamentoDF>();
                        GenericoAtributosVO codigosProdutosCredito = ThreadDemonstrativoFinanceiro.consultarCodigoCategoriaProdutoCredito(con);
                        while (dadosMovPagamento.next()) {

                            if (dadosMovPagamento.getInt("recibopagamento") > 0) {
                                    // Pesquisar os produtos que o pagamento pagou.
                                List<ProdutoRatear> listaProdutoRatear =
                                        ///           ThreadDemonstrativoFinanceiro.pesquisarProdutosDoPagamento(dadosMovPagamento.getInt("CodigoMovPagamento"),
                                        ////                   dadosMovPagamento.getString("produtospagos"), con, "");
                                        ThreadDemonstrativoFinanceiro.pesquisarProdutosDoPagamento(dadosMovPagamento.getInt("CodigoMovPagamento"), dadosMovPagamento.getString("produtospagos"),
                                                con, "");
                                ThreadDemonstrativoFinanceiro.dividirValorPagamentoPorContrato(listaLancamentosDF,
                                        listaProdutoRatear,
                                        dadosMovPagamento.getDouble("valor"),
                                        dadosMovPagamento.getInt("recibopagamento"),
                                        TipoFormaPagto.AVISTA,
                                        dadosMovPagamento.getString("pagDesc"),
                                        dadosMovPagamento.getInt("CodigoMovPagamento"),
                                        dadosMovPagamento.getDate("dataLancamento"),
                                        null, null,
                                        con, true, consultor, dadosMovPagamento.getString("responsavelpagamento"));
                            } else {
                                ThreadDemonstrativoFinanceiro.dividirMovPagamentoContaAluno(listaLancamentosDF,
                                        dadosMovPagamento.getInt("CodigoMovPagamento"),
                                        dadosMovPagamento.getDouble("valor"), TipoFormaPagto.AVISTA,
                                        dadosMovPagamento.getInt("pessoa"), dadosMovPagamento.getString("nomepagador"),
                                        dadosMovPagamento.getInt("codigoempresa"), codigosProdutosCredito,
                                        dadosMovPagamento.getString("responsavelpagamento"), consultor,
                                        null, null,
                                        con);
                            }
                        }
                    }
                }
                Double valorTotal = 0.0;
                Integer qtd = 0;
                int consumidor = 0;
                Contrato contratoDao = new Contrato(con);
                for (LancamentoDF lancamento : listaLancamentosDF) {
                    if(lancamento.getTipoProduto().equals("CH")){
                       continue;
                    }

                    if(!tipoProduto.isEmpty() && !(lancamento.getTipoProduto().equals(tipoProduto)
                            || (tipoProduto.equals("MARERN")
                            && (lancamento.getTipoProduto().equals("MA")
                                    || lancamento.getTipoProduto().equals("RE")
                                    || lancamento.getTipoProduto().equals("RN"))))){
                        continue;

                    }

                    String descricao = lancamento.getDescricaoLancamento();
                    if(tipoProduto.equals("PM")){
                        if(agrupamento.equals("nomeDuracao")){
                            descricao = contratoDao.nomePlano(lancamento.getContrato()) + " - "+contratoDao.duracao(lancamento.getContrato());
                        }else if (agrupamento.equals("nome")){
                            descricao = contratoDao.nomePlano(lancamento.getContrato());
                        }else if(agrupamento.equals("duracao")){
                            int duracao = contratoDao.duracao(lancamento.getContrato());
                            descricao = duracao + (duracao > 1 ? " meses" : " mês");
                        }
                    }
                    FaturamentoSinteticoProdutoVO linha = produtoLinhas.get(descricao);
                    if (linha == null) {
                        linha = new FaturamentoSinteticoProdutoVO();
                        linha.setDescricao(descricao);
                        produtoLinhas.put(descricao, linha);
                    }
                    FaturamentoSinteticoProdutoMesVO prdXmes = linha.getMapaMeses().get(periodo);
                    if (prdXmes == null) {
                        prdXmes = new FaturamentoSinteticoProdutoMesVO();
                        prdXmes.setQtd(qtd);
                        prdXmes.setValor(valorTotal);
                        linha.getMapaMeses().put(periodo, prdXmes);
                    }
                    FaturamentoSinteticoProdutoMesVO prdXmesTOTAL = totalizador.getMapaMeses().get(periodo);
                    if (prdXmesTOTAL == null) {
                        prdXmesTOTAL = new FaturamentoSinteticoProdutoMesVO();
                        prdXmesTOTAL.setQtd(qtd);
                        prdXmesTOTAL.setValor(valorTotal);
                        totalizador.getMapaMeses().put(periodo, prdXmesTOTAL);
                    }

                    FaturamentoSinteticoResumoPessoaVO faturamentoPessoa = prdXmes.getMapaResumoPessoa().get(lancamento.getContrato()+"-"+lancamento.getCodigoPessoa()+"-"+lancamento.getVendaAvulsa()+"-"+lancamento.getAulaavulsadiaria()+"-"+lancamento.getPersonal());
                    if (faturamentoPessoa == null) {
                        faturamentoPessoa = new FaturamentoSinteticoResumoPessoaVO();
                        faturamentoPessoa.getCliente().getPessoa().setCodigo(lancamento.getCodigoPessoa());
//                        if (UteisValidacao.emptyNumber(faturamentoPessoa.getCliente().getPessoa().getCodigo())) {
//                            faturamentoPessoa.getCliente().getPessoa().setNome("CONSUMIDOR");
//                        }

                        faturamentoPessoa.setValor(lancamento.getValorLancamento());
                        faturamentoPessoa.setFormaPagApresentar(lancamento.getFormaPagApresentar());
                        faturamentoPessoa.getContrato().setCodigo(lancamento.getContrato());
                        faturamentoPessoa.setDataLancamentoMovProduto(lancamento.getDataLancamento());
                        faturamentoPessoa.getProdutoVO().setTipoProduto(lancamento.getTipoProduto());
                        faturamentoPessoa.setNomeProduto(lancamento.getDescricaoLancamento());
                        faturamentoPessoa.setQuantidade(lancamento.getQuantidade());
                        faturamentoPessoa.setNomeResponsavelLancamento(lancamento.getResponsavelLancamento());
                        faturamentoPessoa.setNomeResponsavelRecebimento(lancamento.getResponsavelPagamento());
                        faturamentoPessoa.setProdutoQuitado("");
                        faturamentoPessoa.setDescricaoProduto(descricao);
                        faturamentoPessoa.setNomeModalidadeDiaria(lancamento.getModalidadeDiaria());
                        faturamentoPessoa.setConsumidor(lancamento.getNomePessoa());
                        faturamentoPessoa.setNomeEmpresa(lancamento.getNomeEmpresa());
                        prdXmes.getListaResumoPessoa().add(faturamentoPessoa);
                        prdXmesTOTAL.getListaResumoPessoa().add(faturamentoPessoa);

                        prdXmesTOTAL.setQtd(prdXmesTOTAL.getQtd() + 1);
                        prdXmesTOTAL.setValor(prdXmesTOTAL.getValor() + lancamento.getValorLancamento());

                        prdXmes.setQtd(prdXmes.getQtd() + 1);
                        prdXmes.setValor(prdXmes.getValor() + lancamento.getValorLancamento());

                        fatProdMes.setQtd(fatProdMes.getQtd() + 1);
                        fatProdMes.setValor(fatProdMes.getValor() + lancamento.getValorLancamento());
                        fatProdMes.getListaResumoPessoa().add(faturamentoPessoa);
                        if(!"0-0".equals(lancamento.getContrato()+"-"+lancamento.getCodigoPessoa())){
                        prdXmes.getMapaResumoPessoa().put(lancamento.getContrato()+"-"+lancamento.getCodigoPessoa(), faturamentoPessoa);
                    } else {
                        prdXmes.getMapaResumoPessoa().put("consumidor"+(++consumidor), faturamentoPessoa);
                        }
                    }else{
                        faturamentoPessoa.setValor(faturamentoPessoa.getValor() + lancamento.getValorLancamento());
                        prdXmes.setValor(prdXmes.getValor() + lancamento.getValorLancamento());
                        prdXmesTOTAL.setValor(prdXmesTOTAL.getValor() + lancamento.getValorLancamento());
                        fatProdMes.setValor(fatProdMes.getValor() + lancamento.getValorLancamento());
                    }

                }

            }
            for (FaturamentoSinteticoProdutoVO linha : produtoLinhas.values()) {
                for(PeriodoMensal p : periodos){
                    if (linha.getMapaMeses().get(p) != null) {
                        linha.getListaProdutoXMes().add(linha.getMapaMeses().get(p));
                    }
                }
            }
            for (PeriodoMensal p : periodos) {
                totalizador.getListaProdutoXMes().add(totalizador.getMapaMeses().get(p));
            }
            tipoProdutoVO.getListaProduto().addAll(produtoLinhas.values());
            tipoProdutoVO.setApresentarResultado(!tipoProdutoVO.getListaProduto().isEmpty());
            Ordenacao.ordenarLista(tipoProdutoVO.getListaProduto(), "descricao");
            if(!UteisValidacao.emptyString(tipoProduto)){
                tipoProdutoVO.getListaProduto().add(totalizador);
            }
            listaTipoProdutoVO.add(tipoProdutoVO);
        }
        for (PeriodoMensal p : periodos) {
            totalGeral.getListaProdutoXMes().add(gerais.get(p));
        }

        //Pós processamento para zerar os campos.
        for (FaturamentoSinteticoTipoProdutoVO fstpVO : listaTipoProdutoVO) {
            for (FaturamentoSinteticoProdutoVO fatSin: fstpVO.getListaProduto()) {
                if (fatSin.getMapaMeses().size() < periodos.size()) {
                    for (int i = 0; i < periodos.size(); i++) {
                        PeriodoMensal periodoMensal = periodos.get(i);
                        FaturamentoSinteticoProdutoMesVO fatSPMVO = fatSin.getMapaMeses().get(periodoMensal);
                        if (fatSPMVO == null) {
                            fatSPMVO = new FaturamentoSinteticoProdutoMesVO();
                            fatSPMVO.setQtd(0);
                            fatSPMVO.setValor(0.0);
                            fatSin.getMapaMeses().put(periodoMensal, fatSPMVO);

                            fatSin.getListaProdutoXMes().add(i, fatSPMVO);
                        }
                    }
                }
            }
        }

    }

    @Override
    public void consultaFaturamentoRecebidoComChequeDevolvido(
            Integer empresa,
            List<FaturamentoSinteticoTipoProdutoVO> listaTipoProdutoVO,
            List<PeriodoMensal> periodos,
            Map<String, Boolean> mapaTipoProduto,
            FaturamentoSinteticoProdutoVO totalGeral,
            String agrupamento,
            Integer operador,
            Integer consultor,
            boolean contaCorrente,
            boolean ignorarEdicaoPagamento,
            boolean incluirDevolucaoCheque) throws Exception {

        this.consultaFaturamentoRecebidoComChequeDevolvido(empresa, listaTipoProdutoVO,
                periodos, mapaTipoProduto, totalGeral, agrupamento, operador, consultor,
        contaCorrente, ignorarEdicaoPagamento, incluirDevolucaoCheque, null);
    }

    @Override
    public void consultaFaturamentoRecebidoComChequeDevolvido(Integer empresa, List<FaturamentoSinteticoTipoProdutoVO> listaTipoProdutoVO,
                                                              List<PeriodoMensal> periodos, Map<String, Boolean> mapaTipoProduto,
                                                              FaturamentoSinteticoProdutoVO totalGeral,String agrupamento,
                                                              Integer operador, Integer consultor, boolean contaCorrente, boolean ignorarEdicaoPagamento, boolean incluirDevolucaoCheque,
                                                              Map<String, Integer> filtroFaturamentoRecebido) throws Exception {

        Map<PeriodoMensal, FaturamentoSinteticoProdutoMesVO> gerais = new HashMap<PeriodoMensal, FaturamentoSinteticoProdutoMesVO>();
        for (String tipoProduto : mapaTipoProduto.keySet()) {
            Map<String, FaturamentoSinteticoProdutoVO> produtoLinhas = new HashMap<String, FaturamentoSinteticoProdutoVO>();
            FaturamentoSinteticoProdutoVO totalizador = new FaturamentoSinteticoProdutoVO();
            totalizador.setDescricao("TOTALIZADOR");

            if (tipoProduto.trim().isEmpty() && algumMarcado(mapaTipoProduto)) {
                continue;
            } else if (tipoProduto.equals("CC") && !algumMarcado(mapaTipoProduto)) {
                //para que ao desmarcar todos, os produtos de deposito de credito sejam apresentados
            } else if (!mapaTipoProduto.get(tipoProduto)) {
                continue;
            }

            FaturamentoSinteticoTipoProdutoVO tipoProdutoVO = new FaturamentoSinteticoTipoProdutoVO();
            TipoProduto tipoProdutoEnum = TipoProduto.getTipoProdutoCodigo(tipoProduto);
            tipoProdutoVO.setTipoProduto(tipoProdutoEnum == null ?
                    tipoProduto.equals("MARERN") ? "Matrícula, Rematrícula, Renovação" : ""
                    : TipoProduto.getTipoProdutoCodigo(tipoProduto).getDescricao());

            for (PeriodoMensal periodo : periodos) {
                FaturamentoSinteticoProdutoMesVO fatProdMes = gerais.get(periodo);
                if(fatProdMes == null){
                    fatProdMes = new FaturamentoSinteticoProdutoMesVO();
                    fatProdMes.setQtd(0);
                    fatProdMes.setValor(0.0);
                    gerais.put(periodo, fatProdMes);
                }

                String tipos = tipoProduto.isEmpty() ? "" : tipoProduto.equals("MARERN") ? "'MA', 'RE', 'RN'" : "'"+tipoProduto+"'";
                StringBuilder sql;
                if(Objects.isNull(filtroFaturamentoRecebido)) {
                    sql = ThreadDemonstrativoFinanceiro.consultaFaturamentoRecebido(empresa, periodo.getDataInicio(),
                            periodo.getDataTermino(), null, null, false, tipos, false,
                            operador, null, null, false, null, ignorarEdicaoPagamento, null, null);
                } else {
                    sql = ThreadDemonstrativoFinanceiro.consultaFaturamentoRecebido(empresa, periodo.getDataInicio(),
                            periodo.getDataTermino(), null, null, false, tipos, false,
                            operador, null, null, false, null, ignorarEdicaoPagamento, null, null,true, filtroFaturamentoRecebido);
                }

                if(contaCorrente){
                    sql.append(ThreadDemonstrativoFinanceiro.consultaFaturamentoRecebidoCC(empresa, periodo.getDataInicio(),
                            periodo.getDataTermino(), null, tipos, operador, consultor));
                }

                List<LancamentoDF> listaLancamentosDF;
                try (Statement stm = con.createStatement()) {
                    try (ResultSet dadosMovPagamento = stm.executeQuery(sql.toString())) {
                        listaLancamentosDF = new ArrayList<LancamentoDF>();
                        GenericoAtributosVO codigosProdutosCredito = ThreadDemonstrativoFinanceiro.consultarCodigoCategoriaProdutoCredito(con);
                        while (dadosMovPagamento.next()) {
                            if (dadosMovPagamento.getInt("recibopagamento") > 0) {
                                // Pesquisar os produtos que o pagamento pagou.
                                List<ProdutoRatear> listaProdutoRatear = ThreadDemonstrativoFinanceiro.pesquisarProdutosDoPagamento(
                                        dadosMovPagamento.getInt("CodigoMovPagamento"), dadosMovPagamento.getString("produtospagos"), con, "");

                                //Validação para entrar ou não entrar produtos de edicao de pagamento e cheques devolvidos
                                List<ProdutoRatear> listaProdutoRatearNew = new ArrayList<ProdutoRatear>();
                                double valorPagamento = dadosMovPagamento.getDouble("valor");
                                for (ProdutoRatear pr : listaProdutoRatear) {
                                    if (pr.getDescricaoProduto().equals("PAGAMENTO DE CHEQUES DEVOLVIDOS")) {
                                        if (incluirDevolucaoCheque) {
                                            listaProdutoRatearNew.add(pr);
                                        } else {
                                            valorPagamento = Uteis.arredondarForcando2CasasDecimais(valorPagamento - pr.getValorRatear() );
                                        }
                                    } else if (!pr.getDescricaoProduto().equals("PAGAMENTO DE CHEQUES DEVOLVIDOS")) {
                                        listaProdutoRatearNew.add(pr);
                                    }
                                }

                                ThreadDemonstrativoFinanceiro.dividirValorPagamentoPorContrato(listaLancamentosDF,
                                        listaProdutoRatearNew,
                                        valorPagamento,
                                        dadosMovPagamento.getInt("recibopagamento"),
                                        TipoFormaPagto.AVISTA,
                                        dadosMovPagamento.getString("pagDesc"),
                                        dadosMovPagamento.getInt("CodigoMovPagamento"),
                                        dadosMovPagamento.getDate("dataLancamento"),
                                        null, null,
                                        con, true, consultor,dadosMovPagamento.getString("responsavelpagamento"));
                            } else {
                                ThreadDemonstrativoFinanceiro.dividirMovPagamentoContaAluno(listaLancamentosDF,
                                        dadosMovPagamento.getInt("CodigoMovPagamento"),
                                        dadosMovPagamento.getDouble("valor"), TipoFormaPagto.AVISTA,
                                        dadosMovPagamento.getInt("pessoa"), dadosMovPagamento.getString("nomepagador"),
                                        dadosMovPagamento.getInt("codigoempresa"), codigosProdutosCredito,
                                        dadosMovPagamento.getString("responsavelpagamento"), consultor,
                                        null, null,
                                        con);
                            }
                        }
                    }
                }

                Double valorTotal = 0.0;
                Integer qtd = 0;
                int consumidor = 0;
                Contrato contratoDao = new Contrato(con);
                for (LancamentoDF lancamento : listaLancamentosDF) {
                    if(!tipoProduto.isEmpty() && !(lancamento.getTipoProduto().equals(tipoProduto)
                            || (tipoProduto.equals("MARERN")
                            && (lancamento.getTipoProduto().equals("MA")
                            || lancamento.getTipoProduto().equals("RE")
                            || lancamento.getTipoProduto().equals("RN"))))){
                        continue;

                    }

                    String descricao = lancamento.getDescricaoLancamento();
                    if (tipoProduto.equals("PM")) {
                        switch (agrupamento) {
                            case "nomeDuracao":
                                descricao = contratoDao.nomePlano(lancamento.getContrato()) + " - " + contratoDao.duracao(lancamento.getContrato());
                                break;
                            case "nome":
                                descricao = contratoDao.nomePlano(lancamento.getContrato());
                                break;
                            case "duracao":
                                int duracao = contratoDao.duracao(lancamento.getContrato());
                                descricao = duracao + (duracao > 1 ? " meses" : " mês");
                                break;
                            case "tipoContrato":
                                descricao = contratoDao.tipoContrato(lancamento.getContrato());
                                break;
                        }
                    }

                    FaturamentoSinteticoProdutoVO linha = produtoLinhas.get(descricao);
                    if (linha == null) {
                        linha = new FaturamentoSinteticoProdutoVO();
                        linha.setDescricao(descricao);
                        produtoLinhas.put(descricao, linha);
                    }

                    FaturamentoSinteticoProdutoMesVO prdXmes = linha.getMapaMeses().get(periodo);
                    if (prdXmes == null) {
                        prdXmes = new FaturamentoSinteticoProdutoMesVO();
                        prdXmes.setQtd(qtd);
                        prdXmes.setValor(valorTotal);
                        linha.getMapaMeses().put(periodo, prdXmes);
                    }

                    FaturamentoSinteticoProdutoMesVO prdXmesTOTAL = totalizador.getMapaMeses().get(periodo);
                    if (prdXmesTOTAL == null) {
                        prdXmesTOTAL = new FaturamentoSinteticoProdutoMesVO();
                        prdXmesTOTAL.setQtd(qtd);
                        prdXmesTOTAL.setValor(valorTotal);
                        totalizador.getMapaMeses().put(periodo, prdXmesTOTAL);
                    }

                    FaturamentoSinteticoResumoPessoaVO faturamentoPessoa = prdXmes.getMapaResumoPessoa().get(lancamento.getContrato()+"-"+lancamento.getCodigoPessoa()+"-"+lancamento.getVendaAvulsa()+"-"+lancamento.getAulaavulsadiaria()+"-"+lancamento.getPersonal());
                    if (faturamentoPessoa == null) {
                        faturamentoPessoa = new FaturamentoSinteticoResumoPessoaVO();
                        faturamentoPessoa.getCliente().getPessoa().setCodigo(lancamento.getCodigoPessoa());
                        faturamentoPessoa.setValor(lancamento.getValorLancamento());
                        faturamentoPessoa.setFormaPagApresentar(lancamento.getFormaPagApresentar());
                        faturamentoPessoa.getContrato().setCodigo(lancamento.getContrato());
                        faturamentoPessoa.setDataLancamentoMovProduto(lancamento.getDataLancamento());
                        faturamentoPessoa.getProdutoVO().setTipoProduto(lancamento.getTipoProduto());
                        faturamentoPessoa.setNomeProduto(lancamento.getDescricaoLancamento());
                        faturamentoPessoa.setQuantidade(lancamento.getQuantidade());
                        faturamentoPessoa.setNomeResponsavelLancamento(lancamento.getResponsavelLancamento());
                        faturamentoPessoa.setNomeResponsavelRecebimento(lancamento.getResponsavelPagamento());
                        faturamentoPessoa.setProdutoQuitado("");
                        faturamentoPessoa.setDescricaoProduto(descricao);
                        faturamentoPessoa.setNomeModalidadeDiaria(lancamento.getModalidadeDiaria());
                        faturamentoPessoa.setConsumidor(lancamento.getNomePessoa());
                        faturamentoPessoa.setNomeEmpresa(lancamento.getNomeEmpresa());
                        prdXmes.getListaResumoPessoa().add(faturamentoPessoa);
                        prdXmesTOTAL.getListaResumoPessoa().add(faturamentoPessoa);

                        prdXmesTOTAL.setQtd(prdXmesTOTAL.getQtd() + 1);
                        prdXmesTOTAL.setValor(prdXmesTOTAL.getValor() + lancamento.getValorLancamento());

                        prdXmes.setQtd(prdXmes.getQtd() + 1);
                        prdXmes.setValor(prdXmes.getValor() + lancamento.getValorLancamento());

                        fatProdMes.setQtd(fatProdMes.getQtd() + 1);
                        fatProdMes.setValor(fatProdMes.getValor() + lancamento.getValorLancamento());
                        fatProdMes.getListaResumoPessoa().add(faturamentoPessoa);
                        if(!"0-0".equals(lancamento.getContrato()+"-"+lancamento.getCodigoPessoa())){
                            prdXmes.getMapaResumoPessoa().put(lancamento.getContrato()+"-"+lancamento.getCodigoPessoa(), faturamentoPessoa);
                        } else {
                            prdXmes.getMapaResumoPessoa().put("consumidor"+(++consumidor), faturamentoPessoa);
                        }
                    } else {
                        faturamentoPessoa.setValor(faturamentoPessoa.getValor() + lancamento.getValorLancamento());
                        prdXmes.setValor(prdXmes.getValor() + lancamento.getValorLancamento());
                        prdXmesTOTAL.setValor(prdXmesTOTAL.getValor() + lancamento.getValorLancamento());
                        fatProdMes.setValor(fatProdMes.getValor() + lancamento.getValorLancamento());
                    }
                }
            }

            for (FaturamentoSinteticoProdutoVO linha : produtoLinhas.values()) {
                for(PeriodoMensal p : periodos){
                    if (linha.getMapaMeses().get(p) != null) {
                        linha.getListaProdutoXMes().add(linha.getMapaMeses().get(p));
                    }
                }
            }

            for (PeriodoMensal p : periodos) {
                totalizador.getListaProdutoXMes().add(totalizador.getMapaMeses().get(p));
            }

            tipoProdutoVO.getListaProduto().addAll(produtoLinhas.values());
            tipoProdutoVO.setApresentarResultado(!tipoProdutoVO.getListaProduto().isEmpty());
            Ordenacao.ordenarLista(tipoProdutoVO.getListaProduto(), "descricao");
            if(!UteisValidacao.emptyString(tipoProduto)){
                tipoProdutoVO.getListaProduto().add(totalizador);
            }

            listaTipoProdutoVO.add(tipoProdutoVO);
        }

        for (PeriodoMensal p : periodos) {
            totalGeral.getListaProdutoXMes().add(gerais.get(p));
        }

        //Pós processamento para zerar os campos.
        for (FaturamentoSinteticoTipoProdutoVO fstpVO : listaTipoProdutoVO) {
            for (FaturamentoSinteticoProdutoVO fatSin: fstpVO.getListaProduto()) {
                if (fatSin.getMapaMeses().size() >= periodos.size()) {
                    continue;
                }

                for (int i = 0; i < periodos.size(); i++) {
                    PeriodoMensal periodoMensal = periodos.get(i);
                    FaturamentoSinteticoProdutoMesVO fatSPMVO = fatSin.getMapaMeses().get(periodoMensal);
                    if (fatSPMVO != null) {
                        continue;
                    }

                    fatSPMVO = new FaturamentoSinteticoProdutoMesVO();
                    fatSPMVO.setQtd(0);
                    fatSPMVO.setValor(0.0);
                    fatSin.getMapaMeses().put(periodoMensal, fatSPMVO);
                    fatSin.getListaProdutoXMes().add(i, fatSPMVO);
                }
            }
        }
    }

    public List<MovProdutoVO> consultarProdutoPorPessoaDataCompra(Integer codigoPessoa, Integer codigoProduto, Date dataCompra, int nivelMontarDados) throws Exception {
        String sql = "SELECT\n" +
                "  *\n" +
                "FROM movproduto\n" +
                "WHERE 1 = 1\n" +
                "      AND datalancamento :: DATE = '" + Uteis.getDataJDBC(dataCompra) + "'\n" +
                "      AND pessoa = " + codigoPessoa + "\n" +
                "      AND produto = " + codigoProduto + ";";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }
    }

    public MovProdutoVO consultarPorVendaAvulsa(Integer vendaavulsa) throws Exception{
        try (ResultSet rs = criarConsulta("SELECT * FROM movproduto WHERE vendaavulsa = " + vendaavulsa, con)) {
            if (rs.next()) {
                return montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
            }
        }
        return null;
    }

    public List<MovProdutoVO> consultarTodosPorVendaAvulsa(Integer vendaavulsa) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT * FROM movproduto WHERE vendaavulsa = " + vendaavulsa, con)) {
            return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, con);
        }
    }

    public MovProdutoVO getMontarMovProdutoPorTipo(String descricaoTipo, String codigoTipo, Double valor, PessoaVO pessoaVO, ContratoVO contratoVO, EmpresaVO empresaVO, UsuarioVO usuarioVO ) throws Exception {
        Produto produtoDAO = new Produto(con);
        ProdutoVO multaJurosVO = produtoDAO.criarOuConsultarExisteProdutoPorTipo(descricaoTipo, codigoTipo, 0.0);
        produtoDAO = null;

        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setDescricao(multaJurosVO.getDescricao());
        movProdutoVO.setProduto(multaJurosVO);
        movProdutoVO.setTotalFinal(valor);
        movProdutoVO.setPrecoUnitario(valor);
        movProdutoVO.setSituacao("EA");
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(Calendario.hoje()));
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(Calendario.hoje()));
        movProdutoVO.setDataLancamento(Calendario.hoje());
        movProdutoVO.setQuantidade(1);

        movProdutoVO.setContrato(contratoVO);
        movProdutoVO.setEmpresa(empresaVO);
        movProdutoVO.setPessoa(pessoaVO);

        movProdutoVO.setResponsavelLancamento(usuarioVO);

        return movProdutoVO;
    }

    public MovProdutoVO consultaMovProdutoEmAberto(TipoProduto tipoProduto,Integer codigoContrato, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from movProduto mov \n");
        sql.append("inner join produto prod on prod.codigo = mov.produto \n");
        sql.append("where prod.tipoProduto = '").append(tipoProduto.getCodigo()).append("' \n");
        sql.append(" and situacao = 'EA' \n");
        sql.append(" and mov.contrato = ").append(codigoContrato);
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }

    public List<MovProdutoVO> consultarPorMovPagamento(Integer codigoMovPagamento, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from movProduto where codigo in(select movProduto from movProdutoParcela where movParcela in(select movParcela from pagamentoMovParcela where movPagamento = ").append(codigoMovPagamento).append(" ))");
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }
    }

    private boolean existeTipoProdutoDiariaAulaPersonal(List<String> tipoProduto) {
        for (String tipo : tipoProduto) {
            if (tipo.equals("AA") || tipo.equals("DI") || tipo.equals("TP")) {
                return true;
            }
        }
        return false;
    }
    private ResultSet montarSqlProdutoCliente(Integer pessoa,Integer contrato,boolean count,int limit,int offset) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        if(count){
            sql.append(" COUNT(distinct movproduto.codigo) as total ");
        } else {
            sql.append(" distinct on (movproduto.codigo) movproduto.codigo, movproduto.empresa, emp.nome, movproduto.contrato, movproduto.descricao, datalancamento, pessoa, quantidade, precounitario, ");
            sql.append(" valordesconto, totalfinal, situacao, p.tipoproduto, p.codigo as codigoproduto , numeroCupomDesconto, statusprotheus, linknota, oo.dataoperacao as datacancelamento, rp.data as dataPagamento ");
        }
        sql.append(" FROM movproduto ");
        sql.append(" INNER JOIN produto p ON p.codigo = movproduto.produto ");
        sql.append(" INNER JOIN empresa emp ON movproduto.empresa = emp.codigo ");
        sql.append(" LEFT JOIN movprodutoparcela mpp ON mpp.movproduto =  movproduto.codigo ");
        sql.append(" LEFT JOIN recibopagamento rp on rp.codigo = mpp.recibopagamento" );
        sql.append(" LEFT JOIN observacaooperacao oo ON oo.movparcela = mpp.movparcela ");
        sql.append("                                AND oo.tipooperacao = 'PC' ");
        sql.append(" WHERE pessoa = ").append(pessoa);
        sql.append("  AND p.codigo NOT IN (SELECT produto FROM perfileventoproduto) ");
        if(!UteisValidacao.emptyNumber(contrato)){
            sql.append(" AND movproduto.contrato = ").append(contrato);
        }
        if(!count) {
            sql.append(" ORDER BY movproduto.codigo DESC");
        }
        if(!count && !UteisValidacao.emptyNumber(limit)){
            sql.append(" limit ").append(limit).append(" offset ").append(offset);
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        return rs;
    }
    public Integer obterCountProdutosCliente(Integer pessoa,Integer contrato) throws Exception{
        try (ResultSet rs = montarSqlProdutoCliente(pessoa, contrato, true, 0, 0)) {
            while (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }
    public List<MovProdutoVO> consultarTelaCliente(Integer pessoa,Integer contrato, Integer limit,Integer offset) throws Exception{
        List<MovProdutoVO> lista;
        try (ResultSet rs = montarSqlProdutoCliente(pessoa, contrato, false, limit, offset)) {
            lista = new ArrayList<MovProdutoVO>();
            while (rs.next()) {
                MovProdutoVO movProduto = new MovProdutoVO();
                movProduto.setCodigo(rs.getInt("codigo"));
                movProduto.setEmpresa(new EmpresaVO());
                movProduto.getEmpresa().setCodigo(rs.getInt("empresa"));
                movProduto.getEmpresa().setNome(rs.getString("nome"));
                movProduto.setContrato(new ContratoVO());
                movProduto.getContrato().setCodigo(rs.getInt("contrato"));
                movProduto.setDescricao(rs.getString("descricao"));
                movProduto.setDataLancamento(rs.getTimestamp("datalancamento"));
                movProduto.setPessoa(new PessoaVO());
                movProduto.getPessoa().setCodigo(rs.getInt("pessoa"));
                movProduto.setQuantidade(rs.getInt("quantidade"));
                movProduto.setPrecoUnitario(rs.getDouble("precounitario"));
                movProduto.setValorDesconto(rs.getDouble("valordesconto"));
                movProduto.setTotalFinal(rs.getDouble("totalfinal"));
                movProduto.setSituacao(rs.getString("situacao"));
                movProduto.setProduto(new ProdutoVO());
                movProduto.getProduto().setCodigo(rs.getInt("codigoproduto"));
                movProduto.getProduto().setTipoProduto(rs.getString("tipoproduto"));
                movProduto.setNumeroCupomDesconto(rs.getString("numeroCupomDesconto"));
                movProduto.setStatusProtheus(rs.getString("statusprotheus"));
                movProduto.setLinkNota(rs.getString("linknota"));
                movProduto.setDataCancelamento(rs.getDate("datacancelamento"));
                movProduto.setDataPagamento(rs.getDate("datapagamento"));
                if (movProduto.getSituacao().equals("EA")) {
                    obterValorParcialmentPago(movProduto);
                }
                if (movProduto.getProduto().getTipoProduto().equals("SS") && movProduto.getSituacao().equals("CA")) {
                    ReciboDevolucao recDev = new ReciboDevolucao(con);
                    movProduto.setReciboDevolucao(recDev.consultarPorMovProduto(movProduto.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    recDev = null;
                }
                lista.add(movProduto);
            }
        }
        return lista;
    }

    public Integer incluirLista(List<MovProdutoVO> lista) throws Exception{
        for(MovProdutoVO obj : lista){
            this.incluirSemCommit(obj);
        }
        return obterValorChavePrimariaCodigo();

    }

    public List consultarPorVendidoJuntoAoContratoOrdenado(Integer codigoContrato, boolean incluirParcelaAnuidade, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder tiposProduto = new StringBuilder();
        tiposProduto.append("'PM', 'MA', 'RE', 'RN', 'AH', 'MM', 'TD'");
        if (incluirParcelaAnuidade){
            tiposProduto.append(",'").append(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo()).append("'");
        }
        String sqlStr = " SELECT * FROM movproduto WHERE movproduto.codigo in  ("
                + " select movproduto from movprodutoparcela where movparcela in(\n" +
                    "select   distinct movparcela.codigo \n" +
                    "from movparcela inner join movprodutoparcela on movprodutoparcela.movparcela =  movparcela.codigo \n" +
                    "inner join movproduto on movprodutoparcela.movproduto = movproduto.codigo \n" +
                    "inner join produto on movproduto.produto = produto.codigo and produto.tipoproduto in( "+tiposProduto+") \n" +
                    "where movproduto.contrato = "+ codigoContrato +" ))"
                + "  ORDER BY anoreferencia, mesreferencia";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public MovProdutoVO criarMovProdutoMultaJuros(MovParcelaVO movParcelaAtrasada, VendaAvulsaVO vendaAvulsaVO, double multa, double juros, UsuarioVO usuarioVO, Double multiplicadorMultaJuros) throws Exception {
        TipoProduto tipoMultaJuros = TipoProduto.MULTA_JUROS;
        Produto produtoDAO = new Produto(con);
        ProdutoVO produtoMultaJuros = produtoDAO.criarOuConsultarExisteProdutoPorTipo(tipoMultaJuros.getDescricao(), tipoMultaJuros.getCodigo(), 0.0);
        produtoDAO = null;

        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setProduto(produtoMultaJuros);
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao(tipoMultaJuros.getDescricao() + " - Parcela " + movParcelaAtrasada.getCodigo());
        movProdutoVO.setQuantidade(1);
        movProdutoVO.setMesReferencia(Uteis.getDataMesAnoConcatenado(Calendario.hoje()));
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(Calendario.hoje()));
        movProdutoVO.setDataInicioVigencia(Calendario.hoje());
        movProdutoVO.setDataFinalVigencia(Calendario.hoje());
        movProdutoVO.setDataLancamento(Calendario.hoje());
        movProdutoVO.setResponsavelLancamento(usuarioVO);
        movProdutoVO.setPrecoUnitario(0.0);
        if (multiplicadorMultaJuros < 1) {
            double desconto = Uteis.arredondarForcando2CasasDecimais((multa + juros) * (1 - multiplicadorMultaJuros));
            movProdutoVO.setValorDesconto(desconto);
            movProdutoVO.setMulta(Uteis.arredondarForcando2CasasDecimais(multa * multiplicadorMultaJuros));
            movProdutoVO.setJuros(Uteis.arredondarForcando2CasasDecimais(juros * multiplicadorMultaJuros));
        } else {
            movProdutoVO.setValorDesconto(0.0);
            movProdutoVO.setMulta(multa);
            movProdutoVO.setJuros(juros);
        }
        movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getMulta() + movProdutoVO.getJuros()));
        movProdutoVO.setVendaAvulsa(vendaAvulsaVO.getCodigo());
        movProdutoVO.setEmpresa(movParcelaAtrasada.getEmpresa());
        movProdutoVO.setPessoa(movParcelaAtrasada.getPessoa());

        movProdutoVO.setSituacao("EA");
        movProdutoVO.setQuitado(true);
        return movProdutoVO;
    }

    public void alterarDevolucaoCheque(ChequeVO chequeVO)throws Exception{
        String[] split = chequeVO.getProdutosPagos().split("\\|");
        try (PreparedStatement pst = con.prepareStatement("update movProduto set chequeDevolucao = ? where codigo = ?")) {
            for (String str : split) {
                if (!str.isEmpty() && !str.equals("null")) {
                    String[] produto = str.split(",");
                    pst.setInt(1, chequeVO.getCodigo());
                    pst.setInt(2, Integer.parseInt(produto[0]));
                    pst.execute();
                }
            }
        }

    }

    public JSONArray consultarProdutosVigenciaCliente(Integer cliente, Integer produto, Boolean retornarQuantidade) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT mp.codigo, mp.quantidade FROM movproduto mp ");
        sql.append("inner join cliente cli on cli.pessoa = mp.pessoa ");
        sql.append("WHERE cli.codigo = ? ");
        sql.append("AND produto = ? ");
        sql.append("AND mp.situacao = 'PG' ");
        sql.append("AND to_date(to_char(now(), 'yyyy-MM-dd'), 'yyyy-MM-dd') BETWEEN mp.datainiciovigencia AND mp.datafinalvigencia ");
        JSONArray array;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setInt(1, cliente);
            stm.setInt(2, produto);
            try (ResultSet rs = stm.executeQuery()) {
                array = new JSONArray();
                while (rs.next()) {
                    if (retornarQuantidade) {
                        JSONObject json = new JSONObject();
                        json.put("produtoId", rs.getInt("codigo"));
                        json.put("quantidade", rs.getInt("quantidade"));
                        array.put(json);
                    } else {
                        array.put(rs.getInt("codigo"));
                    }
                }
            }
        }
        return array;
    }

    public List<DescontoTO> consultarDescontos(Date inicio, Date fim, Integer empresa, List<ColaboradorVO> consultores) throws SQLException {
        List<DescontoTO> descontos = new ArrayList<DescontoTO>();
        try (ResultSet resultSet = consultarDescontos(inicio, fim, empresa, false, consultores)) {
            while (resultSet.next()) {
                DescontoTO descontoTO = new DescontoTO();
                descontoTO.setMoeda(resultSet.getString("moeda"));
                descontoTO.setDescricao(resultSet.getString("descricao"));
                descontoTO.setUsuario(Uteis.getNomeAbreviado(resultSet.getString("usuario")));
                descontoTO.setMatricula(resultSet.getString("matricula"));
                descontoTO.setCliente(Uteis.getNomeAbreviado(resultSet.getString("cliente")));
                descontoTO.setContrato(resultSet.getInt("contrato"));
                descontoTO.setLancamento(resultSet.getDate("datalancamento"));
                descontoTO.setValor(resultSet.getDouble("valor"));
                descontoTO.setConvenio(resultSet.getString("convenio"));
                descontos.add(descontoTO);
            }
        }
        return descontos;
    }

    public Double consultarTotalDescontos(Date inicio, Date fim, Integer empresa, List<ColaboradorVO> consultores) throws SQLException{
        try (ResultSet resultSet = consultarDescontos(inicio, fim, empresa, true, consultores)) {
            while (resultSet.next()) {
                return resultSet.getDouble("total");
            }
        }
        return 0.0;
    }

    public ResultSet consultarDescontos(Date inicio, Date fim, Integer empresa, boolean sum, List<ColaboradorVO> consultores) throws SQLException {
        StringBuilder sql = new StringBuilder();
        if(sum){
            sql.append(" select sum(desconto) as total ");
        }else{
            sql.append(" select descricao, usuario, matricula, cliente, contrato, datalancamento, sum(desconto) as valor, convenio, moeda ");
        }

        sql.append(" from (select emp.moeda, p.descricao,");
        sql.append("        u.nome        AS usuario,");
        sql.append("        cli.matricula,");
        sql.append("        pes.nome      AS cliente,");
        sql.append("        mp.contrato,");
        sql.append("        mp.datalancamento::date,");
        sql.append("         CASE when p.tipoproduto = 'DE' THEN mp.totalfinal else mp.valordesconto END AS desconto,");
        sql.append("  c2.descricao as convenio");
        sql.append("         from movproduto mp");
        sql.append(" INNER JOIN produto p ON mp.produto = p.codigo");
        sql.append(" INNER JOIN usuario u ON mp.responsavellancamento = u.codigo");
        sql.append(" INNER JOIN cliente cli ON mp.pessoa = cli.pessoa");
        sql.append(" INNER JOIN pessoa pes ON pes.codigo = mp.pessoa");
        sql.append(" INNER JOIN empresa emp ON emp.codigo = mp.empresa");
        sql.append(" LEFT JOIN contrato con ON mp.contrato = con.codigo");
        sql.append(" LEFT JOIN conveniodesconto c2 ON con.conveniodesconto = c2.codigo");
        sql.append(" LEFT JOIN contratoduracao cd ON con.codigo = cd.contrato");
        sql.append(" LEFT JOIN contratoduracaocreditotreino cdct ON cd.codigo = cdct.contratoduracao");
        sql.append(" where mp.datalancamento BETWEEN ? AND ?  ");
        sql.append(" and pes.codigo is not null ");
        sql.append(" and ((mp.valordesconto > 0 and (c2.codigo is not null OR mp.contrato is null OR (p.tipoproduto <> 'DE' AND p.tipoproduto <> 'PM'))) OR (p.tipoproduto = 'DE' and c2.codigo is null))");

        boolean filtrarColaborador = false;
        String codColaboradores = "";
        for (ColaboradorVO colaborador : consultores) {
            if (colaborador.getColaboradorEscolhidoOperacoes()) {
                codColaboradores = codColaboradores + "," + colaborador.getCodigo();
                filtrarColaborador = true;
            }

        }
        if(filtrarColaborador){
            sql.append("AND u.colaborador IN (" + codColaboradores.replaceFirst(",", "") + ")");
        }

        if(empresa != 0){
            sql.append(" and mp.empresa = ?" );
        }
        sql.append("union all\n");
        sql.append("select\n");
        sql.append("'' as moeda,\n");
        sql.append("p.descricao,\n");
        sql.append("u.nome as usuario,\n");
        sql.append("'' as matricula,\n");
        sql.append("'CONSUMIDOR' as cliente,\n");
        sql.append("mp.contrato,\n");
        sql.append("mp.datalancamento::date,\n");
        sql.append("mp.valordesconto ,\n");
        sql.append("'' as convenio\n");
        sql.append("from\n");
        sql.append("movproduto mp\n");
        sql.append("inner join produto p on\n");
        sql.append("mp.produto = p.codigo\n");
        sql.append("inner join usuario u on\n");
        sql.append("mp.responsavellancamento = u.codigo\n");
        sql.append("where\n");
        sql.append("mp.datalancamento BETWEEN ? AND ? \n");
        sql.append("and mp.valordesconto > 0\n");
        sql.append("and mp.pessoa is null\n");

        if(empresa != 0) {
            sql.append(" and mp.empresa = ?");
        }
        sql.append(" ) as query");
        sql.append(sum ? "" :" group by 1,2,3,4,5,6,8,9");
        if(!sum){
            sql.append(" order by datalancamento ");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 0;
        stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(inicio));
        stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Uteis.getDataComUltimaHora(fim)));
        if (empresa != 0) {
            stm.setInt(++i, empresa);
        }
        stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(inicio));
        stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Uteis.getDataComUltimaHora(fim)));
        if (empresa != 0) {
            stm.setInt(++i, empresa);
        }
        return stm.executeQuery();
    }

    public boolean existeProdutoPagoDevolucaoCheque(int codigoCheque) throws SQLException{
        String sqlStr = "SELECT exists (select codigo from movproduto where chequedevolucao = "+codigoCheque+" and situacao = 'PG') as existe";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }

    }

    public boolean alterarValorFaturado() throws Exception {
        String sql = "UPDATE movproduto SET valorfaturado = totalfinal";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            preparedStatement.execute();
        }
        return true;
    }

    @Override
    public void alterarMultaJurosNaoRecebidos(MovProdutoVO movProdutoVO) throws SQLException {
        String sql = "update movproduto " +
                "set jurosnaorecebidos = "+movProdutoVO.getJurosNaoRecebidos()+", " +
                "    multanaorecebida  = "+movProdutoVO.getMultaNaoRecebida()+" " +
                "WHERE codigo = "+movProdutoVO.getCodigo();

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.execute();
        }
    }

    @Override
    public Double obterValorCheioProdutoMatriculaContratoPessoa(Integer pessoa, Integer contrato, boolean buscarValorDesconto) throws SQLException {
        Double valor = new Double(0.0);
        StringBuilder sql = new StringBuilder();
        //montar consulta
        if(buscarValorDesconto){
            sql.append("SELECT MP.valordesconto ");
        }else{
            sql.append("SELECT MP.precounitario ");
        }
        sql.append("  FROM movproduto MP, pessoa P, contrato C, produto PR ");
        sql.append("WHERE MP.produto = PR.codigo AND MP.contrato = C.codigo AND MP.pessoa = P.codigo ");
        sql.append(" AND PR.tipoproduto LIKE 'MA'");
        sql.append(" AND P.codigo = ?");
        sql.append(" AND C.codigo = ?");
        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 0;
        dc.setInt(++i, pessoa);
        dc.setInt(++i, contrato);
        try (ResultSet rs = dc.executeQuery()) {
            if (rs.next()) {
                if (buscarValorDesconto) {
                    valor = rs.getDouble("valordesconto");
                } else {
                    valor = rs.getDouble("precounitario");
                }
            }
        }
        return valor;
    }

    @Override
    public Double obterValorCheioProdutoRematriculaContratoPessoa(Integer pessoa, Integer contrato, boolean buscarValorDesconto) throws SQLException {
        Double valor = new Double(0.0);
        StringBuilder sql = new StringBuilder();
        //montar consulta
        if(buscarValorDesconto){
            sql.append("SELECT MP.valordesconto ");
        }else{
            sql.append("SELECT MP.precounitario ");
        }
        sql.append(" FROM movproduto MP, pessoa P, contrato C, produto PR ");
        sql.append("WHERE MP.produto = PR.codigo AND MP.contrato = C.codigo AND MP.pessoa = P.codigo ");
        sql.append(" AND PR.tipoproduto LIKE 'RE'");
        sql.append(" AND P.codigo = ?");
        sql.append(" AND C.codigo = ?");
        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 0;
        dc.setInt(++i, pessoa);
        dc.setInt(++i, contrato);
        try (ResultSet rs = dc.executeQuery()) {
            if (rs.next()) {
                if (buscarValorDesconto) {
                    valor = rs.getDouble("valordesconto");
                } else {
                    valor = rs.getDouble("precounitario");
                }
            }
        }
        return valor;
    }

    @Override
    public Double obterValorDescontoAnuidadeRecorrenciaContratoPessoa(Integer pessoa, Integer contrato, boolean buscarValorFinal) throws SQLException {
        Double valor = new Double(0.0);
        StringBuilder sql = new StringBuilder();
        //montar consulta
        if(buscarValorFinal){
            sql.append("SELECT MP.totalfinal ");
        }else{
            sql.append("SELECT MP.valordesconto ");
        }
        sql.append(" FROM movproduto MP, pessoa P, contrato C, produto PR ");
        sql.append("WHERE MP.produto = PR.codigo AND MP.contrato = C.codigo AND MP.pessoa = P.codigo ");
        sql.append(" AND PR.tipoproduto LIKE 'TA'");
        sql.append(" AND P.codigo = ?");
        sql.append(" AND C.codigo = ?");
        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 0;
        dc.setInt(++i, pessoa);
        dc.setInt(++i, contrato);
        try (ResultSet rs = dc.executeQuery()) {
            if (rs.next()) {
                if (buscarValorFinal) {
                    valor = rs.getDouble("totalfinal");
                } else {
                    valor = rs.getDouble("valordesconto");
                }
            }
        }
        return valor;
    }

    @Override
    public boolean verificarDesafioVigente(AlunoHorarioTurmaVO alunoHorario) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select m.*");
        sql.append("  from movproduto m");
        sql.append("  join produto p on p.codigo = m.produto");
        sql.append("                and p.tipoproduto = 'DS'");
        sql.append(" where m.pessoa = (select pessoa from cliente cli where cli.codigo = ?)");
        sql.append("   and '" + alunoHorario.getData() + "' between m.datainiciovigencia and m.datafinalvigencia");
        sql.append("   and m.situacao <> 'CA'");
        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 0;
        dc.setInt(++i, alunoHorario.getCliente());
        ResultSet rs = dc.executeQuery();
        return rs.next();
    }

    public void incluirListaMovProdutos(List<MovProdutoVO> lista) throws Exception{
        for(MovProdutoVO obj : lista){
            this.incluirSemCommit(obj);
        }
    }

    public MovProdutoVO consultarAvaliacaoVigente(Integer codCliente, Date dataAvaliacao, int nivelMontarDados) throws Exception {
        String sql = "SELECT mprod.* FROM movproduto mprod\n" +
                "INNER JOIN produto prod ON mprod.produto = prod.codigo\n" +
                "INNER JOIN categoriaproduto cp ON prod.categoriaproduto = cp.codigo\n" +
                "INNER JOIN cliente cli ON mprod.pessoa = cli.pessoa\n" +
                "WHERE cp.avaliacaofisica = true\n" +
                "AND '" + Uteis.getDataJDBC(dataAvaliacao) + "' BETWEEN mprod.datainiciovigencia AND mprod.datafinalvigencia\n" +
                "AND mprod.situacao = 'PG'\n" +
                "AND cli.codigo = " + codCliente + "\n" +
                "order by datalancamento\n" +
                "LIMIT 1";


        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sql)) {
            if (tabelaResultado.next()) {
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
        return null;
    }

    public void excluirPorParcela(Integer movparcela) throws SQLException {
        String sql = "DELETE FROM movproduto " +
                "WHERE codigo IN (SELECT movproduto FROM movprodutoparcela WHERE movparcela = "+movparcela+")";
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    public List<MovProdutoVO> consultarProdutoParaRenovacaoAutomatica(Date dataAvaliar, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT mp.* FROM MovProduto mp ");
        sb.append(" left join Produto pro on mp.produto = pro.codigo");
        sb.append(" WHERE mp.renovavelAutomaticamente "); //mopproduto deve estar configurado para renovação, podendo usuário marcar um movproduto especifico para não renovar
        sb.append(" and  pro.renovavelAutomaticamente ");
        sb.append(" and  pro.desativado = false ");
        sb.append(" and  pro.tipovigencia  = 'ID' "); // só para produtos com vigencia de intervalo de dias
        sb.append(" and mp.empresa = ").append(empresa);
        sb.append(" and mp.datafinalvigencia between  '").append(Uteis.getDataJDBC(Uteis.somarDias(dataAvaliar, -1))).append(" 00:00:00'");
        sb.append(" AND  '").append(Uteis.getDataJDBC(Uteis.somarDias(dataAvaliar,1))).append(" 23:59:59'");
        sb.append(" and  not exists (select codigo from movproduto mpfuturo ");
        sb.append(" where mpfuturo.pessoa = mp.pessoa and mp.produto = mpfuturo.produto and mp.empresa =mpfuturo.empresa ");
        sb.append(" and mpfuturo.datafinalvigencia::date > mp.datafinalvigencia::date) ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public void alterarPermiteRenovacaoAutomaticaMovProduto(MovProdutoVO movProdutoVO,
                                                            boolean renovarAutomaticamente,
                                                            UsuarioVO usuarioVO) throws Exception {
        try {
            con.setAutoCommit(false);

            String sqlStr = "Update movproduto set renovavelAutomaticamente = " + renovarAutomaticamente + " where codigo = " + movProdutoVO.getCodigo();
            try (Statement stm = con.createStatement()) {
                stm.execute(sqlStr);
            }
            gerarLogAlterarRenovacaoAutomaticaMovproduto(movProdutoVO, usuarioVO);

            con.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void gerarLogAlterarRenovacaoAutomaticaMovproduto(MovProdutoVO movProdutoVO, UsuarioVO usuario) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(this.con);
            LogVO log = new LogVO();
            log.setNomeEntidade("MOVPRODUTO");
            log.setDescricao("ALTERAR RENOVAÇÃO AUTOMÁTICA DO PRODUTO " + movProdutoVO.getCodigo() + " -----------");
            log.setChavePrimaria(movProdutoVO.getCodigo().toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuario);
            log.setResponsavelAlteracao(usuario.getNomeAbreviado());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("RENOVAR PRODUTO AUTOMATICAMENTE " + movProdutoVO.getCodigo() + " -----------");
            log.setUserOAMD(usuario.getUserOamd());
            log.setPessoa(movProdutoVO.getPessoa().getCodigo());
            log.setValorCampoAnterior(movProdutoVO.getRenovavelAutomaticamente() ? "SIM" : "NÃO");
            log.setValorCampoAlterado(movProdutoVO.getRenovavelAutomaticamente() ? "NÃO" : "SIM");
            logDAO.incluirSemCommit(log);
        } finally {
            logDAO = null;
        }
    }

    public List<String[]> consultarParaInsertCartaoCreditoProdutosDiariaImportacao() throws Exception {
        List<String[]> listaArraysDadosAjustar = new ArrayList<>();

        //Sql para buscar dados para ajuste
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT mp.codigo AS codigoMovProduto, mpg.operadoracartao, mpg.valor, mpg.datalancamento, mpg.nrparcelacartaocredito, mpg.codigo AS codigoMovPagamento \n");
        sb.append("FROM movproduto mp \n");
        sb.append("INNER JOIN movprodutoparcela mpp ON mpp.movproduto = mp.codigo \n");
        sb.append("INNER JOIN movpagamento mpg ON mpg.recibopagamento = mpp.recibopagamento \n");
        sb.append("INNER JOIN formapagamento f ON f.codigo = mpg.formapagamento \n");
        sb.append("WHERE mp.produto = 7169 \n"); //7169 é o Id do produto DIARIA IMPORTAÇÃO
        sb.append("AND mp.situacao = '").append(SituacaoParcelaEnum.PAGO.getCodigo()).append("' \n");
        sb.append("AND mpg.produtospagos ILIKE '' \n"); //Só gerar para MovProduto que não foi preenchido na tabela MovPagamento
        sb.append("AND f.tipoformapagamento = '").append(TipoFormaPagto.CARTAOCREDITO.getSigla()).append("';");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                while (tabelaResultado.next()){
                    String[] arrayDados = new String[6];

                    Date dataCompensacao = new Date();
                    dataCompensacao = tabelaResultado.getTimestamp("dataLancamento");
                    dataCompensacao = Calendario.somarDias(dataCompensacao, 30);
                    arrayDados[0] = Calendario.getData(dataCompensacao, "yyyy-MM-dd hh:mm:ss");

                    arrayDados[1] = String.valueOf(tabelaResultado.getInt("codigoMovPagamento"));
                    arrayDados[2] = String.valueOf(tabelaResultado.getDouble("valor"));
                    arrayDados[3] = String.valueOf(tabelaResultado.getInt("operadoracartao"));

                    StringBuilder produtosPagos = new StringBuilder();
                    produtosPagos.append("|").append(tabelaResultado.getInt("codigoMovProduto")).append(",").append("DI,0,").append(tabelaResultado.getDouble("valor"));
                    arrayDados[4] = produtosPagos.toString();

                    arrayDados[5] = String.valueOf(tabelaResultado.getInt("nrparcelacartaocredito"));

                    listaArraysDadosAjustar.add(arrayDados);
                }
            }
        }
        return listaArraysDadosAjustar;
    }

    public boolean existeMovprodutoPorProdutoAPartirDe(Integer codProduto, Date data) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM movproduto\n");
        sql.append("WHERE produto = ").append(codProduto).append("\n");
        sql.append("AND datalancamento >= '").append(Uteis.getDataJDBC(data)).append("'");
        return existe(sql.toString(), con);
    }
}
