/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.contrato;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.CancelamentoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoTextoPadraoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.contrato.ContratoAssinaturaDigitalInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ContratoAssinaturaDigital extends SuperEntidade implements ContratoAssinaturaDigitalInterfaceFacade {

    public ContratoAssinaturaDigital() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public ContratoAssinaturaDigital(Connection con) throws Exception {
        super(con);
        setIdEntidade("Contrato");
    }

    @Override
    public void incluir(ContratoAssinaturaDigitalVO obj) throws Exception {
        String sql = "INSERT INTO ContratoAssinaturaDigital(contrato, usuarioresponsavel, contratotextopadrao,"
                + "documentos, endereco, assinatura, atestado, anexo1, anexo2, lancamento, anexoCancelamento, assinatura2, contratoaditivo, aditivo, ehaditivo, ip, tipoAutenticacao, dadosAutenticacao) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int i = 1;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getContrato() == null) {
            sqlInserir.setNull(i++, Types.NULL);
        } else {
            sqlInserir.setInt(i++, obj.getContrato().getCodigo());
        }
        sqlInserir.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        if (obj.getContratoTextoPadrao() != null && UteisValidacao.emptyNumber(obj.getContratoTextoPadrao().getCodigo())) {
            sqlInserir.setNull(i++, Types.NULL);
        } else {
            sqlInserir.setInt(i++, obj.getContratoTextoPadrao().getCodigo());
        }
        sqlInserir.setString(i++, obj.getDocumentos());
        sqlInserir.setString(i++, obj.getEndereco());
        sqlInserir.setString(i++, obj.getAssinatura());
        sqlInserir.setString(i++, obj.getAtestado());
        sqlInserir.setString(i++, obj.getAnexo1());
        sqlInserir.setString(i++, obj.getAnexo2());
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.setString(i++, obj.getAnexoCancelamento());
        sqlInserir.setString(i++, obj.getAssinatura2());
        if (obj.getContratoAditivo() == null) {
            sqlInserir.setNull(i++, 0);
        } else {
            sqlInserir.setInt(i++, obj.getContratoAditivo().getCodigo());
        }
        if (obj.getAditivo() != null) {
            sqlInserir.setInt(i++, obj.getAditivo().getCodigo());
        } else {
            sqlInserir.setNull(i++, Types.NULL);
        }
        sqlInserir.setBoolean(i++, obj.isEhAditivo());
        sqlInserir.setString(i++, obj.getIp());
        sqlInserir.setString(i++, obj.getTipoAutenticacao());
        sqlInserir.setString(i++, obj.getDadosAutenticacao());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        if (obj.getAditivo() == null) {
            new ContratoTextoPadrao(con).alterarAssinaturaHtmlContrato(obj.getContrato().getCodigo());
        } else {
            new ContratoTextoPadrao(con).alterarAssinaturaHtmlContratoAditivo(obj.getContratoAditivo().getCodigo(), obj.getAditivo().getCodigo());
        }
    }

    public String obterPorContrato(Integer contrato, boolean assinatura2) throws Exception {
        String sql = " SELECT assinatura, assinatura2 from contratoassinaturadigital WHERE contrato = " + contrato;
        ResultSet rs = criarConsulta(sql, con);
        if (rs.next()) {
            if (assinatura2) {
                return rs.getString("assinatura2");
            }
            return rs.getString("assinatura");
        }
        return null;
    }

    public String obterPorContratoEAditivo(Integer contrato, boolean assinatura2, Integer aditivo) throws Exception {
        String sql = " SELECT assinatura, assinatura2 from contratoassinaturadigital WHERE contrato = " + contrato + " and aditivo = " + aditivo;
        ResultSet rs = criarConsulta(sql, con);
        if (rs.next()) {
            if (assinatura2) {
                return rs.getString("assinatura2");
            }
            return rs.getString("assinatura");
        }
        return null;
    }

    @Override
    public void excluir(ContratoAssinaturaDigitalVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoAssinaturaDigital WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public void excluirPorContrato(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoAssinaturaDigital WHERE contrato = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, contrato);
        sqlExcluir.execute();
    }

    @Override
    public void alterar(ContratoAssinaturaDigitalVO obj) throws Exception {
        String sql = "UPDATE ContratoAssinaturaDigital set contrato = ?, usuarioresponsavel = ?,"
                + "documentos = ?, endereco = ?,  assinatura = ?, atestado = ?, anexo1 = ?, anexo2 = ?, assinatura2 = ?, contratoaditivo = ?, aditivo = ?, ehaditivo = ?, ip = ?, tipoAutenticacao = ?, dadosAutenticacao = ? WHERE codigo = ?";
        int i = 1;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getContrato() == null) {
            sqlAlterar.setNull(i++, Types.NULL);
        } else {
            sqlAlterar.setInt(i++, obj.getContrato().getCodigo());
        }
        sqlAlterar.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        sqlAlterar.setString(i++, obj.getDocumentos());
        sqlAlterar.setString(i++, obj.getEndereco());
        sqlAlterar.setString(i++, obj.getAssinatura());
        sqlAlterar.setString(i++, obj.getAtestado());
        sqlAlterar.setString(i++, obj.getAnexo1());
        sqlAlterar.setString(i++, obj.getAnexo2());
        sqlAlterar.setString(i++, obj.getAssinatura2());
        if (obj.getContratoAditivo() == null) {
            sqlAlterar.setNull(i++, 0);
        } else {
            sqlAlterar.setInt(i++, obj.getContratoAditivo().getCodigo());
        }
        if (obj.getAditivo() != null) {
            sqlAlterar.setInt(i++, obj.getAditivo().getCodigo());
        } else {
            sqlAlterar.setNull(i++, Types.NULL);
        }
        sqlAlterar.setBoolean(i++, obj.isEhAditivo());
        sqlAlterar.setString(i++, obj.getIp());
        sqlAlterar.setString(i++, obj.getTipoAutenticacao());
        sqlAlterar.setString(i++, obj.getDadosAutenticacao());

        sqlAlterar.setInt(i++, obj.getCodigo());

        sqlAlterar.execute();

        if (obj.getAditivo() == null) {
            new ContratoTextoPadrao(con).alterarAssinaturaHtmlContrato(obj.getContrato().getCodigo());
        } else {
            new ContratoTextoPadrao(con).alterarAssinaturaHtmlContratoAditivo(obj.getContratoAditivo().getCodigo(), obj.getAditivo().getCodigo());
        }
    }

    @Override
    public void alterarSomenteAnexoCancelamento(ContratoAssinaturaDigitalVO obj) throws Exception {
        String sql = "UPDATE ContratoAssinaturaDigital set anexoCancelamento = ? WHERE codigo = ?";
        int i = 1;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(i++, obj.getAnexoCancelamento());
        sqlAlterar.setInt(i++, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void alterarSomenteAssinaturaContrato(ContratoAssinaturaDigitalVO obj) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("UPDATE ContratoAssinaturaDigital\n");
        sqlBuilder.append("SET usuarioresponsavel = ?,\n");
        sqlBuilder.append("assinatura = ?\n");
        sqlBuilder.append("WHERE codigo = ?");

        int i = 1;
        final PreparedStatement sqlAlterar = con.prepareStatement(sqlBuilder.toString());
        sqlAlterar.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        sqlAlterar.setString(i++, obj.getAssinatura());
        sqlAlterar.setInt(i++, obj.getCodigo());
        sqlAlterar.execute();

        if (obj.getContratoAditivo() == null) {
            new ContratoTextoPadrao(con).alterarAssinaturaHtmlContrato(obj.getContrato().getCodigo());
        } else {
            new ContratoTextoPadrao(con).alterarAssinaturaHtmlContratoAditivo(obj.getContratoAditivo().getCodigo(), obj.getAditivo().getCodigo());
        }
    }

    @Override
    public List<ContratoVO> consultarContratos(boolean assinados, String filtro, Integer empresa, boolean todos, Boolean contratosCancelados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT cli.matricula, p.nome, c.codigo as contrato, p.fotokey, cad.lancamento, c.dataassinaturacontrato , c.dataassinaturacancelamento, c.ipassinaturacontrato, c.emailrecebimento, p.cfp, false AS ehAditivo, null AS aditivo ");
        if (contratosCancelados != null && contratosCancelados) {
            sql.append(", cop.codigo as operacaoCancelamento, cop.descricaoCalculo as descricaoCancelamento, cop.datainicioefetivacaooperacao as dataCancelamento, jop.descricao as justificativaCancelamento ");
        }
        sql.append(" from contrato c ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = COALESCE(c.pessoaoriginal, c.pessoa)");
        sql.append(" INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa ");
        if (contratosCancelados != null && contratosCancelados) {
            sql.append(" LEFT JOIN contratooperacao cop ON cop.contrato = c.codigo and cop.tipooperacao = 'CA'");
            sql.append(" LEFT JOIN justificativaoperacao jop ON jop.codigo = cop.tipojustificativa");
            sql.append(" LEFT JOIN cancelamentoassinaturadigital cad ON cad.contrato = c.codigo ");
            sql.append(" WHERE cop.tipooperacao = 'CA'");
        } else {
            sql.append(" LEFT JOIN contratoassinaturadigital cad ON cad.contrato = c.codigo AND cad.ehaditivo is false ");
            sql.append(" WHERE c.situacao = 'AT' ");
        }
        if (empresa != null) {
            sql.append(" AND cli.empresacliente = ").append(empresa);
        }
        sql.append(assinados ? " AND (cad.assinatura is not null AND cad.assinatura <> '' OR " + ((contratosCancelados != null && contratosCancelados) ? "c.dataassinaturacancelamento" : "c.dataassinaturacontrato") + " is not null) " : " AND (cad.assinatura is null OR cad.assinatura = '') and " + ((contratosCancelados != null && contratosCancelados) ? "c.dataassinaturacancelamento" : "c.dataassinaturacontrato") + " is null ");
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append("and (cli.nomeconsulta LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sql.append("  OR  (CASE WHEN '").append(filtro).append("' ~ '^-?[0-9]+$' THEN cli.matricula = CAST(NULLIF('").append(filtro).append("', '') AS integer) ELSE false END)");
            sql.append(" OR c.codigo::varchar = '").append(filtro).append("' )");
        }

        sql = adicionarAditivosQuery(sql, assinados, filtro, empresa, false);
        sql.append(assinados ? " ORDER BY lancamento DESC " : " ORDER BY nome ").append(todos ? "" : "LIMIT 20");

        ResultSet rs = criarConsulta(sql.toString(), con);
        List<ContratoVO> lista = new ArrayList<>();
        while (rs.next()) {
            ContratoVO contrato = new ContratoVO();
            contrato.setCodigo(rs.getInt("contrato"));
            contrato.setPessoa(new PessoaVO());
            contrato.setCliente(new ClienteVO());
            contrato.getCliente().setCodigoMatricula(rs.getInt("matricula"));
            contrato.getPessoa().setNome(rs.getString("nome").toLowerCase());
            contrato.getPessoa().setFotoKey(rs.getString("fotokey"));
            contrato.setAssinadoEm(rs.getTimestamp("lancamento"));
            contrato.setDataAssinaturaContrato(rs.getTimestamp("dataassinaturacontrato"));
            contrato.setIpassinaturacontrato(rs.getString("ipassinaturacontrato"));
            contrato.setEmailRecebimento(rs.getString("emailrecebimento"));
            contrato.getPessoa().setCfp(rs.getString("cfp"));
            contrato.setEhAditivo(rs.getBoolean("ehAditivo"));
            if (rs.getInt("aditivo") != 0) {
                contrato.setAditivo(rs.getInt("aditivo"));
            }

            if (contratosCancelados != null && contratosCancelados) {
                ContratoOperacaoVO operacao = new ContratoOperacaoVO();
                operacao.setCodigo(rs.getInt("operacaoCancelamento"));
                operacao.setDescricaoCalculo(rs.getString("descricaoCancelamento"));
                operacao.setJustificativa(rs.getString("justificativaCancelamento"));
                contrato.setCancelamentoContratoVO(operacao);
            }

            lista.add(contrato);
        }
        return lista;
    }

    StringBuilder adicionarAditivosQuery(StringBuilder sqlRoot, boolean assinados, String filtro, Integer empresa, boolean isCount) {
        final StringBuilder sqlAditivo = new StringBuilder();
        if (isCount) {
            sqlAditivo.append(" SELECT 1 ");
        } else {
            sqlAditivo.append(" SELECT cli.matricula, p.nome, c.codigo as contrato, p.fotokey, cad.lancamento, c.dataassinaturacontrato , c.dataassinaturacancelamento, c.ipassinaturacontrato, c.emailrecebimento, p.cfp, true AS ehAditivo, ad.codigo AS aditivo ");
        }
        sqlAditivo.append(" from contrato c ");
        sqlAditivo.append(" INNER JOIN pessoa p ON p.codigo = COALESCE(c.pessoaoriginal, c.pessoa) ");
        sqlAditivo.append(" INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa ");
        sqlAditivo.append(" INNER JOIN contratotextopadrao ctp ON ctp.contrato = c.codigo ");
        sqlAditivo.append(" INNER JOIN aditivo ad ON ad.codigo = ctp.aditivo ");
        sqlAditivo.append(" LEFT JOIN contratoassinaturadigital cad ON cad.contratoaditivo = c.codigo and cad.aditivo = ad.codigo ");
        sqlAditivo.append(" WHERE c.situacao = 'AT' ");

        if (empresa != null) {
            sqlAditivo.append(" AND cli.empresacliente = ").append(empresa);
        }

        if (assinados) {
            sqlAditivo.append(" AND cad.ehaditivo is true AND cad.aditivo = ad.codigo ");
            sqlAditivo.append(" AND (cad.assinatura is not null AND cad.assinatura <> '') ");
        } else {
            sqlAditivo.append(" AND (cad.assinatura is null OR cad.assinatura = '') ");
            sqlAditivo.append(" AND (cad.ehaditivo is null or cad.ehaditivo is true) ");
        }

        if (!UteisValidacao.emptyString(filtro)) {
            sqlAditivo.append("and (cli.nomeconsulta LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sqlAditivo.append("  OR  (CASE WHEN '").append(filtro).append("' ~ '^-?[0-9]+$' THEN cli.matricula = CAST(NULLIF('").append(filtro).append("', '') AS integer) ELSE false END)");
            sqlAditivo.append(" OR c.codigo::varchar = '").append(filtro).append("' )");
        }

        if (isCount) {
            final StringBuilder countSql = new StringBuilder();
            countSql.append(" SELECT COUNT(*) AS cont from ( ");
            countSql.append(sqlRoot);
            countSql.append(" UNION ALL ");
            countSql.append(sqlAditivo);
            countSql.append(" ) AS cont");

            sqlRoot = countSql;
        } else {
            sqlRoot.append(" UNION ALL ");
            sqlRoot.append(sqlAditivo);
        }

        return sqlRoot;
    }

    @Override
    public List<ContratoVO> consultarTermoResponsabilidade(boolean assinados, String filtro, Integer empresa, boolean todos, boolean isTermoResponsabilidadeExAluno) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.codigo, c.matricula, p.nome, p.fotokey from cliente c ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa ");

        if (isTermoResponsabilidadeExAluno) {
            sql.append(" WHERE c.situacao not in( 'AT') ");
        } else {
            sql.append(" WHERE c.situacao = 'VI' ");
        }
        if (empresa != null) {
            sql.append(" AND c.empresa = ").append(empresa);
        }
        sql.append(assinados ? " AND c.termoresponsabilidadeassinado = true" : " AND c.termoresponsabilidadeassinado = false ");
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append(" and (p.nome ILIKE '").append("%" + Uteis.retirarAcentuacao(filtro).replaceAll("%", "")).append("%'");
            sql.append("  OR c.codigomatricula::varchar  = '").append(filtro).append("' )");
        }
        sql.append(" ORDER BY p.nome ").append(todos ? "" : "LIMIT 20");
        ResultSet rs = criarConsulta(sql.toString(), con);
        List<ContratoVO> lista = new ArrayList<>();
        while (rs.next()) {
            ContratoVO contrato = new ContratoVO();
            contrato.setPessoa(new PessoaVO());
            contrato.setCliente(new ClienteVO());
            contrato.getCliente().setCodigo(rs.getInt("codigo"));
            contrato.getCliente().setCodigoMatricula(rs.getInt("matricula"));
            contrato.getPessoa().setNome(rs.getString("nome").toLowerCase());
            contrato.getPessoa().setFotoKey(rs.getString("fotokey"));
            lista.add(contrato);
        }
        return lista;
    }

    @Override
    public Integer consultarQtdTermoResponsabilidade(boolean assinados, String filtro, Integer empresa, boolean isTermoResponsabilidadeExAluno) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(c.codigo) from cliente c ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa ");
        if (isTermoResponsabilidadeExAluno) {
            sql.append(" WHERE c.situacao not in( 'AT' )");
        } else {
            sql.append(" WHERE c.situacao = 'VI' ");
        }

        sql.append(assinados ? " AND c.termoresponsabilidadeassinado = true" : " AND c.termoresponsabilidadeassinado = false");
        if (empresa != null) {
            sql.append(" AND c.empresa = ").append(empresa);
        }
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append(" and (p.nome ILIKE '").append("%" + Uteis.retirarAcentuacao(filtro).replaceAll("%", "")).append("%'");
            sql.append("  OR c.codigomatricula::varchar  = '").append(filtro).append("' )");
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getInt("count");
        }
        return 0;
    }

    @Override
    public Integer countContratos(boolean assinados, String filtro, Integer empresa, Boolean contratosCancelados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT 1 from contrato c ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = COALESCE(c.pessoaoriginal, c.pessoa)");
        sql.append(" INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa ");
        if (contratosCancelados != null && contratosCancelados) {
            sql.append(" LEFT JOIN cancelamentoassinaturadigital cad ON cad.contrato = c.codigo ");
            sql.append(" WHERE c.situacao = 'CA' ");
        } else {
            sql.append(" LEFT JOIN contratoassinaturadigital cad ON cad.contrato = c.codigo AND cad.ehaditivo is false ");
            sql.append(" WHERE c.situacao = 'AT' ");
        }
        if (empresa != null) {
            sql.append(" AND cli.empresacliente = ").append(empresa);
        }
        sql.append(assinados ? " AND (cad.assinatura is not null AND cad.assinatura <> '' or " + ((contratosCancelados != null && contratosCancelados) ? "c.dataassinaturacancelamento" : "c.dataassinaturacontrato") + " is not null)  " : " AND (cad.assinatura is null OR cad.assinatura = '') AND " + ((contratosCancelados != null && contratosCancelados) ? "c.dataassinaturacancelamento" : "c.dataassinaturacontrato") + " is null ");
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append("and (cli.nomeconsulta LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sql.append("  OR  (CASE WHEN '").append(filtro).append("' ~ '^-?[0-9]+$' THEN cli.matricula = CAST(NULLIF('").append(filtro).append("', '') AS integer) ELSE false END)");
            sql.append(" OR c.codigo::varchar = '").append(filtro).append("' )");
        }

        sql = adicionarAditivosQuery(sql, assinados, filtro, empresa, true);
        ResultSet rs = criarConsulta(sql.toString(), con);

        if (rs.next()) {
            return rs.getInt("cont");
        }
        return 0;
    }

    @Override
    public Integer countContratosAtivosSemAssinatura(Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(c.codigo) as cont from contrato c ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa ");
        sql.append(" INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa ");
        sql.append(" LEFT JOIN contratoassinaturadigital cad ON cad.contrato = c.codigo ");
        sql.append(" WHERE c.situacao = 'AT' ");
        sql.append(" AND cli.codigocliente = ").append(codigoCliente);
        sql.append(" AND (cad.assinatura is null or cad.assinatura = '')   ");
        sql.append(" AND c.dataassinaturacontrato is null "); // isso é para contratos com assinatura eletronica

        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getInt("cont");
        }
        return 0;
    }

    @Override
    public Integer countContratosAditivosAtivosSemAssinatura(Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(c.codigo) as cont from contrato c ");
        sql.append(" INNER JOIN contratoTextoPadrao ctp ON ctp.contrato = c.codigo ");
        sql.append(" INNER JOIN aditivo ad ON ad.codigo = ctp.aditivo ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa ");
        sql.append(" INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa ");
        sql.append(" LEFT JOIN contratoassinaturadigital cad ON cad.contratoaditivo = c.codigo and cad.aditivo = ad.codigo ");
        sql.append(" WHERE c.situacao = 'AT' ");
        sql.append(" AND cli.codigocliente = ").append(codigoCliente);
        sql.append(" AND (cad.assinatura is null or cad.assinatura = '')   ");
        sql.append(" AND c.dataassinaturacontrato is null "); // isso é para contratos com assinatura eletronica

        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getInt("cont");
        }
        return 0;
    }

    public boolean consultaTermoAceiteAssinadoPorAluno(Integer codigoCliente) throws Exception {
        return existe("SELECT codigo FROM cliente c\n" +
                "WHERE c.codigo = " + codigoCliente + "\n" +
                "AND c.situacao NOT IN ('AT')\n" +
                "AND c.termoresponsabilidadeassinado", con);
    }

    @Override
    public ContratoAssinaturaDigitalVO consultarPorContrato(Integer contrato) throws Exception {
        return consultarPorContratoEAditivo(contrato, null);
    }

    @Override
    public ContratoAssinaturaDigitalVO consultarPorContratoEAditivo(Integer contrato, Integer aditivo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.codigo, c.documentos, c.endereco, c.assinatura, c.assinatura2, c.atestado, u.nome as nomeusuario,\n");
        sql.append("c.anexo1, c.anexo2, c.anexoCancelamento, c.ip, c.tipoAutenticacao, c.dadosAutenticacao \n");
        sql.append("FROM contratoassinaturadigital c\n");
        sql.append("inner join usuario u on u.codigo = c.usuarioresponsavel\n");
        if (aditivo == null) {
            sql.append("where contrato = ").append(contrato);
        } else {
            sql.append(" where contratoaditivo = ").append(contrato);
            sql.append(" and aditivo = ").append(aditivo);
        }

        ResultSet rs = criarConsulta(sql.toString(), con);
        ContratoAssinaturaDigitalVO docs = new ContratoAssinaturaDigitalVO();
        docs.setContrato(new ContratoVO());
        docs.getContrato().setCodigo(contrato);

        if (rs.next()) {
            docs.setCodigo(rs.getInt("codigo"));
            if (UteisValidacao.emptyString(rs.getString("documentos"))) {
                docs.setDocumentos("");
            } else {
                docs.setDocumentos(rs.getString("documentos"));
            }
            if (UteisValidacao.emptyString(rs.getString("endereco"))) {
                docs.setEndereco("");
            } else {
                docs.setEndereco(rs.getString("endereco"));
            }
            if (UteisValidacao.emptyString(rs.getString("atestado"))) {
                docs.setAtestado("");
            } else {
                docs.setAtestado(rs.getString("atestado"));
            }
            if (UteisValidacao.emptyString(rs.getString("anexo1"))) {
                docs.setAnexo1("");
            } else {
                docs.setAnexo1(rs.getString("anexo1"));
            }
            if (UteisValidacao.emptyString(rs.getString("anexo2"))) {
                docs.setAnexo2("");
            } else {
                docs.setAnexo2(rs.getString("anexo2"));
            }
            if (UteisValidacao.emptyString(rs.getString("anexoCancelamento"))) {
                docs.setAnexoCancelamento("");
            } else {
                docs.setAnexoCancelamento(rs.getString("anexoCancelamento"));
            }
            if (UteisValidacao.emptyString(rs.getString("assinatura"))) {
                docs.setAssinatura("");
            } else {
                docs.setAssinatura(rs.getString("assinatura"));
            }
            if (UteisValidacao.emptyString(rs.getString("assinatura2"))) {
                docs.setAssinatura2("");
            } else {
                docs.setAssinatura2(rs.getString("assinatura2"));
            }
            if (UteisValidacao.emptyString(rs.getString("ip"))) {
                docs.setIp("");
            } else {
                docs.setIp(rs.getString("ip"));
            }
            if (UteisValidacao.emptyString(rs.getString("tipoAutenticacao"))) {
                docs.setTipoAutenticacao("");
            } else {
                docs.setTipoAutenticacao(rs.getString("tipoAutenticacao"));
            }
            if (UteisValidacao.emptyString(rs.getString("dadosAutenticacao"))) {
                docs.setDadosAutenticacao("");
            } else {
                docs.setDadosAutenticacao(rs.getString("dadosAutenticacao"));
            }
            docs.setUsuarioResponsavel(new UsuarioVO());
            docs.getUsuarioResponsavel().setNome(rs.getString("nomeusuario"));
        }

        return docs;
    }

    @Override
    public CancelamentoAssinaturaDigitalVO consultarPorContratoCancelamento(Integer contrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.codigo, c.contrato, c.contratoOperacao, c.usuarioResponsavel, u.nome, c.assinatura, c.lancamento ");
        sql.append(" FROM cancelamentoassinaturadigital c \n");
        sql.append(" inner join usuario u on u.codigo = c.usuarioresponsavel \n");
        sql.append(" inner join contratooperacao op on op.codigo = c.contratooperacao where c.contrato = ").append(contrato);
        ResultSet rs = criarConsulta(sql.toString(), con);
        CancelamentoAssinaturaDigitalVO ass = new CancelamentoAssinaturaDigitalVO();
        ass.setContrato(new ContratoVO());
        ass.getContrato().setCodigo(contrato);
        if (rs.next()) {
            ass.setCodigo(rs.getInt("codigo"));
            if (UteisValidacao.emptyString(rs.getString("assinatura"))) {
                ass.setAssinatura("");
            } else {
                ass.setAssinatura(rs.getString("assinatura"));
            }
            ass.setUsuarioResponsavel(new UsuarioVO());
            ass.getUsuarioResponsavel().setCodigo(rs.getInt("usuarioResponsavel"));
            ass.getUsuarioResponsavel().setNome(rs.getString("nome"));
            ass.setContratoOperacao(new ContratoOperacaoVO());
            ass.getContratoOperacao().setCodigo(rs.getInt("contratoOperacao"));
        }
        return ass;
    }

    public ContratoAssinaturaDigitalVO consultarPorCodigoContrato(Integer contrato) throws Exception {
        String sql = "SELECT * FROM contratoassinaturadigital WHERE contrato = " + contrato;
        ResultSet rs = criarConsulta(sql, con);

        ContratoAssinaturaDigitalVO contratoAssinaturaDigitalVO = new ContratoAssinaturaDigitalVO();
        if (rs.next()) {
            contratoAssinaturaDigitalVO.setCodigo(rs.getInt("codigo"));
            contratoAssinaturaDigitalVO.setContrato(new ContratoVO());
            contratoAssinaturaDigitalVO.getContrato().setCodigo(rs.getInt("contrato"));
            contratoAssinaturaDigitalVO.setUsuarioResponsavel(new UsuarioVO());
            contratoAssinaturaDigitalVO.getUsuarioResponsavel().setCodigo(rs.getInt("usuarioresponsavel"));
            contratoAssinaturaDigitalVO.setContratoTextoPadrao(new ContratoTextoPadraoVO());
            contratoAssinaturaDigitalVO.getContratoTextoPadrao().setCodigo(rs.getInt("contratotextopadrao"));
            contratoAssinaturaDigitalVO.setDocumentos(rs.getString("documentos"));
            contratoAssinaturaDigitalVO.setEndereco(rs.getString("endereco"));
            contratoAssinaturaDigitalVO.setAssinatura(rs.getString("assinatura"));
            contratoAssinaturaDigitalVO.setAtestado(rs.getString("atestado"));
            contratoAssinaturaDigitalVO.setLancamento(rs.getTimestamp("lancamento"));
            contratoAssinaturaDigitalVO.setAnexo1(rs.getString("anexo1"));
            contratoAssinaturaDigitalVO.setAnexo2(rs.getString("anexo2"));
            contratoAssinaturaDigitalVO.setAnexoCancelamento(rs.getString("anexoCancelamento"));
            contratoAssinaturaDigitalVO.setIp(rs.getString("ip"));
            contratoAssinaturaDigitalVO.setTipoAutenticacao(rs.getString("tipoAutenticacao"));
            contratoAssinaturaDigitalVO.setDadosAutenticacao(rs.getString("dadosAutenticacao"));
        }
        return contratoAssinaturaDigitalVO;
    }

    public List<ContratoAssinaturaDigitalVO> consultarTodosContratosDigitais(Integer montarDados, boolean assinados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM contratoassinaturadigital cd\n");
        if (assinados) {
            sql.append("where cd.assinatura <> '' or cd.assinatura <> null");
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        List<ContratoAssinaturaDigitalVO> contratos = new ArrayList<>();
        while (rs.next()) {
            if (montarDados == Uteis.NIVELMONTARDADOS_TODOS) {
                ContratoAssinaturaDigitalVO contratoAssinaturaDigitalVO = new ContratoAssinaturaDigitalVO();
                contratoAssinaturaDigitalVO.setCodigo(rs.getInt("codigo"));
                contratoAssinaturaDigitalVO.setContrato(new ContratoVO());
                contratoAssinaturaDigitalVO.getContrato().setCodigo(rs.getInt("contrato"));
                contratoAssinaturaDigitalVO.setUsuarioResponsavel(new UsuarioVO());
                contratoAssinaturaDigitalVO.getUsuarioResponsavel().setCodigo(rs.getInt("usuarioresponsavel"));
                contratoAssinaturaDigitalVO.setContratoTextoPadrao(new ContratoTextoPadraoVO());
                contratoAssinaturaDigitalVO.getContratoTextoPadrao().setCodigo(rs.getInt("contratotextopadrao"));
                contratoAssinaturaDigitalVO.setDocumentos(rs.getString("documentos"));
                contratoAssinaturaDigitalVO.setEndereco(rs.getString("endereco"));
                contratoAssinaturaDigitalVO.setAssinatura(rs.getString("assinatura"));
                contratoAssinaturaDigitalVO.setAtestado(rs.getString("atestado"));
                contratoAssinaturaDigitalVO.setLancamento(rs.getTimestamp("lancamento"));
                contratoAssinaturaDigitalVO.setAnexo1(rs.getString("anexo1"));
                contratoAssinaturaDigitalVO.setAnexo2(rs.getString("anexo2"));
                contratoAssinaturaDigitalVO.setAnexoCancelamento(rs.getString("anexoCancelamento"));
                contratos.add(contratoAssinaturaDigitalVO);
            }
            if (montarDados == Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_FOTOS_IMPORTADOR) {
                ContratoAssinaturaDigitalVO contratoAssinaturaDigitalVO = new ContratoAssinaturaDigitalVO();
                contratoAssinaturaDigitalVO.setContrato(new ContratoVO());


                contratoAssinaturaDigitalVO.setCodigo(rs.getInt("codigo"));

                contratoAssinaturaDigitalVO.getContrato().setCodigo(rs.getInt("contrato"));
                contratoAssinaturaDigitalVO.setAssinatura(rs.getString("assinatura"));
                contratos.add(contratoAssinaturaDigitalVO);
            }
        }
        return contratos;
    }

    @Override
    public List<ContratoVO> consultarClientesParQ(boolean assinados, String filtro, Integer empresa, boolean todos) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.codigomatricula as matricula, p.nome, c.codigo as codigocliente, p.fotokey\n ");
        sql.append(" FROM cliente c\n ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa\n ");
        sql.append(" WHERE c.situacao = 'AT' ");
        if (empresa != null) {
            sql.append(" AND c.empresa = ").append(empresa).append("\n ");
        }
        sql.append(assinados ? " AND c.parqpositivo = true\n " : " AND c.parqpositivo <> true\n ");
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append(" AND (p.nome LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sql.append(" OR c.matricula::varchar  = '").append(filtro).append("' )\n");
        }
        sql.append(" ORDER BY p.nome ");
        ResultSet rs = criarConsulta(sql.toString(), con);
        List<ContratoVO> lista = new ArrayList<ContratoVO>();
        while (rs.next()) {
            ContratoVO contrato = new ContratoVO();
            contrato.setCodigo(rs.getInt("codigocliente"));
            contrato.setPessoa(new PessoaVO());
            contrato.setCliente(new ClienteVO());
            contrato.getCliente().setCodigoMatricula(rs.getInt("matricula"));
            contrato.getPessoa().setNome(rs.getString("nome").toLowerCase());
            contrato.getPessoa().setFotoKey(rs.getString("fotokey"));
            lista.add(contrato);
        }
        return lista;
    }

    @Override
    public void excluirAssinaturaPeloContrato(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "UPDATE ContratoAssinaturaDigital SET assinatura=null WHERE contrato = ?";
        try (PreparedStatement sqlExcluirAssinatura = con.prepareStatement(sql)) {
            sqlExcluirAssinatura.setInt(1, contrato);
            sqlExcluirAssinatura.execute();
        }
    }

    @Override
    public void excluirAssinaturaPeloContratoEAditivo(Integer contrato, Integer aditivo) throws Exception {
        excluir(getIdEntidade());
        final String sql = "UPDATE ContratoAssinaturaDigital SET assinatura = null WHERE contratoaditivo = ? and aditivo = ?";
        try (PreparedStatement sqlExcluirAssinatura = con.prepareStatement(sql)) {
            sqlExcluirAssinatura.setInt(1, contrato);
            sqlExcluirAssinatura.setInt(2, aditivo);
            sqlExcluirAssinatura.execute();
        }
    }

    @Override
    public void excluirAssinatura2PeloContrato(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "UPDATE ContratoAssinaturaDigital SET assinatura2=null WHERE contrato = ?";
        try (PreparedStatement sqlExcluirAssinatura = con.prepareStatement(sql)) {
            sqlExcluirAssinatura.setInt(1, contrato);
            sqlExcluirAssinatura.execute();
        }
    }

    @Override
    public void excluirAssinaturaEletronicaContrato(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "UPDATE contrato SET dataassinaturacontrato=null,ipassinaturacontrato=null, emailrecebimento= null  WHERE codigo = ?";
        try (PreparedStatement sqlExcluirAssinatura = con.prepareStatement(sql)) {
            sqlExcluirAssinatura.setInt(1, contrato);
            sqlExcluirAssinatura.execute();
        }
    }
}
