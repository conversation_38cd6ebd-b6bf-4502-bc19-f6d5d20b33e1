package negocio.facade.jdbc.contrato;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.*;
import negocio.comuns.financeiro.ProdutoTextoPadraoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.contrato.ProdutoAssinaturaDigitalInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

public class ProdutoAssinaturaDigital extends SuperEntidade implements ProdutoAssinaturaDigitalInterfaceFacade {

    public ProdutoAssinaturaDigital() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public ProdutoAssinaturaDigital(Connection con) throws Exception {
        super(con);
        setIdEntidade("Contrato");
    }

    @Override
    public void incluir(ProdutoAssinaturaDigitalVO obj) throws Exception {
        String sql = "INSERT INTO ProdutoAssinaturaDigital(usuarioresponsavel, produtotextopadrao,"
                + "documentos, endereco, assinatura, atestado, anexo1, anexo2, lancamento, anexoCancelamento) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int i = 1;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        if (obj.getProdutoTextoPadrao() != null && UteisValidacao.emptyNumber(obj.getProdutoTextoPadrao().getCodigo())) {
            sqlInserir.setNull(i++, Types.NULL);
        } else {
            sqlInserir.setInt(i++, obj.getProdutoTextoPadrao().getCodigo());
        }
        sqlInserir.setString(i++, obj.getDocumentos());
        sqlInserir.setString(i++, obj.getEndereco());
        sqlInserir.setString(i++, obj.getAssinatura());
        sqlInserir.setString(i++, obj.getAtestado());
        sqlInserir.setString(i++, obj.getAnexo1());
        sqlInserir.setString(i++, obj.getAnexo2());
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.setString(i++, obj.getAnexoCancelamento());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        new ProdutoTextoPadrao(con).alterarAssinaturaHtmlContratoProduto(obj.getProdutoTextoPadrao().getCodigo());
    }

    public String obterPorContrato(Integer contrato) throws Exception {
        String sql = " SELECT assinatura from produtoassinaturadigital WHERE produtotextopadrao = " + contrato;
        ResultSet rs = criarConsulta(sql, con);
        if (rs.next()) {
            return rs.getString("assinatura");
        }
        return null;
    }

    @Override
    public void excluir(ProdutoAssinaturaDigitalVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ProdutoAssinaturaDigital WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public void alterar(ProdutoAssinaturaDigitalVO obj) throws Exception {
        String sql = "UPDATE ProdutoAssinaturaDigital set usuarioresponsavel = ?, produtotextopadrao = ?,"
                + "documentos = ?, endereco = ?,  assinatura=?, atestado = ?, anexo1 = ?, anexo2 = ? WHERE codigo = ?";
        int i = 1;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        sqlAlterar.setInt(i++, obj.getProdutoTextoPadrao().getCodigo());
        sqlAlterar.setString(i++, obj.getDocumentos());
        sqlAlterar.setString(i++, obj.getEndereco());
        sqlAlterar.setString(i++, obj.getAssinatura());
        sqlAlterar.setString(i++, obj.getAtestado());
        sqlAlterar.setString(i++, obj.getAnexo1());
        sqlAlterar.setString(i++, obj.getAnexo2());
        sqlAlterar.setInt(i++, obj.getCodigo());
        sqlAlterar.execute();
        new ProdutoTextoPadrao(con).alterarAssinaturaHtmlContratoProduto(obj.getProdutoTextoPadrao().getCodigo());
    }

    @Override
    public List<ProdutoTextoPadraoVO> consultarContratosProdutos(boolean assinados, String filtro, Integer empresa, boolean todos) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT cli.matricula, p.nome, c.codigo as contrato, p.fotokey, cad.lancamento, c.dataassinaturacontrato, c.ipassinaturacontrato, c.emailrecebimento, p.cfp ");
        sql.append(" from produtotextopadrao c ");
        sql.append(" LEFT JOIN vendaavulsa va ON va.codigo = c.vendaavulsa ");
        sql.append(" LEFT JOIN aulaavulsadiaria avd ON avd.codigo = c.aulaavulsadiaria ");
        sql.append(" INNER JOIN cliente cl ON cl.codigo = coalesce(va.cliente, avd.cliente) ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = cl.pessoa");
        sql.append(" INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa ");
        sql.append(" LEFT JOIN produtoassinaturadigital cad ON cad.produtotextopadrao = c.codigo ");
        sql.append(" WHERE 1=1 ");
        if (empresa != null) {
            sql.append(" AND cli.empresacliente = ").append(empresa);
        }
        sql.append(assinados ? " AND (cad.assinatura is not null AND cad.assinatura <> '' OR c.dataassinaturacontrato is not null) " : " AND (cad.assinatura is null OR cad.assinatura = '') and c.dataassinaturacontrato is null ");
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append("and (cli.nomeconsulta LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sql.append("  OR  (CASE WHEN '").append(filtro).append("' ~ '^-?[0-9]+$' THEN cli.matricula = CAST(NULLIF('").append(filtro).append("', '') AS integer) ELSE false END)");
            sql.append(" OR c.codigo::varchar = '").append(filtro).append("' )");
        }

        sql.append(assinados ? " ORDER BY cad.lancamento DESC " : " ORDER BY p.nome ").append(todos ? "" : "LIMIT 20");
        ResultSet rs = criarConsulta(sql.toString(), con);
        List<ProdutoTextoPadraoVO> lista = new ArrayList<>();
        while (rs.next()) {
            ProdutoTextoPadraoVO contrato = new ProdutoTextoPadraoVO();
            contrato.setCodigo(rs.getInt("contrato"));
            contrato.setCliente(new ClienteVO());
            contrato.getCliente().setPessoa(new PessoaVO());
            contrato.getCliente().setCodigoMatricula(rs.getInt("matricula"));
            contrato.getCliente().getPessoa().setNome(rs.getString("nome").toLowerCase());
            contrato.getCliente().getPessoa().setFotoKey(rs.getString("fotokey"));
            contrato.setAssinadoEm(rs.getTimestamp("lancamento"));
            contrato.setDataAssinaturaContrato(rs.getTimestamp("dataassinaturacontrato"));
            contrato.setIpAssinaturaContrato(rs.getString("ipassinaturacontrato"));
            contrato.setEmailRecebimento(rs.getString("emailrecebimento"));
            contrato.getCliente().getPessoa().setCfp(rs.getString("cfp"));
            lista.add(contrato);
        }
        return lista;
    }

    @Override
    public Integer countContratosProdutos(boolean assinados, String filtro, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(c.codigo) as cont from produtotextopadrao c ");
        sql.append(" INNER JOIN vendaavulsa va ON va.codigo = c.vendaavulsa");
        sql.append(" INNER JOIN cliente cl ON cl.codigo = va.cliente");
        sql.append(" INNER JOIN pessoa p ON p.codigo = cl.pessoa");
        sql.append(" INNER JOIN situacaoclientesinteticodw cli ON p.codigo = cli.codigopessoa ");
        sql.append(" LEFT JOIN produtoassinaturadigital cad ON cad.produtotextopadrao = c.codigo ");
        sql.append(" WHERE 1=1 ");
        if (empresa != null) {
            sql.append(" AND cli.empresacliente = ").append(empresa);
        }
        sql.append(assinados ? " AND (cad.assinatura is not null AND cad.assinatura <> '' or c.dataassinaturacontrato is not null)  " : " AND (cad.assinatura is null OR cad.assinatura = '') AND c.dataassinaturacontrato is null ");
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append("and (cli.nomeconsulta LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sql.append("  OR  (CASE WHEN '").append(filtro).append("' ~ '^-?[0-9]+$' THEN cli.matricula = CAST(NULLIF('").append(filtro).append("', '') AS integer) ELSE false END)");
            sql.append(" OR c.codigo::varchar = '").append(filtro).append("' )");
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getInt("cont");
        }
        return 0;
    }

    @Override
    public ProdutoAssinaturaDigitalVO consultarPorContratoProduto(Integer contrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.codigo, c.documentos, c.endereco, c.assinatura, c.atestado, u.nome as nomeusuario, ");
        sql.append(" c.anexo1, c.anexo2, c.anexoCancelamento ");
        sql.append(" FROM produtoassinaturadigital c\n");
        sql.append(" inner join usuario u on u.codigo = c.usuarioresponsavel where produtotextopadrao = ").append(contrato);
        ResultSet rs = criarConsulta(sql.toString(), con);
        ProdutoAssinaturaDigitalVO docs = new ProdutoAssinaturaDigitalVO();
        docs.setProdutoTextoPadrao(new ProdutoTextoPadraoVO());
        docs.getProdutoTextoPadrao().setCodigo(contrato);
        if (rs.next()) {
            docs.setCodigo(rs.getInt("codigo"));
            if (UteisValidacao.emptyString(rs.getString("documentos"))) {
                docs.setDocumentos("");
            } else {
                docs.setDocumentos(rs.getString("documentos"));
            }
            if (UteisValidacao.emptyString(rs.getString("endereco"))) {
                docs.setEndereco("");
            } else {
                docs.setEndereco(rs.getString("endereco"));
            }
            if (UteisValidacao.emptyString(rs.getString("atestado"))) {
                docs.setAtestado("");
            } else {
                docs.setAtestado(rs.getString("atestado"));
            }
            if (UteisValidacao.emptyString(rs.getString("anexo1"))) {
                docs.setAnexo1("");
            } else {
                docs.setAnexo1(rs.getString("anexo1"));
            }
            if (UteisValidacao.emptyString(rs.getString("anexo2"))) {
                docs.setAnexo2("");
            } else {
                docs.setAnexo2(rs.getString("anexo2"));
            }
            if (UteisValidacao.emptyString(rs.getString("anexoCancelamento"))) {
                docs.setAnexoCancelamento("");
            } else {
                docs.setAnexoCancelamento(rs.getString("anexoCancelamento"));
            }
            if (UteisValidacao.emptyString(rs.getString("assinatura"))) {
                docs.setAssinatura("");
            } else {
                docs.setAssinatura(rs.getString("assinatura"));
            }
            docs.setUsuarioResponsavel(new UsuarioVO());
            docs.getUsuarioResponsavel().setNome(rs.getString("nomeusuario"));
        }
        return docs;
    }

    @Override
    public void excluirAssinaturaProdutoContrato(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "UPDATE ProdutoAssinaturaDigital SET assinatura=null WHERE produtotextopadrao = ?";
        try(PreparedStatement sqlExcluirAssinatura = con.prepareStatement(sql)) {
            sqlExcluirAssinatura.setInt(1, contrato);
            sqlExcluirAssinatura.execute();
        }
    }
}
