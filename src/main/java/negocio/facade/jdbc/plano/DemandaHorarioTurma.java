package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import org.json.JSONArray;
import negocio.comuns.plano.AnaliticoDemandaHorarioTurmaTO;
import negocio.comuns.plano.SinteticoDemandaHorarioTurmaTO;
import negocio.comuns.plano.DemandaHorarioTurmaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.DemandaHorarioTurmaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DemandaHorarioTurma extends SuperEntidade implements DemandaHorarioTurmaInterfaceFacade {
    public DemandaHorarioTurma(Connection con) throws Exception {
        super(con);
        setIdEntidade("Turma");
    }

    @Override
    public DemandaHorarioTurmaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new DemandaHorarioTurmaVO();
    }

    @Override
    public void incluir(DemandaHorarioTurmaVO obj) throws Exception {
        DemandaHorarioTurmaVO.validarDados(obj);
        incluir(getIdEntidade());
        String sql = "INSERT INTO DemandaHorarioTurma(cliente, horarioTurma, data, dataProcura) VALUES ( ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getCliente().getCodigo());
        sqlInserir.setInt(2, obj.getHorarioTurma().getCodigo());
        sqlInserir.setDate(3, Uteis.getDataJDBC(obj.getData()));
        sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataProcura()));
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(Integer cliente, Integer horarioturma, Date data) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM DemandaHorarioTurma WHERE cliente = ? and horarioTurma = ? and data = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, cliente);
        sqlExcluir.setInt(2, horarioturma);
        sqlExcluir.setDate(3, Uteis.getDataJDBC(data));
        sqlExcluir.execute();
    }

    @Override
    public List<SinteticoDemandaHorarioTurmaTO> consultarSintetico(JSONArray filtroProfessores, JSONArray filtroModalidades, JSONArray filtroAmbientes,
                                                                   JSONArray filtroTurmas, JSONArray filtroHorarios, JSONArray filtroDiasDaSemana,
                                                                   Date dataInicio, Date dataFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) as demanda, ht.identificadorturma as turma, ht.horainicial as horarioTurma\n");
        sql.append("from demandahorarioturma dht \n");
        sql.append("inner join horarioturma ht on ht.codigo = dht.horarioturma\n");
        sql.append("inner join cliente cli on cli.codigo = dht.cliente\n");
        sql.append("inner join pessoa p on cli.pessoa = p.codigo\n");
        sql.append("inner join turma t on  ht.turma = t.codigo\n");
        sql.append("inner join modalidade mod on mod.codigo = t.modalidade\n");
        sql.append("left join ambiente amb on amb.codigo = t.ambiente\n");
        inserirTodosFiltros(filtroProfessores, filtroModalidades, filtroAmbientes, filtroTurmas, filtroHorarios, filtroDiasDaSemana, dataInicio, dataFim, sql);
        sql.append(" group by ht.identificadorturma , ht.horainicial\n");
        sql.append("order by ht.horainicial");

        List<SinteticoDemandaHorarioTurmaTO> lista = new ArrayList<SinteticoDemandaHorarioTurmaTO>();
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        while (tabelaResultado.next()) {
            SinteticoDemandaHorarioTurmaTO obj = consultaHorarioTurma(lista, tabelaResultado.getString("horarioTurma"));

            if (obj == null) {
                lista.add(montarDadosSintetico(tabelaResultado));
            } else {
                incrementaAgrupamento(obj, tabelaResultado);
            }
        }

        return lista;
    }

    @Override
    public List<AnaliticoDemandaHorarioTurmaTO> consultarAnalitico(JSONArray filtroProfessores, JSONArray filtroModalidades,
                                                                   JSONArray filtroAmbientes, JSONArray filtroTurmas,
                                                                   JSONArray filtroHorarios, JSONArray filtroDiasDaSemana, Date dataInicio, Date dataFim) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select dht.dataProcura, p.nome, mod.nome as aula, t.identificador as turma, \n");
        sql.append("amb.descricao as ambiente, \n");
        sql.append("ht.diasemana || ': ' || ht.horainicial || ' - ' || ht.horafinal as horario\n");
        sql.append("from demandahorarioturma dht\n");
        sql.append("inner join cliente cli on cli.codigo = dht.cliente\n");
        sql.append("inner join pessoa p on p.codigo = cli.pessoa\n");
        sql.append("inner join horarioturma ht on ht.codigo = dht.horarioturma\n");
        sql.append("inner join turma t on t.codigo = ht.turma\n");
        sql.append("inner join modalidade mod on mod.codigo = t.modalidade\n");
        sql.append("left join ambiente amb on amb.codigo = t.ambiente\n");
        inserirTodosFiltros(filtroProfessores, filtroModalidades, filtroAmbientes, filtroTurmas, filtroHorarios, filtroDiasDaSemana, dataInicio, dataFim, sql);
        sql.append(" order by dht.dataProcura;");

        List<AnaliticoDemandaHorarioTurmaTO> lista = new ArrayList<AnaliticoDemandaHorarioTurmaTO>();
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        while (tabelaResultado.next()) {
            lista.add(montarDadosAnalitico(tabelaResultado));
        }

        return lista;
    }

    private AnaliticoDemandaHorarioTurmaTO montarDadosAnalitico(ResultSet tabelaResultado) throws Exception {
        AnaliticoDemandaHorarioTurmaTO  dados = new AnaliticoDemandaHorarioTurmaTO();
        dados.setData(tabelaResultado.getDate("dataProcura"));
        dados.setNome(tabelaResultado.getString("nome"));
        dados.setAula(tabelaResultado.getString("aula"));
        dados.setTurma(tabelaResultado.getString("turma"));
        dados.setAmbiente(tabelaResultado.getString("ambiente"));
        dados.setHorario(tabelaResultado.getString("horario"));
        return dados;
    }

    private void inserirTodosFiltros(JSONArray filtroProfessores, JSONArray filtroModalidades, JSONArray filtroAmbientes,
                                     JSONArray filtroTurmas, JSONArray filtroHorarios, JSONArray filtroDiasDaSemana,
                                     Date dataInicio, Date dataFim, StringBuilder sql) throws Exception {
        sql.append("where dht.data BETWEEN '");
        sql.append(Uteis.getDataJDBC(dataInicio));
        sql.append("' AND '");
        sql.append(Uteis.getDataJDBC(dataFim));
        sql.append("'\n");
        insereFiltroInt(sql, filtroProfessores, "ht", "professor");
        insereFiltroInt(sql, filtroModalidades, "t", "modalidade");
        insereFiltroInt(sql, filtroAmbientes, "amb", "codigo");
        insereFiltroInt(sql, filtroTurmas, "t", "codigo");
        insereFiltroString(sql, filtroHorarios, "horainicial");
        insereFiltroString(sql, converterDiasDaSemana(filtroDiasDaSemana), "diasemana");
    }

    private JSONArray converterDiasDaSemana(JSONArray filtroDiasDaSemana) throws Exception {
        JSONArray diasSemana = new JSONArray();

        for (int i = 0; i< filtroDiasDaSemana.length(); i++) {
            diasSemana.put(DiaSemana.getDiaSemanaNumeral(filtroDiasDaSemana.getInt(i)).getCodigo());
        }

        return diasSemana;
    }

    private void insereFiltroString(StringBuilder sql, JSONArray filtro, String fk) {
        if (filtro.length() > 0) {
            // Validar o nome do campo para prevenir SQL injection
            if (!Uteis.isValidFieldName(fk)) {
                throw new SecurityException("Nome de campo inválido: " + fk);
            }

            StringBuilder listaCodigos = new StringBuilder();
            listaCodigos.append(" AND ht.").append(fk).append(" in (");

            for (int i = 0; i < filtro.length(); i++) {
                String valor = filtro.getString(i);
                // Validar cada valor para prevenir SQL injection
                if (!Uteis.isValidStringValue(valor)) {
                    throw new SecurityException("Valor inválido detectado no filtro: " + valor);
                }
                // Escapar aspas simples para prevenir SQL injection
                String valorEscapado = valor.replace("'", "''");
                listaCodigos.append("'").append(valorEscapado).append("',");
            }

            listaCodigos.deleteCharAt(listaCodigos.length() - 1);
            listaCodigos.append(")");
            sql.append(listaCodigos);
        }
    }


    private void insereFiltroInt(StringBuilder sql, JSONArray filtro, String alias, String fk) {
        if (filtro.length() > 0) {
            // Validar nomes dos campos para prevenir SQL injection
            if (!Uteis.isValidFieldName(alias) || !Uteis.isValidFieldName(fk)) {
                throw new SecurityException("Nome de campo inválido: " + alias + "." + fk);
            }

            StringBuilder listaCodigos = new StringBuilder();
            listaCodigos.append(" AND ").append(alias).append(".").append(fk).append(" in (");

            for (int i = 0; i < filtro.length(); i++) {
                // getInt() já garante que é um número inteiro, sem risco de SQL injection
                listaCodigos.append(filtro.getInt(i)).append(",");
            }

            listaCodigos.deleteCharAt(listaCodigos.length() - 1);
            listaCodigos.append(")");
            sql.append(listaCodigos);
        }
    }

    private void incrementaAgrupamento(SinteticoDemandaHorarioTurmaTO obj, ResultSet tabelaResultado) throws Exception {
        obj.setDetalhes(obj.getDetalhes() + "<br/>" + tabelaResultado.getString("turma") + ": " + tabelaResultado.getInt("demanda"));
        obj.setDemanda(obj.getDemanda() + tabelaResultado.getInt("demanda"));
    }

    private SinteticoDemandaHorarioTurmaTO consultaHorarioTurma(List<SinteticoDemandaHorarioTurmaTO> lista, String horarioTurma) {
        SinteticoDemandaHorarioTurmaTO sinteticoDemandaHorarioTurmaTO = null;

        for(SinteticoDemandaHorarioTurmaTO obj: lista) {
            if (obj.getHorarioTurma().equals(horarioTurma)) {
                sinteticoDemandaHorarioTurmaTO = obj;
                break;
            }
        }

        return sinteticoDemandaHorarioTurmaTO;
    }

    private SinteticoDemandaHorarioTurmaTO montarDadosSintetico(ResultSet dadosSQL) throws Exception {
        SinteticoDemandaHorarioTurmaTO obj = new SinteticoDemandaHorarioTurmaTO();
        obj.setDemanda(dadosSQL.getInt("demanda"));
        obj.setDetalhes(dadosSQL.getString("turma") + ": " + obj.getDemanda());
        obj.setHorarioTurma(dadosSQL.getString("horarioTurma"));
        return obj;
    }

    @Override
    public void excluir(Integer horarioturma) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM DemandaHorarioTurma WHERE horarioTurma = ? ";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, horarioturma);
        sqlExcluir.execute();
    }

}
