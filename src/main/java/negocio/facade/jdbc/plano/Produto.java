package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estrutura.paginacao.PreparedStatementPersonalizado;
import br.com.pactosolucoes.estudio.util.Validador;
import importador.json.ProdutoImportacaoJSON;
import negocio.comuns.acesso.TipoValidacaoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.plano.PacotePersonalVO;
import negocio.comuns.plano.ProdutoImagemTO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.plano.enumerador.UnidadeMedidaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.ComissaoProdutoConfiguracao;
import negocio.facade.jdbc.vendas.VendasConfig;
import negocio.interfaces.plano.ProdutoInterfaceFacade;
import org.apache.commons.io.Charsets;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import servicos.util.ExecuteRequestHttpService;

import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static negocio.comuns.plano.enumerador.TipoProduto.ARMARIO;
import static negocio.comuns.plano.enumerador.TipoProduto.CREDITO_PERSONAL;
import static negocio.comuns.plano.enumerador.TipoProduto.DIARIA;
import static negocio.comuns.plano.enumerador.TipoProduto.FREEPASS;
import static negocio.comuns.plano.enumerador.TipoProduto.SERVICO;
import static negocio.comuns.plano.enumerador.TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA;
import static negocio.comuns.plano.enumerador.TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA;
import static negocio.comuns.plano.enumerador.TipoProduto.TAXA_PERSONAL;
import static negocio.comuns.plano.enumerador.TipoProduto.filtroVendaAvulsa;
import static negocio.comuns.plano.enumerador.TipoProduto.getTipoProdutoCodigo;
import static negocio.comuns.plano.enumerador.TipoProduto.retornaPorDescricaoImportacao;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>ProdutoVO</code>. Responsável por implementar operações como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>ProdutoVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see ProdutoVO
 * @see SuperEntidade
 */
public class Produto extends SuperEntidade implements ProdutoInterfaceFacade {

    private static final int CATEGORIA_PRODUTO_SERVICOS = 5;

    public Produto() throws Exception {
        super();
    }

    public Produto(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>ProdutoVO</code>.
     */
    public ProdutoVO novo() throws Exception {
        incluir(getIdEntidade());
        ProdutoVO obj = new ProdutoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>ProdutoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ProdutoVO</code> que será gravado no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(ProdutoVO obj, boolean ignorarPermissao) throws Exception {
        ProdutoVO.validarDados(obj);
        if (!ignorarPermissao) {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Produto( categoriaProduto, descricao, tipoVigencia, dataInicioVigencia, "
                + "dataFinalVigenciaFixa, nrDiasVigencia, valorFinal, tipoProduto, valorbasecalculo,desativado, capacidade, bloqueiapelavigencia, ncm, "
                + "prevalecerVigenciaContrato, codigobarras, observacao, tamanhoArmario, cfop, codigoListaServico, codigoTributacaoMunicipio, aliquotaPIS, "
                + "aliquotaCOFINS, aliquotaICMS, aliquotaISSQN, ncmNFCe, pontos, apareceraulacheia, prefixo, configuracaonotafiscalNFSe, configuracaonotafiscalNFCe, "
                + "situacaoTributariaICMS, situacaoTributariaISSQN, situacaoTributariaPIS, situacaoTributariaCOFINS, unidadeMedida, "
                + "enviarPercentualImposto, percentualFederal, percentualEstadual, percentualMunicipal, isentoPIS, isentoCOFINS, isentoICMS, id_externo, qtdePontos, "
                + "apresentarVendasOnline, imagens, maxDivisao, enviaAliquotaNFePIS, enviaAliquotaNFeCOFINS, enviaAliquotaNFeICMS, modalidadevendasonline, "
                + "precoCusto, margemLucro, qtdConvites, cest, codigoprodutosesi, negociosesi, codigoBeneficioFiscal,renovavelAutomaticamente, apresentarpactoapp,crsesi,projetosesi, contafinanceirasesi, apresentarPactoFlow, descricaoServicoMunicipio, destravarTranca, \n"
                + "contratoTextoPadrao, idprodutosmd, codigoformulario, exibirrelatoriosmd )\n"
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n"
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n"
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n"
                + "?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getCategoriaProduto().getCodigo() != 0) {
                sqlInserir.setInt(1, obj.getCategoriaProduto().getCodigo());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setString(2, Uteis.getStringNormalizada(obj.getDescricao().toUpperCase()));
            sqlInserir.setString(3, obj.getTipoVigencia());
            sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
            sqlInserir.setDate(5, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
            sqlInserir.setInt(6, obj.getNrDiasVigencia() == null ? 0 : obj.getNrDiasVigencia());
            sqlInserir.setDouble(7, obj.getValorFinal());
            sqlInserir.setString(8, obj.getTipoProduto());
            sqlInserir.setDouble(9, obj.getValorBaseCalculo());
            sqlInserir.setBoolean(10, obj.getDesativado());
            if (Validador.isValidaInteger(obj.getCapacidade())) {
                sqlInserir.setInt(11, obj.getCapacidade());
            } else {
                sqlInserir.setNull(11, Types.INTEGER);
            }
            sqlInserir.setBoolean(12, obj.getBloqueiaPelaVigencia());
            sqlInserir.setString(13, obj.getNcm());
            sqlInserir.setBoolean(14, obj.isPrevalecerVigenciaContrato());
            if (Validador.isValidaString(obj.getCodigoBarras())) {
                sqlInserir.setString(15, obj.getCodigoBarras());
            } else {
                sqlInserir.setNull(15, Types.VARCHAR);
            }
            sqlInserir.setString(16, obj.getObservacao());

            resolveIntegerNull(sqlInserir, 17, obj.getTamanhoArmario().getCodigo());

            sqlInserir.setString(18, obj.getCfop());
            sqlInserir.setString(19, obj.getCodigoListaServico());
            sqlInserir.setString(20, obj.getCodigoTributacaoMunicipio());
            sqlInserir.setDouble(21, obj.getAliquotaPIS());
            sqlInserir.setDouble(22, obj.getAliquotaCOFINS());
            sqlInserir.setDouble(23, obj.getAliquotaICMS());
            sqlInserir.setDouble(24, obj.getAliquotaISSQN());
            sqlInserir.setString(25, obj.getNcmNFCe());
            sqlInserir.setInt(26, obj.getPontos());
            sqlInserir.setBoolean(27, obj.isAparecerAulaCheia());
            sqlInserir.setString(28, obj.getPrefixo());
            resolveIntegerNull(sqlInserir, 29, obj.getConfiguracaoNotaFiscalNFSe().getCodigo());
            resolveIntegerNull(sqlInserir, 30, obj.getConfiguracaoNotaFiscalNFCe().getCodigo());
            sqlInserir.setString(31, obj.getSituacaoTributariaICMS());
            sqlInserir.setString(32, obj.getSituacaoTributariaISSQN());
            sqlInserir.setString(33, obj.getSituacaoTributariaPIS());
            sqlInserir.setString(34, obj.getSituacaoTributariaCOFINS());
            sqlInserir.setString(35, obj.getUnidadeMedida());
            sqlInserir.setBoolean(36, obj.isEnviarPercentualImposto());
            sqlInserir.setDouble(37, obj.getPercentualFederal());
            sqlInserir.setDouble(38, obj.getPercentualEstadual());
            sqlInserir.setDouble(39, obj.getPercentualMunicipal());
            sqlInserir.setBoolean(40, obj.isIsentoPIS());
            sqlInserir.setBoolean(41, obj.isIsentoCOFINS());
            sqlInserir.setBoolean(42, obj.isIsentoICMS());
            resolveIntegerNull(sqlInserir, 43, obj.getId_externo());
            resolveIntegerNull(sqlInserir, 44, obj.getQtdePontos());
            sqlInserir.setBoolean(45, obj.isApresentarVendasOnline());
            sqlInserir.setString(46, obj.getImagens());
            sqlInserir.setInt(47, obj.getMaxDivisao());
            sqlInserir.setBoolean(48, obj.isEnviaAliquotaNFePIS());
            sqlInserir.setBoolean(49, obj.isEnviaAliquotaNFeCOFINS());
            sqlInserir.setBoolean(50, obj.isEnviaAliquotaNFeICMS());
            resolveIntegerNull(sqlInserir, 51, obj.getModalidadeVendasOnline());
            sqlInserir.setDouble(52, obj.getPrecoCusto());
            sqlInserir.setDouble(53, obj.getMargemLucro());
            sqlInserir.setInt(54, obj.getQtdConvites());
            sqlInserir.setString(55, obj.getCest());
            resolveIntegerNull(sqlInserir, 56, obj.getCodigoProdutoSesi());
            resolveIntegerNull(sqlInserir, 57, obj.getNegocioSesi());
            sqlInserir.setString(58, obj.getCodigoBeneficioFiscal());
            sqlInserir.setBoolean(59, obj.getRenovavelAutomaticamente());
            sqlInserir.setBoolean(60, obj.isApresentarPactoApp());
            resolveIntegerNull(sqlInserir, 61, obj.getcRSesi());
            resolveIntegerNull(sqlInserir, 62, obj.getProjetoSesi());
            resolveIntegerNull(sqlInserir, 63, obj.getContaFinanceiraSesi());
            sqlInserir.setBoolean(64, obj.isApresentarPactoFlow());
            sqlInserir.setString(65, obj.getDescricaoServicoMunicipio());
            sqlInserir.setBoolean(66, obj.isDestravarTranca());
            resolveIntegerNull(sqlInserir, 67, obj.getContratoTextoPadrao());
            resolveIntegerNull(sqlInserir, 68, obj.getIdProdutoSMD());
            resolveIntegerNull(sqlInserir, 69, obj.getCodigoFormulario());
            sqlInserir.setBoolean(70, obj.isExibirRelatorioSMD());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());

        gravarPacotesPersonal(obj);
        ConfiguracaoProdutoEmpresa configuracaoProdutoEmpresa = new ConfiguracaoProdutoEmpresa(con);
        configuracaoProdutoEmpresa.alterarPorProduto(obj);
        ConfiguracaoProdutoEmpresaPlano configuracaoProdutoEmpresaPlano = new ConfiguracaoProdutoEmpresaPlano(con);
        configuracaoProdutoEmpresaPlano.alterarPorProduto(obj);
        obj.setNovoObj(false);
    }

    /**
     * Responsável por incluir no BD os dados de um objeto da classe
     * <code>ProdutoVO</code> no ZW.
     *
     * <AUTHOR> 22/03/2011
     * @param obj
     * @throws Exception
     */
    public void incluir(ProdutoVO obj) throws Exception {
        this.incluir(obj, false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>ProdutoVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ProdutoVO</code> que será alterada no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(ProdutoVO obj, boolean centralEventos) throws Exception {
        ConfiguracaoProdutoEmpresa configuracaoProdutoEmpresaDAO;
        ConfiguracaoProdutoEmpresaPlano configuracaoProdutoEmpresaPlanoDAO;
        ComissaoProdutoConfiguracao comissaoProdutoConfiguracaoDAO;
        try {
            con.setAutoCommit(false);
            configuracaoProdutoEmpresaDAO = new ConfiguracaoProdutoEmpresa(this.con);
            configuracaoProdutoEmpresaPlanoDAO = new ConfiguracaoProdutoEmpresaPlano(this.con);
            comissaoProdutoConfiguracaoDAO = new ComissaoProdutoConfiguracao(this.con);

            ProdutoVO.validarDados(obj);
            if (!centralEventos) {
                alterar(getIdEntidade());
            }

            obj.realizarUpperCaseDados();
            String sql = "UPDATE Produto set categoriaProduto=?, descricao=?, tipoVigencia=?, dataInicioVigencia=?, dataFinalVigenciaFixa=?, nrDiasVigencia=?,  valorFinal=?, tipoProduto=?, valorbasecalculo=?,\n"
                    + "desativado=?, capacidade = ?, bloqueiapelavigencia = ?, ncm=?, prevalecerVigenciaContrato = ?, codigoBarras = ?, observacao = ?, tamanhoArmario = ?,\n"
                    + "cfop = ?, codigoListaServico = ?, codigoTributacaoMunicipio = ?, aliquotaPIS = ?, aliquotaCOFINS = ?, aliquotaICMS = ?, aliquotaISSQN = ?,\n"
                    + "ncmNFCe = ?, pontos = ?, apareceraulacheia = ?, prefixo = ?, configuracaonotafiscalNFSe = ?, configuracaonotafiscalNFCe = ?,\n"
                    + "situacaoTributariaICMS = ?, situacaoTributariaISSQN = ?, situacaoTributariaPIS = ?, situacaoTributariaCOFINS = ?, unidadeMedida = ?,\n"
                    + "enviarPercentualImposto = ?, percentualFederal = ?, percentualEstadual = ?, percentualMunicipal = ?, isentoPIS = ?, isentoCOFINS = ?,\n"
                    + "isentoICMS = ?, id_externo = ?,qtdePontos = ?, apresentarVendasOnline = ?, imagens = ?, maxDivisao = ?,\n"
                    + "enviaAliquotaNFePIS = ?, enviaAliquotaNFeCOFINS = ?, enviaAliquotaNFeICMS = ?, modalidadevendasonline = ?, precoCusto = ?, margemLucro = ?,\n"
                    + "qtdConvites = ?, cest = ?, codigoprodutosesi = ?, negociosesi = ?, codigoBeneficioFiscal = ?, renovavelAutomaticamente = ?, apresentarpactoapp = ?, \n"
                    + "crsesi = ?, projetosesi = ? , contafinanceirasesi = ?, apresentarPactoFlow = ? , descricaoServicoMunicipio = ?, destravarTranca = ?, contratoTextoPadrao = ?, mesclado = ?, \n "
                    + "idprodutosmd = ?, codigoformulario = ?, exibirrelatoriosmd = ? "
                    + "WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                if (obj.getCategoriaProduto().getCodigo() != 0) {
                    sqlAlterar.setInt(1, obj.getCategoriaProduto().getCodigo());
                } else {
                    sqlAlterar.setNull(1, 0);
                }
                sqlAlterar.setString(2, Uteis.getStringNormalizada(obj.getDescricao().toUpperCase()));
                sqlAlterar.setString(3, obj.getTipoVigencia());
                sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
                sqlAlterar.setDate(5, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
                sqlAlterar.setInt(6, obj.getNrDiasVigencia());
                sqlAlterar.setDouble(7, obj.getValorFinal());
                sqlAlterar.setString(8, obj.getTipoProduto());
                sqlAlterar.setDouble(9, obj.getValorBaseCalculo());
                sqlAlterar.setBoolean(10, obj.getDesativado());

                if (Validador.isValidaInteger(obj.getCapacidade())) {
                    sqlAlterar.setInt(11, obj.getCapacidade());
                } else {
                    sqlAlterar.setNull(11, Types.INTEGER);
                }

                sqlAlterar.setBoolean(12, obj.getBloqueiaPelaVigencia());
                sqlAlterar.setString(13, obj.getNcm());
                sqlAlterar.setBoolean(14, obj.isPrevalecerVigenciaContrato());
                if (Validador.isValidaString(obj.getCodigoBarras())) {
                    sqlAlterar.setString(15, obj.getCodigoBarras());
                } else {
                    sqlAlterar.setNull(15, 0);
                }

                sqlAlterar.setString(16, obj.getObservacao());
                resolveIntegerNull(sqlAlterar, 17, obj.getTamanhoArmario().getCodigo());

                sqlAlterar.setString(18, obj.getCfop());
                sqlAlterar.setString(19, obj.getCodigoListaServico());
                sqlAlterar.setString(20, obj.getCodigoTributacaoMunicipio());
                sqlAlterar.setDouble(21, obj.getAliquotaPIS());
                sqlAlterar.setDouble(22, obj.getAliquotaCOFINS());
                sqlAlterar.setDouble(23, obj.getAliquotaICMS());
                sqlAlterar.setDouble(24, obj.getAliquotaISSQN());
                sqlAlterar.setString(25, obj.getNcmNFCe());
                sqlAlterar.setInt(26, obj.getPontos());
                sqlAlterar.setBoolean(27, obj.isAparecerAulaCheia());
                sqlAlterar.setString(28, obj.getPrefixo());
                resolveIntegerNull(sqlAlterar, 29, obj.getConfiguracaoNotaFiscalNFSe().getCodigo());
                resolveIntegerNull(sqlAlterar, 30, obj.getConfiguracaoNotaFiscalNFCe().getCodigo());
                sqlAlterar.setString(31, obj.getSituacaoTributariaICMS());
                sqlAlterar.setString(32, obj.getSituacaoTributariaISSQN());
                sqlAlterar.setString(33, obj.getSituacaoTributariaPIS());
                sqlAlterar.setString(34, obj.getSituacaoTributariaCOFINS());
                sqlAlterar.setString(35, obj.getUnidadeMedida());
                sqlAlterar.setBoolean(36, obj.isEnviarPercentualImposto());
                sqlAlterar.setDouble(37, obj.getPercentualFederal());
                sqlAlterar.setDouble(38, obj.getPercentualEstadual());
                sqlAlterar.setDouble(39, obj.getPercentualMunicipal());
                sqlAlterar.setBoolean(40, obj.isIsentoPIS());
                sqlAlterar.setBoolean(41, obj.isIsentoCOFINS());
                sqlAlterar.setBoolean(42, obj.isIsentoICMS());
                resolveIntegerNull(sqlAlterar, 43, obj.getId_externo());
                resolveIntegerNull(sqlAlterar, 44, obj.getQtdePontos());
                sqlAlterar.setBoolean(45, obj.isApresentarVendasOnline());
                sqlAlterar.setString(46, obj.getImagens());
                sqlAlterar.setInt(47, obj.getMaxDivisao());
                sqlAlterar.setBoolean(48, obj.isEnviaAliquotaNFePIS());
                sqlAlterar.setBoolean(49, obj.isEnviaAliquotaNFeCOFINS());
                sqlAlterar.setBoolean(50, obj.isEnviaAliquotaNFeICMS());
                resolveIntegerNull(sqlAlterar, 51, obj.getModalidadeVendasOnline());
                sqlAlterar.setDouble(52, obj.getPrecoCusto());
                sqlAlterar.setDouble(53, obj.getMargemLucro());
                sqlAlterar.setInt(54, obj.getQtdConvites());
                sqlAlterar.setString(55, obj.getCest());
                resolveIntegerNull(sqlAlterar, 56, obj.getCodigoProdutoSesi());
                resolveIntegerNull(sqlAlterar, 57, obj.getNegocioSesi());
                sqlAlterar.setString(58, obj.getCodigoBeneficioFiscal());
                sqlAlterar.setBoolean(59, obj.getRenovavelAutomaticamente());
                sqlAlterar.setBoolean(60, obj.isApresentarPactoApp());
                resolveIntegerNull(sqlAlterar, 61, obj.getcRSesi());
                resolveIntegerNull(sqlAlterar, 62, obj.getProjetoSesi());
                resolveIntegerNull(sqlAlterar, 63, obj.getContaFinanceiraSesi());
                sqlAlterar.setBoolean(64, obj.isApresentarPactoFlow());
                sqlAlterar.setString(65, obj.getDescricaoServicoMunicipio());
                sqlAlterar.setBoolean(66, obj.isDestravarTranca());
                resolveIntegerNull(sqlAlterar, 67, obj.getContratoTextoPadrao());
                sqlAlterar.setBoolean(68, obj.isMesclado());
                resolveIntegerNull(sqlAlterar, 69, obj.getIdProdutoSMD());
                resolveIntegerNull(sqlAlterar, 70, obj.getCodigoFormulario());
                sqlAlterar.setBoolean(71, obj.isExibirRelatorioSMD());
                sqlAlterar.setInt(72, obj.getCodigo());
                sqlAlterar.execute();
            }

            gravarPacotesPersonal(obj);
            configuracaoProdutoEmpresaDAO.alterarPorProduto(obj);
            configuracaoProdutoEmpresaPlanoDAO.alterarPorProduto(obj);
            comissaoProdutoConfiguracaoDAO.atualizarPorProduto(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            configuracaoProdutoEmpresaDAO = null;
            configuracaoProdutoEmpresaPlanoDAO = null;
            comissaoProdutoConfiguracaoDAO = null;
        }
    }

    /**
     * Responsável por alterar no BD os dados de um objeto da classe
     * <code>ProdutoVO</code> no ZW.
     *
     * <AUTHOR> 22/03/2011
     * @param obj
     * @throws Exception
     */
    public void alterar(ProdutoVO obj) throws Exception {
        this.alterar(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ProdutoVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ProdutoVO</code> que será removido no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(ProdutoVO obj, boolean centralEventos) throws Exception {
                try {
                    con.setAutoCommit(false);
                    if (!centralEventos) {
                        excluir(getIdEntidade());
                    }
                    validarValidacaoAcesso(obj.getCodigo().intValue());
                    String sql = "DELETE FROM Produto WHERE ((codigo = ?))";
                    try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                        sqlExcluir.setInt(1, obj.getCodigo().intValue());
                        sqlExcluir.execute();
                    }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public ProdutoVO consultarPorDescricao(String descricao, int nivelMontarDados)throws Exception{
        String sql = "select * from produto where upper(descricao) = ?";
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setString(1, descricao.toUpperCase());
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;

    }

    public void validarValidacaoAcesso(Integer codigoProduto) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT la.descricao FROM validacaolocalacesso vla "
                + "INNER JOIN coletor c ON c.codigo = vla.coletor "
                + "INNER JOIN localacesso la ON c.localacesso = la.codigo "
                + "WHERE tipovalidacao = " + TipoValidacaoEnum.TIPOVALIDACAO_Produto.getId()
                + " AND chave = " + codigoProduto, con)) {
            if (rs.next()) {
                throw new Exception("Este produto não pode ser excluído pois está sendo usado como validação de acesso no local de acesso: "
                        + rs.getString("descricao"));
            }
        }
    }

    /**
     * Responsável por excluir no BD um objeto da classe
     * <code>ProdutoVO</code> no ZW.
     *
     * <AUTHOR> 22/03/2011
     * @param obj
     * @throws Exception
     */
    public void excluir(ProdutoVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Produto</code> através do valor do atributo
     * <code>String tipoVigencia</code>. Retorna os objetos, com início do valor
     * do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ProdutoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorTipoVigencia(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE upper( tipoVigencia ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoVigencia";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Produto</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ProdutoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE upper( descricao ) like('" + valorConsulta.toUpperCase().trim() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Produto</code> ativos, através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ProdutoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDescricaoAtivo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorDescricaoAtivo(valorConsulta,controlarAcesso,nivelMontarDados,"");
    }

    public List consultarPorDescricaoAtivo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, String arrayNotInTipos) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE desativado = false and upper( descricao ) like('" + valorConsulta.toUpperCase().trim() + "%') " +
                (!arrayNotInTipos.isEmpty()?" AND tipoProduto NOT IN ("+arrayNotInTipos+")":"")+" ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ProdutoVO> consultarPorDescricaoTipoProduto(String valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE (upper( descricao ) like('" + valorConsulta.toUpperCase().trim() + "%') or UPPER(codigobarras) like('" + valorConsulta.toUpperCase().trim() + "%')) and tipoProduto = '" + tipoProduto.toUpperCase() + "'  ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ProdutoVO> consultarPorProdutoZillyon(boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE (tipoProduto = 'SS' and upper( descricao ) = 'PRODUTO GENÉRICO'  ) or tipoProduto = 'QU'  ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }


    public List<ProdutoVO> consultarPorDescricaoTipoProdutoAtivo(String valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE (upper( descricao ) like('%" + valorConsulta.toUpperCase() + "%') or UPPER(codigobarras) like('" + valorConsulta.toUpperCase() + "%')) and tipoProduto = '" + tipoProduto.toUpperCase() + "'  and desativado = false ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorDescricaoCategoriaTipoProdutoAtivo(String descricaoProduto, Integer codigoCategoria, String tipoProduto, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * ");
        sql.append("FROM Produto ");
        sql.append("WHERE upper( descricao ) like('").append(descricaoProduto.toUpperCase().trim()).append("%') ");
        sql.append("and tipoProduto = '").append(tipoProduto.toUpperCase()).append("'  and desativado = false ");
        sql.append("and categoriaProduto = ").append(codigoCategoria);
        sql.append("ORDER BY descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }

    }

    public List consultarProdutosComControleEstoque(Integer codigoEmpresa, Integer codigoCategoriaProduto, String descricaoProduto, boolean somenteProdutoEstoqueAtivo, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("prod.* \n");
        sql.append("FROM Produto prod \n");
        sql.append("INNER JOIN produtoEstoque pe on pe.produto = prod.codigo \n");
        sql.append("WHERE pe.empresa = ").append(codigoEmpresa).append(" \n");
        sql.append("AND (prod.descricao ilike '").append(descricaoProduto).append("%' \n");
        sql.append("OR prod.codigobarras ilike '").append(descricaoProduto).append("%' \n");
        sql.append("OR prod.codigo :: TEXT like '").append(descricaoProduto).append("%' ) \n");

        if (somenteProdutoEstoqueAtivo) {
            sql.append(" and pe.situacao = 'A' \n");
            sql.append(" and prod.desativado = false \n");
        }

        if (!UteisValidacao.emptyNumber(codigoCategoriaProduto)) {
            sql.append("and prod.categoriaProduto =").append(codigoCategoriaProduto).append(" \n");
        }
        sql.append(" ORDER BY prod.descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public Optional<ProdutoVO> consultarProdutosComControleEstoquePorCodigoDeBarrasOuDescricao(String codigoBarras, String descricaoProduto, int nivelMontarDados) throws Exception {
        String sql = "SELECT prod.* FROM Produto prod WHERE prod.tipoProduto = 'PE' AND (prod.codigoBarras = ? OR prod.descricao ILIKE '%' || ? || '%') ORDER BY prod.codigoBarras NULLS LAST LIMIT 1";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, codigoBarras);
            ps.setString(2, descricaoProduto);

            try (ResultSet tabelaResultado = ps.executeQuery()) {
                if (tabelaResultado.next()) {
                    return Optional.of(montarDados(tabelaResultado, nivelMontarDados, this.con));
                }
            }
        }

        return Optional.empty();
    }


    public ProdutoVO consultarPorTipoProduto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE tipoProduto = '" + valorConsulta.toUpperCase() + "'  and desativado is false ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new ProdutoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorDescricaoDiferenteTipoProduto(String valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') and tipoProduto != '" + tipoProduto.toUpperCase() + "'  ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorDescricaoDiferenteTipoProdutoAtivo(String valorConsulta, List<String> listaTipoProduto, boolean controlarAcesso,Boolean somenteComEstoque,int codigoEmpresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT p.*,pe.codigo codigoEstoque,  ");
        sql.append("cpe.empresa AS cfgempresa, ");
        sql.append("cpe.valor AS cfgvalor");
        sql.append(" FROM Produto p\n");
        sql.append(" LEFT JOIN ProdutoEstoque pe ON pe.produto = p.codigo\n");
        sql.append(" LEFT JOIN configuracaoprodutoempresa cpe ON cpe.produto = p.codigo ");
        sql.append(" WHERE TRUE ");
        if(!valorConsulta.trim().equals("")) {
            sql.append(" AND upper( p.descricao ) like '").append(valorConsulta.toUpperCase()).append("%'");
        }
        if (somenteComEstoque){
            sql.append(" AND ((pe.codigo is null or  pe.empresa = ").append(codigoEmpresa).append(")");
            sql.append(" AND  (pe.codigo is null or pe.estoque > 0)) ");
        }
        for (String tipo : listaTipoProduto) {
            sql.append(" AND p.tipoProduto <> '").append(tipo.toUpperCase()).append("' ");
        }
        sql.append(" and p.desativado = false ORDER BY p.descricao ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorDescricaoTipoProdutoSemPMCDDE(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') and (tipoProduto != 'PM' and tipoProduto !='CD' and tipoProduto != 'DE' and tipoProduto != 'FR')  ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorDescricaoTipoProdutoSemPMCDDEAtivo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto "
                + "WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') and "
                + "(tipoProduto != 'PM' and tipoProduto !='CD' and "
                + "tipoProduto != 'DE' and tipoProduto != 'FR' and "
                + "tipoProduto != 'DI' and tipoProduto != 'AA' and "
                + "tipoProduto != 'TR'  and tipoProduto != 'MA' and "
                + "tipoProduto != 'RN' and tipoProduto != 'RE' and "
                + "tipoProduto != 'QU' and tipoProduto != 'DV') and "
                + "tipoProduto != 'AR' "
                + "and desativado = false ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorNomeProdutoComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorNomeProdutoComLimite(valorConsulta, controlarAcesso, null, nivelMontarDados);
    }

    public List consultarPorNomeProdutoComLimite(String valorConsulta, boolean controlarAcesso, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("SELECT cpe.empresa AS cfgempresa,cpe.valor as cfgvalor, p.* FROM Produto p \n");
        sqlStr.append(" LEFT JOIN configuracaoprodutoempresa cpe ON cpe.produto = p.codigo \n");
        sqlStr.append(" WHERE (upper( p.descricao ) like('");
        sqlStr.append(valorConsulta.toUpperCase()).append("%') OR upper(p.codigobarras) like('");
        sqlStr.append(valorConsulta.toUpperCase()).append("'))\n and ");
        sqlStr.append(adicionarFiltroTipoProduto());
        sqlStr.append("\n  and p.descricao <> 'PRODUTO GENÉRICO' and (cpe.empresa = ").append(codigoEmpresa).append(" OR cpe.codigo IS NULL) \n");
        sqlStr.append(" ORDER BY descricao limit 50");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorTodosServicoComLimite(boolean controlarAcesso, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM Produto WHERE tipoProduto = 'SS' ");
        sqlStr.append("ORDER BY descricao limit 50");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorNomeServicoComLimite(String valorConsulta, boolean controlarAcesso, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM Produto ");
        sqlStr.append("WHERE (upper( descricao ) like('").append(valorConsulta.toUpperCase()).append("%') OR upper(codigobarras) like('").append(valorConsulta.toUpperCase()).append("%')) and tipoProduto = 'SS' ");
        sqlStr.append(" ORDER BY descricao limit 50");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public ProdutoVO consultarPorNomeProduto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        List lista = consultarPorNomeProdutoComLimite(valorConsulta, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (!lista.isEmpty()) {
            return (ProdutoVO) lista.get(0);
        } else {
            return new ProdutoVO();
        }
    }

    /**
     * Adiciona filtro de tipo de produto nas consultas. Não pode mostrar
     * registros de produtos do tipo: Aula Avulsa, Convênio de Desconto,
     * Desconto, Desconto Renovação Antecipada, Diária, Free Pass, Matrícula,
     * Plano Mensal, Rematrícula, Renovação, Taxa de Adesão Plano Recorrência,
     * Taxa de Anuidade Plano Recorrência, Taxa de Personal Trainer, Alterar
     * Horário, Devolução de Dinheiro- Cancelamento, Manutenção de Modalidade,
     * Quitação de Cancelamento, Taxa de Cancelamento, Trancamento, Devolução de
     * Recebíveis
     *
     * @return
     */
    public String adicionarFiltroTipoProduto() {
        String filtroVendaAvulsa = filtroVendaAvulsa();
        return (filtroVendaAvulsa.isEmpty() ? "" : (filtroVendaAvulsa + " and ")) + " not desativado";
    }

    /**
     *        "MA", "Matrícula"
     *         "PE", "Produto Estoque"
     *         "PM", "Plano Mensal"
     *         "RE", "Rematrícula"
     *         "RN", "Renovação"
     *         "SE", "Serviço"
     *         "CD", "Convênio de Desconto"
     *         "CP", "Crédito Personal"
     *         "DE", "Desconto"
     *         "FR", "Free Pass"
     *         "AA", "Aula Avulsa"
     *         "DI", "Diária"
     *         "TR", "Trancamento"
     *         "DR", "Desconto Renovação Antecipada"
     *         "TP", "Taxa de Personal Trainer"
     *         "TD", "Taxa de Adesão Plano Recorrência"
     *         "TA", "Taxa de Anuidade Plano Recorrência"
     *         "SS", "Sessão"
     *         "AT", "Atestado"
     *         "AR", "Armário"
     */

    public List consultarTodosProdutosSemPontuacaoPaginado(boolean controlarAcesso, Integer codigoEmpresa, int nivelMontarDados, ListaPaginadaTO listaPaginadaTO) throws Exception {
        StringBuilder sqlStr = new StringBuilder(" FROM Produto p ");
        sqlStr.append(" LEFT JOIN configuracaoprodutoempresa cpe ON cpe.produto = p.codigo ");
        sqlStr.append(" WHERE ");
        sqlStr.append(" p.descricao <> 'PRODUTO GENÉRICO' AND not p.desativado AND  pontos = 0 AND (");
        if(codigoEmpresa>0)
            sqlStr.append("cpe.empresa = '"+codigoEmpresa+"' OR ");
        sqlStr.append(" cpe.codigo IS NULL ) AND p.tipoproduto not in ('AC', 'AH', 'CC', 'DC', 'DV', 'RD', 'CH', 'AT', 'MM', 'PM', 'CD', 'QU', 'CP', 'DE', 'DR', 'TN', 'FR', 'TR', 'TP')");
        sqlStr.append(" AND (NOW() BETWEEN p.datainiciovigencia AND p.datafinalvigenciafixa OR (p.datainiciovigencia IS NULL AND p.datafinalvigenciafixa IS NULL)) ");

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery("SELECT COUNT(*) totallinhas "+ sqlStr.toString());
        if(tabelaResultado.next())
            listaPaginadaTO.setCount(tabelaResultado.getInt("totallinhas"));

        sqlStr.append(" ORDER BY descricao ");
        sqlStr.append(" LIMIT "+ listaPaginadaTO.getLimit());
        sqlStr.append(" OFFSET "+listaPaginadaTO.getOffset());

        tabelaResultado = stm.executeQuery("SELECT cpe.empresa AS cfgempresa, cpe.valor as cfgvalor, p.* "+ sqlStr.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public List consultarPorTodosProdutosComLimit(boolean controlarAcesso, Integer codigoEmpresa, int limit, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("SELECT cpe.empresa AS cfgempresa, cpe.valor as cfgvalor, p.* FROM Produto p \n");
        sqlStr.append(" LEFT JOIN configuracaoprodutoempresa cpe ON cpe.produto = p.codigo \n");
        sqlStr.append(" WHERE ");
        sqlStr.append(adicionarFiltroTipoProduto());
        sqlStr.append(" and p.descricao <> 'PRODUTO GENÉRICO' and  (cpe.empresa = ").append(codigoEmpresa).append(" OR cpe.codigo IS NULL) ");
        sqlStr.append("ORDER BY descricao");
        if (limit > 0) {
            sqlStr.append("\n limit ").append(limit);
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorProdutoComVigencia(String valorConsulta, Integer codigoEmpresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        // VALIDAÇÃO DE SEGURANÇA: Verificar se valorConsulta é seguro
        if (!Uteis.isValidStringValue(valorConsulta)) {
            throw new SecurityException("Valor de consulta contém caracteres não permitidos");
        }

        StringBuilder sqlStr = new StringBuilder("SELECT cpe.empresa AS cfgempresa,cpe.valor as cfgvalor, p.* FROM Produto p \n");
        sqlStr.append(" LEFT JOIN configuracaoprodutoempresa cpe ON cpe.produto = p.codigo \n");
        sqlStr.append(" WHERE (upper( p.descricao ) like(?) OR upper(p.codigobarras) like(?))\n and tipovigencia <> '' ");
        sqlStr.append("\n and (cpe.empresa = ? OR cpe.codigo IS NULL) \n");
        sqlStr.append(" ORDER BY descricao limit 50");

        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString())) {
            String valorConsultaUpper = valorConsulta.toUpperCase() + "%";
            stm.setString(1, valorConsultaUpper);  // p.descricao like
            stm.setString(2, valorConsultaUpper);  // p.codigobarras like
            stm.setInt(3, codigoEmpresa);          // cpe.empresa

            try (ResultSet tabelaResultado = stm.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

//    public List consultarPorProdutoPlano(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
//        SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
//        String sqlStr = "SELECT * FROM Produto WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') and (tipoProduto != 'PM' and tipoProduto !='CD' and tipoProduto != 'DE'  )  ORDER BY descricao";
//        Statement stm = con.createStatement();
//        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
//        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
//    }
    /**
     * Responsável por realizar uma consulta de
     * <code>Produto</code> através do valor do atributo
     * <code>descricao</code> da classe
     * <code>CategoriaProduto</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>ProdutoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDescricaoCategoriaProduto(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Produto.* FROM Produto, CategoriaProduto WHERE Produto.categoriaProduto = CategoriaProduto.codigo and upper( CategoriaProduto.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY CategoriaProduto.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<ProdutoVO> consultarPorCodigoCategoriaProduto(Integer codigoCategoriaProduto, TipoProduto tp, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("     SELECT produto.* ");
        sql.append("       FROM produto ");
        sql.append(" INNER JOIN categoriaproduto on produto.categoriaproduto = categoriaproduto.codigo ");
        sql.append("      WHERE categoriaproduto.codigo = ? ");
        if (tp != null) {
            sql.append("    AND produto.tipoproduto = ? ");
        }
        sql.append(" ORDER BY CategoriaProduto.descricao ");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setInt(1, codigoCategoriaProduto);
            if (tp != null) {
                stm.setString(2, tp.getCodigo());
            }
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Autor: Pedro Y. Saito Criado em 01/02/2011
     */
    private String obterTipoVigencia(String valor) {
        String result = "";

        if ("Intervalo de Dias".equalsIgnoreCase(valor)) {
            result = "ID";
        }
        if ("Produto Estoque".equalsIgnoreCase(valor)) {
            result = "PF";
        }

        return result;
    }

    /**
     * Autor: Pedro Y. Saito Criado em 30/12/2010
     */
    private List<String> obterPossiveisTiposProduto(String valor) {
        List<String> tipos = new ArrayList<String>();
        boolean todos = false;
        if (valor == null || valor.trim().equals("")) {
            todos = true;
        }
        if (todos || "Matrícula".equalsIgnoreCase(valor)) {
            tipos.add("MA");
        }
        if (todos || "Produto Estoque".equalsIgnoreCase(valor)) {
            tipos.add("PE");
        }
        if (todos || "Mês de Referência Plano".equalsIgnoreCase(valor)) {
            tipos.add("PM");
        }
        if (todos || "Rematrícula".equalsIgnoreCase(valor)) {
            tipos.add("RE");
        }
        if (todos || "Renovação".equalsIgnoreCase(valor)) {
            tipos.add("RN");
        }
        if (todos || "Serviço".equalsIgnoreCase(valor)) {
            tipos.add("SE");
        }
        if (todos || "Convênio de Desconto".equalsIgnoreCase(valor)) {
            tipos.add("CD");
        }
        if (todos || "Desconto".equalsIgnoreCase(valor)) {
            tipos.add("DE");
        }
        if (todos || "Devolução".equalsIgnoreCase(valor)) {
            tipos.add("DV");
        }
        if (todos || "Trancamento".equalsIgnoreCase(valor)) {
            tipos.add("TR");
        }
        if (todos || "Retorno Trancamento".equalsIgnoreCase(valor)) {
            tipos.add("RT");
        }
        if (todos || "Aula Avulsa".equalsIgnoreCase(valor)) {
            tipos.add("AA");
        }
        if (todos || "Diária".equalsIgnoreCase(valor)) {
            tipos.add("DI");
        }
        if (todos || "FreePass".equalsIgnoreCase(valor)) {
            tipos.add("FR");
        }
        if (todos || "Alterar - Horário".equalsIgnoreCase(valor)) {
            tipos.add("AH");
        }
        if (todos || "Manutenção Modalidade".equalsIgnoreCase(valor)) {
            tipos.add("MM");
        }
        if (todos || "Manutenção Conta Corrente".equalsIgnoreCase(valor)) {
            tipos.add("MC");
        }
        if (tipos.isEmpty()) {
            tipos.add("NENHUM");
        }
        return tipos;
    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco, de acordo
     * com os filtros passados
     *
     * Autor: Pedro Y. Saito Criado em 29/12/2010
     */
    @SuppressWarnings("unchecked")
    public List consultarPaginado(ControleConsulta controleConsulta, boolean controlarAcesso, int nivelMontarDados, ConfPaginacao confPaginacao) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);

        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //sql principal
        StringBuffer sqlSelect = new StringBuffer("SELECT Produto.* ");
        sqlSelect.append(" FROM Produto ");
        if (controleConsulta != null && controleConsulta.getCampoConsulta() != null && "descricaoCategoriaProduto".equals(controleConsulta.getCampoConsulta())) {
            sqlSelect.append(", CategoriaProduto ");
        }

        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);

        //a utilização dos filtros deve ser feita utilizando confPaginacao.getSqlFiltro()
        filtros(controleConsulta, confPaginacao.getSqlFiltro());
        if (!"".equals(confPaginacao.getSqlFiltro().toString())) {
            sqlSelect.append(" WHERE ");
            sqlSelect.append(confPaginacao.getSqlFiltro().toString());
        }

        //concatena ordenações
        ordernacao(controleConsulta, sqlSelect);

        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlSelect);

        //seta os valores dos filtros
        filtrosValoresStatement(controleConsulta, confPaginacao.getStm());

        //4 - REALIZA A CONSULTA COM PAGINACAO
        try (ResultSet tabelaResultado = confPaginacao.consultaPaginada()) {
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
        }
    }

    /**
     * Autor: Pedro Y. Saito Criado em 21/01/2011
     */
    private void filtrosValoresStatement(ControleConsulta controleConsulta, PreparedStatementPersonalizado stm) throws SQLException {
        List<String> tipos = null;
        int i = 0;

        if (controleConsulta != null && controleConsulta.getCampoConsulta() != null && !"".equals(controleConsulta.getCampoConsulta())) {
            if (controleConsulta.getValorConsulta() != null && !"".equals(controleConsulta.getValorConsulta().trim())) {
                if (controleConsulta.getCampoConsulta().equals("codigo")) {
                    //Setando o parametro para pesquisa por codigo
                    stm.setInt(i++, Integer.parseInt(controleConsulta.getValorConsulta()));
                } else if (controleConsulta.getCampoConsulta().equals("tipoProduto")) {
                    /* //Setando o parametro para pesquisa por tipo de Produto
                     tipos = obterPossiveisTiposProduto(controleConsulta.getValorConsulta());
                     for (int j = 0; j < tipos.size(); j++) {
                     stm.setString(i++, tipos.get(j));
                     }*/
                    stm.setString(i++, controleConsulta.getValorConsulta());
                } else if (controleConsulta.getCampoConsulta().equals("descricao")) {
                    stm.setString(i++, controleConsulta.getValorConsulta() + "%");
                } else if (controleConsulta.getCampoConsulta().equals("descricaoCategoriaProduto")) {
                    stm.setString(i++, controleConsulta.getValorConsulta() + "%");
                } else {
                    /*  String param = obterTipoVigencia(controleConsulta.getValorConsulta());
                     //Seta o parametro para "Descricao", "Categoria de Produto" e "Tipo de Vigencia"
                     stm.setString(i++, param + "%");*/
                    stm.setString(i++, controleConsulta.getValorConsulta());
                }
            }
        }
    }

    /**
     * Autor: Pedro Y. Saito Criado em 21/01/2011
     */
    private void ordernacao(ControleConsulta controleConsulta, StringBuffer sql) {
        if (controleConsulta != null && controleConsulta.getCampoConsulta() != null && !"".equals(controleConsulta.getCampoConsulta())) {
            if (controleConsulta.getCampoConsulta().equals("codigo")) {
                sql.append(" ORDER BY Produto.codigo ");
            } else if (controleConsulta.getCampoConsulta().equals("descricaoCategoriaProduto")) {
                sql.append(" ORDER BY CategoriaProduto.descricao ");
            } else if (controleConsulta.getCampoConsulta().equals("tipoVigencia")) {
                sql.append(" ORDER BY tipoVigencia ");
            } else {
                sql.append(" ORDER BY descricao ");
            }
        }
    }

    /**
     * Autor: Pedro Y. Saito Criado em 21/01/2011
     */
    private void filtros(ControleConsulta controleConsulta, StringBuffer sqlFiltro) {
        List<String> tipos = null;

        if (controleConsulta != null && controleConsulta.getCampoConsulta() != null && !"".equals(controleConsulta.getCampoConsulta())) {
            if (controleConsulta.getValorConsulta() != null && !"".equals(controleConsulta.getValorConsulta().trim())) {

                if (controleConsulta.getCampoConsulta().equals("codigo")) {
                    sqlFiltro.append(" Produto.codigo >= ? ");
                } else if (controleConsulta.getCampoConsulta().equals("descricaoCategoriaProduto")) {
                    sqlFiltro.append(" Produto.categoriaProduto = CategoriaProduto.codigo and ");
                    sqlFiltro.append(" CategoriaProduto.descricao ilike ? ");
                } else if (controleConsulta.getCampoConsulta().equals("descricao")) {
                    sqlFiltro.append(" descricao ilike ? ");
                } else if (controleConsulta.getCampoConsulta().equals("tipoProduto")) {
                    /*tipos = obterPossiveisTiposProduto(controleConsulta.getValorConsulta());
                     sqlFiltro.append(" tipoProduto IN( ");
                     for (int i = 0; i < (tipos.size() - 1); i++) {
                     sqlFiltro.append(" ?").append(", ");
                     }
                     sqlFiltro.append(" ?");
                     sqlFiltro.append(" ) ");*/
                    sqlFiltro.append(" tipoProduto IN( ? )");
                } else if (controleConsulta.getCampoConsulta().equals("tipoVigencia")) {
                    sqlFiltro.append(" tipovigencia IN( ? )");
                }

            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Produto</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ProdutoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorCodigoTipoProduto(Integer valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE codigo >= " + valorConsulta.intValue() + "  and tipoProduto = '" + tipoProduto.toUpperCase() + "' and desativado = false ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public ProdutoVO consultarProdutoPorCodigoTipoProduto(Integer valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sql = "SELECT * FROM Produto WHERE codigo = " + valorConsulta.intValue() + "  and tipoProduto = '" + tipoProduto.toUpperCase() + "' ORDER BY codigo";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Produto ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorCodigoDiferenteTipoProduto(Integer valorConsulta, String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE codigo >= " + valorConsulta.intValue() + " and tipoProduto != '" + tipoProduto.toUpperCase() + "'  ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorCodigoDiferenteTipoProdutoAtivo(Integer valorConsulta, List<String> tipoProduto, boolean controlarAcesso,boolean somenteComEstoque,
                                                            int codigoEmpresa,int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT p.*,pe.codigo codigoEstoque, cpe.empresa AS cfgempresa, ");
        sql.append("cpe.valor AS cfgvalor");
        sql.append(" FROM Produto p\n");
        sql.append(" LEFT JOIN ProdutoEstoque pe ON pe.produto = p.codigo\n");
        sql.append(" LEFT JOIN configuracaoprodutoempresa cpe ON cpe.produto = p.codigo ");
        sql.append(" WHERE ");
        sql.append(" p.codigo = ").append(valorConsulta.intValue());
        if (somenteComEstoque){
            sql.append(" AND ((pe.codigo is null or  pe.empresa = ").append(codigoEmpresa).append(")");
            sql.append(" AND  (pe.codigo is null or pe.estoque > 0)) ");
        }
        for (String tipo : tipoProduto) {
            sql.append(" and p.tipoProduto <> '" + tipo + "' ");
        }
        sql.append("and p.desativado = false ORDER BY p.codigo ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ProdutoVO> consultarProdutosPorTipoProduto(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("   SELECT * ");
        sql.append("     FROM produto ");
        sql.append("    WHERE tipoProduto = ? ");
        sql.append("      AND desativado is false ");
        sql.append(" ORDER BY descricao ");
        try (PreparedStatement pstm = con.prepareStatement(sql.toString())) {
            pstm.setString(1, valorConsulta);
            try (ResultSet tabelaResultado = pstm.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ProdutoVO> consultarProdutosPorTipoProdutoGympass(String tipoProduto, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);

        StringBuilder sql = new StringBuilder();
        sql.append("   SELECT * ");
        sql.append("     FROM produto ");
        sql.append("    WHERE tipoProduto = ? ");
        sql.append("      AND desativado IS FALSE ");
        sql.append("      AND (descricao ILIKE ? OR descricao ILIKE ?) ");
        sql.append("      AND  valorfinal = 0 ");
        sql.append(" ORDER BY descricao ");

        try (PreparedStatement pstm = con.prepareStatement(sql.toString())) {
            pstm.setString(1, tipoProduto);
            pstm.setString(2, "%gympass%");
            pstm.setString(3, "%wellhub%");

            try (ResultSet tabelaResultado = pstm.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }


    public List<ProdutoVO> consultarProdutosPorTipoProdutoTipoVigencia(String tipoProduto, String tipoVigencia, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto "
                + "WHERE tipoProduto LIKE ('" + tipoProduto.toUpperCase() + "%') "
                + "AND tipovigencia LIKE '" + tipoVigencia + "' "
                + "AND NOT desativado "
                + "ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ProdutoVO> consultarProdutosPorTipoProdutoNrDiasVigencia(String tipoProduto, int nrDiasVigencia, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM produto \n"
                + "WHERE tipoProduto LIKE ('" + tipoProduto.toUpperCase() + "%') \n"
                + "AND nrdiasvigencia = " + nrDiasVigencia + " \n"
                + "AND desativado IS FALSE \n"
                + "ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<ProdutoVO> consultarProdutosPorTipoProduto(List<String> valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM Produto WHERE tipoProduto IN(");
        for (int i = 0; i < (valorConsulta.size() - 1); i++) {
            sqlStr.append("'").append(valorConsulta.get(i)).append("', ");
        }
        sqlStr.append("'").append(valorConsulta.get(valorConsulta.size() - 1)).append("'");
        sqlStr.append(") ORDER BY descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public List<ProdutoVO> consultarProdutosComMovProduto(Date dataInicial, Date dataFinal, String tipoProduto, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();

        StringBuilder sb = new StringBuilder();
        sb.append("select produto.* ");
        sb.append(" from movProduto ");
        sb.append(" left join produto on movProduto.produto = produto.codigo ");
//        sb.append(" where datalancamento >= '%s 00:00:00' ");
//        sb.append(" and datalancamento =< '%s 23:59:59' ");
        sb.append(" where movProduto.datalancamento >= '").append(Uteis.getDataJDBC(dataInicial)).append("' ");
        sb.append(" and movProduto.datalancamento <= '").append(Uteis.getDataJDBC(dataFinal)).append("' ");
        sb.append(" and produto.tipoproduto = '").append(tipoProduto.toUpperCase()).append("' ");
        sb.append(" and movProduto.empresa = ").append(empresa.intValue()).append(" ");
        sb.append(" group by produto.codigo, produto.descricao, produto.tipoproduto, produto.desconto, produto.valorbasecalculo, ");
        sb.append(" produto.valorfinal, produto.nrdiasvigencia, produto.datafinalvigenciafixa, produto.datainiciovigencia, produto.tipovigencia, produto.categoriaproduto, produto.desativado ");
        sb.append(" order by produto.descricao ");

        String sqlstr = sb.toString();
        List<ProdutoVO> listaProduto;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlstr)) {
                listaProduto = new ArrayList<ProdutoVO>();
                while (tabelaResultado.next()) {
                    ProdutoVO obj = new ProdutoVO();
                    Integer codigo = new Integer(tabelaResultado.getInt("codigo"));
                    listaProduto.add(consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    vetResultado.add(obj);
                }
            }
        }
        return listaProduto;
    }

    public List consultarPorCodigoTipoProdutoSemPMCDDE(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE codigo >= " + valorConsulta.intValue() + " and (tipoProduto != 'PM' and tipoProduto !='CD' and tipoProduto != 'DE' and tipoProduto != 'FR' )  ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorCodigoTipoProdutoSemPMCDDEAtivo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE codigo >= " + valorConsulta.intValue() + " and (tipoProduto != 'PM' and tipoProduto !='CD' "
                + "and tipoProduto != 'DE' and tipoProduto != 'FR' and tipoProduto != 'MA' and tipoProduto != 'TR' "
                + "and tipoProduto != 'DI' and tipoProduto != 'AA' and tipoProduto != 'RN' and tipoProduto != 'RE' "
                + " and tipoProduto != 'QU' and tipoProduto != 'DV') "
                + "and desativado=false  ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>ProdutoVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public ProdutoVO consultarPorCodigoProduto(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Produto WHERE codigo = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Produto ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>ProdutoVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public ProdutoVO consultarPorCodigoProdutoAtivo(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Produto WHERE codigo = ? and desativado = false";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Produto ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>ProdutoVO</code>
     * resultantes da consulta.
     */
    public static List<ProdutoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ProdutoVO> vetResultado = new ArrayList<ProdutoVO>();
        while (tabelaResultado.next()) {
            ProdutoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>ProdutoVO</code>.
     *
     * @return O objeto da classe <code>ProdutoVO</code> com os dados
     * devidamente montados.
     */
    public static ProdutoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ProdutoVO obj = new ProdutoVO();

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setDescricao(dadosSQL.getString("descricao"));
            obj.setTipoProduto(dadosSQL.getString("tipoProduto"));
            obj.setValorFinal(dadosSQL.getDouble("valorFinal"));
            obj.setCapacidade(dadosSQL.getInt("capacidade"));

            try {
                obj.setDestravarTranca(dadosSQL.getBoolean("destravarTranca"));
            } catch (Exception ignored) {
            }

            try {
                obj.setNcm(dadosSQL.getString("ncm"));
                obj.getTamanhoArmario().setCodigo(dadosSQL.getInt("tamanhoarmario"));
            } catch (Exception ignored) {
            }
            try {
                obj.setTipoVigencia(dadosSQL.getString("tipovigencia"));
            } catch (Exception ignored) {
            }
            obj.setObservacao(dadosSQL.getString("observacao"));
            try {
                obj.setModalidadeVendasOnline(dadosSQL.getInt("modalidadevendasonline"));
                obj.setDescricaoServicoMunicipio(dadosSQL.getString("descricaoServicoMunicipio"));
            }catch (Exception ignored){

            }
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_VENDA) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setDescricao(dadosSQL.getString("descricao"));
            obj.setTipoProduto(dadosSQL.getString("tipoProduto"));
            obj.setValorFinal(dadosSQL.getDouble("valorFinal"));
            obj.setCapacidade(dadosSQL.getInt("capacidade"));
            try {
                obj.setNcm(dadosSQL.getString("ncm"));
                obj.getTamanhoArmario().setCodigo(dadosSQL.getInt("tamanhoarmario"));
            } catch (Exception ignored) {
            }
            try {
                obj.setTipoVigencia(dadosSQL.getString("tipovigencia"));
            } catch (Exception ignored) {
            }
            try {
                obj.setRenovavelAutomaticamente(dadosSQL.getBoolean("renovavelAutomaticamente"));
            } catch (Exception ignored) {
            }
            obj.setObservacao(dadosSQL.getString("observacao"));
            obj.getCategoriaProduto().setCodigo(dadosSQL.getInt("categoriaProduto"));

            try {
                obj.setDestravarTranca(dadosSQL.getBoolean("destravarTranca"));
            } catch (Exception ignored) {
            }

            montarDadosCategoriaProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.getCategoriaProduto().setCodigo(dadosSQL.getInt("categoriaProduto"));
            obj.setDescricao(dadosSQL.getString("descricao"));
            obj.setTipoVigencia(dadosSQL.getString("tipoVigencia"));
            obj.setValorFinal(dadosSQL.getDouble("valorFinal"));
            obj.setTipoProduto(dadosSQL.getString("tipoProduto"));
            obj.setCapacidade(dadosSQL.getInt("capacidade"));
            obj.setNcm(dadosSQL.getString("ncm"));
            obj.setObservacao(dadosSQL.getString("observacao"));
            obj.setBloqueiaPelaVigencia(dadosSQL.getBoolean("bloqueiapelavigencia"));

            try {
                obj.setDestravarTranca(dadosSQL.getBoolean("destravarTranca"));
            } catch (Exception ignored) {
            }

            try {
                obj.setQtdePontos(dadosSQL.getInt("qtdepontos"));
            } catch (Exception ignored) {
            }
            montarDadosCategoriaProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getCategoriaProduto().setCodigo(dadosSQL.getInt("categoriaProduto"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setTipoVigencia(dadosSQL.getString("tipoVigencia"));
        obj.setDataInicioVigencia(dadosSQL.getDate("dataInicioVigencia"));
        obj.setDataFinalVigencia(dadosSQL.getDate("dataFinalVigenciaFixa"));
        obj.setNrDiasVigencia(dadosSQL.getInt("nrDiasVigencia"));
        obj.setValorFinal(dadosSQL.getDouble("valorFinal"));
        obj.setValorBaseCalculo(dadosSQL.getDouble("valorBaseCalculo"));
        obj.setTipoProduto(dadosSQL.getString("tipoProduto"));
        obj.setDesativado(dadosSQL.getBoolean("desativado"));
        obj.setCapacidade(dadosSQL.getInt("capacidade"));
        obj.setNcm(dadosSQL.getString("ncm"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setBloqueiaPelaVigencia(dadosSQL.getBoolean("bloqueiapelavigencia"));
        obj.setPrevalecerVigenciaContrato(dadosSQL.getBoolean("prevalecervigenciacontrato"));
        obj.setCodigoBarras(dadosSQL.getString(("codigoBarras")));

        obj.setCfop(dadosSQL.getString("cfop"));
        obj.setCodigoListaServico(dadosSQL.getString("codigoListaServico"));
        obj.setCodigoTributacaoMunicipio(dadosSQL.getString("codigoTributacaoMunicipio"));
        obj.setAliquotaPIS(dadosSQL.getDouble("aliquotaPIS"));
        obj.setAliquotaCOFINS(dadosSQL.getDouble("aliquotaCOFINS"));
        obj.setAliquotaICMS(dadosSQL.getDouble("aliquotaICMS"));
        obj.setAliquotaISSQN(dadosSQL.getDouble("aliquotaISSQN"));
        obj.setNcmNFCe(dadosSQL.getString("ncmNFCe"));
        obj.setPontos(dadosSQL.getInt("pontos"));
        obj.setSituacaoTributariaICMS(dadosSQL.getString("situacaoTributariaICMS"));
        obj.setSituacaoTributariaISSQN(dadosSQL.getString("situacaoTributariaISSQN"));
        obj.setSituacaoTributariaPIS(dadosSQL.getString("situacaoTributariaPIS"));
        obj.setSituacaoTributariaCOFINS(dadosSQL.getString("situacaoTributariaCOFINS"));
        obj.setUnidadeMedida(dadosSQL.getString("unidadeMedida"));

        obj.setEnviarPercentualImposto(dadosSQL.getBoolean("enviarPercentualImposto"));
        obj.setPercentualFederal(dadosSQL.getDouble("percentualFederal"));
        obj.setPercentualEstadual(dadosSQL.getDouble("percentualEstadual"));
        obj.setPercentualMunicipal(dadosSQL.getDouble("percentualMunicipal"));
        obj.setIsentoPIS(dadosSQL.getBoolean("isentoPIS"));
        obj.setIsentoCOFINS(dadosSQL.getBoolean("isentoCOFINS"));
        obj.setIsentoICMS(dadosSQL.getBoolean("isentoICMS"));
        obj.setApresentarVendasOnline(dadosSQL.getBoolean("apresentarVendasOnline"));
        obj.setMaxDivisao(dadosSQL.getInt("maxDivisao"));
        obj.setModalidadeVendasOnline(dadosSQL.getInt("modalidadevendasonline"));
        obj.setApresentarPactoApp(dadosSQL.getBoolean("apresentarpactoapp"));
        obj.setImagens(dadosSQL.getString("imagens"));

        obj.setEnviaAliquotaNFePIS(dadosSQL.getBoolean("enviaAliquotaNFePIS"));
        obj.setEnviaAliquotaNFeCOFINS(dadosSQL.getBoolean("enviaAliquotaNFeCOFINS"));
        obj.setEnviaAliquotaNFeICMS(dadosSQL.getBoolean("enviaAliquotaNFeICMS"));

        obj.setPrecoCusto(dadosSQL.getDouble("precoCusto"));
        obj.setMargemLucro(dadosSQL.getDouble("margemLucro"));
        obj.setCest(dadosSQL.getString("cest"));
        obj.setCodigoBeneficioFiscal(dadosSQL.getString("codigoBeneficioFiscal"));
        obj.setApresentarPactoFlow(dadosSQL.getBoolean("apresentarPactoFlow"));

        try {
            obj.setDestravarTranca(dadosSQL.getBoolean("destravarTranca"));
        } catch (Exception ignored) {
        }

        try {
            obj.getConfiguracaoNotaFiscalNFSe().setCodigo(dadosSQL.getInt("configuracaonotafiscalNFSe"));
            obj.getConfiguracaoNotaFiscalNFCe().setCodigo(dadosSQL.getInt("configuracaonotafiscalNFCe"));
        } catch (Exception ignored) {
        }

        try {
            obj.setId_externo(dadosSQL.getInt("id_externo"));
        } catch (Exception ignored) {
        }

        try {
            obj.getTamanhoArmario().setCodigo(dadosSQL.getInt("tamanhoarmario"));
        } catch (Exception ignored) {
        }

        try {
            obj.setAparecerAulaCheia(dadosSQL.getBoolean("apareceraulacheia"));
            obj.setPrefixo(dadosSQL.getString("prefixo"));
        } catch (Exception ignored) {
        }

        try {
            obj.setQtdePontos(dadosSQL.getInt("qtdepontos"));
        } catch (Exception ignored) {
        }

        try {
            obj.setQtdConvites(dadosSQL.getInt("qtdConvites"));
        } catch (Exception ignored) {
        }

        try {
            obj.setCodigoProdutoSesi(dadosSQL.getInt("codigoprodutosesi"));
            obj.setNegocioSesi(dadosSQL.getInt("negociosesi"));
            obj.setcRSesi(dadosSQL.getInt("crsesi"));
            obj.setProjetoSesi(dadosSQL.getInt("projetosesi"));
            obj.setContaFinanceiraSesi(dadosSQL.getInt("contafinanceirasesi"));

            obj.setIdProdutoSMD(dadosSQL.getInt("idprodutosmd"));
            obj.setCodigoFormulario(dadosSQL.getInt("codigoformulario"));
            obj.setExibirRelatorioSMD(dadosSQL.getBoolean("exibirrelatoriosmd"));
        } catch (Exception ignored) {
        }

        try {
            obj.setContratoTextoPadrao(dadosSQL.getInt("contratoTextoPadrao"));
        }catch (Exception ignored){

        }

        try {
            obj.setRenovavelAutomaticamente(dadosSQL.getBoolean("renovavelAutomaticamente"));
        } catch (Exception ignored) {
        }
        obj.setDescricaoServicoMunicipio(dadosSQL.getString("descricaoServicoMunicipio"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_COMISSAO) {
            montarDadosComissao(obj, con);
            montarDadosCategoriaProduto(obj, Uteis.NIVELMONTARDADOS_COMISSAO, con);
            return obj;
        }
        montarDadosCategoriaProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        if (!UteisValidacao.emptyString(obj.getTipoProduto())
                && obj.getTipoProduto().equals(CREDITO_PERSONAL.getCodigo())) {
            obj.setPacotesPersonal(consultarPacotesPersonal(obj.getCodigo(), con));
        }
        montarDadosCfgEmpresa(dadosSQL, obj);
        ConfiguracaoProdutoEmpresa cfgDao = new ConfiguracaoProdutoEmpresa(con);
        obj.setConfiguracoesEmpresa(cfgDao.consultarPorProduto(obj.getCodigo()));
        montarDadosComissao(obj, con);
        return obj;
    }

    private static void montarDadosComissao(ProdutoVO obj, Connection con) throws Exception {
        ComissaoProdutoConfiguracao comissaoProdutoConfiguracao = new ComissaoProdutoConfiguracao(con);
        obj.setComissaoProdutos(comissaoProdutoConfiguracao.consultarPorProduto(obj.getCodigo()));
    }
    public static void montarDadosCfgEmpresa(ResultSet rs, ProdutoVO obj) throws Exception {
        try {
            ConfiguracaoProdutoEmpresaVO objCfgEmpresa = new ConfiguracaoProdutoEmpresaVO();
            objCfgEmpresa.setEmpresa(new EmpresaVO());
            objCfgEmpresa.getEmpresa().setCodigo(rs.getInt("cfgempresa"));
            objCfgEmpresa.setValor(rs.getDouble("cfgvalor"));
            obj.setCfgEmpresa(objCfgEmpresa);
            if(!UteisValidacao.emptyNumber(rs.getDouble("cfgvalor")) || UteisValidacao.emptyNumber(obj.getValorFinal())){
                obj.setValorFinal(rs.getDouble("cfgvalor"));
            }
        } catch (Exception e) {
        }
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>CategoriaProdutoVO</code> relacionado ao objeto
     * <code>ProdutoVO</code>. Faz uso da chave primária da classe
     * <code>CategoriaProdutoVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCategoriaProduto(ProdutoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCategoriaProduto().getCodigo().intValue() == 0) {
            obj.setCategoriaProduto(new CategoriaProdutoVO());
            return;
        }
        CategoriaProduto categoriaProduto = new CategoriaProduto(con);
        obj.setCategoriaProduto(categoriaProduto.consultarPorChavePrimaria(obj.getCategoriaProduto().getCodigo(), nivelMontarDados));
        categoriaProduto = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>ProdutoVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public ProdutoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);

        ProdutoVO eCache = (ProdutoVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }

        String sql = "SELECT * FROM Produto WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Produto ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }

        putToCache(eCache);
        return eCache;
    }

    public ProdutoVO criarOuConsultarExisteProdutoPorTipo(String descricao, String tipo, Double valor) throws Exception {
        List lista = consultarPorDescricaoTipoProduto(descricao, tipo, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (lista.isEmpty()) {
            ProdutoVO produto = new ProdutoVO();
            CategoriaProduto categoriaProdutoDAO = new CategoriaProduto(con);
            produto.setCategoriaProduto(categoriaProdutoDAO.criarCategoriaOuConsultarSeExistePorDescricao("SERVIÇOS"));
            categoriaProdutoDAO = null;
            produto.setDescricao(descricao);
            produto.setTipoProduto(tipo);
            produto.setValorFinal(valor);
            incluir(produto);
            return produto;
        } else {
            return (ProdutoVO) lista.get(0);
        }
    }

    public JSONArray obterProdutosGymPass(String gymID) throws Exception {
        String result1 = ExecuteRequestHttpService.executeHttpRequest("https://identity.gympass.com/auth/realms/master/protocol/openid-connect/token",
                "grant_type=client_credentials&client_id=pacto&client_secret=f3677289-46ca-4c0f-a270-3aeb262db4af",
                new HashMap<>(), ExecuteRequestHttpService.METODO_GET,
                Charsets.UTF_8.name());
        String token = new JSONObject(result1).getString("access_token");

        Map<String, String> params = new HashMap<>();
        params.put("Authorization", "Bearer ".concat(token));
        String url = "https://api.partners.gympass.com/setup/v1/gyms/".concat(gymID).concat("/products");
        String result2 = ExecuteRequestHttpService.executeHttpRequest(url, null,
                params, ExecuteRequestHttpService.METODO_GET,
                Charsets.UTF_8.name());
        return new JSONObject(result2).getJSONArray("products");
    }

    @Override
    public void alterarFlagMesclado(Integer codigo, boolean mesclado) throws Exception {
        String desativoSQL =mesclado?", desativado = TRUE ": " , desativado = FALSE ";
        String sql = "update produto set mesclado = ?"+desativoSQL+" where codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setBoolean(1, mesclado);
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }
    }


    /**
     * Responsável por consultar todos os registros de produto de uma categoria
     * de forma simplificada, montando num mapa.
     *
     * <AUTHOR> 11/08/2011
     * @param categoria - categoria de produto a ser listada.
     * @param situacao - "AT" para selecionar apenas os produtos ativos, "IN"
     * para os inativos ou "TD" para todos.
     */
    public Map<Integer, String> consultarProdutosPorCategoriaSimplificado(Integer categoria, final String situacao) throws Exception {
        Map<Integer, String> mapaResult = new HashMap<Integer, String>();
        String desativado = "";
        if (situacao == "AT") {
            desativado = " AND produto.desativado = FALSE ";
        } else if (situacao == "IN") {
            desativado = " AND produto.desativado = TRUE ";
        }
        String sql = "SELECT descricao, codigo FROM produto WHERE categoriaproduto = " + categoria + desativado + "AND tipoproduto NOT IN ('PM' ,'DE' , 'DR')";
        ResultSet consulta = criarConsulta(sql, con);
        while (consulta.next()) {
            mapaResult.put(consulta.getInt("codigo"), consulta.getString("descricao"));
        }
        return mapaResult;
    }

    /**
     * <AUTHOR> 25/08/2011
     * @throws Exception
     */
    public String obterTipoProduto(Integer codigo) throws Exception {
        String sql = "SELECT tipoproduto FROM produto WHERE codigo = " + codigo;
        ResultSet consulta = criarConsulta(sql, con);
        String resultado = "";
        while (consulta.next()) {
            resultado = consulta.getString("tipoproduto");
        }
        return resultado;
    }

    public List<ProdutoVO> obterProdutosDePacotesGenericosDoCliente(int cliente) throws SQLException, Exception {
        String sql = "select distinct(pg.id_produto),produto.descricao  from itemvendaavulsa  item "
                + " inner join vendaavulsa venda on venda.codigo = item.vendaavulsa "
                + " inner join sch_estudio.agenda_agendar ag on venda.codigo = ag.id_vendaavulsa  "
                + " inner join sch_estudio.pacote_produto_generico pg on pg.id_pacote = item.pacote "
                + " inner join produto on produto.codigo = pg.id_produto "
                + " where pacote is not null and venda.cliente =" + cliente;
        ResultSet consulta = criarConsulta(sql, con);
        List<ProdutoVO> listaProdutoVOs = new ArrayList<ProdutoVO>();
        while (consulta.next()) {
            ProdutoVO produto = new ProdutoVO();
            produto.setCodigo(consulta.getInt("id_produto"));
            produto.setDescricao(consulta.getString("descricao"));
            listaProdutoVOs.add(produto);
        }
        return listaProdutoVOs;

    }

    public List<ProdutoVO> obterProdutosDePacotesGenericosDaAgenda(int agenda) throws SQLException, Exception {
        String sql = "select distinct(pg.id_produto),produto.descricao  from sch_estudio.agenda_venda  ag  "
                + " inner join itemvendaavulsa item on ag.id_vendaavulsa = item.vendaavulsa "
                + " inner join sch_estudio.pacote_produto_generico pg on item.pacote = pg.id_pacote  "
                + " inner join produto on produto.codigo = pg.id_produto "
                + " where item.pacote is not null and ag.id_agenda = " + agenda;
        ResultSet consulta = criarConsulta(sql, con);
        List<ProdutoVO> listaProdutoVOs = new ArrayList<ProdutoVO>();
        while (consulta.next()) {
            ProdutoVO produto = new ProdutoVO();
            produto.setCodigo(consulta.getInt("id_produto"));
            produto.setDescricao(consulta.getString("descricao"));
            listaProdutoVOs.add(produto);
        }
        return listaProdutoVOs;

    }

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike,
                                Integer colOrdenar, String dirOrdenar, String situacao) throws Exception {
        StringBuilder sqlCount = new StringBuilder("SELECT count(codigo)+1 FROM produto prod");

        ResultSet rs = getRS(offset,limit, situacao,clausulaLike, colOrdenar,dirOrdenar,false, empresa);

        StringBuilder sqlContarFiltrados = new StringBuilder("SELECT count(prod.codigo)\n");
        sqlContarFiltrados.append("FROM produto prod  \n");
        sqlContarFiltrados.append(" LEFT JOIN categoriaproduto catProd ON catProd.codigo = prod.categoriaproduto \n");
        sqlContarFiltrados.append(" WHERE 1=1 \n");
        if (!situacao.equals("TD")){

            if (situacao.equals("NA")){
                sqlContarFiltrados.append(" and desativado = true \n");
            }else if (situacao.equals("AT")){
                sqlContarFiltrados.append(" and desativado = false \n");
            }
        }
        if (!UteisValidacao.emptyString(clausulaLike)) {
            sqlContarFiltrados.append(" AND (");
            sqlContarFiltrados.append("lower(prod.codigo::VARCHAR) ~ '").append(clausulaLike).append("' \n");
            sqlContarFiltrados.append("OR lower(prod.descricao) ~ '").append(clausulaLike).append("' \n");
            sqlContarFiltrados.append("OR lower(catProd.descricao) ~ '").append(clausulaLike).append("' \n");
            sqlContarFiltrados.append(")");
            if(clausulaLike.toLowerCase().equals("[aà-æ]t[iì-ï]v[oò-ö]") && situacao.equals("AT")){
                sqlContarFiltrados.append(" or desativado = true");
            }else if (clausulaLike.toLowerCase().equals("[iì-ï][nñ][aà-æ]t[iì-ï]v[oò-ö]") && situacao.equals("NA")){
                sqlContarFiltrados.append(" or desativado = false");
            }
        }

        ProdutoVO prod = new ProdutoVO();
        JSONArray lista = new JSONArray();
        while (rs.next()) {
            prod.setTipoProduto(rs.getString("tipoProduto"));
            prod.setUnidadeMedida(rs.getString("unidademedida"));
            prod.setValorFinal(rs.getDouble("valor"));

            JSONArray itemLista = new JSONArray();
            itemLista.put(rs.getString("codigo"));
            itemLista.put(rs.getString("descricao"));
            itemLista.put(rs.getBoolean("situacao") ? "Ativo" : "Inativo");
            itemLista.put(rs.getString("categoria"));
            itemLista.put(prod.getTipoProduto_Apresentar() + "(" + rs.getString("tipoProduto") + ")");
            itemLista.put(getMoeda(empresa) + " " + Formatador.formatarValorMonetarioSemMoeda(prod.getValorFinalConvertidoUnidadeMedida()));
            itemLista.put(rs.getInt("qtdepontos"));
            lista.put(itemLista);
        }
        JSONObject objRetorno = new JSONObject();
        objRetorno.put("iTotalRecords", contar(sqlCount.toString(), getCon()));
        objRetorno.put("iTotalDisplayRecords", contar(sqlContarFiltrados.toString(), getCon()));
        objRetorno.put("sEcho", sEcho);
        objRetorno.put("aaData", lista);
        return objRetorno.toString();
    }

    private String getMoeda(Integer empresa) throws SQLException {
        String moeda = "R$";
        StringBuilder sql = new StringBuilder();
        sql.append("select moeda from empresa where codigo = " + empresa);
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        while (rs.next())
            moeda = rs.getString("moeda");
        return moeda;
    }

    private ResultSet getRS( Integer offset, Integer limit, String situacao,
                             String clausulaLike,Integer colOrdenar, String dirOrdenar,
                             Boolean exportar, Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT prod.codigo, prod.descricao, (NOT desativado) AS situacao, catProd.descricao AS categoria, tipoproduto, qtdepontos, \n");
        sql.append("prod.unidademedida, \n");
        sql.append("COALESCE(prod.cfop, '') AS cfop, \n");
        sql.append("COALESCE(prod.ncmnfce, '') AS ncmnfce, \n");
        sql.append("COALESCE(prod.ncm, '') AS ncm, \n");
        sql.append("COALESCE(prod.cest, '') AS cest, \n");
        sql.append("COALESCE(prod.codigobarras, '') AS codigobarras, \n");
        sql.append("COALESCE(prod.codigolistaservico, '') AS codigolistaservico, \n");
        sql.append("COALESCE(prod.codigotributacaomunicipio, '') AS codigotributacaomunicipio, \n");
        sql.append("COALESCE(prod.descricaoservicomunicipio, '') AS descricaoservicomunicipio, \n");
        sql.append("COALESCE(prod.enviarpercentualimposto, false) AS enviarpercentualimposto, \n");
        sql.append("COALESCE(prod.percentualfederal, 0.0) AS percentualfederal, \n");
        sql.append("COALESCE(prod.percentualestadual, 0.0) AS percentualestadual, \n");
        sql.append("COALESCE(prod.percentualmunicipal, 0.0) AS percentualmunicipal, \n");
        sql.append("COALESCE(prod.situacaotributariaicms, '') AS situacaotributariaicms, \n");
        sql.append("COALESCE(prod.situacaotributariaissqn, '') AS situacaotributariaissqn, \n");
        sql.append("COALESCE(prod.situacaotributariapis, '') AS situacaotributariapis, \n");
        sql.append("COALESCE(prod.situacaotributariacofins, '') AS situacaotributariacofins, \n");
        sql.append("COALESCE(prod.isentocofins, false) AS isentocofins, \n");
        sql.append("COALESCE(prod.isentoicms, false) AS isentoicms, \n");
        sql.append("COALESCE(prod.isentopis, false) AS isentopis, \n");
        sql.append("COALESCE(prod.enviaaliquotanfepis, false) AS enviaaliquotanfepis, \n");
        sql.append("COALESCE(prod.enviaaliquotanfecofins, false) AS enviaaliquotanfecofins, \n");
        sql.append("COALESCE(prod.enviaaliquotanfeicms, false) AS enviaaliquotanfeicms, \n");
        sql.append("COALESCE(prod.aliquotapis, 0.0) AS aliquotapis, \n");
        sql.append("COALESCE(prod.aliquotacofins, 0.0) AS aliquotacofins, \n");
        sql.append("COALESCE(prod.aliquotaicms, 0.0) AS aliquotaicms, \n");
        sql.append("COALESCE(prod.aliquotaissqn, 0.0) AS aliquotaissqn, \n");
        sql.append("COALESCE(prod.codigobeneficiofiscal, '') AS codigobeneficiofiscal, \n");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("case \n");
            sql.append("when exists(select codigo from configuracaoprodutoempresa where produto = prod.codigo and empresa = ").append(empresa).append(") then ");
            sql.append("(select max(valor) from configuracaoprodutoempresa where produto = prod.codigo and empresa = ").append(empresa).append(") \n");
            sql.append("else prod.valorfinal END AS valor \n");
        } else {
            sql.append("valorfinal AS valor \n");
        }

        sql.append("FROM produto prod \n");
        sql.append("LEFT JOIN categoriaproduto catProd ON catProd.codigo = prod.categoriaproduto \n");
        sql.append("WHERE 1=1 \n");
        if (situacao.equals("NA")){
            sql.append(" AND desativado = true \n");
        }else if (situacao.equals("AT")){
            sql.append(" AND desativado = false \n");
        }

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append(" AND (");
            sql.append("lower(prod.codigo::VARCHAR) ~ '").append(clausulaLike).append("' \n");
            sql.append("OR lower(prod.descricao) ~ '").append(clausulaLike).append("' \n");
            sql.append("OR lower(catProd.descricao) ~ '").append(clausulaLike).append("' \n");
            sql.append(")");
            if(clausulaLike.toLowerCase().equals("[aà-æ]t[iì-ï]v[oò-ö]") && situacao.equals("AT")){
                sql.append(" or desativado = true");
            }else if (clausulaLike.toLowerCase().equals("[iì-ï][nñ][aà-æ]t[iì-ï]v[oò-ö]") && situacao.equals("NA")){
                sql.append(" or desativado = false");
            }
        }


        if (!exportar){
            sql.append("  ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
            if (limit > 0) {
                sql.append(" LIMIT ").append(limit).append("\n");
            }
            sql.append(" OFFSET ").append(offset).append("\n");
        }else {
            sql.append("  ORDER BY ").append(colOrdenar + 1);
        }

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        return sqlConsultar.executeQuery();
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int empresa,String situacao) throws SQLException {
        ResultSet rs = getRS(0,0, situacao,"",1,"",true, empresa);
        List lista = new ArrayList();
        while (rs.next()) {
            ProdutoVO produtoVO = new ProdutoVO();
            String geral = rs.getString("codigo") +
                    rs.getString("descricao") +
                    (rs.getBoolean("situacao") ? "ATIVO" : "INATIVO") +
                    rs.getString("categoria") +
                    rs.getString("tipoproduto") +
                    rs.getDouble("valor") +
                    rs.getInt("qtdepontos") +
                    rs.getString("codigobarras") +
                    rs.getString("cfop") +
                    rs.getString("ncm") +
                    rs.getString("cest") +
                    rs.getString("ncmnfce") +
                    rs.getString("codigolistaservico") +
                    rs.getString("codigobeneficiofiscal") +
                    rs.getString("codigotributacaomunicipio") +
                    rs.getString("descricaoservicomunicipio") +
                    (rs.getBoolean("enviarpercentualimposto") ? "ENVIA" : "NÃO ENVIA") +
                    rs.getDouble("percentualfederal") +
                    rs.getDouble("percentualmunicipal") +
                    rs.getDouble("percentualestadual") +
                    rs.getString("situacaotributariaicms") +
                    (rs.getBoolean("isentoicms") ? "ISENTO" : "NÃO ISENTO") +
                    (rs.getBoolean("enviaaliquotanfeicms") ? "ENVIA" : "NÃO ENVIA") +
                    rs.getDouble("aliquotaicms") +
                    rs.getString("situacaotributariapis") +
                    (rs.getBoolean("isentopis") ? "ISENTO" : "NÃO ISENTO") +
                    (rs.getBoolean("enviaaliquotanfepis") ? "ENVIA" : "NÃO ENVIA") +
                    rs.getDouble("aliquotapis") +
                    rs.getString("situacaotributariacofins") +
                    (rs.getBoolean("isentocofins") ? "ISENTO" : "NÃO ISENTO") +
                    (rs.getBoolean("enviaaliquotanfecofins") ? "ENVIA" : "NÃO ENVIA") +
                    rs.getDouble("aliquotacofins") +
                    rs.getString("situacaotributariaissqn") +
                    rs.getDouble("aliquotaissqn");

            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                produtoVO.setCodigo(rs.getInt("codigo"));
                produtoVO.setDesativado(!rs.getBoolean("situacao"));
                produtoVO.setDescricao(rs.getString("descricao"));
                produtoVO.getCategoriaProduto().setDescricao(rs.getString("categoria"));
                produtoVO.setValorFinal(rs.getDouble("valor"));
                produtoVO.setTipoProduto(rs.getString("tipoproduto"));
                produtoVO.setQtdePontos(rs.getInt("qtdepontos"));
                produtoVO.setCodigoBarras(rs.getString("codigobarras"));
                produtoVO.setCfop(rs.getString("cfop"));
                produtoVO.setNcm(rs.getString("ncm"));
                produtoVO.setCest(rs.getString("cest"));
                produtoVO.setNcmNFCe(rs.getString("ncmnfce"));
                produtoVO.setCodigoListaServico(rs.getString("codigolistaservico"));
                produtoVO.setCodigoBeneficioFiscal(rs.getString("codigobeneficiofiscal"));
                produtoVO.setCodigoTributacaoMunicipio(rs.getString("codigotributacaomunicipio"));
                produtoVO.setDescricaoServicoMunicipio(rs.getString("descricaoservicomunicipio"));
                produtoVO.setEnviarPercentualImposto(rs.getBoolean("enviarpercentualimposto"));
                produtoVO.setPercentualFederal(rs.getDouble("percentualfederal"));
                produtoVO.setPercentualMunicipal(rs.getDouble("percentualmunicipal"));
                produtoVO.setPercentualEstadual(rs.getDouble("percentualestadual"));
                produtoVO.setSituacaoTributariaICMS(rs.getString("situacaotributariaicms"));
                produtoVO.setIsentoICMS(rs.getBoolean("isentoicms"));
                produtoVO.setEnviaAliquotaNFeICMS(rs.getBoolean("enviaaliquotanfeicms"));
                produtoVO.setAliquotaICMS(rs.getDouble("aliquotaicms"));
                produtoVO.setSituacaoTributariaPIS(rs.getString("situacaotributariapis"));
                produtoVO.setIsentoPIS(rs.getBoolean("isentopis"));
                produtoVO.setEnviaAliquotaNFePIS(rs.getBoolean("enviaaliquotanfepis"));
                produtoVO.setAliquotaPIS(rs.getDouble("aliquotapis"));
                produtoVO.setSituacaoTributariaCOFINS(rs.getString("situacaotributariacofins"));
                produtoVO.setIsentoCOFINS(rs.getBoolean("isentocofins"));
                produtoVO.setEnviaAliquotaNFeCOFINS(rs.getBoolean("enviaaliquotanfecofins"));
                produtoVO.setAliquotaCOFINS(rs.getDouble("aliquotacofins"));
                produtoVO.setSituacaoTributariaISSQN(rs.getString("situacaotributariaissqn"));
                produtoVO.setAliquotaISSQN(rs.getDouble("aliquotaissqn"));
                lista.add(produtoVO);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Situação")) {
            Ordenacao.ordenarLista(lista, "situacao");
        } else if (campoOrdenacao.equals("Categoria")) {
            Ordenacao.ordenarLista(lista, "categoria_Apresentar");
        } else if (campoOrdenacao.equals("Tipo")) {
            Ordenacao.ordenarLista(lista, "tipoProduto_Apresentar");
        } else if (campoOrdenacao.equals("Valor")) {
            Ordenacao.ordenarLista(lista, "valorFinal");
        }else if (campoOrdenacao.equals("QtdePontos")) {
            Ordenacao.ordenarLista(lista, "qtdePontos");
        }

        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    @Override
    public List<PacotePersonalVO> consultarPacotesPersonal(Integer produto) throws Exception {
        return consultarPacotesPersonal(produto, con);
    }

    public static List<PacotePersonalVO> consultarPacotesPersonal(Integer produto, Connection con) throws Exception {
        List<PacotePersonalVO> lista = new ArrayList<PacotePersonalVO>();
        ResultSet rs = criarConsulta("SELECT * FROM pacotepersonal WHERE produto = " + produto + " ORDER BY quantidade ", con);
        while (rs.next()) {
            lista.add(montarDadosPacotePersonal(rs));
        }
        return lista;
    }

    public void gravarPacotesPersonal(ProdutoVO produto) throws Exception {
        executarConsulta("DELETE FROM pacotepersonal WHERE produto = " + produto.getCodigo(), con);
        String insert = "INSERT INTO pacotepersonal(produto, quantidade, valorpospago, valorprepago) VALUES (?,?,?,?)";
        for (PacotePersonalVO pacote : produto.getPacotesPersonal()) {
            try (PreparedStatement stm = con.prepareStatement(insert)) {
                stm.setInt(1, produto.getCodigo());
                stm.setInt(2, pacote.getQuantidade());
                stm.setDouble(3, pacote.getValorPosPago());
                stm.setDouble(4, pacote.getValorPrePago());
                stm.execute();
            }
        }
    }

    public static PacotePersonalVO montarDadosPacotePersonal(ResultSet rs) throws Exception {
        PacotePersonalVO pacote = new PacotePersonalVO();
        pacote.setProduto(rs.getInt("produto"));
        pacote.setQuantidade(rs.getInt("quantidade"));
        pacote.setValorPosPago(rs.getDouble("valorpospago"));
        pacote.setValorPrePago(rs.getDouble("valorprepago"));
        return pacote;

    }

    @Override
    public List<ProdutoVO> consultarPorTamanhoArmario(Integer tamanho, Integer empresa) throws Exception{
        String sql = "SELECT p.*, cpe.empresa as cfgempresa, cpe.valor as cfgvalor FROM produto p \n";
        sql +=" LEFT JOIN configuracaoprodutoempresa cpe ON cpe.produto = p.codigo \n";
        sql +=" WHERE tipoproduto = '"+ ARMARIO.getCodigo()+"'";
        if(tamanho == null){
            sql += " AND tamanhoarmario IS NULL ";
        }else{
            sql += " AND tamanhoarmario = "+tamanho;
        }
        sql += " AND not desativado \n";
        if(!UteisValidacao.emptyNumber(empresa)){
            sql += " AND (cpe.codigo is null or cpe.empresa = "+empresa+")";
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
    }
    @Override
    public List<ProdutoVO> consultarTodosArmario(Integer empresa) throws Exception{
        String sql = "SELECT p.*, cpe.empresa as cfgempresa, cpe.valor as cfgvalor FROM produto p \n";
        sql +=" LEFT JOIN configuracaoprodutoempresa cpe ON cpe.produto = p.codigo \n";
        sql +=" WHERE tipoproduto = '"+ ARMARIO.getCodigo()+"'";
        sql += " AND not desativado \n";
        if(!UteisValidacao.emptyNumber(empresa)){
            sql += " AND (cpe.codigo is null or cpe.empresa = "+empresa+")";
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
    }

    public ProdutoVO consultarProdutoGenerico(boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto WHERE descricao like 'PRODUTO GENÉRICO' ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new ProdutoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }


    public ProdutoVO consultarPorCodigo(Integer codigoProduto, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from produto where codigo = ").append(codigoProduto);
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }

    public ProdutoVO criarOuConsultarProdutoTotalPass() throws Exception {
        String nomeProduto = "PRODUTO TOTALPASS";
        String tipoProduto = FREEPASS.getCodigo();

        List lista = consultarPorDescricaoTipoProduto(nomeProduto, tipoProduto,false, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (lista.isEmpty()) {
            ProdutoVO produto = new ProdutoVO();
            CategoriaProduto categoriaProdutoDAO = new CategoriaProduto(con);
            produto.setCategoriaProduto(categoriaProdutoDAO.criarCategoriaOuConsultarSeExistePorDescricao("SERVIÇOS"));
            categoriaProdutoDAO = null;
            produto.setDescricao(nomeProduto);
            produto.setTipoProduto(tipoProduto);
            produto.setNrDiasVigencia(1);
            produto.setValorFinal(0.0);
            incluir(produto, true);
            return produto;
        } else {
            return (ProdutoVO) lista.get(0);
        }
    }

    public ProdutoVO criarOuConsultarProdutoGymPass() throws Exception {
        String nomeProduto = "PRODUTO GYMPASS";
        String tipoProduto = FREEPASS.getCodigo();

        List lista = consultarPorDescricaoTipoProduto(nomeProduto, tipoProduto,false, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (lista.isEmpty()) {
            ProdutoVO produto = new ProdutoVO();
            CategoriaProduto categoriaProdutoDAO = new CategoriaProduto(con);
            produto.setCategoriaProduto(categoriaProdutoDAO.criarCategoriaOuConsultarSeExistePorDescricao("SERVIÇOS"));
            categoriaProdutoDAO = null;
            produto.setDescricao(nomeProduto);
            produto.setTipoProduto(tipoProduto);
            produto.setNrDiasVigencia(1);
            produto.setValorFinal(0.0);
            incluir(produto, true);
            return produto;
        } else {
            return (ProdutoVO) lista.get(0);
        }
    }

    public ProdutoVO criarOuConsultarProdutoGoGood() throws Exception {
        String nomeProduto = "PRODUTO GOGOOD";
        String tipoProduto = FREEPASS.getCodigo();

        List lista = consultarPorDescricaoTipoProduto(nomeProduto, tipoProduto,false, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (lista.isEmpty()) {
            ProdutoVO produto = new ProdutoVO();
            CategoriaProduto categoriaProdutoDAO = new CategoriaProduto(con);
            produto.setCategoriaProduto(categoriaProdutoDAO.criarCategoriaOuConsultarSeExistePorDescricao("SERVIÇOS"));
            categoriaProdutoDAO = null;
            produto.setDescricao(nomeProduto);
            produto.setTipoProduto(tipoProduto);
            produto.setNrDiasVigencia(1);
            produto.setValorFinal(0.0);
            incluir(produto, true);
            return produto;
        } else {
            return (ProdutoVO) lista.get(0);
        }
    }

    public JSONArray listaJsonProdutosPorTipo(TipoProduto tipo) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("p.codigo, \n");
        sql.append("p.descricao, \n");
        sql.append("p.valorfinal, \n");
        sql.append("c.valor as valorConfiguracao \n");
        sql.append("FROM produto p \n");
        sql.append("LEFT JOIN configuracaoprodutoempresa c ON c.produto = p.codigo \n");
        sql.append("WHERE NOT p.desativado AND p.tipoproduto = '").append(tipo.getCodigo()).append("' \n");
        sql.append("ORDER BY p.descricao ");
        ResultSet rs = criarConsulta(sql.toString(), con);
        JSONArray array = new JSONArray();
        
        while(rs.next()){
            JSONObject json = new JSONObject();
            json.put("codigo", rs.getInt("codigo"));
            json.put("descricao", rs.getString("descricao"));

            Double valor = rs.getDouble("valorfinal");
            Double valorConfiguracao = rs.getDouble("valorConfiguracao");
            if (!UteisValidacao.emptyNumber(valorConfiguracao)) {
                valor = valorConfiguracao;
            }
            json.put("valor", valor);
            json.put("valorApresentar", Formatador.formatarValorMonetario(valor));
            array.put(json);
        }
        return array;
    }

    public ProdutoVO obterProdutoCfgEmpresa(Integer produto, Integer empresa) throws Exception {
        ProdutoVO produtoVO = new ProdutoVO();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("p.codigo, \n");
        sql.append("p.descricao, \n");
        sql.append("p.valorfinal, \n");
        sql.append("c.valor as valorConfiguracao, \n");
        sql.append("c.empresa \n");
        sql.append("FROM produto p \n");
        sql.append("LEFT JOIN configuracaoprodutoempresa c ON c.produto = p.codigo \n");
        sql.append("WHERE p.codigo = ").append(produto);
        ResultSet rs = criarConsulta(sql.toString(), con);
        while (rs.next()) {
            Double valorConfiguracao = rs.getDouble("valorConfiguracao");
            Double valorfinal = rs.getDouble("valorfinal");
            Integer empresaConf = rs.getInt("empresa");
            Integer codigo = rs.getInt("codigo");
            String descricao = rs.getString("descricao");
            produtoVO.setCodigo(codigo);
            produtoVO.setDescricao(descricao);
            produtoVO.setValorFinal(valorfinal);
            if (empresaConf.equals(empresa)) {
                produtoVO.setValorFinal(valorConfiguracao);
                return produtoVO;
            }
        }
        return produtoVO;
    }


    public List<ProdutoVO> consultarParaAulaCheia(Integer empresa, boolean controlarAcesso, int nivelMontarDados, boolean somenteAtivos) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Produto p";
        sqlStr +=" LEFT JOIN configuracaoprodutoempresa cpe ON cpe.produto = p.codigo \n";
        sqlStr +=" WHERE apareceraulacheia \n";
        if(somenteAtivos){
            sqlStr += " AND desativado = false ";
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sqlStr += " AND (cpe.codigo is null or cpe.empresa = "+empresa+") ";
        }
        sqlStr += " ORDER BY descricao ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public void alterarConfigImpostos(ProdutoVO obj) throws Exception{
        try {
            con.setAutoCommit(false);

            obj.realizarUpperCaseDados();
            String sql = "UPDATE Produto set cfop = ?, codigoListaServico = ?, codigoTributacaoMunicipio = ?, aliquotaPIS = ?, aliquotaCOFINS = ?, aliquotaISSQN = ?, ncmNFCe = ?, cest = ?, codigoBeneficioFiscal = ?"
                    + "WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(1, obj.getCfop());
                sqlAlterar.setString(2, obj.getCodigoListaServico());
                sqlAlterar.setString(3, obj.getCodigoTributacaoMunicipio());
                sqlAlterar.setDouble(4, obj.getAliquotaPIS());
                sqlAlterar.setDouble(5, obj.getAliquotaCOFINS());
                sqlAlterar.setDouble(6, obj.getAliquotaISSQN());
                sqlAlterar.setString(7, obj.getNcmNFCe());
                sqlAlterar.setString(8, obj.getCest());
                sqlAlterar.setString(9, obj.getCodigoBeneficioFiscal());
                sqlAlterar.setInt(10, obj.getCodigo().intValue());

                sqlAlterar.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public ProdutoVO criarOuConsultarProdutoPorTipoNrDiasVigencia(final String tipo, final int nrDiasVigencia, final String descricao, int nivelMontarDados) throws Exception {
        List lista = consultarProdutosPorTipoProdutoNrDiasVigencia(tipo, nrDiasVigencia, true, nivelMontarDados);
        if (lista.isEmpty()) {
            ProdutoVO produto = new ProdutoVO();
            produto.setDescricao(descricao);
            produto.setTipoProduto(tipo);
            produto.setTipoVigencia("ID");
            produto.setNrDiasVigencia(nrDiasVigencia);
            CategoriaProduto categoriaProdutoDAO = new CategoriaProduto(con);
            produto.setCategoriaProduto(categoriaProdutoDAO.criarCategoriaOuConsultarSeExistePorDescricao("SERVIÇOS"));
            incluir(produto, true);
            return produto;
        } else {
            return (ProdutoVO) lista.get(0);
        }
    }

    public ProdutoVO criarOuConsultarProdutoPorTipo(final String tipo, final String descricao, int nivelMontarDados) throws Exception {
        //ideal que apenas produtos especificos do sistema sejam criados aqui. Como produtos de manutenção de modalidade,troca de horario,Devoluções.
        List lista = consultarProdutosPorTipoProduto(tipo, true, nivelMontarDados);
        if (lista.isEmpty()) {
            ProdutoVO produto = new ProdutoVO();
            produto.setDescricao(descricao);
            produto.setTipoProduto(tipo);
            incluir(produto, true);
            return produto;
        } else {
            return (ProdutoVO) lista.get(0);
        }
    }

    public ProdutoVO criarProdutoAulaDiaria(final ProdutoVO produtoVO, boolean apresentarVendasOnline) throws Exception {
        String tipo = DIARIA.getCodigo();

        produtoVO.setTipoProduto(tipo);
        produtoVO.setApresentarVendasOnline(apresentarVendasOnline);
        produtoVO.setMaxDivisao(1);

        CategoriaProdutoVO vo = new CategoriaProdutoVO();
        vo.setCodigo(CATEGORIA_PRODUTO_SERVICOS);
        produtoVO.setCategoriaProduto(vo);

        incluir(produtoVO, true);
        return produtoVO;

    }

    public void atualizarProdutoAulaDiaria(final ProdutoVO produtoVO) throws Exception {
        String tipo = DIARIA.getCodigo();

        produtoVO.setTipoProduto(tipo);
        produtoVO.setApresentarVendasOnline(true);
        produtoVO.setMaxDivisao(1);

        CategoriaProdutoVO vo = new CategoriaProdutoVO();
        vo.setCodigo(CATEGORIA_PRODUTO_SERVICOS);
        produtoVO.setCategoriaProduto(vo);

        alterar(produtoVO, true);
    }


    @Override
    public void consultar(String idEntidade, boolean verificarAcesso) throws Exception {
        super.consultar(idEntidade, verificarAcesso);
    }

    @Override
    public void alterarPontos(Integer codigo, Integer pontos) throws Exception {
        String sql = "update produto set pontos = ? where codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, pontos);
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }
    }

    public ProdutoVO criarOuConsultarExisteProdutoPorDescricao(String descricao, String tipo, Double valor) throws Exception {
        List lista = consultarPorDescricaoTipoProduto(descricao, tipo, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (lista.isEmpty()) {
            ProdutoVO produto = new ProdutoVO();
            CategoriaProduto categoriaProdutoDAO = new CategoriaProduto(con);
            produto.setCategoriaProduto(categoriaProdutoDAO.criarCategoriaOuConsultarSeExistePorDescricao("SERVIÇOS"));
            categoriaProdutoDAO = null;
            produto.setDescricao(descricao);
            produto.setTipoProduto(tipo);
            produto.setValorFinal(valor);
            incluir(produto);
            return produto;
        } else {
            return (ProdutoVO) lista.get(0);
        }
    }

    private ProdutoVO criarProdutoPadrao(String descricao, TipoProduto tipoProduto) throws Exception {
        CategoriaProduto categoriaProdutoDAO;
        try {
            categoriaProdutoDAO = new CategoriaProduto(this.con);
            String sql = "SELECT * FROM produto " +
                    "WHERE UPPER(descricao) = '" + descricao + "' " +
                    "   AND UPPER(tipoproduto) = '" + tipoProduto.getCodigo() + "'" +
                    "LIMIT 1";
            try (Statement st = con.createStatement()) {
                try (ResultSet rs = st.executeQuery(sql)) {
                    if (rs.next()) {
                        return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                    } else {
                        ProdutoVO produto = new ProdutoVO();
                        produto.setDescricao(descricao);
                        produto.setTipoProduto(tipoProduto.getCodigo());
                        produto.setCategoriaProduto(categoriaProdutoDAO.obterCategoriaPadraoServico());
                        produto.setObservacao("Produto padrão para " + descricao + ", criado automaticamente pelo sistema.");
                        incluir(produto);

                        return produto;
                    }
                }
            }
        } finally {
            categoriaProdutoDAO = null;
        }
    }

    public ProdutoVO obterProdutoPadraoProRata() throws Exception {
        return criarProdutoPadrao("PRORATA", SERVICO);
    }

    public ProdutoVO obterProdutoPadraoPersonal() throws Exception {
        return criarProdutoPadrao("TAXA PERSONAL", TAXA_PERSONAL);
    }

    public ProdutoVO obterProdutoPadraoAdesaoPlanoRecorrente() throws Exception {
        return criarProdutoPadrao("ADESÃO PLANO RECORRENTE", TAXA_DE_ADESAO_PLANO_RECORRENCIA);
    }

    public ProdutoVO obterProdutoPadraoAnuidadePlanoRecorrente() throws Exception {
        return criarProdutoPadrao("TAXA DE ANUIDADE PLANO RECORRÊNCIA", TAXA_DE_ANUIDADE_PLANO_RECORRENCIA);
    }

    public void importarProdutos(List<ProdutoImportacaoJSON> listaProdutosJSON, boolean validarProdutoMesmoNome,
                                 String tipoProdutoPadrao, Integer categoriaPadrao, UsuarioVO usuarioVO) throws SQLException {

        for (ProdutoImportacaoJSON produtoJSON : listaProdutosJSON) {
            try {
                con.setAutoCommit(false);

                boolean existeIdExterno = SuperFacadeJDBC.existe("select codigo from produto where id_externo = " + produtoJSON.getIdExterno(),con);
                if (existeIdExterno) {
                    throw new Exception("Já existe um produto cadastrado com o Id_Externo \"" + produtoJSON.getIdExterno() + "\"");
                }

                if (validarProdutoMesmoNome) {
                    ProdutoVO produtoExiste = consultarPorDescricao(produtoJSON.getDescricao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (produtoExiste != null && !UteisValidacao.emptyNumber(produtoExiste.getCodigo())) {
                        throw new Exception("Já existe um produto cadastrado com a descrição \"" + produtoJSON.getDescricao() + "\"");
                    }
                }

                ProdutoVO produtoVO = new ProdutoVO();
                produtoVO.setDescricao(produtoJSON.getDescricao());
                produtoVO.setId_externo(produtoJSON.getIdExterno());
                produtoVO.setValorFinal(produtoJSON.getValor());
                produtoVO.setDesativado(!produtoJSON.isAtivo());
                produtoVO.setCodigoBarras(produtoJSON.getCodigoBarras());

                if (!UteisValidacao.emptyNumber(produtoJSON.getVigenciaNrDias())) {
                    produtoVO.setTipoVigencia("ID");
                    produtoVO.setNrDiasVigencia(produtoJSON.getVigenciaNrDias());
                }

                if (produtoJSON.getVigenciaInicio() != null && produtoJSON.getVigenciaFinal() != null) {
                    produtoVO.setTipoVigencia("PF");
                    produtoVO.setNrDiasVigencia(null);
                    produtoVO.setDataInicioVigencia(produtoJSON.getVigenciaInicio());
                    produtoVO.setDataFinalVigencia(produtoJSON.getVigenciaFinal());
                }
                produtoVO.setBloqueiaPelaVigencia(produtoJSON.isBloquearAposVigencia());
                produtoVO.setObservacao(produtoJSON.getObservacao());

                CategoriaProduto categoriaProdutoDAO = new CategoriaProduto(con);
                CategoriaProdutoVO categoriaVO = new CategoriaProdutoVO();
                if (!UteisValidacao.emptyString(produtoJSON.getCategoria().trim())) {
                    categoriaVO = categoriaProdutoDAO.criarCategoriaOuConsultarSeExistePorDescricao(produtoJSON.getCategoria().trim());
                }

                if (UteisValidacao.emptyNumber(categoriaVO.getCodigo()) && !UteisValidacao.emptyNumber(categoriaPadrao)) {
                    categoriaVO = categoriaProdutoDAO.consultarPorChavePrimaria(categoriaPadrao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
                categoriaProdutoDAO = null;

                if (categoriaVO == null || UteisValidacao.emptyNumber(categoriaVO.getCodigo())) {
                    throw new Exception("Categoria não informada.");
                }
                produtoVO.setCategoriaProduto(categoriaVO);


                TipoProduto tipo = null;
                if (!UteisValidacao.emptyString(produtoJSON.getTipo().trim())) {
                    tipo = retornaPorDescricaoImportacao(produtoJSON.getTipo().trim());
                }

                if (tipo == null && !UteisValidacao.emptyString(tipoProdutoPadrao)) {
                    tipo = getTipoProdutoCodigo(tipoProdutoPadrao);
                }

                if (tipo == null) {
                    throw new Exception("Tipo de Produto não encontrado.");
                }

                produtoVO.setTipoProduto(tipo.getCodigo());

                incluir(produtoVO);

                produtoJSON.setCategoria(produtoVO.getCategoriaProduto().getDescricao());
                produtoJSON.setTipo(produtoVO.getTipoProduto_Apresentar());

//                incluirLogInclusao(produtoVO, usuarioVO);
//                gerarLogImportacaoProduto(produtoVO, usuarioVO);

                if (UteisValidacao.emptyNumber(produtoVO.getCodigo())) {
                    throw new Exception("Produto não importado.");
                }

                produtoJSON.setCodigo(produtoVO.getCodigo());
                produtoJSON.setSucesso(true);
                produtoJSON.setMsgRetorno("Produto importado com sucesso.");
                con.commit();
            } catch (Exception ex) {
                ex.printStackTrace();
                produtoJSON.setCodigo(null);
                produtoJSON.setSucesso(false);
                produtoJSON.setMsgRetorno("Não importado - " + ex.getMessage());
                con.rollback();
            } finally {
                con.setAutoCommit(true);
            }
        }
    }

    public List<Integer> produtoNaoApresentaAppPacto(List<Integer> produtos, Integer empresa) throws Exception {
        if (produtos.isEmpty()) {
            return Collections.emptyList();
        }

        List<Integer> codProdutos = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT p.codigo AS codigo FROM produto p ")
                .append("LEFT JOIN produtoestoque pe ON pe.produto = p.codigo ")
                .append("WHERE p.codigo IN (");

        for (int i = 0; i < produtos.size(); i++) {
            sql.append("?");
            if (i < produtos.size() - 1) {
                sql.append(",");
            }
        }

        sql.append(") AND p.apresentarpactoapp = false");

        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            int parameterIndex = 1;
            for (Integer produto : produtos) {
                ps.setInt(parameterIndex++, produto);
            }

            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    codProdutos.add(rs.getInt("codigo"));
                }
            }
        }
        return codProdutos;
    }


    public List<ProdutoVO> consultarParaVendasOnline(Integer codigo, Integer categoria, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        //Lista produtos que não tem modalidade vinculada
        sql.append("SELECT * FROM ( \n");
        sql.append("    SELECT \n");
        sql.append("    p.*, \n");
        sql.append("    pe.codigo codigoEstoque, \n");
        sql.append("    pe.estoque as estoqueatual \n");
        sql.append("    FROM produto p \n");
        sql.append("    LEFT JOIN ProdutoEstoque pe ON pe.produto = p.codigo \n");
        sql.append("    LEFT JOIN configuracaoprodutoempresa cpe on cpe.produto = p.codigo \n");

        sql.append("    WHERE p.apresentarVendasOnline \n");
        sql.append("    AND p.desativado = false \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("    AND (pe.codigo is null or pe.empresa = ").append(empresa).append(") \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("        AND(cpe.codigo is null or cpe.empresa = ").append(empresa).append(") \n");
        }
        if (!UteisValidacao.emptyNumber(categoria)) {
            sql.append("    AND p.categoriaproduto = ").append(categoria).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(codigo)) {
            sql.append("    AND p.codigo = ").append(codigo).append(" \n");
        }
        sql.append("    AND p.modalidadevendasonline IS NULL \n");
        //Lista produtos que tem modaliade vinculada que não utiliza turma
        sql.append("    UNION \n");
        sql.append("    SELECT \n");
        sql.append("    p.*, \n");
        sql.append("    pe.codigo codigoEstoque, \n");
        sql.append("    pe.estoque as estoqueatual \n");
        sql.append("    FROM produto p \n");
        sql.append("    LEFT JOIN ProdutoEstoque pe ON pe.produto = p.codigo \n");
        sql.append("    INNER JOIN modalidade m ON m.codigo = p.modalidadevendasonline \n");
        sql.append("    LEFT JOIN configuracaoprodutoempresa cpe on cpe.produto = p.codigo \n");
        sql.append("    WHERE p.apresentarVendasOnline \n");
        sql.append("    AND p.desativado = false \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("    AND (pe.codigo is null or pe.empresa = ").append(empresa).append(") \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("        AND(cpe.codigo is null or cpe.empresa = ").append(empresa).append(") \n");
        }
        if (!UteisValidacao.emptyNumber(categoria)) {
            sql.append("    AND p.categoriaproduto = ").append(categoria).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(codigo)) {
            sql.append("    AND p.codigo = ").append(codigo).append(" \n");
        }
        sql.append("    AND p.modalidadevendasonline IS NOT NULL \n");
        sql.append("    AND m.utilizarturma IS false \n");
        sql.append(") AS resultado ORDER BY resultado.descricao ASC; \n");

        VendasConfig vendasConfigDAO = new VendasConfig(con);
        boolean apresentarProdutoSemEstoque = vendasConfigDAO.isApresentarProdutoSemEstoque(empresa);
        vendasConfigDAO = null;

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                List<ProdutoVO> vetResultado = new ArrayList<>();
                while (rs.next()) {
                    Integer estoqueatual = rs.getInt("estoqueatual");
                    Integer codigoEstoque = rs.getInt("codigoEstoque");

                    String posicaoEstoque = "";

                    //não controla estoque
                    if (UteisValidacao.emptyNumber(codigoEstoque)) {
                        posicaoEstoque = "NAO-CONTROLA-ESTOQUE";
                    } else {
                        if (!apresentarProdutoSemEstoque && UteisValidacao.emptyNumber(estoqueatual)) {
                            continue;
                        }
                        posicaoEstoque = estoqueatual.toString();
                    }
                    ProdutoVO obj = montarDados(rs, nivelMontarDados, con);
                    if(obj.getConfiguracoesEmpresa() != null) {
                        for (ConfiguracaoProdutoEmpresaVO liConfiguracaoProdutoEmpresaVO : obj.getConfiguracoesEmpresa()) {
                            if (liConfiguracaoProdutoEmpresaVO.getValor() != obj.getValorFinal() &&  liConfiguracaoProdutoEmpresaVO.getValor() > 0 && liConfiguracaoProdutoEmpresaVO.getEmpresa().getCodigo() == empresa) {
                                obj.setValorFinal(liConfiguracaoProdutoEmpresaVO.getValor());
                            }

                        }
                    }
                    obj.setPosicaoEstoque(posicaoEstoque);
                    vetResultado.add(obj);
                }
                return vetResultado;
            }
        }
    }

    public List<ProdutoVO> consultarParaPactoFlow(Integer codigo, Integer categoria, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        //Lista produtos que não tem modalidade vinculada
        sql.append("SELECT * FROM ( \n");
        sql.append("    SELECT \n");
        sql.append("    p.*, \n");
        sql.append("    pe.codigo codigoEstoque, \n");
        sql.append("    pe.estoque as estoqueatual \n");
        sql.append("    FROM produto p \n");
        sql.append("    LEFT JOIN ProdutoEstoque pe ON pe.produto = p.codigo \n");
        sql.append("    WHERE p.apresentarPactoFlow \n");
        sql.append("    AND p.desativado = false \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("    AND (pe.codigo is null or pe.empresa = ").append(empresa).append(") \n");
        }
        if (!UteisValidacao.emptyNumber(categoria)) {
            sql.append("    AND p.categoriaproduto = ").append(categoria).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(codigo)) {
            sql.append("    AND p.codigo = ").append(codigo).append(" \n");
        }
        sql.append("    AND p.modalidadevendasonline IS NULL \n");
        //Lista produtos que tem modaliade vinculada que não utiliza turma
        sql.append("    UNION \n");
        sql.append("    SELECT \n");
        sql.append("    p.*, \n");
        sql.append("    pe.codigo codigoEstoque, \n");
        sql.append("    pe.estoque as estoqueatual \n");
        sql.append("    FROM produto p \n");
        sql.append("    LEFT JOIN ProdutoEstoque pe ON pe.produto = p.codigo \n");
        sql.append("    INNER JOIN modalidade m ON m.codigo = p.modalidadevendasonline \n");
        sql.append("    WHERE p.apresentarPactoFlow \n");
        sql.append("    AND p.desativado = false \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("    AND (pe.codigo is null or pe.empresa = ").append(empresa).append(") \n");
        }
        if (!UteisValidacao.emptyNumber(categoria)) {
            sql.append("    AND p.categoriaproduto = ").append(categoria).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(codigo)) {
            sql.append("    AND p.codigo = ").append(codigo).append(" \n");
        }
        sql.append("    AND p.modalidadevendasonline IS NOT NULL \n");
        sql.append("    AND m.utilizarturma IS false \n");
        sql.append(") AS resultado ORDER BY resultado.descricao ASC; \n");

        VendasConfig vendasConfigDAO = new VendasConfig(con);
        boolean apresentarProdutoSemEstoque = vendasConfigDAO.isApresentarProdutoSemEstoque(empresa);
        vendasConfigDAO = null;

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                List<ProdutoVO> vetResultado = new ArrayList<>();
                while (rs.next()) {
                    Integer estoqueatual = rs.getInt("estoqueatual");
                    Integer codigoEstoque = rs.getInt("codigoEstoque");

                    String posicaoEstoque = "";

                    //não controla estoque
                    if (UteisValidacao.emptyNumber(codigoEstoque)) {
                        posicaoEstoque = "NAO-CONTROLA-ESTOQUE";
                    } else {
                        if (!apresentarProdutoSemEstoque && UteisValidacao.emptyNumber(estoqueatual)) {
                            continue;
                        }
                        posicaoEstoque = estoqueatual.toString();
                    }
                    ProdutoVO obj = montarDados(rs, nivelMontarDados, con);
                    Boolean produtoTemConfiguracaoParaEmpresaFiltrada = true;
                    obj.setValorFinal(rs.getBigDecimal("valorFinal").setScale(2, RoundingMode.HALF_UP).doubleValue());
                    if(obj.getConfiguracoesEmpresa() != null) {
                        for (ConfiguracaoProdutoEmpresaVO liConfiguracaoProdutoEmpresaVO : obj.getConfiguracoesEmpresa()) {
                            produtoTemConfiguracaoParaEmpresaFiltrada = liConfiguracaoProdutoEmpresaVO.getEmpresa().getCodigo() == empresa;
                            if(produtoTemConfiguracaoParaEmpresaFiltrada) {
                                if (liConfiguracaoProdutoEmpresaVO.getValor() != obj.getValorFinal() &&  liConfiguracaoProdutoEmpresaVO.getValor() > 0) {
                                    obj.setValorFinal(liConfiguracaoProdutoEmpresaVO.getValor());
                                }
                                break;
                            } else {
                                produtoTemConfiguracaoParaEmpresaFiltrada = false;
                            }
                        }
                    }
                    if(produtoTemConfiguracaoParaEmpresaFiltrada) {
                        obj.setPosicaoEstoque(posicaoEstoque);
                        vetResultado.add(obj);
                    }
                }
                return vetResultado;
            }
        }
    }

    public void alterarImagens(List<ProdutoImagemTO> imagensSalvar, ProdutoVO obj, UsuarioVO usuarioVO) throws Exception {
        try {
            con.setAutoCommit(false);

            obj.setImagens(new JSONArray(imagensSalvar).toString());

            ProdutoVO produtoVOAnterior = consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            String sql = "UPDATE Produto set imagens = ? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(1, obj.getImagens());
                sqlAlterar.setInt(2, obj.getCodigo());
                sqlAlterar.execute();
            }

            gravarLog(obj, produtoVOAnterior, usuarioVO);

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void gravarLog(ProdutoVO atual, ProdutoVO produtoVOAnterior, UsuarioVO usuarioVO) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("PRODUTO");
            log.setNomeEntidadeDescricao("PRODUTO");
            log.setDescricao("PRODUTO-IMAGENS");
            log.setChavePrimaria(atual.getCodigo().toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("PRODUTO-IMAGENS");
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setValorCampoAnterior("");

            JSONObject json = new JSONObject();
            json.put("anterior", produtoVOAnterior.getImagens());
            json.put("atual", atual.getImagens());
            log.setValorCampoAlterado(json.toString());

            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public List consultarParaVendaAvulsa(String valorConsulta, String codigoBarrasGrama, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cpe.empresa AS cfgempresa,cpe.valor as cfgvalor, p.* FROM Produto p \n");
        sql.append(" LEFT JOIN configuracaoprodutoempresa cpe ON cpe.produto = p.codigo \n");
        sql.append(" WHERE (upper( p.descricao ) like('").append(valorConsulta.toUpperCase()).append("%') \n");
        sql.append("OR (upper(p.codigobarras) like('").append(valorConsulta.toUpperCase()).append("') and p.unidademedida not in ('").append(UnidadeMedidaEnum.GRAMA.getCodigo()).append("') ) \n");
        if (!UteisValidacao.emptyString(codigoBarrasGrama)) {
            sql.append("OR (upper(p.codigobarras) like('").append(codigoBarrasGrama.toUpperCase()).append("') and p.unidademedida = '").append(UnidadeMedidaEnum.GRAMA.getCodigo()).append("' ) \n");
        }
        sql.append(")\n and ");
        sql.append(adicionarFiltroTipoProduto());
        sql.append("\n and p.descricao <> 'PRODUTO GENÉRICO' and (cpe.empresa = ").append(codigoEmpresa).append(" OR cpe.codigo IS NULL) \n");
        sql.append(" ORDER BY descricao limit 25");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public boolean consultarExistePorCodigoTipoProduto(String tipoProduto) throws Exception {

        String sql = "select exists (SELECT * FROM Produto where tipoproduto = '" + tipoProduto.toUpperCase() + "' and desativado = false) as existe";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean(1);
            }
        }
    }

    public Integer modalidadeProduto(Integer produto) throws Exception{
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select modalidadevendasonline from produto where codigo = " + produto, con);
        return resultSet.next() ? resultSet.getInt("modalidadevendasonline") : 0;
    }

}
