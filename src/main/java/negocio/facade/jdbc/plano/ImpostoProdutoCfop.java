package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.estudio.util.Validador;
import negocio.comuns.plano.ImpostoProdutoCfopVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.ComissaoProdutoConfiguracao;
import negocio.interfaces.plano.ImpostoProdutoCfopInterfaceFacade;

import javax.swing.text.html.Option;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.Optional;

/**
 * Classe de persist�ncia que encapsula todas as opera��es de manipula��o dos
 * dados da classe
 * <code>ProdutoVO</code>. Respons�vel por implementar opera��es como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>ProdutoVO</code>. Encapsula toda a intera��o com o banco de dados.
 *
 * @see ImpostoProdutoCfopVO
 * @see SuperEntidade
 */
public class ImpostoProdutoCfop extends SuperEntidade implements ImpostoProdutoCfopInterfaceFacade {

    public ImpostoProdutoCfop() throws Exception {
        super();
    }

    public ImpostoProdutoCfop(Connection con) throws Exception {
        super(con);
    }

    /**
     * Opera��o respons�vel por retornar um novo objeto da classe
     * <code>ProdutoVO</code>.
     */
    public ImpostoProdutoCfopVO novo() throws Exception {
        incluir(getIdEntidade());
        ImpostoProdutoCfopVO obj = new ImpostoProdutoCfopVO();
        return obj;
    }

    @Override
    public void incluir(ImpostoProdutoCfopVO obj) throws Exception {

    /*    ImpostoProdutoCfopVO.validarDados(obj);
        if (!ignorarPermissao) {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Produto( categoriaProduto, descricao, tipoVigencia, dataInicioVigencia, "
                + "dataFinalVigenciaFixa, nrDiasVigencia, valorFinal, tipoProduto, valorbasecalculo,desativado, capacidade, bloqueiapelavigencia, ncm, "
                + "prevalecerVigenciaContrato, codigobarras, observacao, tamanhoArmario, cfop, codigoListaServico, codigoTributacaoMunicipio, aliquotaPIS, "
                + "aliquotaCOFINS, aliquotaICMS, aliquotaISSQN, ncmNFCe, pontos, apareceraulacheia, prefixo, configuracaonotafiscalNFSe, configuracaonotafiscalNFCe, "
                + "situacaoTributariaICMS, situacaoTributariaISSQN, situacaoTributariaPIS, situacaoTributariaCOFINS, unidadeMedida, "
                + "enviarPercentualImposto, percentualFederal, percentualEstadual, percentualMunicipal, isentoPIS, isentoCOFINS, isentoICMS, id_externo, qtdePontos, "
                + "apresentarVendasOnline, imagens, maxDivisao, enviaAliquotaNFePIS, enviaAliquotaNFeCOFINS, enviaAliquotaNFeICMS, modalidadevendasonline, "
                + "precoCusto, margemLucro, qtdConvites, cest, codigoprodutosesi, negociosesi, codigoBeneficioFiscal,renovavelAutomaticamente, apresentarpactoapp,crsesi,projetosesi, contafinanceirasesi, apresentarPactoFlow, descricaoServicoMunicipio )\n"
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n"
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n"
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? , ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getCategoriaProduto().getCodigo() != 0) {
                sqlInserir.setInt(1, obj.getCategoriaProduto().getCodigo());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setString(2, Uteis.getStringNormalizada(obj.getDescricao().toUpperCase()));
            sqlInserir.setString(3, obj.getTipoVigencia());
            sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
            sqlInserir.setDate(5, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
            sqlInserir.setInt(6, obj.getNrDiasVigencia() == null ? 0 : obj.getNrDiasVigencia());
            sqlInserir.setDouble(7, obj.getValorFinal());
            sqlInserir.setString(8, obj.getTipoProduto());
            sqlInserir.setDouble(9, obj.getValorBaseCalculo());
            sqlInserir.setBoolean(10, obj.getDesativado());
            if (Validador.isValidaInteger(obj.getCapacidade())) {
                sqlInserir.setInt(11, obj.getCapacidade());
            } else {
                sqlInserir.setNull(11, Types.INTEGER);
            }
            sqlInserir.setBoolean(12, obj.getBloqueiaPelaVigencia());
            sqlInserir.setString(13, obj.getNcm());
            sqlInserir.setBoolean(14, obj.isPrevalecerVigenciaContrato());
            if (Validador.isValidaString(obj.getCodigoBarras())) {
                sqlInserir.setString(15, obj.getCodigoBarras());
            } else {
                sqlInserir.setNull(15, Types.VARCHAR);
            }
            sqlInserir.setString(16, obj.getObservacao());

            resolveIntegerNull(sqlInserir, 17, obj.getTamanhoArmario().getCodigo());

            sqlInserir.setString(18, obj.getCfop());
            sqlInserir.setString(19, obj.getCodigoListaServico());
            sqlInserir.setString(20, obj.getCodigoTributacaoMunicipio());
            sqlInserir.setDouble(21, obj.getAliquotaPIS());
            sqlInserir.setDouble(22, obj.getAliquotaCOFINS());
            sqlInserir.setDouble(23, obj.getAliquotaICMS());
            sqlInserir.setDouble(24, obj.getAliquotaISSQN());
            sqlInserir.setString(25, obj.getNcmNFCe());
            sqlInserir.setInt(26, obj.getPontos());
            sqlInserir.setBoolean(27, obj.isAparecerAulaCheia());
            sqlInserir.setString(28, obj.getPrefixo());
            resolveIntegerNull(sqlInserir, 29, obj.getConfiguracaoNotaFiscalNFSe().getCodigo());
            resolveIntegerNull(sqlInserir, 30, obj.getConfiguracaoNotaFiscalNFCe().getCodigo());
            sqlInserir.setString(31, obj.getSituacaoTributariaICMS());
            sqlInserir.setString(32, obj.getSituacaoTributariaISSQN());
            sqlInserir.setString(33, obj.getSituacaoTributariaPIS());
            sqlInserir.setString(34, obj.getSituacaoTributariaCOFINS());
            sqlInserir.setString(35, obj.getUnidadeMedida());
            sqlInserir.setBoolean(36, obj.isEnviarPercentualImposto());
            sqlInserir.setDouble(37, obj.getPercentualFederal());
            sqlInserir.setDouble(38, obj.getPercentualEstadual());
            sqlInserir.setDouble(39, obj.getPercentualMunicipal());
            sqlInserir.setBoolean(40, obj.isIsentoPIS());
            sqlInserir.setBoolean(41, obj.isIsentoCOFINS());
            sqlInserir.setBoolean(42, obj.isIsentoICMS());
            resolveIntegerNull(sqlInserir, 43, obj.getId_externo());
            resolveIntegerNull(sqlInserir, 44, obj.getQtdePontos());
            sqlInserir.setBoolean(45, obj.isApresentarVendasOnline());
            sqlInserir.setString(46, obj.getImagens());
            sqlInserir.setInt(47, obj.getMaxDivisao());
            sqlInserir.setBoolean(48, obj.isEnviaAliquotaNFePIS());
            sqlInserir.setBoolean(49, obj.isEnviaAliquotaNFeCOFINS());
            sqlInserir.setBoolean(50, obj.isEnviaAliquotaNFeICMS());
            resolveIntegerNull(sqlInserir, 51, obj.getModalidadeVendasOnline());
            sqlInserir.setDouble(52, obj.getPrecoCusto());
            sqlInserir.setDouble(53, obj.getMargemLucro());
            sqlInserir.setInt(54, obj.getQtdConvites());
            sqlInserir.setString(55, obj.getCest());
            resolveIntegerNull(sqlInserir, 56, obj.getCodigoProdutoSesi());
            resolveIntegerNull(sqlInserir, 57, obj.getNegocioSesi());
            sqlInserir.setString(58, obj.getCodigoBeneficioFiscal());
            sqlInserir.setBoolean(59, obj.getRenovavelAutomaticamente());
            sqlInserir.setBoolean(60, obj.isApresentarPactoApp());
            resolveIntegerNull(sqlInserir, 61, obj.getcRSesi());
            resolveIntegerNull(sqlInserir, 62, obj.getProjetoSesi());
            resolveIntegerNull(sqlInserir, 63, obj.getContaFinanceiraSesi());
            sqlInserir.setBoolean(64, obj.isApresentarPactoFlow());
            sqlInserir.setString(65, obj.getDescricaoServicoMunicipio());
            sqlInserir.execute();
        }*/
        obj.setCodigo(obterValorChavePrimariaCodigo());

    }

    @Override
    public void alterar(ImpostoProdutoCfopVO obj) throws Exception {

    }

    @Override
    public void excluir(ImpostoProdutoCfopVO obj) throws Exception {

    }

    @Override
    public ImpostoProdutoCfopVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        return null;
    }

    @Override
    public Optional<ImpostoProdutoCfopVO> consultarPorCfopENcm(String cfop, String ncm) throws Exception {
        String sql = "SELECT * FROM impostoprodutocfop " +
                "WHERE (cfop = ? AND ncm = ?) " +
                "OR (cfop = ? AND (ncm IS NULL OR ncm = '')) " +
                "ORDER BY CASE WHEN ncm = ? THEN 1 ELSE 2 END " +
                "LIMIT 1";

        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setString(1, cfop);
            pst.setString(2, ncm);
            pst.setString(3, cfop);
            pst.setString(4, ncm);

            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(montarDados(rs));
                }
            }
        }
        return Optional.empty();
    }

    public static ImpostoProdutoCfopVO montarDados(ResultSet dadosSql) throws Exception {
        ImpostoProdutoCfopVO impostoProdutoCfopVO = new ImpostoProdutoCfopVO();

        impostoProdutoCfopVO.setCodigo(dadosSql.getInt("codigo"));
        impostoProdutoCfopVO.setCfop(dadosSql.getString("cfop"));
        impostoProdutoCfopVO.setNcm(dadosSql.getString("ncm"));

        impostoProdutoCfopVO.setEmpresa(getFacade().getEmpresa().consultarPorCodigo(dadosSql.getInt("empresa"), Uteis.NIVELMONTARDADOS_MINIMOS));

        impostoProdutoCfopVO.setConfiguracaoNotaFiscalNFSe(getFacade().getConfiguracaoNotaFiscal().consultarPorChavePrimaria(dadosSql.getInt("configuracaonotafiscalnfse"), Uteis.NIVELMONTARDADOS_MINIMOS));
        impostoProdutoCfopVO.setConfiguracaoNotaFiscalNFCe(getFacade().getConfiguracaoNotaFiscal().consultarPorChavePrimaria(dadosSql.getInt("configuracaonotafiscalnfce"), Uteis.NIVELMONTARDADOS_MINIMOS));

        impostoProdutoCfopVO.setCodigoListaServico(dadosSql.getString("codigolistaservico"));
        impostoProdutoCfopVO.setCodigoTributacaoMunicipio(dadosSql.getString("codigotributacaomunicipio"));

        impostoProdutoCfopVO.setDescricaoServicoMunicipio(dadosSql.getString("descricaoservicomunicipio"));
        impostoProdutoCfopVO.setEnviarPercentualImposto(dadosSql.getBoolean("enviarpercentualimposto"));

        impostoProdutoCfopVO.setPercentualFederal(dadosSql.getDouble("percentualfederal"));
        impostoProdutoCfopVO.setPercentualEstadual(dadosSql.getDouble("percentualestadual"));
        impostoProdutoCfopVO.setPercentualMunicipal(dadosSql.getDouble("percentualmunicipal"));

        impostoProdutoCfopVO.setSituacaoTributariaISSQN(dadosSql.getString("situacaotributariaissqn"));
        impostoProdutoCfopVO.setAliquotaISSQN(dadosSql.getDouble("aliquotaissqn"));

        impostoProdutoCfopVO.setSituacaoTributariaPIS(dadosSql.getString("situacaotributariapis"));
        impostoProdutoCfopVO.setAliquotaPIS(dadosSql.getDouble("aliquotapis"));
        impostoProdutoCfopVO.setIsentoPIS(dadosSql.getBoolean("isentopis"));
        impostoProdutoCfopVO.setEnviaAliquotaNFePIS(dadosSql.getBoolean("enviaaliquotanfepis"));

        impostoProdutoCfopVO.setSituacaoTributariaCOFINS(dadosSql.getString("situacaotributariacofins"));
        impostoProdutoCfopVO.setAliquotaCOFINS(dadosSql.getDouble("aliquotacofins"));
        impostoProdutoCfopVO.setIsentoCOFINS(dadosSql.getBoolean("isentocofins"));
        impostoProdutoCfopVO.setEnviaAliquotaNFeCOFINS(dadosSql.getBoolean("enviaaliquotanfecofins"));

        impostoProdutoCfopVO.setIsentoICMS(dadosSql.getBoolean("isentoicms"));
        impostoProdutoCfopVO.setSituacaoTributariaICMS(dadosSql.getString("situacaotributariaicms"));
        impostoProdutoCfopVO.setAliquotaICMS(dadosSql.getDouble("aliquotaicms"));
        impostoProdutoCfopVO.setEnviaAliquotaNFeICMS(dadosSql.getBoolean("enviaaliquotanfeicms"));

        impostoProdutoCfopVO.setDesativado(dadosSql.getBoolean("desativado"));

        return impostoProdutoCfopVO;
    }


    /**
     * Opera��o respons�vel por alterar no BD os dados de um objeto da classe
     * <code>ProdutoVO</code>. Sempre utiliza a chave prim�ria da classe como
     * atributo para localiza��o do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conex�o com o banco de
     * dados e a permiss�o do usu�rio para realizar esta operac�o na entidade.
     * Isto, atrav�s da opera��o
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ProdutoVO</code> que ser� alterada no
     * banco de dados.
     * @exception Exception Caso haja problemas de conex�o, restri��o de acesso
     * ou valida��o de dados.
     */
    public void alterar(ProdutoVO obj, boolean centralEventos) throws Exception {
        ConfiguracaoProdutoEmpresa configuracaoProdutoEmpresaDAO;
        ComissaoProdutoConfiguracao comissaoProdutoConfiguracaoDAO;
        try {
            con.setAutoCommit(false);
            configuracaoProdutoEmpresaDAO = new ConfiguracaoProdutoEmpresa(this.con);
            comissaoProdutoConfiguracaoDAO = new ComissaoProdutoConfiguracao(this.con);

            ProdutoVO.validarDados(obj);
            if (!centralEventos) {
                alterar(getIdEntidade());
            }

            obj.realizarUpperCaseDados();
            String sql = "UPDATE Produto set categoriaProduto=?, descricao=?, tipoVigencia=?, dataInicioVigencia=?, dataFinalVigenciaFixa=?, nrDiasVigencia=?,  valorFinal=?, tipoProduto=?, valorbasecalculo=?,\n"
                    + "desativado=?, capacidade = ?, bloqueiapelavigencia = ?, ncm=?, prevalecerVigenciaContrato = ?, codigoBarras = ?, observacao = ?, tamanhoArmario = ?,\n"
                    + "cfop = ?, codigoListaServico = ?, codigoTributacaoMunicipio = ?, aliquotaPIS = ?, aliquotaCOFINS = ?, aliquotaICMS = ?, aliquotaISSQN = ?,\n"
                    + "ncmNFCe = ?, pontos = ?, apareceraulacheia = ?, prefixo = ?, configuracaonotafiscalNFSe = ?, configuracaonotafiscalNFCe = ?,\n"
                    + "situacaoTributariaICMS = ?, situacaoTributariaISSQN = ?, situacaoTributariaPIS = ?, situacaoTributariaCOFINS = ?, unidadeMedida = ?,\n"
                    + "enviarPercentualImposto = ?, percentualFederal = ?, percentualEstadual = ?, percentualMunicipal = ?, isentoPIS = ?, isentoCOFINS = ?,\n"
                    + "isentoICMS = ?, id_externo = ?,qtdePontos = ?, apresentarVendasOnline = ?, imagens = ?, maxDivisao = ?,\n"
                    + "enviaAliquotaNFePIS = ?, enviaAliquotaNFeCOFINS = ?, enviaAliquotaNFeICMS = ?, modalidadevendasonline = ?, precoCusto = ?, margemLucro = ?,\n"
                    + "qtdConvites = ?, cest = ?, codigoprodutosesi = ?, negociosesi = ?, codigoBeneficioFiscal = ?, renovavelAutomaticamente = ?, apresentarpactoapp = ?, \n"
                    +  " crsesi = ?, projetosesi = ? , contafinanceirasesi = ?, apresentarPactoFlow = ? , descricaoServicoMunicipio = ?, destravarTranca = ? "
                    + "WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                if (obj.getCategoriaProduto().getCodigo() != 0) {
                    sqlAlterar.setInt(1, obj.getCategoriaProduto().getCodigo());
                } else {
                    sqlAlterar.setNull(1, 0);
                }
                sqlAlterar.setString(2, Uteis.getStringNormalizada(obj.getDescricao().toUpperCase()));
                sqlAlterar.setString(3, obj.getTipoVigencia());
                sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataInicioVigencia()));
                sqlAlterar.setDate(5, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
                sqlAlterar.setInt(6, obj.getNrDiasVigencia());
                sqlAlterar.setDouble(7, obj.getValorFinal());
                sqlAlterar.setString(8, obj.getTipoProduto());
                sqlAlterar.setDouble(9, obj.getValorBaseCalculo());
                sqlAlterar.setBoolean(10, obj.getDesativado());

                if (Validador.isValidaInteger(obj.getCapacidade())) {
                    sqlAlterar.setInt(11, obj.getCapacidade());
                } else {
                    sqlAlterar.setNull(11, Types.INTEGER);
                }

                sqlAlterar.setBoolean(12, obj.getBloqueiaPelaVigencia());
                sqlAlterar.setString(13, obj.getNcm());
                sqlAlterar.setBoolean(14, obj.isPrevalecerVigenciaContrato());
                if (Validador.isValidaString(obj.getCodigoBarras())) {
                    sqlAlterar.setString(15, obj.getCodigoBarras());
                } else {
                    sqlAlterar.setNull(15, 0);
                }

                sqlAlterar.setString(16, obj.getObservacao());
                resolveIntegerNull(sqlAlterar, 17, obj.getTamanhoArmario().getCodigo());

                sqlAlterar.setString(18, obj.getCfop());
                sqlAlterar.setString(19, obj.getCodigoListaServico());
                sqlAlterar.setString(20, obj.getCodigoTributacaoMunicipio());
                sqlAlterar.setDouble(21, obj.getAliquotaPIS());
                sqlAlterar.setDouble(22, obj.getAliquotaCOFINS());
                sqlAlterar.setDouble(23, obj.getAliquotaICMS());
                sqlAlterar.setDouble(24, obj.getAliquotaISSQN());
                sqlAlterar.setString(25, obj.getNcmNFCe());
                sqlAlterar.setInt(26, obj.getPontos());
                sqlAlterar.setBoolean(27, obj.isAparecerAulaCheia());
                sqlAlterar.setString(28, obj.getPrefixo());
                resolveIntegerNull(sqlAlterar, 29, obj.getConfiguracaoNotaFiscalNFSe().getCodigo());
                resolveIntegerNull(sqlAlterar, 30, obj.getConfiguracaoNotaFiscalNFCe().getCodigo());
                sqlAlterar.setString(31, obj.getSituacaoTributariaICMS());
                sqlAlterar.setString(32, obj.getSituacaoTributariaISSQN());
                sqlAlterar.setString(33, obj.getSituacaoTributariaPIS());
                sqlAlterar.setString(34, obj.getSituacaoTributariaCOFINS());
                sqlAlterar.setString(35, obj.getUnidadeMedida());
                sqlAlterar.setBoolean(36, obj.isEnviarPercentualImposto());
                sqlAlterar.setDouble(37, obj.getPercentualFederal());
                sqlAlterar.setDouble(38, obj.getPercentualEstadual());
                sqlAlterar.setDouble(39, obj.getPercentualMunicipal());
                sqlAlterar.setBoolean(40, obj.isIsentoPIS());
                sqlAlterar.setBoolean(41, obj.isIsentoCOFINS());
                sqlAlterar.setBoolean(42, obj.isIsentoICMS());
                resolveIntegerNull(sqlAlterar, 43, obj.getId_externo());
                resolveIntegerNull(sqlAlterar, 44, obj.getQtdePontos());
                sqlAlterar.setBoolean(45, obj.isApresentarVendasOnline());
                sqlAlterar.setString(46, obj.getImagens());
                sqlAlterar.setInt(47, obj.getMaxDivisao());
                sqlAlterar.setBoolean(48, obj.isEnviaAliquotaNFePIS());
                sqlAlterar.setBoolean(49, obj.isEnviaAliquotaNFeCOFINS());
                sqlAlterar.setBoolean(50, obj.isEnviaAliquotaNFeICMS());
                resolveIntegerNull(sqlAlterar, 51, obj.getModalidadeVendasOnline());
                sqlAlterar.setDouble(52, obj.getPrecoCusto());
                sqlAlterar.setDouble(53, obj.getMargemLucro());
                sqlAlterar.setInt(54, obj.getQtdConvites());
                sqlAlterar.setString(55, obj.getCest());
                resolveIntegerNull(sqlAlterar, 56, obj.getCodigoProdutoSesi());
                resolveIntegerNull(sqlAlterar, 57, obj.getNegocioSesi());
                sqlAlterar.setString(58, obj.getCodigoBeneficioFiscal());
                sqlAlterar.setBoolean(59, obj.getRenovavelAutomaticamente());
                sqlAlterar.setBoolean(60, obj.isApresentarPactoApp());
                resolveIntegerNull(sqlAlterar, 61, obj.getcRSesi());
                resolveIntegerNull(sqlAlterar, 62, obj.getProjetoSesi());
                resolveIntegerNull(sqlAlterar, 63, obj.getContaFinanceiraSesi());
                sqlAlterar.setBoolean(64, obj.isApresentarPactoFlow());
                sqlAlterar.setString(65, obj.getDescricaoServicoMunicipio());
                sqlAlterar.setBoolean(66, obj.isDestravarTranca());
                sqlAlterar.setInt(67, obj.getCodigo());
                sqlAlterar.execute();
            }

            //gravarPacotesPersonal(obj);
            configuracaoProdutoEmpresaDAO.alterarPorProduto(obj);
            comissaoProdutoConfiguracaoDAO.atualizarPorProduto(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            configuracaoProdutoEmpresaDAO = null;
            comissaoProdutoConfiguracaoDAO = null;
        }
    }

    /**
     * Respons�vel por alterar no BD os dados de um objeto da classe
     * <code>ProdutoVO</code> no ZW.
     *
     * <AUTHOR> 22/03/2011
     * @param obj
     * @throws Exception
     */
    public void alterar(ProdutoVO obj) throws Exception {
        this.alterar(obj, false);
    }

    /**
     * Opera��o respons�vel por excluir no BD um objeto da classe
     * <code>ProdutoVO</code>. Sempre localiza o registro a ser exclu�do atrav�s
     * da chave prim�ria da entidade. Primeiramente verifica a conex�o com o
     * banco de dados e a permiss�o do usu�rio para realizar esta operac�o na
     * entidade. Isto, atrav�s da opera��o
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ProdutoVO</code> que ser� removido no
     * banco de dados.
     * @exception Exception Caso haja problemas de conex�o ou restri��o de
     * acesso.
     */
    public void excluir(ProdutoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (!centralEventos) {
                excluir(getIdEntidade());
            }
            //validarValidacaoAcesso(obj.getCodigo().intValue());
            String sql = "DELETE FROM Produto WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public ProdutoVO consultarPorDescricao(String descricao, int nivelMontarDados)throws Exception{
        String sql = "select * from produto where upper(descricao) = ?";
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setString(1, descricao.toUpperCase());
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    //return montarDados(rs, nivelMontarDados, con);
                    return null;
                }
            }
        }
        return null;

    }


}
