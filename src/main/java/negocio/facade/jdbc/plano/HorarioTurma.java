package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import controle.basico.VinculoControle;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HorarioTurmaConcatenadoVO;
import negocio.comuns.plano.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.AutorizacaoAcessoGrupoEmpresarial;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Presenca;
import negocio.facade.jdbc.basico.Reposicao;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.contrato.MatriculaAlunoHorarioTurmaInterfaceFacade;
import negocio.interfaces.plano.HorarioTurmaInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>HorarioTurmaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>HorarioTurmaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see HorarioTurmaVO
 * @see SuperEntidade
 * @see Turma
 */
public class HorarioTurma extends SuperEntidade implements HorarioTurmaInterfaceFacade {

    private Date data = null;
    private Date dataInicioPeriodo = null;

    private Integer pessoaOperacao;

    public HorarioTurma() throws Exception {
        super();
        setIdEntidade("Turma");
    }

    public HorarioTurma(Connection con) throws Exception {
        super(con);
        setIdEntidade("Turma");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>HorarioTurmaVO</code>.
     */
    @Override
    public HorarioTurmaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new HorarioTurmaVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>HorarioTurmaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>HorarioTurmaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    @Override
    public void incluir(HorarioTurmaVO obj) throws Exception {
        HorarioTurmaVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO HorarioTurma( turma, horaInicial, horaFinal, "
                + "professor, ambiente, nivelTurma, situacao, nrMaximoAluno, "
                + "diaSemana, diaSemanaNumero, "
                + "identificadorTurma, toleranciaEntradaMinutos, toleranciaEntradaAposMinutos, liberadoMarcacaoApp , ativo, dataEntrouTurma, dataSaiuTurma, "
                + "limiteVagasAgregados) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i=1;
            if (obj.getTurma() != 0) {
                sqlInserir.setInt(i++, obj.getTurma());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setString(i++, obj.getHoraInicial());
            sqlInserir.setString(i++, obj.getHoraFinal());
            if (obj.getProfessor().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getProfessor().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getAmbiente().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getAmbiente().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getNivelTurma().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getNivelTurma().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setString(i++, obj.getSituacao());
            sqlInserir.setInt(i++, obj.getNrMaximoAluno());
            sqlInserir.setString(i++, obj.getDiaSemana());
            sqlInserir.setInt(i++, obj.getDiaSemanaNumero());
            sqlInserir.setString(i++, obj.getIdentificadorTurma());
            sqlInserir.setInt(i++, obj.getToleranciaEntradaMinutos());
            sqlInserir.setInt(i++, obj.getToleranciaEntradaAposMinutos());
            sqlInserir.setBoolean(i++, obj.isLiberadoMarcacaoApp());
            sqlInserir.setBoolean(i++, obj.isAtivo());
            if (obj.getDataEntrouTurma() != null) {
                sqlInserir.setTimestamp(i++, new java.sql.Timestamp(obj.getDataEntrouTurma().getTime()));
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getDataSaiuTurma() != null) {
                sqlInserir.setTimestamp(i++, new java.sql.Timestamp(obj.getDataSaiuTurma().getTime()));
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setInt(i++, obj.getLimiteVagasAgregados() != null ? obj.getLimiteVagasAgregados() : 0);
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>HorarioTurmaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>HorarioTurmaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    @Override
    public void alterar(HorarioTurmaVO obj) throws Exception {
        HorarioTurmaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE HorarioTurma SET turma=?, horaInicial=?, horaFinal=?, "
                + "professor=?, ambiente=?, nivelTurma=?, situacao=?, nrMaximoAluno=?, "
                + "diaSemana=?, diaSemanaNumero=?, "
                + "identificadorTurma=?, toleranciaEntradaMinutos=?, toleranciaEntradaAposMinutos=?, liberadoMarcacaoApp=?,ativo=?, spivieventid=?, "
                + "dataEntrouTurma=?, dataSaiuTurma=?, limiteVagasAgregados=? "
                + "WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i=1;
            if (obj.getTurma() != 0) {
                sqlAlterar.setInt(i++, obj.getTurma());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setString(i++, obj.getHoraInicial());
            sqlAlterar.setString(i++, obj.getHoraFinal());
            if (obj.getProfessor().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getProfessor().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getAmbiente().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getAmbiente().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getNivelTurma().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getNivelTurma().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setString(i++, obj.getSituacao());
            sqlAlterar.setInt(i++, obj.getNrMaximoAluno());
            sqlAlterar.setString(i++, obj.getDiaSemana());
            sqlAlterar.setInt(i++, obj.getDiaSemanaNumero());
            sqlAlterar.setString(i++, obj.getIdentificadorTurma());
            sqlAlterar.setInt(i++, obj.getToleranciaEntradaMinutos());
            sqlAlterar.setInt(i++, obj.getToleranciaEntradaAposMinutos());
            sqlAlterar.setBoolean(i++, obj.isLiberadoMarcacaoApp());
            sqlAlterar.setBoolean(i++, obj.isAtivo());
            Uteis.setInt(sqlAlterar, obj.getSpiviEventID(), i++);
            if (obj.getDataEntrouTurma() != null) {
                sqlAlterar.setTimestamp(i++, new java.sql.Timestamp(obj.getDataEntrouTurma().getTime()));
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getDataSaiuTurma() != null) {
                sqlAlterar.setTimestamp(i++, new java.sql.Timestamp(obj.getDataSaiuTurma().getTime()));
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setInt(i++, obj.getLimiteVagasAgregados() != null ? obj.getLimiteVagasAgregados() : 0);
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
        }
        try {
            try (ResultSet rsValidar = criarConsulta("SELECT aulacoletiva FROM turma WHERE codigo = " + obj.getTurma(), con)) {
                rsValidar.next();
                if (!rsValidar.getBoolean("aulacoletiva")) {
                    VinculoControle.processarVinculosHorario(obj);
                }
            }
        } catch (Exception e) {
            VinculoControle.processarVinculosHorario(obj);
        }
        
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>HorarioTurmaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>HorarioTurmaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public void excluir(HorarioTurmaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM HorarioTurma WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    @Override
    public void desativar(HorarioTurmaVO obj) throws Exception {
        String sql = "UPDATE HorarioTurma SET situacao=?, dataSaiuTurma=? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1,"IN");
            if (obj.getDataSaiuTurma() != null) {
                sqlAlterar.setTimestamp(2, new java.sql.Timestamp(obj.getDataSaiuTurma().getTime()));
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public List<HorarioTurmaVO> consultarTodos(int nivelMontarDados) throws Exception{
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery("select * from HorarioTurma")) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    @Override
    public List<HorarioTurmaVO> consultarGestaoTurma(boolean turmaAtiva, boolean turmaNaoAtiva, int codigoEmpresa, int codigoTurma, int codigoProfessor, int codigoModalidade, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder("select * from HorarioTurma ");
        sql.append(" inner join turma on turma.codigo = horarioturma.turma ");

        if(codigoEmpresa != 0){
            sql.append(" where empresa = "+codigoEmpresa+" ");

            if(codigoTurma != 0){
            sql.append(" and turma = "+codigoTurma+" ");
            }

            if(codigoProfessor != 0){
                sql.append(" and horarioturma.professor = "+codigoProfessor+" ");
            }

            if(codigoModalidade != 0){
                sql.append(" and turma.modalidade = "+codigoModalidade+" ");
            }

            if(turmaAtiva){
                sql.append(" and turma.dataFinalVigencia >= '"+Uteis.getDataJDBC(Calendario.hoje())+"' ");
            }else if(turmaNaoAtiva){
                sql.append(" and turma.dataFinalVigencia < '"+Uteis.getDataJDBC(Calendario.hoje())+"' ");
            }

            sql.append(" and turma.aulacoletiva is false");

        }


        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioTurma</code> através do valor do atributo 
     * <code>String situacao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List consultarPorSituacao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        data = null;
        String sqlStr = "SELECT * FROM HorarioTurma WHERE upper( situacao ) LIKE('" + valorConsulta.toUpperCase() + "%') ORDER BY situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioTurma</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>NivelTurma</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List consultarPorDescricaoNivelTurma(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        data = null;
        String sqlStr = "SELECT HorarioTurma.* FROM HorarioTurma, NivelTurma WHERE HorarioTurma.nivelTurma = NivelTurma.codigo AND upper( NivelTurma.descricao ) LIKE('" + valorConsulta.toUpperCase() + "%') ORDER BY NivelTurma.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioTurma</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Ambiente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List consultarPorDescricaoAmbiente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        data = null;
        String sqlStr = "SELECT HorarioTurma.* FROM HorarioTurma, Ambiente WHERE HorarioTurma.ambiente = Ambiente.codigo AND upper( Ambiente.descricao ) LIKE('" + valorConsulta.toUpperCase() + "%') ORDER BY Ambiente.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioTurma</code> através do valor do atributo 
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        data = null;
        String sqlStr = "SELECT HorarioTurma.* FROM HorarioTurma, Colaborador WHERE HorarioTurma.professor = Colaborador.codigo AND upper( Colaborador.tipoColaborador ) LIKE('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioTurma</code> através do valor do atributo 
     * <code>String horaInicial</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List consultarPorHoraInicial(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        data = null;
        String sqlStr = "SELECT * FROM HorarioTurma WHERE upper( horaInicial ) LIKE('" + valorConsulta.toUpperCase() + "%') ORDER BY horaInicial";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioTurma</code> através do valor do atributo 
     * <code>String identificador</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List consultarPorIdentificador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        data = null;
        String sqlStr = "SELECT * FROM HorarioTurma WHERE upper( identificador ) LIKE('" + valorConsulta.toUpperCase() + "%') ORDER BY identificador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioTurma</code> através do valor do atributo 
     * <code>identificador</code> da classe <code>Turma</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorIdentificadorTurma(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        data = null;
        String sqlStr = "SELECT HorarioTurma.* FROM HorarioTurma, Turma WHERE HorarioTurma.turma = Turma.codigo AND upper( Turma.identificador ) LIKE('" + valorConsulta.toUpperCase() + "%') ORDER BY Turma.identificador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>HorarioTurma</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List<HorarioTurmaVO> consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        data = null;
        String sqlStr = "SELECT * FROM HorarioTurma WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    @Override
    public List<HorarioTurmaVO> consultarPorEmpresaModalidadeTurmaIdade(Integer empresa, Integer modalidade,
            Integer turma, Integer idade, List<String> diaSemana, List<String> horario, Date data, Boolean negociacao,
            int nivelMontarDados , boolean bloquearLotacaoFutura, Integer pessoaOperacao) throws Exception {
        consultar(getIdEntidade(), true);
        this.data = data;
        this.dataInicioPeriodo = data;
        this.pessoaOperacao = pessoaOperacao;
        if(negociacao && bloquearLotacaoFutura){
            this.dataInicioPeriodo = Calendario.hoje();
        }
        if (data == null) {
            this.data = Calendario.hoje();
        }
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT\n" +
                "  horarioturma.*,\n" +
                "  emp.horariocapacidadeporcategoria\n" +
                "FROM turma\n" +
                "  INNER JOIN horarioturma\n" +
                "    ON horarioturma.turma = turma.codigo\n" +
                "  INNER JOIN empresa emp\n" +
                "    ON emp.codigo = turma.empresa\n" +
                "  INNER JOIN colaborador\n" +
                "    ON horarioturma.professor = colaborador.codigo AND colaborador.situacao = 'AT'");
        sqlStr.append("WHERE 1 = 1 AND turma.codigo = ").append(turma).append(" ");
        if (negociacao) {
	        sqlStr.append(" AND turma.datainicialvigencia <= '").append(Uteis.getSQLData(this.data)).append("'");
	        sqlStr.append(" AND turma.datafinalvigencia >= '").append(Uteis.getSQLData(this.data)).append("'");
        }
        sqlStr.append("AND turma.idademinima <= ").append(idade).append(" ");
        sqlStr.append("AND turma.idadeMaxima >= ").append(idade).append(" ");
        boolean temp = true;
        for (String dia : diaSemana) {
            if (temp) {
                temp = false;
                sqlStr.append("AND horarioturma.diasemana IN (");
            } else {
                sqlStr.append(",");
            }
            sqlStr.append(" '").append(dia.toUpperCase()).append("' ");
        }
        if (!temp) {
            temp = true;
            sqlStr.append(") ");
        }
        sqlStr.append("AND turma.modalidade =").append(modalidade.intValue()).append(" ");
        for (String hora : horario) {
            if (temp) {
                temp = false;
                sqlStr.append("AND (");
            } else {
                sqlStr.append("OR ");
            }
            sqlStr.append("(horarioturma.horainicial >= '").append(hora.substring(0, 5)).append("' ");
            sqlStr.append("AND horarioturma.horainicial  <= '").append(hora.substring(8, 13)).append("') ");
        }
        if (!temp) {
            sqlStr.append(") ");
        }
        if (empresa > 0) {
            sqlStr.append("AND turma.empresa = ").append(empresa).append(" ");
        }
        if (negociacao){

            sqlStr.append(" AND ativo = 'true'\n");
        }
        sqlStr.append("and horarioturma.situacao = 'AT' ORDER BY horainicial");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, pessoaOperacao);
            }
        }
    }

    @Override
    public List<HorarioTurmaVO> consultarPorEmpresaModalidadeTurmaProfessorIdade(int empresa, int modalidade,
            int turma, int professor, int ambiente, int idade, int nivel,  List<String> diaSemana,
            List<String> horario, Date datainicio, Date datafim, Boolean negociacao, int nivelMontarDados, boolean todas, boolean bloquearLotacaoFutura, Integer pessoaOperacao) throws Exception {
        consultar(getIdEntidade(), true);
        this.data = datafim;
        this.dataInicioPeriodo = datainicio;
        this.pessoaOperacao = pessoaOperacao;
        if(negociacao && bloquearLotacaoFutura){
            this.dataInicioPeriodo = Calendario.hoje();
        }
        if (data == null) {
            this.data = Calendario.hoje();
        }
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT horarioturma.*, emp.horariocapacidadeporcategoria FROM turma");
        sqlStr.append(" INNER JOIN horarioturma on horarioturma.turma = turma.codigo ");
        sqlStr.append(" INNER JOIN empresa emp on emp.codigo = turma.empresa ");
        sqlStr.append(" WHERE horarioturma.turma = turma.codigo ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND turma.empresa = ").append(empresa);
        }
        if (negociacao) {
	        sqlStr.append(" AND turma.datainicialvigencia <= '").append(Uteis.getSQLData(this.data)).append("'");
	        sqlStr.append(" AND turma.datafinalvigencia >= '").append(Uteis.getSQLData(this.data)).append("'");
        }
        if (idade >= 0) {
            sqlStr.append(" AND turma.idademinima <= ").append(idade);
            sqlStr.append(" AND turma.idadeMaxima >= ").append(idade);
        }
        
        if (modalidade > 0) {
            sqlStr.append(" AND turma.modalidade =").append(modalidade);
        }
        if (turma > 0) {
            sqlStr.append(" AND turma.codigo = ").append(turma);
        }
        if (professor > 0) {
            sqlStr.append(" AND horarioturma.professor = ").append(professor);
        }
        if (ambiente > 0) {
            sqlStr.append(" AND horarioturma.ambiente = ").append(ambiente);
        }
        if (nivel > 0) {
            sqlStr.append(" AND horarioturma.nivelturma = ").append(nivel);
        }
        boolean temp = true;
        for (String dia : diaSemana) {
            if (temp) {
                temp = false;
                sqlStr.append(" AND horarioturma.diasemana IN (");
            } else {
                sqlStr.append(",");
            }
            sqlStr.append(" '").append(dia.toUpperCase()).append("' ");
        }
        if (!temp) {
            temp = true;
            sqlStr.append(") ");
        }
        for (String hora : horario) {
            if (temp) {
                temp = false;
                sqlStr.append(" AND (");
            } else {
                sqlStr.append(" OR ");
            }

            final String[] parteHora = hora.split(" - ");
            sqlStr.append(" (horarioturma.horainicial >= '").append(parteHora[0]).append("' ");
            sqlStr.append(" AND horarioturma.horainicial  <= '").append(parteHora[1]).append("')");
        }
        if (!temp) {
            sqlStr.append(") ");
        }
        sqlStr.append("AND\n");
        sqlStr.append("      (turma.datafinalvigencia >= '").append(Uteis.getSQLData(this.data)).append("'\n");
        sqlStr.append("       OR horarioturma.codigo IN\n");
        sqlStr.append("          (SELECT\n");
        sqlStr.append("             DISTINCT horarioturma\n");
        sqlStr.append("           FROM matriculaalunohorarioturma maht\n");
        sqlStr.append("             INNER JOIN horarioturma ht\n");
        sqlStr.append("               ON ht.codigo = maht.horarioturma\n");
        sqlStr.append("             INNER JOIN turma\n");
        sqlStr.append("               ON ht.turma = turma.codigo\n");

        if (datainicio != null) {
            sqlStr.append("           WHERE maht.datafim BETWEEN '").append(Uteis.getSQLData(datainicio)).append("' AND '").append(Uteis.getSQLData(this.data)).append("'\n");
            sqlStr.append("           OR maht.datafim >= '").append(Uteis.getSQLData(this.data)).append("'\n");
        } else {
            sqlStr.append("           WHERE maht.datafim >= '").append(Uteis.getSQLData(this.data)).append("'\n");
        }

        sqlStr.append("          )\n");
        sqlStr.append("      )");
        if (datainicio == null || !todas) {
            sqlStr.append(" AND situacao = 'AT'\n");
        }
        if (negociacao){

            sqlStr.append(" AND ativo = 'true'\n");
        }
        sqlStr.append(" and not turma.aulaColetiva\n");
        sqlStr.append(" ORDER BY horainicial, diasemana, horarioturma.codigo");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, pessoaOperacao);
            }
        }
    }

    public void validarHorarioTurmaPossuiVagasPorCapacidadeCategorias(String horarios, Integer pessoa) throws Exception {
        List<HorarioTurmaVO> horariosSelecionados = new ArrayList<>();
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("select t.identificador, ht.*, emp.horariocapacidadeporcategoria from horarioturma ht");
        sqlStr.append(" inner join turma t on t.codigo = ht.turma");
        sqlStr.append(" inner join empresa emp on emp.codigo = t.empresa");
        sqlStr.append(" where ht.codigo in (").append(horarios).append(")");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                while (tabelaResultado.next()) {
                    if (tabelaResultado.getBoolean("horariocapacidadeporcategoria")) {
                        horariosSelecionados.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, pessoa));
                        for (HorarioTurmaVO horarioTurmaVO : horariosSelecionados) {
                            int totalVagasOcupadas = (horarioTurmaVO.getNrAlunoMatriculado() + horarioTurmaVO.getNrAlunoMatriculadosFuturo());
                            if (horarioTurmaVO.getNrMaximoAluno() <= totalVagasOcupadas) {
                                throw new ConsistirException("O horário, " + horarioTurmaVO.getHoraInicial() + " às " + horarioTurmaVO.getHoraFinal() +
                                        " da turma " + tabelaResultado.getString("identificador") +
                                        ". Não possui vagas disponíveis de acordo com o número maximo de alunos por categoria, informe um novo horário");
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<HorarioTurmaVO> consultarPorDiaSemanaHorarioModalidadeTurmaProfessorEmpresa(String diaSemana, String horario, Integer modalidade, Integer turma, Integer professor, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        data = null;
        String sqlStr = "SELECT HorarioTurma.* FROM HorarioTurma , Turma, Modalidade, empresa WHERE horarioTurma.turma =  turma.codigo AND turma.modalidade =  modalidade.codigo AND turma.empresa =  empresa.codigo";
        if (modalidade != 0) {
            sqlStr = sqlStr + " AND modalidade.codigo = " + modalidade;
        }
        if (empresa != 0) {
            sqlStr = sqlStr + " AND empresa.codigo = " + empresa;
        }
        if (turma != 0) {
            sqlStr = sqlStr + " AND turma.codigo = " + turma;
        }
        if (professor != 0) {
            sqlStr = sqlStr + " AND horarioturma.professor = " + professor;
        }
        if (!diaSemana.equals("")) {
            sqlStr = sqlStr + " AND horarioturma.diasemana  = '" + diaSemana + "'";
        }
        if (!horario.equals("")) {
            sqlStr = sqlStr + " AND horarioturma.horainicial >= '" + horario.substring(0, 5) + "' AND horarioturma.horainicial  <= '" + horario.substring(8, 13) + "'";
        }
        sqlStr = sqlStr + " AND situacao = 'AT' ORDER BY HorarioTurma.horainicial";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Método que realiza consulta de um horário de uma turma por dia da semana de acordo com a disponibilidade de vagas da mesma.
     * @param horarioTurmaConcatenadoVO
     * @param diaSemana
     * @param controlarAcesso
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    @Override
    public HorarioTurmaVO consultarPorDiaSemana(HorarioTurmaConcatenadoVO horarioTurmaConcatenadoVO, String diaSemana, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        data = null;
        String sqlStr = "SELECT * "
                + " FROM horarioturma "
                + " WHERE ambiente = " + horarioTurmaConcatenadoVO.getHorarioTurma().getAmbiente().getCodigo()
                + " AND turma = " + horarioTurmaConcatenadoVO.getHorarioTurma().getTurma()
                + " AND diasemana = '" + diaSemana + "'"
                + " AND horainicial ='" + horarioTurmaConcatenadoVO.getHorarioTurma().getHoraInicial() + "'"
                + " AND horafinal = '" + horarioTurmaConcatenadoVO.getHorarioTurma().getHoraFinal() + "'"
                + " AND professor = " + horarioTurmaConcatenadoVO.getHorarioTurma().getProfessor().getCodigo()
                + " AND identificadorturma = '" + horarioTurmaConcatenadoVO.getHorarioTurma().getIdentificadorTurma() + "'"
                + " AND nivelturma = " + horarioTurmaConcatenadoVO.getHorarioTurma().getNivelTurma().getCodigo()
                + " AND situacao = '" + horarioTurmaConcatenadoVO.getHorarioTurma().getSituacao() + "'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( HorarioTurma ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Método que consulta horário de uma turma
     * que possui o dia da semana, horário inicial e final e ambiente
     * iguais aos passados como parâmetro do objeto HorarioTurmaVO
     * @param horarioTurmaVO
     * @param empresa
     * @param controlarAcesso
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    @Override
    public HorarioTurmaVO consultarPorDiaSemanaHorarioInicialHorarioFinalAmbiente(HorarioTurmaVO horarioTurmaVO, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        data = null;
        String sqlStr = "select horarioturma.*  "
                + " from horarioturma, empresa "
                + " where horarioturma.ambiente = " + horarioTurmaVO.getAmbiente().getCodigo()
                + " and horarioturma.diasemana = '" + horarioTurmaVO.getDiaSemana() + "'"
                + " and ((horarioturma.horainicial>'" + horarioTurmaVO.getHoraInicial() + "' and horarioturma.horainicial< '" + horarioTurmaVO.getHoraFinal() + "' ) "
                + " or (horarioturma.horafinal > '" + horarioTurmaVO.getHoraInicial() + "' and horarioturma.horafinal< '" + horarioTurmaVO.getHoraFinal() + "')"
                + " or (horarioturma.horainicial = '" + horarioTurmaVO.getHoraInicial() + "' and horarioturma.horafinal = '" + horarioTurmaVO.getHoraFinal() + "')) "
                + " and horarioturma.professor = " + horarioTurmaVO.getProfessor().getCodigo().intValue()
                + " and horarioturma.turma = " + horarioTurmaVO.getTurma();
        if (empresa.intValue() != 0) {
            sqlStr += " and empresa.codigo = " + empresa;
        }
        sqlStr += " and situacao = 'AT'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Método que consulta por professor que possui o dia da semana, horário inicial e final
     * @param horarioTurmaVO
     * @param empresa
     * @return
     * @throws Exception
     */
    @Override
    public HorarioTurmaVO consultarPorProfessorDiaDaSemanaHorarioInicialFinal(HorarioTurmaVO horarioTurmaVO, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        data = null;
        String sqlStr = "SELECT horarioturma.* "
                + " FROM horarioturma, empresa "
                + " WHERE horarioturma.diasemana = '" + horarioTurmaVO.getDiaSemana() + "'"
                + " AND horarioturma.horainicial ='" + horarioTurmaVO.getHoraInicial() + "'"
                + " AND horarioturma.horafinal = '" + horarioTurmaVO.getHoraFinal() + "'"
                + " AND horarioturma.professor = " + horarioTurmaVO.getProfessor().getCodigo()
                + " AND empresa.codigo = " + empresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public HorarioTurmaVO consultar(String horaInicial,String horaFinal, String diaSemana, Integer turma, Integer ambiente, Integer nivelTurma, Integer professor, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from horarioturma \n");
        sql.append("where horainicial='").append(horaInicial).append("' \n");
        sql.append("and horafinal='").append(horaFinal).append("' \n");
        sql.append("and diasemana='").append(diaSemana).append("' \n");
        sql.append("and professor=").append(professor).append(" \n");
        sql.append("and nivelTurma=").append(nivelTurma).append(" \n");
        sql.append("and turma=").append(turma).append(" \n");
        sql.append("and ambiente=").append(ambiente);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }

    }

    public HorarioTurmaVO consultarPorHorarioDiaSemanaCodTurma(String horaInicial,String horaFinal, String diaSemana, Integer codigoTurma, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from horarioturma \n");
        sql.append("where horainicial='").append(horaInicial).append("' \n");
        sql.append("and horafinal='").append(horaFinal).append("' \n");
        sql.append("and diasemana='").append(diaSemana).append("' \n");
        sql.append("and turma=").append(codigoTurma).append(" \n");
        sql.append("and situacao='AT'");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }

    }




    public HorarioTurmaVO consultarPorProfessorDiaDaSemanaHorarioInicialFinalForaDoAmbiente(HorarioTurmaVO horarioTurmaVO, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        data = null;
        String sqlStr = "SELECT\n" +
                "  horarioturma.*\n" +
                "FROM horarioturma\n" +
                "  LEFT JOIN turma\n" +
                "    ON horarioturma.turma = turma.codigo\n" +
                "WHERE 1 = 1\n" +
                "      AND horarioturma.professor = " + horarioTurmaVO.getProfessor().getCodigo() + "\n" +
                "      AND empresa = " + empresa + "\n" +
                "      AND diasemana = '" + horarioTurmaVO.getDiaSemana() + "'\n" +
                "      AND ambiente <> " + horarioTurmaVO.getAmbiente().getCodigo() + "\n" +
                "      AND situacao = 'AT'\n" +
                "      AND (horainicial BETWEEN '" + horarioTurmaVO.getHoraInicial() + "' AND '" + horarioTurmaVO.getHoraFinal() + "')\n" +
                "      AND (horafinal BETWEEN '" + horarioTurmaVO.getHoraInicial() + "' AND '" + horarioTurmaVO.getHoraFinal() + "');";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<HorarioTurmaVO> consultarHorariosConflitantesParaProfessorSemAmbiente(HorarioTurmaVO horarioTurmaVO, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT\n" +
                "  horarioturma.*\n" +
                "FROM horarioturma\n" +
                "  LEFT JOIN turma\n" +
                "    ON horarioturma.turma = turma.codigo\n" +
                "WHERE 1 = 1\n" +
                "      AND horarioturma.professor = "+horarioTurmaVO.getProfessor().getCodigo()+"\n" +
                "      AND empresa = "+empresa+"\n" +
                "      AND diasemana = '"+horarioTurmaVO.getDiaSemana()+"'\n" +
                "      AND situacao = 'AT'\n" +
                "      AND\n" +
                "      (\n" +
                "        (horainicial BETWEEN '"+horarioTurmaVO.getHoraInicial()+"' AND '"+horarioTurmaVO.getHoraFinal()+"') OR\n" +
                "        (horafinal BETWEEN '"+horarioTurmaVO.getHoraInicial()+"' AND '"+horarioTurmaVO.getHoraFinal()+"')\n" +
                "      )\n" +
                "      AND coalesce(turma.usuariodesativou, 0) = 0;";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public List<HorarioTurmaVO> consultarHorariosIguais(HorarioTurmaVO horarioTurmaVO, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT\n" +
                "  horarioturma.*\n" +
                "FROM horarioturma\n" +
                "  LEFT JOIN turma\n" +
                "    ON horarioturma.turma = turma.codigo\n" +
                "WHERE 1 = 1\n" +
                "      AND empresa = "+empresa+"\n" +
                "      AND situacao = 'AT'\n" +
                "      AND horarioturma.professor = "+horarioTurmaVO.getProfessor().getCodigo()+"\n" +
                "      AND ambiente = "+horarioTurmaVO.getAmbiente().getCodigo()+"\n" +
                "      AND diasemana = '"+horarioTurmaVO.getDiaSemana()+"'\n" +
                "      AND horainicial = '"+horarioTurmaVO.getHoraInicial()+"' \n"+
                "      AND horafinal = '"+horarioTurmaVO.getHoraFinal()+"';";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public List<HorarioTurmaVO> consultarColaboradorEmTurmas(ColaboradorVO colaboradorVO, int nivelMontarDados) throws Exception{
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT\n" +
                "  *\n" +
                "FROM horarioturma ht\n" +
                "  INNER JOIN turma t\n" +
                "    ON ht.turma = t.codigo\n" +
                "WHERE ht.situacao = 'AT'\n" +
                "      AND t.usuariodesativou is null" +
                "      AND ht.professor = "+colaboradorVO.getCodigo()+"\n" +
                "      AND\n" +
                "      (t.datafinalvigencia > '"+Uteis.getDataJDBCTimestamp(Calendario.hoje())+"'\n" +
                "       OR ht.codigo IN\n" +
                "          (SELECT\n" +
                "             DISTINCT horarioturma\n" +
                "           FROM matriculaalunohorarioturma maht\n" +
                "             INNER JOIN horarioturma ht\n" +
                "               ON ht.codigo = maht.horarioturma\n" +
                "             INNER JOIN turma\n" +
                "               ON ht.turma = turma.codigo\n" +
                "           WHERE maht.datafim > '"+Uteis.getDataJDBCTimestamp(Calendario.hoje())+"'\n" +
                "          )\n" +
                "      )\n" +
                "ORDER BY ht.codigo;";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     */
    public List<HorarioTurmaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, null);
    }

    public List<HorarioTurmaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Integer pessoaOperacao) throws Exception {
        List<HorarioTurmaVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            HorarioTurmaVO obj = montarDados(tabelaResultado, nivelMontarDados, pessoaOperacao);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private HorarioTurmaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        HorarioTurmaVO obj = new HorarioTurmaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setTurma(dadosSQL.getInt("turma"));
        obj.setHoraInicial(dadosSQL.getString("horaInicial"));
        obj.setHoraFinal(dadosSQL.getString("horaFinal"));
        obj.getProfessor().setCodigo(dadosSQL.getInt("professor"));
        obj.getAmbiente().setCodigo(dadosSQL.getInt("ambiente"));
        obj.getNivelTurma().setCodigo(dadosSQL.getInt("nivelTurma"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setDiaSemana(dadosSQL.getString("diaSemana"));
        obj.setNrMaximoAluno(dadosSQL.getInt("nrMaximoAluno"));
        obj.setLimiteVagasAgregados(dadosSQL.getInt("limiteVagasAgregados"));
        obj.setDiaSemanaNumero(dadosSQL.getInt("diaSemanaNumero"));
        obj.setIdentificadorTurma(dadosSQL.getString("identificadorTurma"));
        obj.setToleranciaEntradaMinutos(dadosSQL.getInt("toleranciaEntradaMinutos"));
        obj.setToleranciaEntradaAposMinutos(dadosSQL.getInt("toleranciaEntradaAposMinutos"));
        obj.setLiberadoMarcacaoApp(dadosSQL.getBoolean("liberadoMarcacaoApp"));
        try {
            obj.setAtivo(dadosSQL.getBoolean("ativo"));
        } catch(Exception ignored) {}
        try {
            obj.setDataEntrouTurma(dadosSQL.getTimestamp("dataEntrouTurma"));
        } catch(Exception ignored) {}
        try {
            obj.setDataSaiuTurma(dadosSQL.getTimestamp("dataSaiuTurma"));
        } catch(Exception ignored) {}
        obj.setSpiviEventID(dadosSQL.getInt("spivieventid"));
        try {
            obj.setPublicIdTurmaMgb(dadosSQL.getString("publicIdTurmaMgb"));
        } catch(Exception ignored) {}

        HorarioTurmaVO.montarDadosDiaSemanaNumero(obj);
        return obj;

    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>HorarioTurmaVO</code>.
     * @return  O objeto da classe <code>HorarioTurmaVO</code> com os dados devidamente montados.
     */
    public HorarioTurmaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        return montarDados(dadosSQL, nivelMontarDados, null);
    }

    public HorarioTurmaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Integer pessoaOperacao) throws Exception {
        HorarioTurmaVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        boolean capacidadePorCategoria = false;
        String tipoCategoriaCliente = "";
        if (Uteis.resultSetContemColuna(dadosSQL, "horariocapacidadeporcategoria") && !UteisValidacao.emptyNumber(pessoaOperacao)) {
            capacidadePorCategoria = dadosSQL.getBoolean("horariocapacidadeporcategoria");
            if (capacidadePorCategoria) {
                Cliente clienteDAO = new Cliente(con);
                tipoCategoriaCliente = clienteDAO.obterTipoCategoriaCliente(pessoaOperacao);
                obj.setNrMaximoAluno(obterNrMaximoAlunosPorCategoriaCliente(obj.getCodigo(), tipoCategoriaCliente, obj.getNrMaximoAluno()));

                obj.setTipoCategoriaCliente(tipoCategoriaCliente);
                obj.setCapacidadePorCategoria(capacidadePorCategoria);
            }
        }

        obj.setNrAlunoMatriculado(nrAlunosMatriculados(obj, tipoCategoriaCliente, capacidadePorCategoria));
        obj.setNrAlunoMatriculadosFuturo(nrAlunosMatriculadosFuturo(obj, tipoCategoriaCliente, capacidadePorCategoria));


        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }

        montarDadosAmbiente(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
        montarDadosNivelTurma(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosProfessor(obj, Uteis.NIVELMONTARDADOS_MINIMOS, this.con);
            return obj;
        }

        montarDadosProfessor(obj, Uteis.NIVELMONTARDADOS_INDICERENOVACAO, this.con);
        return obj;
    }

    private Integer obterNrMaximoAlunosPorCategoriaCliente(Integer horarioTurma, String tipoCategoriaCliente, Integer nrMaximoAlunos) {
        Integer maxCapacidadePorCateg = nrMaximoAlunos;
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select capacidade from horariocapacidadecategoria \n");
            sql.append("where horarioturma = ").append(horarioTurma);
            sql.append(" and tipocategoria = '").append(tipoCategoriaCliente).append("'");
            PreparedStatement pst1 = con.prepareStatement(sql.toString());
            ResultSet rs1 = pst1.executeQuery();
            if (rs1.next()) {
                maxCapacidadePorCateg = rs1.getInt("capacidade");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return maxCapacidadePorCateg;
    }

    private int nrAlunosMatriculados(HorarioTurmaVO obj, String tipoCategoriaCliente, boolean capacidadePorCategoria) throws Exception {
        Date dataConsulta = dataInicioPeriodo == null ? Calendario.hoje() : dataInicioPeriodo;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(mt.codigo) AS qtde FROM horarioturma ht ");
        sql.append("LEFT OUTER JOIN matriculaalunohorarioturma mt ON ht.codigo = mt.horarioturma ");
        if (capacidadePorCategoria) {
            sql.append(" LEFT OUTER JOIN cliente clim on clim.pessoa = mt.pessoa ");
            sql.append(" LEFT OUTER JOIN categoria cat on cat.codigo = clim.categoria ");
        }
        sql.append("WHERE mt.horarioturma = ").append(obj.getCodigo()).append(" AND '");
        sql.append(Uteis.getSQLData(dataConsulta));
        sql .append("' BETWEEN mt.datainicio AND mt.datafim ");
        if(UteisValidacao.notEmptyNumber(pessoaOperacao)){
            sql.append(" AND mt.pessoa <> ").append(pessoaOperacao);
        }
        if (capacidadePorCategoria) {
            sql.append(" and cat.tipocategoria = '" + tipoCategoriaCliente + "'");
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("qtde");
                } else {
                    throw new ConsistirException("Dados Não Encontrados ( HorarioTurma ).");
                }
            }
        }
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>NivelTurmaVO</code> relacionado ao objeto <code>HorarioTurmaVO</code>.
     * Faz uso da chave primária da classe <code>NivelTurmaVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosNivelTurma(HorarioTurmaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getNivelTurma().getCodigo() == 0) {
            obj.setNivelTurma(new NivelTurmaVO());
            return;
        }
        NivelTurma nivelTurma = new NivelTurma(con);
        obj.setNivelTurma(nivelTurma.consultarPorChavePrimaria(obj.getNivelTurma().getCodigo(), nivelMontarDados));
        nivelTurma = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>AmbienteVO</code> relacionado ao objeto <code>HorarioTurmaVO</code>.
     * Faz uso da chave primária da classe <code>AmbienteVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosAmbiente(HorarioTurmaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getAmbiente().getCodigo() == 0) {
            obj.setAmbiente(new AmbienteVO());
            return;
        }
        Ambiente ambiente = new Ambiente(con);
        obj.setAmbiente(ambiente.consultarPorChavePrimaria(obj.getAmbiente().getCodigo(), nivelMontarDados));
        ambiente = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ColaboradorVO</code> relacionado ao objeto <code>HorarioTurmaVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosProfessor(HorarioTurmaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProfessor().getCodigo() == 0) {
            obj.setProfessor(new ColaboradorVO());
            return;
        }
        Colaborador colaborador = new Colaborador(con);
        obj.setProfessor(colaborador.consultarPorChavePrimaria(obj.getProfessor().getCodigo(), nivelMontarDados));
        colaborador = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>HorarioTurmaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>HorarioTurma</code>.
     * @param turma campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public void excluirHorarioTurmas(Integer turma) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM HorarioTurma WHERE turma = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, turma.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>HorarioTurmaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirHorarioTurmas</code> e <code>incluirHorarioTurmas</code> disponíveis na classe <code>HorarioTurma</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public void alterarHorarioTurmas(TurmaVO turma, List objetos, List objetosExclusao) throws Exception {
        MatriculaAlunoHorarioTurmaInterfaceFacade mahtDao = new MatriculaAlunoHorarioTurma(con);
        HorarioTurma hrTurmaDAO = new HorarioTurma(con);
        ControleCreditoTreino cctDao = new ControleCreditoTreino(con);
        Iterator e = objetosExclusao.iterator();
        while (e.hasNext()) {
            HorarioTurmaVO objEx = (HorarioTurmaVO) e.next();
            objEx.setTurma(turma.getCodigo().intValue());
            //procura por alunos matriculados na turma
            List<Integer> clientesMatriculadosAulaCheia = new ArrayList<>();
            Long alunoMatriculadoHorario = mahtDao.consultarPorHorarioTurmaPorPeriodoCount(objEx.getCodigo(), Calendario.hoje(), Calendario.hoje(), false);
            if (alunoMatriculadoHorario.intValue() == 0) {
                clientesMatriculadosAulaCheia = hrTurmaDAO.consultarClientesComAgendamentosNoHorario(Calendario.hoje(), objEx.getCodigo(), turma.getEmpresa().getCodigo(), true);
            }
            //procura por alunos do historico que nao estao nesse horario mais
            Long alunoMatriculadoHorarioHistorico = mahtDao.consultarPorHorarioTurmaCount(objEx.getCodigo(), false);
            Integer alunoMatriculadoHorarioHistoricoAC = mahtDao.consultarPorHorarioTurmaACCount(objEx.getCodigo());
            boolean existeFaltaCreditoTreino = cctDao.existeControleCredito(objEx.getCodigo());
            new DemandaHorarioTurma(con).excluir(objEx.getCodigo());
            objEx.setSituacao("IN");
            desativar(objEx);
        }
        hrTurmaDAO = null;
        mahtDao = null;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            HorarioTurmaVO obj = (HorarioTurmaVO) i.next();
            obj.setTurma(turma.getCodigo().intValue());
            obj.setIdentificadorTurma(turma.getIdentificador());
            if (obj.isNovoObj()) {
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>HorarioTurmaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Turma</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public void incluirHorarioTurmas(TurmaVO turmaPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            HorarioTurmaVO obj = (HorarioTurmaVO) e.next();
            obj.setTurma(turmaPrm.getCodigo().intValue());
            obj.setIdentificadorTurma(turmaPrm.getIdentificador());
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>HorarioTurmaVO</code> relacionados a um objeto da classe <code>plano.Turma</code>.
     * @param turma  Atributo de <code>plano.Turma</code> a ser utilizado para localizar os objetos da classe <code>HorarioTurmaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    @Override
    public List consultarHorarioTurmas(Integer turma, int nivelMontarDados) throws Exception {
        return consultarHorarioTurmas(turma, nivelMontarDados, null);
    }

    public List consultarHorarioTurmas(Integer turma, int nivelMontarDados, Integer codigoPessoa) throws Exception {
        consultar(getIdEntidade());
        data = null;
        List objetos = new ArrayList();
        String sql = "SELECT ht.*, emp.horariocapacidadeporcategoria FROM HorarioTurma ht" +
                " INNER JOIN Turma t on t.codigo = ht.turma"+
                " INNER JOIN Empresa emp on emp.codigo = t.empresa"+
                " WHERE ht.turma = ? and ht.situacao = 'AT'" +
                " ORDER BY ht.horaInicial, ht.diaSemanaNumero ";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, turma.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    HorarioTurmaVO novoObj = new HorarioTurmaVO();
                    novoObj = montarDados(resultado, nivelMontarDados, codigoPessoa);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    @Override
    public List consultarTodosHorariosTurma(Integer turma, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        data = null;
        List objetos = new ArrayList();
        String sql = "SELECT * FROM HorarioTurma WHERE turma = ? ORDER BY horaInicial, diaSemanaNumero ";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, turma.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    HorarioTurmaVO novoObj = new HorarioTurmaVO();
                    novoObj = montarDados(resultado, nivelMontarDados);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>HorarioTurmaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    @Override
    public HorarioTurmaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        data = null;
        HorarioTurmaVO eCache = (HorarioTurmaVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM HorarioTurma WHERE codigo = ? order by diaSemanaNumero";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( HorarioTurma ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public JSONObject consultarParaWS(Integer codigo) throws Exception{
        JSONObject object = new JSONObject();
        String sql = "select " +
                " ht.codigo as horarioturmacodigo, " +
                " ht.identificadorturma," +
                " ht.nrMaximoAluno, " +
                " ht.situacao, " +
                " ht.diaSemanaNumero," +
                " ht.diaSemana," +
                " ht.professor," +
                " ht.horaInicial," +
                " ht.horaFinal, " +
                " p.nome as nomeprofessor, " +
                " p.fotokey, " +
                " t.descricao " +
                " from horarioturma ht \n" +
                " inner join turma t on t.codigo = ht.turma \n" +
                " inner join colaborador c on c.codigo = ht.professor \n" +
                " inner join pessoa p on p.codigo = c.pessoa \n" +
                "where ht.codigo = " + codigo;

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (rs.next()) {
                    object.put("codigo", rs.getInt("horarioturmacodigo"));
                    object.put("identificadorturma", rs.getString("identificadorturma"));
                    object.put("situacao", rs.getString("situacao"));
                    object.put("nrmaximoaluno", rs.getInt("nrMaximoAluno"));
                    object.put("diasemananumero", rs.getInt("diaSemanaNumero"));
                    object.put("diasemana", rs.getString("diaSemana"));
                    object.put("horainicial", rs.getString("horaInicial"));
                    object.put("horafinal", rs.getString("horaFinal"));
                    object.put("turmaDescricao", rs.getString("descricao"));
                    object.put("professorCodigo", rs.getInt("professor"));
                    object.put("professorNome", rs.getString("nomeprofessor"));
                    object.put("professorFotokey", rs.getString("fotokey"));
                }
            }
        }
        return object;
    }

    @Override
    public HorarioTurmaVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception {
        data = null;
        String sql = "SELECT * FROM HorarioTurma WHERE codigo = ?";
        HorarioTurmaVO horarioTurma = null;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, valorConsulta.intValue());
            try (ResultSet resultDados = sqlConsultar.executeQuery()) {
                if (resultDados.next()) {
                    horarioTurma = montarDados(resultDados, nivelMontarDados);
                }
            }
        }
        return horarioTurma;
    }


    public List<HorarioTurmaVO> consultarHorarioTurmaContratoCreditoTreino(Date dataBase)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.codigo as codigoContrato,c.datalancamento, tur.minutosantecedenciadesmarcaraula,  ht.* \n");
        sql.append("FROM Contrato c \n");
        sql.append("inner join SituacaoClienteSinteticoDW sintetico on sintetico.codigoContrato = c.codigo \n");
        sql.append("inner join contratoDuracao cd on cd.contrato = c.codigo \n");
        sql.append("inner join contratoDuracaoCreditoTreino cdc on cdc.contratoDuracao = cd.codigo \n");
        sql.append("inner join contratoModalidade cm on cm.contrato = c.codigo \n");
        sql.append("inner join ContratoModalidadeTurma cmt on cmt.contratoModalidade = cm.codigo \n");
        sql.append("inner join contratomodalidadehorarioturma cmht on cmht.contratoModalidadeTurma = cmt.codigo \n");
        sql.append("inner join horarioTurma ht on ht.codigo = cmht.horarioTurma \n");
        sql.append("inner join turma tur on ht.turma = tur.codigo \n");
        sql.append(" inner join matriculaalunohorarioturma mht on mht.horarioturma = ht.codigo and mht.contrato = c.codigo \n");
        sql.append("WHERE cdc.quantidadeCreditoDisponivel > 0 and c.vendaCreditoTreino = true \n");
        sql.append("and sintetico.situacaoContrato in ('NO','AV','VE') \n");// como esse processo roda baseado no dia anterior, caso o aluno tenha uma ultima aula no ultimo dia do contrato, a situação dele vai estar VE quando processar o dia da ultima aula
        sql.append(" and '").append(Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataBase))).append("' between  mht.datainicio and  mht.datafim \n");
        sql.append("and cdc.tipoHorario = ").append(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA.getCodigo()).append(" \n");
        sql.append("order by c.codigo");
        List<HorarioTurmaVO> lista;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                lista = new ArrayList<HorarioTurmaVO>();
                while (rs.next()) {
                    HorarioTurmaVO horarioTurmaVO = montarDadosBasico(rs);
                    ContratoVO contratoVO = new ContratoVO();
                    contratoVO.setCodigo(rs.getInt("codigoContrato"));
                    contratoVO.setDataLancamento(rs.getTimestamp("datalancamento"));
                    horarioTurmaVO.setContratoVO(contratoVO);
                    horarioTurmaVO.setMinutosAntecedenciaDesmarcarAula(rs.getInt("minutosantecedenciadesmarcaraula"));
                    lista.add(horarioTurmaVO);
                }
            }
        }
        return lista;
    }

    public int nrAlunosMatriculadosRenovacaoRematricula(HorarioTurmaVO obj, Date dataInicio, Set<Integer> codClientes) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        if (codClientes != null) {
            sqlStr.append("SELECT codigocliente FROM horarioturma ht ");
        } else {
            sqlStr.append("SELECT COUNT(mt.codigo) AS qtde FROM horarioturma ht ");
        }
        sqlStr.append(" LEFT OUTER JOIN matriculaalunohorarioturma mt ON ht.codigo = mt.horarioturma ");
        sqlStr.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigopessoa = mt.pessoa ");
        sqlStr.append(" AND sc.situacaocontrato not in ('TR','AE','CR') ");
        sqlStr.append(" WHERE mt.horarioturma = " + obj.getCodigo() + " AND '").append(Uteis.getSQLData(dataInicio));
        sqlStr.append("' BETWEEN mt.datainicio AND mt.datafim ");
        sqlStr.append(" AND sc.codigocliente NOT IN (SELECT cliente FROM reposicao WHERE dataReposicao = '").append(Uteis.getSQLData(dataInicio));
        sqlStr.append("' AND horarioTurma = " + obj.getCodigo() + " ) ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                if(codClientes != null){
                    int qtdClientes = 0;
                    while(tabelaResultado.next()) {
                        codClientes.add(tabelaResultado.getInt("codigocliente"));
                        qtdClientes++;
                    }
                    return qtdClientes;
                }else {
                    if (tabelaResultado.next()) {
                        return tabelaResultado.getInt("qtde");
                    } else {
                        throw new ConsistirException("Dados Não Encontrados ( HorarioTurma ).");
                    }
                }
            }
        }
    }

    public void atualizarReposicao(AlunoHorarioTurmaVO obj) throws Exception {
        String sql = "select a.codigo from alunohorarioturmadesmarcado a\n" +
                "        inner join contrato con on con.codigo = a.contrato\n" +
                "        inner join plano p on p.codigo = con.plano\n" +
                "        where a.reposicao and a.cliente = " + obj.getCliente() +
                "        and aulareposta is null\n" +
                "        and datalimitereposicao  >= current_date\n" +
                "        and (con.vigenciaateajustada >= current_date or a.manternarenovacao)\n" +
                "        and aulareposta is null order by a.codigo limit 1";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            ResultSet rs = stm.executeQuery();
            if(rs.next()){
                int codigo = rs.getInt("codigo");
                sql = "update alunohorarioturmadesmarcado set aulareposta = ?, diareposta = ? where codigo = ?";
                try (PreparedStatement stm2 = con.prepareStatement(sql)) {
                    stm2.setInt(1, obj.getCodigo());
                    stm2.setDate(2, Uteis.getDataJDBC(obj.getData()));
                    stm2.setInt(3, codigo);
                    stm2.execute();
                }
            }
        }
    }

    public void incluirAlunoAulaCheia(AlunoHorarioTurmaVO obj, AgendaTotalJSON aula, final String key) throws Exception {
        validarAlunoJaMatriculado(obj);
        String sql = "INSERT INTO AlunoHorarioTurma( horarioturma, cliente, dia, aulaexperimental, desafio,origemsistema,datalancamento,usuario, autorizado, passivo, indicado, espera, autorizadoGestaoRede, codAcessoAutorizado, matriculaAutorizado) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setInt(++i, obj.getHorarioTurma().getCodigo());
            if(obj.getCliente() == null){
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setInt(++i, obj.getCliente());
            }
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getData()));
            sqlInserir.setBoolean(++i, obj.getExperimental());
            sqlInserir.setBoolean(++i, obj.getDesafio());
            sqlInserir.setInt(++i, obj.getOrigemSistema().getCodigo());
            if(obj.getDatalancamento() != null) {
                sqlInserir.setTimestamp(++i,  Uteis.getDataJDBCTimestamp(obj.getDatalancamento()));
            } else {
                sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            }
            sqlInserir.setInt(++i, obj.getUsuario());
            if(obj.getAutorizado() == null){
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setInt(++i, obj.getAutorizado());
            }
            if(obj.getPassivo() == null){
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setInt(++i, obj.getPassivo());
            }
            if(obj.getIndicado() == null){
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setInt(++i, obj.getIndicado());
            }
            sqlInserir.setBoolean(++i, obj.getEspera());
            sqlInserir.setBoolean(++i, obj.getAutorizadoGestaoRede());
            if (UteisValidacao.emptyString(obj.getCodAcessoAutorizado())){
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setString(++i, obj.getCodAcessoAutorizado());
            }

            if (UteisValidacao.emptyNumber(obj.getMatriculaAutorizado())){
                sqlInserir.setNull(++i, 0);
            } else {
                sqlInserir.setInt(++i, obj.getMatriculaAutorizado());
            }

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigoAHT());
        obj.setNovoObj(false);

        validarAlunosXVagas(obj, aula);
    }

    @Override
    public void incluirAlunoAulaCheiaV2(AlunoHorarioTurmaVO obj, AgendaTotalJSON aula, final String key) throws Exception {
        Connection transactionConnection = null;
        boolean originalAutoCommit = true;
        try {
            // Obter uma nova conexão do pool para gerenciamento explícito de transação
            transactionConnection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
            originalAutoCommit = transactionConnection.getAutoCommit();
            transactionConnection.setAutoCommit(false);

            // Criar uma nova instância da classe usando a conexão transacional
            HorarioTurma transactionalDao = new HorarioTurma(transactionConnection);

            // Executar validação usando a conexão transacional
            transactionalDao.validarAlunoJaMatriculado(obj);

            String sql = "INSERT INTO AlunoHorarioTurma( horarioturma, cliente, dia, aulaexperimental, desafio,origemsistema,datalancamento,usuario, autorizado, passivo, indicado, espera) "
                    + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = transactionConnection.prepareStatement(sql)) {
                int i = 0;
                sqlInserir.setInt(++i, obj.getHorarioTurma().getCodigo());
                if(obj.getCliente() == null){
                    sqlInserir.setNull(++i, 0);
                } else {
                    sqlInserir.setInt(++i, obj.getCliente());
                }
                sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getData()));
                sqlInserir.setBoolean(++i, obj.getExperimental());
                sqlInserir.setBoolean(++i, obj.getDesafio());
                sqlInserir.setInt(++i, obj.getOrigemSistema().getCodigo());
                if(obj.getDatalancamento() != null) {
                    sqlInserir.setTimestamp(++i,  Uteis.getDataJDBCTimestamp(obj.getDatalancamento()));
                } else {
                    sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                }
                sqlInserir.setInt(++i, obj.getUsuario());
                if(obj.getAutorizado() == null){
                    sqlInserir.setNull(++i, 0);
                } else {
                    sqlInserir.setInt(++i, obj.getAutorizado());
                }
                if(obj.getPassivo() == null){
                    sqlInserir.setNull(++i, 0);
                } else {
                    sqlInserir.setInt(++i, obj.getPassivo());
                }
                if(obj.getIndicado() == null){
                    sqlInserir.setNull(++i, 0);
                } else {
                    sqlInserir.setInt(++i, obj.getIndicado());
                }
                sqlInserir.setBoolean(++i, obj.getEspera());
                sqlInserir.execute();
            }

            // Obter o código usando a conexão transacional
            obj.setCodigo(transactionalDao.obterValorChavePrimariaCodigoAHT());
            obj.setNovoObj(false);

            // Validar usando a conexão transacional
            transactionalDao.validarAlunosXVagas(obj, aula);

            // Commit explícito da transação após todas as operações
            transactionConnection.commit();

        } catch (Exception e) {
            // Rollback em caso de exceção
            if (transactionConnection != null) {
                try {
                    transactionConnection.rollback();
                } catch (SQLException rollbackEx) {
                    Uteis.logar(rollbackEx, HorarioTurma.class);
                }
            }
            throw e;
        } finally {
            // Restaurar o estado original do autocommit e fechar a conexão
            if (transactionConnection != null) {
                try {
                    transactionConnection.setAutoCommit(originalAutoCommit);
                    transactionConnection.close();
                } catch (SQLException finallyEx) {
                    Uteis.logar(finallyEx, HorarioTurma.class);
                }
            }
        }
    }

    public void incluirAlunoFilaEspera(FilaDeEsperaVO obj) throws Exception {
        //data de registro  a data da aula
        //data de entrada  que julga a posio do aluno na fila
        String sqlOrdem = "SELECT COALESCE(MAX(ordem), 0) + 1 AS novaOrdem FROM filaesperaturma WHERE horarioturma = ? AND dataRegistro = ?";
        String sqlInserir = "INSERT INTO filaesperaturma (cliente, dataRegistro, horarioturma, dataEntrada, ordem) VALUES (?, ?, ?, ?, ?)";

        try (PreparedStatement psOrdem = con.prepareStatement(sqlOrdem)) {
            psOrdem.setInt(1, obj.getCodigoHorarioTurma());
            psOrdem.setDate(2, Uteis.getDataJDBC(Uteis.getDate(obj.getDia())));

            try (ResultSet rs = psOrdem.executeQuery()) {
                if (rs.next()) {
                    obj.setOrdem(rs.getInt("novaOrdem"));
                } else {
                    throw new SQLException("Erro ao obter a nova ordem.");
                }
            }
        }

        try (PreparedStatement psInserir = con.prepareStatement(sqlInserir)) {
            psInserir.setInt(1, obj.getCodigoAluno());
            psInserir.setDate(2, Uteis.getDataJDBC(Uteis.getDate(obj.getDia())));
            psInserir.setInt(3, obj.getCodigoHorarioTurma());
            psInserir.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getDataEntrada()));
            psInserir.setInt(5, obj.getOrdem());
            psInserir.execute();
        }
    }

    private void validarAlunosXVagas(AlunoHorarioTurmaVO obj, AgendaTotalJSON aula) throws Exception{
        if(aula == null ){
            return;
        }
        Integer nrOcupadosAntesDesse = 0;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                "SELECT COUNT(*) as ocupacao FROM alunohorarioturma WHERE horarioturma = " + aula.getId()
                        + " AND dia = '" + Calendario.getDataAplicandoFormatacao(obj.getData(), "yyyy-MM-dd") + "'"
                        + " AND codigo < " + obj.getCodigo(), con)) {
            if(rs.next()){
                nrOcupadosAntesDesse = rs.getInt("ocupacao");
            }
        }catch (Exception e){
            Uteis.logar(e, HorarioTurma.class);
        }

        if(nrOcupadosAntesDesse >= aula.getNrVagas()){
            excluirAlunoAulaCheia(obj);
            throw new Exception("A aula já está cheia!");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("Select codigo FROM  AlunoHorarioTurma\n");
        sql.append("WHERE\n");
        sql.append("horarioturma = ? and ");
        if (!UteisValidacao.emptyNumber(obj.getCliente())) {
            sql.append(" cliente = ? ");
        } else if (obj.getAutorizadoGestaoRede()) {
            sql.append(" codAcessoAutorizado = ? ");
        } else if (!obj.getAutorizadoGestaoRede() && !UteisValidacao.emptyNumber(obj.getAutorizado())) {
            sql.append(" autorizado = ? ");
        } else if (!UteisValidacao.emptyNumber(obj.getPassivo())) {
            sql.append(" passivo = ? ");
        } else {
            sql.append(" indicado = ? ");
        }
        sql.append(" AND dia = ? AND codigo < " +  obj.getCodigo());

        if (obj.getAutorizadoGestaoRede()) {
            sql.append(" AND matriculaAutorizado = ? ");
        }
        try (PreparedStatement sqlInserir = con.prepareStatement(sql.toString())) {
            sqlInserir.setInt(1, obj.getHorarioTurma().getCodigo());
            if (obj.getAutorizadoGestaoRede()){
                sqlInserir.setString(2, obj.getCodAcessoAutorizado());
            } else {
                sqlInserir.setInt(2, !UteisValidacao.emptyNumber(obj.getCliente()) ? obj.getCliente() : (!UteisValidacao.emptyNumber(obj.getAutorizado()) ? obj.getAutorizado() : (!UteisValidacao.emptyNumber(obj.getPassivo()) ? obj.getPassivo() : obj.getIndicado())));
            }
            sqlInserir.setDate(3, Uteis.getDataJDBC(obj.getData()));
            if (obj.getAutorizadoGestaoRede()) {
                sqlInserir.setInt(4, obj.getMatriculaAutorizado());
            }
            try (ResultSet rs = sqlInserir.executeQuery()) {
                if(rs.next()){
                    executarConsulta("delete from alunohorarioturma where codigo = " + obj.getCodigo(), con);
                }
            }
        }
    }

    public void validarAlunoJaMatriculado(AlunoHorarioTurmaVO obj) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select a.horarioturma, ht.horainicial, ht.horafinal from  AlunoHorarioTurma a\n");
        sql.append(" inner join horarioturma ht on a.horarioturma = ht.codigo\n");
        sql.append(" where a.horarioturma <> ? \n");
        if (obj.getAutorizadoGestaoRede()) {
            sql.append(" and a.codAcessoAutorizado = ? \n");
        } else {
            sql.append(" and a." + (!UteisValidacao.emptyNumber(obj.getCliente()) ? "cliente" : (!UteisValidacao.emptyNumber(obj.getAutorizado()) ? "autorizado" : (!UteisValidacao.emptyNumber(obj.getPassivo()) ? "passivo" : "indicado"))) + " = ? \n");
        }
        sql.append(" and  a.dia = ? and ht.situacao = 'AT' ");

        if (obj.getAutorizadoGestaoRede()){
            sql.append(" and  a.matriculaAutorizado = ? ");
        }

        List<HorarioTurmaVO> horarios;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setInt(1, obj.getHorarioTurma().getCodigo());
            if (obj.getAutorizadoGestaoRede()){
                stm.setString(2, obj.getCodAcessoAutorizado());
            } else {
                stm.setInt(2, !UteisValidacao.emptyNumber(obj.getCliente()) ? obj.getCliente() : (!UteisValidacao.emptyNumber(obj.getAutorizado()) ? obj.getAutorizado() : (!UteisValidacao.emptyNumber(obj.getPassivo()) ? obj.getPassivo() : obj.getIndicado())));
            }
            stm.setDate(3, Uteis.getDataJDBC(obj.getData()));

            if (obj.getAutorizadoGestaoRede()){
                stm.setInt(4, obj.getMatriculaAutorizado());
            }
            try (ResultSet rs = stm.executeQuery()) {
                horarios = new ArrayList<HorarioTurmaVO>();
                while (rs.next()) {
                    HorarioTurmaVO ht = new HorarioTurmaVO();
                    ht.setHoraInicial(rs.getString("horainicial"));
                    ht.setHoraFinal(rs.getString("horafinal"));
                    ht.setCodigo(rs.getInt("horarioturma"));
                    horarios.add(ht);
                }
            }
        }
        if(!horarios.isEmpty()){
            try (ResultSet rsHorario = SuperFacadeJDBC.criarConsulta("select horainicial, horafinal " +
                    "from horarioturma where codigo = " + obj.getHorarioTurma(), con)) {
                if (rsHorario.next()) {
                    Date inicioMarcar = Calendario.getDataComHora(obj.getData(), rsHorario.getString("horainicial"));
                    Date fimMarcar = Calendario.getDataComHora(obj.getData(), rsHorario.getString("horafinal"));
                    for (HorarioTurmaVO ht : horarios) {
                        Date inicioJa = Calendario.getDataComHora(obj.getData(), ht.getHoraInicial());
                        Date fimja = Calendario.getDataComHora(obj.getData(), ht.getHoraFinal());

                        if (Calendario.entre(inicioMarcar, inicioJa, fimja)
                                || Calendario.entre(fimMarcar, inicioJa, fimja)
                                || Calendario.entre(inicioJa, inicioMarcar, fimMarcar)
                                || Calendario.entre(fimja, inicioMarcar, fimMarcar)
                                || fimja.equals(fimMarcar)
                                || inicioJa.equals(inicioMarcar)) {
                            throw new Exception("Matrícula na aula não pode ser feita pois o aluno já está em outra aula no mesmo horário!");
                        }
                    }
                }
            }


        }

    }

    public int reposicoesDisponiveisAulaColetiva(Integer cliente) throws Exception{
        String sql = "select count(*) as total from alunohorarioturmadesmarcado a\n" +
                "        inner join contrato con on con.codigo = a.contrato\n" +
                "        inner join plano p on p.codigo = con.plano\n" +
                "        where a.reposicao and a.cliente = " + cliente +
                "        and aulareposta is null\n" +
                "        and datalimitereposicao  >= current_date\n" +
                "        and (con.vigenciaateajustada >= current_date or a.manternarenovacao)\n" +
                "        and aulareposta is null";


        try (PreparedStatement stm = con.prepareStatement(sql)) {
            ResultSet rs = stm.executeQuery();
            return rs.next() ? rs.getInt("total") : 0;
        }
    }

    public boolean validarSeAulaDesmarcadaEReposicao(Integer cliente, Date diaDesmarcacao, Integer codigoHorarioDesmarcacao) throws Exception {
        String sql = "select exists(select 1 from alunohorarioturmadesmarcado a\n" +
                "        inner join contrato con on con.codigo = a.contrato\n" +
                "        inner join plano p on p.codigo = con.plano\n" +
                "        left join alunohorarioturma aht on aht.codigo = a.aulareposta\n" +
                "        where a.reposicao and a.cliente = " + cliente +
                "        and datalimitereposicao  >= current_date\n" +
                "        and (con.vigenciaateajustada >= current_date or a.manternarenovacao)\n" +
                "        and aulareposta is not null\n" +
                "        and a.diareposta = '" + Uteis.getDataJDBC(diaDesmarcacao) + "'\n" +
                "        and aht.codigo is null) as existe";

        try (PreparedStatement stm = con.prepareStatement(sql)) {
            ResultSet rs = stm.executeQuery();
            return rs.next() && rs.getBoolean("existe");
        }
    }

    public void gravarAlunoHorarioTurmaDesmarcado(Integer horarioTurma,
                                                  Integer cliente,
                                                  Integer contrato,
                                                  Date dia,
                                                  String origem,
                                                  Integer usuario,
                                                  Integer limiteReposicoes,
                                                  Boolean manterRenovacao,
                                                  Boolean bloquearGerarReposicaoAulaJaReposta){
        try{
            boolean reposicao = true;
            if(UteisValidacao.emptyNumber(limiteReposicoes)){
                reposicao = false;
            } else {
                if( reposicoesDisponiveisAulaColetiva(cliente) >= limiteReposicaoAulaColetiva(contrato)){
                    reposicao = false;
                }
            }

            if (reposicao && bloquearGerarReposicaoAulaJaReposta) {
                boolean existe = validarSeAulaDesmarcadaEReposicao(cliente, dia, horarioTurma);
                if (existe) {
                    reposicao = false;
                }
            }

            String sql = "INSERT INTO alunohorarioturmadesmarcado( horarioturma, " +
                    "cliente, " +
                    "contrato, " +
                    "dia, " +
                    "origem," +
                    "lancamento," +
                    "reposicao," +
                    "manternarenovacao," +
                    "datalimitereposicao," +
                    "usuario) "
                    + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                int i = 0;
                sqlInserir.setInt(++i, horarioTurma);
                sqlInserir.setInt(++i, cliente);
                sqlInserir.setInt(++i, contrato);
                sqlInserir.setDate(++i, Uteis.getDataJDBC(dia));
                sqlInserir.setString(++i, origem);
                sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                sqlInserir.setBoolean(++i, reposicao);
                sqlInserir.setBoolean(++i, manterRenovacao);
                if(reposicao){
                    sqlInserir.setDate(++i, Uteis.getDataJDBC(Uteis.somarDias(dia, limiteReposicoes)));
                } else {
                    sqlInserir.setNull(++i, 0);
                }
                sqlInserir.setInt(++i, usuario);
                sqlInserir.execute();
            }
        } catch (Exception e){
            Uteis.logar(e, HorarioTurma.class);
        }
    }

    public int limiteReposicaoAulaColetiva(Integer contrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select pl.limiteReposicaoAulaColetiva from plano pl ");
        sql.append(" inner join contrato con on pl.codigo = con.plano ");
        sql.append(" where con.codigo = ").append(contrato);
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getInt("limiteReposicaoAulaColetiva");
                }
            }
        }
        return 0;
    }

    public int limiteReposicaoAulaColetivaCliente(Integer cliente) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select p.limitereposicaoaulacoletiva from situacaoclientesinteticodw s \n");
            sql.append("inner join contrato c on c.codigo = s.codigocontrato \n");
            sql.append("inner join plano p on c.plano = p.codigo \n");
            sql.append("where s.codigocliente =").append(cliente);
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    if (rs.next()) {
                        return rs.getInt("limiteReposicaoAulaColetiva");
                    }
                }
            }
        }catch (Exception e){
            Uteis.logar(e, HorarioTurma.class);
        }
        return 0;
    }

    @Override
    public void excluirAlunoAulaCheia(AlunoHorarioTurmaVO obj) throws Exception {
        String paramCliente = "cliente";
        if(!UteisValidacao.emptyNumber(obj.getPassivo())){
            paramCliente = "passivo";
        }
        String sql1 = "SELECT CODIGO FROM AlunoHorarioTurma WHERE horarioturma = ? AND "+paramCliente+" = ? AND dia = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql1)) {
            sqlConsultar.setInt(1, obj.getHorarioTurma().getCodigo());
            sqlConsultar.setInt(2, !UteisValidacao.emptyNumber(obj.getPassivo()) ? obj.getPassivo() : obj.getCliente());
            sqlConsultar.setDate(3, Uteis.getDataJDBC(obj.getData()));
            sqlConsultar.execute();
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                while (rs.next()) {
                    excluirRelacionamentoFecharMetaDetalhado(rs.getInt("codigo"));
                    String sqlExcluirHistoricoContato = "DELETE FROM historicocontato WHERE agenda IN (SELECT codigo FROM agenda WHERE alunohorarioturma = ?);";
                    try (PreparedStatement pssqlExcluirHistoricoContato = con.prepareStatement(sqlExcluirHistoricoContato)) {
                        pssqlExcluirHistoricoContato.setInt(1, rs.getInt("codigo"));
                        pssqlExcluirHistoricoContato.execute();
                    }

                    String sql = "DELETE FROM agenda WHERE alunohorarioturma = ? ";
                    try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                        sqlExcluir.setInt(1, rs.getInt("codigo"));
                        sqlExcluir.execute();
                    }
                }
            }
        }

        String sql2 = "DELETE FROM AlunoHorarioTurma WHERE horarioturma = ? AND "+paramCliente+" = ? AND dia = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql2)) {
            sqlExcluir.setInt(1, obj.getHorarioTurma().getCodigo());
            sqlExcluir.setInt(2, !UteisValidacao.emptyNumber(obj.getPassivo()) ? obj.getPassivo() : obj.getCliente());
            sqlExcluir.setDate(3, Uteis.getDataJDBC(obj.getData()));
            sqlExcluir.execute();
        }
    }
    
    private void excluirRelacionamentoFecharMetaDetalhado(Integer codAlunoHorarioTurma) throws Exception {
        // Excluir relacionamento vinculado a remoção de aluno de aula coletiva
        String sql = "SELECT codigo FROM historicocontato WHERE agenda IN (SELECT codigo FROM agenda WHERE alunohorarioturma = ?)";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codAlunoHorarioTurma);
            sqlConsultar.execute();
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                while (rs.next()) {
                    String sqlFecharMetaDetalhado = "DELETE FROM FecharMetaDetalhado WHERE historicocontato = ?;";
                    try (PreparedStatement ps = con.prepareStatement(sqlFecharMetaDetalhado)) {
                        ps.setInt(1, rs.getInt("codigo"));
                        ps.execute();
                    }
                }
            }
        }
    }
    
    @Override
    public List<AgendadoJSON> consultarAgendadosAulaColetiva(Date inicio, Date fim, Integer empresa) throws Exception{
        
        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT aht.autorizado, c.codigo as cliente, c.matricula, p.nome, p.codigo as pessoa, \n");
        sql.append(" t.codigo as turma, aht.codigo as alunohorarioturma, aht.aulaexperimental, aht.bookingid, \n");
        sql.append(" aht.desafio, \n");
        sql.append(" ht.codigo as horarioturma, s.situacao,  aht.dia,s.situacaocontrato as situacaoContrato, \n");
        sql.append(" cast(coalesce((SELECT pac.pessoa FROM aulaavulsadiaria aad INNER JOIN \n");
        sql.append("         periodoacessocliente pac ON aad.codigo = pac.aulaavulsadiaria \n");
        sql.append("         where c.pessoa = pac.pessoa AND t.modalidade = aad.modalidade and aht.dia \n");
        sql.append("         BETWEEN pac.datainicioacesso and pac.datafinalacesso limit 1 \n");
        sql.append(" ), 0) as boolean) diaria, p.fotokey \n");
        sql.append(" from alunohorarioturma aht\n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = aht.horarioturma\n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma\n");
        sql.append(" LEFT JOIN cliente c ON c.codigo = aht.cliente\n");
        sql.append(" LEFT JOIN pessoa p ON p.codigo = c.pessoa\n");
        sql.append(" LEFT JOIN situacaoclientesinteticodw s on s.codigocliente = c.codigo \n");
        sql.append(" WHERE aht.dia BETWEEN '").append(Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio))).append("' ");
        sql.append(" AND '").append(Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59"))).append("' \n");
        sql.append(" AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL)");

        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                AgendadoJSON agendado = new AgendadoJSON();
                agendado.setCodigoCliente(rs.getInt("cliente"));
                agendado.setMatricula(rs.getString("matricula"));
                agendado.setNome(rs.getString("nome"));
                Integer autorizado = rs.getInt("autorizado");
                if(!UteisValidacao.emptyNumber(autorizado)){
                    AutorizacaoAcessoGrupoEmpresarial daoAutorizacao = new AutorizacaoAcessoGrupoEmpresarial(getCon());
                    AutorizacaoAcessoGrupoEmpresarialVO acessoGrupoEmpresarialVO = daoAutorizacao.consultarSimples(autorizado);
                    agendado.setCodigoCliente(acessoGrupoEmpresarialVO.getCodigo());
                    agendado.setMatricula("AUT" + (acessoGrupoEmpresarialVO.getCodigoMatricula() == null ?
                            "" :
                            String.valueOf(acessoGrupoEmpresarialVO.getCodigoMatricula())));
                    agendado.setNome(acessoGrupoEmpresarialVO.getNomePessoa());
                    agendado.setCidade(acessoGrupoEmpresarialVO.getIntegracao().getEmpresaRemota().getNome());
                    ResultSet rsConfirmadoAutorizado = criarConsulta("select codigo from aulaconfirmada where autorizado = " + autorizado
                            + " and horario = " + rs.getInt("horarioturma") + " and diaaula =  '"
                            + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)) + "' ", con);
                    agendado.setConfirmado(rsConfirmadoAutorizado.next());
                }
                agendado.setCodigoPessoa(rs.getInt("pessoa"));
                agendado.setId_agendamento(String.valueOf(rs.getInt("horarioturma")));
                agendado.setInicio(Uteis.getDataAplicandoFormatacao(rs.getDate("dia"), "dd/MM/yyyy"));
                agendado.setFim(Uteis.getDataAplicandoFormatacao(rs.getDate("dia"), "dd/MM/yyyy"));
                agendado.setSituacaoContrato(rs.getString("situacaoContrato"));
                agendado.setSituacao(rs.getString("situacao"));
                agendado.setDiaria(rs.getBoolean("diaria"));
                agendado.setExperimental(rs.getBoolean("aulaexperimental"));
                String bookingid = rs.getString("bookingid");

                try {
                    if (rs.getString("situacao") != null && !rs.getString("situacao").equals("AT") && UteisValidacao.emptyString(bookingid)) {
                        ResultSet rsCheckinGympass = criarConsulta("SELECT EXISTS ( SELECT ic.token \n" +
                                "FROM infocheckin ic \n" +
                                "INNER JOIN periodoacessocliente pac ON pac.codigo = ic.periodoacesso \n" +
                                "WHERE ic.cliente = " + rs.getInt("cliente") + " \n" +
                                "AND '" + rs.getDate("dia") + "' BETWEEN pac.datainicioacesso AND pac.datafinalacesso) as temCheckin", con);
                        if (rsCheckinGympass.next()) {
                            agendado.setGymPass(rsCheckinGympass.getBoolean("temCheckin"));
                        }
                    }
                } catch (Exception e) {
                    Uteis.logar(e, HorarioTurma.class);
                }

                if(!UteisValidacao.emptyString(bookingid)){
                    agendado.setGymPass(true);
                } else if ((agendado.isExperimental() || agendado.isDiaria()) && !agendado.isGymPass()) {
                    try {
                        ResultSet set = criarConsulta("select tipototalpass from periodoacessocliente pa \n" +
                                "inner join pessoa p on p.codigo = pa.pessoa \n" +
                                "where (tipoacesso = 'DI' or tipoacesso = 'PL') and tipototalpass = true \n" +
                                "and '" + Calendario.getDataAplicandoFormatacao(rs.getDate("dia"), "yyyy-MM-dd") + "' between datainicioacesso and datafinalacesso\n" +
                                "and pessoa = " + agendado.getCodigoPessoa(), con);
                        agendado.setTotalPass(set.next());
                    }catch (Exception e){
                        agendado.setTotalPass(false);
                    }
                }
                agendado.setDesafio(rs.getBoolean("desafio"));
                agendado.setFotokey(rs.getString("fotokey"));
                lista.add(agendado);
            }
        }
        return lista;
    }
    
    @Override
    public List<HorarioTurmaVO> consultarHorarioTurmaComBaseHorarios(Integer codigoHorarioTurma, 
                String[] horarios,String[] dias, boolean excluir) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM horarioturma \n");
        sql.append("WHERE turma = ").append(codigoHorarioTurma).append(" AND (").append(excluir ? " NOT (" : " (");
        boolean first = true;
        for (String s : horarios) {
            if(!first){
                sql.append(" OR ");
            }
            String inicio = s.substring(0, 5);
            String fim = s.substring(8, 13);
            sql.append("(horainicial = '").append(inicio);
            sql.append("' AND horafinal = '").append(fim).append("') \n");
            first = false;
        }
        sql.append(excluir ? " ) OR diasemana NOT IN (" : ") AND diasemana IN (");
        first = true;
        for (String d : dias) {
            if(!first){
                sql.append(", ");
            }
            sql.append("'").append(d).append("'");
            first = false;
        }
        sql.append("))");

        List<HorarioTurmaVO> lista;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                lista = new ArrayList<HorarioTurmaVO>();
                while (rs.next()) {
                    HorarioTurmaVO horarioTurmaVO = montarDadosBasico(rs);
                    lista.add(horarioTurmaVO);
                }
            }
        }
        return lista;
    }
    public boolean alunoAgendadoHorarioDia(AlunoHorarioTurmaVO obj) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("Select * FROM  AlunoHorarioTurma\n");
        sql.append("WHERE\n");
        sql.append("horarioturma = ? AND cliente = ? AND dia = ?");
        try (PreparedStatement sqlInserir = con.prepareStatement(sql.toString())) {
            sqlInserir.setInt(1, obj.getHorarioTurma().getCodigo());
            sqlInserir.setInt(2, obj.getCliente());
            sqlInserir.setDate(3, Uteis.getDataJDBC(obj.getData()));
            try (ResultSet rs = sqlInserir.executeQuery()) {
                return rs.next() ? true : false;
            }
        }
    }

    public boolean alunoAgendadoHorarioDiaPorTerminal(Integer codCliente, Date dataConsulta, String terminal) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT aht.codigo FROM alunohorarioturma  aht\n");
        sql.append("INNER JOIN horarioturma ht ON aht.horarioturma = ht.codigo\n");
        sql.append("INNER JOIN turma t ON ht.turma = t.codigo\n");
        sql.append("INNER JOIN ambiente amb ON t.ambiente = amb.codigo\n");
        sql.append("INNER JOIN coletor col ON amb.coletor = col.codigo\n");
        sql.append("WHERE aht.cliente = ?\n");
        sql.append("AND dia = ?\n");
        sql.append("AND col.numeroterminal = ?\n");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            int i = 0;
            ps.setInt(++i, codCliente);
            ps.setDate(++i, Uteis.getDataJDBC(dataConsulta));
            ps.setInt(++i, Integer.parseInt(terminal));
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next();
            }
        }
    }

    public List<HorarioTurmaVO>  horarioTurmaAlunoAgendadoHorarioDia(Integer codCliente, Date dataConsulta) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ht.* FROM alunohorarioturma  aht\n");
        sql.append("INNER JOIN horarioturma ht ON aht.horarioturma = ht.codigo\n");
        sql.append("WHERE aht.cliente = ?\n");
        sql.append("AND dia = ?\n");
        List<HorarioTurmaVO> lista;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql.toString())) {
            int i = 0;
            sqlInserir.setInt(++i, codCliente);
            sqlInserir.setDate(++i, Uteis.getDataJDBC(dataConsulta));
            try (ResultSet rs = sqlInserir.executeQuery()) {
                lista = new ArrayList<HorarioTurmaVO>();
                while (rs.next()) {
                    lista.add(montarDadosBasico(rs));
                }
            }

        }
        return lista;
    }

    public Integer existeHorarioTurma(int codigo, HorarioTurmaVO ht) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select ht.codigo from horarioTurma ht inner join turma t on ht.turma = t.codigo  ")
                .append("where ht.ambiente = ? and ht.professor = ? and ht.nivelturma = ?")
        .append(" and ht.horainicial = ? and ht.horafinal = ? and ht.diasemananumero = ? and t.empresa = ? and ht.situacao = 'AT' and t.aulacoletiva = false ");

        try (PreparedStatement st = con.prepareStatement(sql.toString())) {
            st.setInt(1, ht.getAmbiente().getCodigo());
            st.setInt(2, ht.getProfessor().getCodigo());
            st.setInt(3, ht.getNivelTurma().getCodigo());
            st.setString(4, ht.getHoraInicial());
            st.setString(5, ht.getHoraFinal());
            st.setInt(6, ht.getDiaSemanaNumero());
            st.setInt(7, codigo);

            try (ResultSet rs = st.executeQuery()) {

                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        }

        return 0;
    }

    public List<HorarioTurmaVO> consultar(Integer codigoContrato, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ht.* \n");
        sql.append("FROM contratomodalidadeturma cmt \n");
        sql.append("inner join contratomodalidadehorarioturma cmht on  cmht.contratomodalidadeturma = cmt.codigo \n");
        sql.append("INNER JOIN contratomodalidade cm ON cmt.contratomodalidade = cm.codigo \n");
        sql.append("inner join horarioTurma ht on ht.codigo = cmht.horarioTurma \n");
        sql.append("WHERE cm.contrato = ").append(codigoContrato);
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public JSONArray consultarTodasHoraInicial() throws Exception {
        JSONArray jsonArray;
        try (PreparedStatement sql = con.prepareStatement("select distinct horainicial from horarioturma order by horainicial")) {
            try (ResultSet rs = sql.executeQuery()) {
                jsonArray = new JSONArray();
                while (rs.next()) {
                    jsonArray.put(rs.getString("horainicial"));
                }
            }
        }
        return jsonArray;
    }
    
    public int nrMatriculasFuturas(HorarioTurmaVO obj, Date dataConsulta) throws Exception{
        if (dataConsulta == null) {
            dataConsulta = Calendario.hoje();
        }
        String sqlStr = "select count(distinct pessoa) as qtde, array_agg(contrato) as contratos from matriculaalunohorarioturma  where "
                + " horarioturma = " + obj.getCodigo() + " and datainicio > '"
                + Uteis.getSQLData(dataConsulta)
                + "' and pessoa not in (select mth.pessoa from matriculaalunohorarioturma mth where mth.horarioturma = " + obj.getCodigo() + " and '"
                + Uteis.getSQLData(dataConsulta)
                + "' BETWEEN mth.datainicio AND mth.datafim)";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    int qtdMatriculasFuturas = tabelaResultado.getInt("qtde");
                    if (qtdMatriculasFuturas > 0) {
                        obj.setNrMatriculasFuturas(qtdMatriculasFuturas);
                        if (obj.getNrAlunoMatriculado() + obj.getNrMatriculasFuturas() >= obj.getNrMaximoAluno()) {
                            String msg = tabelaResultado.getString("contratos").replace("{", "").replace("}", "").replace(",", ", ");
                            if (msg.length() > 25) {
                                msg = msg.substring(0, 25);
                                msg = msg.substring(0, msg.lastIndexOf(",")) + "...";
                            }
                            obj.setMsgMatriculasFuturas("Horário com " + tabelaResultado.getInt("qtde") + " matrícula(s) futura(s), podendo execeder o limite. Contrato(s) com matricula(s) futura(s): " + msg);
                        }
                    }
                } else {
                    obj.setNrMatriculasFuturas(0);
                    obj.setMsgMatriculasFuturas("");
                }
            }
        }
        return 0;
    }

    public String horarioTurmaSimplificado(Integer contrato) throws Exception{
        StringBuffer  sql = new StringBuffer();
        sql.append("SELECT t.descricao as turma, h.diasemana, a.descricao as ambiente, horainicial, horafinal, ");
        sql.append("        m.nome as modalidade, pc.nome as professor FROM matriculaalunohorarioturma ma ");
        sql.append("        inner join horarioturma h on h.codigo = ma.horarioturma ");
        sql.append("        inner join turma t on t.codigo = h.turma ");
        sql.append("        inner join ambiente a on h.ambiente = a.codigo ");
        sql.append("        inner join modalidade m on m.codigo = t.modalidade ");
        sql.append("        inner join colaborador c on h.professor = c.codigo ");
        sql.append("        inner join pessoa pc on c.pessoa = pc.codigo ");
        sql.append("        WHERE contrato = ? ");
        sql.append("        and ? between datainicio and datafim");
        JSONArray array;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setInt(1, contrato);
            stm.setDate(2, Uteis.getDataJDBC(Calendario.hoje()));
            array = new JSONArray();
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    JSONObject json = new JSONObject();
                    json.put("turma", rs.getString("turma"));
                    json.put("diasemana", rs.getString("diasemana"));
                    json.put("ambiente", rs.getString("ambiente"));
                    json.put("horainicial", rs.getString("horainicial"));
                    json.put("horafinal", rs.getString("horafinal"));
                    json.put("modalidade", rs.getString("modalidade"));
                    json.put("professor", rs.getString("professor"));
                    array.put(json);
                }
            }
        }
        return array.toString();

    }

    public HorarioTurmaTO processaFrequenciaPorHorario(HorarioTurmaVO horario, Date dataInicio, Date dataTermino, boolean exibirReposicoes) throws Exception {
        Date iniAux;
        Date fimAux;
        int qtdePresencasNoPeriodo = 0;
        double totalFrequenciasAlunos = 0.0;
        HorarioTurmaTO horarioRetorno = new HorarioTurmaTO();
        List<ReposicaoVO> reposicoes = new ArrayList<ReposicaoVO>();
        // busca os alunos do horario
        MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurmaDao = new MatriculaAlunoHorarioTurma(con);
        List<ConsultarAlunosTurmaVO> listaAlunos = matriculaAlunoHorarioTurmaDao.consultarPorHorarioTurmaPeriodo(horario.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, dataInicio, dataTermino, true, exibirReposicoes);
        // percorre a lista de alunos do horario
        for (ConsultarAlunosTurmaVO aluno : listaAlunos) {
            // pega o contrato vigente do aluno
            // estabelece o periodo de acordo com o contrato do aluno
            if (dataInicio.after(aluno.getMatriculaAlunoHorarioTurmaVO().getDataInicio())) {
                iniAux = Calendario.getDataComHoraZerada(dataInicio);
            } else {
                iniAux = Calendario.getDataComHoraZerada(aluno.getMatriculaAlunoHorarioTurmaVO().getDataInicio());
            }
            // estabelece o periodo de acordo com o contrato do aluno
            if (dataTermino.after(aluno.getMatriculaAlunoHorarioTurmaVO().getDataFim())) {
                fimAux = Calendario.getDataComHoraZerada(aluno.getMatriculaAlunoHorarioTurmaVO().getDataFim());
            } else {
                fimAux = Calendario.getDataComHoraZerada(dataTermino);
            }
            // verifica quantas presencas o aluno teve no periodo
            Presenca presencaDao = new Presenca(con);
            qtdePresencasNoPeriodo = presencaDao.contarPorPessoaPeriodoPresenca(aluno.getClienteVO().getPessoa().getCodigo(), horario.getCodigo(), iniAux, fimAux);
            Reposicao reposicaoDao = new Reposicao(con);
            qtdePresencasNoPeriodo += reposicaoDao.contarReposicoesDoAluno(aluno.getClienteVO().getCodigo(), horario, Calendario.periodoSQL(iniAux, fimAux), true);

            if (exibirReposicoes) {
                //verifica as quantidades de reposições e desmarcações de aulas
                aluno.setQtdeReposicoes(getFacade().getReposicao().contarReposicoesDoAluno(
                        aluno.getClienteVO().getCodigo(), horario,
                        Calendario.periodoSQL(iniAux, fimAux), false));

                aluno.setQtdeDesmarcacoes(getFacade().getReposicao().contarDesmarcacoesDoAluno(aluno.getClienteVO().getCodigo(), horario, Calendario.periodoSQL(iniAux, fimAux)));
            }

            // percorre o periodo verificando a quantidade de aulas que o aluno faria
            int qtdeAulasPeriodo = 0;
            qtdeAulasPeriodo = calcularQtdAulas(horario, qtdeAulasPeriodo, iniAux, fimAux);

            aluno.setQtdeAulasPeriodo(qtdeAulasPeriodo);

            if (exibirReposicoes) {
                if (aluno.getMatriculaAlunoHorarioTurmaVO().getCodigo() == 0) {//reposição
                    aluno.setQtdeAulasPeriodo(aluno.getQtdeReposicoes());
                    horario.setNrAlunoEntraramPorReposicao(horario.getNrAlunoEntraramPorReposicao() + aluno.getQtdeReposicoes());
                } else {
                    aluno.setQtdeAulasPeriodo(qtdeAulasPeriodo - aluno.getQtdeDesmarcacoes());
                    horario.setNrAlunoSairamPorReposicao(horario.getNrAlunoSairamPorReposicao() + aluno.getQtdeDesmarcacoes());
                }
            }

            aluno.setQtdePresencasPeriodo(qtdePresencasNoPeriodo);

            if (aluno.getQtdeAulasPeriodo() > 0) {
                aluno.setFrequenciaAluno(Uteis.arredondarForcando2CasasDecimais(Integer.valueOf(qtdePresencasNoPeriodo).doubleValue() / (aluno.getQtdeAulasPeriodo()) * 100));
            }
            totalFrequenciasAlunos += aluno.getFrequenciaAluno();
            horarioRetorno.setQtdPrevisto(horarioRetorno.getQtdPrevisto() + aluno.getQtdeAulasPeriodo());
            horarioRetorno.setQtdPresencas(horarioRetorno.getQtdPresencas() + aluno.getQtdePresencasPeriodo());
        }
        List<ConsultarAlunosTurmaVO> listaAlunosSemReposicao = new ArrayList<ConsultarAlunosTurmaVO>();
        for (ConsultarAlunosTurmaVO aluno : listaAlunos) {
            if (aluno.getMatriculaAlunoHorarioTurmaVO().getReposicao() == null) {
                listaAlunosSemReposicao.add(aluno);
            }
        }

        // testa o tamanho da lista para nao realizar uma divisao por zero
        if (listaAlunosSemReposicao.size() > 0) {
            if (horarioRetorno.getQtdPrevisto() != 0) {
                horario.setFreqMedia(Uteis.arredondarForcando2CasasDecimais(((double) horarioRetorno.getQtdPresencas() / horarioRetorno.getQtdPrevisto()) * 100));
            }
            if (horario.getNrMaximoAluno() != 0) {
                horario.setTxOcupacao(Uteis.arredondarForcando2CasasDecimais(Integer.valueOf(listaAlunosSemReposicao.size()).doubleValue() / horario.getNrMaximoAluno() * 100));
            }
        }
        horarioRetorno.setHorario(horario);
        horarioRetorno.setListaAlunos(listaAlunosSemReposicao);
        if (exibirReposicoes) {
            //busca as reposições previstas para este horário no período especificado
            reposicoes.addAll(getFacade().getReposicao().consultarReposicoesPorHorarioTurma(horario.getCodigo(), true, Calendario.periodoSQL(dataInicio, dataTermino), Uteis.NIVELMONTARDADOS_TODOS, null));
            reposicoes.addAll(getFacade().getReposicao().consultarReposicoesPorHorarioTurma(horario.getCodigo(), false, Calendario.periodoSQL(dataInicio, dataTermino), Uteis.NIVELMONTARDADOS_TODOS, null));
            horarioRetorno.setListaReposicoes(reposicoes);
        }
        return horarioRetorno;
    }

    public List<ClienteHorarioTurmaDescontoTO> processaDescontoHorario(Integer horario, Date dataInicio, Date dataTermino) throws Exception{

        List<ClienteHorarioTurmaDescontoTO> clienteHorarioTurmaDescontoTOS = new ArrayList<ClienteHorarioTurmaDescontoTO>();
        StringBuilder sql = new StringBuilder();
        sql.append("select e.nome cliente,  a.percOcupacao,  a.percDesconto, c.contrato, d.datalancamento ").append("\n");
        sql.append("from ContratoModalidadeHorarioTurma a inner join ").append("\n");
        sql.append("     ContratoModalidadeTurma b on a.contratomodalidadeturma = b.codigo inner join ").append("\n");
        sql.append("     ContratoModalidade c on b.contratomodalidade = c.codigo inner join ").append("\n");
        sql.append("     Contrato d on c.contrato = d.codigo inner join ").append("\n");
        sql.append("     Pessoa e on d.pessoa = e.codigo ").append("\n");
        sql.append("where a.horarioturma = ? AND d.datalancamento between ? and ? ");

        try (PreparedStatement stmt = con.prepareStatement(sql.toString())) {
            stmt.setInt(1, horario);
            stmt.setDate(2, new java.sql.Date(dataInicio.getTime()));
            stmt.setDate(3, new java.sql.Date(dataTermino.getTime()));

            try (ResultSet rs = stmt.executeQuery()) {

                while (rs.next()) {

                    ClienteHorarioTurmaDescontoTO to = new ClienteHorarioTurmaDescontoTO();
                    to.setAluno(rs.getString("cliente"));
                    to.setContrato(rs.getInt("contrato"));
                    to.setDataContrato(rs.getDate("datalancamento"));
                    to.setPercDesconto(rs.getDouble("percDesconto"));
                    to.setPercOcupacao(rs.getDouble("percOcupacao"));

                    clienteHorarioTurmaDescontoTOS.add(to);

                }
            }
        }

        return clienteHorarioTurmaDescontoTOS;
    }

    public int calcularQtdAulas(HorarioTurmaVO horario, int qtdeAulasPeriodo, Date iniAux, Date fimAux) {
        while (Calendario.menorOuIgual(iniAux, fimAux)) {
            String diaSemana = Uteis.obterDiaSemanaData(iniAux);
            if (horario.getDiaSemana().equals(diaSemana)) {
                qtdeAulasPeriodo++;
            }
            iniAux = Uteis.obterDataFutura2(iniAux, 1);
        }
        return qtdeAulasPeriodo;
    }

    @Override
    public JSONArray consultarAulaPeloClienteHorario(String matricula, Date data, String horaInicial, String horaFinal) throws Exception {
        if(!UteisValidacao.emptyString(horaInicial)) {
            horaInicial = UteisValidacao.adicionarUmMinutoNaHora(horaInicial);
        }
        Cliente cliente = new Cliente(con);
        ClienteVO clienteVO = cliente.consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (clienteVO == null) {
            throw new Exception("O cliente com a matricula [" + matricula + "] não foi localizado no ZillyonWeb!");
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ht.horainicial, ht.horafinal, ht.identificadorturma from AlunoHorarioTurma aht ").append("\n");
        sql.append("inner join horarioturma ht on ht.codigo = aht.horarioturma ").append("\n");
        sql.append("inner join turma tu on tu.codigo = ht.turma ").append("\n");
        sql.append("where aht.cliente = ").append(clienteVO.getCodigo()).append("\n");
        sql.append("and (cast(aht.dia as date) = '").append(Uteis.getDataJDBC(data)).append("') \n");
        sql.append("and (('"+horaInicial+"' between horainicial and horafinal) OR ('"+horaFinal+"' between horainicial and horafinal) OR ('"+horaInicial+"' > horainicial and '"+horaFinal+"' < horafinal))").append("\n");
        sql.append("and ht.situacao = 'AT' ").append("\n");
        sql.append("and tu.aulacoletiva ");

        JSONArray jsonArray;
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                jsonArray = new JSONArray();
                while (rs.next()) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("identificadorturma", rs.getString("identificadorturma")+ " - ("+rs.getString("horainicial")+" - "+rs.getString("horafinal")+")");
                    jsonArray.put(jsonObject);
                }
            }
        }

        return jsonArray;
    }

    public Integer modalidadeDoHorario(Integer horarioTurma) throws Exception{
        String sql = "select modalidade from horarioturma ht\n" +
                "inner join turma t on ht.turma = t.codigo \n" +
                "where ht.codigo = " + horarioTurma;
        ResultSet rs = criarConsulta(sql, con);
        return rs.next() ? rs.getInt("modalidade") : null;
    }

    public boolean temDiaria(Integer codigoCliente, Integer codigoModalidade, Date data) throws Exception{
        Integer produtoDiaria = produtoDiaria(codigoCliente, codigoModalidade, data);
        return produtoDiaria != null;
    }
    public Integer produtoDiaria(Integer codigoCliente, Integer codigoModalidade, Date data) throws Exception{
        StringBuilder sb = new StringBuilder();
        sb.append("select aad.produto from aulaavulsadiaria aad inner join \n")
                .append("periodoacessocliente pac on aad.codigo = pac.aulaavulsadiaria \n")
                .append("where aad.modalidade = ").append(codigoModalidade)
                .append(" and aad.cliente = ").append(codigoCliente)
                .append(" and '").append(Uteis.getDataAplicandoFormatacao(data, "yyyy-MM-dd"))
                .append("' between pac.datainicioacesso and pac.datafinalacesso");

        try (Statement st = con.createStatement()) {
            ResultSet rs = st.executeQuery(sb.toString());
            return rs.next() ? rs.getInt("produto") : null;
        }
    }

    private String sqlCheckAulasPeriodoContabilizadoPorDia(int modalidade, int cliente,
                                        Date dataInicio, Date datafinal, boolean contabilizarAulaPorDia, boolean desmarcadas){

        return "select count("+(contabilizarAulaPorDia ? "distinct " : "")+"au.codigo) as qtde from " +
                (desmarcadas ? "alunohorarioturmadesmarcado" : "alunohorarioturma") +
                " au inner join horarioturma ht on au.horarioturma = ht.codigo \n"
                + " inner join turma t on t.codigo = ht.turma \n"
                + " where  " + (desmarcadas ? " au.reposicao is true and " : "")
                + " au.cliente = " + cliente + " and t.modalidade = "+ modalidade
                + " and (t.datafinalvigencia is null or t.datafinalvigencia >=  au.dia) \n"
                + " and au.dia::date between '" + Uteis.getDataJDBC(dataInicio) + "' and '" + Uteis.getDataJDBC(datafinal) + "' "
                + (desmarcadas ? "" : " and aulaexperimental is false ");
    }

    public Integer obterModalidadeAulaAvulsaDiaria(Integer codigoCliente, Date data) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("select aad.modalidade from aulaavulsadiaria aad inner join \n")
                .append("periodoacessocliente pac on aad.codigo = pac.aulaavulsadiaria \n")
                .append("where aad.cliente = ").append(codigoCliente)
                .append(" and '").append(Uteis.getDataAplicandoFormatacao(data, "yyyy-MM-dd"))
                .append("' between pac.datainicioacesso and pac.datafinalacesso");

        try (Statement st = con.createStatement()) {
            ResultSet rs = st.executeQuery(sb.toString());
            return rs.next() ? rs.getInt("modalidade") : null;
        }
    }

    public int nrAulasColetivasMarcadasAlunoPorModalidadePeriodoContabilizadoPorDia(int modalidade, int cliente,
                                                                 Date dataInicio, Date datafinal, boolean contabilizarAulaPorDia) throws Exception {
        int total;
        try (Statement stm = con.createStatement()) {
            String sqlStr = sqlCheckAulasPeriodoContabilizadoPorDia(modalidade, cliente, dataInicio, datafinal, contabilizarAulaPorDia, false);
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                total = tabelaResultado.next() ? tabelaResultado.getInt("qtde") : 0;
            }
            sqlStr = sqlCheckAulasPeriodoContabilizadoPorDia(modalidade, cliente, dataInicio, datafinal, contabilizarAulaPorDia, true);
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                total += tabelaResultado.next() ? tabelaResultado.getInt("qtde") : 0;
            }
        }
        return total;
    }

    private String sqlCheckAulasPeriodo(int modalidade, Date vigenciaContrato, int cliente,
                                        Date dataAula,
                                        Date dataInicio, Date datafinal, boolean vezessemana, boolean desmarcadas){
        return "select count("+(vezessemana ? "distinct " : "")+"au.dia) as qtde from alunohorarioturma" +
                (desmarcadas ? "desmarcado" : "") +
                " au " +
                "inner join horarioturma ht on au.horarioturma = ht.codigo \n"
                + " inner join turma t on t.codigo = ht.turma \n"
                + " where " + (desmarcadas ? " au.reposicao is true and " : "")
                + " au.cliente = " + cliente + " and t.modalidade = "+ modalidade
                + " and (t.datafinalvigencia is null or t.datafinalvigencia >=  au.dia) \n"
                + " and au.dia::date between '" +
                Uteis.getDataJDBC((vigenciaContrato != null && Calendario.maiorOuIgual(vigenciaContrato, dataInicio) && Calendario.menorOuIgual(vigenciaContrato, datafinal)) ? vigenciaContrato : dataInicio)
                + "' and '" + Uteis.getDataJDBC(datafinal) + "' "
                + (dataAula == null ? "" : (" and au.dia::date <> '" + Uteis.getDataJDBC(dataAula) + "'\n ")) + ""
                + (desmarcadas ? "" : " and aulaexperimental is false ");
    }

    @Override
    public int nrAulasColetivasMarcadasAlunoPorModalidadePeriodo(int modalidade, Date vigenciaContrato, int cliente,
                                                                 Date dataAula,
                                                                 Date dataInicio, Date datafinal, boolean vezessemana) throws Exception {
        int total;
        try (Statement stm = con.createStatement()) {
            String sqlStr = sqlCheckAulasPeriodo(modalidade, vigenciaContrato, cliente, dataAula, dataInicio, datafinal, vezessemana, false);
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                total = tabelaResultado.next() ? tabelaResultado.getInt("qtde") : 0;
            }
            int limiteReposicaoAulaColetivaCliente = limiteReposicaoAulaColetivaCliente(cliente);
            if(limiteReposicaoAulaColetivaCliente > 0){
                sqlStr = sqlCheckAulasPeriodo(modalidade, vigenciaContrato, cliente, dataAula, dataInicio, datafinal, vezessemana, true);
                try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                    Integer totalHorarioDesmarcado = tabelaResultado.next() ? tabelaResultado.getInt("qtde") : 0;
                    if(limiteReposicaoAulaColetivaCliente <= totalHorarioDesmarcado){
                        total += totalHorarioDesmarcado;
                    }
                }
            }
        }
        return total;
    }

    @Override
    public int nrAulasColetivasMarcadasAlunoPorPeriodo(int cliente, Date dataAula, Date dataInicio, Date datafinal) throws Exception {
        String sqlStr = "select count(au.dia) as qtde from alunohorarioturma au inner join horarioturma ht on au.horarioturma = ht.codigo \n"
                + " inner join turma t on t.codigo = ht.turma \n"
                + " where  au.cliente = " + cliente
                + " and (t.datafinalvigencia is null or t.datafinalvigencia >=  au.dia) \n"
                + " and au.dia::date between '" + Uteis.getDataJDBC(dataInicio) + "' and '" + Uteis.getDataJDBC(datafinal) + "' "
                + (dataAula == null ? "" : (" and au.dia::date <> '" + Uteis.getDataJDBC(dataAula) + "'\n ")) + ""
                + " and aulaexperimental is false " ;

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("qtde");
                } else {
                    return 0;
                }
            }
        }
    }

    public void alterarHorariosGestaoTurma(HorarioTurmaVO obj) throws Exception {
            alterar(obj);
    }

    public List<HorarioTurmaVO> consultarPorTurma(TurmaVO turma, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM horarioturma WHERE turma = " + turma.getCodigo();
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql)) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<HorarioTurmaVO> consultarPorModalidade(ModalidadeVO modalidadeVO, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM horarioturma ht " +
                "INNER JOIN turma t ON ht.turma = t.codigo " +
                "WHERE modalidade = " + modalidadeVO.getCodigo();
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql)) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<HorarioTurmaVO> consultarPorModalidadeProfessorDiaColetivas(int modalidade, int professor, Date dia, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM horarioturma ht " +
                "INNER JOIN turma t ON ht.turma = t.codigo " +
                "WHERE modalidade = " + modalidade + " \n" +
                "AND t.aulacoletiva \n" +
                "AND ht.professor = "+ professor + " \n" +
                "AND ht.situacao = 'AT' \n" +
                "AND ht.ativo = true \n" +
                "AND ht.diaSemanaNumero = "+ Uteis.diaDaSemana(dia) + " \n" +
                "AND '"+Uteis.getDataFormatoBD(dia)+"' between t.datainicialvigencia and t.datafinalvigencia";
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql)) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<AlunoHorarioTurmaVO> consultarAulasColetivasAluno(Date dataAula, Integer codigoCliente, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select *  \n");
        sql.append("from alunohorarioturma  \n");
        sql.append("where cliente = ? \n");
        sql.append("  and dia =  ? ");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            int i = 1;
            pst.setInt(i++, codigoCliente);
            pst.setDate(i++, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataAula)));
            try (ResultSet rs = pst.executeQuery()) {
                return montarDadosConsultaAlunoHorarioTurma(rs, nivelMontarDados);
            }
        }
    }
    
     public List<AlunoHorarioTurmaVO> montarDadosConsultaAlunoHorarioTurma(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<AlunoHorarioTurmaVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            AlunoHorarioTurmaVO obj = montarDadosAlunoHorarioTurma(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }
     
    public AlunoHorarioTurmaVO montarDadosAlunoHorarioTurma(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        AlunoHorarioTurmaVO obj = montarDadosBasicoAlunoHorarioTurma(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA ||nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        obj.setHorarioTurma(consultarPorChavePrimaria(obj.getHorarioTurma().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        return obj;
    }
     
    private AlunoHorarioTurmaVO montarDadosBasicoAlunoHorarioTurma(ResultSet dadosSQL) throws Exception {
        AlunoHorarioTurmaVO obj = new AlunoHorarioTurmaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getHorarioTurma().setCodigo(dadosSQL.getInt("horarioturma"));
        obj.setCliente(dadosSQL.getInt("cliente"));
        obj.setData(dadosSQL.getDate("dia"));
        obj.setExperimental(dadosSQL.getBoolean("aulaexperimental"));
        obj.setDesafio(dadosSQL.getBoolean("desafio"));
        return obj;
    }

    public AlunoHorarioTurmaVO montarDadosBasicoAlunoHorarioTurmaUltimaDataComAgendamento(Integer codigoTurma, Date inicioVigencia, Date fimVigencia) throws Exception {
        AlunoHorarioTurmaVO obj = new AlunoHorarioTurmaVO();
        StringBuilder sql = new StringBuilder();
        sql.append("select aula.* \n");
        sql.append("from alunohorarioturma aula \n");
        sql.append("inner join horarioturma ht on ht.codigo = aula.horarioturma \n");
        sql.append("where ht.turma = ? \n");
        sql.append("and (aula.dia::date >= ? and aula.dia::date <= ?) \n");
        sql.append("order by dia asc limit 1 ");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            int i = 1;
            pst.setInt(i++, codigoTurma);
            pst.setDate(i++, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(Calendario.hoje())));
            pst.setDate(i++, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(fimVigencia)));
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    obj =montarDadosAlunoHorarioTurma(rs, Uteis.NIVELMONTARDADOS_TODOS);
                    if(Calendario.maior(Calendario.getDataComHoraZerada(inicioVigencia), Calendario.getDataComHoraZerada(obj.getData()))){
                        throw new ConsistirException("Existe aula marcada no dia "+ Uteis.getData(obj.getData(), "dd/MM/yyyy")+" às " +obj.getHorarioTurma().getHoraInicial()+" para essa aula. Portanto, a data de vigência não pode ser alterada.");
                    }
                }
            }
        } catch (Exception e) {
            throw  e;
        }
        return obj;
    }

    public String alunoAgendadoHorarioDia(Date dia, Date agora, Integer codCliente, Integer empresa, boolean validacaoOffline) throws Exception {
        StringBuilder sql = new StringBuilder();
        try (ResultSet result = criarConsulta("SELECT toleranciaacessoaula FROM empresa WHERE codigo = " + empresa, con)) {
            String hhMMss = " " + Uteis.gethoraHHMMSSFormatado(agora);
            Integer toleranciaAcessoAula = 0;
            if (result.next()) {
                toleranciaAcessoAula = result.getInt("toleranciaacessoaula");
                toleranciaAcessoAula = toleranciaAcessoAula == null ? 0 : toleranciaAcessoAula;
            }
            sql.append("SELECT\n")
                    .append("a.codigo, h.horainicial, h.horafinal, h.toleranciaentradaminutos, h.toleranciaentradaaposminutos \n")
                    .append("FROM alunohorarioturma a \n")
                    .append("inner join horarioturma h on h.codigo = a.horarioturma \n")
                    .append("inner join turma t on t.codigo = h.turma \n")
                    .append("where a.cliente = ").append(codCliente).append(" \n")
                    .append("and a.dia::date = '").append(Uteis.getDataFormatoBD(dia)).append("' \n");
            if (!validacaoOffline) {
                sql.append("AND (('").append(Uteis.getDataJDBC(agora)).append("' || ' ' || h.horainicial::time)::TIMESTAMP - INTERVAL '")
                        .append(toleranciaAcessoAula).append(" minutes') <= '").append(Uteis.getDataJDBC(agora)).append(hhMMss).append("'\n")
                        .append("AND '").append(Uteis.getDataJDBC(agora)).append(hhMMss).append("' <= (('")
                        .append(Uteis.getDataJDBC(agora)).append("' || ' ' || h.horafinal::time)::TIMESTAMP + INTERVAL '10 minutes')\n");
            }
            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append("and t.empresa = ").append(empresa).append(" \n");
            }

            sql.append("UNION\n")
                    .append("SELECT\n")
                    .append("rp.codigo, h.horainicial, h.horafinal, h.toleranciaentradaminutos, h.toleranciaentradaaposminutos \n")
                    .append("from reposicao rp\n")
                    .append("inner join horarioturma h on h.codigo = rp.horarioturma\n")
                    .append("inner join turma t on t.codigo = rp.turmadestino\n")
                    .append("where rp.cliente = ").append(codCliente).append(" \n")
                    .append("and rp.datareposicao::date = '").append(Uteis.getDataFormatoBD(dia)).append("'\n");
            if (!validacaoOffline) {
                sql.append("AND (('").append(Uteis.getDataJDBC(agora)).append("' || ' ' || h.horainicial::time)::TIMESTAMP - INTERVAL '")
                        .append(toleranciaAcessoAula).append(" minutes') <= '").append(Uteis.getDataJDBC(agora)).append(hhMMss).append("'\n")
                        .append("AND '").append(Uteis.getDataJDBC(agora)).append(hhMMss).append("' <= (('")
                        .append(Uteis.getDataJDBC(agora)).append("' || ' ' || h.horafinal::time)::TIMESTAMP + INTERVAL '10 minutes')\n");
            }
            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append("and t.empresa = ").append(empresa).append(" \n");
            }

            StringBuilder horarios = new StringBuilder();
            try (Statement stm = con.createStatement();
                 ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    HorarioTurmaVO horario = new HorarioTurmaVO();
                    horario.setHoraInicial(rs.getString("horainicial"));
                    horario.setHoraFinal(rs.getString("horafinal"));
                    horario.setToleranciaEntradaMinutos(rs.getInt("toleranciaentradaminutos"));
                    horario.setToleranciaEntradaAposMinutos(rs.getInt("toleranciaentradaaposminutos"));

                    horarios.append(horario.getHoraInicialComTolerancia()).append("-").append(horario.getHoraFinal()).append("_");
                }
            }

            horarios.append(validarAulaEmOutraUnidade(agora, codCliente));

            return horarios.toString();
        }
    }

    private String validarAulaEmOutraUnidade(Date agora, Integer cliente) throws Exception {
        StringBuilder horarios = new StringBuilder();
        try {
            StringBuilder sql = new StringBuilder("select inicio, fim from aulaoutraunidade a \n");
            sql.append(" inner join cliente cli on cli.pessoa = a.pessoa \n");
            sql.append(" where cli.codigo = ").append(cliente).append(" \n");
            sql.append(" and (inicio - INTERVAL '15 minutes') <= '").append(Uteis.getDataJDBCTimestamp(agora)).append("' \n");
            sql.append(" and '").append(Uteis.getDataJDBCTimestamp(agora)).append("' <= (fim + INTERVAL '10 minutes') ");
            try (Statement stm = con.createStatement();
                 ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    horarios.append(Calendario.getDataAplicandoFormatacao(Uteis.somarCampoData(rs.getTimestamp("inicio"), Calendar.MINUTE, -15),
                            "HH:mm")).append("-");
                    horarios.append(Calendario.getDataAplicandoFormatacao(Uteis.somarCampoData(rs.getTimestamp("fim"), Calendar.MINUTE, 10),
                            "HH:mm"));
                    horarios.append("_");
                }
            }
        }catch (Exception e){
            Uteis.logar(e, HorarioTurma.class);
        }
        return horarios.toString();
    }

    public List<Integer> consultarClientesComAgendamentosNoHorario(Date dia, Integer codigoHorarioTurma, Integer codigoEmpresa, boolean validarFuturo) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT cliente FROM alunohorarioturma WHERE horarioturma IN (\n")
                .append("   SELECT ht.codigo FROM horarioturma ht\n")
                .append("   LEFT JOIN turma t ON ht.turma = t.codigo AND t.empresa = ").append(codigoEmpresa).append("\n")
                .append("   WHERE (SELECT horainicial FROM horarioturma WHERE codigo = ").append(codigoHorarioTurma).append(") BETWEEN horainicial AND horafinal)\n");
                if(validarFuturo){
                    sql.append(" AND dia >= '").append(Uteis.getDataJDBC(dia)).append("'");
                    sql.append(" AND horarioturma = ").append(codigoHorarioTurma);
                }else {
                    sql.append("AND dia = '").append(Uteis.getDataJDBC(dia)).append("'");
                }

        List<Integer> clientes = new ArrayList<>();
        try(Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sql.toString())) {
            while (rs.next()) {
                clientes.add(rs.getInt("cliente"));
            }
        }
        return clientes;
    }


    public int contarAulasPorPeriodoModalidade(Date prmIni, Date prmFim, Integer empresa, Integer codModalidade,TurmaVO turmaFiltroVO) throws Exception {
        String sqlStr = "SELECT distinct ht.codigo, ht.horaInicial, ht.horaFinal, ht.diaSemana, ht.diaSemanaNumero, t.dataInicialVigencia, t.datafinalvigencia\n" +
                "FROM horarioturma ht\n" +
                "INNER JOIN turma t ON ht.turma = t.codigo\n" +
                "WHERE (('" + Uteis.getDataJDBC(prmIni) + "' BETWEEN t.dataInicialVigencia and t.datafinalvigencia) \n" +
                " OR ('" + Uteis.getDataJDBC(prmFim) + "' BETWEEN t.dataInicialVigencia and t.datafinalvigencia))\n" +
                "and t.empresa = " + empresa + "\n" +
                "and t.modalidade = " + codModalidade + ";";
        if(turmaFiltroVO!=null && !UteisValidacao.emptyNumber(turmaFiltroVO.getCodigo())){
            sqlStr = sqlStr.replace(";", " and t.codigo = " + turmaFiltroVO.getCodigo() + ";");
        }

        List<TurmaVO> turmas = new ArrayList<>();
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            while (tabelaResultado.next()) {
                HorarioTurmaVO horarioTurmaVO = new HorarioTurmaVO();
                horarioTurmaVO.setHoraInicial(tabelaResultado.getString("horaInicial"));
                horarioTurmaVO.setHoraFinal(tabelaResultado.getString("horaFinal"));
                horarioTurmaVO.setDiaSemana(tabelaResultado.getString("diaSemana"));
                horarioTurmaVO.setDiaSemanaNumero(tabelaResultado.getInt("diaSemanaNumero"));

                TurmaVO turmaVO = new TurmaVO();
                turmaVO.setDataInicialVigencia(tabelaResultado.getDate("dataInicialVigencia"));
                turmaVO.setDataFinalVigencia(tabelaResultado.getDate("datafinalvigencia"));
                turmaVO.getHorarioTurmaVOs().add(horarioTurmaVO);

                turmas.add(turmaVO);
            }
        }

        int quantidadeAulasPrevistas = 0;
        Date dtTemp = new Date(prmIni.getTime());
        while (dtTemp.before(prmFim)) {
            for (TurmaVO turma : turmas) {
                if (turma.getDataInicialVigencia().before(dtTemp) && turma.getDataFinalVigencia().after(dtTemp)) {
                    HorarioTurmaVO horarioTurmaVO = turma.getHorarioTurmaVOs().get(0);
                    int diaSemanaDtTemp = Calendario.getDiaSemana(dtTemp);
                    if (horarioTurmaVO.getDiaSemanaNumero() == diaSemanaDtTemp) {
                        quantidadeAulasPrevistas++;
                    }

                }
            }
            dtTemp = Uteis.somarDias(dtTemp, 1);
        }

        return quantidadeAulasPrevistas;
    }

    public int contarVagasTurmasPorPeriodoModalidade(Date dataInicial, Date dataFinal, Integer empresa, Integer codModalidade,TurmaVO turmaSelecaoVO) throws Exception {
        String sqlStr = "SELECT distinct ht.codigo, ht.horaInicial, ht.horaFinal, ht.diaSemana, ht.diaSemanaNumero, ht.nrmaximoaluno, t.dataInicialVigencia, t.datafinalvigencia\n" +
                "FROM horarioturma ht\n" +
                "INNER JOIN turma t ON ht.turma = t.codigo\n" +
                "WHERE (('" + Uteis.getDataJDBC(dataInicial) + "' BETWEEN t.dataInicialVigencia and t.datafinalvigencia) \n" +
                " OR ('" + Uteis.getDataJDBC(dataFinal) + "' BETWEEN t.dataInicialVigencia and t.datafinalvigencia))\n" +
                "and t.empresa = " + empresa + "\n" +
                "and t.modalidade = " + codModalidade + ";";

        if(turmaSelecaoVO!=null && !UteisValidacao.emptyNumber(turmaSelecaoVO.getCodigo())){
            sqlStr = sqlStr.replace(";", " and t.codigo = " + turmaSelecaoVO.getCodigo() + ";");
        }

        List<TurmaVO> turmas = new ArrayList<>();
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            while (tabelaResultado.next()) {
                HorarioTurmaVO horarioTurmaVO = new HorarioTurmaVO();
                horarioTurmaVO.setCodigo(tabelaResultado.getInt("codigo"));
                horarioTurmaVO.setNrMaximoAluno(tabelaResultado.getInt("nrmaximoaluno"));
                horarioTurmaVO.setHoraInicial(tabelaResultado.getString("horaInicial"));
                horarioTurmaVO.setHoraFinal(tabelaResultado.getString("horaFinal"));
                horarioTurmaVO.setDiaSemana(tabelaResultado.getString("diaSemana"));
                horarioTurmaVO.setDiaSemanaNumero(tabelaResultado.getInt("diaSemanaNumero"));

                TurmaVO turmaVO = new TurmaVO();
                turmaVO.setDataInicialVigencia(tabelaResultado.getDate("dataInicialVigencia"));
                turmaVO.setDataFinalVigencia(tabelaResultado.getDate("datafinalvigencia"));
                turmaVO.getHorarioTurmaVOs().add(horarioTurmaVO);

                turmas.add(turmaVO);
            }
        }

        int quantidadeVagasHorarios = 0;
        Date dtTemp = new Date(dataInicial.getTime());
        while (dtTemp.before(dataFinal)) {
            for (TurmaVO turma : turmas) {
                if (turma.getDataInicialVigencia().before(dtTemp) && turma.getDataFinalVigencia().after(dtTemp)) {
                    HorarioTurmaVO horarioTurmaVO = turma.getHorarioTurmaVOs().get(0);
                    int diaSemanaDtTemp = Calendario.getDiaSemana(dtTemp);
                    if (horarioTurmaVO.getDiaSemanaNumero() == diaSemanaDtTemp) {
                        quantidadeVagasHorarios = quantidadeVagasHorarios + horarioTurmaVO.getNrMaximoAluno();
                    }
                }
            }
            dtTemp = Uteis.somarDias(dtTemp, 1);
        }

        return quantidadeVagasHorarios;
    }

    public int contarVagasOcupadasTurmasPorPeriodoModalidade(Date dataInicial, Date dataFinal, Integer empresa, Integer codModalidade,TurmaVO turmaSelecaoVO) throws Exception {
        String sqlStr = "SELECT distinct ht.codigo, ht.horaInicial, ht.horaFinal, ht.diaSemana, ht.diaSemanaNumero, ht.nrmaximoaluno, t.dataInicialVigencia, t.datafinalvigencia\n" +
                "FROM horarioturma ht\n" +
                "INNER JOIN turma t ON ht.turma = t.codigo\n" +
                "WHERE (('" + Uteis.getDataJDBC(dataInicial) + "' BETWEEN t.dataInicialVigencia and t.datafinalvigencia) \n" +
                " OR ('" + Uteis.getDataJDBC(dataFinal) + "' BETWEEN t.dataInicialVigencia and t.datafinalvigencia))\n" +
                "and t.empresa = " + empresa + "\n" +
                "and t.modalidade = " + codModalidade + ";";
        if(turmaSelecaoVO!=null && !UteisValidacao.emptyNumber(turmaSelecaoVO.getCodigo())){
            sqlStr = sqlStr.replace(";","and t.codigo = " + turmaSelecaoVO.getCodigo() + ";");
        }
        List<TurmaVO> turmas = new ArrayList<>();
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            while (tabelaResultado.next()) {
                HorarioTurmaVO horarioTurmaVO = new HorarioTurmaVO();
                horarioTurmaVO.setCodigo(tabelaResultado.getInt("codigo"));
                horarioTurmaVO.setNrMaximoAluno(tabelaResultado.getInt("nrmaximoaluno"));
                horarioTurmaVO.setHoraInicial(tabelaResultado.getString("horaInicial"));
                horarioTurmaVO.setHoraFinal(tabelaResultado.getString("horaFinal"));
                horarioTurmaVO.setDiaSemana(tabelaResultado.getString("diaSemana"));
                horarioTurmaVO.setDiaSemanaNumero(tabelaResultado.getInt("diaSemanaNumero"));

                TurmaVO turmaVO = new TurmaVO();
                turmaVO.setDataInicialVigencia(tabelaResultado.getDate("dataInicialVigencia"));
                turmaVO.setDataFinalVigencia(tabelaResultado.getDate("datafinalvigencia"));
                turmaVO.getHorarioTurmaVOs().add(horarioTurmaVO);

                turmas.add(turmaVO);
            }
        }

        int quantidadeVagasOcupadas = 0;
        Date dtTemp = new Date(dataInicial.getTime());
        while (dtTemp.before(dataFinal)) {
            for (TurmaVO turma : turmas) {
                if (turma.getDataInicialVigencia().before(dtTemp) && turma.getDataFinalVigencia().after(dtTemp)) {
                    HorarioTurmaVO horarioTurmaVO = turma.getHorarioTurmaVOs().get(0);
                    int diaSemanaDtTemp = Calendario.getDiaSemana(dtTemp);
                    if (horarioTurmaVO.getDiaSemanaNumero() == diaSemanaDtTemp) {
                        MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurmaDao = new MatriculaAlunoHorarioTurma(con);
                        quantidadeVagasOcupadas += matriculaAlunoHorarioTurmaDao.consultarPorHorarioTurmaPorPeriodoCount(horarioTurmaVO.getCodigo(), dtTemp, dtTemp, false);
                    }
                }
            }
            dtTemp = Uteis.somarDias(dtTemp, 1);
        }
        return quantidadeVagasOcupadas;
    }

    @Override
    public void incluirAlunoAulaCheiaBooking(Integer codigo, String booking ) throws Exception {
        String sql = "UPDATE AlunoHorarioTurma SET bookingid = ? where codigo = ?";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setString(++i, booking);
            sqlInserir.setInt(++i, codigo);
            sqlInserir.execute();
        }
    }

    public String modalidadeHorario(Integer codigoHorarioTurma){
        try {
            ResultSet resultSet = criarConsulta("select m.nome from horarioturma ht" +
                    " inner join turma t on ht.turma = t.codigo and ht.codigo = " + codigoHorarioTurma +
                    " inner join modalidade m on t.modalidade = m.codigo", con);
            return resultSet.next() ? resultSet.getString("nome") : "";
        }catch (Exception e){
            Uteis.logar(e, HorarioTurma.class);
        }
        return "";
    }

    public String tipoModalidadeHorario(Integer codigoHorarioTurma){
        try {
            ResultSet resultSet = criarConsulta("select tm.nome from horarioturma ht" +
                    " inner join turma t on ht.turma = t.codigo and ht.codigo = " + codigoHorarioTurma +
                    " inner join modalidade m on t.modalidade = m.codigo" +
                    " inner join tipomodalidade tm on tm.codigo = m.tipo", con);
            return resultSet.next() ? resultSet.getString("nome") : "";
        }catch (Exception e){
            Uteis.logar(e, HorarioTurma.class);
        }
        return "";
    }

    private int nrAlunosMatriculadosFuturo(HorarioTurmaVO obj, String tipoCategoriaCliente, boolean capacidadePorCategoria) throws Exception {
        Date dataConsulta = Calendario.hoje();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(mt.codigo) AS qtde FROM horarioturma ht ");
        sql.append("LEFT OUTER JOIN matriculaalunohorarioturma mt ON ht.codigo = mt.horarioturma ");
        if (capacidadePorCategoria) {
            sql.append("LEFT OUTER JOIN cliente clim on clim.pessoa = mt.pessoa ");
            sql.append("LEFT OUTER JOIN categoria cat on cat.codigo = clim.categoria ");
        }
        sql.append("WHERE mt.horarioturma = ").append(obj.getCodigo()).append(" AND '");
        sql.append(Uteis.getSQLData(dataConsulta));
        sql.append("' < mt.datainicio ");
        if (capacidadePorCategoria) {
            sql.append(" AND cat.tipocategoria = 'CO' \n");
        }
        sql.append(" AND mt.pessoa not in (");
        sql.append(" SELECT mtp.pessoa FROM horarioturma htp ");
        sql.append("inner JOIN matriculaalunohorarioturma mtp ON htp.codigo = mtp.horarioturma ");
        if (capacidadePorCategoria) {
            sql.append(" inner join cliente clim on clim.pessoa = mtp.pessoa \n");
            sql.append(" inner join categoria cat on cat.codigo = clim.categoria \n");
        }
        sql.append("WHERE mtp.horarioturma = ").append(obj.getCodigo());
        if (capacidadePorCategoria) {
            sql.append(" AND cat.tipocategoria = 'CO' \n");
        }
        sql.append(" AND '");
        sql.append(Uteis.getSQLData(dataConsulta));
        sql.append("' BETWEEN mtp.datainicio AND mtp.datafim ");
        sql.append(")");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("qtde");
                } else {
                    throw new ConsistirException("Dados Não Encontrados ( HorarioTurma ).");
                }
            }
        }
    }

    public Integer getPessoaOperacao() {
        return pessoaOperacao;
    }

    public void setPessoaOperacao(Integer pessoaOperacao) {
        this.pessoaOperacao = pessoaOperacao;
    }

    public boolean apenasAulaColetiva(Integer[] codigosHorarios) throws Exception{
        if(codigosHorarios.length == 0){
            return false;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) as qtde from horarioturma ht inner join turma t on ht.turma = t.codigo where ht.codigo in (");
        for (int i = 0; i < codigosHorarios.length; i++) {
            sql.append(codigosHorarios[i]);
            if(i < codigosHorarios.length - 1){
                sql.append(",");
            }
        }
        sql.append(") and t.aulacoletiva is false");
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sql.toString())) {
            return !rs.next() || rs.getInt("qtde") == 0;
        }
    }
    @Override
    public List<AlunoHorarioTurmaVO> consultarHorarioTurmasCancelamentoContrato(Integer codigoCliente, Integer codigoEmpresa, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select aht.*  \n");
        sql.append("from alunohorarioturma aht \n");
        sql.append("INNER JOIN horarioturma ht ON ht.codigo = aht.horarioturma  \n");
        sql.append("INNER JOIN turma t ON t.codigo = ht.turma  \n");
        sql.append("where aht.dia >=  NOW()::date  \n");
        sql.append(" AND (t.empresa = "+codigoEmpresa+" OR t.empresa IS NULL) and aht.cliente = "+codigoCliente );

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                return montarDadosConsultaAlunoHorarioTurma(rs, nivelMontarDados);
            }
        }
    }
    @Override
    public void excluirAlunoHorarioturma(Integer codigoAlunoHorarioTurma) throws Exception {
        String sql = "DELETE FROM alunohorarioturma WHERE codigo = "+codigoAlunoHorarioTurma;
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.execute();
        }
    }

    public void atualizarNrAlunosMatriculadosAulaDoContrato(ContratoVO contratoVO, Date dataBase) throws Exception {
        //Se a data atual for maior ou igual à data base, então não atualize os dados da quantidade de alunos na aula.
        if (dataBase == null || Calendario.diferencaEmDias(Calendario.hoje(), dataBase) < 1) {
            return;
        }
        try {
            dataInicioPeriodo = dataBase;

            for (ContratoModalidadeVO cm : contratoVO.getContratoModalidadeVOs()) {
                for (ContratoModalidadeTurmaVO cmt : (List<ContratoModalidadeTurmaVO>) cm.getContratoModalidadeTurmaVOs()) {
                    for (ContratoModalidadeHorarioTurmaVO cmht : (List<ContratoModalidadeHorarioTurmaVO>) cmt.getContratoModalidadeHorarioTurmaVOs()) {
                        HorarioTurmaVO obj = cmht.getHorarioTurma();
                        obj.setNrAlunoMatriculado(nrAlunosMatriculados(obj, obj.getTipoCategoriaCliente(), obj.isCapacidadePorCategoria()));
                    }
                }
            }
        } catch (Exception e) {
            Logger.getLogger(HorarioTurma.class.getName()).log(Level.SEVERE, "Não foi possível atualizar o nº de alunos na aula", e);
        } finally {
            dataInicioPeriodo = null;
        }
    }
}

