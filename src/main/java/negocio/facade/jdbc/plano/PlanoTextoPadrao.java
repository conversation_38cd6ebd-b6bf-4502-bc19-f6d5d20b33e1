package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.MarcadorVO;
import negocio.comuns.plano.PlanoTextoPadraoTagVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.interfaces.plano.PlanoTextoPadraoInterfaceFacade;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PlanoTextoPadraoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PlanoTextoPadraoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see PlanoTextoPadraoVO
 * @see SuperEntidade
 */
public class PlanoTextoPadrao extends SuperEntidade implements PlanoTextoPadraoInterfaceFacade {

    private static final String sqlInsert = "INSERT INTO PlanoTextoPadrao("
            + " descricao, dataDefinicao, responsavelDefinicao, texto,situacao, imagemLogo, tipoContrato ) "
            + "VALUES ( ?, ?, ?, ?, ?, ?, ?)";
    private static final String sqlUpdate = "UPDATE PlanoTextoPadrao set descricao=?, "
            + "dataDefinicao=?, responsavelDefinicao=?, texto=?,situacao=?, imagemLogo=?, tipoContrato=? "
            + "WHERE ((codigo = ?))";

    public PlanoTextoPadrao() throws Exception {
        super();
        setIdEntidade("PlanoTextoPadrao");
    }

    public PlanoTextoPadrao(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>PlanoTextoPadraoVO</code>.
     */
    public PlanoTextoPadraoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new PlanoTextoPadraoVO();
    }

    private PreparedStatement prepararIncluir(PlanoTextoPadraoVO obj) throws Exception {
        PlanoTextoPadraoVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();

        PreparedStatement sqlInserir = con.prepareStatement(sqlInsert);
        sqlInserir.setString(1, obj.getDescricao());
        sqlInserir.setDate(2, Uteis.getDataJDBC(obj.getDataDefinicao()));
        if (obj.getResponsavelDefinicao().getCodigo() != 0) {
            sqlInserir.setInt(3, obj.getResponsavelDefinicao().getCodigo());
        } else {
            sqlInserir.setNull(3, 0);
        }
        obj.setTexto(apagarComentariosHTML(obj.getTexto()));
        sqlInserir.setString(4, obj.getTexto());
        sqlInserir.setString(5, obj.getSituacao());
        sqlInserir.setBytes(6, obj.getImagemLogo());
        sqlInserir.setString(7, obj.getTipoContrato());

        return sqlInserir;
    }

    private PreparedStatement prepararAlterar(PlanoTextoPadraoVO obj)
            throws Exception {
        PlanoTextoPadraoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();

        PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate);
        sqlAlterar.setString(1, obj.getDescricao());
        sqlAlterar.setDate(2, Uteis.getDataJDBC(obj.getDataDefinicao()));
        if (obj.getResponsavelDefinicao().getCodigo().intValue() != 0) {
            sqlAlterar.setInt(3, obj.getResponsavelDefinicao().getCodigo().intValue());
        } else {
            sqlAlterar.setNull(3, 0);
        }
        obj.setTexto(apagarComentariosHTML(obj.getTexto()));
        sqlAlterar.setString(4, obj.getTexto());
        sqlAlterar.setString(5, obj.getSituacao());
        sqlAlterar.setBytes(6, obj.getImagemLogo());
        sqlAlterar.setString(7, obj.getTipoContrato());
        sqlAlterar.setInt(8, obj.getCodigo().intValue());
        return sqlAlterar;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PlanoTextoPadraoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PlanoTextoPadraoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PlanoTextoPadraoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            validarComprovanteCompraExistente(obj);
            PreparedStatement sqlInserir = prepararIncluir(obj);
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setListaTagUtilizado(obterTagsAlteradas(obj.getTexto(), obj.getCodigo(), obj.getTipoContrato()));
            PlanoTextoPadraoTag planoTextoPadraoTagDAO = new PlanoTextoPadraoTag(con);
            planoTextoPadraoTagDAO.incluirPlanoTextoPadraoTag(obj.getCodigo(), obj.getListaTagUtilizado());
            planoTextoPadraoTagDAO = null;
            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    public Integer existeTermoResponsabilidade() throws Exception {
        try {
            PreparedStatement sqlInserir = con.prepareStatement("select * from planotextopadrao p where tipocontrato = \'TR\'");
            ResultSet resultSet = sqlInserir.executeQuery();
            if (resultSet.next()){
                return resultSet.getInt("codigo");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PlanoTextoPadraoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PlanoTextoPadraoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(PlanoTextoPadraoVO obj) throws Exception {
        try {
            PreparedStatement sqlInserir = prepararIncluir(obj);
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setListaTagUtilizado(obterTagsAlteradas(obj.getTexto(), obj.getCodigo(), obj.getTipoContrato()));
            getFacade().getPlanoTextoPadraoTag().incluirPlanoTextoPadraoTag(obj.getCodigo(), obj.getListaTagUtilizado());
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PlanoTextoPadraoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoTextoPadraoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PlanoTextoPadraoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(PlanoTextoPadraoVO obj) throws Exception{
        validarComprovanteCompraExistente(obj);
        PreparedStatement sqlAlterar = prepararAlterar(obj);
        sqlAlterar.execute();
        obj.setListaTagUtilizado(obterTagsAlteradas(obj.getTexto(), obj.getCodigo(), obj.getTipoContrato()));
        getFacade().getPlanoTextoPadraoTag().alterarPlanoTextoPadraoTag(obj.getCodigo(), obj.getListaTagUtilizado());
    }

    /**
     * Método que procura comentário em HTML <!-- -->
     * considerando espaços ou letras ou números ou símbolos especiais
     * <AUTHOR>
     * @param texto
     * @return o texto sem os comentários
     */
    public String apagarComentariosHTML(String texto) {
        texto = texto.replaceAll("(?s)<!--.*?-->", "");
        return texto;
    }

    public List obterTagsAlteradas(String texto, Integer codigo, String tipoContrato) throws Exception {
        List lista = new ArrayList();
        String tag = "";
        while ((texto.indexOf("[") != -1) && (texto.indexOf("]") != -1)) {
            int posicaoIni = texto.indexOf("[");
            int posicaoFim = texto.indexOf("]");
            if (posicaoIni > posicaoFim || (!texto.substring(posicaoIni + 1, posicaoIni + 2).equals("("))) {
                throw new Exception("A inserção de colchetes é limitada as tags.Verifique colchete após a última tag válida: " + tag);
            }
            tag = texto.substring(posicaoIni, posicaoFim + 1);
            PlanoTextoPadraoTagVO p = new PlanoTextoPadraoTagVO();
            p.setTag(tag);
            if (tipoContrato.equals("PL")
                    && (p.getTag().contains("Venda") || p.getTag().contains("Itens") || p.getTag().contains("PacoteItem"))) {
                throw new Exception("A Tag " + p.getTag() + " não pode ser usada no contrato de planos");
            } else if (tipoContrato.equals("SE")
                    && (p.getTag().contains("Contrato") || p.getTag().contains("Plano") || p.getTag().contains("Turma")
                    || p.getTag().contains("Modalidade") || p.getTag().contains("Composicao"))) {
                throw new Exception("A Tag " + p.getTag() + " não pode ser usada no contrato de serviços");
            }
            p.setPlanoTextoPadrao(codigo);
            lista.add(p);
            texto = texto.substring(0, posicaoIni) + texto.substring(posicaoFim + 1);
        }
        return lista;
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PlanoTextoPadraoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoTextoPadraoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PlanoTextoPadraoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM PlanoTextoPadrao WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            getFacade().getPlanoTextoPadraoTag().excluirPlanoTextoPadraoTag(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoTextoPadrao</code> através do valor do atributo 
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PlanoTextoPadraoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoTextoPadrao.* FROM PlanoTextoPadrao, Colaborador WHERE PlanoTextoPadrao.responsavelDefinicao = Colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoTextoPadrao</code> através do valor do atributo 
     * <code>Date dataDefinicao</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoTextoPadraoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataDefinicao(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT texto, situacao, responsaveldefinicao, datadefinicao, descricao, codigo, tipocontrato  FROM PlanoTextoPadrao WHERE ((dataDefinicao >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataDefinicao <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataDefinicao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoTextoPadrao</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoTextoPadraoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT texto, situacao, responsaveldefinicao, datadefinicao, descricao, codigo, tipocontrato  FROM PlanoTextoPadrao WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoTextoPadrao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoTextoPadraoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT texto, situacao, responsaveldefinicao, datadefinicao, descricao, codigo, tipocontrato  FROM PlanoTextoPadrao WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public PlanoTextoPadraoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT texto, situacao, responsaveldefinicao, datadefinicao, descricao, codigo, tipocontrato  FROM PlanoTextoPadrao WHERE codigo = " + valorConsulta;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()){
            return (montarDados(tabelaResultado, nivelMontarDados, this.con));
        }
        return null;
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoTextoPadrao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoTextoPadraoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoSituacao(Integer valorConsulta, String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT texto, situacao, responsaveldefinicao, datadefinicao, descricao, codigo, tipocontrato  FROM PlanoTextoPadrao WHERE codigo >= " + valorConsulta + " and situacao = 'AT' ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public List consultarPorCodigoSituacaoTipo(Integer valorConsulta, String situacao, String tipoContrato, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT texto, situacao, responsaveldefinicao, datadefinicao, descricao, codigo, tipocontrato  FROM PlanoTextoPadrao WHERE codigo >= " + valorConsulta + " and situacao = 'AT' and tipoContrato = '" + tipoContrato + "' ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    public PlanoTextoPadraoVO consultarPorChavePrimariaSituacaoTipo(Integer valorConsulta, String situacao,String tipoContrato, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT texto, situacao, responsaveldefinicao, datadefinicao, descricao, codigo, tipocontrato FROM PlanoTextoPadrao WHERE codigo = " + valorConsulta + " and situacao = 'AT' and tipoContrato = '" + tipoContrato + "' ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return (montarDados(tabelaResultado, nivelMontarDados, this.con));
        }
        return null;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>PlanoTextoPadraoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<PlanoTextoPadraoVO> vetResultado = new ArrayList<PlanoTextoPadraoVO>();
        while (tabelaResultado.next()) {
            PlanoTextoPadraoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PlanoTextoPadraoVO</code>.
     * @return  O objeto da classe <code>PlanoTextoPadraoVO</code> com os dados devidamente montados.
     */
    public static PlanoTextoPadraoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PlanoTextoPadraoVO obj = new PlanoTextoPadraoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setTipoContrato(dadosSQL.getString("tipoContrato"));
        obj.setDataDefinicao(dadosSQL.getDate("dataDefinicao"));
        obj.getResponsavelDefinicao().setCodigo(dadosSQL.getInt("responsavelDefinicao"));
        obj.setTexto(dadosSQL.getString("texto"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        PlanoTextoPadraoTag planoTextoPadrao = new PlanoTextoPadraoTag(con);
        obj.setListaTagUtilizado(planoTextoPadrao.consultarPlanoTextoPadraoTag(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        planoTextoPadrao = null;

        montarDadosResponsavelDefinicao(obj, nivelMontarDados, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ColaboradorVO</code> relacionado ao objeto <code>PlanoTextoPadraoVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelDefinicao(PlanoTextoPadraoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelDefinicao().getCodigo().intValue() == 0) {
            obj.setResponsavelDefinicao(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setResponsavelDefinicao(usuario.consultarPorChavePrimaria(obj.getResponsavelDefinicao().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PlanoTextoPadraoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PlanoTextoPadraoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT texto, situacao, responsaveldefinicao, datadefinicao, descricao, codigo, tipocontrato  FROM PlanoTextoPadrao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoTextoPadrao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public void realizarCriacaoImagemLogo(PlanoTextoPadraoVO planoTexto) throws IOException {
        File file = new File(Uteis.obterCaminhoWeb() + File.separator + "fotos" + File.separator + "logoContrato.jpg");
        if (file.exists()) {
            file.delete();
        }
        file.createNewFile();
        OutputStream os = new FileOutputStream(file);
        if (planoTexto.getImagemLogo() != null) {
            os.write(planoTexto.getImagemLogo());
        }
        os.flush();
        os.close();
        //return realizarAlteracaoHTML(planoTexto.getTexto());

    }

    public String realizarAlteracaoHTML(String texto) {
        texto = texto.replaceAll("<img.*?>", "");
        int parametro = 0;
        parametro = texto.lastIndexOf("</p>");
        if (parametro == -1) {
            parametro = texto.lastIndexOf("</body>");
        }
        String textoAntes = texto.substring(0, parametro);
        String textoDepois = texto.substring(parametro, texto.length());
        Integer id = 1 + (int) (Math.random() * 10000);
        return texto = textoAntes + "<img id=\"logo\" src=\"/fotos/logoContrato.jpg?id=" + id + "\" width=\"100\" height=\"57\"  /> " + textoDepois;

    }

    public String consultarJSON() throws Exception {

        ResultSet rs = getRS();

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao").trim())).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("responsaveldefinicao").trim())).append("\",");
            json.append("\"").append(rs.getDate("datadefinicao")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public PlanoTextoPadraoVO consultarPorDescricao(String descricao, int nivelMontarDados)throws Exception{
        String sql = "select * from planotextopadrao where upper(descricao) = ?";
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setString(1, descricao.toUpperCase());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return PlanoTextoPadrao.montarDados(rs, nivelMontarDados, con);
        }
        return null;
    }

    private ResultSet getRS() throws SQLException {
        String sql = "SELECT\n" + "  ptp.codigo, descricao, u.nome AS responsaveldefinicao, datadefinicao\n" + "FROM planotextopadrao ptp\n" + "  LEFT JOIN usuario u ON ptp.responsaveldefinicao = u.codigo" + "  ORDER BY descricao";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    @Override
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        ResultSet rs = getRS();
        List lista = new ArrayList();

        while (rs.next()) {

            PlanoTextoPadraoVO texto = new PlanoTextoPadraoVO();
            String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("responsaveldefinicao") + rs.getString("datadefinicao");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                texto.setCodigo(rs.getInt("codigo"));
                texto.setDescricao(rs.getString("descricao"));
                texto.getResponsavelDefinicao().setNome(rs.getString("responsaveldefinicao"));
                texto.setDataDefinicao(rs.getDate("datadefinicao"));
                lista.add(texto);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Responsável")) {
            Ordenacao.ordenarLista(lista, "responsavel_Apresentar");
        } else if (campoOrdenacao.equals("Definição")) {
            Ordenacao.ordenarLista(lista, "dataDefinicao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    private void validarComprovanteCompraExistente(PlanoTextoPadraoVO obj) throws Exception {
        if (obj.getTipoContrato() == null ||
                !obj.getTipoContrato().equalsIgnoreCase("CC")) {
            return;
        }
        List<PlanoTextoPadraoVO> lista = consultarPorTipoSituacaoCodigoDiferente("CC", "AT", obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!UteisValidacao.emptyList(lista)) {
            throw new Exception("Já existe um comprovante de compra \"" + lista.get(0).getDescricao() + "\" ativo.");
        }
    }

    public StringBuilder obterSQLPorTipoSituacaoCodigoDiferente(String tipo, String situacao, Integer codigoDiferente) {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("* \n");
        sql.append("from planotextopadrao \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyString(tipo)) {
            sql.append("and tipocontrato = '").append(tipo).append("' \n");
        }
        if (!UteisValidacao.emptyString(situacao)) {
            sql.append("and situacao = '").append(situacao).append("' \n");
        }
        if (!UteisValidacao.emptyNumber(codigoDiferente)) {
            sql.append("and codigo <> ").append(codigoDiferente).append(" \n");
        }
        return sql;
    }

    public List<PlanoTextoPadraoVO> consultarPorTipoSituacaoCodigoDiferente(String tipo, String situacao,
                                                                            Integer codigoDiferente, int nivelMontarDados) throws Exception {
        StringBuilder sql = obterSQLPorTipoSituacaoCodigoDiferente(tipo, situacao, codigoDiferente);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    public boolean existePorTipoSituacao(String tipo, String situacao, Integer codigoDiferente) {
        try {
            StringBuilder sql = obterSQLPorTipoSituacaoCodigoDiferente(tipo, situacao, codigoDiferente);
            return SuperFacadeJDBC.existe(sql.toString(), this.con);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public PlanoTextoPadraoVO consultarPorTipoSituacao(String tipoContrato, String situacao, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM PlanoTextoPadrao " +
                "WHERE situacao = '"+situacao+"' " +
                "and tipoContrato = '" + tipoContrato + "' " +
                "ORDER BY codigo desc ";
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql);
        if (rs.next()) {
            return (montarDados(rs, nivelMontarDados, this.con));
        }
        return null;
    }

    public String gerarComprovanteCompra(List<MovParcelaVO> listaMovParcelas) throws Exception {
        Empresa empresaDAO;
        Cliente clienteDAO;
        Contrato contratoDAO;
        VendaAvulsa vendaAvulsaDAO;
        MovParcela movParcelaDAO;
        MovProduto movProdutoDAO;
        Usuario usuarioDAO;
        try {
            empresaDAO = new Empresa(this.con);
            clienteDAO = new Cliente(this.con);
            contratoDAO = new Contrato(this.con);
            vendaAvulsaDAO = new VendaAvulsa(this.con);
            movParcelaDAO = new MovParcela(this.con);
            movProdutoDAO = new MovProduto(this.con);
            usuarioDAO = new Usuario(this.con);

            Set<Integer> listaContrato = new HashSet<>();
            Set<Integer> listaVendaAvulsa = new HashSet<>();
            Set<Integer> listaParcelas = new HashSet<>();
            Set<Integer> listaPessoa = new HashSet<>();
            Set<Integer> listaEmpresas = new HashSet<>();

            for (MovParcelaVO movParcelaVO : listaMovParcelas) {

                if (movParcelaVO.getParcelaEscolhida() != null &&
                        !movParcelaVO.getParcelaEscolhida()) {
                    continue;
                }

                listaEmpresas.add(movParcelaVO.getEmpresa().getCodigo());
                listaParcelas.add(movParcelaVO.getCodigo());
                listaPessoa.add(movParcelaVO.getPessoa().getCodigo());

                if (!UteisValidacao.emptyNumber(movParcelaVO.getVendaAvulsaVO().getCodigo())) {
                    listaVendaAvulsa.add(movParcelaVO.getVendaAvulsaVO().getCodigo());
                }
                if (!UteisValidacao.emptyNumber(movParcelaVO.getContrato().getCodigo())) {
                    listaContrato.add(movParcelaVO.getContrato().getCodigo());
                }
            }

            if (UteisValidacao.emptyNumber(listaParcelas.size())) {
                throw new Exception("Nenhuma parcela selecionada");
            }

            if (listaEmpresas.isEmpty()) {
                throw new Exception("Nenhuma empresa encontrada");
            }

            if (listaEmpresas.size() > 1) {
                throw new Exception("Não é possível gerar o comprovante com parcelas de empresas diferentes.");
            }

            if (listaPessoa.size() > 1) {
                throw new Exception("Não é possível gerar o comprovante com mais de uma pessoa.");
            }

            if (listaVendaAvulsa.size() > 1) {
                throw new Exception("Não é possível gerar o comprovante com mais de uma venda avulsa.");
            }

            if (listaContrato.size() > 1) {
                throw new Exception("Não é possível gerar o comprovante com mais de um contrato.");
            }

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(listaEmpresas.stream().findFirst().get(), Uteis.NIVELMONTARDADOS_TODOS);
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(listaPessoa.stream().findFirst().get(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            ContratoVO contratoVO = null;
            if (!listaContrato.isEmpty()) {
                contratoVO = contratoDAO.consultarPorChavePrimaria(listaContrato.stream().findFirst().get(), Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
                List<MovParcelaVO> parcelasVenda = movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                contratoVO.setMovParcelaVOs(parcelasVenda);
            }

            VendaAvulsaVO vendaAvulsaVO = null;
            if (!listaVendaAvulsa.isEmpty()) {
                vendaAvulsaVO = vendaAvulsaDAO.consultarPorChavePrimaria(listaVendaAvulsa.stream().findFirst().get(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                List<MovParcelaVO> parcelasVenda = movParcelaDAO.consultarPorCodigoVendaAvulsaLista(vendaAvulsaVO.getCodigo(), null, null, null, false, Uteis.NIVELMONTARDADOS_TODOS);
                List<MovProdutoVO> produtosVenda = movProdutoDAO.consultarTodosPorVendaAvulsa(vendaAvulsaVO.getCodigo());
                vendaAvulsaVO.setEmpresa(empresaVO);
                vendaAvulsaVO.setParcela(parcelasVenda.get(0));
                vendaAvulsaVO.setMovParcelaVOs(parcelasVenda);
                vendaAvulsaVO.setMovProdutoVOs(produtosVenda);
                UsuarioVO usuarioVO = new UsuarioVO();
                if (vendaAvulsaVO.getResponsavel() != null &&
                        !UteisValidacao.emptyNumber(vendaAvulsaVO.getResponsavel().getCodigo())) {
                    usuarioVO = usuarioDAO.consultarPorChavePrimaria(vendaAvulsaVO.getResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
                vendaAvulsaVO.setResponsavel(usuarioVO);
            }

            PlanoTextoPadraoVO planoTextoPadraoVO = consultarPorTipoSituacao("CC", "AT", Uteis.NIVELMONTARDADOS_TODOS);

            char aspas = '"';
            String espaco = "<p style=" + aspas + "text-align: left;" + aspas + ">";
            String htmlBotao = "<form> ";
            String htmlBotao2 = "<form> <input id=" + aspas + "imprimir" + aspas + "type=" + aspas + "image" + aspas + " src=" + aspas + "./imagens/imprimirContrato.png" + aspas + " name=" + aspas + "imprimir" + aspas + " alt=" + aspas + "Imprimir Contrato" + aspas + "onclick=" + aspas + "window.print();" + aspas + "/>";
            String textoSub = planoTextoPadraoVO.getTexto();
            textoSub = textoSub.replace("Untitled document", "COMPROVANTE DE COMPRA");
            int posicaoBody = textoSub.indexOf("<body>");
            String parte1 = textoSub.substring(0, posicaoBody + 6);
            String parte2 = textoSub.substring(posicaoBody + 6);
            parte1 = parte1.replace("</head>", "<style>@media print {#imprimir {display: none;}}</style></head>");
            textoSub = parte1 + htmlBotao + parte2;

            posicaoBody = textoSub.indexOf("</body>");
            parte1 = textoSub.substring(0, posicaoBody);
            parte2 = textoSub.substring(posicaoBody);
            textoSub = parte1 + htmlBotao2 + espaco + "</form>" + parte2;
            StringBuilder textoSubBuilder = new StringBuilder();
            textoSubBuilder.append(textoSub);
            textoSub = textoSubBuilder.toString();

            MarcadorVO marcador = new MarcadorVO();
            Iterator i = planoTextoPadraoVO.getListaTagUtilizado().iterator();
            while (i.hasNext()) {
                PlanoTextoPadraoTagVO p = (PlanoTextoPadraoTagVO) i.next();
                marcador.setTag(p.getTag());

                textoSub = planoTextoPadraoVO.verificaQualMetodoParaSubstituicaoInvocar(DAO.resolveKeyFromConnection(this.con),
                        marcador, textoSub, clienteVO, contratoVO,
                        vendaAvulsaVO, null, null, this.con,
                        empresaVO.getDescMoeda(), true, null);
            }
            if (JSFUtilities.isJSFContext()) {
                HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
                request.getSession().setAttribute("textoRelatorio", textoSub);
            }
            return textoSub;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            empresaDAO = null;
            clienteDAO = null;
            contratoDAO = null;
            vendaAvulsaDAO = null;
            movParcelaDAO = null;
            movProdutoDAO = null;
        }
    }
}
