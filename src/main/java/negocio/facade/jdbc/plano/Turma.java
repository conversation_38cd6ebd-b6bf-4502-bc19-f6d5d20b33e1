package negocio.facade.jdbc.plano;

import br.com.pactosolucoes.agendatotal.json.AgendaReposicaoJSON;
import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import br.com.pactosolucoes.agendatotal.json.AgendamentoDesmarcadoJSON;
import br.com.pactosolucoes.agendatotal.json.ClienteSintenticoJson;
import br.com.pactosolucoes.agendatotal.json.TurmaAulaCheiaJSON;
import br.com.pactosolucoes.enumeradores.TipoAntecedenciaMarcarAulaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.json.AlunoAulaAcessoJSON;
import negocio.comuns.basico.AgendaTotalTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.basico.enumerador.TipoToleranciaAulaEnum;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ContratoModalidadeReferenceException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.ReposicaoReferenceException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Reposicao;
import negocio.facade.jdbc.contrato.ContratoModalidadeTurma;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.interfaces.plano.HorarioTurmaInterfaceFacade;
import negocio.interfaces.plano.TurmaInterfaceFacade;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.postgresql.util.PSQLException;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>TurmaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultarSintetico pertinentes a classe <code>TurmaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see TurmaVO
 * @see SuperEntidade
 */
public class Turma extends SuperEntidade implements TurmaInterfaceFacade {

    private Hashtable horarioTurmas;

    public Turma() throws Exception {
        super();
        setHorarioTurmas(new Hashtable());
    }

    public Turma(Connection con) throws Exception {
        super(con);
        setHorarioTurmas(new Hashtable());
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>TurmaVO</code>.
     */
    public TurmaVO novo() throws Exception {
        incluir(getIdEntidade());
        TurmaVO obj = new TurmaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>TurmaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>TurmaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(TurmaVO obj) throws Exception {
        try {
            TurmaVO.validarDados(obj);
            // Turma.incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Turma( descricao ,identificador, modalidade, "
                    + "dataInicialVigencia, dataFinalVigencia, idadeMinima, "
                    + "idadeMinimaMeses, idadeMaxima, idadeMaximaMeses, "
                    + "bloquearMatriculasAcimaLimite, empresa, monitorada,minutosAntecedenciaMarcarAula,minutosAntecedenciaDesmarcarAula,"
                    + "mensagem, bonificacao, pontosBonus, meta, ocupacao,aulaColetiva, codigoaulacheia, "
                    + "dias, horarios, professor, tolerancia, capacidade, ambiente, tipoAntecedenciaMarcarAula , bloquearReposicaoAcimaLimite, permitirAulaExperimental, permitirdesmarcarreposicoes,validarrestricoesmarcacao, "
                    + "qtdeNivelOcupacao, percDescOcupacaoNivel1, percDescOcupacaoNivel2, percDescOcupacaoNivel3, percDescOcupacaoNivel4, percDescOcupacaoNivel5, minutosAposInicioApp, "
                    + "integracaoSpivi, permiteAlunoOutraEmpresa, produtoGymPass, idClasseGymPass, urlTurmaVirtual, naoValidarModalidadeContrato, " +
                    " tipotolerancia, urlVideoYoutube,visualizarProdutosGympass, visualizarProdutosTotalpass, permiteFixar, niveis, tipoReservaEquipamento, mapaEquipamentos, idexterno, aulaIntegracaoSelfloops, "
                    + "limiteVagasAgregados)"
                    + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setString(1, obj.getDescricao());
                sqlInserir.setString(2, obj.getIdentificador());
                if (obj.getModalidade().getCodigo() != 0) {
                    sqlInserir.setInt(3, obj.getModalidade().getCodigo());
                } else {
                    sqlInserir.setNull(3, 0);
                }
                sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataInicialVigencia()));
                sqlInserir.setDate(5, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
                sqlInserir.setInt(6, obj.getIdadeMinima());
                sqlInserir.setInt(7, obj.getIdadeMinimaMeses());
                sqlInserir.setInt(8, obj.getIdadeMaxima());
                sqlInserir.setInt(9, obj.getIdadeMaximaMeses());
                sqlInserir.setBoolean(10, obj.isBloquearMatriculasAcimaLimite());
                if (obj.getEmpresa().getCodigo() != 0) {
                    sqlInserir.setInt(11, obj.getEmpresa().getCodigo());
                } else {
                    sqlInserir.setNull(11, 0);
                }
                sqlInserir.setBoolean(12, obj.isMonitorada());
                if (obj.getTipoAntecedenciaMarcarAulaEnum() == TipoAntecedenciaMarcarAulaEnum.NAO_VALIDAR) {
                    sqlInserir.setNull(13, Types.NULL);
                } else {
                    sqlInserir.setInt(13, obj.getMinutosAntecedenciaMarcarAula());
                }

                sqlInserir.setInt(14, obj.getMinutosAntecedenciaDesmarcarAula());

                //aula cheia
//            private String mensagem;
                sqlInserir.setString(15, obj.getMensagem());
                resolveDoubleNull(sqlInserir, 16, obj.getBonificacao());
                resolveIntegerNull(sqlInserir, 17, obj.getPontosBonus());
                resolveDoubleNull(sqlInserir, 18, obj.getMeta());
                if (obj.getOcupacao() == null) {
                    sqlInserir.setNull(19, Types.NULL);
                } else {
                    sqlInserir.setInt(19, obj.getOcupacao());
                }
                sqlInserir.setBoolean(20, obj.getAulaColetiva());
                resolveIntegerNull(sqlInserir, 21, obj.getCodigoAulaCheia());

                sqlInserir.setString(22, obj.getDias());
                sqlInserir.setString(23, obj.getHorarios());
                resolveIntegerNull(sqlInserir, 24, obj.getProfessor());
                resolveIntegerNull(sqlInserir, 25, obj.getTolerancia());
                resolveIntegerNull(sqlInserir, 26, obj.getCapacidade());
                resolveIntegerNull(sqlInserir, 27, obj.getAmbiente());
                resolveIntegerNull(sqlInserir, 28, obj.getTipoAntecedenciaMarcarAula());
                sqlInserir.setBoolean(29, obj.isBloquearReposicaoAcimaLimite());
                sqlInserir.setBoolean(30, obj.isPermitirAulaExperimental());
                sqlInserir.setBoolean(31, obj.isPermitirDesmarcarReposicoes());
                sqlInserir.setBoolean(32, obj.isValidarRestricoesMarcacao());
                sqlInserir.setInt(33, obj.getQtdeNivelOcupacao());
                sqlInserir.setDouble(34, obj.getPercDescOcupacaoNivel1());
                sqlInserir.setDouble(35, obj.getPercDescOcupacaoNivel2());
                sqlInserir.setDouble(36, obj.getPercDescOcupacaoNivel3());
                sqlInserir.setDouble(37, obj.getPercDescOcupacaoNivel4());
                sqlInserir.setDouble(38, obj.getPercDescOcupacaoNivel5());
                sqlInserir.setDouble(39, obj.getMinutosAposInicioApp());
                sqlInserir.setBoolean(40, obj.getIntegracaoSpivi());
                sqlInserir.setBoolean(41, obj.isPermiteAlunoOutraEmpresa());
                resolveIntegerNull(sqlInserir, 42, obj.getProdutoGymPass());
                resolveIntegerNull(sqlInserir, 43, obj.getIdClasseGymPass());
                sqlInserir.setString( 44, obj.getUrlTurmaVirtual());
                sqlInserir.setBoolean( 45, obj.isNaoValidarModalidadeContrato());
                sqlInserir.setInt( 46, obj.getTipoTolerancia() == null ? TipoToleranciaAulaEnum.APOS_INICIO.getCodigo() : obj.getTipoTolerancia());
                sqlInserir.setString(47,obj.getUrlVideoYoutube());
                sqlInserir.setBoolean(48,obj.getVisualizarProdutosGympass());
                sqlInserir.setBoolean(49,obj.getVisualizarProdutosTotalpass());
                sqlInserir.setBoolean(50,obj.isPermiteFixar());
                sqlInserir.setString(51, obj.getNiveis());
                sqlInserir.setString(52, obj.getTipoReservaEquipamento());
                sqlInserir.setString(53, obj.getMapaEquipamentos());
                resolveStringNull(sqlInserir, 54, obj.getIdExterno());
                sqlInserir.setBoolean(55,obj.isAulaIntegracaoSelfloops());
                sqlInserir.setInt(56, obj.getLimiteVagasAgregados() != null ? obj.getLimiteVagasAgregados().intValue() : 0);
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
//            getFacade().getHorarioTurma().incluirHorarioTurmas(obj, obj.getHorarioTurmaVOs());
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setCodigo(0);
            obj.setNovoObj(true);
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>TurmaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>TurmaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(TurmaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(TurmaVO obj) throws Exception {
        TurmaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Turma set descricao=?, identificador=?, "
                + "modalidade=?, dataInicialVigencia=?, dataFinalVigencia=?, "
                + "idadeMinima=?, idadeMinimaMeses=?, idadeMaxima=?, "
                + "idadeMaximaMeses=?, bloquearMatriculasAcimaLimite=?, empresa=?, "
                + "monitorada=?, minutosAntecedenciaMarcarAula=?, minutosAntecedenciaDesmarcarAula=?, "
                + "mensagem=?, bonificacao=?, pontosBonus=?, meta=?, ocupacao=?,aulaColetiva=?, "
                + "codigoaulacheia=?, dias=?, horarios=?, professor=?, tolerancia = ?, capacidade = ?, "
                + "ambiente = ?, tipoAntecedenciaMarcarAula=?, bloquearReposicaoAcimaLimite = ?,permitirAulaExperimental=?, "
                + "permitirdesmarcarreposicoes=?, validarrestricoesmarcacao=?, "
                + "qtdeNivelOcupacao = ?, percDescOcupacaoNivel1 = ?, percDescOcupacaoNivel2 = ?, "
                + "percDescOcupacaoNivel3 = ?, percDescOcupacaoNivel4 = ?, percDescOcupacaoNivel5 = ?, minutosAposInicioApp = ?, "
                + "integracaoSpivi = ?, permiteAlunoOutraEmpresa = ?, produtoGymPass = ?, idClasseGymPass = ?, " +
                "urlTurmaVirtual = ?, naoValidarModalidadeContrato = ?, tipotolerancia = ?, urlVideoYoutube = ?, " +
                "visualizarProdutosGympass = ?, visualizarProdutosTotalpass = ?, permitefixar = ?, niveis = ?, "
                + "tipoReservaEquipamento = ?, mapaEquipamentos = ?, aulaIntegracaoSelfloops = ? , limiteVagasAgregados = ? "
                + "WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getDescricao());
            sqlAlterar.setString(2, obj.getIdentificador());
            if (obj.getModalidade().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(3, obj.getModalidade().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(3, 0);
            }
            sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataInicialVigencia()));
            sqlAlterar.setDate(5, Uteis.getDataJDBC(obj.getDataFinalVigencia()));
            sqlAlterar.setInt(6, obj.getIdadeMinima().intValue());
            sqlAlterar.setInt(7, obj.getIdadeMinimaMeses().intValue());
            sqlAlterar.setInt(8, obj.getIdadeMaxima().intValue());
            sqlAlterar.setInt(9, obj.getIdadeMaximaMeses().intValue());
            sqlAlterar.setBoolean(10, obj.isBloquearMatriculasAcimaLimite().booleanValue());
            if (obj.getEmpresa().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(11, obj.getEmpresa().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(11, 0);
            }
            sqlAlterar.setBoolean(12, obj.isMonitorada());
            if (obj.getTipoAntecedenciaMarcarAulaEnum() == TipoAntecedenciaMarcarAulaEnum.NAO_VALIDAR) {
                sqlAlterar.setNull(13, Types.NULL);
            } else {
                sqlAlterar.setInt(13, obj.getMinutosAntecedenciaMarcarAula());
            }
            sqlAlterar.setInt(14, obj.getMinutosAntecedenciaDesmarcarAula());

            //aula cheia
            sqlAlterar.setString(15, obj.getMensagem());
            resolveDoubleNull(sqlAlterar, 16, obj.getBonificacao());
            resolveIntegerNull(sqlAlterar, 17, obj.getPontosBonus());
            resolveDoubleNull(sqlAlterar, 18, obj.getMeta());
            if (obj.getOcupacao() == null) {
                sqlAlterar.setNull(19, Types.NULL);
            } else {
                sqlAlterar.setInt(19, obj.getOcupacao());
            }
            sqlAlterar.setBoolean(20, obj.getAulaColetiva());
            resolveIntegerNull(sqlAlterar, 21, obj.getCodigoAulaCheia());

            sqlAlterar.setString(22, obj.getDias());
            sqlAlterar.setString(23, obj.getHorarios());
            resolveIntegerNull(sqlAlterar, 24, obj.getProfessor());
            resolveIntegerNull(sqlAlterar, 25, obj.getTolerancia());
            resolveIntegerNull(sqlAlterar, 26, obj.getCapacidade());
            resolveIntegerNull(sqlAlterar, 27, obj.getAmbiente());
            resolveIntegerNull(sqlAlterar, 28, obj.getTipoAntecedenciaMarcarAula());
            sqlAlterar.setBoolean(29, obj.isBloquearReposicaoAcimaLimite());
            sqlAlterar.setBoolean(30, obj.isPermitirAulaExperimental());
            sqlAlterar.setBoolean(31, obj.isPermitirDesmarcarReposicoes());
            sqlAlterar.setBoolean(32, obj.isValidarRestricoesMarcacao());

            sqlAlterar.setInt(33, obj.getQtdeNivelOcupacao());
            sqlAlterar.setDouble(34, obj.getPercDescOcupacaoNivel1());
            sqlAlterar.setDouble(35, obj.getPercDescOcupacaoNivel2());
            sqlAlterar.setDouble(36, obj.getPercDescOcupacaoNivel3());
            sqlAlterar.setDouble(37, obj.getPercDescOcupacaoNivel4());
            sqlAlterar.setDouble(38, obj.getPercDescOcupacaoNivel5());
            sqlAlterar.setDouble(39, obj.getMinutosAposInicioApp());
            sqlAlterar.setBoolean(40, obj.getIntegracaoSpivi());
            sqlAlterar.setBoolean(41, obj.isPermiteAlunoOutraEmpresa());
            resolveIntegerNull(sqlAlterar, 42, obj.getProdutoGymPass());
            resolveIntegerNull(sqlAlterar, 43, obj.getIdClasseGymPass());
            sqlAlterar.setString(44, obj.getUrlTurmaVirtual());
            sqlAlterar.setBoolean(45, obj.isNaoValidarModalidadeContrato());
            sqlAlterar.setInt(46, obj.getTipoTolerancia() == null ? TipoToleranciaAulaEnum.APOS_INICIO.getCodigo() : obj.getTipoTolerancia());
            sqlAlterar.setString(47, obj.getUrlVideoYoutube());
            sqlAlterar.setBoolean(48, obj.getVisualizarProdutosGympass());
            sqlAlterar.setBoolean(49, obj.getVisualizarProdutosTotalpass());
            sqlAlterar.setBoolean(50, obj.isPermiteFixar());
            sqlAlterar.setString(51, obj.getNiveis());
            sqlAlterar.setString(52, obj.getTipoReservaEquipamento());
            sqlAlterar.setString(53, obj.getMapaEquipamentos());
            sqlAlterar.setBoolean(54, obj.isAulaIntegracaoSelfloops());
            sqlAlterar.setInt(55, obj.getLimiteVagasAgregados() != null ? obj.getLimiteVagasAgregados().intValue() : 0);
            sqlAlterar.setInt(56, obj.getCodigo());

            sqlAlterar.execute();
        }
        HorarioTurmaInterfaceFacade htDao = new HorarioTurma(con);
        htDao.alterarHorarioTurmas(obj, obj.getHorarioTurmaVOs(), obj.getHorarioTurmaVOsExclusao());
        htDao = null;
    }


    /**
     * Operação responsável por excluir no BD um objeto da classe <code>TurmaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>TurmaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(TurmaVO obj) throws ContratoModalidadeReferenceException, Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Turma WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            new HorarioTurma(con).excluirHorarioTurmas(obj.getCodigo());
            con.commit();
        } catch (PSQLException e) {
            con.rollback();
            con.setAutoCommit(true);
            if(e.getSQLState().equals(FOREIGN_KEY_VIOLATION)) {
                if (new ContratoModalidadeTurma(con).existeContratoModalidadeTurma(obj.getCodigo())) {
                    throw new ContratoModalidadeReferenceException(e.getMessage());
                } else if (new Reposicao(con).existeReposicaoTurma(obj.getCodigo())) {
                    throw new ReposicaoReferenceException(e.getMessage());
                }
            }
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Turma</code> através do valor do atributo
     * <code>Integer idadeMaxima</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorIdadeMaxima(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Turma WHERE idadeMaxima <= " + valorConsulta + " ORDER BY idadeMaxima";
        } else {
            sqlStr = "SELECT * FROM Turma WHERE idadeMaxima <= " + valorConsulta + " and empresa = " + empresa + " ORDER BY idadeMaxima";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public void atualizarUsuarioDesativou(TurmaVO turma) {
        try {
            if (Calendario.maior(turma.getDataFinalVigencia(), Calendario.hoje())) {
                SuperFacadeJDBC.executarConsulta("update turma set usuariodesativou = null where codigo = " + turma.getCodigo(), con);
            }
        } catch (Exception ignore) {
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Turma</code> através do valor do atributo
     * <code>Integer idadeMinima</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorIdadeMinima(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Turma WHERE idadeMinima >= " + valorConsulta.intValue() + " ORDER BY idadeMinima";
        } else {
            sqlStr = "SELECT * FROM Turma WHERE idadeMinima >= " + valorConsulta.intValue() + " and empresa = " + empresa.intValue() + " ORDER BY idadeMinima";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<TurmaVO> consultarPorNomeTurmaProfessorComLimite(String valorConsulta, int professor) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT distinct (turma.*) from turma  ");
        sqlStr.append("inner join horarioturma on horarioturma.turma= turma.codigo  ");
        sqlStr.append(" where horarioturma.professor =  " + professor);
        sqlStr.append("  and turma.descricao ilike('" + valorConsulta.toUpperCase() + "%') and not turma.aulaColetiva ORDER BY turma.descricao limit 30");
        ArrayList<TurmaVO> listaTurmas;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                listaTurmas = new ArrayList<TurmaVO>();
                while (tabelaResultado.next()) {
                    TurmaVO turmaVO = new TurmaVO();
                    turmaVO.setCodigo(tabelaResultado.getInt("codigo"));
                    turmaVO.setDescricao(tabelaResultado.getString("descricao"));
                    listaTurmas.add(turmaVO);
                }
            }
        }
        return listaTurmas;
    }

    /**
     * Responsável por realizar uma consulta de <code>Turma</code> através do valor do atributo
     * <code>Date dataInicialVigencia</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataInicialVigencia(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Turma WHERE ((dataInicialVigencia >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataInicialVigencia <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataInicialVigencia";
        } else {
            sqlStr = "SELECT * FROM Turma WHERE ((dataInicialVigencia >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataInicialVigencia <= '" + Uteis.getDataJDBC(prmFim) + "')) and  empresa = " + empresa.intValue() + " ORDER BY dataInicialVigencia";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Turma</code> através do valor do atributo
     * <code>nome</code> da classe <code>Modalidade</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>TurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeModalidade(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Turma.* FROM Turma, Modalidade WHERE Turma.modalidade = Modalidade.codigo and not turma.aulaColetiva and upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Modalidade.nome";
        } else {
            sqlStr = "SELECT Turma.* FROM Turma, Modalidade WHERE Turma.modalidade = Modalidade.codigo and not turma.aulaColetiva and upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') and Turma.empresa = " + empresa.intValue() + " ORDER BY Modalidade.nome";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Turma</code> através do valor do atributo
     * <code>nome</code> da classe <code>Modalidade</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>TurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoModalidade(Integer codigoModalidade, Integer empresa, int nivelMontarDados) throws Exception {
        return consultarPorCodigoModalidade(codigoModalidade, empresa, true, nivelMontarDados);
    }

    public List consultarPorCodigoModalidade(Integer codigoModalidade, Integer empresa, boolean excluiAulaColetiva, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Turma.* FROM Turma, Modalidade "
                    + "WHERE Turma.modalidade = Modalidade.codigo \n";
            if (excluiAulaColetiva) {
                sqlStr += " and not turma.aulaColetiva \n";
            }
            if (codigoModalidade != 0) {
                sqlStr += " and modalidade.codigo = " + codigoModalidade;
            }
            sqlStr += "\n ORDER BY Modalidade.nome";

        } else {
            sqlStr = "SELECT Turma.* FROM Turma, Modalidade "
                    + "WHERE Turma.modalidade = Modalidade.codigo ";
            if (excluiAulaColetiva) {
                sqlStr += " and not turma.aulaColetiva ";
            }
            if (codigoModalidade != 0) {
                sqlStr += " and modalidade.codigo = " + codigoModalidade;
            }
            sqlStr += " and Turma.empresa = " + empresa.intValue();
            sqlStr += " ORDER BY Modalidade.nome";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public TurmaVO consultar(String nomeTurma,Integer codigoModalidade, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from Turma \n");
        sql.append("where modalidade = ").append(codigoModalidade).append(" \n");
        if ((codigoEmpresa != null) && (codigoEmpresa >0)){
            sql.append(" and empresa = ").append(codigoEmpresa).append(" \n");
        }
        sql.append("and upper(descricao) = '").append(nomeTurma).append("'");
        sql.append("order by descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }


    public List consultarPorCodigoModalidadeVigenciaMaiorQueHoje(Integer codigoModalidade, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sql = "SELECT\n" +
                "  DISTINCT turma.*\n" +
                "FROM turma\n" +
                "  INNER JOIN modalidade\n" +
                "    ON turma.modalidade = modalidade.codigo\n" +
                "  LEFT JOIN horarioturma\n" +
                "    ON horarioturma.turma = turma.codigo\n" +
                "WHERE not turma.aulaColetiva ";

        if (codigoModalidade != 0) {
            sql += " and modalidade.codigo = " + codigoModalidade + "\n";
        }

        if (empresa != 0) {
            sql += " and Turma.empresa = " + empresa + "\n";
        }

        sql += "      AND\n" +
                "      (turma.datafinalvigencia > '"+Uteis.getDataJDBCTimestamp(Calendario.hoje())+"'\n" +
                "       OR horarioturma.codigo IN\n" +
                "          (SELECT\n" +
                "             DISTINCT horarioturma\n" +
                "           FROM matriculaalunohorarioturma maht\n" +
                "             INNER JOIN horarioturma ht\n" +
                "               ON ht.codigo = maht.horarioturma\n" +
                "             INNER JOIN turma\n" +
                "               ON ht.turma = turma.codigo\n" +
                "           WHERE maht.datafim > '"+Uteis.getDataJDBCTimestamp(Calendario.hoje())+"'\n" +
                "          )\n" +
                "      )\n" +
                "ORDER BY turma.descricao";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public List consultarPorNomeModalidadeIdade(String valorConsulta, Integer idade, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Turma.* FROM Turma, Modalidade WHERE Turma.modalidade = Modalidade.codigo and upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "and turma.idademinima  <= " + idade.intValue() + " and turma.idadeMaxima >= " + idade.intValue() + " ORDER BY Modalidade.nome";
        } else {
            sqlStr = "SELECT Turma.* FROM Turma, Modalidade WHERE Turma.modalidade = Modalidade.codigo and upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "and turma.idademinima  <= " + idade.intValue() + " and turma.idadeMaxima >= " + idade.intValue() + " and Turma.empresa = " + empresa.intValue() + " ORDER BY Modalidade.nome";

        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public List consultarPorNomeProfessor(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Pessoa.*, Colaborador.*, HorarioTurma.* , Turma.* FROM   Colaborador, Pessoa, HorarioTurma, Turma  WHERE  HorarioTurma.Turma = Turma.codigo and Horarioturma.professor = colaborador.codigo and Colaborador.pessoa = pessoa.codigo and upper( pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY pessoa.nome";
        } else {
            sqlStr = "SELECT Pessoa.*, Colaborador.*, HorarioTurma.* , Turma.* FROM   Colaborador, Pessoa, HorarioTurma, Turma  WHERE  HorarioTurma.Turma = Turma.codigo and Horarioturma.professor = colaborador.codigo and Colaborador.pessoa = pessoa.codigo and upper( pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') and Turma.empresa = " + empresa.intValue() + " ORDER BY pessoa.nome";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Turma</code> através do valor do atributo
     * <code>String identificador</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorIdentificador(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Turma WHERE upper( identificador ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY identificador";
        } else {
            sqlStr = "SELECT * FROM Turma WHERE upper( identificador ) like('" + valorConsulta.toUpperCase() + "%') and empresa = " + empresa.intValue() + " ORDER BY identificador";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorDescricaoTurma(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Turma WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        } else {
            sqlStr = "SELECT * FROM Turma WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') and empresa = " + empresa.intValue() + " ORDER BY descricao";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public boolean existemAlunosNaTurma(TurmaVO turmaVO) throws Exception {
        String sql = "SELECT\n" +
                "  count(*) as qtdAlunos\n" +
                "FROM matriculaalunohorarioturma maht\n" +
                "  INNER JOIN horarioturma ht\n" +
                "    ON maht.horarioturma = ht.codigo\n" +
                "  INNER JOIN turma t\n" +
                "    ON ht.turma = t.codigo\n" +
                "WHERE t.codigo = "+turmaVO.getCodigo()+"\n" +
                "      AND datafim > '"+Uteis.getDataJDBC(Calendario.hoje())+"'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return tabelaResultado.next() && tabelaResultado.getInt("qtdAlunos") > 0;
            }
        }
    }

    public boolean existemMatriculasNaTurma(TurmaVO turmaVO) throws Exception {
        String sql = "SELECT\n" +
                "  count(*) as qtdAlunos\n" +
                "FROM matriculaalunohorarioturma maht\n" +
                "  INNER JOIN horarioturma ht\n" +
                "    ON maht.horarioturma = ht.codigo\n" +
                "  INNER JOIN turma t\n" +
                "    ON ht.turma = t.codigo\n" +
                "WHERE t.codigo = "+turmaVO.getCodigo();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return tabelaResultado.next() && tabelaResultado.getInt("qtdAlunos") > 0;
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Turma</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso,Boolean aulaColetiva, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM Turma\n");
        sql.append(" WHERE TRUE");
        if(!UteisValidacao.emptyNumber(valorConsulta)){
            sql.append(" AND codigo = ").append(valorConsulta).append("\n");
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" AND empresa = ").append(empresa).append("\n");
        }
        if(aulaColetiva != null){
            sql.append(" AND aulaColetiva = ").append(aulaColetiva.toString()).append("\n");
        }
        sql.append(" order by codigo ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Turma</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorCodigo(valorConsulta,empresa,controlarAcesso,false, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>Turma</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>TurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNivelTurma(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT Distinct on (turma.codigo) Turma.* FROM Turma  "
                    + "INNER JOIN HorarioTurma on horarioTurma.turma =  turma.codigo "
                    + "INNER JOIN nivelturma on horarioTurma.nivelturma =  nivelturma.codigo and upper (nivelturma.descricao ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "ORDER BY codigo";
        } else {
            sqlStr = "SELECT Distinct on (turma.codigo) Turma.* FROM Turma  "
                    + "INNER JOIN HorarioTurma on horarioTurma.turma =  turma.codigo "
                    + "INNER JOIN nivelturma on horarioTurma.nivelturma =  nivelturma.codigo and upper (nivelturma.descricao ) like('" + valorConsulta.toUpperCase() + "%') "
                    + "WHERE turma.empresa =" + empresa.intValue() + " "
                    + "ORDER BY codigo";
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public TurmaVO consultarPorHorarioTurma(Integer horarioturma, int nivelMontarDados) throws Exception{
        String sqlStr = "";

        sqlStr = "SELECT Distinct on (turma.codigo) Turma.* FROM Turma  "
                + "INNER JOIN HorarioTurma on horarioTurma.turma =  turma.codigo and  horarioTurma.codigo = " + horarioturma;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public TurmaVO consultarPorDiaSemanaHoraInicialHoraFinalAmbienteNivelTurmaProfessorEmpresa(String diaSemana, String horaInicio, String horaFim, Integer ambiente, Integer professor, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select turma.* from horarioturma, turma"
                + " where horarioturma.turma=turma.codigo "
                + " and horarioTurma.diaSemana= '" + diaSemana + "' "
                + " and ((horarioturma.horainicial>'" + horaInicio + "' and horarioturma.horainicial< '" + horaFim + "' ) "
                + " or (horarioturma.horafinal > '" + horaInicio + "' and horarioturma.horafinal< '" + horaFim + "')"
                + " or (horarioturma.horainicial = '" + horaInicio + "' and horarioturma.horafinal = '" + horaFim + "')) "
                + " and horarioTurma.ambiente =" + ambiente.intValue()
                + " and horarioTurma.situacao = 'AT'"
                + " and horarioturma.professor = " + professor.intValue();
        if (empresa.intValue() != 0) {
            sql += " and turma.empresa = " + empresa.intValue();
        }

        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>TurmaVO</code> resultantes da consulta.
     */
    public List<TurmaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, null);
    }

    public List<TurmaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Integer codigoPessoa) throws Exception {
        List<TurmaVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            TurmaVO obj = montarDados(tabelaResultado, nivelMontarDados, codigoPessoa);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private  static TurmaVO montarDadosBasicoGestao(ResultSet dadosSQL, int nivelMontarDados)throws Exception{
        TurmaVO obj = new TurmaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setIdentificador(dadosSQL.getString("identificador"));
        obj.setNovoObj(false);
        return obj;
    }

    private  static TurmaVO montarDadosBasico(ResultSet dadosSQL)throws Exception{
        TurmaVO obj = new TurmaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setIdentificador(dadosSQL.getString("identificador"));
        obj.getModalidade().setCodigo(new Integer(dadosSQL.getInt("modalidade")));
        obj.getEmpresa().setCodigo(new Integer(dadosSQL.getInt("empresa")));
        obj.setDataInicialVigencia(dadosSQL.getDate("dataInicialVigencia"));
        obj.setDataFinalVigencia(dadosSQL.getDate("dataFinalVigencia"));
        obj.setIdadeMinimaMeses(dadosSQL.getInt("idademinimameses"));
        obj.setIdadeMinima(dadosSQL.getInt("idadeMinima"));
        obj.setIdadeMaximaMeses(dadosSQL.getInt("idademaximameses"));
        obj.setIdadeMaxima(dadosSQL.getInt("idadeMaxima"));
        obj.setBloquearMatriculasAcimaLimite(dadosSQL.getBoolean("bloquearMatriculasAcimaLimite"));
        obj.setMonitorada(dadosSQL.getBoolean("monitorada"));
        obj.setMinutosAntecedenciaMarcarAula(dadosSQL.getInt("minutosAntecedenciaMarcarAula"));
        obj.setMinutosAntecedenciaDesmarcarAula(dadosSQL.getInt("minutosAntecedenciaDesmarcarAula"));
        obj.setTipoAntecedenciaMarcarAula(dadosSQL.getInt("tipoAntecedenciaMarcarAula"));
        obj.setBloquearReposicaoAcimaLimite(dadosSQL.getBoolean("bloquearReposicaoAcimaLimite"));
        obj.setPermitirDesmarcarReposicoes(dadosSQL.getBoolean("permitirDesmarcarReposicoes"));
        obj.setIntegracaoSpivi(dadosSQL.getBoolean("integracaospivi"));
        obj.setCapacidade(dadosSQL.getInt("capacidade"));
        obj.setLimiteVagasAgregados(dadosSQL.getInt("limiteVagasAgregados"));
        obj.setDias(dadosSQL.getString("dias"));
        obj.setHorarios(dadosSQL.getString("horarios"));
        obj.setProfessor(dadosSQL.getInt("professor"));
        try {
            obj.setAulaColetiva(dadosSQL.getBoolean("aulacoletiva"));
            obj.setPontosBonus(dadosSQL.getInt("pontosbonus"));
            obj.setValidarRestricoesMarcacao(dadosSQL.getBoolean("validarrestricoesmarcacao"));

            obj.setQtdeNivelOcupacao(dadosSQL.getInt("qtdeNivelOcupacao"));
            obj.setPercDescOcupacaoNivel1(dadosSQL.getDouble("percDescOcupacaoNivel1"));
            obj.setPercDescOcupacaoNivel2(dadosSQL.getDouble("percDescOcupacaoNivel2"));
            obj.setPercDescOcupacaoNivel3(dadosSQL.getDouble("percDescOcupacaoNivel3"));
            obj.setPercDescOcupacaoNivel4(dadosSQL.getDouble("percDescOcupacaoNivel4"));
            obj.setPercDescOcupacaoNivel5(dadosSQL.getDouble("percDescOcupacaoNivel5"));
            obj.setMinutosAposInicioApp(dadosSQL.getInt("minutosAposInicioApp"));
            obj.setPermiteAlunoOutraEmpresa(dadosSQL.getBoolean("permiteAlunoOutraEmpresa"));
            obj.setProdutoGymPass(dadosSQL.getInt("produtoGymPass"));
            obj.setIdClasseGymPass(dadosSQL.getInt("idClasseGymPass"));
            obj.setUrlTurmaVirtual(dadosSQL.getString("urlTurmaVirtual"));

            obj.setMensagem(dadosSQL.getString("mensagem"));
            obj.setBonificacao(dadosSQL.getDouble("bonificacao"));
            obj.setMeta(dadosSQL.getDouble("meta"));

        } catch (Exception e) {}
        try{
            obj.setNaoValidarModalidadeContrato(dadosSQL.getBoolean("naoValidarModalidadeContrato"));
        }catch (Exception ignore){}
        obj.setPermitirAulaExperimental(dadosSQL.getBoolean("permitirAulaExperimental"));
        try {
            obj.setVisualizarProdutosGympass(dadosSQL.getBoolean("visualizarProdutosGympass"));
        }catch (Exception ignore){}
        try {
            obj.setVisualizarProdutosTotalpass(dadosSQL.getBoolean("visualizarProdutosTotalpass"));
        }catch (Exception ignore){}
        try {
            obj.setBloquearLotacaoFutura(dadosSQL.getBoolean("bloquearLotacaoFutura"));
        }catch (Exception ignore){}
        obj.setNovoObj(false);
        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>TurmaVO</code>.
     * @return  O objeto da classe <code>TurmaVO</code> com os dados devidamente montados.
     */
    public TurmaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        return montarDados(dadosSQL, nivelMontarDados, null);
    }

    public TurmaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Integer codigoPessoa) throws Exception {
        TurmaVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA){
            return obj;
        }
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_WS){
            montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        obj.setHorarioTurmaVOs(new HorarioTurma(con).consultarHorarioTurmas(obj.getCodigo(), nivelMontarDados, codigoPessoa));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }

        montarDadosModalidade(obj, nivelMontarDados, con);
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ModalidadeVO</code> relacionado ao objeto <code>TurmaVO</code>.
     * Faz uso da chave primária da classe <code>ModalidadeVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     * @param con
     */
    public static void montarDadosModalidade(TurmaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getModalidade().getCodigo().intValue() == 0) {
            obj.setModalidade(new ModalidadeVO());
            return;
        }
        obj.setModalidade(new Modalidade(con).consultarPorChavePrimaria(obj.getModalidade().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ModalidadeVO</code> relacionado ao objeto <code>TurmaVO</code>.
     * Faz uso da chave primária da classe <code>ModalidadeVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public void montarDadosEmpresa(TurmaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getEmpresa().getCodigo() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        obj.setEmpresa(new Empresa(con).consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por adicionar um objeto da <code>HorarioTurmaVO</code> no Hashtable <code>HorarioTurmas</code>.
     * Neste Hashtable são mantidos todos os objetos de HorarioTurma de uma determinada Turma.
     * @param obj  Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjHorarioTurmas(HorarioTurmaVO obj) throws Exception {
        getHorarioTurmas().put(obj.getDiaSemana() + " " + obj.getHoraInicial() + "" + obj.getHoraFinal() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>HorarioTurmaVO</code> do Hashtable <code>HorarioTurmas</code>.
     * Neste Hashtable são mantidos todos os objetos de HorarioTurma de uma determinada Turma.
     * @param obj Atributo da classe <code>HorarioTurmaVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjHorarioTurmas(HorarioTurmaVO obj) throws Exception {
        getHorarioTurmas().remove(obj.getDiaSemana() + " " + obj.getHoraInicial() + "" + obj.getHoraFinal() + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>TurmaVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public TurmaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        TurmaVO eCache = (TurmaVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM Turma WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Turma ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public Hashtable getHorarioTurmas() {
        return (horarioTurmas);
    }

    public void setHorarioTurmas(Hashtable horarioTurmas) {
        this.horarioTurmas = horarioTurmas;
    }

    public String consultarJSON(Integer empresa, String tipoTurma) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa, tipoTurma).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("identificador"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("modalidade"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\",");
                json.append("\"").append(rs.getString("idademinima")).append("\",");
                json.append("\"").append(rs.getString("idademaxima")).append("\",");
                json.append("\"").append(rs.getBoolean("integracaoSpivi") ? "Sim" : "Não").append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT tu.codigo, tu.identificador, tu.descricao, \n" + "mod.nome AS modalidade, emp.nome AS empresa, tu.idademinima, tu.idademaxima\n" + "FROM turma tu\n" + "  LEFT JOIN modalidade mod ON tu.modalidade = mod.codigo\n" + "  LEFT JOIN empresa emp ON tu.empresa = emp.codigo");
        sql.append("  WHERE tu.usuariodesativou is null AND not tu.aulaColetiva ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND tu.empresa = ? ");
        }
        sql.append("  ORDER BY tu.descricao");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        return sqlConsultar;
    }

    private PreparedStatement getPS(Integer empresa, String tipoTurma) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT tu.codigo, tu.identificador, tu.descricao, \n" +
                "mod.nome AS modalidade, emp.nome AS empresa, tu.idademinima, tu.integracaoSpivi, tu.idademaxima, \n" +
                "tu.datafinalvigencia, tu.datainicialvigencia\n" +
                "FROM turma tu\n" +
                "  LEFT JOIN modalidade mod ON tu.modalidade = mod.codigo\n" +
                "  LEFT JOIN empresa emp ON tu.empresa = emp.codigo");
        sql.append("  WHERE tu.usuariodesativou is null AND not tu.aulaColetiva ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND tu.empresa = ? ");
        }

        Date dataHoje = Calendario.getDataComHoraZerada(Calendario.hoje());
        if ("VI".equals(tipoTurma)) {
            sql.append(" and '").append(Uteis.getDataJDBC(dataHoje)).append("' between datainicialvigencia and datafinalvigencia");
        } else if("NVI".equals(tipoTurma)){
            sql.append(" and '").append(Uteis.getDataJDBC(dataHoje)).append("' not between datainicialvigencia and datafinalvigencia");
        }

        sql.append("  ORDER BY tu.descricao");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        return sqlConsultar;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa,String tipoTurma) throws SQLException, Exception {

        List lista;
        try (ResultSet rs = getPS(empresa, tipoTurma).executeQuery()) {
            lista = new ArrayList();

            while (rs.next()) {

                TurmaVO turma = new TurmaVO();
                String geral = rs.getString("codigo") + rs.getString("identificador") + rs.getString("descricao") + rs.getString("modalidade") + rs.getString("empresa") + rs.getString("idademinima") + rs.getString("idademaxima");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    turma.setCodigo(rs.getInt("codigo"));
                    turma.setIdentificador(rs.getString("identificador"));
                    turma.setDescricao(rs.getString("descricao"));
                    turma.getModalidade().setNome(rs.getString("modalidade"));
                    turma.getEmpresa().setNome(rs.getString("empresa"));
                    turma.setIdadeMinima(rs.getInt("idademinima"));
                    turma.setIdadeMaxima(rs.getInt("idademaxima"));
                    turma.setDataInicialVigencia(rs.getDate("datainicialvigencia"));
                    turma.setDataFinalVigencia(rs.getDate("datafinalvigencia"));
                    lista.add(turma);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Identificador")) {
            Ordenacao.ordenarLista(lista, "identificador");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Modalidade")) {
            Ordenacao.ordenarLista(lista, "modalidade_Apresentar");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        } else if (campoOrdenacao.equals("Idade Mínima")) {
            Ordenacao.ordenarLista(lista, "idadeMinima");
        } else if (campoOrdenacao.equals("Idade Máxima")) {
            Ordenacao.ordenarLista(lista, "idadeMaxima");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public List<TurmaVO> consultar(boolean somenteVigentes, boolean somenteAulaCheia, Integer modalidade, Integer empresa, int nivelMontarDados, boolean somenteNaoVigentes) throws Exception {
        return consultar(somenteVigentes, somenteAulaCheia, modalidade, empresa, nivelMontarDados, somenteNaoVigentes, 0, null, null);
    }

    public List<TurmaVO> consultar(boolean somenteVigentes, boolean somenteAulaCheia, Integer modalidade, Integer empresa, int nivelMontarDados, boolean somenteNaoVigentes, int idade, String modalidades, Integer codigoPessoa) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT * FROM Turma\n");
        if(somenteVigentes){
            sql.append(" WHERE dataFinalVigencia >= ? \n");
        } else if (somenteNaoVigentes) {
            sql.append(" WHERE dataFinalVigencia < ? \n");
        }

        if(empresa != null && empresa > 0){
            sql.append(sql.toString().contains("WHERE") ? " AND " : " WHERE ").append(" empresa = ? \n");
        }
        if(modalidade != null && modalidade > 0){
            sql.append(sql.toString().contains("WHERE") ? " AND " : " WHERE ").append(" modalidade = ? \n");
        }

        if ((modalidade == null || modalidade == 0) && !UteisValidacao.emptyString(modalidades)) {
            sql.append(sql.toString().contains("WHERE") ? " AND " : " WHERE ").append(" modalidade in (" + modalidades + ") \n");
        }

        if(somenteAulaCheia){
            sql.append("  AND aulacoletiva = "+somenteAulaCheia+" \n");
        }else{
            sql.append("  AND aulacoletiva = "+somenteAulaCheia+" \n");
        }

        if(idade > 0){
            sql.append("  AND "+idade+ " between idademinima and idademaxima \n");
            sql.append("  ORDER BY modalidade asc\n");
        }

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            int i = 1;
            if (somenteVigentes || somenteNaoVigentes) {
                stm.setDate(i++, Uteis.getDataJDBC(Calendario.hoje()));
            }
            if (empresa != null && empresa > 0) {
                stm.setInt(i++, empresa);
            }
            if (modalidade != null && modalidade > 0) {
                stm.setInt(i++, modalidade);
            }
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, codigoPessoa));
            }
        }
    }

    @Override
    public Map<String, List<AgendaTotalJSON>> consultarHorariosTurmaParaAgenda(Date inicio,
                                                                               Integer empresa, List<Integer> modalidades,
                                                                               boolean somenteColetivas, Integer codigoHorario,
                                                                               Integer matricula, boolean isApp) throws Exception{
        return this.consultarHorariosTurmaParaAgenda(inicio, empresa, modalidades, somenteColetivas, codigoHorario,
                matricula, isApp, false);
    }

    @Override
    public Map<String, List<AgendaTotalJSON>> consultarHorariosTurmaParaAgenda(Date inicio,
                                                                               Integer empresa, List<Integer> modalidades,
                                                                               boolean somenteColetivas, Integer codigoHorario,
                                                                               Integer matricula, boolean isApp,
                                                                               boolean isAgendaOnline) throws Exception{

        Uteis.logarDebug("================ Todos valores recebidos no consultarHorariosTurmaParaAgenda() ================");
        Uteis.logarDebug("================ \nINICIO: " + inicio + " \nEMPRESA: " + empresa + " \nMODALIDADES: " + modalidades + " \nSOMENTECOLETIVAS: " + somenteColetivas +
                " \nCODIGOHORARIO: " + codigoHorario + " \nMATRICULA: " + matricula + " \nISAPP: " + isApp + " \nISAGENDAONLINE: " + isAgendaOnline);

        Map<String,List<AgendaTotalJSON>> mapa = new HashMap<String,List<AgendaTotalJSON>>();
        StringBuilder sql = new StringBuilder();
        String validaIdade = "";
        sql.append(" SELECT t.permitirAulaExperimental, t.permiteAlunoOutraEmpresa, t.naovalidarmodalidadecontrato, ");
        if (somenteColetivas) {
            sql.append(" (SELECT COUNT(*) FROM alunohorarioturma WHERE horarioturma = ht.codigo AND dia = '").append(Uteis.getDataJDBC(inicio)).append("') AS ocupacao, ");
        } else {
            sql.append(" t.ocupacao, ");
        }
        sql.append(" COALESCE(edi.capacidade,  ht.nrmaximoaluno) AS nrmaximoaluno, t.codigo as turma, t.empresa,t.datainicialvigencia, t.datafinalvigencia,t.mensagem,t.meta,t.bonificacao,t.pontosbonus, t.niveis, \n");
        sql.append(" t.idademinima as idademinima, t.idademinimameses as idademinimameses, t.idademaxima as idademaxima, t.idademaximameses as idademaximameses, \n");
        sql.append(" t.tipotolerancia, t.identificador,t.aulaColetiva, t.integracaospivi, t.urlVideoYoutube, t.fotoKey, m.codigo as modalidade, m.nome as nomemodalidade, p.fotokey as professorfotokey, m.fotokey as fotoModalidade, p.codigo as professorCodigoPessoa,\n");
        sql.append(" ht.diasemana, ht.toleranciaentradaminutos,ht.professor,p.nome as nomeprofessor, ht.ambiente,a.descricao as nomeambiente,  \n");
        sql.append(" ht.situacao, ht.dataentrouturma, ht.datasaiuturma, \n");
        sql.append(" ht.horainicial, ht.horafinal, ht.codigo as horarioturma,  ht.qtdemaximaalunoexperimental as qtdemaximaalunoexperimental, n.codigo as nivel, n.descricao as nomenivel, t.bloquearMatriculasAcimaLimite \n");
        sql.append(",exists(select dht.codigo from demandahorarioturma dht inner join cliente cli on cli.codigo = dht.cliente where cli.codigomatricula = ");
        sql.append(matricula);
        sql.append(" AND dht.horarioturma = ht.codigo) as jaMarcouEuQuero, t.validarrestricoesmarcacao, t.minutosAposInicioApp, ");
        sql.append(" t.mapaequipamentos ");

        if (isAgendaOnline) {
            sql.append("\n, prod.valorfinal as valorproduto, prod.codigo as codigoProduto ");
        }

        sql.append(" FROM turma t \n");
        sql.append(" INNER JOIN empresa emp ON t.empresa = emp.codigo \n");
        sql.append(" INNER JOIN modalidade m ON m.codigo = t.modalidade \n");
        if(modalidades != null && !modalidades.isEmpty()){
            sql.append(" AND m.codigo IN (");
            String mods = "";
            for(Integer mod : modalidades){
                mods = mods+","+mod;
            }
            sql.append(mods.replaceFirst(",", "")).append(") \n");
        }
        sql.append(" INNER JOIN horarioturma ht ON ht.turma = t.codigo \n");

        if (isAgendaOnline) {
            sql.append(" and ht.situacao = 'AT' ");
        }
        sql.append(" INNER JOIN ambiente a ON ht.ambiente = a.codigo \n");
        sql.append(" INNER JOIN nivelturma n ON ht.nivelturma = n.codigo \n");
        sql.append(" INNER JOIN colaborador c ON c.codigo = ht.professor \n");
        sql.append(" INNER JOIN pessoa p ON c.pessoa = p.codigo \n");
        sql.append(" LEFT JOIN (\n");
        sql.append("    SELECT DISTINCT ON (horarioturma, capacidade) *\n");
        sql.append("    FROM edicaoaulatemporaria\n");
        sql.append("    WHERE diaaula >= '").append(Uteis.getDataJDBC(inicio)).append("'\n");
        sql.append("      AND (\n");
        sql.append("            (tipoalteracao = 'SOMENTE_AULA' AND diaaula = '").append(Uteis.getDataJDBC(inicio)).append("') OR\n");
        sql.append("            (tipoalteracao = 'DETERMINADA_DATA' AND limite >= '").append(Uteis.getDataJDBC(inicio)).append("') OR\n");
        sql.append("            (tipoalteracao = 'ESSA_PROXIMA' AND limite >= '").append(Uteis.getDataJDBC(inicio)).append("') OR\n");
        sql.append("            (tipoalteracao = 'TODAS')\n");
        sql.append("          )\n");
        sql.append("    ORDER BY horarioturma, capacidade\n");
        sql.append(") edi ON edi.horarioturma = ht.codigo \n");
        if (isAgendaOnline) {
            sql.append(" INNER JOIN aulavendasonline avo ON ht.turma = avo.codigoturma and avo.ativo and avo.data_exclusao is null ");
            sql.append(" INNER JOIN produto prod ON prod.codigo = avo.codigoproduto ");
        }

        sql.append(" WHERE t.datafinalvigencia >= '").append(Uteis.getDataJDBC(inicio)).append("'");
        if (isApp) {
            sql.append(" AND m.utilizarturma is true ");
        }
        sql.append(" and emp.ativa \n");
        sql.append(somenteColetivas ? " and ht.situacao = 'AT' \n" : "");
        sql.append(" AND (t.usuariodesativou IS NULL OR t.datafinalvigencia >= '").append(Uteis.getDataJDBC(inicio)).append("' ) \n");
        sql.append(" AND (ht.situacao = 'AT' or ht.datasaiuturma is not null) \n");
        if(UteisValidacao.notEmptyNumber(codigoHorario)){
            sql.append(" AND ht.codigo = ").append(codigoHorario);
        }

        if(UteisValidacao.notEmptyNumber(empresa)){
            sql.append(" AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL)");
        }

        if(somenteColetivas){
            sql.append(" AND t.aulaColetiva ");
        }

        if(isApp){
            sql.append(" AND ht.liberadoMarcacaoApp ");
        }

        if (matricula != null) {
            try (ResultSet dataNascimento = criarConsulta("select p.datanasc from pessoa as p inner join cliente as c on c.pessoa = p.codigo where c.codigomatricula = " + matricula, con)) {
                if (dataNascimento.next()) {
                    Date dataNasc = dataNascimento.getDate("datanasc");
                    if (dataNasc == null) {
                        dataNasc = Calendario.hoje();
                    }
                    String dataNascCalcular = Uteis.getDataAplicandoFormatacao(dataNasc, "yyyy-MM-dd");
                    validaIdade = Uteis.CalculaIdadeComMeses(dataNascCalcular);
                }
            }
        }

        Uteis.logarDebug("================ SQL FULL do consultarHorariosTurmaParaAgenda() ================ \n" + sql.toString());
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                if (matricula != null) {
                    String[] split = validaIdade.split("-");
                    int idadeAlunoAno = 0;
                    int idadeAlunoMes = 0;
                    if(isNotBlank(split[0])) {
                        idadeAlunoAno = Integer.parseInt(split[0]);
                        idadeAlunoMes = Integer.parseInt(split[1]);
                    }
                    boolean isIdadeAlunoValida = validaIdadeAluno(
                            idadeAlunoAno,
                            idadeAlunoMes,
                            rs.getInt("idademinima"),
                            rs.getInt("idademinimameses"),
                            rs.getInt("idademaxima"),
                            rs.getInt("idademaximameses")
                    );
                    if (!isIdadeAlunoValida) {
                        continue;
                    }
                }
                List<AgendaTotalJSON> lista = mapa.get(rs.getString("diasemana"));
                if (lista == null) {
                    lista = new ArrayList<AgendaTotalJSON>();
                    mapa.put(rs.getString("diasemana"), lista);
                }
                AgendaTotalJSON item = new AgendaTotalJSON();
                item.setOcupacao(rs.getInt("ocupacao"));
                item.setEmpresa(rs.getInt("empresa"));
                item.setAulaCheia(rs.getBoolean("aulaColetiva"));
                item.setIntegracaoSpivi(rs.getBoolean("integracaoSpivi"));
                item.setPermitirAulaExperimental(rs.getBoolean("permitirAulaExperimental"));
                item.setInicio(rs.getString("horainicial"));
                item.setFim(rs.getString("horafinal"));
                item.setTitulo(rs.getString("identificador"));
                item.setTipo(rs.getString("nomemodalidade"));
                item.setCodigoTipo(rs.getInt("modalidade"));
                item.setLocal(rs.getString("nomeambiente"));
                item.setCodigoLocal(rs.getInt("ambiente"));
                item.setNrVagas(rs.getInt("nrmaximoaluno"));
                item.setResponsavel(rs.getString("nomeprofessor"));
                item.setCodigoResponsavel(rs.getInt("professor"));
                item.setCodigoPessoaResponsavel(rs.getInt("professorCodigoPessoa"));
                item.setId(String.valueOf(rs.getInt("horarioturma")));
                item.setCodigoNivel(rs.getInt("nivel"));
                item.setNivel(rs.getString("nomenivel"));
                item.setInicioVigencia(rs.getDate("datainicialvigencia"));
                item.setFimVigencia(rs.getDate("datafinalvigencia"));
                item.setMensagem(rs.getString("mensagem"));
                item.setMeta(rs.getDouble("meta"));
                item.setTolerancia(rs.getInt("toleranciaentradaminutos"));
                int tipotolerancia = rs.getInt("tipotolerancia");
                item.setTipoTolerancia(UteisValidacao.emptyNumber(tipotolerancia) ? TipoToleranciaAulaEnum.APOS_INICIO.getCodigo() : tipotolerancia);
                item.setTolerancia(rs.getInt("toleranciaentradaminutos"));
                item.setPontosBonus(rs.getInt("pontosbonus"));
                item.setBonificacao(rs.getDouble("bonificacao"));
                item.setJaMarcouEuQuero(rs.getBoolean("jamarcoueuquero"));
                item.setValidarRestricoesMarcacao(rs.getBoolean("validarrestricoesmarcacao"));
                item.setFotoProfessor(rs.getString("professorfotokey"));
                final String fotoModalidade = rs.getString("fotoModalidade");
                if (!UteisValidacao.emptyString(fotoModalidade)) {
                    item.setFotoModalidade(String.format("%s/%s", new Object[]{
                            PropsService.getPropertyValue(PropsService.urlFotosNuvem), fotoModalidade}));
                }
                item.setToleranciaApresentarApp(rs.getInt("minutosAposInicioApp"));
                item.setPermiteAlunoOutraEmpresa(rs.getBoolean("PermiteAlunoOutraEmpresa"));
                item.setCodigoTurma(rs.getInt("turma"));
                item.setSituacao(rs.getString("situacao"));
                item.setDataEntrouTurma(rs.getTimestamp("dataentrouturma"));
                item.setDataSaiuTurma(rs.getTimestamp("datasaiuturma"));

                try{
                    item.setUrlVideoYoutube(rs.getString("urlVideoYoutube"));
                }catch (Exception e){
                }

                if (isAgendaOnline) {
                    item.setValorProduto(rs.getDouble("valorproduto"));
                    item.setCodigoProduto(rs.getInt("codigoProduto"));
                }

                try{
                    item.setNaoValidarModalidadeContrato(rs.getBoolean("naoValidarModalidadeContrato"));
                }catch (Exception ignore){}

                if(Uteis.resultSetContemColuna(rs, "fotoKey") && isNotBlank(rs.getString("fotoKey"))){
                    item.setImageUrl(Uteis.getPaintFotoDaNuvem(rs.getString("fotoKey")));
                }
                item.setIdadeMinima(rs.getInt("idademinima"));
                item.setIdadeMinimaMeses(rs.getInt("idademinimameses"));
                item.setIdadeMaxima(rs.getInt("idademaxima"));
                item.setIdadeMaximaMeses(rs.getInt("idademaximameses"));
                item.setQtdeMaximaAlunoExperimental(rs.getInt("qtdemaximaalunoexperimental"));
                item.setNiveis(rs.getString("niveis"));
                item.setBloquearMatriculasAcimaLimite(rs.getBoolean("bloquearMatriculasAcimaLimite"));

                try{
                    item.setMapaEquipamentos(rs.getString("mapaEquipamentos"));
                }catch (Exception ignore){}

                lista.add(item);
            }
        }
        Uteis.logarDebug("================ Cheguei no fim do consultarHorariosTurmaParaAgenda() vou imprimir o mapa montado ================");
        Uteis.logarDebug(mapa == null ? "================ o mapa montado está null" : "================ o mapa montado possui registros: " + mapa.size());
        return mapa;
    }

    private boolean validaIdadeAluno(int idadeAnoAluno, int idadeMesAluno, int idadeAnoMinima,
                                     int idadeMesMinima, int idadeAnoMaxima, int idadeMesMaxima) throws SQLException {
        if ((idadeAnoAluno < idadeAnoMinima) || (idadeAnoMinima == idadeAnoAluno && idadeMesAluno < idadeMesMinima)) {
            return false;
        } else if ((idadeAnoAluno > idadeAnoMaxima) || idadeAnoAluno == idadeAnoMaxima && idadeMesAluno > idadeMesMaxima){
            return false;
        }
        return true;
    }

    @Override
    public Integer codigoNivelTurma() throws Exception{
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from nivelturma  where descricao = 'SN'", getCon())) {
            if (rs.next()) {
                return rs.getInt("codigo");
            }
            try (ResultSet rs2 = SuperFacadeJDBC.criarConsulta("select max(codigo) as codigo from nivelturma ", getCon())) {
                if (rs2.next()) {
                    return rs2.getInt("codigo");
                }
            }
        }
        return null;
    }

    @Override
    public List<TurmaAulaCheiaJSON> obterAulasColetivasPaginada(Integer empresa, ListaPaginadaTO paginadoLista, JSONObject filtros) throws Exception{

        StringBuilder sql = retornaSelectConsultaBasicaV2();
        sql.append(retornaFromConsultaBasica(empresa, filtros));
        String ordenacao = null;
        if (filtros.has("ordenacao")) {
            sql.append(" order by  t.descricao "+filtros.get("ordenacao").toString().replace("[","").replace("]","").replace("\"", "") );
        } else if(!UteisValidacao.emptyString(paginadoLista.getOrderBy())) {
            sql.append(" order by  ").append(getSqlOrderBy(paginadoLista.getOrderBy()));
            sql.append(paginadoLista.isOrderByDesc() ? " desc" : " asc");
        } else {
            sql.append(" order by  t.descricao asc ");
        }

        sql.append(" LIMIT "+paginadoLista.getLimit());
        sql.append(" OFFSET "+paginadoLista.getOffset());

        paginadoLista.setCount(SuperFacadeJDBC.contar(" SELECT COUNT(*) "+ retornaFromConsultaBasica(empresa, filtros).toString() , con));
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            return montarListaTurmasAula(rs);
        }
    }

    private String getSqlOrderBy(String orderBy) {
        switch (orderBy) {
            case "id":
                return "t.codigo";
            case "nome":
                return "t.descricao";
            case "professor.nome":
                return "pc.nome";
            case "modalidade.nome":
                return "m.nome";
            case "ambiente.nome":
                return "a.descricao";
            case "dias":
                return "t.dias";
            case "dataInicio":
                return "t.datainicialvigencia";
            case "dataFim":
                return "t.datafinalvigencia";
        }
        return "";
    }

    public List<TurmaAulaCheiaJSON> obterAulasColetivasSemPontuacaoaAtivaPaginada(Integer empresa, ListaPaginadaTO paginadorLista) throws Exception{
        StringBuilder sql = retornaFromConsultaBasica(empresa, null);
        sql.append(" AND t.datafinalvigencia > NOW() ");
        sql.append(" AND t.codigo NOT IN (SELECT i.chaveestrangeira FROM itemcampanha i where i.pontos > 0 AND i.tipoitem="+ TipoItemCampanhaEnum.AULA.getCodigo()+(empresa>0?" AND i.empresa="+empresa:"")+")");

        paginadorLista.setCount(SuperFacadeJDBC.contar(" SELECT COUNT(*) "+ sql , con));
        sql.append(" ORDER BY t.DESCRICAO ").append(!paginadorLista.isOrderByDesc()?" ASC ":"");
        sql.append(" LIMIT "+paginadorLista.getLimit());
        sql.append(" OFFSET "+paginadorLista.getOffset());

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(retornaSelectConsultaBasica() + sql.toString(), con)) {
            return montarListaTurmasAula(rs);
        }
    }

    @Override
    public  List<TurmaAulaCheiaJSON> obterAulasColetivas(Integer empresa)throws Exception{
        StringBuilder sql = retornaSelectConsultaBasica();
        sql.append(retornaFromConsultaBasica(empresa, null));
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {

            return montarListaTurmasAula(rs);
        }

    }

    private List<TurmaAulaCheiaJSON> montarListaTurmasAula(ResultSet rs) throws Exception {
        List<TurmaAulaCheiaJSON> aulas = new ArrayList<TurmaAulaCheiaJSON>();
        while (rs.next()) {
            aulas.add(montarDadosTurmaAulaCheiaJSON(rs,false));
        }
        return aulas;
    }

    private TurmaAulaCheiaJSON montarDadosTurmaAulaCheiaJSON(ResultSet resultSet,boolean tvAula)throws Exception{
        TurmaAulaCheiaJSON turma = new TurmaAulaCheiaJSON();
        turma.setCodigo(resultSet.getInt("turma"));
        turma.setNome(resultSet.getString("identificador"));
        turma.setOcupacao(resultSet.getInt("ocupacao"));
        turma.setModalidade(resultSet.getInt("modalidade"));
        turma.setAmbiente(resultSet.getInt("ambiente"));
        turma.setNomeModalidade(resultSet.getString("nomemodalidade"));
        turma.setNomeAmbiente(resultSet.getString("nomeambiente"));
        turma.setInicio(Uteis.getDataAplicandoFormatacao(resultSet.getDate("datainicialvigencia"), "dd/MM/yyyy"));
        turma.setFim(Uteis.getDataAplicandoFormatacao(resultSet.getDate("datafinalvigencia"), "dd/MM/yyyy"));
        turma.setEmpresa(resultSet.getInt("empresa"));
        turma.setDias(resultSet.getString("dias"));
        turma.setHorarios(resultSet.getString("horarios"));
        turma.setMensagem(resultSet.getString("mensagem"));
        turma.setCapacidade(resultSet.getInt("capacidade"));
        turma.setLimiteVagasAgregados(resultSet.getInt("limiteVagasAgregados"));
        turma.setMeta(resultSet.getDouble("meta"));
        turma.setPontosBonus(resultSet.getInt("pontosbonus"));
        turma.setTolerancia(resultSet.getInt("tolerancia"));
        turma.setUrlVideoYoutube(resultSet.getString("urlVideoYoutube"));
        try {
            turma.setVisualizarProdutosGympass(resultSet.getBoolean("visualizarProdutosGympass"));
        }catch (Exception ignore){}
        try {
            turma.setVisualizarProdutosTotalpass(resultSet.getBoolean("visualizarProdutosTotalpass"));
        }catch (Exception ignore){}
        try {
            turma.setPermiteFixar(resultSet.getBoolean("permiteFixar"));
        }catch (Exception ignore){}
        try {
            turma.setAulaIntegracaoSelfloops(resultSet.getBoolean("aulaIntegracaoSelfloops"));
        }catch (Exception ignore){}
        try {
            if (Uteis.resultSetContemColuna(resultSet, "tipotolerancia")) {
                int tipotolerancia = resultSet.getInt("tipotolerancia");
                turma.setTipoTolerancia(UteisValidacao.emptyNumber(tipotolerancia) ? TipoToleranciaAulaEnum.APOS_INICIO.getCodigo() : tipotolerancia);
            } else {
                turma.setTipoTolerancia(TipoToleranciaAulaEnum.APOS_INICIO.getCodigo());
            }
        } catch (Exception e) {
            turma.setTipoTolerancia(TipoToleranciaAulaEnum.APOS_INICIO.getCodigo());
        }
        turma.setBonificacao(resultSet.getDouble("bonificacao"));
        turma.setProfessor(resultSet.getInt("professor"));
        if (Uteis.resultSetContemColuna(resultSet, "nomeprofessor")) {
            turma.setNomeProfessor(resultSet.getString("nomeprofessor"));
        } else {
            turma.setNomeProfessor("");
        }
        if (tvAula) {
            turma.setProfessorCodigoPessoa(resultSet.getInt("professorCodigoPessoa"));
            turma.setHorarioCodigo(resultSet.getInt("horariocodigo"));
        }
        turma.setValidarRestricoesMarcacao(resultSet.getBoolean("validarrestricoesmarcacao"));
        try{
            turma.setNaoValidarModalidadeContrato(resultSet.getBoolean("naoValidarModalidadeContrato"));
        }catch (Exception ignore){}
        try{
            turma.setNiveis(resultSet.getString("niveis"));
            turma.setIdadeMaximaAnos(resultSet.getInt("idademaxima"));
            turma.setIdadeMaximaMeses(resultSet.getInt("idademaximameses"));
            turma.setIdadeMinimaAnos(resultSet.getInt("idademinima"));
            turma.setIdadeMinimaMeses(resultSet.getInt("idademinimameses"));
        }catch (Exception ignore){}
        turma.setIntegracaoSpivi(resultSet.getBoolean("integracaoSpivi"));
        if(Uteis.resultSetContemColuna(resultSet,"professorfotokey"))
            turma.setProfessorFotokey(resultSet.getString("professorFotokey"));
        if(Uteis.resultSetContemColuna(resultSet,"modalidadefotokey"))
            if(Uteis.resultSetContemColuna(resultSet,"professorfotokey")){
                turma.setProfessorFotokey(resultSet.getString("professorfotokey"));
            }
        if(Uteis.resultSetContemColuna(resultSet,"modalidadefotokey")){
            turma.setModalidadeFotokey(resultSet.getString("modalidadeFotokey"));
        }
        if(Uteis.resultSetContemColuna(resultSet,"produtogympass")){
            turma.setProdutoGymPass(resultSet.getInt("produtogympass"));
        }
        if(Uteis.resultSetContemColuna(resultSet,"idclassegympass")){
            turma.setIdClasseGymPass(resultSet.getInt("idclassegympass"));
        }
        if(Uteis.resultSetContemColuna(resultSet,"urlturmavirtual")){
            turma.setUrlTurmaVirtual(resultSet.getString("urlturmavirtual"));
        }
        if(Uteis.resultSetContemColuna(resultSet,"fotokey") && isNotBlank(resultSet.getString("fotokey"))){
            turma.setFotoKey(resultSet.getString("fotokey"));
        }
        if (Uteis.resultSetContemColuna(resultSet, "tipoReservaEquipamento")) {
            turma.setTipoReservaEquipamento(resultSet.getString("tipoReservaEquipamento"));
        } else {
            turma.setTipoReservaEquipamento(null);
        }

        if (Uteis.resultSetContemColuna(resultSet, "mapaEquipamentos")) {
            turma.setMapaEquipamentos(resultSet.getString("mapaEquipamentos"));
        } else {
            turma.setMapaEquipamentos(null);
        }

        return  turma;
    }
    public String desativarAulaColetiva(Integer codigoAula, Integer usuario) throws Exception{

        String update = "UPDATE turma SET datafinalvigencia = '"+Uteis.getDataFormatoBD(Uteis.somarDias(Calendario.hoje(), -1))+"', usuariodesativou = "+usuario
                +" WHERE codigo = "+codigoAula;
        SuperFacadeJDBC.executarConsulta(update, con);
        update = "update horarioturma set  datasaiuturma = '"+Uteis.getDataHoraJDBC(Calendario.hoje(), Uteis.getHoraAtual())+"' where turma = "+codigoAula+" and ativo = true and situacao = 'AT'";
        SuperFacadeJDBC.executarConsulta(update, con);
        update = "update horarioturma set  situacao = 'IN' where turma = "+codigoAula;
        SuperFacadeJDBC.executarConsulta(update, con);
        update = "update horarioturma set  ativo = false where turma = "+codigoAula;
        SuperFacadeJDBC.executarConsulta(update, con);
        return "ok";
    }

    public String obterFotoKeyTurma(Integer codigoTurma) throws Exception {
        String sql = "SELECT fotokey FROM turma WHERE codigo = " + codigoTurma;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            while (rs.next()) {
                return rs.getString("fotokey");
            }
        }
        return "";
    }

    public void salvarFotoKeyTurma(Integer codigoTurma, String fotoKey) throws Exception{
        String update = "UPDATE turma SET fotokey = '"+fotoKey+"' WHERE codigo = "+codigoTurma;
        SuperFacadeJDBC.executarConsulta(update, con);
    }

    public boolean existeVinculoFuturoAlunoAulaColetiva(Integer codigoAula) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM alunohorarioturma a \n");
        sql.append("INNER JOIN horarioturma h ON a.horarioturma = h.codigo \n");
        sql.append("WHERE h.turma = ").append(codigoAula).append(" \n");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                Date dia = Calendario.getDataComHora(rs.getDate("dia"), rs.getString("horainicial"));
                if(Calendario.maiorComHora(dia, new Date())) {
                    return true;
                }
            }
        }

        return false;
    }

    public HorarioTurmaVO obterAulaExperimentalPassivoIndicado(Date dia, Integer passivo, Integer indicado, Integer cliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT horarioTurma, dia FROM alunohorarioturma \n");
        sql.append("WHERE 1=1 AND ").append(" \n");
        if (!UteisValidacao.emptyNumber(passivo)) {
            sql.append(" passivo = ").append(passivo).append(" \n");
        } else if(!UteisValidacao.emptyNumber(indicado)) {
            sql.append(" indicado = ").append(indicado).append(" \n");
        }else{
            sql.append(" cliente = ").append(cliente).append(" \n");
        }
        sql.append(" AND dia = '").append(Uteis.getDataJDBC(dia)).append("' \n");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                HorarioTurmaVO horarioExperimental = new HorarioTurmaVO();
                horarioExperimental.setCodigo(rs.getInt("horarioTurma"));
                horarioExperimental.setDataAula(rs.getDate("dia"));
                return horarioExperimental;
            }
        }
        return null;
    }

    @Override
    public JSONArray consultarTodas() throws Exception {
        JSONArray jsonArray;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, identificador as nome from turma", con)) {
            jsonArray = new JSONArray();
            while (rs.next()) {
                JSONObject obj = new JSONObject();
                obj.put("codigo", rs.getString("codigo"));
                obj.put("nome", rs.getString("nome"));
                jsonArray.put(obj);
            }
        }
        return jsonArray;
    }

    @Override
    public List<AgendaTotalTO> consultaAulasContrato(ClienteVO cliente, Integer contrato,boolean habilitarSomaDeAulaNaoVigente,List<MatriculaAlunoHorarioTurmaVO> listaMatriculaHorarioTurma) throws Exception {
        AulaDesmarcada aulaDesmarcada = new AulaDesmarcada(con);
        Map<Date, List<Integer>> mapaAgendamentosDesmarcados = aulaDesmarcada.consultarAgendamentosDesmarcadosSemValidarData(cliente.getCodigo(),contrato);

        MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurma = new MatriculaAlunoHorarioTurma(con);
        return new ArrayList<AgendaTotalTO>(matriculaAlunoHorarioTurma.consultarAulas(Integer.parseInt(cliente.getMatricula()), Calendario.hoje(), mapaAgendamentosDesmarcados, contrato, habilitarSomaDeAulaNaoVigente, listaMatriculaHorarioTurma));
    }

    @Override
    public List<TurmaVO> consultar(Integer empresa, int nivelMontarDados) throws Exception {
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM Turma ORDER BY descricao";
        } else {
            sqlStr = "SELECT * FROM Turma where empresa = " + empresa.intValue() + " ORDER BY descricao";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    @Override
    public Map<String, List<AgendadoJSON>> consultarAulasAlunosNaoColetivas(Date data, Integer codigoHorarioTurma) throws Exception {
        HashMap<String, List<AgendadoJSON>> map = new HashMap<String, List<AgendadoJSON>>();

        final ResultSet rsAlunosTurma = getResultSetConsultarAlunosAulasNaoColetivas(data, codigoHorarioTurma, false);
        final ResultSet rsAlunosRealocadosParaEstaTurma = getResultSetConsultarAlunosRealocados(data, codigoHorarioTurma, false);
        final ResultSet rsAlunosRealocadosParaOutraTurma = getResultSetConsultarAlunosRealocadosParaOutraTurma(data, codigoHorarioTurma, false);

        final List<AgendadoJSON> alunosNaAula = popularAlunosTurma(rsAlunosTurma);
        final List<AgendadoJSON> alunosRealocadosParaEstaTurma = popularAlunosTurma(rsAlunosRealocadosParaEstaTurma);
        final List<AgendadoJSON> alunosRealocadosParaOutraTurma = popularAlunosTurma(rsAlunosRealocadosParaOutraTurma);

        fecharResultSetQuietly(rsAlunosTurma);
        fecharResultSetQuietly(rsAlunosRealocadosParaEstaTurma);
        fecharResultSetQuietly(rsAlunosRealocadosParaOutraTurma);

        alunosNaAula.addAll(alunosRealocadosParaEstaTurma);
        map.put("ALUNOS", alunosNaAula);
        map.put("REALOCADOS", alunosRealocadosParaOutraTurma);

        return map;
    }

    private List<AgendadoJSON> popularAlunosTurma(ResultSet rsAlunosTurma) throws SQLException {
        final List<AgendadoJSON> alunos = new ArrayList<AgendadoJSON>();
        while (rsAlunosTurma.next()) {
            AgendadoJSON aluno = new AgendadoJSON();
            aluno.setUrlFoto(Uteis.getPaintFotoDaNuvem(rsAlunosTurma.getString("fotokey")));
            aluno.setNome(rsAlunosTurma.getString("nome"));
            aluno.setMatricula(rsAlunosTurma.getString("matricula"));
            aluno.setTelefones(rsAlunosTurma.getString("telefones"));
            aluno.setFim(rsAlunosTurma.getString("horafinal"));
            aluno.setInicio(rsAlunosTurma.getString("horainicial"));

            try {
                // quando existe presenca
                aluno.setConfirmado(rsAlunosTurma.getInt("quantidadePresenca") > 0);
            } catch (Exception e) {
                try {
                    // quando eh reposicao
                    aluno.setConfirmado(rsAlunosTurma.getBoolean("confirmadoreposicao"));
                } catch (Exception e1) {
                    // ignora...
                }
            }

            aluno.setCodigoCliente(rsAlunosTurma.getInt("codigocliente"));
            aluno.setCodigoPessoa(rsAlunosTurma.getInt("codigopessoa"));
            aluno.setCodigoContrato(rsAlunosTurma.getInt("contrato"));
            alunos.add(aluno);
        }

        return alunos;
    }

    private List<AgendamentoDesmarcadoJSON> popularAgendamentoDesmarcado(ResultSet rsAlunosComAulaDesmarcadaDados) throws SQLException {
        final List<AgendamentoDesmarcadoJSON> agentamentos = new ArrayList<AgendamentoDesmarcadoJSON>();
        while (rsAlunosComAulaDesmarcadaDados.next()) {
            AgendamentoDesmarcadoJSON agendamentoDesmarcado = new AgendamentoDesmarcadoJSON();
            agendamentoDesmarcado.setCodigoCliente(rsAlunosComAulaDesmarcadaDados.getInt("cliente"));
            agendamentoDesmarcado.setReposto(rsAlunosComAulaDesmarcadaDados.getDate("datareposicao") != null);
            agendamentoDesmarcado.setCodigoContrato(rsAlunosComAulaDesmarcadaDados.getInt("contrato"));

            agentamentos.add(agendamentoDesmarcado);
        }

        return agentamentos;
    }

    private List<AgendaReposicaoJSON> popularAgendamentoRealocado(ResultSet rsAlunosRealocadosParaOutraTurmaDados) throws SQLException {
        final List<AgendaReposicaoJSON> agentamentos = new ArrayList<AgendaReposicaoJSON>();
        while (rsAlunosRealocadosParaOutraTurmaDados.next()) {
            AgendaReposicaoJSON agendaReposicao = new AgendaReposicaoJSON();
            agendaReposicao.setCodigoCliente(rsAlunosRealocadosParaOutraTurmaDados.getInt("codigocliente"));
            agendaReposicao.setCodigoPessoa(rsAlunosRealocadosParaOutraTurmaDados.getInt("codigopessoa"));
            agendaReposicao.setCodigoContrato(rsAlunosRealocadosParaOutraTurmaDados.getInt("codigocontrato"));
            agendaReposicao.setConfirmadoReposicao(rsAlunosRealocadosParaOutraTurmaDados.getBoolean("confirmadoReposicao"));
            agendaReposicao.setMatricula(rsAlunosRealocadosParaOutraTurmaDados.getString("matricula"));

            agentamentos.add(agendaReposicao);
        }

        return agentamentos;
    }

    @Override
    public Integer contarAulasAlunosNaoColetivas(Date data, Integer codigoHorarioTurma, Integer nrVagas) throws Exception {
        final ResultSet rsAlunosTurma = getResultSetConsultarAlunosAulasNaoColetivas(data, codigoHorarioTurma, true);
        final ResultSet rsAlunosRealocadosParaEstaTurma = getResultSetConsultarAlunosRealocados(data, codigoHorarioTurma, true);
        final ResultSet rsAlunosRealocadosParaOutraTurma = getResultSetConsultarAlunosRealocadosParaOutraTurma(data, codigoHorarioTurma, true);
        final ResultSet rsAlunosComAulaDesmarcada = getResultSetConsultarComAulasDesmarcadas(data, codigoHorarioTurma, true);

        final Integer total = (rsAlunosTurma.next() ? rsAlunosTurma.getInt("total") : 0);
        Integer alunosRealocadosParaEstaTurma = (rsAlunosRealocadosParaEstaTurma.next() ? rsAlunosRealocadosParaEstaTurma.getInt("total") : 0);
        Integer alunosRealocadosParaOutraTurma = (rsAlunosRealocadosParaOutraTurma.next() ? rsAlunosRealocadosParaOutraTurma.getInt("total") : 0);
        Integer alunosComAulasDesmarcadas = (rsAlunosComAulaDesmarcada.next() ? rsAlunosComAulaDesmarcada.getInt("total") : 0);

        final ResultSet rsAlunosTurmaDados = getResultSetConsultarAlunosAulasNaoColetivas(data, codigoHorarioTurma, false);
        final List<AgendadoJSON> alunosNaAula = popularAlunosTurma(rsAlunosTurmaDados);
        final ResultSet rsAlunosComAulaDesmarcadaDados = getResultSetConsultarComAulasDesmarcadas(data, codigoHorarioTurma, false);
        final List<AgendamentoDesmarcadoJSON> agendamentosDesmarcado = popularAgendamentoDesmarcado(rsAlunosComAulaDesmarcadaDados);
        final ResultSet rsAlunosRealocadosParaEstaTurmaDados = getResultSetConsultarAlunosRealocados(data, codigoHorarioTurma, false);
        final List<AgendaReposicaoJSON> realocados = popularAgendamentoRealocado(rsAlunosRealocadosParaEstaTurmaDados);
        final ResultSet rsAlunosRealocadosParaOutraTurmaDados = getResultSetConsultarAlunosRealocadosParaOutraTurma(data, codigoHorarioTurma, false);
        final List<AgendaReposicaoJSON> reposicoes = popularAgendamentoRealocado(rsAlunosRealocadosParaOutraTurmaDados);

        if (alunosComAulasDesmarcadas > 0) {
            if (!UteisValidacao.emptyList(alunosNaAula)) {
                ArrayList<Integer> clienteDesmarcado = new ArrayList<Integer>();
                ArrayList<Integer> clienteRegistradoDesmarcado = new ArrayList<Integer>();
                for (AgendadoJSON a : alunosNaAula) {
                    if (!clienteDesmarcado.contains(a.getCodigoCliente())) {
                        clienteDesmarcado.add(a.getCodigoCliente());
                    } else {
                        clienteRegistradoDesmarcado.add(a.getCodigoCliente());
                    }
                }
                for (AgendadoJSON a : alunosNaAula) {
                    if (!UteisValidacao.emptyNumber(a.getCodigoContrato())) {
                        if (!UteisValidacao.emptyList(agendamentosDesmarcado)) {
                            for (AgendamentoDesmarcadoJSON ad : agendamentosDesmarcado) {
                                if (a.getCodigoCliente().equals(ad.getCodigoCliente())) {
                                    if (!clienteRegistradoDesmarcado.contains(ad.getCodigoCliente())) {
                                        if (!a.getCodigoContrato().equals(ad.getCodigoContrato())) {
                                            alunosComAulasDesmarcadas--;
                                        }
                                    }
                                }
                            }
                        }
                        if (!UteisValidacao.emptyList(realocados)) {
                            for (AgendaReposicaoJSON re : realocados) {
                                if (a.getCodigoContrato().equals(re.getCodigoContrato())) {
                                    alunosRealocadosParaEstaTurma--;
                                }
                            }
                        }
                    }
                }
            }
            if(!UteisValidacao.emptyList(reposicoes)) {
                for (AgendaReposicaoJSON ar : reposicoes) {
                    if (!UteisValidacao.emptyList(agendamentosDesmarcado)) {
                        for (AgendamentoDesmarcadoJSON ad : agendamentosDesmarcado) {
                            if (ad.getCodigoCliente().equals(ar.getCodigoCliente())) {
                                alunosRealocadosParaOutraTurma--;
                            }
                        }
                    }
                }
            }
        }

        Integer totalAlunosNaAula = 0;
        if (alunosRealocadosParaOutraTurma != 0 && (alunosRealocadosParaOutraTurma + alunosComAulasDesmarcadas == nrVagas)) {
            totalAlunosNaAula = alunosRealocadosParaEstaTurma;
        } else if (total - alunosComAulasDesmarcadas == 0) {
            totalAlunosNaAula = alunosRealocadosParaEstaTurma;
        } else if (alunosRealocadosParaEstaTurma == 0 && alunosRealocadosParaOutraTurma == 0) {
            if (alunosComAulasDesmarcadas == 0) {
                totalAlunosNaAula = total;
            } else {
                totalAlunosNaAula = total - alunosComAulasDesmarcadas;
            }
        } else {
            totalAlunosNaAula = total + alunosRealocadosParaEstaTurma - alunosRealocadosParaOutraTurma - alunosComAulasDesmarcadas;
        }
        if (totalAlunosNaAula<0){
            totalAlunosNaAula = 0;
        }

        fecharResultSetQuietly(rsAlunosTurma);
        fecharResultSetQuietly(rsAlunosTurmaDados);
        fecharResultSetQuietly(rsAlunosComAulaDesmarcadaDados);
        fecharResultSetQuietly(rsAlunosRealocadosParaOutraTurmaDados);
        fecharResultSetQuietly(rsAlunosRealocadosParaEstaTurma);
        fecharResultSetQuietly(rsAlunosRealocadosParaOutraTurma);

        return totalAlunosNaAula;
    }

    private void fecharResultSetQuietly(ResultSet resultSet) {
        try {
            if (resultSet != null && !resultSet.isClosed()) {
                resultSet.close();
            }
        } catch (SQLException e) {
            // se nao for possivel fechar alguma stream entao apenas ignora a mesma
            Uteis.logar(e, Turma.class);
        }
    }

    private ResultSet getResultSetConsultarAlunosRealocados(Date data, Integer codigoHorarioTurma, Boolean contar) throws Exception {
        final StringBuilder sql = new StringBuilder("SELECT ");

        if (contar) {
            sql.append("COUNT(1) as total");
        } else {
            sql
                    .append("m2.contrato as codigocontrato, p.fotokey, p.codigo as codigopessoa, c.codigo as codigocliente,c.matricula,")
                    .append("ht.horainicial, ht.horafinal, p.nome, r.datapresenca IS NOT NULL AS confirmadoreposicao,")
                    .append("ARRAY_TO_STRING(ARRAY(select numero from telefone where pessoa = p.codigo), ';') as telefones");
        }

        sql.append(" FROM reposicao r");
        sql.append(" INNER JOIN cliente c ON c.codigo = r.cliente");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma AND ht.codigo = ?");
        if (!contar) {
            sql.append(" INNER JOIN matriculaalunohorarioturma m2 ON ht.codigo = m2.horarioturma AND p.codigo = m2.pessoa ");
        }
        sql.append(" WHERE r.datareposicao = ?");
        sql.append(" and (c.pessoa not in ( ");
        sql.append(" select pessoa from matriculaalunohorarioturma m ");
        sql.append(" where m.horarioturma = r.horarioturma ");
        sql.append(" and r.datareposicao between m.datainicio and m.datafim) ");
        sql.append(" or exists (select codigo from auladesmarcada a where cliente = c.codigo and horarioturma = ht.codigo and a.dataorigem = r.datareposicao))");
        if (!contar) {
            sql.append(" AND (m2.contrato NOT IN (SELECT a2.contrato FROM auladesmarcada a2 WHERE cliente = c.codigo AND horarioturma = ht.codigo AND a2.dataorigem = r.datareposicao)) ");
            sql.append(" ORDER BY p.nome");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setInt(1, codigoHorarioTurma);
        stm.setDate(2, Uteis.getDataJDBC(data));
        return stm.executeQuery();
    }

    private ResultSet getResultSetConsultarAlunosRealocadosParaOutraTurma(Date data, Integer codigoHorarioTurma, Boolean contar) throws Exception {
        final StringBuilder sql = new StringBuilder("SELECT ");

        if (contar) {
            sql.append("COUNT(1) as total");
        } else {
            sql
                    .append("r.contrato as codigocontrato, p.fotokey, p.codigo as codigopessoa, c.codigo as codigocliente,c.matricula,")
                    .append("ht.horainicial, ht.horafinal, p.nome, r.datapresenca IS NOT NULL AS confirmadoreposicao,")
                    .append("ARRAY_TO_STRING(ARRAY(select numero from telefone where pessoa = p.codigo), ';') as telefones");
        }

        sql
                .append(" FROM reposicao r")

                .append(" INNER JOIN cliente c ON c.codigo = r.cliente")
                .append(" INNER JOIN pessoa p ON p.codigo = c.pessoa")
                .append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturmaorigem AND ht.codigo = ?")

                .append(" WHERE r.dataorigem = ? AND r.horarioturma != r.horarioturmaorigem");

        if (!contar) {
            sql.append(" ORDER BY p.nome");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setInt(1, codigoHorarioTurma);
        stm.setDate(2, Uteis.getDataJDBC(data));
        return stm.executeQuery();
    }

    private ResultSet getResultSetConsultarComAulasDesmarcadas(Date data, Integer codigoHorarioTurma, Boolean contar) throws Exception {
        final StringBuilder sql = new StringBuilder("SELECT ");

        if (contar) {
            sql.append("COUNT(1) as total");
        } else {
            sql.append(" a.cliente, a.contrato, a.datareposicao ");
        }

        sql.append(" FROM auladesmarcada a \n");
        sql.append("INNER JOIN contrato c ON c.codigo = a.contrato  \n");
        sql.append("INNER JOIN horarioturma ht ON ht.codigo = a.horarioturma AND ht.codigo = ? \n");
        sql.append("WHERE a.dataorigem BETWEEN ? AND ?  \n");
        sql.append(" and c.codigo in (select contrato from matriculaalunohorarioturma m\n");
        sql.append(" where a.horarioturma = m.horarioturma and a.dataorigem between m.datainicio and m.datafim)");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setInt(1, codigoHorarioTurma);
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(data)));
        stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(data, "23:59:59")));
        return stm.executeQuery();
    }

    private ResultSet getResultSetConsultarAlunosAulasNaoColetivas(Date data, Integer codigoHorarioTurma, Boolean contar) throws Exception {
        final StringBuilder sql = new StringBuilder("SELECT ");

        if (contar) {
            sql.append("COUNT(1) as total");
        } else {
            sql
                    .append("p.fotokey, p.codigo as codigopessoa, c.codigo as codigocliente,c.matricula,")
                    .append("m.codigo as codigoagendamento, ht.horainicial, ht.horafinal, p.nome, m.contrato,")
                    .append("ARRAY_TO_STRING(ARRAY(select numero from telefone where pessoa = p.codigo), ';') as telefones,")
                    .append("(SELECT COUNT(1) FROM presenca pre WHERE pre.dadosturma = m.codigo AND pre.datapresenca = ?) as quantidadePresenca");
        }

        sql
                .append(" FROM matriculaalunohorarioturma m")

                .append(" INNER JOIN horarioturma ht ON ht.codigo = m.horarioturma AND ht.codigo = ?")
                .append(" INNER JOIN pessoa p ON p.codigo = m.pessoa")
                .append(" INNER JOIN cliente c ON c.pessoa = p.codigo")

                .append(" WHERE ? BETWEEN m.datainicio AND m.datafim");

        if (!contar) {
            sql.append(" ORDER BY p.nome");
        }

        int index = 1;

        PreparedStatement stm = con.prepareStatement(sql.toString());
        if (!contar) {
            stm.setDate(index++, Uteis.getDataJDBC(data));
        }

        stm.setInt(index++, codigoHorarioTurma);
        stm.setDate(index++, Uteis.getDataJDBC(data));
        return stm.executeQuery();
    }

    public List<TurmaAulaCheiaJSON> obterAulasColetivasPorDiaSemana(Integer empresa, Integer dia, Date agora) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t.codigo as turma, t.identificador, \n");
        sql.append("(SELECT COUNT(*) FROM alunohorarioturma WHERE horarioturma = ht.codigo AND dia = CURRENT_DATE) AS ocupacao, \n");
        sql.append("t.modalidade, m.nome as nomemodalidade, m.fotokey as modalidadeFotokey, \n");
        sql.append("t.ambiente, a.descricao as nomeambiente, pc.fotokey as professorFotokey, pc.codigo as professorCodigoPessoa, \n");
        sql.append("t.datainicialvigencia, t.datafinalvigencia, t.validarrestricoesmarcacao, \n");
        sql.append("t.empresa,  ht.horainicial || ' - ' || ht.horafinal horarios, ht.codigo horarioCodigo, ht.diasemana dias, t.mensagem, \n");
        sql.append("t.capacidade, t.meta, t.pontosbonus, t.bonificacao, \n");
        sql.append("t.tolerancia, t.professor, pc.nome as nomeprofessor, t.integracaoSpivi, t.urlVideoYoutube, t.limiteVagasAgregados \n");
        sql.append("FROM turma t ");
        sql.append("INNER JOIN modalidade m ON m.codigo = t.modalidade \n");
        sql.append("INNER JOIN horarioturma ht ON t.codigo = ht.turma \n");
        sql.append("INNER JOIN ambiente a ON a.codigo = ht.ambiente \n");
        sql.append("INNER JOIN colaborador c ON c.codigo = ht.professor \n");
        sql.append("INNER JOIN pessoa pc ON pc.codigo = c.pessoa \n");
        sql.append("WHERE aulacoletiva IS true AND usuariodesativou is null \n");
        sql.append("AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL) \n");
        sql.append("AND (ht.diasemananumero = ").append(dia).append(") \n ");
        sql.append("AND '").append(Calendario.getDataAplicandoFormatacao(agora, "yyyy-MM-dd")).append("' BETWEEN t.datainicialvigencia and t.datafinalvigencia \n");
        sql.append("AND CAST('").append(Calendario.getDataAplicandoFormatacao(agora, "HH:mm:ss")).append("' AS TIME) < CAST(ht.horafinal AS TIME) and ht.situacao = 'AT' ");
        List<TurmaAulaCheiaJSON> aulas;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            aulas = new ArrayList<TurmaAulaCheiaJSON>();
            while (rs.next()) {
                aulas.add(montarDadosTurmaAulaCheiaJSON(rs,true));
            }
        }
        return aulas;
    }

    public Boolean validarColetorPreenchido(Integer horarioTurma) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT am.coletor \n");
        sql.append("FROM horarioturma ht \n");
        sql.append("INNER JOIN ambiente am ON am.codigo = ht.ambiente \n");
        sql.append("WHERE ht.codigo  = ").append(horarioTurma + "\n");
        sql.append("AND am.coletor IS NOT NULL");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {

            if (rs.next()) {
                return true;
            }
        }

        return false;
    }

    public List<AlunoAulaAcessoJSON> obterAlunosDaAulaComAcesso(Integer horarioTurma,
                                                                String dataAula) throws Exception{

        StringBuffer sql = new StringBuffer();
        sql.append("SELECT distinct a.descricao, pessoa.nome, pessoa.fotokey, pessoa.sexo, cli.codigo, pessoa.codigo as codigoPessoa \n");
        sql.append("FROM alunohorarioturma aht \n");
        sql.append("LEFT JOIN horarioturma ht on aht.horarioturma =  ht.codigo \n");
        sql.append("LEFT JOIN ambiente a on a.codigo = ht.ambiente  \n");
        sql.append("INNER JOIN cliente cli on aht.cliente =  cli.codigo \n");
        sql.append("INNER JOIN pessoa on pessoa.codigo = cli.pessoa \n");
        sql.append(" WHERE aht.dia = '")
                .append(dataAula).append("' ");
        sql.append("AND ht.codigo =  '")
                .append(horarioTurma).append("' \n");
        sql.append("GROUP BY a.descricao, pessoa.nome, pessoa.fotokey, pessoa.sexo, cli.codigo, pessoa.codigo  \n ");

        List<AlunoAulaAcessoJSON> alunos;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            alunos = new ArrayList();

            while (rs.next()) {
                AlunoAulaAcessoJSON aluno = new AlunoAulaAcessoJSON();

                aluno.setCodigo(rs.getInt("codigo"));
                aluno.setCodigoPessoa(rs.getInt("codigoPessoa"));
                aluno.setNome(rs.getString("nome"));
                aluno.setSexo(rs.getString("sexo"));
                aluno.setFotokey(rs.getString("fotokey"));
                alunos.add(aluno);
            }
        }

        return alunos;
    }

    public  List<Integer> getCodigoAlunaAulaDesmarcada(final Integer idHorarioTurma, final Date data, boolean desconsiderarMarcados) throws Exception{
        StringBuilder sql = new StringBuilder("SELECT COUNT(*),");
        sql.append("P.CODIGO AS CODIGOPESSOA ");
        sql.append("FROM AULADESMARCADA DES ");
        sql.append("INNER JOIN CLIENTE CLI ON CLI.CODIGO = DES.CLIENTE ");
        sql.append("INNER JOIN PESSOA P ON P.CODIGO = CLI.PESSOA ");
        sql.append("INNER JOIN HORARIOTURMA HT ON HT.CODIGO = DES.HORARIOTURMA ");
        sql.append("AND DES.HORARIOTURMA = ? ");
        sql.append("AND DATAORIGEM = ? ");
        if(desconsiderarMarcados){
            sql.append(" AND des.reposicao is null ");
            ResultSet rs = getResultSetConsultarAlunosAulasNaoColetivas(data, idHorarioTurma, false);
            String codigosMatriculados = "";
            while(rs.next()){
                codigosMatriculados += "," + rs.getInt("codigocliente");
            }
            if(!codigosMatriculados.isEmpty()){
                sql.append(" AND cli.codigo in (").append(codigosMatriculados.replaceFirst(",", "")).append(")");
            }
        }
        sql.append(" GROUP BY P.CODIGO");

        List<Integer> result;
        try (PreparedStatement statement = con.prepareStatement(sql.toString())) {
            statement.setInt(1, idHorarioTurma);
            statement.setDate(2, Uteis.getDataJDBC(data));
            try (ResultSet set = statement.executeQuery()) {
                result = new ArrayList<Integer>();
                while (set.next()) {
                    result.add(set.getInt("CODIGOPESSOA"));
                }
            }
        }

        return result;
    }

    @Override
    @SuppressWarnings("unckecked")
    public List<ClienteSintenticoJson> findClienteSintetico(List<AgendadoJSON> alunos) throws Exception {
        if (CollectionUtils.isEmpty(alunos)) {
            return new ArrayList<ClienteSintenticoJson>();
        }
        StringBuilder  sql = new StringBuilder();
        sql.append("select S.*, ( select EMAIL from EMAIL E where E.PESSOA = S.CODIGOPESSOA limit 1) as EMAIL, ");
        sql.append("	P.FOTO as FOTO, ( select U.NOME from USUARIOMOVEL U ");
        sql.append("	where  U.CLIENTE = S.CODIGOCLIENTE limit 1) as USERNAME, ");
        sql.append("	( select EPR.NOME ");
        sql.append("	from  EMPRESA EPR ");
        sql.append("	where  EPR.CODIGO = S.EMPRESACLIENTE) as NOME_EMPRESA ");
        sql.append("from  SITUACAOCLIENTESINTETICODW S ");
        sql.append("inner join PESSOA P on  P.CODIGO = S.CODIGOPESSOA ");
        sql.append("where  1 = 1 ");
        sql.append("AND S.CODIGOCLIENTE IN(");
        StringBuilder twoSql = new StringBuilder();
        twoSql.append("?");
        for (int i = 1; i < alunos.size() ; i++) {
            twoSql.append(",").append("?");
        }
        sql.append(twoSql.toString() + ")");

        List<ClienteSintenticoJson> result;
        try (PreparedStatement statement = con.prepareStatement(sql.toString())) {
            System.out.println(sql.toString());
            int i = 1;
            for (AgendadoJSON aluno : alunos) {
                statement.setInt(i, aluno.getCodigoCliente());
                i++;
            }

            result = new ArrayList<ClienteSintenticoJson>();
            try (ResultSet set = statement.executeQuery()) {

                while (set.next()) {
                    ClienteSintenticoJson cli = new ClienteSintenticoJson();
                    cli.setCodigo(set.getInt("CODIGO"));
                    cli.setCodigoPessoaZW(set.getInt("CODIGOPESSOA"));
                    cli.setCodigoClienteZW(set.getInt("CODIGOCLIENTE"));
                    cli.setCodigoContrato(set.getInt("CODIGOCONTRATO"));
                    cli.setMatricula(set.getInt("MATRICULA"));
                    cli.setNome(set.getString("NOMECLIENTE"));
                    cli.setDataNascimento(set.getDate("DATANASCIMENTO"));
                    cli.setIdade(set.getInt("IDADE"));
                    cli.setProfissao(set.getString("PROFISSAO"));
                    cli.setColaboradores(set.getString("COLABORADORES"));
                    cli.setSituacao(set.getString("SITUACAO"));
                    cli.setSituacaoContrato(set.getString("SITUACAOCONTRATO"));
                    cli.setDataUltimoAcesso(set.getDate("DATAULTIMOACESSO"));
                    cli.setDataInicioPeriodoAcesso(set.getDate("DATAINICIOPERIODOACESSO"));
                    cli.setDataFimPeriodoAcesso(set.getDate("DATAFIMPERIODOACESSO"));
                    cli.setEmpresa(set.getInt("EMPRESACLIENTE"));
                    cli.setEmail(set.getString("EMAIL"));
                    cli.setTelefones(set.getString("TELEFONESCLIENTE"));
                    cli.setUrlFoto(set.getString("FOTO"));
                    cli.setUserName(set.getString("USERNAME"));
                    cli.setDia(set.getDate("DIA"));
                    cli.setNomeEmpresa(set.getString("NOME_EMPRESA"));
                    cli.setCodigoAcesso(set.getString("CODACESSOCLIENTE"));
                    cli.setSexo(set.getString("SEXOCLIENTE"));
                    cli.setDataVirgencia(set.getDate("DATAVIGENCIAATEAJUSTADA"));
                    result.add(cli);
                }
            }
        }

        return result;
    }

    @Override
    public void inativarTurmas() throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "UPDATE turma SET datafinalvigencia = NOW() - interval '1 day' WHERE datafinalvigencia >= NOW();";
            try (PreparedStatement sqlInativarTurmas = con.prepareStatement(sql)) {
                sqlInativarTurmas.execute();
            }
            con.commit();
        }catch (Exception e ){

            throw  e;
        }
    }

    private StringBuilder retornaSelectConsultaBasica(){
        StringBuilder sqlSelect = new StringBuilder();
        sqlSelect.append(" SELECT t.niveis, t.codigo as turma, t.identificador, t.ocupacao, t.integracaoSpivi,\n");
        sqlSelect.append(" t.idadeMinima, t.idadeMinimaMeses, t.idadeMaxima, t.idadeMaximaMeses, \n");
        sqlSelect.append(" t.modalidade, m.nome as nomemodalidade, \n");
        sqlSelect.append(" t.ambiente, a.descricao as nomeambiente, \n");
        sqlSelect.append(" t.datainicialvigencia, t.datafinalvigencia, \n");
        sqlSelect.append(" t.empresa, t.horarios, t.dias, t.mensagem, \n");
        sqlSelect.append(" t.capacidade, t.meta, t.pontosbonus, t.bonificacao, \n");
        sqlSelect.append(" t.tolerancia, t.tipotolerancia, t.professor, pc.nome as nomeprofessor, t.validarrestricoesmarcacao, \n");
        sqlSelect.append(" t.produtogympass, t.idclassegympass, t.urlTurmaVirtual, t.naovalidarmodalidadecontrato,  \n");
        sqlSelect.append(" t.urlVideoYoutube, t.fotoKey, t.visualizarProdutosGympass,t.visualizarProdutosTotalpass, t.permiteFixar, ");
        sqlSelect.append(" t.tipoReservaEquipamento, t.mapaEquipamentos, t.aulaIntegracaoSelfloops, t.limiteVagasAgregados");
        return sqlSelect;
    }

    private StringBuilder retornaSelectConsultaBasicaV2() {
        // V2 criado para atender a nova estrutura de aula coletiva devido ao retornaSelectConsultaBasica ser utilizado em muitos locais
        StringBuilder sqlSelect = new StringBuilder();
        sqlSelect.append(" SELECT distinct(t.codigo) as turma, t.descricao, t.niveis, t.identificador, t.ocupacao, t.integracaoSpivi,\n");
        sqlSelect.append(" t.idadeMinima, t.idadeMinimaMeses, t.idadeMaxima, t.idadeMaximaMeses, \n");
        sqlSelect.append(" t.modalidade, m.nome as nomemodalidade, \n");
        sqlSelect.append(" t.ambiente, a.descricao as nomeambiente, \n");
        sqlSelect.append(" t.datainicialvigencia, t.datafinalvigencia, \n");
        sqlSelect.append(" t.empresa, t.horarios, t.dias, t.mensagem, \n");
        sqlSelect.append(" t.capacidade, t.meta, t.pontosbonus, t.bonificacao, \n");
        sqlSelect.append(" t.tolerancia, t.tipotolerancia, t.professor, t.validarrestricoesmarcacao, \n");
        sqlSelect.append(" t.produtogympass, t.idclassegympass, t.urlTurmaVirtual, t.naovalidarmodalidadecontrato,  \n");
        sqlSelect.append(" t.urlVideoYoutube, t.fotoKey, t.visualizarProdutosGympass,t.visualizarProdutosTotalpass, t.permiteFixar, ");
        sqlSelect.append(" t.tipoReservaEquipamento, t.mapaEquipamentos, t.aulaIntegracaoSelfloops, t.limiteVagasAgregados");
        return sqlSelect;
    }

    private StringBuilder retornaFromConsultaBasica(JSONObject filtros){
        StringBuilder sqlFrom = new StringBuilder();
        sqlFrom.append(" FROM turma t \n");
        if (filtros != null && filtros.has("professor") && filtros.getJSONArray("professor").length() > 0) {
            sqlFrom.append(" LEFT JOIN horarioturma ht ON ht.turma = t.codigo  \n");
            sqlFrom.append(" LEFT JOIN colaborador c ON c.codigo = ht.professor \n");
            sqlFrom.append(" LEFT JOIN pessoa pc ON pc.codigo = c.pessoa \n");
        } else {
            sqlFrom.append(" LEFT JOIN colaborador c ON c.codigo = t.professor \n");
            sqlFrom.append(" LEFT JOIN pessoa pc ON pc.codigo = c.pessoa \n");
        }
        sqlFrom.append(" LEFT JOIN ambiente a ON a.codigo = t.ambiente \n");
        sqlFrom.append(" INNER JOIN modalidade m ON m.codigo = t.modalidade \n");
        sqlFrom.append(" WHERE aulacoletiva \n");
        if(filtros == null){
            sqlFrom.append(" AND usuariodesativou is null \n");
            return sqlFrom;
        }

        if(filtros.has("codigo") && filtros.getInt("codigo") > 0){
            sqlFrom.append(" AND t.codigo = ").append(filtros.getInt("codigo"));
            return sqlFrom;
        }

        if(filtros.has("vigencia") && filtros.getJSONArray("vigencia").length() > 0){
            List<String> vigencia = Uteis.arrayJsonToList(filtros.getJSONArray("vigencia"));
            sqlFrom.append(" AND ( \n");
            if(vigencia.contains("NAO_VIGENTE")){
                sqlFrom.append(" t.datafinalvigencia < '").append(Uteis.getDataJDBC(Calendario.hoje())).append("' ");
            }
            if(vigencia.contains("NAO_VIGENTE") && vigencia.contains("VIGENTE")){
                sqlFrom.append(" OR ");
            }
            if(vigencia.contains("VIGENTE")){
                sqlFrom.append(" t.datafinalvigencia >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("' ");
            }
            sqlFrom.append(" ) \n");
        }

        if(filtros.has("gympass") && filtros.getJSONArray("gympass").length() > 0){
            List<String> vigencia = Uteis.arrayJsonToList(filtros.getJSONArray("gympass"));
            sqlFrom.append(" AND ( \n");
            if(vigencia.contains("NAO_INTEGRADO")){
                sqlFrom.append(" (t.idclassegympass is null OR t.idclassegympass = 0) ");
            }
            if(vigencia.contains("NAO_INTEGRADO") && vigencia.contains("INTEGRADO")){
                sqlFrom.append(" OR ");
            }
            if(vigencia.contains("INTEGRADO")){
                sqlFrom.append(" t.idclassegympass is not null AND t.idclassegympass <> 0");
            }
            sqlFrom.append(" ) \n");
        }

        if(filtros.has("professor") && filtros.getJSONArray("professor").length() > 0){
            sqlFrom.append(" AND ht.professor in ( \n");
            sqlFrom.append(Uteis.arrayJsonToString(filtros.getJSONArray("professor"), ","));
            sqlFrom.append(" ) \n");
        }

        if(filtros.has("ambiente") && filtros.getJSONArray("ambiente").length() > 0){
            sqlFrom.append(" AND t.ambiente in ( \n");
            sqlFrom.append(Uteis.arrayJsonToString(filtros.getJSONArray("ambiente"), ","));
            sqlFrom.append(" ) \n");
        }

        if(filtros.has("modalidadeIds") && filtros.getJSONArray("modalidadeIds").length() > 0){
            sqlFrom.append(" AND t.modalidade in ( \n");
            sqlFrom.append(Uteis.arrayJsonToString(filtros.getJSONArray("modalidadeIds"), ","));
            sqlFrom.append(" ) \n");
        }

        if(filtros.has("quicksearchValue") && !UteisValidacao.emptyString(filtros.get("quicksearchValue").toString()) && !filtros.get("quicksearchValue").toString().equals("null")){
            if(Uteis.isNumeroValido(filtros.get("quicksearchValue").toString())){
                sqlFrom.append(" and t.codigo = ").append(filtros.get("quicksearchValue").toString());
            } else {
                sqlFrom.append(" and (upper(unaccent(t.descricao)) like upper(unaccent('%").append(filtros.get("quicksearchValue").toString().toUpperCase()).append("%'))");
                sqlFrom.append(" or upper(unaccent(t.identificador)) like upper(unaccent('%").append(filtros.get("quicksearchValue").toString().toUpperCase()).append("%')))");
            }
        }

        return sqlFrom;
    }

    private StringBuilder retornaFromConsultaBasica(Integer empresa, JSONObject filtros){
        StringBuilder sqlFrom = retornaFromConsultaBasica(filtros);
        if(empresa>0)
            sqlFrom.append(" AND t.empresa = "+empresa );
        return sqlFrom;
    }

    @Override
    public List<TurmaAulaCheiaJSON> consultarPorDescricaoTurmaColetiva(String valorConsulta, Integer empresa) throws Exception {
        consultar(getIdEntidade());

        StringBuilder sql = retornaSelectConsultaBasica();
        sql.append(retornaFromConsultaBasica(empresa, null));

        sql.append(" AND UPPER(t.identificador) like UPPER('%" + valorConsulta + "%')");

        List<TurmaAulaCheiaJSON> aulas;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            aulas = new ArrayList<TurmaAulaCheiaJSON>();
            while (rs.next()) {
                aulas.add(montarDadosTurmaAulaCheiaJSON(rs,false));
            }
            return aulas;
        }
    }

    public TurmaAulaCheiaJSON consultarPorCodigoTurmaColetiva(Integer codigo, Integer empresa) throws Exception {
        consultar(getIdEntidade());

        StringBuilder sql = retornaSelectConsultaBasica();
        sql.append(retornaFromConsultaBasica(empresa, null));
        sql.append(" AND t.codigo = " + codigo);

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next())
                return montarDadosTurmaAulaCheiaJSON(rs,false);
            else
                return new TurmaAulaCheiaJSON();
        }
    }

    public TurmaAulaCheiaJSON consultarPorCodigoTurmaColetiva(Integer codigo) throws Exception {
        consultar(getIdEntidade());

        StringBuilder sql = retornaSelectConsultaBasica();
        sql.append(retornaFromConsultaBasica(null));
        sql.append(" AND t.codigo = " + codigo);

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next())
                return montarDadosTurmaAulaCheiaJSON(rs,false);
            else
                return new TurmaAulaCheiaJSON();
        }
    }

    @Override
    public void alterarPontos(Integer codigo, Integer pontos) throws Exception {
        alterar(getIdEntidade());
        String sql = "update turma set pontosbonus = ? where codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, pontos);
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }
    }

    @Override
    public List consultarPorCodigoModalidadeEOuProfessor(boolean somenteVigentes, boolean somenteNaoVigente, int modalidadeSelecionada, int professorSelecionado, Integer codigoEmpresa, int nivelmontardadosDadosbasicos) throws Exception {
        String sqlStr = "";

        sqlStr = "SELECT DISTINCT t.codigo, t.identificador FROM Turma as t\n" +
                "inner join horarioturma on t.codigo = horarioturma.turma\n" +
                "INNER JOIN colaborador on colaborador.codigo = horarioturma.professor\n" +
                "inner join modalidade on modalidade.codigo = t.modalidade\n" +
                "where t.empresa = "+codigoEmpresa+" ";

        if (modalidadeSelecionada != 0) {
            sqlStr += " and modalidade.codigo = " + modalidadeSelecionada;
        }

        if(professorSelecionado != 0){
            sqlStr += " and colaborador.codigo = " + professorSelecionado;
        }

        if(somenteVigentes){
            sqlStr += " and dataFinalVigencia >= '"+Uteis.getDataJDBC(Calendario.hoje())+"' ";
        }else if(somenteNaoVigente){
            sqlStr += " and dataFinalVigencia < '"+Uteis.getDataJDBC(Calendario.hoje())+"' ";
        }


        List vetResultado;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {

                vetResultado = new ArrayList();
                while (tabelaResultado.next()) {
                    TurmaVO obj = montarDadosBasicoGestao(tabelaResultado, nivelmontardadosDadosbasicos);
                    vetResultado.add(obj);
                }
            }
        }
        return vetResultado;
    }

    @Override
    public List<Integer> consultarTodosCodigos() throws Exception {
        String sql = "SELECT codigo FROM turma ORDER BY datafinalvigencia DESC";
        List<Integer> codigos = new ArrayList<Integer>();
        ResultSet rs = con.prepareStatement(sql).executeQuery();
        while(rs.next()){
            codigos.add(rs.getInt("codigo"));
        }
        return codigos;
    }

    @Override
    public void alterarDescricaoTurma(Integer codigo, String descricao) throws Exception {
        String sql = "UPDATE turma SET descricao = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, descricao);
        ps.setInt(2, codigo);
        ps.executeUpdate();
    }

    @Override
    public boolean verificarTurmaParaContrato(Date dataNasc, Integer modalidade, Date dataContrato,Integer empresa) throws Exception {

        Integer idade = Uteis.calcularIdadePessoa(Calendario.hoje(), dataNasc);


        return existe("SELECT * FROM turma "
                + "WHERE modalidade  ="+ modalidade + "  and  idademinima  <="+ idade +  " and idademaxima >="+ idade
                +"  and datafinalvigencia >='"+Uteis.getData(dataContrato,"dd/MM/yyyy") +"'  and datainicialvigencia <='"+Uteis.getData(dataContrato,"dd/MM/yyyy")
                +"' and empresa = "+ empresa, con);
    }

    public void alterarProdutoGymPass(Integer produtoGymPass, Integer codigo) throws Exception {
        String sql = "UPDATE turma SET produtoGymPass = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, produtoGymPass);
        ps.setInt(2, codigo);
        ps.executeUpdate();
    }

    public void alterarIdClasseGymPass(Integer idClasseGymPass, Integer codigo) throws Exception {
        String sql = "UPDATE turma SET idClasseGymPass = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idClasseGymPass);
        ps.setInt(2, codigo);
        ps.executeUpdate();
    }

    public void enviarGymPassBooking(TurmaVO turmaVO) throws Exception {
        try {

            if (UteisValidacao.emptyNumber(turmaVO.getProdutoGymPass())) {
                throw new Exception("Produto Gympass não informado.");
            }

            String key = DAO.resolveKeyFromConnection(this.con);
            String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreino);

            HttpPost httpPost = new HttpPost(urlTreino + "/prest/gympass/" + key + "/sincronizar");
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("empresaZW", turmaVO.getEmpresa().getCodigo().toString());
            httpPost.setHeader("empresaTW", "0");
            httpPost.setHeader("turmaId", turmaVO.getCodigo().toString());
            HttpClient client = ExecuteRequestHttpService.createConnector();
            HttpResponse response = client.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            JSONObject json = new JSONObject(responseBody);
            if (!json.optString("content").equalsIgnoreCase("ok")) {
                throw new Exception(responseBody);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public TurmaVO consultarUltimoCadastradoPorProdutoGymPass(Integer produtoGymPass, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Turma WHERE produtoGymPass = ? order by codigo desc limit 1";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, produtoGymPass);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Turma ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    @Override
    public boolean consultarConfigNaoValidarModalidadeContratoAula(int codHorarioTurma) throws Exception {
        String sql = "select naovalidarmodalidadecontrato from turma t "
                + " inner join horarioturma ht on ht.turma = t.codigo"
                + " where ht.codigo = "+codHorarioTurma;
        ResultSet rs = con.prepareStatement(sql).executeQuery();
        while(rs.next()){
            return rs.getBoolean("naovalidarmodalidadecontrato");
        }
        return false;
    }

    public int contarTurmasPorDataInicialVigenciaModalidade(Date prmIni, Date prmFim, Integer empresa, Integer codModalidade) throws Exception {
        return contarTurmasPorDataInicialVigenciaModalidade(prmIni, prmFim,empresa,codModalidade,null);
    }

    public int contarTurmasPorDataInicialVigenciaModalidade(Date prmIni, Date prmFim, Integer empresa, Integer codModalidade,TurmaVO turmaVO) throws Exception {
        String sqlStr = "SELECT count(codigo) as quantidade\n" +
                "FROM Turma\n" +
                "WHERE ((dataInicialVigencia >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataInicialVigencia <= '" + Uteis.getDataJDBC(prmFim) + "'))\n" +
                "and empresa = " + empresa + "\n" +
                "and modalidade = " + codModalidade + ";";
        if(turmaVO!=null && !UteisValidacao.emptyNumber(turmaVO.getCodigo())){
            sqlStr = sqlStr.replace(";", " and codigo = "+turmaVO.getCodigo()+";");
        }

        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getInt("quantidade");
            }
        }

        return 0;
    }

    public long qtdHoraAulaAtivaComAlunosPeriodoEmMinutos(Date prmIni, Date prmFim, Integer empresa, String modalidades, TurmaVO turma, Integer vezesSemana) throws Exception {
        String sqlStr = "SELECT SUM(EXTRACT(EPOCH FROM TO_TIMESTAMP(ht.horaFinal, 'HH24:MI') - TO_TIMESTAMP(ht.horaInicial, 'HH24:MI')) / 60) AS total_em_minutos\n" +
                "FROM horarioturma ht\n" +
                "INNER JOIN turma t ON t.codigo = ht.turma \n" +
                "INNER JOIN ( SELECT DISTINCT ON (horarioturma) * FROM matriculaalunohorarioturma WHERE \n" +
                " datainicio <= '" + Uteis.getDataJDBC(prmIni) + "' AND dataFim >= '" + Uteis.getDataJDBC(prmFim) + "' \n" +
                "ORDER BY horarioturma, datainicio DESC ) maht ON maht.horarioturma = ht.codigo \n" +
                "inner join contratomodalidade cm ON cm.contrato = maht.contrato and cm.modalidade = t.modalidade\n" +
                "inner join contratomodalidadevezessemana cmvs on cmvs.contratomodalidade = cm.codigo\n" +
                "inner join modalidade m ON m.codigo = t.modalidade\n" +
                "WHERE ((t.dataInicialVigencia <= '" + Uteis.getDataJDBC(prmIni) + "') and (t.dataFinalVigencia >= '" + Uteis.getDataJDBC(prmFim) + "'))\n" +
                "AND t.empresa = " + empresa + "\n" +
                "AND t.modalidade in (" + modalidades + ")\n" +
                "AND (cmvs.nrvezes = "+vezesSemana+" or ((unaccent(m.nome) ILIKE '%musculacao%') and cmvs.nrvezes = 5))\n" +
                "AND ((CASE\n" +
                "WHEN EXTRACT(DOW FROM '" + Uteis.getDataJDBC(prmIni) + "'::DATE) = 0 THEN 'DM'\n" +
                "WHEN EXTRACT(DOW FROM '" + Uteis.getDataJDBC(prmIni) + "'::DATE) = 1 THEN 'SG'\n" +
                "WHEN EXTRACT(DOW FROM '" + Uteis.getDataJDBC(prmIni) + "'::DATE) = 2 THEN 'TR'\n" +
                "WHEN EXTRACT(DOW FROM '" + Uteis.getDataJDBC(prmIni) + "'::DATE) = 3 THEN 'QA'\n" +
                "WHEN EXTRACT(DOW FROM '" + Uteis.getDataJDBC(prmIni) + "'::DATE) = 4 THEN 'QI'\n" +
                "WHEN EXTRACT(DOW FROM '" + Uteis.getDataJDBC(prmIni) + "'::DATE) = 5 THEN 'SX'\n" +
                "WHEN EXTRACT(DOW FROM '" + Uteis.getDataJDBC(prmIni) + "'::DATE) = 6 THEN 'SB'\n" +
                "END) = ht.diaSemana)";
        if (turma != null && !UteisValidacao.emptyNumber(turma.getCodigo())) {
            sqlStr += " and t.codigo = " + turma.getCodigo();

        }

        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getLong("total_em_minutos");
            }
        }

        return 0L;
    }

    public int contarTurmasAtivasComAlunoPorDataInicialVigenciaVariasModalidade(Date prmIni, Date prmFim, Integer empresa, String modalidades, TurmaVO turma, Integer vezesSemana) throws Exception {
        String sqlStr = "select count(distinct(t.codigo)) as quantidade from turma t\n" +
                "inner join horarioturma ht on ht.turma = t.codigo\n" +
                "inner join matriculaalunohorarioturma maht on maht.horarioturma = ht.codigo\n" +
                "inner join contratomodalidade cm ON cm.contrato = maht.contrato and cm.modalidade = t.modalidade\n" +
                "inner join contratomodalidadevezessemana cmvs on cmvs.contratomodalidade = cm.codigo\n" +
                "inner join modalidade m ON m.codigo = t.modalidade\n" +
                "WHERE ((t.dataInicialVigencia <= '" + Uteis.getDataJDBC(prmIni) + "') and (t.dataFinalVigencia >= '" + Uteis.getDataJDBC(prmFim) + "'))\n" +
                "AND ((maht.datainicio <= '" + Uteis.getDataJDBC(prmIni) + "') and (maht.dataFim >= '" + Uteis.getDataJDBC(prmFim) + "'))\n" +
                "AND t.empresa = " + empresa + "\n" +
                "AND t.modalidade in (" + modalidades + ")" +
                "AND (cmvs.nrvezes = "+vezesSemana+" or ((unaccent(m.nome) ILIKE '%musculacao%') and cmvs.nrvezes = 5))\n";
        if (turma != null && !UteisValidacao.emptyNumber(turma.getCodigo())) {
            sqlStr += " and t.codigo = " + turma.getCodigo();

        }

        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getInt("quantidade");
            }
        }

        return 0;
    }

    public int contarTurmasAtivasPorDataInicialVigenciaVariasModalidade(Date prmIni, Date prmFim, Integer empresa, String modalidades, TurmaVO turma) throws Exception {
        String sqlStr = "SELECT count(codigo) as quantidade\n" +
                "FROM Turma\n" +
                "WHERE ((dataInicialVigencia <= '" + Uteis.getDataJDBC(prmIni) + "') and (dataFinalVigencia >= '" + Uteis.getDataJDBC(prmFim) + "'))\n" +
                "and empresa = " + empresa + "\n" +
                "and modalidade in (" + modalidades + ")";
        if (turma != null && !UteisValidacao.emptyNumber(turma.getCodigo())) {
            sqlStr += " and codigo = " + turma.getCodigo();

        }

        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getInt("quantidade");
            }
        }

        return 0;
    }

    public ResultSet consultarToleranciaTurma(Integer codigoHorarioTurma) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select t.tolerancia, t.tipotolerancia, t.visualizarprodutosgympass, \n")
                .append(" t.visualizarprodutostotalpass from turma t inner join horarioturma ht on ht.turma = t.codigo \n")
                .append(" where ht.codigo = ").append(codigoHorarioTurma).append(" \n");

        return criarConsulta(sql.toString(),con);
    }

    public JSONArray aulasCodigos(Integer empresa) throws Exception {
        JSONArray array = new JSONArray();
        try (ResultSet rs = criarConsulta("select codigo, idclassegympass from turma " +
                " where idclassegympass > 0 and empresa = " + empresa, con)) {
            while(rs.next()){
                JSONObject json = new JSONObject();
                json.put("aula", rs.getInt("codigo"));
                json.put("idclasse", rs.getInt("idclassegympass"));
                array.put(json);
            }
        }
        return array;
    }

    public TurmaVO consultarPorIdexterno(String idExterno, int nivelMontarDados) {
        String sql = "SELECT * FROM turma WHERE idexterno = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, idExterno);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
