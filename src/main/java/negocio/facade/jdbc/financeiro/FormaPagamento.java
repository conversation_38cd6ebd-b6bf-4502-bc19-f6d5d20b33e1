package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoEmpresaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.TaxaCartaoVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoDebitoOnlineEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.consulta.ConsultaUtil;
import negocio.interfaces.financeiro.FormaPagamentoInterfaceFacade;

import javax.faces.model.SelectItem;
import java.sql.*;
import java.sql.Date;
import java.util.*;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>FormaPagamentoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>FormaPagamentoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see FormaPagamentoVO
 * @see SuperEntidade
 */
public class FormaPagamento extends SuperEntidade implements FormaPagamentoInterfaceFacade {

    public FormaPagamento() throws Exception {
        super();
    }

    public FormaPagamento(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>FormaPagamentoVO</code>.
     */
    public FormaPagamentoVO novo() throws Exception {
        incluir(getIdEntidade());
        return new FormaPagamentoVO();
    }

    public void incluir(FormaPagamentoVO obj) throws Exception {
        this.incluir(obj, false);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>FormaPagamentoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>FormaPagamentoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(FormaPagamentoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	incluirObj(getIdEntidade());
            } else {
                incluir(getIdEntidade());
            }
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(FormaPagamentoVO obj) throws Exception {
        FormaPagamentoVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO FormaPagamento"
                + "(descricao, convenioCobranca, tipoFormaPagamento, "
                + "taxaCartao, defaultRecorrencia, ativo, somenteFinanceiro, "
                + "compensacaoDiasUteis, apresentarnsu, defaultdco, diasCompensacaoCartaoCredito, cor,exigecodautorizacao, "
                + "tipoDebitoOnline, cartaoDebitoOnline, merchantid, merchantkey, gerarpontos, tipoparceiro, tipoConvenioCobranca, pinpad, receberSomenteViaPinPad, nrMaxParcelasPinpad, systemidgeoitd, posidgeoitd , taxapix, taxaPixValorAbsoluto) "
                + "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        int i = 1;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(i++, obj.getDescricao());
            if (obj.getConvenioCobrancaVO().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getConvenioCobrancaVO().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setString(i++, obj.getTipoFormaPagamento());
            sqlInserir.setDouble(i++, obj.getTaxaCartao());
            sqlInserir.setBoolean(i++, obj.getDefaultRecorrencia());
            sqlInserir.setBoolean(i++, obj.isAtivo());
            if (obj.getTipoFormaPagamento().equals("CC")) {
                sqlInserir.setBoolean(i++, false);
            } else {
                sqlInserir.setBoolean(i++, obj.isSomenteFinanceiro());
            }
            sqlInserir.setBoolean(i++, obj.isCompensacaoDiasUteis());
            sqlInserir.setBoolean(i++, obj.isApresentarNSU());
            sqlInserir.setBoolean(i++, obj.getDefaultDCO());
            sqlInserir.setInt(i++, obj.getDiasCompensacaoCartaoCredito());
            sqlInserir.setString(i++, obj.getCor());
            sqlInserir.setBoolean(i++, obj.isExigeCodAutorizacao());
            if (obj.getTipoDebitoOnline() != null) {
                sqlInserir.setInt(i++, obj.getTipoDebitoOnline().getId());
            } else {
                sqlInserir.setNull(i++, Types.NULL);
            }
            sqlInserir.setBoolean(i++, obj.isCartaoDebitoOnline());
            sqlInserir.setString(i++, obj.getMerchantid());
            sqlInserir.setString(i++, obj.getMerchantkey());
            sqlInserir.setBoolean(i++, obj.isGerarPontos());
            resolveEnumNull(sqlInserir, i++, obj.getTipoParceiro(), "id");
            sqlInserir.setInt(i++, obj.getTipoConvenioCobranca().getCodigo());
            sqlInserir.setInt(i++, obj.getPinpad().getPinpad());
            sqlInserir.setBoolean(i++, obj.isReceberSomenteViaPinPad());
            sqlInserir.setInt(i++, obj.getNrMaxParcelasPinpad());
            sqlInserir.setString(i++, obj.getSystemIdGeoitd());
            sqlInserir.setString(i++, obj.getPosIDGeoitd());
            sqlInserir.setDouble(i++, obj.getTaxaPix());
            sqlInserir.setBoolean(i++, obj.isTaxaPixValorAbsoluto());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());

        if (!obj.getTaxasCartao().isEmpty()) {
            TaxaCartao taxaCartao = new TaxaCartao(con);
            for (TaxaCartaoVO taxa : obj.getTaxasCartao()) {
                taxa.setFormaPagamentoVO(obj);
                taxaCartao.incluir(taxa);
            }
        }

        obj.setNovoObj(false);
    }

    public void alterar(FormaPagamentoVO obj) throws Exception {
        this.alterar(obj, false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>FormaPagamentoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>FormaPagamentoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(FormaPagamentoVO obj, boolean centralEventos) throws Exception {
        alterar("FormaPagamento");
             FormaPagamentoVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE FormaPagamento set descricao=?, ConvenioCobranca=?, "
                + "tipoFormaPagamento=?, taxaCartao=?, defaultRecorrencia=?, "
                + "ativo=?, somenteFinanceiro=?, compensacaoDiasUteis = ?, apresentarnsu = ?, defaultdco=?, diasCompensacaoCartaoCredito = ?, cor = ?, "
                + "exigecodautorizacao = ?, tipoDebitoOnline = ?, cartaoDebitoOnline = ?, merchantid = ?, merchantkey = ?, "
                + "gerarpontos=?, tipoparceiro=?, tipoConvenioCobranca = ?, pinpad = ?, receberSomenteViaPinPad = ?, nrMaxParcelasPinpad = ? , systemidgeoitd = ?, posidgeoitd = ?, taxapix = ?, taxaPixValorAbsoluto = ? "
                + "WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 1;
            sqlAlterar.setString(i++, obj.getDescricao());
            if (obj.getConvenioCobrancaVO().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getConvenioCobrancaVO().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setString(i++, obj.getTipoFormaPagamento());
            sqlAlterar.setDouble(i++, obj.getTaxaCartao());
            sqlAlterar.setBoolean(i++, obj.getDefaultRecorrencia());
            sqlAlterar.setBoolean(i++, obj.isAtivo());
            sqlAlterar.setBoolean(i++, obj.isSomenteFinanceiro());
            sqlAlterar.setBoolean(i++, obj.isCompensacaoDiasUteis());
            sqlAlterar.setBoolean(i++, obj.isApresentarNSU());
            sqlAlterar.setBoolean(i++, obj.getDefaultDCO());
            sqlAlterar.setInt(i++, obj.getDiasCompensacaoCartaoCredito());
            sqlAlterar.setString(i++, obj.getCor());
            sqlAlterar.setBoolean(i++, obj.isExigeCodAutorizacao());
            if (obj.getTipoDebitoOnline() != null) {
                sqlAlterar.setInt(i++, obj.getTipoDebitoOnline().getId());
            } else {
                sqlAlterar.setNull(i++, Types.NULL);
            }

            sqlAlterar.setBoolean(i++, obj.isCartaoDebitoOnline());
            sqlAlterar.setString(i++, obj.getMerchantid());
            sqlAlterar.setString(i++, obj.getMerchantkey());
            sqlAlterar.setBoolean(i++, obj.isGerarPontos());
            resolveEnumNull(sqlAlterar, i++, obj.getTipoParceiro(), "id");
            sqlAlterar.setInt(i++, obj.getTipoConvenioCobranca().getCodigo());
            sqlAlterar.setInt(i++, obj.getPinpad().getPinpad());
        sqlAlterar.setBoolean(i++, obj.isReceberSomenteViaPinPad());
        sqlAlterar.setInt(i++, obj.getNrMaxParcelasPinpad());
        sqlAlterar.setString(i++, obj.getSystemIdGeoitd());
        sqlAlterar.setString(i++, obj.getPosIDGeoitd());
        sqlAlterar.setDouble(i++, obj.getTaxaPix());
        sqlAlterar.setBoolean(i++, obj.isTaxaPixValorAbsoluto());

            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
        }

        TaxaCartao taxaCartao = new TaxaCartao(con);
        String codigoAtuais = "";
        if (!obj.getTaxasCartao().isEmpty()) {
            for (TaxaCartaoVO taxa : obj.getTaxasCartao()) {
                 taxa.getFormaPagamentoVO().setCodigo(obj.getCodigo());
                if(UteisValidacao.emptyNumber(taxa.getCodigo())){
                    taxaCartao.incluir(taxa);
                } else {
                    taxaCartao.alterar(taxa);
                }
                codigoAtuais += ","+taxa.getCodigo();
            }
        }
        taxaCartao.excluirPorFormaPagamentoCodigosAtuais(obj.getCodigo(),codigoAtuais.replaceFirst(",", ""));
    }

    public void excluir(FormaPagamentoVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>FormaPagamentoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>FormaPagamentoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(FormaPagamentoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }

            String sql = "UPDATE CategoriaProduto set formaPagamento = null WHERE formaPagamento = ?";
            try (PreparedStatement sqlUpdate = con.prepareStatement(sql)) {
                sqlUpdate.setInt(1, obj.getCodigo());
                sqlUpdate.execute();
            }

            sql = "DELETE FROM FormaPagamento WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public FormaPagamentoVO consultarPrimeiraFormaPagamentoAVista(int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM FormaPagamento\n" +
                " WHERE upper(tipoFormaPagamento) LIKE ('AV')\n" +
                " AND ativo\n" +
                " AND NOT somentefinanceiro\n" +
                " ORDER BY codigo\n" +
                " LIMIT 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>FormaPagamento</code> através do valor do atributo 
     * <code>String tipoFormaPagamento</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>FormaPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<FormaPagamentoVO> consultarPorTipoFormaPagamento(String valorConsulta, boolean somenteZW, boolean noRecorrencia, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorTipoFormaPagamento(valorConsulta, somenteZW, noRecorrencia, controlarAcesso, false, nivelMontarDados);
    }

    public List<FormaPagamentoVO> consultarPorTipoFormaPagamentoGestaoDeNotas(String valorConsulta, boolean somenteZW, boolean noRecorrencia, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorTipoFormaPagamento(valorConsulta, somenteZW, noRecorrencia, controlarAcesso, true, nivelMontarDados);
    }

    public List<FormaPagamentoVO> consultarPorTipoFormaPagamento(String valorConsulta, boolean somenteZW, boolean noRecorrencia, boolean controlarAcesso, boolean considerarTodos, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM FormaPagamento WHERE upper( tipoFormaPagamento ) ");
        sqlStr.append(" like('").append(valorConsulta.toUpperCase()).append("%')");
        if (!considerarTodos) {
            sqlStr.append("\n and ativo");
        }
        if (somenteZW) {
            sqlStr.append(" and not somentefinanceiro ");
        }
        if (noRecorrencia) {
            sqlStr.append(" and not defaultrecorrencia ");
        }
        sqlStr.append(" ORDER BY tipoFormaPagamento");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<FormaPagamentoVO> consultarPorTiposFormaPagamento(List<TipoFormaPagto> tipoFormaPagtos,
                                                                  Integer empresa,
                                                                  boolean somenteAtivos,
                                                                  boolean somenteZW,
                                                                  Boolean receberSomenteViaPinPad,
                                                                  int nivelMontarDados) throws Exception {

        String sql = "SELECT * FROM FormaPagamento " +
                "WHERE upper( tipoFormaPagamento ) ";
        sql += " in (";
        for(TipoFormaPagto tipoFormaPagto: tipoFormaPagtos){
            sql += "'"+tipoFormaPagto.getSigla()+"',";
        }
        sql = sql.substring(0, sql.length() - 1);
        sql += ")";

        if (somenteAtivos) {
            sql += " and ativo is true";
        }
        if (somenteZW) {
           sql += " and not somentefinanceiro ";
        }

        if(receberSomenteViaPinPad != null){
            sql += " and receberSomenteViaPinPad is "+receberSomenteViaPinPad;
        }


        if(!UteisValidacao.emptyNumber(empresa)){
            sql += " and (codigo in (select formapagamento from formapagamentoempresa where empresa = " + empresa +
                    ") or not exists(select codigo from formapagamentoempresa where formapagamento = formapagamento.codigo))";
        }

        sql += " ORDER BY tipoFormaPagamento";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<FormaPagamentoVO> consultarPorTiposFormaPagamentoSimples(final TipoFormaPagto[] tipos,
                                                                  Integer empresa,
                                                                  boolean somenteAtivos,
                                                                  int nivelMontarDados) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT * FROM FormaPagamento WHERE tipoFormaPagamento in (");

        String condicaoTipos = "";
        if (tipos != null) {
            condicaoTipos = Uteis.splitFromArrayWithStringSeparator(tipos, false, "sigla");
        }

        sb.append(condicaoTipos);
        sb.append(") ");

        if (somenteAtivos) {
            sb.append(" and ativo is true ");
        }

        sb.append(" ORDER BY tipoFormaPagamento");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }



    /**
     * Operação responsável por localizar um objeto da classe <code>FormaPagamentoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public FormaPagamentoVO consultarPorTipoFormaPagamento(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM FormaPagamento WHERE upper( tipoFormaPagamento ) like('" + valorConsulta.toUpperCase() + "%') and ativo ORDER BY tipoFormaPagamento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>FormaPagamento</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Convenio</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>FormaPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoConvenioCobranca(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT FormaPagamento.* FROM FormaPagamento, ConvenioCobranca WHERE FormaPagamento.convenio = ConvenioCobranca.codigo and upper( ConvenioCobranca.descricao ) like('" + valorConsulta.toUpperCase() + "%') and ativo ORDER BY ConvenioCobranca.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>FormaPagamento</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>FormaPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<FormaPagamentoVO> consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FormaPagamento WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') and ativo ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>FormaPagamento</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>FormaPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoTipoFormaPagamento(String valorConsulta,
                                                        boolean controlarAcesso, boolean somenteZW, boolean naoRecorrencia,  boolean somenteAtivo, int nivelMontarDados) throws Exception {
        return consultarPorDescricaoTipoFormaPagamento(valorConsulta, controlarAcesso, somenteZW, naoRecorrencia,  somenteAtivo, null, nivelMontarDados);
    }

    public List consultarPorDescricaoTipoFormaPagamento(String valorConsulta,
                                                        boolean controlarAcesso,
                                                        boolean somenteZW,
                                                        boolean naoRecorrencia,
                                                        boolean somenteAtivo,
                                                        Integer empresa,
                                                        int nivelMontarDados) throws Exception {
        return consultarPorDescricaoTipoFormaPagamento(valorConsulta, controlarAcesso, somenteZW, naoRecorrencia, somenteAtivo, empresa, nivelMontarDados, null);
    }

    public List consultarPorDescricaoTipoFormaPagamento(String valorConsulta,
                                                        boolean controlarAcesso,
                                                        boolean somenteZW,
                                                        boolean naoRecorrencia,
                                                        boolean somenteAtivo,
                                                        Integer empresa,
                                                        int nivelMontarDados,
                                                        Boolean pinpad) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FormaPagamento WHERE upper( descricao ) like('"
                + valorConsulta.toUpperCase() + "%') and tipoFormaPagamento not in ('PD')";

        if(pinpad != null){
            sqlStr = sqlStr  +  " and receberSomenteViaPinPad is "+pinpad;
        }

        if (somenteAtivo){
            sqlStr = sqlStr  +  " and ativo ";
        }
        if (somenteZW) {
            sqlStr += " and not somentefinanceiro ";
        }
        if (naoRecorrencia) {
            sqlStr += " and not defaultRecorrencia ";
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sqlStr += "and (codigo in (select formapagamento from formapagamentoempresa where empresa = " + empresa +
                    ") or not exists(select codigo from formapagamentoempresa where formapagamento = formapagamento.codigo))";
        }
        sqlStr += " ORDER BY tipoFormaPagamento";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    @Override
    public Set<String> consultarTipoTodosEmpresa(Integer empresa) throws Exception {
        final StringBuilder sqlSelectTipoTodasFormasPagamento =
                new StringBuilder("SELECT fp.tipoFormaPagamento as \"fp.tipoFormaPagamento\", fpe.codigo as \"fpe.codigo\", fpe.empresa as \"fpe.empresa\" " +
                        "FROM FormaPagamento fp")
                        .append(" LEFT JOIN formapagamentoempresa fpe ON fpe.formapagamento = fp.codigo")
                        .append(" WHERE fp.tipoFormaPagamento <> 'PD'")
                        .append(" AND fp.ativo")
                        .append(" AND NOT fp.somentefinanceiro")
                        .append(" AND NOT fp.defaultRecorrencia")
                        .append(" ORDER BY fp.tipoFormaPagamento");

        Statement stm = null;
        ResultSet tabelaResultado = null;

        try {
            stm = con.createStatement();
            tabelaResultado = stm.executeQuery(sqlSelectTipoTodasFormasPagamento.toString());

            final Set<String> tiposFormaPagamento = new HashSet<String>();
            while (tabelaResultado.next()) {
                Integer codigoEmpresa = tabelaResultado.getInt("fpe.empresa");
                if (UteisValidacao.emptyNumber(codigoEmpresa)) {
                    tiposFormaPagamento.add(tabelaResultado.getString("fp.tipoFormaPagamento"));
                } else if (codigoEmpresa.equals(empresa)) {
                    tiposFormaPagamento.add(tabelaResultado.getString("fp.tipoFormaPagamento"));
                }
            }
            return tiposFormaPagamento;

        } finally {
            ConsultaUtil.fecharResultSetQuietly(tabelaResultado);
            ConsultaUtil.fecharStatementQuietly(stm);
        }

    }

    /**
     * Responsável por realizar uma consulta de <code>FormaPagamento</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>FormaPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FormaPagamento WHERE codigo >= " + valorConsulta + " and ativo ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public FormaPagamentoVO obterPorCodigo(Integer codigo) throws Exception {
        String sqlStr = "SELECT * FROM FormaPagamento WHERE codigo = " + codigo;
        try (Statement statement = con.createStatement()) {
            try (ResultSet resultSet = statement.executeQuery(sqlStr)) {
                if(resultSet.next()){
                    return montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            }
        }

        return null;
    }

    public List<FormaPagamentoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<FormaPagamentoVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            FormaPagamentoVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>FormaPagamentoVO</code>.
     * @return  O objeto da classe <code>FormaPagamentoVO</code> com os dados devidamente montados.
     */
    public FormaPagamentoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        FormaPagamentoVO obj = new FormaPagamentoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setTipoFormaPagamento(dadosSQL.getString("tipoFormaPagamento"));
        obj.setCor(dadosSQL.getString("cor"));
        try {
            obj.setApresentarNSU(dadosSQL.getBoolean("apresentarnsu"));
        }catch (Exception ignored) {

        }
        if (Uteis.NIVELMONTARDADOS_MINIMOS == nivelMontarDados) {
            return obj;
        }

        obj.getConvenioCobrancaVO().setCodigo(dadosSQL.getInt("ConvenioCobranca"));
        obj.setTaxaCartao(dadosSQL.getDouble("taxaCartao"));
        obj.setDefaultRecorrencia(dadosSQL.getBoolean("defaultRecorrencia"));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        obj.setSomenteFinanceiro(dadosSQL.getBoolean("somenteFinanceiro"));
        obj.setDiasCompensacaoCartaoCredito(dadosSQL.getInt("diasCompensacaoCartaoCredito"));
        obj.setExigeCodAutorizacao(dadosSQL.getBoolean("exigecodautorizacao"));
        obj.setReceberSomenteViaPinPad(dadosSQL.getBoolean("receberSomenteViaPinPad"));
        obj.setNrMaxParcelasPinpad(dadosSQL.getInt("nrMaxParcelasPinpad"));
        obj.setPosIDGeoitd(dadosSQL.getString("posidgeoitd"));
        obj.setSystemIdGeoitd(dadosSQL.getString("systemidgeoitd"));
        obj.setTaxaPix(dadosSQL.getDouble("taxapix"));
        obj.setTaxaPixValorAbsoluto(dadosSQL.getBoolean("taxaPixValorAbsoluto"));

        if (dadosSQL.getInt("tipoDebitoOnline") != 0) {
            obj.setTipoDebitoOnline(TipoDebitoOnlineEnum.getTipoDebitoOnlineEnum(dadosSQL.getInt("tipoDebitoOnline")));
        }
        obj.setCartaoDebitoOnline(dadosSQL.getBoolean("cartaoDebitoOnline"));
        obj.setMerchantid(dadosSQL.getString("merchantid"));
        obj.setMerchantkey(dadosSQL.getString("merchantkey"));
        try {
            obj.setDefaultDCO(dadosSQL.getBoolean("defaultdco"));
            obj.setCompensacaoDiasUteis(dadosSQL.getBoolean("compensacaoDiasUteis"));
        } catch (Exception e) {
            //não fazer nada, pois pode estar sendo executado alguma coisa no ExecutarProcessos quando ainda não existia esta coluna!
        }
        try {
            PinPad pinPad = new PinPad(this.con);
            obj.setListaPinPad(pinPad.consultarPorForma(obj.getCodigo()));
            pinPad = null;
            obj.setGerarPontos(dadosSQL.getBoolean("gerarpontos"));
            obj.setTipoParceiro(TipoParceiroEnum.valueOf(dadosSQL.getInt("tipoparceiro")));
            obj.setTipoConvenioCobranca(TipoConvenioCobrancaEnum.valueOf(dadosSQL.getInt("tipoConvenioCobranca")));
            obj.getPinpad().setPinpad(dadosSQL.getInt("pinpad"));
        } catch (Exception ignored) {
        }

        try {
            FormaPagamentoPerfilAcesso formaPagamentoPerfilAcessoDAO = new FormaPagamentoPerfilAcesso(this.con);
            obj.setFormasPerfilAcesso(formaPagamentoPerfilAcessoDAO.consultarPorForma(obj.getCodigo()));
            formaPagamentoPerfilAcessoDAO = null;
        } catch (Exception ignored) {
        }

        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosConvenioCobranca(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
        montarDadosTaxaCartao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
        return obj;
    }
    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ConvenioVO</code> relacionado ao objeto <code>FormaPagamentoVO</code>.
     * Faz uso da chave primária da classe <code>ConvenioVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosConvenioCobranca(FormaPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getConvenioCobrancaVO().getCodigo() == 0) {
            obj.setConvenioCobrancaVO(new ConvenioCobrancaVO());
            return;
        }

        ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
        obj.setConvenioCobrancaVO(convenioCobrancaDAO.consultarPorCodigoSemInfoEmpresa(obj.getConvenioCobrancaVO().getCodigo(), nivelMontarDados));
        convenioCobrancaDAO = null;
    }

    public static void montarDadosTaxaCartao(FormaPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        TaxaCartao taxaCartao = new TaxaCartao(con);
        obj.setTaxasCartao(taxaCartao.consultarPorFormaPagamento(obj.getCodigo(), null, nivelMontarDados));
        taxaCartao = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>FormaPagamentoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public FormaPagamentoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        FormaPagamentoVO eCache = (FormaPagamentoVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }

        String sql = "SELECT * FROM FormaPagamento WHERE codigo = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public String consultaTipoFormaPagamento(Integer codigo)throws Exception{
        // Consultar os dados da tabela MovContaRateio
        FormaPagamentoVO formaPagamentoVO = new FormaPagamentoVO();
        try (PreparedStatement pst = con.prepareStatement("select * from formapagamento f where codigo = ?")) {
            pst.setInt(1, codigo);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    formaPagamentoVO.setTipoFormaPagamento(rs.getString("tipoformapagamento"));
                }
            }
        }
        return formaPagamentoVO.getTipoFormaPagamento().toUpperCase();
    }

    public boolean existeOutraFormaPagamentoDefault(FormaPagamentoVO obj, boolean recorrencia) throws SQLException {
        String sql = "select exists (select codigo from formapagamento where codigo <> ? ";
        if(recorrencia){
            sql += " and defaultRecorrencia is true) as existe";
        }else{
            sql += " and defaultdco is true) as existe";
        }
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, obj.getCodigo());
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }

    }
    
    public boolean existeOutraFormaPagamentoZW(FormaPagamentoVO obj) throws SQLException {
        String sql = "select exists (select codigo from formapagamento where codigo <> ? ";
        sql += " and tipoformapagamento  = ? and somentefinanceiro is false) as existe";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, obj.getCodigo());
            stm.setString(2, obj.getTipoFormaPagamento());
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }

    }
    

    @Override
    public FormaPagamentoVO criarOuConsultarSeExistePorDescricao(FormaPagamentoVO obj) throws Exception {
        String sql = "SELECT * FROM FormaPagamento WHERE descricao = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, obj.getDescricao());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    incluir(obj);
                    return obj;
                } else {
                    return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                }
            }
        }
    }
    @Override
    public FormaPagamentoVO criarOuConsultarSeExistePorFlag(FormaPagamentoVO obj) throws Exception {
        String sql = "SELECT * FROM FormaPagamento ";
        if(obj.getDefaultRecorrencia()){
            sql += " WHERE defaultrecorrencia ";
        }else if(obj.getDefaultDCO()){
            sql += " WHERE defaultdco ";
        } else {
            sql += " WHERE tipoformapagamento LIKE '" + obj.getTipoFormaPagamento() + "' AND remove_acento_upper(descricao) ilike remove_acento_upper('" + obj.getDescricao() + "')";
        }
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    incluir(obj);
                    return obj;
                } else {
                    return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                }
            }
        }
    }

    public FormaPagamentoVO consultarAVista() throws Exception {
        String sql = "SELECT * FROM FormaPagamento WHERE tipoformapagamento LIKE 'AV' AND not somentefinanceiro";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                } else {
                    return new FormaPagamentoVO();
                }
            }
        }
    }

    public FormaPagamentoVO consultarFormaPorConvenio(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        String sql = "SELECT * FROM FormaPagamento WHERE conveniocobranca = "+ convenioCobrancaVO.getCodigo()+" order by codigo desc limit 1" ;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                } else {
                    return new FormaPagamentoVO();
                }
            }
        }
    }

    public FormaPagamentoVO consultarPorTipo(TipoFormaPagto tipoFormaPagto) throws Exception {
        String sql = "SELECT * FROM formapagamento WHERE tipoformapagamento = '"+ tipoFormaPagto.getSigla()+"'";
        try (PreparedStatement statement = con.prepareStatement(sql)) {
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return (montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                } else {
                    return new FormaPagamentoVO();
                }
            }
        }
    }

    public FormaPagamentoVO consultarPorTipoFormaPagamentoAtiva(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM FormaPagamento WHERE upper( tipoFormaPagamento ) like('" + valorConsulta.toUpperCase() + "%') and ativo ORDER BY tipoFormaPagamento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                } else {
                    return new FormaPagamentoVO();
                }
            }
        }
    }

    public String consultarJSON(String situacao) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(situacao).executeQuery()) {
            json = new StringBuilder();
            FormaPagamentoVO formaPagamento = new FormaPagamentoVO();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao").trim())).append("\",");
                if (rs.getBoolean("situacao")) {
                    json.append("\"").append("Ativo").append("\",");
                } else {
                    json.append("\"").append("Inativo").append("\",");
                }
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("conveniocobranca"))).append("\",");
                formaPagamento.setTipoFormaPagamento(rs.getString("tipoformapagamento"));
                json.append("\"").append(formaPagamento.getTipoFormaPagamento_Apresentar()).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(String situacao) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("  SELECT fp.codigo, fp.descricao, cc.descricao AS conveniocobranca, ativo AS situacao, tipoformapagamento\n");
        sql.append("  FROM formapagamento fp LEFT JOIN conveniocobranca cc\n" );
        sql.append("  ON fp.conveniocobranca = cc.codigo ");
        if(situacao.equals("AT")){
            sql.append(" WHERE ativo ");
        }else if(situacao.equals("IN")){
            sql.append(" WHERE not ativo ");
        }
        sql.append(" ORDER BY fp.descricao");
        return con.prepareStatement(sql.toString());
    }

    public List consultarParaImpressao(String situacao, String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
        List lista;
        try (PreparedStatement ps = getPS(situacao)) {
            lista = new ArrayList();
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {

                    FormaPagamentoVO fPagamento = new FormaPagamentoVO();
                    String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("conveniocobranca") + rs.getString("situacao") + rs.getString("tipoformapagamento");
                    if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                        fPagamento.setCodigo(rs.getInt("codigo"));
                        fPagamento.setDescricao(rs.getString("descricao"));
                        fPagamento.getConvenioCobrancaVO().setDescricao(rs.getString("conveniocobranca"));
                        fPagamento.setAtivo(rs.getBoolean("situacao"));
                        fPagamento.setTipoFormaPagamento(rs.getString("tipoformapagamento"));
                        lista.add(fPagamento);
                    }
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Situacao")) {
            Ordenacao.ordenarLista(lista, "situacao_Apresentar");
        } else if (campoOrdenacao.equals("Convênio")) {
            Ordenacao.ordenarLista(lista, "convenio_Apresentar");
        } else if (campoOrdenacao.equals("Tipo")) {
            Ordenacao.ordenarLista(lista, "tipoFormaPagamento_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
    
    public void atualizarCorFormaPagamento(Integer codigo, String cor) throws Exception{
        executarConsulta("UPDATE formapagamento SET cor = '"+cor+"' WHERE codigo = "+codigo, con);
    }

    public List<FormaPagamentoVO> consultarFormaPagamentoComConvenioCobranca(boolean somenteAtivo, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM FormaPagamento \n");
        sqlStr.append("WHERE 1 = 1 \n");
        sqlStr.append("AND conveniocobranca is not null \n");
        if (somenteAtivo) {
            sqlStr.append(" AND ativo = true ");
        }
        sqlStr.append(" ORDER BY conveniocobranca");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<FormaPagamentoVO> consultarSimplesFormasPagamento(boolean exibirfinanceiro) throws Exception {
        List<FormaPagamentoVO> formas = new ArrayList<FormaPagamentoVO>();
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("select codigo, descricao from formapagamento where ativo ");
        if (!exibirfinanceiro) {
            sqlStr.append(" and not somentefinanceiro  ");
        }
        sqlStr.append(" order by descricao ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                while (tabelaResultado.next()) {
                    FormaPagamentoVO forma = new FormaPagamentoVO();
                    forma.setCodigo(tabelaResultado.getInt("codigo"));
                    forma.setDescricao(tabelaResultado.getString("descricao"));
                    formas.add(forma);
                }
            }
        }
        return formas;
    }


    public TipoFormaPagto consultarTipoFormasPagamento(Integer codigo) throws Exception {
        List<FormaPagamentoVO> formas = new ArrayList<FormaPagamentoVO>();
        ResultSet tabelaResultado = SuperFacadeJDBC.criarConsulta("select tipoformapagamento from formapagamento where codigo =  "+codigo, con);
        if(tabelaResultado.next()){
            return TipoFormaPagto.getTipoFormaPagtoSigla(tabelaResultado.getString("tipoformapagamento"));
        }
        return null;
    }

    @Override
    public boolean existeOutraFormaPagamentoMesmaDescricao(FormaPagamentoVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT 1 FROM formapagamento WHERE remove_acento_upper(descricao) ILIKE remove_acento_upper(?) ");
        if(!UteisValidacao.emptyNumber(obj.getCodigo())){
            sql.append("AND codigo <> ").append(obj.getCodigo());
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setString(1, obj.getDescricao());
            return ps.executeQuery().next();
        }
    }

    public FormaPagamentoVO obterFormaPagamentoRapido(TipoFormaPagto tipo) throws Exception{
        String sql = "select * from formapagamento  where tipoformapagamento = '"
                +tipo.getSigla()+
                "' and not defaultrecorrencia and ativo and not somentefinanceiro ORDER BY codigo  limit 1";
        ResultSet rs = criarConsulta(sql, con);
        return rs.next() ? montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) : new FormaPagamentoVO();
    }

    public List<SelectItem> consultarTipoDebitoOnlineCadastrados() throws Exception {
        List<SelectItem> retorno = new ArrayList<SelectItem>();
        String sql = "select distinct(tipodebitoonline) from formapagamento  where tipodebitoonline  <> 0 and cartaodebitoonline = true order by 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                while (tabelaResultado.next()) {
                    TipoDebitoOnlineEnum tipo = TipoDebitoOnlineEnum.getTipoDebitoOnlineEnum(tabelaResultado.getInt("tipodebitoonline"));
                    retorno.add(new SelectItem(tipo, tipo.getDescricao()));
                }
            }
        }
        return retorno;
    }

    public List<SelectItem> opcoesFormaEmpresa(Integer empresa, TipoFormaPagto tipo) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select fp.codigo, descricao from formapagamento fp \n");
        sql.append(" left join formapagamentoempresa fpe on fpe.formapagamento = fp.codigo \n");
        sql.append(" where fpe.codigo is null or fpe.empresa = ").append(empresa);
        if(tipo != null){
            sql.append(" and fp.tipoformapagamento = '").append(tipo.getSigla()).append("' \n");
        }
        sql.append(" order by descricao");
        ResultSet rs = criarConsulta(sql.toString(), con);
        List<SelectItem> list = new ArrayList<SelectItem>();
        while(rs.next()){
            list.add(new SelectItem(rs.getInt("codigo"), rs.getString("descricao")));
        }
        return list;
    }

    public List<FormaPagamentoVO> consultarPorTipoFormaPagamento(TipoFormaPagto tipoFormaPagto, Boolean situacao, int nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM FormaPagamento WHERE 1 = 1 \n");
        if (tipoFormaPagto != null) {
            sqlStr.append(" AND upper(tipoFormaPagamento) = '").append(tipoFormaPagto.getSigla().toUpperCase()).append("' \n");
        }
        if (situacao != null) {
            sqlStr.append(" AND ativo = ").append(situacao).append(" \n");
        }
        sqlStr.append(" ORDER BY tipoFormaPagamento");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public FormaPagamentoVO obterFormaPagamentoCartaoRecorrente() throws Exception {
        FormaPagamentoVO formaPagamentoVO = new FormaPagamentoVO();
        formaPagamentoVO.setDefaultRecorrencia(true);
        formaPagamentoVO.setDescricao("CARTÃO RECORRENTE");
        formaPagamentoVO.setTipoFormaPagamento("CA");
        formaPagamentoVO = criarOuConsultarSeExistePorFlag(formaPagamentoVO);
        return formaPagamentoVO;
    }

    public List<FormaPagamentoVO> consultarFormaPagamentoAtivoPorConvenioCobranca(int codConvenioCobranca, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM FormaPagamento \n");
        sqlStr.append("WHERE 1 = 1 \n");
        sqlStr.append("AND conveniocobranca = " + codConvenioCobranca + " \n");
        sqlStr.append(" AND ativo = true ");
        sqlStr.append(" ORDER BY conveniocobranca");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }
}
