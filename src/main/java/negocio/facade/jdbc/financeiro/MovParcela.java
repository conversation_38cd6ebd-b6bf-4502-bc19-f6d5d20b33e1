package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoConsultaParcelasEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoEventoEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estrutura.paginacao.PreparedStatementPersonalizado;
import br.com.pactosolucoes.integracao.pactopay.front.FiltroPactoPayDTO;
import br.com.pactosolucoes.integracao.pactopay.front.TotalizadorPendenteDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.LoginControle;
import controle.financeiro.MovParcelaTransacaoTO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ObservacaoOperacaoVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoObservacaoOperacaoEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.BIInadimplenciaTO;
import negocio.comuns.financeiro.CaixaAbertoTO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.InadimplenciaTO;
import negocio.comuns.financeiro.InadimplenteTO;
import negocio.comuns.financeiro.InfoExtratoParcelaTO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaFiltroVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
import negocio.facade.jdbc.basico.ObservacaoOperacao;
import negocio.facade.jdbc.basico.PactoPayConfig;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.webservice.ParcelasRenegociadasJSON;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;
import negocio.facade.jdbc.crm.Feriado;
import negocio.facade.jdbc.plano.ConvenioCobrancaEmpresa;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.interfaces.financeiro.MovParcelaInterfaceFacade;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.basico.RecorrenciaClienteTO;
import relatorio.negocio.comuns.basico.ResumoPessoaRelBIInadimplenciaVO;
import relatorio.negocio.jdbc.financeiro.ParcelaConsolidadaTO;
import relatorio.negocio.jdbc.financeiro.ParcelaSPCTO;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.cielo.DCCCieloStatusEnum;
import servicos.impl.dcc.itau.BBCnab400ItauStatusEnum;
import servicos.impl.microsservice.integracoes.AcaoSPCCallable;
import servicos.pix.PixStatusEnum;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Classe de persist?ncia que encapsula todas as opera??es de manipula??o dos
 * dados da classe
 * <code>MovParcelaVO</code>. Respons?vel por implementar opera??es como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>MovParcelaVO</code>. Encapsula toda a intera??o com o banco de dados.
 *
 * @see MovParcelaVO
 * @see SuperEntidade
 */
public class MovParcela extends SuperEntidade implements MovParcelaInterfaceFacade {

    private static final String sqlUpdate = "UPDATE MovParcela set dataRegistro=?, dataVencimento=?, situacao=?, "
            + "contrato=?, responsavel=?, percentualMulta=?, percentualJuro=?, utilizaConvenio=?, "
            + "convenioCobranca=?, imprimirBoletoParcela=?, valorParcela = ?, descricao = ?, "
            + "vendaAvulsa=?, aulaAvulsaDiaria=?, personal=?, dataAlteracaoManual=?, nrtentativas=?, "
            + "regimerecorrencia=?, empresa=?, pessoa=?, movimentocc=?, movpagamentocc=?, reagendada = ?, datacobranca = ?, "
            + "parcelaDCC = ?, numeroParcelasOperadora = ?, planoPersonal = ?, parcelasRenegociadas = ?, justificativaParcelasRenegociadas = ?, idExterno = ? WHERE codigo = ?";
    private static final String sqlInsert = "INSERT INTO MovParcela( dataRegistro, dataVencimento, situacao, contrato, "
            + "responsavel, percentualMulta, percentualJuro, utilizaConvenio, convenioCobranca, "
            + "imprimirBoletoParcela , valorParcela, descricao, vendaAvulsa, "
            + "aulaAvulsaDiaria, personal, dataAlteracaoManual, nrtentativas, "
            + "regimerecorrencia, empresa, pessoa, movimentocc, movpagamentocc, datacobranca, parcelaDCC, numeroParcelasOperadora, planoPersonal, parcelasRenegociadas, idexterno, justificativaParcelasRenegociadas) "
            + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING codigo";
    private Hashtable movProdutoParcelas;

    public MovParcela() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public MovParcela(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    /**
     * Opera??o respons?vel por retornar um novo objeto da classe
     * <code>MovParcelaVO</code>.
     */
    @Override
    public MovParcelaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new MovParcelaVO();
    }

    public void incluir(MovParcelaVO obj) throws Exception {
        incluir(obj, true);
    }
    /**
     * Opera??o respons?vel por incluir no banco de dados um objeto da classe
     * <code>MovParcelaVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conex?o com o banco de
     * dados e a permiss?o do usu?rio para realizar esta operac?o na entidade.
     * Isto, atrav?s da opera??o
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovParcelaVO</code> que ser? gravado no
     * banco de dados.
     * @exception Exception Caso haja problemas de conex?o, restri??o de acesso
     * ou valida??o de dados.
     */
    @Override
    public void incluir(MovParcelaVO obj, boolean controleTransacao) throws Exception {
        MovProduto movProdutoDAO = null;
        MovProdutoParcela movProdutoParcelaDAO = null;
        try {
            if (controleTransacao) {
                con.setAutoCommit(false);
            }

            movProdutoDAO = new MovProduto(this.con);
            movProdutoParcelaDAO = new MovProdutoParcela(this.con);

            try (PreparedStatement sqlInserir = prepararIncluir(obj)) {
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());

            movProdutoParcelaDAO.incluirMovProdutoParcelas(obj.getCodigo(), obj.getMovProdutoParcelaVOs());
            Iterator i = obj.getContrato().getMovProdutoVOs().iterator();
            while (i.hasNext()) {
                MovProdutoVO movProduto = (MovProdutoVO) i.next();
                if (movProduto.getQuitado()) {
                    movProdutoDAO.alterarSemCommit(movProduto);
                }
            }
            obj.setNovoObj(false);
            atualizarParcelaDCC(obj.getCodigo(), obj.getPessoa().getCodigo(), null, null);
            if (controleTransacao) {
                con.commit();
            }

        } catch (Exception e) {
            obj.setNovoObj(true);
            if (controleTransacao) {
                con.rollback();
            }
            throw e;
        } finally {
            movProdutoDAO = null;
            movProdutoParcelaDAO = null;
            if (controleTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    @Override
    public void incluirComProdutosSemCommit(MovParcelaVO obj) throws Exception {
        try (PreparedStatement sqlInserir = prepararIncluir(obj)) {
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());

        if (obj.getMovParcelaDetalhamentoVO() != null) {
            MovParcelaDetalhamento movParcelaDetalhamento = new MovParcelaDetalhamento(con);
            movParcelaDetalhamento.incluirSemCommit(obj.getMovParcelaDetalhamentoVO());
            movParcelaDetalhamento = null;
        }

        MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(this.con);
        movProdutoParcelaDAO.incluirMovProdutoParcelas(obj.getCodigo(), obj.getMovProdutoParcelaVOs());
        movProdutoParcelaDAO = null;

        MovProduto movProdutoDAO = new MovProduto(this.con);
        for (Object o : obj.getContrato().getMovProdutoVOs()) {
            MovProdutoVO movProduto = (MovProdutoVO) o;
            if (movProduto.getQuitado()) {
                movProdutoDAO.alterarSemCommit(movProduto);
            }
        }
        movProdutoDAO = null;
        obj.setNovoObj(false);
        atualizarParcelaDCC(obj.getCodigo(), obj.getPessoa().getCodigo(), null, null);
    }

    public void incluirSemCommit(MovParcelaVO obj) throws Exception {
        try (PreparedStatement sqlInserir = prepararIncluir(obj)) {
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        atualizarParcelaDCC(obj.getCodigo(), obj.getPessoa().getCodigo(), null, null);
    }

    private PreparedStatement prepararIncluir(MovParcelaVO obj) throws Exception {
        return prepararIncluir(obj, false);
    }

    private PreparedStatement prepararIncluir(MovParcelaVO obj, boolean incluindoAPartirRenegociacao) throws Exception {
        MovParcelaVO.validarDados(obj);
        //incluir(getIdEntidade());
        obj.realizarUpperCaseDados();

        PreparedStatement sqlInserir = con.prepareStatement(sqlInsert);
        sqlInserir.setDate(1, Uteis.getDataJDBC(obj.getDataRegistro()));
        sqlInserir.setDate(2, Uteis.getDataJDBC(obj.getDataVencimento()));
        sqlInserir.setString(3, obj.getSituacao());

        if (obj.getContrato().getCodigo() != 0) {
            sqlInserir.setInt(4, obj.getContrato().getCodigo());
        } else {
            sqlInserir.setNull(4, 0);
        }
        if (obj.getResponsavel().getCodigo() != 0) {
            sqlInserir.setInt(5, obj.getResponsavel().getCodigo());
        } else {
            sqlInserir.setNull(5, 0);
        }
        sqlInserir.setDouble(6, obj.getPercentualMulta());
        sqlInserir.setDouble(7, obj.getPercentualJuro());
        sqlInserir.setBoolean(8, obj.isUtilizaConvenio());
        if (obj.getConvenioCobranca().getCodigo() != 0) {
            sqlInserir.setInt(9, obj.getConvenioCobranca().getCodigo());
        } else {
            sqlInserir.setNull(9, 0);
        }
        sqlInserir.setBoolean(10, obj.isImprimirBoletoParcela());
        if(incluindoAPartirRenegociacao && Uteis.arredondarForcando2CasasDecimais(obj.getValorParcela()) <= 0.0 && obj.getSituacao().equals("EA")){
            throw new Exception("Não foi possivel concluir a operação, inconsistencia no valor da parcela a ser gerada.");
        }else{
            sqlInserir.setDouble(11, Uteis.arredondarForcando2CasasDecimais(obj.getValorParcela()));
        }
        sqlInserir.setString(12, obj.getDescricao());
        if (obj.getVendaAvulsaVO().getCodigo() != 0) {
            sqlInserir.setInt(13, obj.getVendaAvulsaVO().getCodigo());
        } else {
            sqlInserir.setNull(13, 0);
        }
        if (obj.getAulaAvulsaDiariaVO().getCodigo() != 0) {
            sqlInserir.setInt(14, obj.getAulaAvulsaDiariaVO().getCodigo());
        } else {
            sqlInserir.setNull(14, 0);
        }
        if (obj.getPersonal().getCodigo() != 0) {
            sqlInserir.setInt(15, obj.getPersonal().getCodigo());
        } else {
            sqlInserir.setNull(15, 0);
        }
        if (obj.getDataAlteracaoManual() == null) {
            sqlInserir.setNull(16, 0);
        } else {
            sqlInserir.setDate(16, Uteis.getDataJDBC(obj.getDataAlteracaoManual()));
        }
        sqlInserir.setInt(17, obj.getNrTentativas());
        if (obj.getContrato() != null && obj.getContrato().getCodigo() != 0
                && obj.getContrato().getRegimeRecorrencia()) {
            obj.setRegimeRecorrencia(true);
        }
        sqlInserir.setBoolean(18, obj.getRegimeRecorrencia());
        if (obj.getEmpresa().getCodigo() != 0) {
            sqlInserir.setInt(19, obj.getEmpresa().getCodigo());
        } else {
            sqlInserir.setNull(19, 0);
        }
        if (obj.getPessoa().getCodigo() != 0) {
            sqlInserir.setInt(20, obj.getPessoa().getCodigo());
        } else {
            sqlInserir.setNull(20, 0);
        }

        sqlInserir.setBoolean(21, obj.isMovimentoCC());
        sqlInserir.setString(22, obj.getMovPagamentoCC());

        sqlInserir.setDate(23, Uteis.getDataJDBC(obj.getDataVencimento()));
        sqlInserir.setBoolean(24, false);
        if (UteisValidacao.emptyNumber(obj.getNumeroParcelasOperadora())) {
            sqlInserir.setNull(25, 0);
        } else {
            sqlInserir.setInt(25, obj.getNumeroParcelasOperadora());
        }

        if(UteisValidacao.emptyNumber(obj.getPlanoPersonal())){
            sqlInserir.setNull(26, 0);
        }else{
            sqlInserir.setInt(26, obj.getPlanoPersonal());
        }
        sqlInserir.setString(27, obj.getParcelasRenegociadas());
        if (UteisValidacao.emptyNumber(obj.getIdExterno())) {
            sqlInserir.setNull(28, 0);
        } else {
            sqlInserir.setInt(28, obj.getIdExterno());
        }
        if (UteisValidacao.emptyString(obj.getJustificativaParcelasRenegociadas())) {
            sqlInserir.setString(29, "");
        } else {
            sqlInserir.setString(29, obj.getJustificativaParcelasRenegociadas());
        }

        return sqlInserir;
    }

    /**
     * Opera??o respons?vel por incluir no banco de dados um objeto da classe
     * <code>MovParcelaVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conex?o com o banco de
     * dados e a permiss?o do usu?rio para realizar esta operac?o na entidade.
     * Isto, atrav?s da opera??o
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovParcelaVO</code> que ser? gravado no
     * banco de dados.
     * @exception Exception Caso haja problemas de conex?o, restri??o de acesso
     * ou valida??o de dados.
     */
    public void incluirParcelaSemCommit(MovParcelaVO obj) throws Exception {
        MovParcelaVO.validarDados(obj);
        incluirParcelaSemValidar(obj);
    }

    /**
     * M?todo que cont?m parte do m?todo incluirSemCommit, extra?da para este
     * novo m?todo para utiliza??o nas opera??es de pagamento do Central de
     * Eventos, tendo como raz?o a n?o compatibilidade do validarDados com os
     * dados vindo do CE.
     *
     * <AUTHOR>
     * @param obj
     * @throws Exception
     */
    public void incluirParcelaSemValidar(MovParcelaVO obj) throws Exception {
        incluirParcelaSemValidar(obj, false);
    }

    public void incluirParcelaSemValidar(MovParcelaVO obj, boolean incluindoAPartirRenegociacao) throws Exception {
        //incluir(getIdEntidade());
        obj.setValidarDados(false);
        try (PreparedStatement sqlInserir = prepararIncluir(obj, incluindoAPartirRenegociacao)) {
            try (ResultSet rs = sqlInserir.executeQuery()) {
                if (rs.next()) {
                    obj.setCodigo(rs.getInt("codigo"));
                }
            }
        }

        atualizarParcelaDCC(obj.getCodigo(), obj.getPessoa().getCodigo(), null, null);
    }

    /**
     * Opera??o respons?vel por alterar no BD os dados de um objeto da classe
     * <code>MovParcelaVO</code>. Sempre utiliza a chave prim?ria da classe como
     * atributo para localiza??o do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conex?o com o banco de
     * dados e a permiss?o do usu?rio para realizar esta operac?o na entidade.
     * Isto, atrav?s da opera??o
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovParcelaVO</code> que ser? alterada
     * no banco de dados.
     * @exception Exception Caso haja problemas de conex?o, restri??o de acesso
     * ou valida??o de dados.
     */
    public void alterar(MovParcelaVO obj, boolean atualizarDCC) throws Exception {
        try {
            con.setAutoCommit(false);

            try (PreparedStatement sqlAlterar = prepararAlterar(obj)) {
                sqlAlterar.execute();
            }
            if(atualizarDCC){
                ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(this.con);
                zillyonWebFacade.validarParcelaVencidaEmClienteMensagem(obj);
                zillyonWebFacade = null;
                atualizarParcelaDCC(obj.getCodigo(), obj.getPessoa().getCodigo(), null, null);
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    public void alterar(MovParcelaVO obj) throws Exception {
        alterar(obj, true);
    }
    public void cancelarParcelasArmarioEmAberto(List<MovParcelaVO> parcelasIgnorar,int empresa,int vendaAvulsa) throws Exception{
        con.setAutoCommit(false);
        StringBuilder sql = new StringBuilder();
        sql.append(" UPDATE MovParcela Set situacao = 'CA' WHERE ");
        sql.append(" vendaavulsa = ").append(vendaAvulsa).append("\n");
        sql.append(" AND situacao = 'EA'\n");
        if(!parcelasIgnorar.isEmpty()){
        sql.append(" AND codigo not in (");
        for (MovParcelaVO parcela : parcelasIgnorar){
         sql.append(parcela.getCodigo()).append(",");
        }

            sql.deleteCharAt(sql.length() - 1);
            sql.append(")");
        }
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.execute();
        }
        con.commit();
    }


    public void validarMovParcelaPagar(MovParcelaVO obj) throws  Exception{
        if ((obj.getContrato() == null || obj.getContrato().getCodigo() == 0) &&
                (obj.getVendaAvulsaVO() == null || obj.getVendaAvulsaVO().getCodigo() == 0) &&
                (obj.getAulaAvulsaDiariaVO() == null || obj.getAulaAvulsaDiariaVO().getCodigo() == 0) &&
                (obj.getPersonal() == null || obj.getPersonal().getCodigo() == 0)) {

            boolean parcelaEvento = existe("SELECT necp FROM negociacaoeventocontratoparcelas necp\n" +
                    "INNER JOIN movparcela mpar ON necp.parcela = mpar.codigo\n" +
                    "WHERE mpar.codigo = " + obj.getCodigo(), this.con);

            if (!parcelaEvento) {
                throw new ConsistirException("Houve um erro ao carregar as informa??es da Parcela. Por favor, volte ao caixa em aberto e refa?a a opera??o. MovParcela: " + obj.getCodigo());
            }
        }
    }

    /* Consulta específica criada a pedido do lider tecnico */
    private MovParcelaVO consultarSituacaoParcela(int codigoParcela) throws Exception {
        String sql = "SELECT situacao FROM MovParcela WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoParcela);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_REGISTRAR_JUSTIFICATIVA, this.con);
                }
            }
        }
        return new MovParcelaVO();
    }

    private void registrarJustificativa(MovParcelaVO movParcelaVO) throws Exception {

        if (movParcelaVO.getSituacao().equals("CA")) {
            MovParcelaVO movParcelaAtual = consultarSituacaoParcela(movParcelaVO.getCodigo());
            if (!movParcelaAtual.getSituacao().equals("CA")) {
                ObservacaoOperacaoVO observacaoOperacaoVO = new ObservacaoOperacaoVO();
                observacaoOperacaoVO.getMovParcela().setCodigo(movParcelaVO.getCodigo());
                observacaoOperacaoVO.setJustificativa(movParcelaVO.getJustificativaCancelamento());
                observacaoOperacaoVO.setTipoObservacao(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA);
                observacaoOperacaoVO.setValorOperacao(Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela()));
                observacaoOperacaoVO.setDataOperacao(Calendario.hoje());

                UsuarioVO usuarioVO;
                Usuario usuario = new Usuario(this.con);

                if (JSFUtilities.isJSFContext()) {
                    // Prioriza o usuário que foi informado na modal no momento do cancelamento, caso não exista, pega o usuário logado.
                    AutorizacaoFuncionalidadeControle autorizacaoFuncionalidadeControle = (AutorizacaoFuncionalidadeControle) context().getExternalContext().getSessionMap().get("AutorizacaoFuncionalidadeControle");
                    if (autorizacaoFuncionalidadeControle != null) {
                        usuarioVO = autorizacaoFuncionalidadeControle.getUsuario();
                    } else {
                        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
                        usuarioVO = loginControle.getUsuarioLogado();
                    }
                } else {
                    usuarioVO = usuario.consultarNomePorUsername("PACTOBR");
                }

                observacaoOperacaoVO.setUsuarioResponsavel(usuarioVO.getNome() + usuarioVO.getUserOamd());
                ObservacaoOperacao observacaoOperacao = new ObservacaoOperacao(this.con);
                observacaoOperacao.incluir(observacaoOperacaoVO);
            }
        }
    }


    private PreparedStatement prepararAlterar(MovParcelaVO obj) throws Exception {

        try {
            validarMovParcelaPagar(obj);
        } catch (Exception ex) {
            Uteis.logar(null, DAO.resolveKeyFromConnection(this.con));
            Uteis.logar(ex, MovParcela.class);
            throw ex;
        }

        registrarJustificativa(obj);

        PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate);
        //alterar(getIdEntidade());
        obj.realizarUpperCaseDados();

        sqlAlterar.setDate(1, Uteis.getDataJDBC(obj.getDataRegistro()));
        sqlAlterar.setDate(2, Uteis.getDataJDBC(obj.getDataVencimento()));
        sqlAlterar.setString(3, obj.getSituacao());
        if (obj.getContrato().getCodigo() != 0) {
            sqlAlterar.setInt(4, obj.getContrato().getCodigo());
        } else {
            sqlAlterar.setNull(4, 0);
        }
        if (obj.getResponsavel().getCodigo() != 0) {
            sqlAlterar.setInt(5, obj.getResponsavel().getCodigo());
        } else {
            sqlAlterar.setNull(5, 0);
        }
        sqlAlterar.setDouble(6, obj.getPercentualMulta());
        sqlAlterar.setDouble(7, obj.getPercentualJuro());
        sqlAlterar.setBoolean(8, obj.isUtilizaConvenio());
        if (obj.getConvenioCobranca().getCodigo() != 0) {
            sqlAlterar.setInt(9, obj.getConvenioCobranca().getCodigo());
        } else {
            sqlAlterar.setNull(9, 0);
        }
        sqlAlterar.setBoolean(10, obj.isImprimirBoletoParcela());
        sqlAlterar.setDouble(11, Uteis.arredondarForcando2CasasDecimais(obj.getValorParcela()));
        sqlAlterar.setString(12, obj.getDescricao());
        if (obj.getVendaAvulsaVO().getCodigo() != 0) {
            sqlAlterar.setInt(13, obj.getVendaAvulsaVO().getCodigo());
        } else {
            sqlAlterar.setNull(13, 0);
        }
        if (obj.getAulaAvulsaDiariaVO().getCodigo() != 0) {
            sqlAlterar.setInt(14, obj.getAulaAvulsaDiariaVO().getCodigo());
        } else {
            sqlAlterar.setNull(14, 0);
        }
        if (obj.getPersonal().getCodigo() != 0) {
            sqlAlterar.setInt(15, obj.getPersonal().getCodigo());
        } else {
            sqlAlterar.setNull(15, 0);
        }
        if (obj.getDataAlteracaoManual() == null) {
            sqlAlterar.setNull(16, 0);
        } else {
            sqlAlterar.setDate(16, Uteis.getDataJDBC(obj.getDataAlteracaoManual()));
        }
        sqlAlterar.setInt(17, obj.getNrTentativas());
        sqlAlterar.setBoolean(18, obj.getRegimeRecorrencia());
        if (obj.getEmpresa().getCodigo() != 0) {
            sqlAlterar.setInt(19, obj.getEmpresa().getCodigo());
        } else {
            sqlAlterar.setNull(19, 0);
        }
        if (obj.getPessoa().getCodigo() != 0) {
            sqlAlterar.setInt(20, obj.getPessoa().getCodigo());
        } else {
            sqlAlterar.setNull(20, 0);
        }
        sqlAlterar.setBoolean(21, obj.isMovimentoCC());

        sqlAlterar.setString(22, obj.getMovPagamentoCC());
        sqlAlterar.setBoolean(23, obj.isReagendada());
        sqlAlterar.setDate(24, Uteis.getDataJDBC(obj.getDataCobranca()));
        sqlAlterar.setBoolean(25, false);

        if (UteisValidacao.emptyNumber(obj.getNumeroParcelasOperadora())) {
            sqlAlterar.setNull(26, 0);
        } else {
            sqlAlterar.setInt(26, obj.getNumeroParcelasOperadora());
        }

        if (UteisValidacao.emptyNumber(obj.getNumeroParcelasOperadora())) {
            sqlAlterar.setInt(27, 0);
        }else{
            sqlAlterar.setInt(27, obj.getPlanoPersonal());
        }
        sqlAlterar.setString(28, obj.getParcelasRenegociadas());

        if (UteisValidacao.emptyString(obj.getJustificativaParcelasRenegociadas())) {
            sqlAlterar.setString(29, "");
        } else {
            sqlAlterar.setString(29, obj.getJustificativaParcelasRenegociadas());
        }
        resolveIntegerNull(sqlAlterar, 30, obj.getIdExterno());

        sqlAlterar.setInt(31, obj.getCodigo());
        return sqlAlterar;
    }

    /**
     * Opera??o respons?vel por alterar no BD os dados de um objeto da classe
     * <code>MovParcelaVO</code>. Sempre utiliza a chave prim?ria da classe como
     * atributo para localiza??o do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conex?o com o banco de
     * dados e a permiss?o do usu?rio para realizar esta operac?o na entidade.
     * Isto, atrav?s da opera??o
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovParcelaVO</code> que ser? alterada
     * no banco de dados.
     * @exception Exception Caso haja problemas de conex?o, restri??o de acesso
     * ou valida??o de dados.
     */
    public void alterarSemCommit(MovParcelaVO obj) throws Exception {
        alterar(getIdEntidade());
        try (PreparedStatement sqlAlterar = prepararAlterar(obj)) {
            sqlAlterar.execute();
        }
        atualizarParcelaDCC(obj.getCodigo(), obj.getPessoa().getCodigo(), null, null);
    }

    /**
     * Opera??o respons?vel por alterar no BD os dados de um objeto da classe
     * <code>MovParcelaVO</code>. Sempre utiliza a chave prim?ria da classe como
     * atributo para localiza??o do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conex?o com o banco de
     * dados e a permiss?o do usu?rio para realizar esta operac?o na entidade.
     * Isto, atrav?s da opera??o
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovParcelaVO</code> que ser? alterada
     * no banco de dados.
     * @exception Exception Caso haja problemas de conex?o, restri??o de acesso
     * ou valida??o de dados.
     */
    public void alterarSemCommit(MovParcelaVO obj, ReciboPagamentoVO reciboPagamentoVO) throws Exception {
        //alterar(getIdEntidade());
        try (PreparedStatement sqlAlterar = prepararAlterar(obj)) {
            sqlAlterar.execute();
        }
        atualizarParcelaDCC(obj.getCodigo(), obj.getPessoa().getCodigo(), null, null);
        MovProdutoParcela movProdutoParcelaDao = new MovProdutoParcela(con);
        MovProduto movProdutoDao = new MovProduto(con);
        try {
            obj.setMovProdutoParcelaVOs(movProdutoParcelaDao.consultarMovProdutoParcelasPorParcelas(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            Iterator i = obj.getMovProdutoParcelaVOs().iterator();
            while (i.hasNext()) {
                MovProdutoParcelaVO movProdutoParcela = (MovProdutoParcelaVO) i.next();
                movProdutoParcela.setReciboPagamento(reciboPagamentoVO);
                MovProdutoVO movProduto = movProdutoDao.consultarPorChavePrimaria(movProdutoParcela.getMovProduto(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                movProdutoParcela.setMovProdutoVO(movProduto);
                //Joao Alcides: ao pagar a parcela, cada produto s? deve ser marcado como pago
                //quando ele n?o tiver rela??o com parcelas ainda em aberto
                //(exceto, obviamente, a parcela que est? sendo paga)
                if (!produtoEmParcelasAbertas(movProduto.getCodigo(), movProdutoParcela.getMovParcela()) && !movProduto.getSituacao().equals("CA")) {
                    movProduto.setSituacao("PG");
                }
                movProdutoDao.alterarSemCommit(movProduto);
                movProdutoParcelaDao.alterar(movProdutoParcela);
            }
        } catch (Exception e) {
            throw e;
        }finally {
            movProdutoParcelaDao = null;
            movProdutoDao = null;
        }
        ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
        zillyonWebFacade.validarParcelaVencidaEmClienteMensagem(obj);
    }

    public void alterarSomenteSituacaoSemCommit(MovParcelaVO obj) throws Exception {
        //alterar(getIdEntidade());
        registrarJustificativa(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE MovParcela set situacao=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getSituacao());
            sqlAlterar.setInt(2, obj.getCodigo());
            sqlAlterar.execute();
        }

        if (!obj.getSituacao().equalsIgnoreCase("EA")) {
            Pix pix = new Pix(con);
            pix.cancelarAtivosPorParcelas(Arrays.asList(obj.getCodigo()));
            pix = null;
        }
    }

    private void aplicarDesconto(MovProdutoVO movProduto, double valorDesconto,boolean atualizarValorFaturado) {
        double valorFinalComDesconto = movProduto.getTotalFinal() - valorDesconto;
        movProduto.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(valorFinalComDesconto));
        movProduto.setValorDesconto(Uteis.arredondarForcando2CasasDecimais(movProduto.getPrecoUnitario() - movProduto.getTotalFinal()));
        if(atualizarValorFaturado){
            movProduto.setValorFaturado(Uteis.arredondarForcando2CasasDecimais(movProduto.getValorFaturado() - valorDesconto));
        }
    }

    public void renegociarParcelas(List<MovParcelaVO> listaMovParcelaRenegociar,
                                   List<MovParcelaVO> listaMovParcelaRenegociadas,
                                   MovParcelaVO movParcelaDesconto,
                                   MovParcelaVO movParcelaTaxa,
                                   String tipoProdutoExtra,
                                   boolean mapaParcelas,
                                   MovParcelaVO ultimaParcela,
                                   Double valorDesejado,
                                   double porcentagemDescontoDesejado,
                                   boolean temParcelaDeCC,
                                   UsuarioVO usuarioVO,
                                   boolean renegociacaoRedeEmpresa, boolean atualizarValorFaturado,
                                   boolean validarTransacaoPendente, String justificativa,
                                   OrigemSistemaEnum origemSistemaEnum) throws Exception{

        /*
                **************************** ATENÇÃO **********************************
                Este método é reutilizado por um processo que permite renegociar parcelas
                para uma rede de empresas, ou seja, em um mesmo processo este mesmo método pode ser chamado mais de uma vez
                para realizar alterações em banco de dados diferente. Desta forma, não é permitido fazer "getFacade()"

         */
        MovProdutoModalidade movProdutoModalidade = new MovProdutoModalidade(con);
        MovProdutoParcela movProdutoParcelaDao = new MovProdutoParcela(con);
        MovProduto movProdutoDao = new MovProduto(con);
        MovPagamento movPagamentoDao = new MovPagamento(con);
        ClienteMensagem clienteMensagemDao = new ClienteMensagem(con);
        MovParcela movParcelaDao = new MovParcela(con);
        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
        VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
        JSONObject jsonParcelas = new JSONObject();
        JSONArray arrayListaMovParcelaRenegociar = new JSONArray();
        try{
            List<MovProdutoVO> movProdutos = new ArrayList<>();
            List<MovProdutoParcelaVO> produtosParcelas = new ArrayList<>();

            for (MovParcelaVO movParcelaVO : listaMovParcelaRenegociar) {
                if(UteisValidacao.emptyNumber(movParcelaVO.getCodigo())){
                    continue;
                }
                if (validarTransacaoPendente) {
                    validarMovParcelaComTransacaoConcluidaOuPendente(movParcelaVO.getCodigo());
                }
                if(!validarSituacaoParcela(movParcelaVO.getCodigo(), "EA")){
                    throw new ConsistirException("Parcela " + movParcelaVO.getCodigo() +" não está em aberto, portanto não pode ser renegociada.");
                }

                JSONObject parcelaJSON = new JSONObject();
                parcelaJSON.put("codigo", movParcelaVO.getCodigo());
                parcelaJSON.put("descricao", movParcelaVO.getDescricao());
                parcelaJSON.put("valorParcela", movParcelaVO.getValorParcela());
                parcelaJSON.put("dataVencimento", movParcelaVO.getDataVencimento_Apresentar());
                arrayListaMovParcelaRenegociar.put(parcelaJSON);

                produtosParcelas.addAll(movProdutoParcelaDao.consultarMovProdutoParcelasPorParcelas(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            jsonParcelas.put("listaMovParcelaRenegociar", arrayListaMovParcelaRenegociar);
            if (justificativa != null) {
                jsonParcelas.put("observacao", justificativa);
            }

            Map<Integer, Double> produtosPagos = new HashMap<>();
            if (mapaParcelas) {
                int quantidadeItens = produtosParcelas.size();
                double valorDesejadoParcial = valorDesejado;
                for (MovProdutoParcelaVO movProdutoParcelaVO : produtosParcelas) {
                    quantidadeItens--;
                    double valorPago = movProdutoParcelaVO.getValorPago();
                    valorPago = Uteis.arredondarForcando2CasasDecimais(valorPago * (1 - (porcentagemDescontoDesejado / 100)));
                    if (ultimaParcela != null) {
                        if (quantidadeItens == 0) {
                            valorPago = valorDesejadoParcial;
                            valorDesejadoParcial = 0;
                        } else {
                            valorDesejadoParcial -= valorPago;
                        }
                    }

                    if (produtosPagos.containsKey(movProdutoParcelaVO.getMovProduto())) {
                        produtosPagos.put(movProdutoParcelaVO.getMovProduto(), valorPago + produtosPagos.get(movProdutoParcelaVO.getMovProduto()));
                    } else {
                        produtosPagos.put(movProdutoParcelaVO.getMovProduto(), valorPago);
                    }
                }
            } else {
                for (MovProdutoParcelaVO movProdutoParcelaVO : produtosParcelas) {
                    if (produtosPagos.containsKey(movProdutoParcelaVO.getMovProduto())) {
                        produtosPagos.put(movProdutoParcelaVO.getMovProduto(), Uteis.arredondarForcando2CasasDecimais(movProdutoParcelaVO.getValorPago() + produtosPagos.get(movProdutoParcelaVO.getMovProduto())));
                    } else {
                        produtosPagos.put(movProdutoParcelaVO.getMovProduto(), movProdutoParcelaVO.getValorPago());
                    }
                }
            }

            List<MovProdutoVO> movProdutosOriginal = new ArrayList<>();
            for (Integer codMovProduto : produtosPagos.keySet()) {
                MovProdutoVO movProdutoVO = movProdutoDao.consultarPorChavePrimaria(codMovProduto, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                movProdutosOriginal.add((MovProdutoVO) movProdutoVO.getClone(true));

                movProdutoVO.setMovProdutoParcelaVOs(new ArrayList<>());
                movProdutoVO.setQuitado(false);
                movProdutos.add(movProdutoVO);
            }
            try {
                if (!renegociacaoRedeEmpresa){
                    con.setAutoCommit(false);
                }

                if ((temParcelaDeCC) && (!renegociacaoRedeEmpresa)) {
                    List<String> pagamentosPesquisados = new ArrayList<>();
                    List<MovPagamentoVO> pagamentosDebitos = new ArrayList<>();

                    for (MovParcelaVO parcelaVO : listaMovParcelaRenegociar) {
                        String[] codigoPagamentos = parcelaVO.getMovPagamentoCC().split(",");
                        if (codigoPagamentos.length > 0) {
                            for (String codigoPagamento : codigoPagamentos) {
                                if (!pagamentosPesquisados.contains(codigoPagamento)) {
                                    pagamentosDebitos.add(movPagamentoDao.consultarPorChavePrimaria(Integer.parseInt(codigoPagamento), Uteis.NIVELMONTARDADOS_TODOS));
                                    pagamentosPesquisados.add(codigoPagamento);
                                }
                            }
                        }
                    }

                    List<MovPagamentoVO> pagamentoCreditos;

                    for (MovParcelaVO parcelaRenegociada : listaMovParcelaRenegociadas) {
                        pagamentoCreditos = new ArrayList<>();
                        StringBuilder movPagamentoCC = new StringBuilder();
                        double valorNecessario = parcelaRenegociada.getValorParcela();
                        for (MovPagamentoVO debito : pagamentosDebitos) {
                            if (valorNecessario <= debito.getValorTotal()) {

                                MovPagamentoVO debitoClone = (MovPagamentoVO) debito.getClone(true);
                                debitoClone.setReciboPagamento(new ReciboPagamentoVO());
                                debitoClone.setProdutosPagos("");
                                debitoClone.setProdutosPagosCancelados("");
                                debitoClone.setPagamentoMovParcelaVOs(new ArrayList<>());
                                debitoClone.setValor(valorNecessario);
                                debitoClone.setValorTotal(valorNecessario);
                                movPagamentoDao.incluirSemCommit(debitoClone);

                                pagamentoCreditos.add(debitoClone);
                                movPagamentoCC.append(debitoClone.getCodigo()).append(",");
                                break;
                            } else {
                                MovPagamentoVO debitoClone = (MovPagamentoVO) debito.getClone(true);
                                debitoClone.setReciboPagamento(new ReciboPagamentoVO());
                                debitoClone.setProdutosPagos("");
                                debitoClone.setProdutosPagosCancelados("");
                                debitoClone.setPagamentoMovParcelaVOs(new ArrayList<>());
                                movPagamentoDao.incluirSemCommit(debitoClone);

                                valorNecessario -= debitoClone.getValorTotal();

                                pagamentoCreditos.add(debitoClone);
                                movPagamentoCC.append(debitoClone.getCodigo()).append(",");
                            }
                        }
                        movPagamentoCC.deleteCharAt(movPagamentoCC.length() - 1);
                        parcelaRenegociada.setMovPagamentoCC(movPagamentoCC.toString());

                        MovimentoContaCorrenteCliente movimentoContaCorrenteClienteDAO = new MovimentoContaCorrenteCliente(this.con);
                        pagamentosDebitos = movimentoContaCorrenteClienteDAO.atualizarPagamentos(pagamentoCreditos, pagamentosDebitos, false);
                        movimentoContaCorrenteClienteDAO = null;
                    }

                } else {

                    if (tipoProdutoExtra.equals("TX")) {
                        if (movParcelaTaxa.getValorParcela() > 0.0) {
                            Produto produtoDAO = new Produto(con);
                            ProdutoVO taxaRenegociacao = produtoDAO.criarOuConsultarExisteProdutoPorTipo(TipoProduto.TAXA_RENEGOCIACAO.getDescricao(), TipoProduto.TAXA_RENEGOCIACAO.getCodigo(), 0.0);
                            produtoDAO = null;

                            MovProdutoVO modelo = movProdutos.get(0);

                            MovProdutoVO movProdutoVO = new MovProdutoVO();
                            movProdutoVO.setDescricao(taxaRenegociacao.getDescricao());
                            movProdutoVO.setProduto(taxaRenegociacao);
                            movProdutoVO.setTotalFinal(movParcelaTaxa.getValorParcela());
                            movProdutoVO.setPrecoUnitario(movParcelaTaxa.getValorParcela());
                            movProdutoVO.setSituacao("EA");
                            movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(Calendario.hoje()));
                            movProdutoVO.setAnoReferencia(Uteis.getAnoData(Calendario.hoje()));
                            movProdutoVO.setDataLancamento(Calendario.hoje());
                            movProdutoVO.setQuantidade(1);

                            movProdutoVO.setContrato(modelo.getContrato());
                            movProdutoVO.setEmpresa(modelo.getEmpresa());
                            movProdutoVO.setPessoa(modelo.getPessoa());

                            movProdutoVO.setResponsavelLancamento(usuarioVO);
                            movProdutoVO.setQuitado(true);
                            movProdutoDao.incluirSemCommit(movProdutoVO);
                            movProdutoVO.setQuitado(false);
                            movProdutos.add(movProdutoVO);
                        }
                    } else if (tipoProdutoExtra.equals("DE") && !mapaParcelas) {
                        if (movParcelaDesconto.getValorParcela() > 0.0) {
                            double valorTotalParcelas = 0.0;
                            for (MovParcelaVO movParcelaVO : listaMovParcelaRenegociar) {
                                if (!movParcelaVO.getDescricao().contains("DESCONTO")) {
                                    valorTotalParcelas += movParcelaVO.getValorParcela();
                                }
                            }

                            double valorTotalDesconto = movParcelaDesconto.getValorParcela();
                            double valorDescontoAplicarRestante = movParcelaDesconto.getValorParcela();

                            double porcentagemDesconto = (valorTotalDesconto * 100) / valorTotalParcelas;
                            Map<Integer, Double> mapaProdutoDescontoAplicar = new HashMap<Integer, Double>();
                            for (int i = 0; i < produtosParcelas.size(); i++) {
                                MovProdutoParcelaVO mppVO = produtosParcelas.get(i);

                                Integer codMovProduto = mppVO.getMovProduto();
                                Double valorDescontoAplicar = mapaProdutoDescontoAplicar.get(codMovProduto);
                                if (valorDescontoAplicar == null) {
                                    valorDescontoAplicar = 0.0;
                                }
                                double valorAplicar = mppVO.getValorPago() * (porcentagemDesconto / 100);
                                if ((i == (produtosParcelas.size() - 1)) //tenta for?ar o restante do desconto no ultimo produto
                                        || (valorAplicar < 0.01)  ) { //tenta for?ar o desconto de centavos para o primeiro produto
                                    if( Uteis.arredondarForcando2CasasDecimais(mppVO.getValorPago()) > Uteis.arredondarForcando2CasasDecimais(valorDescontoAplicarRestante)){
                                        valorAplicar = valorDescontoAplicarRestante;
                                    } else {
                                        valorAplicar = Uteis.arredondarForcando2CasasDecimais(mppVO.getValorPago());
                                    }
                                }
                                valorDescontoAplicar = Uteis.arredondarForcando2CasasDecimais(valorDescontoAplicar + valorAplicar);
                                valorDescontoAplicarRestante = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorDescontoAplicarRestante - valorAplicar);
                                

                                mapaProdutoDescontoAplicar.put(codMovProduto, valorDescontoAplicar);
                                if(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorDescontoAplicarRestante) <=  0.00){
                                    break;
                                }
                            }
                            HashMap<Integer, ContratoVO> contratosAlterados = new HashMap<Integer, ContratoVO>();
                            Contrato contratoDAO  = new Contrato(con);
                            for (int i = 0; i < movProdutos.size(); i++) {
                                MovProdutoVO movProduto = movProdutos.get(i);
                                MovProdutoVO movProdutoOriginal = movProdutosOriginal.get(i);
                                Double valorDescontoAplicar = mapaProdutoDescontoAplicar.get(movProduto.getCodigo());
                                if (valorDescontoAplicar == null) {
                                    valorDescontoAplicar = 0.0;
                                }
                                aplicarDesconto(movProduto, valorDescontoAplicar, atualizarValorFaturado);
                                atualizarMovProduto(movProdutoDao, movProdutoModalidade, movProduto, movProdutoOriginal);
                                if(atualizarValorFaturado && valorDescontoAplicar > 0.00 && UteisValidacao.notEmptyNumber(movProduto.getContrato().getCodigo())){
                                    if(!contratosAlterados.containsKey(movProduto.getContrato().getCodigo())){
                                        contratosAlterados.put(movProduto.getContrato().getCodigo(), contratoDAO.consultarPorChavePrimaria(movProduto.getContrato().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                                    }
                                    contratosAlterados.get(movProduto.getContrato().getCodigo()).setValorFinal(Uteis.arredondarForcando2CasasDecimais(contratosAlterados.get(movProduto.getContrato().getCodigo()).getValorFinal() - valorDescontoAplicar));
                                    contratosAlterados.get(movProduto.getContrato().getCodigo()).setValorBaseCalculo(Uteis.arredondarForcando2CasasDecimais(contratosAlterados.get(movProduto.getContrato().getCodigo()).getValorBaseCalculo() - valorDescontoAplicar));
                                }
                            }
                            contratoDAO = null;
                            if(atualizarValorFaturado){
                                for (ContratoVO contratoVO: contratosAlterados.values()) {
                                    SuperFacadeJDBC.executarUpdate("UPDATE contrato set valorfinal = "+ contratoVO.getValorFinal()+", valorbasecalculo = "+ contratoVO.getValorBaseCalculo()+ " where codigo = "+ contratoVO.getCodigo(), con);
                                }
                            }
                        }
                    } else if (mapaParcelas) {
                        for (MovProdutoVO movProduto : movProdutos) {
                            if (movProduto.getTotalFinal() >= 0) {
                                movProduto.setValorDesconto(Uteis.arredondarForcando2CasasDecimais(movProduto.getPrecoUnitario() - movProduto.getTotalFinal()));
                            }
                            movProdutoDao.alterarSemCommit(movProduto);
                        }
                    }
                }

                //Excluindo relacionamentos
                for (MovProdutoParcelaVO movProdutoParcelaVO : produtosParcelas) {
                    movProdutoParcelaDao.excluir(movProdutoParcelaVO);
                }

                for (MovProdutoVO movProdutoVO : movProdutos) {
                    movProdutoVO.setMovProdutoParcelaVOs(movProdutoParcelaDao.consultarPorCodigoMovProdutos(movProdutoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }

                for (MovParcelaVO movParcelaVO : listaMovParcelaRenegociadas) {
                    movParcelaVO.setMovProdutoParcelaVOs(new ArrayList());
                    zwFacade.dividirProdutosNasParcelas(movProdutos, movParcelaVO);
                }


                for (MovParcelaVO movParcelaVO : listaMovParcelaRenegociar) {
                    movParcelaVO.setSituacao("RG");
                    movParcelaDao.alterarSomenteSituacaoSemCommit(movParcelaVO);
                    clienteMensagemDao.excluirClienteMensagemPorMovParcela(movParcelaVO.getCodigo());
                }

                for (MovParcelaVO movParcelaVO : listaMovParcelaRenegociadas) {
                    movParcelaVO.setParcelasRenegociadas(jsonParcelas.toString());
                    movParcelaVO.setNrTentativas(0);
                    if (movParcelaVO.getValorParcela() <= 0.0) {
                        movParcelaVO.setSituacao("PG");
                    }

                    if ((movParcelaVO.getContrato().getCodigo() == 0 && movParcelaVO.getPersonal().getCodigo() != 0)
                         && (!renegociacaoRedeEmpresa)){
                        zwFacade.incluirMovParcelaSemValidar(movParcelaVO);
                    } else {
                        movParcelaDao.incluirParcelaSemValidar(movParcelaVO, true);
                        movProdutoParcelaDao.incluirMovProdutoParcelas(movParcelaVO.getCodigo(), movParcelaVO.getMovProdutoParcelaVOs());
                    }

                    if (movParcelaVO.getValorParcela() <= 0.0) {
                        movParcelaVO = movParcelaDao.consultarPorChavePrimaria(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        movParcelaVO.setMovProdutoParcelaVOs(movProdutoParcelaDao.consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                        if(movParcelaVO.getVendaAvulsaVO().getCodigo() != 0 && UteisValidacao.emptyString(movParcelaVO.getPessoa_Apresentar())){
                            movParcelaVO.setVendaAvulsaVO(vendaAvulsaDAO.consultarPorChavePrimaria(movParcelaVO.getVendaAvulsaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                            movPagamentoDao.gerarMovPagamento(movParcelaVO.getVendaAvulsaVO().getNomeComprador(), movParcelaVO.getPessoa(), movParcelaVO);
                        } else {
                            movPagamentoDao.gerarMovPagamento(movParcelaVO.getPessoa_Apresentar(), movParcelaVO.getPessoa(), movParcelaVO);
                        }
                    }
                }

                Integer codPessoa = 0;
                try {
                    codPessoa = listaMovParcelaRenegociar.get(0).getPessoa().getCodigo();
                    LogVO logVO = gerarlogRenegociacao(mapaParcelas, listaMovParcelaRenegociar,listaMovParcelaRenegociadas,movParcelaDesconto,movParcelaTaxa,porcentagemDescontoDesejado,usuarioVO, origemSistemaEnum);
                    if (logVO != null) {
                        logVO.setPessoa(codPessoa);
                        LogInterfaceFacade logFacade = new Log(con);
                        logFacade.incluirSemCommit(logVO);
                        logFacade = null;
                    }
                } catch (Exception e) {
                    SuperControle.registrarLogErroObjetoVO("RENEGOCIAÇÃO DE PARCELA", codPessoa, "ERRO AO GERAR LOG DE RENEGOCIAÇÃO DE PARCELA", usuarioVO.getNome(), usuarioVO.getUserOamd());
                    e.printStackTrace();
                }
                if (!renegociacaoRedeEmpresa){
                    con.commit();
//                    con.rollback();
                }

                if (!renegociacaoRedeEmpresa) {
                    removerNegativacoesSPC(usuarioVO, listaMovParcelaRenegociar);
                }

            } catch (Exception ex) {
                if (!renegociacaoRedeEmpresa){
                    con.rollback();
                }
                throw ex;
            } finally {
                if (!renegociacaoRedeEmpresa){
                    con.setAutoCommit(true);
                }
            }
        } finally {
            movProdutoParcelaDao = null;
            movProdutoDao = null;
            movPagamentoDao = null;
            clienteMensagemDao = null;
            movParcelaDao = null;
            zwFacade = null;
            vendaAvulsaDAO = null;
        }
    }

    public void removerNegativacoesSPC(UsuarioVO usuarioResponsavel, List<MovParcelaVO> listaParcela) {
        List<AcaoSPCCallable> callableTasks = new ArrayList<>();

        Map<Integer, EmpresaVO> empresasParcelas = new HashMap<>();

        try {
            String chave;
            if (JSFUtilities.isJSFContext()) {
                chave = (String) JSFUtilities.getFromSession(JSFUtilities.KEY);
            } else {
                chave = DAO.resolveKeyFromConnection(con);
            }
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(chave);

            Empresa empresaDAO = new Empresa();

            for (MovParcelaVO parcelaPaga : listaParcela) {

                if (parcelaPaga.isIncluidaSPC()) {

                    Integer codEmpresa = parcelaPaga.getEmpresa().getCodigo();
                    EmpresaVO empresaParcela = empresasParcelas.get(codEmpresa);
                    if (empresaParcela == null) {
                        empresaParcela = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        empresasParcelas.put(codEmpresa, empresaParcela);
                    }

                    if (empresaParcela == null
                            || UteisValidacao.emptyString(empresaParcela.getOperadorSpc())
                            || UteisValidacao.emptyString(empresaParcela.getSenhaSpc())
                            || UteisValidacao.emptyNumber(empresaParcela.getCodigoAssociadoSpc())) {
                        continue;
                    }

                    ParcelaSPCTO parcelaSPC = parcelaPaga.toParcelaSPCTO();

                    callableTasks.add(new AcaoSPCCallable()
                            .setIntegracoesMsUrl(clientDiscoveryDataDTO.getServiceUrls().getIntegracoesMsUrl())
                            .setParcela(parcelaSPC)
                            .setEmpresaVO(empresaParcela)
                            .setResponsavel(usuarioResponsavel)
                            .setMovparcelaDAO(new MovParcela(con))
                            .setLogDAO(new Log(con))
                            .setAcaoSPC(AcaoSPCCallable.EXCLUIR)
                            .setChave(chave));
                }
            }

            empresaDAO = null;

            final ExecutorService executorService = Executors.newFixedThreadPool(5);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void atualizarMovProduto(MovProduto movProdutoDao, MovProdutoModalidade movProdutoModalidadeDao, MovProdutoVO movProduto, MovProdutoVO movProdutoOriginal) throws Exception {
        if (movProdutoOriginal.getTotalFinal() > 0.0) {
            double proporcao = movProduto.getTotalFinal() / movProdutoOriginal.getTotalFinal();
            for (MovProdutoModalidadeVO mpmVO : movProduto.getMovProdutoModalidades()) {
                mpmVO.setValor(Uteis.arredondarForcando2CasasDecimais(mpmVO.getValor() * proporcao));
            }
        }

        movProdutoDao.alterarSemCommit(movProduto);
        for (MovProdutoModalidadeVO mpmVO : movProduto.getMovProdutoModalidades()) {
            movProdutoModalidadeDao.alterar(mpmVO);
        }
    }

    public LogVO gerarlogRenegociacao(boolean mapaRenegociacao,
                                      List<MovParcelaVO> listaMovParcelaRenegociar,
                                      List<MovParcelaVO> listaMovParcelaRenegociadas,
                                      MovParcelaVO movParcelaDesconto,
                                      MovParcelaVO movParcelaTaxa,
                                      double porcentagemDescontoDesejado,
                                      UsuarioVO usuarioVO,
                                      OrigemSistemaEnum origemSistemaEnum) throws Exception {
        LogVO obj = new LogVO();
        if (listaMovParcelaRenegociar.size() == 1) {
            obj.setChavePrimaria(listaMovParcelaRenegociar.get(0).getCodigo().toString());
        } else {
            obj.setChavePrimaria("0");
        }
        obj.setNomeEntidade("PARCELA");
        obj.setNomeEntidadeDescricao("parcela");
        obj.setOperacao("RENEGOCIAÇÃO - PARCELA");
        obj.setResponsavelAlteracao(usuarioVO.getNome());
        obj.setUserOAMD(usuarioVO.getUserOamd());
        obj.setNomeCampo("TODOS");
        obj.setOrigem(origemSistemaEnum != null ? origemSistemaEnum.getDescricao() : "");
        obj.setPessoa(listaMovParcelaRenegociar.get(0).getPessoa().getCodigo());

        Cliente clienteDAO = new Cliente(this.con);
        ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(listaMovParcelaRenegociar.get(0).getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
            obj.setCliente(clienteVO.getCodigo());
        }
        clienteDAO = null;

        Empresa empresaDAO = new Empresa(this.con);
        EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(listaMovParcelaRenegociar.get(0).getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
        empresaDAO = null;

        StringBuilder campoAlterado = new StringBuilder();
        campoAlterado.append("--------------------------------------\n\r");
        if (mapaRenegociacao) {
            campoAlterado.append("Foi aplicado um desconto de: ").append(Uteis.arredondarForcando2CasasDecimais(porcentagemDescontoDesejado)).append("%\n\r");
        } else {
            if (movParcelaTaxa != null && movParcelaTaxa.getValorParcela() > 0.0) {
                campoAlterado.append("Foi aplicada uma taxa no valor de: ").append(movParcelaTaxa.getValorParcela_Apresentar()).append("\n\r");
            }

            if (movParcelaDesconto != null && (movParcelaDesconto.getValorParcela() > 0.0)) {
                campoAlterado.append("Foi aplicado um desconto no valor de: ").append(movParcelaDesconto.getValorParcela_Apresentar()).append("\n\r");
            }
        }
        campoAlterado.append("--------------------------------------\n\r");
        campoAlterado.append("Parcelas renegociadas para: \n\r");

        String justificativa = "";
        for (MovParcelaVO parcelaRenegociada : listaMovParcelaRenegociadas) {
            campoAlterado.append("Código nova parcela: ").append(parcelaRenegociada.getCodigo()).append(System.getProperty("line.separator"));
            campoAlterado.append("Parcela no valor: ").append(parcelaRenegociada.getValorParcela_Apresentar()).append(" com vencimento em: ").append(parcelaRenegociada.getDataVencimento_Apresentar()).append("\n\r");
            if(UteisValidacao.emptyString(justificativa)){
                justificativa = parcelaRenegociada.getJustificativaParcelasRenegociadas();
            }
        }
        campoAlterado.append("\n\r").append("Justificativa: ").append(justificativa == null ? "" : justificativa).append("\n\r");

        campoAlterado.append("Responsável pelo Lançamento = ").append(usuarioVO.getNome()).append("\n\r");
        campoAlterado.append("Empresa = ").append(empresaVO.getNome()).append("\n\r");
        obj.setValorCampoAlterado(campoAlterado.toString());

        StringBuilder campoAnterior = new StringBuilder();
        campoAnterior.append("Parcelas que foram renegociadas: \n\r");

        HashMap<Integer, String> mapaMovProduto = new HashMap<Integer, String>();
        for (MovParcelaVO parcelaParaRenegociar : listaMovParcelaRenegociar) {
            if (!parcelaParaRenegociar.getDescricao().equals("DESCONTOS") && !parcelaParaRenegociar.getDescricao().equals("TAXA/JUROS")) {
                campoAnterior.append("Parcela= ")
                        .append(parcelaParaRenegociar.getCodigo())
                        .append(";Valor=")
                        .append(parcelaParaRenegociar.getValorParcela_Apresentar())
                        .append(";Data de vencimento=")
                        .append(parcelaParaRenegociar.getDataVencimento_Apresentar())
                        .append("\n\r");
                prepararMapaRenegociacao(mapaMovProduto, parcelaParaRenegociar);
            }
        }

        HashMap<Integer, String> mapaMovProdutoAtualizado = new HashMap<Integer, String>();
        for (MovParcelaVO parcelaRenegociada : listaMovParcelaRenegociadas) {
            if (!parcelaRenegociada.getDescricao().equals("DESCONTOS") && !parcelaRenegociada.getDescricao().equals("TAXA/JUROS")) {
                prepararMapaRenegociacao(mapaMovProdutoAtualizado, parcelaRenegociada);
            }
        }

        campoAnterior.append("Produtos que foram renegociados: \n\r");
        for (Integer codMovProduto : mapaMovProduto.keySet()) {
            campoAnterior.append("Produto= ").append(codMovProduto).append(";Valor=").append(mapaMovProduto.get(codMovProduto)).append(";Valor Novo=").append(mapaMovProdutoAtualizado.get(codMovProduto)).append("\n\r");
        }

        obj.setValorCampoAnterior(campoAnterior.toString());
        obj.setDataAlteracao(Calendario.hoje());

        return obj;
    }

    private void prepararMapaRenegociacao(HashMap<Integer, String> mapaMovProduto, MovParcelaVO parcela) {
        for (MovProdutoParcelaVO movProdutoParcelaVO : parcela.getMovProdutoParcelaVOs()) {
            String valorMovProdutoFinal = mapaMovProduto.get(movProdutoParcelaVO.getMovProdutoVO().getCodigo());
            if (valorMovProdutoFinal == null) {
                String totalFinal =  Formatador.formatarValorMonetario(movProdutoParcelaVO.getMovProdutoVO().getTotalFinal());
                mapaMovProduto.put(movProdutoParcelaVO.getMovProdutoVO().getCodigo(), totalFinal);
            }
        }
    }

    /**
     * Opera??o respons?vel por excluir no BD um objeto da classe
     * <code>MovParcelaVO</code>. Sempre localiza o registro a ser exclu?do
     * atrav?s da chave prim?ria da entidade. Primeiramente verifica a conex?o
     * com o banco de dados e a permiss?o do usu?rio para realizar esta operac?o
     * na entidade. Isto, atrav?s da opera??o
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovParcelaVO</code> que ser? removido
     * no banco de dados.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public void excluir(MovParcelaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Opera??o respons?vel por excluir no BD um objeto da classe
     * <code>MovParcelaVO</code>. Sempre localiza o registro a ser exclu?do
     * atrav?s da chave prim?ria da entidade. Primeiramente verifica a conex?o
     * com o banco de dados e a permiss?o do usu?rio para realizar esta operac?o
     * na entidade. Isto, atrav?s da opera??o
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovParcelaVO</code> que ser? removido
     * no banco de dados.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public void excluirSemCommit(MovParcelaVO obj) throws Exception {
        excluirSemCommit(obj.getCodigo());
    }

    public void excluirSemCommit(Integer codigo) throws Exception {
            excluir(getIdEntidade());
        String sql = "DELETE FROM MovParcela WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codigo);
            sqlExcluir.execute();
        }

        PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorContrato(Integer codigoContrato, int nivelMontarDados) throws Exception {
        return consultarPorContrato(codigoContrato, nivelMontarDados, "");
    }

    public List consultarPorContrato(Integer codigoContrato, int nivelMontarDados, String situacao) throws Exception {
        consultar(getIdEntidade(), true);
        String condicaoSituacao = "";
        if (!situacao.equals("")) {
            condicaoSituacao = " AND situacao = '" + situacao + "'";
        }
        String sqlStr = "SELECT MovParcela.* FROM MovParcela WHERE MovParcela.contrato =" + codigoContrato.intValue() + condicaoSituacao + " ORDER BY MovParcela.datavencimento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public MovParcelaVO consultarPorNumeroParcelaNoContrato(Integer numeroParcela, Integer codigoContrato, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT MovParcela.* FROM MovParcela WHERE MovParcela.descricao = 'PARCELA " + numeroParcela + "' AND MovParcela.contrato = " + codigoContrato;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados, con);
                } else {
                    return null;
                }
            }
        }
    }



    public MovParcelaVO consultarProximaEmAbertoPorContrato(Integer codigoContrato, int nivelMontarDados, Date dataVencimentoAPartir) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT MovParcela.* " +
                "FROM MovParcela " +
                "WHERE MovParcela.contrato =" + codigoContrato.intValue() +
                " AND situacao = 'EA'" +
                " AND datavencimento >= '" +Calendario.getData(dataVencimentoAPartir, "yyyy-MM-dd") +"' "+
                " ORDER BY MovParcela.datavencimento " +
                "LIMIT 1";

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                } else {
                    return null;
                }
            }
        }
    }

    public List<MovParcelaVO> consultarMensalidadesEmAbertoPorContrato(Integer codigoContrato, int nivelMontarDados, Date dataVencimentoAPartir) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT MovParcela.* " +
                "FROM MovParcela " +
                "INNER JOIN MovProdutoParcela ON MovProdutoParcela.movParcela = MovParcela.codigo " +
                "INNER JOIN MovProduto ON MovProduto.codigo = MovProdutoParcela.movProduto " +
                "INNER JOIN Produto ON Produto.codigo = MovProduto.produto " +
                "WHERE MovParcela.contrato = " + codigoContrato.intValue() +
                " AND MovParcela.situacao = 'EA' " +
                " AND Produto.tipoproduto = '"+TipoProduto.MES_REFERENCIA_PLANO.getCodigo()+"' " +
                " AND MovParcela.datavencimento >= '" +Calendario.getData(dataVencimentoAPartir, "yyyy-MM-dd") +"' "+
                "GROUP BY MovParcela.codigo "+
                "ORDER BY MovParcela.datavencimento ";

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }
    }

    public List<MovParcelaVO> consultarEmAbertoPorContrato(Integer codigoContrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "SELECT MovParcela.* " +
                "FROM MovParcela " +
                "WHERE MovParcela.contrato =" + codigoContrato.intValue() +
                " AND situacao = 'EA'" +
                " ORDER BY MovParcela.datavencimento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List consultarPorContratoNaoRenegociadaNegociada(Integer codigoContrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovParcela.* FROM MovParcela WHERE situacao <> 'RG' and MovParcela.contrato =" + codigoContrato.intValue() + " ORDER BY MovParcela.datavencimento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }


    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List<MovParcelaVO> consultarParcelaSomentePlanoMensalOrMatriculaOrRenovacaoOrRematricaPorContrato(Integer valorConsulta, boolean incluirParcelaAnuidade, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder tiposProdutoDesconsiderar = new StringBuilder();
        tiposProdutoDesconsiderar.append("'").append(TipoProduto.TRANCAMENTO.getCodigo()).append("'");
        tiposProdutoDesconsiderar.append(",'").append(TipoProduto.DEVOLUCAO.getCodigo()).append("'");
        tiposProdutoDesconsiderar.append(",'").append(TipoProduto.QUITACAO_DE_DINHEIRO.getCodigo()).append("'");
        tiposProdutoDesconsiderar.append(",'").append(TipoProduto.DEVOLUCAO_DE_RECEBIVEIS.getCodigo()).append("'");
        if (!incluirParcelaAnuidade){
            tiposProdutoDesconsiderar.append(",'").append(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo()).append("'");
        }
        String sqlStr = "select movparcela.descricao, movparcela.imprimirboletoparcela, movparcela.conveniocobranca, movparcela.utilizaconvenio, "
                + "movparcela.vendaavulsa, movparcela.aulaavulsadiaria, movparcela.percentualjuro, movparcela.percentualmulta, movparcela.movimentocc, movparcela.movpagamentocc, "
                + "movparcela.responsavel, movparcela.valorparcela, movparcela.contrato, movparcela.situacao, movparcela.datavencimento,"
                + "movparcela.dataregistro, movparcela.codigo, movparcela.personal,movparcela.empresa,movparcela.pessoa, movparcela.regimerecorrencia from movparcela "
                + "inner join movprodutoparcela on movprodutoparcela.movparcela =  movparcela.codigo "
                + "inner join movproduto on movprodutoparcela.movproduto = movproduto.codigo "
                + "inner join produto on movproduto.produto = produto.codigo and produto.tipoproduto not in( " + tiposProdutoDesconsiderar.toString() +  ") "
                + "where movproduto.contrato =" + valorConsulta
                + " group by  movparcela.descricao, movparcela.imprimirboletoparcela, movparcela.conveniocobranca, movparcela.utilizaconvenio, "
                + "movparcela.vendaavulsa, movparcela.aulaavulsadiaria, movparcela.percentualjuro, movparcela.percentualmulta, movparcela.movimentocc, movparcela.movpagamentocc, "
                + "movparcela.responsavel, movparcela.valorparcela, movparcela.contrato, movparcela.situacao, movparcela.datavencimento, "
                + "movparcela.dataregistro, movparcela.codigo, movparcela.personal,movparcela.empresa,movparcela.pessoa, movparcela.regimerecorrencia "
                + "ORDER BY MovParcela.datavencimento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorMovProduto(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT distinct on (MovParcela.codigo) MovParcela.* FROM MovParcela "
                + "inner join movprodutoparcela on movprodutoparcela.movparcela = MovParcela.codigo and movprodutoparcela.movproduto = " + valorConsulta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }


    public List consultarPorMovProduto(Integer valorConsulta, String situacao, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT distinct MovParcela.* FROM MovParcela ");
        sql.append("inner join movprodutoparcela on movprodutoparcela.movparcela = MovParcela.codigo and movprodutoparcela.movproduto = ");
        sql.append(valorConsulta);
        sql.append(" AND MovParcela.situacao = '").append(situacao).append("'");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }
    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorNomeCliente(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT MovParcela.codigo,MovParcela.responsavel,"
                    + "MovParcela.situacao, MovParcela.dataregistro,"
                    + "MovParcela.contrato,MovParcela.valorParcela, "
                    + "MovParcela.descricao, movparcela.personal, "
                    + "MovParcela.imprimirboletoparcela,movparcela.empresa,movparcela.pessoa, "
                    + "MovParcela.convenioCobranca,MovParcela.utilizaConvenio,"
                    + "MovParcela.percentualJuro,MovParcela.percentualMulta ,"
                    + "MovParcela.dataVencimento, MovParcela.vendaAvulsa, "
                    + "Movparcela.aulaAvulsaDiaria from MovParcela, "
                    + "Contrato, Pessoa ,Empresa "
                    + "where MovParcela.contrato = Contrato.codigo "
                    + "and Contrato.pessoa =  Pessoa.codigo  "
                    + "and upper( Pessoa.nome ) like(?)  "
                    + "group by  MovParcela.codigo,MovParcela.responsavel,MovParcela.situacao, "
                    + "MovParcela.dataregistro,MovParcela.contrato,MovParcela.valorParcela, "
                    + "MovParcela.descricao, MovParcela.imprimirboletoparcela, movparcela.personal, "
                    + "MovParcela.convenioCobranca,MovParcela.utilizaConvenio,"
                    + "MovParcela.percentualJuro,MovParcela.percentualMulta,movparcela.empresa,movparcela.pessoa, "
                    + "MovParcela.dataVencimento, MovParcela.vendaAvulsa,"
                    + "Movparcela.aulaAvulsaDiaria  ORDER BY MovParcela.contrato ";
        } else {
            sqlStr = "SELECT MovParcela.codigo,MovParcela.responsavel,"
                    + "MovParcela.situacao, MovParcela.dataregistro,"
                    + "MovParcela.contrato,MovParcela.valorParcela, "
                    + "MovParcela.descricao, movparcela.personal,"
                    + "MovParcela.imprimirboletoparcela,movparcela.empresa,movparcela.pessoa, "
                    + "MovParcela.convenioCobranca,MovParcela.utilizaConvenio,"
                    + "MovParcela.percentualJuro,MovParcela.percentualMulta ,"
                    + "MovParcela.dataVencimento, MovParcela.vendaAvulsa, "
                    + "Movparcela.aulaAvulsaDiaria  "
                    + "from MovParcela , Contrato, Pessoa ,Empresa "
                    + "where MovParcela.contrato = Contrato.codigo "
                    + "and Contrato.pessoa =  Pessoa.codigo and contrato.empresa = ? "
                    + " and upper( Pessoa.nome ) like(?)  "
                    + "group by  MovParcela.codigo,MovParcela.responsavel,"
                    + "MovParcela.situacao, MovParcela.dataregistro,"
                    + "MovParcela.contrato,MovParcela.valorParcela,"
                    + "MovParcela.descricao, movparcela.personal,"
                    + "MovParcela.imprimirboletoparcela,movparcela.empresa,movparcela.pessoa, "
                    + "MovParcela.convenioCobranca,MovParcela.utilizaConvenio,"
                    + "MovParcela.percentualJuro,MovParcela.percentualMulta ,"
                    + "MovParcela.dataVencimento, MovParcela.vendaAvulsa, "
                    + "Movparcela.aulaAvulsaDiaria  ORDER BY MovParcela.contrato ";
        }

        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            int i = 1;
            if (!UteisValidacao.emptyNumber(empresa)) {
                stm.setInt(i++, empresa);
            }
            stm.setString(i, valorConsulta.toUpperCase() + "%");

            try (ResultSet tabelaResultado = stm.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List<MovParcelaVO> consultarPorCodigoCliente(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovParcela.codigo,MovParcela.responsavel,"
                + "MovParcela.situacao, MovParcela.dataregistro,"
                + "MovParcela.contrato,MovParcela.valorParcela, "
                + "MovParcela.descricao,MovParcela.imprimirboletoparcela, "
                + "MovParcela.convenioCobranca,MovParcela.utilizaConvenio,"
                + "MovParcela.percentualJuro,MovParcela.percentualMulta,"
                + "MovParcela.dataVencimento,MovParcela.vendaAvulsa,movparcela.personal,"
                + "Movparcela.aulaAvulsaDiaria,movparcela.empresa,movparcela.pessoa, movparcela.regimerecorrencia , movparcela.parcelaDCC from MovParcela, "
                + "Contrato, Pessoa ,Empresa "
                + "where MovParcela.contrato = Contrato.codigo "
                + "and Contrato.pessoa =  Pessoa.codigo  "
                + "and  Pessoa.codigo =" + valorConsulta + " "
                + "group by  MovParcela.codigo,MovParcela.responsavel,"
                + "MovParcela.situacao, MovParcela.dataregistro,movparcela.empresa,movparcela.pessoa,"
                + "MovParcela.contrato,MovParcela.valorParcela, movparcela.personal, "
                + "MovParcela.descricao,MovParcela.imprimirboletoparcela, "
                + "MovParcela.convenioCobranca,MovParcela.utilizaConvenio,"
                + "MovParcela.percentualJuro,MovParcela.percentualMulta ,"
                + "MovParcela.dataVencimento, MovParcela.vendaAvulsa, "
                + "MovParcela.aulaAvulsaDiaria, MovParcela.regimerecorrencia , MovParcela.parcelaDCC "
                + " ORDER BY MovParcela.dataVencimento desc, MovParcela.codigo ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    private String getSqlMovParcelaEmConvenio(List<Integer> convenios, String colunaPessoa){
        return " AND EXISTS (SELECT 1 FROM cliente c2 INNER JOIN autorizacaocobrancacliente cli ON cli.cliente = c2.codigo WHERE cli.conveniocobranca IN (" + Uteis.montarListaIN(convenios) + ") AND c2.pessoa = "+colunaPessoa+") ";
    }

    public ResultSet contarPendenciaParcelaEmAbertoAPagar(int empresa,Date dataBase, final String colaboradores,Boolean regimeRecorrencia,Boolean parcelaDCC, Boolean contarCliente, List<Integer> convenios) throws Exception {
        return contarPendenciaParcelaEmAbertoAPagar(empresa, null, dataBase, colaboradores, regimeRecorrencia, parcelaDCC, contarCliente, convenios);
    }

    public ResultSet contarPendenciaParcelaEmAbertoAPagar(int empresa,Date dataBaseInicio, Date dataBase, final String colaboradores,Boolean regimeRecorrencia,Boolean parcelaDCC, Boolean contarCliente, List<Integer> convenios) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder sqlStr = new StringBuilder();
        if(contarCliente){
            sqlStr.append("SELECT COUNT(distinct(movparcela.pessoa)) AS qtd, ");
        }else {
            sqlStr.append("SELECT COUNT(distinct(movparcela.codigo)) AS qtd, ");
        }
        sqlStr.append("SUM(movparcela.valorparcela) AS total FROM movparcela ");
        sqlStr.append("JOIN (SELECT distinct movparcela.codigo FROM movparcela ");
        sqlStr.append(" INNER JOIN pessoa pes ON pes.codigo = movparcela.pessoa ");
        sqlStr.append(" INNER JOIN cliente ON cliente.pessoa = movparcela.pessoa XX ");
        sqlStr.append(" Left Join contratocondicaopagamento ccp on ccp.contrato = movparcela.contrato\n");
        sqlStr.append(" Left join condicaopagamento cp on cp.codigo = ccp.condicaopagamento\n");
        sqlStr.append(" left join vendaavulsa venda on venda.codigo = movparcela.vendaavulsa \n");
        sqlStr.append("WHERE movparcela.situacao = 'EA' ");
        if (dataBaseInicio != null) {
            sqlStr.append(" AND pes.datacadastro >= '" + Uteis.getDataJDBC(dataBaseInicio) + " 00:00:00' ");
        }
        if( regimeRecorrencia != null && parcelaDCC != null  && regimeRecorrencia  && parcelaDCC){
            sqlStr.append("AND (movparcela.regimerecorrencia or movparcela.parcelaDCC or coalesce(cp.tipoconveniocobranca, 0) >  0)");
        } else {
            if(regimeRecorrencia !=null) {
                if (regimeRecorrencia) {
                    sqlStr.append("AND movparcela.regimerecorrencia ");
                } else {
                    sqlStr.append("AND NOT movparcela.regimerecorrencia ");
                }
            }
            if(parcelaDCC !=null) {
                if (parcelaDCC) {
                    sqlStr.append("AND (movparcela.parcelaDCC or coalesce(cp.tipoconveniocobranca, 0) > 0 )\n");
                } else {
                    sqlStr.append("AND NOT movparcela.parcelaDCC\n");
                    sqlStr.append("and (coalesce(cp.tipoconveniocobranca, 0) = 0 )\n");
                }
            }
            sqlStr.append(" and (movparcela.contrato is not null or movparcela.aulaavulsadiaria is not null or  venda.tipocomprador <> 'CO') ");
        }
        sqlStr.append("AND movparcela.datavencimento::DATE >= '").append(Uteis.getData(Calendario.hoje())).append("' ");
        if(empresa != 0){
            sqlStr.append("AND movparcela.empresa = ? ");
        }
        if(convenios != null && !convenios.isEmpty()){
            sqlStr.append(" ");
            sqlStr.append(getSqlMovParcelaEmConvenio(convenios, "movparcela.pessoa"));
        }

        // filtra a consulta pelos colaboradores
        if(!UteisValidacao.emptyString(colaboradores)){
            sqlStr = new StringBuilder(sqlStr.toString().replaceAll("XX", "INNER JOIN vinculo ON vinculo.cliente = cliente.codigo "));
            sqlStr.append(" AND (" + colaboradores+ ")");
        } else {
            sqlStr = new StringBuilder(sqlStr.toString().replaceAll("XX", ""));
        }
        sqlStr.append(") as movAux ON movAux.codigo = movparcela.codigo");

        PreparedStatement sql = con.prepareStatement(sqlStr.toString());
        if(empresa != 0){
            sql.setInt(1, empresa);
        }
        return sql.executeQuery();
    }

    public ResultSet contarPendenciaParcelaEmAbertoAtraso(int empresa, Date data, final String colaboradores) throws Exception {
        return contarPendenciaParcelaEmAbertoAtraso(empresa, null, data, colaboradores);
    }

    public ResultSet contarPendenciaParcelaEmAbertoAtraso(int empresa, Date dataInicial, Date data, final String colaboradores) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT COUNT(1) AS qtd, SUM(valorparcela) AS total FROM (");
        sqlStr.append("SELECT pessoa , contrato, Sum(valorparcela) AS valorparcela FROM (\n");
        sqlStr.append("SELECT distinct movparcela.codigo, movparcela.pessoa, movparcela.valorparcela, ct.codigo contrato FROM movparcela \n");
        sqlStr.append("INNER JOIN Situacaoclientesinteticodw dw on dw.codigopessoa = movparcela.pessoa\n X\n");
        sqlStr.append("LEFT JOIN Contrato ct ON  ct.codigo = movparcela.contrato\n");
        sqlStr.append("LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo\n");
        sqlStr.append("Left Join contratocondicaopagamento ccp on ccp.contrato = ct.codigo\n");
        sqlStr.append("Left join condicaopagamento cp on cp.codigo = ccp.condicaopagamento\n");
        sqlStr.append(" left join vendaavulsa venda on venda.codigo = movparcela.vendaavulsa \n");
        sqlStr.append("WHERE movparcela.situacao = 'EA' AND NOT movparcela.regimerecorrencia\n");
        sqlStr.append("AND NOT movparcela.parcelaDCC\n");
        sqlStr.append("and (coalesce(cp.tipoconveniocobranca, 0) = 0 )\n");
        sqlStr.append("and (movparcela.contrato is not null or movparcela.aulaavulsadiaria is not null or  venda.tipocomprador <> 'CO') \n");
        sqlStr.append("AND movparcela.datavencimento < ? \n");

        if(empresa != 0){
            sqlStr.append("AND movparcela.empresa = ? \n");
        }

        // filtra a consulta pelos colaboradores
        if(!UteisValidacao.emptyString(colaboradores)){
            String i =  sqlStr.toString();
            i = i.replaceAll("X", "INNER JOIN vinculo ON vinculo.cliente = dw.codigocliente ");
            sqlStr = new StringBuilder(i);
            sqlStr.append(" AND (" + colaboradores+ ")");
        } else {
            String i = sqlStr.toString().replaceAll("X", "");
            sqlStr = new StringBuilder(i);
        }

        if (dataInicial != null) {//Data limite inicial != null
            sqlStr.append(" AND EXISTS(SELECT pes.codigo FROM pessoa pes WHERE pes.codigo = movparcela.pessoa AND pes.datacadastro >= '" + Uteis.getDataJDBC(dataInicial) + " 00:00:00') \n");
        }
        sqlStr.append(") as consulta group by pessoa , contrato) T");
        PreparedStatement sql = con.prepareStatement(sqlStr.toString());
        sql.setDate(1, new java.sql.Date(data.getTime()));
        if(empresa != 0){
            sql.setInt(2, empresa);
        }
        return sql.executeQuery();
    }

    public ResultSet consultarPendenciaParcelaEmAbertoAPagar(int empresa, final String colaboradores,ConfPaginacao paginacao,Date dataBaseInicio) throws Exception {
        int qtde = 0;
        consultar(getIdEntidade(), false);
        StringBuilder strSql = new StringBuilder();
        strSql.append(" SELECT * FROM  (");
        strSql.append("SELECT dw.codigocliente AS cli, dw.codigopessoa,sum(movparcela.valorparcela) as valorEmAberto,\n");
        strSql.append("dw.nomecliente as nome,pl.descricao as nomePlano,dw.codigopessoa as codPessoa,dw.matricula as matriculaCli,dw.situacao as situacaoCliente,movparcela.contrato as codContrato,\n");
        strSql.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente,dw.cpf\n");
        strSql.append(" FROM movparcela\n");
        strSql.append(" INNER JOIN Situacaoclientesinteticodw dw on dw.codigopessoa = movparcela.pessoa X\n");
        strSql.append(" LEFT JOIN Contrato ct ON  ct.codigo = movparcela.contrato\n");
        strSql.append( "LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo\n");
        strSql.append(" Left Join contratocondicaopagamento ccp on ccp.contrato = movparcela.contrato\n");
        strSql.append(" Left join condicaopagamento cp on cp.codigo = ccp.condicaopagamento\n");
        strSql.append( "left join vendaavulsa venda on venda.codigo = movparcela.vendaavulsa\n");
        strSql.append( "LEFT JOIN Plano pl ON pl.codigo = ct.plano\n");
        strSql.append("WHERE movparcela.situacao = 'EA' ");
        strSql.append("AND NOT movparcela.regimerecorrencia ");
        strSql.append("AND NOT movparcela.parcelaDCC ");
        strSql.append(" and coalesce(cp.tipoconveniocobranca, 0) = 0 ");
        strSql.append(" and (movparcela.contrato is not null or movparcela.aulaavulsadiaria is not null or  venda.tipocomprador <> 'CO') ");

        if(empresa != 0){
            strSql.append("AND movparcela.empresa = ? ");
        }

        strSql.append("AND movparcela.datavencimento::DATE >= '").append(Uteis.getData(Calendario.hoje())).append("' ");
        // filtra a consulta pelos colaboradores
        if(!UteisValidacao.emptyString(colaboradores)){
            String i = strSql.toString().replaceAll("X", "INNER JOIN vinculo ON vinculo.cliente = dw.codigocliente ");
            strSql = new StringBuilder(i);
            strSql.append(" AND (" + colaboradores+ ")");
        } else {
            String i =  strSql.toString().replaceAll("X", "");
            strSql = new StringBuilder(i);
        }

        strSql.append(" GROUP BY pl.descricao,cli,matriculaCli,dw.nomePlano,codPessoa, codContrato,situacaoCliente, nome,dataInicio,dataFim,duracaoContrato,ct.nomemodalidades,dw.telefonescliente,dw.cpf");
        strSql.append(") consulta ");
        if(paginacao != null && paginacao.getOrdernar()){
            strSql.append(" ORDER BY ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
        } else {
            strSql.append(" ORDER BY nome, codcontrato");
        }
        if(paginacao != null && paginacao.isExistePaginacao()){
            strSql.append(" LIMIT ").append(paginacao.getItensPorPagina()).append(" OFFSET ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        PreparedStatement sql = con.prepareStatement(strSql.toString());
        if(empresa != 0){
            sql.setInt(1, empresa);
        }
        return sql.executeQuery();
    }

    public ResultSet consultarPendenciaParcelaEmAbertoAtraso(int empresa, Date data, final String colaboradores,ConfPaginacao paginacao,Date dataBaseInicio,Date dataBase) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder strSql = new StringBuilder();
        strSql.append(" SELECT cli, codigopessoa, nome, nomePlano, codPessoa, matriculaCli, situacaoCliente, codContrato, dataInicio, \n");
        strSql.append(" dataFim, duracaoContrato, nomemodalidades, telefonescliente, cpf,count(1) AS qtdParcelas,sum(valorparcela) valorEmAberto FROM  (\n");
        strSql.append("SELECT dw.codigocliente AS cli, dw.codigopessoa,movparcela.valorparcela,\n");
        strSql.append("dw.nomecliente as nome ,pl.descricao as nomePlano,dw.codigopessoa as codPessoa, dw.matricula as matriculaCli,dw.situacao as situacaoCliente,movparcela.contrato as codContrato,\n");
        strSql.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente,dw.cpf, movparcela.codigo\n");
        strSql.append(" FROM movparcela\n");
        strSql.append(" INNER JOIN Situacaoclientesinteticodw dw on dw.codigopessoa = movparcela.pessoa X\n");
        strSql.append(" LEFT JOIN Contrato ct ON  ct.codigo = movparcela.contrato\n");
        strSql.append( "LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo\n");
        strSql.append("Left Join contratocondicaopagamento ccp on ccp.contrato = ct.codigo\n");
        strSql.append("Left join condicaopagamento cp on cp.codigo = ccp.condicaopagamento\n");
        strSql.append( "LEFT JOIN Plano pl ON pl.codigo = ct.plano\n");
        strSql.append( "left join vendaavulsa venda on venda.codigo = movparcela.vendaavulsa\n");
        strSql.append("WHERE movparcela.situacao = 'EA' ");
        strSql.append("AND NOT movparcela.regimerecorrencia ");
        strSql.append("AND NOT movparcela.parcelaDCC ");
        strSql.append("and (coalesce(cp.tipoconveniocobranca, 0) = 0 )\n");
        strSql.append(" and (movparcela.contrato is not null or movparcela.aulaavulsadiaria is not null or  venda.tipocomprador <> 'CO') ");
        strSql.append("AND movparcela.datavencimento < ? ");

        if(empresa != 0){
            strSql.append("AND movparcela.empresa = ?\n");
        }

        // filtra a consulta pelos colaboradores
        if(!UteisValidacao.emptyString(colaboradores)){
            String i = strSql.toString().replaceAll("X", " INNER JOIN vinculo ON vinculo.cliente = dw.codigocliente ");
            strSql = new StringBuilder(i);
            strSql.append(" AND (" + colaboradores+ ")");
        } else {
            String i =  strSql.toString().replaceAll("X", "");
            strSql = new StringBuilder(i);
        }
        if (dataBaseInicio != null) {
            StringBuilder subSQL = new StringBuilder();
            subSQL.append(" AND EXISTS(");
            subSQL.append("SELECT cli.codigo FROM cliente cli ");
            subSQL.append("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ");
            subSQL.append("WHERE cli.codigo = dw.codigocliente AND movparcela.datavencimento >= '" + Uteis.getData(dataBaseInicio) + " 00:00:00'");
            subSQL.append(") ");
            strSql.append(subSQL.toString());
        }
        strSql.append(" GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16\n");
        strSql.append(") consulta GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14");
        if(paginacao != null && paginacao.getOrdernar()){
            strSql.append(" ORDER BY ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
        } else {
            strSql.append(" ORDER BY 3,8");
        }
        if(paginacao != null && paginacao.isExistePaginacao()){
            strSql.append(" LIMIT ").append(paginacao.getItensPorPagina()).append(" OFFSET ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        PreparedStatement sql = con.prepareStatement(strSql.toString());
        sql.setDate(1, new java.sql.Date(dataBase.getTime()));
        if(empresa != 0){
            sql.setInt(2, empresa);
        }
        return sql.executeQuery();
    }

    /**
     * M?todo que conta a quantidade de consultores com parcelas em aberto
     *
     * @param empresa int
     * @throws Exception
     */
    public ResultSet contarPendenciaParcelaEmAbertoAPagarColaborador(int empresa, final String colaboradores) throws Exception {
        return contarPendenciaParcelaEmAbertoAPagarColaborador(empresa, colaboradores, null);
    }
    public ResultSet contarPendenciaParcelaEmAbertoAPagarColaborador(int empresa, final String colaboradores, Date dataBaseInicio) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "SELECT COUNT(DISTINCT(c.codigo)) AS qtd, SUM(movparcela.valorparcela) AS total FROM movparcela " +
                "INNER JOIN colaborador AS c ON c.pessoa = movparcela.pessoa  "
                + " left join vendaavulsa venda on venda.codigo = movparcela.vendaavulsa " +
                "WHERE movparcela.situacao = 'EA' AND NOT movparcela.regimerecorrencia " +
                "AND NOT movparcela.parcelaDCC and ((movparcela.contrato is null and movparcela.aulaavulsadiaria is null)  or venda.tipocomprador = 'CO') ";

        if(empresa != 0){
            sqlStr += " AND c.empresa = ? ";
        }

        // filtra a consulta pelos colaboradores
        if(!UteisValidacao.emptyString(colaboradores)){
            sqlStr += " AND (" + colaboradores.replaceAll("vinculo.colaborador", "c.codigo")+ ")";
        }
        if (dataBaseInicio != null) {//Data limite inicial != null
            sqlStr += " AND EXISTS(SELECT pes.codigo FROM pessoa pes WHERE pes.codigo = movparcela.pessoa AND pes.datacadastro >= '" + Uteis.getDataJDBC(dataBaseInicio) + " 00:00:00') \n";
        }
        PreparedStatement sql = con.prepareStatement(sqlStr);
        if(empresa != 0){
            sql.setInt(1, empresa);
        }
        return sql.executeQuery();
    }

    /**
     * M?todo que retorna colaboradores que possuem parcelas a pagar incluindo
     * pagamentos a serem efetuados de personais
     *
     * @param empresa int
     * @throws Exception
     */
    public ResultSet consultarPendenciaParcelaEmAbertoAPagarColaborador(int empresa, final String colaboradores,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception {
        int qtde = 0;
        consultar(getIdEntidade(), false);
        StringBuilder strSql = new StringBuilder();
        strSql.append(" SELECT * FROM  (");
        strSql.append("SELECT DISTINCT col.codigo AS codigoColaborador, col.pessoa,\n");
        strSql.append("p.nome as nome,col.situacao, p.cfp AS cpf\n");
        strSql.append(" FROM movparcela\n");
        strSql.append(" INNER JOIN Colaborador col ON col.pessoa = movparcela.pessoa\n");
        strSql.append( "LEFT JOIN Pessoa p ON col.pessoa = p.codigo\n");
        strSql.append( " left join vendaavulsa venda on venda.codigo = movparcela.vendaavulsa \n");
        strSql.append("WHERE movparcela.situacao = 'EA' ");
        strSql.append("AND NOT movparcela.regimerecorrencia ");
        strSql.append("and ((movparcela.contrato is null and movparcela.aulaavulsadiaria is null) or venda.tipocomprador = 'CO') ");
        if(empresa != 0){
            strSql.append("AND col.empresa = ? ");
        }
        if(!UteisValidacao.emptyString(colaboradores)){
            strSql.append(" AND (").append(colaboradores.replaceAll("vinculo.colaborador", "col.codigo")).append(")");
        }
        strSql.append(") consulta ");
        if(paginacao != null && paginacao.getOrdernar()){
            strSql.append(" ORDER BY ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
        }
        if(paginacao != null && paginacao.isExistePaginacao()){
            strSql.append(" LIMIT ").append(paginacao.getItensPorPagina()).append(" OFFSET ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        PreparedStatement sql = con.prepareStatement(strSql.toString());
        if(empresa != 0){
            sql.setInt(1, empresa);
        }
        return sql.executeQuery();
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List<MovParcelaVO> consultarPorCodigoPessoa(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovParcela.* from MovParcela "
                + "left join contrato on movparcela.contrato = contrato.codigo "
                + "left join aulaavulsadiaria on movparcela.aulaavulsadiaria = aulaavulsadiaria.codigo "
                + "left join vendaavulsa on movparcela.vendaavulsa = vendaavulsa.codigo "                
                + "WHERE  movparcela.pessoa = " + valorConsulta
                + " ORDER BY MovParcela.dataRegistro desc, MovParcela.codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>codigo</code> da classe
     * <code>Pessoa</code> Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de preparar o
     * List resultante.
     *
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarParcelasColaboradorPorCodigoPessoa(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovParcela.* from MovParcela "
                + "left join contrato on movparcela.contrato = contrato.codigo "
                + "left join aulaavulsadiaria on movparcela.aulaavulsadiaria = aulaavulsadiaria.codigo "
                + "left join vendaavulsa on movparcela.vendaavulsa = vendaavulsa.codigo "
                + "left join controletaxapersonal on movparcela.personal = controletaxapersonal.codigo "
                + "left join colaborador c ON  (vendaavulsa.colaborador = c.codigo) OR "
                + "(contrato.pessoa = c.pessoa) OR (c.codigo = controletaxapersonal.personal) "
                + "where movparcela.pessoa =" + valorConsulta
                + " ORDER BY MovParcela.dataRegistro desc, MovParcela.codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Consulta movimento de parcelas de acordo com o nome do consumidor
     *
     * @param nomeComprador
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public List consultarPorNomeConsumidor(String nomeComprador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = " select movparcela.* from MovParcela "
                + "left join aulaavulsadiaria on movparcela.aulaavulsadiaria = aulaavulsadiaria.codigo "
                + "left join vendaavulsa on movparcela.vendaavulsa = vendaavulsa.codigo "
                + "where vendaavulsa.tipocomprador = 'CN' and vendaavulsa.nomecomprador ilike '" + nomeComprador + "' "
                + "ORDER BY MovParcela.dataRegistro desc, MovParcela.codigo desc ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public MovParcelaVO consultarPorMovProduto(int movproduto, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "select movparcela.* from movproduto inner join produto on movproduto.produto = produto.codigo "
                + "inner join movprodutoparcela on movproduto.codigo = movprodutoparcela.movproduto "
                + "inner join movparcela on movprodutoparcela.movparcela = movparcela.codigo "
                + "where  (Produto.tipoProduto <> 'DE' or Produto.tipoProduto <> 'DV' or Produto.tipoProduto <> 'QU' ) "
                + "and movproduto.codigo= " + movproduto + ";";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new MovParcelaVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }

    }

    public List<MovParcelaVO> consultarPorListMovProduto(Set<Integer> movprodutos, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        List<MovParcelaVO> listaRetorno = new ArrayList<MovParcelaVO>();
        String sqlStr = "select movparcela.* from movproduto inner join produto on movproduto.produto = produto.codigo "
                + "inner join movprodutoparcela on movproduto.codigo = movprodutoparcela.movproduto "
                + "inner join movparcela on movprodutoparcela.movparcela = movparcela.codigo "
                + "where  (Produto.tipoProduto <> 'DE' or Produto.tipoProduto <> 'DV' or Produto.tipoProduto <> 'QU' ) "
                + "and movproduto.codigo in (" + StringUtils.join(movprodutos, ",") + ");";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (tabelaResultado.next()) {
                    listaRetorno.add(montarDados(tabelaResultado, nivelMontarDados, con));
                }
            }
        }
        return listaRetorno;

    }



    public List consultarPorCodigoPessoaContrato(Integer valorConsulta, int contrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovParcela.* from MovParcela "
                + "LEFT JOIN contrato ON movparcela.contrato = contrato.codigo "
                + "LEFT JOIN aulaavulsadiaria ON movparcela.aulaavulsadiaria = aulaavulsadiaria.codigo "
                + "LEFT JOIN vendaavulsa ON movparcela.vendaavulsa = vendaavulsa.codigo "
                + "WHERE movparcela.pessoa = " + valorConsulta + " and "
                + "movparcela.contrato = " + contrato + " "
                + "ORDER BY MovParcela.dataRegistro desc, MovParcela.codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovParcela.* FROM MovParcela, Colaborador WHERE MovParcela.responsavel = Colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>String situacao</code>. Retorna os objetos, com in?cio do valor do
     * atributo id?ntico ao par?metro fornecido. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplica??o dever? verificar se o
     * usu?rio possui permiss?o para esta consulta ou n?o.
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorSituacao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        if (valorConsulta.equalsIgnoreCase("em aberto") || valorConsulta.equalsIgnoreCase("e")) {
            valorConsulta = "EA";
        }
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM MovParcela WHERE upper( situacao ) like('" + valorConsulta.toUpperCase().substring(0, 2) + "%') ORDER BY situacao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores
     * pertecentes ao per?odo informado por par?metro. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplica??o dever? verificar se o
     * usu?rio possui permiss?o para esta consulta ou n?o.
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovParcela WHERE ((dataRegistro >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataRegistro <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataRegistro";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores
     * pertecentes ao per?odo informado por par?metro. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplica??o dever? verificar se o
     * usu?rio possui permiss?o para esta consulta ou n?o.
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorDataRegistroVendaAvulsa(String valorConsultar, Integer empresa, Date prmIni, Date prmTerm, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovParcela, VendaAvulsa WHERE movparcela.vendaAvulsa = vendaAvulsa.codigo and (movparcela.datavencimento >= '"
                + Uteis.getDataJDBC(prmIni) + " 00:00:00' ) and (movparcela.datavencimento <='"
                + Uteis.getDataJDBC(prmTerm) + " 23:59:59' ) "
                + "and vendaAvulsa >= 0 and situacao='EA'  and vendaAvulsa.empresa="
                + empresa.intValue() + " and upper( vendaAvulsa.nomeComprador ) like(?) ORDER BY vendaAvulsa.nomeComprador";


        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            stm.setString(1, valorConsultar.toUpperCase() + "%");
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores
     * pertecentes ao per?odo informado por par?metro. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplica??o dever? verificar se o
     * usu?rio possui permiss?o para esta consulta ou n?o.
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorVendaAvulsa(String valorConsultar, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM MovParcela, VendaAvulsa WHERE movparcela.vendaAvulsa = vendaAvulsa.codigo ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append("AND vendaAvulsa.empresa = ? ");
        }
        sqlStr.append("AND vendaAvulsa >= 0  AND situacao LIKE 'EA' AND upper( vendaAvulsa.nomeComprador ) LIKE (?)  ORDER BY vendaAvulsa.nomeComprador ");

        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString())) {
            int i = 1;
            if (!UteisValidacao.emptyNumber(empresa)) {
                stm.setInt(i++, empresa);
            }
            stm.setString(i, valorConsultar.toUpperCase() + "%");
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores
     * pertecentes ao per?odo informado por par?metro. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplica??o dever? verificar se o
     * usu?rio possui permiss?o para esta consulta ou n?o.
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public MovParcelaVO consultarPorCodigoVendaAvulsa(Integer codigoVenda,
            String situacao, boolean controlarAcesso, int nivelMontarDados)
            throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovParcela WHERE vendaAvulsa =" + codigoVenda;
        if (!situacao.equals("")) {
            sqlStr += "  and situacao='" + situacao + "'";
        }
        sqlStr += "   ORDER BY movparcela.dataRegistro";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados N?o Encontrados ( MovParcela ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<MovParcelaVO> consultarPorCodigoVendaAvulsaLista(Integer codigoVenda, Date dataInicioVencimento,
                                                                 Date dataVencimento, String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM MovParcela WHERE vendaAvulsa =").append(codigoVenda);
        if (!UteisValidacao.emptyString(situacao)) {
            sqlStr.append("  and situacao='").append(situacao).append("'");
        }
        if (dataInicioVencimento != null) {
            sqlStr.append("AND datavencimento >= '").append(Uteis.getDataJDBC(dataInicioVencimento)).append("'\n");
        }
        if (dataVencimento != null) {
            sqlStr.append("AND datavencimento <= '").append(Uteis.getDataJDBC(dataVencimento)).append("'\n");
        }

        sqlStr.append("   ORDER BY movparcela.datavencimento");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores
     * pertecentes ao per?odo informado por par?metro. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplica??o dever? verificar se o
     * usu?rio possui permiss?o para esta consulta ou n?o.
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorDataRegistroAulaAvulsaDiaria(String valorConsultar, Integer empresa, Date prmIni, Date prmTerm, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovParcela, AulaAvulsaDiaria WHERE movparcela.AulaAvulsaDiaria = AulaAvulsaDiaria.codigo and (movparcela.datavencimento >= '" + Uteis.getDataJDBC(prmIni) + " 00:00:00') and (movparcela.datavencimento <= '" + Uteis.getDataJDBC(prmTerm) + " 23:59:59')  " + "and AulaAvulsaDiaria >= 0 and situacao='EA' and AulaAvulsaDiaria.empresa = " + empresa.intValue() + "   and upper( AulaAvulsaDiaria.nomeComprador ) like('" + valorConsultar.toUpperCase() + "%') ORDER BY AulaAvulsaDiaria.nomeComprador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores
     * pertecentes ao per?odo informado por par?metro. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplica??o dever? verificar se o
     * usu?rio possui permiss?o para esta consulta ou n?o.
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorAulaAvulsaDiaria(String valorConsultar, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr;
        if (empresa == 0) {
            sqlStr = "SELECT * FROM MovParcela, AulaAvulsaDiaria WHERE movparcela.AulaAvulsaDiaria = AulaAvulsaDiaria.codigo and AulaAvulsaDiaria >= 0  and situacao='EA' and upper( AulaAvulsaDiaria.nomeComprador ) like('" + valorConsultar.toUpperCase() + "%')   ORDER BY AulaAvulsaDiaria.nomeComprador";
        } else {
            sqlStr = "SELECT * FROM MovParcela, AulaAvulsaDiaria WHERE movparcela.AulaAvulsaDiaria = AulaAvulsaDiaria.codigo and AulaAvulsaDiaria >= 0  and situacao='EA' and AulaAvulsaDiaria.empresa = " + empresa.intValue() + " and upper( AulaAvulsaDiaria.nomeComprador ) like('" + valorConsultar.toUpperCase() + "%')   ORDER BY AulaAvulsaDiaria.nomeComprador";
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores
     * pertecentes ao per?odo informado por par?metro. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplica??o dever? verificar se o
     * usu?rio possui permiss?o para esta consulta ou n?o.
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public MovParcelaVO consultarPorCodigoAulaAvulsaDiaria(Integer codigoVenda, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovParcela WHERE AulaAvulsaDiaria =" + codigoVenda + "  and situacao='EA'   ORDER BY movparcela.dataRegistro";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados N?o Encontrados ( MovParcela ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao par?metro fornecido. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplica??o dever? verificar se o
     * usu?rio possui permiss?o para esta consulta ou n?o.
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovParcela WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Consulta por descri??o de movparcela
     *
     * @param valorConsulta
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public boolean consultarPorDescricao(String valorConsulta, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM MovParcela WHERE descricao ilike '" + valorConsulta + "'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean existeParcelaEmAbertaNoMesAnterior(Integer codigoPessoa) throws Exception{
        Calendar dataLimite = Calendario.getInstance();
        dataLimite.set(Calendar.DAY_OF_MONTH, 1);
        dataLimite.set(Calendar.MONTH, dataLimite.get(Calendar.MONTH) -1);
        dataLimite.set(Calendar.DAY_OF_MONTH, dataLimite.getActualMaximum(Calendar.DAY_OF_MONTH));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        StringBuilder sql = new StringBuilder();
        sql.append("select par.* from movparcela par  \n");
        sql.append("inner join movprodutoparcela mpp on mpp.movparcela = par.codigo   \n");
        sql.append("inner join movproduto movpro on movpro.codigo = mpp.movproduto \n");
        sql.append("inner join produto pro on pro.codigo = movpro.produto \n");
        sql.append("inner join categoriaproduto cat on cat.codigo = pro.categoriaproduto \n");
        sql.append("where par.situacao = 'EA'  \n");
        sql.append("and par.datavencimento <= '").append(sdf.format(dataLimite.getTime())).append("' \n");
        sql.append("and par.pessoa = ").append(codigoPessoa).append(" \n");
        sql.append("and cat.bloquearacessoseprodutoaberto  \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (tabelaResultado.next());
            }
        }
    }
    public boolean existeParcelaVencidaSegundoConfiguracoes(
            Integer codigoPessoa, EmpresaVO empresa,
            ConfiguracaoSistemaVO configuracaoSistema,
            int nivelMontarDados) throws Exception {
        return existeParcelaVencidaSegundoConfiguracoes(codigoPessoa, 0, empresa, configuracaoSistema, nivelMontarDados, 0 );
    }

    public boolean existeParcelaVencidaSegundoConfiguracoes(
            Integer codigoPessoa, Integer codigoContrato, EmpresaVO empresa,
            ConfiguracaoSistemaVO configuracaoSistema,
            int nivelMontarDados, Integer codigoControlePersonal) throws Exception {

        /* Toler?ncia de dias do pagamento de uma parcela vencida
         * Possui prioridade a entidade mais especializada, por exemplo:
         * A toler?ncia da Empresa prevalece sobre a toler?ncia da Configura??o do Sistema,
         * ignorando os valores, sejam maiores ou menores.
         * */

        int toleranciaEmDiasVencimentoEmpresa = empresa.getToleranciaPagamento().intValue();
        int toleranciaEmDiasVencimentoConfiguracaoGlobal = configuracaoSistema.getToleranciaPagamento();
        int toleranciaEscolhida = toleranciaEmDiasVencimentoConfiguracaoGlobal;

        if (toleranciaEmDiasVencimentoEmpresa != 0) {
            toleranciaEscolhida = toleranciaEmDiasVencimentoEmpresa;
        } else {
            toleranciaEscolhida = toleranciaEmDiasVencimentoConfiguracaoGlobal;
        }

        Date dataBase = Uteis.somarDias(negocio.comuns.utilitarias.Calendario.hoje(), -toleranciaEscolhida);
        String dataVencimento = Uteis.getDataAplicandoFormatacao(dataBase,
                "yyyy-MM-dd");


        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT \n");
        sqlStr.append("par.*\n");
        sqlStr.append("from Movparcela par \n");
        sqlStr.append("where (par.dataVencimento < '").append(dataVencimento).append("')\n");
        sqlStr.append("and (par.situacao = 'EA')\n");
        if (!UteisValidacao.emptyNumber(codigoContrato)){
            sqlStr.append("and (par.contrato = ").append(codigoContrato).append(" ) ");
        } else if (!UteisValidacao.emptyNumber(codigoControlePersonal)){
            sqlStr.append("and (par.personal = ").append(codigoControlePersonal).append(" ) ");
        } else {
            sqlStr.append("and (par.contrato > 0 )\n");
            sqlStr.append("and (par.pessoa = ").append(codigoPessoa).append(" ) ");
        }
        sqlStr.append("and (not exists(select ri.codigo from remessaitem ri\n");
        sqlStr.append("       inner join remessa re on re.codigo = ri.remessa\n");
        sqlStr.append("       left join remessaitemmovparcela rim on rim.remessaitem = ri.codigo\n");
        sqlStr.append("       where (ri.movparcela = par.codigo or rim.movparcela = par.codigo)\n");
        sqlStr.append("       and re.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId());
        sqlStr.append(") and re.tipo not in (").append(TipoRemessaEnum.ITAU_BOLETO.getId()).append(",").append(TipoRemessaEnum.APROVA_FACIL.getId()).append(",").append(TipoRemessaEnum.BOLETO.getId()).append("))\n");
        sqlStr.append("or par.nrtentativas > ").append(empresa.getTentativasLiberarParcelaVencida()).append(")\n");
        sqlStr.append("order by par.datavencimento\n");
        sqlStr.append("limit 1");

        if (SuperFacadeJDBC.existe(sqlStr.toString(), con)) {
            return true;
        }
        if (!UteisValidacao.emptyNumber(codigoControlePersonal)){
            return false;
        }
        sqlStr = new StringBuilder();
        sqlStr.append("select par.* from movparcela par \n");
        sqlStr.append("inner join movprodutoparcela mpp on mpp.movparcela = par.codigo  \n");
        sqlStr.append("inner join movproduto movpro on movpro.codigo = mpp.movproduto\n");
        sqlStr.append("inner join produto pro on pro.codigo = movpro.produto\n");
        sqlStr.append("inner join categoriaproduto cat on cat.codigo = pro.categoriaproduto\n");
        sqlStr.append("where par.situacao = 'EA' \n");
        sqlStr.append("and cat.bloquearacessoseprodutoaberto \n");
        sqlStr.append("and par.datavencimento < '").append(dataVencimento).append("' \n");
        if (!UteisValidacao.emptyNumber(codigoContrato)) {
            sqlStr.append("and par.contrato = ").append(codigoContrato).append(" \n");
        } else {
            sqlStr.append("and par.pessoa = ").append(codigoPessoa).append(" \n");
        }
        sqlStr.append("and (not exists(select ri.codigo \n");
        sqlStr.append("	from remessaitem ri \n");
        sqlStr.append("	inner join remessa re on re.codigo = ri.remessa \n");
        sqlStr.append("   left join remessaitemmovparcela rim on rim.remessaitem = ri.codigo\n");
        sqlStr.append("   where (ri.movparcela = par.codigo or rim.movparcela = par.codigo)\n");
        sqlStr.append("   and re.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append("))\n");
        sqlStr.append("or par.nrtentativas > ").append(empresa.getTentativasLiberarParcelaVencida()).append(")\n");
        sqlStr.append("order by datavencimento\n");
        sqlStr.append("offset 0\n");
        sqlStr.append("limit 1\n");
        return SuperFacadeJDBC.existe(sqlStr.toString(), con);

    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovPagamento</code> atrav?s do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao par?metro fornecido. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplica??o dever? verificar se o
     * usu?rio possui permiss?o para esta consulta ou n?o.
     * @return List Contendo v?rios objetos da classe
     * <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public List consultarPorCodigoRecibo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = " SELECT m2.nome as modalidade, mp.*" +
                " from movParcela mp" +
                " left join aulaavulsadiaria a2 ON a2.codigo = mp.aulaavulsadiaria" +
                " left join modalidade m2 on m2.codigo = a2.modalidade" +
                " where mp.codigo in( " +
                " SELECT Distinct (pagamentomovparcela.movParcela) from  pagamentomovparcela " +
                " where pagamentomovparcela.recibopagamento =  " + valorConsulta.intValue() +
                " order by pagamentomovparcela.movParcela) order by codigo ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Opera??o respons?vel por excluir todos os objetos da
     * <code>HistoricoContratoVO</code> no BD. Faz uso da opera??o
     * <code>excluir</code> dispon?vel na classe
     * <code>HistoricoContrato</code>.
     *
     * @param contrato campo chave para exclus?o dos objetos no BD.
     * @exception Exception Erro de conex?o com o BD ou restri??o de acesso a
     * esta opera??o.
     */
    public void excluirMovParcelaContratos(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM MovParcela WHERE (contrato = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, contrato);
            sqlExcluir.execute();
        }

        PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);
    }

    public void excluirMovParcelaVendaAvulsa(Integer vendaAvulsa) throws Exception {
        String sql = "DELETE FROM MovParcela WHERE vendaavulsa = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, vendaAvulsa);
            sqlExcluir.execute();
        }

        PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);
    }

    /**
     * Opera??o respons?vel por alterar todos os objetos da
     * <code>HistoricoContratoVO</code> contidos em um Hashtable no BD. Faz uso
     * da opera??o
     * <code>excluirHistoricoContratos</code> e
     * <code>incluirHistoricoContratos</code> dispon?veis na classe
     * <code>HistoricoContrato</code>.
     *
     * @param objetos List com os objetos a serem alterados ou inclu?dos no BD.
     * @exception Exception Erro de conex?o com o BD ou restri??o de acesso a
     * esta opera??o.
     */
    public void alterarMovParcelaContratos(ContratoVO contrato, List objetos) throws Exception {
        excluirMovParcelaContratos(contrato.getCodigo());
        incluirMovParcelaContratos(contrato, objetos);
    }

    /**
     * Opera??o respons?vel por incluir objetos da
     * <code>HistoricoContratoVO</code> no BD. Garantindo o relacionamento com a
     * entidade principal
     * <code>contrato.Contrato</code> atrav?s do atributo de v?nculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception Erro de conex?o com o BD ou restri??o de acesso a
     * esta opera??o.
     */
    public void incluirMovParcelaContratos(ContratoVO contratoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            MovParcelaVO obj = (MovParcelaVO) e.next();
            obj.setContrato(contratoPrm);
            incluirParcelaSemCommit(obj);
        }
    }

    /**
     * Respons?vel por montar os dados de v?rios objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da opera??o
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     */
    public static List<MovParcelaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<MovParcelaVO> vetResultado = new ArrayList<MovParcelaVO>();
        while (tabelaResultado.next()) {
            MovParcelaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static List<MovParcelaVO> montarDadosConsultaResumido(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        //long start = System.currentTimeMillis();
        List<MovParcelaVO> vetResultado = new ArrayList<MovParcelaVO>();
        while (tabelaResultado.next()) {
            MovParcelaVO obj = montarDadosResumido(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        con.close();
        //System.out.println(System.currentTimeMillis() - start);
        return vetResultado;
    }

    public static MovParcelaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        MovParcelaVO obj = new MovParcelaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDataRegistro(dadosSQL.getDate("dataRegistro"));
        obj.setDataVencimento(dadosSQL.getDate("dataVencimento"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.getContrato().setCodigo(dadosSQL.getInt("contrato"));
        obj.getResponsavel().setCodigo(dadosSQL.getInt("responsavel"));
        obj.setPercentualMulta(dadosSQL.getDouble("percentualMulta"));
        obj.setPercentualJuro(dadosSQL.getDouble("percentualJuro"));
        obj.setValorParcela(Uteis.arredondarForcando2CasasDecimais(dadosSQL.getDouble("valorParcela")));
        obj.setUtilizaConvenio(dadosSQL.getBoolean("utilizaConvenio"));
        obj.getConvenioCobranca().setCodigo(dadosSQL.getInt("convenioCobranca"));
        obj.getVendaAvulsaVO().setCodigo(dadosSQL.getInt("vendaAvulsa"));
        obj.getAulaAvulsaDiariaVO().setCodigo(dadosSQL.getInt("aulaAvulsaDiaria"));
        obj.setImprimirBoletoParcela(dadosSQL.getBoolean("imprimirBoletoParcela"));
        obj.setPersonal(new ControleTaxaPersonalVO(dadosSQL.getInt("personal")));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
        //algumas consultas nomeadas campo a campo n?o citam estas colunas, portando
        //apenas deve le-las se existirem no ResultSet
        try {
            obj.setModalidade(dadosSQL.getString("modalidade"));
        } catch (Exception ignored) {
        }
        try{
            obj.setParcelaDCC(dadosSQL.getBoolean("parcelaDCC"));
        }catch (Exception ignored){}
        try {
            obj.setRegimeRecorrencia(dadosSQL.getBoolean("regimerecorrencia"));
        } catch (Exception ignored) {}
        try {
            obj.setNumeroParcelasOperadora(dadosSQL.getInt("numeroParcelasOperadora"));
        } catch (Exception ignored) {}
        try {
            obj.setMovimentoCC(dadosSQL.getBoolean("movimentoCC"));
            obj.setMovPagamentoCC(dadosSQL.getString("movpagamentocc"));
            dadosSQL.findColumn("dataAlteracaoManual");
            obj.setDataAlteracaoManual(dadosSQL.getDate("dataAlteracaoManual"));
            obj.setNrTentativas(dadosSQL.getInt("nrtentativas"));
            obj.setDataPagamento(dadosSQL.getDate("datapagamento"));
        } catch (Exception ignored) {
        }

        try{
            obj.setParcelasRenegociadas(dadosSQL.getString("parcelasRenegociadas"));
            obj.setPlanoPersonal(dadosSQL.getInt("planoPersonal"));
        }catch (Exception e){}

        try {
            obj.setDataCobranca(dadosSQL.getDate("datacobranca"));
        } catch (Exception e) {
            obj.setDataCobranca(obj.getDataVencimento());
        }
        try {
            obj.setIncluidaSPC(dadosSQL.getBoolean("incluidaSPC"));
            obj.setSituacaoSPC(dadosSQL.getString("situacaoSPC"));
            obj.setJsonSPC(dadosSQL.getString("jsonSpc"));
        } catch (Exception ignored) {}
        try {
            obj.setIdExterno(dadosSQL.getInt("idExterno"));
        } catch (Exception ignored) {}
        try {
            obj.setGerandoBoletoPeloGestaoBoletosOnline(dadosSQL.getBoolean("gerandoBoletoPeloGestaoBoletosOnline"));
        } catch (Exception ignored) {}

        return obj;
    }
    public static List<MovParcelaTransacaoTO> montarDadosConsultaResumido(ResultSet tabelaResultado, Connection con) throws Exception {
        //long start = System.currentTimeMillis();
        List<MovParcelaTransacaoTO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            MovParcelaTransacaoTO obj = montarDadosBasicos(tabelaResultado);
            vetResultado.add(obj);
        }
        con.close();
        //System.out.println(System.currentTimeMillis() - start);
        return vetResultado;
    }

    public static MovParcelaTransacaoTO montarDadosBasicos(ResultSet dadosSQL) throws Exception {
        MovParcelaTransacaoTO obj = new MovParcelaTransacaoTO();
        obj.setNovoObj(false);
        obj.setNome(dadosSQL.getString("nomePessoa"));
        obj.setEmpresaCodigo(dadosSQL.getInt("codigoEmpresa"));
        obj.setContrato(dadosSQL.getInt("contrato"));
        obj.setParcelaCodigo(dadosSQL.getInt("parcelaCodigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setDataVencimentoParcela(dadosSQL.getDate("datavencimento"));
        obj.setValorParcela(Uteis.arredondarForcando2CasasDecimais(dadosSQL.getDouble("valorParcela")));
        obj.setNomeEmpresa(dadosSQL.getString("nome"));
        obj.setCodigoCliente(dadosSQL.getInt("codigoCliente"));
        obj.setNomeConvenioCobranca(dadosSQL.getString("conveniocobranca"));
        obj.setEmRemessa(dadosSQL.getBoolean("estaRemessaAguardando") ? "Sim": "Não");
        obj.setNomeConsultor(dadosSQL.getString("nomeConsultor"));

        return obj;
    }

    /**
     * Respons?vel por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>MovParcelaVO</code>.
     *
     * @return O objeto da classe <code>MovParcelaVO</code> com os dados
     * devidamente montados.
     */
    public static MovParcelaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_REGISTRAR_JUSTIFICATIVA) {
            MovParcelaVO obj = new MovParcelaVO();
            obj.setSituacao(dadosSQL.getString("situacao"));
            return obj;
        }

        MovParcelaVO obj = montarDadosBasico(dadosSQL);

        try{
            obj.setNomePLano(dadosSQL.getString("nomeplano"));
        }catch (Exception ignore){}

        try{
            obj.getContrato().getPlano().setDescricao(dadosSQL.getString("nomeplano"));
            obj.getContrato().getPlano().setParcelamentoOperadora(dadosSQL.getBoolean("parcelamentooperadora"));
            obj.getContrato().getPlano().setMaximoVezesParcelar(dadosSQL.getInt("maximovezesparcelar"));
        }catch(Exception e){}

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        if (obj.getPessoa().getCodigo() > 0) {
            Pessoa pessoa = new Pessoa(con);
            obj.setPessoa(pessoa.consultarPorChavePrimaria(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            pessoa = null;
        }
        if (obj.getEmpresa().getCodigo() > 0) {
            Empresa empresa = new Empresa(con);
            obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            empresa = null;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
//          montarDadosVendaAvulsa(obj, Uteis.NIVELMONTARDADOS_MINIMOS);
//          montarDadosAulaAvulsaDiaria(obj, Uteis.NIVELMONTARDADOS_MINIMOS);
//          montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_MINIMOS);
//          montarDadosPersonal(obj, Uteis.NIVELMONTARDADOS_MINIMOS);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_RECIBOPAGAMENTO) {
            MovParcelaDetalhamento movParcelaDetalhamento = new MovParcelaDetalhamento(con);
            obj.setMovParcelaDetalhamentoVO(movParcelaDetalhamento.consultarPorMovParcela(obj.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            movParcelaDetalhamento = null;
            if (obj.getEmpresa().isMostrarDescricaoParcelaRenegociada() && (obj.getParcelasRenegociadas() != null && !obj.getParcelasRenegociadas().equals("") )) {
                ParcelasRenegociadasJSON parcelasRenegociadasJSON = new ParcelasRenegociadasJSON();
                obj.setParcelasRenegociadasJSONS(parcelasRenegociadasJSON.fromJson(obj));
            }
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_PARCELA) {
            montarDadosVendaAvulsa(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            montarDadosAulaAvulsaDiaria(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_ROBO, con);
            montarDadosPersonal(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_GESTAOREMESSA) {
            montarDadosItemRemessa(obj, nivelMontarDados, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_GESTAOREMESSABOLETO) {
            montarDadosItemRemessa(obj, nivelMontarDados, con);
            return obj;
        }
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS){
            MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);
            obj.setMovProdutoParcelaVOs(movProdutoParcelaDAO.consultarPorCodigoMovParcela(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            movProdutoParcelaDAO = null;
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosVendaAvulsa(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        montarDadosPersonal(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        montarDadosAulaAvulsaDiaria(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosResponsavel(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    public static MovParcelaVO montarDadosResumido(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MovParcelaVO obj = montarDadosBasico(dadosSQL);

        if (obj.getPessoa().getCodigo() > 0) {
            Pessoa pessoa = new Pessoa(con);
            obj.setPessoa(pessoa.consultarPorChavePrimaria(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            pessoa = null;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_GESTAOREMESSA) {
            montarDadosItemRemessa(obj, nivelMontarDados, true, con);
            return obj;
        }

        return obj;
    }

    /**
     * Opera??o respons?vel por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>MovParcelaVO</code>. Faz uso da chave prim?ria da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual ser? montado os dados consultados.
     */
    public static void montarDadosVendaAvulsa(MovParcelaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getVendaAvulsaVO().getCodigo() == 0) {
            obj.setVendaAvulsaVO(new VendaAvulsaVO());
            return;
        }
        VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
        obj.setVendaAvulsaVO(vendaAvulsaDAO.consultarPorChavePrimaria(obj.getVendaAvulsaVO().getCodigo(), nivelMontarDados));
        vendaAvulsaDAO = null;
    }

    public static void montarDadosPersonal(MovParcelaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPersonal().getCodigo() == 0) {
            obj.setPersonal(new ControleTaxaPersonalVO(0));
        } else {
            ControleTaxaPersonal controleTaxaPersonalDAO = new ControleTaxaPersonal(con);
            obj.setPersonal(controleTaxaPersonalDAO.consultarPorChavePrimaria(obj.getPersonal().getCodigo(), nivelMontarDados));
            controleTaxaPersonalDAO = null;
        }
    }

    /**
     * Opera??o respons?vel por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>MovParcelaVO</code>. Faz uso da chave prim?ria da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual ser? montado os dados consultados.
     */
    public static void montarDadosAulaAvulsaDiaria(MovParcelaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getAulaAvulsaDiariaVO().getCodigo() == 0) {
            obj.setAulaAvulsaDiariaVO(new AulaAvulsaDiariaVO());
            return;
        }
        AulaAvulsaDiaria aulaAvulsaDiariaDAO = new AulaAvulsaDiaria(con);
        obj.setAulaAvulsaDiariaVO(aulaAvulsaDiariaDAO.consultarPorChavePrimaria(obj.getAulaAvulsaDiariaVO().getCodigo(), nivelMontarDados));
        aulaAvulsaDiariaDAO = null;
    }

    /**
     * Opera??o respons?vel por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>MovParcelaVO</code>. Faz uso da chave prim?ria da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual ser? montado os dados consultados.
     */
    public static void montarDadosContrato(MovParcelaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getContrato().getCodigo() == 0) {
            obj.setContrato(new ContratoVO());
            return;
        }
        Contrato contratoDAO = new Contrato(con);
        obj.setContrato(contratoDAO.consultarPorChavePrimaria(obj.getContrato().getCodigo(), nivelMontarDados));
        contratoDAO = null;
    }

    public static void montarDadosItemRemessa(MovParcelaVO obj, int nivelMontarDados, boolean parcelaVencida, Connection con) throws Exception {
        RemessaItem remessaItemDAO = new RemessaItem(con);
        obj.setItemRemessa(remessaItemDAO.obterUltimoItemRemessaPorCodigoParcela(obj.getCodigo(), nivelMontarDados, parcelaVencida));
        remessaItemDAO = null;
    }

    public static void montarDadosItemRemessa(MovParcelaVO obj, int nivelMontarDados, Connection con) throws Exception {
        montarDadosItemRemessa(obj, nivelMontarDados, false, con);
    }

    /**
     * Opera??o respons?vel por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>MovParcelaVO</code>. Faz uso da chave prim?ria da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual ser? montado os dados consultados.
     */
    public static void montarDadosResponsavel(MovParcelaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavel().getCodigo() == 0) {
            obj.setResponsavel(new UsuarioVO());
            return;
        }
        Usuario usuarioDAO = new Usuario(con);
        obj.setResponsavel(usuarioDAO.consultarPorChavePrimaria(obj.getResponsavel().getCodigo(), nivelMontarDados));
        usuarioDAO = null;
    }

    public List consultarParcelas(Integer contrato, Date dataVencimento,  int nivelMontarDados) throws Exception {
        return consultarParcelas(contrato, null, dataVencimento, nivelMontarDados);
    }

    /**
     * Opera??o respons?vel por consultar todos os
     * <code>ContratoModalidadeVO</code> relacionados a um objeto da classe
     * <code>contrato.Contrato</code>.
     *
     * @param contrato Atributo de <code>contrato.Contrato</code> a ser
     * utilizado para localizar os objetos da classe
     * <code>ContratoModalidadeVO</code>.
     * @return List Contendo todos os objetos da classe
     * <code>ContratoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Erro de conex?o com o BD ou restri??o de acesso a
     * esta opera??o.
     */
    public List<MovParcelaVO> consultarParcelas(Integer contrato, Date dataInicioVencimento, Date dataVencimento,  int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<MovParcelaVO> objetos = new ArrayList<MovParcelaVO>();
        StringBuilder sql = new StringBuilder("SELECT mp.*,c.pagarComBoleto as pagarComBoleto, mp.empresa FROM movParcela mp ");
        sql.append("inner join contrato c on c.codigo = mp.contrato ");
        sql.append("WHERE mp.contrato = ? and mp.vendaAvulsa isnull ");
        sql.append("and mp.situacao = 'EA'\n");
        if (dataInicioVencimento != null) {
            sql.append("AND mp.datavencimento >= ? \n");
        }
        if (dataVencimento != null) {
            sql.append("AND mp.datavencimento <= ? \n");
        }
        sql.append("ORDER BY mp.datavencimento, mp.codigo");
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql.toString())) {
            int i = 0;
            sqlConsulta.setInt(++i, contrato);
            if (dataInicioVencimento != null) {
                sqlConsulta.setDate(++i, Uteis.getDataJDBC(dataInicioVencimento));
            }
            if (dataVencimento != null) {
                sqlConsulta.setDate(++i, Uteis.getDataJDBC(dataVencimento));
            }
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    MovParcelaVO novoObj = MovParcela.montarDados(resultado, nivelMontarDados, con);
                    novoObj.getContrato().setPagarComBoleto(resultado.getBoolean("pagarComBoleto"));
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Respons?vel por consultar a parcela mais vencida de um contrato
     *
     * <AUTHOR> 08/08/2011
     */
    public MovParcelaVO consultarProximaParcelaContrato(Integer contrato, Integer nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM movParcela WHERE contrato = ? ");
        sql.append("AND vendaAvulsa IS NULL AND situacao = 'EA' ");
        sql.append("ORDER BY datavencimento, codigo ");
        sql.append("LIMIT 1");
        Declaracao dc = new Declaracao(sql.toString(), con);
        dc.setInt(1, contrato);
        try (ResultSet resultSet = dc.executeQuery()) {
            if (resultSet.next()) {
                return montarDados(resultSet, nivelMontarDados, con);
            } else {
                return null;
            }
        }

    }

    /**
     * Opera??o respons?vel por adicionar um objeto da
     * <code>MovProdutoParcelaVO</code> no Hashtable
     * <code>MovProdutoParcelas</code>. Neste Hashtable s?o mantidos todos os
     * objetos de MovProdutoParcela de uma determinada MovProduto.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjMovProdutoParcelas(MovProdutoParcelaVO obj) throws Exception {
        getMovProdutoParcelas().put(obj.getMovProduto() + "", obj);
        // adicionarObjSubordinadoOC
    }

    /**
     * Opera??o respons?vel por remover um objeto da classe
     * <code>MovProdutoParcelaVO</code> do Hashtable
     * <code>MovProdutoParcelas</code>. Neste Hashtable s?o mantidos todos os
     * objetos de MovProdutoParcela de uma determinada MovProduto.
     *
     * @param MovProduto Atributo da classe <code>MovProdutoParcelaVO</code>
     * utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjMovProdutoParcelas(Integer MovProduto) throws Exception {
        getMovProdutoParcelas().remove(MovProduto + "");
        // excluirObjSubordinadoOC
    }

    /**
     * Opera??o respons?vel por localizar um objeto da classe
     * <code>MovParcelaVO</code> atrav?s de sua chave prim?ria.
     *
     * @exception Exception Caso haja problemas de conex?o ou localiza??o do
     * objeto procurado.
     */
    public MovParcelaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM MovParcela WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( MovParcela: " + codigoPrm + " ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public MovParcelaVO consultarPorCodigo(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM MovParcela WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, nivelMontarDados, con));
                }
            }
        }
        return null;
    }

    public MovParcelaVO consultarPorIdExterno(Long idExterno, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM MovParcela WHERE idexterno = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setLong(1, idExterno);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, nivelMontarDados, con));
                }
            }
        }
        return null;
    }


    public Hashtable getMovProdutoParcelas() {
        return (movProdutoParcelas);
    }

    public void setMovProdutoParcelas(Hashtable movProdutoParcelas) {
        this.movProdutoParcelas = movProdutoParcelas;
    }

    /**
     * Opera??o respons?vel por localizar um objeto da classe
     * <code>MovParcelaVO</code> atrav?s do c?digo de um contrato de negocia??o
     * de evento. Opera??o do Central de Eventos.
     *
     * <AUTHOR>
     * @exception Exception Caso haja problemas de conex?o ou localiza??o do
     * objeto procurado.
     */
    public List consultarPorContratoEvento(Integer codigoPrm, int nivelMontarDados) throws Exception {
        if (codigoPrm == null) {
            return new ArrayList();
        }
        consultar(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct MP.*, \n");
        sql.append(" (select max(datapagamento) from movpagamento \n");
        sql.append(" left join pagamentomovparcela pmp ON pmp.movpagamento = movpagamento.codigo \n");
        sql.append(" where pmp.movparcela = mp.codigo) as datapagamento \n");
        sql.append(" from movparcela MP  \n");
        sql.append(" inner join negociacaoeventocontratoparcelas NE  ON MP.codigo = NE.parcela \n");
        sql.append(" where NE.contrato = ? ORDER BY MP.situacao DESC ");
        Declaracao dc = new Declaracao(sql.toString(), con);
        dc.setInt(1, codigoPrm.intValue());
        try (ResultSet tabelaResultado = dc.executeQuery()) {
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
        }
    }

    @Override
    public List<MovParcelaVO> consultarPorMesReferenciaPersonal(int empresa, int personal, String mesReferencia, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        // 1. busca todas as parcelas com vencimento no mes selecionado mas que sua geracao nao atende a segunda regra
        // 2. parcelas que foram geradas no final do mes (selecionado) com vencimento para o inicio do mes seguinte
        // 3. Ignora parcelas de plano personal canceladas
        sql.append("SELECT DISTINCT mp.* ");
        sql.append("FROM movparcela mp ");
        sql.append("INNER JOIN controletaxapersonal ctp ON mp.personal = ctp.codigo ");
        sql.append("INNER JOIN movprodutoparcela mpp ON mp.codigo = mpp.movparcela ");
        sql.append("INNER JOIN movproduto mpd ON mpd.codigo = mpp.movproduto ");
        sql.append("WHERE ctp.empresa = ? AND ctp.personal = ? AND mpd.mesreferencia = ? ");
        sql.append("AND (ctp.plano IS NULL OR (ctp.plano IS NOT NULL AND mp.situacao != 'CA'))");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setInt(1, empresa);
            sqlConsultar.setInt(2, personal);
            sqlConsultar.setString(3, mesReferencia);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public List<MovParcelaVO> consultarParcelasEmAbertoPorPersonal(int empresa, int personal, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        // 1. busca todas as parcelas com vencimento no mes selecionado mas que sua geracao nao atende a segunda regra
        // 2. parcelas que foram geradas no final do mes (selecionado) com vencimento para o inicio do mes seguinte
        sql.append("SELECT DISTINCT mp.* FROM movparcela mp ");
        sql.append("WHERE mp.situacao = 'EA' ");
        sql.append("AND mp.personal IN (SELECT ctp.codigo FROM controletaxapersonal ctp WHERE ctp.empresa = ? AND ctp.personal = ?) ");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setInt(1, empresa);
            sqlConsultar.setInt(2, personal);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public List consultarPorNegociacaoPersonal(String valorConsulta, String situacao, int empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT mp.* FROM movparcela mp ");
        sql.append("WHERE mp.situacao = ? AND mp.personal IN (  ");
        sql.append("SELECT ctp.codigo FROM controletaxapersonal ctp WHERE ");
        if (empresa > 0) {
            sql.append("ctp.empresa = ? AND ");
        }
        sql.append(" ctp.personal IN ( ");
        sql.append("SELECT colaborador.codigo FROM colaborador WHERE colaborador.pessoa IN ( ");
        sql.append("SELECT pessoa.codigo FROM pessoa WHERE pessoa.nome LIKE '").append(valorConsulta).append("%' )))");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setString(1, situacao);
            if (empresa > 0) {
                sqlConsultar.setInt(2, empresa);
            }
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public List consultarPorDataRegistroNegociacaoPersonal(String valorConsulta, String situacao, int empresa, Date dataInicio, Date dataTermino, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT mp.* FROM movparcela mp ");
        sql.append("INNER JOIN controletaxapersonal ctp ON mp.personal = ctp.codigo ");
        sql.append("INNER JOIN colaborador ON ctp.personal = colaborador.codigo ");
        sql.append("INNER JOIN pessoa ON colaborador.pessoa = pessoa.codigo ");
        sql.append("WHERE pessoa.nome LIKE '").append(valorConsulta).append("%' ");
        sql.append("AND mp.situacao = ? AND mp.dataregistro >= ? AND mp.dataregistro <= ? ");
        if (empresa > 0) {
            sql.append("AND ctp.empresa = ? ");
        }
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setString(1, situacao);
            sqlConsultar.setDate(2, Uteis.getDataJDBC(dataInicio));
            sqlConsultar.setDate(3, Uteis.getDataJDBC(dataTermino));
            if (empresa > 0) {
                sqlConsultar.setInt(4, empresa);
            }
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco, de acordo
     * com os filtros passados
     *
     * Autor: Carla Criado em 07/02/2011
     */
    @SuppressWarnings("unchecked")
    public List consultarPaginado(MovParcelaFiltroVO filtro, ConfPaginacao confPaginacao) throws Exception {
        consultar(getIdEntidade(), filtro.isControlarAcesso());

        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //sql principal
        StringBuffer sqlSelect = new StringBuffer("SELECT movparcela.* FROM movparcela");

        //sql inner joins
        associacoes(filtro, sqlSelect);

        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);

        //a utiliza??o dos filtros deve ser feita utilizando confPaginacao.getSqlFiltro()
        filtros(filtro, confPaginacao.getSqlFiltro());
        if (!"".equals(confPaginacao.getSqlFiltro().toString())) {
            sqlSelect.append(" WHERE ");
            sqlSelect.append(confPaginacao.getSqlFiltro().toString());
        }

        //concatena ordena??es
        ordenacao(filtro, sqlSelect);

//		System.out.println("sql: " + sqlSelect);

        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlSelect);

        //seta os valores dos filtros
        filtrosValoresStatement(filtro, confPaginacao.getStm());

        //4 - REALIZA A CONSULTA COM PAGINACAO
        try (ResultSet tabelaResultado = confPaginacao.consultaPaginada()) {
            return (montarDadosConsulta(tabelaResultado, filtro.getNivelMontarDados(), con));
        }
    }

    /**
     * Atribuindo valores para os filtros no Statement para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     *
     * Autora: Carla Criado em 07/02/2011
     */
    private void filtrosValoresStatement(MovParcelaFiltroVO filtro, PreparedStatementPersonalizado stm) throws SQLException, Exception {
        /*itens.add(new SelectItem("nomePessoa", "Nome Cliente"));
         itens.add(new SelectItem("contrato", "N?mero do Contrato"));
         itens.add(new SelectItem("codigo", "C?digo"));
         itens.add(new SelectItem("dataRegistro", "Data de Registro"));
         itens.add(new SelectItem("dataVencimento", "Data de Vencimento"));
         itens.add(new SelectItem("situacao", "Situa??o"));*/
        int i = 0;
        //INICIO - SETANDO PARAMETROS PARA "filtros(ReciboPagamentoFiltroVO filtro, StringBuffer sqlFiltro)"
        //consulta por nome
        if (filtro != null && filtro.getPessoaVO() != null) {
            if (filtro.getPessoaVO().getNome() != null && !"sem valor".equals(filtro.getPessoaVO().getNome())) {
                stm.setString(i++, filtro.getPessoaVO().getNome() + "%");
            }
        } //consulta por codigo de contrato
        else if (filtro != null && filtro.getContratoVO() != null && filtro.getContratoVO().getCodigo() > 0) {
            stm.setInt(i++, filtro.getContratoVO().getCodigo());
        } //consulta por codigo de movparcela
        else if (filtro != null && filtro.getMovParcelaVO() != null && filtro.getMovParcelaVO().getCodigo() > 0) {
            stm.setInt(i++, filtro.getMovParcelaVO().getCodigo());
        }//consulta por data de registro
        else if (filtro != null && filtro.getMovParcelaVO() != null && filtro.getMovParcelaVO().getDataRegistro() != null) {
            Date valorData = filtro.getMovParcelaVO().getDataRegistro();
            stm.setDate(i++, Uteis.getDataJDBC(valorData));
            stm.setDate(i++, Uteis.getDataJDBC(valorData));
        } //consulta por data de vencimento
        else if (filtro != null && filtro.getMovParcelaVO() != null && filtro.getMovParcelaVO().getDataVencimento() != null) {
            Date valorData = filtro.getMovParcelaVO().getDataVencimento();
            stm.setDate(i++, Uteis.getDataJDBC(valorData));
            stm.setDate(i++, Uteis.getDataJDBC(valorData));
        }//consulta por situacao de movparcela
        else if (filtro != null && !filtro.getMovParcelaVO().getSituacao().trim().isEmpty()) {
            stm.setString(i++, filtro.getMovParcelaVO().getSituacao());
        }//consulta de acordo com a empresa do usuario logado
        else if (filtro != null && filtro.getEmpresaVO() != null && filtro.getEmpresaVO().getCodigo() > 0) {
            stm.setInt(i++, filtro.getEmpresaVO().getCodigo());
        }
        //FIM - SETANDO PARAMETROS PARA "filtros(ReciboPagamentoFiltroVO filtro, StringBuffer sqlFiltro)"
    }

    /**
     * Definindo o tipo de ordenacao para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     *
     * Autora: Carla Criado em 07/02/2011
     */
    private void ordenacao(MovParcelaFiltroVO filtro, StringBuffer sql) {
        if (filtro != null) {
            if (filtro.getPessoaVO() != null) {
                sql.append(" ORDER BY pessoa.nome, MovParcela.codigo ");
            } else {
                sql.append(" ORDER BY MovParcela.dataVencimento desc, MovParcela.codigo ");
            }
        }
    }

    /**
     * Definindo as condicoes da clausa WHERE para o metodo
     * consultarPaginado(ClienteFiltroVO filtro, ConfPaginacao confPaginacao)
     *
     * Autora: Carla Criado em 07/02/2011
     */
    private void filtros(MovParcelaFiltroVO filtro, StringBuffer sqlFiltro) {
        //consulta por nome
        if (filtro != null && filtro.getPessoaVO() != null && !filtro.getPessoaVO().getNome().trim().isEmpty()) {
            sqlFiltro.append(" pessoa.nome ilike ?");
        }//consulta por codigo de contrato
        else if (filtro != null && filtro.getContratoVO() != null) {
            if (filtro.getContratoVO().getCodigo() > 0) {
                sqlFiltro.append(" contrato.codigo  = ?");
            }
        } //consulta por codigo de movparcela
        else if (filtro != null && filtro.getMovParcelaVO() != null && filtro.getMovParcelaVO().getCodigo() > 0) {
            sqlFiltro.append(" movparcela.codigo  = ?");
        } //consulta por data de registro
        else if (filtro != null && filtro.getMovParcelaVO().getDataRegistro() != null) {
            sqlFiltro.append(" movparcela.dataRegistro BETWEEN ? AND ? ");
        } //consulta por data de vencimento
        else if (filtro != null && filtro.getMovParcelaVO().getDataVencimento() != null) {
            sqlFiltro.append(" movparcela.datavencimento  BETWEEN ? AND ? ");
        } //consulta por situacao de movparcela
        else if (filtro != null && !filtro.getMovParcelaVO().getSituacao().trim().isEmpty()) {
            sqlFiltro.append(" movparcela.situacao = ? ");
        } //consulta de acordo com a empresa do usuario logado
        else if (filtro != null && filtro.getEmpresaVO() != null && filtro.getEmpresaVO().getCodigo() > 0) {
            if (!"".equals(sqlFiltro.toString())) {
                sqlFiltro.append(" AND empresa.codigo = ?");
            } else {
                sqlFiltro.append(" empresa.codigo  = ?");
            }
        }

    }

    /**
     * Definindo os Inner Join e Left Join para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     *
     * Autora: Carla Criado em 07/02/2011
     */
    private void associacoes(MovParcelaFiltroVO filtro, StringBuffer sql) {
        sql.append(" inner join contrato on contrato.codigo = movparcela.contrato ");
        sql.append(" inner join empresa on empresa.codigo = contrato.empresa ");
        sql.append(" inner join pessoa on pessoa.codigo = contrato.pessoa");
    }

    /**
     * Este m?todo ir? consultar se o produto possui algum relacionamento com
     * parcelas em aberto (excetuando a parcela que possui c?digo igual ao
     * inteiro codigoParcela, passado como par?metro)
     *
     * <AUTHOR> 20/06/2011
     */
    private boolean produtoEmParcelasAbertas(Integer codigoProduto, Integer codigoParcela) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT mp.situacao \n");
        sql.append("FROM   movparcela mp \n");
        sql.append("       INNER JOIN movprodutoparcela mpp \n");
        sql.append("       ON     mpp.movparcela = mp.codigo \n");
        if (codigoParcela != null) {
            sql.append("       AND    mp.codigo     <> ? \n");
        }
        sql.append("       AND    mp.situacao LIKE 'EA' \n");
        sql.append("       INNER JOIN movproduto mpd \n");
        sql.append("       ON     mpp.movproduto = mpd.codigo \n");
        sql.append("       AND    mpd.codigo     = ? ");
        int i = 0;
        Declaracao dc = new Declaracao(sql.toString(), con);
        if (codigoParcela != null) {
            dc.setInt(++i, codigoParcela);
        }
        dc.setInt(++i, codigoProduto);
        try (ResultSet rs = dc.executeQuery()) {
            //se houverem resultados para a pesquisa, o produto ainda est? relacionado a uma parcela em aberto
            return rs.next();
        }

    }

    /**
     *
     * @param dataVencimento
     * @return Lista de parcelas que obedecem aos seguinte crit?rios: 1. Estejam
     * em aberto (situacao 'EA'); 2. Sejam do regime de recorr?ncia
     * (regimeRecorrencia == true); 3. Que ainda n?o foram processadas nenhuma
     * vez (nrTentativas == 0); 4. Que possua a data de vencimento igual ou
     * menor ao par?metro 'dataVencimento'. Isso faz com que parcelas do passado
     * tamb?m sejam processadas; 5. Que possua ?ltima transa??o aprovada para
     * que seja poss?vel a Recorr?ncia.
     * @throws Exception
     */
    public List<MovParcelaVO> consultarParcelasRecorrencia(Date dataVencimento) throws Exception {
        String sql = String.format("select * from movparcela "
                + "where situacao = 'EA' and regimerecorrencia = true "
                + "and (nrtentativas = 0 or reagendada)"
                + "and cast(datacobranca as date) <= '%s' "
                + "and codigo not in (select coalesce(movparcela,0) from remessaitem) "
                + "and contrato in (select contrato from contratorecorrencia where ultimaTransacaoAprovada <> '') "
                + "order by dataVencimento limit 5000",
                new Object[]{Uteis.getDataFormatoBD(dataVencimento)});
        try (ResultSet rs = criarConsulta(sql, con)) {
            return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_PARCELA, con);
        }
    }

    /**
     * Consulta as parcelas para cancelamento automático de contratos.<br>
     *     A consulta possui as restrições:<br>
     *     - A situação da parcela deve ser 'Em aberto'(EA)
     *     - O contrato deve estar ativo
     *     - O contrato deve estar sinalizado para renovação automática
     *     [Importante] Se configurado para simplesmente cancelar o contrato com X parcelas, a regras acima será ignorada
     *     - A data de cobrança/vencimento da parcela deve ser anterior à data informada pelo parâmetro {@param dataVencimento}
     *     - O contrato não deve ser de recorrência
     */
    public List<MovParcelaVO> consultarParcelasParaCancelamentoAutomatico(Date dataVencimento) throws Exception {
        String dataVencimentoConsiderar = Uteis.getDataFormatoBD(dataVencimento);
        String sqlContratosCancelar = "select c.codigo as contrato from movparcela mp "
                        + "inner join contrato c on c.codigo = mp.contrato "
                        + "left join remessaitem ri on ri.movparcela = mp.codigo "
                        + "left join remessa re on re.codigo = ri.remessa "
                        + "left join contratorecorrencia cr on cr.contrato = mp.contrato "
                        + "where mp.situacao = 'EA' "
                        + "and c.situacao = 'AT' "
                        + "and cast(mp.datacobranca as date) < '%s' "
                        + "and (((select max(codigo) from remessaitem where movparcela = mp.codigo) = ri.codigo) "
                        + "or (select max(codigo) from remessaitem where movparcela = mp.codigo) IS NULL) "
                        + "and (re.situacaoremessa IS NULL or re.situacaoremessa = 2) "
                        + "and cr.contrato IS NULL "
                        + "group by c.codigo ";

        final String sqlFormatado = String.format(sqlContratosCancelar, dataVencimentoConsiderar);
        List<MovParcelaVO> vetResultado;
        try (ResultSet rs = criarConsulta(sqlFormatado, con)) {
            vetResultado = new ArrayList<>();
            while (rs.next()) {
                MovParcelaVO obj = new MovParcelaVO();
                obj.getContrato().setCodigo(rs.getInt("contrato"));
                vetResultado.add(obj);
            }
        }
        return vetResultado;
    }


    public List<MovParcelaVO> consultarParcelasRecorrenciaParaCancelamentoAutomatico(Date dataVencimento) throws Exception {
        String sql = String.format("select * from movparcela \n" +
                        "  inner join contratorecorrencia on contratorecorrencia.contrato = movparcela.contrato\n" +
                        "  inner join contrato con on con.codigo = movparcela.contrato and con.situacao = 'AT'\n" +
                        "  where con.vigenciade < '%s'  and not exists (select codigo from contratooperacao where contrato = con.codigo and tipooperacao = 'CA') and \n" +
                        "  movparcela.codigo in (select par.codigo from movparcela par inner join movprodutoparcela mpp ON mpp.movparcela = par.codigo inner join movproduto mpr ON mpr.codigo = mpp.movproduto " +
                        "  inner join produto prd on prd.codigo = mpr.produto AND prd.tipoproduto = 'PM' left join PagamentoMovParcela on PagamentoMovParcela.movparcela = par.codigo where par.contrato = con.codigo  \n" +
                        "  and par.situacao = 'EA' and par.valorparcela > 0 and PagamentoMovParcela.codigo is null\n" +
                        "  and par.regimerecorrencia \n" +
                        "  and coalesce(movparcela.datacobranca,movparcela.datavencimento) < to_date( '%s' , 'YYYY-MM-DD') \n" +
                        "  and par.codigo not in (select coalesce(movparcela,0) from remessaitem inner join remessa on remessa.codigo = remessaitem.remessa and remessa.situacaoremessa = 4) order by par.datavencimento limit 1)\n" +
                        "  order by dataVencimento desc limit 5000 ",
                new Object[]{Uteis.getDataFormatoBD(dataVencimento),Uteis.getDataFormatoBD(dataVencimento)});
        try (ResultSet rs = criarConsulta(sql, con)) {
            return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_PARCELA, con);
        }
    }

    /**
     *
     * @param dataVencimento
     * @return Lista de parcelas que obedecem aos seguinte crit?rios afim de
     * tentar cobrar novamente parcelas vencidas que n?o foram recebidas
     * devidamente: 1. Estejam em aberto (situacao 'EA'); 2. Sejam do regime de
     * recorr?ncia (regimeRecorrencia == true); 3. Que j? foram processadas mais
     * de uma vez (nrTentativas > 0) 4. Que possua a data de vencimento superior
     * ao par?metro 'dataVencimento'. Isso faz com que parcelas do passado
     * tamb?m sejam processadas.
     * @throws Exception
     */
    public List<MovParcelaVO> consultarParcelasRecorrenciaParaRepescagem(Date dataVencimento, Date dataLimite) throws Exception {
        StringBuilder sql = new StringBuilder("select * from movparcela ");
        sql.append("where situacao = 'EA' and regimerecorrencia = true ");
        sql.append("and nrtentativas > 0 \n");
        if(dataLimite != null){
            sql.append("and cast(datavencimento as date) > '").append(Uteis.getDataFormatoBD(dataLimite)).append("' \n");
        }
        sql.append("and cast(datavencimento as date) <= '").append(Uteis.getDataFormatoBD(dataVencimento)).append("' \n");
        sql.append("and codigo not in (select coalesce(movparcela,0) from remessaitem) \n");
        sql.append("and codigo in (select movparcela from transacaomovparcela \n");
        sql.append("where transacao in (select codigo from transacao \n");
        sql.append("where situacao in ");
        sql.append(SituacaoTransacaoEnum.getSituacoesRepescagem()).append(")) \n");
        sql.append("and (select situacao from cliente where pessoa = movparcela.pessoa) like 'AT' \n");
        sql.append("order by dataVencimento ");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_PARCELA, con);
        }
    }


    public Integer consultarParcelasVencidasContratosRecorrencia(Date dataVencimento, int contrato) throws Exception {
        StringBuilder sql = new StringBuilder("select count(*) parcelas from movparcela ");
        sql.append("where situacao = 'EA' and regimerecorrencia = true ");
        sql.append(" and contrato = ").append(contrato);
        sql.append(" and cast(datavencimento as date) < '").append(Uteis.getDataFormatoBD(dataVencimento)).append("'");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return rs.getInt("parcelas");
            }
        }
        return 0;
    }


    public Boolean consultarParcelasRemessasComBoletosItauDeContrato(int contrato) throws Exception {
        StringBuilder sql = new StringBuilder("select count(*) boletos from movparcela ");
        sql.append("inner join remessaitem on remessaitem.movparcela = movparcela.codigo ");
        sql.append("inner join remessa on remessa.codigo = remessaitem.remessa and remessa.tipo = ").append(TipoRemessaEnum.ITAU_BOLETO.getId());
        sql.append(" where movparcela.contrato = ").append(contrato);
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                if (rs.getInt("boletos") > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean parcelaEmAberto(Integer codigoPessoa, Date mesVencimentoAte)throws Exception{
        StringBuilder sql = new StringBuilder();
        Calendar dataIni = Calendario.getInstance();
        dataIni.setTime(mesVencimentoAte);
        dataIni.set(Calendar.DAY_OF_MONTH, 1);
        Calendar dataFim = Calendario.getInstance();
        dataFim.setTime(mesVencimentoAte);
        dataFim.set(Calendar.DAY_OF_MONTH, dataFim.getActualMaximum(Calendar.DAY_OF_MONTH));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sql.append("select *  \n");
        sql.append("from movParcela \n");
        sql.append("where pessoa = ").append(codigoPessoa).append(" \n");
        sql.append(" and situacao = 'EA' \n");
        //sql.append(" and dataVencimento >= '").append(sdf.format(dataIni.getTime())).append("'");
        sql.append(" and dataVencimento <= '").append(sdf.format(dataFim.getTime())).append("'");
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                return rs.next();
            }
        }
    }

    private MovParcelaVO pesquisaPorCodigoParcela(List<MovParcelaVO> src, MovParcelaVO pesquisada) {
        for (MovParcelaVO movParcelaVO : src) {
            if (movParcelaVO.getCodigo().intValue() == pesquisada.getCodigo().intValue()) {
                return movParcelaVO;
            }
        }
        return null;
    }

    /**
     * Recebe uma lista de Parcelas com suas datas de vencimento alteradas e
     * tenta efetuar as altera??es. Pr?-requisitos: 1. Todas as parcelas devem
     * estar com situa??o "Em aberto"; 2. N?o permite que a nova data de
     * vencimento seja menor ou igual a data atual, deve ser no m?nimo amanh?;
     *
     * @param listaModificada
     * @param listaOriginal
 * @param remessas
 * @throws Exception
     */
    @Override
    public void alterarVencimentoListaParcelas(List<MovParcelaVO> listaModificada, List<MovParcelaVO> listaOriginal, boolean remessas,
                                               String usuarioLogado, String origemAlteracao, boolean limparNrTentativas) throws Exception {
        alterarVencimentoListaParcelas(listaModificada, listaOriginal, remessas, false, usuarioLogado,origemAlteracao, limparNrTentativas);
    }

    public void alterarVencimentoListaParcelas(List<MovParcelaVO> listaModificada, List<MovParcelaVO> listaOriginal, boolean remessas,
                                               String usuarioLogado, String origemAlteracao, boolean limparNrTentativas, boolean permitirAlterarAnteriorDataAtual) throws Exception {
        alterarVencimentoListaParcelas(listaModificada, listaOriginal, remessas, permitirAlterarAnteriorDataAtual, usuarioLogado,origemAlteracao, limparNrTentativas);
    }

    /**
     * Recebe uma lista de Parcelas com suas datas de vencimento alteradas e
     * tenta efetuar as altera??es. Pr?-requisitos: 1. Todas as parcelas devem
     * estar com situa??o "Em aberto"; 2. N?o permite que a nova data de
     * vencimento seja menor ou igual a data atual, deve ser no m?nimo amanh?;
     *
     * @param listaModificada
     * @throws Exception
     */
    @Override
    public void alterarVencimentoListaParcelas(List<MovParcelaVO> listaModificada, List<MovParcelaVO> listaOriginal, boolean remessas,
                                               boolean permitirAlterarAnteriorDataAtual, String usuarioLogado, String origemAlteracao, boolean limparNrTentativas) throws Exception {
        boolean todasEmAberto = true;
        boolean todasVencimentoFuturo = true;
        for (MovParcelaVO movParcelaVO : listaModificada) {
            if (!movParcelaVO.getSituacao().equals("EA")) {
                todasEmAberto = false;
                break;
            }
            if (!remessas && Calendario.menorOuIgual(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
                todasVencimentoFuturo = false;
                break;
            }

            if (remessas && Calendario.menorOuIgual(movParcelaVO.getDataCobranca(), Calendario.hoje())) {
                todasVencimentoFuturo = false;
                break;
            }
        }
        if (!todasEmAberto) {
            throw new Exception("Para alterar o vencimento das parcelas todas precisam estar em aberto.");
        }
        if (!todasVencimentoFuturo && !permitirAlterarAnteriorDataAtual) {
            throw new Exception("A data de vencimento da(s) parcela(s) não poder ser menor que "+Uteis.getData(Calendario.hoje())+".");
        }
        //validado as parcelas acima, efetua grava??o dos dados
        try {
            ClienteMensagem clienteMensagem = new ClienteMensagem(getCon());
            for (MovParcelaVO movParcelaVO : listaModificada) {
                if (!remessas) {
                    movParcelaVO.setDataCobranca(movParcelaVO.getDataVencimento());
                }
                MovParcelaVO mpAntes = pesquisaPorCodigoParcela(listaOriginal, movParcelaVO);
                try {
                    LogInterfaceFacade logFacade = new Log(this.con);
                    movParcelaVO.setObjetoVOAntesAlteracao(mpAntes);
                    movParcelaVO.setReagendada(true);
                    if (limparNrTentativas) {
                        movParcelaVO.setNrTentativas(0);
                        executarConsulta("update movparcela set nrtentativasprocessoretentativa = 0, nrtentativas=0 where codigo = " + movParcelaVO.getCodigo(), con);
                    }
                    alterarSemCommit(movParcelaVO);
                    if (Calendario.maiorOuIgual(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
                        clienteMensagem.excluirClienteMensagemPorMovParcela(movParcelaVO.getCodigo());
                    }
                    if (JSFUtilities.isJSFContext()) {
                        movParcelaVO.setNovoObj(false);
                        List lista = movParcelaVO.gerarLogAlteracaoObjetoVO();
                        Iterator i = lista.iterator();
                        while (i.hasNext()) {
                            LogVO log = (LogVO) i.next();
                            log.setChavePrimaria(movParcelaVO.getCodigo().toString());
                            log.setNomeEntidade("MOVPARCELA");
                            log.setValorCampoAlterado(log.getValorCampoAlterado() + " ID: " + movParcelaVO.getCodigo());
                            log.setPessoa(movParcelaVO.getPessoa().getCodigo());
                            logFacade.incluirSemCommit(log);
                        }
                    } else {
                        LogVO log = new LogVO();
                            log.setNomeEntidade("MOVPARCELA");
                            log.setNomeEntidadeDescricao("Movimento da Parcela");
                            log.setNomeCampo("dataCobrança");
                            log.setValorCampoAnterior(Uteis.getData(((MovParcelaVO) movParcelaVO.getObjetoVOAntesAlteracao()).getDataCobranca()));
                            log.setValorCampoAlterado(Uteis.getData(movParcelaVO.getDataCobranca()) + " ID: " + movParcelaVO.getCodigo());
                            log.setChavePrimaria(movParcelaVO.getCodigo().toString());
                            log.setDataAlteracao(Calendario.hoje());
                            log.setResponsavelAlteracao(usuarioLogado);
                            log.setPessoa(movParcelaVO.getPessoa().getCodigo());
                            log.setOperacao("ALTERACAO DE COBRANCA - ORIGEM: " + origemAlteracao);
                            logFacade.incluirSemCommit(log);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    throw ex;
                }

            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public List<CaixaAbertoTO> consultaCaixaEmAberto(String matriculaCliente, String nomeCliente, Integer empresa,
                                                     Date dataInicio, Date dataFim, ConfPaginacao confPaginacao, boolean recorrentes, boolean ordenarPorLancamento,
                                                     List<Integer> parcelasEscolhidas, boolean naoApresentarVencimentosFuturos) throws SQLException, Exception {
        return consultaCaixaEmAberto(matriculaCliente, nomeCliente, null, empresa, dataInicio, dataFim, confPaginacao, recorrentes,
                ordenarPorLancamento, parcelasEscolhidas, naoApresentarVencimentosFuturos, 0);
    }

    /**
     * @param matriculaCliente
     * @param nomeCliente
     * @param descricaoProduto
     * @param empresa                         Se 0, será conultado o caixa em aberto de todas as empresa
     * @param dataInicio
     * @param dataFim
     * @param confPaginacao
     * @param recorrentes
     * @param ordenarPorLancamento
     * @param parcelasEscolhidas
     * @param naoApresentarVencimentosFuturos
     * @param paginaInicialExterna
     * @return
     * @throws SQLException
     * @throws Exception
     */
    public List<CaixaAbertoTO> consultaCaixaEmAberto(String matriculaCliente, String nomeCliente, String descricaoProduto, Integer empresa,
                                                     Date dataInicio, Date dataFim, ConfPaginacao confPaginacao, boolean recorrentes, boolean ordenarPorLancamento,
                                                     List<Integer> parcelasEscolhidas, boolean naoApresentarVencimentosFuturos, Integer paginaInicialExterna) throws SQLException, Exception {

        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);

        if(paginaInicialExterna > 0) {
            confPaginacao.setPagIni(paginaInicialExterna);
        }
        //sql principal
        StringBuffer sql = new StringBuffer();

        sql.append("SELECT\n");
        sql.append("  mp.contrato,\n");
        sql.append("  mp.vendaavulsa,\n");
        sql.append("  mp.aulaavulsadiaria,\n");
        sql.append("  mp.personal,\n");
        sql.append("  mp.pessoa,\n");
        sql.append("  pes.nome,\n");
        sql.append("  cli.codigo as cliente,\n");
        sql.append("  co.codigo as colaborador,\n");
        sql.append("  min(mp.dataregistro) as dataregistro, \n");
        sql.append("  empresa.nome empresa_nome, \n");
        sql.append("  empresa.codigo empresa, \n");
        sql.append("  empresa.locale locale \n");
        if (!UteisValidacao.emptyString(matriculaCliente) || !UteisValidacao.emptyString(nomeCliente)) {
            confPaginacao.setItensPorPagina(1000);
            sql.append("FROM movparcela mp\n");
            sql.append("  INNER JOIN pessoa pes ON pes.codigo = mp.pessoa\n");
            sql.append("  LEFT JOIN cliente cli ON pes.codigo = cli.pessoa\n");
            sql.append("  LEFT JOIN clientetitulardependente dependente ON cli.codigo = dependente.cliente\n");
            sql.append("  LEFT JOIN clientetitulardependente titular ON titular.codigo = dependente.clientetitular\n");
            sql.append("  LEFT JOIN clientetitulardependente dependentesDoTitular ON titular.codigo = dependentesDoTitular.clientetitular\n");
            sql.append("  LEFT JOIN cliente cliDependentes ON cliDependentes.codigo = dependentesDoTitular.cliente\n");
            sql.append("  LEFT JOIN familiar f ON f.cliente = cli.codigo\n");
            sql.append("  LEFT JOIN cliente clif ON f.familiar = clif.codigo\n");
            sql.append("  LEFT JOIN pessoa pesf ON clif.pessoa = pesf.codigo\n");
            sql.append("  LEFT JOIN negociacaoeventocontratoparcelas necp ON mp.codigo = necp.parcela\n");
        } else {
            sql.append("FROM movparcela mp\n");
            sql.append("  INNER JOIN pessoa pes ON pes.codigo = mp.pessoa\n");
            sql.append("  LEFT JOIN cliente cli ON pes.codigo = cli.pessoa\n");
            sql.append("  LEFT JOIN negociacaoeventocontratoparcelas necp ON mp.codigo = necp.parcela\n");
        }
        sql.append("  INNER JOIN empresa ON empresa.codigo = mp.empresa ");
        sql.append("  LEFT JOIN colaborador co ON co.pessoa = pes.codigo AND empresa.codigo = co.empresa\n");

        StringBuilder sqlProduto = new StringBuilder();

        if (!UteisValidacao.emptyString(descricaoProduto)) {
            sqlProduto.append("  INNER JOIN( \n");
            sqlProduto.append("    select DISTINCT ON(contrato, vendaavulsa) contrato, vendaavulsa \n");
            sqlProduto.append("    FROM movproduto mpro \n");
            sqlProduto.append("    INNER JOIN produto pro ON pro.codigo = mpro.produto \n");
            sqlProduto.append("    WHERE pro.descricao ILIKE ? \n");
            sqlProduto.append("    order by contrato, vendaavulsa \n");
            sqlProduto.append("    ) pmp ON pmp.contrato = mp.contrato OR pmp.vendaavulsa = mp.vendaavulsa \n");
        }

        sql.append(sqlProduto);

        sql.append("WHERE necp.parcela IS NULL\n");
         if(!UteisValidacao.emptyString(matriculaCliente) || !UteisValidacao.emptyString(nomeCliente)) {
                sql.append("and ((pes.codigo = cliDependentes.pessoa) OR (pes.codigo = cli.pessoa AND dependente.codigo IS NULL) or (co.pessoa is not null))\n");
         }
        if (!recorrentes) {
            sql.append("AND mp.regimerecorrencia = FALSE  \n");
        }
        sql.append("AND mp.situacao= 'EA'  \n");

        if (dataInicio != null) {
            sql.append("AND mp.datavencimento >= ? \n");
        }
        if (dataFim != null) {
            sql.append("AND mp.datavencimento <= ? \n");
        }
        if (naoApresentarVencimentosFuturos) {
            sql.append("AND mp.datavencimento <= '").append(Uteis.getDataJDBC(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("'\n");
        }
        if (!UteisValidacao.emptyString(nomeCliente)) {
            sql.append("AND (pes.nome ILIKE ? OR pesf.nome ILIKE ?)\n");
        }
        if (!UteisValidacao.emptyString(matriculaCliente)) {
            sql.append("AND (cli.codigomatricula::text ILIKE ? OR cli.matricula ILIKE ? OR clif.codigomatricula::text ILIKE ? OR clif.matricula ILIKE ?) \n");
        }

        if(empresa == 0){
            sql.append("AND mp.empresa IS NOT NULL \n");
        }else{
            sql.append("AND mp.empresa = ?  \n");
        }

        sql.append(" GROUP BY mp.contrato, mp.vendaavulsa, aulaavulsadiaria, personal, mp.pessoa, pes.nome, cli.codigo, co.codigo, empresa.nome, empresa.codigo, empresa.locale \n");

        sql.append("UNION ALL\n ");

        sql.append("SELECT mp.contrato, mp.vendaavulsa, mp.aulaavulsadiaria, mp.personal, mp.pessoa, v.nomecomprador AS nome, cli.codigo as cliente, co.codigo as colaborador,\n");
        sql.append("min(mp.dataregistro) as dataregistro, \n");
        sql.append("empresa.nome empresa_nome, \n");
        sql.append("empresa.codigo empresa, \n");
        sql.append("empresa.locale locale \n");
        sql.append("FROM movparcela mp  \n");
        sql.append("INNER JOIN vendaavulsa v ON mp.vendaavulsa = v.codigo\n");
        sql.append("INNER JOIN empresa ON empresa.codigo = mp.empresa\n");
        sql.append("LEFT JOIN colaborador co ON co.pessoa = mp.pessoa\n");
        sql.append("LEFT JOIN cliente cli ON mp.pessoa = cli.pessoa\n");
        if (!UteisValidacao.emptyString(matriculaCliente)) {
            sql.append("INNER JOIN cliente c ON mp.pessoa = c.pessoa  \n");
        }
        sql.append(sqlProduto);
        sql.append("WHERE v.tipocomprador ILIKE 'CN'\n");
        if (!recorrentes) {
            sql.append("AND mp.regimerecorrencia = FALSE  \n");
        }
        sql.append("AND mp.situacao= 'EA'  \n");

        if (dataInicio != null) {
            sql.append("AND mp.datavencimento >= ? \n");
        }
        if (dataFim != null) {
            sql.append("AND mp.datavencimento <= ? \n");
        }
        if (naoApresentarVencimentosFuturos) {
            sql.append("AND mp.datavencimento <= '").append(Uteis.getDataJDBC(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("'\n");
        }
        if (!UteisValidacao.emptyString(nomeCliente)) {
            sql.append("AND v.nomecomprador ILIKE ?  \n");
        }
        if (!UteisValidacao.emptyString(matriculaCliente)) {
            sql.append("AND (c.matricula ILIKE ? OR c.codigomatricula::text ILIKE ? )\n");
        }
        if(empresa == 0){
            sql.append("AND mp.empresa IS NOT NULL  \n");
        }else{
            sql.append("AND mp.empresa = ?  \n");
        }
        sql.append(" GROUP BY mp.contrato, mp.vendaavulsa, aulaavulsadiaria, personal, mp.pessoa, v.nomecomprador, cli.codigo, co.codigo, empresa.nome, empresa.codigo, empresa.locale \n");


        if (ordenarPorLancamento) {
            sql.append(" ORDER BY dataregistro DESC ");
        } else {
            sql.append(" ORDER BY nome");
        }

        String sqlCount = "SELECT COUNT(*) FROM (" + sql.toString() + ") AS consultaCount";

        ResultSet rs;
        try (PreparedStatement pStmCount = con.prepareStatement(sqlCount)) {
            int i = 1;
            i = prepararStatement(matriculaCliente, nomeCliente, descricaoProduto, empresa, dataInicio, dataFim, pStmCount, i, true);
            //duas vezes pois s?o duas consultas em uma
            i = prepararStatement(matriculaCliente, nomeCliente, descricaoProduto, empresa, dataInicio, dataFim, pStmCount, i, false);

            try (ResultSet rsCount = pStmCount.executeQuery()) {
                rsCount.next();
                //3 - ADICIONA PAGINACAO NA CONSULTA
                confPaginacao.addPaginacao(sql);
                i = 0;
                i = preencherFiltros(matriculaCliente, nomeCliente, descricaoProduto, empresa, dataInicio, dataFim, confPaginacao, i, true);

                //duas vezes pois s?o duas consultas em uma
                i = preencherFiltros(matriculaCliente, nomeCliente, descricaoProduto, empresa, dataInicio, dataFim, confPaginacao, i, false);

                //4 - REALIZA A CONSULTA COM PAGINACAO
                rs = confPaginacao.consultaPaginada(rsCount.getInt(1));
            }
            Date dataVencimento = dataFim;
            if (naoApresentarVencimentosFuturos) {
                dataVencimento = Uteis.obterUltimoDiaMes(Calendario.hoje());
            }
            return montarDadosCaixaAberto(rs, dataInicio, dataVencimento, parcelasEscolhidas);
        }

    }

    private int preencherFiltros(String matriculaCliente, String nomeCliente, String descricaoProduto, Integer empresa, Date dataInicio,
            Date dataFim, ConfPaginacao confPaginacao, Integer i, boolean duplicar) throws Exception, SQLException {

        if (!UteisValidacao.emptyString(descricaoProduto)) {
            confPaginacao.getStm().setString(i++, "%"+descricaoProduto.toUpperCase() + "%");
        }

        if (dataInicio != null && dataFim != null) {
            confPaginacao.getStm().setTimestamp(i++, Uteis.getDataHoraJDBC(dataInicio, "00:00:00"));
            confPaginacao.getStm().setTimestamp(i++, Uteis.getDataHoraJDBC(dataFim, "23:59:59"));
        }
        if (!UteisValidacao.emptyString(nomeCliente)) {
            confPaginacao.getStm().setString(i++, nomeCliente.toUpperCase() + "%");
            if(duplicar){
                confPaginacao.getStm().setString(i++, nomeCliente.toUpperCase() + "%");
            }

        }
        if (!UteisValidacao.emptyString(matriculaCliente)) {
            confPaginacao.getStm().setString(i++, matriculaCliente.toUpperCase());
            confPaginacao.getStm().setString(i++, matriculaCliente.toUpperCase());
            if(duplicar){
                confPaginacao.getStm().setString(i++, matriculaCliente.toUpperCase());
                confPaginacao.getStm().setString(i++, matriculaCliente.toUpperCase());
            }
        }
        if(empresa != 0){
            confPaginacao.getStm().setInt(i++, empresa);
        }
        return i;
    }

    private int prepararStatement(String matriculaCliente, String nomeCliente, String descricaoProduto, Integer empresa,
                                  Date dataInicio, Date dataFim, PreparedStatement pStmCount, int i,
                                  boolean duplicar) throws SQLException, Exception {

        if (!UteisValidacao.emptyString(descricaoProduto)) {
            pStmCount.setString(i++, "%"+descricaoProduto.toUpperCase() + "%");
        }

        if (dataInicio != null && dataFim != null) {
            pStmCount.setTimestamp(i++, Uteis.getDataHoraJDBC(dataInicio, "00:00:00"));
            pStmCount.setTimestamp(i++, Uteis.getDataHoraJDBC(dataFim, "23:59:59"));
        }
        if (!UteisValidacao.emptyString(nomeCliente)) {
            pStmCount.setString(i++, nomeCliente.toUpperCase() + "%");
        }
        if (!UteisValidacao.emptyString(nomeCliente) && duplicar) {
            pStmCount.setString(i++, nomeCliente.toUpperCase() + "%");
        }
        if (!UteisValidacao.emptyString(matriculaCliente)) {
            pStmCount.setString(i++, matriculaCliente.toUpperCase());
            pStmCount.setString(i++, matriculaCliente.toUpperCase());
        }
        if (!UteisValidacao.emptyString(matriculaCliente) && duplicar) {
            pStmCount.setString(i++, matriculaCliente.toUpperCase());
            pStmCount.setString(i++, matriculaCliente.toUpperCase());
        }
        if(empresa != 0){
            pStmCount.setInt(i++, empresa);
        }

        return i;
    }

    public List<CaixaAbertoTO> montarDadosCaixaAberto(ResultSet rs,  Date dataInicioVencimento, Date dataVencimento, List<Integer> parcelasEscolhidas) throws Exception {
        List<CaixaAbertoTO> dados = new ArrayList<CaixaAbertoTO>();
        while (rs.next()) {
            CaixaAbertoTO obj = new CaixaAbertoTO();
            obj.setPessoa(rs.getInt("pessoa"));
            obj.setContrato(rs.getInt("contrato"));
            obj.setVendaAvulsa(rs.getInt("vendaavulsa"));
            obj.setDiaria(rs.getInt("aulaavulsadiaria"));
            obj.setPersonal(rs.getInt("personal"));
            obj.setNomeCliente(rs.getString("nome"));
            obj.setCliente(rs.getInt("cliente"));
            obj.setColaborador(rs.getInt("colaborador"));
            obj.setEmpresaNome(rs.getString("empresa_nome"));
            obj.setEmpresaCodigo(rs.getInt("empresa"));
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(obj.getEmpresaCodigo());
            empresaVO.setNome(obj.getEmpresaNome());
            empresaVO.setLocaleTexto(rs.getString("locale"));
            obj.setEmpresaVO(empresaVO);

            if (!UteisValidacao.emptyNumber(obj.getContrato())) {
                //Consulta de Contrato
                String consultaBasica = "SELECT p.descricao, c.valorfinal, c.datalancamento FROM contrato c, plano p WHERE p.codigo = c.plano AND c.codigo = " + obj.getContrato();
                ResultSet rsDados = criarConsulta(consultaBasica, con);
                rsDados.next();
                obj.setDescricao(rsDados.getString("descricao"));
                obj.setValorTotal(rsDados.getDouble("valorfinal"));
                obj.setLancamento(rsDados.getDate("datalancamento"));
                obj.setParcelas(consultarParcelas(obj.getContrato(), dataInicioVencimento, dataVencimento, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                obj.setMarcarTodas(true);
                for (MovParcelaVO parcela : obj.getParcelas()) {
                    parcela.getPessoa().setCodigo(obj.getPessoa());
                    parcela.getPessoa().setNome(obj.getNomeCliente());
                    if (parcelasEscolhidas.contains(parcela.getCodigo())) {
                        parcela.setParcelaEscolhida(true);
                    } else {
                        obj.setMarcarTodas(false);
                    }

                }
                Ordenacao.ordenarLista(obj.getParcelas(), "dataVencimento");
            } else if (!UteisValidacao.emptyNumber(obj.getVendaAvulsa())) {
                //Consulta de Venda Avulsa
                List<MovParcelaVO> lista = consultarPorCodigoVendaAvulsaLista(obj.getVendaAvulsa(), dataInicioVencimento, dataVencimento, "EA", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (lista == null || lista.isEmpty()) {
                    continue;
                }
                MovParcelaVO parc1 = lista.get(0);
                obj.setDescricao(parc1.getDescricao());
                obj.setValorTotal(0.0);
                obj.setLancamento(parc1.getDataVencimento());
                obj.setMarcarTodas(true);
                for (MovParcelaVO parcela : lista) {
                    MovProduto movProduto = new MovProduto();
                    if (parcela.getVendaAvulsaVO() != null){
                        List<MovProdutoVO> listMovProdutoVO = movProduto.consultarTodosPorVendaAvulsa(parcela.getVendaAvulsaVO().getCodigo());
                        StringBuilder produtos = new StringBuilder();
                        for (MovProdutoVO mp : listMovProdutoVO){
                                produtos.append(mp.getDescricao());
                            obj.setDescricaoProduto(produtos.toString());
                            produtos.append("/ ");
                        }
                        obj.setApresentarNomeProduto(UteisValidacao.emptyString(produtos.toString())?false:true);
                    }
                    parcela.getPessoa().setCodigo(obj.getPessoa());
                    parcela.getPessoa().setNome(obj.getNomeCliente());
                    obj.setValorTotal(obj.getValorTotal() + parcela.getValorParcela());
                    if (parcelasEscolhidas.contains(parcela.getCodigo())) {
                        parcela.setParcelaEscolhida(true);
                    } else {
                        obj.setMarcarTodas(false);
                    }
                }
                obj.setParcelas(lista);
            } else {
                List<MovParcelaVO> lista = new ArrayList<MovParcelaVO>();
                if (!UteisValidacao.emptyNumber(obj.getDiaria())) {
                    //Consulta de Aula Avulsa
                    lista = consultarPorCodigoAulaAvulsaDiariaLista(obj.getDiaria(),"EA", false, dataInicioVencimento, dataVencimento, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else if (!UteisValidacao.emptyNumber(obj.getPersonal())) {
                    //Consulta de Personal
                    StringBuilder consultaBasica = new StringBuilder("SELECT * FROM movparcela WHERE situacao = 'EA' AND personal = ").append(obj.getPersonal()).append("\n");

                    if (dataInicioVencimento != null) {
                        consultaBasica.append("AND datavencimento >= '").append(Uteis.getDataJDBC(dataInicioVencimento)).append("'\n");
                    }
                    if (dataVencimento != null) {
                        consultaBasica.append("AND datavencimento <= '").append(Uteis.getDataJDBC(dataVencimento)).append("'\n");
                    }

                    try (ResultSet resultSet = criarConsulta(consultaBasica.toString(), con)) {
                        lista = montarDadosConsulta(resultSet, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                    }
                }
                if (lista == null || lista.isEmpty()) {
                    continue;
                }
                MovParcelaVO parc1 = lista.get(0);
                obj.setDescricao(parc1.getDescricao());
                obj.setValorTotal(0.0);
                obj.setLancamento(parc1.getDataVencimento());
                obj.setMarcarTodas(true);
                for (MovParcelaVO parcela : lista) {
                    parcela.getPessoa().setCodigo(obj.getPessoa());
                    parcela.getPessoa().setNome(obj.getNomeCliente());
                    obj.setValorTotal(obj.getValorTotal() + parcela.getValorParcela());
                    if (parcelasEscolhidas.contains(parcela.getCodigo())) {
                        parcela.setParcelaEscolhida(true);
                    } else {
                        obj.setMarcarTodas(false);
                    }
                }
                obj.setParcelas(lista);
            }
            obj.setExibeParcelas(false);
            dados.add(obj);
        }
        return dados;
    }

    public MovParcelaVO gerarParcelaQuitacaoCancelamento(Double valorParcela, MovProdutoVO produtoQuitacao, UsuarioVO responsavel, Date dataCancelamento, ContratoVO contrato) {
        MovParcelaVO parcela = new MovParcelaVO();
        parcela.setDescricao("QUITAÇÃO CANCELAMENTO CONTRATO - " + contrato.getCodigo());
        parcela.setDataRegistro(dataCancelamento);
        parcela.setDataVencimento(dataCancelamento);
        parcela.setEmpresa(contrato.getEmpresa());
        if (contrato.getPessoaOriginal() != null && !UteisValidacao.emptyNumber(contrato.getPessoaOriginal().getCodigo())) {
            parcela.setPessoa(contrato.getPessoaOriginal());
        } else {
            parcela.setPessoa(contrato.getPessoa());
        }
        parcela.setContrato(contrato);
        parcela.setResponsavel(responsavel);
        parcela.setSituacao("EA");
        parcela.setValorParcela(valorParcela);


        MovProdutoParcelaVO mpp = new MovProdutoParcelaVO();
        mpp.setMovProduto(produtoQuitacao.getCodigo());
        mpp.setValorPago(produtoQuitacao.getTotalFinal());
        mpp.setMovProdutoVO(produtoQuitacao);
        parcela.getMovProdutoParcelaVOs().add(mpp);

        return parcela;

    }

    @Override
    public boolean existeParcelaEmAbertoNaoRecorrenciaPorCodigoPessoa(Integer valorConsulta) throws Exception {
        String sqlStr = "select coalesce(count(*), 0) from movparcela where situacao = 'EA' and regimerecorrencia = false  and parceladcc = false "
                + "and pessoa = " + valorConsulta.toString();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return tabelaResultado.next() && tabelaResultado.getInt(1) > 0;
            }
        }
    }

    public boolean existeParcelaEmSituacao(Integer cliente, String situacao, Integer empresa) throws  Exception{
        String sql = "SELECT mov.codigo FROM movparcela mov INNER JOIN cliente cli ON cli.pessoa = mov.pessoa WHERE cli.codigo = ? AND mov.situacao = ? AND mov.empresa = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, cliente);
            ps.setString(2, situacao);
            ps.setInt(3, empresa);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() && rs.getInt("codigo") > 0;
            }
        }
    }

    public boolean existeParcelaVencidaEmSituacao(Integer cliente, String situacao, Integer empresa) throws  Exception{
        String sql = "SELECT mov.codigo FROM movparcela mov INNER JOIN cliente cli ON cli.pessoa = mov.pessoa WHERE cli.codigo = ? AND mov.situacao = ? AND mov.empresa = ? AND mov.datavencimento <= ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, cliente);
            ps.setString(2, situacao);
            ps.setInt(3, empresa);
            ps.setTimestamp(4, Uteis.getDataHoraJDBC(Calendario.hoje(), "23:59:59"));
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() && rs.getInt("codigo") > 0;
            }
        }
    }

    public boolean existeParcelaVencidaBoletoGerado(Integer cliente, String situacao, Integer empresa) throws  Exception {
        String sql = "SELECT * FROM movparcela mov INNER JOIN cliente cli ON cli.pessoa = mov.pessoa WHERE cli.codigo = ? AND mov.situacao = ? AND mov.empresa = ? AND mov.datavencimento <= ?";
        List<MovParcelaVO> listaParcelasAberto = new ArrayList<>();
        try (PreparedStatement ps = con.prepareStatement(sql, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY)) {
            ps.setInt(1, cliente);
            ps.setString(2, situacao);
            ps.setInt(3, empresa);
            ps.setTimestamp(4, Uteis.getDataHoraJDBC(Calendario.hoje(), "23:59:59"));
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    rs.beforeFirst();
                    listaParcelasAberto = montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                }
            }
        }
        for (MovParcelaVO movParcelaVO : listaParcelasAberto) {
            if (parcelaEstaBloqueadaPorBoletoPendente(movParcelaVO, false)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<String> consultaTiposProdutosMovParcela(final int codMovParcela) throws Exception {
        List<String> lista = new ArrayList<String>();
        StringBuilder sb = new StringBuilder("select distinct p.tipoproduto from movparcela mp ");
        sb.append("inner join movprodutoparcela mpr on mpr.movparcela = mp.codigo ");
        sb.append("inner join movproduto mprod on mprod.codigo = mpr.movproduto ");
        sb.append("inner join produto p on p.codigo = mprod.produto ");
        sb.append("where mp.codigo = ").append(codMovParcela);

        try (ResultSet rs = criarConsulta(sb.toString(), con)) {
            while (rs.next()) {
                lista.add(rs.getString("tipoproduto"));
            }
        }

        return lista;
    }

    public void incrementarNrTentativasParcelaConvenio(RemessaItemVO remessaItemVO, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        if (!UteisValidacao.emptyNumber(remessaItemVO.getMovParcela().getCodigo())) {
            incrementarNrTentativasParcelaConvenio(remessaItemVO.getMovParcela(), convenioCobrancaVO);
        } else if (!UteisValidacao.emptyList(remessaItemVO.getMovParcelas())) {
            for (RemessaItemMovParcelaVO itemVO : remessaItemVO.getMovParcelas()) {
                incrementarNrTentativasParcelaConvenio(itemVO.getMovParcelaVO(), convenioCobrancaVO);
            }
        }
    }

    public void incrementarNrTentativasParcelaConvenio(MovParcelaVO movParcela, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        movParcela.setNrTentativas(movParcela.getNrTentativas() + 1);
        executarConsulta("update movparcela set nrtentativas = " + movParcela.getNrTentativas() + " where codigo = " + movParcela.getCodigo(), con);
        MovParcelaTentativaConvenio movParcelaTentativaConvenio = new MovParcelaTentativaConvenio(con);
        movParcelaTentativaConvenio.incluirNrTentativaParcela(movParcela, convenioCobrancaVO);
        movParcelaTentativaConvenio = null;
    }

    public void incrementarNrTentativasProcessoRetentativa(int codMovParcela) throws Exception {
        executarConsulta("update movparcela set nrtentativasprocessoretentativa  = (nrtentativasprocessoretentativa + 1) where codigo = " + codMovParcela, con);
    }

    /**
     * Consulta lista de parcelas que j? foram geradas remessas, possuem retorno
     * processado, por?m, continuam "Em aberto" e n?o est?o em outra remessa
     * aguardando retorno ser processado.
     *
     * @param convenio
     * @param inicio
     * @param fim
     * @return
     * @throws Exception
     */
    @Override
    public List<MovParcelaVO> consultarParcelasParaRemessaRepescagem(ConvenioCobrancaVO convenio, final Date inicio, final Date fim,
            final Date inicioCobranca, final Date fimCobranca,
            Integer codigoEmpresa, Integer plano) throws Exception {
        try (ResultSet rs = getResultSetParcelasParaRemessaRepescagem(convenio, inicio, fim, inicioCobranca, fimCobranca, false, codigoEmpresa, plano)) {
            return MovParcela.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);
        }
    }

    public ResultSet getResultSetParcelasParaRemessaRepescagem(ConvenioCobrancaVO convenio, Date inicio, Date fim,
            Date inicioCobranca, Date fimCobranca, boolean count,
            Integer codigoEmpresa, Integer plano) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select\n");
        if (!count) {
            sb.append("movparcela.*\n");
        } else {
            sb.append("count(movparcela.codigo) as qtd,\n");
            sb.append("sum(valorparcela :: NUMERIC) as total\n");
        }
        sb.append("from movparcela\n");
        if (!UteisValidacao.emptyNumber(plano)) {
            sb.append("inner join contrato on contrato.codigo = movparcela.contrato and contrato.plano = ").append(plano).append("\n");
        }

        sb.append("where (");
        sb.append("(movparcela.codigo in (select coalesce(movparcela,0) from remessaitem where remessa in (");
        sb.append("select codigo from remessa where situacaoremessa=2");
        if (convenio != null && convenio.getCodigo() > 0) {
            sb.append(" and conveniocobranca = ? ");
        }
        sb.append(" )))");
        sb.append(" OR nrtentativas > 0");
        sb.append(")");
        sb.append("AND (movparcela.pessoa in (select pessoa from cliente where codigo in( ");
        sb.append("select cliente from autorizacaocobrancacliente ");
        if (convenio != null && convenio.getCodigo() > 0) {
            sb.append("where conveniocobranca = ? ");
        }
        sb.append("))) ");
        sb.append("AND (movparcela.codigo not in (select coalesce(movparcela,0) from remessaitem where remessa in (");
        sb.append("select codigo from remessa where situacaoremessa in (1,4))))");

        sb.append("AND ( movparcela.situacao = 'EA'  ");
        if (inicio != null) {
            sb.append("and datavencimento >= ? ");
        }
        if (fim != null) {
            sb.append("and datavencimento <= ? ");
        }
        if (inicioCobranca != null && fimCobranca != null) {
            sb.append(" and datacobranca between ? and ? ");
        }

        if (codigoEmpresa != null && codigoEmpresa > 0) {
            sb.append(" AND movparcela.empresa = ?");
        }

        sb.append(" and valorParcela > 0)\n");
        if (!count) {
            sb.append("order by datavencimento");
        }

        PreparedStatement stm = con.prepareStatement(sb.toString());
        int i = 1;
        if (convenio != null && convenio.getCodigo() > 0) {
            stm.setInt(i++, convenio.getCodigo());
            stm.setInt(i++, convenio.getCodigo());
        }
        if (inicio != null) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(inicio));
        }
        if (fim != null) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(fim));
        }
        if (inicioCobranca != null && fimCobranca != null) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(inicioCobranca));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(fimCobranca));
        }

        if (codigoEmpresa != null && codigoEmpresa > 0) {
            stm.setInt(i++, codigoEmpresa);
        }

        return stm.executeQuery();
    }

    /**
     * Consulta lista de parcelas que possuem autoriza??o de cobran?a da pessoa
     * relacionada, que estejam 'Em Aberto', que n?o possuam nenhuma tentativa
     * de cobran?a e n?o est?o em outra remessa aguardando retorno ser
     * processado ou tenha sido enviada.
     *
     * @throws Exception
     */
    @Override
    public List<MovParcelaVO> consultarParcelasComAutorizacaoCobranca(ConvenioCobrancaVO convenio,
            final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca, Integer codigoEmpresa, Integer plano) throws Exception {
        try (ResultSet rs = getResultSetParcelasComAutorizacaoCobranca(convenio, inicio, fim, inicioCobranca, fimCobranca, false, codigoEmpresa, plano)) {
            return MovParcela.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);
        }
    }

    public ResultSet getResultSetParcelasComAutorizacaoCobranca(ConvenioCobrancaVO convenio, Date inicio, Date fim,
            Date inicioCobranca, Date fimCobranca, boolean count,
            Integer codigoEmpresa, Integer plano) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select\n");
        if (!count) {
            sb.append("movparcela.*\n");
        } else {
            sb.append("count(movparcela.codigo) as qtd,\n");
            sb.append("sum(valorparcela :: NUMERIC) as total\n");
        }
        sb.append("from movparcela\n");
        if (!UteisValidacao.emptyNumber(plano)) {
            sb.append(" inner join contrato on contrato.codigo = movparcela.contrato and contrato.plano = ").append(plano).append("\n");
        }
        sb.append("where movparcela.pessoa in (select pessoa from cliente where codigo in( ");
        sb.append("select cliente from autorizacaocobrancacliente ");
        if (convenio != null && convenio.getCodigo() > 0) {
            sb.append(" where conveniocobranca = ?");
        }
        sb.append(")) ");
        sb.append("AND (movparcela.codigo not in (select coalesce(movparcela,0) from remessaitem where remessa ");
        sb.append("in (select codigo from remessa where situacaoremessa in (1,4))))");
        if (inicio != null) {
            sb.append("and datavencimento >= ? ");
        }
        if (fim != null) {
            sb.append("and datavencimento <= ? ");
        }
        if (inicioCobranca != null && fimCobranca != null) {
            sb.append("and datacobranca between ? and ? ");
        }
        sb.append(" and movparcela.situacao = 'EA' ");
        sb.append(" and nrtentativas = 0 and valorParcela > 0\n");

        if (codigoEmpresa != null && codigoEmpresa > 0) {
            sb.append(" AND movparcela.empresa = ?\n");
        }

        if (!count) {
            sb.append("order by datavencimento");
        }

        PreparedStatement stm = con.prepareStatement(sb.toString());
        int i = 1;
        if (convenio != null && convenio.getCodigo() > 0) {
            stm.setInt(i++, convenio.getCodigo());
        }
        if (inicio != null) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(inicio));
        }
        if (fim != null) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(fim));
        }

        if (inicioCobranca != null && fimCobranca != null) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(inicioCobranca));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(fimCobranca));
        }

        if (codigoEmpresa != null && codigoEmpresa > 0) {
            stm.setInt(i++, codigoEmpresa);
        }

        return stm.executeQuery();
    }

    public List<MovParcelaVO> consultarParcelasEventoPorRecibo(Integer codigoRecibo) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("select distinct mpa.* from recibopagamento rp \n").
                append("inner join movpagamento mp on mp.recibopagamento = rp.codigo \n").
                append("inner join pagamentomovparcela pmp on pmp.movpagamento = mp.codigo \n").
                append("inner join negociacaoeventocontratoparcelas necp on pmp.movparcela = necp.parcela \n").
                append("inner join movparcela mpa on mpa.codigo = necp.parcela \n").
                append("inner join negociacaoeventocontrato nec on nec.codigo = necp.contrato \n").
                append("WHERE rp.codigo = " + codigoRecibo).append(" order by codigo");
        try (ResultSet rs = criarConsulta(sb.toString(), con)) {
            return MovParcela.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
    }

    public String consultarJSON(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            MovParcelaVO movParcela = new MovParcelaVO();
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(rs.getString("cliente").trim().replaceAll("\"", "\'")).append("\",");
                json.append("\"").append(rs.getString("responsavel").trim().replaceAll("\"", "\'")).append("\",");
                json.append("\"").append(rs.getDate("dataregistro")).append("\",");
                json.append("\"").append(rs.getDate("datavencimento")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\",");
                movParcela.setSituacao(rs.getString("situacao"));
                json.append("\"").append(movParcela.getSituacao_Apresentar()).append("\",");
                json.append("\"").append(Formatador.formatarValorMonetario(rs.getDouble("valorparcela"))).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar) throws Exception {
        StringBuilder sqlCount = new StringBuilder("SELECT count(codigo) FROM movparcela rp");
        if (empresa != 0) {
            sqlCount.append("  WHERE rp.empresa = ").append(empresa).append("\n");
        }

        StringBuilder sql = new StringBuilder("SELECT mp.codigo, pe.nome AS cliente, usr.username AS responsavel, \n");
        sql.append("mp.dataregistro, mp.datavencimento, emp.nome AS empresa, mp.situacao, mp.valorparcela\n");
        sql.append("FROM movparcela mp\n");
        sql.append("  INNER JOIN pessoa pe ON mp.pessoa = pe.codigo\n");
        sql.append("  INNER JOIN empresa emp ON mp.empresa = emp.codigo\n");
        sql.append("  INNER JOIN usuario usr ON mp.responsavel = usr.codigo\n");
        sql.append(" WHERE 1 = 1\n");
        if (empresa != 0) {
            sql.append("  AND mp.empresa = ").append(empresa).append("\n");
        }

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append(" AND (");
            sql.append("lower(mp.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(pe.nome) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(mp.dataregistro::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(mp.datavencimento::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(usr.username) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(emp.nome) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(mp.situacao) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(mp.valorparcela::VARCHAR) ~ '").append(clausulaLike).append("'\n");
            sql.append(")");
        }
        sql.append("  ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
        if (limit > 0) {
            sql.append(" limit ").append(limit).append("\n");
        }
        sql.append(" offset ").append(offset).append("\n");

        StringBuilder sqlContarFiltrados = new StringBuilder("SELECT count(mp.codigo) \n");
        sqlContarFiltrados.append("FROM movparcela mp\n");
        sqlContarFiltrados.append("  INNER JOIN pessoa pe ON mp.pessoa = pe.codigo\n");
        sqlContarFiltrados.append("  INNER JOIN empresa emp ON mp.empresa = emp.codigo\n");
        sqlContarFiltrados.append("  INNER JOIN usuario usr ON mp.responsavel = usr.codigo\n");
        sqlContarFiltrados.append(" WHERE 1 = 1\n");

        if (empresa != 0) {
            sqlContarFiltrados.append("  AND mp.empresa = ").append(empresa).append("\n");
        }

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sqlContarFiltrados.append(" AND (");
            sqlContarFiltrados.append("lower(mp.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(pe.nome) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(mp.dataregistro::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(mp.datavencimento::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(usr.username) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(emp.nome) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(mp.situacao) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(mp.valorparcela::VARCHAR) ~ '").append(clausulaLike).append("'\n");
            sqlContarFiltrados.append(")");
        }

        StringBuilder json;
        boolean dados;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                MovParcelaVO movParcela = new MovParcelaVO();
                json = new StringBuilder();
                json.append("{");
                json.append("\"iTotalRecords\":\"").append(contar(sqlCount.toString(), getCon())).append("\",");
                json.append("\"iTotalDisplayRecords\":\"").append(contar(sqlContarFiltrados.toString(), getCon())).append("\",");
                json.append("\"sEcho\":\"").append(sEcho).append("\",");
                json.append("\"aaData\":[");
                dados = false;
                while (rs.next()) {
                    dados = true;
                    json.append("[\"").append(rs.getString("codigo")).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("cliente"))).append("\",");
                    json.append("\"").append(rs.getString("responsavel").trim().replaceAll("\"", "\'")).append("\",");
                    json.append("\"").append(rs.getDate("dataregistro")).append("\",");
                    json.append("\"").append(rs.getDate("datavencimento")).append("\",");
                    json.append("\"").append(rs.getString("empresa")).append("\",");
                    movParcela.setSituacao(rs.getString("situacao"));
                    json.append("\"").append(movParcela.getSituacao_Apresentar()).append("\",");
                    json.append("\"").append(Formatador.formatarValorMonetario(rs.getDouble("valorparcela"))).append("\"],");

                }
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT mp.codigo, pe.nome AS cliente, usr.username AS responsavel, \n" + "mp.dataregistro, mp.datavencimento, emp.nome AS empresa, mp.situacao, mp.valorparcela\n" + "FROM movparcela mp\n" + "  INNER JOIN pessoa pe ON mp.pessoa = pe.codigo\n" + "  INNER JOIN empresa emp ON mp.empresa = emp.codigo\n" + "  INNER JOIN usuario usr ON mp.responsavel = usr.codigo\n");
        if (empresa != 0) {
            sql.append("  WHERE mp.empresa = ?");
        }
        sql.append("  ORDER BY mp.codigo DESC");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        return sqlConsultar;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                MovParcelaVO mParcela = new MovParcelaVO();
                String geral = rs.getString("codigo") + rs.getString("cliente") + rs.getString("responsavel") + rs.getString("dataregistro") + rs.getString("datavencimento") + rs.getString("empresa") + rs.getString("situacao") + rs.getString("valorparcela");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    mParcela.setCodigo(rs.getInt("codigo"));
                    mParcela.getContrato().getPessoa().setNome(rs.getString("cliente"));
                    mParcela.setUsuarioVO(new UsuarioVO());
                    mParcela.getUsuarioVO().setNome(rs.getString("responsavel"));
                    mParcela.setDataRegistro(rs.getDate("dataregistro"));
                    mParcela.setDataVencimento(rs.getDate("datavencimento"));
                    mParcela.getEmpresa().setNome(rs.getString("empresa"));
                    mParcela.setSituacao(rs.getString("situacao"));
                    mParcela.setValorParcela(rs.getDouble("valorparcela"));
                    lista.add(mParcela);
                }
            }
        }
        if (campoOrdenacao.equals("C?digo")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Cliente")) {
            Ordenacao.ordenarLista(lista, "contratoPessoa_Apresentar");
        } else if (campoOrdenacao.equals("Respons?vel")) {
            Ordenacao.ordenarLista(lista, "responsavel_Apresentar");
        } else if (campoOrdenacao.equals("Registro")) {
            Ordenacao.ordenarLista(lista, "dataRegistro");
        } else if (campoOrdenacao.equals("Vencimento")) {
            Ordenacao.ordenarLista(lista, "dataVencimento");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        } else if (campoOrdenacao.equals("Situa??o")) {
            Ordenacao.ordenarLista(lista, "situacao_Apresentar");
        } else if (campoOrdenacao.equals("Valor")) {
            Ordenacao.ordenarLista(lista, "valorParcela_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    /**
     * Respons?vel por realizar uma consulta de
     * <code>MovParcela</code> atrav?s do valor do atributo
     * <code>String situacao</code>. Retorna os objetos, com in?cio do valor do
     * atributo id?ntico ao par?metro fornecido. Faz uso da opera??o
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo v?rios objetos da classe <code>MovParcelaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conex?o ou restri??o de
     * acesso.
     */
    public Double consultarPorAluno(Integer codPessoa) throws Exception {
        String sqlStr = "SELECT"
                + "    SUM(mov.valorparcela) AS total"
                + " FROM MovParcela mov\n"
                + "   INNER JOIN pessoa p on p.codigo = mov.pessoa \n"
                + " WHERE SITUACAO = 'EA'\n"
                + "   AND mov.pessoa = ? ";
        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            stm.setInt(1, codPessoa);
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getDouble("total");
                }
            }
        }
        return 0.0;
    }

    public List<MovParcelaVO> consultarPorAluno(Integer codPessoa,int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT"
                + "    mov.* total"
                + " FROM MovParcela mov\n"
                + "   INNER JOIN pessoa p on p.codigo = mov.pessoa \n"
                + " WHERE SITUACAO = 'EA'\n"
                + "   AND mov.pessoa = ? ";
        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            stm.setInt(1, codPessoa);
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public MovParcelaVO consultarPorNumeroContrato(Integer numero, Integer contrato) throws Exception {
        String sqlStr = "select mp.* from movparcela mp \n"
                + "INNER JOIN contrato ON contrato.codigo = mp.contrato\n"
                + "WHERE (mp.descricao LIKE ? or mp.descricao LIKE ?) \n"
                + "and contrato = ?";
        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            stm.setString(1, "PARCELA " + numero);
            stm.setString(2, "PARCELA " + numero + " (TRANSFERIDA%");
            stm.setInt(3, contrato);
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                } else {
                    return null;
                }
            }
        }


    }

    public List<MovParcelaVO> consultarPorCodigoAulaAvulsaDiariaLista(Integer codigoVenda,final String situacao, boolean controlarAcesso,
                                                                      Date dataInicioVencimento, Date dataVencimento, int nivelMontarDados)
            throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM MovParcela WHERE AulaAvulsaDiaria =").append(codigoVenda);
        if (!UteisValidacao.emptyString(situacao)){
            sqlStr.append(" and situacao='").append(situacao).append("'\n");
        }
        if (dataInicioVencimento != null) {
            sqlStr.append(" AND datavencimento >= '").append(Uteis.getDataJDBC(dataInicioVencimento)).append("'\n");
        }
        if (dataVencimento != null) {
            sqlStr.append(" AND datavencimento <= '").append(Uteis.getDataJDBC(dataVencimento)).append("'\n");
        }

        sqlStr.append(" ORDER BY movparcela.datavencimento");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public MovParcelaVO consultarParcelaEmAbertoMaisAntiga(Integer codigoPessoa) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT * FROM movparcela WHERE pessoa = " + codigoPessoa
                + " and situacao like 'EA' ORDER BY datavencimento LIMIT 1", con)) {
            if (rs.next()) {
                return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
        }
        return null;


    }


    public List<MovParcelaVO> consultarPorContratoParcelasEmRemessa(String contrato) throws Exception {
        try{
            StringBuilder sqlStr = new StringBuilder("select remessa.codigo remessa, remessa.dataregistro dtgeracao, remessa.dthrinicio dtenvio, movparcela.codigo codmovparcela, movparcela.descricao descricao,");
            sqlStr.append("movparcela.valorparcela valor, movparcela.datavencimento dtvencimento, movparcela.situacao situacao ");
            sqlStr.append("from movparcela ");
            sqlStr.append("inner join remessaitem on remessaitem.movparcela = movparcela.codigo ");
            sqlStr.append("inner join remessa on remessa.codigo = remessaitem.remessa and (remessa.situacaoremessa = 1 or remessa.situacaoremessa = 4) ");
            sqlStr.append("where contrato in (").append(contrato).append(")");
            List<MovParcelaVO> vetResultado;
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                    vetResultado = new ArrayList<MovParcelaVO>(0);
                    while (tabelaResultado.next()) {
                        MovParcelaVO obj = new MovParcelaVO();
                        obj.setRemessa(tabelaResultado.getInt("remessa"));
                        obj.setDtGeracao(tabelaResultado.getDate("dtgeracao"));
                        obj.setDtEnvio(tabelaResultado.getDate("dtenvio"));
                        obj.setCodigo(tabelaResultado.getInt("codmovparcela"));
                        obj.setDescricao(tabelaResultado.getString("descricao"));
                        obj.setValorParcela(tabelaResultado.getDouble("valor"));
                        obj.setDataVencimento(tabelaResultado.getDate("dtvencimento"));
                        obj.setSituacao(tabelaResultado.getString("situacao"));
                        vetResultado.add(obj);
                    }
                }
            }
            return vetResultado;
        }catch (Exception e) {
            throw e;
        }
    }

    public List<MovParcelaVO> consultarParcelasDCC(ConvenioCobrancaVO convenio, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                                   List<Integer> empresas, Integer plano, Boolean repescagem,boolean somenteParcelaRemessa, boolean somenteParcelasSemBoletoGerado,
                                                   Integer modalidade, boolean consultaRemessa, boolean apresentarAlunosBloqueioCobranca, PaginadorDTO paginadorDTO) throws Exception {
        boolean ehRemessaBoleto = convenio.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO);
        List<Integer> convenios = !convenio.getCodigo().equals(0) ? Arrays.asList(convenio.getCodigo()) : null;
        try (ResultSet rs = getRsParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, convenios, empresas, repescagem, false, false, ehRemessaBoleto, somenteParcelaRemessa,
                somenteParcelasSemBoletoGerado, modalidade, consultaRemessa, apresentarAlunosBloqueioCobranca, paginadorDTO)) {
            return MovParcela.montarDadosConsulta(rs, ehRemessaBoleto ? Uteis.NIVELMONTARDADOS_GESTAOREMESSABOLETO : Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);
        }
    }

    public List<MovParcelaVO> consultarParcelasDCCResumido(List<Integer> convenios, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                                           List<Integer> empresas, Integer plano, Boolean repescagem, boolean somenteParcelaRemessa,
                                                           boolean somenteParcelasSemBoletoGerado, Integer modalidade, boolean consultaRemessa, boolean apresentarAlunosBloqueioCobranca) throws Exception {
        try (ResultSet rs = getRsParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, convenios, empresas, repescagem, false, false, false, somenteParcelaRemessa,
                somenteParcelasSemBoletoGerado, modalidade, consultaRemessa, apresentarAlunosBloqueioCobranca, null)) {
            return MovParcela.montarDadosConsultaResumido(rs, Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);
        }
    }

    public List<MovParcelaVO> consultarParcelasDCCBIParcelasEmAberto(ConvenioCobrancaVO convenio, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                                                     List<Integer> empresas, Integer plano, Boolean repescagem,boolean somenteParcelaRemessa, boolean somenteParcelasSemBoletoGerado,
                                                                     Integer modalidade, boolean consultaRemessa, boolean apresentarAlunosBloqueioCobranca) throws Exception {
        boolean ehRemessaBoleto = convenio.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO);
        List<Integer> convenios = !convenio.getCodigo().equals(0) ? Arrays.asList(convenio.getCodigo()) : null;
        try (ResultSet rs = getRsParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, convenios, empresas, repescagem, false, false, ehRemessaBoleto,
                somenteParcelaRemessa, somenteParcelasSemBoletoGerado, modalidade, consultaRemessa, apresentarAlunosBloqueioCobranca, null)) {
            return MovParcela.montarDadosConsulta(rs, ehRemessaBoleto ? Uteis.NIVELMONTARDADOS_GESTAOREMESSABOLETO : Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);
        }
    }

    public List<MovParcelaTransacaoTO> consultarParcelasDCCBIParcelasEmAbertoResumido(List<Integer> convenios, List<Integer> colaboradores, final Date inicio, final Date fim,
                                                                                      final Date inicioCobranca, final Date fimCobranca, List<Integer> empresas, Integer plano, Boolean repescagem,
                                                                                      boolean somenteParcelaRemessa, boolean somenteParcelasSemBoletoGerado, Integer modalidade, boolean consultaRemessa, boolean apresentarAlunosBloqueioCobranca) throws Exception {
        try (ResultSet rs = getRsParcelasDCCBIParcelasEmAberto(plano, inicio, fim, inicioCobranca, fimCobranca, convenios, colaboradores, empresas, repescagem, false, false, false, somenteParcelaRemessa, somenteParcelasSemBoletoGerado, modalidade, consultaRemessa)) {
            return MovParcela.montarDadosConsultaResumido(rs, con);
        }
    }


    public List<MovParcelaVO> consultarParcelasDCC(List<Integer> convenios, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                                   List<Integer> empresas, Integer plano, Boolean repescagem, boolean somenteParcelaRemessa, boolean somenteParcelasSemBoletoGerado,
                                                   Integer modalidade, boolean consultaRemessa, boolean apresentarAlunosBloqueioCobranca) throws Exception {
        try (ResultSet rs = getRsParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, convenios, empresas, repescagem, false, false, false,
                somenteParcelaRemessa, somenteParcelasSemBoletoGerado, modalidade, consultaRemessa, apresentarAlunosBloqueioCobranca, null)) {
            return MovParcela.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);
        }
    }
    public ResultSet consultarParcelasDCC(Integer codigoAutorizacao,int clienteAutorizacao) throws Exception{
        return  getRsParcelasDCC(codigoAutorizacao,clienteAutorizacao);
    }

    public Integer consultarParcelasDCCCount(ConvenioCobrancaVO convenio, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                             Integer codigoEmpresa, Integer plano, boolean somenteParcelasSemBoletoGerado, Integer modalidade, boolean apresentarAlunosBloqueioCobranca) throws Exception {
        boolean ehRemessaBoleto = convenio.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO);
        List<Integer> convenios = !convenio.getCodigo().equals(0) ? Arrays.asList(convenio.getCodigo()) : null;
        List<Integer> empresas = obterEmpresasConvenio(convenios);
        try (ResultSet rs = getRsParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, convenios, empresas, false, true, false,
                ehRemessaBoleto, false, somenteParcelasSemBoletoGerado, modalidade, true, apresentarAlunosBloqueioCobranca, null)) {
            rs.next();
            return rs.getInt("total");
        }
    }

    public Integer consultarParcelasDCCCount(Integer codigoEmpresa, final Date fim, List<Integer> convenios, List<Integer> colaboradores) throws Exception {
        List<Integer> empresas = new ArrayList<Integer>();
        if(UteisValidacao.emptyList(convenios)){
            empresas.add(codigoEmpresa);
        } else {
            empresas = obterEmpresasConvenio(convenios);
        }
        Integer count;
        try (ResultSet rs = getRsParcelasDCCBIParcelasEmAberto(null, null, fim, null, null, convenios, colaboradores,
                empresas, true, true, false, false, false, false, null, false)) {
            rs.next();
            count = rs.getInt("total");
        }
       /* ResultSet rs2 = getRsParcelasDCC(null, null, fim, null, null, convenios,
                empresas, false, true, false, false,false, false, null, false);
        rs2.next();
        count +=  rs2.getInt("total");*/
        return count;
    }

    @Override
    public Double consultarParcelasDCCValor(ConvenioCobrancaVO convenio, final Date inicio, final Date fim,
                                             final Date inicioCobranca, final Date fimCobranca, Integer codigoEmpresa, Integer plano,
                                            boolean somenteParcelasSemBoletoGerado, Integer modalidade, boolean apresentarAlunosBloqueioCobranca) throws Exception {
        boolean ehRemessaBoleto = convenio.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO);
        List<Integer> convenios = !convenio.getCodigo().equals(0) ? Arrays.asList(convenio.getCodigo()) : null;
        List<Integer> empresas = obterEmpresasConvenio(convenios);
        try (ResultSet rs = getRsParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, convenios, empresas, false, false, true, ehRemessaBoleto,
                false, somenteParcelasSemBoletoGerado, modalidade, true, apresentarAlunosBloqueioCobranca, null)) {
            rs.next();
            return rs.getDouble("valor");
        }
    }

    public Double consultarParcelasDCCValor(Integer codigoEmpresa, final Date fim, List<Integer> convenios, List<Integer> colaboradores) throws Exception {
        List<Integer> empresas = new ArrayList<Integer>();
        if(UteisValidacao.emptyList(convenios)){
            empresas.add(codigoEmpresa);
        } else {
            empresas = obterEmpresasConvenio(convenios);
        }
        Double valor;
        try (ResultSet rs = getRsParcelasDCCBIParcelasEmAberto(null, null, fim, null, null, convenios, colaboradores,
                empresas, true, false, true, false, false, false, null, false)) {
            rs.next();
            valor = rs.getDouble("valor");
        }

       /* ResultSet rs2 = getRsParcelasDCC(null, null, fim, null, null,convenios,
                empresas, false,  false, true, false,false, false, null,false);
        rs2.next();
        valor += rs2.getDouble("valor");*/
        return valor;
    }

    public void atualizarParcelaDCC(Integer movparcela, Integer pessoa, Integer codigoCliente, Integer codigoColaborador) throws  Exception{
        StringBuilder sql = new StringBuilder();
        if(UteisValidacao.emptyNumber(pessoa)){
            pessoa = descobrirCodigoPessoa(codigoCliente, codigoColaborador, movparcela);
        }
        if(UteisValidacao.emptyNumber(pessoa)){
            return;
        }

        sql.append("UPDATE movparcela SET parcelaDCC = codigo IN (\n");
        sql.append("SELECT mp.codigo\n");
        sql.append("FROM movparcela mp\n");
        sql.append("INNER JOIN (\n");
        sql.append("SELECT tipoacobrar, cli.pessoa, listaobjetosacobrar\n");
        sql.append("FROM autorizacaocobrancacliente au\n");
        sql.append("INNER JOIN cliente cli ON cli.codigo = au.cliente and au.ativa \n");
        sql.append("WHERE cli.pessoa = ").append(pessoa).append("\n");
        sql.append("UNION\n");
        sql.append("SELECT tipoacobrar, co.pessoa, listaobjetosacobrar\n");
        sql.append("FROM autorizacaocobrancacolaborador au\n");
        sql.append("INNER JOIN colaborador co ON co.codigo = au.colaborador\n");
        sql.append("WHERE co.pessoa = ").append(pessoa).append("\n");
        sql.append(" )acc ON acc.pessoa = mp.pessoa\n");
        sql.append(" WHERE \n");
        sql.append(" mp.valorParcela > 0 \n");
        sql.append(" AND mp.situacao = 'EA'\n");
        if(movparcela != null){
            sql.append("AND mp.codigo = ").append(movparcela).append("\n");
        }
        sql.append(" AND(  \n");
        sql.append(" (tipoacobrar = 1)  \n");
        String tiposPlano = "";
        for(String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoPlano){
            tiposPlano += ",'"+tipo+"'";
        }
        sql.append(" OR (acc.tipoacobrar = 2 AND (SELECT COUNT(*) FROM movproduto mpd  \n");
        sql.append(" 			INNER JOIN movprodutoparcela mpp ON mpd.codigo = mpp.movproduto AND mpp.movparcela = mp.codigo  \n");
        sql.append(" 			INNER JOIN produto p ON p.codigo = mpd.produto AND p.tipoproduto IN ( ");
        sql.append(tiposPlano.replaceFirst(",", "")).append(")) > 0) 	 \n");
        sql.append(" OR (tipoacobrar = 3 AND (SELECT COUNT(*) FROM movproduto mpd  \n");
        sql.append(" 			INNER JOIN movprodutoparcela mpp ON mpd.codigo = mpp.movproduto AND mpp.movparcela = mp.codigo  \n");
        sql.append(" 			INNER JOIN produto p ON p.codigo = mpd.produto ");
        sql.append("                    AND p.tipoproduto = ANY (('{'||acc.listaobjetosacobrar||'}') :: text[])) > 0)  \n");
        String tiposContrato = "";
        for(String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoContrato){
            tiposContrato += ",'"+tipo+"'";
        }
        sql.append(" OR (tipoacobrar = 4 AND (SELECT COUNT(*) FROM movproduto mpd  \n");
        sql.append(" 			INNER JOIN movprodutoparcela mpp ON mpd.codigo = mpp.movproduto AND mpp.movparcela = mp.codigo  \n");
        sql.append(" 			INNER JOIN produto p ON p.codigo = mpd.produto AND p.tipoproduto IN ( ");
        sql.append(tiposContrato.replaceFirst(",", "")).append(")) > 0 ");
        sql.append(" AND ( (SELECT EXISTS(SELECT * FROM contratorecorrencia WHERE contrato = mp.contrato)) OR (SELECT EXISTS(SELECT 1 FROM contrato WHERE codigo = mp.contrato AND renovavelAutomaticamente = 't'))))) 	 \n");

        sql.append(")");
        sql.append(" WHERE valorParcela > 0 \n");
        sql.append(" AND situacao = 'EA'\n");
        sql.append(" AND pessoa = ").append(pessoa).append("\n");
        if(movparcela != null){
            sql.append("AND codigo = ").append(movparcela).append("\n");
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.executeUpdate();
        }
    }

    private Integer descobrirCodigoPessoa(Integer codigoCliente, Integer codigoColaborador, Integer movparcela) throws Exception {
        Integer codigoPessoa = null;
        if (!UteisValidacao.emptyNumber(codigoCliente)) {
            codigoPessoa = findPessoaCliente(codigoCliente);
        } else {
            if (!UteisValidacao.emptyNumber(codigoColaborador)) {
                codigoPessoa = findPessoaColaborador(codigoColaborador);
            } else {
                codigoPessoa = findPessoaMovParcela(movparcela);
            }
        }
        return codigoPessoa;
    }

    private Integer findPessoaCliente(Integer cliente) throws  Exception{
        String sql = "SELECT pessoa FROM cliente WHERE codigo = " + cliente;
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() ? rs.getInt("pessoa") : null;
        }
    }

    private Integer findPessoaColaborador(Integer colaborador) throws  Exception{
        String sql = "SELECT pessoa FROM colaborador WHERE codigo = " + colaborador;
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() ? rs.getInt("pessoa") : null;
        }
    }

    private Integer findPessoaMovParcela(Integer movparcela) throws  Exception{
        String sql = "SELECT pessoa FROM movparcela WHERE codigo = " + movparcela;
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() ? rs.getInt("pessoa") : null;
        }
    }

    public ResultSet getRsParcelasDCC(Integer codigoAutorizacao,int clienteAutorizacao) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT DISTINCT mp.*,r.codigo as codigoRemessa FROM autorizacaocobrancacliente acc \n");
        sql.append(" INNER JOIN cliente cli ON acc.cliente = cli.codigo \n");
        sql.append(" INNER JOIN pessoa pes ON cli.pessoa = pes.codigo \n");
        sql.append(" INNER JOIN movparcela mp ON mp.pessoa = pes.codigo \n");
            sql.append(" LEFT JOIN remessaitem ri ON ri.movparcela = mp.codigo \n");
            sql.append(" LEFT JOIN remessa r ON r.codigo = ri.remessa \n");

        sql.append(" WHERE \n");
            sql.append(" mp.valorParcela > 0 \n");
            sql.append(" AND mp.situacao = 'EA'\n");
            sql.append(" AND acc.codigo = ? \n");
            sql.append(" AND acc.cliente  = ?\n");
        sql.append(" AND(  \n");
        sql.append(" (tipoacobrar = 1)  \n");
        String tiposPlano = "";
        for(String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoPlano){
            tiposPlano += ",'"+tipo+"'";
        }
        sql.append(" OR (tipoacobrar = 2 AND (SELECT COUNT(*) FROM movproduto mpd  \n");
        sql.append(" 			INNER JOIN movprodutoparcela mpp ON mpd.codigo = mpp.movproduto AND mpp.movparcela = mp.codigo  \n");
        sql.append(" 			INNER JOIN produto p ON p.codigo = mpd.produto AND p.tipoproduto IN ( ");
        sql.append(tiposPlano.replaceFirst(",", "")).append(")) > 0) 	 \n");
        sql.append(" OR (tipoacobrar = 3 AND (SELECT COUNT(*) FROM movproduto mpd  \n");
        sql.append(" 			INNER JOIN movprodutoparcela mpp ON mpd.codigo = mpp.movproduto AND mpp.movparcela = mp.codigo  \n");
        sql.append(" 			INNER JOIN produto p ON p.codigo = mpd.produto ");
        sql.append("                    AND p.tipoproduto = ANY (('{'||acc.listaobjetosacobrar||'}') :: text[])) > 0)  \n");
        String tiposContrato = "";
        for(String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoContrato){
            tiposContrato += ",'"+tipo+"'";
        }
        sql.append(" OR (tipoacobrar = 4 AND (SELECT COUNT(*) FROM movproduto mpd  \n");
        sql.append(" 			INNER JOIN movprodutoparcela mpp ON mpd.codigo = mpp.movproduto AND mpp.movparcela = mp.codigo  \n");
        sql.append(" 			INNER JOIN produto p ON p.codigo = mpd.produto AND p.tipoproduto IN ( ");
        sql.append(tiposContrato.replaceFirst(",", "")).append(")) > 0 ");
        sql.append(" AND ( (SELECT EXISTS(SELECT * FROM contratorecorrencia WHERE contrato = mp.contrato)) OR (SELECT EXISTS(SELECT 1 FROM contrato WHERE codigo = mp.contrato AND renovavelAutomaticamente = 't'))))) 	 \n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 1;
        if (!UteisValidacao.emptyNumber(codigoAutorizacao)) {
            stm.setInt(i++, codigoAutorizacao);
        }else{
            stm.setNull(i++,0);
        }
        if(!UteisValidacao.emptyNumber(clienteAutorizacao)){
            stm.setInt(i++, clienteAutorizacao);
        }else{
            stm.setNull(i++,0);
        }
        ResultSet rs = stm.executeQuery();
        return rs;
    }
    public Boolean validarParcelaParaDCC(MovParcelaVO obj) throws Exception{

        if(UteisValidacao.emptyList(obj.getMovProdutoParcelaVOs())){
            if(!UteisValidacao.emptyNumber(obj.getCodigo())){
                MovProdutoParcela movProdParc = new MovProdutoParcela(con);
                obj.setMovProdutoParcelaVOs(movProdParc.consultarMovProdutoParcelasPorParcelas(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                movProdParc = null;
            }
            if(UteisValidacao.emptyList(obj.getMovProdutoParcelaVOs())){
               return  false;
            }
        }
        Integer codigoContrato = null;
        if(obj.getContrato() != null && obj.getContrato().getCodigo() != null && !obj.getContrato().getCodigo().equals(0)){
            codigoContrato = obj.getContrato().getCodigo();
        }
        try (ResultSet rs = consultarAutorizacaoCobranca(obj.getPessoa().getCodigo(), codigoContrato)) {
            String tipoPlanos = "";
            for (String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoPlano) {
                tipoPlanos += ",'" + tipo + "'";
            }
            String tipoContrato = "";
            for (String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoContrato) {
                tipoContrato += ",'" + tipo + "'";
            }
            String movProdutos = "";
            for (MovProdutoParcelaVO mpp : obj.getMovProdutoParcelaVOs()) {
                movProdutos += "," + mpp.getMovProdutoVO().getProduto().getCodigo() + "";
            }
            while (rs.next()) {
                TipoObjetosCobrarEnum tipoCobrar = TipoObjetosCobrarEnum.valueOf(rs.getInt("tipoacobrar"));
                String listaTiposProdutosDefinidos = rs.getString("listaobjetosacobrar");
                String tiposProdutosPreDef = "";
                if (!UteisValidacao.emptyString(listaTiposProdutosDefinidos)) {
                    for (String prod : listaTiposProdutosDefinidos.split(",")) {
                        tiposProdutosPreDef += ",'" + prod + "'";
                    }
                }
                if (tipoCobrar == TipoObjetosCobrarEnum.TUDO && obj.getSituacao().equals("EA")) {
                    return true;
                } else if (tipoCobrar == TipoObjetosCobrarEnum.APENAS_PLANOS) {
                    StringBuilder sql = new StringBuilder();
                    sql.append("SELECT COUNT(*) as qtd FROM Produto p\n");
                    sql.append("WHERE p.tipoproduto IN ( ");
                    sql.append(tipoPlanos.replaceFirst(",", "")).append(") 	 \n");
                    sql.append("AND p.codigo in(").append(movProdutos.replaceFirst(",", "")).append(")");
                    try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
                        try (ResultSet resultado = stm.executeQuery()) {
                            while (resultado.next()) {
                                if (resultado.getInt("qtd") > 0) {
                                    return true;
                                }
                            }
                        }
                    }

                } else if (tipoCobrar == TipoObjetosCobrarEnum.CONTRATOS_RENOVAVEIS_AUTO) {
                    StringBuilder sql = new StringBuilder();
                    sql.append("SELECT COUNT(*) as qtd FROM Produto p\n");
                    sql.append("WHERE  p.tipoproduto IN ( ");
                    sql.append(tipoContrato.replaceFirst(",", "")).append(")\n");
                    sql.append("AND p.codigo in(").append(movProdutos.replaceFirst(",", "")).append(")");
                    try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
                        try (ResultSet resultado = stm.executeQuery()) {
                            while (resultado.next()) {
                                if (resultado.getInt("qtd") > 0) {
                                    return true;
                                }
                            }
                        }
                    }

                } else if (tipoCobrar == TipoObjetosCobrarEnum.TIPOS_PRODUTOS) {
                    StringBuilder sql = new StringBuilder();
                    sql.append(" SELECT COUNT(*) as qtd FROM Produto p\n");
                    sql.append("  WHERE p.tipoproduto IN ( ");
                    sql.append(tiposProdutosPreDef.replaceFirst(",", "")).append(")\n");
                    sql.append(" AND p.codigo in(").append(movProdutos.replaceFirst(",", "")).append(")");
                    try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
                        try (ResultSet resultado = stm.executeQuery()) {
                            while (resultado.next()) {
                                if (resultado.getInt("qtd") > 0) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }
    public ResultSet consultarAutorizacaoCobranca(int codigoPessoa, Integer codigoContrato) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT tipoacobrar, listaobjetosacobrar FROM AutorizacaoCobrancaCliente acc\n");
        sql.append(" INNER JOIN Cliente cli on cli.codigo = acc.cliente\n");
        sql.append(" WHERE cli.pessoa = ").append(codigoPessoa);
        sql.append(" AND (tipoacobrar != 4 \n");
        if(codigoContrato != null){
            sql.append(" OR (tipoacobrar = 4 AND (SELECT EXISTS(SELECT 1 FROM contratorecorrencia WHERE contrato = ").append(codigoContrato).append("))) \n");
            sql.append("OR (tipoacobrar = 4 AND (SELECT EXISTS(SELECT 1 FROM contrato c WHERE C.codigo = ").append(codigoContrato).append(" AND c.renovavelAutomaticamente = 't')))\n");
        }
        sql.append(")\n");
        sql.append("UNION ALL\n");
        sql.append(" SELECT tipoacobrar, listaobjetosacobrar FROM AutorizacaoCobrancaColaborador acc\n");
        sql.append(" INNER JOIN colaborador col on col.codigo = acc.colaborador\n");
        sql.append(" WHERE col.pessoa = ").append(codigoPessoa);
        sql.append(" AND (tipoacobrar != 4 \n");
        if(codigoContrato != null){
            sql.append(" OR (tipoacobrar = 4 AND (SELECT EXISTS(SELECT 1 FROM contratorecorrencia WHERE contrato = ").append(codigoContrato).append("))) \n");
            sql.append("OR (tipoacobrar = 4 AND (SELECT EXISTS(SELECT 1 FROM contrato c WHERE C.codigo = ").append(codigoContrato).append(" AND c.renovavelAutomaticamente = 't')))\n");
        }
        sql.append(")\n");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        return stm.executeQuery();
    }
    public ResultSet getRsParcelasDCCBIParcelasEmAberto(Integer plano, final Date inicio, final Date fim, final Date inicioCobranca,
                                                        final Date fimCobranca, List<Integer> convenios, List<Integer> colaboradores, List<Integer> empresas,
                                                        Boolean repescagem, boolean count, boolean sum, boolean remessaBoleto,
                                                        boolean somenteParcelaRemessa, boolean somenteParcelasSemBoletoGerado, Integer modalidade, boolean consultaRemessa) throws Exception {

        StringBuilder sql = new StringBuilder();
        if (sum) {
            sql.append(" SELECT SUM(valorparcela) as valor FROM (\n");
        } else if (count) {
            sql.append(" SELECT COUNT(*) as total FROM (\n");
        }
        sql.append("SELECT cli.codigo as codigoCliente, pes.nome AS nomePessoa, emp.codigo AS codigoEmpresa, mp.contrato, mp.codigo AS parcelaCodigo, mp.descricao, mp.datavencimento, mp.valorparcela, emp.nome, string_agg(co.descricao,', ') as conveniocobranca, string_agg(pColab.nome, ', ') as nomeConsultor, \n");
        sql.append("EXISTS (\n" +
                "SELECT COALESCE(rri.movparcela,0) \n" +
                "FROM remessaitem rri\n" +
                "INNER JOIN remessa rem ON rem.codigo = rri.remessa AND rri.movparcela = mp.codigo\n" +
                "WHERE rem.situacaoremessa IN (1,4)) AS estaRemessaAguardando \n");
        sql.append("FROM movparcela mp  \n");

        sql.append("LEFT JOIN contrato ct ON  ct.codigo = mp.contrato\n");
        sql.append("LEFT JOIN contratocondicaopagamento ccp on ccp.contrato = ct.codigo\n");
        sql.append("LEFT JOIN condicaopagamento cp on cp.codigo = ccp.condicaopagamento\n");

        sql.append("LEFT JOIN remessaitem ri ON mp.codigo = ri.movparcela AND ri.nrtentativaparcela = mp.nrtentativas \n");
        sql.append("LEFT JOIN remessa r ON ri.remessa = r.codigo \n");
        sql.append("LEFT JOIN transacaomovparcela tmp ON mp.codigo = tmp.movparcela AND tmp.nrtentativaparcela = mp.nrtentativas \n");
        sql.append("LEFT JOIN transacao t ON tmp.transacao = t.codigo \n");
        sql.append("LEFT JOIN pessoa pes ON mp.pessoa = pes.codigo \n");
        sql.append("LEFT JOIN cliente cli ON pes.codigo = cli.pessoa  \n");
        sql.append("LEFT JOIN autorizacaocobrancacliente aut ON aut.cliente = cli.codigo  \n");
        sql.append("                                        AND aut.ativa   = true \n");
        sql.append("LEFT JOIN conveniocobranca co ON aut.conveniocobranca = co.codigo  \n");
        sql.append("LEFT JOIN empresa emp ON mp.empresa = emp.codigo \n");
        sql.append("LEFT JOIN vinculo v on v.cliente = cli.codigo \n");
        sql.append("                   and v.tipovinculo = 'CO'\n");
        sql.append("LEFT JOIN colaborador colab on colab.codigo = v.colaborador \n");
        sql.append("LEFT JOIN pessoa pColab on pColab.codigo = colab.pessoa \n");
        sql.append("where mp.situacao = 'EA'  \n");
        sql.append("AND mp.datavencimento::date < '").append(Uteis.getDataFormatoBD(fim)).append("' \n");
        if (convenios != null && !convenios.isEmpty()) {
            sql.append("                                        AND aut.conveniocobranca IN (").append(Uteis.montarListaIN(convenios)).append(") \n");
        }
        sql.append("AND mp.valorParcela > 0 \n");
        sql.append("AND (mp.parceladcc or mp.regimerecorrencia or coalesce(cp.tipoconveniocobranca, 0) > 0 )\n");
        if (colaboradores != null && !colaboradores.isEmpty()) {
            sql.append(" AND colab.codigo IN (").append(Uteis.montarListaIN(colaboradores)).append(") \n");
        }
//        sql.append(" AND (r.situacaoremessa = 2 or r.situacaoremessa is null) AND mp.nrtentativas >= 0 \n");
        sql.append(" AND mp.nrtentativas >= 0 \n");
//        sql.append(" AND (ri.codigo is not null OR t.codigo is not null) \n");
        sql.append(" AND mp.empresa IN (" + Uteis.montarListaIN(empresas) + ") \n");
//        sql.append(" AND (r.situacaoremessa = 2 OR r.situacaoremessa is null) AND mp.nrtentativas >= 0 \n");
//        sql.append(" AND (mp.codigo NOT IN (SELECT COALESCE(movparcela,0) \n");
//        sql.append(" FROM remessaitem WHERE remessa IN (SELECT codigo FROM remessa WHERE situacaoremessa IN (1,4)))) \n");
        sql.append(" GROUP BY cli.codigo, mp.codigo, pes.nome, emp.codigo \n");
        if(sum || count){
            sql.append(") AS consulta");
        }
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        return rs;
    }

    public ResultSet getRsParcelasDCC(Integer plano, final Date inicio, final Date fim, final Date inicioCobranca, final Date fimCobranca,
                                      List<Integer> convenios, List<Integer> empresas, Boolean repescagem, boolean count, boolean sum, boolean remessaBoleto, boolean somenteParcelaRemessa,
                                      boolean somenteParcelasSemBoletoGerado, Integer modalidade, boolean consultaRemessa, boolean apresentarAlunosBloqueioCobranca,
                                      PaginadorDTO paginadorDTO) throws Exception {
        String tiposPlano = "";
        for (String tipo : AutorizacaoCobrancaVO.TiposProdutoPlano) {
            tiposPlano += ",'" + tipo + "'";
        }

        String tiposContrato = "";
        for (String tipo : AutorizacaoCobrancaVO.TiposProdutoContrato) {
            tiposContrato += ",'" + tipo + "'";
        }

        StringBuilder sql = new StringBuilder();
        if (sum) {
            sql.append(" SELECT SUM(valorparcela) as valor FROM (\n");
        } else if (count) {
            sql.append(" SELECT COUNT(*) as total FROM (\n");
        }
        sql.append(" SELECT DISTINCT mp.* FROM autorizacaocobrancacliente acc \n");
        sql.append(" INNER JOIN cliente cli ON acc.cliente = cli.codigo \n");
        sql.append(" INNER JOIN pessoa pes ON cli.pessoa = pes.codigo \n");
        sql.append(" INNER JOIN movparcela mp ON mp.pessoa = pes.codigo \n");
        adicionarJoinsGenericosParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, somenteParcelaRemessa, sql, consultaRemessa);
        adicionarWhereParcelasDCC(convenios, empresas, repescagem, remessaBoleto, sql, apresentarAlunosBloqueioCobranca);
        adicionarFiltroTipoConvenioCobrancaParcelasDCC(tiposPlano, tiposContrato, sql);
        if(somenteParcelasSemBoletoGerado){
            adicionarFiltroParcelaSemBoleto(sql);
        }
        if (!UteisValidacao.emptyNumber(modalidade)) {
            adicionarFiltroModalidade(sql);
        }
        sql.append("UNION ALL\n");

        sql.append(" SELECT DISTINCT mp.* FROM autorizacaocobrancacolaborador acc \n");
        sql.append(" INNER JOIN colaborador col ON acc.colaborador = col.codigo \n");
        sql.append(" INNER JOIN pessoa pes ON col.pessoa = pes.codigo \n");
        sql.append(" INNER JOIN movparcela mp ON mp.pessoa = pes.codigo \n");
        adicionarJoinsGenericosParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, somenteParcelaRemessa, sql, consultaRemessa);
        adicionarWhereParcelasDCC(convenios, empresas, repescagem, remessaBoleto, sql, apresentarAlunosBloqueioCobranca);
        adicionarFiltroTipoConvenioCobrancaParcelasDCC(tiposPlano, tiposContrato, sql);
        if(somenteParcelasSemBoletoGerado){
            adicionarFiltroParcelaSemBoleto(sql);
        }
        if (!UteisValidacao.emptyNumber(modalidade)) {
            adicionarFiltroModalidade(sql);
        }
        if(sum || count){
            sql.append(") AS consulta");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 1;
        //Deve ser duas vezes, devido ao UNION que existe na consulta, com os mesmos filtros
        i = setarFiltrosParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, modalidade, stm, i);
        i = setarFiltrosParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, modalidade, stm, i);


        if (paginadorDTO != null) {
            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + stm.toString() + ") as qtd", con);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            sql.append("LIMIT ").append(maxResults).append(" \n");
            sql.append("OFFSET ").append(indiceInicial);
        }

        ResultSet rs = stm.executeQuery();
        return rs;
    }

    private void adicionarFiltroModalidade(StringBuilder sql) {
        sql.append(" AND (SELECT EXISTS (\n")
                .append("SELECT mpm.codigo\n")
                .append("FROM movprodutomodalidade mpm\n")
                .append("INNER JOIN movprodutoparcela mpp ON mpm.movproduto = mpp.movproduto AND mpp.movparcela = mp.codigo\n")
                .append("WHERE modalidade = ?))\n");
    }

    private void adicionarFiltroParcelaSemBoleto(StringBuilder sql) {
        sql.append(" and NOT EXISTS(\n");
        sql.append("SELECT 1\n");
        sql.append("FROM remessaitemmovparcela rmov\n");
        sql.append("INNER JOIN remessaitem ri ON ri.codigo = rmov.remessaitem\n");
        sql.append("WHERE ri.tipo IN (").append(TipoRemessaEnum.BOLETO.getId()).append(",").append(TipoRemessaEnum.ITAU_BOLETO.getId()).append(")\n");
        sql.append("AND mp.codigo = rmov.movparcela\n");
        sql.append(")\n ");
    }

    private int setarFiltrosParcelasDCC(Integer plano, Date inicio, Date fim, Date inicioCobranca, Date fimCobranca, Integer modalidade, PreparedStatement stm, int i) throws Exception {
        if(!UteisValidacao.emptyNumber(plano)){
            stm.setInt(i++, plano);
        }
        if (inicio != null) {
            stm.setDate(i++, Uteis.getDataJDBC(inicio));
        }
        if (fim != null) {
            stm.setDate(i++, Uteis.getDataJDBC(fim));
        }
        if (inicioCobranca != null) {
            stm.setDate(i++, Uteis.getDataJDBC(inicioCobranca));
        }
        if (fimCobranca != null) {
            stm.setDate(i++, Uteis.getDataJDBC(fimCobranca));
        }
        if (!UteisValidacao.emptyNumber(modalidade)) {
            stm.setInt(i++, modalidade);
        }
        return i;
    }

    private void adicionarWhereParcelasDCC(List<Integer> convenios, List<Integer> empresas, Boolean repescagem,
                                           boolean remessaBoleto, StringBuilder sql, boolean apresentarAlunosBloqueioCobranca) {

        sql.append(" WHERE not exists (select ri.codigo from remessaitem ri \n");
        sql.append("inner join remessa re on ri.remessa = re.codigo \n");
        sql.append("where ri.movparcela = mp.codigo \n");
        sql.append("and re.situacaoremessa in (");
        sql.append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(")) \n");

        sql.append(" AND not exists (select rim.codigo from remessaitemmovparcela rim \n");
        sql.append("inner join remessaitem ri on ri.codigo = rim.remessaitem \n");
        sql.append("inner join remessa re on ri.remessa = re.codigo \n");
        sql.append("where rim.movparcela = mp.codigo \n");
        sql.append("and re.tipo in (").append(TipoRemessaEnum.EDI_CIELO.getId()).append(",").append(TipoRemessaEnum.DCC_BIN.getId()).append(",").append(TipoRemessaEnum.GET_NET.getId()).append(") \n");
        sql.append("and re.situacaoremessa in (");
        sql.append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(")) \n");

        if (remessaBoleto) {
            sql.append(" AND (mp.situacao = 'EA' AND mp.valorParcela > 0)\n");
            sql.append(" AND mp.naoRealizarCobrancaAutomatica IS NOT TRUE");
        } else {
            sql.append(" AND mp.valorParcela > 0 \n");
            sql.append(" AND mp.situacao = 'EA'\n");
        }
        if(convenios != null && !convenios.isEmpty()){
            sql.append(" AND acc.conveniocobranca IN (").append(Uteis.montarListaIN(convenios)).append(") \n");
            sql.append(" AND acc.ativa = true \n");
        }
        if(!UteisValidacao.emptyList(empresas)){
            sql.append(" AND mp.empresa IN (").append(Uteis.montarListaIN(empresas)).append(") \n");
        }
        if (repescagem != null && repescagem) {
            //REPESCAGEM
            sql.append(" AND (r.situacaoremessa = 2 or r.situacaoremessa is null) AND mp.nrtentativas > 0  \n");
        } else  if(repescagem != null && !repescagem) {
            //ABERTAS
            sql.append(" AND (r.situacaoremessa IS NULL OR r.situacaoremessa NOT IN (1,4)) AND mp.nrtentativas = 0  \n");
        }
        if (!apresentarAlunosBloqueioCobranca) {
            sql.append(" AND ( \n");
            sql.append("pes.databloqueiocobrancaautomatica is null \n");
            sql.append("or (pes.databloqueiocobrancaautomatica is not null ");
            sql.append("and mp.datavencimento <= pes.databloqueiocobrancaautomatica::date and pes.tipobloqueiocobrancaautomatica = ").append(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS.getCodigo()).append(") \n");
            sql.append(") \n");
        }
    }

    private void adicionarFiltroTipoConvenioCobrancaParcelasDCC(String tiposPlano, String tiposContrato, StringBuilder sql) {
        sql.append(" AND (mp.codigo NOT IN (SELECT COALESCE(movparcela,0) \n");
        sql.append("        FROM remessaitem WHERE remessa in (SELECT codigo FROM remessa WHERE situacaoremessa in (1,4)))) \n");
        sql.append(" AND(  \n");
        sql.append(" (tipoacobrar = 1)  \n");
        sql.append(" OR (tipoacobrar = 2 AND (SELECT COUNT(*) FROM movproduto mpd  \n");
        sql.append(" 			INNER JOIN movprodutoparcela mpp ON mpd.codigo = mpp.movproduto AND mpp.movparcela = mp.codigo  \n");
        sql.append(" 			INNER JOIN produto p ON p.codigo = mpd.produto AND p.tipoproduto IN ( ");
        sql.append(tiposPlano.replaceFirst(",", "")).append(")) > 0) 	 \n");
        sql.append(" OR (tipoacobrar = 3 AND (SELECT COUNT(*) FROM movproduto mpd  \n");
        sql.append(" 			INNER JOIN movprodutoparcela mpp ON mpd.codigo = mpp.movproduto AND mpp.movparcela = mp.codigo  \n");
        sql.append(" 			INNER JOIN produto p ON p.codigo = mpd.produto ");
        sql.append("                    AND p.tipoproduto = ANY (('{'||acc.listaobjetosacobrar||'}') :: text[])) > 0)  \n");
        sql.append(" OR (tipoacobrar = 4 AND (SELECT COUNT(*) FROM movproduto mpd  \n");
        sql.append(" 			INNER JOIN movprodutoparcela mpp ON mpd.codigo = mpp.movproduto AND mpp.movparcela = mp.codigo  \n");
        sql.append(" 			INNER JOIN produto p ON p.codigo = mpd.produto AND p.tipoproduto IN ( ");
        sql.append(tiposContrato.replaceFirst(",", "")).append(")) > 0 ");
        sql.append(" AND ( (SELECT EXISTS(SELECT * FROM contratorecorrencia WHERE contrato = mp.contrato)) OR (SELECT EXISTS(SELECT 1 FROM contrato WHERE codigo = mp.contrato AND renovavelAutomaticamente = 't'))))) 	 \n");
    }

    private void adicionarJoinsGenericosParcelasDCC(Integer plano, Date inicio, Date fim, Date inicioCobranca, Date fimCobranca, boolean somenteParcelaRemessa, StringBuilder sql, boolean consultaRemessa) {
        if(!UteisValidacao.emptyNumber(plano)){
            sql.append("INNER JOIN contrato con on con.codigo = mp.contrato and con.plano = ? \n");
        }
        if (inicio != null) {
            sql.append(" AND mp.datavencimento >= ? \n");
        }
        if (fim != null && consultaRemessa) {
            sql.append(" AND mp.datavencimento <= ? \n");
        }else if (fim != null){
            sql.append(" AND mp.datavencimento < ? \n");
        }
        if (inicioCobranca != null) {
            sql.append(" AND mp.datacobranca >= ? \n");
        }
        if (fimCobranca != null) {
            sql.append(" AND mp.datacobranca <= ? \n");
        }
        if(somenteParcelaRemessa) {
            sql.append(" INNER JOIN remessaitem ri ON ri.movparcela = mp.codigo \n");
            sql.append(" INNER JOIN remessa r ON r.codigo = ri.remessa \n");
        }else{
            sql.append(" LEFT JOIN remessaitem ri ON ri.movparcela = mp.codigo \n");
            sql.append(" LEFT JOIN remessa r ON r.codigo = ri.remessa \n");
        }
    }

    public void alterarRegimeDCCParcela(int codigo,Boolean parcelaDCC) throws Exception {
        try {
            String sql = "UPDATE MovParcela SET parcelaDCC = ? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setBoolean(1, parcelaDCC);
                sqlAlterar.setInt(2, codigo);
                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }
    public List<MovParcelaVO> consultarPorCodigoPessoaSituacao(Integer valorConsulta, String situacao, Integer contrato,
                                                               int nivelMontarDados, boolean ignorarEmRemessa, Integer limit) throws Exception {
        consultar(getIdEntidade(), true);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct mov.movProdutoOriginal, plano.descricao as nomeplano,plano.parcelamentooperadora, plano.maximovezesparcelar, MovParcela.*   \n");
        sql.append("from MovParcela \n");
        sql.append("left join movProdutoParcela movProdParc on movProdParc.movParcela = movParcela.codigo \n");
        sql.append("left join movProduto mov on mov.codigo = movProdParc.movProduto \n");
        sql.append("LEFT JOIN contrato on movparcela.contrato = contrato.codigo \n");
        sql.append("LEFT JOIN plano on plano.codigo= contrato.plano \n");
        sql.append("LEFT JOIN aulaavulsadiaria on movparcela.aulaavulsadiaria = aulaavulsadiaria.codigo \n");
        sql.append("LEFT JOIN vendaavulsa on movparcela.vendaavulsa = vendaavulsa.codigo \n");
        sql.append("WHERE  movparcela.pessoa = " + valorConsulta);
        // n?o retornar parcelas de Multa/Juros.
        sql.append(" and (coalesce(mov.movProdutoOriginal,0) = 0 ) \n");
        if (!situacao.isEmpty()) {
            sql.append(" AND movparcela.situacao = '" + situacao + "' \n");
        }
        if (!UteisValidacao.emptyNumber(contrato)) {
            sql.append(" AND movparcela.contrato = " + contrato + " ");
        }
        if(ignorarEmRemessa){
            sql.append(" AND NOT EXISTS( ");
            sql.append("select ri.codigo from remessaitem ri inner join remessa re on ri.remessa = re.codigo left join remessaitemmovparcela  rmov on  ri.codigo = rmov.remessaitem where ");
            sql.append(" (ri.movparcela = movparcela.codigo  or ");
            sql.append(" (movparcela.codigo = rmov.movparcela AND ri.tipo IN (").append(TipoRemessaEnum.BOLETO.getId()).append(",").append(TipoRemessaEnum.ITAU_BOLETO.getId()).append(")))\n");
            sql.append(" and  re.situacaoremessa in (" + SituacaoRemessaEnum.GERADA.getId() + "," + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + ")) \n");

            sql.append(" AND NOT EXISTS( ");
            sql.append(" select b.codigo from boleto b inner join boletomovparcela bmp on bmp.boleto = b.codigo where bmp.movparcela = movparcela.codigo ) ");
        }
       
        sql.append(" ORDER BY MovParcela.dataRegistro desc, MovParcela.codigo desc ");
        if (!UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit);
        }
        List<MovParcelaVO> lista;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                lista = montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
        verificarSeParcelaEstaEmRemessa(lista);
        return lista;
    }

    private void verificarSeParcelaEstaEmRemessa(List<MovParcelaVO> listaMovParcela)throws Exception{
        StringBuilder sql = new StringBuilder("");
        sql.append("select rem.situacaoRemessa, conv.tipoConvenio \n");
        sql.append("from remessa rem \n");
        sql.append("inner join remessaitem remItem on remItem.remessa = rem.codigo \n");
        sql.append("inner join convenioCobranca conv on conv.codigo = rem.convenioCobranca \n");
        // Tipo convenio = 1 - Boleto      situacaoRemessa = 1 (Gerada) e 4 (Remessa enviada)
        sql.append("where (coalesce(conv.tipoConvenio, 0) <> 1) and (rem.situacaoRemessa in (1,4)) and  remItem.movParcela = ?");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            for (MovParcelaVO obj : listaMovParcela) {
                pst.setInt(1, obj.getCodigo());
                ResultSet rs = pst.executeQuery();
                if (rs.next()) {
                    obj.setSituacao("Em Remessa");
                }
            }
        }

    }

    public void cancelarProdutosVinculados(List<MovParcelaVO> parcelasParaCancelar) throws Exception {
        HashMap<Integer,MovProdutoVO> produtos = new HashMap<Integer,MovProdutoVO>();
        MovProdutoParcela mppDAO = new MovProdutoParcela(con);
        MovProduto movProdutoDAO = new MovProduto(con);
        for(MovParcelaVO parcela: parcelasParaCancelar){
            parcela.setMovProdutoParcelaVOs(mppDAO.consultarPorCodigoMovParcela(parcela.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            Iterator i = parcela.getMovProdutoParcelaVOs().iterator();
            while (i.hasNext()){
                MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) i.next();
                if(produtos.containsKey(mpp.getMovProduto())){
                    produtos.get(mpp.getMovProduto()).getMovProdutoParcelaVOs().add(mpp);
                } else {
                    MovProdutoVO produto = movProdutoDAO.consultarPorChavePrimaria(mpp.getMovProduto(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    produto.getMovProdutoParcelaVOs().add(mpp);
                    produtos.put(produto.getCodigo(), produto);
                }
            }
        }
        Set<Integer> chaves = produtos.keySet();
        Double soma = 0.0;
        for (Integer codigo : chaves){
            soma = 0.0;
            Iterator p = produtos.get(codigo).getMovProdutoParcelaVOs().iterator();
            while (p.hasNext()){
                MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) p.next();
                soma = Uteis.arredondarForcando2CasasDecimais(soma + mpp.getValorPago());
            }
            if (Uteis.arredondarForcando2CasasDecimais(produtos.get(codigo).getTotalFinal()) <= soma){
                movProdutoDAO.alterarSomenteSituacaoSemCommit(produtos.get(codigo).getCodigo(), "CA");
            } else {
                if (UteisValidacao.emptyNumber(produtos.get(codigo).getQuantidade())) {
                    produtos.get(codigo).setQuantidade(1);
                }
                MovProdutoVO produtoCan = (MovProdutoVO) produtos.get(codigo).getClone(true);
                produtoCan.setTotalFinal(soma);
                produtoCan.setPrecoUnitario(soma / produtoCan.getQuantidade());
                produtoCan.setValorFaturado(soma);
                produtoCan.setValorDesconto(0.0);
                produtoCan.setSituacao("CA");
                produtoCan.setMovProdutoBase(codigo);
                movProdutoDAO.incluirSemCommit(produtoCan);
                Iterator pc = produtos.get(codigo).getMovProdutoParcelaVOs().iterator();
                while (pc.hasNext()){
                    MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) pc.next();
                    mpp.setMovProduto(produtoCan.getCodigo());
                    mppDAO.alterar(mpp);
                }
                produtos.get(codigo).setTotalFinal(produtos.get(codigo).getTotalFinal() - soma);
                produtos.get(codigo).setValorFaturado(produtos.get(codigo).getTotalFinal());
                produtos.get(codigo).setPrecoUnitario((produtos.get(codigo).getTotalFinal() + produtos.get(codigo).getValorDesconto()) / produtos.get(codigo).getQuantidade());

                produtos.get(codigo).setMovProdutoParcelaVOs(mppDAO.consultarPorCodigoMovProdutos(produtos.get(codigo).getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                Iterator pa = produtos.get(codigo).getMovProdutoParcelaVOs().iterator();
                boolean produtoPago= true;
                while (pa.hasNext()){
                    MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) pa.next();
                    if(mpp.getReciboPagamento().getCodigo() == 0){
                        produtoPago = false;
                        produtos.get(codigo).setSituacao("EA");
                        break;
                    }
                }
                if(produtoPago){
                    produtos.get(codigo).setSituacao("PG");
                }
                movProdutoDAO.alterarSemCommit(produtos.get(codigo));
            }
        }
    }

    public List<MovParcelaVO> criarParcelaMultaJuros(List<MovParcelaVO> listaParcelasPagar, EmpresaVO empresaVO, UsuarioVO usuarioVO, Date dataPagamento, Double multiplicadorMultaJuros, Double valorMoraBanco, boolean controlarTransacao) throws Exception {
        List<MovParcelaVO> listaParcelasMultaJuros = new ArrayList<MovParcelaVO>();
        try {
            if(controlarTransacao){
                con.setAutoCommit(false);
            }
            Map<Integer, VendaAvulsaVO> vendaAvulsaPessoa = new HashMap<Integer, VendaAvulsaVO>();
            for (MovParcelaVO parcelaParaPagar : listaParcelasPagar) {
                VendaAvulsaVO vendaAvulsaVOMultaJuros = vendaAvulsaPessoa.get(parcelaParaPagar.getPessoa().getCodigo());

                Date dataVencimentoAjustada = gerarDataVencimentoUtil(parcelaParaPagar.getDataVencimento(), empresaVO);

                //verifica se gera ou não multa
                if (Calendario.maior(dataPagamento, dataVencimentoAjustada)) {
                    if (parcelaParaPagar.getMostrarMultaJuros()) {
                        tratarParcelasJurosEmultaJaGeradas(parcelaParaPagar);
                        if (vendaAvulsaVOMultaJuros == null) {
                            vendaAvulsaVOMultaJuros = criarVendaAvulsaVOMultaJuros(parcelaParaPagar, usuarioVO);
                            vendaAvulsaPessoa.put(parcelaParaPagar.getPessoa().getCodigo(), vendaAvulsaVOMultaJuros);
                        }

                        MovParcelaVO movParcelaMultaJuros = criarMovParcelaMultaJuros(parcelaParaPagar, vendaAvulsaVOMultaJuros, usuarioVO);

                        double totalMulta = 0.0;
                        double totalJuros = 0.0;
                        double valorParcela = 0.0;
                        for (MovProdutoParcelaVO mppVO : parcelaParaPagar.getMovProdutoParcelaVOs()) {
                            valorParcela += Uteis.arredondarForcando2CasasDecimais(mppVO.getValorPago());
                        }
                        if (valorParcela > 0.0) {
                            boolean usouValorBanco = false;
                            totalMulta = calcularMulta(empresaVO, valorParcela);
                            if (valorMoraBanco != null && valorMoraBanco > 0) {
                                usouValorBanco = true;
                                if (valorMoraBanco > totalMulta) {
                                    valorMoraBanco = Uteis.arredondarForcando2CasasDecimais(valorMoraBanco - totalMulta);
                                } else {
                                    totalMulta = valorMoraBanco;
                                    valorMoraBanco = 0.0;
                                }
                            }

                            totalJuros = calcularJuros(empresaVO, valorParcela, dataVencimentoAjustada, dataPagamento);
                            if (usouValorBanco && valorMoraBanco <= 0) {
                                totalJuros = 0.0;
                            } else {
                                if (valorMoraBanco != null && valorMoraBanco > 0) {
                                    if (valorMoraBanco > totalJuros) {
                                        valorMoraBanco = Uteis.arredondarForcando2CasasDecimais(valorMoraBanco - totalJuros);
                                    } else {
                                        totalJuros= valorMoraBanco;
                                        valorMoraBanco = 0.0;
                                    }
                                }
                            }
                        }

                        if (totalMulta > 0.0 || totalJuros > 0.0) {
                            MovProduto movProdutoDAO = new MovProduto(this.con);
                            MovProdutoVO movProdutoMultaJuros = movProdutoDAO.criarMovProdutoMultaJuros(parcelaParaPagar, vendaAvulsaVOMultaJuros, totalMulta, totalJuros, usuarioVO, multiplicadorMultaJuros);
                            movProdutoDAO = null;

                            MovProdutoParcelaVO movProdutoParcelaMultaJuros = criarMovProdutoParcelaMultaJuros(movProdutoMultaJuros, parcelaParaPagar);
                                movProdutoMultaJuros.getMovProdutoParcelaVOs().add(movProdutoParcelaMultaJuros);
                                movParcelaMultaJuros.getMovProdutoParcelaVOs().add(movProdutoParcelaMultaJuros);
                                movParcelaMultaJuros.setValorParcela(Uteis.arredondarForcando2CasasDecimais(movParcelaMultaJuros.getValorParcela() + movProdutoParcelaMultaJuros.getValorPago()));
                                ItemVendaAvulsaVO itemVendaAvulsaMultaJuros = criarItemVendaAvulsaMultaJuros(movProdutoMultaJuros, usuarioVO);
                                itemVendaAvulsaMultaJuros.setVendaAvulsaVO(vendaAvulsaVOMultaJuros);
                                vendaAvulsaVOMultaJuros.getItemVendaAvulsaVOs().add(itemVendaAvulsaMultaJuros);
                                vendaAvulsaVOMultaJuros.getMovProdutoVOs().add(movProdutoMultaJuros);
                            }

                        listaParcelasMultaJuros.add(movParcelaMultaJuros);
                        vendaAvulsaVOMultaJuros.getMovParcelaVOs().add(movParcelaMultaJuros);
                        vendaAvulsaVOMultaJuros.setValorTotal(Uteis.arredondarForcando2CasasDecimais(vendaAvulsaVOMultaJuros.getValorTotal()));
                    }
                }
            }
            for (Integer codigoPessoaVendaAvulsa : vendaAvulsaPessoa.keySet()) {
                VendaAvulsaVO vendaAvulsaVOMultaJuros = vendaAvulsaPessoa.get(codigoPessoaVendaAvulsa);
                if (vendaAvulsaVOMultaJuros != null) {
                    VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(this.con);
                    vendaAvulsaDAO.incluirVendaAvulsaParaEdicaoPagamento(vendaAvulsaVOMultaJuros);
                    vendaAvulsaDAO = null;

                    ItemVendaAvulsa itemVendaAvulsaDAO = new ItemVendaAvulsa(this.con);
                    itemVendaAvulsaDAO.incluirItemVendaAvulsas(vendaAvulsaVOMultaJuros.getCodigo(), vendaAvulsaVOMultaJuros.getItemVendaAvulsaVOs());
                    itemVendaAvulsaDAO = null;

                    for (MovProdutoVO movProdutoMultaJuros : vendaAvulsaVOMultaJuros.getMovProdutoVOs()) {
                        movProdutoMultaJuros.setVendaAvulsa(vendaAvulsaVOMultaJuros.getCodigo());
                        MovProduto movProdutoDAO = new MovProduto(this.con);
                        movProdutoDAO.incluirSemCommit(movProdutoMultaJuros);
                        movProdutoDAO = null;
                        for (Object object : movProdutoMultaJuros.getMovProdutoParcelaVOs()) {
                            MovProdutoParcelaVO movProdutoParcelaMultaJuros = (MovProdutoParcelaVO) object;
                            movProdutoParcelaMultaJuros.setMovProduto(movProdutoMultaJuros.getCodigo());
                            movProdutoParcelaMultaJuros.setMovProdutoVO(movProdutoMultaJuros);
                        }
                    }

                }
            }

            for (MovParcelaVO movParcelaMultaJuros : listaParcelasMultaJuros) {
                if (movParcelaMultaJuros.isNovoObj()) {
                    incluirComProdutosSemCommit(movParcelaMultaJuros);
                }
            }
            if(controlarTransacao){
                con.commit();
                con.setAutoCommit(true);
            }
        } catch (Exception ex) {
            if(controlarTransacao){
                con.rollback();
                con.setAutoCommit(true);
            }
            throw ex;
        }
        return listaParcelasMultaJuros;
    }

    private void tratarParcelasJurosEmultaJaGeradas(MovParcelaVO parcelaParaPagar) throws Exception {
        List<MovParcelaVO> parcelasJurosEmulta = consultarParcelasMultaEJuros(parcelaParaPagar.getCodigo() , Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        for (MovParcelaVO parcelaVO : parcelasJurosEmulta ) {
           excluirMultaJurosParcela(parcelaVO);
        }
    }

    private MovProdutoParcelaVO criarMovProdutoParcelaMultaJuros(MovProdutoVO movProdutoMultaJuros, MovParcelaVO parcelaOriginalMultaJuros) {
        MovProdutoParcelaVO movProdutoParcelaMultaJuros = new MovProdutoParcelaVO();
        movProdutoParcelaMultaJuros.setMovProdutoVO(movProdutoMultaJuros);
        movProdutoParcelaMultaJuros.setMovParcelaOriginalMultaJuros(parcelaOriginalMultaJuros);
        //N?o ? necess?rio informar a MovParcela, pois este registro ser? inserido junto da MovParcelaVO
        movProdutoParcelaMultaJuros.setValorPago(movProdutoMultaJuros.getTotalFinal());
        return movProdutoParcelaMultaJuros;
    }

    private ItemVendaAvulsaVO criarItemVendaAvulsaMultaJuros(MovProdutoVO movProdutoMultaJuros, UsuarioVO usuarioVO) {
        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        item.setMultaJuros(true);
        item.setDataVenda(movProdutoMultaJuros.getDataLancamento());
        item.setDataValidade(movProdutoMultaJuros.getDataFinalVigencia());
        item.setQuantidade(1);
        item.setUsuarioVO(usuarioVO);
        item.setProduto(movProdutoMultaJuros.getProduto());
        item.setValorParcial(movProdutoMultaJuros.getTotalFinal());
        item.getProduto().setValorFinal(movProdutoMultaJuros.getTotalFinal());
        return item;
    }

    private double calcularMulta(EmpresaVO empresaVO, Double valorParcela) {
        if(empresaVO.isUtilizarMultaValorAbsoluto()) {
            return Uteis.arredondarForcando2CasasDecimais(empresaVO.getMultaCobrancaAutomatica());
        } else {
            return Uteis.arredondarForcando2CasasDecimais(valorParcela * (empresaVO.getMultaCobrancaAutomatica() / 100));
        }
    }

    private double calcularJuros(EmpresaVO empresaVO, Double valorParcela, Date dataVencimento, Date dataPagamento) {
        if (empresaVO.isUtilizarJurosValorAbsoluto()) {
            return Uteis.arredondarForcando2CasasDecimais((Uteis.nrDiasEntreDatas(dataVencimento, dataPagamento) * empresaVO.getJurosCobrancaAutomatica()));
        } else {
            return Uteis.arredondarForcando2CasasDecimais(valorParcela * ((Uteis.nrDiasEntreDatas(dataVencimento, dataPagamento)) * (empresaVO.getJurosCobrancaAutomatica() / 100)));
        }
    }

    private MovParcelaVO criarMovParcelaMultaJuros(MovParcelaVO movParcelaVO, VendaAvulsaVO vendaAvulsaVO, UsuarioVO usuarioVO) throws Exception {
        MovParcelaVO novaParcela = new MovParcelaVO();
        novaParcela.setDescricao("Multa e Juros - Parcela " + movParcelaVO.getCodigo());
        novaParcela.setResponsavel(usuarioVO);
        novaParcela.setValorParcela(0.0);
        novaParcela.setSituacao("EA");
        novaParcela.setDataVencimento(movParcelaVO.getDataVencimento());
        novaParcela.setDataRegistro(Calendario.hoje());
        novaParcela.setEmpresa(movParcelaVO.getEmpresa());
        novaParcela.setPessoa(movParcelaVO.getPessoa());
        novaParcela.setDataCobranca(movParcelaVO.getDataVencimento());
        novaParcela.setVendaAvulsaVO(vendaAvulsaVO);
        return novaParcela;
    }

    private VendaAvulsaVO  criarVendaAvulsaVOMultaJuros(MovParcelaVO movParcelaVO, UsuarioVO usuarioVO) throws Exception {
        VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();

        Cliente clienteDAO = new Cliente(this.con);
        ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        clienteDAO = null;
        if (!clienteVO.isNovoObj()) {
            vendaAvulsaVO.setTipoComprador("CI");
            vendaAvulsaVO.setCliente(clienteVO);
            vendaAvulsaVO.setNomeComprador(clienteVO.getPessoa().getNome());
        } else {
            Colaborador colaboradorDAO = new Colaborador(this.con);
            ColaboradorVO colaboradorVO = colaboradorDAO.consultarColaboradorPorPessoaVinculada(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            colaboradorDAO = null;
            if (!colaboradorVO.isNovoObj()) {
                vendaAvulsaVO.setTipoComprador("CO");
                vendaAvulsaVO.setColaborador(colaboradorVO);
                vendaAvulsaVO.setNomeComprador(colaboradorVO.getPessoa().getNome());
            } else {
                vendaAvulsaVO.setTipoComprador("CN");
                vendaAvulsaVO.setNomeComprador("NÃO INFORMADO");
            }
        }
        vendaAvulsaVO.setEmpresa(movParcelaVO.getEmpresa());
        vendaAvulsaVO.setResponsavel(usuarioVO);
        return vendaAvulsaVO;
    }

    public Double montarMultaJurosParcelaVencida(EmpresaVO empresaVO, List<MovParcelaVO> movParcelas, Date dataPagamento, boolean forcarCalculo, Double multiplicadorMultaJuros, Double valorMoraBanco) throws Exception {
        Double valorMultaJuros = 0.0;
        if (empresaVO.getCobrarAutomaticamenteMultaJuros()) {
            for(MovParcelaVO parcelaVO : movParcelas){
                Date dataVencimentoAjustada = gerarDataVencimentoUtil(parcelaVO.getDataVencimento(), empresaVO);

                //verifica se gera ou não multa
                if (Calendario.maior(dataPagamento, dataVencimentoAjustada)) {
                    if ((Calendario.getDataComHoraZerada(dataPagamento).compareTo(dataVencimentoAjustada) > 0 || forcarCalculo)) {
                        //Desta forma não irá calcular multa e juros em cima de produtos Multa e Juros
                        double valorParcela = 0.0;
                        if(UteisValidacao.emptyList(parcelaVO.getMovProdutoParcelaVOs())) {
                            MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);
                            parcelaVO.setMovProdutoParcelaVOs(movProdutoParcelaDAO.consultarPorCodigoMovParcela(parcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                            movProdutoParcelaDAO = null;
                        }
                        for (MovProdutoParcelaVO mppVO : parcelaVO.getMovProdutoParcelaVOs()) {
                            valorParcela += Uteis.arredondarForcando2CasasDecimais(mppVO.getValorPago());
                        }
                        if (valorParcela > 0.0) {
                            boolean usouValorBanco = false;
                            double valorMultaCalculada = calcularMulta(empresaVO, valorParcela);
                            if (valorMoraBanco != null && valorMoraBanco > 0) {
                                usouValorBanco = true;
                                if (valorMoraBanco > valorMultaCalculada) {
                                    valorMoraBanco -= valorMultaCalculada;
                                } else {
                                    valorMultaCalculada = valorMoraBanco;
                                    valorMoraBanco = 0.0;
                                }
                            }
                            double valorJurosCalculado = calcularJuros(empresaVO, valorParcela, dataVencimentoAjustada, dataPagamento);
                            if (usouValorBanco && valorMoraBanco <= 0) {
                                valorJurosCalculado = 0.0;
                            } else {
                                if (valorMoraBanco != null && valorMoraBanco > 0) {
                                    if (valorMoraBanco > valorJurosCalculado) {
                                        valorMoraBanco -= valorJurosCalculado;
                                    } else {
                                        valorJurosCalculado = valorMoraBanco;
                                        valorMoraBanco = 0.0;
                                    }
                                }
                            }

                            parcelaVO.setValorMulta(Uteis.arredondarForcando2CasasDecimais(valorMultaCalculada));
                            parcelaVO.setValorJuros(Uteis.arredondarForcando2CasasDecimais(valorJurosCalculado));
                            parcelaVO.setValorMultaJuros(Uteis.arredondarForcando2CasasDecimais(valorMultaCalculada + valorJurosCalculado));

                            valorMultaJuros += parcelaVO.getValorMultaJuros();
                        }
                    }
                }
            }
        }
        return valorMultaJuros;
    }
    /**
     *  Caso a data de vencimento caia no final de semana ou em feriado a data de vencimento deve
     *  corresponder ao próximo dia útil
     */
    public Date gerarDataVencimentoUtil(Date dataVencimento, EmpresaVO empresaVO) throws Exception {
        Feriado feriadoDAO = new Feriado(this.con);
        Calendar cal = Calendario.getInstance();
        cal.setTime(dataVencimento);

        List<Date> listaFeriadosDoDia = feriadoDAO.consultarDiasFeriados(cal.getTime(), cal.getTime(), empresaVO);

        while (!UteisValidacao.emptyList(listaFeriadosDoDia) || cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            cal.setTime(Uteis.somarDias(cal.getTime(), 1));
            listaFeriadosDoDia = feriadoDAO.consultarDiasFeriados(cal.getTime(), cal.getTime(), empresaVO);
        }

        return cal.getTime();
    }

    public boolean deveGerarMulta(Date dataPagamento, Date dataVencimento, EmpresaVO empresaVO) throws Exception {
        Date dataVencimentoAjustada = gerarDataVencimentoUtil(dataVencimento, empresaVO);
        return Calendario.maior(dataPagamento, dataVencimentoAjustada);
    }

    public void cancelarMultaJurosPelaParcelaOriginal(Integer codMovParcela) throws Exception {
        executarConsulta("UPDATE movproduto SET situacao = 'CA' where codigo IN (select mppjm.movproduto from movparcela movparcelamj \n"
                + "left join  movprodutoparcela mppjm  ON movparcelamj.codigo = mppjm.movparcela\n"
                + "left join  movproduto mprod ON mprod.codigo = mppjm.movproduto\n"
                + "left join movprodutoparcela mpp on  mprod.movprodutooriginal = mpp.movproduto\n"
                + "where mpp.movparcela = " + codMovParcela + " AND movparcelamj.situacao = 'EA')", con);
        
        executarConsulta("delete from clientemensagem where movparcela IN (select movparcelamj.codigo from movparcela movparcelamj \n"
                + "left join  movprodutoparcela mppjm  ON movparcelamj.codigo = mppjm.movparcela\n"
                + "left join  movproduto mprod ON mprod.codigo = mppjm.movproduto\n"
                + "left join movprodutoparcela mpp on  mprod.movprodutooriginal = mpp.movproduto\n"
                + "where mpp.movparcela = " + codMovParcela + " AND movparcelamj.situacao = 'EA')", con);
        
        executarConsulta("UPDATE movparcela SET situacao = 'CA' where codigo IN (select movparcelamj.codigo from movparcela movparcelamj \n"
                + "left join  movprodutoparcela mppjm  ON movparcelamj.codigo = mppjm.movparcela\n"
                + "left join  movproduto mprod ON mprod.codigo = mppjm.movproduto\n"
                + "left join movprodutoparcela mpp on  mprod.movprodutooriginal = mpp.movproduto\n"
                + "where mpp.movparcela = " + codMovParcela + " AND movparcelamj.situacao = 'EA')", con);

    }
    
    public List<MovParcelaVO> consultarPorCodigoPersonalLista(Integer codigoPersonal,final String situacao, boolean controlarAcesso, Date dataInicioVencimento,
                                                               Date dataVencimento, int nivelMontarDados) throws Exception{
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM MovParcela WHERE personal =").append(codigoPersonal);
        if (!UteisValidacao.emptyString(situacao)){
            sqlStr.append(" and situacao='").append(situacao).append("'\n");
        }
        if (dataInicioVencimento != null) {
            sqlStr.append(" AND datavencimento >= '").append(Uteis.getDataJDBC(dataInicioVencimento)).append("'\n");
        }
        if (dataVencimento != null) {
            sqlStr.append(" AND datavencimento <= '").append(Uteis.getDataJDBC(dataVencimento)).append("'\n");
        }

        sqlStr.append(" ORDER BY movparcela.datavencimento");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    
    public boolean existeParcelaContratoEmRemessaGeradaOuAguardando(Integer contrato, List<Integer> tiposConveniosIgnorar) throws Exception{
        String sqlStr = "select par.codigo from movparcela par "
                   + " inner join remessaitem ri on ri.movparcela = par.codigo "
                   + " inner join remessa re on re.codigo = ri.remessa "
                   + " where re.situacaoremessa in (1,4) and par.contrato = "+contrato;
        if(!UteisValidacao.emptyList(tiposConveniosIgnorar)){
            String tipos = "";
            for (Integer tipo : tiposConveniosIgnorar){
                tipos += ","+tipo;
            }
            tipos = tipos.replaceFirst(",", "");
            sqlStr += " re.tipo not in ( "+ tipos +") ";
        }
        sqlStr += " limit 1";
        return SuperFacadeJDBC.existe(sqlStr, con);
    }

    private String consultaMovParcela(Integer codigoPessoa, MalaDiretaVO malaDiretaVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT MPA.*\n");
        sql.append("FROM movparcela MPA\n");
        if (malaDiretaVO.getTipoEvento() != null &&
                malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS) &&
                malaDiretaVO.getCfgEvento().isBoletoParcelasVencendo()) {
            sql.append("inner JOIN empresa emp on emp.codigo = mpa.empresa \n");
            sql.append("left JOIN cliente cl on cl.pessoa = mpa.pessoa \n");
            sql.append("WHERE MPA.situacao = 'EA' \n");
            sql.append("AND MPA.descricao NOT LIKE '%MULTA%' \n");
            if(!UteisValidacao.emptyNumber(malaDiretaVO.getCfgEvento().getDiasParcelasVencidasInicial())) {
                sql.append("AND (mpa.datavencimento  <= (now() - INTERVAL '").append(malaDiretaVO.getCfgEvento().getDiasParcelasVencidasInicial()).append(" days')::date) ");
                if(!UteisValidacao.emptyNumber(malaDiretaVO.getCfgEvento().getDiasParcelasVencidasFinal())){
                    sql.append("\nAND (datavencimento  >= (now() - INTERVAL '").append(malaDiretaVO.getCfgEvento().getDiasParcelasVencidasFinal()).append(" days')::date) ");
                }
            }
            sql.append("AND ((emp.permitirmaillinggerarautorizacaocobrancaboleto and coalesce(emp.convenioboletopadrao,0) > 0) \n");
            sql.append("OR exists(select codigo from autorizacaocobrancacliente where ativa and tipoautorizacao = 3 and cliente = cl.codigo)) \n");
        } else if (malaDiretaVO.getTipoEvento() != null && malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELA_VENCENDO) && malaDiretaVO.getCfgEvento().isBoletoParcelasVencendo()) {
            sql.append("LEFT JOIN remessaitemmovparcela RIM ON RIM.movparcela = MPA.codigo\n");
            sql.append("LEFT JOIN cliente cl on cl.pessoa = mpa.pessoa \n");
            sql.append("LEFT JOIN autorizacaocobrancacliente au on au.cliente = cl.codigo \n");
            sql.append("LEFT JOIN conveniocobranca cc on cc.codigo = au.conveniocobranca \n");
            sql.append("LEFT JOIN empresa emp on emp.codigo = mpa.empresa \n");
            sql.append("WHERE MPA.datavencimento::date BETWEEN '" + Uteis.getDataFormatoBD(malaDiretaVO.getCfgEvento().getInicio()) + "' AND '" + Uteis.getDataFormatoBD(malaDiretaVO.getCfgEvento().getFim()) + "'\n");
            sql.append("AND (RIM.codigo is not null \n");
            sql.append("OR (RIM.codigo is null and \n");
            sql.append("((emp.permitirmaillinggerarautorizacaocobrancaboleto and coalesce(emp.convenioboletopadrao,0) > 0) \n");
            sql.append("OR (au.codigo is not null and au.ativa and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO.getId()).append(")))) \n");
            sql.append("AND MPA.situacao = 'EA'\n");
        } else if (malaDiretaVO.getTipoEvento() != null && malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELA_VENCENDO) && !malaDiretaVO.getCfgEvento().isBoletoParcelasVencendo()) {
            sql.append("WHERE MPA.datavencimento::date  BETWEEN '" + Uteis.getDataFormatoBD(malaDiretaVO.getCfgEvento().getInicio()) + "' AND '" + Uteis.getDataFormatoBD(malaDiretaVO.getCfgEvento().getFim()) + "'\n");
            sql.append("AND MPA.situacao = 'EA'\n");
        } else {
            sql.append("WHERE MPA.situacao = 'EA'\n");
            if (malaDiretaVO.getDatasVencimentos().length() > 0) {
                sql.append("AND (").append(malaDiretaVO.getDatasVencimentos()).append(")\n");
            } else {
                sql.append("AND MPA.datavencimento::date < '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("'\n");
            }
            sql.append("AND MPA.descricao NOT LIKE '%MULTA%'\n");
        }

        sql.append("AND MPA.pessoa = ").append(codigoPessoa).append("\n");
        return sql.toString();
    }

    public List<MovParcelaVO> consultarPorCodigoPessoaDiasEmAberto(Integer codigoPessoa, MalaDiretaVO malaDiretaVO, int nivelMontarDados) throws Exception {
        Statement stm = con.createStatement();
        String sql = consultaMovParcela(codigoPessoa, malaDiretaVO);
        ResultSet rs = stm.executeQuery(sql);
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

    private ResultSet montarSqlProdutoCliente(Integer pessoa,Integer contrato,boolean count,int limit,int offset, String orderBy, boolean orderByDesc) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        if(count){
            sql.append(" COUNT(*) as total ");
        } else {
            sql.append(" distinct on (movparcela.codigo) movparcela.codigo, movparcela.contrato, movparcela.empresa, emp.nome, descricao, dataregistro, datavencimento, valorparcela, \n");
            sql.append(" situacao, pessoa, mpcd.cupomdesconto, movparcela.parcelasRenegociadas, oo.dataoperacao as datacancelamento, rp.data as datapagamento, \n");
            sql.append(" exists(select pro.codigo from movprodutoparcela mpp \n" +
                    "inner join movproduto pro on pro.codigo = mpp.movproduto \n" +
                    "where mpp.movparcela = movparcela.codigo and pro.descricao ilike '%PRO-RATA%') as existeProdutoProRata, \n");
            sql.append(" exists(select mpp.codigo from movprodutoparcela mpp where mpp.movparcela = movparcela.codigo) as existeMovProdutoParcela ");
        }
        sql.append(" FROM MovParcela ");
        sql.append(" LEFT JOIN negociacaoeventocontratoparcelas necp ON necp.parcela = movparcela.codigo ");
        sql.append(" LEFT JOIN movparcelacupomdesconto mpcd ON mpcd.movparcela = movparcela.codigo ");
        sql.append(" LEFT JOIN movprodutoparcela mpp ON mpp.movparcela = movparcela.codigo ");
        sql.append(" LEFT JOIN recibopagamento rp on rp.codigo = mpp.recibopagamento " );
        sql.append(" LEFT JOIN observacaooperacao oo ON oo.movparcela = movparcela.codigo ");
        sql.append("                                AND oo.tipooperacao = 'PC' ");
        sql.append(" INNER JOIN empresa emp ON movparcela.empresa = emp.codigo ");
        sql.append(" WHERE  necp.contrato is null and pessoa = ").append(pessoa);
        if(!UteisValidacao.emptyNumber(contrato)){
            sql.append(" AND movparcela.contrato = ").append(contrato);
        }
        if(!count) {
            if (UteisValidacao.emptyString(orderBy)) {
                sql.append(" ORDER BY movparcela.codigo DESC");
            } else {
                sql.append(" ORDER BY movparcela.").append(orderBy);
                if (orderByDesc) {
                    sql.append(" DESC ");
                }
            }

        }
        if(!count && !UteisValidacao.emptyNumber(limit)){
            sql.append(" limit ").append(limit).append(" offset ").append(offset);
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        return rs;
    }

    public Integer obterTotalParcelaCliente(Integer pessoa, Integer contrato) throws Exception {
        ResultSet rs = montarSqlParcelasCliente(pessoa,contrato,true,0,0, "", false);
        while (rs.next()){
            return rs.getInt("totalParcelas");
        }
        return 0;
    }

    private ResultSet montarSqlParcelasCliente(Integer pessoa,Integer contrato,boolean count,int limit,int offset, String orderBy, boolean orderByDesc) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        if(count){
            sql.append(" COUNT(*) as totalParcelas ");
        } else {
            sql.append(" movparcela.codigo, movparcela.contrato, movparcela.empresa, emp.nome, descricao, dataregistro, datavencimento, valorparcela, ");
            sql.append(" situacao, pessoa, mpcd.cupomdesconto ");
        }
        sql.append(" FROM MovParcela ");
        sql.append(" LEFT JOIN negociacaoeventocontratoparcelas necp ON necp.parcela = movparcela.codigo ");
        sql.append(" LEFT JOIN movparcelacupomdesconto mpcd ON mpcd.movparcela = movparcela.codigo ");
        sql.append(" INNER JOIN empresa emp ON movparcela.empresa = emp.codigo ");
        sql.append(" WHERE  necp.contrato is null and pessoa = ").append(pessoa);
        if(!UteisValidacao.emptyNumber(contrato)){
            sql.append(" AND movparcela.contrato = ").append(contrato);
        }
        if(!count) {
            if (UteisValidacao.emptyString(orderBy)) {
                sql.append(" ORDER BY movparcela.codigo DESC");
            } else {
                sql.append(" ORDER BY movparcela.").append(orderBy);
                if (orderByDesc) {
                    sql.append(" DESC ");
                }
            }

        }
        if(!count && !UteisValidacao.emptyNumber(limit)){
            sql.append(" limit ").append(limit).append(" offset ").append(offset);
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        return rs;
    }

    public Integer obterCountParcelaCliente(Integer pessoa,Integer contrato) throws Exception {
        ResultSet rs = montarSqlProdutoCliente(pessoa,contrato,true,0,0, "" , false);
        while(rs.next()){
            return rs.getInt("total");
        }
        return 0;
    }

    @Override
    public List<MovParcelaVO> consultarTelaCliente(Integer pessoa, Integer contrato, Integer limit,Integer offset, String orderBy, boolean orderByDesc) throws Exception{
        List<MovParcelaVO> lista;
        try (ResultSet rs = montarSqlProdutoCliente(pessoa, contrato, false, limit, offset, orderBy, orderByDesc)) {
            lista = new ArrayList<MovParcelaVO>();
            while (rs.next()) {
                MovParcelaVO movParcela = new MovParcelaVO();
                movParcela.setCodigo(rs.getInt("codigo"));
                movParcela.setContrato(new ContratoVO());
                movParcela.getContrato().setCodigo(rs.getInt("contrato"));
                movParcela.setDescricao(rs.getString("descricao"));
                movParcela.setDataRegistro(rs.getDate("dataregistro"));
                movParcela.setDataVencimento(rs.getDate("datavencimento"));
                movParcela.setEmpresa(new EmpresaVO());
                movParcela.getEmpresa().setCodigo(rs.getInt("empresa"));
                movParcela.getEmpresa().setNome(rs.getString("nome"));
                movParcela.setPessoa(new PessoaVO());
                movParcela.getPessoa().setCodigo(rs.getInt("pessoa"));
                movParcela.setValorParcela(rs.getDouble("valorparcela"));
                movParcela.setSituacao(rs.getString("situacao"));
                movParcela.setCupomDesconto(rs.getString("cupomDesconto"));
                movParcela.setParcelasRenegociadas(rs.getString("parcelasRenegociadas"));
                movParcela.setExisteProdutoProRata(rs.getBoolean("existeProdutoProRata"));
                movParcela.setDataCancelamento(rs.getDate("datacancelamento"));
                movParcela.setDataPagamento(rs.getDate("datapagamento"));
                movParcela.setExisteMovProdutoParcela(rs.getBoolean("existeMovProdutoParcela"));
                lista.add(movParcela);
            }
        }
        return lista;
    }
    
     public MovParcelaVO consultarPorMovPagamentoContaCorrente(String movpagamento, int nivelMontarDados) throws Exception{
        String sql = "SELECT * FROM movparcela WHERE movpagamentocc = '" + movpagamento + "' or movpagamentocc like '" + movpagamento + ",%' or  movpagamentocc like '%," + movpagamento + ",%' or movpagamentocc like '%," + movpagamento + "'";
         try (Statement stm = con.createStatement()) {
             try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                 if (!tabelaResultado.next()) {
                     return null;
                 }
                 return (montarDados(tabelaResultado, nivelMontarDados, con));
             }
         }
     }

    @Override
    public Boolean parcelaEstaBloqueadaPorCobranca(MovParcelaVO movParcela) throws Exception {
        movParcela.setCodRemessaQueEstaVinculada(0);
        RemessaItem remessaItemDAO = new RemessaItem(con);
        RemessaItemVO remessaItemVO = remessaItemDAO.obterUltimoItemRemessaPorCodigoParcela(movParcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        remessaItemDAO = null;
        String statusRemessa = remessaItemVO != null ? remessaItemVO.getProps().get(DCCAttEnum.StatusVenda.name()) : "";
        Boolean estaEmCobranca = false;
        if (remessaItemVO != null && remessaItemVO.getRemessa() != null
                && !remessaItemVO.getRemessa().getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO)
                && !remessaItemVO.getRemessa().getTipo().equals(TipoRemessaEnum.BOLETO)
                && !(remessaItemVO.getRemessa().getTipo().equals(TipoRemessaEnum.ITAU_BOLETO)
                && statusRemessa != null && statusRemessa.equals(BBCnab400ItauStatusEnum.Status09.getId()))
                && !(remessaItemVO.getRemessa().getTipo().equals(TipoRemessaEnum.ITAU_BOLETO)
                && statusRemessa != null && statusRemessa.equals(BBCnab400ItauStatusEnum.Status21.getId()))) { //status 09 permite o pagamento de boleto itau
            //Validar se a remessa foi retornada manualmente
            if (remessaItemVO.getRemessa().isAguardandoRetorno()
                    && (UteisValidacao.emptyString(statusRemessa) || !statusRemessa.equals(BBCnab400ItauStatusEnum.Status9999.getId()))) {
                estaEmCobranca = true;
            }
            if (estaEmCobranca &&
                    remessaItemVO.getRemessa().isAguardandoRetorno() &&
                    remessaItemVO.getRemessa().getTipo() != null &&
                    remessaItemVO.getRemessa().getTipo().equals(TipoRemessaEnum.EDI_CIELO) &&
                    !UteisValidacao.emptyString(statusRemessa) &&
                    (statusRemessa.equals(DCCCieloStatusEnum.Status81.getId()) ||
                            statusRemessa.equals(DCCCieloStatusEnum.Status74.getId()) ||
                            statusRemessa.equals(DCCCieloStatusEnum.Status87.getId()) ||
                            statusRemessa.equals(DCCCieloStatusEnum.Status42.getId()) ||
                            statusRemessa.equals(DCCCieloStatusEnum.Status71.getId()) ||
                            statusRemessa.equals(DCCCieloStatusEnum.Status73.getId()) ||
                            statusRemessa.equals(DCCCieloStatusEnum.Status76.getId()) ||
                            statusRemessa.equals(DCCCieloStatusEnum.Status90.getId()))) {
                estaEmCobranca = false;
            }
            // validar se a Remessa ? de DCO e esta com status = AT
            if (remessaItemVO.getRemessa().getConvenioCobranca().getTipo().getTipoAutorizacao() != null &&
                    remessaItemVO.getRemessa().getConvenioCobranca().getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)
                    && !UteisValidacao.emptyString(statusRemessa) && statusRemessa.equals("AT")) {
                estaEmCobranca = true;
            }

            if (remessaItemVO.getRemessa().getDCO() && ("01".equals(statusRemessa) || "NA".equals(statusRemessa))) {
                estaEmCobranca = false;
            }
        }
        if (!estaEmCobranca) { //evitar que operações em paralelo dupliquem
            estaEmCobranca = !validarSituacaoParcela(movParcela.getCodigo(), "EA");
        }
        if (!estaEmCobranca) {
            try {
                validarMovParcelaComTransacaoConcluidaOuPendente(movParcela.getCodigo());
            } catch (Exception e) {
                throw new ConsistirException(e.getMessage());
            }
        }
        if (!estaEmCobranca && remessaItemVO != null && remessaItemVO.getRemessa() != null
                && ( remessaItemVO.getRemessa().getTipo().equals(TipoRemessaEnum.BOLETO) || remessaItemVO.getRemessa().getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO) )
        ) {
            estaEmCobranca = remessaItemVO.getRemessa().getSituacaoRemessa() == SituacaoRemessaEnum.GERADA ? true : false;
        }

        if (estaEmCobranca && remessaItemVO != null && !UteisValidacao.emptyNumber(remessaItemVO.getRemessa().getCodigo())) {
            movParcela.setCodRemessaQueEstaVinculada(remessaItemVO.getRemessa().getCodigo());
        }

        if (!estaEmCobranca && movParcela.getGerandoBoletoPeloGestaoBoletosOnline()) {
            estaEmCobranca = true;
        }

        return estaEmCobranca;
    }

    public Boolean parcelaEstaBloqueadaPorBoletoPendente(MovParcelaVO movParcela, boolean desconsiderarPagos) throws Exception {
        Boleto boletoDAO;
        try {
            boletoDAO = new Boleto(con);
            return boletoDAO.existeBoletoPendentePorMovParcela(movParcela.getCodigo(), desconsiderarPagos);
        } finally {
            boletoDAO = null;
        }
    }

    public List<MovParcelaVO> consultarParcelasEmAbertoParaPagamento(ConvenioCobrancaVO convenioCobrancaVO, Date dia, int nivelMontarDados) throws Exception{
        return consultarParcelasEmAbertoParaPagamento(convenioCobrancaVO, dia, nivelMontarDados, null);
    }

    public List<MovParcelaVO> consultarParcelasEmAbertoParaPagamento(ConvenioCobrancaVO convenioCobrancaVO, Date dia, int nivelMontarDados, List<MovParcelaVO> listaParcelasEspecificasConsultar) throws Exception{
        /**
         * Essa é a consulta responsável por obter as parcelas para pagamento online! (Transação)
         * by Luiz Felipe 22/04/2020
         */

        PactoPayConfig pactoPayConfigDAO;
        PactoPayConfigVO payConfigVO = null;
        try {
            pactoPayConfigDAO = new PactoPayConfig(this.con);
            payConfigVO = pactoPayConfigDAO.consultarPorEmpresa(convenioCobrancaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pactoPayConfigDAO = null;
        }

        Date dataMaximaLancamento = Calendario.hoje(); // isso para evitar que lancamentos recentes entrem no processamento e parcela seja paga via sistema e entre na remessa ao mesmo tempo
        if(!UteisValidacao.emptyString(convenioCobrancaVO.getEmpresa().getTimeZoneDefault())){
            dataMaximaLancamento = Calendario.getDateInTimeZone(Calendario.hoje(), convenioCobrancaVO.getEmpresa().getTimeZoneDefault());
        }
        StringBuilder sql1 = new StringBuilder();
        sql1.append("select * from (SELECT \n");
        sql1.append("distinct movp.* \n");
        sql1.append("FROM movparcela movp \n");
        sql1.append("INNER JOIN cliente cli ON cli.pessoa = movp.pessoa \n");
        sql1.append("INNER JOIN movprodutoparcela mpr on mpr.movparcela = movp.codigo \n");
        sql1.append("INNER JOIN movproduto mprod on mprod.codigo = mpr.movproduto \n");
        sql1.append("INNER JOIN autorizacaocobrancacliente autcli ON autcli.cliente = cli.codigo and autcli.ativa \n");
        sql1.append("INNER JOIN conveniocobranca conv ON conv.codigo = autcli.conveniocobranca \n");
        sql1.append("INNER JOIN produto p ON p.codigo = mprod.produto \n");

        StringBuilder sql2 = new StringBuilder();
        sql2.append("union SELECT \n");
        sql2.append("distinct movp.* \n");
        sql2.append("FROM movparcela movp \n");
        sql2.append("INNER JOIN colaborador cli on cli.pessoa = movp.pessoa \n");
        sql2.append("INNER JOIN autorizacaocobrancacolaborador autcli on autcli.colaborador = cli.codigo and autcli.ativa \n");
        sql2.append("INNER JOIN movprodutoparcela mpr on mpr.movparcela = movp.codigo \n");
        sql2.append("INNER JOIN movproduto mprod on mprod.codigo = mpr.movproduto \n");
        sql2.append("INNER JOIN conveniocobranca conv ON conv.codigo = autcli.conveniocobranca \n");
        sql2.append("INNER JOIN produto p ON p.codigo = mprod.produto \n");

        StringBuilder sqlWhere = new StringBuilder();
        sqlWhere.append("WHERE movp.situacao = 'EA' \n");
        sqlWhere.append("AND movp.valorparcela > 0 \n");
        sqlWhere.append("AND movp.empresa = ").append(convenioCobrancaVO.getEmpresa().getCodigo()).append(" \n");

        if (!UteisValidacao.emptyList(listaParcelasEspecificasConsultar)) {
            //o "ANY(ARRAY)" é mais performático que o "IN"
                sqlWhere.append("AND movp.codigo = ANY(ARRAY[");
                // Adiciona os códigos das parcelas na string
                for (int i = 0; i < listaParcelasEspecificasConsultar.size(); i++) {
                    sqlWhere.append(listaParcelasEspecificasConsultar.get(i).getCodigo());
                    if (i < listaParcelasEspecificasConsultar.size() - 1) {
                        sqlWhere.append(", ");
                    }
                }
                sqlWhere.append("]) \n");
            }

        sqlWhere.append("AND '").append(Uteis.getDataJDBCTimestamp(dataMaximaLancamento)).append("' - mprod.datalancamento > '01:00:00.000' \n");
        if (convenioCobrancaVO.getEmpresa().isHabilitarReenvioAutomaticoRemessa()) {
            sqlWhere.append("AND autcli.tipoautorizacao = ").append(convenioCobrancaVO.getTipo().getTipoAutorizacao().getId()).append(" \n");
            sqlWhere.append("AND movp.nrTentativasProcessoRetentativa < ").append(convenioCobrancaVO.getEmpresa().getQtdExecucoesRetentativa()).append(" \n");
        } else {
            sqlWhere.append("AND conv.codigo = ").append(convenioCobrancaVO.getCodigo()).append(" \n");
        }

        //validar se não existe cobrança Stone Connect pendente ou paga para a parcela
        sqlWhere.append("AND not exists(select ppmp.codigo \n");
        sqlWhere.append("from pinpadpedidomovparcela ppmp \n");
        sqlWhere.append("inner join pinpadpedido pp on ppmp.pinpadpedido = pp.codigo \n");
        sqlWhere.append("where ppmp.movparcela = movp.codigo \n");
        sqlWhere.append("and pp.status in (" + StatusPinpadEnum.GERADO.getCodigo() + "," + StatusPinpadEnum.AGUARDANDO.getCodigo() + "," + StatusPinpadEnum.PAGO.getCodigo() + ") \n");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sqlWhere.append("and pp.dataregistro >= '" + sdf.format(Calendario.hoje()) + " 00:00:00.000'\n");
        sqlWhere.append("and pp.dataregistro <= '" + sdf.format(Calendario.hoje()) + " 23:59:59.999'\n");
        sqlWhere.append(")\n");

        //caso desmarcado não pode cobrar parcela que tenha boleto online pendente
        if (!convenioCobrancaVO.getEmpresa().isCobrarParcelaComBoletoGerado()) {
            sqlWhere.append("AND not exists( \n");
            sqlWhere.append("select \n");
            sqlWhere.append("b.codigo \n");
            sqlWhere.append("from boleto b \n");
            sqlWhere.append("inner join boletomovparcela bm on bm.boleto = b.codigo \n");
            sqlWhere.append("where b.situacao in (").append(SituacaoBoletoEnum.AGUARDANDO_REGISTRO.getCodigo()).append(",");
            sqlWhere.append(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO.getCodigo()).append(",");
            sqlWhere.append(SituacaoBoletoEnum.GERADO.getCodigo()).append(",");
            sqlWhere.append(SituacaoBoletoEnum.PAGO.getCodigo()).append(") \n");
            sqlWhere.append("and coalesce(bm.movparcela,0) = movp.codigo \n");
            sqlWhere.append(") \n");
        }

        //Configuração "Intervalo de dias para Retentativa de Cobrança da parcela"
        Integer diasRepescagemConfigSistema = convenioCobrancaVO.getEmpresa().getQtdDiasRepetirCobrancaParcelasRecorrencia();

        //Configuração "Limite de dias após o vencimento para parcela entrar na Retentativa de Cobrança"
        Integer diasMaximoVencimentoConfigSistema = convenioCobrancaVO.getEmpresa().getQtdDiasLimiteCobrancaParcelasRecorrencia();

        //configuração da régua de cobrança
        if (payConfigVO != null && !payConfigVO.isRetentativaAutomaticaAtivo()) {
            diasRepescagemConfigSistema = 0;
            diasMaximoVencimentoConfigSistema = 0;
        }


        if (convenioCobrancaVO.getEmpresa().isHabilitarReenvioAutomaticoRemessa()) {
            //Considerar parcelas vencendo no dia OU que vencem antes MAS nunca foram cobradas
            sqlWhere.append(" AND ( \n");
            sqlWhere.append(" movp.datavencimento >= '").append(Uteis.getDataFormatoBD(Uteis.somarDias(dia, -diasMaximoVencimentoConfigSistema))).append(" 00:00:00.000' AND movp.datavencimento <= '").append(Uteis.getDataFormatoBD(dia)).append(" 23:59:59.999' \n");

            if (convenioCobrancaVO.getEmpresa().isCobrarParcelaVencidaSemTentativaCobranca()) {
                sqlWhere.append(" OR \n");
                sqlWhere.append(" (movp.datavencimento <= '").append(Uteis.getDataFormatoBD(dia)).append(" 23:59:59.999' AND movp.nrtentativas = 0 ) \n");
            }

            sqlWhere.append(" ) \n");

            sqlWhere.append(" AND NOT EXISTS( \n");
            sqlWhere.append("SELECT tran.codigo \n");
            sqlWhere.append("FROM transacao tran \n");
            sqlWhere.append("INNER JOIN transacaomovparcela tmp ON tmp.transacao = tran.codigo  \n");
            sqlWhere.append("WHERE coalesce(tmp.movparcela,0) = movp.codigo \n");
            sqlWhere.append("AND movp.nrtentativas > 0 \n");
            sqlWhere.append("AND (tran.conveniocobranca is null OR tran.conveniocobranca = ").append(convenioCobrancaVO.getCodigo()).append(") \n");
            sqlWhere.append("AND (tran.dataprocessamento >= '").append(Uteis.getDataFormatoBD(Uteis.somarDias(dia, -diasRepescagemConfigSistema)) + " 00:00:00");
            sqlWhere.append("' OR tran.situacao = ").append(SituacaoTransacaoEnum.APROVADA.getId()).append("))");
        } else {

            /**
             * Caso esteja marcado tentativa única de cobrança
             * Verificar se não houve nenhuma tentativa de cobrança para a parcela
             * Consulta todas as parcelas que não tem tentativa de cobrança.
             * by Luiz Felipe 22/04/2020
             */
            if (convenioCobrancaVO.getEmpresa().isTentativaUnicaDeCobranca()) {
                Usuario usuarioDAO = new Usuario(this.con);
                List<UsuarioVO> listaUsuarios = usuarioDAO.obterUsuariosPacto(Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                String usuarios = "";
                if (!UteisValidacao.emptyList(listaUsuarios)) {
                    usuarios = Uteis.retornarCodigos(listaUsuarios);
                }

                if (!UteisValidacao.emptyNumber(diasMaximoVencimentoConfigSistema)) {
                    sqlWhere.append(" AND movp.datavencimento >= '").append(Uteis.getDataFormatoBD(Uteis.somarDias(dia, -diasMaximoVencimentoConfigSistema))).append(" 00:00:00.000' AND movp.datavencimento <='").append(Uteis.getDataFormatoBD(dia)).append(" 23:59:59.999' \n");
                } else {
                    sqlWhere.append(" AND movp.datavencimento <= '")
                            .append(Uteis.getDataFormatoBD(dia))
                            .append(" 23:59:59.999' \n");
                }

                //parcelas que nunca foram enviadas tentaiva 0
                sqlWhere.append("AND movp.nrtentativas = 0 \n");
                //sem tentativa de cobrança online
                sqlWhere.append("AND NOT EXISTS(SELECT tran.codigo FROM transacao tran INNER JOIN transacaomovparcela tmp ON tmp.transacao = tran.codigo WHERE coalesce(tmp.movparcela,0) = movp.codigo \n");
                if (!UteisValidacao.emptyString(usuarios)) {
                    sqlWhere.append("AND tran.usuarioresponsavel in(").append(usuarios).append(") \n");
                }
                sqlWhere.append("AND movp.nrtentativas > 0) \n");
                //sem tentativa de cobrança EDI
                sqlWhere.append("AND NOT EXISTS(SELECT ri.codigo FROM remessaitem ri INNER JOIN remessaitemmovparcela rim ON ri.codigo = rim.remessaitem WHERE coalesce(rim.movparcela,0) = movp.codigo AND movp.nrtentativas > 0) \n");
                sqlWhere.append("AND NOT EXISTS(SELECT ri.codigo FROM remessaitem ri WHERE coalesce(ri.movparcela,0) = movp.codigo AND movp.nrtentativas > 0) \n");

            } else {

                if (!UteisValidacao.emptyNumber(diasMaximoVencimentoConfigSistema)) {
                    sqlWhere.append(" AND movp.datavencimento >= '")
                            .append(Uteis.getDataFormatoBD(Uteis.somarDias(dia, -diasMaximoVencimentoConfigSistema)))
                            .append(" 00:00:00.000' AND movp.datavencimento <= '")
                            .append(Uteis.getDataFormatoBD(dia))
                            .append(" 23:59:59.999' \n");
                } else {
                    sqlWhere.append(" AND movp.datavencimento >= '")
                            .append(Uteis.getDataFormatoBD(dia))
                            .append(" 00:00:00.000' AND movp.datavencimento <= '")
                            .append(Uteis.getDataFormatoBD(dia))
                            .append(" 23:59:59.999' \n");
                }

                if (!UteisValidacao.emptyNumber(diasRepescagemConfigSistema)) {
                    sqlWhere.append(" AND NOT EXISTS( \n");
                    sqlWhere.append("SELECT tran.codigo \n");
                    sqlWhere.append("FROM transacao tran \n");
                    sqlWhere.append("INNER JOIN transacaomovparcela tmp ON tmp.transacao = tran.codigo  \n");
                    sqlWhere.append("WHERE coalesce(tmp.movparcela,0) = movp.codigo \n");
                    sqlWhere.append("AND movp.nrtentativas > 0 \n");
                    if (!convenioCobrancaVO.getTipo().getTipoTransacao().equals(TipoTransacaoEnum.NENHUMA)) {
                        sqlWhere.append("AND tran.tipo = ").append(convenioCobrancaVO.getTipo().getTipoTransacao().getId()).append(" \n");
                    }
                    sqlWhere.append("AND (tran.dataprocessamento > '").append(Uteis.getDataFormatoBD(Uteis.somarDias(dia, -diasRepescagemConfigSistema))).append(" 00:00:00.000' ");
                    sqlWhere.append("OR tran.situacao = ").append(SituacaoTransacaoEnum.APROVADA.getId()).append(")) \n");
                }
            }
        }


        //NÃO PODE TER PARCELA EM REMESSA DE DCC AGUARDANDO RETORNO
        //EDI - CIELO - BIN - GETNET
        sqlWhere.append("AND NOT EXISTS(\n");
        sqlWhere.append("SELECT  \n");
        sqlWhere.append("ri1.codigo \n");
        sqlWhere.append("FROM remessaitem ri1 \n");
        sqlWhere.append("INNER JOIN remessa r1 ON r1.codigo = ri1.remessa \n");
        sqlWhere.append("INNER JOIN remessaitemmovparcela rim1 ON rim1.remessaitem = ri1.codigo \n");
        sqlWhere.append("WHERE r1.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(") \n");
        sqlWhere.append("AND r1.tipo in (").append(TipoRemessaEnum.EDI_CIELO.getId()).append(",").append(TipoRemessaEnum.GET_NET.getId()).append(",").append(TipoRemessaEnum.DCC_BIN.getId()).append(") \n");
        sqlWhere.append("AND coalesce(rim1.movparcela, 0) = movp.codigo) \n");

        sqlWhere.append("AND NOT EXISTS(\n");
        sqlWhere.append("SELECT  \n");
        sqlWhere.append("ri1.codigo \n");
        sqlWhere.append("FROM remessaitem ri1 \n");
        sqlWhere.append("INNER JOIN remessa r1 ON r1.codigo = ri1.remessa \n");
        sqlWhere.append("WHERE r1.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(") \n");
        sqlWhere.append("AND r1.tipo in (").append(TipoRemessaEnum.EDI_CIELO.getId()).append(",").append(TipoRemessaEnum.GET_NET.getId()).append(",").append(TipoRemessaEnum.DCC_BIN.getId()).append(") \n");
        sqlWhere.append("AND coalesce(ri1.movparcela, 0) = movp.codigo) \n");


        //NÃO PODE TER PARCELA DE TRANSAÇÃO APROVADA OU CONCLUIDA COM SUCESSO
        sqlWhere.append("AND NOT EXISTS(\n");
        sqlWhere.append("SELECT  \n");
        sqlWhere.append("tr1.codigo \n");
        sqlWhere.append("FROM transacao tr1 \n");
        sqlWhere.append("INNER JOIN transacaomovparcela trm ON tr1.codigo = trm.transacao \n");
        sqlWhere.append("WHERE tr1.situacao in (").append(SituacaoTransacaoEnum.APROVADA.getId()).append(",").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(") \n");
        sqlWhere.append("AND (trm.movparcela IS not null and trm.movparcela = movp.codigo))  \n");


        //NÃO PODEM TER PARCELA EM REMESSAS DE BOLETO AINDA NAO PROCESSADAS
        //E QUE O ALUNO NÃO TENHA MAIS AUTORIZACAO DE COBRANÇA BOLETO
        sqlWhere.append("AND NOT EXISTS( \n");
        sqlWhere.append("SELECT ri.codigo \n");
        sqlWhere.append("FROM remessaitem ri \n");
        sqlWhere.append("INNER JOIN remessa re ON ri.remessa = re.codigo \n");
        sqlWhere.append("INNER JOIN remessaitemmovparcela rmov ON ri.codigo = rmov.remessaitem \n");
        sqlWhere.append("WHERE movp.codigo = rmov.movparcela \n");
        sqlWhere.append("AND ri.tipo IN (").append(TipoRemessaEnum.BOLETO.getId()).append(",").append(TipoRemessaEnum.ITAU_BOLETO.getId()).append(") \n");
        sqlWhere.append("AND re.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(") \n");
        sqlWhere.append("AND exists(select codigo from autorizacaocobrancacliente where cliente = cli.codigo and ativa and tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO.getId()).append(")) \n");

        //NÃO PODEM TER PARCELA EM PIX DE PixStatusEnum = ATIVA
        sqlWhere.append("AND NOT EXISTS ( \n");
        sqlWhere.append("SELECT \n");
        sqlWhere.append("px.codigo \n");
        sqlWhere.append("FROM pix px \n");
        sqlWhere.append("INNER JOIN pixmovparcela pxmovp ON px.codigo = pxmovp.pix \n");
        sqlWhere.append("WHERE px.status = '").append(PixStatusEnum.ATIVA.getDescricao()).append("' \n");
        sqlWhere.append("AND coalesce(pxmovp.movparcela, 0) = movp.codigo) \n");

        sqlWhere.append("AND(  \n");
        sqlWhere.append(" (autcli.tipoacobrar = ").append(TipoObjetosCobrarEnum.TUDO.getId()).append(") \n");
        String tiposPlano = "";
        for (String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoPlano) {
            tiposPlano += ",'" + tipo + "'";
        }
        sqlWhere.append(" OR (autcli.tipoacobrar = ").append(TipoObjetosCobrarEnum.APENAS_PLANOS.getId()).append(" AND p.tipoproduto IN (").append(tiposPlano.replaceFirst(",", "")).append(")) \n");
        sqlWhere.append(" OR (autcli.tipoacobrar = ").append(TipoObjetosCobrarEnum.TIPOS_PRODUTOS.getId()).append(" AND p.tipoproduto = ANY (('{'||autcli.listaobjetosacobrar||'}') :: text[])) \n");
        String tiposContrato = "";
        for (String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoContrato) {
            tiposContrato += ",'" + tipo + "'";
        }
        sqlWhere.append(" OR (autcli.tipoacobrar = ").append(TipoObjetosCobrarEnum.CONTRATOS_RENOVAVEIS_AUTO.getId()).append(" AND p.tipoproduto IN (").append(tiposContrato.replaceFirst(",", "")).append(") \n");
        sqlWhere.append(" AND ((SELECT EXISTS(SELECT codigo FROM contratorecorrencia WHERE contrato = coalesce(movp.contrato,0))) OR (SELECT EXISTS(SELECT codigo FROM contrato WHERE codigo = coalesce(movp.contrato,0) AND renovavelAutomaticamente ))))) \n");

        sql1.append(sqlWhere);
        sql2.append(sqlWhere);
        sql1.append(sql2).append(") sql");
        sql1.append(" ORDER BY datavencimento ");
        try (PreparedStatement ps = con.prepareStatement(sql1.toString())) {
            if(nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_CODIGOMOVPARCELA){
                return montarDadosApenasCodigo(ps.executeQuery());
            } else {
                return montarDadosConsulta(ps.executeQuery(), Uteis.NIVELMONTARDADOS_MINIMOS, con);
            }
        }
    }

    public static List<MovParcelaVO> montarDadosApenasCodigo(ResultSet tabelaResultado) throws Exception {
        List<MovParcelaVO> vetResultado = new ArrayList<MovParcelaVO>();
        while (tabelaResultado.next()) {
            MovParcelaVO obj = new MovParcelaVO();
            obj.setCodigo(tabelaResultado.getInt("codigo"));
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public List<BIInadimplenciaTO> consultarBIInadimplenciaTO(Integer meses, Integer empresa, Date dataBase, List<Integer> codigoConvenios, Boolean incluirContratosCancelados) throws Exception{
        List<BIInadimplenciaTO> list = new ArrayList<BIInadimplenciaTO>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT to_char(mov.datavencimento, 'MM/YYYY') as mes, to_char(mov.datavencimento, 'YYYYMM') as mesNum, sum(CASE WHEN mov.situacao = 'PG' THEN mov.valorparcela ELSE 0.0 END) valorPago,");
        sql.append(" sum(CASE WHEN mov.situacao IN ('EA' ");
        if(incluirContratosCancelados){
            sql.append(",'CA'");
        }
        sql.append(") THEN mov.valorparcela ELSE 0 END) valorEmAberto\n");
        sql.append("FROM movparcela mov\n");
        if(codigoConvenios != null && !codigoConvenios.isEmpty()){
            sql.append("INNER JOIN (");
            sql.append(" SELECT DISTINCT cli.pessoa");
            sql.append(" FROM autorizacaocobrancacliente con");
            sql.append(" INNER JOIN cliente cli ON cli.codigo = con.cliente");
            sql.append(" WHERE con.conveniocobranca IN (").append(Uteis.montarListaIN(codigoConvenios)).append(")");
            sql.append(") cons ON mov.pessoa = cons.pessoa ");
        }
        sql.append("WHERE mov.situacao IN ('PG', 'EA')\n");
        sql.append(" AND mov.parceladcc \n");
        sql.append("AND mov.datavencimento > ? AND mov.datavencimento <= ?\n");
        sql.append(" AND mov.empresa = ? \n");
        sql.append("GROUP BY to_char(mov.datavencimento, 'MM/YYYY'), to_char(mov.datavencimento, 'YYYYMM') ORDER BY mesNum\n");
        Date diaAposConsiderar = Uteis.somarMeses(dataBase, -meses);
        diaAposConsiderar = Calendario.getDataComHoraZerada(diaAposConsiderar);
        diaAposConsiderar = Uteis.obterPrimeiroDiaMes(diaAposConsiderar);
        try (PreparedStatement ps = getCon().prepareStatement(sql.toString())) {
            ps.setDate(1, Uteis.getDataJDBC(diaAposConsiderar));
            ps.setDate(2, Uteis.getDataJDBC(Uteis.getDataComUltimaHora(dataBase)));
            ps.setInt(3, empresa);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    list.add(new BIInadimplenciaTO(rs.getString("mes"), rs.getBigDecimal("valorEmAberto"), rs.getBigDecimal("valorPago"), BigDecimal.ZERO));
                }
            }
        }
        if(list.isEmpty()){
            Date aux = dataBase;
            for(int i = 0; i < (meses + 1); i++){
                list.add(new BIInadimplenciaTO(Uteis.getDataAplicandoFormatacao(aux, "MM/yyyy"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO));
                aux = Uteis.somarMeses(aux, (-(i+1)));
            }
        }
        return list;
    }
    
    public Date consultarUltimoVencimentoContrato(int contrato) throws Exception { 
        StringBuilder sql = new StringBuilder("select  mp.datavencimento from movparcela mp inner join movprodutoparcela mpp on mpp.movparcela = mp.codigo inner join movproduto mov on mov.codigo = mpp.movproduto  inner join produto pro on pro.codigo = mov.produto ");
        sql.append(" where pro.tipoproduto = 'PM' and mp.contrato =  ").append(contrato);
        sql.append(" and mp.descricao similar to 'PARCELA (1|2|3|4|5|6|7|8|9|RENEGOCIADA)%' and mp.situacao not in ('CA', 'RG') ");
        sql.append(" order by 1 desc limit 1 ");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return rs.getDate("datavencimento");
            }
        }
        return null;
    }
    
    public boolean verificarExisteParcelaContratoMes(Integer contrato, Date dia) throws Exception{
        String sql = "select codigo from movparcela where contrato = ? and datavencimento between ? and ? "
                + " and situacao in ('EA','PG') and descricao like 'PARCELA%'";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, contrato);
            stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Uteis.obterPrimeiroDiaMesPrimeiraHora(dia)));
            stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(Uteis.obterUltimoDiaMesUltimaHora(dia)));
            try (ResultSet rs = stm.executeQuery()) {
                return rs.next();
            }
        }
    }
    
    public boolean existeParcelaEmSituacaoPorContrato(Integer contrato, String situacao) throws  Exception{
        String sql = "SELECT codigo FROM movparcela  WHERE contrato = ? AND situacao = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, contrato);
            ps.setString(2, situacao);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() && rs.getInt("codigo") > 0;
            }
        }
    }

    public List<MovParcelaVO> consultarParcelasVencidasPessoa(Integer pessoa, int nivelMontarDados) throws  Exception{
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM MovParcela WHERE pessoa =").append(pessoa);
        sqlStr.append(" and situacao='EA' \n");
        sqlStr.append(" AND datavencimento <= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'\n");
        sqlStr.append(" ORDER BY movparcela.datavencimento");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<MovParcelaVO> consultarParcelasVencidasPorMatricula(String matricula, int nivelMontarDados) throws  Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT mp.* FROM MovParcela mp\n")
                .append("   LEFT JOIN cliente cli ON cli.pessoa = mp.pessoa\n")
                .append("WHERE cli.matricula = '").append(matricula).append("'\n")
                .append(" and mp.situacao='EA' \n")
                .append(" AND datavencimento <= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'\n")
                .append(" ORDER BY mp.datavencimento");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<MovParcelaVO> consultarParcelasPorMatriculaOuCpf(String matriculaCpf, int nivelMontarDados, boolean somenteVencidas) throws Exception {
        Integer matricula = 0;
        try{
            matricula = Integer.valueOf(matriculaCpf);
        }catch (Exception ignore){}

        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT mp.*, pl.descricao as nomePlano FROM MovParcela mp\n")
                .append("   INNER JOIN pessoa p ON p.codigo = mp.pessoa\n")
                .append("   INNER JOIN cliente cli ON cli.pessoa = p.codigo\n")
                .append("   LEFT JOIN contrato c ON c.codigo = mp.contrato\n")
                .append("   LEFT JOIN plano pl ON pl.codigo = c.plano\n")
                .append("WHERE (cli.codigomatricula = '").append(matricula).append("'\n")
                .append("or (p.cfp = '").append(Uteis.formatarCpfCnpj(matriculaCpf, false)).append("' or p.cfp = '").append(Uteis.formatarCpfCnpj(matriculaCpf, true)).append("')) \n")
                .append(" and mp.situacao='EA' \n");
        if (somenteVencidas) {
            sqlStr.append(" AND datavencimento <= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'\n");
        }
        sqlStr.append(" ORDER BY mp.datavencimento");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<MovParcelaVO> consultarPrimeirasParcelasContrato(Integer contrato, int limit) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT mpar.* from movproduto mprod\n");
        sqlStr.append("LEFT JOIN produto prod ON mprod.produto = prod.codigo\n");
        sqlStr.append("LEFT JOIN movprodutoparcela mpp ON mpp.movproduto = mprod.codigo\n");
        sqlStr.append("LEFT JOIN movparcela mpar ON mpp.movparcela = mpar.codigo\n");
        sqlStr.append("WHERE mprod.contrato = ").append(contrato).append("\n");
        sqlStr.append("AND prod.tipoproduto = 'PM'\n");
        sqlStr.append("ORDER by anoreferencia, mesreferencia\n");
        sqlStr.append("LIMIT ").append(limit).append(";");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
            }
        }
    }


    public List<MovParcelaVO> consultarParcelasContratoAlterarData(Integer contrato) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("select * from movparcela\n");
        sqlStr.append("WHERE situacao = 'EA' and contrato = ").append(contrato).append("\n");
        sqlStr.append("AND datavencimento > '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'\n");
        sqlStr.append("order by datavencimento, descricao \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
            }
        }
    }

    @Override
    public boolean parcelaEmAberto(MovParcelaVO parcela) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT M.codigo ");
        sql.append(" FROM movparcela M ");
        sql.append(" WHERE M.codigo = ? ");
        sql.append(" AND   M.situacao = 'EA' ");
        sql.append(" LIMIT 1" );

        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, parcela.getCodigo());
            return ps.executeQuery().next();
        }
    }

    @Override
    public List<MovParcelaVO> consultarPrimeirasParcelasContrato(Integer contrato, boolean forcarAdesao) throws Exception {
        String sqlStr = "SELECT mpar.* FROM movparcela mpar\n" +
                " INNER JOIN contrato con ON mpar.contrato = con.codigo\n" +
                " LEFT JOIN pagamentomovparcela pgt on mpar.codigo = pgt.movparcela\n"+
                " WHERE pgt.codigo is null AND con.codigo = " + contrato + "\n"+
                " AND mpar.situacao = 'EA' \n";
        if(!forcarAdesao){
            sqlStr = sqlStr+ " AND ((con.vigenciade::DATE = mpar.datavencimento::DATE AND con.vigenciade::DATE <= current_timestamp::DATE) or (con.datalancamento::DATE = mpar.datavencimento::DATE));";
        }else{
            sqlStr = sqlStr+ " AND con.datalancamento::DATE = mpar.datavencimento::DATE;";
        }
        List<MovParcelaVO> parcelas;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                parcelas = (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con));
                if (parcelas.isEmpty() && forcarAdesao) {
                    StringBuilder sql = new StringBuilder();
                    sql.append(" SELECT mpar.* FROM movparcela mpar\n");
                    sql.append(" INNER JOIN contrato con ON mpar.contrato = con.codigo\n");
                    sql.append(" LEFT JOIN pagamentomovparcela pgt on mpar.codigo = pgt.movparcela\n");
                    sql.append(" LEFT JOIN movprodutoparcela mpp on mpp.movparcela = mpar.codigo \n");
                    sql.append(" LEFT JOIN movproduto mp on mp.codigo = mpp.movproduto \n");
                    sql.append(" LEFT JOIN produto p on p.codigo = mp.produto \n");
                    sql.append(" WHERE pgt.codigo is null and con.codigo = " + contrato + "\n");
                    sql.append(" and p.tipoproduto = '").append(TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo()).append("' ");
                    sql.append(" AND con.datalancamento::DATE < mpar.datavencimento::DATE\n");
                    sql.append(" AND mpar.situacao = 'EA'\n");
                    sql.append(" ORDER BY mpar.datavencimento\n");

                    sqlStr = sql.toString();
                    try (ResultSet rs = stm.executeQuery(sqlStr)) {
                        parcelas = (montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con));
                    }
                }
            }
        }

        return parcelas;
    }

    public List<MovParcelaVO> consultarApenasDescricaoPorTransacao(Integer transacao) throws Exception {
        List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("mp.codigo, \n");
        sql.append("mp.descricao,  \n");
        sql.append("mp.pessoa,  \n");
        sql.append("p.nome  \n");
        sql.append("FROM MovParcela mp \n");
        sql.append("inner join transacaomovparcela tm on mp.codigo = tm.movparcela \n");
        sql.append("inner join pessoa p on p.codigo = mp.pessoa \n");
        sql.append("WHERE tm.transacao = ? ");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setInt(1, transacao);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                while (tabelaResultado.next()) {
                    MovParcelaVO mp = new MovParcelaVO();
                    mp.setCodigo(tabelaResultado.getInt("codigo"));
                    mp.setDescricao(tabelaResultado.getString("descricao"));
                    mp.setPessoa(new PessoaVO());
                    mp.getPessoa().setCodigo(tabelaResultado.getInt("pessoa"));
                    mp.getPessoa().setNome(tabelaResultado.getString("nome"));
                    parcelas.add(mp);
                }
            }
        }
        return parcelas;
    }

    public void totalizadorBIInadimplencia(InadimplenciaTO totalizador, Integer codigoEmpresa,
                                           Date dataInicio, Date dataFim, String situacaoParcela,
                                           Date dataMatricula, Integer diaPagamento) throws Exception {
        try (ResultSet rs = biInadimplenciaResultSet(codigoEmpresa, dataInicio, dataFim, situacaoParcela, dataMatricula, diaPagamento, true, false)) {
            while (rs.next()) {
                totalizador.setQuantidade(rs.getInt("qtd"));
                totalizador.setQuantidadeAlunos(rs.getInt("qtdAlunos"));
                totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
            }
        }
    }

    public List<ResumoPessoaRelBIInadimplenciaVO> listaBIInadimplencia(Integer codigoEmpresa,
                                                                       Date dataInicio, Date dataFim, String situacaoParcela,
                                                                       Date dataMatricula, Integer diaPagamento, Boolean distincAlunos) throws Exception {

        List<ResumoPessoaRelBIInadimplenciaVO> lista = new ArrayList<ResumoPessoaRelBIInadimplenciaVO>();
        try (ResultSet rs = biInadimplenciaResultSet(codigoEmpresa, dataInicio, dataFim, situacaoParcela, dataMatricula, diaPagamento, false, distincAlunos)) {
            while (rs.next()) {
                ResumoPessoaRelBIInadimplenciaVO obj = new ResumoPessoaRelBIInadimplenciaVO();

                obj.setMatricula(rs.getString("matricula"));
                obj.setNome(rs.getString("nome"));
                obj.setCliente(rs.getInt("cliente"));
                obj.setTelefone(rs.getString("telefones"));
                obj.setSituacaoCliente(rs.getString("situacao"));
                obj.setContrato(rs.getInt("contrato"));
                obj.setEmail(rs.getString("emails"));
                obj.setSituacaoParcela(rs.getString("situacaoParcelaNormal"));
                obj.setDescricaoParcela(rs.getString("descricaoParcela"));
                obj.setDataVencimentoParcela(rs.getDate("dataVencimento"));
                obj.setValorParcela(rs.getDouble("valorParcela"));
                obj.setEmpresa(rs.getString("nomeempresa"));

                if (distincAlunos) {
                    obj.setQtdParcelas(rs.getInt("qtdParcelas"));
                } else {
                    obj.setMovParcela(rs.getInt("movparcela"));
                    obj.setSituacaoParcelaDiaPagamento(rs.getString("situacaoParcela"));
                    obj.setDataMatricula(rs.getDate("dataMatricula"));
                    Map<String, Object> camposEspeciais = obterCamposEspeciais(obj.getMovParcela());
                    obj.setPrefixo((String) camposEspeciais.get("prefixo"));

                    Map<String, Object> dadosUltimaCobranca = obterDadosUltimaCobranca(obj.getMovParcela());
                    obj.setConvenio((String) dadosUltimaCobranca.get("convenio"));
                    obj.setUltimaCobranca((String) dadosUltimaCobranca.get("ultimaCobranca"));
                    obj.setRetorno((String) dadosUltimaCobranca.get("retorno"));

                    obj.setDataPagamentoParcela(rs.getDate("dataPagamento"));
                    obj.setNrtentativas(rs.getInt("nrtentativas"));
                    obj.setDataultimatentativa(rs.getDate("dataultimatentativa"));
                    obj.setMeioultimatentativa(rs.getString("meioultimatentativa"));
                    obj.setCodigoretorno(rs.getString("codigoretorno"));
                    obj.setMotivoretorno(rs.getString("motivoretorno"));
                    obj.setDataCancelamentoParcela(rs.getDate("dataCancelamento"));

                }
                lista.add(obj);
            }
        }

        return lista;
    }

    public String listaCodigosParcelaBIInadimplenciaDetalhado(Integer codigoEmpresa,
                                                              Date dataInicio, Date dataFim, String situacaoParcela,
                                                              Date dataMatricula, Integer diaPagamento) throws Exception {
        StringBuilder sql = getSQLBIInadimplenciaResultSet(codigoEmpresa, dataInicio, dataFim, situacaoParcela, dataMatricula, diaPagamento, false, true, true);
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("parcelas");
                } else {
                    return "";
                }
            }
        }
    }

    private StringBuilder getSQLBIInadimplenciaResultSet(Integer codigoEmpresa,
                                               Date dataInicio, Date dataFim, String situacaoParcela,
                                               Date dataMatricula, Integer diaPagamento, boolean count, Boolean distinctAlunos, boolean exportarDetalhado) throws Exception {

        StringBuilder sql = new StringBuilder();

        if (exportarDetalhado) {
            sql.append("select array_to_string(array(SELECT distinct(movparcela) \n");
        } else {
            if (count) {
                sql.append("SELECT \n");
                sql.append("count(movparcela) as qtd, \n");
                sql.append("count(distinct (nome||coalesce(cliente,0))) as qtdAlunos,  \n");
                sql.append("sum(round(valorparcela::NUMERIC, 2)) as valor \n");
            } else if (distinctAlunos) {
                sql.append("SELECT \n");
                sql.append("nome, matricula, cliente, situacao, telefones, contrato, emails,  nomeempresa, \n");
                sql.append("count(*) qtdParcelas, array_to_string(array_agg(descricaoparcela order by movparcela), ', ') as descricaoParcela, \n");
                sql.append("array_to_string(array_agg(situacaoparcelanormal order by movparcela), ', ') as situacaoparcelanormal, \n");
                sql.append("sum(valorparcela) as valorParcela, max(datavencimento) as datavencimento \n");
            } else {
                sql.append("SELECT \n");
                sql.append("* \n");
            }
        }

        sql.append("FROM ( \n");
        sql.append("select \n");
        sql.append("distinct on (m.codigo) m.codigo as movparcela, \n");
        sql.append("emp.nome as nomeempresa, \n");
        sql.append("case\n");
        sql.append(" when (m.vendaavulsa > 0\n");
        sql.append("  and (dw.nomecliente is null\n");
        sql.append("   or dw.nomecliente = '')) then vendaavulsa.nomecomprador\n");
        sql.append(" when necp.contrato is not null or m.personal is not null then pesev.nome\n");
        sql.append(" else dw.nomecliente\n");
        sql.append("end as nome,\n");
        sql.append("dw.codigocontrato as contrato, \n");
        sql.append("case when vendaavulsa.colaborador > 0 or m.personal > 0 then col.codigo else dw.codigocliente end as cliente,  \n");
        sql.append("case when vendaavulsa.colaborador > 0 or m.personal > 0  then 'cl-'||col.codigo else dw.matricula::text end as matricula, \n");
        sql.append("dw.situacao, \n");
        sql.append("m.situacao as situacaoParcelaNormal, \n");
        sql.append("m.dataVencimento, \n");
        sql.append("mp.datapagamento, \n");
        sql.append("m.descricao as descricaoParcela, \n");
        if (!count) {
            sql.append("array_to_string(array(SELECT email FROM email WHERE email.pessoa = m.pessoa AND length(email.email) > 0), ';') as emails, \n");
            sql.append("array_to_string(array(SELECT numero FROM telefone WHERE telefone.pessoa = m.pessoa AND length(telefone.numero) > 0), ', ') as telefones, \n");
        }
        sql.append("m.valorparcela as valorparcela, \n");
        if  (!count) {
            sql.append("(select nrtentativas from movparcelaresultadocobranca mr where mr.movparcela = m.codigo order by mr.codigo desc limit 1) as nrtentativas,\n");
            sql.append("(select motivoretorno from movparcelaresultadocobranca mr where mr.movparcela = m.codigo order by mr.codigo desc limit 1) as motivoretorno,\n");
        }
        sql.append("oo.dataoperacao as dataCancelamento, \n");
        if (!UteisValidacao.emptyNumber(diaPagamento)) {
            sql.append("CASE \n");
            sql.append("WHEN (m.situacao = 'PG' AND mp.datapagamento :: DATE > (SELECT date_trunc('month', m.datavencimento :: DATE) + \n");
            sql.append("        INTERVAL '1 month' + INTERVAL '").append(diaPagamento - 1).append(" day')) \n");
            sql.append("THEN 'EA' \n");
            sql.append("ELSE m.situacao END                          AS situacaoParcela, \n");
        } else {
            sql.append("m.situacao as situacaoParcela, \n");
        }
        sql.append("dw.dataMatricula, \n");
        sql.append("m.empresa \n");
        sql.append("FROM movparcela m \n");
        sql.append("LEFT JOIN observacaooperacao oo on oo.movparcela = m.codigo and oo.tipooperacao = 'PC'\n");
        sql.append("LEFT JOIN empresa emp on emp.codigo = m.empresa \n");
        sql.append("LEFT JOIN situacaoclientesinteticodw  dw on dw.codigopessoa = m.pessoa \n");

        sql.append("left join vendaavulsa on vendaavulsa.codigo = m.vendaavulsa\n");
        sql.append("left join colaborador col on col.pessoa = m.pessoa \n");
        sql.append("left join pessoa pesev on pesev.codigo = m.pessoa\n");

        sql.append("left join negociacaoeventocontratoparcelas necp on necp.parcela = m.codigo\n");

        sql.append("LEFT JOIN pagamentomovparcela pm ON pm.movparcela = m.codigo \n");
        sql.append("LEFT JOIN movpagamento mp ON mp.codigo = pm.movpagamento \n");

        sql.append("WHERE\n");
        sql.append("m.datavencimento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql.append("AND m.empresa = ").append(codigoEmpresa).append(" \n");
        }

        if (!UteisValidacao.emptyString(situacaoParcela)) {
            sql.append("AND m.situacao ").append(situacaoParcela).append(" \n");
        }

        sql.append("AND m.situacao not in ('RG') \n");
        sql.append("AND (m.situacao IN ('EA', 'PG') OR (oo.dataoperacao::DATE >= m.datavencimento::DATE)) \n");
        sql.append(") as sql \n");
        sql.append("WHERE 1 = 1 \n");

        if (dataMatricula != null) {
            sql.append("AND dataMatricula::date >= '").append(Uteis.getData(dataMatricula)).append("' \n");
        }

        if (exportarDetalhado) {
            sql.append(" ), ',', '') as parcelas");
        } else if (!count && distinctAlunos) {
            sql.append("group by nome, matricula, cliente, situacao, telefones, contrato, emails,  nomeempresa ");
        }
        return sql;
    }

    private ResultSet biInadimplenciaResultSet(Integer codigoEmpresa,
                                               Date dataInicio, Date dataFim, String situacaoParcela,
                                               Date dataMatricula, Integer diaPagamento, boolean count, boolean distinctAlunos) throws Exception {
        StringBuilder sql = getSQLBIInadimplenciaResultSet(codigoEmpresa, dataInicio, dataFim, situacaoParcela, dataMatricula, diaPagamento, count, distinctAlunos, false);
        PreparedStatement stm = con.prepareStatement(sql.toString());
        return stm.executeQuery();
    }
    
    public boolean parcelaPagaChequeDevolvido(final Integer codigoParcela) throws Exception{
        String sql = "select exists(select mpro.codigo from movprodutoparcela  mpp inner join movparcela par on par.codigo = mpp.movparcela inner join movproduto mpro on mpro.codigo = mpp.movproduto inner join produto pro on pro.codigo = mpro.produto where  par.codigo = ? and pro.tipoproduto = 'CH')";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, codigoParcela);
            try (ResultSet rs = stm.executeQuery()) {
                rs.next();
                return rs.getBoolean(1);
            }
        }
    }
    
    public void validarMovParcelaComTransacaoConcluidaOuPendente(final Integer codigoParcela) throws  Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("t.codigo \n");
        sql.append("from transacao t \n");
        sql.append("inner join transacaomovparcela tm on tm.transacao = t.codigo \n");
        sql.append("where tm.movparcela = ").append(codigoParcela).append(" \n");
        sql.append("and (t.situacao in (2,4) or (t.situacao = 3 and t.tipo = 3 and t.codigoretorno in('0','00'))) \n");
        boolean existeTransacao = existe(sql.toString(), this.con);
        if (existeTransacao) {
            throw new ConsistirException("Parcela " + codigoParcela +" está vinculada a uma transação pendente ou que teve aprovação. Por favor, verifique localizando a parcela e tentando realizar a operação novamente.");
        }
    }

    public boolean temParcelasAberto(Integer cliente) throws Exception{
        String sql = "select * from movparcela mp\n" +
                    "inner join cliente c on c.pessoa = mp.pessoa \n" +
                    "where c.codigo = ? " +
                    "and mp.datavencimento <= ?\n" +
                    "and mp.situacao = 'EA'";

        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, cliente);
            stm.setDate(2, Uteis.getDataJDBC(Calendario.hoje()));
            try (ResultSet rs = stm.executeQuery()) {
                return rs.next();
            }
        }

    }
    
    public boolean validarSituacaoParcela(Integer codigoParcela, String situacao) throws  Exception{
        String sql = "SELECT codigo FROM movparcela  WHERE codigo = ? AND situacao = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoParcela);
            ps.setString(2, situacao);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() && rs.getInt("codigo") > 0;
            }
        }
    }

    private List<Integer> obterEmpresasConvenio(List<Integer> convenios) throws Exception {
        ConvenioCobrancaEmpresa convenioCobrancaEmpresaDAO = new ConvenioCobrancaEmpresa(con);
        List<Integer> lista = convenioCobrancaEmpresaDAO.obterEmpresasConvenio(convenios);
        convenioCobrancaEmpresaDAO = null;
        return lista;
    }

    public void preencherCamposEspeciais(List<MovParcelaVO> parcelas) throws Exception{
        for(MovParcelaVO parcela : parcelas){
            Map<String, Object> camposEspeciais = obterCamposEspeciais(parcela.getCodigo());
            parcela.setPrefixo((String) camposEspeciais.get("prefixo"));
        }
    }

    public Map<String, Object> obterCamposEspeciais(Integer parcela) throws Exception{
        Map<String, Object> campos = new HashMap<String, Object>();
        try (ResultSet rsParcela = criarConsulta("select p.prefixo, mpc.contrato, mpc.pessoa, mpc.descricao, mpc.empresa  from produto p\n" +
                "inner join movproduto mp on p.codigo = mp.produto\n" +
                "inner join movprodutoparcela mpp on mpp.movproduto = mp.codigo\n" +
                "inner join movparcela mpc on mpp.movparcela = mpc.codigo\n" +
                "where mpp.movparcela = " + parcela, con)) {
            if (rsParcela.next()) {
                campos.put("prefixo", rsParcela.getString("prefixo"));
                campos.put("descricao", rsParcela.getString("descricao"));
                campos.put("contrato", rsParcela.getInt("contrato"));
                campos.put("empresa", rsParcela.getInt("empresa"));
                campos.put("pessoa", rsParcela.getInt("pessoa"));
            }
        }
        return campos;
    }

    public Map<String, Object> obterDadosUltimaCobranca(Integer parcela) throws Exception{
        Map<String, Object> campos = new HashMap<String, Object>();
        try (ResultSet rsParcela = criarConsulta("select \n" +
                " c.descricao as convenio, \n" +
                " ri.props,\n" +
                " t.codigoretorno,\n" +
                " t.dataprocessamento,\n" +
                " r.dataregistro\n" +
                " from movparcelatentativaconvenio m \n" +
                " inner join conveniocobranca c on m.conveniocobranca = c.codigo\n" +
                " left join remessaitem ri on m.movparcela = ri.movparcela and m.nrtentativaparcela = ri.nrtentativaparcela\n" +
                " left join remessa r on r.codigo = ri.remessa\n" +
                " left join transacaomovparcela tm on tm.movparcela = m.movparcela and tm.nrtentativaparcela = m.nrtentativaparcela\n" +
                " left join transacao t on t.codigo = tm.transacao\n" +
                " where m.movparcela = " + parcela +
                " order by m.nrtentativaparcela desc limit 1", con)) {
            if (rsParcela.next()) {
                campos.put("convenio", rsParcela.getString("convenio"));
                if (!UteisValidacao.emptyString(rsParcela.getString("props"))) {
                    Map<String, String> props = Uteis.obterMapFromString(rsParcela.getString("props"));
                    campos.put("retorno", props.get("StatusVenda"));
                } else if (!UteisValidacao.emptyString(rsParcela.getString("codigoretorno"))) {
                    campos.put("retorno", rsParcela.getString("codigoretorno"));
                }

                if (rsParcela.getDate("dataregistro") != null) {
                    campos.put("ultimaCobranca", Uteis.getData(rsParcela.getDate("dataregistro")));
                } else if (rsParcela.getDate("dataprocessamento") != null) {
                    campos.put("ultimaCobranca", Uteis.getData(rsParcela.getDate("dataprocessamento")));
                }
            }
        }
        return campos;
    }

    public RecorrenciaClienteTO parcelaRecorrencia(MovParcelaVO parcela, boolean consultarFormaPagamento) throws Exception{
        RecorrenciaClienteTO recorrenciaTO = new RecorrenciaClienteTO();
        recorrenciaTO.setCodigoParcela(parcela.getCodigo());
        Integer codigoPessoa = 0;
        Integer codigoContrato = 0;
        Integer codigoEmpresa = 0;
        recorrenciaTO.setDescricaoParcela(parcela.getDescricao());
        recorrenciaTO.setValor(parcela.getValorParcela());
        recorrenciaTO.setDataVencimento(parcela.getDataVencimento());


        Map<String, Object> camposEspeciais = obterCamposEspeciais(parcela.getCodigo());
        recorrenciaTO.setDescricaoParcela((String) camposEspeciais.get("descricao"));
        recorrenciaTO.setTipo((String) camposEspeciais.get("prefixo"));
        parcela.setDescricao((String) camposEspeciais.get("descricao"));
        codigoContrato = (Integer) camposEspeciais.get("contrato");
        codigoPessoa = (Integer) camposEspeciais.get("pessoa");
        codigoEmpresa = (Integer) camposEspeciais.get("empresa");

        recorrenciaTO.setCodigoContrato(codigoContrato);
        try (ResultSet rs = criarConsulta("select c.codigo, matricula, nome, cfp from cliente c " +
                " inner join pessoa p on c.pessoa = p.codigo " +
                " where p.codigo = " + codigoPessoa, con)) {
            if (rs.next()) {
                recorrenciaTO.setCpf(rs.getString("cfp"));
                recorrenciaTO.setNomeCliente(rs.getString("nome"));
                recorrenciaTO.setMatricula(rs.getString("matricula"));
                recorrenciaTO.setCodigoCliente(rs.getInt("codigo"));
            }
        }

        try (ResultSet rsContrato = criarConsulta("select c.vigenciade, c.vigenciaateajustada, cd.numeromeses, c.datalancamento from contrato c \n" +
                " inner join contratoduracao cd on cd.contrato = c.codigo \n" +
                " where c.codigo = " + codigoContrato, con)) {
            if (rsContrato.next()) {
                recorrenciaTO.setDataInicio(rsContrato.getDate("vigenciade"));
                recorrenciaTO.setDataTermino(rsContrato.getDate("vigenciaateajustada"));
                recorrenciaTO.setDataLancamento(rsContrato.getDate("datalancamento"));
                recorrenciaTO.setDuracao(rsContrato.getInt("numeromeses"));
            }
        }

        try (ResultSet rsEmpresa = criarConsulta("select nome from empresa where codigo = " + codigoEmpresa, con)) {
            if (rsEmpresa.next()) {
                recorrenciaTO.setEmpresa(rsEmpresa.getString("nome"));
            }
        }


        //descobrir forma de pagamento
        StringBuilder sqlForma = new StringBuilder();
        sqlForma.append("select \n");
        sqlForma.append("distinct(fp.descricao) as forma \n");
        sqlForma.append("from movpagamento mp \n");
        sqlForma.append("inner join formapagamento fp on fp.codigo = mp.formapagamento \n");
        sqlForma.append("inner join pagamentomovparcela pm on pm.movpagamento = mp.codigo \n");
        sqlForma.append("where pm.movparcela = ").append(parcela.getCodigo());
        try (ResultSet rsForma = criarConsulta(sqlForma.toString(), con)) {
            if (rsForma.next()) {
                recorrenciaTO.setDescricaoFormaPagamento(rsForma.getString("forma"));
            } else {
                recorrenciaTO.setDescricaoFormaPagamento("");
            }
        }

        Integer numeroParcela = parcela.getNumeroParcela();
        recorrenciaTO.setNrParcela(numeroParcela == null ? 1 : numeroParcela);

        return recorrenciaTO;
    }

    public int numeroPendenciasImportadas(boolean pagos, boolean embordero) throws Exception{
        try (ResultSet resultSet = criarConsulta(" select count(distinct pessoa) as t from movparcela where descricao  like '%PENDENTE%' " +
                (pagos ? " and situacao = 'PG' " : "") + (embordero ? "  and descricao like '%BORDERÔ%' AND movparcela.situacao = 'EA'" : ""), con)) {
            if (resultSet.next()) {
                return resultSet.getInt("t");
            }
        }
        return 0;
    }

    public String pendenciasImportadas(boolean pagos, boolean embordero) throws Exception{
        String sql = "select distinct e.idexterno, matricula, pessoa.nome, cfp from movparcela  \n" +
                "INNER JOIN pessoa on pessoa.codigo = movparcela.pessoa\n" +
                "inner join cliente on cliente.pessoa = pessoa.codigo\n" +
                "inner join empresa e on cliente.empresa = e.codigo\n" +
                "where descricao  like '%PENDENTE%' "
                + (pagos ? " and movparcela.situacao = 'PG' ": "")
                + (embordero ? "  and descricao like '%BORDERÔ%' AND movparcela.situacao = 'EA'" : "")
                + " order by pessoa.nome ";

        StringBuilder resultado;
        try (ResultSet rs = criarConsulta(sql, con)) {
            resultado = new StringBuilder();
            while (rs.next()) {
                resultado.append(rs.getString("idexterno")).append(";");
                resultado.append(rs.getString("matricula")).append(";");
                resultado.append(rs.getString("nome")).append(";");
                resultado.append(rs.getString("cfp")).append(";\n");
            }
        }
        return resultado.toString();
    }

    public MovParcelaVO criarParcelaMultaJuros(MovParcelaVO parcelaParaPagar, Double valorMulta, Double valorJuros, UsuarioVO usuarioVO) throws Exception {
        return criarParcelaMultaJuros(parcelaParaPagar, valorMulta, valorJuros, usuarioVO, true);
    }

    public MovParcelaVO criarParcelaMultaJuros(MovParcelaVO parcelaParaPagar, Double valorMulta, Double valorJuros, UsuarioVO usuarioVO, boolean controlaTransacao) throws Exception {
        MovProduto movProdutoDAO = null;
        VendaAvulsa vendaAvulsaDAO = null;
        ItemVendaAvulsa itemVendaAvulsaDAO = null;
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            movProdutoDAO = new MovProduto(this.con);
            vendaAvulsaDAO = new VendaAvulsa(this.con);
            itemVendaAvulsaDAO = new ItemVendaAvulsa(this.con);
            tratarParcelasJurosEmultaJaGeradas(parcelaParaPagar);

            VendaAvulsaVO vendaAvulsaVOMultaJuros = criarVendaAvulsaVOMultaJuros(parcelaParaPagar, usuarioVO);

            MovParcelaVO movParcelaMultaJuros = criarMovParcelaMultaJuros(parcelaParaPagar, vendaAvulsaVOMultaJuros, usuarioVO);
            movParcelaMultaJuros.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorMulta + valorJuros));

            MovProdutoVO movProdutoMultaJuros = movProdutoDAO.criarMovProdutoMultaJuros(parcelaParaPagar, vendaAvulsaVOMultaJuros, valorMulta, valorJuros, usuarioVO, 1.0);

            MovProdutoParcelaVO movProdutoParcelaMultaJuros = criarMovProdutoParcelaMultaJuros(movProdutoMultaJuros, parcelaParaPagar);

            movProdutoMultaJuros.getMovProdutoParcelaVOs().add(movProdutoParcelaMultaJuros);
            movParcelaMultaJuros.getMovProdutoParcelaVOs().add(movProdutoParcelaMultaJuros);

            ItemVendaAvulsaVO itemVendaAvulsaMultaJuros = criarItemVendaAvulsaMultaJuros(movProdutoMultaJuros, usuarioVO);
            itemVendaAvulsaMultaJuros.setVendaAvulsaVO(vendaAvulsaVOMultaJuros);
            vendaAvulsaVOMultaJuros.getItemVendaAvulsaVOs().add(itemVendaAvulsaMultaJuros);
            vendaAvulsaVOMultaJuros.getMovProdutoVOs().add(movProdutoMultaJuros);
            vendaAvulsaVOMultaJuros.getMovParcelaVOs().add(movParcelaMultaJuros);
            vendaAvulsaVOMultaJuros.setValorTotal(Uteis.arredondarForcando2CasasDecimais(vendaAvulsaVOMultaJuros.getValorTotal()));

            vendaAvulsaDAO.incluirVendaAvulsaParaEdicaoPagamento(vendaAvulsaVOMultaJuros);
            itemVendaAvulsaDAO.incluirItemVendaAvulsas(vendaAvulsaVOMultaJuros.getCodigo(), vendaAvulsaVOMultaJuros.getItemVendaAvulsaVOs());
            movProdutoMultaJuros.setVendaAvulsa(vendaAvulsaVOMultaJuros.getCodigo());
            movProdutoDAO.incluirSemCommit(movProdutoMultaJuros);
            movProdutoParcelaMultaJuros.setMovProduto(movProdutoMultaJuros.getCodigo());

            incluirComProdutosSemCommit(movParcelaMultaJuros);
            if (controlaTransacao) {
                con.commit();
            }
            return movParcelaMultaJuros;
        } catch (Exception ex) {
            ex.printStackTrace();
            if (controlaTransacao) {
                con.rollback();
            }
            throw ex;
        } finally {
            movProdutoDAO = null;
            vendaAvulsaDAO = null;
            itemVendaAvulsaDAO = null;
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    public void montarMultaJurosParcelaVencida(EmpresaVO empresaVO, TipoCobrancaEnum tipoCobrancaEnum, List<MovParcelaVO> listaParcelas, Date dataPagamento) throws Exception {
        try {
            Uteis.logar("montarMultaJurosParcelaVencida | Inicio...");
            if (empresaVO != null && tipoCobrancaEnum != null && empresaVO.getCobrarAutomaticamenteMultaJuros() &&
                    ((empresaVO.getCobrarAutomaticamenteMultaJuros() && tipoCobrancaEnum.equals(TipoCobrancaEnum.BOLETO)) ||
                            (empresaVO.getCobrarAutomaticamenteMultaJuros() && tipoCobrancaEnum.equals(TipoCobrancaEnum.BOLETO_ONLINE)) ||
                            (empresaVO.isCobrarMultaJurosDCO() && tipoCobrancaEnum.equals(TipoCobrancaEnum.EDI_DCO)) ||
                            (empresaVO.isCobrarMultaJurosDCC() && tipoCobrancaEnum.equals(TipoCobrancaEnum.EDI_DCC)) ||
                            (empresaVO.isCobrarMultaJurosTransacao() && tipoCobrancaEnum.equals(TipoCobrancaEnum.ONLINE)) ||
                            (empresaVO.isCobrarMultaJurosPix() && tipoCobrancaEnum.equals(TipoCobrancaEnum.PIX)))) {
                Uteis.logar("montarMultaJurosParcelaVencida | Entrou...");
                montarMultaJurosParcelaVencida(empresaVO, listaParcelas, dataPagamento, false, 1.0, null);
                Uteis.logar("montarMultaJurosParcelaVencida | Processou...");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar("montarMultaJurosParcelaVencida | ERRO: " + ex.getMessage());
            throw ex;
        } finally {
            Uteis.logar("montarMultaJurosParcelaVencida | Fim...");
        }
    }

    public void montarInformacoesExtratoAluno(List<MovParcelaVO> lista, String orderBy, boolean orderByDesc) throws Exception{
        List<MovParcelaVO> listaParcelasProRata = new ArrayList<MovParcelaVO>();
        for(MovParcelaVO mp : lista){
            mp.setInfoExtrato(new InfoExtratoParcelaTO());

            ResultSet rsS = criarConsulta("select statusprotheus, linknota, mc.codigo from movproduto mc " +
                    " inner join movprodutoparcela mpp on mpp.movproduto = mc.codigo" +
                    " where mpp.movparcela = " + mp.getCodigo() +
                    " order by mpp.valorpago DESC limit 1", con);

            if(rsS.next()){
                mp.getInfoExtrato().setCodigoMovproduto(rsS.getInt("codigo"));
                mp.getInfoExtrato().setStatusProtheus(rsS.getString("statusprotheus"));
                mp.getInfoExtrato().setNota(rsS.getString("linknota"));
            }

            ResultSet rsP = criarConsulta(" select \n" +
                    " mp.dataquitacao, \n" +
                    " fp.tipoformapagamento,\n" +
                    " fp.descricao as forma, \n" +
                    " op.descricao as operadoracartao, \n" +
                    " CASE WHEN cc.tipoconvenio is not null THEN cc.tipoconvenio\n" +
                    " WHEN r.tipo is not null THEN r.tipo\n" +
                    " WHEN coalesce(fp.tipoconveniocobranca, 0) > 0 THEN fp.tipoconveniocobranca\n" +
                    " WHEN ccmp.tipoconvenio is not null THEN ccmp.tipoconvenio\n" +
                    " ELSE cc.tipoconvenio END as tipoconvenio\n" +
                    " from movpagamento mp\n" +
                    " inner join pagamentomovparcela pmp on pmp.movpagamento = mp.codigo\n" +
                    " inner join formapagamento fp on fp.codigo = mp.formapagamento\n" +
                    " left join operadoracartao op on op.codigo = mp.operadoracartao\n" +
                    " left join conveniocobranca cc on cc.codigo = fp.conveniocobranca\n" +
                    " left join conveniocobranca ccmp ON ccmp.codigo = mp.conveniocobranca\n" +
                    " left join remessaitem ri on ri.movpagamento = mp.codigo\n" +
                    " left join remessa r on r.codigo = ri.remessa\n" +
                    " where pmp.movparcela = "+mp.getCodigo()+" \n" +
                    " order by pmp.valorpago \n" +
                    " DESC limit 1", con);

            if(rsP.next()){
                mp.getInfoExtrato().setPagamento(rsP.getDate("dataquitacao"));
                mp.getInfoExtrato().setFp(rsP.getString("forma"));
                String bandeira = rsP.getString("operadoracartao");
                if(!UteisValidacao.emptyString(bandeira)){
                    String meio;
                    if(bandeira.toLowerCase().contains("mastercard")){
                        meio = "mastercard.png";
                    }else if(bandeira.toLowerCase().contains("visa")){
                        meio = "visa.png";
                    }else if(bandeira.toLowerCase().contains("amex")){
                        meio = "amex.png";
                    }else if(bandeira.toLowerCase().contains("elo")){
                        meio = "elo.png";
                    }else if(bandeira.toLowerCase().contains("diners")){
                        meio = "diners.png";
                    }else{
                        meio = "cartao.png";
                        mp.getInfoExtrato().setFp(rsP.getString("forma") + " - " + bandeira);
                    }
                    mp.getInfoExtrato().setMeio("./imagens/bandeiras/" + meio);
                }else if(UteisValidacao.emptyNumber(rsP.getInt("tipoconvenio"))){
                    String tf = rsP.getString("tipoformapagamento");
                    if(tf.equals("CD") || tf.equals("CA")){
                        mp.getInfoExtrato().setMeio("./imagens/bandeiras/cartao.png");
                    }else if(tf.equals("BB")){
                        mp.getInfoExtrato().setMeio("./imagens/bandeiras/boleto.png");
                    }else{
                        mp.getInfoExtrato().setMeio("./imagens/bandeiras/generico.png");
                    }

                }else{
                    TipoConvenioCobrancaEnum tipoConvenio = TipoConvenioCobrancaEnum.valueOf(rsP.getInt("tipoconvenio"));
                    if(tipoConvenio == null){
                        mp.getInfoExtrato().setMeio("./imagens/bandeiras/generico.png");
                    } else {
                        mp.getInfoExtrato().setMeio(tipoConvenio.getLogomarca());
                    }
                }
            }

            if (mp.isExisteProdutoProRata()) {

                ResultSet rsPro = criarConsulta("select \n" +
                        "pro.totalfinal as valorProRata, \n" +
                        "pro.descricao \n" +
                        "from movprodutoparcela mpp \n" +
                        "inner join movproduto pro on pro.codigo = mpp.movproduto \n" +
                        "where pro.descricao ilike '%PRO-RATA%'\n" +
                        "and mpp.movparcela = " + mp.getCodigo(), con);

                while(rsPro.next()){
                    Double valorProRata = rsPro.getDouble("valorProRata");
                    MovParcelaVO parcelaProRata = (MovParcelaVO) mp.getClone(true);
                    parcelaProRata.setValorParcela(valorProRata);

                    String[] desc = rsPro.getString("descricao").split("PRO-RATA");
                    parcelaProRata.setDescricao("PARCELA PRO-RATA" + desc[1]);
                    listaParcelasProRata.add(parcelaProRata);
                    mp.setValorParcela(mp.getValorParcela() - valorProRata);
                }
            }
        }

        lista.addAll(listaParcelasProRata);

        if (UteisValidacao.emptyString(orderBy)) {
            Ordenacao.ordenarListaReverse(lista, "codigo");
        } else {
            if (orderByDesc) {
                Ordenacao.ordenarListaReverse(lista, orderBy);
            } else {
                Ordenacao.ordenarLista(lista, orderBy);
            }
        }
    }
    
    public List<MovParcelaVO> consultarParcelasEmAbertoPessoa(Integer pessoa, int nivelMontarDados) throws  Exception{
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM MovParcela WHERE pessoa =").append(pessoa);
        sqlStr.append(" and situacao='EA' \n");
        sqlStr.append(" ORDER BY movparcela.datavencimento ");
        ResultSet tabelaResultado;
        try (Statement stm = con.createStatement()) {
            tabelaResultado = stm.executeQuery(sqlStr.toString());
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
        }
    }

    public List<MovParcelaVO> consultarParcelasFuturasEmAberto(Integer codigoContrato, int nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM MovParcela WHERE contrato =").append(codigoContrato);
        sqlStr.append(" AND situacao = 'EA' ");
        sqlStr.append(" AND datavencimento >= CURRENT_DATE ");
        sqlStr.append(" ORDER BY movparcela.datavencimento ");

        ResultSet tabelaResultado;
        try (Statement stm = con.createStatement()) {
            tabelaResultado = stm.executeQuery(sqlStr.toString());
            return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
        }
    }

    public MovParcelaVO calcularParcelaProRata(Date dataVencimentoOriginalParcela,
                                               Date novaDataVencimentoParcela,
                                               Double valorMensalParcela,
                                               UsuarioVO usuarioResponsavel,
                                               EmpresaVO empresa,
                                               PessoaVO pessoa,
                                               ContratoVO contrato) throws Exception {

        int diferencaDiasNovoVencimento = Calendario.diferencaEmDias(dataVencimentoOriginalParcela, novaDataVencimentoParcela);
        Double valorProRata = (valorMensalParcela / 30) * diferencaDiasNovoVencimento;

        Date inicioVigenciaProdutoProRata = dataVencimentoOriginalParcela;
        Date fimVigenciaProdutoProRata = Calendario.subtrairDias(novaDataVencimentoParcela, 1);
        int anoReferenciaProdutoProRata = Calendario.getAno(novaDataVencimentoParcela);
        String mesReferenciaProdutoProRata = Calendario.getData(novaDataVencimentoParcela, "MM/yyyy");

        String descricao = "PRO RATA - DE "+
                Calendario.getData(inicioVigenciaProdutoProRata, "dd/MM/yyyy")+
                " ATÉ "+Calendario.getData(fimVigenciaProdutoProRata, "dd/MM/yyyy");

        MovParcelaVO parcelaProRata = new MovParcelaVO();
        parcelaProRata.setDataVencimento(novaDataVencimentoParcela);
        parcelaProRata.setDescricao("PARCELA " + descricao);
        parcelaProRata.setResponsavel(usuarioResponsavel);
        parcelaProRata.setValorParcela(valorProRata);
        parcelaProRata.setContrato(contrato);
        parcelaProRata.setSituacao("EA");
        parcelaProRata.setDataRegistro(Calendario.hoje());
        parcelaProRata.setEmpresa(empresa);
        parcelaProRata.setPessoa(pessoa);

        MovProdutoVO produtoProRata = new MovProdutoVO();
        produtoProRata.setSituacao("EA");
        produtoProRata.setDescricao(descricao);
        produtoProRata.setDataInicioVigencia(inicioVigenciaProdutoProRata);
        produtoProRata.setDataFinalVigencia(fimVigenciaProdutoProRata);
        produtoProRata.setAnoReferencia(anoReferenciaProdutoProRata);
        produtoProRata.setResponsavelLancamento(usuarioResponsavel);
        produtoProRata.setDataLancamento(Calendario.hoje());
        produtoProRata.setTotalFinal(valorProRata);
        produtoProRata.setPrecoUnitario(valorProRata);
        produtoProRata.setQuantidade(1);
        produtoProRata.setEmpresa(empresa);
        produtoProRata.setPessoa(pessoa);
        produtoProRata.setContrato(contrato);
        Produto produtoDAO = new Produto(this.con);
        produtoProRata.setProduto(produtoDAO.obterProdutoPadraoProRata());
        produtoDAO = null;
        produtoProRata.setMesReferencia(mesReferenciaProdutoProRata);
        produtoProRata.setAnoReferencia(anoReferenciaProdutoProRata);

        MovProdutoParcelaVO relacionamentoProdutoParcela = new MovProdutoParcelaVO();
        relacionamentoProdutoParcela.setMovProduto(produtoProRata.getCodigo());
        relacionamentoProdutoParcela.setMovProdutoVO(produtoProRata);
        relacionamentoProdutoParcela.setMovParcela(parcelaProRata.getCodigo());
        relacionamentoProdutoParcela.setMovParcelaVO(parcelaProRata);

        List<MovProdutoParcelaVO> movProdutosParcelas = new ArrayList<MovProdutoParcelaVO>();
        movProdutosParcelas.add(relacionamentoProdutoParcela);
        parcelaProRata.setMovProdutoParcelaVOs(movProdutosParcelas);

        return parcelaProRata;
    }

    @Override
    public List<MovParcelaVO> consultarParcelasPlanoPersonalAVencerParaCancelamento(int controleTaxaPersonal, int nivelMontarDados) throws Exception {
        List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();

        String sql = "SELECT * " +
                "FROM movparcela " +
                "WHERE personal IS NOT null " +
                "  AND situacao = 'EA' " +
                "  AND datavencimento >= '"+Calendario.getData(Calendario.hoje(), "yyyy-MM-dd")+ "' " +
                "  AND personal = "+controleTaxaPersonal +
                "  AND descricao not like '%CANCELAMENTO%' ";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    parcelas.add(montarDados(rs, nivelMontarDados, con));
                }
            }
        }

        return parcelas;
    }

    @Override
    public MovParcelaVO consultarProximaParcelaAnuidadeEmAberto(ContratoVO contrato) throws Exception {
        String sql = "SELECT movparcela.* " +
                "FROM movparcela " +
                "INNER JOIN movprodutoparcela ON movprodutoparcela.movparcela = movparcela.codigo " +
                "INNER JOIN movproduto ON movproduto.codigo = movprodutoparcela.movproduto " +
                "INNER JOIN produto ON produto.codigo = movproduto.produto " +
                "WHERE movparcela.situacao = 'EA' " +
                "  AND datavencimento > '"+Calendario.getData(new Date(), "yyyy-MM-dd")+ "' " +
                "  AND produto.tipoproduto IN ('"+TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo()+"') " +
                " AND movparcela.contrato = "+contrato.getCodigo() +
                "GROUP BY movparcela.codigo ";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                }
            }
        }

        return null;
    }

    @Override
    public MovParcelaVO consultarUltimaParcelaAnuidadePaga(List<ContratoVO> contratosTransferencia) throws Exception {
        String codContratos  = Uteis.retornarCodigos(contratosTransferencia);

        String sql = "SELECT movparcela.* " +
                "FROM movparcela " +
                "INNER JOIN movprodutoparcela ON movprodutoparcela.movparcela = movparcela.codigo " +
                "INNER JOIN movproduto ON movproduto.codigo = movprodutoparcela.movproduto " +
                "INNER JOIN produto ON produto.codigo = movproduto.produto " +
                "WHERE movparcela.situacao = 'PG' " +
                "  AND datavencimento < '"+Calendario.getData(new Date(), "yyyy-MM-dd")+ "' " +
                "  AND produto.tipoproduto IN ('"+TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo()+"') " +
                " AND movparcela.contrato in (" + codContratos + ") " +
                "GROUP BY movparcela.codigo " +
                "ORDER BY datavencimento DESC";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                }
            }
        }
        return null;
    }

    public Boolean verificarParcelaVencidaPorPessoa(Integer pessoa, Date data) throws Exception {
        StringBuilder sql = new StringBuilder("select mpa.codigo, mpa.pessoa ");
        sql.append("from movparcela mpa ");
        sql.append("where ");
        sql.append("mpa.datavencimento < '").append(Uteis.retirarHoraDaData(data)).append("' ");
        sql.append("and mpa.situacao = 'EA' ");
        sql.append("and mpa.pessoa = ").append(pessoa);

        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return true;
            }
        } return false;
    }

    @Override
    public List<MovParcelaVO> consultar(String sql, final int nivelMontarDados)
            throws SQLException, Exception {
        try (ResultSet tabelaResultado = SuperFacadeJDBC.criarConsulta(sql, con)) {
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
        }
    }

    public void incluirListaMovParcelas(List<MovParcelaVO> lista) throws Exception {
        for(MovParcelaVO obj : lista){
            this.incluirSemCommit(obj);
        }
    }


    public Integer consultarParcelasVencidasContrato(Date dataVencimento, int contrato) throws  Exception{
        StringBuilder sql = new StringBuilder("select count(*) parcelas from movparcela ");
        sql.append("where situacao = 'EA' ");
        sql.append(" and contrato = ").append(contrato);
        sql.append(" and cast(datavencimento as date) < '").append(Uteis.getDataFormatoBD(dataVencimento)).append("'");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return rs.getInt("parcelas");
            }
        }
        return 0;
    }

    public List<MovParcelaVO> consultarParcelasPagar(ContratoVO contrato, int nivelMontarDados) throws  Exception {

        StringBuilder sql = new StringBuilder("select * from movparcela ");
        sql.append("where situacao = 'EA' ");
        sql.append(" and contrato = ").append(contrato.getCodigo());
        sql.append(" and cast(datavencimento as date) <= '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("'");

        return consultar(sql.toString(), nivelMontarDados);
    }

    public List<MovParcelaVO> consultarParcelasPagarContrato(ContratoVO contratoVO) throws Exception {
        List<MovParcelaVO> movparcelas;
        if(contratoVO.isRegimeRecorrencia()){
            movparcelas = consultarParcelasPagar(contratoVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }else{
            movparcelas = consultarEmAbertoPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        return movparcelas;
    }

    public Date consultarDataPrimeiraParcelaASerPaga(ContratoVO contratoVO) throws Exception {
        Date data = new Date();
        String sql = "select * from movparcela where contrato = " + contratoVO.getCodigo() + " ORDER BY datavencimento LIMIT 1;";

        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sql)) {
            if(rs.next()) {
                data = rs.getDate("datavencimento");
            }
        }

        return data;
    }

    public Double consultarValorPagarContrato(ContratoVO contratoVO) throws Exception {

        List<MovParcelaVO> parcelas = consultarParcelasPagarContrato(contratoVO);

        if(parcelas == null || parcelas.size() == 0){
            return 0.0;
        }else{
            return parcelas.stream().mapToDouble(parcelaVO -> parcelaVO.getValorParcela().doubleValue()).sum();
        }
    }

    public List<MovParcelaVO> consultarParcelasFuturasPMContrato(final Integer contrato, final int nivelMontarDados) throws SQLException, Exception{
        StringBuilder sql = new StringBuilder("select distinct par.* from movparcela par inner join movprodutoparcela mpp on par.codigo = mpp.movparcela\n");
        sql.append(" inner join movproduto mpro on mpro.codigo = mpp.movproduto \n");
        sql.append(" inner join produto pro on pro.codigo =  mpro.produto and pro.tipoproduto = 'PM'");
        sql.append(" where par.contrato = ").append(contrato).append(" and datavencimento > '").append(Uteis.getDataJDBC(Calendario.hoje())).append("' ");
        sql.append(" AND NOT EXISTS( ");
        sql.append("  SELECT tran.codigo  FROM transacao tran inner join transacaomovparcela tmp on tmp.transacao = tran.codigo  ");
        sql.append("  WHERE tmp.movparcela = par.codigo   AND (tran.situacao = 2) limit 1) \n");
        sql.append(" AND NOT EXISTS( ");
        sql.append("select ri.codigo from remessaitem ri inner join remessa re on ri.remessa = re.codigo left join remessaitemmovparcela  rmov on  ri.codigo = rmov.remessaitem where ");
        sql.append(" (ri.movparcela = par.codigo  or ");
        sql.append(" (par.codigo = rmov.movparcela AND ri.tipo IN (").append(TipoRemessaEnum.BOLETO.getId()).append(",").append(TipoRemessaEnum.ITAU_BOLETO.getId()).append(")))\n");
        sql.append(" and  re.situacaoremessa in (" + SituacaoRemessaEnum.GERADA.getId() + "," + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "))\n");
        return consultar(sql.toString(), nivelMontarDados);
    }

    public List<MovParcelaVO> consultarParcelasVencidasPessoaVendasOnline(Integer pessoa, Integer empresa, int nivelMontarDados) throws Exception {
        return consultarParcelasVencidasPessoaVendasOnline(pessoa, empresa, null, null, nivelMontarDados);
    }

    public List<InadimplenteTO> buscarParcelasVencidasPorQtdDias(Integer diasVencido, PaginadorDTO paginadorDTO) throws Exception {
        final StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT DISTINCT c.matricula, e.nome as empresa FROM movparcela mp\n")
                .append("JOIN cliente c on c.pessoa = mp.pessoa\n")
                .append("JOIN empresa e on e.codigo = mp.empresa\n")
                .append("WHERE mp.situacao = 'EA'\n")
                .append("AND mp.datavencimento <= NOW() - INTERVAL '").append(diasVencido).append(" days'\n");

        processarPaginador(sqlStr, "c.matricula asc", paginadorDTO);

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsultaParcelasVencidas(tabelaResultado);
            }
        }
    }

    void processarPaginador(StringBuilder sql, String orderByDefault, PaginadorDTO paginadorDTO) throws Exception {
        if (paginadorDTO != null) {

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            paginadorDTO.setQuantidadeTotalElementos(SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql.toString() + ") as a", this.con).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            // Adicionar ordenação
            if (!UteisValidacao.emptyString(orderByDefault)
                    && paginadorDTO.getSQLOrderByUse() != null
                    && UteisValidacao.emptyString(paginadorDTO.getSQLOrderByUse().trim())) {
                sql.append(" ORDER BY ").append(orderByDefault).append(" \n");
            } else {
                sql.append(paginadorDTO.getSQLOrderByUse());
            }

            // Adicionar limit
            sql.append(paginadorDTO.getSQLLimitByUse());

            // Adicionar offset
            sql.append(paginadorDTO.getSQLOffsetByUse());
        }
    }

    public static List<InadimplenteTO> montarDadosConsultaParcelasVencidas(ResultSet tabelaResultado) throws Exception {
        final List<InadimplenteTO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            final InadimplenteTO obj = new InadimplenteTO();
            obj.setMatricula(tabelaResultado.getString("matricula"));
            obj.setEmpresa(tabelaResultado.getString("empresa"));

            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public List<MovParcelaVO> consultarParcelasVencidasPessoaVendasOnline(Integer pessoa, Integer empresa,
                                                                          Date inicioVencimento, Date fimVencimento,
                                                                          int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM MovParcela \n");
        sql.append("WHERE pessoa = ").append(pessoa).append(" \n");
        sql.append("AND situacao = 'EA' \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND empresa = ").append(empresa).append(" \n");
        }
        if (inicioVencimento != null && fimVencimento != null) {
            sql.append("AND datavencimento::date between '").append(Uteis.getDataJDBC(inicioVencimento)).append("' and '").append(Uteis.getDataJDBC(fimVencimento)).append("' \n");
        } else {
            sql.append("AND datavencimento <= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("' \n");
        }
        sql.append("ORDER BY datavencimento");
        List<MovParcelaVO> vetResultado = new ArrayList<>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    MovParcelaVO obj = montarDados(rs, nivelMontarDados, con);
                    if (obj.getDescricao().contains("MULTA E JUROS")) {
                        continue;
                    }
                    boolean emRemessa = parcelaEstaBloqueadaPorCobranca(obj);
                    if (emRemessa) {
                        continue;
                    }
                    vetResultado.add(obj);
                }
            }
        }
        return vetResultado;
    }

    public List<MovParcelaVO> consultarParcelasCanceladas(ContratoVO contratoVO, Date dataCancelamento) throws Exception {
        List<MovParcelaVO> vetResultado = new ArrayList<>();
        String sql = "SELECT * FROM MovParcela \n" +
                "WHERE contrato = " + contratoVO.getCodigo() + " \n" +
                "AND situacao = 'CA' \n" +
                "AND datavencimento <= '" + Uteis.getDataJDBC(dataCancelamento) + "' \n" +
                "ORDER BY datavencimento";
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sql)) {
            while (rs.next()) {
                MovParcelaVO obj = montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                vetResultado.add(obj);
            }
        }
        return vetResultado;
    }

    public void alterarDiasVencimentoMovParcelas(ContratoVO contratoVo, int nrDias, String operacao, boolean somentePlanos, Date dataLimiteVencimento) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE \n")
                .append("    movparcela \n")
                .append("SET\n")
                .append("    datavencimento = datavencimento ").append(operacao).append(" interval '").append(nrDias).append(" day',\n")
                .append("    datacobranca = datacobranca ").append(operacao).append(" interval '").append(nrDias).append(" day' \n")
                .append("WHERE \n")
                .append("    1 = 1 \n")
                .append("    AND contrato = ").append(contratoVo.getCodigo()).append(" \n")
                .append("    AND situacao = 'EA'\n");
        if (dataLimiteVencimento != null) {
            sql.append("    AND datavencimento::DATE >= '").append(Uteis.getDataJDBC(dataLimiteVencimento)).append("' \n");
        } else {
            sql.append("    AND datavencimento::DATE >= CURRENT_DATE\n");
        }
        if (somentePlanos) {
            sql.append("    AND codigo IN (SELECT mpp.movparcela\n")
                    .append("           FROM movprodutoparcela mpp\n")
                    .append("       LEFT JOIN movproduto mprod ON mpp.movproduto = mprod.codigo\n")
                    .append("       LEFT JOIN produto prod ON mprod.produto = prod.codigo\n")
                    .append("       WHERE prod.tipoproduto IN ('PM'))\n");
        }
        sql.append("RETURNING codigo;");
        SuperFacadeJDBC.executarConsulta(sql.toString(), con);
        if(UteisValidacao.notEmptyNumber(contratoVo.getContratoResponsavelRenovacaoMatricula())){
            Contrato contratoDAO = new Contrato(con);
            ContratoVO contratoRenovado = contratoDAO.consultarPorChavePrimaria(contratoVo.getContratoResponsavelRenovacaoMatricula(),Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            contratoDAO = null;
            alterarDiasVencimentoMovParcelas(contratoRenovado, nrDias, operacao, somentePlanos, dataLimiteVencimento);
        }
    }

    public void alterarSituacaoPorCodigoPessoa(Integer codigoPessoa) throws Exception {
        String sql = "update movparcela set situacao = 'CA' where pessoa = ? and situacao = 'EA';";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codigoPessoa);
            sqlAlterar.execute();
        }
    }

    public List<MovParcelaVO> consultarPorMatricula(String valorConsulta, int nivelMontarDados) throws Exception {
        String sqlStr = "select m.* from movparcela m \n" +
                "inner join cliente c2 on c2.pessoa = m.pessoa \n" +
                "where c2.matricula = '" + valorConsulta + "' ORDER BY datavencimento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<MovParcelaVO> consultarParcelasEmAbertoPactoPay(ConvenioCobrancaVO convenioCobrancaVO, TipoConsultaParcelasEnum tipoConsultaParcelasEnum,
                                                                Date inicio, Date fim, PaginadorDTO paginadorDTO, FiltroPactoPayDTO filtroDTO, Integer codigoPessoa) throws Exception {

        StringBuilder sql = obterSQLConsultarParcelasEmAbertoPactoPay(convenioCobrancaVO, tipoConsultaParcelasEnum, inicio, fim,
                paginadorDTO, false, null, filtroDTO, codigoPessoa);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_MINIMOS, con));
            }
        }
    }

    public List<TotalizadorPendenteDTO> consultarParcelasEmAbertoPactoPayPendentesTotalizador(ConvenioCobrancaVO convenioCobrancaVO, Date inicio, Date fim, FiltroPactoPayDTO filtroDTO) throws Exception {
        StringBuilder sql = obterSQLConsultarParcelasEmAbertoPactoPay(convenioCobrancaVO, null, inicio, fim, null,
                true, null, filtroDTO, null);
        List<TotalizadorPendenteDTO> lista = new ArrayList<>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    TotalizadorPendenteDTO dto = new TotalizadorPendenteDTO();
                    OperacaoRetornoCobrancaEnum operacaoRetornoCobrancaEnum = OperacaoRetornoCobrancaEnum.obterPorCodigo(rs.getInt("operacao"));
                    dto.setOperacao(operacaoRetornoCobrancaEnum.getCodigo());
                    dto.setOperacaoDescricao(operacaoRetornoCobrancaEnum.getDescricao());
                    dto.setQtdTotal(rs.getInt("qtdtotal"));
                    dto.setValorTotal(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valortotal")));
                    dto.setQtdAutomatica(rs.getInt("qtdautomatico"));
                    dto.setQtdManual(rs.getInt("qtdmanual"));
                    lista.add(dto);
                }
            }
        }
        return lista;
    }

    public List<MovParcelaVO> consultarParcelasEmAbertoPactoPayPendentesLista(ConvenioCobrancaVO convenioCobrancaVO, Date inicio, Date fim,
                                                                              PaginadorDTO paginadorDTO, OperacaoRetornoCobrancaEnum operacaoRetornoCobrancaEnum,
                                                                              FiltroPactoPayDTO filtroDTO) throws Exception {

        StringBuilder sql = obterSQLConsultarParcelasEmAbertoPactoPay(convenioCobrancaVO, null, inicio, fim, paginadorDTO, false, operacaoRetornoCobrancaEnum, filtroDTO, null);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_MINIMOS, con));
            }
        }
    }

    public StringBuilder obterSQLConsultarParcelasEmAbertoPactoPay(ConvenioCobrancaVO convenioCobrancaVO, TipoConsultaParcelasEnum tipoConsultaParcelasEnum,
                                                                   Date inicio, Date fim, PaginadorDTO paginadorDTO, boolean totalizadorPendentes,
                                                                   OperacaoRetornoCobrancaEnum operacaoRetornoCobrancaEnum, FiltroPactoPayDTO filtroDTO,
                                                                   Integer codigoPessoa) throws Exception {
        /**
         * Essa é a consulta responsável por obter as parcelas para pagamento online! (Transação)
         * by Luiz Felipe 22/04/2020
         */

        Date dataMaximaLancamento; // isso para evitar que lancamentos recentes entrem no processamento e parcela seja paga via sistema e entre na remessa ao mesmo tempo
        if(convenioCobrancaVO != null && !UteisValidacao.emptyString(convenioCobrancaVO.getEmpresa().getTimeZoneDefault())){
            dataMaximaLancamento = Calendario.getDateInTimeZone(Calendario.hoje(), convenioCobrancaVO.getEmpresa().getTimeZoneDefault());
        }else{
            Empresa empresaDao = new Empresa(con);
            dataMaximaLancamento = Calendario.getDateInTimeZone(Calendario.hoje(), empresaDao.obterTimeZoneDefault(null));
            empresaDao = null;
        }

        StringBuilder sql1 = new StringBuilder();
        sql1.append("select \n");
        if (totalizadorPendentes) {
            sql1.append("coalesce(operacaoretornocobranca, 0) as operacao, \n");
            sql1.append("count(codigo) as qtdtotal, \n");
            sql1.append("sum(valorparcela) as valortotal, \n");
            sql1.append("sum (case when ultimacobranca = 'manual' then 1 else 0 end) as qtdmanual, \n");
            sql1.append("sum (case when ultimacobranca = 'auto' then 1 else 0 end) as qtdautomatico \n");
            sql1.append("from (SELECT \n");
            sql1.append("distinct movp.*, \n");
            sql1.append("mprc.operacaoretornocobranca, \n");
            sql1.append("(select  \n");
            sql1.append("case  \n");
            sql1.append("when upper(u.username) = 'RECOR' then 'auto' \n");
            sql1.append("when upper(u.username) not in ('RECOR') then 'manual' \n");
            sql1.append("else 'Outro' end as ultimacobranca \n");
            sql1.append("from ( \n");
            sql1.append("select \n");
            sql1.append("1 as ordem, \n");
            sql1.append("r.usuario, \n");
            sql1.append("r.dataregistro as data \n");
            sql1.append("from remessaitem ri \n");
            sql1.append("inner join remessa r on r.codigo = ri.remessa \n");
            sql1.append("where ri.movparcela = movp.codigo \n");
            sql1.append("union \n");
            sql1.append("select \n");
            sql1.append("2 as ordem, \n");
            sql1.append("r.usuario, \n");
            sql1.append("r.dataregistro as data \n");
            sql1.append("from remessaitem ri \n");
            sql1.append("inner join remessa r on r.codigo = ri.remessa \n");
            sql1.append("inner join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
            sql1.append("where rim.movparcela = movp.codigo \n");
            sql1.append("union \n");
            sql1.append("select \n");
            sql1.append("3 as ordem, \n");
            sql1.append("tr.usuarioresponsavel as usuario, \n");
            sql1.append("tr.dataprocessamento as data \n");
            sql1.append("from transacao tr \n");
            sql1.append("inner join transacaomovparcela trm on trm.transacao = tr.codigo \n");
            sql1.append("where trm.movparcela = movp.codigo \n");
            sql1.append(") as sql  \n");
            sql1.append("inner join usuario u on u.codigo = sql.usuario \n");
            sql1.append("order by data desc limit 1) as ultimacobranca \n");
        } else {
            sql1.append("* \n");
            sql1.append("from (SELECT \n");
            sql1.append("distinct movp.*, pes.nome as nomepessoa \n");
        }
        sql1.append("FROM movparcela movp \n");
        sql1.append("INNER JOIN pessoa pes on pes.codigo = movp.pessoa \n");
        sql1.append("INNER JOIN cliente cli ON cli.pessoa = movp.pessoa \n");
        sql1.append("INNER JOIN movprodutoparcela mpr on mpr.movparcela = movp.codigo \n");
        sql1.append("INNER JOIN movproduto mprod on mprod.codigo = mpr.movproduto \n");
        sql1.append("INNER JOIN autorizacaocobrancacliente autcli ON autcli.cliente = cli.codigo and autcli.ativa \n");
        sql1.append("INNER JOIN conveniocobranca conv ON conv.codigo = autcli.conveniocobranca \n");
        sql1.append("INNER JOIN produto p ON p.codigo = mprod.produto \n");
        if (totalizadorPendentes || operacaoRetornoCobrancaEnum != null) {
            sql1.append("LEFT JOIN movparcelaresultadocobranca mprc ON mprc.codigo = (select max(codigo) from movparcelaresultadocobranca where movparcela = movp.codigo) \n");
        }

        StringBuilder sql2 = new StringBuilder();
        sql2.append("union SELECT \n");
        if (totalizadorPendentes) {
            sql2.append("distinct movp.*, \n");
            sql2.append("mprc.operacaoretornocobranca, \n");
            sql2.append("(select  \n");
            sql2.append("case  \n");
            sql2.append("when upper(u.username) = 'RECOR' then 'auto' \n");
            sql2.append("when upper(u.username) not in ('RECOR') then 'manual' \n");
            sql2.append("else 'Outro' end as ultimacobranca \n");
            sql2.append("from ( \n");
            sql2.append("select \n");
            sql2.append("1 as ordem, \n");
            sql2.append("r.usuario, \n");
            sql2.append("r.dataregistro as data \n");
            sql2.append("from remessaitem ri \n");
            sql2.append("inner join remessa r on r.codigo = ri.remessa \n");
            sql2.append("where ri.movparcela = movp.codigo \n");
            sql2.append("union \n");
            sql2.append("select \n");
            sql2.append("2 as ordem, \n");
            sql2.append("r.usuario, \n");
            sql2.append("r.dataregistro as data \n");
            sql2.append("from remessaitem ri \n");
            sql2.append("inner join remessa r on r.codigo = ri.remessa \n");
            sql2.append("inner join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
            sql2.append("where rim.movparcela = movp.codigo \n");
            sql2.append("union \n");
            sql2.append("select \n");
            sql2.append("3 as ordem, \n");
            sql2.append("tr.usuarioresponsavel as usuario, \n");
            sql2.append("tr.dataprocessamento as data \n");
            sql2.append("from transacao tr \n");
            sql2.append("inner join transacaomovparcela trm on trm.transacao = tr.codigo \n");
            sql2.append("where trm.movparcela = movp.codigo \n");
            sql2.append(") as sql  \n");
            sql2.append("inner join usuario u on u.codigo = sql.usuario \n");
            sql2.append("order by data desc limit 1) as ultimacobranca \n");
        } else {
            sql2.append("distinct movp.*, pes.nome as nomepessoa \n");
        }
        sql2.append("FROM movparcela movp \n");
        sql2.append("INNER JOIN pessoa pes on pes.codigo = movp.pessoa \n");
        sql2.append("INNER JOIN colaborador cli on cli.pessoa = movp.pessoa \n");
        sql2.append("INNER JOIN autorizacaocobrancacolaborador autcli on autcli.colaborador = cli.codigo and autcli.ativa \n");
        sql2.append("INNER JOIN movprodutoparcela mpr on mpr.movparcela = movp.codigo \n");
        sql2.append("INNER JOIN movproduto mprod on mprod.codigo = mpr.movproduto \n");
        sql2.append("INNER JOIN conveniocobranca conv ON conv.codigo = autcli.conveniocobranca \n");
        sql2.append("INNER JOIN produto p ON p.codigo = mprod.produto \n");
        if (totalizadorPendentes || operacaoRetornoCobrancaEnum != null) {
            sql2.append("LEFT JOIN movparcelaresultadocobranca mprc ON mprc.codigo = (select max(codigo) from movparcelaresultadocobranca where movparcela = movp.codigo) \n");
        }

        StringBuilder sqlWhere = new StringBuilder();
        sqlWhere.append("WHERE movp.situacao = 'EA' \n");
        sqlWhere.append("AND movp.valorparcela > 0 \n");

//        Para consulta quando vem do PactoPay não tem necessidade de aguardar 1 hora para apresentar a parcela.
//        sqlWhere.append("AND '").append(Uteis.getDataJDBCTimestamp(dataMaximaLancamento)).append("' - mprod.datalancamento > '01:00:00.000' \n");

        if (filtroDTO != null) {
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                sqlWhere.append("AND movp.pessoa = ").append(codigoPessoa).append(" \n");
            }
            if (!UteisValidacao.emptyList(filtroDTO.getEmpresas())) {
                sqlWhere.append("AND movp.empresa in (").append(filtroDTO.getEmpresasString()).append(") \n");
            }
            if (!UteisValidacao.emptyList(filtroDTO.getConvenios())) {
                sqlWhere.append("AND conv.codigo in (").append(filtroDTO.getConveniosString()).append(") \n");
            }
        } else {
            if (convenioCobrancaVO != null) {
                sqlWhere.append("AND movp.empresa = ").append(convenioCobrancaVO.getEmpresa().getCodigo()).append(" \n");
            }
            if (convenioCobrancaVO != null) {
                sqlWhere.append("AND conv.codigo = ").append(convenioCobrancaVO.getCodigo()).append(" \n");
            }
        }

        if (operacaoRetornoCobrancaEnum != null) {
            sqlWhere.append("AND coalesce(mprc.operacaoretornocobranca,0) = ").append(operacaoRetornoCobrancaEnum.getCodigo()).append(" \n");
        }

        if (tipoConsultaParcelasEnum != null) {
            if (tipoConsultaParcelasEnum.equals(TipoConsultaParcelasEnum.PARCELAS_EM_ABERTO_AUTORIZADAS)) {
                sqlWhere.append("AND movp.nrtentativas = 0 \n");
            } else if (tipoConsultaParcelasEnum.equals(TipoConsultaParcelasEnum.PARCELAS_REPESCAGEM)) {
                sqlWhere.append("AND movp.nrtentativas > 0 \n");
            }
        }

        if(inicio != null && fim != null) {
            sqlWhere.append("AND movp.datavencimento::date between '").append(Uteis.getDataFormatoBD(inicio)).append("' AND '").append(Uteis.getDataFormatoBD(fim)).append("' \n");
        }

        sql1.append(sqlWhere);
        sql2.append(sqlWhere);
        sql1.append(sql2).append(") sql \n");

        if (filtroDTO != null && !UteisValidacao.emptyString(filtroDTO.getParametro())) {
            sql1.append("where sql.*::text ilike '%").append(filtroDTO.getParametro()).append("%' \n");
        }


        if (totalizadorPendentes) {
            sql1.append("group by 1 \n");
        } else {
            //adicionar ordenação
            if (paginadorDTO != null &&
                    !UteisValidacao.emptyString(paginadorDTO.getSQLOrderByUse())) {
                sql1.append(paginadorDTO.getSQLOrderByUse().replace("vencimento", "datavencimento"));
            } else {
                sql1.append("ORDER BY datavencimento \n");
            }
        }

        if (paginadorDTO != null) {
            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql1 + ") as qtd", con);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            sql1.append("LIMIT ").append(maxResults).append(" \n");
            sql1.append("OFFSET ").append(indiceInicial).append(" \n");
        }

        return sql1;
    }

    public boolean parcelaExiste(Integer codigoParcela) throws Exception {
        String sqlStr = "select exists(select codigo from movparcela where codigo = " + codigoParcela + ") as existe";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                if (rs.next()) {
                    return rs.getBoolean("existe");
                }
                return false;
            }
        }
    }

    public List<ParcelaConsolidadaTO> consultarConsolidadoParcelas(Date mesConsulta, boolean isPossuiIdExterno) throws SQLException {
        Date dataUtilizar = Calendario.somarMeses(mesConsulta, 1);

        String sql = "select\n" +
                "    e.nome as nome_empresa,\n" +
                "    movparcela.codigo as movparcela_codigo,\n" +
                "    case\n" +
                "       when cliev.pessoa is not null then cliev.matricula\n" +
                "       else 'sem matricula'::character varying\n" +
                "    end as cliente_matricula,\n" +
                "    pesev.nome as pessoa_nome,\n" +
                "    (select email.email from email where email.pessoa = pesev.codigo limit 1) as email,\n" +
                "    movparcela.descricao AS parcela_descricao,\n" +
                "    movparcela.dataregistro AS parcela_dataregistro,\n" +
                "    movparcela.datavencimento AS parcela_datavencimento,\n" +
                "    movparcela.situacao AS parcela_situacao,\n" +
                "    movparcela.regimerecorrencia AS regime_recorrencia,\n" +
                "    movparcela.contrato AS parcela_contrato,\n" +
                "    movparcela.valorparcela AS parcela_valorparcela,\n" +
                "    1 AS total,\n" +
                "    mpg.datalancamento::date AS datapagamento,\n" +
                "        CASE\n" +
                "            WHEN movparcela.descricao::text ~~ 'QUITAÇÃO CANCELAMENTO CONTRATO - %'::text THEN 'QUITAÇÃO CANCELAMENTO CONTRATO'::character varying\n" +
                "            WHEN movparcela.descricao::text ~~ 'MULTA E JUROS - PARCELA %'::text THEN 'MULTA E JUROS'::character varying\n" +
                "            WHEN movparcela.descricao::text ~~ 'PAGTO. SALDO DEVEDOR C/C (INCOBRAVEIS)%'::text THEN 'INCOBRAVEIS'::character varying\n" +
                "            WHEN movparcela.descricao::text ~~ 'PAGTO. SALDO DEVEDOR C/C - %'::text THEN 'SALDO DEVEDOR'::character varying\n" +
                "            WHEN movparcela.descricao::text ~~ 'PARCELA %'::text THEN 'PARCELA'::character varying\n" +
                "            WHEN movparcela.descricao::text ~~ 'ADESÃO %'::text THEN 'ADESÃO'::character varying\n" +
                "            WHEN movparcela.descricao::text ~~ 'ACERTO CONTA CORRENTE ALUNO - %'::text THEN 'ACERTO CONTA CORRENTE ALUNO'::character varying\n" +
                "            ELSE movparcela.descricao\n" +
                "        END AS tipo\n" +
                (isPossuiIdExterno ? ", movparcela.idexterno as id_externo_parcela, con.idexterno as id_externo_contrato\n" : "") +
                "   FROM movparcela\n" +
                (isPossuiIdExterno ? "     LEFT JOIN contrato con ON con.codigo = movparcela.contrato\n" : "") +
                "     LEFT JOIN pagamentomovparcela pmpg ON movparcela.codigo = pmpg.movparcela\n" +
                "     LEFT JOIN movpagamento mpg ON mpg.codigo = pmpg.movpagamento\n" +
                "     JOIN empresa e ON e.codigo = movparcela.empresa\n" +
                "     JOIN pessoa pesev ON pesev.codigo = movparcela.pessoa\n" +
                "     LEFT JOIN cliente cliev ON cliev.pessoa = pesev.codigo\n" +
                "  WHERE date_trunc('month'::text, movparcela.datavencimento::date::timestamp with time zone) = date_trunc('month'::text, ('"+Uteis.getDataJDBC(dataUtilizar)+"'::date - 1)::timestamp with time zone) \n" +
                "  AND (movparcela.situacao::text = ANY (ARRAY['PG'::character varying, 'EA'::character varying]::text[]));";

        List<ParcelaConsolidadaTO> parcelas = new ArrayList<>();
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sql)) {
            while (rs.next()) {
                ParcelaConsolidadaTO parcelaConsolidadaTO = new ParcelaConsolidadaTO();

                parcelaConsolidadaTO.setNome_empresa(rs.getString("nome_empresa"));
                parcelaConsolidadaTO.setMovparcela_codigo(rs.getInt("movparcela_codigo"));
                parcelaConsolidadaTO.setCliente_matricula(rs.getString("cliente_matricula"));
                parcelaConsolidadaTO.setPessoa_nome(rs.getString("pessoa_nome"));
                parcelaConsolidadaTO.setEmail(rs.getString("email"));
                parcelaConsolidadaTO.setParcela_descricao(rs.getString("parcela_descricao"));
                parcelaConsolidadaTO.setParcela_dataregistro(rs.getDate("parcela_dataregistro"));
                parcelaConsolidadaTO.setParcela_datavencimento(rs.getDate("parcela_datavencimento"));
                parcelaConsolidadaTO.setParcela_situacao(rs.getString("parcela_situacao"));
                parcelaConsolidadaTO.setRegime_recorrencia(rs.getBoolean("regime_recorrencia"));
                parcelaConsolidadaTO.setParcela_contrato(rs.getInt("parcela_contrato"));
                parcelaConsolidadaTO.setParcela_valorparcela(rs.getDouble("parcela_valorparcela"));
                parcelaConsolidadaTO.setTotal(rs.getInt("total"));
                parcelaConsolidadaTO.setDatapagamento(rs.getDate("datapagamento"));
                parcelaConsolidadaTO.setTipo(rs.getString("tipo"));

                if(isPossuiIdExterno) {
                    parcelaConsolidadaTO.setId_externo_parcela(rs.getLong("id_externo_parcela"));
                    parcelaConsolidadaTO.setId_externo_contrato(rs.getLong("id_externo_contrato"));
                }

                parcelas.add(parcelaConsolidadaTO);
            }
        }
        return parcelas;
    }

    public boolean existeColunasCampoExterno() throws SQLException {
        String sql = "SELECT count(*) = 2 as possui\n" +
                "FROM information_schema.columns\n" +
                "WHERE (table_name='movparcela' and column_name='idexterno') or (table_name='contrato' and column_name='idexterno')\n";

        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sql)) {
            rs.next();
            return rs.getBoolean("possui");
        }
    }

    public List<MovParcelaVO> consultarPorPix(Integer codigoPix, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "SELECT movparcela FROM pixmovparcela WHERE pix = " + codigoPix;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                List<MovParcelaVO> movParcelaVOS = new ArrayList<>();
                while (rs.next()) {
                    String sqlStr2 = "SELECT * FROM movparcela WHERE codigo = " + rs.getInt("movparcela");
                    ResultSet rs2 = stm.executeQuery(sqlStr2);
                    movParcelaVOS.addAll(montarDadosConsulta(rs2, nivelMontarDados, con));
                }
                return movParcelaVOS;
            }
        }
    }

    public List<MovParcelaVO> consultarPorCodigos(List<Integer> codigos, boolean validarPendenteCobranca, int nivelMontarDados) throws Exception {
        String codigosString = "";
        for (Integer cod : codigos) {
            codigosString += ("," + cod);
        }
        String sql = "SELECT * FROM MovParcela WHERE codigo in (" + codigosString.replaceFirst(",", "") + ")";
        List<MovParcelaVO> parcelas = new ArrayList<>();
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                while (rs.next()) {
                    MovParcelaVO movParcelaVO = montarDados(rs, nivelMontarDados, this.con);
                    if (validarPendenteCobranca && parcelaEstaBloqueadaPorCobranca(movParcelaVO)) {
                        continue;
                    }
                    parcelas.add(movParcelaVO);
                }
            }
        }
        return parcelas;
    }

    public List<MovParcelaVO> consultarPorAulaAvulsaDiaria(Integer aulaavulsadiaria, int nivelMontarDados) throws Exception {
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery("select * from movparcela where aulaavulsadiaria = " + aulaavulsadiaria)) {
                return (montarDadosConsulta(rs, nivelMontarDados, con));
            }
        }
    }

    public void alterarSituacaoSPC(Integer codigo, Boolean estaNoSPC, String retornoSPC) throws Exception {
        String sql = "UPDATE movparcela SET incluidaSpc = ?, situacaoSpc = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setBoolean(++i, estaNoSPC);
            ps.setString(++i, retornoSPC);
            ps.setInt(++i, codigo);
            ps.execute();
        }
    }


    public void salvarJSONEnvio(Integer codigo, String jsonEnvio) throws Exception {
        String sql = "UPDATE movparcela SET jsonSpc = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, jsonEnvio);
            ps.setInt(++i, codigo);
            ps.execute();
        }
    }

    public void alterarSituacaoSPCCliente(Integer codigoPessoa) throws Exception {
        String sqlConsultar = "SELECT exists(SELECT codigo FROM movparcela WHERE pessoa = ? AND incluidaspc)";
        boolean existeParcelaNoSPC = false;
        try (PreparedStatement ps = con.prepareStatement(sqlConsultar)) {
            int i = 0;
            ps.setInt(++i, codigoPessoa);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    existeParcelaNoSPC = rs.getBoolean("exists");
                }
            }
        }

        StringBuilder sqlCliente = new StringBuilder();
        sqlCliente.append("UPDATE cliente\n")
                .append("SET datainclusaospc = ?\n")
                .append("WHERE pessoa = ?\n");
        if (existeParcelaNoSPC) {
            sqlCliente.append("AND datainclusaospc IS NULL");
        } else {
            sqlCliente.append("AND datainclusaospc IS NOT NULL");
        }

        try (PreparedStatement ps = con.prepareStatement(sqlCliente.toString())) {
            int i = 0;
            if (existeParcelaNoSPC){
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            } else {
                ps.setNull(++i, Types.TIMESTAMP);
            }
            ps.setInt(++i, codigoPessoa);
            ps.execute();
        }
    }

    public List<MovParcelaVO> consultarPorPessoaAndIncluidaSPC(Integer codigoPessoa, boolean incluidaSPC) throws Exception {
        String sql = "SELECT * FROM movparcela WHERE pessoa = ? AND incluidaspc = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, codigoPessoa);
            ps.setBoolean(++i, incluidaSPC);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
        }
    }

    public List<MovParcelaVO> findByCPFAndVencimentoAndValor(String cpf, Date dataVencimento, BigDecimal valorDebito, int nivelMontarDados) throws Exception {
        String sql = "select mpar.codigo, * from movparcela mpar\n" +
                "inner join pessoa pes on pes.codigo = mpar.pessoa \n" +
                "where pes.cfp = '" + cpf + "'\n" +
                "and mpar.situacao = 'EA'\n" +
                "and mpar.datavencimento = '" + Uteis.getDataJDBC(dataVencimento) + "'\n" +
                "and mpar.valorparcela = '" + valorDebito.toString() + "';";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
        }
    }

    public Integer consultarPessoaDaMovParcela(int idMovParcela) throws Exception {
        return descobrirCodigoPessoa(null, null, idMovParcela);
    }

    public Integer encontrarEntidadeZWPorMovParcelaParaCappta(String coluna, Integer codigoMovParcela) throws  Exception {
        String sql = "SELECT " + coluna + " FROM movparcela WHERE codigo = " + codigoMovParcela;
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() ? rs.getInt(coluna) : null;
        }
    }

    public void excluirMultaJurosParcela(MovParcelaVO parcelaVO) throws Exception {
        MovProdutoParcela movProdutoParcelaDao;
        MovProduto movProdutoDao;
        PixMovParcela pixMovParcelaDao;
        BoletoMovParcela boletoMovParcelaDAO;
        try {
            if(!parcelaVO.getSituacao().equals("PG")) { // parcelas pagas não serão excluídas
                movProdutoParcelaDao = new MovProdutoParcela(con);
                movProdutoDao = new MovProduto(con);
                pixMovParcelaDao = new PixMovParcela(con);
                boletoMovParcelaDAO = new BoletoMovParcela(con);

                List<MovProdutoParcelaVO> movProdutoParcelaVOS = movProdutoParcelaDao.consultarPorCodigoMovParcela(parcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (MovProdutoParcelaVO mppVO : movProdutoParcelaVOS) {
                    if (mppVO.getMovParcelaOriginalMultaJuros().getCodigo() != 0 || parcelaVO.getDescricao().contains("- MULTA E JUROS")) {
                        movProdutoDao.excluirSemCommit(mppVO.getMovProduto());
                        pixMovParcelaDao.excluirPorParcela(mppVO.getMovParcela());
                        boletoMovParcelaDAO.excluirPorMovParcela(mppVO.getMovParcela());
                        excluirSemCommit(mppVO.getMovParcela());
                    }
                }
            }
        } finally {
            movProdutoParcelaDao = null;
            movProdutoDao = null;
            pixMovParcelaDao = null;
            boletoMovParcelaDAO = null;
        }
    }

    public List<MovParcelaVO> consultarParcelasMultaEJuros(Integer movparcelaOriginal, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT distinct par.* from MovParcela par "
                + "inner  join movprodutoparcela mpp on par.codigo = mpp.movparcela "
                + "WHERE  mpp.movparcelaoriginalmultajuros = " + movparcelaOriginal
                + " ORDER BY par.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }


    public List<MovParcelaVO> consultarPorContratoAndIncluidaSPC(Integer codigoPessoa, boolean incluidaSPC) throws Exception {
        String sql = "SELECT * FROM movparcela WHERE contrato = ? AND incluidaspc = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, codigoPessoa);
            ps.setBoolean(++i, incluidaSPC);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
        }
    }

    public List<MovParcelaVO> consultarParcelasVencidasOuUltimaRenovacaoPessoa(Integer pessoa, int nivelMontarDados) throws  Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT contrato, datavencimento FROM MovParcela \n");
        sql.append("WHERE pessoa = ").append(pessoa).append(" \n");
        sql.append("AND situacao = 'EA' \n");
        sql.append("AND contrato in (select max(codigo) from contrato WHERE pessoa = ").append(pessoa).append(" AND contratobaseadorenovacao > 0 AND vigenciade > '").append(Uteis.getDataJDBC(Calendario.hoje())).append("')\n");
        sql.append("ORDER BY datavencimento, codigo LIMIT 1");

        int contratoRenovacao = 0;
        Date dataRenovacao = Calendario.hoje();

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                if (tabelaResultado.next()) {
                    contratoRenovacao = tabelaResultado.getInt("contrato");
                    dataRenovacao = tabelaResultado.getDate("datavencimento");
                }
            }
        }

        sql = new StringBuilder("SELECT * FROM MovParcela WHERE pessoa =").append(pessoa);
        sql.append(" and situacao='EA' \n");
        sql.append(" AND (datavencimento <= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'\n");
        sql.append(" OR (contrato = ").append(contratoRenovacao).append(" AND datavencimento <= '").append(Uteis.getDataJDBC(dataRenovacao)).append("'))");
        sql.append(" ORDER BY movparcela.datavencimento");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List<MovParcelaVO> consultarParcelasEmAbertoParaGerarBoletosOnline(ConvenioCobrancaVO convenioCobrancaVO, Date dataInicio, Date dataFim, EmpresaVO empresaVO) throws Exception{
        /**
         * Essa é a consulta responsável por obter as parcelas gerar os boletos online!
         * by Maurin Noleto 14/10/2024
         */

        StringBuilder sql1 = new StringBuilder();
        sql1.append("select * from (SELECT \n");
        sql1.append("distinct movp.* \n");
        sql1.append("FROM movparcela movp \n");
        sql1.append("INNER JOIN cliente cli ON cli.pessoa = movp.pessoa \n");
        sql1.append("INNER JOIN movprodutoparcela mpr on mpr.movparcela = movp.codigo \n");
        sql1.append("INNER JOIN movproduto mprod on mprod.codigo = mpr.movproduto \n");
        sql1.append("INNER JOIN autorizacaocobrancacliente autcli ON autcli.cliente = cli.codigo and autcli.ativa \n");
        sql1.append("INNER JOIN conveniocobranca conv ON conv.codigo = autcli.conveniocobranca \n");
        sql1.append("INNER JOIN produto p ON p.codigo = mprod.produto \n");

        StringBuilder sqlWhere = new StringBuilder();
        sqlWhere.append("WHERE movp.situacao = 'EA' \n");
        sqlWhere.append("AND movp.valorparcela > 0 \n");
        sqlWhere.append("AND movp.empresa = ").append(empresaVO.getCodigo()).append(" \n");
        sqlWhere.append("AND conv.codigo = ").append(convenioCobrancaVO.getCodigo()).append(" \n");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sqlWhere.append("AND (movp.datavencimento::date >= '" + sdf.format(dataInicio) + "' AND movp.datavencimento::date <= '" + sdf.format(dataFim) + "') \n");

        //validar se não existe cobrança Stone Connect pendente ou paga para a parcela
        sqlWhere.append("AND not exists(select ppmp.codigo \n");
        sqlWhere.append("from pinpadpedidomovparcela ppmp \n");
        sqlWhere.append("inner join pinpadpedido pp on ppmp.pinpadpedido = pp.codigo \n");
        sqlWhere.append("where ppmp.movparcela = movp.codigo \n");
        sqlWhere.append("and pp.status in (" + StatusPinpadEnum.GERADO.getCodigo() + "," + StatusPinpadEnum.AGUARDANDO.getCodigo() + "," + StatusPinpadEnum.PAGO.getCodigo() + ") \n");
        sqlWhere.append(")\n");

        //não pode cobrar parcela que tenha boleto online pendente
        sqlWhere.append("AND not exists( \n");
        sqlWhere.append("select \n");
        sqlWhere.append("b.codigo \n");
        sqlWhere.append("from boleto b \n");
        sqlWhere.append("inner join boletomovparcela bm on bm.boleto = b.codigo \n");
        sqlWhere.append("where b.situacao in (").append(SituacaoBoletoEnum.AGUARDANDO_REGISTRO.getCodigo()).append(",");
        sqlWhere.append(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO.getCodigo()).append(",");
        sqlWhere.append(SituacaoBoletoEnum.GERADO.getCodigo()).append(",");
        sqlWhere.append(SituacaoBoletoEnum.PAGO.getCodigo()).append(") \n");
        sqlWhere.append("and coalesce(bm.movparcela,0) = movp.codigo \n");
        sqlWhere.append(") \n");

        //NÃO PODE TER PARCELA EM REMESSA DE DCC AGUARDANDO RETORNO
        //EDI - CIELO - BIN - GETNET
        sqlWhere.append("AND NOT EXISTS(\n");
        sqlWhere.append("SELECT  \n");
        sqlWhere.append("ri1.codigo \n");
        sqlWhere.append("FROM remessaitem ri1 \n");
        sqlWhere.append("INNER JOIN remessa r1 ON r1.codigo = ri1.remessa \n");
        sqlWhere.append("INNER JOIN remessaitemmovparcela rim1 ON rim1.remessaitem = ri1.codigo \n");
        sqlWhere.append("WHERE r1.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(") \n");
        sqlWhere.append("AND r1.tipo in (").append(TipoRemessaEnum.EDI_CIELO.getId()).append(",").append(TipoRemessaEnum.GET_NET.getId()).append(",").append(TipoRemessaEnum.DCC_BIN.getId()).append(") \n");
        sqlWhere.append("AND coalesce(rim1.movparcela, 0) = movp.codigo) \n");

        sqlWhere.append("AND NOT EXISTS(\n");
        sqlWhere.append("SELECT  \n");
        sqlWhere.append("ri1.codigo \n");
        sqlWhere.append("FROM remessaitem ri1 \n");
        sqlWhere.append("INNER JOIN remessa r1 ON r1.codigo = ri1.remessa \n");
        sqlWhere.append("WHERE r1.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(") \n");
        sqlWhere.append("AND r1.tipo in (").append(TipoRemessaEnum.EDI_CIELO.getId()).append(",").append(TipoRemessaEnum.GET_NET.getId()).append(",").append(TipoRemessaEnum.DCC_BIN.getId()).append(") \n");
        sqlWhere.append("AND coalesce(ri1.movparcela, 0) = movp.codigo) \n");


        //NÃO PODE TER PARCELA DE TRANSAÇÃO APROVADA OU CONCLUIDA COM SUCESSO
        sqlWhere.append("AND NOT EXISTS(\n");
        sqlWhere.append("SELECT  \n");
        sqlWhere.append("tr1.codigo \n");
        sqlWhere.append("FROM transacao tr1 \n");
        sqlWhere.append("INNER JOIN transacaomovparcela trm ON tr1.codigo = trm.transacao \n");
        sqlWhere.append("WHERE tr1.situacao in (").append(SituacaoTransacaoEnum.APROVADA.getId()).append(",").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(") \n");
        sqlWhere.append("AND coalesce(trm.movparcela, 0) = movp.codigo) \n");


        //NÃO PODEM TER PARCELA EM REMESSAS DE BOLETO AINDA NAO PROCESSADAS
        //E QUE O ALUNO NÃO TENHA MAIS AUTORIZACAO DE COBRANÇA BOLETO
        sqlWhere.append("AND NOT EXISTS( \n");
        sqlWhere.append("SELECT ri.codigo \n");
        sqlWhere.append("FROM remessaitem ri \n");
        sqlWhere.append("INNER JOIN remessa re ON ri.remessa = re.codigo \n");
        sqlWhere.append("INNER JOIN remessaitemmovparcela rmov ON ri.codigo = rmov.remessaitem \n");
        sqlWhere.append("WHERE movp.codigo = rmov.movparcela \n");
        sqlWhere.append("AND ri.tipo IN (").append(TipoRemessaEnum.BOLETO.getId()).append(",").append(TipoRemessaEnum.ITAU_BOLETO.getId()).append(") \n");
        sqlWhere.append("AND re.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(") \n");
        sqlWhere.append("AND exists(select codigo from autorizacaocobrancacliente where cliente = cli.codigo and ativa and tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO.getId()).append(")) \n");

        //NÃO PODEM TER PARCELA EM PIX DE PixStatusEnum = ATIVA
        sqlWhere.append("AND NOT EXISTS ( \n");
        sqlWhere.append("SELECT \n");
        sqlWhere.append("px.codigo \n");
        sqlWhere.append("FROM pix px \n");
        sqlWhere.append("INNER JOIN pixmovparcela pxmovp ON px.codigo = pxmovp.pix \n");
        sqlWhere.append("WHERE px.status = '").append(PixStatusEnum.ATIVA.getDescricao()).append("' \n");
        sqlWhere.append("AND coalesce(pxmovp.movparcela, 0) = movp.codigo) \n");

        sqlWhere.append("AND(  \n");
        sqlWhere.append(" (autcli.tipoacobrar = ").append(TipoObjetosCobrarEnum.TUDO.getId()).append(") \n");
        String tiposPlano = "";
        for (String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoPlano) {
            tiposPlano += ",'" + tipo + "'";
        }
        sqlWhere.append(" OR (autcli.tipoacobrar = ").append(TipoObjetosCobrarEnum.APENAS_PLANOS.getId()).append(" AND p.tipoproduto IN (").append(tiposPlano.replaceFirst(",", "")).append(")) \n");
        sqlWhere.append(" OR (autcli.tipoacobrar = ").append(TipoObjetosCobrarEnum.TIPOS_PRODUTOS.getId()).append(" AND p.tipoproduto = ANY (('{'||autcli.listaobjetosacobrar||'}') :: text[])) \n");
        String tiposContrato = "";
        for (String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoContrato) {
            tiposContrato += ",'" + tipo + "'";
        }
        sqlWhere.append(" OR (autcli.tipoacobrar = ").append(TipoObjetosCobrarEnum.CONTRATOS_RENOVAVEIS_AUTO.getId()).append(" AND p.tipoproduto IN (").append(tiposContrato.replaceFirst(",", "")).append(") \n");
        sqlWhere.append(" AND ((SELECT EXISTS(SELECT codigo FROM contratorecorrencia WHERE contrato = coalesce(movp.contrato,0))) OR (SELECT EXISTS(SELECT codigo FROM contrato WHERE codigo = coalesce(movp.contrato,0) AND renovavelAutomaticamente ))))) \n");

        sql1.append(sqlWhere);
        sql1.append(") sql");
        sql1.append(" ORDER BY datavencimento ");
        try (PreparedStatement ps = con.prepareStatement(sql1.toString())) {
            return montarDadosConsulta(ps.executeQuery(), Uteis.NIVELMONTARDADOS_MINIMOS, con);
        }
    }

    public void mudarSituacaoGerandoBoletoGestaoBoletosOnline(boolean situacao, List<MovParcelaVO> movParcelas) throws Exception {
        if (UteisValidacao.emptyList(movParcelas)) {
            return; // Nenhum código para atualizar
        }

        // Constrói o placeholder para o IN (?, ?, ?, ...)
        StringBuilder sql = new StringBuilder("UPDATE movparcela SET gerandoBoletoPeloGestaoBoletosOnline = ? WHERE codigo IN (");
        for (int i = 0; i < movParcelas.size(); i++) {
            sql.append("?");
            if (i < movParcelas.size() - 1) {
                sql.append(", ");
            }
        }
        sql.append(")");

        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            int index = 0;
            // Define o valor para o campo `gerandoBoletoPeloGestaoBoletosOnline`
            ps.setBoolean(++index, situacao);

            // Define todos os códigos
            for (MovParcelaVO movParcelaVO : movParcelas) {
                ps.setInt(++index, movParcelaVO.getCodigo());
            }

            // Executa o update
            ps.executeUpdate();
        }
    }

    public List<Integer> consultarCodigoParcelasCobradasHojeManualmente(ConvenioCobrancaVO convenioCobrancaVO) throws  Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT tmp.movparcela FROM transacaomovparcela tmp \n");
        sql.append("INNER JOIN transacao t ON t.codigo = tmp.transacao \n");
        sql.append("WHERE t.dataprocessamento >= '").append(Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00:00.000")).append("' \n");
        sql.append("AND t.dataprocessamento <= '").append(Uteis.getDataHoraJDBC(Calendario.hoje(), "23:59:59.999")).append("' \n");
        sql.append("AND t.conveniocobranca = ").append(convenioCobrancaVO.getCodigo()).append(" \n");
        sql.append("AND t.origem in (").append(OrigemCobrancaEnum.PACTO_PAY_COBRAR.getCodigo()).append(") \n");
        sql.append("AND t.transacaoverificarcartao = FALSE;");

        List<Integer> codigos = new ArrayList<>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                while (tabelaResultado.next()) {
                    codigos.add(tabelaResultado.getInt("movparcela"));
                }
            }
        }

        return codigos;
    }
    public Integer obterDiasInadimplenteCliente(Integer codigoPessoa, Integer codigoEmpresa) throws Exception {
        StringBuilder sql = getSQLDiasInadimplenteCliente(codigoPessoa, codigoEmpresa);
        PreparedStatement stmt = con.prepareStatement(sql.toString());
        ResultSet rs = stmt.executeQuery();
        if (rs.next()) {
            return rs.getInt("dias_inadimplente");
        }
        return 0;
    }
    private StringBuilder getSQLDiasInadimplenteCliente(Integer codigoPessoa, Integer codigoEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT \n");
        sql.append("    CASE \n");
        sql.append("        WHEN MIN(m.dataVencimento) IS NOT NULL THEN \n");
        sql.append("            DATE_PART('day', CURRENT_DATE - MIN(m.dataVencimento)) \n");
        sql.append("        ELSE 0 \n");
        sql.append("    END AS dias_inadimplente \n");

        sql.append("FROM movparcela m \n");
        sql.append("LEFT JOIN pagamentomovparcela pm ON pm.movparcela = m.codigo \n");
        sql.append("LEFT JOIN movpagamento mp ON mp.codigo = pm.movpagamento \n");

        sql.append("WHERE m.situacao IN ('EA', 'AB') \n"); // Em atraso ou em aberto
        sql.append("  AND m.datavencimento < CURRENT_DATE \n");
        sql.append("  AND mp.datapagamento IS NULL \n");

        sql.append("  AND m.pessoa = ").append(codigoPessoa).append(" \n");

        if (codigoEmpresa != null) {
            sql.append("  AND m.empresa = ").append(codigoEmpresa).append(" \n");
        }

        return sql;
    }



    public void alterarSomenteVencimentoUltimaParcelaContratoSemCommit(Integer contrato, Date novoVencimento) throws Exception {
        String sqlStr = "SELECT codigo FROM MovParcela WHERE MovParcela.descricao ilike 'PARCELA%' AND MovParcela.contrato = " +
                contrato + " order by codigo desc limit 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    String sql = "UPDATE MovParcela set dataVencimento = ? WHERE contrato = ? AND codigo = ?";
                    try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                        sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(novoVencimento));
                        sqlAlterar.setInt(2, contrato);
                        sqlAlterar.setInt(3, tabelaResultado.getInt("codigo"));
                        sqlAlterar.execute();
                    }
                }
            }
        }
    }

}
