package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estudio.dao.AgendaEstudio;
import br.com.pactosolucoes.estudio.dao.Pacote;
import br.com.pactosolucoes.estudio.modelo.DisponibilidadeVO;
import br.com.pactosolucoes.estudio.util.Validador;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.GerarCreditosPersonalServico;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ReciboClienteConsultor;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.financeiro.VendaAvulsaInterfaceFacade;
import negocio.interfaces.plano.ProdutoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.financeiro.ClientesICV;
import servicos.integracao.TreinoWSConsumer;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>VendaAvulsaVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>VendaAvulsaVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see VendaAvulsaVO
 * @see SuperEntidade
 */
public class VendaAvulsa extends SuperEntidade implements VendaAvulsaInterfaceFacade {

    private static final String SQL_VENDAS_STUDIO = "SELECT venda.codigo,venda.dataregistro,venda.empresa,venda.cliente,venda.valortotal,"
            + " usu.nome, item.codigo as item, item.produto,item.quantidade,item.valorparcial, item.pacote, item.tabeladesconto,"
            + "prod.valorfinal,prodpac.valor_unitario,prod.descricao "
            + "FROM vendaavulsa venda "
            + "INNER JOIN itemvendaavulsa item ON item.vendaavulsa = venda.codigo "
            + "INNER JOIN produto prod ON prod.codigo = item.produto "
            + "INNER JOIN usuario usu ON usu.codigo = venda.responsavel "
            + "LEFT JOIN sch_estudio.pacote pac ON pac.id_pacote = item.pacote "
            + "LEFT JOIN sch_estudio.pacote_produto prodpac ON item.produto = prodpac.id_produto and prodpac.id_pacote = item.pacote "
            + "WHERE venda.cliente = ? AND venda.empresa = ? AND prod.tipoproduto = 'SS' "
            + "ORDER BY venda.dataregistro DESC, prod.descricao ASC";
    private Hashtable itemVendaAvulsas;

    public VendaAvulsa() throws Exception {
        super();
        setItemVendaAvulsas(new Hashtable());
    }

    public VendaAvulsa(Connection con) throws Exception {
        super(con);
    }

    public ResultSet incluirSemCommitSomenteVendaAvulsa(VendaAvulsaVO obj) throws Exception {
        VendaAvulsaVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO VendaAvulsa( tipoComprador, nomeComprador, cliente, colaborador, valorTotal,empresa, responsavel, dataRegistro, origemSistema, pessoa, evento) " +
                "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) " + "RETURNING codigo";
        ResultSet rs;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getTipoComprador());
        sqlInserir.setString(2, obj.getNomeComprador());
        if (obj.getCliente().getCodigo() != 0) {
            sqlInserir.setInt(3, obj.getCliente().getCodigo());
        } else {
            sqlInserir.setNull(3, 0);
        }
        if (obj.getColaborador().getCodigo() != 0) {
            sqlInserir.setInt(4, obj.getColaborador().getCodigo());
        } else {
            sqlInserir.setNull(4, 0);
        }
        sqlInserir.setDouble(5, obj.getValorTotal());
        if (obj.getEmpresa().getCodigo() != 0) {
            sqlInserir.setInt(6, obj.getEmpresa().getCodigo());
        } else {
            sqlInserir.setNull(6, 0);
        }
        if (obj.getResponsavel().getCodigo() != 0) {
            sqlInserir.setInt(7, obj.getResponsavel().getCodigo());
        } else {
            sqlInserir.setNull(7, 0);
        }
        sqlInserir.setTimestamp(8, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
        sqlInserir.setInt(9, obj.getOrigemSistema().getCodigo());

        if (!UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
            sqlInserir.setInt(10, obj.getPessoaVO().getCodigo());
        } else {
            sqlInserir.setNull(10, 0);
        }
        resolveIntegerNull(sqlInserir, 11, obj.getEventoVO().getCodigo());

        rs = sqlInserir.executeQuery();

        obj.setCodigo(obterValorChavePrimariaCodigo());
        return rs;
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>VendaAvulsaVO</code>.
     */
    public VendaAvulsaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new VendaAvulsaVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>VendaAvulsaVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe
     * <code>VendaAvulsaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    public Integer incluir(VendaAvulsaVO obj, boolean parcelaPaga, List<DisponibilidadeVO> listaDisponibilidade, Date dataIniAgendar, Date dataFimAgendar) throws Exception {
        return incluir(obj, parcelaPaga, obj.getDataRegistro(), listaDisponibilidade, dataIniAgendar, dataFimAgendar);
    }
    public Integer incluir(VendaAvulsaVO obj, boolean parcelaPaga, Date dataReferencia,  List<DisponibilidadeVO> listaDisponibilidade, Date dataIniAgendar, Date dataFimAgendar) throws Exception {
        try {
            con.setAutoCommit(false);
            Integer idVendaAvulsa = incluirSemCommit(obj, parcelaPaga, dataReferencia);
            gravarAlteracoesEstudio(obj, listaDisponibilidade,dataIniAgendar,dataFimAgendar);
            con.commit();
            return idVendaAvulsa;
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public Integer incluirSemCommit(VendaAvulsaVO obj, boolean parcelaPaga, Date dataReferencia) throws Exception {
        int idVendaAvulsa;
        try (ResultSet rs = incluirSemCommitSomenteVendaAvulsa(obj)) {
            ItemVendaAvulsa itemDao = new ItemVendaAvulsa(con);
            for (ItemVendaAvulsaVO item : obj.getItemVendaAvulsaVOs())
                item.setVendaAvulsaVO(obj);
            itemDao.incluirItemVendaAvulsas(obj.getCodigo(), obj.getItemVendaAvulsaVOs());
            obj.setNovoObj(false);
            gerarParcela(obj, parcelaPaga, ((obj.getValorTotal() == 0.0 || parcelaPaga) ? "PG" : "EA"), dataReferencia);
            idVendaAvulsa = 0;
            if (rs.next()) {
                idVendaAvulsa = rs.getInt("codigo");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return idVendaAvulsa;
    }
    public Integer incluirVendaAvulsaArmario(VendaAvulsaVO obj, boolean parcelaPaga, Date dataReferencia) throws Exception {
        int idVendaAvulsa;
        try (ResultSet rs = incluirSemCommitSomenteVendaAvulsa(obj)) {
            ItemVendaAvulsa itemDao = new ItemVendaAvulsa(con);
            for (ItemVendaAvulsaVO item : obj.getItemVendaAvulsaVOs())
                item.setVendaAvulsaVO(obj);
            itemDao.incluirItemVendaAvulsas(obj.getCodigo(), obj.getItemVendaAvulsaVOs());
            obj.setNovoObj(false);
            gravarParcelasArmario(obj, parcelaPaga, ((obj.getValorTotal() == 0.0 || parcelaPaga) ? "PG" : "EA"), dataReferencia);
            idVendaAvulsa = 0;
            if (rs.next()) {
                idVendaAvulsa = rs.getInt("codigo");
            }
        }
        return idVendaAvulsa;
    }

    private void gerarMovPagamento(VendaAvulsaVO obj, MovParcelaVO parcela) throws Exception {
        ReciboPagamento reciboPagamentoDAO = null;
        FormaPagamento formaPagamentoDAO = null;
        MovPagamento movPagamentoDAO = null;
        MovProdutoParcela movProdutoParcelaDAO = null;
        ReciboClienteConsultor reciboClienteConsultorDAO = null;
        try {
            reciboPagamentoDAO = new ReciboPagamento(this.con);
            formaPagamentoDAO = new FormaPagamento(this.con);
            movPagamentoDAO = new MovPagamento(this.con);
            movProdutoParcelaDAO = new MovProdutoParcela(this.con);
            reciboClienteConsultorDAO = new ReciboClienteConsultor(this.con);

            PessoaVO pessoa = new PessoaVO();
            String nomePagador = "";
            if (obj.getTipoComprador().equals("CI")) {
                pessoa = obj.getCliente().getPessoa();

            } else if (obj.getTipoComprador().equals("CO")) {
                pessoa = obj.getColaborador().getPessoa();
            }
            nomePagador = UteisValidacao.emptyNumber(pessoa.getCodigo()) ? obj.getNomeComprador() : pessoa.getNome();

            ReciboPagamentoVO recibo = new ReciboPagamentoVO();
            recibo.setValorTotal(obj.getValorTotal());
            recibo.setPessoaPagador(pessoa);
            recibo.setNomePessoaPagador(nomePagador);
            recibo.setResponsavelLancamento(obj.getResponsavel());
            recibo.setData(obj.getDataRegistro());
            recibo.setEmpresa(obj.getEmpresa());
            reciboPagamentoDAO.incluir(recibo);

            MovPagamentoVO pagamento = new MovPagamentoVO();
            pagamento.setResponsavelPagamento(obj.getResponsavel());
            pagamento.setMovPagamentoEscolhida(true);
            pagamento.setNomePagador(nomePagador);
            pagamento.setEmpresa(obj.getEmpresa());
            pagamento.setFormaPagamento(formaPagamentoDAO.consultarAVista());
            pagamento.setValor(obj.getValorTotal());
            pagamento.setValorTotal(obj.getValorTotal());
            pagamento.setCredito(false);
            pagamento.setDataPagamento(obj.getDataRegistro());
            pagamento.setDataLancamento(obj.getDataRegistro());
            pagamento.setDataQuitacao(obj.getDataRegistro());
            pagamento.setPessoa(pessoa);
            pagamento.setReciboPagamento(recibo);

            PagamentoMovParcelaVO pagamentoMovParcela = new PagamentoMovParcelaVO();
            pagamentoMovParcela.setMovPagamento(pagamento.getCodigo());
            pagamentoMovParcela.setMovParcela(parcela);
            pagamentoMovParcela.setReciboPagamento(recibo);
            pagamentoMovParcela.setValorPago(obj.getValorTotal());
            pagamentoMovParcela.setUsuarioVO(obj.getResponsavel());

            pagamento.getPagamentoMovParcelaVOs().add(pagamentoMovParcela);

            movPagamentoDAO.incluirSemCommit(pagamento);
            Iterator i = parcela.getMovProdutoParcelaVOs().iterator();
            while (i.hasNext()) {
                MovProdutoParcelaVO movProdutoParcela = (MovProdutoParcelaVO) i.next();
                movProdutoParcela.setReciboPagamento(recibo);
                movProdutoParcelaDAO.alterarSomenteReciboPagamentoSemCommit(movProdutoParcela);
            }
            ProdutosPagosServico produtosPagosServico = new ProdutosPagosServico();
            produtosPagosServico.setarProdutosPagos(getCon(), recibo.getCodigo());

            if (JSFUtilities.isJSFContext() && JSFUtilities.getFromSession("key") != null) {
                GerarCreditosPersonalServico servico = new GerarCreditosPersonalServico(con);
                String returnServ = servico.gerarCreditosColaborador(JSFUtilities.getFromSession("key").toString(), recibo.getCodigo());
                if (returnServ == null || returnServ.isEmpty() || returnServ.toLowerCase().contains("erro")) {
                    throw new Exception("Não foi possível conectar ao TreinoWeb para registrar os créditos do personal. Tente novamente.");
                }

                reciboClienteConsultorDAO.incluirComRecibo(recibo);
            }
        } finally {
            reciboPagamentoDAO = null;
            formaPagamentoDAO = null;
            movPagamentoDAO = null;
            movProdutoParcelaDAO = null;
            reciboClienteConsultorDAO = null;
        }
    }


    private void gerarMovProdutos(VendaAvulsaVO obj, String situacao, Date dataReferencia) throws Exception {
        if(obj.getMovProdutoVOs() == null) {
            obj.setMovProdutoVOs(new ArrayList<MovProdutoVO>());
        }

        Iterator i = obj.getItemVendaAvulsaVOs().iterator();
        while (i.hasNext()) {
            ItemVendaAvulsaVO itemVenda = (ItemVendaAvulsaVO) i.next();
            inicializarMovProduto(itemVenda, obj.getResponsavel(), obj, situacao, dataReferencia);
        }
    }

    private void inicializarMovProduto(ItemVendaAvulsaVO itemVenda, UsuarioVO usuario, VendaAvulsaVO obj, String situacao, Date dataReferencia) throws Exception {
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setProduto(itemVenda.getProduto());
        movProdutoVO.setRenovavelAutomaticamente(itemVenda.getProduto().getRenovavelAutomaticamente());
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao(!obj.getDescricaoAdicional().isEmpty() ? obj.getDescricaoAdicional() : itemVenda.getProduto().getDescricao());
        movProdutoVO.setQuantidade(itemVenda.getQuantidade());
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(dataReferencia));
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(dataReferencia));
        movProdutoVO.setDataInicioVigencia(itemVenda.getDataVenda());
        movProdutoVO.setDataFinalVigencia(itemVenda.getDataValidade());
        movProdutoVO.setVigenciaJaCalculada(itemVenda.getVigenciaJaCalculada());
        movProdutoVO.setDataLancamento(obj.getDataRegistro());
        movProdutoVO.setResponsavelLancamento(usuario);
        if (situacao.equals("EA")) {
            movProdutoVO.setMovpagamentocc(itemVenda.getMovpagamentos());
        }
        if (itemVenda.getTabelaDesconto().getValor() != 0.0 && itemVenda.getTabelaDesconto().getTipoDesconto().equals(TipoDesconto.PE)) {
            movProdutoVO.setValorDesconto(Uteis.arredondarForcando2CasasDecimais(itemVenda.getQuantidade() * (itemVenda.getProduto().getValorFinal() * (itemVenda.getTabelaDesconto().getValor() / 100))));
        } else if (itemVenda.getTabelaDesconto().getValor() != 0.0 && itemVenda.getTabelaDesconto().getTipoDesconto().equals(TipoDesconto.VA)) {
            movProdutoVO.setValorDesconto(itemVenda.getTabelaDesconto().getValor());
        } else if (itemVenda.getDescontoManual()) {
            movProdutoVO.setValorDesconto(itemVenda.getValorDescontoManual());
        } else {
            movProdutoVO.setValorDesconto(0.0);
        }
        movProdutoVO.setPrecoUnitario(itemVenda.getProduto().getValorFinal());
        movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais((movProdutoVO.getPrecoUnitario() * movProdutoVO.getQuantidade()) - movProdutoVO.getValorDesconto()));
        movProdutoVO.setVendaAvulsa(obj.getCodigo());
        movProdutoVO.setEmpresa(obj.getEmpresa());
        if (obj.getTipoComprador().equals("CI")) {
            movProdutoVO.setPessoa(obj.getCliente().getPessoa());
        }
        if (obj.getTipoComprador().equals("CO")) {
            movProdutoVO.setPessoa(obj.getColaborador().getPessoa());
        }
        if (situacao.equals("PG")) {
            movProdutoVO.setSituacao("PG");
        } else {
            movProdutoVO.setSituacao("EA");
        }
        movProdutoVO.setQuitado(true); // apenas para inclusão
        MovProduto movProduto = new MovProduto(this.con);
        movProduto.incluirSemCommit(movProdutoVO);
        movProdutoVO.setQuitado(false); // para gerar o relacionamento entre produtos e parcelas corretamente
        obj.getMovProdutoVOs().add(movProdutoVO);
        movProduto = null;
    }
    private void gravarParcelasArmario(VendaAvulsaVO obj, boolean gerarRecibo, String situacao, Date dataReferencia) throws Exception{
       ZillyonWebFacade zwFacade = new ZillyonWebFacade(this.con);
        if (obj.getMovParcelaVOs() == null) {
            obj.setMovParcelaVOs(new ArrayList<MovParcelaVO>());
        }

        gerarMovProdutos(obj, situacao, dataReferencia);
       for (MovParcelaVO movParcelaVO : obj.getMovParcelaVOs()) {

            if (obj.getValorTotal() != 0.0 && !gerarRecibo) {
                Iterator e = obj.getItemVendaAvulsaVOs().iterator();
                while (e.hasNext()) {
                    ItemVendaAvulsaVO item = (ItemVendaAvulsaVO) e.next();
                    if (item.getProduto().getTipoProduto().equals("CC")) {
                        movParcelaVO.setMovimentoCC(true);
                    }
                    if (item.getMovpagamentos() != null && !item.getMovpagamentos().equals("")) {
                        movParcelaVO.setMovPagamentoCC(item.getMovpagamentos());
                    }
                }
            }
            //gerar relacionamento produto --- parcela
            zwFacade.dividirProdutosNasParcelasArmario(obj.getMovProdutoVOs(), movParcelaVO);
            movParcelaVO.setVendaAvulsaVO(obj);
            movParcelaVO.setEmpresa(obj.getEmpresa());

            if (obj.getTipoComprador().equals("CI")) {
                if (obj.getCliente() == null || obj.getCliente().getPessoa() == null) {
                    movParcelaVO.setPessoa(new PessoaVO());
                } else {
                    movParcelaVO.setPessoa(obj.getCliente().getPessoa());
                }
            } else if (obj.getTipoComprador().equals("CO")) {
                if (obj.getColaborador() == null || obj.getColaborador().getPessoa() == null) {
                    movParcelaVO.setPessoa(new PessoaVO());
                } else {
                    movParcelaVO.setPessoa(obj.getColaborador().getPessoa());
                }
            } else {
                movParcelaVO.setPessoa(new PessoaVO());
            }

            zwFacade.incluirMovParcelaSemCommit(movParcelaVO);
       }
    }
    private void gerarParcela(VendaAvulsaVO obj, boolean gerarRecibo, String situacao, Date dataReferencia) throws Exception{
        int i = 1;
        int mes = 1;
        int nrParcelas = 1;
        if(obj.getValorTotal() != 0.0 && !gerarRecibo){
            nrParcelas = obj.getNrVezesParcelamento();
        } else {
            obj.setNrVezesParcelamento(1);
        }
        double valorParcelas = 0;

        MovParcelaVO movParcelaVO = new MovParcelaVO();
        Date dataAtual = (obj.getVencimentoPrimeiraParcela() == null ? Calendario.hoje() : obj.getVencimentoPrimeiraParcela());
        Date data = (obj.getVencimentoPrimeiraParcela() == null ? Calendario.hoje() : obj.getVencimentoPrimeiraParcela());

        ZillyonWebFacade zwFacade = new ZillyonWebFacade(this.con);
        if (obj.getMovParcelaVOs() == null) {
            obj.setMovParcelaVOs(new ArrayList<MovParcelaVO>());
        }

        gerarMovProdutos(obj, situacao, dataReferencia);
        while (i <= nrParcelas) {
            //preencher valores da parcela
            valorParcelas = obj.obterValorParcelas(i == 1);

            if (obj.getValorTotal() != 0.0 && !gerarRecibo) {
                Iterator e = obj.getItemVendaAvulsaVOs().iterator();
                while (e.hasNext()) {
                    ItemVendaAvulsaVO item = (ItemVendaAvulsaVO) e.next();
                    if (item.getProduto().getTipoProduto().equals("CC")) {
                        movParcelaVO.setMovimentoCC(true);
                    }
                    if (item.getMovpagamentos() != null && !item.getMovpagamentos().equals("")) {
                        movParcelaVO.setMovPagamentoCC(item.getMovpagamentos());
                    }
                }
            }

            movParcelaVO.setValorParcela(valorParcelas);
            movParcelaVO.setValorBaseCalculo(valorParcelas);

            inicializarValoresParcelas(obj, movParcelaVO, i, data, situacao);

            data = Uteis.obterDataFuturaParcela(dataAtual, mes);

            //gerar relacionamento produto --- parcela
            zwFacade.dividirProdutosNasParcelas(obj.getMovProdutoVOs(), movParcelaVO);

            movParcelaVO.setVendaAvulsaVO(obj);
            movParcelaVO.setEmpresa(obj.getEmpresa());

            if (obj.getTipoComprador().equals("CI")) {
                if (obj.getCliente() == null || obj.getCliente().getPessoa() == null) {
                    movParcelaVO.setPessoa(new PessoaVO());
                } else {
                    movParcelaVO.setPessoa(obj.getCliente().getPessoa());
                }
            } else if (obj.getTipoComprador().equals("CO")) {
                if (obj.getColaborador() == null || obj.getColaborador().getPessoa() == null) {
                    movParcelaVO.setPessoa(new PessoaVO());
                } else {
                    movParcelaVO.setPessoa(obj.getColaborador().getPessoa());
                }
            } else {
                movParcelaVO.setPessoa(new PessoaVO());
            }

            zwFacade.incluirMovParcelaSemCommit(movParcelaVO);
            obj.getMovParcelaVOs().add(movParcelaVO);
            movParcelaVO = new MovParcelaVO();
            mes++;
            i++;
        }
        if (gerarRecibo || obj.getValorTotal() == 0.0) {
            MovParcela movParcela = new MovParcela(this.con);
            MovProdutoParcela movProdutoParcela = new MovProdutoParcela(this.con);
            movParcelaVO = movParcela.consultarPorCodigoVendaAvulsa(obj.getCodigo(), "PG", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            movParcelaVO.setMovProdutoParcelaVOs(movProdutoParcela.consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            gerarMovPagamento(obj, movParcelaVO);
         }
    }

    private void inicializarValoresParcelas(VendaAvulsaVO obj, MovParcelaVO movParcela, int nrParcela, Date dataAtual, String situacaoParcela) throws Exception {
        if (obj.getNrVezesParcelamento() == 1) {
            movParcela.setDescricao(obj.getDescricaoAdicional().isEmpty() ? "Venda Avulso" : obj.getDescricaoAdicional());
        } else {
            movParcela.setDescricao(obj.getDescricaoAdicional().isEmpty() ? "Venda Avulso - Parcela " + nrParcela : obj.getDescricaoAdicional());
        }
        movParcela.setDataRegistro(obj.getDataRegistro());
        movParcela.setDataVencimento(dataAtual);
        movParcela.setPercentualJuro(obj.getEmpresa().getJuroParcela());
        movParcela.setPercentualMulta(obj.getEmpresa().getMulta());
        movParcela.setResponsavel(obj.getResponsavel());
        movParcela.setSituacao(situacaoParcela);
    }

    public Integer incluirVendaAvulsaParaEdicaoPagamento(VendaAvulsaVO obj) throws Exception {
        int idVendaAvulsa;
        try (ResultSet rs = incluirSemCommitSomenteVendaAvulsa(obj)) {
            idVendaAvulsa = 0;
            if (rs.next()) {
                idVendaAvulsa = rs.getInt("codigo");
            }
        }
        return idVendaAvulsa;
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>VendaAvulsaVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe
     * <code>VendaAvulsaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(VendaAvulsaVO obj) throws Exception {
        ItemVendaAvulsa itemVendaAvulsaDAO = null;
        try {
            itemVendaAvulsaDAO = new ItemVendaAvulsa(this.con);
            con.setAutoCommit(false);
            VendaAvulsaVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE VendaAvulsa set tipoComprador=?, nomeComprador=?, cliente=?, colaborador=?, valorTotal=?, empresa=?, responsavel=?, dataRegistro=?, origemSistema = ?, pessoa = ? WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(1, obj.getTipoComprador());
                sqlAlterar.setString(2, obj.getNomeComprador());
                if (obj.getCliente().getCodigo() != 0) {
                    sqlAlterar.setInt(3, obj.getCliente().getCodigo());
                } else {
                    sqlAlterar.setNull(3, 0);
                }

                if (obj.getColaborador().getCodigo() != 0) {
                    sqlAlterar.setInt(4, obj.getColaborador().getCodigo());
                } else {
                    sqlAlterar.setNull(4, 0);
                }

                sqlAlterar.setDouble(5, obj.getValorTotal());
                if (obj.getEmpresa().getCodigo() != 0) {
                    sqlAlterar.setInt(6, obj.getEmpresa().getCodigo());
                } else {
                    sqlAlterar.setNull(6, 0);
                }
                if (obj.getResponsavel().getCodigo() != 0) {
                    sqlAlterar.setInt(7, obj.getResponsavel().getCodigo());
                } else {
                    sqlAlterar.setNull(7, 0);
                }
                sqlAlterar.setDate(8, Uteis.getDataJDBC(obj.getDataRegistro()));
                sqlAlterar.setInt(9, obj.getOrigemSistema().getCodigo());

                if (!UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
                    sqlAlterar.setInt(10, obj.getPessoaVO().getCodigo());
                } else {
                    sqlAlterar.setNull(10, 0);
                }

                sqlAlterar.setInt(11, obj.getCodigo());
                sqlAlterar.execute();
            }
            itemVendaAvulsaDAO.alterarItemVendaAvulsas(obj.getCodigo(), obj.getItemVendaAvulsaVOs());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            itemVendaAvulsaDAO = null;
            con.setAutoCommit(true);
        }

    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>VendaAvulsaVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe
     * <code>VendaAvulsaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(VendaAvulsaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirVendasOnline(VendaAvulsaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);

            MovProduto movProdutoDAO = new MovProduto(con);
            movProdutoDAO.excluirMovProdutoVendaAvulsa(obj.getCodigo());
            movProdutoDAO = null;

            MovParcela movParcelaDAO = new MovParcela(con);
            movParcelaDAO.excluirMovParcelaVendaAvulsa(obj.getCodigo());
            movProdutoDAO = null;

            excluirSemCommit(obj);

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    public void estornarFechamentoCreditosPersonal(Integer codigo) throws Exception {
        try (ResultSet rs = criarConsulta(" SELECT iva.codigo FROM itemvendaavulsa iva\n"
                + " INNER JOIN produto p ON iva.produto = p.codigo "
                + "AND iva.vendaavulsa = " + codigo
                + "AND p.tipoproduto = '" + TipoProduto.CREDITO_PERSONAL.getCodigo()
                + "'", con)) {
            String msgNaoFoiPossivel = "Não foi possível estornar o recibo pois ele paga Créditos de Personal e a conexão com o TreinoWeb pode estar comprometida. ";
            if (rs.next()) {
                try {
                    if (JSFUtilities.getFromSession("key") != null) {
                        String result = TreinoWSConsumer.estornarCreditosFechamento(JSFUtilities.getFromSession("key").toString(), codigo);
                        if (!result.equals("ok")) {
                            throw new Exception(result);
                        }
                    } else {
                        throw new Exception(msgNaoFoiPossivel);
                    }
                } catch (Exception e) {
                    throw new Exception(msgNaoFoiPossivel + e.getMessage());
                }
            }
        }
    }

    public void excluirSemCommit(VendaAvulsaVO obj) throws Exception {
        excluirSemCommit(obj, true);
    }

    public void excluirSemCommit(VendaAvulsaVO obj, boolean verificarAcesso) throws Exception {
        ItemVendaAvulsa itemVendaAvulsaDAO = null;
        try {
            itemVendaAvulsaDAO = new ItemVendaAvulsa(this.con);
            estornarFechamentoCreditosPersonal(obj.getCodigo());
            if (verificarAcesso) {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM VendaAvulsa WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            itemVendaAvulsaDAO.excluirItemVendaAvulsas(obj.getCodigo(), verificarAcesso);
        } finally {
            itemVendaAvulsaDAO = null;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>VendaAvulsaVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * <code>VendaAvulsaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluirVendaAvulsaConsumidor(Integer codMovProduto) throws Exception {
        ItemVendaAvulsa itemVendaAvulsaDAO = null;
        try {
            itemVendaAvulsaDAO = new ItemVendaAvulsa(this.con);
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "delete FROM vendaavulsa "
                    + "where codigo = (select movparcela.vendaavulsa from movproduto inner join produto on movproduto.produto = produto.codigo "
                    + "inner join movprodutoparcela on movproduto.codigo = movprodutoparcela.movproduto "
                    + "inner join movparcela on movprodutoparcela.movparcela = movparcela.codigo "
                    + "inner join vendaavulsa on movparcela.vendaavulsa = vendaavulsa.codigo "
                    + "where  (Produto.tipoProduto <> 'DE' or Produto.tipoProduto <> 'DV' or Produto.tipoProduto <> 'QU' ) "
                    + "and vendaAvulsa.tipocomprador = 'CN' "
                    + "and movproduto.pessoa is null "
                    + "and movproduto.codigo= " + codMovProduto + ");";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, codMovProduto);
                sqlExcluir.execute();
            }
            itemVendaAvulsaDAO.excluirItemVendaAvulsas(codMovProduto);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            itemVendaAvulsaDAO = null;
            con.setAutoCommit(true);
        }

    }

    /**
     * Responsável por realizar uma consulta de
     * <code>VendaAvulsa</code> através do valor do atributo
     * <code>String nomeComprador</code>. Retorna os objetos, com início do
     * valor do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     * <code>VendaAvulsaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNomeComprador(
            String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM VendaAvulsa WHERE upper( nomeComprador ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nomeComprador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }




    public Boolean consultarPorClienteProdutoGenerico(int cliente, int produto) throws Exception {
        StringBuilder sqlStr;
        try{
            sqlStr = new StringBuilder("SELECT vendaavulsa.codigo FROM VendaAvulsa ");
            sqlStr.append("inner join itemvendaavulsa on itemvendaavulsa.vendaavulsa = vendaavulsa.codigo and itemvendaavulsa.produto =").append(produto).append(" and itemvendaavulsa.pacote <> 0 ");
            sqlStr.append("where cliente =").append(cliente);
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                    return tabelaResultado.next();
                }
            }
        }catch(Exception e){
            throw e;
        }finally{
            sqlStr = null;
        }
    }

    public VendaAvulsaVO consultarPorClienteProduto(int cliente, int produto) throws Exception {
        String sql =
                "SELECT va.* " +
                        "FROM vendaavulsa va " +
                        "INNER JOIN itemvendaavulsa iva ON iva.vendaavulsa = va.codigo " +
                        "WHERE iva.produto = ? AND va.cliente = ?";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, produto);
            ps.setInt(2, cliente);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            }
        }
        return null;
    }


    /**
     * Responsável por realizar uma consulta de
     * <code>VendaAvulsa</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     * <code>VendaAvulsaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(
            Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM VendaAvulsa WHERE codigo = " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>VendaAvulsaVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public VendaAvulsaVO consultarPorChavePrimaria(
            Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM VendaAvulsa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( VendaAvulsa ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public String obterContratoProdutoTextoPadrao(
            Integer vendaAvulsa, Integer produto) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT texto FROM produtotextopadrao WHERE vendaavulsa = ? and produto = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, vendaAvulsa);
            sqlConsultar.setInt(2, produto);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return "";
                }
                return tabelaResultado.getString("texto");
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>VendaAvulsa</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     * <code>VendaAvulsaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarConsumidorPorCodigo(
            Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM VendaAvulsa WHERE tipoComprador = 'CN' and codigo = " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>VendaAvulsaVO</code> através de seu tipo de comprador.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public List consultarPorTipoComprador(String tipoComprador, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM VendaAvulsa WHERE tipoComprador = '" + tipoComprador + "' ORDER BY dataregistro desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>VendaAvulsaVO</code> do tipo consumidor (CN) através do nome do
     * comprador.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public List consultarConsumidorPorNomeComprador(String nomeComprador, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM VendaAvulsa WHERE tipoComprador = 'CN' AND nomecomprador ilike '" + nomeComprador + "%' ORDER BY nomeComprador, dataregistro desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>VendaAvulsaVO</code> do tipo consumidor (CN) através do valor da
     * compra.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public List consultarConsumidorPorValorCompra(double valorCompra, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM VendaAvulsa WHERE tipoComprador = 'CN' AND valorTotal = '" + valorCompra + "' ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe
     * <code>VendaAvulsaVO</code> resultantes da consulta.
     */
    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            VendaAvulsaVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>VendaAvulsaVO</code>.
     *
     * @return O objeto da classe
     * <code>VendaAvulsaVO</code> com os dados devidamente montados.
     */
    public VendaAvulsaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        VendaAvulsaVO obj = new VendaAvulsaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setTipoComprador(dadosSQL.getString("tipoComprador"));
        obj.setNomeComprador(dadosSQL.getString("nomeComprador"));
        obj.getCliente().setCodigo(new Integer(dadosSQL.getInt("cliente")));
        obj.getColaborador().setCodigo(new Integer(dadosSQL.getInt("colaborador")));
        obj.getEmpresa().setCodigo(new Integer(dadosSQL.getInt("empresa")));
        obj.getResponsavel().setCodigo(new Integer(dadosSQL.getInt("responsavel")));
        obj.setValorTotal(dadosSQL.getDouble("valorTotal"));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(dadosSQL.getInt("origemSistema")));

        try {
            obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
        } catch (Exception ignored) {
        }

        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        ItemVendaAvulsa itemVendaAvulsaDAO = new ItemVendaAvulsa(this.con);
        obj.setItemVendaAvulsaVOs(itemVendaAvulsaDAO.consultarItemVendaAvulsas(obj.getCodigo(), nivelMontarDados));
        itemVendaAvulsaDAO = null;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            MovParcela movParcelaDAO = new MovParcela(this.con);
            obj.setMovParcelaVOs(movParcelaDAO.consultarPorCodigoVendaAvulsaLista(obj.getCodigo(),null,null,"",false,Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            movParcelaDAO = null;
            return obj;
        }

        montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        montarDadosColaborador(obj, nivelMontarDados);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>VendaAvulsaVO</code>. Faz uso da chave primária da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public void montarDadosColaborador(VendaAvulsaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getColaborador().getCodigo() == 0) {
            obj.setColaborador(new ColaboradorVO());
            return;
        }

        Colaborador colaboradorDAO = new Colaborador(this.con);
        obj.setColaborador(colaboradorDAO.consultarPorChavePrimaria(obj.getColaborador().getCodigo(), nivelMontarDados));
        colaboradorDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>VendaAvulsaVO</code>. Faz uso da chave primária da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public void montarDadosCliente(VendaAvulsaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getCliente().getCodigo() == 0) {
            obj.setCliente(new ClienteVO());
            return;
        }

        Cliente clienteDAO = new Cliente(this.con);
        obj.setCliente(clienteDAO.consultarPorChavePrimaria(obj.getCliente().getCodigo(), nivelMontarDados));
        clienteDAO = null;
    }

    public Hashtable getItemVendaAvulsas() {
        if (itemVendaAvulsas == null) {
            itemVendaAvulsas = new Hashtable();
        }

        return (itemVendaAvulsas);
    }

    public void setItemVendaAvulsas(Hashtable itemVendaAvulsas) {
        this.itemVendaAvulsas = itemVendaAvulsas;
    }

    public List<ItemVendaAvulsaVO> consultarPaginado(Integer idCliente, Integer idEmpresa, ConfPaginacao confPaginacao) throws Exception {
        Pacote pacoteDAO = null;
        try {
            pacoteDAO = new Pacote(this.con);
            //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
            confPaginacao.configurarNavegacao();

            //sql principal
            try (PreparedStatement ps = getCon().prepareStatement(SQL_VENDAS_STUDIO)) {
                ps.setInt(1, idCliente);
                ps.setInt(2, idEmpresa);
                //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
                confPaginacao.iniciarPaginacao(this);

                //3 - ADICIONA PAGINACAO NA CONSULTA
                confPaginacao.addPaginacao(new StringBuffer(ps.toString()));
            }

            //4 - REALIZA A CONSULTA COM PAGINACAO
            List<ItemVendaAvulsaVO> listaItem;
            try (ResultSet rs = confPaginacao.consultaPaginada()) {
                listaItem = new ArrayList<ItemVendaAvulsaVO>();
                while (rs.next()) {
                    ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();

                    item.setVendaAvulsa(rs.getInt("codigo"));
                    item.setDataVenda(rs.getTimestamp("dataregistro"));
                    item.setValorParcial(rs.getDouble("valorparcial"));
                    item.setQuantidade(rs.getInt("quantidade"));
                    item.getTabelaDesconto().setCodigo(new Integer(rs.getInt("tabelaDesconto")));
                    item.getProduto().setCodigo(rs.getInt("produto"));
                    item.setPacoteVO(pacoteDAO.consultarPorCodigo(rs.getInt("pacote")));
                    item.getProduto().setValorFinal(rs.getDouble("valorfinal"));
                    if (Validador.isValidaInteger(item.getPacoteVO().getCodigo())) {
                        item.getProduto().setValorFinal(rs.getDouble("valor_unitario"));
                    }
                    item.getProduto().setDescricao(rs.getString("descricao"));
                    item.setCodigo(rs.getInt("item"));
                    item.setUsuarioVO(new UsuarioVO());
                    item.getUsuarioVO().setNome(rs.getString("nome"));
                    listaItem.add(item);
                }
            }
            return listaItem;

        } finally {
            pacoteDAO = null;
        }
    }

    @Override
    public List<ItemVendaAvulsaVO> listarVendaTipoProdutoCliente(Integer idCliente,
            Integer idEmpresa, String tipoProduto) throws SQLException {

        List<ItemVendaAvulsaVO> listaItem;
        try (PreparedStatement ps = getCon().prepareStatement(SQL_VENDAS_STUDIO)) {
            ps.setInt(1, idCliente);
            ps.setInt(2, idEmpresa);
            try (ResultSet rs = ps.executeQuery()) {
                listaItem = new ArrayList<ItemVendaAvulsaVO>();
                while (rs.next()) {
                    ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();

                    item.setVendaAvulsa(rs.getInt("codigo"));
                    item.setDataVenda(rs.getDate("dataregistro"));
                    item.setValorParcial(rs.getDouble("valorparcial"));
                    item.setQuantidade(rs.getInt("quantidade"));
                    item.getTabelaDesconto().setCodigo(new Integer(rs.getInt("tabelaDesconto")));
                    item.getProduto().setCodigo(rs.getInt("produto"));
                    item.getProduto().setDescricao(rs.getString("descricao"));
                    item.getProduto().setValorFinal(rs.getDouble("valorfinal"));

                    listaItem.add(item);
                }
            }
        }

        return listaItem;
    }

    private String montarSqlICV(Date prmIni, Date prmFim, Integer empresa, List<ColaboradorVO> listaColaborador, List<TipoBVEnum> listaTipoBVEnum, String campos, boolean detalhe) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ").append(campos).append(" FROM cliente cli \n");
        sql.append(" INNER JOIN questionariocliente qc ON cli.codigo = qc.cliente \n");
        if (detalhe) {
            sql.append(" INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n");
            sql.append(" INNER JOIN colaborador col ON qc.consultor = col.codigo \n");
            sql.append(" INNER JOIN pessoa pescol ON pescol.codigo = col.pessoa \n");
        }
        sql.append(" INNER JOIN vendaavulsa va ON va.cliente = cli.codigo \n");
        sql.append(" INNER JOIN itemvendaavulsa it ON it.vendaavulsa = va.codigo \n");
        sql.append(" INNER JOIN produto p ON p.codigo = it.produto \n");
        sql.append(" WHERE qc.data BETWEEN '").append(Uteis.getDataJDBC(prmIni)).append(" 00:00:00' and '").append(Uteis.getDataJDBC(prmFim)).append(" 23:59:59' \n");
        sql.append(" AND va.dataregistro BETWEEN '").append(Uteis.getDataJDBC(prmIni)).append(" 00:00:00' and '").append(Uteis.getDataJDBC(prmFim)).append(" 23:59:59' \n");
        sql.append(" AND p.tipoproduto LIKE 'SS' \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and cli.empresa = ").append(empresa);
        }
        String codigoCol = "";
        for (ColaboradorVO col : listaColaborador) {
            if (col.getColaboradorEscolhidoRenovacao() || col.getColaboradorEscolhidoIndiceConversao()) {
                codigoCol += "," + col.getCodigo();
            }
        }
        if (!codigoCol.isEmpty()) {
            sql.append(" and qc.consultor in (").append(codigoCol.replaceFirst(",", "")).append(") ");
        }

        String tipoBV = "";
        for (TipoBVEnum tipoBVEnum : listaTipoBVEnum) {
            tipoBV += "," + tipoBVEnum.getCodigo();
        }
        if (!tipoBV.isEmpty()) {
            sql.append(" and qc.tipobv in (").append(tipoBV.replaceFirst(",", "")).append(") ");
        }

        return sql.toString();
    }

    public Integer consultaQuantidadeSessaoICV(Date prmIni,Date prmFim, Integer empresa, List<ColaboradorVO> listaColaborador, List<TipoBVEnum> listaTipoBVEnum) throws Exception {
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(montarSqlICV(prmIni, prmFim, empresa, listaColaborador, listaTipoBVEnum, " count(distinct cli.*) ", false))) {
                tabelaResultado.next();
                return (tabelaResultado.getInt(1));
            }
        }
    }

    public List<ClientesICV> consultaListaSessaoICV(Date prmIni, Date prmFim, Integer empresa, List<ColaboradorVO> listaColaborador, List<TipoBVEnum> listaTipoBVEnum) throws Exception {
        List<ClientesICV> vetResultado;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(montarSqlICV(prmIni, prmFim, empresa, listaColaborador, listaTipoBVEnum, " distinct cli.situacao, cli.matricula, cli.codigo as cliente, pes.nome as nomecliente, pescol.nome as consultor ", true))) {
                vetResultado = new ArrayList<ClientesICV>();
                while (tabelaResultado.next()) {
                    ClientesICV obj = new ClientesICV();
                    obj.setCliente(new ClienteVO());
                    obj.getCliente().setCodigo(tabelaResultado.getInt("cliente"));
                    obj.getCliente().setSituacao(tabelaResultado.getString("situacao"));
                    obj.getCliente().setMatricula(tabelaResultado.getString("matricula"));
                    obj.getCliente().setPessoa(new PessoaVO());
                    obj.getCliente().getPessoa().setNome(tabelaResultado.getString("nomecliente"));
                    obj.setConsultor(new ColaboradorVO());
                    obj.getConsultor().setPessoa(new PessoaVO());
                    obj.getConsultor().getPessoa().setNome(tabelaResultado.getString("consultor"));
                    vetResultado.add(obj);
                }
            }
        }

        return vetResultado;
    }

    public String consultarJSON(Integer empresa) throws Exception {
        JSONObject aaData;
        JSONArray valores;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            aaData = new JSONObject();
            valores = new JSONArray();

            while (rs.next()) {
                JSONArray itemArray = new JSONArray();

                itemArray.put(rs.getString("codigo"));
                itemArray.put(rs.getString("nomecomprador"));
                itemArray.put(rs.getString("responsavel"));
                itemArray.put(rs.getDate("dataregistro"));
                itemArray.put(rs.getString("empresa"));
                itemArray.put(Formatador.formatarValorMonetario(rs.getDouble("valortotal")));
                valores.put(itemArray);

            }
        }
        aaData.put("aaData", valores);
        return aaData.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT va.codigo, va.nomecomprador, usr.nome AS responsavel, \n" + "va.dataregistro, emp.nome AS empresa, va.valortotal \n" + "FROM vendaavulsa va \n" + " LEFT JOIN usuario usr ON va.responsavel = usr.codigo \n" + " LEFT JOIN empresa emp ON va.empresa = emp.codigo \n" + "WHERE 1 = 1 ");
        if (empresa != 0) {
            sql.append(" AND va.empresa = ?");
        }
        sql.append(" AND tipocomprador = 'CN'");
        sql.append(" ORDER BY va.codigo DESC");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        return sqlConsultar;

    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            lista = new ArrayList();

            while (rs.next()) {

                VendaAvulsaVO vAvulsa = new VendaAvulsaVO();
                String geral = rs.getString("codigo") + rs.getString("nomecomprador") + rs.getString("responsavel") + rs.getString("dataregistro") + rs.getString("empresa") + rs.getString("valortotal");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    vAvulsa.setCodigo(rs.getInt("codigo"));
                    vAvulsa.setNomeComprador(rs.getString("nomecomprador"));
                    vAvulsa.setUsuarioVO(new UsuarioVO());
                    vAvulsa.getUsuarioVO().setNome(rs.getString("responsavel"));
                    vAvulsa.setDataRegistro(rs.getDate("dataregistro"));
                    vAvulsa.getEmpresa().setNome(rs.getString("empresa"));
                    vAvulsa.setValorTotal(rs.getDouble("valortotal"));
                    lista.add(vAvulsa);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Consumidor")) {
            Ordenacao.ordenarLista(lista, "nomeComprador");
        } else if (campoOrdenacao.equals("Responsável")) {
            Ordenacao.ordenarLista(lista, "responsavel_Apresentar");
        } else if (campoOrdenacao.equals("Data")) {
            Ordenacao.ordenarLista(lista, "dataRegistro");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        } else if (campoOrdenacao.equals("Valor")) {
            Ordenacao.ordenarLista(lista, "valor_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
    
    @Override
    public String obterNomeComprador(Integer codigoVenda) throws Exception{
        try (ResultSet rs = criarConsulta("SELECT nomecomprador FROM vendaavulsa WHERE codigo = " + codigoVenda, con)) {
            if (rs.next()) {
                return rs.getString("nomecomprador");
            } else {
                return "";
            }
        }
    }
    
    @Override
    public String gerarVendaCreditos(Integer codigoColaborador, Integer unidadesCredito, Integer codigoUsuario){
        GerarCreditosPersonalServico servico = new GerarCreditosPersonalServico(con);
        return servico.gerarVendaCreditos(codigoColaborador, unidadesCredito, codigoUsuario);
        
    }


    public String gerarVendaProdutoCliente(Integer codigoCliente, Integer codigoProduto, Integer codigoUsuario, Date vencimento, Double valor) {
        try {
            Conexao.guardarConexaoForJ2SE(con);
            Cliente clienteDao = new Cliente(con);
            ProdutoInterfaceFacade produtoDao = new Produto(con);
            UsuarioInterfaceFacade usuarioDao = new Usuario(con);
            UsuarioVO usuario = UteisValidacao.emptyNumber(codigoUsuario) ?
                    usuarioDao.getUsuarioRecorrencia() :
                    usuarioDao.consultarPorCodigo(codigoUsuario, Uteis.NIVELMONTARDADOS_MINIMOS);
            ClienteVO clienteVO = clienteDao.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_TODOS);
            ProdutoVO produto = produtoDao.obterProdutoCfgEmpresa(codigoProduto, clienteVO.getEmpresa().getCodigo());
            if (produto != null) {
                produto.setValorFinal(valor == null ? produto.getValorFinal() : valor);
                VendaAvulsaVO vendaAvulsa = new VendaAvulsaVO();
                vendaAvulsa.setDescricaoAdicional(produto.getDescricao());
                vendaAvulsa.setCliente(clienteVO);
                vendaAvulsa.setNomeComprador(clienteVO.getPessoa().getNome());
                vendaAvulsa.setDataRegistro(Calendario.hoje());
                vendaAvulsa.setEmpresa(clienteVO.getEmpresa());
                vendaAvulsa.setResponsavel(usuario);
                ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
                item.setProduto(produto);
                item.setQuantidade(1);
                item.setValorParcial(valor == null ? produto.getValorFinal() : valor);
                vendaAvulsa.setValorTotal(valor == null ? item.getValorParcial() : valor);
                vendaAvulsa.setTipoComprador("CI");
                vendaAvulsa.getItemVendaAvulsaVOs().add(item);
                Integer codigoVenda = incluir(vendaAvulsa, UteisValidacao.emptyNumber(produto.getValorFinal()), vencimento, null,null, null);
                return "ok|"+codigoVenda;
            }
            return "Erro: Produto não encontrado";
        } catch (Exception e) {
            return "Erro: " + e.getMessage();
        }
    }
    

    private void gravarAlteracoesEstudio(VendaAvulsaVO vendaAvulsaVO, List<DisponibilidadeVO> listaDisponibilidade,Date dataIniAgendar, Date dataFimAgendar) throws Exception {
        AgendaEstudio agendaDAO = new AgendaEstudio(con);
        boolean sessao = false;
        FecharMetaDetalhado fMetaDao = new FecharMetaDetalhado(con);
        try {
            if (!UteisValidacao.emptyList(listaDisponibilidade)) {
                Iterator e = null;
                Iterator eDisp = listaDisponibilidade.iterator();

                // salva relação da venda com a agenda específica e apaga do a faturar
                while (eDisp.hasNext()) {
                    DisponibilidadeVO dis = (DisponibilidadeVO) eDisp.next();
                    if (!dis.getVerificadorAgendamento()) {

                        e =  vendaAvulsaVO.getItemVendaAvulsaVOs().iterator();

                        while (e.hasNext()) {
                            ItemVendaAvulsaVO obj = (ItemVendaAvulsaVO) e.next();
                            if (obj.getProduto().getCodigo().equals(dis.getProdutoVO().getCodigo())) {
                                obj.setQuantidadeAgenda(obj.getQuantidadeAgenda() == null ? 0 : obj.getQuantidadeAgenda());
                                if (obj.getQuantidadeAgenda() < obj.getQuantidade()) {
                                    agendaDAO.salvarRelacaoAgendaVenda(dis.getIdAgenda(),  vendaAvulsaVO.getCodigo(), false);
                                    agendaDAO.apagarAgendaFaturar(dis.getIdAgenda(), false, false);
                                    obj.setUsouQuantidadeAgenda(true);
                                    obj.setQuantidadeAgenda(obj.getQuantidadeAgenda() + 1);
                                }
                            }
                        }
                    }
                }

                // Salva em agenda agendar os itens no qual foi adicionado fora do módulo estúdio
                e = vendaAvulsaVO.getItemVendaAvulsaVOs().iterator();
                while (e.hasNext()) {
                    ItemVendaAvulsaVO obj = (ItemVendaAvulsaVO) e.next();
                    if (obj.getProduto().getTipoProduto().equals("SS")) { // Se produto é do tipo SESSÃO
                        sessao = true;
                        for (int i = 0; i < (obj.getUsouQuantidadeAgenda() ? obj.getQuantidade() - obj.getQuantidadeAgenda() : obj.getQuantidade()); i++) {
                            agendaDAO.salvarAgendaAgendar(obj,  vendaAvulsaVO.getCliente().getCodigo(),  vendaAvulsaVO.getEmpresa().getCodigo(),
                                    dataIniAgendar, dataFimAgendar, false);
                        }
                    }
                }
            } else {
                Iterator e =  vendaAvulsaVO.getItemVendaAvulsaVOs().iterator();
                while (e.hasNext()) {
                    ItemVendaAvulsaVO obj = (ItemVendaAvulsaVO) e.next();
                    if (obj.getProduto().getTipoProduto().equals("SS")) { // Se produto é do tipo SESSÃO
                        sessao = true;
                        for (int i = 0; i < obj.getQuantidade(); i++) {
                            agendaDAO.salvarAgendaAgendar(obj,  vendaAvulsaVO.getCliente().getCodigo(),  vendaAvulsaVO.getEmpresa().getCodigo(),
                                    dataIniAgendar, dataFimAgendar, false);
                        }
                    }
                }
            }
           
            if (sessao) {
                fMetaDao.baterMetaPorFase(vendaAvulsaVO.getCliente().getCodigo(), null, vendaAvulsaVO.getDataRegistro(), FasesCRMEnum.ULTIMAS_SESSOES.getSigla(), 0,0,0);
                fMetaDao.baterMetaPorFase(vendaAvulsaVO.getCliente().getCodigo(), null, vendaAvulsaVO.getDataRegistro(), FasesCRMEnum.SEM_AGENDAMENTO.getSigla(), 0,0,0);
            }
        }catch(Exception e){
            throw e;
        } finally{
            agendaDAO = null;
            fMetaDao = null;
        }
    }

    public String gerarVendaProdutoCliente(Integer codigoCliente, Integer codigoProduto, Integer codigoUsuario, Date vencimento) {
        try {
            Conexao.guardarConexaoForJ2SE(con);
            Cliente clienteDao = new Cliente(con);
            ProdutoInterfaceFacade produtoDao = new Produto(con);
            UsuarioInterfaceFacade usuarioDao = new Usuario(con);
            UsuarioVO usuario = UteisValidacao.emptyNumber(codigoUsuario) ?
                    usuarioDao.getUsuarioRecorrencia() :
                    usuarioDao.consultarPorCodigo(codigoUsuario, Uteis.NIVELMONTARDADOS_MINIMOS);
            ClienteVO clienteVO = clienteDao.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_TODOS);
            ProdutoVO produto = produtoDao.obterProdutoCfgEmpresa(codigoProduto, clienteVO.getEmpresa().getCodigo());
            if (produto != null) {
                VendaAvulsaVO vendaAvulsa = new VendaAvulsaVO();
                vendaAvulsa.setDescricaoAdicional(produto.getDescricao());
                vendaAvulsa.setCliente(clienteVO);
                vendaAvulsa.setNomeComprador(clienteVO.getPessoa().getNome());
                vendaAvulsa.setDataRegistro(Calendario.hoje());
                vendaAvulsa.setEmpresa(clienteVO.getEmpresa());
                vendaAvulsa.setResponsavel(usuario);
                ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
                item.setProduto(produto);
                item.setQuantidade(1);
                item.setValorParcial(produto.getValorFinal());
                vendaAvulsa.setValorTotal(item.getValorParcial());
                vendaAvulsa.setTipoComprador("CI");
                vendaAvulsa.getItemVendaAvulsaVOs().add(item);
                vendaAvulsa.setVencimentoPrimeiraParcela(vencimento);
                Integer codigoVenda = incluir(vendaAvulsa, UteisValidacao.emptyNumber(produto.getValorFinal()), vencimento, null,null, null);
                return "ok|"+codigoVenda;
            }
            return "Erro: Produto não encontrado";
        } catch (Exception e) {
            return "Erro: " + e.getMessage();
        }
    }

    public VendaAvulsaVO gerarVendaAtestado(ClienteVO clienteVO, Date dataInicio, Date dataFinal, ProdutoVO produto, Double valor, UsuarioVO responsavel, EmpresaVO empresaVO) throws Exception {
        VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();

        vendaAvulsaVO.setTipoComprador("CI");
        vendaAvulsaVO.setCliente(clienteVO);
        vendaAvulsaVO.setDataRegistro(dataInicio);
        vendaAvulsaVO.setEmpresa(empresaVO);

        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        item.setDataVenda(dataInicio);
        item.setDataValidade(dataFinal);
        item.setQuantidade(1);
        item.setUsuarioVO(responsavel);
        item.setProduto(produto);
        item.setValorParcial(valor);
        item.getProduto().setValorFinal(valor);
        vendaAvulsaVO.setValorTotal(valor);
        vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
        vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
        vendaAvulsaVO.setResponsavel(item.getUsuarioVO());
        vendaAvulsaVO.setDescricaoAdicional("Atestado de Aptidão Física");
        VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
        vendaAvulsaDAO.incluirSemCommit(vendaAvulsaVO, false, Calendario.hoje());
        vendaAvulsaDAO = null;
        return vendaAvulsaVO;
    }

    public void alterarPessoa(Integer pessoa, Integer vendaAvulsa, String descricaoLog, UsuarioVO usuarioVO, boolean controlaTransacao) throws Exception {
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            Integer pessoaAnterior = obterPessoa(vendaAvulsa);
            String sql = "UPDATE vendaavulsa set pessoa = ? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setInt(1, pessoa);
                sqlAlterar.setInt(2, vendaAvulsa);
                sqlAlterar.execute();
            }
            gerarLogAlterarPessoa(vendaAvulsa, pessoa, pessoaAnterior, descricaoLog, usuarioVO);
            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception ex) {
            if (controlaTransacao) {
                con.rollback();
            }
            throw ex;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    private Integer obterPessoa(Integer vendaAvulsa) throws Exception {
        String sql = "SELECT pessoa FROM vendaAvulsa WHERE codigo = ?";
        try (PreparedStatement sqlFoto = con.prepareStatement(sql)) {
            sqlFoto.setInt(1, vendaAvulsa);
            try (ResultSet rs = sqlFoto.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                } else {
                    return null;
                }
            }
        }
    }

    private void gerarLogAlterarPessoa(Integer vendaAvulsa, Integer pessoaNova, Integer pessoaAlterior, String descricaoLog, UsuarioVO usuarioVO) throws Exception {
        Log logDAO;
        try {
            if (usuarioVO == null) {
                return;
            }
            logDAO = new Log(con);

            LogVO log = new LogVO();
            log.setNomeEntidade("VENDAAVULSA");
            log.setNomeEntidadeDescricao("VENDAAVULSA");
            log.setDescricao("ALTERAÇÃO - PESSOA");
            log.setChavePrimaria(vendaAvulsa.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("PESSOA");
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setPessoa(pessoaNova);
            log.setValorCampoAnterior(pessoaAlterior.toString());
            if (!UteisValidacao.emptyString(descricaoLog)) {
                log.setValorCampoAlterado(descricaoLog += "\n\nNovo CPF: " + pessoaNova);
            } else {
                log.setValorCampoAlterado(pessoaNova.toString());
            }

            logDAO.incluirSemCommit(log);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro ao gerarLogAlterarPessoa VendaAvulsa " + ex.getMessage());
            throw ex;
        } finally {
            logDAO = null;
        }
    }
}
