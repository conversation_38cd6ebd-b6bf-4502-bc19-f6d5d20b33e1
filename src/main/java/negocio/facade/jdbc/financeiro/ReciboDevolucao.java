package negocio.facade.jdbc.financeiro;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboDevolucaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.executarConsulta;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;

public class ReciboDevolucao extends SuperEntidade {

	public ReciboDevolucao() throws Exception {
		super();
	}
	public ReciboDevolucao(Connection con) throws Exception {
		super(con);
	}
	
	/**
	 * Joao Alcides
	 * 21/05/2012
	 */
	public void incluirSemCommit(ReciboDevolucaoVO recibo, Connection con) throws Exception{
		ReciboDevolucaoVO.validarDados(recibo);
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO recibodevolucao(contrato, usuario, datadevolucao, valordevolucao, "); 
		sql.append("        valortotalpagopelocliente, valorutilizadopelocliente, valortotalsomaprodutocontratos,  ");
        sql.append("        valortaxacancelamento, valormultacancelamento, valordevolvidoemdinheiro, quitacao, ");
        sql.append("        quitacaomanual, liberacao, valorcontrato, valorcontacorrente, liberacaodevolucao, ");
        sql.append("        valorrealdevolucao, devolucaomanual, movproduto, pessoa, valororiginal, proddevolucao, prodrecebiveis, valorbasecontrato, reciboeditado) ");
        sql.append("VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?); ");
		PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
		int i = 1;
		//contrato
		if (recibo.getContrato().getCodigo() != 0) {
                    sqlInserir.setInt(i++, recibo.getContrato().getCodigo().intValue());
		} else {
                    sqlInserir.setNull(i++, 0);
                }
		//usuario
		sqlInserir.setInt(i++, recibo.getResponsavelDevolucao().getCodigo());
		//data
		sqlInserir.setTimestamp(i++, new java.sql.Timestamp(recibo.getDataDevolucao().getTime()));
		//valor
		sqlInserir.setDouble(i++, recibo.getValorDevolucao());
		//pago
		sqlInserir.setDouble(i++, recibo.getValorTotalPagoPeloCliente());
		//utilizado
		sqlInserir.setDouble(i++, recibo.getValorUtilizadoPeloCliente());
		//somaprod
		sqlInserir.setDouble(i++, recibo.getValorTotalSomaProdutoContratos());
		//taxa
		sqlInserir.setDouble(i++, recibo.getValorTaxaCancelamento());
		//multa
		sqlInserir.setDouble(i++, recibo.getValorMultaCancelamento());
		
		sqlInserir.setDouble(i++, recibo.getValorDevolvidoEmDinheiro());
		
		sqlInserir.setBoolean(i++, recibo.getQuitacao());
		
		sqlInserir.setBoolean(i++, recibo.getQuitacaoManual());
		
		sqlInserir.setBoolean(i++, recibo.getLiberacao());
		
		sqlInserir.setDouble(i++, recibo.getValorContrato());
		
		sqlInserir.setDouble(i++, recibo.getValorEmContaCorrente());
		
		sqlInserir.setBoolean(i++, recibo.getLiberacaoDevolucao());
		
		if(recibo.getValorRealDevolucao() == null){
			sqlInserir.setDouble(i++, recibo.getValorDevolucao());
		}else
			sqlInserir.setDouble(i++, recibo.getValorRealDevolucao());
		
		
		sqlInserir.setBoolean(i++, recibo.getDevolucaoManual());

        if (recibo.getProdutoVO().getCodigo() != 0) {
            sqlInserir.setInt(i++, recibo.getProdutoVO().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (recibo.getPessoa() != null && recibo.getPessoa() != 0) {
            sqlInserir.setInt(i++, recibo.getPessoa());
        } else {
            sqlInserir.setNull(i++, 0);
        }

        if (recibo.getValorOriginal() != null && recibo.getValorOriginal() != 0) {
            sqlInserir.setDouble(i++, recibo.getValorOriginal());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (recibo.getProdDevolucao() != 0) {
            sqlInserir.setInt(i++, recibo.getProdDevolucao());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (recibo.getProdRecebiveis()!= 0) {
            sqlInserir.setInt(i++, recibo.getProdRecebiveis());
        } else {
            sqlInserir.setNull(i++, 0);
        }
		sqlInserir.setDouble(i++, recibo.getValorBaseContrato());
        if (recibo.getReciboEditado() != 0) {
            sqlInserir.setInt(i++, recibo.getReciboEditado());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        sqlInserir.execute();
		
        recibo.setCodigo(obterValorChavePrimariaCodigo());
        incluirRecebiveis(recibo);
        if(!UteisValidacao.emptyList(recibo.getListaPagamentos())){
            incluirMovPagamentoReciboDevolucao(recibo.getCodigo(), recibo.getListaPagamentos());
        }
        if(!UteisValidacao.emptyList(recibo.getParcelasEdicaoPagamento())){
            incluirParcelasEdicao(recibo);
        }
    }
	
	public ReciboDevolucaoVO consultarPorContrato(Integer codigoContrato, Integer nivelMontarDados) throws Exception{
		String sql = "SELECT * FROM recibodevolucao WHERE contrato = "+codigoContrato;
		ResultSet resultSet = ReciboDevolucao.criarConsulta(sql, con);
		return resultSet.next() ? montarDados(resultSet, con,nivelMontarDados) : new ReciboDevolucaoVO();
	}
	
	public ReciboDevolucaoVO consultarPorMovProduto(Integer codigoMovproduto, Integer nivelMontarDados) throws Exception{
		String sql = "SELECT * FROM recibodevolucao WHERE movproduto = "+codigoMovproduto;
		ResultSet resultSet = ReciboDevolucao.criarConsulta(sql, con);
		return resultSet.next() ? montarDados(resultSet, con,nivelMontarDados) : new ReciboDevolucaoVO();
	}
	
	public ReciboDevolucaoVO consultarPorChavePrimaria(Integer codigo, Integer nivelMontarDados) throws Exception{
		String sql = "SELECT * FROM recibodevolucao WHERE codigo = "+codigo;
		ResultSet resultSet = ReciboDevolucao.criarConsulta(sql, con);
		return resultSet.next() ? montarDados(resultSet, con,nivelMontarDados) : new ReciboDevolucaoVO();
	}

        public List<ReciboDevolucaoVO> consultarPorPessoa(Integer codigo, Integer nivelMontarDados) throws Exception{
		String sql = "SELECT * FROM recibodevolucao WHERE pessoa = "+codigo+" order by datadevolucao desc";
		ResultSet resultSet = ReciboDevolucao.criarConsulta(sql, con);
                return montarDadosConsulta(resultSet, nivelMontarDados, con);
	}

        private static List<ReciboDevolucaoVO> montarDadosConsulta(ResultSet rs, Integer nivelMontarDados, Connection con) throws Exception{
            List<ReciboDevolucaoVO> lista = new ArrayList<ReciboDevolucaoVO>();
            while(rs.next()){
                lista.add(montarDados(rs, con, nivelMontarDados));
            }
            return lista;
        }


	private static ReciboDevolucaoVO montarDados(ResultSet rs, Connection con, Integer nivelMontarDados) throws SQLException, Exception {
		ReciboDevolucaoVO recibo = new ReciboDevolucaoVO();
		recibo.setCodigo(rs.getInt("codigo"));
		//data
		recibo.setDataDevolucao(rs.getTimestamp("datadevolucao"));
		//valor
		recibo.setValorDevolucao(rs.getDouble("valordevolucao"));
		//pago
		recibo.setValorTotalPagoPeloCliente(rs.getDouble("valortotalpagopelocliente"));
		//utilizado
		recibo.setValorUtilizadoPeloCliente(rs.getDouble("valorutilizadopelocliente"));
		//somaprod
		recibo.setValorTotalSomaProdutoContratos(rs.getDouble("valortotalsomaprodutocontratos"));
		//taxa
		recibo.setValorTaxaCancelamento(rs.getDouble("valortaxacancelamento"));
		//multa
		recibo.setValorMultaCancelamento(rs.getDouble("valormultacancelamento"));
		recibo.setQuitacao(rs.getBoolean("quitacao"));
		recibo.setQuitacaoManual(rs.getBoolean("quitacaomanual"));
		recibo.setLiberacao(rs.getBoolean("liberacao"));
		recibo.setValorDevolvidoEmDinheiro(rs.getDouble("valordevolvidoemdinheiro"));
		recibo.setValorContrato(rs.getDouble("valorcontrato"));
		recibo.setValorBaseContrato(rs.getDouble("valorbasecontrato"));
		recibo.setLiberacaoDevolucao(rs.getBoolean("liberacaodevolucao"));
		recibo.setDevolucaoManual(rs.getBoolean("devolucaomanual"));
		recibo.setValorRealDevolucao(rs.getDouble("valorrealdevolucao"));
		recibo.setPessoa(rs.getInt("pessoa"));
		recibo.setValorEmContaCorrente(rs.getDouble("valorcontacorrente"));
		recibo.setValorOriginal(rs.getDouble("valororiginal"));
		recibo.setProdDevolucao(rs.getInt("proddevolucao"));
		recibo.setProdRecebiveis(rs.getInt("prodrecebiveis"));
                recibo.setReciboEditado((rs.getInt("reciboeditado")));
		if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
			if (rs.getInt("contrato") > 0) {
				Contrato contrato = new Contrato(con);
				recibo.setContrato(contrato.consultarPorChavePrimaria(rs.getInt("contrato"), Uteis.NIVELMONTARDADOS_TODOS));
				contrato = null;
				MovProdutoVO produtoVO = new MovProdutoVO();
				recibo.setProdutoVO(produtoVO);
			} else {
				try {
					MovProduto produto = new MovProduto(con);
					recibo.setProdutoVO(produto.consultarPorChavePrimaria(rs.getInt("movproduto"), Uteis.NIVELMONTARDADOS_TODOS));
					ContratoVO contratoVO = new ContratoVO();
					contratoVO.setCodigo(0);
					recibo.setContrato(contratoVO);
				} catch (Exception e) {
					recibo.setProdutoVO(new MovProdutoVO());
					ContratoVO contratoVO = new ContratoVO();
					contratoVO.setCodigo(0);
					recibo.setContrato(contratoVO);
					Pessoa pessoaDao = new Pessoa(con);
					PessoaVO pessoa = pessoaDao.consultarPorChavePrimaria(recibo.getPessoa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
					recibo.getProdutoVO().setPessoa(pessoa);
				}

			}
			Usuario usuario = new Usuario(con);
			recibo.setResponsavelDevolucao(usuario.consultarPorChavePrimaria(rs.getInt("usuario"), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
			usuario = null;
			montarRecebiveis(recibo, con);
                        montarParcelas(recibo, con);
		}

		return recibo;
	}
	
	private static void montarRecebiveis(ReciboDevolucaoVO recibo, Connection con) throws SQLException, Exception{
		String sql = "SELECT * FROM recebiveisdevolucao WHERE recibodevolucao = "+recibo.getCodigo();
		ResultSet resultSet = criarConsulta(sql, con);
		CartaoCredito cartao = new CartaoCredito(con);
		Cheque chequeDAO = new Cheque(con);
		while(resultSet.next()){
			Integer cheque = resultSet.getInt("cheque");
			Integer cartaoCredito = resultSet.getInt("cartaocredito");
			if(!UteisValidacao.emptyNumber(cartaoCredito)){
				recibo.getCartoesDevolvidos().add(cartao.consultarPorChavePrimaria(cartaoCredito, Uteis.NIVELMONTARDADOS_TODOS));
			}
			if(!UteisValidacao.emptyNumber(cheque)){
				recibo.getChequesDevolvidos().add(chequeDAO.consultarPorChavePrimaria(cheque, Uteis.NIVELMONTARDADOS_TODOS));
			}
		}
		cartao = null;
		chequeDAO = null;
	}

	public ArrayList<ReciboDevolucaoVO> consultarCaixaPorOperador(UsuarioVO usuario, Date inicio, Date fim, String horaInicio, String horaFim, int empresa) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT rd.*, u.nome AS responsavel, p.nome AS pagador from recibodevolucao rd, usuario u, pessoa p, contrato c ");
		sql.append(" WHERE rd.usuario = u.codigo ");
		sql.append(" AND c.codigo = rd.contrato ");
		sql.append(" AND p.codigo = c.pessoa ");
		sql.append(" AND datadevolucao >= '" + Uteis.getDataJDBC(inicio) + " " + horaInicio + "' ");
		sql.append(" AND datadevolucao <= '" + Uteis.getDataJDBC(fim) + " " + horaFim + "'  ");
		sql.append((usuario.getCodigo() > 0 ? " AND usuario = " + usuario.getCodigo() : ""));
		if (!UteisValidacao.emptyNumber(empresa)) {
			sql.append(" AND c.empresa = " + empresa);
		}


		ResultSet resultSetContratos = ReciboDevolucao.criarConsulta(sql.toString(), con);

		sql = new StringBuilder();
		sql.append(" SELECT rd.*, u.nome AS responsavel, p.nome AS pagador from recibodevolucao rd, usuario u, pessoa p, movproduto mp ");
		sql.append(" WHERE rd.usuario = u.codigo ");
		sql.append(" AND mp.codigo = rd.movproduto ");
		sql.append(" AND p.codigo = mp.pessoa ");
		sql.append(" AND datadevolucao >= '" + Uteis.getDataJDBC(inicio) + " " + horaInicio + "' ");
		sql.append(" AND datadevolucao <= '" + Uteis.getDataJDBC(fim) + " " + horaFim + "'  ");
		sql.append((usuario.getCodigo() > 0 ? " AND usuario = " + usuario.getCodigo() : ""));
		if (!UteisValidacao.emptyNumber(empresa)) {
			sql.append(" AND mp.empresa = " + empresa);
		}
		ResultSet resultSetProdutos = ReciboDevolucao.criarConsulta(sql.toString(), con);

		StringBuilder sqlPessoa = new StringBuilder();
		sqlPessoa.append(" SELECT rd.*, u.nome AS responsavel, p.nome AS pagador from recibodevolucao rd, usuario u, pessoa p, cliente c ");
		sqlPessoa.append(" WHERE rd.usuario = u.codigo ");
		sqlPessoa.append(" AND rd.contrato is null and rd.movproduto is null ");
		sqlPessoa.append(" AND p.codigo = rd.pessoa ");
		sqlPessoa.append(" AND c.pessoa = rd.pessoa ");
		sqlPessoa.append(" AND datadevolucao >= '").append(Uteis.getDataJDBC(inicio)).append(" ").append(horaInicio).append("' ");
		sqlPessoa.append(" AND datadevolucao <= '").append(Uteis.getDataJDBC(fim)).append(" ").append(horaFim).append("'  ");
		sqlPessoa.append((usuario.getCodigo() > 0 ? " AND usuario = " + usuario.getCodigo() : ""));
		if (!UteisValidacao.emptyNumber(empresa)) {
			sqlPessoa.append(" AND c.empresa = ").append(empresa);
		}

		ResultSet resultSetPessoas = ReciboDevolucao.criarConsulta(sqlPessoa.toString(), con);

		return montarDadosCaixaPorOperador(resultSetContratos, resultSetProdutos, resultSetPessoas);
	}
	
	public ArrayList<ReciboDevolucaoVO> montarDadosCaixaPorOperador(ResultSet rsContratos, ResultSet rsProdutos, ResultSet rsPessoas) throws Exception{
		ArrayList<ReciboDevolucaoVO> lista = new ArrayList<ReciboDevolucaoVO>();
		while(rsContratos.next()){
			lista.add(montarDados(rsContratos, con, Uteis.NIVELMONTARDADOS_TODOS));
		}
		while(rsProdutos.next()){
			lista.add(montarDados(rsProdutos, con, Uteis.NIVELMONTARDADOS_TODOS));
		}
                while(rsPessoas.next()){
			lista.add(montarDados(rsPessoas, con, Uteis.NIVELMONTARDADOS_TODOS));
		}
		return lista;
	}
	
	private void incluirRecebiveis(ReciboDevolucaoVO recibo) throws Exception{
		String sql = "INSERT INTO recebiveisdevolucao(recibodevolucao, cheque, cartaocredito) VALUES (%s, %s, %s);";
		StringBuilder sqlInserir = new StringBuilder();
		for(CartaoCreditoVO cartao : recibo.getCartoesDevolvidos()){
			sqlInserir.append(String.format(sql, new Object[]{recibo.getCodigo(), "null", cartao.getCodigo()}));
		}
		for(ChequeVO cheque : recibo.getChequesDevolvidos()){
			sqlInserir.append(String.format(sql, new Object[]{recibo.getCodigo(), cheque.getCodigo(), "null"}));
		}
		executarConsulta(sqlInserir.toString(), con);
	}
        
        private void incluirParcelasEdicao(ReciboDevolucaoVO recibo) throws Exception{
		String sql = "INSERT INTO recibodevolucaomovparcela(recibodevolucao, movparcela) VALUES (%s, %s);";
		StringBuilder sqlInserir = new StringBuilder();
		for(MovParcelaVO  parcela : recibo.getParcelasEdicaoPagamento()){
			sqlInserir.append(String.format(sql, new Object[]{recibo.getCodigo(), parcela.getCodigo()}));
		}
		executarConsulta(sqlInserir.toString(), con);
	}

    private void incluirMovPagamentoReciboDevolucao(Integer codigoRecibo, List<Integer> listaPagamentos) throws Exception {
        String sql = "INSERT INTO recibodevolucaomovpagamento(recibodevolucao, movpagamento) VALUES (%s, %s);";
        StringBuilder sqlInserir = new StringBuilder();
        for (Integer pagamento : listaPagamentos) {
            sqlInserir.append(String.format(sql, new Object[]{codigoRecibo, pagamento}));
        }
        executarConsulta(sqlInserir.toString(), con);
    }
    
    public List<ReciboDevolucaoVO> consultarPorMovPagamento(Integer movPamento, Integer nivelMontarDados) throws Exception{
        String sql = "SELECT rd.* FROM  recibodevolucao rd inner join recibodevolucaomovpagamento rmp on rd.codigo = rmp.recibodevolucao  WHERE rmp.movpagamento = "+movPamento+" order by datadevolucao desc";
        ResultSet resultSet = ReciboDevolucao.criarConsulta(sql, con);
        return montarDadosConsulta(resultSet, nivelMontarDados, con);
    }
    
    public void excluirSemCommit(ReciboDevolucaoVO obj) throws Exception{
        ReciboDevolucao.executarConsulta("DELETE FROM recibodevolucao WHERE codigo =  " +obj.getCodigo()+";", con);
    }
    
    public void estornarRecibosDevolucao(boolean usarMovimentacaoContas, List<EstornoReciboVO> listaEstornoRecibo) throws Exception{
       List<ReciboDevolucaoVO> recibosDevolucao = new ArrayList();
       List<ReciboDevolucaoVO> listaReciboPagamento;
       List<MovPagamentoVO> pagamentosDependentes;
       for (EstornoReciboVO estorno : listaEstornoRecibo) {
            for (MovPagamentoVO mp : estorno.getListaMovPagamento()) {
                listaReciboPagamento = consultarPorMovPagamento(mp.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if(!UteisValidacao.emptyList(listaReciboPagamento) && mp.getValor() < mp.getValorTotal()){
                    pagamentosDependentes = getFacade().getMovPagamento().consultarCreditosDependentes(mp.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    for(MovPagamentoVO pag : pagamentosDependentes){
                        if(UteisValidacao.emptyNumber(pag.getReciboPagamento().getCodigo()) && existeReciboDevolucaoMovPagamento(pag.getCodigo())){
                            getFacade().getMovPagamento().excluirSemCommit(pag, true);
                        }
                    }
                }
                recibosDevolucao.addAll(listaReciboPagamento);
            }
        }
       MovContaVO contaDevolucao;
       MovProdutoVO prodDevolucao = new MovProdutoVO();
       List<Integer> produtosExcluidos= new ArrayList<Integer>();
       for(ReciboDevolucaoVO recibo : recibosDevolucao){
           if(!UteisValidacao.emptyNumber(recibo.getProdDevolucao()) && !produtosExcluidos.contains(recibo.getProdDevolucao())){
               contaDevolucao = getFacade().getFinanceiro().getMovConta().consultarPorMovProdutoDevolucao(recibo.getProdDevolucao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
               if(contaDevolucao != null && contaDevolucao.getDataQuitacao() != null && usarMovimentacaoContas){
                   throw new Exception("Lançamento não pode ser estornado por estar vinculado a uma conta paga no Financeiro. Acesse o financeiro e estorne o pagamento da "
                           + "conta: "+contaDevolucao.getDescricao()+". Essa conta foi lançada no dia "+contaDevolucao.getDataLancamento_Apresentar()+" e quitada no dia " +contaDevolucao.getDataQuitacao_Apresentar());
               } else if (contaDevolucao != null) {
                   getFacade().getFinanceiro().getMovConta().excluirSemCommit(contaDevolucao, true, true);
               }
               prodDevolucao.setCodigo(recibo.getProdDevolucao());
               getFacade().getMovProduto().excluirSemCommit(prodDevolucao);
               produtosExcluidos.add(recibo.getProdDevolucao());
           }
           if(!UteisValidacao.emptyNumber(recibo.getProdRecebiveis())){
               prodDevolucao.setCodigo(recibo.getProdRecebiveis());
               getFacade().getMovProduto().excluirSemCommit(prodDevolucao);
           }
           excluirSemCommit(recibo);
       }
    }

    private boolean existeReciboDevolucaoMovPagamento(Integer codigoMovpagamento) throws Exception {
        String sql = "SELECT codigo FROM  recibodevolucaomovpagamento  WHERE movpagamento = "+codigoMovpagamento+";";
        ResultSet resultSet = ReciboDevolucao.criarConsulta(sql, con);
        if(resultSet.next()){
            return true;
        }
        return false;
    }
    
    private static void montarParcelas(ReciboDevolucaoVO recibo, Connection con) throws Exception {
        String sql = "SELECT * FROM recibodevolucaomovparcela WHERE recibodevolucao = "+recibo.getCodigo();
        recibo.setParcelasEdicaoPagamento(new ArrayList<MovParcelaVO>());
        ResultSet resultSet = criarConsulta(sql, con);
        MovParcela parcelaDAO = new MovParcela(con);
        while(resultSet.next()){
                Integer parcela = resultSet.getInt("movparcela");
                try{ 
                    MovParcelaVO parcelaVO = parcelaDAO.consultarPorChavePrimaria(parcela, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    recibo.getParcelasEdicaoPagamento().add(parcelaVO);
                } catch(Exception ingnored){
                }
        }
        parcelaDAO = null;
    }

}
