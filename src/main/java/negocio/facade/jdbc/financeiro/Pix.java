package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.integracao.pactopay.StatusPactoPayEnum;
import br.com.pactosolucoes.integracao.pactopay.TipoConsultaPactoPayEnum;
import br.com.pactosolucoes.integracao.pactopay.front.FiltroPactoPayDTO;
import br.com.pactosolucoes.integracao.pactopay.front.ParcelaDTO;
import br.com.pactosolucoes.integracao.pactopay.front.transacao.TotalizadorDTO;
import br.com.pactosolucoes.integracao.pactopay.front.transacao.TotalizadorTipoDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.base.Joiner;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.Telefone;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.interfaces.financeiro.PixInterfaceFacade;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;
import org.json.JSONObject;
import servicos.impl.dcc.base.RemessaService;
import servicos.pix.*;
import servicos.propriedades.PropsService;

import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.stream.Collectors;

public class Pix extends SuperEntidade implements PixInterfaceFacade {

    public Pix() throws Exception {
        super();
    }

    public Pix(Connection connection) throws Exception {
        super(connection);
    }

    public PixVO incluir(PixRequisicaoDto pixRequisicaoDto, ConvenioCobrancaVO convenioCobrancaVO, Integer pessoa,
                         Integer empresa, List<PixMovParcelaVO> pixMovParcelasVOS, Integer formapagamento,
                         UsuarioVO usuarioResponsavel, OrigemCobrancaEnum origemCobrancaEnum, Double desconto) throws Exception {

        preencherCredenciaisPjBank(convenioCobrancaVO);
        PixVO pixVO = incluir(pixRequisicaoDto, convenioCobrancaVO, pessoa, empresa, formapagamento, usuarioResponsavel, origemCobrancaEnum, desconto);
        PixMovParcela pixMovParcela = new PixMovParcela(this.con);
        pixMovParcela.incluir(pixVO, pixMovParcelasVOS);
        pixVO.setPixMovParcelas(pixMovParcelasVOS);
        return pixVO;
    }

    private PixVO incluir(PixRequisicaoDto pixRequisicaoDto, ConvenioCobrancaVO convenioCobrancaVO, Integer pessoa,
                          Integer empresa, Integer formapagamento, UsuarioVO usuarioResponsavel,
                          OrigemCobrancaEnum origemCobrancaEnum, Double desconto) throws Exception {
        PixVO pixVO = new PixVO();
        PixDto pixDto = pixRequisicaoDto.getPixDto();
        pixVO.setTxid(pixDto.getTxid());
        pixVO.setConveniocobranca(convenioCobrancaVO);
        pixVO.setData(Calendario.hoje());
        pixVO.setValor(Double.parseDouble(pixDto.getValor().getOriginal()));
        pixVO.setEmpresa(empresa);
        pixVO.setStatus(pixDto.getStatus());
        pixVO.setTextoImagemQrcode(pixDto.getTextoImagemQRcode());
        if (convenioCobrancaVO.isPixBB()) {
            preencherPessoa(pessoa, pixVO);
        } else {
            pixVO.setDevedorNome(pixDto.getDevedor().getNome());
        }
        pixVO.setDevedorCpf(pixDto.getDevedor().getCpf());
        pixVO.setDevedorCnpj(pixDto.getDevedor().getCnpj());
        pixVO.setPessoa(pessoa);
        pixVO.setRecebedorChave(pixDto.getChave());
        pixVO.setExpiracao(pixDto.getCalendario().getExpiracao());
        pixVO.setClientId(convenioCobrancaVO.getPixClientId());
        pixVO.setClientSecret(convenioCobrancaVO.getPixClientSecret());
        pixVO.setAppKey(convenioCobrancaVO.getPixAppKey());
        pixVO.setBasicAuth(convenioCobrancaVO.getPixBasicAuth());
        pixVO.setAmbiente(convenioCobrancaVO.getAmbiente().toString());
        pixVO.setFormapagamento(formapagamento);
        pixVO.setParamsEnvio(pixRequisicaoDto.getEnvio());
        pixVO.setParamsResposta(pixRequisicaoDto.getResposta());
        pixVO.setUsuarioResponsavel(usuarioResponsavel);
        pixVO.setOrigem(origemCobrancaEnum);
        pixVO.setDesconto(desconto);
        pixVO.setPedidoNumero(pixDto.getPedidoNumero());
        pixVO.setTransactionReceiptUrl(pixDto.getTransactionReceiptUrl());

        incluir(pixVO);
        pixVO.setCodigo(obterValorChavePrimariaCodigo());

        //gerar log
        gerarLogInclusao(pixVO);

        //crédito pacto
        debitarCreditosPacto(pixVO);
        return pixVO;
    }

    private void preencherPessoa(Integer pessoa, PixVO pixVO) throws Exception {
        Pessoa pessoaDAO = new Pessoa(this.con);
        PessoaVO pessoaVO = new PessoaVO();
        try {
            pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoa, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            pixVO.setDevedorNome(pessoaVO.getNome());
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pessoaDAO = null;
        }
    }

    private void gerarLogInclusao(PixVO pixVO) {
        PixLog pixLogDAO;
        try {
            pixLogDAO = new PixLog(this.con);
            PixLogVO pixLogVO = new PixLogVO(pixVO, pixVO.getUsuarioResponsavel(), PixOperacaoLogEnum.CRIACAO);
            JSONObject jsonLog = new JSONObject();
            jsonLog.put("operacao", "PIX - CRIADO");
            jsonLog.put("codigo", pixVO.getCodigo());
            jsonLog.put("valor", pixVO.getValor());
            jsonLog.put("usuario", pixVO.getUsuarioResponsavel().getNome());
            pixLogVO.setLog(jsonLog.toString());
            pixLogDAO.incluir(pixLogVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pixLogDAO = null;
        }
    }

    private void gerarLogCancelar(PixVO pixVO) {
        PixLog pixLogDAO;
        try {
            pixLogDAO = new PixLog(this.con);
            PixLogVO pixLogVO = new PixLogVO(pixVO, pixVO.getUsuarioResponsavel(), PixOperacaoLogEnum.CANCELAR);
            JSONObject jsonLog = new JSONObject();
            jsonLog.put("codigo", pixVO.getCodigo());
            jsonLog.put("operacao", "PIX - CANCELAR");
            jsonLog.put("usuario", pixVO.getUsuarioResponsavel().getNome());
            pixLogVO.setLog(jsonLog.toString());
            pixLogDAO.incluir(pixLogVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pixLogDAO = null;
        }
    }

    public void incluir(PixVO pixVO) throws SQLException {
        String sql = "INSERT INTO public.pix (" +
                "txid, " +
                "conveniocobranca, " +
                "data, " +
                "valor, " +
                "empresa, " +
                "status, " +
                "texto_imagem_qrcode, " +
                "devedor_cpf, " +
                "pessoa, " +
                "devedor_nome, " +
                "recebedor_chave, " +
                "expiracao," +
                "client_id," +
                "client_secret," +
                "app_key," +
                "basic_auth," +
                "ambiente," +
                "formapagamento," +
                "paramsEnvio, paramsresposta, usuarioresponsavel, origem, desconto, pedidoNumero, devedor_cnpj)" +
                "VALUES " +
                "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";

        PreparedStatement statement = con.prepareStatement(sql);
        int i = 1;
        statement.setString(i++, pixVO.getTxid());
        statement.setInt(i++, pixVO.getConveniocobranca().getCodigo());
        statement.setTimestamp(i++, Uteis.getDataJDBCTimestamp(pixVO.getData()));
        statement.setDouble(i++, pixVO.getValor());
        statement.setInt(i++, pixVO.getEmpresa());
        statement.setString(i++, pixVO.getStatus());
        statement.setString(i++, pixVO.getTextoImagemQrcode());
        statement.setString(i++, pixVO.getDevedorCpf());
        statement.setInt(i++, pixVO.getPessoa());
        statement.setString(i++, pixVO.getDevedorNome());
        statement.setString(i++, pixVO.getRecebedorChave());
        statement.setLong(i++, pixVO.getExpiracao());
        statement.setString(i++, pixVO.getClientId());
        statement.setString(i++, pixVO.getClientSecret());
        statement.setString(i++, pixVO.getAppKey());
        statement.setString(i++, pixVO.getBasicAuth());
        statement.setString(i++, pixVO.getAmbiente());
        statement.setInt(i++, pixVO.getFormapagamento());
        statement.setString(i++, pixVO.getParamsEnvio());
        statement.setString(i++, pixVO.getParamsResposta());

        if (UteisValidacao.emptyNumber(pixVO.getUsuarioResponsavel().getCodigo())) {
            statement.setNull(i++, Types.NULL);
        } else {
            statement.setInt(i++, pixVO.getUsuarioResponsavel().getCodigo());
        }
        statement.setInt(i++, pixVO.getOrigem().getCodigo());
        statement.setDouble(i++, pixVO.getDesconto());
        statement.setString(i++, pixVO.getPedidoNumero());
        statement.setString(i++, pixVO.getDevedorCnpj());
        statement.execute();
    }

    public PixVO consultarPorTxId(String txId) throws Exception {
        String sql = "SELECT * from pix WHERE pix.txid = '" + txId + "'";
        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        if (!resultSet.next()) {
            throw new EntityNotFoundException("Pix com txid " + txId + " não existe");
        }
        return montarDados(resultSet, true);
    }

    private List<PixMovParcelaVO> montarPixMovParcelas(PixVO pixVO) throws Exception {
        String sql = "SELECT " +
                "pixmovparcela.movparcela pixmovparcela_movparcela, " +
                "pixmovparcela.pix pixmovparcela_pix, " +
                "pixmovparcela.codigo pixmovparcela_codigo, pixmovparcela.valormulta, pixmovparcela.valorjuros,  " +
                "movparcela.* FROM pixmovparcela " +
                "INNER JOIN movparcela on movparcela.codigo = pixmovparcela.movparcela " +
                "WHERE pixmovparcela.pix = " + pixVO.getCodigo();

        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        List<PixMovParcelaVO> pixMovParcelaVOS = new ArrayList<>();
        while (resultSet.next()) {
            PixMovParcelaVO pixMovParcelaVO = new PixMovParcelaVO();
            pixMovParcelaVO.setCodigo(resultSet.getInt("pixmovparcela_codigo"));
            pixMovParcelaVO.setPix(resultSet.getInt("pixmovparcela_pix"));
            pixMovParcelaVO.setMovparcela(resultSet.getInt("pixmovparcela_movparcela"));
            MovParcelaVO movParcelaVO = MovParcela.montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            pixMovParcelaVO.setMovParcelaVO(movParcelaVO);
            pixMovParcelaVO.setValorMulta(resultSet.getDouble("valormulta"));
            pixMovParcelaVO.setValorJuros(resultSet.getDouble("valorjuros"));
            pixMovParcelaVOS.add(pixMovParcelaVO);
        }
        return pixMovParcelaVOS;
    }

    private List<PixMovParcelaVO> montarPixMovParcelasTelaCliente(PixVO pixVO) throws Exception {
        String sql = "select \n" +
                "pm.codigo, \n" +
                "pm.pix, \n" +
                "pm.movparcela, \n" +
                "cl.codigo as cliente, \n" +
                "cl.matricula, \n" +
                "p.nome, \n" +
                "mp.pessoa as pessoaparcela, \n" +
                "mp.codigo as codparcela, \n" +
                "mp.descricao as parcela, \n" +
                "mp.datavencimento \n" +
                "from pixmovparcela pm \n" +
                "inner join movparcela mp on mp.codigo = pm.movparcela \n" +
                "inner join pessoa p on p.codigo = mp.pessoa \n" +
                "left join cliente cl on cl.pessoa = p.codigo \n" +
                "where pm.pix = " + pixVO.getCodigo();

        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet rs = statement.executeQuery();
        List<PixMovParcelaVO> pixMovParcelaVOS = new ArrayList<>();
        while (rs.next()) {
            PixMovParcelaVO pixMovParcelaVO = new PixMovParcelaVO();
            pixMovParcelaVO.setCodigo(rs.getInt("codigo"));
            pixMovParcelaVO.setPix(rs.getInt("pix"));
            pixMovParcelaVO.setMovparcela(rs.getInt("movparcela"));

            pixMovParcelaVO.setMovParcelaVO(new MovParcelaVO());
            pixMovParcelaVO.getMovParcelaVO().setCodigo(rs.getInt("codparcela"));
            pixMovParcelaVO.getMovParcelaVO().setDescricao(rs.getString("parcela"));
            pixMovParcelaVO.getMovParcelaVO().setDataVencimento(rs.getDate("datavencimento"));

            pixMovParcelaVO.getMovParcelaVO().getPessoa().setCodigo(rs.getInt("pessoaparcela"));
            pixMovParcelaVO.getMovParcelaVO().getPessoa().setNome(rs.getString("nome"));

            pixMovParcelaVO.getClienteVO().setCodigo(rs.getInt("cliente"));
            pixMovParcelaVO.getClienteVO().setMatricula(rs.getString("matricula"));
            pixMovParcelaVO.getClienteVO().getPessoa().setCodigo(rs.getInt("pessoaparcela"));
            pixMovParcelaVO.getClienteVO().getPessoa().setNome(rs.getString("nome"));
            pixMovParcelaVOS.add(pixMovParcelaVO);
        }
        return pixMovParcelaVOS;
    }

    private PixVO montarDados(ResultSet resultSet, boolean montarPixMovParcelas) throws Exception {
        return montarDados(resultSet, montarPixMovParcelas, false);
    }

    public List<PixVO> montarDadosConsulta(ResultSet rs, boolean montarPixMovParcelas, boolean telaCliente) throws Exception {
        List<PixVO> vetResultado = new ArrayList<>();
        while (rs.next()) {
            vetResultado.add(montarDados(rs, montarPixMovParcelas, telaCliente));
        }
        return vetResultado;
    }

    private PixVO montarDados(ResultSet resultSet, boolean montarPixMovParcelas, boolean telaCliente) throws Exception {
        PixVO pixVO = new PixVO();
        pixVO.setCodigo(resultSet.getInt("codigo"));
        pixVO.setTxid(resultSet.getString("txid"));
        pixVO.getConveniocobranca().setCodigo(resultSet.getInt("conveniocobranca"));
        pixVO.setData(resultSet.getTimestamp("data"));
        pixVO.setValor(resultSet.getDouble("valor"));
        pixVO.setEmpresa(resultSet.getInt("empresa"));
        pixVO.setStatus(resultSet.getString("status"));
        pixVO.setTextoImagemQrcode(resultSet.getString("texto_imagem_qrcode"));
        pixVO.setDevedorCpf(resultSet.getString("devedor_cpf"));
        try {
            pixVO.setDevedorCnpj(resultSet.getString("devedor_cnpj"));
        } catch (Exception ex) {}
        pixVO.setPessoa(resultSet.getInt("pessoa"));
        pixVO.getPessoaVO().setCodigo(resultSet.getInt("pessoa"));
        pixVO.setDevedorNome(resultSet.getString("devedor_nome"));
        pixVO.setRecebedorChave(resultSet.getString("recebedor_chave"));
        pixVO.setExpiracao(resultSet.getInt("expiracao"));
        pixVO.setReciboPagamento(resultSet.getInt("recibopagamento"));
        pixVO.setClientId(resultSet.getString("client_id"));
        pixVO.setClientSecret(resultSet.getString("client_secret"));
        pixVO.setAppKey(resultSet.getString("app_key"));
        pixVO.setBasicAuth(resultSet.getString("basic_auth"));
        pixVO.setAmbiente(resultSet.getString("ambiente"));
        pixVO.setParamsEnvio(resultSet.getString("paramsenvio"));
        pixVO.setParamsResposta(resultSet.getString("paramsresposta"));
        pixVO.getUsuarioResponsavel().setCodigo(resultSet.getInt("usuarioresponsavel"));
        pixVO.setDataPagamento(resultSet.getTimestamp("datapagamento"));
        pixVO.setOrigem(OrigemCobrancaEnum.obterPorCodigo(resultSet.getInt("origem")));
        pixVO.setDesconto(resultSet.getDouble("desconto"));

        try {
            pixVO.setReciboEstornado(resultSet.getBoolean("reciboEstornado"));
        } catch (Exception ignored) {
        }

        try {
            pixVO.getPessoaVO().setNome(resultSet.getString("nome_pessoa"));
        } catch (Exception ignored) {
        }
        try {
            pixVO.getUsuarioResponsavel().setNome(resultSet.getString("nome_usuario"));
        } catch (Exception ignored) {
        }

        try {
            pixVO.setQtdMovParcelas(resultSet.getInt("qtd_movparcelas"));
        } catch (Exception ignored) {
        }

        try {
            pixVO.setPedidoNumero(resultSet.getString("pedidoNumero"));
        } catch (Exception ignored) {
        }

        try {
            pixVO.setTransactionReceiptUrl(resultSet.getString("transactionReceiptUrl"));
        } catch (Exception ignored) {
        }

        //montar dados do convênio de cobranca
        montarConvenioCobranca(pixVO);

        if (montarPixMovParcelas) {
            pixVO.setPixMovParcelas(montarPixMovParcelas(pixVO));
        }

        if (telaCliente) {
            pixVO.setPixMovParcelas(montarPixMovParcelasTelaCliente(pixVO));
        }

        try {
            pixVO.setPagoOrigemWebhook(resultSet.getBoolean("pagoOrigemWebhook"));
        } catch (Exception ignored) {
        }
        return pixVO;
    }

    private void montarConvenioCobranca(PixVO pixVO) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);

            ConvenioCobrancaVO convenioCobrancaVO = null;
            if (!UteisValidacao.emptyNumber(pixVO.getConveniocobranca().getCodigo())) {
                convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(pixVO.getConveniocobranca().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            pixVO.setConveniocobranca(convenioCobrancaVO);
        } finally {
            convenioCobrancaDAO = null;
        }
    }

    public void alterarStatus(String txId, String status) throws SQLException {
        String sql = "UPDATE pix SET status = '" + status + "' WHERE txid='" + txId + "'";
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    public void alterarStatus(PixVO pixVO) throws SQLException {
        String sql = "UPDATE pix SET status = '" + pixVO.getStatus() + "' WHERE codigo=" + pixVO.getCodigo() + "";
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    public void alterarStatusAjusteManual(PixVO pixVO, PixStatusEnum pixStatusEnum) throws Exception {
        String sql = "UPDATE pix SET status = '" + pixStatusEnum + "'" + " WHERE codigo=" + pixVO.getCodigo() + "";
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    public void alterarTransactionReceiptUrl(PixVO pixVO) throws SQLException {
        String sql = "UPDATE pix SET transactionReceiptUrl = '" + pixVO.getTransactionReceiptUrl() + "' WHERE codigo=" + pixVO.getCodigo() + "";
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }


    @Override
    public PixVO consultarPorCodigo(Integer codigo) throws Exception {
        return consultarPorCodigo(codigo, false);
    }

    @Override
    public PixVO consultarPorCodigo(Integer codigo, boolean montarParcelas) throws Exception {
        String sql = "SELECT * from pix WHERE codigo = " + codigo;
        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        if (!resultSet.next()) {
            throw new EntityNotFoundException("Pix com codigo " + codigo + " não existe");
        }
        return montarDados(resultSet, montarParcelas);
    }

    @Override
    public int consultarCodigoReciboPorCodigoPix(Integer codigo) throws Exception {
        String sql = "SELECT recibopagamento from pix WHERE codigo = " + codigo;
        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        if (resultSet.next()) {
            return resultSet.getInt("recibopagamento");
        }
        return 0;
    }

    @Override
    public boolean isPixPago(Integer codigo) throws Exception {
        String sql = "SELECT status from pix WHERE codigo = " + codigo;
        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet rs = statement.executeQuery();
        if (!rs.next()) {
            throw new EntityNotFoundException("Pix com codigo " + codigo + " não existe");
        }

            String status = rs.getString("status");
            if (!UteisValidacao.emptyString(status) && status.equals(PixStatusEnum.CONCLUIDA.getDescricao())) {
                return true;
            }

        return false;
    }


    @Override
    public Integer quantidadePorPessoa(Integer pessoa) throws SQLException {
        String sql = getTelaClienteSQL(true, pessoa, 0, 0);
        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet rs = statement.executeQuery();
        if (rs.next()) {
            return rs.getInt("qtd");
        } else {
            return 0;
        }
    }

    private String getTelaClienteSQL(boolean count, Integer pessoa, int limit, int offset) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        if (count) {
            sql.append("COUNT(DISTINCT(p.codigo)) as qtd \n");
        } else {
            sql.append("DISTINCT ON (p.data, p.codigo) p.*, \n");
            sql.append("exists (select codigo from pixlog where pix = p.codigo and operacao = ").append(PixOperacaoLogEnum.ESTORNO_RECIBO.getCodigo()).append(") as reciboEstornado \n");
        }
        sql.append("FROM pix p \n");
        sql.append("WHERE p.codigo in ( \n");
        sql.append("SELECT \n");
        sql.append("pi.codigo \n");
        sql.append("FROM pix pi \n");
        sql.append("INNER JOIN pixmovparcela pm ON pi.codigo = pm.pix \n");
        sql.append("INNER JOIN movparcela mp ON mp.codigo = pm.movparcela \n");
        sql.append("WHERE mp.pessoa = ").append(pessoa).append(" \n");
        sql.append("union \n");
        sql.append("SELECT \n");
        sql.append("pi.codigo \n");
        sql.append("FROM pix pi \n");
        sql.append("WHERE pi.pessoa = ").append(pessoa).append(" \n");
        sql.append(") \n");
        if (!count) {
            sql.append("ORDER BY p.data desc \n");
            sql.append("LIMIT ").append(limit).append(" \n");
            sql.append("OFFSET ").append(offset).append(" \n");
        }
        return sql.toString();
    }

    @Override
    public List<PixVO> consultarPorPessoaTelaCliente(Integer pessoa, int limit, int offset) throws Exception {
        String sql = getTelaClienteSQL(false, pessoa, limit, offset);
        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        List<PixVO> pix = new ArrayList<>();
        while (resultSet.next()) {
            pix.add(montarDados(resultSet, false, true));
        }
        return pix;
    }

    @Override
    public void alterarReciboPagamento(PixVO pixVO) throws SQLException {
        String sql = "UPDATE pix SET recibopagamento = '" + pixVO.getReciboPagamento() + "' WHERE codigo=" + pixVO.getCodigo();
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    public void cancelar(PixVO pixVO) throws Exception {
        PixService pixService;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            //preencher o objeto caso não tenha o tipo carregado
            if (pixVO.getConveniocobranca().getTipo().equals(TipoConvenioCobrancaEnum.NENHUM) &&
                    !UteisValidacao.emptyNumber(pixVO.getConveniocobranca().getCodigo())) {
                convenioCobrancaDAO = new ConvenioCobranca(this.con);
                pixVO.setConveniocobranca(convenioCobrancaDAO.consultarPorChavePrimaria(pixVO.getConveniocobranca().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            if (pixVO.getStatusEnum() != null &&
                    !pixVO.getStatusEnum().equals(PixStatusEnum.ATIVA)) {
                //pix já está inválido.
                return;
            }

            if (pixVO.getConveniocobranca().getTipo().equals(TipoConvenioCobrancaEnum.PIX_SANTANDER)) {
                //Santander não tem cancelamento somente excluir a parcela de multa e juros
                excluirMultaJuros(pixVO);
            } else {

                if (pixVO.getAmbienteEnum().equals(AmbienteEnum.PRODUCAO)) {
                    pixService = new PixService(this.con);
                    pixService.cancelar(pixVO);
                    PixRequisicaoDto pixRequisicaoDto = pixService.consultarCobranca(pixVO);
                    pixVO.setStatus(pixRequisicaoDto.getPixDto().getStatus());
                } else {
                    pixVO.setStatus(PixStatusEnum.CANCELADA.toString());
                }
                alterarStatus(pixVO);
                gerarLogCancelar(pixVO);
                verificarExcluirParcelaMultaJurosPix(pixVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
            pixService = null;
        }
    }

    public void cancelarAtivosPorParcelas(List<Integer> codigosParcelas) throws Exception {
        for (PixVO pixVo : consultarAtivosPorParcelas(codigosParcelas)) {
            cancelar(pixVo);
        }
    }

    public void cancelarAtivosPorParcelasVOs(List<MovParcelaVO> parcelaVOS) throws Exception {
        for (PixVO pixVo : consultarAtivosPorParcelasVOs(parcelaVOS)) {
            cancelar(pixVo);
        }
    }

    public List<PixVO> consultarAtivosPorParcelasVOs(List<MovParcelaVO> parcelaVOS) throws Exception {
        List<Integer> codigosParcelas = parcelaVOS.stream().map(movParcelaVO -> movParcelaVO.getCodigo()).collect(Collectors.toList());
        return consultarAtivosPorParcelas(codigosParcelas);
    }

    @Override
    public List<PixVO> consultarAtivosPorParcelas(List<Integer> codigosParcelas) throws Exception {
        List<PixVO> pix = new ArrayList<>();
        if (codigosParcelas == null || codigosParcelas.size() == 0) {
            return pix;
        }
        String sql = "SELECT distinct pix.* FROM pix \n" +
                "INNER JOIN pixmovparcela ON pixmovparcela.pix = pix.codigo \n" +
                "INNER JOIN conveniocobranca conv ON conv.codigo = pix.conveniocobranca \n" +
                "WHERE pixmovparcela.movparcela IN (" + Joiner.on(",").join(codigosParcelas) + ") \n" +
                " AND status = 'ATIVA' \n" +
                "AND (((data + expiracao * interval '1 second') >= NOW())" + " OR ((data + (expiracao::bigint * 86400) * interval '1 second') >= NOW() and conv.tipoconvenio in( " + TipoConvenioCobrancaEnum.PIX_PJBANK.getCodigo() + "," + TipoConvenioCobrancaEnum.PIX_ASAAS.getCodigo() + "))) \n" + //pjbank e asaas grava expirao em dias e no em segundos
                "ORDER BY data;";

        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        while (resultSet.next()) {
            pix.add(montarDados(resultSet, true));
        }
        return pix;
    }


    public List<PixVO> consultarAtivosPorParcelasETipoConvenio(List<Integer> codigosParcelas, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        List<PixVO> pix = new ArrayList<>();
        if (codigosParcelas == null || codigosParcelas.size() == 0) {
            return pix;
        }
        String sql = "SELECT distinct pix.* FROM pix " +
                "INNER JOIN pixmovparcela ON pixmovparcela.pix = pix.codigo " +
                "WHERE pixmovparcela.movparcela IN (" + Joiner.on(",").join(codigosParcelas) + ") " +
                " AND status = 'ATIVA' AND (data + expiracao * interval '1 second') >= NOW()" +
                " AND conveniocobranca = " + convenioCobrancaVO.getCodigo() +
                " ORDER BY data;";

        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        while (resultSet.next()) {
            pix.add(montarDados(resultSet, true));
        }
        return pix;
    }

    @Override
    public void excluirPorParcela(Integer movparcela, UsuarioVO usuarioVO) throws Exception {
        String sql = "select \n" +
                "pm.codigo as pixmovparcela,\n" +
                "pm.pix,\n" +
                "mp.pessoa,\n" +
                "mp.codigo as parcela,\n" +
                "mp.descricao,\n" +
                "mp.datavencimento\n" +
                "from pixmovparcela pm\n" +
                "inner join movparcela mp on mp.codigo = pm.movparcela\n" +
                "where mp.codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, movparcela);
        ResultSet rs = ps.executeQuery();
        List<PixMovParcelaVO> listaPixMovParcela = new ArrayList<>();
        while (rs.next()) {
            PixMovParcelaVO pixMovParcelaVO = new PixMovParcelaVO();
            pixMovParcelaVO.setCodigo(rs.getInt("pixmovparcela"));
            pixMovParcelaVO.setPix(rs.getInt("pix"));

            pixMovParcelaVO.setMovParcelaVO(new MovParcelaVO());
            pixMovParcelaVO.getMovParcelaVO().setPessoa(new PessoaVO());
            pixMovParcelaVO.getMovParcelaVO().getPessoa().setCodigo(rs.getInt("pessoa"));
            pixMovParcelaVO.getMovParcelaVO().setCodigo(rs.getInt("parcela"));
            pixMovParcelaVO.getMovParcelaVO().setDescricao(rs.getString("descricao"));
            pixMovParcelaVO.getMovParcelaVO().setDataVencimento(rs.getDate("datavencimento"));
            listaPixMovParcela.add(pixMovParcelaVO);

            try {
                cancelar(consultarPorCodigo(pixMovParcelaVO.getPix()));
            } catch (Exception e) {
                Uteis.logar(e.toString());
            }
        }

        PixMovParcela pixMovParcelaDAO = new PixMovParcela(con);
        PixLog pixLogDAO = new PixLog(this.con);
        for (PixMovParcelaVO pixMovParcelaVO : listaPixMovParcela) {
            pixMovParcelaDAO.excluirPorCodigo(pixMovParcelaVO.getCodigo());

            JSONObject json = obterJSONBaseLog(usuarioVO);
            json.put("pix", pixMovParcelaVO.getPix());
            json.put("pixmovparcela", pixMovParcelaVO.getCodigo());
            json.put("movparcela_codigo", pixMovParcelaVO.getMovParcelaVO().getCodigo());
            json.put("movparcela_descricao", pixMovParcelaVO.getMovParcelaVO().getDescricao());
            json.put("movparcela_vencimento", pixMovParcelaVO.getMovParcelaVO().getDataVencimento_Apresentar());

            PixVO pixVO = new PixVO();
            pixVO.setCodigo(pixMovParcelaVO.getPix());
            pixVO.setPessoa(pixMovParcelaVO.getMovParcelaVO().getPessoa().getCodigo());
            PixLogVO pixLogVO = new PixLogVO(pixVO, usuarioVO, PixOperacaoLogEnum.EXCLUIR_PARCELA);
            pixLogVO.setLog(json.toString());
            pixLogDAO.incluir(pixLogVO);
        }
        pixMovParcelaDAO = null;
        pixLogDAO = null;
    }

    public void excluirPorContrato(Integer contrato, UsuarioVO usuarioVO) throws Exception {
        String sql = "select \n" +
                "pm.codigo as pixmovparcela,\n" +
                "pm.pix,\n" +
                "mp.codigo as parcela,\n" +
                "mp.pessoa,\n" +
                "mp.descricao,\n" +
                "mp.datavencimento\n" +
                "from pixmovparcela pm\n" +
                "inner join movparcela mp on mp.codigo = pm.movparcela\n" +
                "where mp.contrato = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, contrato);
        ResultSet rs = ps.executeQuery();
        List<PixMovParcelaVO> listaPixMovParcela = new ArrayList<>();
        while (rs.next()) {
            PixMovParcelaVO pixMovParcelaVO = new PixMovParcelaVO();
            pixMovParcelaVO.setCodigo(rs.getInt("pixmovparcela"));
            pixMovParcelaVO.setPix(rs.getInt("pix"));

            try {
                cancelar(consultarPorCodigo(rs.getInt("pix")));
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            }

            pixMovParcelaVO.setMovParcelaVO(new MovParcelaVO());
            pixMovParcelaVO.getMovParcelaVO().setPessoa(new PessoaVO());
            pixMovParcelaVO.getMovParcelaVO().setCodigo(rs.getInt("parcela"));
            pixMovParcelaVO.getMovParcelaVO().setDescricao(rs.getString("descricao"));
            pixMovParcelaVO.getMovParcelaVO().setDataVencimento(rs.getDate("datavencimento"));
            pixMovParcelaVO.getMovParcelaVO().getPessoa().setCodigo(rs.getInt("pessoa"));
            listaPixMovParcela.add(pixMovParcelaVO);
        }

        PixMovParcela pixMovParcelaDAO = new PixMovParcela(con);
        PixLog pixLogDAO = new PixLog(this.con);
        for (PixMovParcelaVO pixMovParcelaVO : listaPixMovParcela) {
            pixMovParcelaDAO.excluirPorCodigo(pixMovParcelaVO.getCodigo());

            JSONObject json = obterJSONBaseLog(usuarioVO);
            json.put("contrato", contrato);
            json.put("pix", pixMovParcelaVO.getPix());
            json.put("pixmovparcela", pixMovParcelaVO.getCodigo());
            json.put("movparcela_codigo", pixMovParcelaVO.getMovParcelaVO().getCodigo());
            json.put("movparcela_descricao", pixMovParcelaVO.getMovParcelaVO().getDescricao());
            json.put("movparcela_vencimento", pixMovParcelaVO.getMovParcelaVO().getDataVencimento_Apresentar());

            PixVO pixVO = new PixVO();
            pixVO.setCodigo(pixMovParcelaVO.getPix());
            if (pixMovParcelaVO.getMovParcelaVO() != null) {
                pixVO.setPessoa(pixMovParcelaVO.getMovParcelaVO().getPessoa().getCodigo());
                pixVO.setPessoaVO(pixMovParcelaVO.getMovParcelaVO().getPessoa());
            }
            PixLogVO pixLogVO = new PixLogVO(pixVO, usuarioVO, PixOperacaoLogEnum.ESTORNO_CONTRATO);
            pixLogVO.setLog(json.toString());
            pixLogDAO.incluir(pixLogVO);
        }
        pixMovParcelaDAO = null;
        pixLogDAO = null;
    }

    @Override
    public List<PixVO> consultarPorPeriodo(Date dataInicial, Date dataFinal) throws Exception {
        String sql = "SELECT pix.* " +
                "FROM pix " +
                "WHERE data::date between '" + Uteis.getDataFormatoBD(dataInicial) + "' and '" + Uteis.getDataFormatoBD(dataFinal) + "' " +
                "ORDER BY data;";

        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        List<PixVO> pixVOS = new ArrayList<>();
        while (resultSet.next()) {
            pixVOS.add(montarDados(resultSet, true));
        }
        return pixVOS;
    }

    public List<PixVO> consultarAtivos() throws Exception {
        String sql = "SELECT (data + expiracao * interval '1 second') expira_em, * FROM pix " +
                "WHERE status = 'ATIVA'" +
                "ORDER BY data;";

        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        List<PixVO> pix = new ArrayList<>();
        while (resultSet.next()) {
            pix.add(montarDados(resultSet, true));
        }
        return pix;
    }

    public void debitarCreditosPacto(PixVO pixVO) throws Exception {
        Empresa empresaDAO = null;
        EmpresaVO empresaVO = null;
        try {
            empresaDAO = new Empresa(this.con);
            empresaVO = empresaDAO.consultarPorChavePrimaria(pixVO.getEmpresa(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            int creditoDCC = empresaVO.getCreditoDCC();
            Integer qtdUsada = 1;

            if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {

                if (creditoDCC - qtdUsada <= 0) {
                    RemessaService remessaService;
                    try {
                        remessaService = new RemessaService(this.con);
                        remessaService.enviarEmailCreditosAcabando(empresaVO, null, creditoDCC);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    } finally {
                        remessaService = null;
                    }

                }
                marcarContabilizadaPacto(true, pixVO);
                empresaDAO.debitarCreditoDCC(qtdUsada, empresaVO.getCodigo(), "PIX");
                empresaVO.setCreditoDCC(creditoDCC - qtdUsada);

            } else {
                marcarContabilizadaPacto(false, pixVO);
            }

            if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo())) {

                String chave = "";
                try {
                    chave = DAO.resolveKeyFromConnection(con);
                } catch (Exception e) {
                    chave = "";
                }

                if (UteisValidacao.emptyString(chave)) {
                    throw new ConsistirException("Não é possível consultar o crédito disponível da rede de empresa!");
                }

                String urlOamd = PropsService.getPropertyValue(chave, PropsService.urlOamd);

                JSONObject info = null;
                try {
                    info = empresaDAO.obterInfoRedeDCC(urlOamd, chave);
                } catch (Exception ex){
                    throw new ConsistirException("Não foi possível obter infoRedeDcc para debitar os créditos para o Pix");
                }

                creditoDCC = info.getInt("creditos");
                if (creditoDCC - qtdUsada <= 0) {
                    RemessaService remessaService = null;
                    try {
                        remessaService = new RemessaService(this.con);
                        remessaService.enviarEmailCreditosAcabando(empresaVO, null, creditoDCC);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    } finally {
                        remessaService = null;
                    }
                }
                if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                    throw new ConsistirException("Não é possível criar o Pix. Limite de crédito Pacto não é suficiente. Empresa: " + empresaVO.getNome());
                }
                empresaVO.setCreditoDCC(creditoDCC - qtdUsada);
            }
        } finally {
            empresaDAO = null;
            empresaVO = null;
        }
    }

    private void marcarContabilizadaPacto(boolean contabilizada, PixVO pixVO) throws Exception {
        if (pixVO != null && !UteisValidacao.emptyNumber(pixVO.getCodigo())) {
            String sql = "UPDATE pix SET contabilizadaPacto = ? WHERE codigo = ?";
            try (PreparedStatement stm = con.prepareStatement(sql)) {
                stm.setBoolean(1, contabilizada);
                stm.setInt(2, pixVO.getCodigo());
                stm.execute();
            }
        }
    }

    public void validarCreditosPacto(EmpresaVO empresaVO) throws Exception {
        int creditoDCC = empresaVO.getCreditoDCC();
        int qtdUsada = 1;

        Date dataExpiracaoCreditoDCC = empresaVO.getDataExpiracaoCreditoDCC();
        if (dataExpiracaoCreditoDCC != null && Calendario.menorOuIgual(dataExpiracaoCreditoDCC, Calendario.hoje())) {
            throw new ConsistirException("Não é possível criar o Pix. Seus créditos estão bloqueados.");
        }

        if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo()) ||
                empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo())) {
            if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                throw new ConsistirException("Não é possível criar o Pix. Limite de crédito Pacto não é suficiente. Empresa: " + empresaVO.getNome());
            }
        }
    }

    public void incluirDataPagamento(PixVO pixVO) throws SQLException {
        if (pixVO.getDataPagamento() != null) {
            String sql = "UPDATE pix SET datapagamento = '" + Uteis.getDataJDBCTimestamp(pixVO.getDataPagamento()) + "' WHERE codigo=" + pixVO.getCodigo();
            PreparedStatement statement = con.prepareStatement(sql);
            statement.execute();
        }
    }

    public void excluirPorReciboPagamento(ReciboPagamentoVO reciboPagamentoVO, UsuarioVO usuarioVO) throws Exception {
        List<PixVO> lista = this.consultarPorReciboPagamento(reciboPagamentoVO.getCodigo(), false);
        for (PixVO pixVO : lista) {
            excluirPorReciboPagamento(pixVO, reciboPagamentoVO, usuarioVO);
        }
    }

    private void excluirPorReciboPagamento(PixVO pixVO, ReciboPagamentoVO reciboPagamentoVO, UsuarioVO usuarioVO) throws Exception {
        try (PreparedStatement ps = con.prepareStatement("update pix set recibopagamento = null where codigo = ?")) {
            ps.setInt(1, pixVO.getCodigo());
            ps.execute();
        }

        JSONObject json = obterJSONBaseLog(usuarioVO);
        if (reciboPagamentoVO != null) {
            json.put("recibopagamento", reciboPagamentoVO.getCodigo());
        }

        PixLogVO pixLogVO = new PixLogVO(pixVO, usuarioVO, PixOperacaoLogEnum.ESTORNO_RECIBO);
        pixLogVO.setLog(json.toString());

        PixLog pixLogDAO = new PixLog(this.con);
        pixLogDAO.incluir(pixLogVO);
        pixLogDAO = null;
    }

    private JSONObject obterJSONBaseLog(UsuarioVO usuarioVO) {
        JSONObject json = new JSONObject();
        json.put("data", Uteis.getDataComHora(Calendario.hoje()));
        if (usuarioVO != null) {
            json.put("usuario_codigo", usuarioVO.getCodigo());
            json.put("usuario_nome", usuarioVO.getNome());
        }
        return json;
    }

    public List<PixVO> consultarPorReciboPagamento(Integer reciboPagamento, boolean montarPixMovParcelas) throws Exception {
        String sql = "SELECT * from pix WHERE ReciboPagamento = " + reciboPagamento;
        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet rs = statement.executeQuery();
        List<PixVO> pixVOS = new ArrayList<>();
        while (rs.next()) {
            pixVOS.add(montarDados(rs, montarPixMovParcelas));
        }
        return pixVOS;
    }

    public void gerarLogAlterarStatus(PixVO pixVO) {
        PixLog pixLogDAO;
        try {
            pixLogDAO = new PixLog(this.con);
            PixLogVO pixLogVO = new PixLogVO(pixVO, null, PixOperacaoLogEnum.ALTERAR_STATUS);
            JSONObject jsonLog = new JSONObject();
            jsonLog.put("codigo", pixVO.getCodigo());
            jsonLog.put("anterior", pixVO.getStatusAnterior());
            jsonLog.put("atual", pixVO.getStatus());
            jsonLog.put("operacao", "PIX - STATUS");
            pixLogVO.setLog(jsonLog.toString());
            pixLogDAO.incluir(pixLogVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pixLogDAO = null;
        }
    }

    public String obterCPF(PessoaVO pessoaVO) throws Exception {
        Pessoa pessoaDAO;
        try {
            pessoaDAO = new Pessoa(this.con);

            PessoaCPFTO pessoaCPFTO = pessoaDAO.obterCpfValidandoIdade(pessoaVO);
            if (pessoaCPFTO == null || UteisValidacao.emptyString(pessoaCPFTO.getCpfResponsavel().trim())) {
                throw new ValidacaoException("Não existe CPF no cadastro. Adicione um CPF ao cadastro e tente novamente.");
            }
            return pessoaCPFTO.getCpfResponsavel();
        } finally {
            pessoaDAO = null;
        }
    }

    public PixVO gerarObterPix(String chave, PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO,
                               EmpresaVO empresaVO, List<MovParcelaVO> listaParcelas, UsuarioVO usuarioVO,
                               Integer formaPagamento, OrigemCobrancaEnum origemCobrancaEnum, boolean cobrarMultaEJuros) throws Exception {
        return gerarObterPix(chave, pessoaVO, convenioCobrancaVO, empresaVO, listaParcelas, usuarioVO, formaPagamento,
                origemCobrancaEnum, null, null, null, cobrarMultaEJuros);
    }

    public PixVO gerarObterPix(String chave, PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO,
                               EmpresaVO empresaVO, List<MovParcelaVO> listaParcelas, UsuarioVO usuarioVO,
                               Integer formaPagamento, OrigemCobrancaEnum origemCobrancaEnum,
                               Double descontoValorFixo, Double descontoPercentual, Integer expiracao, boolean cobrarMultaEJuros) throws Exception {
        PixService pixService;

        try {
            pixService = new PixService(this.con);

            //validar crédito pacto
            validarCreditosPacto(empresaVO);

            PessoaCPFTO pessoaCPFTO = this.obterDadosPessoaPagador(empresaVO.getCodigo(), pessoaVO, true, true);
            pessoaVO.setNome(pessoaCPFTO.getNomeResponsavel());
            pessoaVO.setCfp(Uteis.formatarCpfCnpj(pessoaCPFTO.getCpfResponsavel(), true));

            List<PixMovParcelaVO> pixMovParcelas = new ArrayList<>();
            List<Integer> codigosParcelas = new ArrayList<>();
            Set<Integer> listaParcelasNovas = new HashSet<>();
            Double valorPix = 0.0;
            for (MovParcelaVO movParcelaVO : listaParcelas) {
                valorPix += cobrarMultaEJuros ? movParcelaVO.getValorParcela() + movParcelaVO.getValorMulta() + movParcelaVO.getValorJuros() : movParcelaVO.getValorParcela();
                PixMovParcelaVO pixMovParcelaVO = new PixMovParcelaVO();
                pixMovParcelaVO.setMovParcelaVO(movParcelaVO);
                pixMovParcelaVO.setMovparcela(movParcelaVO.getCodigo());

                //não setar 0.0 no banco, deixar nulo mesmo
                if (cobrarMultaEJuros && (!UteisValidacao.emptyNumber(movParcelaVO.getValorMulta()) || !UteisValidacao.emptyNumber(movParcelaVO.getValorJuros()) )) {
                    pixMovParcelaVO.setValorMulta(movParcelaVO.getValorMulta());
                    pixMovParcelaVO.setValorJuros(movParcelaVO.getValorJuros());
                }

                pixMovParcelas.add(pixMovParcelaVO);
                codigosParcelas.add(movParcelaVO.getCodigo());
                listaParcelasNovas.add(movParcelaVO.getCodigo());
            }

            //verifica se existe algum pix ativo para as parcelas selecionadas
            List<PixVO> pixAtivosParcelas = consultarAtivosPorParcelas(codigosParcelas);
            for (PixVO pixVOAtivo : pixAtivosParcelas) {

                //verifica se o tipo do convênio do pixVOAtivo é o mesmo do convênio que deseja criar
                boolean cancelarPix = false;
                if (!pixVOAtivo.getConveniocobranca().getCodigo().equals(convenioCobrancaVO.getCodigo()) ||
                        (Uteis.arredondarForcando2CasasDecimais(pixVOAtivo.getValor()) != Uteis.arredondarForcando2CasasDecimais(valorPix))) {
                    cancelarPix = true;
                } else {
                    //verificar se as parcelas do pixVOAtivo são as mesmas que é para gerar
                    Set<Integer> listaParcelasPix = new HashSet<>();
                    for (PixMovParcelaVO pixMovParcelaVO : pixVOAtivo.getPixMovParcelas()) {
                        if (pixMovParcelaVO.getMovParcelaVO() == null) {
                            cancelarPix = true;
                            break;
                        }
                        listaParcelasPix.add(pixMovParcelaVO.getMovParcelaVO().getCodigo());
                    }
                    if (!cancelarPix) {
                        cancelarPix = !listaParcelasPix.equals(listaParcelasNovas);
                    }
                }

                if (cancelarPix) {
                    cancelar(pixVOAtivo);
                } else {
                    //todas parcelas são iguais e é do mesmo convênio então utilizar o pix já criado
                    pixVOAtivo.setChaveBanco(chave);
                    return pixVOAtivo;
                }
            }

            //o valor total é sem o desconto
            Double valorDesconto = 0.0;
            if (!UteisValidacao.emptyNumber(descontoValorFixo)) {
                valorDesconto = descontoValorFixo;
            } else if (!UteisValidacao.emptyNumber(descontoPercentual)) {
                valorDesconto = Uteis.arredondarForcando2CasasDecimais((valorPix * descontoPercentual) / 100);
            }

            PixRequisicaoDto pixRequisicaoDto = pixService.criarCobranca(
                    convenioCobrancaVO,
                    pessoaVO.getCfp(),
                    pessoaVO,
                    telefoneCriarPix(pessoaVO),
                    Uteis.arredondarForcando2CasasDecimais(valorPix - valorDesconto),
                    "PAGAMENTO PARA " + empresaVO.getNome().toUpperCase(),
                    expiracao,
                    empresaVO,
                    chave,
                    listaParcelas
            );

            if (pixRequisicaoDto != null) {
                pixRequisicaoDto.getPixDto().setChave(chave);
                PixVO pixVO = incluir(pixRequisicaoDto, convenioCobrancaVO, pessoaVO.getCodigo(),
                        empresaVO.getCodigo(), pixMovParcelas, formaPagamento, usuarioVO, origemCobrancaEnum, valorDesconto);
                pixVO.setChaveBanco(chave);
                return pixVO;
            } else {
                throw new Exception("Erro ao gerar pix");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixService = null;
        }
    }

    private String telefoneCriarPix(PessoaVO pessoaVO) throws Exception {
        Telefone telefoneDAO;
        try {
            telefoneDAO = new Telefone(this.con);
            String telefone = pessoaVO.getTelefonesCelular();
            if ((telefone == null || telefone.isEmpty()) && pessoaVO.getTelefoneVOs().size() == 0) {
                pessoaVO.setTelefoneVOs(telefoneDAO.consultarTelefones(pessoaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            if (telefone == null || telefone.isEmpty()
                    && pessoaVO.getTelefoneVOs() != null && pessoaVO.getTelefoneVOs().size() > 0) {
                telefone = pessoaVO.getTelefoneVOs().get(0).getNumeroApresentar();
            }
            return telefone;
        } finally {
            telefoneDAO = null;
        }
    }

    public FormaPagamentoVO obterFormaPagamentoPix(PixVO pixVO, ConvenioCobrancaVO convenioVO) {
        FormaPagamento formaPagamentoDAO;
        try {
            formaPagamentoDAO = new FormaPagamento(this.con);

            //utilizar a forma de pagamento informado no pix
            if (pixVO != null && !UteisValidacao.emptyNumber(pixVO.getFormapagamento())) {
                FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorChavePrimaria(pixVO.getFormapagamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                    return formaPagamentoVO;
                }
            }

            //verificar se existe uma forma de pagamento que tem o mesmo convenio do pix
            List<FormaPagamentoVO> lista = formaPagamentoDAO.consultarPorTipoFormaPagamento(TipoFormaPagto.PIX, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (FormaPagamentoVO obj : lista) {
                if (obj.getConvenioCobrancaVO() != null &&
                        obj.getConvenioCobrancaVO().getCodigo().equals(convenioVO.getCodigo())) {
                    return obj;
                }
            }

            //caso não encontre buscar uma forma de pagamento pix
            return formaPagamentoDAO.consultarPorTipo(TipoFormaPagto.PIX);
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            formaPagamentoDAO = null;
        }
    }

    public void verificarExcluirParcelaMultaJurosPix(PixVO pixVO) throws Exception {
        try {
            //não pode estar ativo ou concluido
            if (pixVO != null && !UteisValidacao.emptyNumber(pixVO.getCodigo()) &&
                    !pixVO.getStatusEnum().equals(PixStatusEnum.ATIVA) && !pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                excluirMultaJuros(pixVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private void excluirMultaJuros(PixVO pixVO) throws Exception {
        MovParcela movParcelaDAO;
        MovProduto movProdutoDAO;
        PixMovParcela pixMovParcelaDAO;
        try {
            movParcelaDAO = new MovParcela(con);
            movProdutoDAO = new MovProduto(con);
            pixMovParcelaDAO = new PixMovParcela(con);

            List<PixMovParcelaVO> listaParcelasPix = montarPixMovParcelas(pixVO);
            for (PixMovParcelaVO pixMovParcelaVO : listaParcelasPix) {
                if (pixMovParcelaVO.getMovParcelaVO().getDescricao().contains("MULTA E JUROS")) {
                    //verificar se está em aberto
                    if (pixMovParcelaVO.getMovParcelaVO().getSituacao().equals("EA")) {
                        Uteis.logar(null, "Excluir parcela de MULTA E JUROS | Pix " + pixVO.getCodigo() + " | Parcela " + pixMovParcelaVO.getMovparcela());
                        pixMovParcelaDAO.excluir(pixMovParcelaVO.getMovparcela(), pixVO.getCodigo());
                        movProdutoDAO.excluirPorParcela(pixMovParcelaVO.getMovparcela());
                        movParcelaDAO.excluirSemCommit(pixMovParcelaVO.getMovparcela());
                    } else {
                        Uteis.logar(null, "Parcela de MULTA E JUROS não está em aberto " + pixMovParcelaVO.getMovParcelaVO().getSituacao() + " | Pix " + pixVO.getCodigo() + " | Parcela " + pixMovParcelaVO.getMovparcela());
                    }
                }
            }
        } finally {
            movParcelaDAO = null;
            movProdutoDAO = null;
            pixMovParcelaDAO = null;
        }
    }

    public Integer consultarQtdParcelas(Integer pix) throws Exception {
        try (PreparedStatement ps = con.prepareStatement("select count(*) as qtd from pixmovparcela  where pix = " + pix)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("qtd");
                } else {
                    return 0;
                }
            }
        }
    }

    public List<PixVO> consultarPactoPay(FiltroPactoPayDTO filtroDTO, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLPixPactoPay(filtroDTO, TipoConsultaPactoPayEnum.LISTA);
        processarPaginador(sql, "t.data desc", paginadorDTO);
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, false, false);
            }
        }
    }

    public void processarPaginador(StringBuilder sql, String orderByDefault, PaginadorDTO paginadorDTO) throws Exception {
        if (paginadorDTO != null) {

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            paginadorDTO.setQuantidadeTotalElementos(SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql.toString() + ") as a", this.con).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            //adicionar ordenação
            if (!UteisValidacao.emptyString(orderByDefault)
                    && paginadorDTO.getSQLOrderByUse() != null
                    && UteisValidacao.emptyString(paginadorDTO.getSQLOrderByUse().trim())) {
                sql.append(" ORDER BY ").append(orderByDefault).append(" \n");
            } else {
                sql.append(paginadorDTO.getSQLOrderByUse());
            }

            //adicionar limit
            sql.append(paginadorDTO.getSQLLimitByUse());

            //adicionar offset
            sql.append(paginadorDTO.getSQLOffsetByUse());
        }
    }

    private StringBuilder obterSQLPixPactoPay(FiltroPactoPayDTO filtroDTO, TipoConsultaPactoPayEnum tipoConsultaPactoPayEnum) {
        return obterSQLPixPactoPay(filtroDTO, tipoConsultaPactoPayEnum, null);
    }

    private StringBuilder obterSQLPixPactoPay(FiltroPactoPayDTO filtroDTO,
                                              TipoConsultaPactoPayEnum tipoConsultaPactoPayEnum,
                                              String situacaoParcela) {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");

        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.LISTA)) {
            sql.append("t.*, \n");
            sql.append("usu.nome as nome_usuario, \n");
            sql.append("pes.nome as nome_pessoa, \n");
            sql.append("cl.codigo as cliente_codigo, \n");
            sql.append("cl.matricula as cliente_matricula, \n");
            sql.append("(select count(*) from pixmovparcela where pix = t.codigo) as qtd_movparcelas \n");
        } else if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR)) {
            sql.append("t.status, \n");
            sql.append("count(t.codigo) as qtd, \n");
            sql.append("sum(t.valor) as total \n");
        }
        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_TIPO)) {
            sql.append("cc.tipoconvenio as tipo, \n");
            sql.append("t.status, \n");
            sql.append("count(t.codigo) as qtd, \n");
            sql.append("sum(t.valor) as total \n");
        } else if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA)) {
            sql.append("mp.situacao, \n");
            sql.append("count(distinct(mp.codigo)) as qtd, \n");
            sql.append("sum(tm.valormovparcela) as total \n");
        } else if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA)) {
            sql.append("distinct \n");
            sql.append("clim.matricula, \n");
            sql.append("pesm.nome, \n");
            sql.append("mp.pessoa, \n");
            sql.append("mp.codigo as parcela, \n");
            sql.append("mp.situacao, \n");
            sql.append("mp.descricao, \n");
            sql.append("mp.valorparcela, \n");
            sql.append("mp.nrtentativas, \n");
            sql.append("mp.datavencimento \n");
        }

        sql.append("from pix t \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = t.conveniocobranca\n");

        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA) ||
                tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA)) {
            sql.append("inner join pixmovparcela tm on tm.pix = t.codigo \n");
            sql.append("inner join movparcela mp on mp.codigo = tm.movparcela \n");

            if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA)) {
                sql.append("inner join pessoa pesm on pesm.codigo = mp.pessoa \n");
                sql.append("left join cliente clim on clim.pessoa = mp.pessoa \n");
            }
        }

        sql.append("left join usuario usu on usu.codigo = t.usuarioresponsavel\n");
        sql.append("left join pessoa pes on pes.codigo = t.pessoa\n");
        sql.append("left join cliente cl on cl.pessoa = t.pessoa\n");
        sql.append("where t.data::date between '").append(Uteis.getDataFormatoBD(filtroDTO.getInicioDate())).append("' and '").append(Uteis.getDataFormatoBD(filtroDTO.getFimDate())).append("' \n");

        if (!UteisValidacao.emptyString(situacaoParcela)) {
            sql.append("and mp.situacao = '").append(situacaoParcela).append("' \n");
        }

        if (!UteisValidacao.emptyString(obterStatus(filtroDTO))) {
            sql.append("and t.status in (").append(obterStatus(filtroDTO)).append(") \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getEmpresas())) {
            sql.append("and t.empresa in (").append(filtroDTO.getEmpresasString()).append(") \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getConvenios())) {
            sql.append("and t.conveniocobranca in (").append(filtroDTO.getConveniosString()).append(") \n");
        }

        if (!UteisValidacao.emptyString(filtroDTO.getParametro())) {
            sql.append("and (UPPER(t::text) like '%").append(filtroDTO.getParametro().toUpperCase()).append("%' ");
            sql.append(" OR UPPER(pes::text) like '%").append(filtroDTO.getParametro().toUpperCase()).append("%') ");
        }
        return sql;
    }

    public List<TotalizadorDTO> consultarPactoPayTotalizador(FiltroPactoPayDTO filtroDTO, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLPixPactoPay(filtroDTO, TipoConsultaPactoPayEnum.TOTALIZADOR);
        sql.append("group by 1 \n");
        processarPaginador(sql, "1", paginadorDTO);

        TotalizadorDTO dtoTotal = new TotalizadorDTO();
        dtoTotal.setQuantidade(0);
        dtoTotal.setValor(0.0);
        dtoTotal.setDescricao(StatusPactoPayEnum.GERADA.getDescricao());
        dtoTotal.setCodigo(String.valueOf(StatusPactoPayEnum.GERADA.getCodigo()));

        List<TotalizadorDTO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TotalizadorDTO dto = new TotalizadorDTO();
                    dto.setQuantidade(rs.getInt("qtd"));
                    dto.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("total")));

                    dtoTotal.setQuantidade(dtoTotal.getQuantidade() + dto.getQuantidade());
                    dtoTotal.setValor(Uteis.arredondarForcando2CasasDecimais(dtoTotal.getValor() + dto.getValor()));

                    PixStatusEnum pixStatusEnum = PixStatusEnum.valueOf(rs.getString("status"));
                    dto.setDescricao(pixStatusEnum.getStatusPactoPayEnum().getDescricaoPix());
                    dto.setCodigo(String.valueOf(pixStatusEnum.getStatusPactoPayEnum().getCodigo()));
                    lista.add(dto);
                }
            }
        }
        lista.add(dtoTotal);
        return lista;
    }

    public List<TotalizadorTipoDTO> consultarPactoPayTotalizadorPorTipo(FiltroPactoPayDTO filtroDTO, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLPixPactoPay(filtroDTO, TipoConsultaPactoPayEnum.TOTALIZADOR_TIPO);
        sql.append("group by 1,2 \n");
        processarPaginador(sql, "1,2", paginadorDTO);
        Map<TipoConvenioCobrancaEnum, List<TotalizadorDTO>> mapa = new HashMap<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum = TipoConvenioCobrancaEnum.valueOf(rs.getInt("tipo"));
                    List<TotalizadorDTO> lista = mapa.get(tipoConvenioCobrancaEnum);
                    if (lista == null) {
                        lista = new ArrayList<>();
                    }
                    TotalizadorDTO dto = new TotalizadorDTO();
                    dto.setQuantidade(rs.getInt("qtd"));
                    dto.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("total")));
                    PixStatusEnum pixStatusEnum = PixStatusEnum.valueOf(rs.getString("status"));
                    dto.setDescricao(pixStatusEnum.getStatusPactoPayEnum().getDescricaoPix());
                    dto.setCodigo(String.valueOf(pixStatusEnum.getStatusPactoPayEnum().getCodigo()));
                    lista.add(dto);

                    mapa.put(tipoConvenioCobrancaEnum, lista);
                }
            }
        }

        List<TotalizadorTipoDTO> lista = new ArrayList<>();
        for (TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum : mapa.keySet()) {
            TotalizadorTipoDTO dto = new TotalizadorTipoDTO();
            dto.setTipo_codigo(tipoConvenioCobrancaEnum.getCodigo());
            dto.setTipo_descricao(Uteis.removerEspacosInicioFimString(tipoConvenioCobrancaEnum.getDescricao().toUpperCase().replace("PIX", "")));
            dto.setTotalizadores(mapa.get(tipoConvenioCobrancaEnum));
            lista.add(dto);
        }
        return lista;
    }

    public List<TotalizadorDTO> consultarPactoPayTotalizadorPorParcela(FiltroPactoPayDTO filtroDTO, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLTotalizadorParcela(filtroDTO, TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA, null);
        sql.append("group by situacao \n");
        processarPaginador(sql, "3 desc", paginadorDTO);
        List<TotalizadorDTO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TotalizadorDTO dto = new TotalizadorDTO();
                    dto.setQuantidade(rs.getInt("qtd"));
                    dto.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("total")));

                    String situacao = rs.getString("situacao");
                    String descricao = "";
                    if (situacao.equals("EA")) {
                        descricao = "Em aberto";
                    }
                    if (situacao.equals("PG")) {
                        descricao = "Pagas";
                    }
                    if (situacao.equals("CA")) {
                        descricao = "Canceladas";
                    }
                    if (situacao.equals("RG")) {
                        descricao = "Renegociadas";
                    }

                    dto.setDescricao(descricao.toUpperCase());
                    dto.setCodigo(situacao);
                    lista.add(dto);
                }
            }
        }
        return lista;
    }

    private StringBuilder obterSQLTotalizadorParcela(FiltroPactoPayDTO filtroDTO, TipoConsultaPactoPayEnum tipoConsultaPactoPayEnum, String situacaoParcela) {
        StringBuilder sql = new StringBuilder();
        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA)) {
            sql.append("select \n");
            sql.append("situacao, \n");
            sql.append("count(codigo) as qtd, \n");
            sql.append("sum(valorparcela) as total \n");
            sql.append("from ( \n");
            sql.append("select distinct \n");
            sql.append("mp.* \n");
        } else if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA)) {
            sql.append("select \n");
            sql.append("distinct \n");
            sql.append("clim.matricula, \n");
            sql.append("pesm.nome, \n");
            sql.append("mp.pessoa, \n");
            sql.append("mp.codigo as parcela, \n");
            sql.append("mp.situacao, \n");
            sql.append("mp.descricao, \n");
            sql.append("mp.valorparcela, \n");
            sql.append("mp.nrtentativas, \n");
            sql.append("mp.datavencimento \n");
        }
        sql.append("from movparcela mp \n");
        sql.append("inner join pixmovparcela tmp on tmp.movparcela = mp.codigo \n");
        sql.append("inner join pix t on t.codigo = tmp.pix \n");
        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA)) {
            sql.append("inner join pessoa pesm on pesm.codigo = mp.pessoa \n");
            sql.append("left join cliente clim on clim.pessoa = mp.pessoa \n");
            sql.append("left join pessoa pes on pes.codigo = t.pessoa\n");
            sql.append("left join cliente cl on cl.pessoa = t.pessoa\n");
        }
        sql.append("where t.data::date between '").append(Uteis.getDataFormatoBD(filtroDTO.getInicioDate())).append("' and '").append(Uteis.getDataFormatoBD(filtroDTO.getFimDate())).append("' \n");

        if (!UteisValidacao.emptyString(situacaoParcela)) {
            sql.append("and mp.situacao = '").append(situacaoParcela).append("' \n");
        }

        if (!UteisValidacao.emptyString(obterStatus(filtroDTO))) {
            sql.append("and t.status in (").append(obterStatus(filtroDTO)).append(") \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getEmpresas())) {
            sql.append("and t.empresa in (").append(filtroDTO.getEmpresasString()).append(") \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getConvenios())) {
            sql.append("and t.conveniocobranca in (").append(filtroDTO.getConveniosString()).append(") \n");
        }

        if (!UteisValidacao.emptyString(filtroDTO.getParametro())) {
            sql.append("and (UPPER(t::text) like '%").append(filtroDTO.getParametro().toUpperCase()).append("%') \n");
        }
        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA)) {
            sql.append(") sql \n");
        }
        return sql;
    }

    private String obterStatus(FiltroPactoPayDTO filtroDTO) {
        if (UteisValidacao.emptyList(filtroDTO.getSituacoes())) {
            return "";
        }
        StringBuilder lista = new StringBuilder();

        for (Integer situacaoPay : filtroDTO.getSituacoes()) {
            for (PixStatusEnum pixStatusEnum : PixStatusEnum.values()) {
                if (pixStatusEnum.getStatusPactoPayEnum().getCodigo().equals(situacaoPay)) {
                    lista.append(", '").append(pixStatusEnum.name()).append("'");
                }
            }
        }
        return lista.toString().replaceFirst(", ", "");
    }

    public List<ParcelaDTO> consultarPactoPayTotalizadorPorParcelaLista(FiltroPactoPayDTO filtroDTO, String situacaoParcela, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLTotalizadorParcela(filtroDTO, TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA, situacaoParcela);
        processarPaginador(sql, "pesm.nome", paginadorDTO);
        List<ParcelaDTO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    ParcelaDTO dto = new ParcelaDTO();
                    dto.setCodigo(rs.getInt("parcela"));
                    dto.setMatricula(rs.getString("matricula"));
                    dto.setNome(rs.getString("nome"));
                    dto.setPessoa(rs.getInt("pessoa"));
                    dto.setDescricao(rs.getString("descricao"));

                    MovParcelaVO movParcelaVO = new MovParcelaVO();
                    movParcelaVO.setSituacao(rs.getString("situacao"));
                    dto.setSituacao(movParcelaVO.getSituacao());
                    dto.setSituacaoApresentar(movParcelaVO.getSituacao_Apresentar().toUpperCase());

                    dto.setValorParcela(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorparcela")));
                    dto.setNrTentativas(rs.getInt("nrtentativas"));
                    dto.setVencimento(Calendario.getDataAplicandoFormatacao(rs.getDate("datavencimento"), "dd/MM/yyyy"));
                    lista.add(dto);
                }
            }
        }
        return lista;
    }

    public String obterDetalhePixPJBank(Integer pix) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        PixService service;
        try {
            service = new PixService(this.con);
            PixVO pixVO = consultarPorCodigo(pix);
            convenioCobrancaDAO = new ConvenioCobranca(this.con);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(pixVO.getConveniocobranca().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            PixDto pixDto = new PixDto();

            if (convenioCobrancaVO.isPixPjBank()) {
                PixServiceInterfaceFacade pixService = service.getPixService(convenioCobrancaVO, this.con);
                if (pixService != null) {
                    pixDto = pixService.consultarCobranca(pixVO.getTxid(), convenioCobrancaVO).getPixDto();
                }
            } else {
                throw new Exception("Não é possível consultar pix de outro convênio que não seja PjBank");
            }

            if (UteisValidacao.emptyString(pixDto.getLinkInfo())) {
                throw new Exception("Não foi possível obter as informações do pix");
            }

            return pixDto.getLinkInfo();

        } finally {
            convenioCobrancaDAO = null;
            service = null;
        }
    }
    public void preencherCredenciaisPjBank(ConvenioCobrancaVO convenioCobrancaVO) {
        if (convenioCobrancaVO.isPixPjBank()) {
            if (UteisValidacao.emptyString(convenioCobrancaVO.getPixClientId())) {
                convenioCobrancaVO.setPixClientId(convenioCobrancaVO.getCredencialPJBank());
            }
            if (UteisValidacao.emptyString(convenioCobrancaVO.getPixClientSecret())) {
                convenioCobrancaVO.setPixClientSecret(convenioCobrancaVO.getChavePJBank());
            }
        }
    }

    @Override
    public List<PixVO> consultarParaProcesso(String sql) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        try (ResultSet rs = criarConsulta(sql, con)) {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            List<PixVO> lista = new ArrayList<>();
            while (rs.next()) {
                PixVO pixVO = new PixVO();
                pixVO.setCodigo(rs.getInt("codigo"));
                pixVO.setTxid(rs.getString("txid"));

                MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
                pixVO.setMovPagamentoVO(movPagamentoVO);
                pixVO.getMovPagamentoVO().setCodigo(rs.getInt("codMovPagamento"));

                pixVO.setConveniocobranca(convenioCobrancaDAO.consultarPorChavePrimaria(rs.getInt("conveniocobranca"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                lista.add(pixVO);
            }
            return lista;
        } catch (Exception ex) {

        } finally {
            convenioCobrancaDAO = null;
        }
        return new ArrayList<>();
    }

    public PessoaCPFTO obterDadosPessoaPagador(Integer empresa, PessoaVO pessoaVO,
                                               boolean validarNome, boolean validarCPF) throws Exception {
        Cliente clienteDAO;
        Pessoa pessoaDAO;
        Empresa empresaDAO;
        try {
            clienteDAO = new Cliente(con);
            pessoaDAO = new Pessoa(con);
            empresaDAO = new Empresa(con);

            if (pessoaVO == null || UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                return null;
            }

            boolean utilizarNomeResponsavelNoPixMenorIdade = empresaDAO.isUtilizarNomeResponsavelNoPixMenorIdade(empresa);
            boolean utilizarNomeResponsavelNoPixMaiorIdade = empresaDAO.isUtilizarNomeResponsavelNoPixMaiorIdade(empresa);
            PessoaVO pessoaVOCompleta = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaCPFTO pessoaCPFTO = new PessoaCPFTO();
            pessoaCPFTO.setCodigo(pessoaVOCompleta.getCodigo());
            pessoaCPFTO.setPessoa(pessoaVOCompleta.getCodigo());
            pessoaCPFTO.setNome(pessoaVOCompleta.getNome());

            String nomeResponsavel = "";
            String cpfResponsavel = "";
            String cnpjResponsavel = "";

            cnpjResponsavel = pessoaVOCompleta.getCnpj();
            if ((utilizarNomeResponsavelNoPixMenorIdade && pessoaVOCompleta.getDataNasc() != null && pessoaVOCompleta.getIdade() < 18)
                    || (utilizarNomeResponsavelNoPixMaiorIdade && (pessoaVOCompleta.getDataNasc() != null && UteisValidacao.emptyString(pessoaVOCompleta.getCfp()) &&
                    pessoaVOCompleta.getIdade() >= 18))){

                PessoaVO pessoaResponsavel = clienteDAO.obterPessoaResponsavelCliente(null, pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (pessoaResponsavel != null && !UteisValidacao.emptyString(pessoaResponsavel.getCfp().trim())) {
                    nomeResponsavel = pessoaResponsavel.getNome();
                    cpfResponsavel = pessoaResponsavel.getCfp();
                } else if (!UteisValidacao.emptyString(pessoaVOCompleta.getNomeMae().trim()) && !UteisValidacao.emptyString(pessoaVOCompleta.getCpfMae().trim())) {
                    nomeResponsavel = pessoaVOCompleta.getNomeMae();
                    cpfResponsavel = pessoaVOCompleta.getCpfMae();
                } else if (!UteisValidacao.emptyString(pessoaVOCompleta.getNomePai().trim()) && !UteisValidacao.emptyString(pessoaVOCompleta.getCpfPai().trim())) {
                    nomeResponsavel = pessoaVOCompleta.getNomePai();
                    cpfResponsavel = pessoaVOCompleta.getCpfPai();
                } else {
                    throw new ConsistirException(String.format("Cliente %s não possui responsável cadastrado.", pessoaVOCompleta.getNome()));
                }
            } else {
                nomeResponsavel = pessoaVOCompleta.getNome();
                cpfResponsavel = pessoaVOCompleta.getCfp();
                cnpjResponsavel = pessoaVOCompleta.getCnpj();

                if (!utilizarNomeResponsavelNoPixMenorIdade && validarCPF &&
                        UteisValidacao.emptyString(cpfResponsavel) && pessoaVOCompleta.getPessoaFisica()) {
                    throw new ConsistirException("O aluno não possui CPF cadastrado e também não está habilitado a configuração na empresa para utilizar Nome/CPF do responsável no Pix.");
                }else if(UteisValidacao.emptyString(cnpjResponsavel) && pessoaVOCompleta.getPessoaJuridica()){
                    throw new ConsistirException("O aluno é uma pessoa Jurídica e não possui CNPJ cadastrado.");
                }
            }


            if ((pessoaVOCompleta.getDataNasc() != null && pessoaVOCompleta.getIdade() < 18) && pessoaVOCompleta.getPessoaJuridica() && !utilizarNomeResponsavelNoPixMenorIdade) {
                throw new Exception("Aluno menor de idade está cadastrado como pessoa jurídica. Pode ser que a idade esteja errada ou será \n" +
                        "necessário alterar para pessoa física, cadastrar CPF do responsável e marcar nas configurações da empresa: Utilizar Nome e CPF do responsável pelo aluno no Pix.");
            }
            if (UteisValidacao.emptyString(cnpjResponsavel) && pessoaVOCompleta.getPessoaJuridica()) {
                throw new ConsistirException("O aluno é uma pessoa Jurídica e não possui CNPJ cadastrado.");
            }
            if ((pessoaVOCompleta.getDataNasc() != null && pessoaVOCompleta.getIdade() >= 18) && UteisValidacao.emptyString(cpfResponsavel) && pessoaVOCompleta.getPessoaFisica()) {
                throw new Exception("Aluno não possui CPF cadastrado");
            }
            if ((pessoaVOCompleta.getDataNasc() != null && pessoaVOCompleta.getIdade() >= 18) && UteisValidacao.emptyString(cnpjResponsavel) && pessoaVOCompleta.getPessoaJuridica()) {
                throw new Exception("Aluno não possui CNPJ cadastrado");
            }

            pessoaCPFTO.setNomeResponsavel(nomeResponsavel);
            pessoaCPFTO.setCpfResponsavel(!UteisValidacao.emptyString(cpfResponsavel) && pessoaVOCompleta.getPessoaFisica()  ? Formatador.removerMascara(cpfResponsavel) :  Formatador.removerMascara(cnpjResponsavel));

            if (validarNome && UteisValidacao.emptyString(pessoaCPFTO.getNomeResponsavel())) {
                throw new Exception("Nome do responsável não informado");
            }

            if (validarCPF && UteisValidacao.emptyString(pessoaCPFTO.getCpfResponsavel().replaceAll("[^0-9]", "")) && pessoaVOCompleta.getPessoaFisica()) {
                throw new Exception("CPF do responsável não cadastrado");
            }else if(pessoaVOCompleta.getPessoaJuridica() && UteisValidacao.emptyString(pessoaCPFTO.getCpfResponsavel().replaceAll("[^0-9]", ""))){
                throw new Exception("Pessoa Jurídica não pussui CNPJ cadastrado");
            }
            return pessoaCPFTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            clienteDAO = null;
            pessoaDAO = null;
            empresaDAO = null;
        }
    }

    public void colocarPixProcessadoOrigemPeloWebhook(int pix) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE pix SET pagoOrigemWebhook = TRUE WHERE codigo = " + pix);

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.execute();
        }
    }

    public void incluirPixHistorico(PixVO pixVO, String operacao, String dados) throws Exception {
        incluirPixHistoricoGeral(pixVO != null ? pixVO.getCodigo() : null, operacao, dados);
    }

    public void incluirPixHistoricoGeral(Integer pix, String operacao, String dados) throws Exception {
        String sql = "INSERT INTO pixhistorico(dataregistro, pix, operacao, dados) VALUES (?,?,?,?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveIntegerNull(pst, ++i, pix);
            pst.setString(++i, operacao);
            pst.setString(++i, dados);
            pst.execute();
        }
    }
}

