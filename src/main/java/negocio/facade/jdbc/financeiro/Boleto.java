package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.to.BoletoOnlineTO;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.pjbank.WebhookPJBankJSON;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.BoletoEmailTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.crm.Feriado;
import negocio.interfaces.financeiro.BoletoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.boleto.BoletoOnlineService;
import servicos.impl.boleto.asaas.CobrancaAsaasRetornoDTO;
import servicos.impl.boleto.pjbank.PJBankService;
import servicos.impl.dcc.base.RemessaService;
import servicos.integracao.pjbank.recebimento.ConsultasBoletoManager;
import servicos.interfaces.BoletoOnlineServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 07/12/2021
 */
public class Boleto extends SuperEntidade implements BoletoInterfaceFacade {

    public Boleto() throws Exception {
        super();
    }

    public Boleto(Connection con) throws Exception {
        super(con);
    }

    public void incluir(BoletoVO obj) throws Exception {
        BoletoVO.validarDados(obj);

        String sql = "INSERT INTO boleto(dataRegistro, idExterno, usuario, tipo, pessoa, empresa, situacao, valor, dataVencimento, convenioCobranca, movPagamento, reciboPagamento, " +
                "origem, paramsenvio, paramsresposta, outrasInformacoes, dataPagamento, dataCredito, valorLiquido, valorPago, valorTarifa, linkBoleto, jsonEstorno, nossoNumero, " +
                "numeroInterno, numeroExterno, linhaDigitavel, diaMesDescontoPagAntecipado, porcentagemDescontoPagAntecipado, identificador, pagoUsandoDesconto, valordescontoboletopagantecipado, " +
                "transactionReceiptUrl, codigobarrasnumerico) "
                + "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            ps.setString(++i, obj.getIdExterno());
            resolveIntegerNull(ps, ++i, obj.getUsuarioVO().getCodigo());
            ps.setInt(++i, obj.getTipo().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getPessoaVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            ps.setInt(++i, obj.getSituacao().getCodigo());
            ps.setDouble(++i, obj.getValor());
            ps.setDate(++i, Uteis.getDataJDBC(obj.getDataVencimento()));
            resolveIntegerNull(ps, ++i, obj.getConvenioCobrancaVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getMovPagamentoVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getReciboPagamentoVO().getCodigo());
            ps.setInt(++i, obj.getOrigem().getCodigo());
            ps.setString(++i, obj.getParamsEnvio());
            ps.setString(++i, obj.getParamsResposta());
            ps.setString(++i, obj.getOutrasInformacoes());
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataPagamento()));
            ps.setDate(++i, Uteis.getDataJDBC(obj.getDataCredito()));
            resolveDoubleNull(ps, ++i, obj.getValorLiquido());
            resolveDoubleNull(ps, ++i, obj.getValorPago());
            resolveDoubleNull(ps, ++i, obj.getValorTarifa());
            ps.setString(++i, obj.getLinkBoleto());
            ps.setString(++i, obj.getJsonEstorno());
            ps.setString(++i, obj.getNossoNumero());
            ps.setString(++i, obj.getNumeroInterno());
            ps.setString(++i, obj.getNumeroExterno());
            ps.setString(++i, obj.getLinhaDigitavel());
            resolveIntegerNull(ps, ++i, obj.getDiaMesDescontoPagAntecipado());
            resolveDoubleNull(ps, ++i, obj.getPorcentagemDescontoPagAntecipado());
            resolveIntegerNull(ps, ++i, obj.getIdentificador());
            ps.setBoolean(++i, obj.isPagoUsandoDesconto());
            ps.setDouble(++i, obj.getValorDescontoPagAntecipado());
            ps.setString(++i, obj.getTransactionReceiptUrl());
            ps.setString(++i, obj.getCodigoBarrasNumerico());

            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        //crédito pacto
        boolean debitarCreditosDeBoleto = !obj.getTipo().equals(TipoBoletoEnum.NENHUMA) && !obj.isPJBank()
                && !obj.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.NENHUM)
                && !obj.getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.NENHUM)
                && obj.getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO_ONLINE);
        if (debitarCreditosDeBoleto) {
            debitarCreditosPacto(obj);
        }

        incluirBoletoMovParcela(obj);
    }

    public void alterar(BoletoVO obj) throws Exception {
        String sql = "UPDATE boleto SET idExterno = ?, usuario = ?, tipo = ?, pessoa = ?, empresa = ?, situacao = ?, valor = ?, dataVencimento = ?, convenioCobranca = ?, " +
                "movPagamento = ?, reciboPagamento = ?, origem = ?, paramsenvio = ?, paramsresposta = ?, outrasInformacoes = ?, dataPagamento = ?, dataCredito = ?, valorLiquido = ?, " +
                "valorPago = ?, valorTarifa = ?, linkBoleto = ?, jsonEstorno = ?, nossoNumero = ?, numeroInterno = ?, numeroExterno = ?, linhaDigitavel = ?, diaMesDescontoPagAntecipado = ?, " +
                "porcentagemDescontoPagAntecipado = ?, identificador = ?, pagoUsandoDesconto = ?, valordescontoboletopagantecipado = ?, transactionReceiptUrl = ?, " +
                "codigobarrasnumerico = ? WHERE codigo = ?";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, obj.getIdExterno());
            resolveIntegerNull(ps, ++i, obj.getUsuarioVO().getCodigo());
            ps.setInt(++i, obj.getTipo().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getPessoaVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            ps.setInt(++i, obj.getSituacao().getCodigo());
            ps.setDouble(++i, obj.getValor());
            ps.setDate(++i, Uteis.getDataJDBC(obj.getDataVencimento()));
            resolveIntegerNull(ps, ++i, obj.getConvenioCobrancaVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getMovPagamentoVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getReciboPagamentoVO().getCodigo());
            ps.setInt(++i, obj.getOrigem().getCodigo());
            ps.setString(++i, obj.getParamsEnvio());
            ps.setString(++i, obj.getParamsResposta());
            ps.setString(++i, obj.getOutrasInformacoes());
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataPagamento()));
            ps.setDate(++i, Uteis.getDataJDBC(obj.getDataCredito()));
            resolveDoubleNull(ps, ++i, obj.getValorLiquido());
            resolveDoubleNull(ps, ++i, obj.getValorPago());
            resolveDoubleNull(ps, ++i, obj.getValorTarifa());
            ps.setString(++i, obj.getLinkBoleto());
            ps.setString(++i, obj.getJsonEstorno());
            ps.setString(++i, obj.getNossoNumero());
            ps.setString(++i, obj.getNumeroInterno());
            ps.setString(++i, obj.getNumeroExterno());
            ps.setString(++i, obj.getLinhaDigitavel());
            resolveIntegerNull(ps, ++i, obj.getDiaMesDescontoPagAntecipado());
            resolveDoubleNull(ps, ++i, obj.getPorcentagemDescontoPagAntecipado());
            resolveIntegerNull(ps, ++i, obj.getIdentificador());
            ps.setBoolean(++i, obj.isPagoUsandoDesconto());
            ps.setDouble(++i, obj.getValorDescontoPagAntecipado());
            ps.setString(++i, obj.getTransactionReceiptUrl());
            ps.setString(++i, obj.getCodigoBarrasNumerico());

            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public void debitarCreditosPacto(BoletoVO boletoVO) throws Exception {
        Empresa empresaDAO = null;
        EmpresaVO empresaVO = null;
        try {
            empresaDAO = new Empresa(this.con);
            empresaVO = empresaDAO.consultarPorChavePrimaria(boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            //Se não cobra crédito por boleto, não fazer nada
            if (!empresaVO.isCobrarCreditoPactoBoleto()) {
                return;
            }

            int creditoDCC = empresaVO.getCreditoDCC();
            Integer qtdUsada = 1;

            if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {

                if (creditoDCC - qtdUsada <= 0) {
                    RemessaService remessaService;
                    try {
                        remessaService = new RemessaService(this.con);
                        remessaService.enviarEmailCreditosAcabando(empresaVO, null, creditoDCC);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    } finally {
                        remessaService = null;
                    }

                }
                marcarContabilizadaPacto(true, boletoVO);
                empresaDAO.debitarCreditoDCC(qtdUsada, empresaVO.getCodigo(), "BOLETO");
                empresaVO.setCreditoDCC(creditoDCC - qtdUsada);

            } else {
                marcarContabilizadaPacto(false, boletoVO);
            }

            if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo())) {

                String chave = "";
                try {
                    chave = DAO.resolveKeyFromConnection(con);
                } catch (Exception e) {
                    chave = "";
                }

                if (UteisValidacao.emptyString(chave)) {
                    throw new ConsistirException("Não é possível consultar o crédito disponível da rede de empresa!");
                }

                String urlOamd = PropsService.getPropertyValue(chave, PropsService.urlOamd);

                JSONObject info = null;
                try {
                    info = empresaDAO.obterInfoRedeDCC(urlOamd, chave);
                } catch (Exception ex){
                    throw new ConsistirException("Não foi possível obter infoRedeDcc para debitar os créditos para o Boleto");
                }

                creditoDCC = info.getInt("creditos");
                if (creditoDCC - qtdUsada <= 0) {
                    RemessaService remessaService = null;
                    try {
                        remessaService = new RemessaService(this.con);
                        remessaService.enviarEmailCreditosAcabando(empresaVO, null, creditoDCC);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    } finally {
                        remessaService = null;
                    }
                }
                if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                    throw new ConsistirException("Não é possível criar o Boleto. Limite de crédito Pacto não é suficiente. Empresa: " + empresaVO.getNome());
                }
                empresaVO.setCreditoDCC(creditoDCC - qtdUsada);
            }
        } finally {
            empresaDAO = null;
            empresaVO = null;
        }
    }

    private void marcarContabilizadaPacto(boolean contabilizada, BoletoVO boletoVO) throws Exception {
        if (boletoVO != null && !UteisValidacao.emptyNumber(boletoVO.getCodigo())) {
            String sql = "UPDATE boleto SET contabilizadaPacto = ? WHERE codigo = ?";
            try (PreparedStatement stm = con.prepareStatement(sql)) {
                stm.setBoolean(1, contabilizada);
                stm.setInt(2, boletoVO.getCodigo());
                stm.execute();
            }
        }
    }

    public void validarCreditosPacto(EmpresaVO empresaVO) throws Exception {
        int creditoDCC = empresaVO.getCreditoDCC();
        int qtdUsada = 1;

        Date dataExpiracaoCreditoDCC = empresaVO.getDataExpiracaoCreditoDCC();
        if (dataExpiracaoCreditoDCC != null && Calendario.menorOuIgual(dataExpiracaoCreditoDCC, Calendario.hoje())) {
            throw new ConsistirException("Não é possível criar o Boleto. Seus créditos estão bloqueados.");
        }

        if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo()) ||
                empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo())) {
            if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                throw new ConsistirException("Não é possível criar o Boleto. Limite de crédito Pacto não é suficiente. Empresa: " + empresaVO.getNome());
            }
        }
    }

    public void alterarMovPagamentoReciboPagamento(BoletoVO obj) throws Exception {
        String sql = "UPDATE boleto SET movPagamento = ?, reciboPagamento = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            resolveIntegerNull(ps, ++i, obj.getMovPagamentoVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getReciboPagamentoVO().getCodigo());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public void alterarSituacaoComLog(BoletoVO obj, SituacaoBoletoEnum situacaoBoletoEnum,
                                      UsuarioVO usuarioVO, String operacao, String log) throws Exception {
        JSONObject json = new JSONObject();
        if (usuarioVO != null) {
            json.put("usuario_codigo", usuarioVO.getCodigo());
            json.put("usuario_nome", usuarioVO.getNome());
            json.put("usuario_username", usuarioVO.getUsername());
        }
        json.put("operacao", operacao);
        json.put("log", log);
        json.put("situacao_anterior", obj.getSituacao().getDescricao());
        json.put("situacao_nova", situacaoBoletoEnum.getDescricao());
        alterarSituacaoGeral(obj, situacaoBoletoEnum, json.toString());
    }

    public void alterarSituacao(BoletoVO obj, SituacaoBoletoEnum situacaoBoletoEnum) throws Exception {
        alterarSituacaoGeral(obj, situacaoBoletoEnum, null);
    }

    public void alterarSituacaoGeral(BoletoVO obj, SituacaoBoletoEnum situacaoBoletoEnum,
                                     String log) throws Exception {
        JSONObject json = new JSONObject();
        json.put("situacao_anterior", obj.getSituacao().getDescricao());

        String sql = "UPDATE boleto SET situacao = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, situacaoBoletoEnum.getCodigo());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }

        obj.setSituacao(situacaoBoletoEnum);
        json.put("situacao_nova", obj.getSituacao().getDescricao());
        incluirBoletoHistorico(obj.getCodigo(), "ALTERAR SITUAÇÃO", json.toString());
        if (!UteisValidacao.emptyString(log)) {
            incluirBoletoHistorico(obj.getCodigo(), "ALTERAR SITUAÇÃO", log);
        }
    }

    public void alterarOutrasInformacoes(BoletoVO obj) throws Exception {
        String sql = "UPDATE boleto SET outrasInformacoes = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, obj.getOutrasInformacoes());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public void alterarJsonEstorno(BoletoVO obj) throws Exception {
        String sql = "UPDATE boleto SET jsonEstorno = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, obj.getJsonEstorno());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    private BoletoVO montarDados(ResultSet rs, int nivelMontarDados) throws Exception {
        BoletoMovParcela boletoMovParcelaDAO;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            boletoMovParcelaDAO = new BoletoMovParcela(con);

            BoletoVO obj = new BoletoVO();
            obj.setCodigo(rs.getInt("codigo"));
            obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
            obj.setIdExterno(rs.getString("idexterno"));
            obj.setTipo(TipoBoletoEnum.obterPorCodigo(rs.getInt("tipo")));
            obj.getUsuarioVO().setCodigo(rs.getInt("usuario"));
            obj.getPessoaVO().setCodigo(rs.getInt("pessoa"));
            obj.getEmpresaVO().setCodigo(rs.getInt("empresa"));
            obj.setSituacao(SituacaoBoletoEnum.obterPorCodigo(rs.getInt("situacao")));
            obj.setValor(rs.getDouble("valor"));
            obj.setDataVencimento(rs.getDate("datavencimento"));
            obj.getConvenioCobrancaVO().setCodigo(rs.getInt("convenioCobranca"));
            obj.getMovPagamentoVO().setCodigo(rs.getInt("movpagamento"));
            obj.getReciboPagamentoVO().setCodigo(rs.getInt("reciboPagamento"));
            obj.setOrigem(OrigemCobrancaEnum.obterPorCodigo(rs.getInt("origem")));
            obj.setParamsEnvio(rs.getString("paramsenvio"));
            obj.setParamsResposta(rs.getString("paramsresposta"));
            obj.setOutrasInformacoes(rs.getString("outrasInformacoes"));
            obj.setValorLiquido(rs.getDouble("valorLiquido"));
            obj.setValorPago(rs.getDouble("valorPago"));
            obj.setValorTarifa(rs.getDouble("valorTarifa"));
            obj.setDataPagamento(rs.getTimestamp("dataPagamento"));
            obj.setDataCredito(rs.getDate("dataCredito"));
            obj.setNossoNumero(rs.getString("nossoNumero"));
            obj.setNumeroInterno(rs.getString("numeroInterno"));
            obj.setNumeroExterno(rs.getString("numeroExterno"));
            obj.setLinkBoleto(rs.getString("linkBoleto"));
            obj.setJsonEstorno(rs.getString("jsonEstorno"));
            obj.setLinhaDigitavel(rs.getString("linhaDigitavel"));
            obj.setDiaMesDescontoPagAntecipado(rs.getInt("diaMesDescontoPagAntecipado"));
            obj.setPorcentagemDescontoPagAntecipado(rs.getDouble("porcentagemDescontoPagAntecipado"));
            obj.setIdentificador(rs.getInt("identificador"));
            obj.setPagoUsandoDesconto(rs.getBoolean("pagoUsandoDesconto"));
            obj.setValorDescontoPagAntecipado(rs.getDouble("valordescontoboletopagantecipado"));
            obj.setTransactionReceiptUrl(rs.getString("transactionReceiptUrl"));
            obj.setCodigoBarrasNumerico(rs.getString("codigobarrasnumerico"));

            try {
                obj.getUsuarioVO().setNome(rs.getString("usuario_nome"));
                obj.getConvenioCobrancaVO().setDescricao(rs.getString("conveniocobranca_descricao"));
            } catch (Exception ignored) {
            }

            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
                return obj;
            }

            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
                obj.setListaBoletoMovParcela(boletoMovParcelaDAO.consultarPorCodigoBoleto(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
                obj.setListaBoletoMovParcela(boletoMovParcelaDAO.consultarPorCodigoBoleto(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            }
            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
                convenioCobrancaDAO = new ConvenioCobranca(con);
                obj.setListaBoletoMovParcela(boletoMovParcelaDAO.consultarPorCodigoBoleto(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                obj.setConvenioCobrancaVO(convenioCobrancaDAO.consultarPorChavePrimaria(obj.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            }
            return obj;
        } finally {
            boletoMovParcelaDAO = null;
            convenioCobrancaDAO = null;
        }
    }

    public List<BoletoVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados) throws Exception {
        List<BoletoVO> lista = new ArrayList<>();
        while (rs.next()) {
            lista.add(montarDados(rs, nivelMontarDados));
        }
        return lista;
    }

    public BoletoVO consultarPorChavePrimaria(final int codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM boleto WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public BoletoVO consultarPorIdexternoTipo(String idExterno, TipoBoletoEnum tipoBoletoEnum, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM boleto WHERE idexterno = ? and tipo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, idExterno);
            ps.setInt(++i, tipoBoletoEnum.getCodigo());
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    public Integer consultarTelaClienteQtd(Integer pessoa) throws Exception {
        StringBuilder sql = sqlConsultaPessoaTelaCliente(pessoa, true, 0, 0, null);
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }

    public List<BoletoVO> consultarTelaCliente(Integer pessoa, int limit, int offset) throws Exception {
        StringBuilder sql = sqlConsultaPessoaTelaCliente(pessoa, false, limit, offset, null);
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }
        }
    }

    public BoletoVO consultarChavePrimariaTelaCliente(Integer codigoBoleto) throws Exception {
        StringBuilder sql = sqlConsultaPessoaTelaCliente(null, false, 0, 0, codigoBoleto);
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }
        }
    }

    public StringBuilder sqlConsultaPessoaTelaCliente(Integer pessoa, boolean count, int limit,
                                                      int offset, Integer codigoBoleto) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        if (count) {
            sql.append("count(distinct b.codigo) \n");
        } else {
            sql.append("distinct b.*, \n");
            sql.append("u.nome as usuario_nome, \n");
            sql.append("cc.descricao as conveniocobranca_descricao \n");
        }
        sql.append("from boleto b \n");
        sql.append("left join usuario u on u.codigo = b.usuario \n");
        sql.append("left join conveniocobranca cc on cc.codigo = b.conveniocobranca \n");
        sql.append("where b.codigo in ( \n");
        sql.append("select  \n");
        sql.append("b.codigo \n");
        sql.append("from boleto b \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("and b.pessoa = ").append(pessoa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(codigoBoleto)) {
            sql.append("and b.codigo = ").append(codigoBoleto).append(" \n");
        }
        sql.append("union \n");
        sql.append("select \n");
        sql.append("b.codigo \n");
        sql.append("from boleto b \n");
        sql.append("inner join boletomovparcela bm on bm.boleto = b.codigo \n");
        sql.append("inner join movparcela mp on mp.codigo = bm.movparcela \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("and mp.pessoa = ").append(pessoa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(codigoBoleto)) {
            sql.append("and b.codigo = ").append(codigoBoleto).append(" \n");
        }
        sql.append(") \n");
        if (!count) {
            sql.append("ORDER BY b.codigo desc \n");
            sql.append("LIMIT ").append(limit).append(" \n");
            sql.append("OFFSET ").append(offset).append(" \n");
        }
        return sql;
    }

    public List<BoletoVO> consultarPorCodigoPessoa(Integer pessoa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("distinct b.* \n");
        sql.append("from boleto b \n");
        sql.append("where b.codigo in ( \n");
        sql.append("select  \n");
        sql.append("b.codigo \n");
        sql.append("from boleto b \n");
        sql.append("where b.pessoa = ").append(pessoa).append(" \n");
        sql.append("union \n");
        sql.append("select \n");
        sql.append("b.codigo \n");
        sql.append("from boleto b \n");
        sql.append("inner join boletomovparcela bm on bm.boleto = b.codigo \n");
        sql.append("inner join movparcela mp on mp.codigo = bm.movparcela \n");
        sql.append("where mp.pessoa = ").append(pessoa).append(" \n");
        sql.append(") \n");
        sql.append("order by b.codigo \n");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoVO> consultarPorMovProduto(Integer movProduto, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("distinct b.* \n");
        sql.append("from boleto b \n");
        sql.append("inner join boletomovparcela bm on bm.boleto = b.codigo \n");
        sql.append("inner join movparcela mp on mp.codigo = bm.movparcela \n");
        sql.append("inner join movprodutoparcela mpp on mpp.movparcela = mp.codigo \n");
        sql.append("where mpp.movproduto = ? ");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, movProduto);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoVO> consultarPorMovParcela(Integer movParcela, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("distinct b.* \n");
        sql.append("from boleto b \n");
        sql.append("inner join boletomovparcela bm on bm.boleto = b.codigo \n");
        sql.append("where bm.movParcela = ? ");
        sql.append("AND b.situacao != ").append(SituacaoBoletoEnum.ERRO.getCodigo());
        sql.append("AND b.situacao != ").append(SituacaoBoletoEnum.CANCELADO.getCodigo());
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, movParcela);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoVO> consultarPorContrato(Integer contrato, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("distinct b.* \n");
        sql.append("from boleto b \n");
        sql.append("inner join boletomovparcela bm on bm.boleto = b.codigo \n");
        sql.append("inner join movparcela mp on mp.codigo = bm.movparcela \n");
        sql.append("where mp.contrato = ? ");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, contrato);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoVO> consultarPorMovPagamento(Integer movPagamento, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("b.* \n");
        sql.append("from boleto b \n");
        sql.append("where b.movPagamento = ? ");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, movPagamento);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoVO> consultarPorReciboPagamento(Integer reciboPagamento, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("b.* \n");
        sql.append("from boleto b \n");
        sql.append("where b.reciboPagamento = ? ");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, reciboPagamento);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoVO> consultarPendentes(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO,
                                             boolean adicionarAguardandoPagamento, Date dataVencimentoLimite, int nivelMontarDados,
                                             Date dataInicio, Date dataFim) throws Exception {
        SituacaoBoletoEnum[] array = new SituacaoBoletoEnum[]{SituacaoBoletoEnum.AGUARDANDO_REGISTRO, SituacaoBoletoEnum.GERADO};
        if (adicionarAguardandoPagamento) {
            array = new SituacaoBoletoEnum[]{SituacaoBoletoEnum.AGUARDANDO_REGISTRO, SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO, SituacaoBoletoEnum.GERADO};
        }
        return consultarPorSituacaoConvenioCobranca(array, convenioCobrancaVO, empresaVO, dataVencimentoLimite, nivelMontarDados, dataInicio, dataFim);
    }

    public List<BoletoVO> consultarPorSituacaoConvenioCobranca(SituacaoBoletoEnum[] arraySituacaoBoleto, ConvenioCobrancaVO convenioCobrancaVO,
                                                               EmpresaVO empresaVO, Date dataVencimentoLimite, int nivelMontarDados,
                                                               Date dataInicio, Date dataFim) throws Exception {
        String situacao = "";
        if (arraySituacaoBoleto != null && arraySituacaoBoleto.length > 0) {
            for (SituacaoBoletoEnum situacaoBoletoEnum : arraySituacaoBoleto) {
                situacao += ("," + situacaoBoletoEnum.getCodigo());
            }
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("b.* \n");
        sql.append("from boleto b \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyString(situacao)) {
            sql.append("and b.situacao in (").append(situacao.replaceFirst(",", "")).append(") \n");
        }
        if (dataVencimentoLimite != null) {
            sql.append("and b.datavencimento::date <= '").append(Uteis.getDataFormatoBD(dataVencimentoLimite)).append("' \n");
        }
        if (convenioCobrancaVO != null && !UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
            sql.append("and b.conveniocobranca = ").append(convenioCobrancaVO.getCodigo()).append(" \n");
        }
        if (empresaVO != null && !UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
            sql.append("and b.empresa = ").append(empresaVO.getCodigo()).append(" \n");
        }
        if (dataInicio != null && dataFim != null) {
            sql.append("and b.datavencimento::date between '").append(Uteis.getDataFormatoBD(dataInicio)).append("' and '").append(Uteis.getDataFormatoBD(dataFim)).append("' \n");
        }
        sql.append("order by b.codigo \n");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    private void incluirBoletoMovParcela(BoletoVO obj) throws Exception {
        BoletoMovParcela boletoMovParcelaDAO;
        try {
            boletoMovParcelaDAO = new BoletoMovParcela(con);
            boletoMovParcelaDAO.incluirBoletoMovParcela(obj);
        } finally {
            boletoMovParcelaDAO = null;
        }
    }

    public void incluirBoletoHistorico(BoletoVO boleto, String operacao, String dados) throws Exception {
        incluirBoletoHistorico(boleto != null ? boleto.getCodigo() : null, operacao, dados);
    }

    public void incluirBoletoHistorico(Integer boleto, String operacao, String dados) throws Exception {
        String sql = "INSERT INTO boletohistorico(dataregistro, boleto, operacao, dados) VALUES (?,?,?,?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveIntegerNull(pst, ++i, boleto);
            pst.setString(++i, operacao);
            pst.setString(++i, dados);
            pst.execute();
        }
    }

    public void incluirHistoricoWebHookBoleto(Integer conveniocobranca, Integer empresa, String retorno) throws Exception {
        String sql = "INSERT INTO boletowebhook(dataregistro, conveniocobranca, empresa, retorno) VALUES (?,?,?,?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveIntegerNull(pst, ++i, conveniocobranca);
            pst.setInt(++i, empresa);
            pst.setString(++i, retorno);
            pst.execute();
        }
    }

    @Override
    public String consultarBoletoWebHook(Integer conveniocobranca, Integer empresa) throws Exception {
        try (ResultSet rs = criarConsulta("select retorno from boletowebhook where conveniocobranca = "+conveniocobranca+" and empresa = "+empresa, con)) {
            if (rs.next()) {
                return rs.getString("retorno");
            }
            return "";
        }
    }

    public PessoaCPFTO obterDadosPessoaPagador(Integer empresa, PessoaVO pessoaVO,
                                               boolean validarNome, boolean validarCPF) throws Exception {
        Cliente clienteDAO;
        Pessoa pessoaDAO;
        Empresa empresaDAO;
        try {
            clienteDAO = new Cliente(con);
            pessoaDAO = new Pessoa(con);
            empresaDAO = new Empresa(con);

            if (pessoaVO == null || UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                return null;
            }

            boolean utilizarNomeResponsavelNoBoleto = empresaDAO.isUtilizarNomeResponsavelNoBoleto(empresa);
            boolean utilizarNomeResponsavelNoBoletoMaiorIdade = empresaDAO.isUtilizarNomeResponsavelNoBoletoMaiorIdade(empresa);
            PessoaVO pessoaVOCompleta = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaCPFTO pessoaCPFTO = new PessoaCPFTO();
            pessoaCPFTO.setCodigo(pessoaVOCompleta.getCodigo());
            pessoaCPFTO.setPessoa(pessoaVOCompleta.getCodigo());
            pessoaCPFTO.setNome(pessoaVOCompleta.getNome());

            String nomeResponsavel = "";
            String cpfResponsavel = "";
            String cnpjResponsavel = "";
            if ((utilizarNomeResponsavelNoBoleto && pessoaVOCompleta.getDataNasc() != null && pessoaVOCompleta.getIdade() < 18)
                    || (utilizarNomeResponsavelNoBoletoMaiorIdade && (pessoaVOCompleta.getDataNasc() != null && UteisValidacao.emptyString(pessoaVOCompleta.getCfp()) &&
                    pessoaVOCompleta.getIdade() >= 18))){

                PessoaVO pessoaResponsavel = clienteDAO.obterPessoaResponsavelCliente(null, pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (pessoaResponsavel != null && !UteisValidacao.emptyString(pessoaResponsavel.getCfp().trim())) {
                    nomeResponsavel = pessoaResponsavel.getNome();
                    cpfResponsavel = pessoaResponsavel.getCfp();
                } else if (!UteisValidacao.emptyString(pessoaVOCompleta.getNomeMae().trim()) && !UteisValidacao.emptyString(pessoaVOCompleta.getCpfMae().trim())) {
                    nomeResponsavel = pessoaVOCompleta.getNomeMae();
                    cpfResponsavel = pessoaVOCompleta.getCpfMae();
                } else if (!UteisValidacao.emptyString(pessoaVOCompleta.getNomePai().trim()) && !UteisValidacao.emptyString(pessoaVOCompleta.getCpfPai().trim())) {
                    nomeResponsavel = pessoaVOCompleta.getNomePai();
                    cpfResponsavel = pessoaVOCompleta.getCpfPai();
                } else {
                    throw new ConsistirException(String.format("Cliente %s não possui responsável cadastrado.", pessoaVOCompleta.getNome()));
                }
            } else {
                nomeResponsavel = pessoaVOCompleta.getNome();
                cpfResponsavel = pessoaVOCompleta.getCfp();
                cnpjResponsavel = pessoaVOCompleta.getCnpj();

                if (!utilizarNomeResponsavelNoBoleto && validarCPF &&
                        UteisValidacao.emptyString(cpfResponsavel) && pessoaVOCompleta.getPessoaFisica()) {
                    throw new ConsistirException("O aluno não possui CPF cadastrado e também não está habilitado a configuração na empresa para utilizar Nome/CPF do responsável no boleto.");
                }else if(UteisValidacao.emptyString(cnpjResponsavel) && pessoaVOCompleta.getPessoaJuridica()){
                    throw new ConsistirException("O aluno é uma pessoa Jurídica e não possui CNPJ cadastrado.");
                }
            }


            if ((pessoaVOCompleta.getDataNasc() != null && pessoaVOCompleta.getIdade() < 18) && pessoaVOCompleta.getPessoaJuridica() && !utilizarNomeResponsavelNoBoleto) {
                throw new Exception("Aluno menor de idade está cadastrado como pessoa jurídica. Pode ser que a idade esteja errada ou será \n" +
                        "necessário alterar para pessoa física, cadastrar CPF do responsável e marcar nas configurações da empresa: Utilizar Nome e CPF do responsável pelo aluno no boleto.");
            }
            if (UteisValidacao.emptyString(cnpjResponsavel) && pessoaVOCompleta.getPessoaJuridica()) {
                throw new ConsistirException("O aluno é uma pessoa Jurídica e não possui CNPJ cadastrado.");
            }
            if ((pessoaVOCompleta.getDataNasc() != null && pessoaVOCompleta.getIdade() >= 18) && UteisValidacao.emptyString(cpfResponsavel) && pessoaVOCompleta.getPessoaFisica()) {
                throw new Exception("Aluno não possui CPF cadastrado");
            }
            if ((pessoaVOCompleta.getDataNasc() != null && pessoaVOCompleta.getIdade() >= 18) && UteisValidacao.emptyString(cnpjResponsavel) && pessoaVOCompleta.getPessoaJuridica()) {
                throw new Exception("Aluno não possui CNPJ cadastrado");
            }

            pessoaCPFTO.setNomeResponsavel(nomeResponsavel);

            if (!UteisValidacao.emptyString(cpfResponsavel) && pessoaVOCompleta.getPessoaFisica()) {
                pessoaCPFTO.setCpfResponsavel(Formatador.removerMascara(cpfResponsavel));
            } else if (pessoaVOCompleta.getPessoaJuridica() && !UteisValidacao.emptyString(cnpjResponsavel)) {
                pessoaCPFTO.setCpfResponsavel(Formatador.removerMascara(cnpjResponsavel));
            }

            if (validarNome && UteisValidacao.emptyString(pessoaCPFTO.getNomeResponsavel())) {
                throw new Exception("Nome do responsável não informado");
            }

            if (validarCPF && UteisValidacao.emptyString(pessoaCPFTO.getCpfResponsavel().replaceAll("[^0-9]", "")) && pessoaVOCompleta.getPessoaFisica()) {
                throw new Exception("CPF do responsável não cadastrado");
            }else if(pessoaVOCompleta.getPessoaJuridica() && UteisValidacao.emptyString(pessoaCPFTO.getCpfResponsavel().replaceAll("[^0-9]", ""))){
                throw new Exception("Pessoa Jurídica não pussui CNPJ cadastrado");
            }
            return pessoaCPFTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            clienteDAO = null;
            pessoaDAO = null;
            empresaDAO = null;
        }
    }

    public List<BoletoVO> gerarBoletoPorParcela(PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO, List<MovParcelaVO> listaParcelas,
                                                UsuarioVO usuarioVO, OrigemCobrancaEnum origemCobrancaEnum, boolean cobrarMultaJuros, boolean registrarBoletoAgora) throws Exception {
        ConvenioCobranca convenioCobrancaDAO = null;
        MovParcela movParcelaDAO = null;
        Empresa empresaDAO = null;
        Pessoa pessoaDAO = null;
        BoletoOnlineServiceInterface boletoOnlineServiceInterface = null;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            movParcelaDAO = new MovParcela(con);
            empresaDAO = new Empresa(con);
            pessoaDAO = new Pessoa(con);

            if (convenioCobrancaVO == null || UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
                throw new Exception("Convênio de cobrança não informado.");
            }

            if (UteisValidacao.emptyList(listaParcelas)) {
                throw new Exception("Nenhum parcela informada.");
            }

            for (MovParcelaVO movParcelaVO : listaParcelas) {
                //validar se a parcela não está pendente em uma transação
                if (movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO)) {
                    String msg = "PARCELA ESTÁ BLOQUEADA PARA COBRANÇA - " + movParcelaVO.getCodigo();
                    Uteis.logarDebug(msg);
                    throw new Exception(msg);
                }
            }

            convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(convenioCobrancaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (pessoaVO != null && !UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO);
            }

            alterarVencimentoParcelasVencendoFimDeSemanaEFeriado(listaParcelas, movParcelaDAO, con, empresaDAO);

            List<BoletoVO> boletosGerados = new ArrayList<>();
            for (MovParcelaVO movParcelaVO : listaParcelas) {
                PessoaVO pessoaBoletoVO;
                if (pessoaVO != null && !UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                    pessoaBoletoVO = (PessoaVO) pessoaVO.getClone(true);
                } else {
                    pessoaBoletoVO = pessoaDAO.consultarPorChavePrimaria(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO);
                }
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                BoletoOnlineTO boletoOnlineTO = new BoletoOnlineTO();
                boletoOnlineTO.setTipoBoletoEnum(convenioCobrancaVO.getTipo().getTipoBoleto());

                //Caso parcela esteja vencida, o vencimento do boleto terá um acréscimo de 5 dias em relação a hoje.
                if (isParcelaVencida(movParcelaVO)) {
                    boletoOnlineTO.setDataVencimento(Uteis.somarDias(Calendario.hoje(),empresaVO.getQtdDiasVencimentoBoleto()));
                } else {
                    boletoOnlineTO.setDataVencimento(movParcelaVO.getDataVencimento());
                }

                boletoOnlineTO.setEmpresaVO(empresaVO);
                boletoOnlineTO.setPessoaVO(pessoaBoletoVO);
                boletoOnlineTO.setUsuarioVO(usuarioVO);
                boletoOnlineTO.setConvenioCobrancaVO(convenioCobrancaVO);
                boletoOnlineTO.setOrigemCobrancaEnum(origemCobrancaEnum);
                boletoOnlineTO.setCobrarMultaJuros(cobrarMultaJuros);
                List<MovParcelaVO> listaParcelaGerar = new ArrayList<>();
                listaParcelaGerar.add(movParcelaVO);
                boletoOnlineTO.setListaParcelas(listaParcelaGerar);
                boletoOnlineTO.setRegistrarBoletoAgora(registrarBoletoAgora);

                //Se for Caixa e RegistrarBoletoAgora for true, mais a frente de de verificar se já não existe um boleto gerado e não Registrado
                if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE)) {
                    boletoOnlineTO.setVerificarBoletoExistente(registrarBoletoAgora);
                }

                boletoOnlineServiceInterface = BoletoOnlineService.getBoletoOnlineService(convenioCobrancaVO.getTipo().getTipoBoleto(), empresaVO.getCodigo(), convenioCobrancaVO.getCodigo(), con, true);
                boletosGerados.add(boletoOnlineServiceInterface.criar(boletoOnlineTO));
            }
            if(!boletosGerados.isEmpty()) {
                StringBuilder boletosGeradosInfo = new StringBuilder();
                for(BoletoVO boletoVO: boletosGerados) {
                    if(boletosGeradosInfo.length() > 0) {
                        boletosGeradosInfo.append(", ");
                    }
                    boletosGeradosInfo.append(boletoVO.getCodigo());
                }
                try {
                    if(pessoaVO == null) {
                        throw new IllegalStateException("pessoaVO não pode ser null para logging");
                    }
                    LogVO log = new LogVO();
                    log.setChavePrimaria(pessoaVO.getCodigo().toString());
                    log.setPessoa(pessoaVO.getCodigo());
                    log.setNomeEntidade("BOLETO");
                    log.setOrigem(origemCobrancaEnum.getDescricao());
                    log.setNomeEntidadeDescricao("BOLETO");
                    log.setOperacao("GERAÇÃO DE BOLETO MANUAL");
                    log.setResponsavelAlteracao(usuarioVO != null ? usuarioVO.getNome() : "SISTEMA");
                    log.setUserOAMD(usuarioVO != null ? usuarioVO.getUserOamd() : "");
                    log.setNomeCampo("BOLETOS");
                    log.setDataAlteracao(Calendario.hoje());
                    log.setValorCampoAnterior("");
                    log.setValorCampoAlterado("Boletos Gerados: " + boletosGeradosInfo.toString());

                    StringBuilder infoLog = new StringBuilder();
                    infoLog.append("Boletos Gerados: ").append(boletosGeradosInfo.toString());
                    infoLog.append(" | Convênio: ").append(convenioCobrancaVO.getDescricao());
                    infoLog.append(" | CPF/CNPJ: ").append(Uteis.formatarCpfCnpj(pessoaVO.getCfp(), false));

                    log.setValorCampoAlterado(infoLog.toString());
                    getFacade().getLog().incluirSemCommit(log);
                } catch (Exception e) {
                    Uteis.logarDebug("Erro ao incluir log de geração de boleto: " + e.getMessage());
                }
            }
            return boletosGerados;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
            movParcelaDAO = null;
            empresaDAO = null;
            pessoaDAO = null;
        }
    }

    private void alterarVencimentoParcelasVencendoFimDeSemanaEFeriado(List<MovParcelaVO> listaParcelas, MovParcela movParcelaDAO, Connection con, Empresa empresaDAO) throws Exception {
        List<MovParcelaVO> listaParcelasAntesAlteracao = new ArrayList<>();
        boolean parcelaFoiAlterada = false;
        Feriado feriadoDAO = null;

        try {
            feriadoDAO = new Feriado(con);

            for (MovParcelaVO movParcelaVO : listaParcelas) {
                listaParcelasAntesAlteracao.add((MovParcelaVO) movParcelaVO.getClone(true));
                Date dataValidar = movParcelaVO.getDataVencimento();

                if (!isParcelaVencida(movParcelaVO)) {
                    EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_EMPRESA_BASICO);
                    while (isFeriado(dataValidar, empresaVO, feriadoDAO) || !Calendario.isDiaUtil(dataValidar)){
                        dataValidar = Calendario.somarDias(dataValidar, 1);
                        parcelaFoiAlterada = true;
                    }
                    movParcelaVO.setDataVencimento(dataValidar);
                }
            }

            if(parcelaFoiAlterada) {
                movParcelaDAO.alterarVencimentoListaParcelas(listaParcelas, listaParcelasAntesAlteracao, false, "GeracaoBoletoPjbank", "CaixaAbertoOuLinkPagamento", true, true);
            }
        } finally {
            feriadoDAO = null;
        }
    }

    public boolean isFeriado(Date data, EmpresaVO empresaVO, Feriado feriadoDAO) throws Exception {
        List<Date> dataLimiteFeriado = feriadoDAO.consultarDiasFeriados(data, data, empresaVO);
        if (dataLimiteFeriado.size() > 0){
            return true;
        } else {
            return false;
        }
    }

    public BoletoVO gerarBoleto(PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO, List<MovParcelaVO> listaParcelas,
                                Date dataVencimento, UsuarioVO usuarioVO, OrigemCobrancaEnum origemCobrancaEnum,
                                boolean cobrarMultaJuros, boolean registrarBoletoImpressaoAgora) throws Exception {
        return gerarBoleto(pessoaVO, convenioCobrancaVO, listaParcelas, dataVencimento, usuarioVO, origemCobrancaEnum,
                cobrarMultaJuros, null, null, false, registrarBoletoImpressaoAgora);
    }

    public BoletoVO gerarBoleto(PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO, List<MovParcelaVO> listaParcelas,
                                Date dataVencimento, UsuarioVO usuarioVO, OrigemCobrancaEnum origemCobrancaEnum,
                                boolean cobrarMultaJuros, Double descontoValorFixo, Double descontoPercentual,
                                boolean verificarBoletoExistente, boolean registrarBoletoImpressaoAgora) throws Exception {
        ConvenioCobranca convenioCobrancaDAO = null;
        MovParcela movParcelaDAO = null;
        Empresa empresaDAO = null;
        Pessoa pessoaDAO = null;
        BoletoOnlineServiceInterface boletoOnlineServiceInterface = null;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            movParcelaDAO = new MovParcela(con);
            empresaDAO = new Empresa(con);
            pessoaDAO = new Pessoa(con);

            if (pessoaVO == null || UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                throw new Exception("Pessoa não informada.");
            }

            if (convenioCobrancaVO == null || UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
                throw new Exception("Convênio de cobrança não informado.");
            }

            if (UteisValidacao.emptyList(listaParcelas)) {
                throw new Exception("Nenhum parcela informada.");
            }

            Integer empresa = listaParcelas.get(0).getEmpresa().getCodigo();
            for (MovParcelaVO movParcelaVO : listaParcelas) {
                if (!empresa.equals(movParcelaVO.getEmpresa().getCodigo())) {
                    throw new Exception("As parcelas devem ser da mesma empresa.");
                }

                //validar se a parcela não está pendente em uma transação
                if (movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO)) {
                    String msg = "PARCELA ESTÁ BLOQUEADA PARA COBRANÇA - " + movParcelaVO.getCodigo();
                    Uteis.logarDebug(msg);
                    throw new Exception(msg);
                }
            }

            convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(convenioCobrancaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            BoletoOnlineTO boletoOnlineTO = new BoletoOnlineTO();
            boletoOnlineTO.setTipoBoletoEnum(convenioCobrancaVO.getTipo().getTipoBoleto());
            boletoOnlineTO.setDataVencimento(dataVencimento);
            boletoOnlineTO.setEmpresaVO(empresaVO);
            boletoOnlineTO.setPessoaVO(pessoaVO);
            boletoOnlineTO.setUsuarioVO(usuarioVO);
            boletoOnlineTO.setConvenioCobrancaVO(convenioCobrancaVO);
            boletoOnlineTO.setOrigemCobrancaEnum(origemCobrancaEnum);
            boletoOnlineTO.setCobrarMultaJuros(cobrarMultaJuros);
            boletoOnlineTO.setListaParcelas(listaParcelas);
            boletoOnlineTO.setDescontoValorFixo(descontoValorFixo);
            boletoOnlineTO.setDescontoPercentual(descontoPercentual);
            boletoOnlineTO.setVerificarBoletoExistente(verificarBoletoExistente);
            boletoOnlineTO.setRegistrarBoletoAgora(registrarBoletoImpressaoAgora);

            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE)) {
                boletoOnlineTO.setVerificarBoletoExistente(registrarBoletoImpressaoAgora);
            }

            boletoOnlineServiceInterface = BoletoOnlineService.getBoletoOnlineService(convenioCobrancaVO.getTipo().getTipoBoleto(), empresa, convenioCobrancaVO.getCodigo(), con, true);

            cancelarBoletosCaixaAntigosParaGerarNovosViaLinkPagamento(convenioCobrancaVO, listaParcelas, usuarioVO, origemCobrancaEnum, boletoOnlineServiceInterface);

            BoletoVO boletoGerado = boletoOnlineServiceInterface.criar(boletoOnlineTO);

            if (boletoGerado != null) {
                try {
                    StringBuilder boletosGeradosInfo = new StringBuilder();
                    boletosGeradosInfo.append(boletoGerado.getCodigo());

                    LogVO log = new LogVO();
                    log.setChavePrimaria(pessoaVO.getCodigo().toString());
                    log.setPessoa(pessoaVO.getCodigo());
                    log.setOrigem(origemCobrancaEnum.getDescricao());
                    log.setNomeEntidade("BOLETO");
                    log.setNomeEntidadeDescricao("BOLETO");
                    log.setOperacao("GERAÇÃO DE BOLETO MANUAL");
                    log.setResponsavelAlteracao(usuarioVO != null ? usuarioVO.getNome() : "SISTEMA");
                    log.setNomeCampo("BOLETOS");
                    log.setDataAlteracao(Calendario.hoje());
                    log.setValorCampoAnterior("");

                    StringBuilder infoLog = new StringBuilder();
                    infoLog.append("Boletos Gerados: ").append(boletosGeradosInfo.toString());
                    infoLog.append(" | Convênio: ").append(convenioCobrancaVO.getDescricao());
                    infoLog.append(" | CPF/CNPJ: ").append(Uteis.formatarCpfCnpj(pessoaVO.getCfp(), false));

                    log.setValorCampoAlterado(infoLog.toString());
                    getFacade().getLog().incluirSemCommit(log);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            return boletoGerado;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
            movParcelaDAO = null;
            empresaDAO = null;
            pessoaDAO = null;
        }
    }

    private void cancelarBoletosCaixaAntigosParaGerarNovosViaLinkPagamento(ConvenioCobrancaVO convenioCobrancaVO, List<MovParcelaVO> listaParcelas, UsuarioVO usuarioVO,
                                                                           OrigemCobrancaEnum origemCobrancaEnum, BoletoOnlineServiceInterface boletoOnlineServiceInterface) {
        // Como todos os fluxos de gerar boleto passam por aqui, pensando em minimizar problemas, as condições abaixo atendem apenas a situação do cliente que quetionou
        // Só será executado para boleto vindo do OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO e se for TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE
        // Esse recurso se propoe a resolver um problema onde já tem um boleto gerado no ZW, mas o aluno vai no App e gera um segundo boleto.
        // Com isso a academia tem dois boletos registrados no banco e paga taxa ao banco pelos dois boletos, mas apenas um será pago.
        // Então, antes de gerar o boleto que vem pelo App, o sistema vai cancelar o boleto antigo gerado pelo ZW.
        try {
            if (origemCobrancaEnum.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO) && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE)) {
                List<BoletoVO> boletosCancelar = new ArrayList<>();
                for (MovParcelaVO movParcelaVO : listaParcelas) {
                    boletosCancelar.addAll(consultarPorMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                }

                // Retira possiveis boletos duplicados da lista
                Set<BoletoVO> boletosUnicos = new HashSet<>();
                for (BoletoVO boletoVO : boletosCancelar) {
                    boletosUnicos.add(boletoVO);
                }

                for (BoletoVO boleto : boletosUnicos) {
                    if (boleto.getSituacao().equals(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO)) {
                        try {
                            boletoOnlineServiceInterface.cancelar(boleto, usuarioVO, "Cancelamento pela geração boleto via Link Pagamento", true);
                        } catch (Exception e) {
                        }
                    }
                }
            }
        } catch (Exception e) {
        }
    }

    public void cancelarBoletos(List<BoletoVO> listaBoletosCancelar, UsuarioVO usuarioVO, String operacao) throws Exception {
        if (!UteisValidacao.emptyList(listaBoletosCancelar)) {
            for (BoletoVO boletoVO : listaBoletosCancelar) {
                try {
                    if (!boletoVO.isCancelarBoleto()) {
                        incluirBoletoHistorico(boletoVO.getCodigo(), operacao + " | Não cancelar boleto", "");
                        continue;
                    }
                    cancelarBoleto(boletoVO, usuarioVO, operacao);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    incluirBoletoHistorico(boletoVO.getCodigo(), operacao + " | ERRO", ex.getMessage());
                }
            }
        }
    }

    public void cancelarBoleto(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao) throws Exception {
        ConvenioCobranca convenioCobrancaDAO = null;
        BoletoOnlineServiceInterface boletoOnlineServiceInterface = null;
        try {
            if (!boletoVO.isPodeCancelar()) {
                throw new Exception("Boleto não pode ser cancelado");
            }

            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não informado");
            }

            convenioCobrancaDAO = new ConvenioCobranca(con);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(boletoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            boletoOnlineServiceInterface = BoletoOnlineService.getBoletoOnlineService(convenioCobrancaVO.getTipo().getTipoBoleto(), boletoVO.getEmpresaVO().getCodigo(), convenioCobrancaVO.getCodigo(), con, false);
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE)) {
                boletoVO.setConvenioCobrancaVO(convenioCobrancaVO);
            }
            boletoOnlineServiceInterface.cancelar(boletoVO, usuarioVO, operacao, false);

            if (!boletoVO.getSituacao().equals(SituacaoBoletoEnum.CANCELADO)) {
                throw new Exception("Boleto não foi cancelado.");
            }

            JSONObject json = new JSONObject();
            json.put("usuario", usuarioVO.getCodigo());
            json.put("usuario_nome", usuarioVO.getNome());
            json.put("usuario_username", usuarioVO.getUsername());
            json.put("operacao", operacao);

            incluirBoletoHistorico(boletoVO.getCodigo(), "BOLETO CANCELADO", json.toString());
        } finally {
            convenioCobrancaDAO = null;
            boletoOnlineServiceInterface = null;
        }
    }

    public String sincronizarBoleto(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao) throws Exception {
        ConvenioCobranca convenioCobrancaDAO = null;
        BoletoOnlineServiceInterface boletoOnlineServiceInterface = null;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);

            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não informado");
            }

            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(boletoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            BoletoVO boletoAnterior = (BoletoVO) boletoVO.getClone(true);

            if (convenioCobrancaVO.getTipo().getTipoBoleto() == null) {
                throw new Exception("Tipo de Boleto do convênio é inválido");
            }

            boletoOnlineServiceInterface = BoletoOnlineService.getBoletoOnlineService(convenioCobrancaVO.getTipo().getTipoBoleto(), boletoVO.getEmpresaVO().getCodigo(), convenioCobrancaVO.getCodigo(), con, false);
            boletoOnlineServiceInterface.sincronizar(boletoVO, usuarioVO, operacao);

            if (boletoAnterior.getSituacao().equals(boletoVO.getSituacao()) &&
                    boletoAnterior.getReciboPagamentoVO().getCodigo().equals(boletoVO.getReciboPagamentoVO().getCodigo())) {
                return "Boleto não sofreu alterações.";
            }

            if (boletoVO.getSituacao().equals(SituacaoBoletoEnum.PAGO) && !UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo())) {
                return "Boleto está pago. Foi realizado o pagamento no sistema, gerando o recibo do pagamento de código " +  boletoVO.getReciboPagamentoVO().getCodigo() + ".";
            }

            return "Boleto foi atualizado para a situação \"" + boletoVO.getSituacao().getDescricao() + "\".";
        } finally {
            convenioCobrancaDAO = null;
            boletoOnlineServiceInterface = null;
        }
    }

    public String processarWebhook(TipoBoletoEnum tipoBoletoEnum, Integer boleto,
                                   Integer convenioCobranca, String dados, String boletoExterno) throws Exception {
        ConvenioCobranca convenioCobrancaDAO = null;
        BoletoOnlineServiceInterface boletoOnlineServiceInterface = null;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);

            BoletoVO boletoVO = null;
            ConvenioCobrancaVO convenioCobrancaVO = null;
            if (!UteisValidacao.emptyNumber(boleto)) {
                boletoVO = consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (boletoVO == null) {
                    throw new Exception("Boleto não encontrado com o código " + boleto);
                }
                convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(boletoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if(!UteisValidacao.emptyString(boletoExterno)){
                boletoVO = consultarPorIdexternoTipo(boletoExterno, tipoBoletoEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (boletoVO == null) {
                    throw new Exception("Boleto não encontrado com o idExterno " + boletoExterno);
                }
                convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(boletoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (convenioCobrancaVO == null && !UteisValidacao.emptyNumber(convenioCobranca)) {
                convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(convenioCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (convenioCobrancaVO == null) {
                throw new Exception("Convênio de cobrança não encontrado");
            }
            if (boletoVO == null) {
                throw new Exception("Boleto não encontrado");
            }

            BoletoVO boletoAnterior = (BoletoVO) boletoVO.getClone(true);

            boletoOnlineServiceInterface = BoletoOnlineService.getBoletoOnlineService(tipoBoletoEnum, boletoVO.getEmpresaVO().getCodigo(), convenioCobrancaVO.getCodigo(), con, false);
            boletoOnlineServiceInterface.processarWebhook(boletoVO, dados);

            if (boletoAnterior.getSituacao().equals(boletoVO.getSituacao())) {
                return "Boleto não sofreu alterações.";
            }

            if (boletoVO.getSituacao().equals(SituacaoBoletoEnum.PAGO)) {
                return "Boleto está pago. Foi realizado o pagamento no sistema, gerando o recibo do pagamento.";
            }

            return "Boleto foi atualizado para a situação \"" + boletoVO.getSituacao().getDescricao() + "\".";
        } finally {
            convenioCobrancaDAO = null;
            boletoOnlineServiceInterface = null;
        }
    }

    public String processarWebhookBancoBrasil(String dados) throws Exception {

        BoletoOnlineServiceInterface boletoOnlineServiceInterface = null;

        try {
            JSONArray jsonRetornoArray = new JSONArray(dados);
            List<BoletoVO> boletos = new ArrayList<>();
            for (int i = 0; i < jsonRetornoArray.length(); i++) {
                JSONObject jsonRetorno = jsonRetornoArray.getJSONObject(i);
                String nossoNumero = jsonRetorno.optString("id");
                boletos = consultarPorNossoNumero(nossoNumero, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyList(boletos)) {
                    for (BoletoVO boletoVO: boletos) {
                        BoletoVO boletoAnterior = (BoletoVO) boletoVO.getClone(true);

                        boletoOnlineServiceInterface = BoletoOnlineService.getBoletoOnlineService(TipoBoletoEnum.BANCO_BRASIL, boletoVO.getEmpresaVO().getCodigo(), boletoVO.getConvenioCobrancaVO().getCodigo(), con, false);
                        boletoOnlineServiceInterface.processarWebhook(boletoAnterior, jsonRetorno.toString());

                        if (boletoAnterior.getSituacao().equals(boletoVO.getSituacao())) {
                            return "Boleto não sofreu alterações.";
                        }

                        if (boletoVO.getSituacao().equals(SituacaoBoletoEnum.PAGO)) {
                            return "Boleto está pago. Foi realizado o pagamento no sistema, gerando o recibo do pagamento.";
                        }

                        return "Boleto foi atualizado para a situação \"" + boletoVO.getSituacao().getDescricao() + "\".";
                    }
                }
            }
        } finally {
            boletoOnlineServiceInterface = null;
        }
        return "";
    }

    public void enviarEmailBoleto(String chave, BoletoVO obj, String[] emailEnviar,
                                  boolean enviarLinkBoleto, boolean enviarBoletoEmAnexo) throws Exception {
        Empresa empresaDAO;
        Pessoa pessoaDAO;
        try {
            empresaDAO = new Empresa(this.con);
            pessoaDAO = new Pessoa(this.con);

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = obterConfiguracaoSistemaCRMVO();

            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo("BOLETO", configuracaoSistemaCRMVO);

            // Se o boleto da Caixa estiver Gerado e Não Registrado, vai registrar aqui para enviar no e-mail
            if (obj.getTipo().equals(TipoBoletoEnum.CAIXA) && UteisValidacao.emptyString(obj.getLinhaDigitavel())) {
                obj = registrarBoletoGerado(obj);
            }

            BoletoEmailTO boletoEmailTO = new BoletoEmailTO();
            boletoEmailTO.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
            boletoEmailTO.setPessoaVO(pessoaDAO.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            boletoEmailTO.setLinhaDigitavel(obj.getLinhaDigitavel());
            boletoEmailTO.setValor(obj.getValor());
            boletoEmailTO.setDataVencimento(obj.getDataVencimento());

            if (enviarLinkBoleto) {
                boletoEmailTO.setLinkBoleto(obj.getLinkBoleto());
            }

            if (enviarBoletoEmAnexo) {
                boletoEmailTO.setNomeArquivoBoleto("Boleto-" + obj.getCodigo() + ".pdf");

                File file = new File(PropsService.getPropertyValue(PropsService.diretorioArquivos) + File.separator + "boleto_email_temp" + File.separator + chave + "-" + boletoEmailTO.getNomeArquivoBoleto());
                byte[] arquivo = ExecuteRequestHttpService.obterByteFromUrl(obj.getLinkBoleto(), null, null);
                FileUtilities.saveToFile(arquivo, file.getPath());
                boletoEmailTO.setUrlFileArquivoBoleto(file.getAbsolutePath());

                uteisEmail.addAnexo(boletoEmailTO.getNomeArquivoBoleto(), file);
            }

            gerarHTMLModeloPadraoBoleto(chave, boletoEmailTO);

            uteisEmail.enviarEmailN(emailEnviar, boletoEmailTO.getHtmlEmail(), "Boleto gerado: " + boletoEmailTO.getEmpresaVO().getNome(), boletoEmailTO.getEmpresaVO().getNome());
        } finally {
            empresaDAO = null;
            pessoaDAO = null;
        }
    }

    private BoletoVO registrarBoletoGerado(BoletoVO obj) throws Exception {
        UsuarioVO usuarioVO = getUsuarioLogadoControleAcesso();

        //Lista de Parcela Vazia, mas lista MovParcela preenchida, pegar parcelas da Lista de MovParcela
        if (UteisValidacao.emptyList(obj.getListaParcelas()) && !UteisValidacao.emptyList(obj.getListaBoletoMovParcela())) {
            for (BoletoMovParcelaVO boletoMovParcelaVO : obj.getListaBoletoMovParcela()) {
                obj.getListaParcelas().add(boletoMovParcelaVO.getMovParcelaVO());
            }
        }

        if (!UteisValidacao.emptyList(obj.getListaParcelas())) {
            if (obj.getListaParcelas().size() == 1) {
                List<BoletoVO> listaBoletosRegistrados = gerarBoletoPorParcela(obj.getPessoaVO(), obj.getConvenioCobrancaVO(), obj.getListaParcelas(),
                        usuarioVO, obj.getOrigem(), false, true);
                if (!UteisValidacao.emptyList(listaBoletosRegistrados)) {
                    obj = listaBoletosRegistrados.get(0);
                }
            } else {
                obj = getFacade().getBoleto().gerarBoleto(obj.getPessoaVO(), obj.getConvenioCobrancaVO(), obj.getListaParcelas(), obj.getDataVencimento(),
                        usuarioVO, OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO, false, true);
            }
        }
        return obj;
    }

    public void gerarHTMLModeloPadraoBoleto(String chave, BoletoEmailTO boletoEmailTO) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailBoletoOnline.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath(), "UTF-8");

        String empresaNome = boletoEmailTO.getEmpresaVO().getNome().toUpperCase();
        String empresaUrlLogo = boletoEmailTO.getUrlLogoEmpresa(chave);
        String empresaEndereco = boletoEmailTO.getEmpresaVO().getEndereco();
        String empresaTelefone = boletoEmailTO.getEmpresaVO().getTelComercial1();
        String empresaEmail = boletoEmailTO.getEmpresaVO().getEmail();
        String mesReferencia = boletoEmailTO.getMesReferencia();
        String dataVencimento = boletoEmailTO.getDataVencimentoApresentar();
        String valorBoleto = boletoEmailTO.getValorApresentar();
        String nomeAluno = boletoEmailTO.getPessoaVO().getNome();
        String linhaDigitavel = boletoEmailTO.getLinhaDigitavel();
        String linkBoleto = boletoEmailTO.getLinkBoleto();

        boolean enviarBoletoEmAnexo = !UteisValidacao.emptyString(boletoEmailTO.getUrlFileArquivoBoleto());
        boolean enviarLinkBoleto = !UteisValidacao.emptyString(linkBoleto);

        String aux = texto.toString()
                .replaceAll("#ACADEMIA_NOME#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaNome))
                .replaceAll("#ACADEMIA_URL_LOGO#", empresaUrlLogo)
                .replaceAll("#ACADEMIA_ENDERECO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEndereco))
                .replaceAll("#ACADEMIA_TELEFONE#", empresaTelefone)
                .replaceAll("#ACADEMIA_EMAIL#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEmail))
                .replaceAll("#NOME_ALUNO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeAluno))
                .replaceAll("#MES_REFERENCIA#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(mesReferencia))
                .replaceAll("#DATA_VENCIMENTO#", dataVencimento)
                .replaceAll("#LINHA_DIGITAVEL#", linhaDigitavel)
                .replaceAll("#VALOR_BOLETO#", valorBoleto)
                .replaceAll("#LINK_BOLETO#", linkBoleto)
                .replaceAll("#LINK#", enviarLinkBoleto ? "block" : "none")
                .replaceAll("#ANEXO#", enviarBoletoEmAnexo ? "block" : "none");
        boletoEmailTO.setHtmlEmail(aux);
    }

    public void enviarEmailBoletoLink(String chave, String link, Integer empresa, Integer pessoa,
                                      String[] emailEnviar) throws Exception {
        Empresa empresaDAO;
        Pessoa pessoaDAO;
        try {
            empresaDAO = new Empresa(this.con);
            pessoaDAO = new Pessoa(this.con);

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = obterConfiguracaoSistemaCRMVO();

            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo("BOLETO", configuracaoSistemaCRMVO);

            BoletoEmailTO boletoEmailTO = new BoletoEmailTO();
            boletoEmailTO.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
            boletoEmailTO.setPessoaVO(pessoaDAO.consultarPorChavePrimaria(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            boletoEmailTO.setLinkBoleto(link);

            gerarHTMLModeloPadraoBoletoLinkVariosBoletos(chave, boletoEmailTO);

            uteisEmail.enviarEmailN(emailEnviar, boletoEmailTO.getHtmlEmail(), "Boleto gerado: " + boletoEmailTO.getEmpresaVO().getNome(), boletoEmailTO.getEmpresaVO().getNome());
        } finally {
            empresaDAO = null;
            pessoaDAO = null;
        }
    }

    private void gerarHTMLModeloPadraoBoletoLinkVariosBoletos(String chave, BoletoEmailTO boletoEmailTO) throws Exception {

        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailBoletoOnlineSomenteLink.txt").toURI());

        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());

        String empresaNome = boletoEmailTO.getEmpresaVO().getNome().toUpperCase();
        String empresaUrlLogo = boletoEmailTO.getUrlLogoEmpresa(chave);
        String empresaEndereco = boletoEmailTO.getEmpresaVO().getEndereco();
        String empresaTelefone = boletoEmailTO.getEmpresaVO().getTelComercial1();
        String empresaEmail = boletoEmailTO.getEmpresaVO().getEmail();
        String nomeAluno = boletoEmailTO.getPessoaVO().getNome();
        String linkBoleto = boletoEmailTO.getLinkBoleto();

        String aux = texto.toString()
                .replaceAll("#ACADEMIA_NOME#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaNome))
                .replaceAll("#ACADEMIA_URL_LOGO#", empresaUrlLogo)
                .replaceAll("#ACADEMIA_ENDERECO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEndereco))
                .replaceAll("#ACADEMIA_TELEFONE#", empresaTelefone)
                .replaceAll("#ACADEMIA_EMAIL#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEmail))
                .replaceAll("#NOME_ALUNO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeAluno))
                .replaceAll("#LINK_BOLETO#", linkBoleto);
        boletoEmailTO.setHtmlEmail(aux);
    }

    private ConfiguracaoSistemaCRMVO obterConfiguracaoSistemaCRMVO() throws Exception {
//        ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(con);
//        ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//        configuracaoSistemaCRMDAO = null;
//
//        if (UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer())) {
//            throw new Exception("Não foi possível enviar o e-mail. Verifique as configurações de e-mail no CRM!");
//        }
//        return configuracaoSistemaCRMVO;
        return SuperControle.getConfiguracaoSMTPNoReply();
    }

    public List<BoletoVO> excluirBoletoMovProduto(EstornoMovProdutoVO estornoMovProdutoVO) throws Exception {
        try {
            List<BoletoVO> listaBoletosCancelar = consultarPorMovProduto(estornoMovProdutoVO.getMovProdutoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (BoletoVO boletoVO : listaBoletosCancelar) {
                boolean contaCorrente = estornoMovProdutoVO.getMovProdutoVO().getDescricao().contains(MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO_BOLETO);
                String operacao = ("Estorno MovProduto " + estornoMovProdutoVO.getMovProdutoVO().getCodigo());
                JSONObject jsonBase = gerarBaseJSON(estornoMovProdutoVO.getResponsavelEstorno(), operacao);
                if (!contaCorrente) {
                    alterarSituacao(boletoVO, SituacaoBoletoEnum.ESTORNADO);
                }
                removerRelacionamentoMovParcela(boletoVO.getCodigo(), operacao, jsonBase);
                boletoVO.setCancelarBoleto(!contaCorrente);
            }
            return listaBoletosCancelar;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public List<BoletoVO> excluirBoletoContratoOrigemEstornoContrato(ContratoVO contratoVO, UsuarioVO usuarioVO) throws Exception {
        try {
            List<BoletoVO> listaBoletosCancelar = consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (BoletoVO boletoVO : listaBoletosCancelar) {
                String operacao = ("Estorno do Contrato " + contratoVO.getCodigo());
                JSONObject jsonBase = gerarBaseJSON(usuarioVO, operacao);
                removerRelacionamentoMovParcela(boletoVO.getCodigo(), operacao, jsonBase);
            }
            return listaBoletosCancelar;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public List<BoletoVO> excluirBoletoContratoOrigemCancelamentoContrato(ContratoVO contratoVO, UsuarioVO usuarioVO) throws Exception {
        try {
            List<BoletoVO> listaBoletosContrato = consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<BoletoVO> listaBoletosCancelar = new ArrayList<>();
            //No caso do cancelamento do contrato não alterar os boletos pagos para estornado, deixar como pago mesmo e cancelar os que não estão pagos
            if (!UteisValidacao.emptyList(listaBoletosContrato)) {
                for (BoletoVO boletoVO : listaBoletosContrato) {
                    //não adicionar boletos pagos na lista para não alterar situação para estornado
                    if (!boletoVO.getSituacao().equals(SituacaoBoletoEnum.PAGO)) {
                        listaBoletosCancelar.add(boletoVO);
                    }
                }
            }
            for (BoletoVO boletoVO : listaBoletosCancelar) {
                String operacao = ("Cancelamento do Contrato " + contratoVO.getCodigo());
                JSONObject jsonBase = gerarBaseJSON(usuarioVO, operacao);
                removerRelacionamentoMovParcela(boletoVO.getCodigo(), operacao, jsonBase);
            }
            return listaBoletosCancelar;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public List<BoletoVO> excluirBoletoMovParcela(MovParcelaVO movParcelaVO, UsuarioVO usuarioVO) throws Exception {
        try {
            List<BoletoVO> listaBoletosCancelar = consultarPorMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (BoletoVO boletoVO : listaBoletosCancelar) {
                String operacao = ("Estorno MovParcela " + movParcelaVO.getCodigo());
                JSONObject jsonBase = gerarBaseJSON(usuarioVO, operacao);
                alterarSituacao(boletoVO, SituacaoBoletoEnum.ESTORNADO);
                removerRelacionamentoMovParcela(boletoVO.getCodigo(), operacao, jsonBase);
            }
            return listaBoletosCancelar;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public void estornarReciboPagamento(Integer reciboPagamento, UsuarioVO usuarioVO, String operacao) throws Exception {
        if (UteisValidacao.emptyNumber(reciboPagamento)) {
            return;
        }
        List<BoletoVO> lista = consultarPorReciboPagamento(reciboPagamento, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (BoletoVO boletoVO : lista) {
            removerRelacionamentoPagamentoRecibo(boletoVO, usuarioVO, operacao);
        }
    }

    private void removerRelacionamentoPagamentoRecibo(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao) throws Exception {
        JSONObject jsonBase = gerarBaseJSON(usuarioVO, operacao);
        jsonBase.put("movPagamento", boletoVO.getMovPagamentoVO().getCodigo());
        jsonBase.put("reciboPagamento", boletoVO.getReciboPagamentoVO().getCodigo());
        boletoVO.setReciboPagamentoVO(null);
        boletoVO.setMovPagamentoVO(null);
        alterarMovPagamentoReciboPagamento(boletoVO);
        alterarSituacao(boletoVO, SituacaoBoletoEnum.ESTORNADO);
        incluirBoletoHistorico(boletoVO, "removerRelacionamentoPagamentoRecibo | " + operacao, jsonBase.toString());
    }

    public void estornarMovParcela(Integer movParcela, UsuarioVO usuarioVO, String operacao) throws Exception {
        if (UteisValidacao.emptyNumber(movParcela)) {
            return;
        }
        List<BoletoVO> lista = consultarPorMovParcela(movParcela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (BoletoVO boletoVO : lista) {
            JSONObject jsonBase = gerarBaseJSON(usuarioVO, operacao);
            removerRelacionamentoMovParcela(boletoVO.getCodigo(), operacao, jsonBase);
            removerRelacionamentoPagamentoRecibo(boletoVO, usuarioVO, operacao);
        }
    }

    public JSONObject gerarBaseJSON(UsuarioVO usuarioVO, String operacao) {
        JSONObject jsonEstorno = new JSONObject();
        jsonEstorno.put("operacao", operacao);
        jsonEstorno.put("data", Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
        if (usuarioVO != null) {
            jsonEstorno.put("usuario_cod", usuarioVO.getCodigo());
            jsonEstorno.put("usuario_nome", usuarioVO.getNome());
        }
        return jsonEstorno;
    }

    public JSONObject gerarBaseJSONProcesso(UsuarioVO usuarioVO, String operacao) {
        JSONObject jsonEstorno = new JSONObject();
        jsonEstorno.put("operacao", operacao);
        jsonEstorno.put("data", Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
        if (usuarioVO != null) {
            jsonEstorno.put("usuario_cod", usuarioVO.getCodigo());
            jsonEstorno.put("usuario_nome", usuarioVO.getNome());
        }
        jsonEstorno.put("origem", "Processo");
        return jsonEstorno;
    }

    public void removerRelacionamentoMovParcela(Integer boleto, String operacao, JSONObject jsonEstorno) throws Exception {
        BoletoMovParcela boletoMovParcelaDAO;
        try {
            boletoMovParcelaDAO = new BoletoMovParcela(this.con);

            List<BoletoMovParcelaVO> lista = boletoMovParcelaDAO.consultarPorCodigoBoleto(boleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (BoletoMovParcelaVO boletoMovParcelaVO : lista) {
                JSONObject jsonEstornoParcela = new JSONObject(jsonEstorno);
                jsonEstornoParcela.put("movparcela_codigo", boletoMovParcelaVO.getMovParcelaVO().getCodigo());
                jsonEstornoParcela.put("movparcela_descricao", boletoMovParcelaVO.getMovParcelaVO().getDescricao());
                boletoMovParcelaVO.setMovParcelaVO(null);
                boletoMovParcelaVO.setJsonEstorno(jsonEstornoParcela.toString());
                boletoMovParcelaDAO.alterar(boletoMovParcelaVO);
                incluirBoletoHistorico(boleto, operacao, jsonEstornoParcela.toString());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoMovParcelaDAO = null;
        }
    }

    private StringBuilder obterSQLParcelaPendente(Integer movParcela, boolean desconsiderarPagos, boolean consultaExists) {
        StringBuilder sql = new StringBuilder();
        if (consultaExists) {
            sql.append("select exists( \n");
            sql.append("select \n");
            sql.append("b.codigo \n");
        } else {
            sql.append("select \n");
            sql.append("b.* \n");
        }
        sql.append("from boleto b \n");
        sql.append("inner join boletomovparcela bm on bm.boleto = b.codigo \n");
        sql.append("where bm.movparcela = ").append(movParcela).append(" \n");

        //Para cenários onde o boleto já foi cancelado algum dia, aluna já tinha pago o boleto antes do cancelamento e a parcela ainda está em aberto
        // No momento da baixa do boleto do nosso lado foi gerado CC pois parcela não estava em aberto naquele momento por algum motivo.
        sql.append("and b.jsonestorno = '' ").append(" \n");

        SituacaoBoletoEnum[] situacaoPendente;
        if (desconsiderarPagos){
            situacaoPendente = new SituacaoBoletoEnum[]{SituacaoBoletoEnum.GERADO,
                    SituacaoBoletoEnum.AGUARDANDO_REGISTRO, SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO};
        } else {
            situacaoPendente = new SituacaoBoletoEnum[]{SituacaoBoletoEnum.PAGO, SituacaoBoletoEnum.GERADO,
                    SituacaoBoletoEnum.AGUARDANDO_REGISTRO, SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO};
        }

        String situacoes = "";
        for (SituacaoBoletoEnum situacaoBoletoEnum : situacaoPendente) {
            situacoes += ("," + situacaoBoletoEnum.getCodigo());
        }

        sql.append("and b.situacao in (").append(situacoes.replaceFirst(",", "")).append(") \n");
        if (consultaExists) {
            sql.append(") as existe");
        }
        return sql;
    }

    public boolean existeBoletoPendentePorMovParcela(Integer movParcela, boolean desconsiderarPagos) throws Exception {
        StringBuilder sql = obterSQLParcelaPendente(movParcela, desconsiderarPagos, true);
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getBoolean(1);
                }
                return false;
            }
        }
    }

    public List<BoletoVO> obterBoletosPendentePorMovParcela(Integer movParcela, boolean desconsiderarPagos,
                                                            int nivelMontarDados) throws Exception {
        StringBuilder sql = obterSQLParcelaPendente(movParcela, desconsiderarPagos, false);
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public String processarBoletoPJBank(String json, String origem) throws Exception {
        try {
            WebhookPJBankJSON webhookPJBankJSON = new WebhookPJBankJSON(new JSONObject(json));
            if (UteisValidacao.emptyString(webhookPJBankJSON.getId_unico())) {
                throw new Exception("Registro não encontrado");
            }

            BoletoVO boletoVO = consultarPorIdexternoTipo(webhookPJBankJSON.getId_unico(), TipoBoletoEnum.PJ_BANK, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (boletoVO == null &&
                    !UteisValidacao.emptyString(webhookPJBankJSON.getId_unico_original())) {
                Uteis.logarDebug("Consultar boleto PJBank pelo Id_unico_original: " + webhookPJBankJSON.getId_unico_original() + " | Id_unico: " + webhookPJBankJSON.getId_unico());
                boletoVO = consultarPorIdexternoTipo(webhookPJBankJSON.getId_unico_original(), TipoBoletoEnum.PJ_BANK, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (boletoVO != null) {
                    Uteis.logarDebug("Encontrei pelo Id_unico_original: " + webhookPJBankJSON.getId_unico_original() + " | Id_unico: " + webhookPJBankJSON.getId_unico());
                    String nomePagadorBoleto = boletoVO.getNomePagadorBoleto();
                    if (UteisValidacao.emptyString(nomePagadorBoleto)) {
                        Pessoa pessoaDAO = new Pessoa(this.con);
                        nomePagadorBoleto = pessoaDAO.consultarPorChavePrimaria(boletoVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA).getNome();
                        pessoaDAO = null;
                    }
                    if (!nomePagadorBoleto.equalsIgnoreCase(webhookPJBankJSON.getPagador())) {
                        Uteis.logarDebug("Encontrei pelo Id_unico_original | Pagador diferente | Pagador Boleto: " + webhookPJBankJSON.getPagador() + " | Boleto: " + boletoVO.getNomePagadorBoleto());
                        boletoVO = null;
                    }
                }
            }

            if (boletoVO == null) {
                throw new Exception("Boleto não encontrado com o idExterno " + webhookPJBankJSON.getId_unico());
            }

            //incluir histórico
            incluirBoletoHistorico(boletoVO, "processarBoletoPJBank-" + origem, json);

            return processarWebhook(TipoBoletoEnum.PJ_BANK, boletoVO.getCodigo(), boletoVO.getConvenioCobrancaVO().getCodigo(), json, null);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String processarBoletoAsaas(JSONObject json, String origem) throws Exception {
        try {
            CobrancaAsaasRetornoDTO cobrancaAsaasRetornoDTO = new CobrancaAsaasRetornoDTO(json);
            if (UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getId())) {
                throw new Exception("Id não encontrado");
            }

            BoletoVO boletoVO = consultarPorIdexternoTipo(cobrancaAsaasRetornoDTO.getId(), TipoBoletoEnum.ASAAS, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (boletoVO == null) {
                throw new Exception("Boleto não encontrado com o idExterno " + cobrancaAsaasRetornoDTO.getId());
            }

            //incluir histórico
            incluirBoletoHistorico(boletoVO, "processarBoletoPJBank-" + origem, json.toString());

            return processarWebhook(TipoBoletoEnum.ASAAS, boletoVO.getCodigo(), boletoVO.getConvenioCobrancaVO().getCodigo(), json.toString(), null);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public List<BoletoVO> consultarPorCodigos(String codigos, Integer convenioCobranca, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("* \n");
        sql.append("FROM boleto \n");
        sql.append("WHERE codigo in (").append(codigos).append(") \n");
        if (!UteisValidacao.emptyNumber(convenioCobranca)) {
            sql.append("and conveniocobranca = ").append(convenioCobranca).append(" \n");
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoVO> consultarPorIdentificador(String identificadores, Integer convenioCobranca, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("* \n");
        sql.append("FROM boleto \n");
        sql.append("WHERE identificador in (").append(identificadores).append(") \n");
        if (!UteisValidacao.emptyNumber(convenioCobranca)) {
            sql.append("and conveniocobranca = ").append(convenioCobranca).append(" \n");
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoVO> consultarPorIdexterno(String idsExterno, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM boleto WHERE idexterno in (" + idsExterno + ")";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoVO> consultarPorNossoNumero(String nossoNumero, Integer empresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM boleto WHERE nossoNumero ilike '" + nossoNumero + "'";
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql = sql + " AND empresa = " + empresa;
        }
        sql = sql + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<BoletoVO> consultarPendentesCancelamento(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO,
                                                         int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("b.* \n");
        sql.append("from boleto b \n");
        sql.append("where b.situacao = ").append(SituacaoBoletoEnum.CANCELAMENTO_PENDENTE.getCodigo()).append(" \n");
        sql.append("and (select (count(*) <= 10) as qtd from boletohistorico where operacao = 'TENTATIVA_CANCELAMENTO_PENDENTE' and boleto = b.codigo) \n");
        if (convenioCobrancaVO != null && !UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
            sql.append("and b.conveniocobranca = ").append(convenioCobrancaVO.getCodigo()).append(" \n");
        }
        if (empresaVO != null && !UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
            sql.append("and b.empresa = ").append(empresaVO.getCodigo()).append(" \n");
        }
        sql.append("order by b.codigo \n");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public void forcarGerarPagamentoBoleto(Integer boleto, UsuarioVO usuarioVO) throws Exception {
        BoletoVO boletoVO = null;
        Boleto boletoDAO = null;
        Usuario usuarioDAO = null;
        PJBankService service = null;
        try {
            boletoDAO = new Boleto(this.getCon());
            usuarioDAO = new Usuario(this.getCon());

            boletoVO = boletoDAO.consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_TODOS);
            if (boletoVO == null) {
                throw new Exception("Boleto não encontrado");
            }

            if (!UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo()) ||
                    !UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo())) {
                throw new Exception("Boleto já está pago");
            }

            if (usuarioVO == null) {
                usuarioVO = usuarioDAO.getUsuarioRecorrencia();
            }

            service = new PJBankService(this.con, boletoVO.getEmpresaVO().getCodigo(), boletoVO.getConvenioCobrancaVO().getCodigo());


            boletoDAO.incluirBoletoHistorico(boletoVO, "forcarGerarPagamentoBoleto", "");

            //obter situacao conforme a os dados recebidos
            boletoVO.setSituacao(SituacaoBoletoEnum.PAGO);
            boletoVO.setValorPago(boletoVO.getValor());
            boletoVO.setValorLiquido(boletoVO.getValorTarifa());
            boletoVO.setValorTarifa(boletoVO.getValorTarifa());
            boletoVO.setDataPagamento(boletoVO.getDataVencimento());
            boletoVO.setDataCredito(Calendario.somarDias(boletoVO.getDataVencimento(), 1));
            service.gerarPagamentoBoleto(boletoVO, usuarioVO, true, false);
            //atualizar dados do boleto
            boletoDAO.alterar(boletoVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            if (boletoDAO != null) {
                boletoDAO.incluirBoletoHistorico(boletoVO, "forcarGerarPagamentoBoleto | ERRO", ex.getMessage());
            }
            throw ex;
        } finally {
            boletoDAO = null;
            usuarioDAO = null;
        }
    }

    public String obterDetalheBoletoPJBank(Integer boleto) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(this.con);

            BoletoVO boletoVO = consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyString(boletoVO.getIdExterno())) {
                throw new Exception("Boleto não tem idExterno (id_unico)");
            }
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(boletoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ConsultasBoletoManager consultasBoletoManager = new ConsultasBoletoManager(convenioCobrancaVO.getCredencialPJBank(), convenioCobrancaVO.getChavePJBank(), convenioCobrancaVO);
            return consultasBoletoManager.infoBoleto(boletoVO.getIdExterno());
        } finally {
            convenioCobrancaDAO = null;
        }
    }

    @Override
    public List<BoletoVO> consultarParaProcesso(String sql) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        try (ResultSet rs = criarConsulta(sql, con)) {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            List<BoletoVO> boletos = new ArrayList<>();
            while (rs.next()) {
                BoletoVO boleto = new BoletoVO();
                boleto.setCodigo(rs.getInt("codigo"));
                boleto.setIdExterno(rs.getString("idexterno"));
                boleto.setConvenioCobrancaVO(convenioCobrancaDAO.consultarPorChavePrimaria(rs.getInt("conveniocobranca"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                boleto.setNumeroInterno(rs.getString("numerointerno"));

                EmpresaVO empresaVO = new EmpresaVO();
                empresaVO.setCodigo(rs.getInt("empresa"));
                boleto.setEmpresaVO(empresaVO);
                boletos.add(boleto);
            }
            return boletos;
        } catch (Exception ex) {

        } finally {
            convenioCobrancaDAO = null;
        }
        return new ArrayList<>();
    }

    public boolean isParcelaVencida(MovParcelaVO movParcelaVO) throws Exception {
        if (Calendario.menor(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
            return true;
        }
        return false;
    }

    public List<BoletoVO> consultarPorDataRegistro(Date dataInicio, Date dataFim, ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO) throws Exception {
        List<BoletoVO> listaRetornar = new ArrayList<>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cli.codigo AS codigoCliente, b.* FROM boleto b \n");
        sql.append("LEFT JOIN cliente cli ON cli.pessoa = b.pessoa \n");
        sql.append("WHERE b.dataregistro::date >= '" + sdf.format(dataInicio) + "' AND b.dataregistro::date <= '" + sdf.format(dataFim) + "' \n");
        sql.append("AND b.conveniocobranca = " + convenioCobrancaVO.getCodigo() + " AND b.empresa = " + empresaVO.getCodigo() + ";");

        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                listaRetornar = montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }
        }

        return listaRetornar;
    }

    public List<BoletoVO> consultarPorCodigos(List<Integer> codigos, int nivelMontarDados) throws Exception {
        if (UteisValidacao.emptyList(codigos)) {
            throw new Exception("Nenhum codigo de boleto informado");
        }

        StringBuilder codStr = new StringBuilder();
        for (Integer co : codigos) {
            codStr.append(",").append(co);
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("b.* \n");
        sql.append("from boleto b \n");
        sql.append("where b.codigo in (").append(codStr.toString().replaceFirst(",", "")).append(")");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }
}
