package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.financeiro.MovContaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.RelatorioFechamentoCaixaTO.TotaisFormaPagamento;
import negocio.comuns.financeiro.RelatorioFechamentoCaixaTO.TotaisRecebidos;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.consulta.ConsultaUtil;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.interfaces.financeiro.BloqueioCaixaInterfaceFacade;
import negocio.interfaces.financeiro.MovContaInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class MovConta extends SuperEntidade implements MovContaInterfaceFacade {

    public MovConta() throws Exception {
        super();
    }

    public MovConta(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento, ComportamentoConta comportamento) throws SQLException, Exception {
        incluir(obj, codigoCaixa, validarFormaPagamento, comportamento, false);
    }

    public void incluir(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento, ComportamentoConta comportamento, boolean origemMovAutomaticaConciliacao) throws SQLException, Exception {
        try {
            if (!validarFormaPagamento) {
                con.setAutoCommit(false);
            }


            incluirSemCommit(obj, codigoCaixa, validarFormaPagamento, comportamento,true, origemMovAutomaticaConciliacao);
            if (!validarFormaPagamento) {
                con.commit();
            }
        } catch (Exception e) {
            obj.setNovoObj(true);
            if (!validarFormaPagamento) {
                con.rollback();
            }
            throw e;
        } finally {
            if (!validarFormaPagamento) {
                con.setAutoCommit(true);
            }
        }
    }

    /**
     * <AUTHOR> Alcides
     * 13/12/2012
     */
    public void incluirSemCommit(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento, ComportamentoConta comportamento) throws Exception{
        incluirSemCommit(obj, codigoCaixa, validarFormaPagamento, comportamento, true);
    }

    public void incluirSemCommit(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento, ComportamentoConta comportamento, boolean validarBloqueio) throws Exception{
        incluirSemCommit(obj, codigoCaixa, validarFormaPagamento, comportamento, validarBloqueio, false);
    }

    public void incluirSemCommit(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento, ComportamentoConta comportamento, boolean validarBloqueio, boolean origemMovAutomaticaConciliacao) throws Exception{
        Lote lote = new Lote(con);
        if(obj.getTemLote() && obj.getLote() != null && UteisValidacao.emptyNumber(obj.getLote().getCodigo())){

            boolean atualizarDataCompensacao = comportamento == null ? false : comportamento.equals(ComportamentoConta.BANCO);
            lote.incluirSemCommit(obj.getLote(), atualizarDataCompensacao);

        }else if(obj.getLote() != null && !((obj.getCheques() == null || obj.getCheques().isEmpty())
                && (obj.getCartoes() == null || obj.getCartoes().isEmpty()))
                && !UteisValidacao.emptyNumber(obj.getLote().getCodigo())){
            obj.getLote().setTipoOperacao(obj.getTipoOperacaoLancamento());
            lote.alterarSemCommit(obj.getLote(), false);
        }
        lote = null;
        MovContaVO.validarDados(obj);
        if(validarBloqueio && !origemMovAutomaticaConciliacao){
            validarDataBloqueio(obj, false);
        }

        StringBuilder sql = new StringBuilder();
        sql.append("insert into movconta (descricao, ");
        sql.append("pessoa, usuario, ");
        sql.append("empresa,conta, observacoes, ");
        sql.append("valor, dataquitacao, ");
        sql.append("datalancamento, tipooperacao, datavencimento, datacompetencia, agendamentofinanceiro," +
                " nrparcela, lote, contaorigem, autorizacaocartao, apresentarnocaixa, movproduto, app, dataUltimaAlteracao," +
                " codigobarras, compraEstoque, numerodocumento, taxaAntecipacao,identificadorOrigem, valorpago, valororiginalalterado," +
                " valororiginalantesdaconciliacao, identificadorOrigemTipo, identificadorDados, retiradaAutomaticaRecebivelOrigemCancelamento," +
                " infoMovimentacaoAutomaticaConciliacao, contaDeConsumo, cpfoucnpjbeneficiario, payloadpix) ");
        sql.append("values (?,?,?,?,?,?,?,?,?,?,?,?,?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
        int i = 0;
        sqlInserir.setString(++i, obj.getDescricao());
        if (obj.getPessoaVO().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getPessoaVO().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setInt(++i, obj.getUsuarioVO().getCodigo());
        sqlInserir.setInt(++i, obj.getEmpresaVO().getCodigo());
        if (obj.getContaVO().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getContaVO().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setString(++i, obj.getObservacoes());
        sqlInserir.setDouble(++i, obj.getValor());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataQuitacao()));
        if (obj.isUsarHoraAtual()) {
            sqlInserir.setTimestamp(++i, Uteis.getDataHoraJDBC(obj.getDataLancamento(), Uteis.getHoraAtual()));
        } else {
            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        }
        sqlInserir.setInt(++i, obj.getTipoOperacaoLancamento().getCodigo());
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataVencimento()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataCompetencia()));
        if (obj.getAgendamentoFinanceiro() != 0) {
            sqlInserir.setInt(++i, obj.getAgendamentoFinanceiro());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setInt(++i, obj.getNrParcela());
        if (obj.getLote()!=null && obj.getLote().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getLote().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if(obj.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.TRANSFERENCIA) && !UteisValidacao.emptyNumber(obj.getContaOrigem())){
            sqlInserir.setInt(++i, obj.getContaOrigem());
        }else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setString(++i, obj.getAutorizacaoCartao());
        sqlInserir.setBoolean(++i, obj.getApresentarNoCaixa());
        if(UteisValidacao.emptyNumber(obj.getMovProduto())){
            sqlInserir.setNull(++i, 0);
        }else{
            sqlInserir.setInt(++i, obj.getMovProduto());
        }
        sqlInserir.setBoolean(++i, obj.getApp());
        sqlInserir.setTimestamp(++i, Uteis.getDataHoraJDBC(obj.getDataLancamento(), Uteis.getHoraAtual()));
        sqlInserir.setString(++i, obj.getCodigoBarras().replaceAll(" ", "").replaceAll("  ", "").replaceAll("-", ""));
        if(obj.getCompraEstoque() != null && obj.getCompraEstoque() != 0){
            sqlInserir.setInt(++i, obj.getCompraEstoque());
        }else {
            sqlInserir.setNull(++i, 0);
        }
        if(!UteisValidacao.emptyString(obj.getNumeroDocumento())){
            sqlInserir.setString(++i, obj.getNumeroDocumento());
        }else{
            sqlInserir.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getTaxaAntecipacao())) {
            sqlInserir.setDouble(++i, obj.getTaxaAntecipacao());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setString(++i, obj.getIdentificadorOrigem());
        sqlInserir.setDouble(++i, obj.getValorPago());
        sqlInserir.setDouble(++i, obj.getValor());
        if (!UteisValidacao.emptyNumber(obj.getValorOriginalAntesDaConciliacao())) {
                sqlInserir.setDouble(++i, obj.getValorOriginalAntesDaConciliacao());
            } else {
                sqlInserir.setNull(++i, 0);
            }

        sqlInserir.setInt(++i, obj.getIdentificadorOrigemTipo());
        sqlInserir.setString(++i, obj.getIdentificadorDados());
        sqlInserir.setBoolean(++i, obj.isRetiradaAutomaticaRecebivelOrigemCancelamento());
        sqlInserir.setString(++i, obj.getInfoMovimentacaoAutomaticaConciliacao());
        sqlInserir.setBoolean(++i, obj.isContaDeConsumo());
        sqlInserir.setString(++i, obj.getCpfOuCnpjBeneficiario());
        sqlInserir.setString(++i, obj.getPayloadPix());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        MovContaRateio mrDao = new MovContaRateio(con);
        mrDao.incluirMovContasRateio(obj, obj.getMovContaRateios(), validarFormaPagamento);
        obj.setNovoObj(false);

        if (getUsarMovimentacao() && obj.getDataQuitacao() != null && codigoCaixa > 0){
            // Incluir a quitação no caixa.
            incluirQuitacaoNoCaixa(obj, codigoCaixa);
        }
        if((obj.getValorLiquido() > 0.0) && (obj.getValor() - obj.getValorLiquido() > 0)){
            gravarSaida(obj, codigoCaixa, obj.getValor() - obj.getValorLiquido(), origemMovAutomaticaConciliacao);
        }
        ConfiguracaoFinanceiro configFinanceiro = new ConfiguracaoFinanceiro(this.con);
        if ((configFinanceiro.consultar().isHabilitarExportacaoAlterData()) &&
                ((obj.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.PAGAMENTO)) || (obj.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.RECEBIMENTO)))){
            MovContaContabil movContaContabilDao = new MovContaContabil(this.con);
            try {
                movContaContabilDao.incluir(obj.getMovContaContabilVO());
            } catch (Exception ex) {
                if (ex.getMessage() != null && ex.getMessage().contains("unique_movcontacontabil")) {
                    throw new ConsistirException("A configuração para usar a exportação para AlterData está habilitada, caso não utilize essa exportação, por favor desative e repita a operação. Do contrário, está havendo o seguinte erro: " + ex.getMessage());
                } else {
                    throw ex;
                }
            }
        }
    }

    public MovContaVO incluir(MovContaVO obj) throws Exception{
        MovContaVO.validarDados(obj);
        StringBuilder sql = new StringBuilder();
        sql.append("insert into movconta (descricao, ");
        sql.append("pessoa, usuario, ");
        sql.append("empresa, observacoes, valor, ");
        sql.append("datalancamento, datavencimento, datacompetencia, tipooperacao, tipoContaPagarLote, codigobarras, payloadPix) ");
        sql.append("values (?,?,?,?,?,?,?,?,?,?,?,?,?) RETURNING codigo");

        PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
        int i = 0;
        sqlInserir.setString(++i, obj.getDescricao());
        if (obj.getPessoaVO().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getPessoaVO().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setInt(++i, obj.getUsuarioVO().getCodigo());
        sqlInserir.setInt(++i, obj.getEmpresaVO().getCodigo());
        sqlInserir.setString(++i, obj.getObservacoes());
        sqlInserir.setDouble(++i, obj.getValor());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataVencimento()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataCompetencia()));
        sqlInserir.setInt(++i, obj.getTipoOperacaoLancamento().getCodigo());
        if (obj.getTipoContaPagarLoteEnum() != null) {
            sqlInserir.setInt(++i, obj.getTipoContaPagarLoteEnum().getCodigo());
        } else {
            sqlInserir.setNull(++i, Types.NULL);
        }
        sqlInserir.setString(++i, obj.getCodigoBarras());
        sqlInserir.setString(++i, obj.getCodigoBarras().replaceAll(" ", "").replaceAll("  ", "").replaceAll("-", ""));
        sqlInserir.setString(++i, obj.getPayloadPix());

        ResultSet rsCodigo = sqlInserir.executeQuery();
        rsCodigo.next();
        obj.setCodigo(rsCodigo.getInt(1));

        //RATEIO
        MovContaRateioVO rateio = new MovContaRateioVO();
        rateio.setDescricao(obj.getDescricao());
        rateio.setValor(obj.getValor());
        rateio.setMovContaVO(obj.getCodigo());
        if (obj.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.PAGAMENTO)) {
            rateio.setTipoES(TipoES.SAIDA);
        } else if (obj.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.RECEBIMENTO)) {
            rateio.setTipoES(TipoES.ENTRADA);
        }
        List<MovContaRateioVO> movContaRateios = new ArrayList<>();
        movContaRateios.add(rateio);
        obj.setMovContaRateios(movContaRateios);
        MovContaRateio mrDao = new MovContaRateio(con);
        mrDao.incluirMovContasRateio(obj, obj.getMovContaRateios());

        return obj;
    }

    public void incluirOrigemProcessoCopiarContas(MovContaVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("insert into movconta (codigo, ");
        sql.append("pessoa, usuario, ");
        sql.append("empresa,conta, descricao, observacoes, ");
        sql.append("valor, dataquitacao, ");
        sql.append("datalancamento, tipooperacao, datavencimento, datacompetencia, agendamentofinanceiro," +
                " nrparcela, lote, contaorigem, autorizacaocartao, apresentarnocaixa, conjuntopagamento, movproduto, nfseemitida, app, lotepagouconta, dataUltimaAlteracao," +
                " nfceemitida, codigobarras, chavearquivoconta, chavearquivocomprovante, extencaoarquivocomprovante, extencaoarquivoconta, compraEstoque, numerodocumento, taxaAntecipacao, identificadorOrigem, valorpago, valororiginalalterado," +
                " valororiginalantesdaconciliacao, identificadorOrigemTipo, identificadorDados, retiradaAutomaticaRecebivelOrigemCancelamento) ");
        sql.append("values (?,?,?,?,?,?,?,?,?,?,?,?,?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
        int i = 0;
        sqlInserir.setInt(++i, obj.getCodigo());
        if (obj.getPessoaVO().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getPessoaVO().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setInt(++i, obj.getUsuarioVO().getCodigo());
        sqlInserir.setInt(++i, obj.getEmpresaVO().getCodigo());
        if (obj.getContaVO().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getContaVO().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setString(++i, obj.getDescricao());
        sqlInserir.setString(++i, obj.getObservacoes());
        sqlInserir.setDouble(++i, obj.getValor());
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataQuitacao()));
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlInserir.setInt(++i, obj.getTipoOperacaoLancamento().getCodigo());
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataVencimento()));
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataCompetencia()));
        if (obj.getAgendamentoFinanceiro() != 0) {
            sqlInserir.setInt(++i, obj.getAgendamentoFinanceiro());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setInt(++i, obj.getNrParcela());
        if (obj.getLote() != null && obj.getLote().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getLote().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getContaOrigem())) {
            sqlInserir.setInt(++i, obj.getContaOrigem());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setString(++i, obj.getAutorizacaoCartao());
        sqlInserir.setBoolean(++i, obj.getApresentarNoCaixa());
        sqlInserir.setString(++i, obj.getConjuntoPagamento());
        if (UteisValidacao.emptyNumber(obj.getMovProduto())) {
            sqlInserir.setNull(++i, 0);
        } else {
            sqlInserir.setInt(++i, obj.getMovProduto());
        }
        sqlInserir.setBoolean(++i, obj.getNfseEmitida());
        sqlInserir.setBoolean(++i, obj.getApp());
        if (obj.getLotePagouConta() != null && obj.getLotePagouConta().getCodigo() != 0) {
            sqlInserir.setInt(++i, obj.getLotePagouConta().getCodigo());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDataUltimaAlteracao()));
        sqlInserir.setBoolean(++i, obj.getNfceEmitida());
        sqlInserir.setString(++i, obj.getCodigoBarras());
        sqlInserir.setString(++i, obj.getChaveArquivoConta());
        sqlInserir.setString(++i, obj.getChaveArquivoComprovante());
        sqlInserir.setString(++i, obj.getExtensaoArquivoComprovanteMovConta());
        sqlInserir.setString(++i, obj.getExtensaoArquivoContaMovConta());

        if (obj.getCompraEstoque() != null && obj.getCompraEstoque() != 0) {
            sqlInserir.setInt(++i, obj.getCompraEstoque());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyString(obj.getNumeroDocumento())) {
            sqlInserir.setString(++i, obj.getNumeroDocumento());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getTaxaAntecipacao())) {
            sqlInserir.setDouble(++i, obj.getTaxaAntecipacao());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setString(++i, obj.getIdentificadorOrigem());
        sqlInserir.setDouble(++i, obj.getValorPago());
        sqlInserir.setDouble(++i, obj.getValorOriginalAlterado());
        if (!UteisValidacao.emptyNumber(obj.getValorOriginalAntesDaConciliacao())) {
            sqlInserir.setDouble(++i, obj.getValorOriginalAntesDaConciliacao());
        } else {
            sqlInserir.setNull(++i, 0);
        }
        sqlInserir.setInt(++i, obj.getIdentificadorOrigemTipo());
        sqlInserir.setString(++i, obj.getIdentificadorDados());
        sqlInserir.setBoolean(++i, obj.isRetiradaAutomaticaRecebivelOrigemCancelamento());
        sqlInserir.execute();
    }

    public List<AnexoMovContaVO> anexos(Integer codigoMovConta) throws Exception{
        List<AnexoMovContaVO> anexos = new ArrayList<AnexoMovContaVO>();
        ResultSet resultSet = criarConsulta("select * from anexomovconta WHERE movconta = " + codigoMovConta, con);
        while(resultSet.next()){
            AnexoMovContaVO a = new AnexoMovContaVO();
            a.setCodigo(resultSet.getInt("codigo"));
            a.setFotoKey(resultSet.getString("fotokey"));
            a.setDia(resultSet.getDate("dia"));
            anexos.add(a);
        }
        return anexos;
    }

    public void incluirAnexoMovContaSemCommit(Integer codigoMovConta,int indice,AnexoMovContaVO obj) throws Exception{
        final String chave = DAO.resolveKeyFromConnection(con);
        final String identificador = codigoMovConta+"["+indice+"]"+"[AnexoMovConta]["+Uteis.getDataComHora(Calendario.hoje())+"]";
        String base64 = obj.getDataSource().replace(" ","+");
        String fotoKey = MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_ANEXO_LANCAMENTO,identificador,Base64.decodeBase64(base64));
        StringBuilder sql = new StringBuilder();
        sql.append("insert into AnexoMovConta (fotokey, dia ,movconta) values (?,?,?)");
        PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
        int i = 0;
        sqlInserir.setString(++i,fotoKey);
        sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDia()));
        sqlInserir.setInt(++i, obj.getMovConta());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    private void gravarSaida(MovContaVO entrada, int caixaAberto, Double valor, boolean origemMovAutomaticaConciliacao) throws Exception{
        MovContaVO movConta = (MovContaVO) entrada.getClone(true);
        movConta.setTemLote(false);

        String labelTaxaAdministrativa = "";

        if (entrada.isMovimentandoTipoFormaPix()) {
            labelTaxaAdministrativa = "do Pix";
        } else if (entrada.getTipoForma().equals(TipoFormaPagto.BOLETOBANCARIO)) {
            labelTaxaAdministrativa = "do Boleto";
        } else {
            labelTaxaAdministrativa = "da operadora de cartão";
        }

        movConta.setDescricao("Taxa administrativa " + labelTaxaAdministrativa + " - "+entrada.getDescricao());
        movConta.setMovContaRateios(new ArrayList<>());
        movConta.setValor(valor);
        movConta.setValorPago(valor);
        movConta.setLiquido(false);
        movConta.setTipoOperacaoLancamento(TipoOperacaoLancamento.PAGAMENTO);
        movConta.setLote(new LoteVO());
        movConta.setValorLiquido(0.0);
        movConta.setApresentarNoCaixa(false);

        MovContaRateioVO saida = new MovContaRateioVO();
        ConfiguracaoFinanceiroVO confFinan = null;
        ConfiguracaoFinanceiro configuracaoFinanceiroDAO;
        try {
            configuracaoFinanceiroDAO = new ConfiguracaoFinanceiro(getCon());
            confFinan = configuracaoFinanceiroDAO.consultar();
        }catch (Exception e){
            ConfiguracaoFinanceiro configuracaoFinanceiro = new ConfiguracaoFinanceiro(con);
            confFinan = configuracaoFinanceiro.consultar();
        } finally {
            configuracaoFinanceiroDAO = null;
        }

        if(!entrada.getMovContaRateios().isEmpty()){
            saida.setFormaPagamentoVO(entrada.getMovContaRateios().get(0).getFormaPagamentoVO());
        }

        if (entrada.isMovimentandoTipoFormaPix()) {
            saida.setPlanoContaVO(confFinan.getPlanoContasTaxaPix());
            saida.setCentroCustoVO(confFinan.getCentroCustoTaxaPix());
        } else if (entrada.getTipoForma().equals(TipoFormaPagto.BOLETOBANCARIO)) {
            saida.setPlanoContaVO(confFinan.getPlanoContasTaxaBoleto());
            saida.setCentroCustoVO(confFinan.getCentroCustosTaxaBoleto());
        } else {
            saida.setPlanoContaVO(confFinan.getPlanoContasTaxa());
            saida.setCentroCustoVO(confFinan.getCentroCustosTaxa());
        }

        saida.setTipoES(TipoES.SAIDA);
        saida.setDescricao(movConta.getDescricao());
        saida.setValor(valor);
        movConta.getMovContaRateios().add(saida);

        incluir(movConta, caixaAberto, true, null, origemMovAutomaticaConciliacao);

    }
    @Override
    public void alterar(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento) throws Exception {
        ConfiguracaoFinanceiro configuracaoFinanceiroDao;
        MovContaRateio movContaRateioDao;
        CaixaMovConta caixaMovContaDao;
        MovContaContabil movContaContabilDao;
        try {
            configuracaoFinanceiroDao = new ConfiguracaoFinanceiro(con);
            movContaRateioDao = new MovContaRateio(con);
            caixaMovContaDao = new CaixaMovConta(con);
            movContaContabilDao = new MovContaContabil(con);

            con.setAutoCommit(false);
            MovContaVO.validarDados(obj);
            if (validaDtBloqueio(obj)){
                validarDataBloqueio(obj, false);
            }
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE movconta SET ");
            sql.append("descricao = ? ,");
            sql.append("pessoa = ?, ");
            sql.append("usuario = ?, ");
            sql.append("empresa = ?, ");
            sql.append("observacoes = ?, ");
            sql.append("valor = ?, ");
            sql.append("valororiginalalterado = ?, ");
            sql.append("dataquitacao = ?, ");
            sql.append("datalancamento = ?, ");
            sql.append("datavencimento = ?, ");
            sql.append("datacompetencia = ?, ");
            sql.append("tipooperacao = ?, ");
            sql.append("conta = ?, ");
            sql.append("lote = ?,  ");
            sql.append("apresentarnocaixa = ?, ");
            sql.append("app = ?, ");
            sql.append("dataUltimaAlteracao = ?, ");
            sql.append("codigobarras = ?, ");
            sql.append("compraEstoque = ?, ");
            sql.append("numeroDocumento = ?, ");
            sql.append("valorpago = ?, ");
            sql.append("payloadpix = ?, ");
            sql.append("contaDeConsumo = ?, ");
            sql.append("cpfoucnpjbeneficiario = ? ");
            sql.append("WHERE codigo = ?");
            PreparedStatement sqlAlterar = con.prepareStatement(sql.toString());
            int i = 1;
            sqlAlterar.setString(i++, obj.getDescricao());
            if (obj.getPessoaVO().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getPessoaVO().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setInt(i++, obj.getUsuarioVO().getCodigo());
            sqlAlterar.setInt(i++, obj.getEmpresaVO().getCodigo());
            sqlAlterar.setString(i++, obj.getObservacoes());
            sqlAlterar.setDouble(i++, obj.getValor());
            sqlAlterar.setDouble(i++, obj.getValor());
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataQuitacao()));
            sqlAlterar.setTimestamp(i++, Uteis.getDataHoraJDBC(obj.getDataLancamento(), Uteis.getHoraAtual()));
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataVencimento()));
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataCompetencia()));
            sqlAlterar.setInt(i++, obj.getTipoOperacaoLancamento().getCodigo());
            if (obj.getContaVO().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getContaVO().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getLote().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getLote().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setBoolean(i++, obj.getApresentarNoCaixa());
            sqlAlterar.setBoolean(i++, obj.getApp());
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlAlterar.setString(i++, obj.getCodigoBarras().replaceAll(" ", "").replaceAll("  ", "").replaceAll("-", ""));
            if (obj.getCompraEstoque() != null && obj.getCompraEstoque() != 0) {
                sqlAlterar.setInt(i++, obj.getCompraEstoque());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if(!UteisValidacao.emptyString(obj.getNumeroDocumento())){
                sqlAlterar.setString(i++, obj.getNumeroDocumento());
            } else{
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setDouble(i++, obj.getValorPago());
            sqlAlterar.setString(i++, obj.getPayloadPix());
            sqlAlterar.setBoolean(i++, obj.isContaDeConsumo());
            sqlAlterar.setString(i++, obj.getCpfOuCnpjBeneficiario());
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
            movContaRateioDao.alterarMovContasRateio(obj, obj.getMovContaRateios(), validarFormaPagamento);
            //validar pagamento conjunto
            if(obj.getDataQuitacao() == null){
                if(!UteisValidacao.emptyString(obj.getConjuntoPagamento())){
                    if(UteisValidacao.emptyList(obj.getContasAPagarConjunto())){
                        obj.setContasAPagarConjunto(getFacade().getFinanceiro().
                                getMovConta().verConjuntoPagamentosResumido(obj));
                    }
                    for(MovContaVO objestorno : obj.getContasAPagarConjunto()){
                        objestorno.setUsuarioVO(obj.getUsuarioVO());
                        estorno(objestorno);
                    }
                }
                MovContaVO movContaAntesAlteracao = (MovContaVO) obj.getObjetoVOAntesAlteracao();
                if (movContaAntesAlteracao != null && movContaAntesAlteracao.getDataQuitacao() != null){
                    estorno(obj);
                }
            }else{
                //se não ainda está incluída
                if(!caixaMovContaDao.movContaJaIncluido(obj.getCodigo()) && codigoCaixa > 0){
                    // Incluir a quitação no caixa.
                    incluirQuitacaoNoCaixa(obj, codigoCaixa);
                }
            }
            if (configuracaoFinanceiroDao.consultar().isHabilitarExportacaoAlterData()) {
                MovContaVO movContaAnterior = null;
                if (obj.getObjetoVOAntesAlteracao() != null) {
                    movContaAnterior = (MovContaVO) obj.getObjetoVOAntesAlteracao();
                }

                if (UtilReflection.objetoMaiorQueZero(obj, "getMovContaContabilVO().getCodigo()") && movContaAnterior != null &&
                        !UteisValidacao.emptyNumber(movContaAnterior.getMovContaContabilVO().getCodigo())) {
                    obj.getMovContaContabilVO().getMovContaVO().setIncluirMovContaContabil(true);
                    movContaContabilDao.alterar(obj.getMovContaContabilVO());
                } else {
                    obj.getMovContaContabilVO().setMovContaVO((MovContaVO) obj.getClone(false));
                    obj.getMovContaContabilVO().getMovContaVO().setIncluirMovContaContabil(true);
                    movContaContabilDao.incluir(obj.getMovContaContabilVO());
                }
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
            configuracaoFinanceiroDao = null;
            movContaRateioDao = null;
            caixaMovContaDao = null;
            movContaContabilDao = null;
        }
    }

    private boolean validaDtBloqueio(MovContaVO movContaVO)throws Exception{
        if ((!UtilReflection.objetoMaiorQueZero(movContaVO, "getCodigo()")) || (movContaVO.getObjetoVOAntesAlteracao() == null)){
            return false;
        }
        MovContaVO movContaAnterior = (MovContaVO) movContaVO.getObjetoVOAntesAlteracao();
        return (!(Calendario.getDataComHoraZerada(movContaAnterior.getDataLancamento()).equals(Calendario.getDataComHoraZerada(movContaVO.getDataLancamento()))) &&
                (Calendario.getDataComHoraZerada(movContaAnterior.getDataCompetencia()).equals(Calendario.getDataComHoraZerada(movContaVO.getDataCompetencia()))) &&
                (Calendario.getDataComHoraZerada(movContaAnterior.getDataVencimento()).equals(Calendario.getDataComHoraZerada(movContaVO.getDataVencimento()))));

    }

    /**
     * <AUTHOR> Alcides
     * 07/08/2013
     */
    private void estorno(MovContaVO obj) throws Exception, SQLException {
        CaixaMovConta caixaMovConta = new CaixaMovConta(con);
        salvarLogCaixa(obj, caixaMovConta);

        caixaMovConta.excluirPorMovConta(obj.getCodigo());
        //voltar datas originais dos cheques usados para pagar esta conta
        StringBuilder sql = new StringBuilder();
        sql.append("select ch.* ");
        sql.append("from cheque ch ");
        sql.append("inner join chequecartaolote ccl on ccl.cheque = ch.codigo ");
        sql.append("inner join lote on lote.codigo = ccl.lote ");
        sql.append("left join movConta mc on mc.codigo = lote.pagaMovConta ");
        sql.append("left join movConta mc2 on mc2.lotePagouConta = lote.codigo ");
        sql.append("where (mc.codigo =  ").append(obj.getCodigo()).append(" or mc2.codigo = ").append(obj.getCodigo()).append(")");
        //ResultSet cheques = criarConsulta("SELECT * FROM cheque WHERE codigo IN (" +"select cheque from chequecartaolote  where lote in (select codigo from lote where pagamovconta = "+obj.getCodigo()+" ))");
        ResultSet cheques = criarConsulta(sql.toString(), con);
        while(cheques.next()){
            ChequeVO cheque = Cheque.montarDados(cheques, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            ResultSet consulta = criarConsulta("SELECT cheque.dataoriginal, mp.datalancamento, e.nrdiaschequeavista "
                    + " FROM cheque inner join movpagamento mp on mp.codigo = cheque.movpagamento  "
                    + " inner join empresa e on mp.empresa = e.codigo"
                    + " WHERE cheque.codigo = " + cheque.getCodigo(), con);
            if (consulta.next()) {
                String codigos = UteisValidacao.emptyString(cheque.getComposicao()) ? String.valueOf(cheque.getCodigo()) : cheque.getCodigo() + "," + cheque.getComposicao();
                Integer nrDiasAVista = consulta.getInt("nrdiaschequeavista");
                Date dataLancamento = consulta.getDate("datalancamento");
                Date dataOriginal = consulta.getDate("dataoriginal");
                if (dataOriginal != null) {
                    String vistaouprazo = dataOriginal.compareTo(Uteis.somarDias(dataLancamento, nrDiasAVista)) == 1 ? "PR" : "AV";
                    executarConsulta("UPDATE cheque "
                            + " SET datacompesancao = dataoriginal, vistaouprazo = '" + vistaouprazo + "' WHERE dataoriginal is not null and codigo in (" + codigos + ")", con);
                }

            }
        }
        getFacade().getFinanceiro().getHistoricoCheque().excluirPorMovConta(obj.getCodigo());
        executarConsulta("UPDATE lote SET pagamovconta = null WHERE pagamovconta = "+obj.getCodigo(), con);
        if(!UteisValidacao.emptyString(obj.getConjuntoPagamento())){
            executarConsulta("DELETE from cheque WHERE movconta IN ("+obj.getConjuntoPagamento()+")", con);
        }

        executarConsulta("UPDATE movconta SET dataquitacao = null, conjuntopagamento = null, lotePagouConta = null WHERE codigo = "+obj.getCodigo(), con);

    }

    public void estornoSimplificado(MovContaVO obj) throws Exception, SQLException {
        try {
            CaixaMovConta caixaMovConta = new CaixaMovConta(con);
            salvarLogCaixa(obj, caixaMovConta);

            caixaMovConta.excluirPorMovConta(obj.getCodigo());

            executarConsulta("UPDATE lote SET pagamovconta = null WHERE pagamovconta = " + obj.getCodigo(), con);

            executarConsulta("UPDATE movconta SET dataquitacao = null, conjuntopagamento = null, lotePagouConta = null, conta = null, valorpago = null, valororiginalalterado = valorOriginalAntesDaConciliacao, " +
                    "valor = valorOriginalAntesDaConciliacao WHERE codigo = " + obj.getCodigo(), con);

        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * Responsável por
     * <AUTHOR> Alcides
     * 22/03/2013
     */
    private void salvarLogCaixa(MovContaVO obj, CaixaMovConta caixaMovConta) throws Exception {
        CaixaMovContaVO cmc = caixaMovConta.consultarPorMovConta(obj);
        if(!UteisValidacao.emptyNumber(cmc.getCodigo())){
            salvarLogEstornoPagamento(obj, cmc.getCaixaVo(), "ESTORNO DE QUITAÇÃO", obj.getUsuarioVO(), false, 0.0, null, null);
        }
        salvarLogMovContaEstornoPagamento(obj,"ESTORNO DE QUITAÇÃO", obj.getUsuarioVO());
    }
    public void salvarLogCaixaAlteracaoValor(MovContaVO obj,Double valorAnterior, ChequeVO cheque, LoteVO lote) throws Exception{
        CaixaMovConta caixaMovConta = new CaixaMovConta(con);
        CaixaMovContaVO cmc = caixaMovConta.consultarPorMovConta(obj);
        if(!UteisValidacao.emptyNumber(cmc.getCodigo())){
            salvarLogEstornoPagamento(obj, cmc.getCaixaVo(),
                    "ALTERAÇÃO DE VALORES", obj.getUsuarioVO(), true, valorAnterior, cheque, lote);
        }
        con.prepareStatement("UPDATE caixamovconta SET valor = "+obj.getValor()+" where codigo = "+cmc.getCodigo()).execute();
    }

    private void salvarLogEstornoPagamento(MovContaVO movConta, CaixaVO caixa, String operacao, UsuarioVO responsavel,
                                           boolean alteracaoValor, Double valorAnterior,
                                           ChequeVO cheque, LoteVO lote) throws Exception{
        LogVO log = new LogVO();
        log.setNomeEntidade("Caixa");
        log.setNomeEntidadeDescricao("Caixa");
        log.setChavePrimaria(caixa.getCodigo().toString());
        log.setNomeCampo(alteracaoValor ?
                operacao +" "+ (movConta.getDescricao().length() > 50 ? movConta.getDescricao().substring(0, 50) : movConta.getDescricao())
                : operacao);
        log.setValorCampoAnterior(alteracaoValor ? Formatador.formatarValorMonetario(valorAnterior) : " -- ");
        log.setDataAlteracao(Calendario.hoje());
        log.setOperacao(operacao.toUpperCase());
        log.setUsuarioVO(responsavel);
        log.setResponsavelAlteracao(responsavel.getNome());
        log.setUserOAMD(responsavel.getUserOamd());
        log.setValorCampoAlterado(alteracaoValor ? Formatador.formatarValorMonetario(movConta.getValor()) :
                "Lançamento " +movConta.getDescricao()+" no valor de "+Formatador.formatarValorMonetario(movConta.getValor()));
        if(lote != null){
            if(cheque != null)
                log.setValorCampoAlterado(log.getValorCampoAlterado()+
                        "(Proveniente de retirada do cheque número "+cheque.getNumero()+
                        " do lote "+lote.getCodigo()+"-"+lote.getDescricao()+")");
            else
                log.setValorCampoAlterado(log.getValorCampoAlterado()+
                        "(Proveniente de retirada de cartão  do lote "+lote.getCodigo()+"-"+lote.getDescricao()+")");
        }
        getFacade().getLog().incluirSemCommit(log);
    }

    private void salvarLogMovContaEstornoPagamento(MovContaVO movConta, String operacao, UsuarioVO responsavel) throws Exception{
        LogVO log = new LogVO();
        log.setNomeEntidade("MOVCONTA");
        log.setNomeEntidadeDescricao("MovContaVO");
        log.setChavePrimaria(movConta.getCodigo().toString());
        log.setNomeCampo(operacao +" "+ (movConta.getDescricao().length() > 50 ? movConta.getDescricao().substring(0, 50) : movConta.getDescricao()));
        log.setValorCampoAnterior(" -- ");
        log.setDataAlteracao(Calendario.hoje());
        log.setOperacao(operacao.toUpperCase());
        log.setUsuarioVO(responsavel);
        log.setResponsavelAlteracao(responsavel.getNome());
        log.setUserOAMD(responsavel.getUserOamd());
        log.setValorCampoAlterado("Lançamento " +movConta.getDescricao()+" no valor de "+Formatador.formatarValorMonetario(movConta.getValor()));
        getFacade().getLog().incluirSemCommit(log);
    }


    @Override
    public void excluir(MovContaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj, true, excluirLancamentoEParcelaGeradaAtravesDevolucaoCheque(obj));
            registrarLogExclusaoMovConta(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void registrarLogExclusaoMovConta(MovContaVO movContaVO)throws Exception{
        if ((JSFUtilities.isJSFContext()) && (UtilReflection.objetoMaiorQueZero(movContaVO, "getContaVO().getCodigo()"))){
            MovContaControle movContaControle = (MovContaControle) context().getExternalContext().getSessionMap().get("MovContaControle");
            if (movContaControle != null){
                LogVO obj = new LogVO();
                obj.setChavePrimaria(movContaVO.getContaVO().getCodigo().toString());
                obj.setNomeEntidade(MovContaVO.NOME_ENTIDADE_LOG_ALTERACAO_MOVCONTA);
                obj.setNomeEntidadeDescricao("MOVIMENTAÇÃO DE CONTA");
                obj.setResponsavelAlteracao(movContaControle.getUsuarioLogado().getNome());
                obj.setUserOAMD(movContaControle.getUsuarioLogado().getUserOamd());
                obj.setNomeCampo("MENSAGEM");
                obj.setValorCampoAlterado("");
                obj.setOperacao("ALTERAÇÃO");
                StringBuilder msg = new StringBuilder();
                msg.append("\nEXCLUIDO MOVCONTA:").append(movContaVO.getCodigo()).append("\n");
                msg.append("Descrição: ").append(movContaVO.getDescricao()).append("\n");
                msg.append("Tipo Operação:").append(movContaVO.getTipoOperacaoLancamento_Apresentar()).append("\n");
                msg.append("Conta:").append(movContaVO.getContaVO().getDescricao()).append("\n");
                msg.append("Pessoa:").append(movContaVO.getPessoaVO().getNome()).append("\n");
                msg.append("Valor: ").append(Uteis.getDoubleFormatado(movContaVO.getValor())).append("\n");
                msg.append("Data Lançamento:").append(movContaVO.getDataLancamento_Apresentar()).append("\n");
                msg.append("Data Quitação:").append(movContaVO.getDataQuitacao_Apresentar()).append("\n");

                obj.setValorCampoAlterado(msg.toString());
                obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                getFacade().getLog().incluirSemCommit(obj);
            }
        }

    }

    public void excluir(MovContaVO obj, List<MovContaVO> relacionados) throws Exception {
        validarCaixaAberto(obj);

        try {
            con.setAutoCommit(false);
            executarConsulta("DELETE FROM caixamovconta WHERE movconta in " +
                    "(select codigo from movconta where lote = " + obj.getLote().getCodigo() + ")", con);
            excluirSemCommit(obj, true, true);
            for (MovContaVO mov : relacionados) {
                excluirSemCommit(mov, true, true);
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public boolean excluirLancamentoEParcelaGeradaAtravesDevolucaoCheque(MovContaVO obj) throws Exception {
        boolean podeLimparRelacionamentoCheque = true;
        MovContaVO movcontaDev;
        String nrcheques = "";
        List<String> codCheques = new ArrayList<>();

        //DESCOBRIR NUMERO CHEQUE REFERENTE A ESSE MOVCONTA CUST. QUE ESTA SENDO EXCLUIDO
        ResultSet rs = con.prepareStatement(
                "select c.codigo, c.numero from cheque c" +
                        " inner join chequecartaolote ccl on ccl.cheque = c.codigo " +
                        " inner join movconta mc on mc.lote = ccl.lote " +
                        " where mc.codigo = "+obj.getCodigo()+" order by c.codigo").executeQuery();
        while (rs.next()) {
            codCheques.add(rs.getString("numero"));
            nrcheques += ";" + rs.getString("numero");
        }


        //DESCOBRIR O OUTRO MOVCONTA RELACIONADO, O DO TIPO DEV. CHEQUES. PARA TAMBEM EXCLUIR.
        ResultSet rs2 = con.prepareStatement("select codigo from movconta where descricao ilike '"+
                (codCheques.size() > 1 ? "Cheques devolvidos - " : "Cheque devolvido - ")+nrcheques.replaceFirst(";","")+"'").executeQuery();
        if (rs2.next()) {
            movcontaDev = consultarPorCodigo(rs2.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            executarConsulta("UPDATE CHEQUE SET MOVCONTA = NULL WHERE movconta = " + movcontaDev.getCodigo(), con);
            executarConsulta("DELETE FROM caixamovconta WHERE movconta = " + movcontaDev.getCodigo(), con);
            executarConsulta("DELETE FROM movconta WHERE codigo = " + movcontaDev.getCodigo(), con);
            registrarLogExclusaoMovConta(movcontaDev);
            podeLimparRelacionamentoCheque = false;
        }

        //EXCLUIR LANÇAMENTO DA PARCELA NO ZW
        for(String numeroCheque : codCheques) {
            ResultSet rs3 = con.prepareStatement("select va.codigo as vendaavulsa, mp.codigo as movproduto, mpa.codigo as movparcela, p.nome as pessoa, mp.chequedevolucao as codCheque  from vendaavulsa va" +
                    " inner join movproduto mp on mp.vendaavulsa = va.codigo" +
                    " inner join movparcela mpa on mpa.vendaavulsa = va.codigo" +
                    " inner join pessoa p on p.codigo = mp.pessoa" +
                    " where mp.descricao = 'CHEQUE DEVOLVIDO - " + numeroCheque + "'" +
                    " and mp.situacao = 'EA'").executeQuery();
            if (rs3.next()) {
                executarConsulta("DELETE FROM movprodutoparcela WHERE movproduto = " + rs3.getInt("movproduto"), con);
                executarConsulta("DELETE FROM movproduto WHERE codigo = " + rs3.getInt("movproduto"), con);
                executarConsulta("DELETE FROM movparcela WHERE codigo = " + rs3.getInt("movparcela"), con);
                executarConsulta("DELETE FROM vendaavulsa WHERE codigo  = " + rs3.getInt("vendaavulsa"), con);
                executarConsulta("UPDATE CHEQUE SET SITUACAO = 'EA' WHERE codigo = " + rs3.getInt("codCheque"), con);
            }
        }

        PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);

        return podeLimparRelacionamentoCheque;
    }

    public void excluirSemCommit(MovContaVO obj, boolean excluirLote, boolean limparRelacionamentoCheque) throws Exception {
        validarCaixaAberto(obj);
        executarConsulta("DELETE FROM caixamovconta WHERE movconta = "+obj.getCodigo(), con);
        if (excluirLote && UteisValidacao.notEmptyNumber(obj.getLote().getCodigo())) {
            getFacade().getLote().excluirSemCommit(obj.getLote(), false, limparRelacionamentoCheque);
        }

        getFacade().getFinanceiro().getMovContaContabil().excluir(obj.getCodigo());
        boolean atualizarAgendamento = parcelaEhUltimaDoAgendamento(obj);
        executarConsulta("update movproduto set situacao = 'CA' where codigo in (select movproduto from movconta where codigo = "+obj.getCodigo()+" ) ", con); //produtos relacionados a devoluções, que se não tiverem a situação alterada, podem continuar aparecendo nos relatórios.

        String sql = "DELETE FROM movconta WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();

        try {
            getFacade().getMovContaTransactionPluggy().excluirByMovConta(obj.getCodigo());
        } catch (Exception ignore) {}

        getFacade().getFinanceiro().getMovContaRateio().excluirMovContasRateio(obj.getCodigo());
        // retirar o vinculo do movPagamento.
        StringBuilder sqlMovPag = new StringBuilder();
        sqlMovPag.append("update movPagamento set movConta = null where movConta = ").append(obj.getCodigo());
        Statement st = con.createStatement();
        st.execute(sqlMovPag.toString());

        //Apagar também o lançamento de taxa administrativa
        if (obj.getDataQuitacao() != null) {
            apagarLancamentoDaTaxaAdministrativa(obj);
        }

        if (atualizarAgendamento){
            // Ao excluir o último vencimento do agendamento, corrigir a data do proximo vencimento do agendamento.
            StringBuilder sqlAgendamento = new StringBuilder();
            sqlAgendamento.append("update agendamentoFinanceiro set proximoVencimento = sql.maiorVencimentoCorreto \n");
            sqlAgendamento.append("from ( \n");
            sqlAgendamento.append("       select ag.codigo, \n");
            sqlAgendamento.append("             ((select max(dataVencimento) from movConta where  movConta.agendamentoFinanceiro = ag.codigo) + interval '1 month') as maiorVencimentoCorreto \n");
            sqlAgendamento.append("       from agendamentoFinanceiro ag \n");
            sqlAgendamento.append("       where ag.codigo = ").append(obj.getAgendamentoFinanceiro());
            sqlAgendamento.append(")sql where sql.codigo = agendamentoFinanceiro.codigo and vencimentoUltimaParcela is null ");
            Statement stAg = con.createStatement();
            stAg.execute(sqlAgendamento.toString());
        }

    }

    private void apagarLancamentoDaTaxaAdministrativa(MovContaVO obj) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT codigo \n");
            sql.append("FROM movconta \n");
            sql.append("WHERE tipooperacao = ").append(TipoOperacaoLancamento.PAGAMENTO.getCodigo()).append(" \n");
            sql.append("AND dataquitacao = '").append(obj.getDataQuitacao()).append("' \n"); //mesma quitação da movconta original (data idêntica e hora também.)
            sql.append("AND descricao ILIKE '%taxa administrativa%' ORDER BY codigo DESC LIMIT 1");

            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        int codMovContaTaxaAdministrativaExcluir = rs.getInt("codigo");

                        //deletar primeiro o caixamovconta
                        String sqlCaixaMovConta = "DELETE FROM caixamovconta WHERE movconta = " + codMovContaTaxaAdministrativaExcluir;
                        try (PreparedStatement stmExcluirCaixaMovConta = con.prepareStatement(sqlCaixaMovConta)) {
                            stmExcluirCaixaMovConta.execute();
                        }

                        //deletar a movconta agora
                        String sqlMov = "DELETE FROM movconta WHERE codigo = " + codMovContaTaxaAdministrativaExcluir;
                        try (PreparedStatement sqlExcluir = con.prepareStatement(sqlMov)) {
                            sqlExcluir.execute();
                        }
                    }
                }
            }

        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao apagarLancamentoDaTaxaAdministrativa: " + ex.getMessage());
        }
    }

    private void validarCaixaAberto(MovContaVO movContaVO) throws Exception {
        final StringBuilder sqlCaixa =
                new StringBuilder(" select cmc.caixa                                        ")
                        .append("   from caixamovconta cmc                                  ")
                        .append("       inner join movconta mc on mc.codigo = cmc.movconta  ")
                        .append("       inner join caixa c on c.codigo = cmc.caixa          ")
                        .append("   where mc.codigo = ").append(movContaVO.getCodigo())
                        .append("       and c.datafechamento is not null                    ");

        final ResultSet resultSet = criarConsulta(sqlCaixa.toString(), con);
        if (resultSet.next()) {
            throw new ConsistirException("Esta operação foi lançada no caixa " + resultSet.getInt("caixa") + " que já está fechado, e por isso não pode ser excluída. Realize a reabertura deste caixa e tente novamente.");
        }
    }

    private boolean parcelaEhUltimaDoAgendamento(MovContaVO movContaVO)throws Exception{
        if (movContaVO.getAgendamentoFinanceiro() > 0){
            StringBuilder sql = new StringBuilder();
            sql.append("select max(dataVencimento) as maiorVencimento from movConta where agendamentoFinanceiro = ").append(movContaVO.getAgendamentoFinanceiro());
            Statement st = con.createStatement();
            ResultSet rs = st.executeQuery(sql.toString());
            if (rs.next()){
                return (Calendario.igual(movContaVO.getDataVencimento(), rs.getDate("maiorVencimento")));
            }
        }
        return false;
    }

    public void deleteFromEstorno(Integer contrato, Connection con) throws Exception {
        ResultSet executeQuery = con.prepareStatement("SELECT codigo FROM movconta WHERE movproduto IN "
                + "(SELECT codigo FROM movproduto where contrato = " + contrato + ") and movproduto is not null").executeQuery();
        while (executeQuery.next()) {
            boolean existeMovContaCaixa = existe("SELECT codigo FROM caixamovconta WHERE movconta = " + executeQuery.getInt("codigo"), con);
            if (existeMovContaCaixa) {
                throw new ConsistirException("Este contrato está vinculado a uma conta já quitada no Financeiro.");
            }

            String sql = "DELETE FROM movconta WHERE codigo = ?";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, executeQuery.getInt("codigo"));
            sqlExcluir.execute();
            getFacade().getFinanceiro().getMovContaRateio().excluirMovContasRateio(executeQuery.getInt("codigo"));
        }

    }

    @Override
    public void excluirLista(List<MovContaVO> listaParaExcluir) throws Exception {
        try {
            con.setAutoCommit(false);
            for (MovContaVO mc : listaParaExcluir) {
                excluirSemCommit(mc, true, true);
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public Map<String, Number> consultarValorTotalLancamentos(FiltroLancamentosTO filtro) throws Exception {
        Map<String, Number> mapa = new HashMap<String, Number>();
        ResultSet rs = MovConta.criarConsulta(
                getFacade().getFinanceiro().getLancamentosMovConta().getSQLSelect(filtro, true), con);

        Double valorRecebimento = 0.0;
        Double valorPagamento = 0.0;
        Integer registros = 0;

        while (rs.next()) {
            if (TipoOperacaoLancamento.PAGAMENTO.getCodigo().equals(rs.getInt("tipo"))) {
                valorPagamento = rs.getDouble("soma");
            } else if (TipoOperacaoLancamento.RECEBIMENTO.getCodigo().equals(rs.getInt("tipo"))) {
                valorRecebimento = rs.getDouble("soma");
            }
            registros += rs.getInt("total");
        }

        mapa.put("valorTotalReceb", valorRecebimento);
        mapa.put("valorTotalPag", valorPagamento);
        mapa.put("valorTotal", valorRecebimento  - valorPagamento);
        mapa.put("registros", registros);

        return mapa;
    }

    @Override
    public List<MovContaVO> consultarPaginado(FiltroLancamentosTO filtro, ConfPaginacao confPaginacao, Boolean marcarTodos, boolean visualizarMovExcluidas) throws Exception {
        List<MovContaVO> movimentosConta;

        if (filtro.getDataInicioUltimaAlteracao() != null && filtro.getDataFimUltimaAlteracao() != null && visualizarMovExcluidas) {
            movimentosConta = new ArrayList<MovContaVO>();
            carregarMovimentacoesExcluidas(filtro, movimentosConta);
        }else{
            movimentosConta = realizarConsultaPaginada(filtro, confPaginacao, marcarTodos);
        }

        //setar dados bancários do fornecedor
        setarDadosBancariosFornecedor(movimentosConta);
        return movimentosConta;
    }

    public void setarDadosBancariosFornecedor(List<MovContaVO> movimentosConta) throws Exception {
        for (MovContaVO movContaVO : movimentosConta) {
            movContaVO.setContaBancariaFornecedorVO(getFacade().getContaBancariaFornecedor().consultarPorPessoa(movContaVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            if (!UteisValidacao.emptyNumber(movContaVO.getContaBancariaFornecedorVO().getCodigo())) {
                movContaVO.setFornecedorPossuiDadosBancarios(true);
            } else {
                movContaVO.setFornecedorPossuiDadosBancarios(false);
            }
        }
    }

    private void carregarMovimentacoesExcluidas(FiltroLancamentosTO filtro, List<MovContaVO> movimentosConta) throws Exception {
        List<LogVO> listaExclusao = getFacade().getLog().consultarPorNomeEntidadePorDataAlteracaoPorOperacao("MOVCONTA",
                Uteis.getDataJDBC(filtro.getDataInicioUltimaAlteracao()),
                Uteis.getDataJDBC(filtro.getDataFimUltimaAlteracao()),
                "EXCLUSÃO", "", true, new ArrayList<ColaboradorVO>(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        for (LogVO logVO : listaExclusao) {
            MovContaVO movContaVO = MovContaVO.montarObjetoApartirDoLog(logVO);
            movContaVO.setExcluido(true);
            movContaVO.setLogExclusao(logVO);
            logVO.setOperacao(movContaVO.getLogExclusao().getOperacao() + " realizado por " + movContaVO.getLogExclusao().getResponsavelAlteracao() + " no dia " + movContaVO.getLogExclusao().getDataAlteracao_Apresentar() + " " + movContaVO.getLogExclusao().getHoraAlteracao_Apresentar());
            movimentosConta.add(movContaVO);
        }

        List<LogVO> listaExclusaoLote = getFacade().getLog().consultarPorNomeEntidadePorDataAlteracaoPorOperacao("LOTE",
                Uteis.getDataJDBC(filtro.getDataInicioUltimaAlteracao()),
                Uteis.getDataJDBC(filtro.getDataFimUltimaAlteracao()),
                "EXCLUSAO", "", true, new ArrayList<ColaboradorVO>(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        for (LogVO logVO : listaExclusaoLote) {
            MovContaVO movContaVO = new MovContaVO();
            movContaVO.setDescricao(logVO.getValorCampoAnterior());
            movContaVO.setDataUltimaAlteracao(logVO.getDataAlteracao());
            movContaVO.setExcluido(true);
            movContaVO.setLogExclusao(logVO);
            logVO.setOperacao(movContaVO.getLogExclusao().getOperacao() + " realizado por " + movContaVO.getLogExclusao().getResponsavelAlteracao() + " no dia " + movContaVO.getLogExclusao().getDataAlteracao_Apresentar() + " " + movContaVO.getLogExclusao().getHoraAlteracao_Apresentar());
            movimentosConta.add(movContaVO);
        }

        Ordenacao.ordenarLista(movimentosConta, "dataUltimaAlteracao");
    }

    private List<MovContaVO> realizarConsultaPaginada(FiltroLancamentosTO filtro, ConfPaginacao confPaginacao, Boolean marcarTodos) throws Exception {
        StringBuffer sqlSelect = new StringBuffer(getFacade()
                .getFinanceiro()
                .getLancamentosMovConta()
                .getSQLSelect(filtro));

        // 1 - CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();
        // 2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);
        // concatena ordenações
        //ordernacao(sqlSelect, filtro);

        if(null != confPaginacao.getOrdernar() && confPaginacao.getOrdernar()){

            String order = "";

            if(!UteisValidacao.emptyString(confPaginacao.getColunaOrdenacao())){
                order = String.format("ORDER BY %s %s", confPaginacao.getColunaOrdenacao(), confPaginacao.getDirecaoOrdenacao());
            }

            Uteis.replaceAll(sqlSelect, Pattern.compile("ORDER BY.*?(?=\\s*LIMIT|\\)|$)", Pattern.MULTILINE | Pattern.CASE_INSENSITIVE), order);
        }

        // 3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlSelect);
        // 4 - REALIZA A CONSULTA COM PAGINACAO
        ResultSet resultadoTabela = confPaginacao.consultaPaginada();
        List<MovContaVO> lista;
        try {
            lista = getFacade().getFinanceiro().getLancamentosMovConta().consultar(
                    resultadoTabela,
                    filtro.isAgruparPorPlanoDeContasCentroCusto(),
                    getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isHabilitarExportacaoAlterData(), marcarTodos);
        } finally {
            ConsultaUtil.fecharResultSetQuietly(resultadoTabela);
        }
        return lista;
    }

    private List<MovContaVO> montarDadosAgrupadoPorPlanoContaCentroCusto(ResultSet rs, int nivelMontarDados)throws Exception{
        List<MovContaVO> lista = new ArrayList<MovContaVO>();
        boolean integracaoAlterData = getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isHabilitarExportacaoAlterData();
        while (rs.next()) {
            MovContaVO obj = montarDados(rs, nivelMontarDados,integracaoAlterData, con);
            obj.setNomePlanoConta(rs.getString("nomePlanoConta"));
            obj.setNomeCentroCusto(rs.getString("nomeCentroCusto"));
            obj.setValor(rs.getDouble("valorRateio"));
            obj.setDescricao(rs.getString("descricaoRateio"));
            lista.add(obj);
        }
        return lista;
    }

    @Override
    public List consultarTodas(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM movconta";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    @Override
    public MovContaVO gravarQuitacao(MovContaVO movConta, int codigoCaixa, List<PagamentoMovContaTO> formasQuitacao,
                                     boolean parcial, boolean gravarCheque, boolean pagamentoConjunto, boolean pagamentoMenor, boolean pagamentoMaior)
            throws Exception {
        this.con.setAutoCommit(false);

        try {

            if (movConta.isDefinirPlanoContaCentroCustoValorSuperior() &&
                    !UteisValidacao.emptyList(formasQuitacao) &&
                    !UteisValidacao.emptyList(formasQuitacao.get(0).getRateios())) {
                Double valorDif = (formasQuitacao.get(0).getValor() - formasQuitacao.get(0).valorDosRateios());

                MovContaRateioVO ratNovo = new MovContaRateioVO();
                ratNovo.setDescricao("VALOR PAGO SUPERIOR");
                ratNovo.setValor(valorDif);
                ratNovo.setMovContaVO(movConta.getCodigo());
                ratNovo.setMovConta(movConta);
                ratNovo.setTipoES(formasQuitacao.get(0).getRateios().get(0).getTipoES());
                ratNovo.setPlanoContaVO(movConta.getPlanoContaValorSuperior());
                ratNovo.setCentroCustoVO(movConta.getCentroCustoValorSuperior());
                ratNovo.setFormaPagamentoVO(formasQuitacao.get(0).getFormaPagamento());
                getFacade().getFinanceiro().getMovContaRateio().incluir(ratNovo, movConta, false);
                formasQuitacao.get(0).getRateios().add(ratNovo);
            }

            if (movConta.isIncluirMovContaContabil()){
                movConta.getMovContaContabilVO().setMovContaVO(movConta);
                if (UtilReflection.objetoMaiorQueZero(movConta, "getMovContaContabilVO().getCodigo()")){
                    getFacade().getFinanceiro().getMovContaContabil().alterar(movConta.getMovContaContabilVO());
                } else {
                    getFacade().getFinanceiro().getMovContaContabil().incluir(movConta.getMovContaContabilVO());
                }
            }
            executarConsulta("UPDATE lote SET pagamovconta = null WHERE pagamovconta = " + movConta.getCodigo(), con);
            executarConsulta("DELETE FROM cheque WHERE movconta = "+movConta.getCodigo(), con);

            MovContaVO clone = (MovContaVO) movConta.getClone(true);
            clone.setMovContaRateios(movConta.getListaRateiosClone());

            if(parcial){
                movConta.setValorOriginalAlterado(pagamentoConjunto || pagamentoMenor || pagamentoMaior ? movConta.getValor() : movConta.getValorPago());
            } else if (movConta.getValorOriginalAlterado() == null || movConta.getValorOriginalAlterado() <= 0.0){
                movConta.setValorOriginalAlterado(pagamentoConjunto || pagamentoMenor || pagamentoMaior ? movConta.getValor() : movConta.getValorPago());
            }
            movConta.setValor(pagamentoConjunto ? movConta.getValor() : 0.0 );
            movConta.setValorPago(pagamentoConjunto ? movConta.getValor() : movConta.getValorPago());

            LoteVO lotePagouConta = null;
            if (getUsarMovimentacao()) {
                movConta.setContaVO(getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(movConta.getContaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            clone.setContaVO(movConta.getContaVO());
            for (PagamentoMovContaTO pmc : formasQuitacao) {
                Double valorDosRateios = pagamentoConjunto ? movConta.getValor() : pmc.valorDosRateios();
                movConta.setValor(pagamentoConjunto ? movConta.getValor(): movConta.getValor() + pmc.getValor());

                if(pmc.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.LOTE.getSigla())){
                    if (!UtilReflection.objetoMaiorQueZero(pmc, "getLote().getCodigo()")){
                        throw new ConsistirException("Houve um erro ao consultar o Lote, consulte o lote novamente e repita a operação.");
                    }
                    lotePagouConta = pmc.getLote();
                    pmc.setLote(getFacade().getLote().consultarPorChavePrimaria(pmc.getLote().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                    if(movConta.getDataQuitacao() != null){
                        movimentarHistorico(pmc.getLote(), movConta, false);
                    }

                }else
                if(pmc.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla()) && gravarCheque){
                    getFacade().getCheque().incluirMovContaCheques(movConta, pmc.getCheques());
                }

                for (MovContaRateioVO rateio : pmc.getRateios()) {
                    rateio.setValor(pagamentoConjunto ? rateio.getValor() : pmc.getValor() * rateio.getValor() / valorDosRateios);
                    rateio.setFormaPagamentoVO(pmc.getFormaPagamento());
                    getFacade().getFinanceiro().getMovContaRateio().alterarFormaPagtoValor(rateio);
                }
            }

            // Alterar a data de quitação e a conta
            StringBuilder sqlUpdateMovConta = new StringBuilder();
            sqlUpdateMovConta.append("UPDATE movconta\n")
                    .append("SET dataquitacao = ?\n")
                    .append(", dataUltimaAlteracao=?\n");
            if (pagamentoConjunto) {
                sqlUpdateMovConta.append(", conjuntopagamento = ?\n");
            }
            if (parcial) {
                sqlUpdateMovConta.append(", valororiginalalterado = ?\n");
            }
            sqlUpdateMovConta.append(",conta = ?, valor = ?, lotePagouConta = ?, valorpago = ? WHERE codigo = ?");
            try (PreparedStatement pst = con.prepareStatement(sqlUpdateMovConta.toString())) {
                int i = 0;
                pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(movConta.getDataQuitacao()));
                pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                if (pagamentoConjunto) {
                    pst.setString(++i, movConta.getConjuntoPagamento());
                }
                if (parcial) {
                    pst.setDouble(++i, movConta.getValorOriginalAlterado());
                }
                resolveFKNull(pst, ++i, movConta.getContaVO().getCodigo());
                pst.setDouble(++i, movConta.getValor());
                if (UtilReflection.objetoMaiorQueZero(lotePagouConta, "getCodigo()")) {
                    pst.setInt(++i, lotePagouConta.getCodigo());
                } else {
                    pst.setNull(++i, Types.NULL);
                }
                pst.setDouble(++i, movConta.getValorPago());
                pst.setInt(++i, movConta.getCodigo());
                pst.execute();
            }
            if (movConta.getDataQuitacao() != null)
                incluirQuitacaoNoCaixa(movConta, codigoCaixa);

            if (parcial) {
                clone.setDescricao(clone.getDescricao() + " - RESTANTE PGTO.");
                String[] descricaoClone = clone.getDescricao().split(" - RESTANTE PGTO.");
                if (descricaoClone.length > 1) {
                    try {
                        Integer numero = Integer.parseInt(descricaoClone[descricaoClone.length - 1].replace(" ", ""));
                        numero += 1;
                        String descAdicional = (numero < 10) ? " - RESTANTE PGTO. 0" + numero : " - RESTANTE PGTO. " + numero;
                        clone.setDescricao(descricaoClone[0]);
                        for(int d = 1; d < descricaoClone.length - 1; d++){ //parcelas que tiveram pagamento parcial e depois foram agendadas
                            clone.setDescricao(clone.getDescricao()+" - RESTANTE PGTO."+descricaoClone[d]);
                        }
                        clone.setDescricao(clone.getDescricao() + descAdicional);
                    } catch (Exception e) {
                        clone.setDescricao(clone.getDescricao() + " 01");//parcelas que tiveram pagamento parcial e depois foram agendadas
                    }
                } else {
                    clone.setDescricao(clone.getDescricao() + " 01");
                }

                clone.setValor(clone.getValor() - movConta.getValor());
                clone.setValorOriginalAlterado(clone.getValor());
                clone.setValorPago(0.0);
                clone.setDataQuitacao(null);
                clone.setDataLancamento(Calendario.hoje());
                clone.setMovContaRateios(new ArrayList<MovContaRateioVO>());
                for (PagamentoMovContaTO pmc : formasQuitacao) {
                    Double valorDosRateios = pmc.valorDosRateios();
                    Double valorFPParcial = clone.getValor() * pmc.getValor() / movConta.getValor();

                    for (MovContaRateioVO rateio : pmc.getRateios()) {
                        rateio.setValor(valorFPParcial * rateio.getValor() / valorDosRateios);
                        clone.getMovContaRateios().add(rateio);
                    }
                }

                incluirSemCommit(clone, codigoCaixa, true, null);

            }

            this.con.commit();
            return clone;
        } catch (Exception e) {
            this.con.rollback();
            throw e;
        } finally {
            this.con.setAutoCommit(true);
        }
    }

    @Override
    public void gravarQuitacao(MovContaVO movConta, int codigoCaixa, List<PagamentoMovContaTO> formasQuitacao, boolean conciliacao) throws Exception {
        this.con.setAutoCommit(false);
        try {

            //apagar todos os movcontaRateioExistente pois irá incluir o novo no final
            List<MovContaRateioVO> rateiosAntigos = getFacade().getMovContaRateio().consultarPorMovConta(movConta.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            for (MovContaRateioVO rateio : rateiosAntigos) {
                getFacade().getMovContaRateio().excluirMovContasRateio(movConta.getCodigo());
            }

            //conciliacao deve alterar o valor original do lancamento
            if (conciliacao) {
                if (movConta.getObjetoVOAntesAlteracao() == null) {
                    movConta.setObjetoVOAntesAlteracao((MovContaVO) movConta.getClone(false));
                }
                alterarSomenteValor(movConta, movConta.getValorPago());
                gravarValorAntesDaConciliacao(movConta);

                //consultar novo obj alterado
                MovContaVO obj = consultarPorCodigo(movConta.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                movConta.setValor(obj.getValor());
            }

            executarConsulta("UPDATE lote SET pagamovconta = null WHERE pagamovconta = " + movConta.getCodigo(), con);
            executarConsulta("DELETE FROM cheque WHERE movconta = " + movConta.getCodigo(), con);

            LoteVO lotePagouConta = null;

            //setar conta
            if (getUsarMovimentacao()) {
                movConta.setContaVO(getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(
                        movConta.getContaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            // Alterar a data de quitação e a conta
            StringBuilder sql = new StringBuilder("UPDATE movconta SET dataquitacao = ?, dataUltimaAlteracao=?, ");
            sql.append("conta = ?, valor = ?, lotePagouConta = ?, valorpago = ? WHERE codigo = ?");
            PreparedStatement pst = con.prepareStatement(sql.toString());
            int i = 1;
            pst.setTimestamp(i++, Uteis.getDataJDBCTimestamp(movConta.getDataQuitacao()));
            pst.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveFKNull(pst, i++, movConta.getContaVO().getCodigo());
            pst.setDouble(i++, movConta.getValor());
            if (UtilReflection.objetoMaiorQueZero(lotePagouConta, "getCodigo()")) {
                pst.setInt(i++, lotePagouConta.getCodigo());
            } else {
                pst.setNull(i++, Types.NULL);
            }
            pst.setDouble(i++, movConta.getValorPago());
            pst.setInt(i++, movConta.getCodigo());
            pst.execute();
            if (movConta.getDataQuitacao() != null) {
                incluirQuitacaoNoCaixa(movConta, codigoCaixa);
            }

            //incluir novo rateio
            MovContaRateio mrDao = new MovContaRateio(con);
            mrDao.incluirMovContasRateio(movConta, movConta.getMovContaRateios(), false);

            this.con.commit();
        } catch (Exception e) {
            this.con.rollback();
            throw e;
        } finally {
            this.con.setAutoCommit(true);
        }
    }

    public void gravarQuitacao(MovContaVO movConta, int codigoCaixa, boolean pagoOrigemWebhook, boolean presaEmLoteDePagamento) throws Exception {
        try {
            executarConsulta("UPDATE lote SET pagamovconta = null WHERE pagamovconta = " + movConta.getCodigo(), con);
            executarConsulta("DELETE FROM cheque WHERE movconta = " + movConta.getCodigo(), con);

            // Alterar a data de quitação e a conta
            StringBuilder sql = new StringBuilder("UPDATE movconta SET dataquitacao = ?, dataUltimaAlteracao = ?, ");
            sql.append(" valorpago = ?, pagoOrigemWebhook = ?, presaEmLoteDePagamento = ? WHERE codigo = ?");
            PreparedStatement pst = con.prepareStatement(sql.toString());
            int i = 1;
            pst.setTimestamp(i++, Uteis.getDataJDBCTimestamp(movConta.getDataQuitacao()));
            pst.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setDouble(i++, movConta.getValorPago());
            pst.setBoolean(i++, pagoOrigemWebhook);
            pst.setBoolean(i++, presaEmLoteDePagamento);
            pst.setInt(i++, movConta.getCodigo());
            pst.execute();
            if (movConta.getDataQuitacao() != null) {
                incluirQuitacaoNoCaixa(movConta, codigoCaixa);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void movimentarHistorico(LoteVO lote, MovContaVO movConta, boolean controlarTransacao) throws Exception{

        //CHEQUES
        for(ChequeVO cheque : lote.getCheques()){
            HistoricoChequeVO histCheque = new HistoricoChequeVO();
            histCheque.setCheque(cheque);
            histCheque.setStatus(StatusCheque.CUSTODIA);
            histCheque.setDataInicio(Calendario.hoje());
            histCheque.setLote(lote);
            histCheque.setMovConta(movConta);
            getFacade().getFinanceiro().getHistoricoCheque().incluir(histCheque, controlarTransacao);

            cheque.setDataOriginal(cheque.getDataCompensacao());
            cheque.setDataCompensacao(movConta.getDataQuitacao());

            getFacade().getCheque().alterarMinimo(cheque);
        }

        //CARTÕES
        for(CartaoCreditoVO cartao : lote.getCartoes()){
            HistoricoCartaoVO histCartao = new HistoricoCartaoVO();
            histCartao.setCartao(cartao);
            histCartao.setDataInicio(Calendario.hoje());
            histCartao.setLote(lote);
            histCartao.setMovConta(movConta);
            getFacade().getFinanceiro().getHistoricoCartao().incluir(histCartao, controlarTransacao);

            cartao.setDataOriginal(cartao.getDataCompensacao());
            cartao.setDataCompensacao(movConta.getDataQuitacao());

            getFacade().getCartaoCredito().alterarMinimo(cartao);
        }

    }

    private void incluirQuitacaoNoCaixa(MovContaVO movConta, int codigoCaixa) throws Exception {
        if(!getUsarMovimentacao()){
            return;
        }
        // Incluir quitação no caixa.
        CaixaMovContaVO caixaMovContaVo = new CaixaMovContaVO();
        caixaMovContaVo.getCaixaVo().setCodigo(codigoCaixa);
        caixaMovContaVo.getMovContaVo().setCodigo(movConta.getCodigo());
        caixaMovContaVo.setDescricao(movConta.getDescricao());
        caixaMovContaVo.setValor(movConta.getValor());
        CaixaMovConta cmDao = new CaixaMovConta(con);
        cmDao.incluir(caixaMovContaVo);

        for(MovContaRateioVO rateio : movConta.getMovContaRateios()){
            if(rateio.getTipoES().equals(TipoES.SAIDA)){
                executarConsulta("UPDATE caixaconta set saida = saida + "+rateio.getValor()+", saldofinal = saldofinal - "+rateio.getValor()+" where conta = "
                        +movConta.getContaVO().getCodigo()+" and caixa = "+codigoCaixa, con);
            }else{
                executarConsulta("UPDATE caixaconta set entrada = entrada + "+rateio.getValor()+", saldofinal = saldofinal + "+rateio.getValor()+" where conta = "
                        +movConta.getContaVO().getCodigo()+" and caixa = "+codigoCaixa, con);
            }
        }
    }

    @Override
    public MovContaVO consultarPorCodigo(int codigoMovConta, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT movconta.*, pessoa.nome nomepessoa, conta.descricao descricaoconta, empresa.nome nomeempresa  ");
        sql.append(" FROM movconta ");
        sql.append(" inner join empresa on empresa.codigo = movconta.empresa ");
        sql.append(" left join pessoa on pessoa.codigo = movconta.pessoa ");
        sql.append(" left join conta on conta.codigo = movconta.conta ");
        sql.append(" where movconta.codigo = ");
        sql.append(codigoMovConta);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        MovContaVO movContaVo = null;

        ConfiguracaoFinanceiro configuracaoFinanceiroDAO;
        boolean integracaoAlterData = false;
        try {
            configuracaoFinanceiroDAO = new ConfiguracaoFinanceiro(con);
            integracaoAlterData = configuracaoFinanceiroDAO.consultar().isHabilitarExportacaoAlterData();
        } catch (Exception ex) {
        } finally {
            configuracaoFinanceiroDAO = null;
        }

        if (tabelaResultado.next()) {
            movContaVo = montarDados(tabelaResultado, nivelMontarDados,integracaoAlterData, con);
        }
        return movContaVo;
    }

    @Override
    public MovContaVO consultarPorChavePrimaria(int codMovConta, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT movconta.* \n");
        sql.append(" FROM movconta \n");
        sql.append(" where movconta.codigo = ").append(codMovConta);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());

        while (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, false, con);
        }

        return null;
    }


    public List<MovContaVO> consultarPorConta(int codigoConta, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT movconta.*, pessoa.nome nomepessoa, conta.descricao descricaoconta, empresa.nome nomeempresa  ");
        sql.append(" FROM movconta ");
        sql.append(" inner join empresa on empresa.codigo = movconta.empresa ");
        sql.append(" left join pessoa on pessoa.codigo = movconta.pessoa ");
        sql.append(" left join conta on conta.codigo = movconta.conta ");
        sql.append(" where movconta.conta = ");
        sql.append(codigoConta);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    @Override
    public MovContaVO consultarUltimoLancamentoPessoa(Integer codigoPessoa, Integer tipoLancamento, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM movconta WHERE pessoa = " + codigoPessoa + " AND tipooperacao = " + tipoLancamento + " ORDER BY datalancamento DESC LIMIT 1 ";
        MovContaVO conta = new MovContaVO();
        ResultSet resultSet = criarConsulta(sql, con);
        boolean integracaoAlterData = getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isHabilitarExportacaoAlterData();
        if (resultSet.next()) {
            conta = montarDados(resultSet, nivelMontarDados,integracaoAlterData, con);
        }
        return conta;
    }

    @Override
    public List<MovContaVO> consultarLancamentosPeloAgendamento(int agendamento, Date inicio, Date fim, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM movconta WHERE agendamentofinanceiro = " + agendamento;
        if (inicio != null && fim == null) {
            sqlStr += "AND datavencimento > '" + Uteis.getDataJDBC(inicio) + " 00:00:00' ";
        }

        if (inicio != null && fim != null) {
            sqlStr += " AND datavencimento BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' "
                    + "AND '" + Uteis.getDataJDBC(fim) + " 23:59:59'";
        }
        sqlStr += " ORDER BY codigo ASC";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public List<MovContaVO> consultarPorAgendamento(Integer codigoAgendamento, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from movConta where agendamentoFinanceiro = ").append(codigoAgendamento);
        sql.append(" order by dataVencimento");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        return (montarDadosConsulta(rs, nivelMontarDados, con));
    }


    @Override
    public List<MovContaVO> consultarPeloAgendamentoVencimentoNaoQuitadas(int agendamento, Date vencimento, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM movconta WHERE agendamentofinanceiro = " + agendamento
                + " AND datavencimento > '" + Uteis.getDataJDBC(vencimento) + "' AND dataquitacao is null order by datavencimento";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        ConfiguracaoFinanceiro configuracaoFinanceiroDAO = new ConfiguracaoFinanceiro(con);
        boolean integracaoAlterData = configuracaoFinanceiroDAO.consultar().isHabilitarExportacaoAlterData();
        configuracaoFinanceiroDAO = null;
        while (tabelaResultado.next()) {
            MovContaVO obj = new MovContaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, integracaoAlterData, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static List montarDados(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            MovContaVO obj = new MovContaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, false, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static MovContaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, boolean integracaoAlterData, Connection con) throws Exception {
        MovContaVO obj = new MovContaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setValorOriginalAlterado(dadosSQL.getDouble("valorOriginalAlterado"));
        obj.setValorPago(dadosSQL.getDouble("valorPago"));
        try {
            obj.setCodigoBarras(dadosSQL.getString("codigoBarras"));
        }catch (Exception e){}
        try {
            obj.setPayloadPix(dadosSQL.getString("payloadpix"));
        } catch (Exception ex) {}
        try {
            obj.setPagoOrigemWebhook(dadosSQL.getBoolean("pagoOrigemWebhook"));
        } catch (Exception ex) {}
        try {
            obj.setContaDeConsumo(dadosSQL.getBoolean("contaDeConsumo"));
        } catch (Exception ex) {}
        try {
            obj.setIdExterno(dadosSQL.getString("idExterno"));
        } catch (Exception ex) {}
        try {
            obj.setCpfOuCnpjBeneficiario(dadosSQL.getString("cpfoucnpjbeneficiario"));
        } catch (Exception ex) {}
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS){
            obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
            Pessoa pessoa = new Pessoa(con);
            obj.setPessoaVO(pessoa.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), nivelMontarDados));
            obj.setDataVencimento(dadosSQL.getTimestamp("datavencimento"));
            obj.setDataQuitacao(dadosSQL.getTimestamp("dataquitacao"));
            obj.setDataLancamento(dadosSQL.getTimestamp("datalancamento"));
            obj.setDataCompetencia(dadosSQL.getTimestamp("datacompetencia"));
            return obj;
        }
        obj.setDataUltimaAlteracao(dadosSQL.getTimestamp("dataUltimaAlteracao"));
        obj.setTipoOperacaoLancamento(obj.getTipoOperacaoLancamento().getTipoOperacaoLancamento(dadosSQL.getInt("tipooperacao")));
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_PAGAMENTOS_CONJUNTO_RESUMIDOS){
            obj.getPessoaVO().setNome(dadosSQL.getString("pessoa"));
            return obj;
        }
        obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
        try {
            obj.setApp(dadosSQL.getBoolean("app"));
        }catch (Exception e){}
        try {
            obj.setChaveArquivoConta(dadosSQL.getString("chaveArquivoConta"));
        }catch (Exception e){}
        try {
            obj.setChaveArquivoComprovante(dadosSQL.getString("chaveArquivoComprovante"));
        }catch (Exception e){}
        try {
            obj.setExtensaoArquivoContaMovConta(dadosSQL.getString("extencaoarquivoconta"));
        }catch (Exception e){}
        try {
            obj.setExtensaoArquivoComprovanteMovConta(dadosSQL.getString("extencaoarquivocomprovante"));
        }catch (Exception e){}
        try {
            obj.setCompraEstoque(dadosSQL.getInt("compraEstoque"));
        } catch (Exception e) {}
        try {
            obj.setNumeroDocumento(dadosSQL.getString("numeroDocumento"));
        }catch (Exception e){}
        try {
            obj.setTaxaAntecipacao(dadosSQL.getDouble("taxaAntecipacao"));
        }catch (Exception e){}
        try {
            obj.getPessoaVO().setCfp(dadosSQL.getString("cpfPessoa"));
            obj.setMatricula(dadosSQL.getString("matricula"));
        }catch (Exception ignored){}
        obj.setRetiradaAutomaticaRecebivelOrigemCancelamento(dadosSQL.getBoolean("retiradaAutomaticaRecebivelOrigemCancelamento"));
        obj.getUsuarioVO().setCodigo(dadosSQL.getInt("usuario"));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.getContaVO().setCodigo(dadosSQL.getInt("conta"));
        obj.setObservacoes(dadosSQL.getString("observacoes"));
        obj.setDataQuitacao(dadosSQL.getTimestamp("dataquitacao"));
        obj.setDataLancamento(dadosSQL.getTimestamp("datalancamento"));
        obj.setDataCompetencia(dadosSQL.getTimestamp("datacompetencia"));
        obj.setDataVencimento(dadosSQL.getTimestamp("datavencimento"));
        obj.setAgendamentoFinanceiro(dadosSQL.getInt("agendamentoFinanceiro"));
        obj.setNrParcela(dadosSQL.getInt("nrParcela"));
        obj.getLote().setCodigo(dadosSQL.getInt("lote"));
        obj.setContaOrigem(dadosSQL.getInt("contaorigem"));
        obj.setApresentarNoCaixa(dadosSQL.getBoolean("apresentarnocaixa"));
        obj.setNfseEmitida(dadosSQL.getBoolean("nfseemitida"));
        obj.setNfceEmitida(dadosSQL.getBoolean("nfceemitida"));
        obj.setLotePagouConta(new LoteVO());
        obj.getLotePagouConta().setCodigo(dadosSQL.getInt("lotePagouConta"));
        try {
            obj.setIdentificadorOrigem(dadosSQL.getString("identificadorOrigem"));
        }catch (Exception ignored){}
        try {
            obj.setIdentificadorDados(dadosSQL.getString("identificadorDados"));
        }catch (Exception ignored){}

        Lote loteDAO = new Lote(con);
        obj.setLote(loteDAO.consultarPorChavePrimaria(obj.getLote().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        loteDAO = null;

        obj.setConjuntoPagamento(dadosSQL.getString("conjuntopagamento"));
        obj.setNovoObj(false);

        Caixa caixaDAO = new Caixa(con);
        obj.setCaixa(caixaDAO.consultarCaixaParaMovConta(obj.getCodigo(), null).getCodigo());
        caixaDAO = null;

        if (integracaoAlterData){
            MovContaContabil movContaContabilDAO = new MovContaContabil(con);
            obj.setMovContaContabilVO(movContaContabilDAO.consultar(obj.getCodigo()));
            movContaContabilDAO = null;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            obj.getEmpresaVO().setNome(dadosSQL.getString("nomeempresa"));
            obj.getPessoaVO().setNome(dadosSQL.getString("nomepessoa"));
            obj.getContaVO().setDescricao(dadosSQL.getString("descricaoconta"));
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            Pessoa pessoa = new Pessoa(con);
            obj.setPessoaVO(pessoa.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), nivelMontarDados));

        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_LOTE_AVULSO) {
            return obj;

        }

        if(!UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())){ // movimentacoes de lote avulso não tem favorecido
            ContaBancariaFornecedor contaBancariaFornecedor;
            try {
                contaBancariaFornecedor = new ContaBancariaFornecedor(con);
                obj.setContaBancariaFornecedorVO(contaBancariaFornecedor.consultarPorPessoa(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                if (!UteisValidacao.emptyNumber(obj.getContaBancariaFornecedorVO().getCodigo())) {
                    obj.setFornecedorPossuiDadosBancarios(true);
                }
            } catch (Exception ex) {
            } finally {
                contaBancariaFornecedor = null;
            }
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            if(!UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())){ // movimentacoes de lote avulso não tem favorecido
                Pessoa pessoa = new Pessoa(con);
                obj.setPessoaVO(pessoa.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
            return obj;

        }

        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);
            obj.setUsuarioVO(usuarioDAO.consultarPorChavePrimaria(obj.getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
        } catch (Exception ex) {
        } finally {
            usuarioDAO = null;
        }
        if(obj.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.RETIRADA_RECEBIVEL_LOTE)){
            obj.setChequesRetirados(new Cheque(con).consultarChequesRetirados(null, obj.getCodigo()));
            obj.setCartoesRetirados(new CartaoCredito(con).consultarCartoesRetirados(null, obj.getCodigo()));
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            MovContaRateio movContaRateio = new MovContaRateio(con);
            Empresa empresa = new Empresa(con);
            obj.setEmpresaVO(empresa.consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            Pessoa pessoa = new Pessoa(con);
            obj.setPessoaVO(pessoa.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), nivelMontarDados));
            obj.setMovContaRateios(movContaRateio.consultarPorMovConta(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));

            if(!UteisValidacao.emptyNumber(obj.getContaVO().getCodigo())){
                Conta conta = new Conta(con);
                obj.setContaVO(conta.consultarPorChavePrimaria(obj.getContaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

        }
        return obj;
    }

    private StringBuffer criarConsulta(FiltroLancamentosTO filtro, boolean soma) throws Exception {
        //sql principal
        StringBuffer sqlSelect = new StringBuffer();
        if (soma) {
            sqlSelect.append("SELECT SUM (valor) AS soma, COUNT(*) AS total, tipooperacao  AS tipo FROM ( \n");
            sqlSelect.append("SELECT distinct (mc.codigo), mc.* \n");
        } else {
            sqlSelect.append("SELECT distinct (mc.codigo), mc.*, empresa.nome as nomeempresa, pessoa.nome as nomepessoa, conta.descricao as descricaoconta \n");
            if (filtro.isAgruparPorPlanoDeContasCentroCusto()) {
                sqlSelect.append(" ,(pc.codigoPlanoContas || ' - ' || pc.nome) as nomePlanoConta, (cc.codigoCentroCustos || ' - ' || cc.nome) as nomeCentroCusto, mcr.valor as valorRateio, mcr.descricao as descricaoRateio \n");
            }
        }
        sqlSelect.append("FROM movconta mc \n");
        sqlSelect.append("LEFT JOIN conta ON mc.conta = conta.codigo \n");
        sqlSelect.append("INNER JOIN movcontarateio mcr ON mcr.movconta = mc.codigo \n");
        if ((filtro.isAgruparPorPlanoDeContasCentroCusto()) && (!soma)){
            sqlSelect.append("left join planoConta pc on pc.codigo = mcr.planoConta \n");
            sqlSelect.append("left join centroCusto cc on cc.codigo = mcr.centrocusto \n");
        }
        sqlSelect.append("INNER JOIN empresa ON empresa.codigo = mc.empresa \n");
        if(filtro.getAgendamentosAndamento()){
            sqlSelect.append("INNER JOIN agendamentofinanceiro agend ON mc.agendamentofinanceiro = agend.codigo and agend.vencimentoUltimaParcela is null and (agend.parcelaini is null or agend.parcelaini = 0)  \n");
            sqlSelect.append("LEFT JOIN movconta  mcagend ON mc.agendamentofinanceiro = mcagend.agendamentofinanceiro and mcagend.codigo > mc.codigo \n");
        }
        sqlSelect.append("LEFT JOIN pessoa ON mc.pessoa = pessoa.codigo \n");

        //filtro de pesquisa por cheque...
        if ( filtro.getChequeVO().getBanco().getCodigo() != null && filtro.getChequeVO().getBanco().getCodigo() != 0
                || filtro.getChequeVO().getAgencia() != null && filtro.getChequeVO().getAgencia() != ""
                || filtro.getChequeVO().getConta() != null && filtro.getChequeVO().getConta() != ""
                || filtro.getChequeVO().getNumero() != null && filtro.getChequeVO().getNumero() != "" ){

            sqlSelect.append("INNER JOIN cheque cq ON cq.movconta = mc.codigo \n");
        }

        //os filtros da consulta
        filtros(filtro, sqlSelect);
        return sqlSelect;
    }

    private void filtros(FiltroLancamentosTO filtro, StringBuffer sqlSelect) throws Exception {
        //descricao
        sqlSelect.append("WHERE mc.descricao ILIKE '%" + filtro.getDescricao() + "%' \n");

        if(filtro.getAgendamentosAndamento()){
            sqlSelect.append(" AND mcagend.codigo is null \n");
        }

        if(filtro.getSomenteContasMobile()){
            sqlSelect.append(" AND app \n");
        }

        if (!UteisValidacao.emptyNumber(filtro.getCodigo())) {
            sqlSelect.append("AND mc.codigo = " + filtro.getCodigo() + " \n");
        }

        if (!UteisValidacao.emptyNumber(filtro.getTipoLancamento())) {
            if (filtro.getTipoLancamento().equals(TipoOperacaoLancamento.FLUXO_CAIXA.getCodigo())) {
                sqlSelect.append("AND mc.tipooperacao IN (" +
                        TipoOperacaoLancamento.RECEBIMENTO.getCodigo() + "," +
                        TipoOperacaoLancamento.PAGAMENTO.getCodigo() + ") \n");
            } else {
                sqlSelect.append("AND mc.tipooperacao = " + filtro.getTipoLancamento() + " \n");
            }
        }
        if (filtro.getPagamentosConjunto()) {
            sqlSelect.append("AND mc.conjuntopagamento is not null \n");
        }
        //empresas selecionadas
        String empresas = filtro.getCodigosEmpresa();
        if (!empresas.isEmpty()) {
            sqlSelect.append("AND mc.empresa IN (" + empresas + ") \n");
        }
        //tipos documentos selecionados
        String doc = filtro.getCodigosTipoDocumento();
        if (!doc.isEmpty()) {
            sqlSelect.append("AND tipodocumento IN (" + doc + ") \n");
        }
        //contas selecionadas
        String conta = filtro.getCodigosConta();
        if (!conta.isEmpty()) {
            sqlSelect.append("AND conta IN (" + conta + ") \n");
        }
        //formas de pagamento selecionadas
        String formaPgto = filtro.getCodigosFormaPgto();
        if (!formaPgto.isEmpty()) {
            sqlSelect.append("AND mcr.formapagamento IN (" + formaPgto + ") \n");
        }
        //planos de contas selecionados
        String planoContas = filtro.getCodigosPlanoContas();
        if (!planoContas.isEmpty()) {
            sqlSelect.append("AND mc.codigo IN (SELECT movconta FROM movcontarateio WHERE planoconta IN (" + planoContas + ")) \n");
        }

        String centroCustos = filtro.getCodigosCentroCustos();
        if (!centroCustos.isEmpty()) {
            sqlSelect.append("AND mc.codigo IN (SELECT movconta FROM movcontarateio WHERE centrocusto IN (" + centroCustos + ")) \n");
        }
        //favorecido
        if (!UteisValidacao.emptyNumber(filtro.getFavorecido().getCodigo())) {
            sqlSelect.append("AND mc.pessoa = " + filtro.getFavorecido().getCodigo() + " \n");
        }
        //valores máximo e minimo
        if (!UteisValidacao.emptyNumber(filtro.getValorMaximo())) {
            sqlSelect.append("AND mc.valor BETWEEN " + filtro.getValorMinimo() + " AND " + filtro.getValorMaximo() + " \n");
        }
        //data lancamento
        if (filtro.getInicioLancamento() != null && filtro.getFinalLancamento() != null) {
            sqlSelect.append("AND mc.datalancamento BETWEEN '" + Uteis.getDataJDBC(filtro.getInicioLancamento()) + " 00:00:00' "
                    + "AND '" + Uteis.getDataJDBC(filtro.getFinalLancamento()) + " 23:59:59' \n");
        }
        //data quitacao
        if (filtro.getInicioQuitacao() != null && filtro.getFinalQuitacao() != null) {
            sqlSelect.append("AND mc.dataquitacao BETWEEN '" + Uteis.getDataJDBC(filtro.getInicioQuitacao()) + " 00:00:00' "
                    + "AND '" + Uteis.getDataJDBC(filtro.getFinalQuitacao()) + " 23:59:59' \n");
        }
        //data competencia
        if (filtro.getInicioCompetencia() != null && filtro.getFinalCompetencia() != null) {
            sqlSelect.append("AND mc.datacompetencia BETWEEN '" + Uteis.getDataJDBC(filtro.getInicioCompetencia()) + " 00:00:00' "
                    + "AND '" + Uteis.getDataJDBC(filtro.getFinalCompetencia()) + " 23:59:59' \n");
        }
        //data vencimento
        if (filtro.getInicioVencimento() != null && filtro.getFinalVencimento() != null) {
            sqlSelect.append("AND mc.datavencimento BETWEEN '" + Uteis.getDataJDBC(filtro.getInicioVencimento()) + " 00:00:00' "
                    + "AND '" + Uteis.getDataJDBC(filtro.getFinalVencimento()) + " 23:59:59' \n");
        }

        //data ultima alteração(inclusão, alteração e exclusão)
        if (filtro.getDataInicioUltimaAlteracao() != null && filtro.getDataFimUltimaAlteracao() != null) {
            sqlSelect.append("AND mc.dataUltimaAlteracao BETWEEN '" + Uteis.getDataJDBC(filtro.getDataInicioUltimaAlteracao()) + " 00:00:00' "
                    + "AND '" + Uteis.getDataJDBC(filtro.getDataFimUltimaAlteracao()) + " 23:59:59' \n");
        }

        if (filtro.getApenasNaoQuitados() && !filtro.getApenasQuitados()) {
            sqlSelect.append("AND mc.dataquitacao IS NULL \n");
        }
        if (!filtro.getApenasNaoQuitados() && filtro.getApenasQuitados()) {
            sqlSelect.append("AND mc.dataquitacao IS NOT NULL \n");
        }

        //filtro de pagamento por cheque parametro 5: BANCO
        if(filtro.getChequeVO().getBanco().getCodigo() != null && filtro.getChequeVO().getBanco().getCodigo() != 0){
            sqlSelect.append("AND cq.banco = " + filtro.getChequeVO().getBanco().getCodigo() + " \n");
        }
        //filtro de pagamento por cheque parametro 5: AGENCIA
        if(filtro.getChequeVO().getAgencia() != null && filtro.getChequeVO().getAgencia() != ""){
            sqlSelect.append("AND cq.agencia = '" + filtro.getChequeVO().getAgencia() + "' \n");
        }
        //filtro de pagamento por cheque parametro 5: NUMERO CONTA
        if(filtro.getChequeVO().getConta() != null && filtro.getChequeVO().getConta() != ""){
            sqlSelect.append("AND cq.conta = '" + filtro.getChequeVO().getConta() + "' \n");
        }
        //filtro de pagamento por cheque parametro 5: NUMERO CHEQUE
        if(filtro.getChequeVO().getNumero() != null && filtro.getChequeVO().getNumero() != ""){
            sqlSelect.append("AND cq.numero = '" + filtro.getChequeVO().getNumero() + "' \n");
        }

    }

    private void ordernacao(StringBuffer sql, FiltroLancamentosTO filtro) {
        if (filtro.isAgruparPorPlanoDeContasCentroCusto()){
            sql.append("ORDER BY (pc.codigoPlanoContas || ' - ' || pc.nome)");
        }else{
            sql.append(" ORDER BY movConta.datavencimento DESC");
        }
    }

    private void ordernacaoAntigo(StringBuffer sql, FiltroLancamentosTO filtro) {
        if (filtro.isAgruparPorPlanoDeContasCentroCusto()){
            sql.append("ORDER BY (pc.codigoPlanoContas || ' - ' || pc.nome)");
        }else{
            sql.append(" ORDER BY mc.datavencimento DESC");
        }
    }

    @Override
    public int obterNrParcela(Integer codigo) throws Exception {
        ResultSet resultSet = criarConsulta("SELECT nrparcela FROM movconta WHERE codigo = " + codigo, con);
        if (resultSet.next()) {
            return resultSet.getInt("nrparcela");
        } else {
            return 0;
        }
    }

    public void adicionarLote(Integer movConta, Integer lote) throws Exception{
        executarConsulta("UPDATE movconta SET lote = "+lote+" WHERE codigo = "+movConta, con);
    }

    public void excluirPorLote(int lote) throws  Exception{
        ResultSet resultSet = criarConsulta("SELECT codigo FROM movconta WHERE lote = " + lote, con);
        while(resultSet.next()){
            MovContaVO mconta = new MovContaVO();
            mconta.setCodigo(resultSet.getInt("codigo"));
            excluirSemCommit(mconta,false, true);

        }
    }
    public MovContaVO consultarPorLote(int lote, int nivelMontarDados) throws Exception{
        ResultSet resultSet = criarConsulta("SELECT * FROM movconta WHERE lote = " + lote, con);
        boolean integracaoAlterData = getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isHabilitarExportacaoAlterData();
        if(resultSet.next()){
            return MovConta.montarDados(resultSet, nivelMontarDados, integracaoAlterData, con);
        }else{
            return new MovContaVO();
        }
    }

    public void salvarLoteAvulso(MovContaVO obj, LoteVO loteVO) throws Exception {
        try {
            con.setAutoCommit(false);

            Lote lote = new Lote(con);
            loteVO.setDataLancamento(Calendario.igual(loteVO.getDataDeposito(), Calendario.hoje())   ? Calendario.hoje() : loteVO.getDataDeposito());
            loteVO.setAvulso(true);
            lote.incluirSemCommit(loteVO, false);

            StringBuilder sql = new StringBuilder();
            sql.append("insert into movconta (descricao, conta, valor, dataquitacao, ");
            sql.append("datalancamento, tipooperacao, datavencimento, datacompetencia,  lote) ");
            sql.append("values ('', ?, ?, ?, ?, ?, ?, ?, ?)");
            PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
            int i = 1;
            sqlInserir.setInt(i++, obj.getContaVO().getCodigo());
            sqlInserir.setDouble(i++, obj.getValor());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(loteVO.getDataDeposito()));
            sqlInserir.setDate(i++, Uteis.getDataJDBC(Calendario.hoje()));
            sqlInserir.setInt(i++, TipoOperacaoLancamento.CUSTODIA.getCodigo());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(loteVO.getDataDeposito()));
            sqlInserir.setDate(i++, Uteis.getDataJDBC(loteVO.getDataDeposito()));
            sqlInserir.setInt(i++, loteVO.getCodigo());

            sqlInserir.execute();

            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public List<TotaisRecebidos> consultarRecebidos(Date inicio, Date fim, Integer empresa, Integer codigoCaixa, String contas) throws Exception{
        List<TotaisRecebidos> lista = new ArrayList<TotaisRecebidos>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select mc.descricao, mc.valor from movconta mc \n");
        sql.append(" INNER JOIN caixamovconta cmc ON cmc.movconta = mc.codigo \n");
        sql.append(" WHERE mc.apresentarnocaixa ");
        if(UteisValidacao.emptyNumber(codigoCaixa)){
            sql.append(" AND (mc.dataquitacao >= '"+Uteis.getDataJDBC(inicio)+" 00:00:00' ");
            sql.append(" AND mc.dataquitacao <= '"+Uteis.getDataJDBC(fim)+" 23:59:59') ");
            if(!UteisValidacao.emptyNumber(empresa)){
                sql.append("AND mc.empresa =  "+empresa);
            }
        }else{
            sql.append(" AND cmc.caixa = "+codigoCaixa);
        }
        if(!UteisValidacao.emptyString(contas)){
            sql.append(" AND conta IN ("+contas+")");
        }

        ResultSet consulta = criarConsulta(sql.toString(), con);
        RelatorioFechamentoCaixaTO rfc = new RelatorioFechamentoCaixaTO();
        while(consulta.next()){
            TotaisRecebidos tot = rfc.new TotaisRecebidos();
            tot.setSaldo(consulta.getDouble("valor"));
            tot.setDescricao(consulta.getString("descricao"));
            lista.add(tot);
        }
        Ordenacao.ordenarLista(lista, "descricao");
        return lista;
    }

    public void alterarPayloadPix(String payload, int codMovConta) throws SQLException {
        String sql = "UPDATE movconta SET payloadPix = ? where codigo = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setString(1, payload);
        stm.setInt(2, codMovConta);
        stm.execute();
    }

    public void alterarCodigoBarras(String codigoBarras, int codMovConta) throws SQLException {
        String sql = "UPDATE movconta SET codigoBarras = ? where codigo = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setString(1, codigoBarras);
        stm.setInt(2, codMovConta);
        stm.execute();
    }

    public void alterarCpfCnpjBeneficiario(String cpfOuCnpjBeneficiario, int codMovConta) throws SQLException {
        String sql = "UPDATE movconta SET cpfOuCnpjBeneficiario = ? where codigo = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setString(1, cpfOuCnpjBeneficiario);
        stm.setInt(2, codMovConta);
        stm.execute();
    }

    public void alterarContaDeConsumo(boolean contaDeConsumo, int codMovConta) throws SQLException {
        String sql = "UPDATE movconta SET contaDeConsumo = ? where codigo = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setBoolean(1, contaDeConsumo);
        stm.setInt(2, codMovConta);
        stm.execute();
    }

    public void prenderOuLiberarMovContaEmLoteDePagamento(boolean presaEmLoteDePagamento, Integer loteDePagamento, Integer codMovConta) throws Exception {
        String sql = "UPDATE movconta SET presaEmLoteDePagamento = ?, loteDePagamento = ? where codigo = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setBoolean(1, presaEmLoteDePagamento);
        if (UteisValidacao.emptyNumber(loteDePagamento)) {
            stm.setNull(2, Types.NULL);
        } else {
            stm.setInt(2, loteDePagamento);
        }
        stm.setInt(3, codMovConta);
        stm.execute();
    }

    public void alterarSomenteApresentarCaixa(MovContaVO movConta) throws SQLException{
        String sql = "UPDATE movconta SET apresentarnocaixa = ? where codigo = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setBoolean(1, movConta.getApresentarNoCaixa());
        stm.setInt(2, movConta.getCodigo());

        stm.execute();

    }

    public void alterarSomenteValor(MovContaVO movConta, Double novoValor) throws Exception{
        String sql = "UPDATE movconta SET valor = ?, dataUltimaAlteracao=? where codigo = ?; UPDATE movcontarateio SET valor = ? where movconta = ?;";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setDouble(1, novoValor);
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        stm.setInt(3, movConta.getCodigo());
        stm.setDouble(4, novoValor);
        stm.setInt(5, movConta.getCodigo());
        stm.execute();
    }

    public void gravarValorAntesDaConciliacao(MovContaVO movContaVO) throws Exception{
        //necessário alterar a coluna valororiginalalterado também, pois ela é mostrada na coluna valorPrevisto na lsita de contas a pagar
        String sql = "UPDATE movconta SET valorOriginalAntesDaConciliacao = " + movContaVO.getValorOriginalAntesDaConciliacao()
                + ", valororiginalalterado = " + movContaVO.getValorPago()
                + " WHERE codigo = " + movContaVO.getCodigo();
        PreparedStatement stm = con.prepareStatement(sql);
        stm.execute();
        registrarLogValorAntesDaConciliacao(movContaVO);
    }

    private void registrarLogValorAntesDaConciliacao(MovContaVO movContaVO)throws Exception{
        MovContaVO movContaVOAntesAlteracao = (MovContaVO) movContaVO.getObjetoVOAntesAlteracao();

        LogVO obj = new LogVO();
        obj.setChavePrimaria(movContaVO.getCodigo().toString());
        obj.setChavePrimariaEntidadeSubordinada(movContaVO.getCodigo().toString());
        obj.setNomeEntidade("MOVCONTA");
        obj.setNomeEntidadeDescricao("MOVCONTA");
        obj.setResponsavelAlteracao("PROCESSO-CONCILIACAO");

        obj.setNomeCampo("Valor Original Alterado");
        obj.setValorCampoAnterior(movContaVOAntesAlteracao.getValorOriginalAlterado() == null ? "0.0" : movContaVOAntesAlteracao.getValorOriginalAlterado().toString());
        obj.setValorCampoAlterado(movContaVO.getValorOriginalAlterado().toString());
        obj.setOperacao("ALTERAÇÃO");

        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        getFacade().getLog().incluirSemCommit(obj);

        obj.setNomeCampo("Valor Original Alterado Antes Concilicacao");
        obj.setValorCampoAnterior(movContaVOAntesAlteracao.getValorOriginalAntesDaConciliacao() == null ? "0.0" : movContaVOAntesAlteracao.getValorOriginalAntesDaConciliacao().toString());
        obj.setValorCampoAlterado(movContaVO.getValorOriginalAntesDaConciliacao().toString());
        obj.setOperacao("ALTERAÇÃO");

        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        getFacade().getLog().incluirSemCommit(obj);
    }

    @Override
    public void alterarSomenteDataQuitacaoPorLote(Date data, Integer lote, boolean dataLancamento) throws Exception{
        String sql = "UPDATE movconta SET "+(dataLancamento ? "dataquitacao" : "datalancamento" )+" = ?,  dataUltimaAlteracao=?  where lote = ?;";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setTimestamp(1, Uteis.getDataHoraJDBC(data, Uteis.getHoraAtual()));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        stm.setInt(3, lote);
        stm.execute();
    }

    public List<RelatorioFechamentoCaixaTO.TotaisFormaPagamento> totalizarPorFormasPagamento(Date inicio, Date fim, Integer empresa, Integer caixa, String contas) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT descricao, sum(entrada) as entrada, sum(saida) as saida FROM ( \n");
        sql.append(" SELECT fp.descricao, sum(mcr.valor) AS entrada, 0.0 as saida FROM formapagamento fp \n");
        sql.append(" INNER JOIN movcontarateio mcr ON mcr.formapagamento = fp.codigo AND mcr.tipoes = "+TipoES.ENTRADA.getCodigo()+" \n");

        if(!UteisValidacao.emptyNumber(caixa)){
            sql.append(" INNER JOIN caixamovconta cmc ON cmc.movconta = mcr.movconta AND cmc.caixa = "+caixa+" \n");
        }else{
            sql.append(" INNER JOIN movconta mc ON mc.codigo = mcr.movconta \n");
            if (!UteisValidacao.emptyString(contas)) {
                sql.append(" AND mc.conta IN (" + contas + ") ");
            }

            sql.append(" INNER JOIN conta c ON c.codigo = mc.conta \n");
            sql.append(" AND (mc.dataquitacao >= '"+Uteis.getDataJDBC(inicio)+" 00:00:00' ");
            sql.append(" AND mc.dataquitacao <= '"+Uteis.getDataJDBC(fim)+" 23:59:59') ");
            if(!UteisValidacao.emptyNumber(empresa)){
                sql.append("AND c.empresa =  "+empresa);
            }
        }


        sql.append(" GROUP BY fp.descricao \n");
        sql.append(" UNION ALL  \n");
        sql.append(" SELECT fp.descricao, 0.0 as entrada, sum(mcr.valor) as saida FROM formapagamento fp \n");
        sql.append(" INNER JOIN movcontarateio mcr ON mcr.formapagamento = fp.codigo AND mcr.tipoes = "+TipoES.SAIDA.getCodigo()+" \n");

        if(!UteisValidacao.emptyNumber(caixa)){
            sql.append(" INNER JOIN caixamovconta cmc ON cmc.movconta = mcr.movconta AND cmc.caixa = "+caixa+" \n");
        }else{
            sql.append(" INNER JOIN movconta mc ON mc.codigo = mcr.movconta \n");
            if (!UteisValidacao.emptyString(contas)) {
                sql.append(" AND mc.conta IN (" + contas + ") ");
            }

            sql.append(" INNER JOIN conta c ON c.codigo = mc.conta \n");
            sql.append(" AND (mc.dataquitacao >= '"+Uteis.getDataJDBC(inicio)+" 00:00:00' ");
            sql.append(" AND mc.dataquitacao <= '"+Uteis.getDataJDBC(fim)+" 23:59:59') ");
            if(!UteisValidacao.emptyNumber(empresa)){
                sql.append("AND c.empresa =  "+empresa);
            }
        }
        sql.append(" GROUP BY fp.descricao) AS cons GROUP BY descricao");

        List<RelatorioFechamentoCaixaTO.TotaisFormaPagamento> lista = new ArrayList<RelatorioFechamentoCaixaTO.TotaisFormaPagamento>();
        RelatorioFechamentoCaixaTO rfc = new RelatorioFechamentoCaixaTO();
        ResultSet consulta = criarConsulta(sql.toString(), con);
        while(consulta.next()){
            TotaisFormaPagamento totaisFormaPagamento = rfc.new TotaisFormaPagamento(consulta.getString("descricao"), consulta.getDouble("entrada"), consulta.getDouble("saida"));
            lista.add(totaisFormaPagamento);
        }
        Map<String, TotaisFormaPagamento> mapa = new HashMap<String, TotaisFormaPagamento>();

        StringBuilder sql2 = new StringBuilder();
        sql2.append(" SELECT mcr.descricao, sum(mcr.valor) as valor, mcr.tipoes FROM movcontarateio mcr \n");
        if(!UteisValidacao.emptyNumber(caixa)){
            sql2.append(" INNER JOIN caixamovconta cmc ON cmc.movconta = mcr.movconta AND cmc.caixa = "+caixa);

        }else{
            sql2.append(" INNER JOIN movconta mc ON mc.codigo = mcr.movconta \n");
            if (!UteisValidacao.emptyString(contas)) {
                sql2.append(" AND mc.conta IN (" + contas + ") ");
            }
            sql2.append(" INNER JOIN conta c ON c.codigo = mc.conta \n");
            sql2.append(" AND (mc.dataquitacao >= '"+Uteis.getDataJDBC(inicio)+" 00:00:00' ");
            sql2.append(" AND mc.dataquitacao <= '"+Uteis.getDataJDBC(fim)+" 23:59:59') ");
            if(!UteisValidacao.emptyNumber(empresa)){
                sql2.append("AND c.empresa =  "+empresa);
            }
        }
        sql2.append(" WHERE mcr.formapagamento is null ");
        sql2.append(" GROUP BY mcr.descricao, tipoes ");


        ResultSet consulta2 = criarConsulta(sql2.toString(), con);
        while(consulta2.next()){
            TotaisFormaPagamento totaisFormaPagamento = mapa.get(consulta2.getString("descricao"));
            TipoES tipo = TipoES.getTipoPadrao(consulta2.getInt("tipoes"));
            if(totaisFormaPagamento == null){
                totaisFormaPagamento = rfc.new TotaisFormaPagamento(consulta2.getString("descricao"),
                        tipo.equals(TipoES.ENTRADA) ? consulta2.getDouble("valor") : 0.0,
                        tipo.equals(TipoES.SAIDA) ? consulta2.getDouble("valor") : 0.0);
                totaisFormaPagamento.setSaldo(totaisFormaPagamento.getEntrada()-totaisFormaPagamento.getSaida());
                mapa.put(consulta2.getString("descricao"), totaisFormaPagamento);
            }else{
                totaisFormaPagamento.setEntrada(tipo.equals(TipoES.ENTRADA) ? consulta2.getDouble("valor") : totaisFormaPagamento.getEntrada());
                totaisFormaPagamento.setSaida(tipo.equals(TipoES.SAIDA) ? consulta2.getDouble("valor") : totaisFormaPagamento.getSaida());
                totaisFormaPagamento.setSaldo(totaisFormaPagamento.getEntrada()-totaisFormaPagamento.getSaida());
            }
        }
        lista.addAll(mapa.values());
        Ordenacao.ordenarLista(lista, "descricao");
        return lista;
    }

    public List<ItemRelatorioFechamentoCaixaTO> listarMovimentacoes(Date inicio, Date fim, Integer conta, Integer caixa, Integer empresa, String contas) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT DISTINCT ON (mc.codigo) mc.codigo, mc.dataquitacao as dia, p.nome as favorecido, mc.tipooperacao, mcr.descricao, mcr.tipoes, mcr.valor \n");
        sql.append(" FROM movconta mc \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = mc.pessoa \n");
        sql.append(" INNER JOIN movcontarateio mcr ON mc.codigo = mcr.movconta \n");
        sql.append(" INNER JOIN conta c ON c.codigo = mc.conta \n");
        sql.append(" INNER JOIN caixamovconta cmc ON mc.codigo = cmc.movconta \n");

        sql.append(" WHERE (mc.dataquitacao >= '"+Uteis.getDataJDBC(inicio)+" 00:00:00' ");
        sql.append(" AND mc.dataquitacao <= '"+Uteis.getDataJDBC(fim)+" 23:59:59') ");
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append("AND c.empresa =  "+empresa);
        }
        if(!UteisValidacao.emptyNumber(conta)){
            sql.append(" AND mc.conta = "+conta);
        }
        if(!UteisValidacao.emptyNumber(caixa)){
            sql.append(" AND cmc.caixa = " +caixa);
        }

        if (!UteisValidacao.emptyString(contas)) {
            sql.append(" AND mc.conta IN (" + contas + ") ");
        }

        List<ItemRelatorioFechamentoCaixaTO> lista = new ArrayList<ItemRelatorioFechamentoCaixaTO>();
        ResultSet consulta = criarConsulta(sql.toString(), con);
        while(consulta.next()){
            ItemRelatorioFechamentoCaixaTO ifc = new ItemRelatorioFechamentoCaixaTO();
            ifc.setDescricao(consulta.getString("descricao"));
            ifc.setDia(consulta.getTimestamp("dia"));
            ifc.setFavorecido(consulta.getString("favorecido"));
            ifc.setMovimentacao(TipoOperacaoLancamento.getTipoOperacaoLancamento(consulta.getInt("tipooperacao")).getDescricaoCurta());
            ifc.setTipo(TipoES.getTipoPadrao(consulta.getInt("tipoes")));
            ifc.setValor(consulta.getDouble("valor"));
            lista.add(ifc);
        }
        Ordenacao.ordenarLista(lista, "dia");
        return lista;
    }

    public List<MovContaVO> consultarMovContaRelacionadoPorLote(MovContaVO movConta) throws Exception{
        String sql = "select * from movconta where codigo <> "+movConta.getCodigo()+" and lote = "+movConta.getLote().getCodigo();
        ResultSet consulta = criarConsulta(sql, con);
        return montarDadosConsulta(consulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    public List<MovContaVO> consultarMovContaRelacionados(MovContaVO movConta) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct mc.* from movconta mc \n");
        sql.append(" inner join lote l on mc.lote = l.codigo \n");
        sql.append(" inner join chequecartaolote ccl on ccl.lote = l.codigo \n");
        sql.append(" WHERE (ccl.cheque in (select cheque from chequecartaolote where lote = "+movConta.getLote().getCodigo()+") \n");
        sql.append(" OR ccl.cartao in (select cartao from chequecartaolote where lote = "+movConta.getLote().getCodigo()+")) \n");
//    	sql.append(" AND l.datalancamento > '"+Uteis.getDataJDBCTimestamp(movConta.getLote().getDataLancamento()));
        sql.append(" AND l.codigo > "+movConta.getLote().getCodigo()+" and mc.codigo <> "+movConta.getCodigo()+" order by codigo ");
        ResultSet consulta = criarConsulta(sql.toString(), con);
        return montarDadosConsulta(consulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    @Override
    public void gravarRetiradaRecebivelLote(Date data, List<ChequeTO> cheques,
                                            List<CartaoCreditoTO> cartoes,
                                            LoteVO lote, String observacao,
                                            UsuarioVO responsavel,
                                            int codigoCaixa) throws Exception{
        try {
            con.setAutoCommit(false);
            if (UteisValidacao.emptyList(cheques) && UteisValidacao.emptyList(cartoes)) {
                throw new Exception("Nenhum recebível informado para retirada.");
            }
            lote = getFacade().getLote().consultarPorChavePrimaria(lote.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            MovContaVO retirada = new MovContaVO();
            retirada.setTemLote(false);
            retirada.setContaVO(new ContaVO(lote.getCodigoContaContido()));
            retirada.setDescricao("Retirada de " + (UteisValidacao.emptyList(cheques) ? "cartões" : "cheques") + " do lote " + lote.getCodigo() + "-" + lote.getDescricao());
            retirada.setContaOrigem(null);
            retirada.setTipoOperacaoLancamento(TipoOperacaoLancamento.RETIRADA_RECEBIVEL_LOTE);
            retirada.setApresentarNoCaixa(false);
            retirada.setMovContaRateios(new ArrayList<MovContaRateioVO>());
            retirada.setLiquido(false);
            retirada.setLote(lote);
            retirada.setValorLiquido(0.0);
            retirada.setUsuarioVO(responsavel);
            retirada.setObservacoes(observacao);
            retirada.setPessoaVO(getFacade().getPessoa().consultarPessoaEmpresaFinan(lote.getEmpresa()));
            retirada.setEmpresaVO(lote.getEmpresa());
            retirada.setDataCompetencia(data);
            retirada.setDataVencimento(data);
            retirada.setDataQuitacao(data);
            retirada.setDataLancamento(data);
            Double valor = 0.0;
            for (ChequeTO cheque : cheques) {
                valor += cheque.getValor();
            }
            for (CartaoCreditoTO cartao : cartoes) {
                valor += cartao.getValor();
            }
            retirada.setValor(valor);
            retirada.setValorPago(valor);
            MovContaRateioVO rateio = new MovContaRateioVO();
            rateio.setDescricao(retirada.getDescricao());
            rateio.setValor(valor);
            rateio.setTipoES(TipoES.SAIDA);
            retirada.getMovContaRateios().add(rateio);
            incluirSemCommit(retirada, codigoCaixa, false, null);


            getFacade().getCheque().incluirChequeRetiradoLote(cheques, lote.getCodigo(), retirada.getCodigo());
            getFacade().getCartaoCredito().incluirCartaoRetiradoLote(cartoes, lote.getCodigo(), retirada.getCodigo());
            for (ChequeTO cheque : cheques) {
                removerRecebivel(lote, null, cheque, 0);
                cheque.setChequeEscolhido(false);
            }
            for (CartaoCreditoTO cartao : cartoes) {
                removerRecebivel(lote, cartao, null, 0);
                cartao.setCartaoEscolhido(false);
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }


    }

    @Override
    public void gravarRetiradaAutomaticaRecebivel(ContaVO conta, UsuarioVO responsavel,
                                                  String descricao, String observacao, Date data,
                                                  Integer codigoCaixa) throws Exception {
        MovContaVO retirada = new MovContaVO();
        retirada.setRetiradaAutomaticaRecebivelOrigemCancelamento(true);
        retirada.setTemLote(false);
        retirada.setContaVO(conta);
        retirada.setDescricao(descricao);
        retirada.setContaOrigem(null);
        retirada.setTipoOperacaoLancamento(TipoOperacaoLancamento.RETIRADA_RECEBIVEL_LOTE);
        retirada.setApresentarNoCaixa(false);
        retirada.setMovContaRateios(new ArrayList<MovContaRateioVO>());
        retirada.setLiquido(false);
        retirada.setValorLiquido(0.0);
        retirada.setUsuarioVO(responsavel);
        retirada.setObservacoes(observacao);
        retirada.setPessoaVO(getFacade().getPessoa().consultarPessoaEmpresaFinan(conta.getEmpresa()));
        retirada.setEmpresaVO(conta.getEmpresa());
        retirada.setDataCompetencia(data);
        retirada.setDataVencimento(data);
        retirada.setDataQuitacao(data);
        retirada.setDataLancamento(data);

        retirada.setValor(conta.getValorRetiradaAutomatica());

        MovContaRateioVO rateio = new MovContaRateioVO();
        rateio.setDescricao(retirada.getDescricao());
        rateio.setValor(conta.getValorRetiradaAutomatica());
        rateio.setTipoES(TipoES.SAIDA);
        retirada.getMovContaRateios().add(rateio);
        incluirSemCommit(retirada, codigoCaixa, false, null);


        for (ChequeVO cheque : conta.getChequesRetirar()) {
            removerRecebivel(null, null, cheque.toTO(), retirada.getCodigo().intValue());
            cheque.setChequeEscolhido(false);
        }
        for (CartaoCreditoVO cartao : conta.getCartaoRetirar()) {
            removerRecebivel(null, cartao.toTO(), null, retirada.getCodigo().intValue());
            cartao.setCartaoEscolhido(false);
        }
    }

    public void removerRecebivel(LoteVO loteAtual, CartaoCreditoTO cartao, ChequeTO cheque, int movconta) throws Exception{
        String codigos = "";
        if(cartao == null){
            codigos = UteisValidacao.emptyString(cheque.getCodigosComposicao()) ?  String.valueOf(cheque.getCodigo()) : cheque.getCodigo()+","+cheque.getCodigosComposicao();
        }else{
            codigos = UteisValidacao.emptyString(cartao.getCodigosComposicao()) ?  String.valueOf(cartao.getCodigo()) : cartao.getCodigo()+","+cartao.getCodigosComposicao();
        }

        if(loteAtual != null){
            executarConsulta("DELETE FROM chequecartaolote WHERE "
                    +(cartao == null ? "cheque ": "cartao  ")
                    + " IN ("+codigos+") AND lote = "+loteAtual.getCodigo(), con);
        }
        ResultSet query = criarConsulta(" SELECT * from chequecartaolote WHERE " + (cartao == null ? "cheque  ": "cartao ") + " IN ("+codigos+")", con);
        while(query.next()){
            if(cartao == null){
                getFacade().getCheque().incluirChequeRetiradoSemCommit(cheque, query.getInt("lote"), movconta);
                movconta = 0;

            }else{
                getFacade().getCartaoCredito().incluirCartaoRetiradoSemCommit(cartao, query.getInt("lote"), movconta);
                movconta = 0;
            }
            executarConsulta("DELETE FROM chequecartaolote WHERE "
                    +(cartao == null ? "cheque ": "cartao ")+ " IN ("+codigos+") AND lote = "+query.getInt("lote"), con);
        }
        if (cheque != null) {
            executarConsulta("UPDATE historicocheque SET datafim = '"
                    + Uteis.getDataJDBCTimestamp(Calendario.hoje()) + "' "
                    + " WHERE codigo = (select max(codigo) from historicocheque where cheque IN ("+codigos+"))", con);
        }
        if(cartao!=null){
            executarConsulta("UPDATE historicocartao SET datafim = '"
                    + Uteis.getDataJDBCTimestamp(Calendario.hoje()) + "' "
                    + " WHERE codigo = (select max(codigo) from historicocartao where cartao IN ("+codigos+"))", con);
        }
        if (cartao == null) {
            ResultSet consulta = criarConsulta("SELECT cheque.dataoriginal, mp.datalancamento, e.nrdiaschequeavista "
                    + " FROM cheque inner join movpagamento mp on mp.codigo = cheque.movpagamento  "
                    + " inner join empresa e on mp.empresa = e.codigo"
                    + " WHERE cheque.codigo = " + cheque.getCodigo(), con);
            if (consulta.next()) {
                try {
                    Integer nrDiasAVista = consulta.getInt("nrdiaschequeavista");
                    Date dataLancamento = consulta.getDate("datalancamento");
                    Date dataOriginal = consulta.getDate("dataoriginal");
                    String vistaouprazo = dataOriginal.compareTo(Uteis.somarDias(dataLancamento, nrDiasAVista)) == 1 ? "PR" : "AV";
                    executarConsulta("UPDATE cheque "
                            + " SET datacompesancao = dataoriginal, vistaouprazo = '" + vistaouprazo + "' WHERE dataoriginal is not null and codigo in (" + codigos + ")", con);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            executarConsulta("UPDATE cartaocredito SET datacompesancao = dataoriginal WHERE dataoriginal is not null and codigo in (" + codigos + ")", con);
        }



    }

    public List<MovContaVO> verConjuntoPagamentosResumido(MovContaVO origem) throws Exception{
        ResultSet resultSet = criarConsulta("SELECT mc.codigo, mc.tipooperacao, p.nome as pessoa, mc.descricao, mc.valor, mc.dataUltimaAlteracao, mc.valorpago, mc.valororiginalalterado " +
                " FROM movconta mc " +
                " INNER JOIN pessoa p ON mc.pessoa = p.codigo " +
                " WHERE mc.codigo IN ("+origem.getConjuntoPagamento()+") AND mc.codigo <> "+origem.getCodigo(), con);
        return montarDadosConsulta(resultSet, Uteis.NIVELMONTARDADOS_PAGAMENTOS_CONJUNTO_RESUMIDOS, con);
    }

    public boolean getUsarMovimentacao() throws Exception{
        ConfiguracaoFinanceiro configDao = new ConfiguracaoFinanceiro(this.con);
        ConfiguracaoFinanceiroVO confFinan = configDao.consultar();
        return confFinan.getUsarMovimentacaoContas();
    }


    public List<GenericoTO> consultarGenerico(TipoOperacaoLancamento tipo, Date inicio, Date fim, Integer empresa) throws Exception{
        List<GenericoTO> lista = new ArrayList<GenericoTO>();
        StringBuilder sql = new StringBuilder("SELECT mov.codigo, mov.descricao, mov.datavencimento, mov.valor FROM movconta mov ");
        sql.append(" WHERE mov.datavencimento BETWEEN ? AND ? and dataquitacao is null ");
        if(tipo != null){
            sql.append(" AND tipooperacao = ?");
        }
        if(empresa != null && empresa > 0){
            sql.append(" AND empresa = ?");
        }
        sql.append(" order by datavencimento");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 1;
        stm.setTimestamp(i++, Uteis.getDataHoraJDBC(inicio, "00:00:00"));
        stm.setTimestamp(i++, Uteis.getDataHoraJDBC(fim, "23:59:59"));
        if(tipo != null){
            stm.setInt(i++, tipo.getCodigo());
        }
        if(empresa != null && empresa > 0){
            stm.setInt(i++, empresa);
        }
        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            lista.add(new GenericoTO(rs.getString("descricao"), Uteis.getData(rs.getDate("datavencimento")),
                    rs.getInt("codigo"), rs.getDouble("valor")));
        }
        return lista;

    }

    public LogVO alterarCentroCustoPlanoContaTaxaCartaoBoleto(Integer planoConta, Integer centroCusto, UsuarioVO user, boolean boleto) throws Exception {
        String sqlTaxaCartao = "SELECT COUNT(1) AS nrRegistros FROM (select codigo from movconta where descricao  like 'Taxa administrativa da operadora de cartão -%') AS cont";
        if(boleto){
            sqlTaxaCartao = "SELECT COUNT(1) AS nrRegistros FROM (select codigo from movconta where descricao  like 'Taxa administrativa do boleto -%') AS cont";
        }
        ResultSet rs = criarConsulta(sqlTaxaCartao, con);
        rs.next();
        int nrRegistros = rs.getInt("nrRegistros");

        String sqlUpdate = "update movcontarateio  set planoconta  = ?,centrocusto = ? where  movconta in (select codigo from movconta where descricao  like 'Taxa administrativa da operadora de cartão -%')";
        if(boleto){
            sqlUpdate = "update movcontarateio  set planoconta  = ?,centrocusto = ? where  movconta in (select codigo from movconta where descricao  like 'Taxa administrativa do boleto -%')";
        }
        PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate);
        if (planoConta != null && planoConta > 0) {
            sqlAlterar.setInt(1, planoConta);
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (centroCusto != null && centroCusto > 0) {
            sqlAlterar.setInt(2, centroCusto);
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.execute();

        LogVO log = new LogVO();
        log.setNomeEntidade("MOVCONTA");
        log.setNomeEntidadeDescricao("MovConta");
        log.setResponsavelAlteracao(user.getNome());
        log.setUserOAMD(user.getUserOamd());
        log.setDataAlteracao(Calendario.hoje());
        log.setNomeCampo("Plano Conta/Centro de Custo");
        log.setValorCampoAlterado("Realizada operação de atualização do plano de contas e centro de custos para taxa de "+(boleto ? "boleto." : "operadoras de cartão.")+"<br/>"
                + nrRegistros + " registros atualizados com sucesso em " + Uteis.getDataComHora(Calendario.hoje()));
        return log;
    }

    @Override
    public void lancarSaidaDevolucaoCancelamento(PessoaVO pessoa, EmpresaVO empresa, UsuarioVO usuario,
                                                 Double valor, Date dataDevolucao, String tipo, Integer movProduto) throws Exception {
        MovContaVO saida = new MovContaVO();
        saida.setPessoaVO(pessoa);
        saida.setUsuarioVO(usuario);
        saida.setEmpresaVO(empresa);
        saida.setDescricao("DEVOLUÇÃO DE DINHEIRO - " + pessoa.getNomeAbreviado());
        saida.setObservacoes("Devolução de dinheiro por motivo de cancelamento.");
        saida.setValor(valor);
        saida.setDataCompetencia(dataDevolucao);
        saida.setDataVencimento(dataDevolucao);
        saida.setDataLancamento(dataDevolucao);
        saida.setTipoOperacaoLancamento(TipoOperacaoLancamento.PAGAMENTO);
        saida.setApresentarNoCaixa(false);
        saida.setMovContaRateios(new ArrayList<MovContaRateioVO>());
        saida.setMovProduto(movProduto);

        RateioIntegracao rateioDAO = new RateioIntegracao(con);
        List<RateioIntegracaoTO> rateios = rateioDAO.obterRateiosDevolucaoDinheiro(tipo);
        rateioDAO = null;
        for (RateioIntegracaoTO rateio : rateios) {
            if (UteisValidacao.emptyNumber(rateio.getCodigoCentroCustos())) {
                saida.getMovContaRateios().add(addNovo(saida, rateio));
            }
        }
        for (RateioIntegracaoTO rateio : rateios) {
            if (UteisValidacao.emptyNumber(rateio.getCodigoPlanoContas())) {
                boolean achou = false;
                for (MovContaRateioVO saidaRateio : saida.getMovContaRateios()) {
                    if (saidaRateio.getCentroCustoVO() == null
                            || saidaRateio.getCentroCustoVO().getCodigo() == null
                            || saidaRateio.getCentroCustoVO().getCodigo().intValue() == 0) {
                        achou = true;
                        saidaRateio.setCentroCustoVO(new CentroCustoTO());
                        saidaRateio.getCentroCustoVO().setCodigo(UteisValidacao.emptyNumber(rateio.getCodigoCentroCustos()) ? 0 : rateio.getCodigoCentroCustos());
                    }
                }

                if (!achou) {
                    saida.getMovContaRateios().add(addNovo(saida, rateio));
                }
            }
        }


        if (rateios == null || rateios.isEmpty()) {
            MovContaRateioVO saidaRateio = new MovContaRateioVO();
            saidaRateio.setValor(saida.getValor());
            saidaRateio.setTipoES(TipoES.SAIDA);
            saidaRateio.setDescricao(saida.getDescricao());
            saida.getMovContaRateios().add(saidaRateio);
        }

        incluirSemCommit(saida, 0, false, null);
    }

    private MovContaRateioVO addNovo(MovContaVO saida, RateioIntegracaoTO rateio) {
        MovContaRateioVO saidaRateio = new MovContaRateioVO();
        saidaRateio.setValor(saida.getValor() * rateio.getPercentagem() / 100);
        saidaRateio.setCentroCustoVO(new CentroCustoTO());
        saidaRateio.getCentroCustoVO().setCodigo(UteisValidacao.emptyNumber(rateio.getCodigoCentroCustos()) ? 0 : rateio.getCodigoCentroCustos());
        saidaRateio.setPlanoContaVO(new PlanoContaTO());
        saidaRateio.getPlanoContaVO().setCodigo(UteisValidacao.emptyNumber(rateio.getCodigoPlanoContas()) ? 0 : rateio.getCodigoPlanoContas());
        saidaRateio.setTipoES(TipoES.SAIDA);
        saidaRateio.setDescricao(saida.getDescricao());
        return saidaRateio;
    }

    @Override
    public List<MovContaVO> consultarDevolucoes(Date faturamentoInicio, Date faturamentoFim,
                                                Date compensacaoInicio, Date compensacaoFim, Integer empresa,
                                                String nomePessoa, String nomeUsuario, String cpf, String matricula) throws Exception{
        String sql = "SELECT movconta.*,pessoa.cfp as cpfPessoa, cli.matricula FROM movconta  \n"+
                " inner join pessoa on pessoa.codigo = movconta.pessoa \n"+
                " LEFT join cliente cli on pessoa.codigo = cli.pessoa \n"+
                " inner join usuario on usuario.codigo = movconta.usuario WHERE movproduto IS NOT NULL\n";
        if(faturamentoInicio != null && faturamentoFim != null){
            sql += " \n and datalancamento between ? and ? ";
        }
        if(compensacaoInicio != null && compensacaoFim != null){
            sql += "\n and dataquitacao between ? and ? ";
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sql += "\n and movconta.empresa = ? ";
        }
        if(!UteisValidacao.emptyString(nomePessoa)){
            // VALIDAÇÃO DE SEGURANÇA: Verificar se nomePessoa é seguro
            if (!Uteis.isValidStringValue(nomePessoa)) {
                throw new SecurityException("Nome da pessoa contém caracteres não permitidos");
            }
            sql += "\n and pessoa.nome LIKE ? ";
        }
        if(!UteisValidacao.emptyString(cpf)){
            // VALIDAÇÃO DE SEGURANÇA: Verificar se CPF é seguro
            if (!Uteis.isValidStringValue(cpf)) {
                throw new SecurityException("CPF contém caracteres não permitidos");
            }
            sql += "\n and pessoa.cfp LIKE ? ";
        }
        if(!UteisValidacao.emptyString(matricula)){
            // VALIDAÇÃO DE SEGURANÇA: Verificar se matrícula é um número válido
            if (!Uteis.isValidInteger(matricula)) {
                throw new SecurityException("Matrícula deve ser um número válido");
            }
            sql += "\n and cli.codigomatricula = ? ";
        }
        if(!UteisValidacao.emptyString(nomeUsuario)){
            sql += "\n and usuario.nome LIKE ? \n";
        }

        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            if(faturamentoInicio != null && faturamentoFim != null){
                pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(faturamentoInicio));
                pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(faturamentoFim));
            }
            if(compensacaoInicio != null && compensacaoFim != null){
                pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(compensacaoInicio));
                pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(compensacaoFim));
            }
            if(!UteisValidacao.emptyNumber(empresa)){
                pst.setInt(++i, empresa);
            }
            if(!UteisValidacao.emptyString(nomePessoa)){
                pst.setString(++i, nomePessoa.toUpperCase() + "%");
            }
            if(!UteisValidacao.emptyString(cpf)){
                pst.setString(++i, cpf.toUpperCase());
            }
            if(!UteisValidacao.emptyString(matricula)){
                pst.setInt(++i, Integer.valueOf(matricula));
            }
            if(!UteisValidacao.emptyString(nomeUsuario)){
                pst.setString(++i, nomeUsuario.toUpperCase());
            }

            try (ResultSet rs = pst.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
        }
    }

    public void gravarNFSEEmitida(int codigo) throws Exception {
        executarConsulta("UPDATE movconta SET nfseemitida = true WHERE codigo = " + codigo, con);
    }

    public void desmarcarNFSEEmitida(int codigo) throws Exception {
        executarConsulta("UPDATE movconta SET nfseemitida = false WHERE codigo = " + codigo, con);
    }

    public void gravarNFCEEmitida(int codigo) throws Exception {
        executarConsulta("UPDATE movconta SET nfceemitida = true WHERE codigo = " + codigo, con);
    }

    @Override
    public MovContaVO consultarPorMovProdutoDevolucao(int codigoMovProduto, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT movconta.*, pessoa.nome nomepessoa, conta.descricao descricaoconta, empresa.nome nomeempresa  ");
        sql.append(" FROM movconta ");
        sql.append(" inner join empresa on empresa.codigo = movconta.empresa ");
        sql.append(" left join pessoa on pessoa.codigo = movconta.pessoa ");
        sql.append(" left join conta on conta.codigo = movconta.conta ");
        sql.append(" where movconta.movproduto = ");
        sql.append(codigoMovProduto);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        MovContaVO movContaVo = null;
        boolean integracaoAlterData = getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isHabilitarExportacaoAlterData();
        if (tabelaResultado.next()) {
            movContaVo = montarDados(tabelaResultado, nivelMontarDados,integracaoAlterData, con);
        }
        return movContaVo;
    }

    @Override
    public void gravarMovimentacaoFinanceiraPagamento(Integer movPagamento, Integer movConta) throws SQLException{
        String sql = "INSERT INTO movimentacaofinanceirapagamento (movconta, movpagamento) VALUES (?,?);";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, movConta);
        stm.setInt(2, movPagamento);
        stm.execute();
    }

    @Override
    public List<HistoricoCartaoVO> consultarHistoricoCartaoDebito(MovPagamentoVO movPagamento) throws Exception{
        /*StringBuilder sql = new StringBuilder("SELECT mc.codigo AS movconta , histc.datainicio ,histc.datafim,histc.lote, \n");
        sql.append("c.descricao as conta,histc.movconta,histc.cartao,mc.descricao as DESC  FROM historicocartao histc \n");
        sql.append("INNER JOIN movconta mc ON mc.codigo = histc.movconta \n");
        sql.append("INNER JOIN conta c ON mc.conta = c.codigo \n");
        sql.append("INNER JOIN movimentacaofinanceirapagamento mfp ON mfp.movconta = mc.codigo AND mfp.movpagamento =");
        sql.append(movPagamento.getCodigo());
        sql.append(" order by datafim ASC,datainicio ");

        ResultSet rs = criarConsulta(sql.toString(), con);
        List<HistoricoCartaoVO> lista =  getFacade().getFinanceiro().getHistoricoCartao().montarDadosConsulta(rs,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return lista;*/
        StringBuilder sql = new StringBuilder("");
        sql.append("select mov.dataLancamento as dataInicio,  \n");
        sql.append("       LEAD(mov.dataLancamento) OVER (ORDER BY mov.dataLancamento asc) AS dataFim, \n");
        sql.append("       mov.lote, \n");
        sql.append("       conta.descricao as nomeConta, \n");
        sql.append("       conta.codigo as codigoConta, \n");
        sql.append("       mov.descricao as descricaoLancamento, \n");
        sql.append("       mov.tipoOperacao, \n");
        sql.append("       mov.codigo as codigoMovConta \n");
        sql.append("from movimentacaofinanceirapagamento movFinan \n");
        sql.append("inner join movConta mov on mov.codigo = movFinan.movConta \n");
        sql.append("inner join conta on conta.codigo = mov.conta \n");
        sql.append("where movPagamento = ").append(movPagamento.getCodigo());
        sql.append(" order by mov.dataLancamento desc  ");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<HistoricoCartaoVO> lista = new ArrayList<HistoricoCartaoVO>();
        while (rs.next()){
            HistoricoCartaoVO obj = new HistoricoCartaoVO();
            obj.setDataInicio(rs.getTimestamp("dataInicio"));
            obj.setDataFim(rs.getTimestamp("dataFim"));
            obj.getLote().setCodigo(rs.getInt("lote"));
            obj.getMovConta().setCodigo(rs.getInt("codigoMovConta"));
            obj.getMovConta().setContaVO(new ContaVO());
            obj.getMovConta().getContaVO().setCodigo(rs.getInt("codigoConta"));
            obj.getMovConta().getContaVO().setDescricao(rs.getString("nomeConta"));
            obj.getMovConta().setTipoOperacaoLancamento(TipoOperacaoLancamento.getTipoOperacaoLancamento(rs.getInt("tipoOperacao")));
            lista.add(obj);
        }
        return lista;
    }

    public void povoarMovimentacoesDebito() throws Exception{



    }

    public void finalizarAgendamento(MovContaVO movconta, UsuarioVO usuario) throws Exception {
        Date novaData = obterUltimoVencimentoMovContaAgendamento(movconta.getAgendamentoFinanceiro(), movconta.getCodigo());
        if(novaData == null){
            novaData = Calendario.hoje();
        }
        PreparedStatement stm = con.prepareStatement("UPDATE agendamentofinanceiro SET vencimentoultimaparcela = ?, " +
                "proximovencimento = ? WHERE codigo = ?");
        stm.setDate(1, Uteis.getDataJDBC(novaData));
        stm.setDate(2, Uteis.getDataJDBC(Uteis.somarDias(novaData, 1)));
        stm.setInt(3,movconta.getAgendamentoFinanceiro());
        stm.execute();

        LogVO obj = new LogVO();
        obj.setOperacao("ALTERAÇÃO");
        obj.setChavePrimaria(movconta.getAgendamentoFinanceiro()+"");
        obj.setNomeEntidade("AGENDAMENTOFINANCEIRO");
        obj.setNomeEntidadeDescricao("AGENDAMENTO FINANCEIRO");
        obj.setResponsavelAlteracao(usuario.getNome());
        obj.setUserOAMD(usuario.getUserOamd());
        obj.setNomeCampo("MENSAGEM");
        StringBuilder msg = new StringBuilder();
        msg.append("Agendamento foi encerrado: \n").append(Uteis.getData(novaData));
        obj.setValorCampoAlterado(msg.toString());
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        getFacade().getLog().incluir(obj);

    }
    public Integer consultarMovContaLotePagou(Integer codigoLote)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from movConta where lotePagouConta = ").append(codigoLote);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return rs.getInt("codigo");
        }
        return 0;
    }

    public List<MovContaVO> consultarContasPagamentoComLote(Integer codigoLote, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from movConta where lotePagouConta = ").append(codigoLote);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        return montarDadosConsulta(rs,nivelMontarDados,con);
    }

    public List<MovContaVO> consultarMovContaCompraEstoque(Integer compraEstoque, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from movConta where compraEstoque = ").append(compraEstoque);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<MovContaVO> movContaVOS = new ArrayList<>();
        while (rs.next()) {
            movContaVOS.add(montarDados(rs, nivelMontarDados, false, con));
        }
        return movContaVOS;
    }

    public Integer retornarCodigoCompraEstoquePorMovConta(Integer movConta) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select c.codigo from compra c \n");
        sql.append("inner join movconta mc on mc.compraestoque = c.codigo \n");
        sql.append("where mc.codigo = ").append(movConta);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        while (rs.next()) {
            return rs.getInt("codigo");
        }
        return null;
    }

    public boolean validarMovContaUltimaParcela(Integer codigomovconta) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT EXISTS(\r\n");
        sql.append(" select mc.codigo from movconta mc left join movconta mcagend \r\n");
        sql.append(" on mc.agendamentofinanceiro = mcagend.agendamentofinanceiro and mc.datavencimento < mcagend.datavencimento\r\n");
        sql.append(" inner join agendamentofinanceiro a on a.codigo = mc.agendamentofinanceiro \r\n");
        sql.append(" WHERE mcagend.codigo is null and mc.codigo = ").append(codigomovconta);
        sql.append(" and mc.agendamentofinanceiro is not null \r\n");
        sql.append(" ) as ultimo\r\n");
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if(resultSet.next()){
            return resultSet.getBoolean("ultimo");
        }
        return false;
    }

    public void validarDataBloqueio(MovContaVO movConta, boolean validarSomenteDataQuitacao) throws Exception{
        BloqueioCaixaInterfaceFacade bcDao = new BloqueioCaixa(con);
        BloqueioCaixaVO bloqueio = bcDao.consultarBloqueioAtual(movConta.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if(bloqueio != null){
            List<String> dts = new ArrayList<String>();
            if (validarSomenteDataQuitacao){
                if(movConta.getDataQuitacao() != null && Calendario.menor(movConta.getDataQuitacao(), bloqueio.getDataBloqueio())){
                    dts.add(",quitação");
                }
            } else if (movConta.getDataQuitacao() != null && (Calendario.menor(movConta.getDataQuitacao(), bloqueio.getDataBloqueio()))) {
                if ((movConta.getDataLancamento() != null) && (movConta.validarDataBloqueioParaDtLancamento()) && (Calendario.menor(movConta.getDataLancamento(), bloqueio.getDataBloqueio()))){
                    dts.add(",lançamento");
                }
                if  ((movConta.getDataCompetencia() != null) && (movConta.validarDataBloqueioParaDtCompetencia()) && (Calendario.menor(movConta.getDataCompetencia(), bloqueio.getDataBloqueio()))){
                    dts.add(",competência");
                }
                if ((movConta.validarDataBloqueioParaDtQuitacao())){
                    dts.add(",quitação");
                }
                if ((movConta.getDataVencimento() != null) && (movConta.validarDataBloqueioParaDtVencimento()) && (Calendario.menor(movConta.getDataVencimento(), bloqueio.getDataBloqueio()))){
                    dts.add(",vencimento");
                }

            }
            if(movConta.getDataQuitacao() != null && (Calendario.menor(movConta.getDataQuitacao(), bloqueio.getDataBloqueio()) && movConta.validarDataBloqueioParaValor())){ // contas quitadas antes do bloqueio, não podem ter valor alterado.
                throw new Exception("Conta quitada antes da data de bloqueio, não pode ter o valor alterado");
            }
            if(!dts.isEmpty()){
                String dt = "";
                for(String s : dts){
                    dt += s;
                }
                if(UteisValidacao.emptyNumber(movConta.getCodigo()) && movConta.getLote() != null){
                    movConta.getLote().setCodigo(0);
                }
                throw new Exception("Data"+(dts.size() > 1 ? "s":"")
                        +" de "+dt.replaceFirst(",", "") + " deve"+(dts.size() > 1 ? "m":"") +" ser maior"+(dts.size() > 1 ? "es":"")
                        +" que a data de bloqueio ("+Uteis.getData(bloqueio.getDataBloqueio())+" lançada por "+
                        bloqueio.getUsuarioResponsavel().getNome()+")."
                );
            }
        }
    }

    public Date obterUltimoVencimentoMovContaAgendamento(Integer agendamento, Integer movContaExcluir) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT max(datavencimento) as vencimento  from movconta where agendamentofinanceiro = ").append(agendamento);
        if(!UteisValidacao.emptyNumber(movContaExcluir)){
            sql.append(" and codigo <> ").append(movContaExcluir);
        }
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if(resultSet.next()){
            return resultSet.getDate("vencimento");
        }
        return null;
    }

    public boolean isDepositoAVouCD(Integer codigomovconta) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT EXISTS(\r\n");
        sql.append(" select m.codigo from movconta m  \r\n");
        sql.append(" inner join movpagamento pag on pag.movconta = m.codigo \r\n");
        sql.append(" inner join formapagamento fp on fp.codigo = pag.formapagamento and tipoformapagamento in('AV','CD')  \r\n");
        sql.append(" where m.codigo  = ").append(codigomovconta);
        sql.append(" and tipooperacao = ").append(TipoOperacaoLancamento.DEPOSITO.getCodigo());
        sql.append(" ) as existe\r\n");
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if(resultSet.next()){
            return resultSet.getBoolean("existe");
        }
        return false;
    }

    public void alterarSomenteChaveArquivo(MovContaVO obj) throws SQLException {
        String sql = "UPDATE movconta SET chavearquivoconta = ?, extencaoarquivoconta = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setString(++i, obj.getChaveArquivoConta());
            sqlAlterar.setString(++i, obj.getExtensaoArquivoContaMovConta());
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }

        String sql2 = "UPDATE movconta SET chavearquivocomprovante = ?, extencaoarquivocomprovante = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar2 = con.prepareStatement(sql2)) {
            int i = 0;
            sqlAlterar2.setString(++i, obj.getChaveArquivoComprovante());
            sqlAlterar2.setString(++i, obj.getExtensaoArquivoComprovanteMovConta());
            sqlAlterar2.setInt(++i, obj.getCodigo());
            sqlAlterar2.execute();
        }
    }

    public List<MovContaVO> consultarMovContaComValoresDivergentesDoMovContaRateio() throws Exception {

        String sql = "SELECT * FROM movconta m "
                + "where (SELECT SUM(valor) FROM movcontarateio m2 WHERE m2.movconta = m.codigo) <> m.valor";

        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        return (List<MovContaVO>) montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_MINIMOS, con);
    }

    public List<MovContaVO> consultarTelaCliente(Integer pessoa, int limit, int offset) throws Exception {
        StringBuilder sql = sqlConsultaPessoaTelaCliente(pessoa, false, limit, offset);
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, this.con);
            }
        }
    }

    public Integer quantidadePorPessoaTelaCliente(Integer pessoa) throws Exception {
        StringBuilder sql = sqlConsultaPessoaTelaCliente(pessoa, true, 0, 0);
        PreparedStatement statement = con.prepareStatement(sql.toString());
        ResultSet rs = statement.executeQuery();
        if (rs.next()) {
            return rs.getInt(1);
        } else {
            return 0;
        }
    }

    private StringBuilder sqlConsultaPessoaTelaCliente(Integer pessoa, boolean count, int limit, int offset) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        if (count) {
            sql.append("count(distinct m.codigo) \n");
        } else {
            sql.append("distinct m.* \n");
        }
        sql.append("from movconta m \n");
        sql.append("where m.identificadororigem ilike 'NSU|%' \n"); //buscar somente os estornos que foram lançados automaticamente
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("and m.pessoa = ").append(pessoa).append(" \n");
        }
        if (!count) {
            sql.append("ORDER BY m.codigo desc \n");
            sql.append("LIMIT ").append(limit).append(" \n");
            sql.append("OFFSET ").append(offset).append(" \n");
        }
        return sql;
    }

    public List<MovContaVO> consultarNaoConciliadosENaoQuitados(Date inicio, Date fim, String descricao, TipoOperacaoLancamento tipo, Integer empresa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select * from movConta \n");
        sql.append(" where tipooperacao = ").append(tipo.getCodigo() + " \n");
        sql.append(" and empresa = ").append(empresa + " \n");
        sql.append(" and dataquitacao is null \n");
        if (inicio != null && fim != null) {
            String whereVencimento = " AND datavencimento BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' "
                    + "AND '" + Uteis.getDataJDBC(fim) + " 23:59:59' \n";
            sql.append(whereVencimento);
        }
        if (!UteisValidacao.emptyString(descricao)) {
            sql.append(" AND descricao ILIKE '%").append(descricao).append("%' \n");
        }
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        return montarDados(rs, nivelMontarDados, con);
    }

    @Override
    public List<MovContaVO> consultarPorValorEVencimento(Date inicio, Date fim, Integer empresa, double valor, boolean contasPagar, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT codigo, valor ,descricao ,datavencimento \n");
        sql.append(" FROM movConta \n");
        sql.append(" WHERE empresa = ").append(empresa + " \n");
        sql.append(" AND dataquitacao is null \n");
        String whereVencimento = " AND datavencimento BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' "
                + "AND '" + Uteis.getDataJDBC(fim) + " 23:59:59' \n";
        sql.append(whereVencimento);
        sql.append(" AND valor = ").append(valor + " \n");

        if (contasPagar) {
            sql.append(" AND tipooperacao = ").append(TipoOperacaoLancamento.PAGAMENTO.getCodigo() + " \n");
        } else {
            sql.append(" AND tipooperacao = ").append(TipoOperacaoLancamento.RECEBIMENTO.getCodigo() + " \n");
        }

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());

        List<MovContaVO> movContas = new ArrayList<>();
        while (rs.next()) {
            MovContaVO movContaVO = new MovContaVO();
            movContaVO.setCodigo(rs.getInt("codigo"));
            movContaVO.setValor(rs.getDouble("valor"));
            movContaVO.setDescricao(rs.getString("descricao"));
            movContaVO.setDataVencimento(rs.getTimestamp("datavencimento"));
            movContas.add(movContaVO);
        }
        return movContas;
    }

    //Método criado a partir do gravarQuitacao(), pois foi necessário utilizar o controle de transação manual realizado no MovContaControle
    @Override
    public void gravarQuitacaoMultiplasContas(MovContaVO movConta, int codigoCaixa, List<PagamentoMovContaTO> formasQuitacao, boolean conciliacao) throws Exception {
        try {

            //apagar todos os movcontaRateioExistente pois irá incluir o novo no final
            List<MovContaRateioVO> rateiosAntigos = getFacade().getMovContaRateio().consultarPorMovConta(movConta.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            for (MovContaRateioVO rateio : rateiosAntigos) {
                getFacade().getMovContaRateio().excluirMovContasRateio(movConta.getCodigo());
            }

            //conciliacao deve alterar o valor original do lancamento
            if (conciliacao) {
                if (movConta.getObjetoVOAntesAlteracao() == null) {
                    movConta.setObjetoVOAntesAlteracao((MovContaVO) movConta.getClone(false));
                }
                alterarSomenteValor(movConta, movConta.getValorPago());
                gravarValorAntesDaConciliacao(movConta);

                //consultar novo obj alterado
                MovContaVO obj = consultarPorCodigo(movConta.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                movConta.setValor(obj.getValor());
            }

            executarConsulta("UPDATE lote SET pagamovconta = null WHERE pagamovconta = " + movConta.getCodigo(), con);
            executarConsulta("DELETE FROM cheque WHERE movconta = " + movConta.getCodigo(), con);

            LoteVO lotePagouConta = null;

            //setar conta
            if (getUsarMovimentacao()) {
                movConta.setContaVO(getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(
                        movConta.getContaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            // Alterar a data de quitação e a conta
            StringBuilder sql = new StringBuilder("UPDATE movconta SET dataquitacao = ?, dataUltimaAlteracao=?, ");
            sql.append("conta = ?, valor = ?, lotePagouConta = ?, valorpago = ? WHERE codigo = ?");
            PreparedStatement pst = con.prepareStatement(sql.toString());
            int i = 1;
            pst.setTimestamp(i++, Uteis.getDataJDBCTimestamp(movConta.getDataQuitacao()));
            pst.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveFKNull(pst, i++, movConta.getContaVO().getCodigo());
            pst.setDouble(i++, movConta.getValor());
            if (UtilReflection.objetoMaiorQueZero(lotePagouConta, "getCodigo()")) {
                pst.setInt(i++, lotePagouConta.getCodigo());
            } else {
                pst.setNull(i++, Types.NULL);
            }
            pst.setDouble(i++, movConta.getValorPago());
            pst.setInt(i++, movConta.getCodigo());
            pst.execute();
            if (movConta.getDataQuitacao() != null) {
                incluirQuitacaoNoCaixa(movConta, codigoCaixa);
            }

            //incluir novo rateio
            MovContaRateio mrDao = new MovContaRateio(con);
            mrDao.incluirMovContasRateio(movConta, movConta.getMovContaRateios(), false);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    /**
     *<AUTHOR> A Alves
     * 26/12/2024
     * Método criado para o ticket M2-2959 pois após quitar uma conta com valor a pagar a maior, e, após isso fazer o rateio na tela de lançamento, o valorOriginalAlterado estava sendo
     * atualizado e a coluna Valor Previsto estava apresentando o mesmo valor do valor pago
     */
    public void alterarSemAtualizarValorOriginalAlterado(MovContaVO obj, int codigoCaixa, boolean validarFormaPagamento) throws Exception {
        ConfiguracaoFinanceiro configuracaoFinanceiroDao;
        MovContaRateio movContaRateioDao;
        CaixaMovConta caixaMovContaDao;
        MovContaContabil movContaContabilDao;
        try {
            configuracaoFinanceiroDao = new ConfiguracaoFinanceiro(con);
            movContaRateioDao = new MovContaRateio(con);
            caixaMovContaDao = new CaixaMovConta(con);
            movContaContabilDao = new MovContaContabil(con);

            con.setAutoCommit(false);
            MovContaVO.validarDados(obj);
            if (validaDtBloqueio(obj)){
                validarDataBloqueio(obj, false);
            }
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE movconta SET ");
            sql.append("descricao = ? ,");
            sql.append("pessoa = ?, ");
            sql.append("usuario = ?, ");
            sql.append("empresa = ?, ");
            sql.append("observacoes = ?, ");
            sql.append("valor = ?, ");
            sql.append("dataquitacao = ?, ");
            sql.append("datalancamento = ?, ");
            sql.append("datavencimento = ?, ");
            sql.append("datacompetencia = ?, ");
            sql.append("tipooperacao = ?, ");
            sql.append("conta = ?, ");
            sql.append("lote = ?,  ");
            sql.append("apresentarnocaixa = ?, ");
            sql.append("app = ?, ");
            sql.append("dataUltimaAlteracao = ?, ");
            sql.append("codigobarras = ?, ");
            sql.append("compraEstoque = ?, ");
            sql.append("numeroDocumento = ?, ");
            sql.append("valorpago = ?, ");
            sql.append("contadeconsumo = ?, ");
            sql.append("cpfoucnpjbeneficiario = ?, ");
            sql.append("payloadpix = ? ");
            sql.append("WHERE  codigo = ?");
            PreparedStatement sqlAlterar = con.prepareStatement(sql.toString());
            int i = 1;
            sqlAlterar.setString(i++, obj.getDescricao());
            if (obj.getPessoaVO().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getPessoaVO().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setInt(i++, obj.getUsuarioVO().getCodigo());
            sqlAlterar.setInt(i++, obj.getEmpresaVO().getCodigo());
            sqlAlterar.setString(i++, obj.getObservacoes());
            sqlAlterar.setDouble(i++, obj.getValor());
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataQuitacao()));
            sqlAlterar.setTimestamp(i++, Uteis.getDataHoraJDBC(obj.getDataLancamento(), Uteis.getHoraAtual()));
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataVencimento()));
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataCompetencia()));
            sqlAlterar.setInt(i++, obj.getTipoOperacaoLancamento().getCodigo());
            if (obj.getContaVO().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getContaVO().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getLote().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getLote().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setBoolean(i++, obj.getApresentarNoCaixa());
            sqlAlterar.setBoolean(i++, obj.getApp());
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlAlterar.setString(i++, obj.getCodigoBarras());
            if (obj.getCompraEstoque() != null && obj.getCompraEstoque() != 0) {
                sqlAlterar.setInt(i++, obj.getCompraEstoque());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if(!UteisValidacao.emptyString(obj.getNumeroDocumento())){
                sqlAlterar.setString(i++, obj.getNumeroDocumento());
            } else{
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setDouble(i++, obj.getValorPago());
            sqlAlterar.setBoolean(i++, obj.isContaDeConsumo());
            sqlAlterar.setString(i++, obj.getCpfOuCnpjBeneficiario());
            sqlAlterar.setString(i++, obj.getPayloadPix());
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
            movContaRateioDao.alterarMovContasRateio(obj, obj.getMovContaRateios(), validarFormaPagamento);
            //validar pagamento conjunto
            if(obj.getDataQuitacao() == null){
                if(!UteisValidacao.emptyString(obj.getConjuntoPagamento())){
                    if(UteisValidacao.emptyList(obj.getContasAPagarConjunto())){
                        obj.setContasAPagarConjunto(getFacade().getFinanceiro().
                                getMovConta().verConjuntoPagamentosResumido(obj));
                    }
                    for(MovContaVO objestorno : obj.getContasAPagarConjunto()){
                        objestorno.setUsuarioVO(obj.getUsuarioVO());
                        estorno(objestorno);
                    }
                }
                MovContaVO movContaAntesAlteracao = (MovContaVO) obj.getObjetoVOAntesAlteracao();
                if (movContaAntesAlteracao != null && movContaAntesAlteracao.getDataQuitacao() != null){
                    estorno(obj);
                }
            }else{
                //se não ainda está incluída
                if(!caixaMovContaDao.movContaJaIncluido(obj.getCodigo()) && codigoCaixa > 0){
                    // Incluir a quitação no caixa.
                    incluirQuitacaoNoCaixa(obj, codigoCaixa);
                }
            }
            if (configuracaoFinanceiroDao.consultar().isHabilitarExportacaoAlterData()) {
                MovContaVO movContaAnterior = null;
                if (obj.getObjetoVOAntesAlteracao() != null) {
                    movContaAnterior = (MovContaVO) obj.getObjetoVOAntesAlteracao();
                }

                if (UtilReflection.objetoMaiorQueZero(obj, "getMovContaContabilVO().getCodigo()") && movContaAnterior != null &&
                        !UteisValidacao.emptyNumber(movContaAnterior.getMovContaContabilVO().getCodigo())) {
                    obj.getMovContaContabilVO().getMovContaVO().setIncluirMovContaContabil(true);
                    movContaContabilDao.alterar(obj.getMovContaContabilVO());
                } else {
                    obj.getMovContaContabilVO().setMovContaVO((MovContaVO) obj.getClone(false));
                    obj.getMovContaContabilVO().getMovContaVO().setIncluirMovContaContabil(true);
                    movContaContabilDao.incluir(obj.getMovContaContabilVO());
                }
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
            configuracaoFinanceiroDao = null;
            movContaRateioDao = null;
            caixaMovContaDao = null;
            movContaContabilDao = null;
        }
    }

    @Override
    /**
     *<AUTHOR> A Alves
     * 27/01/2025
     * Método criado para o ticket PAY-202 para validar se existe lançamentos para o cód. pessoa passado, com o intuito de evitar que sejam excluídos cadastros que contenham lançamentos vinculads
     */
    public int consultarQtdLancamentosPessoa(Integer codigoPessoa) throws Exception {
        String sql = "SELECT count(*) as qtdLancamentos FROM movconta WHERE pessoa = " + codigoPessoa;
        int qtd = 0;
        ResultSet resultSet = criarConsulta(sql, con);
        if (resultSet.next()) {
            qtd = resultSet.getInt("qtdLancamentos");
        }
        return qtd;
    }

    @Override
   public List<MovContaVO> consultar(String sql, final int nivelMontarDados) throws SQLException, Exception {
        try (ResultSet tabelaResultado = criarConsulta(sql, con)) {
            return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
        }
    }
}


