/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.ProdutoTextoPadraoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.*;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.PlanoTextoPadrao;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.financeiro.AulaAvulsaDiariaInterfaceFacade;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

/**
 *
 * <AUTHOR>
 */
public class AulaAvulsaDiaria extends SuperEntidade implements AulaAvulsaDiariaInterfaceFacade {    

    public AulaAvulsaDiaria() throws Exception {
        super();        
    }

    public AulaAvulsaDiaria(Connection conexao) throws Exception {
        super(conexao);
    }

    public AulaAvulsaDiariaVO novo() throws Exception {
        incluir(getIdEntidade());
        AulaAvulsaDiariaVO obj = new AulaAvulsaDiariaVO();
        return obj;
    }

    public void incluirSemCommit(final AulaAvulsaDiariaVO obj) throws Exception {
        incluirSemCommit(obj, OrigemSistemaEnum.ZW, true);
    }

    public void incluirSemCommit(final AulaAvulsaDiariaVO obj, OrigemSistemaEnum origem) throws Exception {
        if (origem == null){
           origem = OrigemSistemaEnum.ZW;
        }
        incluirSemCommit(obj, origem, true);
    }
    public void incluirSemCommit(final AulaAvulsaDiariaVO obj, OrigemSistemaEnum origem, Boolean gerarParcela) throws Exception {
        AulaAvulsaDiariaVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO aulaavulsadiaria(empresa,cliente, produto," +
                "modalidade, responsavel,dataregistro,valor, nomeComprador,dataLancamento, origemsistema)VALUES(?,?,?,?,?,?,?,?,?,?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getEmpresa().getCodigo().intValue() != 0) {
            sqlInserir.setInt(1, obj.getEmpresa().getCodigo().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getCliente().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getCliente().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }

        if (obj.getProduto().getCodigo().intValue() != 0) {
            sqlInserir.setInt(3, obj.getProduto().getCodigo().intValue());
        } else {
            sqlInserir.setNull(3, 0);
        }
        if (obj.getModalidade().getCodigo().intValue() != 0) {
            sqlInserir.setInt(4, obj.getModalidade().getCodigo().intValue());
        } else {
            sqlInserir.setNull(4, 0);
        }
        if (obj.getResponsavel().getCodigo().intValue() != 0) {
            sqlInserir.setInt(5, obj.getResponsavel().getCodigo().intValue());
        } else {
            sqlInserir.setNull(5, 0);
        }
        sqlInserir.setDate(6, Uteis.getDataJDBC(obj.getDataRegistro()));
        sqlInserir.setDouble(7, obj.getValor());
        sqlInserir.setString(8, obj.getNomeComprador());
        sqlInserir.setTimestamp(9, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlInserir.setInt(10, origem == null ? OrigemSistemaEnum.ZW.getCodigo() : origem.getCodigo());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        if(gerarParcela){
            if (obj.getValor().doubleValue() == 0) {
                obj.gerarPacela(obj, true, this.con);
            }else{
                obj.gerarPacela(obj, false, this.con);
            }
        }
        obj.gerarPeriodoAcesso(obj, this.con);
        incluirContratoDiariaSemCommit(obj);
    }

    public void incluirContratoDiariaSemCommit(final AulaAvulsaDiariaVO obj) throws Exception {
        PlanoTextoPadrao planoTextoPadraoDao;
        try {
            if (!UteisValidacao.emptyNumber(obj.getCodigo()) && obj.getProduto() != null && !UteisValidacao.emptyNumber(obj.getProduto().getCodigo()) &&
                    !UteisValidacao.emptyNumber(obj.getProduto().getContratoTextoPadrao())) {

                planoTextoPadraoDao = new PlanoTextoPadrao(con);
                PlanoTextoPadraoVO contratoDiaria = planoTextoPadraoDao.consultarPorChavePrimaria(obj.getProduto().getContratoTextoPadrao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                String sql = "INSERT INTO produtotextopadrao(produto, planotextopadrao, aulaavulsadiaria, " +
                        "texto, datalancamento)VALUES(?,?,?,?,?)";
                PreparedStatement sqlInserir = con.prepareStatement(sql);
                sqlInserir.setInt(1, obj.getProduto().getCodigo());
                sqlInserir.setInt(2, obj.getProduto().getContratoTextoPadrao());
                sqlInserir.setInt(3, obj.getCodigo());
                sqlInserir.setString(4, contratoDiaria.getTexto());
                sqlInserir.setTimestamp(5, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
                sqlInserir.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            planoTextoPadraoDao = null;
        }
    }

    public void incluir(AulaAvulsaDiariaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);

            incluirSemCommit(obj);
            
            con.commit();
            
//            //INICIO - REGISTRANDO LOG DE MODIFICACOES
//            obj.setNovoObj(new Boolean(true));
////          SuperEntidade.registrarLogObjetoVO(reciboVO, reciboVO.getPessoaPagador().getCodigo());
//            registrarLogObjetoVO(obj, obj.getCodigo().intValue(), "AULAAVULSADIARIA", obj.getCliente().getPessoa().getCodigo());
//            //FIM - REGISTRANDO LOG DE MODIFICACOES
            
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            obj.setNovoObj(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterar(AulaAvulsaDiariaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            AulaAvulsaDiariaVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE AulaAvulsaDiaria SET empresa=?, cliente=?, " +
                    "produto=?, modalidade=?, responsavel=?, dataregistro=?, valor=?, nomeComprador=?, dataLancamento=? WHERE(codigo=?)";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            if (obj.getEmpresa().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(1, obj.getEmpresa().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(1, 0);
            }

            if (obj.getCliente().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(2, obj.getCliente().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            if (obj.getProduto().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(3, obj.getProduto().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(3, 0);
            }

            if (obj.getModalidade().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(4, obj.getModalidade().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(4, 0);
            }

            if (obj.getResponsavel().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(5, obj.getResponsavel().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(5, 0);
            }

            sqlAlterar.setDate(6, Uteis.getDataJDBC(obj.getDataRegistro()));
            sqlAlterar.setDouble(7, obj.getValor());
            sqlAlterar.setString(8, obj.getNomeComprador());
            sqlAlterar.setTimestamp(9, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
//            sqlAlterar.setString(10, obj.getTipoComprador());
            sqlAlterar.setInt(9, obj.getCodigo().intValue());
            sqlAlterar.execute();

            obj.setNovoObj(new Boolean(false));
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            obj.setNovoObj(new Boolean(true));
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }
    
    public void excluirSemCommit(Integer codigo) throws Exception {
		excluir(getIdEntidade());
		String sql = "DELETE FROM AulaAvulsaDiaria WHERE ((codigo = ?))";
		PreparedStatement sqlExcluir = con.prepareStatement(sql);
		sqlExcluir.setInt(1, codigo.intValue());
		sqlExcluir.execute();
	}
   
    /**
     * Responsável por realizar uma consulta de <code>Pessoa</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PessoaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public AulaAvulsaDiariaVO consultarPorCliente(
            Integer cliente, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM AulaAvulsaDiaria WHERE cliente = " + cliente.intValue() + "ORDER BY codigo";
        } else {
            sqlStr = "SELECT * FROM AulaAvulsaDiaria WHERE cliente = " + cliente.intValue() + " and empresa = " + empresa.intValue() + "ORDER BY codigo";
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return new AulaAvulsaDiariaVO();
        }

        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>CidadeVO</code> resultantes da consulta.
     */
    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            AulaAvulsaDiariaVO obj = new AulaAvulsaDiariaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }

        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>CidadeVO</code>.
     * @return  O objeto da classe <code>CidadeVO</code> com os dados devidamente montados.
     */
    public AulaAvulsaDiariaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        AulaAvulsaDiariaVO obj = new AulaAvulsaDiariaVO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ROBO) {
            obj.getModalidade().setCodigo(dadosSQL.getInt("modalidade"));
            return obj;
        }

        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getCliente().setCodigo(new Integer(dadosSQL.getInt("cliente")));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        //obj.getColaborador().setCodigo(dadosSQL.getInt("colaborador"));
        obj.getProduto().setCodigo(dadosSQL.getInt("produto"));
        obj.getModalidade().setCodigo(dadosSQL.getInt("modalidade"));
        obj.getResponsavel().setCodigo(dadosSQL.getInt("responsavel"));
        obj.setNomeComprador(dadosSQL.getString("nomeComprador"));
        obj.setValor(dadosSQL.getDouble("valor"));
        //obj.setTipoComprador(dadosSQL.getString("tipoComprador"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            //  montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return obj;
        }

        montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        //   montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosResponsavel(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PaisVO</code> relacionado ao objeto <code>CidadeVO</code>.
     * Faz uso da chave primária da classe <code>PaisVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public void montarDadosCliente(AulaAvulsaDiariaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getCliente().getCodigo().intValue() == 0) {
            obj.setCliente(new ClienteVO());
            return;

        }

        Cliente clienteDAO = new Cliente(this.con);
        obj.setCliente(clienteDAO.consultarPorChavePrimaria(obj.getCliente().getCodigo(), nivelMontarDados));
        clienteDAO = null;
    }

    public void montarDadosEmpresa(AulaAvulsaDiariaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getEmpresa().getCodigo().intValue() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;

        }

        Empresa empresaDAO = new Empresa(this.con);
        obj.setEmpresa(empresaDAO.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
        empresaDAO = null;
    }

//    public static void montarDadosColaborador(AulaAvulsaDiariaVO obj, int nivelMontarDados) throws Exception {
//        if (obj.getColaborador().getCodigo().intValue() == 0) {
//            obj.setColaborador(new ColaboradorVO());
//            return;
//
//        }
//
//
//        obj.setColaborador(new Colaborador().consultarPorChavePrimaria(obj.getColaborador().getCodigo(), nivelMontarDados));
//    }
    public void montarDadosProduto(AulaAvulsaDiariaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getProduto().getCodigo().intValue() == 0) {
            obj.setProduto(new ProdutoVO());
            return;

        }


        Produto produtoDAO = new Produto(this.con);
        obj.setProduto(produtoDAO.consultarPorChavePrimaria(obj.getProduto().getCodigo(), nivelMontarDados));
        produtoDAO = null;
    }

    public void montarDadosModalidade(AulaAvulsaDiariaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getModalidade().getCodigo().intValue() == 0) {
            obj.setModalidade(new ModalidadeVO());
            return;

        }

        Modalidade modalidadeDAO = new Modalidade(this.con);
        obj.setModalidade(modalidadeDAO.consultarPorChavePrimaria(obj.getModalidade().getCodigo(), nivelMontarDados));
        modalidadeDAO = null;
    }

    public void montarDadosResponsavel(AulaAvulsaDiariaVO obj, int nivelMontarDados) throws Exception {
        if (obj.getResponsavel().getCodigo().intValue() == 0) {
            obj.setResponsavel(new UsuarioVO());
            return;

        }


        Usuario usuarioDAO = new Usuario(this.con);
        obj.setResponsavel(usuarioDAO.consultarPorChavePrimaria(obj.getResponsavel().getCodigo(), nivelMontarDados));
        usuarioDAO = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>CidadeVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public AulaAvulsaDiariaVO consultarPorChavePrimaria(
            Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM aulaAvulsaDiaria WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( AulaAvulsaDiaria ).");
        }

        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public String obterContratoProdutoTextoPadraoDiaria(
            Integer aulaAvulsaDiaria, Integer produto) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT texto FROM produtotextopadrao WHERE aulaavulsadiaria = ? and produto = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, aulaAvulsaDiaria);
            sqlConsultar.setInt(2, produto);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return "";
                }
                return tabelaResultado.getString("texto");
            }
        }
    }

    public void excluirVendasOnline(Integer diaria) throws Exception {
        try {
            con.setAutoCommit(false);
            ResultSet rs = criarConsulta("select mpar.codigo as parcela, mp.codigo as movproduto from movparcela mpar \n" +
                    " inner join movprodutoparcela mpp on mpar.codigo = mpp.movparcela\n" +
                    " inner join movproduto mp on mp.codigo = mpp.movproduto\n" +
                    " where mpar.aulaavulsadiaria = " + diaria, con);
            MovProduto movProdutoDAO = new MovProduto(con);
            MovParcela movParcelaDAO = new MovParcela(con);
            PeriodoAcessoCliente periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);
            while(rs.next()){
                Integer parcela = rs.getInt("parcela");
                Integer movproduto = rs.getInt("movproduto");
                movProdutoDAO.excluirSemCommit(movproduto);
                movParcelaDAO.excluirSemCommit(parcela);
            }

            ResultSet rsPeriodo = criarConsulta("select codigo from periodoacessocliente where aulaavulsadiaria = " + diaria, con);
            while(rsPeriodo.next()){
                Integer periodoAcesso = rsPeriodo.getInt("codigo");
                PeriodoAcessoClienteVO periodoAcessoClienteVO = new PeriodoAcessoClienteVO();
                periodoAcessoClienteVO.setCodigo(periodoAcesso);
                periodoAcessoClienteDAO.excluirSemCommit(periodoAcessoClienteVO);
            }

            excluirSemCommit(diaria);

            movProdutoDAO = null;
            movProdutoDAO = null;
            periodoAcessoClienteDAO = null;
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private String vendasOnlineDiariaSQL(Integer empresa, Date dataInicial, Date dataFinal) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select \n");
        sql.append(" cl.codigo as cliente, \n");
        sql.append("         cl.matricula, \n");
        sql.append("         p.nome, \n");
        sql.append("         aa.dataregistro, \n");
        sql.append("         aa.codigo as diaria, \n");
        sql.append(" pr.descricao as produtos \n");
        sql.append(" from aulaavulsadiaria aa \n");
        sql.append(" inner join cliente cl on cl.codigo = aa.cliente \n");
        sql.append(" inner join produto pr on pr.codigo = aa.produto \n");
        sql.append(" inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append(" where aa.origemsistema =").append(OrigemSistemaEnum.VENDAS_ONLINE_2.getCodigo()).append(" \n");
        sql.append(" and aa.dataregistro::date between '").append(Uteis.getDataFormatoBD(dataInicial)).append("' and '").append(Uteis.getDataFormatoBD(dataFinal)).append("' \n");
        sql.append(" and aa.empresa = ").append(empresa).append(" \n");
        sql.append(" order by aa.dataregistro desc \n");

        return sql.toString();
    }

    public Integer vendasOnlineDiariaTotal(Integer empresa, Date dataInicial, Date dataFinal) throws Exception {
        String sql = vendasOnlineDiariaSQL(empresa, dataInicial, dataFinal);
        return SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
    }

    public List<ItemRelatorioTO> vendasOnlineDiariaLista(Integer empresa, Date dataInicial, Date dataFinal) throws Exception {
        String sql = vendasOnlineDiariaSQL(empresa, dataInicial, dataFinal);
        List<ItemRelatorioTO> realizadas = new ArrayList<>();
        try (ResultSet resultSet = criarConsulta(sql, con)) {
            while (resultSet.next()) {
                ItemRelatorioTO item = new ItemRelatorioTO();
                item.setMatricula(resultSet.getString("matricula"));
                item.setProduto(resultSet.getString("produtos"));
                item.setCliCodigo(resultSet.getInt("cliente"));
                item.setNome(resultSet.getString("nome"));
                item.setDataLancamento(resultSet.getTimestamp("dataregistro"));
                realizadas.add(item);
            }
        }
        return realizadas;
    }

}
