
package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.financeiro.ItemTaxaPersonalVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.plano.Plano;
import negocio.interfaces.financeiro.ControleTaxaPersonalInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ControleTaxaPersonal extends SuperEntidade implements ControleTaxaPersonalInterfaceFacade {

    public ControleTaxaPersonal() throws Exception {
        super();
    }

    public ControleTaxaPersonal(Connection con) throws Exception {
    	super(con);
	}

	@Override
    public void incluir(ControleTaxaPersonalVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            obj.validarDados();
            incluirSemCommit(obj);
            incluirAlunos(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            obj.setNovoObj(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(ControleTaxaPersonalVO obj) throws Exception {
        String sql = "INSERT INTO controletaxapersonal "
                   + "(personal, dataregistro, empresa, responsavel, dataInicioVigenciaPlano, dataFimVigenciaPlano, plano) "
                   + "VALUES (?, ?, ?, ?, ?, ?, ?)";

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getPersonal().getCodigo().intValue());
        sqlInserir.setDate(2, Uteis.getDataJDBC(obj.getDataRegistro()));
        sqlInserir.setInt(3, obj.getEmpresa().getCodigo());
        sqlInserir.setInt(4, obj.getResponsavel().getCodigo().intValue());
        sqlInserir.setDate(5,  Uteis.getDataJDBC(obj.getDataInicioVigenciaPlano()));
        sqlInserir.setDate(6,  Uteis.getDataJDBC(obj.getDataFimVigenciaPlano()));
        Integer plano = obj.getPlano() == null || obj.getPlano().getCodigo().equals(0) ? null : obj.getPlano().getCodigo();
        if(plano == null){
            sqlInserir.setNull(7, Types.NULL);
        }else{
            sqlInserir.setInt(7,  plano);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        incluirAlunos(obj);

        if (obj.getPlanoPersonalTextoPadrao() != null && obj.getPlanoPersonalTextoPadrao().getPlanoTextoPadrao().getCodigo() > 0) {
            incluirTextoPadraoPlanoPersonal(obj);
        }
    }

    private void incluirTextoPadraoPlanoPersonal(ControleTaxaPersonalVO obj) throws Exception {
        String sql = "INSERT INTO planopersonaltextopadrao "
                + "(planotextopadrao, taxapersonal, codigo) "
                + "VALUES (?, ?, ?)";

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getPlanoPersonalTextoPadrao().getPlanoTextoPadrao().getCodigo());
        sqlInserir.setInt(2, obj.getCodigo());
        sqlInserir.setInt(3, obj.getCodigo());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    private void incluirAlunos(ControleTaxaPersonalVO controle) throws Exception {
            for(ItemTaxaPersonalVO aluno : controle.getAlunos()) {
                getFacade().getItemTaxaPersonal().incluirSemCommit(aluno, controle.getCodigo());
            }
        }

    @Override
    public void excluir(int codigo) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(codigo);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(int codigo) throws Exception {
        String sql = "DELETE FROM controletaxapersonal WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, codigo);
        sqlExcluir.execute();
    }

    @Override
    public ControleTaxaPersonalVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM controletaxapersonal WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados.");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    @Override
    public List<ControleTaxaPersonalVO> consultarPorPeriodo(int empresa, int personal, Date inicio, Date fim, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM controletaxapersonal "
                   + "WHERE empresa = ? AND personal = ? AND "
                   + "dataregistro >= ? AND dataregistro <= ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, empresa);
        sqlConsultar.setInt(2, personal);
        sqlConsultar.setDate(3, Uteis.getDataJDBC(inicio));
        sqlConsultar.setDate(4, Uteis.getDataJDBC(fim));
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    @Override
    public List<ControleTaxaPersonalVO> consultarComParcelasEmAtrasoParaCancelamento(int qtdeMinimaParcelasAtrasadas) throws Exception {
        List<ControleTaxaPersonalVO> controles = new ArrayList<ControleTaxaPersonalVO>();

        String sql = "SELECT * FROM ( " +
                "   SELECT controletaxapersonal.*, " +
                "       (SELECT COUNT(*) " +
                "   FROM movparcela " +
                "   WHERE movparcela.personal = controletaxapersonal.codigo " +
                "   AND movparcela.datavencimento <= '" + Uteis.getData(Calendario.hoje(), "bd") + "' " +
                "   AND movparcela.descricao not like '%CANCELAMENTO%'" +
                "   AND movparcela.situacao = 'EA'" +
                ") as qtdeParcelasAtrasadas " +
                "   FROM controletaxapersonal " +
                ") as subQuery " +
                "WHERE subQuery.qtdeParcelasAtrasadas > "+qtdeMinimaParcelasAtrasadas;

        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        while (rs.next()){
            ControleTaxaPersonalVO controleTaxaPersonal = montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            controleTaxaPersonal.setQtdeParcelasAtrasadas(rs.getInt("qtdeParcelasAtrasadas"));
            controles.add(controleTaxaPersonal);
        }
        return controles;
    }

    public List<ControleTaxaPersonalVO> consultarPorPersonal(int personal, int nivelMontarDados) throws Exception {
        return consultarPorPersonal(personal, nivelMontarDados, false);
    }

    public List<ControleTaxaPersonalVO> consultarPorPersonal(int personal, int nivelMontarDados, boolean incluirQtdeParcelas) throws Exception {
        List<ControleTaxaPersonalVO> controlesTaxaPersonal = new ArrayList<ControleTaxaPersonalVO>();

        String sql = "SELECT ctp.*, " +
                "CASE WHEN now() between datainiciovigenciaplano and datafimvigenciaplano " +
                "     THEN true " +
                "     ELSE false " +
                "END vigente, " +
                "CASE WHEN (now() between datainiciovigenciaplano and datafimvigenciaplano) and (cancelado IS false or cancelado IS NULL) THEN 'Vigênte'  " +
                "     WHEN (now() between datainiciovigenciaplano and datafimvigenciaplano) and cancelado IS true THEN 'Vigênte cancelado'" +
                "     WHEN cancelado IS TRUE THEN 'Cancelado'" +
                "     ELSE 'Inativo' " +
                "END situacao ";

        if(incluirQtdeParcelas){
            sql += ", " +
                    "(SELECT COUNT(*) FROM movparcela WHERE personal = ctp.codigo AND situacao = 'EA') parcelasEmAberto, " +
                    "(SELECT COUNT(*) FROM movparcela WHERE personal = ctp.codigo AND situacao = 'EA' AND datavencimento::date < now()::date) parcelasAtrasadas, " +
                    "(SELECT COUNT(*) FROM movparcela WHERE personal = ctp.codigo AND situacao = 'PG') parcelasPagas ";
        }

        sql += "FROM controletaxapersonal ctp " +
                "INNER JOIN plano p ON p.codigo = ctp.plano " +
                "WHERE personal = " + personal + " AND p.planoPersonal IS true";

        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();

        while (rs.next()){
            ControleTaxaPersonalVO controleTaxaPersonalVO = montarDados(rs, nivelMontarDados, con);
            controleTaxaPersonalVO.setVigente(rs.getBoolean("vigente"));
            controleTaxaPersonalVO.setSituacao(rs.getString("situacao"));

            if(incluirQtdeParcelas){
                controleTaxaPersonalVO.setQtdeParcelasPagas(rs.getInt("parcelasPagas"));
                controleTaxaPersonalVO.setQtdeParcelasAtrasadas(rs.getInt("parcelasAtrasadas"));
                controleTaxaPersonalVO.setQtdeParcelasEmAberto(rs.getInt("parcelasEmAberto"));
            }

            controlesTaxaPersonal.add(controleTaxaPersonalVO);
        }

        return controlesTaxaPersonal;
    }

    public List<ControleTaxaPersonalVO> consultarPorPessoaColaborador(int pessoa, int nivelMontarDados, boolean incluirQtdeParcelas) throws Exception {
        List<ControleTaxaPersonalVO> controlesTaxaPersonal = new ArrayList<ControleTaxaPersonalVO>();

        StringBuilder sbSql = new StringBuilder();

        sbSql.append("SELECT ctp.*,").append("\n");
        sbSql.append("CASE WHEN now() between datainiciovigenciaplano and datafimvigenciaplano").append("\n");
        sbSql.append("THEN true").append("\n");
        sbSql.append("ELSE false").append("\n");
        sbSql.append("END vigente,").append("\n");
        sbSql.append("CASE WHEN (now() between datainiciovigenciaplano and datafimvigenciaplano) and (cancelado IS false or cancelado IS NULL) THEN 'Vigênte'  ").append("\n");
        sbSql.append("WHEN (now() between datainiciovigenciaplano and datafimvigenciaplano) and cancelado IS true THEN 'Vigênte cancelado'").append("\n");
        sbSql.append("WHEN cancelado IS TRUE THEN 'Cancelado'").append("\n");
        sbSql.append("ELSE 'Inativo' ").append("\n");
        sbSql.append("END situacao ").append("\n");

        if (incluirQtdeParcelas) {
            sbSql.append(", ").append("\n");
            sbSql.append("(SELECT COUNT(*) FROM movparcela WHERE personal = ctp.codigo AND situacao = 'EA') parcelasEmAberto, ").append("\n");
            sbSql.append("(SELECT COUNT(*) FROM movparcela WHERE personal = ctp.codigo AND situacao = 'EA' AND datavencimento::date < now()::date) parcelasAtrasadas, ").append("\n");
            sbSql.append("(SELECT COUNT(*) FROM movparcela WHERE personal = ctp.codigo AND situacao = 'PG') parcelasPagas ").append("\n");
        }

        sbSql.append("FROM controletaxapersonal ctp ").append("\n");
        sbSql.append("INNER JOIN plano p ON p.codigo = ctp.plano ").append("\n");
        sbSql.append("INNER JOIN colaborador col ON col.codigo = ctp.personal ").append("\n");
        sbSql.append("INNER JOIN pessoa pes ON pes.codigo = col.pessoa ").append("\n");
        sbSql.append("WHERE pes.codigo = " + pessoa + " AND p.planoPersonal IS true");

        PreparedStatement ps = con.prepareStatement(sbSql.toString());
        ResultSet rs = ps.executeQuery();

        while (rs.next()) {
            ControleTaxaPersonalVO controleTaxaPersonalVO = montarDados(rs, nivelMontarDados, con);
            controleTaxaPersonalVO.setVigente(rs.getBoolean("vigente"));
            controleTaxaPersonalVO.setSituacao(rs.getString("situacao"));

            if (incluirQtdeParcelas) {
                controleTaxaPersonalVO.setQtdeParcelasPagas(rs.getInt("parcelasPagas"));
                controleTaxaPersonalVO.setQtdeParcelasAtrasadas(rs.getInt("parcelasAtrasadas"));
                controleTaxaPersonalVO.setQtdeParcelasEmAberto(rs.getInt("parcelasEmAberto"));
            }

            controlesTaxaPersonal.add(controleTaxaPersonalVO);
        }

        return controlesTaxaPersonal;
    }

    @Override
    public void cancelar(ControleTaxaPersonalVO controleTaxaPersonal) throws Exception {

        String sql = "UPDATE controletaxapersonal set " +
                "datainiciovigenciaplano = '"+  Uteis.getDataJDBC(controleTaxaPersonal.getDataInicioVigenciaPlano())+"', " +
                "datafimvigenciaplano = '"+Uteis.getDataJDBC(controleTaxaPersonal.getDataFimVigenciaPlano())+"', "+
                "cancelado = true, " +
                "dataCancelamento = '"+Uteis.getDataJDBC(controleTaxaPersonal.getDataCancelamento()) + "', "+
                "responsavelCancelamento = "+controleTaxaPersonal.getResponsavelCancelamento() +
                " WHERE codigo = "+controleTaxaPersonal.getCodigo();
        PreparedStatement ps = con.prepareStatement(sql);
        ps.execute();
    }

    public List<ControleTaxaPersonalVO> montarDadosConsulta(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        List<ControleTaxaPersonalVO> aux = new ArrayList<ControleTaxaPersonalVO>();
        while(dadosSQL.next()) 
            aux.add(montarDados(dadosSQL, nivelMontarDados, con));
        return aux;
    }

    public static ControleTaxaPersonalVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection conexao) throws Exception {
        ControleTaxaPersonalVO obj = montarDadosBasicos(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        montarDadosPersonal(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, conexao);
        montarDadosResponsavel(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, conexao);
        montarDadosPlano(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, conexao);
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, conexao);
        montarItens(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, conexao);
        return obj;
    }

    private static ControleTaxaPersonalVO montarDadosBasicos(ResultSet dadosSQL) throws SQLException {
        ControleTaxaPersonalVO obj = new ControleTaxaPersonalVO(0);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.getPersonal().setCodigo(dadosSQL.getInt("personal"));
        obj.setDataRegistro(dadosSQL.getDate("dataregistro"));
        obj.getResponsavel().setCodigo(dadosSQL.getInt("responsavel"));
        obj.setDataInicioVigenciaPlano(dadosSQL.getDate("DataInicioVigenciaPlano"));
        obj.setDataFimVigenciaPlano(dadosSQL.getDate("DataFimVigenciaPlano"));
        obj.getPlano().setCodigo(dadosSQL.getInt("plano"));
        obj.setCancelado(dadosSQL.getBoolean("cancelado"));
        obj.setResponsavelCancelamento(dadosSQL.getInt("responsavelCancelamento"));
        obj.setDataCancelamento(dadosSQL.getDate("dataCancelamento"));
        obj.setNovoObj(false);
        return obj;
    }

    private static void montarDadosPersonal(ControleTaxaPersonalVO obj, int nivelMontarDados, Connection conexao) throws Exception {
        if (obj.getPersonal().getCodigo() != 0) {
            Colaborador colaboradorDao = new Colaborador(conexao);
            obj.setPersonal(colaboradorDao.consultarPorChavePrimaria(obj.getPersonal().getCodigo(), nivelMontarDados));
            colaboradorDao = null;
        }
    }

    private static void montarDadosResponsavel(ControleTaxaPersonalVO obj, int nivelMontarDados, Connection conexao) throws Exception {
        if (obj.getResponsavel().getCodigo() != 0) {
            Usuario usuarioDao = new Usuario(conexao);
            obj.setResponsavel(usuarioDao.consultarPorChavePrimaria(obj.getResponsavel().getCodigo(), nivelMontarDados));
            usuarioDao = null;
        }
    }

    private static void montarDadosPlano(ControleTaxaPersonalVO obj, int nivelMontarDados, Connection conexao) throws Exception {
        if (obj.getPlano().getCodigo() != 0) {
            Plano planoDao = new Plano(conexao);
            obj.setPlano(planoDao.consultarPorChavePrimaria(obj.getPlano().getCodigo(), nivelMontarDados));
            planoDao = null;
        }
    }

    private static void montarItens(ControleTaxaPersonalVO obj, int nivelMontarDados, Connection conexao) throws Exception {
        ItemTaxaPersonal itemTaxaPersonalDao = new ItemTaxaPersonal(conexao);
        obj.setAlunos(itemTaxaPersonalDao.consultarPorControle(obj.getCodigo(), nivelMontarDados));
        itemTaxaPersonalDao = null;
    }

    private static void montarDadosEmpresa(ControleTaxaPersonalVO obj, int nivelMontarDados, Connection conexao) throws Exception {
        if (obj.getEmpresa().getCodigo() != 0) {
            Empresa empresaDao = new Empresa(conexao);
            obj.setEmpresa(empresaDao.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
            empresaDao = null;
        }
    }

}

