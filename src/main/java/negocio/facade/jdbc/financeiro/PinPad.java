package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.PinPadVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.PinPadInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PinPad extends SuperEntidade implements PinPadInterfaceFacade {

    public PinPad() throws Exception {
        super();
    }

    public PinPad(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>PinPadVO</code>.
     */
    public PinPadVO novo() throws Exception {
        incluir(getIdEntidade());
        return new PinPadVO();
    }

    public void incluir(FormaPagamentoVO fp, List<PinPadVO> pinpads) throws Exception {
        for (PinPadVO fpp : pinpads) {
            fpp.setFormaPagamento(fp);
            if (UteisValidacao.emptyNumber(fpp.getCodigo())) {
                incluirSemCommit(fpp);
            } else {
                alterarSemCommit(fpp);
            }
        }
    }

    public void incluir(PinPadVO obj) throws Exception {
        this.incluir(obj, false);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PinPadVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PinPadVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PinPadVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(PinPadVO obj) throws Exception {
        PinPadVO.validarDados(obj);
        String sql = "INSERT INTO PinPad"
                + "(pinpad, descricao, empresa, cnpjPinpad, pdvPinpad, isautoatendimento, "
                + "formapagamentocodigo, conveniocobranca, nrMaxParcelas) "
                + "VALUES (?,?,?,?,?,?,?,?,?)";
        int i = 1;
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            if (!UteisValidacao.emptyNumber(obj.getPinpad())) {
                pst.setInt(i++, obj.getPinpad());
            } else {
                pst.setNull(i++, 0);
            }
            pst.setString(i++, obj.getDescricao());
            pst.setInt(i++, obj.getEmpresa().getCodigo());
            pst.setString(i++, UteisValidacao.emptyString(obj.getCnpjPinpad()) ? null : Uteis.removerMascara(obj.getCnpjPinpad()));
            pst.setString(i++, obj.getPdvPinpad());
            pst.setBoolean(i++, obj.getAutoAtendimento());
            resolveFKNull(pst, i++, obj.getFormaPagamento().getCodigo());
            resolveFKNull(pst, i++, obj.getConvenioCobranca().getCodigo());
            pst.setInt(i++, obj.getNrMaxParcelas());

            pst.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());

        obj.setNovoObj(false);
    }

    public void alterar(PinPadVO obj) throws Exception {
        this.alterarSemCommit(obj);
    }

    public void alterarSemCommit(PinPadVO obj) throws Exception {
        PinPadVO.validarDados(obj);
        String update = "UPDATE pinpad SET pinpad = ?, descricao = ?, empresa = ?, cnpjpinpad = ?, " +
                "pdvpinpad = ?, isautoatendimento = ?, formapagamentocodigo = ?, conveniocobranca = ?, " +
                "nrMaxParcelas = ? WHERE codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(update)) {
            int i = 1;
            pst.setInt(i++, obj.getPinpad());
            pst.setString(i++, obj.getDescricao());
            pst.setInt(i++, obj.getEmpresa().getCodigo());
            pst.setString(i++, UteisValidacao.emptyString(obj.getCnpjPinpad()) ? null : Uteis.removerMascara(obj.getCnpjPinpad()));
            pst.setString(i++, obj.getPdvPinpad());
            pst.setBoolean(i++, obj.getAutoAtendimento());
            resolveFKNull(pst, i++, obj.getFormaPagamento().getCodigo());
            resolveFKNull(pst, i++, obj.getConvenioCobranca().getCodigo());
            pst.setInt(i++, obj.getNrMaxParcelas());

            pst.setInt(i++, obj.getCodigo());
            pst.execute();
        }
    }

    public void excluir(PinPadVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PinPadVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PinPadVO</code> que será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PinPadVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);

            String sql = "DELETE FROM PinPad WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirPorPinPad(Integer codigoPinPad) throws Exception {
        String sql = "DELETE FROM pinpad WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codigoPinPad);
            sqlExcluir.execute();
        }
    }

    public List<PinPadVO> consultarPorForma(Integer forma) throws Exception {
        List<PinPadVO> formasPinPad = new ArrayList<PinPadVO>();
        try (ResultSet rs = criarConsulta("SELECT * FROM pinpad WHERE formapagamentocodigo = " + forma, con)) {
            while (rs.next()) {
                formasPinPad.add(montarDados(rs));
            }
        }
        return formasPinPad;
    }

    public List<PinPadVO> consultarParaTotem() throws Exception {
        List<PinPadVO> formasPinPad = new ArrayList<PinPadVO>();
        try (ResultSet rs = criarConsulta("SELECT * FROM pinpad WHERE coalesce(formapagamentocodigo,0) = 0", con)) {
            while (rs.next()) {
                formasPinPad.add(montarDados(rs));
            }
        }
        return formasPinPad;
    }

    public List<PinPadVO> consultarPorFormaEmpresa(Integer forma, Integer empresa, OpcoesPinpadEnum opcoesPinpadEnum) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("p.* \n");
        sql.append("from pinpad p \n");
        sql.append("where p.formapagamentocodigo = ").append(forma).append(" \n");
        if (opcoesPinpadEnum != null) {
            sql.append("and p.pinpad = ").append(opcoesPinpadEnum.getCodigo()).append(" \n");
        }
        sql.append("and (coalesce(p.empresa,0) = 0 or p.empresa = ").append(empresa != null ? empresa : 0).append(") \n");
        sql.append("AND p.conveniocobranca IS NOT NULL \n");

        List<PinPadVO> lista = new ArrayList<PinPadVO>();
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                lista.add(montarDados(rs));
            }
        }
        return lista;
    }

    public boolean existePinpad(OpcoesPinpadEnum opcoesPinpadEnum, Integer forma, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("exists( \n");
        sql.append("select \n");
        sql.append("p.codigo \n");
        sql.append("from pinpad p \n");
        sql.append("where p.formapagamentocodigo = ").append(forma).append(" \n");
        sql.append("and p.pinpad = ").append(opcoesPinpadEnum.getCodigo()).append(" \n");
        sql.append("and (coalesce(p.empresa,0) = 0 or p.empresa = ").append(empresa != null ? empresa : 0).append(") \n");
        sql.append(") as existe \n");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                rs.next();
                return rs.getBoolean(1);
            }
        }
    }

    @Override
    public PinPadVO consultarPorCodigo(Integer pinPadSelecionado) throws Exception {
        PinPadVO pinPad = new PinPadVO();
        try (ResultSet rs = criarConsulta("SELECT * FROM pinpad WHERE codigo = " + pinPadSelecionado, con)) {
            while (rs.next()) {
                pinPad.setCodigo(rs.getInt("codigo"));
                pinPad.setPinpad(rs.getInt("pinpad"));
                pinPad.setCnpjPinpad(rs.getString("cnpjpinpad"));
                pinPad.setPdvPinpad(rs.getString("pdvpinpad"));
                pinPad.setAutoAtendimento(rs.getBoolean("isautoatendimento"));
                pinPad.getFormaPagamento().setCodigo(rs.getInt("formapagamentocodigo"));
            }
        }
        return pinPad;
    }

    public PinPadVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT * FROM pinpad WHERE codigo = " + codigo, con)) {
            if (rs.next()) {
                return montarDados(rs);
            }
        }
        return null;
    }

    @Override
    public PinPadVO obterPinPadTotem(Integer codigoFormaPagamento) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT * FROM pinpad WHERE formapagamentocodigo = " + codigoFormaPagamento + " and isautoatendimento = true", con)) {
            if (rs.next()) {
                return montarDados(rs);
            }
        }
        return null;
    }

    public PinPadVO montarDados(ResultSet rs) throws Exception {
        PinPadVO obj = new PinPadVO();
        try {
            obj.setCodigo(rs.getInt("codigo"));
            try (ResultSet rsPinPad = criarConsulta("SELECT pinpad, descricao, empresa, cnpjpinpad, pdvpinpad, " +
                    "isautoatendimento, formapagamentocodigo,conveniocobranca, nrMaxParcelas FROM pinpad WHERE codigo = " + rs.getInt("codigo"), con)) {
                if (rsPinPad.next()) {
                    obj.setPinpad(rsPinPad.getInt("pinpad"));
                    if (rsPinPad.getString("descricao") != null) {
                        obj.setDescricao(rsPinPad.getString("descricao"));
                    } else {
                        obj.setDescricao("");
                    }
                    obj.setCnpjPinpad(rsPinPad.getString("cnpjpinpad"));
                    obj.setPdvPinpad(rsPinPad.getString("pdvpinpad"));
                    obj.setAutoAtendimento(rsPinPad.getBoolean("isautoatendimento"));
                    obj.getConvenioCobranca().setCodigo(rsPinPad.getInt("conveniocobranca"));
                    obj.setNrMaxParcelas(rsPinPad.getInt("nrMaxParcelas"));
                }
            }
            if (rs.getInt("empresa") != 0) {
                try (ResultSet rsEmp = criarConsulta("SELECT codigo, nome FROM empresa WHERE codigo = " + rs.getInt("empresa"), con)) {
                    if (rsEmp.next()) {
                        obj.getEmpresa().setCodigo(rsEmp.getInt("codigo"));
                        obj.getEmpresa().setNome(rsEmp.getString("nome"));
                    }
                }
            } else {
                obj.getEmpresa().setCodigo(0);
                obj.getEmpresa().setNome("");
            }

            try (ResultSet rsForma = criarConsulta("SELECT codigo, descricao FROM formapagamento WHERE codigo = " + rs.getInt("formapagamentocodigo"), con)) {
                if (rsForma.next()) {
                    obj.getFormaPagamento().setDescricao(rsForma.getString("descricao"));
                    obj.getFormaPagamento().setCodigo(rsForma.getInt("codigo"));
                }
            }

            obj.getConvenioCobranca().setCodigo(rs.getInt("conveniocobranca"));
            if (!UteisValidacao.emptyNumber(obj.getConvenioCobranca().getCodigo())) {
                ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(this.con);
                obj.setConvenioCobranca(convenioCobrancaDAO.consultarPorChavePrimaria(obj.getConvenioCobranca().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                convenioCobrancaDAO = null;
            }
        } catch (Exception e) {
            //não fazer nada, foi adicionadas colunas novas, apenas para evitar um null pointer caso algo dê errado.
        }
        return obj;
    }

    public void incluirHistorico(Integer pinPadPedido, String idExterno, String operacao, String dados) throws Exception {
        String sql = "INSERT INTO pinpadhistorico(dataregistro, pinpadpedido, idExterno, operacao, dados) VALUES (?,?,?,?,?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveIntegerNull(pst, ++i, pinPadPedido);
            pst.setString(++i, idExterno);
            pst.setString(++i, operacao);
            pst.setString(++i, dados);
            pst.execute();
        }
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        List lista;
        String sql = getPS(i, filtro);
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                ConvenioCobrancaVO cCobranca = new ConvenioCobrancaVO();
                cCobranca.setCodigo(rs.getInt("codigo"));
                cCobranca.setDescricao(rs.getString("descricao"));
                cCobranca.getEmpresa().setNome(rs.getString("empresa"));
                if (!UteisValidacao.emptyString(rs.getString("banco"))) {
                    cCobranca.getBanco().setNome(rs.getString("banco"));
                } else {
                    cCobranca.getBanco().setNome("nenhum");
                }
                cCobranca.setTipo(TipoConvenioCobrancaEnum.valueOf(rs.getInt("tipoconvenio")));
                lista.add(cCobranca);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        } else if (campoOrdenacao.equals("Banco")) {
            Ordenacao.ordenarLista(lista, "banco_Apresentar");
        } else if (campoOrdenacao.equals("Tipo")) {
            Ordenacao.ordenarLista(lista, "tipo_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    private String getPS(Integer empresa, String clausulaLike) throws SQLException {
        StringBuilder sql = new StringBuilder();

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append("select * from ( \n");
        }
        sql.append("SELECT \n");
        sql.append("p.*, \n");
        sql.append("e.nome as empresa_nome, \n");
        sql.append("cc.descricao as convenio_descricao \n");
        sql.append("FROM pinpad p \n");
        sql.append("LEFT JOIN empresa e ON e.codigo = p.empresa \n");
        sql.append("LEFT JOIN conveniocobranca cc ON cc.codigo = p.conveniocobranca \n");
        sql.append("WHERE coalesce(p.formapagamentocodigo,0) = 0 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND (coalesce(p.empresa,0) = 0 or p.empresa = ").append(empresa).append(") \n");
        }

        if (!UteisValidacao.emptyString(clausulaLike)) {
            // VALIDAÇÃO DE SEGURANÇA: Verificar se clausulaLike é segura
            if (!Uteis.isValidStringValue(clausulaLike)) {
                throw new SecurityException("Cláusula de busca contém caracteres não permitidos");
            }
            sql.append(") AS t \n");
            sql.append("WHERE 1 = 1 \n");
            sql.append("AND ( \n");
            // Escapar aspas simples para prevenir SQL injection
            String clausulaEscapada = clausulaLike.replace("'", "''");
            sql.append("lower(codigo::VARCHAR) ~ '").append(clausulaEscapada).append("' \n");
            sql.append("OR lower(descricao) ~ '").append(clausulaEscapada).append("' \n");
            sql.append("OR lower(empresa_nome) ~ '").append(clausulaEscapada).append("' \n");
            sql.append("OR lower(convenio_descricao) ~ '").append(clausulaEscapada).append("' \n");
            sql.append(")");
        }
        return sql.toString();
    }

    public String consultarJSON(Integer empresa, String sEcho, String clausulaLike) throws Exception {
        StringBuilder json;
        boolean dados;
        String sql = getPS(empresa, clausulaLike);
        Integer count = contar("select count(*) from (" +sql + ") as sql", getCon());

        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            json = new StringBuilder();
            json.append("{");
            json.append("\"iTotalRecords\":\"").append(count).append("\",");
            json.append("\"iTotalDisplayRecords\":\"").append(count).append("\",");
            json.append("\"sEcho\":\"").append(sEcho).append("\",");
            json.append("\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa_nome"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("convenio_descricao"))).append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }
}
