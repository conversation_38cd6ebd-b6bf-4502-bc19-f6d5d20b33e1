package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.OperadoraCartaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>OperadoraCartaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>OperadoraCartaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see OperadoraCartaoVO
 * @see SuperEntidade
 */
public class OperadoraCartao extends SuperEntidade implements OperadoraCartaoInterfaceFacade {

    public OperadoraCartao() throws Exception {
        super();
    }

    public OperadoraCartao(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>OperadoraCartaoVO</code>.
     */
    public OperadoraCartaoVO novo() throws Exception {
        incluir(getIdEntidade());
        OperadoraCartaoVO obj = new OperadoraCartaoVO();
        return obj;
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.financeiro.OperadoraCartaoInterfaceFacade#incluir(negocio.comuns.financeiro.OperadoraCartaoVO)
     */
    public void incluir(OperadoraCartaoVO obj) throws Exception {
        incluir(obj, false);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>OperadoraCartaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>OperadoraCartaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(OperadoraCartaoVO obj, boolean centralEventos) throws Exception {
        try {
            OperadoraCartaoVO.validarDados(obj);
            validarPadraoRecebimento(obj);

            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO OperadoraCartao( codigoOperadora, descricao, credito, qtdemaxparcelas, "
                    + "codigoIntegracao,codigoIntegracaoAPF, codigointegracaovindi, codigointegracaocielo, codigoIntegracaoDebito, "
                    + "tipoDebitoOnline, codigointegracaoerede, codigointegracaomaxipago, ativo, codigointegracaofitnesscard, codigointegracaogetnet, "
                    + "bandeiraCappta, codigoIntegracaoStoneOnline, bandeirasgeoitd, pinpadgeoitd, codigoIntegracaomundipagg, codigoIntegracaopagarme, padraoRecebimento, "
                    + "codigoIntegracaostripe, codigoIntegracaopagolivre, codigoIntegracaoPinBank, codigoIntegracaoOnePayment, codigoIntegracaoFacilitePay, codigoIntegracaoCeopag, "
                    + "codigointegracaodcccaixaonline, codigoIntegracaoPagBank, codigointegracaostoneonlinev5) "
                    + " VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            int i = 0;
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setInt(++i, obj.getCodigoOperadora());
                sqlInserir.setString(++i, obj.getDescricao());
                sqlInserir.setBoolean(++i, obj.isCredito());
                sqlInserir.setInt(++i, obj.getQtdeMaxParcelas());
                sqlInserir.setInt(++i, obj.getCodigoIntegracao().getId());
                if (obj.getCodigoIntegracaoAPF() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoAPF().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoVindi() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoVindi().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoCielo() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoCielo().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoDebito() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoDebito().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getTipoDebitoOnline() != null) {
                    sqlInserir.setInt(++i, obj.getTipoDebitoOnline().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoERede() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoERede().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoMaxiPago() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoMaxiPago().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                sqlInserir.setBoolean(++i, obj.isAtivo());
                if (obj.getCodigoIntegracaoFitnessCard() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoFitnessCard().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoGetNet() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoGetNet().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getBandeiraCappta() != null) {
                    sqlInserir.setInt(++i, obj.getBandeiraCappta().getCodigo());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoStoneOnline() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoStoneOnline().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                sqlInserir.setString(++i,obj.getCodBandeiraGeoitd());
                sqlInserir.setBoolean(++i, obj.getGeoitd());
                if (obj.getCodigoIntegracaoMundiPagg() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoMundiPagg().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoPagarMe() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoPagarMe().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                sqlInserir.setBoolean(++i, obj.isPadraoRecebimento());
                if (obj.getCodigoIntegracaoStripe() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoStripe().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }

                if (obj.getCodigoIntegracaoPagoLivre() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoPagoLivre().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoPinBank() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoPinBank().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoOnePayment() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoOnePayment().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoFacilitePay() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoFacilitePay().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoCeopag() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoCeopag().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoDCCCaixaOnline() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoDCCCaixaOnline().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoPagBank() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoPagBank().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }
                if (obj.getCodigoIntegracaoStoneOnlineV5() != null) {
                    sqlInserir.setInt(++i, obj.getCodigoIntegracaoStoneOnlineV5().getId());
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }

                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.financeiro.OperadoraCartaoInterfaceFacade#incluir(negocio.comuns.financeiro.OperadoraCartaoVO)
     */
    public void alterar(OperadoraCartaoVO obj) throws Exception {
        alterar(obj, false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>OperadoraCartaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>OperadoraCartaoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(OperadoraCartaoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(OperadoraCartaoVO obj) throws Exception {
        OperadoraCartaoVO.validarDados(obj);
        validarPadraoRecebimento(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE OperadoraCartao set codigoOperadora=?, descricao=?, credito=?, qtdemaxparcelas=?,"
                + "codigoIntegracao=?, codigoIntegracaoAPF=?, codigointegracaovindi=?, codigointegracaocielo =?, taxa=?, codigoIntegracaoDebito = ?, tipoDebitoOnline = ?, "
                + "codigointegracaoerede = ?, codigointegracaomaxipago = ?, ativo = ?, codigointegracaofitnesscard = ?, codigointegracaogetnet = ?, bandeiraCappta = ?, "
                + "codigoIntegracaoStoneOnline = ?, codigoIntegracaomundipagg = ?, codigoIntegracaopagarme = ?, padraoRecebimento = ?, codigoIntegracaostripe = ?, "
                + "codigoIntegracaopagolivre = ?, codigoIntegracaoPinBank = ?, codigoIntegracaoOnePayment = ?, codigoIntegracaoFacilitePay = ?, codigoIntegracaoCeopag = ?, "
                + "codigointegracaodcccaixaonline = ?, codigoIntegracaoPagBank = ?, codigointegracaostoneonlinev5 = ? "
                + " WHERE codigo = ?";

        int i = 0;
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(++i, obj.getCodigoOperadora());
            sqlAlterar.setString(++i, obj.getDescricao());
            sqlAlterar.setBoolean(++i, obj.isCredito());
            sqlAlterar.setInt(++i, obj.getQtdeMaxParcelas());
            sqlAlterar.setInt(++i, obj.getCodigoIntegracao().getId());
            if (obj.getCodigoIntegracaoAPF() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoAPF().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoVindi() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoVindi().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoCielo() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoCielo().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            sqlAlterar.setDouble(++i, obj.getTaxa());
            if (obj.getCodigoIntegracaoDebito() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoDebito().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getTipoDebitoOnline() != null) {
                sqlAlterar.setInt(++i, obj.getTipoDebitoOnline().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoERede() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoERede().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoMaxiPago() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoMaxiPago().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            sqlAlterar.setBoolean(++i, obj.isAtivo());
            if (obj.getCodigoIntegracaoFitnessCard() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoFitnessCard().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoGetNet() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoGetNet().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getBandeiraCappta() != null) {
                sqlAlterar.setInt(++i, obj.getBandeiraCappta().getCodigo());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoStoneOnline() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoStoneOnline().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoMundiPagg() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoMundiPagg().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoPagarMe() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoPagarMe().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            sqlAlterar.setBoolean(++i, obj.isPadraoRecebimento());
            if (obj.getCodigoIntegracaoStripe() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoStripe().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoPagoLivre() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoPagoLivre().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoPinBank() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoPinBank().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoOnePayment() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoOnePayment().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoFacilitePay() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoFacilitePay().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoCeopag() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoCeopag().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoDCCCaixaOnline() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoDCCCaixaOnline().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoPagBank() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoPagBank().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            if (obj.getCodigoIntegracaoStoneOnlineV5() != null) {
                sqlAlterar.setInt(++i, obj.getCodigoIntegracaoStoneOnlineV5().getId());
            } else {
                sqlAlterar.setNull(++i, Types.NULL);
            }
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.financeiro.OperadoraCartaoInterfaceFacade#incluir(negocio.comuns.financeiro.OperadoraCartaoVO)
     */
    public void excluir(OperadoraCartaoVO obj) throws Exception {
        excluir(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>OperadoraCartaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>OperadoraCartaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(OperadoraCartaoVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }

            String sql = "DELETE FROM OperadoraCartao WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>OperadoraCartao</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>OperadoraCartaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorDescricao(valorConsulta, false, controlarAcesso, nivelMontarDados);
    }

    public List consultarPorDescricao(String valorConsulta, boolean somenteAtivos, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM operadoracartao \n");
        sql.append(" WHERE descricao ilike ('%").append(valorConsulta).append("%') \n");
        if (somenteAtivos){
            sql.append(" AND ativo \n");
        }
        sql.append(" ORDER BY descricao ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>OperadoraCartao</code> através do valor do atributo 
     * <code>Integer codigoOperadora</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>OperadoraCartaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoOperadora(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM OperadoraCartao WHERE codigoOperadora >= " + valorConsulta + " ORDER BY codigoOperadora";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>OperadoraCartao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>OperadoraCartaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM OperadoraCartao WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorTipo(boolean credito, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM OperadoraCartao WHERE ativo = true AND pinpadgeoitd = false AND credito = " + credito + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorTipoGeoidt(boolean credito, boolean controlarAcesso, int nivelMontarDados, boolean geoitd) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM OperadoraCartao WHERE ativo = true  AND credito = " + credito + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>OperadoraCartaoVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            OperadoraCartaoVO obj = new OperadoraCartaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>OperadoraCartaoVO</code>.
     * @return  O objeto da classe <code>OperadoraCartaoVO</code> com os dados devidamente montados.
     */
    public static OperadoraCartaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        OperadoraCartaoVO obj = new OperadoraCartaoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setCodigoOperadora(dadosSQL.getInt("codigoOperadora"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setCredito(dadosSQL.getBoolean("credito"));
        obj.setQtdeMaxParcelas(dadosSQL.getInt("qtdemaxparcelas"));
        obj.setCodigoIntegracao(OperadorasExternasPagamentoDigitalEnum.valueOf(dadosSQL.getInt("codigoIntegracao")));
        if (dadosSQL.getInt("codigoIntegracaoAPF") != 0) {
            obj.setCodigoIntegracaoAPF(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigoIntegracaoAPF")));
        }
        if(dadosSQL.getInt("codigointegracaovindi") != 0){
            obj.setCodigoIntegracaoVindi(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigointegracaovindi")));
        }
        if(dadosSQL.getInt("codigointegracaocielo") != 0){
            obj.setCodigoIntegracaoCielo(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigointegracaocielo")));
        }
        obj.setTaxa(dadosSQL.getDouble("taxa"));

        if (dadosSQL.getInt("codigoIntegracaoDebito") != 0) {
            obj.setCodigoIntegracaoDebito(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigoIntegracaoDebito")));
        }
        if (dadosSQL.getInt("tipoDebitoOnline") != 0) {
            obj.setTipoDebitoOnline(TipoDebitoOnlineEnum.getTipoDebitoOnlineEnum(dadosSQL.getInt("tipoDebitoOnline")));
        }
        if(dadosSQL.getInt("codigointegracaoerede") != 0){
            obj.setCodigoIntegracaoERede(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigointegracaoerede")));
        }
        if(dadosSQL.getInt("codigointegracaomaxipago") != 0){
            obj.setCodigoIntegracaoMaxiPago(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigointegracaomaxipago")));
        }
        obj.setAtivo(dadosSQL.getBoolean("ativo"));

        if(dadosSQL.getInt("codigointegracaofitnesscard") != 0){
            obj.setCodigoIntegracaoFitnessCard(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigointegracaofitnesscard")));
        }

        if(dadosSQL.getInt("codigointegracaogetnet") != 0){
            obj.setCodigoIntegracaoGetNet(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigointegracaogetnet")));
        }
        if(dadosSQL.getInt("bandeiraCappta") != 0){
            obj.setBandeiraCappta(BandeirasCapptaEnum.obterPorCodigo(dadosSQL.getInt("bandeiraCappta")));
        }
        if(dadosSQL.getInt("codigoIntegracaoStoneOnline") != 0){
            obj.setCodigoIntegracaoStoneOnline(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigoIntegracaoStoneOnline")));
        }
        if(dadosSQL.getInt("codigoIntegracaomundipagg") != 0){
            obj.setCodigoIntegracaoMundiPagg(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigoIntegracaomundipagg")));
        }
        if(dadosSQL.getInt("codigoIntegracaopagarme") != 0){
            obj.setCodigoIntegracaoPagarMe(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigoIntegracaopagarme")));
        }
        if(dadosSQL.getInt("codigoIntegracaostripe") != 0){
            obj.setCodigoIntegracaoStripe(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigoIntegracaostripe")));
        }
        if(dadosSQL.getInt("codigointegracaopagolivre") != 0){
            obj.setCodigoIntegracaoPagoLivre(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigointegracaopagolivre")));
        }
        if(dadosSQL.getInt("codigointegracaopinbank") != 0){
            obj.setCodigoIntegracaoPinBank(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigointegracaopinbank")));
        }
        if(dadosSQL.getInt("codigointegracaoonepayment") != 0){
            obj.setCodigoIntegracaoOnePayment(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigointegracaoonepayment")));
        }
        if(dadosSQL.getInt("codigoIntegracaoFacilitePay") != 0){
            obj.setCodigoIntegracaoFacilitePay(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigoIntegracaoFacilitePay")));
        }
        if(dadosSQL.getInt("codigoIntegracaoCeopag") != 0){
            obj.setCodigoIntegracaoCeopag(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigoIntegracaoCeopag")));
        }
        if(dadosSQL.getInt("codigoIntegracaoDccCaixaOnline") != 0){
            obj.setCodigoIntegracaoDCCCaixaOnline(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigoIntegracaoDccCaixaOnline")));
        }
        if(dadosSQL.getInt("codigoIntegracaoPagBank") != 0){
            obj.setCodigoIntegracaoPagBank(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigoIntegracaoPagBank")));
        }
        if(dadosSQL.getInt("codigointegracaostoneonlinev5") != 0){
            obj.setCodigoIntegracaoStoneOnlineV5(OperadorasExternasAprovaFacilEnum.valueOf(dadosSQL.getInt("codigointegracaostoneonlinev5")));
        }
        obj.setPadraoRecebimento(dadosSQL.getBoolean("padraoRecebimento"));

        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>OperadoraCartaoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public OperadoraCartaoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        OperadoraCartaoVO eCache = (OperadoraCartaoVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM OperadoraCartao WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( OperadoraCartao ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária, 
     * a apresentação do mesmo e a implementação de possíveis relacionamentos. 
     */
    public Integer obterValorChavePrimariaCodigo() throws Exception {
        inicializar();
        String sqlStr = "SELECT MAX(codigo) FROM OperadoraCartao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return (new Integer(tabelaResultado.getInt(1)));
            }
        }
    }

    public List consultarPorCodigoIntegracaoAPF(int codigoIntegracao, int nivelMontarDados) throws Exception {
        return consultarPorCodigoIntegracaoAPF(codigoIntegracao, null, null, nivelMontarDados);
    }

    public List consultarPorCodigoIntegracaoAPF(int codigoIntegracao, Boolean ativo, Boolean credito, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM OperadoraCartao \n");
        sql.append("WHERE codigoIntegracaoAPF = ").append(codigoIntegracao).append(" \n");
        if (ativo != null) {
            sql.append("AND ativo = ").append(ativo).append(" \n");
        }
        if (credito != null) {
            sql.append("AND credito = ").append(credito).append(" \n");
        }
        sql.append("ORDER BY codigo");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public String consultarJSON(String situacao) throws Exception {
        StringBuilder json;
        boolean dados;
        try (PreparedStatement ps = getRS(situacao)) {
            try (ResultSet rs = ps.executeQuery()) {

                json = new StringBuilder();
                json.append("{\"aaData\":[");
                dados = false;
                while (rs.next()) {
                    dados = true;
                    json.append("[\"").append(rs.getString("codigo")).append("\",");
                    json.append("\"").append(rs.getString("codigooperadora")).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
                    if (rs.getBoolean("ativo")) {
                        json.append("\"").append("ATIVO").append("\"],");
                    } else {
                        json.append("\"").append("INATIVO").append("\"],");
                    }
                }
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getRS(String situacao) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo, codigooperadora, descricao, ativo FROM operadoracartao ");
        if(situacao.equals("AT")) {
            sql.append("WHERE ativo = true ");
        } else if(situacao.equals("NA")) {
            sql.append("WHERE ativo = false ");
        }
       // sql.append(" AND pinpadgeoitd=" + isGeoitd );
        sql.append(" ORDER BY descricao");
        return con.prepareStatement(sql.toString());
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, String situacao) throws SQLException {
        List lista;
        try (PreparedStatement ps = getRS(situacao)) {
            try (ResultSet rs = ps.executeQuery()) {
                lista = new ArrayList();

                while (rs.next()) {

                    OperadoraCartaoVO oCartao = new OperadoraCartaoVO();
                    String geral = rs.getString("codigo") + rs.getString("codigooperadora") + rs.getString("descricao");
                    if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                        oCartao.setCodigo(rs.getInt("codigo"));
                        oCartao.setCodigoOperadora(rs.getInt("codigooperadora"));
                        oCartao.setDescricao(rs.getString("descricao"));
                        oCartao.setAtivo((rs.getBoolean("ativo")));
                        lista.add(oCartao);
                    }
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Código operadora:")) {
            Ordenacao.ordenarLista(lista, "codigoOperadora");
        } else if (campoOrdenacao.equals("Descrição:")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals(("Situação"))){
            Ordenacao.ordenarLista(lista,"ativo");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoVindi(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaovindi = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    public OperadoraCartaoVO cadastrarOperadoraCartaoParaVindi(AutorizacaoCobrancaClienteVO autorizacaoCliente) throws Exception{
        OperadoraCartaoVO operadoraCartaoVO = new OperadoraCartaoVO();
        operadoraCartaoVO.setCodigoIntegracaoVindi(autorizacaoCliente.getOperadoraCartao());
        operadoraCartaoVO.setCodigoOperadora(autorizacaoCliente.getOperadoraCartao().getId());
        operadoraCartaoVO.setCredito(true);
        operadoraCartaoVO.setQtdeMaxParcelas(1);
        operadoraCartaoVO.setDescricao(autorizacaoCliente.getOperadoraCartao().getDescricao() + " VINDI ");
        incluir(operadoraCartaoVO);
        return operadoraCartaoVO;
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoCielo(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaocielo = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    public List consultarPorTipoDebitoOnline(TipoDebitoOnlineEnum tipoDebitoOnlineEnum, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM OperadoraCartao WHERE credito  = false AND tipoDebitoOnline = " + tipoDebitoOnlineEnum.getId() +" ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoERede(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao WHERE codigointegracaoerede = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoPagoLivre(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaopagolivre = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoFacilitePay(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigoIntegracaoFacilitePay = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoCeopag(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigoIntegracaoCeopag = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoDCCCaixaOnline(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaodcccaixaonline = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoPinBank(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaopinbank = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoOnePayment(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaoonepayment = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoMaxiPago(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaomaxipago = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoFitnessCard(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaofitnesscard = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoGetNet(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaogetnet = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoStoneOnline(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaostoneonline = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    public OperadoraCartaoVO consultarPorCodigoIntegracaoAPF(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaoapf = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoMundiPagg(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigoIntegracaoMundipagg = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoPagarMe(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigoIntegracaoPagarme = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoPagBank(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigoIntegracaoPagBank = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }


    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoStoneOnlineV5(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigointegracaostoneonlinev5 = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarPorCodigoIntegracaoStripe(OperadorasExternasAprovaFacilEnum operadora, int nivelMontarDados, boolean somenteAtivas) throws Exception {
        String condicaoAtivas = somenteAtivas ? " and ativo" : "";
        String sql = "SELECT * FROM operadoracartao op WHERE op.codigoIntegracaostripe = ? " + condicaoAtivas + ";";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadora.getId());
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, nivelMontarDados) : null;
            }
        }
    }

    public OperadoraCartaoVO consultarOuCriarPorCodigoOperadora(Integer codigoOperadora, final String descricao, boolean credito, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM OperadoraCartao WHERE codigooperadora = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoOperadora);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    OperadoraCartaoVO nova = new OperadoraCartaoVO();
                    nova.setCodigoOperadora(codigoOperadora);
                    nova.setDescricao(descricao);
                    nova.setAtivo(true);
                    nova.setCredito(credito);
                    incluir(nova);
                    return nova;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public OperadoraCartaoVO consultarOuCriar(String descricao, Integer codigoOperadora, boolean credito, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM OperadoraCartao WHERE descricao = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, descricao);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    OperadoraCartaoVO nova = new OperadoraCartaoVO();
                    nova.setCodigoOperadora(codigoOperadora);
                    nova.setDescricao(descricao);
                    nova.setAtivo(true);
                    nova.setCredito(credito);
                    if (credito) {
                        nova.setQtdeMaxParcelas(12);
                    }
                    incluir(nova);
                    return nova;
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    @Override
    public OperadoraCartaoVO consultarOuCriaSeNaoExistirPorCodigoIntegracaoCappta(BandeirasCapptaEnum bandeirasCapptaEnum, boolean credito, int nivelMontarDados) {
        try {
            String sql = "SELECT * FROM operadoracartao WHERE ativo = true AND bandeiraCappta = ? AND credito = ?;";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setInt(1, bandeirasCapptaEnum.getCodigo());
                ps.setBoolean(2, credito);
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        return montarDados(rs, nivelMontarDados);
                    } else {
                        OperadoraCartaoVO nova = new OperadoraCartaoVO();
                        nova.setBandeiraCappta(bandeirasCapptaEnum);
                        nova.setAtivo(true);
                        nova.setDescricao(bandeirasCapptaEnum.getDescricao() + " - LINX");
                        nova.setCredito(credito);
                        if (credito) {
                            nova.setQtdeMaxParcelas(1);
                        }
                        incluir(nova);
                        return nova;
                    }
                }
            }
        } catch (Exception ex) {
            return new OperadoraCartaoVO();
        }
    }

    public List consultarTodas(boolean ativo, boolean credito, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM operadoracartao WHERE ativo = ? and credito = ?;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setBoolean(1, ativo);
            ps.setBoolean(2, credito);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<OperadoraCartaoVO> consultarTodas(boolean ativo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM operadoracartao WHERE ativo = ? ;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setBoolean(1, ativo);
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<OperadoraCartaoVO> consultarOperadorasTipoConvenio(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, boolean criarSeNaoExistir) throws Exception {
        try {
            List<OperadoraCartaoVO> lista = new ArrayList<>();
            for (OperadorasExternasAprovaFacilEnum operadora : OperadorasExternasAprovaFacilEnum.operadorasConvenio(tipoConvenioCobrancaEnum)) {
                OperadoraCartaoVO obj = consultarPorCodigoIntegracao(tipoConvenioCobrancaEnum, 12, operadora, criarSeNaoExistir, true);
                if (obj != null) {
                    lista.add(obj);
                }
            }
            Ordenacao.ordenarLista(lista, "descricao");
            return lista;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public void criarOperadorasAutomatico(List<OperadoraCartaoVO> listaAdicionar) throws Exception {
        try {
            con.setAutoCommit(false);

            for (OperadoraCartaoVO obj : listaAdicionar) {
                if (UteisValidacao.emptyNumber(obj.getCodigo())) {
                    incluir(obj);
                } else {
                    alterarSemCommit(obj);
                }
            }

            con.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            con.setAutoCommit(true);
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void validarPadraoRecebimento(OperadoraCartaoVO obj) throws Exception {
        if(obj.isPadraoRecebimento()) {
            String sql = "SELECT * FROM operadoracartao op WHERE op.ativo and op.padraoRecebimento and op.credito = " + obj.isCredito();
            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                sql += " and op.codigo <> " + obj.getCodigo();
            }
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        throw new Exception("Já existe uma operadora \"" + rs.getString("descricao") + "\" como padrão para recebimento " + (obj.isCredito() ? "crédito." : "débito."));
                    }
                }
            }
        }
    }

    public OperadoraCartaoVO obterOperadoraCartaoPadrao(boolean credito) {
        try {
            String sql = "SELECT * FROM operadoracartao op WHERE op.ativo and op.padraoRecebimento and op.credito = " + credito;
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }
                }
            }
            return  new OperadoraCartaoVO();
        } catch (Exception ex) {
            ex.printStackTrace();
            return new OperadoraCartaoVO();
        }
    }

    private OperadoraCartaoVO criarNova(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, Integer nrMaxParcelas,
                                        OperadorasExternasAprovaFacilEnum operadoraEnum) {
        OperadoraCartaoVO obj = new OperadoraCartaoVO();
        if (tipoConvenioCobrancaEnum.getTipoTransacao().equals(TipoTransacaoEnum.NENHUMA)) {
            obj.setDescricao(operadoraEnum.getDescricao().toUpperCase() + " - " + tipoConvenioCobrancaEnum.getDescricao());
        } else {
            obj.setDescricao(operadoraEnum.getDescricao().toUpperCase() + " - " + tipoConvenioCobrancaEnum.getTipoTransacao().getDescricao());
        }
        obj.setCredito(true);
        obj.setQtdeMaxParcelas(nrMaxParcelas);
        obj.setAtivo(true);
        obj.realizarUpperCaseDados();
        return obj;
    }

    public OperadoraCartaoVO consultarOuCriaPorCodigoIntegracao(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, Integer nrMaxParcelas,
                                                                OperadorasExternasAprovaFacilEnum operadoraEnum) throws Exception {

        OperadoraCartaoVO obj = consultarPorCodigoIntegracao(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum, true, true);
        if (obj != null && UteisValidacao.emptyNumber(obj.getCodigo())) {
            incluir(obj);
        }
        return obj;
    }

    private OperadoraCartaoVO consultarPorCodigoIntegracao(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, Integer nrMaxParcelas,
                                                          OperadorasExternasAprovaFacilEnum operadoraEnum, boolean criarSeNaoExistir, boolean somenteAtivas) throws Exception {

        if (UteisValidacao.emptyNumber(nrMaxParcelas)) {
            nrMaxParcelas = 1;
        }

        OperadoraCartaoVO obj = null;
        if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC) ||
                tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET) ||
                tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
            obj = consultarPorCodigoIntegracaoAPF(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoAPF(operadoraEnum);
            }
            return obj;
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
            obj = consultarPorCodigoIntegracaoMundiPagg(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoMundiPagg(operadoraEnum);
            }
            return obj;
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
            obj = consultarPorCodigoIntegracaoPagarMe(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoPagarMe(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
            obj = consultarPorCodigoIntegracaoPagBank(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoPagBank(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
            obj = consultarPorCodigoIntegracaoCielo(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoCielo(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
            obj = consultarPorCodigoIntegracaoStoneOnline(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoStoneOnline(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            obj = consultarPorCodigoIntegracaoStoneOnlineV5(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoStoneOnlineV5(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
            obj = consultarPorCodigoIntegracaoVindi(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoVindi(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            obj = consultarPorCodigoIntegracaoERede(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoERede(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
            obj = consultarPorCodigoIntegracaoGetNet(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoGetNet(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO)) {
            obj = consultarPorCodigoIntegracaoMaxiPago(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoMaxiPago(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_FITNESS_CARD)) {
            obj = consultarPorCodigoIntegracaoFitnessCard(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoFitnessCard(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
            obj = consultarPorCodigoIntegracaoStripe(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoStripe(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
            obj = consultarPorCodigoIntegracaoPagoLivre(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoPagoLivre(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
            obj = consultarPorCodigoIntegracaoFacilitePay(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoFacilitePay(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            obj = consultarPorCodigoIntegracaoPinBank(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoPinBank(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT)) {
            obj = consultarPorCodigoIntegracaoOnePayment(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoOnePayment(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CEOPAG)) {
            obj = consultarPorCodigoIntegracaoCeopag(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoCeopag(operadoraEnum);
            }
        } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE)) {
            obj = consultarPorCodigoIntegracaoDCCCaixaOnline(operadoraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, somenteAtivas);
            if (obj == null && criarSeNaoExistir) {
                obj = criarNova(tipoConvenioCobrancaEnum, nrMaxParcelas, operadoraEnum);
                obj.setCodigoIntegracaoDCCCaixaOnline(operadoraEnum);
            }
        }
        return obj;
    }

    public OperadoraCartaoVO consultarOuCriaSeNaoExistirPinpad(OperadorasExternasAprovaFacilEnum operadoraEnum,
                                                               OpcoesPinpadEnum opcoesPinpadEnum,
                                                               boolean credito, int nivelMontarDados) {
        try {
            String descricao = (operadoraEnum.getDescricao() + " - " + opcoesPinpadEnum.getNome()).toUpperCase();
            if (opcoesPinpadEnum.equals(OpcoesPinpadEnum.GETCARD)) {
                descricao = operadoraEnum.getDescricao().toUpperCase();
            }

            // Primeira tentativa: busca pela descrição específica (com pinpad)
            String sql = "SELECT * FROM operadoracartao WHERE ativo = true AND descricao ilike '%" + descricao + "%' AND credito = ?;";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setBoolean(1, credito);
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        return montarDados(rs, nivelMontarDados);
                    }
                }
            }

            // Segunda tentativa: busca pela descrição padrão
            String descricaoPadrao = operadoraEnum.getDescricaoOperadoraCartaoPadrao().toUpperCase();
            String sqlPadrao = "SELECT * FROM operadoracartao WHERE ativo = true AND descricao ilike '%" + descricaoPadrao + "%' AND credito = ?;";
            try (PreparedStatement psPadrao = con.prepareStatement(sqlPadrao)) {
                psPadrao.setBoolean(1, credito);
                try (ResultSet rsPadrao = psPadrao.executeQuery()) {
                    if (rsPadrao.next()) {
                        return montarDados(rsPadrao, nivelMontarDados);
                    } else {
                        // Se não encontrou nem pela descrição específica nem pela padrão, cria uma nova
                        OperadoraCartaoVO nova = new OperadoraCartaoVO();
                        nova.setAtivo(true);
                        nova.setDescricao(descricaoPadrao);
                        nova.setCredito(credito);
                        if (credito) {
                            nova.setQtdeMaxParcelas(12);
                        }
                        incluir(nova);
                        return nova;
                    }
                }
            }
        } catch (Exception ex) {
            return new OperadoraCartaoVO();
        }
    }

    public boolean existeOperadoraComCodigoIntegracao(OperadoraCartaoVO operadoraVO) throws Exception {
        return verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoVindi(), "codigointegracaovindi")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoCielo(), "codigointegracaocielo")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoERede(), "codigointegracaoerede")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoMaxiPago(), "codigointegracaomaxipago")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoFitnessCard(), "codigointegracaofitnesscard")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoGetNet(), "codigointegracaogetnet")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoStoneOnline(), "codigointegracaostoneonline")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoMundiPagg(), "codigointegracaomundipagg")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoPagarMe(), "codigointegracaopagarme")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoStripe(), "codigointegracaostripe")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoPagoLivre(), "codigointegracaopagolivre")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoPinBank(), "codigointegracaopinbank")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoOnePayment(), "codigointegracaoonepayment")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoFacilitePay(), "codigointegracaofacilitepay")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoCeopag(), "codigointegracaoceopag")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoDCCCaixaOnline(), "codigointegracaodcccaixaonline")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoPagBank(), "codigoIntegracaoPagBank")
                || verificaExistenciaCodigoIntegracao(operadoraVO.getCodigoIntegracaoStoneOnlineV5(), "codigointegracaostoneonlinev5");
    }

    private boolean verificaExistenciaCodigoIntegracao(OperadorasExternasAprovaFacilEnum operadoraEnum, String colunaIntegracao) throws Exception {
        if (operadoraEnum == null || UteisValidacao.emptyNumber(operadoraEnum.getId())) {
            return false;
        }

        String sql = "SELECT EXISTS (SELECT 1 FROM operadoracartao WHERE " + colunaIntegracao + " = ?);";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, operadoraEnum.getId());
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getBoolean("exists");
                }
            }
        }
        return false;
    }

    public OperadoraCartaoVO consultarOperadoraPadraoPorDescricao(String valorConsulta, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM operadoracartao \n");
        sql.append("WHERE descricao ilike '").append(valorConsulta).append("' \n");
        sql.append("ORDER BY codigo ASC;");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return tabelaResultado.next() ? montarDados(tabelaResultado, nivelMontarDados) : null;
            }
        }
    }

}
