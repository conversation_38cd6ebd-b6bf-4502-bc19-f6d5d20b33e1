/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.enumeradores.SituacaoItemExtratoEnum;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoDadoConciliacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import controle.financeiro.EstornoReciboControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.ExtratoDiarioItemVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.interfaces.financeiro.ExtratoDiarioItemInterfaceFacade;
import org.apache.commons.beanutils.BeanUtils;
import org.json.JSONObject;
import servicos.impl.dcc.bin.ExtratoBinTipoRegistroEnum;
import servicos.impl.dcc.cielo.OrigemAjusteCieloEnum;
import servicos.impl.dcc.getnet.TipoRegistroExtratoGetNetEnum;
import servicos.impl.dcc.rede.ExtratoRedeTipoRegistroEnum;
import servicos.impl.dcc.rede.TipoArquivoRedeEnum;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.sql.*;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public class ExtratoDiarioItem extends SuperEntidade implements ExtratoDiarioItemInterfaceFacade {

    public ExtratoDiarioItem() throws Exception {
    }

    public ExtratoDiarioItem(Connection conex) throws Exception {
        super(conex);
    }

    public void incluir(ExtratoDiarioItemVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO extratodiarioitem (arquivo,taxa, ro, props, datalancamento, dataprevistapagamento, ");
        sql.append("autorizacao, estabelecimento, nrcartao, valorbruto, valorliquido, valorcomissao, ");
        sql.append("codigomovpagamento,codigomovconta, codigocartaocredito, situacao, nrparcela, tiporegistro, tipoconciliacao, ");
        sql.append("conveniocobranca, dataProcessamentoExtrato,dataReprocessamento, nrTotalParcelas,credito,identificadorproduto, ");
        sql.append("tipoArquivo,apresentarExtrato,empresa,nsu, estorno, tipoConvenioCobranca, antecipacao, dataPgtoOriginalAntesDaAntecipacao, pessoa, ");
        sql.append("valorDescontadoAntecipacao, taxaCalculadaAntecipacao, observacao, idExterno, idExterno2, alterouDataRecebimentoZWAutomaticamente, dataPgtoOriginalZWAntesDaAlteracaoAutomatica, tipoParcelamento) ");
        sql.append("VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 1;
        stm.setString(i++, obj.getArquivo());
        stm.setDouble(i++, obj.getTaxa());
        stm.setString(i++, obj.getRo());
        if (!UteisValidacao.emptyString(obj.getPropsString())) {
            stm.setString(i++, obj.getPropsString().toString());
        } else {
            stm.setString(i++, obj.getProps().toString());
        }
        stm.setDate(i++, Uteis.getDataJDBC(obj.getDataLancamento()));
        stm.setDate(i++, Uteis.getDataJDBC(obj.getDataPrevistaPagamento()));
        stm.setString(i++, obj.getAutorizacao());
        stm.setString(i++, obj.getEstabelecimento());
        stm.setString(i++, obj.getNrCartao());


        if (obj.getValorBruto() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setDouble(i++, obj.getValorBruto());
        }

        if (obj.getValorLiquido() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setDouble(i++, obj.getValorLiquido());
        }

        if (obj.getValorComissao() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setDouble(i++, obj.getValorComissao());
        }

        if (obj.getCodigoMovPagamento() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setInt(i++, obj.getCodigoMovPagamento());
        }

        stm.setInt(i++, obj.getCodigoMovConta());
        stm.setInt(i++, obj.getCodigoCartaoCredito());
        if (obj.getSituacao() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setInt(i++, obj.getSituacao().ordinal());
        }

        stm.setInt(i++, obj.getNrParcela());
        stm.setString(i++, obj.getTipoRegistro());

        if (obj.getTipoConciliacao() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setInt(i++, obj.getTipoConciliacao());
        }

        stm.setInt(i++, obj.getConvenio().getCodigo());
        stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataProcessamentoExtrato()));
        stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataReprocessamento()));
        stm.setInt(i++, obj.getNrTotalParcelas());
        stm.setBoolean(i++, obj.getCredito());
        stm.setString(i++, obj.getIdentificadorProduto());
        stm.setString(i++, obj.getTipoArquivo());
        stm.setBoolean(i++, obj.isApresentarExtrato());
        stm.setInt(i++, obj.getEmpresa().getCodigo());
        stm.setString(i++, obj.getNsu());
        stm.setBoolean(i++, obj.isEstorno());
        stm.setInt(i++, obj.getTipoConvenioCobrancaEnum().getCodigo());
        stm.setBoolean(i++, obj.isAntecipacao());
        if (obj.getDataPgtoOriginalAntesDaAntecipacao() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setDate(i++, Uteis.getDataJDBC(obj.getDataPgtoOriginalAntesDaAntecipacao()));
        }
        if (obj.getPessoa() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setInt(i++, obj.getPessoa().getCodigo());
        }
        if (UteisValidacao.emptyNumber(obj.getValorDescontadoAntecipacao())) {
            stm.setNull(i++, 0);
        } else {
            stm.setDouble(i++, obj.getValorDescontadoAntecipacao());
        }
        if (UteisValidacao.emptyNumber(obj.getTaxaCalculadaAntecipacao())) {
            stm.setNull(i++, 0);
        } else {
            stm.setDouble(i++, obj.getTaxaCalculadaAntecipacao());
        }
        stm.setString(i++, obj.getObservacao());
        stm.setString(i++, obj.getIdExterno());
        stm.setString(i++, obj.getIdExterno2());
        stm.setBoolean(i++, obj.isAlterouDataRecebimentoZWAutomaticamente());
        if (obj.getDataPgtoOriginalZWAntesDaAlteracaoAutomatica() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setDate(i++, Uteis.getDataJDBC(obj.getDataPgtoOriginalZWAntesDaAlteracaoAutomatica()));
        }
        stm.setInt(i++, obj.getTipoParcelamento());

        stm.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    public void alterar(ExtratoDiarioItemVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" UPDATE extratodiarioitem set taxa = ?, ro = ? , props = ?, datalancamento = ? , dataprevistapagamento = ?, \n");
        sql.append(" autorizacao = ?, estabelecimento = ?, nrcartao = ?, valorbruto = ?, valorliquido = ?, valorcomissao = ?, \n");
        sql.append(" codigomovpagamento = ?, codigomovconta = ?, codigocartaocredito = ?, situacao = ?, nrparcela = ?, tiporegistro = ?, tipoconciliacao = ?, \n");
        sql.append(" dataProcessamentoExtrato = ?, dataReprocessamento = ?, nrTotalParcelas = ?, credito=?, identificadorproduto=?, tipoArquivo=?, apresentarExtrato = ?, \n" +
                " empresa = ?, nsu = ?, estorno = ?, antecipacao = ?, dataPgtoOriginalAntesDaAntecipacao = ?, pessoa = ?, valorDescontadoAntecipacao = ?, \n" +
                " taxaCalculadaAntecipacao = ?, observacao = ?, idExterno = ?, idExterno2 = ?, alterouDataRecebimentoZWAutomaticamente = ?, dataPgtoOriginalZWAntesDaAlteracaoAutomatica = ? \n" +
                " WHERE codigo = ?");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 1;
        stm.setDouble(i++, obj.getTaxa());
        stm.setString(i++, obj.getRo());
        if (!UteisValidacao.emptyString(obj.getPropsString())) {
            stm.setString(i++, obj.getPropsString().toString());
        } else {
            stm.setString(i++, obj.getProps().toString());
        }
        stm.setDate(i++, Uteis.getDataJDBC(obj.getDataLancamento()));
        stm.setDate(i++, Uteis.getDataJDBC(obj.getDataPrevistaPagamento()));
        stm.setString(i++, obj.getAutorizacao());
        stm.setString(i++, obj.getEstabelecimento());
        stm.setString(i++, obj.getNrCartao());
        stm.setDouble(i++, obj.getValorBruto());
        if (obj.getValorLiquido() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setDouble(i++, obj.getValorLiquido());
        }
        if (obj.getValorComissao() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setDouble(i++, obj.getValorComissao());
        }
        stm.setInt(i++, obj.getCodigoMovPagamento());
        stm.setInt(i++, obj.getCodigoMovConta());
        stm.setInt(i++, obj.getCodigoCartaoCredito());
        if (obj.getSituacao() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setInt(i++, obj.getSituacao().ordinal());
        }
        stm.setInt(i++, obj.getNrParcela());
        stm.setString(i++, obj.getTipoRegistro());
        stm.setInt(i++, obj.getTipoConciliacao());
        stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataProcessamentoExtrato()));
        stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataReprocessamento()));
        stm.setInt(i++, obj.getNrTotalParcelas());
        stm.setBoolean(i++, obj.getCredito());
        stm.setString(i++, obj.getIdentificadorProduto());
        stm.setString(i++, obj.getTipoArquivo());
        stm.setBoolean(i++, obj.isApresentarExtrato());
        stm.setInt(i++, obj.getEmpresa().getCodigo());
        stm.setString(i++, obj.getNsu());
        stm.setBoolean(i++, obj.isEstorno());
        stm.setBoolean(i++, obj.isAntecipacao());
        if (obj.getDataPgtoOriginalAntesDaAntecipacao() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setDate(i++, Uteis.getDataJDBC(obj.getDataPgtoOriginalAntesDaAntecipacao()));
        }
        if (obj.getPessoa() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setInt(i++, obj.getPessoa().getCodigo());
        }
        if (UteisValidacao.emptyNumber(obj.getValorDescontadoAntecipacao())) {
            stm.setNull(i++, 0);
        } else {
            stm.setDouble(i++, obj.getValorDescontadoAntecipacao());
        }
        if (UteisValidacao.emptyNumber(obj.getTaxaCalculadaAntecipacao())) {
            stm.setNull(i++, 0);
        } else {
            stm.setDouble(i++, obj.getTaxaCalculadaAntecipacao());
        }
        stm.setString(i++, obj.getObservacao());
        stm.setString(i++, obj.getIdExterno());
        stm.setString(i++, obj.getIdExterno2());
        stm.setBoolean(i++, obj.isAlterouDataRecebimentoZWAutomaticamente());
        if (obj.getDataPgtoOriginalZWAntesDaAlteracaoAutomatica() == null) {
            stm.setNull(i++, 0);
        } else {
            stm.setDate(i++, Uteis.getDataJDBC(obj.getDataPgtoOriginalZWAntesDaAlteracaoAutomatica()));
        }

        stm.setInt(i++, obj.getCodigo());
        stm.execute();
    }

    @Override
    public List<ExtratoDiarioItemVO> consultarExtratoPorDia(Date inicio, Date fim) throws Exception {
        return montarDadosConsulta(consultarExtrato(inicio, fim), this.con);
    }

    public Map<String, ExtratoDiarioItemVO> consultarMapaExtratoDiario(Date inicio, Date fim) throws Exception {
        Map<String, ExtratoDiarioItemVO> mapa = new HashMap<String, ExtratoDiarioItemVO>();
        ResultSet rs = consultarExtrato(inicio, fim);
        while (rs.next()) {
            ExtratoDiarioItemVO item = montarDados(rs, con);
            mapa.put(item.getAutorizacao(), item);
        }
        return mapa;
    }

    private ResultSet consultarExtrato(Date inicio, Date fim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT mp.nomepagador, p.nome, ei.* FROM extratodiarioitem ei ");
        sql.append(" LEFT JOIN movpagamento mp ON mp.codigo = ei.codigomovpagamento ");
        sql.append(" LEFT JOIN cartaocredito cc ON cc.codigo = ei.codigocartaocredito ");
        sql.append(" LEFT JOIN movconta mc ON mc.codigo = ei.codigomovconta ");
        sql.append(" LEFT JOIN pessoa p ON mc.pessoa = p.codigo ");
        sql.append(" WHERE ei.datalancamento BETWEEN ? AND ? ");
        sql.append(" OR mp.datalancamento BETWEEN ? AND ? ");
        sql.append(" ORDER BY situacao, mp.nomepagador, p.nome, autorizacao");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59")));
        stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
        stm.setTimestamp(4, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59")));
        return stm.executeQuery();
    }

    @Override
    public List<ExtratoDiarioItemVO> consultarExtratoParaConciliacao(Date inicio, Date fim, String ro, String autorizacao, Integer tipoConciliacao,
                                                                     Integer empresa, Integer convenio, Integer formaPagamento, Boolean apresentarPagamentosCancelados,
                                                                     Integer operadoraCartao, Integer adquirente, String nsu) throws Exception {
        List<ExtratoDiarioItemVO> lista = new ArrayList<ExtratoDiarioItemVO>();
        boolean filtrandoPorCartao = validarFiltroPorCartao(adquirente, operadoraCartao, autorizacao, ro);
        ResultSet rs = consultarConciliadosPorMovpagamento(inicio,fim,ro,autorizacao, tipoConciliacao, empresa, convenio,formaPagamento,apresentarPagamentosCancelados, operadoraCartao, adquirente, nsu);
        lista.addAll(montarDadosConciliacao(rs, true, filtrandoPorCartao, con, false, tipoConciliacao, false, TipoDadoConciliacaoEnum.Movpagamento));
        if(UteisValidacao.emptyNumber(formaPagamento)){
            ResultSet rsEstornados = consultarExtratoConcilicaoEstornado(inicio,fim,ro,autorizacao, tipoConciliacao, empresa, convenio, nsu);
            lista.addAll(montarDadosConciliacao(rsEstornados, true, false, con, false, tipoConciliacao, false, TipoDadoConciliacaoEnum.Estornado));
        }

        //Adiciona a lista MovPagamentos com ProdutosPagos CC, adicionado direto do Caixa em Aberto e não foram utilizados
        ResultSet rsCC = consultarExtratoConcilicaoMovpagamentoContaCorrente(inicio,fim,ro,autorizacao, tipoConciliacao, empresa, convenio,formaPagamento,apresentarPagamentosCancelados, operadoraCartao, adquirente, nsu);
        lista.addAll(montarDadosConciliacao(rsCC, true, filtrandoPorCartao, con, true, tipoConciliacao, false, TipoDadoConciliacaoEnum.MovPagamentoContaCorrente));

        //Adiciona a lista MovPagamentos de ContaCorrenteOrigem preenchido, para CC que foi utilizado e movimentado
        ResultSet rsCCDataAlterada = consultarCCDataAlterada(inicio,fim,ro,autorizacao, tipoConciliacao, empresa, convenio,formaPagamento,apresentarPagamentosCancelados, operadoraCartao, adquirente, nsu);
        lista.addAll(montarDadosConciliacao(rsCCDataAlterada, true, filtrandoPorCartao, con, false, tipoConciliacao, false, TipoDadoConciliacaoEnum.CCDataAlterada));

        //Se esse metodo retirar os valores duplicados de lançamentos errados do cliente, não mexer no código, orientar o cliente a lançar corretamente
        //Ex: Dois lançamentos distintos com o mesmo código de autorização, sendo que deveria ter lançamento único por ser um Pagamento Conjunto.
        lista = tratarVendasProdutosDiferentesMesmaAutorizacaoEReciboContaCorrente(lista, con);
        incluirContaMovimentoParaContaCorrente(lista, con);

        ResultSet rsMovconta = consultarExtratoConcilicaoMovConta(inicio,fim,ro,autorizacao, tipoConciliacao, empresa, convenio, nsu);
        lista.addAll(montarDadosConciliacao(rsMovconta, false, false, con, false, tipoConciliacao, true, TipoDadoConciliacaoEnum.MovConta));

        //Se tiver ExtratoDiarioItem sem MovPagamento na data do filtro, vem nessa consulta
        ResultSet rsNao = consultarExtratoNaoConciliado(inicio,fim,ro,autorizacao, tipoConciliacao, empresa, convenio,formaPagamento,apresentarPagamentosCancelados, operadoraCartao, adquirente, nsu);
        lista.addAll(montarDadosConciliacao(rsNao, false, false, con, false, tipoConciliacao, true, TipoDadoConciliacaoEnum.NaoConciliado));

        //Se tiver ExtratoDiarioItem de Conta Corrente não utilizada (Sem MovPagamentoOrigemCredito), que foi Movimentado para uma data diferente do Extrato, vem nessa consulta para Compensação
        if (tipoConciliacao.equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) {
            ResultSet rsCCNao = consultarExtratoCCMovimentadoNaoUtilizado(inicio,fim,ro,autorizacao, tipoConciliacao, empresa, convenio,formaPagamento,apresentarPagamentosCancelados, operadoraCartao, adquirente, nsu, lista);
            lista.addAll(montarDadosConciliacao(rsCCNao, true, false, con, false, tipoConciliacao, true, TipoDadoConciliacaoEnum.NaoConciliado));
        }

        //Se tiver MovPagamento sem ExtratoDiarioItem, vem nessa consulta (Vermelho)
        adicionaMovPagamentoNaoConciliado(inicio, fim, ro, autorizacao, tipoConciliacao, empresa, convenio, formaPagamento, apresentarPagamentosCancelados, operadoraCartao, adquirente, nsu, lista);

        //Ajusta o valor de itens na lista com MovPagamentos sem ProdutosPagos CC, mas que são Saldo de Conta Corrente vindo de Cancelamento de Contrato do mesmo aluno
        //OBS: Não fiz com saldo de Terceiro, não sei como vai se comportar nesse cenário
        ajustarValorMovpagamentoOuCartaoCreditoComSaldoEnviadoDeCancelamentoContratoParaContaCorrente(lista, con);
        validarSituacaoExtratoDiarioItemVO(lista);
        return lista;
    }

    private void adicionaMovPagamentoNaoConciliado(Date inicio, Date fim, String ro, String autorizacao, Integer tipoConciliacao, Integer empresa, Integer convenio, Integer formaPagamento, Boolean apresentarPagamentosCancelados, Integer operadoraCartao, Integer adquirente, String nsu, List<ExtratoDiarioItemVO> lista) throws Exception {
        if (tipoConciliacao.equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) {
            ResultSet rsMPsED = consultarMovPagamentoSemExtratoDiario(inicio, fim, ro, autorizacao, tipoConciliacao, empresa, convenio, formaPagamento, apresentarPagamentosCancelados, operadoraCartao, adquirente, nsu);
            List<ExtratoDiarioItemVO> listaResultado = montarDadosConciliacao(rsMPsED, true, false, con, false, tipoConciliacao, true,
                    TipoDadoConciliacaoEnum.NaoConciliado);
            for (ExtratoDiarioItemVO item : listaResultado) {
                boolean contain = false;
                for (ExtratoDiarioItemVO itemLista : lista) {
                    if (itemLista.getCodigoMovPagamento().equals(item.getCodigoMovPagamento())) {
                        contain = true;
                        break;
                    }
                }
                if (!contain && UteisValidacao.emptyNumber(item.getCodigo())) {
                    lista.add(item);
                }
            }
        }
    }

    public ResultSet consultarCCDataAlterada(Date inicio, Date fim, String ro,
                                                                          String autorizacao, Integer tipoConciliacao,
                                                                          Integer empresa, Integer convenio, Integer formaPagamento,
                                                                          Boolean apresentarPagamentosCancelados, Integer operadoraCartao, Integer adquirente, String nsu) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT mp.operadoracartao, mp.adquirente, ad.nome as nomeAdquirente ,ei.props,ei.tiporegistro, ei.estabelecimento, pes.codigo as codigopessoa, ei.tipoconciliacao,  fp.tipoformapagamento,fp.codigo as codigoformapagamento, fp.descricao as formapgto, mp.nomepagador, ei.codigo, ei.datalancamento as lancei, \n");
        sql.append(" ei.autorizacao as autorizacaoei, mp.autorizacaocartao as autorizacaomp, ei.nrtotalparcelas, mp.produtospagos, mp.movpagamentoorigemcredito, mp.recibopagamento, \n");
        sql.append(" mp.valor as valormp, mp.datalancamento as lancmp, mp.datapagamento as datapagamentomp, \n");
        sql.append(" coalesce(cc.valor,ccpag.valor) as valorcc, coalesce(cc.datacompesancao,ccpag.datacompesancao) as compensacaocc,mp.nrparcelacartaocredito, cc.nrparcela, \n");
        sql.append(" ei.dataprevistapagamento, ei.autorizacao as autorizacaoei, \n");
        sql.append(" ei.nrcartao, ei.valorbruto, ei.ro, ei.valorliquido, ei.valorcomissao, \n");
        sql.append(" ei.codigomovpagamento as codigoMovPagamentoExtrato, mp.codigo as codigomovpagamento,  ei.codigomovconta, ei.codigocartaocredito as codigocartaocreditoextrato, ccpag.codigo as codigocartaocredito, mp.movconta,  \n");
        sql.append(" ei.situacao, ei.nrparcela as nrParcelaei,  ei.taxa, coalesce(cc.nrparcela,ccpag.nrparcela) nrparcelacartao, case when length(coalesce(conta.descricaocurta,'')) > 1 then conta.descricaocurta else conta.descricao end as contamovimento, ei.credito,ei.identificadorproduto, ei.nsu, ei.tipoarquivo, ei.estorno \n");
        sql.append(" ,pes2.nome as nomeAluno, cli.matricula, ei.tipoconveniocobranca, \n");
        sql.append(" coalesce(cc.composicao, ccpag.composicao ) as composicao, ei.antecipacao, ei.datapgtooriginalantesdaantecipacao, ei.pessoa, ei.valorDescontadoAntecipacao, ei.taxaCalculadaAntecipacao, \n");
        sql.append(" ei.alterouDataRecebimentoZWAutomaticamente, ei.dataPgtoOriginalZWAntesDaAlteracaoAutomatica \n");
        sql.append(" FROM movpagamento mp \n");
        sql.append(" LEFT JOIN pessoa pes on pes.codigo = mp.pessoa \n");
        sql.append(" LEFT JOIN (select a.*, b.tipoconvenio from extratodiarioitem a left join conveniocobranca b on  a.conveniocobranca = b.codigo \n");

        if (tipoConciliacao == 4) {
            sql.append(" WHERE a.tipoconciliacao in (6, " + tipoConciliacao + ")");
        } else {
            sql.append(" WHERE a.tipoconciliacao in (" + tipoConciliacao + ")");
        }
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao)) {
            sql.append(" AND  (b.tipoconvenio <> 16 or ( b.tipoconvenio = 16 and a.tipoarquivo in ('EEVC', 'EEVD', 'API_REDE'))) \n");
        } else {
            sql.append(" AND  (b.tipoconvenio <> 16 or ( b.tipoconvenio = 16 and a.tipoarquivo in ('EEVC', 'EEVD', 'EEFI', 'API_REDE'))) \n");
        }
        sql.append(" ) ei ON mp.codigo = ei.codigomovpagamento \n");

        sql.append(" LEFT JOIN pessoa pes2 on pes2.codigo = ei.pessoa \n");
        sql.append(" LEFT JOIN cliente cli on cli.pessoa = pes2.codigo \n");
        sql.append(" LEFT JOIN cartaocredito cc ON cc.codigo = ei.codigocartaocredito \n");
        sql.append(" LEFT JOIN cartaocredito ccpag ON ccpag.movpagamento = mp.codigo \n");
        sql.append(" LEFT JOIN formapagamento fp ON fp.codigo = mp.formapagamento \n");
        sql.append(" LEFT JOIN movconta mc ON mc.codigo = mp.movconta \n");
        sql.append(" LEFT JOIN conta ON conta.codigo = mc.conta  \n");
        sql.append(" LEFT JOIN adquirente ad on ad.codigo=mp.adquirente \n");

        sql.append(" WHERE (apresentarExtrato = true or apresentarExtrato is null) \n");
        sql.append(" AND fp.tipoformapagamento in ('CA', 'CD') AND mp.valor > 0 \n");
        if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND ((ei.dataprevistapagamento BETWEEN ? AND ?  and (cc.codigo is null or cc.codigo = ccpag.codigo) ) \n");
            sql.append(" OR (mp.dataPagamento BETWEEN ? AND ? AND ei.codigo is null AND fp.tipoformapagamento  = 'CD') \n");
            sql.append(" OR (ccpag.datacompesancao BETWEEN ? AND ? AND ei.codigo is null AND fp.tipoformapagamento  = 'CA')) \n");
        }
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND ((ei.datalancamento BETWEEN ? AND ? ) \n");
            sql.append(" OR (mp.datalancamento BETWEEN ? AND ? AND ei.codigo is null)) \n");
        }
        if (!UteisValidacao.emptyString(ro)) {
            sql.append(" AND ((CAST(coalesce(ei.ro, '0') AS integer) = ?)) \n");
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            sql.append(" AND (ei.autorizacao ilike ? or mp.autorizacaocartao ilike ?) \n");
        }
        if (!UteisValidacao.emptyString(nsu)) {
            sql.append(" AND (ei.nsu ilike '").append(nsu).append("' or mp.nsu ilike '").append(nsu).append("') \n");
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            sql.append(" AND ei.conveniocobranca = ? \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND mp.empresa = ? \n");
        }

        sql.append(" AND mp.recibopagamento is not null \n");
        sql.append(" AND ((mp.movpagamentoorigemcredito is null) or (mp.movpagamentoorigemcredito is not null and valorbruto is not null)) \n");

        if (!UteisValidacao.emptyNumber(formaPagamento)) {
            sql.append(" AND mp.formapagamento = ? \n");
        }
        if (!apresentarPagamentosCancelados) {
            sql.append(" AND (coalesce(ccpag.situacao, '') NOT IN ('CA'))\n");
        }
        sql.append(" AND mp.movpagamentoorigemcredito NOTNULL ");
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            sql.append("AND mp.operadoracartao = ? \n");
        }
        if (!UteisValidacao.emptyNumber(adquirente)) {
            sql.append(" and mp.adquirente= ").append(adquirente);
        }
        if(tipoConciliacao == TipoConciliacaoEnum.CHARGEBACK.getCodigo()){
            sql.append(" and ei.estorno = true");
        }
        int i = 1;
        sql.append(" ORDER BY situacao, mp.nomepagador, autorizacao ");
        PreparedStatement stm = con.prepareStatement(sql.toString());

        if (inicio != null && fim != null && tipoConciliacao != TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
            if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao)) {
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
            }
        }

        if (!UteisValidacao.emptyString(ro)) {
            Integer roInt = 0;
            try {
                roInt = Integer.valueOf(ro);
            } catch (Exception e) {
            }
            stm.setInt(i++, roInt);
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            stm.setString(i++, "%" + autorizacao);
            stm.setString(i++, "%" + autorizacao);
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            stm.setInt(i++, convenio);
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            stm.setInt(i++, empresa);
        }
        if (!UteisValidacao.emptyNumber(formaPagamento)) {
            stm.setInt(i++, formaPagamento);
        }
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            stm.setInt(i++, operadoraCartao);
        }

        return stm.executeQuery();
    }

    private List<ExtratoDiarioItemVO> tratarVendasProdutosDiferentesMesmaAutorizacaoEReciboContaCorrente(List<ExtratoDiarioItemVO> lista, Connection con) throws SQLException {
        List<ExtratoDiarioItemVO> listaSemDuplicados = new ArrayList<>();
        Map<String, Object[]> mapaAutorizacoesJaProcessadas = new HashMap<>();
        for (ExtratoDiarioItemVO item: lista) {
            if (item.getMovPagamento() != null) {
                String[] conteudoArray;
                if (item.getTipoFormaPagamento().equals("CA") && item.getCartao() != null) {
                    conteudoArray = new String[]{item.getMovPagamento().getMovPagamentoOrigemCredito().toString(), item.getMovPagamento().getProdutosPagos(), Integer.toString(item.getCartao().getNrParcela())};
                } else {
                    conteudoArray = new String[]{item.getMovPagamento().getMovPagamentoOrigemCredito().toString(), item.getMovPagamento().getProdutosPagos(), "0"};
                }

                if (!mapaAutorizacoesJaProcessadas.containsKey(item.getMovPagamento().getAutorizacaoCartao())) {
                    mapaAutorizacoesJaProcessadas.put(item.getMovPagamento().getAutorizacaoCartao(), conteudoArray);
                } else if (item.getTipoFormaPagamento() != null && item.getTipoFormaPagamento().equals("CA")){
                    String[] nrParcela = (String[]) mapaAutorizacoesJaProcessadas.get(item.getMovPagamento().getAutorizacaoCartao());
                    if(item.getMovPagamento().getValor() >= 0.0 && item.getCartao() != null && !nrParcela[2].equals(Integer.toString(item.getCartao().getNrParcela())) && !conteudoArray[1].contains("CC")){
                        //Precisa dessa parte porquê cartão de crédito pode antecipar e vir mais de um no mesmo Extrato e não pode retirar, pois o Número da Parcela é diferente
                        mapaAutorizacoesJaProcessadas.put(item.getMovPagamento().getAutorizacaoCartao(), conteudoArray);
                    } else if (item.getMovPagamento().getValor() < 0.0 || item.getValorBruto() < 0.0) {
                        //Precisa dessa parte porquê pode vir Cancelamento parcial no mesmo dia
                        mapaAutorizacoesJaProcessadas.put(item.getMovPagamento().getAutorizacaoCartao(), conteudoArray);
                    } else {
                        continue;
                    }
                } else {
                    continue;
                }
            }

            if (item.getMovPagamento() != null) {

                StringBuilder sqlCount = new StringBuilder();
                sqlCount.append("SELECT COUNT(codigo)");
                sqlCount.append(" FROM movpagamento WHERE recibopagamento = " + item.getMovPagamento().getReciboPagamento().getCodigo() );
                sqlCount.append(" AND autorizacaocartao = '" + item.getMovPagamento().getAutorizacaoCartao() + "' AND produtospagos LIKE '%CC%';");
                PreparedStatement stm = con.prepareStatement(sqlCount.toString());
                ResultSet rs = stm.executeQuery();

                while (rs.next()) {
                    if (rs.getInt("count") == 1) {
                        Double totalListaReciboAutorizacaoRepetido = 0.0;

                        StringBuilder sqlItem = new StringBuilder();
                        sqlItem.append("select codigo, recibopagamento, valortotal, valor, movpagamentoorigemcredito, autorizacaocartao, produtospagos \n");
                        sqlItem.append(" from movpagamento m where recibopagamento = " + item.getMovPagamento().getReciboPagamento().getCodigo() );
                        sqlItem.append(" and autorizacaocartao = '" + item.getMovPagamento().getAutorizacaoCartao() + "' and movpagamentoorigemcredito is null;");
                        PreparedStatement stmItem = con.prepareStatement(sqlItem.toString());
                        ResultSet rsItem = stmItem.executeQuery();

                        while (rsItem.next()) {
                            totalListaReciboAutorizacaoRepetido = totalListaReciboAutorizacaoRepetido + rsItem.getDouble("valortotal");
                        }
                        item.getMovPagamento().setValor(Uteis.arredondarForcando2CasasDecimais(totalListaReciboAutorizacaoRepetido));

                        if (
                                Calendario.igual(item.getMovPagamento().getDataPagamento(), item.getDataPrevistaPagamento())
                                && Uteis.valoresIguaisComTolerancia(item.getValorBruto(), item.getMovPagamento().getValor(), 0.9, item.getComposicao())
                        ) {
                            item.setSituacao(SituacaoItemExtratoEnum.OK);
                        }
                    }
                }
            }
            listaSemDuplicados.add(item);
        }
        return listaSemDuplicados;
    }

    private boolean validarFiltroPorCartao(Integer adquirente, Integer operadoraCartao, String autorizacao, String ro) {
        if(!UteisValidacao.emptyNumber(adquirente) || !UteisValidacao.emptyNumber(operadoraCartao) || !UteisValidacao.emptyString(autorizacao) || !UteisValidacao.emptyString(ro)){
            return true;
        }
        return false;
    }

    public static List<ExtratoDiarioItemVO> montarDadosConciliacao(ResultSet rs, Boolean mp, Boolean filtrandoPorCartao, Connection con,
                                                                   Boolean EContaCorrente, Integer tipoConciliacao, Boolean ENaoMovimentados,
                                                                   TipoDadoConciliacaoEnum tipoDadoConciliacaoEnum) throws Exception {
        List<Integer> pagamentosProcessados = new ArrayList<>();
        List<Integer> parcJaAddFiltroCartao = new ArrayList<>();
        List<ExtratoDiarioItemVO> lista = new ArrayList<ExtratoDiarioItemVO>();
        int posicao = 0;
        boolean negativoAdicionado = false;
        List<String> listaNSUs = new ArrayList<>();
        while (rs.next()) {
            boolean montarCartao = true;
            if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(rs.getInt("tipoconciliacao"))) {
                if (pagamentosProcessados.contains(rs.getInt("codigomovpagamento"))) {
                    if (posicao > 0 && montarCartao) {
                        montarCartao(rs, lista.get(posicao - 1), con);
                    }
                    continue;
                }
            } else if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(rs.getInt("tipoconciliacao"))) {
                if (!rs.getBoolean("estorno") && !(rs.getDouble("valorbruto") < 0.0) && pagamentosProcessados.contains(rs.getInt("codigomovpagamento"))
                        && (parcJaAddFiltroCartao.contains(rs.getInt("nrParcela")) && filtrandoPorCartao)) {
                    continue;
                }
                if ((rs.getDouble("valorbruto") < 0.0) && filtrandoPorCartao && pagamentosProcessados.contains(rs.getInt("codigomovpagamento")) && negativoAdicionado) {
                    continue;
                }
                if (rs.getDouble("valorbruto") < 0.0) {
                    negativoAdicionado = true;
                }
            }

            //Verifica pagamentos de conta corrente que tiveram a data de compensação alterada e precisa retirar da lista, pois o rs vem com a data original
            if (ENaoMovimentados != true) {
                if (Uteis.resultSetContemColuna(rs, "compensacaocc") && rs.getDate("compensacaocc") != null) {
                    if (rs.getString("produtospagos").contains("CC")){
                        ResultSet rsCodigoMovOrigemCredito = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM movpagamento WHERE movpagamentoorigemcredito = " + rs.getInt("codigomovpagamento"), con);
                        boolean naoAcrescentarLista = true;
                        while (rsCodigoMovOrigemCredito.next()) {
                            StringBuilder sqlVerificacao = new StringBuilder();
                            sqlVerificacao.append("SELECT datacompesancao, dataoriginal FROM cartaocredito WHERE movpagamento = " + rsCodigoMovOrigemCredito.getInt("codigo"));
                            sqlVerificacao.append(" AND datacompesancao BETWEEN '" + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(rs.getDate("compensacaocc"))) + "' AND '" + Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(rs.getDate("compensacaocc"))) + "'");
                            ResultSet rsExisteMovDataAlterada = SuperFacadeJDBC.criarConsulta(sqlVerificacao.toString(), con);
                            if (rsExisteMovDataAlterada.next()){
                                Date data1 = rsExisteMovDataAlterada.getDate("dataoriginal");
                                Date data2 = rsExisteMovDataAlterada.getDate("datacompesancao");
                                if (data1 != null && data1 != data2) {
                                    naoAcrescentarLista = true;
                                } else {
                                    naoAcrescentarLista = false;
                                }
                            }
                        }
                        if (naoAcrescentarLista){
                            continue;
                        }
                    }
                }
            }

            ExtratoDiarioItemVO item = new ExtratoDiarioItemVO();
            if (EContaCorrente) {
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT datalancamento as lancei, autorizacao as autorizacaoei, codigocartaocredito as codigocartaocreditoextrato, nrparcela as nrParcelaei, ");
                sql.append("codigomovpagamento as codigoMovPagamentoExtrato, * \n");
                sql.append("FROM extratodiarioitem WHERE autorizacao = '" + rs.getString("autorizacaomp") + "' \n");
                if(tipoConciliacao == 4){
                    sql.append("AND dataprevistapagamento = '" + (rs.getDate("compensacaocc") != null ? rs.getDate("compensacaocc") : rs.getDate("datapagamentomp")) + "' \n");
                } else {
                    sql.append("AND datalancamento = '" + (rs.getDate("lancei") != null ? rs.getDate("lancei") : rs.getDate("lancmp")) + "' \n");
                }
                sql.append("AND tipoconciliacao = " + tipoConciliacao + "; \n");
                ResultSet rsMovPag = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                if (rsMovPag.next()) {
                    montarDadosExtratoContaCorente(rsMovPag, item, tipoConciliacao);
                    try {
                        item.setNrParcela(rsMovPag.getInt("nrParcelaei"));
                        parcJaAddFiltroCartao.add(rsMovPag.getInt("nrParcelaei"));
                    } catch (Exception ex) {
                        item.setNrParcela(rs.getInt("nrParcela"));
                        parcJaAddFiltroCartao.add(rs.getInt("nrParcela"));
                    }
                    if (rsMovPag.getInt("situacao") == SituacaoItemExtratoEnum.ESTORNADO_SISTEMA.ordinal()) {
                        item.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_SISTEMA);
                    }
                } else {
                    item.setTipoConciliacao(rs.getInt("tipoconciliacao"));
                }
            } else {
                montarDadosExtratoContaCorente(rs, item, tipoConciliacao);
                try {
                    item.setNrParcela(rs.getInt("nrParcelaei"));
                    parcJaAddFiltroCartao.add(rs.getInt("nrParcelaei"));
                } catch (Exception ex) {
                    item.setNrParcela(rs.getInt("nrParcela"));
                    parcJaAddFiltroCartao.add(rs.getInt("nrParcela"));
                }
                if (rs.getInt("situacao") == SituacaoItemExtratoEnum.ESTORNADO_SISTEMA.ordinal()) {
                    item.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_SISTEMA);
                }
            }
            item.setCodigoMovPagamento(rs.getInt("codigomovpagamento"));

            try {
                item.setNomeAluno(rs.getString("nomeAluno"));
                item.setMatricula(rs.getString("matricula"));
            } catch (Exception ignored) {
            }

            if (Uteis.resultSetContemColuna(rs, "conveniocobranca") && !UteisValidacao.emptyNumber(rs.getInt("conveniocobranca"))) {
                item.setCodConvenio(rs.getInt("conveniocobranca"));
            }

            try {
                if (tipoDadoConciliacaoEnum.equals(TipoDadoConciliacaoEnum.NaoConciliado)) {
                    item.setNomePagador(rs.getString("nomeAluno"));
                }
            } catch (Exception ignored) {
            }

            try {
                item.setIdExterno(rs.getString("idExterno"));
            } catch (Exception ignored) {
            }

            try {
                item.setIdExterno2(rs.getString("idExterno2"));
            } catch (Exception ignored) {
            }

            try {
                item.setTipoFormaPagamento(rs.getString("tipoFormaPagamento"));
            } catch (Exception ignored) {
                item.setTipoFormaPagamento("");
            }
//            item.setAjuste(rs.getString("ajuste"));
            if (item.getSituacao() == SituacaoItemExtratoEnum.ESTORNADO_SISTEMA) {
            } else if (mp) {
                item.setContaMovimento(rs.getString("contamovimento"));
                item.setNomePagador(rs.getString("nomepagador"));
                FormaPagamentoVO fp = new FormaPagamentoVO();
                fp.setDescricao(rs.getString("formapgto"));
                fp.setCodigo(rs.getInt("codigoformapagamento"));
                fp.setTipoFormaPagamento(rs.getString("tipoformapagamento"));
                item.setFormaPagamentoVO(fp);
                if (item.getMovPagamento() == null)
                    item.setMovPagamento(new MovPagamentoVO());
                if (Uteis.resultSetContemColuna(rs, "adquirente")) {
                    item.getMovPagamento().getAdquirenteVO().setCodigo(rs.getInt("adquirente"));
                    item.getMovPagamento().getAdquirenteVO().setNome(rs.getString("nomeAdquirente"));
                }
                item.getMovPagamento().setCodigo(rs.getInt("codigomovpagamento"));
                item.getMovPagamento().getPessoa().setCodigo(rs.getInt("codigopessoa"));
                if (!UteisValidacao.emptyNumber(rs.getInt("movpagamentoorigemcredito"))) {
                    item.getMovPagamento().setMovPagamentoOrigemCredito(rs.getInt("movpagamentoorigemcredito"));
                }
                item.getMovPagamento().setProdutosPagos(rs.getString("produtospagos"));
                item.getMovPagamento().getReciboPagamento().setCodigo(rs.getInt("recibopagamento"));
                if (UteisValidacao.emptyNumber(rs.getInt("nrparcelacartaocredito"))) {
                    item.getMovPagamento().setNrParcelaCartaoCredito(1);
                } else {
                    item.getMovPagamento().setNrParcelaCartaoCredito(rs.getInt("nrparcelacartaocredito"));
                }
                try {
                    item.getMovPagamento().setMovconta(rs.getInt("movconta"));
                } catch (Exception e) {
                }
                item.getMovPagamento().setFormaPagamento(fp);
                item.getMovPagamento().setAutorizacaoCartao(rs.getString("autorizacaomp"));
                Double valor = 0.0;
                String aut = item.getAutorizacao();
                if (UteisValidacao.emptyString(aut) && item.getCartao() != null) {
                    aut = item.getCartao().getAutorizacao();
                }
                if (UteisValidacao.emptyString(aut) && item.getMovPagamento() != null) {
                    aut = item.getMovPagamento().getAutorizacaoCartao();
                }

                boolean redeDebito = item.getTipoArquivo().equalsIgnoreCase(TipoArquivoRedeEnum.EEVD.getId());

                StringBuilder sqlDebito = new StringBuilder();
                // O primeiro fluxo é o padrão. O else é o ajuste para exibir o valor da Conta Corrente correto.
                if (
                        !tipoDadoConciliacaoEnum.equals(TipoDadoConciliacaoEnum.CCDataAlterada)
                        || (tipoDadoConciliacaoEnum.equals(TipoDadoConciliacaoEnum.CCDataAlterada) && UteisValidacao.emptyNumber(item.getMovPagamento().getMovPagamentoOrigemCredito()))
                ) {
                    sqlDebito.append("SELECT mp.codigo as codigomovpagamento,  mp.valor as valormp, mp.nsu \n");
                    sqlDebito.append(" FROM movpagamento mp \n");
                    sqlDebito.append(" WHERE mp.valor > 0 \n");
                    if(rs.getInt("codigopessoa") > 0){
                        sqlDebito.append(" and pessoa = ").append(rs.getInt("codigopessoa")).append(" \n");
                    }
                    sqlDebito.append(" and formapagamento = ").append(rs.getInt("codigoformapagamento")).append(" \n");
                    if (redeDebito) {
                        try {
                            sqlDebito.append(" AND (nsu ilike '").append(Integer.parseInt(item.getNsu())).append("'");
                            sqlDebito.append(" OR nsu ilike '").append(item.getNsu()).append("')");
                        } catch (Exception e) {
                            sqlDebito.append(" AND nsu ilike '").append(aut).append("'");
                        }
                    } else {
                        if (UteisValidacao.somenteNumeros(aut) && !aut.trim().isEmpty()) {
                            String ro = UteisValidacao.emptyString(item.getRo()) ? "" : item.getRo();
                            sqlDebito.append(" AND "+(ro.isEmpty() ? "" : "(")+"(autorizacaocartao ilike '").append(aut).append("' or TRIM(LEADING '0' FROM CAST (autorizacaocartao AS TEXT)) ilike '").append(aut).append("') ");
                            if(!ro.isEmpty()) {
                                BigInteger bigRo = new BigInteger(ro);
                                String roNormalizado = bigRo.toString();

                                sqlDebito.append(" OR (autorizacaocartao ilike '").append(ro).append("' or TRIM(LEADING '0' FROM CAST (autorizacaocartao AS TEXT)) ilike '").append(roNormalizado).append("')) ");
                            }
                        } else {
                            sqlDebito.append(" AND (autorizacaocartao ilike '")
                                    .append(aut)
                                    .append("' or TRIM(LEADING '0' FROM CAST (autorizacaocartao AS TEXT)) ilike '")
                                    .append(aut)
                                    .append("') ");
                        }
                        if (Objects.nonNull(item.getMovPagamento())) {
                            sqlDebito.append(" AND ( codigo = " + item.getMovPagamento().getCodigo() + " OR movpagamentoorigemcredito = " + item.getMovPagamento().getCodigo() + ")");
                        }
                    }
                } else {
                    sqlDebito.append("SELECT mp.codigo as codigomovpagamento,  mp.valortotal as valormp, mp.nsu \n");
                    sqlDebito.append("FROM movpagamento mp \n");
                    sqlDebito.append("WHERE mp.codigo = ").append(item.getMovPagamento().getMovPagamentoOrigemCredito()).append(";");
                }

                ResultSet rsDebito = criarConsulta(sqlDebito.toString(), con);
                while (rsDebito.next()) {
                    pagamentosProcessados.add(rsDebito.getInt("codigomovpagamento"));
                    valor = Uteis.arredondarForcando2CasasDecimais(valor + rsDebito.getDouble("valormp"));
                    //as vezes movpagamento e outros registros no sistema gravam com valor double arredondando pra cima
                    //Para estes casos aqui verifico se a diferença for de apenas 1 centavo, então ele deverá considerar o valor da operadora mesmo, pra ficar correto lá na tela de conciliação
                    if (Uteis.arredondarForcando2CasasDecimais(valor - Uteis.arredondarForcando2CasasDecimais(item.getValorBruto())) == 0.01) {
                        valor = item.getValorBruto();
                    }
                    item.getMovPagamento().setNsu(rsDebito.getString("nsu"));
                }

                item.getMovPagamento().setValor(valor);
                item.getMovPagamento().setDataPagamento(rs.getTimestamp("datapagamentomp"));
                item.getMovPagamento().setDataLancamento(rs.getTimestamp("lancmp"));
                if (UteisValidacao.emptyString(item.getMovPagamento().getNsu())) {
                    item.getMovPagamento().setNsu(rs.getString("nsu"));
                }

                if(UteisValidacao.emptyNumber(item.getCodigoMovPagamentoExtratoDiario())){
                    item.setSituacao(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE);
                }else if(item.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())){
                    if(Calendario.igual(item.getMovPagamento().getDataLancamento(), item.getDataLancamento()) &&
                            Uteis.valoresIguaisComTolerancia(item.getValorBruto(), item.getMovPagamento().getValor(), 0.9, item.getComposicao())){
                        item.setSituacao(SituacaoItemExtratoEnum.OK);
                    }else{
                        item.setSituacao(SituacaoItemExtratoEnum.PENDENCIAS);
                    }
                }else if(item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())){
                    if (item.getNrTotalParcelas() == null || item.getNrTotalParcelas() == 0) {
                        Integer existeDadosTabelaCartaoCredito = existeDadosCartaoCredito(item.getMovPagamento().getNsu(), con);
                        if (!UteisValidacao.emptyNumber(existeDadosTabelaCartaoCredito)) {
                            item.setCredito(true);
                            item.setNrTotalParcelas(existeDadosTabelaCartaoCredito);
                        }
                    }
                    CartaoCreditoVO cartaoCredito = item.getCredito() ? buscarCartaoCredito(item.getCodigoCartaoCredito(), con) : null;
                    int temComposicao = 0;
                    if (cartaoCredito != null) {
                        if (cartaoCredito.getComposicao().contains(",")) {
                            temComposicao = Integer.parseInt(cartaoCredito.getComposicao().split(",")[0]);
                        } else {
                            temComposicao = Integer.parseInt(cartaoCredito.getComposicao());
                        }
                    }

                    if(cartaoCredito != null && Calendario.igual(cartaoCredito.getDataCompensacao(), item.getDataPrevistaPagamento()) &&
                            Uteis.valoresIguaisComTolerancia(item.getValorBruto(), cartaoCredito.getValor(), 0.9, temComposicao)){
                        item.setSituacao(SituacaoItemExtratoEnum.OK);
                    }else if(cartaoCredito == null && Calendario.igual(item.getMovPagamento().getDataPagamento(), item.getDataPrevistaPagamento()) &&
                        Uteis.valoresIguaisComTolerancia(item.getValorBruto(), item.getMovPagamento().getValor(), 0.9, item.getComposicao())){
                        item.setSituacao(SituacaoItemExtratoEnum.OK);
                    }else{
                        item.setSituacao(SituacaoItemExtratoEnum.PENDENCIAS);
                    }
                }

                if (!UteisValidacao.emptyNumber(rs.getInt("codigocartaocredito")) && montarCartao) {
                    montarCartao(rs, item, con);
                }
            } else {

                if(item.getMovPagamento() == null) {
                    item.setMovPagamento(new MovPagamentoVO());
                }

                boolean redeDebito = item.getTipoArquivo().equalsIgnoreCase(TipoArquivoRedeEnum.EEVD.getId());
                if (redeDebito) {
                    item.setAutorizacao("NSU " + item.getNsu());
                }

                StringBuilder sql = new StringBuilder();
                sql.append("select fp.descricao as forma, mcr.formapagamento, p.nome, cc.codigo as codigocartaocredito, cc.datacompesancao as compensacaocc, \n");
                sql.append(" m.datalancamento as lancmp, autorizacaocartao as autorizacaomp, cc.valor as valorcc, cc.nrparcela as nrparcelacartao, 1 as nrparcelacartaocredito, ei.tipoconciliacao from movconta m \n");
                sql.append(" inner join pessoa p on m.pessoa = p.codigo \n");
                sql.append(" inner join movcontarateio mcr ON mcr.movconta = m.codigo \n");
                sql.append(" inner join formapagamento fp ON fp.codigo = mcr.formapagamento \n");
                sql.append(" inner join cartaocredito cc on cc.movconta =  m.codigo \n");
                sql.append(" LEFT OUTER JOIN extratodiarioitem ei ON ei.codigocartaocredito  = cc.codigo \n");

                if (UteisValidacao.somenteNumeros(item.getAutorizacaoInt())  && !item.getAutorizacaoInt().trim().isEmpty()) {
                    sql.append(" where (autorizacaocartao ilike '").append(item.getAutorizacao()).append("' or TRIM(LEADING '0' FROM CAST (autorizacaocartao AS TEXT)) ilike '").append(item.getAutorizacaoInt()).append("') \n");
                } else {
                    String autorizacaoNSU = UteisValidacao.emptyString(item.getAutorizacao()) ?
                            item.getNsu() : item.getAutorizacao();
                    sql.append(" where (autorizacaocartao ilike '").append(autorizacaoNSU);
                    sql.append("' or TRIM(LEADING '0' FROM CAST (autorizacaocartao AS TEXT)) ilike '").append(autorizacaoNSU).append("') \n");
                }

                sql.append(" and tipooperacao = ").append(TipoOperacaoLancamento.RECEBIVEL_AVULSO.getCodigo());

                ResultSet rsAvulso = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                if (rsAvulso.next()) {
                    item.setNomePagador(rsAvulso.getString("nome"));
                    item.setFormaPagamentoVO(new FormaPagamentoVO());
                    item.getFormaPagamentoVO().setDescricao(rsAvulso.getString("forma"));
                    item.getFormaPagamentoVO().setCodigo(rsAvulso.getInt("formapagamento"));
                    montarCartao(rsAvulso, item, con);
                } else {
//                    if (UteisValidacao.emptyString(item.getAjuste())) {
                    int codigomovconta = rs.getInt("codigomovconta");
                    if (!UteisValidacao.emptyNumber(codigomovconta)) {
                        item.setSituacao(SituacaoItemExtratoEnum.getFromOrdinal(rs.getInt("situacao")));
                    } else {
                        if (item.getValorBruto() < 0.0) {
                            item.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_OPERADORA);
                        } else {
                            item.setSituacao(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE);
                        }
                    }
//                    } else {
//                        item.setSituacao(SituacaoItemExtratoEnum.AJUSTADO);
//                    }
                    try {
                        item.setNomePagador(rs.getString("nome"));
                    } catch (Exception e) {
                    }
                }
            }
            if (UteisValidacao.emptyNumber(item.getCodigo())) {
                item.setSituacao(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE);
            }
            if (item.isEstorno()) {
                item.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_OPERADORA);
            }
            try {
                item.setAntecipacao(rs.getBoolean("antecipacao"));
                item.setDataPgtoOriginalAntesDaAntecipacao(rs.getTimestamp("dataPgtoOriginalAntesDaAntecipacao"));
                item.setValorDescontadoAntecipacao(rs.getDouble("valorDescontadoAntecipacao"));
                item.setTaxaCalculadaAntecipacao(rs.getDouble("taxaCalculadaAntecipacao"));
            } catch (Exception ignored) {
                item.setAntecipacao(false);
                item.setDataPgtoOriginalAntesDaAntecipacao(null);
                item.setValorDescontadoAntecipacao(0.0);
                item.setTaxaCalculadaAntecipacao(0.0);
            }
            try {
                item.setAlterouDataRecebimentoZWAutomaticamente(rs.getBoolean("alterouDataRecebimentoZWAutomaticamente"));
                item.setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(rs.getTimestamp("dataPgtoOriginalZWAntesDaAlteracaoAutomatica"));
            } catch (Exception ex) {
                item.setAlterouDataRecebimentoZWAutomaticamente(false);
                item.setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(null);

            }
            try {
                if (!UteisValidacao.emptyNumber(rs.getInt("pessoa"))) {
                    montarDadosPessoa(item, rs.getInt("pessoa"), Uteis.NIVELMONTARDADOS_CONSULTA_WS , con);
                }
            } catch (Exception ignore) {}
            CartaoCredito cartaoCreditoDAO = new CartaoCredito(con);
            try{
                CartaoCreditoVO cartao = cartaoCreditoDAO.consultarPorChavePrimaria(rs.getInt("composicao"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                item.setComposicao(rs.getInt("composicao"));
                item.setValorComposicao(cartao.getValor());
            }catch (Exception ignore){}finally {
                cartaoCreditoDAO = null;
            }
            //Esse if é para tirar valores Tipo Conciliação = 3 para Stone, que grava no banco de dados por parcela e estava exibindo por parcela na Venda e deve exibir apenas 1 vez.
            if(item.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())){
                if(!listaNSUs.contains(item.getNsu())){
                    lista.add(item);
                    posicao++;
                }
            } else {
                lista.add(item);
                posicao++;
            }

            //Incrementar lsita de NSUs para itens de venda
            if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(rs.getInt("tipoconciliacao")) && !UteisValidacao.emptyString(item.getNsu())) {
                listaNSUs.add(item.getNsu());
            }
        }
        return lista;
    }

    public static void montarDadosPessoa(ExtratoDiarioItemVO extratoDiarioItemVO, int codPessoa, int nivelMontarDados, Connection con) throws Exception {
        Pessoa pessoa = new Pessoa(con);
        extratoDiarioItemVO.setPessoa(pessoa.consultarPorChavePrimaria(codPessoa, nivelMontarDados));
        pessoa = null;
    }

    public static CartaoCreditoVO buscarCartaoCredito(int codigoCartao, Connection con){
        try{
            if(!UteisValidacao.emptyNumber(codigoCartao)){
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select datacompesancao, valor, valortotal, composicao from cartaocredito where codigo = "+codigoCartao, con);
                while (rs.next()){
                    CartaoCreditoVO cartao = new CartaoCreditoVO();
                    cartao.setDataCompensacao(rs.getTimestamp("datacompesancao"));
                    cartao.setValor(rs.getDouble("valor"));
                    cartao.setValorTotal(rs.getDouble("valortotal"));
                    cartao.setComposicao(UteisValidacao.emptyString(rs.getString("composicao")) ? "0" : rs.getString("composicao"));
                    return cartao;
                }
            }
        }catch (Exception e){
            return null;
        }
        return null;
    }

    private static Integer existeDadosCartaoCredito(String nsuMovPagamento, Connection con){
        try {
            if (!UteisValidacao.emptyString(nsuMovPagamento)) {
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT  \n");
                sql.append("COUNT(c.*) as qtd \n");
                sql.append("FROM cartaocredito c \n");
                sql.append("INNER JOIN movpagamento mp on mp.codigo = c.movpagamento \n");
                sql.append("where mp.nsu = '").append(nsuMovPagamento).append("' \n");
                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                if (rs.next()) {
                    return rs.getInt("qtd");
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void montarCartao(ResultSet rs, final ExtratoDiarioItemVO item, Connection con) throws Exception {
        CartaoCreditoTO cc = new CartaoCreditoTO();
        cc.setDataCompensacao(rs.getDate("compensacaocc"));
        cc.setDataLancamento(rs.getDate("lancmp"));
        cc.setAutorizacao(rs.getString("autorizacaomp"));
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(rs.getInt("tipoconciliacao")) && !UteisValidacao.emptyNumber(item.getNrTotalParcelas())) {
                cc.setValor(rs.getDouble("valorcc") * item.getNrTotalParcelas());
        } else {
            obterValorCartaoCredito(rs, con, cc);
        }

        cc.setCodigo(rs.getInt("codigocartaocredito"));
        cc.setNrVezes(rs.getInt("nrparcelacartaocredito"));
        cc.setNrParcela(rs.getInt("nrparcelacartao"));
        Lote lote = new Lote(con);
        LoteVO loteVO = lote.consultarPorCartao(cc.getCodigo(), negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (loteVO != null && !UteisValidacao.emptyNumber(loteVO.getCodigo())) {
            cc.setNumeroLote(loteVO.getCodigo());
            getFacade().getCartaoCredito().getContaLoteCartao(cc, cc.getNumeroLote());
        }
        try {
            cc.setAdquirenteCod(rs.getInt("adquirente"));
            cc.setAdquirente(rs.getString("nomeadquirente"));
            cc.setOperadoraCodigo(rs.getInt("operadoracartao"));
        }catch (Exception e){
        }

        try {
            cc.setFormaPagamentoVO(new FormaPagamentoVO());
            cc.getFormaPagamentoVO().setCodigo(rs.getInt("codigoformapagamento"));
            cc.getFormaPagamentoVO().setDescricao(rs.getString("formapgto"));
        }catch (Exception e){

        }
        try{
            item.setComposicao(Integer.parseInt(!UteisValidacao.emptyString(rs.getString("composicao")) ? rs.getString("composicao") : "0"));
        }catch(Exception ignore){}

        item.setCartao(cc);
        item.getCartoes().add(cc);
        item.setContaMovimento(cc.getContaContido());
        if (!TipoConciliacaoEnum.VENDAS.getCodigo().equals(item.getTipoConciliacao())) {
            item.setSituacao((Calendario.igual(cc.getDataCompensacao(), item.getDataPrevistaPagamento())
                    && Uteis.valoresIguaisComTolerancia(item.getValorBruto(), cc.getValor(), 0.9, item.getComposicao())
                    ? SituacaoItemExtratoEnum.OK : SituacaoItemExtratoEnum.PENDENCIAS));
        }
    }

    public double obterValorLiquidoParcelamentoVendaAgrupadosItem(Integer movPagamento, Date dataLancamentoItens) throws SQLException {
        String sql = "SELECT SUM(valorliquido) as valorliquido FROM extratodiarioitem " +
                "WHERE codigomovpagamento = ? " +
                "AND datalancamento BETWEEN ? AND ?";

        try (PreparedStatement stmt = con.prepareStatement(sql)) {
            stmt.setInt(1, movPagamento);
            stmt.setTimestamp(2, Timestamp.valueOf(Uteis.getDataJDBC(dataLancamentoItens) + " 00:00:00"));
            stmt.setTimestamp(3, Timestamp.valueOf(Uteis.getDataJDBC(dataLancamentoItens) + " 23:59:59"));

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getDouble("valorliquido");
            }
        }
        return 0.0;
    }

    public double obterValorDescontadoTaxasParcelamentoVendaAgrupadosItem(Integer movPagamento, Date dataLancamentoItens) throws SQLException {
        String sql = "SELECT SUM(valorcomissao) as valorcomissao FROM extratodiarioitem " +
                "WHERE codigomovpagamento = ? " +
                "AND datalancamento BETWEEN ? AND ?";

        try (PreparedStatement stmt = con.prepareStatement(sql)) {
            stmt.setInt(1, movPagamento);
            stmt.setTimestamp(2, Timestamp.valueOf(Uteis.getDataJDBC(dataLancamentoItens) + " 00:00:00"));
            stmt.setTimestamp(3, Timestamp.valueOf(Uteis.getDataJDBC(dataLancamentoItens) + " 23:59:59"));

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getDouble("valorcomissao");
            }
        }
        return 0.0;
    }

    private static void obterValorCartaoCredito(ResultSet rs, Connection con, CartaoCreditoTO cc) throws SQLException {
        //O if valida apenas para Stone por ser o cenário encontrado. Se acontecer o mesmo para outros Tipo Convênio, podemos tirar o if só para Stone.
        if (Uteis.resultSetContemColuna(rs, "tipoconveniocobranca") && !UteisValidacao.emptyNumber(rs.getInt("tipoconveniocobranca"))
                && rs.getInt("tipoconveniocobranca") == TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo()
                && Uteis.resultSetContemColuna(rs, "composicao") && !UteisValidacao.emptyString(rs.getString("composicao"))
                && Uteis.resultSetContemColuna(rs, "codigocartaocredito") && !UteisValidacao.emptyNumber(rs.getInt("codigocartaocredito"))) {
            try {
                int codigoCartaoCredito = rs.getInt("codigocartaocredito");
                ResultSet rsCC = SuperFacadeJDBC.criarConsulta("SELECT valortotal FROM cartaocredito WHERE codigo = " + codigoCartaoCredito, con);
                while (rsCC.next()) {
                    cc.setValor(rsCC.getDouble("valortotal"));
                }
            } catch (Exception e) {
                cc.setValor(rs.getDouble("valorcc"));
            }
        } else {
            cc.setValor(rs.getDouble("valorcc"));
        }
    }

    public static List<ExtratoDiarioItemVO> montarDadosConsulta(ResultSet rs, Connection con) throws Exception {
        List<ExtratoDiarioItemVO> lista = new ArrayList<ExtratoDiarioItemVO>();
        while (rs.next()) {
            lista.add(montarDados(rs, con));
        }
        return lista;
    }

    public static ExtratoDiarioItemVO montarDados(ResultSet rs, Connection con) throws Exception {
        ExtratoDiarioItemVO item = new ExtratoDiarioItemVO();
        item.setCodigo(rs.getInt("codigo"));
        item.setProps(negocio.comuns.utilitarias.Uteis.obterMapFromString(rs.getString("props")));
        item.setDataLancamento(rs.getDate("datalancamento"));
        item.setDataPrevistaPagamento(rs.getDate("dataprevistapagamento"));
        item.setAutorizacao(rs.getString("autorizacao"));
        item.setEstabelecimento(rs.getString("estabelecimento"));
        item.setNrCartao(rs.getString("nrcartao"));
        item.setValorBruto(rs.getDouble("valorbruto"));
        item.setValorLiquido(rs.getDouble("valorliquido"));
        item.setValorComissao(rs.getDouble("valorcomissao"));
        item.setCodigoMovPagamento(rs.getInt("codigomovpagamento"));
        item.setCodigoMovConta(rs.getInt("codigomovconta"));
        item.setCodigoCartaoCredito(rs.getInt("codigocartaocredito"));
        item.setSituacao(SituacaoItemExtratoEnum.getFromOrdinal(rs.getInt("situacao")));
        item.setNrParcela(rs.getInt("nrParcela"));
        item.setTipoRegistro(rs.getString("tiporegistro"));
        item.setRo(rs.getString("ro"));
        item.setTaxa(rs.getDouble("taxa"));
        item.setArquivo(rs.getString("arquivo"));
        item.setApresentarExtrato(rs.getBoolean("apresentarExtrato"));
        item.setNsu(rs.getString("nsu"));
        item.setEstorno(rs.getBoolean("estorno"));
        try {
            item.setIdExterno(rs.getString("idExterno"));
        } catch (Exception ex) {}
        try {
            item.setIdExterno2(rs.getString("idExterno2"));
        } catch (Exception ex) {}
        try {
            item.setTipoConciliacao(rs.getInt("tipoconciliacao"));
        } catch (Exception e) {
        }
        try {
            item.setTipoArquivo(rs.getString("tipoArquivo"));
        } catch (Exception e) {
        }
        try {
            item.getEmpresa().setCodigo(rs.getInt("empresa"));
            item.getConvenio().setCodigo(rs.getInt("conveniocobranca"));
        } catch (Exception e) {
        }
        item.setDataProcessamentoExtrato(rs.getTimestamp("dataprocessamentoextrato"));
        item.setDataReprocessamento(rs.getTimestamp("datareprocessamento"));
        item.setNrTotalParcelas(rs.getInt("nrtotalparcelas"));
        item.setCredito(rs.getBoolean("credito"));
        item.setIdentificadorProduto(rs.getString("identificadorProduto"));
        try {
            item.setNomePagador(UteisValidacao.emptyString(rs.getString("nomepagador")) ? rs.getString("nome") : rs.getString("nomepagador"));
        } catch (Exception e) {
        }
        try {
            item.setAntecipacao(rs.getBoolean("antecipacao"));
        } catch (Exception ignore) {}
        try {
            item.setDataPgtoOriginalAntesDaAntecipacao(rs.getDate("dataPgtoOriginalAntesDaAntecipacao"));
        } catch (Exception ignore) {}
        try {
            item.setAlterouDataRecebimentoZWAutomaticamente(rs.getBoolean("alterouDataRecebimentoZWAutomaticamente"));
        } catch (Exception ignore) {}
        try {
            item.setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(rs.getDate("dataPgtoOriginalZWAntesDaAlteracaoAutomatica"));
        } catch (Exception ignore) {}
        try {
            item.setValorDescontadoAntecipacao(rs.getDouble("valorDescontadoAntecipacao"));
        } catch (Exception ignore) {}
        try {
            item.setTaxaCalculadaAntecipacao(rs.getDouble("taxaCalculadaAntecipacao"));
        } catch (Exception ignore) {}
        try {
            item.setObservacao(rs.getString("observacao"));
        } catch (Exception ignore) {}

        try {
            if (!UteisValidacao.emptyNumber(rs.getInt("pessoa"))) {
                montarDadosPessoa(item, rs.getInt("pessoa"), Uteis.NIVELMONTARDADOS_CONSULTA_WS, con);
            }
        } catch (Exception ignore) {}

        try {
            if(!UteisValidacao.emptyNumber(rs.getInt("tipoParcelamento"))) {
                item.setTipoParcelamento(rs.getInt("tipoParcelamento"));
            }
        } catch (Exception ignore) {}

        return item;
    }

    public boolean arquivoProcessado(String arquivo) throws Exception {
        return SuperFacadeJDBC.existe("SELECT codigo from extratodiarioitem where arquivo like '"
                + arquivo + "'", con);
    }

    @Override
    public void processarListaExtratoDiario(List<ExtratoDiarioItemVO> listaExtratoDiarioItem, boolean reprocessando) throws Exception {
        processarListaExtratoDiario(listaExtratoDiarioItem, reprocessando, null);
    }

    @Override
    public void processarListaExtratoDiario(List<ExtratoDiarioItemVO> listaExtratoDiarioItem, boolean reprocessando, ConvenioCobrancaVO convenioCobranca) throws Exception {
        Map<String, Date> mapaData = new HashMap<String, Date>();
        Map<String, Double> mapaTaxa = new HashMap<String, Double>();
        Map<String, String> mapaBandeira = new HashMap<String, String>();
        Map<String, String> mapaOrigem = new HashMap<String, String>();
        Map<String, String> mapaROAutorizacao = new HashMap<String, String>();

        //Cielo layout 14
        if(!reprocessando){
            for (ExtratoDiarioItemVO item : listaExtratoDiarioItem) {

                if (item.getTipoRegistro().equals("1")) {
                    mapaData.put(item.getRo(), item.getDataPrevistaPagamento());
                    mapaTaxa.put(item.getRo(), item.getTaxa());
                    mapaBandeira.put(item.getRo(), item.getIdentificadorProduto());
                    mapaOrigem.put(item.getRo(), item.getOrigemAjuste());
                }

                if (item != null && !UteisValidacao.emptyString(item.getRo()) &&
                        !UteisValidacao.emptyString(item.getAutorizacao())) {
                    mapaROAutorizacao.put(item.getRo(), item.getAutorizacao());
                }
            }
        }

        //Concilhar rede pois em um retorno pode vir valor de mais de uma autorizacao
        //Metodo para Conciliação Rede Arquivos, onde encontra a Conciliação de Venda e troca o ApresentarExtrato para true.
        doCapturaAutorizacaoERede(listaExtratoDiarioItem);

        if (!reprocessando) {
            listaExtratoDiarioItem = processarListaGetNetEOutros(listaExtratoDiarioItem);
        }

        Map<Integer, EmpresaVO> empresaVOMap = new HashMap<>();
        Map<String, ConvenioCobrancaVO> convenioCobrancaVOMap = new HashMap<>();

        try{
            obterConveniosEEmpresasOriginalCieloERede(convenioCobranca, empresaVOMap, convenioCobrancaVOMap);
        } catch (Exception e){}

        ConfiguracaoFinanceiroVO configuracaoFinanceiroVO = getFacade().getConfiguracaoFinanceiro().consultar();
        for (ExtratoDiarioItemVO item : listaExtratoDiarioItem) {

            try {
                //codMovPagamento antes de qualquer alteração do item
                int codMovPgtoAntesDeQualquerAlteracao = item.getCodigoMovPagamento() != null ? item.getCodigoMovPagamento() : 0;

                boolean extratoBIN = ExtratoBinTipoRegistroEnum.extratoBIN(item.getTipoRegistro());
                boolean extratoREDE = item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE);
                boolean extratoREDEOnline = item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) && item.getTipoArquivo().equals(TipoArquivoRedeEnum.API_REDE.getId());
                boolean extratoGetNet = item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE) || item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET);
                boolean stoneOnline = item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) || item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT);
                boolean stoneOnlineV5 = item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5);
                boolean pagoLivre = item.isPagoLivre();

                if(!stoneOnline && !stoneOnlineV5 && !extratoREDE && !pagoLivre) {
                    item.setCodigoMovPagamento(0);//para reprocessamento;
                    item.setCodigoCartaoCredito(0);//para reprocessamento;
                    item.setCodigoMovConta(0);//para reprocessamento;
                }

                if(extratoREDE && item.getTipoRegistro()!= null && !item.getTipoRegistro().equals("014") && !extratoREDEOnline) {
                    item.setCodigoMovPagamento(0);//para reprocessamento;
                    item.setCodigoCartaoCredito(0);//para reprocessamento;
                    item.setCodigoMovConta(0);//para reprocessamento;
                }

                if (extratoREDE && item.getTipoRegistro()!= null && !item.getTipoRegistro().equals("014")) {
                    if (!UteisValidacao.emptyString(item.getRo()) && UteisValidacao.emptyString(item.getAutorizacao())) {
                        String auto = mapaROAutorizacao.get(item.getRo());
                        if (!UteisValidacao.emptyString(auto)) {
                            item.setAutorizacao(auto);
                        }
                    }
                }

//            obter as informações da venda, -- autorizacao etc...
                if (extratoREDE && UteisValidacao.emptyString(item.getTipoRegistro()) && !item.getTipoRegistro().equals("014")) {

                    if (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getTipoArquivoRedeEnum().getId())
                            && item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getId())) {
                        ExtratoDiarioItemVO extratoDiarioVenda = consultarExtratoGenerico(item.getEstabelecimento(), item.getRo(), ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro010.getId(), ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro010.getTipoArquivoRedeEnum().getId(), null, null, null);
                        if (!UteisValidacao.emptyNumber(extratoDiarioVenda.getCodigo())) {
                            item.setDataPrevistaPagamento(extratoDiarioVenda.getDataPrevistaPagamento());
                        }
                    }

                    if (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro014.getTipoArquivoRedeEnum().getId())
                            && item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro014.getId())) {
                        ExtratoDiarioItemVO extratoDiarioVenda = consultarExtratoGenerico(item.getEstabelecimento(), item.getRo(), ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getId(), ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getTipoArquivoRedeEnum().getId(), null, null, null);
                        if (!UteisValidacao.emptyNumber(extratoDiarioVenda.getCodigo())) {
                            item.setNrCartao(extratoDiarioVenda.getNrCartao());
                            item.setAutorizacao(extratoDiarioVenda.getAutorizacao());
                            item.setNrTotalParcelas(extratoDiarioVenda.getNrTotalParcelas());
                        }
                    }

                    if ((item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getTipoArquivoRedeEnum().getId())
                            && item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getId())) ||
                            (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro034.getTipoArquivoRedeEnum().getId())
                                    && item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro034.getId()))) {
                        ExtratoDiarioItemVO extratoDiarioVenda = consultarExtratoGenerico(item.getEstabelecimento(), item.getRo(), ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro006.getId(), ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro006.getTipoArquivoRedeEnum().getId(), null, null, null);
                        if (!UteisValidacao.emptyNumber(extratoDiarioVenda.getCodigo())) {
                            item.setDataPrevistaPagamento(extratoDiarioVenda.getDataPrevistaPagamento());
                        }
                    }

                    if (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getTipoArquivoRedeEnum().getId())
                            && item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getId())) {

                        List<ExtratoDiarioItemVO> extratosPagos = consultarExtratosRedeProcessarPagamento(item.getEstabelecimento(), item.getRo(), ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getId(), ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getTipoArquivoRedeEnum().getId(), null, null, null);
                        for (ExtratoDiarioItemVO ext : extratosPagos) {
                            ext.setDataPrevistaPagamento(item.getDataPrevistaPagamento());
                            alterar(ext);
                        }

                    }
                }

                boolean processarRede = (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro014.getTipoArquivoRedeEnum().getId()) &&
                        item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro014.getId())) ||
                        (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getTipoArquivoRedeEnum().getId()) &&
                                item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getId())) ||
                        (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getTipoArquivoRedeEnum().getId()) &&
                                item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getId()) ||
                                (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro034.getTipoArquivoRedeEnum().getId()) &&
                                        item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro034.getId())) ||
                                (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro006.getTipoArquivoRedeEnum().getId()) &&
                                        item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro006.getId())) ||
                                (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getTipoArquivoRedeEnum().getId()) &&
                                        item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getId())) ||
                                (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro035.getTipoArquivoRedeEnum().getId()) &&
                                        item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro035.getId()))
                        );

                boolean redeDebito = item.getTipoArquivo().equalsIgnoreCase(TipoArquivoRedeEnum.EEVD.getId()) || (extratoREDEOnline && !item.getCredito());

                if (
                        (!UteisValidacao.emptyString(item.getAutorizacao())
                            && (item.getTipoRegistro().equals("2") || item.getTipoRegistro().equals("E") || extratoBIN || extratoGetNet || stoneOnline || stoneOnlineV5 || pagoLivre
                            || (extratoREDE && processarRede && (!item.getTipoRegistro().equals("014") ||
                            (item.getTipoRegistro().equals("014") && item.getSituacao() == null) ||
                            (item.getTipoRegistro().equals("014") && !item.getSituacao().equals(SituacaoItemExtratoEnum.OK)))))
                            && !UteisValidacao.emptyString(item.getAutorizacaoInt())
                        )
                        || (redeDebito && !UteisValidacao.emptyString(item.getNsu())
                        || extratoREDEOnline)
                ) {

                    ConvenioCobrancaVO convenioCobrancaConsultaConciliacao = convenioCobranca;
                    if (
                        item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) ||
                        (item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC) && item.getConvenio().getTipo().getTipoRemessa().equals((TipoRemessaEnum.EDI_CIELO))) ||
                        (item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) && !extratoREDEOnline)
                    ) {
                        try {
                            // Dentro do arquivo Cielo e Rede, vem todos os estabelecimentos e pv misturados, tentar setar o convenio original aqui e não da empresa que está processando
                            convenioCobrancaConsultaConciliacao = convenioCobrancaVOMap.getOrDefault(item.getEstabelecimento(), convenioCobranca);
                            EmpresaVO empresaVO = empresaVOMap.get(convenioCobrancaConsultaConciliacao.getConfiguracoesEmpresa().get(0).getEmpresa().getCodigo());
                            convenioCobrancaConsultaConciliacao.setEmpresa(empresaVO);
                            item.setConvenio(convenioCobrancaConsultaConciliacao);
                            item.setEmpresa(empresaVO);
                        } catch (Exception e){}
                    }

                    ResultSet rsMovPagamento = null;
                    String sqlConciliacaoMovPags = getConsultaConciliacao(convenioCobrancaConsultaConciliacao, item, extratoREDE, redeDebito, null, stoneOnline || stoneOnlineV5);
                    ResultSet rsMovPagamentoTest = criarConsultaRolavel(sqlConciliacaoMovPags, con);

                    while (rsMovPagamentoTest.next()) {
                        String sqlConciliacaoMovPagsOrigemCredito = getConsultaConciliacao(convenioCobranca, item, extratoREDE, redeDebito,
                                rsMovPagamentoTest.getInt("codigomovpagamento"), stoneOnline || stoneOnlineV5);
                        ResultSet rsMovPagamentoOrigemCreditoTest = criarConsultaRolavel(sqlConciliacaoMovPagsOrigemCredito, con);
                        if (rsMovPagamentoOrigemCreditoTest.next()) {
                            rsMovPagamento = rsMovPagamentoOrigemCreditoTest; // Pagamento Anterior Foi cancelado gerando um novo movpagamento origem do credito, conciliação deve ser feita com base nele.
                        } else {
                            rsMovPagamento = rsMovPagamentoTest;
                        }
                        // limit 1, então só passa uma vez.
                    }
                    if (rsMovPagamento != null) {
                        rsMovPagamento.beforeFirst();
                    }

                    if (!extratoREDE && !extratoBIN && !extratoGetNet && !reprocessando && !stoneOnline && !stoneOnlineV5 && !pagoLivre) {
                        //Cielo layout 14
                        if (!item.getTipoRegistro().equals("E")) {
                            item.setDataPrevistaPagamento(mapaData.get(item.getRo()));
                            item.setIdentificadorProduto(mapaBandeira.get(item.getRo()));
                            item.avaliarCreditoOuDebito();
                        }

                        if (item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) {
                            item.setOrigemAjuste(mapaOrigem.get(item.getRo()));
                            if (OrigemAjusteCieloEnum.valueOff(mapaOrigem.get(item.getRo())).isChargeback() && item.getValorBruto() < 0.0) {
                                item.setEstorno(true);
                                item.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_OPERADORA);
                            }
                        }
                    }

                    List<Integer> processados = new ArrayList<Integer>();
                    if (rsMovPagamento != null && !item.getTipoConciliacao().equals(TipoConciliacaoEnum.ESTORNO_CHARGEBACK.getCodigo())) {
                        while (rsMovPagamento.next()) {

                            Date dataLancamentoSistema = Calendario.getDataComHoraZerada(rsMovPagamento.getDate("datalancamento"));
                            Date dataCompensacaoSistema = rsMovPagamento.getDate("datacompesancao") == null
                                    ? Calendario.getDataComHoraZerada(rsMovPagamento.getDate("dataquitacao")) : Calendario.getDataComHoraZerada(rsMovPagamento.getDate("datacompesancao"));
                            Double valor = Uteis.arredondarForcando2CasasDecimais(UteisValidacao.emptyNumber(rsMovPagamento.getDouble("valorcc")) || item.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo()) ? rsMovPagamento.getDouble("valormp") : rsMovPagamento.getDouble("valorcc"));
                            if (item.getTipoConciliacao() != null && (UteisValidacao.emptyNumber(rsMovPagamento.getInt("codigocartaocredito")) || item.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo()))) {
                                valor = 0.0;
                                StringBuilder sqlDebito = new StringBuilder();
                                sqlDebito.append("SELECT mp.autorizacaocartao, mp.codigo as codigomovpagamento,  mp.valor as valormp \n");
                                sqlDebito.append("FROM movpagamento mp \n");
                                sqlDebito.append("WHERE mp.valor > 0 \n");
                                sqlDebito.append("AND mp.pessoa = ").append(rsMovPagamento.getInt("codigopessoa")).append(" \n");
                                sqlDebito.append("AND mp.formapagamento = ").append(rsMovPagamento.getInt("codigoformapagamento")).append(" \n");
                                if (redeDebito) {
                                    try {
                                        sqlDebito.append(" AND (nsu ilike '").append(Integer.parseInt(item.getNsu())).append("'");
                                        sqlDebito.append(" OR nsu ilike '").append(item.getNsu()).append("')");
                                    } catch (Exception e) {
                                        sqlDebito.append(" AND nsu ilike '").append(item.getNsu()).append("'");
                                    }
                                } else {
                                    if (convenioCobranca != null &&
                                            (convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) ||
                                                    convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC)
                                            )) {
                                        sqlDebito.append(" AND (");
                                        if (!UteisValidacao.emptyString(item.getNumeroUnicoTransacao())) {
                                            sqlDebito.append(" numerounicotransacao ILIKE '").append(item.getNumeroUnicoTransacao()).append("' OR ");
                                        }
                                        sqlDebito.append(" mp.autorizacaocartao ILIKE '%").append(item.getAutorizacao()).append("') \n");
                                    } else {
                                        if (extratoREDE) {
                                            if (!UteisValidacao.emptyString(item.getAutorizacaoInt()) && !Uteis.getValidarStringSePossuiLetra(item.getAutorizacaoInt())) {
                                                sqlDebito.append(" and (mp.autorizacaocartao ilike '").append(item.getAutorizacao()).append("' or TRIM(LEADING '0' FROM CAST (mp.autorizacaocartao AS TEXT)) ilike '").append(Integer.valueOf(item.getAutorizacaoInt())).append("') ");
                                            } else {
                                                sqlDebito.append(" and (mp.autorizacaocartao ilike '").append(item.getAutorizacao()).append("' or TRIM(LEADING '0' FROM CAST (mp.autorizacaocartao AS TEXT)) ilike '").append(item.getAutorizacao()).append("') ");
                                            }
                                        } else {
                                            sqlDebito.append(" and (mp.autorizacaocartao ilike '").append(item.getAutorizacao()).append("' or TRIM(LEADING '0' FROM CAST (mp.autorizacaocartao AS TEXT)) ilike '").append(item.getAutorizacao()).append("') ");
                                        }
                                    }
                                }
                                ResultSet rsDebito = criarConsulta(sqlDebito.toString(), con);
                                while (rsDebito.next()) {
                                    processados.add(rsDebito.getInt("codigomovpagamento"));
                                    valor = Uteis.arredondarForcando2CasasDecimais(valor + rsDebito.getDouble("valormp"));
                                    if (redeDebito) {
                                        item.setAutorizacao(rsDebito.getString("autorizacaocartao"));
                                    }
                                }

                            }
                            setarSituacaoItemExtrato(item, dataLancamentoSistema, dataCompensacaoSistema, rsMovPagamento.getInt("codigomovpagamento"), rsMovPagamento.getInt("codigomovconta"),
                                    rsMovPagamento.getInt("codigocartaocredito"), valor, rsMovPagamento.getString("nomepagador"), rsMovPagamento.getInt("codigopessoa"));
                            if (!UteisValidacao.emptyNumber(rsMovPagamento.getInt("codigocartaocredito")) && !item.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())) {
                                processados.add(rsMovPagamento.getInt("codigocartaocredito"));
                                if (!UteisValidacao.emptyString(rsMovPagamento.getString("composicao"))) {
                                    String[] composicao = rsMovPagamento.getString("composicao").split(",");
                                    for (String codCartao : composicao) {
                                        processados.add(Integer.parseInt(codCartao));
                                    }
                                }
                            }
                        }
                    }
                    if (!extratoREDE && !extratoBIN && !reprocessando && !stoneOnline && !stoneOnlineV5 && !pagoLivre && !item.getTipoRegistro().equals("E")) {
                        item.setTaxa(mapaTaxa.get(item.getRo()) == null ? 0.0 : mapaTaxa.get(item.getRo()));
                        item.setValorComissao((item.getValorBruto() * item.getTaxa()) / 100.0);
                        item.setValorLiquido(item.getValorBruto() - item.getValorComissao());
                    }


                    if (UteisValidacao.emptyNumber(item.getCodigoMovPagamento())
                            && UteisValidacao.emptyNumber(item.getCodigoMovConta())) {
                        item.setSituacao(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE);
                    }
                } else if (!extratoREDE && !extratoBIN && !reprocessando && !stoneOnline && !stoneOnlineV5 && !pagoLivre
                        && item.getTipoRegistro()!= null && !item.getTipoRegistro().equals("014")) {
                    item.setDataPrevistaPagamento(mapaData.get(item.getRo()));
                    item.setTaxa(mapaTaxa.get(item.getRo()) == null ? 0.0 : mapaTaxa.get(item.getRo()));
                    item.setValorComissao((item.getValorBruto() * item.getTaxa()) / 100.0);
                    item.setValorLiquido(item.getValorBruto() - item.getValorComissao());
                }
//            if (!UteisValidacao.emptyString(item.getAjuste())) {
//                item.setSituacao(SituacaoItemExtratoEnum.AJUSTADO);
//            }

                // Registros novos processados pelo SERVICE estão duplicando registro pela TAG movimentação "PF","PG"
                // Se o registro ja tiver codigo não e necessário buscar.
                if (UteisValidacao.emptyNumber(item.getCodigo()) && !UteisValidacao.emptyString(item.getAutorizacao()) && null != item.getDataPrevistaPagamento()
                        && !item.getTipoRegistro().equals("012"))
                    item.setCodigo(consultarCodigoPor(item.getAutorizacao(), item.getDataPrevistaPagamento(), item.getNsu(), item.getTipoConciliacao(), item.getNrParcela(), item.getEmpresa().getCodigo()));
                if (UteisValidacao.emptyNumber(item.getCodigo()) || isCancelamentoOrChargeback(item)) {
                    boolean alterarDtPgtoZWAutomaticamenteConc = configuracaoFinanceiroVO.isAlterarDtPgtoZWAutomaticamenteConc();
                    if (alterarDtPgtoZWAutomaticamenteConc && item.getConvenio() != null
                            && item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)
                            && !isCancelamentoOrChargeback(item)) {
                        //Alterar automaticamente as datas de compensação caso configuração esteja marcada
                        verificarEAlterarDatasPagamentoZWAutomaticamente(item);
                    }
                    incluir(item);
                } else {
                    if (item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) ||
                            item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC)) {

                        if (item.getValorBruto() < 0) {
                            // Cielo teve um período em que vinha o layout 14 e 15 no mesmo dia.
                            // Se o dados de cancelamento for do mesmo arquivo, então é uma informação das parcelas canceladas que deve Incluir.
                            // Se for de arquivo diferente, então é um cancelamento de uma mesma venda que deve Alterar para atualizar.
                            ExtratoDiarioItemVO itemOriginal = consultarPorChavePrimaria(item.getCodigo());
                            if (itemOriginal != null && itemOriginal.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())
                                    && itemOriginal.getArquivo().equals(item.getArquivo())) {
                                item.setDataReprocessamento(Calendario.hoje());
                                incluir(item);
                            } else {
                                alterar(item);
                            }
                        } else if (reprocessando && UteisValidacao.emptyNumber(codMovPgtoAntesDeQualquerAlteracao) //se o codmovpagamento do item original era 0 e foi alterado em memória, deve alterar também em banco
                                && !UteisValidacao.emptyNumber(item.getCodigoMovPagamento())) {
                            alterar(item);
                        }
                    } else {
                        item.setDataReprocessamento(Calendario.hoje());
                        alterar(item);
                    }
                }

                //LANÇAMENTO DE CONTA A PAGAR!
                boolean lancarContaAPagarAutomaticamente = configuracaoFinanceiroVO.isCriarContaPagarAutomatico()
                        && item.getSituacao() != null && item.getSituacao().equals(SituacaoItemExtratoEnum.PENDENCIAS)
                        && item.getValorBruto() < 0 && item.getValorLiquido() < 0;
                if (lancarContaAPagarAutomaticamente) {
                    lancarContaAPagarOuReceberAutomaticaFinanceiro(convenioCobranca, item.getEmpresa(), item, item.getDataPrevistaPagamento(), configuracaoFinanceiroVO);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private static void lancarContaAPagarOuReceberAutomaticaFinanceiro(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresa, ExtratoDiarioItemVO itemVO, Date dataArquivo, ConfiguracaoFinanceiroVO configuracaoFinanceiroVO) {
        try {
            if (!convenioCobrancaVO.isCieloOnline()) {
                return;
            }
            itemVO.setDataArquivo(dataArquivo);
            if (convenioCobrancaVO.isCieloOnline()) {
                getFacade().getExtratoDiarioItem().criarContaPagarOuReceberCancelamentoCielo(convenioCobrancaVO, itemVO, empresa, configuracaoFinanceiroVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void verificarEAlterarDatasPagamentoZWAutomaticamente(ExtratoDiarioItemVO item) throws Exception {
        try {

            CartaoCreditoVO cartaoCreditoVO = null;
            if (!UteisValidacao.emptyNumber(item.getCodigoCartaoCredito())) {
                cartaoCreditoVO = getFacade().getCartaoCredito().consultarPorChavePrimaria(item.getCodigoCartaoCredito(), Uteis.NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS);
            }

            //reprocessamento chega aqui
            // no caso se estiver reprocessando, irá pegar a data daqui, pois o sistema já alterou a data de pagamento em outro processamento de extrato
            if (cartaoCreditoVO != null && cartaoCreditoVO.isAlterouDataRecebimentoZWAutomaticamente() && cartaoCreditoVO.getDataPgtoOriginalZWAntesDaAlteracaoAutomatica() != null) {
                item.setAlterouDataRecebimentoZWAutomaticamente(true);
                item.setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(cartaoCreditoVO.getDataPgtoOriginalZWAntesDaAlteracaoAutomatica());
//                return;
            }

            //primeiro processamento chega aqui
            if (cartaoCreditoVO != null && item.getDataPrevistaPagamento() != null) {
                Date dataPagamentoSistemaZW = cartaoCreditoVO.getDataCompensacao();
                boolean dataPgtoExtratoIgualSistemaZW = Calendario.igual(dataPagamentoSistemaZW, item.getDataPrevistaPagamento());
                if (dataPagamentoSistemaZW != null && !dataPgtoExtratoIgualSistemaZW) {
                    Uteis.logarDebug("Encontrei um pagamento do extrato com data prevista de pagamento diferente do sistema zw, vou mudar a data de pagamento dentro do ZW automaticamente..." +
                            (!UteisValidacao.emptyString(item.getAutorizacao()) ? " | AUT: " + item.getAutorizacao() : "") +
                            (!UteisValidacao.emptyString(item.getNsu()) ? " | NSU: " + item.getNsu() : ""));
                    try {
                        //atualizar informações extratodiarioitem antes de gravar e alterar a data de pagamento do zw aqui
                        item.setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(dataPagamentoSistemaZW);
                        getFacade().getExtratoDiarioItem().alterarDatasPagamento(item);
                        item.setAlterouDataRecebimentoZWAutomaticamente(true);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        Uteis.logarDebug("Não foi possível mudar a data de pagamento dentro do ZW automaticamente... | " + ex.getMessage());
                    }
                        //agora atualiza as informações na tabela cartaocredito que podem ser usadas futuramente em um reprocessamento de extrato
                    try {
                        cartaoCreditoVO.setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(dataPagamentoSistemaZW);
                        cartaoCreditoVO.setAlterouDataRecebimentoZWAutomaticamente(true);
                        getFacade().getCartaoCredito().alterarDadosMudancaAutomaticaPagamentoConciliacao(cartaoCreditoVO);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        Uteis.logarDebug("Não foi possível mudar a data de pagamento dentro do ZW automaticamente... | " + ex.getMessage());
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void obterConveniosEEmpresasOriginalCieloERede(ConvenioCobrancaVO convenioCobranca, Map<Integer, EmpresaVO> empresaVOMap, Map<String, ConvenioCobrancaVO> convenioCobrancaVOMap) throws Exception {
        // Dentro do arquivo Cielo, vem todos os estabelecimentos misturados, por isso precisa identificar os Convênios e Empresas pelo número de estabelecimento do arquivo, para cada item.
        if (
                convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) ||
                (convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC) && convenioCobranca.getTipo().getTipoRemessa().equals((TipoRemessaEnum.EDI_CIELO))) ||
                (convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE))
        ) {
            List<EmpresaVO> empresasList = getFacade().getEmpresa().consultarEmpresas(Uteis.NIVELMONTARDADOS_MINIMOS);
            List<ConvenioCobrancaVO> convenioCobrancaVOList = getFacade().getConvenioCobranca().consultarPorEmpresaSituacaoTipoAutorizacao(null, SituacaoConvenioCobranca.ATIVO,
                    null, null, Uteis.NIVELMONTARDADOS_TODOS);
            if(!UteisValidacao.emptyList(empresasList) && !UteisValidacao.emptyList(convenioCobrancaVOList)){
                for(EmpresaVO empresaVO: empresasList){
                    empresaVOMap.put(empresaVO.getCodigo(), empresaVO);
                }
                for(ConvenioCobrancaVO convenioCobrancaVO: convenioCobrancaVOList){
                    convenioCobrancaVOMap.put(convenioCobrancaVO.getNumeroContrato(), convenioCobrancaVO);
                }
            }
        }
    }

    public boolean isCancelamentoOrChargeback(ExtratoDiarioItemVO extratoDiarioItemVO) {
        if (!UteisValidacao.emptyNumber(extratoDiarioItemVO.getTipoConciliacao()) &&
                (extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.CHARGEBACK.getCodigo()) ||
                        extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) ||
                        extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.TAXA_CANCELAMENTO.getCodigo()))
        ) {
            return true;
        }
        return false;
    }

    private String getConsultaConciliacao(ConvenioCobrancaVO convenioCobranca, ExtratoDiarioItemVO item, boolean extratoREDE,
                                          boolean redeDebito, Integer codMovPagOrigemCredito, boolean stone) throws Exception {
        StringBuilder consultaConciliacao = new StringBuilder();
        //Teve de criar condições para a Conciliação da PagoLivre, por não informar a Data da Venda que é utilizada em todas as outras integrações.
        if (convenioCobranca != null &&
                (convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) || convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) &&
                item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) {
            consultaConciliacao.append("SELECT ");
        } else {
            consultaConciliacao.append("SELECT  @(mp.datalancamento::date - date'").append(Uteis.getDataFormatoBD(item.getDataLancamento())).append("') as diferencaLancamento, \n");
        }
        consultaConciliacao.append("mp.nomepagador,mp.pessoa as codigopessoa,mp.formapagamento as codigoformapagamento,  cc.codigo as codigocartaocredito,cc.composicao, mp.codigo as codigomovpagamento, NULL as codigomovconta, ");
        consultaConciliacao.append(" cc.datacompesancao, mp.datalancamento, mp.dataPagamento as dataQuitacao, mp.valor as valormp, cc.valortotal as valorcc, mp.nsu,\n" +
                " mp.movpagamentoorigemcredito ");
        consultaConciliacao.append(" FROM movpagamento mp ");
        consultaConciliacao.append(" LEFT JOIN cartaocredito cc ON mp.codigo = cc.movpagamento and cc.nrparcela = ").append(item.getNrParcela());
        consultaConciliacao.append(" WHERE mp.valor > 0 \n");
        if (redeDebito ||
                ((convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) || convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) &&
                item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo()))) {
            try {
                Integer nsuInteger = Integer.parseInt(item.getNsu());
                consultaConciliacao.append(" AND (nsu = '").append(nsuInteger).append("'");
                consultaConciliacao.append(" OR nsu ilike '").append(item.getNsu()).append("')");
            } catch (Exception e) {
                consultaConciliacao.append(" AND nsu ilike '").append(item.getNsu()).append("' \n");
            }
        } else {

            if(convenioCobranca != null &&
                    (convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) ||
                            convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC))){

                consultaConciliacao.append(" AND (");
                if (!UteisValidacao.emptyString(item.getNumeroUnicoTransacao())) {
                    consultaConciliacao.append(" numerounicotransacao = '").append(item.getNumeroUnicoTransacao()).append("' or ");
                }
                    // Utiliza numerounicotransacao porque ele é o único número unico confiável para fazer conciliação em contas de multiempresas.
                    // Com vários pinpads os códigos de autorização e nsu podem ser repetidos.
                    // Veja mais detalhes no ticket #14469 (assembla) e documentação EDI cielo https://github.com/DeveloperCielo/EDI/blob/master/source/extrato-edi.md
                    // Exitem casos que não estão preechidos os valores do numero unico logo os valores não são conciliados
                    // Possivel causa seja o lançamento manual.

                consultaConciliacao.append(" mp.autorizacaocartao ilike '%").append(item.getAutorizacao()).append("') \n");
            } else {
                if (extratoREDE && !UteisValidacao.emptyString(item.getAutorizacaoInt()) && !Uteis.getValidarStringSePossuiLetra(item.getAutorizacaoInt())) {
                    consultaConciliacao.append(" AND (mp.autorizacaocartao ilike '").append(item.getAutorizacao()).append("' or TRIM(LEADING '0' FROM CAST (mp.autorizacaocartao AS TEXT)) ilike '").append(Integer.valueOf(item.getAutorizacaoInt())).append("') ");
                } else {
                    consultaConciliacao.append(" AND (mp.autorizacaocartao ilike '").append(item.getAutorizacao()).append("' or TRIM(LEADING '0' FROM CAST (mp.autorizacaocartao AS TEXT)) ilike '").append(item.getAutorizacao()).append("') ");
                }
//                if (stone && !UteisValidacao.emptyString(item.getNsu())) {
//                    consultaConciliacao.append(" AND mp.nsu ilike '").append(item.getNsu()).append("' \n");
//                }
            }

            if(item.getTipoConciliacao() != null && item.getCredito() && item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())){
                consultaConciliacao.append(" AND cc.codigo is not null ");
            }
            if(!UteisValidacao.emptyNumber(codMovPagOrigemCredito)){
                consultaConciliacao.append(" AND mp.movpagamentoorigemcredito = ").append(codMovPagOrigemCredito);
            }
            consultaConciliacao.append("AND NOT (@(mp.datalancamento::date - date'").append(Uteis.getDataFormatoBD(item.getDataLancamento())).append("') > 10)"); //Sistema irá conciliar apenas com movpagamentos lançados em até 10 dias atrás.
        }
        if (stone && !UteisValidacao.emptyNumber(item.getEmpresa().getCodigo())) {
            consultaConciliacao.append(" AND mp.empresa = ").append(item.getEmpresa().getCodigo());
        } else {
            consultaConciliacao.append(" AND mp.empresa = ").append(convenioCobranca.getEmpresa().getCodigo());
        }
        if(
                item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())
                && convenioCobranca != null
                && (convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) || convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC))
        ) {
            //Cielo vai conciliar com dados no sistema com a mesma Data Lançamento informada pela Cielo no arquivo
            consultaConciliacao.append("AND (mp.datalancamento::date = date'").append(Uteis.getDataFormatoBD(item.getDataLancamento())).append("') \n");
        }
        consultaConciliacao.append(" UNION ALL	");
        if (convenioCobranca != null &&
                (convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) || convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) &&
                item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) {
            consultaConciliacao.append("SELECT ");
        } else {
            consultaConciliacao.append("SELECT  @(mp.datalancamento::date - date'").append(Uteis.getDataFormatoBD(item.getDataLancamento())).append("') as diferencaLancamento, \n");
        }
        consultaConciliacao.append( "p.nome as nomepagador, p.codigo as codigopessoa, null as codigoformapagamento,  cc.codigo as codigocartaocredito, null as composicao, null as codigomovpagamento, mp.codigo as codigomovconta, ");
        consultaConciliacao.append( " cc.datacompesancao, mp.datavencimento as datalancamento, mp.dataquitacao, mp.valor as valormp, cc.valor as valorcc, movp.nsu,\n" +
                " null as movpagamentoorigemcredito ");
        consultaConciliacao.append( " FROM movconta mp ");
        consultaConciliacao.append(" INNER JOIN pessoa p ON mp.pessoa = p.codigo ");
        consultaConciliacao.append( " LEFT JOIN cartaocredito cc ON mp.codigo = cc.movconta ");
        consultaConciliacao.append( " LEFT JOIN movpagamento movp ON movp.codigo = cc.movpagamento ");

        // Aqui não precisa validar numerounicotransacao porque lançamentos em movconta, que são feitas no modulo financeiros em contas a pagar e contas a receber,
        // nunca armazenam o numerounicotransacao
        if (extratoREDE && !redeDebito) {
            if (!UteisValidacao.emptyString(item.getAutorizacaoInt()) && !Uteis.getValidarStringSePossuiLetra(item.getAutorizacaoInt())) {
                consultaConciliacao.append(" WHERE (mp.autorizacaocartao ilike '").append(item.getAutorizacao()).append("' or TRIM(LEADING '0' FROM CAST (mp.autorizacaocartao AS TEXT)) ilike '").append(Integer.valueOf(item.getAutorizacaoInt())).append("') ");
            } else {
                consultaConciliacao.append(" WHERE (mp.autorizacaocartao ilike '").append(item.getAutorizacao()).append("' or TRIM(LEADING '0' FROM CAST (mp.autorizacaocartao AS TEXT)) ilike '").append(item.getAutorizacaoInt()).append("') ");
            }
        } else if (extratoREDE) {
            //pesquisando por nsu para as remessas que são por débito.
            consultaConciliacao.append(" WHERE (nsu = '").append(!UteisValidacao.emptyString(item.getNsu()) ? Integer.parseInt(item.getNsu()) : item.getNsu()).append("'");
            consultaConciliacao.append(" OR nsu = '").append(item.getNsu()).append("')");
        } else{
            consultaConciliacao.append(" WHERE (mp.autorizacaocartao ilike '").append(item.getAutorizacao()).append("' or TRIM(LEADING '0' FROM CAST (mp.autorizacaocartao AS TEXT)) ilike '").append(item.getAutorizacao()).append("') ");
        }
        if (stone && !UteisValidacao.emptyNumber(item.getEmpresa().getCodigo())) {
            consultaConciliacao.append(" AND mp.empresa = ").append(item.getEmpresa().getCodigo()).append("\n ");
        } else {
            consultaConciliacao.append(" AND mp.empresa = ").append(convenioCobranca.getEmpresa().getCodigo()).append("\n ");
        }
        if(
                item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())
                && convenioCobranca != null
                && (convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) || convenioCobranca.getTipo().equals(TipoConvenioCobrancaEnum.DCC))
        ) {
            //Cielo vai conciliar com dados no sistema com a mesma Data Lançamento informada pela Cielo no arquivo
            consultaConciliacao.append("AND (mp.datalancamento::date = date'").append(Uteis.getDataFormatoBD(item.getDataLancamento())).append("') \n");
        }
        consultaConciliacao.append(" AND mp.tipooperacao NOT IN (").append(TipoOperacaoLancamento.RECEBIVEL_AVULSO.getCodigo()).append(") \n");
        consultaConciliacao.append(" order by 1,11,9,7,5 limit 1"); // ordenação tenta processar as compensações em order crescente,tentando sempre conciliar as primeiras

        return consultaConciliacao.toString();
    }

    private void doCapturaAutorizacaoERede(List<ExtratoDiarioItemVO> extrato) throws Exception {
        Iterator<ExtratoDiarioItemVO> it = extrato.iterator();
        List<ExtratoDiarioItemVO> extratoExtra = new ArrayList<ExtratoDiarioItemVO>();


        while (it.hasNext()){
            ExtratoDiarioItemVO item = it.next();
            boolean extratoREDE = item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE);

            if (extratoREDE) {
                if (item.getTipoArquivo().equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getTipoArquivoRedeEnum().getId())
                        && item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getId())) {

                    if (UteisValidacao.emptyString(item.getAutorizacao())) {
                        List<ExtratoDiarioItemVO> itenAutorizao = consultarExtratosRedeProcessarPagamento(item.getEstabelecimento(), item.getRo(), ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getId() ,ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getTipoArquivoRedeEnum().getId() , item.getNrParcela(), item.getNrTotalParcelas(), null);
                        if (itenAutorizao.size() == 0){
                            itenAutorizao = consultarExtratosRedeProcessarPagamento(item.getEstabelecimento(), item.getRo(), ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro014.getId() ,ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getTipoArquivoRedeEnum().getId() , item.getNrParcela(), null, null);
                        }
                        if (!UteisValidacao.emptyList(itenAutorizao)) {//caso tenha somente uma autorizacao vinculada ao RO
                            for (ExtratoDiarioItemVO item2 : itenAutorizao) {
                                item2.setApresentarExtrato(true);
                                if(item2.getCodigoMovPagamento() != null && item2.getCodigoMovPagamento() > 0){
                                    MovPagamento movPagamentoDAO = new MovPagamento(con);
                                    MovPagamentoVO movPagamentoVO = null;
                                    try {
                                        movPagamentoVO = movPagamentoDAO.consultarPorChavePrimaria(item2.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    } catch (ConsistirException ignored) {
                                        //É ignorado essa exception, pois ela pode ser referente a um MovPagamento que foi Extornado e o valor consta no ExtratoDiarioItem
                                        item2.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_SISTEMA);
                                    }
                                    if (movPagamentoVO != null) {
                                        item2.setSituacao(SituacaoItemExtratoEnum.OK);
                                    }
                                }
                                if(!UteisValidacao.emptyNumber(item2.getConvenio().getCodigo())){
                                    ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
                                    ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(item2.getConvenio().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    if(convenioCobrancaVO != null){
                                        item2.setCodConvenio(convenioCobrancaVO.getCodigo());
                                        item2.setConvenio(convenioCobrancaVO);
                                    }
                                }
                            }
                            extratoExtra.addAll(itenAutorizao);
                        }
                    }
                }
            }
        }

        if(!UteisValidacao.emptyList(extratoExtra)){
            extrato.addAll(extratoExtra);
        }
    }

    private List<ExtratoDiarioItemVO> processarListaGetNetEOutros(List<ExtratoDiarioItemVO> listaExtratoDiarioItem) {

        Map<String, List<ExtratoDiarioItemVO>> mapaAutoItem = new HashMap<String, List<ExtratoDiarioItemVO>>();

        List<ExtratoDiarioItemVO> extratoFinal = new ArrayList<ExtratoDiarioItemVO>();

        for (ExtratoDiarioItemVO item : listaExtratoDiarioItem) {
            boolean extratoGetNet = item.isItemGetNet();

            if (!extratoGetNet) {

                extratoFinal.add(item);

            } else if (item.getTipoRegistro().equals(TipoRegistroExtratoGetNetEnum.TipoRegistro2.getId())) {

                List<ExtratoDiarioItemVO> listaAuto = mapaAutoItem.get(item.getAutorizacao());
                if (listaAuto == null) {
                    listaAuto = new ArrayList<ExtratoDiarioItemVO>();
                }

                listaAuto.add(item);
                mapaAutoItem.put(item.getAutorizacao(), listaAuto);

                extratoFinal.add(item);
            }
        }

        if (!mapaAutoItem.isEmpty()) {
            Set<String> autorizacoes = mapaAutoItem.keySet();
            for (String auto : autorizacoes) {

                List<ExtratoDiarioItemVO> listaItens = mapaAutoItem.get(auto);
                if (!UteisValidacao.emptyList(listaItens)) {

                    Double total = 0.0;
                    Double liquido = 0.0;
                    Double comissao = 0.0;
                    ExtratoDiarioItemVO novo = new ExtratoDiarioItemVO();

                    try {

                        for (ExtratoDiarioItemVO item : listaItens) {

                            novo = (ExtratoDiarioItemVO) BeanUtils.cloneBean(item);

                            total += (item.getValorBruto() == null ? 0.0 : item.getValorBruto());
                            liquido += (item.getValorLiquido() == null ? 0.0 : item.getValorLiquido());
                            comissao += (item.getValorComissao() == null ? 0.0 : item.getValorComissao());

                        }

                        novo.setApresentarExtrato(true);
                        novo.setNrParcela(0);
                        novo.setTipoConciliacao(TipoConciliacaoEnum.VENDAS.getCodigo());
                        novo.setTipoRegistro(TipoRegistroExtratoGetNetEnum.TipoRegistro1.getId());
                        novo.setValorBruto(total);
                        novo.setValorLiquido(liquido);
                        novo.setValorComissao(comissao);
                        extratoFinal.add(novo);
                    } catch (Exception ex) {
                        System.out.println("Erro ao clonar item || " + ex.getMessage());
                    }

                }
            }
        }

        return extratoFinal;
    }

    private void setarSituacaoItemExtrato(ExtratoDiarioItemVO item, Date dataLancamentoSistema,
                                          Date dataCompensacaoSistema, Integer movpagamento, Integer movconta, Integer cartaocredito, Double valor, String nomePagador,
                                          Integer codigoPessoa) {
        item.setCodigoCartaoCredito(cartaocredito);
        item.setNomePagador(nomePagador);
        item.setCodigoMovPagamento(movpagamento);
        item.setCodigoMovConta(movconta);
        item.setDataAproximada(dataCompensacaoSistema);
        item.getPessoa().setCodigo(codigoPessoa);
        if (item.getTipoConciliacao() != null && item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) {
            item.setSituacao(Calendario.igual(item.getDataPrevistaPagamento(), dataCompensacaoSistema)
                    && item.getValorBruto().equals(valor) ? SituacaoItemExtratoEnum.OK : SituacaoItemExtratoEnum.PENDENCIAS);
            if (item.isEstorno()) {
                item.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_OPERADORA);
            }
        } else {
            item.setSituacao(Calendario.igual(item.getDataLancamento(), dataLancamentoSistema)
                    && item.getValorBruto().equals(valor) ? SituacaoItemExtratoEnum.OK : SituacaoItemExtratoEnum.PENDENCIAS);
        }
        item.setSomenteDatas(item.getValorBruto().equals(valor));

    }

    public void simularExtratoDiario(Date inicio, Date fim) throws Exception {
        List<ExtratoDiarioItemVO> extratoPorDia = consultarExtratoPorDia(inicio, fim);
        if (extratoPorDia == null || extratoPorDia.isEmpty()) {
            extratoPorDia = new ArrayList<ExtratoDiarioItemVO>();
            String sql = "SELECT autorizacaocartao, (select count(codigo) from cartaocredito ccd WHERE "
                    + "ccd.datacompesancao <= cc.datacompesancao and ccd.movpagamento = cc.movpagamento) as nrparcela, \n"
                    + "cc.codigo as cartaocredito, mp.codigo as movpagamento, cc.datacompesancao, mp.datalancamento, mp.dataquitacao, mp.valor as valormp, cc.valor as valorcc  FROM movpagamento mp\n"
                    + "LEFT JOIN cartaocredito cc ON mp.codigo = cc.movpagamento\n"
                    + "where datalancamento BETWEEN ? AND ? and autorizacaocartao is not null and autorizacaocartao not like '' ";
            PreparedStatement stm = con.prepareStatement(sql);
            stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(inicio, "00:00:00")));
            stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:00")));
            ResultSet rs = stm.executeQuery();
            while (rs.next()) {
                ExtratoDiarioItemVO item = new ExtratoDiarioItemVO();
                item.setDataLancamento(rs.getDate("datalancamento"));
                item.setDataPrevistaPagamento(rs.getDate("datacompesancao") == null ? rs.getDate("dataquitacao") : rs.getDate("datacompesancao"));
                item.setAutorizacao(rs.getString("autorizacaocartao"));
                item.setEstabelecimento("10255454865");
                item.setNrCartao("518544******3067");
                item.setValorBruto(UteisValidacao.emptyNumber(rs.getDouble("valorcc")) ? rs.getDouble("valormp") : rs.getDouble("valorcc"));
                item.setValorLiquido(item.getValorBruto() - (item.getValorBruto() * 0.05));
                item.setValorComissao(item.getValorBruto() * 0.05);
                item.setNrParcela(rs.getInt("nrParcela"));
                extratoPorDia.add(item);
            }
            boolean naoAltereiAlgum = true;
            if (!extratoPorDia.isEmpty()) {
                ExtratoDiarioItemVO i = new ExtratoDiarioItemVO();
                i.setDataLancamento(inicio);
                i.setDataPrevistaPagamento(inicio);
                i.setAutorizacao("TE" + inicio.getTime());
                i.setEstabelecimento("10255454865");
                i.setNrCartao("221444******8012");
                i.setValorBruto(158.0);
                i.setValorLiquido(i.getValorBruto() - (i.getValorBruto() * 0.05));
                i.setValorComissao(i.getValorBruto() * 0.05);
                i.setNrParcela(0);
                extratoPorDia.add(i);

                for (ExtratoDiarioItemVO item : extratoPorDia) {
                    if (UteisValidacao.emptyNumber(item.getNrParcela())) {
                        if (naoAltereiAlgum) {
                            naoAltereiAlgum = false;
                            item.setValorBruto(item.getValorBruto() + 8);
                            item.setValorLiquido(item.getValorBruto() - (item.getValorBruto() * 0.05));
                            item.setValorComissao(item.getValorBruto() * 0.05);
                        } else {
                            item.setDataPrevistaPagamento(Uteis.somarCampoData(item.getDataPrevistaPagamento(), Calendar.DAY_OF_MONTH, 2));
                        }
                    }
                    incluir(item);
                }

            }
        }
        processarListaExtratoDiario(extratoPorDia, true);
    }

    public void alterarDatasPagamento(ExtratoDiarioItemVO item) throws Exception {
        MovPagamento movPagamentoDAO;
        try {
            movPagamentoDAO = new MovPagamento(con);

            if (!UteisValidacao.emptyNumber(item.getCodigoCartaoCredito())) {
                List<Integer> codigosCartao = new ArrayList<>();
                codigosCartao.add(item.getCodigoCartaoCredito());
                ResultSet rsComposicao = criarConsulta("SELECT composicao "
                        + " FROM cartaocredito "
                        + " WHERE codigo = " + item.getCodigoCartaoCredito(), con);
                while (rsComposicao.next()) {
                    if (UteisValidacao.emptyString(rsComposicao.getString("composicao"))) {
                        continue;
                    }
                    String[] composicao = rsComposicao.getString("composicao").split(",");
                    for (String codCartao : composicao) {
                        codigosCartao.add(Integer.parseInt(codCartao));
                    }
                }
                for (Integer cartao : codigosCartao) {
                    //preencher data original da compensação antes de alterar a compensação
                    PreparedStatement stm = con.prepareStatement("UPDATE cartaocredito SET dataoriginal = datacompesancao WHERE codigo = ?");
                    stm.setInt(1, cartao);
                    stm.execute();

                    PreparedStatement stm2 = con.prepareStatement("UPDATE cartaocredito SET datacompesancao = ? WHERE codigo = ?");
                    stm2.setDate(1, Uteis.getDataJDBC(item.getDataPrevistaPagamento()));
                    stm2.setInt(2, cartao);
                    stm2.execute();
                }
                item.setSituacao(SituacaoItemExtratoEnum.OK);
                alterarSituacao(item);
            } else if (!UteisValidacao.emptyNumber(item.getCodigoMovPagamento())) {

                if (item.getMovPagamento() == null ||
                        UteisValidacao.emptyNumber(item.getMovPagamento().getCodigo())) {
                    item.setMovPagamento(movPagamentoDAO.consultarPorChavePrimaria(item.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }

                if (!UteisValidacao.emptyString(item.getNsu())) {
                    PreparedStatement stmMp = con.prepareStatement("UPDATE movpagamento SET  dataquitacao = ?, datapagamento = ? WHERE pessoa = ? and formapagamento = ? and (upper(autorizacaocartao) = ? or upper(nsu) = ?)");
                    stmMp.setDate(1, Uteis.getDataJDBC(item.getDataPrevistaPagamento()));
                    stmMp.setDate(2, Uteis.getDataJDBC(item.getDataPrevistaPagamento()));
                    stmMp.setInt(3, item.getMovPagamento().getPessoa().getCodigo());
                    stmMp.setInt(4, item.getMovPagamento().getFormaPagamento().getCodigo());
                    stmMp.setString(5, item.getAutorizacao().toUpperCase());
                    stmMp.setString(6, item.getNsu().toUpperCase());
                    stmMp.execute();
                    item.setSituacao(SituacaoItemExtratoEnum.OK);
                    alterarSituacao(item);
                } else {
                    PreparedStatement stmMp = con.prepareStatement("UPDATE movpagamento SET  dataquitacao = ?, datapagamento = ? WHERE pessoa = ? and formapagamento = ? and upper(autorizacaocartao) = ?");
                    stmMp.setDate(1, Uteis.getDataJDBC(item.getDataPrevistaPagamento()));
                    stmMp.setDate(2, Uteis.getDataJDBC(item.getDataPrevistaPagamento()));
                    stmMp.setInt(3, item.getMovPagamento().getPessoa().getCodigo());
                    stmMp.setInt(4, item.getMovPagamento().getFormaPagamento().getCodigo());
                    stmMp.setString(5, item.getAutorizacao().toUpperCase());
                    stmMp.execute();
                    item.setSituacao(SituacaoItemExtratoEnum.OK);
                    alterarSituacao(item);
                }
            } else if (!UteisValidacao.emptyNumber(item.getCodigoMovConta())) {
                PreparedStatement stmMc = con.prepareStatement("UPDATE movconta SET datavencimento = ? WHERE codigo = ?");
                stmMc.setDate(1, Uteis.getDataJDBC(item.getDataLancamento()));
                stmMc.setInt(2, item.getCodigoMovConta());
                stmMc.execute();
                item.setSituacao(SituacaoItemExtratoEnum.OK);
                alterarSituacao(item);
            }
        } finally {
            movPagamentoDAO = null;
        }
    }

    @Override
    public void mesclarLancamentos(ExtratoDiarioItemVO itemZW, ExtratoDiarioItemVO itemED) throws Exception {
        boolean conciliado = false;
        if (itemZW.getCartao() != null && !UteisValidacao.emptyNumber(itemZW.getCartao().getCodigo())) {
            itemED.setCodigoCartaoCredito(itemZW.getCartao().getCodigo());
            conciliado = true;
            PreparedStatement stmMp = con.prepareStatement("UPDATE movpagamento SET autorizacaocartao = ? WHERE codigo IN (SELECT movpagamento FROM cartaocredito cc WHERE cc.codigo = ?)");
            stmMp.setString(1, itemED.getAutorizacao());
            stmMp.setInt(2, itemZW.getCartao().getCodigo());
            stmMp.execute();
        } else if (itemZW.getMovPagamento() != null && !UteisValidacao.emptyNumber(itemZW.getMovPagamento().getCodigo())) {
            itemED.setCodigoMovPagamento(itemZW.getMovPagamento().getCodigo());
            itemED.setMovPagamento(itemZW.getMovPagamento());
            conciliado = true;
            PreparedStatement stmMp = con.prepareStatement("UPDATE movpagamento SET autorizacaocartao = ? WHERE pessoa = ? and formapagamento = ? and datalancamento = ? and datapagamento = ? ");
            stmMp.setString(1, itemED.getAutorizacao());
            stmMp.setInt(2, itemED.getMovPagamento().getPessoa().getCodigo());
            stmMp.setInt(3, itemED.getMovPagamento().getFormaPagamento().getCodigo());
            stmMp.setTimestamp(4, Uteis.getDataJDBCTimestamp(itemED.getMovPagamento().getDataLancamento()));
            stmMp.setTimestamp(5,  Uteis.getDataJDBCTimestamp(itemED.getMovPagamento().getDataPagamento()));
            stmMp.execute();
        }

        if (conciliado) {
            alterarDatasPagamento(itemED);
        }
    }

    public ResultSet consultarConciliadosPorMovpagamento(Date inicio, Date fim, String ro, String autorizacao, Integer tipoConciliacao,
                                                         Integer empresa, Integer convenio, Integer formaPagamento, Boolean apresentarPagamentosCancelados,
                                                         Integer operadoraCartao, Integer adquirente, String nsu) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT mp.operadoracartao, mp.adquirente, ad.nome as nomeAdquirente ,ei.props,ei.tiporegistro, ei.estabelecimento, pes.codigo as codigopessoa, ei.tipoconciliacao, \n");
        sql.append(" ei.autorizacao as autorizacaoei, mp.autorizacaocartao as autorizacaomp, ei.nrtotalparcelas, mp.produtospagos, mp.movpagamentoorigemcredito, mp.recibopagamento, \n");
        sql.append(" mp.valor as valormp, mp.datalancamento as lancmp, mp.datapagamento as datapagamentomp, \n");
        sql.append(" coalesce(cc.valor,ccpag.valor) as valorcc, coalesce(cc.datacompesancao,ccpag.datacompesancao) as compensacaocc,mp.nrparcelacartaocredito, cc.nrparcela, \n");
        sql.append(" ei.dataprevistapagamento, fp.tipoformapagamento,fp.codigo as codigoformapagamento, fp.descricao as formapgto, mp.nomepagador, ei.codigo, ei.datalancamento as lancei, \n");
        sql.append(" ei.nrcartao, ei.valorbruto, ei.ro, ei.valorliquido, ei.valorcomissao, \n");
        sql.append(" ei.codigomovpagamento as codigoMovPagamentoExtrato, mp.codigo as codigomovpagamento,  ei.codigomovconta, ei.codigocartaocredito as codigocartaocreditoextrato, ccpag.codigo as codigocartaocredito, mp.movconta,  \n");
        sql.append(" ei.situacao, ei.nrparcela as nrParcelaei,  ei.taxa, coalesce(cc.nrparcela,ccpag.nrparcela) nrparcelacartao, case when length(coalesce(conta.descricaocurta,'')) > 1 then conta.descricaocurta else conta.descricao end as contamovimento, ei.credito,ei.identificadorproduto, ei.nsu, ei.tipoarquivo, ei.estorno \n");
        sql.append(" ,pes2.nome as nomeAluno, cli.matricula, ei.tipoconveniocobranca, ei.conveniocobranca, \n");
        sql.append(" coalesce(cc.composicao, ccpag.composicao ) as composicao, ei.antecipacao, ei.datapgtooriginalantesdaantecipacao, ei.pessoa, ei.valorDescontadoAntecipacao, ei.taxaCalculadaAntecipacao, \n");
        sql.append(" ei.idexterno, ei.idexterno2, ei.alterouDataRecebimentoZWAutomaticamente, ei.dataPgtoOriginalZWAntesDaAlteracaoAutomatica \n");
        sql.append(" FROM movpagamento mp \n");
        sql.append(" LEFT JOIN pessoa pes on pes.codigo = mp.pessoa \n");
        sql.append(" LEFT JOIN (select a.*, b.tipoconvenio from extratodiarioitem a left join conveniocobranca b on  a.conveniocobranca = b.codigo \n");

        if (tipoConciliacao.equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) {
            sql.append(" WHERE a.tipoconciliacao in (").append(TipoConciliacaoEnum.CHARGEBACK.getCodigo()).append(", ").append(TipoConciliacaoEnum.TAXA_CANCELAMENTO.getCodigo()).append(", ").append(tipoConciliacao).append(")");
        } else {
            sql.append(" WHERE a.tipoconciliacao in (").append(tipoConciliacao).append(", ").append(TipoConciliacaoEnum.CANCELAMENTO.getCodigo()).append(")");
        }
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao)) {
            sql.append(" AND  (b.tipoconvenio <> 16 or ( b.tipoconvenio = 16 and a.tipoarquivo not in ('EEFI'))) \n");
        }
        sql.append(" ) ei ON mp.codigo = ei.codigomovpagamento \n");

        sql.append(" LEFT JOIN pessoa pes2 on pes2.codigo = ei.pessoa \n");
        sql.append(" LEFT JOIN cliente cli on cli.pessoa = pes2.codigo \n");
        sql.append(" LEFT JOIN cartaocredito cc ON cc.codigo = ei.codigocartaocredito \n");
        sql.append(" LEFT JOIN cartaocredito ccpag ON ccpag.movpagamento = mp.codigo and ccpag.codigo = cc.codigo \n");
        sql.append(" LEFT JOIN formapagamento fp ON fp.codigo = mp.formapagamento \n");
        sql.append(" LEFT JOIN movconta mc ON mc.codigo = mp.movconta \n");
        sql.append(" LEFT JOIN conta ON conta.codigo = mc.conta  \n");
        sql.append(" LEFT JOIN adquirente ad on ad.codigo=mp.adquirente \n");

        sql.append(" WHERE (apresentarExtrato = true or apresentarExtrato is null) \n");
        sql.append(" AND fp.tipoformapagamento in ('CA', 'CD') AND mp.valor > 0 \n");
        if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND ((ei.dataprevistapagamento BETWEEN ? AND ?  and (cc.codigo is null or cc.codigo = ccpag.codigo) ) \n");
            sql.append(" OR (mp.dataPagamento BETWEEN ? AND ? AND ei.codigo is null AND fp.tipoformapagamento  = 'CD') \n");
            sql.append(" OR (ccpag.datacompesancao BETWEEN ? AND ? AND ei.codigo is null AND fp.tipoformapagamento  = 'CA')) \n");
        }
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND ((ei.datalancamento BETWEEN ? AND ? ) \n");
            sql.append(" OR (mp.datalancamento BETWEEN ? AND ? AND ei.codigo is null)) \n");
        }
        if (!UteisValidacao.emptyString(ro)) {
            sql.append(" AND ((CAST(coalesce(ei.ro, '0') AS integer) = ?)) \n");
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            sql.append(" AND (ei.autorizacao ilike ? or mp.autorizacaocartao ilike ?) \n");
        }
        if (!UteisValidacao.emptyString(nsu)) {
            sql.append(" AND (ei.nsu ilike '").append(nsu).append("' or mp.nsu ilike '").append(nsu).append("') \n");
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            sql.append(" AND ei.conveniocobranca = ? \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND mp.empresa = ? \n");
        }

        sql.append(" AND (mp.recibopagamento is not null or (mp.recibopagamento is null and coalesce(valorbruto, 0) < 0))\n"); //valor negativo para cancelamentos
        sql.append(" AND ((mp.movpagamentoorigemcredito is null) or (mp.movpagamentoorigemcredito is not null and valorbruto is not null)) \n");

        if (!UteisValidacao.emptyNumber(formaPagamento)) {
            sql.append(" AND mp.formapagamento = ? \n");
        }
        if (!apresentarPagamentosCancelados) {
            sql.append(" AND (coalesce(ccpag.situacao, '') NOT IN ('CA'))\n");
        }
        sql.append(" AND ((mp.movpagamentoorigemcredito IS NULL AND mp.produtospagos NOT LIKE '%CC%') or (mp.movpagamentoorigemcredito IS not null and coalesce(valorbruto, 0) < 0))"); //valor negativo para cancelamentos
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            sql.append("AND mp.operadoracartao = ? \n");
        }
        if (!UteisValidacao.emptyNumber(adquirente)) {
            sql.append(" and mp.adquirente= ").append(adquirente);
        }
        if(tipoConciliacao == TipoConciliacaoEnum.CHARGEBACK.getCodigo()){
            sql.append(" and ei.estorno = true");
        }
        int i = 1;
        sql.append(" ORDER BY situacao, mp.nomepagador, autorizacao ");
        PreparedStatement stm = con.prepareStatement(sql.toString());

        if (inicio != null && fim != null && tipoConciliacao != TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
            if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao)) {
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
            }
        }

        if (!UteisValidacao.emptyString(ro)) {
            Integer roInt = 0;
            try {
                roInt = Integer.valueOf(ro);
            } catch (Exception e) {
            }
            stm.setInt(i++, roInt);
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            stm.setString(i++, "%" + autorizacao);
            stm.setString(i++, "%" + autorizacao);
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            stm.setInt(i++, convenio);
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            stm.setInt(i++, empresa);
        }
        if (!UteisValidacao.emptyNumber(formaPagamento)) {
            stm.setInt(i++, formaPagamento);
        }
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            stm.setInt(i++, operadoraCartao);
        }

        return stm.executeQuery();
    }

    private ResultSet consultarExtratoConcilicaoMovpagamentoContaCorrente(Date inicio, Date fim, String ro,
                                                             String autorizacao, Integer tipoConciliacao,
                                                             Integer empresa, Integer convenio, Integer formaPagamento,
                                                             Boolean apresentarPagamentosCancelados, Integer operadoraCartao, Integer adquirente, String nsu) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT mp.operadoracartao, mp.adquirente, ad.nome as nomeAdquirente ,ei.props,ei.tiporegistro, ei.estabelecimento, pes.codigo as codigopessoa, ei.tipoconciliacao,  fp.tipoformapagamento,fp.codigo as codigoformapagamento, fp.descricao as formapgto, mp.nomepagador, ei.codigo, ei.datalancamento as lancei, \n");
        sql.append(" ei.autorizacao as autorizacaoei, mp.autorizacaocartao as autorizacaomp, ei.nrtotalparcelas, mp.produtospagos, mp.movpagamentoorigemcredito, mp.recibopagamento, \n");
        sql.append(" mp.valor as valormp, mp.datalancamento as lancmp, mp.datapagamento as datapagamentomp, \n");
        sql.append(" coalesce(cc.valor,ccpag.valor) as valorcc, coalesce(cc.datacompesancao,ccpag.datacompesancao) as compensacaocc,mp.nrparcelacartaocredito, cc.nrparcela, \n");
        sql.append(" ei.dataprevistapagamento, ei.autorizacao as autorizacaoei, \n");
        sql.append(" ei.nrcartao, ei.valorbruto, ei.ro, ei.valorliquido, ei.valorcomissao, \n");
        sql.append(" ei.codigomovpagamento as codigoMovPagamentoExtrato, mp.codigo as codigomovpagamento,  ei.codigomovconta, ei.codigocartaocredito as codigocartaocreditoextrato, ccpag.codigo as codigocartaocredito, mp.movconta,  \n");
        sql.append(" ei.situacao, ei.nrparcela as nrParcelaei,  ei.taxa, coalesce(cc.nrparcela,ccpag.nrparcela) nrparcelacartao, case when length(coalesce(conta.descricaocurta,'')) > 1 then conta.descricaocurta else conta.descricao end as contamovimento, ei.credito,ei.identificadorproduto, ei.nsu, ei.tipoarquivo, ei.estorno \n");
        sql.append(" ,pes2.nome as nomeAluno, cli.matricula, ei.tipoconveniocobranca, \n");
        sql.append(" coalesce(cc.composicao, ccpag.composicao ) as composicao, ei.antecipacao, ei.datapgtooriginalantesdaantecipacao, ei.pessoa, ei.valorDescontadoAntecipacao, ei.taxaCalculadaAntecipacao, \n");
        sql.append(" ei.alterouDataRecebimentoZWAutomaticamente, ei.dataPgtoOriginalZWAntesDaAlteracaoAutomatica \n");
        sql.append(" FROM movpagamento mp \n");
        sql.append(" LEFT JOIN pessoa pes on pes.codigo = mp.pessoa \n");
        sql.append(" LEFT JOIN (select a.*, b.tipoconvenio from extratodiarioitem a left join conveniocobranca b on  a.conveniocobranca = b.codigo \n");

        if (tipoConciliacao == 4) {
            sql.append(" WHERE a.tipoconciliacao in (6, " + tipoConciliacao + ")");
        } else {
            sql.append(" WHERE a.tipoconciliacao in (" + tipoConciliacao + ")");
        }
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao)) {
            sql.append(" AND  (b.tipoconvenio <> 16 or ( b.tipoconvenio = 16 and a.tipoarquivo in ('EEVC', 'EEVD'))) \n");
        } else {
            sql.append(" AND  (b.tipoconvenio <> 16 or ( b.tipoconvenio = 16 and a.tipoarquivo in ('EEVC', 'EEVD', 'EEFI', 'API_REDE'))) \n");
        }
        sql.append(" ) ei ON mp.codigo = ei.codigomovpagamento \n");

        sql.append(" LEFT JOIN pessoa pes2 on pes2.codigo = ei.pessoa \n");
        sql.append(" LEFT JOIN cliente cli on cli.pessoa = pes2.codigo \n");
        sql.append(" LEFT JOIN cartaocredito cc ON cc.codigo = ei.codigocartaocredito \n");
        sql.append(" LEFT JOIN cartaocredito ccpag ON ccpag.movpagamento = mp.codigo \n");
        sql.append(" LEFT JOIN formapagamento fp ON fp.codigo = mp.formapagamento \n");
        sql.append(" LEFT JOIN movconta mc ON mc.codigo = mp.movconta \n");
        sql.append(" LEFT JOIN conta ON conta.codigo = mc.conta  \n");
        sql.append(" LEFT JOIN adquirente ad on ad.codigo=mp.adquirente \n");

        sql.append(" WHERE (apresentarExtrato = true or apresentarExtrato is null) \n");
        sql.append(" AND fp.tipoformapagamento in ('CA', 'CD') \n");
        if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND ((ei.dataprevistapagamento BETWEEN ? AND ?  and (cc.codigo is null or cc.codigo = ccpag.codigo) ) \n");
            sql.append(" OR (mp.dataPagamento BETWEEN ? AND ? AND ei.codigo is null AND fp.tipoformapagamento  = 'CD') \n");
            sql.append(" OR (ccpag.datacompesancao BETWEEN ? AND ? AND ei.codigo is null AND fp.tipoformapagamento  = 'CA' AND (ccpag.situacao LIKE 'EA' || ccpag.situacao IS NULL))) \n");
        }
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND ((ei.datalancamento BETWEEN ? AND ? ) \n");
            sql.append(" OR (mp.datalancamento BETWEEN ? AND ? AND ei.codigo is null)) \n");
        }
        if (!UteisValidacao.emptyString(ro)) {
            sql.append(" AND ((CAST(coalesce(ei.ro, '0') AS integer) = ?)) \n");
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            sql.append(" AND (ei.autorizacao ilike ? or mp.autorizacaocartao ilike ?) \n");
        }
        if (!UteisValidacao.emptyString(nsu)) {
            sql.append(" AND (ei.nsu ilike '").append(nsu).append("' or mp.nsu ilike '").append(nsu).append("') \n");
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            sql.append(" AND ei.conveniocobranca = ? \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND mp.empresa = ? \n");
        }

        sql.append(" AND mp.recibopagamento is not null \n");
        sql.append(" AND ((mp.movpagamentoorigemcredito is null) or (mp.movpagamentoorigemcredito is not null and valorbruto is not null)) \n");

        if (!UteisValidacao.emptyNumber(formaPagamento)) {
            sql.append(" AND mp.formapagamento = ? \n");
        }

        sql.append(" AND mp.produtospagos LIKE '%CC%' \n");

        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            sql.append("AND mp.operadoracartao = ? \n");
        }
        if (!UteisValidacao.emptyNumber(adquirente)) {
            sql.append(" and mp.adquirente= ").append(adquirente);
        }
        if(tipoConciliacao == TipoConciliacaoEnum.CHARGEBACK.getCodigo()){
            sql.append(" and ei.estorno = true");
        }
        int i = 1;
        sql.append(" ORDER BY situacao, mp.nomepagador, autorizacao ");
        PreparedStatement stm = con.prepareStatement(sql.toString());

        if (inicio != null && fim != null && tipoConciliacao != TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
            if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao)) {
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
                stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
            }
        }

        if (!UteisValidacao.emptyString(ro)) {
            Integer roInt = 0;
            try {
                roInt = Integer.valueOf(ro);
            } catch (Exception e) {
            }
            stm.setInt(i++, roInt);
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            stm.setString(i++, "%" + autorizacao);
            stm.setString(i++, "%" + autorizacao);
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            stm.setInt(i++, convenio);
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            stm.setInt(i++, empresa);
        }
        if (!UteisValidacao.emptyNumber(formaPagamento)) {
            stm.setInt(i++, formaPagamento);
        }
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            stm.setInt(i++, operadoraCartao);
        }

        return stm.executeQuery();
    }

    private ResultSet consultarExtratoNaoConciliado(Date inicio, Date fim, String ro, String autorizacao, Integer tipoConciliacao,
                                                    Integer empresa, Integer convenio, Integer formaPagamento, Boolean apresentarPagamentosCancelados,
                                                    Integer operadoraCartao, Integer adquirente, String nsu) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("cl.matricula, \n");
        sql.append("p.nome as nome, \n");
        sql.append("p.nome as nomeAluno, \n");
        sql.append("p.nome as nomepagador, \n");
        sql.append("ei.datalancamento as lancei, \n");
        sql.append("ei.autorizacao as autorizacaoei, \n");
        sql.append("ei.codigomovpagamento as codigoMovPagamentoExtrato, \n");
        sql.append("ei.codigocartaocredito as codigocartaocreditoextrato, \n");
        sql.append("ei.codigomovconta as codigomovcontaextrato, \n");
        sql.append("ei.* \n");
        sql.append("FROM extratodiarioitem ei \n");
        sql.append("LEFT JOIN cartaocredito cc on cc.codigo = ei.codigocartaocredito \n");
        sql.append("LEFT JOIN movpagamento mv on mv.codigo = ei.codigomovpagamento \n");
        sql.append("LEFT JOIN movconta mvc on mvc.codigo = ei.codigomovconta \n");
        sql.append("LEFT JOIN pessoa p on p.codigo = ei.pessoa \n");
        sql.append("LEFT JOIN cliente cl on cl.pessoa = p.codigo \n");
        sql.append("WHERE (ei.apresentarExtrato = true or ei.apresentarExtrato is null)  \n");
        sql.append("and ei.tipoconveniocobranca in (");
        sql.append(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo()).append(",");
        sql.append(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.getCodigo()).append(",");
        sql.append(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT.getCodigo()).append(",");
        sql.append(TipoConvenioCobrancaEnum.DCC_E_REDE.getCodigo()).append(",");
        sql.append(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE.getCodigo()).append(",");
        sql.append(TipoConvenioCobrancaEnum.DCC_FACILITEPAY.getCodigo()).append(") \n");
        sql.append("and coalesce(mv.codigo,0) = 0 \n");
        sql.append("and coalesce(cc.codigo,0) = 0 \n");
        sql.append("and coalesce(mvc.codigo,0) = 0 \n");
        if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao)) {
            sql.append(" AND ei.tipoconciliacao in (" + TipoConciliacaoEnum.CHARGEBACK.getCodigo()
                    + ", " + TipoConciliacaoEnum.TAXA_CANCELAMENTO.getCodigo()
                    + ", " + tipoConciliacao
                    + ", " + TipoConciliacaoEnum.ESTORNO_CHARGEBACK.getCodigo() + ")");
        } else {
            sql.append("AND ei.tipoconciliacao in (").append(tipoConciliacao + ", " + TipoConciliacaoEnum.CANCELAMENTO.getCodigo()).append(") \n");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND ei.empresa = ").append(empresa).append(" \n");
        }

        if (!UteisValidacao.emptyString(ro)) {
            Integer roInt = 0;
            try {
                roInt = Integer.valueOf(ro);
            } catch (Exception e) {
            }
            if (!UteisValidacao.emptyNumber(roInt)) {
                sql.append(" AND ((CAST(coalesce(ei.ro, '0') AS integer) = ").append(roInt).append(")) \n");
            }
        }

        if (!UteisValidacao.emptyString(autorizacao)) {
            sql.append("AND ei.autorizacao like '%").append(autorizacao).append("' \n");
        }
        if (!UteisValidacao.emptyString(nsu)) {
            sql.append(" AND ei.nsu ilike '").append(nsu).append("' \n");
        }

        if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append("AND ei.dataprevistapagamento::date BETWEEN '").append(Uteis.getDataFormatoBD(inicio)).append("' AND '").append(Uteis.getDataFormatoBD(fim)).append("' \n");
        }

        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append("AND ei.datalancamento::date BETWEEN '").append(Uteis.getDataFormatoBD(inicio)).append("' AND '").append(Uteis.getDataFormatoBD(fim)).append("' \n");
        }

        if (!UteisValidacao.emptyNumber(convenio)) {
            sql.append(" AND ei.conveniocobranca = ").append(convenio).append(" \n");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        return stm.executeQuery();
    }

    private ResultSet consultarExtratoConcilicaoMovConta(Date inicio, Date fim, String ro, String autorizacao, Integer tipoConciliacao,
                                                         Integer empresa, Integer convenio, String nsu) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct p.nome,ei.props,ei.tiporegistro, ei.estabelecimento, ei.tipoconciliacao,  ei.codigo, ei.datalancamento as lancei, \n");
        sql.append(" ei.dataprevistapagamento, ei.autorizacao as autorizacaoei, ei.nrtotalparcelas, \n");
        sql.append(" ei.nrcartao, ei.valorbruto, ei.valorliquido, ei.valorcomissao, \n");
        sql.append(" ei.codigomovpagamento as codigoMovPagamentoExtrato, ei.codigomovpagamento, ei.codigomovconta, ei.codigocartaocredito as codigoCartaoCreditoExtrato, 1 as nrparcelacartaocredito,\n");
        sql.append(" ei.situacao, ei.nrparcela, ei.taxa, ei.ro, ei.credito,ei.identificadorproduto, ei.nsu, ei.tipoarquivo, ei.estorno \n");
        sql.append(" ,pes2.nome as nomeAluno, cli.matricula, ei.tipoconveniocobranca \n");
        sql.append(" from extratodiarioitem  ei\n");
        sql.append(" LEFT JOIN movconta mc ON mc.codigo = ei.codigomovconta\n");
        sql.append(" LEFT JOIN pessoa p ON mc.pessoa = p.codigo\n");
        sql.append(" LEFT JOIN pessoa pes2 ON pes2.codigo = ei.pessoa \n");
        sql.append(" LEFT JOIN cliente cli ON cli.pessoa = pes2.codigo \n");
        if (!UteisValidacao.emptyNumber(empresa) || !UteisValidacao.emptyNumber(convenio)) {
            sql.append(" LEFT JOIN conveniocobranca cob ON cob.numerocontrato = ei.estabelecimento \n");
        }
        sql.append(" where 1 = 1 \n");
        sql.append(" AND apresentarExtrato = true \n");
        if (tipoConciliacao == 4) {
            sql.append(" and ei.tipoconciliacao in (6, " + tipoConciliacao + ") \n");
        } else {
            sql.append(" and ei.tipoconciliacao in (" + tipoConciliacao + ") \n");
        }
        sql.append(" AND (codigocartaocredito is null or codigocartaocredito = 0)\n");
        sql.append(" AND (codigomovpagamento is null or codigomovpagamento = 0)");
        if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND ei.dataprevistapagamento BETWEEN ? AND ? \n");
        }
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND ei.datalancamento BETWEEN ? AND ? \n");
        }
        if (!UteisValidacao.emptyString(ro)) {
            sql.append(" AND (CAST(coalesce(ei.ro, '0') AS integer) = ?) \n");
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            sql.append(" AND (ei.autorizacao like ?) \n");
        }
        if (!UteisValidacao.emptyString(nsu)) {
            sql.append(" AND (ei.nsu ilike '").append(nsu).append("') \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND ? IN (SELECT empresa FROM conveniocobrancaempresa WHERE conveniocobrancaempresa.conveniocobranca = cob.codigo ) \n");
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            sql.append(" AND cob.codigo = ? \n");
        }
        sql.append(" AND ei.empresa = ").append(empresa).append(" \n");
        //Rede débito não conciliado está exibindo. Adicionado isso para não impactar outros, até descobrir se esse problema não acontece com outros.
        sql.append(" AND ei.tipoconveniocobranca != ").append(TipoConvenioCobrancaEnum.DCC_E_REDE.getCodigo()).append(" \n");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 1;
        if (inicio != null && fim != null && tipoConciliacao != TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59")));
        }
        if (!UteisValidacao.emptyString(ro)) {
            Integer roInt = 0;
            try {
                roInt = Integer.valueOf(ro);
            } catch (Exception e) {
            }
            stm.setInt(i++, roInt);
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            stm.setString(i++, "%"+autorizacao);
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            stm.setInt(i++, empresa);
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            stm.setInt(i++, convenio);
        }
        return stm.executeQuery();
    }

    private ResultSet consultarExtratoConcilicaoEstornado(Date inicio, Date fim, String ro, String autorizacao,
                                                          Integer tipoConciliacao, Integer empresa, Integer convenio, String nsu) throws SQLException {
         StringBuilder sql = new StringBuilder();
        sql.append(" SELECT distinct 0 as codigoMovPagamento, ei.props,ei.tiporegistro, ei.estabelecimento,ei.tipoconciliacao,  ei.codigo, ei.datalancamento as lancei, \n");
        sql.append(" ei.autorizacao as autorizacaoei, ei.nrtotalparcelas, \n");
        sql.append(" ei.dataprevistapagamento, ei.autorizacao as autorizacaoei, \n");
        sql.append(" ei.nrcartao, ei.valorbruto, ei.ro, ei.valorliquido, ei.valorcomissao, \n");
        sql.append(" ei.codigomovpagamento as codigoMovPagamentoExtrato,  ei.codigomovconta, ei.codigocartaocredito as codigocartaocreditoextrato,  \n");
        sql.append(" ei.situacao, ei.nrparcela, ei.taxa,ei.credito,ei.identificadorproduto, ei.nsu, ei.tipoarquivo, ei.estorno \n");
        sql.append(" ,pes2.nome as nomeAluno, cli.matricula, ei.tipoconveniocobranca \n");
        sql.append(" FROM extratodiarioitem ei  \n");
        sql.append(" LEFT JOIN conveniocobranca cob ON cob.numerocontrato = ei.estabelecimento \n");
        sql.append(" LEFT JOIN pessoa pes2 ON pes2.codigo = ei.pessoa \n");
        sql.append(" LEFT JOIN cliente cli ON cli.pessoa = pes2.codigo \n");

        if (tipoConciliacao.equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) {
            sql.append(" WHERE ei.situacao in (6, " + tipoConciliacao + ") \n");
        } else {
            sql.append(" WHERE ei.situacao in (" + tipoConciliacao + ") \n");
        }
        sql.append(" AND apresentarExtrato = true \n");
        sql.append(" AND  (ei.tipoconciliacao = ? ) \n");
        if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND (ei.dataprevistapagamento BETWEEN ? AND ?  ) \n");
        }
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND (ei.datalancamento BETWEEN ? AND ? ) \n");
        }
        if (!UteisValidacao.emptyString(ro)) {
            sql.append(" AND ((CAST(coalesce(ei.ro, '0') AS integer) = ?)) \n");
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            sql.append(" AND (ei.autorizacao like ?) \n");
        }
        if (!UteisValidacao.emptyString(nsu)) {
            sql.append(" AND (ei.nsu ilike '").append(nsu).append("') \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND ? IN (SELECT empresa FROM conveniocobrancaempresa WHERE conveniocobrancaempresa.conveniocobranca = cob.codigo ) \n");
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            sql.append(" AND cob.codigo = ? \n");
        }
        int i = 1;
        sql.append(" ORDER BY situacao, autorizacaoei ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setInt(i++, SituacaoItemExtratoEnum.ESTORNADO_SISTEMA.ordinal());
        if (inicio != null && fim != null && tipoConciliacao != TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59")));
        }
        if (!UteisValidacao.emptyString(ro)) {
            Integer roInt = 0;
            try {
                roInt = Integer.valueOf(ro);
            } catch (Exception e) {
            }
            stm.setInt(i++, roInt);
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            stm.setString(i++, "%"+autorizacao);
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            stm.setInt(i++, empresa);
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            stm.setInt(i++, convenio);
        }
        return stm.executeQuery();
    }

    private ResultSet consultarExtratoCCMovimentadoNaoUtilizado(Date inicio, Date fim, String ro, String autorizacao, Integer tipoConciliacao,
                                                                Integer empresa, Integer convenio, Integer formaPagamento, Boolean apresentarPagamentosCancelados,
                                                                Integer operadoraCartao, Integer adquirente, String nsu, List<ExtratoDiarioItemVO> listaExtrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT mp.operadoracartao, mp.adquirente, ad.nome as nomeAdquirente ,ei.props,ei.tiporegistro, ei.estabelecimento, pes.codigo as codigopessoa, ei.tipoconciliacao,  fp.tipoformapagamento,fp.codigo as codigoformapagamento, fp.descricao as formapgto, mp.nomepagador, ei.codigo, ei.datalancamento as lancei, \n");
        sql.append(" ei.autorizacao as autorizacaoei, mp.autorizacaocartao as autorizacaomp, ei.nrtotalparcelas, mp.produtospagos, mp.movpagamentoorigemcredito, mp.recibopagamento, \n");
        sql.append(" mp.valor as valormp, mp.datalancamento as lancmp, mp.datapagamento as datapagamentomp, \n");
        sql.append(" coalesce(cc.valor,ccpag.valor) as valorcc, coalesce(cc.datacompesancao,ccpag.datacompesancao) as compensacaocc,mp.nrparcelacartaocredito, cc.nrparcela, \n");
        sql.append(" ei.dataprevistapagamento, ei.autorizacao as autorizacaoei, \n");
        sql.append(" ei.nrcartao, ei.valorbruto, ei.ro, ei.valorliquido, ei.valorcomissao, \n");
        sql.append(" ei.codigomovpagamento as codigoMovPagamentoExtrato, mp.codigo as codigomovpagamento,  ei.codigomovconta, ei.codigocartaocredito as codigocartaocreditoextrato, ccpag.codigo as codigocartaocredito, mp.movconta,  \n");
        sql.append(" ei.situacao, ei.nrparcela as nrParcelaei,  ei.taxa, coalesce(cc.nrparcela,ccpag.nrparcela) nrparcelacartao, case when length(coalesce(conta.descricaocurta,'')) > 1 then conta.descricaocurta else conta.descricao end as contamovimento, ei.credito,ei.identificadorproduto, ei.nsu, ei.tipoarquivo, ei.estorno \n");
        sql.append(" ,pes2.nome as nomeAluno, cli.matricula, ei.tipoconveniocobranca, ei.conveniocobranca, \n");
        sql.append(" coalesce(cc.composicao, ccpag.composicao ) as composicao, ei.antecipacao, ei.datapgtooriginalantesdaantecipacao, ei.pessoa, ei.valorDescontadoAntecipacao, ei.taxaCalculadaAntecipacao, \n");
        sql.append(" ei.alterouDataRecebimentoZWAutomaticamente, ei.dataPgtoOriginalZWAntesDaAlteracaoAutomatica  \n");
        sql.append(" FROM movpagamento mp \n");
        sql.append(" LEFT JOIN pessoa pes on pes.codigo = mp.pessoa \n");
        sql.append(" LEFT JOIN (select a.*, b.tipoconvenio from extratodiarioitem a left join conveniocobranca b on  a.conveniocobranca = b.codigo \n");

        if (tipoConciliacao.equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) {
            sql.append(" WHERE a.tipoconciliacao in (" + TipoConciliacaoEnum.CHARGEBACK.getCodigo() + ", " + TipoConciliacaoEnum.TAXA_CANCELAMENTO.getCodigo() + ", " + tipoConciliacao + ")");
        } else {
            sql.append(" WHERE a.tipoconciliacao in (" + tipoConciliacao + ", " + TipoConciliacaoEnum.CANCELAMENTO.getCodigo() + ")");
        }
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao)) {
            sql.append(" AND  (b.tipoconvenio <> 16 or ( b.tipoconvenio = 16 and a.tipoarquivo in ('EEVC', 'EEVD'))) \n");
        } else {
            sql.append(" AND  (b.tipoconvenio <> 16 or ( b.tipoconvenio = 16 and a.tipoarquivo in ('EEVC', 'EEVD', 'EEFI', 'API_REDE'))) \n");
        }
        sql.append(" ) ei ON mp.codigo = ei.codigomovpagamento \n");

        sql.append(" LEFT JOIN pessoa pes2 on pes2.codigo = ei.pessoa \n");
        sql.append(" LEFT JOIN cliente cli on cli.pessoa = pes2.codigo \n");
        sql.append(" LEFT JOIN cartaocredito cc ON cc.codigo = ei.codigocartaocredito \n");
        sql.append(" LEFT JOIN cartaocredito ccpag ON ccpag.movpagamento = mp.codigo and ccpag.codigo = cc.codigo \n");
        sql.append(" LEFT JOIN formapagamento fp ON fp.codigo = mp.formapagamento \n");
        sql.append(" LEFT JOIN movconta mc ON mc.codigo = mp.movconta \n");
        sql.append(" LEFT JOIN conta ON conta.codigo = mc.conta  \n");
        sql.append(" LEFT JOIN adquirente ad on ad.codigo=mp.adquirente \n");

        sql.append(" WHERE (apresentarExtrato = true or apresentarExtrato is null) \n");
        sql.append(" AND fp.tipoformapagamento in ('CA', 'CD') AND mp.valor > 0 \n");
        if (TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND ei.dataprevistapagamento BETWEEN ? AND ?  and (cc.codigo is null or cc.codigo = ccpag.codigo) \n");
        }
        if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao) && inicio != null && fim != null) {
            sql.append(" AND ei.datalancamento BETWEEN ? AND ? \n");
        }
        if (!UteisValidacao.emptyString(ro)) {
            sql.append(" AND ((CAST(coalesce(ei.ro, '0') AS integer) = ?)) \n");
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            sql.append(" AND (ei.autorizacao ilike ? or mp.autorizacaocartao ilike ?) \n");
        }
        if (!UteisValidacao.emptyString(nsu)) {
            sql.append(" AND (ei.nsu ilike '").append(nsu).append("' or mp.nsu ilike '").append(nsu).append("') \n");
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            sql.append(" AND ei.conveniocobranca = ? \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND mp.empresa = ? \n");
        }

        sql.append(" AND ((mp.movpagamentoorigemcredito is null) or (mp.movpagamentoorigemcredito is not null and valorbruto is not null)) \n");

        if (!UteisValidacao.emptyNumber(formaPagamento)) {
            sql.append(" AND mp.formapagamento = ? \n");
        }
        if (!apresentarPagamentosCancelados) {
            sql.append(" AND (coalesce(ccpag.situacao, '') NOT IN ('CA'))\n");
        }
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            sql.append("AND mp.operadoracartao = ? \n");
        }
        if (!UteisValidacao.emptyNumber(adquirente)) {
            sql.append(" and mp.adquirente= ").append(adquirente);
        }
        if(tipoConciliacao == TipoConciliacaoEnum.CHARGEBACK.getCodigo()){
            sql.append(" and ei.estorno = true");
        }

        //Só consultar itens que ainda não estão na lista, que não foram adicionado nas consultas anteriores
        if (!UteisValidacao.emptyList(listaExtrato)) {
            sql.append("AND ei.codigo NOT IN (");
            boolean primeiro = true;
            for (ExtratoDiarioItemVO extratoDiarioItemVO: listaExtrato){
                if (primeiro) {
                    sql.append(extratoDiarioItemVO.getCodigo());
                    primeiro = false;
                } else {
                    sql.append(", ").append(extratoDiarioItemVO.getCodigo());
                }
            }
            sql.append(") \n");
        }

        int i = 1;
        sql.append("ORDER BY situacao, mp.nomepagador, autorizacao;");
        PreparedStatement stm = con.prepareStatement(sql.toString());

        if (inicio != null && fim != null && tipoConciliacao != TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) {
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
        }

        if (!UteisValidacao.emptyString(ro)) {
            Integer roInt = 0;
            try {
                roInt = Integer.valueOf(ro);
            } catch (Exception e) {
            }
            stm.setInt(i++, roInt);
        }
        if (!UteisValidacao.emptyString(autorizacao)) {
            stm.setString(i++, "%" + autorizacao);
            stm.setString(i++, "%" + autorizacao);
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            stm.setInt(i++, convenio);
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            stm.setInt(i++, empresa);
        }
        if (!UteisValidacao.emptyNumber(formaPagamento)) {
            stm.setInt(i++, formaPagamento);
        }
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            stm.setInt(i++, operadoraCartao);
        }

        return stm.executeQuery();
    }

    public ResultSet consultarMovPagamentoSemExtratoDiario(Date inicio, Date fim, String ro, String autorizacao, Integer tipoConciliacao,
                                                               Integer empresa, Integer convenio, Integer formaPagamento, Boolean apresentarPagamentosCancelados,
                                                               Integer operadoraCartao, Integer adquirente, String nsu) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT mp.operadoracartao, mp.adquirente, ad.nome as nomeAdquirente , pes.codigo as codigopessoa, \n");
        sql.append(" mp.autorizacaocartao as autorizacaomp, mp.produtospagos, mp.movpagamentoorigemcredito, mp.recibopagamento, \n");
        sql.append(" mp.valor as valormp, mp.datalancamento as lancmp, mp.datapagamento as datapagamentomp, \n");
        sql.append(" ccpag.valor as valorcc, ccpag.datacompesancao as compensacaocc, mp.nrparcelacartaocredito, ccpag.nrparcela, \n");
        sql.append(" fp.tipoformapagamento,fp.codigo as codigoformapagamento, fp.descricao as formapgto, mp.nomepagador, \n");
        sql.append(" mp.codigo as codigomovpagamento, ccpag.codigo as codigocartaocredito, mp.movconta,  \n");
        sql.append(" ccpag.nrparcela as nrparcelacartao, case when length(coalesce(conta.descricaocurta,'')) > 1 then conta.descricaocurta else conta.descricao end as contamovimento, \n");
        sql.append(" pes.nome as nomeAluno, cli.matricula, \n");
        sql.append(" ccpag.composicao, ei.tipoconciliacao, ei.props, ei.tiporegistro, ei.estabelecimento, ei.codigo, ei.datalancamento as lancei, \n");
        sql.append(" ei.nrtotalparcelas, ei.dataprevistapagamento, ei.situacao, ei.nrparcela as nrParcelaei,  ei.taxa,   \n");
        sql.append(" ei.nrcartao, ei.valorbruto, ei.ro, ei.valorliquido, ei.valorcomissao, ei.autorizacao as autorizacaoei, \n");
        sql.append(" ei.codigomovpagamento as codigoMovPagamentoExtrato, ei.codigomovconta, ei.codigocartaocredito as codigocartaocreditoextrato, \n");
        sql.append(" ei.antecipacao, ei.datapgtooriginalantesdaantecipacao, ei.pessoa, ei.valorDescontadoAntecipacao, ei.taxaCalculadaAntecipacao, \n");
        sql.append(" ei.tipoconveniocobranca, ei.conveniocobranca, ei.credito,ei.identificadorproduto, ei.nsu, ei.tipoarquivo, ei.estorno, \n");
        sql.append(" ei.alterouDataRecebimentoZWAutomaticamente, ei.dataPgtoOriginalZWAntesDaAlteracaoAutomatica \n");
        sql.append(" FROM movpagamento mp \n");
        sql.append(" LEFT JOIN extratodiarioitem ei on ei.codigomovpagamento = mp.codigo \n");
        sql.append(" LEFT JOIN pessoa pes on pes.codigo = mp.pessoa \n");
        sql.append(" LEFT JOIN cliente cli on cli.pessoa = pes.codigo \n");
        sql.append(" LEFT JOIN cartaocredito ccpag ON ccpag.movpagamento = mp.codigo \n");
        sql.append(" LEFT JOIN formapagamento fp ON fp.codigo = mp.formapagamento \n");
        sql.append(" LEFT JOIN movconta mc ON mc.codigo = mp.movconta \n");
        sql.append(" LEFT JOIN conta ON conta.codigo = mc.conta  \n");
        sql.append(" LEFT JOIN adquirente ad on ad.codigo=mp.adquirente \n");
        sql.append(" WHERE fp.tipoformapagamento in ('CA', 'CD') AND mp.valor > 0 \n");

        if(inicio != null && fim != null && tipoConciliacao != TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) {
            sql.append(" AND ((mp.dataPagamento BETWEEN '" + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)) + "'")
                    .append("AND '" + Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)) + "'")
                    .append("AND fp.tipoformapagamento  = 'CD') \n");
            sql.append(" OR (ccpag.datacompesancao BETWEEN '" + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)) + "'")
                    .append("AND '" + Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)) + "'")
                    .append("AND fp.tipoformapagamento  = 'CA')) \n");
        }

        if (!UteisValidacao.emptyString(autorizacao)) {
            sql.append(" AND mp.autorizacaocartao ilike '").append("%"+ autorizacao).append("' \n");
        }
        if (!UteisValidacao.emptyString(nsu)) {
            sql.append(" AND (mp.nsu ilike '").append(nsu).append("') \n");
        }
        if (!UteisValidacao.emptyNumber(convenio)) {
            sql.append(" AND mp.conveniocobranca = ").append(convenio).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND mp.empresa = ").append(empresa).append(" \n");
        }

        sql.append(" AND (mp.recibopagamento is not null or (mp.recibopagamento is null and coalesce(mp.valor, 0) < 0))\n"); //valor negativo para cancelamentos
        sql.append(" AND ((mp.movpagamentoorigemcredito is null) or (mp.movpagamentoorigemcredito is not null and mp.valor is not null)) \n");

        if (!UteisValidacao.emptyNumber(formaPagamento)) {
            sql.append(" AND mp.formapagamento = ").append(formaPagamento).append(" \n");
        }
        if (!apresentarPagamentosCancelados) {
            sql.append(" AND (coalesce(ccpag.situacao, '') NOT IN ('CA'))\n");
        }
        sql.append(" AND ((mp.movpagamentoorigemcredito IS NULL AND mp.produtospagos NOT LIKE '%CC%') or (mp.movpagamentoorigemcredito IS not null and coalesce(mp.valor, 0) < 0))").append(" \n"); //valor negativo para cancelamentos
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            sql.append("AND mp.operadoracartao = ").append(operadoraCartao).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(adquirente)) {
            sql.append(" and mp.adquirente = ").append(adquirente).append(" \n");
        }
        int i = 1;
        sql.append(" ORDER BY mp.nomepagador, mp.autorizacaocartao;");
        PreparedStatement stm = con.prepareStatement(sql.toString());

        return stm.executeQuery();
    }

    public List<ExtratoDiarioItemVO> consultarTodosItens() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM extratodiarioitem ei ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs =  stm.executeQuery();
        return montarDadosConsulta(rs, con);
    }
    
    public List<ExtratoDiarioItemVO> consultarItensSql(String consulta) throws Exception {
        PreparedStatement stm = con.prepareStatement(consulta);
        ResultSet rs =  stm.executeQuery();
        return montarDadosConsulta(rs, con);
    }
    
    @Override
    public ExtratoDiarioItemVO consultarPorChavePrimaria(final Integer codigo) throws Exception {
        PreparedStatement stm = con.prepareStatement("select * from extratodiarioitem where codigo = "+codigo);
        ResultSet rs =  stm.executeQuery();
        if(rs.next()){
            return montarDados(rs, con);
        } else {
            return new ExtratoDiarioItemVO();
        }
    }

    public List<ExtratoDiarioItemVO> consultarExtratosRedeProcessarPagamento(String estabelecimento, String ro, String tipoRegistro, String tipoArquivo, Integer nrParcela, Integer nrTotalParcelas, Integer convenioCobranca) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from extratodiarioitem \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyString(estabelecimento)) {
            sql.append("and estabelecimento = '").append(estabelecimento).append("' \n");
        }
        if (!UteisValidacao.emptyString(ro)) {
            sql.append("and ro = '").append(ro).append("' \n");
        }
        if (!UteisValidacao.emptyString(tipoRegistro)) {
            sql.append("and tipoRegistro = '").append(tipoRegistro).append("' \n");
        }
        if (!UteisValidacao.emptyString(tipoArquivo)) {
            sql.append("and tipoArquivo = '").append(tipoArquivo).append("' \n");
        }
        if (!UteisValidacao.emptyNumber(nrParcela)) {
            sql.append("and nrParcela = ").append(nrParcela).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(nrTotalParcelas)) {
            sql.append("and nrtotalparcelas = ").append(nrTotalParcelas).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(convenioCobranca)) {
            sql.append("and conveniocobranca = ").append(convenioCobranca).append(" \n");
        }
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs =  stm.executeQuery();
        return montarDadosConsulta(rs, con);
    }

    @Override
    public List<ExtratoDiarioItemVO> consultarExtratosRedeVenda(String ro) throws Exception {
        List<ExtratoDiarioItemVO> lista = new ArrayList<>();
        try {
            if (!UteisValidacao.emptyString(ro)) {
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT * \n");
                sql.append("FROM extratodiarioitem \n");
                sql.append("WHERE 1 = 1 \n");
                sql.append("AND tipoconciliacao in (").append(TipoConciliacaoEnum.VENDAS.getCodigo()).append(",").append(TipoConciliacaoEnum.CANCELAMENTO.getCodigo()).append(") \n");
                sql.append("AND ro = '").append(ro).append("';");

                PreparedStatement stm = con.prepareStatement(sql.toString());
                ResultSet rs =  stm.executeQuery();
                lista = montarDadosConsulta(rs, con);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return lista;
        }
        return lista;
    }

    private ExtratoDiarioItemVO consultarExtratoGenerico(String estabelecimento, String ro, String tipoRegistro, String tipoArquivo, Integer nrParcela, Integer nrTotalParcelas, Integer convenioCobranca) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from extratodiarioitem \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyString(estabelecimento)) {
            sql.append("and estabelecimento = '").append(estabelecimento).append("' \n");
        }
        if (!UteisValidacao.emptyString(ro)) {
            sql.append("and ro = '").append(ro).append("' \n");
        }
        if (!UteisValidacao.emptyString(tipoRegistro)) {
            sql.append("and tipoRegistro = '").append(tipoRegistro).append("' \n");
        }
        if (!UteisValidacao.emptyString(tipoArquivo)) {
            sql.append("and tipoArquivo = '").append(tipoArquivo).append("' \n");
        }
        if (!UteisValidacao.emptyNumber(nrParcela)) {
            sql.append("and nrParcela = ").append(nrParcela).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(nrTotalParcelas)) {
            sql.append("and nrtotalparcelas = ").append(nrTotalParcelas).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(convenioCobranca)) {
            sql.append("and conveniocobranca = ").append(convenioCobranca).append(" \n");
        }
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs =  stm.executeQuery();
        if(rs.next()){
            return montarDados(rs, con);
        } else {
            return new ExtratoDiarioItemVO();
        }
    }
    
    public void alterarSituacao(ExtratoDiarioItemVO obj) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getCodigo())) {
            return;
        }
        PreparedStatement stm = con.prepareStatement("UPDATE extratodiarioitem set situacao = ?, codigocartaocredito = ?, codigomovpagamento = ? WHERE codigo = ?");
        int i = 1;
        stm.setInt(i++, obj.getSituacao().ordinal());
        stm.setInt(i++, obj.getCodigoCartaoCredito() == null ? 0 : obj.getCodigoCartaoCredito());
        stm.setInt(i++, obj.getCodigoMovPagamento() == null ? 0 : obj.getCodigoMovPagamento());
        stm.setInt(i++, obj.getCodigo());
        stm.execute();
    }

    public void estornarRecibo(ExtratoDiarioItemVO item) throws Exception {
        if (!UteisValidacao.emptyNumber(item.getCodigoMovPagamento())) {

            MovPagamento movPagamentoDAO = new MovPagamento(con);
            ReciboPagamento reciboDAO = new ReciboPagamento(con);
            PagamentoMovParcela pagamentoMovParcelaDAO = new PagamentoMovParcela(con);
            MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);

            EstornoReciboControle estorno = new EstornoReciboControle();
            EstornoReciboVO estornoVO = new EstornoReciboVO();

            List<MovPagamentoVO> listaMovPagamento = new ArrayList<MovPagamentoVO>();
            MovPagamentoVO movPagamentoVO = movPagamentoDAO.consultarPorChavePrimaria(item.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_TODOS);
            listaMovPagamento.add(movPagamentoVO);

            estornoVO.setListaMovPagamento(listaMovPagamento);

            List<PagamentoMovParcelaVO> listaPagamentoMovParcela = pagamentoMovParcelaDAO.consultarPagamentoMovParcelas(movPagamentoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            List<MovParcelaVO> listaParcelas = new ArrayList<MovParcelaVO>();
            for (PagamentoMovParcelaVO parcelaVO : listaPagamentoMovParcela) {
                listaParcelas.add(parcelaVO.getMovParcela());
            }

            estornoVO.setListaMovParcela(listaParcelas);
            estornoVO.setEstornarOperadora(false);
            estornoVO.setReciboPagamentoVO(reciboDAO.consultarPorChavePrimaria(movPagamentoVO.getReciboPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

            estorno.setEstornoReciboVO(estornoVO);

            reciboDAO.estornarReciboPagamento(estornoVO, movPagamentoDAO, movProdutoParcelaDAO, null, null);

            movPagamentoDAO = null;
            reciboDAO = null;
            pagamentoMovParcelaDAO = null;
            movProdutoParcelaDAO = null;
        }
    }

    public void preencherParcelasItem(ExtratoDiarioItemVO extratoDiarioItemVO) throws Exception {
        try {
            extratoDiarioItemVO.setListaParcelas(new ArrayList<MovParcelaVO>());
            if (!UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
                PagamentoMovParcela pagamentoMovParcelaDAO = new PagamentoMovParcela(con);
                List<PagamentoMovParcelaVO> list = pagamentoMovParcelaDAO.consultarPagamentoMovParcelas(extratoDiarioItemVO.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_TODOS);
                for (PagamentoMovParcelaVO pag : list) {
                    extratoDiarioItemVO.setReciboPagamentoVO(pag.getReciboPagamento());
                    extratoDiarioItemVO.getListaParcelas().add(pag.getMovParcela());
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public int consultarCodigoPor(String autorizacao, Date dataPrevisao, String nsu, Integer tipoConciliacao, Integer nrParcela, Integer empresa) throws Exception {
        PreparedStatement stm = con.prepareStatement("select codigo from extratodiarioitem where autorizacao like '"+autorizacao+
                "' and dataprevistapagamento ='"+ Uteis.getDataFormatoBD(dataPrevisao)+"' and nsu like '"+nsu+"' and tipoconciliacao = "+tipoConciliacao+
                " and nrparcela = "+nrParcela+" and empresa = "+empresa);
        ResultSet rs =  stm.executeQuery();
        if(rs.next()){
            return rs.getInt("codigo");
        } else {
            return 0;
        }
    }

    public int consultarCodigoPor2(String autorizacao, Date dataPrevisao, String nsu, Integer tipoConciliacao) throws Exception {
        StringBuilder sql = new StringBuilder("select codigo\n")
                .append("from extratodiarioitem\n")
                .append("where dataprevistapagamento = ").append("'" + Uteis.getDataFormatoBD(dataPrevisao) + "'\n")
                .append("and nsu ilike ").append("'").append(nsu).append("'\n")
                .append("and tipoconciliacao = ").append(tipoConciliacao).append("\n");

        if (UteisValidacao.emptyString(autorizacao)) {
            sql.append("and autorizacao is null");
        } else {
            sql.append("and autorizacao ilike ").append("'" + autorizacao + "'");
//            sqlDebito.append(" and (autorizacaocartao = '").append(item.getAutorizacao()).append("' or TRIM(LEADING '0' FROM CAST (autorizacaocartao AS TEXT)) = '").append(Integer.valueOf(item.getAutorizacaoInt())).append("') ");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());

//        PreparedStatement stm = con.prepareStatement("select codigo from extratodiarioitem where autorizacao like '"+autorizacao+
//                "' and dataprevistapagamento ='"+ Uteis.getDataFormatoBD(dataPrevisao)+"' and nsu like '"+nsu+"' and tipoconciliacao = "+tipoConciliacao);
        ResultSet rs =  stm.executeQuery();
        if(rs.next()){
            return rs.getInt("codigo");
        } else {
            return 0;
        }
    }

    @Override
    public String consultarAutorizacaoPorNSU(String nsu) throws Exception {
        PreparedStatement stm = con.prepareStatement("select autorizacao from extratodiarioitem where  NSU like '"+nsu+"'");
        ResultSet rs =  stm.executeQuery();
        if(rs.next()){
            return rs.getString("autorizacao");
        } else {
            return "";
        }
    }

    @Override
    public Integer consultarMovPagamento(String autorizacao, String nsu) throws Exception {
        PreparedStatement stm = con.prepareStatement("select codigomovpagamento from extratodiarioitem where  NSU like '"+nsu+"' AND autorizacao like '"+autorizacao+"' ");
        ResultSet rs =  stm.executeQuery();
        if(rs.next()){
            return rs.getInt("codigomovpagamento");
        } else {
            return 0;
        }
    }

    public void incluirExtratoArquivo(String arquivo, String nomeArquivo, ConvenioCobrancaVO convenioCobrancaVO) {
        try {
            String sql = "INSERT INTO extratoarquivo(dataregistro, conveniocobranca, tipoconvenio, arquivo, nomearquivo) VALUES (?, ?, ?, ?, ?);";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                sqlInserir.setInt(2, convenioCobrancaVO.getCodigo());
                sqlInserir.setInt(3, convenioCobrancaVO.getTipo().getCodigo());
                sqlInserir.setString(4, arquivo);
                sqlInserir.setString(5, nomeArquivo);
                sqlInserir.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void preencherMovPagamento(ExtratoDiarioItemVO obj) throws Exception {
        if (!UteisValidacao.emptyString(obj.getAutorizacao())) {
            consultarMovPagamentoNrAutorizacao(obj);
        } else {
            Uteis.logarDebug("Não tem autorizaçao movpagamento NSU: " + obj.getNsu());
        }
        if (UteisValidacao.emptyNumber(obj.getCodigoMovPagamento()) && !UteisValidacao.emptyString(obj.getNsu())) {
            consultarMovPagamentoNSU(obj);
        }
    }

    private void consultarMovPagamentoNrAutorizacao(ExtratoDiarioItemVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("mp.codigo as movpagamento,  \n");
        sql.append("mp.autorizacaocartao, \n");
        sql.append("mp.nsu \n");
        sql.append("from movpagamento mp \n");
        sql.append("where mp.datalancamento::date = '").append(Uteis.getDataFormatoBD(obj.getDataLancamento())).append("' \n");
        sql.append("and mp.autorizacaocartao ilike '").append(obj.getAutorizacao()).append("' \n");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs =  stm.executeQuery()) {
                if(rs.next()) {
                    Integer movPagamento = rs.getInt("movpagamento");
                    String autorizacaocartao = rs.getString("autorizacaocartao");
                    String nsu = rs.getString("nsu");
                    obj.setCodigoMovPagamento(movPagamento);

                    if (UteisValidacao.emptyString(obj.getAutorizacao()) && !UteisValidacao.emptyString(autorizacaocartao)) {
                        obj.setAutorizacao(autorizacaocartao);
                    }
                    if (UteisValidacao.emptyString(obj.getNsu()) && !UteisValidacao.emptyString(nsu)) {
                        obj.setNsu(nsu);
                    }
                }
            }
        }
    }

    private void consultarMovPagamentoNSU(ExtratoDiarioItemVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("mp.codigo as movpagamento,  \n");
        sql.append("mp.autorizacaocartao, \n");
        sql.append("mp.nsu \n");
        sql.append("from movpagamento mp \n");
        sql.append("where mp.datalancamento::date = '").append(Uteis.getDataFormatoBD(obj.getDataLancamento())).append("' \n");
        sql.append("and mp.nsu ilike '").append(obj.getNsu()).append("' \n");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs =  stm.executeQuery()) {
                if(rs.next()) {
                    Integer movPagamento = rs.getInt("movpagamento");
                    String autorizacaocartao = rs.getString("autorizacaocartao");
                    String nsu = rs.getString("nsu");
                    obj.setCodigoMovPagamento(movPagamento);

                    if (UteisValidacao.emptyString(obj.getAutorizacao()) && !UteisValidacao.emptyString(autorizacaocartao)) {
                        obj.setAutorizacao(autorizacaocartao);
                    }
                    if (UteisValidacao.emptyString(obj.getNsu()) && !UteisValidacao.emptyString(nsu)) {
                        obj.setNsu(nsu);
                    }
                }
            }
        }
    }

    public void preencherCartaoCredito(ExtratoDiarioItemVO obj, boolean isConciliarSemNumeroParcela) throws Exception {
        if (UteisValidacao.emptyString(obj.getAutorizacao()) && !UteisValidacao.emptyString(obj.getNsu())) {
            consultarNrAutorizacaoPeloNSU(obj);
        }
        if (!UteisValidacao.emptyString(obj.getAutorizacao())) {
            consultarCartaoCreditoNrAutorizacao(obj);
        }
        if (UteisValidacao.emptyNumber(obj.getCodigoCartaoCredito()) && !UteisValidacao.emptyString(obj.getNsu())) {
            consultarCartaoCreditoNSU(obj);
        }
        if (isConciliarSemNumeroParcela
                && UteisValidacao.emptyNumber(obj.getCodigoCartaoCredito())
                && (obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) ||
                obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT) ||
                obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5))
                && !UteisValidacao.emptyNumber(obj.getCodigoMovPagamento())
                && obj.getDataPrevistaPagamento() != null
        ) {
            consultarCartaoCreditoSemNumeroParcela(obj);
        }
    }

    private void consultarCartaoCreditoSemNumeroParcela(ExtratoDiarioItemVO obj) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo \n");
        sql.append("FROM cartaocredito \n");
        sql.append("WHERE movpagamento = ").append(obj.getCodigoMovPagamento()).append(" \n");
        sql.append("ORDER BY codigo DESC \n");
        sql.append("LIMIT 1; \n");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs =  stm.executeQuery()) {
                if(rs.next()){
                    obj.setCodigoCartaoCredito(rs.getInt("codigo"));
                }
            }
        }
    }

    private void consultarNrAutorizacaoPeloNSU(ExtratoDiarioItemVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("e.autorizacao \n");
        sql.append("from extratodiarioitem e \n");
        sql.append("where coalesce(e.autorizacao, '') <> '' \n");
        sql.append("and e.estabelecimento  = '").append(obj.getEstabelecimento()).append("' \n");
        sql.append("and e.nsu ilike '").append(obj.getNsu()).append("' \n");
        sql.append("and e.datalancamento::date = '").append(Uteis.getDataFormatoBD(obj.getDataLancamento())).append("' \n");
        sql.append("limit 1\n");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs =  stm.executeQuery()) {
                if(rs.next()){
                    obj.setAutorizacao(rs.getString("autorizacao"));
                }
            }
        }
    }

    private void consultarCartaoCreditoNrAutorizacao(ExtratoDiarioItemVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cc.codigo as cartaocredito, \n");
        sql.append("mp.codigo as movpagamento, \n");
        sql.append("mp.autorizacaocartao, \n");
        sql.append("mp.nsu \n");
        sql.append("from movpagamento mp \n");
        sql.append("inner join cartaocredito cc on cc.movpagamento = mp.codigo \n");
        sql.append("where mp.datalancamento::date = '").append(Uteis.getDataFormatoBD(obj.getDataLancamento())).append("' \n");
        sql.append("and mp.autorizacaocartao ilike '").append(obj.getAutorizacao()).append("' \n");
        if (!UteisValidacao.emptyNumber(obj.getNrParcela())) {
            sql.append("and cc.nrparcela = ").append(obj.getNrParcela()).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(obj.getCodigoMovPagamento())) {
            sql.append("and mp.codigo = ").append(obj.getCodigoMovPagamento()).append(" \n");
        }

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs =  stm.executeQuery()) {
                if(rs.next()){
                    Integer cartaocredito = rs.getInt("cartaocredito");
                    Integer movpagamento = rs.getInt("movpagamento");
                    String autorizacaocartao = rs.getString("autorizacaocartao");
                    String nsu = rs.getString("nsu");
                    obj.setCodigoCartaoCredito(cartaocredito);
                    obj.setCodigoMovPagamento(movpagamento);

                    if (UteisValidacao.emptyString(obj.getAutorizacao()) && !UteisValidacao.emptyString(autorizacaocartao)) {
                        obj.setAutorizacao(autorizacaocartao);
                    }
                    if (UteisValidacao.emptyString(obj.getNsu()) && !UteisValidacao.emptyString(nsu)) {
                        obj.setNsu(nsu);
                    }
                }
            }
        }
    }

    private void consultarCartaoCreditoNSU(ExtratoDiarioItemVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cc.codigo as cartaocredito, \n");
        sql.append("mp.codigo as movpagamento, \n");
        sql.append("mp.autorizacaocartao, \n");
        sql.append("mp.nsu \n");
        sql.append("from movpagamento mp \n");
        sql.append("inner join cartaocredito cc on cc.movpagamento = mp.codigo \n");
        if (obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            //-10+10dias em relação à data de lançamento
            sql.append("WHERE mp.datalancamento::DATE BETWEEN '")
                    .append(Uteis.getDataFormatoBD(obj.getDataLancamento())).append("'::DATE - INTERVAL '10 days' ")
                    .append("AND '").append(Uteis.getDataFormatoBD(obj.getDataLancamento())).append("'::DATE + INTERVAL '10 days' \n");

        } else {
            sql.append("where mp.datalancamento::date = '").append(Uteis.getDataFormatoBD(obj.getDataLancamento())).append("' \n");
        }
        sql.append("and mp.nsu ilike '").append(obj.getNsu()).append("' \n");
        if (!UteisValidacao.emptyNumber(obj.getNrParcela())) {
            sql.append("and cc.nrparcela = ").append(obj.getNrParcela()).append(" \n");
        }

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs =  stm.executeQuery()) {
                if(rs.next()){
                    Integer cartaocredito = rs.getInt("cartaocredito");
                    Integer movpagamento = rs.getInt("movpagamento");
                    String autorizacaocartao = rs.getString("autorizacaocartao");
                    String nsu = rs.getString("nsu");
                    obj.setCodigoCartaoCredito(cartaocredito);
                    obj.setCodigoMovPagamento(movpagamento);

                    if (UteisValidacao.emptyString(obj.getAutorizacao()) && !UteisValidacao.emptyString(autorizacaocartao)) {
                        obj.setAutorizacao(autorizacaocartao);
                    }
                    if (UteisValidacao.emptyString(obj.getNsu()) && !UteisValidacao.emptyString(nsu)) {
                        obj.setNsu(nsu);
                    }
                }
            }
        }
    }

    public void preencherCartaoDebito(ExtratoDiarioItemVO obj) throws Exception {
        if (UteisValidacao.emptyString(obj.getAutorizacao()) && !UteisValidacao.emptyString(obj.getNsu())) {
            consultarNrAutorizacaoPeloNSU(obj);
        }

        if (!UteisValidacao.emptyString(obj.getAutorizacao())) {
            consultarCartaoDebitoNrAutorizacao(obj);
        }
        if (UteisValidacao.emptyNumber(obj.getCodigoCartaoCredito()) && !UteisValidacao.emptyString(obj.getNsu())) {
            consultarCartaoDebitoNSU(obj);
        }
    }

    private void consultarCartaoDebitoNrAutorizacao(ExtratoDiarioItemVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("mp.codigo as movpagamento, \n");
        sql.append("mp.autorizacaocartao, \n");
        sql.append("mp.nsu \n");
        sql.append("from movpagamento mp \n");
        sql.append("where mp.datalancamento::date = '").append(Uteis.getDataFormatoBD(obj.getDataLancamento())).append("' \n");
        sql.append("and mp.autorizacaocartao ilike '").append(obj.getAutorizacao()).append("' \n");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs =  stm.executeQuery()) {
                if(rs.next()){
                    Integer movpagamento = rs.getInt("movpagamento");
                    String autorizacaocartao = rs.getString("autorizacaocartao");
                    String nsu = rs.getString("nsu");
                    obj.setCodigoMovPagamento(movpagamento);

                    if (UteisValidacao.emptyString(obj.getAutorizacao()) && !UteisValidacao.emptyString(autorizacaocartao)) {
                        obj.setAutorizacao(autorizacaocartao);
                    }
                    if (UteisValidacao.emptyString(obj.getNsu()) && !UteisValidacao.emptyString(nsu)) {
                        obj.setNsu(nsu);
                    }
                }
            }
        }
    }

    private void consultarCartaoDebitoNSU(ExtratoDiarioItemVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("mp.codigo as movpagamento, \n");
        sql.append("mp.autorizacaocartao, \n");
        sql.append("mp.nsu \n");
        sql.append("from movpagamento mp \n");
        sql.append("where mp.datalancamento::date = '").append(Uteis.getDataFormatoBD(obj.getDataLancamento())).append("' \n");
        sql.append("and mp.nsu ilike '").append(obj.getNsu()).append("' \n");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs =  stm.executeQuery()) {
                if(rs.next()){
                    Integer movpagamento = rs.getInt("movpagamento");
                    String autorizacaocartao = rs.getString("autorizacaocartao");
                    String nsu = rs.getString("nsu");
                    obj.setCodigoMovPagamento(movpagamento);

                    if (UteisValidacao.emptyString(obj.getAutorizacao()) && !UteisValidacao.emptyString(autorizacaocartao)) {
                        obj.setAutorizacao(autorizacaocartao);
                    }
                    if (UteisValidacao.emptyString(obj.getNsu()) && !UteisValidacao.emptyString(nsu)) {
                        obj.setNsu(nsu);
                    }
                }
            }
        }
    }

    public void verificarCreditoDebitoExtrato(ExtratoDiarioItemVO obj) throws Exception {
        if (!UteisValidacao.emptyString(obj.getNsu())) {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("mp.codigo as movpagamento, \n");
            sql.append("(fp.tipoformapagamento = 'CA') as credito, \n");
            sql.append("split_part(split_part(t.paramsenvio, '<PAN>', 2), '</PAN>', 1) as nrcartao, \n");
            sql.append("mp.nrparcelacartaocredito as nrtotalparcelas \n");
            sql.append("from movpagamento mp  \n");
            sql.append("inner join formapagamento fp on fp.codigo = mp.formapagamento  \n");
            sql.append("left join transacao t on t.movpagamento  = mp.codigo  \n");
            sql.append("where mp.nsu ilike '").append(obj.getNsu()).append("' \n");
            try (PreparedStatement stm1 = con.prepareStatement(sql.toString())) {
                try (ResultSet rs2 = stm1.executeQuery()) {
                    if (rs2.next()) {
                        obj.setCredito(rs2.getBoolean("credito"));
                        obj.setNrCartao(rs2.getString("nrcartao"));
                        obj.setNrTotalParcelas(rs2.getInt("nrtotalparcelas"));
                        if (UteisValidacao.emptyNumber(obj.getCodigoMovPagamento())) {
                            obj.setCodigoMovPagamento(rs2.getInt("movpagamento"));
                        }
                    }
                }
                if (obj.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo()) && !obj.getCredito()) {
                    //se não for crédito, para itens de PAGAMENTOS, mesmo assim verificar se é crédito pelo extrato de VENDA, pois pode ser que não tenha sido lançado no sistema e ficaria como débito pelo sql anterior.
                    if (!UteisValidacao.emptyString(obj.getNsu())) {
                        String sql2 = "select credito, nrcartao ,nrtotalparcelas from extratodiarioitem e where tipoconciliacao = " + TipoConciliacaoEnum.VENDAS.getCodigo() + " and nsu = '" + obj.getNsu() + "' limit 1 \n";
                        try (PreparedStatement stm2 = con.prepareStatement(sql2.toString())) {
                            try (ResultSet rs = stm2.executeQuery()) {
                                if (rs.next()) {
                                    obj.setCredito(rs.getBoolean("credito"));
                                    obj.setNrCartao(rs.getString("nrcartao"));
                                    obj.setNrTotalParcelas(rs.getInt("nrtotalparcelas"));
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public Integer pessoaExtrato(ExtratoDiarioItemVO extratoDiarioItem) throws Exception{
        if(extratoDiarioItem.getMovPagamento() != null){
             return extratoDiarioItem.getMovPagamento().getPessoa().getCodigo();
        }

        return extratoDiarioItem.getCartao().getCodigoPessoa();

    }

    public static void montarDadosExtratoContaCorente(ResultSet rs, ExtratoDiarioItemVO item, Integer tipoConciliacaoFiltro) throws Exception{
        item.setProps(negocio.comuns.utilitarias.Uteis.obterMapFromString(rs.getString("props")));
        item.setTipoRegistro(rs.getString("tiporegistro"));
        item.setEstabelecimento(rs.getString("estabelecimento"));

        Integer tipoConciliacao = rs.getInt("tipoconciliacao");
        if (UteisValidacao.emptyNumber(tipoConciliacao) && tipoConciliacaoFiltro != null) {
            tipoConciliacao = tipoConciliacaoFiltro;
        }

        item.setTipoConciliacao(tipoConciliacao);
        item.setCodigo(rs.getInt("codigo"));
        item.setDataLancamento(rs.getDate("lancei"));
        item.setNrTotalParcelas(rs.getInt("nrtotalparcelas"));
        item.setDataPrevistaPagamento(rs.getDate("dataprevistapagamento"));
        item.setAutorizacao(rs.getString("autorizacaoei"));
        item.setNrCartao(rs.getString("nrcartao"));
        try {
            /*
             * Tipos de Parcelamento - InstallmentType
             * 1 - A Vista lojista
             * 2 - Parcelado lojista
             * 3 - Parcelado emissor
             * */
            item.setTipoParcelamento(rs.getInt("tipoparcelamento"));
        } catch (Exception ignore) {
        }
        item.setValorBruto(
                (
                        (rs.getInt("tipoconveniocobranca") == TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo() ||
                                rs.getInt("tipoconveniocobranca") == TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.getCodigo() ||
                                rs.getInt("tipoconveniocobranca") == TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT.getCodigo())
                        && TipoConciliacaoEnum.VENDAS.getCodigo().equals(rs.getInt("tipoconciliacao"))
                        && item.getTipoParcelamento() != 3
                ) ? (rs.getDouble("valorbruto") * rs.getInt("nrtotalparcelas")) : rs.getDouble("valorbruto")
        );
        item.setRo(rs.getString("ro"));
        item.setValorLiquido(rs.getDouble("valorliquido"));
        item.setValorComissao(rs.getDouble("valorcomissao"));
        item.setCodigoMovConta(rs.getInt("codigomovconta"));
        item.setCodigoCartaoCredito(rs.getInt("codigocartaocreditoextrato"));
        item.setTaxa(rs.getDouble("taxa"));
        item.setCredito(rs.getBoolean("credito"));
        item.setIdentificadorProduto(rs.getString("identificadorProduto"));
        item.setNsu(rs.getString("nsu"));
        item.setEstorno(rs.getBoolean("estorno"));
        item.setTipoArquivo(rs.getString("tipoarquivo"));
        item.setCodigoMovPagamentoExtratoDiario(rs.getInt("codigoMovPagamentoExtrato"));
        try {
            item.setTipoConvenioCobrancaEnum(TipoConvenioCobrancaEnum.valueOf(rs.getInt("tipoconveniocobranca")));
        } catch (Exception ignored) {
        }
    }

    public static void incluirContaMovimentoParaContaCorrente(List<ExtratoDiarioItemVO> lista, Connection con) throws Exception{

        for (ExtratoDiarioItemVO item: lista) {
            if (item.getMovPagamento() != null && item.getMovPagamento().getProdutosPagos().contains("CC") && UteisValidacao.emptyString(item.getContaMovimento())) {
                if (item.getTipoFormaPagamento().equals("CD")) {
                    montarContaMovimentadoCartaoDebitoContaCorrente(item, con);
                } else if (item.getTipoFormaPagamento().equals("CA")) {
                    montarContaMovimentadoCartaoCreditoContaCorrente(item, con);
                }
            }
        }
    }

    public void ajustarValorMovpagamentoOuCartaoCreditoComSaldoEnviadoDeCancelamentoContratoParaContaCorrente(List<ExtratoDiarioItemVO> lista, Connection con) throws Exception{
        //Teve de criar essa validação porquê ao fazer um Cancelamento de Contrato e jogar o valor na Conta Corrente, o sistema fica alterando os dados do MovPagamento e CartaoCredito
        //para gerar valores proporcionais para exibir em alguns relatórios e não gerar um MovaPagamento com ProdutosPagos CC.
        for (ExtratoDiarioItemVO item: lista) {
            if (item.getCredito()){
                if(item.getCartao() != null && item.getSituacao() != null && !item.getSituacao().equals(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE)){
                    if (!Uteis.valoresIguaisComTolerancia(item.getValorBruto(), item.getCartao().getValor(), 0.9, 0)){
                        //Ajustar diferença maior que 0,01 centavo
                        StringBuilder sql = new StringBuilder();
                        sql.append("SELECT valortotal FROM cartaocredito WHERE movpagamento = " + item.getMovPagamento().getCodigo() + " \n");
                        if(item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())){
                            sql.append("AND nrparcela = " + item.getCartao().getNrParcela() + " \n");
                        }
                        sql.append("AND situacao NOT IN ('CA');");
                        PreparedStatement stm = con.prepareStatement(sql.toString());
                        ResultSet rs = stm.executeQuery();

                        Double valorTotal = 0.0;
                        while (rs.next()) {
                            valorTotal += rs.getDouble("valortotal");
                        }
                        item.getMovPagamento().setValor(valorTotal);
                        item.getCartao().setValor(valorTotal);
                    } else {
                        //Ajustar diferença de 0,01 colocando o valor igual ao do extrato da adquirente
                        item.getMovPagamento().setValor(item.getValorBruto());
                        item.getCartao().setValor(item.getValorBruto());
                    }
                }
            } else {
                if(item.getSituacao() != null && !item.getSituacao().equals(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE) && item.getMovPagamento() != null && !UteisValidacao.emptyNumber(item.getValorBruto())){
                    BigDecimal bd1 = BigDecimal.valueOf(item.getMovPagamento().getValor());
                    BigDecimal bd2 = BigDecimal.valueOf(item.getValorBruto());
                    //Validação da pessoa para não entrar nessa condição se for Venda para Consumidor.
                    //Se no futuro encontrar uma situação de Venda ao Consumidor que precisa ajustar o valor, adicionar um else if para não impactar o que já funciona com alunos.
                    if (bd1.setScale(2, RoundingMode.HALF_UP).compareTo(bd2.setScale(2, RoundingMode.HALF_UP)) != 0 &&
                            item.getMovPagamento() != null && item.getMovPagamento().getPessoa() != null &&
                            !UteisValidacao.emptyNumber(item.getMovPagamento().getPessoa().getCodigo())){
                        StringBuilder sql = new StringBuilder();
                        sql.append("SELECT valortotal FROM movpagamento WHERE autorizacaocartao LIKE '" + item.getMovPagamento().getAutorizacaoCartao() + "' AND pessoa = "
                                + item.getMovPagamento().getPessoa().getCodigo() + " AND recibopagamento IS NOT NULL;");
                        PreparedStatement stm = con.prepareStatement(sql.toString());
                        ResultSet rs = stm.executeQuery();

                        Double valorTotal = 0.0;
                        while (rs.next()) {
                            valorTotal += rs.getDouble("valortotal");
                        }
                        item.getMovPagamento().setValor(valorTotal);
                    }
                }
            }
        }
    }

    public void validarSituacaoExtratoDiarioItemVO(List<ExtratoDiarioItemVO> lista) throws Exception{
        for (ExtratoDiarioItemVO item: lista) {
            try {
            if (item.getSituacao().equals(SituacaoItemExtratoEnum.ESTORNADO_SISTEMA.ordinal()) ||
                    UteisValidacao.emptyNumber(item.getCodigoMovPagamento())) {
                item.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_SISTEMA);
            } else if(item.getMovPagamento() == null && item.getValorBruto() > 0.0){
                item.setSituacao(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE);
            } else if(item.getMovPagamento() == null && item.getValorBruto() < 0.0) {
                item.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_OPERADORA);
            } else if(item.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())){
                if(item.getMovPagamento() != null && item.getMovPagamento().getDataLancamento() != null &&
                        Calendario.igual(item.getMovPagamento().getDataLancamento(), item.getDataLancamento()) &&
                        Uteis.valoresIguaisComTolerancia(item.getValorBruto(), item.getMovPagamento().getValor(), 0.9, item.getComposicao())){
                    item.setSituacao(SituacaoItemExtratoEnum.OK);
                } else if (item.getValorBruto() == 0.0) {
                    item.setSituacao(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE);
                } else {
                    item.setSituacao(SituacaoItemExtratoEnum.PENDENCIAS);
                }
            } else if (item.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) {
                if (item.getCartao() == null && Calendario.igual(item.getMovPagamento().getDataPagamento(), item.getDataPrevistaPagamento()) &&
                        Uteis.valoresIguaisComTolerancia(item.getValorBruto(), item.getMovPagamento().getValor(), 0.9, item.getComposicao())) {
                    item.setSituacao(SituacaoItemExtratoEnum.OK);
                } else if (item.getCartao() != null && Calendario.igual(item.getCartao().getDataCompensacao(), item.getDataPrevistaPagamento()) &&
                        Uteis.valoresIguaisComTolerancia(item.getValorBruto(), item.getCartao().getValor(), 0.9, item.getComposicao())) {
                    item.setSituacao(SituacaoItemExtratoEnum.OK);
                } else if (UteisValidacao.emptyNumber(item.getCodigo()) && item.getValorBruto() == 0.0) {
                    item.setSituacao(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE);
                } else {
                    item.setSituacao(SituacaoItemExtratoEnum.PENDENCIAS);
                }
            }
            } catch (Exception ex) {
            }
        }
    }

    public static void montarContaMovimentadoCartaoCreditoContaCorrente(ExtratoDiarioItemVO item, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.codigo FROM cartaocredito c WHERE c.movpagamento = ( \n")
                .append(" SELECT mp.codigo from movpagamento mp where mp.movpagamentoorigemcredito = " + item.getMovPagamento().getCodigo() + " ORDER BY mp.codigo DESC LIMIT 1 \n")
                .append(" ); \n");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();

        if (rs.next()) {
            Lote lote = new Lote(con);
            LoteVO loteVO = lote.consultarPorCartao(rs.getInt("codigo"), negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (loteVO != null && !UteisValidacao.emptyNumber(loteVO.getCodigo())) {
                item.getCartao().setNumeroLote(loteVO.getCodigo());
                getFacade().getCartaoCredito().getContaLoteCartao(item.getCartao(), item.getCartao().getNumeroLote());
            }

            item.setContaMovimento(item.getCartao().getContaContido());
        }
        if (!TipoConciliacaoEnum.VENDAS.getCodigo().equals(item.getTipoConciliacao())) {
            item.setSituacao((Calendario.igual(item.getCartao().getDataCompensacao(), item.getDataPrevistaPagamento())
                    && Uteis.valoresIguaisComTolerancia(item.getValorBruto(), item.getCartao().getValor(), 0.9, item.getComposicao())
                    ? SituacaoItemExtratoEnum.OK : SituacaoItemExtratoEnum.PENDENCIAS));
        }
    }

    public static void montarContaMovimentadoCartaoDebitoContaCorrente(ExtratoDiarioItemVO item, Connection con) throws Exception {
        StringBuilder sqlCount = new StringBuilder();
        sqlCount.append("SELECT mvp.codigo, mvp.movpagamentoorigemcredito, mvp.movconta, c.descricao \n");
        sqlCount.append(" FROM movpagamento mvp \n");
        sqlCount.append(" LEFT JOIN movconta mvc ON mvc.codigo = mvp.movconta \n");
        sqlCount.append(" LEFT JOIN conta c ON c.codigo = mvc.conta \n");
        sqlCount.append(" WHERE mvp.movpagamentoorigemcredito = '" + item.getMovPagamento().getCodigo() + "' ORDER BY mvp.codigo DESC LIMIT 1; ");
        PreparedStatement stm = con.prepareStatement(sqlCount.toString());
        ResultSet rs = stm.executeQuery();

        if (rs.next() && !UteisValidacao.emptyString(rs.getString("descricao"))) {
            item.setContaMovimento(rs.getString("descricao"));
        }
    }

    public void incluirInfoCodPessoa(ExtratoDiarioItemVO extratoDiarioItemVO) throws SQLException {
        if (UteisValidacao.emptyNumber(extratoDiarioItemVO.getPessoa().getCodigo())) {
            return;
        }
        String sql = "UPDATE extratodiarioitem SET pessoa = " + extratoDiarioItemVO.getPessoa().getCodigo() + " WHERE codigo = " + extratoDiarioItemVO.getCodigo();
        PreparedStatement statement = con.prepareStatement(sql);
        statement.execute();
    }

    public void processarItensSemPessoa(Integer convenioCobranca, Date dataInicio) {
        Transacao transacaoDAO;
        try {
            transacaoDAO = new Transacao(con);
            transacaoDAO.preencherNSUeAutorizacao(convenioCobranca, dataInicio);

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("ei.codigo, \n");
            sql.append("ei.nsu, \n");
            sql.append("ei.autorizacao, \n");
            sql.append("ei.datalancamento, \n");
            sql.append("ei.conveniocobranca \n");
            sql.append("from extratodiarioitem ei \n");
            sql.append("left join cartaocredito cc on cc.codigo = ei.codigocartaocredito \n");
            sql.append("left join movconta mvc on mvc.codigo = ei.codigomovconta \n");
            sql.append("left join movpagamento mp on mp.codigo = ei.codigomovpagamento \n");
            sql.append("where (coalesce(ei.nsu,'') <> '' or coalesce(ei.autorizacao,'') <> '') \n");
            sql.append("and coalesce(ei.pessoa,0) = 0 \n");
            sql.append("and coalesce(mp.codigo,0) = 0 \n");
            sql.append("and coalesce(mvc.codigo,0) = 0 \n");
            sql.append("and coalesce(cc.codigo,0) = 0 \n");
            if (dataInicio != null) {
                sql.append("and ei.datalancamento::date >= '").append(Uteis.getDataFormatoBD(dataInicio)).append("' \n");
            }
            if (!UteisValidacao.emptyNumber(convenioCobranca)) {
                sql.append("and ei.convenioCobranca = ").append(convenioCobranca).append(" \n");
            }
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                try {
                    ExtratoDiarioItemVO obj = new ExtratoDiarioItemVO();
                    obj.setCodigo(rs.getInt("codigo"));
                    obj.setNsu(rs.getString("nsu"));
                    obj.setAutorizacao(rs.getString("autorizacao"));
                    obj.setDataLancamento(rs.getDate("datalancamento"));
                    obj.getConvenio().setCodigo(rs.getInt("conveniocobranca"));

                    Uteis.logarDebug("Buscar Pessoa | Convênio: " + obj.getConvenio().getCodigo() + " | Autorizacao: \"" + obj.getAutorizacao() + "\" | NSU: \"" + obj.getNsu() + "\" | Data: \"" + obj.getDataLancamento() + "\"");

                    obterPessoaExtrato(obj);
                    if (!UteisValidacao.emptyNumber(obj.getPessoa().getCodigo())) {
                        Uteis.logarDebug("Encontrei Pessoa "+obj.getPessoa().getCodigo()+" | Autorizacao: \"" + obj.getAutorizacao() + "\" | NSU: \"" + obj.getNsu() + "\" | Data: \"" + obj.getDataLancamento() + "\"");
                        incluirInfoCodPessoa(obj);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            transacaoDAO = null;
        }
    }

    public void obterPessoaExtrato(ExtratoDiarioItemVO itemVO) {
        try {
            String autorizacao = itemVO.getAutorizacao();
            String nsu = itemVO.getNsu();
            if (UteisValidacao.emptyString(nsu) &&
                    UteisValidacao.emptyString(autorizacao)) {
                return;
            }

            Integer convenioCobranca = itemVO.getConvenio().getCodigo();
            Date dataProcessamento = itemVO.getDataLancamento();

            if (!UteisValidacao.emptyNumber(itemVO.getCodigo())) {
                StringBuilder sql1 = new StringBuilder();
                sql1.append("select \n");
                sql1.append("0 as ordem, \n");
                sql1.append("case \n");
                sql1.append("when coalesce(mpcc.pessoa,0) > 0 then mpcc.pessoa \n");
                sql1.append("when coalesce(mp.pessoa,0) > 0 then mp.pessoa \n");
                sql1.append("when coalesce(mvc.pessoa,0) > 0 then mvc.pessoa \n");
                sql1.append("else null end as pessoa \n");
                sql1.append("from extratodiarioitem ei \n");
                sql1.append("left join cartaocredito cc on cc.codigo = ei.codigocartaocredito \n");
                sql1.append("left join movconta mvc on mvc.codigo = ei.codigomovconta \n");
                sql1.append("left join movpagamento mp on mp.codigo = ei.codigomovpagamento \n");
                sql1.append("left join movpagamento mpcc on mpcc.codigo = cc.movpagamento \n");
                sql1.append("where ei.codigo = ").append(itemVO.getCodigo()).append(" \n");

                try (PreparedStatement ps = con.prepareStatement(sql1.toString())) {
                    try (ResultSet rs = ps.executeQuery()) {
                        if (rs.next()) {
                            itemVO.getPessoa().setCodigo(rs.getInt("pessoa"));
                            return;
                        }
                    }
                }
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("sql.* \n");
            sql.append("from ( \n");

            //nsu movpagamento
            sql.append("select \n");
            sql.append("1 as ordem, \n");
            sql.append("mp.pessoa \n");
            sql.append("from movpagamento mp \n");
            sql.append("where mp.datalancamento::date between '").append(Uteis.getDataJDBC(Uteis.voltarDias(dataProcessamento, 15))).append("' ");
            sql.append("and '").append(Uteis.getDataJDBC(Uteis.somarDias(dataProcessamento, 15))).append("' \n");
//            sql.append("and mp.convenioCobranca = ").append(convenioCobranca).append(" \n");
            if (!UteisValidacao.emptyString(nsu)) {
                sql.append("and mp.nsu ilike '").append(nsu).append("' \n");
            } else {
                sql.append("and false \n");
            }

            sql.append("union all \n");

            //nsu transacao
            sql.append("select \n");
            sql.append("2 as ordem, \n");
            sql.append("t.pessoapagador \n");
            sql.append("from transacao t \n");
            sql.append("where t.dataprocessamento::date between '").append(Uteis.getDataJDBC(Uteis.voltarDias(dataProcessamento, 15))).append("' ");
            sql.append("and '").append(Uteis.getDataJDBC(Uteis.somarDias(dataProcessamento, 15))).append("' \n");
//            sql.append("and t.convenioCobranca = ").append(convenioCobranca).append(" \n");
            if (!UteisValidacao.emptyString(nsu)) {
                sql.append("and t.nsu ilike '").append(nsu).append("' \n");
            } else {
                sql.append("and false \n");
            }

            sql.append("union all \n");

            //autorizacao movpagamento
            sql.append("select \n");
            sql.append("3 as ordem, \n");
            sql.append("mp.pessoa \n");
            sql.append("from movpagamento mp \n");
            sql.append("where mp.datalancamento::date between '").append(Uteis.getDataJDBC(Uteis.voltarDias(dataProcessamento, 15))).append("' ");
            sql.append("and '").append(Uteis.getDataJDBC(Uteis.somarDias(dataProcessamento, 15))).append("' \n");
//            sql.append("and mp.convenioCobranca = ").append(convenioCobranca).append(" \n");

            if (!UteisValidacao.emptyString(autorizacao)) {
                sql.append("and mp.autorizacaocartao ilike '").append(autorizacao).append("' \n");
            } else {
                sql.append("and false \n");
            }

            sql.append("union all \n");

            //autorizacao transacao
            sql.append("select \n");
            sql.append("4 as ordem, \n");
            sql.append("t.pessoapagador \n");
            sql.append("from transacao t \n");
            sql.append("where t.dataprocessamento::date between '").append(Uteis.getDataJDBC(Uteis.voltarDias(dataProcessamento, 15))).append("' ");
            sql.append("and '").append(Uteis.getDataJDBC(Uteis.somarDias(dataProcessamento, 15))).append("' \n");
//            sql.append("and t.convenioCobranca = ").append(convenioCobranca).append(" \n");
            if (!UteisValidacao.emptyString(autorizacao)) {
                sql.append("and t.codigoautorizacao ilike '").append(autorizacao).append("' \n");
            } else {
                sql.append("and false \n");
            }

            sql.append(") as sql \n");
            sql.append("order by ordem \n");

            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        itemVO.getPessoa().setCodigo(rs.getInt("pessoa"));
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void criarContaPagarOuReceberCancelamentoStone(ConvenioCobrancaVO convenioCobrancaVO, ExtratoDiarioItemVO itemVO,
                                                          EmpresaVO empresaVO, ConfiguracaoFinanceiroVO configuracaoFinanceiroVO) {
        Conta contaDAO;
        try {
            if ((!itemVO.getConvenio().isStone() && !itemVO.getConvenio().isStoneV5() && !itemVO.getConvenio().isStoneConnect()) ||
                    UteisValidacao.emptyString(itemVO.getNsu())) {
                return;
            }
            contaDAO = new Conta(this.con);

            String identificador = ("NSU|" + itemVO.getNsu());
            String identificadorBusca = ("NSU|" + itemVO.getNsu());
            String descricao = "";
            TipoES tipoES = TipoES.SAIDA;

            if (itemVO.getTipoConciliacao() != null && itemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.CHARGEBACK.getCodigo())) {
                descricao = ("CHARGEBACK STONE CANCELAMENTO - NSU " + itemVO.getNsu());
                if (!UteisValidacao.emptyNumber(itemVO.getNrParcela())) {
                    identificador = ("NSU|" + itemVO.getNsu() + "|" + itemVO.getNrParcela());
                    descricao = ("CHARGEBACK STONE CANCELAMENTO - NSU " + itemVO.getNsu() + " - Parcela " + itemVO.getNrParcela());
                }
            } else if (itemVO.getTipoConciliacao() != null && itemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.TAXA_CANCELAMENTO.getCodigo())) {
                descricao = ("ESTORNO STONE CANCELAMENTO - NSU " + itemVO.getNsu());
                if (!UteisValidacao.emptyNumber(itemVO.getNrParcela())) {
                    identificador = ("NSU|" + itemVO.getNsu() + "|" + itemVO.getNrParcela());
                    descricao = ("ESTORNO STONE CANCELAMENTO - NSU " + itemVO.getNsu() + " - Parcela " + itemVO.getNrParcela());
                }
            } else if (itemVO.getTipoConciliacao() != null && itemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.ESTORNO_CHARGEBACK.getCodigo())) {
                tipoES = TipoES.ENTRADA;
                descricao = ("ESTORNO DE CHARGEBACK STONE - NSU " + itemVO.getNsu());
                if (!UteisValidacao.emptyNumber(itemVO.getNrParcela())) {
                    identificador = ("NSU|" + itemVO.getNsu() + "|" + itemVO.getNrParcela());
                    descricao = ("ESTORNO DE CHARGEBACK STONE - NSU " + itemVO.getNsu() + " - Parcela " + itemVO.getNrParcela());
                }
            } else {
                descricao = ("ESTORNO STONE CANCELAMENTO - NSU " + itemVO.getNsu());
                if (!UteisValidacao.emptyNumber(itemVO.getNrParcela())) {
                    identificador = ("NSU|" + itemVO.getNsu() + "|" + itemVO.getNrParcela());
                    descricao = ("ESTORNO STONE CANCELAMENTO - NSU " + itemVO.getNsu() + " - Parcela " + itemVO.getNrParcela());
                }
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("c.* \n");
            sql.append("from movconta c \n");
            sql.append("where (c.identificadorOrigem = '").append(identificador).append("' \n");
            sql.append("or c.identificadorOrigem = '").append(identificadorBusca).append("')\n");
            sql.append("and c.identificadorOrigemTipo = ").append(itemVO.getTipoConciliacao()).append(" \n");
            sql.append("and c.datalancamento::date = '").append(Uteis.getDataJDBC(itemVO.getDataArquivo())).append("'");

            //verificar se já existe conta lançada com o nsu na data do processamento do arquivo
            boolean existe = false;

            ResultSet rsExiste = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con);
            while (rsExiste.next()) {
                String identificadorDados = rsExiste.getString("identificadorDados");
                if (UteisValidacao.emptyString(identificadorDados) ||
                        UteisValidacao.emptyString(itemVO.getIdExterno())) {
                    existe = true;
                    break;
                }

                try {
                    JSONObject jsonObject = new JSONObject(identificadorDados);
                    String idExterno = jsonObject.optString("IdExterno");
                    if (UteisValidacao.emptyString(idExterno)) {
                        existe = true;
                        break;
                    }
                    if (idExterno.equals(itemVO.getIdExterno())) {
                        existe = true;
                        break;
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    //evitar que seja lançado conta duplicado
                    //se der erro marca como já existe
                    existe = true;
                }
            }

            if (existe) {
                Uteis.logarDebug("Já existe conta a pagar | " + descricao);
                return;
            }

            Uteis.logarDebug("Vou incluir conta a pagar | Cancelamento NSU: " + itemVO.getNsu() +
                    (!UteisValidacao.emptyNumber(itemVO.getNrParcela())  ? (" | Parcela: " + itemVO.getNrParcela()) : "") +
                    (itemVO.getDataCancelamento() != null  ? (" | Data: " + Calendario.getDataAplicandoFormatacao(itemVO.getDataCancelamento(), "dd/MM/yyyy HH:mm:ss")) : "") +
                    (itemVO.getTipoConciliacaoEnum() != null ? (" | Tipo: " + itemVO.getTipoConciliacaoEnum().name()) : ""));

            PessoaVO pessoaVO = new PessoaVO();

            StringBuilder sqlPessoa = new StringBuilder();
            sqlPessoa.append("select pessoapagador as pessoa from transacao where nsu = '").append(itemVO.getNsu()).append("' \n");
            sqlPessoa.append("union all \n");
            sqlPessoa.append("select pessoa from movpagamento where nsu = '").append(itemVO.getNsu()).append("' \n");
            ResultSet rs = criarConsulta(sqlPessoa.toString(), this.con);
            boolean pessoaEncontrada = rs.next();

            if (pessoaEncontrada) {
                pessoaVO.setCodigo(rs.getInt("pessoa"));
            } else if (!UteisValidacao.emptyString(itemVO.getNsu())) {
                //incluir cadastro pessoa genérico
                String nomeGenericoPessoa = "FAVORECIDO CONCILIAÇÃO NSU " + itemVO.getNsu();
                pessoaVO.setNome(nomeGenericoPessoa);
                pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
                getFacade().getPessoa().incluir(pessoaVO);
            } else {
                throw new Exception("Pessoa não encontrada com o NSU: " + itemVO.getNsu());
            }

            MovContaVO movContaVO = new MovContaVO();
            movContaVO.setEmpresaVO(empresaVO);
            movContaVO.setPessoaVO(pessoaVO);
            movContaVO.setDescricao(descricao);
            movContaVO.setUsarHoraAtual(false);
            movContaVO.setDataLancamento(itemVO.getDataArquivo());
            movContaVO.setDataCompetencia(itemVO.getDataArquivo());
            movContaVO.setDataVencimento(itemVO.getDataArquivo());
            movContaVO.setDataQuitacao(itemVO.getDataArquivo());
            movContaVO.setValor(Uteis.arredondarForcando2CasasDecimais(itemVO.getValorBruto()));
            movContaVO.setValorPago(Uteis.arredondarForcando2CasasDecimais(itemVO.getValorBruto()));
            movContaVO.setValorOriginalAlterado(Uteis.arredondarForcando2CasasDecimais(itemVO.getValorBruto()));
            movContaVO.setValorOriginalAntesDaConciliacao(Uteis.arredondarForcando2CasasDecimais(itemVO.getValorBruto()));
            movContaVO.setIdentificadorOrigem(identificador);
            movContaVO.setIdentificadorOrigemTipo(itemVO.getTipoConciliacao() != null ? itemVO.getTipoConciliacao() : 0);

            //obter conta
            if (!UteisValidacao.emptyNumber(configuracaoFinanceiroVO.getContaCriarContaPagarAutomaticoVO().getCodigo())) {
                try {
                    ContaVO contaVO = contaDAO.consultarPorChavePrimaria(configuracaoFinanceiroVO.getContaCriarContaPagarAutomaticoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    movContaVO.setContaVO(contaVO);
                } catch (Exception ignored) {
                }
            }

            MovContaRateioVO movContaRateioVO = new MovContaRateioVO();
            movContaRateioVO.setDescricao(movContaVO.getDescricao());
            movContaRateioVO.setValor(movContaVO.getValor());
            movContaRateioVO.setTipoES(tipoES);
            if (tipoES.equals(TipoES.ENTRADA)) {
                movContaRateioVO.setPlanoContaVO(configuracaoFinanceiroVO.getPlanoContasLancamentoAutomaticoEntrada());
                movContaRateioVO.setCentroCustoVO(configuracaoFinanceiroVO.getCentroCustoLancamentoAutomaticoEntrada());
            } else {
                movContaRateioVO.setPlanoContaVO(configuracaoFinanceiroVO.getPlanoContasLancamentoAutomaticoSaida());
                movContaRateioVO.setCentroCustoVO(configuracaoFinanceiroVO.getCentroCustoLancamentoAutomaticoSaida());
            }

            String obs = ("Cancelamento de cobrança: \n");
            if (tipoES.equals(TipoES.ENTRADA)) {
                obs = ("Estorno de chargeback: \n");
            }

            obs += ("\nNSU: " + itemVO.getNsu());
            if (!UteisValidacao.emptyNumber(itemVO.getNrParcela())) {
                obs += ("\nParcela: " + itemVO.getNrParcela());
            }
            if (itemVO.getDataCancelamento() != null) {
                obs += ("\nDt. Cancelamento: " + Calendario.getDataAplicandoFormatacao(itemVO.getDataCancelamento(), "dd/MM/yyyy HH:mm:ss"));
            }
            obs += ("\nConvênio: " + convenioCobrancaVO.getDescricao() + " (" + convenioCobrancaVO.getTipo().getDescricao() + ")");
            movContaVO.setObservacoes(obs);

            // Se no futuro alguém questionar por estar indo tudo para cartão de crédito e tiver mais experiência,
            // estudar a possibilidade de usar a mesma Forma de Pagamento do MovPagamento do itemVO.
            movContaRateioVO.setFormaPagamentoVO(getFacade().getFormaPagamento().obterFormaPagamentoRapido(TipoFormaPagto.CARTAOCREDITO));

            movContaVO.setMovContaRateios(new ArrayList<>());
            movContaVO.getMovContaRateios().add(movContaRateioVO);
            movContaVO.setUsuarioVO(getFacade().getUsuario().getUsuarioRecorrencia());

            if (tipoES.equals(TipoES.ENTRADA)) {
                movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.RECEBIMENTO);
            } else {
                movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.PAGAMENTO);
            }

            try {
                JSONObject json = new JSONObject();
                json.put("IdExterno", itemVO.getIdExterno());
                json.put("StoneCode", convenioCobrancaVO.getCodigoAutenticacao01());
                json.put("Arquivo", itemVO.getArquivo());
                movContaVO.setIdentificadorDados(json.toString());
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            getFacade().getMovConta().incluirSemCommit(movContaVO, 0, false, null, false);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            contaDAO = null;
        }
    }

    public void criarContaPagarOuReceberCancelamentoCielo(ConvenioCobrancaVO convenioCobrancaVO, ExtratoDiarioItemVO itemVO,
                                                          EmpresaVO empresaVO, ConfiguracaoFinanceiroVO configuracaoFinanceiroVO) {
        Conta contaDAO;
        try {
            if (!itemVO.getConvenio().isCieloOnline() ||
                    UteisValidacao.emptyString(itemVO.getAutorizacao())) {
                return;
            }
            contaDAO = new Conta(this.con);

            String identificador = ("CÓD. AUTORIZAÇÃO|" + itemVO.getAutorizacao());
            String descricao = "";
            TipoES tipoES = TipoES.SAIDA;

            descricao = ("ESTORNO/CHARGEBACK CIELO CANCELAMENTO - CÓD. AUTORIZAÇÃO " + itemVO.getAutorizacao());
            if (!UteisValidacao.emptyNumber(itemVO.getNrParcela())) {
                identificador = ("CÓD. AUTORIZAÇÃO|" + itemVO.getAutorizacao() + "|" + itemVO.getNrParcela());
                descricao = ("ESTORNO/CHARGEBACK CIELO CANCELAMENTO - CÓD. AUTORIZAÇÃO " + itemVO.getAutorizacao() + " - Parcela " + itemVO.getNrParcela());
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("c.* \n");
            sql.append("from movconta c \n");
            sql.append("where (c.identificadorOrigem = '").append(identificador).append("' \n");
            sql.append("or c.identificadorOrigem = '").append(identificador).append("')\n");
            sql.append("and c.identificadorOrigemTipo = ").append(itemVO.getTipoConciliacao()).append(" \n");
            sql.append("and c.datalancamento::date = '").append(Uteis.getDataJDBC(itemVO.getDataArquivo())).append("'");
            sql.append("and c.valor = ").append(Math.abs(itemVO.getValorBruto())); //Math.abs transforma o valor negativo em positivo pra comparar na movconta

            String identificadorDados = "";
            //verificar se já existe conta lançada com a autorização na data do processamento do arquivo
            ResultSet rsExiste = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con);
            boolean existe = rsExiste.next();
            if (existe) {
                Uteis.logarDebug("Já existe conta a pagar | " + descricao + " --> " + identificadorDados);
                return;
            }

            Date dataCancelamento = itemVO.getDataPrevistaPagamento(); //Assumir que a data de cancelamento é a data prevista de pagamento, pois é data em que o registro de cancelemento vem no arquivo
            Uteis.logarDebug("Vou incluir conta a pagar | Cancelamento CÓD. AUTORIZAÇÃO: " + itemVO.getAutorizacao() +
                    (!UteisValidacao.emptyNumber(itemVO.getNrParcela()) ? (" | Parcela: " + itemVO.getNrParcela()) : "") +
                    (dataCancelamento != null ? (" | Data: " + Calendario.getDataAplicandoFormatacao(dataCancelamento, "dd/MM/yyyy HH:mm:ss")) : "") +
                    (itemVO.getTipoConciliacaoEnum() != null ? (" | Tipo: " + itemVO.getTipoConciliacaoEnum().name()) : ""));

            PessoaVO pessoaVO = new PessoaVO();

            StringBuilder sqlPessoa = new StringBuilder();
            sqlPessoa.append("select pessoapagador as pessoa from transacao where codigoautorizacao = '").append(itemVO.getAutorizacao()).append("' \n");
            sqlPessoa.append("union all \n");
            sqlPessoa.append("select pessoa from movpagamento where autorizacaocartao = '").append(itemVO.getAutorizacao()).append("' \n");
            ResultSet rs = criarConsulta(sqlPessoa.toString(), this.con);
            boolean pessoaEncontrada = rs.next();

            if (pessoaEncontrada) {
                pessoaVO.setCodigo(rs.getInt("pessoa"));
            } else if (!UteisValidacao.emptyString(itemVO.getAutorizacao())) {
                //incluir cadastro pessoa genérico
                String nomeGenericoPessoa = "FAVORECIDO CONCILIAÇÃO CÓD. AUTORIZAÇÃO " + itemVO.getAutorizacao();
                pessoaVO.setNome(nomeGenericoPessoa);
                pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
                getFacade().getPessoa().incluir(pessoaVO);
            } else {
                throw new Exception("Pessoa não encontrada com O CÓD. AUTORIZAÇÃO: " + itemVO.getAutorizacao());
            }

            MovContaVO movContaVO = new MovContaVO();
            movContaVO.setEmpresaVO(empresaVO);
            movContaVO.setPessoaVO(pessoaVO);
            movContaVO.setDescricao(descricao);
            movContaVO.setUsarHoraAtual(false);
            movContaVO.setDataLancamento(itemVO.getDataArquivo());
            movContaVO.setDataCompetencia(itemVO.getDataArquivo());
            movContaVO.setDataVencimento(itemVO.getDataArquivo());
            movContaVO.setDataQuitacao(itemVO.getDataArquivo());
            movContaVO.setValor(Uteis.arredondarForcando2CasasDecimais(itemVO.getValorBruto()));
            movContaVO.setValorPago(Uteis.arredondarForcando2CasasDecimais(itemVO.getValorBruto()));
            movContaVO.setValorOriginalAlterado(Uteis.arredondarForcando2CasasDecimais(itemVO.getValorBruto()));
            movContaVO.setValorOriginalAntesDaConciliacao(Uteis.arredondarForcando2CasasDecimais(itemVO.getValorBruto()));
            movContaVO.setIdentificadorOrigem(identificador);
            movContaVO.setIdentificadorOrigemTipo(itemVO.getTipoConciliacao() != null ? itemVO.getTipoConciliacao() : 0);

            //obter conta
            if (!UteisValidacao.emptyNumber(configuracaoFinanceiroVO.getContaCriarContaPagarAutomaticoVO().getCodigo())) {
                try {
                    ContaVO contaVO = contaDAO.consultarPorChavePrimaria(configuracaoFinanceiroVO.getContaCriarContaPagarAutomaticoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    movContaVO.setContaVO(contaVO);
                } catch (Exception ignored) {
                }
            }

            MovContaRateioVO movContaRateioVO = new MovContaRateioVO();
            movContaRateioVO.setDescricao(movContaVO.getDescricao());
            movContaRateioVO.setValor(movContaVO.getValor());
            movContaRateioVO.setTipoES(tipoES);
            if (tipoES.equals(TipoES.ENTRADA)) {
                movContaRateioVO.setPlanoContaVO(configuracaoFinanceiroVO.getPlanoContasLancamentoAutomaticoEntrada());
                movContaRateioVO.setCentroCustoVO(configuracaoFinanceiroVO.getCentroCustoLancamentoAutomaticoEntrada());
            } else {
                movContaRateioVO.setPlanoContaVO(configuracaoFinanceiroVO.getPlanoContasLancamentoAutomaticoSaida());
                movContaRateioVO.setCentroCustoVO(configuracaoFinanceiroVO.getCentroCustoLancamentoAutomaticoSaida());
            }

            String obs = ("Cancelamento de cobrança: \n");
            if (tipoES.equals(TipoES.ENTRADA)) {
                obs = ("Estorno de chargeback: \n");
            }

            String nsu = itemVO.getProps().getOrDefault("TID", "");

            obs += ("\nCÓD. AUTORIZAÇÃO: " + itemVO.getAutorizacao());
            obs += ("\nNSU: " + nsu);
            if (!UteisValidacao.emptyNumber(itemVO.getNrParcela())) {
                obs += ("\nParcela: " + itemVO.getNrParcela());
            }
            if (itemVO.getDataCancelamento() != null) {
                obs += ("\nDt. Cancelamento: " + Calendario.getDataAplicandoFormatacao(itemVO.getDataCancelamento(), "dd/MM/yyyy HH:mm:ss"));
            }
            obs += ("\nConvênio: " + convenioCobrancaVO.getDescricao() + " (" + convenioCobrancaVO.getTipo().getDescricao() + ")");
            movContaVO.setObservacoes(obs);

            // Se no futuro alguém questionar por estar indo tudo para cartão de crédito e tiver mais experiência,
            // estudar a possibilidade de usar a mesma Forma de Pagamento do MovPagamento do itemVO.
            movContaRateioVO.setFormaPagamentoVO(getFacade().getFormaPagamento().obterFormaPagamentoRapido(TipoFormaPagto.CARTAOCREDITO));

            movContaVO.setMovContaRateios(new ArrayList<>());
            movContaVO.getMovContaRateios().add(movContaRateioVO);
            movContaVO.setUsuarioVO(getFacade().getUsuario().getUsuarioRecorrencia());

            if (tipoES.equals(TipoES.ENTRADA)) {
                movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.RECEBIMENTO);
            } else {
                movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.PAGAMENTO);
            }

            try {
                JSONObject json = new JSONObject();
                json.put("Estabelecimento:", itemVO.getEstabelecimento());
                json.put("MerchantId", convenioCobrancaVO.getCodigoAutenticacao01());
                json.put("Arquivo", itemVO.getArquivo());
                json.put("Autorização", itemVO.getAutorizacao());
                json.put("Valor", itemVO.getValorBruto());
                json.put("NSU", nsu);
                movContaVO.setIdentificadorDados(json.toString());
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            getFacade().getMovConta().incluirSemCommit(movContaVO, 0, false, null, false);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            contaDAO = null;
        }
    }

    public void excluirMovPagamento(Integer idMovPagamento) throws Exception {
        if (UteisValidacao.emptyNumber(idMovPagamento)) {
            return;
        }
        PreparedStatement stm = con.prepareStatement("UPDATE extratodiarioitem set codigomovpagamento = 0 WHERE codigomovpagamento = ?");
        stm.setInt(1, idMovPagamento);
        stm.execute();
    }

    public Integer consultarUltimaParcelaPelaAutorizacao(String autorizacao, Integer codEmpresa, Integer tipoConciliacao, Date dataTransacao, Integer codPessoa) throws Exception {
        PreparedStatement stm = con.prepareStatement("SELECT nrparcela FROM extratodiarioitem WHERE autorizacao ILIKE '" + autorizacao + "' " +
                "AND tipoconciliacao = " + tipoConciliacao + " AND empresa = " + codEmpresa + " AND dataprevistapagamento <= '" + Calendario.getData(dataTransacao, "yyyy-MM-dd") + "' " +
                "AND pessoa = " + codPessoa + " ORDER BY dataprevistapagamento DESC, nrparcela DESC LIMIT 1;");
        ResultSet rs =  stm.executeQuery();
        if(rs.next()){
            return rs.getInt("nrparcela");
        } else {
            return 0;
        }
    }

}
