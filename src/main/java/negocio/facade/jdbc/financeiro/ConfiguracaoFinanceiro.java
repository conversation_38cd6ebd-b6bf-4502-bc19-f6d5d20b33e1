package negocio.facade.jdbc.financeiro;

import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.interfaces.financeiro.ConfiguracaoFinanceiroInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;

public class ConfiguracaoFinanceiro extends SuperEntidade implements ConfiguracaoFinanceiroInterfaceFacade {

    public ConfiguracaoFinanceiro() throws Exception {
        super();
    }

    public ConfiguracaoFinanceiro(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void alterar(ConfiguracaoFinanceiroVO confFinan) throws Exception {
        if(confFinan.isBloquearAlunoChequeDevolvido()
                && UteisValidacao.emptyString(confFinan.getMensagembloqueio())){
            throw new Exception("Informe a mensagem de bloqueio da catraca");
        }

        PreparedStatement stm = con.prepareStatement("UPDATE configuracaofinanceiro set planocontastaxa = ? , centrocustostaxa = ?, "
                + "usarcentraleventos = ?, solicitasenhalancarconta = ?, permitirContaOutraUnidade = ?, "
                + "especificarCompetencia = ?, habilitarExportacaoAlterData=?, fecharCaixaAutomaticamente = ?,"
                + "mensagembloqueio = ?, bloquearalunochequedevolvido = ?, planoContasDevolucao = ?, adicionarDevolucaoRelatorioComissao = ?, centroCustoDevolucao = ?, planocontastaxaboleto = ? , centrocustostaxaboleto = ?,"
                + " cnpjObrigatorioFornecedor = ?, contaPagarCompraEstoque = ?, contaPagarReceberColabInativo = ?, criarContaPagarAutomatico = ?, contacriarcontapagarautomatico = ?, apresentarValorPago = ?, "
                + "permitirTipoPlanoContaFilho= ?, planoContasLancamentoAutomaticoSaida = ?, planoContasLancamentoAutomaticoEntrada = ?, centroCustoLancamentoAutomaticoSaida = ?, centroCustoLancamentoAutomaticoEntrada = ?,"
                + "informarfavorecidoaorealizarmovimentacaorecebiveis = ?, planoContasTaxaPix = ?, centroCustoTaxaPix = ?, "
                + "descricaomovimentacaoautomaticadebito= ?, descricaomovimentacaoautomaticacredito= ?, contamovimentacaoautomaticadebito= ?,  contamovimentacaoautomaticacredito= ?, favorecidomovimentacaoautomaticadebito= ?," +
                " favorecidomovimentacaoautomaticacredito= ?, movimentacaoAutomaticaRecebiveisConciliacao = ?, buscarFornecedorTodasUnidades = ?, ordemCompraEstoque = ?, alterarValorPrevisto = ?, alterarDtPgtoZWAutomaticamenteConc = ?, obrigarPreenchimentoManualDtCompetencia = ? ");
        resolveFKNull(stm, 1, confFinan.getPlanoContasTaxa().getCodigo());
        resolveFKNull(stm, 2, confFinan.getCentroCustosTaxa().getCodigo());
        stm.setBoolean(3, confFinan.getUsarCentralEventos());
        stm.setBoolean(4, confFinan.getSolicitaSenhaLancarConta());
        stm.setBoolean(5, confFinan.isPermitirContaOutraUnidade());
        stm.setBoolean(6, confFinan.isEspecificarCompetencia());
        stm.setBoolean(7, confFinan.isHabilitarExportacaoAlterData());
        stm.setBoolean(8, confFinan.isFecharCaixaAutomaticamente());
        stm.setString(9, confFinan.getMensagembloqueio());
        stm.setBoolean(10, confFinan.isBloquearAlunoChequeDevolvido());
        resolveFKNull(stm, 11, confFinan.getPlanoContasDevolucao().getCodigo());
        stm.setBoolean(12, confFinan.isAdicionarDevolucaoRelatorioComissao());
        resolveFKNull(stm, 13, confFinan.getCentroCustoDevolucao().getCodigo());
        resolveFKNull(stm, 14, confFinan.getPlanoContasTaxaBoleto().getCodigo());
        resolveFKNull(stm, 15, confFinan.getCentroCustosTaxaBoleto().getCodigo());
        stm.setBoolean( 16, confFinan.isCnpjObrigatorioFornecedor());
        stm.setBoolean( 17, confFinan.isContaPagarCompraEstoque());
        stm.setBoolean( 18, confFinan.isContaPagarReceberColabInativo());
        stm.setBoolean( 19, confFinan.isCriarContaPagarAutomatico());
        resolveFKNull(stm, 20, confFinan.getContaCriarContaPagarAutomaticoVO().getCodigo());
        stm.setBoolean( 21, confFinan.isApresentarValorPago());
        stm.setBoolean( 22, confFinan.isPermitirTipoPlanoContaFilho());
        resolveFKNull(stm, 23, confFinan.getPlanoContasLancamentoAutomaticoSaida().getCodigo());
        resolveFKNull(stm, 24, confFinan.getPlanoContasLancamentoAutomaticoEntrada().getCodigo());
        resolveFKNull(stm, 25, confFinan.getCentroCustoLancamentoAutomaticoSaida().getCodigo());
        resolveFKNull(stm, 26, confFinan.getCentroCustoLancamentoAutomaticoEntrada().getCodigo());
        stm.setBoolean( 27, confFinan.isInformarFavorecidoAoRealizarMovimentacaoRecebiveis());
        resolveFKNull(stm, 28, confFinan.getPlanoContasTaxaPix().getCodigo());
        resolveFKNull(stm, 29, confFinan.getCentroCustoTaxaPix().getCodigo());
        stm.setString(30, confFinan.getDescricaomovimentacaoautomaticaDebito());
        stm.setString(31, confFinan.getDescricaomovimentacaoautomaticaCredito());
        resolveFKNull(stm, 32, confFinan.getContamovimentacaoautomaticadebito().getCodigo());
        resolveFKNull(stm, 33, confFinan.getContamovimentacaoautomaticacredito().getCodigo());
        resolveFKNull(stm, 34, confFinan.getFavorecidomovimentacaoautomaticaDebito().getCodigo());
        resolveFKNull(stm, 35, confFinan.getFavorecidomovimentacaoautomaticaCredito().getCodigo());
        stm.setBoolean( 36, confFinan.isMovimentacaoAutomaticaRecebiveisConciliacao());
        stm.setBoolean( 37, confFinan.isBuscarFornecedorTodasUnidades());
        stm.setBoolean( 38, confFinan.isOrdemCompraEstoque());
        stm.setBoolean(39, confFinan.isHabilitarAlteracaoValorPrevisto());
        stm.setBoolean(40, confFinan.isAlterarDtPgtoZWAutomaticamenteConc());
        stm.setBoolean(41, confFinan.isObrigarPreenchimentoManualDtCompetencia());

        stm.execute();
    }

    @Override
    public ConfiguracaoFinanceiroVO incluir() throws Exception {
        executarConsulta(" INSERT INTO configuracaofinanceiro(planocontastaxa, "
                + "centrocustostaxa, usarcentraleventos, solicitasenhalancarconta, permitirContaOutraUnidade, especificarCompetencia, habilitarExportacaoAlterData, fecharCaixaAutomaticamente," +
                "bloquearalunochequedevolvido, mensagembloqueio, planocontastaxaboleto, centrocustostaxaboleto, cnpjObrigatorioFornecedor, contaPagarCompraEstoque, contaPagarReceberColabInativo, " +
                "criarContaPagarAutomatico,contaCriarContaPagarAutomatico,apresentarValorPago,permitirTipoPlanoContaFilho, informarfavorecidoaorealizarmovimentacaorecebiveis, alterarDtPgtoZWAutomaticamenteConc;)"
                + "VALUES (null, null, false, true, false, false, false, false, false, null, null, null, false, false, false, false, null, false, false, false, true)", con);
        ConfiguracaoFinanceiroVO conf = new ConfiguracaoFinanceiroVO();
        conf.setCentroCustosTaxa(new CentroCustoTO());
        conf.setPlanoContasTaxa(new PlanoContaTO());
        return conf;
    }

    @Override
    public ConfiguracaoFinanceiroVO consultar() throws Exception {
        ResultSet dados = criarConsulta("select * from configuracaofinanceiro", con);
        if (dados.next()) {
            return montarDados(dados, con);
        } else {
            return incluir();
        }
    }

    @Override
    public boolean consultarUsarMovimentacaoContas() throws Exception {
        ResultSet dados = criarConsulta("select usarMovimentacaoContas from configuracaofinanceiro", con);
        if (dados.next()) {
            return dados.getBoolean("usarMovimentacaoContas");
        } else {
            return false;
        }
    }

    @Override
    public boolean isAlterarDtPgtoZWAutomaticamenteConc() throws Exception {
        ResultSet dados = criarConsulta("select alterarDtPgtoZWAutomaticamenteConc from configuracaofinanceiro", con);
        if (dados.next()) {
            return dados.getBoolean("alterarDtPgtoZWAutomaticamenteConc");
        } else {
            return false;
        }
    }

    public void habilitarDesabilitarFinanceiroAvancado(boolean status) throws Exception {
        try {
            con.setAutoCommit(false);
            executarConsulta("update configuracaofinanceiro set usarMovimentacaoContas = " + status, con);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public static ConfiguracaoFinanceiroVO montarDados(ResultSet dados, Connection con) throws Exception {
        ConfiguracaoFinanceiroVO conf = new ConfiguracaoFinanceiroVO();
        CentroCusto centroCustoDao = new CentroCusto(con);
        Conta conta = new Conta(con);
        Pessoa pessoa = new Pessoa(con);
        PlanoConta planoDao = new PlanoConta(con);

        try {
            conf.setCodigo(dados.getInt("codigo"));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        conf.setUsarMovimentacaoContas(dados.getBoolean("usarmovimentacaocontas"));
        try {
            conf.setCentroCustosTaxa(centroCustoDao.obter(dados.getInt("centrocustostaxa")));
        } catch (Exception e) {
            conf.setCentroCustosTaxa(new CentroCustoTO());
        }
        try {
            conf.setCentroCustoDevolucao(centroCustoDao.obter(dados.getInt("centrocustodevolucao")));
        } catch (Exception e) {
            conf.setCentroCustoDevolucao(new CentroCustoTO());
        }

        try {
            conf.setPlanoContasTaxa(planoDao.obter(dados.getInt("planocontastaxa")));
        } catch (Exception e) {
            conf.setPlanoContasTaxa(new PlanoContaTO());
        }
        try {
            conf.setCentroCustosTaxaBoleto(centroCustoDao.obter(dados.getInt("centrocustostaxaboleto")));
        } catch (Exception e) {
            conf.setCentroCustosTaxaBoleto(new CentroCustoTO());
        }

        try {
            conf.setPlanoContasTaxaBoleto(planoDao.obter(dados.getInt("planocontastaxaboleto")));
        } catch (Exception e) {
            conf.setPlanoContasTaxaBoleto(new PlanoContaTO());
        }
        try {
            conf.setPlanoContasDevolucao(planoDao.obter(dados.getInt("planoContasDevolucao")));
        } catch (Exception e) {
            conf.setPlanoContasDevolucao(new PlanoContaTO());
        }


        try {
            conf.setPlanoContasLancamentoAutomaticoSaida(planoDao.obter(dados.getInt("planoContasLancamentoAutomaticoSaida")));
        } catch (Exception e) {
            conf.setPlanoContasLancamentoAutomaticoSaida(new PlanoContaTO());
        }
        try {
            conf.setPlanoContasLancamentoAutomaticoEntrada(planoDao.obter(dados.getInt("planoContasLancamentoAutomaticoEntrada")));
        } catch (Exception e) {
            conf.setPlanoContasLancamentoAutomaticoEntrada(new PlanoContaTO());
        }
        try {
            conf.setCentroCustoLancamentoAutomaticoSaida(centroCustoDao.obter(dados.getInt("centroCustoLancamentoAutomaticoSaida")));
        } catch (Exception e) {
            conf.setCentroCustoLancamentoAutomaticoSaida(new CentroCustoTO());
        }
        try {
            conf.setCentroCustoLancamentoAutomaticoEntrada(centroCustoDao.obter(dados.getInt("centroCustoLancamentoAutomaticoEntrada")));
        } catch (Exception e) {
            conf.setCentroCustoLancamentoAutomaticoEntrada(new CentroCustoTO());
        }


        conf.setUsarCentralEventos(dados.getBoolean("usarcentraleventos"));
        try {
            conf.setSolicitaSenhaLancarConta(dados.getBoolean("solicitasenhalancarconta"));
        } catch (Exception ex) {}
        try {
            conf.setFecharCaixaAutomaticamente(dados.getBoolean("fecharCaixaAutomaticamente"));
        } catch (Exception ex) {}
        try {
            conf.setPermitirContaOutraUnidade(dados.getBoolean("permitirContaOutraUnidade"));
            conf.setEspecificarCompetencia(dados.getBoolean("especificarCompetencia"));
            conf.setHabilitarExportacaoAlterData(dados.getBoolean("habilitarExportacaoAlterData"));
            conf.setAdicionarDevolucaoRelatorioComissao(dados.getBoolean("adicionarDevolucaoRelatorioComissao"));
            conf.setBloquearAlunoChequeDevolvido(dados.getBoolean("bloquearalunochequedevolvido"));
            conf.setMensagembloqueio(dados.getString("mensagembloqueio"));

        } catch (Exception ignored) {
        }
        try{
            conf.setCnpjObrigatorioFornecedor(dados.getBoolean("cnpjObrigatorioFornecedor"));
        } catch (Exception ignored) {
        }
        try{
            conf.setContaPagarCompraEstoque(dados.getBoolean("contaPagarCompraEstoque"));
        } catch (Exception ignored) {
        }
        try{
            conf.setOrdemCompraEstoque(dados.getBoolean("ordemCompraEstoque"));
        } catch (Exception ignored) {
        }
        try{
            conf.setContaPagarReceberColabInativo(dados.getBoolean("contaPagarReceberColabInativo"));
        } catch (Exception ignored) {
        }
        try{
            conf.setCriarContaPagarAutomatico(dados.getBoolean("criarContaPagarAutomatico"));
        } catch (Exception ignored) {
        }
        try {
            conf.getContaCriarContaPagarAutomaticoVO().setCodigo(dados.getInt("contaCriarContaPagarAutomatico"));
        } catch (Exception ignored) {
        }
        try{
            conf.setApresentarValorPago(dados.getBoolean("apresentarValorPago"));
            conf.setPermitirTipoPlanoContaFilho(dados.getBoolean("permitirTipoPlanoContaFilho"));
        } catch (Exception ignored) {
        }
        try{
            conf.setMetaFinanceiraPorFaturamento(dados.getBoolean("metaFinanceiraPorFaturamento"));
        } catch (Exception ignored) {
        }
        try {
            conf.setInformarFavorecidoAoRealizarMovimentacaoRecebiveis(dados.getBoolean("informarfavorecidoaorealizarmovimentacaorecebiveis"));
        } catch (Exception ignored) {}
        try {
            conf.setPlanoContasTaxaPix(planoDao.obter(dados.getInt("planoContasTaxaPix")));
        } catch (Exception e) {
            conf.setPlanoContasTaxaPix(new PlanoContaTO());
        }
        try {
            conf.setCentroCustoTaxaPix(centroCustoDao.obter(dados.getInt("centroCustoTaxaPix")));
        } catch (Exception e) {
            conf.setCentroCustoTaxaPix(new CentroCustoTO());
        }
        try {
            conf.setDescricaomovimentacaoautomaticaDebito(dados.getString("descricaomovimentacaoautomaticadebito"));
        } catch (Exception e) {
        }
        try {
            conf.setDescricaomovimentacaoautomaticaCredito(dados.getString("descricaomovimentacaoautomaticacredito"));
        } catch (Exception e) {
        }
        try {
            conf.setContamovimentacaoautomaticadebito(conta.consultarPorChavePrimaria(dados.getInt("contamovimentacaoautomaticadebito"),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            conf.setContamovimentacaoautomaticadebito(new ContaVO());
        }
        try {
            conf.setContamovimentacaoautomaticacredito(conta.consultarPorChavePrimaria(dados.getInt("contamovimentacaoautomaticacredito"),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            conf.setContamovimentacaoautomaticacredito(new ContaVO());
        }
        try {
            conf.setFavorecidomovimentacaoautomaticaDebito(pessoa.consultarPorChavePrimaria(dados.getInt("favorecidomovimentacaoautomaticadebito"),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            conf.setFavorecidomovimentacaoautomaticaDebito(new PessoaVO());
        }
        try {
            conf.setFavorecidomovimentacaoautomaticaCredito(pessoa.consultarPorChavePrimaria(dados.getInt("favorecidomovimentacaoautomaticacredito"),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            conf.setFavorecidomovimentacaoautomaticaCredito(new PessoaVO());
        }
        try {
            conf.setMovimentacaoAutomaticaRecebiveisConciliacao(dados.getBoolean("movimentacaoAutomaticaRecebiveisConciliacao"));
        } catch (Exception e) {
        }
        try {
            conf.setBuscarFornecedorTodasUnidades(dados.getBoolean("buscarFornecedorTodasUnidades"));
        } catch (Exception e) {
        }
        try {
           conf.setHabilitarAlteracaoValorPrevisto(dados.getBoolean("alterarValorPrevisto"));
        } catch (Exception e) {
        }
        try {
            conf.setAlterarDtPgtoZWAutomaticamenteConc(dados.getBoolean("alterarDtPgtoZWAutomaticamenteConc"));
        } catch (Exception e) {
        }

        try {
            conf.setObrigarPreenchimentoManualDtCompetencia(dados.getBoolean("obrigarPreenchimentoManualDtCompetencia"));
        } catch (Exception e) {
        }

        try {
            conf.setBloquearCriacaoPlanoConta(dados.getBoolean("bloquearCriacaoPlanoConta"));
        } catch (Exception e) {
        }


        return conf;
    }

    public void estornarReciboCancelamento(Integer codigoContrato) throws Exception {
        try {
            con.setAutoCommit(false);
            executarConsulta("DELETE FROM recibodevolucao WHERE contrato = " + codigoContrato, con);

            executarConsulta("DELETE FROM movconta WHERE codigo IN ( "
                    + " select mc.codigo from movconta mc"
                    + " INNER JOIN movproduto mp ON mp.codigo = mc.movproduto"
                    + " AND mp.contrato =" + codigoContrato + ")", con);

            executarConsulta(" DELETE FROM movproduto WHERE codigo IN ( "
                    + " SELECT mp.codigo FROM movproduto mp"
                    + " INNER JOIN produto p ON p.codigo = mp.produto AND"
                    + " mp.contrato = " + codigoContrato + " AND p.tipoproduto LIKE '" + TipoProduto.DEVOLUCAO.getCodigo() + "')", con);

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void executarAjusteCompensacaoCheque(int nrDiasAdd, Date apartirDe) throws Exception {
        try {
            con.setAutoCommit(false);
            executarConsulta("update cheque set datacompesancao = CAST(datacompesancao AS DATE) + INTERVAL '"+nrDiasAdd+" DAYS' where datacompesancao >= '"+ Uteis.getDataFormatoBD(apartirDe)+"'", con);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }
}
