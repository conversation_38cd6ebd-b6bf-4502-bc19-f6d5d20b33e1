
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.basico.webservice.client.ConsultarLocaisAcesso;
import negocio.comuns.financeiro.BILtvChurnRateEmpresa;
import negocio.comuns.financeiro.BILtvGraficoChurnRateDTO;
import negocio.comuns.financeiro.BIPlanoContaDTO;
import negocio.comuns.financeiro.BIProdutoDTO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.interfaces.financeiro.PlanoContaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PlanoConta extends SuperEntidade implements PlanoContaInterfaceFacade {

    public PlanoConta() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public PlanoConta(Connection con) throws Exception {
        super(con);
        setIdEntidade("Contrato");
    }

    /**
     * @see \negocio.interfaces.financeiro.PlanoContaInterfaceFacade#incluir(br.com.pactosolucoes.finan.modelo.comuns.PlanoContaTO)
     */
    @Override
    public void incluir(final PlanoContaTO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(final PlanoContaTO obj) throws Exception {
        PlanoContaTO.validarDados(obj);
        // Cria a string sql
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO planoconta( " + " codigoplanocontas, nome, tipoes, equivalenciadre, meta, percgasto, defaultdevolucaocheque, insideltv, codigolumi, investimento) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        // Prepara a conexão
        try (PreparedStatement dc = con.prepareStatement(sql.toString())) {
            int i = 0;
            dc.setString(++i, obj.getCodigoPlano());
            dc.setString(++i, obj.getDescricao());

            if (obj.getTipoPadrao() == TipoES.INVESTIMENTO){
                dc.setInt(++i, TipoES.SAIDA.getCodigo());
            } else {
                dc.setInt(++i, obj.getTipoPadrao().getCodigo());
            }
            if (obj.getEquivalenciaDRE() != null) {
                dc.setInt(++i, obj.getEquivalenciaDRE().getCodigo());
            } else {
                dc.setNull(++i, 0);
            }
            if (obj.getMeta() != null) {
                dc.setDouble(++i, obj.getMeta());
            } else {
                dc.setNull(++i, 0);
            }
            if (obj.getPercGastoPretendido() != null) {
                dc.setDouble(++i, obj.getPercGastoPretendido());
            } else {
                dc.setNull(++i, 0);
            }
            dc.setBoolean(++i, obj.isDefaultDevolucaoCheque());
            dc.setBoolean(++i, obj.isInsidirLTV());
            dc.setInt(++i, obj.getCodigoLumi());
            if (obj.getTipoPadrao() == TipoES.INVESTIMENTO){
                dc.setBoolean(++i,true);
            } else {
                dc.setBoolean(++i,false);
            }
            // Executa a consulta
            dc.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        if (obj.isDefaultDevolucaoCheque()) {
            garantirApenasUmDefaultCheque(obj);
        }
        obj.setNovoObj(false);
        atualizarNosFilhos(obj.getCodigoPlano(), obj.getEquivalenciaDRE());
    }

    private void atualizarNosFilhos(String codigoPlanoContas, TipoEquivalenciaDRE equivalencia) throws Exception {
        String sql = "UPDATE planoconta SET equivalenciadre  = " + (equivalencia == null ? "null" : equivalencia.getCodigo()) + " WHERE codigoplanocontas LIKE '" + codigoPlanoContas + "%'";
        executarConsulta(sql, con);
    }

    /**
     * @see \negocio.interfaces.financeiro.PlanoContaInterfaceFacade#alterar(br.com.pactosolucoes.finan.modelo.comuns.PlanoContaTO)
     */
    @Override
    public void alterar(final PlanoContaTO obj) throws Exception {
        PlanoContaTO.validarDados(obj);
        // Cria a String que irá conter o codigo sql
        String sql = "UPDATE planoconta SET codigoplanocontas=?, nome=? , tipoes=?, equivalenciadre = ?, " +
                "meta = ?, percgasto = ?, defaultdevolucaocheque = ?,  insideltv = ?, codigolumi = ?, investimento = ? "
                + "WHERE codigo=?";
        // Prepara a consulta
        try (PreparedStatement dc = con.prepareStatement(sql.toString())) {
            int i = 0;
            dc.setString(++i, obj.getCodigoPlano());
            dc.setString(++i, obj.getDescricao());
            dc.setInt(++i, obj.getTipoPadrao().getCodigo());
            if (obj.getEquivalenciaDRE() == null) {
                dc.setNull(++i, 0);
            } else {
                dc.setInt(++i, obj.getEquivalenciaDRE().getCodigo());
            }
            if (obj.getMeta() == null) {
                dc.setNull(++i, 0);
            } else {
                dc.setDouble(++i, obj.getMeta());
            }
            if (obj.getPercGastoPretendido() == null) {
                dc.setNull(++i, 0);
            } else {
                dc.setDouble(++i, obj.getPercGastoPretendido());
            }
            dc.setBoolean(++i, obj.isDefaultDevolucaoCheque());
            if (obj.isDefaultDevolucaoCheque()) {
                garantirApenasUmDefaultCheque(obj);
            }
            dc.setBoolean(++i, obj.isInsidirLTV());
            dc.setInt(++i, obj.getCodigoLumi());

            if (obj.getTipoPadrao() == TipoES.INVESTIMENTO){
                dc.setBoolean(++i,true);
            } else {
                dc.setBoolean(++i,false);
            }

            dc.setInt(++i, obj.getCodigo());
            // Executa a consulta
            dc.execute();
        }
        if (obj.getCodigoPlano().length() == 3) {
            getFacade().getFinanceiro().getPlanoConta().alterarPlanosFilhos(obj);
        }
    }

    private void garantirApenasUmDefaultCheque(PlanoContaTO plano) throws Exception {

    }

    @Override
    public void alterarSemCommit(PlanoContaTO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterar(obj);
            atualizarNosFilhos(obj.getCodigoPlano(), obj.getEquivalenciaDRE());
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            obj.setCodigo(0);
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarPlanosFilhos(PlanoContaTO obj) throws Exception {
        List<PlanoContaTO> listaPlanosFilhos = getFacade().getFinanceiro().getPlanoConta().consultarPlanoFilhos(obj);
        for (PlanoContaTO planoConta : listaPlanosFilhos) {
            String sql = "UPDATE planoconta SET tipoes=? "
                    + "WHERE codigo= ?";
            // Cria a String que irá conter o codigo sql
            // Prepara a consulta
            Declaracao dc = new Declaracao(sql.toString(), FacadeManager.getFacade().getRisco().getCon());
            int i = 0;
            dc.setInt(++i, obj.getTipoPadrao().getCodigo());
            dc.setInt(++i, planoConta.getCodigo());
            // Executa a consulta
            dc.execute();
        }
    }

    @Override
    public List<PlanoContaTO> consultarPlanoFilhos(PlanoContaTO planoContaPai) throws SQLException, Exception {
        String sql = "SELECT * FROM planoconta"
                + "  where codigoplanocontas like '" + planoContaPai.getCodigoPlano() + ".%' ";
        //+ "  and SPLIT_PART(codigoplanocontas, '" + planoContaPai.getCodigoPlano() + ".',2) NOT SIMILAR TO '%(.)%'";
        List<PlanoContaTO> vetResultado;
        try (ResultSet rsFilhos = SuperFacadeJDBC.criarConsulta(sql, con)) {
            vetResultado = new ArrayList<PlanoContaTO>();
            // Lista que irá guardar os codigos para a tree view
            int[] codigos = new int[10];
            codigos[0] = 1;
            while (rsFilhos.next()) {
                PlanoContaTO obj = new PlanoContaTO();
                obj.setCodigo(rsFilhos.getInt("codigo"));
                String codigoPlano = UteisValidacao.removerZeros(rsFilhos.getString("codigoplanocontas"));
                //obj.setCodigoTrv(UteisValidacao.rearanjar(codigos, codigoPlano));
                obj.setCodigoTrv(codigoPlano);
                obj.setCodigoPlano(rsFilhos.getString("codigoplanocontas"));
                obj.setDescricao(rsFilhos.getString("nome"));
                int cdgTipoPadrao = rsFilhos.getInt("tipoes");
                TipoES tipoPadrao = TipoES.getTipoPadrao(cdgTipoPadrao);
                obj.setTipoPadrao(tipoPadrao);
                obj.setEquivalenciaDRE(TipoEquivalenciaDRE.getTipoEquivalenciaDRE(rsFilhos.getInt("equivalenciadre")));
                obj.setInsidirLTV(rsFilhos.getBoolean("insideltv"));
                obj.setCodigoLumi(rsFilhos.getInt("codigoLumi"));
                obj.setMeta(rsFilhos.getDouble("meta"));
                vetResultado.add(obj);
            }
        }
        return vetResultado;
    }

    @Override
    public void excluir(final PlanoContaTO obj) throws Exception {
        // super.excluirObj(this.getIdEntidade());
        Declaracao dc = new Declaracao(
                "DELETE FROM planoconta WHERE CODIGO = ?", FacadeManager.getFacade().getRisco().getCon());
        dc.setInt(1, obj.getCodigo());
        dc.execute();
    }

    @Override
    public void excluirTodosPlanos() throws Exception {
        Declaracao dc = new Declaracao(
                "DELETE FROM planoconta ", FacadeManager.getFacade().getRisco().getCon());
        dc.execute();
    }

    @Override
    public void moverNos(PlanoContaTO planoDestino, PlanoContaTO planoOrigem)
            throws Exception {
        // CONSULTA OS FILHOS DO PLANO DE ORIGEM
        List<PlanoContaTO> filhos;
        try (ResultSet rsFilhos = SuperFacadeJDBC.criarConsulta("select codigo, codigoplanocontas, nome from planoconta where codigo < 100 and codigoplanocontas like '"
                + planoOrigem.getCodigoPlano()
                + "%' order by codigoplanocontas", con)) {

            filhos = new ArrayList<PlanoContaTO>();
            while (rsFilhos.next()) {
                PlanoContaTO obj = new PlanoContaTO();
                obj.setCodigo(rsFilhos.getInt("codigo"));
                obj.setCodigoPlano(rsFilhos.getString("codigoplanocontas"));
                obj.setDescricao(rsFilhos.getString("nome"));
                filhos.add(obj);
            }
        }
        // SELECIONA O MAIOR NÓ DOS FILHOS DO PLANO DE DESTINO
        String planoDest = planoDestino.getCodigoPlano();
        String codigoProximo = obterCodigoProximoFilho(planoDest);
        // atualiza o nó de origem
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE planoconta ");
        sql.append(" SET codigoplanocontas = ? ");
        sql.append(" WHERE  codigo = ?");
        String novoCodigo = planoDest + "." + codigoProximo;
        for (PlanoContaTO obj : filhos) {
            String cdgTemp = obj.getCodigoPlano().replaceAll(
                    planoOrigem.getCodigoPlano(), novoCodigo);
            try (PreparedStatement query = SuperFacadeJDBC.criarQuery(sql.toString(), con)) {
                query.setString(1, cdgTemp);
                query.setInt(2, obj.getCodigo());
                query.execute();
            }
        }
    }

    @Override
    public String obterCodigoProximoFilho(String codigoPlanoPai)
            throws SQLException, Exception {
        String sql = "";
        if (!UteisValidacao.emptyString(codigoPlanoPai)) {
            sql = "SELECT MAX(SPLIT_PART(codigoplanocontas, '" + codigoPlanoPai + ".', 2)) as codigoplanocontas FROM planoconta "
                    + "where codigoplanocontas like '" + codigoPlanoPai + ".%' "
                    + "and SPLIT_PART(codigoplanocontas, '" + codigoPlanoPai + ".', 2) NOT SIMILAR TO '%(.)%'";
        }
        PlanoContaTO ultimoFilho;
        try (ResultSet rsMaiorFilho = SuperFacadeJDBC.criarConsulta(sql, con)) {
            ultimoFilho = new PlanoContaTO();
            if (rsMaiorFilho.next()) {
                PlanoContaTO obj = new PlanoContaTO();
                obj.setCodigoPlano(rsMaiorFilho.getString("codigoplanocontas"));
                ultimoFilho = obj;
            }
        }
        if (UteisValidacao.emptyString(ultimoFilho.getCodigoPlano())) {
            String ret = codigoPlanoPai;
            if (!ret.isEmpty()) {
                ret += ".";
            }
            ret += "001";
            return ret;
        } else {
            // parte do codigo do ultimo filho (numero apos virgula)
            String ultiFilho = ultimoFilho.getCodigoPlano();
            //numero do ultimo filho + 1
            Integer temp = Integer.parseInt(ultiFilho) + 1;
            //pega o numero antes do ponto e soma com o numero apos a virgula gerado
            String novoCodigoFilho = codigoPlanoPai;
            novoCodigoFilho += ".";
            if (String.valueOf(temp).length() == 1) {
                novoCodigoFilho += "00";
            } else if (String.valueOf(temp).length() == 2) {
                novoCodigoFilho += "0";
            }
            novoCodigoFilho += String.valueOf(temp);
            if (ultiFilho.equals("999")) {
                throw new Exception(
                        "Este plano de contas está cheio, não é permitido cadastrar novos filhos.");
            }
            return novoCodigoFilho;
        }
    }

    private boolean encontrouPlanoPorCodigoInterno(Integer codigoInterno, String tipoConsulta) throws Exception {
        if ((codigoInterno != null) && (codigoInterno > 0)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select * \n");
            sql.append("from planoconta \n");
            sql.append("where codigo = ").append(codigoInterno).append(" \n");
            if (tipoConsulta.equals("SOMENTE_ENTRADA")) {
                sql.append("and tipoes = 1 ");
            } else {
                sql.append("and tipoes = 2 ");
            }
            try (Statement st = con.createStatement()) {
                try (ResultSet rs = st.executeQuery(sql.toString())) {
                    return rs.next();
                }
            }
        }
        return false;
    }


    @Override
    public List<PlanoContaTO> consultar(String codigoplanocontas, String descricao, Integer codigoInterno, String tipoConsulta) throws Exception {

        String sql = "select * from planoconta ";
        boolean achouPlanoPorCodigoInterno = encontrouPlanoPorCodigoInterno(codigoInterno, tipoConsulta);
        if (achouPlanoPorCodigoInterno) {
            sql += " where codigo =" + codigoInterno;
        } else {
            if (!UteisValidacao.emptyString(codigoplanocontas)) {
                sql += " where codigoplanocontas like '" + codigoplanocontas + "%' ";
            }
            if (!UteisValidacao.emptyString(descricao)) {
                if (!sql.contains("where")) {
                    sql += " where upper(nome) like '%" + descricao.toUpperCase() + "%' ";
                } else {
                    sql += " and upper(nome) like '%" + descricao.toUpperCase() + "%' ";
                }
            }
        }

        if (tipoConsulta.equals("SOMENTE_ENTRADA")) {
            if (!sql.contains("where")) {
                sql += " where tipoes = 1 ";
            } else {
                sql += " and tipoes = 1 ";
            }
        } else if (tipoConsulta.equals("SOMENTE_SAIDA")) {
            if (!sql.contains("where")) {
                sql += " where tipoes = 2 ";
            } else {
                sql += " and tipoes = 2 ";
            }
        }
        sql += "order by codigoplanocontas";
        List<PlanoContaTO> vetResultado;
        try (ResultSet rsFilhos = SuperFacadeJDBC.criarConsulta(sql, con)) {
            vetResultado = new ArrayList<PlanoContaTO>();
            // Lista que irá guardar os codigos para a tree view
            int[] codigos = new int[10];
            codigos[0] = 1;

            while (rsFilhos.next()) {
                try {
                    UteisValidacao.validaCodigoFinanceiro(rsFilhos.getString("codigoplanocontas"));
                } catch (Exception e) {
                    try {
                        SuperFacadeJDBC.executarConsultaUpdate("delete from planoconta where codigo = " + rsFilhos.getInt("codigo"), con);
                    } catch (Exception ex) {
                        Uteis.logarDebug("Não foi possível excluir o plano de contas incorreto." + ex.getMessage());
                    }
                    continue;
                }
                PlanoContaTO obj = new PlanoContaTO();
                obj.setCodigo(rsFilhos.getInt("codigo"));
                String codigoPlano = UteisValidacao.removerZeros(rsFilhos.getString("codigoplanocontas"));
                //obj.setCodigoTrv(UteisValidacao.rearanjar(codigos, codigoPlano));
                obj.setCodigoTrv(codigoPlano);
                obj.setCodigoPlano(rsFilhos.getString("codigoplanocontas"));
                obj.setDescricao(rsFilhos.getString("nome"));
                int cdgTipoPadrao = rsFilhos.getInt("tipoes");
                TipoES tipoPadrao = TipoES.getTipoPadrao(cdgTipoPadrao);
                obj.setTipoPadrao(tipoPadrao);
                obj.setEquivalenciaDRE(TipoEquivalenciaDRE.getTipoEquivalenciaDRE(rsFilhos.getInt("equivalenciadre")));
                obj.setInsidirLTV(rsFilhos.getBoolean("insideltv"));
                obj.setCodigoLumi(rsFilhos.getInt("codigoLumi"));

                //verifica se o plano de contas possui rateio de centro de custos
                List<RateioIntegracaoTO> listaRateiosCentroCustos = getFacade().getFinanceiro().getRateioIntegracao().consultarPlanoContaRateio(obj.getCodigo());
                obj.setListaRateiosCentroCustos(listaRateiosCentroCustos);

                //verifica se possui registros filhos
                //esse controle é necessário para permitir ou não a exclusão do plano
                try (ResultSet haFilhos = SuperFacadeJDBC.criarConsulta("select codigo from planoconta where codigoplanocontas like '"
                        + obj.getCodigoPlano() + ".%'", con)) {
                    obj.setLeaf(!haFilhos.next());
                }
                vetResultado.add(obj);
            }
        }
        return vetResultado;
    }

    @Override
    public boolean consultarSeExistePlanoContas() throws Exception {
        String sql = "select exists (select codigo from planoconta limit 1 ) as existe";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    @Override
    public String consultarCodigoPaiContendoSomenteUmNumeroEAdicionaUm() throws SQLException, Exception {
        String sql = "select max(codigoplanocontas) as codigoplanocontas from planoconta  where CHAR_LENGTH(codigoplanocontas) = 3";
        Integer maiorCodigoPai;
        try (ResultSet rsFilhos = SuperFacadeJDBC.criarConsulta(sql, con)) {
            maiorCodigoPai = 0;
            if (rsFilhos.next()) {
                maiorCodigoPai = rsFilhos.getInt("codigoplanocontas");
            }
        }
        maiorCodigoPai = maiorCodigoPai + 1;
        if (String.valueOf(maiorCodigoPai).length() < 3) {
            if (String.valueOf(maiorCodigoPai).length() == 1) {
                return "00" + maiorCodigoPai;
            } else if (String.valueOf(maiorCodigoPai).length() == 2) {
                return "0" + maiorCodigoPai;
            }
        } else {
            throw new Exception("Não é possível cadastrar uma numeração maior que 999");
        }
        return "" + maiorCodigoPai;
    }

    @Override
    public PlanoContaTO obter(int pkey) throws Exception {

        List<PlanoContaTO> vetResultado;
        PlanoContaTO obj;
        try (ResultSet rsFilhos = SuperFacadeJDBC.criarConsulta("select * from planoconta where codigo = "
                + pkey, con)) {

            vetResultado = new ArrayList<PlanoContaTO>();

            // Lista que irá guardar os codigos para a tree view
            int[] codigos = new int[10];
            codigos[0] = 1;
            obj = null;
            if (rsFilhos.next()) {
                obj = new PlanoContaTO();
                obj.setCodigo(rsFilhos.getInt("codigo"));
                String codigoPlano = UteisValidacao.removerZeros(rsFilhos.getString("codigoplanocontas"));
                //obj.setCodigoTrv(UteisValidacao.rearanjar(codigos, codigoPlano));
                obj.setCodigoTrv(codigoPlano);
                obj.setCodigoPlano(rsFilhos.getString("codigoplanocontas"));
                obj.setDescricao(rsFilhos.getString("nome"));
                obj.setMeta(rsFilhos.getDouble("meta"));
                obj.setMetaDesc(Formatador.formatarValorMonetarioSemMoeda(obj.getMeta()));

                obj.setPercGastoPretendido(rsFilhos.getDouble("percGasto"));
                obj.setPercGastoPretendidoDesc(Formatador.formatarValorMonetarioSemMoeda(obj.getPercGastoPretendido()));

                if (TipoES.getTipoPadrao(rsFilhos.getInt("tipoes")) == TipoES.SAIDA && rsFilhos.getBoolean("investimento") == true){
                    obj.setTipoPadrao(TipoES.INVESTIMENTO);
                } else {
                    obj.setTipoPadrao(TipoES.getTipoPadrao(rsFilhos.getInt("tipoes")));
                }
                obj.setEquivalenciaDRE(TipoEquivalenciaDRE.getTipoEquivalenciaDRE(rsFilhos.getInt("equivalenciadre")));
                obj.setInsidirLTV(rsFilhos.getBoolean("insideltv"));
                obj.setCodigoLumi(rsFilhos.getInt("codigoLumi"));
                obj.setInvestimento(rsFilhos.getBoolean("investimento"));
                vetResultado.add(obj);
            }
        }

        if (vetResultado.size() < 1) {
            throw new Exception(
                    "Não foi encontrado nenhum plano de contas cadastrado");
        }

        //verifica se possui registros filhos
        //esse controle é necessário para permitir ou não a exclusão do plano
        try (ResultSet haFilhos = SuperFacadeJDBC.criarConsulta("select codigo from planoconta where codigoplanocontas like '"
                + obj.getCodigoPlano() + ".%'", con)) {
            obj.setLeaf(!haFilhos.next());
        }
        return obj;
    }

    @Override
    public PlanoContaTO consultarPorCodigoPlano(String codigoPlano) throws Exception {
        try (ResultSet consulta = PlanoConta.criarConsulta("SELECT * FROM planoconta WHERE codigoplanocontas LIKE '" + codigoPlano + "'", con)) {
            if (consulta.next()) {
                return montarDados(consulta);
            }
        }
        return new PlanoContaTO();
    }

    @Override
    public PlanoContaTO consultarPorCodigoPlanoTipo(String codigoPlano, TipoES tipoES) throws Exception {
        String sql = "SELECT pc.* FROM planoconta pc \n" +
                " WHERE pc.codigoplanocontas = '" + codigoPlano + "' \n" +
                " AND pc.tipoes = " + tipoES.getCodigo();
        try (ResultSet consulta = criarConsulta(sql, con)) {
            if (consulta.next()) {
                return montarDados(consulta);
            }
        }
        return new PlanoContaTO();
    }

    @Override
    public List<PlanoContaTO> consultarTodos() throws Exception {
        return consultarTodos(0, 0);
    }

    @Override
    public List<PlanoContaTO> consultarTodos(Integer tipoes, Integer empresa) throws Exception {
        List<PlanoContaTO> listaPlanoConta;
        StringBuilder sql = new StringBuilder();
        if (tipoes == 0) {
            sql.append("select * from planoconta order by codigoplanocontas ");
        } else {
            sql.append("select * from planoconta p ");
            sql.append("where 1=1 ");
            sql.append(" and p.tipoes = " + tipoes + " order by p.nome;");
        }
        try (ResultSet resultDados = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            listaPlanoConta = new ArrayList<PlanoContaTO>();
            while (resultDados.next()) {
                listaPlanoConta.add(montarDados(resultDados));
            }
        }
        return listaPlanoConta;
    }

    @Override
    public List<BIPlanoContaDTO> consultarTodosPlanoContaComValoresPorPeriodoEEmpresa(Integer empresa, Date inicio, Date fim) throws Exception {
        List<BIPlanoContaDTO> listaPlanoConta;
        StringBuilder query = new StringBuilder();
        query.append("select p.codigo codigo, p.nome nome, p.insideltv insideltv, \n");
        query.append("(select sum(m.valor) valor from movcontarateio mr \n");
        query.append("inner join movconta m on m.codigo = mr.movconta \n");
        query.append("where 1=1 and mr.planoconta = p.codigo and m.datavencimento between '" + inicio + "' and '" + fim + "' \n");
        query.append("and m.empresa = " + empresa + " ) valor \n");
        query.append("from planoconta p\n");
        query.append("where tipoes = 2\n");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            listaPlanoConta = new ArrayList<>();
            while (rs.next()) {
                BIPlanoContaDTO bIPlanoContaDTO = new BIPlanoContaDTO();
                bIPlanoContaDTO.getPlanoContaTO().setCodigo(rs.getInt("codigo"));
                bIPlanoContaDTO.getPlanoContaTO().setInsidirLTV(rs.getBoolean("insideltv"));
                bIPlanoContaDTO.setSelecionado(rs.getBoolean("insideltv"));
                bIPlanoContaDTO.getPlanoContaTO().setDescricao(rs.getString("nome"));
                try {
                    bIPlanoContaDTO.setValorTotal(rs.getDouble("valor"));
                } catch (Exception e) {
                    bIPlanoContaDTO.setValorTotal(0.0);
                }
                listaPlanoConta.add(bIPlanoContaDTO);
            }
        }
        return listaPlanoConta;
    }

    @Override
    public List<PlanoContaTO> consultarTodasDespesas(Integer empresa) throws Exception {
        return consultarTodos(2, empresa);
    }

    @Override
    public List<PlanoContaTO> consultarTodasReceitas(Integer empresa) throws Exception {
        return consultarTodos(1, empresa);
    }

    @Override
    public List<Double> consultarValorRateioPorPlanoContaDataVencimentoEEmpresa(String codigosPlanoContaSeparadosPorVirgula, Date inicio, Date fim, Integer empresa) throws Exception {
        if(!UteisValidacao.emptyString(codigosPlanoContaSeparadosPorVirgula)) {
            StringBuilder sql = new StringBuilder("select m.valor valor from movcontarateio mr inner join movconta m on m.codigo = mr.movconta ");
            sql.append("where 1=1 ");
            sql.append("and mr.planoconta in (" + codigosPlanoContaSeparadosPorVirgula + ") ");
            sql.append("and m.datavencimento between '" + inicio + "' and '" + fim + "' ");
            if (empresa > 0) {
                sql.append("and m.empresa = " + empresa);
            }
            return montaListaValores(sql, "valor");
        }else{
            return new ArrayList<>();
        }
    }

    @Override
    public List<Double> consultarValoresServicos(Date inicio, Date fim, Integer empresa) throws Exception {
        StringBuilder query = new StringBuilder();
        query.append("select v.valortotal valor from vendaavulsa v ");
        query.append("inner join movproduto m on m.vendaavulsa = v.codigo ");
        query.append("inner join produto p on p.codigo = m.produto ");
        query.append("where 1=1 ");
        query.append("and m.situacao = 'PG' ");
        query.append("and v.tipocomprador = 'CI' ");
        query.append("and p.tipoproduto = 'SE' ");
        query.append("and p.desativado = false ");
        query.append("and v.dataregistro between '" + inicio + "' and '" + fim + "' ");
        if (empresa > 0) {
            query.append("and v.empresa = " + empresa);
        }
        return montaListaValores(query, "valor");
    }

    @Override
    public HashMap<String, Double> valorMedioMensalContratos(Date inicio, Date fim, Integer empresa) throws Exception {
        StringBuilder query = new StringBuilder();
        query.append("select sum(c.valorfinal) valor, sum(cd.numeromeses) meses, (sum(c.valorfinal) / sum(cd.numeromeses)) total \n");
        query.append("from contrato c \n");
        query.append("inner join contratoduracao cd on cd.contrato = c.codigo \n");
        query.append("where 1=1 \n");
        query.append("and c.vigenciade between '" + inicio + "' and '" + fim + "' \n");
        if (empresa > 0) {
            query.append("and c.empresa = " + empresa);
        }
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            if (rs.next()) {
                HashMap<String, Double> map = new HashMap<String, Double>();
                map.put("valor", rs.getDouble("valor"));
                map.put("meses", rs.getDouble("meses"));
                map.put("total", rs.getDouble("total"));
                return map;
            } else {
                return null;
            }
        }
    }

    @Override
    public HashMap<String, Double> valorMedioMensalContratosAtivos(Integer empresa) throws Exception {
        StringBuilder query = new StringBuilder();
        query.append("select sum(c.valorfinal) valor, sum(cd.numeromeses) meses, (sum(c.valorfinal) / sum(cd.numeromeses)) total \n");
        query.append("from contrato c inner join contratoduracao cd on cd.contrato = c.codigo \n");
        query.append("where 1=1 \n");
        query.append("and c.situacao = 'AT' \n");
        if (empresa > 0) {
            query.append("and c.empresa = " + empresa);
        }
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            if (rs.next()) {
                HashMap<String, Double> map = new HashMap<String, Double>();
                map.put("valor", rs.getDouble("valor"));
                map.put("meses", rs.getDouble("meses"));
                map.put("total", rs.getDouble("total"));
                return map;
            } else {
                return null;
            }
        }
    }

    @Override
    public List<Double> consultarValoresProdutos(String codigosProdutosContaSeparadosPorVirgula, Date inicio, Date fim, Integer empresa) throws Exception {
        StringBuilder query = new StringBuilder();
        query.append("select v.valortotal valor from vendaavulsa v ");
        query.append("inner join movproduto m on m.vendaavulsa = v.codigo ");
        query.append("inner join produto p on p.codigo = m.produto ");
        query.append("where 1=1 ");
        query.append("and m.situacao = 'PG' ");
        query.append("and v.tipocomprador = 'CI' ");
        query.append("and p.tipoproduto <> 'SE' ");
        query.append("and v.dataregistro between '" + inicio + "' and '" + fim + "' ");
        query.append("and p.codigo in (" + codigosProdutosContaSeparadosPorVirgula + ") ");
        if (empresa > 0) {
            query.append("and v.empresa = " + empresa);
        }

        return montaListaValores(query, "valor");
    }

    @Override
    public List<BIProdutoDTO> consultarProdutos() throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, descricao, * from produto where tipoproduto not in ('SE','PE','CD','DE','DV','RT','FR','DR','DC','AT','RD','CH','DS','HM') and desativado = false order by descricao", con)) {
            List<BIProdutoDTO> lista = new ArrayList<BIProdutoDTO>();
            while (rs.next()) {
                BIProdutoDTO dto = new BIProdutoDTO();
                dto.setCodigo(rs.getInt("codigo"));
                dto.setDescricao(rs.getString("descricao"));
                lista.add(dto);
            }
            return lista;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public int quantidadeContratosAtivos(Integer empresa) throws Exception {
        int quantidade = 0;
        StringBuilder query = new StringBuilder("select DATE_PART('YEAR', CURRENT_TIMESTAMP) \"ANO\", count(distinct c.codigo) quantidade from contrato c  \n");
        query.append("where 1=1 \n");
        query.append("and c.vigenciade <= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP),'-12-31 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')\n");
        query.append("and c.vigenciaate >= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP),'-01-01 00:00:00'), 'YYYY-MM-DD HH24:MI:SS')\n");
        if (empresa > 0) {
            query.append("and c.empresa = " + empresa + "\n");
        }
        query.append("and c.situacao = 'AT'\n");
        query.append("group by \"ANO\"\n");
        query.append("order by \"ANO\";\n");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            if (rs.next()) {
                quantidade = rs.getInt("quantidade");
            }
            return quantidade;
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public int quantidadeContratosInativos(Date inicio, Date fim, Integer empresa) throws Exception {
        int quantidade = 0;
        StringBuilder query = new StringBuilder("select count(distinct co.codigo) quantidade ");
        query.append("from contrato co where 1=1 ");
        query.append("and co.situacao <> 'AT' ");
        query.append("and co.vigenciaate  between '" + inicio + "' and '" + fim + "' ");
        query.append("and co.situacaocontrato not in ('RE', 'RN', 'AT') ");
        if (empresa > 0) {
            query.append("and co.empresa = " + empresa);
        }
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            if (rs.next()) {
                quantidade = rs.getInt("quantidade");
            }
            return quantidade;
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public double churnRate(Date inicio, Date fim) throws Exception {
        StringBuilder query = new StringBuilder();
        query.append("select CAST(count(distinct co.codigo) AS double precision), (CAST(count(distinct co.codigo) AS double precision) / (select count(distinct c.codigo) from contrato c  \n");
        query.append("where 1=1 \n");
        query.append("and c.vigenciade <= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP),'-12-31 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')\n");
        query.append("and c.vigenciaate >= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP),'-01-01 00:00:00'), 'YYYY-MM-DD HH24:MI:SS')\n");
        query.append("and c.situacao = 'AT') * 100) churn from contrato co \n");
        query.append("where 1=1 \n");
        query.append("and co.vigenciade <= now()\n");
        query.append("and co.vigenciaate  between '" + inicio + "' and now()\n");
        query.append("and co.situacao <> 'AT'\n");
        query.append("and co.situacaocontrato not in ('RE', 'RN')\n");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            if (rs.next()) {
                return rs.getDouble("churn");
            } else {
                return 0.00D;
            }
        } catch (Exception e) {
            return 0.00D;
        }
    }

    @Override
    public double consultarValorRateioPorAluno(Integer codigoCliente) throws Exception {
        double valorfinal = 0.00D;
        StringBuilder query = new StringBuilder("select c.codigo, c.contratoresponsavelrenovacaomatricula, c.contratobaseadorenovacao, sum(mp.totalfinal) valorfinal \n");
        query.append("from contrato c \n");
        query.append(" inner join situacaoclientesinteticodw s on s.codigocontrato = c.codigo \n");
        query.append("inner join movproduto mp on c.codigo = mp.contrato \n");
        query.append("inner join produto pro on pro.codigo = mp.produto \n");
        query.append("where s.codigocliente = ").append(codigoCliente);
        query.append(" and mp.situacao = 'PG' and pro.tipoproduto not in (").append(MovProduto.PRODUTOS_IGNORADOS).append(") \n");
        query.append(" group by 1,2,3 ");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            if (rs.next()) {
                valorfinal += rs.getDouble("valorfinal");
                if(!UteisValidacao.emptyNumber(rs.getInt("contratoresponsavelrenovacaomatricula"))){
                    valorfinal += consultarValorRateioPorAlunoContratosVinculados(rs.getInt("contratoresponsavelrenovacaomatricula"), true);
                }
                if(!UteisValidacao.emptyNumber(rs.getInt("contratobaseadorenovacao"))){
                    valorfinal += consultarValorRateioPorAlunoContratosVinculados(rs.getInt("contratobaseadorenovacao"), false);
                }
            }
            return valorfinal;
        } catch (Exception e) {
            return 0;
        }
    }

    private double consultarValorRateioPorAlunoContratosVinculados(Integer contrato, boolean renovacoesFuturas) {
        double valorfinal = 0.00D;
        StringBuilder query = new StringBuilder("select c.codigo, c.contratoresponsavelrenovacaomatricula, c.contratobaseadorenovacao, sum(mp.totalfinal) valorfinal \n");
        query.append("from contrato c \n");
        query.append("inner join movproduto mp on c.codigo = mp.contrato \n");
        query.append("inner join produto pro on pro.codigo = mp.produto \n");
        query.append("where c.codigo = ").append(contrato);
        query.append(" and mp.situacao = 'PG' and pro.tipoproduto not in (").append(MovProduto.PRODUTOS_IGNORADOS).append(") \n");
        query.append(" group by 1,2,3 ");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            if (rs.next()) {
                valorfinal += rs.getDouble("valorfinal");
                if(!UteisValidacao.emptyNumber(rs.getInt("contratoresponsavelrenovacaomatricula")) && renovacoesFuturas){
                    valorfinal += consultarValorRateioPorAlunoContratosVinculados(rs.getInt("contratoresponsavelrenovacaomatricula"), true);
                }
                if(!UteisValidacao.emptyNumber(rs.getInt("contratobaseadorenovacao")) && !renovacoesFuturas){
                    valorfinal += consultarValorRateioPorAlunoContratosVinculados(rs.getInt("contratobaseadorenovacao"), false);
                }
            }
            return valorfinal;
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public Date consultarDataContratoVigenciaAte(Integer codigoCliente, Integer contrato) {
        StringBuilder query = new StringBuilder();
        query.append("select c.codigo,c.vigenciaateajustada , c.contratoresponsavelrenovacaomatricula  from contrato c\n");
        if(UteisValidacao.emptyNumber(contrato)){
            query.append(" inner join situacaoclientesinteticodw s on s.codigocontrato = c.codigo\n");
            query.append("where s.codigocliente = ").append(codigoCliente);
        } else {
            query.append(" where c.codigo = ").append(contrato);
        }



        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            if (rs.next()) {
                if(UteisValidacao.emptyNumber(rs.getInt("contratoresponsavelrenovacaomatricula"))){
                    return rs.getDate("vigenciaateajustada");
                } else {
                    return consultarDataInicioVinculoAtual(null, rs.getInt("contratobaseadorenovacao"));
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public double valorTotalContratosPorPeriodoEEmpresa(Date inicio, Date fim, Integer empresa) throws Exception {
        double valorContratos = 0;
        StringBuilder query = new StringBuilder();
        query.append("select sum(co.valorfinal) valorContratos from contrato co ");
        query.append("where 1=1 ");
        query.append("and co.vigenciade between '" + inicio + "' and '" + fim + "' ");
        if (empresa > 0) {
            query.append("and empresa = " + empresa);
        }
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            List<BIProdutoDTO> lista = new ArrayList<BIProdutoDTO>();
            if (rs.next()) {
                valorContratos = rs.getDouble("valorContratos");
            }
            return valorContratos;
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public double valorTotalDevolucaoPorPeriodoEEmpresa(Date inicio, Date fim, Integer empresa) throws Exception {
        double valorDevolucao = 0;
        StringBuilder query = new StringBuilder();
        query.append("select sum(d.valorrealdevolucao) valorDevolucao from recibodevolucao d ");
        query.append("inner join contrato c on c.codigo = d.contrato ");
        query.append("where 1=1 ");
        query.append("and datadevolucao between '" + inicio + "' and '" + fim + "' ");
        if (empresa > 0) {
            query.append("and empresa = " + empresa);
        }
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            if (rs.next()) {
                valorDevolucao = rs.getDouble("valorDevolucao");
            }
            return valorDevolucao;
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public int tempoDuracaoMedioContratos(Date inicio, Date fim, Integer empresa) throws Exception {
        int duracao = 0;
        int quant = 0;
        StringBuilder query = new StringBuilder();
        query.append("select pd.numeromeses duracao from contrato c ");
        query.append("inner join plano p on p.codigo = c.plano ");
        query.append("inner join planoduracao pd on pd.plano = p.codigo ");
        query.append("where 1=1 ");
        query.append("and c.vigenciade between '" + inicio + "' and '" + fim + "' ");
        if (empresa > 0) {
            query.append("and c.empresa = " + empresa);
        }
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            while (rs.next()) {
                duracao += rs.getInt("duracao");
                quant++;
            }
            return duracao / quant;
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public boolean exibirBI(Integer codigo) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from bioculto where mostrar = true and codigobi = " + codigo, con)) {
            return rs.next();
        } catch (Exception e) {
            return false;
        }
    }

    private List<Double> montaListaValores(StringBuilder sql, String nomeColuna) {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            List<Double> lista = new ArrayList<Double>();
            while (rs.next()) {
                lista.add(rs.getDouble(nomeColuna));
            }
            return lista;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public HashMap<Integer, List<BILtvGraficoChurnRateDTO>> getContratosGafico(Integer empresa) throws Exception {

        HashMap<Integer, List<BILtvGraficoChurnRateDTO>> map = new HashMap<Integer, List<BILtvGraficoChurnRateDTO>>();
        int anoAtual = Calendar.getInstance().get(Calendar.YEAR);
        int anoPassado = anoAtual - 1;
        int anoRetrasado = anoAtual - 2;
        double quantidadeAnoAtual = 0.00D;
        double quantidadeAnoPassado = 0.00D;
        double quantidadeAnoRetrasado = 0.00D;
        List<BILtvGraficoChurnRateDTO> listaAnoAtual = new ArrayList<BILtvGraficoChurnRateDTO>();
        List<BILtvGraficoChurnRateDTO> listaAnoPassado = new ArrayList<BILtvGraficoChurnRateDTO>();
        List<BILtvGraficoChurnRateDTO> listaAnoRetrasado = new ArrayList<BILtvGraficoChurnRateDTO>();
        BILtvGraficoChurnRateDTO dto;

        StringBuilder query = new StringBuilder("select DATE_PART('YEAR', CURRENT_TIMESTAMP) - 2 \"ANO\", count(distinct c.codigo) quantidade from contrato c  \n");
        query.append("where 1=1 \n");
        query.append("and c.vigenciade <= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP) - 2,'-12-31 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')\n");
        query.append("and c.vigenciaate >= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP) - 2,'-01-00 01:00:00'), 'YYYY-MM-DD HH24:MI:SS')\n");
        if (empresa > 0) {
            query.append("and c.empresa = " + empresa + "\n");
        }
        query.append("and c.situacaocontrato in ('RE', 'RN', 'MA')\n");
        query.append("union\n");
        query.append("select DATE_PART('YEAR', CURRENT_TIMESTAMP) - 1 \"ANO\", count(distinct c.codigo) quantidade from contrato c  \n");
        query.append("where 1=1 \n");
        query.append("and c.vigenciade <= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP) - 1,'-12-31 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')\n");
        query.append("and c.vigenciaate >= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP) - 1,'-01-00 01:00:00'), 'YYYY-MM-DD HH24:MI:SS')\n");
        if (empresa > 0) {
            query.append("and c.empresa = " + empresa + "\n");
        }
        query.append("and c.situacaocontrato in ('RE', 'RN', 'MA')\n");
        query.append("union\n");
        query.append("select DATE_PART('YEAR', CURRENT_TIMESTAMP) \"ANO\", count(distinct c.codigo) quantidade from contrato c  \n");
        query.append("where 1=1 \n");
        query.append("and c.vigenciade <= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP),'-12-31 23:59:59'), 'YYYY-MM-DD HH24:MI:SS') \n");
        query.append("and c.vigenciaate >= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP),'-01-01 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') \n");
        if (empresa > 0) {
            query.append("and c.empresa = " + empresa + "\n");
        }
        query.append("and c.situacao = 'AT'\n");
        query.append("group by \"ANO\"\n");
        query.append("order by \"ANO\";\n");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            while (rs.next()) {
                if (rs.getInt("ANO") == anoAtual) {
                    quantidadeAnoAtual = rs.getDouble("quantidade");
                } else if (rs.getInt("ANO") == anoPassado) {
                    quantidadeAnoPassado = rs.getDouble("quantidade");
                } else if (rs.getInt("ANO") == anoRetrasado) {
                    quantidadeAnoRetrasado = rs.getDouble("quantidade");
                }
            }
        } catch (Exception e) {
            return null;
        }

        query = new StringBuilder("select DATE_PART('YEAR', CURRENT_TIMESTAMP) - 2 \"ANO\", to_char(c.vigenciaate, 'MM') competencia, count(distinct c.codigo) quantidade from contrato c \n");
        query.append("where 1=1 \n");
        query.append("and c.vigenciade <= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP) - 2,'-12-31 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')\n");
        query.append("and c.vigenciaate >= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP) - 2,'-01-01 00:00:00'), 'YYYY-MM-DD HH24:MI:SS')\n");
        if (empresa > 0) {
            query.append("and c.empresa = " + empresa + "\n");
        }
        query.append("and c.situacao <> 'AT'\n");
        query.append("and c.situacaocontrato not in ('RE', 'RN')\n");
        query.append("group by competencia\n");
        query.append("union\n");
        query.append("select DATE_PART('YEAR', CURRENT_TIMESTAMP) - 1 \"ANO\", to_char(c.vigenciaate, 'MM') competencia, count(distinct c.codigo) quantidade from contrato c \n");
        query.append("where 1=1 \n");
        query.append("and c.vigenciade <= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP) - 1,'-12-31 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')\n");
        query.append("and c.vigenciaate >= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP) - 1,'-01-01 00:00:00'), 'YYYY-MM-DD HH24:MI:SS')\n");
        if (empresa > 0) {
            query.append("and c.empresa = " + empresa + "\n");
        }
        query.append("and c.situacao <> 'AT'\n");
        query.append("and c.situacaocontrato not in ('RE', 'RN')\n");
        query.append("group by competencia\n");
        query.append("union\n");
        query.append("select DATE_PART('YEAR', CURRENT_TIMESTAMP) \"ANO\", to_char(c.vigenciaate, 'MM') competencia, count(distinct c.codigo) quantidade from contrato c \n");
        query.append("where 1=1 \n");
        query.append("and c.vigenciade <= now()\n");
        query.append("and c.vigenciaate between TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP),'-01-01 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') and now()\n");
        if (empresa > 0) {
            query.append("and c.empresa = " + empresa + "\n");
        }
        query.append("and c.situacao <> 'AT'\n");
        query.append("and c.situacaocontrato not in ('RE', 'RN')\n");
        query.append("group by competencia\n");
        query.append("order by \"ANO\", competencia\n");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            while (rs.next()) {
                dto = new BILtvGraficoChurnRateDTO();
                if (rs.getInt("ANO") == anoAtual) {
                    dto.setAnoEvasao(rs.getInt("ANO"));
                    dto.setCompetenciaEvasao(rs.getInt("competencia"));
                    dto.setChurnRate(rs.getDouble("quantidade") / quantidadeAnoAtual);
                    listaAnoAtual.add(dto);
                } else if (rs.getInt("ANO") == anoPassado) {
                    dto.setAnoEvasao(rs.getInt("ANO"));
                    dto.setCompetenciaEvasao(rs.getInt("competencia"));
                    dto.setChurnRate(rs.getDouble("quantidade") / quantidadeAnoAtual);
                    listaAnoPassado.add(dto);
                } else if (rs.getInt("ANO") == anoRetrasado) {
                    dto.setAnoEvasao(rs.getInt("ANO"));
                    dto.setCompetenciaEvasao(rs.getInt("competencia"));
                    dto.setChurnRate(rs.getDouble("quantidade") / quantidadeAnoAtual);
                    listaAnoRetrasado.add(dto);
                }
            }
        } catch (Exception e) {
            return null;
        }
        map.put(anoAtual, listaAnoAtual);
        map.put(anoPassado, listaAnoPassado);
        map.put(anoRetrasado, listaAnoRetrasado);
        return map;
    }

    @Override
    public List<BILtvChurnRateEmpresa> churnRatePorEmpresa() throws Exception {
        List<BILtvChurnRateEmpresa> lista = new ArrayList<BILtvChurnRateEmpresa>();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, nome from empresa order by nome;", con)) {
            BILtvChurnRateEmpresa dto;
            while (rs.next()) {
                dto = new BILtvChurnRateEmpresa();
                dto.setCodigoEmpresa(rs.getInt("codigo"));
                dto.setNomeEmpresa(rs.getString("nome"));
                lista.add(dto);
            }
        } catch (Exception e) {
            return null;
        }
        for (BILtvChurnRateEmpresa dto : lista) {
            StringBuilder query = new StringBuilder("select to_char(c.vigenciaate, 'MM') competencia, count(distinct c.codigo) quantidade, (select count(distinct c.codigo) from contrato c  \n");
            query.append("where 1=1 \n");
            query.append("and c.vigenciade <= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP),'-12-31 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')\n");
            query.append("and c.vigenciaate >= TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP),'-01-01 00:00:00'), 'YYYY-MM-DD HH24:MI:SS')\n");
            query.append("and c.empresa = " + dto.getCodigoEmpresa() + " \n");
            query.append("and c.situacao = 'AT') quantidadeDividir from contrato c \n");
            query.append("where 1=1 \n");
            query.append("and c.vigenciade <= now()\n");
            query.append("and c.vigenciaate between TO_TIMESTAMP(concat(DATE_PART('YEAR', CURRENT_TIMESTAMP),'-01-01 00:00:00'), 'YYYY-MM-DD HH24:MI:SS') and now()\n");
            query.append("and c.empresa = " + dto.getCodigoEmpresa() + " \n");
            query.append("and c.situacao <> 'AT'\n");
            query.append("and c.situacaocontrato not in ('RE', 'RN')\n");
            query.append("group by competencia\n");
            query.append("order by competencia;\n");
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
                while (rs.next()) {
                    double quantidade = 0.00D;
                    try {
                        double quantidadeTotal = rs.getDouble("quantidade");
                        double quantidadeDividir = rs.getDouble("quantidadeDividir");
                        if (quantidadeTotal > 0 && quantidadeDividir > 0) {
                            quantidade = quantidadeTotal / quantidadeDividir * 100;
                        }
                    } catch (Exception e) {
                        quantidade = 0.00D;
                    }
                    switch (rs.getInt("competencia")) {
                        case 1:
                            dto.setJaneiro(quantidade);
                            break;
                        case 2:
                            dto.setFevereiro(quantidade);
                        case 3:
                            dto.setMarco(quantidade);
                            break;
                        case 4:
                            dto.setAbril(quantidade);
                            break;
                        case 5:
                            dto.setMaio(quantidade);
                            break;
                        case 6:
                            dto.setJunho(quantidade);
                            break;
                        case 7:
                            dto.setJulho(quantidade);
                            break;
                        case 8:
                            dto.setAgosto(quantidade);
                            break;
                        case 9:
                            dto.setSetembro(quantidade);
                            break;
                        case 10:
                            dto.setOutubro(quantidade);
                            break;
                        case 11:
                            dto.setNovembro(quantidade);
                            break;
                        case 12:
                            dto.setDezembro(quantidade);
                            break;
                    }
                }
            } catch (Exception e) {
                return null;
            }
        }
        return lista;
    }

    @Override
    public List<PlanoContaTO> consultarPorEquivalenciaDRE(int codigoEquivalenciaDRE) throws Exception {
        List<PlanoContaTO> listaPlanoConta;
        try (ResultSet resultDados = SuperFacadeJDBC.criarConsulta("select * from planoconta where equivalenciaDRE = " + codigoEquivalenciaDRE + " order by codigoplanocontas ", con)) {
            listaPlanoConta = new ArrayList<PlanoContaTO>();
            while (resultDados.next()) {
                listaPlanoConta.add(montarDados(resultDados));
            }
        }
        return listaPlanoConta;
    }

    public PlanoContaTO montarDados(ResultSet resultDados) throws Exception {
        PlanoContaTO obj = new PlanoContaTO();
        obj.setCodigo(resultDados.getInt("codigo"));
        obj.setCodigoPlano(resultDados.getString("codigoplanocontas"));
        obj.setDescricao(resultDados.getString("nome"));
        if (TipoES.getTipoPadrao(resultDados.getInt("tipoes")) == TipoES.SAIDA && resultDados.getBoolean("investimento") == true){
            obj.setTipoPadrao(TipoES.INVESTIMENTO);
        } else {
            obj.setTipoPadrao(TipoES.getTipoPadrao(resultDados.getInt("tipoes")));
        }
        obj.setEquivalenciaDRE(TipoEquivalenciaDRE.getTipoEquivalenciaDRE(resultDados.getInt("equivalenciadre")));
        obj.setMeta(resultDados.getDouble("meta"));
        obj.setPercGastoPretendido(resultDados.getDouble("percGasto"));
        obj.setPercGastoPretendidoDesc(Formatador.formatarValorMonetarioSemMoeda(obj.getPercGastoPretendido()));
        obj.setInsidirLTV(resultDados.getBoolean("insideltv"));
        obj.setCodigoLumi(resultDados.getInt("codigoLumi"));
        obj.setInvestimento(resultDados.getBoolean("investimento"));
        //verifica se o plano de contas possui rateio de centro de custos
        RateioIntegracao rateioIntegracaoDAO = new RateioIntegracao(con);
        List<RateioIntegracaoTO> listaRateiosCentroCustos = rateioIntegracaoDAO.consultarPlanoContaRateio(obj.getCodigo());
        rateioIntegracaoDAO = null;
        obj.setListaRateiosCentroCustos(listaRateiosCentroCustos);
        return obj;
    }

    /**
     * <AUTHOR>
     * 21/11/2011
     */
    @Override
    public Boolean verificarExistenciaPlano(String codigoPlano) throws Exception {
        return PlanoConta.criarConsulta("SELECT * FROM planoconta WHERE codigoplanocontas LIKE '" + codigoPlano + "'", con).next();
    }

    /**
     * Responsável por obter Codigo dos Filhos do plano de código informado como parametro
     *
     * <AUTHOR>
     * 22/11/2011
     */
    @Override
    public Map<Integer, String> obterCodigoPlanoIrmaos(String codigoPai) throws Exception {
        Map<Integer, String> codigosFilhos = new HashMap<Integer, String>();
        if (!UteisValidacao.emptyString(codigoPai)) {
            codigoPai = codigoPai + ".";
        }
        try (ResultSet rs = PlanoConta.criarConsulta("SELECT codigo, codigoplanocontas FROM planoconta WHERE codigoplanocontas LIKE '" + codigoPai + "___'", con)) {
            while (rs.next()) {
                codigosFilhos.put(rs.getInt("codigo"), rs.getString("codigoplanocontas"));
            }
        }
        return codigosFilhos;
    }

    /**
     * Responsável por atualizar o Codigo do PlanoContas
     *
     * <AUTHOR>
     * 22/11/2011
     */
    @Override
    public void atualizarCodigoPlanoContas(Integer codigoPlano, String codigoNovo, String codigoAntesAlteracao, List<Integer> codigosFilhos, Integer tipoES) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE planoconta SET codigoplanocontas = '" + codigoNovo + "' WHERE codigo = " + codigoPlano + ";\n");
        for (Integer codigo : codigosFilhos) {
            sql.append("UPDATE planoconta SET codigoplanocontas = '");
            sql.append(codigoNovo + "'||SUBSTRING (codigoplanocontas ," + (codigoAntesAlteracao.length() + 1) + ", LENGTH(codigoplanocontas) )  ");
            if (!UteisValidacao.emptyNumber(tipoES)) {
                sql.append(", tipoes = " + tipoES);
            }
            sql.append(" WHERE codigo = " + codigo + ";\n");
        }
        PlanoConta.executarConsulta(sql.toString(), con);
    }

    @Override
    public List<Integer> obterCodigoFilhos(String codigoPai, String exceto) throws Exception {
        List<Integer> codigosFilhos = new ArrayList<Integer>();
        if (!UteisValidacao.emptyString(codigoPai)) {
            codigoPai = codigoPai + ".";
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo FROM planoconta WHERE codigoplanocontas LIKE '" + codigoPai + "%' ");
        if (!UteisValidacao.emptyString(exceto)) {
            sql.append("AND codigoplanocontas NOT LIKE '" + exceto + "%'");
        }
        try (ResultSet rs = PlanoConta.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                codigosFilhos.add(rs.getInt("codigo"));
            }
        }
        return codigosFilhos;
    }

    /**
     * <AUTHOR>
     * 25/11/2011
     */
    @Override
    public Integer obterTipoES(String codigoPlano) throws Exception {
        try (ResultSet rs = PlanoConta.criarConsulta("SELECT tipoes FROM planoconta WHERE codigoplanocontas LIKE '" + codigoPlano + "'", con)) {
            rs.next();
            return rs.getInt("tipoes");
        }
    }

    private void consultarTodosPlanoPai(PlanoContaTO planoContaTo, List<PlanoContaTO> lista) throws Exception {
        String codigoPlanoConta = planoContaTo.getCodigoPlano();
        while (codigoPlanoConta.length() != 3) {
            codigoPlanoConta = codigoPlanoConta.substring(0, codigoPlanoConta.length() - 4);
            PlanoContaTO planoConta = consultarPorCodigoPlano(codigoPlanoConta);
            if (!lista.contains(planoConta))
                lista.add(planoConta);
        }

    }

    private List<PlanoContaTO> montarDadosPlanoContaMovContaRateio(ResultSet dados) throws Exception {
        List<PlanoContaTO> lista = new ArrayList<PlanoContaTO>();
        while (dados.next()) {
            PlanoContaTO obj = montarDados(dados);
            if (!lista.contains(obj)) {
                lista.add(obj);
                // Consultar todos os plano de contas Pai.
                consultarTodosPlanoPai(obj, lista);
            }
        }
        return lista;
    }

    public List<PlanoContaTO> consultarPorCaixa(int codigoCaixa) throws Exception {
        /*
         * 12/04/12 Ulisses... Consultar somente os plano de contas que foi utilizado no caixa.
         */
        StringBuilder sb = new StringBuilder();
        sb.append("select pc.*, mcr.descricao from planoConta pc ");
        sb.append("inner join movContaRateio mcr on mcr.planoconta = pc.codigo ");
        sb.append("inner join caixaMovConta cmc on cmc.movConta = mcr.movconta ");
        sb.append("where cmc.caixa = ").append(codigoCaixa);
        sb.append(" order by codigoPlanoContas ");
        List<PlanoContaTO> lista;
        try (PreparedStatement pst = this.con.prepareStatement(sb.toString())) {
            try (ResultSet dados = pst.executeQuery()) {
                lista = montarDadosPlanoContaMovContaRateio(dados);
            }
        }
        Collections.sort(lista, new PlanoContaTO().new ComparatorPorCodigoPlanoConta());

        return lista;

    }


    public Map<String, TipoEquivalenciaDRE> obterEquivalencias() throws Exception {
        Map<String, TipoEquivalenciaDRE> mapa = new HashMap<String, TipoEquivalenciaDRE>();
        try (ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select codigoplanocontas, equivalenciadre from planoconta", con)) {
            while (resultSet.next()) {
                mapa.put(resultSet.getString(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(resultSet.getInt(2)));
            }
        }
        return mapa;
    }


    public void trocarPlanoContas(Integer codigoPlanoAntigo, Integer codigoPlanoNovo) throws Exception {
        executarConsulta(" UPDATE movcontarateio SET planoconta = " + codigoPlanoNovo + " WHERE planoconta = " + codigoPlanoAntigo, con);
        executarConsulta(" UPDATE rateiointegracao SET planoconta = " + codigoPlanoNovo + " WHERE planoconta = " + codigoPlanoAntigo, con);
        executarConsulta(" UPDATE configuracaofinanceiro SET planocontastaxa = " + codigoPlanoNovo + " WHERE planocontastaxa = " + codigoPlanoAntigo, con);
        executarConsulta(" UPDATE fornecedor SET planoconta = " + codigoPlanoNovo + " WHERE planoconta = " + codigoPlanoAntigo, con);
    }

    public boolean verificarExisteRelacionamento() {
        try {
            return existe("SELECT * FROM movcontarateio WHERE planoconta IS NOT NULL", getCon())
                    || existe("select * from rateiointegracao WHERE planoconta IS NOT NULL", getCon())
                    || existe("select * from configuracaofinanceiro  where planocontastaxa IS NOT NULL", getCon());
        } catch (Exception e) {
            return true;
        }
    }

    public boolean verificarExisteRelacionamento(Integer codigo) {
        try {
            return existe("SELECT * FROM movcontarateio WHERE planoconta = " + codigo, getCon())
                    || existe("select * from rateiointegracao WHERE planoconta = " + codigo, getCon())
                    || existe("select * from configuracaofinanceiro  where planocontastaxa = " + codigo, getCon())
                    || existe("select * from fornecedor  where planoconta = " + codigo, getCon());
        } catch (Exception e) {
            return true;
        }
    }

    public void rePovoarPlanoContas() throws Exception {
        executarConsulta("DELETE FROM planoconta", con);
        PlanoContasSugeridoServico povoador = new PlanoContasSugeridoServico(getCon());
        povoador.povoar();
    }

    public PlanoContaTO consultarPorChavePrimaria(Integer codigo) throws Exception {
        try (ResultSet consulta = PlanoConta.criarConsulta("SELECT * FROM planoconta WHERE codigo = " + codigo, con)) {
            if (consulta.next()) {
                return montarDados(consulta);
            }
        }
        return new PlanoContaTO();
    }

    public Integer consultarCodigoLumi(Integer codigo) throws Exception {
        if(codigo != null) {
            try (ResultSet consulta = PlanoConta.criarConsulta("SELECT codigoLumi FROM planoconta WHERE codigo = " + codigo, con)) {
                if (consulta.next()) {
                    return consulta.getInt("codigoLumi");
                }
            }
        }

        return 0;
    }

    public Date consultarDataInicioVinculoAtual(Integer codigoCliente, Integer contrato) {
        StringBuilder query = new StringBuilder();
        query.append("select c.codigo, c.vigenciade, c.contratobaseadorenovacao from contrato c\n");
        if(UteisValidacao.emptyNumber(contrato)){
            query.append(" inner join situacaoclientesinteticodw s on s.codigocontrato = c.codigo\n");
            query.append("where s.codigocliente = ").append(codigoCliente);
        } else {
            query.append(" where c.codigo = ").append(contrato);
        }



        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(query.toString(), con)) {
            if (rs.next()) {
                if(UteisValidacao.emptyNumber(rs.getInt("contratobaseadorenovacao"))){
                    return rs.getDate("vigenciade");
                } else {
                    return consultarDataInicioVinculoAtual(null, rs.getInt("contratobaseadorenovacao"));
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

}
