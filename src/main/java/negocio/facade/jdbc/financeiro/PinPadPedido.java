package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PinPadPedidoMovParcelaVO;
import negocio.comuns.financeiro.PinPadPedidoVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.StatusPinpadEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import org.json.JSONObject;
import servicos.impl.stone.connect.DadosPedidoDTO;
import servicos.impl.stone.connect.PedidoRetornoDTO;
import servicos.impl.stone.connect.StoneConnectService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PinPadPedido extends SuperEntidade {

    public PinPadPedido() throws Exception {
        super();
    }
    public PinPadPedido(Connection con) throws Exception {
        super(con);
    }

    public void incluir(PinPadPedidoVO obj) throws Exception {
        String sql = "INSERT INTO PinPadPedido(dataRegistro, pinpad, empresa, pessoa, " +
                "valor, idExterno, pdvPinPad, status, paramsEnvio, paramsResp, conveniocobranca, " +
                "origem, movpagamento, recibopagamento, dadospedido, formapagamento, usuario, idExternoCancel, " +
                "paramsRespCancel, pinPadPedidoOrigem) "
                + "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) RETURNING codigo";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setInt(++i, obj.getPinpad().getCodigo());
            resolveIntegerNull(pst, ++i, obj.getEmpresaVO().getCodigo());
            resolveIntegerNull(pst, ++i, obj.getPessoaVO().getCodigo());
            pst.setDouble(++i, obj.getValor());
            pst.setString(++i, obj.getIdExterno());
            pst.setString(++i, obj.getPdvPinPad());
            pst.setInt(++i, obj.getStatus().getCodigo());
            pst.setString(++i, obj.getParamsEnvio());
            pst.setString(++i, obj.getParamsResp());
            resolveIntegerNull(pst, ++i, obj.getConvenioCobrancaVO().getCodigo());
            pst.setInt(++i, obj.getOrigem().getCodigo());
            resolveIntegerNull(pst, ++i, obj.getMovPagamentoVO().getCodigo());
            resolveIntegerNull(pst, ++i, obj.getReciboPagamentoVO().getCodigo());
            pst.setString(++i, obj.getDadosPedidoDTO().toString());
            resolveIntegerNull(pst, ++i, obj.getFormaPagamentoVO().getCodigo());
            resolveIntegerNull(pst, ++i, obj.getUsuarioVO().getCodigo());
            pst.setString(++i, obj.getIdExternoCancel());
            pst.setString(++i, obj.getParamsRespCancel());
            resolveIntegerNull(pst, ++i, obj.getPinPadPedidoOrigem());

            ResultSet rsCodigo = pst.executeQuery();
            rsCodigo.next();
            obj.setCodigo(rsCodigo.getInt(1));
            obj.setNovoObj(false);
        }
    }

    public void alterar(PinPadPedidoVO obj) throws Exception{
        String update = "UPDATE PinPadPedido SET pinpad = ?, empresa = ?, pessoa = ?, valor = ?, " +
                "idExterno = ?, pdvPinPad = ?, status = ?, paramsEnvio = ?, paramsResp = ?, " +
                "conveniocobranca = ?, origem = ?, movpagamento = ?, recibopagamento = ?, dadospedido = ?, " +
                "formapagamento = ?, usuario = ?, idExternoCancel = ?, paramsRespCancel = ?, pinPadPedidoOrigem = ? " +
                "WHERE codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(update)) {
            int i = 0;
            pst.setInt(++i, obj.getPinpad().getCodigo());
            resolveIntegerNull(pst, ++i, obj.getEmpresaVO().getCodigo());
            resolveIntegerNull(pst, ++i, obj.getPessoaVO().getCodigo());
            pst.setDouble(++i, obj.getValor());
            pst.setString(++i, obj.getIdExterno());
            pst.setString(++i, obj.getPdvPinPad());
            pst.setInt(++i, obj.getStatus().getCodigo());
            pst.setString(++i, obj.getParamsEnvio());
            pst.setString(++i, obj.getParamsResp());
            resolveIntegerNull(pst, ++i, obj.getConvenioCobrancaVO().getCodigo());
            pst.setInt(++i, obj.getOrigem().getCodigo());
            resolveIntegerNull(pst, ++i, obj.getMovPagamentoVO().getCodigo());
            resolveIntegerNull(pst, ++i, obj.getReciboPagamentoVO().getCodigo());
            pst.setString(++i, obj.getDadosPedidoDTO().toString());
            resolveIntegerNull(pst, ++i, obj.getFormaPagamentoVO().getCodigo());
            resolveIntegerNull(pst, ++i, obj.getUsuarioVO().getCodigo());
            pst.setString(++i, obj.getIdExternoCancel());
            pst.setString(++i, obj.getParamsRespCancel());
            resolveIntegerNull(pst, ++i, obj.getPinPadPedidoOrigem());

            pst.setInt(++i, obj.getCodigo());
            pst.execute();
        }
    }

    public void alterarStatus(PinPadPedidoVO obj) throws Exception{
        String update = "UPDATE PinPadPedido SET status = ? WHERE codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(update)) {
            int i = 0;
            pst.setInt(++i, obj.getStatus().getCodigo());
            pst.setInt(++i, obj.getCodigo());
            pst.execute();
        }
    }

    public PinPadPedidoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        PinPadPedidoVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            if (!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
                Empresa empresaDAO = new Empresa(this.con);
                obj.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
                empresaDAO = null;
            }

            if (!UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
                Pessoa pessoaDAO;
                try {
                    pessoaDAO = new Pessoa(this.con);
                    obj.setPessoaVO(pessoaDAO.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    pessoaDAO = null;
                }
            }
        }
        return obj;
    }

    public PinPadPedidoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        PinPadPedidoVO obj = new PinPadPedidoVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setIdExterno(dadosSQL.getString("idExterno"));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setPdvPinPad(dadosSQL.getString("pdvPinPad"));
        obj.setStatus(StatusPinpadEnum.fromCodigo(dadosSQL.getInt("status")));
        obj.setPinpad(OpcoesPinpadEnum.fromCodigo(dadosSQL.getInt("pinpad")));
        obj.setParamsEnvio(dadosSQL.getString("paramsEnvio"));
        obj.setParamsResp(dadosSQL.getString("paramsResp"));
        obj.getConvenioCobrancaVO().setCodigo(dadosSQL.getInt("conveniocobranca"));
        obj.setOrigem(OrigemCobrancaEnum.obterPorCodigo(dadosSQL.getInt("origem")));
        obj.getMovPagamentoVO().setCodigo(dadosSQL.getInt("movpagamento"));
        obj.getReciboPagamentoVO().setCodigo(dadosSQL.getInt("recibopagamento"));
        obj.getFormaPagamentoVO().setCodigo(dadosSQL.getInt("formapagamento"));
        obj.getUsuarioVO().setCodigo(dadosSQL.getInt("usuario"));
        try {
            if (!UteisValidacao.emptyString(dadosSQL.getString("dadospedido"))) {
                obj.setDadosPedidoDTO(JSONMapper.getObject(new JSONObject(dadosSQL.getString("dadospedido")), DadosPedidoDTO.class));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            obj.setIdExternoCancel(dadosSQL.getString("idExternoCancel"));
            obj.setParamsRespCancel(dadosSQL.getString("paramsRespCancel"));
            obj.setPinPadPedidoOrigem(dadosSQL.getInt("pinPadPedidoOrigem"));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return obj;
    }

    public PinPadPedidoVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        String sql = "SELECT * FROM pinpadpedido WHERE codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigo);
            try (ResultSet rs = pst.executeQuery()) {
                if (!rs.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( PinPadPedido: " + codigo + " ).");
                }
                return montarDadosBasico(rs);
            }
        }
    }

    public PinPadPedidoVO consultarPorIdExterno(String idExterno) throws Exception {
        String sql = "SELECT * FROM pinpadpedido WHERE idExterno = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setString(1, idExterno);
            try (ResultSet rs = pst.executeQuery()) {
                if (!rs.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( PinPadPedido: " + idExterno + " ).");
                }
                return montarDadosBasico(rs);
            }
        }
    }

    public List<PinPadPedidoVO> consultar(Integer recibo, Integer movpagamento, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT * FROM pinpadpedido WHERE 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(movpagamento)) {
            sql.append("AND movpagamento = ").append(movpagamento).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(recibo)) {
            sql.append("AND recibopagamento = ").append(recibo).append(" \n");
        }
        try (PreparedStatement statement = con.prepareStatement(sql.toString())) {
            ResultSet resultSet = statement.executeQuery();
            List<PinPadPedidoVO> lista = new ArrayList<>();
            while (resultSet.next()) {
                try {
                    lista.add(montarDados(resultSet, nivelMontarDados));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            return lista;
        }
    }

    public PinPadPedidoVO criarPedido(PessoaVO pessoaVO, Double valor, OpcoesPinpadEnum opcoesPinpadEnum,
                                      EmpresaVO empresaVO, ConvenioCobrancaVO convenioCobrancaVO, String pdvPinpad,
                                      FormaPagamentoVO formaPagamentoVO, OrigemCobrancaEnum origemCobrancaEnum,
                                      UsuarioVO usuarioVO, List<MovParcelaVO> movParcelaVOS) throws Exception {
        PinPadPedidoVO obj = new PinPadPedidoVO();
        obj.setDataRegistro(Calendario.hoje());
        obj.setStatus(StatusPinpadEnum.GERADO);
        obj.setPinpad(opcoesPinpadEnum);
        obj.setEmpresaVO(empresaVO);
        obj.setPessoaVO(pessoaVO);
        obj.setConvenioCobrancaVO(convenioCobrancaVO);
        obj.setFormaPagamentoVO(formaPagamentoVO);
        obj.setUsuarioVO(usuarioVO);
        obj.setValor(valor);
        obj.setPdvPinPad(pdvPinpad);
        obj.setOrigem(origemCobrancaEnum);
        if (!UteisValidacao.emptyList(movParcelaVOS)) {
            List<Integer> codParcelas = new ArrayList<>();

            movParcelaVOS.forEach(movParcela -> {codParcelas.add(movParcela.getCodigo());});
            DadosPedidoDTO dadosPedido = new DadosPedidoDTO();
            dadosPedido.setParcelas(codParcelas);
            obj.setDadosPedidoDTO(dadosPedido);
        }

        //incluir pinpadpedido
        incluir(obj);

        //incluir pinpadpedidomovparcela
        PinPadPedidoMovParcela pinPadPedidoMovParcela;
        try {
            pinPadPedidoMovParcela = new PinPadPedidoMovParcela(getCon());
            if (!UteisValidacao.emptyList(movParcelaVOS)) {
                for (MovParcelaVO movParcelaVO : movParcelaVOS) {
                    PinPadPedidoMovParcelaVO pinPadPedidoMovParcelaVO = new PinPadPedidoMovParcelaVO();
                    pinPadPedidoMovParcelaVO.setPinpadpedido(obj.getCodigo());
                    pinPadPedidoMovParcelaVO.setMovparcela(movParcelaVO.getCodigo());
                    pinPadPedidoMovParcela.incluir(pinPadPedidoMovParcelaVO);
                }
            }
        } catch (Exception ex) {
        } finally {
            pinPadPedidoMovParcela = null;
        }

        return obj;
    }

    public List<PinPadPedidoVO> consultarPorPeriodo(Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception {
        String sql = "SELECT p.* " +
                "FROM PinPadPedido p " +
                "WHERE p.dataregistro::date between '" + Uteis.getDataFormatoBD(dataInicial) + "' and '" + Uteis.getDataFormatoBD(dataFinal) + "' " +
                "ORDER BY p.dataregistro desc;";

        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        List<PinPadPedidoVO> lista = new ArrayList<>();
        while (resultSet.next()) {
            try {
                lista.add(montarDados(resultSet, nivelMontarDados));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }

    public PedidoRetornoDTO consultarPedido(PinPadPedidoVO pedidoVO) {
        return StoneConnectService.obterPedidoRetornoDTO(pedidoVO, this.con);
    }

    public void ajustarPedidos() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("p2.codigo as pinpadpedido, \n");
        sql.append("(p2.movpagamento is null) as semMovPagamento, \n");
        sql.append("(p2.recibopagamento  is null) as semRecibo, \n");
        sql.append("sql.* \n");
        sql.append("from ( \n");
        sql.append("select \n");
        sql.append("mp.codigo as movpagamento, \n");
        sql.append("mp.recibopagamento as recibo, \n");
        sql.append("(split_part(split_part(mp.respostarequisicaopinpad,'pinpadPedido\":',2),',',1))::integer as pinpadpedido \n");
        sql.append("from movpagamento mp \n");
        sql.append("inner join pessoa p on p.codigo = mp.pessoa \n");
        sql.append("where length(coalesce(mp.respostarequisicaopinpad,'')) > 0 \n");
        sql.append("and length(split_part(split_part(coalesce(mp.respostarequisicaopinpad,''),'pinpadPedido\":',2),',',1)) > 0 \n");
        sql.append("order by mp.codigo desc \n");
        sql.append(") as sql \n");
        sql.append("inner join pinpadpedido p2 on p2.codigo = sql.pinpadpedido \n");
        sql.append("where (p2.recibopagamento is null or p2.movpagamento is null) \n");
        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
        Uteis.logarDebug("PinPadPedido | ajustarPedidos | Total " + total);
        Integer atual = 0;
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                while (rs.next()) {
                    Uteis.logarDebug("PinPadPedido | ajustarPedidos | " + ++atual + "/" + total);
                    boolean semMovPagamento = rs.getBoolean("semMovPagamento");
                    boolean semRecibo = rs.getBoolean("semRecibo");
                    Integer recibo = rs.getInt("recibo");
                    Integer movPagamento = rs.getInt("movpagamento");
                    Integer pinpadpedido = rs.getInt("pinpadpedido");

                    String update = "";
                    if (semMovPagamento && semRecibo) {
                        update = ("update pinpadpedido set movpagamento = " + movPagamento + ", recibopagamento = " + recibo + " where codigo = " + pinpadpedido + ";");
                    } else if (semMovPagamento) {
                        update = ("update pinpadpedido set movpagamento = " + movPagamento + " where codigo = " + pinpadpedido + ";");
                    } else if (semRecibo) {
                        update = ("update pinpadpedido set recibopagamento = " + recibo + " where codigo = " + pinpadpedido + ";");
                    }
                    if (!UteisValidacao.emptyString(update)) {
                        SuperFacadeJDBC.executarUpdate(update, this.con);
                    }
                }
            }
        }
    }

    public List<PinPadPedidoVO> consultarPedidosAguardando(String pdvPinpad) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT * FROM pinpadpedido WHERE status = 2 AND dataregistro < (NOW() - INTERVAL '3 minute') and pdvpinpad ilike '").append(pdvPinpad).append("';");
        try (PreparedStatement statement = con.prepareStatement(sql.toString())) {
            ResultSet resultSet = statement.executeQuery();
            List<PinPadPedidoVO> lista = new ArrayList<>();
            while (resultSet.next()) {
                try {
                    lista.add(montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            return lista;
        }
    }

    public void gravarRetornoPagamentoStoneConnect(String retornoGravar, PinPadPedidoVO obj) throws Exception {
        //Estamos tendo problemas onde o equipamento informa pagamento com sucesso, mas não acontece a baixa da parcela.
        //Gravar esse registro para ajudar em analises futuras.
        String update = "UPDATE PinPadPedido SET retornoPagamentoStoneConnect = ?, dataUltimaAlteracaoRetornoStoneConnect = ? WHERE codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(update)) {
            int i = 0;
            pst.setString(++i, retornoGravar);
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setInt(++i, obj.getCodigo());
            pst.execute();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

}
