package negocio.facade.jdbc.crm;

import br.com.pacto.priv.sms.beans.BeanSMSExternal;
import br.com.pactosolucoes.enumeradores.OcorrenciaEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;
import br.com.pactosolucoes.enumeradores.TipoEventoEnum;
import br.com.pactosolucoes.enumeradores.TipoVigenciaConsultaEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.security.AlgoritmoCriptoEnum;
import controle.crm.MalaDiretaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.crm.*;
import negocio.comuns.basico.EmpresaVO;

import java.sql.*;
import java.util.Arrays;
import java.util.Hashtable;

import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.facade.jdbc.utilitarias.CacheControl;
import negocio.interfaces.basico.PessoaInterfaceFacade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import negocio.interfaces.crm.MalaDiretaInterfaceFacade;
import relatorio.controle.basico.MailingFiltrosTO;

import controle.arquitetura.SuperControle;
import servicos.propriedades.PropsService;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe <code>malaDiretaVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar pertinentes
 * a classe <code>malaDiretaVO</code>. Encapsula toda a interação com o
 * banco de dados.
 * 
 * @see MalaDiretaVO
 * @see SuperEntidade
 */
public class MalaDireta extends SuperEntidade implements MalaDiretaInterfaceFacade {

    private Hashtable malaDiretaEnviadas;
    private PessoaInterfaceFacade pessoaFacade;
    private String url = PropsService.getPropertyValue(PropsService.urlVendasOnline);

    public MalaDireta() throws Exception {
        super();
        setMalaDiretaEnviadas(new Hashtable());
        pessoaFacade = getFacade().getPessoa();
    }
    
    
    public MalaDireta(Connection con) throws Exception {
        super(con);
        setMalaDiretaEnviadas(new Hashtable());
        pessoaFacade = new Pessoa(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>malaDiretaVO</code>.
     */
    public MalaDiretaVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        return new MalaDiretaVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>malaDiretaVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj
     *            Objeto da classe <code>malaDiretaVO</code> que será
     *            gravado no banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão, restrição de acesso ou
     *                validação de dados.
     */
    public void incluir(MalaDiretaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(MalaDiretaVO obj) throws Exception {
        MalaDiretaVO.validarDados(obj);
        incluirCRM(getIdEntidade());
        String sql = "INSERT INTO maladireta( dataCriacao, mensagem, titulo, remetente, modeloMensagem, meioDeEnvio,empresa, "
                + "vigenteAte, " +
                "sql, " +
                "tipoagendamento, " +
                "evento, " +
                "dataenvio, " +
                "contatoavulso, " +
                "excluida, " +
                "faseEnvio, " +
                "tipopergunta, " +
                "opcoes, " +
                "codaberturameta, " +
                "diasposvenda," +
                "metaExtraIndividual," +
                "tipoConsultorMetaExtraIndividual, " +
                "quantidadeMinimaAcessos, " +
                "quantidadeMaximaAcessos, " +
                "intervaloDias," +
                "todasEmpresas, questionario, " +
                "tipoCancelamento, importarlista, configs, enviohabilitado, smsmarketing, idTemplate, " +
                "urlwebhoobotconversa, configuracaointegracaogymbotpro, urlWebhook) " +
                "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataCriacao()));
            sqlInserir.setString(2, obj.getMensagem());
            sqlInserir.setString(3, obj.getTitulo());

            resolveFKNull(sqlInserir, 4, obj.getRemetente().getCodigo());
            resolveFKNull(sqlInserir, 5, obj.getModeloMensagem().getCodigo());

            obj.setDataEnvio(Calendario.getDataComHora(obj.getDataEnvio(), UteisValidacao.emptyNumber(obj.getHoraInicio()) ? "00:00" : obj.getHoraInicioString()));
            sqlInserir.setInt(6, obj.getMeioDeEnvio());
            resolveFKNull(sqlInserir, 7, obj.getEmpresa().getCodigo());

            resolveDateNull(sqlInserir, 8, obj.getVigenteAte());
            sqlInserir.setString(9, obj.getSql());
            sqlInserir.setInt(10, obj.getTipoAgendamento().getCodigo());
            resolveIntegerNull(sqlInserir, 11, obj.getCfgEvento().getEventoCodigo());
            sqlInserir.setTimestamp(12, Uteis.getDataJDBCTimestamp(obj.getDataEnvio()));
            sqlInserir.setBoolean(13, obj.isContatoAvulso());
            sqlInserir.setBoolean(14, false); //Excluida
            sqlInserir.setString(15, obj.getFaseEnvio());
            resolveIntegerNull(sqlInserir, 16, obj.getTipoPergunta());
            sqlInserir.setString(17, obj.getOpcoes());
            resolveIntegerNull(sqlInserir, 18, obj.getCodAberturaMeta());
            sqlInserir.setInt(19, obj.getDiasPosVenda());
            sqlInserir.setBoolean(20, obj.getMetaExtraIndividual());
            sqlInserir.setString(21, obj.getTipoConsultorMetaExtraIndividual());
            sqlInserir.setInt(22, obj.getQuantidadeMinimaAcessos());
            sqlInserir.setInt(23, obj.getQuantidadeMinimaAcessos());
            sqlInserir.setInt(24, obj.getIntervaloDias());
            sqlInserir.setBoolean(25, obj.isTodasEmpresas());
            resolveIntegerNull(sqlInserir, 26, obj.getQuestionario());
            resolveIntegerNull(sqlInserir, 27, obj.getTipoCancelamento());
            sqlInserir.setBoolean(28, obj.getImportarLista());
            sqlInserir.setString(29, obj.getConfigs());
            sqlInserir.setBoolean(30, obj.getEnvioHabilitado());
            sqlInserir.setBoolean(31, obj.getSmsMarketing());
            sqlInserir.setInt(32, obj.getIdTemplate() == null? 0 : obj.getIdTemplate());
            sqlInserir.setString(33, obj.getUrlwebhoobotconversa());
            resolveFKNull(sqlInserir, 34, obj.getConfigGymbotPro() == null ? null : obj.getConfigGymbotPro().getCodigo());
            sqlInserir.setString(35, obj.getUrlWebhook());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());

        if (!obj.isCrmExtra()) {
            //CRM-EXTRA NÃO INCLUI MAILINGAGENDAMENTO -- MALADIRETAENVIADAS
            obj.getAgendamento().setDataInicial(obj.getDataEnvio());
            getFacade().getMailingAgendamento().incluir(obj.getAgendamento(), obj.getCodigo());

            if (!obj.getTipoAgendamento().equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO)) {
                getFacade().getMalaDiretaEnviada().incluirMalaDiretaEnviadas(obj.getCodigo(), obj.getMalaDiretaEnviadaVOs());
            }
        }

        incluirFiltros(obj);

        if(!UteisValidacao.emptyNumber(obj.getCfgEvento().getEventoCodigo())){
            incluirConfigEventoMailing(obj.getCodigo(), obj.getCfgEvento(), con);
        }
        
    }
    
    public void preencherDataEnvio(MalaDiretaVO malaDireta, Date dataEnvio) throws Exception{
        try (PreparedStatement sqlInserir = con.prepareStatement("UPDATE maladireta SET dataenvio = ? WHERE codigo = ?")) {
            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataEnvio));
            sqlInserir.setInt(2, malaDireta.getCodigo());
            sqlInserir.execute();
        }
        malaDireta.setDataEnvio(dataEnvio);
    }

    public void atualizarStatusEntregabilidade(MalaDiretaVO malaDireta, Boolean status) throws Exception{
        try (PreparedStatement sqlInserir = con.prepareStatement("UPDATE maladireta SET statusEntregabilidade = ? WHERE codigo = ?")) {
            sqlInserir.setBoolean(1, status);
            sqlInserir.setInt(2, malaDireta.getCodigo());
            sqlInserir.execute();
        }
        malaDireta.setStatusEntregabilidade(status);
    }
    public void preencherDataUltimaExecucao(MalaDiretaVO malaDireta, Date dataEnvio) throws Exception{
        try (PreparedStatement sqlInserir = con.prepareStatement("UPDATE mailingagendamento SET ultimaexecucao = ? WHERE maladireta = ?")) {
            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataEnvio));
            sqlInserir.setInt(2, malaDireta.getCodigo());
            sqlInserir.execute();
        }
    }
    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>malaDiretaVO</code>. Sempre utiliza a chave primária da
     * classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto.
     * Verifica a conexão com o banco de dados e a permissão do usuário para
     * realizar esta operacão na entidade. Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj
     *            Objeto da classe <code>malaDiretaVO</code> que será
     *            alterada no banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão, restrição de acesso ou
     *                validação de dados.
     */
    public void alterar(MalaDiretaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }


    /**
     * <AUTHOR> Alcides
     * 03/05/2013
     */
    private void alterarSemCommit(MalaDiretaVO obj) throws Exception {
        MalaDiretaVO.validarDados(obj);
        alterarCRM(getIdEntidade());
        String sql = "UPDATE maladireta set " +
                "dataEnvio = ?, " +
                "dataCriacao = ?, " +
                "mensagem = ?, " +
                "titulo = ?, " +
                "remetente = ?, " +
                "modeloMensagem = ?, " +
                "meioDeEnvio = ?, " +
                "vigenteate = ?, " +
                "sql = ?, " +
                "evento = ?, " +
                "excluida = ?, " +
                "faseEnvio = ?, " +
                "tipopergunta = ?, " +
                "opcoes = ?, " +
                "codaberturameta = ?, " +
                "diasposvenda = ?, " +
                "metaExtraIndividual =?, " +
                "tipoConsultorMetaExtraIndividual = ?, " +
                "quantidadeMinimaAcessos = ?, " +
                "quantidadeMaximaAcessos = ?, " +
                "intervaloDias = ?, " +
                "todasEmpresas = ?, " +
                "questionario = ?, " +
                "tipoCancelamento = ?," +
                "importarlista = ?, " +
                "configs = ?," +
                "enviohabilitado = ?, " +
                "smsmarketing = ?,  " +
                "idTemplate= ? ," +
                "urlwebhoobotconversa= ?, " +
                "urlWebhook = ? "+
                "WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {

            obj.setDataEnvio(Calendario.getDataComHora(obj.getDataEnvio(), UteisValidacao.emptyNumber(obj.getHoraInicio()) ? "00:00" : obj.getHoraInicioString()));
            sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataEnvio()));

            sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataCriacao()));
            sqlAlterar.setString(3, obj.getMensagem());
            sqlAlterar.setString(4, obj.getTitulo());
            if (obj.getRemetente().getCodigo() != 0) {
                sqlAlterar.setInt(5, obj.getRemetente().getCodigo());
            } else {
                sqlAlterar.setNull(5, 0);
            }
            if (obj.getModeloMensagem().getCodigo() != 0) {
                sqlAlterar.setInt(6, obj.getModeloMensagem().getCodigo());
            } else {
                sqlAlterar.setNull(6, 0);
            }
            sqlAlterar.setInt(7, obj.getMeioDeEnvio());

            resolveDateNull(sqlAlterar, 8, obj.getVigenteAte());
            sqlAlterar.setString(9, obj.getSql());
            resolveIntegerNull(sqlAlterar, 10, obj.getCfgEvento().getEventoCodigo());
            sqlAlterar.setBoolean(11, false); //Excluida
            sqlAlterar.setString(12, obj.getFaseEnvio());
            if (obj.getTipoPergunta() == null) {
                sqlAlterar.setNull(13, Types.INTEGER);
            } else {
                sqlAlterar.setInt(13, obj.getTipoPergunta());
            }
            sqlAlterar.setString(14, obj.getOpcoes());
            resolveIntegerNull(sqlAlterar, 15, obj.getCodAberturaMeta());
            sqlAlterar.setInt(16, obj.getDiasPosVenda());
            sqlAlterar.setBoolean(17, obj.getMetaExtraIndividual());
            sqlAlterar.setString(18, obj.getTipoConsultorMetaExtraIndividual());

            sqlAlterar.setInt(19, obj.getQuantidadeMinimaAcessos());
            sqlAlterar.setInt(20, obj.getQuantidadeMaximaAcessos());
            sqlAlterar.setInt(21, obj.getIntervaloDias());
            sqlAlterar.setBoolean(22, obj.isTodasEmpresas());
            resolveIntegerNull(sqlAlterar, 23, obj.getQuestionario());
            resolveIntegerNull(sqlAlterar, 24, obj.getTipoCancelamento());
            sqlAlterar.setBoolean(25, obj.getImportarLista());
            sqlAlterar.setString(26, obj.getConfigs());
            sqlAlterar.setBoolean(27, obj.getEnvioHabilitado());
            sqlAlterar.setBoolean(28, obj.getSmsMarketing());
            sqlAlterar.setInt(29, obj.getIdTemplate()  == null ? 0 :obj.getIdTemplate() );
            sqlAlterar.setString(30, obj.getUrlwebhoobotconversa());
            sqlAlterar.setString(31, obj.getUrlWebhook());
            sqlAlterar.setInt(32, obj.getCodigo());

            sqlAlterar.execute();
        }

        if (!obj.isCrmExtra()) { //CRM-EXTRA NÃO INCLUI MAILINGAGENDAMENTO -- MALADIRETAENVIADAS
            obj.getAgendamento().setDataInicial(obj.getDataEnvio());
            getFacade().getMailingAgendamento().incluir(obj.getAgendamento(), obj.getCodigo());
        }

        incluirFiltros(obj);

        if (!obj.getMalaDiretaEnviadaVOs().isEmpty())
            getFacade().getMalaDiretaEnviada().alterarMalaDiretaEnviadas(obj.getCodigo(), obj.getMalaDiretaEnviadaVOs());
        if (!UteisValidacao.emptyNumber(obj.getCfgEvento().getEventoCodigo())) {
            incluirConfigEventoMailing(obj.getCodigo(), obj.getCfgEvento(), con);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>malaDiretaVO</code>. Sempre localiza o registro a ser
     * excluído através da chave primária da entidade. Primeiramente verifica a
     * conexão com o banco de dados e a permissão do usuário para realizar esta
     * operacão na entidade. Isto, através da operação <code>excluir</code> da
     * superclasse.
     *
     * @param obj
     *            Objeto da classe <code>malaDiretaVO</code> que será
     *            removido no banco de dados.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(MalaDiretaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM maladireta WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            getFacade().getMalaDiretaEnviada().excluirMalaDiretaEnviadas(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * - Envia email para um destinatário que não seja uma PessoaVO. <br />
     * - Nao é necessario utilizar o addDestinatario <br />
     * - O destinatário é informado neste método através dos parametros:
     * emailDestinatario, nomeDestinatario <br />
     *
     * @throws Exception
     */
    public void agendarEnvio(MalaDiretaVO mensagem, Date diaMeta, EmpresaVO empresa) throws Exception {
        try {
            con.setAutoCommit(false);
            agendarEnvioEmailSemCommit(mensagem, false, diaMeta, empresa);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void habilitarEnvio(MalaDiretaVO malaDiretaVO) throws Exception{
        executarUpdate("update maladireta set enviohabilitado = true where codigo = " + malaDiretaVO.getCodigo(), con);
    }

    /**
     * - Envia email para um destinatário que não seja uma PessoaVO. <br />
     * - Nao é necessario utilizar o addDestinatario <br />
     * - O destinatário é informado neste método através dos parametros:
     * emailDestinatario, nomeDestinatario <br />
     *
     * @throws Exception
     */
    public void agendarEnvioEmailSemCommit(MalaDiretaVO mensagem, Boolean gerarHistorico, Date diaMeta, EmpresaVO empresa) throws Exception {

        FecharMetaDetalhado fecharMetaDetalhadoDAO = new FecharMetaDetalhado(con);
        mensagem.setEmpresa(empresa);
        if(mensagem.getMeioDeEnvio()!=3 && mensagem.getMeioDeEnvio()!=2 &&mensagem.getMeioDeEnvio()!=9){
            if (mensagem.isContatoAvulso()) {
                String html = mensagem.getMensagem();
                html = footerEmails(html, empresa);
                mensagem.setMensagem(html);
            }
        }
        if(mensagem.getNovoObj()){
            incluirSemCommit(mensagem);
        }else{
            alterarSemCommit(mensagem);
        }
        
        List<Map<String, Object>> listaMetas = new ArrayList<Map<String, Object>>();

        //iterar nos destinatários
        for (MalaDiretaEnviadaVO malaDiretaEnviadaVO : mensagem.getMalaDiretaEnviadaVOs()) {
            if(!mensagem.isContatoAvulso() && gerarHistorico){ //só irá consultar metas se não for um contato avulso e for  depois da execução do agendamento.
                    listaMetas.addAll(getFacade().getFecharMetaDetalhado().consultarPorClienteDia(
                            malaDiretaEnviadaVO.getClienteVO().getPessoa().getCodigo(),
                            diaMeta,
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            if (gerarHistorico) {
                getFacade().getHistoricoContato().executarGravacaoVindoEmail(mensagem, malaDiretaEnviadaVO.getPassivoVO().getCodigo(), malaDiretaEnviadaVO.getClienteVO().getCodigo(), malaDiretaEnviadaVO.getIndicadoVO().getCodigo(), null, false);
            }
        }
        if (!listaMetas.isEmpty()) {
            for (Map<String, Object> map : listaMetas) {
                getFacade().getFecharMeta().executarValidacaoQuandoGravaHistoricoContatoAlcancaMetaAtingida((Date) map.get("dia"),
                        (String) map.get("identificadormeta"),
                        (Integer) map.get("codigoFecharMetaDetalhada"), (Boolean) map.get("metaemaberto"),
                        fecharMetaDetalhadoDAO);
            }
        }
    }

    public List<BeanSMSExternal> montarListaBeans(String[] telefones, MalaDiretaVO malaDireta, String nomePessoa, SuperControle superControle) throws Exception {
        List<BeanSMSExternal> listaBeans = new ArrayList<BeanSMSExternal>();
        if (telefones != null) {
            for (String tel : telefones) {
                //personalizar a tag de nome
                malaDireta.setMensagemComAlteracaoTag(ModeloMensagemVO.personalizarTagNomePessoa(malaDireta.getMensagem(), nomePessoa));
                BeanSMSExternal beanSms = new BeanSMSExternal();
                beanSms.setMsg(malaDireta.getMensagemComAlteracaoTag());
                beanSms.setNumero(tel);
                listaBeans.add(beanSms);
            }
        }
        return listaBeans;
    }

    /**
     * Responsável por realizar uma consulta de <code>malaDireta</code>
     * através do valor do atributo <code>titulo</code> da classe
     * <code>ModeloMensagem</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe
     *         <code>malaDiretaVO</code> resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTituloModeloMensagem(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT maladireta.* FROM maladireta, ModeloMensagem WHERE maladireta.modeloMensagem = " +
                "ModeloMensagem.codigo and upper( ModeloMensagem.titulo ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List consultarPorDataEnvio(Date prmIni, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT maladireta.* FROM maladireta WHERE ((Cast (dataenvio as Date) = '" + Uteis.getDataJDBC(prmIni) + "'))  " +
                "ORDER BY codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>malaDireta</code>
     * através do valor do atributo <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe
     *         <code>malaDiretaVO</code> resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorRemetente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT maladireta.* FROM maladireta, Usuario WHERE maladireta.remetente = Usuario.codigo and upper( Usuario.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>malaDiretaVO</code>
     * através do valor do atributo <code>String titulo</code>. Retorna os
     * objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso
     *            Indica se a aplicação deverá verificar se o usuário possui
     *            permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     *         <code>malaDiretaVO</code> resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTitulo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM maladireta WHERE upper( titulo ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>malaDireta</code>
     * através do valor do atributo <code>String mensagem</code>. Retorna os
     * objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o
     * trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso
     *            Indica se a aplicação deverá verificar se o usuário possui
     *            permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     *         <code>malaDiretaVO</code> resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorMensagem(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM maladireta WHERE upper( mensagem ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>malaDiretaVO</code>
     * através do valor do atributo <code>Integer codigo</code>. Retorna os
     * objetos com valores iguais ou superiores ao parâmetro fornecido. Faz uso
     * da operação <code>montarDadosConsulta</code> que realiza o trabalho de
     * prerarar o List resultante.
     *
     * @param controlarAcesso
     *            Indica se a aplicação deverá verificar se o usuário possui
     *            permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     *         <code>malaDiretaVO</code> resultantes da consulta.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM maladireta WHERE codigo = " + valorConsulta.intValue() + " ORDER BY codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (<code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe
     *         <code>malaDiretaVO</code> resultantes da consulta.
     */
    public static List<MalaDiretaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<MalaDiretaVO> vetResultado = new ArrayList<MalaDiretaVO>();
        while (tabelaResultado.next()) {
            MalaDiretaVO obj = montarDados(tabelaResultado, nivelMontarDados, con, false);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (<code>ResultSet</code>) em um objeto da classe
     * <code>malaDiretaVO</code>.
     *
     * @return O objeto da classe <code>malaDiretaVO</code> com os
     *         dados devidamente montados.
     */
    public static MalaDiretaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con, boolean envio) throws Exception {
        MalaDiretaVO obj = new MalaDiretaVO();
        obj.setNovoObj(false);
        obj.setStatusEntregabilidade(dadosSQL.getBoolean("statusentregabilidade"));
        obj.setDataEnvio(dadosSQL.getTimestamp("dataEnvio"));
        obj.setHoraInicio(obj.getDataEnvio() == null ? 0 : Uteis.gethoraHH(obj.getDataEnvio()));
        obj.setFaseEnvio(dadosSQL.getString("faseEnvio"));
        obj.setQuantidadeMinimaAcessos(dadosSQL.getInt("quantidadeMinimaAcessos"));
        obj.setQuantidadeMaximaAcessos(dadosSQL.getInt("quantidadeMaximaAcessos"));
        obj.setIntervaloDias(dadosSQL.getInt("intervaloDias"));
        obj.setTodasEmpresas(dadosSQL.getBoolean("todasEmpresas"));
        obj.setQuestionario(dadosSQL.getInt("questionario"));
        obj.setImportarLista(dadosSQL.getBoolean("importarLista"));
        obj.setListaImportada(dadosSQL.getBoolean("importarLista"));
        obj.setTipoCancelamento(dadosSQL.getInt("tipoCancelamento"));
        obj.setIdTemplate(dadosSQL.getInt("idTemplate"));
        obj.setUrlwebhoobotconversa( dadosSQL.getString("urlwebhoobotconversa"));
        try {
            obj.setEnvioHabilitado(dadosSQL.getBoolean("enviohabilitado"));
            obj.setSmsMarketing(dadosSQL.getBoolean("smsmarketing"));
            obj.setConfigs(dadosSQL.getString("configs"));
        }catch (Exception e){
            Uteis.logar(e, MalaDireta.class);
        }

        try {
            obj.setUrlWebhook(dadosSQL.getString("urlWebhook"));
        } catch (Exception e) {

        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setTitulo(dadosSQL.getString("titulo"));
            obj.setDataCriacao(dadosSQL.getTimestamp("dataCriacao"));
            obj.getModeloMensagem().setCodigo(new Integer(dadosSQL.getInt("modeloMensagem")));
            obj.getRemetente().setCodigo(new Integer(dadosSQL.getInt("remetente")));
            obj.getEmpresa().setCodigo(new Integer(dadosSQL.getInt("empresa")));
            obj.setMeioDeEnvioEnum(MeioEnvio.getMeioEnvioPorCodigo(new Integer(dadosSQL.getInt("meioDeEnvio"))));
            obj.setVigenteAte(dadosSQL.getDate("vigenteate"));
            obj.setTipoAgendamento(UteisValidacao.emptyNumber(
                    dadosSQL.getInt("tipoagendamento")) ? TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO : TipoAgendamentoEnum.getTipo(dadosSQL.getInt("tipoagendamento")));
            obj.setEvento(dadosSQL.getInt("evento"));
            obj.setTipoEvento(TipoEventoEnum.obter(dadosSQL.getInt("evento")));
            obj.setMensagem(dadosSQL.getString("mensagem"));
            montarDadosRemetente(obj, nivelMontarDados, con);
            montarDadosModeloMensagem(obj, nivelMontarDados, con, envio);
            try{
                obj.setCodAberturaMeta(dadosSQL.getInt("codaberturameta"));
                obj.setUltimaExecucao(dadosSQL.getTimestamp("ultimaexecucao"));
            }catch (Exception e) {
                obj.setUltimaExecucao(null);
            }

            try{
                obj.getEmpresa().setNome(dadosSQL.getString("nomeEmpresa"));
            }catch (Exception e) {
            }
            return obj;
        }

        MalaDireta malaDiretaDAO;
        try {
            malaDiretaDAO = new MalaDireta(con);
            if (dadosSQL.getInt("configuracaointegracaogymbotpro") != 0) {
                obj.setConfigGymbotPro(malaDiretaDAO.consultarFluxoGymbotPro(dadosSQL.getInt("configuracaointegracaogymbotpro"), dadosSQL.getInt("empresa")));
            }
        } catch (Exception ex) {
        } finally {
            malaDiretaDAO = null;
        }

        obj.setEvento(dadosSQL.getInt("evento"));
        obj.setTipoEvento(TipoEventoEnum.obter(dadosSQL.getInt("evento")));
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setMeioDeEnvioEnum(MeioEnvio.getMeioEnvioPorCodigo(new Integer(dadosSQL.getInt("meioDeEnvio"))));
        obj.setDataCriacao(dadosSQL.getTimestamp("dataCriacao"));
        obj.setMensagem(dadosSQL.getString("mensagem"));
        obj.setTitulo(dadosSQL.getString("titulo"));
        obj.getRemetente().setCodigo(new Integer(dadosSQL.getInt("remetente")));
        obj.getModeloMensagem().setCodigo(new Integer(dadosSQL.getInt("modeloMensagem")));
        obj.getEmpresa().setCodigo(new Integer(dadosSQL.getInt("empresa")));
        obj.setVigenteAte((dadosSQL.getDate("vigenteate") != null ? Uteis.getDateTime(dadosSQL.getDate("vigenteate"), 23, 59, 59) : null));
        obj.setSql(dadosSQL.getString("sql"));
        obj.setExcluida(dadosSQL.getBoolean("excluida"));
        obj.setDiasPosVenda(dadosSQL.getInt("diasposvenda"));
        try {
            obj.setTipoPergunta(dadosSQL.getInt("tipopergunta"));
            obj.setOpcoes(dadosSQL.getString("opcoes"));
            obj.setCodAberturaMeta(dadosSQL.getInt("codaberturameta"));
            obj.setMetaExtraIndividual(dadosSQL.getBoolean("metaExtraIndividual"));
            obj.setTipoConsultorMetaExtraIndividual(dadosSQL.getString("tipoConsultorMetaExtraIndividual"));
        } catch (Exception e) {
        }

        obj.setTipoAgendamento(UteisValidacao.emptyNumber(
                dadosSQL.getInt("tipoagendamento")) ? TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO : TipoAgendamentoEnum.getTipo(dadosSQL.getInt("tipoagendamento")));

        obj.setAgendamento(new MailingAgendamento(con).consultar(obj));
        obj.setContatoAvulso(dadosSQL.getBoolean("contatoavulso"));
        
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        obj.setmalaDiretaEnviadaVOs(new MalaDiretaEnviada(con).consultarMalaDiretaEnviadas(obj.getCodigo(), nivelMontarDados));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        montarDadosFiltros(obj, con);
        montarDadosEmpresa(obj, con);
        montarDadosRemetente(obj, nivelMontarDados, con);
        montarDadosModeloMensagem(obj, nivelMontarDados, con, envio);
        return obj;
    }

    private static void montarDadosFiltros(MalaDiretaVO malaDireta, Connection c) throws Exception{
        try (ResultSet resultSet = criarConsulta("SELECT * FROM mailingfiltros WHERE maladireta = " + malaDireta.getCodigo(), c)) {
            malaDireta.setMailingFiltros(new MailingFiltrosTO());
            if (resultSet.next()) {
                malaDireta.getMailingFiltros().setEvento(resultSet.getInt("evento"));
                malaDireta.getMailingFiltros().setCodigoContratoDuracao(resultSet.getString("codigoContratoDuracao"));
                malaDireta.getMailingFiltros().setCodigosModalidades(resultSet.getString("codigosModalidades"));
                malaDireta.getMailingFiltros().setCodigosCategoria(resultSet.getString("codigosCategoria"));
                malaDireta.getMailingFiltros().setListaSituacoes(resultSet.getString("listaSituacoes"));
                malaDireta.getMailingFiltros().setCodigosConsultores(resultSet.getString("codigosConsultores"));
                malaDireta.getMailingFiltros().setCodigosPlanos(resultSet.getString("codigosPlanos"));
                malaDireta.getMailingFiltros().setCodigosProfessores(resultSet.getString("codigosProfessores"));

                malaDireta.getMailingFiltros().setDataCadastroMin(resultSet.getDate("datacadastromin"));
                malaDireta.getMailingFiltros().setDataCadastroMax(resultSet.getDate("datacadastromax"));
                malaDireta.getMailingFiltros().setIdadeMin(resultSet.getInt("idademin"));
                malaDireta.getMailingFiltros().setIdadeMax(resultSet.getInt("idademax"));
                malaDireta.getMailingFiltros().setFeminino(resultSet.getBoolean("feminino"));
                malaDireta.getMailingFiltros().setMasculino(resultSet.getBoolean("masculino"));
                try {
                    malaDireta.getMailingFiltros().setVencimentoContratoMax(resultSet.getDate("vencimentoContratoMax"));
                    malaDireta.getMailingFiltros().setVencimentoContratoMin(resultSet.getDate("vencimentoContratoMin"));
                    malaDireta.getMailingFiltros().setDiasSemComparecerMin(resultSet.getInt("diasSemComparecerMin"));
                    malaDireta.getMailingFiltros().setDiasSemComparecerMax(resultSet.getInt("diasSemComparecerMax"));
                    malaDireta.getMailingFiltros().setSomenteRecorrencia(resultSet.getBoolean("somenterecorrencia"));
                }catch (Exception ignored){
                }

            }
        }

    }
    private static void montarDadosEmpresa(MalaDiretaVO obj, Connection con) throws Exception{
        try{
            obj.setEmpresa(new Empresa(con).consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }catch (Exception e) {
            Uteis.logar(e, MalaDireta.class);
        }

    }
    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ModeloMensagemVO</code> relacionado ao objeto
     * <code>malaDiretaVO</code>. Faz uso da chave primária da classe
     * <code>ModeloMensagemVO</code> para realizar a consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    private static void montarDadosModeloMensagem(MalaDiretaVO obj, int nivelMontarDados, Connection con, boolean envio) throws Exception {
        if (obj.getModeloMensagem().getCodigo() == 0) {
            obj.setModeloMensagem(new ModeloMensagemVO());
            return;
        }
        CacheControl.toggleCache(ModeloMensagem.class, true);
        try {
            obj.setModeloMensagem(new ModeloMensagem(con).consultarPorChavePrimaria(obj.getModeloMensagem().getCodigo(), nivelMontarDados));
            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
                return;
            }
            if (obj.getModeloMensagem() != null) {
                final String chave = DAO.resolveKeyFromConnection(con);
                obj.getModeloMensagem().verificarSeExisteImagemModelo(envio, chave);

                if (obj.getModeloMensagem().isMensagemUploadImagem() && !envio && UteisValidacao.emptyString(obj.getMensagem())) {
                    obj.setMensagem(obj.getModeloMensagem().getMensagem());
                }
            }
        } finally {
            CacheControl.clear();
        }
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>malaDiretaVO</code>. Faz uso da chave primária da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosRemetente(MalaDiretaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getRemetente().getCodigo().intValue() == 0) {
            obj.setRemetente(new UsuarioVO());
            return;
        }
        obj.setRemetente(new Usuario(con).consultarPorChavePrimaria(obj.getRemetente().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>MalaDiretaEnviadaVO</code> no Hashtable
     * <code>MalaDiretaEnviadaVOs</code>. Neste Hashtable são mantidos todos os
     * objetos de MalaDiretaEnviadaVO de uma determinada malaDireta.
     *
     * @param obj
     *            Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjMalaDiretaEnviadas(MalaDiretaEnviadaVO obj) throws Exception {
        getMalaDiretaEnviadas().put(obj.getClienteVO().getPessoa().getCodigo() + "", obj);
        // adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>MalaDiretaEnviadaVO</code> do Hashtable
     * <code>MalaDiretaEnviadaVOs</code>. Neste Hashtable são mantidos todos os
     * objetos de MalaDiretaEnviadaVO de uma determinada malaDireta.
     *
     * @param Pessoa
     *            Atributo da classe <code>MalaDiretaEnviadaVO</code> utilizado
     *            como apelido (key) no Hashtable.
     */
    public void excluirObjMalaDiretaEnviadas(Integer Pessoa) throws Exception {
        getMalaDiretaEnviadas().remove(Pessoa + "");
        // excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>malaDiretaVO</code> através de sua chave primária.
     *
     * @exception Exception
     *                Caso haja problemas de conexão ou localização do objeto
     *                procurado.
     */
    public MalaDiretaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados, boolean envio) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM maladireta WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( malaDireta ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con, envio));
            }
        }
    }

    public Hashtable getMalaDiretaEnviadas() {
        if (malaDiretaEnviadas == null) {
            malaDiretaEnviadas = new Hashtable();
        }
        return (malaDiretaEnviadas);
    }

    public void setMalaDiretaEnviadas(Hashtable malaDiretaEnviadas) {
        this.malaDiretaEnviadas = malaDiretaEnviadas;
    }

    public static String executarInsercaoTag(String texto, String tag, boolean modelo) {
        int parametro = 0;
        if (modelo) {
            if ("nome".equalsIgnoreCase(texto)) {
                parametro = texto.lastIndexOf("&tl;NOME&gt;");
            } else {
                parametro = texto.lastIndexOf(tag);
            }
        } else {
            parametro = texto.lastIndexOf("</p>");
        }
        if (parametro == -1) {
            parametro = texto.lastIndexOf("</body>");
        }
        String textoAntes = texto.substring(0, parametro);
        String textoDepois = texto.substring(parametro);
        texto = (textoAntes + " " + tag + textoDepois);
        return texto;
    }
    
    private void incluirFiltros(MalaDiretaVO malaDiretaVO) throws Exception{
        executarConsulta("DELETE FROM mailingfiltros where maladireta = "+malaDiretaVO.getCodigo(), con);
        String sql = "INSERT INTO mailingfiltros( "+
            " maladireta,  evento, codigosCategoria, codigosModalidades, "
            + "listaSituacoes, codigosConsultores, codigosProfessores, codigosPlanos, codigoContratoDuracao,"
            + "datacadastromin, datacadastromax, idademin, idademax, feminino, masculino, vencimentoContratoMin, vencimentoContratoMax,  diasSemComparecerMin, diasSemComparecerMax, somenterecorrencia) " +
            " VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setInt(++i, malaDiretaVO.getCodigo());
            resolveIntegerNull(stm, ++i, malaDiretaVO.getMailingFiltros().getEvento());
            stm.setString(++i, malaDiretaVO.getMailingFiltros().getCodigosCategoria());
            stm.setString(++i, malaDiretaVO.getMailingFiltros().getCodigosModalidades());
            stm.setString(++i, malaDiretaVO.getMailingFiltros().getListaSituacoes());
            stm.setString(++i, malaDiretaVO.getMailingFiltros().getCodigosConsultores());
            stm.setString(++i, malaDiretaVO.getMailingFiltros().getCodigosProfessores());
            stm.setString(++i, malaDiretaVO.getMailingFiltros().getCodigosPlanos());
            stm.setString(++i, malaDiretaVO.getMailingFiltros().getCodigoContratoDuracao());

            stm.setDate(++i, Uteis.getDataJDBC(malaDiretaVO.getMailingFiltros().getDataCadastroMin()));
            stm.setDate(++i, Uteis.getDataJDBC(malaDiretaVO.getMailingFiltros().getDataCadastroMax()));

            stm.setInt(++i, malaDiretaVO.getMailingFiltros().getIdadeMin());
            stm.setInt(++i, malaDiretaVO.getMailingFiltros().getIdadeMax());

            stm.setBoolean(++i, malaDiretaVO.getMailingFiltros().isFeminino());
            stm.setBoolean(++i, malaDiretaVO.getMailingFiltros().isMasculino());
            stm.setDate(++i, Uteis.getDataJDBC(malaDiretaVO.getMailingFiltros().getVencimentoContratoMin()));
            stm.setDate(++i, Uteis.getDataJDBC(malaDiretaVO.getMailingFiltros().getVencimentoContratoMax()));
            resolveIntegerNull(stm, ++i, malaDiretaVO.getMailingFiltros().getDiasSemComparecerMin());
            resolveIntegerNull(stm, ++i, malaDiretaVO.getMailingFiltros().getDiasSemComparecerMax());
            stm.setBoolean(++i, malaDiretaVO.getMailingFiltros().getSomenteRecorrencia());

            stm.execute();
        }
    }

    public List<MalaDiretaVO> consultarPaginado(Integer codigo, ConfPaginacao confPaginacao,
                                                Date inicio, Date fim, String consultarDescricao,
                                                Date inicioCriacao, Date fimCriacao, String consultarRemetente,
                                                Integer consultarMeioEnvio, Integer codigoTipoAgendamento,
                                                TipoVigenciaConsultaEnum tipoVigencia,
                                                Integer codEmpresa, Integer nivelMontarDados) throws Exception{

        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //sql principal
        StringBuffer sqlStr = new StringBuffer();

        // Não alterar o alias das tabelas
        sqlStr.append(" SELECT distinct ml.*, ma.ultimaexecucao, emp.nome as nomeEmpresa, u.nome FROM maladireta ml \n");
        sqlStr.append(" LEFT JOIN empresa emp ON emp.codigo = ml.empresa \n");
        sqlStr.append(" INNER JOIN usuario u ON u.codigo = ml.remetente \n");
        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo())) {
            sqlStr.append(" LEFT JOIN mailinghistorico mh ON mh.maladireta = ml.codigo \n");
        }
        sqlStr.append(" LEFT JOIN mailingagendamento ma ON ma.maladireta = ml.codigo \n");
        sqlStr.append(" WHERE ml.meiodeenvio = ? ");
        sqlStr.append(" AND UPPER(ml.titulo) LIKE ? ");
        sqlStr.append(" AND UPPER(u.nome) LIKE ? ");
        
        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo())){
            sqlStr.append(" AND mh.datafim between ? and ?  \n");
        }
        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())){
            sqlStr.append(" AND ml.dataenvio between ? and ?  \n");
        }
        if(codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo()) || codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())){
            sqlStr.append(" AND ml.tipoagendamento = ? \n");
        }
        
        if (inicioCriacao != null && fimCriacao != null) {
            sqlStr.append(" AND ml.datacriacao between ? and ? \n");
        }
        if(!UteisValidacao.emptyNumber(codigo)){
            sqlStr.append(" AND ml.codigo = ? \n");
        }
        if (!UteisValidacao.emptyNumber(codEmpresa)) {
            sqlStr.append(" AND ml.empresa = ? \n");
        }
        
        if(tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.SOMENTE_VIGENTES)){
            sqlStr.append(" AND (ml.vigenteAte is null or  ml.vigenteAte >= ?) \n");
        }
        if(tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.VENCIDOS)){
            sqlStr.append(" AND (ml.vigenteAte is not null and  ml.vigenteAte < ?) \n");
        }
        
        String sqlCount = "SELECT COUNT(*) FROM (" + sqlStr.toString() + ") AS consultaCount";
        //sql inner joins

        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);

        if (!UteisValidacao.emptyString(confPaginacao.getColunaOrdenacao()) && !UteisValidacao.emptyString(confPaginacao.getDirecaoOrdenacao())) {
            sqlStr.append(" ORDER BY " + confPaginacao.getColunaOrdenacao().split("_")[0] + "." + confPaginacao.getColunaOrdenacao().split("_")[1] + " " + confPaginacao.getDirecaoOrdenacao() );
        }else {
            sqlStr.append(" ORDER BY ml.codigo DESC");
        }

        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlStr);

        preencherFiltros(codigo, confPaginacao, inicio, fim, inicioCriacao, fimCriacao, consultarDescricao, consultarRemetente, 
                consultarMeioEnvio, codigoTipoAgendamento, codEmpresa, tipoVigencia);

        try (PreparedStatement pStmCount = con.prepareStatement(sqlCount)) {
            preencherFiltrosCount(codigo, pStmCount, inicio, fim, inicioCriacao, fimCriacao, consultarDescricao, consultarRemetente,
                    consultarMeioEnvio, codigoTipoAgendamento, codEmpresa, tipoVigencia);
            try (ResultSet rsCount = pStmCount.executeQuery()) {
                rsCount.next();
                //4 - REALIZA A CONSULTA COM PAGINACAO
                try (ResultSet tabelaResultado = confPaginacao.consultaPaginada(rsCount.getInt(1))) {
                    return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
                }
            }
        }

    }



    public List<MalaDiretaVO> consultar(Integer codigo, Date inicio, Date fim, String consultarDescricao,
                                                Date inicioCriacao, Date fimCriacao, String consultarRemetente,
                                                Integer consultarMeioEnvio, Integer codigoTipoAgendamento,
                                                TipoVigenciaConsultaEnum tipoVigencia,
                                                Integer codEmpresa, Integer nivelMontarDados, int limite, int pagina, String campoOrdenar, String tipoOrdenacao) throws Exception{


        StringBuffer sqlStr = new StringBuffer();

        sqlStr.append(" SELECT distinct ml.*, ma.ultimaexecucao, TRIM(ml.titulo), TRIM(u.nome), TRIM(mm.titulo), emp.nome as nomeEmpresa  FROM maladireta ml \n");
        sqlStr.append(" LEFT JOIN empresa emp ON emp.codigo = ml.empresa \n");
        sqlStr.append(" INNER JOIN usuario u ON u.codigo = ml.remetente \n");
        sqlStr.append(" LEFT JOIN modelomensagem mm on mm.codigo = ml.modelomensagem");
        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo())) {
            sqlStr.append(" LEFT JOIN mailinghistorico mh ON mh.maladireta = ml.codigo \n");
        }
        sqlStr.append(" LEFT JOIN mailingagendamento ma ON ma.maladireta = ml.codigo \n");
        sqlStr.append(" WHERE ml.meiodeenvio = ? ");
        sqlStr.append(" AND UPPER(ml.titulo) LIKE ? ");
        sqlStr.append(" AND UPPER(u.nome) LIKE ? ");

        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo())){
            sqlStr.append(" AND mh.datafim between ? and ?  \n");
        }
        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())){
            sqlStr.append(" AND ml.dataenvio between ? and ?  \n");
        }
        if(codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo()) || codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())){
            sqlStr.append(" AND ml.tipoagendamento = ? \n");
        }
        if (inicioCriacao != null && fimCriacao != null) {
            sqlStr.append(" AND ml.datacriacao between ? and ? \n");
        }
        if(!UteisValidacao.emptyNumber(codigo)){
            sqlStr.append(" AND ml.codigo = ? \n");
        }
        if (!UteisValidacao.emptyNumber(codEmpresa)) {
            sqlStr.append(" AND ml.empresa = ? \n");
        }
        if(tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.SOMENTE_VIGENTES)){
            sqlStr.append(" AND (ml.vigenteAte is null or  ml.vigenteAte >= ?) \n");
        }
        if(tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.VENCIDOS)){
            sqlStr.append(" AND (ml.vigenteAte is not null and  ml.vigenteAte < ?) \n");
        }

        sqlStr.append(" ORDER BY ").append(campoOrdenar).append(" ").append(tipoOrdenacao);
        sqlStr.append(" limit ").append(limite).append(" offset ").append(pagina);


        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr.toString())) {
            int i = 1;
            sqlConsultar.setInt(i++, consultarMeioEnvio);
            sqlConsultar.setString(i++, consultarDescricao.toUpperCase() + "%");
            sqlConsultar.setString(i++, consultarRemetente.toUpperCase() + "%");
            if (inicio != null && fim != null && !codigoTipoAgendamento.equals(TipoAgendamentoEnum.TODOS.getCodigo())) {
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(inicio, "00:00"));
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(fim, "23:59"));
            }
            if (codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo()) || codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())) {
                sqlConsultar.setInt(i++, codigoTipoAgendamento);
            }
            if (inicioCriacao != null && fimCriacao != null) {
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(inicioCriacao, "00:00"));
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(fimCriacao, "23:59"));
            }
            if (!UteisValidacao.emptyNumber(codigo)) {
                sqlConsultar.setInt(i++, codigo);
            }
            if (!UteisValidacao.emptyNumber(codEmpresa)) {
                sqlConsultar.setInt(i++, codEmpresa);
            }

            if (tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.SOMENTE_VIGENTES)) {
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00"));
            }
            if (tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.VENCIDOS)) {
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00"));
            }

            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }



    public Integer totalizadorMalaDireta(Integer codigo, Date inicio, Date fim, String consultarDescricao,
                                        Date inicioCriacao, Date fimCriacao, String consultarRemetente,
                                        Integer consultarMeioEnvio, Integer codigoTipoAgendamento,
                                        TipoVigenciaConsultaEnum tipoVigencia,
                                        Integer codEmpresa) throws Exception{


        StringBuffer sqlStr = new StringBuffer();

        sqlStr.append(" SELECT count(distinct ml.*) totalregistros FROM maladireta ml \n");
        sqlStr.append(" INNER JOIN usuario u ON u.codigo = ml.remetente \n");
        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo())) {
            sqlStr.append(" LEFT JOIN mailinghistorico mh ON mh.maladireta = ml.codigo \n");
        }
        sqlStr.append(" LEFT JOIN mailingagendamento ma ON ma.maladireta = ml.codigo \n");
        sqlStr.append(" WHERE ml.meiodeenvio = ? ");
        sqlStr.append(" AND UPPER(ml.titulo) LIKE ? ");
        sqlStr.append(" AND UPPER(u.nome) LIKE ? ");

        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo())){
            sqlStr.append(" AND mh.datafim between ? and ?  \n");
        }
        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())){
            sqlStr.append(" AND ml.dataenvio between ? and ?  \n");
        }
        if(codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo()) || codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())){
            sqlStr.append(" AND ml.tipoagendamento = ? \n");
        }
        if (inicioCriacao != null && fimCriacao != null) {
            sqlStr.append(" AND ml.datacriacao between ? and ? \n");
        }
        if(!UteisValidacao.emptyNumber(codigo)){
            sqlStr.append(" AND ml.codigo = ? \n");
        }
        if (!UteisValidacao.emptyNumber(codEmpresa)) {
            sqlStr.append(" AND ml.empresa = ? \n");
        }
        if(tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.SOMENTE_VIGENTES)){
            sqlStr.append(" AND (ml.vigenteAte is null or  ml.vigenteAte >= ?) \n");
        }
        if(tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.VENCIDOS)){
            sqlStr.append(" AND (ml.vigenteAte is not null and  ml.vigenteAte < ?) \n");
        }

        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr.toString())) {
            int i = 1;
            sqlConsultar.setInt(i++, consultarMeioEnvio);
            sqlConsultar.setString(i++, consultarDescricao.toUpperCase() + "%");
            sqlConsultar.setString(i++, consultarRemetente.toUpperCase() + "%");
            if (inicio != null && fim != null && !codigoTipoAgendamento.equals(TipoAgendamentoEnum.TODOS.getCodigo())) {
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(inicio, "00:00"));
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(fim, "23:59"));
            }
            if (codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo()) || codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())) {
                sqlConsultar.setInt(i++, codigoTipoAgendamento);
            }
            if (inicioCriacao != null && fimCriacao != null) {
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(inicioCriacao, "00:00"));
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(fimCriacao, "23:59"));
            }
            if (!UteisValidacao.emptyNumber(codigo)) {
                sqlConsultar.setInt(i++, codigo);
            }
            if (!UteisValidacao.emptyNumber(codEmpresa)) {
                sqlConsultar.setInt(i++, codEmpresa);
            }

            if (tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.SOMENTE_VIGENTES)) {
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00"));
            }
            if (tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.VENCIDOS)) {
                sqlConsultar.setTimestamp(i++, Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00"));
            }

            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("totalregistros");
                }
            }
        }
        return 0;
    }



    /**
     * <AUTHOR> Alcides
     * 02/05/2013
     */
    private void preencherFiltros(Integer codigo, ConfPaginacao confPaginacao, Date inicio, Date fim, Date inicioCriacao,
                                  Date fimCriacao, String consultarDescricao, String consultarRemetente, Integer consultarMeioEnvio,
                                  Integer codigoTipoAgendamento, Integer codEmpresa, TipoVigenciaConsultaEnum tipoVigencia) throws Exception {
        int i = 0;
        confPaginacao.getStm().setInt(i++, consultarMeioEnvio);
        confPaginacao.getStm().setString(i++, consultarDescricao.toUpperCase() + "%");
        confPaginacao.getStm().setString(i++, consultarRemetente.toUpperCase() + "%");
        if (inicio != null && fim != null && !codigoTipoAgendamento.equals(TipoAgendamentoEnum.TODOS.getCodigo())) {
            confPaginacao.getStm().setTimestamp(i++, Uteis.getDataHoraJDBC(inicio, "00:00"));
            confPaginacao.getStm().setTimestamp(i++, Uteis.getDataHoraJDBC(fim, "23:59"));
        }
        if (codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo()) || codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())) {
            confPaginacao.getStm().setInt(i++, codigoTipoAgendamento);
        }
        if (inicioCriacao != null && fimCriacao != null) {
            confPaginacao.getStm().setTimestamp(i++, Uteis.getDataHoraJDBC(inicioCriacao, "00:00"));
            confPaginacao.getStm().setTimestamp(i++, Uteis.getDataHoraJDBC(fimCriacao, "23:59"));
        }
        if (!UteisValidacao.emptyNumber(codigo)) {
            confPaginacao.getStm().setInt(i++, codigo);
        }
        if (!UteisValidacao.emptyNumber(codEmpresa)) {
            confPaginacao.getStm().setInt(i++, codEmpresa);
        }
        
        if(tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.SOMENTE_VIGENTES)){
            confPaginacao.getStm().setTimestamp(i++, Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00"));

        }
        if(tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.VENCIDOS)){
            confPaginacao.getStm().setTimestamp(i++, Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00"));

        }
    }

    /**
     * <AUTHOR> Alcides
     * 02/05/2013
     */
    private void preencherFiltrosCount(Integer codigo, PreparedStatement stm, Date inicio, Date fim, Date inicioCriacao, Date fimCriacao, String consultarDescricao, String consultarRemetente,
                                       Integer consultarMeioEnvio, Integer codigoTipoAgendamento, Integer codEmpresa,
                        TipoVigenciaConsultaEnum tipoVigencia) throws Exception {
        int i = 1;
        stm.setInt(i++, consultarMeioEnvio);
        stm.setString(i++, consultarDescricao.toUpperCase()+"%");
        stm.setString(i++, consultarRemetente.toUpperCase()+"%");
        if(inicio != null && fim != null && !codigoTipoAgendamento.equals(TipoAgendamentoEnum.TODOS.getCodigo())){
            stm.setTimestamp(i++, Uteis.getDataHoraJDBC(inicio, "00:00"));
            stm.setTimestamp(i++, Uteis.getDataHoraJDBC(fim, "23:59"));
        }
        if(codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo()) || codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())){
            stm.setInt(i++, codigoTipoAgendamento);
        }
        if (inicioCriacao != null && fimCriacao != null) {
            stm.setTimestamp(i++, Uteis.getDataHoraJDBC(inicioCriacao, "00:00"));
            stm.setTimestamp(i++, Uteis.getDataHoraJDBC(fimCriacao, "23:59"));
        }
        if(!UteisValidacao.emptyNumber(codigo)){
            stm.setInt(i++, codigo);
        }
        if (!UteisValidacao.emptyNumber(codEmpresa)) {
            stm.setInt(i++, codEmpresa);
        }
        
        if(tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.SOMENTE_VIGENTES)){
            stm.setTimestamp(i++, Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00"));

        }
        if(tipoVigencia != null && tipoVigencia.equals(TipoVigenciaConsultaEnum.VENCIDOS)){
            stm.setTimestamp(i++, Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00"));

        }
    }

        public void incluirConfigEventoMailing(Integer maladireta, ConfigEventoMailingTO cfgEvento, Connection con) throws Exception{
            executarConsulta("DELETE FROM configeventomailing WHERE maladireta = "+maladireta, con);
            
            String insert = "INSERT INTO configeventomailing (maladireta, evento, inicio, fim, ocorrencia, codigosRiscos, nrfaltasmaior, nrfaltas,\n" +
                    "                                 nrDiasInicioFreePass, minimodiasvencido, minvalor, maxvalor, nomespendencias, codigostiposprodutos,\n" +
                    "                                 codigosprodutosessao, codigosambientes, codigoscolaboradores, naoclientes,\n" +
                    "                                 dmais, dmenos, qtdMinParcelasVencidas, qtdDiasParcelasVencidas, codigosprodutosvencidos, codigoErroRemessa, diasRemessa,\n" +
                    "                                 boletoParcelasVencendo, modeloPadraoBoleto)\n" +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement stm = con.prepareStatement(insert);
            int i = 1;
            stm.setInt(i++, maladireta);
            stm.setInt(i++, cfgEvento.getEvento().getCodigo());
            stm.setDate(i++, Uteis.getDataJDBC(cfgEvento.getInicio()));
            stm.setDate(i++, Uteis.getDataJDBC(cfgEvento.getFim()));
            stm.setInt(i++, cfgEvento.getOcorrencia());
            stm.setString(i++, Uteis.getListaEscolhidos(cfgEvento.getRiscos(), "Selecionado", "Codigo", true));
            stm.setBoolean(i++, cfgEvento.getNrFaltasMaior());
            stm.setInt(i++, cfgEvento.getNrFaltas());
            stm.setInt(i++, cfgEvento.getNrDiasInicioFreePass());
            stm.setInt(i++, cfgEvento.getMinimoDiasVencido());
            stm.setDouble(i++, cfgEvento.getMinValor());
            stm.setDouble(i++, cfgEvento.getMaxValor());

            stm.setString(i++, Uteis.getListaEscolhidos(cfgEvento.getPendencias(), "Selecionado", "Label", false));
            stm.setString(i++, Uteis.getListaEscolhidos(cfgEvento.getTipoProdutos(), "Selecionado", "CodigoString", false));
            stm.setString(i++, Uteis.getListaEscolhidos(cfgEvento.getProdutosSessao(), "Selecionado", "Codigo", true));
            stm.setString(i++, Uteis.getListaEscolhidos(cfgEvento.getAmbientes(), "Selecionado", "Codigo", true));
            stm.setString(i++, Uteis.getListaEscolhidos(cfgEvento.getColaboradores(), "Selecionado", "Codigo", true));

            stm.setBoolean(i++, cfgEvento.getNaoClientes());

            stm.setInt(i++, cfgEvento.getDiaMais());
            stm.setInt(i++, cfgEvento.getDiaMenos());
            stm.setInt(i++, cfgEvento.getQtdMinParcelasVencidas());
            if (!UteisValidacao.emptyNumber(cfgEvento.getDiasParcelasVencidasInicial())) {
                StringBuilder qtdDiasParcelasVencidas = new StringBuilder();
                qtdDiasParcelasVencidas.append(cfgEvento.getDiasParcelasVencidasInicial());
                if (!UteisValidacao.emptyNumber(cfgEvento.getDiasParcelasVencidasFinal())) {
                    qtdDiasParcelasVencidas.append(",");
                    qtdDiasParcelasVencidas.append(cfgEvento.getDiasParcelasVencidasFinal());
                }
                stm.setString(i++, qtdDiasParcelasVencidas.toString());
            } else {
                stm.setNull(i++, Types.VARCHAR);

            }
            stm.setString(i++, Uteis.getListaEscolhidos(cfgEvento.getListaProdutosVOs(), "Selecionado", "Codigo", true));
            stm.setString(i++, cfgEvento.getCodigoErroRemessa());
            stm.setInt(i++, cfgEvento.getDiasRemessa());
            stm.setBoolean(i++, cfgEvento.isBoletoParcelasVencendo());
            stm.setBoolean(i++, cfgEvento.isModeloPadraoBoleto());

            stm.execute();
        }

        public ConfigEventoMailingTO consultarConfigEvento(Integer malaDireta, boolean tela,  ConfigEventoMailingTO cfg) throws Exception{
            try (ResultSet rs = criarConsulta("SELECT * FROM configeventomailing WHERE maladireta = " + malaDireta, con)) {
                if (rs.next()) {
                    cfg.setDiasRemessa(rs.getInt("diasRemessa"));
                    cfg.setCodigoErroRemessa(rs.getString("codigoErroRemessa"));
                    cfg.setEventoCodigo(rs.getInt("evento"));
                    cfg.setInicio(rs.getDate("inicio"));
                    cfg.setFim(rs.getDate("fim"));
                    cfg.setOcorrencia(rs.getInt("ocorrencia"));
                    cfg.setCodigosRiscos(rs.getString("codigosRiscos"));
                    cfg.setNrFaltasMaior(rs.getBoolean("nrfaltasmaior"));
                    cfg.setNrFaltas(rs.getInt("nrfaltas"));
                    cfg.setNrDiasInicioFreePass(rs.getInt("nrDiasInicioFreePass"));
                    cfg.setMinimoDiasVencido(rs.getInt("minimodiasvencido"));
                    cfg.setMinValor(rs.getDouble("minvalor"));
                    cfg.setMaxValor(rs.getDouble("maxvalor"));

                    cfg.setNomesPendencias(rs.getString("nomespendencias"));
                    cfg.setCodigosTiposProdutos(rs.getString("codigostiposprodutos"));
                    cfg.setCodigosProdutoSessao(rs.getString("codigosprodutosessao"));
                    cfg.setCodigosProdutosVencidos(rs.getString("codigosprodutosvencidos"));
                    cfg.setCodigosAmbientes(rs.getString("codigosambientes"));
                    cfg.setCodigosColaboradores(rs.getString("codigoscolaboradores"));

                    cfg.setNaoClientes(rs.getBoolean("naoclientes"));

                    cfg.setDiaMais(rs.getInt("dmais"));
                    cfg.setDiaMenos(rs.getInt("dmenos"));
                    cfg.setQtdMinParcelasVencidas(rs.getInt("qtdMinParcelasVencidas"));

                    cfg.setBoletoParcelasVencendo(rs.getBoolean("boletoParcelasVencendo"));
                    cfg.setModeloPadraoBoleto(rs.getBoolean("modeloPadraoBoleto"));

                String qtdDiasParcelasVencidas = rs.getString("qtdDiasParcelasVencidas");
                if (!UteisValidacao.emptyString(qtdDiasParcelasVencidas) && !"[]".equals(qtdDiasParcelasVencidas)) {
                    String[] valores = qtdDiasParcelasVencidas.split(",");
                    cfg.setDiasParcelasVencidasInicial(Integer.valueOf(valores[0]));
                    if(valores.length == 2){
                        cfg.setDiasParcelasVencidasFinal(Integer.valueOf(valores[1]));
                    }
                }

                }
            }
            if(tela) {
                cfg.montarSelecionados();
                configProdutosVencidos(cfg);
            }

            return cfg;
        }

    private void configProdutosVencidos(ConfigEventoMailingTO cfg) throws Exception {
        if(!UteisValidacao.emptyList(Arrays.asList(cfg.getListaCodigosProdutosVencidos()))) {
            for (String codigo : cfg.getListaCodigosProdutosVencidos()) {
                ProdutoVO produto = getFacade().getProduto()
                        .consultarPorCodigoProduto(Integer.parseInt(codigo), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                produto.setSelecionado(true);
                cfg.getListaProdutosVOs().add(produto);
            }
        }
    }

    public List<MalaDiretaVO> consultarTodos(Integer codigo, ConfPaginacao confPaginacao,
                                                Date inicio, Date fim, String consultarDescricao,
                                                Date inicioCriacao, Date fimCriacao, String consultarRemetente,
                                                Integer consultarMeioEnvio, Integer codigoTipoAgendamento,
                                                Integer codEmpresa, Integer nivelMontarDados) throws Exception{

        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //sql principal
        StringBuffer sqlStr = new StringBuffer();

        sqlStr.append(" SELECT distinct ml.*, ma.ultimaexecucao FROM maladireta ml \n");
        sqlStr.append(" INNER JOIN usuario u ON u.codigo = ml.remetente \n");
        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo())) {
            sqlStr.append(" LEFT JOIN mailinghistorico mh ON mh.maladireta = ml.codigo \n");
        }
        sqlStr.append(" LEFT JOIN mailingagendamento ma ON ma.maladireta = ml.codigo \n");
        sqlStr.append(" WHERE ml.meiodeenvio = ? ");
        sqlStr.append(" AND UPPER(ml.titulo) LIKE ? ");
        sqlStr.append(" AND UPPER(u.nome) LIKE ? ");

        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo())){
            sqlStr.append(" AND mh.datafim between ? and ?  \n");
        }
        if(inicio != null && fim != null && codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())){
            sqlStr.append(" AND ml.dataenvio between ? and ?  \n");
        }
        if(codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo()) || codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo())){
            sqlStr.append(" AND ml.tipoagendamento = ? \n");
        }
        if (inicioCriacao != null && fimCriacao != null) {
            sqlStr.append(" AND ml.datacriacao between ? and ? \n");
        }
        if(!UteisValidacao.emptyNumber(codigo)){
            sqlStr.append(" AND ml.codigo = ? \n");
        }
        if (!UteisValidacao.emptyNumber(codEmpresa)) {
            sqlStr.append(" AND ml.empresa = ? \n");
        }
        String sqlCount = "SELECT COUNT(*) FROM (" + sqlStr.toString() + ") AS consultaCount";
        //sql inner joins

        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);

        sqlStr.append(" ORDER BY ml.codigo DESC ");

        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlStr);

        preencherFiltros(codigo, confPaginacao, inicio, fim, inicioCriacao, fimCriacao, consultarDescricao, consultarRemetente, consultarMeioEnvio, codigoTipoAgendamento, codEmpresa, null);

        try (PreparedStatement pStmCount = con.prepareStatement(sqlCount)) {
            preencherFiltrosCount(codigo, pStmCount, inicio, fim, inicioCriacao, fimCriacao, consultarDescricao, consultarRemetente, consultarMeioEnvio, codigoTipoAgendamento, codEmpresa, null);
            try (ResultSet rsCount = pStmCount.executeQuery()) {
                rsCount.next();
                //4 - REALIZA A CONSULTA COM PAGINACAO
                try (ResultSet tabelaResultado = confPaginacao.consultaPaginada(rsCount.getInt(1))) {
                    return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
                }
            }
        }

    }
    
    public void alterarSituacaoExclusao(MalaDiretaVO malaDireta, boolean excluida) throws Exception{
        try (PreparedStatement sqlInserir = con.prepareStatement("UPDATE maladireta SET excluida = ? WHERE codigo = ?")) {
            sqlInserir.setBoolean(1, excluida);
            sqlInserir.setInt(2, malaDireta.getCodigo());
            sqlInserir.execute();
        }
    }

    public void gravarCRMExtra(MalaDiretaVO mensagem) throws Exception {
        try {
            con.setAutoCommit(false);
            gravarCRMExtraSemCommit(mensagem);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void gravarCRMExtraSemCommit(MalaDiretaVO mensagem) throws Exception {
        if (mensagem.getNovoObj()) {
            incluirSemCommit(mensagem);
        } else {
            alterarSemCommit(mensagem);
            apagarFecharMetaFecharMetaDetalhadoCRMMetaExtra(mensagem);
        }
    }

    public List<MalaDiretaVO> consultarExisteMetaCRMExtraDiaUsuario(Date dia, Integer codUsuario) throws Exception {
        consultarCRM(getIdEntidade(), false);

        StringBuffer sqlStr = new StringBuffer();
        sqlStr.append("select \n");
        sqlStr.append("distinct(m.codigo), \n");
        sqlStr.append("m.titulo, \n");
        sqlStr.append("m.remetente as codusuariocriou, \n");
        sqlStr.append("m.datacriacao, \n");
        sqlStr.append("m.dataenvio as dtiniciometa, \n");
        sqlStr.append("m.vigenteate as dtfimmeta, \n");
        sqlStr.append("m.empresa, \n");
        sqlStr.append("m.tipoConsultorMetaExtraIndividual,\n");
        sqlStr.append("m.metaExtraIndividual\n");
        sqlStr.append("from maladireta m \n");
        sqlStr.append("left join maladiretacrmextracolaborador  mc on mc.maladireta = m.codigo \n");
        sqlStr.append("where \n");
        sqlStr.append("m.excluida = false \n");
        sqlStr.append("and date(m.dataenvio) <= '").append(Uteis.getData(dia)).append("' \n");
        sqlStr.append("and m.vigenteate  >= '").append(Uteis.getData(dia)).append("' \n");
        sqlStr.append("and (mc.usuario = ").append(codUsuario).append(" OR m.metaExtraIndividual )");

        List<MalaDiretaVO> resultado;
        try (PreparedStatement pStmCount = con.prepareStatement(sqlStr.toString())) {
            try (ResultSet dadosSQL = pStmCount.executeQuery()) {
                resultado = new ArrayList<MalaDiretaVO>();
                while (dadosSQL.next()) {
                    MalaDiretaVO obj = new MalaDiretaVO();
                    obj.setCodigo(dadosSQL.getInt("codigo"));
                    obj.setTitulo(dadosSQL.getString("titulo"));
                    obj.getRemetente().setCodigo(dadosSQL.getInt("codusuariocriou"));
                    obj.setDataCriacao(dadosSQL.getTimestamp("datacriacao"));
                    obj.setDataEnvio(dadosSQL.getDate("dtiniciometa"));
                    obj.setVigenteAte(dadosSQL.getDate("dtfimmeta"));
                    obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
                    obj.setTipoConsultorMetaExtraIndividual(dadosSQL.getString("tipoConsultorMetaExtraIndividual"));
                    obj.setMetaExtraIndividual(dadosSQL.getBoolean("metaExtraIndividual"));
                    resultado.add(obj);
                }
            }
        }
        return resultado;
    }

    private PreparedStatement getPSCRMExtra(Integer empresa) throws SQLException {

        StringBuffer sqlStr = new StringBuffer();
        sqlStr.append("select \n");
        sqlStr.append("md.codigo, \n");
        sqlStr.append("md.titulo, \n");
        sqlStr.append("md.remetente as codusuariocriou, \n");
        sqlStr.append("u.nome as usuariocriou, \n");
        sqlStr.append("md.datacriacao, \n");
        sqlStr.append("md.dataenvio as dtiniciometa, \n");
        sqlStr.append("md.vigenteate as dtfimmeta, \n");
        sqlStr.append("(select count(*) from maladiretacrmextracliente  where maladireta  = md.codigo) as meta, \n");
        sqlStr.append("md.excluida, \n");
        sqlStr.append("md.empresa \n");
        sqlStr.append("from maladireta md \n");
        sqlStr.append("inner join usuario u on u.codigo = md.remetente \n");
        sqlStr.append("where md.meiodeenvio  = ").append(MeioEnvio.CRM_EXTRA.getCodigo()).append(" \n");
        if (empresa != 0) {
            sqlStr.append("and md.empresa = ").append(empresa).append(" \n");
        }
        sqlStr.append("order by md.datacriacao desc;");

        return con.prepareStatement(sqlStr.toString());
    }

    public String consultarJSONCRMExtra(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPSCRMExtra(empresa).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("titulo"))).append("\",");
                if (rs.getBoolean("excluida")) {
                    json.append("\"").append(Uteis.normalizarStringJSON("Excluída")).append("\",");
                } else {
                    json.append("\"").append(Uteis.normalizarStringJSON("Ativo")).append("\",");
                }
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("usuariocriou"))).append("\",");
                json.append("\"").append(Uteis.getData(rs.getDate("datacriacao"))).append("\",");
                json.append("\"").append(Uteis.getData(rs.getDate("dtiniciometa"))).append("\",");
                json.append("\"").append(Uteis.getData(rs.getDate("dtfimmeta"))).append("\",");
                json.append("\"").append(rs.getInt("meta")).append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public void atualizaStatusCRMMetaExtra(Date dataRobo) throws Exception {
        try (PreparedStatement sql = con.prepareStatement("UPDATE maladireta SET excluida = TRUE WHERE (vigenteate < '" + Uteis.getDataJDBC(dataRobo) + "' OR vigenteate is null) AND excluida <> TRUE AND meiodeenvio = " + MeioEnvio.CRM_EXTRA.getCodigo())) {
            sql.execute();
        }
    }

    @Override
    public List<ConfiguracaoIntegracaoBotConversaVO> consultarFluxoEmpresa (Integer empresa) throws Exception {
        List<ConfiguracaoIntegracaoBotConversaVO> ccnfiguracaoIntegracaoBotConversaVO = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.*  \n");
        sql.append("FROM configuracaointegracaobotconversa  c \n");
        sql.append("WHERE c.empresa = ").append(empresa).append(" \n");

        ConfiguracaoIntegracaoBotConversaVO config;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    config = new ConfiguracaoIntegracaoBotConversaVO();
                    config.setCodigo(rs.getInt("codigo"));
                    config.setAtivo(rs.getBoolean("ativo"));
                    config.setDescricao(rs.getString("descricao"));
                    config.setTipofluxo(rs.getString("tipofluxo"));
                    config.setUrlwebhoobotconversa(rs.getString("urlwebhoobotconversa"));
                    ccnfiguracaoIntegracaoBotConversaVO.add(config);
                }
            }
        }
        return ccnfiguracaoIntegracaoBotConversaVO;

    }

    @Override
    public List<ConfiguracaoIntegracaoGymbotProVO> consultarFluxoEmpresaGymbotPro (Integer empresa) throws Exception {
        List<ConfiguracaoIntegracaoGymbotProVO> ccnfiguracaoIntegracaoBotConversaVO = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.*  \n");
        sql.append("FROM configuracaointegracaogymbotpro  c \n");
        sql.append("WHERE c.empresa = ").append(empresa).append(" \n");

        ConfiguracaoIntegracaoGymbotProVO config;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    config = new ConfiguracaoIntegracaoGymbotProVO();
                    config.setAtivo(rs.getBoolean("ativo"));
                    config.setCodigo(rs.getInt("codigo"));
                    config.setDescricao(rs.getString("descricao"));
                    config.setTipofluxo(rs.getString("tipofluxo"));
                    config.setToken(rs.getString("token"));
                    config.setIdFluxo(rs.getString("idFluxo"));
                    ccnfiguracaoIntegracaoBotConversaVO.add(config);
                }
            }
        }
        return ccnfiguracaoIntegracaoBotConversaVO;
    }

    @Override
    public List<ConfiguracaoIntegracaoBotConversaVO> consultarFluxoEmpresaFase (Integer empresa, String fase) throws Exception {
        List<ConfiguracaoIntegracaoBotConversaVO> ccnfiguracaoIntegracaoBotConversaVO = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.*  \n");
        sql.append("FROM configuracaointegracaobotconversa  c \n");
        sql.append("WHERE c.empresa = ").append(empresa).append(" \n");
        sql.append(" AND c.ativo ");
        sql.append(" and c.fase = '").append(fase).append("'");

        ConfiguracaoIntegracaoBotConversaVO config;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    config = new ConfiguracaoIntegracaoBotConversaVO();
                    config.setCodigo(rs.getInt("codigo"));
                    config.setDescricao(rs.getString("descricao"));
                    config.setUrlwebhoobotconversa(rs.getString("urlwebhoobotconversa"));
                    config.setFase(rs.getString("fase"));
                    ccnfiguracaoIntegracaoBotConversaVO.add(config);
                }
            }
        }
        return ccnfiguracaoIntegracaoBotConversaVO;

    }

    @Override
    public ConfiguracaoIntegracaoGymbotProVO consultarFluxoGymbotPro (Integer codigo, Integer empresa) throws Exception {
        List<ConfiguracaoIntegracaoGymbotProVO> consultarFluxoEmpresaFaseGymbotPro = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.*  \n");
        sql.append("FROM configuracaointegracaogymbotpro  c \n");
        sql.append("WHERE c.empresa = ").append(empresa).append(" \n");
        sql.append(" AND c.ativo ");
        sql.append(" and c.codigo = '").append(codigo).append("'");

        ConfiguracaoIntegracaoGymbotProVO config;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    config = new ConfiguracaoIntegracaoGymbotProVO();
                    config.setCodigo(rs.getInt("codigo"));
                    config.setAtivo(rs.getBoolean("ativo"));
                    config.setDescricao(rs.getString("descricao"));
                    config.setFase(rs.getString("fase"));
                    config.setToken(rs.getString("token"));
                    config.setIdFluxo(rs.getString("idFluxo"));
                    consultarFluxoEmpresaFaseGymbotPro.add(config);
                }
            }
        }
        return consultarFluxoEmpresaFaseGymbotPro.get(0);
    }

    @Override
    public List<ConfiguracaoIntegracaoGymbotProVO> consultarFluxoEmpresaFaseGymbotPro (Integer empresa, String fase) throws Exception {
        List<ConfiguracaoIntegracaoGymbotProVO> consultarFluxoEmpresaFaseGymbotPro = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.*  \n");
        sql.append("FROM configuracaointegracaogymbotpro  c \n");
        sql.append("WHERE c.empresa = ").append(empresa).append(" \n");
        sql.append(" and c.fase = '").append(fase).append("'");
        sql.append(" AND c.ativo ");

        ConfiguracaoIntegracaoGymbotProVO config;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    config = new ConfiguracaoIntegracaoGymbotProVO();
                    config.setCodigo(rs.getInt("codigo"));
                    config.setDescricao(rs.getString("descricao"));
                    config.setFase(rs.getString("fase"));
                    config.setToken(rs.getString("token"));
                    config.setIdFluxo(rs.getString("idFluxo"));
                    consultarFluxoEmpresaFaseGymbotPro.add(config);
                }
            }
        }
        return consultarFluxoEmpresaFaseGymbotPro;
    }

    @Override
    public List<Integer> consultarCodigosPorOcorrencia(OcorrenciaEnum ocorrencia, Integer empresa) throws Exception {
        List<Integer> codigosMalaDireta = new ArrayList<Integer>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ma.codigo \n");
        sql.append("FROM maladireta ma \n");
        sql.append("INNER JOIN configeventomailing ce ON ce.maladireta = ma.codigo \n");
        sql.append("WHERE ce.ocorrencia = ").append(ocorrencia.getCodigo()).append(" \n");
        sql.append("and not excluida \n");
        sql.append("and vigenteate >= now() \n");
        sql.append("and (ma.empresa = ").append(empresa).append(" or ma.todasempresas = true)");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    codigosMalaDireta.add(rs.getInt("codigo"));
                }
            }
        }
        return codigosMalaDireta;
    }

    public ConfiguracaoIntegracaoGymbotProVO buscarFluxoGymbotProPeloCodigoEmpresa(Integer empresaId, Connection con) throws Exception{
        if(con == null){
            con = this.con;
        }

        StringBuilder jpql = new StringBuilder();
        jpql.append("SELECT c.* ");
        jpql.append("FROM ConfiguracaoIntegracaoGymbotPro c ");
        jpql.append("WHERE c.empresa = ").append(Integer.valueOf(empresaId));
        jpql.append(" AND c.tipoFluxo = '"+2+"'");
        jpql.append(" AND c.ativo ");

        String urlWebHooBotConversa = null;

        ConfiguracaoIntegracaoGymbotProVO config = new ConfiguracaoIntegracaoGymbotProVO();
        try (PreparedStatement ps = con.prepareStatement(jpql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    config = new ConfiguracaoIntegracaoGymbotProVO();
                    config.setCodigo(rs.getInt("codigo"));
                    config.setDescricao(rs.getString("descricao"));
                    config.setTipofluxo(rs.getString("tipofluxo"));
                    config.setIdFluxo(rs.getString("idFluxo"));
                    config.setToken(rs.getString("token"));
                }
            }
        }
        return config;
    }

    public ConfiguracaoIntegracaoBotConversaVO buscarFluxoPeloCodigoEmpresa(Integer empresaId, Connection con) throws Exception{
        if(con == null){
            con = this.con;
        }

        StringBuilder jpql = new StringBuilder();
        jpql.append("SELECT c.* ");
        jpql.append("FROM ConfiguracaoIntegracaoBotConversa c ");
        jpql.append("WHERE c.empresa = ").append(Integer.valueOf(empresaId));
        jpql.append("AND c.tipoFluxo = '"+2+"'");
        jpql.append(" AND c.ativo ");

        String urlWebHooBotConversa = null;

        ConfiguracaoIntegracaoBotConversaVO config = new ConfiguracaoIntegracaoBotConversaVO();
        try (PreparedStatement ps = con.prepareStatement(jpql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    config = new ConfiguracaoIntegracaoBotConversaVO();
                    config.setCodigo(rs.getInt("codigo"));
                    config.setDescricao(rs.getString("descricao"));
                    config.setTipofluxo(rs.getString("tipofluxo"));
                    config.setUrlwebhoobotconversa(rs.getString("urlwebhoobotconversa"));
                }
            }
        }
        return config;
    }

    public String findbyFluxoGymbotProTelaAluno (Integer empresaId,  Connection con) throws Exception{
        if(con == null){
            con = this.con;
        }

        StringBuilder jpql = new StringBuilder();
        jpql.append("SELECT c.descricao ");
        jpql.append("FROM configuracaointegracaogymbotpro c ");
        jpql.append("WHERE c.empresa = ").append(Integer.valueOf(empresaId));
        jpql.append(" AND c.ativo ");
        jpql.append("AND c.tipoFluxo = '"+2+"'");

        String descricao = null;

        try (PreparedStatement ps = con.prepareStatement(jpql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    descricao = rs.getString("descricao");
                }
            }
        }

        return descricao;
    }

    public String findbyFluxoUrlTelaAluno (Integer empresaId,  Connection con) throws Exception{
        if(con == null){
            con = this.con;
        }

        StringBuilder jpql = new StringBuilder();
        jpql.append("SELECT c.urlWebHooBotConversa ");
        jpql.append("FROM ConfiguracaoIntegracaoBotConversa c ");
        jpql.append("WHERE c.empresa = ").append(Integer.valueOf(empresaId));
        jpql.append("AND c.tipoFluxo = '"+2+"'");
        jpql.append(" AND c.ativo ");

        String urlWebHooBotConversa = null;

        try (PreparedStatement ps = con.prepareStatement(jpql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    urlWebHooBotConversa = rs.getString("urlWebHooBotConversa");
                }
            }
        }

        return urlWebHooBotConversa;

    }

    public ConfiguracaoIntegracaoBotConversaVO buscarFluxoBotConversa(Connection con, String urlWebhookbot, String tipoFluxo) throws Exception{
        if(con == null){
            con = this.con;
        }

        String sql = "SELECT c.* " +
                "FROM ConfiguracaoIntegracaoBotConversa c " +
                "WHERE c.urlwebhoobotconversa = ? " +
                "AND c.tipoFluxo = ? " +
                "AND c.ativo = true";

        String urlWebHooBotConversa = null;

        ConfiguracaoIntegracaoBotConversaVO config = new ConfiguracaoIntegracaoBotConversaVO();
        try (PreparedStatement ps = con.prepareStatement(sql)){
            ps.setString(1, urlWebhookbot);
            ps.setString(2, tipoFluxo);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    config = new ConfiguracaoIntegracaoBotConversaVO();
                    config.setCodigo(rs.getInt("codigo"));
                    config.setDescricao(rs.getString("descricao"));
                    config.setTipofluxo(rs.getString("tipofluxo"));
                    config.setUrlwebhoobotconversa(rs.getString("urlwebhoobotconversa"));
                }
            }
        }
        return config;
    }

    public boolean clienteValidoParaEnvio(OcorrenciaEnum ocorrencia, ClienteVO clienteVO, Integer malaDireta) throws Exception {
        return clienteValidoParaEnvio(ocorrencia, clienteVO, malaDireta, null);
    }

    public boolean clienteValidoParaEnvio(OcorrenciaEnum ocorrencia, ClienteVO clienteVO, Integer malaDireta, Connection con) throws Exception {
        if(con == null){
            con = this.con;
        }
        //valida se o aluno terá email enviado após cancelamento manual ou automático
        if (ocorrencia.equals(OcorrenciaEnum.APOS_CANCELAMENTO)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("(co.observacao ilike '%CANCELAMENTO AUTOMATICO%') as cancelamentoAutomatico, \n");
            sql.append("(select tipocancelamento from maladireta where codigo = ").append(malaDireta).append(") as tipocancelamento \n");
            sql.append("from situacaoclientesinteticodw sw \n");
            sql.append("left join contratooperacao co on co.contrato = sw.codigocontrato \n");
            sql.append("where co.tipooperacao  = 'CA' \n");
            sql.append("and sw.codigocliente  = ").append(clienteVO.getCodigo());

            boolean cancelamentoAutomatico = false;
            Integer tipocancelamento = 0;
            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        cancelamentoAutomatico = rs.getBoolean("cancelamentoAutomatico");
                        tipocancelamento = rs.getInt("tipocancelamento");
                    }
                }
            }
            return tipocancelamento.equals(0) || (cancelamentoAutomatico && tipocancelamento.equals(2)) || (!cancelamentoAutomatico && tipocancelamento.equals(1));
        }

        String sql = "select " +
                "    c.codigo, " +
                "    c.situacao, " +
                "    to_char(p.datacadastro,'YYYY-MM-DD') = '"+Uteis.getDataJDBC(Calendario.hoje())+"' as cadastroHoje " +
                "from cliente c " +
                "         inner join pessoa p on p.codigo = c.pessoa " +
                "Where c.codigo = "+clienteVO.getCodigo();

        boolean cadastroHoje = false;
        boolean visitante = false;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    cadastroHoje = rs.getBoolean("cadastroHoje");
                    visitante = rs.getString("situacao").equals("VI");
                }
            }
        }

        return !ocorrencia.equals(OcorrenciaEnum.INCLUSAO_VISITANTE) || (cadastroHoje && visitante);
    }

    private void apagarFecharMetaFecharMetaDetalhadoCRMMetaExtra(MalaDiretaVO malaDiretaVO) throws Exception {
        getFacade().getFecharMetaDetalhado().excluirFecharMetaDetalhadoMalaDireta(malaDiretaVO);
        getFacade().getFecharMeta().excluirFecharMetaMalaDireta(malaDiretaVO);
    }

    public Integer countEmails(Date dia, boolean igual, boolean integracaoPacto) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("select COUNT(CODIGO) as emails from historicocontato\n");
            sb.append("where tipocontato = 'EM'\n");
            sb.append(" and dia >= '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd 00:00:00")).append("'\n");
            if (igual) {
                sb.append(" and dia <= '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd 23:59:59")).append("'\n");
            }
            if (integracaoPacto) {
                sb.append("and wagienvi = true\n");
            }
            ResultSet rs = criarConsulta(sb.toString(), con);
            return rs.next() ? rs.getInt("emails") : 0;
        } catch (Exception e) {
            return 0;
        }
    }

    public String footerEmails(String html, EmpresaVO empresa) throws Exception {
        Empresa empresaDAO;
        try {
            if (html.contains("unsubscribe-optin")) {
                return html;
            }

            empresaDAO = new Empresa(this.con);

            MalaDiretaControle malaDiretaControle = new MalaDiretaControle();
            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(empresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            String urlUnsubscribe = url + "/unsubscribe-optin?key=";

            String parametrosEmpresa = malaDiretaControle.getKey() + "&" + empresaVO.getCodigo() + "&" + empresaVO.getEmail() + "&";
            parametrosEmpresa = Criptografia.encrypt(parametrosEmpresa, SuperControle.Crypt_KEY, AlgoritmoCriptoEnum.ALGORITMO_AES);

            String parametrosCliente = "&TAG_CODCLIENTE" + "&TAG_EMAIL_CLIENTE";
            parametrosCliente = Criptografia.encrypt(parametrosCliente, SuperControle.Crypt_KEY, AlgoritmoCriptoEnum.ALGORITMO_AES);

            String[] result = html.split("</body>");

            String footer = "</body>" +
                    "<footer>" +
                    "<p style=\"text-align: center; font-size: 13px; color: #83888F;\">" +
                    "Se deseja não receber mais mensagens como esta, clique " +
                    "<a target=\"_blank\" style=\"color: inherit; font-size: 13px\" href=\"" + urlUnsubscribe + parametrosEmpresa + "+" + parametrosCliente + "\"><b>aqui</b></a>." +
                    "</p>" +
                    "</footer>";
            html = result[0] + footer + (result.length > 1 ? result[1] : "");
            return html;
        } finally {
            empresaDAO = null;
        }
    }

}
