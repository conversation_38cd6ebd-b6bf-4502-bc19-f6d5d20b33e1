package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.enumeradores.OcorrenciaEnum;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;
import br.com.pactosolucoes.enumeradores.TipoEventoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.SituacaoClienteTO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.crm.MailingAgendamentoVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.MailingAgendamentoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;

public class MailingAgendamento extends SuperEntidade implements MailingAgendamentoInterfaceFacade {

    public MailingAgendamento(Connection conexao) throws Exception {
        super(conexao);
    }

    public MailingAgendamento() throws Exception {
        super();
    }

    public static MailingAgendamentoVO montarDados(ResultSet dados, Connection con) throws Exception {
        MailingAgendamentoVO mailing = new MailingAgendamentoVO();
        mailing.setCodigo(dados.getInt("codigo"));
        mailing.setCron(dados.getString("cron"));
        mailing.setDataInicial(dados.getTimestamp("dataInicial"));
        mailing.setOcorrencia(OcorrenciaEnum.getOcorrencia(dados.getInt("ocorrencia")));
        mailing.setMalaDireta(dados.getInt("maladireta"));
        mailing.setUltimaExecucao(dados.getTimestamp("ultimaexecucao"));
        if (!UteisValidacao.emptyString(mailing.getCron())) {
            mailing.interpretarCRON();
        }
        return mailing;
    }

    @Override
    public MailingAgendamentoVO consultar(MalaDiretaVO malaDireta) throws Exception {
        ResultSet dados = criarConsulta("SELECT * FROM mailingagendamento WHERE maladireta = " + malaDireta.getCodigo(), con);
        if (dados.next()) {
            return montarDados(dados, con);
        } else
            return new MailingAgendamentoVO();
    }

    @Override
    public void excluir(MailingAgendamentoVO agendamento) throws Exception {
        executarConsulta("DELETE FROM mailingagendamento WHERE codigo = " + agendamento.getCodigo(), con);
    }

    @Override
    public void incluir(MailingAgendamentoVO agendamento, int maladireta) throws Exception {
        executarConsulta("DELETE FROM mailingagendamento WHERE maladireta = " + maladireta, con);
        PreparedStatement stm = con.prepareStatement("INSERT INTO mailingagendamento(cron, datainicial, ocorrencia, maladireta) VALUES (?,?,?,?)");
        int i = 0;
        if (UteisValidacao.emptyString(agendamento.getCron())) {
            agendamento.montarCronDataAtual();
        }
        stm.setString(++i, agendamento.getCron());
        stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(agendamento.getDataInicial()));
        stm.setInt(++i, agendamento.getOcorrencia().getCodigo());
        stm.setInt(++i, maladireta);
        stm.execute();
    }

    public void montarSql(MalaDiretaVO malaDireta, Integer empresa,
                          List<CategoriaVO> categoriaVOs,
                          List<SituacaoClienteTO> situacaoClienteTOs,
                          List<ModalidadeVO> modalidadeVOs,
                          List<ColaboradorVO> consultoresVOs,
                          List<ColaboradorVO> professoresVOs,
                          List<PlanoVO> planoVOs,
                          List<ContratoDuracaoVO> contratoDuracaoVOs,
                          List<ConvenioCobrancaVO> convenioCobrancaVO,
                          Date inicioVencimento, Date fimVencimento,
                          Integer inicioDiasSemAparecer, Integer fimDiasSemAparecer, boolean dist) throws Exception{


        String distinct = " distinct ";
        if (!dist)  distinct="";

        String sql = "SELECT "+ distinct +" %s FROM cliente cli INNER JOIN situacaoclientesinteticodw sw ON sw.codigocliente = cli.codigo ";
        String join = "";
        String where = " WHERE 1=1 ";
        boolean pessoa = false;
        boolean contrato = false;

        if (malaDireta.getCfgEvento().getEvento() != null) {
            if (malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.INDICADOS)) {
                sql = malaDireta.getCfgEvento().getEvento().getWhere();
                if(!UteisValidacao.emptyNumber(malaDireta.getMailingFiltros().getEvento())){
                    join +=  " INNER JOIN indicacao ind ON sw.indicacao = ind.codigo\n";
                    where += " and ind.evento = " + malaDireta.getMailingFiltros().getEvento();
                }
                malaDireta.setSql(sql + join + where);
                return;
            }

            where = malaDireta.getCfgEvento().getEvento().getWhere();
            join = malaDireta.getCfgEvento().getEvento().getJoin();

            pessoa = malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.VISITANTES)
                    || malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.DEBITO)
                    || malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.VENCIMENTO_PRODUTO)
                    || malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.COMPRA_PRODUTO)
                    || malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.PRODUTOS_VENDIDOS)
                    || malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS);
            contrato = malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.RENOVAR);

            if (malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.ACESSOS_INTERVALO_DIAS)) {
                where = where.replace("{quantidadeMinimaAcessos}", Integer.toString(malaDireta.getQuantidadeMinimaAcessos()))
                        .replace("{quantidadeMaximaAcessos}", Integer.toString(malaDireta.getQuantidadeMaximaAcessos()));
                join = join.replace("{intevaloDias}", Integer.toString(malaDireta.getIntervaloDias()))
                    .replace("{situacoesAcesso}", SituacaoAcessoEnum.consultarSituacoesLiberacaoIds());
            }

            if(malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.CANCELADO_INTERVALO_DIAS)){
                where = where.replace("{intevaloDias}", Integer.toString(malaDireta.getIntervaloDias()));
                if (malaDireta.getTipoCancelamento() == null){
                        malaDireta.setTipoCancelamento(0);
                }
                //verifica o tipo do cancelamento para concatenar o "AND" necessário.
                // 0 = todos, 1 = manual, 2 = automático
                if (malaDireta.getTipoCancelamento() == 0) {
                    where = where.replace("{tipoCancelamento}", "");
                }
                else if (malaDireta.getTipoCancelamento() == 1) {
                    where = where.replace("{tipoCancelamento}", "AND u.username <> 'RECOR'");
                }
                else if (malaDireta.getTipoCancelamento() == 2) {
                    where = where.replace("{tipoCancelamento}", "AND u.username = 'RECOR'");
                }
            }

            if (malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.CARTOES_VENCENDO)) {
                StringBuilder sqlAlterar = new StringBuilder();

                //INSTANTANEO verificar datas selecionadas no filtro
                if (malaDireta.getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.INSTANTANEO.getCodigo())) {

                    List<Date> listaMeses = Uteis.getMesesEntreDatas(malaDireta.getCfgEvento().getInicio(), malaDireta.getCfgEvento().getFim());
                    assert listaMeses != null;
                    for (Date mes : listaMeses) {
                        sqlAlterar.append("'").append(Uteis.getDataMesAnoConcatenado(mes)).append("',");
                    }
                    sqlAlterar.deleteCharAt(sqlAlterar.length()-1);

                } else { // MENSALMENTE buscar do próximo mês
                    sqlAlterar.append("to_char((now() + interval '1 month'), 'MM/YYYY')");
                }

                where = where.replace("LISTA_MESES", sqlAlterar);
            }
        }

        switch (malaDireta.getMeioDeEnvioEnum()) {
            case EMAIL:
                join += "\n INNER JOIN email e ON e.pessoa = cli.pessoa AND e.emailcorrespondencia IS TRUE AND e.bloqueadobounce IS FALSE ";
                break;
            case SMS:
                join += "\n INNER JOIN telefone t ON (t.pessoa = cli.pessoa and t.tipotelefone = 'CE')";
                break;
        }
        join += " left join optin o on o.cliente = cli.codigo ";

        //LISTAS
        String codigosCategoria = Uteis.getListaEscolhidos(categoriaVOs, "Selecionado", "Codigo", true);
        String codigosModalidade = Uteis.getListaEscolhidos(modalidadeVOs, "Selecionado", "Codigo", true);
        String codigosConsultores = Uteis.getListaEscolhidos(consultoresVOs, "Selecionado", "Codigo", true);
        String codigosProfessores = Uteis.getListaEscolhidos(professoresVOs, "Selecionado", "Codigo", true);
        String codigosPlano = Uteis.getListaEscolhidos(planoVOs, "Selecionado", "Codigo", true);
        String codigosContratoDuracao = Uteis.getListaEscolhidos(contratoDuracaoVOs, "Selecionado", "NumeroMeses", true);
        String codigosConvenios = Uteis.getListaEscolhidos(convenioCobrancaVO, "Selecionado", "Codigo", true);

        //SITUACOES
        String situacoes = "";
        String situacoesContrato = "";
        String situacoesContratoInner = "";
        for(SituacaoClienteTO situacao : situacaoClienteTOs){
            if(situacao.getSelecionado() && situacao.getSituacaoClienteEnum().isSituacaoCliente()){
                situacoes += ",'"+situacao.getSituacaoClienteEnum().getCodigo()+"'";
            }
            if(situacao.getSelecionado() && (!situacao.getSituacaoClienteEnum().isSituacaoCliente() || situacao.getSituacaoClienteEnum().isSituacaoContratoTambem())){
                if(situacao.getSituacaoClienteEnum().equals(SituacaoClienteEnum.TRANCADO)){
                    situacoesContrato += ",'"+situacao.getSituacaoClienteEnum().getCodigo()+"', 'TV'";
                }else{
                    situacoesContrato += ",'"+situacao.getSituacaoClienteEnum().getCodigo()+"'";
                    situacoesContratoInner += ",'"+situacao.getSituacaoClienteEnum().getSituacaoContrato()+"'";
                }
            }
        }

        //Cuidado ao mexer nessa validação.
        //Só é pra validar OptIn se for emails de marketing. Alguns eventos pré-definidos não precisam validar optin e serão sempre enviados, até mesmo se o aluno tiver clicado no email que não quer receber mais emails.
        //Isso porque o optin é para propagandas e emails de Marketing em sí. No caso por exemplo de mailing de evento pré-definido de cobranças de parcelas em aberto não se trata de um marketing e por isso deve ignorar optIn
        // e será sempre enviado independente se o aluno autorizou o optin ou não..
        if (malaDireta.getMeioDeEnvioEnum() == MeioEnvio.EMAIL && verificarOptIn(malaDireta) && !isOcorrenciaIgnorarOptIn(malaDireta)) {
            where += "  and (o.bloqueadobounce = false and dataexclusao is null  or o.bloqueadobounce is null) ";
        }

        //CATEGORIA
        if (!UteisValidacao.emptyString(codigosCategoria)) {
            where += "\n and cli.categoria IN (" + codigosCategoria+") ";
        }

        //COLABORADORES
        if (!UteisValidacao.emptyString(codigosConsultores)||!UteisValidacao.emptyString(codigosProfessores) ) {
            join += "\n INNER JOIN vinculo vi ON vi.cliente = cli.codigo ";
            where += "\n and vi.colaborador in ("+(codigosConsultores
                    + ((UteisValidacao.emptyString(codigosConsultores)
                        || UteisValidacao.emptyString(codigosProfessores)) ? "" : ",") + codigosProfessores)+") ";
        }

        //PLANOS
        if (!UteisValidacao.emptyString(codigosPlano)) {
            join += "\n INNER JOIN contrato con2 ON con2.pessoa = sw.codigopessoa ";
            where += "\n and con2.plano in (" + codigosPlano+") ";
            if (!UteisValidacao.emptyString(situacoesContratoInner)) {
                join += "\n and con2.situacao in (" + situacoesContratoInner.replaceFirst(",", "")+") ";
            }
            if (malaDireta.getMailingFiltros().getSomenteRecorrencia()) {
                where += "\n and con2.regimerecorrencia = true ";
            }
        } else if (malaDireta.getMailingFiltros().getSomenteRecorrencia()) {
            join += "\n INNER JOIN contrato con2 ON con2.pessoa = sw.codigopessoa ";
            join += "\n INNER JOIN plano pl ON pl.codigo = con2.plano ";
            where += "\n and pl.recorrencia = true ";
            where += "\n and con2.situacao = 'AT' ";
        }

        //MODALIDADES
        if (!UteisValidacao.emptyString(codigosModalidade)) {
            join += "\n INNER JOIN contratomodalidade cm ON cm.contrato = sw.codigocontrato ";
            where += "\n and cm.modalidade in (" + codigosModalidade + ") ";
        }

        //DURACAO
        if (!UteisValidacao.emptyString(codigosContratoDuracao)) {
            where += "\n and sw.duracaocontratomeses in ("+codigosContratoDuracao+") ";
        }

        //SITUACOES
        if (!UteisValidacao.emptyString(situacoes)) {
                where += "\n and (sw.situacao IN (" + situacoes.replaceFirst(",", "") + ")";
                where += UteisValidacao.emptyString(situacoesContrato) ? ")"
                        : " or sw.situacaocontrato IN (" + situacoesContrato.replaceFirst(",", "") + "))";
        }
        if (UteisValidacao.emptyString(situacoes) && !UteisValidacao.emptyString(situacoesContrato)) {
                where += "\n and sw.situacaocontrato IN (" + situacoesContrato.replaceFirst(",", "") + ")";
        }



        //EMPRESA
        if (!malaDireta.isTodasEmpresas() && !UteisValidacao.emptyNumber(empresa)) {
            where += "\n and cli.empresa = " + empresa;
        }

        //IDADE maior que
        if (!UteisValidacao.emptyNumber(malaDireta.getMailingFiltros().getIdadeMin())) {
            where += "\n and sw.idade >= " + malaDireta.getMailingFiltros().getIdadeMin();
        }
        //IDADE menor que
        if (!UteisValidacao.emptyNumber(malaDireta.getMailingFiltros().getIdadeMax())) {
            where += "\n and sw.idade <= " + malaDireta.getMailingFiltros().getIdadeMax();
        }

        //data cadastro maior que
        if (malaDireta.getMailingFiltros().getDataCadastroMin() != null || malaDireta.getMailingFiltros().getDataCadastroMax() != null) {
            if (join.contains("pessoa pes")) {
                join += "\n INNER JOIN pessoa pes2 ON pes2.codigo = cli.pessoa ";
                pessoa = true;
                where += malaDireta.getMailingFiltros().getDataCadastroMin() != null ?
                        "\n and pes2.datacadastro >= '"+Uteis.getDataJDBC(malaDireta.getMailingFiltros().getDataCadastroMin())+" 00:00:00'" : "";

                where += malaDireta.getMailingFiltros().getDataCadastroMax() != null ?
                        "\n and pes2.datacadastro <= '"+Uteis.getDataJDBC(malaDireta.getMailingFiltros().getDataCadastroMax())+" 23:59:59'" : "";
            }else{
                join += pessoa ? "" :"\n INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ";
                pessoa = true;
                where += malaDireta.getMailingFiltros().getDataCadastroMin() != null ?
                        "\n and pes.datacadastro >= '"+Uteis.getDataJDBC(malaDireta.getMailingFiltros().getDataCadastroMin())+" 00:00:00'" : "";

                where += malaDireta.getMailingFiltros().getDataCadastroMax() != null ?
                        "\n and pes.datacadastro <= '"+Uteis.getDataJDBC(malaDireta.getMailingFiltros().getDataCadastroMax())+" 23:59:59'" : "";
            }

        }
        if(!UteisValidacao.emptyNumber(malaDireta.getMailingFiltros().getEvento())){
            where += " and qc.evento = "+malaDireta.getMailingFiltros().getEvento();
           join +=  " INNER JOIN questionariocliente qc ON cli.codigo = qc.cliente \n";
        }
        if((malaDireta.getMailingFiltros().isFeminino()
                || malaDireta.getMailingFiltros().isMasculino())
                && !(malaDireta.getMailingFiltros().isFeminino() && malaDireta.getMailingFiltros().isMasculino())){
            join += pessoa ? "" : "\n INNER JOIN pessoa pes3 ON pes3.codigo = cli.pessoa ";
            where += malaDireta.getMailingFiltros().isFeminino() ? "\n AND pes3.sexo LIKE 'F'" : "";
            where += malaDireta.getMailingFiltros().isMasculino() ? "\n AND pes3.sexo LIKE 'M'" : "";
        }

        if (inicioVencimento != null) {
            if (!join.contains("contrato con2")) {
                join += "\n INNER JOIN contrato con2 ON con2.codigo = sw.codigocontrato ";
            }
            where += " AND con2.vigenciaateajustada >= '" + Uteis.getDataHoraJDBC(inicioVencimento, "00:00:00") + "' ";
        }
        if (fimVencimento != null) {
            if (!join.contains("contrato con2")) {
                join += "\n INNER JOIN contrato con2 ON con2.codigo = sw.codigocontrato ";
            }
            where += " AND con2.vigenciaateajustada <= '" + Uteis.getDataHoraJDBC(fimVencimento, "23:59:59") + "' ";
        }

        if ((inicioDiasSemAparecer != null && inicioDiasSemAparecer > 0) && (fimDiasSemAparecer != null && fimDiasSemAparecer > 0)) {
            if (!join.contains("acessocliente ac ")) {
                join += "\n LEFT JOIN acessocliente ac ON ac.codigo = cli.uacodigo ";
            }
            where += " AND extract(day from (current_timestamp - ac.dthrentrada )) BETWEEN " + inicioDiasSemAparecer + " AND " + fimDiasSemAparecer;
        }

        if(malaDireta.getTipoEvento() == TipoEventoEnum.EVENTO_REMATRICULADOS && malaDireta.getMeioDeEnvioEnum() == MeioEnvio.SMS){
            join +=" and not exists (select 1 from smsenviados envs where envs.dataenvio::date BETWEEN '%s' AND '%s'  and envs.pessoa = t.pessoa and envs.cliente = cli.codigo) ";
        }

        if(malaDireta.getTipoEvento() == TipoEventoEnum.EVENTO_MATRICULADOS && malaDireta.getTipoAgendamento() == TipoAgendamentoEnum.AGENDAMENTO_PREVISTO
            && join.contains("MAT_CONTRATO")){
            where += " and  not exists (select 1 from malingenviados envs where envs.dataenvio::date BETWEEN '%s' AND '%s'  and envs.pessoa = MAT_CONTRATO.pessoa and envs.cliente = cli.codigo) ";
        }

        if (malaDireta.getCfgEvento().getEvento() != null && malaDireta.getCfgEvento().getEvento().equals(TipoEventoEnum.CONVENIO_COBRANCA) && !UteisValidacao.emptyString(codigosConvenios)) {
            where = where.replace("LISTA_CONVENIO",  " AND acc.conveniocobranca in ("+codigosConvenios+") " );
         }
        else {
            where =  where .replace("LISTA_CONVENIO", "");
        }

        malaDireta.setSql(sql + join + where );
    }

    public boolean verificarOptIn(MalaDiretaVO malaDiretaVO){
        try {
            if (malaDiretaVO != null && malaDiretaVO.getCfgEvento() != null && malaDiretaVO.getCfgEvento().getEvento() != null) {
                return malaDiretaVO.getCfgEvento().getEvento().isVerificarOptIn();
            }
            return true;
        } catch (Exception ex) {
            Uteis.logarDebug("");
            return true;
        }
    }

    public boolean isOcorrenciaIgnorarOptIn(MalaDiretaVO malaDiretaVO) {
        try {
            if (malaDiretaVO.getAgendamento() != null && malaDiretaVO.getAgendamento().getOcorrencia() != null &&
                    (malaDiretaVO.getAgendamento().getOcorrencia().equals(OcorrenciaEnum.INCLUSAO_VISITANTE) ||
                            malaDiretaVO.getAgendamento().getOcorrencia().equals(OcorrenciaEnum.VENDA_CONTRATO) ||
                            malaDiretaVO.getAgendamento().getOcorrencia().equals(OcorrenciaEnum.APOS_MATRICULA) ||
                            malaDiretaVO.getAgendamento().getOcorrencia().equals(OcorrenciaEnum.APOS_RENOVACAO) ||
                            malaDiretaVO.getAgendamento().getOcorrencia().equals(OcorrenciaEnum.APOS_REMATRICULA) ||
                            malaDiretaVO.getAgendamento().getOcorrencia().equals(OcorrenciaEnum.APOS_CANCELAMENTO))) {
                return true;
            }
            return false;
        } catch (Exception ex) {
            Uteis.logarDebug("");
            return false;
        }
    }


    public List<GenericoTO> consultarSimples(String campoCodigo, String campoLabel, String tabela, String campoCondicao, String condicao) throws Exception{
        return consultarSimples("SELECT "+campoCodigo+" as codigo, "+campoLabel+" as label FROM "
                +tabela+" WHERE "+campoCondicao+" LIKE '"+condicao+"%' ", con);

    }
}
