/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.acesso;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.acesso.AcessoColaboradorVO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LiberacaoAcessoVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.interfaces.acesso.AcessoColaboradorInterfaceFacade;
import negocio.interfaces.acesso.ColetorInterfaceFacade;
import negocio.interfaces.acesso.LocalAcessoInterfaceFacade;
import negocio.interfaces.basico.ColaboradorInterfaceFacade;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

/**
 *
 * <AUTHOR>
 */
public class AcessoColaborador extends SuperEntidade implements AcessoColaboradorInterfaceFacade {

    protected static String idEntidade;

    public AcessoColaborador() throws Exception {
        super();
        setIdEntidade("AcessoColaborador");
    }

    public AcessoColaborador(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("AcessoColaborador");
    }

    private Integer alterar(AcessoColaboradorVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            //alterar(idEntidade);
            String sql = "UPDATE AcessoColaborador set dthrsaida=?, sentido=?, meioIdentificacaoSaida=? where codigo=? ";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setTimestamp(1, new java.sql.Timestamp(obj.getDataHoraSaida().getTime()));
            sqlAlterar.setString(2, obj.getSentido());
            sqlAlterar.setInt(3, obj.getMeioIdentificacaoSaida().getCodigo());
            sqlAlterar.setInt(4, obj.getCodigo());
            sqlAlterar.execute();

            con.commit();

            return obj.getCodigo();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public Integer registrarAcessoColaborador(Date dataAcesso, ColaboradorVO colaborador,
            DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor,
            MeioIdentificacaoEnum meioIdentificacao) throws Exception {

        return registrarAcessoColaborador(dataAcesso, colaborador,
                direcao, local, coletor,
                meioIdentificacao, null);
    }

    public Integer registrarAcessoColaborador(Date dataAcesso, ColaboradorVO colaborador,
                                              DirecaoAcessoEnum direcao, LocalAcessoVO local, ColetorVO coletor,
                                              MeioIdentificacaoEnum meioIdentificacao, LiberacaoAcessoVO liberacaoAcessoVO) throws Exception {

        AcessoColaboradorVO obj = null;
        if (direcao == DirecaoAcessoEnum.DA_ENTRADA) {
            obj = this.novo();
            obj.setColaborador(colaborador);
            obj.setColetor(coletor);
            obj.setDataHoraEntrada(dataAcesso);
            obj.setLocalAcesso(local);
            obj.setSentido("E");
            obj.setMeioIdentificacaoEntrada(meioIdentificacao);
            obj.setLiberacaoacesso(liberacaoAcessoVO);

            return this.incluir(obj);
        } else if (direcao == DirecaoAcessoEnum.DA_SAIDA) {
            return this.registrarSaida(dataAcesso, colaborador, meioIdentificacao, local.getCodigo(), coletor);
        }
        return null;
    }

    public List<AcessoColaboradorVO> consultarTodosAcessos(ColaboradorVO colaborador, int nivelMontarDados) throws Exception {
        consultar(idEntidade);

        String sql = "SELECT * FROM acessocolaborador where colaborador = ?";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, colaborador.getCodigo());

        stm.setFetchDirection(ResultSet.FETCH_REVERSE);
        ResultSet resultTabela = stm.executeQuery(sql);

        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    public AcessoColaboradorVO consultarUltimoAcesso(ColaboradorVO colaborador, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AcessoColaborador WHERE colaborador = ? "
                + "order by codigo desc limit 1";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, colaborador.getCodigo());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        if (!tabelaResultado.next()) {
            return new AcessoColaboradorVO();
        } else {
            return montarDados(tabelaResultado, nivelMontarDados);
        }
    }

    public List<AcessoColaboradorVO> consultarUltimos5Acessos(ColaboradorVO colaborador, int nivelMontarDados) throws Exception {
        consultar(idEntidade);

        //String sql = "SELECT * FROM acessocolaborador where colaborador = ? ORDER BY codigo DESC";
        String sql = "select ac.* from AcessoColaborador ac inner join colaborador c on c.codigo = ac.colaborador  "
                + "where (c.pessoa = ?) "
                + "order by ac.dthrentrada DESC "
                + "offset 0 limit 5";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, colaborador.getPessoa().getCodigo());
        ResultSet resultTabela = stm.executeQuery();

        return montarDadosConsulta(resultTabela, nivelMontarDados);
    }

    private Integer incluir(AcessoColaboradorVO obj) throws Exception {
        UsuarioVO usuarioParaAtualizar = null;
        try {
            String sql = "INSERT INTO acessocolaborador (colaborador, dthrentrada, sentido, localacesso, coletor,meioIdentificacaoEntrada, liberacaoacesso) VALUES (?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, obj.getColaborador().getCodigo());
            sqlInserir.setTimestamp(2, new java.sql.Timestamp(obj.getDataHoraEntrada().getTime()));
            sqlInserir.setString(3, obj.getSentido());
            sqlInserir.setInt(4, obj.getLocalAcesso().getCodigo());
            sqlInserir.setInt(5, obj.getColetor().getCodigo());
            sqlInserir.setInt(6, obj.getMeioIdentificacaoEntrada().getCodigo());
            if (obj.getLiberacaoacesso() == null || UteisValidacao.emptyNumber(obj.getLiberacaoacesso().getCodigo())) {
                sqlInserir.setNull(7, 0);
            } else {
                sqlInserir.setInt(7, obj.getLiberacaoacesso().getCodigo());
            }

            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);

            // Preparar dados do usuário ANTES do commit
            Usuario usuarioDAO = new Usuario(con);
            obj.getColaborador().setUsuarioVO(usuarioDAO.consultarPorCodigoColaborador(obj.getColaborador().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));

            if (obj.getColaborador().getUsuarioVO() != null && !UteisValidacao.emptyNumber(obj.getColaborador().getUsuarioVO().getCodigo())) {
                usuarioParaAtualizar = obj.getColaborador().getUsuarioVO();
            }

            // Atualizar último acesso APÓS o commit (sem deadlock)
            if (usuarioParaAtualizar != null) {
                try {
                    usuarioDAO.registrarUltimoLoginAcessoAgora(usuarioParaAtualizar);
                } catch (Exception ex) {
                    // Log do erro mas não falha a operação principal
                    Logger.getLogger(getClass().getSimpleName()).log(Level.WARNING,
                            "#### ERRO AO REGISTRAR ÚLTIMO LOGIN (operação principal foi bem-sucedida). ERRO: " + ex.getMessage(), ex);
                }
            }

            usuarioDAO = null;
            return obj.getCodigo();

        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO REGISTRAR ENTRADA DO COLABORADOR. ERRO: " + e.getMessage());
            obj.setNovoObj(true);
            throw e;
        }
    }

    public AcessoColaboradorVO novo() throws Exception {
        incluir(idEntidade);
        AcessoColaboradorVO obj = new AcessoColaboradorVO();
        return obj;
    }

    public void setIdEntidade(String idEntidade) {
        AcessoColaborador.idEntidade = idEntidade;
    }

    private AcessoColaboradorVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        AcessoColaboradorVO obj = new AcessoColaboradorVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setMeioIdentificacaoEntrada(MeioIdentificacaoEnum.getMeioIdentificacao(dadosSQL.getInt("meioIdentificacaoEntrada")));
        obj.setMeioIdentificacaoSaida(MeioIdentificacaoEnum.getMeioIdentificacao(dadosSQL.getInt("meioIdentificacaoSaida")));
        obj.setSentido(dadosSQL.getString("sentido"));
        obj.setDataHoraEntrada(dadosSQL.getTimestamp("dthrentrada"));
        obj.setDataHoraSaida(dadosSQL.getTimestamp("dthrsaida"));
        obj.setColaborador(new ColaboradorVO());
        obj.getColaborador().setCodigo(dadosSQL.getInt("colaborador"));
        obj.setLocalAcesso(new LocalAcessoVO());
        obj.getLocalAcesso().setCodigo(dadosSQL.getInt("localacesso"));
        obj.setColetor(new ColetorVO());
        obj.getColetor().setCodigo(dadosSQL.getInt("coletor"));

        return obj;
    }

    private AcessoColaboradorVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        AcessoColaboradorVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        } else if ((nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS)
                || ((nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS))) {
            ColaboradorInterfaceFacade colaboradorDao = getFacade().getColaborador();
            obj.setColaborador(colaboradorDao.consultarPorChavePrimaria(obj.getColaborador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            LocalAcessoInterfaceFacade localAcessoDao = getFacade().getLocalAcesso();
            obj.setLocalAcesso(localAcessoDao.consultarPorCodigo(obj.getLocalAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            ColetorInterfaceFacade coletorDao = getFacade().getColetor();
            obj.setColetor(coletorDao.consultarPorCodigo(obj.getColetor().getCodigo()));
        }

        return obj;
    }

    private List<AcessoColaboradorVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<AcessoColaboradorVO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            AcessoColaboradorVO obj = new AcessoColaboradorVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * 09/02/11 Ulisses...
     * Operação responsável por localizar um objeto da classe
     * <code>AcessoColaboradorVO</code> através de sua chave primária.
     *
     * @exception Exception
     *                Caso haja problemas de conexão ou localização do objeto
     *                procurado.
     */
    public AcessoColaboradorVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM acessoColaborador WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( AcessoColaborador ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));

    }

    /**
     * 10/02/11 Ulisses...
     * Operação responsável por localizar um objeto da classe
     * <code>AcessoColaboradorVO</code> através de sua chave primária.
     *
     * @exception Exception
     *                Caso haja problemas de conexão ou localização do objeto
     *                procurado.
     */
    public AcessoColaboradorVO consultarPorCodigo(Integer codigoPrm, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM acessoColaborador WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (tabelaResultado.next()) {
            return (montarDados(tabelaResultado, nivelMontarDados));
        }
        return null;
    }

    public int consultarTotalAcessosFiltros(int empresa, java.sql.Date dataInicial, java.sql.Date dataFinal, String horaInicial, String horaFinal) throws SQLException {
        String sqlStr = "select count(*) as qtde from acessocolaborador "
                + "inner join colaborador on colaborador.codigo = acessocolaborador.colaborador "
                + "inner join empresa on empresa.codigo = colaborador.empresa "
                + "where empresa = " + empresa + " "
                + "and ((acessocolaborador.dthrentrada >= '" + dataInicial + " " + horaInicial + "' and acessocolaborador.dthrentrada <= '" + dataFinal + " " + horaFinal + "')"
                + " or (acessocolaborador.dthrsaida >= '" + dataInicial + " " + horaInicial + "' and acessocolaborador.dthrsaida <= '" + dataFinal + " " + horaFinal + "'))"
                + " ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("qtde");
        } else {
            return 0;
        }
    }
    @Override
    public AcessoColaboradorVO consultarUltimoAcessoPorLocal(ColaboradorVO colaborador,Date data, Integer localAcesso, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AcessoColaborador WHERE colaborador = ? and dthrentrada > ? and localacesso = ? "
                + "order by dthrentrada desc limit 1";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, colaborador.getCodigo());
        sqlConsultar.setDate(2, Uteis.getDataJDBC(data));
        sqlConsultar.setInt(3, localAcesso);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        if (!tabelaResultado.next()) {
            return new AcessoColaboradorVO();
        } else {
            return montarDados(tabelaResultado, nivelMontarDados);
        }
    }
    
      private Integer registrarSaida(Date dataAcesso,
            ColaboradorVO colaborador,
            MeioIdentificacaoEnum meioIdentificacao,
            Integer localAcesso, ColetorVO coletorVO) throws Exception {
        
        PreparedStatement pstUa = con.prepareStatement("select codigo from acessocolaborador where colaborador = "+colaborador.getCodigo()+" and dthrentrada > '"+Uteis.getDataJDBC(dataAcesso)+"' and localacesso = "+localAcesso+" order by dthrentrada desc limit 1");
        ResultSet rsUa = pstUa.executeQuery();
        if(rsUa.next()){
            colaborador.setUaColaborador(new AcessoColaboradorVO());
            colaborador.getUaColaborador().setCodigo(rsUa.getInt("codigo"));
        }
        if ((colaborador.getUaColaborador() == null) 
                || (colaborador.getUaColaborador().getCodigo() == null)
                || (colaborador.getUaColaborador().getCodigo() == 0)) {
            //throw new Exception("Erro ao registrar Saída. Não foi encontrado o último acesso do colaborador. ");
            registrarSaidaSemEntrada(dataAcesso,colaborador,localAcesso,coletorVO,meioIdentificacao);
        }
        try {
            String sql = "update acessocolaborador "
                    + "set dthrSaida = ?, sentido=?, meioIdentificacaoSaida=? "
                    + "where codigo = ? ";

            PreparedStatement pst = con.prepareStatement(sql);
            pst.setTimestamp(1, new java.sql.Timestamp(dataAcesso.getTime()));
            pst.setString(2, "S");
            pst.setInt(3, meioIdentificacao.getCodigo());
            pst.setInt(4, colaborador.getUaColaborador().getCodigo());
            pst.execute();
            return colaborador.getUaColaborador().getCodigo();
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO REGISTRAR SAIDA DO COLABORADOR. ERRO: " + e.getMessage());
            throw e;
        }
    }

    private Integer registrarSaidaSemEntrada(Date dataAcesso, ColaboradorVO colaborador,Integer codigoLocalAcesso, ColetorVO coletor, MeioIdentificacaoEnum meioIdentificacao) throws Exception{
        try{
            String sql = "INSERT INTO acessocolaborador (colaborador, dthrentrada, dthrSaida, sentido, localacesso, coletor, meioIdentificacaoEntrada, meioIdentificacaoSaida) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement pst = con.prepareStatement(sql);
            pst.setInt(1, colaborador.getCodigo());
            pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataAcesso));
            pst.setTimestamp(3, Uteis.getDataJDBCTimestamp(dataAcesso));
            pst.setString(4, "S");
            pst.setInt(5, codigoLocalAcesso);
            pst.setInt(6, coletor.getCodigo());
            pst.setInt(7, meioIdentificacao.getCodigo());
            pst.setInt(8, meioIdentificacao.getCodigo());
            pst.execute();
            Usuario usuarioDAO= new Usuario(con);
            colaborador.setUsuarioVO(usuarioDAO.consultarPorCodigoColaborador(colaborador.getCodigo(),Uteis.NIVELMONTARDADOS_MINIMOS));
            if(colaborador.getUsuarioVO()!=null && !UteisValidacao.emptyNumber(colaborador.getUsuarioVO().getCodigo()))
                usuarioDAO.registrarUltimoLoginAcessoAgora(colaborador.getUsuarioVO());
            return obterValorChavePrimariaCodigo();
        }catch (Exception e){
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO REGISTRAR SAIDA SEM ENTRADA(COLABORADOR). ERRO: " + e.getMessage());
            throw e;
        }
    }

    public List<ItemRelatorioTO> consultarUltimosAcessosRelatorio(Integer pessoa, String dataInicio, String dataTermino) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT log.codigo, log.sentido, log.dthrentrada, log.dthrsaida, log.meioIdentificacaoEntrada, log.meioIdentificacaoSaida,\n");
        sql.append("la.descricao as localacesso, colet.descricao as coletor,\n");
        sql.append("em.nome as empresa,\n");
        sql.append("pes.dataCadastro, pes.nome, pes.dataNasc, pes.tipoPessoa,\n");
        sql.append("pes.cfp, pes.rg, pes.rgOrgao, pes.rgUf,\n");
        sql.append("cid.nome as cidade, est.descricao as estado, pais.nome as pais,\n");
        sql.append("col.codigo as colaborador,\n");
        sql.append("ende.ceps, ende.logradouros, ende.numeros, ende.complementos, ende.bairros, email.emails, tel.telefones\n");
        sql.append("FROM AcessoColaborador log\n");
        sql.append("INNER JOIN colaborador col ON col.codigo = log.colaborador\n");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = col.pessoa\n");
        sql.append("LEFT JOIN empresa em ON em.codigo = col.empresa\n");
        sql.append("LEFT JOIN cidade cid ON cid.codigo = pes.cidade\n");
        sql.append("LEFT JOIN estado est ON est.codigo = cid.estado\n");
        sql.append("LEFT JOIN pais ON pais.codigo = est.pais\n");
        sql.append("LEFT JOIN LocalAcesso la ON la.codigo = log.localacesso\n");
        sql.append("LEFT JOIN Coletor colet ON colet.codigo = log.coletor\n");
        sql.append("LEFT JOIN (\n");
        sql.append("  SELECT pessoa, STRING_AGG(cep, ' | '  ORDER BY codigo DESC) as ceps,\n");
        sql.append("  STRING_AGG(endereco, ' | ' ORDER BY codigo DESC) as logradouros,\n");
        sql.append("  STRING_AGG(numero, ' | ' ORDER BY codigo DESC) as numeros,\n");
        sql.append("  STRING_AGG(complemento, ' | ' ORDER BY codigo DESC) as complementos,\n");
        sql.append("  STRING_AGG(bairro, ' | ' ORDER BY codigo DESC) as bairros\n");
        sql.append("  FROM endereco\n");
        sql.append("  GROUP BY pessoa\n");
        sql.append("  ORDER BY pessoa\n");
        sql.append(") ende ON ende.pessoa = pes.codigo\n");
        sql.append("LEFT JOIN (\n");
        sql.append("  SELECT pessoa, STRING_AGG(email, ' | ' ORDER BY codigo DESC) as emails\n");
        sql.append("  FROM email\n");
        sql.append("  GROUP BY pessoa\n");
        sql.append("  ORDER BY pessoa\n");
        sql.append(") email ON email.pessoa = pes.codigo\n");
        sql.append("LEFT JOIN (\n");
        sql.append("  SELECT pessoa, STRING_AGG(numero, ' | ' ORDER BY codigo DESC) as telefones\n");
        sql.append("  FROM telefone\n");
        sql.append("  GROUP BY pessoa\n");
        sql.append("  ORDER BY pessoa\n");
        sql.append(") tel ON tel.pessoa = pes.codigo\n");
        sql.append("WHERE col.pessoa = ").append(pessoa).append("\n");
        sql.append("and log.dthrentrada between '").append(dataInicio + " 00:00:00.000' and '").append(dataTermino + " 23:59:59.000'\n");
        sql.append("ORDER BY log.dthrentrada desc");

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());

        List lista = new ArrayList();
        while (rs.next()) {
            ItemRelatorioTO obj = new ItemRelatorioTO();
            obj.setUltimoAcesso(new Integer(rs.getInt("codigo")));
            obj.setDataInicioOperacao(rs.getTimestamp("dthrentrada"));
            obj.setDataFimOperacao(rs.getTimestamp("dthrsaida"));
            obj.setLocalAcesso(rs.getString("localacesso"));
            obj.setColetor(rs.getString("coletor"));
            obj.setSentido(rs.getString("sentido"));
            obj.setMeioIdentificacaoEntrada(MeioIdentificacaoEnum.getMeioIdentificacaoDescricao(rs.getInt("meioIdentificacaoEntrada")));
            obj.setMeioIdentificacaoSaida(MeioIdentificacaoEnum.getMeioIdentificacaoDescricao(rs.getInt("meioIdentificacaoSaida")));
            obj.setNomeEmpresa(rs.getString("empresa"));
            obj.setNome(rs.getString("nome"));
            obj.setDataCadastro(rs.getTimestamp("dataCadastro"));
            obj.setDataNascimento(rs.getTimestamp("dataNasc"));
            obj.setCategoria(TipoPessoa.obterPorCodigo(rs.getInt("tipoPessoa")) != null ? TipoPessoa.obterPorCodigo(rs.getInt("tipoPessoa")).getLabel() : "");
            obj.setCpf(rs.getString("cfp"));
            obj.setRg(rs.getString("rg"));
            obj.setRgOrgao(rs.getString("rgOrgao"));
            obj.setRgUf(rs.getString("rgUf"));
            obj.setCidade(rs.getString("cidade"));
            obj.setEstado(rs.getString("estado"));
            obj.setPais(rs.getString("pais"));
            obj.setCodColaborador(rs.getInt("colaborador"));
            obj.setCep(rs.getString("ceps"));
            obj.setLogradouro(rs.getString("logradouros"));
            obj.setNumero(rs.getString("numeros"));
            obj.setComplemento(rs.getString("complementos"));
            obj.setBairro(rs.getString("bairros"));
            obj.setEmail(rs.getString("emails"));
            obj.setTelefone(rs.getString("telefones"));
            lista.add(obj);
        }
        return lista;
    }

}
