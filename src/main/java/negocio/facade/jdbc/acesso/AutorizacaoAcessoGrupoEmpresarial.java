/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.acesso;

import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.interfaces.acesso.AutorizacaoAcessoGrupoEmpresarialInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.IntegracaoCadastrosWSConsumer;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutorizacaoAcessoGrupoEmpresarial extends SuperEntidade implements AutorizacaoAcessoGrupoEmpresarialInterfaceFacade {

    private static final String COL_CODIGO = "codigo";
    private static final String COL_CODIGO_MATRICULA = "codigomatricula";
    private static final String COL_NOME_PESSOA = "nomepessoa";
    private static final String COL_COD_ACESSO = "codacesso";

    public AutorizacaoAcessoGrupoEmpresarial(Connection conexao) throws Exception {
        super(conexao);
    }

    public AutorizacaoAcessoGrupoEmpresarial() throws Exception {
        super();
    }

    @Override
    public void incluir(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception {
         try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception {
        AutorizacaoAcessoGrupoEmpresarialVO.validarDados(obj);
        String sql = "INSERT INTO autorizacaoacessogrupoempresarial(\n" +
                "empresalocal, empresaremota, integracaoacessogrupoempresarial,\n" +
                "tipopessoa, nomepessoa, codigopessoa, codacesso, codigomatricula,\n" +
                "codigogenerico, codacessoalternativo, usuarioresponsavel, senhaacesso,\n" +
                "props, fotokey, assinaturabiometriafacial, assinaturaBiometriaDigital, cpf)\n" +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setInt(++i, obj.getEmpresaLocal().getCodigo());
            stm.setInt(++i, obj.getEmpresaRemota().getCodigo());
            stm.setInt(++i, obj.getIntegracao().getCodigo());
            stm.setString(++i, obj.getTipoPessoa());
            stm.setString(++i, obj.getNomePessoa());
            stm.setInt(++i, obj.getCodigoPessoa());
            stm.setString(++i, obj.getCodAcesso());
            resolveIntegerNull(stm, ++i, obj.getCodigoMatricula());
            stm.setInt(++i, obj.getCodigoGenerico());
            stm.setString(++i, obj.getCodAcessoAlternativo());
            stm.setInt(++i, obj.getUsuarioResponsavel().getCodigo());
            stm.setString(++i, obj.getSenhaAcesso());
            stm.setString(++i, obj.getPropsText());
            stm.setString(++i, obj.getFotoKey());
            stm.setString(++i, obj.getAssinaturaBiometriaFacial());
            stm.setString(++i, obj.getAssinaturaBiometriaDigital());
            stm.setString(++i, obj.getCpf());
            stm.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
            String gerarCodAutorizacao = gerarCodAutorizacao(obj.getCodigo());
            obj.setCodigoAutorizacao(gerarCodAutorizacao);
            executarConsulta("UPDATE autorizacaoacessogrupoempresarial\n" +
                    "SET codigoautorizacao = '" + gerarCodAutorizacao + "'\n" +
                    "WHERE codigo = " + obj.getCodigo(), con);
        }
    }


    @Override
    public void alterar(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception {
        AutorizacaoAcessoGrupoEmpresarialVO.validarDados(obj);
        String sql = "UPDATE autorizacaoacessogrupoempresarial\n" +
                "SET empresalocal = ?, empresaremota = ?, integracaoacessogrupoempresarial = ?,\n" +
                "tipopessoa = ?, nomepessoa = ?, codigopessoa = ?, codacesso = ?, codigomatricula = ?,\n" +
                "codigogenerico = ?, codacessoalternativo = ?, usuarioresponsavel = ?, senhaacesso = ?,\n" +
                "props  = ?, fotokey = ?, assinaturabiometriafacial = ?, assinaturaBiometriaDigital = ?,\n" +
                "cpf = ?\n" +
                "where codigo = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setInt(++i, obj.getEmpresaLocal().getCodigo());
            stm.setInt(++i, obj.getEmpresaRemota().getCodigo());
            stm.setInt(++i, obj.getIntegracao().getCodigo());
            stm.setString(++i, obj.getTipoPessoa());
            stm.setString(++i, obj.getNomePessoa());
            stm.setInt(++i, obj.getCodigoPessoa());
            stm.setString(++i, obj.getCodAcesso());
            resolveIntegerNull(stm, ++i, obj.getCodigoMatricula());
            stm.setInt(++i, obj.getCodigoGenerico());
            stm.setString(++i, obj.getCodAcessoAlternativo());
            stm.setInt(++i, obj.getUsuarioResponsavel().getCodigo());
            stm.setString(++i, obj.getSenhaAcesso());
            stm.setString(++i, obj.getPropsText());
            stm.setString(++i, obj.getFotoKey());
            stm.setString(++i, obj.getAssinaturaBiometriaFacial());
            stm.setString(++i, obj.getAssinaturaBiometriaDigital());
            stm.setString(++i, obj.getCpf());
            stm.setInt(++i, obj.getCodigo());
            stm.execute();
        }
    }

    @Override
    public String consultarJSON(Integer empresa) throws Exception {
        String sql = "SELECT\n" +
                "  aa.codigo, aa.nomepessoa, aa.codacesso, aa.codigomatricula, aa.tipopessoa \n" +
                "FROM autorizacaoacessogrupoempresarial aa\n";
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql += "WHERE empresalocal = " + empresa + "\n";
        }
        sql += "  ORDER BY nomepessoa";

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql);
             ResultSet rs = sqlConsultar.executeQuery()) {

            JSONObject objReturn = new JSONObject();
            JSONArray aaData = new JSONArray();

            while (rs.next()) {
                JSONArray itemArray = new JSONArray();
                itemArray.put(rs.getString(COL_CODIGO));
                itemArray.put(rs.getString(COL_NOME_PESSOA));

                String codAcesso = rs.getString(COL_COD_ACESSO);
                if (codAcesso.startsWith("NU")) {
                    itemArray.put("");
                } else {
                    itemArray.put(codAcesso);
                }

                String tipo = rs.getString("tipopessoa");
                if (!UteisValidacao.emptyString(tipo)) {
                    itemArray.put(TipoPessoaEnum.getTipo(tipo).getLabel());
                } else {
                    itemArray.put("");
                }

                int codMatricula = rs.getInt(COL_CODIGO_MATRICULA);
                if (UteisValidacao.emptyNumber(codMatricula)) {
                    itemArray.put("");
                } else {
                    itemArray.put(rs.getString(COL_CODIGO_MATRICULA));
                }
                aaData.put(itemArray);
            }

            objReturn.put("aaData", aaData);
            return objReturn.toString();
        }
    }


    public String gerarCodAutorizacao(Integer codigo) {
        return "AUT" + String.format("%07d", codigo);
    }

    public static List<AutorizacaoAcessoGrupoEmpresarialVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<AutorizacaoAcessoGrupoEmpresarialVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            AutorizacaoAcessoGrupoEmpresarialVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public AutorizacaoAcessoGrupoEmpresarialVO consultarPorCodigo(
            Integer codigo,
            Integer codigoMatricula,
            String codAcesso,
            String codAcessoAlternativo,
            String senhaAcesso,
            String codigoAutorizacao,
            String tipo,
            Integer codigoGenerico,
            Integer nivelMontarDados,
            String chaveRemota,
            Integer codEmpresaRemota) throws Exception {
        if (!UteisValidacao.emptyString(codAcesso) && codAcesso.startsWith("AUT")) {
            codigoAutorizacao = codAcesso;
            codAcesso = "";
        }
        StringBuilder sql = new StringBuilder("SELECT * FROM autorizacaoacessogrupoempresarial acge\n");
        sql.append("INNER JOIN integracaoacessogrupoempresarial iage ON acge.integracaoacessogrupoempresarial = iage.codigo\n");
        sql.append("WHERE 1 = 1\n");

        boolean existeInformacaoParaFiltrar = false;
        if (!UteisValidacao.emptyNumber(codigo)) {
            sql.append("AND acge.codigo = ").append(codigo).append("\n");
            existeInformacaoParaFiltrar = true;
        } else if (!UteisValidacao.emptyNumber(codigoMatricula)) {
            sql.append("AND acge.codigomatricula = ").append(codigoMatricula).append("\n");
            existeInformacaoParaFiltrar = true;
        } else if (!UteisValidacao.emptyString(codAcesso)) {
            if (codAcesso.length() == 8 || codAcesso.startsWith("T")) {
                sql.append("AND acge.codAcessoAlternativo like '").append(codAcesso.replaceFirst("T", "")).append("'\n");
                existeInformacaoParaFiltrar = true;
            } else {
                sql.append("AND acge.codacesso like '").append(codAcesso).append("'\n");
                existeInformacaoParaFiltrar = true;
            }
        } else if (!UteisValidacao.emptyString(codAcessoAlternativo)) {
            sql.append("AND acge.codAcessoAlternativo like '").append(codAcessoAlternativo).append("'\n");
            existeInformacaoParaFiltrar = true;
        } else if (!UteisValidacao.emptyString(senhaAcesso)) {
            sql.append("AND acge.senhaAcesso like '").append(Uteis.encriptar(senhaAcesso)).append("'\n");
            existeInformacaoParaFiltrar = true;
        } else if (!UteisValidacao.emptyString(codigoAutorizacao)) {
            sql.append("AND acge.codigoAutorizacao like '").append(codigoAutorizacao).append("'\n");
            existeInformacaoParaFiltrar = true;
        } else if (!UteisValidacao.emptyNumber(codigoGenerico)) {
            sql.append("AND acge.codigoGenerico = ").append(codigoGenerico).append("\n");
            existeInformacaoParaFiltrar = true;
        }

        if (!existeInformacaoParaFiltrar) {
            return new AutorizacaoAcessoGrupoEmpresarialVO();
        }

        if (!UteisValidacao.emptyString(tipo)) {
            sql.append("AND tipopessoa like '").append(tipo).append("'\n");
        }

        if (!UteisValidacao.emptyString(chaveRemota)) {
            sql.append("AND iage.chave = '").append(chaveRemota).append("'\n");
        }

        if (!UteisValidacao.emptyNumber(codEmpresaRemota)) {
            sql.append("AND iage.empresaremota = ").append(codEmpresaRemota).append("\n");
        }

        ResultSet set = criarConsulta(sql.toString(), con);
        return set.next() ? montarDados(set, nivelMontarDados, con) : new AutorizacaoAcessoGrupoEmpresarialVO();
    }

    public List<AutorizacaoAcessoGrupoEmpresarialVO> consultarPorIntegracaoAcesso(Integer codIntegracao) throws Exception {
        String sql = "SELECT * FROM autorizacaoacessogrupoempresarial WHERE integracaoacessogrupoempresarial = " + codIntegracao + " ORDER BY codigo";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
            }
        }
    }

    public static AutorizacaoAcessoGrupoEmpresarialVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        AutorizacaoAcessoGrupoEmpresarialVO obj = new AutorizacaoAcessoGrupoEmpresarialVO();
        obj.setCodigo(dadosSQL.getInt(COL_CODIGO));
        obj.getEmpresaRemota().setCodigo(dadosSQL.getInt("empresaRemota"));
        obj.getEmpresaLocal().setCodigo(dadosSQL.getInt("empresaLocal"));
        obj.getIntegracao().setCodigo(dadosSQL.getInt("integracaoacessogrupoempresarial"));
        obj.getUsuarioResponsavel().setCodigo(dadosSQL.getInt("usuarioresponsavel"));
        obj.setCodigoPessoa(dadosSQL.getInt("codigopessoa"));
        obj.setCodigoGenerico(dadosSQL.getInt("codigogenerico"));
        obj.setCodigoMatricula(dadosSQL.getInt(COL_CODIGO_MATRICULA));
        obj.setTipoPessoa(dadosSQL.getString("tipopessoa"));
        obj.setNomePessoa(dadosSQL.getString(COL_NOME_PESSOA));
        obj.setCodAcesso(dadosSQL.getString(COL_COD_ACESSO));
        obj.setCodAcessoAlternativo(dadosSQL.getString("codacessoalternativo"));
        obj.setCodigoAutorizacao(dadosSQL.getString("codigoautorizacao"));
        obj.setSenhaAcesso(dadosSQL.getString("senhaacesso"));
        obj.setPropsText(dadosSQL.getString("props"));
        obj.setFotoKey(dadosSQL.getString("fotoKey"));
        obj.setAssinaturaBiometriaFacial(dadosSQL.getString("assinaturaBiometriaFacial"));
        obj.setAssinaturaBiometriaDigital(dadosSQL.getString("assinaturaBiometriaDigital"));
        obj.setCpf(dadosSQL.getString("cpf"));
        obj.setNovoObj(false);

        if (Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA == nivelMontarDados) {
            return obj;
        }

        IntegracaoAcessoGrupoEmpresarial integracao = new IntegracaoAcessoGrupoEmpresarial(con);
        obj.setIntegracao(integracao.consultarPorCodigo(obj.getIntegracao().getCodigo(), nivelMontarDados));

        return obj;

    }


    public String validarExistenciaDados(AutorizacaoAcessoGrupoEmpresarialVO autorizacao) throws Exception {
        boolean existeNoBanco = existe("SELECT codigo FROM autorizacaoacessogrupoempresarial "
                + "WHERE codigopessoa = " + autorizacao.getCodigoPessoa()
                + " AND integracaoacessogrupoempresarial = "
                + autorizacao.getIntegracao().getCodigo()
                + " AND codigo <> "+autorizacao.getCodigo(), con);
        if (existeNoBanco) {
            throw new ConsistirException("Esta pessoa já possui uma autorização de acesso cadastrada com essa integração.");
        }
        List<String> campos = new ArrayList<>();
        StringBuilder retorno = new StringBuilder();
        String msgSenha = "";
        if (!UteisValidacao.emptyString(autorizacao.getSenhaAcesso()) ) {
            if(getFacade().getPessoa().senhaAcessoJaUtilizada(0, 0, autorizacao.getSenhaAcesso())){
                msgSenha = "Caso o aluno queira utilizar senha de acesso na catraca nesta unidade, primeiramente clique em 'Gravar' e depois informe uma nova senha.";
                autorizacao.setSenhaAcesso("");
            } else {
                msgSenha = "Senha de acesso na catraca que o aluno utiliza para acessar a unidade de origem poderá ser usada nessa unidade normalmente.";
            }
        }

        if ((existe("SELECT codigo FROM cliente WHERE codacessoalternativo like '" + autorizacao.getCodAcessoAlternativo() + "' ", con)
                || existe("SELECT codigo FROM colaborador WHERE codacessoalternativo like '" + autorizacao.getCodAcessoAlternativo() + "' ", con))
                && !UteisValidacao.emptyString(autorizacao.getCodAcessoAlternativo())) {
            campos.add(" um código de acesso alternativo");
            autorizacao.setCodAcessoAlternativo("");
        }
        autorizacao.setCodAcesso("NU"+autorizacao.getCodAcesso());
        retorno.append(msgSenha);
        if(campos.isEmpty()){
            return retorno.toString();
        }
        retorno.append(" Já existe ");
        for(String string : campos){
            retorno.append(string);
        }
        retorno.append(" no banco de dados igual a deste ");
        retorno.append(autorizacao.getTipoPessoa().equals(TipoPessoaEnum.ALUNO.getTipo()) ? "cliente, " : "colaborador, ");
        retorno.append(" portanto não será possível utilizar como token de entrada estas informações nesta autorização");
        return retorno.toString();
    }

    @Override
    public void excluir(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception {
        executarConsulta("DELETE FROM alunohorarioturma WHERE autorizado = " + obj.getCodigo(), con);
        executarConsulta("DELETE FROM autorizacaoacessogrupoempresarial WHERE codigo = "+obj.getCodigo(), con);
    }

    public List<ClienteVO> consultarPorNomeMock(String nome) throws Exception {
        List<ClienteVO> lista = new ArrayList<>();
        String sql = "SELECT * FROM autorizacaoacessogrupoempresarial WHERE tipopessoa like 'CL' AND nomepessoa like '" + nome.toUpperCase() + "%'";
        ResultSet consulta = criarConsulta(sql, con);
        while (consulta.next()) {
            AutorizacaoAcessoGrupoEmpresarialVO autorizacao = montarDados(consulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            ClienteVO cliente = autorizacao.toClienteVO();
            lista.add(cliente);
        }
        return lista;
    }

    public List<ClienteVO> consultarPorCpfMock(String cpf) throws Exception{
        List<ClienteVO> lista = new ArrayList<>();
        String sql = "SELECT * FROM autorizacaoacessogrupoempresarial WHERE tipopessoa like 'CL' AND cpf like '"+cpf+"%'";
        ResultSet consulta = criarConsulta(sql, con);
        while(consulta.next()){
            AutorizacaoAcessoGrupoEmpresarialVO autorizacao = montarDados(consulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            ClienteVO cliente = new ClienteVO();
            cliente.getPessoa().setNome(autorizacao.getNomePessoa());
            cliente.setCodAcesso(autorizacao.getCodigoAutorizacao());
            cliente.setCodAcessoAlternativo(autorizacao.getCodAcessoAlternativo());
            cliente.getPessoa().setSenhaAcesso(autorizacao.getSenhaAcesso());

            List telefones = (List) autorizacao.getProps().get("telefone");
            if (telefones == null) {
                telefones = new ArrayList();
            }
            cliente.getPessoa().setTelefoneVOs(telefones);

            cliente.getPessoa().setEmailVOs((List) autorizacao.getProps().get("email"));
            cliente.getPessoa().setDataNasc((Date) autorizacao.getProps().get("datanasc"));
            lista.add(cliente);
        }
        return lista;
    }

    public void alterarSenhaAcesso(AutorizacaoAcessoGrupoEmpresarialVO obj,String senhaEncriptada) throws Exception {
        StringBuilder sql = new StringBuilder();
        AutorizacaoAcessoGrupoEmpresarialVO.validarDados(obj);
        sql.append(" UPDATE autorizacaoacessogrupoempresarial \n");
        sql.append("     SET senhaacesso  = ? \n");
        sql.append(" where codigo = ? ");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            int i = 0;
            stm.setString(++i, senhaEncriptada);
            stm.setInt(++i, obj.getCodigo());
            stm.execute();
            obj.setSenhaAcesso(senhaEncriptada);
        }
    }

    public Integer consultarCriando(Integer matricula, String chaveAluno) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select a.codigo from autorizacaoacessogrupoempresarial a \n");
        sql.append(" inner join integracaoacessogrupoempresarial i on i.codigo = a.integracaoacessogrupoempresarial \n");
        sql.append(" where a.codigomatricula = ").append(matricula).append(" and i.chave = '");
        sql.append(chaveAluno);
        sql.append("'");
        ResultSet rs = criarConsulta(sql.toString(), con);
        if(rs.next()){
            return rs.getInt(COL_CODIGO);
        }

        ResultSet rsInt = criarConsulta("select * from integracaoacessogrupoempresarial where chave = '" +
                chaveAluno + "'", con);
        if(rsInt.next()){
            String urlzw = rsInt.getString("urlzillyonweb");
            List<ClienteVO> listaClientes = IntegracaoCadastrosWSConsumer.getListaClientes(urlzw, chaveAluno,
                    rsInt.getInt("empresaremota"), matricula.toString());
            if(listaClientes == null || listaClientes.isEmpty()){
                throw new Exception("Não foi possível criar a integração para esse aluno. Entre em contato com a unidade.");
            }
            ClienteVO cliente = listaClientes.get(0);
            AutorizacaoAcessoGrupoEmpresarialVO autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO();
            autorizacao.setCodAcesso(cliente.getCodAcesso());
            autorizacao.setCodAcessoAlternativo(cliente.getCodAcessoAlternativo());
            autorizacao.setNomePessoa(cliente.getPessoa().getNome());
            autorizacao.setCodigoGenerico(cliente.getCodigo());
            autorizacao.setCodigoMatricula(cliente.getCodigoMatricula());
            autorizacao.setSenhaAcesso(cliente.getPessoa().getSenhaAcesso());
            autorizacao.setCodigoPessoa(cliente.getPessoa().getCodigo());
            autorizacao.setFotoKey(cliente.getPessoa().getFotoKey());
            autorizacao.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            autorizacao.getEmpresaRemota().setCodigo(rsInt.getInt("empresaremota"));
            autorizacao.getEmpresaLocal().setCodigo(rsInt.getInt("empresalocal"));
            autorizacao.getIntegracao().setCodigo(rsInt.getInt(COL_CODIGO));
            Usuario usuarioDAO = new Usuario(con);
            autorizacao.setUsuarioResponsavel(usuarioDAO.getUsuarioRecorrencia());
            incluir(autorizacao);
            return autorizacao.getCodigo();
        } else {
            throw new Exception("Não existe integração para a chave de origem do aluno. Entre em contato com a unidade.");
        }

    }

    public JSONArray consultarParaAula(Integer empresa, String parametro) throws Exception{
        StringBuilder sql = new StringBuilder(" select codigo, nomepessoa, codacesso from autorizacaoacessogrupoempresarial a\n");
        sql.append(" where tipopessoa = 'CL' and empresalocal = ").append(empresa);
        try {
            Integer matricula = Integer.valueOf(parametro);
            sql.append(" and codigomatricula = ").append(matricula);
        } catch (NumberFormatException ne) {
            sql.append(" and upper(nomepessoa) like '").append(parametro.toUpperCase()).append("%'");
        }
        sql.append(" limit 30 ");
        ResultSet resultSet = criarConsulta(sql.toString(), con);
        JSONArray autorizados = new JSONArray();
        while(resultSet.next()){
            JSONObject json = new JSONObject();
            json.put("id", resultSet.getInt(COL_CODIGO));
            json.put("nome", resultSet.getString(COL_NOME_PESSOA));
            json.put("codAcesso", resultSet.getString(COL_COD_ACESSO));
            autorizados.put(json);
        }
        return autorizados;
    }

    public AutorizacaoAcessoGrupoEmpresarialVO consultarSimples(Integer codigo) throws Exception{
        AutorizacaoAcessoGrupoEmpresarialVO autorizado = new AutorizacaoAcessoGrupoEmpresarialVO();
        StringBuilder sql = new StringBuilder(" select i.nomeempresa,a.codigo, nomepessoa, codigomatricula  from autorizacaoacessogrupoempresarial a\n");
        sql.append(" inner join integracaoacessogrupoempresarial i on i.codigo = a.integracaoacessogrupoempresarial  ");
        sql.append(" where a.codigo = ").append(codigo);
        ResultSet resultSet = criarConsulta(sql.toString(), con);
        while(resultSet.next()){
            autorizado.setCodigo(resultSet.getInt(COL_CODIGO));
            autorizado.setNomePessoa(resultSet.getString(COL_NOME_PESSOA));
            autorizado.setCodigoMatricula(resultSet.getInt(COL_CODIGO_MATRICULA));
            autorizado.setIntegracao(new IntegracaoAcessoGrupoEmpresarialVO());
            autorizado.getIntegracao().setEmpresaRemota(new EmpresaVO());
            autorizado.getIntegracao().getEmpresaRemota().setNome(resultSet.getString("nomeempresa"));
        }
        return autorizado;
    }

    public void excluirAulaAutorizado(Integer matricula, String chaveAluno, Date dia, Integer codigoHorarioTurma) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select a.codigo from alunohorarioturma a \n");
        sql.append(" inner join autorizacaoacessogrupoempresarial aa on aa.codigo = a.autorizado \n");
        sql.append(" inner join integracaoacessogrupoempresarial i on i.codigo = aa.integracaoacessogrupoempresarial \n");
        sql.append(" where aa.codigomatricula = ").append(matricula);
        sql.append(" and i.chave = '").append(chaveAluno).append("' \n");
        sql.append(" and a.horarioturma = ").append(codigoHorarioTurma);
        sql.append(" and a.dia::date = '");
        sql.append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("'");
        ResultSet resultSet = criarConsulta(sql.toString(), con);
        if(resultSet.next()){
            executarConsulta("delete from alunohorarioturma where codigo = " + resultSet.getInt(COL_CODIGO), con);
        }
    }

    public void excluirAulaAutorizadoGestaoRede(Integer matriculaAutorizado, String codAcessoAutorizado, Date dia, Integer codigoHorarioTurma) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select a.codigo from alunohorarioturma a \n");
        sql.append(" where a.matriculaautorizado = ").append(matriculaAutorizado);
        sql.append(" and a.codAcessoAutorizado = '").append(codAcessoAutorizado).append("' \n");
        sql.append(" and a.horarioturma = ").append(codigoHorarioTurma);
        sql.append(" and a.dia::date = '");
        sql.append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("'");
        ResultSet resultSet = criarConsulta(sql.toString(), con);
        if(resultSet.next()){
            executarConsulta("delete from alunohorarioturma where codigo = " + resultSet.getInt(COL_CODIGO), con);
        }
    }

    public JSONArray aulasAutorizado(Integer matricula, String chaveAluno, Date dia) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select a.horarioturma, a.dia from alunohorarioturma a \n");
        sql.append(" inner join autorizacaoacessogrupoempresarial aa on aa.codigo = a.autorizado \n");
        sql.append(" inner join integracaoacessogrupoempresarial i on i.codigo = aa.integracaoacessogrupoempresarial \n");
        sql.append(" where aa.codigomatricula = ").append(matricula);
        sql.append(" and i.chave = '").append(chaveAluno).append("' \n");
        sql.append(" and a.dia::date = '");
        sql.append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("'");
        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            JSONArray aulas = new JSONArray();
            while (resultSet.next()) {
                aulas.put(resultSet.getInt("horarioturma") + "_" + Uteis.getData(resultSet.getDate("dia")));
            }
            return aulas;
        }
    }

    public AutorizacaoAcessoGrupoEmpresarialVO consultarPorCpfOuTelefone(String text) throws Exception {
        String unmaskedText = Uteis.removerMascara(text);

        if (unmaskedText.length() != 11) {
            return null;
        }

        String cpfParte1 = unmaskedText.substring(0, 3);
        String cpfParte2 = unmaskedText.substring(3, 6);
        String cpfParte3 = unmaskedText.substring(6, 9);
        String cpfParte4 = unmaskedText.substring(9);

        String dddTel = unmaskedText.substring(0, 2);
        String numeroTel = unmaskedText.substring(2);

        String sql = "SELECT * FROM autorizacaoacessogrupoempresarial aut\n" +
                "WHERE (cpf like '%" + cpfParte1 + "%'\n" +
                " and cpf like '%" + cpfParte2 + "%'\n" +
                " and cpf like '%" + cpfParte3 + "%'\n" +
                " and cpf like '%" + cpfParte4 + "%')\n" +
                " or (props like '%" + dddTel + "%'\n" +
                " and props like '%" + numeroTel + "%')\n";

        try (PreparedStatement ps = con.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()) {
            if(rs.next()) {
                return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }
            return null;
        }
    }

    public void atualizarCamposEspecificos(AutorizacaoAcessoGrupoEmpresarialVO aut) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE autorizacaoacessogrupoempresarial\n");
        sql.append("SET cpf = ?, assinaturabiometriadigital = ?, codacesso = ?\n");
        sql.append("where codigo = ? ");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            int i = 0;
            stm.setString(++i, aut.getCpf());
            stm.setString(++i, aut.getAssinaturaBiometriaDigital());
            stm.setString(++i, aut.getCodAcesso());
            stm.setInt(++i, aut.getCodigo());
            stm.execute();
        }
    }

    public Integer statusFotoValida(Integer codAutorizacao) throws Exception {
        StringBuilder sql = new StringBuilder(" select statusSincronizacaoFotoPessoa from autorizacaoacessogrupoempresarial a\n");
        sql.append(" where codigo = ").append(codAutorizacao);
        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            if (resultSet.next()) {
                return resultSet.getInt("statusSincronizacaoFotoPessoa");
            }
            return 0;
        }
    }
}

