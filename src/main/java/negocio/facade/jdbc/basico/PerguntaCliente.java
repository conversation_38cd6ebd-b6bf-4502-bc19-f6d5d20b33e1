package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.util.Hashtable;

import negocio.comuns.basico.RespostaPergClienteVO;
import negocio.interfaces.basico.*;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;

import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PerguntaClienteVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PerguntaClienteVO</code>.
 * Encapsula toda a interação com o banco de dados.
 *
 * @see PerguntaClienteVO
 * @see SuperEntidade
 */
public class PerguntaCliente extends SuperEntidade implements PerguntaClienteInterfaceFacade {

    private Hashtable respostaPergClientes;

    public PerguntaCliente() throws Exception {
        super();
        setIdEntidade("Cliente");
        setRespostaPergClientes(new Hashtable());
    }

    public PerguntaCliente(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Cliente");
        setRespostaPergClientes(new Hashtable());
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>PerguntaClienteVO</code>.
     */
    public PerguntaClienteVO novo() throws Exception {
        incluir(getIdEntidade());
        PerguntaClienteVO obj = new PerguntaClienteVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PerguntaClienteVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PerguntaClienteVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PerguntaClienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(PerguntaClienteVO obj) throws Exception {
        PerguntaClienteVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO PerguntaCliente( descricao, tipoPergunta, multipla, simples, textual ) VALUES ( ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getDescricao());
        sqlInserir.setString(2, obj.getTipoPergunta());
        sqlInserir.setBoolean(3, obj.getMultipla());
        sqlInserir.setBoolean(4, obj.getSimples());
        sqlInserir.setBoolean(5, obj.getTextual());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        getFacade().getRespostaPergCliente().incluirRespostaPergClientes(obj.getCodigo(), obj.getRespostaPergClienteVOs(), true, false);
        obj.setNovoObj(new Boolean(false));
    }

    public void incluirPerguntaCliente(PerguntaClienteVO obj, Boolean validarQuestionario, boolean controlarAcesso) throws Exception {
        try {
            if (validarQuestionario) {
                PerguntaClienteVO.validarDados(obj);
            }
            if (controlarAcesso) {
                incluir(getIdEntidade());
            }
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO PerguntaCliente( descricao, tipoPergunta, multipla, simples, textual ) VALUES ( ?, ?, ?, ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getDescricao());
            sqlInserir.setString(2, obj.getTipoPergunta());
            sqlInserir.setBoolean(3, obj.getMultipla());
            sqlInserir.setBoolean(4, obj.getSimples());
            sqlInserir.setBoolean(5, obj.getTextual());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());

            if (validarQuestionario && (obj.getSimples() || obj.getMultipla())) {
                new RespostaPergClienteVO().validarDadosRepostasSimples(obj.getCodigo(), obj.getRespostaPergClienteVOs());
            }
            new RespostaPergCliente(con).incluirRespostaPergClientes(obj.getCodigo(), obj.getRespostaPergClienteVOs(), validarQuestionario, controlarAcesso);
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PerguntaClienteVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PerguntaClienteVO</code> que será alterada no banco de dados.
     * @throws Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PerguntaClienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(PerguntaClienteVO obj) throws Exception {
        PerguntaClienteVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE PerguntaCliente set descricao=?, tipoPergunta=?, multipla=?, simples=?, textual=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, obj.getDescricao());
        sqlAlterar.setString(2, obj.getTipoPergunta());
        sqlAlterar.setBoolean(3, obj.getMultipla());
        sqlAlterar.setBoolean(4, obj.getSimples());
        sqlAlterar.setBoolean(5, obj.getTextual());
        sqlAlterar.setInt(6, obj.getCodigo().intValue());
        sqlAlterar.execute();
        getFacade().getRespostaPergCliente().alterarRespostaPergClientes(obj.getCodigo(), obj.getRespostaPergClienteVOs(), true);
    }

    public void alterarPeguntaCliente(PerguntaClienteVO obj, Boolean validarQuestionario) throws Exception {
        if (validarQuestionario) {
            PerguntaClienteVO.validarDados(obj);
        }
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE PerguntaCliente set descricao=?, tipoPergunta=?, multipla=?, simples=?, textual=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, obj.getDescricao());
        sqlAlterar.setString(2, obj.getTipoPergunta());
        sqlAlterar.setBoolean(3, obj.getMultipla());
        sqlAlterar.setBoolean(4, obj.getSimples());
        sqlAlterar.setBoolean(5, obj.getTextual());
        sqlAlterar.setInt(6, obj.getCodigo().intValue());
        sqlAlterar.execute();
        if (validarQuestionario && (obj.getSimples() || obj.getMultipla())) {
            new RespostaPergClienteVO().validarDadosRepostasSimples(obj.getCodigo(), obj.getRespostaPergClienteVOs());
        }
        getFacade().getRespostaPergCliente().alterarRespostaPergClientes(obj.getCodigo(), obj.getRespostaPergClienteVOs(), validarQuestionario);

    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PerguntaClienteVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>PerguntaClienteVO</code> que será removido no banco de dados.
     * @throws Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PerguntaClienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM PerguntaCliente WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            getFacade().getRespostaPergCliente().excluirRespostaPergClientes(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirPerguntaCliente(PerguntaClienteVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PerguntaCliente WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
        getFacade().getRespostaPergCliente().excluirRespostaPergClientes(obj.getCodigo());

    }

    /**
     * Responsável por realizar uma consulta de <code>PerguntaCliente</code> através do valor do atributo
     * <code>String tipoPergunta</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PerguntaClienteVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTipoPergunta(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PerguntaCliente WHERE upper( tipoPergunta ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoPergunta";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, con, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>PerguntaCliente</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PerguntaClienteVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PerguntaCliente WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, con, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>PerguntaCliente</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>PerguntaClienteVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PerguntaCliente WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, con, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>PerguntaClienteVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, Connection con, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PerguntaClienteVO obj = new PerguntaClienteVO();
            obj = montarDados(tabelaResultado, con, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PerguntaClienteVO</code>.
     *
     * @return O objeto da classe <code>PerguntaClienteVO</code> com os dados devidamente montados.
     */
    public static PerguntaClienteVO montarDados(ResultSet dadosSQL, Connection con, int nivelMontarDados) throws Exception {
        PerguntaClienteVO obj = new PerguntaClienteVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setTipoPergunta(dadosSQL.getString("tipoPergunta"));
        obj.setMultipla(new Boolean(dadosSQL.getBoolean("multipla")));
        obj.setSimples(new Boolean(dadosSQL.getBoolean("simples")));
        obj.setTextual(new Boolean(dadosSQL.getBoolean("textual")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        RespostaPergCliente respostaPergClienteDAO = new RespostaPergCliente(con);
        obj.setRespostaPergClienteVOs(respostaPergClienteDAO.consultarRespostaPergClientes(obj.getCodigo(), nivelMontarDados));
        respostaPergClienteDAO = null;
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por adicionar um objeto da <code>RespostaPergClienteVO</code> no Hashtable <code>RespostaPergClientes</code>.
     * Neste Hashtable são mantidos todos os objetos de RespostaPergCliente de uma determinada PerguntaCliente.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjRespostaPergClientes(RespostaPergClienteVO obj) throws Exception {
        getRespostaPergClientes().put(obj.getDescricaoRespota() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>RespostaPergClienteVO</code> do Hashtable <code>RespostaPergClientes</code>.
     * Neste Hashtable são mantidos todos os objetos de RespostaPergCliente de uma determinada PerguntaCliente.
     *
     * @param DescricaoRespota Atributo da classe <code>RespostaPergClienteVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjRespostaPergClientes(String DescricaoRespota) throws Exception {
        getRespostaPergClientes().remove(DescricaoRespota + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PerguntaClienteVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PerguntaClienteVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PerguntaCliente WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PerguntaCliente ).");
        }
        return (montarDados(tabelaResultado, con, nivelMontarDados));
    }


    public Hashtable getRespostaPergClientes() {
        return (respostaPergClientes);
    }

    public void setRespostaPergClientes(Hashtable respostaPergClientes) {
        this.respostaPergClientes = respostaPergClientes;
    }
}
