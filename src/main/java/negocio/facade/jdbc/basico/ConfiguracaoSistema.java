package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.enumerador.FiltrosEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.acesso.Coletor;
import negocio.facade.jdbc.acesso.LocalAcesso;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.basico.ConfiguracaoSistemaInterfaceFacade;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ConfiguracaoSistemaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ConfiguracaoSistemaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 *
 * @see ConfiguracaoSistemaVO
 * @see SuperEntidade
 */
public class ConfiguracaoSistema extends SuperEntidade implements ConfiguracaoSistemaInterfaceFacade {

    private static final String sqlUpdate = "UPDATE ConfiguracaoSistema SET questionarioPrimeiraVisita=?, questionarioRetorno=?, questionarioReMatricula=?, "
            + "juroParcela=?, multa=?, nrDiasVigenteQuestionarioVisita=?, nrDiasVigenteQuestionarioRetorno=?,"
            + "nrDiasVigenteQuestionarioRematricula=?, mascaraMatricula=?, carenciaRenovacao=?, nrDiasAvencer=?, "
            + "toleranciapagamento=?, qtdFaltaPeso1=?,  qtdFaltaInicioPeso2=?, qtdFaltaTerminoPeso2=?, qtdFaltaPeso3=?, datainiciodesconsideraracessorisco = ?, datafimdesconsideraracessorisco = ?, "
            + "carencia=?, cpfValidar=?, nrDiasProrata=?, urlGoogleAgenda=?, vencimentoColaborador=?, aliquotaservico=?, "
            + "aliquotaproduto=?, usaecf=?, rodarsqlsbancoinicial=?, alteracaodatabasecontrato=?, toleranciaDiasContratoVencido = ?, "
            + " urlrecorrencia = ? , qtdDiasExpirarSenha =?, qtdDiasEstornoAutomaticoContrato=?, acessoChamada=?, localAcessoChamada=?, coletorChamada=?,"
            + " dataUltimaRepescagem=?,bloquearacessoseparcelaaberta=?, "
            + " enviarSMSAutomatico = ?, nomedatanascvalidar = ?, enviarRemessasRemotamente = ?, validarcontatometa = ?, ecfporpagamento = ?,"
            + "forcarCodigoAlternativoAcesso = ?, itemVendaAvulsaAutomatico = ?, questionarioPrimeiraCompra = ?, nrDiasVigenteQuestionarioPrimeiraCompra = ?,"
            + "questionarioRetornoCompra = ?, nrDiasVigenteQuestionarioRetornoCompra = ?, numerocielo = ?, validarcpfduplicado = ?, "
            + "marcarpresencapeloacesso = ?, usarnomeresponsavelnota = ?, qtddiaprimeiraparcelavencidaestornarcontrato=?, qtddiaprimeiraparcelavencidaestornarcontrato_origemvendasonline=?,"
            + "defaultEnderecoCorrespondecia = ?, utilizarSistemaParaClube= ?, imprimirReciboPagtoMatricial= ?, ecfApenasPlano = ?,"
            + "habilitarGestaoArmarios = ?,diaProrataArmario=?,nomenclaturavendacredito=?, sequencialItem = ?, sesc = ?, sesiCe = ?, "
            + "controleAcessoMultiplasEmpresasPorPlano = ?, priorizarVendaRapida = ?, barrarDevedorVendaRapida = ?, lancamentoContratosIguais = ?, usaAprovaFacil = ?, "
            + "cancelarContratoNaUnidadeOrigemAoTransferirAluno = ?, usarDigitalComoAssinatura =  ?, utilizarTipoPlano = ?, transferirAutorizacaoCobranca = ?, "
            + "sequencialarquivo = ?, nomeArquivoRemessaPadraoTivit = ?, definirDataInicioPlanosRecorrencia = ?, usarSistemaInternacional= ?, validarCPFResponsaveis = ?, "
            + "exibirmodalusuariosinativos = ?, usarVerificadorRemessasRejeitadas = ?, agruparRemessasGetnet = ?, apresentarMarketPlace = ?, agruparRemessasCartaoEDI = ?, "
            + "permitirreplicarplanoredeempresa = ?, propagaraAssinaturaDigital = ?, forcarUtilizacaoPlanoAntigo=?, mascaraTelefone=?, utilizarFormatoMMDDYYYDtNascimento= ?, "
            + "utilizarServicoSesiSC = ?, lumi = ?, assinaturacontratoviaemail = ?, termoresponsabilidade = ?,"
            + "qtdacessogympassiniciofaixa1=?, qtdacessogympassiniciofaixa2=?, qtdacessogympassiniciofaixa3=?, qtdacessogympassiniciofaixa4=?, qtdacessogympassfinalfaixa1=?, qtdacessogympassfinalfaixa2=?, qtdacessogympassfinalfaixa3=?, qtdacessogympassfinalfaixa4=?, "
            + "realizarenviosesisc = ?, permiteTrocaEmpresaMultiChave = ?, permiteLancarFeriasPlanoRecorrente=?, permitirMudarTipoParcelamentoVendaRapida = ?,\n"
            + "permitirreplicarferiadoredeempresa = ?, usaPlanoRecorrenteCompartilhado = ?, permitirReplicarPerfilAcessoRedeEmpresa = ?, permitirReplicarUsuarioRedeEmpresa = ?, permitirReplicarFornecedorRedeEmpresa = ?, permiteImpressaoContratoMutavel= ?,\n"
            + "permitirReplicarModeloContratoRedeEmpresa = ?, conciliarsemnumeroparcela = ?, manterContratoAssinadoNaRenovacaoContrato = ?, termoresponsabilidadeExaluno = ?, loginatravesazuread = ?, azureadtenatid = ?, azureadclientid = ?, apiSescGo = ?, usuarioApiSescGo = ?, senhaApiSescGo = ?,\n"
            + "exibirmodalplanosinativos = ?, permiteEstornarContrato30MinAposLancamento = ?, idademinima= ? "
            + " WHERE codigo = ?";
    private static final String sqlUpdateEmpresa = "UPDATE empresa SET questionarioPrimeiraVisita=?, questionarioRetorno=?, "
            + "questionarioReMatricula=?, nrDiasVigenteQuestionarioVisita=?, nrDiasVigenteQuestionarioRetorno=?, "
            + "nrDiasVigenteQuestionarioRematricula=?, mascaraMatricula=?, carenciaRenovacao=?, nrDiasAvencer=?, "
            + "toleranciapagamento=?, qtdFaltaPeso1=?,  qtdFaltaInicioPeso2=?, qtdFaltaTerminoPeso2=?, qtdFaltaPeso3=?, "
            + "carencia=?, nrDiasProrata=?, toleranciaDiasContratoVencido = ?, urlrecorrencia = ?, bloquearacessoseparcelaaberta=?,"
            + "questionarioPrimeiraCompra = ?, nrDiasVigenteQuestionarioPrimeiraCompra = ?, questionarioRetornoCompra = ?, "
            + "nrDiasVigenteQuestionarioRetornoCompra = ?, usarDigitalComoAssinatura =  ? WHERE codigo = ?";
    private static final String sqlInsert = "INSERT INTO ConfiguracaoSistema(questionarioPrimeiraVisita, questionarioRetorno, questionarioReMatricula, "
            + "juroParcela, multa, nrDiasVigenteQuestionarioVisita, nrDiasVigenteQuestionarioRetorno,"
            + "nrDiasVigenteQuestionarioRematricula, mascaraMatricula, carenciaRenovacao, nrDiasAvencer, "
            + "toleranciapagamento, qtdFaltaPeso1,  qtdFaltaInicioPeso2, qtdFaltaTerminoPeso2, qtdFaltaPeso3, datainiciodesconsideraracessorisco, datafimdesconsideraracessorisco, "
            + "carencia, cpfValidar, nrDiasProrata, urlGoogleAgenda, vencimentoColaborador, aliquotaservico, "
            + "aliquotaproduto, usaecf, rodarsqlsbancoinicial, alteracaodatabasecontrato, toleranciaDiasContratoVencido, "
            + " urlrecorrencia, qtdDiasExpirarSenha, qtdDiasEstornoAutomaticoContrato,  acessoChamada, localAcessoChamada, "
            + "coletorChamada,dataUltimaRepescagem,bloquearacessoseparcelaaberta,enviarSMSAutomatico, nomedatanascvalidar, "
            + " enviarRemessasRemotamente, validarcontatometa, ecfporpagamento, forcarCodigoAlternativoAcesso, itemVendaAvulsaAutomatico, "
            + " questionarioPrimeiraCompra, nrDiasVigenteQuestionarioPrimeiraCompra, questionarioRetornoCompra, nrDiasVigenteQuestionarioRetornoCompra, numerocielo, validarcpfduplicado, "
            + "marcarpresencapeloacesso, usarnomeresponsavelnota,qtddiaprimeiraparcelavencidaestornarcontrato, qtddiaprimeiraparcelavencidaestornarcontrato_origemvendasonline, defaultEnderecoCorrespondecia, "
            + "utilizarSistemaParaClube, imprimirReciboPagtoMatricial, ecfApenasPlano,habilitarGestaoArmarios,diaProrataArmario,nomenclaturavendacredito, sequencialItem, sesc, sesiCe, usaAprovaFacil, "
            + "cancelarContratoNaUnidadeOrigemAoTransferirAluno, usarDigitalComoAssinatura, utilizarTipoPlano, transferirAutorizacaoCobranca, sequencialarquivo, nomeArquivoRemessaPadraoTivit, "
            + "definirDataInicioPlanosRecorrencia, usarSistemaInternacional, validarCPFResponsaveis,exibirmodalusuariosinativos, usarVerificadorRemessasRejeitadas, agruparRemessasGetnet, "
            + "apresentarMarketPlace, agruparRemessasCartaoEDI, permitirreplicarplanoredeempresa, propagaraAssinaturaDigital, termoresponsabilidade, forcarUtilizacaoPlanoAntigo, mascaraTelefone, "
            + "utilizarFormatoMMDDYYYDtNascimento, utilizarServicoSesiSC, lumi, assinaturacontratoviaemail, permiteTrocaEmpresaMultiChave, permiteLancarFeriasPlanoRecorrente,\n"
            + "permitirMudarTipoParcelamentoVendaRapida, permitirreplicarferiadoredeempresa, usaPlanoRecorrenteCompartilhado, permitirReplicarPerfilAcessoRedeEmpresa, permitirReplicarUsuarioRedeEmpresa, permitirReplicarFornecedorRedeEmpresa, permiteImpressaoContratoMutavel,"
            +  "permitirReplicarModeloContratoRedeEmpresa, conciliarsemnumeroparcela, manterContratoAssinadoNaRenovacaoContrato, termoresponsabilidadeExaluno, loginatravesazuread, azureadtenatid, azureadclientid, apiSescGo, usuarioApiSescGo, senhaApiSescGo,  exibirmodalplanosinativos, "
            + "permiteEstornarContrato30MinAposLancamento,idademinima)"
            + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,"
            + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    public ConfiguracaoSistema() throws Exception {
        super();
    }

    public ConfiguracaoSistema(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>ConfiguracaoSistemaVO</code> resultantes da consulta.
     */
    public static List<ConfiguracaoSistemaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ConfiguracaoSistemaVO> vetResultado = new ArrayList<ConfiguracaoSistemaVO>();
        while (tabelaResultado.next()) {
            ConfiguracaoSistemaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static ConfiguracaoSistemaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        ConfiguracaoSistemaVO obj = new ConfiguracaoSistemaVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNrDiasVigenteQuestionarioVista(dadosSQL.getInt("nrDiasVigenteQuestionarioVisita"));
        obj.setNrDiasVigenteQuestionarioRetorno(dadosSQL.getInt("nrDiasVigenteQuestionarioRetorno"));
        obj.setNrDiasVigenteQuestionarioRematricula(dadosSQL.getInt("nrDiasVigenteQuestionarioRematricula"));
        obj.getQuestionarioPrimeiraVisita().setCodigo(dadosSQL.getInt("questionarioPrimeiraVisita"));
        obj.getQuestionarioRetorno().setCodigo(dadosSQL.getInt("questionarioRetorno"));
        obj.getQuestionarioReMatricula().setCodigo(dadosSQL.getInt("questionarioReMatricula"));
        obj.setJuroParcela(dadosSQL.getDouble("juroParcela"));
        obj.setMulta(dadosSQL.getDouble("multa"));
        obj.setMascaraMatricula(dadosSQL.getString("mascaraMatricula"));
        obj.setCarenciaRenovacao(dadosSQL.getInt("carenciaRenovacao"));
        obj.setNrDiasAvencer(dadosSQL.getInt("nrDiasAvencer"));
        obj.setToleranciaPagamento(dadosSQL.getInt("toleranciaPagamento"));
        obj.setQtdFaltaPeso1(dadosSQL.getInt("qtdFaltaPeso1"));
        obj.setQtdFaltaInicioPeso2(dadosSQL.getInt("qtdFaltaInicioPeso2"));
        obj.setQtdFaltaTerminoPeso2(dadosSQL.getInt("qtdFaltaTerminoPeso2"));
        obj.setQtdFaltaPeso3(dadosSQL.getInt("qtdFaltaPeso3"));
        obj.setCarencia(dadosSQL.getInt("carencia"));
        obj.setCpfValidar(dadosSQL.getBoolean("cpfValidar"));
        obj.setNrDiasProrata(dadosSQL.getInt("nrDiasProrata"));
        obj.setUrlGoogleAgenda(dadosSQL.getString("urlGoogleAgenda"));
        obj.setRodarSqlsBancoInicial(dadosSQL.getBoolean("rodarSqlsBancoInicial"));
        obj.setVencimentoColaborador(dadosSQL.getInt("vencimentoColaborador"));
        obj.setAliquotaServico(dadosSQL.getDouble("aliquotaservico"));
        obj.setAliquotaProduto(dadosSQL.getDouble("aliquotaproduto"));
        obj.setUsaEcf(dadosSQL.getBoolean("usaecf"));
        obj.setAlteracaoDataBaseContrato(dadosSQL.getBoolean("alteracaoDataBaseContrato"));
        obj.setToleranciaDiasContratoVencido(dadosSQL.getInt("toleranciaDiasContratoVencido"));
        obj.setUrlRecorrencia(dadosSQL.getString("urlrecorrencia"));
        obj.setQtdDiasExpirarSenha(dadosSQL.getInt("qtdDiasExpirarSenha"));
        try {//ignorar este campo quando ainda não está criado no banco de dados
            obj.setSequencialArquivo(dadosSQL.getInt("sequencialarquivo"));
            obj.setNrDiasVigenteQuestionarioPrimeiraCompra(dadosSQL.getInt("nrDiasVigenteQuestionarioPrimeiraCompra"));
            obj.setNrDiasVigenteQuestionarioRetornoCompra(dadosSQL.getInt("nrDiasVigenteQuestionarioRetornoCompra"));
            obj.setControleAcessoMultiplasEmpresasPorPlano(dadosSQL.getBoolean("controleAcessoMultiplasEmpresasPorPlano"));
            obj.getQuestionarioPrimeiraCompra().setCodigo(dadosSQL.getInt("questionarioPrimeiraCompra"));
            obj.getQuestionarioRetornoCompra().setCodigo(dadosSQL.getInt("questionarioRetornoCompra"));
            obj.setAcessoChamada(dadosSQL.getBoolean("acessoChamada"));
            obj.getLocalAcessoChamada().setCodigo(dadosSQL.getInt("localAcessoChamada"));
            obj.getColetorChamada().setCodigo(dadosSQL.getInt("coletorChamada"));
            obj.setQtdDiasEstornoAutomaticoContrato(dadosSQL.getInt("qtdDiasEstornoAutomaticoContrato"));
            obj.setQtdDiaPrimeiraParcelaVencidaEstornarContrato(dadosSQL.getInt("qtddiaprimeiraparcelavencidaestornarcontrato"));
            obj.setQtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline(dadosSQL.getInt("qtddiaprimeiraparcelavencidaestornarcontrato_origemvendasonline"));
            obj.setDataUltimaRepescagem(dadosSQL.getTimestamp("dataUltimaRepescagem"));
            obj.setBloquearAcessoSeParcelaAberta(dadosSQL.getBoolean("bloquearAcessoSeParcelaAberta"));
            obj.setEnviarSMSAutomatico(dadosSQL.getBoolean("enviarSMSAutomatico"));
            obj.setNomeDataNascValidar(dadosSQL.getBoolean("nomedatanascvalidar"));
            obj.setEnviarRemessasRemotamente(dadosSQL.getBoolean("enviarRemessasRemotamente"));
            obj.setValidarContatoMeta(dadosSQL.getBoolean("validarcontatometa"));
            obj.setEcfPorPagamento(dadosSQL.getBoolean("ecfporpagamento"));
            obj.setForcarCodigoAlternativoAcesso(dadosSQL.getBoolean("forcarCodigoAlternativoAcesso"));
            obj.setItemVendaAvulsaAutomatico(dadosSQL.getBoolean("itemVendaAvulsaAutomatico"));
            obj.setNumeroCielo(dadosSQL.getString("numerocielo"));
            obj.setValidarCpfDuplicado(dadosSQL.getBoolean("validarcpfduplicado"));
            obj.setMarcarPresencaPeloAcesso(dadosSQL.getBoolean("marcarpresencapeloacesso"));
            obj.setUsarNomeResponsavelNota(dadosSQL.getBoolean("usarnomeresponsavelnota"));
            obj.setDefaultEnderecoCorrespondecia(dadosSQL.getBoolean("defaultEnderecoCorrespondecia"));
            obj.setUtilizarSistemaParaClube(dadosSQL.getBoolean("utilizarSistemaParaClube"));
            obj.setImprimirReciboPagtoMatricial(dadosSQL.getBoolean("imprimirReciboPagtoMatricial"));
            obj.setEcfApenasPlano(dadosSQL.getBoolean("ecfApenasPlano"));
            obj.setHabilitarGestaoArmarios(dadosSQL.getBoolean("habilitarGestaoArmarios"));
            obj.setDiaProrataArmario(dadosSQL.getInt("diaProrataArmario"));
            obj.setNomenclaturaVendaCredito(dadosSQL.getString("nomenclaturavendacredito"));
            obj.setSequencialItem(dadosSQL.getInt("sequencialItem"));
            obj.setSesc(dadosSQL.getBoolean("sesc"));
            obj.setSesiCe(dadosSQL.getBoolean("sesiCe"));
            obj.setPriorizarVendaRapida(dadosSQL.getBoolean("priorizarvendarapida"));
            obj.setBarrarDevedorVendaRapida(dadosSQL.getBoolean("barrardevedorvendarapida"));
            obj.setLancamentoContratosIguais(dadosSQL.getBoolean("lancamentocontratosiguais"));
            obj.setUsaAprovaFacil(dadosSQL.getBoolean("usaAprovaFacil"));
            obj.setCancelarContratoNaUnidadeOrigemAoTransferirAluno(dadosSQL.getBoolean("cancelarContratoNaUnidadeOrigemAoTransferirAluno"));
            obj.setUsarDigitalComoAssinatura(dadosSQL.getBoolean("usarDigitalComoAssinatura" ));
            obj.setUtilizarTipoPlano(dadosSQL.getBoolean("utilizarTipoPlano"));
            obj.setTransferirAutorizacaoCobranca(dadosSQL.getBoolean("transferirAutorizacaoCobranca"));
            obj.setNomeArquivoRemessaPadraoTivit(dadosSQL.getBoolean("nomeArquivoRemessaPadraoTivit"));
            obj.setDefinirDataInicioPlanosRecorrencia(dadosSQL.getBoolean("definirDataInicioPlanosRecorrencia"));
            obj.setUsarSistemaInternacional(dadosSQL.getBoolean("usarSistemaInternacional"));
            obj.setValidarCPFResponsaveis(dadosSQL.getBoolean("validarCPFResponsaveis"));
            obj.setExibirModalInativarUsuarios(dadosSQL.getBoolean("exibirmodalusuariosinativos"));
            obj.setUsarVerificadorRemessasRejeitadas(dadosSQL.getBoolean("usarVerificadorRemessasRejeitadas"));
            obj.setAgruparRemessasGetnet(dadosSQL.getBoolean("agruparRemessasGetnet"));
            obj.setDataInicioDesconsiderarAcessoRisco(dadosSQL.getDate("datainiciodesconsideraracessorisco"));
            obj.setDataFimDesconsiderarAcessoRisco(dadosSQL.getDate("datafimdesconsideraracessorisco"));
            obj.setApresentarMarketPlace(dadosSQL.getBoolean("apresentarMarketPlace"));
            obj.setAgruparRemessasCartaoEDI(dadosSQL.getBoolean("agruparRemessasCartaoEDI"));
            obj.setChavePublicaSESC(dadosSQL.getString("chavepublicasesc"));
            obj.setChavePrivadaSESC(dadosSQL.getString("chaveprivadasesc"));
            obj.setPermitirReplicarPlanoRedeEmpresa(dadosSQL.getBoolean("permitirreplicarplanoredeempresa"));
            obj.setPropagaraAssinaturaDigital(dadosSQL.getBoolean("propagaraAssinaturaDigital"));
            obj.setTermoResponsabilidade(dadosSQL.getBoolean("termoresponsabilidade"));
            obj.setForcarUtilizacaoPlanoAntigo(dadosSQL.getBoolean("forcarUtilizacaoPlanoAntigo"));
            obj.setMascaraTelefone(dadosSQL.getString("mascaraTelefone"));
            obj.setUtilizarServicoSesiSC(dadosSQL.getBoolean("utilizarServicoSesiSC"));
            obj.setLumi(dadosSQL.getBoolean("lumi"));
            obj.setAssinaturaContratoViaEmail(dadosSQL.getBoolean("assinaturacontratoviaemail"));
            obj.setUtilizarFormatoMMDDYYYDtNascimento(dadosSQL.getBoolean("utilizarFormatoMMDDYYYDtNascimento"));
            obj.setQtdAcessoGymPassInicioFaixa1(dadosSQL.getInt("qtdacessogympassiniciofaixa1"));
            obj.setQtdAcessoGymPassInicioFaixa2(dadosSQL.getInt("qtdacessogympassiniciofaixa2"));
            obj.setQtdAcessoGymPassInicioFaixa3(dadosSQL.getInt("qtdacessogympassiniciofaixa3"));
            obj.setQtdAcessoGymPassInicioFaixa4(dadosSQL.getInt("qtdacessogympassiniciofaixa4"));
            obj.setQtdAcessoGymPassFinalFaixa1(dadosSQL.getInt("qtdacessogympassfinalfaixa1"));
            obj.setQtdAcessoGymPassFinalFaixa2(dadosSQL.getInt("qtdacessogympassfinalfaixa2"));
            obj.setQtdAcessoGymPassFinalFaixa3(dadosSQL.getInt("qtdacessogympassfinalfaixa3"));
            obj.setQtdAcessoGymPassFinalFaixa4(dadosSQL.getInt("qtdacessogympassfinalfaixa4"));
            obj.setRealizarEnvioSesiSC(dadosSQL.getBoolean("realizarenviosesisc"));
            obj.setPermiteTrocaEmpresaMultiChave(dadosSQL.getBoolean("permiteTrocaEmpresaMultiChave"));
            obj.setPermiteLancarFeriasPlanoRecorrente(dadosSQL.getBoolean("permiteLancarFeriasPlanoRecorrente"));
            obj.setPermitirMudarTipoParcelamentoVendaRapida(dadosSQL.getBoolean("permitirMudarTipoParcelamentoVendaRapida"));
            obj.setPermitirReplicarFeriadoRedeEmpresa(dadosSQL.getBoolean("permitirreplicarferiadoredeempresa"));
            obj.setUsaPlanoRecorrenteCompartilhado(dadosSQL.getBoolean("usaPlanoRecorrenteCompartilhado"));
            obj.setPermitirReplicarPerfilAcessoRedeEmpresa(dadosSQL.getBoolean("permitirReplicarPerfilAcessoRedeEmpresa"));
            obj.setPermitirReplicarUsuarioRedeEmpresa(dadosSQL.getBoolean("permitirReplicarUsuarioRedeEmpresa"));
            obj.setPermitirReplicarFornecedorRedeEmpresa(dadosSQL.getBoolean("permitirReplicarFornecedorRedeEmpresa"));
            obj.setPermiteImpressaoContratoMutavel(dadosSQL.getBoolean("permiteImpressaoContratoMutavel"));
            obj.setPermitirReplicarModeloContratoRedeEmpresa(dadosSQL.getBoolean("permitirReplicarModeloContratoRedeEmpresa"));
            obj.setConciliarSemNumeroParcela(dadosSQL.getBoolean("conciliarsemnumeroparcela"));
            obj.setManterContratoAssinadoNaRenovacaoContrato(dadosSQL.getBoolean("manterContratoAssinadoNaRenovacaoContrato"));
            obj.setTermoResponsabilidadeExaluno(dadosSQL.getBoolean("termoresponsabilidadeExaluno"));
            obj.setLoginatravesazuread(dadosSQL.getBoolean("loginatravesazuread"));
            obj.setAzureadtenatid(dadosSQL.getString("azureadtenatid"));
            obj.setAzureadclientid(dadosSQL.getString("azureadclientid"));
            obj.setApiSescGo(dadosSQL.getBoolean("apiSescGo"));
            obj.setUsuarioApiSescGo(dadosSQL.getString("usuarioApiSescGo"));
            obj.setSenhaApiSescGo(dadosSQL.getString("senhaApiSescGo"));
            obj.setExibirModalPlanosInativos(dadosSQL.getBoolean("exibirmodalplanosinativos"));
            obj.setPermiteEstornarContrato30MinAposLancamento(dadosSQL.getBoolean("permiteEstornarContrato30MinAposLancamento"));
            obj.setIdadeMinima(dadosSQL.getInt("idademinima"));
            JSFUtilities.storeOnSession(JSFUtilities.CONF_SISTEMA, obj);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ConfiguracaoSistemaVO</code>.
     *
     * @return O objeto da classe <code>ConfiguracaoSistemaVO</code> com os dados devidamente montados.
     */
    public static ConfiguracaoSistemaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ConfiguracaoSistemaVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }
        //preencher lista de emails da recorrencia
        ConfiguracaoSistema configSistema = new ConfiguracaoSistema(con);
        configSistema.montarEmailsRecorrencia(obj);
        configSistema = null;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ROBO) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            JSFUtilities.storeOnSession(JSFUtilities.CONF_SISTEMA, obj);
            return obj;
        }
        montarDadosColetorChamada(obj, con);
        montarDadosLocalAcessoChamada(obj, con, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        JSFUtilities.storeOnSession(JSFUtilities.CONF_SISTEMA, obj);
        return obj;
    }

    public static void montarDadosLocalAcessoChamada(ConfiguracaoSistemaVO obj, Connection con, int nivelMontarDados) throws Exception {
        if (obj.getLocalAcessoChamada().getCodigo() == 0) {
            obj.setLocalAcessoChamada(new LocalAcessoVO());
            return;
        }
        LocalAcesso localAcessoDAO = new LocalAcesso(con);
        obj.setLocalAcessoChamada(localAcessoDAO.consultarPorCodigo(obj.getLocalAcessoChamada().getCodigo(), nivelMontarDados));
        localAcessoDAO = null;
    }

    public static void montarDadosColetorChamada(ConfiguracaoSistemaVO obj, Connection con) throws Exception {
        if (obj.getColetorChamada().getCodigo() == 0) {
            obj.setColetorChamada(new ColetorVO());
            return;
        }
        Coletor coletorDAO = new Coletor(con);
        obj.setColetorChamada(coletorDAO.consultarPorCodigo(obj.getColetorChamada().getCodigo()));
        coletorDAO = null;
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ConfiguracaoSistemaVO</code>.
     */
    public ConfiguracaoSistemaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ConfiguracaoSistemaVO();
    }

    private PreparedStatement prepararIncluir(ConfiguracaoSistemaVO obj) throws Exception {
        PreparedStatement sqlInserir = con.prepareStatement(sqlInsert);
        int i = 1;
        if (obj.getQuestionarioPrimeiraVisita().getCodigo() != 0) {
            sqlInserir.setInt(i++, obj.getQuestionarioPrimeiraVisita().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (obj.getQuestionarioRetorno().getCodigo() != 0) {
            sqlInserir.setInt(i++, obj.getQuestionarioRetorno().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (obj.getQuestionarioReMatricula().getCodigo() != 0) {
            sqlInserir.setInt(i++, obj.getQuestionarioReMatricula().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        sqlInserir.setDouble(i++, obj.getJuroParcela());
        sqlInserir.setDouble(i++, obj.getMulta());
        sqlInserir.setInt(i++, obj.getNrDiasVigenteQuestionarioVista());
        sqlInserir.setInt(i++, obj.getNrDiasVigenteQuestionarioRetorno());
        sqlInserir.setInt(i++, obj.getNrDiasVigenteQuestionarioRematricula());
        sqlInserir.setString(i++, obj.getMascaraMatricula());
        sqlInserir.setInt(i++, obj.getCarenciaRenovacao());
        sqlInserir.setInt(i++, obj.getNrDiasAvencer());
        sqlInserir.setInt(i++, obj.getToleranciaPagamento());
        sqlInserir.setInt(i++, obj.getQtdFaltaPeso1());
        sqlInserir.setInt(i++, obj.getQtdFaltaInicioPeso2());
        sqlInserir.setInt(i++, obj.getQtdFaltaTerminoPeso2());
        sqlInserir.setInt(i++, obj.getQtdFaltaPeso3());
        sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataInicioDesconsiderarAcessoRisco()));
        sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataFimDesconsiderarAcessoRisco()));
        sqlInserir.setInt(i++, obj.getCarencia());
        sqlInserir.setBoolean(i++, obj.isCpfValidar());
        sqlInserir.setInt(i++, obj.getNrDiasProrata());
        sqlInserir.setString(i++, obj.getUrlGoogleAgenda());
        sqlInserir.setInt(i++, obj.getVencimentoColaborador());
        sqlInserir.setDouble(i++, obj.getAliquotaServico());
        sqlInserir.setDouble(i++, obj.getAliquotaProduto());
        sqlInserir.setBoolean(i++, obj.isUsaEcf());
        sqlInserir.setBoolean(i++, obj.isRodarSqlsBancoInicial());
        sqlInserir.setBoolean(i++, obj.isAlteracaoDataBaseContrato());
        sqlInserir.setInt(i++, obj.getToleranciaDiasContratoVencido());
        sqlInserir.setString(i++, obj.getUrlRecorrencia());
        sqlInserir.setInt(i++, obj.getQtdDiasExpirarSenha());
        sqlInserir.setInt(i++, obj.getQtdDiasEstornoAutomaticoContrato());
        sqlInserir.setBoolean(i++, obj.isAcessoChamada());
        if (obj.getLocalAcessoChamada().getCodigo() != 0) {
            sqlInserir.setInt(i++, obj.getLocalAcessoChamada().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        if (obj.getColetorChamada().getCodigo() != 0) {
            sqlInserir.setInt(i++, obj.getColetorChamada().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUltimaRepescagem()));
        sqlInserir.setBoolean(i++, obj.isBloquearAcessoSeParcelaAberta());
        sqlInserir.setBoolean(i++, obj.isEnviarSMSAutomatico());
        sqlInserir.setBoolean(i++, obj.isNomeDataNascValidar());
        sqlInserir.setBoolean(i++, obj.isEnviarRemessasRemotamente());
        sqlInserir.setBoolean(i++, obj.isValidarContatoMeta());
        sqlInserir.setBoolean(i++, obj.isEcfPorPagamento());
        sqlInserir.setBoolean(i++, obj.isForcarCodigoAlternativoAcesso());
        sqlInserir.setBoolean(i++, obj.isItemVendaAvulsaAutomatico());

        if (obj.getQuestionarioPrimeiraCompra().getCodigo() != 0) {
            sqlInserir.setInt(i++, obj.getQuestionarioPrimeiraCompra().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        sqlInserir.setInt(i++, obj.getNrDiasVigenteQuestionarioPrimeiraCompra());

        if (obj.getQuestionarioRetornoCompra().getCodigo() != 0) {
            sqlInserir.setInt(i++, obj.getQuestionarioRetornoCompra().getCodigo());
        } else {
            sqlInserir.setNull(i++, 0);
        }
        sqlInserir.setInt(i++, obj.getNrDiasVigenteQuestionarioRetornoCompra());
        sqlInserir.setString(i++, obj.getNumeroCielo());
        sqlInserir.setBoolean(i++, obj.isValidarCpfDuplicado());
        sqlInserir.setBoolean(i++, obj.isMarcarPresencaPeloAcesso());
        sqlInserir.setBoolean(i++, obj.isUsarNomeResponsavelNota());
        sqlInserir.setInt(i++, obj.getQtdDiaPrimeiraParcelaVencidaEstornarContrato());
        sqlInserir.setInt(i++, obj.getQtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline());
        sqlInserir.setBoolean(i++, obj.getDefaultEnderecoCorrespondecia());
        sqlInserir.setBoolean(i++, obj.isUtilizarSistemaParaClube());
        sqlInserir.setBoolean(i++, obj.isImprimirReciboPagtoMatricial());
        sqlInserir.setBoolean(i++, obj.isEcfApenasPlano());
        sqlInserir.setBoolean(i++, obj.getHabilitarGestaoArmarios());
        sqlInserir.setInt(i++, obj.getDiaProrataArmario());
        sqlInserir.setString(i++, obj.getNomenclaturaVendaCredito());
        sqlInserir.setInt(i++, obj.getSequencialItem());
        sqlInserir.setBoolean(i++, obj.getSesc());
        sqlInserir.setBoolean(i++, obj.getSesiCe());
        sqlInserir.setBoolean(i++, obj.isUsaAprovaFacil());
        sqlInserir.setBoolean(i++, obj.isCancelarContratoNaUnidadeOrigemAoTransferirAluno());
        sqlInserir.setBoolean(i++, obj.isUsarDigitalComoAssinatura());
        sqlInserir.setBoolean(i++, obj.isUtilizarTipoPlano());
        sqlInserir.setBoolean(i++, obj.isTransferirAutorizacaoCobranca());
        sqlInserir.setInt(i++, obj.getSequencialArquivo());
        sqlInserir.setBoolean(i++, obj.isNomeArquivoRemessaPadraoTivit());
        sqlInserir.setBoolean(i++, obj.isDefinirDataInicioPlanosRecorrencia());
        sqlInserir.setBoolean(i++, obj.isUsarSistemaInternacional());
        sqlInserir.setBoolean(i++, obj.isValidarCPFResponsaveis());
        sqlInserir.setBoolean(i++, obj.isExibirModalInativarUsuarios());
        sqlInserir.setBoolean(i++, obj.isUsarVerificadorRemessasRejeitadas());
        sqlInserir.setBoolean(i++, obj.isAgruparRemessasGetnet());
        sqlInserir.setBoolean(i++, obj.isApresentarMarketPlace());
        sqlInserir.setBoolean(i++, obj.isAgruparRemessasCartaoEDI());
        sqlInserir.setBoolean(i++, obj.isPermitirReplicarPlanoRedeEmpresa());
        sqlInserir.setBoolean(i++, obj.isPropagaraAssinaturaDigital());
        sqlInserir.setBoolean(i++, obj.isTermoResponsabilidade());
        sqlInserir.setBoolean(i++, obj.getForcarUtilizacaoPlanoAntigo());
        sqlInserir.setString(i++, obj.getMascaraTelefone());
        sqlInserir.setBoolean(i++, obj.isUtilizarFormatoMMDDYYYDtNascimento());
        sqlInserir.setBoolean(i++, obj.isUtilizarServicoSesiSC());
        sqlInserir.setBoolean(i++, obj.getLumi());
        sqlInserir.setBoolean(i++, obj.isAssinaturaContratoViaEmail());
        sqlInserir.setBoolean(i++, obj.isRealizarEnvioSesiSC());
        sqlInserir.setBoolean(i++, obj.isPermiteTrocaEmpresaMultiChave());
        sqlInserir.setBoolean(i++, obj.isPermiteLancarFeriasPlanoRecorrente());
        sqlInserir.setBoolean(i++, obj.isPermitirMudarTipoParcelamentoVendaRapida());
        sqlInserir.setBoolean(i++, obj.isPermitirReplicarFeriadoRedeEmpresa());
        sqlInserir.setBoolean(i++, obj.isUsaPlanoRecorrenteCompartilhado());
        sqlInserir.setBoolean(i++, obj.isPermitirReplicarPerfilAcessoRedeEmpresa());
        sqlInserir.setBoolean(i++, obj.isPermitirReplicarUsuarioRedeEmpresa());
        sqlInserir.setBoolean(i++, obj.isPermitirReplicarFornecedorRedeEmpresa());
        sqlInserir.setBoolean(i++, obj.isPermiteImpressaoContratoMutavel());
        sqlInserir.setBoolean(i++, obj.isPermitirReplicarModeloContratoRedeEmpresa());
        sqlInserir.setBoolean(i++, obj.isConciliarSemNumeroParcela());
        sqlInserir.setBoolean(i++, obj.isManterContratoAssinadoNaRenovacaoContrato());
        sqlInserir.setBoolean(i++,obj.isTermoResponsabilidadeExaluno());
        sqlInserir.setBoolean(i++,obj.isLoginatravesazuread());
        sqlInserir.setString(i++,obj.getAzureadtenatid());
        sqlInserir.setString(i++,obj.getAzureadclientid());
        sqlInserir.setBoolean(i++, obj.getApiSescGo());
        sqlInserir.setString(i++, obj.getUsuarioApiSescGo());
        sqlInserir.setString(i++, obj.getSenhaApiSescGo());
        sqlInserir.setBoolean(i++, obj.isExibirModalPlanosInativos());
        sqlInserir.setBoolean(i++, obj.isPermiteEstornarContrato30MinAposLancamento());
        sqlInserir.setInt(i++, obj.getIdadeMinima());

        return sqlInserir;

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ConfiguracaoSistemaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operação na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoSistemaVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ConfiguracaoSistemaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ConfiguracaoSistemaVO.validarDados(obj);
            incluir(getIdEntidade());
            try (PreparedStatement sqlInserir = prepararIncluir(obj)) {
                sqlInserir.execute();
            }
            gravarEmailsRecorrencia(obj);
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ConfiguracaoSistemaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operação na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoSistemaVO</code> que será alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ConfiguracaoSistemaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ConfiguracaoSistemaVO.validarDados(obj);
            alterar(getIdEntidade());
            try (PreparedStatement sqlAlterar = prepararAlterar(obj)) {
                sqlAlterar.execute();
                if (maisDeUmaEmpresa()) {
                    try (PreparedStatement sqlAlterar2 = prepararAlterarEmpresa(obj, 1)) {
                        sqlAlterar2.execute();
                    }
                }
            }

            getFacade().getConfiguracaoSistemaCadastroCliente().alterarConfiguracaoClienteSemCommit(obj);
            gravarEmailsRecorrencia(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public boolean maisDeUmaEmpresa() throws Exception {
        try (PreparedStatement sqlAlterar = con.prepareStatement("SELECT count(*) AS qtdEmpresa FROM empresa")) {
            try (ResultSet resultSet = sqlAlterar.executeQuery()) {
                resultSet.next();
                return resultSet.getInt("qtdEmpresa") == 1;
            }
        }
    }

    public boolean maisEmpresa() throws Exception {
        try (PreparedStatement sqlAlterar = con.prepareStatement("SELECT count(*) AS qtdEmpresa FROM empresa")) {
            try (ResultSet resultSet = sqlAlterar.executeQuery()) {
                resultSet.next();
                return resultSet.getInt("qtdEmpresa") > 1;
            }
        }
    }

    private PreparedStatement prepararAlterar(ConfiguracaoSistemaVO obj) throws Exception {
        PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate);
        int i = 1;
        if (obj.getQuestionarioPrimeiraVisita().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioPrimeiraVisita().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (obj.getQuestionarioRetorno().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioRetorno().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (obj.getQuestionarioReMatricula().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioReMatricula().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }

        sqlAlterar.setDouble(i++, obj.getJuroParcela());
        sqlAlterar.setDouble(i++, obj.getMulta());
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioVista());
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRetorno());
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRematricula());
        sqlAlterar.setString(i++, obj.getMascaraMatricula());
        sqlAlterar.setInt(i++, obj.getCarenciaRenovacao());
        sqlAlterar.setInt(i++, obj.getNrDiasAvencer());
        sqlAlterar.setInt(i++, obj.getToleranciaPagamento());
        sqlAlterar.setInt(i++, obj.getQtdFaltaPeso1());
        sqlAlterar.setInt(i++, obj.getQtdFaltaInicioPeso2());
        sqlAlterar.setInt(i++, obj.getQtdFaltaTerminoPeso2());
        sqlAlterar.setInt(i++, obj.getQtdFaltaPeso3());
        sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataInicioDesconsiderarAcessoRisco()));
        sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataFimDesconsiderarAcessoRisco()));
        sqlAlterar.setInt(i++, obj.getCarencia());
        sqlAlterar.setBoolean(i++, obj.isCpfValidar());
        sqlAlterar.setInt(i++, obj.getNrDiasProrata());
        sqlAlterar.setString(i++, obj.getUrlGoogleAgenda());
        sqlAlterar.setInt(i++, obj.getVencimentoColaborador());
        sqlAlterar.setDouble(i++, obj.getAliquotaServico());
        sqlAlterar.setDouble(i++, obj.getAliquotaProduto());
        sqlAlterar.setBoolean(i++, obj.isUsaEcf());
        sqlAlterar.setBoolean(i++, obj.isRodarSqlsBancoInicial());
        sqlAlterar.setBoolean(i++, obj.isAlteracaoDataBaseContrato());
        sqlAlterar.setInt(i++, obj.getToleranciaDiasContratoVencido());
        sqlAlterar.setString(i++, obj.getUrlRecorrencia());
        sqlAlterar.setInt(i++, obj.getQtdDiasExpirarSenha());
        sqlAlterar.setInt(i++, obj.getQtdDiasEstornoAutomaticoContrato());
        sqlAlterar.setBoolean(i++, obj.isAcessoChamada());
        if (obj.getLocalAcessoChamada().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getLocalAcessoChamada().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (obj.getColetorChamada().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getColetorChamada().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUltimaRepescagem()));
        sqlAlterar.setBoolean(i++, obj.isBloquearAcessoSeParcelaAberta());
        sqlAlterar.setBoolean(i++, obj.isEnviarSMSAutomatico());
        sqlAlterar.setBoolean(i++, obj.isNomeDataNascValidar());
        sqlAlterar.setBoolean(i++, obj.isEnviarRemessasRemotamente());
        sqlAlterar.setBoolean(i++, obj.isValidarContatoMeta());
        sqlAlterar.setBoolean(i++, obj.isEcfPorPagamento());
        sqlAlterar.setBoolean(i++, obj.isForcarCodigoAlternativoAcesso());
        sqlAlterar.setBoolean(i++, obj.isItemVendaAvulsaAutomatico());

        if (obj.getQuestionarioPrimeiraCompra().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioPrimeiraCompra().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioPrimeiraCompra());

        if (obj.getQuestionarioRetornoCompra().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioRetornoCompra().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRetornoCompra());
        sqlAlterar.setString(i++, obj.getNumeroCielo());
        sqlAlterar.setBoolean(i++, obj.isValidarCpfDuplicado());
        sqlAlterar.setBoolean(i++, obj.isMarcarPresencaPeloAcesso());
        sqlAlterar.setBoolean(i++, obj.isUsarNomeResponsavelNota());
        sqlAlterar.setInt(i++, obj.getQtdDiaPrimeiraParcelaVencidaEstornarContrato());
        sqlAlterar.setInt(i++, obj.getQtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline());
        sqlAlterar.setBoolean(i++, obj.getDefaultEnderecoCorrespondecia());
        sqlAlterar.setBoolean(i++, obj.isUtilizarSistemaParaClube());
        sqlAlterar.setBoolean(i++, obj.isImprimirReciboPagtoMatricial());
        sqlAlterar.setBoolean(i++, obj.isEcfApenasPlano());
        sqlAlterar.setBoolean(i++, obj.getHabilitarGestaoArmarios());
        sqlAlterar.setInt(i++, obj.getDiaProrataArmario());
        sqlAlterar.setString(i++, obj.getNomenclaturaVendaCredito());
        sqlAlterar.setInt(i++, obj.getSequencialItem());
        sqlAlterar.setBoolean(i++, obj.getSesc());
        sqlAlterar.setBoolean(i++, obj.getSesiCe());
        sqlAlterar.setBoolean(i++, obj.isControleAcessoMultiplasEmpresasPorPlano());
        sqlAlterar.setBoolean(i++, obj.getPriorizarVendaRapida());
        sqlAlterar.setBoolean(i++, obj.getBarrarDevedorVendaRapida());
        sqlAlterar.setBoolean(i++, obj.getLancamentoContratosIguais());
        sqlAlterar.setBoolean(i++, obj.isUsaAprovaFacil());
        sqlAlterar.setBoolean(i++, obj.isCancelarContratoNaUnidadeOrigemAoTransferirAluno());
        sqlAlterar.setBoolean(i++, obj.isUsarDigitalComoAssinatura());
        sqlAlterar.setBoolean(i++, obj.isUtilizarTipoPlano());
        sqlAlterar.setBoolean(i++, obj.isTransferirAutorizacaoCobranca());
        sqlAlterar.setInt(i++, obj.getSequencialArquivo());
        sqlAlterar.setBoolean(i++, obj.isNomeArquivoRemessaPadraoTivit());
        sqlAlterar.setBoolean(i++, obj.isDefinirDataInicioPlanosRecorrencia());
        sqlAlterar.setBoolean(i++, obj.isUsarSistemaInternacional());
        sqlAlterar.setBoolean(i++, obj.isValidarCPFResponsaveis());
        sqlAlterar.setBoolean(i++, obj.isExibirModalInativarUsuarios());
        sqlAlterar.setBoolean(i++, obj.isUsarVerificadorRemessasRejeitadas());
        sqlAlterar.setBoolean(i++, obj.isAgruparRemessasGetnet());
        sqlAlterar.setBoolean(i++, obj.isApresentarMarketPlace());
        sqlAlterar.setBoolean(i++, obj.isAgruparRemessasCartaoEDI());
        sqlAlterar.setBoolean(i++, obj.isPermitirReplicarPlanoRedeEmpresa());
        sqlAlterar.setBoolean(i++, obj.isPropagaraAssinaturaDigital());
        sqlAlterar.setBoolean(i++, obj.getForcarUtilizacaoPlanoAntigo());
        sqlAlterar.setString(i++, obj.getMascaraTelefone());
        sqlAlterar.setBoolean(i++, obj.isUtilizarFormatoMMDDYYYDtNascimento());
        sqlAlterar.setBoolean(i++, obj.isUtilizarServicoSesiSC());
        sqlAlterar.setBoolean(i++, obj.getLumi());
        sqlAlterar.setBoolean(i++, obj.isAssinaturaContratoViaEmail());
        sqlAlterar.setBoolean(i++, obj.isTermoResponsabilidade());
        sqlAlterar.setInt(i++, obj.getQtdAcessoGymPassInicioFaixa1());
        sqlAlterar.setInt(i++, obj.getQtdAcessoGymPassInicioFaixa2());
        sqlAlterar.setInt(i++, obj.getQtdAcessoGymPassInicioFaixa3());
        sqlAlterar.setInt(i++, obj.getQtdAcessoGymPassInicioFaixa4());
        sqlAlterar.setInt(i++, obj.getQtdAcessoGymPassFinalFaixa1());
        sqlAlterar.setInt(i++, obj.getQtdAcessoGymPassFinalFaixa2());
        sqlAlterar.setInt(i++, obj.getQtdAcessoGymPassFinalFaixa3());
        sqlAlterar.setInt(i++, obj.getQtdAcessoGymPassFinalFaixa4());
        sqlAlterar.setBoolean(i++, obj.isRealizarEnvioSesiSC());
        sqlAlterar.setBoolean(i++, obj.isPermiteTrocaEmpresaMultiChave());
        sqlAlterar.setBoolean(i++, obj.isPermiteLancarFeriasPlanoRecorrente());
        sqlAlterar.setBoolean(i++, obj.isPermitirMudarTipoParcelamentoVendaRapida());
        sqlAlterar.setBoolean(i++, obj.isPermitirReplicarFeriadoRedeEmpresa());
        sqlAlterar.setBoolean(i++, obj.isUsaPlanoRecorrenteCompartilhado());
        sqlAlterar.setBoolean(i++, obj.isPermitirReplicarPerfilAcessoRedeEmpresa());
        sqlAlterar.setBoolean(i++, obj.isPermitirReplicarUsuarioRedeEmpresa());
        sqlAlterar.setBoolean(i++, obj.isPermitirReplicarFornecedorRedeEmpresa());
        sqlAlterar.setBoolean(i++, obj.isPermiteImpressaoContratoMutavel());
        sqlAlterar.setBoolean(i++, obj.isPermitirReplicarModeloContratoRedeEmpresa());
        sqlAlterar.setBoolean(i++, obj.isConciliarSemNumeroParcela());
        sqlAlterar.setBoolean(i++, obj.isManterContratoAssinadoNaRenovacaoContrato());
        sqlAlterar.setBoolean(i++, obj.isTermoResponsabilidadeExaluno());
        sqlAlterar.setBoolean(i++, obj.isLoginatravesazuread());
        sqlAlterar.setString(i++, obj.getAzureadtenatid());
        sqlAlterar.setString(i++, obj.getAzureadclientid());
        sqlAlterar.setBoolean(i++, obj.getApiSescGo());
        sqlAlterar.setString(i++, obj.getUsuarioApiSescGo());
        sqlAlterar.setString(i++, obj.getSenhaApiSescGo());
        sqlAlterar.setBoolean(i++, obj.isExibirModalPlanosInativos());
        sqlAlterar.setBoolean(i++, obj.isPermiteEstornarContrato30MinAposLancamento());
        sqlAlterar.setInt(i++, obj.getIdadeMinima());

        sqlAlterar.setInt(i++, obj.getCodigo());
        return sqlAlterar;

    }

    private PreparedStatement prepararAlterarEmpresa(ConfiguracaoSistemaVO obj, final int codigoEmpresa) throws SQLException {
        PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdateEmpresa);
        int i = 1;
        if (obj.getQuestionarioPrimeiraVisita().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioPrimeiraVisita().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (obj.getQuestionarioRetorno().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioRetorno().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (obj.getQuestionarioReMatricula().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioReMatricula().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }

        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioVista());
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRetorno());
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRematricula());
        sqlAlterar.setString(i++, obj.getMascaraMatricula());
        sqlAlterar.setInt(i++, obj.getCarenciaRenovacao());
        sqlAlterar.setInt(i++, obj.getNrDiasAvencer());
        sqlAlterar.setInt(i++, obj.getToleranciaPagamento());
        sqlAlterar.setInt(i++, obj.getQtdFaltaPeso1());
        sqlAlterar.setInt(i++, obj.getQtdFaltaInicioPeso2());
        sqlAlterar.setInt(i++, obj.getQtdFaltaTerminoPeso2());
        sqlAlterar.setInt(i++, obj.getQtdFaltaPeso3());
        sqlAlterar.setInt(i++, obj.getCarencia());
        sqlAlterar.setInt(i++, obj.getNrDiasProrata());
        sqlAlterar.setInt(i++, obj.getToleranciaDiasContratoVencido());
        sqlAlterar.setString(i++, obj.getUrlRecorrencia());
        sqlAlterar.setBoolean(i++, obj.isBloquearAcessoSeParcelaAberta());

        if (obj.getQuestionarioPrimeiraCompra().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioPrimeiraCompra().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioPrimeiraCompra());

        if (obj.getQuestionarioRetornoCompra().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioRetornoCompra().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRetornoCompra());

        sqlAlterar.setBoolean(i++, obj.isUsarDigitalComoAssinatura());

        sqlAlterar.setInt(i++, codigoEmpresa);
        return sqlAlterar;

    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ConfiguracaoSistemaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operação na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoSistemaVO</code> que será alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterarSemCommit(ConfiguracaoSistemaVO obj) throws Exception {
        try {
            ConfiguracaoSistemaVO.validarDados(obj);
            alterar(getIdEntidade());
            try (PreparedStatement sqlAlterar = prepararAlterar(obj)) {
                sqlAlterar.execute();
            }
            getFacade().getConfiguracaoSistemaCadastroCliente().alterarConfiguracaoClienteSemCommit(obj);
            gravarEmailsRecorrencia(obj);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ConfiguracaoSistemaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operação na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoSistemaVO</code> que será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ConfiguracaoSistemaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM ConfiguracaoSistema WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public String obterMascaraMatricula(Integer configuracaoSistema) throws Exception {
        inicializar();
        String sqlStr = "SELECT ConfiguracaoSistema.mascaraMatricula FROM ConfiguracaoSistema  WHERE codigo = " + configuracaoSistema + "";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getString("mascaraMatricula");
            }
        }
    }

    public Integer obterCarenciaConfiguracao() throws Exception {
        return obterCarenciaConfiguracao(true);
    }

    public Integer obterCarenciaConfiguracao(boolean inicializar) throws Exception {
        if (inicializar) {
            inicializar();
        }
        String sqlStr = "SELECT ConfiguracaoSistema.carencia FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getInt("carencia")));
            }
        }
    }

    public boolean obterPermiteReplicarFornecedorConfiguracao() throws Exception {
        final String sqlStr = "SELECT ConfiguracaoSistema.permitirreplicarfornecedorredeempresa FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet result = stm.executeQuery(sqlStr)) {
                result.next();
                return result.getBoolean("permitirreplicarfornecedorredeempresa");
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConfiguracaoSistema</code> através do valor do atributo
     * <code>descricao</code> da classe <code>Questionario</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de preparar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>ConfiguracaoSistemaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoQuestionario(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ConfiguracaoSistema.* FROM ConfiguracaoSistema, Questionario WHERE ConfiguracaoSistema.questionarioPrimeiraVisita = Questionario.codigo and upper( Questionario.nomeinterno ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Questionario.nomeinterno";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConfiguracaoSistema</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de preparar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ConfiguracaoSistemaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConfiguracaoSistema WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ConfiguracaoSistema</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de preparar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ConfiguracaoSistemaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public ConfiguracaoSistemaVO buscarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConfiguracaoSistema WHERE codigo = " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, nivelMontarDados, con));
                } else {
                    return new ConfiguracaoSistemaVO();
                }
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ConfiguracaoSistemaVO</code>
     * através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ConfiguracaoSistemaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        if (JSFUtilities.configuracao() != null)
            return JSFUtilities.configuracao();
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ConfiguracaoSistema WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ConfiguracaoSistema ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }


    /*public boolean consultarSistemaInternacional(Integer codigoPrm, int nivelMontarDados) throws Exception{
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ConfiguracaoSistema WHERE codigo = ? AND usarsistemainternacional = true";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet resultado = sqlConsultar.executeQuery();
        if(resultado.next()){

        }

        return resultado;
    }*/


    /*public boolean consultarSistemaInternacional(Integer codigoPrm, int nivelMontarDados) throws Exception{
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ConfiguracaoSistema WHERE codigo = ? AND usarsistemainternacional = true";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet resultado = sqlConsultar.executeQuery();
        if(resultado.next()){

        }

        return resultado;
    }*/

    /**
     * Operação responsável por localizar um atributo da classe <code>ConfiguracaoSistemaVO</code>.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public boolean consultarCampoRodarSqlsBancoInicial() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ConfiguracaoSistema WHERE codigo = 1";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    ConfiguracaoSistemaVO config = new ConfiguracaoSistemaVO();
                    config.setRodarSqlsBancoInicial(tabelaResultado.getBoolean("rodarSqlsBancoInicial"));
                    if (config.isRodarSqlsBancoInicial()) {
                        return true;
                    }
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * Operação responsável por localizar um atributo da classe <code>ConfiguracaoSistemaVO</code>
     * chamado alteracaoDataBase para permitir alteração da data base do contrato.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public boolean consultarSePermiteAlteracaoDataBaseContrato() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ConfiguracaoSistema";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    ConfiguracaoSistemaVO config = new ConfiguracaoSistemaVO();
                    config.setAlteracaoDataBaseContrato(tabelaResultado.getBoolean("alteracaoDataBaseContrato"));
                    if (config.isAlteracaoDataBaseContrato()) {
                        return true;
                    }
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * @see negocio.interfaces.basico.ConfiguracaoSistemaInterfaceFacade#consultarSeInsereCupom()
     */
    public boolean consultarSeInsereCupom() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT usaecf,ecfporpagamento FROM ConfiguracaoSistema";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return !tabelaResultado.getBoolean("ecfporpagamento") && tabelaResultado.getBoolean("usaecf");
            }
        }
    }

    /**
     * @see negocio.interfaces.basico.ConfiguracaoSistemaInterfaceFacade#consultarSeInsereCupom()
     */
    public boolean consultarUsaEcf() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT usaecf FROM ConfiguracaoSistema";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("usaecf");
            }
        }
    }

    public boolean consultarUsarUsarNomeResponsavelNota() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT usarnomeresponsavelnota FROM ConfiguracaoSistema";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("usarnomeresponsavelnota");
            }
        }
    }

    public boolean consultarEcfApenasPlano() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT ecfapenasplano FROM ConfiguracaoSistema";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("ecfapenasplano");
            }
        }
    }

    public boolean consultarUsaEcfPagamento() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT ecfporpagamento FROM ConfiguracaoSistema";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("ecfporpagamento");
            }
        }
    }

    /**
     * Responsável por inserir em banco a lista de emails dos responsáveis pelo pagamento em recorrência
     *
     * <AUTHOR>
     * 04/07/2011
     */
    public void gravarEmailsRecorrencia(ConfiguracaoSistemaVO config) throws Exception {
        //limpar os emails cadastrados para evitar duplicidade
        String delete = "DELETE FROM emailsrecorrencia";
        Declaracao dc = new Declaracao(delete, con);
        dc.execute();
        //inserir todos os emails da lista
        String insert = "INSERT INTO emailsrecorrencia(email) VALUES (?)";
        dc = new Declaracao(insert, con);
        for (String email : config.getListaEmailsRecorrencia()) {
            dc.setString(1, email);
            dc.execute();
        }
    }

    /**
     * Responsável por inserir em banco a lista de emails dos responsáveis pelo pagamento em recorrência
     *
     * <AUTHOR>
     * 04/07/2011
     */
    public void gravarEmailsFechamentoAcessos(ConfiguracaoSistemaVO conf) throws Exception {
        //inserir todos os emails da lista
        String update = "UPDATE configuracaosistema SET emailsFechamentoAcessos = ? where codigo = 1";
        Declaracao dc = new Declaracao(update, con);
        dc.setString(1, conf.getEmailsFechamentoAcessos());
        dc.execute();
    }

    /**
     * Responsável por montar nas configurações do sistema a lista de Emails dos responsáveis Recorrência
     *
     * <AUTHOR>
     * 04/07/2011
     */
    public void montarEmailsRecorrencia(ConfiguracaoSistemaVO config) throws Exception {
        String select = "SELECT email FROM emailsrecorrencia";
        Declaracao dc = new Declaracao(select, con);
        try (ResultSet rs = dc.executeQuery()) {
            config.setListaEmailsRecorrencia(new ArrayList<String>());
            while (rs.next()) {
                config.getListaEmailsRecorrencia().add(rs.getString("email"));
            }
        }
    }

    /**
     * Responsável por montar nas configurações do sistema a lista de Emails
     *
     * <AUTHOR>
     * 27/01/2012
     */
    public String consultarEmailsFechamentoAcessos(ConfiguracaoSistemaVO config) throws Exception {
        String select = "SELECT emailsFechamentoAcessos FROM configuracaosistema";
        Declaracao dc = new Declaracao(select, con);
        try (ResultSet rs = dc.executeQuery()) {
            if (rs.next()) {
                return rs.getString("emailsFechamentoAcessos");
            }
        }
        return "";
    }

    public Integer buscarQtdDiasParaExpirarSenha() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT qtdDiasExpirarSenha FROM ConfiguracaoSistema";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getInt("qtdDiasExpirarSenha");
            }
        }
    }

    public Boolean verificarSePermiteEnviarSMSAutomatico() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT enviarSMSAutomatico FROM ConfiguracaoSistema";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("enviarSMSAutomatico");
            }
        }
    }

    public Boolean verificarValidacaoContatoMeta() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT validarcontatometa FROM ConfiguracaoSistema";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("validarcontatometa");
            }
        }
    }

    public String obterNumeroCielo() throws Exception {
        inicializar();
        String sqlStr = "SELECT numerocielo FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getString("numerocielo")));
            }
        }
    }

    public boolean obterReplicarRedeEmpresa(String nomeConfig) throws Exception {
        inicializar();
        String sqlStr = "SELECT "+nomeConfig+" FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getBoolean(nomeConfig)));
            }
        }
    }

    public ConfiguracaoSistemaVO consultarConfigs(int nivelMontarDados) throws Exception {
        if (JSFUtilities.configuracao() != null)
            return JSFUtilities.configuracao();
        String sqlStr = "SELECT * FROM ConfiguracaoSistema ORDER BY codigo ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public ConfiguracaoSistemaVO consultarConfigsParaAtualizarDadosBI(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ConfiguracaoSistema ORDER BY codigo ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public boolean usarNomeResponsavelNota() throws Exception {
        String sqlStr = "SELECT usarnomeresponsavelnota FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getBoolean("usarnomeresponsavelnota")));
            }
        }
    }

    public boolean utilizarSistemaParaClube() throws Exception {
        String sqlStr = "SELECT utilizarSistemaParaClube FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getBoolean("utilizarSistemaParaClube")));
            }
        }
    }

    public boolean imprimirReciboPagtoMatricial() throws Exception {
        String sqlStr = "SELECT imprimirReciboPagtoMatricial FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getBoolean("imprimirReciboPagtoMatricial")));
            }
        }
    }


    public boolean validarCPF() throws Exception{
        String sqlStr = "SELECT cpfValidar FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getBoolean("cpfValidar")));
            }
        }
    }

    public Integer incrementarSequencialItem() throws Exception {
        Integer sequencial;
        Connection con2 = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        con2.setAutoCommit(false);
        try {
            String sql = "SELECT sequencialItem FROM configuracaosistema FOR UPDATE";
            try (ResultSet rs = con2.prepareStatement(sql).executeQuery()) {
                rs.next();
                sequencial = rs.getInt("sequencialItem");
            }
            sequencial++;
            String sqlUpdate = "UPDATE configuracaosistema SET sequencialItem = " + sequencial;
            try (PreparedStatement ps = con2.prepareStatement(sqlUpdate)) {
                ps.executeUpdate();
            }
            con2.commit();
        } catch (Exception e) {
            con2.rollback();
            throw e;
        } finally {
            con2.setAutoCommit(true);
            con2.close();
        }
        return sequencial;
    }

    @Override
    public Integer incrementarSequencialArquivo() throws Exception {
        Integer sequencial;
        Connection con2 = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        con2.setAutoCommit(false);
        try {
            String sql = "SELECT sequencialarquivo FROM configuracaosistema FOR UPDATE";
            try (ResultSet rs = con2.prepareStatement(sql).executeQuery()) {
                rs.next();
                sequencial = rs.getInt("sequencialarquivo");
            }
            sequencial++;
            String sqlUpdate = "UPDATE configuracaosistema SET sequencialarquivo = " + sequencial;
            try (PreparedStatement ps = con2.prepareStatement(sqlUpdate)) {
                ps.executeUpdate();
            }
            con2.commit();
        } catch (Exception e) {
            con2.rollback();
            throw e;
        } finally {
            con2.setAutoCommit(true);
            con2.close();
        }
        return sequencial;
    }


    public void incluirFiltro(Integer usuario, FiltrosEnum filtro, String valor) throws Exception {
        deleteFiltro(usuario, filtro);
        StringBuilder insert = new StringBuilder();
        insert.append("INSERT INTO filtros( filtro, valor, usuario) ");
        insert.append("    VALUES (?, ?, ?);");
        try (PreparedStatement stm = con.prepareStatement(insert.toString())) {
            stm.setInt(1, filtro.getCodigo());
            stm.setString(2, valor);
            stm.setInt(3, usuario);
            stm.execute();
        }
    }

    public void deleteFiltro(Integer usuario, FiltrosEnum filtro) throws Exception{
        try (PreparedStatement stm = con.prepareStatement("DELETE FROM filtros WHERE filtro = ? and usuario = ?")) {
            stm.setInt(1, filtro.getCodigo());
            stm.setInt(2, usuario);
            stm.execute();
        }
    }

    public String obterFiltro(Integer usuario, FiltrosEnum filtro) throws Exception {
        try (PreparedStatement stm = con.prepareStatement("SELECT valor FROM filtros WHERE filtro = ? and usuario = ?")) {
            stm.setInt(1, filtro.getCodigo());
            stm.setInt(2, usuario);
            try (ResultSet rs = stm.executeQuery()) {
                return rs.next() ? rs.getString("valor") : null;
            }
        }
    }

    @Override
    public Integer obterDiasQuestionarioPorTipo(Integer codigo) throws Exception {
        Integer retorno = 0;
        try (PreparedStatement stm = con.prepareStatement("SELECT nrdiasvigentequestionariovisita from configuracaosistema  where questionarioprimeiravisita = ?")) {
            stm.setInt(1, codigo);
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    retorno = rs.getInt("nrdiasvigentequestionariovisita");
                } else {
                    try (PreparedStatement stm2 = con.prepareStatement("SELECT nrdiasvigentequestionarioretorno from configuracaosistema where questionarioretorno = ?")) {
                        stm2.setInt(1, codigo);
                        try (ResultSet rs1 = stm2.executeQuery()) {
                            if (rs1.next()) {
                                retorno = rs1.getInt("nrdiasvigentequestionarioretorno");
                            } else {
                                try (PreparedStatement stm3 = con.prepareStatement("SELECT nrdiasvigentequestionariorematricula from configuracaosistema where questionariorematricula = ?")) {
                                    stm3.setInt(1, codigo);
                                    try (ResultSet rs2 = stm3.executeQuery()) {
                                        if (rs2.next()) {
                                            retorno = rs2.getInt("nrdiasvigentequestionariorematricula");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return retorno;
    }

    public boolean usarDigitalComoAssinatura() throws Exception {
        String sqlStr = "SELECT usardigitalcomoassinatura FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getBoolean("usardigitalcomoassinatura")));
            }
        }
    }

    public boolean isJustfit() throws Exception {
        String sqlStr = "SELECT justfit FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("justfit");
            }
        }
    }

    public boolean isNomeArquivoRemessaPadraoTivit() throws Exception {
        String sqlStr = "SELECT nomeArquivoRemessaPadraoTivit FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("nomeArquivoRemessaPadraoTivit");
            }
        }
    }

    public boolean isDefinirDataInicioPlanosRecorrencia() throws Exception {
        String sqlStr = "SELECT definirDataInicioPlanosRecorrencia FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("definirDataInicioPlanosRecorrencia");
            }
        }
    }

    public Integer obterSequencialNotaFamilia() throws Exception {
        String sqlStr = "SELECT seqNotaFiscalFamilia FROM ConfiguracaoSistema";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getInt("seqNotaFiscalFamilia");
            }
        }
    }

    public void incrementarSequencialNotaFamilia() throws Exception {
        String sql = "UPDATE ConfiguracaoSistema set seqNotaFiscalFamilia = (seqNotaFiscalFamilia + 1)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.execute();
        }
    }

    public Integer obterSequencialProcessoImportacao() throws Exception {
        String sqlStr = "SELECT seqProcessoImportacao FROM ConfiguracaoSistema";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getInt("seqProcessoImportacao");
            }
        }
    }

    public void incrementarSequencialProcessoImportacao() throws Exception {
        String sql = "UPDATE ConfiguracaoSistema set seqProcessoImportacao = (seqProcessoImportacao + 1)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.execute();
        }
    }

    public Integer obterNrDiasAvencer() throws Exception {
        String sqlStr = "SELECT nrDiasAvencer FROM ConfiguracaoSistema";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getInt("nrDiasAvencer");
            }
        }
    }

    public void alterarBancoApresentacao() throws Exception{
        executarUpdate("UPDATE ConfiguracaoSistemaCadastroCliente set obrigatorio = 'f', mostrar = 'f', pendente = 'f' WHERE nome = 'CPF'", this.con);
        executarUpdate("UPDATE pessoa set cfp = '', rg = ''", this.con);
        executarUpdate("UPDATE configuracaosistema\n" +
                "   SET emailcontapagdigital='', tokencontapagdigital='', \n" +
                "       tokencontasms='', cpfvalidar='f', \n" +
                "      urlgoogleagenda='', \n" +
                "       urlrecorrencia='', emailsfechamentoacessos='',  \n" +
                "       enviarsmsautomatico='f',  enviarremessasremotamente='f', \n" +
                "        validarcpfduplicado='f', \n" +
                "        validarcpfresponsaveis='f', realizarenviosesisc='f'", this.con);
        executarUpdate("UPDATE empresa\n" +
                "   SET urlrecorrencia='', serviceusuario='', servicesenha='',enviarnfseautomatico='f',\n" +
                "       codigogympass='', tokenapigympass='', codigochaveintegracaodigitais=null, \n" +
                "       tokensmsshortcode='',      \n" +
                "       enviarnfceautomatico='f',\n" +
                "       tokenbuzzlead='', urllinksitecadastro='',    \n" +
                " integracaomywellnehabilitada='f',  integracaomywellnessfacilityurl='', integracamywellneapikey='', integracaomywellnesspassword='', integracaomywellnessuser='', " +
                "       cod_empresafinanceiro=0;", this.con);
        executarUpdate("delete from configuracaoemailfechamentometa", this.con);
        executarUpdate("delete from emailsrecorrencia", this.con);
        executarUpdate("UPDATE configuracaosistemacrm\n" +
                "   SET  remetentepadrao='academia Apresentacao', emailpadrao='<EMAIL>', login='', senha='', remetentepadraomailing=null, mailingftpserver='', mailingftpuser='', mailingftppass='', mailingftpport=null, mailingftptype='', \n" +
                "       mailingftpfolder='', tokenbitly=''", this.con);

    }

    public boolean isUsarVerificadorRemessasRejeitadas() {
        try {
            String sqlStr = "SELECT usarverificadorremessasrejeitadas FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                    tabelaResultado.next();
                    return tabelaResultado.getBoolean("usarverificadorremessasrejeitadas");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean isAgruparRemessasGetnet() {
        try {
            String sqlStr = "SELECT agruparRemessasGetnet FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("agruparRemessasGetnet");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean isPriorizarVendaRapida() throws Exception {
        String sqlStr = "SELECT priorizarVendaRapida FROM ConfiguracaoSistema ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                rs.next();
                return rs.getBoolean("priorizarVendaRapida");
            }
        }
    }

    public boolean isApresentarMarketPlace() throws Exception {
        try {
            String sqlStr = "SELECT apresentarMarketPlace FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("apresentarMarketPlace");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean isAgruparRemessasCartaoEDI() {
        try {
            String sqlStr = "SELECT agruparRemessasCartaoEDI FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("agruparRemessasCartaoEDI");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public void persistirChavePublicaPrivadaSesc(ConfiguracaoSistemaVO obj) throws Exception {
        URIBuilder builder = new URIBuilder(Uteis.URL_SESC + "ValidarChavePublica");
        builder.setParameter("chavePublica", obj.getChavePublicaSESC())
                .setParameter("chavePrivada", obj.getChavePrivadaSESC());

        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        HttpGet httpGet = new HttpGet(builder.build());
        HttpResponse httpResponse = httpClient.execute(httpGet);
        String retornoValidacao = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");

        JSONObject jsonRetornoValidacao = new JSONObject(retornoValidacao);
        boolean valida = jsonRetornoValidacao.optBoolean("Valida", false);

        if (valida) {
            persistirChavesSesc(obj);
        } else {
            String message = jsonRetornoValidacao.optString("Message");
            throw new ConsistirException("Retorno SESC: " + message);
        }
    }

    private void persistirChavesSesc(ConfiguracaoSistemaVO obj) throws SQLException {
        String sql = "UPDATE ConfiguracaoSistema SET chavepublicasesc = ?, chaveprivadasesc = ?";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setString(++i, obj.getChavePublicaSESC());
            sqlInserir.setString(++i, obj.getChavePrivadaSESC());
            sqlInserir.execute();
        }
    }

    public boolean isAtivarVerificarCartao() {
        try {
            String sqlStr = "SELECT ativarverificarcartao FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("ativarverificarcartao");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return true;
        }
    }

    public boolean isEnviarRemessasRemotamente() {
        try {
            String sqlStr = "SELECT enviarRemessasRemotamente FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("enviarRemessasRemotamente");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean isPropagarAssinaturaDigital() {
        try {
            String sqlStr = "SELECT propagaraAssinaturaDigital FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("propagaraAssinaturaDigital");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean isTermoResponsabilidade() {
        try {
            String sqlStr = "SELECT termoresponsabilidade FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("termoresponsabilidade");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean isTermoResponsabilidadeExaluno() {
        try {
            String sqlStr = "SELECT termoresponsabilidadeExaluno FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("termoresponsabilidadeExaluno");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean utilizarServicoSesiSC() {
        try {
            String sqlStr = "SELECT utilizarServicoSesiSC FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("utilizarServicoSesiSC");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean realizarEnvioSesiSC() {
        try {
            String sqlStr = "SELECT realizarenviosesisc, utilizarServicoSesiSC FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("utilizarServicoSesiSC") && rs.getBoolean("realizarenviosesisc") ;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public Boolean verificarAssinaturaDigital() throws Exception{
        try (PreparedStatement stm = this.con.prepareStatement("SELECT * FROM configuracaosistema WHERE assinaturacontratoviaemail = true")) {
            try (ResultSet rs = stm.executeQuery()){
                while (rs.next()) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isPermiteTrocaEmpresaMultiChave() {
        try {
            String sqlStr = "SELECT permiteTrocaEmpresaMultiChave FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("permiteTrocaEmpresaMultiChave");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean isForcarNovoLogin() {
        try {
            String sqlStr = "SELECT forcarNovoLogin FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean(1);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean isPermiteImpressaoContratoMutavel() {
        try {
            String sqlStr = "SELECT permiteImpressaoContratoMutavel FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean(1);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean isPermiteEstornarContrato30MinAposLancamento() {
        try {
            String sqlStr = "SELECT permiteEstornarContrato30MinAposLancamento FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean(1);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public boolean isConciliarSemNumeroParcela() throws Exception {
        String sqlStr = "SELECT conciliarsemnumeroparcela FROM ConfiguracaoSistema;";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getBoolean("conciliarsemnumeroparcela")));
            }
        }
    }

    public boolean isManterContratoAssinadoNaRenovacaoContrato() {
        try {
            String sqlStr = "SELECT manterContratoAssinadoNaRenovacaoContrato FROM ConfiguracaoSistema ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    rs.next();
                    return rs.getBoolean("manterContratoAssinadoNaRenovacaoContrato");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    @Override
    public ConfiguracaoSistemaVO buscarConfiguracaoSistema() throws Exception {
        String sqlStr = "SELECT codigo, tituloconvite, descricaoconvite, tempoReabilitacaoExAluno FROM ConfiguracaoSistema ORDER BY codigo ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ConfiguracaoSistema ).");
                }
                return (montarDadosConfigConvenio(tabelaResultado));
            }
        }
    }

    public static ConfiguracaoSistemaVO montarDadosConfigConvenio(ResultSet dadosSQL) throws Exception {
        ConfiguracaoSistemaVO obj = new ConfiguracaoSistemaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setTituloConvite(dadosSQL.getString("tituloConvite"));
        obj.setDescricaoConvite(dadosSQL.getString("descricaoConvite"));
        obj.setTempoReabilitacaoExAluno(dadosSQL.getInt("tempoReabilitacaoExAluno"));
        return obj;
    }
}
