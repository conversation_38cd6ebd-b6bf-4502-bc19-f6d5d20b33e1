package negocio.facade.jdbc.basico;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.StatusPactoPayComunicacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.PactoPayComunicacaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PactoPayComunicacao extends SuperEntidade implements PactoPayComunicacaoInterfaceFacade {

    public PactoPayComunicacao() throws Exception {
        super();
    }

    public PactoPayComunicacao(Connection conexao) throws Exception {
        super(conexao);
    }

    private PactoPayComunicacaoVO montarDadosBasico(ResultSet rs) throws Exception {
        PactoPayComunicacaoVO obj = new PactoPayComunicacaoVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
        obj.getEmpresaVO().setCodigo(rs.getInt("empresa"));
        obj.getPessoaVO().setCodigo(rs.getInt("pessoa"));
        obj.getHistoricoContatoVO().setCodigo(rs.getInt("historicoContato"));
        obj.setMeioEnvio(MeioEnvio.getMeioEnvioPorCodigo(rs.getInt("meioEnvio")));
        obj.setTipoEnvioPactoPay(TipoEnvioPactoPayEnum.obterPorId(rs.getInt("tipoEnvio")));
        obj.setComunicacao(rs.getString("comunicacao"));
        obj.setSucesso(rs.getBoolean("sucesso"));
        obj.setDataExecucao(rs.getTimestamp("dataExecucao"));
        obj.setResultado(rs.getString("resultado"));
        obj.setDataLido(rs.getTimestamp("datalido"));
        obj.setDataClicou(rs.getTimestamp("dataclicou"));
        obj.setTags(rs.getString("tags"));

        try {
            obj.getPessoaVO().setNome(rs.getString("nome_pessoa"));
        } catch (Exception ignored) {
        }
        try {
            obj.getEmpresaVO().setNome(rs.getString("nome_empresa"));
        } catch (Exception ignored) {
        }
        try {
            obj.getPactoPayConfigVO().setCodigo(rs.getInt("pactopayconfig"));
        } catch (Exception ignored) {
        }

        try {
            obj.setStatus(StatusPactoPayComunicacaoEnum.obterPorId(rs.getInt("status")));
        } catch (Exception ignored) {
        }
        try {
            obj.getTransacaoVO().setCodigo(rs.getInt("transacao"));
            obj.getPixVO().setCodigo(rs.getInt("pix"));
            obj.getBoletoVO().setCodigo(rs.getInt("boleto"));
            obj.getAutorizacaoCobrancaClienteVO().setCodigo(rs.getInt("autorizacaoCobrancaCliente"));
        } catch (Exception ignored) {
        }
        try {
            obj.setDesconto(rs.getDouble("desconto"));
        } catch (Exception ignored) {
        }
        return obj;
    }

    private PactoPayComunicacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PactoPayComunicacaoVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);
            return obj;
        }
        return obj;
    }

    private void montarDadosEmpresa(PactoPayComunicacaoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            obj.setEmpresaVO(new EmpresaVO());
            return;
        }

        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(con);
            obj.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), nivelMontarDados));
        } finally {
            empresaDAO = null;
        }
    }

    public void incluir(PactoPayComunicacaoVO obj) throws Exception {
        try {
            this.con.setAutoCommit(false);
            incluirSemCommit(obj);
            this.con.commit();
        } catch (Exception ex) {
            this.con.rollback();
            throw ex;
        } finally {
            this.con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(PactoPayComunicacaoVO obj) throws Exception {
        PactoPayComunicacaoVO.validarDados(obj);
        String sql = "INSERT INTO pactopaycomunicacao" +
                "(dataRegistro, empresa, pessoa, historicoContato, " +
                "meioEnvio, tipoEnvio, comunicacao, " +
                "sucesso, dataExecucao, resultado, " +
                "datalido, dataclicou, status, versao, " +
                "desconto, tags, pactopayconfig) " +
                "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getPessoaVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getHistoricoContatoVO().getCodigo());
            ps.setInt(++i, obj.getMeioEnvio() == null ? 0 : obj.getMeioEnvio().getCodigo());
            ps.setInt(++i, obj.getTipoEnvioPactoPay().getId());
            ps.setString(++i, obj.getComunicacao());
            ps.setBoolean(++i, obj.isSucesso());
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataExecucao()));
            ps.setString(++i, obj.getResultado());
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLido()));
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataClicou()));
            ps.setInt(++i, obj.getStatus().getId());
            ps.setInt(++i, 2); //versão 2 - Novas atualizacoes
            ps.setDouble(++i, obj.getDesconto());
            ps.setString(++i, obj.getTags());
            resolveIntegerNull(ps, ++i, obj.getPactoPayConfigVO().getCodigo());

            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        incluirPactoPayComunicacaoMovParcela(obj);
    }

    public void alterar(PactoPayComunicacaoVO obj) throws Exception {
        try {
            this.con.setAutoCommit(false);
            alterarSemCommit(obj);
            this.con.commit();
        } catch (Exception ex) {
            this.con.rollback();
            throw ex;
        } finally {
            this.con.setAutoCommit(true);
        }
    }

    public void gravarSemCommit(PactoPayComunicacaoVO obj) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getCodigo())) {
            this.incluirSemCommit(obj);
        } else {
            this.alterarSemCommit(obj);
        }
    }

    public void alterarSemCommit(PactoPayComunicacaoVO obj) throws Exception {
        PactoPayComunicacaoVO.validarDados(obj);
        String sql = "UPDATE pactopaycomunicacao SET " +
                "empresa = ?, pessoa = ?, historicoContato = ?, " +
                "meioEnvio = ?, tipoEnvio = ?, comunicacao = ?, " +
                "sucesso = ?, dataExecucao = ?, resultado = ?, " +
                "datalido = ?, dataclicou = ?, status = ?, " +
                "desconto = ?, tags = ?, pactopayconfig = ? " +
                "WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getPessoaVO().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getHistoricoContatoVO().getCodigo());
            ps.setInt(++i, obj.getMeioEnvio() == null ? 0 : obj.getMeioEnvio().getCodigo());
            ps.setInt(++i, obj.getTipoEnvioPactoPay().getId());
            ps.setString(++i, obj.getComunicacao());
            ps.setBoolean(++i, obj.isSucesso());
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataExecucao()));
            ps.setString(++i, obj.getResultado());
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataLido()));
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataClicou()));
            ps.setInt(++i, obj.getStatus().getId());
            ps.setDouble(++i, obj.getDesconto());
            ps.setString(++i, obj.getTags());
            resolveIntegerNull(ps, ++i, obj.getPactoPayConfigVO().getCodigo());

            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public PactoPayComunicacaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM pactopaycomunicacao WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, this.con);
                } else {
                    return null;
                }
            }
        }
    }

    public void alteraJobExcluido(boolean jobexcluido, Integer codigo) throws SQLException {
        try (PreparedStatement ps = con.prepareStatement("UPDATE pactopaycomunicacao SET jobexcluido = ? WHERE codigo = ?")) {
            int i = 0;
            ps.setBoolean(++i, jobexcluido);
            ps.setInt(++i, codigo);
            ps.execute();
        }
    }

    public void alterarLido(Date dataLido, Integer codigo, boolean sobrescrever, boolean controlaTransacao) throws Exception {
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            incluirPactoPayComunicacaoLog(codigo, "ALTERAR_DATA_LIDO", dataLido == null ? "" : Calendario.getDataAplicandoFormatacao(dataLido, "dd/MM/yyyy HH:mm:ss"));

            try (PreparedStatement ps = con.prepareStatement("UPDATE pactopaycomunicacao SET datalido = ? WHERE codigo = ? " + (sobrescrever ? "" : " and datalido is null"))) {
                int i = 0;
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(dataLido));
                ps.setInt(++i, codigo);
                ps.execute();
            }

            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (controlaTransacao) {
                con.rollback();
            }
            throw ex;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    public void alterarClicou(Date dataClicou, Integer codigo, boolean sobrescrever, boolean controlaTransacao) throws Exception {
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            incluirPactoPayComunicacaoLog(codigo, "ALTERAR_DATA_CLICOU", dataClicou == null ? "" : Calendario.getDataAplicandoFormatacao(dataClicou, "dd/MM/yyyy HH:mm:ss"));

            try (PreparedStatement ps = con.prepareStatement("UPDATE pactopaycomunicacao SET dataclicou = ? WHERE codigo = ? " + (sobrescrever ? "" : " and dataclicou is null"))) {
                int i = 0;
                ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(dataClicou));
                ps.setInt(++i, codigo);
                ps.execute();
            }

            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (controlaTransacao) {
                con.rollback();
            }
            throw ex;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    public void incluirPactoPayComunicacaoLog(Integer pactoPayComunicacao, String operacao, String dados) throws Exception {
        String sql = "INSERT INTO pactopaycomunicacaolog (dataregistro, pactopaycomunicacao, operacao, dados) VALUES(?,?,?,?);";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveIntegerNull(ps, ++i, pactoPayComunicacao);
            ps.setString(++i, operacao);
            ps.setString(++i, dados);

            ps.execute();
        }
    }

    public void incluirPactoPayEnvioEmailLog(Integer pactopayenvioemail, String operacao, String dados) throws Exception {
        String sql = "INSERT INTO pactopayenvioemaillog (dataregistro, pactopayenvioemail, operacao, dados) VALUES(?,?,?,?);";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveIntegerNull(ps, ++i, pactopayenvioemail);
            ps.setString(++i, operacao);
            ps.setString(++i, dados);
            ps.execute();
        }
    }

    public void incluirPactoPayLog(Integer pactoPayComunicacao, Integer pactoPayEnvioEmail, String operacao, String dados) throws Exception {
        String sql = "INSERT INTO pactopaylog(dataregistro, pactopaycomunicacao, pactoPayEnvioEmail, operacao, dados) VALUES(?,?,?,?,?);";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveIntegerNull(ps, ++i, pactoPayComunicacao);
            resolveIntegerNull(ps, ++i, pactoPayEnvioEmail);
            ps.setString(++i, operacao);
            ps.setString(++i, dados);
            ps.execute();
        }
    }

    public void alterarTransacao(Integer transacao,
                                 Integer codigoPactoPayComunicacao,
                                 boolean controlaTransacao) throws Exception {
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            incluirPactoPayComunicacaoLog(codigoPactoPayComunicacao, "ALTERAR_TRANSACAO", transacao == null ? "" : transacao.toString());

            try (PreparedStatement ps = con.prepareStatement("UPDATE pactopaycomunicacao SET transacao = ? WHERE codigo = ?;")) {
                int i = 0;
                resolveIntegerNull(ps, ++i, transacao);
                ps.setInt(++i, codigoPactoPayComunicacao);
                ps.execute();
            }

            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (controlaTransacao) {
                con.rollback();
            }
            throw ex;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    public void alterarPix(Integer pix,
                           Integer codigoPactoPayComunicacao,
                           boolean controlaTransacao) throws Exception {
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            incluirPactoPayComunicacaoLog(codigoPactoPayComunicacao, "ALTERAR_PIX", pix == null ? "" : pix.toString());

            try (PreparedStatement ps = con.prepareStatement("UPDATE pactopaycomunicacao SET pix = ? WHERE codigo = ?;")) {
                int i = 0;
                resolveIntegerNull(ps, ++i, pix);
                ps.setInt(++i, codigoPactoPayComunicacao);
                ps.execute();
            }

            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (controlaTransacao) {
                con.rollback();
            }
            throw ex;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    public void alterarBoleto(Integer boleto,
                              Integer codigoPactoPayComunicacao,
                              boolean controlaTransacao) throws Exception {
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            incluirPactoPayComunicacaoLog(codigoPactoPayComunicacao, "ALTERAR_BOLETO", boleto == null ? "" : boleto.toString());

            try (PreparedStatement ps = con.prepareStatement("UPDATE pactopaycomunicacao SET boleto = ? WHERE codigo = ?;")) {
                int i = 0;
                resolveIntegerNull(ps, ++i, boleto);
                ps.setInt(++i, codigoPactoPayComunicacao);
                ps.execute();
            }

            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (controlaTransacao) {
                con.rollback();
            }
            throw ex;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    public void alterarAutorizacaoCobrancaCliente(Integer autorizacaoCobrancaCliente,
                                                  Integer codigoPactoPayComunicacao,
                                                  boolean controlaTransacao) throws Exception {
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            incluirPactoPayComunicacaoLog(codigoPactoPayComunicacao, "ALTERAR_AUTORIZACAO_COBRANCA_CLIENTE", autorizacaoCobrancaCliente == null ? "" : autorizacaoCobrancaCliente.toString());

            try (PreparedStatement ps = con.prepareStatement("UPDATE pactopaycomunicacao SET autorizacaoCobrancaCliente = ? WHERE codigo = ?;")) {
                int i = 0;
                resolveIntegerNull(ps, ++i, autorizacaoCobrancaCliente);
                ps.setInt(++i, codigoPactoPayComunicacao);
                ps.execute();
            }

            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (controlaTransacao) {
                con.rollback();
            }
            throw ex;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    public List<PactoPayComunicacaoVO> consultar(Date dataInicialRegistro, Date dataFinalRegistro,
                                                 Date dataInicialExecucao, Date dataFinalExecucao,
                                                 Date dataInicialClicou, Date dataFinalClicou,
                                                 Date dataInicialLido, Date dataFinalLido,
                                                 int nivelMontarDados) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("p.*, \n");
        sql.append("pe.nome as nome_pessoa, \n");
        sql.append("e.nome as nome_empresa \n");
        sql.append("FROM pactopaycomunicacao p \n");
        sql.append("LEFT JOIN pessoa pe on pe.codigo = p.pessoa \n");
        sql.append("LEFT JOIN empresa e on e.codigo = p.empresa \n");
        sql.append("WHERE 1 = 1 \n");
        boolean filtrou = false;
        if (dataInicialRegistro != null && dataFinalRegistro != null) {
            sql.append("AND p.dataregistro::date between '").append(Uteis.getDataFormatoBD(dataInicialRegistro)).append("' and '").append(Uteis.getDataFormatoBD(dataFinalRegistro)).append("' \n");
            filtrou = true;
        }
        if (dataInicialExecucao != null && dataFinalExecucao != null) {
            sql.append("AND p.dataexecucao::date between '").append(Uteis.getDataFormatoBD(dataInicialExecucao)).append("' and '").append(Uteis.getDataFormatoBD(dataFinalExecucao)).append("' \n");
            filtrou = true;
        }
        if (dataInicialClicou != null && dataFinalClicou != null) {
            sql.append("AND p.dataclicou::date between '").append(Uteis.getDataFormatoBD(dataInicialClicou)).append("' and '").append(Uteis.getDataFormatoBD(dataFinalClicou)).append("' \n");
            filtrou = true;
        }
        if (dataInicialLido != null && dataFinalLido != null) {
            sql.append("AND p.datalido::date between '").append(Uteis.getDataFormatoBD(dataInicialLido)).append("' and '").append(Uteis.getDataFormatoBD(dataFinalLido)).append("' \n");
            filtrou = true;
        }
        sql.append("ORDER BY p.dataregistro desc \n");

        if (!filtrou) {
            throw new Exception("Informe algum filtro");
        }

        PreparedStatement statement = con.prepareStatement(sql.toString());
        ResultSet resultSet = statement.executeQuery();
        List<PactoPayComunicacaoVO> lista = new ArrayList<>();
        while (resultSet.next()) {
            try {
                lista.add(montarDados(resultSet, nivelMontarDados, this.con));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }

    private void incluirPactoPayComunicacaoMovParcela(PactoPayComunicacaoVO obj) {
        for (MovParcelaVO movParcelaVO : obj.getListaMovParcela()) {
            try {
                incluirPactoPayComunicacaoMovParcela(obj.getCodigo(), movParcelaVO.getCodigo());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private void incluirPactoPayComunicacaoMovParcela(Integer pactoPayComunicacao, Integer movParcela) throws Exception {
        String sql = "INSERT INTO pactopaycomunicacaomovparcela (pactopaycomunicacao, movparcela) VALUES(?,?);";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            resolveIntegerNull(ps, ++i, pactoPayComunicacao);
            resolveIntegerNull(ps, ++i, movParcela);
            ps.execute();
        }

    }

    public void alterarDesconto(Double desconto, Integer codigoPactoPayComunicacao,
                                boolean controlaTransacao) throws Exception {
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            if (desconto == null) {
                desconto = 0.0;
            }

            incluirPactoPayComunicacaoLog(codigoPactoPayComunicacao, "ALTERAR_DESCONTO", UteisValidacao.emptyNumber(desconto) ? "" : desconto.toString());

            try (PreparedStatement ps = con.prepareStatement("UPDATE pactopaycomunicacao SET desconto = ? WHERE codigo = ?;")) {
                int i = 0;
                ps.setDouble(++i, desconto);
                ps.setInt(++i, codigoPactoPayComunicacao);
                ps.execute();
            }

            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (controlaTransacao) {
                con.rollback();
            }
            throw ex;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    public void alterarTags(PactoPayComunicacaoVO obj) throws SQLException {
        try (PreparedStatement ps = con.prepareStatement("UPDATE pactopaycomunicacao SET tags = ? WHERE codigo = ?;")) {
            int i = 0;
            ps.setString(++i, obj.getTags());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public List<PactoPayComunicacaoVO> consultarPorPactoPayEnvioEmail(Integer pactopayenvioemail, int nivelMontarDados) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("c.*\n");
        sql.append("FROM pactopayenvioemailcomunicacao p\n");
        sql.append("INNER JOIN pactopaycomunicacao c on c.codigo = p.pactopaycomunicacao \n");
        sql.append("WHERE p.pactopayenvioemail = ").append(pactopayenvioemail);

        PreparedStatement statement = con.prepareStatement(sql.toString());
        ResultSet resultSet = statement.executeQuery();
        List<PactoPayComunicacaoVO> lista = new ArrayList<>();
        while (resultSet.next()) {
            try {
                lista.add(montarDados(resultSet, nivelMontarDados, this.con));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }
}
