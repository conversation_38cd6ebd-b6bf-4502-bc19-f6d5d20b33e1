package negocio.facade.jdbc.basico;

import negocio.comuns.basico.PerguntaClienteVO;

import java.sql.Connection;
import java.util.Iterator;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>QuestionarioPerguntaClienteVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>QuestionarioPerguntaClienteVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see QuestionarioPerguntaClienteVO
 * @see SuperEntidade
 * @see QuestionarioCliente
 */
public class QuestionarioPerguntaCliente extends SuperEntidade {    

    public QuestionarioPerguntaCliente() throws Exception {
        super();
        setIdEntidade("Cliente");
    }

    public QuestionarioPerguntaCliente(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Cliente");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>QuestionarioPerguntaClienteVO</code>.
     */
    public QuestionarioPerguntaClienteVO novo() throws Exception {
        incluir(getIdEntidade());
        return new QuestionarioPerguntaClienteVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>QuestionarioPerguntaClienteVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>QuestionarioPerguntaClienteVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(QuestionarioPerguntaClienteVO obj, Boolean validarQuestionario, boolean controlarAcesso) throws Exception {
        if (validarQuestionario) {
            QuestionarioPerguntaClienteVO.validarDados(obj);
        }
        if (controlarAcesso) {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO QuestionarioPerguntaCliente( questionarioCliente, perguntaCliente ) VALUES ( ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getQuestionarioCliente() != 0) {
            sqlInserir.setInt(1, obj.getQuestionarioCliente());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getPerguntaCliente().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getPerguntaCliente().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>QuestionarioPerguntaClienteVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>QuestionarioPerguntaClienteVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(QuestionarioPerguntaClienteVO obj, Boolean validarQuestionario) throws Exception {
        if (validarQuestionario) {
            QuestionarioPerguntaClienteVO.validarDados(obj);
        }
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE QuestionarioPerguntaCliente set questionarioCliente=?, perguntaCliente=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getQuestionarioCliente() != 0) {
            sqlAlterar.setInt(1, obj.getQuestionarioCliente());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getPerguntaCliente().getCodigo() != 0) {
            sqlAlterar.setInt(2, obj.getPerguntaCliente().getCodigo());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setInt(3, obj.getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>QuestionarioPerguntaClienteVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>QuestionarioPerguntaClienteVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(QuestionarioPerguntaClienteVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM QuestionarioPerguntaCliente WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioPerguntaCliente</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>PerguntaCliente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>QuestionarioPerguntaClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<QuestionarioPerguntaClienteVO> consultarPorDescricaoPerguntaCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT QuestionarioPerguntaCliente.* FROM QuestionarioPerguntaCliente, PerguntaCliente WHERE QuestionarioPerguntaCliente.perguntaCliente = PerguntaCliente.codigo and upper( PerguntaCliente.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY PerguntaCliente.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioPerguntaCliente</code> através do valor do atributo 
     * <code>codigo</code> da classe <code>QuestionarioCliente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>QuestionarioPerguntaClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoQuestionarioCliente(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT QuestionarioPerguntaCliente.* FROM QuestionarioPerguntaCliente, QuestionarioCliente WHERE QuestionarioPerguntaCliente.questionarioCliente = QuestionarioCliente.codigo and QuestionarioCliente.codigo >= " + valorConsulta.intValue() + " ORDER BY QuestionarioCliente.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    public List consultarPorCodigoQuestionarioClienteCRM(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM QuestionarioPerguntaCliente, QuestionarioCliente WHERE  QuestionarioCliente.codigo >= " + valorConsulta.intValue() + " ORDER BY QuestionarioCliente.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioPerguntaCliente</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>QuestionarioPerguntaClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM QuestionarioPerguntaCliente WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>QuestionarioPerguntaClienteVO</code> resultantes da consulta.
     */
    public static List<QuestionarioPerguntaClienteVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<QuestionarioPerguntaClienteVO> vetResultado = new ArrayList<QuestionarioPerguntaClienteVO>();
        while (tabelaResultado.next()) {
            QuestionarioPerguntaClienteVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>QuestionarioPerguntaClienteVO</code>.
     * @return  O objeto da classe <code>QuestionarioPerguntaClienteVO</code> com os dados devidamente montados.
     */
    public static QuestionarioPerguntaClienteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        QuestionarioPerguntaClienteVO obj = new QuestionarioPerguntaClienteVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setQuestionarioCliente(new Integer(dadosSQL.getInt("questionarioCliente")));
        obj.getPerguntaCliente().setCodigo(new Integer(dadosSQL.getInt("perguntaCliente")));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosPerguntaCliente(obj, Uteis.NIVELMONTARDADOS_TODOS);
        return obj;
    }

    public static QuestionarioPerguntaClienteVO montarDados(ResultSet dadosSQL, Connection con, int nivelMontarDados) throws Exception {
        QuestionarioPerguntaClienteVO obj = new QuestionarioPerguntaClienteVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setQuestionarioCliente(new Integer(dadosSQL.getInt("questionarioCliente")));
        obj.getPerguntaCliente().setCodigo(new Integer(dadosSQL.getInt("perguntaCliente")));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosPerguntaCliente(obj, con, Uteis.NIVELMONTARDADOS_TODOS);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>PerguntaClienteVO</code> relacionado ao objeto <code>QuestionarioPerguntaClienteVO</code>.
     * Faz uso da chave primária da classe <code>PerguntaClienteVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPerguntaCliente(QuestionarioPerguntaClienteVO obj, int nivelMontarDados) throws Exception {
        if (obj.getPerguntaCliente().getCodigo() == 0) {
            obj.setPerguntaCliente(new PerguntaClienteVO());
            return;
        }
        obj.setPerguntaCliente(getFacade().getPerguntaCliente().consultarPorChavePrimaria(obj.getPerguntaCliente().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosPerguntaCliente(QuestionarioPerguntaClienteVO obj, Connection con, int nivelMontarDados) throws Exception {
        if (obj.getPerguntaCliente().getCodigo() == 0) {
            obj.setPerguntaCliente(new PerguntaClienteVO());
            return;
        }
        PerguntaCliente perguntaClienteDAO = new PerguntaCliente(con);
        obj.setPerguntaCliente(perguntaClienteDAO.consultarPorChavePrimaria(obj.getPerguntaCliente().getCodigo(), nivelMontarDados));
        perguntaClienteDAO = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>QuestionarioPerguntaClienteVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>QuestionarioPerguntaCliente</code>.
     * @param questionarioCliente campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirQuestionarioPerguntaClientes(Integer questionarioCliente) throws Exception {
        // QuestionarioPerguntaCliente.excluir(getIdEntidade());
        String sql = "DELETE FROM QuestionarioPerguntaCliente WHERE (questionarioCliente = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, questionarioCliente);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>QuestionarioPerguntaClienteVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirQuestionarioPerguntaClientes</code> e <code>incluirQuestionarioPerguntaClientes</code> disponíveis na classe <code>QuestionarioPerguntaCliente</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarQuestionarioPerguntaClientes(Integer questionarioCliente, List objetos, Boolean validarQuestionario) throws Exception {
        String str = "DELETE FROM QuestionarioPerguntaCliente WHERE questionarioCliente = " + questionarioCliente;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO objeto = (QuestionarioPerguntaClienteVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            QuestionarioPerguntaClienteVO obj = (QuestionarioPerguntaClienteVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setQuestionarioCliente(questionarioCliente);
                incluir(obj, validarQuestionario, true);
            } else {
                alterar(obj, validarQuestionario);
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>QuestionarioPerguntaClienteVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.QuestionarioCliente</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirQuestionarioPerguntaClientes(Integer questionarioClientePrm, List objetos, Boolean validarQuestionario, boolean controlarAcesso) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            QuestionarioPerguntaClienteVO obj = (QuestionarioPerguntaClienteVO) e.next();
            obj.setQuestionarioCliente(questionarioClientePrm);
            incluir(obj, validarQuestionario, controlarAcesso);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>QuestionarioPerguntaClienteVO</code> relacionados a um objeto da classe <code>basico.QuestionarioCliente</code>.
     * @param questionarioCliente  Atributo de <code>basico.QuestionarioCliente</code> a ser utilizado para localizar os objetos da classe <code>QuestionarioPerguntaClienteVO</code>.
     * @return List  Contendo todos os objetos da classe <code>QuestionarioPerguntaClienteVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarQuestionarioPerguntaClientes(Integer questionarioCliente, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM QuestionarioPerguntaCliente WHERE questionarioCliente = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, questionarioCliente);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            QuestionarioPerguntaClienteVO novoObj = QuestionarioPerguntaCliente.montarDados(resultado, con, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>QuestionarioPerguntaClienteVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public QuestionarioPerguntaClienteVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM QuestionarioPerguntaCliente WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( QuestionarioPerguntaCliente ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
    
}
