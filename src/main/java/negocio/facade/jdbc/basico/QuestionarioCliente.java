package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.crm.Evento;
import negocio.interfaces.basico.QuestionarioClienteInterfaceFacade;
import relatorio.negocio.comuns.basico.ICVTO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>QuestionarioClienteVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>QuestionarioClienteVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see QuestionarioClienteVO
 * @see SuperEntidade
 */
public class QuestionarioCliente extends SuperEntidade implements QuestionarioClienteInterfaceFacade {

    private Hashtable questionarioPerguntaClientes;

    public QuestionarioCliente() throws Exception {
        super();
        setIdEntidade("Cliente");
        setQuestionarioPerguntaClientes(new Hashtable());
    }

    public QuestionarioCliente(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Cliente");
        setQuestionarioPerguntaClientes(new Hashtable());
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>QuestionarioClienteVO</code>.
     */
    public QuestionarioClienteVO novo() throws Exception {
        incluir(getIdEntidade());
        return new QuestionarioClienteVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>QuestionarioClienteVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>QuestionarioClienteVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(QuestionarioClienteVO obj, Boolean validarQuestionario) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemComit(obj, validarQuestionario, false);
            con.commit();
            ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
            zwFacade.atualizarSintetico(obj.getCliente(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, true);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @SuppressWarnings("unchecked")
    public void incluirSemComit(QuestionarioClienteVO obj, Boolean validarQuestionario, boolean processo) throws Exception {
        if (!obj.getQuestionario().isPesquisa()) {
            QuestionarioClienteVO.validarDadosConsultor(obj);
        }
        if (validarQuestionario) {
            QuestionarioClienteVO.validarDados(obj);
        }
        if (!obj.getQuestionario().isPesquisa()) {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        if (!obj.getQuestionario().isPesquisa()) {
            validarDuplicacaoExcluirQuestionario(obj);
        }
        String sql = "INSERT INTO QuestionarioCliente( questionario, cliente, consultor, data,observacao , evento, tipobv,ultimaAtualizacao,origemSistema) VALUES ( ?, ?, ?, ?, ?, ?,?,?,?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getQuestionario().getCodigo() != 0) {
            sqlInserir.setInt(1, obj.getQuestionario().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getCliente().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getCliente().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }
        if (obj.getConsultor().getCodigo() != 0) {
            sqlInserir.setInt(3, obj.getConsultor().getCodigo());
        } else {
            sqlInserir.setNull(3, 0);
        }
        sqlInserir.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getData()));
        sqlInserir.setString(5, obj.getObservacao());
        if (obj.getEventoVO().getCodigo() != 0) {
            sqlInserir.setInt(6, obj.getEventoVO().getCodigo());
        } else {
            sqlInserir.setNull(6, 0);
        }
        if (obj.getTipoBV() != null && obj.getTipoBV().getCodigo() != 0) {
            sqlInserir.setInt(7, obj.getTipoBV().getCodigo());
        } else {
            sqlInserir.setNull(7, 0);
        }
        sqlInserir.setTimestamp(8, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        if (obj.getOrigemSistemaEnum() != null){
            sqlInserir.setInt(9,obj.getOrigemSistemaEnum().getCodigo());
        }else{
            sqlInserir.setNull(9, Types.NULL);
        }
        sqlInserir.execute();
        verificarAgendamentosConsultor(obj);
        obj.setCodigo(obterValorChavePrimariaCodigo());
        if (!Calendario.igual(Calendario.hoje(), obj.getData())) {
            registrarLogAlteracaoDataBoletim(obj);
        }
        Iterator i = obj.getQuestionarioPerguntaClienteVOs().iterator();
        PerguntaCliente perguntaDao = new PerguntaCliente(con);
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO q = (QuestionarioPerguntaClienteVO) i.next();
            perguntaDao.incluirPerguntaCliente(q.getPerguntaCliente(), validarQuestionario, !obj.getQuestionario().isPesquisa());
        }
        perguntaDao = null;

        QuestionarioPerguntaCliente questDAO = new QuestionarioPerguntaCliente(con);
        questDAO.incluirQuestionarioPerguntaClientes(obj.getCodigo(), obj.getQuestionarioPerguntaClienteVOs(), validarQuestionario, !obj.getQuestionario().isPesquisa());
        questDAO = null;

        if(!processo){
            ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
            zwFacade.atualizarSintetico(obj.getCliente(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, true);
        }
        obj.setNovoObj(false);
    }

    public void incluirSemComit(QuestionarioClienteVO obj) throws Exception {
        incluirSemComit(obj,false,false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>QuestionarioClienteVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>QuestionarioClienteVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(QuestionarioClienteVO obj, Boolean validarQuestionario) throws Exception {
        try {
            con.setAutoCommit(false);
            QuestionarioClienteVO.validarDadosConsultor(obj);
            if (validarQuestionario) {
                QuestionarioClienteVO.validarDados(obj);
            }
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE QuestionarioCliente set questionario=?, cliente=?, consultor=?, data=?, observacao=? , evento=?, tipobv=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            if (obj.getQuestionario().getCodigo() != 0) {
                sqlAlterar.setInt(1, obj.getQuestionario().getCodigo());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getCliente().getCodigo() != 0) {
                sqlAlterar.setInt(2, obj.getCliente().getCodigo());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            if (obj.getConsultor().getCodigo() != 0) {
                sqlAlterar.setInt(3, obj.getConsultor().getCodigo());
            } else {
                sqlAlterar.setNull(3, 0);
            }
            sqlAlterar.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getData()));
            sqlAlterar.setString(5, obj.getObservacao());
            if (obj.getEventoVO().getCodigo() != 0) {
                sqlAlterar.setInt(6, obj.getEventoVO().getCodigo());
            } else {
                sqlAlterar.setNull(6, 0);
            }
            if (obj.getTipoBV() != null && obj.getTipoBV().getCodigo() != 0) {
                sqlAlterar.setInt(7, obj.getTipoBV().getCodigo());
            } else {
                sqlAlterar.setNull(7, 0);
            }
            sqlAlterar.setInt(8, obj.getCodigo());
            sqlAlterar.execute();

//          //INICIO - REGISTRANDO LOG DE MODIFICACOES
//          registrarLogObjetoVO(obj, obj.getCliente().getPessoa().getCodigo());
////          registrarLogObjetoVO(obj.getQuestionario(), obj.getCodigo().intValue(), "QUESTIONARIOCLIENTE", obj.getCliente().getPessoa().getCodigo());
//          //FIM - REGISTRANDO LOG DE MODIFICACOES

            Iterator i = obj.getQuestionarioPerguntaClienteVOs().iterator();
            while (i.hasNext()) {
                QuestionarioPerguntaClienteVO q = (QuestionarioPerguntaClienteVO) i.next();

                getFacade().getPerguntaCliente().alterarPeguntaCliente(q.getPerguntaCliente(), validarQuestionario);
            }
            getFacade().getQuestionarioPerguntaCliente().alterarQuestionarioPerguntaClientes(obj.getCodigo(), obj.getQuestionarioPerguntaClienteVOs(), validarQuestionario);
            Boolean existeQuestionarioCliente = (getFacade().getClienteMensagem().consultarClienteMensagemPorClienteQuestionarioCliente(obj.getCliente().getCodigo(), obj.getCodigo(), false));
            if (existeQuestionarioCliente) {
                getFacade().getClienteMensagem().excluirClienteMensagemPorClienteQuestionario(obj.getCliente().getCodigo(), obj.getCodigo());
            }
            verificarAgendamentosConsultor(obj);
            con.commit();
            getFacade().getZWFacade().atualizarSintetico(obj.getCliente(),
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, true);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void registrarLogAlteracaoDataBoletim(QuestionarioClienteVO questionario) throws Exception {
        try {
            LogVO obj = new LogVO();
            obj.setPessoa(questionario.getCliente().getPessoa().getCodigo());
            obj.setChavePrimaria(questionario.getCodigo().toString());
            obj.setNomeEntidade("BOLETIM_VISITA");
            obj.setNomeEntidadeDescricao("QuestionarioCliente");
            obj.setOperacao("ALTERAÇÃO DA DATA DO BOLETIM " + questionario.getCodigo());
            obj.setResponsavelAlteracao(questionario.getResponsavel().getNome());
            obj.setUserOAMD(questionario.getResponsavel().getUserOamd());
            obj.setNomeCampo("DATA");
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setValorCampoAnterior(Uteis.getData(Calendario.hoje()));
            obj.setValorCampoAlterado(Uteis.getData(questionario.getData()));
            getFacade().getLog().incluirSemCommit(obj);
        } catch (Exception e) {
            Uteis.logar(e, Questionario.class);
        }
    }

    /**
     * Verifica se a propriedade <code>QuestionarioClienteVO</code>
     * @param obj
     */
    private void verificarAgendamentosConsultor(QuestionarioClienteVO obj) throws  Exception{
        if(obj.getCodigoColaboradorAntesAlteracao() != null && obj.getConsultor() != null && !obj.getCodigoColaboradorAntesAlteracao().equals(obj.getConsultor().getCodigo())){
            UsuarioVO usuarioAntigo = getFacade().getUsuario().consultarPorColaborador(obj.getCodigoColaboradorAntesAlteracao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioNovo = getFacade().getUsuario().consultarPorColaborador(obj.getConsultor().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getFacade().getAgenda().alterarConsultorResponsavelAgenda(usuarioAntigo.getCodigo(), usuarioNovo.getCodigo(), obj.getCliente().getCodigo(), Calendario.hoje());
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>QuestionarioClienteVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>QuestionarioClienteVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterarSemCommit(QuestionarioClienteVO obj, Boolean validarQuestionario) throws Exception {
        QuestionarioClienteVO.validarDadosConsultor(obj);
        if (validarQuestionario) {
            QuestionarioClienteVO.validarDados(obj);
        }
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE QuestionarioCliente set questionario=?, cliente=?, consultor=?, data=? , evento=?, tipobv=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getQuestionario().getCodigo() != 0) {
            sqlAlterar.setInt(1, obj.getQuestionario().getCodigo());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getCliente().getCodigo() != 0) {
            sqlAlterar.setInt(2, obj.getCliente().getCodigo());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        if (obj.getConsultor().getCodigo() != 0) {
            sqlAlterar.setInt(3, obj.getConsultor().getCodigo());
        } else {
            sqlAlterar.setNull(3, 0);
        }
        sqlAlterar.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getData()));
        if (obj.getEventoVO().getCodigo() != 0) {
            sqlAlterar.setInt(5, obj.getEventoVO().getCodigo());
        } else {
            sqlAlterar.setNull(5, 0);
        }
        if (obj.getTipoBV() != null && obj.getTipoBV().getCodigo() != 0) {
            sqlAlterar.setInt(6, obj.getTipoBV().getCodigo());
        } else {
            sqlAlterar.setNull(6, 0);
        }
        sqlAlterar.setInt(7, obj.getCodigo());
        sqlAlterar.execute();
        verificarAgendamentosConsultor(obj);
        Iterator i = obj.getQuestionarioPerguntaClienteVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO q = (QuestionarioPerguntaClienteVO) i.next();
            getFacade().getPerguntaCliente().alterarPeguntaCliente(q.getPerguntaCliente(), validarQuestionario);
        }
        getFacade().getQuestionarioPerguntaCliente().alterarQuestionarioPerguntaClientes(obj.getCodigo(), obj.getQuestionarioPerguntaClienteVOs(), validarQuestionario);
        getFacade().getZWFacade().atualizarSintetico(obj.getCliente(),
                Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, true);
    }

    public void alterarSemCommit(QuestionarioClienteVO obj) throws Exception {
        alterarSemCommit(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>QuestionarioClienteVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>QuestionarioClienteVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(QuestionarioClienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemComit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>QuestionarioClienteVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>QuestionarioClienteVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluirSemComit(QuestionarioClienteVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM QuestionarioCliente WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
        Iterator i = obj.getQuestionarioPerguntaClienteVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO q = (QuestionarioPerguntaClienteVO) i.next();
            getFacade().getPerguntaCliente().excluirPerguntaCliente(q.getPerguntaCliente());
        }
        getFacade().getQuestionarioPerguntaCliente().excluirQuestionarioPerguntaClientes(obj.getCodigo());
    }
//    
//    update questionariocliente
//set cliente=null
//where questionariocliente.cliente=222

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioCliente</code> através do valor do atributo 
     * <code>Date data</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>QuestionarioClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorData(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT * FROM QuestionarioCliente WHERE ((data >= '" + Uteis.getDataJDBC(prmIni) + " 00:00:00')"
                    + " and (data <= '" + Uteis.getDataJDBC(prmFim) + " 23:59:59')) ORDER BY data";
        } else {
            sqlStr = "SELECT QuestionarioCliente.* FROM QuestionarioCliente, cliente WHERE ((data >= '" + Uteis.getDataJDBC(prmIni) + " 00:00:00')"
                    + " and (data <= '" + Uteis.getDataJDBC(prmFim) + " 23:59:59'))and QuestionarioCliente.cliente = cliente.codigo "
                    + "and cliente.empresa = " + empresa + " ORDER BY data";
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioCliente</code> através do valor do atributo
     * <code>Date data</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>QuestionarioClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public Integer consultaQuantidadeQuestionarioPorDataEmpresa(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (empresa == 0) {
            sqlStr = "SELECT COUNT(QuestionarioCliente.codigo) FROM QuestionarioCliente WHERE ((data >= '" + Uteis.getDataJDBC(prmIni) + " 00:00:00') and (data <= '" + Uteis.getDataJDBC(prmFim) + " 23:59:59'))";
        } else {
            sqlStr = "SELECT COUNT(QuestionarioCliente.codigo) FROM QuestionarioCliente, cliente WHERE ((data >= '" + Uteis.getDataJDBC(prmIni) + " 00:00:00') and (data <= '" + Uteis.getDataJDBC(prmFim) + " 23:59:59')) and QuestionarioCliente.cliente = cliente.codigo and cliente.empresa = " + empresa;
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        tabelaResultado.next();
        return (tabelaResultado.getInt(1));
    }

    public Integer contarQtdQuestionarioVisitantesPorDataEmpresa(Date dataInicial, Date dataFinal, String horarioInicial, String horarioFinal, Integer empresa) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("select count(distinct(bv.codigo)) from questionariocliente as bv ");
        sqlStr.append("inner join cliente on cliente.codigo = bv.cliente ");
        sqlStr.append("inner join pessoa on pessoa.codigo = cliente.pessoa ");
        sqlStr.append("left join contrato as con on con.pessoa= cliente.pessoa ");
        sqlStr.append("left join historicocontrato as hist on hist.contrato = con.codigo ");
        sqlStr.append("where  bv.data between '" + Uteis.getDataJDBC(dataInicial) + " " + horarioInicial + "' and  '" + Uteis.getDataJDBC(dataFinal) + " " + horarioFinal + "' ");
        sqlStr.append("and cliente.empresa = " + empresa);

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        tabelaResultado.next();
        return (tabelaResultado.getInt(1));
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioCliente</code> através do valor do atributo
     * <code>Date data</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>QuestionarioClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public Integer consultaQuantidadeQuestionarioPorDataEmpresaPorColaborador(Date prmIni,
                                                                              Date prmFim,
                                                                              String codigoColaboradores,
                                                                              Integer empresa,
                                                                              List<TipoBVEnum> listaTiposBV,
                                                                              boolean controlarAcesso) throws Exception {
        return consultaMapaQuestionarioPorDataEmpresaPorColaborador(prmIni, prmFim, codigoColaboradores, empresa, listaTiposBV, controlarAcesso, true, true).size();
    }


    public String adicionarFiltrosConsultaQuestionarioPorDataEmpresaPorColaborador(Date prmIni,
                                                                                   Date prmFim,
                                                                                   String codigoColaboradores,
                                                                                   Integer empresa,
                                                                                   List<TipoBVEnum> listaTiposBV,
                                                                                   boolean incluirBolsistas,
                                                                                   boolean desconsiderarGympass,
                                                                                   String listaOrigemSistema, String listaEvento) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" inner JOIN cliente ON cliente.codigo = questionariocliente.cliente");
        sql.append(" inner join pessoa on pessoa.codigo = cliente.pessoa");
        sql.append(" inner join questionario on questionario.codigo = questionariocliente.questionario");
        sql.append(" inner join colaborador on colaborador.codigo = questionariocliente.consultor");
        sql.append(" inner join pessoa as pescol on colaborador.pessoa = pescol.codigo");

        sql.append(" WHERE ((data >= '" + Uteis.getDataJDBC(prmIni) + " 00:00:00') and (data <= '" + Uteis.getDataJDBC(prmFim) + " 23:59:59')) ");
        if (empresa != null && empresa != 0) {
            sql.append(" and cliente.empresa = " + empresa);
        }
        if (!UteisValidacao.emptyString(listaOrigemSistema)){
            sql.append(" and questionariocliente.origemSistema in(" + listaOrigemSistema + ") \n");
        }
        if (!UteisValidacao.emptyString(listaEvento)){
            sql.append(" and questionariocliente.evento in(" + listaEvento + ") \n");
        }

        if(!incluirBolsistas){
            Date primeiroDiaMes = Uteis.obterPrimeiroDiaMes(prmFim);// para uma avaliação correta, a verificação de contratos de bolsa são do mês todo, pois a pessoa pode lançar o BV num dia e a fazer um contrato de bolsa em outro.
            Date ultimoDiaMes = Uteis.obterUltimoDiaMes(prmFim);
            //são feitas duas validações, se não existe contrato de bolsa lançado, e outra se existe contrato normal, pois podem haver contratos concomitantes nessas situações, fazendo com que o BV seja incluído.
            sql.append(" and (not exists(select codigo from contrato where pessoa = cliente.pessoa and  bolsa  and contrato.datalancamento::date between '"+ Uteis.getDataJDBC(primeiroDiaMes) +"' and '"+ Uteis.getDataJDBC(ultimoDiaMes) +"' and  situacaocontrato <> 'RN')");
            sql.append("  or exists (select codigo from contrato where pessoa = cliente.pessoa and  bolsa = 'f'  and contrato.datalancamento::date between '"+ Uteis.getDataJDBC(primeiroDiaMes) +"' and '"+ Uteis.getDataJDBC(ultimoDiaMes) +"' and  situacaocontrato <> 'RN'))");
        }

        if (!codigoColaboradores.isEmpty()) {
            sql.append(" " + codigoColaboradores);
        }

        if (desconsiderarGympass) {
            sql.append(" and cliente.gympassuniquetoken is null \n");
        }

        if (listaTiposBV != null && !listaTiposBV.isEmpty()) {
            sql.append(" and questionariocliente.tipobv in (");
        }

        int i = 0;
        for (TipoBVEnum tipoBV : listaTiposBV) {
            if (i == 0) {
                sql.append(tipoBV.getCodigo());
                i++;
            } else {
                sql.append(",").append(tipoBV.getCodigo());
            }
        }
        if (listaTiposBV != null && !listaTiposBV.isEmpty()) {
            sql.append(" )");
        }
        return sql.toString();
    }

    public List<QuestionarioClienteVO> consultaQuestionarioPorDataEmpresaPorColaborador(Date prmIni, Date prmFim, String codigoColaboradores, List<TipoBVEnum> listaTiposBVs,
                                                                                        Integer empresa, boolean controlarAcesso, int nivelMontarDados, boolean bolsa,
                                                                                        boolean desconsiderarGympass, String listaOrigemSistema, String listaEvento) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT cliente.codigo as cliente, cliente.matricula,cliente.situacao, pessoa.nome as nomecliente, ");
        sqlStr.append(" questionariocliente.data as dataresposta, questionario.nomeinterno as questionario, questionariocliente.tipobv, pescol.nome as consultor ");
        sqlStr.append(" from QuestionarioCliente ");
        sqlStr.append(adicionarFiltrosConsultaQuestionarioPorDataEmpresaPorColaborador(prmIni, prmFim, codigoColaboradores, empresa, listaTiposBVs, bolsa, desconsiderarGympass,listaOrigemSistema,listaEvento));
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return montarDadosConsultaEspecial(tabelaResultado);
    }

    public List<QuestionarioClienteVO> montarDadosConsultaEspecial(ResultSet tabelaResultado) throws SQLException {
        List<QuestionarioClienteVO> vetResultado = new ArrayList<QuestionarioClienteVO>();
        while (tabelaResultado.next()) {
            QuestionarioClienteVO obj = montarDadosEspecial(tabelaResultado);
            vetResultado.add(obj);
        }
        return vetResultado;

    }

    public QuestionarioClienteVO montarDadosEspecial(ResultSet tabelaResultado) throws SQLException {
        QuestionarioClienteVO quest = new QuestionarioClienteVO();
        quest.setCliente(new ClienteVO());
        quest.getCliente().setCodigo(tabelaResultado.getInt("cliente"));
        quest.getCliente().setSituacao(tabelaResultado.getString("situacao"));
        quest.getCliente().setMatricula(tabelaResultado.getString("matricula"));
        quest.getCliente().setPessoa(new PessoaVO());
        quest.getCliente().getPessoa().setNome(tabelaResultado.getString("nomecliente"));
        quest.setQuestionario(new QuestionarioVO());
        quest.getQuestionario().setTituloPesquisa(tabelaResultado.getString("questionario"));
        quest.setData(tabelaResultado.getTimestamp("dataresposta"));
        quest.setTipoBV(TipoBVEnum.getTipo(tabelaResultado.getInt("tipobv")));
        quest.setConsultor(new ColaboradorVO());
        quest.getConsultor().setPessoa(new PessoaVO());
        quest.getConsultor().getPessoa().setNome(tabelaResultado.getString("consultor"));
        return quest;
    }

    public List consultarPorDataECliente(Date prmIni, Date prmFim, Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT QuestionarioCliente.* FROM QuestionarioCliente, Cliente WHERE QuestionarioCliente.cliente = Cliente.codigo   and Cliente.codigo = " + valorConsulta + " and ((data >= '" + Uteis.getDataJDBC(prmIni) + " 00:00:00') and (data <= '" + Uteis.getDataJDBC(prmFim) + " 23:59:59')) ORDER BY data";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioCliente</code> através do valor do atributo
     * <code>situacao</code> da classe <code>Colaborador</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>QuestionarioClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT QuestionarioCliente.* FROM QuestionarioCliente, Colaborador WHERE QuestionarioCliente.consultor = Colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioCliente</code> através do valor do atributo
     * <code>matricula</code> da classe <code>Cliente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>QuestionarioClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorMatriculaCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT QuestionarioCliente.* FROM QuestionarioCliente, Cliente WHERE QuestionarioCliente.cliente = Cliente.codigo and upper( Cliente.matricula ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cliente.matricula";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioCliente</code> através do valor do atributo
     * <code>matricula</code> da classe <code>Cliente</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>QuestionarioClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoCliente(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sql = "SELECT QuestionarioCliente.* FROM QuestionarioCliente, Cliente WHERE QuestionarioCliente.cliente = Cliente.codigo   and Cliente.codigo = " + valorConsulta + " ORDER BY QuestionarioCliente.data ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    public List consultarPorCodigoClienteTipoQuestionario(Integer valorConsulta, String tipoQuestionario, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sql = "SELECT *\n" +
                "FROM questionariocliente qc\n" +
                "  INNER JOIN questionario q\n" +
                "    ON qc.questionario = q.codigo AND q.tipoquestionario = '" + tipoQuestionario + "'\n" +
                "WHERE qc.cliente = " + valorConsulta + "\n" +
                "ORDER BY qc.data;";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    public QuestionarioClienteVO consultarUltimoBVCliente(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        String sql = "select * from QuestionarioCliente "
                + "where (cliente = ?) "
                + "order by codigo DESC "
                + "limit 1";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, valorConsulta);
        ResultSet resultTabela = stm.executeQuery();
        if (!resultTabela.next()) {
            return new QuestionarioClienteVO();
        }
        return montarDados(resultTabela, nivelMontarDados);
    }

    public ResultSet consultarPorCodigoClienteResultset(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sql = "SELECT QuestionarioCliente.* FROM QuestionarioCliente, Cliente WHERE QuestionarioCliente.cliente = Cliente.codigo   and Cliente.codigo = " + valorConsulta + " ORDER BY Cliente.codigo";
        PreparedStatement stm = con.prepareStatement(sql);
        return stm.executeQuery();
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioCliente</code> através do valor do atributo
     * <code>descricao</code> da classe <code>Questionario</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>QuestionarioClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoQuestionario(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT QuestionarioCliente.* FROM QuestionarioCliente, Questionario WHERE QuestionarioCliente.questionario = Questionario.codigo and upper( Questionario.nomeinterno ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Questionario.nomeinterno";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>QuestionarioCliente</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>QuestionarioClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM QuestionarioCliente WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>QuestionarioClienteVO</code> resultantes da consulta.
     */
    public List<QuestionarioClienteVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<QuestionarioClienteVO> vetResultado = new ArrayList<QuestionarioClienteVO>();
        while (tabelaResultado.next()) {
            QuestionarioClienteVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>QuestionarioClienteVO</code>.
     * @return  O objeto da classe <code>QuestionarioClienteVO</code> com os dados devidamente montados.
     */
    public QuestionarioClienteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        QuestionarioClienteVO obj = new QuestionarioClienteVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getQuestionario().setCodigo(new Integer(dadosSQL.getInt("questionario")));
        obj.getCliente().setCodigo(new Integer(dadosSQL.getInt("cliente")));
        obj.getConsultor().setCodigo(new Integer(dadosSQL.getInt("consultor")));
        obj.setData(dadosSQL.getTimestamp("data"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        Evento eventoDAO = new Evento(con);
        obj.setEventoVO(eventoDAO.consultarPorCodigo(dadosSQL.getInt("evento"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        obj.setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(dadosSQL.getInt("origemSistema")));
        eventoDAO = null;
        obj.setTipoBV(TipoBVEnum.getTipo(dadosSQL.getInt("tipobv")));
        obj.setUltimaAtualizacao(dadosSQL.getDate("ultimaAtualizacao"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        QuestionarioPerguntaCliente questionarioPerguntaClienteDAO = new QuestionarioPerguntaCliente(con);
        obj.setQuestionarioPerguntaClienteVOs(questionarioPerguntaClienteDAO.consultarQuestionarioPerguntaClientes(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        questionarioPerguntaClienteDAO = null;
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_INDICERENOVACAO) {
            montarDadosConsultor(obj, con, Uteis.NIVELMONTARDADOS_TODOS);
            return obj;
        }
        montarDadosQuestionario(obj, con, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosCliente(obj, con, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosConsultor(obj, con, Uteis.NIVELMONTARDADOS_TODOS);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ColaboradorVO</code> relacionado ao objeto <code>QuestionarioClienteVO</code>.
     * Faz uso da chave primária da classe <code>ColaboradorVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosConsultor(QuestionarioClienteVO obj, Connection con, int nivelMontarDados) throws Exception {
        if (obj.getConsultor().getCodigo() == 0) {
            obj.setConsultor(new ColaboradorVO());
            return;
        }
        Colaborador colaboradorDAO = new Colaborador(con);
        obj.setConsultor(colaboradorDAO.consultarPorChavePrimaria(obj.getConsultor().getCodigo(), nivelMontarDados));
        colaboradorDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ClienteVO</code> relacionado ao objeto <code>QuestionarioClienteVO</code>.
     * Faz uso da chave primária da classe <code>ClienteVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCliente(QuestionarioClienteVO obj, Connection con, int nivelMontarDados) throws Exception {
        if (obj.getCliente().getCodigo() == 0) {
            obj.setCliente(new ClienteVO());
            return;
        }
        Cliente clienteDAO = new Cliente(con);
        obj.setCliente(clienteDAO.consultarPorChavePrimaria(obj.getCliente().getCodigo(), nivelMontarDados));
        clienteDAO = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>QuestionarioVO</code> relacionado ao objeto <code>QuestionarioClienteVO</code>.
     * Faz uso da chave primária da classe <code>QuestionarioVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosQuestionario(QuestionarioClienteVO obj, Connection con, int nivelMontarDados) throws Exception {
        if (obj.getQuestionario().getCodigo() == 0) {
            obj.setQuestionario(new QuestionarioVO());
            return;
        }
        Questionario questionarioDAO = new Questionario(con);
        obj.setQuestionario(questionarioDAO.consultarPorChavePrimaria(obj.getQuestionario().getCodigo(), nivelMontarDados));
        questionarioDAO = null;
    }

    /**
     * Operação responsável por adicionar um objeto da <code>QuestionarioPerguntaClienteVO</code> no Hashtable <code>QuestionarioPerguntaClientes</code>.
     * Neste Hashtable são mantidos todos os objetos de QuestionarioPerguntaCliente de uma determinada QuestionarioCliente.
     * @param obj  Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjQuestionarioPerguntaClientes(QuestionarioPerguntaClienteVO obj) throws Exception {
        getQuestionarioPerguntaClientes().put(obj.getPerguntaCliente().getCodigo() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>QuestionarioPerguntaClienteVO</code> do Hashtable <code>QuestionarioPerguntaClientes</code>.
     * Neste Hashtable são mantidos todos os objetos de QuestionarioPerguntaCliente de uma determinada QuestionarioCliente.
     * @param PerguntaCliente Atributo da classe <code>QuestionarioPerguntaClienteVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjQuestionarioPerguntaClientes(Integer PerguntaCliente) throws Exception {
        getQuestionarioPerguntaClientes().remove(PerguntaCliente + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>QuestionarioClienteVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public QuestionarioClienteVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM QuestionarioCliente WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( QuestionarioCliente ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public QuestionarioClienteVO consultarPorCodigoQuestionario(QuestionarioClienteVO obj, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM QuestionarioCliente WHERE questionario = ? and cliente = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, obj.getQuestionario().getCodigo());
        sqlConsultar.setInt(2, obj.getCliente().getCodigo());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public Hashtable getQuestionarioPerguntaClientes() {
        return (questionarioPerguntaClientes);
    }

    public void setQuestionarioPerguntaClientes(Hashtable questionarioPerguntaClientes) {
        this.questionarioPerguntaClientes = questionarioPerguntaClientes;
    }
    
    @Override
    public Map<Integer, ICVTO> gerarICVResumido(Integer empresa, Date data) throws Exception {

        Date prmIni = Uteis.obterPrimeiroDiaMes(data);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT questionariocliente.consultor, pescol.nome as nomecol, COUNT(questionariocliente.codigo) as nr, 'BV' as tipo FROM QuestionarioCliente  \n");
        sql.append(" inner JOIN cliente ON cliente.codigo = questionariocliente.cliente  \n");
        sql.append(" inner join pessoa on pessoa.codigo = cliente.pessoa  \n");
        sql.append(" inner join questionario on questionario.codigo = questionariocliente.questionario  \n");
        sql.append(" inner join colaborador on colaborador.codigo = questionariocliente.consultor  \n");
        sql.append(" inner join pessoa as pescol on colaborador.pessoa = pescol.codigo  \n");
        sql.append(" WHERE ((data >= '").append(Uteis.getDataJDBC(prmIni)).append(" 00:00:00') and (data <= '");
        sql.append(Uteis.getDataJDBC(data)).append(" 23:59:59'))  and cliente.empresa = ").append(empresa);
        sql.append(" GROUP BY questionariocliente.consultor, pescol.nome  \n");
        sql.append(" UNION ALL \n");
        sql.append(" SELECT contrato.consultor, pescol.nome as nomecol, COUNT(contrato.pessoa) as nr, situacaoContrato as tipo FROM cliente   \n");
        sql.append(" INNER JOIN contrato ON contrato.pessoa = cliente.pessoa  \n");
        sql.append(" inner join pessoa on pessoa.codigo = cliente.pessoa  \n");
        sql.append(" inner join colaborador on colaborador.codigo = contrato.consultor   \n");
        sql.append(" INNER JOIN contratoDuracao ON contrato.codigo = contratoDuracao.contrato   \n");
        sql.append(" inner join pessoa as pescol on colaborador.pessoa = pescol.codigo   \n");
        sql.append(" WHERE contrato.dataLancamento >= '").append(Uteis.getDataJDBC(prmIni)).append(" 00:00:00'  \n");
        sql.append(" and  contrato.dataLancamento <= '").append(Uteis.getDataJDBC(data)).append(" 23:59:59'  \n");
        sql.append(" and situacaoContrato IN('MA', 'RE') and contrato.empresa =").append(empresa).append("  and contrato.origemcontrato in (2,1 ) \n");
        sql.append(" GROUP BY contrato.consultor, situacaoContrato, pescol.nome ");
        
        ResultSet rs = criarConsulta(sql.toString(), con);
        Map<Integer, ICVTO> mapa = new HashMap<Integer, ICVTO>();
        while(rs.next()){
            ICVTO obj = mapa.get(rs.getInt("consultor"));
            if(obj == null){
                obj = new ICVTO();
                obj.setNomeCol(rs.getString("nomecol"));
                obj.setConsultor(rs.getInt("consultor"));
                mapa.put(rs.getInt("consultor"), obj);
            }
            if(rs.getString("tipo").equals("BV")){
                obj.setBv(rs.getInt("nr"));
            }else if(rs.getString("tipo").equals("MA")){
                obj.setMa(rs.getInt("nr"));
            }else if(rs.getString("tipo").equals("RE")){
                obj.setRe(rs.getInt("nr"));    
            }
        }
        return mapa;
    }

    private void validarDuplicacaoExcluirQuestionario(QuestionarioClienteVO obj) throws Exception {
        List<QuestionarioClienteVO> questionarios = consultarPorDataECliente(obj.getData(), obj.getData(), obj.getCliente().getCodigo(),false, Uteis.NIVELMONTARDADOS_MINIMOS);
        for(QuestionarioClienteVO ques: questionarios){
            if(ques.getTipoBV().equals(obj.getTipoBV())){
                ClienteMensagem clienteMensagem = new ClienteMensagem(con);
                clienteMensagem.excluirClienteMensagemPorClienteQuestionario(obj.getCliente().getCodigo(), ques.getCodigo());
                excluirSemComit(ques);
            }
        }
    }
    
    
    /**
     * Responsável por realizar uma consulta de <code>QuestionarioCliente</code> através do valor do atributo
     * <code>Date data</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>QuestionarioClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public Map<Integer,Integer> consultaMapaQuestionarioPorDataEmpresaPorColaborador(Date prmIni,
                                                                                     Date prmFim,
                                                                                     String codigoColaboradores,
                                                                                     Integer empresa,
                                                                                     List<TipoBVEnum> listaTiposBV,
                                                                                     boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        Map<Integer,Integer> mapaQuestionarios = new HashMap<Integer, Integer>();
        String sqlStr = "SELECT questionariocliente.codigo,questionariocliente.cliente,questionariocliente.tipobv  FROM QuestionarioCliente ";
        sqlStr += adicionarFiltrosConsultaQuestionarioPorDataEmpresaPorColaborador(prmIni, prmFim, codigoColaboradores, empresa, listaTiposBV, true, true,OrigemSistemaEnum.ZW.getCodigo().toString(),null);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        while(tabelaResultado.next()){
            mapaQuestionarios.put(tabelaResultado.getInt("cliente"), tabelaResultado.getInt("tipobv"));
        }
        return mapaQuestionarios;
    }

    public Map<Integer,Integer> consultaMapaQuestionarioPorDataEmpresaPorColaborador(Date prmIni,
                                                                                     Date prmFim,
                                                                                     String codigoColaboradores,
                                                                                     Integer empresa,
                                                                                     List<TipoBVEnum> listaTiposBV,
                                                                                     boolean controlarAcesso,
                                                                                     boolean incluirBolsistas,
                                                                                     boolean desconsiderarGympass) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        Map<Integer,Integer> mapaQuestionarios = new HashMap<Integer, Integer>();
        String sqlStr = "SELECT questionariocliente.codigo,questionariocliente.cliente,questionariocliente.tipobv  FROM QuestionarioCliente ";
        sqlStr += adicionarFiltrosConsultaQuestionarioPorDataEmpresaPorColaborador(prmIni, prmFim, codigoColaboradores, empresa, listaTiposBV, incluirBolsistas, desconsiderarGympass,OrigemSistemaEnum.ZW.getCodigo().toString(),null);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        while(tabelaResultado.next()){
            mapaQuestionarios.put(tabelaResultado.getInt("cliente"), tabelaResultado.getInt("tipobv"));
        }
        return mapaQuestionarios;
    }

    public List consultarPorClienteQuestionario(Integer cliente, Integer questionario, int nivelMontarDados) throws Exception {
        String sql = "select * from questionariocliente where cliente = "+cliente+" and questionario = " + questionario;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }
}
