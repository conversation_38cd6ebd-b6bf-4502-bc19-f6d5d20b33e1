package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.enumeradores.TemporalRemocaoPontoEnum;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.enumeradores.TipoVigenciaMyWellnessGymPassEnum;
import br.com.pactosolucoes.enumeradores.UsoArredondamentoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.EmpresaControle;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.PessoaAnexoEnum;
import negocio.comuns.basico.enumerador.TipoEmpresaFinanceiro;
import negocio.comuns.crm.ConfiguracaoEmpresaBitrixVO;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.acesso.Coletor;
import negocio.facade.jdbc.acesso.LocalAcesso;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.MemCachedManager;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.oamd.CustomerSuccessTO;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import org.postgresql.util.PSQLException;
import servicos.adm.CreditoDCCService;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>EmpresaVO</code>. Responsável por implementar operações como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>EmpresaVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see EmpresaVO
 * @see SuperEntidade
 * @see SuperEmpresaVO
 */
public class Empresa extends SuperEntidade implements EmpresaInterfaceFacade {

    private static String URL_RELOAD_DISCOVERY = "https://discovery.ms.pactosolucoes.com.br/reload";
    private static String URL_RELOAD_LOGIN = "https://app.pactosolucoes.com.br/login/prest/config/reload";
    private static String URL_RELOAD_MARKETING = "https://ms1.pactosolucoes.com.br/marketing-ms/v1/campanha/reset";

    public Empresa() throws Exception {
        super();
    }

    public Empresa(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>EmpresaVO</code>.
     */
    public EmpresaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new EmpresaVO();
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.EmpresaInterfaceFacade#alterar(negocio.comuns.basico.EmpresaVO)
     */
    public void incluir(EmpresaVO obj) throws Exception {
        this.incluir(obj, false);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>EmpresaVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>EmpresaVO</code> que será gravado no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(EmpresaVO obj, boolean centralEventos) throws Exception {
        EmpresaVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        zeroFillByteArrays(obj);
        String sql = "INSERT INTO public.Empresa( nome, razaoSocial, endereco, setor, numero, complemento, "
                + "cidade, estado , pais, CEP, CNPJ, inscEstadual,telComercial1, telComercial2, telComercial3, "
                + "email, site, fax,questionarioPrimeiraVisita, questionarioRetorno, questionarioReMatricula,"
                + " permiteSituacaoAtestadoContrato, permiteContratosConcomintante, juroParcela, multa ,"
                + "nrDiasVigenteQuestionarioVisita, nrDiasVigenteQuestionarioRetorno,nrDiasVigenteQuestionarioRematricula, "
                + "mascaraMatricula, carenciaRenovacao, foto, fotoRelatorio, alturaFotoEmpresa, larguraFotoEmpresa, "
                + "alturaFotoRelatorio, larguraFotoRelatorio,nrDiasAvencer,toleranciaPagamento, qtdFaltaPeso1, "
                + "qtdFaltaInicioPeso2, qtdFaltaTerminoPeso2, qtdFaltaPeso3, somadv, "
                + "carencia, nrDiasProrata, fotoEmail, alturaFotoEmail, larguraFotoEmail, toleranciaDiasContratoVencido,timeZoneDefault,"
                + "urlRecorrencia, nrdiascompensacao, serviceUsuario, serviceSenha, toleranciaocupacaoturma, bloquearacessoseparcelaaberta, bloquearacessosemassinaturadigital, bloquearAcessoCrefVencido,"
                + "consultorvendaavulsa, permiteHorariosConcorrentesParaProfessor, professorEmAmbientesDiferentesMesmoHorario, mostrarcnpj,"
                + "chavenfse, usarnfse, tokensms, nrdiaschequeavista,"
                + "mostrarModalidade, bvobrigatorio, tempoAposFaltaReposicao, enviarNFSeAutomatico, comissaoMatriculaRematricula,"
                + "questionarioPrimeiraCompra, nrDiasVigenteQuestionarioPrimeiraCompra, questionarioRetornoCompra,"
                + "nrDiasVigenteQuestionarioRetornoCompra, nfseporpagamento, qtdDiasCobrarRematricula,"
                + "qtdvias, quebrarpaginarecibo, detalharperiodoproduto, detalharparcelas, detalharpagamentos, "
                + "detalhardescontos, apresentarassinaturas,observacaorecibo, tipogestaonfse, retrocederValorMensalPlanoCancelamento, "
                + "fecharNegociacaoSemAutorizacaoDCC, nrdiasdesistenteremovervinculotreino, removervinculosaposdesistencia, "
                + "liberarpersonalcomtaxaemaberto, reciboparaimpressoratermica, usarManutencaoModalidadeComissao, devolucaoEntraNoCaixa, "
                + "fotoredesocial,alturaFotoredesocial, larguraFotoredesocial, arredondamento, toleranciaprorata,"
                + "usarGestaoCreditosPersonal, permitereposicaoemturmasdiferentes, forcarMinimoVencimento2parcela, "
                + "permiterenovarcontratosemturmaslotadas, homeBackground640x551, homeBackground320x276,"
                + "alturahomeBackground640x551, largurahomeBackground640x551,alturahomeBackground320x276, largurahomeBackground320x276,"
                + "latitude, longitude, acessoChamada, localAcessoChamada, coletorChamada, diasrenovacaoautomaticaantecipada, "
                + "mostrarMensagemValoresRodape, cobrarAutomaticamenteMultaJuros, multaCobrancaAutomatica, "
                + "jurosCobrancaAutomatica, liberarPersonalProfessorDebito, consultorSite, criarBvVendaSite, ativa, emiteNFSEPorDataCompensacao, "
                + "pagarComissaoSeAtingirMetaFinanceira, pagarComissaoManutencaoModalidade,somenteVendaProdutosComEstoque, permiteAlterarDataEmissaoNFSe, "
                + "minutosAposUltimoAcessoDiminuirCredito, modeloMensagemVendasOnline, cancelamentoObrigatoriedadePagamento, cancelamentoAntecipado, cancelamentoAntecipadoDias, cancelamentoAntecipadoMulta, "
                + "cancelamentoAntecipadoPlanos, cancelamentoAntecipadoPlanosMulta, cancelamentoAntecipadoPlanosData, enviarEmailCancelamento, permitecontratopospagorenovacaoauto, "
                + "tipoCobrancaPacto, gerarCobrancaAutomaticaPacto, dtUltimaCobrancaPacto, qtdDiasFechamentoCobrancaPacto, valorCreditoPacto, gerarNotaFiscalCobrancaPacto, "
                + "qtdParcelasCobrancaPacto, qtdCreditoRenovarPrePagoCobrancaPacto, "
                + "qtdDiasParaLiberacaoDeVagaEmTrancamento, tentativasliberarparcelavencida, propagandaBoleto, alturaPropagandaBoleto, larguraPropagandaBoleto,\n"
                + "qtdDiasVencimentoBoleto, usarDataInicioDeContratoNoBI_ICV, existemNovosBoletosPacto, tipoParcelasCobrarVendaSite,convenioBoletoPadrao, gerarLoginAPIAoIncluirContrato, "
                + "modeloMensagemEsqueciMinhaSenhaVendasOnline, tipoParcelaCancelamento, quantidadeParcelasSeguidasCancelamento, enviarNotaCidadeEmpresa, permitirmaillinggerarautorizacaocobrancaboleto, "
                + "gerarNFSeContaCorrente, inscMunicipal, sequencialLoteRPS, permiteGerarArquivoLoteRPS, permiteGerarNotaManual, impedirVendaContratoPorConflitoReposicao, usarNFCe, "
                + "utilizarNomeResponsavelNoBoleto, gerarquitacaocancelamentoauto, gerarquitacaocancelamentoproporcional, pagarcomissaoproduto,"
                + "alterarDataHoraCheckGestaoPersonal,senhaAcessoOnzeDigitos,naorenovarcontratosemindicefinanceiro,codigoGymPass,tokenApiGymPass, gerarRemessaContratoCancelado,habilitarSomaDeAulaNaoVigente, codigoChaveIntegracaoDigitais, "
                + "definirCpfComoSenhaCatraca,mostrarnotapordiacompetencia,pontosalunoacesso,pontosalunoacessochuva,pontosalunoacessofrio,pontosalunoacessocalor,trabalharComPontuacao, retirarEdicaoPagamento,adicionarAulasDesmarcadasContratoAnterior, "
                + "tipoParcelaCancelamentoForaRegimeRecorrencia, quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia, quantidadeDiasUteisAposVencimentoParaCancelarContrato, limiteInicialItensBIPendencia,"
                + "tempoSaidaAcademia,tokensmsshortcode, permiteRenovarContratoViaAPP, valorMensalEmitirNFSe, habilitarReenvioAutomaticoRemessa, emitirNotaSomenteRecorrencia, "
                + "emitirNomeAlunoNotaFamilia, cancelamentoAntecipadoContratosDepoisDe, irTelaPagamentoCartaoCreditoRecorrente, enviarNFCeAutomatico, emitirNFCeSomenteRecorrencia, usaintegracoescrm, "
                + "tokenbuzzlead, urlLinkSiteCadastro, usarDataOriginalCompensacaoNFSe, utilizaSistemaEstacionamento, permitirLancarVariasParcelasSaldoDevedor, "
                + "produtoEmissaoNFCeFinanceiro, produtoEmissaoNFSeFinanceiro, qtdExecucoesRetentativa, usarNFCePorPagamento, consultarDiasAnterioresNFSe, consultarDiasAnterioresNFCe, "
                + "notasAutoPgRetroativo, validarVencimentoCartaoAutorizacao, permitirEstornarContratoComParcelasPG,permMarcarAulaFeriado, horaAberturaFeriado, horaFechamentoFeriado,"
                + "formasPagamentoNFCe, formasPagamentoNFSe, considerarSomenteParcelasPlanos, cobrarMultaJurosTransacao, cobrarMultaJurosDCO, cobrarMultaJurosDCC, addAutoClienteTreinoWeb, usarParceiroFidelidade, "
                + "envioNotificacaoNotasNFSe, envioNotificacaoNotasNFCe, emailsNotificacaoAutomaticaNotas, emailNotificacaoVendasOnline, validarCertificado, bloquearAcessoArmarioVigenciaVencida, utilizarJurosValorAbsoluto, concessao_dia_extra, "
                + "email_movidesk, sincronizado_movidesk, usarConciliadora, empresaConciliadora, senhaConciliadora, diasParaRetirarRelFechamentoDeCaixa, "
                + "moeda, locale, configuracaoNotaFiscalNFSe, configuracaoNotaFiscalNFCe, usarNomeResponsavelNFCe, tipoProdutoEmissaoNFSe, tipoProdutoEmissaoNFCe, descmoeda, naocobrarmultadecontratorenovado, naocobrarmultadetodasparcelaspagas,"
                + "minCreditarProximoPontoClubeVantagens, apenasPrimeiroAcessoClubeVantagens, zerarPontosAposVencimento, pontuarApenasCampanhasAtivas, aplicarIndicacaoQlqrPlano, "
                + "tiposProduto, emitirDuplicataNFSe,utilizardatacancelamentovalidarparcela, emitirMesReferenciaNFCe, integracaoSpiviHabilitada, integracaoSpiviSourceName, integracaoSpiviSiteID, integracaoSpiviPassword, cod_empresafinanceiro,"
                + "mostrarValoresZeradosRel,zerarValorCancelamentoTransferencia,cobrarCreditoVindi, qtddiaslimitecobrancaparcelasrecorrencia, "
                + "qtddiasrepetircobrancaparcelasrecorrencia, tentativaUnicaDeCobranca,mostrarDescricaoParcelaRenegociada, acessoSomenteComAgendamento, capacidadeSimultanea, utilizaLeitorCodigoBarras,nomeusuarioamigofit,senhausuarioamigofit,"
                + "integracaoMyWellnessUser,integracaoMyWellnessPassword,integracaMyWellneApiKey, integracaoMyWellneHabilitada, integracaoMyWellnessFacilityUrl, "
                + "agruparParcelasPorCartao, agruparParcelasPorCartaoValorLimite, somenteUmEnvioCartaoTentativa, integracaoMyWellnessEnviarVinculos, integracaoMyWellnessEnviarGrupos, diaVencimentoCobrancaPacto, valorLimiteCaixaAbertoVendaAvulsa,\n"
                + "detalharNiveisModalidades,notificarWebhook, urlWebhookNotificar, "
                + "integracaoMentorWebHabilitada, integracaoMentorWebUrl, integracaoMentorWebServico, integracaoMentorWebUser, integracaoMentorWebPassword, integracaoF360Dir, \n"
                + "integracaoF360RelFatHabilitada, integracaoF360FtpServer, integracaoF360FtpPort, integracaoF360User, integracaoF360Password, integracaoF360Quinzenal, \n"
                + "tipovigenciamywellnessgympass , nrdiasvigenciamywellnessgympass, \n"
                + "integracaoAmigoFitHabilitada,permiteCadastrarCartaoMesmoAssim, convenioVerificacaoCartao, transferida, novachave_transferencia, novocodigo_transferencia, utilizarMultaValorAbsoluto, permiteMaillingCriarBoleto, \n"
                + "convenioCobrancaPix, bloquearSemCartaoVacina,idadeMinimaCartaoVacina, tipoAnexoCartaoVacina, tipoEmpresaFinanceiro, enviarEmailPagamento, cobrarMultaJurosPix, restringirConvidadoUmaVezPorMes,\n"
                + "produtoDayUse, modalidadeDayUse, produtoDiaPlus, tipoPlanoDiaPlus, modalidadeDiaPlus, aplicarMultaeJurosNoCancelamentoAutomatico, aplicarMultaMudancaPlano, bloquearRenovacaoAutomaticaPlanosForaDaVigencia, gerarBoletoCaixaAberto,\n"
                + "cpfCodigoInternoWeHelp, urlEnvioAcesso, naoGerarResiduoCancelamentoAutomatico, depositarResiduoCancelamentoNaContaCorrente, cancelamentoAvaliandoParcelas,tokenEnvioAcesso, usavitio, linkcheckoutvitio, \n"
                + "mensagemvitiowpp, mensagemvitioquercomprar, linkebook, convenioCobrancaBoleto, idcontabancariasesi, codexternounidadesesi, utilizaIntegracaoDelsoft, hostIntegracaoDelsoft, portaIntegracaoDelsoft, tokenIntegracaoDelsoft, nomeAplicacaoDelsoft, usuarioAplicacaoDelsoft, senhaAplicacaoDelsoft, planoAplicacaoDelsoft,"
                + "emiteValorTotalCompetencia, codigorede, responderBVNaVendaRapida, aplicarMultaSobreValorTotalContrato,\n"
                + "emitirNoNomeResponsavel,cnpjclientesesi, cobrarParcelaComBoletoGerado, cobrarParcelaVencidaSemTentativaCobranca, utilizarNomeResponsavelNoBoletoMaiorIdade, convenioCobrancaCartao,\n"
                + "qtdDiasEnvioSpc, consultarNovoCadastroSpc, enviarAutomaticoSpc, operadorSpc, senhaSpc, cancelamentoApresentarTransacoes, isentarCancelamento7Dias,\n"
                + "cancelarContratosNaoRenovaveisForaRecorrencia, codigoAssociadoSPC, limitarDescontosPorPerfil, registrarTentativasAcesso, pesquisaAutomaticaSPC, cobrarMultaJurosAsaas, valorMultaAsaas, valorJurosAsaas, concContasPagarFacilitePay, utilizarNomeResponsavelNoPixMenorIdade, utilizarNomeResponsavelNoPixMaiorIdade,\n"
                + "cancelamentoantecipadogerarparcelamultaseparada, irTelaPagamentoCartaoCreditoFormaPagamento, bloquearAcessoAlunoParqNaoAssinado, gerarAutCobrancaComCobAutBloqueada, \n"
                + "permitirAlterarDataFinalContratoNoCancelamento, emiteValorTotalFaturamento, gerarNotaFiscalComDesconto, facilitePayReguaCobranca, obrigatorioPreencherCamposCartao, concContasReceberFacilitePay,"
                + "cancelamentoAutomaticoAntecipadoContratoForaRecorrencia, valorMetaFacilitePay, ignorarCodigoDeBarrasEmissaoNfce, facilitePayConciliacaoCartao, diasParaVencimentoParq, bloquearAcessoSemTermoResponsabilidade, "
                + "facilitePayStoneConnect, facilitePayCDLSPC,facilitePayReguaCobrancaEmail,facilitePayReguaCobrancaSms,facilitePayReguaCobrancaApp,facilitePayReguaCobrancaWhatsApp, bloquearAcessoDiariaEmpresaDiferente, marcarAutoRecebiveisCartaoChequeCancelamento, habilitarCadastroEmpresaSesi, "
                + "integracaoNuvemshopNomeApp,integracaoNuvemshopEmail,integracaoNuvemshopTokenAcesso,integracaoNuvemshopHabilitada,integracaoNuvemshopStoreId,habilitarCobrancaAutomaticaNaVenda,bloquearAcessoMatriculaRematriculaTotemSemPagamento,"
                + "utilizarPactoPrint,validadeMesesCarteirinhaSocio,presidente,superintendente, bloquearAcessoSeDebitoEmConta, exigirAssinaturaDigitalResponsavelFinanceiro, idexterno, toleranciaCancelarContratosNaoAssinados, conveniocobrancacartaoregua, conveniocobrancapixregua, conveniocobrancaboletoregua, "
                + "tipoParcelasCobrarVendaSiteRegua, enviarEmailPagamentoRegua, gerarAutCobrancaComCobAutBloqueadaRegua, nomeCurto, habilitarValidacaoHorariosMesmaTurma, bloquearRenovacaoAutomaticaPlanosForaCategoriaCliente, horariocapacidadeporcategoria, envioNFCeAutomaticoNoPagamento)\n"
        + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
                + "?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 1;
            sqlInserir.setString(i++, obj.getNome());
            sqlInserir.setString(i++, obj.getRazaoSocial());
            sqlInserir.setString(i++, obj.getEndereco().trim());
            sqlInserir.setString(i++, obj.getSetor().trim());
            sqlInserir.setString(i++, obj.getNumero());
            sqlInserir.setString(i++, obj.getComplemento());
            sqlInserir.setInt(i++, obj.getCidade().getCodigo());
            sqlInserir.setInt(i++, obj.getEstado().getCodigo());
            sqlInserir.setInt(i++, obj.getPais().getCodigo());
            sqlInserir.setString(i++, obj.getCEP());
            sqlInserir.setString(i++, obj.getCNPJ());
            sqlInserir.setString(i++, obj.getInscEstadual());
            sqlInserir.setString(i++, obj.getTelComercial1());
            sqlInserir.setString(i++, obj.getTelComercial2());
            sqlInserir.setString(i++, obj.getTelComercial3());
            sqlInserir.setString(i++, Uteis.removerEspacosInicioFimString(obj.getEmail()));
            sqlInserir.setString(i++, obj.getSite());
            sqlInserir.setString(i++, obj.getFax());

            if (obj.getQuestionarioPrimeiraVisita().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getQuestionarioPrimeiraVisita().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getQuestionarioRetorno().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getQuestionarioRetorno().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getQuestionarioReMatricula().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getQuestionarioReMatricula().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setBoolean(i++, obj.isPermiteSituacaoAtestadoContrato());
            sqlInserir.setBoolean(i++, obj.isPermiteContratosConcomintante());
            sqlInserir.setDouble(i++, obj.getJuroParcela());
            sqlInserir.setDouble(i++, obj.getMulta());
            sqlInserir.setInt(i++, obj.getNrDiasVigenteQuestionarioVista());
            sqlInserir.setInt(i++, obj.getNrDiasVigenteQuestionarioRetorno());
            sqlInserir.setInt(i++, obj.getNrDiasVigenteQuestionarioRematricula());
            sqlInserir.setString(i++, obj.getMascaraMatricula());
            sqlInserir.setInt(i++, obj.getCarenciaRenovacao());
            sqlInserir.setBytes(i++, obj.getFoto());
            sqlInserir.setBytes(i++, obj.getFotoRelatorio());
            sqlInserir.setString(i++, obj.getAlturaFotoEmpresa());
            sqlInserir.setString(i++, obj.getLarguraFotoEmpresa());
            sqlInserir.setString(i++, obj.getAlturaFotoRelatorio());
            sqlInserir.setString(i++, obj.getLarguraFotoRelatorio());
            sqlInserir.setInt(i++, obj.getNrDiasAvencer());
            sqlInserir.setInt(i++, obj.getToleranciaPagamento());
            sqlInserir.setInt(i++, obj.getQtdFaltaPeso1());
            sqlInserir.setInt(i++, obj.getQtdFaltaInicioPeso2());
            sqlInserir.setInt(i++, obj.getQtdFaltaTerminoPeso2());
            sqlInserir.setInt(i++, obj.getQtdFaltaPeso3());
            sqlInserir.setInt(i++, obj.getSomaDv());
            sqlInserir.setInt(i++, obj.getCarencia());
            sqlInserir.setInt(i++, obj.getNrDiasProrata());
            sqlInserir.setBytes(i++, obj.getFotoEmail());
            sqlInserir.setString(i++, obj.getAlturaFotoEmail());
            sqlInserir.setString(i++, obj.getLarguraFotoEmail());
            sqlInserir.setInt(i++, obj.getToleranciaDiasContratoVencido());
            sqlInserir.setString(i++, obj.getTimeZoneDefault());
            sqlInserir.setString(i++, obj.getUrlRecorrencia());
            sqlInserir.setInt(i++, obj.getNrDiasCompensacao());
            sqlInserir.setString(i++, obj.getServiceUsuario());
            if (!obj.getServiceSenha().isEmpty() && !obj.getServiceSenha().contains("==")) {
                obj.setServiceSenha(Criptografia.encrypt(obj.getServiceSenha(),
                        SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM));
            }
            sqlInserir.setString(i++, obj.getServiceSenha());
            sqlInserir.setInt(i++, obj.getToleranciaOcupacaoTurma());
            sqlInserir.setBoolean(i++, obj.isBloquearAcessoSeParcelaAberta());
            sqlInserir.setBoolean(i++, obj.getBloquearAcessoSemAssinaturaDigital());
            sqlInserir.setBoolean(i++, obj.getBloquearAcessoCrefVencido());
            if (obj.getConsultorVendaAvulsa() != null && obj.getConsultorVendaAvulsa().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getConsultorVendaAvulsa().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setBoolean(i++, obj.isPermiteHorariosConcorrentesParaProfessor());
            sqlInserir.setBoolean(i++, obj.isProfessorEmAmbientesDiferentesMesmoHorario());
            sqlInserir.setBoolean(i++, obj.isMostrarCnpj());
            sqlInserir.setString(i++, obj.getChaveNFSe());
            sqlInserir.setBoolean(i++, obj.getUsarNFSe());
            sqlInserir.setString(i++, obj.getTokenSMS());
            sqlInserir.setInt(i++, obj.getNrDiasChequeAVista());
            sqlInserir.setBoolean(i++, obj.isMostrarModalidade());
            sqlInserir.setBoolean(i++, obj.isBvObrigatorio());
            sqlInserir.setInt(i++, obj.getTempoAposFaltaReposicao());
            sqlInserir.setBoolean(i++, obj.isEnviarNFSeAutomatico());
            sqlInserir.setBoolean(i++, obj.isComissaoMatriculaRematricula());

            if (obj.getQuestionarioPrimeiraCompra().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getQuestionarioPrimeiraCompra().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setInt(i++, obj.getNrDiasVigenteQuestionarioPrimeiraCompra());

            if (obj.getQuestionarioRetornoCompra().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getQuestionarioRetornoCompra().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setInt(i++, obj.getNrDiasVigenteQuestionarioRetornoCompra());
            sqlInserir.setBoolean(i++, obj.isUsarNFSePorPagamento());
            sqlInserir.setInt(i++, obj.getQtdDiasCobrarRematricula());
            sqlInserir.setInt(i++, obj.getQtdVias());
            sqlInserir.setBoolean(i++, obj.isQuebrarPaginaRecibo());
            sqlInserir.setBoolean(i++, obj.isDetalharPeriodoProduto());
            sqlInserir.setBoolean(i++, obj.isDetalharParcelas());
            sqlInserir.setBoolean(i++, obj.isDetalharPagamentos());
            sqlInserir.setBoolean(i++, obj.isDetalharDescontos());
            sqlInserir.setBoolean(i++, obj.isApresentarAssinaturas());
            sqlInserir.setString(i++, obj.getObservacaoRecibo());
            sqlInserir.setInt(i++, obj.getTipoGestaoNFSe());
            sqlInserir.setBoolean(i++, obj.isRetrocederValorMensalPlanoCancelamento());
            sqlInserir.setBoolean(i++, obj.isFecharNegociacaoSemAutorizacaoDCC());
            sqlInserir.setInt(i++, obj.getNrDiasDesistenteRemoverVinculoTreino());
            sqlInserir.setBoolean(i++, obj.isRemoverVinculosAposDesistencia());
            sqlInserir.setBoolean(i++, obj.isLiberarPersonalComTaxaEmAberto());
            sqlInserir.setBoolean(i++, obj.isReciboParaImpressoraTermica());
            sqlInserir.setBoolean(i++, obj.isUsarManutencaoModalidadeComissao());
            sqlInserir.setBoolean(i++, obj.isDevolucaoEntraNoCaixa());
            sqlInserir.setBytes(i++, obj.getFotoRedeSocial());
            sqlInserir.setString(i++, obj.getAlturaFotoRedeSocial());
            sqlInserir.setString(i++, obj.getLarguraFotoRedeSocial());
            sqlInserir.setInt(i++, obj.getArredondamento().getId());
            sqlInserir.setInt(i++, obj.getToleranciaProrata());
            sqlInserir.setBoolean(i++, obj.isUsarGestaoCreditosPersonal());
            sqlInserir.setBoolean(i++, obj.isPermiteReposicaoEmTurmasDiferentes());
            sqlInserir.setBoolean(i++, obj.isForcarMinimoVencimento2parcela());
            sqlInserir.setBoolean(i++, obj.isPermiteRenovarContratosEmTurmasLotadas());

            sqlInserir.setBytes(i++, obj.getHomeBackground640x551());
            sqlInserir.setBytes(i++, obj.getHomeBackground320x276());
            //alturahomeBackground640x551, largurahomeBackground640x551,alturahomeBackground320x276, largurahomeBackground320x276
            sqlInserir.setString(i++, obj.getAlturahomeBackground640x551());
            sqlInserir.setString(i++, obj.getLargurahomeBackground640x551());
            sqlInserir.setString(i++, obj.getAlturahomeBackground320x276());
            sqlInserir.setString(i++, obj.getLargurahomeBackground320x276());

            sqlInserir.setString(i++, obj.getLatitude());
            sqlInserir.setString(i++, obj.getLongitude());

            sqlInserir.setBoolean(i++, obj.isAcessoChamada());
            if (obj.getLocalAcessoChamada().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getLocalAcessoChamada().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            if (obj.getColetorChamada().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getColetorChamada().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            resolveIntegerNull(sqlInserir, i++, obj.getDiasRenovacaoAutomaticaAntecipada());
            sqlInserir.setBoolean(i++, obj.isMostrarMensagemValoresRodape());
            sqlInserir.setBoolean(i++, obj.getCobrarAutomaticamenteMultaJuros());
            sqlInserir.setDouble(i++, obj.getMultaCobrancaAutomatica());
            sqlInserir.setDouble(i++, obj.getJurosCobrancaAutomatica());
            sqlInserir.setBoolean(i++, obj.getLiberarPersonalProfessorDebito());
            if (obj.getConsultorSite() != null && obj.getConsultorSite().getCodigo() != 0) {
                sqlInserir.setInt(i++, obj.getConsultorSite().getCodigo());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setBoolean(i++, obj.isCriarBvVendaSite());
            sqlInserir.setBoolean(i++, obj.isAtiva());
            sqlInserir.setBoolean(i++, obj.getEmiteNFSEPorDataCompensacao());
            sqlInserir.setBoolean(i++, obj.isPagarComissaoSeAtingirMetaFinanceira());
            sqlInserir.setBoolean(i++, obj.isPagarComissaoManutencaoModalidade());
            sqlInserir.setBoolean(i++, obj.getSomenteVendaProdutosComEstoque());
            sqlInserir.setBoolean(i++, obj.isPermiteAlterarDataEmissaoNFSe());
            sqlInserir.setInt(i++, obj.getMinutosAposUltimoAcessoDiminuirCredito());
            if (obj.getModeloMensagemVendasOnline() == null || obj.getModeloMensagemVendasOnline().getCodigo() == 0) {
                sqlInserir.setNull(i++, Types.INTEGER);
            } else {
                sqlInserir.setInt(i++, obj.getModeloMensagemVendasOnline().getCodigo());
            }

            sqlInserir.setBoolean(i++, obj.isCancelamentoObrigatoriedadePagamento());
            if (obj.isCancelamentoObrigatoriedadePagamento()) {
                sqlInserir.setBoolean(i++, true);
            } else {
                sqlInserir.setBoolean(i++, obj.isCancelamentoAntecipado());
            }

            sqlInserir.setInt(i++, obj.getCancelamentoAntecipadoDias());
            sqlInserir.setDouble(i++, obj.getCancelamentoAntecipadoMulta());
            sqlInserir.setString(i++, obj.getCancelamentoAntecipadoPlanos());
            sqlInserir.setDouble(i++, obj.getCancelamentoAntecipadoPlanosMulta());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getCancelamentoAntecipadoPlanosData()));
            sqlInserir.setBoolean(i++, obj.isEnviarEmailCancelamento());
            sqlInserir.setBoolean(i++, obj.isPermiteContratoPosPagoRenovacaoAuto());
            sqlInserir.setInt(i++, obj.getTipoCobrancaPacto());
            sqlInserir.setBoolean(i++, obj.isGerarCobrancaAutomaticaPacto());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDtUltimaCobrancaPacto()));
            sqlInserir.setInt(i++, obj.getQtdDiasFechamentoCobrancaPacto());
            sqlInserir.setDouble(i++, obj.getValorCreditoPacto());
            sqlInserir.setBoolean(i++, obj.isGerarNotaFiscalCobrancaPacto());
            sqlInserir.setInt(i++, obj.getQtdParcelasCobrancaPacto());
            sqlInserir.setInt(i++, obj.getQtdCreditoRenovarPrePagoCobrancaPacto());
            sqlInserir.setInt(i++, obj.getQtdDiasParaLiberacaoDeVagaEmTrancamento());
            sqlInserir.setInt(i++, obj.getTentativasLiberarParcelaVencida());

            sqlInserir.setBytes(i++, obj.getPropagandaBoleto());
            sqlInserir.setString(i++, obj.getAlturaPropagandaBoleto());
            sqlInserir.setString(i++, obj.getLarguraPropagandaBoleto());
            sqlInserir.setInt(i++, obj.getQtdDiasVencimentoBoleto());
            sqlInserir.setBoolean(i++, obj.isUsarDataInicioDeContratoNoBI_ICV());
            sqlInserir.setBoolean(i++, obj.isExistemNovosBoletosPacto());
            sqlInserir.setInt(i++, obj.getTipoParcelasCobrarVendaSite().getId());
            if (UtilReflection.objetoMaiorQueZero(obj, "getConvenioBoletoPadrao().getCodigo()")) {
                sqlInserir.setInt(i++, obj.getConvenioBoletoPadrao().getCodigo());
            } else {
                sqlInserir.setNull(i++, Types.NULL);
            }
            sqlInserir.setBoolean(i++, obj.isGerarLoginAPIAoIncluirContrato());
            if (UtilReflection.objetoMaiorQueZero(obj, "getModeloMensagemEsqueciMinhaSenhaVendasOnline().getCodigo()")) {
                sqlInserir.setInt(i++, obj.getModeloMensagemEsqueciMinhaSenhaVendasOnline().getCodigo());
            } else {
                sqlInserir.setNull(i++, Types.NULL);
            }
            sqlInserir.setString(i++, obj.getTipoParcelaCancelamento());
            sqlInserir.setInt(i++, obj.getQuantidadeParcelasSeguidasCancelamento());
            sqlInserir.setBoolean(i++, obj.isEnviarNotaCidadeEmpresa());
            sqlInserir.setBoolean(i++, obj.isPermitirMaillingGerarAutorizacaoCobrancaBoleto());
            sqlInserir.setBoolean(i++, obj.isGerarNFSeContaCorrente());
            sqlInserir.setString(i++, obj.getInscMunicipal());
            sqlInserir.setInt(i++, obj.getSequencialLoteRPS());
            sqlInserir.setBoolean(i++, obj.isPermiteGerarArquivoLoteRPS());
            sqlInserir.setBoolean(i++, obj.isPermiteGerarNotaManual());
            sqlInserir.setBoolean(i++, obj.isImpedirVendaContratoPorConflitoReposicao());
            sqlInserir.setBoolean(i++, obj.isUsarNFCe());
            sqlInserir.setBoolean(i++, obj.isUtilizarNomeResponsavelNoBoleto());
            sqlInserir.setBoolean(i++, obj.isGerarQuitacaoCancelamentoAuto());
            sqlInserir.setBoolean(i++, obj.isGerarQuitacaoCancelamentoRemanescente());
            sqlInserir.setBoolean(i++, obj.isPagarComissaoProdutos());
            sqlInserir.setBoolean(i++, obj.isAlterarDataHoraCheckGestaoPersonal());
            sqlInserir.setBoolean(i++, obj.isSenhaAcessoOnzeDigitos());
            sqlInserir.setBoolean(i++, obj.isNaoRenovarContratoSemIndiceFinanceiro());
            sqlInserir.setString(i++, obj.getCodigoGymPass().trim());
            sqlInserir.setString(i++, obj.getTokenApiGymPass().trim());
            sqlInserir.setBoolean(i++, obj.isGerarRemessaContratoCancelado());
            sqlInserir.setBoolean(i++, obj.isHabilitarSomaDeAulaNaoVigente());
            if (obj.getCodigoChaveIntegracaoDigitais() != 0) {
                sqlInserir.setInt(i++, obj.getCodigoChaveIntegracaoDigitais());
            } else {
                sqlInserir.setNull(i++, 0);
            }
            sqlInserir.setBoolean(i++, obj.isDefinirCpfComoSenhaCatraca());
            sqlInserir.setBoolean(i++, obj.isMostrarNotaPorDiaCompetencia());
            sqlInserir.setInt(i++, obj.getPontosAlunoAcesso());
            sqlInserir.setInt(i++, obj.getPontosAlunoAcessoChuva());
            sqlInserir.setInt(i++, obj.getPontosAlunoAcessoFrio());
            sqlInserir.setInt(i++, obj.getPontosAlunoAcessoCalor());
            sqlInserir.setBoolean(i++, obj.isTrabalharComPontuacao());
            sqlInserir.setBoolean(i++, obj.isRetirarEdicaoPagamento());
            sqlInserir.setBoolean(i++, obj.isAdicionarAulasDesmarcadasContratoAnterior());
            sqlInserir.setString(i++, obj.getTipoParcelaCancelamentoForaRegimeRecorrencia());
            sqlInserir.setInt(i++, obj.getQuantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia());
            sqlInserir.setInt(i++, obj.getQuantidadeDiasUteisAposVencimentoParaCancelarContrato());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getLimiteInicialItensBIPendencia()));
            obj.setTempoSaidaAcademia(Uteis.converteHoraParaInteiro(obj.getTempoSaidaAcademiaFormatada()));
            sqlInserir.setInt(i++, obj.getTempoSaidaAcademia());
            sqlInserir.setString(i++, obj.getTokenSMSShortCode());
            sqlInserir.setBoolean(i++, obj.isPermiteRenovarContratoViaAPP());
            sqlInserir.setDouble(i++, obj.getValorMensalEmitirNFSe());
            sqlInserir.setBoolean(i++, obj.isHabilitarReenvioAutomaticoRemessa());
            sqlInserir.setBoolean(i++, obj.isEmitirNotaSomenteRecorrencia());
            sqlInserir.setBoolean(i++, obj.isEmitirNomeAlunoNotaFamilia());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getCancelamentoAntecipadoContratosDepoisDe()));
            sqlInserir.setBoolean(i++, obj.isIrTelaPagamentoCartaoCreditoRecorrente());
            sqlInserir.setBoolean(i++, obj.isEnviarNFCeAutomatico());
            sqlInserir.setBoolean(i++, obj.isEmitirNFCeSomenteRecorrencia());
            sqlInserir.setBoolean(i++, obj.isUsaIntegracoesCrm());
            sqlInserir.setString(i++, obj.getTokenBuzzLead());
            sqlInserir.setString(i++, obj.getUrlLinkSiteCadastro());
            sqlInserir.setBoolean(i++, obj.isUsarDataOriginalCompensacaoNFSe());
            sqlInserir.setBoolean(i++, obj.isUtilizaSistemaEstacionamento());
            sqlInserir.setBoolean(i++, obj.isPermitirLancarVariasParcelasSaldoDevedor());
            sqlInserir.setInt(i++, obj.getProdutoEmissaoNFCeFinanceiro());
            sqlInserir.setInt(i++, obj.getProdutoEmissaoNFSeFinanceiro());
            sqlInserir.setInt(i++, obj.getQtdExecucoesRetentativa());
            sqlInserir.setBoolean(i++, obj.isUsarNFCePorPagamento());
            sqlInserir.setBoolean(i++, obj.isConsultarDiasAnterioresNFSe());
            sqlInserir.setBoolean(i++, obj.isConsultarDiasAnterioresNFCe());
            sqlInserir.setBoolean(i++, obj.isNotasAutoPgRetroativo());
            sqlInserir.setBoolean(i++, obj.isValidarVencimentoCartaoAutorizacao());
            sqlInserir.setBoolean(i++, obj.isPermitirEstornarContratoComParcelasPG());
            sqlInserir.setBoolean(i++, obj.isPermMarcarAulaFeriado());
            sqlInserir.setString(i++, obj.getHoraAberturaFeriado());
            sqlInserir.setString(i++, obj.getHoraFechamentoFeriado());
            sqlInserir.setString(i++, obj.getFormasPagamentoEmissaoNFCe());
            sqlInserir.setString(i++, obj.getFormasPagamentoEmissaoNFSe());
            sqlInserir.setBoolean(i++, obj.isConsiderarSomenteParcelasPlanos());
            sqlInserir.setBoolean(i++, obj.isCobrarMultaJurosTransacao());
            sqlInserir.setBoolean(i++, obj.isCobrarMultaJurosDCO());
            sqlInserir.setBoolean(i++, obj.isCobrarMultaJurosDCC());
            sqlInserir.setBoolean(i++, obj.isAddAutoClienteTreinoWeb());
            sqlInserir.setBoolean(i++, obj.isUsarParceiroFidelidade());
            sqlInserir.setBoolean(i++, obj.isEnvioNotificacaoNotasNFSe());
            sqlInserir.setBoolean(i++, obj.isEnvioNotificacaoNotasNFCe());
            sqlInserir.setString(i++, obj.getEmailsNotificacaoAutomaticaNotas());
            sqlInserir.setString(i++, obj.getEmailNotificacaoVendasOnline());
            sqlInserir.setBoolean(i++, obj.isValidarCertificado());
            sqlInserir.setBoolean(i++, obj.isBloquearAcessoArmarioVigenciaVencida());
            sqlInserir.setBoolean(i++, obj.isUtilizarJurosValorAbsoluto());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataConcessaoDiaExtra()));
            sqlInserir.setString(i++, obj.getEmailMovidesk());
            sqlInserir.setBoolean(i++, obj.getSincronizadoMovidesk());
            sqlInserir.setBoolean(i++, obj.isUsarConciliadora());
            sqlInserir.setString(i++, obj.getEmpresaConciliadora());
            sqlInserir.setString(i++, obj.getSenhaConciliadora());
            sqlInserir.setInt(i++, obj.getDiasParaRetirarRelFechamentoDeCaixa());
            sqlInserir.setString(i++, obj.getMoeda());
            sqlInserir.setString(i++, obj.getLocaleTexto());
            resolveIntegerNull(sqlInserir, i++, obj.getConfiguracaoNotaFiscalNFSe().getCodigo());
            resolveIntegerNull(sqlInserir, i++, obj.getConfiguracaoNotaFiscalNFCe().getCodigo());
            sqlInserir.setBoolean(i++, obj.isUsarNomeResponsavelNFCe());
            sqlInserir.setString(i++, obj.getTipoProdutoEmissaoNFSe());
            sqlInserir.setString(i++, obj.getTipoProdutoEmissaoNFCe());
            sqlInserir.setString(i++, obj.getDescMoeda());
            sqlInserir.setBoolean(i++, obj.isNaoCobrarMultaDeContratoRenovado());
            sqlInserir.setBoolean(i++, obj.isNaoCobrarMultaDeTodasParcelasPagas());
            resolveIntegerNull(sqlInserir, i++, obj.getMinutosCreditarProximoPontoClubeVantagens());
            sqlInserir.setBoolean(i++, obj.isApenasPrimeiroAcessoClubeVantagens());
            if (obj.getZerarPontosAposVencimento() != null) {
                sqlInserir.setInt(i++, obj.getZerarPontosAposVencimento().getCodigo());
            } else {
                sqlInserir.setNull(i++, Types.INTEGER);
            }
            sqlInserir.setBoolean(i++, obj.isPontuarApenasCategoriasEmCampanhasAtivas());
            sqlInserir.setBoolean(i++, obj.isAplicarIndicacaoQlqrPlano());

            sqlInserir.setString(i++, obj.getTiposProduto());
            sqlInserir.setBoolean(i++, obj.isEmitirDuplicataNFSe());
            sqlInserir.setBoolean(i++, obj.isUtilizarDataCancelamentoParaValidarParcelas());
            sqlInserir.setBoolean(i++, obj.isEmitirMesReferenciaNFCe());

            sqlInserir.setBoolean(i++, obj.isIntegracaoSpiviHabilitada());
            sqlInserir.setString(i++, obj.getIntegracaoSpiviSourceName());
            if (UteisValidacao.emptyNumber(obj.getIntegracaoSpiviSiteID())) {
                sqlInserir.setNull(i++, Types.INTEGER);
            } else {
                sqlInserir.setInt(i++, obj.getIntegracaoSpiviSiteID());
            }
            if (UteisValidacao.emptyString(obj.getIntegracaoSpiviPassword())) {
                sqlInserir.setNull(i++, Types.VARCHAR);
            } else {
                sqlInserir.setString(i++, obj.getIntegracaoSpiviPassword());
            }
            if (obj.getCodEmpresaFinanceiro() != null) {
                sqlInserir.setInt(i++, obj.getCodEmpresaFinanceiro());
            } else {
                sqlInserir.setNull(i++, Types.INTEGER);
            }
            sqlInserir.setBoolean(i++, obj.isMostrarValoresZeradosRel());
            sqlInserir.setBoolean(i++, obj.isZerarValorCancelamentoTransferencia());
            sqlInserir.setBoolean(i++, obj.isCobrarCreditoVindi());
            sqlInserir.setInt(i++, obj.getQtdDiasLimiteCobrancaParcelasRecorrencia());
            sqlInserir.setInt(i++, obj.getQtdDiasRepetirCobrancaParcelasRecorrencia());
            sqlInserir.setBoolean(i++, obj.isTentativaUnicaDeCobranca());
            sqlInserir.setBoolean(i++, obj.isMostrarDescricaoParcelaRenegociada());
            sqlInserir.setBoolean(i++, obj.isAcessoSomenteComAgendamento());
            sqlInserir.setInt(i++, obj.getCapacidadeSimultanea());
            sqlInserir.setBoolean(i++, obj.isUtilizaLeitorCodigoBarras());
            sqlInserir.setString(i++, obj.getNomeUsuarioAmigoFit());
            sqlInserir.setString(i++, obj.getSenhaUsuarioAmigoFit());
            sqlInserir.setString(i++, obj.getIntegracaoMyWellnessUser().trim());
            sqlInserir.setString(i++, obj.getIntegracaoMyWellnessPassword().trim());
            sqlInserir.setString(i++, obj.getIntegracaoMyWellnessApiKey().trim());
            sqlInserir.setBoolean(i++, obj.isIntegracaoMyWellnessHabilitada());
            sqlInserir.setString(i++, obj.getIntegracaoMyWellnessFacilityUrl().trim());
            sqlInserir.setBoolean(i++, obj.isAgruparParcelasPorCartao());
            sqlInserir.setDouble(i++, obj.getAgruparParcelasPorCartaoValorLimite());
            sqlInserir.setBoolean(i++, obj.isSomenteUmEnvioCartaoTentativa());
            sqlInserir.setBoolean(i++, obj.isIntegracaoMyWellnessEnviarVinculos());
            sqlInserir.setBoolean(i++, obj.isIntegracaoMyWellnessEnviarGrupos());
            sqlInserir.setInt(i++, obj.getDiaVencimentoCobrancaPacto());
            sqlInserir.setDouble(i++, obj.getValorLimiteCaixaAbertoVendaAvulsa());
            sqlInserir.setBoolean(i++, obj.isDetalharNiveisModalidades());
            sqlInserir.setBoolean(i++, obj.isNotificarWebhook());
            sqlInserir.setString(i++,obj.getUrlWebhookNotificar());

            sqlInserir.setBoolean(i++, obj.isIntegracaoMentorWebHabilitada());
            sqlInserir.setString(i++, obj.getIntegracaoMentorWebUrl());
            sqlInserir.setString(i++, obj.getIntegracaoMentorWebServico());
            sqlInserir.setString(i++, obj.getIntegracaoMentorWebUser());
            sqlInserir.setString(i++, obj.getIntegracaoMentorWebPassword());
            sqlInserir.setString(i++, obj.getIntegracaoF360Dir());

            sqlInserir.setBoolean(i++, obj.isIntegracaoF360RelFatHabilitada());
            sqlInserir.setString(i++, obj.getIntegracaoF360FtpServer());
            if (obj.getIntegracaoF360FtpPort() != null) {
                sqlInserir.setInt(i++, obj.getIntegracaoF360FtpPort());
            } else {
                sqlInserir.setNull(i++, Types.INTEGER);
            }
            sqlInserir.setString(i++, obj.getIntegracaoF360User());
            sqlInserir.setString(i++, null);
            sqlInserir.setBoolean(i++, obj.isIntegracaoF360Quinzenal());

            sqlInserir.setInt(i++, obj.getTipoVigenciaMyWellnessGymPass().getId());
            sqlInserir.setInt(i++, obj.getNrDiasVigenciaMyWellnessGymPass());

            sqlInserir.setBoolean(i++, obj.isIntegracaoAmigoFitHabilitada());
            sqlInserir.setBoolean(i++, obj.isPermiteCadastrarCartaoMesmoAssim());
            if (UteisValidacao.emptyNumber(obj.getConvenioVerificacaoCartao().getCodigo())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getConvenioVerificacaoCartao().getCodigo());
            }

            if (obj.isAtiva()) {
                obj.setTransferida(false);
            }
            sqlInserir.setBoolean(i++, obj.getTransferida());

            if (obj.getTransferida()) {
                sqlInserir.setString(i++, obj.getNovaChaveTransferencia());
                sqlInserir.setInt(i++, obj.getNovoCodigoTransferencia());
            } else {
                sqlInserir.setNull(i++, Types.VARCHAR);
                sqlInserir.setNull(i++, Types.INTEGER);
            }

            sqlInserir.setBoolean(i++, obj.isUtilizarMultaValorAbsoluto());
            sqlInserir.setBoolean(i++, obj.isPermiteMaillingCriarBoleto());
            if (UteisValidacao.emptyNumber(obj.getConvenioCobrancaPix().getCodigo())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getConvenioCobrancaPix().getCodigo());
            }
            sqlInserir.setBoolean(i++, obj.getBloquearSemCartaoVacina());
            if (UteisValidacao.emptyNumber(obj.getIdadeMinimaCartaoVacina())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getIdadeMinimaCartaoVacina());
            }
            if (obj.getTipoAnexoCartaoVacina() == null) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getTipoAnexoCartaoVacina().getCodigo());
            }
            if (obj.getTipoEmpresa() == null) {
                sqlInserir.setString(i++, TipoEmpresaFinanceiro.NAO_TIPIFICADO.toString());
            } else {
                sqlInserir.setString(i++, obj.getTipoEmpresa().toString());
            }
            sqlInserir.setBoolean(i++, obj.isEnviarEmailPagamento());
            sqlInserir.setBoolean(i++, obj.isCobrarMultaJurosPix());
            sqlInserir.setBoolean(i++, obj.getRestringirConvidadoUmaVezPorMes());
            if (obj.getProdutoDayUse() == null || obj.getProdutoDayUse().getCodigo() == 0) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getProdutoDayUse().getCodigo());
            }

            if (obj.getModalidadeDayUse() == null || obj.getModalidadeDayUse().getCodigo() == 0) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getModalidadeDayUse().getCodigo());
            }

            if (obj.getProdutoDiaPlus() == null || obj.getProdutoDiaPlus().getCodigo() == 0) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getProdutoDiaPlus().getCodigo());
            }

            if (obj.getTipoPlanoDiaPlus() == null || obj.getTipoPlanoDiaPlus().getCodigo() == 0) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getTipoPlanoDiaPlus().getCodigo());
            }

            if (obj.getModalidadeDiaPlus() == null || obj.getModalidadeDiaPlus().getCodigo() == 0) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getModalidadeDiaPlus().getCodigo());
            }
            sqlInserir.setBoolean(i++, obj.isAplicarMultaeJurosNoCancelamentoAutomatico());
            sqlInserir.setBoolean(i++, obj.isAplicarMultaMudancaPlano());
            sqlInserir.setBoolean(i++, obj.isBloquearRenovacaoAutomaticaPlanosForaDaVigencia());
            sqlInserir.setBoolean(i++, obj.isGerarBoletoCaixaAberto());
            sqlInserir.setBoolean(i++, obj.isCpfCodigoInternoWeHelp());
            sqlInserir.setString(i++, obj.getUrlEnvioAcesso());
            sqlInserir.setBoolean(i++, obj.isNaoGerarResiduoCancelamentoAutomatico());
            sqlInserir.setBoolean(i++, obj.isDepositarResiduoCancelamentoNaContaCorrente());
            sqlInserir.setBoolean(i++, obj.isCancelamentoAvaliandoParcelas());
            sqlInserir.setString(i++, obj.getTokenEnvioAcesso());
            sqlInserir.setBoolean(i++, obj.isUsaVitio());
            sqlInserir.setString(i++, obj.getLinkCheckoutVitio());
            sqlInserir.setString(i++, obj.getMensagemVitioWhatsapp());
            sqlInserir.setString(i++, obj.getMensagemVitioQuerComprar());
            sqlInserir.setString(i++, obj.getLinkEbook());

            resolveIntegerNull(sqlInserir, i++, obj.getConvenioCobrancaBoleto().getCodigo());
            resolveIntegerNull(sqlInserir, i++, obj.getIdContabancariaSesi());
            resolveIntegerNull(sqlInserir, i++, obj.getCodExternoUnidadeSesi());

            sqlInserir.setBoolean(i++, obj.getUtilizaIntegracaoDelsoft());
            sqlInserir.setString(i++, obj.getHostIntegracaoDelsoft());
            sqlInserir.setInt(i++, obj.getPortaIntegracaoDelsoft());
            sqlInserir.setString(i++, obj.getTokenIntegracaoDelsoft());
            sqlInserir.setString(i++, obj.getNomeAplicacaoDelsoft());
            sqlInserir.setString(i++, obj.getUsuarioAplicacaoDelsoft());
            sqlInserir.setString(i++, obj.getSenhaAplicacaoDelsoft());
            if (obj.getPlanoAplicacaoDelsoft() == null || obj.getPlanoAplicacaoDelsoft().getCodigo() == 0) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setInt(i++, obj.getPlanoAplicacaoDelsoft().getCodigo());
            }
            sqlInserir.setBoolean(i++, obj.isEmiteValorTotalCompetencia());
            if (UteisValidacao.emptyString(obj.getCodigoRede())) {
                sqlInserir.setNull(i++, 0);
            } else {
                sqlInserir.setString(i++, obj.getCodigoRede());
            }
            sqlInserir.setBoolean(i++, obj.isResponderBVNaVendaRapida());
            sqlInserir.setBoolean(i++,obj.isAplicarMultaSobreValorTotalContrato());
            sqlInserir.setBoolean(i++, obj.isEmitirNoNomeResponsavel());
            sqlInserir.setString(i++, obj.getCnpjClienteSesi());
            sqlInserir.setBoolean(i++, obj.isCobrarParcelaComBoletoGerado());
            sqlInserir.setBoolean(i++, obj.isCobrarParcelaVencidaSemTentativaCobranca());
            sqlInserir.setBoolean(i++, obj.isUtilizarNomeResponsavelNoBoletoMaiorIdade());
            resolveIntegerNull(sqlInserir, i++, obj.getConvenioCobrancaCartao().getCodigo());

            sqlInserir.setInt(i++, obj.getQtdDiasEnvioSPC());
            sqlInserir.setBoolean(i++, obj.isConsultarNovoCadastroSPC());
            sqlInserir.setBoolean(i++, obj.isEnvioAutomaticoSPC());
            sqlInserir.setString(i++, obj.getOperadorSpc());
            sqlInserir.setString(i++, obj.getSenhaSpc());
            sqlInserir.setBoolean(i++, obj.isCancelamentoApresentarTransacoes());
            sqlInserir.setBoolean(i++, obj.isIsentarCancelamento7Dias());
            sqlInserir.setBoolean(i++, obj.isCancelarContratosNaoRenovaveisForaRecorrencia());
            resolveLongNull(sqlInserir, i++, obj.getCodigoAssociadoSpc());
            sqlInserir.setBoolean(i++, obj.isLimitarDescontosPorPerfil());
            sqlInserir.setBoolean(i++, obj.isRegistrarTentativasAcesso());
            sqlInserir.setBoolean(i++, obj.isPesquisaAutomaticaSPC());

            sqlInserir.setBoolean(i++, obj.isCobrarMultaJurosAsaas());
            sqlInserir.setDouble(i++, obj.getValorMultaAsaas());
            sqlInserir.setDouble(i++, obj.getValorJurosAsaas());
            sqlInserir.setBoolean(i++, obj.isConcContasPagarFacilitePay());
            sqlInserir.setBoolean(i++, obj.isUtilizarNomeResponsavelNoPixMenorIdade());
            sqlInserir.setBoolean(i++, obj.isUtilizarNomeResponsavelNoPixMaiorIdade());
            sqlInserir.setBoolean(i++, obj.isCancelamentoAntecipadoGerarParcelaMultaSeparada());
            sqlInserir.setBoolean(i++, obj.isIrTelaPagamentoCartaoCreditoFormaPagamento());
            sqlInserir.setBoolean(i++, obj.getBloquearAcessoAlunoParqNaoAssinado());
            sqlInserir.setBoolean(i++, obj.isGerarAutCobrancaComCobAutBloqueada());
            sqlInserir.setBoolean(i++, obj.isPermitirAlterarDataFinalContratoNoCancelamento());
            sqlInserir.setBoolean(i++, obj.isEmiteValorTotalFaturamento());
            sqlInserir.setBoolean(i++, obj.isGerarNotaFiscalComDesconto());
            sqlInserir.setBoolean(i++, obj.isFacilitePayReguaCobranca());
            sqlInserir.setBoolean(i++, obj.isObrigatorioPreencherCamposCartao());
            sqlInserir.setBoolean(i++, obj.isConcContasReceberFacilitePay());
            sqlInserir.setBoolean(i++, obj.isCancelamentoAutomaticoAntecipadoContratoForaRecorrencia());
            sqlInserir.setDouble(i++, obj.getValorMetaFacilitePay());
            sqlInserir.setBoolean(i++, obj.isIgnorarCodigoDeBarrasEmissaoNfce());
            sqlInserir.setBoolean(i++, obj.isFacilitePayConciliacaoCartao());
            sqlInserir.setInt(i++, obj.getDiasParaVencimentoParq());
            sqlInserir.setBoolean(i++, obj.getBloquearAcessoSemTermoResponsabilidade());
            sqlInserir.setBoolean(i++, obj.isFacilitePayStoneConnect());
            sqlInserir.setBoolean(i++, obj.isFacilitePayCDLSPC());
            sqlInserir.setBoolean(i++, obj.isFacilitePayReguaCobrancaEmail());
            sqlInserir.setBoolean(i++, obj.isFacilitePayReguaCobrancaSms());
            sqlInserir.setBoolean(i++, obj.isFacilitePayReguaCobrancaApp());
            sqlInserir.setBoolean(i++, obj.isFacilitePayReguaCobrancaWhatsApp());
            sqlInserir.setBoolean(i++, obj.getBloquearAcessoDiariaEmpresaDiferente());
            sqlInserir.setBoolean(i++, obj.isMarcarAutoRecebiveisCartaoChequeCancelamento());
            sqlInserir.setBoolean(i++, obj.isHabilitarCadastroEmpresaSesi());
            sqlInserir.setString(i++, obj.getIntegracaoNuvemshopNomeApp());
            sqlInserir.setString(i++, obj.getIntegracaoNuvemshopEmail());
            sqlInserir.setString(i++, obj.getIntegracaoNuvemshopTokenAcesso());
            sqlInserir.setBoolean(i++, obj.isIntegracaoNuvemshopHabilitada());
            sqlInserir.setString(i++, obj.getIntegracaoNuvemshopStoreId());
            sqlInserir.setBoolean(i++, obj.isHabilitarCobrancaAutomaticaNaVenda());
            sqlInserir.setBoolean(i++, obj.isBloquearAcessoMatriculaRematriculaTotemSemPagamento());
            sqlInserir.setBoolean(i++, obj.getUtilizarPactoPrint());
            resolveIntegerNull(sqlInserir, i++, obj.getValidadeMesesCarteirinhaSocio());
            sqlInserir.setString(i++, obj.getPresidente());
            sqlInserir.setString(i++, obj.getSuperintendente());
            sqlInserir.setBoolean(i++, obj.isBloquearAcessoSeDebitoEmConta());
            sqlInserir.setBoolean(i++, obj.isExigirAssinaturaDigitalResponsavelFinanceiro());
            sqlInserir.setString(i++, obj.getIdExterno());
            sqlInserir.setInt(i++, obj.getToleranciaCancelarContratosNaoAssinados());
            resolveIntegerNull(sqlInserir, i++, obj.getConvenioCobrancaCartaoRegua().getCodigo());
            resolveIntegerNull(sqlInserir, i++, obj.getConvenioCobrancaPixRegua().getCodigo());
            resolveIntegerNull(sqlInserir, i++, obj.getConvenioCobrancaBoletoRegua().getCodigo());
            sqlInserir.setInt(i++, obj.getTipoParcelasCobrarVendaSiteRegua().getId());
            sqlInserir.setBoolean(i++, obj.isEnviarEmailPagamentoRegua());
            sqlInserir.setBoolean(i++, obj.isGerarAutCobrancaComCobAutBloqueadaRegua());
            sqlInserir.setString(i++, obj.getNomeCurto());
            sqlInserir.setBoolean(i++, obj.getHabilitarValidacaoHorariosMesmaTurma());
            sqlInserir.setBoolean(i++, obj.isBloquearRenovacaoAutomaticaPlanosForaCategoriaCliente());
            sqlInserir.setBoolean(i++, obj.isHorariocapacidadeporcategoria());
            sqlInserir.setBoolean(i++, obj.isEnvioNFCeAutomaticoNoPagamento());


            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        ContaCorrenteEmpresa contaCorrenteEmpresaDAO = new ContaCorrenteEmpresa(con);
        contaCorrenteEmpresaDAO.incluirContaCorrenteEmpresas(obj.getCodigo(), obj.getContaCorrenteEmpresaVOs());
        contaCorrenteEmpresaDAO = null;

        ProdutoDevolverCancelamentoEmpresa produtoDevolverCancelamentoEmpresaDAO = new ProdutoDevolverCancelamentoEmpresa(con);
        produtoDevolverCancelamentoEmpresaDAO.incluirProdutosDevolverCancelEmpresa(obj.getProdutoDevolverCancelamentoEmpresaVOS());
        contaCorrenteEmpresaDAO = null;

        ConfiguracaoReenvioMovParcelaEmpresa configDAO = new ConfiguracaoReenvioMovParcelaEmpresa(con);
        configDAO.incluirConfiguracaoReenvioMovParcelaEmpresa(obj.getCodigo(), obj.getConfiguracaoReenvioMovParcelaEmpresaVOS());
        configDAO = null;

        inserirConfigsGestaoPersonal(obj);
        inserirConfigsRDStation(obj);
        inserirConfigEstacionamento(obj);
        gravarParceiroFidelidade(obj);
        obj.setNovoObj(false);
        updateFotos(obj);
        reiniciarServicosNovaEmpresaOuAtivaInativa();
    }

    private void zeroFillByteArrays(EmpresaVO obj) {
        if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
            obj.getFotos().put(MidiaEntidadeEnum.FOTO_EMPRESA, obj.getFoto());
            obj.getFotos().put(MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, obj.getFotoEmail());
            obj.getFotos().put(MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, obj.getFotoRelatorio());
            obj.getFotos().put(MidiaEntidadeEnum.FOTO_EMPRESA_REDESOCIAL, obj.getFotoRedeSocial());
            obj.getFotos().put(MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground320x276, obj.getHomeBackground320x276());
            obj.getFotos().put(MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground640x551, obj.getHomeBackground640x551());
            obj.getFotos().put(MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO, obj.getPropagandaBoleto());
            obj.setFoto(null);
            obj.setFotoEmail(null);
            obj.setFotoRedeSocial(null);
            obj.setFotoRelatorio(null);
            obj.setHomeBackground320x276(null);
            obj.setHomeBackground640x551(null);
            obj.setPropagandaBoleto(null);
        }
    }

    @Override
    public void updateFotos(EmpresaVO obj){
        try {
            if (PropsService.isTrue(PropsService.fotosParaNuvem)
                    && obj.isFeitoUploadAlgumaFoto()
                    && !UteisValidacao.emptyNumber(obj.getCodigo())) {
                final String chave = DAO.resolveKeyFromConnection(con);
                MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA, obj.getCodigo().toString(), obj.getFotos().get(MidiaEntidadeEnum.FOTO_EMPRESA));

                MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, obj.getCodigo().toString(), obj.getFotos().get(MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL));

                MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, obj.getCodigo().toString(), obj.getFotos().get(MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));

                MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_REDESOCIAL, obj.getCodigo().toString(), obj.getFotos().get(MidiaEntidadeEnum.FOTO_EMPRESA_REDESOCIAL));

                MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground320x276, obj.getCodigo().toString(), obj.getFotos().get(MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground320x276));

                MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground640x551, obj.getCodigo().toString(),obj.getFotos().get(MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground640x551));

                MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO, obj.getCodigo().toString(), obj.getFotos().get(MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO));
            }
        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>EmpresaVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>EmpresaVO</code> que será alterada no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(EmpresaVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (!centralEventos) {
                alterar(getIdEntidade());
            }
            alterarSemCommit(obj);
            con.commit();
            updateFotos(obj);
        } catch (Exception e) {
            e.printStackTrace();
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(EmpresaVO obj) throws Exception{
        EmpresaVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        zeroFillByteArrays(obj);
        String sql = "UPDATE public.Empresa set nome = ?, razaoSocial = ?, endereco = ?, setor = ?, "
                + "numero = ?, complemento = ?, cidade = ?, estado = ?, pais = ?, CEP = ?, CNPJ = ?, inscEstadual = ?,"
                + " telComercial1 = ?, telComercial2 = ?, telComercial3 = ?, email = ?, site = ?, fax = ?, "
                + "questionarioPrimeiraVisita = ?, questionarioRetorno = ?, questionarioReMatricula = ?,"
                + " permiteSituacaoAtestadoContrato = ?, permiteContratosConcomintante = ?, juroParcela = ?, "
                + "multa = ?, nrDiasVigenteQuestionarioVisita = ?, nrDiasVigenteQuestionarioRetorno = ?"
                + " ,nrDiasVigenteQuestionarioRematricula = ?, mascaraMatricula = ?, carenciaRenovacao = ?, "
                + "foto = ?, fotoRelatorio = ?, alturaFotoEmpresa = ?, larguraFotoEmpresa = ?, alturaFotoRelatorio = ?,"
                + " larguraFotoRelatorio = ?, nrDiasAvencer = ?, toleranciaPagamento = ?, qtdFaltaPeso1 = ?, "
                + "qtdFaltaInicioPeso2 = ?, qtdFaltaTerminoPeso2 = ?, qtdFaltaPeso3 = ?, "
                + "somadv = ?, carencia = ?, nrDiasProrata = ? "
                + ", fotoEmail = ?, alturaFotoEmail = ?, larguraFotoEmail = ?, toleranciaDiasContratoVencido = ?,"
                + "timeZoneDefault = ?, urlRecorrencia = ?, nrdiascompensacao = ?, serviceUsuario = ?, serviceSenha = ?"
                + ", toleranciaocupacaoturma = ?, bloquearacessoseparcelaaberta = ?, bloquearacessosemassinaturadigital = ?, bloquearAcessoCrefVencido = ?, consultorvendaavulsa = ?, "
                + "permiteHorariosConcorrentesParaProfessor = ?, professorEmAmbientesDiferentesMesmoHorario = ?, mostrarcnpj = ?, "
                + "chavenfse = ?, usarnfse = ?, tokensms = ?, nrdiaschequeavista = ?, mostrarModalidade = ?, bvobrigatorio = ?,"
                + "tempoAposFaltaReposicao = ?, enviarNFSeAutomatico = ?, comissaoMatriculaRematricula = ?, "
                + "questionarioPrimeiraCompra = ?, nrDiasVigenteQuestionarioPrimeiraCompra = ?, questionarioRetornoCompra = ?,"
                + "nrDiasVigenteQuestionarioRetornoCompra = ?, nfseporpagamento = ?, qtdDiasCobrarRematricula = ?,"
                + "qtdvias = ?, quebrarpaginarecibo = ?, detalharperiodoproduto = ?, detalharparcelas = ?,"
                + "detalharpagamentos = ?, detalhardescontos = ?, apresentarassinaturas = ?, observacaorecibo = ?, "
                + "tipogestaonfse = ?, retrocederValorMensalPlanoCancelamento = ?, "
                + "fecharNegociacaoSemAutorizacaoDCC = ?, nrdiasdesistenteremovervinculotreino = ?,"
                + "removervinculosaposdesistencia = ?, liberarpersonalcomtaxaemaberto = ?, reciboparaimpressoratermica = ?,"
                + "usarManutencaoModalidadeComissao = ?, devolucaoEntraNoCaixa = ?, fotoredesocial = ?, alturafotoredesocial = ?, largurafotoredesocial = ?, "
                + "arredondamento = ?, toleranciaprorata = ?, "
                + "usarGestaoCreditosPersonal = ?, permitereposicaoemturmasdiferentes = ?, "
                + "forcarMinimoVencimento2parcela = ?, permiterenovarcontratosemturmaslotadas = ?, "
                + "homeBackground640x551 = ?, homeBackground320x276 = ?,"
                + "alturahomeBackground640x551 = ?, largurahomeBackground640x551  = ?,"
                + "alturahomeBackground320x276 = ?, largurahomeBackground320x276 = ?,"
                + "latitude = ?, longitude = ?, acessoChamada = ?, localAcessoChamada = ?, coletorChamada = ?, diasrenovacaoautomaticaantecipada = ?,"
                + "mostrarMensagemValoresRodape = ?, cobrarAutomaticamenteMultaJuros = ?, "
                + "multaCobrancaAutomatica = ?, jurosCobrancaAutomatica = ?, liberarPersonalProfessorDebito= ?, consultorSite = ?, criarBvVendaSite = ?, "
                + "ativa = ?, emiteNFSEPorDataCompensacao = ?, pagarComissaoSeAtingirMetaFinanceira= ?, pagarComissaoManutencaoModalidade = ?,"
                + "somenteVendaProdutosComEstoque= ?, permiteAlterarDataEmissaoNFSe = ?, minutosAposUltimoAcessoDiminuirCredito = ?, modeloMensagemVendasOnline = ?, cancelamentoObrigatoriedadePagamento = ?,\n"
                + "cancelamentoAntecipado = ?, cancelamentoAntecipadoDias = ?, cancelamentoAntecipadoMulta = ?, cancelamentoAntecipadoPlanos = ?, cancelamentoAntecipadoPlanosMulta = ?,\n"
                + "cancelamentoAntecipadoPlanosData = ?, enviarEmailCancelamento = ?, permitecontratopospagorenovacaoauto = ?, tipoCobrancaPacto = ?, gerarCobrancaAutomaticaPacto = ?,\n"
                + "dtUltimaCobrancaPacto = ?, qtdDiasFechamentoCobrancaPacto = ?, valorCreditoPacto = ?, gerarNotaFiscalCobrancaPacto = ?, qtdParcelasCobrancaPacto = ?,\n"
                + "qtdCreditoRenovarPrePagoCobrancaPacto = ?, qtdDiasParaLiberacaoDeVagaEmTrancamento = ?,\n"
                + "tentativasliberarparcelavencida = ?, propagandaBoleto = ?, alturaPropagandaBoleto = ?, larguraPropagandaBoleto = ?, qtdDiasVencimentoBoleto = ?,\n"
                + "usarDataInicioDeContratoNoBI_ICV = ?, existemNovosBoletosPacto = ?, tipoParcelasCobrarVendaSite = ?, convenioBoletoPadrao=?, gerarLoginAPIAoIncluirContrato=?, modeloMensagemEsqueciMinhaSenhaVendasOnline=?, "
                + "tipoParcelaCancelamento = ?, quantidadeParcelasSeguidasCancelamento = ?, enviarNotaCidadeEmpresa = ?, permitirmaillinggerarautorizacaocobrancaboleto = ?, gerarNFSeContaCorrente = ?, "
                + "inscMunicipal = ?, sequencialLoteRPS = ?, permiteGerarArquivoLoteRPS = ?, permiteGerarNotaManual = ?, impedirVendaContratoPorConflitoReposicao = ?, usarNFCe= ?, "
                + "utilizarNomeResponsavelNoBoleto = ?, gerarquitacaocancelamentoauto = ?, gerarquitacaocancelamentoproporcional = ?, pagarComissaoProduto = ?, alterarDataHoraCheckGestaoPersonal = ?,\n"
                + "senhaAcessoOnzeDigitos = ?, naorenovarcontratosemindicefinanceiro = ?, codigoGymPass = ?, tokenApiGymPass = ?, gerarRemessaContratoCancelado = ?, habilitarSomaDeAulaNaoVigente = ?, codigoChaveIntegracaoDigitais = ?, definirCpfComoSenhaCatraca=?, \n"
                + "mostrarnotapordiacompetencia = ?, pontosalunoacesso = ?,pontosalunoacessochuva = ?,pontosalunoacessofrio = ?,pontosalunoacessocalor = ?, trabalharComPontuacao = ?, retirarEdicaoPagamento = ?,adicionarAulasDesmarcadasContratoAnterior = ?, \n"
                + "tipoParcelaCancelamentoForaRegimeRecorrencia=?, quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia=?, quantidadeDiasUteisAposVencimentoParaCancelarContrato=?, limiteInicialItensBIPendencia=?, tempoSaidaAcademia = ?, tokensmsshortcode = ?, permiteRenovarContratoViaAPP = ?, "
                + "valorMensalEmitirNFSe = ?, habilitarReenvioAutomaticoRemessa = ?, emitirNotaSomenteRecorrencia = ?, emitirNomeAlunoNotaFamilia = ?, "
                + "cancelamentoAntecipadoContratosDepoisDe = ?, irTelaPagamentoCartaoCreditoRecorrente = ?, enviarNFCeAutomatico = ?, emitirNFCeSomenteRecorrencia = ?, "
                + "usaintegracoescrm = ?, tokenbuzzlead = ?, urlLinkSiteCadastro = ?, tiposProduto = ?, usarDataOriginalCompensacaoNFSe = ?, "
                + "utilizaSistemaEstacionamento = ?, permitirLancarVariasParcelasSaldoDevedor = ?, produtoEmissaoNFCeFinanceiro = ?, produtoEmissaoNFSeFinanceiro = ?, qtdExecucoesRetentativa = ?, usarNFCePorPagamento = ?, consultarDiasAnterioresNFSe = ?, consultarDiasAnterioresNFCe = ?, "
                + "notasAutoPgRetroativo = ?, validarVencimentoCartaoAutorizacao = ?, permitirEstornarContratoComParcelasPG = ?, permMarcarAulaFeriado = ?, horaAberturaFeriado = ?, horaFechamentoFeriado = ?, "
                + "formasPagamentoNFCe = ?, formasPagamentoNFSe = ?, considerarSomenteParcelasPlanos = ?, cobrarMultaJurosTransacao = ?, cobrarMultaJurosDCO = ?, cobrarMultaJurosDCC = ?, addAutoClienteTreinoWeb = ?, "
                + "usarParceiroFidelidade = ?, envioNotificacaoNotasNFSe = ?, envioNotificacaoNotasNFCe = ?, emailsNotificacaoAutomaticaNotas = ?, emailNotificacaoVendasOnline = ?, validarCertificado = ?, "
                + "bloquearAcessoArmarioVigenciaVencida = ?, utilizarJurosValorAbsoluto = ?, concessao_dia_extra = ?, email_movidesk = ?, sincronizado_movidesk = ?, usarConciliadora = ?, "
                + "empresaConciliadora = ?, senhaConciliadora = ?, diasParaRetirarRelFechamentoDeCaixa = ?, moeda = ?, descmoeda = ?, locale = ?, "
                + "configuracaoNotaFiscalNFSe = ?, configuracaoNotaFiscalNFCe = ?, usarNomeResponsavelNFCe = ?, tipoProdutoEmissaoNFSe = ?, tipoProdutoEmissaoNFCe = ?, naocobrarmultadecontratorenovado = ?, naocobrarmultadetodasparcelaspagas = ?, "
                + "minCreditarProximoPontoClubeVantagens=?, apenasPrimeiroAcessoClubeVantagens=?, zerarPontosAposVencimento=?, pontuarApenasCampanhasAtivas=?, aplicarIndicacaoQlqrPlano=?, emitirDuplicataNFSe = ?,utilizardatacancelamentovalidarparcela  =?, "
                + "emitirMesReferenciaNFCe = ?, integracaoSpiviHabilitada = ?, integracaoSpiviSourceName = ?, integracaoSpiviSiteID = ?, integracaoSpiviPassword = ?, cod_empresafinanceiro = ?, mostrarvaloreszeradosrel = ?, zerarvalorcancelamentotransferencia=?,"
                + "cobrarCreditoVindi = ?, qtddiaslimitecobrancaparcelasrecorrencia = ?, qtddiasrepetircobrancaparcelasrecorrencia = ?, "
                + "tentativaUnicaDeCobranca = ?,mostrarDescricaoParcelaRenegociada  = ?, acessoSomenteComAgendamento = ?, capacidadeSimultanea = ?, toleranciaAcessoAula = ?, utilizaLeitorCodigoBarras = ?,nomeusuarioamigofit = ?, senhausuarioamigofit = ?, \n"
                + "integracaoMyWellnessUser = ?,integracaoMyWellnessPassword = ?, integracaMyWellneApiKey = ?, integracaoMyWellneHabilitada = ?,  integracaoMyWellnessFacilityUrl = ?, "
                + "agruparParcelasPorCartao = ?, agruparParcelasPorCartaoValorLimite = ?, somenteUmEnvioCartaoTentativa = ?, integracaoMyWellnessEnviarVinculos = ?, integracaoMyWellnessEnviarGrupos = ?, diaVencimentoCobrancaPacto = ?, valorLimiteCaixaAbertoVendaAvulsa = ?, \n"
                + "detalharNiveisModalidades = ?,notificarWebhook = ?, urlWebhookNotificar = ?, \n"
                + "integracaoMentorWebHabilitada = ?, integracaoMentorWebUrl = ?, integracaoMentorWebServico = ?, integracaoMentorWebUser = ?, integracaoMentorWebPassword = ?, integracaoF360Dir = ?, \n"
                + "integracaoF360RelFatHabilitada = ?, integracaoF360FtpServer = ?, integracaoF360FtpPort = ?, integracaoF360User = ?, integracaoF360Password = ?, integracaoF360Quinzenal = ?, \n"
                + "tipovigenciamywellnessgympass = ?, nrdiasvigenciamywellnessgympass = ?, \n"
                + "integracaoAmigoFitHabilitada = ?, permitecadastrarcartaomesmoassim = ?, convenioVerificacaoCartao = ?, \n"
                + "transferida = ?, novachave_transferencia = ?, novocodigo_transferencia = ?, utilizarMultaValorAbsoluto = ?, \n"
                + "permiteMaillingCriarBoleto = ?, conveniocobrancapix = ?, bloquearSemCartaoVacina= ? , idadeMinimaCartaoVacina= ?, \n"
                + "tipoAnexoCartaoVacina = ?, tipoEmpresaFinanceiro = ?, enviarEmailPagamento = ?, cobrarMultaJurosPix = ?, restringirConvidadoUmaVezPorMes = ?, \n"
                + "produtoDayUse = ?, modalidadeDayUse = ?, produtoDiaPlus = ?, tipoPlanoDiaPlus = ?, modalidadeDiaPlus = ?, aplicarMultaeJurosNoCancelamentoAutomatico = ?, aplicarMultaMudancaPlano = ?, bloquearRenovacaoAutomaticaPlanosForaDaVigencia = ?, gerarBoletoCaixaAberto = ?, \n"
                + "cpfCodigoInternoWeHelp = ?, urlEnvioAcesso = ?, naoGerarResiduoCancelamentoAutomatico = ?, depositarResiduoCancelamentoNaContaCorrente = ?, cancelamentoAvaliandoParcelas = ?, tokenEnvioAcesso = ?, usavitio = ?, linkcheckoutvitio = ?, mensagemvitioquercomprar = ?, mensagemvitiowpp = ?, linkebook = ?, convenioCobrancaBoleto = ?, idcontabancariasesi= ?, codexternounidadesesi = ?, \n"
                + "utilizaIntegracaoDelsoft= ?, hostIntegracaoDelsoft= ?, portaIntegracaoDelsoft= ?, tokenIntegracaoDelsoft= ?, nomeAplicacaoDelsoft= ?, usuarioAplicacaoDelsoft = ?, senhaAplicacaoDelsoft = ?, planoAplicacaoDelsoft = ?, emiteValorTotalCompetencia = ?,\n"
                + "codigorede = ?, responderBVNaVendaRapida = ?, aplicarMultaSobreValorTotalContrato = ?, emitirNoNomeResponsavel = ?, cnpjclientesesi = ?, "
                + "cobrarParcelaComBoletoGerado = ?, cobrarParcelaVencidaSemTentativaCobranca = ?, utilizarNomeResponsavelNoBoletoMaiorIdade = ?, conveniocobrancacartao = ?, \n"
                + "qtdDiasEnvioSpc = ?, consultarNovoCadastroSpc = ?, enviarAutomaticoSpc = ?, operadorSpc = ?, senhaSpc = ?, manterMarcacoesFuturasCreditoRenovacao = ?, \n"
                + "cancelamentoApresentarTransacoes = ?, isentarCancelamento7Dias = ?, cancelarContratosNaoRenovaveisForaRecorrencia = ?, codigoAssociadoSpc = ?, limitarDescontosPorPerfil = ?,\n"
                + "registrarTentativasAcesso = ?, pesquisaAutomaticaSPC = ?, cobrarMultaJurosAsaas = ?, valorMultaAsaas = ?, valorJurosAsaas = ?, concContasPagarFacilitePay = ?, utilizarNomeResponsavelNoPixMenorIdade = ?, utilizarNomeResponsavelNoPixMaiorIdade = ?,\n"
                + "cancelamentoantecipadogerarparcelamultaseparada = ?, irTelaPagamentoCartaoCreditoFormaPagamento = ?, bloquearAcessoAlunoParqNaoAssinado = ?, gerarAutCobrancaComCobAutBloqueada = ?, \n"
                + "permitirAlterarDataFinalContratoNoCancelamento = ?, emiteValorTotalFaturamento = ?, gerarNotaFiscalComDesconto = ?, facilitePayReguaCobranca = ?, obrigatorioPreencherCamposCartao = ?, concContasReceberFacilitePay = ?,"
                +" cancelamentoAutomaticoAntecipadoContratoForaRecorrencia = ?, valorMetaFacilitePay = ?, ignorarCodigoDeBarrasEmissaoNfce = ?, facilitePayConciliacaoCartao = ?, diasParaVencimentoParq = ?,\n"
                + "bloquearAcessoSemTermoResponsabilidade = ?, facilitePayStoneConnect = ?, facilitePayCDLSPC = ?, "
                + "facilitePayReguaCobrancaEmail = ?, facilitePayReguaCobrancaSms = ?, facilitePayReguaCobrancaApp = ?, facilitePayReguaCobrancaWhatsApp = ?, bloquearAcessoDiariaEmpresaDiferente = ?, \n"
                + "utilizaGestaoClientesComRestricoes = ?, marcarAutoRecebiveisCartaoChequeCancelamento = ?, habilitarCadastroEmpresaSesi = ?, integracaoNuvemshopNomeApp = ?, integracaoNuvemshopEmail = ?, integracaoNuvemshopTokenAcesso = ?, integracaoNuvemshopHabilitada = ?, integracaoNuvemshopStoreId = ?, permiteCompartilhamentoPlanoClienteAtivoPlanoCredito = ?, \n"
                + "utilizaConfigCancelamentoSesc = ?, habilitarCobrancaAutomaticaNaVenda = ?, bloquearAcessoMatriculaRematriculaTotemSemPagamento=?  , utilizarPactoPrint = ?, validadeMesesCarteirinhaSocio = ?, presidente = ?, superintendente = ?, bloquearAcessoSeDebitoEmConta = ?, exigirAssinaturaDigitalResponsavelFinanceiro = ?, idexterno = ?, toleranciaCancelarContratosNaoAssinados = ?, \n"
                + "convenioCobrancaCartaoRegua = ?, convenioCobrancaPixRegua = ?, convenioCobrancaBoletoRegua = ?, tipoParcelasCobrarVendaSiteRegua = ?, enviarEmailPagamentoRegua = ?, gerarAutCobrancaComCobAutBloqueadaRegua = ?, \n"
                + "nomecurto = ?, habilitarValidacaoHorariosMesmaTurma = ?, bloquearRenovacaoAutomaticaPlanosForaCategoriaCliente = ?, horariocapacidadeporcategoria = ?,  envioNFCeAutomaticoNoPagamento = ? \n"
                + " WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 1;
            sqlAlterar.setString(i++, obj.getNome());
            sqlAlterar.setString(i++, obj.getRazaoSocial());
            sqlAlterar.setString(i++, obj.getEndereco().replaceAll("\\s{2,}", " ").trim()); //a expressão regular substitui tudo que houver mais de 2 espaços em branco por somente um espaço em branco e o trim remove espaço em branco antes e depois da string
            sqlAlterar.setString(i++, obj.getSetor().trim());
            sqlAlterar.setString(i++, obj.getNumero());
            sqlAlterar.setString(i++, obj.getComplemento());
            sqlAlterar.setInt(i++, obj.getCidade().getCodigo());
            sqlAlterar.setInt(i++, obj.getEstado().getCodigo());
            sqlAlterar.setInt(i++, obj.getPais().getCodigo());
            sqlAlterar.setString(i++, obj.getCEP());
            sqlAlterar.setString(i++, obj.getCNPJ());
            sqlAlterar.setString(i++, obj.getInscEstadual());
            sqlAlterar.setString(i++, obj.getTelComercial1());
            sqlAlterar.setString(i++, obj.getTelComercial2());
            sqlAlterar.setString(i++, obj.getTelComercial3());
            sqlAlterar.setString(i++, Uteis.removerEspacosInicioFimString(obj.getEmail()));
            sqlAlterar.setString(i++, obj.getSite());
            sqlAlterar.setString(i++, obj.getFax());

            if (obj.getQuestionarioPrimeiraVisita().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getQuestionarioPrimeiraVisita().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getQuestionarioRetorno().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getQuestionarioRetorno().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getQuestionarioReMatricula().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getQuestionarioReMatricula().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setBoolean(i++, obj.isPermiteSituacaoAtestadoContrato());
            sqlAlterar.setBoolean(i++, obj.isPermiteContratosConcomintante());
            sqlAlterar.setDouble(i++, obj.getJuroParcela());
            sqlAlterar.setDouble(i++, obj.getMulta());
            sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioVista());
            sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRetorno());
            sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRematricula());
            sqlAlterar.setString(i++, obj.getMascaraMatricula());
            sqlAlterar.setInt(i++, obj.getCarenciaRenovacao());
            sqlAlterar.setBytes(i++, obj.getFoto());
            sqlAlterar.setBytes(i++, obj.getFotoRelatorio());
            sqlAlterar.setString(i++, obj.getAlturaFotoEmpresa());
            sqlAlterar.setString(i++, obj.getLarguraFotoEmpresa());
            sqlAlterar.setString(i++, obj.getAlturaFotoRelatorio());
            sqlAlterar.setString(i++, obj.getLarguraFotoRelatorio());
            sqlAlterar.setInt(i++, obj.getNrDiasAvencer());
            sqlAlterar.setInt(i++, obj.getToleranciaPagamento());
            sqlAlterar.setInt(i++, obj.getQtdFaltaPeso1());
            sqlAlterar.setInt(i++, obj.getQtdFaltaInicioPeso2());
            sqlAlterar.setInt(i++, obj.getQtdFaltaTerminoPeso2());
            sqlAlterar.setInt(i++, obj.getQtdFaltaPeso3());
            sqlAlterar.setInt(i++, obj.getSomaDv());
            sqlAlterar.setInt(i++, obj.getCarencia());
            sqlAlterar.setInt(i++, obj.getNrDiasProrata());
            sqlAlterar.setBytes(i++, obj.getFotoEmail());
            sqlAlterar.setString(i++, obj.getAlturaFotoEmail());
            sqlAlterar.setString(i++, obj.getLarguraFotoEmail());
            sqlAlterar.setInt(i++, obj.getToleranciaDiasContratoVencido());
            sqlAlterar.setString(i++, obj.getTimeZoneDefault());
            sqlAlterar.setString(i++, obj.getUrlRecorrencia());
            sqlAlterar.setInt(i++, obj.getNrDiasCompensacao());

            sqlAlterar.setString(i++, obj.getServiceUsuario());
            if (obj.getServiceSenha() != null && !obj.getServiceSenha().isEmpty() && !obj.getServiceSenha().contains("==")) {
                obj.setServiceSenha(Criptografia.encrypt(obj.getServiceSenha(),
                        SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM));
            }
            sqlAlterar.setString(i++, obj.getServiceSenha());
            sqlAlterar.setInt(i++, obj.getToleranciaOcupacaoTurma());
            sqlAlterar.setBoolean(i++, obj.isBloquearAcessoSeParcelaAberta());
            sqlAlterar.setBoolean(i++, obj.getBloquearAcessoSemAssinaturaDigital());
            sqlAlterar.setBoolean(i++, obj.getBloquearAcessoCrefVencido());

            if (obj.getConsultorVendaAvulsa() != null && obj.getConsultorVendaAvulsa().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getConsultorVendaAvulsa().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }

            sqlAlterar.setBoolean(i++, obj.isPermiteHorariosConcorrentesParaProfessor());
            sqlAlterar.setBoolean(i++, obj.isProfessorEmAmbientesDiferentesMesmoHorario());
            sqlAlterar.setBoolean(i++, obj.isMostrarCnpj());
            sqlAlterar.setString(i++, obj.getChaveNFSe());
            sqlAlterar.setBoolean(i++, obj.getUsarNFSe());
            sqlAlterar.setString(i++, obj.getTokenSMS());
            sqlAlterar.setInt(i++, obj.getNrDiasChequeAVista());
            sqlAlterar.setBoolean(i++, obj.isMostrarModalidade());
            sqlAlterar.setBoolean(i++, obj.isBvObrigatorio());
            sqlAlterar.setInt(i++, obj.getTempoAposFaltaReposicao());
            sqlAlterar.setBoolean(i++, obj.isEnviarNFSeAutomatico());
            sqlAlterar.setBoolean(i++, obj.isComissaoMatriculaRematricula());

            if (obj.getQuestionarioPrimeiraCompra().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getQuestionarioPrimeiraCompra().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioPrimeiraCompra());

            if (obj.getQuestionarioRetornoCompra().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getQuestionarioRetornoCompra().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRetornoCompra());

            sqlAlterar.setBoolean(i++, obj.isUsarNFSePorPagamento());
            sqlAlterar.setInt(i++, obj.getQtdDiasCobrarRematricula());
            sqlAlterar.setInt(i++, obj.getQtdVias());
            sqlAlterar.setBoolean(i++, obj.isQuebrarPaginaRecibo());
            sqlAlterar.setBoolean(i++, obj.isDetalharPeriodoProduto());
            sqlAlterar.setBoolean(i++, obj.isDetalharParcelas());
            sqlAlterar.setBoolean(i++, obj.isDetalharPagamentos());
            sqlAlterar.setBoolean(i++, obj.isDetalharDescontos());
            sqlAlterar.setBoolean(i++, obj.isApresentarAssinaturas());
            sqlAlterar.setString(i++, obj.getObservacaoRecibo());
            sqlAlterar.setInt(i++, obj.getTipoGestaoNFSe());
            sqlAlterar.setBoolean(i++, obj.isRetrocederValorMensalPlanoCancelamento());
            sqlAlterar.setBoolean(i++, obj.isFecharNegociacaoSemAutorizacaoDCC());
            sqlAlterar.setInt(i++, obj.getNrDiasDesistenteRemoverVinculoTreino());
            sqlAlterar.setBoolean(i++, obj.isRemoverVinculosAposDesistencia());
            sqlAlterar.setBoolean(i++, obj.isLiberarPersonalComTaxaEmAberto());
            sqlAlterar.setBoolean(i++, obj.isReciboParaImpressoraTermica());
            sqlAlterar.setBoolean(i++, obj.isUsarManutencaoModalidadeComissao());
            sqlAlterar.setBoolean(i++, obj.isDevolucaoEntraNoCaixa());
            sqlAlterar.setBytes(i++, obj.getFotoRedeSocial());
            sqlAlterar.setString(i++, obj.getAlturaFotoRedeSocial());
            sqlAlterar.setString(i++, obj.getLarguraFotoRedeSocial());
            sqlAlterar.setInt(i++, obj.getArredondamento().getId());
            sqlAlterar.setInt(i++, obj.getToleranciaProrata());
            sqlAlterar.setBoolean(i++, obj.isUsarGestaoCreditosPersonal());
            sqlAlterar.setBoolean(i++, obj.isPermiteReposicaoEmTurmasDiferentes());
            sqlAlterar.setBoolean(i++, obj.isForcarMinimoVencimento2parcela());
            sqlAlterar.setBoolean(i++, obj.isPermiteRenovarContratosEmTurmasLotadas());

            sqlAlterar.setBytes(i++, obj.getHomeBackground640x551());
            sqlAlterar.setBytes(i++, obj.getHomeBackground320x276());

            //alturahomeBackground640x551, largurahomeBackground640x551,alturahomeBackground320x276, largurahomeBackground320x276
            sqlAlterar.setString(i++, obj.getAlturahomeBackground640x551());
            sqlAlterar.setString(i++, obj.getLargurahomeBackground640x551());
            sqlAlterar.setString(i++, obj.getAlturahomeBackground320x276());
            sqlAlterar.setString(i++, obj.getLargurahomeBackground320x276());

            sqlAlterar.setString(i++, obj.getLatitude());
            sqlAlterar.setString(i++, obj.getLongitude());

            sqlAlterar.setBoolean(i++, obj.isAcessoChamada());
            if (obj.getLocalAcessoChamada().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getLocalAcessoChamada().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            if (obj.getColetorChamada().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getColetorChamada().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            resolveIntegerNull(sqlAlterar, i++, obj.getDiasRenovacaoAutomaticaAntecipada());
            sqlAlterar.setBoolean(i++, obj.isMostrarMensagemValoresRodape());
            sqlAlterar.setBoolean(i++, obj.getCobrarAutomaticamenteMultaJuros());
            sqlAlterar.setDouble(i++, obj.getMultaCobrancaAutomatica());
            sqlAlterar.setDouble(i++, obj.getJurosCobrancaAutomatica());
            sqlAlterar.setBoolean(i++, obj.getLiberarPersonalProfessorDebito());

            if (obj.getConsultorSite() != null && obj.getConsultorSite().getCodigo() != 0) {
                sqlAlterar.setInt(i++, obj.getConsultorSite().getCodigo());
            } else {
                sqlAlterar.setNull(i++, 0);
            }
            sqlAlterar.setBoolean(i++, obj.isCriarBvVendaSite());
            sqlAlterar.setBoolean(i++, obj.isAtiva());
            sqlAlterar.setBoolean(i++, obj.getEmiteNFSEPorDataCompensacao());
            sqlAlterar.setBoolean(i++, obj.isPagarComissaoSeAtingirMetaFinanceira());
            sqlAlterar.setBoolean(i++, obj.isPagarComissaoManutencaoModalidade());
            sqlAlterar.setBoolean(i++, obj.getSomenteVendaProdutosComEstoque());
            sqlAlterar.setBoolean(i++, obj.isPermiteAlterarDataEmissaoNFSe());
            sqlAlterar.setInt(i++, obj.getMinutosAposUltimoAcessoDiminuirCredito());
            if (obj.getModeloMensagemVendasOnline() == null || obj.getModeloMensagemVendasOnline().getCodigo() == 0) {
                sqlAlterar.setNull(i++, Types.INTEGER);
            } else {
                sqlAlterar.setInt(i++, obj.getModeloMensagemVendasOnline().getCodigo());
            }

            sqlAlterar.setBoolean(i++, obj.isCancelamentoObrigatoriedadePagamento());
            if (obj.isCancelamentoObrigatoriedadePagamento()) {
                sqlAlterar.setBoolean(i++, true);
            } else {
                sqlAlterar.setBoolean(i++, obj.isCancelamentoAntecipado());
            }

            sqlAlterar.setInt(i++, obj.getCancelamentoAntecipadoDias());
            sqlAlterar.setDouble(i++, obj.getCancelamentoAntecipadoMulta());
            sqlAlterar.setString(i++, obj.getCancelamentoAntecipadoPlanos());
            sqlAlterar.setDouble(i++, obj.getCancelamentoAntecipadoPlanosMulta());
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getCancelamentoAntecipadoPlanosData()));
            sqlAlterar.setBoolean(i++, obj.isEnviarEmailCancelamento());
            sqlAlterar.setBoolean(i++, obj.isPermiteContratoPosPagoRenovacaoAuto());
            sqlAlterar.setInt(i++, obj.getTipoCobrancaPacto());
            sqlAlterar.setBoolean(i++, obj.isGerarCobrancaAutomaticaPacto());
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDtUltimaCobrancaPacto()));
            sqlAlterar.setInt(i++, obj.getQtdDiasFechamentoCobrancaPacto());
            sqlAlterar.setDouble(i++, obj.getValorCreditoPacto());
            sqlAlterar.setBoolean(i++, obj.isGerarNotaFiscalCobrancaPacto());
            sqlAlterar.setInt(i++, obj.getQtdParcelasCobrancaPacto());
            sqlAlterar.setInt(i++, obj.getQtdCreditoRenovarPrePagoCobrancaPacto());
            sqlAlterar.setInt(i++, obj.getQtdDiasParaLiberacaoDeVagaEmTrancamento());
            sqlAlterar.setInt(i++, obj.getTentativasLiberarParcelaVencida());

            sqlAlterar.setBytes(i++, obj.getPropagandaBoleto());
            sqlAlterar.setString(i++, obj.getAlturaPropagandaBoleto());
            sqlAlterar.setString(i++, obj.getLarguraPropagandaBoleto());
            sqlAlterar.setInt(i++, obj.getQtdDiasVencimentoBoleto());
            sqlAlterar.setBoolean(i++, obj.isUsarDataInicioDeContratoNoBI_ICV());
            sqlAlterar.setBoolean(i++, obj.isExistemNovosBoletosPacto());
            sqlAlterar.setInt(i++, obj.getTipoParcelasCobrarVendaSite().getId());
            if (UtilReflection.objetoMaiorQueZero(obj, "getConvenioBoletoPadrao().getCodigo()")) {
                sqlAlterar.setInt(i++, obj.getConvenioBoletoPadrao().getCodigo());
            } else {
                sqlAlterar.setNull(i++, Types.NULL);
            }
            sqlAlterar.setBoolean(i++, obj.isGerarLoginAPIAoIncluirContrato());
            if (UtilReflection.objetoMaiorQueZero(obj, "getModeloMensagemEsqueciMinhaSenhaVendasOnline().getCodigo()")) {
                sqlAlterar.setInt(i++, obj.getModeloMensagemEsqueciMinhaSenhaVendasOnline().getCodigo());
            } else {
                sqlAlterar.setNull(i++, Types.NULL);
            }
            sqlAlterar.setString(i++, obj.getTipoParcelaCancelamento());
            sqlAlterar.setInt(i++, obj.getQuantidadeParcelasSeguidasCancelamento());
            sqlAlterar.setBoolean(i++, obj.isEnviarNotaCidadeEmpresa());
            sqlAlterar.setBoolean(i++, obj.isPermitirMaillingGerarAutorizacaoCobrancaBoleto());
            sqlAlterar.setBoolean(i++, obj.isGerarNFSeContaCorrente());
            sqlAlterar.setString(i++, obj.getInscMunicipal());
            sqlAlterar.setInt(i++, obj.getSequencialLoteRPS());
            sqlAlterar.setBoolean(i++, obj.isPermiteGerarArquivoLoteRPS());
            sqlAlterar.setBoolean(i++, obj.isPermiteGerarNotaManual());
            sqlAlterar.setBoolean(i++, obj.isImpedirVendaContratoPorConflitoReposicao());
            sqlAlterar.setBoolean(i++, obj.isUsarNFCe());
            sqlAlterar.setBoolean(i++, obj.isUtilizarNomeResponsavelNoBoleto());
            sqlAlterar.setBoolean(i++, obj.isGerarQuitacaoCancelamentoAuto());
            sqlAlterar.setBoolean(i++, obj.isGerarQuitacaoCancelamentoRemanescente());
            sqlAlterar.setBoolean(i++, obj.isPagarComissaoProdutos());
            sqlAlterar.setBoolean(i++, obj.isAlterarDataHoraCheckGestaoPersonal());
            sqlAlterar.setBoolean(i++, obj.isSenhaAcessoOnzeDigitos());
            sqlAlterar.setBoolean(i++, obj.isNaoRenovarContratoSemIndiceFinanceiro());
            sqlAlterar.setString(i++, obj.getCodigoGymPass().trim());
            sqlAlterar.setString(i++, obj.getTokenApiGymPass().trim());
            sqlAlterar.setBoolean(i++, obj.isGerarRemessaContratoCancelado());
            sqlAlterar.setBoolean(i++, obj.isHabilitarSomaDeAulaNaoVigente());
            if (obj.getCodigoChaveIntegracaoDigitais() != 0) {
                sqlAlterar.setInt(i++, obj.getCodigoChaveIntegracaoDigitais());
            } else {
                sqlAlterar.setNull(i++, Types.NULL);
            }
            sqlAlterar.setBoolean(i++, obj.isDefinirCpfComoSenhaCatraca());
            sqlAlterar.setBoolean(i++, obj.isMostrarNotaPorDiaCompetencia());
            sqlAlterar.setInt(i++, obj.getPontosAlunoAcesso());
            sqlAlterar.setInt(i++, obj.getPontosAlunoAcessoChuva());
            sqlAlterar.setInt(i++, obj.getPontosAlunoAcessoFrio());
            sqlAlterar.setInt(i++, obj.getPontosAlunoAcessoCalor());
            sqlAlterar.setBoolean(i++, obj.isTrabalharComPontuacao());
            sqlAlterar.setBoolean(i++, obj.isRetirarEdicaoPagamento());
            sqlAlterar.setBoolean(i++, obj.isAdicionarAulasDesmarcadasContratoAnterior());
            sqlAlterar.setString(i++, obj.getTipoParcelaCancelamentoForaRegimeRecorrencia());
            sqlAlterar.setInt(i++, obj.getQuantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia());
            sqlAlterar.setInt(i++, obj.getQuantidadeDiasUteisAposVencimentoParaCancelarContrato());
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getLimiteInicialItensBIPendencia()));
            obj.setTempoSaidaAcademia(Uteis.converteHoraParaInteiro(obj.getTempoSaidaAcademiaFormatada()));
            sqlAlterar.setInt(i++, obj.getTempoSaidaAcademia());
            sqlAlterar.setString(i++, obj.getTokenSMSShortCode());
            sqlAlterar.setBoolean(i++, obj.isPermiteRenovarContratoViaAPP());
            sqlAlterar.setDouble(i++, obj.getValorMensalEmitirNFSe());
            sqlAlterar.setBoolean(i++, obj.isHabilitarReenvioAutomaticoRemessa());
            sqlAlterar.setBoolean(i++, obj.isEmitirNotaSomenteRecorrencia());
            sqlAlterar.setBoolean(i++, obj.isEmitirNomeAlunoNotaFamilia());
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getCancelamentoAntecipadoContratosDepoisDe()));
            sqlAlterar.setBoolean(i++, obj.isIrTelaPagamentoCartaoCreditoRecorrente());
            sqlAlterar.setBoolean(i++, obj.isEnviarNFCeAutomatico());
            sqlAlterar.setBoolean(i++, obj.isEmitirNFCeSomenteRecorrencia());
            sqlAlterar.setBoolean(i++, obj.isUsaIntegracoesCrm());
            sqlAlterar.setString(i++, obj.getTokenBuzzLead());
            sqlAlterar.setString(i++, obj.getUrlLinkSiteCadastro());
            sqlAlterar.setString(i++, obj.getTiposProduto());
            sqlAlterar.setBoolean(i++, obj.isUsarDataOriginalCompensacaoNFSe());
            sqlAlterar.setBoolean(i++, obj.isUtilizaSistemaEstacionamento());
            sqlAlterar.setBoolean(i++, obj.isPermitirLancarVariasParcelasSaldoDevedor());
            sqlAlterar.setInt(i++, obj.getProdutoEmissaoNFCeFinanceiro());
            sqlAlterar.setInt(i++, obj.getProdutoEmissaoNFSeFinanceiro());
            sqlAlterar.setInt(i++, obj.getQtdExecucoesRetentativa());
            sqlAlterar.setBoolean(i++, obj.isUsarNFCePorPagamento());
            sqlAlterar.setBoolean(i++, obj.isConsultarDiasAnterioresNFSe());
            sqlAlterar.setBoolean(i++, obj.isConsultarDiasAnterioresNFCe());
            sqlAlterar.setBoolean(i++, obj.isNotasAutoPgRetroativo());
            sqlAlterar.setBoolean(i++, obj.isValidarVencimentoCartaoAutorizacao());
            sqlAlterar.setBoolean(i++, obj.isPermitirEstornarContratoComParcelasPG());
            sqlAlterar.setBoolean(i++, obj.isPermMarcarAulaFeriado());
            sqlAlterar.setString(i++, obj.getHoraAberturaFeriado());
            sqlAlterar.setString(i++, obj.getHoraFechamentoFeriado());
            sqlAlterar.setString(i++, obj.getFormasPagamentoEmissaoNFCe());
            sqlAlterar.setString(i++, obj.getFormasPagamentoEmissaoNFSe());
            sqlAlterar.setBoolean(i++, obj.isConsiderarSomenteParcelasPlanos());
            sqlAlterar.setBoolean(i++, obj.isCobrarMultaJurosTransacao());
            sqlAlterar.setBoolean(i++, obj.isCobrarMultaJurosDCO());
            sqlAlterar.setBoolean(i++, obj.isCobrarMultaJurosDCC());
            sqlAlterar.setBoolean(i++, obj.isAddAutoClienteTreinoWeb());
            sqlAlterar.setBoolean(i++, obj.isUsarParceiroFidelidade());
            sqlAlterar.setBoolean(i++, obj.isEnvioNotificacaoNotasNFSe());
            sqlAlterar.setBoolean(i++, obj.isEnvioNotificacaoNotasNFCe());
            sqlAlterar.setString(i++, obj.getEmailsNotificacaoAutomaticaNotas());
            sqlAlterar.setString(i++, obj.getEmailNotificacaoVendasOnline());
            sqlAlterar.setBoolean(i++, obj.isValidarCertificado());
            sqlAlterar.setBoolean(i++, obj.isBloquearAcessoArmarioVigenciaVencida());
            sqlAlterar.setBoolean(i++, obj.isUtilizarJurosValorAbsoluto());
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataConcessaoDiaExtra()));
            sqlAlterar.setString(i++, obj.getEmailMovidesk());
            sqlAlterar.setBoolean(i++, obj.getSincronizadoMovidesk());
            sqlAlterar.setBoolean(i++, obj.isUsarConciliadora());
            sqlAlterar.setString(i++, obj.getEmpresaConciliadora());
            sqlAlterar.setString(i++, obj.getSenhaConciliadora());
            sqlAlterar.setInt(i++, obj.getDiasParaRetirarRelFechamentoDeCaixa());
            sqlAlterar.setString(i++, obj.getMoeda());
            sqlAlterar.setString(i++, obj.getDescMoeda());
            sqlAlterar.setString(i++, obj.getLocaleTexto());
            resolveIntegerNull(sqlAlterar, i++, obj.getConfiguracaoNotaFiscalNFSe().getCodigo());
            resolveIntegerNull(sqlAlterar, i++, obj.getConfiguracaoNotaFiscalNFCe().getCodigo());
            sqlAlterar.setBoolean(i++, obj.isUsarNomeResponsavelNFCe());
            sqlAlterar.setString(i++, obj.getTipoProdutoEmissaoNFSe());
            sqlAlterar.setString(i++, obj.getTipoProdutoEmissaoNFCe());
            sqlAlterar.setBoolean(i++, obj.isNaoCobrarMultaDeContratoRenovado());
            sqlAlterar.setBoolean(i++, obj.isNaoCobrarMultaDeTodasParcelasPagas());

            sqlAlterar.setInt(i++, obj.getMinutosCreditarProximoPontoClubeVantagens());
            sqlAlterar.setBoolean(i++, obj.isApenasPrimeiroAcessoClubeVantagens());
            sqlAlterar.setInt(i++, obj.getZerarPontosAposVencimento().getCodigo());
            sqlAlterar.setBoolean(i++, obj.isPontuarApenasCategoriasEmCampanhasAtivas());
            sqlAlterar.setBoolean(i++, obj.isAplicarIndicacaoQlqrPlano());
            sqlAlterar.setBoolean(i++, obj.isEmitirDuplicataNFSe());
            sqlAlterar.setBoolean(i++, obj.isUtilizarDataCancelamentoParaValidarParcelas());
            sqlAlterar.setBoolean(i++, obj.isEmitirMesReferenciaNFCe());

            sqlAlterar.setBoolean(i++, obj.isIntegracaoSpiviHabilitada());
            sqlAlterar.setString(i++, obj.getIntegracaoSpiviSourceName());
            sqlAlterar.setInt(i++, obj.getIntegracaoSpiviSiteID());
            sqlAlterar.setString(i++, obj.getIntegracaoSpiviPassword());
            if (obj.getCodEmpresaFinanceiro() != null) {
                sqlAlterar.setInt(i++, obj.getCodEmpresaFinanceiro());
            } else {
                sqlAlterar.setNull(i++, Types.INTEGER);
            }
            sqlAlterar.setBoolean(i++, obj.isMostrarValoresZeradosRel());
            sqlAlterar.setBoolean(i++, obj.isZerarValorCancelamentoTransferencia());
            sqlAlterar.setBoolean(i++, obj.isCobrarCreditoVindi());
            sqlAlterar.setInt(i++, obj.getQtdDiasLimiteCobrancaParcelasRecorrencia());
            sqlAlterar.setInt(i++, obj.getQtdDiasRepetirCobrancaParcelasRecorrencia());
            sqlAlterar.setBoolean(i++, obj.isTentativaUnicaDeCobranca());
            sqlAlterar.setBoolean(i++, obj.isMostrarDescricaoParcelaRenegociada());
            sqlAlterar.setBoolean(i++, obj.isAcessoSomenteComAgendamento());
            sqlAlterar.setInt(i++, obj.getCapacidadeSimultanea());
            sqlAlterar.setInt(i++, obj.getToleranciaAcessoAula());
            sqlAlterar.setBoolean(i++, obj.isUtilizaLeitorCodigoBarras());
            sqlAlterar.setString(i++,obj.getNomeUsuarioAmigoFit());
            sqlAlterar.setString(i++,obj.getSenhaUsuarioAmigoFit());
            sqlAlterar.setString(i++, obj.getIntegracaoMyWellnessUser().trim());
            sqlAlterar.setString(i++, obj.getIntegracaoMyWellnessPassword().trim());
            sqlAlterar.setString(i++, obj.getIntegracaoMyWellnessApiKey().trim());
            sqlAlterar.setBoolean(i++, obj.isIntegracaoMyWellnessHabilitada());
            sqlAlterar.setString(i++, obj.getIntegracaoMyWellnessFacilityUrl().trim());
            sqlAlterar.setBoolean(i++, obj.isAgruparParcelasPorCartao());
            sqlAlterar.setDouble(i++, obj.getAgruparParcelasPorCartaoValorLimite());
            sqlAlterar.setBoolean(i++, obj.isSomenteUmEnvioCartaoTentativa());
            sqlAlterar.setBoolean(i++, obj.isIntegracaoMyWellnessEnviarVinculos());
            sqlAlterar.setBoolean(i++, obj.isIntegracaoMyWellnessEnviarGrupos());
            sqlAlterar.setInt(i++, obj.getDiaVencimentoCobrancaPacto());
            sqlAlterar.setDouble(i++, obj.getValorLimiteCaixaAbertoVendaAvulsa());
            sqlAlterar.setBoolean(i++, obj.isDetalharNiveisModalidades());
            sqlAlterar.setBoolean(i++, obj.isNotificarWebhook());
            sqlAlterar.setString(i++,obj.getUrlWebhookNotificar());

            sqlAlterar.setBoolean(i++, obj.isIntegracaoMentorWebHabilitada());
            sqlAlterar.setString(i++, obj.getIntegracaoMentorWebUrl().trim());
            sqlAlterar.setString(i++, obj.getIntegracaoMentorWebServico().trim());
            sqlAlterar.setString(i++, obj.getIntegracaoMentorWebUser().trim());
            sqlAlterar.setString(i++, obj.getIntegracaoMentorWebPassword().trim());
            sqlAlterar.setString(i++, obj.getIntegracaoF360Dir() != null ? obj.getIntegracaoF360Dir().trim() : "");

            sqlAlterar.setBoolean(i++, obj.isIntegracaoF360RelFatHabilitada());
            sqlAlterar.setString(i++, obj.getIntegracaoF360FtpServer().trim());
            if (obj.getIntegracaoF360FtpPort() != null) {
                sqlAlterar.setInt(i++, obj.getIntegracaoF360FtpPort());
            } else {
                sqlAlterar.setNull(i++, Types.INTEGER);
            }
            sqlAlterar.setString(i++, obj.getIntegracaoF360User().trim());
            sqlAlterar.setString(i++, null);
            sqlAlterar.setBoolean(i++, obj.isIntegracaoF360Quinzenal());

            sqlAlterar.setInt(i++, obj.getTipoVigenciaMyWellnessGymPass().getId());
            sqlAlterar.setInt(i++, obj.getNrDiasVigenciaMyWellnessGymPass());

            sqlAlterar.setBoolean(i++, obj.isIntegracaoAmigoFitHabilitada());
            sqlAlterar.setBoolean(i++, obj.isPermiteCadastrarCartaoMesmoAssim());
            if (UteisValidacao.emptyNumber(obj.getConvenioVerificacaoCartao().getCodigo())) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getConvenioVerificacaoCartao().getCodigo());
            }

            if (obj.isAtiva()) {
                obj.setTransferida(false);
            }
            sqlAlterar.setBoolean(i++, obj.getTransferida());

            if (obj.getTransferida()) {
                sqlAlterar.setString(i++, obj.getNovaChaveTransferencia());
                sqlAlterar.setInt(i++, obj.getNovoCodigoTransferencia());
            } else {
                sqlAlterar.setNull(i++, Types.VARCHAR);
                sqlAlterar.setNull(i++, Types.INTEGER);
            }

            sqlAlterar.setBoolean(i++, obj.isUtilizarMultaValorAbsoluto());
            sqlAlterar.setBoolean(i++, obj.isPermiteMaillingCriarBoleto());
            if (UteisValidacao.emptyNumber(obj.getConvenioCobrancaPix().getCodigo())) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getConvenioCobrancaPix().getCodigo());
            }

            sqlAlterar.setBoolean(i++, obj.getBloquearSemCartaoVacina());
            if (UteisValidacao.emptyNumber(obj.getIdadeMinimaCartaoVacina())) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getIdadeMinimaCartaoVacina());
            }
            if (obj.getTipoAnexoCartaoVacina() == null) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getTipoAnexoCartaoVacina().getCodigo());
            }
            if (obj.getTipoEmpresa() == null) {
                sqlAlterar.setString(i++, TipoEmpresaFinanceiro.NAO_TIPIFICADO.toString());
            } else {
                sqlAlterar.setString(i++, obj.getTipoEmpresa().toString());
            }
            sqlAlterar.setBoolean(i++, obj.isEnviarEmailPagamento());
            sqlAlterar.setBoolean(i++, obj.isCobrarMultaJurosPix());
            sqlAlterar.setBoolean(i++, obj.getRestringirConvidadoUmaVezPorMes());
            if (obj.getProdutoDayUse() == null || obj.getProdutoDayUse().getCodigo() == 0) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getProdutoDayUse().getCodigo());
            }

            if (obj.getModalidadeDayUse() == null || obj.getModalidadeDayUse().getCodigo() == 0) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getModalidadeDayUse().getCodigo());
            }


            if (obj.getProdutoDiaPlus() == null || obj.getProdutoDiaPlus().getCodigo() == 0) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getProdutoDiaPlus().getCodigo());
            }

            if (obj.getTipoPlanoDiaPlus() == null || obj.getTipoPlanoDiaPlus().getCodigo() == 0) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getTipoPlanoDiaPlus().getCodigo());
            }

            if (obj.getModalidadeDiaPlus() == null || obj.getModalidadeDiaPlus().getCodigo() == 0) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getModalidadeDiaPlus().getCodigo());
            }
            sqlAlterar.setBoolean(i++, obj.isAplicarMultaeJurosNoCancelamentoAutomatico());
            sqlAlterar.setBoolean(i++, obj.isAplicarMultaMudancaPlano());
            sqlAlterar.setBoolean(i++, obj.isBloquearRenovacaoAutomaticaPlanosForaDaVigencia());
            sqlAlterar.setBoolean(i++, obj.isGerarBoletoCaixaAberto());
            sqlAlterar.setBoolean(i++, obj.isCpfCodigoInternoWeHelp());
            sqlAlterar.setString(i++, obj.getUrlEnvioAcesso());
            sqlAlterar.setBoolean(i++, obj.isNaoGerarResiduoCancelamentoAutomatico());
            sqlAlterar.setBoolean(i++, obj.isDepositarResiduoCancelamentoNaContaCorrente());
            sqlAlterar.setBoolean(i++, obj.isCancelamentoAvaliandoParcelas());
            sqlAlterar.setString(i++, obj.getTokenEnvioAcesso());
            sqlAlterar.setBoolean(i++, obj.isUsaVitio());
            sqlAlterar.setString(i++, obj.getLinkCheckoutVitio());
            sqlAlterar.setString(i++, obj.getMensagemVitioQuerComprar());
            sqlAlterar.setString(i++, obj.getMensagemVitioWhatsapp());
            sqlAlterar.setString(i++, obj.getLinkEbook());
            resolveIntegerNull(sqlAlterar, i++, obj.getConvenioCobrancaBoleto().getCodigo());

            resolveIntegerNull(sqlAlterar, i++, obj.getIdContabancariaSesi());
            resolveIntegerNull(sqlAlterar, i++, obj.getCodExternoUnidadeSesi());

            sqlAlterar.setBoolean(i++, obj.getUtilizaIntegracaoDelsoft());
            sqlAlterar.setString(i++, obj.getHostIntegracaoDelsoft());
            sqlAlterar.setInt(i++, obj.getPortaIntegracaoDelsoft());
            sqlAlterar.setString(i++, obj.getTokenIntegracaoDelsoft());
            sqlAlterar.setString(i++, obj.getNomeAplicacaoDelsoft());
            sqlAlterar.setString(i++, obj.getUsuarioAplicacaoDelsoft());
            sqlAlterar.setString(i++, obj.getSenhaAplicacaoDelsoft());

            if (obj.getPlanoAplicacaoDelsoft() == null || obj.getPlanoAplicacaoDelsoft().getCodigo() == 0) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setInt(i++, obj.getPlanoAplicacaoDelsoft().getCodigo());
            }
            sqlAlterar.setBoolean(i++, obj.isEmiteValorTotalCompetencia());
            if (UteisValidacao.emptyString(obj.getCodigoRede())) {
                sqlAlterar.setNull(i++, 0);
            } else {
                sqlAlterar.setString(i++, obj.getCodigoRede());
            }
            sqlAlterar.setBoolean(i++, obj.isResponderBVNaVendaRapida());
            sqlAlterar.setBoolean(i++, obj.isAplicarMultaSobreValorTotalContrato());
            sqlAlterar.setBoolean(i++, obj.isEmitirNoNomeResponsavel());
            sqlAlterar.setString(i++, obj.getCnpjClienteSesi());
            sqlAlterar.setBoolean(i++, obj.isCobrarParcelaComBoletoGerado());
            sqlAlterar.setBoolean(i++, obj.isCobrarParcelaVencidaSemTentativaCobranca());
            sqlAlterar.setBoolean(i++, obj.isUtilizarNomeResponsavelNoBoletoMaiorIdade());
            resolveIntegerNull(sqlAlterar, i++, obj.getConvenioCobrancaCartao().getCodigo());

            sqlAlterar.setInt(i++, obj.getQtdDiasEnvioSPC());
            sqlAlterar.setBoolean(i++, obj.isConsultarNovoCadastroSPC());
            sqlAlterar.setBoolean(i++, obj.isEnvioAutomaticoSPC());
            sqlAlterar.setString(i++, obj.getOperadorSpc());
            sqlAlterar.setString(i++, obj.getSenhaSpc());
            sqlAlterar.setBoolean(i++, obj.isManterMarcacoesFuturasCreditoRenovacao());
            sqlAlterar.setBoolean(i++, obj.isCancelamentoApresentarTransacoes());
            sqlAlterar.setBoolean(i++, obj.isIsentarCancelamento7Dias());
            sqlAlterar.setBoolean(i++, obj.isCancelarContratosNaoRenovaveisForaRecorrencia());
            resolveLongNull(sqlAlterar, i++, obj.getCodigoAssociadoSpc());
            sqlAlterar.setBoolean(i++, obj.isLimitarDescontosPorPerfil());
            sqlAlterar.setBoolean(i++, obj.isRegistrarTentativasAcesso());
            sqlAlterar.setBoolean(i++, obj.isPesquisaAutomaticaSPC());
            sqlAlterar.setBoolean(i++, obj.isCobrarMultaJurosAsaas());
            sqlAlterar.setDouble(i++, obj.getValorMultaAsaas());
            sqlAlterar.setDouble(i++, obj.getValorJurosAsaas());
            sqlAlterar.setBoolean(i++, obj.isConcContasPagarFacilitePay());
            sqlAlterar.setBoolean(i++, obj.isUtilizarNomeResponsavelNoPixMenorIdade());
            sqlAlterar.setBoolean(i++, obj.isUtilizarNomeResponsavelNoPixMaiorIdade());
            sqlAlterar.setBoolean(i++, obj.isCancelamentoAntecipadoGerarParcelaMultaSeparada());
            sqlAlterar.setBoolean(i++, obj.isIrTelaPagamentoCartaoCreditoFormaPagamento());
            sqlAlterar.setBoolean(i++, obj.getBloquearAcessoAlunoParqNaoAssinado());
            sqlAlterar.setBoolean(i++, obj.isGerarAutCobrancaComCobAutBloqueada());
            sqlAlterar.setBoolean(i++, obj.isPermitirAlterarDataFinalContratoNoCancelamento());
            sqlAlterar.setBoolean(i++, obj.isEmiteValorTotalFaturamento());
            sqlAlterar.setBoolean(i++, obj.isGerarNotaFiscalComDesconto());
            sqlAlterar.setBoolean(i++, obj.isFacilitePayReguaCobranca());
            sqlAlterar.setBoolean(i++, obj.isObrigatorioPreencherCamposCartao());
            sqlAlterar.setBoolean(i++, obj.isConcContasReceberFacilitePay());
            sqlAlterar.setBoolean(i++, obj.isCancelamentoAutomaticoAntecipadoContratoForaRecorrencia());
            sqlAlterar.setDouble(i++, obj.getValorMetaFacilitePay());
            sqlAlterar.setBoolean(i++, obj.isIgnorarCodigoDeBarrasEmissaoNfce());
            sqlAlterar.setBoolean(i++, obj.isFacilitePayConciliacaoCartao());
            sqlAlterar.setInt(i++, obj.getDiasParaVencimentoParq());
            sqlAlterar.setBoolean(i++, obj.getBloquearAcessoSemTermoResponsabilidade());
            sqlAlterar.setBoolean(i++, obj.isFacilitePayStoneConnect());
            sqlAlterar.setBoolean(i++, obj.isFacilitePayCDLSPC());
            sqlAlterar.setBoolean(i++, obj.isFacilitePayReguaCobrancaEmail());
            sqlAlterar.setBoolean(i++, obj.isFacilitePayReguaCobrancaSms());
            sqlAlterar.setBoolean(i++, obj.isFacilitePayReguaCobrancaApp());
            sqlAlterar.setBoolean(i++, obj.isFacilitePayReguaCobrancaWhatsApp());
            sqlAlterar.setBoolean(i++, obj.getBloquearAcessoDiariaEmpresaDiferente());
            sqlAlterar.setBoolean(i++, obj.isUtilizaGestaoClientesComRestricoes());
            sqlAlterar.setBoolean(i++, obj.isMarcarAutoRecebiveisCartaoChequeCancelamento());
            sqlAlterar.setBoolean(i++, obj.isHabilitarCadastroEmpresaSesi());
            sqlAlterar.setString(i++, obj.getIntegracaoNuvemshopNomeApp());
            sqlAlterar.setString(i++, obj.getIntegracaoNuvemshopEmail());
            sqlAlterar.setString(i++, obj.getIntegracaoNuvemshopTokenAcesso());
            sqlAlterar.setBoolean(i++, obj.isIntegracaoNuvemshopHabilitada());
            sqlAlterar.setString(i++, obj.getIntegracaoNuvemshopStoreId());
            sqlAlterar.setBoolean(i++, obj.isPermiteCompartilhamentoPlanoClienteAtivoPlanoCredito());
            sqlAlterar.setBoolean(i++, obj.isUtilizaConfigCancelamentoSesc());
            sqlAlterar.setBoolean(i++, obj.isHabilitarCobrancaAutomaticaNaVenda());
            sqlAlterar.setBoolean(i++, obj.isBloquearAcessoMatriculaRematriculaTotemSemPagamento());
            sqlAlterar.setBoolean(i++, obj.getUtilizarPactoPrint());
            sqlAlterar.setInt(i++, obj.getValidadeMesesCarteirinhaSocio());
            sqlAlterar.setString(i++, obj.getPresidente());
            sqlAlterar.setString(i++, obj.getSuperintendente());
            sqlAlterar.setBoolean(i++, obj.isBloquearAcessoSeDebitoEmConta());
            sqlAlterar.setBoolean(i++, obj.isExigirAssinaturaDigitalResponsavelFinanceiro());
            sqlAlterar.setString(i++, obj.getIdExterno());
            sqlAlterar.setInt(i++, obj.getToleranciaCancelarContratosNaoAssinados());
            resolveIntegerNull(sqlAlterar, i++, obj.getConvenioCobrancaCartaoRegua().getCodigo());
            resolveIntegerNull(sqlAlterar, i++, obj.getConvenioCobrancaPixRegua().getCodigo());
            resolveIntegerNull(sqlAlterar, i++, obj.getConvenioCobrancaBoletoRegua().getCodigo());
            sqlAlterar.setInt(i++, obj.getTipoParcelasCobrarVendaSiteRegua().getId());
            sqlAlterar.setBoolean(i++, obj.isEnviarEmailPagamentoRegua());
            sqlAlterar.setBoolean(i++, obj.isGerarAutCobrancaComCobAutBloqueadaRegua());
            sqlAlterar.setString(i++, obj.getNomeCurto());
            sqlAlterar.setBoolean(i++, obj.getHabilitarValidacaoHorariosMesmaTurma());
            sqlAlterar.setBoolean(i++, obj.isBloquearRenovacaoAutomaticaPlanosForaCategoriaCliente());
            sqlAlterar.setBoolean(i++, obj.isHorariocapacidadeporcategoria());

            sqlAlterar.setBoolean(i++, obj.isEnvioNFCeAutomaticoNoPagamento());


            sqlAlterar.setInt(i, obj.getCodigo());
            sqlAlterar.execute();
            getFacade().getContaCorrenteEmpresa().alterarContaCorrenteEmpresas(obj.getCodigo(), obj.getContaCorrenteEmpresaVOs());
            getFacade().getProdutoDevolverCancelamentoEmpresa().alterarProdutosDevolverCancelEmpresas(obj.getCodigo(), obj.getProdutoDevolverCancelamentoEmpresaVOS());
            getFacade().getConfiguracaoReenvioMovParcelaEmpresa().alterarConfiguracaoReenvioMovParcelaEmpresa(obj.getCodigo(), obj.getConfiguracaoReenvioMovParcelaEmpresaVOS());
            if(!obj.isAtiva()){
                getFacade().getConfiguracaoNotaFiscal().desabilitarEmpresaInativaEnotas(obj.getCodigo());
            }
            inserirConfigsGestaoPersonal(obj);
            tratarEdicaoConfiguracaoRD(obj);
            inserirConfigEstacionamento(obj);
            gravarParceiroFidelidade(obj);
            Pessoa.atualizarNomePessoaEmpresaFinan(obj, con);

            // responsável por manter a consistência entre as Configurações do Sistema
            // e as configurações em EMPRESA quando existir apenas uma empresa.
            try (PreparedStatement count = con.prepareStatement("SELECT count(*) AS qtdEmpresa FROM empresa")) {
                try (ResultSet resultSet = count.executeQuery()) {
                    resultSet.next();
                    if (resultSet.getInt("qtdEmpresa") == 1) {
                        try (PreparedStatement sqlAlterarConf = prepararAlterarConfiguracaoSistema(obj, 1)) {
                            sqlAlterarConf.execute();
                        }
                    }
                }
            }
        }
        reiniciarServicosNovaEmpresaOuAtivaInativa();
    }

    private PreparedStatement prepararAlterarConfiguracaoSistema(EmpresaVO obj, final int codigoEmpresa) throws SQLException {
        PreparedStatement sqlAlterar = con.prepareStatement("UPDATE ConfiguracaoSistema SET questionarioPrimeiraVisita = ?, "
                + "questionarioRetorno = ?, questionarioReMatricula = ?, nrDiasVigenteQuestionarioVisita = ?, "
                + "nrDiasVigenteQuestionarioRetorno = ?, nrDiasVigenteQuestionarioRematricula = ?, mascaraMatricula = ?, "
                + "carenciaRenovacao = ?, nrDiasAvencer = ?, toleranciapagamento = ?, qtdFaltaPeso1 = ?, qtdFaltaInicioPeso2 = ?, "
                + "qtdFaltaTerminoPeso2 = ?, qtdFaltaPeso3 = ?, carencia = ?, nrDiasProrata = ?, toleranciaDiasContratoVencido = ?, "
                + "urlrecorrencia = ?, bloquearacessoseparcelaaberta = ?, questionarioPrimeiraCompra = ?, "
                + "nrDiasVigenteQuestionarioPrimeiraCompra = ?, questionarioRetornoCompra = ?, nrDiasVigenteQuestionarioRetornoCompra = ? "
                + " WHERE codigo = ?");
        int i = 1;
        if (obj.getQuestionarioPrimeiraVisita().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioPrimeiraVisita().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (obj.getQuestionarioRetorno().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioRetorno().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        if (obj.getQuestionarioReMatricula().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioReMatricula().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }

        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioVista());
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRetorno());
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRematricula());
        sqlAlterar.setString(i++, obj.getMascaraMatricula());
        sqlAlterar.setInt(i++, obj.getCarenciaRenovacao());
        sqlAlterar.setInt(i++, obj.getNrDiasAvencer());
        sqlAlterar.setInt(i++, obj.getToleranciaPagamento());
        sqlAlterar.setInt(i++, obj.getQtdFaltaPeso1());
        sqlAlterar.setInt(i++, obj.getQtdFaltaInicioPeso2());
        sqlAlterar.setInt(i++, obj.getQtdFaltaTerminoPeso2());
        sqlAlterar.setInt(i++, obj.getQtdFaltaPeso3());
        sqlAlterar.setInt(i++, obj.getCarencia());
        sqlAlterar.setInt(i++, obj.getNrDiasProrata());
        sqlAlterar.setInt(i++, obj.getToleranciaDiasContratoVencido());
        sqlAlterar.setString(i++, obj.getUrlRecorrencia());
        sqlAlterar.setBoolean(i++, obj.isBloquearAcessoSeParcelaAberta());

        if (obj.getQuestionarioPrimeiraCompra().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioPrimeiraCompra().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioPrimeiraCompra());

        if (obj.getQuestionarioRetornoCompra().getCodigo() != 0) {
            sqlAlterar.setInt(i++, obj.getQuestionarioRetornoCompra().getCodigo());
        } else {
            sqlAlterar.setNull(i++, 0);
        }
        sqlAlterar.setInt(i++, obj.getNrDiasVigenteQuestionarioRetornoCompra());

        sqlAlterar.setInt(i++, codigoEmpresa);
        return sqlAlterar;
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.EmpresaInterfaceFacade#alterar(negocio.comuns.basico.EmpresaVO)
     */
    public void alterar(EmpresaVO obj) throws Exception {
        this.alterar(obj, false);

    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>EmpresaVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>EmpresaVO</code> que será removido no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(EmpresaVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (!centralEventos) {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM Empresa WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            getFacade().getContaCorrenteEmpresa().excluirContaCorrenteEmpresas(obj.getCodigo());
            getFacade().getConfiguracaoReenvioMovParcelaEmpresa().excluirConfiguracaoReenvioMovParcelaEmpresa(obj.getCodigo());

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.basico.EmpresaInterfaceFacade#alterar(negocio.comuns.basico.EmpresaVO)
     */

    public void excluir(EmpresaVO obj) throws Exception {
        this.excluir(obj, false);

    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Empresa</code> através do valor do atributo
     * <code>descricao</code> da classe
     * <code>Questionario</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorQuestionarioPrimeiraVisita(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Empresa.* FROM Empresa, Questionario WHERE Empresa.questionarioPrimeiraVisita = Questionario.codigo and upper( Questionario.nomeinterno ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Questionario.nomeinterno";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Empresa</code> através do valor do atributo
     * <code>descricao</code> da classe
     * <code>Questionario</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorQuestionarioRetorno(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Empresa.* FROM Empresa, Questionario WHERE Empresa.questionarioRetorno = Questionario.codigo and upper( Questionario.nomeInterno ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Questionario.nomeInterno";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Empresa</code> através do valor do atributo
     * <code>descricao</code> da classe
     * <code>Questionario</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     * @exception Execption Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    /**
     * Responsável por realizar uma consulta de
     * <code>Empresa</code> através do valor do atributo
     * <code>descricao</code> da classe
     * <code>Questionario</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorQuestionarioRematricula(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Empresa.* FROM Empresa, Questionario WHERE Empresa.questionarioRematricula = Questionario.codigo and upper( Questionario.nomeinterno ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Questionario.nomeinterno";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Empresa</code> através do valor do atributo
     * <code>String inscEstadual</code>. Retorna os objetos, com início do valor
     * do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorInscEstadual(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Empresa.* FROM Empresa WHERE upper( inscEstadual ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY inscEstadual";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Empresa</code> através do valor do atributo
     * <code>Integer cidade</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCidade(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Empresa.* FROM Empresa, Cidade WHERE Empresa.cidade = cidade.codigo and upper( cidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY cidade.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Empresa</code> através do valor do atributo
     * <code>String razaoSocial</code>. Retorna os objetos, com início do valor
     * do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorRazaoSocial(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Empresa.* FROM Empresa WHERE upper( razaoSocial ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY razaoSocial";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }


    public List<EmpresaVO> consultarPorNome(String nome,final Boolean situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorNome(nome,situacao, controlarAcesso, nivelMontarDados, null);
    }

    public List<EmpresaVO> consultarPorNomeClubeVantagens(String nome,final Boolean situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        List<EmpresaVO> trabalhanComPontuacao = new ArrayList<>();
        for (EmpresaVO empresaVO : consultarPorNome(nome, situacao, controlarAcesso, nivelMontarDados, null)) {
            if (empresaVO.isTrabalharComPontuacao())
                trabalhanComPontuacao.add(empresaVO);
        }
        return trabalhanComPontuacao;
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Empresa</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List<EmpresaVO> consultarPorNome(String nome,final Boolean situacao, boolean controlarAcesso, int nivelMontarDados, Integer codigoEmpresaNaoConsultar) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Empresa.* FROM Empresa WHERE upper( nome ) like('%" + nome.toUpperCase() + "%') ";
        if(situacao != null){
            sqlStr += "and ativa = "+situacao;
        }

        if(codigoEmpresaNaoConsultar != null){
            sqlStr += " and codigo != "+codigoEmpresaNaoConsultar;
        }

        sqlStr += " ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     *
     * Consulta e apresenta todos os dados da tabela empresa.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNomeLogin(String valorConsulta,final Boolean situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Empresa WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ";
        if(situacao != null){
            sqlStr += "and ativa = "+situacao;
        }
        sqlStr += " ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public EmpresaVO consultarPorNomeEmpresa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Empresa.* FROM Empresa WHERE upper( nome ) like ('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Empresa</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List<EmpresaVO> consultarPorCodigo(Integer valorConsulta, final Boolean situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT Empresa.* FROM Empresa WHERE codigo >= " + valorConsulta + "  ";
        if(situacao != null){
            sqlStr += "and ativa = "+situacao;
        }
        sqlStr += " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /*author   : Ulisses
     * Data    : 16/02/11
     * Objetivo: Consultar uma empresa pelo código
     */
    public EmpresaVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception {
        EmpresaVO empresa = null;
        String sqlStr = "SELECT Empresa.* FROM Empresa WHERE codigo = " + valorConsulta;
        try (Statement stm = con.createStatement()) {
            try (ResultSet resultDados = stm.executeQuery(sqlStr)) {
                if (resultDados.next()) {
                    empresa = montarDados(resultDados, nivelMontarDados, this.con);
                }
            }
        }

        return empresa;
    }

    public boolean isReenvioAutomaticoDeCobranca(Integer codEmpresa) throws Exception {
        EmpresaVO empresa = null;
        String sqlStr = "SELECT Empresa.habilitarreenvioautomaticoremessa FROM Empresa WHERE codigo = " + codEmpresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet resultDados = stm.executeQuery(sqlStr)) {
                if (resultDados.next()) {
                    return resultDados.getBoolean("habilitarreenvioautomaticoremessa");
                }
            }
        }
        return false;
    }

    public EmpresaVO consultarPorCnpj(String valorConsulta, int nivelMontarDados) throws Exception {
        EmpresaVO empresa = null;
        valorConsulta = Uteis.formatarCpfCnpj(valorConsulta, true).replace("'", "\\'");
        String sqlStr = "SELECT Empresa.* FROM Empresa WHERE replace(replace(replace(cnpj, '.', ''), '/', ''), '-', '') = '" + valorConsulta + "' order by codigo desc ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet resultDados = stm.executeQuery(sqlStr)) {
                if (resultDados.next()) {
                    empresa = montarDados(resultDados, nivelMontarDados, this.con);
                }
            }
        }

        return empresa;
    }

    public List<EmpresaVO> consultarTodas(final Boolean situacao,int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT Empresa.* FROM Empresa ";
        sqlStr = addFiltroEmpresaAtiva(situacao, sqlStr);
        sqlStr += " order by codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public int quantidadeEmpresas(final Boolean situacao) throws Exception {
        String sql = "SELECT COUNT(*) as qtde FROM Empresa ";
        sql = addFiltroEmpresaAtiva(situacao, sql);
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                rs.next();
                return rs.getInt("qtde");
            }
        }
    }

    private String addFiltroEmpresaAtiva(Boolean situacao, String sqlStr) {
        if(situacao != null){
            sqlStr += " where ativa =" + situacao.toString();
        }
        return sqlStr;
    }

    public int countEmpresas(final Boolean situacao) throws Exception {
        String sqlStr = "SELECT count(codigo) as total FROM Empresa ";
        sqlStr = addFiltroEmpresaAtiva(situacao, sqlStr);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (tabelaResultado.next())
                    return tabelaResultado.getInt("total");
            }
        }

        return 0;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>EmpresaVO</code>
     * resultantes da consulta.
     */
    public static List<EmpresaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<EmpresaVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            EmpresaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static EmpresaVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        EmpresaVO obj = new EmpresaVO();

        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.setRazaoSocial(dadosSQL.getString("razaoSocial"));
        obj.setEndereco(dadosSQL.getString("endereco"));
        obj.setSetor(dadosSQL.getString("setor"));
        obj.setNumero(dadosSQL.getString("numero"));
        obj.setComplemento(dadosSQL.getString("complemento"));
        obj.getCidade().setCodigo(dadosSQL.getInt("cidade"));
        obj.getEstado().setCodigo(dadosSQL.getInt("estado"));
        obj.getPais().setCodigo(dadosSQL.getInt("pais"));
        obj.setCEP(dadosSQL.getString("CEP"));
        obj.setCNPJ(dadosSQL.getString("CNPJ"));
        obj.setMascaraMatricula(dadosSQL.getString("mascaraMatricula"));
        obj.setInscEstadual(dadosSQL.getString("inscEstadual"));
        obj.setTelComercial1(dadosSQL.getString("telComercial1"));
        obj.setTelComercial2(dadosSQL.getString("telComercial2"));
        obj.setTelComercial3(dadosSQL.getString("telComercial3"));
        obj.setEmail(dadosSQL.getString("email"));
        obj.setSite(dadosSQL.getString("site"));
        obj.setFax(dadosSQL.getString("fax"));
        obj.getQuestionarioPrimeiraVisita().setCodigo(dadosSQL.getInt("questionarioPrimeiraVisita"));
        obj.getQuestionarioRetorno().setCodigo(dadosSQL.getInt("questionarioRetorno"));
        obj.getQuestionarioReMatricula().setCodigo(dadosSQL.getInt("questionarioReMatricula"));
        obj.setNrDiasVigenteQuestionarioVista(dadosSQL.getInt("nrDiasVigenteQuestionarioVisita"));
        obj.setNrDiasVigenteQuestionarioRetorno(dadosSQL.getInt("nrDiasVigenteQuestionarioRetorno"));
        obj.setNrDiasVigenteQuestionarioRematricula(dadosSQL.getInt("nrDiasVigenteQuestionarioRematricula"));
        obj.setPermiteSituacaoAtestadoContrato(dadosSQL.getBoolean("permiteSituacaoAtestadoContrato"));
        obj.setPermiteContratosConcomintante(dadosSQL.getBoolean("permiteContratosConcomintante"));
        obj.setJuroParcela(dadosSQL.getDouble("juroParcela"));
        obj.setMulta(dadosSQL.getDouble("multa"));
        obj.setSomaDv(dadosSQL.getInt("somadv"));
        obj.setAlturaFotoEmpresa(dadosSQL.getString("alturafotoempresa"));
        obj.setLarguraFotoEmpresa(dadosSQL.getString("largurafotoempresa"));
        obj.setAlturaFotoRelatorio(dadosSQL.getString("alturafotorelatorio"));
        obj.setLarguraFotoRelatorio(dadosSQL.getString("largurafotorelatorio"));
        obj.setAlturaFotoEmail(dadosSQL.getString("alturaFotoEmail"));
        obj.setLarguraFotoEmail(dadosSQL.getString("larguraFotoEmail"));
        obj.setNrDiasAvencer(dadosSQL.getInt("nrDiasAvencer"));
        obj.setCarenciaRenovacao(dadosSQL.getInt("carenciaRenovacao"));
        obj.setToleranciaPagamento(dadosSQL.getInt("toleranciaPagamento"));
        obj.setQtdFaltaPeso1(dadosSQL.getInt("qtdFaltaPeso1"));
        obj.setQtdFaltaInicioPeso2(dadosSQL.getInt("qtdFaltaInicioPeso2"));
        obj.setQtdFaltaTerminoPeso2(dadosSQL.getInt("qtdFaltaTerminoPeso2"));
        obj.setQtdFaltaPeso3(dadosSQL.getInt("qtdFaltaPeso3"));
        obj.setCarencia(dadosSQL.getInt("carencia"));
        obj.setNrDiasProrata(dadosSQL.getInt("nrDiasProrata"));
        obj.setToleranciaDiasContratoVencido(dadosSQL.getInt("toleranciaDiasContratoVencido"));
        obj.setTimeZoneDefault(dadosSQL.getString("timeZoneDefault"));
        try {

            obj.setUsaVitio(dadosSQL.getBoolean("usavitio"));
            obj.setLinkCheckoutVitio(dadosSQL.getString("linkcheckoutvitio"));
            obj.setLinkEbook(dadosSQL.getString("linkebook"));
            obj.setMensagemVitioWhatsapp(dadosSQL.getString("mensagemvitiowpp"));
            obj.setMensagemVitioQuerComprar(dadosSQL.getString("mensagemvitioquercomprar"));
            obj.setAplicarMultaSobreValorTotalContrato(dadosSQL.getBoolean("aplicarMultaSobreValorTotalContrato"));
        }catch (Exception ignored) {}
        try {
            obj.setIdContabancariaSesi(dadosSQL.getInt("idcontabancariasesi"));
        } catch (Exception e) {
            obj.setIdContabancariaSesi(0);
        }
        try {
            obj.setCodExternoUnidadeSesi(dadosSQL.getInt("codexternounidadesesi"));
            obj.setCnpjClienteSesi(dadosSQL.getString("cnpjclientesesi"));
        } catch (Exception e) {
            obj.setCodExternoUnidadeSesi(0);
        }
        try {
            obj.setCobrarParcelaComBoletoGerado(dadosSQL.getBoolean("cobrarParcelaComBoletoGerado"));
            obj.setCobrarParcelaVencidaSemTentativaCobranca(dadosSQL.getBoolean("cobrarParcelaVencidaSemTentativaCobranca"));
        } catch (Exception ignored) {
        }
        try {
            obj.setTotalDiasExtras(dadosSQL.getInt("total_dias_solicitados"));
        } catch (Exception e) {
            obj.setTotalDiasExtras(0);
        }
        try {
            obj.setDataSuspensao(dadosSQL.getDate("datasuspensao"));
        } catch (Exception e) {
            obj.setDataSuspensao(null);
        }

        //
        try {
            obj.getConsultorVendaAvulsa().setCodigo(dadosSQL.getInt("consultorvendaavulsa"));
            obj.setToleranciaOcupacaoTurma(dadosSQL.getInt("toleranciaOcupacaoTurma"));
            obj.setUrlRecorrencia(dadosSQL.getString("urlRecorrencia"));
            obj.setNrDiasCompensacao(dadosSQL.getInt("nrdiascompensacao"));
            obj.setNrDiasChequeAVista(dadosSQL.getInt("nrdiaschequeavista"));
            obj.setServiceUsuario(dadosSQL.getString("serviceUsuario"));
            obj.setServiceSenha(dadosSQL.getString("serviceSenha"));
            obj.setBloquearAcessoSeParcelaAberta(dadosSQL.getBoolean("bloquearAcessoSeParcelaAberta"));
            obj.setPermiteHorariosConcorrentesParaProfessor(dadosSQL.getBoolean("permiteHorariosConcorrentesParaProfessor"));
            obj.setProfessorEmAmbientesDiferentesMesmoHorario(dadosSQL.getBoolean("professorEmAmbientesDiferentesMesmoHorario"));
            obj.setMostrarCnpj(dadosSQL.getBoolean("mostrarcnpj"));
            obj.setChaveNFSe(dadosSQL.getString("chavenfse"));
            obj.setUsarNFSe(dadosSQL.getBoolean("usarnfse"));
            obj.setTokenSMS(dadosSQL.getString("tokensms"));
            obj.setTokenSMSShortCode(dadosSQL.getString("tokensmsshortcode"));
            obj.setMostrarModalidade(dadosSQL.getBoolean("mostrarModalidade"));
            obj.setBvObrigatorio(dadosSQL.getBoolean("bvobrigatorio"));
            obj.setTempoAposFaltaReposicao(dadosSQL.getInt("tempoAposFaltaReposicao"));
            obj.setEnviarNFSeAutomatico(dadosSQL.getBoolean("enviarNFSeAutomatico"));
            obj.setComissaoMatriculaRematricula(dadosSQL.getBoolean("comissaoMatriculaRematricula"));
            obj.getQuestionarioPrimeiraCompra().setCodigo(dadosSQL.getInt("questionarioPrimeiraCompra"));
            obj.getQuestionarioRetornoCompra().setCodigo(dadosSQL.getInt("questionarioRetornoCompra"));
            obj.setNrDiasVigenteQuestionarioPrimeiraCompra(dadosSQL.getInt("nrDiasVigenteQuestionarioPrimeiraCompra"));
            obj.setNrDiasVigenteQuestionarioRetornoCompra(dadosSQL.getInt("nrDiasVigenteQuestionarioRetornoCompra"));
            obj.setUsarNFSePorPagamento(dadosSQL.getBoolean("nfseporpagamento"));
            obj.setQtdDiasCobrarRematricula(dadosSQL.getInt("qtdDiasCobrarRematricula"));

            obj.setQtdVias(dadosSQL.getInt("qtdvias"));
            obj.setQuebrarPaginaRecibo(dadosSQL.getBoolean("quebrarpaginarecibo"));
            obj.setDetalharPeriodoProduto(dadosSQL.getBoolean("detalharperiodoproduto"));
            obj.setDetalharParcelas(dadosSQL.getBoolean("detalharparcelas"));
            obj.setDetalharPagamentos(dadosSQL.getBoolean("detalharpagamentos"));
            obj.setDetalharDescontos(dadosSQL.getBoolean("detalhardescontos"));
            obj.setApresentarAssinaturas(dadosSQL.getBoolean("apresentarassinaturas"));
            obj.setObservacaoRecibo(dadosSQL.getString("observacaorecibo"));
            obj.setTipoGestaoNFSe(dadosSQL.getInt("tipogestaonfse"));
            obj.setRetrocederValorMensalPlanoCancelamento(dadosSQL.getBoolean("retrocederValorMensalPlanoCancelamento"));
            obj.setFecharNegociacaoSemAutorizacaoDCC(dadosSQL.getBoolean("fecharNegociacaoSemAutorizacaoDCC"));
            obj.setNrDiasDesistenteRemoverVinculoTreino(dadosSQL.getInt("nrdiasdesistenteremovervinculotreino"));
            obj.setRemoverVinculosAposDesistencia(dadosSQL.getBoolean("removervinculosaposdesistencia"));
            obj.setLiberarPersonalComTaxaEmAberto(dadosSQL.getBoolean("liberarpersonalcomtaxaemaberto"));
            obj.setReciboParaImpressoraTermica(dadosSQL.getBoolean("reciboparaimpressoratermica"));

            obj.setUsarManutencaoModalidadeComissao(dadosSQL.getBoolean("usarManutencaoModalidadeComissao"));
            obj.setDevolucaoEntraNoCaixa(dadosSQL.getBoolean("devolucaoentranocaixa"));

            obj.setUsarGestaoCreditosPersonal(dadosSQL.getBoolean("usarGestaoCreditosPersonal"));

            obj.setAlturaFotoRedeSocial(dadosSQL.getString("alturafotoredesocial"));
            obj.setLarguraFotoRedeSocial(dadosSQL.getString("largurafotoredesocial"));

            obj.setCreditoDCC(dadosSQL.getInt("creditodcc"));
            obj.setDataExpiracaoCreditoDCC(dadosSQL.getDate("dataexpiracaocreditodcc"));

            obj.setArredondamento(UsoArredondamentoEnum.getFromId(dadosSQL.getInt("arredondamento")));
            obj.setToleranciaProrata(dadosSQL.getInt("toleranciaprorata"));
            obj.setPermiteReposicaoEmTurmasDiferentes(dadosSQL.getBoolean("permitereposicaoemturmasdiferentes"));
            obj.setForcarMinimoVencimento2parcela(dadosSQL.getBoolean("forcarMinimoVencimento2parcela"));
            obj.setPermiteRenovarContratosEmTurmasLotadas(dadosSQL.getBoolean("permiterenovarcontratosemturmaslotadas"));

            obj.setMostrarMensagemValoresRodape(dadosSQL.getBoolean("mostrarmensagemvaloresrodape"));
            obj.setAlturahomeBackground640x551(dadosSQL.getString("alturahomeBackground640x551"));
            obj.setLargurahomeBackground640x551(dadosSQL.getString("largurahomeBackground640x551"));
            obj.setAlturahomeBackground320x276(dadosSQL.getString("alturahomeBackground320x276"));
            obj.setLargurahomeBackground320x276(dadosSQL.getString("largurahomeBackground320x276"));
            obj.setLatitude(dadosSQL.getString("latitude"));
            obj.setLongitude(dadosSQL.getString("longitude"));
            obj.setAcessoChamada(dadosSQL.getBoolean("acessoChamada"));
            obj.getLocalAcessoChamada().setCodigo(dadosSQL.getInt("localAcessoChamada"));
            obj.getColetorChamada().setCodigo(dadosSQL.getInt("coletorChamada"));

            obj.setDiasRenovacaoAutomaticaAntecipada(dadosSQL.getInt("diasrenovacaoautomaticaantecipada"));
            obj.setCobrarAutomaticamenteMultaJuros(dadosSQL.getBoolean("cobrarautomaticamentemultajuros"));
            obj.setMultaCobrancaAutomatica(dadosSQL.getDouble("multacobrancaautomatica"));
            obj.setJurosCobrancaAutomatica(dadosSQL.getDouble("juroscobrancaautomatica"));
            obj.setLiberarPersonalProfessorDebito(dadosSQL.getBoolean("liberarPersonalProfessorDebito"));
            obj.getConsultorSite().setCodigo(dadosSQL.getInt("consultorSite"));
            obj.setCriarBvVendaSite(dadosSQL.getBoolean("criarBvVendaSite"));
            obj.setAtiva(dadosSQL.getBoolean("ativa"));
            obj.setEmiteNFSEPorDataCompensacao(dadosSQL.getBoolean("emiteNFSEPorDataCompensacao"));
            obj.setPagarComissaoSeAtingirMetaFinanceira(dadosSQL.getBoolean("pagarComissaoSeAtingirMetaFinanceira"));
            obj.setPagarComissaoManutencaoModalidade(dadosSQL.getBoolean("pagarComissaoManutencaoModalidade"));
            obj.setSomenteVendaProdutosComEstoque(dadosSQL.getBoolean("somenteVendaProdutosComEstoque"));
            obj.setPermiteAlterarDataEmissaoNFSe(dadosSQL.getBoolean("permiteAlterarDataEmissaoNFSe"));
            obj.setMinutosAposUltimoAcessoDiminuirCredito(dadosSQL.getInt("minutosAposUltimoAcessoDiminuirCredito"));
            obj.getModeloMensagemVendasOnline().setCodigo(dadosSQL.getInt("modeloMensagemVendasOnline"));

            obj.setConvenioBoletoPadrao(new ConvenioCobrancaVO());
            obj.getConvenioBoletoPadrao().setCodigo(dadosSQL.getInt("convenioBoletoPadrao"));

            obj.setCancelamentoObrigatoriedadePagamento(dadosSQL.getBoolean("cancelamentoObrigatoriedadePagamento"));
            obj.setCancelamentoAntecipado(dadosSQL.getBoolean("cancelamentoAntecipado"));
            obj.setCancelamentoAntecipadoDias(dadosSQL.getInt("cancelamentoAntecipadoDias"));
            obj.setCancelamentoAntecipadoMulta(dadosSQL.getDouble("cancelamentoAntecipadoMulta"));
            obj.setCancelamentoAntecipadoPlanos(dadosSQL.getString("cancelamentoAntecipadoPlanos"));
            obj.setCancelamentoAntecipadoPlanosMulta(dadosSQL.getDouble("cancelamentoAntecipadoPlanosMulta"));
            obj.setCancelamentoAntecipadoPlanosData(dadosSQL.getDate("cancelamentoAntecipadoPlanosData"));
            obj.setCancelamentoAntecipadoContratosDepoisDe(dadosSQL.getDate("cancelamentoAntecipadoContratosDepoisDe"));
            obj.setCancelamentoAntecipadoGerarParcelaMultaSeparada(dadosSQL.getBoolean("cancelamentoantecipadogerarparcelamultaseparada"));
            obj.setPermitirAlterarDataFinalContratoNoCancelamento(dadosSQL.getBoolean("permitirAlterarDataFinalContratoNoCancelamento"));
            obj.setCancelamentoAutomaticoAntecipadoContratoForaRecorrencia(dadosSQL.getBoolean("cancelamentoAutomaticoAntecipadoContratoForaRecorrencia"));
            obj.setCancelamentoAvaliandoParcelas(dadosSQL.getBoolean("cancelamentoAvaliandoParcelas"));
            obj.preencherTipoCancelamento();

            obj.setEnviarEmailCancelamento(dadosSQL.getBoolean("enviarEmailCancelamento"));
            obj.setPermiteContratoPosPagoRenovacaoAuto(dadosSQL.getBoolean("permitecontratopospagorenovacaoauto"));
            obj.setTipoCobrancaPacto(dadosSQL.getInt("tipoCobrancaPacto"));
            obj.setGerarCobrancaAutomaticaPacto(dadosSQL.getBoolean("gerarCobrancaAutomaticaPacto"));
            obj.setDtUltimaCobrancaPacto(dadosSQL.getDate("dtUltimaCobrancaPacto"));
            obj.setQtdDiasFechamentoCobrancaPacto(dadosSQL.getInt("qtdDiasFechamentoCobrancaPacto"));
            obj.setValorCreditoPacto(dadosSQL.getDouble("valorCreditoPacto"));
            obj.setGerarNotaFiscalCobrancaPacto(dadosSQL.getBoolean("gerarNotaFiscalCobrancaPacto"));
            obj.setQtdParcelasCobrancaPacto(dadosSQL.getInt("qtdParcelasCobrancaPacto"));
            obj.setQtdCreditoRenovarPrePagoCobrancaPacto(dadosSQL.getInt("qtdCreditoRenovarPrePagoCobrancaPacto"));
            obj.setQtdDiasParaLiberacaoDeVagaEmTrancamento(dadosSQL.getInt("qtdDiasParaLiberacaoDeVagaEmTrancamento"));
            obj.setTentativasLiberarParcelaVencida(dadosSQL.getInt("tentativasliberarparcelavencida"));

            obj.setResponderBVNaVendaRapida(dadosSQL.getBoolean("responderBVNaVendaRapida"));
            obj.setAlturaFotoRedeSocial(dadosSQL.getString("alturaPropagandaBoleto"));
            obj.setLarguraFotoRedeSocial(dadosSQL.getString("larguraPropagandaBoleto"));
            obj.setQtdDiasVencimentoBoleto(dadosSQL.getInt("qtdDiasVencimentoBoleto"));
            obj.setUsarDataInicioDeContratoNoBI_ICV(dadosSQL.getBoolean("usarDataInicioDeContratoNoBI_ICV"));
            obj.setExistemNovosBoletosPacto(dadosSQL.getBoolean("existemNovosBoletosPacto"));
            obj.setTipoParcelasCobrarVendaSite(TipoObjetosCobrarEnum.valueOf(dadosSQL.getInt("tipoParcelasCobrarVendaSite")));

            obj.setGerarLoginAPIAoIncluirContrato(dadosSQL.getBoolean("gerarLoginAPIAoIncluirContrato"));

            obj.setModeloMensagemEsqueciMinhaSenhaVendasOnline(new ModeloMensagemVO());
            obj.getModeloMensagemEsqueciMinhaSenhaVendasOnline().setCodigo(dadosSQL.getInt("modeloMensagemEsqueciMinhaSenhaVendasOnline"));
            obj.setQuantidadeParcelasSeguidasCancelamento(dadosSQL.getInt("quantidadeParcelasSeguidasCancelamento"));
            obj.setTipoParcelaCancelamento(dadosSQL.getString("tipoParcelaCancelamento"));
            obj.setQuantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia(dadosSQL.getInt("quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia"));
            obj.setQuantidadeDiasUteisAposVencimentoParaCancelarContrato(dadosSQL.getInt("quantidadeDiasUteisAposVencimentoParaCancelarContrato"));
            obj.setTipoParcelaCancelamentoForaRegimeRecorrencia(dadosSQL.getString("tipoParcelaCancelamentoForaRegimeRecorrencia"));
            obj.setEnviarNotaCidadeEmpresa(dadosSQL.getBoolean("enviarNotaCidadeEmpresa"));
            obj.setPermitirMaillingGerarAutorizacaoCobrancaBoleto(dadosSQL.getBoolean("permitirmaillinggerarautorizacaocobrancaboleto"));
            obj.setGerarNFSeContaCorrente(dadosSQL.getBoolean("gerarNFSeContaCorrente"));
            obj.setInscMunicipal(dadosSQL.getString("inscMunicipal"));
            obj.setSequencialLoteRPS(dadosSQL.getInt("sequencialLoteRPS"));
            obj.setPermiteGerarArquivoLoteRPS(dadosSQL.getBoolean("permiteGerarArquivoLoteRPS"));
            obj.setPessoaFinan(new PessoaVO());
            obj.getPessoaFinan().setCodigo(dadosSQL.getInt("pessoaFinan"));
            obj.setPermiteGerarNotaManual(dadosSQL.getBoolean("permiteGerarNotaManual"));
            obj.setImpedirVendaContratoPorConflitoReposicao(dadosSQL.getBoolean("impedirVendaContratoPorConflitoReposicao"));
            obj.setUsarNFCe(dadosSQL.getBoolean("usarNFCe"));
            obj.setUtilizarNomeResponsavelNoBoleto(dadosSQL.getBoolean("utilizarNomeResponsavelNoBoleto"));
            obj.setUtilizarNomeResponsavelNoBoletoMaiorIdade(dadosSQL.getBoolean("utilizarNomeResponsavelNoBoletoMaiorIdade"));
            obj.setUtilizarNomeResponsavelNoPixMenorIdade(dadosSQL.getBoolean("utilizarNomeResponsavelNoPixMenorIdade"));
            obj.setUtilizarNomeResponsavelNoPixMaiorIdade(dadosSQL.getBoolean("utilizarNomeResponsavelNoPixMaiorIdade"));
            obj.setGerarQuitacaoCancelamentoAuto(dadosSQL.getBoolean("gerarquitacaocancelamentoauto"));
            obj.setGerarQuitacaoCancelamentoRemanescente(dadosSQL.getBoolean("gerarquitacaocancelamentoproporcional"));
            obj.setPagarComissaoProdutos(dadosSQL.getBoolean("pagarcomissaoproduto"));
            obj.setAlterarDataHoraCheckGestaoPersonal(dadosSQL.getBoolean("alterarDataHoraCheckGestaoPersonal"));
            obj.setSenhaAcessoOnzeDigitos(dadosSQL.getBoolean("senhaAcessoOnzeDigitos"));
            obj.setNaoRenovarContratoSemIndiceFinanceiro(dadosSQL.getBoolean("naorenovarcontratosemindicefinanceiro"));
            obj.setCodigoGymPass(dadosSQL.getString("codigoGymPass"));
            obj.setTokenApiGymPass(dadosSQL.getString("tokenApiGymPass"));
            obj.setGerarRemessaContratoCancelado(dadosSQL.getBoolean("gerarRemessaContratoCancelado"));
            obj.setHabilitarSomaDeAulaNaoVigente(dadosSQL.getBoolean("habilitarSomaDeAulaNaoVigente"));
            obj.setCodigoChaveIntegracaoDigitais(dadosSQL.getInt("codigoChaveIntegracaoDigitais"));
            obj.setDefinirCpfComoSenhaCatraca(dadosSQL.getBoolean("definirCpfComoSenhaCatraca"));
            obj.setMostrarNotaPorDiaCompetencia(dadosSQL.getBoolean("mostrarnotapordiacompetencia"));
            obj.setDiasAtivosPontuacaoAcesso(dadosSQL.getString("diasativospontuacaoacesso"));
            obj.setMarcarAutoRecebiveisCartaoChequeCancelamento(dadosSQL.getBoolean("marcarAutoRecebiveisCartaoChequeCancelamento"));
            obj.setHabilitarCadastroEmpresaSesi(dadosSQL.getBoolean("habilitarCadastroEmpresaSesi"));
            obj.setPontosAlunoAcesso(dadosSQL.getInt("pontosalunoacesso"));
            obj.setPontosAlunoAcessoChuva(dadosSQL.getInt("pontosalunoacessochuva"));
            obj.setPontosAlunoAcessoFrio(dadosSQL.getInt("pontosalunoacessofrio"));
            obj.setPontosAlunoAcessoCalor(dadosSQL.getInt("pontosalunoacessocalor"));
            obj.setTrabalharComPontuacao(dadosSQL.getBoolean("trabalharComPontuacao"));
            obj.setRetirarEdicaoPagamento(dadosSQL.getBoolean("retirarEdicaoPagamento"));
            obj.setAdicionarAulasDesmarcadasContratoAnterior(dadosSQL.getBoolean("adicionarAulasDesmarcadasContratoAnterior"));
            obj.setLimiteInicialItensBIPendencia(dadosSQL.getDate("limiteInicialItensBIPendencia"));
            obj.setTempoSaidaAcademia(dadosSQL.getInt("tempoSaidaAcademia"));
            obj.setTempoSaidaAcademiaFormatada(Uteis.converteMinutosParaHora(obj.getTempoSaidaAcademia()));
            obj.setRegistrarTentativasAcesso(dadosSQL.getBoolean("registrarTentativasAcesso"));
            obj.setPermiteRenovarContratoViaAPP(dadosSQL.getBoolean("permiteRenovarContratoViaAPP"));
            obj.setValorMensalEmitirNFSe(dadosSQL.getDouble("valorMensalEmitirNFSe"));
            obj.setHabilitarReenvioAutomaticoRemessa(dadosSQL.getBoolean("habilitarReenvioAutomaticoRemessa"));
            obj.setEmitirNotaSomenteRecorrencia(dadosSQL.getBoolean("emitirNotaSomenteRecorrencia"));
            obj.setEmitirNomeAlunoNotaFamilia(dadosSQL.getBoolean("emitirNomeAlunoNotaFamilia"));
            obj.setIrTelaPagamentoCartaoCreditoRecorrente(dadosSQL.getBoolean("irTelaPagamentoCartaoCreditoRecorrente"));
            obj.setIrTelaPagamentoCartaoCreditoFormaPagamento(dadosSQL.getBoolean("irTelaPagamentoCartaoCreditoFormaPagamento"));
            obj.setGerarAutCobrancaComCobAutBloqueada(dadosSQL.getBoolean("gerarAutCobrancaComCobAutBloqueada"));
            obj.setEnviarNFCeAutomatico(dadosSQL.getBoolean("enviarNFCeAutomatico"));
            obj.setEmitirNFCeSomenteRecorrencia(dadosSQL.getBoolean("emitirNFCeSomenteRecorrencia"));
            obj.setUsaIntegracoesCrm(dadosSQL.getBoolean("usaintegracoescrm"));
            obj.setIntegracaoWeHelpHabilitada(dadosSQL.getBoolean("integracaoWeHelpHabilitada"));
            obj.setCpfCodigoInternoWeHelp(dadosSQL.getBoolean("cpfCodigoInternoWeHelp"));
            obj.setTokenBuzzLead(dadosSQL.getString("tokenbuzzlead"));
            obj.setUrlLinkSiteCadastro(dadosSQL.getString("urlLinkSiteCadastro"));
            obj.setTiposProduto(dadosSQL.getString("tiposProduto"));
            obj.setUsarDataOriginalCompensacaoNFSe(dadosSQL.getBoolean("usarDataOriginalCompensacaoNFSe"));
            obj.setUtilizaSistemaEstacionamento(dadosSQL.getBoolean("utilizaSistemaEstacionamento"));
            obj.setPermitirLancarVariasParcelasSaldoDevedor(dadosSQL.getBoolean("permitirLancarVariasParcelasSaldoDevedor"));
            obj.setProdutoEmissaoNFCeFinanceiro(dadosSQL.getInt("produtoEmissaoNFCeFinanceiro"));
            obj.setProdutoEmissaoNFSeFinanceiro(dadosSQL.getInt("produtoEmissaoNFSeFinanceiro"));
            obj.setQtdExecucoesRetentativa(dadosSQL.getInt("qtdExecucoesRetentativa"));
            obj.setUsarNFCePorPagamento(dadosSQL.getBoolean("usarNFCePorPagamento"));
            obj.setConsultarDiasAnterioresNFSe(dadosSQL.getBoolean("consultarDiasAnterioresNFSe"));
            obj.setConsultarDiasAnterioresNFCe(dadosSQL.getBoolean("consultarDiasAnterioresNFCe"));
            obj.setEmImportacao(dadosSQL.getBoolean("emimportacao"));
            obj.setNotasAutoPgRetroativo(dadosSQL.getBoolean("notasAutoPgRetroativo"));
            obj.setValidarVencimentoCartaoAutorizacao(dadosSQL.getBoolean("validarVencimentoCartaoAutorizacao"));
            obj.setPermitirEstornarContratoComParcelasPG(dadosSQL.getBoolean("permitirEstornarContratoComParcelasPG"));
            obj.setPermMarcarAulaFeriado(dadosSQL.getBoolean("permMarcarAulaFeriado"));
            obj.setHoraAberturaFeriado(dadosSQL.getString("horaAberturaFeriado"));
            obj.setHoraFechamentoFeriado(dadosSQL.getString("horaFechamentoFeriado"));
            obj.setFormasPagamentoEmissaoNFCe(dadosSQL.getString("formasPagamentoNFCe"));
            obj.setFormasPagamentoEmissaoNFSe(dadosSQL.getString("formasPagamentoNFSe"));
            obj.setConsiderarSomenteParcelasPlanos(dadosSQL.getBoolean("considerarSomenteParcelasPlanos"));
            obj.setCobrarMultaJurosTransacao(dadosSQL.getBoolean("cobrarMultaJurosTransacao"));
            obj.setCobrarMultaJurosDCO(dadosSQL.getBoolean("cobrarMultaJurosDCO"));
            obj.setCobrarMultaJurosDCC(dadosSQL.getBoolean("cobrarMultaJurosDCC"));
            obj.setAddAutoClienteTreinoWeb(dadosSQL.getBoolean("addAutoClienteTreinoWeb"));
            obj.setUsarParceiroFidelidade(dadosSQL.getBoolean("usarParceiroFidelidade"));
            obj.setEnvioNotificacaoNotasNFSe(dadosSQL.getBoolean("envioNotificacaoNotasNFSe"));
            obj.setEnvioNotificacaoNotasNFCe(dadosSQL.getBoolean("envioNotificacaoNotasNFCe"));
            obj.setEmailsNotificacaoAutomaticaNotas(dadosSQL.getString("emailsNotificacaoAutomaticaNotas"));
            obj.setEmailNotificacaoVendasOnline(dadosSQL.getString("emailNotificacaoVendasOnline"));
            obj.setValidarCertificado(dadosSQL.getBoolean("validarCertificado"));
            obj.setBloquearAcessoArmarioVigenciaVencida(dadosSQL.getBoolean("bloquearAcessoArmarioVigenciaVencida"));
            obj.setUtilizarJurosValorAbsoluto(dadosSQL.getBoolean("utilizarJurosValorAbsoluto"));
            obj.setDataConcessaoDiaExtra(dadosSQL.getDate("concessao_dia_extra"));
            obj.setEmailMovidesk(dadosSQL.getString("email_movidesk"));
            obj.setSincronizadoMovidesk(dadosSQL.getBoolean("sincronizado_movidesk"));
            obj.setUsarConciliadora(dadosSQL.getBoolean("usarConciliadora"));
            obj.setEmpresaConciliadora(dadosSQL.getString("empresaConciliadora"));
            obj.setSenhaConciliadora(dadosSQL.getString("senhaConciliadora"));
            obj.setDiasParaRetirarRelFechamentoDeCaixa(dadosSQL.getInt("diasParaRetirarRelFechamentoDeCaixa"));
            if (Uteis.resultSetContemColuna(dadosSQL, "diasParaVencimentoParq")) {
                obj.setDiasParaVencimentoParq(dadosSQL.getInt("diasParaVencimentoParq"));
            }
            obj.setMoeda(dadosSQL.getString("moeda"));
            obj.setDescMoeda(dadosSQL.getString("descmoeda"));
            obj.setLocaleTexto(dadosSQL.getString("locale"));
            obj.getConfiguracaoNotaFiscalNFSe().setCodigo(dadosSQL.getInt("configuracaonotafiscalnfse"));
            obj.getConfiguracaoNotaFiscalNFCe().setCodigo(dadosSQL.getInt("configuracaonotafiscalnfce"));
            obj.setUsarNomeResponsavelNFCe(dadosSQL.getBoolean("usarNomeResponsavelNFCe"));
            obj.setTipoProdutoEmissaoNFSe(dadosSQL.getString("tipoProdutoEmissaoNFSe"));
            obj.setTipoProdutoEmissaoNFCe(dadosSQL.getString("tipoProdutoEmissaoNFCe"));
            obj.setMinutosCreditarProximoPontoClubeVantagens(dadosSQL.getInt("minCreditarProximoPontoClubeVantagens"));
            obj.setApenasPrimeiroAcessoClubeVantagens(dadosSQL.getBoolean("apenasPrimeiroAcessoClubeVantagens"));
            obj.setZerarPontosAposVencimento(TemporalRemocaoPontoEnum.of(dadosSQL.getInt("zerarPontosAposVencimento")));
            obj.setPontuarApenasCategoriasEmCampanhasAtivas(dadosSQL.getBoolean("pontuarApenasCampanhasAtivas"));
            obj.setAplicarIndicacaoQlqrPlano(dadosSQL.getBoolean("aplicarIndicacaoQlqrPlano"));
            obj.setNaoCobrarMultaDeContratoRenovado(dadosSQL.getBoolean("naocobrarmultadecontratorenovado"));
            obj.setEmitirDuplicataNFSe(dadosSQL.getBoolean("emitirDuplicataNFSe"));
            obj.setEmiteValorTotalCompetencia(dadosSQL.getBoolean("emiteValorTotalCompetencia"));
            obj.setEmiteValorTotalFaturamento(dadosSQL.getBoolean("emiteValorTotalFaturamento"));
            obj.setGerarNotaFiscalComDesconto(dadosSQL.getBoolean("gerarNotaFiscalComDesconto"));
            obj.setZerarValorCancelamentoTransferencia(dadosSQL.getBoolean("zerarvalorcancelamentotransferencia"));

            obj.setIntegracaoSpiviHabilitada(dadosSQL.getBoolean("integracaoSpiviHabilitada"));
            obj.setIntegracaoSpiviSourceName(dadosSQL.getString("integracaoSpiviSourceName"));
            obj.setIntegracaoSpiviSiteID(dadosSQL.getInt("integracaoSpiviSiteID"));
            obj.setIntegracaoSpiviPassword(dadosSQL.getString("integracaoSpiviPassword"));
            obj.setCodEmpresaFinanceiro(dadosSQL.getInt("cod_empresafinanceiro"));
            obj.setAtualizarDadosCadastro(dadosSQL.getBoolean("atualizarDadosCadastro"));

            obj.setUtilizarDataCancelamentoParaValidarParcelas(dadosSQL.getBoolean("utilizardatacancelamentovalidarparcela"));
            obj.setEmitirMesReferenciaNFCe(dadosSQL.getBoolean("emitirMesReferenciaNFCe"));
            obj.setMostrarValoresZeradosRel(dadosSQL.getBoolean("mostrarValoresZeradosRel"));
            obj.setCreditoDCCBonus(dadosSQL.getInt("creditodccbonus"));
            obj.setBloquearAcessoSemAssinaturaDigital(dadosSQL.getBoolean("bloquearacessosemassinaturadigital"));
            obj.setBloquearAcessoCrefVencido(dadosSQL.getBoolean("bloquearAcessoCrefVencido"));
            obj.setBloquearAcessoAlunoParqNaoAssinado(dadosSQL.getBoolean("bloquearAcessoAlunoParqNaoAssinado"));
            obj.setCobrarCreditoVindi(dadosSQL.getBoolean("cobrarCreditoVindi"));
            obj.setQtdDiasLimiteCobrancaParcelasRecorrencia(dadosSQL.getInt("qtddiaslimitecobrancaparcelasrecorrencia"));
            obj.setQtdDiasRepetirCobrancaParcelasRecorrencia(dadosSQL.getInt("qtddiasrepetircobrancaparcelasrecorrencia"));
            obj.setTentativaUnicaDeCobranca(dadosSQL.getBoolean("tentativaUnicaDeCobranca"));
            obj.setMostrarDescricaoParcelaRenegociada(dadosSQL.getBoolean("mostrarDescricaoParcelaRenegociada"));
            obj.setFacilitePayReguaCobranca(dadosSQL.getBoolean("facilitePayReguaCobranca"));
            obj.setFacilitePayReguaCobrancaGymbotPro(dadosSQL.getBoolean("facilitepayreguacobrancagymbotpro"));
            obj.setObrigatorioPreencherCamposCartao(dadosSQL.getBoolean("obrigatorioPreencherCamposCartao"));
            try {
                obj.setNaoCobrarMultaDeTodasParcelasPagas(dadosSQL.getBoolean("naocobrarmultadetodasparcelaspagas"));
            }catch (PSQLException e){
                obj.setNaoCobrarMultaDeTodasParcelasPagas(false);
            }
            obj.setAcessoSomenteComAgendamento(dadosSQL.getBoolean("acessoSomenteComAgendamento"));
            obj.setCapacidadeSimultanea(dadosSQL.getInt("capacidadeSimultanea"));
            obj.setToleranciaAcessoAula(dadosSQL.getInt("toleranciaAcessoAula"));
            obj.setUtilizaLeitorCodigoBarras(dadosSQL.getBoolean("utilizaLeitorCodigoBarras"));
            obj.setNomeUsuarioAmigoFit(dadosSQL.getString(  "nomeusuarioamigofit"));
            obj.setSenhaUsuarioAmigoFit(dadosSQL.getString("senhausuarioamigofit"));
            obj.setAgruparParcelasPorCartao(dadosSQL.getBoolean("agruparParcelasPorCartao"));
            obj.setAgruparParcelasPorCartaoValorLimite(dadosSQL.getDouble("agruparParcelasPorCartaoValorLimite"));
            obj.setSomenteUmEnvioCartaoTentativa(dadosSQL.getBoolean("somenteUmEnvioCartaoTentativa"));
            obj.setDiaVencimentoCobrancaPacto(dadosSQL.getInt("diaVencimentoCobrancaPacto"));
            obj.setEmpresaResponsavelCobrancaPacto(dadosSQL.getBoolean("empresaResponsavelCobrancaPacto"));
            obj.setEmpresaResponsavelCobrancaPacto(dadosSQL.getBoolean("empresaResponsavelCobrancaPacto"));
            obj.setValorLimiteCaixaAbertoVendaAvulsa(dadosSQL.getDouble("valorLimiteCaixaAbertoVendaAvulsa"));
            obj.setNotificarWebhook(dadosSQL.getBoolean("notificarWebhook"));
            obj.setUrlWebhookNotificar(dadosSQL.getString("urlWebhookNotificar"));
            obj.setPermiteCadastrarCartaoMesmoAssim(dadosSQL.getBoolean("permitecadastrarcartaomesmoassim"));
            obj.getConvenioVerificacaoCartao().setCodigo(dadosSQL.getInt("convenioVerificacaoCartao"));
            obj.setPermiteMaillingCriarBoleto(dadosSQL.getBoolean("permiteMaillingCriarBoleto"));
            obj.getConvenioCobrancaPix().setCodigo(dadosSQL.getInt("conveniocobrancapix"));
            obj.setEnviarEmailPagamento(dadosSQL.getBoolean("enviarEmailPagamento"));
            obj.setCobrarMultaJurosPix(dadosSQL.getBoolean("cobrarMultaJurosPix"));

            obj.setRestringirConvidadoUmaVezPorMes(dadosSQL.getBoolean("restringirConvidadoUmaVezPorMes"));
            obj.setAplicarMultaeJurosNoCancelamentoAutomatico(dadosSQL.getBoolean("aplicarMultaeJurosNoCancelamentoAutomatico"));
            obj.setAplicarMultaMudancaPlano(dadosSQL.getBoolean("aplicarMultaMudancaPlano"));
            obj.setBloquearRenovacaoAutomaticaPlanosForaDaVigencia(dadosSQL.getBoolean("bloquearRenovacaoAutomaticaPlanosForaDaVigencia"));
            obj.setBloquearRenovacaoAutomaticaPlanosForaCategoriaCliente(dadosSQL.getBoolean("bloquearRenovacaoAutomaticaPlanosForaCategoriaCliente"));
            obj.setGerarBoletoCaixaAberto(dadosSQL.getBoolean("gerarBoletoCaixaAberto"));
            obj.setUrlEnvioAcesso(dadosSQL.getString("urlEnvioAcesso"));
            obj.setTokenEnvioAcesso(dadosSQL.getString("tokenEnvioAcesso"));
            obj.setNaoGerarResiduoCancelamentoAutomatico(dadosSQL.getBoolean("naoGerarResiduoCancelamentoAutomatico"));
            obj.setDepositarResiduoCancelamentoNaContaCorrente(dadosSQL.getBoolean("depositarResiduoCancelamentoNaContaCorrente"));
            obj.getConvenioCobrancaBoleto().setCodigo(dadosSQL.getInt("conveniocobrancaboleto"));
            obj.setEmitirNoNomeResponsavel(dadosSQL.getBoolean("emitirNoNomeResponsavel"));
            obj.getConvenioCobrancaCartao().setCodigo(dadosSQL.getInt("conveniocobrancacartao"));
            obj.setCancelamentoApresentarTransacoes(dadosSQL.getBoolean("cancelamentoApresentarTransacoes"));
            obj.setIsentarCancelamento7Dias(dadosSQL.getBoolean("isentarCancelamento7Dias"));
            obj.setCancelarContratosNaoRenovaveisForaRecorrencia(dadosSQL.getBoolean("cancelarContratosNaoRenovaveisForaRecorrencia"));
            obj.getConvenioCobrancaCartaoRegua().setCodigo(dadosSQL.getInt("conveniocobrancacartaoregua"));
            obj.getConvenioCobrancaPixRegua().setCodigo(dadosSQL.getInt("conveniocobrancapixregua"));
            obj.getConvenioCobrancaBoletoRegua().setCodigo(dadosSQL.getInt("conveniocobrancaboletoregua"));
            obj.getConvenioCobrancaBoletoRegua().setCodigo(dadosSQL.getInt("conveniocobrancaboletoregua"));
            obj.setTipoParcelasCobrarVendaSiteRegua(TipoObjetosCobrarEnum.valueOf(dadosSQL.getInt("tipoParcelasCobrarVendaSiteRegua")));
            obj.setEnviarEmailPagamentoRegua(dadosSQL.getBoolean("enviarEmailPagamentoRegua"));
            obj.setGerarAutCobrancaComCobAutBloqueadaRegua(dadosSQL.getBoolean("gerarAutCobrancaComCobAutBloqueadaRegua"));


        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }

        try{
            obj.setToleranciaCancelarContratosNaoAssinados(dadosSQL.getInt("toleranciaCancelarContratosNaoAssinados"));
        } catch (Exception e){
            obj.setToleranciaCancelarContratosNaoAssinados(0);
        }

        try {
            obj.setManterMarcacoesFuturasCreditoRenovacao(dadosSQL.getBoolean("manterMarcacoesFuturasCreditoRenovacao"));
        }catch (Exception e){
        }

        try {
            obj.setOperadorSpc(dadosSQL.getString("operadorSPC"));
            obj.setSenhaSpc(dadosSQL.getString("senhaSPC"));
            obj.setQtdDiasEnvioSPC(dadosSQL.getInt("qtdDiasEnvioSPC"));
            obj.setConsultarNovoCadastroSPC(dadosSQL.getBoolean("consultarNovoCadastroSPC"));
            obj.setEnvioAutomaticoSPC(dadosSQL.getBoolean("enviarAutomaticoSpc"));
            obj.setCodigoAssociadoSpc(dadosSQL.getLong("codigoAssociadoSPC"));
            obj.setPesquisaAutomaticaSPC(dadosSQL.getBoolean("pesquisaAutomaticaSPC"));
        } catch (Exception ignored) {
        }
        try{
            obj.setLimitarDescontosPorPerfil(dadosSQL.getBoolean("limitarDescontosPorPerfil"));
        } catch (Exception ignored) {
            obj.setBloqueioTemporario(false);
        }


        try {
            obj.setCobrarMultaJurosAsaas(dadosSQL.getBoolean("cobrarMultaJurosAsaas"));
            obj.setValorMultaAsaas(dadosSQL.getDouble("valorMultaAsaas"));
            obj.setValorJurosAsaas(dadosSQL.getDouble("valorJurosAsaas"));
        } catch (Exception ex){}

        try {
            obj.setConcContasPagarFacilitePay(dadosSQL.getBoolean("concContasPagarFacilitePay"));
        } catch (Exception ignored){}

        try {
            obj.setNomeCurto(dadosSQL.getString("nomecurto"));
        } catch (Exception ignored){}

        try {
            obj.setConcContasReceberFacilitePay(dadosSQL.getBoolean("concContasReceberFacilitePay"));
        } catch (Exception ignored){}

        try {
            obj.setQtdLmtContasConcFacilitePay(dadosSQL.getInt("qtdlmtcontasconcfacilitepay"));
        } catch (Exception ignored) {}

        try {
            obj.setFacilitePayStoneConnect(dadosSQL.getBoolean("facilitePayStoneConnect"));
        } catch (Exception ignored){}
        try {
            obj.setFacilitePayCDLSPC(dadosSQL.getBoolean("facilitePayCDLSPC"));
        } catch (Exception ignored){}
        try {
            obj.setFacilitePayReguaCobrancaEmail(dadosSQL.getBoolean("facilitePayReguaCobrancaEmail"));
            obj.setFacilitePayReguaCobrancaSms(dadosSQL.getBoolean("facilitePayReguaCobrancaSms"));
            obj.setFacilitePayReguaCobrancaApp(dadosSQL.getBoolean("facilitePayReguaCobrancaApp"));
            obj.setFacilitePayReguaCobrancaWhatsApp(dadosSQL.getBoolean("facilitePayReguaCobrancaWhatsApp"));
        } catch (Exception ignored){}
        try {
            obj.setIntegracaoNuvemshopNomeApp(dadosSQL.getString("integracaoNuvemshopNomeApp"));
            obj.setIntegracaoNuvemshopEmail(dadosSQL.getString("integracaoNuvemshopEmail"));
            obj.setIntegracaoNuvemshopTokenAcesso(dadosSQL.getString("integracaoNuvemshopTokenAcesso"));
            obj.setIntegracaoNuvemshopHabilitada(dadosSQL.getBoolean("integracaoNuvemshopHabilitada"));
            obj.setIntegracaoNuvemshopStoreId(dadosSQL.getString("integracaoNuvemshopStoreId"));
        } catch (Exception ignored){}

        try{
            obj.setHabilitarCobrancaAutomaticaNaVenda(dadosSQL.getBoolean("habilitarCobrancaAutomaticaNaVenda"));
        } catch (Exception ignored) {
            obj.setHabilitarCobrancaAutomaticaNaVenda(false);
        }

        try{
            obj.setBloqueioTemporario(dadosSQL.getBoolean("bloqueiotemporario"));
        } catch (Exception ignored) {
            obj.setBloqueioTemporario(false);
        }

        try{
            obj.setIdExterno(dadosSQL.getString("idexterno"));
        } catch (Exception ignored) {
        }

        try{
            obj.setCodigoRede(dadosSQL.getString("codigorede"));
        } catch (Exception ignored) {
        }

        try {
            obj.setSincronizacaoFinanceiro(dadosSQL.getTimestamp("sincronizacaofinanceiro"));
        } catch (Exception ignored) { }

        try {
            obj.setIntegracaoMyWellnessApiKey(dadosSQL.getString("integracaMyWellneApiKey"));
            obj.setIntegracaoMyWellnessFacilityUrl(dadosSQL.getString("integracaoMyWellnessFacilityUrl"));
            obj.setIntegracaoMyWellnessPassword(dadosSQL.getString("integracaoMyWellnessPassword"));
            obj.setIntegracaoMyWellnessUser(dadosSQL.getString("integracaoMyWellnessUser"));
            obj.setIntegracaoMyWellnessHabilitada(dadosSQL.getBoolean("integracaoMyWellneHabilitada"));
            obj.setIntegracaoMyWellnessEnviarVinculos(dadosSQL.getBoolean("integracaoMyWellnessEnviarVinculos"));
            obj.setIntegracaoMyWellnessEnviarGrupos(dadosSQL.getBoolean("integracaoMyWellnessEnviarGrupos"));
        } catch (Exception ignored) { }

        try {
            obj.setDataExpiracao(dadosSQL.getDate("dataExpiracao"));
            obj.setDataExpiracaoNFe(dadosSQL.getDate("dataExpiracaoNfe"));
            obj.setDataExpiracaoVendasOnline(dadosSQL.getDate("dataExpiracaoVendasOnline"));
            obj.setDataExpiracaoApp(dadosSQL.getDate("dataExpiracaoApp"));
            obj.setDataExpiracaoOutros(dadosSQL.getDate("dataExpiracaoOutros"));
            obj.setMotivoExpiracaoOutros(dadosSQL.getString("motivoExpiracaoOutros("));
        } catch (Exception ignored) {}

        try {
            obj.setDetalharNiveisModalidades(dadosSQL.getBoolean("detalharNiveisModalidades"));
        }catch (Exception ignored) {}

        try {
            obj.setExigirAssinaturaDigitalResponsavelFinanceiro(dadosSQL.getBoolean("exigirAssinaturaDigitalResponsavelFinanceiro"));
        }catch (Exception ignored) {}

        try {
            obj.setIntegracaoMentorWebHabilitada(dadosSQL.getBoolean("integracaoMentorWebHabilitada"));
            obj.setIntegracaoMentorWebUrl(dadosSQL.getString("integracaoMentorWebUrl"));
            obj.setIntegracaoMentorWebServico(dadosSQL.getString("integracaoMentorWebServico"));
            obj.setIntegracaoMentorWebUser(dadosSQL.getString("integracaoMentorWebUser"));
            obj.setIntegracaoMentorWebPassword(dadosSQL.getString("integracaoMentorWebPassword"));
        } catch (Exception ignored) {}

        try {
            obj.setIntegracaoF360RelFatHabilitada(dadosSQL.getBoolean("integracaoF360RelFatHabilitada"));
            obj.setIntegracaoF360FtpServer(dadosSQL.getString("integracaoF360FtpServer"));
            obj.setIntegracaoF360FtpPort(dadosSQL.getInt("integracaoF360FtpPort"));
            obj.setIntegracaoF360User(dadosSQL.getString("integracaoF360User"));
            obj.setIntegracaoF360Password(null);
            obj.setIntegracaoF360Dir(dadosSQL.getString("integracaoF360Dir"));
            obj.setIntegracaoF360Quinzenal(dadosSQL.getBoolean("integracaoF360Quinzenal"));
        } catch (Exception ignored) {}

        try {
            obj.setTipoVigenciaMyWellnessGymPass(TipoVigenciaMyWellnessGymPassEnum.getFromId(dadosSQL.getInt("tipovigenciamywellnessgympass")));
            obj.setNrDiasVigenciaMyWellnessGymPass(dadosSQL.getInt("nrdiasvigenciamywellnessgympass"));
        } catch (Exception ignored) {}

        try {
            obj.setIntegracaoAmigoFitHabilitada(dadosSQL.getBoolean("integracaoAmigoFitHabilitada"));
        } catch (Exception ignored) {}

        try {
            obj.setTransferida(dadosSQL.getBoolean("transferida"));
            obj.setNovaChaveTransferencia(dadosSQL.getString("novachave_transferencia"));
            obj.setNovoCodigoTransferencia(dadosSQL.getInt("novocodigo_transferencia"));
            obj.setUtilizarMultaValorAbsoluto(dadosSQL.getBoolean("utilizarMultaValorAbsoluto"));
        } catch (Exception ignored) {}
        try{
            obj.setBloquearSemCartaoVacina(dadosSQL.getBoolean("bloquearSemCartaoVacina"));
            obj.setTipoAnexoCartaoVacina(PessoaAnexoEnum.consultarPorCodigo(dadosSQL.getInt("tipoAnexoCartaoVacina")));
            obj.setIdadeMinimaCartaoVacina(dadosSQL.getInt("idadeMinimaCartaoVacina"));
        } catch (Exception ignored) {
            obj.setBloqueioTemporario(false);
        }
        try {
            obj.setTipoEmpresa(TipoEmpresaFinanceiro.valueOf(dadosSQL.getString("tipoEmpresaFinanceiro")));
        } catch (Exception ignored) {}
        try {
            obj.getProdutoDayUse().setCodigo(dadosSQL.getInt("produtoDayUse"));
            obj.getModalidadeDayUse().setCodigo(dadosSQL.getInt("modalidadeDayUse"));
            obj.getProdutoDiaPlus().setCodigo(dadosSQL.getInt("produtoDiaPlus"));
            obj.getTipoPlanoDiaPlus().setCodigo(dadosSQL.getInt("tipoPlanoDiaPlus"));
            obj.getModalidadeDiaPlus().setCodigo(dadosSQL.getInt("modalidadeDiaPlus"));
        } catch (Exception ignored) {}

        try {
            obj.setUtilizaIntegracaoDelsoft(dadosSQL.getBoolean("utilizaIntegracaoDelsoft"));
            obj.setHostIntegracaoDelsoft(dadosSQL.getString("hostIntegracaoDelsoft"));
            obj.setPortaIntegracaoDelsoft(dadosSQL.getInt("portaIntegracaoDelsoft"));
            obj.setTokenIntegracaoDelsoft(dadosSQL.getString("tokenIntegracaoDelsoft"));
            obj.setNomeAplicacaoDelsoft(dadosSQL.getString("nomeAplicacaoDelsoft"));
            obj.setUsuarioAplicacaoDelsoft(dadosSQL.getString("usuarioAplicacaoDelsoft"));
            obj.setSenhaAplicacaoDelsoft(dadosSQL.getString("senhaAplicacaoDelsoft"));
            obj.getPlanoAplicacaoDelsoft().setCodigo(dadosSQL.getInt("planoAplicacaoDelsoft"));
        } catch (Exception ignored) {}

        try {
            obj.setValorMetaFacilitePay(dadosSQL.getDouble("valorMetaFacilitePay"));
            obj.setIgnorarCodigoDeBarrasEmissaoNfce(dadosSQL.getBoolean("ignorarCodigoDeBarrasEmissaoNfce"));
            obj.setFacilitePayConciliacaoCartao(dadosSQL.getBoolean("facilitePayConciliacaoCartao"));
        } catch (Exception ignored) {}

        try{
            obj.setBloquearAcessoSemTermoResponsabilidade(dadosSQL.getBoolean("bloquearAcessoSemTermoResponsabilidade"));
        }catch (Exception ignored){}

        try{
            obj.setBloquearAcessoDiariaEmpresaDiferente(dadosSQL.getBoolean("bloquearAcessoDiariaEmpresaDiferente"));
        }catch (Exception ignored){}

        try {
            obj.setUtilizaGestaoClientesComRestricoes(dadosSQL.getBoolean("utilizaGestaoClientesComRestricoes"));
        } catch (Exception ignored) {}

        try {
            obj.setPermiteCompartilhamentoPlanoClienteAtivoPlanoCredito(dadosSQL.getBoolean("permiteCompartilhamentoPlanoClienteAtivoPlanoCredito"));
        } catch (Exception ignored) {}
        try {
            obj.setUtilizaConfigCancelamentoSesc(dadosSQL.getBoolean("utilizaConfigCancelamentoSesc"));
        } catch (Exception ignored) {}

        try {
            obj.setHabilitarCobrancaAutomaticaNaVenda(dadosSQL.getBoolean("habilitarCobrancaAutomaticaNaVenda"));
        } catch (Exception ignored) {}

        try {
            obj.setBloquearAcessoMatriculaRematriculaTotemSemPagamento(dadosSQL.getBoolean("bloquearAcessoMatriculaRematriculaTotemSemPagamento"));
        } catch (Exception ignored) {}
        try {
            obj.setBloquearAcessoSeDebitoEmConta(dadosSQL.getBoolean("bloquearAcessoSeDebitoEmConta"));
        } catch (Exception ignored) {}

        try {
            obj.setHorariocapacidadeporcategoria(dadosSQL.getBoolean("horariocapacidadeporcategoria"));
        } catch (Exception ignored) {}

        try {
            obj.setUtilizarPactoPrint(dadosSQL.getBoolean("utilizarPactoPrint"));
            obj.setValidadeMesesCarteirinhaSocio(dadosSQL.getInt("validadeMesesCarteirinhaSocio"));
            obj.setPresidente(dadosSQL.getString("presidente"));
            obj.setSuperintendente(dadosSQL.getString("superintendente"));
        } catch (Exception ignored) {}
        try {
            obj.setIntegracaoManyChatTokenApi(dadosSQL.getString("integracaoManyChatTokenApi"));
            obj.setIntegracaoManyChatHabilitada(dadosSQL.getBoolean("integracaoManyChatHabilitada"));
            obj.setIntegracaoManyChatTagUnidade(dadosSQL.getString("integracaoManyChatTagUnidade"));
        } catch (Exception ignored) {}
        try {
            obj.setEnvioNFCeAutomaticoNoPagamento(dadosSQL.getBoolean("envioNFCeAutomaticoNoPagamento"));

        } catch (Exception ignored) {}

        try{
            obj.setHabilitarValidacaoHorariosMesmaTurma(dadosSQL.getBoolean("habilitarValidacaoHorariosMesmaTurma"));
        } catch (Exception ignored) {
            obj.setHabilitarCobrancaAutomaticaNaVenda(false);
        }

        try {
            obj.setTokenSescDf(dadosSQL.getString("tokensescdf"));
            obj.setUsarSescDf(dadosSQL.getBoolean("usarsescdf"));
        } catch (Exception ignored) {}

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>EmpresaVO</code>.
     *
     * @return O objeto da classe <code>EmpresaVO</code> com os dados
     * devidamente montados.
     */
    public static EmpresaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            EmpresaVO obj = new EmpresaVO();
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setNome(dadosSQL.getString("nome"));
            obj.setCNPJ(dadosSQL.getString("cnpj"));
            obj.setTrabalharComPontuacao(dadosSQL.getBoolean("trabalharComPontuacao"));
            try {
                obj.setCodigoChaveIntegracaoDigitais(dadosSQL.getInt("codigoChaveIntegracaoDigitais"));
                obj.setMoeda(dadosSQL.getString("moeda"));
                obj.setLocaleTexto(dadosSQL.getString("locale"));
                obj.setDescMoeda(dadosSQL.getString("descmoeda"));
                obj.setTipoEmpresa(TipoEmpresaFinanceiro.valueOf(dadosSQL.getString("tipoEmpresaFinanceiro")));
                obj.setNomeCurto(dadosSQL.getString("nomecurto"));
            } catch (Exception ignored) {
            }
            return obj;
        }

        EmpresaVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_EMPRESA_BASICO) {
            return obj;
        }
        montarUsoSistemaInternacional(obj, con);
        montarUsaModuloNotaFiscal(obj, con);

        try {
            obj.setCobrarCreditoPactoBoleto(dadosSQL.getBoolean("cobrarCreditoPactoBoleto"));
        } catch (Exception ex) {
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_GESTAOREMESSA) {
            return obj;
        }

        //montar configs da gestão de personal mantém na mesma conexão, pode estar aqui
        //Colocado para ser diferente do "Tela Consulta" para diminuir o número de consultas na tela de cliente.
        if (nivelMontarDados != Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            montarDadosConfigGestaoPersonal(obj, con);
            obj.setConfiguracaoRDStation(montarDadosConfigRDStation(obj.getCodigo(), con));
            obj.setConfiguracaoEmpresaHubspot(montarDadosConfigHubspot(obj.getCodigo(), con));
            obj.setConfiguracaoIntegracaoBuzzLeadVO(montarDadosConfigBuzzLead(obj.getCodigo(), con));
            obj.setConfiguracaoIntegracaoWordPressVO(montarDadosConfigWordPress(obj.getCodigo(), con));
            obj.setConfiguracaoIntegracaoJoinVO(montarDadosConfigIntegracaoJoin(obj.getCodigo(), con));
            obj.setConfiguracaoIntegracaoGenericaLeadsVO(montarDadosConfigIntegracaoGenericaLeads(obj.getCodigo(), con));
            //obj.setConfiguracaoEmpresaBitrix(montarDadosConfigIntegracaEmpresaBitrix24(obj.getempresa, con));
            obj.setConfiguracaoIntegracaoGenericaLeadsGymbotVO(montarDadosConfigIntegracaoGenericaLeadsGymbot(obj.getCodigo(), con));
        }

        if(obj.isUtilizaSistemaEstacionamento()){
            obj.setConfigEstacionamento(montarDadosConfigEstacionamento(obj.getCodigo(), con));
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            montarDadosCidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ROBO) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosCidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosEstado(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        ContaCorrenteEmpresa contaCorrenteEmpresa = new ContaCorrenteEmpresa(con);
        obj.setContaCorrenteEmpresaVOs(contaCorrenteEmpresa.consultarContaCorrenteEmpresas(obj.getCodigo(), nivelMontarDados));
        contaCorrenteEmpresa = null;

        ProdutoDevolverCancelamentoEmpresa produtoDevolverCancelamentoEmpresaDAO = new ProdutoDevolverCancelamentoEmpresa(con);
        obj.setProdutoDevolverCancelamentoEmpresaVOS(produtoDevolverCancelamentoEmpresaDAO.consultarPorEmpresa(obj.getCodigo(), nivelMontarDados));
        produtoDevolverCancelamentoEmpresaDAO = null;

        ConfiguracaoReenvioMovParcelaEmpresa configuracaoReenvioMovParcelaEmpresa = new ConfiguracaoReenvioMovParcelaEmpresa(con);
        obj.setConfiguracaoReenvioMovParcelaEmpresaVOS(configuracaoReenvioMovParcelaEmpresa.consultarConfiguracaoReenvioMovParcelaEmpresa(obj.getCodigo(), nivelMontarDados));
        configuracaoReenvioMovParcelaEmpresa = null;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        montarDadosEstado(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosCidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosPais(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosLocalAcessoChamada(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosColetorChamada(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    public static void montarUsoSistemaInternacional(EmpresaVO obj, Connection con) {
        try {
            try (ResultSet rs = criarConsulta("select usarsistemainternacional from configuracaosistema", con)) {
                if (rs.next()) {
                    obj.setUsarSistemaInternacional(rs.getBoolean("usarsistemainternacional"));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }
    }

    public static void montarUsaModuloNotaFiscal(EmpresaVO obj, Connection con) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("exists(select codigo from configuracaonotafiscal  where enotas = true ");
            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                sql.append(" and empresa = ").append(obj.getCodigo());
            }
            sql.append(" limit 1) as enotas, \n");
            sql.append("exists(select codigo from configuracaonotafiscal  where enotas = false ");
            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                sql.append(" and empresa = ").append(obj.getCodigo());
            }
            sql.append(" limit 1) as notaDelphi \n");
            sql.append("from empresa \n");
            sql.append("limit 1 ");

            try (ResultSet rs = criarConsulta(sql.toString(), con)) {
                if (rs.next()) {
                    obj.setUsaEnotas(rs.getBoolean("enotas"));
                    obj.setUsaNotasDelphi(rs.getBoolean("notaDelphi"));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }
    }

    public static void montarDadosConfigGestaoPersonal(EmpresaVO obj, Connection con) {
        try {
            try (ResultSet set = criarConsulta("SELECT * FROM configuracaogestaopersonal where empresa = " + obj.getCodigo(), con)) {
                if (set.next()) {
                    ConfiguracaoGestaoPersonalVO cfg = new ConfiguracaoGestaoPersonalVO();
                    cfg.setBloquearAcessoPersonalSemCredito(set.getBoolean("BloquearAcessoPersonalSemCredito"));
                    cfg.setConsumirCreditoPorAlunoVinculado(set.getBoolean("ConsumirCreditoPorAlunoVinculado"));
                    cfg.setGerarCreditoSomenteAoPagar(set.getBoolean("GerarCreditoSomenteAoPagar"));
                    cfg.setMostrarFotosAlunosMonitor(set.getBoolean("MostrarFotosAlunosMonitor"));
                    cfg.setObrigatorioAssociarAlunoAoCheckIn(set.getBoolean("ObrigatorioAssociarAlunoAoCheckIn"));
                    cfg.setDiasBloqueioParcelaEmAberto(set.getInt("DiasBloqueioParcelaEmAberto"));
                    cfg.setDuracaoCredito(set.getInt("DuracaoCredito"));
                    cfg.setTempoCheckOutAutomatico(set.getInt("tempocheckoutautomatico"));
                    cfg.setUsarFotoPersonal(set.getBoolean("usarfotopersonal"));
                    cfg.setEmpresa(obj.getCodigo());
                    try (ResultSet setEmail = criarConsulta("SELECT * FROM configuracaogestaopersonalemail where empresa = " + obj.getCodigo(), con)) {
                        while (setEmail.next()) {
                            ConfiguracaoGestaoPersonalEmailVO cfgEmail = new ConfiguracaoGestaoPersonalEmailVO(setEmail.getString("email"));
                            cfgEmail.setEmpresa(obj.getCodigo());
                            cfg.getEmails().add(cfgEmail);
                        }
                    }
                    obj.setConfigsPersonal(cfg);
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }
    }

    public void inserirConfigsGestaoPersonal(EmpresaVO obj) throws Exception {
        if (obj.isUsarGestaoCreditosPersonal() && obj.getConfigsPersonal() != null) {
            executarConsulta("DELETE FROM configuracaogestaopersonal WHERE empresa = " + obj.getCodigo(), con);
            try (PreparedStatement stm = con.prepareStatement("INSERT INTO configuracaogestaopersonal "
                    + "(BloquearAcessoPersonalSemCredito, ConsumirCreditoPorAlunoVinculado, "
                    + "GerarCreditoSomenteAoPagar, ObrigatorioAssociarAlunoAoCheckIn,MostrarFotosAlunosMonitor, "
                    + "DiasBloqueioParcelaEmAberto, DuracaoCredito, empresa, tempocheckoutautomatico, usarfotopersonal) VALUES (?,?,?,?,?,?,?,?,?, ?)")) {
                int i = 1;
                stm.setBoolean(i++, obj.getConfigsPersonal().isBloquearAcessoPersonalSemCredito());
                stm.setBoolean(i++, obj.getConfigsPersonal().isConsumirCreditoPorAlunoVinculado());
                stm.setBoolean(i++, obj.getConfigsPersonal().isGerarCreditoSomenteAoPagar());
                stm.setBoolean(i++, obj.getConfigsPersonal().isObrigatorioAssociarAlunoAoCheckIn());
                stm.setBoolean(i++, obj.getConfigsPersonal().isMostrarFotosAlunosMonitor());
                stm.setInt(i++, obj.getConfigsPersonal().getDiasBloqueioParcelaEmAberto());
                stm.setInt(i++, obj.getConfigsPersonal().getDuracaoCredito());
                stm.setInt(i++, obj.getCodigo());
                stm.setInt(i++, obj.getConfigsPersonal().getTempoCheckOutAutomatico());
                stm.setBoolean(i++, obj.getConfigsPersonal().isUsarFotoPersonal());
                stm.execute();
            }
            executarConsulta("DELETE FROM configuracaogestaopersonalemail WHERE empresa = " + obj.getCodigo(), con);
            for (ConfiguracaoGestaoPersonalEmailVO cfgEmail : obj.getConfigsPersonal().getEmails()) {
                try (PreparedStatement stmEmail = con.prepareStatement("INSERT INTO configuracaogestaopersonalemail (email, empresa) VALUES (?,?)")) {
                    stmEmail.setString(1, cfgEmail.getEmail());
                    stmEmail.setInt(2, obj.getCodigo());
                    stmEmail.execute();
                }
            }
        }

    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>QuestionarioVO</code> relacionado ao objeto
     * <code>EmpresaVO</code>. Faz uso da chave primária da classe
     * <code>QuestionarioVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosEstado(EmpresaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEstado().getCodigo() == 0) {
            obj.setEstado(new EstadoVO());
            return;
        }
        Estado estado = new Estado(con);
        obj.setEstado(estado.consultarPorChavePrimaria(obj.getEstado().getCodigo(), nivelMontarDados));
        estado = null;
    }

    public static void montarDadosCidade(EmpresaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCidade().getCodigo() == 0) {
            obj.setCidade(new CidadeVO());
            return;
        }
        Cidade cidade = new Cidade(con);
        obj.setCidade(cidade.consultarPorChavePrimaria(obj.getCidade().getCodigo(), nivelMontarDados));
        cidade = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>QuestionarioVO</code> relacionado ao objeto
     * <code>EmpresaVO</code>. Faz uso da chave primária da classe
     * <code>QuestionarioVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPais(EmpresaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPais().getCodigo() == 0) {
            obj.setPais(new PaisVO());
            return;
        }
        Pais pais = new Pais(con);
        obj.setPais(pais.consultarPorChavePrimaria(obj.getPais().getCodigo(), nivelMontarDados));
        pais = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>EmpresaVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public EmpresaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        EmpresaVO eCache = (EmpresaVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT Empresa.* FROM Empresa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Empresa ).");
                }
                eCache = montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public EmpresaVO consultarPorChavePrimariaLogin(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Empresa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Empresa ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>EmpresaVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public EmpresaVO consultarPorChavePrimaria(Integer codigoPrm, Boolean administrador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        EmpresaVO eCache = (EmpresaVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT Empresa.* FROM Empresa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    if (administrador) {
                        return new EmpresaVO();
                    } else {
                        throw new ConsistirException("Dados Não Encontrados ( Empresa ).");
                    }
                }
                eCache = montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
        putToCache(eCache);
        return eCache;
    }

    public EmpresaVO consultarPorChavePrimariaLogin(Integer codigoPrm, Boolean administrador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        EmpresaVO eCache = (EmpresaVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM Empresa WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    if (administrador) {
                        return new EmpresaVO();
                    } else {
                        throw new ConsistirException("Dados Não Encontrados ( Empresa ).");
                    }
                }
                eCache = montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
        putToCache(eCache);
        return eCache;
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave
     * primária. É utilizada para obter o valor gerado pela SGBD para uma chave
     * primária, a apresentação do mesmo e a implementação de possíveis
     * relacionamentos.
     */
    public String obterMascaraMatricula(Integer empresa) throws Exception {
        inicializar();
        String sqlStr = "SELECT Empresa.mascaraMatricula FROM Empresa  WHERE codigo = " + empresa + "";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getString("mascaraMatricula")));
            }
        }
    }

    public Integer obterCarenciaEmpresa(Integer empresa) throws Exception {
        inicializar();
        String sqlStr = "SELECT Empresa.carencia FROM Empresa  WHERE codigo = " + empresa + "";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return ((tabelaResultado.getInt("carencia")));
            }
        }
    }

    public String obterTimeZoneDefault(Integer empresa) throws Exception {
        String sqlStr = "SELECT timeZoneDefault FROM Empresa ";
        if(empresa == null){
            sqlStr += "LIMIT 1";
        }else{
            sqlStr += "WHERE codigo = " + empresa;
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getString("timeZoneDefault");
            }
        }

    }

    public Boolean obterConfiguracaoLiberarPersonalComDebito(Integer empresa) throws Exception {
        String sqlStr = "SELECT liberarPersonalProfessorDebito FROM Empresa  WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("liberarPersonalProfessorDebito");
            }
        }
    }

    public Boolean isCobrarCreditoPactoBoleto(Integer empresa) throws Exception {
        String sqlStr = "SELECT cobrarCreditoPactoBoleto FROM Empresa  WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("cobrarCreditoPactoBoleto");
            }
        }
    }


    @Override
    public EmpresaVO obterEmpresaDeUmaListaParcelas(List<MovParcelaVO> listaParc) throws Exception {
        EmpresaVO emp = null;
        for (MovParcelaVO movParcelaVO : listaParc) {
            if (movParcelaVO.getEmpresa() != null && movParcelaVO.getEmpresa().getCodigo() != 0) {
                emp = consultarPorChavePrimaria(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                break;
            }
        }
        return emp;
    }

    @Override
    public byte[] obterFoto(final String chave, final Integer codigoEmpresa,
                            MidiaEntidadeEnum tipo) throws Exception {
        //TODO Irmaoo de Deus se der problema aqui por causa de uma foto mata todo do processo
        try{
            if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                byte[] foto = null;
                if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
                    foto = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                            tipo, codigoEmpresa.toString(), null);
                }
                if (foto == null) {
                    foto = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                            tipo, null, null);
                } else {
                    return foto;
                }
                return foto;
            } else {
                return obterFotoBanco(codigoEmpresa, tipo);
            }

        }catch (Exception e){
            e.printStackTrace();
            return null;
        }

    }

    @Override
    public byte[] obterFotoPadrao(final String chave, final Integer codigoEmpresa,
                            MidiaEntidadeEnum tipo) throws Exception {
        byte[] foto = null;
        if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
            try {
                if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
                    try {
                        foto = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                                tipo, codigoEmpresa.toString(), null);
                    }catch (NoClassDefFoundError ncdf){
                        Logger.getLogger(Empresa.class.getName()).log(Level.SEVERE, ncdf.getMessage(), ncdf);
                        return obterFotoBanco(codigoEmpresa, tipo);
                    }
                }
                if (foto == null) {
                    try {
                        foto = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                                tipo, null, null);
                    }catch (NoClassDefFoundError ncdf){
                        Logger.getLogger(Empresa.class.getName()).log(Level.SEVERE, ncdf.getMessage(), ncdf);
                        return obterFotoBanco(codigoEmpresa, tipo);
                    }
                } else {
                    return foto;
                }

            } catch (Exception ex) {
                Logger.getLogger(Empresa.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
                return obterFotoBanco(codigoEmpresa, tipo);
            }
        }
        return foto;
    }

    public byte[] obterFotoBanco(final Integer codigoEmpresa, MidiaEntidadeEnum tipo) throws SQLException {
        byte[] foto = null;
        String sql = String.format("SELECT %s FROM empresa WHERE ((codigo = ?))", tipo.getNomeCampo());
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoEmpresa);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    try {
                        foto = rs.getBytes(1);
                        // Quando a coluna está vazia uma exceção é lançada
                        // ArrayIndexOutOfBoundsException
                    } catch (ArrayIndexOutOfBoundsException ex) {
                    }

                }
            }
        }
        return foto;

    }

    public void atualizarNrdiascompensacao(Integer nrdiascompensacao, Integer codigoEmpresa) throws Exception {
        String sql = "UPDATE empresa SET nrdiascompensacao = " + nrdiascompensacao + " WHERE codigo = " + codigoEmpresa;
        executarConsulta(sql, con);
    }

    @Override
    public String obterTokenSMS(final int empresa) throws Exception {
        String sql = "select tokensms from empresa where codigo = " + empresa;

        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getString(1);
            } else {
                return null;
            }
        }
    }

    @Override
    public String obterTokenMQV(final int empresa) throws Exception {
        String sql = "select tokenmqv from empresa where codigo = " + empresa;

        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getString(1);
            } else {
                return null;
            }
        }
    }

    @Override
    public String obterTokenSMSShortCode(final int empresa) throws Exception {
        String sql = "select tokensmsshortcode from empresa where codigo = " + empresa;

        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getString(1);
            } else {
                return null;
            }
        }
    }

    @Override
    public Date obterDataExpiracao(final int empresa) throws Exception {
        String sql = "select dataExpiracao from empresa where codigo = " + empresa;
        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getDate(1);
            } else {
                return null;
            }
        }
    }

    public String consultarJSON() throws Exception {

        StringBuilder json;
        boolean dados;
        try (PreparedStatement ps = getRS()) {
            try (ResultSet rs = ps.executeQuery()) {

                json = new StringBuilder();
                json.append("{\"aaData\":[");
                dados = false;
                while (rs.next()) {
                    dados = true;
                    json.append("[\"").append(rs.getString("codigo")).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome").trim())).append("\",");
                    if (rs.getBoolean("ativa")) {
                        json.append("\"").append("Ativa").append("\",");
                    } else {
                        json.append("\"").append("Inativa").append("\",");
                    }
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("razaosocial"))).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomeCidade"))).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("inscestadual"))).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("cnpj"))).append("\"],");
                }
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getRS() throws SQLException {
        StringBuilder sql = new StringBuilder("select em.codigo, em.nome,em.ativa, em.razaosocial, em.inscestadual, em.cnpj, c.nome as nomeCidade from empresa em ");
        sql.append(" LEFT JOIN cidade c ON c.codigo = em.cidade \n");
        return con.prepareStatement(sql.toString());
    }

    @Override
    public List<EmpresaVO> consultarEmpresas() throws Exception {
        return consultarEmpresas(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    @Override
    public List<EmpresaVO> consultarEmpresas(int nivelMontarDados) throws Exception {
        try (ResultSet empresas = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa ORDER BY nome ", con)) {
            return montarDadosConsulta(empresas, nivelMontarDados, con);
        }
    }


    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {

        List lista;
        try (PreparedStatement ps = getRS()) {
            try (ResultSet rs = ps.executeQuery()) {
                lista = new ArrayList();

                while (rs.next()) {

                    SuperEmpresaVO emp = new SuperEmpresaVO();
                    String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("razaosocial") + rs.getString("inscestadual") + rs.getString("cnpj") + rs.getString("nomeCidade");
                    if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                        emp.setCodigo(rs.getInt("codigo"));
                        emp.setNome(rs.getString("nome"));
                        emp.setRazaoSocial(rs.getString("razaosocial"));
                        emp.setInscEstadual(rs.getString("inscestadual"));
                        emp.setCNPJ(rs.getString("cnpj"));
                        emp.getCidade().setNome(rs.getString("nomeCidade"));
                        emp.setAtiva(rs.getBoolean("ativa"));
                        lista.add(emp);
                    }
                }
            }
        }
        switch (campoOrdenacao) {
            case "Código":
                Ordenacao.ordenarLista(lista, "codigo");
                break;
            case "Nome":
                Ordenacao.ordenarLista(lista, "nome");
                break;
            case "Razão Social":
                Ordenacao.ordenarLista(lista, "razaoSocial");
                break;
            case "Cidade":
                Ordenacao.ordenarLista(lista, "cidade_Apresentar");
                break;
            case "Inscrição Estadual":
                Ordenacao.ordenarLista(lista, "inscEstadual");
                break;
            case "CNPJ":
                Ordenacao.ordenarLista(lista, "CNPJ");
                break;
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    public void debitarCreditoDCC(Integer quantidadeSubtrair, Integer codigoEmpresa, String observacao) throws Exception {
        new CreditoDCCService(this.getCon()).alterarSaldo(codigoEmpresa, (-1 * quantidadeSubtrair), observacao);
    }

    public static void montarDadosLocalAcessoChamada(EmpresaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getLocalAcessoChamada() == null || obj.getLocalAcessoChamada().getCodigo() == 0) {
            obj.setLocalAcessoChamada(new LocalAcessoVO());
            return;
        }
        LocalAcesso localAcesso = new LocalAcesso(con);
        obj.setLocalAcessoChamada(localAcesso.consultarPorCodigo(obj.getLocalAcessoChamada().getCodigo(), nivelMontarDados));
        localAcesso = null;
    }

    public static void montarDadosColetorChamada(EmpresaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getColetorChamada() == null || obj.getColetorChamada().getCodigo() == 0) {
            obj.setColetorChamada(new ColetorVO());
            return;
        }
        Coletor coletor = new Coletor(con);
        obj.setColetorChamada(coletor.consultarPorCodigo(obj.getColetorChamada().getCodigo()));
        coletor = null;
    }

    public JSONArray consultarEmpresasJson() throws Exception{
        JSONArray json;
        try (ResultSet rs = criarConsulta("select empresa.nome, empresa.codigo, estado.sigla as estado from empresa\n" +
                "LEFT JOIN estado ON estado.codigo = empresa.estado", con)) {
            json = new JSONArray();
            while (rs.next()) {
                JSONObject obj = new JSONObject();
                obj.put("estado", rs.getString("estado"));
                obj.put("nome", rs.getString("nome"));
                obj.put("codigo", rs.getInt("codigo"));
                json.put(obj);
            }
        }
        return json;
    }
    public JSONObject obterParametrosAtendimentoSol(int codigoEmpresa,int codigoUsuario) throws Exception{

        EmpresaVO empresaVO  = consultarPorChavePrimaria(codigoEmpresa,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        UsuarioVO usuarioVO = new Usuario(this.con).consultarPorChavePrimaria(codigoUsuario,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        String nomeUsuarioLogado = usuarioVO.getNome();
        String email = usuarioVO.getColaboradorVO().getPessoa().getEmail();
        String usuario = usuarioVO.getServiceUsuario();
        String senha = usuarioVO.getServiceSenha();
        String empresa = usuarioVO.getNome();
        String telefone = empresaVO.getTelComercial1();

        if (!UteisValidacao.emptyString(usuario)) {
            usuario = usuarioVO.getServiceUsuario();
            senha = usuarioVO.getServiceSenha();
        } else if(!UteisValidacao.emptyString(empresaVO.getServiceUsuario())) {
            usuario = empresaVO.getServiceUsuario();
            senha = empresaVO.getServiceSenha();
            email = empresaVO.getEmail();
        }

        if (!UteisValidacao.emptyString(usuario)) {
            senha = Criptografia.decrypt(senha, SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM);
        }
        StringBuilder sb = new StringBuilder();
        if (!UteisValidacao.emptyString(email)) {
            sb.append("email=").append(email).append(";");
        } else if (!UteisValidacao.emptyString(empresaVO.getEmail())) {
            sb.append("email=").append(empresaVO.getEmail()).append(";");
        }

        if (!UteisValidacao.emptyString(empresa)) {
            sb.append("empresa=").append(empresa).append(";");
        }

        if (!UteisValidacao.emptyString(telefone)) {
            sb.append("telefone=").append(telefone).append(";");
        }

        if (!UteisValidacao.emptyString(usuario)) {
            sb.append("usuarioservice=").append(usuario).append(";");
        }

        if (!UteisValidacao.emptyString(senha)) {
            sb.append("senhaservice=").append(senha).append(";");
        }

        if (!UteisValidacao.emptyString(nomeUsuarioLogado)) {
            sb.append("nomeusuariologado=").append(nomeUsuarioLogado).append(";");
        }

        JSONObject obj = new JSONObject();
        JSONObject objLoginService = new JSONObject();
        objLoginService.put("usuario",usuario);
        objLoginService.put("senha",senha);
        obj.put("paramSol",Uteis.encriptarSol(sb.toString()));
        obj.put("loginService", objLoginService);

        return obj;
    }

    public Map<Integer, EmpresaVO> obterMapaEmpresas(int nivelMontarDados) throws Exception {
        Map<Integer, EmpresaVO> mapaEmpresas = new HashMap<>();

        List<EmpresaVO> empresas = consultarEmpresas(nivelMontarDados);
        for (EmpresaVO empresaVO : empresas) {
            mapaEmpresas.put(empresaVO.getCodigo(), empresaVO);
        }

        return mapaEmpresas;
    }

    public Map<Integer, EmpresaVO> obterMapaEmpresas() throws Exception {
        return obterMapaEmpresas(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    public int consultarSaldoDCC(int codigoEmpresa) throws Exception {
        String sql = "SELECT creditodcc FROM empresa WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codigoEmpresa);
            try (ResultSet rs = sqlAlterar.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("creditodcc");
                }
            }
        }
        return 0;
    }

    public void atualizarSequencialLoteRPS(Integer sequencialLoteRPS, Integer codEmpresa) throws Exception {
        executarConsulta("update empresa set sequencialLoteRPS = " + sequencialLoteRPS + " where codigo = " + codEmpresa, con);
    }

    public String obterCnpjEmpresa(Integer empresa) throws Exception{
        try (ResultSet rs = criarConsulta("select cnpj from empresa where codigo = " + empresa, con)) {
            return rs.next() ? rs.getString("cnpj") : "";
        }
    }

    public boolean toggleBloqueioTemporario(Integer empresa, boolean consulta) throws Exception{
        try (ResultSet rs = criarConsulta("select bloqueiotemporario from empresa where codigo = " + empresa, con)) {
            if (rs.next()) {
                boolean simples = rs.getBoolean("bloqueiotemporario");

                if (consulta) {
                    return simples;
                }

                executarConsulta("update empresa set bloqueiotemporario = "
                        + (!simples) + " where codigo = " + empresa, con);

                return !simples;
            }
        }
        throw new Exception("Empresa não encontrada");
    }

    public void tratarEdicaoConfiguracaoRD(EmpresaVO obj) throws Exception{
        if (obj.getConfiguracaoRDStation().isEmpresaUsaRD()) {
            inserirConfigsRDStation(obj);
        }else{
            alteraAlgunsCamposRD(obj);
        }
    }

    public void alteraAlgunsCamposRD(EmpresaVO obj) throws Exception{
        boolean existe = SuperFacadeJDBC.existe("select codigo from configuracaoempresardstation where empresa = " +
                obj.getCodigo(), con);
        if(existe){
            try (PreparedStatement stm = con.prepareStatement("update configuracaoempresardstation " +
                    "set responsavelpadrao = ?, horalimite = ?, acaoobjecao = ? where empresa = ? ")) {
                int i = 1;
                if (!UteisValidacao.emptyNumber(obj.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())) {
                    stm.setInt(i++, obj.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo());
                } else {
                    stm.setNull(i++, 0);
                }
                stm.setString(i++, obj.getConfiguracaoRDStation().getHoraLimite());
                stm.setInt(i++, obj.getConfiguracaoRDStation().getAcaoObjecao());
                stm.setInt(i++, obj.getCodigo());
                stm.execute();
            }
        }else{
            inserirConfigsRDStation(obj);
        }

    }

    public void inserirConfigsRDStation(EmpresaVO obj) throws Exception {
        executarConsulta("DELETE FROM configuracaoempresardstation WHERE empresa = " + obj.getCodigo(), con);
        try (PreparedStatement stm = con.prepareStatement("INSERT INTO configuracaoempresardstation(\n" +
                "             empresa, responsavelpadrao, empresausard, chavepublica, \n" +
                "            chaveprivada, horalimite, acaoobjecao, clientidoauthrds, clientsecretoauthrds, eventWeebHook, \n" +
                "            accesstokenrdstationmarketing, refreshtokenrdstationmarketing)\n" +
                "    VALUES (?, ?, ?, ?, ?, \n" +
                "            ?, ?, ?, ?, ?, ?, ?);")) {
            int i = 1;
            stm.setInt(i++, obj.getCodigo());
            if (!UteisValidacao.emptyNumber(obj.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())) {
                stm.setInt(i++, obj.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo());
            } else {
                stm.setNull(i++, 0);
            }
            stm.setBoolean(i++, obj.getConfiguracaoRDStation().isEmpresaUsaRD());
            stm.setString(i++, obj.getConfiguracaoRDStation().getClientIdOauth() != null ? obj.getConfiguracaoRDStation().getClientIdOauth().trim() : "");
            stm.setString(i++, obj.getConfiguracaoRDStation().getClientSecretOauth() != null ? obj.getConfiguracaoRDStation().getClientSecretOauth().trim() : "");
            stm.setString(i++, obj.getConfiguracaoRDStation().getHoraLimite());
            stm.setInt(i++, obj.getConfiguracaoRDStation().getAcaoObjecao());
            stm.setString(i++, obj.getConfiguracaoRDStation().getClientIdOauth());
            stm.setString(i++, obj.getConfiguracaoRDStation().getClientSecretOauth());
            stm.setString(i++, obj.getConfiguracaoRDStation().getEventWeebHook());

            stm.setString(i++, obj.getConfiguracaoRDStation().getAccessTokenRdStationMarketing() != null ? obj.getConfiguracaoRDStation().getAccessTokenRdStationMarketing().trim() : "");
            stm.setString(i++, obj.getConfiguracaoRDStation().getRefreshTokenRdStationMarketing() != null ? obj.getConfiguracaoRDStation().getRefreshTokenRdStationMarketing().trim() : "");

            stm.execute();
        }
    }

    public static ConfiguracaoEmpresaRDStationVO montarDadosConfigRDStation(Integer codigoEmpresa, Connection con) throws SQLException {
        ConfiguracaoEmpresaRDStationVO cfg = new ConfiguracaoEmpresaRDStationVO();
        try {
            try (ResultSet set = criarConsulta("SELECT * FROM configuracaoempresardstation where empresa = " + codigoEmpresa, con)) {
                if (set.next()) {
                    cfg.setNovoObj(false);
                    cfg.setEmpresaUsaRD(set.getBoolean("empresausard"));
                    cfg.getResponsavelPadrao().setCodigo(set.getInt("responsavelpadrao"));
                    cfg.setChavePublica(set.getString("clientidoauthrds"));
                    cfg.setChavePrivada(set.getString("clientsecretoauthrds"));
                    cfg.setHoraLimite(set.getString("horalimite"));
                    cfg.setEmpresa(codigoEmpresa);
                    cfg.setAcaoObjecao(set.getInt("acaoobjecao"));
                    cfg.setClientIdOauth(set.getString("clientidoauthrds"));
                    cfg.setClientSecretOauth(set.getString("clientsecretoauthrds"));
                    cfg.setEventWeebHook(set.getString("eventWeebHook"));
                    cfg.setConfigAtualizarAlunoRdStationMarketing(set.getBoolean("configatualizaralunordstationmarketing"));
                    cfg.setAccessTokenRdStationMarketing(set.getString("accesstokenrdstationmarketing"));
                    cfg.setRefreshTokenRdStationMarketing(set.getString("refreshtokenrdstationmarketing"));
                    if (!UteisValidacao.emptyNumber(cfg.getResponsavelPadrao().getCodigo())) {
                        Usuario usuarioDAO = new Usuario(con);
                        cfg.setResponsavelPadrao(usuarioDAO.consultarPorCodigo(cfg.getResponsavelPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
                        usuarioDAO = null;
                    }
                }
            }
        } catch (Exception e) {
            Logger.getLogger(con.getMetaData().getURL());
            Uteis.logar(e, Empresa.class);
        }
        return cfg;
    }

    public static ConfiguracaoIntegracaoBuzzLeadVO montarDadosConfigBuzzLead(Integer codigoEmpresa, Connection con) throws SQLException {
        ConfiguracaoIntegracaoBuzzLeadVO cfg = new ConfiguracaoIntegracaoBuzzLeadVO();
        try {
            try (ResultSet set = criarConsulta("SELECT * FROM configuracaointegracaobuzzlead where empresa = " + codigoEmpresa, con)) {
                if (set.next()) {
                    cfg.setNovoObj(false);
                    cfg.setHabilitada(set.getBoolean("habilitada"));
                    cfg.getResponsavelPadrao().setCodigo(set.getInt("responsavelpadrao"));
                    cfg.setHoraLimite(set.getString("horalimite"));
                    cfg.setEmpresa(codigoEmpresa);
                    cfg.setAcaoObjecao(set.getInt("acaoobjecao"));
                    if (!UteisValidacao.emptyNumber(cfg.getResponsavelPadrao().getCodigo())) {
                        Usuario usuarioDAO = new Usuario(con);
                        cfg.setResponsavelPadrao(usuarioDAO.consultarPorCodigo(cfg.getResponsavelPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
                        usuarioDAO = null;
                    }
                }
            }
        } catch (Exception e) {
            Logger.getLogger(con.getMetaData().getURL());
            Uteis.logar(e, Empresa.class);
        }
        return cfg;
    }

    public static ConfiguracaoIntegracaoWordPressVO montarDadosConfigWordPress(Integer codigoEmpresa, Connection con) throws SQLException {
        ConfiguracaoIntegracaoWordPressVO cfg = new ConfiguracaoIntegracaoWordPressVO();
        try {
            try (ResultSet set = criarConsulta("SELECT * FROM configuracaointegracaowordpress where empresa = " + codigoEmpresa, con)) {
                if (set.next()) {
                    cfg.setNovoObj(false);
                    cfg.setHabilitada(set.getBoolean("habilitada"));
                    cfg.getResponsavelPadrao().setCodigo(set.getInt("responsavelpadrao"));
                    cfg.setHoraLimite(set.getString("horalimite"));
                    cfg.setEmpresa(codigoEmpresa);
                    cfg.setAcaoObjecao(set.getInt("acaoobjecao"));
                    if (!UteisValidacao.emptyNumber(cfg.getResponsavelPadrao().getCodigo())) {
                        Usuario usuarioDAO = new Usuario(con);
                        cfg.setResponsavelPadrao(usuarioDAO.consultarPorCodigo(cfg.getResponsavelPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
                        usuarioDAO = null;
                    }
                }
            }
        } catch (Exception e) {
            Logger.getLogger(con.getMetaData().getURL());
            Uteis.logar(e, Empresa.class);
        }
        return cfg;
    }

    public static ConfiguracaoIntegracaoJoinVO montarDadosConfigIntegracaoJoin(Integer codigoEmpresa, Connection con) throws SQLException {
        ConfiguracaoIntegracaoJoinVO cfg = new ConfiguracaoIntegracaoJoinVO();
        try {
            try (ResultSet set = criarConsulta("SELECT * FROM configuracaointegracaojoin where empresa = " + codigoEmpresa, con)) {
                if (set.next()) {
                    cfg.setNovoObj(false);
                    cfg.setHabilitada(set.getBoolean("habilitada"));
                    cfg.getResponsavelPadrao().setCodigo(set.getInt("responsavelpadrao"));
                    cfg.setHoraLimite(set.getString("horalimite"));
                    cfg.setEmpresa(codigoEmpresa);
                    cfg.setAcaoObjecao(set.getInt("acaoobjecao"));
                    if (!UteisValidacao.emptyNumber(cfg.getResponsavelPadrao().getCodigo())) {
                        Usuario usuarioDAO = new Usuario(con);
                        cfg.setResponsavelPadrao(usuarioDAO.consultarPorCodigo(cfg.getResponsavelPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
                        usuarioDAO = null;
                    }
                }
            }
        } catch (Exception e) {
            Logger.getLogger(con.getMetaData().getURL());
            Uteis.logar(e, Empresa.class);
        }
        return cfg;
    }

    public ConfiguracaoEmpresaBitrixVO consultarConfigIntegracaEmpresaBitrix24(String chave,  Connection con) throws Exception {
        ConfiguracaoEmpresaBitrixVO item = new ConfiguracaoEmpresaBitrixVO();
        try (ResultSet consulta = criarConsulta("SELECT * FROM configuracaoempresabitrix24 where empresa = '" + chave + "'", con)) {
            if (consulta.next()) {
                item.setAcaoobjecao(consulta.getInt("acaoobjecao"));
                item.setEmpresa(consulta.getString("empresa"));
                item.setResponsavelPadrao(consulta.getInt("responsavelpadrao"));
                item.setAcao(consulta.getString("acao"));
                item.setHabilitada(consulta.getBoolean("habilitada"));
                item.setUrl(consulta.getString("url"));

            }
        }
        return item;
    }

    public static ConfiguracaoIntegracaoGenericaLeadsVO montarDadosConfigIntegracaoGenericaLeads(Integer codigoEmpresa, Connection con) throws SQLException {
        ConfiguracaoIntegracaoGenericaLeadsVO cfg = new ConfiguracaoIntegracaoGenericaLeadsVO();
        try {
            try (ResultSet set = criarConsulta("SELECT * FROM configuracaointegracaogenericaleads where empresa = " + codigoEmpresa, con)) {
                if (set.next()) {
                    cfg.setNovoObj(false);
                    cfg.setHabilitada(set.getBoolean("habilitada"));
                    cfg.getResponsavelPadrao().setCodigo(set.getInt("responsavelpadrao"));
                    cfg.setHoraLimite(set.getString("horalimite"));
                    cfg.setEmpresa(codigoEmpresa);
                    cfg.setAcaoObjecao(set.getInt("acaoobjecao"));
                    if (!UteisValidacao.emptyNumber(cfg.getResponsavelPadrao().getCodigo())) {
                        Usuario usuarioDAO = new Usuario(con);
                        cfg.setResponsavelPadrao(usuarioDAO.consultarPorCodigo(cfg.getResponsavelPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
                        usuarioDAO = null;
                    }
                }
            }
        } catch (Exception e) {
            Logger.getLogger(con.getMetaData().getURL());
            Uteis.logar(e, Empresa.class);
        }
        return cfg;
    }


    public static ConfiguracaoIntegracaoGenericaLeadsGymbotVO montarDadosConfigIntegracaoGenericaLeadsGymbot(Integer codigoEmpresa, Connection con) throws SQLException {
        ConfiguracaoIntegracaoGenericaLeadsGymbotVO cfg = new ConfiguracaoIntegracaoGenericaLeadsGymbotVO();
        try {
            try (ResultSet set = criarConsulta("SELECT * FROM configuracaointegracaogenericaleadsgymbot where empresa = " + codigoEmpresa, con)) {
                if (set.next()) {
                    cfg.setNovoObj(false);
                    cfg.setHabilitada(set.getBoolean("habilitada"));
                    cfg.getResponsavelPadrao().setCodigo(set.getInt("responsavelpadrao"));
                    cfg.setHoraLimite(set.getString("horalimite"));
                    cfg.setEmpresa(codigoEmpresa);
                    cfg.setAcaoObjecao(set.getInt("acaoobjecao"));
                    if (!UteisValidacao.emptyNumber(cfg.getResponsavelPadrao().getCodigo())) {
                        Usuario usuarioDAO = new Usuario(con);
                        cfg.setResponsavelPadrao(usuarioDAO.consultarPorCodigo(cfg.getResponsavelPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
                        usuarioDAO = null;
                    }
                }
            }
        } catch (Exception e) {
            Logger.getLogger(con.getMetaData().getURL());
            Uteis.logar(e, Empresa.class);
        }
        return cfg;
    }

    @Override
    public void atualizarLimiteInicialItensBIPendencia(EmpresaVO empresaVO, Date limiteInicialItensBIPendencia) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "UPDATE public.Empresa set limiteInicialItensBIPendencia = ? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setDate(1, Uteis.getDataJDBC(limiteInicialItensBIPendencia));
                sqlAlterar.setInt(2, empresaVO.getCodigo());
                sqlAlterar.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public static ConfiguracaoEmpresaHubspotVO montarDadosConfigHubspot(Integer codigoEmpresa, Connection con) throws SQLException {
        ConfiguracaoEmpresaHubspotVO cfg = new ConfiguracaoEmpresaHubspotVO();
        try {
            try (ResultSet set = criarConsulta("SELECT * FROM configuracaoempresahubspot where empresa = " + codigoEmpresa, con)) {
                if (set.next()) {
                    cfg.setNovoObj(false);
                    cfg.setCodigo(set.getInt("codigo"));
                    cfg.setEmpresausahub(set.getBoolean("empresausahub"));
                    cfg.setClientId(set.getString("clientId"));
                    cfg.setUrl_instalacao(set.getString("url_instalacao"));
                    cfg.setUrl_redirect(set.getString("url_redirect"));
                    cfg.setHoraexpiracao(set.getString("horaexpiracao"));
                    cfg.setEmpresa(codigoEmpresa);
                    cfg.setClientsecret(set.getString("clientsecret"));
                    cfg.setHoraLimite(set.getString("horalimite"));
                    cfg.setAcaoObjecao(set.getInt("acaoobjecao"));
                    cfg.setAppId(set.getString("appid"));
                    cfg.setToken(set.getString("token"));
                    cfg.setResponsavelPadrao(new UsuarioVO());
                    cfg.getResponsavelPadrao().setCodigo(set.getInt("responsavelpadrao"));
                }
            }
        } catch (Exception e) {
            Logger.getLogger(con.getMetaData().getURL());
            Uteis.logar(e, Empresa.class);
        }
        return cfg;
    }

    public JSONObject consultarParaMapa(Integer codigo) throws Exception{
        try (ResultSet resultSet = criarConsulta("SELECT nome, latitude, longitude from empresa where codigo = " + codigo, con)) {
            if (resultSet.next()) {
                JSONObject empresa = new JSONObject();
                empresa.put("nome", resultSet.getString("nome"));
                empresa.put("latitude", resultSet.getString("latitude"));
                empresa.put("longitude", resultSet.getString("longitude"));
                return empresa;
            }
        }
        return new JSONObject();
    }

    public void inserirConfigEstacionamento(EmpresaVO obj) throws Exception {
        executarConsulta("DELETE FROM empresaconfigestacionamento WHERE empresa = " + obj.getCodigo(), con);
        if (obj.isUtilizaSistemaEstacionamento() && obj.getConfigEstacionamento() != null) {
            try (PreparedStatement stm = con.prepareStatement("INSERT INTO empresaconfigestacionamento(\n" +
                    "             empresa, ftphost, ftpuser, ftppass, ftpport, enviavalor, nomearquivo, enviahorario, produtosAdicionar)\n" +
                    "    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)")) {

                int i = 1;
                stm.setInt(i++, obj.getCodigo());
                stm.setString(i++, obj.getConfigEstacionamento().getHost().trim());
                stm.setString(i++, obj.getConfigEstacionamento().getUser().trim());
                stm.setString(i++, obj.getConfigEstacionamento().getPass().trim());
                stm.setInt(i++, obj.getConfigEstacionamento().getPort());
                stm.setBoolean(i++, obj.getConfigEstacionamento().isEnviaValor());
                stm.setString(i++, obj.getConfigEstacionamento().getNomeArquivo());
                stm.setBoolean(i++, obj.getConfigEstacionamento().isEnviaHorario());
                stm.setString(i++, obj.getConfigEstacionamento().obterProdutosAdicionar());
                stm.execute();
            }
        }
    }

    public static EmpresaConfigEstacionamentoVO montarDadosConfigEstacionamento(Integer codigoEmpresa, Connection con) {
        EmpresaConfigEstacionamentoVO ece = new EmpresaConfigEstacionamentoVO();
        try {
            Produto produtoDAO = new Produto(con);
            try (ResultSet set = criarConsulta("SELECT * FROM empresaconfigestacionamento where empresa = " + codigoEmpresa, con)) {
                if (set.next()) {
                    ece.setNovoObj(false);
                    ece.setHost(set.getString("ftphost"));
                    ece.setPort(set.getInt("ftpport"));
                    ece.setUser(set.getString("ftpUser"));
                    ece.setPass(set.getString("ftpPass"));
                    ece.setEnviaValor(set.getBoolean("enviavalor"));
                    ece.setNomeArquivo(set.getString("nomearquivo"));
                    ece.setEnviaHorario(set.getBoolean("enviahorario"));
                    ece.setEnviaTelefoneEmail(set.getBoolean("enviaTelefoneEmail"));
                    ece.setProdutosAdicionar(set.getString("produtosAdicionar"));
                    if (!ece.getProdutosAdicionar().isEmpty()) {
                        String[] codigosProduto = ece.getProdutosAdicionar().split(",");
                        for (String codProduto : codigosProduto) {
                            try {
                                Integer codigo = Integer.parseInt(codProduto);
                                ProdutoVO produto = produtoDAO.consultarPorCodigo(codigo, Uteis.NIVELMONTARDADOS_MINIMOS);
                                ece.getProdutosVOAdicionar().add(produto);
                            } catch (Exception ignored) {
                            }
                        }
                    }
                }
            }
            produtoDAO = null;
        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }
        return ece;
    }

    public JSONObject obterInfoRedeDCC(String urlOamd, String chave) throws Exception{
        String s = ExecuteRequestHttpService.executeRequestGET(urlOamd + "/prest/empresaFinanceiro/consultarInfosRedeDCC?chave=" + chave,
                new HashMap<>());
        return new JSONObject(s).getJSONObject("return");
    }

    @Override
    public JSONArray obterInfoClima(String urlOamd, String chave, Integer codigoEmpresa, long data) throws Exception{
        String s = ExecuteRequestHttpService.executeRequestGET(urlOamd + "/prest/empresa/consultarInfosClima?chave="+chave+"&codigoEmpresa="+codigoEmpresa+"&dataTime="+data,
                new HashMap<>());
        return new JSONObject(s).getJSONArray("return");
    }

    @Override
    public void alteraPontosChuva(Integer codigo, Integer pontos) throws Exception {
        alterar(getIdEntidade());
        String sql = "update empresa set pontosalunoacessochuva = ? where codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, pontos);
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }
    }

    @Override
    public void alteraPontosFrio(Integer codigo, Integer pontos) throws Exception {
        alterar(getIdEntidade());
        String sql = "update empresa set pontosalunoacessofrio = ? where codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, pontos);
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }
    }

    @Override
    public void alteraPontosCalor(Integer codigo, Integer pontos) throws Exception {
        alterar(getIdEntidade());
        String sql = "update empresa set pontosalunoacessocalor = ? where codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, pontos);
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }
    }

    @Override
    public Date concederDiaExtra(Integer codigoEmpresa) throws Exception {
        if (codigoEmpresa == null || codigoEmpresa == 0) {
            throw new Exception("O código da empresa não foi informado. Empresa: " + codigoEmpresa);
        }

        final String sqlConsultarRegistroNoMes = "SELECT concessao_dia_extra FROM empresa WHERE codigo = ? ";
        boolean utilizadoNesteMes;
        try (PreparedStatement stmConsulta = con.prepareStatement(sqlConsultarRegistroNoMes)) {
            stmConsulta.setInt(1, codigoEmpresa);

            utilizadoNesteMes = false;
            try {
                try (ResultSet rs = stmConsulta.executeQuery()) {
                    if (rs.next() && rs.getDate(1) != null) {
                        utilizadoNesteMes = Uteis.getMesData(rs.getDate(1)) == Uteis.getMesData(Calendario.hoje());
                    }
                }
            } catch (Exception e) {
                Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "Erro ao tentar recuperar a data extra para utilização do sistema.", e);
            }
        }

        if (utilizadoNesteMes) {
            throw new Exception("Não é possível liberar mais um dia no sistema este mês.");
        }

        final Date dataConcedida = new Date();
        final String sqlUpdate = "UPDATE empresa SET concessao_dia_extra = ? WHERE codigo = ? ";
        try (PreparedStatement stmUpdate = con.prepareStatement(sqlUpdate)) {
            stmUpdate.setDate(1, Uteis.getDataJDBC(dataConcedida));
            stmUpdate.setInt(2, codigoEmpresa);

            stmUpdate.executeUpdate();
        }

        return dataConcedida;
    }

    @Override
    public Date concederDiasExtras(Integer codigoEmpresa, int dias) throws Exception {
        if (codigoEmpresa == null || codigoEmpresa == 0) {
            throw new Exception("O código da empresa não foi informado. Empresa: " + codigoEmpresa);
        }

        final Date dataConcedida = Uteis.somarDias(new Date(), dias);
        final String sqlUpdate = "UPDATE empresa SET concessao_dia_extra = ?, total_dias_solicitados = (total_dias_solicitados + " + dias + ") WHERE codigo = ?; " +
                "UPDATE empresa SET datasuspensao = ?  WHERE codigo = ? and total_dias_solicitados >= "+SuperControle.MAXIMO_DIAS_EXTRA_CONCEDIDOS+";";
        try (PreparedStatement stmUpdate = con.prepareStatement(sqlUpdate)) {
            stmUpdate.setDate(1, Uteis.getDataJDBC(dataConcedida));
            stmUpdate.setInt(2, codigoEmpresa);
            stmUpdate.setDate(3, Uteis.getDataJDBC(dataConcedida));
            stmUpdate.setInt(4, codigoEmpresa);


            stmUpdate.executeUpdate();
        }

        return dataConcedida;
    }

    @Override
    public int consultarTotalDiasConcedidos(Integer codigoEmpresa) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select total_dias_solicitados from empresa where codigo =  ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoEmpresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next())
                    return tabelaResultado.getInt("total_dias_solicitados");
            }
        }
        return 0;
    }

    public void debitarCreditoDCCRede(String urlOamd, String chave, Integer quantidadeSubtrair, Integer codigoEmpresa) throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("chave", chave);
        map.put("debito", quantidadeSubtrair.toString());
        map.put("codigoEmpresa", codigoEmpresa.toString());
        String s = ExecuteRequestHttpService.executeRequest(
                urlOamd + "/prest/empresaFinanceiro/debitarDCCRede",
                map);
        JSONObject json = new JSONObject(s);
        if(!UteisValidacao.emptyString(json.optString("erro"))){
            throw new Exception(json.optString("erro"));
        }
    }

    public void addCreditoDCCRede(String urlOamd, String chave) throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("chave", chave);
        String s = ExecuteRequestHttpService.executeRequest(
                urlOamd + "/prest/empresaFinanceiro/addDCCRede",
                map);
        JSONObject json = new JSONObject(s);
        if(!UteisValidacao.emptyString(json.optString("erro"))){
            throw new Exception(json.optString("erro"));
        }
    }

    public void alterarConfiguracoesPactoPay(EmpresaVO empresaVO) throws Exception {
        String sql = "UPDATE empresa SET habilitarReenvioAutomaticoRemessa = ?, qtdExecucoesRetentativa = ?, " +
                "qtdDiasLimiteCobrancaParcelasRecorrencia = ?, qtdDiasRepetirCobrancaParcelasRecorrencia = ? WHERE codigo = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setBoolean(++i, empresaVO.isHabilitarReenvioAutomaticoRemessa());
            stm.setInt(++i, empresaVO.getQtdExecucoesRetentativa());
            stm.setInt(++i, empresaVO.getQtdDiasLimiteCobrancaParcelasRecorrencia());
            stm.setInt(++i, empresaVO.getQtdDiasRepetirCobrancaParcelasRecorrencia());
            stm.setInt(++i, empresaVO.getCodigo());
            stm.execute();
        }
    }

    public void alterarConfiguracoesReenvioAutomaticoRemessa(boolean habilitarReenvioAutomaticoRemessa, Integer qtdExecucoesRetentativa, Integer empresa) throws Exception {
        String sql = "UPDATE empresa SET habilitarReenvioAutomaticoRemessa = ?, qtdExecucoesRetentativa = ? WHERE codigo = ?";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setBoolean(1, habilitarReenvioAutomaticoRemessa);
            stm.setInt(2, qtdExecucoesRetentativa);
            stm.setInt(3, empresa);
            stm.execute();
        }
    }

    @Override
    public void alteraPontosAcesso(Integer codigo, Integer pontos) throws Exception {
        alterar(getIdEntidade());
        String sql = "update empresa set pontosalunoacesso = ? where codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, pontos);
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }
    }

    public void alteraDiasPontuacaoAtiva(Integer codigo, List<String> diasAtivosArray) throws Exception {
        alterar(getIdEntidade());

        String diasAtivosStr = String.join(",", diasAtivosArray);

        String sql = "update empresa set diasativospontuacaoacesso = ? where codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, diasAtivosStr);
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }
    }

    private void gravarParceiroFidelidade(EmpresaVO empresaVO) throws Exception {
        if (empresaVO.isUsarParceiroFidelidade() && empresaVO.getParceiroFidelidade() != null){
            ParceiroFidelidadeVO.validarDados(empresaVO.getParceiroFidelidade());
            if (!UteisValidacao.emptyList(empresaVO.getParceiroFidelidade().getItens())){
                ParceiroFidelidade parceiroFidelidadeDAO = new ParceiroFidelidade(con);
                if (UteisValidacao.emptyNumber(empresaVO.getParceiroFidelidade().getCodigo())) {
                    parceiroFidelidadeDAO.incluir(empresaVO.getParceiroFidelidade());
                } else {
                    parceiroFidelidadeDAO.alterar(empresaVO.getParceiroFidelidade());
                }
            }
        }
    }

    public EmpresaVO consultarPorIdExterno(String idexterno, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT Empresa.* FROM Empresa WHERE idexterno = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, idexterno);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new EmpresaVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public void alterarConfiguracoesClubeVantagens(EmpresaVO empresaEnviada) throws Exception {
        String sql = "update empresa set apenasPrimeiroAcessoClubeVantagens = ? , minCreditarProximoPontoClubeVantagens = ? , zerarPontosAposVencimento=?, pontuarApenasCampanhasAtivas=?, " +
                " aplicarIndicacaoQlqrPlano=?  " + (empresaEnviada.getCodigo() == 0 ? "" : "where codigo = ? ");
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setBoolean(1, empresaEnviada.isApenasPrimeiroAcessoClubeVantagens());
            sqlAlterar.setInt(2, empresaEnviada.getMinutosCreditarProximoPontoClubeVantagens());
            sqlAlterar.setInt(3, empresaEnviada.getZerarPontosAposVencimento().getCodigo());
            sqlAlterar.setBoolean(4, empresaEnviada.isPontuarApenasCategoriasEmCampanhasAtivas());
            sqlAlterar.setBoolean(5, empresaEnviada.isAplicarIndicacaoQlqrPlano());
            if (empresaEnviada.getCodigo() != 0)
                sqlAlterar.setInt(6, empresaEnviada.getCodigo());
            sqlAlterar.execute();
        }
    }

    @Override
    public List<Integer> consultarTodosCodigos() throws Exception {
        String sql = "SELECT codigo FROM empresa";
        List<Integer> codigos = new ArrayList<>();
        ResultSet rs = con.prepareStatement(sql).executeQuery();
        while(rs.next()){
            codigos.add(rs.getInt("codigo"));
        }
        return codigos;
    }

    public void alterarNomeEmpresa(String nome, String razaoSocial, Integer codigo) throws  Exception{
        String sql = "UPDATE empresa SET nome = ?, razaoSocial = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, nome);
        ps.setString(2, razaoSocial);
        ps.setInt(3, codigo);
        ps.executeUpdate();
    }

    @Override
    public boolean isIntegracaoSpivi(int codigo) throws Exception {
        boolean habilitado = false;
        ResultSet rs = criarConsulta("SELECT integracaoSpiviHabilitada from empresa where codigo = " + codigo, con);
        if(rs.next()){
            habilitado = rs.getBoolean("integracaoSpiviHabilitada");
        }

        return habilitado;
    }

    public void alterarSomenteAtualizarDadosCadastroSemCommit(EmpresaVO obj) throws SQLException {
        String sql = "UPDATE public.Empresa set atualizarDadosCadastro = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 1;
            sqlAlterar.setBoolean(i++, obj.isAtualizarDadosCadastro());
            sqlAlterar.setInt(i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public boolean atualizarEmpresaFinanceiroOAMD(String chave, Integer empresa, String classificacao, String gestor, String financeiro) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("chave", chave);
            map.put("empresa", empresa.toString());
            map.put("classificacao", classificacao);
            map.put("gestor", gestor);
            map.put("financeiro", financeiro);
            String urlOamd = PropsService.getPropertyValue(chave, PropsService.urlOamd);
            String s = ExecuteRequestHttpService.executeRequest(urlOamd + "/prest/empresaFinanceiro/atualizarDadosEmpresa", map);
            JSONObject json = new JSONObject(s);
            if (!UteisValidacao.emptyString(json.optString("return"))) {
                String msg = json.optString("return");
                return msg.equalsIgnoreCase("SUCESSO");
            } else if (!UteisValidacao.emptyString(json.optString("erro"))) {
                throw new Exception(json.optString("erro"));
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }

    public JSONArray todas(String chave) throws Exception {
        JSONArray empresas = new JSONArray();
        try (ResultSet rs = criarConsulta("select e.codigo, e.nome, e.codigorede, e.cod_empresafinanceiro, e.ativa, est.sigla as siglaEstado, p.nome as pais from empresa e " +
                " left join estado est on est.codigo = e.estado " +
                " left join pais p on p.codigo = e.pais " +
                " where ativa", con)) {
            while (rs.next()) {
                JSONObject empresa = new JSONObject();
                empresa.put("codigo", rs.getInt("codigo"));
                empresa.put("nome", rs.getString("nome"));
                empresa.put("codigoEmpresaRede", rs.getString("codigorede"));
                empresa.put("codigoFinanceiro", rs.getInt("cod_empresafinanceiro"));
                empresa.put("ativa", rs.getBoolean("ativa"));
                try {
                    String fotokey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA, rs.getString("codigo"));
                    empresa.put("fotoKey", Uteis.getPaintFotoDaNuvem(fotokey));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                try {
                    empresa.put("siglaEstado", rs.getString("siglaEstado"));
                    empresa.put("pais", rs.getString("pais"));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                empresas.put(empresa);
            }
        }
        return empresas;
    }

    @Override
    public void alterarDataSincronizacaoFinanceiro(EmpresaVO obj) throws Exception {
        String sql = "UPDATE empresa SET sincronizacaofinanceiro = ?, cod_empresafinanceiro = ? WHERE codigo = " + obj.getCodigo();
        try (PreparedStatement stm = con.prepareStatement(sql)) {

            if (obj.getSincronizacaoFinanceiro() != null) {
                stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getSincronizacaoFinanceiro()));
            } else {
                stm.setNull(1, Types.TIMESTAMP);
            }

            stm.setInt(2, obj.getCodEmpresaFinanceiro());
            stm.execute();
        }
    }


    public boolean isAcessoSomenteComAgendamento(Integer empresa) {
        try {
            String sqlStr = "SELECT acessoSomenteComAgendamento FROM empresa where codigo = " + empresa;
            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sqlStr);
            rs.next();
            return rs.getBoolean("acessoSomenteComAgendamento");
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public void alterarAcessoSomenteComAgendamentoTodasEmpresas(boolean acessoSomenteComAgendamento) throws Exception {
        for (EmpresaVO empresaVO : consultarTodas(null, Uteis.NIVELMONTARDADOS_MINIMOS)) {
            String sql = "update empresa set acessoSomenteComAgendamento = ? where codigo = ?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setBoolean(1, acessoSomenteComAgendamento);
            sqlAlterar.setInt(2, empresaVO.getCodigo());
            sqlAlterar.execute();
            if (MemCachedManager.getInstance().getMemcachedOn()) {
                MemCachedManager.getInstance().remover(EmpresaVO.class, empresaVO.getCodigo().toString());
            }
        }
    }

    public Integer obterCapacidade(Integer codEmpresa) throws SQLException {
        String sql = "SELECT capacidadeSimultanea FROM empresa WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codEmpresa);
            try (ResultSet ignored = sqlAlterar.executeQuery()) {
                if (ignored.next()) {
                    return ignored.getInt("capacidadeSimultanea");
                }
            }
        }
        return 0;
    }

    public Boolean integracaoMyWellnesHabilitada(Integer empresa , boolean validarEnviarColaborador) throws Exception {
        String sqlStr = "SELECT integracaoMyWellneHabilitada, integracaoMyWellnessEnviarVinculos FROM Empresa  WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                if(validarEnviarColaborador) {
                    return tabelaResultado.getBoolean(1) && tabelaResultado.getBoolean(2);
                } else {
                    return tabelaResultado.getBoolean(1);
                }
            }
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean isBloquearAcessoSemAssinaturaDigital(Integer empresa) {
        String sqlStr = "SELECT bloquearAcessoSemAssinaturaDigital FROM Empresa  WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getBoolean("bloquearAcessoSemAssinaturaDigital");
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean isHabilitarCobrancaAutomaticaNaVenda(Integer empresa) {
        String sqlStr = "SELECT habilitarCobrancaAutomaticaNaVenda FROM Empresa  WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getBoolean("habilitarCobrancaAutomaticaNaVenda");
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean isBloquearAcessoAlunoParQNaoAssinado(Integer empresa) {
        String sqlStr = "SELECT bloquearacessoalunoparqnaoassinado FROM Empresa  WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getBoolean("bloquearacessoalunoparqnaoassinado");
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean bloquearAcessoSemTermoResponsabilidade(Integer empresa) {
        String sqlStr = "SELECT bloquearAcessoSemTermoResponsabilidade FROM Empresa  WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getBoolean("bloquearAcessoSemTermoResponsabilidade");
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean isBloquearAcessoCrefVencido(Integer empresa) {
        String sqlStr = "SELECT bloquearAcessoCrefVencido FROM Empresa  WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getBoolean("bloquearAcessoCrefVencido");
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public Integer obterEmpresaClientePessoa(Integer cliente, Integer pessoa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT empresa FROM cliente WHERE 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(cliente)) {
            sql.append("AND codigo = ").append(cliente).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("AND pessoa = ").append(pessoa).append(" \n");
        }
        sql.append("limit 1 \n");
        try (ResultSet rs = con.prepareStatement(sql.toString()).executeQuery()) {
            return rs.next() ? rs.getInt("empresa") : null;
        }
    }

    public Integer obterEmpresaColaborador(Integer colaborador) throws Exception {
        String sql = "SELECT empresa FROM colaborador WHERE codigo = " + colaborador;
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() ? rs.getInt("empresa") : null;
        }
    }

    public boolean integracaoMentorWebHabilitada(Integer codigoEmpresa) throws Exception {
        boolean habilitado = false;
        ResultSet rs = criarConsulta("SELECT integracaoMentorWebHabilitada FROM empresa WHERE codigo = " + codigoEmpresa, con);
        if(rs.next()) {
            habilitado = rs.getBoolean("integracaoMentorWebHabilitada");
        }
        return habilitado;
    }

    public boolean integracaoAmigoFitHabilitada(Integer codigoEmpresa) throws Exception {
        boolean habilitado = false;
        ResultSet rs = criarConsulta("SELECT integracaoAmigoFitHabilitada FROM empresa WHERE codigo = " + codigoEmpresa, con);
        if(rs.next()) {
            habilitado = rs.getBoolean("integracaoAmigoFitHabilitada");
        }
        return habilitado;
    }

    public String obterUrlWebhookNotificar(Integer empresa) throws Exception {
        String sql = "SELECT urlWebhookNotificar,notificarwebhook FROM empresa WHERE codigo = " + empresa;
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            if (rs.next()) {
                if (rs.getBoolean("notificarwebhook")) {
                    return rs.getString("urlWebhookNotificar");
                } else {
                    return null;
                }
            } else {
                return null;
            }
        }
    }

    public boolean isNotificarWebhook(Integer empresa) {
        String sqlStr = "SELECT notificarwebhook FROM Empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public Integer consultarCarenciaRenovacaoPrimeiraEmpresaAtiva() throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT carenciarenovacao FROM empresa WHERE ativa ORDER BY codigo LIMIT 1";
        try (ResultSet rs = con.prepareStatement(sql).executeQuery()) {
            return rs.next() ? rs.getInt("carenciarenovacao") : 0;
        }
    }

    public Integer obterConvenioCobrancaPix(Integer codigoEmpresa) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("e.conveniocobrancapix \n");
            sql.append("from empresa e \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = e.conveniocobrancapix \n");
            sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
            sql.append("and e.codigo = ").append(codigoEmpresa);
            ResultSet rs = criarConsulta(sql.toString(), con);
            if (rs.next()) {
                return rs.getInt("conveniocobrancapix");
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public Integer obterConvenioCobrancaBoleto(Integer codigoEmpresa) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("e.conveniocobrancaboleto \n");
            sql.append("from empresa e \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = e.conveniocobrancaboleto \n");
            sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
            sql.append("and e.codigo = ").append(codigoEmpresa);
            ResultSet rs = criarConsulta(sql.toString(), con);
            if (rs.next()) {
                return rs.getInt("conveniocobrancaboleto");
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public Integer obterConvenioCobrancaCartao(Integer codigoEmpresa) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("e.ConvenioCobrancaCartao \n");
            sql.append("from empresa e \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = e.ConvenioCobrancaCartao \n");
            sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
            sql.append("and e.codigo = ").append(codigoEmpresa);
            ResultSet rs = criarConsulta(sql.toString(), con);
            if (rs.next()) {
                return rs.getInt("ConvenioCobrancaCartao");
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public Integer obterConvenioCobrancaCartaoRegua(Integer codigoEmpresa) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("e.ConvenioCobrancaCartaoRegua \n");
            sql.append("from empresa e \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = e.ConvenioCobrancaCartaoRegua \n");
            sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
            sql.append("and e.codigo = ").append(codigoEmpresa);
            ResultSet rs = criarConsulta(sql.toString(), con);
            if (rs.next()) {
                return rs.getInt("ConvenioCobrancaCartaoRegua");
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public Integer obterConvenioCobrancaPixRegua(Integer codigoEmpresa) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("e.conveniocobrancapixregua \n");
            sql.append("from empresa e \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = e.conveniocobrancapixregua \n");
            sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
            sql.append("and e.codigo = ").append(codigoEmpresa);
            ResultSet rs = criarConsulta(sql.toString(), con);
            if (rs.next()) {
                return rs.getInt("conveniocobrancapixregua");
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public Integer obterConvenioCobrancaBoletoRegua(Integer codigoEmpresa) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("e.conveniocobrancaboletoregua \n");
            sql.append("from empresa e \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = e.conveniocobrancaboletoregua \n");
            sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
            sql.append("and e.codigo = ").append(codigoEmpresa);
            ResultSet rs = criarConsulta(sql.toString(), con);
            if (rs.next()) {
                return rs.getInt("conveniocobrancaboletoregua");
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public void atualizarTokenMqvEmpresa(String codigoEmpresa, String token) throws Exception {
        String sql = "UPDATE empresa SET tokenmqv = '" + token + "' WHERE codigo = " + codigoEmpresa;
        executarConsulta(sql, con);
    }

    public boolean isGerarBoletoCaixaAberto(Integer empresa) {
        String sqlStr = "SELECT gerarBoletoCaixaAberto FROM Empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isCobrarAutomaticamenteMultaJuros(Integer empresa) {
        String sqlStr = "SELECT CobrarAutomaticamenteMultaJuros FROM Empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isUtilizarNomeResponsavelNoBoleto(Integer empresa) {
        String sqlStr = "SELECT UtilizarNomeResponsavelNoBoleto FROM Empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isUtilizarNomeResponsavelNoBoletoMaiorIdade(Integer empresa) {
        String sqlStr = "SELECT utilizarNomeResponsavelNoBoletoMaiorIdade FROM Empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isUtilizarNomeResponsavelNoPixMenorIdade(Integer empresa) {
        String sqlStr = "SELECT UtilizarNomeResponsavelNoPixMenorIdade FROM Empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isUtilizarNomeResponsavelNoPixMaiorIdade(Integer empresa) {
        String sqlStr = "SELECT UtilizarNomeResponsavelNoPixMaiorIdade FROM Empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public RedeEmpresaVO consultarRedeEmpresaOAMD(String key) {
        try {
            String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/empresaFinanceiro/consultarRede?key=" + key;
            String response = ExecuteRequestHttpService.executeRequestGet(url, new HashMap<>());
            JSONObject resposta = new JSONObject(response).optJSONObject("return");
            if (resposta == null) {
                return null;
            }
            return JSONMapper.getObject(resposta, RedeEmpresaVO.class);
        } catch (Exception ex) {
            Uteis.logar(ex, LoginControle.class);
        }
        return null;
    }

    public CustomerSuccessTO consultarResponsavelPacto(String key, Integer empresa) {
        try {
            String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/empresaFinanceiro/consultarResponsavelPacto?key=" + key +
                    "&codigoEmpresaZw=" + empresa;
            String response = ExecuteRequestHttpService.executeRequestGet(url, new HashMap<>());
            JSONObject resposta = new JSONObject(response).optJSONObject("return");
            if (resposta == null) {
                return null;
            }
            return JSONMapper.getObject(resposta, CustomerSuccessTO.class);
        } catch (Exception ex) {
            Uteis.logar(ex, LoginControle.class);
        }
        return null;
    }

    public boolean verificaSeUtilizaVitio(Integer empresa) {
        String sqlStr = "SELECT usavitio FROM empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public String consultarLinkVitio(Integer empresa) {
        String sqlStr = "SELECT linkcheckoutvitio FROM empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getString(1);
            }
            return "";
        } catch (Exception e) {
            return "";
        }
    }

    public String consultarLinkEbookVitio (Integer empresa) {
        String sqlStr = "SELECT linkebook FROM empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getString(1);
            }
            return "";
        } catch (Exception e) {
            return "";
        }
    }

    public String mensagemWpp(Boolean aderiu, Integer empresa) {
        if(aderiu) {
            String sqlStr = "SELECT mensagemvitioquercomprar FROM empresa WHERE codigo = " + empresa;
            try (Statement stm = con.createStatement();
                 ResultSet rs = stm.executeQuery(sqlStr)) {
                if (rs.next()) {
                    return rs.getString(1);
                }
                return "";
            } catch (Exception e) {
                return "";
            }
        } else {
            String sqlStr = "SELECT mensagemvitiowpp FROM empresa WHERE codigo = " + empresa;
            try (Statement stm = con.createStatement();
                 ResultSet rs = stm.executeQuery(sqlStr)) {
                if (rs.next()) {
                    return rs.getString(1);
                }
                return "";
            } catch (Exception e) {
                return "";
            }
        }
    }

    public JSONArray gymIds() throws Exception{
        JSONArray idsEmpresa = new JSONArray();
        ResultSet resultSet = criarConsulta("select nome, codigogympass, codigo from empresa", con);
        while(resultSet.next()){
            if(!UteisValidacao.emptyString(resultSet.getString("codigogympass"))){
                JSONObject gymId = new JSONObject();
                gymId.put("gymid", resultSet.getString("codigogympass"));
                gymId.put("codigoEmpresa", resultSet.getString("codigo"));
                gymId.put("nome", resultSet.getString("nomeunidade"));
                idsEmpresa.put(gymId);
            }
        }
        return idsEmpresa;
    }

    public Integer obterLimiteDeAcessosPorDiaGympass(Integer codEmpresa) throws SQLException {
        String sql = "SELECT limitedeacessospordiagympass FROM empresa WHERE codigo = ?";
        try (PreparedStatement pstm = con.prepareStatement(sql)) {
            pstm.setInt(1, codEmpresa);
            try (ResultSet rs = pstm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("limitedeacessospordiagympass");
                }
            }
        }
        return 0;
    }

    public Integer obterLimiteDeAulasPorDiaGympass(Integer codEmpresa) throws SQLException {
        String sql = "SELECT limitedeaulaspordiagympass FROM empresa WHERE codigo = ?";
        try (PreparedStatement pstm = con.prepareStatement(sql)) {
            pstm.setInt(1, codEmpresa);
            try (ResultSet rs = pstm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("limitedeaulaspordiagympass");
                }
            }
        }
        return 0;
    }

    public Integer obterCodEmpresaPorGymID(final String gymId) throws Exception {
        String sql = "select codigo from empresa where codigogympass = '" + gymId + "' and ativa ";
        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getInt(1);
            } else {
                return null;
            }
        }
    }

    public Integer obterCodEmpresaPorGoGoodTokenAcademy(final String tokenAcademy) throws Exception {
        String sql = "select codigo from empresa where tokenAcademyGoGood = '" + tokenAcademy + "' and ativa ";
        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getInt(1);
            } else {
                return null;
            }
        }
    }

    public String obterGymIDPorCodEmpresa(final Integer codEmpresa) throws Exception {
        String sql = "select codigogympass from empresa where codigo = " + codEmpresa;
        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getString(1);
            } else {
                return null;
            }
        }
    }

    public boolean temPlanoPacote(Integer empresa){
        try {
            return false;
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }

    }

    public void alterarRecursoSistemaUsuarios(TipoInfoMigracaoEnum tipoInfoMigracaoEnum,
                                              boolean ativar, Integer codEmpresa,
                                              UsuarioVO usuarioVO, boolean alterarUsuariosExistentes,
                                              Connection con) throws Exception {
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);

            //adicionar o recurso para quem não tem no infomigracao
            usuarioDAO.incluirInfoMigracaoParaUsuarioSem(ativar ? "true" : "false", codEmpresa, tipoInfoMigracaoEnum, usuarioVO);

            if (alterarUsuariosExistentes) {
                //alterar todos para de acordo com o solicitado
                StringBuilder sqlU = new StringBuilder();
                sqlU.append("update infomigracao set info = '").append(ativar).append("' where tipoinfo = ").append(tipoInfoMigracaoEnum.getId());
                if (!UteisValidacao.emptyNumber(codEmpresa)) {
                    sqlU.append("and usuario in (select p.usuario from usuarioperfilacesso p where p.usuario = infomigracao.usuario and p.empresa = ").append(codEmpresa).append(") ");
                }
                SuperFacadeJDBC.executarUpdate(sqlU.toString(), con);
                gerarLogRecursoInfoMigracao(tipoInfoMigracaoEnum, codEmpresa, "", String.valueOf(ativar), usuarioVO, false);
            }
        } finally {
            usuarioDAO = null;
        }
    }

    private void gerarLogRecursoInfoMigracao(TipoInfoMigracaoEnum tipoInfoMigracaoEnum, Integer codEmpresa,
                                             String valorAnterior, String valorNovo, UsuarioVO usuarioVO,
                                             boolean novo) {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO obj = new LogVO();
            obj.setChavePrimaria(codEmpresa.toString());
            obj.setNomeEntidade("INFOMIGRACAO");
            obj.setNomeEntidadeDescricao("INFOMIGRACAO");
            obj.setOperacao((novo ? "INCLUIR" : "ALTERAR") +  " INFOMIGRACAO USUARIOS DA EMPRESA - Empresa: " + codEmpresa + " | TipoInfoMigracao: " + (tipoInfoMigracaoEnum != null ? (tipoInfoMigracaoEnum.getId() + " - " + tipoInfoMigracaoEnum.name()) : ""));
            obj.setPessoa(0);
            obj.setDataAlteracao(Calendario.hoje());
            try {
                if (usuarioVO != null) {
                    obj.setUsuarioVO(usuarioVO);
                    obj.setResponsavelAlteracao(usuarioVO.getNome());
                    obj.setUserOAMD(usuarioVO.getUserOamd());
                }
            } catch (Exception ex) {
                obj.setResponsavelAlteracao("");
            }
            obj.setNomeCampo("INFOMIGRACAO_INFO");
            obj.setValorCampoAnterior(valorAnterior);
            obj.setValorCampoAlterado(valorNovo);
            logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            logDAO = null;
        }
    }

    public void gerarLogAlterarRecursoEmpresa(Integer empresa, Integer usuarioOAMDCodigo, String usuarioOAMDUsername,
                                               String valorAnterior, String valorAtual, Connection con, String campo) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("EMPRESA");
            log.setNomeEntidadeDescricao("EMPRESA");
            log.setDescricao("ALTERAÇÃO - " + campo.toUpperCase());
            log.setChavePrimaria(empresa.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setResponsavelAlteracao("Usuário OAMD: " + usuarioOAMDCodigo + " - " + usuarioOAMDUsername);
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo(campo);
            log.setUserOAMD(usuarioOAMDUsername);

            if (UteisValidacao.emptyString(valorAnterior)) {
                log.setValorCampoAnterior("");
            } else {
                log.setValorCampoAnterior(valorAnterior);
            }

            if (UteisValidacao.emptyString(valorAtual)) {
                log.setValorCampoAlterado("");
            } else {
                log.setValorCampoAlterado(valorAtual);
            }

            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            Uteis.logar(null, "Erro ao gerarLogAlterarRecursoEmpresa " + ex.getMessage());
            ex.printStackTrace();
            throw ex;
        }
    }

    public void processarAutomaticoInfoMigracaoUsuario(Integer codEmpresa, boolean controlarTransacao) throws Exception {
        Usuario usuarioDAO;
        try {
            if (controlarTransacao) {
                con.setAutoCommit(false);
            }

            usuarioDAO = new Usuario(con);
            UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();

            StringBuilder sqlEmpresa = new StringBuilder();
            sqlEmpresa.append("select \n");
            sqlEmpresa.append("codigo, \n");
            sqlEmpresa.append("nome, \n");
            sqlEmpresa.append("tiposInfoMigracaoPadrao, \n");
            sqlEmpresa.append("tiposInfoMigracaoPadraoAutomatico \n");
            sqlEmpresa.append("from empresa \n");
            sqlEmpresa.append("where ativa \n");
            sqlEmpresa.append("and length(coalesce(tiposInfoMigracaoPadraoAutomatico,'')) > 0 \n");
            if (!UteisValidacao.emptyNumber(codEmpresa)) {
                sqlEmpresa.append("and codigo = ").append(codEmpresa).append(" \n");
            }
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmpresa.toString(), con);
            while (rs.next()) {
                Integer codigo = rs.getInt("codigo");
                String nome = rs.getString("nome");
                String tiposInfoMigracaoPadrao = rs.getString("tiposInfoMigracaoPadrao");
                String tiposInfoMigracaoPadraoAutomatico = rs.getString("tiposInfoMigracaoPadraoAutomatico");

                Set<TipoInfoMigracaoEnum> tiposEmpresa = new HashSet<>();
                if (!UteisValidacao.emptyString(tiposInfoMigracaoPadrao)) {
                    for (String tipo : tiposInfoMigracaoPadrao.split(",")) {
                        if (!UteisValidacao.emptyString(tipo)) {
                            TipoInfoMigracaoEnum tipoInfoMigracaoEnum = TipoInfoMigracaoEnum.valueOf(tipo);
                            tiposEmpresa.add(tipoInfoMigracaoEnum);
                        }
                    }
                }

                Uteis.logarDebug("processarAutomaticoInfoMigracao | Empresa: " + codigo + " | " + nome + " | " + tiposInfoMigracaoPadraoAutomatico);
                if (!UteisValidacao.emptyString(tiposInfoMigracaoPadraoAutomatico)) {
                    for (String tipo : tiposInfoMigracaoPadraoAutomatico.split(",")) {
                        if (!UteisValidacao.emptyString(tipo)) {
                            TipoInfoMigracaoEnum tipoInfoMigracaoEnum = TipoInfoMigracaoEnum.valueOf(tipo);
                            Uteis.logarDebug("processarAutomaticoInfoMigracao | Empresa: " + codigo + " | " + nome + " | " + tipoInfoMigracaoEnum.name());
                            if (tiposEmpresa.contains(tipoInfoMigracaoEnum)) {
                                //se já existe nas configurações da empresa então não precisa alterar
                                Uteis.logarDebug("processarAutomaticoInfoMigracao | Empresa: " + codigo + " | " + nome + " | " + tipoInfoMigracaoEnum.name() + " | Já existe na empresa");
                            } else {
                                alterarRecursoSistemaUsuarios(tipoInfoMigracaoEnum, true, codigo, usuarioVO, true, con);
                            }
                        }
                    }
                }
            }

            if (controlarTransacao) {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (controlarTransacao) {
                con.rollback();
            }
            Uteis.logarDebug(ex.getMessage());
            throw ex;
        } finally {
            if (controlarTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    public void atualizarTokensRdStationApiMarketing(int codigoEmpresa, String accessToken, String refreshToken) throws Exception {
        String sql = "UPDATE configuracaoempresardstation SET " +
                "accesstokenrdstationmarketing = ?, " +
                "refreshtokenrdstationmarketing = ? " +
                "WHERE empresa = ?";

        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setString(1, accessToken != null ? accessToken.trim() : null);
            stm.setString(2, refreshToken != null ? refreshToken.trim() : null);
            stm.setInt(3, codigoEmpresa);
            stm.execute();
        } catch (SQLException e) {
            System.err.println("Erro ao atualizar tokens RD Station para a empresa " + codigoEmpresa);
            e.printStackTrace();
            throw new Exception("Erro ao atualizar tokens RD Station para a empresa " + codigoEmpresa, e);
        }
    }

    public Boolean configsRdStationMarketingEstaoHabilitadas(int codigoEmpresa) throws Exception {
        String sql = "SELECT codigo FROM configuracaoempresardstation \n" +
                "WHERE empresa = " + codigoEmpresa + " \n" +
                "AND empresaUsaRD IS TRUE \n" +
                "AND configAtualizarAlunoRdStationMarketing IS TRUE";
        return existe(sql, con);
    }

    public boolean empresaUsaPinpad(int codigoEmpresa) throws Exception {
        Date ultimos6Meses = Calendario.somarMeses(Calendario.hoje(), -6);
        StringBuilder sql = new StringBuilder();
        sql.append("select empresa_codigo from ( \n");
        sql.append("select  \n");
        sql.append("e.codigo as empresa_codigo, \n");
        sql.append("e.nome as empresa_nome, \n");
        sql.append("(select max(m.datapagamento) from movpagamento m where m.datapagamento::date > '"+Uteis.getDataFormatoBD(ultimos6Meses)+"' and m.empresa = e.codigo and length(coalesce(m.numerounicotransacao, '')) > 0 and  length(coalesce(m.respostaRequisicaoPinpad, '')) > 0) data_recebimento_cappta, \n");
        sql.append("(select max(p.dataregistro) from pinpadpedido p where p.pinpad = 3 and p.dataregistro::date > '"+Uteis.getDataFormatoBD(ultimos6Meses)+"' and p.empresa = e.codigo and coalesce(p.recibopagamento, 0) > 0) data_recebimento_stoneconect, \n");
        sql.append("(select max(p.dataregistro) from pinpadpedido p where p.pinpad = 4 and p.dataregistro::date > '"+Uteis.getDataFormatoBD(ultimos6Meses)+"' and p.empresa = e.codigo and coalesce(p.recibopagamento, 0) > 0) data_recebimento_getcard, \n");
        sql.append("exists( \n");
        sql.append("(select p.codigo from pinpad p where p.pinpad = 2 and (coalesce(p.empresa,0) = 0 or p.empresa = e.codigo))  \n");
        sql.append("union \n");
        sql.append("(select p.codigo from formapagamento p where length(coalesce(p.systemidgeoitd,'')) > 0) \n");
        sql.append(") as pinpad_Geoitd \n");
        sql.append("from empresa e  \n");
        sql.append("where e.ativa \n");
        sql.append(") as sql  \n");
        sql.append("where sql.empresa_codigo = ").append(codigoEmpresa).append(" \n");
        sql.append("and ( \n");
        sql.append("sql.data_recebimento_cappta is not null  \n");
        sql.append("or sql.data_recebimento_stoneconect  is not null \n");
        sql.append("or sql.data_recebimento_getcard  is not null \n");
        sql.append("or sql.pinpad_Geoitd) \n");
        return existe(sql.toString(), con);
    }

    @Override
    public ConfiguracaoIntegracaoFogueteVO consultarConfiguracaoIntegracaoFoguete(int codigoEmpresa) {
        ConfiguracaoIntegracaoFogueteVO cfg = new ConfiguracaoIntegracaoFogueteVO();
        try {
            try (ResultSet rs = criarConsulta("SELECT * FROM configuracaointegracaofoguete \n" +
                    " where empresa = " + codigoEmpresa, con)) {
                if (rs.next()) {
                    cfg.setHabilitada(rs.getBoolean("habilitada"));
                    cfg.setEmpresa(rs.getInt("empresa"));
                    cfg.setTokenApi(rs.getString("tokenapi"));
                    cfg.setProduto(rs.getInt("produto"));
                    cfg.setUrlApi(rs.getString("urlApi"));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }
        return cfg;
    }

    @Override
    public ConfiguracaoIntegracaoAcessoPratiqueVO consultarConfiguracaoIntegracaoAcessoPratique(int codigoEmpresa) {
        ConfiguracaoIntegracaoAcessoPratiqueVO cfg = new ConfiguracaoIntegracaoAcessoPratiqueVO();
        try {
            try (ResultSet rs = criarConsulta("SELECT * FROM configuracaointegracaoacessopratique \n" +
                    " where empresa = " + codigoEmpresa, con)) {
                if (rs.next()) {
                    cfg.setHabilitada(rs.getBoolean("habilitada"));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }
        return cfg;
    }

    public Boolean integracaoManyChatHabilitada(int empresa) {
        String sqlStr = "SELECT integracaoManyChatHabilitada FROM empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public String obterTokenApiIntegracaoManyChat(int empresa) {
        String sqlStr = "SELECT integracaoManyChatTokenApi FROM empresa WHERE codigo = " + empresa;
        try (Statement stm = con.createStatement();
             ResultSet rs = stm.executeQuery(sqlStr)) {
            if (rs.next()) {
                return rs.getString(1);
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    private String getKey() {
        EmpresaControle empresaControle = (EmpresaControle) context().getExternalContext().getSessionMap().get("EmpresaControle");
        return empresaControle.getKey();
    }

    @Override
    public void reiniciarServicosNovaEmpresaOuAtivaInativa(){
        reiniciarDiscovery();
        reiniciarLogin();
        reiniciarMarketingMS();
    }

    private void reiniciarDiscovery() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> params = new HashMap<>();
        RequestHttpService service = new RequestHttpService();

        try {
            String key = getKey();
            String urlBase = URL_RELOAD_DISCOVERY;

            // 1. Reiniciar apenas a chave específica
            String urlComKey = urlBase + "?key=" + key;
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlComKey, headers, params, "", MetodoHttpEnum.GET);
            JSONObject jsonResponse = new JSONObject(respostaHttpDTO.getResponse());
            if (jsonResponse.has("content")) {
                System.out.println("REINICIOU DISCOVERY POR KEY " + key + " " + jsonResponse.getString("content"));
            }

            // 2. Reiniciar mapa completo
            respostaHttpDTO = service.executeRequest(urlBase, headers, params, "", MetodoHttpEnum.GET);
            jsonResponse = new JSONObject(respostaHttpDTO.getResponse());
            if (jsonResponse.has("content")) {
                System.out.println("REINICIOU DISCOVERY " + jsonResponse.getString("content"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void reiniciarLogin() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> params = new HashMap<>();
        RequestHttpService service = new RequestHttpService();
        try {
            String key = getKey();
            String urlBase = URL_RELOAD_LOGIN;

            // 1. Reiniciar apenas a chave específica
            String urlComKey = urlBase + "?key=" + key;
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlComKey, headers, params, "", MetodoHttpEnum.GET);
            JSONObject jsonResponse = new JSONObject(respostaHttpDTO.getResponse());
            if (jsonResponse.has("return")){
                System.out.println("REINICIOU LOGIN POR KEY " + key + " " + jsonResponse.getString("return"));
            }

            // 2. Reiniciar mapa completo
            respostaHttpDTO = service.executeRequest(urlBase, headers, params, "", MetodoHttpEnum.GET);
            jsonResponse = new JSONObject(respostaHttpDTO.getResponse());
            if (jsonResponse.has("return")){
                System.out.println("REINICIOU LOGIN " + jsonResponse.getString("return"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void reiniciarMarketingMS() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> params = new HashMap<>();
        RequestHttpService service = new RequestHttpService();
        try {
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(URL_RELOAD_MARKETING, headers, params, "", MetodoHttpEnum.GET);
            JSONObject jsonResponse = new JSONObject(respostaHttpDTO.getResponse());
            if (jsonResponse.has("result")){
                System.out.println("REINICIOU MARKETING MS " + jsonResponse.getJSONArray("result").get(0));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
