package negocio.facade.jdbc.basico;

import java.sql.*;
import java.util.Collections;
import java.util.List;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.Formatador;

import java.util.ArrayList;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.interfaces.basico.FornecedorInterfaceFacade;

public class Fornecedor extends CEDao implements FornecedorInterfaceFacade {
    // essa variável é usada para evitar duplicidades para consultas que tragam
    // vários telefones, emails ou endereços.

    private int codigoAnterior;

    public Fornecedor() throws Exception {
        super();
        setIdEntidade("Servico");
    }

    public Fornecedor(Connection con) throws Exception {
        super(con);
        setIdEntidade("Servico");
    }

    @Override
    public List<FornecedorVO> consultarFornecedor(boolean somenteVigente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select *, pes.nome  as pessoaNome from fornecedor inner join pessoa pes on pes.codigo = fornecedor.pessoa ");
        if(somenteVigente){
            sql.append(" where dataValidade is not null and dataValidade > '"+Uteis.getDataFormatoBD(Calendario.hoje())+"'");
        }

        Declaracao dc = new Declaracao(sql.toString(), this.con);

        try (ResultSet tabelaResultado = dc.executeQuery()) {
            return (this.montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        }
    }

    public boolean existeCNPJNoBanco(Integer codigo, String cnpj, int idEmpresa) throws Exception {
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM fornecedor ");
        sqlStr.append("WHERE cnpj like '").append(cnpj).append("' ");
        sqlStr.append("AND (empresa is null or empresa <> ").append(idEmpresa).append(")");
        if(!UteisValidacao.emptyNumber(codigo)) {
            sqlStr.append(" AND codigo <> ").append(codigo);
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return tabelaResultado.next();
    }

    public void alterarSomenteChaveArquivo(FornecedorVO obj) throws SQLException {
        String sql = "UPDATE fornecedor SET documento = ?, documentoExtensao = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setString(++i, obj.getDocumento());
            sqlAlterar.setString(++i, obj.getDocumentoExtensao());
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    @Override
    public List<FornecedorVO> consultarTodosFornecedorPorEmpresa(int codigoEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT  *, pes.nome  as pessoaNome FROM fornecedor\n");
        sql.append(" INNER JOIN pessoa pes ON pes.codigo = fornecedor.pessoa\n");
        sql.append(" WHERE empresa = ").append(codigoEmpresa);
        Declaracao dc = new Declaracao(sql.toString(), this.con);
        try (ResultSet tabelaResultado = dc.executeQuery()) {
            return (this.montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        }
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        List<FornecedorVO> lista;
        try (ResultSet rs = getPS().executeQuery()) {
            lista = new ArrayList<FornecedorVO>();
            while (rs.next()) {
                FornecedorVO fornecedorVO = new FornecedorVO();
                String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("contato");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    fornecedorVO.setCodigo(rs.getInt("codigo"));
                    fornecedorVO.setPessoa(new PessoaVO());
                    fornecedorVO.getPessoa().setNome(rs.getString("nome"));
                    fornecedorVO.setContato(rs.getString("contato"));
                    fornecedorVO.getEmpresaVO().setNome(rs.getString("contato"));
                    lista.add(fornecedorVO);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Fornecedor")) {
            Ordenacao.ordenarLista(lista, "nome_Apresentar");
        } else if (campoOrdenacao.equals("Contato")) {
            Ordenacao.ordenarLista(lista, "contato");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
    @Override
    /**
     * @deprecated
     * Não utilizá-la devido o nivel de montarDados necessário
     * Por isso melhor usar montarDados(ResultSet dadosSQL,Uteis.nivelMontarDados)
     */
    public FornecedorVO montarDados(ResultSet dadosSQL) throws SQLException {
        return new FornecedorVO();
    }

    @Override
    public FornecedorVO obter(int codigo) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("select forn.codigo as codigo, ");
        sb.append("forn.cnpj as cnpj, ");
        sb.append("forn.inscricaoEstadual as inscricaoEstadual, ");
        sb.append("forn.inscricaoMunicipal as inscricaoMunicipal, ");
        sb.append("forn.cfdf as cfdf, ");
        sb.append("forn.descricaocnae, ");
        sb.append("forn.observacao, ");
        sb.append("forn.contato as contato, ");
        sb.append("pes.nome as descricao, ");
        sb.append("pes.codigo as pessoa, ");
        sb.append("tel.numero as telefone, ");
        sb.append("ende.endereco as endereco, ");
        sb.append("em.email as email, ");
        sb.append("forn.planoconta, ");
        sb.append("forn.centrocusto, ");
        sb.append("forn.nomeFantasia, forn.razaoSocial, forn.dataCadastro, forn.dataValidade," +
                " forn.cnae, forn.codigoFpas, forn.grauRiscoNr4, forn.grauRiscoInss, forn.sindicato," +
                " forn.nrTotalFuncionarios, forn.porteEmpresa, forn.documento, forn.documentoExtensao ");
        sb.append("from fornecedor forn, pessoa pes ");
        sb.append("left join telefone tel on tel.pessoa = pes.codigo ");
        sb.append("left join endereco ende on ende.pessoa = pes.codigo ");
        sb.append("left join email em on em.pessoa = pes.codigo ");
        sb.append("where forn.pessoa = pes.codigo ");
        sb.append("and forn.codigo = ? ");
        Declaracao dc = new Declaracao(sb.toString(), this.con);
        dc.setInt(1, codigo);
        codigoAnterior = 0;
        List<FornecedorVO> result;
        try (ResultSet tabelaResultado = dc.executeQuery()) {
            result = this.montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        }
        return result.isEmpty() ? null : result.get(0);
    }

    public FornecedorVO consultarPorPessoa(PessoaVO pessoaVO) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("select forn.codigo as codigo, ");
        sb.append("forn.cnpj as cnpj, ");
        sb.append("forn.inscricaoEstadual as inscricaoEstadual, ");
        sb.append("forn.inscricaoMunicipal as inscricaoMunicipal, ");
        sb.append("forn.cfdf as cfdf, ");
        sb.append("forn.observacao, ");
        sb.append("forn.contato as contato, ");
        sb.append("pes.nome as descricao, ");
        sb.append("pes.codigo as pessoa, ");
        sb.append("tel.numero as telefone, ");
        sb.append("ende.endereco as endereco, ");
        sb.append("em.email as email, ");
        sb.append("forn.planoconta, ");
        sb.append("forn.centrocusto ");
        sb.append("from fornecedor forn, pessoa pes ");
        sb.append("left join telefone tel on tel.pessoa = pes.codigo ");
        sb.append("left join endereco ende on ende.pessoa = pes.codigo ");
        sb.append("left join email em on em.pessoa = pes.codigo ");
        sb.append("where forn.pessoa = pes.codigo ");
        sb.append("and forn.pessoa = ? ");
        Declaracao dc = new Declaracao(sb.toString(), this.con);
        dc.setInt(1, pessoaVO.getCodigo());
        codigoAnterior = 0;
        List<FornecedorVO> result;
        try (ResultSet tabelaResultado = dc.executeQuery()) {
            result = this.montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        }
        return result.isEmpty() ? null : result.get(0);
    }

    @Override
    public void incluir(FornecedorVO obj) throws Exception {
        String sql = "insert into fornecedor (cnpj, contato, pessoa, empresa, inscricaoEstadual, inscricaomunicipal, cfdf, observacao, planoConta, centroCusto, " +
                "nomeFantasia, razaoSocial, dataCadastro, dataValidade, cnae, codigoFpas, grauRiscoNr4, grauRiscoInss, sindicato, nrTotalFuncionarios, porteEmpresa, documento, documentoExtensao, descricaocnae) " +
                "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setString(++i, retiraMascara(obj.getCnpj()));
            sqlInserir.setString(++i, obj.getContato().toUpperCase());
            sqlInserir.setInt(++i, obj.getPessoa().getCodigo());
            resolveIntegerNull(sqlInserir, ++i, obj.getEmpresaVO().getCodigo());
            sqlInserir.setString(++i, obj.getInscricaoEstadual().toUpperCase());
            sqlInserir.setString(++i, obj.getInscricaoMunicipal().toUpperCase());
            sqlInserir.setString(++i, obj.getCfdf().toUpperCase());
            sqlInserir.setString(++i, obj.getObservacao());
            resolveIntegerNull( sqlInserir, ++i, obj.getPlanoConta());
            resolveIntegerNull( sqlInserir, ++i, obj.getCentroCusto());
            sqlInserir.setString(++i, obj.getNomeFantasia());
            sqlInserir.setString(++i, obj.getRazaoSocial());
            resolveDateNull(sqlInserir, ++i, obj.getDataCadastro());
            resolveDateNull(sqlInserir, ++i, obj.getDataValidade());
            sqlInserir.setString(++i, obj.getCnae());
            sqlInserir.setString(++i, obj.getCodigoFpas());
            resolveIntegerNull( sqlInserir, ++i, obj.getGrauRiscoNr4());
            resolveIntegerNull( sqlInserir, ++i, obj.getGrauRiscoInss());
            sqlInserir.setString(++i, obj.getSindicato());
            resolveIntegerNull( sqlInserir, ++i, obj.getNrTotalFuncionarios());
            resolveIntegerNull( sqlInserir, ++i, obj.getPorteEmpresa());
            sqlInserir.setString(++i, obj.getDocumento());
            sqlInserir.setString(++i, obj.getDocumentoExtensao());
            sqlInserir.setString(++i, obj.getDescricaoCnae());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        obj.setCodigo(obterValorChavePrimariaCodigo("fornecedor"));
    }

    private String retiraMascara(String cnpj) {
        return cnpj.replaceAll("\\D", ""); // regex remove nao numerico
    }

    @Override
    public void alterar(FornecedorVO obj) throws Exception {
        String sql = "UPDATE fornecedor SET cnpj = ?, contato = ?, empresa = ?, inscricaoEstadual = ?, inscricaomunicipal = ?, cfdf = ?, observacao = ?, planoconta = ?, centrocusto = ?," +
                " nomeFantasia = ?, razaoSocial = ?, dataCadastro = ?, dataValidade = ?, cnae = ?, codigoFpas = ?, grauRiscoNr4 = ?,  grauRiscoInss = ?,  sindicato = ?,  nrTotalFuncionarios = ?," +
                " porteEmpresa = ?,  documento = ?,  documentoExtensao = ?, descricaocnae = ?" +
                " WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setString(++i, retiraMascara(obj.getCnpj()));
            ps.setString(++i, obj.getContato().toUpperCase());
            resolveIntegerNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            ps.setString(++i, obj.getInscricaoEstadual().toUpperCase());
            ps.setString(++i, obj.getInscricaoMunicipal().toUpperCase());
            ps.setString(++i, obj.getCfdf().toUpperCase());
            ps.setString(++i, obj.getObservacao());
            resolveIntegerNull(ps, ++i, obj.getPlanoConta());
            resolveIntegerNull(ps, ++i, obj.getCentroCusto());
            ps.setString(++i, obj.getNomeFantasia());
            ps.setString(++i, obj.getRazaoSocial());
            resolveDateNull(ps, ++i, obj.getDataCadastro());
            resolveDateNull(ps, ++i, obj.getDataValidade());
            ps.setString(++i, obj.getCnae());
            ps.setString(++i, obj.getCodigoFpas());
            resolveIntegerNull( ps, ++i, obj.getGrauRiscoNr4());
            resolveIntegerNull( ps, ++i, obj.getGrauRiscoInss());
            ps.setString(++i, obj.getSindicato());
            resolveIntegerNull( ps, ++i, obj.getNrTotalFuncionarios());
            resolveIntegerNull( ps, ++i, obj.getPorteEmpresa());
            ps.setString(++i, obj.getDocumento());
            ps.setString(++i, obj.getDocumentoExtensao());
            ps.setString(++i, obj.getDescricaoCnae());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    @Override
    public void excluir(FornecedorVO forn) throws Exception {
        try {
            con.setAutoCommit(false);
            Declaracao dc = new Declaracao("DELETE FROM fornecedor WHERE CODIGO = ?", this.con);
            dc.setInt(1, forn.getCodigo());
            dc.execute();
            getFacade().getPessoa().excluirConexaoInicializada(
                    getFacade().getPessoa().consultarPorCodigo(forn.getPessoa().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS), con);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public List<FornecedorVO> consultarPorDescricao(String valorConsulta) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("select forn.codigo as codigo, ");
        sb.append("forn.cnpj as cnpj, ");
        sb.append("forn.inscricaoEstadual as inscricaoEstadual, ");
        sb.append("forn.inscricaoMunicipal as inscricaoMunicipal, ");
        sb.append("forn.cfdf as cfdf, ");
        sb.append("forn.observacao, ");
        sb.append("forn.contato as contato, ");
        sb.append("pes.nome as descricao, ");
        sb.append("pes.codigo as pessoa, ");
        sb.append("tel.numero as telefone, ");
        sb.append("ende.endereco as endereco, ");
        sb.append("em.email as email, ");
        sb.append("forn.planoconta, ");
        sb.append("forn.centrocusto ");
        sb.append("from fornecedor forn, pessoa pes ");
        sb.append("left join telefone tel on tel.pessoa = pes.codigo ");
        sb.append("left join endereco ende on ende.pessoa = pes.codigo ");
        sb.append("left join email em on em.pessoa = pes.codigo ");
        sb.append("where forn.pessoa = pes.codigo ");
        sb.append("and upper( pes.nome ) like '" + valorConsulta.toUpperCase() + "%' ");
        sb.append("order by pes.nome ");
        try (Statement stm = this.con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            }
        }
    }

    @Override
    public boolean existeFornecedorNome(FornecedorVO fornecedorVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from fornecedor f ");
        sql.append("left join pessoa p on p.codigo = f.pessoa ");
        sql.append("where p.nome ilike '").append(Uteis.removerEspacosInicioFimString(fornecedorVO.getDescricao())).append("'");
        if (!UteisValidacao.emptyNumber(fornecedorVO.getCodigo())) {
            sql.append(" AND f.codigo <> ? ");
        }
        PreparedStatement sqlConsultar = this.con.prepareStatement(sql.toString());
        if (!UteisValidacao.emptyNumber(fornecedorVO.getCodigo())) {
            sqlConsultar.setInt(1, fornecedorVO.getCodigo());
        }
        ResultSet rs = sqlConsultar.executeQuery();
        return rs.next();
    }

    public boolean existeFornecedorCNPJ(FornecedorVO fornecedorVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from fornecedor ");
        sql.append("where cnpj = '").append(Uteis.formatarCpfCnpj(fornecedorVO.getCnpj(),true)).append("'");
        if (!UteisValidacao.emptyNumber(fornecedorVO.getCodigo())) {
            sql.append(" AND codigo <> ? ");
        }
        PreparedStatement sqlConsultar = this.con.prepareStatement(sql.toString());
        if (!UteisValidacao.emptyNumber(fornecedorVO.getCodigo())) {
            sqlConsultar.setInt(1, fornecedorVO.getCodigo());
        }
        ResultSet rs = sqlConsultar.executeQuery();
        return rs.next();
    }

    private PreparedStatement getPS() throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("Select f.codigo,p.nome,f.contato, f.dataValidade from Fornecedor f\n");
        sql.append("INNER JOIN Pessoa p ON  p.codigo = f.pessoa\n");
        sql.append(" ORDER BY  p.nome");
        return con.prepareStatement(sql.toString());
    }


    public String consultarJSON(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS().executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            Empresa empresaDAO = new Empresa(con);
            EmpresaVO emp = empresaDAO.consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome").trim())).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("contato")));
                if (emp.isHabilitarCadastroEmpresaSesi()) {
                    json.append("\",").append("\"").append((rs.getDate("dataValidade") != null &&
                            Calendario.maior(rs.getDate("dataValidade"), Calendario.hoje())) ? "Ativo" : "Inativo").append("\"],");
                } else {
                    json.append("\"],");
                }
            }
            empresaDAO = null;
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }
    @Override
    public List<FornecedorVO> consultarOrdenadoPelaDescricao() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * \n");
        sql.append("FROM fornecedor ORDER BY descricao");
        Declaracao dc = new Declaracao(sql.toString(), this.con);
        try (ResultSet tabelaResultado = dc.executeQuery()) {
            return (this.montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
    }

    @Override
    public FornecedorVO consultarPorCNPJExcetoCodigo(FornecedorVO fornecedor) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select fornecedor.*,pessoa.nome as pessoaNome from fornecedor ");
        sql.append("inner join pessoa on pessoa.codigo =fornecedor.pessoa ");
        sql.append("where upper(fornecedor.cnpj) like ('");
        sql.append("" + Uteis.removerMascara(fornecedor.getCnpj().toUpperCase()) + "')");
        sql.append(" AND fornecedor.codigo <> " + fornecedor.getCodigo().intValue() + "");
        sql.append(" ORDER BY fornecedor.cnpj; ");
        Declaracao dc = new Declaracao(sql.toString(), this.con);
        FornecedorVO result;
        try (ResultSet tabelaResultado = dc.executeQuery()) {
            result = null;
            if (tabelaResultado.next()) {
                result = this.montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            }
        }
        return result;
    }

    @Override
    public FornecedorVO consultarPorCNPJ(String cnpj) throws Exception {
        String sql = "SELECT fornecedor.*, pessoa.nome as pessoaNome " +
                "FROM fornecedor " +
                "INNER JOIN pessoa ON pessoa.codigo = fornecedor.pessoa " +
                "WHERE fornecedor.cnpj = ? " +
                "ORDER BY fornecedor.cnpj";

        Declaracao dc = new Declaracao(sql, this.con);
        dc.setString(1, Uteis.removerMascara(cnpj));

        FornecedorVO result = null;
        try (ResultSet tabelaResultado = dc.executeQuery()) {
            if (tabelaResultado.next()) {
                result = this.montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            }
        }
        return result;
    }

    @Override
    public FornecedorVO consultarPorCodigo(int codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from fornecedor f");
        sql.append("inner join pessoa p on p.codigo = f.pessoa ");
        sql.append("where f.codigo = ").append(codigo);

        try (PreparedStatement sqlConsultar = this.con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                }
                return null;
            }
        }
    }


    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>EventoVO</code> resultantes da consulta.
     */
    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        codigoAnterior = 0;
        while (tabelaResultado.next()) {
            FornecedorVO obj = new FornecedorVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            if (obj != null) {
                vetResultado.add(obj);
            }
        }
        tabelaResultado = null;
        return vetResultado;
    }

    public FornecedorVO montarDados(final ResultSet dadosSQL, int nivelMontarDados) throws SQLException {
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            if (dadosSQL.getInt("codigo") == codigoAnterior) {
                // é o mesmo fornecedor anterior
                // estamos manipulando o mesmo registro por se tratar de outro
                // telefone, email ou endereço.
                return null;
            } else {
                FornecedorVO obj = new FornecedorVO();
                obj.setCodigo(dadosSQL.getInt("codigo"));
                obj.setDescricao(dadosSQL.getString("descricao"));
                obj.setTelefone(dadosSQL.getString("telefone"));
                obj.setEndereco(dadosSQL.getString("endereco"));
                obj.setEmail(dadosSQL.getString("email"));
                obj.setInscricaoEstadual(dadosSQL.getString("inscricaoEstadual"));
                obj.setInscricaoMunicipal(dadosSQL.getString("inscricaomunicipal"));
                obj.setCfdf(dadosSQL.getString("cfdf"));
                obj.setObservacao(dadosSQL.getString("observacao"));

                //seta a pessoa
                obj.setPessoa(new PessoaVO());
                obj.getPessoa().setNome(dadosSQL.getString("descricao"));
                obj.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));

                String cnpj = dadosSQL.getString("cnpj");
                if (cnpj != null && !"".equals(cnpj)) {
                    obj.setCnpj(Formatador.formatarString("##.###.###/####-##",
                            cnpj));
                }
                obj.setContato(dadosSQL.getString("contato"));
                codigoAnterior = dadosSQL.getInt("codigo");
                obj.setPlanoConta(dadosSQL.getInt("planoconta"));
                obj.setCentroCusto(dadosSQL.getInt("centrocusto"));

                try {
                    obj.setNomeFantasia(dadosSQL.getString("nomeFantasia"));
                    obj.setRazaoSocial(dadosSQL.getString("razaoSocial"));
                    obj.setDataCadastro(dadosSQL.getDate("dataCadastro"));
                    obj.setDataValidade(dadosSQL.getDate("dataValidade"));
                    obj.setCnae(dadosSQL.getString("cnae"));
                    obj.setDescricaoCnae(dadosSQL.getString("descricaocnae"));
                    obj.setCodigoFpas(dadosSQL.getString("codigoFpas"));
                    obj.setGrauRiscoNr4(dadosSQL.getInt("grauRiscoNr4"));
                    obj.setGrauRiscoInss(dadosSQL.getInt("grauRiscoInss"));
                    obj.setSindicato(dadosSQL.getString("sindicato"));
                    obj.setNrTotalFuncionarios(dadosSQL.getInt("nrTotalFuncionarios"));
                    obj.setPorteEmpresa(dadosSQL.getInt("porteEmpresa"));
                    obj.setDocumento(dadosSQL.getString("documento"));
                    obj.setDocumentoExtensao(dadosSQL.getString("documentoExtensao"));
                }catch (Exception ex){}

                return obj;
            }
        }
        FornecedorVO obj1 = new FornecedorVO();
        obj1.setCodigo(dadosSQL.getInt("codigo"));
        obj1.setContato(dadosSQL.getString("contato"));
        String cnpj = dadosSQL.getString("cnpj");
        if (cnpj != null && !"".equals(cnpj)) {
            obj1.setCnpj(Formatador.formatarString("##.###.###/####-##",
                    cnpj));
        }
        obj1.setInscricaoEstadual(dadosSQL.getString("inscricaoEstadual"));
        obj1.setInscricaoMunicipal(dadosSQL.getString("inscricaomunicipal"));
        obj1.setCfdf(dadosSQL.getString("cfdf"));
        obj1.setObservacao(dadosSQL.getString("observacao"));
        obj1.setPessoa(new PessoaVO());
        obj1.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
        obj1.setEmpresaVO(new EmpresaVO());
        obj1.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            obj1.getPessoa().setNome(dadosSQL.getString("pessoaNome"));
        }
        return obj1;
    }
}
