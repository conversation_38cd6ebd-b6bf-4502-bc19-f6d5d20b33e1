package negocio.facade.jdbc.vendas;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoCategoria;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Categoria;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.Pix;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.pix.PixStatusEnum;
import servicos.vendasonline.VendasOnlineService;
import servicos.vendasonline.dto.RetornoVendaTO;
import servicos.vendasonline.dto.VendaDTO;
import servicos.vendasonline.dto.VendaProdutoDTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 30/09/2022
 */
public class VendasOnlineVenda extends SuperEntidade {

    public VendasOnlineVenda() throws Exception {
        super();
    }

    public VendasOnlineVenda(Connection con) throws Exception {
        super(con);
    }

    public void incluirVendasOnlineVenda(RetornoVendaTO retornoVendaTO) throws Exception {
        incluir(new VendasOnlineVendaVO(retornoVendaTO));
    }

    private void incluir(VendasOnlineVendaVO obj) throws Exception {
        try (PreparedStatement stm = con.prepareStatement("INSERT INTO VendasOnlineVenda(dataRegistro,pessoa,dados,transacao,boleto,pix) VALUES (?,?,?,?,?,?);")) {
            int i = 0;
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            SuperFacadeJDBC.resolveIntegerNull(stm, ++i, obj.getPessoaVO().getCodigo());
            stm.setString(++i, obj.getDados());
            SuperFacadeJDBC.resolveIntegerNull(stm, ++i, obj.getTransacaoVO().getCodigo());
            SuperFacadeJDBC.resolveIntegerNull(stm, ++i, obj.getBoletoVO().getCodigo());
            SuperFacadeJDBC.resolveIntegerNull(stm, ++i, obj.getPixVO().getCodigo());
            stm.execute();
        }
    }

    public void estornarVendaVendasOnline(PixVO pixVO) {
        try {
            if (pixVO.getStatusEnum().equals(PixStatusEnum.ATIVA) ||
                    pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                return;
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("v.* \n");
            sql.append("from vendasonlinevenda v \n");
            sql.append("inner join pix p on p.codigo = v.pix \n");
            sql.append("where p.codigo = ").append(pixVO.getCodigo()).append(" \n");
            sql.append("and (select v.estornarVendaPix from vendasonlineconfig v where v.empresa = p.empresa) \n");
            ResultSet rs = criarConsulta(sql.toString(), this.getCon());
            while (rs.next()) {
                Usuario usuarioDAO;
                VendasOnlineService vendasOnlineService;
                try {
                    usuarioDAO = new Usuario(this.getCon());
                    String key = DAO.resolveKeyFromConnection(this.getCon());
                    vendasOnlineService = new VendasOnlineService(key, this.getCon());
                    Uteis.logarDebug("ESTORNAR PIX " + pixVO.getCodigo() + " | STATUS " + pixVO.getStatus());
                    JSONObject dados = new JSONObject(rs.getString("dados"));
                    List<RetornoVendaTO> lista = new ArrayList<>();
                    RetornoVendaTO retornoVendaTO = new RetornoVendaTO(dados);
                    lista.add(retornoVendaTO);
                    vendasOnlineService.estornarDevidoErro(key, lista, usuarioDAO.getUsuarioRecorrencia(), "Estorno devido status PIX | " + pixVO.getStatusEnum().getDescricao());
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logar(ex, VendasOnlineVenda.class);
                    Uteis.logarDebug("ERRO | ESTORNAR PIX " + pixVO.getCodigo() + " | MSG " + ex.getMessage());
                } finally {
                    vendasOnlineService = null;
                    usuarioDAO = null;
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void marcarAulaVendasOnline(PixVO pixVO, BoletoVO boletoVO) {
        //marcar aula do aluno pelo fluxo de pagamento do Pix
        try {

            StringBuilder sql = new StringBuilder();

            if (pixVO != null) {
                if (!pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                    return;
                }

                sql.append("select \n");
                sql.append("v.* \n");
                sql.append("from vendasonlinevenda v \n");
                sql.append("inner join pix p on p.codigo = v.pix \n");
                sql.append("where p.codigo = ").append(pixVO.getCodigo()).append(" \n");
            } else {
                if (!boletoVO.getSituacao().equals(SituacaoBoletoEnum.PAGO)) {
                    return;
                }

                sql.append("select \n");
                sql.append("v.* \n");
                sql.append("from vendasonlinevenda v \n");
                sql.append("inner join boleto b on b.codigo = v.boleto \n");
                sql.append("where b.codigo = ").append(boletoVO.getCodigo()).append(" \n");
            }

            ResultSet rs = criarConsulta(sql.toString(), this.getCon());
            while (rs.next()) {
                Usuario usuarioDAO;
                VendasOnlineService vendasOnlineService;
                Cliente clienteDAO;
                try {
                    usuarioDAO = new Usuario(this.getCon());
                    String key = DAO.resolveKeyFromConnection(this.getCon());
                    vendasOnlineService = new VendasOnlineService(key, this.getCon());
                    Uteis.logarDebug("MARCAR AULA DO ALUNO...");
                    JSONObject dados = new JSONObject(rs.getString("dados"));
                    List<RetornoVendaTO> lista = new ArrayList<>();
                    RetornoVendaTO retornoVendaTO = new RetornoVendaTO(dados);
                    clienteDAO = new Cliente(this.getCon());
                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pixVO.getPessoa(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    retornoVendaTO.setClienteVO(clienteVO);
                    lista.add(retornoVendaTO);

                    List<VendaDTO> listaVendas = new ArrayList<>();
                    listaVendas.add(retornoVendaTO.getVendaDTO());
                    vendasOnlineService.marcarAulasAluno(key, lista, listaVendas, usuarioDAO.getUsuarioRecorrencia(), false);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logar(ex, VendasOnlineVenda.class);
                    Uteis.logarDebug("ERRO: não foi possível marcar a aula do aluno pelo fluxo de pagamento de pix | MSG: " + ex.getMessage());
                } finally {
                    vendasOnlineService = null;
                    usuarioDAO = null;
                    clienteDAO = null;
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public PixVO obterPixAtivoVendaContrato(ClienteVO clienteVO, VendaDTO vendaDTO) {
        Pix pixDAO;
        try {
            pixDAO = new Pix(this.getCon());
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("v.*, \n");
            sql.append("(p.data + p.expiracao * interval '1 second') expira_em \n");
            sql.append("from vendasonlinevenda v \n");
            sql.append("inner join pix p on p.codigo = v.pix \n");
            sql.append("where v.dataregistro::date = current_date \n");
            sql.append("and p.status = 'ATIVA' \n");
            sql.append("and exists(select pm.codigo from pixmovparcela pm where pm.pix = p.codigo) \n");
            sql.append("and v.pessoa = ").append(clienteVO.getPessoa().getCodigo()).append(" \n");
            sql.append("order by v.codigo desc \n");

            ResultSet rs = criarConsulta(sql.toString(), this.getCon());
            while (rs.next()) {
                Date expira_em = rs.getTimestamp("expira_em");
                Long diferenca = Calendario.diferencaEmMinutos(Calendario.hoje(), expira_em);
                Integer difMinutos = diferenca.intValue();
                if (difMinutos >= 10) {
                    VendaDTO vendaDTOExiste = new VendaDTO(new JSONObject(rs.getString("dados")).optString("vendaDTO"));
                    boolean planoIgual = (vendaDTOExiste.getPlano().equals(vendaDTO.getPlano()));
                    boolean vendaAvulsaIgual = (vendaDTOExiste.getProdutos().size() == vendaDTO.getProdutos().size());
                    if (vendaAvulsaIgual) {
                        for (VendaProdutoDTO vendaProdutoDTO : vendaDTO.getProdutos()) {
                            boolean existe = false;
                            for (VendaProdutoDTO vendaProdutoDTOExiste : vendaDTOExiste.getProdutos()) {
                                // Tem que ser o mesmo produto
                                if (vendaProdutoDTO.getProduto().equals(vendaProdutoDTOExiste.getProduto())) {
                                    // Tem que ser na mesma quantidade
                                    if(vendaProdutoDTO.getQtd().equals(vendaProdutoDTOExiste.getQtd())) {
                                        // E tem que ser pelo mesmo preço
                                        if(vendaProdutoDTO.getValorUnitario().equals(vendaProdutoDTOExiste.getValorUnitario())) {
                                            existe = true;
                                        }
                                    }
                                    break;
                                }
                            }
                            if (!existe) {
                                vendaAvulsaIgual = false;
                            }
                        }
                    }
                    if (planoIgual && vendaAvulsaIgual) {
                        return pixDAO.consultarPorCodigo(rs.getInt("pix"));
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            pixDAO = null;
        }
    }

    public List<BoletoVO> obterBoletoAtivoVendaContrato(ClienteVO clienteVO, VendaDTO vendaDTO) {
        Boleto boletoDAO;
        try {
            boletoDAO = new Boleto(this.getCon());
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("v.* \n");
            sql.append("from vendasonlinevenda v \n");
            sql.append("inner join boleto b on b.codigo = v.boleto \n");
            sql.append("where v.dataregistro::date = current_date \n");
            sql.append("and exists(select bm.codigo from boletomovparcela bm where bm.boleto = b.codigo) \n");
            sql.append("and b.situacao in (").append(SituacaoBoletoEnum.AGUARDANDO_REGISTRO.getCodigo()).append(",");
            sql.append(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO.getCodigo()).append(") \n");
            sql.append("and v.pessoa = ").append(clienteVO.getPessoa().getCodigo()).append(" \n");
            sql.append("order by v.codigo desc \n");

            ResultSet rs = criarConsulta(sql.toString(), this.getCon());
            while (rs.next()) {
                VendaDTO vendaDTOExiste = new VendaDTO(new JSONObject(rs.getString("dados")).optString("vendaDTO"));
                boolean planoIgual = (vendaDTOExiste.getPlano().equals(vendaDTO.getPlano()));
                boolean vendaAvulsaIgual = (vendaDTOExiste.getProdutos().size() == vendaDTO.getProdutos().size());
                if (vendaAvulsaIgual) {
                    for (VendaProdutoDTO vendaProdutoDTO : vendaDTO.getProdutos()) {
                        boolean existe = false;
                        for (VendaProdutoDTO vendaProdutoDTOExiste : vendaDTOExiste.getProdutos()) {
                            if (vendaProdutoDTO.getProduto().equals(vendaProdutoDTOExiste.getProduto())) {
                                existe = true;
                                break;
                            }
                        }
                        if (!existe) {
                            vendaAvulsaIgual = false;
                        }
                    }
                }
                if (planoIgual && vendaAvulsaIgual) {
                    List<BoletoVO> lista = new ArrayList<>();
                    try {
                        JSONObject jsonDados = new JSONObject(rs.getString("dados"));
                        JSONArray jsonArray = jsonDados.getJSONArray("boletosGerados");
                        for (int e = 0; e < jsonArray.length(); e++) {
                            try {
                                BoletoVO boletoVO = boletoDAO.consultarPorChavePrimaria(jsonArray.getInt(e), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                lista.add(boletoVO);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    return lista;
                }
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            boletoDAO = null;
        }
    }

    public void incluirCategoriaClientePix(PixVO pixVO, Integer pessoa, Integer empresa, UsuarioVO usuarioVO) {
        Categoria categoriaDAO;
        Cliente clienteDAO;
        VendasOnlineService service;
        try {
            service = new VendasOnlineService(null, this.con);
            if (!service.obterConfigIncluirCategoriaPix(empresa) &&
                    !UteisValidacao.emptyNumber(service.obterConvenioPixVendaContrato(empresa))) {
                return;
            }
            categoriaDAO = new Categoria(this.con);
            clienteDAO = new Cliente(this.con);

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("cl.codigo, \n");
            sql.append("c.nome, \n");
            sql.append("c.codigo as categoria \n");
            sql.append("from cliente cl \n");
            sql.append("left join categoria c on c.codigo = cl.categoria \n");
            sql.append("where cl.pessoa = ").append(pessoa).append(" \n");
            sql.append("and exists(select codigo from vendasonlinevenda where pix = ").append(pixVO.getCodigo()).append(") ");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con);

            ClienteVO clienteVO = null;
            if (rs.next()) {
                clienteVO = new ClienteVO();
                clienteVO.setCodigo(rs.getInt("codigo"));
                clienteVO.setCategoria(new CategoriaVO());
                clienteVO.getCategoria().setCodigo(rs.getInt("categoria"));
                clienteVO.getCategoria().setNome(rs.getString("nome"));
            } else {
                return;
            }

            String descricao = "VENDAS ONLINE - PIX";
            List<CategoriaVO> lista = categoriaDAO.consultarPorNome(descricao, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            CategoriaVO categoriaVO = null;
            if (UteisValidacao.emptyList(lista)) {
                categoriaVO = new CategoriaVO();
                categoriaVO.setNome(descricao);
                categoriaVO.setTipoCategoria(TipoCategoria.ALUNO.getCodigo());
                categoriaDAO.incluir(categoriaVO);
            } else {
                categoriaVO = lista.get(0);
            }

            if (categoriaVO == null) {
                throw new Exception("Categoria não encontrada");
            }
            clienteDAO.alterarCategoria(clienteVO, categoriaVO, usuarioVO, "Alteração automática Vendas Online");
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            categoriaDAO = null;
            clienteDAO = null;
            service = null;
        }
    }
}
