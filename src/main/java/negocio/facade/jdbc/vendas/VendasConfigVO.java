package negocio.facade.jdbc.vendas;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.FKJson;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.VendasConfigWS;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.oamd.CampanhaCupomDescontoVO;

public class VendasConfigVO extends SuperVO {

    private Integer codigo;
    private Integer empresa;
    private Boolean redirecionarApp = true;
    private String paginaRedirecionar;
    private String urlvenda;
    private String emailAvisar;
    private String cor;
    private String camposAdicionais;
    private String camposAdicionaisProduto;
    private String camposAdicionaisPlanoFlow;
    private String camposAdicionaisProdutoFlow;
    private String analyticsId;
    private String pixelId;
    private String tokenApiConversao;
    private String googleTagId;
    private String googleTagIdHotsite;
    private Boolean cobrarPrimeiraParcelaCompra = true;
    private Boolean detalharParcelaTelaCheckout = false;
    private Boolean cobrarProdutosJuntoAdesao = false;
    private String tituloCheckout = "Bora treinar?";
    @ChaveEstrangeira
    private ColaboradorVO consultorSite;
    private Boolean apresentarValorAnuidade = false;
    private boolean apresentarProdutoSemEstoque = false;
    private boolean usarConvenioPlanoProduto = false;
    private boolean usarFormaPagamentoPlanoProduto = false;
    private boolean apresentarCPFLinkPag = false;
    private boolean apresentarDtFaturaLinkPag = false;
    private boolean apresentarTermoAceiteLinkPag = false;
    private boolean renovarContratoAntigo = false;
    private boolean cobrarPrimeiraParcelaCompraRenovacao = false;
    private boolean cobrarParcelasMesSeguinteRenovacao = false;
    private boolean pagarUsandoSaldoCliente = false;
    private boolean habilitarCampanha = false;
    @ChaveEstrangeira
    @FKJson
    private ConvenioCobrancaVO convenioCobrancaPixVO;
    @ChaveEstrangeira
    @FKJson
    private ConvenioCobrancaVO convenioCobrancaBoletoVO;

    //Site
    private boolean temaClaro = false;
    private boolean temaEscuro = false;
    private String tema = "";
    private AgendaVendasOnlineVO agenda;
    private boolean integracaoBotConversa = false;
    private String enderecoEnviarAcoesBotConversa;
    private boolean gerarBoletoTodasParcelas = false;
    private boolean criarAutorizacaoCobrancaPix = false;
    private boolean criarAutorizacaoCobrancaBoleto = false;
    private boolean incluirCategoriaPix = false;
    private Integer diasVencimentoBoleto;
    private boolean estornarVendaPix = true;
    private boolean dominioProprioHotsite = false;
    private boolean permitirMudarTipoParcelamento = false;
    private boolean apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano = false;
    private boolean permiteContratoConcomitanteComParcelaEmAberto = true;
    private CampanhaCupomDescontoVO campanhaCupomDescontoIndicacoes;
    private boolean permiteVendaProdutoAlunoOutraUnidade = false;
    private boolean exibirTipoDocumentoTelaVendasOnline = false;
    private boolean habilitarAgendamentoAulaExperimentalLinkVisitante = false;
    private boolean habilitarPreCadastro = false;
    private boolean configSescHabilitada = false;

    private boolean enviarEmailUsuarioMovelAutomaticamente = true;
    private boolean primeiraCobrancaPixEGuardarCartao = false;
    private boolean exibirBotaoAgendaSobreBanner = false;
    private boolean modalidadesIniciarSelecionadasContratoTurma = false;
    private boolean ativarLinksGooglePlayEAppleStore = false;
    private String urlLinkGooglePlay;
    private String urlLinkAppleStore;
    private boolean permiteProsseguirMesmoCpfCadastroVisitante = false;

    private boolean exibeDataUtilizacaoDiaria = false;
    private String igopassUrl;
    private String igopassQrCodeUrl;
    private String igopassEstabelecimento;
    private String igopassUsuario;

    public String getCamposAdicionais() {
        return camposAdicionais == null ? "" : camposAdicionais;
    }

    public void setCamposAdicionais(String camposAdicionais) {
        this.camposAdicionais = camposAdicionais;
    }

    public VendasConfigVO() {

    }
    public VendasConfigVO(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Boolean getRedirecionarApp() {
        return redirecionarApp;
    }

    public void setRedirecionarApp(Boolean redirecionarApp) {
        this.redirecionarApp = redirecionarApp;
    }

    public String getPaginaRedirecionar() {
        return paginaRedirecionar;
    }

    public void setPaginaRedirecionar(String paginaRedirecionar) {
        this.paginaRedirecionar = paginaRedirecionar;
    }

    public String getUrlvenda() {
        return urlvenda;
    }

    public void setUrlvenda(String urlvenda) {
        this.urlvenda = urlvenda;
    }

    public String getAnalyticsId() {
        return analyticsId;
    }

    public void setAnalyticsId(String analyticsId) {
        this.analyticsId = analyticsId;
    }

    public String getPixelId() {
        return pixelId;
    }

    public void setPixelId(String pixelId) {
        this.pixelId = pixelId;
    }

    public String getTokenApiConversao() {
        return tokenApiConversao;
    }

    public void setTokenApiConversao(String tokenApiConversao) {
        this.tokenApiConversao = tokenApiConversao;
    }

    public String getCor() {
        if(cor == null){
            cor = "#1998fc";
        }
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getEmailAvisar() {
        return emailAvisar;
    }

    public void setEmailAvisar(String emailAvisar) {
        this.emailAvisar = emailAvisar;
    }

    public Boolean getCobrarPrimeiraParcelaCompra() {
        return cobrarPrimeiraParcelaCompra;
    }

    public void setCobrarPrimeiraParcelaCompra(Boolean cobrarPrimeiraParcelaCompra) {
        this.cobrarPrimeiraParcelaCompra = cobrarPrimeiraParcelaCompra;
    }

    public Boolean getDetalharParcelaTelaCheckout() {
        return detalharParcelaTelaCheckout;
    }

    public void setDetalharParcelaTelaCheckout(Boolean detalharParcelaTelaCheckout) {
        this.detalharParcelaTelaCheckout = detalharParcelaTelaCheckout;
    }

    public Boolean getCobrarProdutosJuntoAdesao() {
        return cobrarProdutosJuntoAdesao;
    }

    public void setCobrarProdutosJuntoAdesao(Boolean cobrarProdutosJuntoAdesao) {
        this.cobrarProdutosJuntoAdesao = cobrarProdutosJuntoAdesao;
    }

    public String getTituloCheckout() {
        return tituloCheckout;
    }

    public void setTituloCheckout(String tituloCheckout) {
        this.tituloCheckout = tituloCheckout;
    }

    public ColaboradorVO getConsultorSite() {
        if(consultorSite == null){
            consultorSite = new ColaboradorVO();
        }
        return consultorSite;
    }

    public void setConsultorSite(ColaboradorVO consultorSite) {
        this.consultorSite = consultorSite;
    }

    public Boolean getApresentarValorAnuidade() {
        return apresentarValorAnuidade;
    }

    public void setApresentarValorAnuidade(Boolean apresentarValorAnuidade) {
        this.apresentarValorAnuidade = apresentarValorAnuidade;
    }

    public boolean isApresentarProdutoSemEstoque() {
        return apresentarProdutoSemEstoque;
    }

    public void setApresentarProdutoSemEstoque(boolean apresentarProdutoSemEstoque) {
        this.apresentarProdutoSemEstoque = apresentarProdutoSemEstoque;
    }

    public boolean isUsarConvenioPlanoProduto() {
        return usarConvenioPlanoProduto;
    }

    public void setUsarConvenioPlanoProduto(boolean usarConvenioPlanoProduto) {
        this.usarConvenioPlanoProduto = usarConvenioPlanoProduto;
    }

    public boolean isApresentarCPFLinkPag() {
        return apresentarCPFLinkPag;
    }

    public void setApresentarCPFLinkPag(boolean apresentarCPFLinkPag) {
        this.apresentarCPFLinkPag = apresentarCPFLinkPag;
    }

    public boolean isApresentarDtFaturaLinkPag() {
        return apresentarDtFaturaLinkPag;
    }

    public void setApresentarDtFaturaLinkPag(boolean apresentarDtFaturaLinkPag) {
        this.apresentarDtFaturaLinkPag = apresentarDtFaturaLinkPag;
    }

    public boolean isApresentarTermoAceiteLinkPag() {
        return apresentarTermoAceiteLinkPag;
    }

    public void setApresentarTermoAceiteLinkPag(boolean apresentarTermoAceiteLinkPag) {
        this.apresentarTermoAceiteLinkPag = apresentarTermoAceiteLinkPag;
    }

    public boolean isRenovarContratoAntigo() {
        if (!renovarContratoAntigo){
            setCobrarPrimeiraParcelaCompraRenovacao(false);
            setCobrarParcelasMesSeguinteRenovacao(false);
        }
        return renovarContratoAntigo;
    }

    public void setRenovarContratoAntigo(boolean renovarContratoAntigo) {
        this.renovarContratoAntigo = renovarContratoAntigo;
    }

    public boolean isCobrarPrimeiraParcelaCompraRenovacao() {
        return cobrarPrimeiraParcelaCompraRenovacao;
    }

    public void setCobrarPrimeiraParcelaCompraRenovacao(boolean cobrarPrimeiraParcelaCompraRenovacao) {
        this.cobrarPrimeiraParcelaCompraRenovacao = cobrarPrimeiraParcelaCompraRenovacao;
    }

    public boolean isCobrarParcelasMesSeguinteRenovacao() {
        return cobrarParcelasMesSeguinteRenovacao;
    }

    public void setCobrarParcelasMesSeguinteRenovacao(boolean cobrarParcelasMesSeguinteRenovacao) {
        this.cobrarParcelasMesSeguinteRenovacao = cobrarParcelasMesSeguinteRenovacao;
    }

    public boolean isPagarUsandoSaldoCliente() {
        return pagarUsandoSaldoCliente;
    }

    public void setPagarUsandoSaldoCliente(boolean pagarUsandoSaldoCliente) {
        this.pagarUsandoSaldoCliente = pagarUsandoSaldoCliente;
    }

    public boolean isHabilitarCampanha() {
        return habilitarCampanha;
    }

    public void setHabilitarCampanha(boolean habilitarCampanha) {
        this.habilitarCampanha = habilitarCampanha;
    }

    public VendasConfigWS toWS() {
        VendasConfigWS vendasConfigWS = new VendasConfigWS();

        vendasConfigWS.setCodigo(this.getCodigo());
        vendasConfigWS.setEmpresa(this.getEmpresa());
        vendasConfigWS.setRedirecionarApp(this.getRedirecionarApp());
        vendasConfigWS.setPaginaRedirecionar(this.getPaginaRedirecionar());
        vendasConfigWS.setUrlvenda(this.getUrlvenda());
        vendasConfigWS.setEmailAvisar(this.getEmailAvisar());
        vendasConfigWS.setCor(this.getCor());
        vendasConfigWS.setCamposAdicionais(this.getCamposAdicionais());
        vendasConfigWS.setAnalyticsId(this.getAnalyticsId());
        vendasConfigWS.setPixelId(this.getPixelId());
        vendasConfigWS.setTokenApiConversao(this.getTokenApiConversao());
        vendasConfigWS.setCobrarPrimeiraParcelaCompra(this.getCobrarPrimeiraParcelaCompra());
        vendasConfigWS.setCobrarPrimeiraParcelaCompraRenovacao(this.isCobrarPrimeiraParcelaCompraRenovacao());
        vendasConfigWS.setDetalharParcelaTelaCheckout(this.getDetalharParcelaTelaCheckout());
        vendasConfigWS.setCobrarProdutosJuntoAdesao(this.getCobrarProdutosJuntoAdesao());
        vendasConfigWS.setTituloCheckout(this.getTituloCheckout());
        vendasConfigWS.setApresentarValorAnuidade(this.getApresentarValorAnuidade());
        vendasConfigWS.setApresentarProdutoSemEstoque(this.getApresentarValorAnuidade());
        vendasConfigWS.setUsarConvenioPlanoProduto(this.isUsarConvenioPlanoProduto());
        vendasConfigWS.setUsarFormaPagamentoPlanoProduto(this.isUsarFormaPagamentoPlanoProduto());
        vendasConfigWS.setApresentarCPFLinkPag(this.isApresentarCPFLinkPag());
        vendasConfigWS.setApresentarDtFaturaLinkPag(this.isApresentarDtFaturaLinkPag());
        vendasConfigWS.setApresentarTermoAceiteLinkPag(this.isApresentarTermoAceiteLinkPag());
        vendasConfigWS.setRenovarContratoAntigo(this.isRenovarContratoAntigo());
        vendasConfigWS.setCobrarParcelasMesSeguinteRenovacao(this.isCobrarParcelasMesSeguinteRenovacao());
        vendasConfigWS.setGoogleTagId(this.getGoogleTagId());
        vendasConfigWS.setGoogleTagIdHotsite(this.getGoogleTagIdHotsite());
        vendasConfigWS.setPermitirMudarTipoParcelamento(isPermitirMudarTipoParcelamento());
        vendasConfigWS.setHabilitarPreCadastro(this.habilitarPreCadastro);
        vendasConfigWS.setConfigSescHabilitada(this.configSescHabilitada);
        vendasConfigWS.setIgopassUrl(this.getIgopassUrl());
        vendasConfigWS.setIgopassQrCodeUrl(this.getIgopassQrCodeUrl());
        vendasConfigWS.setIgopassEstabelecimento(this.getIgopassEstabelecimento());
        vendasConfigWS.setIgopassUsuario(this.getIgopassUsuario());

        return vendasConfigWS;
    }

    public boolean isTemaClaro() {
        return temaClaro;
    }

    public void setTemaClaro(boolean temaClaro) {
        this.temaClaro = temaClaro;
    }

    public boolean isTemaEscuro() {
        return temaEscuro;
    }

    public void setTemaEscuro(boolean temaEscuro) {
        this.temaEscuro = temaEscuro;
    }

    public String getTema() {
        return tema;
    }

    public void setTema(String tema) {
        this.tema = tema;
    }

    public void setAgenda(AgendaVendasOnlineVO agendaVendasOnlineVO) {
        this.agenda = agendaVendasOnlineVO;
    }

    public AgendaVendasOnlineVO getAgenda() {
        return agenda;
    }

    public ConvenioCobrancaVO getConvenioCobrancaPixVO() {
        if (convenioCobrancaPixVO == null) {
            convenioCobrancaPixVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaPixVO;
    }

    public void setConvenioCobrancaPixVO(ConvenioCobrancaVO convenioCobrancaPixVO) {
        this.convenioCobrancaPixVO = convenioCobrancaPixVO;
    }

    public String getGoogleTagId() {
        return googleTagId;
    }

    public void setGoogleTagId(String googleTagId) {
        this.googleTagId = googleTagId;
    }

    public boolean isIntegracaoBotConversa() {
        return integracaoBotConversa;
    }

    public void setIntegracaoBotConversa(boolean integracaoBotConversa) {
        this.integracaoBotConversa = integracaoBotConversa;
    }

    public String getEnderecoEnviarAcoesBotConversa() {
        return enderecoEnviarAcoesBotConversa;
    }

    public void setEnderecoEnviarAcoesBotConversa(String enderecoEnviarAcoesBotConversa) {
        this.enderecoEnviarAcoesBotConversa = enderecoEnviarAcoesBotConversa;
    }

    public ConvenioCobrancaVO getConvenioCobrancaBoletoVO() {
        if (convenioCobrancaBoletoVO == null) {
            convenioCobrancaBoletoVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaBoletoVO;
    }

    public void setConvenioCobrancaBoletoVO(ConvenioCobrancaVO convenioCobrancaBoletoVO) {
        this.convenioCobrancaBoletoVO = convenioCobrancaBoletoVO;
    }

    public boolean isGerarBoletoTodasParcelas() {
        return gerarBoletoTodasParcelas;
    }

    public void setGerarBoletoTodasParcelas(boolean gerarBoletoTodasParcelas) {
        this.gerarBoletoTodasParcelas = gerarBoletoTodasParcelas;
    }

    public boolean isCriarAutorizacaoCobrancaPix() {
        return criarAutorizacaoCobrancaPix;
    }

    public void setCriarAutorizacaoCobrancaPix(boolean criarAutorizacaoCobrancaPix) {
        this.criarAutorizacaoCobrancaPix = criarAutorizacaoCobrancaPix;
    }

    public boolean isCriarAutorizacaoCobrancaBoleto() {
        return criarAutorizacaoCobrancaBoleto;
    }

    public void setCriarAutorizacaoCobrancaBoleto(boolean criarAutorizacaoCobrancaBoleto) {
        this.criarAutorizacaoCobrancaBoleto = criarAutorizacaoCobrancaBoleto;
    }

    public Integer getDiasVencimentoBoleto() {
        if (diasVencimentoBoleto == null) {
            diasVencimentoBoleto= 0;
        }
        return diasVencimentoBoleto;
    }

    public void setDiasVencimentoBoleto(Integer diasVencimentoBoleto) {
        this.diasVencimentoBoleto = diasVencimentoBoleto;
    }

    public boolean isIncluirCategoriaPix() {
        return incluirCategoriaPix;
    }

    public void setIncluirCategoriaPix(boolean incluirCategoriaPix) {
        this.incluirCategoriaPix = incluirCategoriaPix;
    }

    public boolean isEstornarVendaPix() {
        return estornarVendaPix;
    }

    public void setEstornarVendaPix(boolean estornarVendaPix) {
        this.estornarVendaPix = estornarVendaPix;
    }

    public boolean isDominioProprioHotsite() {
        return dominioProprioHotsite;
    }

    public void setDominioProprioHotsite(boolean dominioProprioHotsite) {
        this.dominioProprioHotsite = dominioProprioHotsite;
    }

    public boolean isPermitirMudarTipoParcelamento() {
        return permitirMudarTipoParcelamento;
    }

    public void setPermitirMudarTipoParcelamento(boolean permitirMudarTipoParcelamento) {
        this.permitirMudarTipoParcelamento = permitirMudarTipoParcelamento;
    }

    public boolean isApresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano() {
        return apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano;
    }

    public void setApresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano(boolean apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano) {
        this.apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano = apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano;
    }

    public boolean isPermiteContratoConcomitanteComParcelaEmAberto() {
        return permiteContratoConcomitanteComParcelaEmAberto;
    }

    public void setPermiteContratoConcomitanteComParcelaEmAberto(boolean permiteContratoConcomitanteComParcelaEmAberto) {
        this.permiteContratoConcomitanteComParcelaEmAberto = permiteContratoConcomitanteComParcelaEmAberto;
    }
    public CampanhaCupomDescontoVO getCampanhaCupomDescontoIndicacoes() {
        if (campanhaCupomDescontoIndicacoes == null) {
            campanhaCupomDescontoIndicacoes = new CampanhaCupomDescontoVO();
        }
        return campanhaCupomDescontoIndicacoes;
    }

    public void setCampanhaCupomDescontoIndicacoes(CampanhaCupomDescontoVO campanhaCupomDescontoIndicacoes) {
        this.campanhaCupomDescontoIndicacoes = campanhaCupomDescontoIndicacoes;
    }

    public boolean isPermiteVendaProdutoAlunoOutraUnidade() {
        return permiteVendaProdutoAlunoOutraUnidade;
    }

    public void setPermiteVendaProdutoAlunoOutraUnidade(boolean permiteVendaProdutoAlunoOutraUnidade) {
        this.permiteVendaProdutoAlunoOutraUnidade = permiteVendaProdutoAlunoOutraUnidade;
    }

    public boolean isExibirTipoDocumentoTelaVendasOnline() {
        return exibirTipoDocumentoTelaVendasOnline;
    }

    public void setExibirTipoDocumentoTelaVendasOnline(boolean exibirTipoDocumentoTelaVendasOnline) {
        this.exibirTipoDocumentoTelaVendasOnline = exibirTipoDocumentoTelaVendasOnline;
    }

    public String getCamposAdicionaisProduto() {
        return camposAdicionaisProduto == null ? "" : camposAdicionaisProduto;
    }

    public void setCamposAdicionaisProduto(String camposAdicionaisProduto) {
        this.camposAdicionaisProduto = camposAdicionaisProduto;
    }

    public boolean isHabilitarAgendamentoAulaExperimentalLinkVisitante() {
        return habilitarAgendamentoAulaExperimentalLinkVisitante;
    }

    public void setHabilitarAgendamentoAulaExperimentalLinkVisitante(boolean habilitarAgendamentoAulaExperimentalLinkVisitante) {
        this.habilitarAgendamentoAulaExperimentalLinkVisitante = habilitarAgendamentoAulaExperimentalLinkVisitante;
    }

    public boolean isEnviarEmailUsuarioMovelAutomaticamente() {
        return enviarEmailUsuarioMovelAutomaticamente;
    }

    public void setEnviarEmailUsuarioMovelAutomaticamente(boolean enviarEmailUsuarioMovelAutomaticamente) {
        this.enviarEmailUsuarioMovelAutomaticamente = enviarEmailUsuarioMovelAutomaticamente;
    }

    public boolean isUsarFormaPagamentoPlanoProduto() {
        return usarFormaPagamentoPlanoProduto;
    }

    public void setUsarFormaPagamentoPlanoProduto(boolean usarFormaPagamentoPlanoProduto) {
        this.usarFormaPagamentoPlanoProduto = usarFormaPagamentoPlanoProduto;
    }

    public boolean isHabilitarPreCadastro() {
        return habilitarPreCadastro;
    }

    public void setHabilitarPreCadastro(boolean habilitarPreCadastro) {
        this.habilitarPreCadastro = habilitarPreCadastro;
    }

    public boolean isConfigSescHabilitada() {
        return configSescHabilitada;
    }

    public void setConfigSescHabilitada(boolean configSescHabilitada) {
        this.configSescHabilitada = configSescHabilitada;
    }

    public boolean isPrimeiraCobrancaPixEGuardarCartao() {
        return primeiraCobrancaPixEGuardarCartao;
    }

    public void setPrimeiraCobrancaPixEGuardarCartao(boolean primeiraCobrancaPixEGuardarCartao) {
        this.primeiraCobrancaPixEGuardarCartao = primeiraCobrancaPixEGuardarCartao;
    }

    public boolean isExibirBotaoAgendaSobreBanner() {
        return exibirBotaoAgendaSobreBanner;
    }

    public void setExibirBotaoAgendaSobreBanner(boolean exibirBotaoAgendaSobreBanner) {
        this.exibirBotaoAgendaSobreBanner = exibirBotaoAgendaSobreBanner;
    }

    public boolean isModalidadesIniciarSelecionadasContratoTurma() {
        return modalidadesIniciarSelecionadasContratoTurma;
    }

    public void setModalidadesIniciarSelecionadasContratoTurma(boolean modalidadesIniciarSelecionadasContratoTurma) {
        this.modalidadesIniciarSelecionadasContratoTurma = modalidadesIniciarSelecionadasContratoTurma;
    }

    public boolean isAtivarLinksGooglePlayEAppleStore() {
        return ativarLinksGooglePlayEAppleStore;
    }

    public void setAtivarLinksGooglePlayEAppleStore(boolean ativarLinksGooglePlayEAppleStore) {
        this.ativarLinksGooglePlayEAppleStore = ativarLinksGooglePlayEAppleStore;
    }

    public String getUrlLinkGooglePlay() {
        return urlLinkGooglePlay;
    }

    public void setUrlLinkGooglePlay(String urlLinkGooglePlay) {
        this.urlLinkGooglePlay = urlLinkGooglePlay;
    }

    public String getUrlLinkAppleStore() {
        return urlLinkAppleStore;
    }

    public void setUrlLinkAppleStore(String urlLinkAppleStore) {
        this.urlLinkAppleStore = urlLinkAppleStore;
    }

    public boolean isPermiteProsseguirMesmoCpfCadastroVisitante() {
        return permiteProsseguirMesmoCpfCadastroVisitante;
    }

    public void setPermiteProsseguirMesmoCpfCadastroVisitante(boolean permiteProsseguirMesmoCpfCadastroVisitante) {
        this.permiteProsseguirMesmoCpfCadastroVisitante = permiteProsseguirMesmoCpfCadastroVisitante;
    }

    public String getCamposAdicionaisPlanoFlow() {
        return camposAdicionaisPlanoFlow == null ? "" : camposAdicionaisPlanoFlow;
    }

    public void setCamposAdicionaisPlanoFlow(String camposAdicionaisPlanoFlow) {
        this.camposAdicionaisPlanoFlow = camposAdicionaisPlanoFlow;
    }

    public String getCamposAdicionaisProdutoFlow() {
        return camposAdicionaisProdutoFlow == null ? "" : camposAdicionaisProdutoFlow;
    }

    public void setCamposAdicionaisProdutoFlow(String camposAdicionaisProdutoFlow) {
        this.camposAdicionaisProdutoFlow = camposAdicionaisProdutoFlow;
    }

    public boolean isExibeDataUtilizacaoDiaria() {
        return exibeDataUtilizacaoDiaria;
    }

    public void setExibeDataUtilizacaoDiaria(boolean exibeDataUtilizacaoDiaria) {
        this.exibeDataUtilizacaoDiaria = exibeDataUtilizacaoDiaria;
    }

    public String getGoogleTagIdHotsite() {
        return googleTagIdHotsite;
    }

    public void setGoogleTagIdHotsite(String googleTagIdHotsite) {
        this.googleTagIdHotsite = googleTagIdHotsite;
    }

    // Integração Igopass - controle de trava magnetica para armários, geladeiras e etc
    public String getIgopassUrl() {
        return igopassUrl;
    }

    public void setIgopassUrl(String igopassUrl) {
        this.igopassUrl = igopassUrl;
    }

    public String getIgopassQrCodeUrl() {
        return igopassQrCodeUrl;
    }

    public void setIgopassQrCodeUrl(String igopassQrCodeUrl) {
        this.igopassQrCodeUrl = igopassQrCodeUrl;
    }

    public String getIgopassEstabelecimento() {
        return igopassEstabelecimento;
    }

    public void setIgopassEstabelecimento(String igopassEstabelecimento) {
        this.igopassEstabelecimento = igopassEstabelecimento;
    }

    public String getIgopassUsuario() {
        return igopassUsuario;
    }

    public void setIgopassUsuario(String igopassUsuario) {
        this.igopassUsuario = igopassUsuario;
    }
}
