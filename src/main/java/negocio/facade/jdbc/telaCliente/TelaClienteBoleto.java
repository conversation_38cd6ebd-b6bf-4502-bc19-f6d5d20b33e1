package negocio.facade.jdbc.telaCliente;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.financeiro.BoletoBancarioControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AuxiliarBoletoBancarioTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.ArquivoLayoutRemessaEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Endereco;
import negocio.facade.jdbc.basico.Estado;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.facade.jdbc.financeiro.RemessaItemMovParcela;
import org.jboleto.JBoleto;
import org.jboleto.JBoletoBean;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.impl.boleto.BancoEnum;
import servicos.impl.boleto.sicredi.LayoutRemessaSicrediBoleto;
import servicos.impl.dcc.base.RemessaService;
import servicos.integracao.pjbank.recebimento.BoletosManager;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.faces.context.FacesContext;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.sql.Connection;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static controle.arquitetura.SuperControle.getConfiguracaoSMTPNoReply;

public class TelaClienteBoleto {

    public TelaClienteBoleto() {
    }

    private String getUrlAplicacaoHttps(String key) {
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(key);
            return clientDiscoveryDataDTO.getServiceUrls().getZwUrl();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public void removerParcelaBoleto(String chave, Integer remessa_item, Integer usuario,
                                     HttpServletRequest request, Connection con) throws Exception {
        RemessaItem remessaItemDAO;
        RemessaItemMovParcela remessaItemMovParcelaDAO;
        Usuario usuarioDAO;
        Cliente clienteDAO;
        try {
            remessaItemDAO = new RemessaItem(con);
            remessaItemMovParcelaDAO = new RemessaItemMovParcela(con);
            usuarioDAO = new Usuario(con);
            clienteDAO = new Cliente(con);

            if (UteisValidacao.emptyNumber(remessa_item)) {
                throw new Exception("Código remessa item não informado");
            }
            if (UteisValidacao.emptyNumber(usuario)) {
                throw new Exception("Usuário não informado");
            }

            RemessaItemVO remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(remessa_item, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(remessaItemVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            remessaItemMovParcelaDAO.removerParcelaBoleto(clienteVO, remessaItemVO, usuarioVO);
        } finally {
            remessaItemDAO = null;
            remessaItemMovParcelaDAO = null;
            usuarioDAO = null;
            clienteDAO = null;
        }
    }

    public String detalhes(Integer boleto, Integer usuario, Connection con) throws Exception {
        Boleto boletoDAO;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            boletoDAO = new Boleto(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            if (UteisValidacao.emptyNumber(boleto)) {
                throw new Exception("Boleto não informado");
            }

            BoletoVO boletoVO = boletoDAO.consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            if (!(boletoVO.isPJBank() && boletoVO.isApresentarDetalhes())) {
                throw new Exception("Boleto não pode verificar detalhes");
            }

            boletoVO.setConvenioCobrancaVO(convenioCobrancaDAO.consultarPorCodigo(boletoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).get(0));

            if (boletoVO.getConvenioCobrancaVO().isBoletoPjBank()) {
                return boletoDAO.obterDetalheBoletoPJBank(boletoVO.getCodigo());
            } else {
                throw new Exception("Erro consultar detalhes");
            }
        } finally {
            boletoDAO = null;
            convenioCobrancaDAO = null;
        }
    }

    public String sincronizar(Integer boleto, Integer usuario, Connection con) throws Exception {
        Usuario usuarioDAO;
        Boleto boletoDAO;
        try {
            usuarioDAO = new Usuario(con);
            boletoDAO = new Boleto(con);

            if (UteisValidacao.emptyNumber(boleto)) {
                throw new Exception("Boleto não informado");
            }
            if (UteisValidacao.emptyNumber(usuario)) {
                throw new Exception("Usuário não informado");
            }

            BoletoVO boletoVO = boletoDAO.consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            if (!boletoVO.isPodeSincronizar()) {
                throw new Exception("Boleto não pode ser sincronizado");
            }
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            return boletoDAO.sincronizarBoleto(boletoVO, usuarioVO, "SincronizarBoleto - Tela Cliente - PactoPay");
        } catch (Exception e) {
            if (!e.getMessage().contains("Não foi encontrado boleto com o Id Único")) {
                throw e;
            }
            return "";
        } finally {
            usuarioDAO = null;
            boletoDAO = null;
        }
    }

    public void cancelar(Integer boleto, Integer usuario, Connection con) throws Exception {
        Usuario usuarioDAO;
        Boleto boletoDAO;
        try {
            usuarioDAO = new Usuario(con);
            boletoDAO = new Boleto(con);

            if (UteisValidacao.emptyNumber(boleto)) {
                throw new Exception("Boleto não informado");
            }
            if (UteisValidacao.emptyNumber(usuario)) {
                throw new Exception("Usuário não informado");
            }

            BoletoVO boletoVO = boletoDAO.consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            if (!boletoVO.isPodeCancelar()) {
                throw new Exception("Boleto não pode ser cancelado");
            }
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            boletoDAO.cancelarBoleto(boletoVO, usuarioVO, "CancelarBoleto - Tela Cliente - PactoPay");
        } catch (Exception e) {
            if (!e.getMessage().contains("Não foi encontrado boleto com o Id Único")) {
                throw e;
            }
        } finally {
            usuarioDAO = null;
            boletoDAO = null;
        }
    }

    public void enviarEmailBoleto(String chave, Integer boleto, Integer usuario,
                                  String emailDestino, HttpServletRequest request, Connection con) throws Exception {
        Usuario usuarioDAO;
        Boleto boletoDAO;
        Email emailDAO;
        Pessoa pessoaDAO;
        try {
            usuarioDAO = new Usuario(con);
            boletoDAO = new Boleto(con);
            emailDAO = new Email(con);
            pessoaDAO = new Pessoa(con);

            if (UteisValidacao.emptyNumber(boleto)) {
                throw new Exception("Boleto não informado");
            }
//            if (UteisValidacao.emptyNumber(usuario)) {
//                throw new Exception("Usuário não informado");
//            }

            BoletoVO boletoVO = boletoDAO.consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            if (!boletoVO.isPodeEnviarEmail()) {
                throw new Exception("Não é possível enviar o email desse boleto");
            }

            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(boletoVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            boletoVO.setPessoaVO(pessoaVO);
//            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            List<EmailVO> emailsEnviar = new ArrayList<>();
            if (UteisValidacao.emptyString(emailDestino)) {
                emailsEnviar = emailDAO.consultarEmails(boletoVO.getPessoaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyList(emailsEnviar)) {
                    throw new Exception(boletoVO.getPessoaVO().getNome() + " não tem e-mail cadastrado.");
                }
            } else {
                EmailVO emailVO = new EmailVO();
                emailVO.setEmail(emailDestino);
                emailsEnviar.add(emailVO);
            }

            String[] emails = new String[emailsEnviar.size()];
            int i = 0;
            for (Object objEmail : emailsEnviar) {
                EmailVO emailVO = (EmailVO) objEmail;
                emails[i] = emailVO.getEmail();
                i++;
            }

            boletoDAO.enviarEmailBoleto(chave, boletoVO, emails, true, false);
        } finally {
            usuarioDAO = null;
            boletoDAO = null;
        }
    }

    public void enviarEmailRemessaItem(String chave, Integer remessa_item, Integer usuario,
                                       String emailDestino, HttpServletRequest request, Connection con) throws Exception {
        RemessaItem remessaItemDAO;
        Endereco enderecoDAO;
        Cidade cidadeDAO;
        Estado estadoDAO;
        Empresa empresaDAO;
        ConvenioCobranca convenioCobrancaDAO;
        Pessoa pessoaDAO;
        Usuario usuarioDAO;
        try {
            remessaItemDAO = new RemessaItem(con);
            enderecoDAO = new Endereco(con);
            cidadeDAO = new Cidade(con);
            estadoDAO = new Estado(con);
            empresaDAO = new Empresa(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            pessoaDAO = new Pessoa(con);
            usuarioDAO = new Usuario(con);

            if (UteisValidacao.emptyNumber(remessa_item)) {
                throw new Exception("Código remessa item não informado");
            }
            if (UteisValidacao.emptyNumber(usuario)) {
                throw new Exception("Usuário não informado");
            }

            RemessaItemVO remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(remessa_item, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            remessaItemVO.getRemessa().setEmpresaVO(empresaDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(remessaItemVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //verificar se o cliente tem email válido
            if (UteisValidacao.emptyList(pessoaVO.getEmailVOs())) {
                throw new Exception("Não foi possível enviar o contrato pois o cliente não possui um email válido.");
            }


            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            remessaItemVO.getRemessa().setConvenioCobranca(convenioCobrancaDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getConvenioCobranca().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

            remessaItemVO.getPessoa().setEnderecoVOs(enderecoDAO.consultarPorCodigoPessoa(remessaItemVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemVO.getPessoa().setCidade(cidadeDAO.consultarPorCodigoExato(remessaItemVO.getPessoa().getCidade().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemVO.getPessoa().setEstadoVO(estadoDAO.consultarPorCodigo(remessaItemVO.getPessoa().getEstadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemVO.setMesesAbertos(remessaItemDAO.consultarMesesEmAberto(remessaItemVO));
            remessaItemVO.setPorcentagemDescontoBoleto(remessaItemVO.getRemessa().getConvenioCobranca().getDescontoBoleto());
            RemessaService service = new RemessaService(con);
            remessaItemVO.setIdentificadorEmpresaFinanceiro("");
            boolean layout240 = (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                    remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE));
            JBoletoBean boletoBean = service.gerarBoletoCobranca(remessaItemVO);
            processarNossoNumero(remessaItemVO, boletoBean);
            JBoleto boleto = new JBoleto(null, boletoBean, remessaItemVO.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco(), layout240);
            List<JBoleto> boletos = new ArrayList<JBoleto>();
            boletos.add(boleto);

            imprimirRelatorio(remessaItemVO, boletos, request, usuarioVO, con);
            String caminhoArquivo = (obterCaminhoWebAplicacao(request) + "/servlet-relatorio/" + request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString());

            File arquivo = new File(caminhoArquivo);
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
            UteisEmail email = new UteisEmail();
            email.novo("BOLETO", configuracaoSistemaCRMVO);
            email.setRemetente(usuarioVO);
            email = email.addAnexo(request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString(), arquivo);

            if (!UteisValidacao.emptyString(emailDestino)) {
                EmailVO emailVO = new EmailVO();
                emailVO.setEmail(emailDestino);
                pessoaVO.setEmailVOs(new ArrayList());
                pessoaVO.getEmailVOs().add(emailVO);
            }

            String[] emails = new String[pessoaVO.getEmailVOs().size()];
            int i = 0;
            for (Object obj : pessoaVO.getEmailVOs()) {
                EmailVO emailVO = (EmailVO) obj;
                emails[i] = emailVO.getEmail();
                i++;
            }

            String urlArquivo = (getUrlAplicacaoHttps(chave) + "/servlet-relatorio/" + request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString());
            String mensagem = gerarHTMLModeloPadraoBoleto(boletos, remessaItemVO, chave, caminhoArquivo, urlArquivo);
            email.enviarEmailN(emails, mensagem, "BOLETO", remessaItemVO.getRemessa().getEmpresaVO().getNome());
            incluirHistoricoContato(pessoaVO, usuarioVO, con);
        } finally {
            remessaItemDAO = null;
            enderecoDAO = null;
            cidadeDAO = null;
            estadoDAO = null;
            empresaDAO = null;
            convenioCobrancaDAO = null;
            pessoaDAO = null;
            usuarioDAO = null;
        }
    }

    private void incluirHistoricoContato(PessoaVO pessoaVO, UsuarioVO usuarioVO, Connection con) {
        Cliente clienteDAO;
        HistoricoContato historicoContatoDAO;
        try {
            clienteDAO = new Cliente(con);
            historicoContatoDAO = new HistoricoContato(con);

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                throw new Exception("Histórico de Envio de E-mail não gravado, pois cliente não encontrado.");
            }

            HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
            historicoContatoVO.setDia(Calendario.hoje());
            historicoContatoVO.setClienteVO(clienteVO);
            historicoContatoVO.setResponsavelCadastro(usuarioVO);
            historicoContatoVO.setResultado("Envio Email");
            historicoContatoVO.setTipoContato("EM");

            StringBuilder sb = new StringBuilder();
            sb.append("<p>Título: BOLETO</p> ");
            sb.append("<p>Data de envio: ").append(Calendario.getDataAplicandoFormatacao(historicoContatoVO.getDia(), "dd/MM/yyyy")).append("</p> ");
            sb.append("<p>Remetente: ").append(historicoContatoVO.getResponsavelCadastro()).append("</p> ");
            sb.append("<p>------------------------------------------------------</p> ");
            sb.append("<p>Mensagem: UTILIZA ENVIO DE BOLETO PADRÃO</p>");
            historicoContatoVO.setObservacao(sb.toString());
            historicoContatoDAO.incluirSemCommitSemAtualizarSintetico(historicoContatoVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            clienteDAO = null;
            historicoContatoDAO = null;
        }
    }

    public String imprimirRemessaItem(String chave, Integer remessa_item, HttpServletRequest request, Connection con) throws Exception {
        RemessaItem remessaItemDAO;
        Endereco enderecoDAO;
        Cidade cidadeDAO;
        Estado estadoDAO;
        Empresa empresaDAO;
        ConvenioCobranca convenioCobrancaDAO;

        try {
            remessaItemDAO = new RemessaItem(con);
            enderecoDAO = new Endereco(con);
            cidadeDAO = new Cidade(con);
            estadoDAO = new Estado(con);
            empresaDAO = new Empresa(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            if (UteisValidacao.emptyNumber(remessa_item)) {
                throw new Exception("Código remessa item não informado");
            }

            // Obtenha os dados do remessa_item
            RemessaItemVO remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(remessa_item, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            remessaItemVO.getRemessa().setEmpresaVO(empresaDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemVO.getRemessa().setConvenioCobranca(convenioCobrancaDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getConvenioCobranca().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

            // Informações da pessoa
            remessaItemVO.getPessoa().setEnderecoVOs(enderecoDAO.consultarPorCodigoPessoa(remessaItemVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemVO.getPessoa().setCidade(cidadeDAO.consultarPorCodigoExato(remessaItemVO.getPessoa().getCidade().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemVO.getPessoa().setEstadoVO(estadoDAO.consultarPorCodigo(remessaItemVO.getPessoa().getEstadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            // Consultar meses abertos e desconto do boleto
            remessaItemVO.setMesesAbertos(remessaItemDAO.consultarMesesEmAberto(remessaItemVO));
            remessaItemVO.setPorcentagemDescontoBoleto(remessaItemVO.getRemessa().getConvenioCobranca().getDescontoBoleto());

            // Geração do boleto
            RemessaService service = new RemessaService(con);
            remessaItemVO.setIdentificadorEmpresaFinanceiro("");
            boolean layout240 = (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                    remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE));
            JBoletoBean boletoBean = service.gerarBoletoCobranca(remessaItemVO);
            processarNossoNumero(remessaItemVO, boletoBean);
            JBoleto boleto = new JBoleto(null, boletoBean, remessaItemVO.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco(), layout240);

            // Chamar a função de impressão
            List<JBoleto> boletos = new ArrayList<JBoleto>();
            boletos.add(boleto);
            imprimirRelatorio(remessaItemVO, boletos, request, remessaItemVO.getUsuarioVO(), con);

            // Retornar a URL do PDF gerado
            return getUrlAplicacaoHttps(chave) + "/servlet-relatorio/" + request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
        } finally {
            // Limpeza
            remessaItemDAO = null;
            enderecoDAO = null;
            cidadeDAO = null;
            estadoDAO = null;
            empresaDAO = null;
            convenioCobrancaDAO = null;
        }
    }


    private void processarNossoNumero(RemessaItemVO remessaItemVO, JBoletoBean boletoBean) {
        if (remessaItemVO.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO) &&
                remessaItemVO.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCODOBRASIL.getCodigo())) {
            return;
        }
        if (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICREDI) ||
                remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.CARNE_BANCO_SICREDI)) {
            boletoBean.setNossoNumero(LayoutRemessaSicrediBoleto.gerarNossoNumero(remessaItemVO, remessaItemVO.getIdentificador(), 2, Integer.parseInt(boletoBean.getAgencia()),
                    Integer.parseInt(boletoBean.getDvAgencia()), Integer.parseInt(boletoBean.getContaCorrente())));
        } else if (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.CARNE_BANCO_DO_BRASIL)) {
            String formataNossoNumero = String.format("%010d", remessaItemVO.getIdentificador());
            boletoBean.setNossoNumero(formataNossoNumero);
        } else if (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE)) {
            boletoBean.setNossoNumero(remessaItemVO.getIdentificador().toString());
        } else if (!remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_CAIXA)
                && !remessaItemVO.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SANTANDER.getCodigo())) {
            boletoBean.setNossoNumero(remessaItemVO.getCodigo().toString());
        }
    }

    private void imprimirRelatorio(RemessaItemVO remessaItemVO, List<JBoleto> boletos,
                                   HttpServletRequest request, UsuarioVO usuarioVO, Connection con) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        prepareParams(remessaItemVO, boletos, params, usuarioVO, con);
        new SuperControleRelatorio().apresentarRelatorioObjetosComRequest(params, request, con);
    }

    private void prepareParams(RemessaItemVO remessaItemVO, List<JBoleto> boletos,
                               Map<String, Object> params, UsuarioVO usuarioVO, Connection con) throws Exception {
        EmpresaVO empre = remessaItemVO.getRemessa().getEmpresaVO();

        params.put("nomeRelatorio", "Boleto");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", "Boleto");
        params.put("nomeDesignIReport", getDesignIReportRelatorio(remessaItemVO.getRemessa().getConvenioCobranca()));
        params.put("nomeDesignSubReport", getDesignSubReportRelatorio(remessaItemVO.getRemessa().getConvenioCobranca()));
        params.put("nomeUsuario", usuarioVO != null ? usuarioVO.getNome() : "API");

        List<AuxiliarBoletoBancarioTO> auxiliarBoletoBancarioTOs = new ArrayList<AuxiliarBoletoBancarioTO>();
        for (JBoleto boleto : boletos) {
            AuxiliarBoletoBancarioTO auxiliar = new AuxiliarBoletoBancarioTO();
            auxiliar.setBoleto(boleto);
            auxiliarBoletoBancarioTOs.add(auxiliar);
        }

        params.put("listaObjetos", auxiliarBoletoBancarioTOs);

        StringBuilder sb = getEnderecoEmpresa(empre);

        params.put("enderecoEmpresa", sb.toString());
        params.put("cnpjEmpresa", empre.getCNPJ());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
        Empresa empresaDAO = new Empresa(con);
        byte[] propaganda = empresaDAO.obterFotoPadrao(DAO.resolveKeyFromConnection(con), empre.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO);
        empresaDAO = null;

        InputStream fs = null;
        if (propaganda != null)
            fs = new ByteArrayInputStream(propaganda);
        params.put("propaganda", fs);
    }

    public String getDesignIReportRelatorio(ConvenioCobrancaVO convenioCobrancaVO) throws ConsistirException {
        String nomeArquivo = convenioCobrancaVO.getTipoRemessa().getArquivoLayoutRemessa().getReport();
        if (UteisValidacao.emptyString(nomeArquivo)) {
            throw new ConsistirException("Configuração do tipo de remessa inválido");
        }
        return "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "boletos" + File.separator + nomeArquivo + ".jrxml";
    }

    public String getDesignSubReportRelatorio(ConvenioCobrancaVO convenioCobrancaVO) {
        return convenioCobrancaVO.getTipoRemessa().getArquivoLayoutRemessa().getSubReport();
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "boletos" + File.separator);
    }

    public StringBuilder getEnderecoEmpresa(EmpresaVO empre) {
        StringBuilder sb = new StringBuilder();
        boolean adicionouEndereco = false;
        if (!UteisValidacao.emptyString(empre.getEndereco())) {
            sb.append(empre.getEndereco());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getNumero())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getNumero());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getSetor())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getSetor());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getCidade_Apresentar())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getCidade_Apresentar());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getEstado().getSigla())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getEstado().getSigla());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getCEP())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getCEP());
        }
        return sb;
    }

    public String gerarHTMLModeloPadraoBoleto(List<JBoleto> boletos, RemessaItemVO remessaItemVO, String chave, String caminhoArquivo, String linkBoleto) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailBoletoOnline.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath(), "UTF-8");

        ConvenioCobrancaVO cobrancaVO = remessaItemVO.getRemessa().getConvenioCobranca();

        String empresaNome = cobrancaVO.getEmpresa().getNome().toUpperCase();
        String empresaUrlLogo = getUrlLogoEmpresa(remessaItemVO.getRemessa().getEmpresaVO(), chave);
        String empresaEndereco = cobrancaVO.getEmpresa().getEndereco();
        String empresaTelefone = cobrancaVO.getEmpresa().getTelComercial1();
        String empresaEmail = cobrancaVO.getEmpresa().getEmail();
        String mesReferencia = "";
        for (int i = 0; i < boletos.size(); i++) {
            if (i == 0) {
                mesReferencia = boletos.get(i).getBoleto().getInstrucao3();
            } else {
                mesReferencia = mesReferencia + ", " + boletos.get(i).getBoleto().getInstrucao3();
            }
        }
        String dataVencimento = "";
        for (int i = 0; i < boletos.size(); i++) {
            if (i == 0) {
                dataVencimento = boletos.get(i).getBoleto().getDataVencimento();
            } else {
                dataVencimento = dataVencimento + ", " + boletos.get(i).getBoleto().getDataVencimento();
            }
        }
        Double soma = 0.0;
        for (int i = 0; i < boletos.size(); i++) {
            String valor = boletos.get(i).getBoleto().getValorBoleto();
            soma = soma + Double.parseDouble(valor.replaceAll(",", "."));
        }
        DecimalFormat df = new DecimalFormat("0.00");
        df.setMaximumFractionDigits(2);
        String valorBoleto = df.format(soma);
        String nomeAluno = boletos.get(0).getBoleto().getNomeSacado();
        String linhaDigitavel = "";
        for (int i = 0; i < boletos.size(); i++) {
            linhaDigitavel = linhaDigitavel + boletos.get(i).getBoleto().getLinhaDigitavel() + " <br>";
        }

        boolean enviarBoletoEmAnexo = !UteisValidacao.emptyString(caminhoArquivo);
        boolean enviarLinkBoleto = !UteisValidacao.emptyString(linkBoleto);

        String aux = texto.toString()
                .replaceAll("#ACADEMIA_NOME#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaNome))
                .replaceAll("#ACADEMIA_URL_LOGO#", empresaUrlLogo)
                .replaceAll("#ACADEMIA_ENDERECO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEndereco))
                .replaceAll("#ACADEMIA_TELEFONE#", empresaTelefone)
                .replaceAll("#ACADEMIA_EMAIL#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEmail))
                .replaceAll("#NOME_ALUNO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeAluno))
                .replaceAll("#MES_REFERENCIA#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(mesReferencia))
                .replaceAll("#DATA_VENCIMENTO#", dataVencimento)
                .replaceAll("#LINHA_DIGITAVEL#", linhaDigitavel)
                .replaceAll("#VALOR_BOLETO#", valorBoleto)
                .replaceAll("#LINK_BOLETO#", linkBoleto == null ? "" : linkBoleto)
                .replaceAll("#LINK#", enviarLinkBoleto ? "block" : "none")
                .replaceAll("#ANEXO#", enviarBoletoEmAnexo ? "block" : "none");
        return aux;
    }

    public String getUrlLogoEmpresa(EmpresaVO empresaVO, String chave) {
        try {
            String genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, empresaVO.getCodigo().toString());
            if (UteisValidacao.emptyString(genKey)) {
                genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, empresaVO.getCodigo().toString());
            }
            if (UteisValidacao.emptyString(genKey)) {
                genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA, empresaVO.getCodigo().toString());
            }
            return Uteis.getPaintFotoDaNuvem(genKey);
        } catch (Exception ignored) {
            return "";
        }
    }

    public String imprimirBoletos(List<BoletoVO> boletosOnline,
                                  List<RemessaItemVO> boletosRemessaItem, Connection con, HttpServletRequest request) throws Exception {
        Boleto boletoDAO;
        ConvenioCobranca convenioCobrancaDAO;
        RemessaItem remessaItemDAO;
        try {
            boletoDAO = new Boleto(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            remessaItemDAO = new RemessaItem(con);
            String key = DAO.resolveKeyFromConnection(con);

            if (UteisValidacao.emptyList(boletosOnline) &&
                    UteisValidacao.emptyList(boletosRemessaItem)) {
                throw new Exception("Nenhum boleto informado");
            }

            if (!UteisValidacao.emptyList(boletosOnline)) {
                List<Integer> listaBoletoOnline = new ArrayList<>();
                if (!UteisValidacao.emptyList(boletosOnline)) {
                    for (BoletoVO boletoVO : boletosOnline) {
                        listaBoletoOnline.add(boletoVO.getCodigo());
                    }
                }

                List<BoletoVO> boletosSelecionados = boletoDAO.consultarPorCodigos(listaBoletoOnline, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                List<BoletoVO> boletosSelecionadosImp = new ArrayList<>();
                boolean todosPjBank = true;
                for (BoletoVO boletoVO : boletosSelecionados) {
                    if (boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                        if (boletoVO.isApresentarImprimirBoleto()) {
                            boletosSelecionadosImp.add(boletoVO);
                        }
                    } else {
                        todosPjBank = false;
                    }
                }

                if (!todosPjBank) {
                    throw new Exception("Boleto selecionado não é PjBank");
                }

                if (UteisValidacao.emptyList(boletosSelecionadosImp)) {
                    throw new Exception("Nenhum boleto válido para impressão.");
                }

                ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(boletosSelecionadosImp.get(0).getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                LinkedHashSet<String> pedidos = BoletoVO.obterListaImpressaoBoleto(boletosSelecionadosImp);
                BoletosManager boletosManager = new BoletosManager(convenioCobrancaVO.getCredencialPJBank(), convenioCobrancaVO.getChavePJBank(), convenioCobrancaVO);
                return boletosManager.getByIds(pedidos);
            }

            if (!UteisValidacao.emptyList(boletosRemessaItem)) {
                StringBuilder codRemessaItem = new StringBuilder();
                if (!UteisValidacao.emptyList(boletosRemessaItem)) {
                    for (RemessaItemVO itemVO : boletosRemessaItem) {
                        codRemessaItem.append("," + itemVO.getCodigo());
                    }
                    codRemessaItem = new StringBuilder(codRemessaItem.toString().replaceFirst(",",""));
                }
                List<Integer> listaDeCodRemessaItems = Arrays.stream(codRemessaItem.toString().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());

                //PAY-1208 - Implementada tratativa para imprimir vários boletos em um mesmo PDF
                if (!UteisValidacao.emptyList(listaDeCodRemessaItems)) {
                    if(listaDeCodRemessaItems.size() > 1) {
                        EmpresaVO empresaVO = new EmpresaVO();
                        ConvenioCobrancaVO convenioCobrancaVO = new ConvenioCobrancaVO();
                        List<RemessaItemVO> lstRemessaItems = new ArrayList<>();
                        for(Integer codRemItem : listaDeCodRemessaItems) {
                            RemessaItemVO item = remessaItemDAO.consultarPorChavePrimaria(codRemItem, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                            if(UteisValidacao.emptyNumber(empresaVO.getCodigo()) && item.getRemessa() != null) {
                                empresaVO.setCodigo(item.getRemessa().getEmpresa());
                            }
                            if(UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo()) && item.getRemessa() != null && item.getRemessa().getConvenioCobranca() != null) {
                                convenioCobrancaVO = item.getRemessa().getConvenioCobranca();
                            }

                            if(item != null && !UteisValidacao.emptyNumber(item.getCodigo())) {
                                lstRemessaItems.add(item);
                            }
                        }

                        if(!UteisValidacao.emptyList(lstRemessaItems)) {
                            //Instancia a Classe que possuí a lógica de gerar relatório com múltiplos boletos
                            BoletoBancarioControle boletoBancarioControle = new BoletoBancarioControle();
                            boletoBancarioControle.setEmpresa(empresaVO);
                            boletoBancarioControle.setCobrancaVO(convenioCobrancaVO);
                            boletoBancarioControle.imprimirBoletosRemessaPerfilAluno(lstRemessaItems, key, con, request);

                            return getUrlAplicacaoHttps(key) + "/servlet-relatorio/" + request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
                        }

                    }else{
                        for (Integer codRemItem : listaDeCodRemessaItems) {
                            return imprimirRemessaItem(key, codRemItem, request, con);
                        }
                    }
                }
            }

            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String imprimirBoletosOnlineNaoRegistrado(Integer codigoBoleto,
                                                     Integer codUsuario, Connection con) throws Exception {
        Boleto boletoDAO;
        Usuario usuarioDAO;
        try {
            boletoDAO = new Boleto(con);
            usuarioDAO = new Usuario(con);

            if (UteisValidacao.emptyNumber(codigoBoleto)) {
                throw new Exception("Nenhum boleto informado");
            }

            List<Integer> listaBoletoOnline = new ArrayList<>();
            listaBoletoOnline.add(codigoBoleto);

            List<BoletoVO> boletosSelecionados = boletoDAO.consultarPorCodigos(listaBoletoOnline, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            List<BoletoVO> boletosSelecionadosImp = new ArrayList<>();

            UsuarioVO usuarioOperacao = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (BoletoVO boletoVO: boletosSelecionados) {
                //Lista de Parcela Vazia, mas lista MovParcela preenchida, pegar parcelas da Lista de MovParcela
                List<MovParcelaVO> listaParcelasBoleto = new ArrayList<>();
                if (UteisValidacao.emptyList(boletoVO.getListaParcelas()) && !UteisValidacao.emptyList(boletoVO.getListaBoletoMovParcela())) {
                    for (BoletoMovParcelaVO boletoMovParcelaVO : boletoVO.getListaBoletoMovParcela()) {
                        listaParcelasBoleto.add(boletoMovParcelaVO.getMovParcelaVO());
                    }
                }

                if (!UteisValidacao.emptyList(listaParcelasBoleto)) {
                    if (listaParcelasBoleto.size() == 1) {
                        List<BoletoVO> listaBoletosRegistrados = boletoDAO.gerarBoletoPorParcela(boletoVO.getPessoaVO(),
                                boletoVO.getConvenioCobrancaVO(), listaParcelasBoleto,
                                usuarioOperacao, OrigemCobrancaEnum.ZW_MANUAL_TELA_ALUNO, false, true);
                        if (!UteisValidacao.emptyList(listaBoletosRegistrados)) {
                            boletosSelecionadosImp.add(listaBoletosRegistrados.get(0));
                        }
                    } else {
                        boletosSelecionadosImp.add(boletoDAO.gerarBoleto(boletoVO.getPessoaVO(), boletoVO.getConvenioCobrancaVO(), listaParcelasBoleto, boletoVO.getDataVencimento(),
                                usuarioOperacao, OrigemCobrancaEnum.ZW_MANUAL_TELA_ALUNO, false, true));
                    }
                }
            }

            if (UteisValidacao.emptyList(boletosSelecionadosImp)) {
                return "Nenhum boleto válido para impressão.";
            }

            String urlBoleto = "";
            for (BoletoVO boletoVO: boletosSelecionadosImp) {
                if (!UteisValidacao.emptyString(boletoVO.getLinkBoleto())) {
                    urlBoleto = boletoVO.getLinkBoleto();
                }
            }
            return urlBoleto;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoDAO = null;
            usuarioDAO = null;
        }
    }

    public String obterCaminhoWebAplicacao(HttpServletRequest request) throws Exception {
        return new File(request.getSession().getServletContext().getRealPath("")).getAbsolutePath();
    }

    public String cancelarBoletos(List<BoletoVO> boletosOnline,
                                  List<RemessaItemVO> boletosRemessaItem,
                                  Integer codUsuario, Connection con) throws Exception {
        Boleto boletoDAO;
        Usuario usuarioDAO;
        try {
            boletoDAO = new Boleto(con);
            usuarioDAO = new Usuario(con);

            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            if (UteisValidacao.emptyList(boletosOnline) &&
                    UteisValidacao.emptyList(boletosRemessaItem)) {
                throw new Exception("Nenhum boleto informado");
            }

            List<Integer> listaBoletoOnline = new ArrayList<>();
            if (!UteisValidacao.emptyList(boletosOnline)) {
                for (BoletoVO boletoVO : boletosOnline) {
                    listaBoletoOnline.add(boletoVO.getCodigo());
                }
            }

            StringBuilder codRemessaItem = new StringBuilder();
            if (!UteisValidacao.emptyList(boletosRemessaItem)) {
                for (RemessaItemVO itemVO : boletosRemessaItem) {
                    codRemessaItem.append("," + itemVO.getCodigo());
                }
                codRemessaItem = new StringBuilder(codRemessaItem.toString().replaceFirst(",",""));
            }

            if(!UteisValidacao.emptyString(codRemessaItem.toString())) {
                throw new Exception("Os boletos: " + codRemessaItem + " são do tipo Remessa, para cancelá-los é necessário acessar o portal do banco e realizar o cancelamento!");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<BoletoVO> boletos = boletoDAO.consultarPorCodigos(listaBoletoOnline, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            List<BoletoVO> boletosSelecionados = new ArrayList<>();
            boolean todosPjBank = true;
            for (BoletoVO boletoVO : boletos) {
                if (boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                    if (boletoVO.isPodeCancelar()) {
                        boletosSelecionados.add(boletoVO);
                    }
                } else {
                    todosPjBank = false;
                    break;
                }
            }

            if (!todosPjBank) {
                throw new Exception("Boleto selecionado não é PjBank");
            }

            if (UteisValidacao.emptyList(boletosSelecionados)) {
                throw new Exception("Nenhum boleto válido para cancelamento");
            }

            Integer qtdCancelado = 0;
            for (BoletoVO obj : boletosSelecionados) {
                try {
                    boletoDAO.cancelarBoleto(obj, usuarioVO, "CancelarBoletos - Tela Cliente - PactoPay");
                    qtdCancelado++;
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            if (UteisValidacao.emptyNumber(qtdCancelado)) {
                throw new Exception("Nenhum boleto cancelado.");
            }
            return "Foi cancelado " + qtdCancelado + " boleto(s).";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String sincronizarBoletos(List<BoletoVO> boletosOnline,
                                     List<RemessaItemVO> boletosRemessaItem,
                                     Integer codUsuario, Connection con) throws Exception {
        Boleto boletoDAO;
        Usuario usuarioDAO;
        try {
            boletoDAO = new Boleto(con);
            usuarioDAO = new Usuario(con);

            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            if (UteisValidacao.emptyList(boletosOnline) &&
                    UteisValidacao.emptyList(boletosRemessaItem)) {
                throw new Exception("Nenhum boleto informado");
            }

            List<Integer> listaBoletoOnline = new ArrayList<>();
            if (!UteisValidacao.emptyList(boletosOnline)) {
                for (BoletoVO boletoVO : boletosOnline) {
                    listaBoletoOnline.add(boletoVO.getCodigo());
                }
            }

            StringBuilder codRemessaItem = new StringBuilder();
            if (!UteisValidacao.emptyList(boletosRemessaItem)) {
                for (RemessaItemVO itemVO : boletosRemessaItem) {
                    codRemessaItem.append("," + itemVO.getCodigo());
                }
                codRemessaItem = new StringBuilder(codRemessaItem.toString().replaceFirst(",",""));
            }
            //só selecionou boleto de remessa
            if (UteisValidacao.emptyList(listaBoletoOnline) &&
                    !UteisValidacao.emptyString(codRemessaItem.toString())) {
                throw new Exception("Boleto de remessa não podem ser sincronizados.");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<BoletoVO> boletos = boletoDAO.consultarPorCodigos(listaBoletoOnline, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            List<BoletoVO> boletosSelecionados = new ArrayList<>();
            for (BoletoVO boletoVO : boletos) {
                if (boletoVO.isPodeSincronizar()) {
                    boletosSelecionados.add(boletoVO);
                }
            }

            if (UteisValidacao.emptyList(boletosSelecionados)) {
                throw new Exception("Nenhum boleto válido para sincronizar.");
            }

            Integer qtdSincronizado = 0;
            for (BoletoVO obj : boletosSelecionados) {
                try {
                    boletoDAO.sincronizarBoleto(obj, usuarioVO, "SincronizarBoletos - Tela Cliente - PactoPay");
                    qtdSincronizado++;
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            if (UteisValidacao.emptyNumber(qtdSincronizado)) {
                throw new Exception("Nenhum boleto sincronizado.");
            }
            return "Foi sincronizado " + qtdSincronizado + " boleto(s).";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String enviarEmailBoletos(List<BoletoVO> boletosOnline,
                                     List<RemessaItemVO> boletosRemessaItem,
                                     Integer codUsuario, Connection con, HttpServletRequest request) throws Exception {
        Boleto boletoDAO;
        Email emailDAO;
        ConvenioCobranca convenioCobrancaDAO;
        StringBuilder msgRetornar = new StringBuilder();
        try {
            boletoDAO = new Boleto(con);
            emailDAO = new Email(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            String key = DAO.resolveKeyFromConnection(con);

            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não informado");
            }

            if (UteisValidacao.emptyList(boletosOnline)
                    && UteisValidacao.emptyList(boletosRemessaItem)) {
                throw new Exception("Nenhum boleto informado");
            }

            //BOLETOS ONLINE
            if (!UteisValidacao.emptyList(boletosOnline)) {
                List<Integer> listaBoletoOnline = new ArrayList<>();
                if (!UteisValidacao.emptyList(boletosOnline)) {
                    for (BoletoVO boletoVO : boletosOnline) {
                        listaBoletoOnline.add(boletoVO.getCodigo());
                    }
                }
                List<BoletoVO> boletos = boletoDAO.consultarPorCodigos(listaBoletoOnline, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                List<BoletoVO> boletosFiltro = new ArrayList<>();
                boolean todosPjBank = true;
                for (BoletoVO boletoVO : boletos) {
                    if (boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                        if (boletoVO.isPodeEnviarEmail()) {
                            boletosFiltro.add(boletoVO);
                        }
                    } else {
                        todosPjBank = false;
                        break;
                    }
                }

                Map<Integer, List<BoletoVO>> mapaConvenioBoleto = new HashMap<>();
                for (BoletoVO boletoVO : boletosFiltro) {
                    if (boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                        List<BoletoVO> listaBoleto = mapaConvenioBoleto.get(boletoVO.getConvenioCobrancaVO().getCodigo());
                        if (listaBoleto == null) {
                            listaBoleto = new ArrayList<>();
                        }
                        listaBoleto.add(boletoVO);
                        mapaConvenioBoleto.put(boletoVO.getConvenioCobrancaVO().getCodigo(), listaBoleto);
                    }
                }

                if (mapaConvenioBoleto.isEmpty()) {
                    throw new Exception("Nenhum boleto selecionado");
                }

                for (Integer conv : mapaConvenioBoleto.keySet()) {
                    List<BoletoVO> boletosSelecionados = mapaConvenioBoleto.get(conv);
                    List<EmailVO> emailsEnviar = emailDAO.consultarEmails(boletosSelecionados.get(0).getPessoaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (UteisValidacao.emptyList(emailsEnviar)) {
                        throw new Exception(boletosSelecionados.get(0).getPessoaVO().getNome() + " não tem e-mail cadastrado.");
                    }

                    String[] emails = new String[emailsEnviar.size()];
                    int i = 0;
                    for (Object objEmail : emailsEnviar) {
                        EmailVO emailVO = (EmailVO) objEmail;
                        emails[i] = emailVO.getEmail();
                        i++;
                    }

                    if (boletosSelecionados.size() == 1) {
                        boletoDAO.enviarEmailBoleto(key, boletosSelecionados.get(0), emails, true, false);
                    } else {
                        ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(conv, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        LinkedHashSet<String> pedidos = BoletoVO.obterListaImpressaoBoleto(boletosSelecionados);
                        BoletosManager boletosManager = new BoletosManager(convenioCobrancaVO.getCredencialPJBank(), convenioCobrancaVO.getChavePJBank(), convenioCobrancaVO);
                        String linkBoleto = boletosManager.getByIds(pedidos);
                        boletoDAO.enviarEmailBoletoLink(key, linkBoleto, boletosSelecionados.get(0).getEmpresaVO().getCodigo(),
                                boletosSelecionados.get(0).getPessoaVO().getCodigo(), emails);
                    }
                    msgRetornar.append("E-mail enviado com sucesso, para " + Arrays.toString(emails) + ". ");
                }
            }

            //BOLETOS REMESSA
            if (!UteisValidacao.emptyList(boletosRemessaItem)) {
                StringBuilder codRemessaItem = new StringBuilder();
                if (!UteisValidacao.emptyList(boletosRemessaItem)) {
                    for (RemessaItemVO itemVO : boletosRemessaItem) {
                        codRemessaItem.append("," + itemVO.getCodigo());
                    }
                    codRemessaItem = new StringBuilder(codRemessaItem.toString().replaceFirst(",", ""));
                }
                List<Integer> listaDeCodRemessaItems = Arrays.stream(codRemessaItem.toString().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());

                if (!UteisValidacao.emptyList(listaDeCodRemessaItems)) {
                    for (Integer codRemItem : listaDeCodRemessaItems) {
                        enviarEmailRemessaItem(key, codRemItem, codUsuario, null, request, con);
                    }
                }
            }

            return "Boleto(s) enviado(s) com sucesso!";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoDAO = null;
            emailDAO = null;
            convenioCobrancaDAO = null;
        }
    }
}
