package negocio.facade.jdbc;

/**
 * Created by <PERSON> on 31/01/2024.
 */

import negocio.comuns.basico.enumerador.TipoTokenEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.TokenInterfaceFacade;
import servicos.pix.TokenVO;

import java.sql.*;

public class Token extends SuperEntidade implements TokenInterfaceFacade {

    public Token() throws Exception {
        super();
    }

    public Token(Connection connection) throws Exception {
        super(connection);
    }

    public static TokenVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {

        TokenVO obj = new TokenVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setAccess_token(dadosSQL.getString("access_token"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        return obj;
    }

    public TokenVO incluir(TokenVO obj) throws Exception {
        String sql = "INSERT INTO public.token (" +
                "data_gerado, access_token, token_type, expires_in, scope, vezes_utilizado, tipotokenenum, convenioCobranca)" +
                "VALUES " +
                "(?, ?, ?, ?, ?, ?, ?, ?) RETURNING CODIGO;";

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 1;
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getData_gerado()));
        sqlInserir.setString(i++, obj.getAccess_token());
        sqlInserir.setString(i++, obj.getToken_type());
        sqlInserir.setLong(i++, obj.getExpires_in());
        sqlInserir.setString(i++, obj.getScope());
        sqlInserir.setInt(i++, obj.getVezes_utilizado());
        if (obj.getTipoTokenEnum() != null) {
            sqlInserir.setInt(i++, obj.getTipoTokenEnum().getCodigo());
        } else {
            sqlInserir.setNull(i++, Types.NULL);
        }
        if (!UteisValidacao.emptyNumber(obj.getConvenioCobranca())) {
            sqlInserir.setInt(i++, obj.getConvenioCobranca());
        } else {
            sqlInserir.setNull(i++, Types.NULL);
        }

        ResultSet rs = sqlInserir.executeQuery();
        if (rs.next()) {
            obj.setCodigo(rs.getInt("codigo"));
        }
        return obj;
    }

    @Override
    public TokenVO consultarAptoParaReutilizacao(int codConvenioCobranca, TipoTokenEnum tipoTokenEnum) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT codigo, access_token FROM token \n");
        sb.append("WHERE data_gerado::DATE = NOW()::DATE \n");
        sb.append("AND tipotokenenum = ? \n");
        sb.append("AND vezes_utilizado < 5 \n");
        sb.append("AND conveniocobranca = ? \n");
        if (tipoTokenEnum.equals(TipoTokenEnum.PIX_ITAU)) {
            sb.append("AND NOW() - INTERVAL '360 seconds' < data_gerado \n");
        } else {
            sb.append("AND NOW() - INTERVAL '3600 seconds' < data_gerado \n");
        }
        sb.append("ORDER BY codigo DESC LIMIT 1 \n");

        try (PreparedStatement ps = con.prepareStatement(sb.toString())) {
            ps.setInt(1, tipoTokenEnum.getCodigo());
            ps.setInt(2, codConvenioCobranca);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_MINIMOS);
                }
                return null;
            }
        } catch (Exception ex) {
            return null;
        }
    }

    public void incrementarUtilizacao(int codigo) throws SQLException {
        String sql = "UPDATE token SET vezes_utilizado = vezes_utilizado + 1 WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codigo);
            sqlAlterar.execute();
        }
    }
}

