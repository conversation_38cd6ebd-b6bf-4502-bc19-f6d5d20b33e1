package test.simulacao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.LogIntegracoes;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.integracao.foguete.ThreadIntegracaoFoguete;
import servicos.integracao.foguete.enums.ClienteFogueteStatusEnum;
import servicos.util.ExecuteRequestHttpService;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoSincronizarContratosClientesIntegracaoFoguete implements AutoCloseable {

    private static final String HEADER_TOKEN_API = "Api-Token";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";
    private final Connection con;
    private final Empresa empresaDAO;
    private final Contrato contratoDAO;
    private final Plano planoDAO;
    private final ContratoDependente contratoDependenteDAO;
    private final Cliente clienteDAO;
    private final LogIntegracoes logIntegracoesDAO;
    private final EmpresaVO empresaVO;
    private final ConfiguracaoIntegracaoFogueteVO configuracaoIntegracaoFogueteVO;
    private final boolean processarApenasAlunosNaoSincronizados;
    public final int CORES = Runtime.getRuntime().availableProcessors();
    private final ExecutorService executorService = Executors.newFixedThreadPool(CORES);
    private final UsuarioVO usuarioVO;

    public ProcessoSincronizarContratosClientesIntegracaoFoguete(Connection con, Integer codigoEmpresa, boolean processarApenasAlunosNaoSincronizados, ConfiguracaoIntegracaoFogueteVO configuracaoIntegracaoFogueteVO, UsuarioVO usuarioVO) throws Exception {
        this.con = con;
        Conexao.guardarConexaoForJ2SE(this.con);
        this.empresaDAO = new Empresa(this.con);
        this.contratoDAO = new Contrato(this.con);
        this.planoDAO = new Plano(this.con);
        this.contratoDependenteDAO = new ContratoDependente(this.con);
        this.clienteDAO = new Cliente(this.con);
        this.processarApenasAlunosNaoSincronizados = processarApenasAlunosNaoSincronizados;
        this.configuracaoIntegracaoFogueteVO = configuracaoIntegracaoFogueteVO != null
                ? configuracaoIntegracaoFogueteVO : empresaDAO.consultarConfiguracaoIntegracaoFoguete(codigoEmpresa);
        this.empresaVO = empresaDAO.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        this.logIntegracoesDAO = new LogIntegracoes(this.con);
        this.usuarioVO = usuarioVO;
    }

    public static void main(String[] args) throws Exception {

        String chave = "engenhariadocorpoingleses";
        Integer codigoEmpresa = 1;
        String codigoContrato = "28071";
        boolean processarApenasAlunosNaoSincronizados = true;

        Connection con = new DAO().obterConexaoEspecifica(chave);
        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        usuarioDAO = null;

        ProcessoSincronizarContratosClientesIntegracaoFoguete processo = new ProcessoSincronizarContratosClientesIntegracaoFoguete(con, codigoEmpresa, processarApenasAlunosNaoSincronizados, null, usuarioVO);
        processo.sincronizarContratosClientesIntegracaoFoguete(codigoContrato);

    }

    private void sincronizarContratosClientesIntegracaoFoguete(String codigoContratos) throws Exception {
        if (UteisValidacao.emptyString(codigoContratos)) {
            throw new ConsistirException("Nenhum código de contrato informado.");
        }

        configuracaoIntegracaoFogueteVO.setProduto(0); // não validar o produto nesse processo

        if (!configuracaoIntegracaoFogueteVO.isHabilitada()) {
            throw new ConsistirException("A configuração de integração com o Foguete não está habilitada.");
        }
        if (UteisValidacao.emptyString(configuracaoIntegracaoFogueteVO.getTokenApi())) {
            throw new ConsistirException("O token da API do Foguete não foi configurado.");
        }
        if (UteisValidacao.emptyString(configuracaoIntegracaoFogueteVO.getUrlApi())) {
            throw new ConsistirException("URL Api não está configurada para Integração Foguete!");
        }

        int total = codigoContratos.split(",").length;
        int atual = 0;

        for (String codContrato: codigoContratos.split(",")) {
            try {

                Uteis.logarDebug(String.format("%s\\%s - Sincronizando contrato cod: %s", ++atual, total, codContrato));

                ContratoVO contratoVO = this.contratoDAO.consultarPorCodigo(Integer.parseInt(codContrato), Uteis.NIVELMONTARDADOS_TODOS);

                if (contratoVO == null || UteisValidacao.emptyNumber(contratoVO.getCodigo())) {
                    Uteis.logarDebug(String.format("\t Contrato cod: %s não foi encontrado!", codContrato));
                    continue;
                }

                if (UteisValidacao.emptyString(contratoVO.getPlano().getDescricao())) {
                    PlanoVO planoVO = planoDAO.consultarPorChavePrimaria(contratoVO.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    contratoVO.setPlano(planoVO);
                    if (planoVO == null || UteisValidacao.emptyString(planoVO.getDescricao())) {
                        throw new ConsistirException("Plano não encontrado: " + contratoVO.getPlano().getCodigo());
                    }
                }

                if (contratoVO == null || contratoVO.getCodigo() == 0) {
                    throw new ConsistirException("Contrato não encontrado: " + codContrato);
                }

                if (!codContrato.matches("\\d+")) {
                    throw new ConsistirException("Código de matrícula inválido: " + codContrato);
                }

                if (Calendario.maior(Calendario.hoje(), contratoVO.getVigenciaAteAjustada()) ||
                        (!contratoVO.getSituacao().equals("AT") && !contratoVO.getSituacao().equals("TR"))) {
                    throw new ConsistirException("O contrato não está vigente!");
                }

                if (Calendario.maior(contratoVO.getVigenciaDe(), Calendario.hoje())) {
                    throw new ConsistirException("O contrato ainda não iniciou!");
                }

                sincronizarContratoCliente(contratoVO);
                sincronizarContratosClientesDependentes(contratoVO);

            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private void sincronizarContratoCliente(ContratoVO contratoVO) throws Exception {
        ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        if (processarApenasAlunosNaoSincronizados && jaFoiSincronizado(contratoVO.getCodigo(), clienteVO.getCodigo())) {
            return;
        }
        ThreadIntegracaoFoguete threadIntegracaoFoguete = new ThreadIntegracaoFoguete(this.con, configuracaoIntegracaoFogueteVO, ClienteFogueteStatusEnum.ACTIVE, contratoVO, clienteVO, usuarioVO, false);
        threadIntegracaoFoguete.run();
    }

    private boolean jaFoiSincronizado(Integer codigoContrato, Integer codigoCliente) throws Exception {
        String chaveLog = String.format("%d_%d", codigoContrato, codigoCliente);
        return SuperFacadeJDBC.existe("SELECT l.codigo FROM logintegracoes l\n" +
                "WHERE l.servico = 'INTEGRACAO_FOGUETE' \n" +
                "AND l.dadosrecebidos LIKE '{%}' \n" +
                "AND l.chaveprimaria = '" + chaveLog + "' \n" +
                "AND lower(l.dadosrecebidos::json->>'responseApi') = 'ok'", this.con);
    }

    private void sincronizarContratosClientesDependentes(ContratoVO contratoVO) throws Exception {
        List<ContratoDependenteVO> contratoDependenteVOS = contratoDependenteDAO.findAllByContrato(contratoVO.getCodigo());
        for (ContratoDependenteVO cdVO: contratoDependenteVOS) {
            if (!UteisValidacao.emptyNumber(cdVO.getCliente().getCodigo())) {
                ClienteVO clienteDependente = clienteDAO.consultarPorChavePrimaria(cdVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                if (processarApenasAlunosNaoSincronizados && jaFoiSincronizado(contratoVO.getCodigo(), clienteDependente.getCodigo())) {
                    continue;
                }
                System.out.printf("Sincronizando cliente dependente: %d %s\n", clienteDependente.getCodigoMatricula(), clienteDependente.getNome_Apresentar());
                ThreadIntegracaoFoguete thredDependente = new ThreadIntegracaoFoguete(this.con, configuracaoIntegracaoFogueteVO, ClienteFogueteStatusEnum.ACTIVE, contratoVO, clienteDependente, usuarioVO, false);
                thredDependente.run();
            }
        }
    }

    public void sincronizarContratosClientes() throws Exception {
        for (ClienteFogueteStatusEnum situacao : ClienteFogueteStatusEnum.values()) {
            Uteis.logarDebug(String.format("Integracao Foguete empresa: %s - processando situação: %s", empresaVO.getNome(), situacao.name()));
            String sql = sqlBaseConsultarContratosClientesSincronizar();
            switch (situacao) {
                case ACTIVE:
                    sql += filtroSqlAtivosNaoSincronizados();
                    break;
                case PAST_DUE:
                    sql += filtrosSqlInativosNaoSincronizados();
                    break;
                case INACTIVE:
                    sql += filtroSqlInativosNaoSincronizados();
                    break;
                case CANCELED:
                    sql += filtroSqlCanceladosNaoSincronizados();
                    break;
                default:
                    throw new ConsistirException("Situação não implementada: " + situacao);
            }

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                Integer contrato = rs.getInt("contrato");
                Integer codigoCliente = rs.getInt("codigocliente");

                ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_TODOS);
                ClienteVO clienteVO = clienteDAO.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_TODOS);
                ThreadIntegracaoFoguete threadIntegracaoFoguete = new ThreadIntegracaoFoguete(con, configuracaoIntegracaoFogueteVO, situacao, contratoVO, clienteVO, usuarioVO, false);
                executorService.submit(() -> {
                    try {
                        Thread.sleep(3000);
                        threadIntegracaoFoguete.run();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                });
            }
        }
    }

    public void sincronizarContratosEstornados() throws Exception {
        Uteis.logarDebug(String.format("Integracao Foguete empresa: %s - processando contratos estornados", empresaVO.getNome()));

        String sql = "SELECT \n" +
                "\tdistinct \n" +
                "\tli.chaveprimaria,\n" +
                "\tsubstring(li.chaveprimaria, 0, position('_' in li.chaveprimaria)) as codcontrato,\n" +
                "\tli.dadosrecebidos\n" +
                "FROM logintegracoes li \n" +
                "\tLEFT JOIN contrato con ON con.codigo = (substring(li.chaveprimaria, 0, position('_' in li.chaveprimaria)))::integer\n" +
                "WHERE li.servico = 'INTEGRACAO_FOGUETE' \n" +
                "AND coalesce((substring(li.chaveprimaria, 0, position('_' in li.chaveprimaria)))::integer, 0) > 0 \n" +
                "AND con.codigo IS NULL \n" +
                "AND li.dadosrecebidos LIKE '{%}'\n" +
                "AND (li.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' IN ('ACTIVE','PAST_DUE')\n" +
                "AND trim(lower(li.dadosrecebidos::json->>'responseApi')) = 'ok'\n" +
                "AND NOT EXISTS(SELECT li_sub.codigo FROM logintegracoes li_sub \n" +
                "\t\t\tWHERE li_sub.servico = 'INTEGRACAO_FOGUETE'\n" +
                "\t\t\tAND li_sub.chaveprimaria = li.chaveprimaria\n" +
                "\t\t\tAND li_sub.dadosrecebidos LIKE '{%}'\n" +
                "\t\t\tAND trim(lower(li_sub.dadosrecebidos::json->>'responseApi')) = 'ok'\n" +
                "\t\t\tAND (li_sub.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' IN ('INACTIVE', 'CANCELED'))";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            String chavePrimaria = rs.getString("chaveprimaria");
            Integer codigoContrato = rs.getInt("codcontrato");
            JSONObject jsonLog = new JSONObject(rs.getString("dadosrecebidos"));
            Integer codigoMatricula = jsonLog.optInt("codigoMatricula");

            try {
                if (!jsonLog.has("jsonBodyEnviado")) {
                    continue;
                }

                if (UteisValidacao.emptyNumber(codigoMatricula)) {
                    continue;
                }

                ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatricula(codigoMatricula, empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    continue;
                }

                JSONObject jsonBody = jsonLog.getJSONObject("jsonBodyEnviado");
                jsonBody.put("status", ClienteFogueteStatusEnum.INACTIVE.name());

                URIBuilder builder = new URIBuilder(configuracaoIntegracaoFogueteVO.getUrlApi());
                URI uri = builder.build();

                HttpPost httpPost = new HttpPost(uri);
                httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
                httpPost.setHeader(HEADER_TOKEN_API, configuracaoIntegracaoFogueteVO.getTokenApi());

                StringEntity entity = new StringEntity(jsonBody.toString(), StandardCharsets.UTF_8);
                httpPost.setEntity(entity);

                HttpClient httpClient = ExecuteRequestHttpService.createConnector(null);

                HttpResponse httpResponse = httpClient.execute(httpPost);
                String response = EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);

                jsonLog.put("responseApi", response != null ? response : "");
                jsonLog.put("jsonBodyEnviado", jsonBody);

                String msg = String.format("Identifier: %s - Contrato cod: %d - Status: %s", jsonBody.optString("identifier"), codigoContrato, ClienteFogueteStatusEnum.INACTIVE.name());

                if ("ok".equals(response)) {
                    salvarLog(msg + " - OK", jsonLog.toString(), chavePrimaria);
                } else {
                    salvarLog(msg + " - FALHA", jsonLog.toString(), chavePrimaria);
                }
            } catch (Exception e) {
                String msg = String.format("Erro inesperado ao sincronizar contrato contrato estornado: %s", e.getMessage());
                Uteis.logarDebug(msg);
                salvarLog(msg, null, chavePrimaria);
            }
        }
    }

    public void reenviarContratosInativosCancelados() throws Exception {
        Uteis.logarDebug(String.format("Integracao Foguete empresa: %s - processando contratos estornados", empresaVO.getNome()));

        if (UteisValidacao.emptyString(configuracaoIntegracaoFogueteVO.getUrlApi())) {
            throw new ConsistirException("URL Api não está configurada para Integração Foguete!");
        }

        String sql = "SELECT \n" +
                "distinct \n" +
                "li.chaveprimaria,\n" +
                "substring(li.chaveprimaria, 0, position('_' in li.chaveprimaria)) as codcontrato,\n" +
                "li.dadosrecebidos \n" +
                "FROM logintegracoes li \n" +
                "WHERE li.servico = 'INTEGRACAO_FOGUETE' \n" +
                "AND li.dadosrecebidos LIKE '{%}'\n" +
                "AND (li.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' IN ('INACTIVE','CANCELED')\n" +
                "AND trim(lower(li.dadosrecebidos::json->>'responseApi')) = 'ok' \n" +
                "AND NOT EXISTS(SELECT li_sub.codigo FROM logintegracoes li_sub \n" +
                "   WHERE li_sub.servico = 'INTEGRACAO_FOGUETE'\n" +
                "   AND li_sub.chaveprimaria = li.chaveprimaria\n" +
                "   AND li_sub.dadosrecebidos LIKE '{%}'\n" +
                "   AND (li_sub.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' IN ('ACTIVE')\n" +
                "   AND trim(lower(li_sub.dadosrecebidos::json->>'responseApi')) = 'ok' \n" +
                "   AND li_sub.datalancamento > li.datalancamento)";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            String chavePrimaria = rs.getString("chaveprimaria");
            Integer codigoContrato = rs.getInt("codcontrato");
            JSONObject jsonLog = new JSONObject(rs.getString("dadosrecebidos"));
            Integer codigoMatricula = jsonLog.optInt("codigoMatricula");

            try {
                if (!jsonLog.has("jsonBodyEnviado")) {
                    continue;
                }

                if (UteisValidacao.emptyNumber(codigoMatricula)) {
                    continue;
                }

                ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatricula(codigoMatricula, empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    continue;
                }

                JSONObject jsonBody = jsonLog.getJSONObject("jsonBodyEnviado");

                URIBuilder builder = new URIBuilder(configuracaoIntegracaoFogueteVO.getUrlApi());
                URI uri = builder.build();

                HttpPost httpPost = new HttpPost(uri);
                httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
                httpPost.setHeader(HEADER_TOKEN_API, configuracaoIntegracaoFogueteVO.getTokenApi());

                StringEntity entity = new StringEntity(jsonBody.toString(), StandardCharsets.UTF_8);
                httpPost.setEntity(entity);

                HttpClient httpClient = ExecuteRequestHttpService.createConnector(null);

                HttpResponse httpResponse = httpClient.execute(httpPost);
                String response = EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);

                jsonLog.put("responseApi", response != null ? response : "");
                jsonLog.put("jsonBodyEnviado", jsonBody);

                String msg = String.format("Identifier: %s - Contrato cod: %d - Status: %s", jsonBody.optString("identifier"), codigoContrato, jsonBody.optString("status"));

                if ("ok".equals(response)) {
                    salvarLog(msg + " - OK", jsonLog.toString(), chavePrimaria);
                } else {
                    salvarLog(msg + " - FALHA", jsonLog.toString(), chavePrimaria);
                }
            } catch (Exception e) {
                String msg = String.format("Erro inesperado ao sincronizar contrato contrato estornado: %s", e.getMessage());
                Uteis.logarDebug(msg);
                salvarLog(msg, null, chavePrimaria);
            }
        }
    }

    private void salvarLog(String resultado, String dadosRecebidos, String chavePrimaria) throws Exception {
        LogIntegracoesVO log = new LogIntegracoesVO();
        log.setDataLancamento(new Date());
        log.setServico("INTEGRACAO_FOGUETE");
        log.setResultado(resultado);
        log.setDadosRecebidos(dadosRecebidos);
        log.setChavePrimaria(chavePrimaria);
        logIntegracoesDAO.incluir(log);
    }

    private String sqlBaseConsultarContratosClientesSincronizar() {
        return "SELECT\n" +
                "\t s.codigocliente,\n" +
                "\t s.contrato,\n" +
                "\t s.inicio,\n" +
                "\t s.fim,\n" +
                "\t s.contrato_situacao,\n" +
                "\t s.tipo_aluno,\n" +
                "\t pes.cfp AS cpf,\n" +
                "\t e.email\n" +
                "FROM (SELECT\n" +
                "\t\t cli.codigo AS codigocliente,\n" +
                "\t\t cli.pessoa,\n" +
                "\t\t con.codigo AS contrato,\n" +
                "\t\t con.empresa,\n" +
                "\t\t con.situacao as contrato_situacao,\n" +
                "\t\t con.vigenciade AS inicio,\n" +
                "\t\t con.vigenciaateajustada AS fim,\n" +
                "\t\t 'TITULAR' AS tipo_aluno\n" +
                "\t\t FROM contrato con\n" +
                "\t\t INNER JOIN cliente cli ON cli.pessoa = con.pessoa\n" +
                "\t\t UNION\n" +
                "\t\t SELECT\n" +
                "\t\t cli.codigo AS codigocliente,\n" +
                "\t\t cli.pessoa,\n" +
                "\t\t con.codigo AS contrato,\n" +
                "\t\t con.empresa,\n" +
                "\t\t con.situacao as contrato_situacao,\n" +
                "\t\t cond.datainicio AS inicio,\n" +
                "\t\t cond.datafinalajustada::date AS fim,\n" +
                "\t\t 'DEPENDENTE' AS tipo_aluno\n" +
                "\t\t FROM contratodependente cond\n" +
                "\t\t INNER JOIN contrato con ON con.codigo = cond.contrato\n" +
                "\t\t INNER JOIN cliente cli ON cli.codigo = cond.cliente) AS s\n" +
                "INNER JOIN pessoa pes ON pes.codigo = s.pessoa \n" +
                "LEFT JOIN email e ON e.codigo = (SELECT e2.codigo FROM email e2 WHERE e2.pessoa = s.pessoa ORDER BY e2.codigo DESC LIMIT 1)\n" +
                "INNER JOIN empresa emp on emp.codigo = s.empresa\n" +
                "INNER JOIN configuracaointegracaofoguete cfg_fgt ON cfg_fgt.empresa = s.empresa AND cfg_fgt.habilitada is true\n" +
                "LEFT JOIN contratoplanoprodutosugerido cpps ON cpps.codigo = (SELECT cpps_sub.codigo FROM contratoplanoprodutosugerido cpps_sub INNER JOIN planoprodutosugerido pps ON pps.codigo = cpps_sub.planoprodutosugerido WHERE cpps_sub.contrato = s.contrato and pps.produto = cfg_fgt.produto LIMIT 1)\n" +
                "WHERE 1 = 1\n";
    }

    private String filtroSqlInativosNaoSincronizados() {
        return  "AND s.contrato_situacao = 'IN'\n" +
                "AND current_date > (s.fim + interval '1 day' * emp.carenciarenovacao)\n" +
                "AND NOT EXISTS(SELECT cp.codigo FROM contratooperacao cp WHERE cp.contrato = s.contrato AND cp.tipooperacao = 'CA')\n" +
                "AND EXISTS (SELECT l_sub.codigo FROM logintegracoes l_sub\n" +
                "\tWHERE l_sub.servico = 'INTEGRACAO_FOGUETE'\n" +
                "\tAND l_sub.dadosrecebidos like '{%}'\n" +
                "\tAND (l_sub.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' IN ('ACTIVE','PAST_DUE')\n" +
                "\tAND l_sub.chaveprimaria = CONCAT(s.contrato, '_', s.codigocliente))\n" +
                "AND NOT EXISTS (SELECT l_sub.codigo FROM logintegracoes l_sub\n" +
                "\tWHERE l_sub.servico = 'INTEGRACAO_FOGUETE'\n" +
                "\tAND l_sub.dadosrecebidos like '{%}'\n" +
                "\tAND (l_sub.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' = 'INACTIVE'\n" +
                "\tAND trim(lower(l_sub.dadosrecebidos::json->>'responseApi')) = 'ok'\n" +
                "\tAND l_sub.chaveprimaria = CONCAT(s.contrato, '_', s.codigocliente))\n" +
                "ORDER BY s.contrato";
    }

    private String filtrosSqlInativosNaoSincronizados() {
        return "and s.contrato_situacao = 'IN'\n" +
                "and current_date > s.fim\n" +
                "AND current_date <= (s.fim + interval '1 day' * emp.carenciarenovacao)\n" +
                "AND NOT EXISTS(SELECT cp.codigo FROM contratooperacao cp WHERE cp.contrato = s.contrato AND cp.tipooperacao = 'CA')\n" +
                "AND EXISTS (SELECT l_sub.codigo FROM logintegracoes l_sub\n" +
                "\tWHERE l_sub.servico = 'INTEGRACAO_FOGUETE'\n" +
                "\tand l_sub.dadosrecebidos like '{%}'\n" +
                "\tand (l_sub.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' = 'ACTIVE'\n" +
                "\tAND l_sub.chaveprimaria = CONCAT(s.contrato, '_', s.codigocliente))\n" +
                "AND NOT EXISTS (SELECT l_sub.codigo FROM logintegracoes l_sub\n" +
                "\tWHERE l_sub.servico = 'INTEGRACAO_FOGUETE'\n" +
                "\tAND l_sub.dadosrecebidos like '{%}'\n" +
                "\tAND (l_sub.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' = 'PAST_DUE'\n" +
                "\tAND trim(lower(l_sub.dadosrecebidos::json->>'responseApi')) = 'ok'\n" +
                "\tAND l_sub.chaveprimaria = CONCAT(s.contrato, '_', s.codigocliente))\n" +
                "ORDER BY s.contrato";
    }

    private String filtroSqlAtivosNaoSincronizados() {
        return "AND s.contrato_situacao = 'AT'\n" +
                "AND COALESCE(pes.cfp,'') <> ''\n" +
                "AND COALESCE(e.email,'') <> ''\n" +
                "AND s.inicio <= current_date\n" +
                "AND s.fim >= current_date\n" +
                "AND NOT EXISTS(SELECT cp.codigo FROM contratooperacao cp WHERE cp.contrato = s.contrato AND cp.tipooperacao = 'CA')\n" +
                "AND NOT EXISTS (SELECT l_sub.codigo FROM logintegracoes l_sub\n" +
                "\tWHERE l_sub.servico = 'INTEGRACAO_FOGUETE'\n" +
                "\tAND l_sub.dadosrecebidos like '{%}'\n" +
                "\tAND (l_sub.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' = 'ACTIVE'\n" +
                "\tAND trim(lower(l_sub.dadosrecebidos::json->>'responseApi')) = 'ok'\n" +
                "\tAND l_sub.chaveprimaria = CONCAT(s.contrato, '_', s.codigocliente))\n" +
                "AND (cpps.codigo IS NOT NULL \n" +
                "\tOR EXISTS (SELECT mpro.codigo FROM movproduto mpro \n" +
                "\t\tWHERE mpro.contrato = s.contrato \n" +
                "\t\tAND mpro.produto = cfg_fgt.produto))\n" +
                "ORDER BY s.contrato";
    }

    private String filtroSqlCanceladosNaoSincronizados() {
        return "AND s.contrato_situacao = 'CA'\n" +
                "AND current_date >= s.fim\n" +
                "AND EXISTS (SELECT l_sub.codigo FROM logintegracoes l_sub\n" +
                "\tWHERE l_sub.servico = 'INTEGRACAO_FOGUETE'\n" +
                "\tAND l_sub.dadosrecebidos like '{%}'\n" +
                "\tAND (l_sub.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' = 'ACTIVE'\n" +
                "\tAND l_sub.chaveprimaria = CONCAT(s.contrato, '_', s.codigocliente)\n" +
                "\tORDER BY codigo)\n" +
                "AND NOT EXISTS (SELECT l_sub.codigo FROM logintegracoes l_sub\n" +
                "\tWHERE l_sub.servico = 'INTEGRACAO_FOGUETE'\n" +
                "\tAND l_sub.dadosrecebidos like '{%}'\n" +
                "\tAND (l_sub.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' = 'CANCELED'\n" +
                "\tAND trim(lower(l_sub.dadosrecebidos::json->>'responseApi')) = 'ok'\n" +
                "\tAND l_sub.chaveprimaria = CONCAT(s.contrato, '_', s.codigocliente)\n" +
                "\tORDER BY codigo)\n" +
                "ORDER BY s.contrato";
    }

    @Override
    public void close() throws Exception {
        try {
            if (!this.con.isClosed()) {
                this.con.close();
            }
            executorService.shutdown();
            finalizarExecutor(60);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void finalizarExecutor(int minutosAguardar) {
        int numeroVerificacoes = minutosAguardar * 2;
        while (isExecutorRunning() && numeroVerificacoes > 0) {
            try {
                Thread.sleep(30000);
            } catch (InterruptedException ex) {
                Logger.getLogger(Uteis.class.getName()).log(Level.SEVERE, null, ex);
            }
            numeroVerificacoes--;
        }
        if (isExecutorRunning()) {
            destroyExecutor();
        }
    }

    public boolean isExecutorRunning() {
        return !(executorService.isShutdown() || executorService.isTerminated());
    }

    public void destroyExecutor() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                List<Runnable> droppedTasks = executorService.shutdownNow();
                Uteis.logarDebug("Integracao Foguete - Tarefas pendentes finalizadas por TimeOut: " + droppedTasks.size());
            }
        } catch (InterruptedException e) {
            Uteis.logarDebug("Integracao Foguete - Erro ao finalizar executor: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
