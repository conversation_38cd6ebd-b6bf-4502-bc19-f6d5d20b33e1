package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ReplicarRedeEmpresaEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.ReplicarRedeEmpresaCallable;
import controle.basico.clube.MensagemGenericaControle;
import controle.estoque.xmlNfe.DetalheProduto;
import controle.estoque.xmlNfe.NfeProc;
import controle.estoque.BalancoControle;
import negocio.armario.TamanhoArmarioVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ComissaoProdutoConfiguracaoVO;
import negocio.comuns.estoque.BalancoItensVO;
import negocio.comuns.estoque.BalancoVO;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.plano.*;
import negocio.comuns.plano.enumerador.NegocioSesiEnum;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.plano.enumerador.UnidadeMedidaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.plano.Produto;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.discovery.RedeDTO;
import servicos.integracao.impl.produtoMs.ProdutoMsService;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.proxy.dto.AutenticacaoDTO;
import servicos.proxy.dto.PersonaDTO;
import servicos.proxy.impl.ApiProxyImp;
import servicos.proxy.interfaces.ApiProxy;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static negocio.comuns.utilitarias.Uteis.setIfBlank;
import static negocio.comuns.utilitarias.Uteis.setIfNull;
import static negocio.comuns.utilitarias.Uteis.setIfZeroOrNull;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas produtoForm.jsp produtoCons.jsp) com as funcionalidades da classe
 * <code>Produto</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see Produto
 * @see ProdutoVO
 */
public class ProdutoControle extends SuperControle {

    private ProdutoVO produtoVO;
    protected List<SelectItem> listaSelectItemCategoriaProduto = new ArrayList<>();

    /* Atributos para a Pesquisa de Produtos com AutoComplete.
     * Página que utiliza estes atributos: include_SuggestionBoxProduto.jsp
     */
    //public static final String chaveSessionProduto = "produtoSelecionado";
    private static String produtoSel = "";
    private String produtoSelecionado = "";
    private String produtoMescladoSelecionado = "";
    private static final String chaveSessionProduto = "produtoSelSuggestionBox";
    private boolean apresentarComboTipoProduto = false;
    private boolean apresentarComboTipoVigencia = false;
    private boolean naoPermiteEditar = false;
    private String descricaoAnterior = "";
    private String tipoProdutoAnterior = "";
    private PacotePersonalVO pacote = new PacotePersonalVO();
    private boolean editandoPersonal = false;
    private boolean editandoCfg = false;
    private boolean editandoCfgValorPorPlano = false;
    private boolean podeAdicionarCfgEmpresa = false;
    private ConfiguracaoProdutoEmpresaVO cfgEmpresa = new ConfiguracaoProdutoEmpresaVO();
    private ConfiguracaoProdutoEmpresaPlanoVO cfgValorProdutoPlano = new ConfiguracaoProdutoEmpresaPlanoVO();
    private List<SelectItem> listaPlanos;
    private List<SelectItem> tiposArmario;
    private boolean editarSomenteDescricao = false;

    private ComissaoProdutoConfiguracaoVO comissaoVO;
    private boolean apresentarEmpresaComissao = true;
    private List<SelectItem> listaConfigEmissaoNFSe;
    private List<SelectItem> listaConfigEmissaoNFCe;
    private String situacaoFiltro;
    private String onComplete;
    private List<ProdutoImagemTO> listaImagens;
    private List<SelectItem> listaSelectItemModalidades;
    private List<ProdutoRedeEmpresaVO> listaProdutoRedeEmpresa;
    private boolean integranteRedeEmpresa;
    private boolean apresentarConfiguracoesSesi = false;
    private ApiProxy apiProxy = new ApiProxyImp();
    private String msgAguardandoReplicacao = "AGUARDANDO REPLICAR PRODUTO";
    private String msgPrefixoDataAtualizacaoReplicacao = "REPLICADO EM ";
    protected List listaSelectItemContratoTextoPadrao;
    private List<ProdutoVO> listaProdutoMescladoVO = new ArrayList<>();

    List<DetalheProduto> produtosParaCadastrarOuVincular = new ArrayList<>();

    protected List<CategoriaProdutoVO> categorias = new ArrayList<>();
    protected List<ProdutoVO> produtos = new ArrayList<>();
    protected List<SelectItem> listaSelectItemProduto = new ArrayList<>();
    protected List<ProdutoVO> produtosSemCodigoDeBarras = new ArrayList<>();
    List<ProdutoEstoqueVO> produtoEstoquesParaCriar = new ArrayList<>();

    Integer categoriaParaNovosProdutosNfe = null;
    Integer estoqueMinimoParaNovosProdutosNfe = null;
    boolean exibirCampoConfigValorGympass = false;
    boolean exibirCampoConfigValorTotalpass = false;
    boolean exibirCampoConfigValorGogood = false;

    /**
     * Interface
     * <code>ProdutoInterfaceFacade</code> responsável pela interconexão da
     * camada de controle com a camada de negócio. Criando uma independência da
     * camada de controle com relação a tenologia de persistência dos dados
     * (DesignPatter: Façade).
     */
    public ProdutoControle() throws Exception {
        limparMsg();
        obterUsuarioLogado();
        inicializarFacades();
        montarListaEmpresas();
        inicializarComissao();
        montarListaSelectItemModalidades();
        montarListaSelectItemProdutoSemCodigoDeBarras();
        montarListaSelectItemCategoriaProduto();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
        setApresentarConfiguracoesSesi(getFacade().getConfiguracaoSistema().utilizarServicoSesiSC());
    }

    public void adicionarPacote() {
        try {
            if (UteisValidacao.emptyNumber(pacote.getQuantidade())
                    || UteisValidacao.emptyNumber(pacote.getValorPosPago())
                    || UteisValidacao.emptyNumber(pacote.getValorPrePago())) {
                throw new Exception(getMensagemInternalizacao("msg_erro_pacote_personal"));
            }
            if(!editandoPersonal){
                produtoVO.getPacotesPersonal().add(new PacotePersonalVO(produtoVO.getCodigo(), 
                    pacote.getQuantidade(), pacote.getValorPosPago(), pacote.getValorPrePago()));
            }
            pacote = new PacotePersonalVO();
            editandoPersonal = false;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarListaSelectItemModalidades() {
        try {
            setListaSelectItemModalidades(new ArrayList<>());
            List<ModalidadeVO> resultadoConsulta = getFacade().getModalidade().consultarTodasModalidades(
                    getEmpresaLogado().getCodigo(), true, null);
            getListaSelectItemModalidades().add(new SelectItem(0, ""));
            for (ModalidadeVO obj : resultadoConsulta) {
                getListaSelectItemModalidades().add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        } catch (Exception e) {
            Uteis.logar(e, ProdutoControle.class);
        }
    }

    public void removerPacote() {
        editandoPersonal = false;
        PacotePersonalVO obj = (PacotePersonalVO) context().getExternalContext().getRequestMap().get("pacote");
        produtoVO.getPacotesPersonal().remove(obj);
    }
    public void editarPacote() {
        editandoPersonal = true;
        PacotePersonalVO obj = (PacotePersonalVO) context().getExternalContext().getRequestMap().get("pacote");
        pacote = obj;
    }
    
    public void removerCfg() {
        editandoCfg = false;
        ConfiguracaoProdutoEmpresaVO obj = (ConfiguracaoProdutoEmpresaVO) context().getExternalContext().getRequestMap().get("cfg");
        produtoVO.getConfiguracoesEmpresa().remove(obj);
    }
    public void editarCfg() {
        editandoCfg = true;
        ConfiguracaoProdutoEmpresaVO obj = (ConfiguracaoProdutoEmpresaVO) context().getExternalContext().getRequestMap().get("cfg");
        cfgEmpresa = obj;
    }

    public void removerCfgValorPorPlano() {
        editandoCfgValorPorPlano = false;
        ConfiguracaoProdutoEmpresaPlanoVO obj = (ConfiguracaoProdutoEmpresaPlanoVO) context().getExternalContext().getRequestMap().get("cfg");
        produtoVO.getConfiguracoesValorPorPlano().remove(obj);
    }
    public void editarCfgValorPorPlano() {
        editandoCfgValorPorPlano = true;
        ConfiguracaoProdutoEmpresaPlanoVO obj = (ConfiguracaoProdutoEmpresaPlanoVO) context().getExternalContext().getRequestMap().get("cfg");
        cfgValorProdutoPlano = obj;
    }
    
    public void adicionarCfg() {
        try {
            for(SelectItem si : getListaEmpresas()){
                if(cfgEmpresa.getEmpresa().getCodigo().equals(si.getValue())){
                    cfgEmpresa.getEmpresa().setNome(si.getLabel());
                }
            }
            if(!editandoCfg){
                for(ConfiguracaoProdutoEmpresaVO cfg : getProdutoVO().getConfiguracoesEmpresa()){
                    if(cfg.getEmpresa().getCodigo().equals(cfgEmpresa.getEmpresa().getCodigo())){
                        throw new Exception("Já existe configuração para esta empresa.");
                    }
                }
                produtoVO.getConfiguracoesEmpresa().add(cfgEmpresa.clone());
            }
            cfgEmpresa = new ConfiguracaoProdutoEmpresaVO();
            editandoCfg = false;
            setMensagemID("msg_adicionados_dados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void adicionarCfgValorPorPlano() {
        try {
            if(UteisValidacao.emptyNumber(cfgValorProdutoPlano.getEmpresa().getCodigo())) {
                throw new Exception("Selecione uma empresa válida.");
            }
            if(UteisValidacao.emptyNumber(cfgValorProdutoPlano.getPlano().getCodigo())) {
                throw new Exception("Selecione um plano válido.");
            }
            if(UteisValidacao.emptyNumber(cfgValorProdutoPlano.getValor())) {
                throw new Exception("O valor não pode ser zero.");
            }

            for (SelectItem si : getListaEmpresas()) {
                if (cfgValorProdutoPlano.getEmpresa().getCodigo().equals(si.getValue())) {
                    cfgValorProdutoPlano.getEmpresa().setNome(si.getLabel());
                }
            }

            for (SelectItem si : getListaPlanos()) {
                if (cfgValorProdutoPlano.getPlano().getCodigo().equals(si.getValue())) {
                    cfgValorProdutoPlano.getPlano().setDescricao(si.getLabel());
                }
            }

            if (!editandoCfgValorPorPlano) {
                for (ConfiguracaoProdutoEmpresaPlanoVO cfg : getProdutoVO().getConfiguracoesValorPorPlano()) {
                    if (cfg.getEmpresa().getCodigo().equals(cfgValorProdutoPlano.getEmpresa().getCodigo())
                    && cfg.getPlano().getCodigo().equals(cfgValorProdutoPlano.getPlano().getCodigo())) {
                        throw new Exception("Já existe uma mesma configuração para esse plano nessa mesma empresa.");
                    }
                }
                produtoVO.getConfiguracoesValorPorPlano().add(cfgValorProdutoPlano.clone());
            }

            cfgValorProdutoPlano = new ConfiguracaoProdutoEmpresaPlanoVO();

            editandoCfgValorPorPlano = false;
            setMensagemID("msg_adicionados_dados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }


    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>Produto</code> para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        setProdutoVO(new ProdutoVO());
        setListaImagens(new ArrayList<>());
        setNaoPermiteEditar(false);
        editandoPersonal = false;
        montarListaSelectItemContratoTextoPadrao();
        inicializarListasSelectItemTodosComboBox();
        cfgEmpresa = new ConfiguracaoProdutoEmpresaVO();
        cfgValorProdutoPlano = new ConfiguracaoProdutoEmpresaPlanoVO();
        validarPermissaoCfgEmpresa();
        montarComboTiposArmario();
        montarComboConfigEmissao();
        montarListaPlanos(null);

        setEditarSomenteDescricao(false);
        limparMsg();
        return "editar";
    }
    public void resetarComboTiposArmario() {
        try {
            tiposArmario = new ArrayList<SelectItem>();
            tiposArmario.add(new SelectItem(null, ""));
            List<TamanhoArmarioVO> tipos = getFacade().getTamanhoArmario().consultarPorDescricao("");
            for (TamanhoArmarioVO tipo : tipos) {
                tiposArmario.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
            }

        } catch (Exception e) {
            Uteis.logar(e, ProdutoControle.class);
            tiposArmario = new ArrayList<SelectItem>();
        }
    }
    public void montarComboTiposArmario() {
        if (tiposArmario == null || tiposArmario.isEmpty()) {
            resetarComboTiposArmario();
        }
    }

    public void abrirTela() {
        limparMsg();
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>Produto</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        limparMsg();
        montarListaSelectItemContratoTextoPadrao();
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            ProdutoVO obj = getFacade().getProduto().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);

            if (obj.isProdutoZillyonWeb() && !getUsuarioLogado().getAdministrador()) {
                throw new ConsistirException("Este produto não pode ser editado.");
            }

            inicializarAtributosRelacionados(obj);
            if (obj.getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo())) {
                obj.setValorFinal(obj.getValorFinal() * 1000);
            }
            setProdutoVO(new ProdutoVO());
            setProdutoVO(obj);
            if (getFacade().getMovProduto().consultarPorCodigoProduto(getProdutoVO().getCodigo())) {
                setNaoPermiteEditar(true);
            } else {
                setNaoPermiteEditar(false);
            }
            setEditarSomenteDescricao(getProdutoVO().getTipoProduto().equals(TipoProduto.MANUTENCAO_MODALIDADE.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.CHEQUE_DEVOLVIDO.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.ACERTO_CONTA_CORRENTE_ALUNO.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.ALTERAR_HORARIO.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.CREDITO_PERSONAL.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.DEPOSITO_CONTA_CORRENTE_ALUNO.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.DEVOLUCAO.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.DEVOLUCAO_CREDITO.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.DEVOLUCAO_DE_RECEBIVEIS.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.MANUTENCAO_CONTA_CORRENTE.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.QUITACAO_DE_DINHEIRO.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.TAXA_RENEGOCIACAO.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.TAXA_RENEGOCIACAO.getCodigo()));
            getProdutoVO().setNovoObj(false);
            getProdutoVO().registrarObjetoVOAntesDaAlteracao();
            editandoPersonal = false;
            inicializarListasSelectItemTodosComboBox();
            cfgEmpresa = new ConfiguracaoProdutoEmpresaVO();
            cfgValorProdutoPlano = new ConfiguracaoProdutoEmpresaPlanoVO();
            validarPermissaoCfgEmpresa();
            montarComboTiposArmario();
            montarComboConfigEmissao();
            montarListaPlanos(null);
            montarImagens();
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
        return "editar";
    }

    private void montarImagens() {
        try {
            setListaImagens(new ArrayList<>());
            
            if(!UteisValidacao.emptyString(getProdutoVO().getImagens())){
                setListaImagens(new ArrayList<>());
                JSONArray lista = new JSONArray(getProdutoVO().getImagens());
                for (int e = 0; e < lista.length(); e++) {
                    JSONObject obj = lista.getJSONObject(e);
                    try {
                        getListaImagens().add(new ProdutoImagemTO(obj));
                    } catch (Exception ignored) {
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean isUsaAulaCheia(){
        if(null != getProdutoVO()){
            return getProdutoVO().getTipoProduto().equals(TipoProduto.DIARIA.getCodigo())
                    || getProdutoVO().getTipoProduto().equals(TipoProduto.FREEPASS.getCodigo());
        }
        return  false;
    }

    /**
     * Método responsável inicializar objetos relacionados a classe
     * <code>ProdutoVO</code>. Esta inicialização é necessária por exigência da
     * tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(ProdutoVO obj) {
        if (obj.getCategoriaProduto() == null) {
            obj.setCategoriaProduto(new CategoriaProdutoVO());
        }
        setDescricaoAnterior(obj.getDescricao());
        setTipoProdutoAnterior(obj.getTipoProduto());
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe
     * <code>Produto</code>. Caso o objeto seja novo (ainda não gravado no BD) é
     * acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>. Se houver alguma inconsistência o objeto não é
     * gravado, sendo re-apresentado para o usuário juntamente com uma mensagem
     * de erro.
     */
    public void gravar(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                if (produtoVO.isNovoObj()) {

                    if(!isUsaAulaCheia()){
                        produtoVO.setAparecerAulaCheia(false);
                    }

                    validarProdutoExistente();
                    getFacade().getProduto().incluir(produtoVO, true);

                    //LOG - INICIO
                    try {
                        produtoVO.setObjetoVOAntesAlteracao(new ProdutoVO());
                        produtoVO.setNovoObj(true);
                        registrarLogObjetoVO(produtoVO, produtoVO.getCodigo(), "PRODUTO CE", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("PRODUTO CE", produtoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PRODUTO CE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    produtoVO.setNovoObj(false);
//                    LOG - FIM
                } else {
                    /*if(getEmpresaLogado().isTrabalharComPontuacao() && getFacade().getItemCampanha().existeItemCampanha(getProdutoVO().getCodigo(), TipoItemCampanhaEnum.PRODUTO.getCodigo())) {
                        getFacade().getItemCampanha().alterarPontos(getProdutoVO().getCodigo(), getProdutoVO().getPontos(), TipoItemCampanhaEnum.PRODUTO.getCodigo());
                    }*/
                    validarProdutoExistente();
                    getFacade().getProduto().alterar(produtoVO, true);

                    //LOG - INICIO
                    try {
                        registrarLogObjetoVO(produtoVO, produtoVO.getCodigo(), "PRODUTO CE", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("PRODUTO CE", produtoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PRODUTO CE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                }
            } else {

                if (produtoVO.getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo())) {
                    produtoVO.setValorFinal(produtoVO.getValorFinal() / 1000);
                }

                if (produtoVO.isNovoObj()) {
                    validarProdutoExistente();
                    /*if (!getEmpresaLogado().isTrabalharComPontuacao()) {
                        if (produtoVO.getPontos() > 0) {
                            throw new Exception("Para cadastrar produtos por pontos é necessário que a configuração desta função esteja ativada. Vá nas configurações da empresa, procure pela configuração 'Habilitar Pontuação por venda de produto' e habilite esta função.");
                        }
                    }*/
                    getFacade().getProduto().incluir(produtoVO);
                    incluirLogInclusao();
                } else {
                    if (getFacade().getMovProduto().consultarPorCodigoProduto(getProdutoVO().getCodigo())) {
                        setNaoPermiteEditar(true);
                    } else {
                        setNaoPermiteEditar(false);
                    }

                    validarProdutoExistente();

                    /*if (!getEmpresaLogado().isTrabalharComPontuacao()) {
                        if (produtoVO.getPontos() > 0) {
                            throw new Exception("Para cadastrar produtos por pontos é necessário que a configuração desta função esteja ativada. Vá nas configurações da empresa, procure pela configuração 'Habilitar Pontuação por venda de produto' e habilite esta função.");
                        }
                    }*/
                    ProdutoVO.validarProdutoVendido(produtoVO, getTipoProdutoAnterior(), naoPermiteEditar);
                    /*if(getEmpresaLogado().isTrabalharComPontuacao() && getFacade().getItemCampanha().existeItemCampanha(getProdutoVO().getCodigo(), TipoItemCampanhaEnum.PRODUTO.getCodigo())) {
                        getFacade().getItemCampanha().alterarPontos(getProdutoVO().getCodigo(), getProdutoVO().getPontos(), TipoItemCampanhaEnum.PRODUTO.getCodigo());
                    }*/
                    getFacade().getProduto().alterar(produtoVO);
                    if(isExibirReplicarRedeEmpresa()) {
                        prepararListaReplicarEmpresa();
                        atualizarProdutoReplicado();
                    }


                    //LOG - INICIO
                    incluirLogAlteracao();
                    //LOG - FIM
                }

                adicionarEstoqueMinimoOrdemCompra();

                if (produtoVO.getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo())) {
                    produtoVO.setValorFinal(produtoVO.getValorFinal() * 1000);
                }

                alterarSituacaoPlanoProdutoSugerido();
            }
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
            }
//            novo();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);

            validarModulosEAtualizarContextoProdutoAsync();

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);

        }
    }

    public void uploadNfe(UploadEvent upload) {
        try {
            limparMsg();
            uploadXmlNfe(upload);

            if (!UteisValidacao.emptyList(this.produtosParaCadastrarOuVincular)) {
                montarInfo("Arquivo XML processado, alguns produtos não foram identificados, por favor verifique os itens.");
                setMsgAlert("Richfaces.showModalPanel('modalProdutosParaCadastroOuVinculo');" + this.getMensagemNotificar());
            } else {
                montarInfo("Arquivo XML processado com sucesso");
                montarSucesso("msg_produtos_processados_atualizados_sucesso");
                setMsgAlert(this.getMensagemNotificar());
            }

        } catch (Exception e) {
            e.printStackTrace();
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.erroProcessamento", "Erro ao processar o arquivo XML.");
            montarErro(e);
            setMsgAlert(this.getMensagemNotificar());
        }
    }

    private void uploadXmlNfe(UploadEvent upload) throws Exception {
        produtosParaCadastrarOuVincular.clear();
        produtoEstoquesParaCriar.clear();
        UploadItem item = upload.getUploadItem();

        // Verificar se o arquivo é XML
        String fileName = item.getFileName();
        if (!fileName.endsWith(".xml")) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.tipoInvalido", "Somente arquivos XML são permitidos.");
            throw new Exception("Arquivo não é um XML válido.");
        }

        // Verificar tamanho do arquivo
        if (item.getFile().length() > 512000) { // 500 KB
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.tamanhoLimiteExcedido", "Arquivo tem tamanho superior a 500KB");
            throw new Exception("Tamanho Superior a 500KB");
        }

        // Processar o arquivo XML
        XmlMapper xmlMapper = new XmlMapper();
        NfeProc nfeProc = xmlMapper.readValue(item.getFile(), NfeProc.class);

        produtosXmlNfe(nfeProc);
    }

    private void produtosXmlNfe(NfeProc nfeProc) throws Exception {
        produtosParaCadastrarOuVincular.clear();
        produtoEstoquesParaCriar.clear();
        nfeProc.getNfe().getInfNFe().getDetalhesProduto().forEach(detalheProduto -> {
            try {

                detalheProduto.getProduto().setDescricao(detalheProduto.getProduto().getDescricao().replace("\"", ""));

                Optional<ProdutoVO> produto = getFacade().getProduto()
                        .consultarProdutosComControleEstoquePorCodigoDeBarrasOuDescricao(detalheProduto.getProduto().getCodigoDeBarras(), detalheProduto.getProduto().getDescricao(), Uteis.NIVELMONTARDADOS_TODOS);

                detalheProduto.setValorFinal(0.00);

                Optional<ImpostoProdutoCfopVO> impostoProdutoCfopVO = getFacade().getImpostoProdutoCfop().consultarPorCfopENcm(detalheProduto.getProduto().getCfop(), detalheProduto.getProduto().getNcm());

                if(produto.isPresent()) {
                    ProdutoVO produtoVO = produto.get();

                    //VEM DO XML DA NFE (SETTADOS AUTOMATICAMENTE)
                    setIfBlank(produtoVO::getCfop, produtoVO::setCfop, detalheProduto.getProduto().getCfop());
                    setIfBlank(produtoVO::getNcm, produtoVO::setNcm, detalheProduto.getProduto().getNcm());
                    setIfBlank(produtoVO::getCest, produtoVO::setCest, detalheProduto.getProduto().getCest());
                    setIfBlank(produtoVO::getNcmNFCe, produtoVO::setNcmNFCe, detalheProduto.getProduto().getNcm());
                    setIfBlank(produtoVO::getCodigoBarras, produtoVO::setCodigoBarras, detalheProduto.getProduto().getCodigoDeBarras());

                    impostoProdutoCfopVO.ifPresent(produtoCfopVO -> settarCamposConfigDeImpostos(produtoVO, produtoCfopVO));

                    getFacade().getProduto().alterar(produtoVO);
                } else {
                    produtosParaCadastrarOuVincular.add(detalheProduto);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    private void settarCamposConfigDeImpostos(ProdutoVO produtoVO, ImpostoProdutoCfopVO impostoProdutoCfopVO) {
        setIfNull(produtoVO::getConfiguracaoNotaFiscalNFSe, produtoVO::setConfiguracaoNotaFiscalNFSe, impostoProdutoCfopVO.getConfiguracaoNotaFiscalNFSe());
        setIfNull(produtoVO::getConfiguracaoNotaFiscalNFCe, produtoVO::setConfiguracaoNotaFiscalNFCe, impostoProdutoCfopVO.getConfiguracaoNotaFiscalNFCe());

        // NOTA FISCAL
        setIfBlank(produtoVO::getCodigoListaServico, produtoVO::setCodigoListaServico, impostoProdutoCfopVO.getCodigoListaServico());
        setIfBlank(produtoVO::getCodigoTributacaoMunicipio, produtoVO::setCodigoTributacaoMunicipio, impostoProdutoCfopVO.getCodigoTributacaoMunicipio());
        setIfBlank(produtoVO::getDescricaoServicoMunicipio, produtoVO::setDescricaoServicoMunicipio, impostoProdutoCfopVO.getDescricaoServicoMunicipio());
        produtoVO.setEnviarPercentualImposto(impostoProdutoCfopVO.isEnviarPercentualImposto());
        setIfZeroOrNull(produtoVO::getPercentualFederal, produtoVO::setPercentualFederal, impostoProdutoCfopVO.getPercentualFederal());
        setIfZeroOrNull(produtoVO::getPercentualEstadual, produtoVO::setPercentualEstadual, impostoProdutoCfopVO.getPercentualEstadual());
        setIfZeroOrNull(produtoVO::getPercentualMunicipal, produtoVO::setPercentualMunicipal, impostoProdutoCfopVO.getPercentualMunicipal());

        //ICMS
        setIfBlank(produtoVO::getSituacaoTributariaICMS, produtoVO::setSituacaoTributariaICMS, impostoProdutoCfopVO.getSituacaoTributariaICMS());
        produtoVO.setIsentoICMS(impostoProdutoCfopVO.isIsentoICMS());
        produtoVO.setEnviaAliquotaNFeICMS(impostoProdutoCfopVO.isEnviaAliquotaNFeICMS());
        setIfZeroOrNull(produtoVO::getAliquotaICMS, produtoVO::setAliquotaICMS, impostoProdutoCfopVO.getAliquotaICMS());

        // ISSQN
        setIfBlank(produtoVO::getSituacaoTributariaISSQN, produtoVO::setSituacaoTributariaISSQN, impostoProdutoCfopVO.getSituacaoTributariaISSQN());
        setIfZeroOrNull(produtoVO::getAliquotaISSQN, produtoVO::setAliquotaISSQN, impostoProdutoCfopVO.getAliquotaISSQN());

        //CBNEF
        setIfBlank(produtoVO::getCodigoBeneficioFiscal, produtoVO::setCodigoBeneficioFiscal, impostoProdutoCfopVO.getCodigoBeneficioFiscal());

        // PIS
        setIfBlank(produtoVO::getSituacaoTributariaPIS, produtoVO::setSituacaoTributariaPIS, impostoProdutoCfopVO.getSituacaoTributariaPIS());
        produtoVO.setIsentoPIS(impostoProdutoCfopVO.isIsentoPIS());
        produtoVO.setEnviaAliquotaNFePIS(impostoProdutoCfopVO.isEnviaAliquotaNFePIS());
        setIfZeroOrNull(produtoVO::getAliquotaPIS, produtoVO::setAliquotaPIS, impostoProdutoCfopVO.getAliquotaPIS());

        // COFINS
        setIfBlank(produtoVO::getSituacaoTributariaCOFINS, produtoVO::setSituacaoTributariaCOFINS, impostoProdutoCfopVO.getSituacaoTributariaCOFINS());
        produtoVO.setIsentoCOFINS(impostoProdutoCfopVO.isIsentoCOFINS());
        produtoVO.setEnviaAliquotaNFeCOFINS(impostoProdutoCfopVO.isEnviaAliquotaNFeCOFINS());
        setIfZeroOrNull(produtoVO::getAliquotaCOFINS, produtoVO::setAliquotaCOFINS, impostoProdutoCfopVO.getAliquotaCOFINS());
    }

    public void montarListaSelectItemProdutoSemCodigoDeBarras() throws Exception {
        this.listaSelectItemProduto = new ArrayList<>();
        this.produtosSemCodigoDeBarras = getFacade().getProduto().consultarPorCodigoTipoProduto(0, "PE", true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ProdutoVO obj : produtosSemCodigoDeBarras) {
            if (!UteisValidacao.emptyString(obj.getCodigoBarras())) {
                //somente produto que não tem código de barras cadastrado
                continue;
            }
            listaSelectItemProduto.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        Ordenacao.ordenarLista(this.listaSelectItemProduto, "label");
        this.listaSelectItemProduto.add(0, new SelectItem(0, ""));
    }

    public void montarListaSelectItemCategoriaProduto() throws Exception {
        listaSelectItemCategoriaProduto.clear();
        categorias = consultarCategoriaProdutoPorDescricao("");
        listaSelectItemCategoriaProduto.add(new SelectItem(0, ""));

        for (CategoriaProdutoVO obj : categorias) {
            listaSelectItemCategoriaProduto.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
    }

    public void salvarProdutosNaoEncontradosNaNfe() {
        try {
            limparMsg();

            List<ProdutoVO> produtosSelecionados = new ArrayList<>();

            Optional<CategoriaProdutoVO> categoriaSelecionada = categorias.stream()
                    .filter(it -> it.getCodigo().equals(categoriaParaNovosProdutosNfe))
                    .findFirst();

            for (DetalheProduto produto : produtosParaCadastrarOuVincular) {
                if (!produto.getCadastrarNovo() && produto.getProdutoParaVincularNoEstoque() == 0) {
                    throw new Exception("O produto '" + produto.getProduto().getDescricao() + "' precisa ser cadastrado ou vinculado a um existente.");
                }
            }

            // Segunda etapa: Executar as ações de salvar e vincular os produtos
            for (DetalheProduto produto : produtosParaCadastrarOuVincular) {
                if (produto.getCadastrarNovo() && produto.getProdutoParaVincularNoEstoque() == 0) {
                    if (!categoriaSelecionada.isPresent()) {
                        throw new Exception("Você precisa selecionar uma categoria para os novos produtos");
                    }
                    try {
                        ProdutoVO produtoVO = new ProdutoVO();
                        ProdutoEstoqueVO produtoEstoqueVO = new ProdutoEstoqueVO();
                        Optional<ImpostoProdutoCfopVO> impostoProdutoCfopVO = getFacade().getImpostoProdutoCfop().consultarPorCfopENcm(produto.getProduto().getCfop(), produto.getProduto().getNcm());

                        produtoVO.setTipoProduto(TipoProduto.PRODUTO_ESTOQUE.getCodigo());
                        produtoVO.setDescricao(produto.getProduto().getDescricao());
                        produtoVO.setCodigoBarras(produto.getProduto().getCodigoDeBarras());
                        produtoVO.setCategoriaProduto(categoriaSelecionada.get());
                        produtoVO.setValorFinal(produto.getValorFinal());
                        produtoVO.setCest(produto.getProduto().getCest());
                        produtoVO.setCfop(produto.getProduto().getCfop());
                        produtoVO.setNcmNFCe(produto.getProduto().getNcm());
                        produtoVO.setNcm(produto.getProduto().getNcm());
                        produtoVO.setObservacao("Produto cadastrado automaticamente via importação de XML");

                        impostoProdutoCfopVO.ifPresent(produtoCfopVO -> settarCamposConfigDeImpostos(produtoVO, produtoCfopVO));

                        getFacade().getProduto().incluir(produtoVO);

                        produtoEstoqueVO.setProduto(produtoVO);
                        produtoEstoqueVO.setEstoqueMinimo(estoqueMinimoParaNovosProdutosNfe);
                        produtoEstoqueVO.setSituacao("A");
                        produtoEstoqueVO.setEmpresa(getEmpresaLogado());

                        produtoEstoquesParaCriar.add(produtoEstoqueVO);

                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                } else {
                    ProdutoVO produtoSelecionado = produtos.stream()
                            .filter(it -> it.getCodigo().equals(produto.getProdutoParaVincularNoEstoque()))
                            .findFirst()
                            .orElseThrow(() -> new RuntimeException("Produto não encontrado"));

                    produtoSelecionado.setCest(produto.getProduto().getCest());
                    produtoSelecionado.setCfop(produto.getProduto().getCfop());
                    produtoSelecionado.setNcmNFCe(produto.getProduto().getNcm());
                    produtoSelecionado.setNcm(produto.getProduto().getNcm());

                    Optional<ImpostoProdutoCfopVO> impostoProdutoCfopVO = getFacade().getImpostoProdutoCfop().consultarPorCfopENcm(produto.getProduto().getCfop(), produto.getProduto().getNcm());

                    impostoProdutoCfopVO.ifPresent(produtoCfopVO -> settarCamposConfigDeImpostos(produtoSelecionado, produtoCfopVO));

                    // Adiciona o produto à lista de selecionados para vincular
                    produtosSelecionados.add(produtoSelecionado);
                    produtoSelecionado.setCodigoBarras(produto.getProduto().getCodigoDeBarras());

                    try {
                        getFacade().getProduto().alterar(produtoSelecionado);
                    } catch (Exception e) {
                        throw new Exception(e);
                    }

                }
            }

            getFacade().getProdutoEstoque().incluir(produtoEstoquesParaCriar, getUsuario());

            produtosParaCadastrarOuVincular.clear();
            produtoEstoquesParaCriar.clear();
            montarInfo("Produtos processados e cadastrados com sucesso.");

            montarSucesso("msg_produtos_processados_atualizados_sucesso");

        } catch (Exception e) {
            e.printStackTrace();
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.erroProcessamento", "Erro ao processar os produtos da NFe.");
            montarErro(e);
            setMsgAlert(this.getMensagemNotificar());
        }
    }

    public String irParaUploadNfe() {
        limparMsg();
        return "uploadNfe";
    }

    public String voltarParaTelaAnterior() {
        limparMsg();
        return "produtoCons";
    }


    public void validarModulosEAtualizarContextoProdutoAsync() throws Exception {
        try{
            Integer empresa = getEmpresaLogado().getCodigo() != null ? getEmpresaLogado().getCodigo() : getUsuarioLogado().getCodEmpresaLogada();
            String chave = getUsuarioLogado().getChave() != null ? getUsuarioLogado().getChave() : context().getExternalContext().getSessionMap().get("key").toString();
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(chave);
            String modulos = clientDiscoveryDataDTO.getModulosHabilitados().length != 0 ? String.join(",", clientDiscoveryDataDTO.getModulosHabilitados()) : context().getExternalContext().getSessionMap().get("modulosHabilitados").toString();

            if (modulos.contains("ZW") && modulos.contains("CRM") && modulos.contains("IA")) {
                CompletableFuture.runAsync(() -> {
                    try {
                        this.enviarAtualizacaoContextoProdutosParaApiConversasIA(chave, empresa, clientDiscoveryDataDTO);
                    } catch (Exception e) {
                        Uteis.logar(e, ProdutoControle.class);
                    }
                });
            }
        }catch (Exception e){
            Uteis.logar("Erro ao validar modulos e atualizar contexto de produtos na api do pacto conversas", ProdutoControle.class);
            Uteis.logar(e, ProdutoControle.class);
        }
    }

    private AutenticacaoDTO getAutenticacaoMsV2ContentResponseVO(String chave, String baseUrl) {
        String path_id_persona = PropsService.getPropertyValue("AUTH_SECRET_PERSONA_PATH");
        String idValue = Uteis.readLineByLineJava8(path_id_persona).replace("\n", "").replace("\r", "");
        return this.apiProxy.mandarPost(baseUrl + "/aut/v2/gt", null, new PersonaDTO(idValue, chave), AutenticacaoDTO.class);
    }


    private void enviarAtualizacaoContextoProdutosParaApiConversasIA(String chave, Integer empresa, ClientDiscoveryDataDTO clientDiscovery) throws Exception {
        try {
            AutenticacaoDTO  autenticacaoDTO = getAutenticacaoMsV2ContentResponseVO(chave, clientDiscovery.getServiceUrls().getAutenticacaoUrl());
            this.apiProxy.mandarPut(clientDiscovery.getServiceUrls().getContatoMsUrl()+ "/v1/ia/contextos/produto?empresa=" + empresa, autenticacaoDTO.getContent(), null, Object.class);
        } catch (Exception e) {
            Uteis.logar(e, ProdutoControle.class);

        }
    }


    private void adicionarEstoqueMinimoOrdemCompra() throws Exception {
        if (produtoVO.getTipoProduto().equals(TipoProduto.ORDEM_COMPRA.getCodigo())) {
            List<ProdutoEstoqueVO> listaProd = getFacade().getProdutoEstoque().consultar(getEmpresaLogado().getCodigo(), produtoVO.getCodigo(), null, "T", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (listaProd == null || UteisValidacao.emptyList(listaProd)) {
                ProdutoEstoqueVO produtoEstoqueVO = new ProdutoEstoqueVO();
                produtoEstoqueVO.setEmpresa(getEmpresaLogado());
                produtoEstoqueVO.setEstoqueMinimo(1);
                produtoEstoqueVO.setSituacao("A");
                produtoEstoqueVO.setProduto(produtoVO);
                getFacade().getProdutoEstoque().incluir(Collections.singletonList(produtoEstoqueVO), getUsuarioLogado());
            }
        }
    }

    private void alterarSituacaoPlanoProdutoSugerido() throws Exception{
        if (produtoVO.getDesativado()){
            List<PlanoProdutoSugeridoVO> planoProdutoSugeridoVOs = getFacade().getPlanoProdutoSugerido().consultarPorProduto(produtoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyList(planoProdutoSugeridoVOs)){
                for (PlanoProdutoSugeridoVO obj: planoProdutoSugeridoVOs){
                    obj.setObrigatorio(false);
                    obj.setAtivoPlano(false);
                    getFacade().getPlanoProdutoSugerido().alterar(obj);
                }
            }
        }
    }

    /**
     * Responsável por por gravar no BD os dados editados de um novo objeto da
     * classe
     * <code>Produto</code> usado no ZW
     *
     * <AUTHOR> 22/03/2011
     */
    public void gravar() {
        this.gravar(false);
    }

    /**
     * Responsável por por gravar no BD os dados editados de um novo objeto da
     * classe
     * <code>Produto</code> usado no CE
     *
     * <AUTHOR> 22/03/2011
     */
    public void gravarCE() {
        this.gravar(true);
    }

    public void validarProdutoExistente() throws Exception {
        try {
            ProdutoVO obj;
            if (getProdutoVO().getTipoProduto().equals("MA")) {
                obj = getFacade().getProduto().consultarPorTipoProduto("MA", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getProdutoVO().validarProdutoExistente(obj);
            }
            if (getProdutoVO().getTipoProduto().equals("RE")) {
                obj = getFacade().getProduto().consultarPorTipoProduto("RE", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getProdutoVO().validarProdutoExistente(obj);
            }
            if (getProdutoVO().getTipoProduto().equals("RN")) {
                obj = getFacade().getProduto().consultarPorTipoProduto("RN", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getProdutoVO().validarProdutoExistente(obj);
            }
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * Metodo listener responsavel por setar na lista da view o resultado de uma
     * consulta paginada em banco de acordo com os filtros passados
     *
     * Autor: Pedro Y. Saito Criado em 29/12/2010
     */
    @SuppressWarnings("unchecked")
    public void consultarPaginadoListener(ActionEvent evt) {
        try {
            //Obtendo qual pagina deverá ser exibida
            Object component = evt.getComponent().getAttributes().get("pagNavegacao");
            if (component != null && !"".equals(component.toString())) {
                getConfPaginacao().setPagNavegacao(component.toString());
            }

            Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
            if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
                if (compPaginaInicial.toString().equals("paginaInicial")) {
                    setConfPaginacao(new ConfPaginacao());
                }
            }

            super.consultar();
            List objs = new ArrayList();

            // Foi encapsulado o método de consulta em um único, que suportara todos os filtros utilizados atualmente
            objs = getFacade().getProduto().consultarPaginado(getControleConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA, getConfPaginacao());

            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * ProdutoCons.jsp. Define o tipo de consulta a ser executada, por meio de
     * ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getProduto().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else if (getControleConsulta().getCampoConsulta().equals("descricaoCategoriaProduto")) {
                objs = getFacade().getProduto().consultarPorDescricaoCategoriaProduto(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getProduto().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else if (getControleConsulta().getCampoConsulta().equals("tipoProduto")) {
                List<String> tipos = obterPossiveisTiposProduto(getControleConsulta().getValorConsulta());
                objs = getFacade().getProduto().consultarProdutosPorTipoProduto(tipos, true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else if (getControleConsulta().getCampoConsulta().equals("tipoVigencia")) {
                objs = getFacade().getProduto().consultarPorTipoVigencia(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    private static List<String> obterPossiveisTiposProduto(String valor) {
        List<String> tipos = new ArrayList<String>();
        boolean todos = false;
        if (valor == null || valor.equals("")) {
            todos = true;
        }
        if (todos || "Matrícula".indexOf(valor) >= 0) {
            tipos.add("MA");
        }
        if (todos || "Produto Estoque".indexOf(valor) >= 0) {
            tipos.add("PE");
        }
        if (todos || "Mês de Referência Plano".indexOf(valor) >= 0) {
            tipos.add("PM");
        }
        if (todos || "Rematrícula".indexOf(valor) >= 0) {
            tipos.add("RE");
        }
        if (todos || "Renovação".indexOf(valor) >= 0) {
            tipos.add("RN");
        }
        if (todos || "Serviço".indexOf(valor) >= 0) {
            tipos.add("SE");
        }
        if (todos || "Convênio de Desconto".indexOf(valor) >= 0) {
            tipos.add("CD");
        }
        if (todos || "Desconto".indexOf(valor) >= 0) {
            tipos.add("DE");
        }
        if (todos || "Devolução".indexOf(valor) >= 0) {
            tipos.add("DV");
        }
        if (todos || "Trancamento".indexOf(valor) >= 0) {
            tipos.add("TR");
        }
        if (todos || "Retorno Trancamento".indexOf(valor) >= 0) {
            tipos.add("RT");
        }
        if (todos || "Aula Avulsa".indexOf(valor) >= 0) {
            tipos.add("AA");
        }
        if (todos || "Diária".indexOf(valor) >= 0) {
            tipos.add("DI");
        }
        if (todos || "FreePass".indexOf(valor) >= 0) {
            tipos.add("FR");
        }
        if (todos || "Alterar - Horário".indexOf(valor) >= 0) {
            tipos.add("AH");
        }
        if (todos || "Manutenção Modalidade".indexOf(valor) >= 0) {
            tipos.add("MM");
        }
        if (todos || "Manutenção Conta Corrente".indexOf(valor) >= 0) {
            tipos.add("MC");
        }
        if (tipos.isEmpty()) {
            tipos.add("NENHUM");
        }
        return tipos;
    }

    public void montarListaSelectItemContratoTextoPadrao() {
        try {
            montarListaSelectItemContratoTextoPadrao("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void montarListaSelectItemContratoTextoPadrao(String prm) throws Exception {
        List resultadoConsulta = consultarContratoTextoPadraoPorDescricao(0);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            PlanoTextoPadraoVO obj = (PlanoTextoPadraoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListaSelectItemContratoTextoPadrao(objs);
    }

    public List consultarContratoTextoPadraoPorDescricao(Integer codigoPrm) throws Exception {
        return getFacade().getPlanoTextoPadrao().consultarPorCodigoSituacaoTipo(codigoPrm, "AT", "SE", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>ProdutoVO</code> Após a exclusão ela automaticamente aciona a
     * rotina para uma nova inclusão.
     */
    public void excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
            }
            if (isEditarSomenteDescricao()){
                throw new ConsistirException("Esse Produto não pode ser excluído, pois é necessário para o funcionamento do sistema");
            }
            getFacade().getProduto().excluir(produtoVO);

            if (centralEventos) {
                //LOG - INICIO
                try {
                    registrarLogExclusaoObjetoVO(produtoVO, produtoVO.getCodigo().intValue(), "PRODUTO CE", 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("PRODUTO CE", produtoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PRODUTO CE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM       		
            } else {
                incluirLogExclusao();
            }

            validarModulosEAtualizarContextoProdutoAsync();
            setProdutoVO(new ProdutoVO());
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_excluidos");
            redirect("/faces/produtoCons.jsp");
        } catch (Exception e) {
            Uteis.logar(e, ProdutoControle.class);
            setMensagemDetalhada(Uteis.obterMensagemDetalhada(e));

            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void excluir() {
        this.excluir(false);
    }

    public void excluirCE() {
        this.excluir(true);
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /* Método responsável por inicializar List<SelectItem> de valores do 
     * ComboBox correspondente ao atributo <code>tipoProduto</code>
     */
    public List<SelectItem> getListaSelectItemTipoProdutoProduto() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable tipoProdutos = (Hashtable) Dominios.getTipoProduto();
        Enumeration keys = tipoProdutos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoProdutos.get(value);
            //REMOVER OPÇÃO DE AULA AVULSA - Luiz Felipe - Projeto 120203
            if (!value.equals("AA")) {
                objs.add(new SelectItem(value, label));
            }
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /* Método responsável por inicializar List<SelectItem> de valores do 
     * ComboBox correspondente ao atributo <code>tipoVigencia</code>
     */
    public List<SelectItem> getListaSelectItemTipoVigenciaProduto() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable tipoVigencias = (Hashtable) Dominios.getTipoVigencia();
        Enumeration keys = tipoVigencias.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoVigencias.get(value);
            if (!value.equals("VV")) {
                objs.add(new SelectItem(value, label));
            } else {
                if (getProdutoVO() == null || (!getProdutoVO().getTipoProduto().equals("CD")
                        && !getProdutoVO().getTipoProduto().equals("PE")
                        && !getProdutoVO().getTipoProduto().equals("SE")
                        && !getProdutoVO().getTipoProduto().equals("SS")
                        && !getProdutoVO().getTipoProduto().equals("TP")
                        && !getProdutoVO().getTipoProduto().equals("TD")
                        && !getProdutoVO().getTipoProduto().equals("TA"))) {
                    objs.add(new SelectItem(value, label));
                }
            }
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>CategoriaProduto</code>.
     */
//    public void montarListaSelectItemCategoriaProduto(String prm) throws Exception {
//        List resultadoConsulta = consultarCategoriaProdutoPorDescricao(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            CategoriaProdutoVO obj = (CategoriaProdutoVO) i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
//        }
//        setListaSelectItemCategoriaProduto(objs);
//        montarListaEmpresas();
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo
//     * <code>CategoriaProduto</code>. Buscando todos os objetos correspondentes
//     * a entidade
//     * <code>CategoriaProduto</code>. Esta rotina não recebe parâmetros para
//     * filtragem de dados, isto é importante para a inicialização dos dados da
//     * tela para o acionamento por meio requisições Ajax.
//     */
//    public void montarListaSelectItemCategoriaProduto() {
//        try {
//            montarListaSelectItemCategoriaProduto("");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarCategoriaProdutoPorDescricao(String descricaoPrm) throws Exception {
        List lista = getFacade().getCategoriaProduto().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (
     * <code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() throws Exception {
        montarListaSelectItemCategoriaProduto();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("descricaoCategoriaProduto", "Categoria de Produto"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("tipoProduto", "Tipo de Produto"));
        itens.add(new SelectItem("tipoVigencia", "Tipo de Vigência"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        setListaProdutoMescladoVO(new ArrayList<>());
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos
     * de persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List getListaSelectItemCategoriaProduto() {
        return (listaSelectItemCategoriaProduto);
    }

    public ProdutoVO getProdutoVO() {
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public String getProdutoSelecionado() {
        produtoSelecionado = this.produtoSel;
        return produtoSelecionado;
    }

    public void setProdutoSelecionado(String produtoSelecionado) {
        this.produtoSel = produtoSelecionado;
        this.produtoSelecionado = produtoSelecionado;
    }

    /*28/01/11 Ulisses...
     * Tela onde o método é utilizado: include_SuggestionBoxProduto.jsp
     Objetivo: Selecionar o produto a partir do suggestionBox de pesquisa de Produto
     *     Quando o método é acionado: Este método é acionado quando o usuário
     seleciona o produto apartir do suggestionBox
     */
    public void selecionarProduto() {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("result");
        /* Colocar o Produto selecionado na sessão, para que quem chamou o
         * include_SuggestionBoxProduto.jsp possa utilizá-lo .
         */
        HttpSession session = (HttpSession) context().getExternalContext().getSession(true);
        session.setAttribute(chaveSessionProduto, obj);
    }

    /*02/02/11 Ulisses...
     * Objetivo do método: Setar um determinado produto para o suggestionBox.
     * Quando é utilizado: Ao editar um objeto que utiliza o Suggestionbox de pesquisa de produtos.
     */
    public static void setProdutoSuggestionBox(ProdutoVO produto) {
        HttpSession session = (HttpSession) FacesContext.getCurrentInstance().getExternalContext().getSession(true);
        session.setAttribute(chaveSessionProduto, produto);
        produtoSel = produto.getDescricao();
    }

    /*02/02/11 Ulisses...
     * Objetivo do método: Pegar o produto que o usuário selecionou no suggestionBox de produtos.
     * Quando é utilizado: Ao gravar um objeto que utiliza o Suggestionbox de pesquisa de produtos.
     */
    public static ProdutoVO pegarProdutoSelecionadoSuggestionBox() {
        return (ProdutoVO) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get(ProdutoControle.chaveSessionProduto);
    }

    /*
     28/01/11 Ulisses...
     * Tela onde o método é utilizado: include_SuggestionBoxProduto.jsp
     Objetivo: Pesquisar o produto no banco de dados
     *     Quando o método é acionado: Este método é acionado toda vez que o
     usuário digitar uma letra no suggestionBox
     */
    public List<ProdutoVO> executarAutocompletePesqProduto(Object suggest) {
        List<ProdutoVO> listaProdutos = null;
        try {
            String nomePesq = (String) suggest;
            listaProdutos = getFacade().getProduto().consultarPorDescricaoAtivo(nomePesq, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaProdutos;
    }
    public List<ProdutoVO> executarAutocompletePesqProdutoMesclado(Object suggest) {
        List<ProdutoVO> listaProdutos = null;
        try {
            String nomePesq = (String) suggest;
            listaProdutos = getFacade().getProduto().consultarPorDescricaoAtivo(nomePesq, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            //excluindo produto atual q está sendo mesclado
            listaProdutos = listaProdutos.stream().filter(p -> p.getCodigo().intValue()!= getProdutoVO().getCodigo().intValue()).collect(Collectors.toList());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaProdutos;
    }
    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("</span>","");
         campoOrdenacao = campoOrdenacao.replace("[<span>", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getProduto().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(),getSituacaoFiltro());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    /*02/02/11 Ulisses...
     * Objetivo do método: Limpar a tela de pesquisa do suggestionBox de produtos.
     * Quando é utilizado: Ao incluir um objeto que utiliza o Suggestionbox de pesquisa de produtos.
     */
    public static void limparSuggestionBoxProduto() {
        produtoSel = "";
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = produtoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), produtoVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       produtoVO = new ProdutoVO();
       realizarConsultaLogObjetoSelecionado();
    }


    /*02/02/11 Ulisses...
     * Objetivo do método: Saber se o usuário selecionou algum produto no suggestionBox.
     * Quando é utilizado: Ao gravar um objeto que utiliza o Suggestionbox de pesquisa de produtos.
     */
    public static Boolean selecionouProdutoNoSuggestionBox() {
        return ((!produtoSel.trim().equals("")));
    }

    public boolean isApresentarComboTipoProduto() {
        return apresentarComboTipoProduto;
    }

    public void setApresentarComboTipoProduto(boolean apresentarComboTipoProduto) {
        this.apresentarComboTipoProduto = apresentarComboTipoProduto;
    }

    public void habilitarCombo() {
        if (getControleConsulta().getCampoConsulta().equals("tipoProduto")) {
            apresentarComboTipoProduto = true;
            apresentarComboTipoVigencia = false;
        } else if (getControleConsulta().getCampoConsulta().equals("tipoVigencia")) {
            apresentarComboTipoProduto = false;
            apresentarComboTipoVigencia = true;
        } else {
            apresentarComboTipoProduto = false;
            apresentarComboTipoVigencia = false;
        }

    }

    public Boolean getCreditoPersonal() {
        return produtoVO != null
                && !UteisValidacao.emptyString(produtoVO.getTipoProduto())
                && produtoVO.getTipoProduto().equals(TipoProduto.CREDITO_PERSONAL.getCodigo());
    }

    public boolean isApresentarComboTipoVigencia() {
        return apresentarComboTipoVigencia;
    }

    public void setApresentarComboTipoVigencia(boolean apresentarComboTipoVigencia) {
        this.apresentarComboTipoVigencia = apresentarComboTipoVigencia;
    }

    public void habilitarComboTipoVigencia() {
        apresentarComboTipoVigencia = getControleConsulta().getCampoConsulta().equals("tipoVigencia");
    }

    public boolean isNaoPermiteEditar() {
        return naoPermiteEditar;
    }

    public void setNaoPermiteEditar(boolean naoPermiteEditar) {
        this.naoPermiteEditar = naoPermiteEditar;
    }

    public String getDescricaoAnterior() {
        return descricaoAnterior;
    }

    public void setDescricaoAnterior(String descricaoAnterior) {
        this.descricaoAnterior = descricaoAnterior;
    }

    public String getTipoProdutoAnterior() {
        return tipoProdutoAnterior;
    }

    public void setTipoProdutoAnterior(String tipoProduto) {
        this.tipoProdutoAnterior = tipoProduto;
    }

    public PacotePersonalVO getPacote() {
        return pacote;
    }

    public void setPacote(PacotePersonalVO pacote) {
        this.pacote = pacote;
    }

    public ConfiguracaoProdutoEmpresaVO getCfgEmpresa() {
        return cfgEmpresa;
    }

    public void setCfgEmpresa(ConfiguracaoProdutoEmpresaVO cfgEmpresa) {
        this.cfgEmpresa = cfgEmpresa;
    }

    public boolean isEditandoCfg() {
        return editandoCfg;
    }

    public void setEditandoCfg(boolean editandoCfg) {
        this.editandoCfg = editandoCfg;
    }
    
    public Boolean getProdutoVendaAvulsa(){
        return TipoProduto.tipoProdutoVendaAvulsa(getProdutoVO().getTipoProduto());
    }

    public boolean isPodeAdicionarCfgEmpresa() {
        return podeAdicionarCfgEmpresa;
    }

    public void setPodeAdicionarCfgEmpresa(boolean podeAdicionarCfgEmpresa) {
        this.podeAdicionarCfgEmpresa = podeAdicionarCfgEmpresa;
    }


    public boolean isEditandoCfgValorPorPlano() {
        return editandoCfgValorPorPlano;
    }

    public void setEditandoCfgValorPorPlano(boolean editandoCfgValorPorPlano) {
        this.editandoCfgValorPorPlano = editandoCfgValorPorPlano;
    }


    public void validarPermissaoCfgEmpresa() {
        try {
            if (getFacade().getEmpresa().countEmpresas(true) <= 1) {
                podeAdicionarCfgEmpresa = false;
                return;
            }

            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    podeAdicionarCfgEmpresa = true;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator<UsuarioPerfilAcessoVO> i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "ConfiguracaoEmpresaProduto", 
                            "5.13 - Configuração de empresa para produto");
                    podeAdicionarCfgEmpresa = true;
                    return;
                }
            }
        } catch (Exception e) {
            podeAdicionarCfgEmpresa = false;
        }
    }

    public List<SelectItem> getTiposArmario() {
        return tiposArmario;
    }

    public void setTiposArmario(List<SelectItem> tiposArmario) {
        this.tiposArmario = tiposArmario;
    }
    
    public void selecionarTipoProduto(){
        if(getProdutoVO().getTipoProduto().equals(TipoProduto.ARMARIO.getCodigo())){
            getProdutoVO().setTipoVigencia("ID");
        }
    }

    public boolean isEditarSomenteDescricao() {
        return editarSomenteDescricao;
    }

    public void setEditarSomenteDescricao(boolean editarSomenteDescricao) {
        this.editarSomenteDescricao = editarSomenteDescricao;
    }

    public boolean isExibirConfiguracoesParaEmpresas() {
        return !UteisValidacao.emptyList(this.produtoVO.getConfiguracoesEmpresa()) ||
                ((this.getProdutoVendaAvulsa()
                        || this.produtoVO.getArmario()
                        || this.produtoVO.getTipoProduto().equalsIgnoreCase(TipoProduto.DIARIA.getCodigo()))
                        && this.podeAdicionarCfgEmpresa);
    }



    public Boolean getDiaria(){
        return getProdutoVO() != null &&
                !UteisValidacao.emptyString(this.getProdutoVO().getTipoProduto()) &&
                this.getProdutoVO().getTipoProduto().equalsIgnoreCase(TipoProduto.DIARIA.getCodigo());
    }

    public void incluirLogInclusao() throws Exception {
        try {
            produtoVO.setObjetoVOAntesAlteracao(new ProdutoVO());
            produtoVO.setNovoObj(true);
            registrarLogObjetoVO(produtoVO, produtoVO.getCodigo(), "PRODUTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PRODUTO", produtoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PRODUTO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        produtoVO.setNovoObj(new Boolean(false));
        produtoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            produtoVO.setObjetoVOAntesAlteracao(new ProdutoVO());
            produtoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(produtoVO, produtoVO.getCodigo(), "PRODUTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PRODUTO", produtoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PRODUTO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(produtoVO, produtoVO.getCodigo(), "PRODUTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PRODUTO", produtoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PRODUTO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        produtoVO.registrarObjetoVOAntesDaAlteracao();
    }

    public ComissaoProdutoConfiguracaoVO getComissaoVO() {
        return comissaoVO;
    }

    public void setComissaoVO(ComissaoProdutoConfiguracaoVO comissaoVO) {
        this.comissaoVO = comissaoVO;
    }

    public void adicionarComissao() {
        try {
            montarSucesso("");
            getComissaoVO().setProduto(produtoVO);
            if (isApresentarEmpresaComissao()) {
                if (!UteisValidacao.emptyNumber(getComissaoVO().getEmpresa().getCodigo())) {
                    EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(getComissaoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    getComissaoVO().setEmpresa(empresaVO);
                }
            }

            boolean problema = false;
            ComissaoProdutoConfiguracaoVO comissaoProblema = null;
            for (ComissaoProdutoConfiguracaoVO comissaoCadastrada : getProdutoVO().getComissaoProdutos()) {
                if (getComissaoVO().getEmpresa().getCodigo().equals(comissaoCadastrada.getEmpresa().getCodigo()) &&
                        ((getComissaoVO().getVigenciaInicio_Long() >= comissaoCadastrada.getVigenciaInicio_Long() && getComissaoVO().getVigenciaInicio_Long() <= comissaoCadastrada.getVigenciaFinal_Long())
                        || (getComissaoVO().getVigenciaFinal_Long() >= comissaoCadastrada.getVigenciaInicio_Long() && getComissaoVO().getVigenciaInicio_Long() <= comissaoCadastrada.getVigenciaFinal_Long()))) {

                    if (!getComissaoVO().isNovoObj() && getComissaoVO().getCodigo().equals(comissaoCadastrada.getCodigo())) {
                        continue;
                    }

                    problema = true;
                    comissaoProblema = comissaoCadastrada;
                    break;
                }

                comissaoCadastrada.ajustarDataFinal(getComissaoVO().getVigenciaInicio());
            }

            if (!problema) {
                if (getComissaoVO().isNovoObj()) {
                    getProdutoVO().getComissaoProdutos().add(getComissaoVO());
                } else {
                    ComissaoProdutoConfiguracaoVO configRemover = null;
                    for (ComissaoProdutoConfiguracaoVO comissaoCadastrada : getProdutoVO().getComissaoProdutos()) {
                        if (comissaoCadastrada.getCodigo().equals(getComissaoVO().getCodigo())) {
                            configRemover = comissaoCadastrada;
                            break;
                        }
                    }
                    getProdutoVO().getComissaoProdutos().remove(configRemover);
                    getProdutoVO().getComissaoProdutos().add(getComissaoVO());

                }
                inicializarComissao();
            } else {
                montarErro("A configuração está conflitando com outra (" + String.valueOf(comissaoProblema) + " [" + comissaoProblema.getVigenciaInicio_Apresentar() + "-" + comissaoProblema.getVigenciaFinal_Apresentar() + "]" + "). Por favor, reveja a nova configuração.");
            }
        } catch (Exception ex) {
            montarErro("Problemas ao adicionar nova configuração de comissão.");
        }
    }

    public void inicializarComissao() throws Exception {
        List<EmpresaVO> empresaVOs = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_MINIMOS);
        setComissaoVO(new ComissaoProdutoConfiguracaoVO());
        setApresentarEmpresaComissao(true);
        if (empresaVOs.size() == 1) {
            getComissaoVO().setEmpresa(empresaVOs.get(0));
            setApresentarEmpresaComissao(false);
        }
    }

    public void montarProdutosMesclado(Integer idProduto) throws Exception {
        this.listaProdutoMescladoVO = new ArrayList<>();
        List<ProdutoMescladoVO> produtoMescladoVOS = getFacade().getProdutoMesclado().consultarPorProdutoDestino(idProduto, Uteis.NIVELMONTARDADOS_MINIMOS);

        produtoMescladoVOS.forEach(produtoMescladoVO -> {
            try {
                listaProdutoMescladoVO.add(produtoMescladoVO.getProdutoOrigem());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

    }

    public void removerComissao() {
        ComissaoProdutoConfiguracaoVO comissaoRemover = (ComissaoProdutoConfiguracaoVO) JSFUtilities.getFromRequest("comissaoProduto");
        getProdutoVO().getComissaoProdutos().remove(comissaoRemover);
    }

    public void editarComissao() throws Exception {
        ComissaoProdutoConfiguracaoVO comissaoRemover = (ComissaoProdutoConfiguracaoVO) JSFUtilities.getFromRequest("comissaoProduto");
        if (comissaoRemover == null) {
            setErro(true);
            setMensagemDetalhada("", "Erro ao selecionar configuração");
            return;
        }
        setComissaoVO((ComissaoProdutoConfiguracaoVO) comissaoRemover.getClone(true));
        if (getComissaoVO().getCodigo() > 0) {
            getComissaoVO().setNovoObj(false);
        }
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }

    public boolean isApresentarEmpresaComissao() {
        return apresentarEmpresaComissao;
    }

    public void setApresentarEmpresaComissao(boolean apresentarEmpresaComissao) {
        this.apresentarEmpresaComissao = apresentarEmpresaComissao;
    }

    public boolean isApresentarAbaComissao() {
        return !getProdutoVO().isNovoObj() &&
                !TipoProduto.CHEQUE_DEVOLVIDO.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.DESCONTO.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.CONVENIO_DESCONTO.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.FREEPASS.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.DESCONTO_RENOVACAO_ANTECIPADA.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.DEVOLUCAO.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.DEVOLUCAO_CREDITO.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.DEVOLUCAO_DE_RECEBIVEIS.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.MANUTENCAO_MODALIDADE.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.TAXA_RENEGOCIACAO.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
//                DEIXAR MANUTENCAO - by Luiz Felipe Ticket #24123
//                !TipoProduto.MANUTENCAO_CONTA_CORRENTE.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.MES_REFERENCIA_PLANO.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.ACERTO_CONTA_CORRENTE_ALUNO.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.MATRICULA.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.REMATRICULA.getCodigo().equals(getProdutoVO().getTipoProduto()) &&
                !TipoProduto.RENOVACAO.getCodigo().equals(getProdutoVO().getTipoProduto()) ;
        
    }

    public boolean isExibirConfiguracoesNFCe() {
        try {
            if (getUsuarioLogado().getUsuarioPactoSolucoes()) {
                return true;
            }
            return getEmpresaLogado().isUsarNFCe();
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isExibirConfiguracoesNFSe() {
        try {
            if (getUsuarioLogado().getUsuarioPactoSolucoes()) {
                return true;
            }
            return getEmpresaLogado().getUsarNFSe();
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isExibirAbaVendasOnline() {
        try {
            //caso for fazer alguma validação pelo tipo do produto
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void montarComboConfigEmissao() {
        try {
            listaConfigEmissaoNFSe = new ArrayList<SelectItem>();
            listaConfigEmissaoNFCe = new ArrayList<SelectItem>();

            listaConfigEmissaoNFSe.add(new SelectItem(0, ""));
            listaConfigEmissaoNFCe.add(new SelectItem(0, ""));

            List<ConfiguracaoNotaFiscalVO> listaTodos = getFacade().getConfiguracaoNotaFiscal().consultarConfiguracaoNotaFiscal(getEmpresaLogado().getCodigo(), null, true, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (ConfiguracaoNotaFiscalVO confg : listaTodos) {
                if (confg.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFSE) || confg.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE)) {
                    listaConfigEmissaoNFSe.add(new SelectItem(confg.getCodigo(), confg.getDescricao()));
                } else if (confg.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE)) {
                    listaConfigEmissaoNFCe.add(new SelectItem(confg.getCodigo(), confg.getDescricao()));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, ProdutoControle.class);
            listaConfigEmissaoNFSe = new ArrayList<SelectItem>();
            listaConfigEmissaoNFCe = new ArrayList<SelectItem>();
        }
    }

    public List<SelectItem> getListaConfigEmissaoNFSe() {
        if (listaConfigEmissaoNFSe == null) {
            listaConfigEmissaoNFSe = new ArrayList<SelectItem>();
        }
        return listaConfigEmissaoNFSe;
    }

    public void setListaConfigEmissaoNFSe(List<SelectItem> listaConfigEmissaoNFSe) {
        this.listaConfigEmissaoNFSe = listaConfigEmissaoNFSe;
    }

    public List<SelectItem> getListaConfigEmissaoNFCe() {
        if (listaConfigEmissaoNFCe == null) {
            listaConfigEmissaoNFCe = new ArrayList<SelectItem>();
        }
        return listaConfigEmissaoNFCe;
    }

    public void setListaConfigEmissaoNFCe(List<SelectItem> listaConfigEmissaoNFCe) {
        this.listaConfigEmissaoNFCe = listaConfigEmissaoNFCe;
    }

    public List<SelectItem> getListaSelectItemSituacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("AT", "Ativo"));
        objs.add(new SelectItem("NA", "Inativo"));
        objs.add(new SelectItem("TD", "Todos"));
        return objs;
    }

    public String getSituacaoFiltro() {
        if (situacaoFiltro == null) {
            situacaoFiltro = "AT";
        }
        return situacaoFiltro;
    }

    public String getSituacaoFiltroPadrao() {
        situacaoFiltro = "AT";
        return situacaoFiltro;
    }

    public void setSituacaoFiltro(String situacaoFiltro) {
        this.situacaoFiltro = situacaoFiltro;
    }

    public void confirmarExcluir(){
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("metodochamar");

        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        if (obj.equals("excluir")){
            control.init("Exclusão de Produto",
                    "Deseja excluir o Produto?",
                    this, obj, "", "", "", "grupoBtnExcluir, pnlMensagem, form");
        }else {
            control.init("Adição de Produto",
                    "Confirma adicionar o produto ao controle de estoque?",
                    this, "alterarSituacaoProdutoEstoque", "", "", "", "grupoBtnExcluir,form");

        }

    }

    public byte[] uploadImagem(UploadEvent upload) throws Exception {
        limparMsg();
        setOnComplete("");
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] foto = arrayOutputStream.toByteArray();
            arrayOutputStream.close();
            fi.close();

            String key = MidiaService.getInstance().uploadObjectFromByteArray(getKey(),
                    MidiaEntidadeEnum.IMAGENS_PRODUTO_ACADEMIA, String.format("%s_%s", Calendario.hoje().getTime(), getProdutoVO().getCodigo()), arrayOutputStream.toByteArray());

            ProdutoImagemTO imagem = new ProdutoImagemTO();
            imagem.setFotoKey(key);
            imagem.setOrdem(getListaImagens().size() + 1);

            getListaImagens().add(imagem);
            ordenarImagens();
            getFacade().getProduto().alterarImagens(getListaImagens(), getProdutoVO(), getUsuarioLogado());
            return foto;
        } catch (Exception ex) {
            ex.printStackTrace();
            tratarErroAoCarregarImagem(ex, erroAoLerArquivo, item1, "uploadImagem");
            montarErro(ex);
            return null;
        }
    }

    private void tratarErroAoCarregarImagem(Exception e, boolean erroAoLerArquivo, File file, String nomeMetodo){
        if ((erroAoLerArquivo) || (!isCMYK(file))){
            montarErro("Existe um erro não identificado no arquivo de imagem. Edite a imagem, crie outro arquivo e repita a operação.");
        }
        Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO CARREGAR IMAGEM EMPRESA. método:" + nomeMetodo + ". Erro: " + e.getMessage());
    }

    public void removerImagem(){
        try {
            ProdutoImagemTO imagem = (ProdutoImagemTO) context().getExternalContext().getRequestMap().get("imagem");
            MidiaService.getInstance().deleteObject(getKey(), MidiaEntidadeEnum.IMAGENS_PRODUTO_ACADEMIA, imagem.getFotoKey());

            List<ProdutoImagemTO> listaNova = new ArrayList<>();
            for (ProdutoImagemTO dto : getListaImagens()) {
                if (!dto.getFotoKey().equalsIgnoreCase(imagem.getFotoKey())) {
                    listaNova.add(dto);
                }
            }

            setListaImagens(listaNova);
            ordenarImagens();
            getFacade().getProduto().alterarImagens(getListaImagens(), getProdutoVO(), getUsuarioLogado());
        }catch (Exception ex){
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public List<ProdutoRedeEmpresaVO> getListaProdutoRedeEmpresa() {
        if(listaProdutoRedeEmpresa == null) {
            listaProdutoRedeEmpresa = new ArrayList<>();
        }
        return listaProdutoRedeEmpresa;
    }

    public void setListaProdutoRedeEmpresa(List<ProdutoRedeEmpresaVO> listaProdutoRedeEmpresa) {
        this.listaProdutoRedeEmpresa = listaProdutoRedeEmpresa;
    }

    public Integer getListaProdutoRedeEmpresaSize() {
        return getListaProdutoRedeEmpresa().size();
    }

    public Integer getListaProdutoRedeEmpresaSincronizado() {
        Integer cont = 0;
        for (ProdutoRedeEmpresaVO unid : getListaProdutoRedeEmpresa()) {
            if (unid.getDataAtualizacao() != null) {
                cont++;
            }
        }
        return cont;
    }


    private void ordenarImagens() {
        int ordem = 0;
        for (ProdutoImagemTO dto : getListaImagens()) {
            dto.setOrdem(++ordem);
        }
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public List<ProdutoImagemTO> getListaImagens() {
        if (listaImagens == null) {
            listaImagens = new ArrayList<>();
        }
        return listaImagens;
    }

    public void setListaImagens(List<ProdutoImagemTO> listaImagens) {
        this.listaImagens = listaImagens;
    }

    public List<SelectItem> getListaSelectItemUnidadeMedida() {
        if (produtoVO.getTipoProduto().equals("LC")) {
            return UnidadeMedidaEnum.getListaSelectItemLocacao();
        }
        return UnidadeMedidaEnum.getListaSelectItem(false, false);
    }

    public List<SelectItem> getListaSelectItemModalidades() {
        return listaSelectItemModalidades;
    }

    public void setListaSelectItemModalidades(List<SelectItem> listaSelectItemModalidades) {
        this.listaSelectItemModalidades = listaSelectItemModalidades;
    }

    public boolean isApresentarMargemLucroPrecoCusto() {
        try {
            String chave = getKey();
            return (chave.equalsIgnoreCase("teste") || chave.equalsIgnoreCase("458906d2b465dd6cd2c07507e2e4650e"));
        } catch (Exception ex) {
            return false;
        }
    }

    public void acaoAlterarPrecoCusto() {
        try {
            //calcular valor final do produto
            if (!UteisValidacao.emptyNumber(getProdutoVO().getPrecoCusto()) &&
                    !UteisValidacao.emptyNumber(getProdutoVO().getMargemLucro())) {
                Double valor = Uteis.arredondarForcando2CasasDecimais(getProdutoVO().getPrecoCusto() + (getProdutoVO().getPrecoCusto() * (getProdutoVO().getMargemLucro() / 100)));
                getProdutoVO().setValorFinal(valor);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void acaoAlterarMargemLucro() {
        try {
            if (!UteisValidacao.emptyNumber(getProdutoVO().getPrecoCusto()) &&
                    !UteisValidacao.emptyNumber(getProdutoVO().getMargemLucro())) {
                Double valor = Uteis.arredondarForcando2CasasDecimais(getProdutoVO().getPrecoCusto() + (getProdutoVO().getPrecoCusto() * (getProdutoVO().getMargemLucro() / 100)));
                getProdutoVO().setValorFinal(valor);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void acaoAlterarValorProduto() {
        try {
            if (!UteisValidacao.emptyNumber(getProdutoVO().getPrecoCusto()) &&
                    !UteisValidacao.emptyNumber(getProdutoVO().getValorFinal())) {

                if (getProdutoVO().getValorFinal() < getProdutoVO().getPrecoCusto()) {
                    getProdutoVO().setMargemLucro(0.0);
                    return;
                }

                Double valorDif = (getProdutoVO().getValorFinal() - getProdutoVO().getPrecoCusto());
                Double margem = Uteis.arredondarForcando2CasasDecimais((valorDif * 100) / getProdutoVO().getPrecoCusto());
                getProdutoVO().setMargemLucro(margem);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean isApresentarConfiguracoesSesi() {
        return apresentarConfiguracoesSesi;
    }

    public void setApresentarConfiguracoesSesi(boolean apresentarConfiguracoesSesi) {
        this.apresentarConfiguracoesSesi = apresentarConfiguracoesSesi;
    }

    public List<SelectItem> getListaSelectItemNegocioSesi() {
        return NegocioSesiEnum.getListaSelectItem();
    }

    public void validarRenovacaoAutomatica(){
        try{
            if(this.produtoVO.getRenovavelAutomaticamente()){
                produtoVO.setPrevalecerVigenciaContrato(false);
            }
        }catch (Exception e){
            montarErro(e);
        }
    }

    public String getTitleUnidadeMedidaProduto() {
        if (produtoVO.getTipoProduto().equalsIgnoreCase("LC") && produtoVO.getUnidadeMedida().equals(UnidadeMedidaEnum.TEMPO_HORA.getCodigo())) {
            return "Valor por hora que será utilizado no calculo do valor total da locação na agenda do treino, de acordo com o tempo de cada locação.";
        } else {
            if (produtoVO.getUnidadeMedida().equals(UnidadeMedidaEnum.UNIDADE.getCodigo())) {
                return "Valor por Un.";
            } else if (produtoVO.getUnidadeMedida().equals(UnidadeMedidaEnum.GRAMA.getCodigo())) {
                return "Valor por Kg.";
            }
            return "";
        }
    }

    public void replicarTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (ProdutoRedeEmpresaVO produtoRedeEmpresaVO : getListaProdutoRedeEmpresa()) {
                if (!produtoRedeEmpresaVO.getDataAtualizacaoInformada()) {
                    produtoRedeEmpresaVO.setSelecionado(true);
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PRODUTO, null, null, null, null, null, produtoRedeEmpresaVO, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarSelecionadas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (ProdutoRedeEmpresaVO produtoRedeEmpresaVO : getListaProdutoRedeEmpresa()) {
                if (produtoRedeEmpresaVO.isSelecionado()) {
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PRODUTO,
                            null,
                            null,
                            null,
                            null,
                            null,
                            produtoRedeEmpresaVO, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void limparReplicar() {
        for(ProdutoRedeEmpresaVO obj : getListaProdutoRedeEmpresa()){
            obj.setSelecionado(false);
        }
    }

    public boolean isExibirReplicarRedeEmpresa() {
        boolean integranteFranqueadoraRedeEmpresa = false;
        boolean permiteReplicarRedeEmpresa = false;
        boolean usuarioAdministrador = false;

        try {
            for (UsuarioPerfilAcessoVO userPerfAcess : getControladorTipado(LoginControle.class).getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                if (userPerfAcess.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR")) {
                    usuarioAdministrador = true;
                }
            }
        } catch (Exception e) {
            usuarioAdministrador = false;
        }
        try {
            RedeEmpresaVO redeEmpresaVO = (RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA);
            integranteFranqueadoraRedeEmpresa = redeEmpresaVO != null;
            permiteReplicarRedeEmpresa = !UteisValidacao.emptyNumber(redeEmpresaVO.getId()) && redeEmpresaVO.getChaveFranqueadora().equals(getKey());
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean usuarioAdminPacto = false;
        try {
            usuarioAdminPacto = getUsuarioLogado().getUsuarioAdminPACTO();
            if (!usuarioAdminPacto) {
                usuarioAdminPacto = getUsuarioLogado().getUsuarioPACTOBR();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return integranteFranqueadoraRedeEmpresa
                && !produtoVO.isNovoObj()
                && permiteReplicarRedeEmpresa
                && (usuarioAdminPacto || usuarioAdministrador);
    }

    public void inicializarPossuiRedeEmpresa() {
        try {
            RedeEmpresaVO rede = ((LoginControle) getControlador("LoginControle")).getRedeEmpresaVO();
            integranteRedeEmpresa = rede != null && !UteisValidacao.emptyNumber(rede.getId());
        } catch (Exception ex) {
            Logger.getLogger(ProdutoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void prepararListaReplicarEmpresa() {
        try {
            getListaProdutoRedeEmpresa().clear();
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            for (RedeDTO redeDTO : redeEmpresaDataDTO.getRedeEmpresas()) {
                if (redeDTO.getChave().toLowerCase().trim().equals(getKey().toLowerCase().trim())) {
                    continue;
                }
                ProdutoRedeEmpresaVO produtoRedeEmpresaVO = getFacade().getProdutoRedeEmpresa().consultarPorChaveProdutoECodigoEmpresaDestino(redeDTO.getChave(), produtoVO.getCodigo(), redeDTO.getEmpresaZw());
                if (produtoRedeEmpresaVO == null) {
                    produtoRedeEmpresaVO = new ProdutoRedeEmpresaVO();
                    produtoRedeEmpresaVO.setMensagemSituacao(msgAguardandoReplicacao);
                }
                produtoRedeEmpresaVO.setCodigoEmpresaDestino(redeDTO.getEmpresaZw());
                produtoRedeEmpresaVO.setChave(redeDTO.getChave().toLowerCase().trim());
                produtoRedeEmpresaVO.setNomeUnidade(redeDTO.getNomeFantasia());
                produtoRedeEmpresaVO.setRedeDTO(redeDTO);
                getListaProdutoRedeEmpresa().add(produtoRedeEmpresaVO);
            }
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            Logger.getLogger(ProdutoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void replicarProdutoRedeEmpresaGeral() {
        ProdutoRedeEmpresaVO obj = (ProdutoRedeEmpresaVO) context().getExternalContext().getRequestMap().get("produtoRedeEmpresaReplicacao");
        replicarProdutoRedeEmpresaUnica(obj);
    }

    public void atualizarProdutoReplicado() {
        try {
            for (ProdutoRedeEmpresaVO produtoRedeEmpresaVO : listaProdutoRedeEmpresa) {
                boolean produtosBatem = produtoRedeEmpresaVO.getProduto().equals(produtoVO.getCodigo());
                boolean replicacaoJaExiste = produtoRedeEmpresaVO.getDataAtualizacaoInformada()
                        && produtoRedeEmpresaVO.getMensagemSituacao().contains(msgPrefixoDataAtualizacaoReplicacao);
                if(produtosBatem && replicacaoJaExiste) {
                    try {
                        RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
                        String urlOrigemProdutoMs = redeEmpresaDataDTO.getServiceUrls().getProdutoMsUrl();

                        JSONObject produtoAtualizadoJson = ProdutoMsService.clonarProduto(produtoVO.getCodigo(), urlOrigemProdutoMs, getKey());
                        produtoAtualizadoJson.put("codigo", produtoRedeEmpresaVO.getProdutoReplicado());

                        ProdutoMsService.atualizarProdutoReplicado(produtoAtualizadoJson,
                                produtoRedeEmpresaVO.getRedeDTO().getProdutoMsUrl(), produtoRedeEmpresaVO.getRedeDTO().getChave(),
                                produtoRedeEmpresaVO.getCodigoEmpresaDestino());

                    } catch (Exception e) {
                        String msgErroAtualizacaoReplicacao = "REPLICAO DE PRODUTOS: Ocorreu um erro ao atualizar os dados do produto replicado '"
                                + produtoVO.getDescricao() + "' (cod.: " + produtoRedeEmpresaVO.getProdutoReplicado()
                                + " na unidade de chave " + produtoRedeEmpresaVO.getChave()
                                + "). Produto original: cod. " + produtoRedeEmpresaVO.getProduto();
                        Uteis.logar(msgErroAtualizacaoReplicacao + "- ERRO: " + e, ProdutoControle.class);

                    }
                }
            }

        } catch (Exception e) {
            Uteis.logar(e, ProdutoControle.class);
        }

    }

    public void replicarProdutoRedeEmpresa(ProdutoRedeEmpresaVO obj) throws Exception {
        ProdutoRedeEmpresaVO produtoRedeEmpresaVO = getFacade()
                .getProdutoRedeEmpresa()
                .consultarPorChaveProdutoECodigoEmpresaDestino(
                        obj.getChave(),
                        produtoVO.getCodigo(),
                        obj.getCodigoEmpresaDestino()
                );

        boolean jaExisteProdutoRedeEmpresa = produtoRedeEmpresaVO != null;

        if(jaExisteProdutoRedeEmpresa) {
            //atualizar registro ProdutoRedeEmpresa existente
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String urlOrigemProdutoMs = redeEmpresaDataDTO.getServiceUrls().getProdutoMsUrl();

            JSONObject cloneProdutoOrigem = ProdutoMsService.clonarProduto(produtoVO.getCodigo(), urlOrigemProdutoMs, getKey());
            if (!Uteis.isAmbienteDesenvolvimentoTeste()) {
                cloneProdutoOrigem.put("descricao", cloneProdutoOrigem.getString("descricao").toUpperCase().replace("CPIA DE ", ""));
            }

            JSONObject novoProduto = ProdutoMsService.replicarProduto(cloneProdutoOrigem, obj.getRedeDTO().getProdutoMsUrl(), obj.getRedeDTO().getChave(), obj.getCodigoEmpresaDestino());
            produtoRedeEmpresaVO.setProdutoReplicado(novoProduto.getInt("codigo"));
            produtoRedeEmpresaVO.setDataAtualizacao(new Date());
            produtoRedeEmpresaVO.setMensagemSituacao(msgPrefixoDataAtualizacaoReplicacao + Uteis.getDataComHora(produtoRedeEmpresaVO.getDataAtualizacao()));

            obj.setProdutoReplicado(novoProduto.getInt("codigo"));
            obj.setDataAtualizacao(new Date());
            obj.setMensagemSituacao(msgPrefixoDataAtualizacaoReplicacao + Uteis.getDataComHora(obj.getDataAtualizacao()));
            getFacade().getProdutoRedeEmpresa().alterarDataAtualizacao(produtoVO.getCodigo(), obj.getChave(), novoProduto.getInt("codigo"), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());



        } else {
            //criar novo registro na tabela ProdutoRedeEmpresa e fazer a replicacao dozero
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String urlOrigemProdutoMs = redeEmpresaDataDTO.getServiceUrls().getProdutoMsUrl();

            JSONObject cloneProdutoOrigem = ProdutoMsService.clonarProduto(produtoVO.getCodigo(), urlOrigemProdutoMs, getKey());
            cloneProdutoOrigem.put("empresa", obj.getCodigoEmpresaDestino());
            if (!Uteis.isAmbienteDesenvolvimentoTeste()) {
                cloneProdutoOrigem.put("descricao", cloneProdutoOrigem.getString("descricao").toUpperCase().replace("CPIA DE ", ""));
            }
            produtoRedeEmpresaVO = new ProdutoRedeEmpresaVO(produtoVO.getCodigo(), obj.getChave(), null, obj.getCodigoEmpresaDestino());
            getFacade().getProdutoRedeEmpresa().inserir(produtoRedeEmpresaVO);
            // PRODUTO no tem na outra academia da rede, ento inclui
            JSONObject novoProduto = ProdutoMsService.replicarProduto(cloneProdutoOrigem, obj.getRedeDTO().getProdutoMsUrl(), obj.getRedeDTO().getChave(), obj.getCodigoEmpresaDestino());
            produtoRedeEmpresaVO.setProdutoReplicado(novoProduto.getInt("codigo"));
            produtoRedeEmpresaVO.setDataAtualizacao(new Date());
            produtoRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(produtoRedeEmpresaVO.getDataAtualizacao()));
            obj.setProdutoReplicado(novoProduto.getInt("codigo"));
            obj.setDataAtualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataAtualizacao()));
            getFacade().getProdutoRedeEmpresa().alterarDataAtualizacao(produtoVO.getCodigo(), obj.getChave(), novoProduto.getInt("codigo"), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());



        }
    }

    public void replicarProdutoRedeEmpresaUnica(ProdutoRedeEmpresaVO obj) {
        try {
            replicarProdutoRedeEmpresa(obj);
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            obj.setMensagemSituacao("ERRO: " + ex.getMessage());

            try {
                getFacade().getProdutoRedeEmpresa().alterarMensagemSituacao(obj.getCodigo(), obj.getChave(), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());
            } catch (Exception e) {
            }

            Logger.getLogger(ProdutoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void retirarVinculoReplicacao() {
        limparMsg();
        ProdutoRedeEmpresaVO obj = (ProdutoRedeEmpresaVO) context().getExternalContext().getRequestMap().get("produtoRedeEmpresaReplicacao");
        try {
            obj.setDataAtualizacao(null);
            obj.setMensagemSituacao(msgAguardandoReplicacao);
            getFacade().getProdutoRedeEmpresa().limparDataAtualizacao(produtoVO.getCodigo(), obj.getChave(), obj.getCodigoEmpresaDestino());
            getFacade().getProdutoRedeEmpresa().alterarMensagemSituacao(produtoVO.getCodigo(), obj.getChave(), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    public void selecionarProdutoMesclado() {
        try{
            ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("result");

            Optional<ProdutoVO> optionalProduto = this.listaProdutoMescladoVO.stream().filter(pm -> obj.getCodigo().intValue() == pm.getCodigo().intValue()).findFirst();
            if(optionalProduto.isPresent()){
                setMensagemDetalhada("msg_produto_mesclado_cadastrado", "");
                return ;
            }
            this.listaProdutoMescladoVO.add(obj);
            this.setProdutoMescladoSelecionado("");
        }catch(Exception e){
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }

    }


    public int quantidadeEstoquePorProduto(Integer codigo){
        try {
            ProdutoEstoqueVO produtoEstoqueVO = getFacade().getProdutoEstoque().consultarPorProduto(codigo,getEmpresaLogado().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return produtoEstoqueVO.getEstoque();
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    public void removerProdutoMesclado() throws Exception {
        try {
            ProdutoVO produtoVO = (ProdutoVO) context().getExternalContext().getRequestMap().get("produtoMescladoItem");
            this.listaProdutoMescladoVO.remove(produtoVO);
            setMensagemID("msg_dados_excluidos");
            setMensagem("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void salvarProdutosMesclados() {
        try {
            limparMsg();

            String nomeUsuarioLogado = getUsuarioLogado()!=null? getUsuarioLogado().getNome():"";
            //incluir na tabela de produtos mesclados
            List<ProdutoMescladoVO> produtoMescladosVO =listaProdutoMescladoVO.stream().map(produtoOrgem -> new ProdutoMescladoVO(getProdutoVO() , produtoOrgem,nomeUsuarioLogado)).collect(Collectors.toList());
            getFacade().getProdutoMesclado().incluir(produtoMescladosVO);

            //altera os produtos origem para mesclado=true e desativado = false
            for (ProdutoMescladoVO produtoMescladoVO : produtoMescladosVO) {
                getFacade().getProduto().alterarFlagMesclado(produtoMescladoVO.getProdutoOrigem().getCodigo(), true);
            }

            BalancoControle balancoControle = JSFUtilities.getManagedBean(BalancoControle.class);

            int quantidadeDeEstoqueProdutosMesclados =0;
            //fazendo balanco de produtos origem ou seja, produtos que serão anexados ao produto destino e seus estoques serão somados ao produto destino
            quantidadeDeEstoqueProdutosMesclados = criaBalancoDeProdutoOrigem(produtoMescladosVO, balancoControle, criaObjetoBalanco("Mecland Produto Origem"), quantidadeDeEstoqueProdutosMesclados);

            //atribuindo ao produto de destino o somatorio dos produtos mesclados
            criaBalancoDeProdutoDestino(balancoControle, criaObjetoBalanco("Mesclando Produto Destino"), quantidadeDeEstoqueProdutosMesclados);

            if(!balancoControle.getSucesso()){
                new Exception("Erro ao inserir balanco");
            }
            //log na entidade produto mesclado
            for (ProdutoMescladoVO produtoMescladoVO : produtoMescladosVO) {
                registrarLogObjetoVO(produtoMescladoVO,produtoMescladoVO.getCodigo(),BalancoControle.NOME_ENTIDADE,0);
            }


            montarInfo("Produtos processados com sucesso. Verifique as informações e confirme a operação.");

            montarSucesso("msg_produtos_processados_sucesso");


        } catch (Exception e) {
            e.printStackTrace();
            rollbackProdutoMesclado();
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.erroProcessamento", "Erro ao processar os produtos da NFe.");
            montarErro(e);
            setMsgAlert(this.getMensagemNotificar());
        }finally {
            setListaProdutoMescladoVO(new ArrayList<>());
        }
    }

    private void rollbackProdutoMesclado() {
        try {
            getFacade().getProdutoMesclado().excluirTodosProdutosPelaDestino(getProdutoVO().getCodigo());

            for (ProdutoVO vo : getListaProdutoMescladoVO()) {
                getFacade().getProduto().alterarFlagMesclado(vo.getCodigo(), false);
            }

            List<Integer> idProdutosMesclados = new ArrayList<>();
            idProdutosMesclados.add(getProdutoVO().getCodigo());
            idProdutosMesclados.addAll(getListaProdutoMescladoVO().stream().map(pm -> pm.getCodigo()).collect(Collectors.toList()));
            List<BalancoVO> balancoVOCanelamento = getFacade().getBalanco().consultarBalancoMesclado(idProdutosMesclados);

            for (BalancoVO balancoVO : balancoVOCanelamento) {
                balancoVO.setCancelado(true);
                balancoVO.setUsuarioCancelamento(getUsuarioLogado());
                getFacade().getBalanco().cancelar(balancoVO);
                registrarLogObjetoVO(balancoVO,balancoVO.getCodigo(),BalancoControle.NOME_ENTIDADE,0);
            }

        }catch(Exception e){
            e.printStackTrace();
        }

    }

    private BalancoVO criaObjetoBalanco( String descricao) throws Exception {
        BalancoVO balancoVO= new BalancoVO();
        balancoVO.setEmpresa(getEmpresaLogado());
        balancoVO.setUsuarioCadastro(getUsuarioLogado());
        balancoVO.setDescricao(descricao);
        balancoVO.setObservacoes("Mesclado");
        balancoVO.registrarObjetoVOAntesDaAlteracao();
        return balancoVO;
    }

    private void criaBalancoDeProdutoDestino(BalancoControle balancoControle, BalancoVO balancoVO, int quantidadeDeEstoqueProdutosMesclados) {
        ProdutoVO produtoVDestino  = getProdutoVO();
        BalancoItensVO balancoItensVO =new BalancoItensVO();
        balancoItensVO.setProduto(produtoVDestino);
        int estoqueAtualProduto =quantidadeEstoquePorProduto(produtoVDestino.getCodigo());
        balancoItensVO.setQtdeEstoqueAnterior(estoqueAtualProduto);
        int estoqueProdutoDestino =quantidadeDeEstoqueProdutosMesclados +estoqueAtualProduto ;
        balancoItensVO.setQtdeBalanco(estoqueProdutoDestino <0 ?0: estoqueProdutoDestino);
        balancoVO.getItens().add(balancoItensVO);
        balancoControle.setBalancoVO(balancoVO);
        balancoControle.gravar();
    }

    private int criaBalancoDeProdutoOrigem(List<ProdutoMescladoVO> produtoMescladosVO, BalancoControle balancoControle, BalancoVO balancoVO, int quantidadeDeEstoqueProdutosMesclados) {
        for (ProdutoMescladoVO produtoMescladoVO : produtoMescladosVO) {
            ProdutoVO produtoVOrigem  = produtoMescladoVO.getProdutoOrigem();
            BalancoItensVO balancoItensVO =new BalancoItensVO();
            balancoItensVO.setProduto(produtoVOrigem);

            int estoqueAtualProduto =quantidadeEstoquePorProduto(produtoVOrigem.getCodigo());
            balancoItensVO.setQtdeEstoqueAnterior(estoqueAtualProduto);

            if(estoqueAtualProduto >0){
                quantidadeDeEstoqueProdutosMesclados += estoqueAtualProduto;
            }
            balancoItensVO.setQtdeBalanco(0);
            balancoVO.getItens().add(balancoItensVO);
        }
        balancoControle.setBalancoVO(balancoVO);
        balancoControle.gravar();
        return quantidadeDeEstoqueProdutosMesclados;
    }

    public boolean isIntegranteRedeEmpresa() {
        return integranteRedeEmpresa;
    }

    public void setIntegranteRedeEmpresa(boolean integranteRedeEmpresa) {
        this.integranteRedeEmpresa = integranteRedeEmpresa;
    }

    public void setListaSelectItemCategoriaProduto(List<SelectItem> listaSelectItemCategoriaProduto) {
        this.listaSelectItemCategoriaProduto = listaSelectItemCategoriaProduto;
    }

    public List<CategoriaProdutoVO> getCategorias() {
        return categorias;
    }

    public void setCategorias(List<CategoriaProdutoVO> categorias) {
        this.categorias = categorias;
    }

    public List<ProdutoEstoqueVO> getProdutoEstoquesParaCriar() {
        return produtoEstoquesParaCriar;
    }


    public List getListaSelectItemContratoTextoPadrao() {
        return listaSelectItemContratoTextoPadrao;
    }

    public void setListaSelectItemContratoTextoPadrao(List listaSelectItemContratoTextoPadrao) {
        this.listaSelectItemContratoTextoPadrao = listaSelectItemContratoTextoPadrao;
    }

    public String getProdutoMescladoSelecionado() {
        return produtoMescladoSelecionado;
    }

    public void setProdutoMescladoSelecionado(String produtoMescladoSelecionado) {
        this.produtoMescladoSelecionado = produtoMescladoSelecionado;
    }

    public List<ProdutoVO> getListaProdutoMescladoVO() {
        return listaProdutoMescladoVO;
    }

    public void setListaProdutoMescladoVO(List<ProdutoVO> listaProdutoMescladoVO) {
        this.listaProdutoMescladoVO = listaProdutoMescladoVO;
    }
    public void setProdutoEstoquesParaCriar(List<ProdutoEstoqueVO> produtoEstoquesParaCriar) {
        this.produtoEstoquesParaCriar = produtoEstoquesParaCriar;
    }

    public Integer getCategoriaParaNovosProdutosNfe() {
        return categoriaParaNovosProdutosNfe;
    }

    public void setCategoriaParaNovosProdutosNfe(Integer categoriaParaNovosProdutosNfe) {
        this.categoriaParaNovosProdutosNfe = categoriaParaNovosProdutosNfe;
    }

    public List<ProdutoVO> getProdutosSemCodigoDeBarras() {
        return produtosSemCodigoDeBarras;
    }

    public void setProdutosSemCodigoDeBarras(List<ProdutoVO> produtosSemCodigoDeBarras) {
        this.produtosSemCodigoDeBarras = produtosSemCodigoDeBarras;
    }

    public List<SelectItem> getListaSelectItemProduto() {
        return listaSelectItemProduto;
    }

    public void setListaSelectItemProduto(List<SelectItem> listaSelectItemProduto) {
        this.listaSelectItemProduto = listaSelectItemProduto;
    }

    public List<DetalheProduto> getProdutosParaCadastrarOuVincular() {
        return produtosParaCadastrarOuVincular;
    }

    public void setProdutosParaCadastrarOuVincular(List<DetalheProduto> produtosParaCadastrarOuVincular) {
        this.produtosParaCadastrarOuVincular = produtosParaCadastrarOuVincular;
    }

    public String getMsgPrefixoDataAtualizacaoReplicacao() {
        return msgPrefixoDataAtualizacaoReplicacao;
    }

    public void setMsgPrefixoDataAtualizacaoReplicacao(String msgPrefixoDataAtualizacaoReplicacao) {
        this.msgPrefixoDataAtualizacaoReplicacao = msgPrefixoDataAtualizacaoReplicacao;
    }

    public String getMsgAguardandoReplicacao() {
        return msgAguardandoReplicacao;
    }

    public void setMsgAguardandoReplicacao(String msgAguardandoReplicacao) {
        this.msgAguardandoReplicacao = msgAguardandoReplicacao;
    }

    public ApiProxy getApiProxy() {
        return apiProxy;
    }

    public void setApiProxy(ApiProxy apiProxy) {
        this.apiProxy = apiProxy;
    }

    public boolean isEditandoPersonal() {
        return editandoPersonal;
    }

    public void setEditandoPersonal(boolean editandoPersonal) {
        this.editandoPersonal = editandoPersonal;
    }

    public static String getProdutoSel() {
        return produtoSel;
    }

    public static void setProdutoSel(String produtoSel) {
        ProdutoControle.produtoSel = produtoSel;
    }

    public Integer getEstoqueMinimoParaNovosProdutosNfe() {
        return estoqueMinimoParaNovosProdutosNfe;
    }

    public void setEstoqueMinimoParaNovosProdutosNfe(Integer estoqueMinimoParaNovosProdutosNfe) {
        this.estoqueMinimoParaNovosProdutosNfe = estoqueMinimoParaNovosProdutosNfe;
    }

    public List<ProdutoVO> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<ProdutoVO> produtos) {
        this.produtos = produtos;
    }

    public ConfiguracaoProdutoEmpresaPlanoVO getCfgValorProdutoPlano() {
        return cfgValorProdutoPlano;
    }

    public void setCfgValorProdutoPlano(ConfiguracaoProdutoEmpresaPlanoVO cfgValorProdutoPlano) {
        this.cfgValorProdutoPlano = cfgValorProdutoPlano;
    }

    public List<SelectItem> getListaPlanos() {
        return listaPlanos;
    }

    public void setListaPlanos(List<SelectItem> listaPlanos) {
        this.listaPlanos = listaPlanos;
    }

    public boolean isExibirCampoConfigValorGympass() {
        return exibirCampoConfigValorGympass;
    }

    public void setExibirCampoConfigValorGympass(boolean exibirCampoConfigValorGympass) {
        this.exibirCampoConfigValorGympass = exibirCampoConfigValorGympass;
    }

    public boolean isExibirCampoConfigValorTotalpass() {
        return exibirCampoConfigValorTotalpass;
    }

    public void setExibirCampoConfigValorTotalpass(boolean exibirCampoConfigValorTotalpass) {
        this.exibirCampoConfigValorTotalpass = exibirCampoConfigValorTotalpass;
    }

    public boolean isExibirCampoConfigValorGogood() {
        return exibirCampoConfigValorGogood;
    }

    public void setExibirCampoConfigValorGogood(boolean exibirCampoConfigValorGogood) {
        this.exibirCampoConfigValorGogood = exibirCampoConfigValorGogood;
    }

    public void atualizarListaPlano() {
        montarListaPlanos(cfgValorProdutoPlano.getEmpresa().getCodigo());
    }

    public void montarListaPlanos(Integer codEmpresa)   {
        try {
            int empresa = codEmpresa != null ? codEmpresa : 1;

            List<PlanoVO> planos = getFacade().getPlano()
                    .consultarPorCodigoEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            List<SelectItem> itensSelect = new ArrayList<>();
            for (PlanoVO plano : planos) {
                itensSelect.add(new SelectItem(plano.getCodigo(), plano.getCodigo() + " - " + plano.getDescricao()));
            }

            setListaPlanos(itensSelect);

        } catch (Exception ignored) {

        }

    }

}
