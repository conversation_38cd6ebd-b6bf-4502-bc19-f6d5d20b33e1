package controle.plano;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ReplicarRedeEmpresaEnum;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.ReplicarRedeEmpresaCallable;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.plano.ComposicaoModalidadeVO;
import negocio.comuns.plano.ComposicaoVO;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeEmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoAnuidadeParcelaVO;
import negocio.comuns.plano.PlanoCategoriaVO;
import negocio.comuns.plano.PlanoComposicaoVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoCreditoTreinoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoEmpresaVO;
import negocio.comuns.plano.PlanoExcecaoVO;
import negocio.comuns.plano.PlanoHorarioVO;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import negocio.comuns.plano.PlanoProdutoSugeridoVO;
import negocio.comuns.plano.PlanoRecorrenciaParcelaVO;
import negocio.comuns.plano.PlanoRecorrenciaVO;
import negocio.comuns.plano.PlanoRedeEmpresaVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoSugeridoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.plano.enumerador.TipoReajuste;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ColecaoUtils;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.PlanoTipoVO;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.PlanoRecorrencia;
import negocio.oamd.RedeEmpresaVO;
import org.apache.commons.collections.Predicate;
import org.json.JSONObject;
import servicos.discovery.RedeDTO;
import servicos.impl.planoMs.PlanoMsService;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;
import servicos.propriedades.PropsService;
import servicos.util.thread.ThresholdAsyncService;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas planoForm.jsp planoCons.jsp) com as funcionalidades da classe
 * <code>Plano</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see Plano
 * @see PlanoVO
 */
public class PlanoControle extends SuperControle {

    protected List listaSelectItemEmpresa;
    protected List listaSelectItemDuracao;
    protected List listaSelectItemCondicaoPagamento;
    protected List listaSelectItemComposicao;
    protected List listaSelectItemComposicaoAdicionada;
    protected ComposicaoVO composicaoVOValorReferencia;
    protected Integer codigoComposicaoVOValorReferencia;
    protected List listaSelectItemModalidade;
    protected List listaSelectItemVezesSemana;
    protected List listaSelectItemHorario;
    protected List listaSelectItemPlanoProdutoSugeriodo;
    protected List listaSelectItemProdutoPadrao;
    protected List listaSelectItemProdutoTaxaCancelamento;
    protected List listaSelectItemPlanoTextoPadrao;
    protected List listaSelectItemTermoAceite;
    protected String campoConsultaPlanoProduto;
    protected String valorConsultaPlanoProduto;
    protected List listaConsultaPlanoProduto;
    protected Double valorMensal;
    protected Boolean apresentarPlamoModalidadeVesezSemana;
    protected Boolean apresentarPlamoCondicaoPagamento;
    protected Boolean apresentarPanelCarencia;
    boolean habilitarCampoVezesPorSemana = true;
    boolean habilitarCampoHorario = true;
    boolean habilitarCampoNrMeses = true;
    boolean habilitarCampoCondicaoPagamento = true;
    private PlanoVO planoVO;
    /**
     * Interface <code>PlanoInterfaceFacade</code> responsável pela interconexão
     * da camada de controle com a camada de negócio. Criando uma independência
     * da camada de controle com relação a tenologia de persistência dos dados
     * (DesignPatter: Façade).
     */
    private PlanoDuracaoVO planoDuracaoVO;
    private PlanoDuracaoVO planoDuracaoVOSelecionado;
    private PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO;
    private PlanoComposicaoVO planoComposicaoVO;
    private PlanoModalidadeVO planoModalidadeVOSelecionado;
    private PlanoModalidadeVO planoModalidadeVO;
    private PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaVO;
    private PlanoHorarioVO planoHorarioVO;
    private PlanoProdutoSugeridoVO planoProdutoSugeridoVO;
    private String diaVencimento;
    private String campoConsultaDescontoAntecipado;
    private String valorConsultaDescontoAntecipado;
    private List<DescontoVO> listaConsultaDescontoAntecipado;
    //atributos usados em cadastro de plano no projeto de recorrência
    private boolean mostrarAbaDuracoes = true;
    private boolean mostrarAbaRecorrencia = false;
    private boolean mostrarCampoRegimeRecorrencia = true;
    @NaoControlarLogAlteracao
    private boolean processandoReajuste = false;
    @NaoControlarLogAlteracao
    private Integer pgrAtual = 0;
    @NaoControlarLogAlteracao
    private Integer pgrTotal = 0;
    private List<SelectItem> listaVezesParcelarAdesao = null;
    private Double valorAjustadoModalidade = 0.0;
    private String msgModal = "";
    private String onCompleteGravar = "";
    private PlanoExcecaoVO planoExcecaoVOSelecionado = new PlanoExcecaoVO();
    private String textoLinkRenovacaoAutomatica;
    private List<ContratoVO> listaContratosRenovacaoAutomatica;
    private boolean mostrarConfiguracaoDuracaoCreditoTreino = false;
    private PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO;
    private List<SelectItem> listaTipoHorarioCreditoTreino;
    private Boolean desabilitarVendaCreditoTreino = false;
    private Integer qtdContratosCancelamentoAutomatico;
    private String msgContratosCancelamentoAutomatico;
    private boolean apresentarBtnContratosCancelamentoAutomatico = false;
    private String titlePlanoCreditoTreino = "";
    private List<SelectItem> listaTipoAlteracao;
    private String tipoAlteracaoSelecionado = PlanoVO.TIPO_ALTERACAO_DETALHADO;
    private double valorMensalNovo = 0;
    private double valorMensalAntigo = 0;
    private Integer codigoContratoSelecionado = 0;
    private Integer duracaoSelecionado = 0;
    private String tipoPlano;
    private List<SelectItem> listaTipoPlano = new ArrayList<SelectItem>();
    private List<SelectItem> listaVezesParcelarMatricula = null;
    private List<SelectItem> listaSelectConvenioCobrancaPrivateLabel;

    private ConfiguracaoSistemaVO configuracaoSistema;
    private boolean checkTodasAcessoEmpresas;
    private boolean checkTodasVendaEmpresas;
    private Integer qtdContratosCancelamentoProporcional;
    private String msgContratosCancelamentoProporcional;
    private boolean apresentarBtnContratosCancelamentoProporcional = false;
    private PlanoRecorrenciaParcelaVO parcelaValorAlterado = new PlanoRecorrenciaParcelaVO();
    private List<SelectItem> listaParcelasParaAlteracaoValor = new ArrayList<SelectItem>();
    private List<SelectItem> planoTipos = new ArrayList<SelectItem>();
    private int qtdeContratosComInicioAposInicioMinimo = 0;
    private Date inicioMinimoContratoSemAlteracao;
    private String abaSelecionada;
    private ArrayList<String> abasSelecionadas;
    private PlanoVO planoVOClone;
    private boolean historico[];
    private String msgAlert;
    private PlanoAnuidadeParcelaVO planoAnuidadeParcelaVO;
    private List<SelectItem> listaParcelasAnuidadeParcela;
    private List<PlanoRedeEmpresaVO> listaPlanoRedeEmpresa;
    private boolean integranteRedeEmpresa;

    private PlanoCategoriaVO planoCategoriaVO;
    private List listaSelectItemPlanoCategoria;

    public PlanoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setMensagemID("");
        criarListaHorarioCreditoTreino();
        criarListaTipoReajuste();
        montarListaSelectTipoPlano();
        montarListaPlanoTipos();
        abasSelecionadas = new ArrayList<>();
        historico = new boolean[3];
    }

    public void montarListaPlanoTipos() {
        try {
            List<PlanoTipoVO> tipos = getFacade().getPlanoTipo().consultar(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setPlanoTipos(new ArrayList<SelectItem>());
            getPlanoTipos().add(new SelectItem(null, ""));
            for (PlanoTipoVO tipo : tipos) {
                getPlanoTipos().add(new SelectItem(tipo.getCodigo(), tipo.getNome()));
            }
        } catch (Exception e) {
            montarErroComLog(e);
        }
    }

    public void inicializarUsuarioLogado() {
        try {
            planoVO.setUsuarioVO(getUsuarioLogado());
        } catch (Exception ignored) {
        }
    }

    public void inicializarEmpresaLogado() {
        try {
            planoVO.setEmpresa(getEmpresaLogado());
        } catch (Exception ignored) {
        }
    }

    private void criarListaTipoReajuste() {
        this.listaTipoAlteracao = new ArrayList<SelectItem>();
        this.listaTipoAlteracao.add(new SelectItem(PlanoVO.TIPO_ALTERACAO_DETALHADO, PlanoVO.TIPO_ALTERACAO_DETALHADO));
        this.listaTipoAlteracao.add(new SelectItem(PlanoVO.TIPO_ALTERACAO_SIMPLES, PlanoVO.TIPO_ALTERACAO_SIMPLES));
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>Plano</code> para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        setPlanoVO(new PlanoVO());
        inicializarUsuarioLogado();
        inicializarEmpresaLogado();
        inicializarListasSelectItemTodosComboBox();
        inicializarConfiguracoesSistema();
        setPlanoHorarioVO(new PlanoHorarioVO());
        setPlanoModalidadeVO(new PlanoModalidadeVO());
        setPlanoModalidadeVOSelecionado(new PlanoModalidadeVO());
        setPlanoModalidadeVezesSemanaVO(new PlanoModalidadeVezesSemanaVO());
        setPlanoComposicaoVO(new PlanoComposicaoVO());
        setDesabilitarVendaCreditoTreino(false);
        setPlanoCondicaoPagamentoVO(new PlanoCondicaoPagamentoVO());
        setPlanoDuracaoVO(new PlanoDuracaoVO());
        setPlanoDuracaoVOSelecionado(new PlanoDuracaoVO());
        setPlanoProdutoSugeridoVO(new PlanoProdutoSugeridoVO());
        setValorMensal(0.0);
        adicionarObjDefault();
        setControleConsulta(new ControleConsulta());
        setListaConsulta(new ArrayList());
        setValorConsultaPlanoProduto("");
        setCampoConsultaPlanoProduto("");
        setListaConsultaPlanoProduto(new ArrayList());
        setValorConsultaDescontoAntecipado("");
        setCampoConsultaDescontoAntecipado("");
        setListaConsultaDescontoAntecipado(new ArrayList());
        setApresentarPlamoModalidadeVesezSemana(false);
        setApresentarPlamoCondicaoPagamento(false);
        this.mostrarConfiguracaoDuracaoCreditoTreino = false;
        setApresentarPanelCarencia(false);
        limparMsg();
        //inicializando atributos de recorrencia
        setMostrarAbaDuracoes(true);
        setMostrarCampoRegimeRecorrencia(true);
        setMostrarAbaRecorrencia(false);
        getPlanoVO().setPlanoRecorrencia(new PlanoRecorrenciaVO());
        getPlanoVO().getPlanoRecorrencia().registrarObjetoVOAntesDaAlteracao();
        getPlanoVO().registrarObjetoVOAntesDaAlteracao();
        this.listaContratosRenovacaoAutomatica = new ArrayList<ContratoVO>();
        this.textoLinkRenovacaoAutomatica = "";
        montarTitlePlanoCreditoTreino();
        habilitarCampoCondicaoPagamento = true;
        habilitarCampoHorario = true;
        habilitarCampoNrMeses = true;
        habilitarCampoVezesPorSemana = true;
        getPlanoVO().setEmpresas(getFacade().getPlanoEmpresa().consultarTodas(null));
        montarListaParcelasParaAlteracaoValor();
        montarListaPlanoTipos();
        preencherCamposPadrao();
        setPlanoCategoriaVO(new PlanoCategoriaVO());
        planoVOClone = (PlanoVO) planoVO.getClone(false);
        planoVOClone.setPlanoRecorrencia((PlanoRecorrenciaVO) planoVO.getPlanoRecorrencia().getClone(false));
        setAbaSelecionada("dadosPlano");
        return "editar";
    }

    private void preencherCamposPadrao() {
        for (Object item : getListaSelectItemProdutoPadrao()) {
            SelectItem selectItem = (SelectItem) item;
            if (selectItem.getLabel().trim().equals("MÊS DE REFERÊNCIA PLANO")) {
                getPlanoVO().getProdutoPadraoGerarParcelasContrato().setCodigo((Integer) selectItem.getValue());
                break;
            }
        }

        for (Object item : getListaSelectItemProdutoTaxaCancelamento()) {
            SelectItem selectItem = (SelectItem) item;
            if (selectItem.getLabel().trim().equals("CUSTO ADMINISTRATIVO DO CANCELAMENTO")) {
                getPlanoVO().getProdutoTaxaCancelamento().setCodigo((Integer) selectItem.getValue());
                break;
            }
        }
    }

    public void criarListaHorarioCreditoTreino() {
        this.listaTipoHorarioCreditoTreino = new ArrayList<SelectItem>();
        this.listaTipoHorarioCreditoTreino.add(new SelectItem(0, ""));
        for (TipoHorarioCreditoTreinoEnum obj : TipoHorarioCreditoTreinoEnum.values()) {
            this.listaTipoHorarioCreditoTreino.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
    }

    private Boolean existemContratosParaOPlanoSelecionado() throws Exception {
        return getFacade().getContrato().consultaQuantidadePorPlano(planoVO.getCodigo()).longValue() > 0;
    }


    public void clonar() {
        try {
            planoVO = (PlanoVO) planoVO.getClone(false);
            planoVO.setObjetoVOAntesAlteracao(new PlanoVO());
            planoVO.getPlanoRecorrencia().setObjetoVOAntesAlteracao(new PlanoRecorrenciaVO());
            planoVO.setCodigo(new Integer(0));
            planoVO.setNovoObj(true);
            planoVO.setDescricao("Cópia " + getPlanoVO().getDescricao());
            removerProdutosSugeridosPlanoClonado();
            montarListaVezesSemana(planoVO);
            setDesabilitarVendaCreditoTreino(false);
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void removerProdutosSugeridosPlanoClonado() throws Exception {
        List<Integer> codigosProdutosSugeridos = new ArrayList<>();
        Iterator i = planoVO.getPlanoProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            PlanoProdutoSugeridoVO objExistente = (PlanoProdutoSugeridoVO) i.next();
            if (!objExistente.getProduto().getDesativado() && !objExistente.getProduto().getTipoProduto().equals("RE") && !objExistente.getProduto().getTipoProduto().equals("RN") && !objExistente.getProduto().getTipoProduto().equals("MA")) {
                codigosProdutosSugeridos.add(objExistente.getProduto().getCodigo());
            }
        }
        for (Integer codigoProduto : codigosProdutosSugeridos) {
            planoVO.excluirObjPlanoProdutoSugeridoVOs(codigoProduto);
        }
    }


    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>Plano</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() throws Exception {
        inicializarPossuiRedeEmpresa();
        setQtdContratosCancelamentoAutomatico(0);
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        novo();
        PlanoVO obj = getFacade().getPlano().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
        obj.setPlanoProdutoSugeridoVOs(getFacade().getPlanoProdutoSugerido().consultarPlanoProdutoSugeridos(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
        inicializarUsuarioLogado();
        inicializarAtributosRelacionados(obj);
        try {
            setPlanoVO(obj);
            getPlanoVO().setNovoObj(false);
            //analisa qual aba de recorrencia ou de duração deverá aparecer
            if (getPlanoVO().getRegimeRecorrencia() == null || !getPlanoVO().getRegimeRecorrencia()) {
                setMostrarCampoRegimeRecorrencia(false);
                setMostrarAbaDuracoes(true);
                setMostrarAbaRecorrencia(false);
            } else {
                setMostrarCampoRegimeRecorrencia(true);
                setMostrarAbaDuracoes(false);
                setMostrarAbaRecorrencia(true);
            }
            getPlanoVO().setPlanoDuracaoVOs(Ordenacao.ordenarLista(getPlanoVO().getPlanoDuracaoVOs(), "numeroMeses"));
            getPlanoVO().registrarObjetoVOAntesDaAlteracao();
            getPlanoVO().getPlanoRecorrencia().registrarObjetoVOAntesDaAlteracao();
            setDesabilitarVendaCreditoTreino(existemContratosParaOPlanoSelecionado());
            getPlanoVO().setEmpresas(getFacade().getPlanoEmpresa().consultarTodas(obj.getCodigo()));
            processarCheckTodasEmpresas();
            if (getPlanoVO().getInicioMinimoContrato() != null) {
                setInicioMinimoContratoSemAlteracao(new Date(getPlanoVO().getInicioMinimoContrato().getTime()));
            }
            montarListaPlanoTipos();
            montarListaParcelasMatricula();
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        montarTitlePlanoCreditoTreino();
        setValorMensal(0.0);
        getPlanoVO().getListaDiasVencimento().remove("");
        this.tipoAlteracaoSelecionado = PlanoVO.TIPO_ALTERACAO_DETALHADO;
        this.valorMensalAntigo = 0;
        this.valorMensalNovo = 0;
        this.codigoContratoSelecionado = 0;
        this.duracaoSelecionado = 0;
        montarListaParcelasParaAlteracaoValor();
        planoVOClone = (PlanoVO) planoVO.getClone(false);
        planoVOClone.setPlanoRecorrencia((PlanoRecorrenciaVO) planoVO.getPlanoRecorrencia().getClone(false));
        historico[0] = isCheckTodasAcessoEmpresas();
        historico[1] = isCheckTodasVendaEmpresas();
        historico[2] = planoVO.isPermitirAcessoSomenteNaEmpresaVendeuContrato();
        montarListaParcelasAnuidadeParcela();
        return "editar";
    }

    public void replicarAutomaticoTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (PlanoRedeEmpresaVO obj : getListaPlanoRedeEmpresa()) {
                if (obj.getDataAtualizacaoInformada()) {
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PLANO, null, null, null, null, obj, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void processarCheckTodasEmpresas() {
        int empresasAcessoSelecionadas = 0;
        int empresasVendaSelecionadas = 0;
        int totalEmpresas = planoVO.getEmpresas().size();
        for (PlanoEmpresaVO planoEmpresa : planoVO.getEmpresas()) {
            if (planoEmpresa.isAcesso())
                empresasAcessoSelecionadas++;

            if (planoEmpresa.isVenda())
                empresasVendaSelecionadas++;
        }

        setCheckTodasAcessoEmpresas(empresasAcessoSelecionadas == totalEmpresas);
        setCheckTodasVendaEmpresas(empresasVendaSelecionadas == totalEmpresas);
    }

    public void processarPlanoSelecionado(PlanoVO planoParametro) throws Exception {
        novo();
        PlanoVO obj = getFacade().getPlano().consultarPorChavePrimaria(planoParametro.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        obj.setPlanoProdutoSugeridoVOs(getFacade().getPlanoProdutoSugerido().consultarPlanoProdutoSugeridos(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
        inicializarUsuarioLogado();
        inicializarAtributosRelacionados(obj);
        try {
            setPlanoVO(obj);
            getPlanoVO().setNovoObj(false);
            //analisa qual aba de recorrencia ou de duração deverá aparecer
            if (getPlanoVO().getRegimeRecorrencia() == null || !getPlanoVO().getRegimeRecorrencia()) {
                setMostrarCampoRegimeRecorrencia(false);
                setMostrarAbaDuracoes(true);
                setMostrarAbaRecorrencia(false);
            } else {
                setMostrarCampoRegimeRecorrencia(true);
                setMostrarAbaDuracoes(false);
                setMostrarAbaRecorrencia(true);
            }
            getPlanoVO().setPlanoDuracaoVOs(Ordenacao.ordenarLista(getPlanoVO().getPlanoDuracaoVOs(), "numeroMeses"));
            getPlanoVO().registrarObjetoVOAntesDaAlteracao();
            getPlanoVO().getPlanoRecorrencia().registrarObjetoVOAntesDaAlteracao();
            setDesabilitarVendaCreditoTreino(existemContratosParaOPlanoSelecionado());
        } catch (Exception e) {
            montarErro(e);
        }
        montarTitlePlanoCreditoTreino();
        setValorMensal(0.0);
        getPlanoVO().getListaDiasVencimento().remove("");
        this.tipoAlteracaoSelecionado = PlanoVO.TIPO_ALTERACAO_DETALHADO;
        this.valorMensalAntigo = 0;
        this.valorMensalNovo = 0;
        this.codigoContratoSelecionado = 0;
        this.duracaoSelecionado = 0;
    }

    /**
     * Método responsável inicializar objetos relacionados a classe
     * <code>PlanoVO</code>. Esta inicialização é necessária por exigência da
     * tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(PlanoVO obj) {
        this.mostrarConfiguracaoDuracaoCreditoTreino = false;
        if (obj.getEmpresa() == null) {
            obj.setEmpresa(new EmpresaVO());
        }
    }

    public void validarDadosParaGravar() {
        try {
            limparMsg();
            boolean existeDescricao = getFacade().getPlano().existePlanoComMesmoNome(getPlanoVO());
            if (existeDescricao) {
                throw new ConsistirException("Já existe um plano cadastrado com esta descrição");
            }

            if (getPlanoVO().getPlanoRecorrencia() != null &&
                    getPlanoVO().getPlanoRecorrencia().getParcelas() != null &&
                    getPlanoVO().getPlanoRecorrencia().getParcelas().size() > 0) {
                Double valorMensal = getPlanoVO().getPlanoRecorrencia().getValorMensal();
                for (PlanoRecorrenciaParcelaVO parcela : getPlanoVO().getPlanoRecorrencia().getParcelas()) {

                    if (parcela.getValorFinal().equals(valorMensal)) {
                        throw new ConsistirException("O valor da parcela " + parcela.getNumero() + " é igual ao da mensalidade, portanto, não adicione na configuração.");
                    }

                    if (parcela.getValorFinal() > valorMensal) {
                        throw new ConsistirException("O valor da parcela " + parcela.getNumero() + " ultrapassa o valor da mensalidade.");
                    }

                }
            }

            if (getPlanoVO().getRegimeRecorrencia() && getPlanoVO().getPlanoRecorrencia().getDuracaoPlano() < 12) {
                getPlanoVO().setRenovarAnuidadeAutomaticamente(false);
            }
            if ((getPlanoVO().getSite() || getPlanoVO().isVendaCreditoTreino() || getPlanoVO().getRegimeRecorrencia()) && getPlanoVO().getPlanoComposicaoVOs().size() != 0) { // Site, credito e recorrencia não permite pacote.
                getPlanoVO().setPlanoComposicaoVOs(new ArrayList());
            }
            validarExigenciasPlanoSite();
            if (getPlanoVO().getPlanoDuracaoVOs().size() > 1 || getPlanoVO().getRegimeRecorrencia()) {
                int qtdCarenciaPreenchidas = 0;
                for (Object obj : getPlanoVO().getPlanoDuracaoVOs()) {
                    PlanoDuracaoVO pDuracao = (PlanoDuracaoVO) obj;
                    if (pDuracao.getCarencia() > 0) {
                        qtdCarenciaPreenchidas++;
                    }
                }

                if (qtdCarenciaPreenchidas < (getPlanoVO().getPlanoDuracaoVOs().size() - 3)) {
                    setMsgModal("Não foram preenchidas todas as configurações referentes à férias, deseja prosseguir?");
                    setOnCompleteGravar("Richfaces.showModalPanel('mdlAviso');");
                } else {
                    gravarPlano();
                }
            } else if (getPlanoVO().getPlanoDuracaoVOs().isEmpty()) {
                gravarPlano();
            } else {
                PlanoDuracaoVO pDuracao = (PlanoDuracaoVO) getPlanoVO().getPlanoDuracaoVOs().get(0);
                if (pDuracao.getNumeroMeses() != 1 && pDuracao.getCarencia() == 0) {
                    setMsgModal("As férias estão com o valor de 0 dias, deseja prosseguir?");
                    setOnCompleteGravar("Richfaces.showModalPanel('mdlAviso');");
                } else {
                    gravarPlano();
                }
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    private void validarExigenciasPlanoSite() throws ConsistirException {
        if (getPlanoVO().getSite()) {
            for (Object obj : getPlanoVO().getPlanoModalidadeVOs()) {
                PlanoModalidadeVO pm = (PlanoModalidadeVO) obj;
                if (pm.getModalidade().isUtilizarTurma()) {
                    throw new ConsistirException("Um plano SITE pode ter apenas modalidades sem turmas.");
                }
            }

            if (getPlanoVO().getPlanoDuracaoVOs().size() > 1) {
                throw new ConsistirException("Plano SITE pode ter apenas uma duração.");
            }

            if (getPlanoVO().getPlanoDuracaoVOs().size() != 0 && getPlanoVO().getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().size() > 1) {
                throw new ConsistirException("Plano SITE pode ter apenas uma condição de pagamento.");
            }

            if (getPlanoVO().getPlanoHorarioVOs().size() != 0 && getPlanoVO().getPlanoHorarioVOs().size() > 1) {
                throw new ConsistirException("Plano SITE pode ter apenas um horário.");
            }
            if (getPlanoVO().getRegimeRecorrencia()) {

                for (Object obj : getPlanoVO().getPlanoProdutoSugeridoVOs()) {
                    PlanoProdutoSugeridoVO prdtSugerido = (PlanoProdutoSugeridoVO) obj;

                    if (prdtSugerido.getProduto().getTipoProduto().equalsIgnoreCase(TipoProduto.MATRICULA.getCodigo()) && prdtSugerido.getValorProduto() > 0.0) {
                        throw new ConsistirException("Você está configurando um plano que é de \"Regime Recorrência\" e ao mesmo tempo \"Site\",portanto o valor da matrícula não pode ser maior que zero.");
                    }
                }
            }
            for (Object obj : getPlanoVO().getPlanoProdutoSugeridoVOs()) {
                PlanoProdutoSugeridoVO prdtSugerido = (PlanoProdutoSugeridoVO) obj;

                if (!prdtSugerido.isObrigatorio()) {
                    throw new ConsistirException("O Plano Site só permite produtos obrigatórios! Vá na aba \"Produtos Sugeridos\" e configure todos como obrigatórios.");
                }
            }
        }
    }

    private void gravarPlano() throws IllegalAccessException, InstantiationException {
        setMsgModal("");
        setOnCompleteGravar("");
        gravar();
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe <code>Plano</code>. Caso o objeto seja novo (ainda não gravado
     * no BD) é acionado a operação <code>incluir()</code>. Caso contrário é
     * acionado o <code>alterar()</code>. Se houver alguma inconsistência o
     * objeto não é gravado, sendo re-apresentado para o usuário juntamente com
     * uma mensagem de erro.
     */
    public void gravar() throws InstantiationException, IllegalAccessException {
        try {
            if ((getPlanoVO().getSite() || getPlanoVO().isVendaCreditoTreino() || getPlanoVO().getRegimeRecorrencia()) && getPlanoVO().getPlanoComposicaoVOs().size() != 0) { // Site, credito e recorrencia não permite pacote.
                getPlanoVO().setPlanoComposicaoVOs(new ArrayList());
            }
            validarExigenciasPlanoSite();
//            if (configuracaoSistema.isUtilizarTipoPlano() &&
//                    (planoVO.getPlanoTipo().getCodigo() == null || planoVO.getPlanoTipo().getCodigo().equals(0))
//            ) {
//                throw new ValidacaoException("O tipo do plano precisa ser informado");
//            }
            if(planoVO.getRestringeVendaPorCategoria() && planoVO.getPlanoCategoriaVOs().isEmpty()){
                throw new ValidacaoException("A categoria do plano precisa ser informado");
            }
            if (!planoVO.getSite() && !getConfiguracaoSistema().isDefinirDataInicioPlanosRecorrencia()) {
                planoVO.setInicioMinimoContrato(null);
            }
            if (!planoVO.getRegimeRecorrencia()) {
                planoVO.setConvenioCobrancaPrivateLabel(new ConvenioCobrancaVO());
            }
            if (planoVO.isNovoObj()) {
                planoVO.atualizaStringVencimentos();
                //Retirado porque avaliado com o Humberto e não há necessidade desta validação já que o clube já grava isto quando utilizado pelo academia
//                if (!getEmpresaLogado().isTrabalharComPontuacao()) {
//                    if (planoVO.getPontos() > 0) {
//                        throw new Exception("Para cadastrar plano por pontos é necessário que a configuração desta função esteja ativada. Vá nas configurações da empresa, procure pela configuração 'Habilitar Pontuação por plano' e habilite esta função.");
//                    }
//                }
                getFacade().getPlano().incluir(planoVO);
                notificarRecursoEmpresaPlano();
                planoVOClone = (PlanoVO) planoVO.getClone(false);
                planoVOClone.setPlanoRecorrencia((PlanoRecorrenciaVO) planoVO.getPlanoRecorrencia().getClone(false));
                incluirLogInclusao();
            } else {
                if (!getEmpresaLogado().isTrabalharComPontuacao()) {
//                    if (planoVO.getPontos() > 0) {
//                        throw new Exception("Para cadastrar plano por pontos é necessário que a configuração desta função esteja ativada. Vá nas configurações da empresa, procure pela configuração 'Habilitar Pontuação por plano' e habilite esta função.");
//                    }
                }

                if (getConfiguracaoSistema().isControleAcessoMultiplasEmpresasPorPlano() &&
                        planoVO.getEmpresas().size() == 0) {
                    throw new Exception("Você precisa escolher uma empresa para esse plano.");
                }
//                if(getEmpresaLogado().isTrabalharComPontuacao() && getFacade().getItemCampanha().existeItemCampanha(getPlanoVO().getCodigo(), 2)) {
//                    getFacade().getItemCampanha().alterarPontos(getPlanoVO().getCodigo(), getPlanoVO().getPontos(), 2);
//                }
                getFacade().getPlano().alterar(planoVO);
                incluirLogAlteracao();
                posEdicao();
                abasSelecionadas = new ArrayList<>();
            }
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
                replicarAutomaticoTodas();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
            notificarRecursoEmpresaPlano();
            planoVOClone = (PlanoVO) planoVO.getClone(false);
            planoVOClone.setPlanoRecorrencia((PlanoRecorrenciaVO) planoVO.getPlanoRecorrencia().getClone(false));

            ThresholdAsyncService.makeAsyncRequest((service) -> {
                        String url = PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/config/cache/plano/reload?key=" + JSFUtilities.getFromSession(JSFUtilities.KEY);
                        service.executeRequestInner(url, new HashMap<>());
                    }, 3,
                    30000,
                    "Não foi possível atualizar a cache de planos na API-ZW");


        } catch (ConsistirException conEx) {
            if (!UteisValidacao.emptyString(conEx.getAba())) {
                setAbaSelecionada(conEx.getAba());
            }
            montarErro(conEx);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void replicarTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (PlanoRedeEmpresaVO obj : getListaPlanoRedeEmpresa()) {
                if (!obj.getDataAtualizacaoInformada()) {
                    obj.setSelecionado(true);
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PLANO, null, null, null, null, obj, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarSelecionadas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (PlanoRedeEmpresaVO obj : getListaPlanoRedeEmpresa()) {
                if (obj.isSelecionado()) {
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PLANO, null, null, null, null, obj, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void limparReplicar() {
        for(PlanoRedeEmpresaVO obj : getListaPlanoRedeEmpresa()){
            obj.setSelecionado(false);
        }
    }

    public void prepararListaReplicarEmpresa() {
        try {
            getListaPlanoRedeEmpresa().clear();
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            for (RedeDTO redeDTO : redeEmpresaDataDTO.getRedeEmpresas()) {
                if (redeDTO.getChave().toLowerCase().trim().equals(getKey().toLowerCase().trim())) {
                    continue;
                }
                PlanoRedeEmpresaVO planoRedeEmpresaVO = getFacade().getPlanoRedeEmpresa().consultarPorChavePlano(redeDTO.getChave(), planoVO.getCodigo(), redeDTO.getEmpresaZw());
                if (planoRedeEmpresaVO == null) {
                    planoRedeEmpresaVO = new PlanoRedeEmpresaVO();
                    planoRedeEmpresaVO.setMensagemSituacao("AGUARDANDO REPLICAR PLANO");
                }
                planoRedeEmpresaVO.setCodigoEmpresaDestino(redeDTO.getEmpresaZw());
                planoRedeEmpresaVO.setChave(redeDTO.getChave().toLowerCase().trim());
                planoRedeEmpresaVO.setNomeUnidade(redeDTO.getNomeFantasia());
                planoRedeEmpresaVO.setRedeDTO(redeDTO);
                getListaPlanoRedeEmpresa().add(planoRedeEmpresaVO);
            }
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void replicarPlanoRedeEmpresaUnica(PlanoRedeEmpresaVO obj) {
        try {
            replicarPlanoRedeEmpresa(obj);
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            obj.setMensagemSituacao("ERRO: " + ex.getMessage());
            try {
                getFacade().getPlanoRedeEmpresa().alterarMensagemSituacao(planoVO.getCodigo(), obj.getChave(), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());
            } catch (Exception e) {
            }
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void retirarVinculoReplicacao() {
        limparMsg();
        PlanoRedeEmpresaVO obj = (PlanoRedeEmpresaVO) context().getExternalContext().getRequestMap().get("planoRedeEmpresaReplicacao");
        try {
            obj.setDataatualizacao(null);
            obj.setMensagemSituacao("AGUARDANDO REPLICAR PLANO");
            getFacade().getPlanoRedeEmpresa().limparDataAtualizacao(planoVO.getCodigo(), obj.getChave(), obj.getCodigoEmpresaDestino());
            getFacade().getPlanoRedeEmpresa().alterarMensagemSituacao(planoVO.getCodigo(), obj.getChave(), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    public void replicarPlanoRedeEmpresaGeral() {
        PlanoRedeEmpresaVO obj = (PlanoRedeEmpresaVO) context().getExternalContext().getRequestMap().get("planoRedeEmpresaReplicacao");
        replicarPlanoRedeEmpresaUnica(obj);
    }

    public void replicarPlanoRedeEmpresa(PlanoRedeEmpresaVO obj) throws Exception {

        PlanoRedeEmpresaVO planoRedeEmpresaVO = getFacade().getPlanoRedeEmpresa().consultarPorChavePlano(obj.getChave(), planoVO.getCodigo(), obj.getCodigoEmpresaDestino());
        if (planoRedeEmpresaVO == null) {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String urlOrigemPlanoMs = redeEmpresaDataDTO.getServiceUrls().getPlanoMsUrl();

            JSONObject clonePlanoOrigem = PlanoMsService.clonarPlano(planoVO.getCodigo(), urlOrigemPlanoMs, getKey());
            clonePlanoOrigem.put("empresa", obj.getCodigoEmpresaDestino());
            if (!Uteis.isAmbienteDesenvolvimentoTeste()) {
                clonePlanoOrigem.put("descricao", clonePlanoOrigem.getString("descricao").toUpperCase().replace("CÓPIA DE ", ""));
            }
            planoRedeEmpresaVO = new PlanoRedeEmpresaVO(planoVO.getCodigo(), obj.getChave(), null, obj.getCodigoEmpresaDestino());
            getFacade().getPlanoRedeEmpresa().inserir(planoRedeEmpresaVO);
            // Plano não tem na outra academia da rede, então inclui
            JSONObject novoPlano = PlanoMsService.replicarPlano(clonePlanoOrigem, obj.getRedeDTO().getPlanoMsUrl(), obj.getRedeDTO().getChave(), obj.getCodigoEmpresaDestino());
            planoRedeEmpresaVO.setPlanoReplicado(novoPlano.getInt("codigo"));
            planoRedeEmpresaVO.setDataatualizacao(new Date());
            planoRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(planoRedeEmpresaVO.getDataatualizacao()));
            obj.setPlanoReplicado(novoPlano.getInt("codigo"));
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getPlanoRedeEmpresa().alterarDataAtualizacao(planoVO.getCodigo(), obj.getChave(), novoPlano.getInt("codigo"), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());
        } else {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String urlOrigemPlanoMs = redeEmpresaDataDTO.getServiceUrls().getPlanoMsUrl();

            JSONObject clonePlanoOrigem = PlanoMsService.clonarPlano(planoVO.getCodigo(), urlOrigemPlanoMs, getKey());
            if (!Uteis.isAmbienteDesenvolvimentoTeste()) {
                clonePlanoOrigem.put("descricao", clonePlanoOrigem.getString("descricao").toUpperCase().replace("CÓPIA DE ", ""));
            }
            JSONObject novoPlano = PlanoMsService.replicarPlano(clonePlanoOrigem, obj.getRedeDTO().getPlanoMsUrl(), obj.getRedeDTO().getChave(), obj.getCodigoEmpresaDestino());
            planoRedeEmpresaVO.setPlanoReplicado(novoPlano.getInt("codigo"));
            planoRedeEmpresaVO.setDataatualizacao(new Date());
            planoRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(planoRedeEmpresaVO.getDataatualizacao()));
            obj.setPlanoReplicado(novoPlano.getInt("codigo"));
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getPlanoRedeEmpresa().alterarDataAtualizacao(planoVO.getCodigo(), obj.getChave(), novoPlano.getInt("codigo"), obj.getMensagemSituacao(), obj.getCodigoEmpresaDestino());
        }
    }

    private void posEdicao() throws Exception {
        planoVO.setAtualizarEmpresas(false);
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * PlanoCons.jsp. Define o tipo de consulta a ser executada, por meio de
     * ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getPlano().consultarPorCodigo(valorInt, planoVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getPlano().consultarPorDescricao(getControleConsulta().getValorConsulta(), planoVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeEmpresa")) {
                objs = getFacade().getPlano().consultarPorNomeEmpresa(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getControleConsulta().getCampoConsulta().equals("vigenciaDe")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getPlano().consultarPorVigenciaDe(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), planoVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getControleConsulta().getCampoConsulta().equals("vigenciaAte")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getPlano().consultarPorVigenciaAte(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), planoVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }

    }

    public void inicializarConfiguracoesSistema() throws Exception {
        setConfiguracaoSistema(getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS));
    }

    public void calcularQtdeCreditoTreinoHorarioTurma() {
        if (this.planoDuracaoCreditoTreinoVO != null && this.planoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA) {
            double qtdSemanasMes = 4;
            if (getPlanoVO().getQtdSemanasAno() == 52) {
                qtdSemanasMes = 4.34524;
            }

            this.planoDuracaoCreditoTreinoVO.setQuantidadeCreditoCompra((int) (this.planoDuracaoVOSelecionado.getNumeroMeses() * qtdSemanasMes) * this.planoDuracaoCreditoTreinoVO.getNumeroVezesSemana());
            if (this.planoDuracaoVOSelecionado.getQuantidadeDiasExtra() > 7 && !this.getPlanoVO().isCreditoTreinoNaoCumulativo()) {
                int qtdeSemana = Math.abs(this.planoDuracaoVOSelecionado.getQuantidadeDiasExtra() / 7);
                this.planoDuracaoCreditoTreinoVO.setQuantidadeCreditoCompra(this.planoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra() + (qtdeSemana * this.planoDuracaoCreditoTreinoVO.getNumeroVezesSemana()));
            }
            this.planoDuracaoCreditoTreinoVO.setQuantidadeCreditoMensal((int) (this.planoDuracaoCreditoTreinoVO.getNumeroVezesSemana() * qtdSemanasMes));
        }
    }

    public boolean isMostrarColunaTotalDias() {
        for (PlanoDuracaoVO planoDuracaoVO : this.planoVO.getPlanoDuracaoVOs()) {
            if (planoDuracaoVO.getQuantidadeDiasExtra() > 0) {
                return true;
            }
        }
        return false;
    }

    public void consultarPlanoProduto() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getCampoConsultaPlanoProduto().equals("nome")) {
                objs = getFacade().getProduto().consultarPorDescricaoTipoProdutoSemPMCDDEAtivo(getValorConsultaPlanoProduto(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultaPlanoProduto(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setListaConsultaPlanoProduto(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarPlanoProduto() {
        setPlanoProdutoSugeridoVO(new PlanoProdutoSugeridoVO());
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("produto");
        Iterator i = planoVO.getPlanoModalidadeVOs().iterator();
        int passo = 0;
        while (i.hasNext()) {
            PlanoModalidadeVO planoModalidade = (PlanoModalidadeVO) i.next();
            Iterator j = planoModalidade.getModalidade().getProdutoSugeridoVOs().iterator();
            while (j.hasNext()) {
                ProdutoSugeridoVO produtoSugerido = (ProdutoSugeridoVO) j.next();
                if (produtoSugerido.getProduto().getCodigo().equals(obj.getCodigo().intValue())) {
                    setMensagemDetalhada("O PRODUTO escolhido ( " + obj.getDescricao() + " ) já está cadastrado para Modalidade( " + planoModalidade.getModalidade().getNome() + " ). ");
                    obj = null;
                    listaConsultaPlanoProduto.clear();
                    campoConsultaPlanoProduto = null;
                    valorConsultaPlanoProduto = null;
                    setSucesso(false);
                    setAtencao(false);
                    setErro(true);
                    passo = 1;
                    return;
                }
            }
        }
        if (passo == 0) {
            getPlanoProdutoSugeridoVO().setProduto(obj);
            getPlanoProdutoSugeridoVO().setValorProduto(obj.getValorFinal());
            if (TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo().equals(obj.getTipoProduto()) && getPlanoVO().getRegimeRecorrencia()) {
                getPlanoProdutoSugeridoVO().setObrigatorio(true);
            }
            obj = null;
            listaConsultaPlanoProduto.clear();
            campoConsultaPlanoProduto = null;
            valorConsultaPlanoProduto = null;
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        }
    }

    public void consultarDescontoAntecipado() {
        try {
            setListaConsultaDescontoAntecipado(getFacade().getDesconto().consultarPorTipoProduto("DR", true, Uteis.NIVELMONTARDADOS_TODOS));
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setListaConsultaDescontoAntecipado(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarDescontoAntecipado() throws Exception {
        DescontoVO obj = (DescontoVO) context().getExternalContext().getRequestMap().get("desconto");
        if (obj == null) {
            throw new Exception("Erro ao Posicionar o Desconto Selecionado.");
        }
        getPlanoVO().setDescontoAntecipado(obj);
        listaConsultaDescontoAntecipado.clear();
        campoConsultaDescontoAntecipado = null;
        valorConsultaDescontoAntecipado = null;
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }

    public void removerDescontoAntecipado() {
        getPlanoVO().setDescontoAntecipado(new DescontoVO());
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>PlanoVO</code> Após a exclusão ela automaticamente aciona a rotina
     * para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getPlano().excluir(planoVO);
            incluirLogExclusao();
            novo();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"plano\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"plano\" violates foreign key")) {
                setMensagemDetalhada("Este Plano não pode ser excluído, pois está sendo utilizado!");
            } else if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"planoprodutosugerido\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"planoprodutosugerido\" violates foreign key")) {
                setMensagemDetalhada("Este Plano não pode ser excluído, pois está sendo utilizado!");
            } else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            return "editar";
        }
    }

    /* Método responsável por alterar o Valor de Referência Mensal pelo Valor
     * da Composição selecionada na tela de Durações. Se nenhuma Composição
     * estiver selecionada o valor não é alterado.
     */
    public void alterarValorReferenciaPelaComposicaoVO() {
        ComposicaoVO obj = getComposicaoVOValorReferencia();
        if (obj != null && obj.getCodigo() > 0) {
            setValorMensal(obj.getPrecoComposicao());
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>ProdutoSugerido</code>
     * para o objeto <code>modalidadeVO</code> da classe <code>Modalidade</code>
     */
    public void adicionarPlanoProdutoSugerido() throws Exception {
        try {
            if (!getPlanoVO().getCodigo().equals(new Integer(0))) {
                planoProdutoSugeridoVO.setPlano(getPlanoVO().getCodigo());
            }

            if (getPlanoProdutoSugeridoVO().getProduto().getCodigo() != 0) {
                Integer campoProduto = getPlanoProdutoSugeridoVO().getProduto().getCodigo();
                ProdutoVO produto = getFacade().getProduto().consultarPorChavePrimaria(campoProduto, Uteis.NIVELMONTARDADOS_TODOS);
                getPlanoProdutoSugeridoVO().setProduto(produto);
            }
            getPlanoVO().adicionarObjPlanoProdutoSugeridoVOs(getPlanoProdutoSugeridoVO());
            this.setPlanoProdutoSugeridoVO(new PlanoProdutoSugeridoVO());
            setMensagemID("msg_dados_adicionados");
            setAtencao(true);
            setSucesso(false);
            setErro(false);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>ProdutoSugerido</code>
     * para edição pelo usuário.
     */
    public void editarPlanoProdutoSugerido() throws Exception {
        PlanoProdutoSugeridoVO obj = (PlanoProdutoSugeridoVO) context().getExternalContext().getRequestMap().get("planoProdutoSugerido");
        if (obj.getProduto().getTipoProduto().equals(TipoProduto.MATRICULA.getCodigo())
                || obj.getProduto().getTipoProduto().equals(TipoProduto.REMATRICULA.getCodigo())
                || obj.getProduto().getTipoProduto().equals(TipoProduto.RENOVACAO.getCodigo())
                || obj.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())) {
            obj.setObrigatorio(true);
        }
        setPlanoProdutoSugeridoVO(obj);
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }

    /* Método responsável por remover um novo objeto da classe <code>ProdutoSugerido</code>
     * do objeto <code>modalidadeVO</code> da classe <code>Modalidade</code>
     */
    public void removerPlanoProdutoSugerido() throws Exception {
        setErro(false);
        PlanoProdutoSugeridoVO obj = (PlanoProdutoSugeridoVO) context().getExternalContext().getRequestMap().get("planoProdutoSugerido");
        String key = "msg_dados_excluidosPlano";
        setAtencao(true);
        if (!getFacade().getPlanoProdutoSugerido().vinculadoAoContrato(obj)) {
            key = "msg_dados_excluidos";
            getPlanoVO().excluirObjPlanoProdutoSugeridoVOs(obj.getProduto().getCodigo());
            setSucesso(true);
            setAtencao(false);
        }
        setMensagemID(key);
    }

    public void editarDiaVencimento() throws Exception {
        String obj = (String) context().getExternalContext().getRequestMap().get("dias");
        setDiaVencimento(obj);
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }

    public void removerDiaVencimento() throws Exception {
        String obj = (String) context().getExternalContext().getRequestMap().get("dias");
        getPlanoVO().getListaDiasVencimento().remove(obj);
        String diasVenc = "";
        diasVenc = planoVO.getListaDiasVencimento().toString().replace("[", "");
        diasVenc = diasVenc.replace("]", "");
        diasVenc = diasVenc.replace(" ", "");
        getPlanoVO().setDiasVencimentoProrata(diasVenc);
        setMensagemID("msg_dados_excluidos");
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }

    /* Método responsável por adicionar um novo objeto da classe <code>PlanoHorario</code>
     * para o objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void adicionarPlanoHorario() throws Exception {
        try {
            if (!getPlanoVO().getCodigo().equals(new Integer(0))) {
                planoHorarioVO.setPlano(getPlanoVO().getCodigo());
            }
            if (getPlanoHorarioVO().getHorario().getCodigo() != 0) {
                Integer campoConsulta = getPlanoHorarioVO().getHorario().getCodigo();
                HorarioVO horario = getFacade().getHorario().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
                getPlanoHorarioVO().setHorario(horario);
            }
            getPlanoVO().adicionarObjPlanoHorarioVOs(getPlanoHorarioVO());

            this.setPlanoHorarioVO(new PlanoHorarioVO());
            habilitarCampoHorario = true;
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);

        } catch (Exception e) {
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>PlanoHorario</code>
     * para edição pelo usuário.
     */
    public void editarPlanoHorario() throws Exception {
        PlanoHorarioVO obj = (PlanoHorarioVO) context().getExternalContext().getRequestMap().get("planoHorario");
        setPlanoHorarioVO((PlanoHorarioVO) obj.getClone(true));
        getPlanoHorarioVO().desenhaTipoValor();
        habilitarCampoHorario = false;
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setAtencao(false);
        setErro(false);

    }

    /* Método responsável por remover um novo objeto da classe <code>PlanoHorario</code>
     * do objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void removerPlanoHorario() throws Exception {
        try {
            setMensagemDetalhada("");
            PlanoHorarioVO obj = (PlanoHorarioVO) context().getExternalContext().getRequestMap().get("planoHorario");
            if (!UteisValidacao.emptyNumber(planoVO.getCodigo()) && getFacade().getPlano().existeContratoRenovavelAutomaticamenteComEssaConfiguracao(planoVO.getCodigo(), 0, 0, obj.getHorario().getCodigo(), 0, 0, 0)) {
                throw new ConsistirException("Horário não pode ser excluído, pois está vinculado a contratos passíveis de renovação automática, podendo acarretar em problemas na renovação desses contratos");
            }
            getPlanoVO().excluirObjPlanoHorarioVOs(obj.getHorario().getCodigo());
            this.setPlanoHorarioVO(new PlanoHorarioVO());
            habilitarCampoHorario = true;
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>PlanoModalidade</code>
     * para o objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void adicionarPlanoModalidade() throws Exception {
        try {
            if (!getPlanoVO().getCodigo().equals(0)) {
                planoModalidadeVO.setPlano(getPlanoVO().getCodigo());
            }
            if (getPlanoModalidadeVO().getModalidade().getCodigo() != 0) {
                Integer campoConsulta = getPlanoModalidadeVO().getModalidade().getCodigo();
                ModalidadeVO modalidade = getFacade().getModalidade().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
                planoModalidadeVO.setModalidade(modalidade);
            }

            if (getPlanoVO().getSite() && planoModalidadeVO.getModalidade().isUtilizarTurma()) {
                throw new ConsistirException("Um plano SITE pode ter apenas modalidades sem turmas.");
            }
            getPlanoVO().adicionarObjPlanoModalidadeVOs(planoModalidadeVO);
            adicionarPlanoModalidadeVezesSemanaDefault();
            adicionarPlanoModalidade(getPlanoModalidadeVO());
            this.setPlanoModalidadeVO(new PlanoModalidadeVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);

        } catch (Exception e) {
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    public void adicionarPlanoExcecao() throws Exception {
        try {
            if (!getPlanoVO().getCodigo().equals(0)) {
                planoExcecaoVOSelecionado.setPlano(getPlanoVO());
                if ((planoExcecaoVOSelecionado.getModalidade().getCodigo() != 0
                        || planoExcecaoVOSelecionado.getPacote().getCodigo() != 0)
                        && planoExcecaoVOSelecionado.getDuracao() > 0
                        && planoExcecaoVOSelecionado.getValor() > 0.0) {
                    if (!planoExcecaoVOSelecionado.getModalidade().getCodigo().equals(0)) {
                        ModalidadeVO modalidade = getFacade().getModalidade().consultarPorChavePrimaria(
                                planoExcecaoVOSelecionado.getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        planoExcecaoVOSelecionado.setModalidade(modalidade);
                    } else {
                        planoExcecaoVOSelecionado.setModalidade(new ModalidadeVO());
                    }
                    if (!planoExcecaoVOSelecionado.getPacote().getCodigo().equals(0)) {
                        ComposicaoVO composicao = getFacade().getComposicao().consultarPorChavePrimaria(
                                planoExcecaoVOSelecionado.getPacote().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        planoExcecaoVOSelecionado.setPacote(composicao);
                    } else {
                        planoExcecaoVOSelecionado.setPacote(new ComposicaoVO());
                    }
                    if (!planoExcecaoVOSelecionado.getHorario().getCodigo().equals(0)) {
                        HorarioVO horarioVO = getFacade().getHorario().consultarPorChavePrimaria(
                                planoExcecaoVOSelecionado.getHorario().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        planoExcecaoVOSelecionado.setHorario(horarioVO);
                    } else {
                        planoExcecaoVOSelecionado.setHorario(new HorarioVO());
                    }
                    getPlanoVO().adicionarPlanoExcecaoVOs(planoExcecaoVOSelecionado);
                    planoExcecaoVOSelecionado = new PlanoExcecaoVO();
                    setMensagemID("msg_dados_adicionados");
                    setSucesso(false);
                    setAtencao(true);
                    setErro(false);
                }
            }
        } catch (Exception e) {
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void iniciarPlanoExcecao() {
        planoExcecaoVOSelecionado = new PlanoExcecaoVO();
    }

    public void adicionarModalidade(ModalidadeVO obj) throws Exception {
        try {
            if (!getPlanoVO().getCodigo().equals(new Integer(0))) {
                planoModalidadeVO.setPlano(getPlanoVO().getCodigo());
            }
            getPlanoModalidadeVO().setModalidade(obj);
            getPlanoVO().adicionarObjPlanoModalidadeVOs(getPlanoModalidadeVO());
            this.setPlanoModalidadeVO(new PlanoModalidadeVO());
        } catch (Exception ignored) {
        }
    }
    /* Método responsável por adicionar um novo objeto da classe <code>PlanoModalidade</code>
     * para o objeto <code>planoVO</code> da classe <code>Plano</code>
     */


    public void montarListaVezesSemana(PlanoVO planoVO) {
        for (PlanoModalidadeVO obj : planoVO.getPlanoModalidadeVOs()) {
            obj.montarNrVezesSemana();
        }
    }

    public void adicionarPlanoModalidade(PlanoModalidadeVO obj) throws Exception {
        obj.setListaVezesSemana("");
        if (!obj.getPlanoModalidadeVezesSemanaVOs().isEmpty()) {
            Ordenacao.ordenarLista(obj.getPlanoModalidadeVezesSemanaVOs(), "nrVezes");
            for (Iterator it = obj.getPlanoModalidadeVezesSemanaVOs().iterator(); it.hasNext(); ) {
                String descricao = "";
                PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemana = (PlanoModalidadeVezesSemanaVO) it.next();
                Double valor;
                if (planoModalidadeVezesSemana.getTipoOperacao() == null) {
                    planoModalidadeVezesSemana.setTipoOperacao("");
                }
                if (planoModalidadeVezesSemana.getTipoOperacao().equals("RE")) {
                    descricao = " Redução ";
                    if (planoModalidadeVezesSemana.getTipoValor().equals("PD")) {
                        valor = Uteis.arredondarForcando2CasasDecimais(planoModalidadeVezesSemana.getPercentualDesconto());
                        descricao += Formatador.formatarValorMonetarioSemMoeda(valor) + "%";
                    } else {
                        valor = Uteis.arredondarForcando2CasasDecimais(planoModalidadeVezesSemana.getValorEspecifico());
                        descricao += Formatador.formatarValorMonetario(valor);
                    }
                } else if (planoModalidadeVezesSemana.getTipoOperacao().equals("AC")) {
                    descricao = " Acréscimo ";
                    if (planoModalidadeVezesSemana.getTipoValor().equals("PD")) {
                        valor = Uteis.arredondarForcando2CasasDecimais(planoModalidadeVezesSemana.getPercentualDesconto());
                        descricao += Formatador.formatarValorMonetarioSemMoeda(valor) + "%";
                    } else {
                        valor = Uteis.arredondarForcando2CasasDecimais(planoModalidadeVezesSemana.getValorEspecifico());
                        descricao += Formatador.formatarValorMonetario(valor);
                    }
                } else if (planoModalidadeVezesSemana.getTipoOperacao().equals("EX")) {
                    descricao = " Exatamente " + Formatador.formatarValorMonetario(planoModalidadeVezesSemana.getValorEspecifico());
                }
                obj.setListaVezesSemana(String.format("%s %s Vezes%s,",
                        obj.getListaVezesSemana(),
                        planoModalidadeVezesSemana.getNrVezes(),
                        descricao.isEmpty() ? "" : " - " + descricao));
            }
            if (obj.getListaVezesSemana().lastIndexOf(",") == (obj.getListaVezesSemana().length() - 1)) {
                obj.setListaVezesSemana(obj.getListaVezesSemana().substring(0, obj.getListaVezesSemana().length() - 1));
            }
        }
        getPlanoVO().adicionarObjPlanoModalidadeVOs(obj);
        setMensagemID("msg_dados_adicionados");
        setAtencao(true);
        setSucesso(false);
        setErro(false);

    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>PlanoModalidade</code>
     * para edição pelo usuário.
     */
    public void editarPlanoModalidade() throws Exception {
        PlanoModalidadeVO obj = (PlanoModalidadeVO) context().getExternalContext().getRequestMap().get("planoModalidade");
        setPlanoModalidadeVO(obj);
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setAtencao(false);
        setErro(false);

    }
    /* Método responsável por disponibilizar dados de um objeto da classe <code>PlanoModalidade</code>
     * para edição pelo usuário.
     */

    public void removerPlanoModalidade() throws Exception {
        try {
            setMensagemDetalhada("");
            PlanoModalidadeVO obj = (PlanoModalidadeVO) context().getExternalContext().getRequestMap().get("planoModalidade");
            if (!UteisValidacao.emptyNumber(planoVO.getCodigo()) && getFacade().getPlano().existeContratoRenovavelAutomaticamenteComEssaConfiguracao(planoVO.getCodigo(), obj.getModalidade().getCodigo(), 0, 0, 0, 0, 0)) {
                throw new ConsistirException("Modalidade não pode ser excluída, pois está vinculada a contratos passíveis de renovação automática, podendo acarretar em problemas na renovação desses contratos");
            }
            if (!getPlanoVO().getRegimeRecorrencia()) {
                processarPlanoModalidadeEmPlanoComposicao(obj);
            }
            getPlanoVO().excluirObjPlanoModalidadeVOs(obj.getModalidade().getCodigo());
            Iterator i = getPlanoVO().getPlanoModalidadeVOs().iterator();
            setValorMensal(0.0);
            while (i.hasNext()) {
                PlanoModalidadeVO planoModalidade = (PlanoModalidadeVO) i.next();
                setValorMensal(planoModalidade.getModalidade().getValorMensal() + getValorMensal());
            }
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    /**
     * metodo que pecorre a lista de compiscao verificando se a modalidade que
     * sera retirada da lista PlanoModalidade não faz parte da lista de
     * compisição tambem.
     *
     * @throws Exception
     */
    public void processarPlanoModalidadeEmPlanoComposicao(PlanoModalidadeVO obj) throws Exception {
        Iterator i = getPlanoVO().getPlanoComposicaoVOs().iterator();
        while (i.hasNext()) {
            PlanoComposicaoVO planoComp = (PlanoComposicaoVO) i.next();
            Iterator j = planoComp.getComposicao().getComposicaoModalidadeVOs().iterator();
            while (j.hasNext()) {
                ComposicaoModalidadeVO composicaoModa = (ComposicaoModalidadeVO) j.next();
                if (composicaoModa.getModalidade().getCodigo().equals(obj.getModalidade().getCodigo().intValue())) {
                    throw new Exception("Essa Modalidade não pode ser removida, pois há um Pacote que utiliza a mesma!");
                }
            }
        }
    }

    /* Método responsável por remover um novo objeto da classe <code>PlanoModalidade</code>
     * do objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void selecionarPlanoModalidade() throws Exception {
        PlanoModalidadeVO obj = (PlanoModalidadeVO) context().getExternalContext().getRequestMap().get("planoModalidade");
        PlanoModalidadeVezesSemanaVO referencia = (PlanoModalidadeVezesSemanaVO) ColecaoUtils.find(obj.getPlanoModalidadeVezesSemanaVOs(), new Predicate() {
            @Override
            public boolean evaluate(Object o) {
                PlanoModalidadeVezesSemanaVO pmvs = (PlanoModalidadeVezesSemanaVO) o;
                return pmvs.isReferencia() && !UteisValidacao.emptyNumber(pmvs.getValorEspecifico());
            }
        });
        if (referencia != null) {
            obj.getModalidade().setValorOriginal(obj.getModalidade().getValorMensal());
            obj.getModalidade().setValorMensal(referencia.getValorEspecifico());
        }
        setPlanoModalidadeVOSelecionado(obj);
//        calcularValorAjustado();
        setApresentarPlamoModalidadeVesezSemana(true);
        setMensagemID("msg_dados_selecionados");
        setSucesso(true);
        setAtencao(false);
        setErro(false);

    }
    /* Método responsável por adicionar um novo objeto da classe <code>PlanoModalidade</code>
     * para o objeto <code>planoVO</code> da classe <code>Plano</code>
     */

    public void adicionarPlanoModalidadeVezesSemana() throws Exception {
        try {
            setMensagemDetalhada("");
            final int nrVezes = planoModalidadeVezesSemanaVO.getNrVezes();
            if (!planoModalidadeVezesSemanaVO.getTipoOperacao().equals("EX")) {
                planoModalidadeVezesSemanaVO.setReferencia(false);
            }
            boolean existe = ColecaoUtils.exists(getPlanoModalidadeVOSelecionado().getPlanoModalidadeVezesSemanaVOs(), new Predicate() {
                @Override
                public boolean evaluate(Object o) {
                    PlanoModalidadeVezesSemanaVO vezesSemana = (PlanoModalidadeVezesSemanaVO) o;
                    return vezesSemana.isReferencia() && !vezesSemana.getNrVezes().equals(nrVezes);
                }
            });
            if (existe && planoModalidadeVezesSemanaVO.isReferencia()) {
                throw new ConsistirException("Já existe um Registro PlanoModalidadeVezesSemana como referência de Valor Mensal, no máximo um pode ser definido");
            }
            if (!getPlanoModalidadeVOSelecionado().getCodigo().equals(0)) {
                planoModalidadeVezesSemanaVO.setPlanoModalidade(getPlanoModalidadeVOSelecionado().getCodigo());
            }
            getPlanoModalidadeVOSelecionado().adicionarObjPlanoModalidadeVezesSemanaVOs(getPlanoModalidadeVezesSemanaVO());
            adicionarPlanoModalidade(getPlanoModalidadeVOSelecionado());
            this.setPlanoModalidadeVezesSemanaVO(new PlanoModalidadeVezesSemanaVO());
            habilitarCampoVezesPorSemana = true;
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>PlanoModalidade</code>
     * para edição pelo usuário.
     */
    public void editarPlanoModalidadeVezesSemana() throws Exception {
        PlanoModalidadeVezesSemanaVO obj = (PlanoModalidadeVezesSemanaVO) context().getExternalContext().getRequestMap().get("planoModalidadeVezesSemana");
        setPlanoModalidadeVezesSemanaVO((PlanoModalidadeVezesSemanaVO) obj.getClone(true));
        habilitarCampoVezesPorSemana = false;
//        calcularValorAjustado();
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }

    public void editarPlanoExcecao() throws Exception {
        planoExcecaoVOSelecionado = (PlanoExcecaoVO) context().getExternalContext().getRequestMap().get("planoExcecao");
    }

    public void removerPlanoExcecao() throws Exception {
        PlanoExcecaoVO planoExcecaoVO = (PlanoExcecaoVO) context().getExternalContext().getRequestMap().get("planoExcecao");
        planoVO.getPlanoExcecaoVOs().remove(planoExcecaoVO);
    }

    /* Método responsável por remover um novo objeto da cpublic void editarPlanoPlanoExcecao() throws Exception {
     planoExcecaoVOSelecionado = (PlanoExcecaoVO) context().getExternalContext().getRequestMap().get("planoExcecao");
     }lasse <code>PlanoModalidade</code>
     * do objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void removerPlanoModalidadeVezesSemana() throws Exception {
        try {
            setMensagemDetalhada("");
            PlanoModalidadeVezesSemanaVO obj = (PlanoModalidadeVezesSemanaVO) context().getExternalContext().getRequestMap().get("planoModalidadeVezesSemana");
            if (!UteisValidacao.emptyNumber(planoVO.getCodigo()) && getFacade().getPlano().existeContratoRenovavelAutomaticamenteComEssaConfiguracao(planoVO.getCodigo(), planoModalidadeVOSelecionado.getModalidade().getCodigo(), obj.getNrVezes(), 0, 0, 0, 0)) {
                throw new ConsistirException("Vezes por semana não pode ser excluída, pois está vinculada a contratos passíveis de renovação automática, podendo acarretar em problemas na renovação desses contratos");
            }
            PlanoModalidadeVezesSemanaVO referencia = (PlanoModalidadeVezesSemanaVO) ColecaoUtils.find(getPlanoModalidadeVOSelecionado().getPlanoModalidadeVezesSemanaVOs(), new Predicate() {
                @Override
                public boolean evaluate(Object o) {
                    PlanoModalidadeVezesSemanaVO pmvs = (PlanoModalidadeVezesSemanaVO) o;
                    return pmvs.isReferencia() && !UteisValidacao.emptyNumber(pmvs.getValorEspecifico());
                }
            });
            if (referencia != null) {
                getPlanoModalidadeVOSelecionado().getModalidade().setValorMensal(getPlanoModalidadeVOSelecionado().getModalidade().getValorOriginal());
            }
            getPlanoModalidadeVOSelecionado().getPlanoModalidadeVezesSemanaVOs().remove(obj);
            adicionarPlanoModalidade(getPlanoModalidadeVOSelecionado());
            this.setPlanoModalidadeVezesSemanaVO(new PlanoModalidadeVezesSemanaVO());
            habilitarCampoVezesPorSemana = true;
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void fechaJanelaPlanoModalidadeVezesSemana() {
        setPlanoModalidadeVOSelecionado(new PlanoModalidadeVO());
        setApresentarPlamoModalidadeVesezSemana(false);
        setMensagemID("msg_dados_adicionados");
        setSucesso(false);
        setAtencao(true);
        setErro(false);
    }

    /* Método responsável por adicionar um novo objeto da classe <code>PlanoComposicao</code>
     * para o objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void adicionarPlanoComposicao() throws Exception {
        try {
            if (!getPlanoVO().getCodigo().equals(new Integer(0))) {
                planoComposicaoVO.setPlano(getPlanoVO().getCodigo());
            }
            if (getPlanoComposicaoVO().getComposicao().getCodigo() != 0) {
                Integer campoConsulta = getPlanoComposicaoVO().getComposicao().getCodigo();
                ComposicaoVO composicao = getFacade().getComposicao().consultarPorCodigo(campoConsulta, planoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                getPlanoComposicaoVO().setComposicao(composicao);
            }
            getPlanoVO().adicionarObjPlanoComposicaoVOs(getPlanoComposicaoVO());

            Iterator j = getPlanoComposicaoVO().getComposicao().getComposicaoModalidadeVOs().iterator();
            while (j.hasNext()) {
                ComposicaoModalidadeVO obj = (ComposicaoModalidadeVO) j.next();
                setPlanoModalidadeVO(new PlanoModalidadeVO());
                getPlanoModalidadeVO().setModalidade(obj.getModalidade());
                getPlanoVO().adicionarObjPlanoModalidadeVOs(getPlanoModalidadeVO());
            }
            adicionarPlanoModalidadeVezesSemanaDefault();
            this.setPlanoModalidadeVO(new PlanoModalidadeVO());
            this.setPlanoComposicaoVO(new PlanoComposicaoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
            setCodigoComposicaoVOValorReferencia(0);
        } catch (Exception e) {
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarPlanoCategoria() throws Exception{
        try {
            if (!getPlanoVO().getCodigo().equals(0)) {
                planoCategoriaVO.setPlano(getPlanoVO().getCodigo());
            }
            if (getPlanoCategoriaVO().getCategoria().getCodigo() != 0) {
                Integer campoConsulta = getPlanoCategoriaVO().getCategoria().getCodigo();
                CategoriaVO categoriaVO = getFacade().getCategoria().consultarPorCodigo(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
                getPlanoCategoriaVO().setCategoria(categoriaVO);
            }
            getPlanoVO().adicionarObjPlanoCategoriaVOs(getPlanoCategoriaVO());

            this.setPlanoCategoriaVO(new PlanoCategoriaVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>PlanoComposicao</code>
     * para edição pelo usuário.
     */
    public void editarPlanoComposicao() throws Exception {
        PlanoComposicaoVO obj = (PlanoComposicaoVO) context().getExternalContext().getRequestMap().get("planoComposicao");
        setPlanoComposicaoVO(obj);
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setAtencao(false);
        setErro(false);

    }

    /* Método responsável por remover um novo objeto da classe <code>PlanoComposicao</code>
     * do objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void removerPlanoComposicao() throws Exception {
        try {
            setMensagemDetalhada("");
            PlanoComposicaoVO obj = (PlanoComposicaoVO) context().getExternalContext().getRequestMap().get("planoComposicao");
            if (!UteisValidacao.emptyNumber(planoVO.getCodigo()) && getFacade().getPlano().existeContratoRenovavelAutomaticamenteComEssaConfiguracao(planoVO.getCodigo(), 0, 0, 0, 0, 0, obj.getComposicao().getCodigo())) {
                throw new ConsistirException("Pacote não pode ser excluído, pois está vinculado a contratos passíveis de renovação automática, podendo acarretar em problemas na renovação desses contratos");
            }
            getPlanoVO().excluirObjPlanoComposicaoVOs(obj.getComposicao().getCodigo());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }

    }

    public void removerPlanoCategoria() throws Exception {
        try {
            setMensagemDetalhada("");
            PlanoCategoriaVO obj = (PlanoCategoriaVO) context().getExternalContext().getRequestMap().get("planoCategoria");
            getPlanoVO().excluirObjPlanoCategoriaVOs(obj.getCategoria().getCodigo());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }

    }

    /* Método responsável por remover um novo objeto da classe <code>PlanoModalidade</code>
     * do objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void selecionarPlanoDuracao() throws Exception {
        //PlanoDuracaoVO obj = (PlanoDuracaoVO) context().getExternalContext().getRequestMap().get("planoDuracao");
        //setPlanoDuracaoVOSelecionado(obj);
        setApresentarPlamoCondicaoPagamento(true);
        setMensagemID("msg_dados_selecionados");
        setSucesso(true);
        setAtencao(false);
        setErro(false);

    }

    public void atualizaListaPlanosCategoria(){
        if(!planoVO.getRestringeVendaPorCategoria()){
            planoVO.setPlanoCategoriaVOs(new ArrayList<>());
        }
    }

    public void configurarDuracaoCreditoTreino() throws Exception {
        try {
            if (this.planoDuracaoVOSelecionado.getListaPlanoDuracaoCreditoTreino() == null) {
                this.planoDuracaoVOSelecionado.setListaPlanoDuracaoCreditoTreino(new ArrayList<PlanoDuracaoCreditoTreinoVO>());
            }
            this.planoDuracaoCreditoTreinoVO = new PlanoDuracaoCreditoTreinoVO();
            planoDuracaoCreditoTreinoVO.setPlanoDuracaoVO(this.planoDuracaoVOSelecionado);
            this.mostrarConfiguracaoDuracaoCreditoTreino = true;
            Collections.sort(this.planoDuracaoVOSelecionado.getListaPlanoDuracaoCreditoTreino(), PlanoDuracaoCreditoTreinoVO.COMPARATOR_QTDE_CREDITO);
            montarSucesso("msg_dados_selecionados");
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }


    public void selecionarCarenciaPlanoDuracao() throws Exception {
        PlanoDuracaoVO obj = (PlanoDuracaoVO) context().getExternalContext().getRequestMap().get("planoDuracao");
        setPlanoDuracaoVO(obj);
        setApresentarPanelCarencia(true);
        setMensagemID("msg_dados_selecionados");
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }

    /* Método responsável por adicionar um novo objeto da classe <code>PlanoCondicaoPagamento</code>
     * para o objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void adicionarPlanoCondicaoPagamento() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getPlanoDuracaoVOSelecionado().getCodigo())) {
                planoCondicaoPagamentoVO.setPlanoDuracao(getPlanoDuracaoVOSelecionado().getCodigo());
            }
            if (getPlanoCondicaoPagamentoVO().getCondicaoPagamento().getCodigo() != 0) {
                Integer campoCondicaoPagamento = getPlanoCondicaoPagamentoVO().getCondicaoPagamento().getCodigo();
                CondicaoPagamentoVO condicaoPagamento = getFacade().getCondicaoPagamento().consultarPorChavePrimaria(campoCondicaoPagamento, Uteis.NIVELMONTARDADOS_TODOS);
                getPlanoCondicaoPagamentoVO().setCondicaoPagamento(condicaoPagamento);
            }
            getPlanoDuracaoVOSelecionado().adicionarObjPlanoCondicaoPagamentoVOs(getPlanoCondicaoPagamentoVO());
            this.setPlanoCondicaoPagamentoVO(new PlanoCondicaoPagamentoVO());
            habilitarCampoCondicaoPagamento = true;
            montarListaParcelasMatricula();
            setAtencao(true);
            setSucesso(false);
            setErro(false);
            setMensagemID("msg_dados_adicionados");

        } catch (Exception e) {
            setAtencao(false);
            setSucesso(false);
            setErro(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>PlanoCondicaoPagamento</code>
     * para edição pelo usuário.
     */
    public void editarPlanoCondicaoPagamento() throws Exception {
        PlanoCondicaoPagamentoVO obj = (PlanoCondicaoPagamentoVO) context().getExternalContext().getRequestMap().get("planoCondicaoPagamento");
        setPlanoCondicaoPagamentoVO((PlanoCondicaoPagamentoVO) obj.getClone(true));
        habilitarCampoCondicaoPagamento = false;
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }

    public void editarDuracaoCreditoTreino() throws Exception {
        montarSucesso("msg_dados_editar");

    }

    public void removerDuracaoCreditoTreino() throws Exception {

        int index = 0;
        Iterator i = this.planoDuracaoVOSelecionado.getListaPlanoDuracaoCreditoTreino().iterator();
        while (i.hasNext()) {
            PlanoDuracaoCreditoTreinoVO objExistente = (PlanoDuracaoCreditoTreinoVO) i.next();
            if (objExistente.getCodigo().equals(this.planoDuracaoCreditoTreinoVO.getCodigo())) {
                this.planoDuracaoVOSelecionado.getListaPlanoDuracaoCreditoTreino().remove(index);
                montarSucesso("msg_dados_excluidos");
                return;
            }
            index++;
        }

    }


    /* Método responsável por remover um novo objeto da classe <code>PlanoCondicaoPagamento</code>
     * do objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void removerPlanoCondicaoPagamento() throws Exception {
        try {
            setMensagemDetalhada("");
            PlanoCondicaoPagamentoVO obj = (PlanoCondicaoPagamentoVO) context().getExternalContext().getRequestMap().get("planoCondicaoPagamento");
            if (!UteisValidacao.emptyNumber(planoVO.getCodigo()) && getFacade().getPlano().existeContratoRenovavelAutomaticamenteComEssaConfiguracao(planoVO.getCodigo(), 0, 0, 0, getPlanoDuracaoVOSelecionado().getNumeroMeses(), obj.getCondicaoPagamento().getCodigo(), 0)) {
                notificarRecursoEmpresa(RecursoSistema.PLANO_CONDICAO_PAGAMENTO);
                throw new ConsistirException("Condição de Pagamento não pode ser excluída, pois está vinculada a contratos passíveis de renovação automática, podendo acarretar em problemas na renovação desses contratos");
            }
            validarExisteContratoDuracaoCondicao(planoVO.getCodigo(), obj.getCondicaoPagamento().getCodigo(), true);

            getPlanoDuracaoVOSelecionado().excluirObjPlanoCondicaoPagamentoVOs(obj);
            this.setPlanoCondicaoPagamentoVO(new PlanoCondicaoPagamentoVO());
            habilitarCampoCondicaoPagamento = true;
            montarListaParcelasMatricula();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }

    }

    public void fechaJanelaPlanoDuracao() {
        setPlanoDuracaoVOSelecionado(new PlanoDuracaoVO());
        setApresentarPlamoCondicaoPagamento(false);
        setMensagemID("msg_dados_adicionados");
        setSucesso(false);
        setAtencao(true);
        setErro(false);
    }

    public void fechaJanelaDuracaoCreditoTreino() {
        this.planoDuracaoCreditoTreinoVO = new PlanoDuracaoCreditoTreinoVO();
        this.mostrarConfiguracaoDuracaoCreditoTreino = false;
        montarSucesso("msg_dados_adicionados");
    }


    public void calcularDuracaoMensal() {
        try {
            setPlanoDuracaoVO(new Plano().calculoDuracaoMensal(getPlanoDuracaoVO(), getValorMensal()));
            setSucesso(true);
            setAtencao(false);
            setErro(false);
            setMensagemDetalhada("", "");
            setMensagemID("msg_calculo");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void calcularDuracaoParcela() {
        try {
            setPlanoDuracaoVO(new Plano().calculoDuracaoParcela(getPlanoDuracaoVO(), getValorMensal()));
            setSucesso(true);
            setErro(false);
            setAtencao(false);
            setMensagemDetalhada("", "");
            setMensagemID("msg_calculo");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void calcularDuracao() {
        try {
            setPlanoDuracaoVO(getFacade().getPlano().calculoDuracao(getPlanoDuracaoVO(), getValorMensal()));
            setSucesso(true);
            setErro(false);
            setAtencao(false);
            setMensagemDetalhada("", "");
            setMensagemID("msg_calculo");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>PlanoDuracao</code>
     * para o objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void adicionarPlanoDuracao() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getPlanoVO().getCodigo())) {
                planoDuracaoVO.setPlano(getPlanoVO().getCodigo());
            }
            if (getPlanoDuracaoVO().getValorDesejado() != 0 && getPlanoDuracaoVO().getTipoValor() != null) {
                if (getPlanoDuracaoVO().getTipoValor().equals("VE")) {
                    getPlanoDuracaoVO().setValorEspecifico(getPlanoDuracaoVO().getValorDesejado());
                } else if (getPlanoDuracaoVO().getTipoValor().equals("PD")) {
                    getPlanoDuracaoVO().setPercentualDesconto(getPlanoDuracaoVO().getValorDesejado());
                }
            }
            if (this.planoVO.isVendaCreditoTreino()) {
                getPlanoDuracaoVO().setTipoValor("VE");
                getPlanoDuracaoVO().desenhaTipoValor();
            }
            if (planoVO.getSite() == true && planoVO.getPlanoDuracaoVOs().size() > 0 && UteisValidacao.emptyNumber(getPlanoDuracaoVO().getCodigo())) {
                throw new Exception("Só é permitido 1 (uma) duração para planos do tipo SITE");
            }


            //Retirado porque avaliado com o Humberto e não há necessidade desta validação já que o clube já grava isto quando utilizado pelo academia
//            if (!getEmpresaLogado().isTrabalharComPontuacao()) {
//                if (planoDuracaoVO.getPontos() > 0) {
//                    throw new Exception("Para cadastrar plano por pontos é necessário que a configuração desta função esteja ativada. Vá nas configurações da empresa, procure pela configuração 'Habilitar Pontuação por plano' e habilite esta função.");
//                }
//            }
            if (UteisValidacao.emptyNumber(getPlanoDuracaoVO().getCodigo())) {
                adicionarPlanoCondicoesPagamentoPadroes();
            }
            getPlanoVO().adicionarObjPlanoDuracaoVOs(getPlanoDuracaoVO());
            this.setPlanoDuracaoVO(new PlanoDuracaoVO());
            habilitarCampoNrMeses = true;
            getPlanoVO().setPlanoDuracaoVOs(Ordenacao.ordenarLista(getPlanoVO().getPlanoDuracaoVOs(), "numeroMeses"));
            this.planoDuracaoCreditoTreinoVO = new PlanoDuracaoCreditoTreinoVO();
            montarListaParcelasMatricula();
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);

        } catch (Exception e) {
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método que adiciona ao plano as condições de pagamento automaticamente
     * de acordo com o número máximo de parcelas informado.
     *
     * @throws Exception
     */
    private void adicionarPlanoCondicoesPagamentoPadroes() throws Exception {
        Integer maxNrParcelas = getPlanoDuracaoVO().getNrMaximoParcelasCondPagamento();
        List<CondicaoPagamentoVO> listaCondicaoPagamentos =
                getFacade().getCondicaoPagamento().consultarPorNrParcelasMenorOuIgual(maxNrParcelas, true, Uteis.NIVELMONTARDADOS_TODOS);
        List<PlanoCondicaoPagamentoVO> listaPlanoCondicaoPagamentos = new ArrayList<PlanoCondicaoPagamentoVO>();

        for (CondicaoPagamentoVO condPagamento : listaCondicaoPagamentos) {
            boolean existeCondicaoPagamento = false;
            for (PlanoCondicaoPagamentoVO planoCondPagamento : getPlanoDuracaoVO().getPlanoCondicaoPagamentoVOs()) {
                if (planoCondPagamento.getCondicaoPagamento().getCodigo().equals(condPagamento.getCodigo())) {
                    existeCondicaoPagamento = true;
                    listaPlanoCondicaoPagamentos.add(planoCondPagamento);
                }
            }
            if (!existeCondicaoPagamento) {
                PlanoCondicaoPagamentoVO planoCondPagamento = new PlanoCondicaoPagamentoVO();
                planoCondPagamento.setCondicaoPagamento(condPagamento);

                listaPlanoCondicaoPagamentos.add(planoCondPagamento);
            }
        }

        getPlanoDuracaoVO().setPlanoCondicaoPagamentoVOs(listaPlanoCondicaoPagamentos);
    }

    public void verificarHorarioCreditoTreinoSelecionado() {
        if (planoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum() != TipoHorarioCreditoTreinoEnum.HORARIO_TURMA) {
            planoDuracaoCreditoTreinoVO.setNumeroVezesSemana(7);
        } else {
            planoDuracaoCreditoTreinoVO.setNumeroVezesSemana(0);
        }
    }

    public void adicionarPlanoDuracaoCreditoTreino() throws Exception {
        try {
            if (getPlanoVO().isCreditoTreinoNaoCumulativo()) {
                this.planoDuracaoCreditoTreinoVO.validarDados(true);
            } else this.planoDuracaoCreditoTreinoVO.validarDados();

            if (getPlanoVO().getSite() && getPlanoVO().isVendaCreditoTreino() && this.getPlanoDuracaoVOSelecionado().getListaPlanoDuracaoCreditoTreino().size() >= 1) {
                throw new Exception("Só é permitido uma configuração de crédito em um plano site");
            }

            if (getPlanoVO().isCreditoTreinoNaoCumulativo()) {
                if (UteisValidacao.emptyNumber(this.planoDuracaoCreditoTreinoVO.getQuantidadeCreditoMensal())) {
                    throw new Exception("O campo Quantidade Crédito Mensal (PlanoDuracaoCreditoTreino) deve ser informado.");
                }
                Integer qtdComprada = this.planoDuracaoCreditoTreinoVO.getPlanoDuracaoVO().getNumeroMeses() * this.planoDuracaoCreditoTreinoVO.getQuantidadeCreditoMensal();
                this.planoDuracaoCreditoTreinoVO.setQuantidadeCreditoCompra(qtdComprada);
            }

            if (this.planoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum().equals(TipoHorarioCreditoTreinoEnum.LIVRE_OBRIGATORIO_MARCAR_AULA) &&
                    this.planoVO.getQuantidadeMaximaFrequencia() == 0) {
                this.planoDuracaoCreditoTreinoVO.setNumeroVezesSemana(7);
            } else if (this.planoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum().equals(TipoHorarioCreditoTreinoEnum.LIVRE_OBRIGATORIO_MARCAR_AULA)) {
                this.planoDuracaoCreditoTreinoVO.setNumeroVezesSemana(this.planoVO.getQuantidadeMaximaFrequencia());
            }

            this.planoDuracaoVOSelecionado.adicionarDuracaoCreditoTreino(this.planoDuracaoCreditoTreinoVO);
            this.planoDuracaoCreditoTreinoVO = new PlanoDuracaoCreditoTreinoVO();
            this.planoDuracaoCreditoTreinoVO.setPlanoDuracaoVO(this.planoDuracaoVOSelecionado);
            Collections.sort(this.planoDuracaoVOSelecionado.getListaPlanoDuracaoCreditoTreino(), PlanoDuracaoCreditoTreinoVO.COMPARATOR_QTDE_CREDITO);
            montarSucesso("msg_dados_adicionados");

        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void adicionarCarencia() throws Exception {
        try {
            getPlanoDuracaoVO().getCarencia();
            if (!getPlanoVO().getCodigo().equals(new Integer(0))) {
                planoDuracaoVO.setPlano(getPlanoVO().getCodigo());
            }
            getPlanoVO().adicionarObjPlanoDuracaoVOs(getPlanoDuracaoVO());
            setPlanoDuracaoVO(new PlanoDuracaoVO());
            setApresentarPanelCarencia(false);
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarVecimento() {
        try {
            if (diaVencimento.isEmpty()) {
                throw new Exception("Informe um Dia para Vencimento.");
            }
            int numero = Integer.valueOf(diaVencimento);
            if (numero < 1 || numero > 31) {
                throw new Exception("O Dia de Vencimento Informado Inválido.");
            }
            diaVencimento = String.valueOf(numero);
            for (String dia : getPlanoVO().getListaDiasVencimento()) {
                if (dia.equals(diaVencimento)) {
                    throw new Exception("O Dia de Vencimento Informado Já Foi Incluído.");
                }
            }
            getPlanoVO().setDiaVencimento(diaVencimento);
            String diasVenc = "";
            diasVenc = planoVO.getListaDiasVencimento().toString().replace("[", "");
            diasVenc = diasVenc.replace("]", "");
            diasVenc = diasVenc.replace(" ", "");
            getPlanoVO().setDiasVencimentoProrata(diasVenc);
            setSucesso(false);
            setAtencao(true);
            setErro(false);
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarTodosOsDiasVencimento() {
        try {
            if (diaVencimento.isEmpty()) {
                throw new Exception("Informe um Dia para Vencimento");
            }
            int numero = Integer.valueOf(diaVencimento);
            if (numero < 1 || numero > 31) {
                throw new Exception("O Dia de Vencimento Informado Inválido");
            }
            diaVencimento = String.valueOf(numero);
            for (String dia : getPlanoVO().getListaDiasVencimento()) {
                if (dia.equals(diaVencimento)) {
                    throw new Exception("O Dia de Vencimento Informado Já Foi Incluído");
                }
            }
            String diasVenc = "";
            for (Integer i = 1; i <= 31; i++) {
                getPlanoVO().setDiaVencimento(i.toString());
                if (i == 1) {
                    diasVenc = "1";
                } else {
                    diasVenc = diasVenc + "," + i;
                }
            }
            getPlanoVO().setDiasVencimentoProrata(diasVenc);

            setSucesso(false);
            setAtencao(true);
            setErro(false);
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void removerTodosOsDiasVencimentos() {
        for (Integer i = 1; i <= 31; i++) {
            getPlanoVO().getListaDiasVencimento().remove(i.toString());
        }
        planoVO.setDiasVencimentoProrata("");
        setMensagemID("msg_dados_excluidos");
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>PlanoDuracao</code>
     * para edição pelo usuário.
     */
    public void editarPlanoDuracao() throws Exception {
        PlanoDuracaoVO obj = (PlanoDuracaoVO) context().getExternalContext().getRequestMap().get("planoDuracao");
        setPlanoDuracaoVO((PlanoDuracaoVO) obj.getClone(true));
        habilitarCampoNrMeses = false;
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }

    /* Método responsável por remover um novo objeto da classe <code>PlanoDuracao</code>
     * do objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void removerPlanoDuracao() throws Exception {
        try {
            PlanoDuracaoVO obj = (PlanoDuracaoVO) context().getExternalContext().getRequestMap().get("planoDuracao");
            if (!UteisValidacao.emptyNumber(planoVO.getCodigo()) && getFacade().getPlano().existeContratoRenovavelAutomaticamenteComEssaConfiguracao(planoVO.getCodigo(), 0, 0, 0, obj.getNumeroMeses(), 0, 0)) {
                notificarRecursoEmpresa(RecursoSistema.PLANO_DURACAO_PAGAMENTO);
                throw new ConsistirException("Duração não pode ser excluída, pois está vinculada a contratos passíveis de renovação automática, podendo acarretar em problemas na renovação desses contratos");
            }
            validarExisteContratoDuracaoCondicao(planoVO.getCodigo(), obj.getNumeroMeses(), false);

            getPlanoVO().excluirObjPlanoDuracaoVOs(obj);
            this.setPlanoDuracaoVO(new PlanoDuracaoVO());
            habilitarCampoNrMeses = true;
            montarListaParcelasMatricula();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void adicionarObjDefault() throws Exception {
        if (planoVO.getEmpresa().getCodigo() != 0) {
            adicionarPlanoComposicaoDefault();
            adicionarPlanoModalidadeDefault();
            adicionarPlanoProdutoSugeridoDefault();
            adicionarPlanoProdutoPadraoDefault();
        }
    }

    public void adicionarObjDefaultEmpresa() throws Exception {
        planoVO.setPlanoComposicaoVOs(new ArrayList());
        planoVO.setPlanoDuracaoVOs(new ArrayList());
        planoVO.setPlanoHorarioVOs(new ArrayList());
        planoVO.setPlanoModalidadeVOs(new ArrayList());
        planoVO.setPlanoProdutoSugeridoVOs(new ArrayList());
        planoVO.setPlanoProdutoPadraoVOs(new ArrayList());
        adicionarObjDefault();
        montarListaSelectItemModalidade();
        montarListaSelectItemComposicao();
    }

    /* Método responsável por adicionar um novo objeto da classe <code>PlanoModalidade</code>
     * para o objeto <code>planoVO</code> da classe <code>Plano</code>
     */
    public void adicionarPlanoModalidadeDefault() throws Exception {
        try {
            final NumberFormat nf = NumberFormat.getInstance();
            nf.setMinimumFractionDigits(2);
            List list;
            List lista = getFacade().getModalidade().consultarPorModalidadeDefault(true, planoVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            if (planoVO.getEmpresa().getCodigo() != 0) {
                list = getFacade().getModalidade().consultarPorNome("", 0, true, Uteis.NIVELMONTARDADOS_TODOS);
                Iterator i = list.iterator();
                while (i.hasNext()) {
                    ModalidadeVO object = (ModalidadeVO) i.next();
                    if (object.getModalidadeEmpresaVOs().size() == 0 && object.getModalidadeDefault()) {
                        lista.add(object);
                    }
                }
            }
            Iterator i = lista.iterator();
            PlanoModalidadeVO planoModalidade = new PlanoModalidadeVO();
            while (i.hasNext()) {
                ModalidadeVO objExistente = (ModalidadeVO) i.next();
                planoModalidade.setModalidade(objExistente);
                getPlanoVO().adicionarObjPlanoModalidadeVOs(planoModalidade);
                planoModalidade = new PlanoModalidadeVO();
            }
            adicionarPlanoModalidadeVezesSemanaDefault();
            this.setPlanoModalidadeVO(new PlanoModalidadeVO());
        } catch (Exception ignored) {
        }
    }

    public void adicionarPlanoModalidadeVezesSemanaDefault() {
        try {
            setValorMensal(0.0);
            for (PlanoModalidadeVO pm : getPlanoVO().getPlanoModalidadeVOs()) {
                if (UteisValidacao.emptyList(pm.getPlanoModalidadeVezesSemanaVOs())) {
                    PlanoModalidadeVezesSemanaVO pmVezesSemana = new PlanoModalidadeVezesSemanaVO();
                    pmVezesSemana.setNrVezes(pm.getModalidade().getNrVezes());
                    pm.adicionarObjPlanoModalidadeVezesSemanaVOs(pmVezesSemana);
                }
                setValorMensal(pm.getModalidade().getValorMensal() + getValorMensal());
            }
            this.setPlanoModalidadeVezesSemanaVO(new PlanoModalidadeVezesSemanaVO());
        } catch (Exception ignored) {
        }
    }

    public void adicionarPlanoComposicaoDefault() throws Exception {
        try {
            List lista = getFacade().getComposicao().consultarPorComposicaoDefault(true, planoVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = lista.iterator();
            PlanoComposicaoVO planoComposicao = new PlanoComposicaoVO();
            while (i.hasNext()) {
                ComposicaoVO objExistente = (ComposicaoVO) i.next();
                planoComposicao.setComposicao(objExistente);
                getPlanoVO().adicionarObjPlanoComposicaoVOs(planoComposicao);
                for (ComposicaoModalidadeVO obj : objExistente.getComposicaoModalidadeVOs()) {
                    if (!obj.getModalidade().getModalidadeDefault()) {
                        adicionarModalidade(obj.getModalidade());
                    }
                }
                planoComposicao = new PlanoComposicaoVO();
            }

            this.setPlanoComposicaoVO(new PlanoComposicaoVO());
        } catch (Exception ignored) {
        }
    }

    public void adicionarPlanoProdutoSugeridoDefault() throws Exception {
        ProdutoVO obj = getFacade().getProduto().consultarPorTipoProduto("MA", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj.getCodigo() != 0) {
            getPlanoProdutoSugeridoVO().setProduto(obj);
            getPlanoProdutoSugeridoVO().setValorProduto(obj.getValorFinal());
            getPlanoProdutoSugeridoVO().setObrigatorio(true);
            getPlanoVO().adicionarObjPlanoProdutoSugeridoVOs(getPlanoProdutoSugeridoVO());
            this.setPlanoProdutoSugeridoVO(new PlanoProdutoSugeridoVO());
        }
        obj = getFacade().getProduto().consultarPorTipoProduto("RE", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj.getCodigo() != 0) {
            getPlanoProdutoSugeridoVO().setProduto(obj);
            getPlanoProdutoSugeridoVO().setValorProduto(obj.getValorFinal());
            getPlanoProdutoSugeridoVO().setObrigatorio(true);
            getPlanoVO().adicionarObjPlanoProdutoSugeridoVOs(getPlanoProdutoSugeridoVO());
            this.setPlanoProdutoSugeridoVO(new PlanoProdutoSugeridoVO());
        }
        obj = getFacade().getProduto().consultarPorTipoProduto("RN", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj.getCodigo() != 0) {
            getPlanoProdutoSugeridoVO().setProduto(obj);
            getPlanoProdutoSugeridoVO().setValorProduto(obj.getValorFinal());
            getPlanoProdutoSugeridoVO().setObrigatorio(true);
            getPlanoVO().adicionarObjPlanoProdutoSugeridoVOs(getPlanoProdutoSugeridoVO());
            this.setPlanoProdutoSugeridoVO(new PlanoProdutoSugeridoVO());
        }
    }

    public void adicionarPlanoProdutoPadraoDefault() throws Exception {
        ProdutoVO obj = getFacade().getProduto().consultarPorTipoProduto("MA", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj.getCodigo() != 0) {
            getPlanoProdutoSugeridoVO().setProduto(obj);
            getPlanoProdutoSugeridoVO().setValorProduto(obj.getValorFinal());
            getPlanoProdutoSugeridoVO().setObrigatorio(true);
            getPlanoVO().adicionarObjPlanoProdutoSugeridoVOs(getPlanoProdutoSugeridoVO());
            this.setPlanoProdutoSugeridoVO(new PlanoProdutoSugeridoVO());
        }
        obj = getFacade().getProduto().consultarPorTipoProduto("RE", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj.getCodigo() != 0) {
            getPlanoProdutoSugeridoVO().setProduto(obj);
            getPlanoProdutoSugeridoVO().setValorProduto(obj.getValorFinal());
            getPlanoProdutoSugeridoVO().setObrigatorio(true);
            getPlanoVO().adicionarObjPlanoProdutoSugeridoVOs(getPlanoProdutoSugeridoVO());
            this.setPlanoProdutoSugeridoVO(new PlanoProdutoSugeridoVO());
        }
        obj = getFacade().getProduto().consultarPorTipoProduto("RN", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj.getCodigo() != 0) {
            getPlanoProdutoSugeridoVO().setProduto(obj);
            getPlanoProdutoSugeridoVO().setValorProduto(obj.getValorFinal());
            getPlanoProdutoSugeridoVO().setObrigatorio(true);
            getPlanoVO().adicionarObjPlanoProdutoSugeridoVOs(getPlanoProdutoSugeridoVO());
            this.setPlanoProdutoSugeridoVO(new PlanoProdutoSugeridoVO());
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>tipoValor</code>
     */
    public List getListaSelectItemTipoValor() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable valor = (Hashtable) Dominios.getTipoValor();
        Enumeration keys = valor.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) valor.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }
    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>tipoValor</code>
     */

    public List getListaSelectItemTipoOperacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable valor = (Hashtable) Dominios.getTipoOperacao();
        Enumeration keys = valor.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) valor.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemTipoOperacaoVezesSemana() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable valor = (Hashtable) Dominios.getTipoOperacao();
        Enumeration keys = valor.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) valor.get(value);
            objs.add(new SelectItem(value, label));
        }
        objs.add(new SelectItem("EX", "Exatamente"));
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Método usado para montar o comboBox de vezes de adesão.
     *
     * @return
     */
    public List getListaSelectItemVezesAdesao() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(""));
        for (int i = 0; i < 12; i++) {
            objs.add(new SelectItem(i + 1));
        }
        return objs;
    }

    /**
     * Método usado para montar a comboBox de Dias do mês para o pagamento de
     * anuidade
     *
     * @return
     */
    public List getDiasAnuidade() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(""));
        for (int i = 0; i < 31; i++) {
            objs.add(new SelectItem(i + 1));
        }
        return objs;
    }

    /**
     * Método usado para montar a comboBox de Meses do ano para o pagamento de
     * anuidade
     *
     * @return
     */
    public List getMesesAnuidade() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(""));
        objs.add(new SelectItem(Mes.JANEIRO.getCodigo(), Mes.JANEIRO.getDescricao()));
        objs.add(new SelectItem(Mes.FEVEREIRO.getCodigo(), Mes.FEVEREIRO.getDescricao()));
        objs.add(new SelectItem(Mes.MARCO.getCodigo(), Mes.MARCO.getDescricao()));
        objs.add(new SelectItem(Mes.ABRIL.getCodigo(), Mes.ABRIL.getDescricao()));
        objs.add(new SelectItem(Mes.MAIO.getCodigo(), Mes.MAIO.getDescricao()));
        objs.add(new SelectItem(Mes.JUNHO.getCodigo(), Mes.JUNHO.getDescricao()));
        objs.add(new SelectItem(Mes.JULHO.getCodigo(), Mes.JULHO.getDescricao()));
        objs.add(new SelectItem(Mes.AGOSTO.getCodigo(), Mes.AGOSTO.getDescricao()));
        objs.add(new SelectItem(Mes.SETEMBRO.getCodigo(), Mes.SETEMBRO.getDescricao()));
        objs.add(new SelectItem(Mes.OUTUBRO.getCodigo(), Mes.OUTUBRO.getDescricao()));
        objs.add(new SelectItem(Mes.NOVEMBRO.getCodigo(), Mes.NOVEMBRO.getDescricao()));
        objs.add(new SelectItem(Mes.DEZEMBRO.getCodigo(), Mes.DEZEMBRO.getDescricao()));
        return objs;
    }

    /**
     * Método que verifica qual aba deverá ser aberta (Durações ou Recorrência)
     */
    public void verificaAbaASerAbertaDuracaoOuRecorrencia() {
        if (getPlanoVO().getRegimeRecorrencia()) {
            setMostrarAbaRecorrencia(true);
            setMostrarAbaDuracoes(false);
            if (!getPlanoVO().getSite()) {
                desabilitarVendaCreditoTreino = true;
            }
            if (planoVO.getPlanoDuracaoVOs() == null){
                planoVO.setPlanoDuracaoVOs(new ArrayList<>());
            }
            if (planoVO.getPlanoDuracaoVOs().isEmpty()){
                PlanoDuracaoVO planoDuracaoVO = new PlanoDuracaoVO();
                PlanoRecorrencia.criarPlanoDuracao(planoVO.getPlanoRecorrencia(), planoDuracaoVO);
                planoVO.getPlanoRecorrencia().setPlanoDuracao(planoDuracaoVO);
                planoVO.getPlanoDuracaoVOs().add(planoDuracaoVO);
            }
        } else {
            setMostrarAbaRecorrencia(false);
            setMostrarAbaDuracoes(true);
            desabilitarVendaCreditoTreino = false;
            if (!UteisValidacao.emptyList(planoVO.getPlanoDuracaoVOs())){
                for (PlanoDuracaoVO obj: planoVO.getPlanoDuracaoVOs()){
                    if (obj.isDuracaoPlanoRecorrencia()){
                        planoVO.getPlanoDuracaoVOs().remove(obj);
                        break;
                    }
                }
            }
        }

    }

    public List getListaSelectItemValorDesejado() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable valor = (Hashtable) Dominios.getValorDesejado();
        Enumeration keys = valor.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) valor.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Horario</code>.
     */
    public void montarListaSelectItemHorario(String prm) throws Exception {
        List resultadoConsulta = consultarHorarioPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            HorarioVO obj = (HorarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getCodigo() + " - " + obj.getDescricao()));
        }
        if (objs.size() == 2 && UteisValidacao.emptyNumber(getPlanoVO().getCodigo())) {
            getPlanoHorarioVO().getHorario().setCodigo((Integer) objs.get(1).getValue());
            adicionarPlanoHorario();
        }
        setListaSelectItemHorario(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Horario</code>. Buscando todos os objetos correspondentes a
     * entidade <code>Horario</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemHorario() {
        try {
            montarListaSelectItemHorario("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade      <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista ( <code>List</code>)
     * utilizada para definir os valores a serem apresentados no ComboBox
     * correspondente
     */
    public List consultarHorarioPorDescricao(String descricaoPrm) throws Exception {
        return getFacade().getHorario().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>PlanoSugerido</code>.
     */
    public List consultarPlanoProdutosPadrao() {
        List lista = new ArrayList();
        try {
            lista = getFacade().getPlanoProdutoSugerido().consultarPlanoProdutoSugeridos(getPlanoVO().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = getPlanoVO().getPlanoProdutoSugeridoVOs().iterator();
            while (i.hasNext()) {
                PlanoProdutoSugeridoVO objExistente = (PlanoProdutoSugeridoVO) i.next();
                if ((objExistente.isObrigatorio()) && (objExistente.getProduto().getDescricao().equalsIgnoreCase("Matrícula")
                        || objExistente.getProduto().getDescricao().equalsIgnoreCase("Rematrícula")
                        || objExistente.getProduto().getDescricao().equalsIgnoreCase("Renovado"))) {
                    lista.add(objExistente);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Modalidade</code>.
     */
    public void montarListaSelectItemModalidade(String prm) throws Exception {
        List resultadoConsulta = consultarModalidadePorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ModalidadeVO obj = (ModalidadeVO) i.next();
            Iterator j = obj.getModalidadeEmpresaVOs().iterator();
            while (j.hasNext()) {
                ModalidadeEmpresaVO modalidadeEmpresa = (ModalidadeEmpresaVO) j.next();
                if (modalidadeEmpresa.getEmpresa().getCodigo().equals(planoVO.getEmpresa().getCodigo())) {
                    objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                }
            }
            if (obj.getModalidadeEmpresaVOs().isEmpty()) {
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        }
        Ordenacao.ordenarLista(objs, "label");
        setListaSelectItemModalidade(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Modalidade</code>. Buscando todos os objetos correspondentes a
     * entidade <code>Modalidade</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemModalidade() {
        try {
            montarListaSelectItemModalidade("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * Método responsável por consultar dados da entidade      <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista ( <code>List</code>)
     * utilizada para definir os valores a serem apresentados no ComboBox
     * correspondente
     */
    public List consultarModalidadePorNome(String nomePrm) throws Exception {
        int codEmpresa = this.getPlanoVO().getEmpresa().getCodigo() == 0 ? getEmpresaLogado().getCodigo() : this.getPlanoVO().getEmpresa().getCodigo();
        List tmp = getFacade().getModalidade().consultarPorNomeModalidadeSemDesativado(
                nomePrm,
                codEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        if (codEmpresa != 0) {
            List semEmpresa = getFacade().getModalidade().consultarPorNomeModalidadeSemDesativado(
                    nomePrm,
                    0, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            Iterator i = semEmpresa.iterator();
            while (i.hasNext()) {
                ModalidadeVO m = (ModalidadeVO) i.next();
                if (m.getModalidadeEmpresaVOs().isEmpty()) {
                    tmp.add(m);
                }
            }
        }
        return tmp;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Composicao</code>.
     */
    public void montarListaSelectItemComposicao(String prm) throws Exception {
        List resultadoConsulta = consultarComposicaoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ComposicaoVO obj = (ComposicaoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getCodigo() + " - " + obj.getDescricao()));
        }

        setListaSelectItemComposicao(objs);
    }

    public void montarListaSelectItemPlanoCategoria() {
        try {
            List resultadoConsulta = getFacade().getCategoria().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator i = resultadoConsulta.iterator();
            List<SelectItem> objs = new ArrayList<SelectItem>();
            objs.add(new SelectItem(0, ""));
            while (i.hasNext()) {
                CategoriaVO obj = (CategoriaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }

            setListaSelectItemPlanoCategoria(objs);
        } catch (Exception e){
            montarErro(e);
        }
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Composicao</code>. Buscando todos os objetos correspondentes a
     * entidade <code>Composicao</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemComposicao() {
        try {
            montarListaSelectItemComposicao("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * Método responsável por atualizar o ComboBox mostrada na tela de Durações
     * que contém as Composições acrescentadas ao Plano. Escolhendo a Composição
     * o sistema irá sugerir um valor de referência
     */
    public void montarListaSelectItemComposicaoAdicionada() {
        Iterator i = planoVO.getPlanoComposicaoVOs().iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ComposicaoVO obj = ((PlanoComposicaoVO) i.next()).getComposicao();
            objs.add(new SelectItem(obj.getCodigo(), obj.getCodigo() + " - " + obj.getDescricao()));
        }
        setListaSelectItemComposicaoAdicionada(objs);
    }

    /**
     * Método responsável por consultar dados da entidade      <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista ( <code>List</code>)
     * utilizada para definir os valores a serem apresentados no ComboBox
     * correspondente
     */
    public List consultarComposicaoPorDescricao(String descricaoPrm) throws Exception {
        return getFacade().getComposicao().consultarPorDescricao(descricaoPrm, planoVO.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>CondicaoPagamento</code>.
     */
    public void montarListaSelectItemCondicaoPagamento(String prm) throws Exception {
        List resultadoConsulta = consultarCondicaoPagamentoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            CondicaoPagamentoVO obj = (CondicaoPagamentoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }

        setListaSelectItemCondicaoPagamento(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>CondicaoPagamento</code>. Buscando todos os objetos correspondentes
     * a entidade <code>CondicaoPagamento</code>. Esta rotina não recebe
     * parâmetros para filtragem de dados, isto é importante para a
     * inicialização dos dados da tela para o acionamento por meio requisições
     * Ajax.
     */
    public void montarListaSelectItemCondicaoPagamento() {
        try {
            montarListaSelectItemCondicaoPagamento("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * Método responsável por consultar dados da entidade      <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista ( <code>List</code>)
     * utilizada para definir os valores a serem apresentados no ComboBox
     * correspondente
     */
    public List consultarCondicaoPagamentoPorDescricao(String descricaoPrm) throws Exception {
        return getFacade().getCondicaoPagamento().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Empresa</code>.
     */
    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }

        setListaSelectItemEmpresa(objs);

        if (getEmpresaLogado().getCodigo() != 0) {
            this.getPlanoVO().setEmpresa(getEmpresaLogado());
        }

    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Empresa</code>. Buscando todos os objetos correspondentes a
     * entidade <code>Empresa</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void montarListaSelectTipoPlano() {
        if (this.listaTipoPlano.isEmpty()) {
            this.listaTipoPlano.add(new SelectItem("VI", "Vigente"));
            this.listaTipoPlano.add(new SelectItem("NVI", "Não Vigente"));
            this.listaTipoPlano.add(new SelectItem("TD", "Todos"));
        }
    }

    /**
     * Método responsável por consultar dados da entidade      <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista ( <code>List</code>)
     * utilizada para definir os valores a serem apresentados no ComboBox
     * correspondente
     */
    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNome(nomePrm, true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Produto</code>.
     */
    public void montarListaSelectItemPlanoProduto(String prm) throws Exception {
        List resultadoConsulta = consultarProdutoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ProdutoVO obj = (ProdutoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }

        setListaSelectItemPlanoProdutoSugeriodo(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Produto</code>. Buscando todos os objetos correspondentes a
     * entidade <code>Produto</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemPlanoProduto() {
        try {
            montarListaSelectItemPlanoProduto("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * Método responsável por consultar dados da entidade      <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista ( <code>List</code>)
     * utilizada para definir os valores a serem apresentados no ComboBox
     * correspondente
     */
    public List consultarProdutoPorDescricao(String descricaoPrm) throws Exception {
        return getFacade().getProduto().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Produto</code>.
     */
    public void montarListaSelectItemPlanoTextoPadrao(String prm) throws Exception {
        List resultadoConsulta = consultarPlanoTextoPadraoPorDescricao(0);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        while (i.hasNext()) {
            PlanoTextoPadraoVO obj = (PlanoTextoPadraoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        if (objs.size() > 1) {
            objs.add(0, new SelectItem(0, ""));
        }
        setListaSelectItemPlanoTextoPadrao(objs);
    }

    public void alterarTextoPadraoContratosLancados() {
        try {
            gravar();
            // se permite a impressão de contrato de forma mutável, não é necessário atualizar o html dos contratos
            boolean permiteImpressaoContratoMutavel = getFacade().getConfiguracaoSistema().isPermiteImpressaoContratoMutavel();
            if (getPlanoVO().isPlanoPersonal()){
                getFacade().getPlano().setarPlanoPersonalTextoPadrao(getPlanoVO().getPlanoTextoPadrao().getCodigo(),
                        getPlanoVO().getCodigo());
            } else {
                getFacade().getPlano().setarContratoTextoPadrao(getPlanoVO().getPlanoTextoPadrao().getCodigo(),
                        getPlanoVO().getCodigo(), getUsuarioLogado(), permiteImpressaoContratoMutavel);
            }
            LogVO obj = new LogVO();
            obj.setChavePrimaria(getPlanoVO().getCodigo().toString());
            obj.setNomeEntidade("PLANO");
            obj.setNomeEntidadeDescricao("Plano - Alterar texto padrão dos contratos lançados");
            obj.setOperacao("ALTERAÇÃO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("ALTERAR TEXTO PADRÃO DOS CONTRATOS JÁ LANÇADOS");
            obj.setDataAlteracao(Calendario.hoje());
            obj.setValorCampoAlterado(getPlanoVO().getPlanoTextoPadrao().getCodigo().toString());
            obj.setValorCampoAnterior(" X ");
            registrarLogObjetoVO(obj, getPlanoVO().getCodigo());
            setMensagemID("msg_operacao_sucesso");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void alterarFrequenciaMaximaContratosLancados() {
        try {
            gravar();
            getFacade().getPlano().alterarFrequenciaMaximaContratosLancados(getPlanoVO().getQuantidadeMaximaFrequencia(), getPlanoVO().getCodigo());

            LogVO obj = new LogVO();
            obj.setChavePrimaria(getPlanoVO().getCodigo().toString());
            obj.setNomeEntidade("PLANO");
            obj.setNomeEntidadeDescricao("Plano - Alterar quantidade máxima de frequência dos contratos lançados");
            obj.setOperacao("ALTERAÇÃO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("ALTERAR QUANTIDADE MÁXIMA DE FREQUÊNCIA DOS CONTRATOS JÁ LANÇADOS");
            obj.setDataAlteracao(Calendario.hoje());
            obj.setValorCampoAlterado(getPlanoVO().getQuantidadeMaximaFrequencia().toString());
            obj.setValorCampoAnterior("-");
            registrarLogObjetoVO(obj, getPlanoVO().getCodigo());
            setMensagemID("msg_operacao_sucesso");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Produto</code>. Buscando todos os objetos correspondentes a
     * entidade <code>Produto</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemPlanoTextoPadrao() {
        try {
            montarListaSelectItemPlanoTextoPadrao("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void montarListaSelectItemTermoAceite() {
        try {
            List resultadoConsulta = getFacade().getPlanoTextoPadrao().consultarPorCodigoSituacaoTipo(0, "AT", "VO", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator i = resultadoConsulta.iterator();
            List<SelectItem> objs = new ArrayList<SelectItem>();
            objs.add(new SelectItem(0, ""));
            while (i.hasNext()) {
                PlanoTextoPadraoVO obj = (PlanoTextoPadraoVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }

            setListaSelectItemTermoAceite(objs);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    /**
     * Método responsável por consultar dados da entidade      <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista ( <code>List</code>)
     * utilizada para definir os valores a serem apresentados no ComboBox
     * correspondente
     */
    public List consultarPlanoTextoPadraoPorDescricao(Integer codigoPrm) throws Exception {
        return getFacade().getPlanoTextoPadrao().consultarPorCodigoSituacaoTipo(codigoPrm, "AT", "PL", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Produto</code>.
     */
    public void montarListaSelectItemProdutoPadrao(String prm) throws Exception {
        List resultadoConsulta = consultarProdutoPadraoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        while (i.hasNext()) {
            ProdutoVO obj = (ProdutoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        if (objs.size() > 1) {
            objs.add(0, new SelectItem(0, ""));
        }

        setListaSelectItemProdutoPadrao(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Produto</code>. Buscando todos os objetos correspondentes a
     * entidade <code>Produto</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemProdutoPadrao() {
        try {
            montarListaSelectItemProdutoPadrao("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * Método responsável por consultar dados da entidade      <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista ( <code>List</code>)
     * utilizada para definir os valores a serem apresentados no ComboBox
     * correspondente
     */
    public List consultarProdutoPadraoPorDescricao(String descricaoPrm) throws Exception {
        return getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo(descricaoPrm, "PM", true, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Produto</code>.
     */
    public void montarListaSelectItemProdutoTaxaCancelamento(String prm) throws Exception {
        List resultadoConsulta = consultarProdutoTaxaCancelamentoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ProdutoVO obj = (ProdutoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListaSelectItemProdutoTaxaCancelamento(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Produto</code>. Buscando todos os objetos correspondentes a
     * entidade <code>Produto</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemProdutoTaxaCancelamento() {
        try {
            montarListaSelectItemProdutoTaxaCancelamento("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade      <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista ( <code>List</code>)
     * utilizada para definir os valores a serem apresentados no ComboBox
     * correspondente
     */
    public List consultarProdutoTaxaCancelamentoPorDescricao(String descricaoPrm) throws Exception {
        return getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo(descricaoPrm, "SE", true, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    /**
     * Método responsável por inicializar a lista de valores (
     * <code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemEmpresa();
        // montarListaSelectItemDuracao();
        montarListaSelectItemCondicaoPagamento();
        montarListaSelectItemComposicao();
        montarListaSelectItemModalidade();
        montarListaSelectItemHorario();
        // montarListaSelectItemVezesSemana();
        montarListaSelectItemPlanoProduto();
        montarListaSelectItemPlanoTextoPadrao();
        montarListaSelectItemTermoAceite();
        montarListaSelectItemProdutoPadrao();
        montarListaSelectItemProdutoTaxaCancelamento();
        montarListaSelectTipoPlano();
        montarListaSelectItemConvenioPrivateLabel();
        montarListaSelectItemPlanoCategoria();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = planoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), planoVO.getCodigo(), 0);
    }

    public void realizarConsultaLogObjetoGeral() {
        planoVO = new PlanoVO();
        realizarConsultaLogObjetoSelecionado();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        if (planoVO.getUsuarioVO() == null) {
            inicializarUsuarioLogado();
        }
        if (planoVO.getUsuarioVO().getAdministrador()) {
            itens.add(new SelectItem("nomeEmpresa", "Empresa"));
        }

        itens.add(new SelectItem("vigenciaDe", "Vigência De"));
        itens.add(new SelectItem("vigenciaAte", "Vigência Até"));

        return itens;
    }

    public List<SelectItem> getTipoConsultaComboProduto() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    public List<SelectItem> getTipoConsultaComboDesconto() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("descricao", "Descricao"));
        return itens;
    }

    public List<SelectItem> getTipoConsultaComboTextoPadrao() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        inicializarUsuarioLogado();
        if (planoVO.getUsuarioVO().getAdministrador()) {
            planoVO.setEmpresa(new EmpresaVO());
        }
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }


    public String getTitleQuantidadeMaximaFrequencia() {
        try {
            StringBuilder msg = new StringBuilder();
            msg.append("<b>Informe zero para não restringir o acesso. </b> ");
            msg.append("<br>");
            msg.append("<br><b>Atenção,</b> esta configuração não pode ser utilizada nos casos abaixo:");
            msg.append("<br>1 - Quando o aluno tem contrato concomitante.");
            msg.append("<br>2 - Na negociação do contrato o aluno é incluído em uma turma.");
            if (getUsuarioLogado().getAdministrador()) {
                msg.append(getTitleQuantidadeMaximaFrequenciaAdmin());
            }
            return msg.toString();
        } catch (Exception e) {
            montarErro(e);
        }
        return "";
    }

    public String getTitleQuantidadeMaximaFrequenciaAdmin() {
        StringBuilder msg = new StringBuilder();
        msg.append("<br>");
        msg.append("<br>");
        msg.append("<b>Motivos de não permitir esta configuração para contratos concomitantes:</b>");
        msg.append("<br>1 - Quando o aluno passa pela catraca, não temos como identificar que aquele acesso é referente ao contrato X.");
        msg.append("<br>2 - A informação de quantos acessos o aluno já teve está na tabela sintético do cliente e não está associado a nenhum contrato específico.");
        msg.append("<br>");
        msg.append("<b>Motivos de não permitir esta configuração quando o aluno está inserido em uma turma:</b>");
        msg.append("<br>1 - Pode haver casos onde é configurado (2 vezes por semana) e o usuário vende um plano e lota o aluno na turma em tres dias da semana. Desta forma, o aluno não conseguiria frequentar os 3 dias.");
        return msg.toString();
    }


    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos
     * de persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }

    }

    public List getListaSelectItemHorario() {
        return (listaSelectItemHorario);
    }

    public void setListaSelectItemHorario(List listaSelectItemHorario) {
        this.listaSelectItemHorario = listaSelectItemHorario;
    }

    public List getListaSelectItemHorarioExcecao() {
        Iterator i = planoVO.getPlanoHorarioVOs().iterator();
        List<SelectItem> items = new ArrayList<SelectItem>();
        items.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            PlanoHorarioVO ph = (PlanoHorarioVO) i.next();
            items.add(new SelectItem(ph.getHorario().getCodigo(), ph.getHorario().getDescricao()));
        }
        Ordenacao.ordenarLista(items, "label");
        return items;
    }

    public PlanoHorarioVO getPlanoHorarioVO() {
        return planoHorarioVO;
    }

    public void setPlanoHorarioVO(PlanoHorarioVO planoHorarioVO) {
        this.planoHorarioVO = planoHorarioVO;
    }

    public List getListaSelectItemModalidade() {
        return (listaSelectItemModalidade);
    }

    public void setListaSelectItemModalidade(List listaSelectItemModalidade) {
        this.listaSelectItemModalidade = listaSelectItemModalidade;
    }

    public List getListaSelectItemModalidadeExcecao() {
        Iterator i = planoVO.getPlanoModalidadeVOs().iterator();
        List<SelectItem> items = new ArrayList<SelectItem>();
        items.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            PlanoModalidadeVO pm = (PlanoModalidadeVO) i.next();
            items.add(new SelectItem(pm.getModalidade().getCodigo(), pm.getModalidade().getNome()));
        }
        Ordenacao.ordenarLista(items, "label");
        return items;
    }

    public PlanoModalidadeVO getPlanoModalidadeVO() {
        return planoModalidadeVO;
    }

    public void setPlanoModalidadeVO(PlanoModalidadeVO planoModalidadeVO) {
        this.planoModalidadeVO = planoModalidadeVO;
    }

    public List getListaSelectItemComposicao() {
        return (listaSelectItemComposicao);
    }

    public void setListaSelectItemComposicao(List listaSelectItemComposicao) {
        this.listaSelectItemComposicao = listaSelectItemComposicao;
    }

    public List getListaSelectItemComposicaoAdicionada() {
        if (listaSelectItemComposicaoAdicionada == null) {
            listaSelectItemComposicaoAdicionada = new ArrayList<>();
        }
        return listaSelectItemComposicaoAdicionada;
    }

    public void setListaSelectItemComposicaoAdicionada(List listaSelectItemComposicaoAdicionada) {
        this.listaSelectItemComposicaoAdicionada = listaSelectItemComposicaoAdicionada;
    }

    public PlanoComposicaoVO getPlanoComposicaoVO() {
        return planoComposicaoVO;
    }

    public void setPlanoComposicaoVO(PlanoComposicaoVO planoComposicaoVO) {
        this.planoComposicaoVO = planoComposicaoVO;
    }

    public List getListaSelectItemCondicaoPagamento() {
        return (listaSelectItemCondicaoPagamento);
    }

    public void setListaSelectItemCondicaoPagamento(List listaSelectItemCondicaoPagamento) {
        this.listaSelectItemCondicaoPagamento = listaSelectItemCondicaoPagamento;
    }

    public PlanoCondicaoPagamentoVO getPlanoCondicaoPagamentoVO() {
        return planoCondicaoPagamentoVO;
    }

    public void setPlanoCondicaoPagamentoVO(PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO) {
        this.planoCondicaoPagamentoVO = planoCondicaoPagamentoVO;
    }

    public List getListaSelectItemDuracao() {
        return (listaSelectItemDuracao);
    }

    public void setListaSelectItemDuracao(List listaSelectItemDuracao) {
        this.listaSelectItemDuracao = listaSelectItemDuracao;
    }

    public PlanoDuracaoVO getPlanoDuracaoVO() {
        return planoDuracaoVO;
    }

    public void setPlanoDuracaoVO(PlanoDuracaoVO planoDuracaoVO) {
        this.planoDuracaoVO = planoDuracaoVO;
    }

    public List getListaSelectItemEmpresa() {
        return (listaSelectItemEmpresa);
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public PlanoVO getPlanoVO() {
        return planoVO;
    }

    public void setPlanoVO(PlanoVO planoVO) {
        this.planoVO = planoVO;
    }

    public PlanoModalidadeVezesSemanaVO getPlanoModalidadeVezesSemanaVO() {
        return planoModalidadeVezesSemanaVO;
    }

    public void setPlanoModalidadeVezesSemanaVO(PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaVO) {
        this.planoModalidadeVezesSemanaVO = planoModalidadeVezesSemanaVO;
    }

    public List getListaSelectItemVezesSemana() {
        return (listaSelectItemVezesSemana);
    }

    public void setListaSelectItemVezesSemana(List listaSelectItemVezesSemana) {
        this.listaSelectItemVezesSemana = listaSelectItemVezesSemana;
    }

    public PlanoModalidadeVO getPlanoModalidadeVOSelecionado() {
        return planoModalidadeVOSelecionado;
    }

    public void setPlanoModalidadeVOSelecionado(PlanoModalidadeVO planoModalidadeVOSelecionado) {
        this.planoModalidadeVOSelecionado = planoModalidadeVOSelecionado;
    }

    public List getListaSelectItemPlanoTextoPadrao() {
        if (listaSelectItemPlanoTextoPadrao == null) {
            listaSelectItemPlanoTextoPadrao = new ArrayList();
        }
        return listaSelectItemPlanoTextoPadrao;
    }

    public void setListaSelectItemPlanoTextoPadrao(List listaSelectItemPlanoTextoPadrao) {
        this.listaSelectItemPlanoTextoPadrao = listaSelectItemPlanoTextoPadrao;
    }

    public List getListaSelectItemProdutoPadrao() {
        if (listaSelectItemProdutoPadrao == null) {
            listaSelectItemProdutoPadrao = new ArrayList();
        }
        return listaSelectItemProdutoPadrao;
    }

    public void setListaSelectItemProdutoPadrao(List listaSelectItemProdutoPadrao) {
        this.listaSelectItemProdutoPadrao = listaSelectItemProdutoPadrao;
    }

    public String getCampoConsultaPlanoProduto() {
        return campoConsultaPlanoProduto;
    }

    public void setCampoConsultaPlanoProduto(String campoConsultaPlanoProduto) {
        this.campoConsultaPlanoProduto = campoConsultaPlanoProduto;
    }

    public ComposicaoVO getComposicaoVOValorReferencia() {
        if (getCodigoComposicaoVOValorReferencia() == 0) {
            return null;
        }

        ComposicaoVO obj = null;
        Iterator i = planoVO.getPlanoComposicaoVOs().iterator();
        while (i.hasNext()) {
            ComposicaoVO objTemp = ((PlanoComposicaoVO) i.next()).getComposicao();
            if (objTemp.getCodigo().intValue() == getCodigoComposicaoVOValorReferencia().intValue()) {
                obj = objTemp;
                break;
            }
        }
        return obj;
    }

    public Integer getCodigoComposicaoVOValorReferencia() {
        return codigoComposicaoVOValorReferencia;
    }

    public void setCodigoComposicaoVOValorReferencia(Integer codigo) {
        this.codigoComposicaoVOValorReferencia = codigo;
    }

    public List getListaConsultaPlanoProduto() {
        return listaConsultaPlanoProduto;
    }

    public void setListaConsultaPlanoProduto(List listaConsultaPlanoProduto) {
        this.listaConsultaPlanoProduto = listaConsultaPlanoProduto;
    }

    public List getListaSelectItemPlanoProdutoSugeriodo() {
        return listaSelectItemPlanoProdutoSugeriodo;
    }

    public void setListaSelectItemPlanoProdutoSugeriodo(List listaSelectItemPlanoProdutoSugeriodo) {
        this.listaSelectItemPlanoProdutoSugeriodo = listaSelectItemPlanoProdutoSugeriodo;
    }

    public PlanoProdutoSugeridoVO getPlanoProdutoSugeridoVO() {
        return planoProdutoSugeridoVO;
    }

    public void setPlanoProdutoSugeridoVO(PlanoProdutoSugeridoVO planoProdutoSugeridoVO) {
        this.planoProdutoSugeridoVO = planoProdutoSugeridoVO;
    }

    public String getValorConsultaPlanoProduto() {
        return valorConsultaPlanoProduto;
    }

    public void setValorConsultaPlanoProduto(String valorConsultaPlanoProduto) {
        this.valorConsultaPlanoProduto = valorConsultaPlanoProduto;
    }

    public Double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public Boolean getApresentarPlamoModalidadeVesezSemana() {
        return apresentarPlamoModalidadeVesezSemana;
    }

    public void setApresentarPlamoModalidadeVesezSemana(Boolean apresentarPlamoModalidadeVesezSemana) {
        this.apresentarPlamoModalidadeVesezSemana = apresentarPlamoModalidadeVesezSemana;
    }

    public Boolean getApresentarPlamoCondicaoPagamento() {
        return apresentarPlamoCondicaoPagamento;
    }

    public void setApresentarPlamoCondicaoPagamento(Boolean apresentarPlamoCondicaoPagamento) {
        this.apresentarPlamoCondicaoPagamento = apresentarPlamoCondicaoPagamento;
    }

    public PlanoDuracaoVO getPlanoDuracaoVOSelecionado() {
        return planoDuracaoVOSelecionado;
    }

    public void setPlanoDuracaoVOSelecionado(PlanoDuracaoVO planoDuracaoVOSelecionado) {
        this.planoDuracaoVOSelecionado = planoDuracaoVOSelecionado;
    }

    public List getListaSelectItemProdutoTaxaCancelamento() {
        if (listaSelectItemProdutoTaxaCancelamento == null) {
            listaSelectItemProdutoTaxaCancelamento = new ArrayList();
        }
        return listaSelectItemProdutoTaxaCancelamento;
    }

    public void setListaSelectItemProdutoTaxaCancelamento(List listaSelectItemProdutoTaxaCancelamento) {
        this.listaSelectItemProdutoTaxaCancelamento = listaSelectItemProdutoTaxaCancelamento;
    }

    /**
     * Método responsável por percorrer as Modalidades adicionadas ao Plano
     * calculando o Valor Mensal
     */
    public void calcularValorMensal() {
        Iterator i = getPlanoVO().getPlanoModalidadeVOs().iterator();
        setValorMensal(0.0);
        while (i.hasNext()) {
            PlanoModalidadeVO planoModalidade = (PlanoModalidadeVO) i.next();
            setValorMensal(planoModalidade.getModalidade().getValorMensal() + getValorMensal());
        }
    }

    public void limparComposicaoVOValorReferencia() {
        setCodigoComposicaoVOValorReferencia(0);
        calcularValorMensal();
    }

    public Boolean getApresentarPanelCarencia() {
        return apresentarPanelCarencia;
    }

    public void setApresentarPanelCarencia(Boolean apresentarPanelCarencia) {
        this.apresentarPanelCarencia = apresentarPanelCarencia;
    }

    public String getDiaVencimento() {
        return diaVencimento;
    }

    public void setDiaVencimento(String diaVencimento) {
        this.diaVencimento = diaVencimento;
    }

    public String getCampoConsultaDescontoAntecipado() {
        return campoConsultaDescontoAntecipado;
    }

    public void setCampoConsultaDescontoAntecipado(String campoConsultaDescontoAntecipado) {
        this.campoConsultaDescontoAntecipado = campoConsultaDescontoAntecipado;
    }

    public String getValorConsultaDescontoAntecipado() {
        return valorConsultaDescontoAntecipado;
    }

    public void setValorConsultaDescontoAntecipado(String valorConsultaDescontoAntecipado) {
        this.valorConsultaDescontoAntecipado = valorConsultaDescontoAntecipado;
    }

    public List<DescontoVO> getListaConsultaDescontoAntecipado() {
        return listaConsultaDescontoAntecipado;
    }

    public void setListaConsultaDescontoAntecipado(List<DescontoVO> listaConsultaDescontoAntecipado) {
        this.listaConsultaDescontoAntecipado = listaConsultaDescontoAntecipado;
    }

    /**
     * Início - Atributos de recorrencia /
     * <p>
     * *
     *
     * @return the mostrarAbaDuracoes
     */
    public boolean isMostrarAbaDuracoes() {
        return mostrarAbaDuracoes;
    }

    /**
     * @param mostrarAbaDuracoes the mostrarAbaDuracoes to set
     */
    public void setMostrarAbaDuracoes(boolean mostrarAbaDuracoes) {
        this.mostrarAbaDuracoes = mostrarAbaDuracoes;
    }

    /**
     * @return the mostrarAbaRecorrencia
     */
    public boolean isMostrarAbaRecorrencia() {
        return mostrarAbaRecorrencia;
    }

    /**
     * @param mostrarAbaRecorrencia the mostrarAbaRecorrencia to set
     */
    public void setMostrarAbaRecorrencia(boolean mostrarAbaRecorrencia) {
        this.mostrarAbaRecorrencia = mostrarAbaRecorrencia;
    }

    /**
     * @return the mostrarCampoRegimeRecorrencia
     */
    public boolean isMostrarCampoRegimeRecorrencia() {
        return mostrarCampoRegimeRecorrencia;
    }

    /**
     * @param mostrarCampoRegimeRecorrencia the mostrarCampoRegimeRecorrencia to
     *                                      set
     */
    public void setMostrarCampoRegimeRecorrencia(boolean mostrarCampoRegimeRecorrencia) {
        this.mostrarCampoRegimeRecorrencia = mostrarCampoRegimeRecorrencia;
    }

    public boolean isProcessandoReajuste() {
        return processandoReajuste;
    }

    public void setProcessandoReajuste(boolean processandoReajuste) {
        this.processandoReajuste = processandoReajuste;
    }

    public Integer getPgrAtual() {
        return pgrAtual;
    }

    public void setPgrAtual(Integer pgrAtual) {
        this.pgrAtual = pgrAtual;
    }

    public Integer getPgrTotal() {
        return pgrTotal;
    }

    public void setPgrTotal(Integer pgrTotal) {
        this.pgrTotal = pgrTotal;
    }

    public List getTiposReajuste() {
        return JSFUtilities.getSelectItemListFromEnum(TipoReajuste.class, "descricao", false);
    }

    private void validarDadosObrigatorioAbaReajuste() throws Exception {
        if (this.tipoAlteracaoSelecionado.equals(PlanoVO.TIPO_ALTERACAO_DETALHADO)) {
            if (planoVO.getDataLancamentoContratoInicio() != null && planoVO.getDataLancamentoContratoFim() == null) {
                throw new ConsistirException("Data de lançamento inicial foi informada, com isso a data de lançamento final também  deve ser informada");
            }
        } else {
            if ((this.duracaoSelecionado == null) || (this.duracaoSelecionado <= 0)) {
                throw new ConsistirException("Informe o campo (Duração do contrato)");
            }

            if (this.valorMensalAntigo <= 0) {
                throw new ConsistirException("Informe o campo (Valor da mensalidade atual)");
            }
            if (this.valorMensalNovo <= 0) {
                throw new ConsistirException("Informe o campo (Novo valor da mensalidade)");
            }
        }

    }

    public void processarReajuste(ActionEvent evt) {
        boolean simular = Boolean.valueOf(evt.getComponent().getAttributes().get("simular").toString());
        setMensagemDetalhada("", "");
        setMensagem("");
        setProcessandoReajuste(true);
        try {
            validarDadosObrigatorioAbaReajuste();
            String tiposContratos = resolveTiposContrato();
            StringBuilder resultado = getFacade().getPlano().alterarValoresContratos(
                    planoVO.getTipoReajuste(), planoVO.getValorAcrescentar(),
                    planoVO.getCodigo(), planoVO.getDataVencimentoParcela(),
                    getUsuarioLogado(), simular, tiposContratos, planoVO.getDataLancamentoContratoInicio(),
                    planoVO.getDataLancamentoContratoFim(), this.valorMensalAntigo, this.valorMensalNovo, this.codigoContratoSelecionado, this.duracaoSelecionado,
                    this.tipoAlteracaoSelecionado, false, null, null, null);
            if (resultado.length() > 0) {
                if (!simular) {
                    Uteis.logar(new ConsistirException("Chave: " + getKey() + " - Plano: " + planoVO.getCodigo()
                            + " - " + resultado.toString()), PlanoControle.class);
                }
                planoVO.setDescricaoProgresso(resultado.toString());
            } else {
                planoVO.setDescricaoProgresso("");
                throw new ConsistirException("Nenhum reajuste foi possível de ser aplicado. Nenhum contrato compatível com os parâmetros informados.");
            }
            String msg = simular ? "msg_simulacao_efetuada" : "msg_reajuste_efetuado";
            setMensagemID(msg);
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        } finally {
            setProcessandoReajuste(false);
            setPgrAtual(0);
            setPgrTotal(0);
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getPlano().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(), getTipoPlano());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    public List<SelectItem> getListaVezesParcelarAdesao() {
        if (listaVezesParcelarAdesao == null) {
            montarListaParcelasAdesao();
        }
        return listaVezesParcelarAdesao;
    }

    public void montarListaParcelasAdesao() {
        planoVO.getPlanoRecorrencia().setParcelasAnuidade(new ArrayList<PlanoAnuidadeParcelaVO>());
        listaVezesParcelarAdesao = new ArrayList<SelectItem>();
        Integer max = planoVO.getPlanoRecorrencia().getDuracaoPlano() == null ? 1 : planoVO.getPlanoRecorrencia().getDuracaoPlano();
        for (Integer i = 1; i <= max; i++) {
            listaVezesParcelarAdesao.add(new SelectItem(i, i.toString()));
        }
        montarListaParcelasParaAlteracaoValor();
        montarListaParcelasAnuidadeParcela();
    }

    public void montarListaParcelasMatricula() {
        listaVezesParcelarMatricula = new ArrayList<SelectItem>();
        Integer max = obterNumeroMaximoVezesCondicaoPagamento();
        for (Integer i = 1; i <= max; i++) {
            listaVezesParcelarMatricula.add(new SelectItem(i, i.toString()));
        }
    }

    private Integer obterNumeroMaximoVezesCondicaoPagamento() {
        Integer maximo = 1;
        if (!UteisValidacao.emptyList(planoVO.getPlanoDuracaoVOs())) {
            int index = planoVO.getPlanoDuracaoVOs().size() - 1;
            if (!UteisValidacao.emptyList(planoVO.getPlanoDuracaoVOs().get(index).getPlanoCondicaoPagamentoVOs())) {
                for (PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO : planoVO.getPlanoDuracaoVOs().get(index).getPlanoCondicaoPagamentoVOs()) {
                    if (planoCondicaoPagamentoVO.getCondicaoPagamento().getNrParcelas() > maximo) {
                        maximo = planoCondicaoPagamentoVO.getCondicaoPagamento().getNrParcelas();
                    }
                }

            }
        }
        return maximo;
    }

    public List<SelectItem> getListaParcelaAnuidadeRecorrencia() {
        List<SelectItem> retorno = new ArrayList<SelectItem>();
        Integer max = planoVO.getPlanoRecorrencia().getDuracaoPlano() == null ? 1 : planoVO.getPlanoRecorrencia().getDuracaoPlano();
        for (Integer i = 1; i <= max; i++) {
            retorno.add(new SelectItem(i, "PARCELA " + i.toString()));
        }
        return retorno;
    }


    public String getValorAjustadoModalidade_Apresentar() {
        return Formatador.formatarValorMonetario(getValorAjustadoModalidade());
    }

    public Double getValorAjustadoModalidade() {
        return valorAjustadoModalidade;
    }

    public void setValorAjustadoModalidade(Double valorAjustadoModalidade) {
        this.valorAjustadoModalidade = valorAjustadoModalidade;
    }

    public void calcularValorAjustado() {
        if (getPlanoModalidadeVezesSemanaVO().getApresentarValorEspecifico()) {
            if (getPlanoModalidadeVezesSemanaVO().getTipoOperacao().equals("AC")) {
                setValorAjustadoModalidade(getPlanoModalidadeVOSelecionado().getModalidade().getValorMensal() + getPlanoModalidadeVezesSemanaVO().getValorEspecifico());
            } else {
                setValorAjustadoModalidade(getPlanoModalidadeVOSelecionado().getModalidade().getValorMensal() - getPlanoModalidadeVezesSemanaVO().getValorEspecifico());
            }
        } else {
            if (getPlanoModalidadeVezesSemanaVO().getTipoOperacao().equals("AC")) {
                setValorAjustadoModalidade(getPlanoModalidadeVOSelecionado().getModalidade().getValorMensal() * ((100 + getPlanoModalidadeVezesSemanaVO().getPercentualDesconto()) / 100));
            } else {
                setValorAjustadoModalidade(getPlanoModalidadeVOSelecionado().getModalidade().getValorMensal() * ((100 - getPlanoModalidadeVezesSemanaVO().getPercentualDesconto()) / 100));
            }
        }
        if (getPlanoModalidadeVezesSemanaVO().getTipoOperacao().equals("EX")) {
            setValorAjustadoModalidade(getPlanoModalidadeVezesSemanaVO().getValorEspecifico());
        }
    }

    public String getMsgModal() {
        return msgModal;
    }

    public void setMsgModal(String msgModal) {
        this.msgModal = msgModal;
    }

    public String getOnCompleteGravar() {
        return onCompleteGravar;
    }

    public void setOnCompleteGravar(String onCompleteGravar) {
        this.onCompleteGravar = onCompleteGravar;
    }

    public void tratarTipoOperacaoVezesSemana() {
        if (planoModalidadeVezesSemanaVO != null) {
            if (planoModalidadeVezesSemanaVO.getTipoOperacao().equals("EX")) {
                planoModalidadeVezesSemanaVO.setTipoValor("VE");
            }
        }
    }

    public PlanoExcecaoVO getPlanoExcecaoVOSelecionado() {
        return planoExcecaoVOSelecionado;
    }

    public void setPlanoExcecaoVOSelecionado(PlanoExcecaoVO planoExcecaoVOSelecionado) {
        this.planoExcecaoVOSelecionado = planoExcecaoVOSelecionado;
    }

    private String resolveTiposContrato() throws Exception {
        StringBuilder tipos = new StringBuilder("");
        if (planoVO.getContratosMatricula()) {
            tipos.append("'MA',");
        }
        if (planoVO.getContratosRematricula()) {
            tipos.append("'RE',");
        }
        if (planoVO.getContratosRenovacao()) {
            tipos.append("'RN',");
        }
        if (!tipos.toString().equals("")) {
            tipos.deleteCharAt(tipos.length() - 1);
        } else {
            throw new ConsistirException("Selecione ao menos um tipo de Contrato!");
        }
        return tipos.toString();
    }

    public void selecionouSite() {
        getPlanoVO().setRegimeRecorrencia(getPlanoVO().isVendaCreditoTreino() ? false : getPlanoVO().getSite());
        getPlanoVO().setCreditoSessao(false);
        getPlanoVO().setApresentaVendaRapida(false);
        verificaAbaASerAbertaDuracaoOuRecorrencia();
    }

    public void selecionouCredito() {
        if (getPlanoVO().getSite() && getPlanoVO().isVendaCreditoTreino()) {
            getPlanoVO().setRegimeRecorrencia(false);
            setMostrarAbaDuracoes(true);
            setMostrarAbaRecorrencia(false);
        }
    }

    public void consultarContratosRenovacaoAutomatica() throws Exception {
        planoVO.setRenovarProdutoObrigatorio(false);
        if (this.listaContratosRenovacaoAutomatica.isEmpty()) {
            this.listaContratosRenovacaoAutomatica = getFacade().getContrato().consultarContratosAtivosPorPlano(getPlanoVO().getCodigo(), getPlanoVO().getEmpresa().getCodigo());
        }
        this.textoLinkRenovacaoAutomatica = null;
        if (!this.listaContratosRenovacaoAutomatica.isEmpty()) {
            boolean marcado = (this.planoVO.getRegimeRecorrencia()) ? this.planoVO.getPlanoRecorrencia().getRenovavelAutomaticamente() : this.planoVO.getRenovavelAutomaticamente();
            if (marcado) {
                if (this.listaContratosRenovacaoAutomatica.size() == 1)
                    this.textoLinkRenovacaoAutomatica = "Atenção ! '" + this.listaContratosRenovacaoAutomatica.size() + "' contrato será renovado automaticamente.";
                else
                    this.textoLinkRenovacaoAutomatica = "Atenção ! '" + this.listaContratosRenovacaoAutomatica.size() + "' contratos serão renovados automaticamente.";
            } else {
                if (this.listaContratosRenovacaoAutomatica.size() == 1)
                    this.textoLinkRenovacaoAutomatica = "Atenção ! '" + this.listaContratosRenovacaoAutomatica.size() + "' contrato não será mais renovado automaticamente.";
                else
                    this.textoLinkRenovacaoAutomatica = "Atenção ! '" + this.listaContratosRenovacaoAutomatica.size() + "' contratos não serão mais renovados automaticamente.";
            }
        }

    }

    public String getTotalContratosRenovarAuto() {
        if (this.listaContratosRenovacaoAutomatica.isEmpty())
            return "";
        boolean marcado = (this.planoVO.getRegimeRecorrencia()) ? this.planoVO.getPlanoRecorrencia().getRenovavelAutomaticamente() : this.planoVO.getRenovavelAutomaticamente();
        if (marcado) {
            if (this.listaContratosRenovacaoAutomatica.size() == 1)
                return "'" + this.listaContratosRenovacaoAutomatica.size() + "' contrato será renovado automaticamente.";
            else
                return "'" + this.listaContratosRenovacaoAutomatica.size() + "' contratos serão renovados automaticamente.";
        } else {
            if (this.listaContratosRenovacaoAutomatica.size() == 1)
                return "'" + this.listaContratosRenovacaoAutomatica.size() + "' contrato não será mais renovados automaticamente.";
            else
                return "'" + this.listaContratosRenovacaoAutomatica.size() + "' contratos não serão mais renovados automaticamente.";
        }
    }

    public String getTextoLinkRenovacaoAutomatica() {
        return textoLinkRenovacaoAutomatica;
    }

    public void setTextoLinkRenovacaoAutomatica(String textoLinkRenovacaoAutomatica) {
        this.textoLinkRenovacaoAutomatica = textoLinkRenovacaoAutomatica;
    }

    public List<ContratoVO> getListaContratosRenovacaoAutomatica() {
        return listaContratosRenovacaoAutomatica;
    }

    public void setListaContratosRenovacaoAutomatica(List<ContratoVO> listaContratosRenovacaoAutomatica) {
        this.listaContratosRenovacaoAutomatica = listaContratosRenovacaoAutomatica;
    }

    public boolean isMostrarConfiguracaoDuracaoCreditoTreino() {
        return mostrarConfiguracaoDuracaoCreditoTreino;
    }

    public void setMostrarConfiguracaoDuracaoCreditoTreino(boolean mostrarConfiguracaoDuracaoCreditoTreino) {
        this.mostrarConfiguracaoDuracaoCreditoTreino = mostrarConfiguracaoDuracaoCreditoTreino;
    }

    public PlanoDuracaoCreditoTreinoVO getPlanoDuracaoCreditoTreinoVO() {
        return planoDuracaoCreditoTreinoVO;
    }

    public void setPlanoDuracaoCreditoTreinoVO(PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO) {
        this.planoDuracaoCreditoTreinoVO = planoDuracaoCreditoTreinoVO;
    }

    public List<SelectItem> getListaTipoHorarioCreditoTreino() {
        return listaTipoHorarioCreditoTreino;
    }

    public void setListaTipoHorarioCreditoTreino(List<SelectItem> listaTipoHorarioCreditoTreino) {
        this.listaTipoHorarioCreditoTreino = listaTipoHorarioCreditoTreino;
    }

    public Boolean getDesabilitarVendaCreditoTreino() {
        return desabilitarVendaCreditoTreino;
    }

    public void setDesabilitarVendaCreditoTreino(Boolean desabilitarVendaCreditoTreino) {
        this.desabilitarVendaCreditoTreino = desabilitarVendaCreditoTreino;
    }

    public Integer getQtdContratosCancelamentoAutomatico() {
        if (qtdContratosCancelamentoAutomatico == null) {
            qtdContratosCancelamentoAutomatico = 0;
        }
        return qtdContratosCancelamentoAutomatico;
    }

    public void setQtdContratosCancelamentoAutomatico(Integer qtdContratosCancelamentoAutomatico) {
        this.qtdContratosCancelamentoAutomatico = qtdContratosCancelamentoAutomatico;
    }

    public String getMsgContratosCancelamentoAutomatico() {
        if (msgContratosCancelamentoAutomatico == null) {
            msgContratosCancelamentoAutomatico = "";
        }
        return msgContratosCancelamentoAutomatico;
    }

    public void setMsgContratosCancelamentoAutomatico(String msgContratosCancelamentoAutomatico) {
        this.msgContratosCancelamentoAutomatico = msgContratosCancelamentoAutomatico;
    }

    public boolean isApresentarBtnContratosCancelamentoAutomatico() {
        return apresentarBtnContratosCancelamentoAutomatico;
    }

    public void setApresentarBtnContratosCancelamentoAutomatico(boolean apresentarBtnContratosCancelamentoAutomatico) {
        this.apresentarBtnContratosCancelamentoAutomatico = apresentarBtnContratosCancelamentoAutomatico;
    }

    public void consultarContratosDiasVencimentoCancelamentoAutomatico() {
        try {
            this.qtdContratosCancelamentoAutomatico = getFacade().getPlano().consultarTotalContratosAlterarQtdDiasCancelamentoAutomatico(getPlanoVO().getCodigo());
            if (this.qtdContratosCancelamentoAutomatico > 0) {
                this.msgContratosCancelamentoAutomatico = this.qtdContratosCancelamentoAutomatico + " contratos serão modificados para cancelamento automático caso tenha uma parcela vencida a mais de "
                        + getPlanoVO().getPlanoRecorrencia().getQtdDiasAposVencimentoCancelamentoAutomatico() + " dias.";
                this.apresentarBtnContratosCancelamentoAutomatico = true;
            } else {
                this.apresentarBtnContratosCancelamentoAutomatico = false;
            }
            setOnCompleteGravar("Richfaces.showModalPanel('modalContratosCancelamentoAutoma');");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void ajustarConsultarContratosDiasVencimentoCancelamentoAutomatico() {
        try {
            getFacade().getPlano().alterarContratosAlterarQtdDiasCancelamentoAutomatico(getPlanoVO().getCodigo(), getPlanoVO().getPlanoRecorrencia().getQtdDiasAposVencimentoCancelamentoAutomatico());
            registraLogAlteracaoDiasCancelamentoAutomatico();
            this.msgContratosCancelamentoAutomatico = "Foram modificados " + this.qtdContratosCancelamentoAutomatico + " contratos.";
            this.apresentarBtnContratosCancelamentoAutomatico = false;
            setMensagem(this.msgContratosCancelamentoAutomatico);
            setSucesso(true);
            setAtencao(false);
            setWarning(false);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void registraLogAlteracaoDiasCancelamentoAutomatico() throws Exception {
        try {

            LogVO obj = new LogVO();
            obj.setChavePrimaria(getPlanoVO().getCodigo().toString());
            obj.setNomeEntidade("PLANO");
            obj.setNomeEntidadeDescricao("Plano");
            obj.setOperacao("ALTERAÇÃO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("");

            String msg = "Foram modificados " + this.qtdContratosCancelamentoAutomatico + " contratos para cancelar automáticamente caso tenha uma parcela vencida a mais de "
                    + getPlanoVO().getPlanoRecorrencia().getQtdDiasAposVencimentoCancelamentoAutomatico() + " dias.";

            obj.setValorCampoAlterado(msg);
            obj.setDataAlteracao(Calendario.hoje());
            registrarLogObjetoVO(obj, getPlanoVO().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("USUARIO", getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PLANO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }


    public void incluirLogInclusao() throws Exception {
        try {
            planoVO.setObjetoVOAntesAlteracao(new PlanoVO());
            planoVO.setNovoObj(true);
            registrarLogObjetoVO(planoVO, planoVO.getCodigo(), "PLANO", 0);
            planoVO.getPlanoRecorrencia().setObjetoVOAntesAlteracao(new PlanoRecorrenciaVO());
            planoVO.getPlanoRecorrencia().setNovoObj(true);
            registrarLogObjetoVO(planoVO.getPlanoRecorrencia(), planoVO.getCodigo(), "PLANO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PLANO", planoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PLANO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        planoVO.setNovoObj(new Boolean(false));
        planoVO.getPlanoRecorrencia().setNovoObj(new Boolean(false));
        planoVO.registrarObjetoVOAntesDaAlteracao();
        planoVO.getPlanoRecorrencia().registrarObjetoVOAntesDaAlteracao();

    }

    public void incluirLogExclusao() throws Exception {
        try {
            planoVO.setObjetoVOAntesAlteracao(new PlanoVO());
            planoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(planoVO, planoVO.getCodigo(), "PLANO", 0);
            planoVO.getPlanoRecorrencia().setObjetoVOAntesAlteracao(new PlanoRecorrenciaVO());
            planoVO.getPlanoRecorrencia().setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(planoVO.getPlanoRecorrencia(), planoVO.getCodigo(), "PLANO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PLANO", planoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PLANO ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     *
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(planoVO, planoVO.getCodigo(), "PLANO", 0);
            registrarLogObjetoVO(planoVO.getPlanoRecorrencia(), planoVO.getCodigo(), "PLANO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PLANO", planoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PLANO ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        planoVO.registrarObjetoVOAntesDaAlteracao();
        planoVO.getPlanoRecorrencia().registrarObjetoVOAntesDaAlteracao();
    }

    public void montarTitlePlanoCreditoTreino() {
        try {
            StringBuilder msg = new StringBuilder();
            if (desabilitarVendaCreditoTreino) {
                msg.append("<b>Não pode ser alterado pois existem contratos cadastrados para este plano. </b> ");
                msg.append("<br>");
                msg.append("<br>");
            }
            msg.append("<b>Atenção,</b> é permitido a concomitância de contrato de crédito com contratos não créditos, exceto se ambos contratos forem HORÁRIO LIVRE;");
            msg.append("Porém o aluno só vai conseguir marcar aulas no (app-treino) para o contrato de crédito.");
            if (getUsuarioLogado().getAdministrador()) {
                msg.append(getTitlePlanoCreditoTreinoAdmin());
            }
            this.titlePlanoCreditoTreino = msg.toString();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String getTitlePlanoCreditoTreinoAdmin() {
        StringBuilder msg = new StringBuilder();
        msg.append("<br>");
        msg.append("Para contratos concomitantes, o aluno vai visualizar somente o contrato de crédito no perfil do app-treino, também só vai conseguir marcar aula do contrato de crédito. Temos que criar um projeto futuro para alterar o app-treino para contemplar mais de um contrato.");
        msg.append("<br>");
        msg.append("<b>NÃO VAMOS PERMITIR LANÇAR DOIS CONTRATOS DE CRÉDITOS CONCOMITANTES, </b>");
        msg.append("pois o impacto no app treino são inúmeras.(Ex: a aba contrato do perfil do aluno, foi projetada para exibir informações de somente um contrato;");
        msg.append("A tela de saldo de créditos também foi projetada para somente um contrato;");
        msg.append("As informações de saldo de crédito do aluno foi projetado para somente um contrato, pois esta informação está no sintético do cliente.");
        msg.append("Se o cliente desejar ter dois contratos de crédito concomitante,");
        msg.append("basta criar um plano de crédito e configurar as modalidades necessárias, desta forma,");
        msg.append("o aluno terá somente um contrato com mais de uma modalidade.");
        return msg.toString();
    }

    public String getTitlePlanoCreditoTreino() {
        return titlePlanoCreditoTreino;
    }

    public void setTitlePlanoCreditoTreino(String titlePlanoCreditoTreino) {
        this.titlePlanoCreditoTreino = titlePlanoCreditoTreino;
    }

    public boolean isHabilitarCampoVezesPorSemana() {
        return habilitarCampoVezesPorSemana;
    }

    public void setHabilitarCampoVezesPorSemana(boolean habilitarCampoVezesPorSemana) {
        this.habilitarCampoVezesPorSemana = habilitarCampoVezesPorSemana;
    }

    public boolean isHabilitarCampoHorario() {
        return habilitarCampoHorario;
    }

    public void setHabilitarCampoHorario(boolean habilitarCampoHorario) {
        this.habilitarCampoHorario = habilitarCampoHorario;
    }

    public boolean isHabilitarCampoNrMeses() {
        return habilitarCampoNrMeses;
    }

    public void setHabilitarCampoNrMeses(boolean habilitarCampoNrMeses) {
        this.habilitarCampoNrMeses = habilitarCampoNrMeses;
    }

    public boolean isHabilitarCampoCondicaoPagamento() {
        return habilitarCampoCondicaoPagamento;
    }

    public void setHabilitarCampoCondicaoPagamento(boolean habilitarCampoCondicaoPagamento) {
        this.habilitarCampoCondicaoPagamento = habilitarCampoCondicaoPagamento;
    }

    public String getTitleEdicaoVezesPorSemana() {
        if (!habilitarCampoVezesPorSemana) {
            return "Edição não permite a alteração desse campo. Caso não queira mais esse número de vezes, exclua da tabela";
        }
        return "";
    }

    public String getTitleEdicaoHorario() {
        if (!habilitarCampoHorario) {
            return "Edição não permite a alteração desse campo. Caso não queira mais esse horário, exclua da tabela";
        }
        return "";
    }

    public String getTitleEdicaoDuracao() {
        if (!habilitarCampoNrMeses) {
            return "Edição não permite a alteração desse campo. Caso não queira mais essa duração, exclua da tabela";
        }
        return "";
    }

    public String getTitleEdicaoCondicaoPagamento() {
        if (!habilitarCampoCondicaoPagamento) {
            return "Edição não permite a alteração desse campo. Caso não queira mais essa condição, exclua da tabela";
        }
        return "";
    }

    private void validarExisteContratoDuracaoCondicao(Integer codigoPlano, Integer duracao, boolean validarCondicaoPagamento) throws Exception {
        if (getFacade().getPlano().existeContratoDuracaoCondicao(codigoPlano, duracao, validarCondicaoPagamento)) {
            if (validarCondicaoPagamento) {
                throw new ConsistirException("Condição de pagamento não pode ser excluída, pois está vinculada a contratos");
            }
            throw new ConsistirException("Duração não pode ser excluída, pois está vinculada a contratos");
        }
    }

    public String getTipoAlteracaoSelecionado() {
        return tipoAlteracaoSelecionado;
    }

    public void setTipoAlteracaoSelecionado(String tipoAlteracaoSelecionado) {
        this.tipoAlteracaoSelecionado = tipoAlteracaoSelecionado;
    }

    public List<SelectItem> getListaTipoAlteracao() {
        return listaTipoAlteracao;
    }

    public void setListaTipoAlteracao(List<SelectItem> listaTipoAlteracao) {
        this.listaTipoAlteracao = listaTipoAlteracao;
    }

    public double getValorMensalAntigo() {
        return valorMensalAntigo;
    }

    public void setValorMensalAntigo(double valorMensalAntigo) {
        this.valorMensalAntigo = valorMensalAntigo;
    }

    public double getValorMensalNovo() {
        return valorMensalNovo;
    }

    public void setValorMensalNovo(double valorMensalNovo) {
        this.valorMensalNovo = valorMensalNovo;
    }

    public Integer getCodigoContratoSelecionado() {
        return codigoContratoSelecionado;
    }

    public void setCodigoContratoSelecionado(Integer codigoContratoSelecionado) {
        this.codigoContratoSelecionado = codigoContratoSelecionado;
    }

    public Integer getDuracaoSelecionado() {
        return duracaoSelecionado;
    }

    public void setDuracaoSelecionado(Integer duracaoSelecionado) {
        this.duracaoSelecionado = duracaoSelecionado;
    }

    public List<SelectItem> getListaTipoPlano() {
        return listaTipoPlano;
    }

    public String getTipoPlano() {
        if (tipoPlano == null) {
            tipoPlano = "VI";
        }
        return tipoPlano;
    }

    public void setTipoPlano(String tipoPlano) {
        this.tipoPlano = tipoPlano;
    }

    public List<SelectItem> getListaVezesParcelarMatricula() {
        if (listaVezesParcelarMatricula == null) {
            montarListaParcelasMatricula();
        }
        return listaVezesParcelarMatricula;
    }

    public void setListaVezesParcelarMatricula(List<SelectItem> listaVezesParcelarMatricula) {
        this.listaVezesParcelarMatricula = listaVezesParcelarMatricula;
    }

    public List getListaSelectItemTermoAceite() {
        return listaSelectItemTermoAceite;
    }

    public void setListaSelectItemTermoAceite(List listaSelectItemTermoAceite) {
        this.listaSelectItemTermoAceite = listaSelectItemTermoAceite;
    }

    public void montarListaSelectItemConvenioPrivateLabel() {
        try {
            Integer tipos[] = {TipoConvenioCobrancaEnum.DCC_FITNESS_CARD.getCodigo()};
            List<ConvenioCobrancaVO> resultadoConsulta = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(getPlanoVO().getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS, tipos, SituacaoConvenioCobranca.ATIVO);
            listaSelectConvenioCobrancaPrivateLabel = new ArrayList<SelectItem>();
            listaSelectConvenioCobrancaPrivateLabel.add(new SelectItem(0, ""));
            for (ConvenioCobrancaVO obj : resultadoConsulta) {
                listaSelectConvenioCobrancaPrivateLabel.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List<SelectItem> getListaSelectConvenioCobrancaPrivateLabel() {
        if (listaSelectConvenioCobrancaPrivateLabel == null) {
            listaSelectConvenioCobrancaPrivateLabel = new ArrayList<SelectItem>();
        }
        return listaSelectConvenioCobrancaPrivateLabel;
    }

    public void setListaSelectConvenioCobrancaPrivateLabel(List<SelectItem> listaSelectConvenioCobrancaPrivateLabel) {
        this.listaSelectConvenioCobrancaPrivateLabel = listaSelectConvenioCobrancaPrivateLabel;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public boolean isCheckTodasAcessoEmpresas() {
        return checkTodasAcessoEmpresas;
    }

    public void setCheckTodasAcessoEmpresas(boolean checkTodasAcessoEmpresas) {
        this.checkTodasAcessoEmpresas = checkTodasAcessoEmpresas;
    }

    public boolean isCheckTodasVendaEmpresas() {
        return checkTodasVendaEmpresas;
    }

    public void setCheckTodasVendaEmpresas(boolean checkTodasVendaEmpresas) {
        this.checkTodasVendaEmpresas = checkTodasVendaEmpresas;
    }

    public Integer getQtdContratosCancelamentoProporcional() {
        if (qtdContratosCancelamentoProporcional == null) {
            qtdContratosCancelamentoProporcional = 0;
        }
        return qtdContratosCancelamentoProporcional;
    }

    public void setQtdContratosCancelamentoProporcional(Integer qtdContratosCancelamentoProporcional) {
        this.qtdContratosCancelamentoProporcional = qtdContratosCancelamentoProporcional;
    }

    public String getMsgContratosCancelamentoProporcional() {
        if (msgContratosCancelamentoProporcional == null) {
            msgContratosCancelamentoProporcional = "";
        }
        return msgContratosCancelamentoProporcional;
    }

    public void setMsgContratosCancelamentoProporcional(String msgContratosCancelamentoProporcional) {
        this.msgContratosCancelamentoProporcional = msgContratosCancelamentoProporcional;
    }

    public boolean isApresentarBtnContratosCancelamentoProporcional() {
        return apresentarBtnContratosCancelamentoProporcional;
    }

    public void setApresentarBtnContratosCancelamentoProporcional(boolean apresentarBtnContratosCancelamentoProporcional) {
        this.apresentarBtnContratosCancelamentoProporcional = apresentarBtnContratosCancelamentoProporcional;
    }

    public void consultarContratosCancelamentoPropocional() {
        try {
            this.qtdContratosCancelamentoProporcional = getFacade().getPlano().consultarTotalContratosCancelamentoProporcional(getPlanoVO().getCodigo());
            if (this.qtdContratosCancelamentoProporcional > 0) {
                this.msgContratosCancelamentoProporcional = this.qtdContratosCancelamentoProporcional + " contratos serão modificados.";
                this.apresentarBtnContratosCancelamentoProporcional = true;
            } else {
                this.apresentarBtnContratosCancelamentoProporcional = false;
            }
            setOnCompleteGravar("Richfaces.showModalPanel('modalContratosCancelamentoProporcional');");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void ajustarConsultarContratosCancelamentoProporcional() {
        try {
            getFacade().getPlano().alterarContratosCancelamentoProporcional(getPlanoVO().getCodigo(),
                    getPlanoVO().getPlanoRecorrencia().isCancelamentoProporcional(),
                    getPlanoVO().getPlanoRecorrencia().getQtdDiasCobrarProximaParcela(),
                    getPlanoVO().getPlanoRecorrencia().getQtdDiasCobrarAnuidadeTotal(),
                    getPlanoVO().getPlanoRecorrencia().isCancelamentoProporcionalSomenteRenovacao());
            registraLogAlteracaoDiasCancelamentoProporcional();
            this.msgContratosCancelamentoProporcional = "Foram modificados " + this.qtdContratosCancelamentoProporcional + " contratos.";
            this.apresentarBtnContratosCancelamentoProporcional = false;
            setMensagem(this.msgContratosCancelamentoProporcional);
            setSucesso(true);
            setAtencao(false);
            setWarning(false);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void registraLogAlteracaoDiasCancelamentoProporcional() throws Exception {
        try {

            LogVO obj = new LogVO();
            obj.setChavePrimaria(getPlanoVO().getCodigo().toString());
            obj.setNomeEntidade("PLANO");
            obj.setNomeEntidadeDescricao("Plano");
            obj.setOperacao("ALTERAÇÃO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("");

            String msg = "Foram modificados " + this.qtdContratosCancelamentoProporcional + " contratos.\nConfiguração: \n"
                    + "\nHabilitar método de cancelamento verificando próxima parcela em aberto: " + (getPlanoVO().getPlanoRecorrencia().isCancelamentoProporcional() ? "SIM" : "NÃO")
                    + "\nQuantidade de dias avisar com antecedência para não cobrar próxima parcela\": " + getPlanoVO().getPlanoRecorrencia().getQtdDiasCobrarProximaParcela()
                    + "\nQuantidade de dias até a data de término do contrato para cobrar valor total da anuidade\": " + getPlanoVO().getPlanoRecorrencia().getQtdDiasCobrarAnuidadeTotal();

            obj.setValorCampoAlterado(msg);
            obj.setDataAlteracao(Calendario.hoje());
            registrarLogObjetoVO(obj, getPlanoVO().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("USUARIO", getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PLANO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void montarListaParcelasParaAlteracaoValor() {
        List<SelectItem> parcelas = new ArrayList<SelectItem>();
        Integer max = planoVO.getPlanoRecorrencia().getDuracaoPlano() == null ? 1 : planoVO.getPlanoRecorrencia().getDuracaoPlano();
        boolean parcelaAlterada;
        for (Integer i = 1; i <= max; i++) {
            parcelaAlterada = false;
            for (PlanoRecorrenciaParcelaVO parcela :
                    getPlanoVO().getPlanoRecorrencia().getParcelas()) {
                if (parcela.getNumero() == i) {
                    parcelaAlterada = true;
                }
            }
            if (!parcelaAlterada) {
                parcelas.add(new SelectItem(i, "PARCELA " + i.toString()));
            }
        }
        setListaParcelasParaAlteracaoValor(parcelas);
    }

    public void adicionarParcelaValorAlterado() {
        try {
            PlanoRecorrenciaParcelaVO parcela = (PlanoRecorrenciaParcelaVO) getParcelaValorAlterado().getClone(true);

            if (parcela.getValorFinal().equals(getPlanoVO().getPlanoRecorrencia().getValorMensal())) {
                throw new ConsistirException("O valor da parcela é igual ao da mensalidade, portanto, não adicione na configuração.");
            }

            if (parcela.getValorFinal() > getPlanoVO().getPlanoRecorrencia().getValorMensal()) {
                throw new ConsistirException("O valor da parcela ultrapassa o valor da mensalidade.");
            }

            getPlanoVO()
                    .getPlanoRecorrencia()
                    .getParcelas()
                    .add(parcela);
            montarListaParcelasParaAlteracaoValor();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void removerParcelaValorAlterado() {
        PlanoRecorrenciaParcelaVO parcelaARemover = (PlanoRecorrenciaParcelaVO) context().getExternalContext().getRequestMap().get("parcela");
        Iterator<PlanoRecorrenciaParcelaVO> iterator = getPlanoVO().getPlanoRecorrencia().getParcelas().iterator();
        while (iterator.hasNext()) {
            PlanoRecorrenciaParcelaVO parcela = iterator.next();
            if (parcelaARemover.getNumero() == parcela.getNumero()) {
                iterator.remove();
            }
        }
        montarListaParcelasParaAlteracaoValor();
    }

    public PlanoRecorrenciaParcelaVO getParcelaValorAlterado() {
        if (parcelaValorAlterado == null) {
            return new PlanoRecorrenciaParcelaVO();
        }
        return parcelaValorAlterado;
    }

    public List<SelectItem> getListaParcelasParaAlteracaoValor() {
        return listaParcelasParaAlteracaoValor;
    }

    public void setListaParcelasParaAlteracaoValor(List<SelectItem> listaParcelasParaAlteracaoValor) {
        this.listaParcelasParaAlteracaoValor = listaParcelasParaAlteracaoValor;
    }

    public List<SelectItem> getPlanoTipos() {
        return planoTipos;
    }

    public void setPlanoTipos(List<SelectItem> planoTipos) {
        this.planoTipos = planoTipos;
    }

    public int getQtdeContratosComInicioAposInicioMinimo() {
        return qtdeContratosComInicioAposInicioMinimo;
    }

    public void setQtdeContratosComInicioAposInicioMinimo(int qtdeContratosComInicioAposInicioMinimo) {
        this.qtdeContratosComInicioAposInicioMinimo = qtdeContratosComInicioAposInicioMinimo;
    }

    public void consultarContratosQueIniciamAposAmanha() {
        try {
            this.qtdeContratosComInicioAposInicioMinimo = getFacade()
                    .getContrato()
                    .consultarTotalContratosIniciamApos(Calendario.amanha(),
                            getPlanoVO().getCodigo());
        } catch (Exception e) {
            montarErroComLog(e);
        }
    }

    public void alterarInicioDeContratos() {
        try {
            getFacade().getZWFacade().alterarInicioMinimoDeContratos(
                    getPlanoVO().getInicioMinimoContrato(),
                    Calendario.amanha(),
                    getPlanoVO().getCodigo());
        } catch (Exception e) {
            montarErroComLog(e);
        }
    }

    public Date getInicioMinimoContratoSemAlteracao() {
        return inicioMinimoContratoSemAlteracao;
    }

    public void setInicioMinimoContratoSemAlteracao(Date inicioMinimoContratoSemAlteracao) {
        this.inicioMinimoContratoSemAlteracao = inicioMinimoContratoSemAlteracao;
    }

    public void alterarPlanoPersonal() {
        getPlanoVO().setRegimeRecorrencia(true);
        setMostrarAbaRecorrencia(true);
    }

    public boolean isPermiteAlterarInicioMinimoContrato() {
        return (getPlanoVO().getSite()
                || getConfiguracaoSistema().isDefinirDataInicioPlanosRecorrencia()
                || getPlanoVO().getInicioMinimoContrato() != null)
                && !getPlanoVO().isPlanoPersonal()
                && !getPlanoVO().isVendaCreditoTreino();
    }

    public void selecionouCreditoNaoCumulativo() {
        getPlanoVO().setCreditoSessao(false);
        calcularQtdeCreditoTreinoHorarioTurma();
    }

    public void selecionouCreditoSessao() {
        getPlanoVO().setCreditoTreinoNaoCumulativo(false);
    }

    public String getAbaSelecionada() {
        return abaSelecionada;
    }

    public void setAbaSelecionada(String abaSelecionada) {
        this.abasSelecionadas.add(abaSelecionada);
        this.abaSelecionada = abaSelecionada;
    }

    private void notificarRecursoEmpresaPlano() {
        notificarRecursoEmpresa(RecursoSistema.PLANO);
        if (planoVO.getPlanoRecorrencia().isAnuidadeNaParcela()) {
            if (planoVO.isNovoObj()) {
                notificarRecursoEmpresa(RecursoSistema.CADASTRO_PLANO_ANUIDADE_PARCELA_ESPECIFICA_MARCOU);
            } else if (!planoVO.equals(planoVOClone)) {
                if (planoVO.getPlanoRecorrencia().isAnuidadeNaParcela() != planoVOClone.getPlanoRecorrencia().isAnuidadeNaParcela()) {
                    notificarRecursoEmpresa(RecursoSistema.CADASTRO_PLANO_ANUIDADE_PARCELA_ESPECIFICA_MARCOU);
                }
            }
        }
        if (planoVO.getPlanoRecorrencia().isCancelamentoProporcional()) {
            if (planoVO.isNovoObj()) {
                notificarRecursoEmpresa(RecursoSistema.CANCELAMENTO_PROXIMA_PARCELA_EM_ABERTO_MARCOU);
            } else if (!planoVO.equals(planoVOClone)) {
                if (planoVO.getPlanoRecorrencia().isCancelamentoProporcional() != planoVOClone.getPlanoRecorrencia().isCancelamentoProporcional()) {
                    notificarRecursoEmpresa(RecursoSistema.CANCELAMENTO_PROXIMA_PARCELA_EM_ABERTO_MARCOU);
                }
            }
        }
        if (planoVO.getPlanoRecorrencia().isGerarParcelasValorDiferente()) {
            if (planoVO.isNovoObj()) {
                notificarRecursoEmpresa(RecursoSistema.DEFINIR_PARCELAS_VALOR_DIFERENTE_MARCOU);
            } else if (!planoVO.equals(planoVOClone)) {
                if (planoVO.getPlanoRecorrencia().isGerarParcelasValorDiferente() != planoVOClone.getPlanoRecorrencia().isGerarParcelasValorDiferente()) {
                    notificarRecursoEmpresa(RecursoSistema.DEFINIR_PARCELAS_VALOR_DIFERENTE_MARCOU);
                }
            }
        }
        if (planoVO.isPlanoPersonal()) {
            if (planoVO.isPlanoPersonal() != planoVOClone.isPlanoPersonal()) {
                notificarRecursoEmpresa(RecursoSistema.PLANO_PERSONAL_CADASTRO_PLANO_MARCOU);
            }
        }
        if (planoVO.getInicioMinimoContrato() != null) {
            //não está notificando
            if (planoVO.isNovoObj()) {
                notificarRecursoEmpresa(RecursoSistema.CONFIG_PLANO_CONTRATOS_INICIAM_EM_INFORMADO_SITE_OU_JUST);
            } else if (planoVO.equals(planoVOClone)) {
                if (planoVO.getInicioMinimoContrato() != planoVOClone.getInicioMinimoContrato()) {
                    notificarRecursoEmpresa(RecursoSistema.CONFIG_PLANO_CONTRATOS_INICIAM_EM_INFORMADO_SITE_OU_JUST);
                }
            }
        }
        if (!historico[1] == isCheckTodasVendaEmpresas()) {
            if (isCheckTodasVendaEmpresas()) {
                notificarRecursoEmpresa(RecursoSistema.CADASTRO_PLANO_ABA_EMPRESA_MARCOU);
            }
        }
        if (!historico[2] == planoVO.isPermitirAcessoSomenteNaEmpresaVendeuContrato()) {
            if (planoVO.isPermitirAcessoSomenteNaEmpresaVendeuContrato()) {
                notificarRecursoEmpresa(RecursoSistema.CADASTRO_PLANO_ABA_EMPRESA_MARCOU);
            }
        }
        if (!historico[0] == isCheckTodasAcessoEmpresas()) {
            if (isCheckTodasAcessoEmpresas()) {
                notificarRecursoEmpresa(RecursoSistema.CADASTRO_PLANO_ABA_EMPRESA_MARCOU);
            }
        }
        historico[0] = isCheckTodasAcessoEmpresas();
        historico[1] = isCheckTodasVendaEmpresas();
        historico[2] = planoVO.isPermitirAcessoSomenteNaEmpresaVendeuContrato();
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public void confirmarExcluir() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Plano",
                "Deseja excluir o Plano?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public void marcarAnuidadeNaParcela(ActionEvent actionEvent) {
        if (getPlanoVO().getPlanoRecorrencia().isAnuidadeNaParcela()) {
            getPlanoVO().getPlanoRecorrencia().setNaoCobrarAnuidadeProporcional(true);
        } else {
            getPlanoVO().getPlanoRecorrencia().setParcelarAnuidade(false);
        }
    }

    public void limparDescricaoEncantamentoParaTotem() {
        if (!UteisValidacao.emptyString(getPlanoVO().getDescricaoEncantamento())) {
            getPlanoVO().setDescricaoEncantamento("");
        }
    }

    public void selecionouParcelarAnuidade() {
        setPlanoAnuidadeParcelaVO(new PlanoAnuidadeParcelaVO());
        montarListaParcelasAnuidadeParcela();

        if (getPlanoVO().getPlanoRecorrencia().isParcelarAnuidade()) {
            double valorCalculado = 0.0;
            for (PlanoAnuidadeParcelaVO parcelaVO : getPlanoVO().getPlanoRecorrencia().getParcelasAnuidade()) {
                valorCalculado += parcelaVO.getValor();
            }
            getPlanoVO().getPlanoRecorrencia().setValorAnuidade(valorCalculado);
        }
    }

    public void montarListaParcelasAnuidadeParcela() {
        listaParcelasAnuidadeParcela = new ArrayList<SelectItem>();
        Integer max = planoVO.getPlanoRecorrencia().getDuracaoPlano() == null ? 1 : planoVO.getPlanoRecorrencia().getDuracaoPlano();
        for (Integer i = 1; i <= max; i++) {
            listaParcelasAnuidadeParcela.add(new SelectItem(i, "PARCELA " + i.toString()));
        }
    }

    public void adicionarParcelaAnuidade() {
        try {
            limparMsg();
            if (UteisValidacao.emptyNumber(getPlanoAnuidadeParcelaVO().getValor())) {
                throw new Exception("Informe o valor da parcela.");
            }
            if (getPlanoVO().getPlanoRecorrencia().getParcelasAnuidade().size() == 0) {
                getPlanoVO().getPlanoRecorrencia().setValorAnuidade(0.0);
            }
            for (PlanoAnuidadeParcelaVO planoAnuidadeParcelaVO : getPlanoVO().getPlanoRecorrencia().getParcelasAnuidade()) {
                if (planoAnuidadeParcelaVO.getParcela().equals(getPlanoAnuidadeParcelaVO().getParcela())) {
                    throw new Exception("Parcela " + getPlanoAnuidadeParcelaVO().getParcela() + " já adicionada na lista.");
                }
            }
            getPlanoAnuidadeParcelaVO().setNumero(getPlanoVO().getPlanoRecorrencia().getParcelasAnuidade().size() + 1);

            PlanoAnuidadeParcelaVO parcelaAnuidade = (PlanoAnuidadeParcelaVO) getPlanoAnuidadeParcelaVO().getClone(true);
            getPlanoVO().getPlanoRecorrencia().getParcelasAnuidade().add(parcelaAnuidade);
            getPlanoVO().getPlanoRecorrencia().setValorAnuidade(getPlanoVO().getPlanoRecorrencia().getValorAnuidade() + parcelaAnuidade.getValor());
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void removerParcelaAnuidade() {
        PlanoAnuidadeParcelaVO parcelaARemover = (PlanoAnuidadeParcelaVO) context().getExternalContext().getRequestMap().get("parcela");
        Iterator<PlanoAnuidadeParcelaVO> iterator = getPlanoVO().getPlanoRecorrencia().getParcelasAnuidade().iterator();
        while (iterator.hasNext()) {
            PlanoAnuidadeParcelaVO parcela = iterator.next();
            if (parcelaARemover.getNumero().equals(parcela.getNumero())) {
                iterator.remove();
                getPlanoVO().getPlanoRecorrencia().setValorAnuidade(getPlanoVO().getPlanoRecorrencia().getValorAnuidade() - parcelaARemover.getValor());
            }
        }

        Ordenacao.ordenarLista(getPlanoVO().getPlanoRecorrencia().getParcelasAnuidade(), "numero");
        int i = 0;
        for (PlanoAnuidadeParcelaVO obj : getPlanoVO().getPlanoRecorrencia().getParcelasAnuidade()) {
            obj.setNumero(++i);
        }
    }

    public PlanoAnuidadeParcelaVO getPlanoAnuidadeParcelaVO() {
        if (planoAnuidadeParcelaVO == null) {
            planoAnuidadeParcelaVO = new PlanoAnuidadeParcelaVO();
        }
        return planoAnuidadeParcelaVO;
    }

    public void setPlanoAnuidadeParcelaVO(PlanoAnuidadeParcelaVO planoAnuidadeParcelaVO) {
        this.planoAnuidadeParcelaVO = planoAnuidadeParcelaVO;
    }

    public List<SelectItem> getListaParcelasAnuidadeParcela() {
        if (listaParcelasAnuidadeParcela == null) {
            listaParcelasAnuidadeParcela = new ArrayList<SelectItem>();
        }
        return listaParcelasAnuidadeParcela;
    }

    public void setListaParcelasAnuidadeParcela(List<SelectItem> listaParcelasAnuidadeParcela) {
        this.listaParcelasAnuidadeParcela = listaParcelasAnuidadeParcela;
    }

    public void forcarInformarEmpresasDoPlano() {
        limparMsg();
        if (planoVO.isApresentaVendaRapida()) {
            if (getPlanoVO().getEmpresas().size() > 0) {
                setAbaSelecionada("tabEmpresasPlano");
                montarAviso("Verifique as empresas que participaram desse plano");
            } else {
                getPlanoVO().getEmpresas().get(0).setAcesso(true);
                getPlanoVO().getEmpresas().get(0).setVenda(true);
            }
        }
    }

    public boolean isExibirReplicarRedeEmpresa() {
        boolean integranteFranqueadoraRedeEmpresa = false;
        boolean permiteReplicarRedeEmpresa = false;
        boolean usuarioAdministrador = false;
        try {
            for (UsuarioPerfilAcessoVO userPerfAcess : getControladorTipado(LoginControle.class).getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                if (userPerfAcess.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR")) {
                    usuarioAdministrador = true;
                }
            }
        } catch (Exception e) {
            usuarioAdministrador = false;
        }
        try {
            RedeEmpresaVO redeEmpresaVO = (RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA);
            integranteFranqueadoraRedeEmpresa = redeEmpresaVO != null;
            permiteReplicarRedeEmpresa = getFacade().getConfiguracaoSistema().obterReplicarRedeEmpresa("permitirreplicarferiadoredeempresa");
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean usuarioAdminPacto = false;
        try {
            usuarioAdminPacto = getUsuarioLogado().getUsuarioAdminPACTO();
            if (!usuarioAdminPacto) {
                usuarioAdminPacto = getUsuarioLogado().getUsuarioPACTOBR();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return integranteFranqueadoraRedeEmpresa
                && !planoVO.isNovoObj()
                && permiteReplicarRedeEmpresa
                && (usuarioAdminPacto || usuarioAdministrador);
    }

    public void inicializarPossuiRedeEmpresa() {
        try {
            RedeEmpresaVO rede = ((LoginControle) getControlador("LoginControle")).getRedeEmpresaVO();
            integranteRedeEmpresa = rede != null && !UteisValidacao.emptyNumber(rede.getId());
        } catch (Exception ex) {
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public String getLabelAjustarContratosHabilitandoMetodo() {
        if (getPlanoVO().getPlanoRecorrencia().isCancelamentoProporcionalSomenteRenovacao()) {
            return "Ajustar todos Contratos Renovados ativos";
        }
        return "Ajustar Todos Contratos Ativos";
    }

    public Integer getListaPlanoRedeEmpresaSize() {
        return getListaPlanoRedeEmpresa().size();
    }

    public Integer getListaPlanoRedeEmpresaSincronizado() {
        Integer cont = 0;
        for (PlanoRedeEmpresaVO unid : getListaPlanoRedeEmpresa()) {
            if (unid.getDataAtualizacaoInformada()) {
                cont++;
            }
        }
        return cont;
    }

    public List<PlanoRedeEmpresaVO> getListaPlanoRedeEmpresa() {
        if (listaPlanoRedeEmpresa == null) {
            listaPlanoRedeEmpresa = new ArrayList<>();
        }
        return listaPlanoRedeEmpresa;
    }

    public void setListaPlanoRedeEmpresa(List<PlanoRedeEmpresaVO> listaPlanoRedeEmpresa) {
        this.listaPlanoRedeEmpresa = listaPlanoRedeEmpresa;
    }

    public List getListaSelectItemPlanoCategoria() {
        return listaSelectItemPlanoCategoria;
    }

    public void setListaSelectItemPlanoCategoria(List listaSelectItemPlanoCategoria) {
        this.listaSelectItemPlanoCategoria = listaSelectItemPlanoCategoria;
    }

    public PlanoCategoriaVO getPlanoCategoriaVO() {
        return planoCategoriaVO;
    }

    public void setPlanoCategoriaVO(PlanoCategoriaVO planoCategoriaVO) {
        this.planoCategoriaVO = planoCategoriaVO;
    }

    public boolean isIntegranteRedeEmpresa() {
        return integranteRedeEmpresa;
    }

    public void setIntegranteRedeEmpresa(boolean integranteRedeEmpresa) {
        this.integranteRedeEmpresa = integranteRedeEmpresa;
    }
}
