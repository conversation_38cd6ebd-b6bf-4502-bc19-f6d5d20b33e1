/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.estoque;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.EntidadeRateioEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.estoque.xmlNfe.DetalheProduto;
import controle.estoque.xmlNfe.NfeProc;
import controle.plano.ProdutoControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.estoque.CompraItensVO;
import negocio.comuns.estoque.CompraVO;
import negocio.comuns.estoque.ContaFinanceiroCompraVO;
import negocio.comuns.estoque.DocumentoCompraVO;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ImpostoProdutoCfopVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.*;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.codehaus.groovy.runtime.InvokerHelper.asList;

import negocio.interfaces.arquitetura.LogInterfaceFacade;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

/**
 *
 * <AUTHOR>
 */
public class CompraControle extends SuperControle {

    private CompraVO compraVO;
    protected List listaSelectItemEmpresa;
    protected List listaSelectItemFornedor;
    protected List<SelectItem> listaSelectItemProduto = new ArrayList<>();
    protected List<SelectItem> listaSelectItemCategoriaProduto = new ArrayList<>();
    private List<SelectItem> listaConfigEmissaoNFSe = new ArrayList<>();
    private List<SelectItem> listaConfigEmissaoNFCe = new ArrayList<>();
    protected List<ProdutoVO> produtos = new ArrayList<>();
    protected List<CategoriaProdutoVO> categorias = new ArrayList<>();
    private CompraItensVO compraItensVO = new CompraItensVO();
    // Atributos para pesquisa.
    private Date periodoDe = Calendario.hoje();
    private Date periodoAte = Calendario.hoje();
    private String numeroNF = "";
    private Integer codigoFornecedor;
    private Integer codigoEmpresa;
    // Atributos para o suggestion Box de Produto.
    private String produtoSelecionado = "";
    private Date dataCompraValida;
    private Date dataCompraCalendario;
    private Date dataVencimentoContaCompra;
    private Date dataCompraOriginal;
    private File documento;
    private HashMap<File, String> documentos;
    private boolean existeDocumento;
    List<DetalheProduto> produtosNaoEncontradosNaNfe = new ArrayList<>();
    Integer categoriaParaNovosProdutosNfe = null;
    Integer estoqueMinimoParaNovosProdutosNfe = null;
    List<ProdutoVO> produtosParaSalvar = new ArrayList<>();
    List<ProdutoVO> produtosParaAlterar = new ArrayList<>();
    List<ProdutoEstoqueVO> produtoEstoquesParaCriar = new ArrayList<>();
    ProdutoVO produtoComConfigsGenericas = new ProdutoVO();
    private Double valorParcelaContaFinanceiro;
    private Integer nrParcelaContaFinanceiro;
    private String codigoBarrasParcelaContaFinanceiro;

    public CompraControle() throws Exception {
        inicializarDados();
    }

    private void inicializarDados() throws Exception {
        obterUsuarioLogado();
        montarListaSelectItemEmpresa();
        montarListaSelectItemFornecedor();
        montarListaSelectItemCategoriaProduto();
        montarListaSelectItemProduto();
        setControleConsulta(new ControleConsulta());
        produtosNaoEncontradosNaNfe.clear();
        produtoEstoquesParaCriar.clear();
        produtosParaSalvar.clear();
        produtosParaAlterar.clear();
    }

    public void clonar() {
        this.compraVO.setCodigo(new Integer(0));
        this.compraVO.setCancelada(false);
        this.compraVO.setNovoObj(true);
        setMensagemID("msg_entre_dados");
        setSucesso(true);
        setErro(false);
    }

    public void cancelarCompra() {
        try {
            validarPermissaoCancelarCompra();
            this.compraVO.setCancelada(true);
            this.compraVO.setUsuarioCancelamento(getUsuarioLogado());
            getFacade().getCompra().cancelar(compraVO);
            registrarLogCancelarCompra();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void montarComboConfigEmissao() {
        try {
            listaConfigEmissaoNFSe = new ArrayList<SelectItem>();
            listaConfigEmissaoNFCe = new ArrayList<SelectItem>();

            listaConfigEmissaoNFSe.add(new SelectItem(0, ""));
            listaConfigEmissaoNFCe.add(new SelectItem(0, ""));

            List<ConfiguracaoNotaFiscalVO> listaTodos = getFacade().getConfiguracaoNotaFiscal().consultarConfiguracaoNotaFiscal(getEmpresaLogado().getCodigo(), null, true, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (ConfiguracaoNotaFiscalVO confg : listaTodos) {
                if (confg.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFSE) || confg.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE)) {
                    listaConfigEmissaoNFSe.add(new SelectItem(confg.getCodigo(), confg.getDescricao()));
                } else if (confg.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE)) {
                    listaConfigEmissaoNFCe.add(new SelectItem(confg.getCodigo(), confg.getDescricao()));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, ProdutoControle.class);
            listaConfigEmissaoNFSe = new ArrayList<SelectItem>();
            listaConfigEmissaoNFCe = new ArrayList<SelectItem>();
        }
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        List listaEmpresa = new ArrayList();
        listaEmpresa.add(new SelectItem(new Integer(0), ""));
        List listaConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
        for (Object obj : listaConsulta) {
            EmpresaVO empresa = (EmpresaVO) obj;
            listaEmpresa.add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
        setListaSelectItemEmpresa(listaEmpresa);
    }

    public void montarListaSelectItemFornecedor() throws Exception {
        List listaFornecedor = new ArrayList();
        listaFornecedor.add(new SelectItem(new Integer(0), ""));
        List listaConsulta = getFacade().getFornecedor().consultarPorDescricao("");
        for (Object obj : listaConsulta) {
            FornecedorVO fornecedor = (FornecedorVO) obj;
            listaFornecedor.add(new SelectItem(fornecedor.getCodigo(), fornecedor.getPessoa().getNome()));
        }
        setListaSelectItemFornedor(listaFornecedor);
    }

    public void montarListaSelectItemFornecedorComUmSelecionado(Integer fornecedorSelecionado) throws Exception {
        List<SelectItem> listaFornecedor = new ArrayList<>();
        listaFornecedor.add(new SelectItem(0, ""));
        List<FornecedorVO> listaConsulta = getFacade().getFornecedor().consultarPorDescricao("");
        for (FornecedorVO fornecedor : listaConsulta) {
            listaFornecedor.add(new SelectItem(fornecedor.getCodigo(), fornecedor.getPessoa().getNome()));
        }
        setListaSelectItemFornedor(listaFornecedor);

        // Garantir que o fornecedor correto está selecionado na compraVO
        if (fornecedorSelecionado != null) {
            compraVO.setFornecedor(getFacade().getFornecedor().obter(fornecedorSelecionado));
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>CompraItensVO</code>
     * para o objeto <code>CompraVO</code> da classe <code>Compra</code>
     */
    public void adicionarItensCompra() throws Exception {
        try {
            if(this.compraItensVO.getDataUltimaOperacao() == null){
                throw new ConsistirException("Produto não adicionado ao Controle de Estoque");
            }
            if(this.compraVO.getDataCadastro() == null){
                throw new ConsistirException("Informe corretamente a data da compra");
            }

            if(Calendario.menorComHora(this.compraVO.getDataCadastro(), this.compraItensVO.getDataUltimaOperacao())){
                throw new ConsistirException("Produto foi adicionado ao Controle ou teve balanço no dia "+Uteis.getDataComHHMM(compraItensVO.getDataUltimaOperacao())+". Não pode ser ser lançado em compras com data anterior a essa.");
            }
            if(this.compraItensVO.getDataSaidaControle() != null && Calendario.maiorComHora(this.compraVO.getDataCadastro() , this.compraItensVO.getDataSaidaControle())){
                throw new ConsistirException("Produto deixou de ter o estoque controlado no dia "+Uteis.getDataComHHMM(compraItensVO.getDataUltimaOperacao())+", portanto não pode ser adicionado a essa compra");
            }
            if(isOrdemCompraEstoque() && compraVO.getAutorizada() == null){
                this.compraItensVO.setQuantidadeAutorizar(this.compraItensVO.getQuantidade());
            }
            this.compraItensVO.setCompra(compraVO);
            CompraItensVO.validarDadosTelaInclusao(this.compraItensVO);
            this.compraItensVO.setPontos(compraItensVO.getQuantidade() * compraItensVO.getProduto().getQtdePontos());
            this.compraVO.getItens().remove(compraItensVO);
            this.compraVO.getItens().add(compraItensVO);

            this.compraItensVO = new CompraItensVO();
            setSucesso(true);
            setErro(false);
            this.produtoSelecionado = "";
            setMensagemID("msg_dados_adicionados");

            adicionarAutomaticamenteParcelaContaFinanceiroCompra();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarAutomaticamenteParcelaContaFinanceiroCompra() {
        try {
            if (getCompraVO().getNrParcelasContaPagar() > 1) {
                getCompraVO().setCompraContasLancarFinanceiro(new ArrayList<>());
                int parcela = getCompraVO().getNrParcelasContaPagar();
                while (parcela > 0) {
                    ContaFinanceiroCompraVO contaFinanceiroCompra = new ContaFinanceiroCompraVO();
                    contaFinanceiroCompra.setNrParcela(parcela);
                    contaFinanceiroCompra.setDataVencimento(parcela == 1 ? Calendario.hoje() : Calendario.somarMeses(Calendario.hoje(), parcela-1));
                    contaFinanceiroCompra.setValorParcela(getCompraVO().getValorTotal() / getCompraVO().getNrParcelasContaPagar() );
                    getCompraVO().getCompraContasLancarFinanceiro().add(contaFinanceiroCompra);
                    parcela--;
                }
                Ordenacao.ordenarLista(getCompraVO().getCompraContasLancarFinanceiro(), "nrParcela");
            }
        }catch (Exception ignore){}
    }

    private void validarParametrosPesquisa() throws Exception {
        if (this.periodoDe != null) {
            if (this.periodoAte == null) {
                throw new ConsistirException("Informe a data final");
            }
        }
        if (this.periodoAte != null) {
            if (this.periodoDe == null) {
                throw new ConsistirException("Informe a data inicial");
            }
        }
        if ((periodoDe == null) && (periodoAte == null)
                && ((codigoFornecedor == null) || (codigoFornecedor.intValue() <= 0))
                && (numeroNF.equals(""))) {
            throw new ConsistirException("É necessário informar ao menos um parâmetro para pesquisa.");
        }

    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP CompraCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            validarParametrosPesquisa();

            if (!getUsuarioLogado().getAdministrador()) {
                this.codigoEmpresa = getEmpresaLogado().getCodigo();
            }
            List<CompraVO> listaCompra = getFacade().getCompra().consultar(this.codigoEmpresa, periodoDe, periodoAte, codigoFornecedor, numeroNF, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            objs.addAll(listaCompra);

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String abrirTelaCompra() {
        try {
            if (request() != null && request().getSession() != null && !UteisValidacao.emptyString(request().getParameter("solicitacaoCompra"))) {
                request().getSession().setAttribute("solicitacaoCompra", request().getParameter("solicitacaoCompra"));
            } else if (request() != null && request().getSession() != null) {
                request().getSession().removeAttribute("solicitacaoCompra");
            }
            notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.COMPRA.toString()));
            validarPermissaoCadastrarCompra();
            montarListaSelectItemEmpresa();
            montarListaSelectItemFornecedor();
            montarListaSelectItemCategoriaProduto();
            montarListaSelectItemProduto();
            produtoEstoquesParaCriar.clear();
            produtosParaSalvar.clear();
            produtosParaAlterar.clear();
            produtosNaoEncontradosNaNfe.clear();
            setMsgAlert("abrirPopup('compraCons.jsp', 'Compra', 1000, 650);");
            return abrirTelaConsulta();
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return "";
        }
    }

    public String abrirTelaConsulta() {
        this.produtoSelecionado = "";
        setListaConsulta(null);
        limparMsg();
        return "consultar";
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultar";
    }

    public void removerItensCompra() throws Exception {
        try {
            CompraItensVO compraItensVO = (CompraItensVO) context().getExternalContext().getRequestMap().get("compraItem");
            this.compraVO.getItens().remove(compraItensVO);
            setMensagemID("msg_dados_excluidos");
            setMensagem("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));

        return itens;
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>CompraVO</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        try {
            limparCampos();
            validarPermissaoCadastrarCompra();
            this.compraVO = new CompraVO();
            if (request() != null && request().getSession() != null &&
                    request().getSession().getAttribute("solicitacaoCompra") != null &&
                    !UteisValidacao.emptyString(request().getSession().getAttribute("solicitacaoCompra").toString())) {
                this.compraVO.setSolicitacaoCompra(Integer.parseInt(request().getSession().getAttribute("solicitacaoCompra").toString()));
            }
            this.compraVO.setUsuarioCadastro(getUsuarioLogado());
            this.compraVO.setEmpresa(getEmpresaLogado());
            this.compraVO.setDataCadastro(getDataCalendarioComHora());
            this.setDataCompraValida(compraVO.getDataCadastro());
            this.compraVO.registrarObjetoVOAntesDaAlteracao();
            produtosNaoEncontradosNaNfe.clear();
            produtosParaAlterar.clear();
            produtosParaSalvar.clear();
            produtoEstoquesParaCriar.clear();
            montarListaSelectItemEmpresa();
            montarListaSelectItemFornecedor();
            montarListaSelectItemCategoriaProduto();
            montarListaSelectItemProduto();
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>CompraVO</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        return gravarAutorizando(false);
    }

    public String autorizar() {
        try {
            validarPermissaoAutorizarNegarCompra();
            compraVO.setAutorizada(true);
            return gravarAutorizando(true);
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String negar() {
        try {
            validarPermissaoAutorizarNegarCompra();
            compraVO.setAutorizada(false);
            getFacade().getCompra().alterar(compraVO, !Calendario.igualComHora(compraVO.getDataCadastro(), dataCompraOriginal), false);
            cancelarCompra();
            return "consultar";
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String gravarAutorizando(boolean autorizar) {
        try {
            if (getProcessandoOperacao() != null && getProcessandoOperacao()){
                throw new Exception("Operação em processamento, favor aguardar.");
            }
            setProcessandoOperacao(true);
            validarDataCompra();
            validarCamposObrigatoriosContaPagar(compraVO);
            int pontos = 0;
            for (CompraItensVO compraItens : compraVO.getItens()){
                pontos += compraItens.getPontos();
            }
            compraVO.setPontos(pontos);

            if (isOrdemCompraEstoque()) {
                if (!autorizar){
                    compraVO.getItens().forEach(item -> {
                        if (!UteisValidacao.emptyNumber(item.getQuantidade())) {
                            item.setQuantidadeAutorizar(item.getQuantidade());
                            item.setQuantidade(0);
                        }
                    });
                } else if (autorizar) {
                    compraVO.getItens().forEach(item -> {
                        if (!UteisValidacao.emptyNumber(item.getQuantidadeAutorizar())) {
                            item.setQuantidade(item.getQuantidadeAutorizar());
                            item.setQuantidadeAutorizar(0);
                        }
                    });
                }
            }

            if (compraVO.isNovoObj().booleanValue()) {
                validarDadosPersonaizacaoParcelasContaFinanceiro();

                if (!produtosParaSalvar.isEmpty()) {
                    for (ProdutoVO produtoVO : produtosParaSalvar) {
                        getFacade().getProduto().incluir(produtoVO);

                        try {
                            produtoVO.setObjetoVOAntesAlteracao(new ProdutoVO());
                            produtoVO.setNovoObj(true);
                            registrarLogObjetoVO(produtoVO, produtoVO.getCodigo(), "PRODUTO", 0);
                        } catch (Exception e) {
                            registrarLogErroObjetoVO("PRODUTO", produtoVO.getCodigo(),
                                    "ERRO AO GERAR LOG DE INCLUSÃO DE PRODUTO",
                                    this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                            e.printStackTrace();
                        }

                        produtoVO.setNovoObj(false);
                        produtoVO.registrarObjetoVOAntesDaAlteracao();
                    }

                    produtosParaSalvar.clear();
                }

                if (!produtoEstoquesParaCriar.isEmpty()) {
                    getFacade().getProdutoEstoque().incluir(produtoEstoquesParaCriar, getUsuario());
                    produtoEstoquesParaCriar.clear();
                }

                if (!produtosParaAlterar.isEmpty()){
                    for (ProdutoVO produtoVO : produtosParaAlterar){

                        Optional<ImpostoProdutoCfopVO> impostoProdutoCfopVO = getFacade().getImpostoProdutoCfop().consultarPorCfopENcm(produtoVO.getCfop(), produtoVO.getNcm());

                        impostoProdutoCfopVO.ifPresent(produtoCfopVO -> settarCamposConfigDeImpostos(produtoVO, produtoCfopVO));

                        getFacade().getProduto().alterar(produtoVO);
                    }

                    produtosParaAlterar.clear();
                }

                getFacade().getCompra().incluir(compraVO);

                if(!isOrdemCompraEstoque() && isContaPagarCompraEstoque()) {
                        try {
                            povoarMovContaCompraEstoque(compraVO, null);
                        } catch (Exception ex) {
                            getFacade().getCompra().cancelar(compraVO);
                            throw ex;
                        }
                }

               registrarLog();
            } else {
                getFacade().getCompra().alterar(compraVO,!Calendario.igualComHora(compraVO.getDataCadastro(), dataCompraOriginal), autorizar);

                if(isContaPagarCompraEstoque()) {

                    if (!isOrdemCompraEstoque() || autorizar) {
                        try {
                            List<MovContaVO> movConta = alterarMovContaCompraEstoque(compraVO);
                            if (movConta != null && !movConta.isEmpty()) {
                                movConta.forEach(m -> {
                                    try {
                                        if (m.getCodigo() != null && m.getCodigo() != 0) {
                                            getFacade().getMovConta().alterar(m, 0, false);
                                        }
                                    } catch (Exception e) {
                                        throw new RuntimeException(e);
                                    }
                                });
                            } else if (isOrdemCompraEstoque() && autorizar && movConta != null && movConta.isEmpty()) {
                                povoarMovContaCompraEstoque(compraVO, autorizar);
                            }
                        } catch (Exception ex) {
                            getFacade().getCompra().cancelar(compraVO);
                            throw ex;
                        }
                    }
                }

                try {
                    registrarLogObjetoVO(compraVO, compraVO.getCodigo(), "COMPRA", 0);

                } catch (Exception e) {
                    registrarLogErroObjetoVO("COMPRA", compraVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE COMPRA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
            }
            anexarArquivo();
            setProcessandoOperacao(false);
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if (autorizar) {
                compraVO.setAutorizada(null);
            }
            setProcessandoOperacao(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public List<MovContaVO> getContasDestaCompra() {
        try {
            return getFacade().getMovConta().consultarMovContaCompraEstoque(compraVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    private void validarDadosPersonaizacaoParcelasContaFinanceiro() throws Exception {
        if (isContaPagarCompraEstoque() && getCompraVO().getCompraContasLancarFinanceiro() != null && (getCompraVO().getNrParcelasContaPagar() > getCompraVO().getCompraContasLancarFinanceiro().size())) {
            throw new ConsistirException("Número de parcelas à gerar é diferente da quantidade de parcelas personalizadas na aba financeiro.");
        }

        if (isContaPagarCompraEstoque() && getCompraVO().getCompraContasLancarFinanceiro() != null && !getCompraVO().getCompraContasLancarFinanceiro().isEmpty()) {
            double valorTotalCompra = 0;
            for (CompraItensVO item : getCompraVO().getItens()) {
                valorTotalCompra += item.getTotal();
            }

            double valorTotalParcelasPersonalizadas = 0;
            for (ContaFinanceiroCompraVO conta : getCompraVO().getCompraContasLancarFinanceiro()) {
                valorTotalParcelasPersonalizadas += conta.getValorParcela();
            }

            if (Uteis.arredondarForcando2CasasDecimais(valorTotalCompra) != Uteis.arredondarForcando2CasasDecimais(valorTotalParcelasPersonalizadas)) {
                throw new ConsistirException("O valor total da compra é diferente do valor total das parcelas personalizadas na aba financeiro. Diferença de R$ "+
                        (Uteis.arredondarForcando2CasasDecimais(valorTotalParcelasPersonalizadas - valorTotalCompra)));
            }

        }
    }

    private void validarCamposObrigatoriosContaPagar(CompraVO compra) throws Exception {
        if (isContaPagarCompraEstoque()) {
            if (compra.getFornecedor() != null && compra.getFornecedor().getCodigo() == 0) {
                throw new Exception("Obrigatório informar campo: Fornecedor");
            } else if (StringUtils.isBlank(compra.getDescricaoFinanceiro())) {
                throw new Exception("Obrigatório informar campo: Descrição da Conta a Pagar");
            } else if (compra.getDataCadastro() == null) {
                throw new Exception("Obrigatório informar campo: Data Compra");
            } else if (compra.getItens() != null && compra.getItens().isEmpty()) {
                throw new Exception("Obrigatório informar campo: Produtos da Compra");
            } else if (compra.getValorTotal() == 0.0) {
                throw new Exception("Obrigatório informar campo: Valor Total");
            }
        }
    }

    public void validarPermissaoAutorizarNegarCompra() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "AutorizarNegarCompra", "12.09 - Autorizar/Negar Compra");
            }
        }
    }

    public void prepararTelaProdutosNaoEncontrados() throws Exception {
        montarListaSelectItemProduto();
        montarListaSelectItemCategoriaProduto();
        montarListaSelectItemFornecedor();
    }

    public void uploadNfe(UploadEvent upload) {
        try {
            setMsgAlert("");
            limparMsg();
            uploadXmlNfe(upload);

            if (!UteisValidacao.emptyList(this.produtosNaoEncontradosNaNfe)) {
                montarListaSelectItemProduto();
                montarListaSelectItemCategoriaProduto();
                montarListaSelectItemFornecedor();
                setMsgAlert("Richfaces.showModalPanel('compraFormNfeProdutosNaoEncontradosModal');");
            } else {
                montarInfo("Arquivo XML processado com sucesso, verifique as informações e confirme a operação.");
                montarSucesso("msg_xml_processado_sucesso");
            }
//            setMsgAlert("document.getElementById('form:dadosCompra_lbl').click();" + this.getMensagemNotificar());
//            setMsgAlert(this.getMensagemNotificar());
        } catch (Exception e) {
            e.printStackTrace();
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.erroProcessamento", "Erro ao processar o arquivo XML.");
            montarErro(e);
            setMsgAlert(this.getMensagemNotificar());
        }
    }

    public boolean isExibirConfiguracoesNFCe() {
        try {
            if (getUsuarioLogado().getUsuarioPactoSolucoes()) {
                return true;
            }
            return getEmpresaLogado().isUsarNFCe();
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isExibirConfiguracoesNFSe() {
        try {
            if (getUsuarioLogado().getUsuarioPactoSolucoes()) {
                return true;
            }
            return getEmpresaLogado().getUsarNFSe();
        } catch (Exception e) {
            return false;
        }
    }


    private void fornecedorDoXmlDaNfe(NfeProc nfeProc) throws Exception {
        String cnpjFornecedor = Uteis.removerMascara(nfeProc.getNfe().getInfNFe().getEmitente().getCNPJ());
        String nomeFornecedor = nfeProc.getNfe().getInfNFe().getEmitente().getNome();

        FornecedorVO fornecedorExistente = getFacade().getFornecedor().consultarPorCNPJ(cnpjFornecedor);

        if (fornecedorExistente != null) {
            compraVO.setFornecedor(fornecedorExistente);
            setCodigoFornecedor(fornecedorExistente.getCodigo());
            montarListaSelectItemFornecedorComUmSelecionado(fornecedorExistente.getCodigo());
        } else {
            PessoaVO novaPessoaFornecedor = new PessoaVO();
            novaPessoaFornecedor.setNome(nomeFornecedor);

            getFacade().getPessoa().incluirSemComit(novaPessoaFornecedor);

            Integer codigoPessoa = novaPessoaFornecedor.getCodigo();
            if (codigoPessoa == null) {
                throw new Exception("Erro ao inserir Pessoa. Código não gerado.");
            }

            FornecedorVO novoFornecedor = new FornecedorVO();
            novoFornecedor.setCnpj(cnpjFornecedor);
            novoFornecedor.setPessoa(novaPessoaFornecedor);
            novoFornecedor.setEmpresaVO(getEmpresaLogado());
            novoFornecedor.setNomeFantasia(nomeFornecedor);
            novoFornecedor.setContato(nomeFornecedor);

            getFacade().getFornecedor().incluir(novoFornecedor);

            // Associar fornecedor à compra
            compraVO.setFornecedor(novoFornecedor);
            setCodigoFornecedor(novoFornecedor.getCodigo());
            montarListaSelectItemFornecedorComUmSelecionado(novoFornecedor.getCodigo());
        }
    }

    private void produtosXmlNfe(NfeProc nfeProc) throws Exception {
        nfeProc.getNfe().getInfNFe().getDetalhesProduto().forEach(detalheProduto -> {
            try {

                detalheProduto.getProduto().setDescricao(detalheProduto.getProduto().getDescricao().replace("\"", ""));

                Optional<ProdutoVO> produto = getFacade().getProduto()
                        .consultarProdutosComControleEstoquePorCodigoDeBarrasOuDescricao(detalheProduto.getProduto().getCodigoDeBarras(), detalheProduto.getProduto().getDescricao(), Uteis.NIVELMONTARDADOS_TODOS);

                detalheProduto.setValorFinal(0.00);

                Optional<ImpostoProdutoCfopVO> impostoProdutoCfopVO = getFacade().getImpostoProdutoCfop().consultarPorCfopENcm(detalheProduto.getProduto().getCfop(), detalheProduto.getProduto().getNcm());

                if(produto.isPresent()) {
                    ProdutoVO produtoVO = produto.get();

                    //VEM DO XML DA NFE (SETTADOS AUTOMATICAMENTE)
                    setIfBlank(produtoVO::getCfop, produtoVO::setCfop, detalheProduto.getProduto().getCfop());
                    setIfBlank(produtoVO::getNcm, produtoVO::setNcm, detalheProduto.getProduto().getNcm());
                    setIfBlank(produtoVO::getCest, produtoVO::setCest, detalheProduto.getProduto().getCest());
                    setIfBlank(produtoVO::getNcmNFCe, produtoVO::setNcmNFCe, detalheProduto.getProduto().getNcm());
                    setIfBlank(produtoVO::getCodigoBarras, produtoVO::setCodigoBarras, detalheProduto.getProduto().getCodigoDeBarras());

                    //CONFIGURAÇÃO NFS-e
                    impostoProdutoCfopVO.ifPresent(produtoCfopVO -> settarCamposConfigDeImpostos(produtoVO, produtoCfopVO));

                    adicionarProdutoNaCompra(produtoVO, Double.valueOf(detalheProduto.getProduto().getQuantidade()).intValue(), Double.valueOf(detalheProduto.getProduto().getValorUnitario()), detalheProduto.getProduto().getDesconto());
                    produtosParaAlterar.add(produtoVO);
                } else {
                    produtosNaoEncontradosNaNfe.add(detalheProduto);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    private void setIfZeroOrNull(Supplier<Double> getter, Consumer<Double> setter, Double newValue) {
        if (getter.get() == null || getter.get() == 0.0) {
            setter.accept(newValue);
        }
    }

    public static void setIfBlank(Supplier<String> getter, Consumer<String> setter, String newValue) {
        if (StringUtils.isBlank(getter.get())) {
            setter.accept(newValue);
        }
    }

    public static <T> void setIfNull(Supplier<T> getter, Consumer<T> setter, T newValue) {
        if (getter.get() == null) {
            setter.accept(newValue);
        }
    }

    public List consultarCategoriaProdutoPorDescricao(String descricaoPrm) throws Exception {
        List lista = getFacade().getCategoriaProduto().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return lista;
    }

    public void montarListaSelectItemCategoriaProduto() throws Exception {
        listaSelectItemCategoriaProduto.clear();
        categorias = consultarCategoriaProdutoPorDescricao("");
        listaSelectItemCategoriaProduto.add(new SelectItem(0, ""));

        for (CategoriaProdutoVO obj : categorias) {
            listaSelectItemCategoriaProduto.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
    }

    public void montarListaSelectItemProduto() throws Exception {
        this.listaSelectItemProduto = new ArrayList<>();
        this.produtos = getFacade().getProduto().consultarPorCodigoTipoProduto(0, "PE", true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ProdutoVO obj : produtos) {
            if (!UteisValidacao.emptyString(obj.getCodigoBarras())) {
                //somente produto que não tem código de barras cadastrado
                continue;
            }
            listaSelectItemProduto.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        Ordenacao.ordenarLista(this.listaSelectItemProduto, "label");
        this.listaSelectItemProduto.add(0, new SelectItem(0, ""));
    }


    public void salvarProdutosNaoEncontradosNaNfe() {
            try {
                limparMsg();

                List<ProdutoVO> produtosSelecionados = new ArrayList<>();
                Set<Integer> produtosSelecionadosCodigos = new HashSet<>(); // Set para verificar duplicidade

                Optional<CategoriaProdutoVO> categoriaSelecionada = categorias.stream()
                        .filter(it -> it.getCodigo().equals(categoriaParaNovosProdutosNfe))
                        .findFirst();

                for (DetalheProduto produto : produtosNaoEncontradosNaNfe) {
                    if (!produto.getCadastrarNovo() && produto.getProdutoParaVincularNoEstoque() == 0) {
                        throw new Exception("O produto '" + produto.getProduto().getDescricao() + "' precisa ser cadastrado ou vinculado a um existente.");
                    }

                    if (produto.getProdutoParaVincularNoEstoque() != 0) {
                        if (!produtos.stream().anyMatch(it -> it.getCodigo().equals(produto.getProdutoParaVincularNoEstoque()))) {
                            throw new Exception("Produto selecionado para vínculo não foi encontrado no estoque.");
                        }

                        // Verifica duplicidade antes de qualquer ação
                        if (!produtosSelecionadosCodigos.add(produto.getProdutoParaVincularNoEstoque())) {
                            throw new Exception("Você não pode vincular o mesmo produto mais de uma vez!");
                        }
                    }
                }

                // Segunda etapa: Executar as ações de salvar e vincular os produtos
                for (DetalheProduto produto : produtosNaoEncontradosNaNfe) {

                    Optional<ImpostoProdutoCfopVO> impostoProdutoCfopVO = getFacade().getImpostoProdutoCfop().consultarPorCfopENcm(produto.getProduto().getCfop(), produto.getProduto().getNcm());

                    if (produto.getCadastrarNovo() && produto.getProdutoParaVincularNoEstoque() == 0) {
                        if (!categoriaSelecionada.isPresent()) {
                            throw new Exception("Você precisa selecionar uma categoria para os novos produtos");
                        }
                        try {
                            ProdutoVO produtoVO = new ProdutoVO();
                            ProdutoEstoqueVO produtoEstoqueVO = new ProdutoEstoqueVO();

                            produtoVO.setTipoProduto(TipoProduto.PRODUTO_ESTOQUE.getCodigo());
                            produtoVO.setDescricao(produto.getProduto().getDescricao());
                            produtoVO.setCodigoBarras(produto.getProduto().getCodigoDeBarras());
                            produtoVO.setCategoriaProduto(categoriaSelecionada.get());
                            produtoVO.setValorFinal(produto.getValorFinal());
                            produtoVO.setCest(produto.getProduto().getCest());
                            produtoVO.setCfop(produto.getProduto().getCfop());
                            produtoVO.setNcmNFCe(produto.getProduto().getNcm());
                            produtoVO.setNcm(produto.getProduto().getNcm());
                            produtoVO.setObservacao("Produto cadastrado automaticamente via importação de XML");

                            //CONFIGURAÇÃO NFS-e
                            impostoProdutoCfopVO.ifPresent(produtoCfopVO -> settarCamposConfigDeImpostos(produtoVO, produtoCfopVO));

                            ProdutoVO.validarDados(produtoVO);
                            produtosParaSalvar.add(produtoVO);

                            produtoEstoqueVO.setProduto(produtoVO);
                            produtoEstoqueVO.setEstoqueMinimo(estoqueMinimoParaNovosProdutosNfe);
                            produtoEstoqueVO.setSituacao("A");
                            produtoEstoqueVO.setEmpresa(getEmpresaLogado());

                            produtoEstoquesParaCriar.add(produtoEstoqueVO);

                            adicionarProdutoNaCompra(produtoVO,
                                    Double.valueOf(produto.getProduto().getQuantidade()).intValue(),
                                    Double.valueOf(produto.getProduto().getValorUnitario()), produto.getProduto().getDesconto());

                        } catch (Exception e) {
                            produtoEstoquesParaCriar.clear();
                            produtosParaSalvar.clear();
                            throw new RuntimeException(e);
                        }
                    } else {
                        ProdutoVO produtoSelecionado = produtos.stream()
                                .filter(it -> it.getCodigo().equals(produto.getProdutoParaVincularNoEstoque()))
                                .findFirst()
                                .orElseThrow(() -> new RuntimeException("Produto não encontrado"));

                        produtoSelecionado.setCest(produto.getProduto().getCest());
                        produtoSelecionado.setCfop(produto.getProduto().getCfop());
                        produtoSelecionado.setNcmNFCe(produto.getProduto().getNcm());
                        produtoSelecionado.setNcm(produto.getProduto().getNcm());
                        impostoProdutoCfopVO.ifPresent(produtoCfopVO -> settarCamposConfigDeImpostos(produtoSelecionado, produtoCfopVO));
                        produtosParaAlterar.add(produtoSelecionado);

                        ProdutoEstoqueVO produtoEstoque = getFacade().getProdutoEstoque().consultarPorProduto(produtoSelecionado.getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                        if (produtoEstoque == null){
                            ProdutoEstoqueVO produtoEstoqueVO = new ProdutoEstoqueVO();
                            produtoEstoqueVO.setProduto(produtoSelecionado);
                            produtoEstoqueVO.setEstoqueMinimo(estoqueMinimoParaNovosProdutosNfe);
                            produtoEstoqueVO.setSituacao("A");
                            produtoEstoqueVO.setEmpresa(getEmpresaLogado());

                            produtoEstoquesParaCriar.add(produtoEstoqueVO);
                        }

                        // Adiciona o produto à lista de selecionados para vincular
                        produtosSelecionados.add(produtoSelecionado);
                        produtoSelecionado.setCodigoBarras(produto.getProduto().getCodigoDeBarras());

                        try {
                            getFacade().getProduto().alterar(produtoSelecionado);
                        } catch (Exception e) {
                            throw new Exception(e);
                        }

                        adicionarProdutoNaCompra(produtoSelecionado,
                                Double.valueOf(produto.getProduto().getQuantidade()).intValue(),
                                Double.valueOf(produto.getProduto().getValorUnitario()), produto.getProduto().getDesconto());
                    }
                }

                if (!produtoEstoquesParaCriar.isEmpty()) {
                    if(estoqueMinimoParaNovosProdutosNfe == null){
                        throw new Exception("É necessário definir o estoque mínimo para os produtos que estão sendo criados e para aqueles que serão vinculados, mas ainda não possuem um estoque configurado.");
                    }
                }

                produtosNaoEncontradosNaNfe.clear();
                montarInfo("Produtos processados com sucesso. Verifique as informações e confirme a operação.");

                montarSucesso("msg_produtos_processados_sucesso");

            } catch (Exception e) {
                e.printStackTrace();
                setSucesso(false);
                setErro(true);
                setMensagemDetalhada("operacoes.arquivo.upload.erroProcessamento", "Erro ao processar os produtos da NFe.");
                montarErro(e);
                setMsgAlert(this.getMensagemNotificar());
            }
        }

    private void settarCamposDoProdutoComConfigsGenericas(ProdutoVO produtoVO) {
        setIfNull(produtoVO::getConfiguracaoNotaFiscalNFSeNullable, produtoVO::setConfiguracaoNotaFiscalNFSe, produtoComConfigsGenericas.getConfiguracaoNotaFiscalNFSe());
        setIfNull(produtoVO::getConfiguracaoNotaFiscalNFCeNullable, produtoVO::setConfiguracaoNotaFiscalNFCe, produtoComConfigsGenericas.getConfiguracaoNotaFiscalNFCe());

        // NOTA FISCAL
        setIfBlank(produtoVO::getCodigoListaServico, produtoVO::setCodigoListaServico, produtoComConfigsGenericas.getCodigoListaServico());
        setIfBlank(produtoVO::getCodigoTributacaoMunicipio, produtoVO::setCodigoTributacaoMunicipio, produtoComConfigsGenericas.getCodigoTributacaoMunicipio());
        setIfBlank(produtoVO::getDescricaoServicoMunicipio, produtoVO::setDescricaoServicoMunicipio, produtoComConfigsGenericas.getDescricaoServicoMunicipio());
        produtoVO.setEnviarPercentualImposto(produtoComConfigsGenericas.isEnviarPercentualImposto());
        setIfZeroOrNull(produtoVO::getPercentualFederal, produtoVO::setPercentualFederal, produtoComConfigsGenericas.getPercentualFederal());
        setIfZeroOrNull(produtoVO::getPercentualEstadual, produtoVO::setPercentualEstadual, produtoComConfigsGenericas.getPercentualEstadual());
        setIfZeroOrNull(produtoVO::getPercentualMunicipal, produtoVO::setPercentualMunicipal, produtoComConfigsGenericas.getPercentualMunicipal());

        //ICMS
        setIfBlank(produtoVO::getSituacaoTributariaICMS, produtoVO::setSituacaoTributariaICMS, produtoComConfigsGenericas.getSituacaoTributariaICMS());
        produtoVO.setIsentoICMS(produtoComConfigsGenericas.isIsentoICMS());
        produtoVO.setEnviaAliquotaNFeICMS(produtoComConfigsGenericas.isEnviaAliquotaNFeICMS());
        setIfZeroOrNull(produtoVO::getAliquotaICMS, produtoVO::setAliquotaICMS, produtoComConfigsGenericas.getAliquotaICMS());

        //CBNEF
        setIfBlank(produtoVO::getCodigoBeneficioFiscal, produtoVO::setCodigoBeneficioFiscal, produtoComConfigsGenericas.getCodigoBeneficioFiscal());

        // ISSQN
        setIfBlank(produtoVO::getSituacaoTributariaISSQN, produtoVO::setSituacaoTributariaISSQN, produtoComConfigsGenericas.getSituacaoTributariaISSQN());
        setIfZeroOrNull(produtoVO::getAliquotaISSQN, produtoVO::setAliquotaISSQN, produtoComConfigsGenericas.getAliquotaISSQN());

        // PIS
        setIfBlank(produtoVO::getSituacaoTributariaPIS, produtoVO::setSituacaoTributariaPIS, produtoComConfigsGenericas.getSituacaoTributariaPIS());
        produtoVO.setIsentoPIS(produtoComConfigsGenericas.isIsentoPIS());
        produtoVO.setEnviaAliquotaNFePIS(produtoComConfigsGenericas.isEnviaAliquotaNFePIS());
        setIfZeroOrNull(produtoVO::getAliquotaPIS, produtoVO::setAliquotaPIS, produtoComConfigsGenericas.getAliquotaPIS());

        // COFINS
        setIfBlank(produtoVO::getSituacaoTributariaCOFINS, produtoVO::setSituacaoTributariaCOFINS, produtoComConfigsGenericas.getSituacaoTributariaCOFINS());
        produtoVO.setIsentoCOFINS(produtoComConfigsGenericas.isIsentoCOFINS());
        produtoVO.setEnviaAliquotaNFeCOFINS(produtoComConfigsGenericas.isEnviaAliquotaNFeCOFINS());
        setIfZeroOrNull(produtoVO::getAliquotaCOFINS, produtoVO::setAliquotaCOFINS, produtoComConfigsGenericas.getAliquotaCOFINS());
    }

    private void settarCamposConfigDeImpostos(ProdutoVO produtoVO, ImpostoProdutoCfopVO impostoProdutoCfopVO) {
        setIfNull(produtoVO::getConfiguracaoNotaFiscalNFSe, produtoVO::setConfiguracaoNotaFiscalNFSe, impostoProdutoCfopVO.getConfiguracaoNotaFiscalNFSe());
        setIfNull(produtoVO::getConfiguracaoNotaFiscalNFCe, produtoVO::setConfiguracaoNotaFiscalNFCe, impostoProdutoCfopVO.getConfiguracaoNotaFiscalNFCe());

        // NOTA FISCAL
        setIfBlank(produtoVO::getCodigoListaServico, produtoVO::setCodigoListaServico, impostoProdutoCfopVO.getCodigoListaServico());
        setIfBlank(produtoVO::getCodigoTributacaoMunicipio, produtoVO::setCodigoTributacaoMunicipio, impostoProdutoCfopVO.getCodigoTributacaoMunicipio());
        setIfBlank(produtoVO::getDescricaoServicoMunicipio, produtoVO::setDescricaoServicoMunicipio, impostoProdutoCfopVO.getDescricaoServicoMunicipio());
        produtoVO.setEnviarPercentualImposto(impostoProdutoCfopVO.isEnviarPercentualImposto());
        setIfZeroOrNull(produtoVO::getPercentualFederal, produtoVO::setPercentualFederal, impostoProdutoCfopVO.getPercentualFederal());
        setIfZeroOrNull(produtoVO::getPercentualEstadual, produtoVO::setPercentualEstadual, impostoProdutoCfopVO.getPercentualEstadual());
        setIfZeroOrNull(produtoVO::getPercentualMunicipal, produtoVO::setPercentualMunicipal, impostoProdutoCfopVO.getPercentualMunicipal());

        //ICMS
        setIfBlank(produtoVO::getSituacaoTributariaICMS, produtoVO::setSituacaoTributariaICMS, impostoProdutoCfopVO.getSituacaoTributariaICMS());
        produtoVO.setIsentoICMS(impostoProdutoCfopVO.isIsentoICMS());
        produtoVO.setEnviaAliquotaNFeICMS(impostoProdutoCfopVO.isEnviaAliquotaNFeICMS());
        setIfZeroOrNull(produtoVO::getAliquotaICMS, produtoVO::setAliquotaICMS, impostoProdutoCfopVO.getAliquotaICMS());

        // ISSQN
        setIfBlank(produtoVO::getSituacaoTributariaISSQN, produtoVO::setSituacaoTributariaISSQN, impostoProdutoCfopVO.getSituacaoTributariaISSQN());
        setIfZeroOrNull(produtoVO::getAliquotaISSQN, produtoVO::setAliquotaISSQN, impostoProdutoCfopVO.getAliquotaISSQN());

        // PIS
        setIfBlank(produtoVO::getSituacaoTributariaPIS, produtoVO::setSituacaoTributariaPIS, impostoProdutoCfopVO.getSituacaoTributariaPIS());
        produtoVO.setIsentoPIS(impostoProdutoCfopVO.isIsentoPIS());
        produtoVO.setEnviaAliquotaNFePIS(impostoProdutoCfopVO.isEnviaAliquotaNFePIS());
        setIfZeroOrNull(produtoVO::getAliquotaPIS, produtoVO::setAliquotaPIS, impostoProdutoCfopVO.getAliquotaPIS());

        // COFINS
        setIfBlank(produtoVO::getSituacaoTributariaCOFINS, produtoVO::setSituacaoTributariaCOFINS, impostoProdutoCfopVO.getSituacaoTributariaCOFINS());
        produtoVO.setIsentoCOFINS(impostoProdutoCfopVO.isIsentoCOFINS());
        produtoVO.setEnviaAliquotaNFeCOFINS(impostoProdutoCfopVO.isEnviaAliquotaNFeCOFINS());
        setIfZeroOrNull(produtoVO::getAliquotaCOFINS, produtoVO::setAliquotaCOFINS, impostoProdutoCfopVO.getAliquotaCOFINS());
    }

    public String montarJsonToSaveDoProdutoComConfigsGenericasJson(ProdutoVO produtoComConfigsGenericas) {
        JSONObject jsonToSave = new JSONObject();
        jsonToSave.put("configuracaoNotaFiscalNFSe", produtoComConfigsGenericas.getConfiguracaoNotaFiscalNFSe().getCodigo());
        jsonToSave.put("configuracaoNotaFiscalNFCe", produtoComConfigsGenericas.getConfiguracaoNotaFiscalNFCe().getCodigo());

        //NOTA FISCAL
        jsonToSave.put("codigoListaServico", produtoComConfigsGenericas.getCodigoListaServico());
        jsonToSave.put("codigoTributacaoMunicipio", produtoComConfigsGenericas.getCodigoTributacaoMunicipio());
        jsonToSave.put("descricaoServicoMunicipio", produtoComConfigsGenericas.getDescricaoServicoMunicipio());
        jsonToSave.put("enviarPercentualImposto", produtoComConfigsGenericas.isEnviarPercentualImposto());
        jsonToSave.put("percentualFederal", produtoComConfigsGenericas.getPercentualFederal());
        jsonToSave.put("percentualEstadual", produtoComConfigsGenericas.getPercentualEstadual());
        jsonToSave.put("percentualMunicipal", produtoComConfigsGenericas.getPercentualMunicipal());

        //ICMS
        jsonToSave.put("situacaoTributariaICMS", produtoComConfigsGenericas.getSituacaoTributariaICMS());
        jsonToSave.put("isentoICMS", produtoComConfigsGenericas.isIsentoICMS());
        jsonToSave.put("enviaAliquotaNFeICMS", produtoComConfigsGenericas.isEnviaAliquotaNFeICMS());
        jsonToSave.put("aliquotaICMS", produtoComConfigsGenericas.getAliquotaICMS());

        //CBNEF
        jsonToSave.put("codigoDeBenefecioFiscal", produtoComConfigsGenericas.getCodigoBeneficioFiscal());

        //ISSQN
        jsonToSave.put("situacaoTributariaISSQN", produtoComConfigsGenericas.getSituacaoTributariaISSQN());
        jsonToSave.put("aliquotaISSQN", produtoComConfigsGenericas.getAliquotaISSQN());

        //PIS
        jsonToSave.put("situacaoTributariaPIS", produtoComConfigsGenericas.getSituacaoTributariaPIS());
        jsonToSave.put("isentoPIS", produtoComConfigsGenericas.isIsentoPIS());
        jsonToSave.put("enviaAliquotaNFePIS", produtoComConfigsGenericas.isEnviaAliquotaNFePIS());
        jsonToSave.put("aliquotaPIS", produtoComConfigsGenericas.getAliquotaPIS());

        //COFINS
        jsonToSave.put("situacaoTributariaCOFINS", produtoComConfigsGenericas.getSituacaoTributariaCOFINS());
        jsonToSave.put("isentoCOFINS", produtoComConfigsGenericas.isIsentoCOFINS());
        jsonToSave.put("enviaAliquotaNFeCOFINS", produtoComConfigsGenericas.isEnviaAliquotaNFeCOFINS());
        jsonToSave.put("aliquotaCOFINS", produtoComConfigsGenericas.getAliquotaCOFINS());

        return jsonToSave.toString();
    }

    public void montarConfigsBasicasComBaseNoUltimoJson() throws Exception {
        String stringToJson = getFacade().getCompra().consultarUltimoProdutoConfigJson(getEmpresaLogado().getCodigo());

        if (stringToJson == null || stringToJson.isEmpty()) {
            produtoComConfigsGenericas = new ProdutoVO();
        } else {
            JSONObject json = new JSONObject(stringToJson);
            produtoComConfigsGenericas = new ProdutoVO();

            produtoComConfigsGenericas.setConfiguracaoNotaFiscalNFSe(
                    getFacade().getConfiguracaoNotaFiscal().consultarPorChavePrimaria(json.optInt("configuracaoNotaFiscalNFSe", 0), Uteis.NIVELMONTARDADOS_DADOSBASICOS)
            );

            produtoComConfigsGenericas.setConfiguracaoNotaFiscalNFCe(
                    getFacade().getConfiguracaoNotaFiscal().consultarPorChavePrimaria(json.optInt("configuracaoNotaFiscalNFCe", 0), Uteis.NIVELMONTARDADOS_DADOSBASICOS)
            );

            // Nota Fiscal
            produtoComConfigsGenericas.setCodigoListaServico(json.optString("codigoListaServico", null));
            produtoComConfigsGenericas.setCodigoTributacaoMunicipio(json.optString("codigoTributacaoMunicipio", null));
            produtoComConfigsGenericas.setDescricaoServicoMunicipio(json.optString("descricaoServicoMunicipio", null));
            produtoComConfigsGenericas.setEnviarPercentualImposto(json.optBoolean("enviarPercentualImposto", false));
            produtoComConfigsGenericas.setPercentualFederal(json.optDouble("percentualFederal", 0d));
            produtoComConfigsGenericas.setPercentualEstadual(json.optDouble("percentualEstadual", 0d));
            produtoComConfigsGenericas.setPercentualMunicipal(json.optDouble("percentualMunicipal", 0d));

            //ICMS
            produtoComConfigsGenericas.setSituacaoTributariaICMS(json.optString("situacaoTributariaICMS", null));
            produtoComConfigsGenericas.setIsentoICMS(json.optBoolean("isentoICMS", false));
            produtoComConfigsGenericas.setEnviaAliquotaNFeICMS(json.optBoolean("enviaAliquotaNFeICMS", false));
            produtoComConfigsGenericas.setAliquotaICMS(json.optDouble("aliquotaICMS", 0d));

            //CBNEF
            produtoComConfigsGenericas.setCodigoBeneficioFiscal(json.optString("codigoDeBenefecioFiscal", null));

            // ISSQN
            produtoComConfigsGenericas.setSituacaoTributariaISSQN(json.optString("situacaoTributariaISSQN", null));
            produtoComConfigsGenericas.setAliquotaISSQN(json.optDouble("aliquotaISSQN", 0d));

            // PIS
            produtoComConfigsGenericas.setSituacaoTributariaPIS(json.optString("situacaoTributariaPIS", null));
            produtoComConfigsGenericas.setIsentoPIS(json.optBoolean("isentoPIS", false));
            produtoComConfigsGenericas.setEnviaAliquotaNFePIS(json.optBoolean("enviaAliquotaNFePIS", false));
            produtoComConfigsGenericas.setAliquotaPIS(json.optDouble("aliquotaPIS", 0d));

            // COFINS
            produtoComConfigsGenericas.setSituacaoTributariaCOFINS(json.optString("situacaoTributariaCOFINS", null));
            produtoComConfigsGenericas.setIsentoCOFINS(json.optBoolean("isentoCOFINS", false));
            produtoComConfigsGenericas.setEnviaAliquotaNFeCOFINS(json.optBoolean("enviaAliquotaNFeCOFINS", false));
            produtoComConfigsGenericas.setAliquotaCOFINS(json.optDouble("aliquotaCOFINS", 0d));
        }
    }

    private void adicionarProdutoNaCompra(ProdutoVO produto, Integer quantidade, Double valorUnitarioCompra, Double desconto) {
        compraVO.getItens().add(new CompraItensVO(
                compraVO,
                produto,
                compraVO.getDataCadastro(),
                null,
                quantidade,
                valorUnitarioCompra,
                desconto,
                valorUnitarioCompra * quantidade.doubleValue(),
                0,
                0
        ));
    }

    public void adicionarParcelaContaFinanceiroCompra() {
        try {
            if(getCompraVO().getCompraContasLancarFinanceiro() == null){
                getCompraVO().setCompraContasLancarFinanceiro(new ArrayList<>());
            }

            validarItensEdicaoContaCompraFinanceiro();

            ContaFinanceiroCompraVO contaFinanceiroCompra = new ContaFinanceiroCompraVO();
            contaFinanceiroCompra.setNrParcela(getNrParcelaContaFinanceiro());
            contaFinanceiroCompra.setDataVencimento(getDataVencimentoContaCompra());
            contaFinanceiroCompra.setValorParcela(getValorParcelaContaFinanceiro());
            contaFinanceiroCompra.setCodigoBarras(getCodigoBarrasParcelaContaFinanceiro());
            getCompraVO().getCompraContasLancarFinanceiro().add(contaFinanceiroCompra);

            Ordenacao.ordenarLista(getCompraVO().getCompraContasLancarFinanceiro(), "nrParcela");

            setNrParcelaContaFinanceiro(null);
            setDataVencimentoContaCompra(null);
            setValorParcelaContaFinanceiro(null);
            setCodigoBarrasParcelaContaFinanceiro(null);

            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    private void validarItensEdicaoContaCompraFinanceiro() throws Exception {

        if (UteisValidacao.emptyNumber(getNrParcelaContaFinanceiro())) {
            throw new Exception("Obrigatório informar campo: Parcela");
        }

        if (getNrParcelaContaFinanceiro() > getCompraVO().getNrParcelasContaPagar()) {
            throw new Exception("Número da parcela não pode ser maior que o número de parcelas definido na compra.");
        }

        if (getDataVencimentoContaCompra() == null) {
            throw new Exception("Obrigatório informar campo: Data de Vencimento");
        }

        if (UteisValidacao.emptyNumber(getValorParcelaContaFinanceiro())) {
            throw new Exception("Obrigatório informar campo: Valor");
        }

        if (Calendario.menor(getDataVencimentoContaCompra(), getCompraVO().getDataCadastro())) {
            throw new Exception("Data de vencimento não pode ser menor que a data da compra.");
        }

        double valorTotalParcelas = 0;
        for (ContaFinanceiroCompraVO conta : getCompraVO().getCompraContasLancarFinanceiro()) {
            valorTotalParcelas += conta.getValorParcela();
            if (conta.getNrParcela().equals(getNrParcelaContaFinanceiro())) {
                throw new Exception("Já existe uma parcela com o número informado.");
            }
        }

        if (getCompraVO().getItens() == null || getCompraVO().getItens().isEmpty()) {
            throw new Exception("É necessário adicionar produtos à compra antes de adicionar parcelas.");
        } else {
            double valorTotalCompra = 0;
            for (CompraItensVO item : getCompraVO().getItens()) {
                valorTotalCompra += item.getTotal();
            }
            if (Uteis.arredondarForcando2CasasDecimais((valorTotalParcelas + getValorParcelaContaFinanceiro())) > Uteis.arredondarForcando2CasasDecimais(valorTotalCompra)) {
                throw new Exception("O valor total das parcelas não pode ser maior que o valor total da compra. Diferença de R$ "+
                        (Uteis.arredondarForcando2CasasDecimais(valorTotalParcelas + getValorParcelaContaFinanceiro() - valorTotalCompra)));
            }
        }
    }

    public void removerParcelaContaFinanceiroCompra() {
        ContaFinanceiroCompraVO obj = (ContaFinanceiroCompraVO) context().getExternalContext().getRequestMap().get("contaFinanceiroCompra");
        int index = 0;
        Iterator i = getCompraVO().getCompraContasLancarFinanceiro().iterator();
        while (i.hasNext()) {
            ContaFinanceiroCompraVO objExistente = (ContaFinanceiroCompraVO) i.next();
            if (objExistente.equals(obj)) {
                getCompraVO().getCompraContasLancarFinanceiro().remove(index);
                return;
            }
            index++;
        }
        setMensagemID("msg_dados_excluidos");
    }


    public void editarParcelaContaFinanceiroCompra() {
        ContaFinanceiroCompraVO obj = (ContaFinanceiroCompraVO) context().getExternalContext().getRequestMap().get("contaFinanceiroCompra");
        int index = 0;
        Iterator i = getCompraVO().getCompraContasLancarFinanceiro().iterator();
        while (i.hasNext()) {
            ContaFinanceiroCompraVO objExistente = (ContaFinanceiroCompraVO) i.next();
            if (objExistente.equals(obj)) {
                setNrParcelaContaFinanceiro(objExistente.getNrParcela());
                setDataVencimentoContaCompra(objExistente.getDataVencimento());
                setValorParcelaContaFinanceiro(objExistente.getValorParcela());
                setCodigoBarrasParcelaContaFinanceiro(objExistente.getCodigoBarras());
                getCompraVO().getCompraContasLancarFinanceiro().remove(index);
                return;
            }
            index++;
        }
    }

    public void limparProdutosNaoEncontradosNaNfe() {
        this.produtosNaoEncontradosNaNfe.clear();
    }

    private void uploadXmlNfe(UploadEvent upload) throws Exception {
        UploadItem item = upload.getUploadItem();
        novo();

        // Verificar se o arquivo é XML
        String fileName = item.getFileName();
        if (!fileName.endsWith(".xml")) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.tipoInvalido", "Somente arquivos XML são permitidos.");
            throw new Exception("Arquivo não é um XML válido.");
        }

        // Verificar tamanho do arquivo
        if (item.getFile().length() > 512000) { // 500 KB
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.tamanhoLimiteExcedido", "Arquivo tem tamanho superior a 500KB");
            throw new Exception("Tamanho Superior a 500KB");
        }

        // Processar o arquivo XML
        XmlMapper xmlMapper = new XmlMapper();
        NfeProc nfeProc = xmlMapper.readValue(item.getFile(), NfeProc.class);

        if(nfeProc.getNfe() == null){
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.erroProcessamento", "NFe invalida (Não processada pelo SEFAZ)");
            throw new Exception("NFe fornecida invalida, precisa ser processada e autorizada pelo SEFAZ");
        }

        CompraVO existeCompraVO = getFacade().getCompra().consultarPorNumeroNF(nfeProc.getNfe().getInfNFe().getIde().getNumeroNF(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if(existeCompraVO != null && !existeCompraVO.getCancelada() ) {
            setSucesso(false);
            setErro(true);
            String msg = "Já existe uma compra ATIVA registrada com esse numeroNF (" + existeCompraVO.getNumeroNF() + "). Código: " + existeCompraVO.getCodigo()
                            + ". Data de Cadastro: " + Uteis.getData(existeCompraVO.getDataCadastro()) + ".";
            setMensagemDetalhada("operacoes.arquivo.upload.erroProcessamento", msg);
            throw new Exception(msg);
        }

        String[] partes = item.getFileName().split("\\.");
        setDocumento(item.getFile());
        getCompraVO().setDocumentoExtensao("." + partes[partes.length - 1]);

        compraVO.setImportacaoObs("XML_NFE");
        compraVO.setNumeroNF(nfeProc.getNfe().getInfNFe().getIde().getNumeroNF());
        setNumeroNF(nfeProc.getNfe().getInfNFe().getIde().getNumeroNF());
        compraVO.setTelefoneContato(nfeProc.getNfe().getInfNFe().getEmitente().getEnderecoEmitente().getTelefoneContato());
        compraVO.setContato(nfeProc.getNfe().getInfNFe().getEmitente().getNome());
        fornecedorDoXmlDaNfe(nfeProc);
        produtosXmlNfe(nfeProc);
        compraVO.setCancelada(false);
        String dataEmissaoStr = nfeProc.getNfe().getInfNFe().getIde().getDataEmitida();
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(dataEmissaoStr);
        Date dataEmissao = Date.from(offsetDateTime.toInstant());
        compraVO.setDataEmissao(dataEmissao);
    }

    public void uploadDocumento(UploadEvent upload) {
        try {
            limparMsg();
            upload(upload);
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    private void upload(UploadEvent upload) throws Exception {
        UploadItem item = upload.getUploadItem();
        if (item.getFile().length() > 1048576) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.tamanhoLimiteExcedido", "Arquivo tem tamanho superior a 1MB");
            throw new Exception("Tamanho Superior a 1MB");
        }
        String[] partes = item.getFileName().split("\\.");

        if(getDocumentos() == null){
            setDocumentos(new HashMap<>());
        }
        getDocumentos().put(item.getFile(), "." + partes[partes.length - 1]);

        setSucesso(true);
        setErro(false);
        setMensagem("Arquivo enviado com sucesso");
        setMensagemDetalhada("", "");
    }

    public void limparArquivo() {
        try {
            limparMsg();
            setDocumento(null);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void downloadDocumentoListener(ActionEvent actionEvent) {
        try {
            limparMsg();
            downloadArquivoListener(actionEvent);
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    private void downloadArquivoListener(ActionEvent actionEvent) throws Exception {
        HttpServletResponse res = (HttpServletResponse) context().getExternalContext().getResponse();
        if (getCompraVO().getDocumentosCompra() != null) {
            // Criando o nome do ZIP
            String nomeZip = getCompraVO().getCodigo() + "_DOCUMENTOS_COMPRA.zip";
            res.setHeader("Content-Disposition", "attachment; filename=\"" + nomeZip + "\"");
            res.setContentType("application/zip");
            res.setCharacterEncoding("UTF-8");

            try (ZipOutputStream zipOut = new ZipOutputStream(res.getOutputStream())) {
                for (DocumentoCompraVO doc : getCompraVO().getDocumentosCompra()) {
                    byte[] arquivo = MidiaService.getInstance().downloadObjectWithExtensionAsByteArray(
                            getKey(),
                            MidiaEntidadeEnum.ANEXO_DOCUMENTO_COMPRA,
                            doc.getIdentificadorArquivo(),
                            doc.getExtensaoArquivo(),
                            doc.getChaveArquivo()
                    );

                    if (arquivo == null || arquivo.length == 0) {
                        throw new Exception("Não foi possível realizar o download do arquivo " +
                                (UteisValidacao.emptyString(doc.getExtensaoArquivo()) ? ", extensão do arquivo inválida." : ""));
                    }

                    // Nome do arquivo dentro do ZIP
                    String nomeArquivo = doc.getIdentificadorArquivo() + "_COMP" + doc.getExtensaoArquivo();

                    // Criando entrada no ZIP
                    zipOut.putNextEntry(new ZipEntry(nomeArquivo));
                    zipOut.write(arquivo);
                    zipOut.closeEntry();
                }
                zipOut.finish();
            }
            FacesContext.getCurrentInstance().responseComplete(); // Finaliza a resposta corretamente
        } else {
            ServletOutputStream out = res.getOutputStream();
            byte[] b = MidiaService.getInstance().downloadObjectWithExtensionAsByteArray(getKey(),
                    MidiaEntidadeEnum.ANEXO_DOCUMENTO_COMPRA,
                    getCompraVO().getCodigo().toString() + "_DOC",
                    getCompraVO().getDocumentoExtensao(), null);
            if (b == null || b.length == 0) {
                throw new Exception("Não foi possível realizar o download do arquivo" +
                        (UteisValidacao.emptyString(getCompraVO().getDocumentoExtensao()) ? ", extensão do arquivo inválida." : ""));
            }

            String nomeArquivo = getCompraVO().getCodigo() + ("_COMP" + getCompraVO().getDocumentoExtensao());
            res.setHeader("Content-disposition", "attachment;filename=\"" + nomeArquivo + "\"");
            res.setContentLength(b.length);
            res.setContentType("application/octet-stream");

            out.write(b);
            out.flush();
            out.close();
            FacesContext.getCurrentInstance().responseComplete();
        }
    }

    public void excluirArqDocumentoListener(ActionEvent actionEvent) {
        try {
            limparMsg();
            getFacade().getCompraDocumentos().excluirPorCompra(getKey(), getCompraVO());
            setExisteDocumento(false);
            setDocumentos(null);
            setDocumento(null);
            getCompraVO().setDocumento("");
            getCompraVO().setDocumentosCompra(null);
            montarSucessoGrowl("Arquivo excluído com sucesso!");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    private void anexarArquivo() throws Exception {
        if (getDocumentos() != null) {
            getDocumentos().forEach((arquivo, extensao) -> {
                String chaveArquivo = null;
                String identificador = getCompraVO().getCodigo().toString() + "_DOC_" + Calendario.hoje().getTime();
                try {
                    chaveArquivo = MidiaService.getInstance().uploadObjectWithExtension(getKey(),
                            MidiaEntidadeEnum.ANEXO_DOCUMENTO_COMPRA,
                            identificador,
                            arquivo,
                            extensao);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                setExisteDocumento(true);
                if (getCompraVO().getDocumentosCompra() == null) {
                    getCompraVO().setDocumentosCompra(new ArrayList<>());
                }
                getCompraVO().getDocumentosCompra().add(new DocumentoCompraVO(chaveArquivo, identificador, extensao));
            });
            getFacade().getCompraDocumentos().incluirListaDocumentosCompra(getKey(), getCompraVO());
            setDocumentos(null);
        }

        if(!UteisValidacao.emptyString(getCompraVO().getDocumento())) { // Inativar modelo antigo.
            MidiaService.getInstance().deleteObject(getCompraVO().getDocumento());
            getCompraVO().setDocumento("");
            getCompraVO().setDocumentoExtensao("");
            getFacade().getCompra().alterarSomenteChaveArquivo(getCompraVO());
        }
    }

    public boolean isContaPagarCompraEstoque() throws Exception {
        return getFacade().getConfiguracaoFinanceiro().consultar().isContaPagarCompraEstoque();
    }

    public boolean isOrdemCompraEstoque() throws Exception {
        return getFacade().getConfiguracaoFinanceiro().consultar().isOrdemCompraEstoque();
    }

    private List<MovContaVO> alterarMovContaCompraEstoque(CompraVO compra) throws Exception {
        List<MovContaVO> movConta = getFacade().getMovConta().consultarMovContaCompraEstoque(compraVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        if(movConta != null && !movConta.isEmpty()) {
            Ordenacao.ordenarLista(movConta, "codigo");
            final int[] qtdContasPagar = {1};
            movConta.forEach(m ->{
                Date dataVencContaPagar = ((qtdContasPagar[0] > 1) ? Calendario.somarMeses(compra.getDataCadastro(), qtdContasPagar[0]-1) : compra.getDataCadastro());
                m.setDescricao(compra.getDescricaoFinanceiro() + (compra.getNrParcelasContaPagar() > 1 ? " " + qtdContasPagar[0] + "/" + compra.getNrParcelasContaPagar() : ""));

                if (compra.getCompraContasLancarFinanceiro() != null && !compra.getCompraContasLancarFinanceiro().isEmpty()) {
                    dataVencContaPagar = compra.getCompraContasLancarFinanceiro().stream().filter(c -> c.getNrParcela().equals(qtdContasPagar[0])).findFirst().get().getDataVencimento();
                }

                try {
                    m.setPessoaVO(getFacade().getFornecedor().obter(compra.getFornecedor().getCodigo()).getPessoa());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                if (m.getDataQuitacao() == null) {
                    m.setDataCompetencia(dataVencContaPagar);
                    m.setDataVencimento(dataVencContaPagar);
                    m.setDataLancamento(compra.getDataCadastro());
                    m.setNumeroDocumento(compra.getNumeroNF());
                    final String[] observacao = {""};
                    compra.getItens().forEach(item -> observacao[0] += "PRODUTO: " + item.getProduto().getDescricao().toLowerCase() + " \nQUANTIDADE: " + item.getQuantidade() + "\nVALOR TOTAL: " + item.getTotal_Apresentar() + "\n-----------------------------------------------------\n");
                    m.setObservacoes(observacao[0] + "\nQTD PARCELAS: " + compra.getObservacoes() + "\n-----------------------------------------------------\n");
                    m.setObservacoes(m.getObservacoes() + "\nCOMPRA OBS: " + compra.getObservacoes());
                }
                qtdContasPagar[0]++;
            });
        }
        return movConta;
    }

    private void povoarMovContaCompraEstoque(CompraVO compra, Boolean autorizar) throws Exception {
        int qtdContasPagar = 1;
        while(qtdContasPagar <= compra.getNrParcelasContaPagar()) {
            String codigoBarras = "";
            Date dataVencContaPagar = ((qtdContasPagar > 1) ? Calendario.somarMeses(compra.getDataCadastro(), qtdContasPagar-1) : compra.getDataCadastro());
            double totalCompra = compra.getValorTotal()/compra.getNrParcelasContaPagar();

            if (compra.getCompraContasLancarFinanceiro() != null && !compra.getCompraContasLancarFinanceiro().isEmpty()) {
                int nrParcelaGerar = qtdContasPagar;
                totalCompra = compra.getCompraContasLancarFinanceiro().stream().filter(c -> c.getNrParcela().equals(nrParcelaGerar)).findFirst().get().getValorParcela();
                dataVencContaPagar = compra.getCompraContasLancarFinanceiro().stream().filter(c -> c.getNrParcela().equals(nrParcelaGerar)).findFirst().get().getDataVencimento();
                codigoBarras = compra.getCompraContasLancarFinanceiro().stream().filter(c -> c.getNrParcela().equals(nrParcelaGerar)).findFirst().get().getCodigoBarras();
            }

            MovContaVO movConta = new MovContaVO();

            movConta.setTipoOperacaoLancamento(TipoOperacaoLancamento.PAGAMENTO);
            movConta.setEmpresaVO(compra.getEmpresa());
            movConta.setUsuarioVO(getUsuarioLogado());
            movConta.setPessoaVO(getFacade().getFornecedor().obter(compra.getFornecedor().getCodigo()).getPessoa());
            movConta.setDescricao(compra.getDescricaoFinanceiro() + (compra.getNrParcelasContaPagar() > 1 ? " " + qtdContasPagar + "/" + compra.getNrParcelasContaPagar() : ""));
            movConta.setValor(totalCompra);
            movConta.setCodigoBarras(codigoBarras);
            movConta.setDataLancamento(compra.getDataCadastro());
            movConta.setDataVencimento(dataVencContaPagar);
            movConta.setDataCompetencia(compra.getDataCadastro());
            movConta.setNumeroDocumento(compra.getNumeroNF());
            final String[] observacao = {""};
            compra.getItens().forEach(item -> observacao[0] += "PRODUTO: " + item.getProduto().getDescricao().toLowerCase() + " \nQUANTIDADE: " + item.getQuantidade() + "\nVALOR TOTAL: " + item.getTotal_Apresentar() + "\n-----------------------------------------------------\n");
            movConta.setObservacoes(observacao[0] + "\nQTD PARCELAS: " + compra.getNrParcelasContaPagar() + "\n-----------------------------------------------------\n");
            movConta.setObservacoes(movConta.getObservacoes() + "\nCOMPRA OBS: " + compra.getObservacoes());
            movConta.setCompraEstoque(compra.getCodigo());

            //Se autorizar não é null está lançando uma ordem de compra, se for null está lançando uma compra estoque
            //Essa tratativa tem o objetivo de garantir que o plano de contas e centro de custos seja sempre preenchido
            if (autorizar != null && autorizar) {
                for (CompraItensVO item : compra.getItens()) {
                    List<RateioIntegracaoTO> rateios = getFacade().getRateioIntegracao().consultar(EntidadeRateioEnum.PRODUTO, item.getProduto().getCodigo());
                    for (RateioIntegracaoTO rateio : rateios) {
                        if (item.getProduto().getCodigo().equals(rateio.getCodigoProduto())) {
                            if ((rateio.getTipoES() != null && rateio.getTipoES().equals(TipoES.SAIDA)) && rateio.getPercentagem().equals(100.0)) {
                                MovContaRateioVO movContaRateio = new MovContaRateioVO();
                                movContaRateio.setDescricao(compra.getDescricaoFinanceiro() + " - " + item.getProduto().getDescricao());
                                if (compra.getNrParcelasContaPagar() > 1) {
                                    movContaRateio.setValor(item.getTotal() / compra.getNrParcelasContaPagar());
                                } else {
                                    movContaRateio.setValor(item.getTotal());
                                }
                                PlanoContaTO plano = getFacade().getPlanoConta().obter(rateio.getPlanoConta());
                                movContaRateio.setPlanoContaVO(plano);
                                try {
                                    CentroCustoTO centroCusto = getFacade().getCentroCusto().obter(rateios.stream().filter(r ->
                                            (!UteisValidacao.emptyNumber(r.getCentroCusto()) && r.getCodigoProduto().equals(item.getProduto().getCodigo()) && r.getPercentagem().equals(100.0))).findFirst().get().getCentroCusto());
                                    movContaRateio.setCentroCustoVO(centroCusto);
                                } catch (Exception ignore) {
                                }
                                movContaRateio.setTipoES(plano.getTipoPadrao());
                                if (!UteisValidacao.emptyString(codigoBarras)) {
                                    movConta.setTipoForma(TipoFormaPagto.BOLETOBANCARIO);
                                    movContaRateio.setFormaPagamentoVO(
                                            getFacade().getFormaPagamento().consultarPorTipoFormaPagamentoAtiva(
                                                    TipoFormaPagto.BOLETOBANCARIO.getSigla(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                                }

                                try {
                                    List<TipoDocumentoVO> listaTipoDocumento = getFacade().getTipoDocumento().consultarTodas(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    TipoDocumentoVO tipoDocumentoVONotaFiscal = obterTipoDocumento("nota fiscal", listaTipoDocumento);
                                    movContaRateio.setTipoDocumentoVO(tipoDocumentoVONotaFiscal);
                                }catch (Exception ignore){}

                                movConta.getMovContaRateios().add(movContaRateio);
                            }
                        }
                    }
                }
                compra.setMovConta(movConta);
            }else if(autorizar == null) {
                for (CompraItensVO item : compra.getItens()) {
                    List<RateioIntegracaoTO> rateios = getFacade().getRateioIntegracao().consultar(EntidadeRateioEnum.PRODUTO, item.getProduto().getCodigo());
                    for (RateioIntegracaoTO rateio : rateios) {
                        if (item.getProduto().getCodigo().equals(rateio.getCodigoProduto())) {
                            if ((rateio.getTipoES() != null && rateio.getTipoES().equals(TipoES.SAIDA)) && rateio.getPercentagem().equals(100.0)) {
                                MovContaRateioVO movContaRateio = new MovContaRateioVO();
                                movContaRateio.setDescricao(compra.getDescricaoFinanceiro() + " - " + item.getProduto().getDescricao());
                                if (compra.getNrParcelasContaPagar() > 1) {
                                    movContaRateio.setValor(item.getTotal() / compra.getNrParcelasContaPagar());
                                } else {
                                    movContaRateio.setValor(item.getTotal());
                                }
                                PlanoContaTO plano = getFacade().getPlanoConta().obter(rateio.getPlanoConta());
                                movContaRateio.setPlanoContaVO(plano);
                                try {
                                    CentroCustoTO centroCusto = getFacade().getCentroCusto().obter(rateios.stream().filter(r ->
                                            (!UteisValidacao.emptyNumber(r.getCentroCusto()) && r.getCodigoProduto().equals(item.getProduto().getCodigo()) && r.getPercentagem().equals(100.0))).findFirst().get().getCentroCusto());
                                    movContaRateio.setCentroCustoVO(centroCusto);
                                } catch (Exception ignore) {
                                }
                                movContaRateio.setTipoES(plano.getTipoPadrao());
                                if (!UteisValidacao.emptyString(codigoBarras)) {
                                    movConta.setTipoForma(TipoFormaPagto.BOLETOBANCARIO);
                                    movContaRateio.setFormaPagamentoVO(
                                            getFacade().getFormaPagamento().consultarPorTipoFormaPagamentoAtiva(
                                                    TipoFormaPagto.BOLETOBANCARIO.getSigla(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                                }

                                try {
                                    List<TipoDocumentoVO> listaTipoDocumento = getFacade().getTipoDocumento().consultarTodas(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    TipoDocumentoVO tipoDocumentoVONotaFiscal = obterTipoDocumento("nota fiscal", listaTipoDocumento);
                                    movContaRateio.setTipoDocumentoVO(tipoDocumentoVONotaFiscal);
                                }catch (Exception ignore){}

                                movConta.getMovContaRateios().add(movContaRateio);
                            }
                        }
                    }
                }
                compra.setMovConta(movConta);
            }

            if (movConta.getMovContaRateios() == null || movConta.getMovContaRateios().isEmpty()) {
                MovContaRateioVO movContaRateio = new MovContaRateioVO();
                movContaRateio.setDescricao(movConta.getDescricao());
                movContaRateio.setValor(movConta.getValor());
                movContaRateio.setTipoES(TipoES.SAIDA);
                if (!UteisValidacao.emptyString(codigoBarras)) {
                    movConta.setTipoForma(TipoFormaPagto.BOLETOBANCARIO);
                    movContaRateio.setFormaPagamentoVO(
                            getFacade().getFormaPagamento().consultarPorTipoFormaPagamentoAtiva(
                                    TipoFormaPagto.BOLETOBANCARIO.getSigla(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }

                try {
                    List<TipoDocumentoVO> listaTipoDocumento = getFacade().getTipoDocumento().consultarTodas(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    TipoDocumentoVO tipoDocumentoVONotaFiscal = obterTipoDocumento("nota fiscal", listaTipoDocumento);
                    movContaRateio.setTipoDocumentoVO(tipoDocumentoVONotaFiscal);
                }catch (Exception ignore){}

                movConta.setMovContaRateios(asList(movContaRateio));
                /* Valor pago sendo setado com o valor da conta removido devido ao ticket M2-2953 */
                //movConta.setValorPago(movConta.getValor());
                compra.setMovConta(movConta);
            }
            if(compraVO.getMovConta() != null && UteisValidacao.emptyNumber(compraVO.getMovConta().getCodigo())){
                getFacade().getMovConta().incluir(compraVO.getMovConta(), 0, false, null);
            }
            qtdContasPagar++;
        }
    }

    private static TipoDocumentoVO obterTipoDocumento(String descricao, List<TipoDocumentoVO> lista) {
        for (TipoDocumentoVO obj : lista) {
            if (obj.getDescricao().equalsIgnoreCase(descricao)) {
                return obj;
            }
        }
        return null;
    }

    private void registrarLog() throws Exception {
        List logS = compraVO.gerarLogAlteracaoObjetoVO();
        LogInterfaceFacade logFacade = getFacade().getLog();
        for (CompraItensVO obj : compraVO.getItensList()) {
            LogVO log = (LogVO) ((LogVO) logS.get(0)).getClone(true);
            log.setDescricao("Add produto na compra: " + compraVO.getCodigo());
            log.setOperacao("INCLUSÃO");
            log.setNomeCampo("Produto");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(obj.getProduto().getDescricao() + ". Qtd= " + obj.getQuantidade() + "  Valor Unit= " + obj.getValorUnitario() + "  Desconto Total= " + obj.getDesconto());
            logFacade.incluirSemCommit(log);
            logS.add(log);
        }

        if (!UteisValidacao.emptyList(compraVO.getCompraContasLancarFinanceiro())) {
            for (ContaFinanceiroCompraVO obj : compraVO.getCompraContasLancarFinanceiro()) {
                LogVO log = (LogVO) ((LogVO) logS.get(0)).getClone(true);
                log.setDescricao("Add conta personalizada na compra: " + compraVO.getCodigo());
                log.setOperacao("INCLUSÃO");
                log.setNomeCampo("Conta personalizada");
                log.setValorCampoAnterior("");
                log.setValorCampoAlterado("Parcela= " + obj.getNrParcela() + ". Data de Vencimento= " + obj.getDataVencimento_Apresentar() + ".  Valor= " + obj.getValorParcela() + ".  Código de Barras= " + obj.getCodigoBarras());
                logFacade.incluirSemCommit(log);
                logS.add(log);
            }
        }
    }
    
    public void realizarConsultaLogObjetoGeral() {
        compraVO = new CompraVO();
        realizarConsultaLogObjetoSelecionadoCompra();

    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>CompraVO</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        limparCampos();
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            validarPermissaoCadastrarCompra();
            setCompraVO(getFacade().getCompra().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS));
            if (!UteisValidacao.emptyString(getCompraVO().getDocumento()) || !UteisValidacao.emptyList(getCompraVO().getDocumentosCompra())) {
                setExisteDocumento(true);
            }
            getCompraVO().setNovoObj(new Boolean(false));
            getCompraVO().registrarObjetoVOAntesDaAlteracao();
            setDataCompraValida(this.compraVO.getDataCadastro());
            dataCompraOriginal = this.compraVO.getDataCadastro();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
        return "editar";
    }

    private void limparCampos() {
        setNrParcelaContaFinanceiro(null);
        setDataVencimentoContaCompra(null);
        setValorParcelaContaFinanceiro(null);
        setDocumentos(null);
        setDocumento(null);
        if (getCompraVO() != null) {
            getCompraVO().setDocumento("");
            getCompraVO().setDocumentosCompra(null);
            getCompraVO().setCompraContasLancarFinanceiro(null);
        }
        setExisteDocumento(false);
    }

    public void registrarLogCancelarCompra() throws  Exception{

                LogVO log = new LogVO();
                log.setChavePrimaria(compraVO.getCodigo().toString());
                log.setDescricao("Produtos da Compra");
                log.setNomeEntidade("COMPRA");
                log.setResponsavelAlteracao(getUsuarioLogado().getNome());
                log.setNomeEntidadeDescricao("COMPRA");
                log.setOperacao("CANCELAMENTO COMPRA");
                log.setNomeCampo("*Situação Compra:");
                if(compraVO.getCancelada()) {
                     log.setValorCampoAnterior("Ativa");
                     log.setValorCampoAlterado("Cancelada");
                }else{
                    log.setValorCampoAnterior("Cancelada");
                    log.setValorCampoAlterado("Ativa");
                }
                getFacade().getLog().incluirSemCommit(log);

    }

    public void consultarCompra(Integer codigoCompra) {
        try {
            setCompraVO(getFacade().getCompra().consultarPorChavePrimaria(codigoCompra, Uteis.NIVELMONTARDADOS_TODOS));
            getCompraVO().setNovoObj(new Boolean(false));
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_editar");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }

    }

    public void editarCompraItem() throws Exception {
        try {
            this.compraItensVO = ((CompraItensVO) context().getExternalContext().getRequestMap().get("compraItem")).clone();
            this.produtoSelecionado = compraItensVO.getProduto().getDescricao();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void abrirCadastroFornecedor() {
        try {
//            validarPermissaoFornecedor();
            setMsgAlert("abrirPopup('../faces/fornecedorForm.jsp','Fornecedor', 800, 595);");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

    public CompraVO getCompraVO() {
        return compraVO;
    }

    public void setCompraVO(CompraVO compraVO) {
        this.compraVO = compraVO;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public CompraItensVO getCompraItensVO() {
        return compraItensVO;
    }

    public void setCompraItensVO(CompraItensVO compraItensVO) {
        this.compraItensVO = compraItensVO;
    }

    public Date getPeriodoAte() {
        return periodoAte;
    }

    public void setPeriodoAte(Date periodoAte) {
        this.periodoAte = periodoAte;
    }

    public Date getPeriodoDe() {
        return periodoDe;
    }

    public void setPeriodoDe(Date periodoDe) {
        this.periodoDe = periodoDe;
    }

    public String getNumeroNF() {
        return numeroNF;
    }

    public void setNumeroNF(String numeroNF) {
        this.numeroNF = numeroNF;
    }

    public List getListaSelectItemFornedor() {
        return listaSelectItemFornedor;
    }

    public void setListaSelectItemFornedor(List listaSelectItemFornedor) {
        this.listaSelectItemFornedor = listaSelectItemFornedor;
    }

    public Integer getCodigoFornecedor() {
        return codigoFornecedor;
    }

    public void setCodigoFornecedor(Integer codigoFornecedor) {
        this.codigoFornecedor = codigoFornecedor;
    }

    public List<ProdutoVO> executarAutocompletePesqProduto(Object suggest) {
        List<ProdutoVO> listaProdutos = null;
        try {
            if ((this.compraVO.getEmpresa() == null)
                    || (this.compraVO.getEmpresa().getCodigo() == null)
                    || (this.compraVO.getEmpresa().getCodigo() <= 0)) {
                throw new ConsistirException("Antes de incluir os produtos é necessário informar a empresa da compra.");
            }
            String nomePesq = (String) suggest;
            listaProdutos = getFacade().getProduto().consultarProdutosComControleEstoque(this.compraVO.getEmpresa().getCodigo(), null, nomePesq, true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            setSucesso(true);
            setErro(false);

        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaProdutos;
    }

    public void selecionarProduto() {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("result");
        
        this.compraItensVO.setProduto(obj);
        try{
            getFacade().getProdutoEstoque().pesquisarDatasAlteracoesProdutoEstoque(this.compraItensVO, this.getCompraVO().getEmpresa().getCodigo());
        }catch(Exception e){
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public String getProdutoSelecionado() {
        return produtoSelecionado;
    }

    public void setProdutoSelecionado(String produtoSelecionado) {
        this.produtoSelecionado = produtoSelecionado;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public void validarPermissaoCadastrarCompra() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "CadastrarCompra", "12.01 - Cadastrar Compra");
            }
        }
    }

    public void validarPermissaoAdicionarProdutoEstoque() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        for (Object o : getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "ConfigurarProdutoEstoque", "12.06 - Adicionar Produto ao Controle de Estoque");
            }
        }
    }

    public void validarPermissaoCancelarCompra() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "CancelarCompra", "12.02 - Cancelar Compra");
            }
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getCompra().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void realizarConsultaLogObjetoSelecionadoCompra() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if (compraVO.getCodigo() != 0) {
            String nomeClasse = "COMPRA";
            loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
            loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                    compraVO.getCodigo(), 0);
        }else{
            loginControle.limparListaLog();
        }
        

    }
    
    public void validarDataCompra() throws Exception{
        try{
            Date dataAvaliar = compraVO.getDataCadastro();
            if(compraVO.getCodigo() > 0){
                dataCompraValida = this.dataCompraOriginal;
                if(Calendario.igualComHora(compraVO.getDataCadastro(), dataCompraOriginal)){
                    return;
                } else if(Calendario.maiorComHora(compraVO.getDataCadastro(), dataCompraOriginal)){
                    dataAvaliar = dataCompraOriginal;
                }
            }
            for(CompraItensVO item: compraVO.getItens()){
                if(item.getDataUltimaOperacao() == null){
                   compraVO.setDataCadastro(dataCompraValida);
                   throw new ConsistirException("Produto "+item.getProduto().getDescricao()+" não adicionado ao Controle de Estoque");
               }
               if(Calendario.menorComHora(dataAvaliar, item.getDataUltimaOperacao())){
                   compraVO.setDataCadastro(dataCompraValida);
                   throw new ConsistirException("Produto "+item.getProduto().getDescricao()+" foi adicionado ao Controle ou teve balanço no dia "+Uteis.getDataComHHMM(item.getDataUltimaOperacao())+",  portanto a Data da Compra não pode ser menor que esse dia.");
               }
               if(item.getDataSaidaControle() != null && Calendario.maiorComHora(compraVO.getDataCadastro() , item.getDataSaidaControle())){
                   compraVO.setDataCadastro(dataCompraValida);
                   throw new ConsistirException("Produto "+item.getProduto().getDescricao()+" deixou de ter o estoque controlado no dia "+Uteis.getDataComHHMM(item.getDataUltimaOperacao())+", portanto a Data da Compra não pode ser maior que esse dia");
               }
            }
            setDataCompraValida(compraVO.getDataCadastro());
        } catch (Exception e) {
            throw e;
        }
        
    }

    public Date getDataCompraValida() {
        return dataCompraValida;
    }

    public void setDataCompraValida(Date dataCompraValida) {
        this.dataCompraValida = dataCompraValida;
    }
    
    public Date getDataCompraCalendario() {
        if (dataCompraCalendario == null) {
            dataCompraCalendario = Calendario.hoje();
        }
        return dataCompraCalendario;
    }

    public Date getDataVencimentoContaCompra() {
        return dataVencimentoContaCompra;
    }

    public void setDataVencimentoContaCompra(Date dataVencimentoContaCompra) {
        this.dataVencimentoContaCompra = dataVencimentoContaCompra;
    }

    public void calendarioDataCadastro() {
        this.compraVO.setDataCadastro(getDataCalendarioComHora());
    }


    public Date getDataCalendarioComHora() {
        Date dataCalendarioComHora = getDataCompraCalendario();
        dataCalendarioComHora.setHours(Calendario.hoje().getHours());
        dataCalendarioComHora.setMinutes(Calendario.hoje().getMinutes());
        dataCalendarioComHora.setSeconds(Calendario.hoje().getSeconds());
        return dataCalendarioComHora;
    }
    
    public void setDataCompraCalendario(Date dataCompraCalendario) {
        this.dataCompraCalendario = dataCompraCalendario;
    }

    public void confirmarExcluir(){
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("metodochamar");

        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        if (obj.equals("clonar")){
            control.init("Clonar Compra",
                    "Deseja Clonar Compra?",
                    this, obj, "", "", "", "grupoBtnExcluir,form");
        }else {
            control.init("Cancelamento de Compra",
                    "Deseja Cancelar esta Compra?",
                    this, obj, "", "", "", "grupoClonarCompra,form");

        }

    }

    public File getDocumento() {
        return documento;
    }

    public void setDocumento(File documento) {
        this.documento = documento;
    }

    public boolean isExisteDocumento() {
        return existeDocumento;
    }

    public void setExisteDocumento(boolean existeDocumento) {
        this.existeDocumento = existeDocumento;
    }

    public List<DetalheProduto> getProdutosNaoEncontradosNaNfe() {
        return produtosNaoEncontradosNaNfe;
    }

    public void setProdutosNaoEncontradosNaNfe(List<DetalheProduto> produtosNaoEncontradosNaNfe) {
        this.produtosNaoEncontradosNaNfe = produtosNaoEncontradosNaNfe;
    }

    public Date getDataCompraOriginal() {
        return dataCompraOriginal;
    }

    public void setDataCompraOriginal(Date dataCompraOriginal) {
        this.dataCompraOriginal = dataCompraOriginal;
    }

    public Integer getCategoriaParaNovosProdutosNfe() {
        return categoriaParaNovosProdutosNfe;
    }

    public void setCategoriaParaNovosProdutosNfe(Integer categoriaParaNovosProdutosNfe) {
        this.categoriaParaNovosProdutosNfe = categoriaParaNovosProdutosNfe;
    }

    public Integer getEstoqueMinimoParaNovosProdutosNfe() {
        return estoqueMinimoParaNovosProdutosNfe;
    }

    public void setEstoqueMinimoParaNovosProdutosNfe(Integer estoqueMinimoParaNovosProdutosNfe) {
        this.estoqueMinimoParaNovosProdutosNfe = estoqueMinimoParaNovosProdutosNfe;
    }
    
    public List<SelectItem> getListaSelectItemProduto(){
        return listaSelectItemProduto;
    }

    public void setListaSelectItemProduto(List<SelectItem> listaSelectItemProduto) {
        this.listaSelectItemProduto = listaSelectItemProduto;
    }

    public List<SelectItem> getListaSelectItemCategoriaProduto() {
        return listaSelectItemCategoriaProduto;
    }

    public void setListaSelectItemCategoriaProduto(List<SelectItem> listaSelectItemCategoriaProduto) {
        this.listaSelectItemCategoriaProduto = listaSelectItemCategoriaProduto;
    }

    public List<SelectItem> getListaConfigEmissaoNFSe() {
        return listaConfigEmissaoNFSe;
    }

    public void setListaConfigEmissaoNFSe(List<SelectItem> listaConfigEmissaoNFSe) {
        this.listaConfigEmissaoNFSe = listaConfigEmissaoNFSe;
    }

    public ProdutoVO getProdutoComConfigsGenericas() {
        return produtoComConfigsGenericas;
    }

    public void setProdutoComConfigsGenericas(ProdutoVO produtoComConfigsGenericas) {
        this.produtoComConfigsGenericas = produtoComConfigsGenericas;
    }

    public List<ProdutoEstoqueVO> getProdutoEstoquesParaCriar() {
        return produtoEstoquesParaCriar;
    }

    public void setProdutoEstoquesParaCriar(List<ProdutoEstoqueVO> produtoEstoquesParaCriar) {
        this.produtoEstoquesParaCriar = produtoEstoquesParaCriar;
    }

    public List<ProdutoVO> getProdutosParaAlterar() {
        return produtosParaAlterar;
    }

    public void setProdutosParaAlterar(List<ProdutoVO> produtosParaAlterar) {
        this.produtosParaAlterar = produtosParaAlterar;
    }

    public List<ProdutoVO> getProdutosParaSalvar() {
        return produtosParaSalvar;
    }

    public void setProdutosParaSalvar(List<ProdutoVO> produtosParaSalvar) {
        this.produtosParaSalvar = produtosParaSalvar;
    }

    public List<CategoriaProdutoVO> getCategorias() {
        return categorias;
    }

    public void setCategorias(List<CategoriaProdutoVO> categorias) {
        this.categorias = categorias;
    }

    public List<ProdutoVO> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<ProdutoVO> produtos) {
        this.produtos = produtos;
    }

    public List<SelectItem> getListaConfigEmissaoNFCe() {
        return listaConfigEmissaoNFCe;
    }

    public void setListaConfigEmissaoNFCe(List<SelectItem> listaConfigEmissaoNFCe) {
        this.listaConfigEmissaoNFCe = listaConfigEmissaoNFCe;
    }

    public HashMap<File, String> getDocumentos() {
        return documentos;
    }

    public void setDocumentos(HashMap<File, String> documentos) {
        this.documentos = documentos;
    }

    public Double getValorParcelaContaFinanceiro() {
        return valorParcelaContaFinanceiro;
    }

    public void setValorParcelaContaFinanceiro(Double valorParcelaContaFinanceiro) {
        this.valorParcelaContaFinanceiro = valorParcelaContaFinanceiro;
    }

    public Integer getNrParcelaContaFinanceiro() {
        return nrParcelaContaFinanceiro;
    }

    public void setNrParcelaContaFinanceiro(Integer nrParcelaContaFinanceiro) {
        this.nrParcelaContaFinanceiro = nrParcelaContaFinanceiro;
    }

    public String getCodigoBarrasParcelaContaFinanceiro() {
        return codigoBarrasParcelaContaFinanceiro;
    }

    public void setCodigoBarrasParcelaContaFinanceiro(String codigoBarrasParcelaContaFinanceiro) {
        this.codigoBarrasParcelaContaFinanceiro = codigoBarrasParcelaContaFinanceiro;
    }
}
